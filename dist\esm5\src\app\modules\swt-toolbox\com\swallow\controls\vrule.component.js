/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
var $ = require('jquery');
var VRule = /** @class */ (function (_super) {
    tslib_1.__extends(VRule, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function VRule(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions----------------------------------------------------------------------------------------
        _this._strokeColor = "";
        _this._shadowColor = "";
        _this._themeColor = "";
        return _this;
    }
    Object.defineProperty(VRule.prototype, "strokeColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._strokeColor;
        },
        //----strokeColor--------------------------------------------------------------------------------------------------
        set: 
        //----strokeColor--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VRule.prototype, "shadowColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._shadowColor;
        },
        //----shadowColor--------------------------------------------------------------------------------------------------
        set: 
        //----shadowColor--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VRule.prototype, "themeColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._themeColor;
        },
        //----themeColor---------------------------------------------------------------------------------------------------
        set: 
        //----themeColor---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    VRule.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        try {
            console.log('[VRule] ngOnDestroy');
            delete this._strokeColor;
            delete this._shadowColor;
            delete this._themeColor;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    VRule.decorators = [
        { type: Component, args: [{
                    selector: 'VRule',
                    template: "\n    <div class=\"v-rule\"></div>\n  ",
                    styles: ["\n       .v-rule {\n           background-color: #000;\n           height: 100%;\n           width:1px;\n           box-shadow: -1px 0px 0px #fff;\n       }\n  "]
                }] }
    ];
    /** @nocollapse */
    VRule.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    VRule.propDecorators = {
        strokeColor: [{ type: Input }],
        shadowColor: [{ type: Input }],
        themeColor: [{ type: Input }]
    };
    return VRule;
}(Container));
export { VRule };
if (false) {
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._strokeColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._shadowColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._themeColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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