import { <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewContainerRef } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "../../../com/swallow/utils/common.service";
export declare class Handler {
    viewContainerRef: ViewContainerRef;
    constructor(viewContainerRef: ViewContainerRef);
}
export declare class TitleWindow implements OnInit, OnDestroy {
    private element;
    private commonServ;
    tabid: string;
    visible: boolean;
    width: string;
    height: string;
    position: any;
    maxWidth: string;
    maxHeight: string;
    minWidth: string;
    minHeight: string;
    enableResize: boolean;
    showControls: boolean;
    showHeader: boolean;
    title: string;
    data: any;
    id: string;
    layoutOrder: number;
    minimizeIcon: string;
    onClose: EventEmitter<any>;
    result: any;
    url: string;
    isModal: boolean;
    contentHolder: Handler;
    mloaderOutlet: ViewContainerRef;
    private windowStatte;
    private content;
    private windowService;
    private childcomponent;
    private enforceUserPosition;
    private logger;
    private parent;
    private subscriptions;
    constructor(element: ElementRef, commonServ: CommonService);
    initX: number;
    initY: number;
    ngOnInit(): void;
    ngOnDestroy(): void;
    addChild(child: any): void;
    display(): void;
    /**
     * This method will be called when window title clicked.
     * @param event
     */
    onTitleBarClick(): void;
    /**
     * This method is used to synchronize the title window
     * layout.
     */
    validateNow(): void;
    getChild(): any;
    /**
     * this method is used to include content in the title window.
     * @param component
     */
    includeContent(parent: any, service: any, url?: string, component?: any, data?: any): void;
    /**
     * This method is used to minimize the current window.
     */
    minimizeWindow(): void;
    setBounds(x: string, y: string, width: string, height: string): void;
    setWindowXY(x: string, y: string): void;
    setPosition(location: any): void;
    /**
     * This method return the window state:
     *  true : window minimized;
     *  false: window maximized;
     */
    isMinimized(): boolean;
    /**
     * This method is used to close current window.
     */
    close(): void;
    /**
     * This method is used to get query from url.
     * @param url
     */
    private getUrlQuery;
    private loadComponent;
    /**
     * Maps your object passed in the creation to fields in your own window classes
     * @param component
     * @param window
     * @param data
     */
    private mapDataObject;
}
