/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, Component, Input, Renderer2 } from "@angular/core";
export class VBox extends Container {
    //-------constructor-----------------------------------------------------------//
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this.verticalGap = "6";
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        $($(this.elem.nativeElement)[0]).attr('selector', 'VBox');
    }
}
VBox.decorators = [
    { type: Component, args: [{
                selector: 'VBox',
                template: `
    
    <div fxLayout="column" fxLayoutAlign="{{verticalAlign}} {{horizontalAlign}}"   class="verticalLayout {{styleName}}" scroll="scroll" tabindex="-1">
        <ng-content ></ng-content>
        <ng-container #_container></ng-container>
    </div>
  `,
                styles: [`  
    :host {
      margin:  0px;
      width: fit-content;
      outline: none;
    }
    .verticalLayout {
      width: 100%;
      outline: none;
    }
  `]
            }] }
];
/** @nocollapse */
VBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
VBox.propDecorators = {
    styleName: [{ type: Input, args: ['styleName',] }]
};
if (false) {
    /** @type {?} */
    VBox.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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