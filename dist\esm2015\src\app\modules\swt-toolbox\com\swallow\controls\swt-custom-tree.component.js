/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Injectable, Input, Output, ViewChild } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { SwtCheckboxEvent, CustomTreeEvent, genericEvent } from "../events/swt-events.module";
//Import LESS or CSS:
// import 'assets/css/TreeThemes/skin-lion/ui.fancytree.less'
import { JSONReader } from '../jsonhandler/jsonreader.service';
import { Logger } from '../logging/logger.service';
import { CommonService } from '../utils/common.service';
import { HashMap } from '../utils/HashMap.service';
import { AdvancedToolTip } from "./advanced-tool-tip.component";
import { UIComponent } from "./UIComponent.service";
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
const fancytree = require('jquery.fancytree');
require('jquery.fancytree/dist/modules/jquery.fancytree.edit');
require('jquery.fancytree/dist/modules/jquery.fancytree.filter');
require('jquery.fancytree/dist/modules/jquery.fancytree.dnd');
require('jquery.fancytree/dist/modules/jquery.fancytree.multi');
require('jquery.fancytree/dist/modules/jquery.fancytree.table');
require('jquery.fancytree/dist/modules/jquery.fancytree.wide');
export class CustomTree extends Container {
    // tree constructor.
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        // handle to check if tree loaded for first time.
        this.firstLoad = true;
        // A local sequence
        this.local_sequence = 1;
        // variables to handle CRUD operations.
        this.CRUD_OPERATION = 'crud_operation';
        this.CRUD_DATA = 'crud_data';
        this.iconFunction = new Function();
        this.ITEM_CLICK = new EventEmitter();
        this.ITEM_ACTIVATE = new EventEmitter();
        this.ITEM_DBCLICK = new EventEmitter();
        this.MOUSE_OUT = new EventEmitter();
        this.itemOpen = new EventEmitter();
        this.itemClose = new EventEmitter();
        this.MOUSE_OVER = new EventEmitter();
        this.FOCUS_IN = new EventEmitter();
        this.FOCUS_OUT = new EventEmitter();
        this.styleName = '';
        //    @Output('mouseOut') mouseOut = new EventEmitter<Function>();
        this.buttonMode = false;
        // variable used in the algorithm that calculate selectedIndex.
        this._tree_state = new Array();
        this.itemEditEnd = new EventEmitter();
        /**
         * private array to hold tree options.
         * private.
         */
        this.options = ['dnd', 'edit', 'table' /*, 'filter' 'wide'*/];
        this.isItemSelected = false;
        //    private _click: Function = null;
        this._dbclick = null;
        //    private _mouseOver: Function = null;
        this._tempIndex = -1;
        //    private _focusIn: Function = null;
        //    private _focusOut: Function = null;
        //    private _mouseIn: Function = null;
        // private variable to handle selectedLevel.
        this._selectedLevel = null;
        this._keyDownFlag = false;
        this._level0OrderAttrName = 'DEFAULT_ORDER_ATTR';
        this._level1OrderAttrName = 'DEFAULT_ORDER_ATTR';
        this._level2OrderAttrName = 'DEFAULT_ORDER_ATTR';
        this._level3OrderAttrName = 'DEFAULT_ORDER_ATTR';
        this._level4OrderAttrName = 'DEFAULT_ORDER_ATTR';
        this._globalFindStop = false;
        //Parameter List for Edited ITem
        this.editableAttribute = null;
        this.editableAdditionalAttribute = null;
        this.editableValidationFunction = null;
        this.leaf_key = 0;
        this._firstLoad = true;
        this.__OriginalDataProvider = null;
        this._hideIcons = false;
        this._addCheckbox = false;
        this._indeterminateCheckbox = false;
        this._saveTreeStateBasedOn = '';
        // private variable to handle changes.
        this._changes = new HashMap();
        //    @Output('mouseOver') mouseOver = new EventEmitter<Function>();
        this._hideFunction = (/**
         * @param {?} item
         * @return {?}
         */
        (item) => this.defaultHideFunction(item));
        this._level2Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        this._level3Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        this._level4Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        /**
         * private.
         */
        this._allowMultipleSelection = false;
        this._selectedIndices = new Array();
        // private variable to handle selected index.
        this._selectedIndex = -1;
        // private variable to activate doubleClick
        this._doubleClickEnabled = true;
        // private variable to enable/disable selectable property
        this._selectable = true;
        // private variable to enable / disable tree.
        this._enabled = true;
        this._level1Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        this._level0Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        this._iconWidth = 16; // Adjust this if @fancy-icon-width != "16px"
        this._iconSpacing = 3; // Adjust this if @fancy-icon-spacing != "3px"
        this._labelSpacing = 3; // Adjust this if padding between icon and label !=  "3px"
        this._levelOfs = 16; // Adjust this if ul padding != "16px"
        this._dragMoveEnabled = false;
        this._dragEnabled = false;
        this._editable = false;
        this._openItems = new Array();
        this._closeItems = new Array();
        this._verticalScrollPosition = 0;
        this._showRoot = true;
        this.logger = new Logger('CustomTree', this.commonService.httpclient, 6);
    }
    /**
     * @protected
     * @param {?} item
     * @return {?}
     */
    defaultHideFunction(item) {
        if (item.data.visible == false) {
            return true;
        }
        else {
            if (!this.isVisibleNode(item)) {
                return true;
            }
        }
    }
    /**
     * _changes getter
     *
     * @return {?}
     */
    get changes() {
        return this._changes;
    }
    /**
     * _changes setter
     *
     * @param {?} value
     * @return {?}
     */
    set changes(value) {
        this._changes = value;
    }
    /**
     * This method is used to set a callback function to the tree
     * component in order to show or hide specific items.
     * @param {?} callback
     * @return {?}
     */
    set hideFunction(callback) {
        this._hideFunction = callback;
    }
    /**
     * Setter for _level2Order
     * @return {?}
     */
    get level2Order() {
        return this._level2Order;
    }
    /**
     * Setter for _level2Order
     * @param {?} value
     * @return {?}
     */
    set level2Order(value) {
        this._level2Order = value;
        this._level2OrderAttrName = this._level2Order.substr(0, this._level2Order.indexOf('=') !== -1 ? this._level2Order.indexOf('=') : this._level2Order.length);
    }
    /**
     * Setter for _level3Order
     *
     * @return {?}
     */
    get level3Order() {
        return this._level3Order;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set level3Order(value) {
        this._level3Order = value;
        this._level3OrderAttrName = this._level3Order.substr(0, this._level3Order.indexOf('=') != -1 ? this._level3Order.indexOf('=') : this._level3Order.length);
    }
    /**
     * Setter for _level4Order
     *
     * @return {?}
     */
    get level4Order() {
        return this._level4Order;
    }
    /**
     * Setter for _level4Order
     *
     * @param {?} value
     * @return {?}
     */
    set level4Order(value) {
        this._level4Order = value;
        this._level4OrderAttrName = this._level4Order.substr(0, this._level4Order.indexOf('=') != -1 ? this._level4Order.indexOf('=') : this._level4Order.length);
    }
    /**
     * @return {?}
     */
    get allowMultipleSelection() {
        return this._allowMultipleSelection;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set allowMultipleSelection(value) {
        try {
            if (this._allowMultipleSelection == value) {
                return;
            }
            value.toString() == "true" ? this._allowMultipleSelection = true : this._allowMultipleSelection = false;
            if (this._allowMultipleSelection) {
                this.options.push("multi");
            }
            else {
                this.options.indexOf("multi") !== -1 ? this.options.splice(this.options.indexOf("multi")) : null;
            }
        }
        catch (e) {
            this.logger.error("allowMultipleSelection error: " + e);
        }
    }
    /**
     * @return {?}
     */
    get selectedIndices() {
        return this._selectedIndices;
    }
    /**
     * This method is used to get selected Indices.
     * @param {?} indices
     * @return {?}
     */
    set selectedIndices(indices) {
        this.logger.info('[ selectedIndices ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
        }
        catch (error) {
            this.logger.error('[ selectedIndices ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectedIndices ] END.');
    }
    // dataProvider getter.
    /**
     * @return {?}
     */
    get dataProvider() {
        return this.__OriginalDataProvider;
    }
    // Input to hold tree dataProvider.
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        /** @type {?} */
        let tempData = [];
        /** @type {?} */
        var errorLocation = 0;
        try {
            tempData = this._dataProvider;
            /** Khalil needs */
            if (value.length) {
                this.__OriginalDataProvider = [...value];
            }
            else {
                this.__OriginalDataProvider = JSON.parse(JSON.stringify(value));
            }
            // Khalil needs end.
            this.leaf_key = 0;
            errorLocation = 10;
            this._dataProvider = this.recursive(value, null);
            if (this._dataProvider !== tempData) {
                if (this.eventlist[genericEvent.CHANGE]) {
                    this.eventlist[genericEvent.CHANGE](this);
                }
            }
            errorLocation = 20;
            if (this.firstLoad) {
                this.init();
                // set first load to false.
                this.firstLoad = false;
                // initialize tree instance.
                this._instance = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree');
            }
            else {
                this.getInstance().reload(this._dataProvider);
            }
            errorLocation = 30;
        }
        catch (error) {
            this.logger.error('[ dataProvider ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    }
    // width getter method.
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    // width setter method
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        this._width = value;
    }
    // width getter method.
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    // height setter method.
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = value;
    }
    // width getter method.
    /**
     * @return {?}
     */
    get hideIcons() {
        return this._hideIcons;
    }
    // height setter method.
    /**
     * @param {?} value
     * @return {?}
     */
    set hideIcons(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._hideIcons = true;
            }
            else {
                this._hideIcons = false;
            }
        }
        else {
            this._hideIcons = value;
        }
    }
    // width getter method.
    /**
     * @return {?}
     */
    get addCheckbox() {
        return this._addCheckbox;
    }
    // height setter method.
    /**
     * @param {?} value
     * @return {?}
     */
    set addCheckbox(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._addCheckbox = true;
            }
            else {
                this._addCheckbox = false;
            }
        }
        else {
            this._addCheckbox = value;
        }
    }
    // width getter method.
    /**
     * @return {?}
     */
    get indeterminateCheckbox() {
        return this._indeterminateCheckbox;
    }
    // height setter method.
    /**
     * @param {?} value
     * @return {?}
     */
    set indeterminateCheckbox(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._indeterminateCheckbox = true;
            }
            else {
                this._indeterminateCheckbox = false;
            }
        }
        else {
            this._indeterminateCheckbox = value;
        }
    }
    // width getter method.
    /**
     * @return {?}
     */
    get saveTreeStateBasedOn() {
        return this._saveTreeStateBasedOn;
    }
    // height setter method.
    /**
     * @param {?} value
     * @return {?}
     */
    set saveTreeStateBasedOn(value) {
        this._saveTreeStateBasedOn = value;
    }
    // get tree selectedIndex.
    /**
     * @return {?}
     */
    get selectedIndex() {
        return this._selectedIndex;
    }
    // get tree selectedIndex.
    /**
     * @param {?} index
     * @return {?}
     */
    set selectedIndex(index) {
        this.logger.info('[ selectedIndex ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (index > -1) {
                this.diselectAll();
                /** @type {?} */
                const node = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getNodeByKey(this._tree_state[index]);
                if (this._hideFunction) {
                    /** @type {?} */
                    const item = new CustomTreeItem(node, this.commonService);
                    /** @type {?} */
                    let hideNode = this._hideFunction(item);
                    if (!hideNode) {
                        // scroll into selected node.
                        node.makeVisible({ scrollIntoView: true });
                        node.setActive(true);
                        this.selectNode(node);
                        if (node.isSelected()) {
                            $(node.span).closest('tr').addClass("fancytree-selected");
                        }
                        if (node.isActive()) {
                            $(node.span).closest('tr').addClass("fancytree-active");
                        }
                    }
                }
                /** @type {?} */
                const _height = $(this.treeContainer.nativeElement).height();
                /** @type {?} */
                const scrollTop = this.getScrollPosition();
                if (scrollTop > _height) {
                    $($(this.treeContainer.nativeElement)[0]).scrollTop(scrollTop);
                }
            }
            else {
                if (!this.addCheckbox) {
                    ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
                     * @param {?} node
                     * @return {?}
                     */
                    (node) => {
                        if (node.isSelected()) {
                            node.setSelected(false);
                        }
                    }));
                }
                this._selectedItem = null;
                this._selectedLevel = null;
            }
            this._selectedIndex = index;
            this._verticalScrollPosition = index * 20;
        }
        catch (error) {
            this.logger.error('[ selectedIndex ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectedIndex ] END.');
    }
    // get Tree selected Item.
    /**
     * @return {?}
     */
    get selectedItem() {
        return this._selectedItem;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedItem(value) {
        if (value) {
            if (value === this._selectedItem) {
                return;
            }
            this.diselectAll();
            value.selected = true;
            this._selectedItem = value;
            this._selectedIndex = this._tree_state.indexOf(this._selectedItem.key);
        }
    }
    // doubleClickEnabled getter.
    /**
     * @return {?}
     */
    get doubleClickEnabled() {
        return this._doubleClickEnabled;
    }
    // doubleClickEnabled setter
    /**
     * @param {?} value
     * @return {?}
     */
    set doubleClickEnabled(value) {
        if (value.toString() == "false") {
            this._doubleClickEnabled = false;
        }
        else if (value.toString() == "true") {
            this._doubleClickEnabled = true;
        }
        else {
            this._doubleClickEnabled = value;
        }
    }
    /**
     * @return {?}
     */
    get selectable() {
        return this._selectable;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectable(value) {
        //TODO
    }
    // enabled getter.
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    // enabled setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        this._enabled = value;
        ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').enable(this._enabled);
    }
    /**
     * Setter for _level1Order
     * @return {?}
     */
    get level1Order() {
        return this._level1Order;
    }
    /**
     * Setter for _level1Order
     * @return {?}
     */
    get level0Order() {
        return this._level0Order;
    }
    /**
     * Setter for _level1Order
     * @param {?} value
     * @return {?}
     */
    set level0Order(value) {
        this._level0Order = value;
        this._level0OrderAttrName = this._level0Order.substr(0, this._level0Order.indexOf('=') !== -1 ? this._level0Order.indexOf('=') : this._level0Order.length);
    }
    /**
     * Setter for _level1Order
     * @param {?} value
     * @return {?}
     */
    set level1Order(value) {
        this._level1Order = value;
        this._level1OrderAttrName = this._level1Order.substr(0, this._level1Order.indexOf('=') !== -1 ? this._level1Order.indexOf('=') : this._level1Order.length);
    }
    // Adjust this if @fancy-icon-width != "16px"
    /**
     * @return {?}
     */
    get iconWidth() {
        return this._iconWidth;
    }
    /**
     *  set tree icon width
     * @param {?} value
     * @return {?}
     */
    set iconWidth(value) {
        this._iconWidth = value;
    }
    // Adjust this if @fancy-icon-spacing != "3px"
    /**
     * @return {?}
     */
    get iconSpacing() {
        return this._iconSpacing;
    }
    /**
     * set tree icon spacing.
     * @param {?} value
     * @return {?}
     */
    set iconSpacing(value) {
        this._iconSpacing = value;
    }
    // Adjust this if padding between icon and label !=  "3px"
    /**
     * @return {?}
     */
    get labelSpacing() {
        return this._labelSpacing;
    }
    /**
     * set tree label spacing
     * @param {?} value
     * @return {?}
     */
    set labelSpacing(value) {
        this._labelSpacing = value;
    }
    // Adjust this if ul padding != "16px"
    /**
     * @return {?}
     */
    get levelOfs() {
        return this._levelOfs;
    }
    /**
     * set tree level offsets.
     * @param {?} value
     * @return {?}
     */
    set levelOfs(value) {
        this._levelOfs = value;
    }
    // enabled getter.
    /**
     * @return {?}
     */
    get dragMoveEnabled() {
        return this._dragMoveEnabled;
    }
    // enabled setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set dragMoveEnabled(value) {
        this._dragMoveEnabled = value;
    }
    /**
     * @return {?}
     */
    get dragEnabled() {
        return this._dragEnabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dragEnabled(value) {
        this._dragEnabled = value;
    }
    // enabled getter.
    /**
     * @return {?}
     */
    get editable() {
        return this._editable;
    }
    // enabled setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        if (typeof value === "string") {
            if (value === "true") {
                this._editable = true;
            }
            else if (value === "false") {
                this._editable = false;
            }
        }
        else {
            this._editable = value;
        }
    }
    /**
     * @return {?}
     */
    get openItems() {
        return this._openItems;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set openItems(value) {
        this._openItems = value;
    }
    /**
     * @return {?}
     */
    get closeItems() {
        return this._closeItems;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set closeItems(value) {
        this._closeItems = value;
    }
    /**
     * @return {?}
     */
    get verticalScrollPosition() {
        return this._verticalScrollPosition;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set verticalScrollPosition(value) {
        $(this.elem.nativeElement.children[0]).animate({ scrollTop: value }, 0);
        this._verticalScrollPosition = value;
    }
    /**
     *  Sets the visibility of the root item.
     *
     *  If the dataProvider data has a root node, and this is set to
     *  <code>false</code>, the Tree control does not display the root item.
     *  Only the decendants of the root item are displayed.
     *
     *  This flag has no effect on non-rooted dataProviders, such as List and Array.
     *
     * \@default true
     * @see #hasRoot
     * @return {?}
     */
    get showRoot() {
        return this._showRoot;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set showRoot(value) {
        if (this._showRoot !== value) {
            this._showRoot = value;
        }
    }
    /**
     *  The column and row index of the item renderer for the
     *  data provider item being edited, if any.
     *
     *  <p>This Object has two fields, <code>columnIndex</code> and
     *  <code>rowIndex</code>,
     *  the zero-based column and item indexes of the item.
     *  For a List control, the <code>columnIndex</code> property is always 0;
     *  for example: <code>{columnIndex:0, rowIndex:3}</code>.</p>
     *
     *  <p>Setting this property scrolls the item into view and
     *  dispatches the <code>itemEditBegin</code> event to
     *  open an item editor on the specified item,
     *  </p>
     * @return {?}
     */
    get editedItemPosition() {
        if (this._editedItemPosition) {
            return {
                rowIndex: this._editedItemPosition.rowIndex,
                columnIndex: 0
            };
        }
        else {
            return this._editedItemPosition;
        }
    }
    /**
     *  private
     * @param {?} value
     * @return {?}
     */
    set editedItemPosition(value) {
        try {
            /** @type {?} */
            let key = this._tree_state[value.rowIndex];
            /** @type {?} */
            let node = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getNodeByKey(key);
            this.editable = true;
            // this.diselectAll();
            // node.setSelected();
            this.selectedItem = new CustomTreeItem(node, this.commonService);
            node.editStart();
            this._editedItemPosition = value;
        }
        catch (e) {
            this.logger.error("editedItemPosition " + e);
        }
    }
    /**
     * @return {?}
     */
    get isFirstLoad() {
        return this._firstLoad;
    }
    /**
     * @param {?} node
     * @return {?}
     */
    static getExpander(node) {
        /** @type {?} */
        const $span = $(node.span);
        return $span.find('> span.fancytree-expander');
    }
    /**
     * @param {?} node
     * @param {?} operation
     * @param {?=} updatedAttribute
     * @param {?=} map
     * @return {?}
     */
    manageChangesArray(node, operation, updatedAttribute = null, map = null) {
        /** @type {?} */
        let changeObject = new Object;
        /** @type {?} */
        let tempObject = new Object;
        /** @type {?} */
        let changesChildObject = new Object;
        /** @type {?} */
        let putChanges = true;
        /** @type {?} */
        let existAndEqual = true;
        /** @type {?} */
        let sequence = -1;
        /** @type {?} */
        let index = 0;
        /** @type {?} */
        let seq;
        // Set itemSeq value
        sequence = node.key;
        // check if the operation is insert
        if (operation == "I") {
            index = this.changes.size();
            // if the changes contains items and map is not null
            if (this.changes.size() > 0 && map != null) {
                //                 existAndEqual=true;
                // itterrate the changes map
                for (seq in this.changes.getKeys()) {
                    changesChildObject = this.changes.getValue(seq);
                    //
                    existAndEqual = false;
                    if (changesChildObject[CustomTree.CRUD_OPERATION] == "D") {
                        existAndEqual = true;
                        for (let key in map.getKeys()) {
                            if (changesChildObject[key] == null || changesChildObject[key] != map.getValue(key)) {
                                existAndEqual = false;
                                break;
                            }
                        }
                    }
                    if (existAndEqual) {
                        break;
                    }
                    index--;
                }
                if (existAndEqual) {
                    this._changes.remove(seq);
                    putChanges = false;
                }
            }
            changeObject[CustomTree.CRUD_OPERATION] = "I";
            changeObject[CustomTree.CRUD_DATA] = node;
        }
        else if (operation == "U") { // if operation is an update
            // if operation is an update
            // set the attribute map
            /** @type {?} */
            var findMap = new HashMap();
            findMap.put('TREE_ITEM_LOCAL_SEQ', sequence.toString());
            // set _globalFindStop to false
            this._globalFindStop = false;
            // set the original xml node
            /** @type {?} */
            var xml = this.getInstance().getNodeByKey(sequence.toString());
            // check if the chnages map contains the updated element
            if (this._changes.containsKey(sequence)) {
                changeObject = this._changes.getValue(sequence);
                if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf("U") != -1) {
                    // check if the updated attributes is the same as the original attribute value
                    if (xml[updatedAttribute] == node[updatedAttribute]) {
                        /* Remove the updated attribute from changes */
                        changeObject[CustomTree.CRUD_OPERATION] = String(changeObject[CustomTree.CRUD_OPERATION]).replace('U(' + updatedAttribute + ')', "");
                        changeObject[CustomTree.CRUD_OPERATION] = String(changeObject[CustomTree.CRUD_OPERATION]).replace('>>', '>');
                        if (String(changeObject[CustomTree.CRUD_OPERATION]).indexOf('U') < 0)
                            this._changes.remove(sequence);
                        // operation will not be logged in the changes map
                        putChanges = false;
                    }
                    if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf(updatedAttribute) < 0) {
                        changeObject[CustomTree.CRUD_OPERATION] = changeObject[CustomTree.CRUD_OPERATION] + '>' + 'U(' + updatedAttribute + ')';
                    }
                }
                // set the changeObject
                changeObject[CustomTree.CRUD_DATA] = node;
            }
            else {
                // check if xml is not null
                if (xml != null) {
                    // check if the updated attributes is the same as the original attribute value
                    if (xml[updatedAttribute] == node[updatedAttribute]) {
                        // operation will not be logged in the changes map
                        putChanges = false;
                    }
                }
                changeObject[CustomTree.CRUD_OPERATION] = 'U(' + updatedAttribute + ')';
                changeObject[CustomTree.CRUD_DATA] = node;
            }
        }
        else if (operation == "D") {
            if (this._changes.containsKey(sequence)) {
                changeObject = this._changes.getValue(sequence);
                if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf("I") != -1) {
                    this._changes.remove(sequence);
                    putChanges = false;
                }
                else {
                    changeObject[CustomTree.CRUD_OPERATION] = "D";
                    changeObject[CustomTree.CRUD_DATA] = node;
                }
            }
            else {
                changeObject[CustomTree.CRUD_OPERATION] = "D";
                changeObject[CustomTree.CRUD_DATA] = node;
            }
        }
        else {
            changeObject[CustomTree.CRUD_OPERATION] = operation;
            changeObject[CustomTree.CRUD_DATA] = node;
            //     sequence = local_sequence++;
        }
        if (map != null) {
            /** @type {?} */
            let mapkeys = map.getKeys();
            for (let index in mapkeys) {
                tempObject = map.getValue(mapkeys[index]);
                changeObject[mapkeys[index]] = tempObject;
            }
        }
        if (putChanges) {
            this._changes.put(sequence, changeObject);
        }
        putChanges = true;
    }
    /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @param {?=} parentNode
     * @return {?}
     */
    findNode(attributes, parentNode = null) {
        /** @type {?} */
        let matchedNode = null;
        try {
            if (parentNode) {
                /** @type {?} */
                let subChildren = parentNode.getChildren();
                subChildren.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                (item) => {
                    if (this.findNodeRecursively(item.getNode(), attributes)) {
                        matchedNode = item;
                        return;
                    }
                }));
            }
            else {
                ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
                 * @param {?} node
                 * @return {?}
                 */
                (node) => {
                    if (this.findNodeRecursively(node, attributes)) {
                        matchedNode = new CustomTreeItem(node, this.commonService);
                        return;
                    }
                }));
            }
        }
        catch (e) {
            this.logger.error("findNode " + e);
        }
        return matchedNode;
    }
    /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @return {?}
     */
    findAndExpandNode(attributes) {
        try {
            this.findNode(attributes, null).expand();
        }
        catch (e) {
            this.logger.error("findAndExpandNode " + e);
        }
    }
    /**
     * Recursively expand a node
     *
     * Enhancement to expand the Tree to specified Level
     * @param {?} nodeXmlList
     * @param {?} expandToLvl
     * @return {?}
     */
    expandNode(nodeXmlList, expandToLvl) {
        try {
            nodeXmlList.expand();
        }
        catch (e) {
            this.logger.error("expandNode " + e);
        }
    }
    /**
     * Sorts a tree based on configs given for level1Order, level2Order, level3Order and level4Order
     * TODO: Think on a recursive function for levels 1, 2 3 and .. n
     * @param {?} node
     * @return {?}
     */
    sort(node) {
        try {
            /** @type {?} */
            const nodeLevel = node.getLevel();
            if (nodeLevel === 1) {
                this.sortNodeBy(node, this.level1Order);
            }
            else if (nodeLevel === 2) {
                this.sortNodeBy(node, this.level2Order);
            }
            else if (nodeLevel === 3) {
                this.sortNodeBy(node, this.level3Order);
            }
            else if (nodeLevel === 4) {
                this.sortNodeBy(node, this.level4Order);
            }
        }
        catch (e) {
            this.log.error("sort error: ", e);
        }
    }
    /**
     * Function responsible of setting the label
     * @param {?} item
     * @return {?}
     */
    treeLabelFunction(item) {
        return item ? item.NAME : "";
    }
    /**
     * Recursively finds a node and expands it if argument expand is passed "true"
     * @param {?} node
     * @param {?} attributes
     * @param {?=} expand
     * @return {?}
     */
    findNodeRecursively(node, attributes, expand = false) {
        /** @type {?} */
        let nodeData = node.data;
        /** @type {?} */
        let keys = attributes.getKeys();
        /** @type {?} */
        let values = attributes.getValues();
        try {
            for (let index = 0; index < keys.length; index++) {
                if (!nodeData[keys[index]]) {
                    if (!node[keys[index]]) {
                        return false;
                    }
                }
                else {
                    if (nodeData[keys[index]] != attributes.getValue(keys[index])) {
                        return false;
                    }
                }
            }
            if (expand) {
                node.setExpanded();
            }
            return true;
        }
        catch (e) {
            console.error(e);
        }
    }
    /**
     * This method is used to select node item by attribute id and value
     * @param {?} attributeId
     * @param {?} attributeValue
     * @param {?=} uniqueNode
     * @return {?}
     */
    selectNodeByAttribute(attributeId, attributeValue, uniqueNode = false) {
        /** @type {?} */
        const errorLocation = 0;
        /** @type {?} */
        let nodeToFind = null;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                if (node.data[attributeId] == attributeValue) {
                    try {
                        if (nodeToFind == null && uniqueNode) {
                            nodeToFind = node;
                            node.setActive(true);
                            this.selectNode(node);
                        }
                        else {
                            if (!uniqueNode) {
                                node.setActive(true);
                                this.selectNode(node);
                            }
                        }
                    }
                    catch (e) {
                    }
                }
            }));
        }
        catch (error) {
            this.logger.error('[ selectNodeByAttribute ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectNodeByAttribute ] END.');
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (!this.id) {
                throw new Error('Programming error you must specify CustomTree id');
            }
            this.logger.info('[ ngOnInit ] METHOD ENTER.');
            // Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtCustomTree.
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtCustomTree');
            // set id to SwtCustomTree DOM.
            if (this.id) {
                $($(this.elem.nativeElement)[0]).attr('id', this.id);
            }
            //-END-
            $(this.elem.nativeElement).height(this.height);
            $(this.elem.nativeElement).width(this.width);
            this.logger.info('[ ngOnInit ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ ngOnInit ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    }
    /**
     * @return {?}
     */
    ngAfterContentInit() {
        /** @type {?} */
        const errorLocation = 0;
        try {
            this.logger.info('[ ngAfterContentInit ] METHOD ENTER.');
            this.logger.info('[ ngAfterContentInit ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ ngAfterContentInit ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    }
    /**
     * @param {?} node
     * @return {?}
     */
    isVisibleNode(node) {
        /** @type {?} */
        let visible = true;
        if (node.data && node.data.visible == false) {
            return false;
        }
        else if (node.parent != null && node.parent.data != null) {
            return (this.isVisibleNode(node.parent));
        }
        else {
            return true;
        }
    }
    /**
     * This method initialize the tree component View
     * @return {?}
     */
    init() {
        /** @type {?} */
        const errorLocation = 0;
        try {
            this.logger.info('[ init ] METHOD ENTER.');
            this._instance = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree({
                extensions: this.options,
                source: this._dataProvider,
                checkbox: this.addCheckbox,
                lazy: true,
                // lazyLoad: function(event, data){
                //     data.result = data.node.data;
                // },
                selectMode: this.addCheckbox && this.indeterminateCheckbox ? 3 : 2,
                autoScroll: false,
                quicksearch: (/**
                 * @return {?}
                 */
                () => {
                    return true;
                }),
                table: {
                    indentation: 20,
                    // indent 20px per node level
                    nodeColumnIdx: 0,
                    // render the node title into the 2nd column
                    checkboxColumnIdx: this.addCheckbox ? 1 : 0 // render the checkboxes into the 1st column
                },
                tooltip: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    // Create dynamic tooltips
                    /** @type {?} */
                    let dataTip = data.node.title;
                    if (this.dataTipFunction) {
                        dataTip = this.dataTipFunction(new CustomTreeItem(data.node, this.commonService));
                    }
                    return dataTip;
                }),
                create: (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                }),
                init: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    // data.tree.getRootNode().sortChildren((n1, n2) => {
                    //     const title1 = String(n1.title);
                    //     const title2 = String(n2.title);
                    //     return title2 === title1 ? 0 : title1 > title2 ? 1 : -1;
                    // }, true);
                    this.sortNodeBy(data.tree.getRootNode(), this.level0Order);
                    data.tree.visit((/**
                     * @param {?} node
                     * @return {?}
                     */
                    (node) => {
                        this.sort(node);
                    }));
                }),
                click: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    if (data.targetType == 'expander')
                        return;
                    // get tree node to be rendered.
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    const isItemSelected = node.isSelected();
                    this._selectedIndex = this._tree_state.indexOf(node.key);
                    // create new Tree Item object.
                    /** @type {?} */
                    const item = new CustomTreeItem(node, this.commonService);
                    this._selectedLevel = item.level;
                    this._selectedItem = item;
                    if (event.ctrlKey) {
                        // if (node) {
                        //     if (node.isSelected()) {
                        //         // node.selected = false;
                        //         // this.diselectAll();
                        //     } else {
                        //         node.selected = true;
                        //     }
                        //     node.render();
                        // }
                    }
                    this._selectedIndices = [];
                    // calculate selectedIndices on each item click.
                    if (this._allowMultipleSelection) {
                        setTimeout((/**
                         * @return {?}
                         */
                        () => {
                            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
                             * @param {?} treeNode
                             * @return {?}
                             */
                            (treeNode) => {
                                if ($(treeNode.span).closest('tr').hasClass("fancytree-selected")) {
                                    // verify if the selected indice already exist
                                    if (this._selectedIndices.indexOf(this._tree_state.indexOf(treeNode.key)) === -1) {
                                        this._selectedIndices.push(this._tree_state.indexOf(treeNode.key));
                                    }
                                }
                            }));
                            // dispache the item click event;
                            if (this.eventlist[CustomTreeEvent.ITEMCLICK]) {
                                this.eventlist[CustomTreeEvent.ITEMCLICK](item);
                            }
                            // emit item click event.
                            this.ITEM_CLICK.emit(item);
                        }), 0);
                    }
                    else {
                        this._selectedIndices.push(this._tree_state.indexOf(node.key));
                        // dispache the item click event;
                        if (this.eventlist[CustomTreeEvent.ITEMCLICK]) {
                            this.eventlist[CustomTreeEvent.ITEMCLICK](item);
                        }
                        // emit item click event.
                        this.ITEM_CLICK.emit(item);
                    }
                    this.cursorLocation = { x: event.pageX, y: event.pageY };
                    if (item.getCustomToolTip()) {
                        item.getCustomToolTip().top = event.pageY;
                        item.getCustomToolTip().left = event.pageX;
                    }
                }),
                icon: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    if (this.hideIcons) {
                        return false;
                    }
                    // get tree node to be rendered.
                    /** @type {?} */
                    const node = data.node;
                    // create new Tree Item object.
                    /** @type {?} */
                    const item = new CustomTreeItem(node, this.commonService);
                    try {
                        // call custom StyleFunction
                        return this.customStyleFunction(item);
                    }
                    catch (e) {
                        console.error(e);
                    }
                }),
                dblclick: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    if (!this.doubleClickEnabled) {
                        return false;
                    }
                    // get tree node to be rendered.
                    /** @type {?} */
                    let tempSelectedIndex = this._selectedIndex;
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    let item = new CustomTreeItem(node, this.commonService);
                    this._selectedIndex = this._tree_state.indexOf(node.key);
                    this._selectedItem = item;
                    if (this._selectedIndex !== tempSelectedIndex) {
                        if (this.eventlist[genericEvent.CHANGE]) {
                            this.eventlist[genericEvent.CHANGE](this);
                        }
                    }
                    // this.itemDoubleClick_.emit(item);
                    // if exist a double click listener so emit the event.
                    if (this.eventlist[CustomTreeEvent.ITEMDOUBLECLICK]) {
                        this.eventlist[CustomTreeEvent.ITEMDOUBLECLICK](item);
                    }
                }),
                keydown: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    this._keyDownFlag = true;
                    /** @type {?} */
                    const node = data.node;
                    // handle  key navigation
                    if (event.keyCode === 40) {
                        this.scrollToBottom(event, node);
                    }
                    else if (event.keyCode === 38) {
                        this.scrollToTop(event, node);
                    }
                }),
                focus: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                }),
                focusTree: (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                }),
                select: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    // ($(this.treeContainer.nativeElement) as any).fancytree('getTree').visit((node) => {
                    //     let item = new CustomTreeItem(node, this.commonService);
                    //     if (this._hideFunction) {
                    //     }
                    // });
                    if (data.targetType === 'checkbox') {
                        // node.data.selected = node.selected;
                        /** @type {?} */
                        let dataProviderNode = this.findNodeinDataprovider(node.key, this.dataProvider);
                        this.recusiveSelectDataProvider(node, dataProviderNode);
                        this.recusiveSelectDataProviderChildren(node, dataProviderNode);
                        /** @type {?} */
                        var dto = {};
                        dto.node = node;
                        dto.data = data;
                        // dto.selected = this.selected;
                        // dto.yField = this.yField;
                        // // Dispatch the event so that legends will be highlightened as well
                        if ($(this.elem.nativeElement).closest('swttabpushstrategy')) {
                            dto.parentTab = $(this.elem.nativeElement).closest('swttabpushstrategy').attr('id');
                        }
                        SwtCheckboxEvent.emit(dto);
                    }
                    // if (node.getChildren() && node.getChildren().length > 0) {
                    //     for (var index = 0; index < node.getChildren().length; index++) {
                    //         if (this._hideFunction) {
                    //             const item = new CustomTreeItem(node.getChildren()[index], this.commonService);
                    //             let hideNode = this._hideFunction(item);
                    //             if (hideNode + "" === "true") {
                    //                 $(node.getChildren()[index].span).closest('tr').addClass('fancytree-helper-hidden');
                    //             }
                    //         }
                    //     }
                    // }
                    node.visit((/**
                     * @param {?} childNode
                     * @return {?}
                     */
                    (childNode) => {
                        if (this._hideFunction) {
                            /** @type {?} */
                            const item = new CustomTreeItem(childNode, this.commonService);
                            /** @type {?} */
                            let hideNode = this._hideFunction(item);
                            if (hideNode + "" === "true") {
                                $(childNode.span).closest('tr').addClass('fancytree-helper-hidden');
                            }
                        }
                    }));
                }),
                beforeSelect: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    //this.diselectAll();
                }),
                activate: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    let tempSelectedIndex = this._selectedIndex;
                    /** @type {?} */
                    const isItemSelected = node.isSelected();
                    // this.diselectAll();
                    /*                this.selectedIndex = */
                    this._selectedIndex = this._tree_state.indexOf(node.key);
                    if (this._selectedIndex !== tempSelectedIndex) {
                        if (this.eventlist[genericEvent.CHANGE]) {
                            this.eventlist[genericEvent.CHANGE](this);
                        }
                    }
                    // update selected item.
                    this._selectedItem = new CustomTreeItem(node, this.commonService);
                    this._verticalScrollPosition = $(this.elem.nativeElement.children[0]).scrollTop();
                    this._selectedLevel = 'Level' + data.node.getLevel();
                    this.selectNode(node);
                    // if (!this._keyDownFlag) { TODO item clik event moved to clik verify if an error appear.
                    //     this.ITEM_CLICK.emit(data.node);
                    // }
                    this.ITEM_ACTIVATE.emit(data.node);
                    // this._keyDownFlag = false;
                }),
                // Image folder used for data.icon attribute.
                imagePath: 'assets/spirites/',
                // icon: false,
                renderNode: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    // get tree node to be rendered.
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    const $span = $(node.span);
                    /** @type {?} */
                    var _icon = null;
                    if ($span.find('> span.fancytree-icon').length > 0) {
                        _icon = $span.find('> span.fancytree-icon');
                    }
                    else {
                        _icon = $span.find('> span.fancytree-custom-icon');
                    }
                    /** @type {?} */
                    const _expander = $span.find('> span.fancytree-expander');
                    /** @type {?} */
                    const _title = $span.find('> span.fancytree-title');
                    try {
                        if (this.labelFunction) {
                            /** @type {?} */
                            const label = new TreeLabel(_title, this.commonService);
                            if (node.data) {
                                for (const attribute in node.data) {
                                    if (node.data.hasOwnProperty(attribute)) {
                                        if (isNaN(Number(attribute))) {
                                            label.bindAttribute(attribute, node.data[attribute]);
                                        }
                                    }
                                }
                            }
                            label.item = new CustomTreeItem(node, this.commonService);
                            this.labelFunction(label);
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                    try {
                        /** @type {?} */
                        const _data = node.data;
                        if (this._hideFunction) {
                            /** @type {?} */
                            const item = new CustomTreeItem(node, this.commonService);
                            /** @type {?} */
                            let hideNode = this._hideFunction(item);
                            if (hideNode + "" === "true") {
                                $(node.span).closest('tr').addClass('fancytree-helper-hidden');
                            }
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                    // console.log(node.data, node.selected , node.partsel);
                    try {
                        /** @type {?} */
                        let dataProviderNode = this.findNodeinDataprovider(node.key, this.dataProvider);
                        if (dataProviderNode) {
                            dataProviderNode.selected = node.data.selected = node.selected;
                            dataProviderNode.indeterminate = node.data.indeterminate = node.partsel;
                        }
                    }
                    catch (e) {
                        console.log(e);
                    }
                    // if(this._saveTreeStateBasedOn){
                    //     let itemFound =  this.openItems.find(x => (x['data'][this._saveTreeStateBasedOn] == node.data[this._saveTreeStateBasedOn])) != null;
                    //     if(itemFound){
                    //     }
                    // }
                    if (this.eventlist[CustomTreeEvent.ICONCLICK]) {
                        $(_icon).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        (evt) => {
                            /** @type {?} */
                            const item = new CustomTreeItem(node, this.commonService);
                            evt.target = item;
                            this.eventlist[CustomTreeEvent.ICONCLICK](evt);
                            if (item.getCustomToolTip()) {
                                // item.getCustomToolTip().top = this.getCursorLocation().y;
                                item.getCustomToolTip().left = this.getCursorLocation().x;
                            }
                            this._selectedItem = item;
                        }));
                        $(_icon).css("cursor", "pointer");
                    }
                    if (this.eventlist[CustomTreeEvent.ICONMOUSEENTER]) {
                        $(_icon).mouseenter((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        (evt) => {
                        }));
                        this.buttonMode = true;
                    }
                    if (this.eventlist[CustomTreeEvent.ICONMOUSELEAVE]) {
                        $(_icon).mouseleave((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        (evt) => {
                            /** @type {?} */
                            const item = new CustomTreeItem(node, this.commonService);
                            this.eventlist[CustomTreeEvent.ICONMOUSELEAVE](item);
                        }));
                        this.buttonMode = false;
                    }
                    if (this.eventlist[CustomTreeEvent.TITLECLICK]) {
                        $($span.find('> span.fancytree-title')).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        (evt) => {
                        }));
                    }
                    if (this.eventlist[CustomTreeEvent.EXPANDERCLICK]) {
                        $($span.find('> span.fancytree-expander')).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        (evt) => {
                        }));
                    }
                    if (this.eventlist[CustomTreeEvent.ICONFOCUS]) {
                        // TODO -------
                    }
                    if (this.eventlist[CustomTreeEvent.ICONFOCUSOUT]) {
                        // TODO -------
                    }
                }),
                expand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    const $span = $(node.span);
                    /** @type {?} */
                    const _title = $span.find('> span.fancytree-title');
                    // this._openItems = this.getOpenedItems();
                    // start of code used to update tree state capture
                    /** @type {?} */
                    const _children = new Array();
                    if (node.getChildren()) {
                        for (var index = 0; index < node.getChildren().length; index++) {
                            _children.push(node.getChildren()[index].key);
                        }
                    }
                    /** @type {?} */
                    const _exist = this._tree_state.join().indexOf(_children.join()) !== -1 ? true : false;
                    if (this._tree_state.indexOf(_children[0]) === -1) {
                        this._tree_state.splice(this._tree_state.indexOf(node.key) + 1, 0, ..._children);
                    }
                    // end of code used to update tree state capture
                    this.itemOpen.emit({ item: new CustomTreeItem(node, this.commonService), target: this });
                }),
                collapse: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    // this._openItems = this.getOpenedItems();
                    // const _index = this._openItems.indexOf(node.data.id);
                    // if (_index !== -1) {
                    //     this._openItems.splice(_index, 1);
                    // }
                    // start of code used to update tree state capture
                    this._tree_state.splice(this._tree_state.indexOf(node.key) + 1, node.getChildren().length);
                    // end of code used to update tree state capture
                }),
                beforeExpand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    const $span = $(node.span);
                }),
                //            multi: {
                //               mode: "sameParent"  // Restrict range selection behavior
                //            },
                dnd: {
                    autoExpandMS: 400,
                    focusOnClick: true,
                    preventVoidMoves: true,
                    preventRecursiveMoves: true,
                    dragStart: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    (node, data) => {
                        return this.dragMoveEnabled;
                    }),
                    dragEnter: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    (node, data) => {
                        return this.dragMoveEnabled;
                    }),
                    dragDrop: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    (node, data) => {
                        data.otherNode.moveTo(node, data.hitMode);
                    })
                },
                edit: {
                    triggerStart: ['clickActive', 'dblclick', 'f2', 'mac+enter', 'shift+click'],
                    beforeEdit: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    (event, data) => {
                        /** @type {?} */
                        const node = data.node;
                        /** @type {?} */
                        const $span = $(node.span);
                        if (this.editable) {
                            /** @type {?} */
                            const _title = $span.find('> span.fancytree-title');
                            _title.css("background-color", "#FFF");
                            _title.css("outline", "2px solid #8ACFFF");
                        }
                        // Return false to prevent edit mode
                        return this.editable;
                    }),
                    edit: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    (event, data) => {
                        // Editor was opened (available as data.input)
                        // return this.editable;
                    }),
                    beforeClose: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    (event, data) => {
                        /** @type {?} */
                        const node = data.node;
                        /** @type {?} */
                        const $span = $(node.span);
                        // call edit item function to validate the entered string.
                        // this.itemEditEndHandler(new CustomTreeItem(node, this.commonService));
                        // Return false to prevent cancel/save (data.input is available)
                        // return true;
                    }),
                    save: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    (event, data) => {
                        // return false;
                    }),
                    close: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    (event, data) => {
                        // Editor was removed
                        if (data.save) {
                            // Since we started an async request, mark the node as preliminary
                            $(data.node.span).addClass('pending');
                        }
                        /** @type {?} */
                        let editedItem = new CustomTreeItem(data.node, this.commonService);
                        // editedItem.title = data.input.val();
                        this.itemEditEndHandler(editedItem);
                        // return this.editable;
                    })
                }
            });
            $.ui.fancytree.debugLevel = 2;
            /** @type {?} */
            const i = 0;
            //        $(".fancytree-container").toggleClass("fancytree-connectors");
            if (this.isFirstLoad) {
                /** @type {?} */
                const root = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getRootNode();
                for (var index = 0; index < root.getChildren().length; index++) {
                    this._tree_state.push(root.getChildren()[index].key);
                }
            }
            // update tree instance.
            this.logger.info('[ init ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ init ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    }
    /**
     * @param {?} id
     * @param {?} currentNode
     * @return {?}
     */
    findNodeinDataprovider(id, currentNode) {
        /** @type {?} */
        var i;
        /** @type {?} */
        var currentChild;
        /** @type {?} */
        var result;
        if (id == currentNode.key) {
            return currentNode;
        }
        else {
            // Use a for loop instead of forEach to avoid nested functions
            // Otherwise "return" will not work properly
            if (currentNode.length !== undefined) {
                for (i = 0; i < currentNode.length; i += 1) {
                    currentChild = currentNode[i];
                    // Search in the current child
                    result = this.findNodeinDataprovider(id, currentChild);
                    // Return the result if the node has been found
                    if (result !== false) {
                        return result;
                    }
                }
            }
            for (i = 0; currentNode.children !== undefined && i < currentNode.children.length; i += 1) {
                currentChild = currentNode.children[i];
                // Search in the current child
                result = this.findNodeinDataprovider(id, currentChild);
                // Return the result if the node has been found
                if (result !== false) {
                    return result;
                }
            }
            // The node has not been found and we have no more options
            return false;
        }
    }
    /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    recusiveSelectDataProvider(node, dataProvidernode) {
        dataProvidernode.selected = node.data.selected = node.selected;
        dataProvidernode.indeterminate = node.data.indeterminate = node.partsel;
        if (node.parent.title != 'root') {
            /** @type {?} */
            let parentProviderNode = dataProvidernode.parentData;
            this.recusiveSelectDataProvider(node.parent, parentProviderNode);
        }
        // if()
    }
    /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    recusiveSelectDataProviderChildren(node, dataProvidernode) {
        if (node.children && node.children.length > 0) {
            for (var index = 0; index < node.children.length; index++) {
                dataProvidernode.children[index].selected = node.children[index].data.selected = node.children[index].selected;
                dataProvidernode.children[index].indeterminate = node.children[index].data.indeterminate = node.children[index].partsel;
                if (node.children[index].children && node.children[index].children.length > 0)
                    this.recusiveSelectDataProviderChildren(node.children[index], dataProvidernode.children[index]);
            }
        }
    }
    /**
     * This method is used to remove tree node.
     * @param {?=} itemToRemove
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    removeNode(itemToRemove, map, addToChanges = true) {
        this.logger.info('[ removeNode ] START.');
        /** @type {?} */
        let item = null;
        /** @type {?} */
        const errorLocation = 0;
        try {
            this.editable = false;
            if (itemToRemove == null) {
                item = this.selectedItem;
            }
            else {
                item = itemToRemove;
            }
            if (item) {
                this._tree_state.splice(this._tree_state.indexOf(item.key), 1);
                item.remove();
            }
            if (addToChanges) {
                this.manageChangesArray(item, "D", null, map);
            }
        }
        catch (error) {
            this.logger.error('[ removeNode ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ removeNode ] END.');
    }
    /**
     * This method is used to determine the expander orientation according
     * to it's state (expanded/collapsed)
     * @private
     * @param {?} node
     * @return {?}
     */
    manageExpanderOrientation(node) {
        try {
            /** @type {?} */
            const $span = $(node.span);
            /** @type {?} */
            let isCustomIcons = false;
            /** @type {?} */
            var icon = null;
            if ($span.find('> span.fancytree-icon').length > 0) {
                icon = $span.find('> span.fancytree-icon');
                isCustomIcons = false;
            }
            else {
                icon = $span.find('> span.fancytree-custom-icon');
                isCustomIcons = true;
            }
            if (!isCustomIcons) {
                if (node.isExpanded()) {
                    icon.removeClass("swt-icon-folder-closed");
                    icon.addClass("swt-icon-folder-open");
                }
                else {
                    icon.removeClass("swt-icon-folder-open");
                    icon.addClass("swt-icon-folder-closed");
                }
            }
            /** @type {?} */
            const expander = $span.find('> span.fancytree-expander');
            expander.css("background-image", "url('icons.gif')");
            if (!node.hasChildren() && node.getLevel() !== 1) {
                expander.css("visibility", "hidden");
                icon.addClass("file");
            }
            else {
                if (node.isExpanded()) {
                    expander.css("background-position", "-48px -80px");
                }
                else {
                    expander.css("background-position", "0px -80px");
                }
            }
        }
        catch (e) {
            this.logger.error("manageExpanderOrientatio", e);
        }
    }
    /**
     * This method is used to remove all tree children.
     * @param {?=} addToChanges
     * @return {?}
     */
    removeAll(addToChanges = true) {
        this.logger.info('[ removeAll ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('destroy');
        }
        catch (error) {
            this.logger.error('[ removeAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ removeAll ] END.');
    }
    /**
     * This method is used to set tree item to editable.
     * @param {?} attributeName
     * @param {?=} attributesToFind
     * @param {?=} parentNode
     * @param {?=} additionalInformation
     * @param {?=} validateFunction
     * @return {?}
     */
    setEditableItem(attributeName, attributesToFind = null, parentNode = null, additionalInformation = null, validateFunction = null) {
        this.logger.info('[ setEditableItem ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            this.editableAttribute = attributeName.toString();
            this.editableAdditionalAttribute = additionalInformation;
            this.editableValidationFunction = validateFunction;
            /** @type {?} */
            let itemToEdit = null;
            //Find XML if attribute hashMap is passed
            if (attributesToFind != null && attributesToFind.size() > 0) {
                itemToEdit = this.findNode(attributesToFind, parentNode);
                this.selectedItem = itemToEdit;
            }
            // set the item as editable
            this.editable = true;
            this.editedItemPosition = { rowIndex: this.selectedIndex };
        }
        catch (error) {
            this.logger.error('[ setEditableItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ setEditableItem ] END.');
    }
    /**
     * Appends a node to tree, if afterNode is passed as parameter usually tree.selectedItem
     * then add node after the passed node else add it at the end
     * @param {?} aNode
     * @param {?=} afterNode
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    appendNode(aNode, afterNode = null, map = null, addToChanges = true) {
        this.logger.info('[ appendNode ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (aNode) {
                if (!aNode.key) {
                    aNode.key = "apnd_" + new Date().getTime();
                }
                // convert name or label to title.
                if (aNode.NAME !== undefined) {
                    aNode["title"] = aNode["NAME"];
                }
                else if (aNode.label !== undefined) {
                    aNode["title"] = aNode["label"];
                }
                if (afterNode) {
                    if (afterNode instanceof CustomTreeItem) {
                        if (aNode.level) {
                            if (this.getNumberFrom(aNode.level) == afterNode.getNode().getLevel() + 1) {
                                aNode.level = "Level" + this.getNumberFrom(aNode.level);
                                ((/** @type {?} */ (afterNode))).getNode().addNode(aNode, "firstChild");
                                /** @type {?} */
                                let afterKey = afterNode.key;
                                this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                                !afterNode.isExpanded() ? afterNode.expand() : null;
                            }
                            else {
                                aNode.level = "Level" + afterNode.getNode().getLevel();
                                ((/** @type {?} */ (afterNode))).getNode().appendSibling(aNode);
                                /** @type {?} */
                                let afterKey = afterNode.key;
                                this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                            }
                        }
                        else {
                            aNode.level = "Level" + afterNode.getNode().getLevel();
                            ((/** @type {?} */ (afterNode))).getNode().addNode(aNode, "after");
                            /** @type {?} */
                            let afterKey = afterNode.key;
                            this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                        }
                    }
                }
                else {
                    this.getInstance().getRootNode().addChildren(aNode);
                    this._tree_state.push(aNode.key);
                }
            }
        }
        catch (error) {
            this.logger.error('[ appendNode ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ appendNode ] END.');
    }
    /**
     * This method is used to expand all tree nodes.
     * @param {?=} expandToLvl
     * @return {?}
     */
    expandAll(expandToLvl) {
        this.logger.info('[ expandAll ] START.');
        /** @type {?} */
        const errorLocation = 0;
        /** @type {?} */
        let hide;
        /** @type {?} */
        const targetLevel = expandToLvl ? Number(expandToLvl.match(/(\d+)/)[0]) : -1;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                /** @type {?} */
                let item = new CustomTreeItem(node, this.commonService);
                if (this._hideFunction) {
                    hide = this._hideFunction(item);
                    if (hide + "" !== "true") {
                        if (expandToLvl && targetLevel !== -1) {
                            if (node.getLevel() <= targetLevel) {
                                // node.setExpanded();
                                item.expand();
                            }
                        }
                        else {
                            // node.setExpanded();
                            item.expand();
                        }
                    }
                    else {
                        $(node.span).closest('tr').addClass('fancytree-helper-hidden');
                    }
                }
                else {
                    if (expandToLvl && targetLevel !== -1) {
                        if (node.getLevel() <= targetLevel) {
                            // node.setExpanded();
                            item.expand();
                        }
                    }
                    else {
                        // node.setExpanded();
                        item.expand();
                    }
                }
            }));
        }
        catch (error) {
            this.logger.error('[ expandAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandAll ] END.');
    }
    /**
     * Close all nodes
     * @return {?}
     */
    collapseAll() {
        this.logger.info('[ collapseAll ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                node.setExpanded(false);
            }));
        }
        catch (error) {
            this.logger.error('[ expandAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ collapseAll ] END.');
    }
    /**
     * @param {?} Comp
     * @return {?}
     */
    setDataTip(Comp) {
        try {
            /** @type {?} */
            const compRef = this.commonService.resolver.resolveComponentFactory(AdvancedToolTip).create(this.commonService.injector);
            /** @type {?} */
            const child = compRef.instance.addChild(Comp);
            // attach component to current application.
            this.commonService.applicationRef.attachView(compRef.hostView);
            // get component root node (component selector).
            /** @type {?} */
            const selector = (/** @type {?} */ (((/** @type {?} */ (compRef.hostView))).rootNodes[0]));
            // attach component root node to DOM.
            $(this.treeTipHolder.nativeElement).append(selector);
            this._dataTip = compRef.instance;
            return compRef.instance;
        }
        catch (error) {
            this.logger.error("setDataTip error: ", error);
        }
    }
    /**
     * @return {?}
     */
    getDataTip() {
        return this._dataTip;
    }
    /**
     * This method is used to get selected Items.
     * @return {?}
     */
    selectedItems() {
        this.logger.info('[ selectedItems ] START.');
        /** @type {?} */
        const errorLocation = 0;
        /** @type {?} */
        let items = [];
        try {
            if (this.selectedIndices.length > 0) {
                for (let i = 0; i < this.selectedIndices.length; i++) {
                    /** @type {?} */
                    let key = this._tree_state[this.selectedIndices[i]];
                    /** @type {?} */
                    let node = ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(key);
                    items.push(new CustomTreeItem(node, this.commonService));
                }
            }
            return items;
        }
        catch (error) {
            this.logger.error('[ selectedItems ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectedItems ] END.');
    }
    /**
     * This method is used to validate Display List.
     * @return {?}
     */
    validateDisplayList() {
        this.logger.info('[ validateDisplayList ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            //TODO
        }
        catch (error) {
            this.logger.error('[ validateDisplayList ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ validateDisplayList ] END.');
    }
    /**
     * This method is used reopens the already opened items
     * @return {?}
     */
    reOpenSavedState() {
        this.logger.info('[ reOpenSavedState ] START.');
        /** @type {?} */
        const errorLocation = 0;
        /** @type {?} */
        var i = 0;
        try {
            /*($(this.elem.nativeElement.children[0].children[0]) as any).fancytree('getTree').visit((node) => {
                if (this.openItems.indexOf(node.data.id) !== -1) {
                    this.expandItem({key: node.key}, true);
                    i++;
                }
            });*/
            if (this.saveTreeStateBasedOn) {
                this.openItems.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                (item) => {
                    ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
                     * @param {?} node
                     * @return {?}
                     */
                    (node) => {
                        if (node.data[this._saveTreeStateBasedOn] == item.data[this._saveTreeStateBasedOn]) {
                            node.setExpanded();
                        }
                    }));
                }));
            }
            else {
                this.openItems.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                (item) => {
                    // item.setExpanded(true);
                    ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(item.key + '').setExpanded();
                }));
            }
            // selected saved index
            this.selectedIndex = this._tempIndex;
        }
        catch (error) {
            this.logger.error('[ reOpenSavedState ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ reOpenSavedState ] END.');
    }
    /**
     * Save the tree open state
     * @return {?}
     */
    saveTreeOpenState() {
        this.logger.info('[ saveTreeOpenState ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            // ($(this.treeContainer.nativeElement) as any).fancytree('getTree').visit((node) => {
            //     if (node.isExpanded()) {
            //         if (this._openItems.indexOf(node.data.id) === -1) {
            //             this._openItems.push(node.data.id);
            //         }
            //     }
            // });
            this._openItems = this.getOpenedItems();
            this._closeItems = this.getClosedItems();
            // save selected index.
            this._tempIndex = this.selectedIndex;
        }
        catch (error) {
            this.logger.error('[ saveTreeOpenState ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ saveTreeOpenState ] END.');
    }
    /**
     * Opens or closes a branch item.
     * When a branch item opens, it restores the open and closed states
     * of its child branches if they were already opened.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    expandItem(item, open, animate, dispatchEvent, cause) {
        this.logger.info('[ expandItem ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            /** @type {?} */
            var _nodePath = (((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(item.key + '').getKeyPath(false)).split('/');
            errorLocation = 10;
            _nodePath = _nodePath.slice(1);
            errorLocation = 20;
            for (var key = 0; key < _nodePath.length; key++) {
                errorLocation = 30;
                ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(_nodePath[key] + '').setExpanded();
                errorLocation = 40;
            }
        }
        catch (error) {
            this.logger.error('[ expandItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandItem ] END.');
    }
    /**
     * This method is used to set custom style function.
     * @param {?} item
     * @return {?}
     */
    customStyleFunction(item) {
        this.logger.info('[ customStyleFunction ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (this.eventlist[CustomTreeEvent.ITEMRENDER]) {
                this.eventlist[CustomTreeEvent.ITEMRENDER](item);
            }
            // call icon function if exist.
            return this.iconFunction(item);
        }
        catch (error) {
            this.logger.error('[ customStyleFunction ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ customStyleFunction ] END.');
    }
    /**
     * This method is used to get Item Index.
     * @param {?} item
     * @return {?}
     */
    getItemIndex(item) {
        this.logger.info('[ expandItem ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            return item.key ? this._tree_state.indexOf(item.key) : -1;
        }
        catch (error) {
            this.logger.error('[ expandItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandItem ] END.');
    }
    /**
     * This method is used to cleans CRUD changes.
     * @return {?}
     */
    clearChanges() {
        this.logger.info('[ clearChanges ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            this.changes.clear();
        }
        catch (error) {
            this.logger.error('[ clearChanges ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ clearChanges ] END.');
    }
    /**
     * Returns the level of the selected node, result is Level1, Level2..Leveln
     * @return {?}
     */
    getSelectedLevel() {
        return this._selectedLevel;
    }
    /**
     * public method used to return tree Instance.
     * @return {?}
     */
    getInstance() {
        return this._instance;
    }
    /**
     * manageChangesArray
     *
     * <AUTHOR> M.Bouraoui
     *
     * This method is used to log the crud opertaions in the changes HashMap
     * @param {?} dataprovider
     * @param {?} item
     * @param {?=} operation
     * @return {?}
     */
    dataProviderCRUD(dataprovider, item, operation = 0) {
        this.logger.info('[ dataProviderCRUD ] START.');
        /** @type {?} */
        const errorLocation = 0;
        /** @type {?} */
        var obj = null;
        try {
            if (item) {
                for (var index = 0; index < dataprovider.length; index++) {
                    obj = dataprovider[index];
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof (obj[key]) === 'object') {
                                this.dataProviderCRUD(obj[key], item, operation);
                            }
                            if (JSONReader.compareJSON(obj, item)) {
                            }
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ dataProviderCRUD ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ dataProviderCRUD ] END.');
    }
    /**
     * This method is used to diselect All selected items of tree.
     * @return {?}
     */
    diselectAll() {
        if (!this.addCheckbox) {
            ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                if (node.isSelected()) {
                    $(node.span).closest('tr').removeClass("fancytree-selected");
                }
                if (node.isActive()) {
                    $(node.span).closest('tr').removeClass("fancytree-active");
                }
            }));
            this._selectedIndex = this._selectedLevel = this._selectedItem = null;
            this._selectedIndices = new Array();
        }
    }
    /**
     * This method will be used to update the tree state
     * when the given node is expanded.
     * @private
     * @param {?} node
     * @return {?}
     */
    updateStateOnExpand(node) {
        try { // start of code used to update tree state capture
            // start of code used to update tree state capture
            /** @type {?} */
            const _children = new Array();
            if (node.getChildren()) {
                for (var index = 0; index < node.getChildren().length; index++) {
                    _children.push(node.getChildren()[index].key);
                }
            }
            /** @type {?} */
            const _exist = this._tree_state.join().indexOf(_children.join()) !== -1 ? true : false;
            if (this._tree_state.indexOf(_children[0]) === -1) {
                this._tree_state.splice(this._tree_state.indexOf(node.key) + 1, 0, ..._children);
            }
        }
        catch (e) {
            this.logger.error("updateStateOnExpand ", e);
        }
    }
    /**
     * @private
     * @param {?} targetItem
     * @return {?}
     */
    itemEditEndHandler(targetItem) {
        // Get the new value from the editor.
        /** @type {?} */
        var newVal = targetItem.title;
        /** @type {?} */
        var valid = true;
        this.log.info("Function [itemEditEndHandler] is called newVal: " + newVal);
        //Validate new entred value
        if (this.editableValidationFunction != null) {
            valid = this.editableValidationFunction(newVal);
        }
        // set new value if it's valid
        if (valid) {
            ((/** @type {?} */ (this.selectedItem)))[this.editableAttribute] = newVal;
            //save changes
            this.manageChangesArray(this.selectedItem, "U", this.editableAttribute, this.editableAdditionalAttribute);
        }
        this.editable = false;
        this.itemEditEnd.emit(targetItem);
    }
    /**
     * @private
     * @param {?} node
     * @param {?} levelIOrder
     * @return {?}
     */
    sortNodeBy(node, levelIOrder) {
        try {
            /** @type {?} */
            const levelIOrderAttr = StringUtils.trim(levelIOrder);
            /** @type {?} */
            const indexOf_Eq = levelIOrderAttr.indexOf('=');
            /** @type {?} */
            const indexOf_Colon = levelIOrderAttr.indexOf(":");
            /** @type {?} */
            const sortDirection = levelIOrder.indexOf("DESC") != -1 ? "DESC" : "ASC";
            /** @type {?} */
            const attrName = levelIOrder.indexOf("=") != -1 ? levelIOrder.split("=")[0] : levelIOrder;
            if (node) {
                if (sortDirection === "ASC") {
                    node.sortChildren((/**
                     * @param {?} n1
                     * @param {?} n2
                     * @return {?}
                     */
                    (n1, n2) => {
                        return n1.data[attrName] === n2.data[attrName] ? 0 : n1.data[attrName] > n2.data[attrName] ? 1 : -1;
                    }), false);
                }
                else {
                    node.sortChildren((/**
                     * @param {?} n1
                     * @param {?} n2
                     * @return {?}
                     */
                    (n1, n2) => {
                        return n1.data[attrName] === n2.data[attrName] ? 0 : n1.data[attrName] < n2.data[attrName] ? 1 : -1;
                    }), false);
                }
            }
        }
        catch (e) {
            this.log.error("sortNodeBy: ", e);
        }
    }
    /**
     * This method is used to navigate to bottom.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    scrollToBottom(event, node) {
        this.logger.info('[ scrollToBottom ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            // make reference to list items.
            /** @type {?} */
            const tree = this.elem.nativeElement.children[0];
            // get list items height.
            /** @type {?} */
            const treeHeight = Number($(this.elem.nativeElement).height());
            // get item index.
            /** @type {?} */
            const itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                const item = tree.children[itemIndex];
                // check existence of item.
                if (node) {
                    // get item height.
                    /** @type {?} */
                    const itemHeight = 27 /*Number($($(this.selectedItem.li.children[0])[0]).height())*/;
                    // calculate item top position.
                    /** @type {?} */
                    const itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    const itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    const viewBottom = this.verticalScrollPosition + treeHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        this.verticalScrollPosition = itemBottom - treeHeight;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ scrollToBottom ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ scrollToBottom ] END.');
    }
    /**
     * This method is used to navigate to top.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    scrollToTop(event, node) {
        this.logger.info('[ scrollToTop ] START.');
        /** @type {?} */
        const errorLocation = 0;
        try {
            // make reference to list items.
            /** @type {?} */
            const tree = this.elem.nativeElement.children[0];
            // get list items height.
            /** @type {?} */
            const treeHeight = Number($(this.elem.nativeElement).height());
            // get item index.
            /** @type {?} */
            const itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                const item = tree.children[itemIndex];
                // check existence of item.
                if (node) {
                    // get item height.
                    /** @type {?} */
                    const itemHeight = 27 /*Number($($(this.selectedItem.li.children[0])[0]).height())*/;
                    // calculate item top position.
                    /** @type {?} */
                    const itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    const itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    const viewBottom = this.verticalScrollPosition + treeHeight;
                    // update scroll bar position.
                    if (itemTop < this.verticalScrollPosition) {
                        this.verticalScrollPosition = itemTop;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ scrollToTop ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ scrollToTop ] END.');
    }
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @param {?} parent
     * @return {?}
     */
    recursive(value, parent) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            // test if value is an object.
            if (value.length === undefined) {
                value = [value];
            }
            errorLocation = 10;
            // loop to data
            for (let index = 0; index < value.length; index++) {
                /** @type {?} */
                const leaf = value[index];
                if (leaf.NAME) {
                    leaf['title'] = leaf.NAME;
                }
                else if (leaf.label || leaf.label == "") {
                    leaf['title'] = leaf.label;
                }
                /*
                 * if the tree leaf isBranch, that means this leaf has children
                * so put folder icon by default and this icon can be changed by
                * custom icon in the tree use (with icon function).
                * */
                if (leaf.isBranch) {
                    if (Boolean(leaf.isBranch)) {
                        leaf['folder'] = true;
                    }
                    else {
                        leaf['icon'] = "file";
                    }
                }
                else { // if isBranch attribute is not exist put file icon.
                    leaf['icon'] = "file";
                }
                // generate dynamic item key.
                leaf['key'] = 'lf_' + this.leaf_key;
                this.leaf_key++;
                // loop leaf keys.
                for (const key in leaf) {
                    if (typeof (leaf[key]) === 'object' && key != "parentData" && key != "children") {
                        if (leaf[key].length) { // if leaf has children.
                            leaf['children'] = leaf[key];
                            // leaf['folder'] = true;
                        }
                        else { // if leaf has one children.
                            leaf['children'] = [leaf[key]];
                            // leaf['folder'] = true;
                        }
                        // check if leaf is Branch.
                        // if (leaf.isBranch) {
                        //     leaf['folder'] = true;
                        // }
                        if (leaf.checked) {
                            // leaf["checkbox"] = leaf.checked;
                        }
                        this.recursive(leaf[key], leaf);
                    }
                    else {
                        // if (leaf.isBranch) {
                        //     leaf['folder'] = true;
                        // }
                    }
                    // if (leaf.isBranch) {
                    //     if (Boolean(leaf.isBranch)) {
                    //         leaf['folder'] = true;
                    //         if (leaf['children'] === undefined) {
                    //             leaf['children'] = [];
                    //         }
                    //     } else {
                    //
                    //     }
                    // } else {
                    //     if (leaf[key].children) {
                    //         leaf['folder'] = true;
                    //     }
                    // }
                    leaf['folder'] = true;
                    leaf['parentData'] = parent;
                }
            }
        }
        catch (error) {
            this.logger.error('[ recursive ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
        return value;
    }
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    getOpenedItems() {
        /** @type {?} */
        const rtn = new Array();
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                if (node.isExpanded()) {
                    rtn.push(node);
                }
            }));
            return rtn;
        }
        catch (error) {
            console.error("getOpenedItems error: ", error);
        }
    }
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    getClosedItems() {
        /** @type {?} */
        const rtn = new Array();
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            (node) => {
                if ((node.expanded != undefined) && !node.isExpanded()) {
                    rtn.push(node);
                }
            }));
            return rtn;
        }
        catch (error) {
            console.error("getClosedItems error: ", error);
        }
    }
    /**
     * @private
     * @param {?} node
     * @return {?}
     */
    selectNode(node) {
        if (node) {
            // node.setSelected(true);
            this._selectedItem = new CustomTreeItem(node, this.commonService);
            this._selectedLevel = 'Level' + node.getLevel();
        }
    }
    /**
     * @private
     * @return {?}
     */
    getScrollPosition() {
        return this.selectedIndex * 20;
    }
}
CustomTree.seqAttribute = 'TREE_ITEM_LOCAL_SEQ';
CustomTree.TREE_STR = 'Tree';
CustomTree.LEVEL_1_STR = 'Level1';
CustomTree.LEVEL_2_STR = 'Level2';
CustomTree.LEVEL_3_STR = 'Level3';
CustomTree.CRUD_OPERATION = "crud_operation";
CustomTree.CRUD_DATA = "crud_data";
CustomTree.decorators = [
    { type: Component, args: [{
                selector: 'CustomTree',
                template: `
        <!--<div id="{{id}}" selector="SwtCustomTree" #treeContainer class="treeContainer {{ styleName }}"></div>-->
        <div class="customTreeWarapper">
            <table #treeContainer class="treeContainer {{ styleName }}">
                <tr style="height: 23px">
                    <td></td>
                </tr>
            </table>
        </div>
        <span #treeTipHolder></span>
    `,
                styles: [`
        :host {
            display: block;
            /*background-color: #FFF;*/
            /*overflow: auto;*/
            width: 300px;
            height: 450px;
        }

        .customTreeWarapper {
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: #FFF;
            border: 1px solid #B7BABC;
            line-height: initial;
        }

        .customTreeWarapper table td, table th{
            padding : 2px;
        }



        .treeContainer {
            width: 100%;
            padding-left: 3px;
        }

        .fancytree-container {
            outline: none;
            border: none;
        }

        .fancytree-container:focus {
            outline: none;
            border: none;
        }
    `]
            }] }
];
/** @nocollapse */
CustomTree.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
CustomTree.propDecorators = {
    ITEM_CLICK: [{ type: Output, args: ['itemClick',] }],
    ITEM_ACTIVATE: [{ type: Output, args: ['itemActivate',] }],
    ITEM_DBCLICK: [{ type: Output, args: ['dbClick',] }],
    MOUSE_OUT: [{ type: Output, args: ['mouseOut',] }],
    itemOpen: [{ type: Output, args: ['itemOpen',] }],
    itemClose: [{ type: Output, args: ['itemClose',] }],
    MOUSE_OVER: [{ type: Output, args: ['mouseOver',] }],
    FOCUS_IN: [{ type: Output, args: ['focusIn',] }],
    FOCUS_OUT: [{ type: Output, args: ['focusOut',] }],
    id: [{ type: Input, args: ['id',] }],
    styleName: [{ type: Input, args: ['styleName ',] }],
    itemEditEnd: [{ type: Output, args: ["itemEditEnd",] }],
    treeContainer: [{ type: ViewChild, args: ['treeContainer',] }],
    treeTipHolder: [{ type: ViewChild, args: ['treeTipHolder',] }],
    level2Order: [{ type: Input }],
    level3Order: [{ type: Input }],
    level4Order: [{ type: Input }],
    allowMultipleSelection: [{ type: Input }],
    dataProvider: [{ type: Input }],
    width: [{ type: Input }],
    height: [{ type: Input }],
    hideIcons: [{ type: Input }],
    addCheckbox: [{ type: Input }],
    indeterminateCheckbox: [{ type: Input }],
    saveTreeStateBasedOn: [{ type: Input }],
    selectedItem: [{ type: Input }],
    doubleClickEnabled: [{ type: Input }],
    enabled: [{ type: Input }],
    level0Order: [{ type: Input }],
    level1Order: [{ type: Input }],
    dragMoveEnabled: [{ type: Input }],
    editable: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    CustomTree.seqAttribute;
    /** @type {?} */
    CustomTree.TREE_STR;
    /** @type {?} */
    CustomTree.LEVEL_1_STR;
    /** @type {?} */
    CustomTree.LEVEL_2_STR;
    /** @type {?} */
    CustomTree.LEVEL_3_STR;
    /** @type {?} */
    CustomTree.CRUD_OPERATION;
    /** @type {?} */
    CustomTree.CRUD_DATA;
    /** @type {?} */
    CustomTree.prototype.firstLoad;
    /** @type {?} */
    CustomTree.prototype.local_sequence;
    /** @type {?} */
    CustomTree.prototype.CRUD_OPERATION;
    /** @type {?} */
    CustomTree.prototype.CRUD_DATA;
    /** @type {?} */
    CustomTree.prototype.iconFunction;
    /** @type {?} */
    CustomTree.prototype.ITEM_CLICK;
    /** @type {?} */
    CustomTree.prototype.ITEM_ACTIVATE;
    /** @type {?} */
    CustomTree.prototype.ITEM_DBCLICK;
    /** @type {?} */
    CustomTree.prototype.MOUSE_OUT;
    /** @type {?} */
    CustomTree.prototype.itemOpen;
    /** @type {?} */
    CustomTree.prototype.itemClose;
    /** @type {?} */
    CustomTree.prototype.MOUSE_OVER;
    /** @type {?} */
    CustomTree.prototype.FOCUS_IN;
    /** @type {?} */
    CustomTree.prototype.FOCUS_OUT;
    /** @type {?} */
    CustomTree.prototype.id;
    /** @type {?} */
    CustomTree.prototype.styleName;
    /** @type {?} */
    CustomTree.prototype.buttonMode;
    /** @type {?} */
    CustomTree.prototype.labelFunction;
    /** @type {?} */
    CustomTree.prototype.dataTipFunction;
    /** @type {?} */
    CustomTree.prototype._tree_state;
    /** @type {?} */
    CustomTree.prototype.itemEditEnd;
    /**
     * private array to hold tree options.
     * private.
     * @type {?}
     * @protected
     */
    CustomTree.prototype.options;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.isItemSelected;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dbclick;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._tempIndex;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._selectedLevel;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._keyDownFlag;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level0OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level1OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level2OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level3OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level4OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._globalFindStop;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableAttribute;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableAdditionalAttribute;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableValidationFunction;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.logger;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.leaf_key;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._instance;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._firstLoad;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.treeContainer;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.treeTipHolder;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.__OriginalDataProvider;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dataTip;
    /** @type {?} */
    CustomTree.prototype._hideIcons;
    /** @type {?} */
    CustomTree.prototype._addCheckbox;
    /** @type {?} */
    CustomTree.prototype._indeterminateCheckbox;
    /** @type {?} */
    CustomTree.prototype._saveTreeStateBasedOn;
    /** @type {?} */
    CustomTree.prototype._changes;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._hideFunction;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level2Order;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level3Order;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level4Order;
    /**
     * private.
     * @type {?}
     * @protected
     */
    CustomTree.prototype._allowMultipleSelection;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._selectedIndices;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._width;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._height;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._doubleClickEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectable;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._level1Order;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._level0Order;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._iconWidth;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._iconSpacing;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._labelSpacing;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._levelOfs;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._dragMoveEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._dragEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._openItems;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._closeItems;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._verticalScrollPosition;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._showRoot;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._editedItemPosition;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.commonService;
}
export class CustomTreeItem extends UIComponent {
    /**
     * CustomTreeItem constructor.
     * @param {?} itemElement
     * @param {?} commonServ
     */
    constructor(itemElement, commonServ) {
        super(itemElement.span, commonServ);
        this.itemElement = itemElement;
        this.commonServ = commonServ;
        // private variable to hold item parent.
        this.parent = null;
        // private variable will be true if the item is expanded.
        this._expanded = false;
        // private variable to contains all item children.
        this._children = [];
        // private variable will be true if this item is editable.
        this._editable = false;
        // private variable to hold customToolTip.
        this._customToolTip = null;
        // This varible will be true when we editing the current item.
        this._editMode = false;
        // private variable will be true if the item is selected.
        this._selected = false;
        // private variable to specify if this item has folder icon or not.
        this._folder = true;
        this._visible = true;
        // private variable to hold tree item icon.
        this._icon = "";
        // private variable to hold tree item title.
        this._title = "";
        // private variable to hold tree item expander.
        this._expander = "";
        // private variable to hold tree item key.
        this._key = "";
        // private variable to set item tooltip.
        this._toolTip = "";
        // private variable to set item height.
        this._height = "";
        // private variable to set tree item level.
        this._level = "";
        try {
            this.node = itemElement;
            this.data = itemElement.data;
            this._title = itemElement.title;
            this._icon = itemElement.icon;
            this._key = itemElement.key;
            this._folder = itemElement.folder;
            this._expanded = itemElement.expanded;
            this._selected = itemElement.selected;
            this._toolTip = itemElement.tooltip;
            if (itemElement.parent)
                this.parent = itemElement.parent;
            this._level = "Level" + this.node.getLevel();
            if (itemElement.children) {
                for (var index = 0; index < itemElement.children.length; index++) {
                    this._children.push(new CustomTreeItem(itemElement.children[index], commonServ));
                }
            }
            /**
             * Bind data in tree item.
             */
            if (this.data) {
                for (const attribute in this.data) {
                    if (this.data.hasOwnProperty(attribute)) {
                        // this[attribute] = this._data[attribute];
                        if (isNaN(Number(attribute))) {
                            // CustomTreeItem[attribute] = this._data[attribute];
                            this.bindAttribute(attribute, this.data[attribute]);
                        }
                    }
                }
            }
        }
        catch (e) {
            this.log.error('CustomTreeItem constructor: ', e);
        }
    }
    /**
     * @return {?}
     */
    get editMode() {
        return this._editMode;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set editMode(value) {
        if (this._editMode === value) {
            return;
        }
        this._editMode = value;
        this._editMode ? this.triggerEdit() : this.stopEdit();
    }
    /**
     * This setter to make item selected.
     * @param {?} value
     * @return {?}
     */
    set selected(value) {
        this._selected = value;
        // this.itemElement.setSelected(value);
    }
    /**
     * @return {?}
     */
    get labelObject() {
        return this._labelObject;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set labelObject(value) {
        this._labelObject = value;
    }
    /**
     * @return {?}
     */
    get folder() {
        return this._folder;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set folder(value) {
        this._folder = value;
        this.node.folder = this._folder;
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        this._visible = value;
        this.itemVisibilityState(this._visible);
    }
    /**
     * icon getter
     * @return {?}
     */
    get icon() {
        return this._icon;
    }
    /**
     * icon setter.
     * @param {?} value
     * @return {?}
     */
    set icon(value) {
        if (typeof value === "string") {
            /** @type {?} */
            const $span = $(this.node.span);
            /** @type {?} */
            const icon = $span.find('> span.' + this._icon);
            icon.removeClass(this._icon).addClass(value);
            this._icon = value;
        }
        this.node.icon = value;
    }
    /**
     * title getter.
     * @return {?}
     */
    get title() {
        return this._title;
    }
    /**
     * title setter.
     * @param {?} value
     * @return {?}
     */
    set title(value) {
        this._title = value;
    }
    /**
     * expander getter.
     * @return {?}
     */
    get expander() {
        return this._expander;
    }
    /**
     * expander setter.
     * @param {?} value
     * @return {?}
     */
    set expander(value) {
        this._expander = value;
    }
    /**
     * This method is used to get the key of
     * current item.
     * @return {?}
     */
    get key() {
        return this._key;
    }
    /**
     * toolTip getter.
     * @return {?}
     */
    get toolTip() {
        return this._toolTip;
    }
    /**
     * toolTip setter.
     * @param {?} value
     * @return {?}
     */
    set toolTip(value) {
        this._toolTip = value;
    }
    /**
     * height getter.
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /**
     * height setter.
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = value;
    }
    /**
     * level getter.
     * @return {?}
     */
    get level() {
        return this._level;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set level(value) {
        this._level = value;
        this.itemElement.level = this.getNumberFrom(value);
    }
    /**
     * @return {?}
     */
    getNode() {
        return this.itemElement;
    }
    /**
     * return index of the current item
     * relative to its parent
     * @return {?}
     */
    childIndex() {
        /** @type {?} */
        let index = 0;
        try {
            /** @type {?} */
            let children = this.getParent().getChildren();
            for (let i = 0; i < children.length; i++) {
                if (children[i].key === this.key) {
                    index = i;
                    break;
                }
            }
        }
        catch (e) {
            this.log.error(e);
        }
        return index;
    }
    /**
     * This method is used to get parent item
     * of the current tree item.
     * @return {?}
     */
    getParent() {
        this.parent = new CustomTreeItem(this.itemElement.parent, this.commonServ);
        return this.parent;
    }
    /**
     * This method is used to get children of current tree item.
     * @return {?}
     */
    getChildren() {
        return this._children;
    }
    /**
     * return true if the item is expanded.
     * @return {?}
     */
    isExpanded() {
        return this._expanded;
    }
    /**
     * return true if the item is editable.
     * @return {?}
     */
    isEditable() {
        return this._editable;
    }
    /**
     * return true if the item is selected.
     * @return {?}
     */
    isSelected() {
        return this._selected;
    }
    /**
     * This method is used to set data to the current
     * item.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    setData(prop, value) {
        this.data[prop] = value;
    }
    /**
     * This method with optional property is used to get the data
     * if prop is empty else return the given property.
     * @param {?=} prop
     * @return {?}
     */
    getData(prop = "") {
        if (prop && prop !== "") {
            return this.data[prop];
        }
        return this.data;
    }
    /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    expand() {
        try {
            this.itemElement.setExpanded(true);
        }
        catch (error) {
            this.log.error("expand - error : ", error);
        }
    }
    /**
     * This method is used to collapse the current item.
     * @return {?}
     */
    collapse() {
        try {
            this.itemElement.setExpanded(false);
        }
        catch (error) {
            this.log.error("collapse - error : ", error);
        }
    }
    /**
     * This method is used to add a sub item to the current item.
     * @param {?} item
     * @return {?}
     */
    appendItem(item) {
        try {
            this.folder = true;
            this.node.addChildren(item);
            this.node.render(true);
        }
        catch (error) {
            this.log.error("appendItem - error : ", error);
        }
    }
    /**
     * This method is used to remove the current item.
     * @return {?}
     */
    remove() {
        try {
            if (!this._editMode) {
                this.itemElement.remove();
            }
            else {
                this.stopEdit();
                this.itemElement.remove();
            }
        }
        catch (error) {
            this.log.error("remove - error : ", error);
        }
    }
    /**
     * This method trigger the edit mode of the
     * current CustomTreeItem.
     * @return {?}
     */
    triggerEdit() {
        this.itemElement.editStart();
        this._editMode = true;
    }
    /**
     * This method stop the editing mode
     * of the current item.
     * @param {?=} applyChanges
     * @return {?}
     */
    stopEdit(applyChanges = false) {
        this.itemElement.editEnd(applyChanges);
        this._editMode = false;
    }
    /**
     * This method is used to get the root of the current item.
     * @return {?}
     */
    getRoot() {
        try {
            return new CustomTreeItem(this.node.getRootNode(), this.commonServ);
        }
        catch (error) {
            this.log.error("getRoot - error : ", error);
        }
    }
    /**
     * This method is used to detect if the current item
     * has a children or not
     * it will return true | false | undefined.
     * @return {?}
     */
    hasChildren() {
        try {
            return this.node.hasChildren();
        }
        catch (e) {
            this.log.error("hasChildren - error: ", e);
        }
    }
    /**
     * @param {?} tooltip
     * @return {?}
     */
    setCustomTooltip(tooltip) {
        try {
            // $(this.node.span).append("<AdvancedToolTip></AdvancedToolTip>")
            // this.addChild(AdvancedToolTip);
            /** @type {?} */
            const compRef = this.commonServ.resolver.resolveComponentFactory(AdvancedToolTip).create(this.commonServ.injector);
            /** @type {?} */
            const child = compRef.instance.addChild(tooltip);
            // attach component to current application.
            this.commonServ.applicationRef.attachView(compRef.hostView);
            // get component root node (component selector).
            /** @type {?} */
            const selector = (/** @type {?} */ (((/** @type {?} */ (compRef.hostView))).rootNodes[0]));
            // attach component root node to DOM.
            $(this.node.span).append(selector);
            this._customToolTip = compRef.instance;
            this._customToolTip.child = child;
            return compRef.instance;
        }
        catch (error) {
            this.log.error("setCustomTooltip - error: ", error);
        }
    }
    /**
     * @return {?}
     */
    localName() {
        return this._level;
    }
    /**
     * @return {?}
     */
    getCustomToolTip() {
        return this._customToolTip;
    }
    /**
     * @param {?} visibility
     * @return {?}
     */
    setVisible(visibility) {
        this._visible = visibility;
        this.itemVisibilityState(this._visible);
    }
    /**
     * @private
     * @param {?} visibility
     * @return {?}
     */
    itemVisibilityState(visibility) {
        // setTimeout(() => {
        //     if (!visibility) {
        //         $(this.itemElement.span).closest('tr').addClass('fancytree-helper-hidden');
        //     } else {
        //         $(this.itemElement.span).closest('tr').removeClass('fancytree-helper-hidden');
        //     }
        // }, 0);
    }
}
CustomTreeItem.decorators = [
    { type: Injectable }
];
/** @nocollapse */
CustomTreeItem.ctorParameters = () => [
    { type: undefined },
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    CustomTreeItem.prototype.data;
    /** @type {?} */
    CustomTreeItem.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.node;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._expanded;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._children;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._customToolTip;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._toolTipchild;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._editMode;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._labelObject;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._folder;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._icon;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._title;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._expander;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._key;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._height;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._level;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.itemElement;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.commonServ;
}
export class TreeLabel extends UIComponent {
    /**
     * @param {?} lblelement
     * @param {?} commonService
     */
    constructor(lblelement, commonService) {
        super(lblelement, commonService);
        this.lblelement = lblelement;
        this.commonService = commonService;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set item(value) {
        this._item = value;
    }
    /**
     * @return {?}
     */
    getParentItem() {
        return this._item;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype._item;
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype.lblelement;
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype.commonService;
}
export class TreeIcon extends UIComponent {
    /**
     * @param {?} iconElement
     * @param {?} commonService
     */
    constructor(iconElement, commonService) {
        super(iconElement, commonService);
        this.iconElement = iconElement;
        this.commonService = commonService;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeIcon.prototype.iconElement;
    /**
     * @type {?}
     * @private
     */
    TreeIcon.prototype.commonService;
}
export class TreeExpander extends UIComponent {
    /**
     * @param {?} expanderElement
     * @param {?} commonService
     */
    constructor(expanderElement, commonService) {
        super(expanderElement, commonService);
        this.expanderElement = expanderElement;
        this.commonService = commonService;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeExpander.prototype.expanderElement;
    /**
     * @type {?}
     * @private
     */
    TreeExpander.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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