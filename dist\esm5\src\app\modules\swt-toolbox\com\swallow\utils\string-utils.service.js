/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { XML } from '../xmlhandler/swt-xml.service';
import { HashMap } from './HashMap.service';
import * as moment_ from 'moment-mini';
/** @type {?} */
var moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
//@dynamic
var StringUtils = /** @class */ (function () {
    function StringUtils() {
    }
    /**
    *	unformats a formatted amount
    *
    *	@param amount The amount string to be formatted
    *   @amountPattern:    0 or others : e.g 12,345,698.50
    *                      1: e.g 12.345.698,50
    *                      2: e.g 12 345 698.50
    *                      3: e.g 12 345 698,50
    *	@returns string The unformatted amount
    *
    * 	@langversion Angular
    *
    */
    /**
     * 	unformats a formatted amount
     *
     * \@amountPattern: 0 or others : e.g 12,345,698.50
     *                      1: e.g 12.345.698,50
     *                      2: e.g 12 345 698.50
     *                      3: e.g 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @return {?} string The unformatted amount
     *
     */
    StringUtils.unformatAmount = /**
     * 	unformats a formatted amount
     *
     * \@amountPattern: 0 or others : e.g 12,345,698.50
     *                      1: e.g 12.345.698,50
     *                      2: e.g 12 345 698.50
     *                      3: e.g 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @return {?} string The unformatted amount
     *
     */
    function (formattedAmount, amountPattern) {
        // No problem if precision was lost
        /** @type {?} */
        var unformattedAmount = formattedAmount;
        // If "," is decimal separator
        if (amountPattern == StringUtils.AMOUNT_PATTERN1 || amountPattern == StringUtils.AMOUNT_PATTERN3) {
            unformattedAmount = unformattedAmount.replace(/\./g, "");
            unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        }
        // If "." is decimal separator
        else {
            unformattedAmount = unformattedAmount.replace(/,/g, "");
            unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        }
        return unformattedAmount;
    };
    /**
   * function used to format amout with precision
   * used to unformat format amount but keep the last '.'
   * for the decimal part
   * the block of "," decimal seperator is removed because the "," is replaced by "." in expandMBTAmount()
   *  please see formatAmount() function
   * */
    /**
     * function used to format amout with precision
     * used to unformat format amount but keep the last '.'
     * for the decimal part
     * the block of "," decimal seperator is removed because the "," is replaced by "." in expandMBTAmount()
     *  please see formatAmount() function
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} precision
     * @return {?}
     */
    StringUtils.unformatAmountWithPrecision = /**
     * function used to format amout with precision
     * used to unformat format amount but keep the last '.'
     * for the decimal part
     * the block of "," decimal seperator is removed because the "," is replaced by "." in expandMBTAmount()
     *  please see formatAmount() function
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} precision
     * @return {?}
     */
    function (formattedAmount, amountPattern, precision) {
        // No problem if precision was lost
        /** @type {?} */
        var unformattedAmount = formattedAmount;
        //check if the decimal part is 0
        if (formattedAmount.substr(unformattedAmount.length - 3, unformattedAmount.length) == ".00") {
            unformattedAmount = unformattedAmount.substr(0, unformattedAmount.length - (precision + 1));
        }
        else {
            unformattedAmount = formattedAmount;
        }
        //remove all seprator except last one
        unformattedAmount = unformattedAmount.replace(/\.(?=.*\.)/g, "");
        unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        return unformattedAmount;
    };
    /**
  *	Formats an amount
  *
  *	@param amount The amount string to be formatted
  *   @param dicimals The number of dicimals for a given currency
  *   @formattingOption: 0 or others : 12345698.50 -> 12,345,698.50
  *                      1: 12345698.50 -> 12.345.698,50
  *                      2: 12345698.50 -> 12 345 698.50
  *                      3: 12345698.50 -> 12 345 698,50
  *	@returns string The formatted amount
  *
  * 	@langversion Angular
  *
  */
    /**
     * 	Formats an amount
     *
     * \@formattingOption: 0 or others : 12345698.50 -> 12,345,698.50
     *                      1: 12345698.50 -> 12.345.698,50
     *                      2: 12345698.50 -> 12 345 698.50
     *                      3: 12345698.50 -> 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} amount The amount string to be formatted
     * @param {?} decimals
     * @param {?} amountPattern
     * @return {?} string The formatted amount
     *
     */
    StringUtils.formatAmount = /**
     * 	Formats an amount
     *
     * \@formattingOption: 0 or others : 12345698.50 -> 12,345,698.50
     *                      1: 12345698.50 -> 12.345.698,50
     *                      2: 12345698.50 -> 12 345 698.50
     *                      3: 12345698.50 -> 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} amount The amount string to be formatted
     * @param {?} decimals
     * @param {?} amountPattern
     * @return {?} string The formatted amount
     *
     */
    function (amount, decimals, amountPattern) {
        /** @type {?} */
        var formattedAmount = amount;
        //var cf: CurrencyFormatter = new CurrencyFormatter();
        try {
            formattedAmount = formattedAmount.toUpperCase();
            formattedAmount = StringUtils.expandMBTAmount(formattedAmount, amountPattern);
            // let rounding = "none";
            // let useThousandsSeparator = "true";
            // let useNegativeSign = "true";
            // let precision = decimals;
            // let currencySymbol = "";
            if (amountPattern == StringUtils.AMOUNT_PATTERN1) {
                this.decimalSeparatorTo = ",";
                this.thousandsSeparatorTo = ".";
            }
            else if (amountPattern == StringUtils.AMOUNT_PATTERN2) {
                this.decimalSeparatorTo = ".";
                this.thousandsSeparatorTo = " ";
            }
            else if (amountPattern == StringUtils.AMOUNT_PATTERN3) {
                this.decimalSeparatorTo = ",";
                this.thousandsSeparatorTo = " ";
            }
            else {
                this.decimalSeparatorTo = ".";
                this.thousandsSeparatorTo = ",";
            }
            // Call currency formatter
            //var numberFormattedAmount: number = Number(formattedAmount);
            if (decimals) {
                formattedAmount = StringUtils.format(Number(formattedAmount).toFixed(decimals));
            }
            else {
                formattedAmount = StringUtils.format(Number(formattedAmount));
            }
        }
        catch (error) {
            formattedAmount = amount;
        }
        //cf= null;
        return formattedAmount;
    };
    /**
     * @private
     * @param {?} number
     * @return {?}
     */
    StringUtils.format = /**
     * @private
     * @param {?} number
     * @return {?}
     */
    function (number) {
        /** @type {?} */
        var result;
        result = number.toString().replace(".", this.decimalSeparatorTo);
        result = result.replace(/\B(?=(\d{3})+(?!\d))/g, this.thousandsSeparatorTo);
        return result;
    };
    /**
  *  Expands 0 to an amount regarding to MBT marker
  *	@returns string The expanded amount
  *
  * 	@langversion Angular
  *
  */
    /**
     *  Expands 0 to an amount regarding to MBT marker
     * \@langversion Angular
     *
     * @private
     * @param {?} amount
     * @param {?} amountPattern
     * @return {?} string The expanded amount
     *
     */
    StringUtils.expandMBTAmount = /**
     *  Expands 0 to an amount regarding to MBT marker
     * \@langversion Angular
     *
     * @private
     * @param {?} amount
     * @param {?} amountPattern
     * @return {?} string The expanded amount
     *
     */
    function (amount, amountPattern) {
        /** @type {?} */
        var marker = (amount.charAt(amount.length - 1)).toString().toUpperCase();
        /** @type {?} */
        var amountNumber;
        // If , is decimal separator
        amount = amount.replace(/\,/g, ".");
        // Extract amount without MBT marker
        amountNumber = Number(StringUtils.isNumeric(marker) ? amount : amount.substr(0, amount.length - 1));
        // Multiply with markers
        if (marker == 'M') {
            amountNumber = amountNumber * 1000000;
        }
        else if (marker == 'B') {
            amountNumber = amountNumber * 1000000000;
        }
        else if (marker == 'K' || marker == 'T') {
            amountNumber = amountNumber * 1000;
        }
        return amountNumber.toString();
    };
    /**
          *	Determines whether the specified string is numeric.
          *
          *	@param p_string The string.
          *
          *	@returns Boolean
          *
          * 	@langversion Angular
          *
          *	@tiptext
          */
    /**
     * 	Determines whether the specified string is numeric.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string.
     *
     * @return {?} Boolean
     *
     */
    StringUtils.isNumeric = /**
     * 	Determines whether the specified string is numeric.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string.
     *
     * @return {?} Boolean
     *
     */
    function (p_string) {
        if (p_string == null) {
            return false;
        }
        /** @type {?} */
        var regx = /^[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?$/;
        return regx.test(p_string);
    };
    /**
  *	Determines whether the specified string contains any characters.
  *
  *	@param p_string The string to check
  *
  *	@returns Boolean
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    /**
     * 	Determines whether the specified string contains any characters.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string to check
     *
     * @return {?} Boolean
     *
     */
    StringUtils.isEmpty = /**
     * 	Determines whether the specified string contains any characters.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string to check
     *
     * @return {?} Boolean
     *
     */
    function (p_string) {
        if (p_string == null) {
            return true;
        }
        return !p_string.length;
    };
    /**
     * @param {?} value
     * @return {?}
     */
    StringUtils.isTrue = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (typeof (value) === 'string') {
            if (value === 'true') {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            return value;
        }
    };
    /**
  * Pads p_string with specified character to a specified length from the left.
  *
  *	@param p_string string to pad
  *
  *	@param p_padChar Character for pad.
  *
  *	@param p_length Length to pad to.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    /**
     * Pads p_string with specified character to a specified length from the left.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    StringUtils.padLeft = /**
     * Pads p_string with specified character to a specified length from the left.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    function (p_string, p_padChar, p_length) {
        /** @type {?} */
        var s = p_string;
        while (s.length < p_length) {
            s = p_padChar + s;
        }
        return s;
    };
    /**
  * Pads p_string with specified character to a specified length from the right.
  *
  *	@param p_string string to pad
  *
  *	@param p_padChar Character for pad.
  *
  *	@param p_length Length to pad to.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    /**
     * Pads p_string with specified character to a specified length from the right.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    StringUtils.padRight = /**
     * Pads p_string with specified character to a specified length from the right.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    function (p_string, p_padChar, p_length) {
        /** @type {?} */
        var s = p_string;
        while (s.length < p_length) {
            s += p_padChar;
        }
        return s;
    };
    /**
  *	Removes extraneous whitespace (extra spaces, tabs, line breaks, etc) from the
  *	specified string.
  *
  *	@param p_string The string whose extraneous whitespace will be removed.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    /**
     * 	Removes extraneous whitespace (extra spaces, tabs, line breaks, etc) from the
     * 	specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose extraneous whitespace will be removed.
     *
     * @return {?} string
     *
     */
    StringUtils.removeExtraWhitespace = /**
     * 	Removes extraneous whitespace (extra spaces, tabs, line breaks, etc) from the
     * 	specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose extraneous whitespace will be removed.
     *
     * @return {?} string
     *
     */
    function (p_string) {
        if (p_string == null) {
            return '';
        }
        /** @type {?} */
        var str = StringUtils.trim(p_string);
        return str.replace(/\s+/g, ' ');
    };
    /**
  *	Removes whitespace from the front and the end of the specified
  *	string.
  *
  *	@param p_string The string whose beginning and ending whitespace will
  *	will be removed.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    /**
     * 	Removes whitespace from the front and the end of the specified
     * 	string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning and ending whitespace will
     * 	will be removed.
     *
     * @return {?} string
     *
     */
    StringUtils.trim = /**
     * 	Removes whitespace from the front and the end of the specified
     * 	string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning and ending whitespace will
     * 	will be removed.
     *
     * @return {?} string
     *
     */
    function (p_string) {
        if (p_string == null) {
            return '';
        }
        return p_string.replace(/^\s+|\s+$/g, '');
    };
    /**
    *	Removes whitespace from the front (left-side) of the specified string.
    *
    *	@param p_string The string whose beginning whitespace will be removed.
    *
    *	@returns string
    *
    * 	@langversion Angular
    *
    *	@tiptext
    */
    /**
     * 	Removes whitespace from the front (left-side) of the specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning whitespace will be removed.
     *
     * @return {?} string
     *
     */
    StringUtils.trimLeft = /**
     * 	Removes whitespace from the front (left-side) of the specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning whitespace will be removed.
     *
     * @return {?} string
     *
     */
    function (p_string) {
        if (p_string == null) {
            return '';
        }
        return p_string.replace(/^\s+/, '');
    };
    /**
    *	Removes whitespace from the end (right-side) of the specified string.
    *
    *	@param p_string The string whose ending whitespace will be removed.
    *
    *	@returns string	.
    *
    * 	@langversion ActionScript 3.0
    *	@playerversion Flash 9.0
    *	@tiptext
    */
    /**
     * 	Removes whitespace from the end (right-side) of the specified string.
     *
     * \@langversion ActionScript 3.0
     * \@playerversion Flash 9.0
     * \@tiptext
     * @param {?} p_string The string whose ending whitespace will be removed.
     *
     * @param {?=} trimChar
     * @return {?} string	.
     *
     */
    StringUtils.trimRight = /**
     * 	Removes whitespace from the end (right-side) of the specified string.
     *
     * \@langversion ActionScript 3.0
     * \@playerversion Flash 9.0
     * \@tiptext
     * @param {?} p_string The string whose ending whitespace will be removed.
     *
     * @param {?=} trimChar
     * @return {?} string	.
     *
     */
    function (p_string, trimChar) {
        if (trimChar === void 0) { trimChar = '\\s'; }
        if (p_string == null) {
            return '';
        }
        /** @type {?} */
        var regex = new RegExp(StringUtils.fixRegex(trimChar) + '+$');
        return p_string.replace(regex, '');
    };
    /**
     * This part of code is used to ignore reserved chars inside a regex
     * @param regex
     * @return
     */
    /**
     * This part of code is used to ignore reserved chars inside a regex
     * @param {?} regex
     * @return {?}
     */
    StringUtils.fixRegex = /**
     * This part of code is used to ignore reserved chars inside a regex
     * @param {?} regex
     * @return {?}
     */
    function (regex) {
        /** @type {?} */
        var rtn = regex;
        if (regex != null) {
            /** @type {?} */
            var rsv = ['$', '(', ')', '*', '+', '-', '.', '?', '[', ']', '^', '{', '|', '}'];
            rsv.forEach((/**
             * @param {?} c
             * @return {?}
             */
            function (c) {
                rtn = rtn.replace(c, "\\" + c);
            }));
        }
        return rtn;
    };
    /**
   * decodeXmlChars
   *
   * @param text:string
   * <AUTHOR> SwallowTech Tunisia
   *
   * this method is used to clean a string from unwanted encoded characters
   **/
    /**
     * decodeXmlChars
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @return {?}
     */
    StringUtils.decodeXmlChars = /**
     * decodeXmlChars
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @return {?}
     */
    function (text) {
        /** @type {?} */
        var result = StringUtils.replaceAll(text, {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&apos;': "'",
            '&quot;': '"'
        });
        return result;
    };
    /**
     * replaceAll
     *
     * @param source: string
     * @param map: Object
     * <AUTHOR> SwallowTech Tunisia
     *
     * Replace all occurences of the map values in a given string
     **/
    /**
     * replaceAll
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * Replace all occurences of the map values in a given string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    StringUtils.replaceAll = /**
     * replaceAll
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * Replace all occurences of the map values in a given string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    function (source, map) {
        for (var replaceToken in map) {
            source = source.replace(new RegExp(replaceToken, "g"), map[replaceToken]);
        }
        return source;
    };
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     * */
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    StringUtils.encode64 = /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    function (text) {
        // btoa() is used to convert string to base64
        /** @type {?} */
        var result = btoa(text);
        result = StringUtils.replaceAll(result, {
            '=': '(',
            '\\+': ')'
        });
        return result;
    };
    /**
  * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
  * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
  * getMenuWindow().decode64(text);
  **/
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    StringUtils.decode64 = /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    function (text) {
        /** @type {?} */
        var result = StringUtils.replaceAll(text, {
            '\\(': '=',
            '\\)': '+'
        });
        result = atob(result);
        return result;
    };
    /**
  * codeXmlChars
  *
  * this method is used to clean a string from unwanted encoded characters
  **/
    /**
     * codeXmlChars
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @param {?=} keepHtml
     * @return {?}
     */
    StringUtils.codeXmlChars = /**
     * codeXmlChars
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @param {?=} keepHtml
     * @return {?}
     */
    function (text, keepHtml) {
        if (keepHtml === void 0) { keepHtml = false; }
        /** @type {?} */
        var result = null;
        if (!text) {
            return '';
        }
        if (!keepHtml) {
            result = StringUtils.replaceAll(text, { '<': '&lt;', '>': '&gt;', '\'': '&apos;', '"': '&quot;' });
        }
        else {
            // Find Html tags in the provided text
            /** @type {?} */
            var htmlTags /*of string*/ = text.match(StringUtils.htmlTagsRegex);
            // Apply classic XML replacements
            result = StringUtils.codeXmlChars(text, false);
            // Revert back the html tags
            for (var i = 0; i < htmlTags.length; i++) {
                result = result.replace(StringUtils.codeXmlChars(htmlTags[i], false), htmlTags[i]);
            }
        }
        return result;
    };
    //TODO
    // =========================================================<TODO>==============================================================
    /**
  * Removes an XML node from its parent root
  * */
    /* public static deleteXMLNode(xmlToDelete):boolean
    {
        var cn:XMLList = XMLList(xmlToDelete.parent()).children();
        for ( var i:Number = 0 ; i < cn.length() ; i++ )
        {
          if ( cn[i] == xmlToDelete )
          {
           delete cn[i];
          return true;
          }
        }
        return false;
    } */
    /**
  * Basic convertion of XMLList into an Array
  * the resulting array will contain either value or child XML for each node
  * */
    /* 		public static function xmlListToArray(xml:XMLList):Array {
          var array:Array=[], i:string;
          for (i in xml)
            array[i] = xml[i];
          
          return array;
        } */
    /**
     * Converts an XML into an Objects array collection
     * here datafield is your xml tag
     * */
    /* 	public static function convertXmlToArrayCollection( file:string, datafield:string ):ArrayCollection
        {
            var xml:XMLDocument = new XMLDocument( file );
            
            var decoder:SimpleXMLDecoder = new SimpleXMLDecoder();
            var data:Object = decoder.decodeXML( xml );
            var array:Array = ArrayUtil.toArray( data[datafield] );
            return new ArrayCollection( array );
    } */
    // =======================================================</TODO>=============================================================================
    /*
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    //TODO
    // =========================================================<TODO>==============================================================
    /**
    * Removes an XML node from its parent root
    * */
    /* public static deleteXMLNode(xmlToDelete):boolean
      {
          var cn:XMLList = XMLList(xmlToDelete.parent()).children();
          for ( var i:Number = 0 ; i < cn.length() ; i++ )
          {
            if ( cn[i] == xmlToDelete )
            {
             delete cn[i];
            return true;
            }
          }
          return false;
      } */
    /**
    * Basic convertion of XMLList into an Array
    * the resulting array will contain either value or child XML for each node
    * */
    /* 		public static function xmlListToArray(xml:XMLList):Array {
            var array:Array=[], i:string;
            for (i in xml)
              array[i] = xml[i];
            
            return array;
          } */
    /**
     * Converts an XML into an Objects array collection
     * here datafield is your xml tag
     *
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    /* 	public static function convertXmlToArrayCollection( file:string, datafield:string ):ArrayCollection
            {
                var xml:XMLDocument = new XMLDocument( file );
                
                var decoder:SimpleXMLDecoder = new SimpleXMLDecoder();
                var data:Object = decoder.decodeXML( xml );
                var array:Array = ArrayUtil.toArray( data[datafield] );
                return new ArrayCollection( array );
        } */
    // =======================================================</TODO>=============================================================================
    /*
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    StringUtils.formatDate = 
    //TODO
    // =========================================================<TODO>==============================================================
    /**
    * Removes an XML node from its parent root
    * */
    /* public static deleteXMLNode(xmlToDelete):boolean
      {
          var cn:XMLList = XMLList(xmlToDelete.parent()).children();
          for ( var i:Number = 0 ; i < cn.length() ; i++ )
          {
            if ( cn[i] == xmlToDelete )
            {
             delete cn[i];
            return true;
            }
          }
          return false;
      } */
    /**
    * Basic convertion of XMLList into an Array
    * the resulting array will contain either value or child XML for each node
    * */
    /* 		public static function xmlListToArray(xml:XMLList):Array {
            var array:Array=[], i:string;
            for (i in xml)
              array[i] = xml[i];
            
            return array;
          } */
    /**
     * Converts an XML into an Objects array collection
     * here datafield is your xml tag
     *
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    /* 	public static function convertXmlToArrayCollection( file:string, datafield:string ):ArrayCollection
            {
                var xml:XMLDocument = new XMLDocument( file );
                
                var decoder:SimpleXMLDecoder = new SimpleXMLDecoder();
                var data:Object = decoder.decodeXML( xml );
                var array:Array = ArrayUtil.toArray( data[datafield] );
                return new ArrayCollection( array );
        } */
    // =======================================================</TODO>=============================================================================
    /*
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    function (date, patern) {
        /** @type {?} */
        var regex = new RegExp('[^a-z0-9A-Z]');
        /** @type {?} */
        var d = new Date(date);
        /** @type {?} */
        var month = '' + (d.getMonth() + 1);
        /** @type {?} */
        var day = '' + d.getDate();
        /** @type {?} */
        var year = d.getFullYear();
        /** @type {?} */
        var separator = patern[patern.search(regex)];
        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;
        if (patern == 'YYYY' + separator + 'MM' + separator + 'DD') {
            return [year, month, day].join(separator);
        }
        else if (patern == 'DD' + separator + 'MM' + separator + 'YYYY' || patern == 'DDMMYYYY') {
            return [day, month, year].join(separator);
        }
        else if (patern == 'MM' + separator + 'DD' + separator + 'YYYY') {
            return [month, day, year].join(separator);
        }
        else if (patern == 'DD' + separator + 'MMM' + separator + 'YYYY') {
            /** @type {?} */
            var month_names_short = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            month = month_names_short[Number(month) - 1];
            return [day, month, year].join('-');
        }
        else
            return '';
    };
    // ======================================== TODO =========================================================================================================
    /**
     * Converts all url parameters into string
     */
    /* public static encodeUrlParams(params):string {
      var paramsAsObj:Object = new Object();
      var classInfo:Object = ObjectUtil.getClassInfo(params);
      var properties:Array = classInfo.properties;
      var pCount:uint = properties.length;
      var fieldName:string ;
      
      // Convert array to object
      for (var p:uint = 0; p < pCount; p++)
      {
        fieldName = properties[p].localName;
        paramsAsObj[fieldName] = params[fieldName];
      }
      
      //Convert object to XML string
      var paramsAxXMLStr:string = StringUtils.convertObjectToXML(paramsAsObj, ["*"], "object", false).toString();
      
      // Return base64 encoded parameters
      return StringUtils.encode64(paramsAxXMLStr);
    } */
    /**
  * Converts an object to XML without using SimpleXMLEncoder class
  *
  * <AUTHOR> SwallowTech Tunisia
  * */
    /* 		public static function cdata(data:string):XML {
          return new XML("<![CDATA[" + data + "]]>");
        }
          
        public static function convertObjectToXML(obj:Object, acceptAttributes:Array, tag:string="object", addCdata:Boolean=false):XML {
          try
          {
            var xml:XML = <{tag}/>;
            var classInfo:Object = ObjectUtil.getClassInfo(obj);
            var properties:Array = classInfo.properties;
              
            var pCount:uint = properties.length;
            var fieldName:string ;
            var xmlNode:XML;
            var text:string;
            var acceptAll:Boolean = false;
              
            if(!acceptAttributes)
              acceptAttributes = new Array();
            else if(acceptAttributes.length==1 && acceptAttributes[0]=='*')
              acceptAll = true;
              
            var acceptAttributesColl:ArrayCollection = new ArrayCollection(acceptAttributes);
            var createdTagsColl:ArrayCollection = new ArrayCollection([]);
            // Loop on all elements of the object
            for (var p:uint = 0; p < pCount; p++)
            {
              fieldName = properties[p].localName;
              // If atribute is to ignore, then do not include in the resulting XML
              if(acceptAll||acceptAttributesColl.contains(fieldName)){
                text = obj[fieldName].toString();
                if(addCdata)
                {
                  xmlNode = <{fieldName}>{cdata(text)}</{fieldName}>
                }
                else
                {
                  xmlNode = <{fieldName}>{text}</{fieldName}>
                }
      
                xml.appendChild(xmlNode);
                  
                // Push the fieldName into createdTagsColl, this will allow adding empty tags for remaining elements later
                if(!acceptAll)
                  createdTagsColl.addItem(fieldName);
              }
            }
              
          
              
            // Some entries exist on acceptAttributesColl but not on obj properties, So we should add empty tags
            for each (var element:string in acceptAttributes){
              if(!createdTagsColl.contains(element) && element != "*"){
                text = "";
                xmlNode = <{element}>{text}</{element}>
                xml.appendChild(xmlNode);
              }
            }
      
            return xml;
          }
          catch(error:Error)
          {
            var err:string = "Error: " + error.message+", Object: "+obj;
            var xml:XML = <{tag}>{err}</{tag}>;
            return xml;
          }
          return null;
        } */
    /**
  * Converts all url parameters string into an array
  */
    /* public static function decodeUrlParams(encodedParamsStr:string):Array {
      var paramsStr:string = StringUtils.decode64(encodedParamsStr);
      var paramsXml:XML = new XML(paramsStr);
      var paramsArr:Array = new Array();
      for each(var tag:XML in paramsXml.*){
        paramsArr[tag.name()] = tag.text();
      }
      return paramsArr;
    } */
    // ===========================================================================================================================================================    
    /**
     * Get url params as indexed array
     * */
    // ======================================== TODO =========================================================================================================
    /**
       * Converts all url parameters into string
       */
    /* public static encodeUrlParams(params):string {
        var paramsAsObj:Object = new Object();
        var classInfo:Object = ObjectUtil.getClassInfo(params);
        var properties:Array = classInfo.properties;
        var pCount:uint = properties.length;
        var fieldName:string ;
        
        // Convert array to object
        for (var p:uint = 0; p < pCount; p++)
        {
          fieldName = properties[p].localName;
          paramsAsObj[fieldName] = params[fieldName];
        }
        
        //Convert object to XML string
        var paramsAxXMLStr:string = StringUtils.convertObjectToXML(paramsAsObj, ["*"], "object", false).toString();
        
        // Return base64 encoded parameters
        return StringUtils.encode64(paramsAxXMLStr);
      } */
    /**
    * Converts an object to XML without using SimpleXMLEncoder class
    *
    * <AUTHOR> SwallowTech Tunisia
    * */
    /* 		public static function cdata(data:string):XML {
            return new XML("<![CDATA[" + data + "]]>");
          }
            
          public static function convertObjectToXML(obj:Object, acceptAttributes:Array, tag:string="object", addCdata:Boolean=false):XML {
            try
            {
              var xml:XML = <{tag}/>;
              var classInfo:Object = ObjectUtil.getClassInfo(obj);
              var properties:Array = classInfo.properties;
                
              var pCount:uint = properties.length;
              var fieldName:string ;
              var xmlNode:XML;
              var text:string;
              var acceptAll:Boolean = false;
                
              if(!acceptAttributes)
                acceptAttributes = new Array();
              else if(acceptAttributes.length==1 && acceptAttributes[0]=='*')
                acceptAll = true;
                
              var acceptAttributesColl:ArrayCollection = new ArrayCollection(acceptAttributes);
              var createdTagsColl:ArrayCollection = new ArrayCollection([]);
              // Loop on all elements of the object
              for (var p:uint = 0; p < pCount; p++)
              {
                fieldName = properties[p].localName;
                // If atribute is to ignore, then do not include in the resulting XML
                if(acceptAll||acceptAttributesColl.contains(fieldName)){
                  text = obj[fieldName].toString();
                  if(addCdata)
                  {
                    xmlNode = <{fieldName}>{cdata(text)}</{fieldName}>
                  }
                  else
                  {
                    xmlNode = <{fieldName}>{text}</{fieldName}>
                  }
        
                  xml.appendChild(xmlNode);
                    
                  // Push the fieldName into createdTagsColl, this will allow adding empty tags for remaining elements later
                  if(!acceptAll)
                    createdTagsColl.addItem(fieldName);
                }
              }
                
            
                
              // Some entries exist on acceptAttributesColl but not on obj properties, So we should add empty tags
              for each (var element:string in acceptAttributes){
                if(!createdTagsColl.contains(element) && element != "*"){
                  text = "";
                  xmlNode = <{element}>{text}</{element}>
                  xml.appendChild(xmlNode);
                }
              }
        
              return xml;
            }
            catch(error:Error)
            {
              var err:string = "Error: " + error.message+", Object: "+obj;
              var xml:XML = <{tag}>{err}</{tag}>;
              return xml;
            }
            return null;
          } */
    /**
    * Converts all url parameters string into an array
    */
    /* public static function decodeUrlParams(encodedParamsStr:string):Array {
        var paramsStr:string = StringUtils.decode64(encodedParamsStr);
        var paramsXml:XML = new XML(paramsStr);
        var paramsArr:Array = new Array();
        for each(var tag:XML in paramsXml.*){
          paramsArr[tag.name()] = tag.text();
        }
        return paramsArr;
      } */
    // ===========================================================================================================================================================    
    /**
     * Get url params as indexed array
     *
     * @param {?} url
     * @return {?}
     */
    StringUtils.getUrlParams = 
    // ======================================== TODO =========================================================================================================
    /**
       * Converts all url parameters into string
       */
    /* public static encodeUrlParams(params):string {
        var paramsAsObj:Object = new Object();
        var classInfo:Object = ObjectUtil.getClassInfo(params);
        var properties:Array = classInfo.properties;
        var pCount:uint = properties.length;
        var fieldName:string ;
        
        // Convert array to object
        for (var p:uint = 0; p < pCount; p++)
        {
          fieldName = properties[p].localName;
          paramsAsObj[fieldName] = params[fieldName];
        }
        
        //Convert object to XML string
        var paramsAxXMLStr:string = StringUtils.convertObjectToXML(paramsAsObj, ["*"], "object", false).toString();
        
        // Return base64 encoded parameters
        return StringUtils.encode64(paramsAxXMLStr);
      } */
    /**
    * Converts an object to XML without using SimpleXMLEncoder class
    *
    * <AUTHOR> SwallowTech Tunisia
    * */
    /* 		public static function cdata(data:string):XML {
            return new XML("<![CDATA[" + data + "]]>");
          }
            
          public static function convertObjectToXML(obj:Object, acceptAttributes:Array, tag:string="object", addCdata:Boolean=false):XML {
            try
            {
              var xml:XML = <{tag}/>;
              var classInfo:Object = ObjectUtil.getClassInfo(obj);
              var properties:Array = classInfo.properties;
                
              var pCount:uint = properties.length;
              var fieldName:string ;
              var xmlNode:XML;
              var text:string;
              var acceptAll:Boolean = false;
                
              if(!acceptAttributes)
                acceptAttributes = new Array();
              else if(acceptAttributes.length==1 && acceptAttributes[0]=='*')
                acceptAll = true;
                
              var acceptAttributesColl:ArrayCollection = new ArrayCollection(acceptAttributes);
              var createdTagsColl:ArrayCollection = new ArrayCollection([]);
              // Loop on all elements of the object
              for (var p:uint = 0; p < pCount; p++)
              {
                fieldName = properties[p].localName;
                // If atribute is to ignore, then do not include in the resulting XML
                if(acceptAll||acceptAttributesColl.contains(fieldName)){
                  text = obj[fieldName].toString();
                  if(addCdata)
                  {
                    xmlNode = <{fieldName}>{cdata(text)}</{fieldName}>
                  }
                  else
                  {
                    xmlNode = <{fieldName}>{text}</{fieldName}>
                  }
        
                  xml.appendChild(xmlNode);
                    
                  // Push the fieldName into createdTagsColl, this will allow adding empty tags for remaining elements later
                  if(!acceptAll)
                    createdTagsColl.addItem(fieldName);
                }
              }
                
            
                
              // Some entries exist on acceptAttributesColl but not on obj properties, So we should add empty tags
              for each (var element:string in acceptAttributes){
                if(!createdTagsColl.contains(element) && element != "*"){
                  text = "";
                  xmlNode = <{element}>{text}</{element}>
                  xml.appendChild(xmlNode);
                }
              }
        
              return xml;
            }
            catch(error:Error)
            {
              var err:string = "Error: " + error.message+", Object: "+obj;
              var xml:XML = <{tag}>{err}</{tag}>;
              return xml;
            }
            return null;
          } */
    /**
    * Converts all url parameters string into an array
    */
    /* public static function decodeUrlParams(encodedParamsStr:string):Array {
        var paramsStr:string = StringUtils.decode64(encodedParamsStr);
        var paramsXml:XML = new XML(paramsStr);
        var paramsArr:Array = new Array();
        for each(var tag:XML in paramsXml.*){
          paramsArr[tag.name()] = tag.text();
        }
        return paramsArr;
      } */
    // ===========================================================================================================================================================    
    /**
     * Get url params as indexed array
     *
     * @param {?} url
     * @return {?}
     */
    function (url) {
        /** @type {?} */
        var qm = url.lastIndexOf("?");
        /** @type {?} */
        var result = [];
        if (qm != -1) {
            /** @type {?} */
            var query = url.substr(qm + 1);
            /** @type {?} */
            var params = query.split("&");
            //Get the parameter key and value  
            for (var i = 0; i < params.length; i++) {
                /** @type {?} */
                var param = params[i];
                /** @type {?} */
                var nameValue = param.split("=");
                if (nameValue.length == 2) {
                    // set key 
                    /** @type {?} */
                    var key = nameValue[0];
                    // set value 
                    /** @type {?} */
                    var val = nameValue[1];
                    // Add to result
                    result[key] = val;
                }
            }
        }
        return result;
    };
    /**
           * Clones an object and returns the cloned instance
           * */
    /**
     * Clones an object and returns the cloned instance
     *
     * @param {?} obj
     * @return {?}
     */
    StringUtils.cloneObject = /**
     * Clones an object and returns the cloned instance
     *
     * @param {?} obj
     * @return {?}
     */
    function (obj) {
        /** @type {?} */
        var copy = Object.assign({}, obj);
        return copy;
    };
    /**
           * counts number of occurences of pattern inside a given string
           * */
    /**
     * counts number of occurences of pattern inside a given string
     *
     * @param {?} pattern
     * @param {?} target
     * @return {?}
     */
    StringUtils.countOccurences = /**
     * counts number of occurences of pattern inside a given string
     *
     * @param {?} pattern
     * @param {?} target
     * @return {?}
     */
    function (pattern, target) {
        // to hold number of occurences of pattern inside a given string 
        /** @type {?} */
        var count = 0;
        /** @type {?} */
        var index = -1;
        // loop through str
        while ((index = target.indexOf(pattern, index + 1)) >= 0) {
            count++;
        }
        return count;
    };
    /**
       *  Substitutes "{n}" tokens within the specified string
       *  with the respective arguments passed in.
       *
       *  @param stringParam The string to make substitutions in.
       *  This string can contain special tokens of the form
       *  <code>{n}</code>, where <code>n</code> is a zero based index,
       *  that will be replaced with the additional parameters
       *  found at that index if specified.
       *
       *  @param words Additional parameters that can be substituted
       *  in the <code>stringParam</code> parameter at each <code>{n}</code>
       *  location, where <code>n</code> is an integer (zero based)
       *  index value into the array of values specified.
       *  If the first parameter is an array this array will be used as
       *  a parameter list.
       *  This allows reuse of this routine in other methods that want to
       *  use the ... words signature.
       *  For example <pre>
       *     public function myTracer(stringParam:String, ... words):void
       *     {
       *         label.text += StringUtil.substitute(stringParam, words) + "\n";
       *     } </pre>
       *
       *  @return New string with all of the <code>{n}</code> tokens
       *  replaced with the respective arguments specified.
       *
       *  @example
       *
       *  var stringParam:String = "here is some info '{0}' and {1}";
       *  trace(StringUtil.substitute(stringParam, 15.4, true));
       *
       *  // this will output the following string:
       *  // "here is some info '15.4' and true"
       */
    /**
     *  Substitutes "{n}" tokens within the specified string
     *  with the respective arguments passed in.
     *
     * \@example
     *
     *  var stringParam:String = "here is some info '{0}' and {1}";
     *  trace(StringUtil.substitute(stringParam, 15.4, true));
     *
     *  // this will output the following string:
     *  // "here is some info '15.4' and true"
     * @param {?} stringParam The string to make substitutions in.
     *  This string can contain special tokens of the form
     *  <code>{n}</code>, where <code>n</code> is a zero based index,
     *  that will be replaced with the additional parameters
     *  found at that index if specified.
     *
     * @param {...?} words Additional parameters that can be substituted
     *  in the <code>stringParam</code> parameter at each <code>{n}</code>
     *  location, where <code>n</code> is an integer (zero based)
     *  index value into the array of values specified.
     *  If the first parameter is an array this array will be used as
     *  a parameter list.
     *  This allows reuse of this routine in other methods that want to
     *  use the ... words signature.
     *  For example <pre>
     *     public function myTracer(stringParam:String, ... words):void
     *     {
     *         label.text += StringUtil.substitute(stringParam, words) + "\n";
     *     } </pre>
     *
     * @return {?} New string with all of the <code>{n}</code> tokens
     *  replaced with the respective arguments specified.
     *
     */
    StringUtils.substitute = /**
     *  Substitutes "{n}" tokens within the specified string
     *  with the respective arguments passed in.
     *
     * \@example
     *
     *  var stringParam:String = "here is some info '{0}' and {1}";
     *  trace(StringUtil.substitute(stringParam, 15.4, true));
     *
     *  // this will output the following string:
     *  // "here is some info '15.4' and true"
     * @param {?} stringParam The string to make substitutions in.
     *  This string can contain special tokens of the form
     *  <code>{n}</code>, where <code>n</code> is a zero based index,
     *  that will be replaced with the additional parameters
     *  found at that index if specified.
     *
     * @param {...?} words Additional parameters that can be substituted
     *  in the <code>stringParam</code> parameter at each <code>{n}</code>
     *  location, where <code>n</code> is an integer (zero based)
     *  index value into the array of values specified.
     *  If the first parameter is an array this array will be used as
     *  a parameter list.
     *  This allows reuse of this routine in other methods that want to
     *  use the ... words signature.
     *  For example <pre>
     *     public function myTracer(stringParam:String, ... words):void
     *     {
     *         label.text += StringUtil.substitute(stringParam, words) + "\n";
     *     } </pre>
     *
     * @return {?} New string with all of the <code>{n}</code> tokens
     *  replaced with the respective arguments specified.
     *
     */
    function (stringParam) {
        var words = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            words[_i - 1] = arguments[_i];
        }
        try {
            /** @type {?} */
            var result = stringParam.toString();
            for (var index = 0; index < words.length; index++) {
                result = result.replace("{" + index.toString() + "}", words[index]);
            }
            return result;
        }
        catch (error) {
            console.log(error);
        }
    };
    /**
      *  Returns <code>true</code> if the specified string is
      *  a single space, tab, carriage return, newline, or formfeed character.
      *
      *  @param str The String that is is being queried.
      *
      *  @return <code>true</code> if the specified string is
      *  a single space, tab, carriage return, newline, or formfeed character.
      */
    /**
     *  Returns <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     *
     * @param {?} character
     * @return {?} <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     */
    StringUtils.isWhitespace = /**
     *  Returns <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     *
     * @param {?} character
     * @return {?} <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     */
    function (character) {
        try {
            switch (character) {
                case " ":
                case "\t":
                case "\r":
                case "\n":
                case "\f":
                    return true;
                default:
                    return false;
            }
        }
        catch (error) {
            console.log(error);
        }
    };
    /**
     * Converts an associative array of parameters into an XML string,
     * The goal is to use Jaxb marshalling on java side.
     *
     * <AUTHOR> JABALLAH, SwallowTech Tunisia
     * */
    /**
     * Converts an associative array of parameters into an XML string,
     * The goal is to use Jaxb marshalling on java side.
     *
     * <AUTHOR> JABALLAH, SwallowTech Tunisia
     *
     * @param {?} params
     * @param {?} tableName
     * @param {?} operation
     * @param {?} tableLevel
     * @return {?}
     */
    StringUtils.getKVTypeTabAsXML = /**
     * Converts an associative array of parameters into an XML string,
     * The goal is to use Jaxb marshalling on java side.
     *
     * <AUTHOR> JABALLAH, SwallowTech Tunisia
     *
     * @param {?} params
     * @param {?} tableName
     * @param {?} operation
     * @param {?} tableLevel
     * @return {?}
     */
    function (params, tableName, operation, tableLevel) {
        /** @type {?} */
        var tab = (tableName != '') ? "tableName='" + tableName + "'" : "tableName=''";
        /** @type {?} */
        var oper = (operation != '') ? "operation='" + operation + "'" : "operation=''";
        /** @type {?} */
        var lev = (tableLevel != '') ? "tableLevel='" + tableLevel + "'" : "tableLevel=''";
        /** @type {?} */
        var xml = new XML('<tabKVType ' + tab + ' ' + oper + ' ' + lev + '/>');
        /** @type {?} */
        var kvTypeNode;
        if (params instanceof Array) {
            //For each key of the array params
            for (var keyname in params) {
                /** @type {?} */
                var value = params[keyname] == undefined ? '' : params[keyname];
                kvTypeNode = new XML('<kvType/>');
                // Append nodes
                kvTypeNode.appendChild('<key>' + keyname + '</key>');
                kvTypeNode.appendChild('<value>' + value + '</value>');
                xml.appendChild(kvTypeNode);
            }
        }
        else if (params instanceof HashMap) {
            for (var key in ((/** @type {?} */ (params))).getKeys()) {
                kvTypeNode = new XML('<kvType/>');
                kvTypeNode.appendChild('<key>' + key + '</key>');
                kvTypeNode.appendChild('<value>' + ((/** @type {?} */ (params))).getValue(key) + '</value>');
                xml.appendChild(kvTypeNode);
            }
        }
        return xml;
    };
    /**
     * Removes an XML node from its parent root
     * @param parentXml : XML
     * @param xmlToDelete :XML
     */
    /**
     * Removes an XML node from its parent root
     * @param {?} parentXml : XML
     * @param {?} xmlToDelete :XML
     * @return {?}
     */
    StringUtils.deleteXMLNode = /**
     * Removes an XML node from its parent root
     * @param {?} parentXml : XML
     * @param {?} xmlToDelete :XML
     * @return {?}
     */
    function (parentXml, xmlToDelete) {
        if (parentXml.removeChild(xmlToDelete)) {
            return true;
        }
        return false;
    };
    StringUtils.AMOUNT_PATTERN0 = 0;
    StringUtils.AMOUNT_PATTERN1 = 1;
    StringUtils.AMOUNT_PATTERN2 = 2;
    StringUtils.AMOUNT_PATTERN3 = 3;
    StringUtils.htmlTagsRegex = new RegExp("(<style[^>]*/>|<style[^>]*>|</style>|<font[^>]*>|</font>|<br>|<br/>|<a[^>]*>|</a>|<b>|</b>|<li>|</li>|<i>|</i>|<u>|</u>|<p[^>]*>|</p>|<span[^>]*>|</span>)", "ig");
    StringUtils.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    StringUtils.ctorParameters = function () { return []; };
    return StringUtils;
}());
export { StringUtils };
if (false) {
    /**
     * @type {?}
     * @private
     */
    StringUtils.decimalSeparatorTo;
    /**
     * @type {?}
     * @private
     */
    StringUtils.thousandsSeparatorTo;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN0;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN1;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN2;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN3;
    /**
     * @type {?}
     * @private
     */
    StringUtils.htmlTagsRegex;
}
//# sourceMappingURL=data:application/json;base64,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