/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { HttpHeaders, HttpParams } from '@angular/common/http';
import { RemoteTransaction } from './RemoteTransaction';
import { SwtAlert } from '../utils/swt-alert.service';
import { Alert } from '../utils/alert.component';
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { Logger } from "../logging/logger.service";
import { CommonService } from "../utils/common.service";
import { saveAs } from 'file-saver/FileSaver';
import { Observable } from 'rxjs';
var HTTPComms = /** @class */ (function () {
    //////////////////////////////////////////////////////////////////////////////////////////
    function HTTPComms(common) {
        this.common = common;
        // Callback functions
        this.cbFault = new Function();
        this.cbResult = new Function();
        this.cbStart = new Function();
        this.cbStop = new Function();
        this.headers = new HttpHeaders();
        this.stopTimers = new Function();
        this.busy = false;
        this.encode = true;
        this._start = 0;
        this._result = 0;
        this._localStart = 0;
        this.alertFlag = false;
        this._cancelRequest = false;
        this.screenUniqueTransactionId_ = null;
        this._responsetype = '?response=json';
        this.jsonReader = new JSONReader();
        this._profling = false;
        //////////////////////////////////////////////////////////////////////////////////////////
        //                               HTTPCOMMS CONSTRUCTOR                                  //
        this._serverDelay = 0;
        this._localDelay = 0;
        this.encodeURL = false;
        this._method = 'POST';
        this.logger = new Logger('HTTPComms', this.common.httpclient);
        this.swtAlert = new SwtAlert(common);
    }
    Object.defineProperty(HTTPComms.prototype, "profling", {
        /**
         * Get profiling condition
         * */
        get: /**
         * Get profiling condition
         *
         * @return {?}
         */
        function () {
            return this._profling;
        },
        /**
         * Set profiling condition
         * @param value
         * */
        set: /**
         * Set profiling condition
         * @param {?} value
         *
         * @return {?}
         */
        function (value) {
            this._profling = value;
            this._start = this.getTimer();
            this._localDelay = 0;
            this._serverDelay = 0;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HTTPComms.prototype, "serverDelay", {
        /**
         * Get delay of last HTTP request/response
         * */
        get: /**
         * Get delay of last HTTP request/response
         *
         * @return {?}
         */
        function () {
            return this._serverDelay;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HTTPComms.prototype, "localDelay", {
        get: /**
         * @return {?}
         */
        function () {
            return this._localDelay;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HTTPComms.prototype, "url", {
        get: /**
         * @return {?}
         */
        function () {
            return this._url;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._url = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HTTPComms.prototype, "encodeURL", {
        get: /**
         * @return {?}
         */
        function () {
            return this.encode;
        },
        set: /**
         * @param {?} encode
         * @return {?}
         */
        function (encode) {
            this.encode = encode;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Overrides the main send function so that the busy flag is set
     * The start callback function is called
     * and
     * Then sending the data, encoding it - So be aware that it needs to be decoded at the server end
     */
    /**
     * Overrides the main send function so that the busy flag is set
     * The start callback function is called
     * and
     * Then sending the data, encoding it - So be aware that it needs to be decoded at the server end
     * @param {?=} parameters
     * @param {?=} json
     * @return {?}
     */
    HTTPComms.prototype.send = /**
     * Overrides the main send function so that the busy flag is set
     * The start callback function is called
     * and
     * Then sending the data, encoding it - So be aware that it needs to be decoded at the server end
     * @param {?=} parameters
     * @param {?=} json
     * @return {?}
     */
    function (parameters, json) {
        if (parameters === void 0) { parameters = null; }
        /** @type {?} */
        var errorLocation = 0;
        try {
            // check if parameter is null or undefined and remove it.
            for (var attr in parameters) {
                if (parameters[attr] === undefined || parameters[attr] == null) {
                    delete parameters[attr];
                }
            }
            // check if URL contain parameters.
            // var qm:number= this.url.lastIndexOf("?");
            // if (qm !== -1) {
            //     errorLocation=10;
            //     var query:string= this.url.substr(qm + 1);
            //     var params = query.split("&");
            //     for (var i = 0; i < params.length; i++) {
            //         errorLocation=20;
            //         var param:string=params[i];
            //         var nameValue = param.split("=");
            //         if (nameValue.length === 2) {
            //             errorLocation=30;
            //             var key = nameValue[0];
            //             var val = nameValue[1];
            //             parameters[key] = val;
            //         }
            //     }
            // }
            if (!this.busy) {
                this.busy = true;
                this.onStart();
                // If screen unique transactionId is set, then send it as a parameter to the server
                if (this.screenUniqueTransactionId_ && parameters) {
                    parameters["screenUniqueTransactionId"] = this.screenUniqueTransactionId_;
                }
                // If encode is enabled then encode the parameters, then send
                if (this.encode) {
                    this.sendRequest(this.encodeData(parameters), json);
                }
                else {
                    /** @type {?} */
                    var dNoCache = new Date();
                    if (parameters) {
                        parameters.nocache = dNoCache.getTime().toString();
                    }
                    this.sendRequest(parameters, json);
                }
            }
            else {
                this.cancel();
            }
        }
        catch (error) {
            this.logger.error("[ send ] METHOD ERROR:", error);
        }
    };
    /**
     * @param {?=} id
     * @return {?}
     */
    HTTPComms.prototype.cancel = /**
     * @param {?=} id
     * @return {?}
     */
    function (id) {
        if (id === void 0) { id = null; }
        this.onFinish();
        this._cancelRequest = true;
    };
    /**
     *  Use this function to send arrays to the server
     */
    /**
     *  Use this function to send arrays to the server
     * @param {?} params
     * @param {?} name
     * @param {?} needQ
     * @return {?}
     */
    HTTPComms.prototype.arraySend = /**
     *  Use this function to send arrays to the server
     * @param {?} params
     * @param {?} name
     * @param {?} needQ
     * @return {?}
     */
    function (params, name, needQ) {
        this.originalURL = this.url;
        this.url += needQ ? '?' : '';
        this.url += this.arrayToGetParams(params, name);
        this.busy = true;
        // Alert.show(""+this.url);
        this.send();
        this.url = this.originalURL;
        this.originalURL = '';
    };
    //////////////////////////////////////////////////////////////////////////////////////////
    /**
     *  Use to check the state of the request
     */
    //////////////////////////////////////////////////////////////////////////////////////////
    /**
     *  Use to check the state of the request
     * @return {?}
     */
    HTTPComms.prototype.isBusy = 
    //////////////////////////////////////////////////////////////////////////////////////////
    /**
     *  Use to check the state of the request
     * @return {?}
     */
    function () {
        return this.busy;
    };
    /**
     * Reset the transaction unique Id by setting it to null value
     **/
    /**
     * Reset the transaction unique Id by setting it to null value
     *
     * @return {?}
     */
    HTTPComms.prototype.resetTransactionUId = /**
     * Reset the transaction unique Id by setting it to null value
     *
     * @return {?}
     */
    function () {
        this.screenUniqueTransactionId_ = null;
    };
    //////////////////////////////////////////////////////////////////////////////////////////
    //                            HTTPCOMMS GETTER AND SETTER                               //
    //////////////////////////////////////////////////////////////////////////////////////////
    //                            HTTPCOMMS GETTER AND SETTER                               //
    /**
     * @return {?}
     */
    HTTPComms.prototype.getTransactionUId = 
    //////////////////////////////////////////////////////////////////////////////////////////
    //                            HTTPCOMMS GETTER AND SETTER                               //
    /**
     * @return {?}
     */
    function () {
        return this.screenUniqueTransactionId_;
    };
    /**
     * Sets the screen transaction unique Id
     */
    /**
     * Sets the screen transaction unique Id
     * @param {?} programId
     * @param {?} uniqueIdentifier
     * @return {?}
     */
    HTTPComms.prototype.setTransactionUId = /**
     * Sets the screen transaction unique Id
     * @param {?} programId
     * @param {?} uniqueIdentifier
     * @return {?}
     */
    function (programId, uniqueIdentifier) {
        this.screenUniqueTransactionId_ = programId + ':' + ((uniqueIdentifier != null) ? uniqueIdentifier : '');
    };
    /**
     * Remotely start a transaction
     */
    /**
     * Remotely start a transaction
     * @param {?} programId
     * @param {?=} uniqueIdentifier
     * @return {?}
     */
    HTTPComms.prototype.startRemoteTransaction = /**
     * Remotely start a transaction
     * @param {?} programId
     * @param {?=} uniqueIdentifier
     * @return {?}
     */
    function (programId, uniqueIdentifier) {
        if (uniqueIdentifier === void 0) { uniqueIdentifier = null; }
        /** @type {?} */
        var trx = new RemoteTransaction(this, programId, uniqueIdentifier);
        trx.start();
        return trx;
    };
    /*
    ===========================================================================|
    Aded by Khalil.B to to send transactions Requests                          |
    ===========================================================================|
    */
    /*
        ===========================================================================|
        Aded by Khalil.B to to send transactions Requests                          |
        ===========================================================================|
        */
    /**
     * @param {?} url
     * @return {?}
     */
    HTTPComms.prototype.sendTransaction = /*
        ===========================================================================|
        Aded by Khalil.B to to send transactions Requests                          |
        ===========================================================================|
        */
    /**
     * @param {?} url
     * @return {?}
     */
    function (url) {
        var _this = this;
        try {
            /** @type {?} */
            var reponseType_1 = '&response=json';
            /** @type {?} */
            var headers_1 = new HttpHeaders();
            headers_1 = headers_1.append('Content-Type', 'application/x-www-form-urlencoded; charset=utf-8');
            headers_1 = headers_1.append('Accept', 'application/json, text/plain, */*');
            /** @type {?} */
            var body_1 = new HttpParams();
            return Observable.create((/**
             * @param {?} observer
             * @return {?}
             */
            function (observer) {
                _this.common.httpclient.post(url + reponseType_1, body_1, { headers: headers_1 })
                    .subscribe((/**
                 * @param {?} data
                 * @return {?}
                 */
                function (data) {
                    observer.next(data);
                }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                function (error) { return _this.fault(error); }));
            }));
        }
        catch (error) {
            console.error("sendTransaction ERROR: ", error);
        }
    };
    // Start : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    // Start : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    HTTPComms.prototype.result = 
    // Start : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.onFinish();
        try {
            if (event != null && event !== '') {
                if (this.jsonReader.getRequestReplyMessage() !== undefined
                    && this.jsonReader.getRequestReplyMessage() === 'session.expired') {
                    if (!this.alertFlag) {
                        this.alertFlag = true;
                        this.swtAlert.warning('Your session has been timed-out due to inactivity. Click OK to log in', 'Warning', Alert.OK, this.showLogon);
                        this.stopTimers();
                    }
                    else if (this.jsonReader.getRequestReplyMessage() !== undefined
                        && this.jsonReader.getRequestReplyMessage() === 'alert.sessionKilled.sceen') {
                        /*  SwtAlert.info(StringUtil.
                         * substitute(SwtUtil.getCommonMessages('alert.sessionKilled.sceen'),(event.result as XML).@programName)
                                  , SwtUtil.getCommonMessages('alert_header.warning'),null,null);*/
                        this.swtAlert.error('Session killed', 'Session killed', Alert.OK, this.showLogon);
                    }
                    else {
                        if (this.jsonReader.getRequestReplyStatus() === false) {
                            // Temporarly display error message
                            this.logger.error("[ result ] method : request replay message :", event.request_reply.message, " | ", event.message);
                        }
                        if (this.profling) {
                            this._localStart = this.getTimer();
                        }
                        this.cbResult(event);
                        if (this.profling) {
                            this._localDelay = this.getTimer() - this._localStart;
                        }
                    }
                }
                else {
                    if (this.profling) {
                        this._localStart = this.getTimer();
                    }
                    this.cbResult(event);
                    if (this.profling) {
                        this._localDelay = this.getTimer() - this._localStart;
                    }
                }
            }
            else {
                //Added By Seif Boubakri :
                //I add this code to fix the problem of return an empty response in Scheduler Screen.
                //Begin
                if (this.profling) {
                    this._localStart = this.getTimer();
                }
                this.cbResult(event);
                if (this.profling) {
                    this._localDelay = this.getTimer() - this._localStart;
                }
                //End
                //Commented as some request does not need to have a response message only status "200" is required
                //this.logger.error('Server side error, empty response is received.. See server logs');
            }
        }
        catch (error) {
            console.error("result error", error);
        }
    };
    // Start : Added to give custom message for fault event of a request by chiheb on 25 Jan 2018
    // Start : Added to give custom message for fault event of a request by chiheb on 25 Jan 2018
    /**
     * @private
     * @return {?}
     */
    HTTPComms.prototype.showLogon = 
    // Start : Added to give custom message for fault event of a request by chiheb on 25 Jan 2018
    /**
     * @private
     * @return {?}
     */
    function () {
        // TODO
    };
    // End : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    // End : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    HTTPComms.prototype.fault = 
    // End : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.error('url  = ' + this._url + '   System Fault: ' + event.message);
        this.onFinish();
        this.cbFault(event);
    };
    // End : Added to give custom message for fault event of a request by chiheb on 18/01/2018
    // End : Added to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @return {?}
     */
    HTTPComms.prototype.onStart = 
    // End : Added to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @return {?}
     */
    function () {
        if (this._profling) {
            this._start = this.getTimer();
        }
        this.cbStart();
    };
    /**
     * @private
     * @param {?} result
     * @return {?}
     */
    HTTPComms.prototype.getResultObject = /**
     * @private
     * @param {?} result
     * @return {?}
     */
    function (result) {
        /** @type {?} */
        var rootnode = '';
        /** @type {?} */
        var key;
        for (key in result) {
            if (result.hasOwnProperty(key)) {
                rootnode = key;
                break;
            }
        }
        return result[rootnode];
    };
    /**
     *  Function encodes the parameters that are passed
     */
    /**
     *  Function encodes the parameters that are passed
     * @private
     * @param {?} data
     * @return {?}
     */
    HTTPComms.prototype.encodeData = /**
     *  Function encodes the parameters that are passed
     * @private
     * @param {?} data
     * @return {?}
     */
    function (data) {
        /** @type {?} */
        var rtn = new Object();
        // Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            if (data) {
                errorLocation = 20;
                /** @type {?} */
                var dNoCache = new Date();
                /** @type {?} */
                var variable = void 0;
                /** @type {?} */
                var name_1;
                data.nocache = dNoCache.getTime().toString();
                for (name_1 in data) {
                    if (data.hasOwnProperty(name_1)) {
                        errorLocation = 30;
                        variable = encodeURIComponent(data[name_1]);
                        rtn[name_1] = variable.toString();
                    }
                }
            }
            errorLocation = 40;
        }
        catch (error) {
            //          SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".as", "encodeData", errorLocation);
        }
        return rtn;
    };
    /**
     *  Executes an HTTPService request. The parameters are optional, but if specified should
     *  be an Object containing name-value pairs or an XML object depending on the <code>contentType</code>.
     *  @return An object representing the asynchronous completion token. It is the same object
     *  available in the <code>result</code> or <code>fault</code> event's <code>token</code> property.
     */
    /**
     *  Executes an HTTPService request. The parameters are optional, but if specified should
     *  be an Object containing name-value pairs or an XML object depending on the <code>contentType</code>.
     * @private
     * @param {?=} params
     * @param {?=} json
     * @return {?} An object representing the asynchronous completion token. It is the same object
     *  available in the <code>result</code> or <code>fault</code> event's <code>token</code> property.
     */
    HTTPComms.prototype.sendRequest = /**
     *  Executes an HTTPService request. The parameters are optional, but if specified should
     *  be an Object containing name-value pairs or an XML object depending on the <code>contentType</code>.
     * @private
     * @param {?=} params
     * @param {?=} json
     * @return {?} An object representing the asynchronous completion token. It is the same object
     *  available in the <code>result</code> or <code>fault</code> event's <code>token</code> property.
     */
    function (params, json) {
        var _this = this;
        if (params === void 0) { params = new Object(); }
        try {
            /** @type {?} */
            var body = new HttpParams();
            /** @type {?} */
            var param = void 0;
            /** @type {?} */
            var reponseType = this._responsetype;
            /*response=json*/
            // TODO: it may be a case where no parameters are defined => ?response=json should be used instead of &response=json
            if (json === false) {
                reponseType = '&response=binary';
            }
            if (this._url.indexOf("?") !== -1) {
                reponseType = '&response=json';
            }
            for (param in params) {
                if (params.hasOwnProperty(param)) {
                    body = body.append(param, params[param].toString().replace(/\+/g, "%2B"));
                }
            }
            if (json === false) {
                this.common.httpclient.get(this._url + reponseType, { responseType: 'blob', observe: 'response' })
                    .subscribe((/**
                 * @param {?} response
                 * @return {?}
                 */
                function (response) {
                    /** @type {?} */
                    var contentDispositionHeader = response.headers.get('Content-Disposition');
                    /** @type {?} */
                    var parts = contentDispositionHeader.split(';');
                    /** @type {?} */
                    var filename = parts[1].split('=')[1];
                    /** @type {?} */
                    var blob = new Blob([((/** @type {?} */ (response.body)))], { type: 'application/octet-stream' });
                    saveAs(blob, filename);
                    // Added by Rihab JABALLAH to handle the finish of the report by the progress bar.
                    _this.onFinish();
                }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                function (error) {
                    _this.logger.error('Blob repose error !', error);
                }));
            }
            else {
                this.common.httpclient.post(this._url + reponseType, body)
                    .subscribe((/**
                 * @param {?} data
                 * @return {?}
                 */
                function (data) { return _this.result(data); }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                function (error) { return _this.fault(error); }));
            }
        }
        catch (error) {
            console.error("[ sendRequest ] METHOD ERROR:", error);
        }
    };
    /**
     * @private
     * @return {?}
     */
    HTTPComms.prototype.onFinish = /**
     * @private
     * @return {?}
     */
    function () {
        this.busy = false;
        this.cbStop();
        if (this._profling) {
            this._serverDelay = this.getTimer() - this._start;
        }
    };
    /**
     *  Use when sending an array it needs to be split
     */
    /**
     *  Use when sending an array it needs to be split
     * @private
     * @param {?} array
     * @param {?} name
     * @return {?}
     */
    HTTPComms.prototype.arrayToGetParams = /**
     *  Use when sending an array it needs to be split
     * @private
     * @param {?} array
     * @param {?} name
     * @return {?}
     */
    function (array, name) {
        return name + '[]=' + array.join('&' + name + '[]=');
    };
    /**
     * @private
     * @return {?}
     */
    HTTPComms.prototype.getTimer = /**
     * @private
     * @return {?}
     */
    function () {
        return new Date().getTime();
    };
    HTTPComms.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    HTTPComms.ctorParameters = function () { return [
        { type: CommonService }
    ]; };
    return HTTPComms;
}());
export { HTTPComms };
if (false) {
    /** @type {?} */
    HTTPComms.prototype.cbFault;
    /** @type {?} */
    HTTPComms.prototype.cbResult;
    /** @type {?} */
    HTTPComms.prototype.cbStart;
    /** @type {?} */
    HTTPComms.prototype.cbStop;
    /** @type {?} */
    HTTPComms.prototype.headers;
    /** @type {?} */
    HTTPComms.prototype.stopTimers;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.originalURL;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.busy;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.encode;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._start;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._result;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._method;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._localStart;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.alertFlag;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._cancelRequest;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.screenUniqueTransactionId_;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._responsetype;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._profling;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._serverDelay;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._localDelay;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._url;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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