/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, Injectable, Input, Output, ViewChild } from '@angular/core';
import { UIComponent } from "./UIComponent.service";
import { CommonService } from "../utils/common.service";
import { Logger } from "../../../com/swallow/logging/logger.service";
import { genericEvent } from "../../../com/swallow/events/swt-events.module";
import { LinkItemRander } from "../renderers/advancedDataGridRendres/link-item-render.component";
import { StringItemRender } from "../renderers/advancedDataGridRendres/string-item-render.component";
import { NumberItemRender } from "../renderers/advancedDataGridRendres/number-item-render.component";
import { Types } from "../renderers/advancedDataGridRendres/types";
import ResizeObserver from 'resize-observer-polyfill';
/** @type {?} */
var $ = require('jquery');
require('jquery.fancytree/dist/modules/jquery.fancytree.table');
require('jquery.fancytree/dist/modules/jquery.fancytree.fixed');
require('jquery.fancytree/dist/modules/jquery.fancytree.gridnav');
var AdvancedDataGrid = /** @class */ (function (_super) {
    tslib_1.__extends(AdvancedDataGrid, _super);
    function AdvancedDataGrid(elemnt, commonS) {
        var _this = _super.call(this, elemnt.nativeElement, commonS) || this;
        _this.elemnt = elemnt;
        _this.commonS = commonS;
        // input to set advancedDataGrid width.
        _this.width = "";
        // array to store advancedDataGrid column definition.
        _this.columnDefinition = [];
        // array to hold visible columns.
        _this.visibleColumn = [];
        // variable to set header width.
        _this.headerWidth = _this.width;
        // variable to set tree width.
        _this.treeWidth = 250;
        // variable to show/hide tree name in the dataGrid header
        _this.showTreeHeader = false;
        // variable to store tree name;
        _this.treeName = "";
        // public array to contain dataGrid Headers
        _this.AdvancedDataGridHeaders = [];
        // event emitter to dispatch row click event.
        _this.rowClick = new EventEmitter();
        // event emitter to dispatch row double click event.
        _this.rowDbClick = new EventEmitter();
        // event emitter to dispatch item expand event.
        _this.itemExpand = new EventEmitter();
        // event emitter to dispatch item collapse event.
        _this.itemCollapse = new EventEmitter();
        // event emitter to dispatch activate event.
        _this.activate = new EventEmitter();
        // variable to hold advanced data grid tree instance
        _this._treeInstance = null;
        // private variable to be true if it's the first load of grid
        _this._firstload = true;
        // private array to hold tree opened items.
        _this.savedState = [];
        // private variable to define row height
        _this._rowHeight = 22;
        // private variable to indicate that the grid have scroll bar or not.
        _this.scrollable = true;
        _this.isInternetExplorer = true;
        // private variable to handle scroll position.
        _this._verticalScrollPosition = 0;
        // private variable to set advancedDataGrid height.
        _this._height = "";
        // variable to store advancedDataGrid data.
        _this._dataProvider = [];
        _this.scrollableContentHeight = 0;
        _this.advancedDatagridContentHeight = 0;
        _this.rowHeight = -1;
        _this.itemRenderList = [];
        _this.log = new Logger("AdvancedDataGrid", _this.commonS.httpclient);
        return _this;
    }
    /**
     * @return {?}
     */
    AdvancedDataGrid.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var ro = new ResizeObserver((/**
         * @param {?} entries
         * @param {?} observer
         * @return {?}
         */
        function (entries, observer) {
            _this.calculateDivHeight();
        }));
        ro.observe(this.treegrid.nativeElement);
    };
    Object.defineProperty(AdvancedDataGrid.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGrid.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            try {
                this.initialize();
                if (this.isFirstLoad()) {
                    this.treeData = value["Tree"];
                    /** @type {?} */
                    var columns = value["grid_data"].metadata.columns.column;
                    this.recursive(this.treeData);
                    if (this.treeData.children) {
                        this._dataProvider = this.treeData;
                    }
                    else {
                        this._dataProvider = [];
                    }
                    // get tree width if exists.
                    if (this.treeData.width) {
                        this.treeWidth = this.treeData.width;
                    }
                    this.columnDefinition = tslib_1.__spread(columns);
                    this.AdvancedDataGridHeaders = columns;
                    this.columnDefinition.forEach((/**
                     * @param {?} column
                     * @return {?}
                     */
                    function (column) {
                        if (column.visible) {
                            _this.visibleColumn.push(column);
                        }
                    }));
                    this.paintColumns();
                    this.tempWidth = "";
                    this.tempWidth = this.headerWidth;
                    if (this.showTreeHeader) {
                        this.treeName = this.treeData.title;
                    }
                    else {
                        this.treeName = "";
                    }
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        _this.init();
                    }), 0);
                    this._firstload = false;
                }
                else {
                    for (var index = 0; index < this.itemRenderList.length; index++) {
                        this.itemRenderList[index].destroy();
                    }
                    this.itemRenderList = null;
                    this.itemRenderList = [];
                    /** @type {?} */
                    var clone = this.clone(value["Tree"]);
                    if (clone && clone.children) {
                        this.treeData = value["Tree"];
                        this.recursive(clone);
                        this._dataProvider = clone;
                        this.refresh();
                        if (this.savedState) {
                            this.openSavedTreeState();
                        }
                    }
                    else {
                        this._dataProvider = [];
                        this.refresh();
                    }
                }
            }
            catch (error) {
                this.log.error('[ dataProvider ] METHOD ERROR: ', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    AdvancedDataGrid.prototype.calculateDivHeight = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var calculateDivHeight;
        // console.log($(".scrollable-content")[.height()] , $(".advancedDatagrid-content").height());
        this.scrollableContentHeight = $('.scrollable-content')[0].clientHeight;
        this.advancedDatagridContentHeight = $('.advancedDatagrid-content')[0].clientHeight;
        if (this.advancedDatagridContentHeight >= this.scrollableContentHeight) {
            calculateDivHeight = 0;
        }
        else {
            calculateDivHeight = this.scrollableContentHeight - this.advancedDatagridContentHeight - 5;
        }
        $('.divTable').height(calculateDivHeight);
    };
    /**
     * @return {?}
     */
    AdvancedDataGrid.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.setStyle("height", this.height + (this.height.toString().indexOf("%") === -1 ? "px" : ""));
        this.setStyle("width", this.width + (this.width.toString().indexOf("%") === -1 ? "px" : ""));
        this.elemnt.nativeElement.style.overflow = "hidden";
        $(((/** @type {?} */ (window)))).resize((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.paintColumns();
            _this.synchronizeHeaderLayout();
            _this.calculateDivHeight();
        }));
        this.isInternetExplorer = this.isIE();
    };
    /**
     * This method is used to paint advanced data grid columns
     */
    /**
     * This method is used to paint advanced data grid columns
     * @private
     * @return {?}
     */
    AdvancedDataGrid.prototype.paintColumns = /**
     * This method is used to paint advanced data grid columns
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var __width = 0;
        /** @type {?} */
        var diff = 0;
        /** @type {?} */
        var colId = 0;
        try {
            for (var i = 0; i < this.visibleColumn.length; i++) {
                __width += Number(this.visibleColumn[i].width);
            }
            this.headerWidth = String(__width + this.treeWidth);
            if (Number(this.headerWidth) > Number($(this.elemnt.nativeElement).width())) {
                this.scrollable = true;
            }
            else {
                this.scrollable = false;
            }
            /** @type {?} */
            var columnNumber = this.visibleColumn.length;
            /** @type {?} */
            var scrollBarContentWidht = this.isInternetExplorer ? 4 : 2;
            colId = columnNumber - 1;
            if (Number(this.headerWidth) < Number($(this.elemnt.nativeElement).width())) {
                this.scrollable = false;
                diff = Number($(this.elemnt.nativeElement).width()) - Number(this.headerWidth) - (scrollBarContentWidht + this.getScrollbarWidth());
                this.headerWidth = String(Number(this.headerWidth) + diff);
            }
            else if (Number(this.headerWidth) > Number($(this.elemnt.nativeElement).width())) {
                if (Number(this.headerWidth) > this.tempWidth) {
                    diff = Number(this.headerWidth) - Number($(this.elemnt.nativeElement).width()) - 2;
                    this.scrollable = true;
                    this.visibleColumn[colId].width = Number(this.visibleColumn[columnNumber - 1].width) - diff;
                    if (Number($(this.elemnt.nativeElement).width() > Number(this.tempWidth))) {
                        this.headerWidth = String(Number(this.headerWidth) - diff);
                    }
                    else {
                        this.headerWidth = this.tempWidth;
                    }
                    $(".scrollable-content").width(this.tempWidth);
                    $("#" + colId).width(this.visibleColumn[columnNumber - 1].width);
                }
            }
        }
        catch (error) {
            this.log.error("paintColumns error: ", error);
        }
    };
    /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     **/
    /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     *
     * @private
     * @return {?}
     */
    AdvancedDataGrid.prototype.getScrollbarWidth = /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     *
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var outer = document.createElement("div");
        outer.style.visibility = "hidden";
        outer.style.width = "100px";
        outer.style.msOverflowStyle = "scrollbar"; // needed for WinJS apps
        document.body.appendChild(outer);
        /** @type {?} */
        var widthNoScroll = outer.offsetWidth;
        // force scrollbars
        outer.style.overflow = "scroll";
        // add innerdiv
        /** @type {?} */
        var inner = document.createElement("div");
        inner.style.width = "100%";
        outer.appendChild(inner);
        /** @type {?} */
        var widthWithScroll = inner.offsetWidth;
        // remove divs
        outer.parentNode.removeChild(outer);
        return widthNoScroll - widthWithScroll;
    };
    /**
     * @return {?}
     */
    AdvancedDataGrid.prototype.getTreeStates = /**
     * @return {?}
     */
    function () {
        return this.savedState;
    };
    /**
     * @param {?} value
     * @return {?}
     */
    AdvancedDataGrid.prototype.setTreeStates = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.savedState = value;
    };
    /**
     * This method is used to get tree instance.
     */
    /**
     * This method is used to get tree instance.
     * @return {?}
     */
    AdvancedDataGrid.prototype.getTreeInstance = /**
     * This method is used to get tree instance.
     * @return {?}
     */
    function () {
        if (this._treeInstance) {
            return this._treeInstance.fancytree("getTree");
        }
        else {
            return null;
        }
    };
    /**
     * This method is used to set metaData to advanced dataGrid instance.
     * @param metadata
     */
    /**
     * This method is used to set metaData to advanced dataGrid instance.
     * @param {?} metadata
     * @return {?}
     */
    AdvancedDataGrid.prototype.setAdvancedGridMetaData = /**
     * This method is used to set metaData to advanced dataGrid instance.
     * @param {?} metadata
     * @return {?}
     */
    function (metadata) {
        var _this = this;
        try {
            this.visibleColumn = [];
            metadata.forEach((/**
             * @param {?} column
             * @return {?}
             */
            function (column) {
                if (column.visible) {
                    _this.visibleColumn.push(column);
                }
            }));
            this.dataProvider = this.treeData;
            this.getTreeInstance().reload(this.treeData);
        }
        catch (error) {
            this.log.error("setGridMetaData - error: ", error);
        }
    };
    /**
     * This method is used to get capture of the
     * current tree state and save it in the
     * saved tree state array.
     */
    /**
     * This method is used to get capture of the
     * current tree state and save it in the
     * saved tree state array.
     * @return {?}
     */
    AdvancedDataGrid.prototype.saveOpenTreeState = /**
     * This method is used to get capture of the
     * current tree state and save it in the
     * saved tree state array.
     * @return {?}
     */
    function () {
        try {
            if (this.getTreeInstance()) {
                this.savedState = this.getTreeInstance().findAll((/**
                 * @param {?} node
                 * @return {?}
                 */
                function (node) {
                    return node.expanded === true;
                }));
            }
            return this.savedState;
        }
        catch (error) {
            this.log.error("saveOpenTreeState - error: ", error);
        }
    };
    /**
     * This method is used to open saved tree state
     * or open a given state.
     * @param state
     */
    /**
     * This method is used to open saved tree state
     * or open a given state.
     * @param {?=} state
     * @return {?}
     */
    AdvancedDataGrid.prototype.openSavedTreeState = /**
     * This method is used to open saved tree state
     * or open a given state.
     * @param {?=} state
     * @return {?}
     */
    function (state) {
        /** @type {?} */
        var _state = null;
        try {
            if (state) {
                _state = state;
            }
            else {
                _state = this.savedState;
            }
            /** @type {?} */
            var tree_1 = this.getTreeInstance();
            if (_state && _state.length > 0) {
                _state.forEach((/**
                 * @param {?} node
                 * @return {?}
                 */
                function (node) {
                    /** @type {?} */
                    var savedNode = tree_1.getNodeByKey(node.key);
                    if (savedNode) {
                        savedNode.setExpanded(true);
                    }
                }));
            }
            this.savedState = _state;
        }
        catch (error) {
            this.log.error("openSavedTreeState - error: ", error);
        }
    };
    /**
     * get all the advancedDataGrid meta data if index is undefined
     * else return the metaData in the given index.
     * @param index
     */
    /**
     * get all the advancedDataGrid meta data if index is undefined
     * else return the metaData in the given index.
     * @param {?=} index
     * @return {?}
     */
    AdvancedDataGrid.prototype.getAdvancedGridMetaData = /**
     * get all the advancedDataGrid meta data if index is undefined
     * else return the metaData in the given index.
     * @param {?=} index
     * @return {?}
     */
    function (index) {
        if (index) {
            return this.columnDefinition[index];
        }
        else {
            return this.columnDefinition;
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    AdvancedDataGrid.prototype.setConnectors = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        setTimeout((/**
         * @return {?}
         */
        function () {
            $(".fancytree-container").toggleClass("fancytree-connectors");
        }), 0);
    };
    /**
     * This method is used to handle double click event
     * in advanced DataGrid.
     * @param event
     * @param data
     */
    /**
     * This method is used to handle double click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} data
     * @return {?}
     */
    AdvancedDataGrid.prototype.advancedDataGridRowDbClick = /**
     * This method is used to handle double click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} data
     * @return {?}
     */
    function (event, data) {
        try {
            /** @type {?} */
            var node = data.node;
            /** @type {?} */
            var selectedRow = new AdvancedDataGridRow(node, this.commonS);
            selectedRow.node = node;
            this.rowDbClick.emit(selectedRow);
            if (this.eventlist[genericEvent.ROW_DBCLICK]) {
                this.eventlist[genericEvent.ROW_DBCLICK](selectedRow);
            }
        }
        catch (error) {
            this.log.error("advancedDataGridRowDbClick - error: ", error);
        }
    };
    /**
     * This method is used to handle click event
     * in advanced DataGrid.
     * @param event
     * @param data
     */
    /**
     * This method is used to handle click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} row
     * @return {?}
     */
    AdvancedDataGrid.prototype.advancedDataGridRowClick = /**
     * This method is used to handle click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} row
     * @return {?}
     */
    function (event, row) {
        try {
            /** @type {?} */
            var searchIDs = this.getTreeInstance().getSelectedNodes();
            searchIDs.forEach((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                node.setSelected(false);
                node.selected = false;
                node.render();
            }));
            // Update selected row.
            this._selectedRow = row;
            this.rowClick.emit(row);
            if (this.eventlist[genericEvent.ROW_CLICK]) {
                this.eventlist[genericEvent.ROW_CLICK](row);
            }
            if (Number(event.target.id) > -1) {
                if (this.eventlist[genericEvent.CELL_CLICK]) {
                    /** @type {?} */
                    var cell = (/** @type {?} */ (row.getCellAt(Number(event.target.id))));
                    cell.setParentRow(row);
                    cell.columnHeader = this.visibleColumn[Number(event.target.id)];
                    this.eventlist[genericEvent.CELL_CLICK](cell);
                }
            }
            /** @type {?} */
            var node = this.getTreeInstance().getNodeByKey(row.key);
            node.setSelected(true);
        }
        catch (error) {
            this.log.error("advancedDataGridRowClick - error: ", error);
        }
    };
    /**
     * This method called on each group item render.
     * @param row
     */
    /**
     * This method called on each group item render.
     * @param {?} row
     * @return {?}
     */
    AdvancedDataGrid.prototype.onGroupItemRender = /**
     * This method called on each group item render.
     * @param {?} row
     * @return {?}
     */
    function (row) {
        try {
        }
        catch (error) {
            this.log.error("onGroupItemRender - error: ", error);
        }
    };
    /**
     * This method is used to get the selected row.
     */
    /**
     * This method is used to get the selected row.
     * @return {?}
     */
    AdvancedDataGrid.prototype.getSelectedRow = /**
     * This method is used to get the selected row.
     * @return {?}
     */
    function () {
        return this._selectedRow;
    };
    /**
     * This method is used to set selected row.
     * @param value
     */
    /**
     * This method is used to set selected row.
     * @param {?} value
     * @return {?}
     */
    AdvancedDataGrid.prototype.setSelectedRow = /**
     * This method is used to set selected row.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        try {
            this._selectedRow = value;
            if (value) {
                /** @type {?} */
                var node = this.getTreeInstance().getNodeByKey(value.key);
                if (node) {
                    try {
                        node.setSelected(true);
                    }
                    catch (error) {
                        console.log(error);
                    }
                }
            }
        }
        catch (error) {
            this.log.error("setSelectedRow - error: ", error);
        }
    };
    /**
     * This method is used to get the selected column.
     */
    /**
     * This method is used to get the selected column.
     * @return {?}
     */
    AdvancedDataGrid.prototype.getSelectedColumn = /**
     * This method is used to get the selected column.
     * @return {?}
     */
    function () {
        return this._selectedColumn;
    };
    /**
     * This method is used to get the selected
     * cell.
     */
    /**
     * This method is used to get the selected
     * cell.
     * @return {?}
     */
    AdvancedDataGrid.prototype.getSelectedCell = /**
     * This method is used to get the selected
     * cell.
     * @return {?}
     */
    function () {
        return this._selectedCell;
    };
    /**
     * Collapses all the nodes of the navigation tree.
     */
    /**
     * Collapses all the nodes of the navigation tree.
     * @return {?}
     */
    AdvancedDataGrid.prototype.collapseAll = /**
     * Collapses all the nodes of the navigation tree.
     * @return {?}
     */
    function () {
        try {
            if (this.getTreeInstance()) {
                this.getTreeInstance().expandAll(false);
                this.synchronizeHeaderLayout();
            }
        }
        catch (error) {
            this.log.error("collapseAll - error: ", error);
        }
    };
    /**
     *  Expands all the nodes of the navigation tree in the control.
     */
    /**
     *  Expands all the nodes of the navigation tree in the control.
     * @return {?}
     */
    AdvancedDataGrid.prototype.expandAll = /**
     *  Expands all the nodes of the navigation tree in the control.
     * @return {?}
     */
    function () {
        try {
            if (this.getTreeInstance()) {
                this.getTreeInstance().expandAll(true);
                this.synchronizeHeaderLayout();
            }
        }
        catch (error) {
            this.log.error("expandAll - error: ", error);
        }
    };
    /**
     * @param {?} source
     * @return {?}
     */
    AdvancedDataGrid.prototype.clone = /**
     * @param {?} source
     * @return {?}
     */
    function (source) {
        if (Object.prototype.toString.call(source) === '[object Array]') {
            /** @type {?} */
            var clone = [];
            for (var i = 0; i < source.length; i++) {
                clone[i] = this.clone(source[i]);
            }
            return clone;
        }
        else if (typeof (source) === "object") {
            /** @type {?} */
            var clone = {};
            for (var prop in source) {
                if (source.hasOwnProperty(prop)) {
                    clone[prop] = this.clone(source[prop]);
                }
            }
            return clone;
        }
        else {
            return source;
        }
    };
    /**
     * @return {?}
     */
    AdvancedDataGrid.prototype.refresh = /**
     * @return {?}
     */
    function () {
        try {
            // reload the tree data.
            this.getTreeInstance().reload(this.dataProvider);
            // repaint the grid view.
            this.synchronizeHeaderLayout();
        }
        catch (error) {
            this.log.error("refresh - error: ", error);
        }
    };
    /**
     *  Opens or closes all the nodes of the navigation tree below the specified item.
     * @param item
     * @param open
     */
    /**
     *  Opens or closes all the nodes of the navigation tree below the specified item.
     * @param {?} item
     * @param {?} open
     * @return {?}
     */
    AdvancedDataGrid.prototype.expandChildrenOf = /**
     *  Opens or closes all the nodes of the navigation tree below the specified item.
     * @param {?} item
     * @param {?} open
     * @return {?}
     */
    function (item, open) {
    };
    /**
     *  Opens or closes a branch node of the navigation tree.
     * @param item
     * @param open
     * @param animate
     * @param dispatchEvent
     * @param cause
     */
    /**
     *  Opens or closes a branch node of the navigation tree.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    AdvancedDataGrid.prototype.expandItem = /**
     *  Opens or closes a branch node of the navigation tree.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    function (item, open, animate, dispatchEvent, cause) {
        if (animate === void 0) { animate = false; }
        if (dispatchEvent === void 0) { dispatchEvent = false; }
        if (cause === void 0) { cause = null; }
    };
    /**
     *  Returns the parent of a child item.
     * @param item
     */
    /**
     *  Returns the parent of a child item.
     * @param {?} item
     * @return {?}
     */
    AdvancedDataGrid.prototype.getParentItem = /**
     *  Returns the parent of a child item.
     * @param {?} item
     * @return {?}
     */
    function (item) {
    };
    /**
     *  Returns true if the specified branch node is open.
     * @param item
     */
    /**
     *  Returns true if the specified branch node is open.
     * @param {?} item
     * @return {?}
     */
    AdvancedDataGrid.prototype.isItemOpen = /**
     *  Returns true if the specified branch node is open.
     * @param {?} item
     * @return {?}
     */
    function (item) {
    };
    /**
     *  Sets the associated icon in the navigation tree for the item.
     * @param item
     * @param iconID
     * @param iconID2
     */
    /**
     *  Sets the associated icon in the navigation tree for the item.
     * @param {?} item
     * @param {?} iconID
     * @param {?} iconID2
     * @return {?}
     */
    AdvancedDataGrid.prototype.setItemIcon = /**
     *  Sets the associated icon in the navigation tree for the item.
     * @param {?} item
     * @param {?} iconID
     * @param {?} iconID2
     * @return {?}
     */
    function (item, iconID, iconID2) {
    };
    /**
     * This method returns true if is the first load of
     * grid.
     */
    /**
     * This method returns true if is the first load of
     * grid.
     * @return {?}
     */
    AdvancedDataGrid.prototype.isFirstLoad = /**
     * This method returns true if is the first load of
     * grid.
     * @return {?}
     */
    function () {
        return this._firstload;
    };
    /**
     * @private
     * @param {?} mainObj
     * @return {?}
     */
    AdvancedDataGrid.prototype.deepCopy = /**
     * @private
     * @param {?} mainObj
     * @return {?}
     */
    function (mainObj) {
        /** @type {?} */
        var objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        var key;
        for (key in mainObj) {
            if (mainObj.hasOwnProperty(key)) {
                objCopy[key] = mainObj[key]; // copies each property to the objCopy object
            }
        }
        return objCopy;
    };
    /**
     * This method is used to synchronize the position
     * of headers with the corresponding column
     */
    /**
     * This method is used to synchronize the position
     * of headers with the corresponding column
     * @private
     * @return {?}
     */
    AdvancedDataGrid.prototype.synchronizeHeaderLayout = /**
     * This method is used to synchronize the position
     * of headers with the corresponding column
     * @private
     * @return {?}
     */
    function () {
        try {
            $("tr.unselectable_r").remove();
            /** @type {?} */
            var headerheigth = Number($(".head")[0].clientHeight);
            $(".scrollable-content").height(Number($(this.elemnt.nativeElement).height()) - headerheigth - 15 + "px");
            $('.scroller').height($(".advancedDatagrid-content").height());
            /** @type {?} */
            var localHeigt = $($('.fancytree-has-children')[0]).outerHeight();
            this.rowHeight = localHeigt;
            if (this.isInternetExplorer) {
                $($($(this.elemnt.nativeElement).find('.advancedTreeDivItem'))[0])[0].style.backgroundImage = '-ms-repeating-linear-gradient( -90deg, white, white ' + (localHeigt - 1) + 'px,#DFDFDF ' + (localHeigt - 1) + 'px, #DFDFDF ' + (localHeigt) + 'px, #E0F0FF ' + (localHeigt) + 'px, #E0F0FF ' + (localHeigt * 2 - 1) + 'px,#DFDFDF ' + (localHeigt * 2 - 1) + 'px, #DFDFDF ' + (localHeigt * 2) + 'px)';
            }
            else {
                $($($(this.elemnt.nativeElement).find('.advancedTreeDivItem'))[0])[0].style.backgroundImage = 'repeating-linear-gradient( 180deg, white, white ' + (this.rowHeight - 1) + 'px,#DFDFDF ' + (this.rowHeight - 1) + 'px, #DFDFDF ' + (this.rowHeight) + 'px, #E0F0FF ' + (this.rowHeight) + 'px, #E0F0FF ' + (this.rowHeight * 2 - 1) + 'px,#DFDFDF ' + (this.rowHeight * 2 - 1) + 'px, #DFDFDF ' + (this.rowHeight * 2) + 'px)';
            }
            $('#advancedTreeNoDataBody').empty();
            $('#advancedTreeNoDataBody').append("<div class='divTableCell'  style=' height:100%;width: " + this.treeWidth + "px; border: 1px solid #DFDFDF'></div>");
            for (var i = 0; i < this.visibleColumn.length; i++) {
                $('#advancedTreeNoDataBody').append("<div class='divTableCell' style=' height:100%;width: " + (i < this.visibleColumn.length - 1 ? (this.visibleColumn[i].width - 1) : (this.visibleColumn[i].width - 2)) + "px; border-right: 1px solid #DFDFDF'></div>");
            }
            this.paintColumns();
        }
        catch (e) {
            this.log.error("synchronizeHeaderLayout - error :", e);
        }
    };
    Object.defineProperty(AdvancedDataGrid.prototype, "verticalScrollPosition", {
        get: /**
         * @return {?}
         */
        function () {
            return this._verticalScrollPosition;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(".scrollable-content").animate({ scrollTop: value }, 0);
            this._verticalScrollPosition = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @private
     * @return {?}
     */
    AdvancedDataGrid.prototype.isIE = /**
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var ua = navigator.userAgent;
        /* MSIE used to detect old browsers and Trident used to newer ones*/
        /** @type {?} */
        var is_ie = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;
        return is_ie;
    };
    /**
     * @private
     * @param {?} c
     * @return {?}
     */
    AdvancedDataGrid.prototype.componentToHex = /**
     * @private
     * @param {?} c
     * @return {?}
     */
    function (c) {
        /** @type {?} */
        var hex = c.toString(16);
        return hex.length === 1 ? "0" + hex : hex;
    };
    /**
     * @private
     * @param {?} r
     * @param {?} g
     * @param {?} b
     * @return {?}
     */
    AdvancedDataGrid.prototype.rgbToHex = /**
     * @private
     * @param {?} r
     * @param {?} g
     * @param {?} b
     * @return {?}
     */
    function (r, g, b) {
        return "#" + this.componentToHex(r) + this.componentToHex(g) + this.componentToHex(b);
    };
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @param value
     */
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @return {?}
     */
    AdvancedDataGrid.prototype.recursive = /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            // test if value is an object.
            if (value.length === undefined) {
                value = tslib_1.__spread([value]);
            }
            errorLocation = 10;
            // loop to data
            for (var index = 0; index < value.length; index++) {
                /** @type {?} */
                var leaf = value[index];
                // loop leaf keys.
                for (var key in leaf) {
                    if (leaf.hasOwnProperty(key)) {
                        if (leaf[key] === "true") {
                            leaf[key] = true;
                        }
                        else if (leaf[key] === "false") {
                            leaf[key] = false;
                        }
                        if (typeof (leaf[key]) === 'object') {
                            if (!leaf[key].length) {
                                leaf[key] = [leaf[key]];
                            }
                            this.recursive(leaf[key]);
                        }
                    }
                }
            }
        }
        catch (error) {
            this.log.error('[ recursive ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
        return value;
    };
    /**
     * This method is used to initialize params to default values.
     */
    /**
     * This method is used to initialize params to default values.
     * @return {?}
     */
    AdvancedDataGrid.prototype.initialize = /**
     * This method is used to initialize params to default values.
     * @return {?}
     */
    function () {
        this.setSelectedRow(null);
    };
    /**
     * @private
     * @return {?}
     */
    AdvancedDataGrid.prototype.init = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            this._treeInstance = ((/** @type {?} */ ($('.advancedDatagrid-content')))).fancytree({
                extensions: ['table'],
                checkbox: false,
                table: {
                    indentation: 20,
                    // indent 20px per node level
                    nodeColumnIdx: 0,
                    // render the node title into the 2nd column
                    checkboxColumnIdx: 0 // render the checkboxes into the 1st column
                },
                source: this.dataProvider,
                tooltip: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    return data.node.data.title;
                }),
                lazyLoad: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                }),
                renderColumns: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var tdList = $(node.tr).find('>td');
                    /** @type {?} */
                    var row = new AdvancedDataGridRow(node, _this.commonS);
                    row.itemRenderListFromParent = _this.itemRenderList;
                    row.node = node;
                    row.createCells(node, _this.visibleColumn);
                    _this.onGroupItemRender(row);
                    if (_this.groupItemRenderer) {
                        _this.groupItemRenderer(row);
                    }
                    $(node.tr).click((/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        if (!$(event.target).hasClass("fancytree-expander")) {
                            _this.advancedDataGridRowClick(event, row);
                        }
                    }));
                    $(node.tr).dblclick((/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.advancedDataGridRowDbClick(event, data);
                    }));
                }),
                activate: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    // const selectedRow = new AdvancedDataGridRow($(node.tr)[0], this.commonS);
                    /** @type {?} */
                    var selectedRow = new AdvancedDataGridRow(node, _this.commonS);
                    selectedRow.node = node;
                    selectedRow.createCells(node, _this.visibleColumn);
                    _this.activate.emit(selectedRow);
                    if (_this.eventlist[genericEvent.ACTIVATE]) {
                        _this.eventlist[genericEvent.ACTIVATE](selectedRow);
                    }
                }),
                expand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    _this.synchronizeHeaderLayout();
                }),
                collapse: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    _this.synchronizeHeaderLayout();
                }),
                beforeExpand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                })
            });
            // synchronize the dataGrid view.
            this.synchronizeHeaderLayout();
            // a helper variable
            /** @type {?} */
            var timeout;
            $('.scroller-container, .scrollable-content').on("scroll", (/**
             * @return {?}
             */
            function callback() {
                // clear the 'timeout' every 'scroll' event call
                // to prevent re-assign 'scroll' event to other element
                // before finished scrolling
                clearTimeout(timeout);
                // get the used elements
                /** @type {?} */
                var source = $(this);
                /** @type {?} */
                var target = $(source.is(".scroller-container") ? '.scrollable-content' : '.scroller-container');
                /** @type {?} */
                var scrollPoisition = source.scrollTop();
                this._verticalScrollPosition = scrollPoisition;
                // remove the callback from the other 'div' and set the 'scrollTop'
                target.off("scroll").scrollTop(scrollPoisition);
                // create a new 'timeout' and reassign 'scroll' event
                // to other 'div' on 100ms after the last event call
                timeout = setTimeout((/**
                 * @return {?}
                 */
                function () {
                    target.on("scroll", callback);
                }), 100);
            }));
        }
        catch (error) {
            this.log.error('[ init ] METHOD ERROR: ', error);
        }
    };
    AdvancedDataGrid.decorators = [
        { type: Component, args: [{
                    selector: 'AdvancedDataGrid',
                    template: "\n        <div class = \"advancedTreeDivItem\" style=\"overflow-x: scroll; overflow-y: hidden; border: 1px solid #cccccc; width: calc(100% - 17px)\"\n             [style.height.%]=\"100\">\n\n            <table   class=\"head\" [style.width.px]=\"headerWidth\" [class.default]=\"visibleColumn.length === 0\">\n                <colgroup>\n                    <col [style.width.px]=\"treeWidth\"/>\n                    <col *ngFor=\"let column of visibleColumn\"  [style.width.px]=\"column.width\"/>\n                </colgroup>\n                <thead>\n                <tr class=\"advancedDataGridHeader\">\n                    <th class=\"header-column\" style=\"border-left: 1px solid #529FED\" [style.width.px]=\"treeWidth\">{{ treeName }}</th>\n                    <ng-container *ngFor=\"let column of visibleColumn let colindex = index\">\n                        <th class=\"header-column\" *ngIf=\"column.visible\" [style.width.px]=\"column.width\" [class.lastcolumn-border]=\"(colindex === visibleColumn.length - 1)\">{{ column.heading }}</th>\n                    </ng-container>\n                </tr>\n                </thead>\n            </table>\n            <div  class=\"scrollable-content\"  style=\"overflow-x: hidden; overflow-y: auto;\" [style.width.px]=\"headerWidth\">\n                <table  #treegrid class=\"advancedDatagrid-content\" [style.width.px]=\"headerWidth\">\n                    <tbody class=\"advancedtree-body\">\n                    <tr>\n                        <td style=\"padding-left: 3px\" [style.width.px]=\"treeWidth\"></td>\n                        <ng-container *ngFor=\"let column of visibleColumn let colIndex = index\">\n                            <td class=\"header-column-r\" [id]=\"colIndex\" [style.width.px]=\"column.width\" *ngIf=\"column.visible\"></td>\n                        </ng-container>\n                    </tr>\n                    </tbody>\n                </table>\n                <div class=\"divTable\"  style=\"width:100%\">\n                    <div class=\"divTableBody\">\n                        <div id=\"advancedTreeNoDataBody\" class=\"divTableRow\">\n\n                        </div>\n\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class=\"scroll-bar\" style=\"display: block; border: none\">\n            <div class=\"header-end\"\n                 style=\"height: 26px; background-color: #529FED; border-top: 1px solid #CCC; border-left: none; width: 100%\"></div>\n            <div class=\"scroller-container\"\n                 style=\"width: 100%; height: calc(100% - 23px); overflow-y: scroll; overflow-x: hidden\">\n                <div class=\"scroller\" style=\"width: 1px;\"></div>\n            </div>\n        </div>\n    ",
                    styles: ["\n        :host {\n            display: flex;\n            overflow: auto;\n            background-color: #FFF;\n        }\n        .scroll-bar {\n            width: 17px;\n            height: calc(100% - 17px);\n        }\n        @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n            .scroll-bar {\n                width: 19px !important;\n            }\n        }\n\n        .advancedTreeDivItem table td, table th{\n            padding : 2px;\n        }\n\n        .scrollable-content::-webkit-scrollbar {\n            width: 0 !important; /* Remove scrollbar space */\n            background: transparent !important; /* Optional: just make scrollbar invisible */\n            -ms-overflow-style: none !important; /* hide scroll bar for IE*/\n            overflow: -moz-scrollbars-none !important;\n            display: none !important;\n        }\n\n        .scrollable-content {\n            -ms-overflow-style: none;\n            scrollbar-width: none;  // Firefox\n        }\n\n        .advancedDatagrid-content {\n            width: 100%;\n            border: 1px solid #DFDFDF;\n            overflow: hidden;\n        }\n\n        .advancedDatagrid-content:focus {\n            outline: none;\n        }\n\n        /* class to set the last border darkblue*/\n        .lastcolumn-border {\n            border-right: 1px solid #529FED !important;\n        }\n\n        .default {\n            width: 100%;\n        }\n\n        thead tr {\n            height: 25px;\n            background-color: #529FED;\n            color: #FFFFFF;\n            font-size: 11px!important;\n            font-weight: bold!important;\n            font-family: Verdana, Helvatica !important;\n        }\n\n        /* provide some minimal visual accomodation for IE8 and below */\n        .advancedtree-body tr {\n            background: #FFFFFF;\n            border-top: 1px solid #DFDFDF;\n            border-bottom: 1px solid #DFDFDF;\n            text-overflow: clip;\n            white-space: nowrap;\n            overflow: hidden;\n        }\n\n        /*  Define the background color for all the ODD background rows  */\n        .advancedtree-body tr:nth-child(odd) {\n            background: #FFFFFF;\n        }\n\n        /*  Define the background color for all the EVEN background rows  */\n        .advancedtree-body tr:nth-child(even) {\n            background: #E0F0FF;\n        }\n\n        table.fancytree-ext-table tbody tr.fancytree-active {\n            background-color: #FFCC66 !important;\n        }\n        .fancytree-selected {\n            background-color: #FFCC66 !important;\n        }\n        .span.fancytree-title {\n            color:black !important;\n        }\n\n        .fancytree-active span.fancytree-title {\n            background-color: transparent !important;\n        }\n\n        .header-column {\n            border-left: 1px solid #FFFFFF;\n            border-right: 1px solid #FFFFFF;\n            padding-left: 10px;\n            text-align: center;\n        }\n\n        .head {\n            table-layout: fixed;\n        }\n        .header-column-r {\n            border: 1px solid #DFDFDF;\n            padding: 0 0 0 0;\n            margin: 0px;\n        }\n\n\n\n    "]
                }] }
    ];
    /** @nocollapse */
    AdvancedDataGrid.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    AdvancedDataGrid.propDecorators = {
        treegrid: [{ type: ViewChild, args: ['treegrid',] }],
        width: [{ type: Input, args: ['width',] }],
        rowClick: [{ type: Output, args: ["rowClick",] }],
        rowDbClick: [{ type: Output, args: ["rowDbClick",] }],
        itemExpand: [{ type: Output, args: ["itemExpand",] }],
        itemCollapse: [{ type: Output, args: ["itemCollapse",] }],
        activate: [{ type: Output, args: ["activate",] }],
        height: [{ type: Input }],
        dataProvider: [{ type: Input }]
    };
    return AdvancedDataGrid;
}(UIComponent));
export { AdvancedDataGrid };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    AdvancedDataGrid.prototype.treegrid;
    /** @type {?} */
    AdvancedDataGrid.prototype.width;
    /** @type {?} */
    AdvancedDataGrid.prototype.columnDefinition;
    /** @type {?} */
    AdvancedDataGrid.prototype.visibleColumn;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeData;
    /** @type {?} */
    AdvancedDataGrid.prototype.headerWidth;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeWidth;
    /** @type {?} */
    AdvancedDataGrid.prototype.showTreeHeader;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeName;
    /** @type {?} */
    AdvancedDataGrid.prototype.displayDisclosureIcon;
    /** @type {?} */
    AdvancedDataGrid.prototype.displayItemsExpanded;
    /** @type {?} */
    AdvancedDataGrid.prototype.firstVisibleItem;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupIconFunction;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupItemRenderer;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupLabelFunction;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupRowHeight;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupedColumns;
    /** @type {?} */
    AdvancedDataGrid.prototype.hierarchicalCollectionView;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemIcons;
    /** @type {?} */
    AdvancedDataGrid.prototype.lockedColumnCount;
    /** @type {?} */
    AdvancedDataGrid.prototype.lockedRowCount;
    /** @type {?} */
    AdvancedDataGrid.prototype.rendererProviders;
    /** @type {?} */
    AdvancedDataGrid.prototype.selectedCells;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeColumn;
    /** @type {?} */
    AdvancedDataGrid.prototype.AdvancedDataGridHeaders;
    /** @type {?} */
    AdvancedDataGrid.prototype.rowClick;
    /** @type {?} */
    AdvancedDataGrid.prototype.rowDbClick;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemExpand;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemCollapse;
    /** @type {?} */
    AdvancedDataGrid.prototype.activate;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedColumn;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedRow;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedCell;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._treeInstance;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._firstload;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.savedState;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._rowHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.scrollable;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.y;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.tempWidth;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.isInternetExplorer;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._verticalScrollPosition;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._height;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.scrollableContentHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.advancedDatagridContentHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.rowHeight;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemRenderList;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.elemnt;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.commonS;
}
var AdvancedDataGridRow = /** @class */ (function (_super) {
    tslib_1.__extends(AdvancedDataGridRow, _super);
    function AdvancedDataGridRow(adelement, commonServ) {
        var _this = _super.call(this, $(adelement.tr)[0], commonServ) || this;
        _this.adelement = adelement;
        _this.commonServ = commonServ;
        // private variable to store advancedDataGridTree node
        _this.node = null;
        // array to contain the list of cell of this row.
        _this._cellList = [];
        // variable to hold parent
        _this._parent = null;
        // private variable set row state expanded / collapsed
        _this._expanded = false;
        // private variable to know if row is selected or not.
        _this._selected = false;
        // private variable to hold row toolTip.
        _this._toolTip = "";
        _this.itemRenderListFromParent = [];
        // private variable to hold row title
        _this._title = "";
        // private variable to hold row icon
        _this._icon = "";
        // private variable to hold row key
        _this._key = "";
        // private variable to hold row icon type folder.
        _this._folder = false;
        _this.node = adelement;
        _this._rowData = adelement.data;
        _this._title = adelement.title;
        _this._icon = adelement.icon;
        _this._key = _this.adelement.key;
        _this._folder = adelement.folder;
        _this._expanded = adelement.expanded;
        _this._selected = adelement.selected;
        _this._toolTip = adelement.tooltip;
        _this._children = adelement.children;
        return _this;
    }
    Object.defineProperty(AdvancedDataGridRow.prototype, "title", {
        get: /**
         * @return {?}
         */
        function () {
            return this._title;
        },
        /**
         * set advancedDataGridRow title.
         * @param value
         */
        set: /**
         * set advancedDataGridRow title.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._title = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridRow.prototype, "icon", {
        get: /**
         * @return {?}
         */
        function () {
            return this._icon;
        },
        /**
         * set advancedDataGridRow icon.
         * @param value
         */
        set: /**
         * set advancedDataGridRow icon.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._icon = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridRow.prototype, "key", {
        get: /**
         * @return {?}
         */
        function () {
            return this._key;
        },
        /**
         * set advancedDataGridRow key.
         * @param value
         */
        set: /**
         * set advancedDataGridRow key.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._key = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridRow.prototype, "folder", {
        get: /**
         * @return {?}
         */
        function () {
            return this._folder;
        },
        /**
         * set advancedDataGridRow icon type.
         * @param value
         */
        set: /**
         * set advancedDataGridRow icon type.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._folder = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridRow.prototype, "width", {
        /**
         * read only property.
         */
        get: /**
         * read only property.
         * @return {?}
         */
        function () {
            return this._width;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridRow.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value && typeof value === "number") {
                this._height = value;
                this.setStyle("height", value + "px");
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * this method return is the current row is expanded or not.
     */
    /**
     * this method return is the current row is expanded or not.
     * @return {?}
     */
    AdvancedDataGridRow.prototype.isExpanded = /**
     * this method return is the current row is expanded or not.
     * @return {?}
     */
    function () {
        return this._expanded;
    };
    /**
     * This method return if the current row is selected or not.
     */
    /**
     * This method return if the current row is selected or not.
     * @return {?}
     */
    AdvancedDataGridRow.prototype.isSelected = /**
     * This method return if the current row is selected or not.
     * @return {?}
     */
    function () {
        return this._selected;
    };
    /**
     * This method is used to get a given attribute from
     * selected row data.
     * @param attr
     */
    /**
     * This method is used to get a given attribute from
     * selected row data.
     * @param {?} attr
     * @return {?}
     */
    AdvancedDataGridRow.prototype.getDataAttribute = /**
     * This method is used to get a given attribute from
     * selected row data.
     * @param {?} attr
     * @return {?}
     */
    function (attr) {
        return this.getRowData()[attr];
    };
    /**
     * This method is used to create AdvancedDataGridCell
     * Do not use this method because it is used internally.
     */
    /**
     * This method is used to create AdvancedDataGridCell
     * Do not use this method because it is used internally.
     * @param {?} node
     * @param {?} columns
     * @return {?}
     */
    AdvancedDataGridRow.prototype.createCells = /**
     * This method is used to create AdvancedDataGridCell
     * Do not use this method because it is used internally.
     * @param {?} node
     * @param {?} columns
     * @return {?}
     */
    function (node, columns) {
        try {
            /** @type {?} */
            var type;
            /** @type {?} */
            var itemRander;
            /** @type {?} */
            var startCellRenderIndex = 1;
            this.node = node;
            this._rowData = node.data;
            /** @type {?} */
            var tds = $(node.tr).find('>td');
            for (var i = 0; i < tds.length - 1; i++) {
                // create new cell object.
                /** @type {?} */
                var cell = new AdvancedDataGridCell(tds[startCellRenderIndex], this.commonServ);
                // set cell parent row.
                cell.setParentRow(this);
                // set cell column header.
                cell.columnHeader = columns[i];
                // get column type.
                if (columns[i].type) {
                    type = columns[i].type;
                }
                else {
                    this.log.warn("Column shoold contain field type to display renders.");
                }
                // render cells with the specific item render.
                if (type === Types.LINK_NUM) {
                    itemRander = cell.renderCell(LinkItemRander);
                    ((/** @type {?} */ (itemRander))).text = ('' + this.getRowData()[columns[i].dataelement]).replace(new RegExp('\\$', 'g'), '');
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.LINK_NUM;
                }
                else if (type === Types.STR) {
                    itemRander = cell.renderCell(StringItemRender);
                    ((/** @type {?} */ (itemRander))).text = this.getRowData()[columns[i].dataelement];
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.STR;
                }
                else if (type === Types.NUM) {
                    itemRander = cell.renderCell(NumberItemRender);
                    ((/** @type {?} */ (itemRander))).text = ('' + this.getRowData()[columns[i].dataelement]).replace(new RegExp('\\$', 'g'), '');
                    ;
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.NUM;
                }
                else if (type === Types.DATE) {
                }
                else if (type === Types.CHECKBOX) { // checkbox
                }
                else if (type === Types.RADIO) {
                }
                else if (type === Types.COMBO) {
                }
                else if (type === Types.TIME) {
                }
                else if (type === Types.INPUT) {
                }
                this._cellList.push(cell);
                startCellRenderIndex++;
            }
        }
        catch (error) {
            this.log.error("createCells - error: ", error);
        }
    };
    /**
     * This method will return the parent of selected
     * row.
     */
    /**
     * This method will return the parent of selected
     * row.
     * @return {?}
     */
    AdvancedDataGridRow.prototype.getParentItem = /**
     * This method will return the parent of selected
     * row.
     * @return {?}
     */
    function () {
        // limit to break the loop if no parent found.
        /** @type {?} */
        var loopLimit = 20;
        // variable to hold the root node.
        /** @type {?} */
        var rootNode = null;
        // if the node has a parent
        if (this.node.parent) {
            return new AdvancedDataGridRow(this.node.parent, this.commonServ);
        }
        else {
            // search for the root node and return it.
            rootNode = this.node;
            while (loopLimit > 0 && rootNode.title !== "root") {
                loopLimit--;
                rootNode = this.node.parent;
            }
            return new AdvancedDataGridRow(rootNode, this.commonServ);
        }
    };
    /**
     * @return {?}
     */
    AdvancedDataGridRow.prototype.getRowData = /**
     * @return {?}
     */
    function () {
        return this._rowData;
    };
    /**
     * @return {?}
     */
    AdvancedDataGridRow.prototype.getCells = /**
     * @return {?}
     */
    function () {
        try {
            return this._cellList;
        }
        catch (error) {
            this.log.error("getCells - error: ", error);
        }
    };
    /**
     * @param {?} index
     * @return {?}
     */
    AdvancedDataGridRow.prototype.getCellAt = /**
     * @param {?} index
     * @return {?}
     */
    function (index) {
        try {
            return this._cellList[index];
        }
        catch (error) {
            this.log.error("getCellAt - error: ", error);
        }
    };
    /**
     * This method is used to expand the current
     * item.
     */
    /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    AdvancedDataGridRow.prototype.expand = /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    function () {
        try {
            this.node.setExpanded(true);
        }
        catch (error) {
            this.log.error("expand - error: ", error);
        }
    };
    /**
     * This method is used to collapse the
     * current item.
     */
    /**
     * This method is used to collapse the
     * current item.
     * @return {?}
     */
    AdvancedDataGridRow.prototype.collapse = /**
     * This method is used to collapse the
     * current item.
     * @return {?}
     */
    function () {
        try {
            this.node.setExpanded(false);
        }
        catch (error) {
            this.log.error("collapse - error: ", error);
        }
    };
    AdvancedDataGridRow.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    AdvancedDataGridRow.ctorParameters = function () { return [
        { type: undefined },
        { type: CommonService }
    ]; };
    return AdvancedDataGridRow;
}(UIComponent));
export { AdvancedDataGridRow };
if (false) {
    /** @type {?} */
    AdvancedDataGridRow.prototype.node;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._cellList;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._rowData;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._parent;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._expanded;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._children;
    /** @type {?} */
    AdvancedDataGridRow.prototype.itemRenderListFromParent;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._title;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._icon;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._key;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._folder;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._width;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._height;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype.adelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype.commonServ;
}
var AdvancedDataGridCell = /** @class */ (function (_super) {
    tslib_1.__extends(AdvancedDataGridCell, _super);
    function AdvancedDataGridCell(cellelement, comServ) {
        var _this = _super.call(this, cellelement, comServ) || this;
        _this.cellelement = cellelement;
        _this.comServ = comServ;
        // private variable to store the item render instance.
        _this._itemRander = null;
        // private variable to hold cell parent row.
        _this._parentRow = null;
        // private variable to store cell column header.
        _this._columnHeader = null;
        return _this;
    }
    Object.defineProperty(AdvancedDataGridCell.prototype, "columnHeader", {
        /**
         * This method is used to set the cell column header.
         * @param value
         */
        set: /**
         * This method is used to set the cell column header.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._columnHeader = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to return the parent row of the current cell.
     */
    /**
     * This method is used to return the parent row of the current cell.
     * @return {?}
     */
    AdvancedDataGridCell.prototype.getParentRow = /**
     * This method is used to return the parent row of the current cell.
     * @return {?}
     */
    function () {
        return this._parentRow;
    };
    /**
     * This method is used to set parent row for the current cell.
     * @param row
     */
    /**
     * This method is used to set parent row for the current cell.
     * @param {?} row
     * @return {?}
     */
    AdvancedDataGridCell.prototype.setParentRow = /**
     * This method is used to set parent row for the current cell.
     * @param {?} row
     * @return {?}
     */
    function (row) {
        this._parentRow = row;
    };
    /**
     * This method is used to create rander in this
     * grid cell.
     * @param itemRender
     */
    /**
     * This method is used to create rander in this
     * grid cell.
     * @param {?} itemRender
     * @return {?}
     */
    AdvancedDataGridCell.prototype.renderCell = /**
     * This method is used to create rander in this
     * grid cell.
     * @param {?} itemRender
     * @return {?}
     */
    function (itemRender) {
        $(this.cellelement).empty();
        // $(this.cellelement).html(itemRender as any);
        try {
            // Create a component reference from the component
            /** @type {?} */
            var componentRef = this.comServ.componentFactoryResolver
                .resolveComponentFactory((/** @type {?} */ (itemRender)))
                .create(this.comServ.injector);
            // Attach component to the appRef so that it's inside the ng component tree
            this.comServ.applicationRef.attachView(componentRef.hostView);
            // Get DOM element from component
            /** @type {?} */
            var domElem = (/** @type {?} */ (((/** @type {?} */ (componentRef.hostView)))
                .rootNodes[0]));
            // Append DOM element to the body
            // document.body.appendChild(domElem);
            $(this.cellelement).html(domElem);
            this._itemRander = componentRef.instance;
            this._parentRow.itemRenderListFromParent.push(componentRef);
            return componentRef.instance;
        }
        catch (error) {
            this.log.error("renderCell - error: ", error);
        }
    };
    /**
     * This method is used to get the cell item render.
     */
    /**
     * This method is used to get the cell item render.
     * @return {?}
     */
    AdvancedDataGridCell.prototype.getItemRander = /**
     * This method is used to get the cell item render.
     * @return {?}
     */
    function () {
        return this._itemRander;
    };
    /**
     * This method is used to get the cell column header.
     */
    /**
     * This method is used to get the cell column header.
     * @return {?}
     */
    AdvancedDataGridCell.prototype.getColumnHeader = /**
     * This method is used to get the cell column header.
     * @return {?}
     */
    function () {
        return this._columnHeader;
    };
    AdvancedDataGridCell.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    AdvancedDataGridCell.ctorParameters = function () { return [
        { type: undefined },
        { type: CommonService }
    ]; };
    return AdvancedDataGridCell;
}(UIComponent));
export { AdvancedDataGridCell };
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._itemRander;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._parentRow;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._columnHeader;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype.cellelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype.comServ;
}
var AdvancedDataGridColumn = /** @class */ (function (_super) {
    tslib_1.__extends(AdvancedDataGridColumn, _super);
    function AdvancedDataGridColumn(adelement, commonServ) {
        var _this = _super.call(this, adelement, commonServ) || this;
        _this.adelement = adelement;
        _this.commonServ = commonServ;
        // private variable to store column index.
        _this._index = null;
        return _this;
    }
    Object.defineProperty(AdvancedDataGridColumn.prototype, "index", {
        /**
         * This method is used to return the index of column
         * in the advancedDataGrid.
         */
        get: /**
         * This method is used to return the index of column
         * in the advancedDataGrid.
         * @return {?}
         */
        function () {
            return this._index;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedDataGridColumn.prototype, "metaData", {
        /**
         * This method is used to get the metaData of this column.
         */
        get: /**
         * This method is used to get the metaData of this column.
         * @return {?}
         */
        function () {
            return this._metaData;
        },
        /**
         * This method is used to set new metaData to current column.
         * @param value
         */
        set: /**
         * This method is used to set new metaData to current column.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._metaData = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to get the list of cell in the current
     * column.
     */
    /**
     * This method is used to get the list of cell in the current
     * column.
     * @return {?}
     */
    AdvancedDataGridColumn.prototype.getCells = /**
     * This method is used to get the list of cell in the current
     * column.
     * @return {?}
     */
    function () {
        return this._listCell;
    };
    /**
     * This method is used to get the cell in the given index.
     * @param index
     */
    /**
     * This method is used to get the cell in the given index.
     * @param {?} index
     * @return {?}
     */
    AdvancedDataGridColumn.prototype.getCellAt = /**
     * This method is used to get the cell in the given index.
     * @param {?} index
     * @return {?}
     */
    function (index) {
        return this._listCell[index];
    };
    AdvancedDataGridColumn.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    AdvancedDataGridColumn.ctorParameters = function () { return [
        { type: undefined },
        { type: CommonService }
    ]; };
    return AdvancedDataGridColumn;
}(UIComponent));
export { AdvancedDataGridColumn };
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._listCell;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._index;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._metaData;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype.adelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype.commonServ;
}
/**
 * Interface to declare all attribute of advancedDataGrid.
 * @record
 */
export function IAdvancedDataGrid() { }
if (false) {
    /** @type {?} */
    IAdvancedDataGrid.prototype.displayDisclosureIcon;
    /** @type {?} */
    IAdvancedDataGrid.prototype.displayItemsExpanded;
    /** @type {?} */
    IAdvancedDataGrid.prototype.firstVisibleItem;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupedColumns;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupIconFunction;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupItemRenderer;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupLabelFunction;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupRowHeight;
    /** @type {?} */
    IAdvancedDataGrid.prototype.hierarchicalCollectionView;
    /** @type {?} */
    IAdvancedDataGrid.prototype.itemIcons;
    /** @type {?} */
    IAdvancedDataGrid.prototype.lockedColumnCount;
    /** @type {?} */
    IAdvancedDataGrid.prototype.lockedRowCount;
    /** @type {?} */
    IAdvancedDataGrid.prototype.rendererProviders;
    /** @type {?} */
    IAdvancedDataGrid.prototype.selectedCells;
    /** @type {?} */
    IAdvancedDataGrid.prototype.treeColumn;
}
/**
 * This interface define the aAdvancedDataGrid metaData.
 * @record
 */
export function IAdvancedDataGridMetaData() { }
if (false) {
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.dataelement;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.heading;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.type;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.columnorder;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.width;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.draggable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.filterable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.visible;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.visible_default;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.editable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.sort;
}
//# sourceMappingURL=data:application/json;base64,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