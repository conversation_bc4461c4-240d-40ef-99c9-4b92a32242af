/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Directive, ElementRef, Input, Output, EventEmitter } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
/**
 * In order to improve the readability of code on the one hand and to structure the toolbox project,
 * we created a directive that loads the jquery library to do all the manipulations on the DOM
 * and that has the role of managing all that is vusuel.
 * this directive avoids the repetition of the same processing in each component such as width, height, enabled, visible ... etc
 * with this solution the components only contain the getter and setter or / and the inputs.
 * all future components should use this directive to fix the html invalid tags issue generated by angular
 * these tags cause browser compatibility issues also the css does not apply on these tags.
 * Example: if we write <VBox height="100%"> bla..bla.. </VBox> if we inspect the view we find in the DOM this code
 *
 * <vbox ng-reflect-height="100%"> <-- This tag generated by angular cause the problem
 *   <div style="height:100%"> bla..bla</div>
 * </vbox>
 *
 * So the solution is to remove all angular tags in order to obtain a valid HTML tags in the DOM.
 * <AUTHOR>
 * @version 1.0.0
 */
var SwtDOMManager = /** @class */ (function () {
    function SwtDOMManager(element) {
        this.element = element;
        // variable to handle visibility  
        this._visible = true;
        // variable to handle visibility  
        this._enabled = true;
        // private variable to store button mode.
        this._buttonMode = true;
        // private variable to store style name
        this._styleName = "";
        //private variable to store component name.
        this.component = "";
        // private variable to set toolTip to component.
        this._toolTip = "";
        // output to handle click event.
        this.onClick = new EventEmitter();
        // output to handle KeyDown event.
        this.onKeyDown = new EventEmitter();
        // output to handle FocusOut event.
        this.onFocusOut = new EventEmitter();
        // output to handle onKeyUp event.
        this.onKeyUp = new EventEmitter();
        // output to handle mouseOver event.
        this.mouseOver = new EventEmitter();
        // output to handle mouseEnter event.
        this.mouseEnter = new EventEmitter();
        // output to handle mouseLeave event.
        this.mouseLeave = new EventEmitter();
        // output to handle Focus event.
        this.onFocus = new EventEmitter();
        // output to handle mouseDown event.
        this.mouseDown = new EventEmitter();
        // output to handle onmouseUp event.
        this.mouseUp = new EventEmitter();
        // Input to handle component width.
        this.width = "";
        //Input to handle component height.
        this.height = "";
        //Input to handle component styleName.
        this.styleName = "";
        //Input to handle component id.
        this.id = "";
        //Input to handle component paddingTop.
        this.paddingTop = "";
        //Input to handle component paddingRight.
        this.paddingRight = "";
        //Input to handle component paddingBottom.
        this.paddingBottom = "";
        //Input to handle component paddingLeft.
        this.paddingLeft = "";
        //Input to handle component marginTop.
        this.marginTop = "";
        //Input to handle component marginRight.
        this.marginRight = "";
        //Input to handle component marginBottom.
        this.marginBottom = "";
        //Input to handle component marginLeft.
        this.marginLeft = "";
        // Input to handle content align.
        this.horizontalAlign = "";
    }
    Object.defineProperty(SwtDOMManager.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visible;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                if (typeof (value) === "string") {
                    if (value === "false") {
                        _this._visible = false;
                        $(_this.child).hide();
                    }
                    else {
                        _this._visible = true;
                        $(_this.child).show();
                    }
                }
                else {
                    _this._visible = value;
                    if (value) {
                        $(_this.child).show();
                    }
                    else {
                        $(_this.child).hide();
                    }
                }
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDOMManager.prototype, "toolTip", {
        get: /**
         * @return {?}
         */
        function () {
            return this._toolTip;
        },
        //  @Input()
        //  set enabled(value: boolean) {
        //      setTimeout(() => {
        //          console.log('--------------SwtDOMManager--------------')
        //          if (typeof(value) === "string") {
        //              if(value === "false") {
        //                  this._enabled = false;
        //                  this.disableComponent(this.component, false);
        //              } else {
        //                  this._enabled = true;
        //                  this.disableComponent(this.component, true);
        //              }
        //          } else {
        //              this._enabled = value;
        //              if(!value) {
        //                  this.disableComponent(this.component, false);
        //              } else {
        //                  this.disableComponent(this.component, true);
        //              }
        //          }
        //      }, 0);
        //  }
        //  get enabled() {
        //      return this._enabled;
        //  }
        set: 
        //  @Input()
        //  set enabled(value: boolean) {
        //      setTimeout(() => {
        //          console.log('--------------SwtDOMManager--------------')
        //          if (typeof(value) === "string") {
        //              if(value === "false") {
        //                  this._enabled = false;
        //                  this.disableComponent(this.component, false);
        //              } else {
        //                  this._enabled = true;
        //                  this.disableComponent(this.component, true);
        //              }
        //          } else {
        //              this._enabled = value;
        //              if(!value) {
        //                  this.disableComponent(this.component, false);
        //              } else {
        //                  this.disableComponent(this.component, true);
        //              }
        //          }
        //      }, 0);
        //  }
        //  get enabled() {
        //      return this._enabled;
        //  }
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTip = value;
            //   setTimeout(() => {
            //       $(this.element.nativeElement).attr("title",value);
            //   }, 0);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @method ngOnInit.
     */
    /**
     * \@method ngOnInit.
     * @return {?}
     */
    SwtDOMManager.prototype.ngOnInit = /**
     * \@method ngOnInit.
     * @return {?}
     */
    function () {
        try {
            // add toolTip to component
            // $(this.element.nativeElement).tooltip({
            //     position: { my: "left+20 center", at: "right-20 bottom+20" }
            // });
            $(this.element.nativeElement).on("focusout", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
            }));
            // This variable to store parent of current component.
            this.parent = $(this.element.nativeElement).parent()[0];
            // This variable to store the current component DOM.
            this.child = $(this.element.nativeElement)[0];
            // get component name.
            this.component = $(this.parent).prop("tagName");
            // remove angular tag from the DOM.
            $(this.parent).replaceWith(this.child);
            // if width not contain % sign so append px.
            this.width.indexOf("%") === -1 ? this.width += "px" : null;
            // if height not contain % sign so append px.
            this.height.indexOf("%") === -1 ? this.height += "px" : null;
            // set component height.
            $(this.child).height(this.height);
            //set component width
            $(this.child).width(this.width);
            // set component styleName.
            $(this.child).addClass(this.styleName);
            // set component styleName.
            $(this.child).attr("id", this.id);
            // if component is HBOX so display flex.
            if (this.component === "HBOX") {
                //                if(!this.enabled) {
                //                    $(this.child).addClass("disabled-container");
                //                } else {
                //                    $(this.child).removeClass("disabled-container");
                //                }
                // if HBOX is visible display content flex. 
                this.visible === true ? $(this.child).css("display", "flex") : $(this.child).css("display", "none");
                // set HBOX width.
                $(this.child).width(this.width);
                // set VBOX paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set VBOX margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // set horizontal position.
                this.alignContent(this.child, "HBOX", this.horizontalAlign);
            }
            // if component is HBOX so display flex.
            if (this.component === "VBOX") {
                //            if(!this.enabled) {
                //                $(this.child).addClass("disabled-container");
                //            } else {
                //                $(this.child).removeClass("disabled-container");
                //            }
                // set component width
                /** @type {?} */
                var className = $($(this.child).parent()[0]).attr("class");
                if (className) {
                    if (className === "hbox1") {
                        $(this.child).width(this.width).css('width', '+=' + (Number(this.paddingLeft) + Number(this.paddingRight)) + 'px');
                    }
                    else {
                        $(this.child).width(this.width).css('width', '-=' + (Number(this.paddingLeft) + Number(this.paddingRight)) + 'px');
                    }
                }
                // set VBOX paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set VBOX margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // set horizontal position.
                this.alignContent(this.child, "VBOX", this.horizontalAlign);
            }
            if (this.component === "SWTCANVAS") {
                //            if(!this.enabled) {
                //                $(this.child).addClass("disabled-container");
                //            } else {
                //                $(this.child).removeClass("disabled-container");
                //            }
                // set SWTCANVAS paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set SWTCANVAS margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // subtract margins and paddings from width to conserve canvas in the center
                $(this.child).width(this.width)
                    .css('width', '-=' + (Number(this.marginRight) + Number(this.marginLeft) + Number(this.paddingRight) + Number(this.paddingLeft) + 2) + 'px');
            }
            if (this.component === "SWTPANEL") {
                /** @type {?} */
                var panel = $($($(this.child)[0])[0])[0];
                /** @type {?} */
                var panelbody = $($($(this.child)[0])[0]).children()[0];
                /** @type {?} */
                var parent = $($(this.child)[0]).parent()[0];
                /** @type {?} */
                var parentName = $(panel).prop("class");
                // set SWTPANEL paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set SWTPANEL margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // subtract margins and paddings from width to conserve canvas in the center
                $(this.child).width(this.width)
                    .css('width', '-=' + (Number(this.paddingRight) + Number(this.paddingLeft) + Number(this.marginRight) + Number(this.marginLeft)) + 'px');
                /** @type {?} */
                var paddingBottom = $(parent).css("padding-bottom");
                /** @type {?} */
                var paddingTop = $(parent).css("padding-top");
                /** @type {?} */
                var marginBottom = $(parent).css("margin-bottom");
                /** @type {?} */
                var marginTop = $(parent).css("margin-top");
                $(panel).height(this.height)
                    .css('height', '-=' + (Number(this.marginTop) + Number(this.marginBottom)) + 'px')
                    .css("height", "-=" + paddingBottom).css("height", "-=" + paddingTop)
                    .css("height", "-=" + marginBottom)
                    .css("height", "-=" + marginTop);
                $(panelbody).height($(panel).height()).css('height', '-=' + (Number(this.paddingTop) + Number(this.paddingBottom) + 12) + 'px');
                /*
                 * TO DO add case when panel exist in other panel
                 * in the level 3 is not stable
                 * if (parentName === "panelInsideFormLayout") {
                    $(panelbody).height($(panel).height()).css('height', '-='+(Number(this.paddingTop)+Number(this.paddingBottom) + 12)+'px');
                }*/
            }
            if (this.component === "SWTBUTTON") {
                //            $(this.child).height(this.height+"px");
            }
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - ngOnInit method error:", error);
        }
    };
    /**
     * @private
     * @param {?} component
     * @param {?} enable
     * @return {?}
     */
    SwtDOMManager.prototype.disableComponent = /**
     * @private
     * @param {?} component
     * @param {?} enable
     * @return {?}
     */
    function (component, enable) {
        //      if (component === "SWTBUTTON") {
        //          
        //      } else {
        //          if(enable) {
        //              $(this.child).removeClass("disabled-container");
        //          } else {
        //              $(this.child).addClass("disabled-container"); 
        //          }
        //      }
    };
    /**
     * @private
     * @param {?} component
     * @return {?}
     */
    SwtDOMManager.prototype.removeListeners = /**
     * @private
     * @param {?} component
     * @return {?}
     */
    function (component) {
        //      $(component).off();
    };
    /**
     * This method is used to set paddings to component.
     * @param component
     * @param top
     * @param right
     * @param bottom
     * @param left
     */
    /**
     * This method is used to set paddings to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    SwtDOMManager.prototype.setPaddings = /**
     * This method is used to set paddings to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    function (component, top, right, bottom, left) {
        try {
            //          top.indexOf("%") === -1 ? top+= "px":null; 
            //          right.indexOf("%") === -1 ? right+= "px":null; 
            //          bottom.indexOf("%") === -1 ? bottom+= "px":null; 
            //          left.indexOf("%") === -1 ? left+= "px":null;
            //          //set component paddings.
            //          $(component).css("padding-top", top)
            //          .css("padding-right",right)
            //          .css("padding-bottom", bottom)
            //          .css("padding-left",left); 
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - setPaddings method error :", error);
        }
    };
    /**
     * This method is used to set margins to component.
     * @param component
     * @param top
     * @param right
     * @param bottom
     * @param left
     */
    /**
     * This method is used to set margins to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    SwtDOMManager.prototype.setMargins = /**
     * This method is used to set margins to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    function (component, top, right, bottom, left) {
        try {
            //         top.indexOf("%") === -1 ? top+= "px":null; 
            //         right.indexOf("%") === -1 ? right+= "px":null; 
            //         bottom.indexOf("%") === -1 ? bottom+= "px":null; 
            //         left.indexOf("%") === -1 ? left+= "px":null;
            //         //set component margins.
            //         $(component).css("margin-top", top)
            //         .css("margin-right",right)
            //         .css("margin-bottom", bottom)
            //         .css("margin-left",left);
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - setMargins method error :", error);
        }
    };
    /**
     * This method is used to align element content.
     * @param component
     * @param name
     * @param position
     */
    /**
     * This method is used to align element content.
     * @private
     * @param {?} component
     * @param {?} name
     * @param {?} position
     * @return {?}
     */
    SwtDOMManager.prototype.alignContent = /**
     * This method is used to align element content.
     * @private
     * @param {?} component
     * @param {?} name
     * @param {?} position
     * @return {?}
     */
    function (component, name, position) {
        try {
            //        if(name.toUpperCase() === "VBOX") {
            //            if (position.toUpperCase() === "RIGHT") {
            //                position = "flex-end";
            //            }
            //            $(component).css("display", "flex")
            //            .css("align-items",position);
            //        }
            //        if(name.toUpperCase() === "HBOX") {
            //            if (position.toUpperCase() === "RIGHT") {
            //                position = "flex-end";
            //            }
            //            $(component).css("justify-content",position);
            //        }
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - alignContent method error :", error);
        }
    };
    SwtDOMManager.decorators = [
        { type: Directive, args: [{
                    selector: '[SwtDOMManager]'
                },] }
    ];
    /** @nocollapse */
    SwtDOMManager.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    SwtDOMManager.propDecorators = {
        onClick: [{ type: Output, args: ["onClick",] }],
        onKeyDown: [{ type: Output, args: ["onKeyDown",] }],
        onFocusOut: [{ type: Output, args: ["onFocusOut",] }],
        onKeyUp: [{ type: Output, args: ["onKeyUp",] }],
        mouseOver: [{ type: Output, args: ["mouseOver",] }],
        mouseEnter: [{ type: Output, args: ["mouseEnter",] }],
        mouseLeave: [{ type: Output, args: ["mouseLeave",] }],
        onFocus: [{ type: Output, args: ["onFocus",] }],
        mouseDown: [{ type: Output, args: ["mouseDown",] }],
        mouseUp: [{ type: Output, args: ["mouseUp",] }],
        width: [{ type: Input, args: ["width",] }],
        height: [{ type: Input, args: ["height",] }],
        styleName: [{ type: Input, args: ["styleName",] }],
        id: [{ type: Input, args: ["id",] }],
        paddingTop: [{ type: Input, args: ["paddingTop",] }],
        paddingRight: [{ type: Input, args: ["paddingRight",] }],
        paddingBottom: [{ type: Input, args: ["paddingBottom",] }],
        paddingLeft: [{ type: Input, args: ["paddingLeft",] }],
        marginTop: [{ type: Input, args: ["marginTop",] }],
        marginRight: [{ type: Input, args: ["marginRight",] }],
        marginBottom: [{ type: Input, args: ["marginBottom",] }],
        marginLeft: [{ type: Input, args: ["marginLeft",] }],
        horizontalAlign: [{ type: Input, args: ["horizontalAlign",] }],
        visible: [{ type: Input }],
        toolTip: [{ type: Input }]
    };
    return SwtDOMManager;
}());
export { SwtDOMManager };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.child;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._buttonMode;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.component;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._toolTip;
    /** @type {?} */
    SwtDOMManager.prototype.onClick;
    /** @type {?} */
    SwtDOMManager.prototype.onKeyDown;
    /** @type {?} */
    SwtDOMManager.prototype.onFocusOut;
    /** @type {?} */
    SwtDOMManager.prototype.onKeyUp;
    /** @type {?} */
    SwtDOMManager.prototype.mouseOver;
    /** @type {?} */
    SwtDOMManager.prototype.mouseEnter;
    /** @type {?} */
    SwtDOMManager.prototype.mouseLeave;
    /** @type {?} */
    SwtDOMManager.prototype.onFocus;
    /** @type {?} */
    SwtDOMManager.prototype.mouseDown;
    /** @type {?} */
    SwtDOMManager.prototype.mouseUp;
    /** @type {?} */
    SwtDOMManager.prototype.width;
    /** @type {?} */
    SwtDOMManager.prototype.height;
    /** @type {?} */
    SwtDOMManager.prototype.styleName;
    /** @type {?} */
    SwtDOMManager.prototype.id;
    /** @type {?} */
    SwtDOMManager.prototype.paddingTop;
    /** @type {?} */
    SwtDOMManager.prototype.paddingRight;
    /** @type {?} */
    SwtDOMManager.prototype.paddingBottom;
    /** @type {?} */
    SwtDOMManager.prototype.paddingLeft;
    /** @type {?} */
    SwtDOMManager.prototype.marginTop;
    /** @type {?} */
    SwtDOMManager.prototype.marginRight;
    /** @type {?} */
    SwtDOMManager.prototype.marginBottom;
    /** @type {?} */
    SwtDOMManager.prototype.marginLeft;
    /** @type {?} */
    SwtDOMManager.prototype.horizontalAlign;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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