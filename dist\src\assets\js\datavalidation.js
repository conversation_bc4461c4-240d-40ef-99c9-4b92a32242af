// This file contains the data validation JavaScript functions
// It is included in the HTML pages with forms that need these
// data validation routines.
// DEFINE VARIABLES
// whitespace characters
var whitespace = " \t\n\r";
var PatternsDict = {};
PatternsDict.zipPat = /\d{5}(-\d{4})?/; // matches zip codes
PatternsDict.emailPat = /.*@.*\..*/; // matches email addresses
PatternsDict.notEmptyPat = /.{1,}/; // matches at least one character
PatternsDict.numberPat = /^\d*$/; // matches numbers only
PatternsDict.numberPatExpand = /^\d*$/; // matches numbers only, make the number 1 display as 01
PatternsDict.numberPatAll = /^[+|-]?\d*$/; // matches numbers only
PatternsDict.decimalPat = /^\d*(\.\d{1,7})?$/; // matches numbers only
PatternsDict.alphaNumPat = /^[a-zA-Z0-9]+$/; // matches alphanumerics only
PatternsDict.alphaNumPatWithHyphenAndUnderScore = /^[a-zA-Z0-9\-_]+$/; // matches alphanumerics with - and _

PatternsDict.alphaNumPatPassWord= /^[-a-zA-Z0-9.,:;~@\$&"\'<#!\(\)\*\?\[\]%>_+=^\s\|\\+/]+$/;   // matches alphanumerics and the symbols (.,~!@${}&%>/|\\-_+=^;:[]*()?#'<) only
PatternsDict.alphaNumPatExtended = /^[-a-zA-Z0-9.,:;#\(\)\*\?\[\]%&<>_+=^\s\|\\+/]+$/; // matches alphanumerics and the symbols (.,%&<>/|\-_+=^) only
PatternsDict.alphaNumPatExtended_AcctName = /^[-a-zA-Z0-9.,:;#\(\)\*\?\[\]%>_+=^\s\|\\+/]+$/; // matches alphanumerics and the symbols (.,%>/|\\-_+=^;:[]*()?#) only
PatternsDict.ascii_standard = /^[A-Za-z\d !"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~]*$/;//Standard ASCII printable characters between HEX(20) and HEX(7E)
PatternsDict.alphaNumPatWithUppercase = /^[A-Z0-9]+$/; // matches alpha uppercase and numerics only
PatternsDict.alphaNumPatWithSpace = /^[a-zA-Z0-9][a-zA-Z0-9\s\']*$/; // matches alphanumerics with spacesonly
PatternsDict.alphaNumPatWithSpace_IBAN = /^[a-zA-Z0-9][a-zA-Z0-9\s\']*$/; // matches alphanumerics with spacesonly
PatternsDict.pwPat = /^[^!?]\S{5,20}$/; // matches between 4 and 10 characters with non-digit leading

PatternsDict.currencyPat1 = /^\d{1,}(,\d{1,})*(\.\d{1,3})?(M|B|T|m|b|t)?$/; // matches currency with commas
PatternsDict.currencyPat2 = /^\d{1,}(\.\d{1,})*(,\d{1,3})?(M|B|T|m|b|t)?$/; // matches currency with commas
PatternsDict.dateWithTimePat= /^(\d{1,2})(\/)(\d{1,2})\2(\d{4})\s*([01]?\d|2[0-3]):?([0-5]\d):?([0-5]\d)\s*$/;
PatternsDict.timePat = /^([01]?[0-9]|[2][0-3])(:[0-5][0-9])?$/; // matches times
PatternsDict.timePatWithSec = /^([01]?[0-9]|[2][0-3])(:[0-5][0-9])(:[0-5][0-9])?$/; // matches times with seconds
PatternsDict.timePatWithonedigit = /^([01]?[0-9]|[2][0-3])(:[0-5]?[0-9])(:[0-5]?[0-9])$/; // matches times with seconds
PatternsDict.timePatWithSign = /^([+|-]?[01]?[0-9]|[+|-]?[2][0-3])(:[0-5][0-9])?$/; // matches times with sign
PatternsDict.datePat4 = /^(\d{1,2})(\/)(\d{1,2})\2(\d{2}|\d{4})$/; //matches  MM/DD/YY MM/DD/YYYY 
PatternsDict.datePat2 = /^(\d{1,2})(\/)(\d{1,2})\2(\d{4})$/; //matches  MM/DD/YYYY  
PatternsDict.datePat1 = /^(\d{1,2})(\/)(\d{1,2})\2(\d{4})$/; //matches  DD/MM/YYYY 
PatternsDict.datePat3 = /^(\d{1,2})(\/)(\d{1,2})\2(\d{4})$/; //matches  DD/MM/YYYY
//start:code modified by Naseema.sd for Manties 1797:BIC code should allow 8 and 11 caractors
PatternsDict.biccode = /^([A-Z]){4}([A-Z]){2}([0-9A-Z]){2}([0-9A-Z]{3})?$/;
//End:code modified by Naseema.sd for Manties 1797:BIC code should allow 8 and 11 caractors
PatternsDict.alphaNumPatWithUnderScore = /^[_a-zA-Z0-9]+$/; // matches alphanumerics with underscoreonly
PatternsDict.ArchivePassword = /^[\S]*$/; // matches Password without space
PatternsDict.alphaNumPatWithHyphen = /^[-a-zA-Z0-9\+/]+$/; // matches alphanumerics,hyphens and forward slash only
PatternsDict.alphaNumPatWithHyphenForward = /^[-a-zA-Z0-9\s\.\\_\+/]+$/; // matches alphanumerics,hyphens and forward slash only and decimal and backslash
PatternsDict.alphaNumPatWithUnderScoreAndDotAndQuestionmark = /^([a-zA-Z0-9]|[-]|[_]|[.]|[?])*?$/; // Matches alphanumerics, under score and dot
PatternsDict.UnderScoreAndDotPattern = /^[-_.]*?$/; // matches question mark only

PatternsDict.negativeWithDigits = /^[+|-]?\d{0,13}(\.\d{1,7})?$/; // matches numbers only
PatternsDict.decimalPatWithoutNegative = /^\d{0,13}(\.\d{1,7})?$/; // matches numbers with out negative only

var PatternsMetaData = {};
PatternsDict.alphaNumPatWithoutPercentage = /^[a-zA-Z0-9 ][^%]*$/; // matches alphanumerics without percentage
PatternsMetaData.alphaNumPatWithoutPercentage = "string without '%' ";
 
 
PatternsMetaData.alphaNumPatPassWord= 'password ';
  
PatternsMetaData.negativeWithDigits = "Invalid amount: Allowed format is 20 digits with up to 7 digits after the decimal place";
PatternsDict.alphaNumPatWithHyphenOnly = /^[-a-zA-Z0-9]+$/; // matches alphanumerics and hyphens only
PatternsMetaData.alphaNumPatWithHyphenOnly = 'Alphanumeric String With Hyphens';
PatternsDict.alphaNumPatWithHyphenUnderscoreOnly = /^[-_a-zA-Z0-9]+$/; // matches alphanumerics,underscore and hyphens only
PatternsMetaData.alphaNumPatWithHyphenUnderscoreOnly = 'Alphanumeric String With Hyphens and Underscore';
PatternsDict.alphaNumPatWithHyphenUnderscoreWithSpace = /^[-_a-zA-Z0-9\s]+$/; // matches alphanumerics,underscore and hyphens only
PatternsMetaData.alphaNumPatWithHyphenUnderscoreWithSpace = 'Alphanumeric String With Hyphens and Underscore With Spaces';
PatternsDict.alphaNumPatWithoutTilde = /^[^~]*$/;
PatternsMetaData.alphaNumPatWithoutTilde = "string without '~' ";
PatternsMetaData.zipPat = 'Zip Code'; // matches zip codes
PatternsMetaData.emailPat = 'Email'; // matches email addresses
PatternsMetaData.notEmptyPat = 'blank'; // matches at least one character
PatternsMetaData.numberPat = 'integer number'; // matches integer numbers only
PatternsMetaData.numberPatExpand = 'number'; // matches numbers only, make the number 1 display as 01
PatternsMetaData.numberPatAll = 'integer number'; // matches integer numbers only
PatternsMetaData.decimalPat = 'decimal number'; // matches decimal numbers only
PatternsMetaData.alphaNumPat = 'alpha numeric string'; // matches alphanumerics only
PatternsMetaData.alphaNumPatWithUppercase = 'The BIC should be 8, 11 or 12 alphanumeric characters in length (consisting only of uppercase A-Z or a number)'; // matches alpha uppercase and numerics only
PatternsMetaData.alphaNumPatWithSpace = 'name'; // matches alphanumerics only

PatternsMetaData.currencyPat1 = 'amount'; // matches currency with commas
PatternsMetaData.currencyPat2 = 'amount'; // matches currency with commas
PatternsMetaData.timePat = 'time'; // matches times
PatternsMetaData.timePatWithSec = 'time'; // matches times
PatternsMetaData.timePatWithonedigit = 'time'; // matches times
PatternsMetaData.timePatWithSign = 'time';
PatternsMetaData.datePat2 = 'date'; //matches  MM/DD/YYYY 
PatternsMetaData.datePat4 = 'date'; //matches   MM/DD/YY  MM/DD/YYYY  
PatternsMetaData.datePat1 = 'date'; //matches  MM/DD/YYYY  
PatternsMetaData.datePat3 = 'date';
//start:code modified by Naseema.sd for Manties 1797:BIC code should allow 8 and 11 caractors
PatternsMetaData.biccode = 'Warning: The BIC should be 8 or 11 alphanumeric characters in length and be in a valid format. Do you wish to continue?'; // matches alpha uppercase and numerics only
//End:code modified by Naseema.sd for Manties 1797:BIC code should allow 8 and 11 caractors
PatternsMetaData.alphaNumPatWithUnderScore = 'Alphanumeric String With Underscore';
PatternsMetaData.ArchivePassword = 'Archive Password';
PatternsMetaData.alphaNumPatWithHyphen = 'Alphanumeric String With Hyphens And Forward Slash';
PatternsMetaData.alphaNumPatWithHyphenForward = 'Alphanumeric String With Hyphens,Forward and Backward Slash , Decimal Point and Underscore';
PatternsDict.alphaNumPatWithHyphenUnderscoreWithSpace = /^[-_a-zA-Z0-9\s]+$/; // matches alphanumerics,underscore and hyphens only
PatternsMetaData.alphaNumPatWithHyphenUnderscoreWithSpace = 'Alphanumeric String With Hyphens and Underscore';
PatternsMetaData.alphaNumPatWithUnderScoreAndDotAndQuestionmark = 'Alphanumeric String With UnderScore,Hyphen,Dot and Question Mark';
PatternsMetaData.alphaNumPatExtended_AcctName = 'character. Only alphanumeric characters and the symbols (.,%>/|\\-_+=^;:[]*()?#) are accepted';
PatternsMetaData.alphaNumPatWithSpace_IBAN = 'character. Only alphanumeric characters and space ( /^[a-zA-Z0-9][a-zA-Z0-9\s\']*$/ ) are accepted.';
PatternsMetaData.ascii_standard = 'character. Only alphanumeric characters and the symbols ( !"#$%&\'()*+,-/.\:;<=>?@[]^_`{|}~ ) are accepted';
PatternsMetaData.alphaNumPatWithHyphenAndUnderScore = 'alphanumeric value, the underscore and the hypen characters are allowed';

PatternsDict.timePat1=/^\s*([01]?\d|2[0-3]):?([0-5]\d)\s*$/;
PatternsDict.timePat2=/^\s*([01]?\d|2[0-3]):?([0-5]\d):?([0-5]\d)\s*$/;
PatternsDict.timePat3=/^\s*([01]?\d|2[0-3])\s*$/;

function validate(elArr) {
   var v = "notEmptyPat";
   var thePat = PatternsDict[v];
   for (var i = 0; i < elArr.length; i++)
   with(elArr[i]) {
      var gotIt = thePat.exec(elArr[i].value);
      if (!gotIt) {
         var returnStr;
         readName = elArr[i].name;
         returnStr = "Please fill all mandatory fields (marked with '*')";
         alert(returnStr);
         if(readName!='')
         document.getElementById(readName).focus;
         return false;
      }
   }
   return true;
}

function validateField(strField, strLabel, strPat, maxValue, minValue) {
  if (isCancelorCloseButtonPressed() == false) {
      var thePat = PatternsDict[strPat];
      if (strField.value.length > 0) {
         var gotIt = thePat.exec(strField.value);
         if (strPat == "alphaNumPatWithSpace") {
            // A message is given if only blank spaces are entered, as only Spaces are allowed 
            if (strField.value.trim().length == 0) {
               returnStr = "Please enter a valid " + PatternsMetaData[strPat];
               alert(returnStr);
               if(window.event)
            	   window.event.returnValue = false;
               strField.focus();
               return false;
            }
            //Condition Added to check the names contains only Alpha numeric with spaces
            if (!gotIt) {
               returnStr = "Please enter a valid " + PatternsMetaData[strPat];
               alert(returnStr);
               if(window.event)
            	   window.event.returnValue = false;
               strField.focus();
               return false;
            }
         }
         else if (strPat == "alphaNumPatWithUnderScoreAndDotAndQuestionmark") {
            // A message is given if only blank spaces are entered, as only Spaces are allowed 
            if (strField.value.trim().length == 0) {
               returnStr = "Please enter a valid " + PatternsMetaData[strPat];
               alert(returnStr);
               if(window.event)
            	   window.event.returnValue = false;
               strField.focus();
               return false;
            }
            // This  pattern is used to provide alert only for combination of dot and underscore only
            thePat = PatternsDict['UnderScoreAndDotPattern'];
            gotIt = thePat.exec(strField.value);
            if (gotIt) {
               returnStr = "Please enter a valid " + PatternsMetaData[strPat];
               alert(returnStr);
               if(window.event)
            	   window.event.returnValue = false;
               strField.focus();
               return false;
            }
            else {
               //Condition Added to check the names contains only Alpha numeric with spaces
               thePat = PatternsDict['alphaNumPatWithUnderScoreAndDotAndQuestionmark'];
               gotIt = thePat.exec(strField.value);
               if (!gotIt) {
                  returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                  alert(returnStr);
                  if(window.event)
                	  window.event.returnValue = false;
                  strField.focus();
                  return false;
               }
            }
         }
        
         if (strPat == "alphaNumPatWithHyphenAndUnderScore") {
        	 if (strField.value.trim().length == 0) {
                 returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                 alert(returnStr);
                 window.event.returnValue = false;
                 strField.focus();
                 return false;
             }
        	 thePat = PatternsDict['alphaNumPatWithHyphenAndUnderScore'];
             gotIt = thePat.exec(strField.value);
             if (!gotIt) {
            	 returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                 alert(returnStr);
                 window.event.returnValue = false;
                 strField.focus();
                 return false;
             }
         }
        
          if (strPat == "biccode") {
            if (!gotIt) {
            
              returnStr = PatternsMetaData[strPat];
              //confirm message to get the conformation for BIC code from the user
         var resultFlag=ShowErrMsgWindowWithBtn('Microsoft Internet Explorer',returnStr,YES_NO);
		        if(resultFlag==6)
			   {
		        if(window.event)
		        		window.event.returnValue = false;         
			    strField.focus();
			   }
			    
			   			     
               return false;
            }
         }
         
               
         if (strPat == "alphaNumPatExtended") {
            if (!gotIt) {
            	if(window.event)
            		window.event.returnValue = false;
               strField.focus();
               return false;
            }
         }
         if (strPat != "alphaNumPatWithSpace") {
            if (!gotIt) {
               var returnStr;
               if (strPat == "alphaNumPatWithUppercase") {
                  returnStr = PatternsMetaData[strPat];
                  if(window.event)
                	  window.event.returnValue = true;
               }
               
               //Condition Checks for negative with digits and without negative
               else if (strPat == "negativeWithDigits" || strPat == "decimalPatWithoutNegative") {
                  returnStr = PatternsMetaData["negativeWithDigits"];
                  if(window.event)
                	  window.event.returnValue = true;
               }
               else if (strPat=="alphaNumPatPassWord" ) {
                  returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                  if(window.event)
                	  window.event.returnValue = true;
               }
              
               else {
                  returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                  if(window.event)
                	  window.event.returnValue = false;
               }
               alert(returnStr);               
               strField.focus();
               return false;
            }
         }
         if (strPat == "numberPat" || strPat == "numberPatAll" || strPat == "numberPatExpand") {
            var isValidValue = true;
            if (maxValue != undefined && maxValue != 'MAX') {
               var obj = new MaximumValue(strField, maxValue);
               isValidValue = obj.isValidValue();
            }
            if (isValidValue && minValue != undefined && minValue != 'MIN') {
               var obj = new MinimumValue(strField, minValue);
               isValidValue = obj.isValidValue();
            }
            if (isValidValue && strPat == "numberPatExpand") {
               strField.onblur = expandTimeDigit;
            }
            return isValidValue;
         } // end numberPat 	
         else if (thePat == PatternsDict.datePat4 || thePat == PatternsDict.datePat2) {
            month = gotIt[1]; // parse date into variables
            day = gotIt[3];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Month must be between 1 and 12.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Day must be between 1 and 31.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Invalid day for this month");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("Invalid day for this month");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat 
         else if (thePat == PatternsDict.datePat1) {
            month = gotIt[3]; // parse date into variables
            day = gotIt[1];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Month must be between 1 and 12.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Day must be between 1 and 31.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Invalid day for this month");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("February " + year + " doesn't have " + day + " days!");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat 
         else if (thePat == PatternsDict.datePat3) {
            month = gotIt[3]; // parse date into variables
            day = gotIt[1];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("Date must be in the format DD/MM/YYYY");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat 
         else if (thePat == PatternsDict.timePat) {
            strField.onblur = expandTimeValue;
         } // end timePat
         else if (thePat == PatternsDict.timePatWithSign) {
            strField.onblur = expandTimeSignValue;
         } // end timePat
      } // end value not blank
  } // end cancelclosebutton
   return true;
}

function validateDateField(strField, strLabel, strPat, maxValue, minValue) {
   if (isCancelorCloseButtonPressed() == false) {
      var thePat = PatternsDict[strPat];
      if (strField.value.length > 0) {
         var gotIt = thePat.exec(strField.value);
         if (strPat != "alphaNumPatWithSpace" ) {
            if (!gotIt) {
               var returnStr;
               returnStr = "Please enter a valid " + PatternsMetaData[strPat];
               alert(returnStr);
               if(window.event)
            	   window.event.returnValue = false;
               strField.focus();
               return false;
            }
         }
         if (strPat == "numberPat" || strPat == "numberPatAll" || strPat == "numberPatExpand") {
            var isValidValue = true;
            if (maxValue != undefined && maxValue != 'MAX') {
               var obj = new MaximumValue(strField, maxValue);
               isValidValue = obj.isValidValue();
            }
            if (isValidValue && minValue != undefined && minValue != 'MIN') {
               var obj = new MinimumValue(strField, minValue);
               isValidValue = obj.isValidValue();
            }
            if (isValidValue && strPat == "numberPatExpand") {
               strField.onblur = expandTimeDigit;
            }
            return isValidValue;
         } // end numberPat 	
         else if (thePat == PatternsDict.datePat4 || thePat == PatternsDict.datePat2) {
            month = gotIt[1]; // parse date into variables
            day  = gotIt[3];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Month must be between 1 and 12.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Day must be between 1 and 31.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Invalid day for this month");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("Invalid day for this month");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat 
         else if (thePat == PatternsDict.datePat1) {
            month = gotIt[3]; // parse date into variables
            day = gotIt[1];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Month must be between 1 and 12.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Day must be between 1 and 31.");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Invalid day for this month");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("February " + year + " doesn't have " + day + " days!");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat 
         else if (thePat == PatternsDict.datePat3) {
            month = gotIt[3]; // parse date into variables
            day = gotIt[1];
            year = gotIt[4];
            if (month < 1 || month > 12) { // check month range
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (day < 1 || day > 31) {
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
               alert("Date must be in the format DD/MM/YYYY");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            if (month == 2) { // check for february 29th
               var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
               if (day > 29 || (day == 29 && !isleap)) {
                  alert("Date must be in the format DD/MM/YYYY");
                  strField.focus();
                  if(window.event)
                	  window.event.returnValue = false;
                  return false;
               }
            }
            if (year == "0000") {
               alert("Invalid year");
               strField.focus();
               if(window.event)
            	   window.event.returnValue = false;
               return false;
            }
            return true; // date is valid
         } // end datePat
         else if (thePat == PatternsDict.timePat) {
            strField.onblur = expandTimeValue;
         } // end timePat
         else if (thePat == PatternsDict.timePatWithSign) {
            strField.onblur = expandTimeSignValue;
         } // end timePat
      } // end value not blank
      else {
         var returnStr;
         returnStr = "Please enter a valid " + PatternsMetaData[strPat];
         alert(returnStr);
         if(window.event)
        	 window.event.returnValue = false;
         strField.focus();
         return false;
      }
   } // end cancelclosebutton
   return true;
}

/**
* this method is used to change the currency decimial places in  validate currency.
* @strField
* @ strLabel
* @ strPat
* @currCode
* @retutn true.
*/

function validateCurrencyPlaces(strField, strPat, currCode) {
        //thepat variable holds the regular expression
        var thePat = PatternsDict[strPat];
        // To Check the decimal seperator
        var decimalChar = '.';
        // the amountvalue values to gotIt
        var gotIt = thePat.exec(strField.text);
        // Get the text field value
        var strVal = strField.text;
        // Checks the pattern and assign the decimal seperator
        if (thePat == PatternsDict.currencyPat2) {
                decimalChar = ',';
        }
        //declare the strDecimals is 2. 
        var strDecimals = '2';
        // declare the boolean var for execution check
        var execFlag = false;
        // check the currencycode 
        if (currCode != 'undefined' && currCode != null) {
                //currCode assigns strDecimals
                if (typeof getCurrencyDecimal === 'function')  { 
                  strDecimals = getCurrencyDecimal(currCode);
                  //if strDecimals gretaer than 3
                  if (strDecimals > 3) strDecimals = 3;
               }else {
                  strDecimals = 2;
               }
        }
        // Checks the currency abrreviation
        if (((strVal.charAt(strVal.length - 1)).toUpperCase() == 'M') || ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'B') || ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'T')) {
                strField.onblur = "";

        }
        else {
                // Gets the decimal seperation position
                dotPos = strVal.indexOf(decimalChar);
                var gotSub = thePat.exec(strVal.substring(0, dotPos));
                // Checks the currCode and decimal postion and decimal digits	
                if (currCode != null && dotPos != -1 && gotSub) {
                        //variable for strDecimalValue
                        var strDecimalValue = strVal.substring(dotPos + 1);
                        //to get the decimal length
                        var declen = strDecimalValue.length;
                        //Checks for zero decimal places 
                        if (strDecimals == 0) {
                                // Checks and get the integer part of the currency
                                if (strVal.indexOf(decimalChar)) {
                                        strVal = strVal.substring(0, strVal.indexOf(decimalChar));
                                }
                                else {
                                        strVal = strVal.substring(0, strVal.length);
                                }
                                strField.text = strVal;
                        }
                        // checks the decimal value greater than strDecimals
                        else if (strDecimalValue.length > strDecimals) {
                                // remove decimal palces
                                while (declen > strDecimals) {
                                        strVal = strVal.substring(0, strVal.length - 1);
                                        declen--;
                                } //to get the amount value
                                strField.text = strVal;

                        }
                        else if (strDecimalValue.length < strDecimals) {
                                // add decimal places
                                while (declen < strDecimals) {
                                        strVal = strVal + "0";
                                        declen++;
                                } //to get the amount value
                                strField.text = strVal;
                        }
                        execFlag = true;
                }
                else {
                        strField.onblur = "";
                }
        }
        // Execute the RegEx		
        gotIt = thePat.exec(strField.text);
        if (strVal.length > 0) {
                //Condition Added to check the names contains only Alpha numeric with spaces
                if (!gotIt) {
                        //variable holds returnstr
                        var returnStr;
                        returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                        //alert shows a popupmessage
                        //alert(returnStr);
                        if(window.event)
                        	window.event.returnValue = false;
                        strField.focus();
                        return false;
                }
                else {
                        //check for integer part max 18, decimal part max 3
                        var strValNew = String(expandMBTValue(strField, thePat, strDecimals, strVal));
                        var strInt = strValNew.length;
                        var strDec = 0;
                        dotPos = strValNew.indexOf('.');
                        if (dotPos != -1) {
                                strInt = strValNew.substring(0, dotPos);
                                strValNew = strInt;
                                //to get the value length
                                strInt = strInt.length;
                                strDec = strValNew.substring(dotPos + 1, strValNew.length);
                                strDec = strDec.length;
                        }

                        // check the valid length
                        if (strInt > 18) {
                                returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                                //alert shows a popupmessage
                                //alert(returnStr);
                                if(window.event)
                                	window.event.returnValue = false;
                                strField.focus();
                                return false;
                        }
                        execFlag = true;

                }

        }
		// Checks exec flag true
        if (execFlag) {
				// Sets the pattern to Check
                strField.thePat = PatternsDict[strPat];
				// Sets the strDecimals to Check
                strField.strDecimals = strDecimals;
                //assign the str value to text
                strField.text = strVal;
				// Calls the function on expandCurrencyValue
                strField.onblur = expandCurrencyValue;
				//Checks the String length
                if (strVal.length > 0) {
						// Expand the abbreviation
                        var strVal1 = String(expandMBTValue(strField, thePat, strDecimals, strVal));
						// Adds the Zeros,Commas and dots(.)
                        strField.text = insertChars(strVal1, strDecimals, thePat);
                }
        }
        console.log('strField.text', strField.text);
        return true;
}
// added to check amount as a string not as a field
function checkCurrencyPlaces(strVal, strPat, currCode) {
   //thepat variable holds the regular expression
   var thePat = PatternsDict[strPat];
   // To Check the decimal seperator
   var decimalChar = '.';
   // the amountvalue values to gotIt
   var gotIt = thePat.exec(strVal);
   // Get the text field value
   //var strVal = strField.text;
   // Checks the pattern and assign the decimal seperator
   if (thePat == PatternsDict.currencyPat2) {
           decimalChar = ',';
   }
   //declare the strDecimals is 2. 
   var strDecimals = '2';
   // declare the boolean var for execution check
   var execFlag = false;
   // check the currencycode 
   if (currCode != 'undefined' && currCode != null) {
           //currCode assigns strDecimals
           if (typeof getCurrencyDecimal === 'function')  { 
             strDecimals = getCurrencyDecimal(currCode);
             //if strDecimals gretaer than 3
             if (strDecimals > 3) strDecimals = 3;
          }else {
             strDecimals = 2;
          }
   }
   // Checks the currency abrreviation
   if (((strVal.charAt(strVal.length - 1)).toUpperCase() == 'M') || ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'B') || ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'T')) {
      strVal.onblur = "";

   }
   else {
           // Gets the decimal seperation position
           dotPos = strVal.indexOf(decimalChar);
           var gotSub = thePat.exec(strVal.substring(0, dotPos));
           // Checks the currCode and decimal postion and decimal digits	
           if (currCode != null && dotPos != -1 && gotSub) {
                   //variable for strDecimalValue
                   var strDecimalValue = strVal.substring(dotPos + 1);
                   //to get the decimal length
                   var declen = strDecimalValue.length;
                   //Checks for zero decimal places 
                   if (strDecimals == 0) {
                           // Checks and get the integer part of the currency
                           if (strVal.indexOf(decimalChar)) {
                                   strVal = strVal.substring(0, strVal.indexOf(decimalChar));
                           }
                           else {
                                   strVal = strVal.substring(0, strVal.length);
                           }
                           //strField.text = strVal;
                   }
                   // checks the decimal value greater than strDecimals
                   else if (strDecimalValue.length > strDecimals) {
                           // remove decimal palces
                           while (declen > strDecimals) {
                                   strVal = strVal.substring(0, strVal.length - 1);
                                   declen--;
                           } //to get the amount value
                           //strField.text = strVal;

                   }
                   else if (strDecimalValue.length < strDecimals) {
                           // add decimal places
                           while (declen < strDecimals) {
                                   strVal = strVal + "0";
                                   declen++;
                           } //to get the amount value
                           //strField.text = strVal;
                   }
                   execFlag = true;
           }
           else {
            strVal.onblur = "";
           }
   }
   // Execute the RegEx		
   gotIt = thePat.exec(strVal);
   if (strVal.length > 0) {
           //Condition Added to check the names contains only Alpha numeric with spaces
           if (!gotIt) {
                   //variable holds returnstr
                   var returnStr;
                   returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                   //alert shows a popupmessage
                   //alert(returnStr);
                   if(window.event)
                      window.event.returnValue = false;
                      //strVal.focus();
                   return false;
           }
           else {
                   //check for integer part max 18, decimal part max 3
                   var strValNew = String(expandMBTValue1(thePat,strVal));
                   var strInt = strValNew.length;
                   var strDec = 0;
                   dotPos = strValNew.indexOf('.');
                   if (dotPos != -1) {
                           strInt = strValNew.substring(0, dotPos);
                           strValNew = strInt;
                           //to get the value length
                           strInt = strInt.length;
                           strDec = strValNew.substring(dotPos + 1, strValNew.length);
                           strDec = strDec.length;
                   }

                   // check the valid length
                   if (strInt > 18) {
                           returnStr = "Please enter a valid " + PatternsMetaData[strPat];
                           //alert shows a popupmessage
                           //alert(returnStr);
                           if(window.event)
                              window.event.returnValue = false;
                              //strVal.focus();
                           return false;
                   }
                   execFlag = true;

           }

   }
 // Checks exec flag true
   if (execFlag) {
       // Sets the pattern to Check
       strVal.thePat = PatternsDict[strPat];
       // Sets the strDecimals to Check
       strVal.strDecimals = strDecimals;
           //assign the str value to text
          // strField.text = strVal;
       // Calls the function on expandCurrencyValue
       strVal.onblur = expandCurrencyValue;
       //Checks the String length
           if (strVal.length > 0) {
             // Expand the abbreviation
                   var strVal1 = String(expandMBTValue1(thePat, strVal));
             // Adds the Zeros,Commas and dots(.)
             strVal = insertChars(strVal1, strDecimals, thePat);
           }
   }
   return true;
}

function validateNegativeCurrencyPlaces(strField, strLabel, strPat, currCode) {
   if (isCancelorCloseButtonPressed() == false) {
      var thePat = PatternsDict[strPat];
      var tempAmtStr = String(strField.value);
      var gotIt = thePat.exec(tempAmtStr.substring(1, tempAmtStr.length));
      var strVal = strField.value;
      var strDecimals = '2';
      if (currCode != 'undefined' && currCode != null) {
         strDecimals = getCurrencyDecimal(currCode);
      }
      if (strVal.length > 0) {
         if (!gotIt) {
            var returnStr;
            returnStr = "Please enter a valid " + PatternsMetaData[strPat];
            //alert(returnStr);
            if(window.event)
            	window.event.returnValue = false;
            strField.focus();
            return false;
         }
         //check for integer part max 18, decimal part max 4
         var strValNew = String(expandNegativeMBTValue(strField, thePat, strDecimals, strVal));
         var strInt = strValNew.length;
         var strDec = 0;
         dotPos = strValNew.indexOf('.');
         if (dotPos != -1) {
            strInt = strValNew.substring(0, dotPos);
            strInt = strInt.length;
            strDec = strValNew.substring(dotPos + 1, strValNew.length);
            strDec = strDec.length;
         }
		 
		 // check the valid length
         if (strInt > 18 || strDec > 3) {
		 
		    returnStr = "Please enter a valid " + PatternsMetaData[strPat];
            //alert(returnStr);
            if(window.event)
            	window.event.returnValue = false;
            strField.focus();
            return false;
         }
         strField.onblur = expandNegativeCurrencyValue;
         strField.thePat = PatternsDict[strPat];
         strField.strDecimals = strDecimals;
      }
   }
   return true;
}
function validateCurrency(strField, strLabel, strPat, currCode) {
   return validateCurrencyPlaces(strField, strLabel, strPat, currCode);
}

function expandMBTValue(strField, thePat, strDecimals, strVal1) {
   var strVal = strVal1;
   if (thePat == PatternsDict.currencyPat2) {
      strVal = replace(strVal, '.', '');
      strVal = replace(strVal, ',', '.');
   }
   strVal = replace(strVal, ',', '');
   if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'M') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'B') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'T') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000;
   }
   return strVal;
}

function expandMBTValue1(thePat, strVal1) {
   var strVal = strVal1;
   if (thePat == PatternsDict.currencyPat2) {
      strVal = replace(strVal, '.', '');
      strVal = replace(strVal, ',', '.');
   }
   strVal = replace(strVal, ',', '');
   if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'M') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'B') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'T') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000;
   }
   return strVal;
}

function expandNegativeMBTValue(strField, thePat, strDecimals, strVal1) {
   var strVal = strVal1;
   if (thePat == PatternsDict.currencyPat2) {
      strVal = replace(strVal, '.', '');
      strVal = replace(strVal, ',', '.');
   }
   strVal = replace(strVal, ',', '');
   if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'M') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'B') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000000000;
   }
   else if ((strVal.charAt(strVal.length - 1)).toUpperCase() == 'T') {
      strVal = strVal.substr(0, strVal.length - 1);
      strVal = strVal * 1000;
   }
   return strVal;
}

function expandCurrencyValue(event) {
   var event = (window.event || event); 
   strField = (event.srcElement||event.target);
   thePat = strField.thePat;
   strDecimals = strField.strDecimals;
   strVal = strField.value;   
   if (strVal.length > 0) {
      var strVal1 = String(expandMBTValue(strField, thePat, strDecimals, strVal));
      strField.value = insertChars(strVal1, strDecimals, thePat);
   }
}

function expandNegativeCurrencyValue(e) {
   var event = (window.event|| e);
   var target = (event.srcElement || event.target);
   strField = target;
   thePat = strField.thePat;
   strDecimals = strField.strDecimals;
   strVal = strField.value;
   if (strVal.length > 0) {
      var strVal1 = String(expandNegativeMBTValue(strField, thePat, strDecimals, strVal));
      strField.value = insertNegativeChars(strVal1, strDecimals, thePat);
   }
}

function replace(haystack, oldNeedle, newNeedle) {
   i = haystack.indexOf(oldNeedle);
   r = "";
   if (i == -1) return haystack;
   r += haystack.substring(0, i) + newNeedle;
   if (i + oldNeedle.length < haystack.length) r += replace(haystack.substring(i + oldNeedle.length, haystack.length), oldNeedle, newNeedle);
   return r;
}

function insertChars(strVal, strDecimals, thePat) {
   strVal = round_decimals(strVal, strDecimals);
   var objRegExp = /-?[0-9]+\.[0-9]{1,6}$/;
   objRegExp.compile('^-');
   strVal = addCommas(strVal);
   if (objRegExp.test(strVal)) {
      strVal = strVal.replace(objRegExp, '');
   }
   if (thePat == PatternsDict.currencyPat2) {
      strVal = replace(strVal, '.', '^');
      strVal = replace(strVal, ',', '.');
      strVal = replace(strVal, '^', ',');
   }
   return strVal;
}

function insertNegativeChars(strVal, strDecimals, thePat) {
   strVal = round_decimals(strVal, strDecimals);
   var objRegExp = /-?[0-9]+\.[0-9]{1,6}$/;
   objRegExp.compile('^-');
   strVal = addCommas(strVal);
   if (objRegExp.test(strVal)) {
      strVal = strVal.replace(objRegExp, '');
   }
   if (thePat == PatternsDict.currencyPat2) {
      strVal = replace(strVal, '.', '^');
      strVal = replace(strVal, ',', '.');
      strVal = replace(strVal, '^', ',');
   }
   strVal = "-" + strVal;
   return strVal;
}

function round_decimals(original_number, decimals) {
   var dotPos = original_number.indexOf('.');
   if (dotPos != -1) {
      strDecimal = original_number.substring(dotPos + 1, original_number.length);
      if (strDecimal.length > decimals) {
         diff = strDecimal.length - decimals;
         original_number = original_number.substring(0, original_number.length - diff);
      }
   }
   return pad_with_zeros(original_number, decimals);
}

function pad_with_zeros(rounded_value, decimal_places) {
   // Convert the number to a string
   var value_string = rounded_value.toString();
   // Locate the decimal point
   var decimal_location = value_string.indexOf(".");
   // Is there a decimal point?
   if (decimal_location == -1) {
      // If no, then all decimal places will be padded with 0s
      decimal_part_length = 0;
      // If decimal_places is greater than zero, tack on a decimal point
      value_string += decimal_places > 0 ? "." : "";
   }
   else {
      // If yes, then only the extra decimal places will be padded with 0s
      decimal_part_length = value_string.length - decimal_location - 1;
   }
   // Calculate the number of decimal places that need to be padded with 0s
   var pad_total = decimal_places - decimal_part_length;
   if (pad_total > 0) {
      // Pad the string with 0s
      for (var counter = 1; counter <= pad_total; counter++)
      value_string += "0";
   }
   return value_string;
}

/************************************************
DESCRIPTION: Inserts commas into numeric string.
PARAMETERS:
  strValue - source string containing commas.
RETURNS: String modified with comma grouping if
  source was all numeric, otherwise source is
  returned.
REMARKS: Used with integers or numbers with
  0 or more decimal places.
*************************************************/
function addCommas(strValue) {
   var objRegExp = new RegExp('(-?[0-9]+)([0-9]{3})');
   var decimal = "";
   // Checks for decimal seperator available or not
   if(strValue.indexOf('.')>0)
   {
		// Gets the decimal part of the currency
		decimal = strValue.substring(strValue.indexOf('.'), strValue.length);
		// Gets the integer part of the currency
		strValue = strValue.substring(0, strValue.indexOf('.'));
		
   }   
   //check for match to search criteria
   while (objRegExp.test(strValue)) {   
      //replace original string with first group match,
      //a comma, then second group match
      strValue = strValue.replace(objRegExp, '$1,$2');
   }
   //Concats the integer part of the currency and decimal part of the currency
   strValue = strValue + decimal;
   return strValue;
}

function trim(argvalue) {
   var tmpstr = ltrim(argvalue);
   return rtrim(tmpstr);
}

function ltrim(argvalue) {
   while (1) {
      if (argvalue.substring(0, 1) != " ") break;
      argvalue = argvalue.substring(1, argvalue.length);
   }
   return argvalue;
}

function rtrim(argvalue) {
   while (1) {
      if (argvalue.substring(argvalue.length - 1, argvalue.length) != " ") break;
      argvalue = argvalue.substring(0, argvalue.length - 1);
   }
   return argvalue;
}

function MinimumValue(element, minval) {
   this.element = element;
   this.minval = minval;
}
MinimumValue.prototype.isValidValue = function () {
   var retValue = false;
   if (this.element.value != 'undefined' && this.minval != 'undefined') {
      var numberObject = Number(this.element.value);
      if (numberObject) {
         if (numberObject.valueOf() >= Number(this.minval).valueOf()) retValue = true;
      }
   }
   if (retValue == false) {
      alert("This field should have minumum value " + this.minval + ".");
      this.element.focus();
   }
   return retValue;
};

function MaximumValue(element, maxval) {
   this.element = element;
   this.maxval = maxval;
}
MaximumValue.prototype.isValidValue = function () {
   var retValue = false;
   if (this.element.value != 'undefined' && this.maxval != 'undefined') {
      var numberObject = Number(this.element.value);
      if (numberObject) {
         if (numberObject.valueOf() <= Number(this.maxval).valueOf()) retValue = true;
      }
   }
   if (retValue == false) {
      alert("This field has a maximum value of " + this.maxval + ".");
      this.element.focus();
   }
   return retValue;
};

function isPointListInRect(textrect, x, y) {
   if (x >= textrect.left && x <= textrect.right && y >= textrect.top && y <= textrect.bottom) {
      return true;
   }
   else return false;
}
/**
* isCancelorCloseButtonPressed
* 			
* This method is used to prevent validation while clicking close/cancel button  
*/
function isCancelorCloseButtonPressed(){	
	var bodyrect = window.document.body.getClientRects()[0];
	if(window.event== undefined || window.event.y < 0 || window.event.x > bodyrect.right)
	return false;
	var retValue = false;
	if(typeof  cancelcloseElements != 'undefined'){
		var length = cancelcloseElements.length;
		for(var idx = 0; idx < length ; ++idx){
			var element = document.getElementById(cancelcloseElements[idx]);
			if(typeof element ==  'undefined' || element == null )
				continue;
			var textrect = element.getClientRects()[0];
			retValue = isPointListInRect(textrect,window.event.x, window.event.y);
			if(retValue == true){
				break;
			}
		}
	}
	return retValue;
}

function expandTimeValue(e) {
   var event = (window.event|| e);
   var target = (event.srcElement || event.target);
   var strField = target;
   var strValue = strField.value;
   var sign = '+';
   var indexSrt = 0;
   var indexEnd = 0;
   var hours;
   var minutes = 0;
   if (strValue.length > 0) {
      indexEnd = strValue.indexOf(':');
      if (indexEnd == -1) {
         indexEnd = strValue.length;
         minutes = '00';
      }
      else {
         minutes = strValue.substring(indexEnd + 1, strValue.length);
      }
      hours = strValue.substring(indexSrt, indexEnd);
      hours = hours.replace(/\b(\d)\b/g, '0$1');
      strValue = hours + ':' + minutes;
      strField.value = strValue;
   }
}

function expandTimeSignValue() {
   var event = (window.event|| e);
   var target = (event.srcElement || event.target);
   var strField = target;
   var strValue = strField.value;
   var sign = '+';
   var indexSrt = 0;
   var indexEnd = 0;
   var hours;
   var minutes = 0;
   if (strValue.length > 0) {
      if (strValue.indexOf('-') != -1) {
         sign = strValue.charAt(0);
      }
      if (strValue.indexOf(sign) != -1) {
         indexSrt = strValue.indexOf(sign) + 1;
      }
      indexEnd = strValue.indexOf(':');
      if (indexEnd == -1) {
         indexEnd = strValue.length;
         minutes = '00';
      }
      else {
         minutes = strValue.substring(indexEnd + 1, strValue.length);
      }
      hours = strValue.substring(indexSrt, indexEnd);
      hours = hours.replace(/\b(\d)\b/g, '0$1');
      strValue = sign + hours + ':' + minutes;
      strField.value = strValue;
   }
}

function expandTimeDigit() {
   var strField = window.event.srcElement;
   var strValue = strField.value;
   if (strValue.length > 0) {
      strValue = strValue.replace(/\b(\d)\b/g, '0$1');
      strField.value = strValue;
   }
}

function expandCurrencyAmountFlex(amountValue, strPat, currCode) {
   var thePat = PatternsDict[strPat];
   var strVal = amountValue + "";
   var strDecimals = '2';
   if (currCode != 'undefined' && currCode != null) {}
   var length = strVal.length;
   var part1 = strVal.substring(0, length - strDecimals);
   var part2 = strVal.substring(length - strDecimals);
   strVal = part1 + "." + part2;
   strVal = round_decimals(strVal, strDecimals);
   if (strVal.length > 0) {
      var strVal1 = expandMBTValue(amountValue, thePat, strDecimals, strVal);
      strVal = insertChars(strVal, strDecimals, thePat);
   }
   return strVal;
}

function expandCurrencyAmount(amountValue, strPat, currCode) {
   var thePat = PatternsDict[strPat];
   var strVal = amountValue + "";
   var strDecimals = '2';
   if (currCode != 'undefined' && currCode != null) {
      strDecimals = getCurrencyDecimal(currCode);
   }
   var length = strVal.length;
   var part1 = strVal.substring(0, length - strDecimals);
   var part2 = strVal.substring(length - strDecimals);
   strVal = part1 + "." + part2;
   strVal = round_decimals(strVal, strDecimals);
   if (strVal.length > 0) {
      var strVal1 = String(expandMBTValue(amountValue, thePat, strDecimals, strVal));
      strVal = insertChars(strVal, strDecimals, thePat);
   }
   return strVal;
}

function expandCurrencyAmount_movementdisplay(amountValue, strPat, currCode) {
   var thePat = PatternsDict[strPat];
   var strVal = amountValue + "";
   var strDecimals = '2';
   if (currCode != 'undefined' && currCode != null) {
      strDecimals = getCurrencyDecimal(currCode);
   }
   var length = strVal.length;
   var part1 = strVal.substring(0, length - strDecimals);
   var part2 = strVal.substring(length - strDecimals);
   strVal = part1 + "." + part2;
   strVal = round_decimals(strVal, strDecimals);
   if (strVal.length > 0) {
      var strVal1 = String(expandMBTValue(amountValue, thePat, strDecimals, strVal));
      strVal = insertChars(strVal1, strDecimals, thePat);
   }
   return strVal;
}

function formatCurrency_centralMonitor(amountValue, strPat, currCode) {
   var thePat = PatternsDict[strPat];
   var gotIt = thePat.exec(amountValue);
   var strVal = amountValue;
   var strDecimals = '2';
   if (currCode != 'undefined' && currCode != null) {
      strDecimals = getCurrencyDecimal(currCode);
   }
   if (strVal.length > 0) {
      if (!gotIt) {
         return "invalid";
      }
	  

      //check for integer part max 18, decimal part max 4
      var strValNew = String(expandMBTValue(amountValue, thePat, strDecimals, strVal));
      var strInt = strValNew.length;
      var strDec = 0;
      dotPos = strValNew.indexOf('.');
      if (dotPos != -1) {
         strInt = strValNew.substring(0, dotPos);
         strInt = strInt.length;
         strDec = strValNew.substring(dotPos + 1, strValNew.length);
         strDec = strDec.length;
      }
	 
	  // check the valid length
      if (strInt > 18 || strDec > 3) {
	  
         return "invalid";
      }
      strVal = insertChars(strValNew, strDecimals, thePat);
   }
   return strVal;
}
function formatCurrency_AccountGroup(amountValue, strPat, currCode) {
	var thePat = PatternsDict[strPat];
	var gotIt = thePat.exec(amountValue);
	var strVal = amountValue;
	var strDecimals = '0';
	if (strVal.length > 0) {
		if (!gotIt) {
			return "invalid";
		}
		//check for integer part max 18, decimal part max 4
		var strValNew = String(expandMBTValue(amountValue, thePat, strDecimals, strVal));
		var strInt = strValNew.length;
		var strDec = 0;
		dotPos = strValNew.indexOf('.');
		if (dotPos != -1) {
			strInt = strValNew.substring(0, dotPos);
			strInt = strInt.length;
			strDec = strValNew.substring(dotPos + 1, strValNew.length);
			strDec = strDec.length;
		}
		
		// check the valid length
		if (strInt > 18 || strDec > 3) {
			return "invalid";
		}
		strVal = insertChars(strValNew, strDecimals, thePat);
	}
	return strVal;
}

/*
  formatCurrency_forecastMonitor
  This function is used to format the currency
  @param amountValue
  @param strPat
  @param currCode
  @return String
*/

function formatCurrency_forecastMonitor(amountValue, strPat, currCode) {
   var thePat = PatternsDict[strPat];
   var gotIt = thePat.exec(amountValue);
   var strVal = amountValue;
   var strDecimals = '2';
   // get the decimel
   if (currCode != 'undefined' && currCode != null) {
      strDecimals = getCurrencyDecimal(currCode);
   }
   if (strVal.length > 0) {
      if (!gotIt) {
         return "invalid";
      }
      //check for integer part max 19, decimal part max 3
      var strValNew = String(expandMBTValue(amountValue, thePat, strDecimals, strVal));
      var strInt = strValNew.length;
      var strDec = 0;
      dotPos = strValNew.indexOf('.');
      if (dotPos != -1) {
         strInt = strValNew.substring(0, dotPos);
         strInt = strInt.length;
         strDec = strValNew.substring(dotPos + 1, strValNew.length);
         strDec = strDec.length;
      }
      // check the valid length
      if (strInt > 19 || strDec > 3) {
         return "invalid";
      }
      strVal = insertChars(strValNew, strDecimals, thePat);
   }
   return strVal;
}

function isValid(strField, strPat) {
   var thePat = PatternsDict[strPat];
   var gotIt = thePat.exec(strField.value);
   return gotIt;
} 
/**
 * This is a generic function to validate the column data when performing filter sort.
 *
 * @param value
 * @param tempFilterValue
 * @param modifiedFilterValue
 *
 */

function filterValidation(value, tempFilterValue, modifiedFilterValue) { 
	/* special characters replaced. */
   tempFilterValue = replace(tempFilterValue, '&', '&amp;');
   tempFilterValue = replace(tempFilterValue, '>', '&gt;');
   tempFilterValue = replace(tempFilterValue, '<', '&lt;');
   tempFilterValue = tempFilterValue.trim();
   value = value.trim();
   // Checks the number of rows for the filter value
   if (value == tempFilterValue) {
      modifiedFilterValue = replace(modifiedFilterValue, '\n', '');
      // Sets the filter value if modified value not empty
      if (modifiedFilterValue != "") value = modifiedFilterValue;
   }
}

/**
 * This function returns true, if the given field is empty. Otherwise returns false
 * 
 * @param objField
 * @return boolean
 */
function isEmpty(objField) {
	return (objField.value.trim().length == 0) ? true : false;
}

/** Added By Med Amine For Mantis 2145
 * This function is use to validate the format time 
 * @param strField
 * @returns
 */
function validateFormatTime(strField){
    var result = false,result1 = false,result2 = false ,result3 = false ,m;
    if ((m = PatternsDict.timePat1.exec(strField.text))) {
      result1 = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2] ;
    } else if ((m = PatternsDict.timePat2.exec(strField.text))){
      result2 = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2]+":"+ m[3] ;
    } else if ((m = PatternsDict.timePat3.exec(strField.text))){
      result3 = (m[1].length == 2 ? "" : "0") + m[1] + ":00" ;
    }

	result=(result1||result2||result3);
	if(!result) {	 //variable holds returnstr
		var returnStr;
		returnStr = "Please enter a valid time ";
		if(window.event)
			window.event.returnValue = false;
		return false;
	} else {
       strField.text = result;
    }
    return [true, strField.text];
}
function validateFormatTimeSecond(strField){
    var result = false,result1 = false,result2 = false ,result3 = false ,m;
    if ((m = PatternsDict.timePat1.exec(strField.text))) {
      result1 = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2] +":00"  ;
    } else if ((m = PatternsDict.timePat2.exec(strField.text))){
      result2 = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2]+":"+ m[3] ;
    } else if ((m = PatternsDict.timePat3.exec(strField.text))){
      result3 = (m[1].length == 2 ? "" : "0") + m[1] + ":00:00" ;
    }

	result=(result1||result2||result3);
	if(!result) {	 //variable holds returnstr
		var returnStr;
		returnStr = "Please enter a valid time ";
		if(window.event)
			window.event.returnValue = false;
		return false;
	} else {
       strField.text = result;
    }
    return [true, strField.text];
}


function valideDateWithoutTime(year, month ,day){
    if (month < 1 || month > 12) { // check month range
        alert("Month must be between 1 and 12.");
        if(window.event)
        	window.event.returnValue = false;
        return false;
     }
     if (day < 1 || day > 31) {
        alert("Day must be between 1 and 31.");
        if(window.event)
        	window.event.returnValue = false;
        return false;
     }
     if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
        alert("Invalid day for this month");
        if(window.event)
        	window.event.returnValue = false;
        return false;
     }
     if (month == 2) { // check for february 29th
        var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
        if (day > 29 || (day == 29 && !isleap)) {
           alert("Invalid day for this month");
           if(window.event)
        	   window.event.returnValue = false;
           return false;
        }
     }
     if (year == "0000") {
        alert("Invalid year");
        if(window.event)
        	window.event.returnValue = false;
        return false;
     }      
return true;
}


function validateDateWithTimeField(strField,strLabel,strPat){
	
	var isValid = false;
	if(strField.value.trim() == "")
		return true;
	if (strPat == "datePat1"){
	      if ((gotIt = PatternsDict.datePat1.exec(strField.value))){
	            month = gotIt[3]; 
	            day  = gotIt[1];
	            year = gotIt[4];
	                 
	          if(!valideDateWithoutTime(year,month,day)){
	         strField.focus();
	         return false;
	         }
	         strField.value = strField.value +" 00:00:00";
	         return true;
	        
	       }
	       if ((gotIt = PatternsDict.dateWithTimePat.exec(strField.value))){
	         month = gotIt[3]; 
	         day   = gotIt[1];
	         year  = gotIt[4];
	         hour  = gotIt[5];
	         min   = gotIt[6];
	         sec   = gotIt[7];
	         //Validate year, monthy ,day values 
	        if(!valideDateWithoutTime(year,month,day)){
	         strField.focus();
	         return false;
	         }
	        var	 time = hour+":"+min+":"+sec ;
	        var result = false,m;
	      
	     if ((m = PatternsDict.timePat2.exec(time))) 
	            result = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2]+":"+ m[3] ;
	      
	        if(!result)
	        {	 //variable holds returnstr
	            var returnStr;
	            returnStr = "Please enter a valid time ";
	            //alert shows a popupmessage
	            alert(returnStr);
	            if(window.event)
	            	window.event.returnValue = false;
	            strField.focus();
	            strField.value="";
	            return false;
	        }    
	  
	    return true;
	        
	        }
	       if(window.event)
	    	   window.event.returnValue = false;
	      alert("Please enter a valid Date in the format DD/MM/YYYY hh:mm:ss" );
	      return false;
	    }
	 else if (strPat == "datePat2"){
	        if ((gotIt = PatternsDict.datePat2.exec(strField.value))){
	            month = gotIt[1]; 
	            day  = gotIt[3];
	            year = gotIt[4];
	                 
	          if(!valideDateWithoutTime(year,month,day)){
	        	  if(window.event)
	        		  window.event.returnValue = false;
	         strField.focus();
	         return false;
	         }
	         strField.value = strField.value +" 00:00:00";
	         return true;
	        
	       }
	       if ((gotIt = PatternsDict.dateWithTimePat.exec(strField.value))){
	         month = gotIt[1]; 
	         day   = gotIt[3];
	         year  = gotIt[4];
	         hour  = gotIt[5];
	         min   = gotIt[6];
	         sec   = gotIt[7];
	         //Validate year, monthy ,day values 
	        if(!valideDateWithoutTime(year,month,day)){
	        	if(window.event)
	        		window.event.returnValue = false;
	         strField.focus();
	         return false;
	         }
	        var	 time = hour+":"+min+":"+sec ;
	        var result = false,m;
	      
	     if ((m = PatternsDict.timePat2.exec(time))) 
	            result = (m[1].length == 2 ? "" : "0") + m[1] + ":" + m[2]+":"+ m[3] ;
	      
	        if(!result)
	        {	 //variable holds returnstr
	            var returnStr;
	            returnStr = "Please enter a valid time ";
	            //alert shows a popupmessage
	            alert(returnStr);
	            if(window.event)
	            	window.event.returnValue = false;
	            strField.focus();
	            strField.value="";
	            return false;
	        }    
	  
	    return true;
	        
	        }
	       if(window.event)
	    	   window.event.returnValue = false;
	       alert("Please enter a valid Date in the format MM/DD/YYYY hh:mm:ss" );
	       strField.focus();
		      return false;
	}

}

function validateFlexField(strPat,value,maxValue,minValue) {
    var thePat = PatternsDict[strPat];
    var gotIt = thePat.exec(value);
	var returnStr;
       if (strPat == "alphaNumPatWithSpace") {
          // A message is given if only blank spaces are entered, as only Spaces are allowed 
          if (value.trim().length == 0) {
             returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
             return returnStr;
          }
          //Condition Added to check the names contains only Alpha numeric with spaces
          if (!gotIt) {
             returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
             return returnStr;
          }
       }
       else if (strPat == "alphaNumPatWithUnderScoreAndDotAndQuestionmark") {
          // A message is given if only blank spaces are entered, as only Spaces are allowed 
          if (value.trim().length == 0) {
             returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
             return returnStr;
          }
          // This  pattern is used to provide alert only for combination of dot and underscore only
          thePat = PatternsDict['UnderScoreAndDotPattern'];
          gotIt = thePat.exec(value);
          if (gotIt) {
             returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
             return returnStr;
          }
          else {
             //Condition Added to check the names contains only Alpha numeric with spaces
             thePat = PatternsDict['alphaNumPatWithUnderScoreAndDotAndQuestionmark'];
             gotIt = thePat.exec(value);
             if (!gotIt) {
                returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
                return returnStr;
             }
          }
       }
      
       if (strPat == "alphaNumPatWithHyphenAndUnderScore") {
      	 if (value.trim().length == 0) {
               returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
               return returnStr;
           }
      	 thePat = PatternsDict['alphaNumPatWithHyphenAndUnderScore'];
           gotIt = thePat.exec(value);
           if (!gotIt) {
          	 returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
              return returnStr;
           }
       }
          if(strPat == "biccode"){
        	  if(!gotIt){
        		  returnStr ="false:"+PatternsMetaData[strPat];
        		  return returnStr; 
        		  
        	  }
          }
             
       if (strPat == "alphaNumPatExtended") {
          if (!gotIt) {
             return "false";
          }
       }
       if (strPat != "alphaNumPatWithSpace") {
          if (!gotIt) {
             if (strPat == "alphaNumPatWithUppercase") {
                returnStr ="false:"+PatternsMetaData[strPat];
                return returnStr;
             }
             
             //Condition Checks for negative with digits and without negative
             else if (strPat == "negativeWithDigits" || strPat == "decimalPatWithoutNegative") {
                returnStr ="false:"+PatternsMetaData["negativeWithDigits"];
                return returnStr;
             }
             else if (strPat=="alphaNumPatPassWord" ) {
                returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
                return returnStr;
             }
            
             else {
                returnStr = "false:Please enter a valid " + PatternsMetaData[strPat];
                return returnStr;
             }
             return returnStr;
          }
       }
       if (strPat == "numberPat" || strPat == "numberPatAll" || strPat == "numberPatExpand") {
          var isValidValue = "true";
          var valueNumber = parseInt(value);
          if (maxValue != undefined && maxValue != 'MAX') {
             var maxValueNumber = parseInt(maxValue);
            
             if(maxValue < valueNumber)
            	 isValidValue = "false:This field should have maximum value " + maxValue + "."
          }
          if (isValidValue.indexOf("true") != -1 && minValue != undefined && minValue != 'MIN') {
              var minValueNumber = parseInt(minValue);
              if(minValue > valueNumber)
            	  isValidValue = "false:This field should have minimum value " + minValue + "."
          }

          return isValidValue;
       } 
		 // end numberPat 	
       else if (thePat == PatternsDict.datePat4 || thePat == PatternsDict.datePat2) {
    	   alert(thePat);
          month = gotIt[1]; // parse date into variables
          day = gotIt[3];
          year = gotIt[4];
          if (month < 1 || month > 12) { // check month range
             returnStr =  "false:Month must be between 1 and 12.";
			 return returnStr;
          }
          if (day < 1 || day > 31) {
             returnStr = "false:Day must be between 1 and 31.";
			 return returnStr;
          }
          if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
             returnStr = "false:Invalid day for this month";
			 return returnStr;
             
          }
          if (month == 2) { // check for february 29th
             var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
             if (day > 29 || (day == 29 && !isleap)) {
                returnStr = "false:Invalid day for this month";
				return returnStr;
             }
          }
          if (year == "0000") {
             returnStr = "false:Invalid year";
			 return returnStr;
          }
          return true; // date is valid
       } // end datePat 
       else if (thePat == PatternsDict.datePat1) {
          month = gotIt[3]; // parse date into variables
          day = gotIt[1];
          year = gotIt[4];
          if (month < 1 || month > 12) { // check month range
             returnStr = "false:Month must be between 1 and 12.";
			 return returnStr;
          }
          if (day < 1 || day > 31) {
             returnStr = "false:Day must be between 1 and 31.";
			 return returnStr;
          }
          if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
             returnStr = "false:Invalid day for this month";
			 return returnStr;
          }
          if (month == 2) { // check for february 29th
             var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
             if (day > 29 || (day == 29 && !isleap)) {
                returnStr = "false:February " + year + " doesn't have " + day + " days!";
				return returnStr;
             }
          }
          if (year == "0000") {
             returnStr = "false:Invalid year";
			 return returnStr;
          }
          return "true"; // date is valid
       } // end datePat 
       else if (thePat == PatternsDict.datePat3) {
          month = gotIt[3]; // parse date into variables
          day = gotIt[1];
          year = gotIt[4];
          if (month < 1 || month > 12) { // check month range
             returnStr =  "false:Date must be in the format DD/MM/YYYY";
			 return returnStr;
          }
          if (day < 1 || day > 31) {
             returnStr = "false:Date must be in the format DD/MM/YYYY";
			 return returnStr;
          }
          if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
             returnStr = "false:Date must be in the format DD/MM/YYYY.";
			 return returnStr;
          }
          if (month == 2) { // check for february 29th
             var isleap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
             if (day > 29 || (day == 29 && !isleap)) {
                returnStr = "false:Date must be in the format DD/MM/YYYY";
				return returnStr;
             }
          }
          if (year == "0000") {
             returnStr = "false:Invalid year";
			 return returnStr;
          }
          return "true"; // date is valid
       } // end datePat 
 return returnStr;
}
