/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
var $ = require('jquery');
var AdvancedToolTip = /** @class */ (function (_super) {
    tslib_1.__extends(AdvancedToolTip, _super);
    function AdvancedToolTip(tipelement, tipcommon) {
        var _this = _super.call(this, tipelement, tipcommon) || this;
        _this.tipelement = tipelement;
        _this.tipcommon = tipcommon;
        _this._top = 0;
        _this._left = 0;
        _this._destroyTimeOut = 20000;
        return _this;
    }
    /**
     * @return {?}
     */
    AdvancedToolTip.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        setTimeout((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.destroy();
        }), this.timeOut);
    };
    Object.defineProperty(AdvancedToolTip.prototype, "top", {
        get: /**
         * @return {?}
         */
        function () {
            return this._top;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(this.tipelement.nativeElement.children[0]).css("top", value);
            this._top = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AdvancedToolTip.prototype, "left", {
        get: /**
         * @return {?}
         */
        function () {
            return this._left;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(this.tipelement.nativeElement.children[0]).css("left", value);
            this._left = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    AdvancedToolTip.prototype.destroy = /**
     * @return {?}
     */
    function () {
        $(this.tipelement.nativeElement.children[0]).hide();
    };
    /**
     * @return {?}
     */
    AdvancedToolTip.prototype.display = /**
     * @return {?}
     */
    function () {
        //console.log("this.tipelement.nativeElement.children[0] = ", this.tipelement.nativeElement.children[0]);
        $(this.tipelement.nativeElement.children[0]).show();
    };
    Object.defineProperty(AdvancedToolTip.prototype, "timeOut", {
        get: /**
         * @return {?}
         */
        function () {
            return this._destroyTimeOut;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._destroyTimeOut = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    AdvancedToolTip.prototype.getChild = /**
     * @return {?}
     */
    function () {
        return this.child;
    };
    AdvancedToolTip.decorators = [
        { type: Component, args: [{
                    selector: 'AdvancedToolTip',
                    template: "\n        <div class=\"advanced-tooltip\">\n            <ng-container #container></ng-container>\n        </div>\n    ",
                    animations: [],
                    styles: ["\n            .advanced-tooltip {\n                width: auto;\n                height: auto;\n                position: fixed;\n                background-color: transparent;\n                display: none;\n            }\n    "]
                }] }
    ];
    /** @nocollapse */
    AdvancedToolTip.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return AdvancedToolTip;
}(Container));
export { AdvancedToolTip };
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._top;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._left;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._destroyTimeOut;
    /** @type {?} */
    AdvancedToolTip.prototype.child;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype.tipelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype.tipcommon;
}
//# sourceMappingURL=data:application/json;base64,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