import { OnInit, ElementRef, ViewContainerRef, <PERSON>derer2, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class SwtPanel extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    private _renderer;
    private _styleName;
    private _title;
    container: ViewContainerRef;
    constructor(elem: ElementRef, commonService: CommonService, _renderer: Renderer2);
    /**
     * addChild : append a dynamically created component.
     * @param type : Component type
     */
    addChild(type: any): {};
    ngOnDestroy(): void;
    ngOnInit(): void;
    styleName: string;
    title: string;
}
