/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtLabel } from "./swt-label.component";
import { SwtProgressBar } from "./progress-bar.component";
import { FileUploader } from 'ng2-file-upload';
import { SwtTextInput } from "./swt-text-input.component";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { Logger } from "../logging/logger.service";
import { StringUtils } from "../utils/string-utils.service";
import { SwtAlert } from '../utils/swt-alert.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var FileUpload = /** @class */ (function (_super) {
    tslib_1.__extends(FileUpload, _super);
    function FileUpload(element, commonService) {
        var _this = _super.call(this, element, commonService) || this;
        _this.element = element;
        _this.commonService = commonService;
        _this.value = 0;
        _this._refUploadFile = new FileUploader({ url: SwtUtil.getBaseURL() + "core/file!upload.do?response=json", headers: [] });
        _this.hasBaseDropZoneOver = false;
        _this.hasAnotherDropZoneOver = false;
        _this.uploadCompletedData = new Function();
        _this.name = "";
        _this.fileDeleted = false;
        _this.jsonReader = new JSONReader();
        _this.inputData = new HTTPComms(_this.commonService);
        _this.baseURL = "";
        _this.actionMethod = "";
        _this.actionPath = "";
        _this.requestParams = new Array();
        _this._arrUploadFiles = new Array();
        _this._numCurrentUpload = 0;
        _this._fileObj = { name: "", size: "" };
        _this._validFiles = new Array();
        _this._arrayFiles = new Array();
        _this.uploadWindow = null;
        // Variable to set the number of current file
        _this.fileNumber = 0;
        _this._validExtensions = new Array();
        _this._valideFilesDesc = "";
        _this._tableName = "";
        _this._columnName = "";
        _this.logger = new Logger("FileUpload", _this.commonService.httpclient);
        // initialize setter.
        _this.SwtAlert = new SwtAlert(commonService);
        return _this;
    }
    Object.defineProperty(FileUpload.prototype, "validExtensions", {
        //Set valid extensions
        set: 
        //Set valid extensions
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._validExtensions = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "valideFilesDesc", {
        // Sets the valid files name
        set: 
        // Sets the valid files name
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._valideFilesDesc = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "tableName", {
        // Set table Name
        set: 
        // Set table Name
        /**
         * @param {?} tableName
         * @return {?}
         */
        function (tableName) {
            this._tableName = tableName;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "columnName", {
        // Set column Name
        set: 
        // Set column Name
        /**
         * @param {?} columnName
         * @return {?}
         */
        function (columnName) {
            this._columnName = columnName;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "uploadUrl", {
        // Set uploadUrl
        set: 
        // Set uploadUrl
        /**
         * @param {?} strUploadUrl
         * @return {?}
         */
        function (strUploadUrl) {
            this._strUploadUrl = strUploadUrl;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "downloadUrl", {
        // Set downloadUrl
        set: 
        // Set downloadUrl
        /**
         * @param {?} strDownloadUrl
         * @return {?}
         */
        function (strDownloadUrl) {
            this._strDownloadUrl = strDownloadUrl;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(FileUpload.prototype, "deleteUrl", {
        // Set deleteUrl
        set: 
        // Set deleteUrl
        /**
         * @param {?} strDeleteUrl
         * @return {?}
         */
        function (strDeleteUrl) {
            this._strDeleteUrl = strDeleteUrl;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method called when file upload initialized.
     */
    /**
     * This method called when file upload initialized.
     * @return {?}
     */
    FileUpload.prototype.onLoad = /**
     * This method called when file upload initialized.
     * @return {?}
     */
    function () {
        var _this = this;
        this._refUploadFile.onProgressItem = (/**
         * @param {?} fileItem
         * @param {?} progress
         * @return {?}
         */
        function (fileItem, progress) {
            _this.updateProgBar(progress);
        });
        this._refUploadFile.onErrorItem = (/**
         * @param {?} item
         * @param {?} response
         * @param {?} status
         * @param {?} headers
         * @return {?}
         */
        function (item, response, status, headers) {
            _this.onErrorItem(item, response, status, headers);
        });
        this._refUploadFile.onSuccessItem = (/**
         * @param {?} item
         * @param {?} response
         * @param {?} status
         * @param {?} headers
         * @return {?}
         */
        function (item, response, status, headers) {
            _this.onSuccessItem(item, response, status, headers);
            // On multiFile select upload one by one.
            if (_this._numCurrentUpload < _this._refUploadFile.queue.length - 1) {
                _this._numCurrentUpload++;
                _this.progBar.value = 0;
                _this.progBar.label = "";
                _this.lblFomattedSize.text = _this.formatFileSize(_this._refUploadFile.queue[_this._numCurrentUpload].file.size);
                _this.toUploadFile.text = _this._refUploadFile.queue[_this._numCurrentUpload].file.name;
                _this._refUploadFile.options.headers = [{
                        name: 'Uploadfile',
                        value: _this._refUploadFile.queue[_this._numCurrentUpload].file.name
                    }];
                _this._refUploadFile.queue[_this._numCurrentUpload].upload();
                _this._arrayFiles.push(_this._refUploadFile.queue[_this._numCurrentUpload].file);
            }
            _this.fileNumber--;
        });
        this._refUploadFile.onCompleteAll = (/**
         * @return {?}
         */
        function () {
            _this.uploadCompletedData({ arrayFiles: _this._arrayFiles });
        });
        $("input:file").change((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.progBar.value = 0;
            _this.progBar.label = "";
            _this.lblFomattedSize.text = _this.formatFileSize(_this._refUploadFile.queue[_this._numCurrentUpload].file.size);
            _this.toUploadFile.text = _this._refUploadFile.queue[_this._numCurrentUpload].file.name;
            _this._refUploadFile.options.headers = [{
                    name: 'Uploadfile',
                    value: _this._refUploadFile.queue[_this._numCurrentUpload].file.name
                }];
            _this._refUploadFile.queue[_this._numCurrentUpload].upload();
            _this._arrayFiles.push(_this._refUploadFile.queue[_this._numCurrentUpload].file);
            _this.fileNumber = _this._refUploadFile.queue.length - 1;
        }));
    };
    /**
     * This method is used to close file upload.
     * @param event
     */
    /**
     * This method is used to close file upload.
     * @param {?} event
     * @return {?}
     */
    FileUpload.prototype.closeHandler = /**
     * This method is used to close file upload.
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.close();
    };
    /**
     * @param {?} parent
     * @param {?} callback
     * @return {?}
     */
    FileUpload.prototype.startUpload = /**
     * @param {?} parent
     * @param {?} callback
     * @return {?}
     */
    function (parent, callback) {
        try {
            this.uploadWindow = SwtPopUpManager.createPopUp(parent, FileUpload, {
                title: SwtUtil.getCommonMessages('fileUpload.lblTitle.label'),
                uploadCompletedData: callback
            });
            this.uploadWindow.enableResize = false;
            this.uploadWindow.width = "510";
            this.uploadWindow.height = "180";
            this.uploadWindow.display();
        }
        catch (e) {
            this.logger.error("startUpload error: ", e);
        }
    };
    /**
     * @param {?} e
     * @return {?}
     */
    FileUpload.prototype.fileOverBase = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        this.hasBaseDropZoneOver = e;
    };
    /**
     * @param {?} e
     * @return {?}
     */
    FileUpload.prototype.fileOverAnother = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        this.hasAnotherDropZoneOver = e;
    };
    /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    FileUpload.prototype.onSuccessItem = /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    function (item, response, status, headers) {
        /** @type {?} */
        var result = JSON.parse(response).fileUpload;
        // Show alert if file exist on server.
        if (result.singletons.message === 'FILE_ALREADY_EXISTS') {
            this.SwtAlert.info(StringUtils.substitute(SwtUtil.getCommonMessages('alert.file_already_exists'), this._fileObj.name, result.singletons.fileToStore), SwtUtil.getCommonMessages('alert_header.info'));
            if (this.fileNumber === 0) {
                //   SwtPopUpManager.removePopUp(this);
                this.close();
            }
        }
        else {
            if (this.fileNumber === 0) {
                this.SwtAlert.info(SwtUtil.getCommonMessages('alert.upload_data_info'), SwtUtil.getCommonMessages('alert_header.info'));
                //  SwtPopUpManager.removePopUp(this);
                this.close();
            }
        }
    };
    /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    FileUpload.prototype.onErrorItem = /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    function (item, response, status, headers) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            //Security Error in uploading file.
            this.SwtAlert.error(SwtUtil.getCommonMessages('alert.security_error'), SwtUtil.getCommonMessages('alert_header.error'));
            errorLocation = 10;
            this.progBar.value = 0;
            this.progBar.label = "0%";
            errorLocation = 20;
            this.clearUpload();
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "onUploadIoError", errorLocation);
        }
    };
    //Cancel and clear eventlisteners on last upload
    //Cancel and clear eventlisteners on last upload
    /**
     * @private
     * @return {?}
     */
    FileUpload.prototype.clearUpload = 
    //Cancel and clear eventlisteners on last upload
    /**
     * @private
     * @return {?}
     */
    function () {
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            if (this._refUploadFile) {
                errorLocation = 20;
                this._refUploadFile.cancelAll();
                errorLocation = 30;
                this._refUploadFile.clearQueue();
            }
            errorLocation = 40;
            this._numCurrentUpload = 0;
            this.updateProgBar();
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "clearUpload", errorLocation);
        }
    };
    /**
     * @private
     * @param {?=} numPerc
     * @return {?}
     */
    FileUpload.prototype.updateProgBar = /**
     * @private
     * @param {?=} numPerc
     * @return {?}
     */
    function (numPerc) {
        if (numPerc === void 0) { numPerc = 0; }
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var strLabel = "";
        try {
            errorLocation = 10;
            strLabel = (this._numCurrentUpload + 1 <= this._arrUploadFiles.length && numPerc > 0 && numPerc < 100) ? numPerc + "% - " + strLabel : strLabel;
            errorLocation = 15;
            strLabel = (this._numCurrentUpload + 1 === this._arrUploadFiles.length && numPerc === 100) ? "Upload Complete - " + strLabel : strLabel;
            errorLocation = 20;
            strLabel = (this._arrUploadFiles.length === 0) ? "" : strLabel;
            errorLocation = 25;
            this.progBar.label = strLabel;
            this.progBar.value = numPerc;
            errorLocation = 30;
            // progBar.validateNow();
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "updateProgBar", errorLocation);
            this.logger.error("[ updateProgBar ] method error: ", error, " errorLocation : ", errorLocation);
        }
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    FileUpload.prototype.formatFileSize = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var _size = "";
        if (value < Math.pow(10, 3)) {
            _size = value + " " + SwtUtil.getCommonMessages("fileUpload.lblSize.unit");
        }
        else if (value > Math.pow(10, 3) && value < Math.pow(10, 6)) {
            _size = (value / Math.pow(10, 3)).toFixed(1) + " KB";
        }
        else if (value > Math.pow(10, 6) && value < Math.pow(10, 9)) {
            _size = (value / Math.pow(10, 6)).toFixed(1) + " Mo";
        }
        else {
            _size = (value / Math.pow(10, 9)).toFixed(1) + " Go";
        }
        return (_size);
    };
    FileUpload.decorators = [
        { type: Component, args: [{
                    selector: 'FileUpload',
                    template: "\n        <SwtModule (creationComplete)=\"onLoad()\" width=\"100%\" height=\"100%\">\n            <VBox width=\"100%\" height=\"100%\" paddingTop=\"5\" paddingLeft=\"5\" paddingBottom=\"5\" paddingRight=\"5\">\n                <SwtPanel id=\"lblTitle\" title=\" \" width=\"100%\" height=\"132\">\n                    <HBox paddingLeft=\"8\">\n                        <SwtLabel id=\"lblnameFile\"\n                                  text=\"{{ getCommonMessages('fileUpload.lblnameFile.label') }}\"></SwtLabel>\n                        <SwtTextInput #toUploadFile id=\"toUploadFile\" editable=\"false\" width=\"330\" marginTop=\"5\"></SwtTextInput>\n                        <!--<SwtButton #btnAdd id=\"btnAdd\" width=\"50\" label=\"...\" (onClick)=\"addFiles()\"\n                         toolTip=\"{{ getCommonMessages('fileUpload.btnAdd.tooltip') }}\"></SwtButton>-->\n\n                        <div class=\"file_Upload\" >\n                            <span>...</span>\n                            <input type=\"file\" class=\"upload\"  ng2FileSelect [uploader]=\"_refUploadFile\"   multiple />\n                        </div>\n                    </HBox>\n                    <HBox>\n                        <SwtLabel #lblSize id=\"lblSize\" width=\"48\" textAlign=\"right\"\n                                  text=\"{{ getCommonMessages('fileUpload.lblSize.label') }}\"></SwtLabel>\n                        <SwtLabel #lblFomattedSize id=\"lblFomattedSize\" width=\"105\" text=\"\"></SwtLabel>\n                    </HBox>\n                    <HBox>\n                        <SwtProgressBar #progBar marginLeft=\"15\" marginBottom=\"5\"  width=\"445\"></SwtProgressBar>\n                    </HBox>\n                    <HBox>\n                        <spacer width=\"405\"></spacer>\n                        <SwtButton #buttonClose\n                                   id=\"buttonClose\"\n                                   label=\"{{ getCommonMessages('button.close') }}\"\n                                   (click)=\"close()\"\n                                   toolTip=\"{{ getCommonMessages('button.tooltip.close') }}\"></SwtButton>\n                    </HBox>\n                </SwtPanel>\n            </VBox>\n        </SwtModule>\n    ",
                    styles: ["\n        .file_Upload {\n            position: relative;\n            overflow: hidden;\n            margin: 10px;\n            border-bottom:1px solid #52869a!important ;\n            border-top:1px solid #90b6c4!important;\n            border-left:1px solid #52869a!important;\n            border-right:1px solid #52869a!important;\n            border-radius: 5px;\n            font-size:12px;\n            letter-spacing:0.2px;\n            height: 23px;\n            margin : 5px!important;\n            font-weight:bolder;\n            color: #173553;\n            width: auto;\n            padding: 0px 10px;\n            background-color: #C2E5FF;\n            background-image: -webkit-gradient(linear, left top, left bottom, from(#C2E5FF), to(#92ACBF));\n            background-image: -webkit-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -moz-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -ms-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -o-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: linear-gradient(to bottom, #C2E5FF, #92ACBF);\n            background-image: url(\"assets/images/button_bg.png\");\n        }\n\n        .file_Upload input.upload {\n            position: absolute;\n            top: 0;\n            right: 0;\n            margin: 0;\n            padding: 0;\n            font-size: 20px;\n            cursor: pointer;\n            opacity: 0;\n            filter: alpha(opacity=0);\n        }\n\n        #lblnameFile {\n            margin-top: 5px;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    FileUpload.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    FileUpload.propDecorators = {
        progBar: [{ type: ViewChild, args: ["progBar",] }],
        lblFomattedSize: [{ type: ViewChild, args: ["lblFomattedSize",] }],
        toUploadFile: [{ type: ViewChild, args: ["toUploadFile",] }]
    };
    return FileUpload;
}(SwtModule));
export { FileUpload };
if (false) {
    /** @type {?} */
    FileUpload.prototype.id;
    /** @type {?} */
    FileUpload.prototype.value;
    /** @type {?} */
    FileUpload.prototype.progBar;
    /** @type {?} */
    FileUpload.prototype.lblFomattedSize;
    /** @type {?} */
    FileUpload.prototype.toUploadFile;
    /** @type {?} */
    FileUpload.prototype._refUploadFile;
    /** @type {?} */
    FileUpload.prototype.hasBaseDropZoneOver;
    /** @type {?} */
    FileUpload.prototype.hasAnotherDropZoneOver;
    /** @type {?} */
    FileUpload.prototype.uploadCompletedData;
    /** @type {?} */
    FileUpload.prototype.name;
    /** @type {?} */
    FileUpload.prototype.fileDeleted;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._fileName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.lastRecievedXML;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.prevRecievedXML;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.inputData;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.baseURL;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.actionMethod;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strUploadUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strDownloadUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strDeleteUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._arrUploadFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._numCurrentUpload;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._fileObj;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._validFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._arrayFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.uploadWindow;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.fileNumber;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._validExtensions;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._valideFilesDesc;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._tableName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._columnName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.element;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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