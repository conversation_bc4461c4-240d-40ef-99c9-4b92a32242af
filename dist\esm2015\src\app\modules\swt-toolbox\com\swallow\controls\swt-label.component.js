/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ViewChild, ElementRef, Input, Component } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from '../utils/swt-util.service';
/** @type {?} */
const $ = require('jquery');
export class SwtLabel extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions--------------------------------------------------------------------------------------
        this._text = "";
        this._truncate = true;
        this._htmlText = "";
        this._fontSize = "11";
        this._fontWeight = "";
        this._width = "";
        this._buttonMode = false;
    }
    //---width--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        try {
            this._width = this.adaptUnit(value, "auto");
            this.setStyle("width", this._width);
            $(this.elem.nativeElement.children[0]).css({
                "width": "100%"
            });
        }
        catch (error) {
            console.error('method [set width] - error :', error);
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    //---Truncate---------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set truncate(value) {
        this._truncate = this.adaptValueAsBoolean(value);
        if (this._truncate) {
            $(this.elem.nativeElement.children[0]).removeClass('ellipsisDisabled').addClass('ellipsisEnabled');
        }
        else {
            $(this.elem.nativeElement.children[0]).removeClass('ellipsisEnabled').addClass('ellipsisDisabled');
        }
    }
    /**
     * @return {?}
     */
    get truncate() {
        return this._truncate;
    }
    //---styleName--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set styleName(value) {
        $(this.elem.nativeElement.children[0]).addClass(value);
    }
    //---TextAlign---------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set textAlign(value) {
        if (value) {
            this.labelDOM.nativeElement.parentElement.style.textAlign = value;
        }
    }
    //---HtmlText--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set htmlText(value) {
        if (this.firstCall) {
            this.originalValue = value;
            this.firstCall = false;
        }
        else {
            this._spyChanges(value);
        }
        this._htmlText = value;
        //        this._htmlText= this._htmlText.replace(/&<;/ig, '&#60').replace(/>/g, '&#62');
        this._htmlText = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\r\n|\r|\n)/g, '<br>');
        $(this.elem.nativeElement.children[0]).removeClass('ellipsisEnabled');
        $(this.elem.nativeElement.children[0]).html(this._htmlText);
    }
    /**
     * @return {?}
     */
    get htmlText() {
        return this._htmlText;
    }
    //---Text-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        if (this.firstCall) {
            this.originalValue = value;
            this.firstCall = false;
        }
        else {
            this._spyChanges(value);
        }
        this._text = value;
        $(this.elem.nativeElement.children[0]).text(value);
        // set tooltip to label if the text is too long and it will be hidden.
        setTimeout((/**
         * @return {?}
         */
        () => {
            if (this.elem.nativeElement.children[0].scrollWidth > this.elem.nativeElement.children[0].clientWidth)
                this.toolTip = this.text;
        }), 0);
    }
    /**
     * @return {?}
     */
    get text() {
        return this._text;
    }
    //---FontSize--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set fontSize(value) {
        this._fontSize = value;
        $(this.elem.nativeElement.children[0]).css('font-size', value + "px");
    }
    /**
     * @return {?}
     */
    get fontSize() {
        return this._fontSize;
    }
    //---FontWeight------------------------------------------------------------------------------------------------
    /**
     * @param {?} fontWeight
     * @return {?}
     */
    set fontWeight(fontWeight) {
        this._fontWeight = fontWeight;
        this.setStyle('font-weight', fontWeight, this.elem.nativeElement.children[0]);
    }
    /**
     * @return {?}
     */
    get fontWeight() {
        return this._fontWeight;
    }
    //---Color-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} color
     * @return {?}
     */
    set color(color) {
        this._color = color;
        this.setStyle('color', color, this.elem.nativeElement.children[0]);
    }
    /**
     * @return {?}
     */
    get color() {
        return this._color;
    }
    //---Color-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set buttonMode(value) {
        if (typeof (value) === 'string') {
            if (value === 'true') {
                this._buttonMode = true;
            }
            else {
                this._buttonMode = false;
            }
        }
        else {
            this._buttonMode = value;
        }
        if (this._buttonMode) {
            $(this.elem.nativeElement.children[0]).addClass('linkbutton');
            this.onClick = this.emtpyOnnClick;
        }
        else {
            $(this.elem.nativeElement.children[0]).removeClass('linkbutton');
            this.onClick = this.defaultOnnClick;
        }
    }
    /**
     * @return {?}
     */
    get buttonMode() {
        return this._buttonMode;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        if (this.constructor.name.toUpperCase() == "SWTLABEL") {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtLabel');
        }
        else {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtText');
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set textDictionaryId(value) {
        if (value) {
            this.text = SwtUtil.getPredictMessage(value);
        }
    }
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    defaultOnnClick(event) {
        event.preventDefault();
        event.stopPropagation();
    }
    /**
     * @param {?} event
     * @return {?}
     */
    emtpyOnnClick(event) {
    }
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    onClick(event) {
        event.preventDefault();
        event.stopPropagation();
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            delete this.labelDOM;
            delete this._text;
            delete this._color;
            delete this._truncate;
            delete this._htmlText;
            delete this._fontSize;
            delete this.firstCall;
            delete this._fontWeight;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
}
SwtLabel.decorators = [
    { type: Component, args: [{
                selector: 'SwtLabel',
                template: `
        <label  
               (click)="onClick($event)"
               class="ellipsisEnabled"
               #labelDOM>
        </label>
    `,
                styles: [`
          :host {
              outline:none;
              display: block;
           }
           
           label{
             font-size:11px;
             color: black;
             height: 23px;
              line-height: 22px;
             font-family: verdana,helvetica;
             vertical-align: bottom;
            /* margin: 0px 0px 5px 0px;*/
             pointer-events: auto!important; 
           }
          .ellipsisEnabled{
             text-overflow: ellipsis;
             overflow: hidden;
             white-space: nowrap;
          }
          .ellipsisDisabled{
             text-overflow: clip;
          }
   `]
            }] }
];
/** @nocollapse */
SwtLabel.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtLabel.propDecorators = {
    labelDOM: [{ type: ViewChild, args: ['labelDOM',] }],
    width: [{ type: Input, args: ['width',] }],
    truncate: [{ type: Input, args: ["truncate",] }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    styleName: [{ type: Input, args: ['styleName',] }],
    textAlign: [{ type: Input, args: ['textAlign',] }],
    htmlText: [{ type: Input }],
    text: [{ type: Input }],
    fontSize: [{ type: Input, args: ["fontSize",] }],
    fontWeight: [{ type: Input }],
    color: [{ type: Input }],
    buttonMode: [{ type: Input }],
    textDictionaryId: [{ type: Input, args: ['textDictionaryId',] }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.labelDOM;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._color;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._truncate;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._htmlText;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._fontSize;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._buttonMode;
    /** @type {?} */
    SwtLabel.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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