/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
//import * as $ from "jquery";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
export class RadioButtonEditor {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.isBool = false; //isBool : variable to test the radiobutton's value type (boolean or string)
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.logger = new Logger('RadioButtonEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    init() {
        this.logger.info('Method [init] -START- enabledFlag :', this.enableFlag);
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting 
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        this.defaultValue = this.args.item[this.args.column.field];
        this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field];
        if (this.showHideCells) {
            this.$input = $(`<input type='radio'  ${(this.enableFlag == false) ? 'disabled' : ''} value='${this.defaultValue}' class='editor-radio' hideFocus  />`);
        }
        else {
            this.$input = $(``);
        }
        this.$input.appendTo(this.args.container);
        this.logger.info('Method [init] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    destroy() {
        this.logger.info('Method [destroy] -START-');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.logger.info('Method [destroy] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('Method [loadValue] -START/END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    focus() {
        this.logger.info('Method [focus] -START/END-');
        this.$input.focus();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    serializeValue() {
        this.logger.info('Method [serializeValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            //For the first click on the render , the CheckBox will change the value within the opposite precede state.
            if (this.enableFlag == true) {
                this.$input.prop('checked', true);
            }
            ;
            //RadioButton's Event change handler
            this.$input.change((/**
             * @return {?}
             */
            () => {
                if (this.enableFlag == true) {
                    this.applyValue(this.args.item, this.$input.prop('checked'));
                    this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => ((x.crud_data.id == this.args.item.id))));
                    ;
                    if (this.CRUD_CHANGES_DATA != undefined && this.CRUD_CHANGES_DATA != null) {
                        this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : null;
                    }
                    /*console.log('[serializeValue] this.originalDefaultValue =',this.originalDefaultValue)
                    console.log('[serializeValue] this.defaultValue =',this.defaultValue)
                    console.log('[serializeValue] this.args.item[this.args.column.field] =',this.args.item[this.args.column.field])*/
                    if ((this.originalDefaultValue == null && this.defaultValue != this.args.item[this.args.column.field]) || ((this.originalDefaultValue != null) && (this.originalDefaultValue != this.args.item[this.args.column.field]))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                            if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field] = { content: this.defaultValue };
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            this.commonGrid.spyChanges({ field: this.args.column.field });
                            this.commonGrid.updateCrud(updatedObject);
                            SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                        }
                    }
                    else if ((this.originalDefaultValue == this.args.item[this.args.column.field])) {
                        /** @type {?} */
                        var crudChange = this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => ((x.crud_data.id == this.args.item.id))));
                        /** @type {?} */
                        var ch = String("U(" + this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => ((x.crud_data.id == this.args.item.id))));
                                this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        //- Do not emit SpyNoChanges on the grid unless there is other changes.
                        if (this.commonGrid.changes.size() == 0)
                            this.commonGrid.spyNoChanges({ field: this.args.column.field });
                        SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                    }
                    /* for (var index = 0; index < this.commonGrid.dataProvider.length; index++) {
                         this.commonGrid.dataProvider[index][this.args.column.field]="false";
                     }
                     this.commonGrid.refresh();*/
                    //                    this.commonGrid.spyChanges({ field: this.args.column.field });
                    this.commonGrid.RadioButtonChange();
                    console.log('[RadioButtonEditor] this.args.column =', this.args.column);
                    console.log('[RadioButtonEditor] this.args.item =', this.args.item);
                    /** @type {?} */
                    var ListEvent = {
                        radioButtonDTO: {
                            col: this.args.column.columnorder,
                            id: this.args.column.id,
                            field: this.args.column.field,
                            name: this.args.column.name,
                            type: this.args.column.columnType
                        }
                    };
                    for (let key in this.args.item) {
                        if (key != 'slickgrid_rowcontent')
                            ListEvent.radioButtonDTO[key] = this.args.item[key];
                    }
                    this.commonGrid.radioButtonChange.emit(ListEvent);
                }
            }));
            this.$input.change();
        }
        this.logger.info('Method [serializeValue] -END-');
        return this.$input.prop('checked');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('Method [applyValue] -START-');
        if (this.showHideCells && this.enableFlag) {
            this.args.item[this.args.column.field] = '' + state;
            this.args.item.slickgrid_rowcontent[this.args.column.field] = { content: '' + state };
            this.$input.val(state);
        }
        this.logger.info('Method [applyValue] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    isValueChanged() {
        this.logger.info('Method [isValueChanged] -START/END-');
        if (this.showHideCells) {
            return (!(this.$input.val() === '' && this.defaultValue === null)) && (this.$input.val() !== this.defaultValue);
        }
        else {
            return false;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    validate() {
        this.logger.info('Method [validate] -START-');
        return {
            valid: true,
            msg: null
        };
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.isBool;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.showHideCells;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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