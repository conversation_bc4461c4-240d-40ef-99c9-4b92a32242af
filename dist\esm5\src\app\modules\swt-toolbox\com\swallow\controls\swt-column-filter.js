/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { OperatorType, unsubscribeAllObservables, castToPromise, getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
import { Subject } from 'rxjs';
/** @type {?} */
var o = DOMPurify_;
var SwtColumnFilter = /** @class */ (function () {
    /**
     * Initialize the Filter
     */
    function SwtColumnFilter(translate, collectionService) {
        this.translate = translate;
        this.collectionService = collectionService;
        this.isFilled = false;
        this.lastSelectedValue = undefined;
        this.enableTranslateLabel = false;
        this.subscriptions = [];
        this.scroll = false;
        this._clearFilterTriggered = false;
        this.FilterInputSearch = false;
        this._shouldTriggerQuery = true;
        this.isOpened = false;
        this.checkboxContainer = null;
    }
    /**
     * @return {?}
     */
    SwtColumnFilter.prototype.refreshFilterValues = /**
     * @return {?}
     */
    function () {
        if (this.columnFilter) {
            /** @type {?} */
            var newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // Ensure clear button exists after refresh
            if (this.isMultipleSelect) {
                this.ensureClearButtonExists();
            }
        }
    };
    Object.defineProperty(SwtColumnFilter.prototype, "columnFilter", {
        /** Getter for the Column Filter itself */
        get: /**
         * Getter for the Column Filter itself
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "collectionOptions", {
        /** Getter for the Collection Options */
        get: /**
         * Getter for the Collection Options
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.collectionOptions;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "customStructure", {
        /** Getter for the Custom Structure if exist */
        get: /**
         * Getter for the Custom Structure if exist
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.customStructure;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "gridOptions", {
        /** Getter for the Grid Options pulled through the Grid Object */
        get: /**
         * Getter for the Grid Options pulled through the Grid Object
         * @protected
         * @return {?}
         */
        function () {
            return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "operator", {
        /** Getter for the filter operator */
        get: /**
         * Getter for the filter operator
         * @return {?}
         */
        function () {
            if (this.columnDef && this.columnDef.filter && this.columnDef.filter.operator) {
                return this.columnDef && this.columnDef.filter && this.columnDef.filter.operator;
            }
            return this.isMultipleSelect ? OperatorType.in : OperatorType.equal;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Initialize the filter template
     */
    /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    SwtColumnFilter.prototype.init = /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    function (args) {
        var _this = this;
        try {
            this.grid = args.grid;
            this.callback = args.callback;
            this.columnDef = args.columnDef;
            this.searchTerms = args.searchTerms || [];
            this.isMultipleSelect = this.columnDef['FilterType'] == "MultipleSelect" ? true : false;
            this.FilterInputSearch = this.columnDef['FilterInputSearch'];
            this.setFilterOptions();
            if (!this.grid || !this.columnDef || !this.columnFilter || (!this.columnFilter.collection && !this.columnFilter.collectionAsync)) {
                throw new Error("[Angular-SlickGrid] You need to pass a \"collection\" (or \"collectionAsync\") for the MultipleSelect/SingleSelect Filter to work correctly. Also each option should include a value/label pair (or value/labelKey when using Locale). For example:: { filter: model: Filters.multipleSelect, collection: [{ value: true, label: 'True' }, { value: false, label: 'False'}] }");
            }
            this.enableTranslateLabel = this.columnFilter.enableTranslateLabel;
            this.labelName = this.customStructure && this.customStructure.label || 'label';
            this.labelPrefixName = this.customStructure && this.customStructure.labelPrefix || 'labelPrefix';
            this.labelSuffixName = this.customStructure && this.customStructure.labelSuffix || 'labelSuffix';
            this.optionLabel = this.customStructure && this.customStructure.optionLabel || 'value';
            this.valueName = this.customStructure && this.customStructure.value || 'value';
            if (this.enableTranslateLabel && (!this.translate || typeof this.translate.instant !== 'function')) {
                throw new Error("[select-editor] The ngx-translate TranslateService is required for the Select Filter to work correctly");
            }
            // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
            // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
            /** @type {?} */
            var newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // on every Filter which have a "collection" or a "collectionAsync"
            // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
            // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
            /** @type {?} */
            var collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
            if (collectionAsync) {
                this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
            }
            // step 3, subscribe to the keyup event and run the callback when that happens
            this.$filterElm.keyup((/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                /** @type {?} */
                var value = e && e.target && e.target.value || '';
                /** @type {?} */
                var enableWhiteSpaceTrim = _this.gridOptions.enableFilterTrimWhiteSpace || _this.columnFilter.enableTrimWhiteSpace;
                if (typeof value === 'string' && enableWhiteSpaceTrim) {
                    value = value.trim();
                }
                if (_this._clearFilterTriggered) {
                    _this.callback(e, { columnDef: _this.columnDef, clearFilterTriggered: _this._clearFilterTriggered, shouldTriggerQuery: _this._shouldTriggerQuery });
                    _this.$filterElm.removeClass('filled');
                }
                else {
                    value === '' ? _this.$filterElm.removeClass('filled') : _this.$filterElm.addClass('filled');
                    _this.callback(e, { columnDef: _this.columnDef, searchTerms: [value], shouldTriggerQuery: _this._shouldTriggerQuery });
                }
                // reset both flags for next use
                _this._clearFilterTriggered = false;
                _this._shouldTriggerQuery = true;
            }));
        }
        catch (error) {
            console.error('method [ init] error :', error);
        }
    };
    /**
     * @return {?}
     */
    SwtColumnFilter.prototype.refreshHeaderOnly = /**
     * @return {?}
     */
    function () {
        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        /** @type {?} */
        var newCollection = this.columnFilter.collection || [];
        this.renderDomElement(newCollection);
        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        /** @type {?} */
        var collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if (collectionAsync) {
            this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
        }
        // Ensure clear button exists after header refresh
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    };
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
       * Clear the filter value
       */
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    SwtColumnFilter.prototype.clear = 
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    function (shouldTriggerQuery) {
        if (shouldTriggerQuery === void 0) { shouldTriggerQuery = true; }
        console.log("run clear function !");
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            // For multiselect, we need to clear selections using the multipleSelect API
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                this.$filterElm.multipleSelect('setSelects', []);
            }
            else {
                this.$filterElm.val('');
            }
            this.searchTerms = [];
            // For multiselect, trigger the onClose event which will call the callback
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                // Directly call the callback to clear the filter
                this.callback(undefined, {
                    columnDef: this.columnDef,
                    operator: this.operator,
                    searchTerms: [],
                    shouldTriggerQuery: true
                });
                // Remove filled class if present
                this.$filterElm.removeClass('filled');
            }
            else {
                // For regular input, trigger keyup which will call the callback
                this.$filterElm.trigger('keyup');
            }
        }
    };
    /**
     * destroy the filter
     */
    /**
     * destroy the filter
     * @return {?}
     */
    SwtColumnFilter.prototype.destroy = /**
     * destroy the filter
     * @return {?}
     */
    function () {
        if (this.$filterElm) {
            // remove event watcher
            this.$filterElm.off().remove();
        }
        this.$filterElm.multipleSelect('destroy');
        // also dispose of all Subscriptions
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    };
    /**
     * Set value(s) on the DOM element
     */
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    SwtColumnFilter.prototype.setValues = /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    function (values) {
        if (values) {
            values = Array.isArray(values) ? values : [values];
            this.$filterElm.multipleSelect('setSelects', values);
        }
    };
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    SwtColumnFilter.prototype.filterCollection = 
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    function (inputCollection) {
        /** @type {?} */
        var outputCollection = inputCollection;
        // user might want to filter certain items of the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionFilterBy) {
            /** @type {?} */
            var filterBy = this.columnFilter.collectionFilterBy;
            /** @type {?} */
            var filterCollectionBy = this.columnFilter.collectionOptions && this.columnFilter.collectionOptions.filterResultAfterEachPass || null;
            outputCollection = this.collectionService.filterCollection(outputCollection, filterBy, filterCollectionBy);
        }
        return outputCollection;
    };
    /**
     * user might want to sort the collection in a certain way
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    SwtColumnFilter.prototype.sortCollection = /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    function (inputCollection) {
        /** @type {?} */
        var outputCollection = inputCollection;
        // user might want to sort the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionSortBy) {
            /** @type {?} */
            var sortBy = this.columnFilter.collectionSortBy;
            outputCollection = this.collectionService.sortCollection(this.columnDef, outputCollection, sortBy, this.enableTranslateLabel);
        }
        return outputCollection;
    };
    /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    SwtColumnFilter.prototype.renderOptionsAsync = /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    function (collectionAsync) {
        return tslib_1.__awaiter(this, void 0, void 0, function () {
            var awaitedCollection;
            return tslib_1.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        awaitedCollection = [];
                        if (!collectionAsync) return [3 /*break*/, 2];
                        return [4 /*yield*/, castToPromise(collectionAsync)];
                    case 1:
                        awaitedCollection = _a.sent();
                        this.renderDomElementFromCollectionAsync(awaitedCollection);
                        // because we accept Promises & HttpClient Observable only execute once
                        // we will re-create an RxJs Subject which will replace the "collectionAsync" which got executed once anyway
                        // doing this provide the user a way to call a "collectionAsync.next()"
                        this.createCollectionAsyncSubject();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    /** Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it */
    /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    SwtColumnFilter.prototype.createCollectionAsyncSubject = /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var newCollectionAsync = new Subject();
        this.columnFilter.collectionAsync = newCollectionAsync;
        this.subscriptions.push(newCollectionAsync.subscribe((/**
         * @param {?} collection
         * @return {?}
         */
        function (collection) { return _this.renderDomElementFromCollectionAsync(collection); })));
    };
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     */
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    SwtColumnFilter.prototype.renderDomElementFromCollectionAsync = /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    function (collection) {
        if (this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('Something went wrong while trying to pull the collection from the "collectionAsync" call in the Select Filter, the collection is not a valid array.');
        }
        // copy over the array received from the async call to the "collection" as the new collection to use
        // this has to be BEFORE the `collectionObserver().subscribe` to avoid going into an infinite loop
        this.columnFilter.collection = collection;
        // recreate Multiple Select after getting async collection
        this.renderDomElement(collection);
        // Ensure clear button exists after async collection update
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    };
    /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    SwtColumnFilter.prototype.renderDomElement = /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    function (collection) {
        if (!Array.isArray(collection) && this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('The "collection" passed to the Select Filter is not a valid array');
        }
        // user can optionally add a blank entry at the beginning of the collection
        if (this.collectionOptions && this.collectionOptions.addBlankEntry) {
            collection.unshift(this.createBlankEntry());
        }
        /** @type {?} */
        var newCollection = collection;
        // user might want to filter and/or sort certain items of the collection
        newCollection = this.filterCollection(newCollection);
        newCollection = this.sortCollection(newCollection);
        // step 1, create HTML string template
        /** @type {?} */
        var filterTemplate = this.buildTemplateHtmlString(newCollection, this.searchTerms);
        // step 2, create the DOM Element of the filter & pre-load search terms
        // also subscribe to the onClose event
        this.createDomElement(filterTemplate);
    };
    /**
     * Create the HTML template as a string
     */
    /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    SwtColumnFilter.prototype.buildTemplateHtmlString = /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    function (optionCollection, searchTerms) {
        var _this = this;
        /** @type {?} */
        var options = '';
        /** @type {?} */
        var fieldId = this.columnDef && this.columnDef.id;
        /** @type {?} */
        var separatorBetweenLabels = this.collectionOptions && this.collectionOptions.separatorBetweenTextLabels || '';
        /** @type {?} */
        var isRenderHtmlEnabled = this.columnFilter && this.columnFilter.enableRenderHtml || false;
        /** @type {?} */
        var sanitizedOptions = this.gridOptions && this.gridOptions.sanitizeHtmlOptions || {};
        // collection could be an Array of Strings OR Objects
        if (optionCollection.every((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return typeof x === 'string'; }))) {
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            function (option) {
                /** @type {?} */
                var selected = (searchTerms.findIndex((/**
                 * @param {?} term
                 * @return {?}
                 */
                function (term) { return term === option; })) >= 0) ? 'selected' : '';
                options += "<option value=\"" + option + "\" label=\"" + option + "\" " + selected + ">" + option + "</option>";
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    _this.isFilled = true;
                }
            }));
        }
        else {
            // array of objects will require a label/value pair unless a customStructure is passed
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            function (option) {
                if (!option || (option[_this.labelName] === undefined && option.labelKey === undefined)) {
                    throw new Error("[select-filter] A collection with value/label (or value/labelKey when using Locale) is required to populate the Select list, for example:: { filter: model: Filters.multipleSelect, collection: [ { value: '1', label: 'One' } ]')");
                }
                /** @type {?} */
                var labelKey = (/** @type {?} */ ((option.labelKey || option[_this.labelName])));
                /** @type {?} */
                var selected = (searchTerms.length > 0) ? 'selected' : '';
                /** @type {?} */
                var labelText = ((option.labelKey || _this.enableTranslateLabel) && labelKey) ? _this.translate.instant(labelKey || ' ') : labelKey;
                /** @type {?} */
                var prefixText = option[_this.labelPrefixName] || '';
                /** @type {?} */
                var suffixText = option[_this.labelSuffixName] || '';
                /** @type {?} */
                var optionLabel = option[_this.optionLabel] || '';
                optionLabel = optionLabel.toString().replace(/\"/g, '\''); // replace double quotes by single quotes to avoid interfering with regular html
                // also translate prefix/suffix if enableTranslateLabel is true and text is a string
                prefixText = (_this.enableTranslateLabel && prefixText && typeof prefixText === 'string') ? _this.translate.instant(prefixText || ' ') : prefixText;
                suffixText = (_this.enableTranslateLabel && suffixText && typeof suffixText === 'string') ? _this.translate.instant(suffixText || ' ') : suffixText;
                optionLabel = (_this.enableTranslateLabel && optionLabel && typeof optionLabel === 'string') ? _this.translate.instant(optionLabel || ' ') : optionLabel;
                // add to a temp array for joining purpose and filter out empty text
                /** @type {?} */
                var tmpOptionArray = [prefixText, labelText, suffixText].filter((/**
                 * @param {?} text
                 * @return {?}
                 */
                function (text) { return text; }));
                /** @type {?} */
                var optionText = tmpOptionArray.join(separatorBetweenLabels);
                // if user specifically wants to render html text, he needs to opt-in else it will stripped out by default
                // also, the 3rd party lib will saninitze any html code unless it's encoded, so we'll do that
                if (isRenderHtmlEnabled) {
                    // sanitize any unauthorized html tags like script and others
                    // for the remaining allowed tags we'll permit all attributes
                    /** @type {?} */
                    var sanitizedText = DOMPurify_.sanitize(optionText, sanitizedOptions);
                    optionText = htmlEncode(sanitizedText);
                }
                // html text of each select option
                options += "<option value=\"" + option[_this.valueName] + "\" label=\"" + optionLabel + "\" " + selected + ">" + optionText + "</option>";
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    _this.isFilled = true;
                }
            }));
        }
        return "<select class=\"ms-filter search-filter filter-" + fieldId + "\" " + (this.isMultipleSelect ? 'multiple="multiple"' : '') + ">" + options + "</select>";
    };
    /** Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be */
    /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    SwtColumnFilter.prototype.createBlankEntry = /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    function () {
        var _a;
        /** @type {?} */
        var blankEntry = (_a = {},
            _a[this.labelName] = '',
            _a[this.valueName] = '',
            _a);
        if (this.labelPrefixName) {
            blankEntry[this.labelPrefixName] = '';
        }
        if (this.labelSuffixName) {
            blankEntry[this.labelSuffixName] = '';
        }
        return blankEntry;
    };
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @param filterTemplate
     */
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    SwtColumnFilter.prototype.createDomElement = /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    function (filterTemplate) {
        /** @type {?} */
        var fieldId = this.columnDef && this.columnDef.id;
        // provide the name attribute to the DOM element which will be needed to auto-adjust drop position (dropup / dropdown)
        this.elementName = "filter-" + fieldId;
        this.defaultOptions.name = this.elementName;
        /** @type {?} */
        var $headerElm = this.grid.getHeaderColumn(fieldId);
        // create the DOM element & add an ID and filter class
        this.$filterElm = $(filterTemplate);
        if (typeof this.$filterElm.multipleSelect !== 'function') {
            throw new Error("multiple-select.js was not found, make sure to modify your \"angular-cli.json\" file and include \"../node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js\" and it's css or SASS file");
        }
        this.$filterElm.attr('id', this.elementName);
        this.$filterElm.data('columnId', fieldId);
        // if there's a search term, we will add the "filled" class for styling purposes
        if (this.isFilled) {
            this.$filterElm.addClass('filled');
        }
        // append the new DOM element to the header row
        if (this.$filterElm && typeof this.$filterElm.appendTo === 'function') {
            this.$filterElm.appendTo($headerElm);
            $('.slick-header-column > .ms-parent').click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                event.stopPropagation();
            }));
        }
        // merge options & attach multiSelect
        /** @type {?} */
        var elementOptions = tslib_1.__assign({}, this.defaultOptions, this.columnFilter.filterOptions);
        this.filterElmOptions = tslib_1.__assign({}, this.defaultOptions, elementOptions);
        this.$filterElm = this.$filterElm.multipleSelect(this.filterElmOptions);
        // Ensure clear button is added for multiple select filters after DOM is ready
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    };
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method is called both during DOM creation and when the dropdown opens
     * to handle cases where the grid is refreshed or server-side sorting occurs
     */
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method is called both during DOM creation and when the dropdown opens
     * to handle cases where the grid is refreshed or server-side sorting occurs
     * @private
     * @return {?}
     */
    SwtColumnFilter.prototype.ensureClearButtonExists = /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method is called both during DOM creation and when the dropdown opens
     * to handle cases where the grid is refreshed or server-side sorting occurs
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        if (!this.isMultipleSelect) {
            return;
        }
        // Use a more robust approach to find the checkbox container
        // Try multiple times with increasing delays to handle async DOM updates
        /** @type {?} */
        var attempts = [0, 50, 100, 200, 500];
        // milliseconds
        /** @type {?} */
        var tryAddClearButton = (/**
         * @param {?} attemptIndex
         * @return {?}
         */
        function (attemptIndex) {
            if (attemptIndex >= attempts.length) {
                console.warn('Failed to add clear button after all attempts for column:', _this.columnDef.id);
                return;
            }
            setTimeout((/**
             * @return {?}
             */
            function () {
                // Find the container using multiple selectors to be more robust
                /** @type {?} */
                var container = $("div[name=filter-" + _this.columnDef.id + "]");
                // If not found, try alternative selectors
                if (!container.length) {
                    container = $(".ms-drop[data-name=filter-" + _this.columnDef.id + "]");
                }
                if (!container.length) {
                    container = $(".ms-drop:has(.ms-choice[data-name=filter-" + _this.columnDef.id + "])");
                }
                // If container exists and clear button doesn't exist, add it
                if (container.length && container.find('.clear-filter-btn').length === 0) {
                    // Create clear filter button with an inline SVG icon on the right
                    /** @type {?} */
                    var clearBtn = $("<button class=\"ms-ok-button clear-filter-btn\">\n                            <span style=\"display: inline-flex; align-items: center;\">\n                                Clear Filter\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" style=\"margin-left: 5px;\">\n                                    <path d=\"M3 4h18l-7 8v8h-4v-8l-7-8z\" stroke=\"currentColor\" stroke-width=\"1.5\" fill=\"none\"/>\n                                    <path d=\"M5 5L19 19\" stroke=\"red\" stroke-width=\"2\"/>\n                                </svg>\n                            </span>\n                        </button>");
                    // Insert at the very beginning of the dropdown container
                    container.prepend(clearBtn);
                    // Add click handler to clear button
                    clearBtn.on('click', (/**
                     * @param {?} e
                     * @return {?}
                     */
                    function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        // Call the clear method
                        _this.clear(true);
                        // Close the dropdown menu
                        if (_this.$filterElm && _this.$filterElm.multipleSelect) {
                            _this.$filterElm.multipleSelect('close');
                        }
                    }));
                    console.log('Clear button successfully added for column:', _this.columnDef.id);
                }
                else if (!container.length) {
                    // Container not found, try next attempt
                    tryAddClearButton(attemptIndex + 1);
                }
                // If container exists but clear button already exists, we're done
            }), attempts[attemptIndex]);
        });
        // Start the first attempt
        tryAddClearButton(0);
    };
    /**
     * @private
     * @return {?}
     */
    SwtColumnFilter.prototype.setFilterOptions = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            /** @type {?} */
            var clickHandler_1 = (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                /** @type {?} */
                var clickedCheckbox = event.target;
                /** @type {?} */
                var name = clickedCheckbox.dataset ? clickedCheckbox.dataset.name : "";
                if (_this.checkboxContainer && clickedCheckbox.value === "(NOT EMPTY)") {
                    _this.checkboxContainer.find("input[type=checkbox][value='(EMPTY)']").prop('checked', false);
                }
                if (_this.checkboxContainer && clickedCheckbox.value === "(EMPTY)") {
                    _this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                if (_this.checkboxContainer && name.includes("selectAllfilter")) {
                    _this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                // Add your desired code here to handle the checkbox click event
            });
            /** @type {?} */
            var options = {
                autoAdjustDropHeight: true,
                autoAdjustDropPosition: true,
                autoAdjustDropWidthByTextSize: true,
                container: 'body',
                filter: this.FilterInputSearch,
                maxHeight: 275,
                minWidth: this.columnDef.width,
                //-Fix M6549:try to enhance the design of the filter in case of short values (case of sign).
                filterAcceptOnEnter: true,
                single: !this.isMultipleSelect,
                //animate: 'slide',
                textTemplate: (/**
                 * @param {?} $elm
                 * @return {?}
                 */
                function ($elm) {
                    // render HTML code or not, by default it is sanitized and won't be rendered
                    /** @type {?} */
                    var isRenderHtmlEnabled = _this.columnDef && _this.columnDef.filter && _this.columnDef.filter.enableRenderHtml || false;
                    return isRenderHtmlEnabled ? $elm.text() : $elm.html();
                }),
                onClose: (/**
                 * @return {?}
                 */
                function () {
                    try {
                        //console.log('-----onClose----------', this.elementName);
                        // we will subscribe to the onClose event for triggering our callback
                        // also add/remove "filled" class for styling purposes
                        if ((!_this.isMultipleSelect && _this.lastSelectedValue != undefined) || _this.isMultipleSelect) {
                            /** @type {?} */
                            var selectedItems = _this.$filterElm.multipleSelect('getSelects');
                            if (Array.isArray(selectedItems) && selectedItems.length > 0 && _this.lastSelectedValue != _this.columnDef.params.grid.all) {
                                _this.isFilled = true;
                                _this.$filterElm.addClass('filled').siblings('div .search-filter').addClass('filled');
                            }
                            else {
                                selectedItems = [];
                                _this.isFilled = false;
                                _this.$filterElm.removeClass('filled').siblings('div .search-filter').removeClass('filled');
                            }
                            //-Fix M6549:Filter with an empty string doesn't exist.
                            if (selectedItems.length == 1 && selectedItems[0] == '')
                                selectedItems.push('');
                            _this.callback(undefined, { columnDef: _this.columnDef, operator: _this.operator, searchTerms: selectedItems, shouldTriggerQuery: true });
                            $(document).off('click');
                        }
                        if (event)
                            event.stopPropagation();
                        if (_this.checkboxContainer)
                            _this.checkboxContainer.find("input[type=checkbox]").off("click", clickHandler_1);
                    }
                    catch (error) {
                        console.error(' error', error);
                    }
                }),
                onOpen: (/**
                 * @return {?}
                 */
                function () {
                    console.log('-----onOpen----------', _this.columnDef.width);
                    if (!_this.isMultipleSelect) {
                        console.log('02020');
                        _this.lastSelectedValue = undefined;
                        //console.log('-----onOpen----------');
                        $("div[name^=filter-]").each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        function (index, item) {
                            /** @type {?} */
                            var name = $(item).attr('name');
                            if (name != _this.elementName && $(item).css('display') == "block") {
                                //console.log('-----onOpen---------- slideUp ')
                                $(item).slideUp();
                            }
                        }));
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + _this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + _this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            $("div[name=filter-" + _this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        /** @type {?} */
                        var ul = $($($("div[name=filter-" + _this.columnDef.id + "]").children()[0]));
                        $(document).on('click', (/**
                         * @param {?} event
                         * @return {?}
                         */
                        function (event) {
                            /** @type {?} */
                            var target = $(event.target);
                            if (!target.is(ul[0])) {
                                $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                                 * @return {?}
                                 */
                                function () {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        _this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        function (e) {
                            $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            function () {
                                $(document).off('click');
                            }));
                        }));
                        event.stopPropagation();
                    }
                    else {
                        _this.checkboxContainer = $("div[name=filter-" + _this.columnDef.id + "]");
                        // Ensure clear button exists using the centralized method
                        _this.ensureClearButtonExists();
                        // Add a small delay to ensure the DOM is ready for checkbox handlers
                        setTimeout((/**
                         * @return {?}
                         */
                        function () {
                            // Attach the click event handler to checkboxes
                            if (_this.checkboxContainer) {
                                _this.checkboxContainer.find("input[type=checkbox]").click(clickHandler_1);
                            }
                        }), 50);
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + _this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + _this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            // console.log("🚀 ~ file: swt-column-filter.ts:591 ~ setFilterOptions ~ newposLeft:", newposLeft)
                            $("div[name=filter-" + _this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        //-Fix M6549: Select a filter then scroll left, the filter is still open.
                        _this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        function (e) {
                            $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            function () {
                                $(document).off('click');
                            }));
                        }));
                        $('div[name^="filter-"]').each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        function (index, item) {
                            /** @type {?} */
                            var name = $(item).attr('name');
                            if (name != "filter-" + _this.columnDef.id && $(item).css('display') == "block") {
                                $(item).slideUp((/**
                                 * @return {?}
                                 */
                                function () {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        event.stopPropagation();
                    }
                }),
                onClick: (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.lastSelectedValue = event.label;
                    //Commented is not needed check if it's working fine 
                    /*if ( event.label == this.columnDef.params.grid.all ) {
                        if ( !this.columnDef.params.grid.paginationComponent.realPagination && this.columnDef.params.grid.GroupId == null){
                            this.clear(true);
                        }
                    }*/
                })
            };
            if (this.isMultipleSelect) {
                options.single = false;
                options.okButton = true;
                options.addTitle = true; // show tooltip of all selected items while hovering the filter
                options.countSelected = this.translate.instant('X_OF_Y_SELECTED');
                options.allSelected = this.translate.instant('ALL_SELECTED');
                options.selectAllText = this.translate.instant('ALL');
                options.selectAllDelimiter = ['', '']; // remove default square brackets of default text "[Select All]" => "Select All"
            }
            this.defaultOptions = options;
        }
        catch (error) {
            console.error('method [setFilterOptions] error :', error);
        }
    };
    return SwtColumnFilter;
}());
export { SwtColumnFilter };
if (false) {
    /**
     * DOM Element Name, useful for auto-detecting positioning (dropup / dropdown)
     * @type {?}
     */
    SwtColumnFilter.prototype.elementName;
    /**
     * Filter Multiple-Select options
     * @type {?}
     */
    SwtColumnFilter.prototype.filterElmOptions;
    /**
     * The JQuery DOM element
     * @type {?}
     */
    SwtColumnFilter.prototype.$filterElm;
    /** @type {?} */
    SwtColumnFilter.prototype.grid;
    /** @type {?} */
    SwtColumnFilter.prototype.searchTerms;
    /** @type {?} */
    SwtColumnFilter.prototype.columnDef;
    /** @type {?} */
    SwtColumnFilter.prototype.callback;
    /** @type {?} */
    SwtColumnFilter.prototype.defaultOptions;
    /** @type {?} */
    SwtColumnFilter.prototype.isFilled;
    /** @type {?} */
    SwtColumnFilter.prototype.lastSelectedValue;
    /** @type {?} */
    SwtColumnFilter.prototype.labelName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelPrefixName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelSuffixName;
    /** @type {?} */
    SwtColumnFilter.prototype.optionLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.valueName;
    /** @type {?} */
    SwtColumnFilter.prototype.enableTranslateLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.scroll;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._clearFilterTriggered;
    /** @type {?} */
    SwtColumnFilter.prototype.isMultipleSelect;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.FilterInputSearch;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.isOpened;
    /** @type {?} */
    SwtColumnFilter.prototype.checkboxContainer;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.translate;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.collectionService;
}
//# sourceMappingURL=data:application/json;base64,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