/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { OperatorType, unsubscribeAllObservables, castToPromise, getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
import { Subject } from 'rxjs';
/** @type {?} */
var o = DOMPurify_;
var SwtColumnFilter = /** @class */ (function () {
    /**
     * Initialize the Filter
     */
    function SwtColumnFilter(translate, collectionService) {
        this.translate = translate;
        this.collectionService = collectionService;
        this.isFilled = false;
        this.lastSelectedValue = undefined;
        this.enableTranslateLabel = false;
        this.subscriptions = [];
        this.scroll = false;
        this._clearFilterTriggered = false;
        this.FilterInputSearch = false;
        this._shouldTriggerQuery = true;
        this.isOpened = false;
        this.checkboxContainer = null;
    }
    /**
     * @return {?}
     */
    SwtColumnFilter.prototype.refreshFilterValues = /**
     * @return {?}
     */
    function () {
        if (this.columnFilter) {
            /** @type {?} */
            var newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // Ensure clear button exists after refresh
            if (this.isMultipleSelect) {
                this.ensureClearButtonExists();
            }
        }
    };
    Object.defineProperty(SwtColumnFilter.prototype, "columnFilter", {
        /** Getter for the Column Filter itself */
        get: /**
         * Getter for the Column Filter itself
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "collectionOptions", {
        /** Getter for the Collection Options */
        get: /**
         * Getter for the Collection Options
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.collectionOptions;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "customStructure", {
        /** Getter for the Custom Structure if exist */
        get: /**
         * Getter for the Custom Structure if exist
         * @protected
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.customStructure;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "gridOptions", {
        /** Getter for the Grid Options pulled through the Grid Object */
        get: /**
         * Getter for the Grid Options pulled through the Grid Object
         * @protected
         * @return {?}
         */
        function () {
            return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtColumnFilter.prototype, "operator", {
        /** Getter for the filter operator */
        get: /**
         * Getter for the filter operator
         * @return {?}
         */
        function () {
            if (this.columnDef && this.columnDef.filter && this.columnDef.filter.operator) {
                return this.columnDef && this.columnDef.filter && this.columnDef.filter.operator;
            }
            return this.isMultipleSelect ? OperatorType.in : OperatorType.equal;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Initialize the filter template
     */
    /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    SwtColumnFilter.prototype.init = /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    function (args) {
        var _this = this;
        try {
            this.grid = args.grid;
            this.callback = args.callback;
            this.columnDef = args.columnDef;
            this.searchTerms = args.searchTerms || [];
            this.isMultipleSelect = this.columnDef['FilterType'] == "MultipleSelect" ? true : false;
            this.FilterInputSearch = this.columnDef['FilterInputSearch'];
            this.setFilterOptions();
            if (!this.grid || !this.columnDef || !this.columnFilter || (!this.columnFilter.collection && !this.columnFilter.collectionAsync)) {
                throw new Error("[Angular-SlickGrid] You need to pass a \"collection\" (or \"collectionAsync\") for the MultipleSelect/SingleSelect Filter to work correctly. Also each option should include a value/label pair (or value/labelKey when using Locale). For example:: { filter: model: Filters.multipleSelect, collection: [{ value: true, label: 'True' }, { value: false, label: 'False'}] }");
            }
            this.enableTranslateLabel = this.columnFilter.enableTranslateLabel;
            this.labelName = this.customStructure && this.customStructure.label || 'label';
            this.labelPrefixName = this.customStructure && this.customStructure.labelPrefix || 'labelPrefix';
            this.labelSuffixName = this.customStructure && this.customStructure.labelSuffix || 'labelSuffix';
            this.optionLabel = this.customStructure && this.customStructure.optionLabel || 'value';
            this.valueName = this.customStructure && this.customStructure.value || 'value';
            if (this.enableTranslateLabel && (!this.translate || typeof this.translate.instant !== 'function')) {
                throw new Error("[select-editor] The ngx-translate TranslateService is required for the Select Filter to work correctly");
            }
            // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
            // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
            /** @type {?} */
            var newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // on every Filter which have a "collection" or a "collectionAsync"
            // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
            // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
            /** @type {?} */
            var collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
            if (collectionAsync) {
                this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
            }
            // step 3, subscribe to the keyup event and run the callback when that happens
            this.$filterElm.keyup((/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                /** @type {?} */
                var value = e && e.target && e.target.value || '';
                /** @type {?} */
                var enableWhiteSpaceTrim = _this.gridOptions.enableFilterTrimWhiteSpace || _this.columnFilter.enableTrimWhiteSpace;
                if (typeof value === 'string' && enableWhiteSpaceTrim) {
                    value = value.trim();
                }
                if (_this._clearFilterTriggered) {
                    _this.callback(e, { columnDef: _this.columnDef, clearFilterTriggered: _this._clearFilterTriggered, shouldTriggerQuery: _this._shouldTriggerQuery });
                    _this.$filterElm.removeClass('filled');
                }
                else {
                    value === '' ? _this.$filterElm.removeClass('filled') : _this.$filterElm.addClass('filled');
                    _this.callback(e, { columnDef: _this.columnDef, searchTerms: [value], shouldTriggerQuery: _this._shouldTriggerQuery });
                }
                // reset both flags for next use
                _this._clearFilterTriggered = false;
                _this._shouldTriggerQuery = true;
            }));
        }
        catch (error) {
            console.error('method [ init] error :', error);
        }
    };
    /**
     * @return {?}
     */
    SwtColumnFilter.prototype.refreshHeaderOnly = /**
     * @return {?}
     */
    function () {
        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        /** @type {?} */
        var newCollection = this.columnFilter.collection || [];
        this.renderDomElement(newCollection);
        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        /** @type {?} */
        var collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if (collectionAsync) {
            this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
        }
        // Ensure clear button exists after header refresh
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    };
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
       * Clear the filter value
       */
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    SwtColumnFilter.prototype.clear = 
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    function (shouldTriggerQuery) {
        if (shouldTriggerQuery === void 0) { shouldTriggerQuery = true; }
        console.log("run clear function !");
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            // For multiselect, we need to clear selections using the multipleSelect API
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                this.$filterElm.multipleSelect('setSelects', []);
            }
            else {
                this.$filterElm.val('');
            }
            this.searchTerms = [];
            // For multiselect, trigger the onClose event which will call the callback
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                // Directly call the callback to clear the filter
                this.callback(undefined, {
                    columnDef: this.columnDef,
                    operator: this.operator,
                    searchTerms: [],
                    shouldTriggerQuery: true
                });
                // Remove filled class if present
                this.$filterElm.removeClass('filled');
            }
            else {
                // For regular input, trigger keyup which will call the callback
                this.$filterElm.trigger('keyup');
            }
        }
    };
    /**
     * destroy the filter
     */
    /**
     * destroy the filter
     * @return {?}
     */
    SwtColumnFilter.prototype.destroy = /**
     * destroy the filter
     * @return {?}
     */
    function () {
        if (this.$filterElm) {
            // remove event watcher
            this.$filterElm.off().remove();
        }
        this.$filterElm.multipleSelect('destroy');
        // also dispose of all Subscriptions
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    };
    /**
     * Set value(s) on the DOM element
     */
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    SwtColumnFilter.prototype.setValues = /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    function (values) {
        if (values) {
            values = Array.isArray(values) ? values : [values];
            this.$filterElm.multipleSelect('setSelects', values);
        }
    };
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    SwtColumnFilter.prototype.filterCollection = 
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    function (inputCollection) {
        /** @type {?} */
        var outputCollection = inputCollection;
        // user might want to filter certain items of the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionFilterBy) {
            /** @type {?} */
            var filterBy = this.columnFilter.collectionFilterBy;
            /** @type {?} */
            var filterCollectionBy = this.columnFilter.collectionOptions && this.columnFilter.collectionOptions.filterResultAfterEachPass || null;
            outputCollection = this.collectionService.filterCollection(outputCollection, filterBy, filterCollectionBy);
        }
        return outputCollection;
    };
    /**
     * user might want to sort the collection in a certain way
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    SwtColumnFilter.prototype.sortCollection = /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    function (inputCollection) {
        /** @type {?} */
        var outputCollection = inputCollection;
        // user might want to sort the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionSortBy) {
            /** @type {?} */
            var sortBy = this.columnFilter.collectionSortBy;
            outputCollection = this.collectionService.sortCollection(this.columnDef, outputCollection, sortBy, this.enableTranslateLabel);
        }
        return outputCollection;
    };
    /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    SwtColumnFilter.prototype.renderOptionsAsync = /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    function (collectionAsync) {
        return tslib_1.__awaiter(this, void 0, void 0, function () {
            var awaitedCollection;
            return tslib_1.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        awaitedCollection = [];
                        if (!collectionAsync) return [3 /*break*/, 2];
                        return [4 /*yield*/, castToPromise(collectionAsync)];
                    case 1:
                        awaitedCollection = _a.sent();
                        this.renderDomElementFromCollectionAsync(awaitedCollection);
                        // because we accept Promises & HttpClient Observable only execute once
                        // we will re-create an RxJs Subject which will replace the "collectionAsync" which got executed once anyway
                        // doing this provide the user a way to call a "collectionAsync.next()"
                        this.createCollectionAsyncSubject();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    /** Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it */
    /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    SwtColumnFilter.prototype.createCollectionAsyncSubject = /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var newCollectionAsync = new Subject();
        this.columnFilter.collectionAsync = newCollectionAsync;
        this.subscriptions.push(newCollectionAsync.subscribe((/**
         * @param {?} collection
         * @return {?}
         */
        function (collection) { return _this.renderDomElementFromCollectionAsync(collection); })));
    };
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     */
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    SwtColumnFilter.prototype.renderDomElementFromCollectionAsync = /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    function (collection) {
        if (this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('Something went wrong while trying to pull the collection from the "collectionAsync" call in the Select Filter, the collection is not a valid array.');
        }
        // copy over the array received from the async call to the "collection" as the new collection to use
        // this has to be BEFORE the `collectionObserver().subscribe` to avoid going into an infinite loop
        this.columnFilter.collection = collection;
        // recreate Multiple Select after getting async collection
        this.renderDomElement(collection);
        // Ensure clear button exists after async collection update
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    };
    /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    SwtColumnFilter.prototype.renderDomElement = /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    function (collection) {
        if (!Array.isArray(collection) && this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('The "collection" passed to the Select Filter is not a valid array');
        }
        // user can optionally add a blank entry at the beginning of the collection
        if (this.collectionOptions && this.collectionOptions.addBlankEntry) {
            collection.unshift(this.createBlankEntry());
        }
        /** @type {?} */
        var newCollection = collection;
        // user might want to filter and/or sort certain items of the collection
        newCollection = this.filterCollection(newCollection);
        newCollection = this.sortCollection(newCollection);
        // step 1, create HTML string template
        /** @type {?} */
        var filterTemplate = this.buildTemplateHtmlString(newCollection, this.searchTerms);
        // step 2, create the DOM Element of the filter & pre-load search terms
        // also subscribe to the onClose event
        this.createDomElement(filterTemplate);
    };
    /**
     * Create the HTML template as a string
     */
    /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    SwtColumnFilter.prototype.buildTemplateHtmlString = /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    function (optionCollection, searchTerms) {
        var _this = this;
        /** @type {?} */
        var options = '';
        /** @type {?} */
        var fieldId = this.columnDef && this.columnDef.id;
        /** @type {?} */
        var separatorBetweenLabels = this.collectionOptions && this.collectionOptions.separatorBetweenTextLabels || '';
        /** @type {?} */
        var isRenderHtmlEnabled = this.columnFilter && this.columnFilter.enableRenderHtml || false;
        /** @type {?} */
        var sanitizedOptions = this.gridOptions && this.gridOptions.sanitizeHtmlOptions || {};
        // collection could be an Array of Strings OR Objects
        if (optionCollection.every((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return typeof x === 'string'; }))) {
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            function (option) {
                /** @type {?} */
                var selected = (searchTerms.findIndex((/**
                 * @param {?} term
                 * @return {?}
                 */
                function (term) { return term === option; })) >= 0) ? 'selected' : '';
                options += "<option value=\"" + option + "\" label=\"" + option + "\" " + selected + ">" + option + "</option>";
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    _this.isFilled = true;
                }
            }));
        }
        else {
            // array of objects will require a label/value pair unless a customStructure is passed
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            function (option) {
                if (!option || (option[_this.labelName] === undefined && option.labelKey === undefined)) {
                    throw new Error("[select-filter] A collection with value/label (or value/labelKey when using Locale) is required to populate the Select list, for example:: { filter: model: Filters.multipleSelect, collection: [ { value: '1', label: 'One' } ]')");
                }
                /** @type {?} */
                var labelKey = (/** @type {?} */ ((option.labelKey || option[_this.labelName])));
                /** @type {?} */
                var selected = (searchTerms.length > 0) ? 'selected' : '';
                /** @type {?} */
                var labelText = ((option.labelKey || _this.enableTranslateLabel) && labelKey) ? _this.translate.instant(labelKey || ' ') : labelKey;
                /** @type {?} */
                var prefixText = option[_this.labelPrefixName] || '';
                /** @type {?} */
                var suffixText = option[_this.labelSuffixName] || '';
                /** @type {?} */
                var optionLabel = option[_this.optionLabel] || '';
                optionLabel = optionLabel.toString().replace(/\"/g, '\''); // replace double quotes by single quotes to avoid interfering with regular html
                // also translate prefix/suffix if enableTranslateLabel is true and text is a string
                prefixText = (_this.enableTranslateLabel && prefixText && typeof prefixText === 'string') ? _this.translate.instant(prefixText || ' ') : prefixText;
                suffixText = (_this.enableTranslateLabel && suffixText && typeof suffixText === 'string') ? _this.translate.instant(suffixText || ' ') : suffixText;
                optionLabel = (_this.enableTranslateLabel && optionLabel && typeof optionLabel === 'string') ? _this.translate.instant(optionLabel || ' ') : optionLabel;
                // add to a temp array for joining purpose and filter out empty text
                /** @type {?} */
                var tmpOptionArray = [prefixText, labelText, suffixText].filter((/**
                 * @param {?} text
                 * @return {?}
                 */
                function (text) { return text; }));
                /** @type {?} */
                var optionText = tmpOptionArray.join(separatorBetweenLabels);
                // if user specifically wants to render html text, he needs to opt-in else it will stripped out by default
                // also, the 3rd party lib will saninitze any html code unless it's encoded, so we'll do that
                if (isRenderHtmlEnabled) {
                    // sanitize any unauthorized html tags like script and others
                    // for the remaining allowed tags we'll permit all attributes
                    /** @type {?} */
                    var sanitizedText = DOMPurify_.sanitize(optionText, sanitizedOptions);
                    optionText = htmlEncode(sanitizedText);
                }
                // html text of each select option
                options += "<option value=\"" + option[_this.valueName] + "\" label=\"" + optionLabel + "\" " + selected + ">" + optionText + "</option>";
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    _this.isFilled = true;
                }
            }));
        }
        return "<select class=\"ms-filter search-filter filter-" + fieldId + "\" " + (this.isMultipleSelect ? 'multiple="multiple"' : '') + ">" + options + "</select>";
    };
    /** Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be */
    /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    SwtColumnFilter.prototype.createBlankEntry = /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    function () {
        var _a;
        /** @type {?} */
        var blankEntry = (_a = {},
            _a[this.labelName] = '',
            _a[this.valueName] = '',
            _a);
        if (this.labelPrefixName) {
            blankEntry[this.labelPrefixName] = '';
        }
        if (this.labelSuffixName) {
            blankEntry[this.labelSuffixName] = '';
        }
        return blankEntry;
    };
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @param filterTemplate
     */
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    SwtColumnFilter.prototype.createDomElement = /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    function (filterTemplate) {
        /** @type {?} */
        var fieldId = this.columnDef && this.columnDef.id;
        // provide the name attribute to the DOM element which will be needed to auto-adjust drop position (dropup / dropdown)
        this.elementName = "filter-" + fieldId;
        this.defaultOptions.name = this.elementName;
        /** @type {?} */
        var $headerElm = this.grid.getHeaderColumn(fieldId);
        // create the DOM element & add an ID and filter class
        this.$filterElm = $(filterTemplate);
        if (typeof this.$filterElm.multipleSelect !== 'function') {
            throw new Error("multiple-select.js was not found, make sure to modify your \"angular-cli.json\" file and include \"../node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js\" and it's css or SASS file");
        }
        this.$filterElm.attr('id', this.elementName);
        this.$filterElm.data('columnId', fieldId);
        // if there's a search term, we will add the "filled" class for styling purposes
        if (this.isFilled) {
            this.$filterElm.addClass('filled');
        }
        // append the new DOM element to the header row
        if (this.$filterElm && typeof this.$filterElm.appendTo === 'function') {
            this.$filterElm.appendTo($headerElm);
            $('.slick-header-column > .ms-parent').click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                event.stopPropagation();
            }));
        }
        // merge options & attach multiSelect
        /** @type {?} */
        var elementOptions = tslib_1.__assign({}, this.defaultOptions, this.columnFilter.filterOptions);
        this.filterElmOptions = tslib_1.__assign({}, this.defaultOptions, elementOptions);
        this.$filterElm = this.$filterElm.multipleSelect(this.filterElmOptions);
        // Ensure clear button is added for multiple select filters after DOM is ready
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
            // Also start monitoring to catch cases where the filter is recreated
            this.monitorAndEnsureClearButton();
        }
    };
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method checks for ms-select-all element as a trigger and adds clear button if missing
     * Called whenever the filter is recreated or refreshed
     */
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method checks for ms-select-all element as a trigger and adds clear button if missing
     * Called whenever the filter is recreated or refreshed
     * @private
     * @return {?}
     */
    SwtColumnFilter.prototype.ensureClearButtonExists = /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method checks for ms-select-all element as a trigger and adds clear button if missing
     * Called whenever the filter is recreated or refreshed
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        if (!this.isMultipleSelect) {
            return;
        }
        // Use a more robust approach to find the checkbox container
        // Try multiple times with increasing delays to handle async DOM updates
        /** @type {?} */
        var attempts = [0, 50, 100, 200, 500, 1000];
        // milliseconds
        /** @type {?} */
        var tryAddClearButton = (/**
         * @param {?} attemptIndex
         * @return {?}
         */
        function (attemptIndex) {
            if (attemptIndex >= attempts.length) {
                console.warn('Failed to add clear button after all attempts for column:', _this.columnDef.id);
                return;
            }
            setTimeout((/**
             * @return {?}
             */
            function () {
                // Find the container using multiple selectors to be more robust
                /** @type {?} */
                var container = $("div[name=filter-" + _this.columnDef.id + "]");
                // If not found, try alternative selectors
                if (!container.length) {
                    container = $(".ms-drop[data-name=filter-" + _this.columnDef.id + "]");
                }
                if (!container.length) {
                    container = $(".ms-drop:has(.ms-choice[data-name=filter-" + _this.columnDef.id + "])");
                }
                // Check if ms-select-all exists as a trigger - this indicates the dropdown is properly initialized
                /** @type {?} */
                var hasSelectAll = container.find('.ms-select-all').length > 0;
                console.log("🚀 ~ setTimeout ~ hasSelectAll:", hasSelectAll);
                // If container exists, has ms-select-all, and clear button doesn't exist, add it
                // Create clear filter button with an inline SVG icon on the right
                /** @type {?} */
                var clearBtn = $("<button class=\"ms-ok-button clear-filter-btn\">\n                            <span style=\"display: inline-flex; align-items: center;\">\n                                Clear Filter\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" style=\"margin-left: 5px;\">\n                                    <path d=\"M3 4h18l-7 8v8h-4v-8l-7-8z\" stroke=\"currentColor\" stroke-width=\"1.5\" fill=\"none\"/>\n                                    <path d=\"M5 5L19 19\" stroke=\"red\" stroke-width=\"2\"/>\n                                </svg>\n                            </span>\n                        </button>");
                // Insert at the very beginning of the dropdown container
                container.prepend(clearBtn);
                // Add click handler to clear button
                clearBtn.on('click', (/**
                 * @param {?} e
                 * @return {?}
                 */
                function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // Call the clear method
                    _this.clear(true);
                    // Close the dropdown menu
                    if (_this.$filterElm && _this.$filterElm.multipleSelect) {
                        _this.$filterElm.multipleSelect('close');
                    }
                }));
                console.log('Clear button successfully added for column:', _this.columnDef.id);
                // If container exists, has select-all, but clear button already exists, we're done
            }), attempts[attemptIndex]);
        });
        // Start the first attempt
        tryAddClearButton(0);
    };
    /**
     * Monitors for the existence of ms-select-all and ensures clear button is added
     * This is a more aggressive approach for cases where the filter is frequently recreated
     */
    /**
     * Monitors for the existence of ms-select-all and ensures clear button is added
     * This is a more aggressive approach for cases where the filter is frequently recreated
     * @private
     * @return {?}
     */
    SwtColumnFilter.prototype.monitorAndEnsureClearButton = /**
     * Monitors for the existence of ms-select-all and ensures clear button is added
     * This is a more aggressive approach for cases where the filter is frequently recreated
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        if (!this.isMultipleSelect) {
            return;
        }
        /** @type {?} */
        var checkInterval = setInterval((/**
         * @return {?}
         */
        function () {
            /** @type {?} */
            var container = $("div[name=filter-" + _this.columnDef.id + "]");
            /** @type {?} */
            var hasSelectAll = container.find('.ms-select-all').length > 0;
            /** @type {?} */
            var hasClearButton = container.find('.clear-filter-btn').length > 0;
            // If we have select-all but no clear button, add it
            if (container.length && hasSelectAll && !hasClearButton) {
                // Create clear filter button
                /** @type {?} */
                var clearBtn = $("<button class=\"ms-ok-button clear-filter-btn\">\n                        <span style=\"display: inline-flex; align-items: center;\">\n                            Clear Filter\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" style=\"margin-left: 5px;\">\n                                <path d=\"M3 4h18l-7 8v8h-4v-8l-7-8z\" stroke=\"currentColor\" stroke-width=\"1.5\" fill=\"none\"/>\n                                <path d=\"M5 5L19 19\" stroke=\"red\" stroke-width=\"2\"/>\n                            </svg>\n                        </span>\n                    </button>");
                // Insert at the very beginning of the dropdown container
                container.prepend(clearBtn);
                // Add click handler to clear button
                clearBtn.on('click', (/**
                 * @param {?} e
                 * @return {?}
                 */
                function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // Call the clear method
                    _this.clear(true);
                    // Close the dropdown menu
                    if (_this.$filterElm && _this.$filterElm.multipleSelect) {
                        _this.$filterElm.multipleSelect('close');
                    }
                }));
                console.log('Clear button added via monitoring for column:', _this.columnDef.id);
            }
            // If container no longer exists, stop monitoring
            if (!container.length) {
                clearInterval(checkInterval);
            }
        }), 100);
        // Stop monitoring after 10 seconds to prevent memory leaks
        setTimeout((/**
         * @return {?}
         */
        function () {
            clearInterval(checkInterval);
        }), 10000);
    };
    /**
     * @private
     * @return {?}
     */
    SwtColumnFilter.prototype.setFilterOptions = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            /** @type {?} */
            var clickHandler_1 = (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                /** @type {?} */
                var clickedCheckbox = event.target;
                /** @type {?} */
                var name = clickedCheckbox.dataset ? clickedCheckbox.dataset.name : "";
                if (_this.checkboxContainer && clickedCheckbox.value === "(NOT EMPTY)") {
                    _this.checkboxContainer.find("input[type=checkbox][value='(EMPTY)']").prop('checked', false);
                }
                if (_this.checkboxContainer && clickedCheckbox.value === "(EMPTY)") {
                    _this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                if (_this.checkboxContainer && name.includes("selectAllfilter")) {
                    _this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                // Add your desired code here to handle the checkbox click event
            });
            /** @type {?} */
            var options = {
                autoAdjustDropHeight: true,
                autoAdjustDropPosition: true,
                autoAdjustDropWidthByTextSize: true,
                container: 'body',
                filter: this.FilterInputSearch,
                maxHeight: 275,
                minWidth: this.columnDef.width,
                //-Fix M6549:try to enhance the design of the filter in case of short values (case of sign).
                filterAcceptOnEnter: true,
                single: !this.isMultipleSelect,
                //animate: 'slide',
                textTemplate: (/**
                 * @param {?} $elm
                 * @return {?}
                 */
                function ($elm) {
                    // render HTML code or not, by default it is sanitized and won't be rendered
                    /** @type {?} */
                    var isRenderHtmlEnabled = _this.columnDef && _this.columnDef.filter && _this.columnDef.filter.enableRenderHtml || false;
                    return isRenderHtmlEnabled ? $elm.text() : $elm.html();
                }),
                onClose: (/**
                 * @return {?}
                 */
                function () {
                    try {
                        //console.log('-----onClose----------', this.elementName);
                        // we will subscribe to the onClose event for triggering our callback
                        // also add/remove "filled" class for styling purposes
                        if ((!_this.isMultipleSelect && _this.lastSelectedValue != undefined) || _this.isMultipleSelect) {
                            /** @type {?} */
                            var selectedItems = _this.$filterElm.multipleSelect('getSelects');
                            if (Array.isArray(selectedItems) && selectedItems.length > 0 && _this.lastSelectedValue != _this.columnDef.params.grid.all) {
                                _this.isFilled = true;
                                _this.$filterElm.addClass('filled').siblings('div .search-filter').addClass('filled');
                            }
                            else {
                                selectedItems = [];
                                _this.isFilled = false;
                                _this.$filterElm.removeClass('filled').siblings('div .search-filter').removeClass('filled');
                            }
                            //-Fix M6549:Filter with an empty string doesn't exist.
                            if (selectedItems.length == 1 && selectedItems[0] == '')
                                selectedItems.push('');
                            _this.callback(undefined, { columnDef: _this.columnDef, operator: _this.operator, searchTerms: selectedItems, shouldTriggerQuery: true });
                            $(document).off('click');
                        }
                        if (event)
                            event.stopPropagation();
                        if (_this.checkboxContainer)
                            _this.checkboxContainer.find("input[type=checkbox]").off("click", clickHandler_1);
                    }
                    catch (error) {
                        console.error(' error', error);
                    }
                }),
                onOpen: (/**
                 * @return {?}
                 */
                function () {
                    console.log('-----onOpen----------', _this.columnDef.width);
                    if (!_this.isMultipleSelect) {
                        console.log('02020');
                        _this.lastSelectedValue = undefined;
                        //console.log('-----onOpen----------');
                        $("div[name^=filter-]").each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        function (index, item) {
                            /** @type {?} */
                            var name = $(item).attr('name');
                            if (name != _this.elementName && $(item).css('display') == "block") {
                                //console.log('-----onOpen---------- slideUp ')
                                $(item).slideUp();
                            }
                        }));
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + _this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + _this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            $("div[name=filter-" + _this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        /** @type {?} */
                        var ul = $($($("div[name=filter-" + _this.columnDef.id + "]").children()[0]));
                        $(document).on('click', (/**
                         * @param {?} event
                         * @return {?}
                         */
                        function (event) {
                            /** @type {?} */
                            var target = $(event.target);
                            if (!target.is(ul[0])) {
                                $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                                 * @return {?}
                                 */
                                function () {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        _this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        function (e) {
                            $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            function () {
                                $(document).off('click');
                            }));
                        }));
                        event.stopPropagation();
                    }
                    else {
                        _this.checkboxContainer = $("div[name=filter-" + _this.columnDef.id + "]");
                        // Ensure clear button exists using the centralized method
                        _this.ensureClearButtonExists();
                        // Also start monitoring to catch any recreation of the filter
                        _this.monitorAndEnsureClearButton();
                        // Add a small delay to ensure the DOM is ready for checkbox handlers
                        setTimeout((/**
                         * @return {?}
                         */
                        function () {
                            // Attach the click event handler to checkboxes
                            if (_this.checkboxContainer) {
                                _this.checkboxContainer.find("input[type=checkbox]").click(clickHandler_1);
                            }
                        }), 50);
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + _this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + _this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            // console.log("🚀 ~ file: swt-column-filter.ts:591 ~ setFilterOptions ~ newposLeft:", newposLeft)
                            $("div[name=filter-" + _this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        //-Fix M6549: Select a filter then scroll left, the filter is still open.
                        _this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        function (e) {
                            $("div[name=filter-" + _this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            function () {
                                $(document).off('click');
                            }));
                        }));
                        $('div[name^="filter-"]').each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        function (index, item) {
                            /** @type {?} */
                            var name = $(item).attr('name');
                            if (name != "filter-" + _this.columnDef.id && $(item).css('display') == "block") {
                                $(item).slideUp((/**
                                 * @return {?}
                                 */
                                function () {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        event.stopPropagation();
                    }
                }),
                onClick: (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.lastSelectedValue = event.label;
                    //Commented is not needed check if it's working fine 
                    /*if ( event.label == this.columnDef.params.grid.all ) {
                        if ( !this.columnDef.params.grid.paginationComponent.realPagination && this.columnDef.params.grid.GroupId == null){
                            this.clear(true);
                        }
                    }*/
                })
            };
            if (this.isMultipleSelect) {
                options.single = false;
                options.okButton = true;
                options.addTitle = true; // show tooltip of all selected items while hovering the filter
                options.countSelected = this.translate.instant('X_OF_Y_SELECTED');
                options.allSelected = this.translate.instant('ALL_SELECTED');
                options.selectAllText = this.translate.instant('ALL');
                options.selectAllDelimiter = ['', '']; // remove default square brackets of default text "[Select All]" => "Select All"
            }
            this.defaultOptions = options;
        }
        catch (error) {
            console.error('method [setFilterOptions] error :', error);
        }
    };
    return SwtColumnFilter;
}());
export { SwtColumnFilter };
if (false) {
    /**
     * DOM Element Name, useful for auto-detecting positioning (dropup / dropdown)
     * @type {?}
     */
    SwtColumnFilter.prototype.elementName;
    /**
     * Filter Multiple-Select options
     * @type {?}
     */
    SwtColumnFilter.prototype.filterElmOptions;
    /**
     * The JQuery DOM element
     * @type {?}
     */
    SwtColumnFilter.prototype.$filterElm;
    /** @type {?} */
    SwtColumnFilter.prototype.grid;
    /** @type {?} */
    SwtColumnFilter.prototype.searchTerms;
    /** @type {?} */
    SwtColumnFilter.prototype.columnDef;
    /** @type {?} */
    SwtColumnFilter.prototype.callback;
    /** @type {?} */
    SwtColumnFilter.prototype.defaultOptions;
    /** @type {?} */
    SwtColumnFilter.prototype.isFilled;
    /** @type {?} */
    SwtColumnFilter.prototype.lastSelectedValue;
    /** @type {?} */
    SwtColumnFilter.prototype.labelName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelPrefixName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelSuffixName;
    /** @type {?} */
    SwtColumnFilter.prototype.optionLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.valueName;
    /** @type {?} */
    SwtColumnFilter.prototype.enableTranslateLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.scroll;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._clearFilterTriggered;
    /** @type {?} */
    SwtColumnFilter.prototype.isMultipleSelect;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.FilterInputSearch;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.isOpened;
    /** @type {?} */
    SwtColumnFilter.prototype.checkboxContainer;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.translate;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.collectionService;
}
//# sourceMappingURL=data:application/json;base64,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