/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
export class XML {
    /**
     * @param {?} xmlString
     */
    constructor(xmlString) {
        this.xmlString = xmlString;
        this.xpath = require('xpath');
        this.dom = require('xmldom').DOMParser;
        this.fromXMLString();
    }
    /**
     * This method is used to convert the entered String (xmlString) to XML .
     * returns XML
     * @return {?}
     */
    fromXMLString() {
        try {
            /** @type {?} */
            var doc = new this.dom().parseFromString(this.xmlString, 'text/xml');
            this.xml = this.xpath.select("/" + doc.childNodes[0].nodeName, doc);
            return this.xml;
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    toXMLString() {
        try {
            return this.xml.toString();
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    toString() {
        try {
            return this.xml.toString();
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * appends xml or string to the existing xml.
     * @param {?} value : it can be string or xml(SwtXml)
     * @return {?}
     */
    appendChild(value) {
        try {
            /** @type {?} */
            var child = new this.dom().parseFromString(value.toString(), 'text/xml');
            this.xml[0].appendChild(child);
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     *
     * @param {?} xmlToDelete
     * @return {?}
     */
    removeChild(xmlToDelete) {
        for (var index = 0; index < this.children().length; index++) {
            if (this.children()[index].toString() == xmlToDelete.toString()) {
                this.xml[0].removeChild(this.children()[index]);
                return true;
            }
        }
        return false;
    }
    /**
     *
     * @return {?}
     */
    parent() {
        return (this.xml[0].parentNode);
    }
    /**
     *
     * @return {?}
     */
    children() {
        return (this.xml[0].childNodes);
    }
}
XML.decorators = [
    { type: Injectable }
];
/** @nocollapse */
XML.ctorParameters = () => [
    { type: String }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xpath;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.dom;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xml;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xmlString;
}
export class XMLListCollection {
    constructor() {
        this.list = new XML("</>");
    }
    /**
     * returns string
     * @return {?}
     */
    toString() {
        try {
            return this.list.toString();
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * @return {?}
     */
    toXMLString() {
        try {
            return this.list.toString();
        }
        catch (error) {
            console.error(error);
        }
    }
}
XMLListCollection.decorators = [
    { type: Injectable }
];
/** @nocollapse */
XMLListCollection.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    XMLListCollection.prototype.list;
}
//# sourceMappingURL=data:application/json;base64,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