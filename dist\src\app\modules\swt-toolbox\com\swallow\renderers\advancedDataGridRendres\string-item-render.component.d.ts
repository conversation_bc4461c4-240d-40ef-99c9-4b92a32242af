import { ElementRef, OnInit } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { IItemRender } from "./iitem-render";
import { CommonService } from "../../utils/common.service";
export declare class StringItemRender extends UIComponent implements OnIni<PERSON>, IItemRender {
    private stringelement;
    private common;
    text: string;
    color: string;
    type: string;
    id: number;
    constructor(stringelement: ElementRef, common: CommonService);
    ngOnInit(): void;
}
