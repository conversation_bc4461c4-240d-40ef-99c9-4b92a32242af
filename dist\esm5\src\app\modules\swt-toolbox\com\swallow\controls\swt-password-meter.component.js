/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild, Input } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
/** @type {?} */
var $ = require('jquery');
var SwtPasswordMeter = /** @class */ (function () {
    function SwtPasswordMeter() {
        this._label = "";
        this._includeInLayout = false;
        this._visible = true;
        this.includeInlayout = false;
        //Target text input field, to validate the strength
        this._target = null;
        //callback function after calculate the password strength
        this._labelFunction = null;
    }
    /**
     * @return {?}
     */
    SwtPasswordMeter.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this._labelFunction = this.getStrengthLabel;
    };
    /**
     * This function calculate the strength of the target input field value
     */
    /**
     * This function calculate the strength of the target input field value
     * @private
     * @return {?}
     */
    SwtPasswordMeter.prototype.calcuateStrength = /**
     * This function calculate the strength of the target input field value
     * @private
     * @return {?}
     */
    function () {
        //Password
        /** @type {?} */
        var password = this._target.text;
        //Default value
        this._strength = 0;
        if (password.length > 0) {
            //Minimum unique spl characters
            /** @type {?} */
            var uniqueSplChars = 2;
            //Minimum alphabets
            /** @type {?} */
            var uniqueAlpha = 4;
            //Mininum numbers
            /** @type {?} */
            var uniqueNumber = 2;
            //Password valid flag, check the password against password rule
            /** @type {?} */
            var validFlag = true;
            //Special characters count
            /** @type {?} */
            var splCharCount = 0;
            //lower case characters count
            /** @type {?} */
            var lowerCount = 0;
            //upper case characters count
            /** @type {?} */
            var upperCount = 0;
            //numbers count
            /** @type {?} */
            var numCount = 0;
            //Unique special characters count
            /** @type {?} */
            var splCharUniqueCount = 0;
            //Unique lower case characters count
            /** @type {?} */
            var lowerUniqueCount = 0;
            //Unique upper case characters count
            /** @type {?} */
            var upperUniqueCount = 0;
            //Unique numbers count
            /** @type {?} */
            var numUniqueCount = 0;
            //Temporary variable to hold unique chars
            /** @type {?} */
            var uniqueChar = "";
            //Temporary variable to add unique chars
            /** @type {?} */
            var uniqueFlag;
            //Special characters
            /** @type {?} */
            var splChar = "~#!@$%^&*()-_=+[]:;'\",<.>/?";
            //Numbers
            /** @type {?} */
            var numbers = "0123456789";
            //Lower case characters
            /** @type {?} */
            var lowerChars = "abcdefghijklmnopqrstuvwxyz";
            //Upper case characters
            /** @type {?} */
            var upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            //Validate password strength
            for (var i = 0; i < password.length; i++) {
                /** @type {?} */
                var char = password.charAt(i);
                uniqueFlag = false;
                if (splChar.indexOf(char) >= 0) {
                    splCharCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        splCharUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (lowerChars.indexOf(char) >= 0) {
                    lowerCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        lowerUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (upperChars.indexOf(char) >= 0) {
                    upperCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        upperUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (numbers.indexOf(char) >= 0) {
                    numCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        numUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                if (uniqueFlag) {
                    uniqueChar += char;
                }
            }
            //Check password length
            if (password.length < this._minLength) {
                validFlag = false;
            }
            //Check Special characters count
            if (splCharCount < this._minSplChar) {
                validFlag = false;
            }
            //Check alphabets count
            if ((upperCount + lowerCount) < this._minAlpha) {
                validFlag = false;
            }
            //Check mixed case count
            if (this._mixedCase) {
                if (upperCount == 0 || lowerCount == 0) {
                    validFlag = false;
                }
            }
            //Check numbers count
            if (numCount < this._minNumber) {
                validFlag = false;
            }
            //If the password validation is success against the rule, 
            //then calculate its strength 
            if (validFlag) {
                //Check password minimum length
                if (password.length >= (uniqueSplChars + uniqueAlpha + uniqueNumber)) {
                    this._strength = this._strength + 20;
                }
                //Check unique special characters count
                if (splCharUniqueCount >= uniqueSplChars) {
                    this._strength = this._strength + 20;
                }
                //Check unique alphabets count
                if ((upperUniqueCount + lowerUniqueCount) >= uniqueAlpha) {
                    this._strength = this._strength + 20;
                }
                //Check unique mixed case count
                if (upperUniqueCount > 0 && lowerUniqueCount > 0) {
                    this._strength = this._strength + 20;
                }
                //Check unique numbers count
                if (numUniqueCount >= uniqueNumber) {
                    this._strength = this._strength + 20;
                }
                //Very weak password
                if (this._strength == 0) {
                    this._strength = 20;
                }
            }
            else {
                //Invalid password
                this._strength = -1;
            }
        }
        if (this._strength == -1) {
            //Set bar color for invalid password
            this.setStyle("background-color", "#FF0000");
            //update progress 
            this.setProgress(100, 100);
        }
        else {
            //Set bar color
            this.setStyle("background-color", "#1FA9FF");
            //update progress 
            this.setProgress(this._strength, 100);
        }
    };
    /**
     * This method is used to set style property to
     * SwtPasswordMetter.
     * @param attribute
     * @param value
     */
    /**
     * This method is used to set style property to
     * SwtPasswordMetter.
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    SwtPasswordMeter.prototype.setStyle = /**
     * This method is used to set style property to
     * SwtPasswordMetter.
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    function (attribute, value) {
        $(this.passMetterObject.nativeElement.children[0]).css(attribute, value);
    };
    /**
     * @param {?} value
     * @param {?} total
     * @return {?}
     */
    SwtPasswordMeter.prototype.setProgress = /**
     * @param {?} value
     * @param {?} total
     * @return {?}
     */
    function (value, total) {
        $(this.passMetterObject.nativeElement.children[0]).css("width", value + "%");
    };
    /**
     * This function resets the strength value to clear the progress bar
     */
    /**
     * This function resets the strength value to clear the progress bar
     * @return {?}
     */
    SwtPasswordMeter.prototype.clearProgress = /**
     * This function resets the strength value to clear the progress bar
     * @return {?}
     */
    function () {
        //reset the value 
        this._strength = 0;
        //Clear progress bar
        this.setProgress(this._strength, 100);
    };
    /**
     * Calculate strength of the password, whenever the value is changed
     *
     * @param event: Event
     */
    /**
     * Calculate strength of the password, whenever the value is changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtPasswordMeter.prototype.onValueChange = /**
     * Calculate strength of the password, whenever the value is changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.calcuateStrength();
        this.label = this._labelFunction(this._strength);
    };
    /**
     * <pre>
     * This function is used to get the strength of the password in word
     *  - Invalid Password:- Validation fail against password rule
     *  - Very weak (20%)
     *  - Weak (40%)
     *  - Medium (60%)
     *  - Strong (80%)
     *  - Very strong (100%)
     * </pre>
     *
     * @param strength - password strength value in percentage
     * @return String - password strengh in word
     */
    /**
     * <pre>
     * This function is used to get the strength of the password in word
     *  - Invalid Password:- Validation fail against password rule
     *  - Very weak (20%)
     *  - Weak (40%)
     *  - Medium (60%)
     *  - Strong (80%)
     *  - Very strong (100%)
     * </pre>
     *
     * @private
     * @param {?} strength - password strength value in percentage
     * @return {?} String - password strengh in word
     */
    SwtPasswordMeter.prototype.getStrengthLabel = /**
     * <pre>
     * This function is used to get the strength of the password in word
     *  - Invalid Password:- Validation fail against password rule
     *  - Very weak (20%)
     *  - Weak (40%)
     *  - Medium (60%)
     *  - Strong (80%)
     *  - Very strong (100%)
     * </pre>
     *
     * @private
     * @param {?} strength - password strength value in percentage
     * @return {?} String - password strengh in word
     */
    function (strength) {
        /** @type {?} */
        var strStrength = "";
        //Get password strength in word based on strength value
        switch (strength) {
            case -1:
                strStrength = "Invalid - Too weak";
                break;
            case 20:
                strStrength = "Very weak";
                break;
            case 40:
                strStrength = "Weak";
                break;
            case 60:
                strStrength = "Medium";
                break;
            case 80:
                strStrength = "Strong";
                break;
            case 100:
                strStrength = "Very strong";
                break;
        }
        //Returns password strength in word
        return strStrength;
    };
    Object.defineProperty(SwtPasswordMeter.prototype, "target", {
        /**
         * Getter method of target
         */
        get: /**
         * Getter method of target
         * @return {?}
         */
        function () {
            return this._target;
        },
        //////////////////////////////////////////////////////////////////////
        //      GETTERS AND SETTERS
        //////////////////////////////////////////////////////////////////////
        /**
         * Setter method of target
         */
        set: 
        //////////////////////////////////////////////////////////////////////
        //      GETTERS AND SETTERS
        //////////////////////////////////////////////////////////////////////
        /**
         * Setter method of target
         * @param {?} target
         * @return {?}
         */
        function (target) {
            var _this = this;
            this._target = target;
            target.keyUp = (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.onValueChange(event);
            });
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "strength", {
        /**
         * Getter method of strength
         */
        get: /**
         * Getter method of strength
         * @return {?}
         */
        function () {
            return this._strength;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "minLength", {
        /**
         * Getter method of minLength
         */
        get: /**
         * Getter method of minLength
         * @return {?}
         */
        function () {
            return this._minLength;
        },
        /**
         * Setter method of minLength
         */
        set: /**
         * Setter method of minLength
         * @param {?} minLength
         * @return {?}
         */
        function (minLength) {
            this._minLength = minLength;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "minSplChar", {
        /**
         * Getter method of minSplChar
         */
        get: /**
         * Getter method of minSplChar
         * @return {?}
         */
        function () {
            return this._minSplChar;
        },
        /**
         * Setter method of minSplChar
         */
        set: /**
         * Setter method of minSplChar
         * @param {?} minSplChar
         * @return {?}
         */
        function (minSplChar) {
            this._minSplChar = minSplChar;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "minAlpha", {
        /**
         * Getter method of minAlpha
         */
        get: /**
         * Getter method of minAlpha
         * @return {?}
         */
        function () {
            return this._minAlpha;
        },
        /**
         * Setter method of minAlpha
         */
        set: /**
         * Setter method of minAlpha
         * @param {?} minAlpha
         * @return {?}
         */
        function (minAlpha) {
            this._minAlpha = minAlpha;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "minNumber", {
        /**
         * Getter method of minNumber
         */
        get: /**
         * Getter method of minNumber
         * @return {?}
         */
        function () {
            return this._minNumber;
        },
        /**
         * Setter method of minNumber
         */
        set: /**
         * Setter method of minNumber
         * @param {?} minNumber
         * @return {?}
         */
        function (minNumber) {
            this._minNumber = minNumber;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "mixedCase", {
        /**
         * Getter method of mixedCase
         */
        get: /**
         * Getter method of mixedCase
         * @return {?}
         */
        function () {
            return this._mixedCase;
        },
        /**
         * Setter method of mixedCase
         */
        set: /**
         * Setter method of mixedCase
         * @param {?} mixedCase
         * @return {?}
         */
        function (mixedCase) {
            this._mixedCase = mixedCase;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "labelFunction", {
        /**
         * Getter method of labelFunction function
         */
        get: /**
         * Getter method of labelFunction function
         * @return {?}
         */
        function () {
            return this._labelFunction;
        },
        /**
         * Setter method of labelFunction function
         */
        set: /**
         * Setter method of labelFunction function
         * @param {?} labelFunction
         * @return {?}
         */
        function (labelFunction) {
            this._labelFunction = labelFunction;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "width", {
        /**
         * Getter method of width
         */
        get: /**
         * Getter method of width
         * @return {?}
         */
        function () {
            return this._width;
        },
        /**
         * Setter method of width
         */
        set: /**
         * Setter method of width
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._width = value;
            $(this.passMetterObject.nativeElement).width(value).css('width', '-=' + 2 + "px");
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "height", {
        /**
         * Getter method of height
         */
        get: /**
         * Getter method of height
         * @return {?}
         */
        function () {
            return this._height;
        },
        /**
         * Setter method of height
         */
        set: /**
         * Setter method of height
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "visible", {
        /**
         * Getter method of visible
         */
        get: /**
         * Getter method of visible
         * @return {?}
         */
        function () {
            return this._visible;
        },
        /**
         * Setter method of visible
         */
        set: /**
         * Setter method of visible
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._visible = value;
            if (typeof (value) == "string") {
                if (this.includeInLayout) {
                    if (value == "true") {
                        $($(this.passMetterObject.nativeElement)[0]).css("visibility", "visible");
                    }
                    else {
                        $($(this.passMetterObject.nativeElement)[0]).css("visibility", "hidden");
                    }
                }
                else {
                    if (value == "true") {
                        $(this.passMetterObject.nativeElement).show();
                    }
                    else {
                        $(this.passMetterObject.nativeElement).hide();
                    }
                }
            }
            else {
                if (this.includeInLayout) {
                    if (value) {
                        $($(this.passMetterObject.nativeElement)[0]).css("visibility", "visible");
                    }
                    else {
                        $($(this.passMetterObject.nativeElement)[0]).css("visibility", "hidden");
                    }
                }
                else {
                    if (value) {
                        $(this.passMetterObject.nativeElement).show();
                    }
                    else {
                        $(this.passMetterObject.nativeElement).hide();
                    }
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "includeInLayout", {
        /**
         * Getter method of visible
         */
        get: /**
         * Getter method of visible
         * @return {?}
         */
        function () {
            return this._includeInLayout;
        },
        /**
         * Setter method of visible
         */
        set: /**
         * Setter method of visible
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._includeInLayout = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPasswordMeter.prototype, "label", {
        /**
         * Getter method of visible
         */
        get: /**
         * Getter method of visible
         * @return {?}
         */
        function () {
            return this._label;
        },
        /**
         * Setter method of visible
         */
        set: /**
         * Setter method of visible
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._label = value;
            $($(this.passMetterObject.nativeElement.children[1])[0]).text(value);
        },
        enumerable: true,
        configurable: true
    });
    SwtPasswordMeter.decorators = [
        { type: Component, args: [{
                    selector: 'SwtPasswordMeter',
                    template: "\n    <div #passMetterObject class=\"password-meter-progressBar\">\n      <div class=\"password-meter-progress\"></div>\n      <span id=\"label\"></span>\n    </div>\n  ",
                    styles: ["\n           .password-meter-progressBar {\n              width: 100%;\n              height: 9px;\n              margin-left:5px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#F5F8FA), to(#A9D2E4));\n              background-image: -webkit-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -moz-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -ms-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -o-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: linear-gradient(to bottom, #F5F8FA, #A9D2E4);\n              border-top: 1px solid #6B6B6B;\n              border-left: 1px solid #818181;\n              border-right: 1px solid #7D7D7D;\n              border-bottom: 1px solid #949494;\n            }\n            span {\n                display: block;\n                position: relative;\n                top:-9px;\n                width:100%;\n                text-align: center;\n                color: #000;\n                font-size: 9px;\n                font-weight: bold;\n                font-family: verdana,helvetica;\n            }\n            .password-meter-progress {\n              width: 0%;\n              height: 100%;\n              border-radius: 0px;\n            }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtPasswordMeter.ctorParameters = function () { return []; };
    SwtPasswordMeter.propDecorators = {
        passMetterObject: [{ type: ViewChild, args: ["passMetterObject",] }],
        target: [{ type: Input }],
        labelFunction: [{ type: Input }],
        width: [{ type: Input }],
        height: [{ type: Input }],
        visible: [{ type: Input }],
        includeInLayout: [{ type: Input }],
        label: [{ type: Input }]
    };
    return SwtPasswordMeter;
}());
export { SwtPasswordMeter };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._includeInLayout;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype.includeInlayout;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._target;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._strength;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minLength;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minSplChar;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minAlpha;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minNumber;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._mixedCase;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._labelFunction;
    /** @type {?} */
    SwtPasswordMeter.prototype.passMetterObject;
}
//# sourceMappingURL=data:application/json;base64,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