/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { CommonService } from "../utils/common.service";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { StringUtils } from "../utils/string-utils.service";
import { parentApplication } from "../utils/parent-application.service";
import { ExternalInterface } from "../utils/external-interface.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtCanvas } from "./swt-canvas.component";
import { HBox } from "./swt-hbox.component";
import { SwtButton } from "./swt-button.component";
import { SwtLoadingImage } from "./swt-loading-image.component";
import { SwtHelpButton } from "./swt-helpButton.component";
import { Keyboard } from "../utils/keyboard.service";
import { focusManager } from "../managers/focus-manager.service";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { SwtTabNavigatorHandler } from "../utils/swt-tabnavigator.service";
import { ModuleLoader } from "../utils/module-loader.service";
import { ModuleEvent } from "../events/swt-events.module";
import { Logger } from '../logging/logger.service';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtCommonModule extends SwtModule {
    /**
     * @param {?} _element
     * @param {?} _commonService
     */
    constructor(_element, _commonService) {
        super(_element, _commonService);
        this._element = _element;
        this._commonService = _commonService;
        this._creationComplete = new EventEmitter();
        //set preinitialize event
        this._preinitialize = new EventEmitter();
        // @Input to set dynamic buttons.
        this.buttons = "";
        this.actionPathReport = "report!exportData.do?";
        this.addParamReport = "";
        this.swtKVParams = null;
        this.baseURL = "";
        this.actionMethod = "";
        this.actionPath = "";
        this.requestParams = new Array();
        //To identify the url path for report setting
        this.urlReportSetting = null;
        // iable to identify a grid Object
        this.gridReport = null;
        //To store menu access
        this.menuAccess = null;
        //set program
        this.programId = null;
        //To identify the module window status
        this.undockWindow = null;
        /* iable to hold init X value to load child screen */
        this.initXValue = 300;
        //hold componentId
        this.componentId = null;
        this.reportGroupId = null;
        //VBox to view report setting
        this.newVBox = null;
        // SwtAlert to show errors
        this.SwtAlert = null;
        this.jsonReader = new JSONReader();
        //Buttons
        this.addButton = null;
        this.changeButton = null;
        this.viewButton = null;
        this.duplicateButton = null;
        this.deleteButton = null;
        this.searchButton = null;
        this.cancelButton = null;
        this.saveButton = null;
        this.printIcon = null;
        this.closeButton = null;
        this.deleteMethod = null;
        this.duplicateMethod = null;
        this.popupClosedEvent = null;
        this.kvParamsMethod = null;
        //To identify the url path
        this.urlDetailsPathAdd = null;
        //To identify the url path
        this.urlDetailsPathChange = null;
        //To identify the url path
        this.urlDetailsPathView = null;
        //To identify the url path
        this.urlDetailsPathDelete = null;
        //To identify the url path
        this.urlDetailsPathDuplicate = null;
        //To identify the url path
        this.urlDetailsPathSave = null;
        //To identify the title of child window
        this.childPanelTitleAdd = null;
        //To identify the title of child window
        this.childPanelTitleChange = null;
        //To identify the title of child window
        this.childPanelTitleView = null;
        //To identify the title of child window
        this.childPanelTitleDelete = null;
        //To identify the title of child window
        this.childPanelTitleDuplicate = null;
        //To identify the component Add.
        this.addComponent = null;
        //To identify the component Change.
        this.changeComponent = null;
        //To identify the component View.
        this.viewComponent = null;
        //To identify the component Delete.
        this.deleteComponent = null;
        //To identify the component Duplicate.
        this.duplicateComponent = null;
        //To identify the component ReportSetting.
        this.reportSettingComponent = null;
        //To identify the title of child window
        this.saveComponent = null;
        this.vBoxCustomGridPaddingTop = 5;
        this.lockedColumnCount = 0;
        this.uniqueColumn = "";
        this.eventString = "";
        this.screenName = "";
        this.versionNumber = "";
        this.availableScreen = null;
        this.objectType = null;
        this.recordId = null;
        this.partyType = null;
        this.closeURL = null;
        // Initialize ModuleLoader to load child windows
        this.mLoader = null;
        //Panel to view child window
        this.childPanel = null;
        this._inputData = new HTTPComms(this._commonService);
        this.popUpScreen = null;
        this.SwtAlert = new SwtAlert(_commonService);
        this.logger = new Logger("SwtCommonModule", _commonService.httpclient);
    }
    /**
     * This method fired on commonModule load.
     * @param {?} event
     * @return {?}
     */
    onCommonModuleInit(event) {
        this._creationComplete.emit(this);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onCommonModuleBeforeInit(event) {
        if (this.buttons) {
            $(this.commonTemplate.nativeElement).hide();
            $(this.buttonsTemplate.nativeElement).show();
            this.setDynamicButtons();
        }
        else {
            $(this.commonTemplate.nativeElement).show();
            $(this.buttonsTemplate.nativeElement).hide();
        }
        this._preinitialize.emit(this);
    }
    /**
     * This is a report icon action handler method
     * @param {?} type
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    report(type, displayFilter = null, xmlDataSource = null) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var moduleId = null;
        /** @type {?} */
        var actionParams = "";
        /** @type {?} */
        var displayFilterAsJSON;
        this.logger.info("[ report ] method START");
        try {
            errorLocation = 10;
            /** @type {?} */
            const parentKVParams = this.getKVParams();
            errorLocation = 20;
            displayFilterAsJSON = StringUtils.getKVTypeTabAsXML(displayFilter, '', '', '');
            errorLocation = 30;
            //Get the current Module Id.
            moduleId = parentApplication.getCurrentModuleId();
            errorLocation = 40;
            //set the action path
            this.actionPath = this.actionPathReport;
            actionParams = "type=" + type;
            actionParams = actionParams + "&action=EXPORT&print=ALL&currentModuleId=" + moduleId + "&response=json";
            actionParams = actionParams + "&programId=" + this.programId + "&reportGroupId=" + this.reportGroupId
                + "&kvParams=" + StringUtils.encode64(parentKVParams).replace(/\+/gi, ")").replace(/\=/gi, "(")
                + "&displayFilter=" + StringUtils.encode64(displayFilterAsJSON.toXMLString()).replace(/\+/gi, ")").replace(/\=/gi, "(");
            errorLocation = 50;
            //Calls a method from mainflex.jsp file
            ExternalInterface.call("getReportsAndProgress", this.actionPath + actionParams, xmlDataSource != null ? StringUtils.encode64(xmlDataSource.toXMLString()) : "");
        }
        catch (error) {
            this.logger.error("[ report ] method - error ", error);
            this.SwtAlert.error(error.message, 'Programming Error');
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "report", errorLocation);
        }
        this.logger.info("[ report ] method END");
    }
    /**
     * keyDownHandlerEvent
     *
     * @param {?} event
     * @return {?}
     */
    keyDownHandlerEvent(event) {
        // Variable Number Error Location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 0;
            //Currently focussed property name
            /** @type {?} */
            const eventString = focusManager.getFocus().name;
            errorLocation = 10;
            if ((event.keyCode === Keyboard.ENTER)) {
                errorLocation = 20;
                if (eventString === "closeButton") {
                    errorLocation = 30;
                    this.closeScreenHandler(event);
                }
                else if (eventString === "csv") {
                    errorLocation = 40;
                    this.report('csv');
                }
                else if (eventString === "excel") {
                    errorLocation = 50;
                    this.report('xls');
                }
                else if (eventString === "pdf") {
                    errorLocation = 60;
                    this.report('pdf');
                }
                else if (eventString === 'settingButton') {
                    errorLocation = 70;
                    this.getSetting();
                }
                else if (eventString === "addButton") {
                    errorLocation = 30;
                    this.clickHandler(event);
                }
                else if (eventString === "changeButton") {
                    errorLocation = 40;
                    this.clickHandler(event);
                }
                else if (eventString === "viewButton") {
                    errorLocation = 50;
                    this.clickHandler(event);
                }
                else if (eventString === "duplicateButton") {
                    errorLocation = 60;
                    this.clickHandler(event);
                }
                else if (eventString === "printIcon") {
                    errorLocation = 70;
                    this.clickHandler(event);
                }
            }
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "keyDownHandlerEvent", errorLocation);
        }
    }
    /**
     * getSetting
     *
     * @return {?}
     */
    getSetting() {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.swtKVParams = this.getKVParams();
            errorLocation = 10;
            // Get component  Id from xml
            this.componentId = this.lastRecievedJSON.screenid;
            errorLocation = 20;
            //Add listener to moduleReadyEventHandler
            this.moduleReadyEventHandlerReportSetting(event);
            /** @type {?} */
            const title = SwtUtil.getCommonMessages('report.windowtitle.title');
            errorLocation = 30;
            // set details screen title.
            /** @type {?} */
            const data = this.getDataFromUrl(this.urlReportSetting);
            // Load view screen
            // this.loadModule(title, this.reportSettingComponent, this.urlReportSetting, data);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "getSetting", errorLocation);
        }
    }
    /**
     * This method is used to show help
     * page.
     * @return {?}
     */
    showhelp() {
    }
    /**
     * kvReportParams function that should be overriden by child classes
     * @protected
     * @return {?}
     */
    kvReportParams() {
        if (this.kvParamsMethod) {
            return this.kvParamsMethod();
        }
        else {
            throw new Error("Programming Error: function kvReportParams() must be overriden in child classes implementing SwtCommonModule !!");
        }
    }
    /**
     * Constructs the report parameters as xml string
     * @protected
     * @return {?}
     */
    getKVParams() {
        /** @type {?} */
        const paramsList = this.kvReportParams();
        /** @type {?} */
        const kvAsXML = StringUtils.getKVTypeTabAsXML(paramsList, '', '', '');
        return kvAsXML.toXMLString();
    }
    /**
     * This method is used
     * @protected
     * @param {?} event
     * @return {?}
     */
    clickHandler(event) {
        //Variable for errorLocation
        /** @type {?} */
        const errorLocation = 0;
        // String to hold the current target of the event
        /** @type {?} */
        var eventString = null;
        this.logger.info("[ clickHandler ] method START");
        try {
            //Currently focussed property name
            eventString = focusManager.getFocus().name;
            if (eventString === "addButton" && this.urlDetailsPathAdd != null) {
                // Load view screen
                this.loadModule(this.childPanelTitleAdd, this.urlDetailsPathAdd);
            }
            else if (eventString === "changeButton" && this.urlDetailsPathChange != null) {
                // Load change screen
                this.loadModule(this.childPanelTitleChange, this.urlDetailsPathChange);
            }
            else if (eventString === "viewButton" && this.urlDetailsPathView != null) {
                // Load view screen
                this.loadModule(this.childPanelTitleView, this.urlDetailsPathView);
            }
            if (eventString === "deleteButton") {
                // Load view screen
                this.deleteAction();
            }
            else if (eventString === "duplicateButton") {
                this.duplicateAction();
            }
            else if (eventString === "printIcon") {
                //duplicateAction(); TODO
            }
            else if (eventString === "closeButton") {
                /** @type {?} */
                const res = this._commonService.Router.url.split(";");
                SwtTabNavigatorHandler.closeTab(res[0].substr(1, res[0].length));
                this.closeScreenHandler(event);
            }
        }
        catch (error) {
            this.logger.error("[ clickHandler ] method - error ", error);
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "clickHandler", errorLocation);
        }
        this.logger.info("[ clickHandler ] method END");
    }
    /**
     * loadModule
     *
     * This method is used to open child module
     * @protected
     * @param {?} title
     * @param {?} url
     * @return {?}
     */
    loadModule(title, url) {
        this.logger.info("[ loadModule ] method START");
        /** @type {?} */
        let errorLocation = 0;
        try {
            if (this.mLoader === null) {
                this.childPanel = SwtPopUpManager.createPopUp(this.context);
                this.childPanel.title = title;
                this.childPanel.isModal = true;
                this.childPanel.showControls = true;
                this.childPanel.enableResize = true;
                this.mLoader = new ModuleLoader(this._commonService);
                this.mLoader.percentHeight = 50;
                this.mLoader.percentWidth = 50;
                errorLocation = 40;
                /* Load Add screen module */
                this.mLoader.addEventListener(ModuleEvent.READY, (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => this.moduleReadyEventHandler(event)));
                errorLocation = 50;
                this.mLoader.loadModule(url);
            }
            else {
                SwtPopUpManager.bringToFront(null); // TODO add popup instance to be set in the top.
            }
        }
        catch (error) {
            this.logger.error("[ loadModule ] method - error ", error);
        }
        this.logger.info("[ loadModule ] method END");
    }
    /**
     * moduleReadyEventHandler
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    moduleReadyEventHandler(event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.childPanel.addChild(event.target);
            errorLocation = 20;
            this.childPanel.onClose.subscribe((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.popupClosedEventHandler(event);
            }));
            this.childPanel.display();
            errorLocation = 30;
            /* Disable buttons while child screen is loaded */
            this._helpIcon.enabled = false;
            this._helpIcon.buttonMode = false;
            this._pdf.enabled = false;
            this._pdf.buttonMode = false;
            this._csv.enabled = false;
            this._csv.buttonMode = false;
            this._excel.enabled = false;
            this._excel.buttonMode = false;
            this._settingButton.enabled = false;
            this._settingButton.buttonMode = false;
            errorLocation = 40;
            // check if addButton is not null
            if (this.addButton != null) {
                this.addButton.enabled = false;
                this.addButton.buttonMode = false;
            }
            // check if changeButton is not null
            if (this.changeButton != null) {
                // disable change button
                this.changeButton.enabled = false;
                this.changeButton.buttonMode = false;
            }
            // check if viewButton button is not null
            if (this.viewButton != null) {
                // disable view button
                this.viewButton.enabled = false;
                this.viewButton.buttonMode = false;
            }
            // check if deleteButton button is not null
            if (this.deleteButton != null) {
                // disable delete button
                this.deleteButton.enabled = false;
                this.deleteButton.buttonMode = false;
            }
            // check if duplicateButton is not null
            if (this.duplicateButton != null) {
                // disable duplicate button
                this.duplicateButton.enabled = false;
                this.duplicateButton.buttonMode = false;
            }
            if (this.searchButton != null) {
                this.searchButton.enabled = false;
                this.searchButton.buttonMode = false;
            }
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "moduleReadyEventHandler", errorLocation);
        }
    }
    /**
     * popupClosedEventHandler
     *
     * @protected
     * @param {?} event :Event
     *
     * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
     *
     * @return {?}
     */
    popupClosedEventHandler(event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        this.logger.info("[ popupClosedEventHandler ] method START");
        try {
            errorLocation = 10;
            this._helpIcon.enabled = true;
            this._helpIcon.buttonMode = true;
            this._pdf.enabled = true;
            this._pdf.buttonMode = true;
            this._csv.enabled = true;
            this._csv.buttonMode = true;
            this._excel.enabled = true;
            this._excel.buttonMode = true;
            this._settingButton.enabled = true;
            this._settingButton.buttonMode = true;
            errorLocation = 20;
            if (this.searchButton != null) {
                this.searchButton.enabled = true;
                this.searchButton.buttonMode = true;
            }
            errorLocation = 30;
            if (this.swtCommonGrid) {
                if (this.swtCommonGrid.selectedIndex !== -1) {
                    this.viewButton.enabled = true;
                    this.viewButton.buttonMode = true;
                    if (this.menuAccess === "0") {
                        if (this.changeButton != null) {
                            this.changeButton.enabled = true;
                            this.changeButton.buttonMode = true;
                        }
                        if (this.deleteButton != null) {
                            this.deleteButton.enabled = true;
                            this.deleteButton.buttonMode = true;
                        }
                        if (this.duplicateButton != null) {
                            this.duplicateButton.enabled = true;
                            this.duplicateButton.buttonMode = true;
                        }
                    }
                    else {
                        if (this.changeButton != null) {
                            this.changeButton.enabled = false;
                            this.changeButton.buttonMode = false;
                        }
                        if (this.deleteButton != null) {
                            this.deleteButton.enabled = false;
                            this.deleteButton.buttonMode = false;
                        }
                        if (this.duplicateButton != null) {
                            this.duplicateButton.enabled = false;
                            this.duplicateButton.buttonMode = false;
                        }
                    }
                }
                else {
                    errorLocation = 40;
                    this.viewButton.enabled = false;
                    this.viewButton.buttonMode = false;
                    if (this.changeButton != null) {
                        this.changeButton.enabled = false;
                        this.changeButton.buttonMode = false;
                    }
                    if (this.deleteButton != null) {
                        this.deleteButton.enabled = false;
                        this.deleteButton.buttonMode = false;
                    }
                    if (this.duplicateButton != null) {
                        this.duplicateButton.enabled = false;
                        this.duplicateButton.buttonMode = false;
                    }
                }
            }
            // check for Add button
            if (this.menuAccess === '0') {
                this.addButton.enabled = true;
                this.addButton.buttonMode = true;
                if (this.duplicateButton != null) {
                    this.duplicateButton.enabled = true;
                    this.duplicateButton.buttonMode = true;
                }
            }
            else {
                this.addButton.enabled = false;
                this.addButton.buttonMode = false;
            }
            // initialize mloader and childPanel.
            this.mLoader = null;
            this.childPanel = null;
        }
        catch (error) {
            this.logger.error("[ popupClosedEventHandler ] method - error ", error);
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "popupClosedEventHandler", errorLocation);
        }
        this.logger.info("[ popupClosedEventHandler ] method END");
    }
    /**
     * dispose
     *
     * This is a event handler, used to close the current tab/window
     * @protected
     * @return {?}
     */
    dispose() {
        // remove event listeners on buttons
        if (this.addButton != null) {
            this.addButton.click = null;
            this.addButton = null;
        }
        if (this.viewButton != null) {
            this.viewButton.click = null;
            this.viewButton = null;
        }
        if (this.changeButton != null) {
            this.changeButton.click = null;
            this.changeButton = null;
        }
        if (this.deleteButton != null) {
            this.deleteButton.click = null;
            this.deleteButton = null;
        }
        if (this.duplicateButton != null) {
            this.duplicateButton.click = null;
            this.duplicateButton = null;
        }
        if (this.printIcon != null) {
            this.printIcon.click = null;
            this.printIcon = null;
        }
        //
        // this.removeEventListener(FocusEvent.KEY_FOCUS_CHANGE, focusChangeHandler, true);
        // this.removeAllChildren();
        // this._loadingImage.unloadAndStop(true);
        this.requestParams = null;
        this._inputData = null;
        this.jsonReader = null;
        this.lastRecievedJSON = null;
        this.prevRecievedJSON = null;
        this.baseURL = null;
        this.programId = null;
        this.mLoader = null;
        this.childPanel = null;
    }
    /**
     * closeScreenHandler
     * @protected
     * @param {?} event
     * @return {?}
     */
    closeScreenHandler(event) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.dispose();
            errorLocation = 20;
            /* If screen is undocked call javascript close */
            if (this.undockWindow === "true") {
                errorLocation = 30;
                ExternalInterface.call("closeScreen");
            }
            else if (this.popUpScreen === "popUp") {
                errorLocation = 30;
            }
            else {
                errorLocation = 40;
                parentApplication.navigator.removeChildAt(parentApplication.navigator.selectedIndex);
            }
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.AML_MODULE_ID, this.getQualifiedClassName(this), "closeScreenHandler", errorLocation);
        }
    }
    /**
     * moduleReadyEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    moduleReadyEventHandlerReportSetting(event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.popupClosedEventHandler(event);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "moduleReadyEventHandlerReportSetting", errorLocation);
        }
    }
    /**
     * popupClosedEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    popupClosedEventHandlerReportSetting(event) {
        //Variable for errorLocation
        /** @type {?} */
        const errorLocation = 0;
        try {
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this) + ".mxml", "popupClosedEventHandlerReportSetting", errorLocation);
        }
    }
    /**
     * deleteAction
     * @protected
     * @return {?}
     */
    deleteAction() {
        if (this.deleteMethod) {
            this.deleteMethod();
        }
        else {
            throw new Error("you must set deleteMethod in child class.");
        }
    }
    /**
     * duplicateAction
     * @protected
     * @return {?}
     */
    duplicateAction() {
        if (this.duplicateMethod) {
            this.duplicateMethod();
        }
        else {
            throw new Error("you must set duplicateMethod in child class.");
        }
    }
    /**
     * @private
     * @return {?}
     */
    setDynamicButtons() {
        // set dynamic buttons.
        /** @type {?} */
        const buttons = this.buttons.split(",");
        // loop on buttons array.
        for (const index in buttons) {
            if (buttons[index] === "add") {
                this.addButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.addButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.addButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.addButton.tabIndex = Number(index + 1);
                this.addButton.id = buttons[index] + "Button";
                this.addButton.name = buttons[index] + "Button";
                this.addButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "change") {
                this.changeButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.changeButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.changeButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.changeButton.tabIndex = Number(index + 1);
                this.changeButton.id = buttons[index] + "Button";
                this.changeButton.name = buttons[index] + "Button";
                this.changeButton.enabled = false;
                this.changeButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "view") {
                this.viewButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.viewButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.viewButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.viewButton.tabIndex = Number(index + 1);
                this.viewButton.id = buttons[index] + "Button";
                this.viewButton.name = buttons[index] + "Button";
                this.viewButton.enabled = false;
                this.viewButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "delete") {
                this.deleteButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.deleteButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.deleteButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.deleteButton.tabIndex = Number(index + 1);
                this.deleteButton.id = buttons[index] + "Button";
                this.deleteButton.name = buttons[index] + "Button";
                this.deleteButton.enabled = false;
                this.deleteButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "duplicate") {
                this.duplicateButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.duplicateButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.duplicateButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.duplicateButton.tabIndex = Number(index + 1);
                this.duplicateButton.id = buttons[index] + "Button";
                this.duplicateButton.name = buttons[index] + "Button";
                this.duplicateButton.enabled = false;
                this.duplicateButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "search") {
                this.searchButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.searchButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.searchButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.searchButton.tabIndex = Number(index + 1);
                this.searchButton.id = buttons[index] + "Button";
                this.searchButton.name = buttons[index] + "Button";
                this.searchButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "cancel") {
                this.cancelButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.cancelButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.cancelButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.cancelButton.tabIndex = Number(index + 1);
                this.cancelButton.id = buttons[index] + "Button";
                this.cancelButton.name = buttons[index] + "Button";
                this.cancelButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "save") {
                this.saveButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.saveButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.saveButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.saveButton.tabIndex = Number(index + 1);
                this.saveButton.id = buttons[index] + "Button";
                this.saveButton.name = buttons[index] + "Button";
                this.saveButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
            else if (buttons[index] === "print") {
                this.printIcon = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.printIcon.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.printIcon.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.printIcon.tabIndex = Number(index + 1);
                this.printIcon.id = buttons[index] + "Button";
                this.printIcon.name = buttons[index] + "Button";
                this.printIcon.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.clickHandler(event);
                });
            }
        }
        // add close button to template.
        this.closeButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
        this.closeButton.label = SwtUtil.getCommonMessages("button.close");
        this.closeButton.toolTip = SwtUtil.getCommonMessages("button.tooltip.close");
        this.closeButton.tabIndex = 10;
        this.closeButton.id = "closeButton";
        this.closeButton.click = (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.clickHandler(event);
        });
    }
    /**
     * This method is used to split url and
     * get params from this last one.
     * @private
     * @param {?} url
     * @return {?}
     */
    getDataFromUrl(url) {
        /** @type {?} */
        const data = new Array();
        /** @type {?} */
        let errorLocation;
        this.logger.info("[ getDataFromUrl ] method START");
        try {
            /** @type {?} */
            const qm = url.lastIndexOf("?");
            errorLocation = 10;
            if (qm !== -1) {
                errorLocation = 20;
                /** @type {?} */
                const query = url.substr(qm + 1);
                /** @type {?} */
                const params = query.split("&");
                errorLocation = 30;
                for (var i = 0; i < params.length; i++) {
                    errorLocation = 40;
                    /** @type {?} */
                    const param = params[i];
                    /** @type {?} */
                    const nameValue = param.split("=");
                    errorLocation = 60;
                    if (nameValue.length === 2) {
                        errorLocation = 70;
                        /** @type {?} */
                        const key = nameValue[0];
                        /** @type {?} */
                        const val = nameValue[1];
                        errorLocation = 80;
                        if (key === "access") {
                            errorLocation = 90;
                            //Get access right
                            data["menuAccess"] = val;
                        }
                        if (key === "programId") {
                            errorLocation = 100;
                            //Get programId
                            data["programId"] = val;
                        }
                        if (key === "popUpScreen") {
                            errorLocation = 110;
                            //Get popUpScreen
                            data["popUpScreen"] = val;
                        }
                        if (key === "availableScreen") {
                            errorLocation = 120;
                            //Get availabeScreen
                            data["availableScreen"] = val;
                        }
                        if (key === "recordId") {
                            errorLocation = 130;
                            data["recordId"] = val;
                        }
                        if (key === "objectType") {
                            errorLocation = 140;
                            data["objectType"] = val;
                        }
                        if (key === "pv_party_type") {
                            errorLocation = 150;
                            data["partyType"] = val;
                        }
                    }
                }
            }
            parentApplication.loaderInfo.url = url;
        }
        catch (error) {
            this.logger.error("[ getDataFromUrl ] method - error ", error);
        }
        this.logger.info("[ getDataFromUrl ] method END");
        return data;
    }
}
SwtCommonModule.decorators = [
    { type: Component, args: [{
                selector: 'SwtCommonModule',
                template: `
        <SwtModule (creationComplete)="onCommonModuleInit($event)" (preinitialize)="onCommonModuleBeforeInit($event)"
                   width="{{ _width }}" height="{{ _height }}">
            <div #commonTemplate [style.width.px]="_width" [style.height.px]="_height">
                <ng-content></ng-content>
            </div>
            <div #buttonsTemplate class="swtmodule" [style.width.px]="_width" [style.height.px]="_height">
                <SwtCanvas #canvasContainer width="100%" height="90%" paddingLeft="10" paddingRight="10" paddingTop="10">

                    <SwtCanvas #customGrid width="100%" height="87%">
                        <ng-content select=".commonModule"></ng-content>
                    </SwtCanvas>
                    <SwtCanvas width="100%" marginTop="10">
                        <HBox width="100%" height="100%">
                            <HBox #hboxButtons width="100%"></HBox>
                            <HBox #hboxExport horizontalAlign="right">
                                <SwtLoadingImage #loadingImage id="loadingImage"></SwtLoadingImage>
                                <SwtButton #settingButton
                                           buttonMode="true"
                                           styleName="reportIcon"
                                           tabIndex="11"
                                           id="settingButton"
                                           (click)="getSetting()"
                                           (keyDown)="keyDownHandlerEvent($event)"
                                           toolTip="{{ getCommonMessages('button.report_setting') }}"></SwtButton>
                                <SwtButton #csv
                                           buttonMode="true"
                                           tabIndex="12"
                                           id="csv"
                                           enabled="true"
                                           styleName="csvIcon"
                                           (click)="report('csv')"
                                           (keyDown)="keyDownHandlerEvent($event)"
                                               toolTip="{{ getCommonMessages('button.tooltip.csv') }}"></SwtButton>
                                <SwtButton #excel
                                           buttonMode="true"
                                           tabIndex="13"
                                           id="excel"
                                           enabled="true"
                                           styleName="excelIcon"
                                           (click)="report('xls')"
                                           (keyDown)="keyDownHandlerEvent($event)"
                                               toolTip="{{ getCommonMessages('button.tooltip.excel') }}"></SwtButton>
                                <SwtButton #pdf
                                           buttonMode="true"
                                           tabIndex="14"
                                           id="pdf"
                                           enabled="true"
                                           styleName="pdfIcon"
                                           (click)="report('pdf')"
                                           (keyDown)="keyDownHandlerEvent($event)"
                                               toolTip="{{ getCommonMessages('button.tooltip.pdf') }}"></SwtButton>
                                <SwtButton #fatcaReport
                                           buttonMode="true"
                                           tabIndex="15"
                                           id="fatcaReport"
                                           includeInLayout="false"
                                           visible="false"
                                           enabled="true"
                                           styleName="fatcaReportIcon"
                                               toolTip="{{ getCommonMessages('button.tooltip.pdf') }}"></SwtButton>
                                <SwtHelpButton #helpIcon
                                               id="helpIcon"
                                               tabIndex="16"
                                               buttonMode="true"
                                               (click)="showhelp()"
                                               enabled="true"
                                               styleName="helpIcon"></SwtHelpButton>
                            </HBox>
                        </HBox>
                    </SwtCanvas>
                </SwtCanvas>
            </div>
        </SwtModule>
    `
            }] }
];
/** @nocollapse */
SwtCommonModule.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtCommonModule.propDecorators = {
    _creationComplete: [{ type: Output, args: ["creationComplete",] }],
    _preinitialize: [{ type: Output, args: ['preinitialize',] }],
    _width: [{ type: Input, args: ["width",] }],
    _height: [{ type: Input, args: ["height",] }],
    hboxButtons: [{ type: ViewChild, args: ["hboxButtons",] }],
    customGrid: [{ type: ViewChild, args: ["customGrid",] }],
    reportbuttons: [{ type: ViewChild, args: ["reportbuttons",] }],
    _loadingImage: [{ type: ViewChild, args: ["loadingImage",] }],
    _settingButton: [{ type: ViewChild, args: ["settingButton",] }],
    _csv: [{ type: ViewChild, args: ["csv",] }],
    _excel: [{ type: ViewChild, args: ["excel",] }],
    _pdf: [{ type: ViewChild, args: ["pdf",] }],
    _fatcaReport: [{ type: ViewChild, args: ["fatcaReport",] }],
    _helpIcon: [{ type: ViewChild, args: ["helpIcon",] }],
    buttonsTemplate: [{ type: ViewChild, args: ["buttonsTemplate",] }],
    commonTemplate: [{ type: ViewChild, args: ["commonTemplate",] }],
    buttons: [{ type: Input, args: ['buttons',] }]
};
if (false) {
    /** @type {?} */
    SwtCommonModule.prototype._creationComplete;
    /** @type {?} */
    SwtCommonModule.prototype._preinitialize;
    /** @type {?} */
    SwtCommonModule.prototype._width;
    /** @type {?} */
    SwtCommonModule.prototype._height;
    /** @type {?} */
    SwtCommonModule.prototype.hboxButtons;
    /** @type {?} */
    SwtCommonModule.prototype.customGrid;
    /** @type {?} */
    SwtCommonModule.prototype.reportbuttons;
    /** @type {?} */
    SwtCommonModule.prototype._loadingImage;
    /** @type {?} */
    SwtCommonModule.prototype._settingButton;
    /** @type {?} */
    SwtCommonModule.prototype._csv;
    /** @type {?} */
    SwtCommonModule.prototype._excel;
    /** @type {?} */
    SwtCommonModule.prototype._pdf;
    /** @type {?} */
    SwtCommonModule.prototype._fatcaReport;
    /** @type {?} */
    SwtCommonModule.prototype._helpIcon;
    /** @type {?} */
    SwtCommonModule.prototype.buttonsTemplate;
    /** @type {?} */
    SwtCommonModule.prototype.commonTemplate;
    /** @type {?} */
    SwtCommonModule.prototype.buttons;
    /** @type {?} */
    SwtCommonModule.prototype.actionPathReport;
    /** @type {?} */
    SwtCommonModule.prototype.addParamReport;
    /** @type {?} */
    SwtCommonModule.prototype.swtKVParams;
    /** @type {?} */
    SwtCommonModule.prototype.baseURL;
    /** @type {?} */
    SwtCommonModule.prototype.actionMethod;
    /** @type {?} */
    SwtCommonModule.prototype.actionPath;
    /** @type {?} */
    SwtCommonModule.prototype.requestParams;
    /** @type {?} */
    SwtCommonModule.prototype.urlReportSetting;
    /** @type {?} */
    SwtCommonModule.prototype.gridReport;
    /** @type {?} */
    SwtCommonModule.prototype.menuAccess;
    /** @type {?} */
    SwtCommonModule.prototype.programId;
    /** @type {?} */
    SwtCommonModule.prototype.undockWindow;
    /** @type {?} */
    SwtCommonModule.prototype.initXValue;
    /** @type {?} */
    SwtCommonModule.prototype.initYValue;
    /** @type {?} */
    SwtCommonModule.prototype.componentId;
    /** @type {?} */
    SwtCommonModule.prototype.reportGroupId;
    /** @type {?} */
    SwtCommonModule.prototype.newVBox;
    /** @type {?} */
    SwtCommonModule.prototype.SwtAlert;
    /** @type {?} */
    SwtCommonModule.prototype.jsonReader;
    /** @type {?} */
    SwtCommonModule.prototype.lastRecievedJSON;
    /** @type {?} */
    SwtCommonModule.prototype.prevRecievedJSON;
    /** @type {?} */
    SwtCommonModule.prototype.addButton;
    /** @type {?} */
    SwtCommonModule.prototype.changeButton;
    /** @type {?} */
    SwtCommonModule.prototype.viewButton;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateButton;
    /** @type {?} */
    SwtCommonModule.prototype.deleteButton;
    /** @type {?} */
    SwtCommonModule.prototype.searchButton;
    /** @type {?} */
    SwtCommonModule.prototype.cancelButton;
    /** @type {?} */
    SwtCommonModule.prototype.saveButton;
    /** @type {?} */
    SwtCommonModule.prototype.printIcon;
    /** @type {?} */
    SwtCommonModule.prototype.closeButton;
    /** @type {?} */
    SwtCommonModule.prototype.deleteMethod;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateMethod;
    /** @type {?} */
    SwtCommonModule.prototype.popupClosedEvent;
    /** @type {?} */
    SwtCommonModule.prototype.kvParamsMethod;
    /** @type {?} */
    SwtCommonModule.prototype.super;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathAdd;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathChange;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathView;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathDelete;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathDuplicate;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathSave;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleAdd;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleChange;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleView;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleDelete;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleDuplicate;
    /** @type {?} */
    SwtCommonModule.prototype.addComponent;
    /** @type {?} */
    SwtCommonModule.prototype.changeComponent;
    /** @type {?} */
    SwtCommonModule.prototype.viewComponent;
    /** @type {?} */
    SwtCommonModule.prototype.deleteComponent;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateComponent;
    /** @type {?} */
    SwtCommonModule.prototype.reportSettingComponent;
    /** @type {?} */
    SwtCommonModule.prototype.saveComponent;
    /** @type {?} */
    SwtCommonModule.prototype.vBoxCustomGridPaddingTop;
    /** @type {?} */
    SwtCommonModule.prototype.lockedColumnCount;
    /** @type {?} */
    SwtCommonModule.prototype.uniqueColumn;
    /** @type {?} */
    SwtCommonModule.prototype.swtCommonGrid;
    /** @type {?} */
    SwtCommonModule.prototype.eventString;
    /** @type {?} */
    SwtCommonModule.prototype.screenName;
    /** @type {?} */
    SwtCommonModule.prototype.versionNumber;
    /** @type {?} */
    SwtCommonModule.prototype.availableScreen;
    /** @type {?} */
    SwtCommonModule.prototype.objectType;
    /** @type {?} */
    SwtCommonModule.prototype.recordId;
    /** @type {?} */
    SwtCommonModule.prototype.partyType;
    /** @type {?} */
    SwtCommonModule.prototype.closeURL;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.mLoader;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.childPanel;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._inputData;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype.showJSON;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype.popUpScreen;
    /** @type {?} */
    SwtCommonModule.prototype.context;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._element;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbW1vbi1tb2R1bGUuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1jb21tb24tbW9kdWxlLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsWUFBWSxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQzVGLE9BQU8sRUFBQyxTQUFTLEVBQUMsTUFBTSx3QkFBd0IsQ0FBQztBQUNqRCxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0seUJBQXlCLENBQUM7QUFFdEQsT0FBTyxFQUFDLFVBQVUsRUFBQyxNQUFNLG1DQUFtQyxDQUFDO0FBRzdELE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSwrQkFBK0IsQ0FBQztBQUMxRCxPQUFPLEVBQUMsaUJBQWlCLEVBQUMsTUFBTSxxQ0FBcUMsQ0FBQztBQUN0RSxPQUFPLEVBQUMsaUJBQWlCLEVBQUMsTUFBTSxxQ0FBcUMsQ0FBQztBQUN0RSxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sNEJBQTRCLENBQUM7QUFDcEQsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLDJCQUEyQixDQUFDO0FBQ2xELE9BQU8sRUFBQyxTQUFTLEVBQUMsTUFBTSx3QkFBd0IsQ0FBQztBQUNqRCxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFDMUMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLHdCQUF3QixDQUFDO0FBQ2pELE9BQU8sRUFBQyxlQUFlLEVBQUMsTUFBTSwrQkFBK0IsQ0FBQztBQUM5RCxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0sNEJBQTRCLENBQUM7QUFFekQsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLDJCQUEyQixDQUFDO0FBQ25ELE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSxtQ0FBbUMsQ0FBQztBQUMvRCxPQUFPLEVBQUMsZUFBZSxFQUFDLE1BQU0sd0NBQXdDLENBQUM7QUFDdkUsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLG9DQUFvQyxDQUFDO0FBQzdELE9BQU8sRUFBQyxzQkFBc0IsRUFBQyxNQUFNLG1DQUFtQyxDQUFDO0FBQ3pFLE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSxnQ0FBZ0MsQ0FBQztBQUM1RCxPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0sNkJBQTZCLENBQUM7QUFFeEQsT0FBTyxFQUFFLE1BQU0sRUFBRSxNQUFNLDJCQUEyQixDQUFDOzs7TUFJN0MsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQUM7QUFpRjNCLE1BQU0sT0FBTyxlQUFnQixTQUFRLFNBQVM7Ozs7O0lBZ0kxQyxZQUFvQixRQUFvQixFQUFVLGNBQTZCO1FBQzNFLEtBQUssQ0FBQyxRQUFRLEVBQUUsY0FBYyxDQUFDLENBQUM7UUFEaEIsYUFBUSxHQUFSLFFBQVEsQ0FBWTtRQUFVLG1CQUFjLEdBQWQsY0FBYyxDQUFlO1FBOUhuRCxzQkFBaUIsR0FBRyxJQUFJLFlBQVksRUFBbUIsQ0FBQzs7UUFFM0QsbUJBQWMsR0FBRyxJQUFJLFlBQVksRUFBbUIsQ0FBQzs7UUFpQjVELFlBQU8sR0FBVyxFQUFFLENBQUM7UUFDaEMscUJBQWdCLEdBQVcsdUJBQXVCLENBQUM7UUFDbkQsbUJBQWMsR0FBVyxFQUFFLENBQUM7UUFDNUIsZ0JBQVcsR0FBVyxJQUFJLENBQUM7UUFDM0IsWUFBTyxHQUFXLEVBQUUsQ0FBQztRQUNyQixpQkFBWSxHQUFXLEVBQUUsQ0FBQztRQUMxQixlQUFVLEdBQVcsRUFBRSxDQUFDO1FBQ3hCLGtCQUFhLEdBQUcsSUFBSSxLQUFLLEVBQUUsQ0FBQzs7UUFFNUIscUJBQWdCLEdBQVcsSUFBSSxDQUFDOztRQUVoQyxlQUFVLEdBQVcsSUFBSSxDQUFDOztRQUUxQixlQUFVLEdBQVcsSUFBSSxDQUFDOztRQUUxQixjQUFTLEdBQVcsSUFBSSxDQUFDOztRQUV6QixpQkFBWSxHQUFXLElBQUksQ0FBQzs7UUFFNUIsZUFBVSxHQUFXLEdBQUcsQ0FBQzs7UUFJekIsZ0JBQVcsR0FBVyxJQUFJLENBQUM7UUFDM0Isa0JBQWEsR0FBVyxJQUFJLENBQUM7O1FBRTdCLFlBQU8sR0FBUyxJQUFJLENBQUM7O1FBRXJCLGFBQVEsR0FBYSxJQUFJLENBQUM7UUFDMUIsZUFBVSxHQUFlLElBQUksVUFBVSxFQUFFLENBQUM7O1FBSTFDLGNBQVMsR0FBYyxJQUFJLENBQUM7UUFDNUIsaUJBQVksR0FBYyxJQUFJLENBQUM7UUFDL0IsZUFBVSxHQUFjLElBQUksQ0FBQztRQUM3QixvQkFBZSxHQUFjLElBQUksQ0FBQztRQUNsQyxpQkFBWSxHQUFjLElBQUksQ0FBQztRQUMvQixpQkFBWSxHQUFjLElBQUksQ0FBQztRQUMvQixpQkFBWSxHQUFjLElBQUksQ0FBQztRQUMvQixlQUFVLEdBQWMsSUFBSSxDQUFDO1FBQzdCLGNBQVMsR0FBYyxJQUFJLENBQUM7UUFDNUIsZ0JBQVcsR0FBYyxJQUFJLENBQUM7UUFDOUIsaUJBQVksR0FBYSxJQUFJLENBQUM7UUFDOUIsb0JBQWUsR0FBYSxJQUFJLENBQUM7UUFDakMscUJBQWdCLEdBQWEsSUFBSSxDQUFDO1FBQ2xDLG1CQUFjLEdBQWEsSUFBSSxDQUFDOztRQUloQyxzQkFBaUIsR0FBVyxJQUFJLENBQUM7O1FBRWpDLHlCQUFvQixHQUFXLElBQUksQ0FBQzs7UUFFcEMsdUJBQWtCLEdBQVcsSUFBSSxDQUFDOztRQUVsQyx5QkFBb0IsR0FBVyxJQUFJLENBQUM7O1FBRXBDLDRCQUF1QixHQUFXLElBQUksQ0FBQzs7UUFFdkMsdUJBQWtCLEdBQVcsSUFBSSxDQUFDOztRQUVsQyx1QkFBa0IsR0FBVyxJQUFJLENBQUM7O1FBRWxDLDBCQUFxQixHQUFXLElBQUksQ0FBQzs7UUFFckMsd0JBQW1CLEdBQVcsSUFBSSxDQUFDOztRQUVuQywwQkFBcUIsR0FBVyxJQUFJLENBQUM7O1FBRXJDLDZCQUF3QixHQUFXLElBQUksQ0FBQzs7UUFFeEMsaUJBQVksR0FBUSxJQUFJLENBQUM7O1FBRXpCLG9CQUFlLEdBQVEsSUFBSSxDQUFDOztRQUU1QixrQkFBYSxHQUFRLElBQUksQ0FBQzs7UUFFMUIsb0JBQWUsR0FBUSxJQUFJLENBQUM7O1FBRTVCLHVCQUFrQixHQUFRLElBQUksQ0FBQzs7UUFFL0IsMkJBQXNCLEdBQVEsSUFBSSxDQUFDOztRQUVuQyxrQkFBYSxHQUFRLElBQUksQ0FBQztRQUMxQiw2QkFBd0IsR0FBVyxDQUFDLENBQUM7UUFDckMsc0JBQWlCLEdBQVcsQ0FBQyxDQUFDO1FBQzlCLGlCQUFZLEdBQVcsRUFBRSxDQUFDO1FBRTFCLGdCQUFXLEdBQVcsRUFBRSxDQUFDO1FBQ3pCLGVBQVUsR0FBVyxFQUFFLENBQUM7UUFDeEIsa0JBQWEsR0FBVyxFQUFFLENBQUM7UUFDM0Isb0JBQWUsR0FBVyxJQUFJLENBQUM7UUFDL0IsZUFBVSxHQUFXLElBQUksQ0FBQztRQUMxQixhQUFRLEdBQVcsSUFBSSxDQUFDO1FBQ3hCLGNBQVMsR0FBVyxJQUFJLENBQUM7UUFDekIsYUFBUSxHQUFXLElBQUksQ0FBQzs7UUFFckIsWUFBTyxHQUFpQixJQUFJLENBQUM7O1FBRTdCLGVBQVUsR0FBZ0IsSUFBSSxDQUFDO1FBRWpDLGVBQVUsR0FBYyxJQUFJLFNBQVMsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFM0QsZ0JBQVcsR0FBVyxJQUFJLENBQUM7UUFLL0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLFFBQVEsQ0FBQyxjQUFjLENBQUMsQ0FBQztRQUM3QyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLGlCQUFpQixFQUFFLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUMzRSxDQUFDOzs7Ozs7SUFLRCxrQkFBa0IsQ0FBQyxLQUFLO1FBQ3BCLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDdEMsQ0FBQzs7Ozs7SUFFRCx3QkFBd0IsQ0FBQyxLQUFLO1FBQzFCLElBQUksSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNkLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQzVDLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQzdDLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1NBQzVCO2FBQU07WUFDSCxDQUFDLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUM1QyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztTQUNoRDtRQUNELElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ25DLENBQUM7Ozs7Ozs7O0lBUUQsTUFBTSxDQUFDLElBQVksRUFBRSxnQkFBeUIsSUFBSSxFQUFFLGdCQUFxQixJQUFJOzs7WUFFckUsYUFBYSxHQUFXLENBQUM7O1lBQ3pCLFFBQVEsR0FBVyxJQUFJOztZQUN2QixZQUFZLEdBQVcsRUFBRTs7WUFDekIsbUJBQXdCO1FBQzVCLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLHlCQUF5QixDQUFDLENBQUM7UUFDNUMsSUFBSTtZQUVBLGFBQWEsR0FBRyxFQUFFLENBQUM7O2tCQUNiLGNBQWMsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ3pDLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsbUJBQW1CLEdBQUcsV0FBVyxDQUFDLGlCQUFpQixDQUFDLGFBQWEsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQy9FLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsNEJBQTRCO1lBQzVCLFFBQVEsR0FBRyxpQkFBaUIsQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1lBQ2xELGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIscUJBQXFCO1lBQ3JCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDO1lBQ3hDLFlBQVksR0FBRyxPQUFPLEdBQUcsSUFBSSxDQUFDO1lBQzlCLFlBQVksR0FBRyxZQUFZLEdBQUcsMkNBQTJDLEdBQUcsUUFBUSxHQUFHLGdCQUFnQixDQUFDO1lBQ3hHLFlBQVksR0FBRyxZQUFZLEdBQUcsYUFBYSxHQUFHLElBQUksQ0FBQyxTQUFTLEdBQUcsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLGFBQWE7a0JBQy9GLFlBQVksR0FBRyxXQUFXLENBQUMsUUFBUSxDQUFDLGNBQWMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUM7a0JBQzdGLGlCQUFpQixHQUFHLFdBQVcsQ0FBQyxRQUFRLENBQUMsbUJBQW1CLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUM7WUFDNUgsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQix1Q0FBdUM7WUFDdkMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLHVCQUF1QixFQUFFLElBQUksQ0FBQyxVQUFVLEdBQUcsWUFBWSxFQUFFLGFBQWEsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1NBRW5LO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyw0QkFBNEIsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUN2RCxJQUFJLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLG1CQUFtQixDQUFDLENBQUM7WUFDeEQsT0FBTyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEVBQUUsT0FBTyxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsRUFBRSxRQUFRLEVBQUUsYUFBYSxDQUFDLENBQUM7U0FDaEg7UUFDRCxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDO0lBQzlDLENBQUM7Ozs7Ozs7SUFVTSxtQkFBbUIsQ0FBQyxLQUFLOzs7WUFFeEIsYUFBYSxHQUFXLENBQUM7UUFFN0IsSUFBSTtZQUNBLGFBQWEsR0FBRyxDQUFDLENBQUM7OztrQkFFWixXQUFXLEdBQUcsWUFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLElBQUk7WUFDaEQsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sS0FBSyxRQUFRLENBQUMsS0FBSyxDQUFDLEVBQUU7Z0JBQ3BDLGFBQWEsR0FBRyxFQUFFLENBQUM7Z0JBQ25CLElBQUksV0FBVyxLQUFLLGFBQWEsRUFBRTtvQkFDL0IsYUFBYSxHQUFHLEVBQUUsQ0FBQztvQkFDbkIsSUFBSSxDQUFDLGtCQUFrQixDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUNsQztxQkFDSSxJQUFJLFdBQVcsS0FBSyxLQUFLLEVBQUU7b0JBQzVCLGFBQWEsR0FBRyxFQUFFLENBQUM7b0JBQ25CLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7aUJBQ3RCO3FCQUNJLElBQUksV0FBVyxLQUFLLE9BQU8sRUFBRTtvQkFDOUIsYUFBYSxHQUFHLEVBQUUsQ0FBQztvQkFDbkIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDdEI7cUJBQ0ksSUFBSSxXQUFXLEtBQUssS0FBSyxFQUFFO29CQUM1QixhQUFhLEdBQUcsRUFBRSxDQUFDO29CQUNuQixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUN0QjtxQkFDSSxJQUFJLFdBQVcsS0FBSyxlQUFlLEVBQUU7b0JBQ3RDLGFBQWEsR0FBRyxFQUFFLENBQUM7b0JBQ25CLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQztpQkFDckI7cUJBQ0ksSUFBSSxXQUFXLEtBQUssV0FBVyxFQUFFO29CQUNsQyxhQUFhLEdBQUcsRUFBRSxDQUFDO29CQUNuQixJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUM1QjtxQkFDSSxJQUFJLFdBQVcsS0FBSyxjQUFjLEVBQUU7b0JBQ3JDLGFBQWEsR0FBRyxFQUFFLENBQUM7b0JBQ25CLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUM7aUJBQzVCO3FCQUNJLElBQUksV0FBVyxLQUFLLFlBQVksRUFBRTtvQkFDbkMsYUFBYSxHQUFHLEVBQUUsQ0FBQztvQkFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDNUI7cUJBQ0ksSUFBSSxXQUFXLEtBQUssaUJBQWlCLEVBQUU7b0JBQ3hDLGFBQWEsR0FBRyxFQUFFLENBQUM7b0JBQ25CLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUM7aUJBQzVCO3FCQUNJLElBQUksV0FBVyxLQUFLLFdBQVcsRUFBRTtvQkFDbEMsYUFBYSxHQUFHLEVBQUUsQ0FBQztvQkFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDNUI7YUFDSjtTQUNKO1FBQ0QsT0FBTyxLQUFLLEVBQUU7WUFDViw2QkFBNkI7WUFDN0IsT0FBTyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEVBQUUsT0FBTyxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsRUFBRSxxQkFBcUIsRUFBRSxhQUFhLENBQUMsQ0FBQztTQUM3SDtJQUNMLENBQUM7Ozs7OztJQVNNLFVBQVU7OztZQUVULGFBQWEsR0FBVyxDQUFDO1FBQzdCLElBQUk7WUFDQSxJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUN0QyxhQUFhLEdBQUcsRUFBRSxDQUFDO1lBQ25CLDZCQUE2QjtZQUM3QixJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUM7WUFDbEQsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQix5Q0FBeUM7WUFDekMsSUFBSSxDQUFDLG9DQUFvQyxDQUFDLEtBQUssQ0FBQyxDQUFDOztrQkFDM0MsS0FBSyxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQywwQkFBMEIsQ0FBQztZQUNuRSxhQUFhLEdBQUcsRUFBRSxDQUFDOzs7a0JBRWIsSUFBSSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDO1lBQ3ZELG1CQUFtQjtZQUNuQixvRkFBb0Y7U0FDdkY7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUUsWUFBWSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1NBQ3BIO0lBQ0wsQ0FBQzs7Ozs7O0lBTUQsUUFBUTtJQUVSLENBQUM7Ozs7OztJQUtTLGNBQWM7UUFDcEIsSUFBSSxJQUFJLENBQUMsY0FBYyxFQUFFO1lBQ3JCLE9BQU8sSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDO1NBQ2hDO2FBQU07WUFDSCxNQUFNLElBQUksS0FBSyxDQUFDLGlIQUFpSCxDQUFDLENBQUM7U0FDdEk7SUFDTCxDQUFDOzs7Ozs7SUFLUyxXQUFXOztjQUNYLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxFQUFFOztjQUNsQyxPQUFPLEdBQVEsV0FBVyxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQztRQUMxRSxPQUFPLE9BQU8sQ0FBQyxXQUFXLEVBQUUsQ0FBQztJQUNqQyxDQUFDOzs7Ozs7O0lBTVMsWUFBWSxDQUFDLEtBQUs7OztjQUVsQixhQUFhLEdBQVcsQ0FBQzs7O1lBRTNCLFdBQVcsR0FBVyxJQUFJO1FBQzlCLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLCtCQUErQixDQUFDLENBQUM7UUFDbEQsSUFBSTtZQUVBLGtDQUFrQztZQUNsQyxXQUFXLEdBQUcsWUFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLElBQUksQ0FBQztZQUMzQyxJQUFJLFdBQVcsS0FBSyxXQUFXLElBQUksSUFBSSxDQUFDLGlCQUFpQixJQUFJLElBQUksRUFBRTtnQkFDL0QsbUJBQW1CO2dCQUNuQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQzthQUNwRTtpQkFDSSxJQUFJLFdBQVcsS0FBSyxjQUFjLElBQUksSUFBSSxDQUFDLG9CQUFvQixJQUFJLElBQUksRUFBRTtnQkFDMUUscUJBQXFCO2dCQUNyQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQzthQUMxRTtpQkFDSSxJQUFJLFdBQVcsS0FBSyxZQUFZLElBQUksSUFBSSxDQUFDLGtCQUFrQixJQUFJLElBQUksRUFBRTtnQkFDdEUsbUJBQW1CO2dCQUNuQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQzthQUN0RTtZQUNELElBQUksV0FBVyxLQUFLLGNBQWMsRUFBRTtnQkFDaEMsbUJBQW1CO2dCQUNuQixJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7YUFDdkI7aUJBQ0ksSUFBSSxXQUFXLEtBQUssaUJBQWlCLEVBQUU7Z0JBQ3hDLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQzthQUMxQjtpQkFBTSxJQUFJLFdBQVcsS0FBSyxXQUFXLEVBQUU7Z0JBQ3BDLHlCQUF5QjthQUU1QjtpQkFBTSxJQUFJLFdBQVcsS0FBSyxhQUFhLEVBQUU7O3NCQUNoQyxHQUFHLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7Z0JBQ3JELHNCQUFzQixDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQztnQkFDakUsSUFBSSxDQUFDLGtCQUFrQixDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQ2xDO1NBRUo7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGtDQUFrQyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBQzdELE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUUsY0FBYyxFQUFFLGFBQWEsQ0FBQyxDQUFDO1NBQ3RIO1FBQ0QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsNkJBQTZCLENBQUMsQ0FBQztJQUNwRCxDQUFDOzs7Ozs7Ozs7O0lBT1MsVUFBVSxDQUFDLEtBQWEsRUFBRSxHQUFXO1FBQzNDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLDZCQUE2QixDQUFDLENBQUM7O1lBQzVDLGFBQWEsR0FBVyxDQUFDO1FBQzdCLElBQUk7WUFDQSxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssSUFBSSxFQUFFO2dCQUV2QixJQUFJLENBQUMsVUFBVSxHQUFHLGVBQWUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUM1RCxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUM7Z0JBQzlCLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztnQkFDL0IsSUFBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDO2dCQUNwQyxJQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUM7Z0JBQ3BDLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxZQUFZLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO2dCQUNyRCxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUM7Z0JBQ2hDLElBQUksQ0FBQyxPQUFPLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQztnQkFDL0IsYUFBYSxHQUFHLEVBQUUsQ0FBQztnQkFDbkIsNEJBQTRCO2dCQUM1QixJQUFJLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLFdBQVcsQ0FBQyxLQUFLOzs7O2dCQUFFLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLENBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQztnQkFDakcsYUFBYSxHQUFHLEVBQUUsQ0FBQztnQkFDbkIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDaEM7aUJBQU07Z0JBQ0gsZUFBZSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLGdEQUFnRDthQUN2RjtTQUVKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUM5RDtRQUNELElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLDJCQUEyQixDQUFDLENBQUM7SUFDbEQsQ0FBQzs7Ozs7Ozs7SUFTUyx1QkFBdUIsQ0FBQyxLQUFLOzs7WUFHL0IsYUFBYSxHQUFXLENBQUM7UUFFN0IsSUFBSTtZQUNBLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3ZDLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsU0FBUzs7OztZQUFDLENBQUMsS0FBSyxFQUFFLEVBQUU7Z0JBQ3hDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN4QyxDQUFDLEVBQUMsQ0FBQztZQUNILElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDMUIsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixrREFBa0Q7WUFDbEQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO1lBQy9CLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztZQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7WUFDMUIsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO1lBQzdCLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztZQUMxQixJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7WUFDN0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO1lBQzVCLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztZQUMvQixJQUFJLENBQUMsY0FBYyxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7WUFDcEMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO1lBQ3ZDLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsaUNBQWlDO1lBQ2pDLElBQUksSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLEVBQUU7Z0JBQ3hCLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztnQkFDL0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO2FBQ3JDO1lBQ0Qsb0NBQW9DO1lBQ3BDLElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLEVBQUU7Z0JBQzNCLHdCQUF3QjtnQkFDeEIsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO2dCQUNsQyxJQUFJLENBQUMsWUFBWSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7YUFDeEM7WUFDRCx5Q0FBeUM7WUFDekMsSUFBSSxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksRUFBRTtnQkFDekIsc0JBQXNCO2dCQUN0QixJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0JBQ2hDLElBQUksQ0FBQyxVQUFVLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQzthQUN0QztZQUNELDJDQUEyQztZQUMzQyxJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxFQUFFO2dCQUMzQix3QkFBd0I7Z0JBQ3hCLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztnQkFDbEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO2FBQ3hDO1lBQ0QsdUNBQXVDO1lBQ3ZDLElBQUksSUFBSSxDQUFDLGVBQWUsSUFBSSxJQUFJLEVBQUU7Z0JBQzlCLDJCQUEyQjtnQkFDM0IsSUFBSSxDQUFDLGVBQWUsQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO2dCQUNyQyxJQUFJLENBQUMsZUFBZSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7YUFDM0M7WUFDRCxJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxFQUFFO2dCQUMzQixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQzthQUN4QztTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixPQUFPLENBQUMsUUFBUSxDQUFDLEtBQUssRUFBRSxPQUFPLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFFLHlCQUF5QixFQUFFLGFBQWEsQ0FBQyxDQUFDO1NBQ2pJO0lBQ0wsQ0FBQzs7Ozs7Ozs7Ozs7SUFVUyx1QkFBdUIsQ0FBQyxLQUFLOzs7WUFFL0IsYUFBYSxHQUFXLENBQUM7UUFDN0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsMENBQTBDLENBQUMsQ0FBQztRQUM3RCxJQUFJO1lBQ0EsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixJQUFJLENBQUMsU0FBUyxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7WUFDOUIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1lBQ2pDLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztZQUN6QixJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7WUFDNUIsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO1lBQ3pCLElBQUksQ0FBQyxJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztZQUM1QixJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7WUFDM0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1lBQzlCLElBQUksQ0FBQyxjQUFjLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztZQUNuQyxJQUFJLENBQUMsY0FBYyxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7WUFDdEMsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxFQUFFO2dCQUMzQixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7Z0JBQ2pDLElBQUksQ0FBQyxZQUFZLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQzthQUN2QztZQUNELGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO2dCQUNwQixJQUFJLElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxLQUFLLENBQUMsQ0FBQyxFQUFFO29CQUN6QyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7b0JBQy9CLElBQUksQ0FBQyxVQUFVLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztvQkFDbEMsSUFBSSxJQUFJLENBQUMsVUFBVSxLQUFLLEdBQUcsRUFBRTt3QkFFekIsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksRUFBRTs0QkFDM0IsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDOzRCQUNqQyxJQUFJLENBQUMsWUFBWSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7eUJBQ3ZDO3dCQUVELElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLEVBQUU7NEJBQzNCLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQzs0QkFDakMsSUFBSSxDQUFDLFlBQVksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO3lCQUN2Qzt3QkFFRCxJQUFJLElBQUksQ0FBQyxlQUFlLElBQUksSUFBSSxFQUFFOzRCQUM5QixJQUFJLENBQUMsZUFBZSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7NEJBQ3BDLElBQUksQ0FBQyxlQUFlLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQzt5QkFDMUM7cUJBQ0o7eUJBQU07d0JBRUgsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksRUFBRTs0QkFDM0IsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDOzRCQUNsQyxJQUFJLENBQUMsWUFBWSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7eUJBQ3hDO3dCQUVELElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLEVBQUU7NEJBQzNCLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQzs0QkFDbEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO3lCQUN4Qzt3QkFFRCxJQUFJLElBQUksQ0FBQyxlQUFlLElBQUksSUFBSSxFQUFFOzRCQUM5QixJQUFJLENBQUMsZUFBZSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7NEJBQ3JDLElBQUksQ0FBQyxlQUFlLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQzt5QkFDM0M7cUJBQ0o7aUJBQ0o7cUJBQU07b0JBQ0gsYUFBYSxHQUFHLEVBQUUsQ0FBQztvQkFDbkIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO29CQUNoQyxJQUFJLENBQUMsVUFBVSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7b0JBRW5DLElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLEVBQUU7d0JBQzNCLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQzt3QkFDbEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO3FCQUN4QztvQkFFRCxJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxFQUFFO3dCQUMzQixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7d0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztxQkFDeEM7b0JBRUQsSUFBSSxJQUFJLENBQUMsZUFBZSxJQUFJLElBQUksRUFBRTt3QkFDOUIsSUFBSSxDQUFDLGVBQWUsQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO3dCQUNyQyxJQUFJLENBQUMsZUFBZSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7cUJBQzNDO2lCQUVKO2FBQ0o7WUFFRCx1QkFBdUI7WUFDdkIsSUFBSSxJQUFJLENBQUMsVUFBVSxLQUFLLEdBQUcsRUFBRTtnQkFDekIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO2dCQUM5QixJQUFJLENBQUMsU0FBUyxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7Z0JBRWpDLElBQUksSUFBSSxDQUFDLGVBQWUsSUFBSSxJQUFJLEVBQUU7b0JBQzlCLElBQUksQ0FBQyxlQUFlLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztvQkFDcEMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO2lCQUMxQzthQUVKO2lCQUFNO2dCQUNILElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztnQkFDL0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO2FBQ3JDO1lBQ0QscUNBQXFDO1lBQ3JDLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1NBQzFCO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyw2Q0FBNkMsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUN4RSxPQUFPLENBQUMsUUFBUSxDQUFDLEtBQUssRUFBRSxPQUFPLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFFLHlCQUF5QixFQUFFLGFBQWEsQ0FBQyxDQUFDO1NBQ2pJO1FBQ0QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsd0NBQXdDLENBQUMsQ0FBQztJQUMvRCxDQUFDOzs7Ozs7OztJQU9TLE9BQU87UUFDYixvQ0FBb0M7UUFDcEMsSUFBSSxJQUFJLENBQUMsU0FBUyxJQUFJLElBQUksRUFBRTtZQUN4QixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7WUFDNUIsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUM7U0FDekI7UUFDRCxJQUFJLElBQUksQ0FBQyxVQUFVLElBQUksSUFBSSxFQUFFO1lBQ3pCLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQztZQUM3QixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztTQUMxQjtRQUNELElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLEVBQUU7WUFDM0IsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO1lBQy9CLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDO1NBQzVCO1FBQ0QsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksRUFBRTtZQUMzQixJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7WUFDL0IsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUM7U0FDNUI7UUFDRCxJQUFJLElBQUksQ0FBQyxlQUFlLElBQUksSUFBSSxFQUFFO1lBQzlCLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQztZQUNsQyxJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQztTQUMvQjtRQUNELElBQUksSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLEVBQUU7WUFDeEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO1lBQzVCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO1NBQ3pCO1FBQ0QsRUFBRTtRQUNGLG1GQUFtRjtRQUNuRiw0QkFBNEI7UUFDNUIsMENBQTBDO1FBQzFDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDO1FBQzFCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUM7UUFDN0IsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQztRQUM3QixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztRQUNwQixJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztRQUN0QixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztRQUNwQixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztJQUMzQixDQUFDOzs7Ozs7O0lBT1Msa0JBQWtCLENBQUMsS0FBSzs7WUFDMUIsYUFBYSxHQUFXLENBQUM7UUFDN0IsSUFBSTtZQUNBLGFBQWEsR0FBRyxFQUFFLENBQUM7WUFDbkIsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ2YsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixpREFBaUQ7WUFDakQsSUFBSSxJQUFJLENBQUMsWUFBWSxLQUFLLE1BQU0sRUFBRTtnQkFDOUIsYUFBYSxHQUFHLEVBQUUsQ0FBQztnQkFDbkIsaUJBQWlCLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2FBQ3pDO2lCQUFNLElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxPQUFPLEVBQUU7Z0JBQ3JDLGFBQWEsR0FBRyxFQUFFLENBQUM7YUFDdEI7aUJBQU07Z0JBQ0gsYUFBYSxHQUFHLEVBQUUsQ0FBQztnQkFDbkIsaUJBQWlCLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUM7YUFDeEY7U0FDSjtRQUNELE9BQU8sS0FBSyxFQUFFO1lBQ1YsT0FBTyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEVBQUUsT0FBTyxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUUsb0JBQW9CLEVBQUUsYUFBYSxDQUFDLENBQUM7U0FDekg7SUFDTCxDQUFDOzs7Ozs7OztJQVNTLG9DQUFvQyxDQUFDLEtBQUs7OztZQUU1QyxhQUFhLEdBQVcsQ0FBQztRQUM3QixJQUFJO1lBQ0EsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixJQUFJLENBQUMsdUJBQXVCLENBQUMsS0FBSyxDQUFDLENBQUM7U0FDdkM7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUUsc0NBQXNDLEVBQUUsYUFBYSxDQUFDLENBQUM7U0FDOUk7SUFDTCxDQUFDOzs7Ozs7OztJQVNTLG9DQUFvQyxDQUFDLEtBQUs7OztjQUUxQyxhQUFhLEdBQVcsQ0FBQztRQUMvQixJQUFJO1NBRUg7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEdBQUcsT0FBTyxFQUFFLHNDQUFzQyxFQUFFLGFBQWEsQ0FBQyxDQUFDO1NBQ3hKO0lBRUwsQ0FBQzs7Ozs7O0lBS1MsWUFBWTtRQUNsQixJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1NBQ3ZCO2FBQU07WUFDSCxNQUFNLElBQUksS0FBSyxDQUFDLDJDQUEyQyxDQUFDLENBQUM7U0FDaEU7SUFDTCxDQUFDOzs7Ozs7SUFLUyxlQUFlO1FBQ3JCLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN0QixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7U0FDMUI7YUFBTTtZQUNILE1BQU0sSUFBSSxLQUFLLENBQUMsOENBQThDLENBQUMsQ0FBQztTQUNuRTtJQUNMLENBQUM7Ozs7O0lBRU8saUJBQWlCOzs7Y0FFZixPQUFPLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDO1FBQ3ZDLHlCQUF5QjtRQUN6QixLQUFLLE1BQU0sS0FBSyxJQUFJLE9BQU8sRUFBRTtZQUN6QixJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxLQUFLLEVBQUU7Z0JBQzFCLElBQUksQ0FBQyxTQUFTLEdBQUcsbUJBQUEsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQWEsQ0FBQztnQkFDbkUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDN0UsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUN2RixJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsR0FBRyxNQUFNLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUM1QyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUM5QyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUNoRCxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUs7Ozs7Z0JBQUcsQ0FBQyxLQUFLLEVBQUUsRUFBRTtvQkFDN0IsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDN0IsQ0FBQyxDQUFBLENBQUM7YUFDTDtpQkFBTSxJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLEVBQUU7Z0JBQ3BDLElBQUksQ0FBQyxZQUFZLEdBQUcsbUJBQUEsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQWEsQ0FBQztnQkFDdEUsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDaEYsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUMxRixJQUFJLENBQUMsWUFBWSxDQUFDLFFBQVEsR0FBRyxNQUFNLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUMvQyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUNqRCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUNuRCxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUNoQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO2lCQUFNLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLE1BQU0sRUFBRTtnQkFDbEMsSUFBSSxDQUFDLFVBQVUsR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO2dCQUNwRSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM5RSxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQ3hGLElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7Z0JBQzdDLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQy9DLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ2pELElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztnQkFDaEMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLOzs7O2dCQUFHLENBQUMsS0FBSyxFQUFFLEVBQUU7b0JBQzlCLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQzdCLENBQUMsQ0FBQSxDQUFDO2FBQ0w7aUJBQU0sSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssUUFBUSxFQUFFO2dCQUNwQyxJQUFJLENBQUMsWUFBWSxHQUFHLG1CQUFBLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFhLENBQUM7Z0JBQ3RFLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQ2hGLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxpQkFBaUIsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDMUYsSUFBSSxDQUFDLFlBQVksQ0FBQyxRQUFRLEdBQUcsTUFBTSxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQztnQkFDL0MsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxHQUFHLFFBQVEsQ0FBQztnQkFDakQsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxHQUFHLFFBQVEsQ0FBQztnQkFDbkQsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO2dCQUNsQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUs7Ozs7Z0JBQUcsQ0FBQyxLQUFLLEVBQUUsRUFBRTtvQkFDaEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDN0IsQ0FBQyxDQUFBLENBQUM7YUFDTDtpQkFBTSxJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxXQUFXLEVBQUU7Z0JBQ3ZDLElBQUksQ0FBQyxlQUFlLEdBQUcsbUJBQUEsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQWEsQ0FBQztnQkFDekUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDbkYsSUFBSSxDQUFDLGVBQWUsQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM3RixJQUFJLENBQUMsZUFBZSxDQUFDLFFBQVEsR0FBRyxNQUFNLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNsRCxJQUFJLENBQUMsZUFBZSxDQUFDLEVBQUUsR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUNwRCxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLEdBQUcsUUFBUSxDQUFDO2dCQUN0RCxJQUFJLENBQUMsZUFBZSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0JBQ3JDLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUNuQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO2lCQUFNLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDcEMsSUFBSSxDQUFDLFlBQVksR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO2dCQUN0RSxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUNoRixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQzFGLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7Z0JBQy9DLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ2pELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ25ELElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUNoQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO2lCQUFNLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDcEMsSUFBSSxDQUFDLFlBQVksR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO2dCQUN0RSxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUNoRixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQzFGLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7Z0JBQy9DLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ2pELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ25ELElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUNoQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO2lCQUFNLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLE1BQU0sRUFBRTtnQkFDbEMsSUFBSSxDQUFDLFVBQVUsR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO2dCQUNwRSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM5RSxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQ3hGLElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7Z0JBQzdDLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQy9DLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ2pELElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUM5QixJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO2lCQUFNLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLE9BQU8sRUFBRTtnQkFDbkMsSUFBSSxDQUFDLFNBQVMsR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO2dCQUNuRSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM3RSxJQUFJLENBQUMsU0FBUyxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLEdBQUcsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQ3ZGLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7Z0JBQzVDLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQzlDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxRQUFRLENBQUM7Z0JBQ2hELElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSzs7OztnQkFBRyxDQUFDLEtBQUssRUFBRSxFQUFFO29CQUM3QixJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUM3QixDQUFDLENBQUEsQ0FBQzthQUNMO1NBQ0o7UUFDRCxnQ0FBZ0M7UUFDaEMsSUFBSSxDQUFDLFdBQVcsR0FBRyxtQkFBQSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBYSxDQUFDO1FBQ3JFLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxjQUFjLENBQUMsQ0FBQztRQUNuRSxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsc0JBQXNCLENBQUMsQ0FBQztRQUM3RSxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDL0IsSUFBSSxDQUFDLFdBQVcsQ0FBQyxFQUFFLEdBQUcsYUFBYSxDQUFDO1FBQ3BDLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSzs7OztRQUFHLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDL0IsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUM3QixDQUFDLENBQUEsQ0FBQztJQUNOLENBQUM7Ozs7Ozs7O0lBT08sY0FBYyxDQUFDLEdBQVc7O2NBQ3hCLElBQUksR0FBRyxJQUFJLEtBQUssRUFBRTs7WUFDcEIsYUFBcUI7UUFDekIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsaUNBQWlDLENBQUMsQ0FBQztRQUNwRCxJQUFJOztrQkFDTSxFQUFFLEdBQUcsR0FBRyxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUM7WUFDL0IsYUFBYSxHQUFHLEVBQUUsQ0FBQztZQUNuQixJQUFJLEVBQUUsS0FBSyxDQUFDLENBQUMsRUFBRTtnQkFDWCxhQUFhLEdBQUcsRUFBRSxDQUFDOztzQkFDYixLQUFLLEdBQVcsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDOztzQkFDbEMsTUFBTSxHQUFHLEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDO2dCQUMvQixhQUFhLEdBQUcsRUFBRSxDQUFDO2dCQUNuQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtvQkFDcEMsYUFBYSxHQUFHLEVBQUUsQ0FBQzs7MEJBQ2IsS0FBSyxHQUFXLE1BQU0sQ0FBQyxDQUFDLENBQUM7OzBCQUN6QixTQUFTLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7b0JBQ2xDLGFBQWEsR0FBRyxFQUFFLENBQUM7b0JBQ25CLElBQUksU0FBUyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7d0JBQ3hCLGFBQWEsR0FBRyxFQUFFLENBQUM7OzhCQUNiLEdBQUcsR0FBVyxTQUFTLENBQUMsQ0FBQyxDQUFDOzs4QkFDMUIsR0FBRyxHQUFXLFNBQVMsQ0FBQyxDQUFDLENBQUM7d0JBQ2hDLGFBQWEsR0FBRyxFQUFFLENBQUM7d0JBQ25CLElBQUksR0FBRyxLQUFLLFFBQVEsRUFBRTs0QkFDbEIsYUFBYSxHQUFHLEVBQUUsQ0FBQzs0QkFDbkIsa0JBQWtCOzRCQUNsQixJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsR0FBRyxDQUFDO3lCQUM1Qjt3QkFDRCxJQUFJLEdBQUcsS0FBSyxXQUFXLEVBQUU7NEJBQ3JCLGFBQWEsR0FBRyxHQUFHLENBQUM7NEJBQ3BCLGVBQWU7NEJBQ2YsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLEdBQUcsQ0FBQzt5QkFDM0I7d0JBQ0QsSUFBSSxHQUFHLEtBQUssYUFBYSxFQUFFOzRCQUN2QixhQUFhLEdBQUcsR0FBRyxDQUFDOzRCQUNwQixpQkFBaUI7NEJBQ2pCLElBQUksQ0FBQyxhQUFhLENBQUMsR0FBRyxHQUFHLENBQUM7eUJBQzdCO3dCQUNELElBQUksR0FBRyxLQUFLLGlCQUFpQixFQUFFOzRCQUMzQixhQUFhLEdBQUcsR0FBRyxDQUFDOzRCQUNwQixvQkFBb0I7NEJBQ3BCLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLEdBQUcsQ0FBQzt5QkFDakM7d0JBQ0QsSUFBSSxHQUFHLEtBQUssVUFBVSxFQUFFOzRCQUNwQixhQUFhLEdBQUcsR0FBRyxDQUFDOzRCQUNwQixJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsR0FBRyxDQUFDO3lCQUMxQjt3QkFDRCxJQUFJLEdBQUcsS0FBSyxZQUFZLEVBQUU7NEJBQ3RCLGFBQWEsR0FBRyxHQUFHLENBQUM7NEJBQ3BCLElBQUksQ0FBQyxZQUFZLENBQUMsR0FBRyxHQUFHLENBQUM7eUJBQzVCO3dCQUNELElBQUksR0FBRyxLQUFLLGVBQWUsRUFBRTs0QkFDekIsYUFBYSxHQUFHLEdBQUcsQ0FBQzs0QkFDcEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLEdBQUcsQ0FBQzt5QkFDM0I7cUJBQ0o7aUJBQ0o7YUFDSjtZQUNELGlCQUFpQixDQUFDLFVBQVUsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDO1NBQzFDO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxvQ0FBb0MsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUNsRTtRQUNELElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLCtCQUErQixDQUFDLENBQUM7UUFDbEQsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQzs7O1lBaDlCSixTQUFTLFNBQUM7Z0JBQ1AsUUFBUSxFQUFFLGlCQUFpQjtnQkFDM0IsUUFBUSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQTBFVDthQUVKOzs7O1lBOUdrQixVQUFVO1lBRXJCLGFBQWE7OztnQ0ErR2hCLE1BQU0sU0FBQyxrQkFBa0I7NkJBRXpCLE1BQU0sU0FBQyxlQUFlO3FCQUN0QixLQUFLLFNBQUMsT0FBTztzQkFDYixLQUFLLFNBQUMsUUFBUTswQkFDZCxTQUFTLFNBQUMsYUFBYTt5QkFDdkIsU0FBUyxTQUFDLFlBQVk7NEJBQ3RCLFNBQVMsU0FBQyxlQUFlOzRCQUN6QixTQUFTLFNBQUMsY0FBYzs2QkFDeEIsU0FBUyxTQUFDLGVBQWU7bUJBQ3pCLFNBQVMsU0FBQyxLQUFLO3FCQUNmLFNBQVMsU0FBQyxPQUFPO21CQUNqQixTQUFTLFNBQUMsS0FBSzsyQkFDZixTQUFTLFNBQUMsYUFBYTt3QkFDdkIsU0FBUyxTQUFDLFVBQVU7OEJBRXBCLFNBQVMsU0FBQyxpQkFBaUI7NkJBQzNCLFNBQVMsU0FBQyxnQkFBZ0I7c0JBRTFCLEtBQUssU0FBQyxTQUFTOzs7O0lBbkJoQiw0Q0FBb0Y7O0lBRXBGLHlDQUE4RTs7SUFDOUUsaUNBQTRCOztJQUM1QixrQ0FBOEI7O0lBQzlCLHNDQUFpRDs7SUFDakQscUNBQStDOztJQUMvQyx3Q0FBZ0Q7O0lBQ2hELHdDQUEwRDs7SUFDMUQseUNBQXNEOztJQUN0RCwrQkFBa0M7O0lBQ2xDLGlDQUFzQzs7SUFDdEMsK0JBQWtDOztJQUNsQyx1Q0FBa0Q7O0lBQ2xELG9DQUFnRDs7SUFFaEQsMENBQTBEOztJQUMxRCx5Q0FBd0Q7O0lBRXhELGtDQUF1Qzs7SUFDdkMsMkNBQTBEOztJQUMxRCx5Q0FBbUM7O0lBQ25DLHNDQUFrQzs7SUFDbEMsa0NBQTRCOztJQUM1Qix1Q0FBaUM7O0lBQ2pDLHFDQUErQjs7SUFDL0Isd0NBQW1DOztJQUVuQywyQ0FBdUM7O0lBRXZDLHFDQUFpQzs7SUFFakMscUNBQWlDOztJQUVqQyxvQ0FBZ0M7O0lBRWhDLHVDQUFtQzs7SUFFbkMscUNBQWdDOztJQUVoQyxxQ0FBMEI7O0lBRTFCLHNDQUFrQzs7SUFDbEMsd0NBQW9DOztJQUVwQyxrQ0FBNEI7O0lBRTVCLG1DQUFpQzs7SUFDakMscUNBQWlEOztJQUNqRCwyQ0FBNkI7O0lBQzdCLDJDQUE2Qjs7SUFFN0Isb0NBQW1DOztJQUNuQyx1Q0FBc0M7O0lBQ3RDLHFDQUFvQzs7SUFDcEMsMENBQXlDOztJQUN6Qyx1Q0FBc0M7O0lBQ3RDLHVDQUFzQzs7SUFDdEMsdUNBQXNDOztJQUN0QyxxQ0FBb0M7O0lBQ3BDLG9DQUFtQzs7SUFDbkMsc0NBQXFDOztJQUNyQyx1Q0FBcUM7O0lBQ3JDLDBDQUF3Qzs7SUFDeEMsMkNBQXlDOztJQUN6Qyx5Q0FBdUM7O0lBRXZDLGdDQUE4Qjs7SUFFOUIsNENBQXdDOztJQUV4QywrQ0FBMkM7O0lBRTNDLDZDQUF5Qzs7SUFFekMsK0NBQTJDOztJQUUzQyxrREFBOEM7O0lBRTlDLDZDQUF5Qzs7SUFFekMsNkNBQXlDOztJQUV6QyxnREFBNEM7O0lBRTVDLDhDQUEwQzs7SUFFMUMsZ0RBQTRDOztJQUU1QyxtREFBK0M7O0lBRS9DLHVDQUFnQzs7SUFFaEMsMENBQW1DOztJQUVuQyx3Q0FBaUM7O0lBRWpDLDBDQUFtQzs7SUFFbkMsNkNBQXNDOztJQUV0QyxpREFBMEM7O0lBRTFDLHdDQUFpQzs7SUFDakMsbURBQTRDOztJQUM1Qyw0Q0FBcUM7O0lBQ3JDLHVDQUFpQzs7SUFDakMsd0NBQW9DOztJQUNwQyxzQ0FBZ0M7O0lBQ2hDLHFDQUErQjs7SUFDL0Isd0NBQWtDOztJQUNsQywwQ0FBc0M7O0lBQ3RDLHFDQUFpQzs7SUFDakMsbUNBQStCOztJQUMvQixvQ0FBZ0M7O0lBQ2hDLG1DQUErQjs7Ozs7SUFFL0Isa0NBQXVDOzs7OztJQUV2QyxxQ0FBeUM7Ozs7O0lBQ3pDLGlDQUF5Qjs7Ozs7SUFDekIscUNBQW1FOzs7OztJQUNuRSxtQ0FBc0I7Ozs7O0lBQ3RCLHNDQUFtQzs7SUFDbkMsa0NBQW9COzs7OztJQUVSLG1DQUE0Qjs7Ozs7SUFBRSx5Q0FBcUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0NvbXBvbmVudCwgRWxlbWVudFJlZiwgRXZlbnRFbWl0dGVyLCBJbnB1dCwgT3V0cHV0LCBWaWV3Q2hpbGR9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG5pbXBvcnQge1N3dE1vZHVsZX0gZnJvbSBcIi4vc3d0LW1vZHVsZS5jb21wb25lbnRcIjtcclxuaW1wb3J0IHtDb21tb25TZXJ2aWNlfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtWQm94fSBmcm9tIFwiLi9zd3QtdmJveC5jb21wb25lbnRcIjtcclxuaW1wb3J0IHtKU09OUmVhZGVyfSBmcm9tIFwiLi4vanNvbmhhbmRsZXIvanNvbnJlYWRlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7SGFzaE1hcH0gZnJvbSBcIi4uL3V0aWxzL0hhc2hNYXAuc2VydmljZVwiO1xyXG5pbXBvcnQge1hNTH0gZnJvbSBcIi4uL3htbGhhbmRsZXIvc3d0LXhtbC5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7U3RyaW5nVXRpbHN9IGZyb20gXCIuLi91dGlscy9zdHJpbmctdXRpbHMuc2VydmljZVwiO1xyXG5pbXBvcnQge3BhcmVudEFwcGxpY2F0aW9ufSBmcm9tIFwiLi4vdXRpbHMvcGFyZW50LWFwcGxpY2F0aW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtFeHRlcm5hbEludGVyZmFjZX0gZnJvbSBcIi4uL3V0aWxzL2V4dGVybmFsLWludGVyZmFjZS5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7U3d0QWxlcnR9IGZyb20gXCIuLi91dGlscy9zd3QtYWxlcnQuc2VydmljZVwiO1xyXG5pbXBvcnQge1N3dFV0aWx9IGZyb20gXCIuLi91dGlscy9zd3QtdXRpbC5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7U3d0Q2FudmFzfSBmcm9tIFwiLi9zd3QtY2FudmFzLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQge0hCb3h9IGZyb20gXCIuL3N3dC1oYm94LmNvbXBvbmVudFwiO1xyXG5pbXBvcnQge1N3dEJ1dHRvbn0gZnJvbSBcIi4vc3d0LWJ1dHRvbi5jb21wb25lbnRcIjtcclxuaW1wb3J0IHtTd3RMb2FkaW5nSW1hZ2V9IGZyb20gXCIuL3N3dC1sb2FkaW5nLWltYWdlLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQge1N3dEhlbHBCdXR0b259IGZyb20gXCIuL3N3dC1oZWxwQnV0dG9uLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQge1N3dENvbW1vbkdyaWR9IGZyb20gXCIuL3N3dC1jb21tb24tZ3JpZC5jb21wb25lbnRcIjtcclxuaW1wb3J0IHtLZXlib2FyZH0gZnJvbSBcIi4uL3V0aWxzL2tleWJvYXJkLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtmb2N1c01hbmFnZXJ9IGZyb20gXCIuLi9tYW5hZ2Vycy9mb2N1cy1tYW5hZ2VyLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtTd3RQb3BVcE1hbmFnZXJ9IGZyb20gXCIuLi9tYW5hZ2Vycy9zd3QtcG9wLXVwLW1hbmFnZXIuc2VydmljZVwiO1xyXG5pbXBvcnQge0hUVFBDb21tc30gZnJvbSBcIi4uL2NvbW11bmljYXRpb24vaHR0cGNvbW1zLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtTd3RUYWJOYXZpZ2F0b3JIYW5kbGVyfSBmcm9tIFwiLi4vdXRpbHMvc3d0LXRhYm5hdmlnYXRvci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7TW9kdWxlTG9hZGVyfSBmcm9tIFwiLi4vdXRpbHMvbW9kdWxlLWxvYWRlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7TW9kdWxlRXZlbnR9IGZyb20gXCIuLi9ldmVudHMvc3d0LWV2ZW50cy5tb2R1bGVcIjtcclxuaW1wb3J0IHsgVGl0bGVXaW5kb3cgfSBmcm9tICcuL3RpdGxlLXdpbmRvdy5jb21wb25lbnQnO1xyXG5pbXBvcnQgeyBMb2dnZXIgfSBmcm9tICcuLi9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlJztcclxuXHJcbmRlY2xhcmUgdmFyIHJlcXVpcmU6IGFueTtcclxuLy9pbXBvcnQgJCBmcm9tICdqcXVlcnknO1xyXG5jb25zdCAkID0gcmVxdWlyZSgnanF1ZXJ5Jyk7XHJcblxyXG5AQ29tcG9uZW50KHtcclxuICAgIHNlbGVjdG9yOiAnU3d0Q29tbW9uTW9kdWxlJyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICAgICAgPFN3dE1vZHVsZSAoY3JlYXRpb25Db21wbGV0ZSk9XCJvbkNvbW1vbk1vZHVsZUluaXQoJGV2ZW50KVwiIChwcmVpbml0aWFsaXplKT1cIm9uQ29tbW9uTW9kdWxlQmVmb3JlSW5pdCgkZXZlbnQpXCJcclxuICAgICAgICAgICAgICAgICAgIHdpZHRoPVwie3sgX3dpZHRoIH19XCIgaGVpZ2h0PVwie3sgX2hlaWdodCB9fVwiPlxyXG4gICAgICAgICAgICA8ZGl2ICNjb21tb25UZW1wbGF0ZSBbc3R5bGUud2lkdGgucHhdPVwiX3dpZHRoXCIgW3N0eWxlLmhlaWdodC5weF09XCJfaGVpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICA8bmctY29udGVudD48L25nLWNvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2ICNidXR0b25zVGVtcGxhdGUgY2xhc3M9XCJzd3Rtb2R1bGVcIiBbc3R5bGUud2lkdGgucHhdPVwiX3dpZHRoXCIgW3N0eWxlLmhlaWdodC5weF09XCJfaGVpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICA8U3d0Q2FudmFzICNjYW52YXNDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiOTAlXCIgcGFkZGluZ0xlZnQ9XCIxMFwiIHBhZGRpbmdSaWdodD1cIjEwXCIgcGFkZGluZ1RvcD1cIjEwXCI+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxTd3RDYW52YXMgI2N1c3RvbUdyaWQgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiODclXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxuZy1jb250ZW50IHNlbGVjdD1cIi5jb21tb25Nb2R1bGVcIj48L25nLWNvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Td3RDYW52YXM+XHJcbiAgICAgICAgICAgICAgICAgICAgPFN3dENhbnZhcyB3aWR0aD1cIjEwMCVcIiBtYXJnaW5Ub3A9XCIxMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SEJveCB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCIxMDAlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SEJveCAjaGJveEJ1dHRvbnMgd2lkdGg9XCIxMDAlXCI+PC9IQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhCb3ggI2hib3hFeHBvcnQgaG9yaXpvbnRhbEFsaWduPVwicmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3d0TG9hZGluZ0ltYWdlICNsb2FkaW5nSW1hZ2UgaWQ9XCJsb2FkaW5nSW1hZ2VcIj48L1N3dExvYWRpbmdJbWFnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3d0QnV0dG9uICNzZXR0aW5nQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBidXR0b25Nb2RlPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZU5hbWU9XCJyZXBvcnRJY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYkluZGV4PVwiMTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZXR0aW5nQnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChjbGljayk9XCJnZXRTZXR0aW5nKClcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGtleURvd24pPVwia2V5RG93bkhhbmRsZXJFdmVudCgkZXZlbnQpXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvb2xUaXA9XCJ7eyBnZXRDb21tb25NZXNzYWdlcygnYnV0dG9uLnJlcG9ydF9zZXR0aW5nJykgfX1cIj48L1N3dEJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3d0QnV0dG9uICNjc3ZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbk1vZGU9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYkluZGV4PVwiMTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjc3ZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlZD1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGVOYW1lPVwiY3N2SWNvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY2xpY2spPVwicmVwb3J0KCdjc3YnKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoa2V5RG93bik9XCJrZXlEb3duSGFuZGxlckV2ZW50KCRldmVudClcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvb2xUaXA9XCJ7eyBnZXRDb21tb25NZXNzYWdlcygnYnV0dG9uLnRvb2x0aXAuY3N2JykgfX1cIj48L1N3dEJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3d0QnV0dG9uICNleGNlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnV0dG9uTW9kZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFiSW5kZXg9XCIxM1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImV4Y2VsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVuYWJsZWQ9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlTmFtZT1cImV4Y2VsSWNvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY2xpY2spPVwicmVwb3J0KCd4bHMnKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoa2V5RG93bik9XCJrZXlEb3duSGFuZGxlckV2ZW50KCRldmVudClcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvb2xUaXA9XCJ7eyBnZXRDb21tb25NZXNzYWdlcygnYnV0dG9uLnRvb2x0aXAuZXhjZWwnKSB9fVwiPjwvU3d0QnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTd3RCdXR0b24gI3BkZlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnV0dG9uTW9kZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFiSW5kZXg9XCIxNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInBkZlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbmFibGVkPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZU5hbWU9XCJwZGZJY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChjbGljayk9XCJyZXBvcnQoJ3BkZicpXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChrZXlEb3duKT1cImtleURvd25IYW5kbGVyRXZlbnQoJGV2ZW50KVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9vbFRpcD1cInt7IGdldENvbW1vbk1lc3NhZ2VzKCdidXR0b24udG9vbHRpcC5wZGYnKSB9fVwiPjwvU3d0QnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTd3RCdXR0b24gI2ZhdGNhUmVwb3J0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBidXR0b25Nb2RlPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YWJJbmRleD1cIjE1XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZmF0Y2FSZXBvcnRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jbHVkZUluTGF5b3V0PVwiZmFsc2VcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlzaWJsZT1cImZhbHNlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVuYWJsZWQ9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlTmFtZT1cImZhdGNhUmVwb3J0SWNvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9vbFRpcD1cInt7IGdldENvbW1vbk1lc3NhZ2VzKCdidXR0b24udG9vbHRpcC5wZGYnKSB9fVwiPjwvU3d0QnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTd3RIZWxwQnV0dG9uICNoZWxwSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaGVscEljb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYkluZGV4PVwiMTZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbk1vZGU9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY2xpY2spPVwic2hvd2hlbHAoKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlZD1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlTmFtZT1cImhlbHBJY29uXCI+PC9Td3RIZWxwQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0hCb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Td3RDYW52YXM+XHJcbiAgICAgICAgICAgICAgICA8L1N3dENhbnZhcz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9Td3RNb2R1bGU+XHJcbiAgICBgLFxyXG4gICAgc3R5bGVzOiBbXVxyXG59KVxyXG5leHBvcnQgY2xhc3MgU3d0Q29tbW9uTW9kdWxlIGV4dGVuZHMgU3d0TW9kdWxlIHtcclxuXHJcbiAgICBAT3V0cHV0KFwiY3JlYXRpb25Db21wbGV0ZVwiKSBfY3JlYXRpb25Db21wbGV0ZSA9IG5ldyBFdmVudEVtaXR0ZXI8U3d0Q29tbW9uTW9kdWxlPigpO1xyXG4gICAgLy9zZXQgcHJlaW5pdGlhbGl6ZSBldmVudFxyXG4gICAgQE91dHB1dCgncHJlaW5pdGlhbGl6ZScpIF9wcmVpbml0aWFsaXplID0gbmV3IEV2ZW50RW1pdHRlcjxTd3RDb21tb25Nb2R1bGU+KCk7XHJcbiAgICBASW5wdXQoXCJ3aWR0aFwiKSBfd2lkdGg6IGFueTtcclxuICAgIEBJbnB1dChcImhlaWdodFwiKSBfaGVpZ2h0OiBhbnk7XHJcbiAgICBAVmlld0NoaWxkKFwiaGJveEJ1dHRvbnNcIikgaGJveEJ1dHRvbnM6IFN3dENhbnZhcztcclxuICAgIEBWaWV3Q2hpbGQoXCJjdXN0b21HcmlkXCIpIGN1c3RvbUdyaWQ6IFN3dENhbnZhcztcclxuICAgIEBWaWV3Q2hpbGQoXCJyZXBvcnRidXR0b25zXCIpIHJlcG9ydGJ1dHRvbnM6IEhCb3g7XHJcbiAgICBAVmlld0NoaWxkKFwibG9hZGluZ0ltYWdlXCIpIF9sb2FkaW5nSW1hZ2U6IFN3dExvYWRpbmdJbWFnZTtcclxuICAgIEBWaWV3Q2hpbGQoXCJzZXR0aW5nQnV0dG9uXCIpIF9zZXR0aW5nQnV0dG9uOiBTd3RCdXR0b247XHJcbiAgICBAVmlld0NoaWxkKFwiY3N2XCIpIF9jc3Y6IFN3dEJ1dHRvbjtcclxuICAgIEBWaWV3Q2hpbGQoXCJleGNlbFwiKSBfZXhjZWw6IFN3dEJ1dHRvbjtcclxuICAgIEBWaWV3Q2hpbGQoXCJwZGZcIikgX3BkZjogU3d0QnV0dG9uO1xyXG4gICAgQFZpZXdDaGlsZChcImZhdGNhUmVwb3J0XCIpIF9mYXRjYVJlcG9ydDogU3d0QnV0dG9uO1xyXG4gICAgQFZpZXdDaGlsZChcImhlbHBJY29uXCIpIF9oZWxwSWNvbjogU3d0SGVscEJ1dHRvbjtcclxuICAgIC8vIG1ha2UgbW9kdWxlIHdpdGggY29tbW9uIGJ1dHRvbnMgRE9NIHJlZmVyZW5jZS5cclxuICAgIEBWaWV3Q2hpbGQoXCJidXR0b25zVGVtcGxhdGVcIikgYnV0dG9uc1RlbXBsYXRlOiBFbGVtZW50UmVmO1xyXG4gICAgQFZpZXdDaGlsZChcImNvbW1vblRlbXBsYXRlXCIpIGNvbW1vblRlbXBsYXRlOiBFbGVtZW50UmVmO1xyXG4gICAgLy8gQElucHV0IHRvIHNldCBkeW5hbWljIGJ1dHRvbnMuXHJcbiAgICBASW5wdXQoJ2J1dHRvbnMnKSBidXR0b25zOiBzdHJpbmcgPSBcIlwiO1xyXG4gICAgcHVibGljIGFjdGlvblBhdGhSZXBvcnQ6IHN0cmluZyA9IFwicmVwb3J0IWV4cG9ydERhdGEuZG8/XCI7XHJcbiAgICBwdWJsaWMgYWRkUGFyYW1SZXBvcnQ6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBwdWJsaWMgc3d0S1ZQYXJhbXM6IHN0cmluZyA9IG51bGw7XHJcbiAgICBwdWJsaWMgYmFzZVVSTDogc3RyaW5nID0gXCJcIjtcclxuICAgIHB1YmxpYyBhY3Rpb25NZXRob2Q6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBwdWJsaWMgYWN0aW9uUGF0aDogc3RyaW5nID0gXCJcIjtcclxuICAgIHB1YmxpYyByZXF1ZXN0UGFyYW1zID0gbmV3IEFycmF5KCk7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSB1cmwgcGF0aCBmb3IgcmVwb3J0IHNldHRpbmdcclxuICAgIHB1YmxpYyB1cmxSZXBvcnRTZXR0aW5nOiBzdHJpbmcgPSBudWxsO1xyXG4gICAgLy8gaWFibGUgdG8gaWRlbnRpZnkgYSBncmlkIE9iamVjdFxyXG4gICAgcHVibGljIGdyaWRSZXBvcnQ6IE9iamVjdCA9IG51bGw7XHJcbiAgICAvL1RvIHN0b3JlIG1lbnUgYWNjZXNzXHJcbiAgICBwdWJsaWMgbWVudUFjY2Vzczogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vc2V0IHByb2dyYW1cclxuICAgIHB1YmxpYyBwcm9ncmFtSWQ6IHN0cmluZyA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSBtb2R1bGUgd2luZG93IHN0YXR1c1xyXG4gICAgcHVibGljIHVuZG9ja1dpbmRvdzogc3RyaW5nID0gbnVsbDtcclxuICAgIC8qIGlhYmxlIHRvIGhvbGQgaW5pdCBYIHZhbHVlIHRvIGxvYWQgY2hpbGQgc2NyZWVuICovXHJcbiAgICBwdWJsaWMgaW5pdFhWYWx1ZTogbnVtYmVyID0gMzAwO1xyXG4gICAgLyogaWFibGUgdG8gaG9sZCBpbml0IFkgdmFsdWUgdG8gbG9hZCBjaGlsZCBzY3JlZW4gKi9cclxuICAgIHB1YmxpYyBpbml0WVZhbHVlOiBudW1iZXI7XHJcbiAgICAvL2hvbGQgY29tcG9uZW50SWRcclxuICAgIHB1YmxpYyBjb21wb25lbnRJZDogc3RyaW5nID0gbnVsbDtcclxuICAgIHB1YmxpYyByZXBvcnRHcm91cElkOiBzdHJpbmcgPSBudWxsO1xyXG4gICAgLy9WQm94IHRvIHZpZXcgcmVwb3J0IHNldHRpbmdcclxuICAgIHB1YmxpYyBuZXdWQm94OiBWQm94ID0gbnVsbDtcclxuICAgIC8vIFN3dEFsZXJ0IHRvIHNob3cgZXJyb3JzXHJcbiAgICBwdWJsaWMgU3d0QWxlcnQ6IFN3dEFsZXJ0ID0gbnVsbDtcclxuICAgIHB1YmxpYyBqc29uUmVhZGVyOiBKU09OUmVhZGVyID0gbmV3IEpTT05SZWFkZXIoKTtcclxuICAgIHB1YmxpYyBsYXN0UmVjaWV2ZWRKU09OOiBhbnk7XHJcbiAgICBwdWJsaWMgcHJldlJlY2lldmVkSlNPTjogYW55O1xyXG4gICAgLy9CdXR0b25zXHJcbiAgICBwdWJsaWMgYWRkQnV0dG9uOiBTd3RCdXR0b24gPSBudWxsO1xyXG4gICAgcHVibGljIGNoYW5nZUJ1dHRvbjogU3d0QnV0dG9uID0gbnVsbDtcclxuICAgIHB1YmxpYyB2aWV3QnV0dG9uOiBTd3RCdXR0b24gPSBudWxsO1xyXG4gICAgcHVibGljIGR1cGxpY2F0ZUJ1dHRvbjogU3d0QnV0dG9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBkZWxldGVCdXR0b246IFN3dEJ1dHRvbiA9IG51bGw7XHJcbiAgICBwdWJsaWMgc2VhcmNoQnV0dG9uOiBTd3RCdXR0b24gPSBudWxsO1xyXG4gICAgcHVibGljIGNhbmNlbEJ1dHRvbjogU3d0QnV0dG9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBzYXZlQnV0dG9uOiBTd3RCdXR0b24gPSBudWxsO1xyXG4gICAgcHVibGljIHByaW50SWNvbjogU3d0QnV0dG9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBjbG9zZUJ1dHRvbjogU3d0QnV0dG9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBkZWxldGVNZXRob2Q6IEZ1bmN0aW9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBkdXBsaWNhdGVNZXRob2Q6IEZ1bmN0aW9uID0gbnVsbDtcclxuICAgIHB1YmxpYyBwb3B1cENsb3NlZEV2ZW50OiBGdW5jdGlvbiA9IG51bGw7XHJcbiAgICBwdWJsaWMga3ZQYXJhbXNNZXRob2Q6IEZ1bmN0aW9uID0gbnVsbDtcclxuICAgIC8vIHRoaXMgdmFyaWFibGUgc3RvcmUgYW4gaW5zdGFuY2Ugb2YgU3d0TW9kdWxlLlxyXG4gICAgcHVibGljIHN1cGVyOiBTd3RDb21tb25Nb2R1bGU7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSB1cmwgcGF0aFxyXG4gICAgcHVibGljIHVybERldGFpbHNQYXRoQWRkOiBzdHJpbmcgPSBudWxsO1xyXG4gICAgLy9UbyBpZGVudGlmeSB0aGUgdXJsIHBhdGhcclxuICAgIHB1YmxpYyB1cmxEZXRhaWxzUGF0aENoYW5nZTogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIHVybCBwYXRoXHJcbiAgICBwdWJsaWMgdXJsRGV0YWlsc1BhdGhWaWV3OiBzdHJpbmcgPSBudWxsO1xyXG4gICAgLy9UbyBpZGVudGlmeSB0aGUgdXJsIHBhdGhcclxuICAgIHB1YmxpYyB1cmxEZXRhaWxzUGF0aERlbGV0ZTogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIHVybCBwYXRoXHJcbiAgICBwdWJsaWMgdXJsRGV0YWlsc1BhdGhEdXBsaWNhdGU6IHN0cmluZyA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSB1cmwgcGF0aFxyXG4gICAgcHVibGljIHVybERldGFpbHNQYXRoU2F2ZTogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIHRpdGxlIG9mIGNoaWxkIHdpbmRvd1xyXG4gICAgcHVibGljIGNoaWxkUGFuZWxUaXRsZUFkZDogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIHRpdGxlIG9mIGNoaWxkIHdpbmRvd1xyXG4gICAgcHVibGljIGNoaWxkUGFuZWxUaXRsZUNoYW5nZTogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIHRpdGxlIG9mIGNoaWxkIHdpbmRvd1xyXG4gICAgcHVibGljIGNoaWxkUGFuZWxUaXRsZVZpZXc6IHN0cmluZyA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSB0aXRsZSBvZiBjaGlsZCB3aW5kb3dcclxuICAgIHB1YmxpYyBjaGlsZFBhbmVsVGl0bGVEZWxldGU6IHN0cmluZyA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSB0aXRsZSBvZiBjaGlsZCB3aW5kb3dcclxuICAgIHB1YmxpYyBjaGlsZFBhbmVsVGl0bGVEdXBsaWNhdGU6IHN0cmluZyA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSBjb21wb25lbnQgQWRkLlxyXG4gICAgcHVibGljIGFkZENvbXBvbmVudDogYW55ID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIGNvbXBvbmVudCBDaGFuZ2UuXHJcbiAgICBwdWJsaWMgY2hhbmdlQ29tcG9uZW50OiBhbnkgPSBudWxsO1xyXG4gICAgLy9UbyBpZGVudGlmeSB0aGUgY29tcG9uZW50IFZpZXcuXHJcbiAgICBwdWJsaWMgdmlld0NvbXBvbmVudDogYW55ID0gbnVsbDtcclxuICAgIC8vVG8gaWRlbnRpZnkgdGhlIGNvbXBvbmVudCBEZWxldGUuXHJcbiAgICBwdWJsaWMgZGVsZXRlQ29tcG9uZW50OiBhbnkgPSBudWxsO1xyXG4gICAgLy9UbyBpZGVudGlmeSB0aGUgY29tcG9uZW50IER1cGxpY2F0ZS5cclxuICAgIHB1YmxpYyBkdXBsaWNhdGVDb21wb25lbnQ6IGFueSA9IG51bGw7XHJcbiAgICAvL1RvIGlkZW50aWZ5IHRoZSBjb21wb25lbnQgUmVwb3J0U2V0dGluZy5cclxuICAgIHB1YmxpYyByZXBvcnRTZXR0aW5nQ29tcG9uZW50OiBhbnkgPSBudWxsO1xyXG4gICAgLy9UbyBpZGVudGlmeSB0aGUgdGl0bGUgb2YgY2hpbGQgd2luZG93XHJcbiAgICBwdWJsaWMgc2F2ZUNvbXBvbmVudDogYW55ID0gbnVsbDtcclxuICAgIHB1YmxpYyB2Qm94Q3VzdG9tR3JpZFBhZGRpbmdUb3A6IG51bWJlciA9IDU7XHJcbiAgICBwdWJsaWMgbG9ja2VkQ29sdW1uQ291bnQ6IG51bWJlciA9IDA7XHJcbiAgICBwdWJsaWMgdW5pcXVlQ29sdW1uOiBzdHJpbmcgPSBcIlwiO1xyXG4gICAgcHVibGljIHN3dENvbW1vbkdyaWQ6IFN3dENvbW1vbkdyaWQ7XHJcbiAgICBwdWJsaWMgZXZlbnRTdHJpbmc6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBwdWJsaWMgc2NyZWVuTmFtZTogc3RyaW5nID0gXCJcIjtcclxuICAgIHB1YmxpYyB2ZXJzaW9uTnVtYmVyOiBzdHJpbmcgPSBcIlwiO1xyXG4gICAgcHVibGljIGF2YWlsYWJsZVNjcmVlbjogc3RyaW5nID0gbnVsbDtcclxuICAgIHB1YmxpYyBvYmplY3RUeXBlOiBzdHJpbmcgPSBudWxsO1xyXG4gICAgcHVibGljIHJlY29yZElkOiBzdHJpbmcgPSBudWxsO1xyXG4gICAgcHVibGljIHBhcnR5VHlwZTogc3RyaW5nID0gbnVsbDtcclxuICAgIHB1YmxpYyBjbG9zZVVSTDogc3RyaW5nID0gbnVsbDtcclxuICAgIC8vIEluaXRpYWxpemUgTW9kdWxlTG9hZGVyIHRvIGxvYWQgY2hpbGQgd2luZG93c1xyXG4gICAgcHJvdGVjdGVkIG1Mb2FkZXI6IE1vZHVsZUxvYWRlciA9IG51bGw7XHJcbiAgICAvL1BhbmVsIHRvIHZpZXcgY2hpbGQgd2luZG93XHJcbiAgICBwcm90ZWN0ZWQgY2hpbGRQYW5lbDogVGl0bGVXaW5kb3cgPSBudWxsO1xyXG4gICAgcHJvdGVjdGVkIGxvZ2dlcjogTG9nZ2VyO1xyXG4gICAgcHJpdmF0ZSBfaW5wdXREYXRhOiBIVFRQQ29tbXMgPSBuZXcgSFRUUENvbW1zKHRoaXMuX2NvbW1vblNlcnZpY2UpO1xyXG4gICAgcHJpdmF0ZSBzaG93SlNPTjogYW55O1xyXG4gICAgcHJpdmF0ZSBwb3BVcFNjcmVlbjogc3RyaW5nID0gbnVsbDtcclxuICAgIHB1YmxpYyBjb250ZXh0OiBhbnk7XHJcblxyXG4gICAgY29uc3RydWN0b3IocHJpdmF0ZSBfZWxlbWVudDogRWxlbWVudFJlZiwgcHJpdmF0ZSBfY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSkge1xyXG4gICAgICAgIHN1cGVyKF9lbGVtZW50LCBfY29tbW9uU2VydmljZSk7XHJcbiAgICAgICAgdGhpcy5Td3RBbGVydCA9IG5ldyBTd3RBbGVydChfY29tbW9uU2VydmljZSk7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIgPSBuZXcgTG9nZ2VyKFwiU3d0Q29tbW9uTW9kdWxlXCIsIF9jb21tb25TZXJ2aWNlLmh0dHBjbGllbnQpO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogVGhpcyBtZXRob2QgZmlyZWQgb24gY29tbW9uTW9kdWxlIGxvYWQuXHJcbiAgICAgKi9cclxuICAgIG9uQ29tbW9uTW9kdWxlSW5pdChldmVudCkge1xyXG4gICAgICAgIHRoaXMuX2NyZWF0aW9uQ29tcGxldGUuZW1pdCh0aGlzKTtcclxuICAgIH1cclxuXHJcbiAgICBvbkNvbW1vbk1vZHVsZUJlZm9yZUluaXQoZXZlbnQpIHtcclxuICAgICAgICBpZiAodGhpcy5idXR0b25zKSB7XHJcbiAgICAgICAgICAgICQodGhpcy5jb21tb25UZW1wbGF0ZS5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgICAgICAgICAgICQodGhpcy5idXR0b25zVGVtcGxhdGUubmF0aXZlRWxlbWVudCkuc2hvdygpO1xyXG4gICAgICAgICAgICB0aGlzLnNldER5bmFtaWNCdXR0b25zKCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgJCh0aGlzLmNvbW1vblRlbXBsYXRlLm5hdGl2ZUVsZW1lbnQpLnNob3coKTtcclxuICAgICAgICAgICAgJCh0aGlzLmJ1dHRvbnNUZW1wbGF0ZS5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuX3ByZWluaXRpYWxpemUuZW1pdCh0aGlzKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgaXMgYSByZXBvcnQgaWNvbiBhY3Rpb24gaGFuZGxlciBtZXRob2RcclxuICAgICAqIEBwYXJhbSB0eXBlXHJcbiAgICAgKiBAcGFyYW0gZGlzcGxheUZpbHRlclxyXG4gICAgICogQHBhcmFtIHhtbERhdGFTb3VyY2VcclxuICAgICAqL1xyXG4gICAgcmVwb3J0KHR5cGU6IHN0cmluZywgZGlzcGxheUZpbHRlcjogSGFzaE1hcCA9IG51bGwsIHhtbERhdGFTb3VyY2U6IFhNTCA9IG51bGwpIHtcclxuICAgICAgICAvL1ZhcmlhYmxlIGZvciBlcnJvckxvY2F0aW9uXHJcbiAgICAgICAgdmFyIGVycm9yTG9jYXRpb246IG51bWJlciA9IDA7XHJcbiAgICAgICAgdmFyIG1vZHVsZUlkOiBTdHJpbmcgPSBudWxsO1xyXG4gICAgICAgIHZhciBhY3Rpb25QYXJhbXM6IFN0cmluZyA9IFwiXCI7XHJcbiAgICAgICAgdmFyIGRpc3BsYXlGaWx0ZXJBc0pTT046IGFueTtcclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKFwiWyByZXBvcnQgXSBtZXRob2QgU1RBUlRcIik7XHJcbiAgICAgICAgdHJ5IHtcclxuXHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxMDtcclxuICAgICAgICAgICAgY29uc3QgcGFyZW50S1ZQYXJhbXMgPSB0aGlzLmdldEtWUGFyYW1zKCk7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAyMDtcclxuICAgICAgICAgICAgZGlzcGxheUZpbHRlckFzSlNPTiA9IFN0cmluZ1V0aWxzLmdldEtWVHlwZVRhYkFzWE1MKGRpc3BsYXlGaWx0ZXIsICcnLCAnJywgJycpO1xyXG4gICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMzA7XHJcbiAgICAgICAgICAgIC8vR2V0IHRoZSBjdXJyZW50IE1vZHVsZSBJZC5cclxuICAgICAgICAgICAgbW9kdWxlSWQgPSBwYXJlbnRBcHBsaWNhdGlvbi5nZXRDdXJyZW50TW9kdWxlSWQoKTtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDQwO1xyXG4gICAgICAgICAgICAvL3NldCB0aGUgYWN0aW9uIHBhdGhcclxuICAgICAgICAgICAgdGhpcy5hY3Rpb25QYXRoID0gdGhpcy5hY3Rpb25QYXRoUmVwb3J0O1xyXG4gICAgICAgICAgICBhY3Rpb25QYXJhbXMgPSBcInR5cGU9XCIgKyB0eXBlO1xyXG4gICAgICAgICAgICBhY3Rpb25QYXJhbXMgPSBhY3Rpb25QYXJhbXMgKyBcIiZhY3Rpb249RVhQT1JUJnByaW50PUFMTCZjdXJyZW50TW9kdWxlSWQ9XCIgKyBtb2R1bGVJZCArIFwiJnJlc3BvbnNlPWpzb25cIjtcclxuICAgICAgICAgICAgYWN0aW9uUGFyYW1zID0gYWN0aW9uUGFyYW1zICsgXCImcHJvZ3JhbUlkPVwiICsgdGhpcy5wcm9ncmFtSWQgKyBcIiZyZXBvcnRHcm91cElkPVwiICsgdGhpcy5yZXBvcnRHcm91cElkXHJcbiAgICAgICAgICAgICAgICArIFwiJmt2UGFyYW1zPVwiICsgU3RyaW5nVXRpbHMuZW5jb2RlNjQocGFyZW50S1ZQYXJhbXMpLnJlcGxhY2UoL1xcKy9naSwgXCIpXCIpLnJlcGxhY2UoL1xcPS9naSwgXCIoXCIpXHJcbiAgICAgICAgICAgICAgICArIFwiJmRpc3BsYXlGaWx0ZXI9XCIgKyBTdHJpbmdVdGlscy5lbmNvZGU2NChkaXNwbGF5RmlsdGVyQXNKU09OLnRvWE1MU3RyaW5nKCkpLnJlcGxhY2UoL1xcKy9naSwgXCIpXCIpLnJlcGxhY2UoL1xcPS9naSwgXCIoXCIpO1xyXG4gICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNTA7XHJcbiAgICAgICAgICAgIC8vQ2FsbHMgYSBtZXRob2QgZnJvbSBtYWluZmxleC5qc3AgZmlsZVxyXG4gICAgICAgICAgICBFeHRlcm5hbEludGVyZmFjZS5jYWxsKFwiZ2V0UmVwb3J0c0FuZFByb2dyZXNzXCIsIHRoaXMuYWN0aW9uUGF0aCArIGFjdGlvblBhcmFtcywgeG1sRGF0YVNvdXJjZSAhPSBudWxsID8gU3RyaW5nVXRpbHMuZW5jb2RlNjQoeG1sRGF0YVNvdXJjZS50b1hNTFN0cmluZygpKSA6IFwiXCIpO1xyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICB0aGlzLmxvZ2dlci5lcnJvcihcIlsgcmVwb3J0IF0gbWV0aG9kIC0gZXJyb3IgXCIsIGVycm9yKTtcclxuICAgICAgICAgICAgdGhpcy5Td3RBbGVydC5lcnJvcihlcnJvci5tZXNzYWdlLCAnUHJvZ3JhbW1pbmcgRXJyb3InKTtcclxuICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCB0aGlzLmdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSwgXCJyZXBvcnRcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbIHJlcG9ydCBdIG1ldGhvZCBFTkRcIik7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBrZXlEb3duSGFuZGxlckV2ZW50XHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIGV2ZW50OiAgS2V5Ym9hcmRFdmVudFxyXG4gICAgICpcclxuICAgICAqIFRoaXMgaXMgYSBrZXkgZXZlbnQgbGlzdGVuZXIsIHVzZWQgdG8gcGVyZm9ybSB0aGUgb3BlcmF0aW9uXHJcbiAgICAgKiB3aGVuIGhpdCB0aGUgZW50ZXIga2V5IGJhc2VkIG9uIHRoZSBjdXJyZW50bHkgZm9jdXNlZCBwcm9wZXJ0eShCdXR0b24pXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBrZXlEb3duSGFuZGxlckV2ZW50KGV2ZW50KTogdm9pZCB7XHJcbiAgICAgICAgLy8gVmFyaWFibGUgTnVtYmVyIEVycm9yIExvY2F0aW9uXHJcbiAgICAgICAgdmFyIGVycm9yTG9jYXRpb246IG51bWJlciA9IDA7XHJcblxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAwO1xyXG4gICAgICAgICAgICAvL0N1cnJlbnRseSBmb2N1c3NlZCBwcm9wZXJ0eSBuYW1lXHJcbiAgICAgICAgICAgIGNvbnN0IGV2ZW50U3RyaW5nID0gZm9jdXNNYW5hZ2VyLmdldEZvY3VzKCkubmFtZTtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDEwO1xyXG4gICAgICAgICAgICBpZiAoKGV2ZW50LmtleUNvZGUgPT09IEtleWJvYXJkLkVOVEVSKSkge1xyXG4gICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDIwO1xyXG4gICAgICAgICAgICAgICAgaWYgKGV2ZW50U3RyaW5nID09PSBcImNsb3NlQnV0dG9uXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMzA7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbG9zZVNjcmVlbkhhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnRTdHJpbmcgPT09IFwiY3N2XCIpIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNDA7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXBvcnQoJ2NzdicpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnRTdHJpbmcgPT09IFwiZXhjZWxcIikge1xyXG4gICAgICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSA1MDtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlcG9ydCgneGxzJyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJwZGZcIikge1xyXG4gICAgICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSA2MDtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlcG9ydCgncGRmJyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gJ3NldHRpbmdCdXR0b24nKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDcwO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0U2V0dGluZygpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnRTdHJpbmcgPT09IFwiYWRkQnV0dG9uXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMzA7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnRTdHJpbmcgPT09IFwiY2hhbmdlQnV0dG9uXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNDA7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnRTdHJpbmcgPT09IFwidmlld0J1dHRvblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDUwO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGV2ZW50U3RyaW5nID09PSBcImR1cGxpY2F0ZUJ1dHRvblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDYwO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGV2ZW50U3RyaW5nID09PSBcInByaW50SWNvblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDcwO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBFUlJPUiBMT0dcclxuICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCB0aGlzLmdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSwgXCJrZXlEb3duSGFuZGxlckV2ZW50XCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIGdldFNldHRpbmdcclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0gbm9uZVxyXG4gICAgICpcclxuICAgICAqIFRoaXMgbWV0aG9kIHVzZWQgdG8gY3JlYXRlIHRoZSBjaGlsZCByZXBvcnQgc2V0dGluZyBzY3JlZW5cclxuICAgICAqL1xyXG4gICAgcHVibGljIGdldFNldHRpbmcoKTogdm9pZCB7XHJcbiAgICAgICAgLy9WYXJpYWJsZSBmb3IgZXJyb3JMb2NhdGlvblxyXG4gICAgICAgIHZhciBlcnJvckxvY2F0aW9uOiBudW1iZXIgPSAwO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIHRoaXMuc3d0S1ZQYXJhbXMgPSB0aGlzLmdldEtWUGFyYW1zKCk7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxMDtcclxuICAgICAgICAgICAgLy8gR2V0IGNvbXBvbmVudCAgSWQgZnJvbSB4bWxcclxuICAgICAgICAgICAgdGhpcy5jb21wb25lbnRJZCA9IHRoaXMubGFzdFJlY2lldmVkSlNPTi5zY3JlZW5pZDtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDIwO1xyXG4gICAgICAgICAgICAvL0FkZCBsaXN0ZW5lciB0byBtb2R1bGVSZWFkeUV2ZW50SGFuZGxlclxyXG4gICAgICAgICAgICB0aGlzLm1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyUmVwb3J0U2V0dGluZyhldmVudCk7XHJcbiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcygncmVwb3J0LndpbmRvd3RpdGxlLnRpdGxlJyk7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAzMDtcclxuICAgICAgICAgICAgLy8gc2V0IGRldGFpbHMgc2NyZWVuIHRpdGxlLlxyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5nZXREYXRhRnJvbVVybCh0aGlzLnVybFJlcG9ydFNldHRpbmcpO1xyXG4gICAgICAgICAgICAvLyBMb2FkIHZpZXcgc2NyZWVuXHJcbiAgICAgICAgICAgIC8vIHRoaXMubG9hZE1vZHVsZSh0aXRsZSwgdGhpcy5yZXBvcnRTZXR0aW5nQ29tcG9uZW50LCB0aGlzLnVybFJlcG9ydFNldHRpbmcsIGRhdGEpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgdGhpcy5nZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcyksIFwiZ2V0U2V0dGluZ1wiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIHNob3cgaGVscFxyXG4gICAgICogcGFnZS5cclxuICAgICAqL1xyXG4gICAgc2hvd2hlbHAoKSB7XHJcblxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICoga3ZSZXBvcnRQYXJhbXMgZnVuY3Rpb24gdGhhdCBzaG91bGQgYmUgb3ZlcnJpZGVuIGJ5IGNoaWxkIGNsYXNzZXNcclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGt2UmVwb3J0UGFyYW1zKCkgLypPZiBrZXkvdmFsdWUgdG8gYmUgdXNlZCBhcyBwYXJhbWV0ZXJzKi8ge1xyXG4gICAgICAgIGlmICh0aGlzLmt2UGFyYW1zTWV0aG9kKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmt2UGFyYW1zTWV0aG9kKCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUHJvZ3JhbW1pbmcgRXJyb3I6IGZ1bmN0aW9uIGt2UmVwb3J0UGFyYW1zKCkgbXVzdCBiZSBvdmVycmlkZW4gaW4gY2hpbGQgY2xhc3NlcyBpbXBsZW1lbnRpbmcgU3d0Q29tbW9uTW9kdWxlICEhXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIENvbnN0cnVjdHMgdGhlIHJlcG9ydCBwYXJhbWV0ZXJzIGFzIHhtbCBzdHJpbmdcclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGdldEtWUGFyYW1zKCkge1xyXG4gICAgICAgIGNvbnN0IHBhcmFtc0xpc3QgPSB0aGlzLmt2UmVwb3J0UGFyYW1zKCk7XHJcbiAgICAgICAgY29uc3Qga3ZBc1hNTDogWE1MID0gU3RyaW5nVXRpbHMuZ2V0S1ZUeXBlVGFiQXNYTUwocGFyYW1zTGlzdCwgJycsICcnLCAnJyk7XHJcbiAgICAgICAgcmV0dXJuIGt2QXNYTUwudG9YTUxTdHJpbmcoKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWRcclxuICAgICAqIEBwYXJhbSBidXR0b25cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGNsaWNrSGFuZGxlcihldmVudCkge1xyXG4gICAgICAgIC8vVmFyaWFibGUgZm9yIGVycm9yTG9jYXRpb25cclxuICAgICAgICBjb25zdCBlcnJvckxvY2F0aW9uOiBudW1iZXIgPSAwO1xyXG4gICAgICAgIC8vIFN0cmluZyB0byBob2xkIHRoZSBjdXJyZW50IHRhcmdldCBvZiB0aGUgZXZlbnRcclxuICAgICAgICB2YXIgZXZlbnRTdHJpbmc6IHN0cmluZyA9IG51bGw7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbyhcIlsgY2xpY2tIYW5kbGVyIF0gbWV0aG9kIFNUQVJUXCIpO1xyXG4gICAgICAgIHRyeSB7XHJcblxyXG4gICAgICAgICAgICAvL0N1cnJlbnRseSBmb2N1c3NlZCBwcm9wZXJ0eSBuYW1lXHJcbiAgICAgICAgICAgIGV2ZW50U3RyaW5nID0gZm9jdXNNYW5hZ2VyLmdldEZvY3VzKCkubmFtZTtcclxuICAgICAgICAgICAgaWYgKGV2ZW50U3RyaW5nID09PSBcImFkZEJ1dHRvblwiICYmIHRoaXMudXJsRGV0YWlsc1BhdGhBZGQgIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgLy8gTG9hZCB2aWV3IHNjcmVlblxyXG4gICAgICAgICAgICAgICAgdGhpcy5sb2FkTW9kdWxlKHRoaXMuY2hpbGRQYW5lbFRpdGxlQWRkLCB0aGlzLnVybERldGFpbHNQYXRoQWRkKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJjaGFuZ2VCdXR0b25cIiAmJiB0aGlzLnVybERldGFpbHNQYXRoQ2hhbmdlICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgIC8vIExvYWQgY2hhbmdlIHNjcmVlblxyXG4gICAgICAgICAgICAgICAgdGhpcy5sb2FkTW9kdWxlKHRoaXMuY2hpbGRQYW5lbFRpdGxlQ2hhbmdlLCB0aGlzLnVybERldGFpbHNQYXRoQ2hhbmdlKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJ2aWV3QnV0dG9uXCIgJiYgdGhpcy51cmxEZXRhaWxzUGF0aFZpZXcgIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgLy8gTG9hZCB2aWV3IHNjcmVlblxyXG4gICAgICAgICAgICAgICAgdGhpcy5sb2FkTW9kdWxlKHRoaXMuY2hpbGRQYW5lbFRpdGxlVmlldywgdGhpcy51cmxEZXRhaWxzUGF0aFZpZXcpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChldmVudFN0cmluZyA9PT0gXCJkZWxldGVCdXR0b25cIikge1xyXG4gICAgICAgICAgICAgICAgLy8gTG9hZCB2aWV3IHNjcmVlblxyXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGVBY3Rpb24oKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJkdXBsaWNhdGVCdXR0b25cIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVBY3Rpb24oKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJwcmludEljb25cIikge1xyXG4gICAgICAgICAgICAgICAgLy9kdXBsaWNhdGVBY3Rpb24oKTsgVE9ET1xyXG5cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChldmVudFN0cmluZyA9PT0gXCJjbG9zZUJ1dHRvblwiKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCByZXMgPSB0aGlzLl9jb21tb25TZXJ2aWNlLlJvdXRlci51cmwuc3BsaXQoXCI7XCIpO1xyXG4gICAgICAgICAgICAgICAgU3d0VGFiTmF2aWdhdG9ySGFuZGxlci5jbG9zZVRhYihyZXNbMF0uc3Vic3RyKDEsIHJlc1swXS5sZW5ndGgpKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2xvc2VTY3JlZW5IYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICB0aGlzLmxvZ2dlci5lcnJvcihcIlsgY2xpY2tIYW5kbGVyIF0gbWV0aG9kIC0gZXJyb3IgXCIsIGVycm9yKTtcclxuICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCB0aGlzLmdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSwgXCJjbGlja0hhbmRsZXJcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbIGNsaWNrSGFuZGxlciBdIG1ldGhvZCBFTkRcIik7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBsb2FkTW9kdWxlXHJcbiAgICAgKlxyXG4gICAgICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBvcGVuIGNoaWxkIG1vZHVsZVxyXG4gICAgICovXHJcbiAgICBwcm90ZWN0ZWQgbG9hZE1vZHVsZSh0aXRsZTogc3RyaW5nLCB1cmw6IHN0cmluZyk6IHZvaWQge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbIGxvYWRNb2R1bGUgXSBtZXRob2QgU1RBUlRcIik7XHJcbiAgICAgICAgbGV0IGVycm9yTG9jYXRpb246IG51bWJlciA9IDA7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKHRoaXMubUxvYWRlciA9PT0gbnVsbCkge1xyXG5cclxuICAgICAgICAgICAgICAgIHRoaXMuY2hpbGRQYW5lbCA9IFN3dFBvcFVwTWFuYWdlci5jcmVhdGVQb3BVcCh0aGlzLmNvbnRleHQpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGlsZFBhbmVsLnRpdGxlID0gdGl0bGU7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNoaWxkUGFuZWwuaXNNb2RhbCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNoaWxkUGFuZWwuc2hvd0NvbnRyb2xzID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2hpbGRQYW5lbC5lbmFibGVSZXNpemUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5tTG9hZGVyID0gbmV3IE1vZHVsZUxvYWRlcih0aGlzLl9jb21tb25TZXJ2aWNlKTtcclxuICAgICAgICAgICAgICAgIHRoaXMubUxvYWRlci5wZXJjZW50SGVpZ2h0ID0gNTA7XHJcbiAgICAgICAgICAgICAgICB0aGlzLm1Mb2FkZXIucGVyY2VudFdpZHRoID0gNTA7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNDA7XHJcbiAgICAgICAgICAgICAgICAvKiBMb2FkIEFkZCBzY3JlZW4gbW9kdWxlICovXHJcbiAgICAgICAgICAgICAgICB0aGlzLm1Mb2FkZXIuYWRkRXZlbnRMaXN0ZW5lcihNb2R1bGVFdmVudC5SRUFEWSwgKGV2ZW50KSA9PiB0aGlzLm1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyKGV2ZW50KSk7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNTA7XHJcbiAgICAgICAgICAgICAgICB0aGlzLm1Mb2FkZXIubG9hZE1vZHVsZSh1cmwpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgU3d0UG9wVXBNYW5hZ2VyLmJyaW5nVG9Gcm9udChudWxsKTsgLy8gVE9ETyBhZGQgcG9wdXAgaW5zdGFuY2UgdG8gYmUgc2V0IGluIHRoZSB0b3AuXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoXCJbIGxvYWRNb2R1bGUgXSBtZXRob2QgLSBlcnJvciBcIiwgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKFwiWyBsb2FkTW9kdWxlIF0gbWV0aG9kIEVORFwiKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIG1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIGV2ZW50OiBNb2R1bGVFdmVudFxyXG4gICAgICpcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gbG9hZCB2aWV3IHVzZXIgc2NyZWVuIGFzIGEgcG9wdXAsIG9uY2UgaXQgaXMgcmVhZHkgdG8gbG9hZC5cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIG1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyKGV2ZW50KTogdm9pZCB7XHJcblxyXG4gICAgICAgIC8vVmFyaWFibGUgZm9yIGVycm9yTG9jYXRpb25cclxuICAgICAgICB2YXIgZXJyb3JMb2NhdGlvbjogbnVtYmVyID0gMDtcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDEwO1xyXG4gICAgICAgICAgICB0aGlzLmNoaWxkUGFuZWwuYWRkQ2hpbGQoZXZlbnQudGFyZ2V0KTtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDIwO1xyXG4gICAgICAgICAgICB0aGlzLmNoaWxkUGFuZWwub25DbG9zZS5zdWJzY3JpYmUoKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnBvcHVwQ2xvc2VkRXZlbnRIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHRoaXMuY2hpbGRQYW5lbC5kaXNwbGF5KCk7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAzMDtcclxuICAgICAgICAgICAgLyogRGlzYWJsZSBidXR0b25zIHdoaWxlIGNoaWxkIHNjcmVlbiBpcyBsb2FkZWQgKi9cclxuICAgICAgICAgICAgdGhpcy5faGVscEljb24uZW5hYmxlZCA9IGZhbHNlO1xyXG4gICAgICAgICAgICB0aGlzLl9oZWxwSWNvbi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHRoaXMuX3BkZi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHRoaXMuX3BkZi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHRoaXMuX2Nzdi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHRoaXMuX2Nzdi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHRoaXMuX2V4Y2VsLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgdGhpcy5fZXhjZWwuYnV0dG9uTW9kZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICB0aGlzLl9zZXR0aW5nQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgdGhpcy5fc2V0dGluZ0J1dHRvbi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSA0MDtcclxuICAgICAgICAgICAgLy8gY2hlY2sgaWYgYWRkQnV0dG9uIGlzIG5vdCBudWxsXHJcbiAgICAgICAgICAgIGlmICh0aGlzLmFkZEJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLy8gY2hlY2sgaWYgY2hhbmdlQnV0dG9uIGlzIG5vdCBudWxsXHJcbiAgICAgICAgICAgIGlmICh0aGlzLmNoYW5nZUJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBkaXNhYmxlIGNoYW5nZSBidXR0b25cclxuICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvLyBjaGVjayBpZiB2aWV3QnV0dG9uIGJ1dHRvbiBpcyBub3QgbnVsbFxyXG4gICAgICAgICAgICBpZiAodGhpcy52aWV3QnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgIC8vIGRpc2FibGUgdmlldyBidXR0b25cclxuICAgICAgICAgICAgICAgIHRoaXMudmlld0J1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24uYnV0dG9uTW9kZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIGNoZWNrIGlmIGRlbGV0ZUJ1dHRvbiBidXR0b24gaXMgbm90IG51bGxcclxuICAgICAgICAgICAgaWYgKHRoaXMuZGVsZXRlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgIC8vIGRpc2FibGUgZGVsZXRlIGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24uZW5hYmxlZCA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24uYnV0dG9uTW9kZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIGNoZWNrIGlmIGR1cGxpY2F0ZUJ1dHRvbiBpcyBub3QgbnVsbFxyXG4gICAgICAgICAgICBpZiAodGhpcy5kdXBsaWNhdGVCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgLy8gZGlzYWJsZSBkdXBsaWNhdGUgYnV0dG9uXHJcbiAgICAgICAgICAgICAgICB0aGlzLmR1cGxpY2F0ZUJ1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmR1cGxpY2F0ZUJ1dHRvbi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHRoaXMuc2VhcmNoQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgdGhpcy5nZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcyksIFwibW9kdWxlUmVhZHlFdmVudEhhbmRsZXJcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogcG9wdXBDbG9zZWRFdmVudEhhbmRsZXJcclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0gZXZlbnQgOkV2ZW50XHJcbiAgICAgKlxyXG4gICAgICogTWV0aG9kIGNhbGxlZCB3aGVuIHBvcCB1cCBpcyBjbG9zZWQgdG8gcmVmcmVzaCBHcmlkIGFuZCBFbmFibGUgQnV0dG9ucyBhZnRlciBwb3AgdXAgY2xvc2VkXHJcbiAgICAgKlxyXG4gICAgICovXHJcbiAgICBwcm90ZWN0ZWQgcG9wdXBDbG9zZWRFdmVudEhhbmRsZXIoZXZlbnQpOiB2b2lkIHtcclxuICAgICAgICAvL1ZhcmlhYmxlIGZvciBlcnJvckxvY2F0aW9uXHJcbiAgICAgICAgdmFyIGVycm9yTG9jYXRpb246IG51bWJlciA9IDA7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbyhcIlsgcG9wdXBDbG9zZWRFdmVudEhhbmRsZXIgXSBtZXRob2QgU1RBUlRcIik7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDEwO1xyXG4gICAgICAgICAgICB0aGlzLl9oZWxwSWNvbi5lbmFibGVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgdGhpcy5faGVscEljb24uYnV0dG9uTW9kZSA9IHRydWU7XHJcbiAgICAgICAgICAgIHRoaXMuX3BkZi5lbmFibGVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgdGhpcy5fcGRmLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICB0aGlzLl9jc3YuZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgIHRoaXMuX2Nzdi5idXR0b25Nb2RlID0gdHJ1ZTtcclxuICAgICAgICAgICAgdGhpcy5fZXhjZWwuZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgIHRoaXMuX2V4Y2VsLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICB0aGlzLl9zZXR0aW5nQnV0dG9uLmVuYWJsZWQgPSB0cnVlO1xyXG4gICAgICAgICAgICB0aGlzLl9zZXR0aW5nQnV0dG9uLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMjA7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLnNlYXJjaEJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNlYXJjaEJ1dHRvbi5lbmFibGVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAzMDtcclxuICAgICAgICAgICAgaWYgKHRoaXMuc3d0Q29tbW9uR3JpZCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuc3d0Q29tbW9uR3JpZC5zZWxlY3RlZEluZGV4ICE9PSAtMSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMudmlld0J1dHRvbi5lbmFibGVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24uYnV0dG9uTW9kZSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMubWVudUFjY2VzcyA9PT0gXCIwXCIpIHtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmNoYW5nZUJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5lbmFibGVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5kZWxldGVCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24uZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbi5idXR0b25Nb2RlID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZHVwbGljYXRlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmVuYWJsZWQgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24uYnV0dG9uTW9kZSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuY2hhbmdlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZGVsZXRlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZHVwbGljYXRlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDQwO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMudmlld0J1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy52aWV3QnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuY2hhbmdlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VCdXR0b24uZW5hYmxlZCA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5idXR0b25Nb2RlID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5kZWxldGVCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmR1cGxpY2F0ZUJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24uYnV0dG9uTW9kZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIGNoZWNrIGZvciBBZGQgYnV0dG9uXHJcbiAgICAgICAgICAgIGlmICh0aGlzLm1lbnVBY2Nlc3MgPT09ICcwJykge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5hZGRCdXR0b24uZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbi5idXR0b25Nb2RlID0gdHJ1ZTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5kdXBsaWNhdGVCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmVuYWJsZWQgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmJ1dHRvbk1vZGUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuYWRkQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuYWRkQnV0dG9uLmJ1dHRvbk1vZGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvLyBpbml0aWFsaXplIG1sb2FkZXIgYW5kIGNoaWxkUGFuZWwuXHJcbiAgICAgICAgICAgIHRoaXMubUxvYWRlciA9IG51bGw7XHJcbiAgICAgICAgICAgIHRoaXMuY2hpbGRQYW5lbCA9IG51bGw7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoXCJbIHBvcHVwQ2xvc2VkRXZlbnRIYW5kbGVyIF0gbWV0aG9kIC0gZXJyb3IgXCIsIGVycm9yKTtcclxuICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCB0aGlzLmdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSwgXCJwb3B1cENsb3NlZEV2ZW50SGFuZGxlclwiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbyhcIlsgcG9wdXBDbG9zZWRFdmVudEhhbmRsZXIgXSBtZXRob2QgRU5EXCIpO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogZGlzcG9zZVxyXG4gICAgICpcclxuICAgICAqIFRoaXMgaXMgYSBldmVudCBoYW5kbGVyLCB1c2VkIHRvIGNsb3NlIHRoZSBjdXJyZW50IHRhYi93aW5kb3dcclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGRpc3Bvc2UoKTogdm9pZCB7XHJcbiAgICAgICAgLy8gcmVtb3ZlIGV2ZW50IGxpc3RlbmVycyBvbiBidXR0b25zXHJcbiAgICAgICAgaWYgKHRoaXMuYWRkQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgdGhpcy5hZGRCdXR0b24uY2xpY2sgPSBudWxsO1xyXG4gICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbiA9IG51bGw7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh0aGlzLnZpZXdCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24uY2xpY2sgPSBudWxsO1xyXG4gICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24gPSBudWxsO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodGhpcy5jaGFuZ2VCdXR0b24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5jbGljayA9IG51bGw7XHJcbiAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uID0gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRoaXMuZGVsZXRlQnV0dG9uICE9IG51bGwpIHtcclxuICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24uY2xpY2sgPSBudWxsO1xyXG4gICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbiA9IG51bGw7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh0aGlzLmR1cGxpY2F0ZUJ1dHRvbiAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLmNsaWNrID0gbnVsbDtcclxuICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24gPSBudWxsO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodGhpcy5wcmludEljb24gIT0gbnVsbCkge1xyXG4gICAgICAgICAgICB0aGlzLnByaW50SWNvbi5jbGljayA9IG51bGw7XHJcbiAgICAgICAgICAgIHRoaXMucHJpbnRJY29uID0gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy9cclxuICAgICAgICAvLyB0aGlzLnJlbW92ZUV2ZW50TGlzdGVuZXIoRm9jdXNFdmVudC5LRVlfRk9DVVNfQ0hBTkdFLCBmb2N1c0NoYW5nZUhhbmRsZXIsIHRydWUpO1xyXG4gICAgICAgIC8vIHRoaXMucmVtb3ZlQWxsQ2hpbGRyZW4oKTtcclxuICAgICAgICAvLyB0aGlzLl9sb2FkaW5nSW1hZ2UudW5sb2FkQW5kU3RvcCh0cnVlKTtcclxuICAgICAgICB0aGlzLnJlcXVlc3RQYXJhbXMgPSBudWxsO1xyXG4gICAgICAgIHRoaXMuX2lucHV0RGF0YSA9IG51bGw7XHJcbiAgICAgICAgdGhpcy5qc29uUmVhZGVyID0gbnVsbDtcclxuICAgICAgICB0aGlzLmxhc3RSZWNpZXZlZEpTT04gPSBudWxsO1xyXG4gICAgICAgIHRoaXMucHJldlJlY2lldmVkSlNPTiA9IG51bGw7XHJcbiAgICAgICAgdGhpcy5iYXNlVVJMID0gbnVsbDtcclxuICAgICAgICB0aGlzLnByb2dyYW1JZCA9IG51bGw7XHJcbiAgICAgICAgdGhpcy5tTG9hZGVyID0gbnVsbDtcclxuICAgICAgICB0aGlzLmNoaWxkUGFuZWwgPSBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogY2xvc2VTY3JlZW5IYW5kbGVyXHJcbiAgICAgKiBAcGFyYW0gZXZlbnQ6IEV2ZW50XHJcbiAgICAgKiBGdW5jdGlvbiBjYWxsZWQgdG8gY2xvc2UgZmllbGRzIHRvIGNoZWNrIG1haW50ZW5hbmNlIHNjcmVlbi5cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGNsb3NlU2NyZWVuSGFuZGxlcihldmVudCk6IHZvaWQge1xyXG4gICAgICAgIHZhciBlcnJvckxvY2F0aW9uOiBudW1iZXIgPSAwO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxMDtcclxuICAgICAgICAgICAgdGhpcy5kaXNwb3NlKCk7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAyMDtcclxuICAgICAgICAgICAgLyogSWYgc2NyZWVuIGlzIHVuZG9ja2VkIGNhbGwgamF2YXNjcmlwdCBjbG9zZSAqL1xyXG4gICAgICAgICAgICBpZiAodGhpcy51bmRvY2tXaW5kb3cgPT09IFwidHJ1ZVwiKSB7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMzA7XHJcbiAgICAgICAgICAgICAgICBFeHRlcm5hbEludGVyZmFjZS5jYWxsKFwiY2xvc2VTY3JlZW5cIik7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5wb3BVcFNjcmVlbiA9PT0gXCJwb3BVcFwiKSB7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMzA7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNDA7XHJcbiAgICAgICAgICAgICAgICBwYXJlbnRBcHBsaWNhdGlvbi5uYXZpZ2F0b3IucmVtb3ZlQ2hpbGRBdChwYXJlbnRBcHBsaWNhdGlvbi5uYXZpZ2F0b3Iuc2VsZWN0ZWRJbmRleCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuQU1MX01PRFVMRV9JRCwgdGhpcy5nZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcyksIFwiY2xvc2VTY3JlZW5IYW5kbGVyXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIG1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyUmVwb3J0U2V0dGluZ1xyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSBldmVudDogTW9kdWxlRXZlbnRcclxuICAgICAqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGxvYWQgcmVwb3J0IHNldHRpbmcgc2NyZWVuIGFzIGEgcG9wdXAsIG9uY2UgaXQgaXMgcmVhZHkgdG8gbG9hZC5cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIG1vZHVsZVJlYWR5RXZlbnRIYW5kbGVyUmVwb3J0U2V0dGluZyhldmVudCk6IHZvaWQge1xyXG4gICAgICAgIC8vVmFyaWFibGUgZm9yIGVycm9yTG9jYXRpb25cclxuICAgICAgICB2YXIgZXJyb3JMb2NhdGlvbjogbnVtYmVyID0gMDtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTA7XHJcbiAgICAgICAgICAgIHRoaXMucG9wdXBDbG9zZWRFdmVudEhhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgdGhpcy5nZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcyksIFwibW9kdWxlUmVhZHlFdmVudEhhbmRsZXJSZXBvcnRTZXR0aW5nXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIHBvcHVwQ2xvc2VkRXZlbnRIYW5kbGVyUmVwb3J0U2V0dGluZ1xyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSBldmVudDogRXZlbnRcclxuICAgICAqXHJcbiAgICAgKiBUaGlzIGZ1bmN0aW9uIGlzIHVzZWQgdG8gaGFuZGxlIHRoZSBjaGlsZCB3aW5kb3cgY2xvc2UgZXZlbnRcclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIHBvcHVwQ2xvc2VkRXZlbnRIYW5kbGVyUmVwb3J0U2V0dGluZyhldmVudCk6IHZvaWQge1xyXG4gICAgICAgIC8vVmFyaWFibGUgZm9yIGVycm9yTG9jYXRpb25cclxuICAgICAgICBjb25zdCBlcnJvckxvY2F0aW9uOiBudW1iZXIgPSAwO1xyXG4gICAgICAgIHRyeSB7XHJcblxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgdGhpcy5nZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcykgKyBcIi5teG1sXCIsIFwicG9wdXBDbG9zZWRFdmVudEhhbmRsZXJSZXBvcnRTZXR0aW5nXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBkZWxldGVBY3Rpb25cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGRlbGV0ZUFjdGlvbigpOiB2b2lkIHtcclxuICAgICAgICBpZiAodGhpcy5kZWxldGVNZXRob2QpIHtcclxuICAgICAgICAgICAgdGhpcy5kZWxldGVNZXRob2QoKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ5b3UgbXVzdCBzZXQgZGVsZXRlTWV0aG9kIGluIGNoaWxkIGNsYXNzLlwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBkdXBsaWNhdGVBY3Rpb25cclxuICAgICAqL1xyXG4gICAgcHJvdGVjdGVkIGR1cGxpY2F0ZUFjdGlvbigpOiB2b2lkIHtcclxuICAgICAgICBpZiAodGhpcy5kdXBsaWNhdGVNZXRob2QpIHtcclxuICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVNZXRob2QoKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ5b3UgbXVzdCBzZXQgZHVwbGljYXRlTWV0aG9kIGluIGNoaWxkIGNsYXNzLlwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBzZXREeW5hbWljQnV0dG9ucygpIHtcclxuICAgICAgICAvLyBzZXQgZHluYW1pYyBidXR0b25zLlxyXG4gICAgICAgIGNvbnN0IGJ1dHRvbnMgPSB0aGlzLmJ1dHRvbnMuc3BsaXQoXCIsXCIpO1xyXG4gICAgICAgIC8vIGxvb3Agb24gYnV0dG9ucyBhcnJheS5cclxuICAgICAgICBmb3IgKGNvbnN0IGluZGV4IGluIGJ1dHRvbnMpIHtcclxuICAgICAgICAgICAgaWYgKGJ1dHRvbnNbaW5kZXhdID09PSBcImFkZFwiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbiA9IHRoaXMuaGJveEJ1dHRvbnMuYWRkQ2hpbGQoU3d0QnV0dG9uKSBhcyBTd3RCdXR0b247XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbi5sYWJlbCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24uXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFkZEJ1dHRvbi50b29sVGlwID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi50b29sdGlwLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5hZGRCdXR0b24udGFiSW5kZXggPSBOdW1iZXIoaW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuYWRkQnV0dG9uLmlkID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5hZGRCdXR0b24ubmFtZSA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuYWRkQnV0dG9uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChidXR0b25zW2luZGV4XSA9PT0gXCJjaGFuZ2VcIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VCdXR0b24gPSB0aGlzLmhib3hCdXR0b25zLmFkZENoaWxkKFN3dEJ1dHRvbikgYXMgU3d0QnV0dG9uO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VCdXR0b24ubGFiZWwgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VCdXR0b24udG9vbFRpcCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24udG9vbHRpcC5cIiArIGJ1dHRvbnNbaW5kZXhdKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLnRhYkluZGV4ID0gTnVtYmVyKGluZGV4ICsgMSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5pZCA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlQnV0dG9uLm5hbWUgPSBidXR0b25zW2luZGV4XSArIFwiQnV0dG9uXCI7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNoYW5nZUJ1dHRvbi5jbGljayA9IChldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoYnV0dG9uc1tpbmRleF0gPT09IFwidmlld1wiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24gPSB0aGlzLmhib3hCdXR0b25zLmFkZENoaWxkKFN3dEJ1dHRvbikgYXMgU3d0QnV0dG9uO1xyXG4gICAgICAgICAgICAgICAgdGhpcy52aWV3QnV0dG9uLmxhYmVsID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi5cIiArIGJ1dHRvbnNbaW5kZXhdKTtcclxuICAgICAgICAgICAgICAgIHRoaXMudmlld0J1dHRvbi50b29sVGlwID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi50b29sdGlwLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy52aWV3QnV0dG9uLnRhYkluZGV4ID0gTnVtYmVyKGluZGV4ICsgMSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24uaWQgPSBidXR0b25zW2luZGV4XSArIFwiQnV0dG9uXCI7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24ubmFtZSA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMudmlld0J1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpZXdCdXR0b24uY2xpY2sgPSAoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLmNsaWNrSGFuZGxlcihldmVudCk7XHJcbiAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGJ1dHRvbnNbaW5kZXhdID09PSBcImRlbGV0ZVwiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbiA9IHRoaXMuaGJveEJ1dHRvbnMuYWRkQ2hpbGQoU3d0QnV0dG9uKSBhcyBTd3RCdXR0b247XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbi5sYWJlbCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24uXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRlbGV0ZUJ1dHRvbi50b29sVGlwID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi50b29sdGlwLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24udGFiSW5kZXggPSBOdW1iZXIoaW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmlkID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGVCdXR0b24ubmFtZSA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlQnV0dG9uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChidXR0b25zW2luZGV4XSA9PT0gXCJkdXBsaWNhdGVcIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24gPSB0aGlzLmhib3hCdXR0b25zLmFkZENoaWxkKFN3dEJ1dHRvbikgYXMgU3d0QnV0dG9uO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24ubGFiZWwgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5kdXBsaWNhdGVCdXR0b24udG9vbFRpcCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24udG9vbHRpcC5cIiArIGJ1dHRvbnNbaW5kZXhdKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLnRhYkluZGV4ID0gTnVtYmVyKGluZGV4ICsgMSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmR1cGxpY2F0ZUJ1dHRvbi5pZCA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuZHVwbGljYXRlQnV0dG9uLm5hbWUgPSBidXR0b25zW2luZGV4XSArIFwiQnV0dG9uXCI7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmR1cGxpY2F0ZUJ1dHRvbi5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmR1cGxpY2F0ZUJ1dHRvbi5jbGljayA9IChldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoYnV0dG9uc1tpbmRleF0gPT09IFwic2VhcmNoXCIpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uID0gdGhpcy5oYm94QnV0dG9ucy5hZGRDaGlsZChTd3RCdXR0b24pIGFzIFN3dEJ1dHRvbjtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uLmxhYmVsID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi5cIiArIGJ1dHRvbnNbaW5kZXhdKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VhcmNoQnV0dG9uLnRvb2xUaXAgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLnRvb2x0aXAuXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNlYXJjaEJ1dHRvbi50YWJJbmRleCA9IE51bWJlcihpbmRleCArIDEpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZWFyY2hCdXR0b24uaWQgPSBidXR0b25zW2luZGV4XSArIFwiQnV0dG9uXCI7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNlYXJjaEJ1dHRvbi5uYW1lID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZWFyY2hCdXR0b24uY2xpY2sgPSAoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLmNsaWNrSGFuZGxlcihldmVudCk7XHJcbiAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGJ1dHRvbnNbaW5kZXhdID09PSBcImNhbmNlbFwiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNhbmNlbEJ1dHRvbiA9IHRoaXMuaGJveEJ1dHRvbnMuYWRkQ2hpbGQoU3d0QnV0dG9uKSBhcyBTd3RCdXR0b247XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNhbmNlbEJ1dHRvbi5sYWJlbCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24uXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNhbmNlbEJ1dHRvbi50b29sVGlwID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi50b29sdGlwLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jYW5jZWxCdXR0b24udGFiSW5kZXggPSBOdW1iZXIoaW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2FuY2VsQnV0dG9uLmlkID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jYW5jZWxCdXR0b24ubmFtZSA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2FuY2VsQnV0dG9uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChidXR0b25zW2luZGV4XSA9PT0gXCJzYXZlXCIpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2F2ZUJ1dHRvbiA9IHRoaXMuaGJveEJ1dHRvbnMuYWRkQ2hpbGQoU3d0QnV0dG9uKSBhcyBTd3RCdXR0b247XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNhdmVCdXR0b24ubGFiZWwgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zYXZlQnV0dG9uLnRvb2xUaXAgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLnRvb2x0aXAuXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNhdmVCdXR0b24udGFiSW5kZXggPSBOdW1iZXIoaW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2F2ZUJ1dHRvbi5pZCA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2F2ZUJ1dHRvbi5uYW1lID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zYXZlQnV0dG9uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChidXR0b25zW2luZGV4XSA9PT0gXCJwcmludFwiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnByaW50SWNvbiA9IHRoaXMuaGJveEJ1dHRvbnMuYWRkQ2hpbGQoU3d0QnV0dG9uKSBhcyBTd3RCdXR0b247XHJcbiAgICAgICAgICAgICAgICB0aGlzLnByaW50SWNvbi5sYWJlbCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24uXCIgKyBidXR0b25zW2luZGV4XSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnByaW50SWNvbi50b29sVGlwID0gU3d0VXRpbC5nZXRDb21tb25NZXNzYWdlcyhcImJ1dHRvbi50b29sdGlwLlwiICsgYnV0dG9uc1tpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5wcmludEljb24udGFiSW5kZXggPSBOdW1iZXIoaW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgIHRoaXMucHJpbnRJY29uLmlkID0gYnV0dG9uc1tpbmRleF0gKyBcIkJ1dHRvblwiO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5wcmludEljb24ubmFtZSA9IGJ1dHRvbnNbaW5kZXhdICsgXCJCdXR0b25cIjtcclxuICAgICAgICAgICAgICAgIHRoaXMucHJpbnRJY29uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGlja0hhbmRsZXIoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICAvLyBhZGQgY2xvc2UgYnV0dG9uIHRvIHRlbXBsYXRlLlxyXG4gICAgICAgIHRoaXMuY2xvc2VCdXR0b24gPSB0aGlzLmhib3hCdXR0b25zLmFkZENoaWxkKFN3dEJ1dHRvbikgYXMgU3d0QnV0dG9uO1xyXG4gICAgICAgIHRoaXMuY2xvc2VCdXR0b24ubGFiZWwgPSBTd3RVdGlsLmdldENvbW1vbk1lc3NhZ2VzKFwiYnV0dG9uLmNsb3NlXCIpO1xyXG4gICAgICAgIHRoaXMuY2xvc2VCdXR0b24udG9vbFRpcCA9IFN3dFV0aWwuZ2V0Q29tbW9uTWVzc2FnZXMoXCJidXR0b24udG9vbHRpcC5jbG9zZVwiKTtcclxuICAgICAgICB0aGlzLmNsb3NlQnV0dG9uLnRhYkluZGV4ID0gMTA7XHJcbiAgICAgICAgdGhpcy5jbG9zZUJ1dHRvbi5pZCA9IFwiY2xvc2VCdXR0b25cIjtcclxuICAgICAgICB0aGlzLmNsb3NlQnV0dG9uLmNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgIHRoaXMuY2xpY2tIYW5kbGVyKGV2ZW50KTtcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBzcGxpdCB1cmwgYW5kXHJcbiAgICAgKiBnZXQgcGFyYW1zIGZyb20gdGhpcyBsYXN0IG9uZS5cclxuICAgICAqIEBwYXJhbSB1cmxcclxuICAgICAqL1xyXG4gICAgcHJpdmF0ZSBnZXREYXRhRnJvbVVybCh1cmw6IHN0cmluZyk6IGFueVtdIHtcclxuICAgICAgICBjb25zdCBkYXRhID0gbmV3IEFycmF5KCk7XHJcbiAgICAgICAgbGV0IGVycm9yTG9jYXRpb246IG51bWJlcjtcclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKFwiWyBnZXREYXRhRnJvbVVybCBdIG1ldGhvZCBTVEFSVFwiKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCBxbSA9IHVybC5sYXN0SW5kZXhPZihcIj9cIik7XHJcbiAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxMDtcclxuICAgICAgICAgICAgaWYgKHFtICE9PSAtMSkge1xyXG4gICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDIwO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcXVlcnk6IFN0cmluZyA9IHVybC5zdWJzdHIocW0gKyAxKTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHF1ZXJ5LnNwbGl0KFwiJlwiKTtcclxuICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAzMDtcclxuICAgICAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgcGFyYW1zLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDQwO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcmFtOiBTdHJpbmcgPSBwYXJhbXNbaV07XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmFtZVZhbHVlID0gcGFyYW0uc3BsaXQoXCI9XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSA2MDtcclxuICAgICAgICAgICAgICAgICAgICBpZiAobmFtZVZhbHVlLmxlbmd0aCA9PT0gMikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gNzA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleTogU3RyaW5nID0gbmFtZVZhbHVlWzBdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWw6IFN0cmluZyA9IG5hbWVWYWx1ZVsxXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDgwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoa2V5ID09PSBcImFjY2Vzc1wiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gOTA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL0dldCBhY2Nlc3MgcmlnaHRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFbXCJtZW51QWNjZXNzXCJdID0gdmFsO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChrZXkgPT09IFwicHJvZ3JhbUlkXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxMDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL0dldCBwcm9ncmFtSWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFbXCJwcm9ncmFtSWRcIl0gPSB2YWw7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGtleSA9PT0gXCJwb3BVcFNjcmVlblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTEwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy9HZXQgcG9wVXBTY3JlZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFbXCJwb3BVcFNjcmVlblwiXSA9IHZhbDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoa2V5ID09PSBcImF2YWlsYWJsZVNjcmVlblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTIwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy9HZXQgYXZhaWxhYmVTY3JlZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFbXCJhdmFpbGFibGVTY3JlZW5cIl0gPSB2YWw7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGtleSA9PT0gXCJyZWNvcmRJZFwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTMwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YVtcInJlY29yZElkXCJdID0gdmFsO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChrZXkgPT09IFwib2JqZWN0VHlwZVwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTQwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YVtcIm9iamVjdFR5cGVcIl0gPSB2YWw7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGtleSA9PT0gXCJwdl9wYXJ0eV90eXBlXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAxNTA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhW1wicGFydHlUeXBlXCJdID0gdmFsO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHBhcmVudEFwcGxpY2F0aW9uLmxvYWRlckluZm8udXJsID0gdXJsO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIHRoaXMubG9nZ2VyLmVycm9yKFwiWyBnZXREYXRhRnJvbVVybCBdIG1ldGhvZCAtIGVycm9yIFwiLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbIGdldERhdGFGcm9tVXJsIF0gbWV0aG9kIEVORFwiKTtcclxuICAgICAgICByZXR1cm4gZGF0YTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFBhcnQgb2YgYSBjYWxsYmFjayBmdW5jdGlvbiB0byBhbGwgZm9yIGNvbnRyb2wgb2YgdGhlIGxvYWRpbmcgc3dmIGZyb20gdGhlIEhUVFBDb21tcyBPYmplY3RcclxuICAgICAqL1xyXG4vLyAgICBwdWJsaWMgc3RhcnRPZkNvbW1zKCk6dm9pZCB7XHJcbi8vICAgICAgICBcclxuLy8gICAgICAgIHRoaXMuX2xvYWRpbmdJbWFnZS5zZXRWaXNpYmxlKHRydWUpO1xyXG4vL1xyXG4vLyAgICB9XHJcbi8vXHJcbi8vICAgIC8qKlxyXG4vLyAgICAgKiBQYXJ0IG9mIGEgY2FsbGJhY2sgZnVuY3Rpb24gdG8gYWxsIGZvciBjb250cm9sIG9mIHRoZSBsb2FkaW5nIHN3ZiBmcm9tIHRoZSBIVFRQQ29tbXMgT2JqZWN0XHJcbi8vICAgICoqL1xyXG4vLyAgICBwdWJsaWMgZW5kT2ZDb21tcygpOnZvaWQge1xyXG4vL1xyXG4vLyAgICAgICAgdGhpcy5fbG9hZGluZ0ltYWdlLnNldFZpc2libGUoZmFsc2UpO1xyXG4vL1xyXG4vLyAgICB9XHJcbi8vXHJcbi8vICAgIC8qKlxyXG4vLyAgICAgKiBJZiBhIGZhdWx0IG9jY3VycyB3aXRoIHRoZSBjb25uZWN0aW9uIHdpdGggdGhlIHNlcnZlciB0aGVuIGRpc3BsYXkgdGhlIGxvc3QgY29ubmVjdGlvbiBsYWJlbFxyXG4vLyAgICAqKi9cclxuLy8gICAgcHVibGljIGlucHV0RGF0YUZhdWx0KGV2ZW50KTp2b2lkIHtcclxuLy9cclxuLy8gICAgICAgIHRoaXMuU3d0QWxlcnQuZXJyb3IoZXZlbnQuZmF1bHQuZmF1bHRTdHJpbmcgKyBcIlxcblwiICsgZXZlbnQuZmF1bHQuZmF1bHRDb2RlICsgXCJcXG5cIiArIGV2ZW50LmZhdWx0LmZhdWx0RGV0YWlsKTtcclxuLy9cclxuLy8gICAgfVxyXG59XHJcbiJdfQ==