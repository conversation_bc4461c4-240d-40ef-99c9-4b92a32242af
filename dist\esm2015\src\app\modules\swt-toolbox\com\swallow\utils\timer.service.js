/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
export class Timer {
    /**
     * @param {?} duration
     * @param {?=} repeatCount
     */
    constructor(duration, repeatCount = 0) {
        this.duration = duration;
        this._isRunning = false;
        this._callback = new Function();
    }
    /**
     * This method is used to start timer.
     * @return {?}
     */
    start() {
        this._timer = setInterval((/**
         * @return {?}
         */
        () => {
            if (this._callback) {
                this._callback();
            }
        }), this.duration);
        this._isRunning = true;
    }
    /**
     * This method is used to stop timer
     * @return {?}
     */
    stop() {
        clearInterval(this._timer);
        this._isRunning = false;
    }
    /**
     * This method is used to set timer delay.
     * @param {?} duration
     * @return {?}
     */
    delay(duration) {
        this.stop();
        this.duration = duration;
        this.start();
    }
    /**
     * This method is used to check timer state
     * <code>timer.running()</code> if true the timer
     * is already lunched.
     * @return {?}
     */
    get running() {
        return this._isRunning;
    }
    /**
     * This method is used to add event listener to
     * timer instance.
     * @param {?} name
     * @param {?} callBack
     * @return {?}
     */
    addEventListener(name, callBack) {
        this._callback = callBack;
    }
}
Timer.decorators = [
    { type: Injectable }
];
/** @nocollapse */
Timer.ctorParameters = () => [
    { type: Number },
    { type: Number }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._isRunning;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._callback;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._timer;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype.duration;
}
//# sourceMappingURL=data:application/json;base64,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