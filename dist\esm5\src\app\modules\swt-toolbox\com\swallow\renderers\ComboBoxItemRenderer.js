/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
/** @type {?} */
var select2 = require('select2');
//@dynamic
var 
//@dynamic
ComboBoxItemRenderer = /** @class */ (function () {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    function ComboBoxItemRenderer(args) {
        this.args = args;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        // private appendSource = this.commonGrid.appendSourceFunction( this.args.item, this.args.column.field );
        this.enabledFlag = true;
        this.columnDef = this.args.column;
        this.fieldId = this.columnDef && this.columnDef.id;
        this.cursor = 0;
        this.logger = new Logger('ComboBoxItemRenderer', null, 6);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.init = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [init] -START- ', this.args.column.params.selectDataSource);
        try {
            if (this.columnDef.params.enableDisableCells) {
                this.enabledFlag = this.columnDef.params.enableDisableCells(this.args.item, this.args.column.field);
            }
            if (this.commonGrid.ITEM_CHANGED.observers && this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                /** @type {?} */
                var x = this.commonGrid.ITEM_CHANGED.observers[0];
                this.commonGrid.ITEM_CHANGED.observers = [];
                this.commonGrid.ITEM_CHANGED.observers[0] = x;
            }
            if (this.showHideCells) {
                /** @type {?} */
                var gridOptions = this.args.grid.getOptions();
                $('.comboBoxRender-dropDown-' + this.fieldId).remove();
                this.$input = $("\n                        <div class=\"renderAsInput comboBoxRender-container  container-" + this.fieldId + "\">\n                           <div class=\"input-group\">\n                               <input " + ((!this.enabledFlag || !this.columnDef.params.grid.enabled) ? 'disabled' : '') + " autocomplete=\"off\" id=\"combobox-filter-input-" + this.fieldId + "\" type=\"text\"   class=\"form-control comboBoxRender-filter-input\">\n                               <div " + (!this.enabledFlag || !this.columnDef.params.grid.enabled ? 'style="opacity:0.7;"' : "") + " class=\"renderAsInput input-group-addon arrow-" + this.fieldId + "\"><i class=\"renderAsInput glyphicon glyphicon-triangle-bottom\"></i></div>\n                           </div>");
                this.$dropDown = $("         " + (this.enabledFlag && this.columnDef.params.grid.enabled ?
                    "<div class=\"renderAsInput comboBoxRender-dropDown-" + this.fieldId + "\" style=\"position:absolute;z-index:2\"> \n                               <ul id=\"list-" + this.fieldId + "\" class=\"renderAsInput comboBoxRender-dropDown-ul\">\n                                   <!--<li *ngIf=\"_exist\">{{ notFound }}</li>-->\n                               </ul>\n                           </div>" : '') + "\n                           \n                       </div>\n                   ");
                this.$input.appendTo(this.args.container);
                $('body').append(this.$dropDown);
                this.populateSelect(this.$input, this.args.column.params.selectDataSource, false);
            }
            else {
                this.$input = $("");
            }
        }
        catch (e) {
            return;
        }
        this.logger.info('Method [init] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} select
     * @param {?} dataSource
     * @param {?} addBlank
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.populateSelect = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} select
     * @param {?} dataSource
     * @param {?} addBlank
     * @return {?}
     */
    function (select, dataSource, addBlank) {
        var _this = this;
        try {
            this.logger.info('Method [populateSelect] -START-', this.showHideCells);
            if (this.showHideCells) {
                // make sure the prop exists first
                /** @type {?} */
                var item = this.args.item;
                if (!(dataSource instanceof Array)) {
                    /** @type {?} */
                    var array = [];
                    array[0] = dataSource;
                    dataSource = array;
                }
                this.dataSource = dataSource;
                this.defaultValue = item[this.columnDef.field] && item[this.columnDef.field].toString();
                if (dataSource && dataSource.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.value == _this.defaultValue; }))) {
                    this.defaultContent = dataSource.find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return x.value == _this.defaultValue; })).content;
                }
                if ((this.defaultContent == '' || this.defaultContent == undefined) && this.defaultValue != '') {
                    this.defaultContent = this.defaultValue;
                }
                $('#combobox-filter-input-' + this.fieldId).val(this.defaultContent);
                //console.log(' this.enabledFlag :',this.enabledFlag, ' this.columnDef.params.grid.enabled :',this.columnDef.params.grid.enabled)
                if (this.enabledFlag && this.columnDef.params.grid.enabled) {
                    //console.log('enter')
                    for (var index = 0; index < dataSource.length; index++) {
                        /** @type {?} */
                        var item = dataSource[index];
                        if (this.columnDef.dataId) {
                            if (dataSource[index].type && this.args.item[this.columnDef.dataId]) {
                                if (dataSource[index].type == this.args.item[this.columnDef.dataId]) {
                                    $('#list-' + this.fieldId).append("<li  class='dropDownli-" + this.fieldId + " renderAsInput  comboBoxrender-dropDown-li " + (String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : "") + "'> \n                                  <option class=\"renderAsInput combo-option\" value=\"" + item.value + "\">\n                                       " + (item.content ? item.content : '') + "   \n                                  </option> \n                          </li>");
                                }
                            }
                            else {
                                $('#list-' + this.fieldId).append("<li  class='dropDownli-" + this.fieldId + " renderAsInput  comboBoxrender-dropDown-li " + (String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : "") + "'> \n                                    <option class=\"renderAsInput combo-option\" value=\"" + item.value + "\">\n                                        " + (item.content ? item.content : '') + "   \n                                    </option> \n                                </li>");
                            }
                        }
                        else {
                            $('#list-' + this.fieldId).append("<li  class='dropDownli-" + this.fieldId + " renderAsInput  comboBoxrender-dropDown-li " + (String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : "") + "'> \n                                <option class=\"renderAsInput combo-option\" value=\"" + item.value + "\">\n                                    " + (item.content ? item.content : '') + "   \n                                </option> \n                            </li>");
                        }
                    }
                    $('ul.comboBoxRender-dropDown-ul li:even').addClass('evenRows');
                    $('ul.comboBoxRender-dropDown-ul li:odd').addClass('oddRows');
                    $('.comboBoxRender-dropDown-' + this.fieldId).show();
                    $('#combobox-filter-input-' + this.fieldId).focus();
                    $('#combobox-filter-input-' + this.fieldId).select();
                    $(".arrow-" + this.fieldId).click((/**
                     * @return {?}
                     */
                    function () {
                        $('.comboBoxRender-dropDown-' + _this.fieldId).toggle();
                    }));
                    $('.comboBoxRender-dropDown-ul').width($('.container-' + this.fieldId).width());
                    $("span[class^='fancytree-'] , .swtbtn ").click((/**
                     * @return {?}
                     */
                    function () {
                        $('#list-' + _this.fieldId).remove();
                    }));
                    $(".arrow-" + this.fieldId).click((/**
                     * @return {?}
                     */
                    function () {
                        //-M5821 -issue 2.
                        $('#list-' + _this.fieldId).toggle();
                    }));
                    $("#list-" + this.fieldId).width($('.container-' + this.fieldId).width());
                    $('.comboBoxRender-dropDown-' + this.fieldId).css({ 'z-index': '999' });
                    /** @type {?} */
                    var left = this.commonGrid.gridObj.getActiveCellPosition().left;
                    /** @type {?} */
                    var bottom = $(window).height() - this.commonGrid.gridObj.getActiveCellPosition().top - this.commonGrid.gridObj.getActiveCellPosition().height;
                    //-Fix M5267 / Issue 23 : Open the "Recon.type/Life.type" combobox in the upper grid ==> The list options is shifted
                    $('.comboBoxRender-dropDown-' + this.fieldId).css('bottom', bottom);
                    /** @type {?} */
                    var classes = $($($('.slickgrid-container.' + this.commonGrid.gridUID)[0].offsetParent)[0]).attr("class");
                    /** @type {?} */
                    var exist = false;
                    if (classes) {
                        exist = classes.includes('window-container');
                    }
                    $("#list-" + this.fieldId).css('left', left + (exist ? 9 : 0));
                    /** @type {?} */
                    var selectedItem = $('li.comboBoxrender-dropDown-li.isSelected:visible');
                    //console.log(' selectedItem =',selectedItem);
                    //-Fix M5305/Issue 1.
                    if (selectedItem && selectedItem.length > 0)
                        $(selectedItem)[0].scrollIntoView();
                    /** @type {?} */
                    var selected = false;
                    $('#combobox-filter-input-' + this.fieldId).on("input", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        $("#list-" + _this.fieldId + " li").filter((/**
                         * @return {?}
                         */
                        function () {
                            //-Fix M5305 : Search By UpperCase does not work.
                            $(this).toggle($(this).text().toLowerCase().indexOf(String(event.target.value).toLowerCase()) > -1);
                        }));
                        //console.log('displayed li :', $('li[style*="display: list-item"]') );
                        /** @type {?} */
                        var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                        if (visibleLi.length > 0) {
                            $('.isSelected').removeClass('isSelected');
                            $(visibleLi[0]).addClass('isSelected');
                        }
                        _this.cursor = 0;
                    }));
                    $('#combobox-filter-input-' + this.fieldId).keydown((/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        //console.log('dropDownli keydown event.keyCode',event.keyCode);
                        if (event.keyCode == 40 || event.keyCode == 38) {
                            event.preventDefault();
                            event.stopPropagation();
                            $("#list-" + _this.fieldId).show();
                            $('.comboBoxRender-dropDown-' + _this.fieldId).show();
                        }
                        else if (event.keyCode == 13) {
                            /** @type {?} */
                            var newValue = $('#combobox-filter-input-' + _this.fieldId).val();
                            /** @type {?} */
                            var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                            if (visibleLi.length == 0) {
                                _this.args.grid.getEditorLock().commitCurrentEdit();
                            }
                            else {
                                $('li.isSelected').mousedown();
                            }
                        }
                    }));
                    //-CLICK ON LIST - SELECT ITEM
                    $('.dropDownli-' + this.fieldId).mousedown((/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        /** @type {?} */
                        var target_value = '';
                        if ($(event.target)[0].nodeName == "LI") {
                            //console.log('$($( li.isSelected ).children()[0] ).text().trim() =',$($('li.isSelected').children()[0] ).text().trim())
                            /** @type {?} */
                            var target_value_index = _this.args.column.params.selectDataSource.findIndex((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return x.content == $($('li.isSelected').children()[0]).text().trim(); }));
                            if (target_value_index != -1)
                                target_value = _this.args.column.params.selectDataSource[target_value_index].value;
                        }
                        /** @type {?} */
                        var value = $(event.target)[0].nodeName == "LI" ? target_value : event.target.value;
                        /** @type {?} */
                        var label = $(event.target)[0].nodeName == "LI" ? $($('li.isSelected').children()[0]).text().trim() : event.target.label;
                        if (value !== _this.defaultValue) {
                            $('#combobox-filter-input-' + _this.fieldId).val(label);
                            _this.args.item[_this.args.column.field] = value;
                            _this.commonGrid.selectedItem[_this.args.column.field].content = value;
                            // -- check if changed
                            _this.CRUD_CHANGES_DATA = _this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                            if (_this.CRUD_CHANGES_DATA != undefined && _this.CRUD_CHANGES_DATA != null) {
                                _this.originalDefaultValue = _this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? _this.CRUD_CHANGES_DATA['crud_original_data'][_this.args.column.field] : null;
                            }
                            /** @type {?} */
                            var crudInsert = null;
                            /** @type {?} */
                            var thereIsInsert = false;
                            //console.log('originalDefaultValue :',this.originalDefaultValue)
                            //console.log('this.defaultValue :',this.defaultValue)
                            //console.log('this.args.item[this.args.column.field] :',this.args.item[this.args.column.field])
                            if ((_this.originalDefaultValue == null && (_this.defaultValue != _this.args.item[_this.args.column.field])) || (((_this.originalDefaultValue != null) && (_this.originalDefaultValue != _this.args.item[_this.args.column.field])))) {
                                if (_this.commonGrid.changes.getValues().length > 0) {
                                    crudInsert = _this.commonGrid.changes.getValues().find((/**
                                     * @param {?} x
                                     * @return {?}
                                     */
                                    function (x) { return ((x.crud_data.id == _this.args.item.id) && (x.crud_operation == "I")); }));
                                    if (crudInsert != undefined && crudInsert[_this.CRUD_OPERATION].indexOf('I') == 0)
                                        thereIsInsert = true;
                                }
                                /** @type {?} */
                                var ComboboxSelectedItem = _this.dataSource.find((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return x.value == value; }));
                                /** @type {?} */
                                var updatedObject = {
                                    rowIndex: _this.args.item.id,
                                    columnIndex: _this.args.column.columnorder,
                                    new_row: _this.args.item,
                                    changedColumn: _this.args.column.field,
                                    oldValue: _this.defaultValue,
                                    newValue: _this.args.item[_this.args.column.field],
                                    ComboboxSelectedItem: ComboboxSelectedItem
                                };
                                if (!thereIsInsert) {
                                    /** @type {?} */
                                    var original_row = [];
                                    for (var key in _this.args.item) {
                                        if (key != 'slickgrid_rowcontent') {
                                            original_row[key] = _this.args.item[key];
                                        }
                                        else {
                                            break;
                                        }
                                    }
                                    original_row['slickgrid_rowcontent'] = {};
                                    for (var key in _this.args.item['slickgrid_rowcontent']) {
                                        original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, _this.args.item['slickgrid_rowcontent'][key]);
                                        original_row[key] = original_row['slickgrid_rowcontent'][key].content;
                                    }
                                    original_row[_this.args.column.field] = _this.defaultValue;
                                    original_row['slickgrid_rowcontent'][_this.args.column.field].content = _this.defaultValue;
                                    updatedObject['original_row'] = original_row;
                                    _this.commonGrid.spyChanges({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field] });
                                    if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                        /** @type {?} */
                                        var x = SwtCommonGridItemRenderChanges.observers[0];
                                        SwtCommonGridItemRenderChanges.observers = [];
                                        SwtCommonGridItemRenderChanges.observers[0] = x;
                                    }
                                    SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                                    _this.commonGrid.updateCrud(updatedObject);
                                }
                                else {
                                    crudInsert['crud_data'][_this.args.column.field] = _this.args.item[_this.args.column.field];
                                    crudInsert['crud_data']['slickgrid_rowcontent'][_this.args.column.field] = { content: _this.args.item[_this.args.column.field] };
                                }
                                /** @type {?} */
                                var listEvent = {
                                    rowIndex: _this.args.item.id,
                                    target: "ComboBoxItemRenderer",
                                    dataField: _this.args.column.field,
                                    listData: tslib_1.__assign({}, updatedObject),
                                    dataprovider: _this.args.column.params.selectDataSource
                                };
                                /*  if ( this.commonGrid.ITEM_CHANGED.observers.length > 1 ) {
                                    var x: Observer<any> = this.commonGrid.ITEM_CHANGED.observers[0];
                                    this.commonGrid.ITEM_CHANGED.observers = []
                                    this.commonGrid.ITEM_CHANGED.observers[0] = x;
                                }*/
                                _this.commonGrid.ITEM_CHANGED.emit(listEvent);
                            }
                            else if ((_this.originalDefaultValue == _this.args.item[_this.args.column.field])) {
                                /** @type {?} */
                                var crudChange = _this.commonGrid.changes.getValues().find((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                /** @type {?} */
                                var ch = String("U(" + _this.args.column.field + ")");
                                if (crudChange) {
                                    if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                                    }
                                    else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                                    }
                                    else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                                    }
                                    if (crudChange['crud_operation'] == "") {
                                        /** @type {?} */
                                        var crudChangeIndex = _this.commonGrid.changes.getValues().findIndex((/**
                                         * @param {?} x
                                         * @return {?}
                                         */
                                        function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                        _this.commonGrid.changes.remove(_this.commonGrid.changes.getKeys()[crudChangeIndex]);
                                    }
                                }
                                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                                if (_this.commonGrid.changes.size() == 0)
                                    _this.commonGrid.spyNoChanges({ field: _this.args.column.field });
                                SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                            }
                        }
                        //-M5821 -issue 2.
                        $('#list-' + _this.fieldId).hide();
                        _this.args.grid.getEditorLock().commitCurrentEdit();
                        _this.cursor = 0;
                    }));
                    //-FOCUS IN :
                    /** @type {?} */
                    var target = {
                        name: this.args.column.name,
                        field: this.args.column.field,
                        editor: "ComboBoxItemRenderer",
                        formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                        data: this.args.item
                    };
                    /** @type {?} */
                    var ListEvent = {
                        rowIndex: this.args.item.id,
                        cellIndex: this.args.column.columnorder,
                        columnIndex: this.args.column.columnorder,
                        target: target
                    };
                    if (this.commonGrid.ITEM_FOCUS_IN.observers.length > 1) {
                        /** @type {?} */
                        var x = this.commonGrid.ITEM_FOCUS_IN.observers[0];
                        this.commonGrid.ITEM_FOCUS_IN.observers = [];
                        this.commonGrid.ITEM_FOCUS_IN.observers[0] = x;
                    }
                    this.commonGrid.ITEM_FOCUS_IN.emit(ListEvent);
                    //FOCUS OUT :
                    $(this.$input).on('focusout', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        console.log('focusout');
                        /** @type {?} */
                        var newValue = $('#combobox-filter-input-' + _this.fieldId).val();
                        if (_this.commonGrid.onFocusOut.observers.length > 1) {
                            /** @type {?} */
                            var x = _this.commonGrid.onFocusOut.observers[0];
                            _this.commonGrid.onFocusOut.observers = [];
                            _this.commonGrid.onFocusOut.observers[0] = x;
                        }
                        _this.commonGrid.onFocusOut.emit(_this);
                        /** @type {?} */
                        var target = {
                            name: _this.args.column.name,
                            field: _this.args.column.field,
                            editor: "ComboBoxItemRenderer",
                            formatter: "ComboFormatter",
                            data: _this.args.item
                        };
                        /** @type {?} */
                        var ListEvent = {
                            rowIndex: _this.args.item.id,
                            cellIndex: _this.args.column.columnorder,
                            columnIndex: _this.args.column.columnorder,
                            target: target,
                            dataprovider: _this.args.column.params.selectDataSource
                        };
                        if (_this.commonGrid.ITEM_FOCUS_OUT.observers.length > 1) {
                            /** @type {?} */
                            var x = _this.commonGrid.ITEM_FOCUS_OUT.observers[0];
                            _this.commonGrid.ITEM_FOCUS_OUT.observers = [];
                            _this.commonGrid.ITEM_FOCUS_OUT.observers[0] = x;
                        }
                        _this.commonGrid.ITEM_FOCUS_OUT.emit(ListEvent);
                        //-M5821 -issue 2.
                        console.log('this.fieldId  :', _this.fieldId);
                        $('.comboBoxRender-dropDown-ul').hide();
                        $('#list-' + _this.fieldId).hide();
                        if (!$($(event)[0].relatedTarget).is('div.grid-canvas')) {
                            _this.commonGrid.gridObj.getEditorLock().commitCurrentEdit();
                        }
                        if (_this.dataSource.findIndex((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return x.content === $('#combobox-filter-input-' + _this.fieldId).val(); })) == -1) {
                            //-Append new item to combodataprovider:
                            _this.args.item[_this.args.column.field] = newValue;
                            _this.commonGrid.selectedItem[_this.args.column.field].content = newValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: _this.args.item.id,
                                columnIndex: _this.args.column.columnorder,
                                new_row: _this.args.item,
                                changedColumn: _this.args.column.field,
                                oldValue: _this.defaultValue,
                                newValue: newValue,
                                ComboboxSelectedItem: null
                            };
                            /** @type {?} */
                            var listEvent = {
                                rowIndex: _this.args.item.id,
                                target: "ComboBoxItemRenderer",
                                dataField: _this.args.column.field,
                                listData: tslib_1.__assign({}, updatedObject),
                                dataprovider: _this.args.column.params.selectDataSource
                            };
                            _this.commonGrid.ITEM_CHANGED.emit(listEvent);
                            _this.args.grid.getEditorLock().commitCurrentEdit();
                            _this.cursor = 0;
                        }
                    }));
                    //-FILTER ON INPUT:
                    /** @type {?} */
                    var i = 0;
                    $('#combobox-filter-input-' + this.fieldId).on('keyup', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        /** @type {?} */
                        var $hlight = $('li.isSelected:visible');
                        /** @type {?} */
                        var $div = $('li');
                        /** @type {?} */
                        var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                        //console.log('displayed visibleLi.length :', visibleLi.length , ' this.cursor : ',this.cursor);
                        if (event.keyCode == 40) {
                            if (visibleLi.length > 0 && _this.cursor < visibleLi.length - 1) {
                                $('.isSelected').removeClass('isSelected');
                                $(visibleLi[_this.cursor + 1]).addClass('isSelected');
                                $(visibleLi[_this.cursor + 1])[0].scrollIntoView();
                                _this.cursor++;
                            }
                        }
                        else if (event.keyCode === 38) {
                            if (visibleLi.length > 0 && _this.cursor > 0) {
                                $('.isSelected').removeClass('isSelected');
                                $(visibleLi[_this.cursor - 1]).addClass('isSelected');
                                $(visibleLi[_this.cursor - 1])[0].scrollIntoView();
                                _this.cursor--;
                            }
                        }
                    }));
                }
            }
            this.logger.info('Method [populateSelect] -END-');
        }
        catch (e) {
            return;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.loadValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('Method [loadValue] -START-');
        this.logger.info('Method [loadValue] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.serializeValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [serializeValue] -START/END-', this.$input.val());
        return this.$input.val();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.destroy = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        try {
            this.logger.info('Method [destroy] -START/END-');
        }
        catch (e) {
            return;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.focus = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [focus] -START/END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.applyValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        this.logger.info('Method [applyValue] -START/end- item =', item, ' state =', state);
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.isValueChanged = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        try {
            this.logger.info('Method [isValueChanged] -START/END-  =');
            return true;
        }
        catch (e) {
            return;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.validate = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        try {
            this.logger.info('Method [validate] -START-');
            //console.log('Method [validate] -START-');
            if (this.showHideCells) {
                if (this.args.column.validator) {
                    /** @type {?} */
                    var validationResults = this.args.column.validator(this.$input.val());
                    if (!validationResults.valid) {
                        return validationResults;
                    }
                }
            }
            this.logger.info('Method [validate] -END-');
            //console.log('Method [validate] -END-');
            return {
                valid: true,
                msg: null
            };
        }
        catch (e) {
            return;
        }
    };
    /**
     * This method is used to refresh
     * comboBox view.
     */
    /**
     * This method is used to refresh
     * comboBox view.
     * @return {?}
     */
    ComboBoxItemRenderer.prototype.showAlert = /**
     * This method is used to refresh
     * comboBox view.
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            /** @type {?} */
            var index = this.dataSource.findIndex((/**
             * @param {?} item
             * @return {?}
             */
            function (item) { return (((((/** @type {?} */ (item.content))).toUpperCase()).trim()).startsWith((((/** @type {?} */ ($('#combobox-filter-input-' + _this.fieldId).val()))).toUpperCase()).trim())); }));
            // if ( index === -1 && !this.appendSource && this.dataSource.length > 0 ) {
            // SwtAlert.warning( SwtUtil.getCommonMessages( 'alert.combo_warning' ), null, 4, this, () => {
            //     $( '#combobox-filter-input-' + this.fieldId ).val( null );
            //     $( '#combobox-filter-input-' + this.fieldId ).focus();
            //     $( "#list-" + this.fieldId + " li" ).filter( function() {
            //         $( this ).toggle( true )
            //     } );
            // }, 4 );
            // }
        }
        catch (error) {
            return;
        }
    };
    return ComboBoxItemRenderer;
}());
//@dynamic
export { ComboBoxItemRenderer };
if (false) {
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.defaultContent;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.dataSource;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.correspondantProperty;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.$dropDown;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_OPERATION;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.enabledFlag;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.columnDef;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.fieldId;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.cursor;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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