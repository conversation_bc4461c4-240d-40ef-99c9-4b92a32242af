/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CodemirrorComponent } from '@ctrl/ngx-codemirror';
import * as CodeMirror from 'codemirror';
/**
 * <AUTHOR>
 *
 */
export class SwtPrettyPrintTextArea {
    /**
     * @param {?} elem
     * @param {?} cd
     */
    constructor(elem, cd) {
        this.elem = elem;
        this.cd = cd;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this._scroll = new Function();
        this._focusIn = new Function();
        this._focusOut = new Function();
        this._change = new Function();
        this._text = '';
        this._initialtxt = '';
        this.firstCall = true;
        this.CUSTOM = 99;
        this.id = '';
        // @Input("editable") editable: string;
        this.change_ = new EventEmitter();
        this.focusOut_ = new EventEmitter();
        this.focusIn_ = new EventEmitter();
        this.listCodeWordsInBD = ['P${ACCOUNT_FULLNAME}', 'P${AMOUNT}', 'P${CORRESP_EXT_ID}', 'P${CURRENCY}', 'P${ENTITY_EXT_ID}', 'P${ENTITY_NAME}', 'P${FOLLOWUP_ID}', 'P${MATCH_BALANCE}', 'P${MATCH_DATE}',
            'P${MATCH_ID}', 'P${OUR_REFERENCE}', 'P${USER NAME}', 'P${USERNAME}', 'P${VALUE_DATE}', 'T${azaz}', 'T${CHAR_WORDS}', 'T${template_fr}', 'T${test1}', 'T${test2}',
            'T${test208}', 'T${test', '208_210}', 'T${aaaaa}', 'T${Template1}', 'T${Template2}', 'T${Template3}',
            'T${Template4}', 'T${t}', 'T${test_208210}', 'T${zzzzzzzzzzzztest}', 'T${TEST1}', 'T${testSeif2}',
            'T${Imed}', 'T${testIkram}', 'T${marwen}', 'T${test210}', 'T${test', 'case', '108}'];
        this.options = {
            lineNumbers: false,
            lineWrapping: true,
            readOnly: false,
            theme: 'neat',
            mode: 'codewords',
        };
    }
    /**
     * @return {?}
     */
    ngDoCheck() {
        if (this.editable == 'true' || this.editable == true) {
            this.options['readOnly'] = false;
        }
        else if (this.editable == 'false' || this.editable == false) {
            this.options['readOnly'] = true;
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.defineMode('codewords', this.listCodeWordsInBD);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    handleChange(event) {
        this._text = event;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    focusChange(event) {
        if (event == true) {
            this.focusIn_.emit(event);
            if (this.text != this._initialtxt) {
                this.change_.emit(event);
            }
        }
        else if (event == false) {
            this.focusOut_.emit(event);
            if (this.text != this._initialtxt) {
                this.change_.emit(event);
            }
        }
    }
    /**
     * @param {?} language
     * @param {?} keywords
     * @return {?}
     */
    defineMode(language, keywords) {
        // Definir un language dite "codewords"
        // Exemple: https://github.com/converge/brackets-nasm-syntax-highlighter/blob/master/lib/nasm/nasm.js
        // Liens utils:
        // https://codemirror.net/demo/theme.html#3024-day
        // https://github.com/TypeCtrl/ngx-codemirror
        // https://codemirror.net/demo/simplemode.html
        //  keywords = this.listCodeWordsInBD.match(/("[^"]+"|[^"\s]+)/g);
        //  console.log("defineMode---------listCodeWordsInBD", this.listCodeWordsInBD);
        //  console.log("defineMode----------language", language, "keywords", keywords);
        if (language != undefined && keywords != undefined) {
            CodeMirror.defineMode(language, (/**
             * @return {?}
             */
            function () {
                /**
                 * @param {?} str
                 * @return {?}
                 */
                function words(str) {
                    /** @type {?} */
                    const obj = {};
                    /** @type {?} */
                    const words = keywords;
                    for (let i = 0; i < words.length; ++i) {
                        obj[words[i].toLowerCase()] = true;
                    }
                    return obj;
                }
                // instructions http://www.nasm.us/doc/nasmdocb.html
                /** @type {?} */
                const keywordsTab = words(keywords);
                return {
                    /**
                     * @return {?}
                     */
                    startState() {
                        return {
                            tokenize: null,
                        };
                    },
                    /**
                     * @param {?} stream
                     * @param {?} state
                     * @return {?}
                     */
                    token(stream, state) {
                        if (state.tokenize) {
                            return state.tokenize(stream, state);
                        }
                        /** @type {?} */
                        let cur;
                        /** @type {?} */
                        let ch = stream.next();
                        // labels and sections/segments
                        if (/\S/.test(ch)) {
                            stream.eatWhile(/\S/);
                            cur = stream.current().toLowerCase();
                            // Dans certain cas, le keyword contient de l'espace ! , il faut tout consommer
                            if (cur.indexOf('p${', 0) >= 0 && cur.indexOf('}', 0) < 0) {
                                stream.eatSpace();
                                stream.eatWhile(/\S/);
                                cur = stream.current().toLowerCase();
                            }
                        }
                        if (keywordsTab.propertyIsEnumerable(cur)) {
                            stream.eatWhile(/\w/);
                            return 'keyword';
                        }
                    },
                };
            }));
        }
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        if (this.codemirror) {
            this.codemirror = this.ngxCodeMirror.codeMirror;
        }
    }
    /**
     * @return {?}
     */
    ngAfterViewChecked() {
        this.ngxCodeMirror.writeValue(this.text);
    }
    /**
     * @param {?} syntax
     * @param {?=} keywords
     * @return {?}
     */
    registerSyntax(syntax, keywords) {
        if (syntax == 99) {
            this.listCodeWordsInBD = keywords.match(/("[^"]+"|[^"\s]+)/g);
        }
    }
    // /* enabled getter and setter */
    // @Input()
    // set enabled(value: boolean) {
    //     if (typeof (value) !== 'string') {
    //         this._enabled = value;
    //     } else {
    //         if (value + '' === 'true') {
    //             this._enabled = true;
    //         } else {
    //             this._enabled = false;
    //         }
    //     }
    // }
    // get enabled() {
    //     return this._enabled;
    // }
    /* setFocus function */
    /**
     * @return {?}
     */
    setFocus() {
        this.elem.nativeElement.focus();
    }
    /* height getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._height = value + 'px';
        }
        else {
            this._height = value;
        }
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /* width getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._width = value + 'px';
        }
        else {
            this._width = value;
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /* paddingTop getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set paddingTop(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._paddingTop = value + 'px';
        }
        else {
            this._paddingTop = value;
        }
    }
    /**
     * @return {?}
     */
    get paddingTop() {
        return this._paddingTop;
    }
    /* paddingRight getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set paddingRight(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._paddingRight = value + 'px';
        }
        else {
            this._paddingRight = value;
        }
    }
    /**
     * @return {?}
     */
    get paddingRight() {
        return this._paddingRight;
    }
    /* marginLeft getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set marginLeft(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._marginLeft = value + 'px';
        }
        else {
            this._marginLeft = value;
        }
    }
    /**
     * @return {?}
     */
    get marginLeft() {
        return this._marginLeft;
    }
    /* marginBottom getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set marginBottom(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._marginBottom = value + 'px';
        }
        else {
            this._marginBottom = value;
        }
    }
    /**
     * @return {?}
     */
    get marginBottom() {
        return this._marginBottom;
    }
    /* marginRight getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set marginRight(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._marginRight = value + 'px';
        }
        else {
            this._marginRight = value;
        }
    }
    /**
     * @return {?}
     */
    get marginRight() {
        return this._marginRight;
    }
    /* marginTop getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set marginTop(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._marginTop = value + 'px';
        }
        else {
            this._marginTop = value;
        }
    }
    /**
     * @return {?}
     */
    get marginTop() {
        return this._marginTop;
    }
    /* paddingLeft getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set paddingLeft(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._paddingLeft = value + 'px';
        }
        else {
            this._paddingLeft = value;
        }
    }
    /**
     * @return {?}
     */
    get paddingLeft() {
        return this._paddingLeft;
    }
    /* paddingBottom getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set paddingBottom(value) {
        if (value !== undefined && value.indexOf('%') === -1) {
            this._paddingBottom = value + 'px';
        }
        else {
            this._paddingBottom = value;
        }
    }
    /**
     * @return {?}
     */
    get paddingBottom() {
        return this._paddingBottom;
    }
    /* scroll getter and setter */
    /**
     * @param {?} handler
     * @return {?}
     */
    set scroll(handler) {
        this._scroll = handler;
    }
    /**
     * @return {?}
     */
    get scroll() {
        return this._scroll;
    }
    /* focusOut getter and setter */
    /**
     * @param {?} handler
     * @return {?}
     */
    set focusOut(handler) {
        this._focusOut = handler;
    }
    /**
     * @return {?}
     */
    get focusOut() {
        return this._focusOut;
    }
    /* focusIn getter and setter */
    /**
     * @param {?} handler
     * @return {?}
     */
    set focusIn(handler) {
        this._focusIn = handler;
    }
    /**
     * @return {?}
     */
    get focusIn() {
        return this._focusIn;
    }
    /* change getter and setter */
    /**
     * @param {?} handler
     * @return {?}
     */
    set change(handler) {
        this._change = handler;
    }
    /**
     * @return {?}
     */
    get change() {
        return this._change;
    }
    /* text getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        if (value) {
            this._text = value;
        }
        else {
            this._text = '';
        }
        if (this.firstCall) {
            this.originalValue = this._text;
            this.firstCall = false;
        }
    }
    /**
     * @return {?}
     */
    get text() {
        return this._text;
    }
    /* editable getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        if (typeof (value) === 'string') {
            if (value === 'true') {
                this._editable = 'true';
            }
            else {
                this._editable = 'false';
            }
        }
        else {
            this._editable = value;
        }
    }
    /**
     * @return {?}
     */
    get editable() {
        return this._editable;
    }
    /* Spy change function */
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ target: this, value: event });
        }
        else {
            this.onSpyChange.emit({ target: this, value: event });
        }
    }
}
SwtPrettyPrintTextArea.decorators = [
    { type: Component, args: [{
                selector: 'SwtPrettyPrintTextArea',
                template: `
      <div class="swtPrettyPrintTextArea"
      [ngStyle]="{
        'width'         :width,
        'height'        :height,
        'padding-top'   :paddingTop,
        'padding-right' :paddingRight,
        'padding-bottom':paddingBottom,
        'padding-left'  :paddingLeft,
        'margin-top'    :marginTop,
        'margin-right'  :marginRight,
        'margin-bottom' :marginBottom,
        'margin-left'   :marginLeft
      }"
     >
      <ngx-codemirror #ngxCodeMirror [options]= "options" [(ngModel)]="contentValue"
      (ngModelChange)="handleChange($event)"
      (focusChange)="focusChange($event)">
      </ngx-codemirror>
      </div >
      `,
                styles: [`
      textarea {
          resize: none;
                }
    .swtPrettyPrintTextArea {
                    -moz-appearance: textfield-multiline;
                    -webkit-appearance: textarea;
                    background-color: #FFF;
                    overflow: auto;
                    border: 1px solid #A9A9A9;
                    padding: 5px;
                    font-size: 11px;
                    font-weight: bold;
                    font-family: sans-serif;
                }
          `]
            }] }
];
/** @nocollapse */
SwtPrettyPrintTextArea.ctorParameters = () => [
    { type: ElementRef },
    { type: ChangeDetectorRef }
];
SwtPrettyPrintTextArea.propDecorators = {
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    backgroundColor: [{ type: Input }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    id: [{ type: Input, args: ['id',] }],
    toolTip: [{ type: Input, args: ['toolTip',] }],
    enabled: [{ type: Input, args: ['enabled',] }],
    change_: [{ type: Output, args: ['change',] }],
    focusOut_: [{ type: Output, args: ['focusOut',] }],
    focusIn_: [{ type: Output, args: ['focusIn',] }],
    swtPrettyPrintTextArea: [{ type: ViewChild, args: ['swtPrettyPrintTextArea',] }],
    ngxCodeMirror: [{ type: ViewChild, args: ['ngxCodeMirror',] }],
    height: [{ type: Input }],
    width: [{ type: Input }],
    paddingTop: [{ type: Input }],
    paddingRight: [{ type: Input }],
    marginLeft: [{ type: Input }],
    marginBottom: [{ type: Input }],
    marginRight: [{ type: Input }],
    marginTop: [{ type: Input }],
    paddingLeft: [{ type: Input }],
    paddingBottom: [{ type: Input }],
    text: [{ type: Input }],
    editable: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.originalValue;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.onSpyChange;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.onSpyNoChange;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingTop;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingRight;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingBottom;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingLeft;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginTop;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginRight;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginBottom;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginLeft;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._scroll;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._focusIn;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._initialtxt;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.firstCall;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.CUSTOM;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.contentValue;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.backgroundColor;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.tabIndex;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.id;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.toolTip;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.enabled;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.change_;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.focusOut_;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.focusIn_;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.swtPrettyPrintTextArea;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.ngxCodeMirror;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.codemirror;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.listCodeWordsInBD;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.options;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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