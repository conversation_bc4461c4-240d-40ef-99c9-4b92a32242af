/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Subject } from 'rxjs';
import { Injectable } from '@angular/core';
//@dynamic
export class SwtTabNavigatorHandler {
    constructor() { }
    /**
     * this function is used to close tab.
     * @param {?} change
     * @return {?}
     */
    static closeTab(change) {
        this.emitChangeSource.next(change);
    }
}
// Observable string sources
SwtTabNavigatorHandler.emitChangeSource = new Subject();
// Observable string streams
SwtTabNavigatorHandler.Listener = SwtTabNavigatorHandler.emitChangeSource.asObservable();
SwtTabNavigatorHandler.decorators = [
    { type: Injectable }
];
/** @nocollapse */
SwtTabNavigatorHandler.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigatorHandler.emitChangeSource;
    /** @type {?} */
    SwtTabNavigatorHandler.Listener;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXRhYm5hdmlnYXRvci5zZXJ2aWNlLmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L3V0aWxzL3N3dC10YWJuYXZpZ2F0b3Iuc2VydmljZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFnQixPQUFPLEVBQUUsTUFBTSxNQUFNLENBQUM7QUFDN0MsT0FBTyxFQUFFLFVBQVUsRUFBRSxNQUFNLGVBQWUsQ0FBQzs7QUFHM0MsTUFBTSxPQUFPLHNCQUFzQjtJQUUvQixnQkFBZSxDQUFDOzs7Ozs7SUFVVCxNQUFNLENBQUMsUUFBUSxDQUFDLE1BQVc7UUFDOUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUN2QyxDQUFDOzs7QUFUYyx1Q0FBZ0IsR0FBRyxJQUFJLE9BQU8sRUFBTyxDQUFDOztBQUV2QywrQkFBUSxHQUFHLHNCQUFzQixDQUFDLGdCQUFnQixDQUFDLFlBQVksRUFBRSxDQUFDOztZQVJuRixVQUFVOzs7Ozs7Ozs7SUFNUCx3Q0FBcUQ7O0lBRXJELGdDQUFnRiIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE9ic2VydmFibGUgLCAgU3ViamVjdCB9IGZyb20gJ3J4anMnO1xyXG5pbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbi8vQGR5bmFtaWNcclxuQEluamVjdGFibGUoKVxyXG5leHBvcnQgY2xhc3MgU3d0VGFiTmF2aWdhdG9ySGFuZGxlciB7XHJcbiAgICBcclxuICAgIGNvbnN0cnVjdG9yKCkge31cclxuICAgIFxyXG4gICAgLy8gT2JzZXJ2YWJsZSBzdHJpbmcgc291cmNlc1xyXG4gICAgcHJpdmF0ZSBzdGF0aWMgZW1pdENoYW5nZVNvdXJjZSA9IG5ldyBTdWJqZWN0PGFueT4oKTtcclxuICAgIC8vIE9ic2VydmFibGUgc3RyaW5nIHN0cmVhbXNcclxuICAgIHB1YmxpYyBzdGF0aWMgTGlzdGVuZXIgPSBTd3RUYWJOYXZpZ2F0b3JIYW5kbGVyLmVtaXRDaGFuZ2VTb3VyY2UuYXNPYnNlcnZhYmxlKCk7XHJcbiAgICAvKipcclxuICAgICAqIHRoaXMgZnVuY3Rpb24gaXMgdXNlZCB0byBjbG9zZSB0YWIuXHJcbiAgICAgKiBAcGFyYW0gY2hhbmdlXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBzdGF0aWMgY2xvc2VUYWIoY2hhbmdlOiBhbnkpOiB2b2lkIHtcclxuICAgICAgICB0aGlzLmVtaXRDaGFuZ2VTb3VyY2UubmV4dChjaGFuZ2UpO1xyXG4gICAgfVxyXG4gICAgXHJcbn0iXX0=