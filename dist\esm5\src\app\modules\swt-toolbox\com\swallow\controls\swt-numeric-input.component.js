/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
import { FormControl, Validators } from "@angular/forms";
var SwtNumericInput = /** @class */ (function (_super) {
    tslib_1.__extends(SwtNumericInput, _super);
    function SwtNumericInput() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._maximum = -1;
        _this._minimum = -1;
        _this.RESTRICT_CHARS = "0-9";
        return _this;
    }
    Object.defineProperty(SwtNumericInput.prototype, "maximum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maximum;
        },
        //---maximum-----------------------------------------------------------------------------------------------------
        set: 
        //---maximum-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._maximum = Number(value);
            }
            catch (error) {
                console.error('method [ set maximum] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtNumericInput.prototype, "minimum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minimum;
        },
        //---minimum-----------------------------------------------------------------------------------------------------
        set: 
        //---minimum-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._minimum = Number(value);
            }
            catch (error) {
                console.error('method [ set minimum] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtNumericInput.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        //Allows numbers
        this.restrict = this.RESTRICT_CHARS;
        //Add listener
        this.addEventListener("input", this.onValueChange.bind(this), false, 0, true);
    };
    /**
      * Validate the value, whenever its changed
      *
      * @param event: Event
     */
    /**
     * Validate the value, whenever its changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtNumericInput.prototype.onValueChange = /**
     * Validate the value, whenever its changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // Stop the event.
        // event.stopImmediatePropagation();
        this.text = (this.text == "" ? "" : String(this.checkValidValue(this.text)));
        //_onChange(event);
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtNumericInput.prototype.checkValidValue = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (this.maximum != -1 && this.minimum != -1) {
            //If the value exceeds maximum, then returns maximum value
            /** @type {?} */
            var controlMax = new FormControl(value, Validators.max(Number(this.maximum)));
            /** @type {?} */
            var controlMin = new FormControl(value, Validators.min(Number(this.minimum)));
            if (!controlMax.valid) {
                return this.maximum;
            }
            else if (!controlMin.valid) {
                //If the value less than minimum, then returns minimum value
                return this.minimum;
            }
            else {
                return value;
            }
        }
        else {
            return value;
        }
    };
    SwtNumericInput.decorators = [
        { type: Component, args: [{
                    selector: 'SwtNumericInput',
                    template: "\n     <input #textfield\n     popper=\"{{this.toolTipPreviousValue}}\"\n     [popperTrigger]=\"'hover'\"\n     [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n     [popperPlacement]=\"'bottom'\"\n     [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n            type=\"number\"\n            (paste)=\"onPaste($event)\"\n            class=\"textinput \"\n            [class.requiredInput]= \"this.required==true && !this.text  && enabled==true\"\n     />\n  ",
                    styles: ["\n        :host {\n            outline:none;\n        }\n           input {\n               height: 23px;\n               line-height:23px;\n               padding-left: 3px;\n               padding-right: 3px;\n               border: 1px solid #7f9db9; \n               width: 100%;\n               cursor: text;\n               color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/\n           /* margin: 0px 5px 5px 0px;*/\n          }\n\n           input:disabled {\n               color: #E5E5E5;\n           }\n\n          .ng-invalid {\n             border: 2px solid red; \n             color: red;\n          }\n\n          span {\n              color: red;\n              font-weight: bold;\n              font-size: 11px;\n          }\n      "]
                }] }
    ];
    SwtNumericInput.propDecorators = {
        maximum: [{ type: Input, args: ['maximum',] }],
        minimum: [{ type: Input, args: ['minimum',] }]
    };
    return SwtNumericInput;
}(SwtTextInput));
export { SwtNumericInput };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype._maximum;
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype._minimum;
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype.RESTRICT_CHARS;
}
//# sourceMappingURL=data:application/json;base64,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