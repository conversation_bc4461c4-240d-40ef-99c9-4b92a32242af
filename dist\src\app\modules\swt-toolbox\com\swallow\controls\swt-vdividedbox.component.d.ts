import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, EventEmitter, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
export declare class VDividedBox extends Container implements OnInit, AfterViewInit, OnDestroy {
    private elem;
    private commonService;
    DIVIDER_DRAG_COMPLETE: EventEmitter<any>;
    DIVIDER_BUTTON_CLICK: EventEmitter<any>;
    private _height;
    private _width;
    private _resize;
    private _resizeStart;
    private _resizeStop;
    private _heightTop;
    private _heightBottom;
    private TopContent;
    private BottomContent;
    private prevHeightTop;
    private prevHeightBottom;
    heightTopPixel: number;
    heightBottomPixel: number;
    private _dividersAnimation;
    private _extendedDividedBox;
    private _liveDrag;
    private doResize;
    private startDrag;
    private _maxHeightBottom;
    private _minHeightBottom;
    private _maxHeightTop;
    private _minHeightTop;
    maxHeightBottom: any;
    minHeightBottom: any;
    maxHeightTop: any;
    minHeightTop: any;
    vdividedboxContainer: ElementRef;
    panelTop: ElementRef;
    panelBottom: ElementRef;
    private resize_;
    private resizeStart_;
    private resizeStop_;
    splitter: ElementRef;
    private defaultIcon;
    private vdividerClosed;
    private vdividerOpened;
    resize: Function;
    resizeStart: Function;
    resizeStop: Function;
    height: string;
    width: string;
    onButtonClickHandler(): void;
    heightBottom: string;
    dividersAnimation: string;
    extendedDividedBox: any;
    liveDrag: any;
    private forceNoEvent;
    setHeightTopWithoutEvent(value: string): void;
    setHeightBottomWithoutEvent(value: string): void;
    heightTop: string;
    /**
     * constructor
     * @param elem
     * @param commonService
     * @param _rendred
     */
    constructor(elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     * ngAfterViewInit
     */
    ngAfterViewInit(): void;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
