/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
var EmailValidator = /** @class */ (function () {
    function EmailValidator() {
    }
    /**
     *  Convenience method for calling a validator
     *  from within a custom validation function.
     *  Each of the standard Angular validators has a similar convenience method.
     *
     *  @param validator The EmailValidator instance.
     *
     *  @param value A field to validate.
     *
     *  @param baseField Text representation of the subfield
     *  specified in the value parameter.
     *  For example, if the <code>value</code> parameter specifies value.email,
     *  the <code>baseField</code> value is "email".
     *
     *  @return An Array of ValidationResult objects, with one
     *  ValidationResult object for each field examined by the validator.
     *
     *  @see mx.validators.ValidationResult
     */
    /**
     *  Convenience method for calling a validator
     *  from within a custom validation function.
     *  Each of the standard Angular validators has a similar convenience method.
     *
     * @see mx.validators.ValidationResult
     * @param {?} validator The EmailValidator instance.
     *
     * @param {?} value A field to validate.
     *
     * @param {?} baseField Text representation of the subfield
     *  specified in the value parameter.
     *  For example, if the <code>value</code> parameter specifies value.email,
     *  the <code>baseField</code> value is "email".
     *
     * @return {?} An Array of ValidationResult objects, with one
     *  ValidationResult object for each field examined by the validator.
     *
     */
    EmailValidator.validateEmail = /**
     *  Convenience method for calling a validator
     *  from within a custom validation function.
     *  Each of the standard Angular validators has a similar convenience method.
     *
     * @see mx.validators.ValidationResult
     * @param {?} validator The EmailValidator instance.
     *
     * @param {?} value A field to validate.
     *
     * @param {?} baseField Text representation of the subfield
     *  specified in the value parameter.
     *  For example, if the <code>value</code> parameter specifies value.email,
     *  the <code>baseField</code> value is "email".
     *
     * @return {?} An Array of ValidationResult objects, with one
     *  ValidationResult object for each field examined by the validator.
     *
     */
    function (validator, value, baseField) {
        /** @type {?} */
        var results = new Array();
        // Validate the domain name
        // If IP domain, then must follow [x.x.x.x] format
        // Can not have continous periods.
        // Must have at least one period.
        // Must end in a top level domain name that has 2, 3, 4, or 6 characters.
        /** @type {?} */
        var emailStr = String(value);
        /** @type {?} */
        var username = "";
        /** @type {?} */
        var domain = "";
        /** @type {?} */
        var n;
        /** @type {?} */
        var i;
        // Find the @
        /** @type {?} */
        var ampPos = emailStr.indexOf("@");
        if (ampPos == -1) {
            results.push("missingAtSign");
            return results;
        }
        // Make sure there are no extra @s.
        else if (emailStr.indexOf("@", ampPos + 1) != -1) {
            results.push("tooManyAtSigns");
            return results;
        }
        // Separate the address into username and domain.
        username = emailStr.substring(0, ampPos);
        domain = emailStr.substring(ampPos + 1);
        // Validate username has no illegal characters
        // and has at least one character.
        /** @type {?} */
        var usernameLen = username.length;
        if (usernameLen == 0) {
            results.push("missingUsername");
            return results;
        }
        for (i = 0; i < usernameLen; i++) {
            if (this.DISALLOWED_CHARS.indexOf(username.charAt(i)) != -1) {
                results.push("invalidChar");
                return results;
            }
        }
        /** @type {?} */
        var domainLen = domain.length;
        // check for IP address
        if ((domain.charAt(0) == "[") && (domain.charAt(domainLen - 1) == "]")) {
            // Validate IP address
            if (!EmailValidator.isValidIPAddress(domain.substring(1, domainLen - 1))) {
                results.push("invalidIPDomain");
                return results;
            }
        }
        else {
            // Must have at least one period
            /** @type {?} */
            var periodPos = domain.indexOf(".");
            /** @type {?} */
            var nextPeriodPos = 0;
            /** @type {?} */
            var lastDomain = "";
            if (periodPos == -1) {
                results.push("missingPeriodInDomain");
                return results;
            }
            while (true) {
                nextPeriodPos = domain.indexOf(".", periodPos + 1);
                if (nextPeriodPos == -1) {
                    lastDomain = domain.substring(periodPos + 1);
                    if (lastDomain.length != 3 &&
                        lastDomain.length != 2 &&
                        lastDomain.length != 4 &&
                        lastDomain.length != 6) {
                        results.push("invalidDomain");
                        return results;
                    }
                    break;
                }
                else if (nextPeriodPos == periodPos + 1) {
                    results.push("invalidPeriodsInDomain");
                    return results;
                }
                periodPos = nextPeriodPos;
            }
            // Check that there are no illegal characters in the domain.
            for (i = 0; i < domainLen; i++) {
                if (this.DISALLOWED_CHARS.indexOf(domain.charAt(i)) != -1) {
                    results.push("invalidChar");
                    return results;
                }
            }
            // Check that the character immediately after the @ is not a period.
            if (domain.charAt(0) == ".") {
                results.push("invalidDomain");
                return results;
            }
        }
        return results;
    };
    /**
     * Validate a given IP address
     *
     * If IP domain, then must follow [x.x.x.x] format
     * or for IPv6, then follow [x:x:x:x:x:x:x:x] or [x::x:x:x] or some
     * IPv4 hybrid, like [::x.x.x.x] or [0:00::***********]
     */
    /**
     * Validate a given IP address
     *
     * If IP domain, then must follow [x.x.x.x] format
     * or for IPv6, then follow [x:x:x:x:x:x:x:x] or [x::x:x:x] or some
     * IPv4 hybrid, like [::x.x.x.x] or [0:00::***********]
     * @private
     * @param {?} ipAddr
     * @return {?}
     */
    EmailValidator.isValidIPAddress = /**
     * Validate a given IP address
     *
     * If IP domain, then must follow [x.x.x.x] format
     * or for IPv6, then follow [x:x:x:x:x:x:x:x] or [x::x:x:x] or some
     * IPv4 hybrid, like [::x.x.x.x] or [0:00::***********]
     * @private
     * @param {?} ipAddr
     * @return {?}
     */
    function (ipAddr) {
        /** @type {?} */
        var ipArray = new Array();
        /** @type {?} */
        var pos = 0;
        /** @type {?} */
        var newpos = 0;
        /** @type {?} */
        var item;
        /** @type {?} */
        var n;
        /** @type {?} */
        var i;
        // if you have :, you're in IPv6 mode
        // if you have ., you're in IPv4 mode
        if (ipAddr.indexOf(":") != -1) {
            // IPv6
            // validate by splitting on the colons
            // to make it easier, since :: means zeros, 
            // lets rid ourselves of these wildcards in the beginning
            // and then validate normally
            // get rid of unlimited zeros notation so we can parse better
            /** @type {?} */
            var hasUnlimitedZeros = ipAddr.indexOf("::") != -1;
            if (hasUnlimitedZeros) {
                ipAddr = ipAddr.replace(/^::/, "");
                ipAddr = ipAddr.replace(/::/g, ":");
            }
            while (true) {
                newpos = ipAddr.indexOf(":", pos);
                if (newpos != -1) {
                    ipArray.push(ipAddr.substring(pos, newpos));
                }
                else {
                    ipArray.push(ipAddr.substring(pos));
                    break;
                }
                pos = newpos + 1;
            }
            n = ipArray.length;
            /** @type {?} */
            var lastIsV4 = ipArray[n - 1].indexOf(".") != -1;
            if (lastIsV4) {
                // if no wildcards, length must be 7
                // always, never more than 7
                if ((ipArray.length != 7 && !hasUnlimitedZeros) || (ipArray.length > 7))
                    return false;
                for (i = 0; i < n; i++) {
                    if (i == n - 1) {
                        // IPv4 part...
                        return EmailValidator.isValidIPAddress(ipArray[i]);
                    }
                    item = parseInt(ipArray[i], 16);
                    if (item != 0)
                        return false;
                }
            }
            else {
                // if no wildcards, length must be 8
                // always, never more than 8
                if ((ipArray.length != 8 && !hasUnlimitedZeros) || (ipArray.length > 8))
                    return false;
                for (i = 0; i < n; i++) {
                    item = parseInt(ipArray[i], 16);
                    if (isNaN(item) || item < 0 || item > 0xFFFF)
                        return false;
                }
            }
            return true;
        }
        if (ipAddr.indexOf(".") != -1) {
            // IPv4
            // validate by splling on the periods
            while (true) {
                newpos = ipAddr.indexOf(".", pos);
                if (newpos != -1) {
                    ipArray.push(ipAddr.substring(pos, newpos));
                }
                else {
                    ipArray.push(ipAddr.substring(pos));
                    break;
                }
                pos = newpos + 1;
            }
            if (ipArray.length != 4)
                return false;
            n = ipArray.length;
            for (i = 0; i < n; i++) {
                item = Number(ipArray[i]);
                if (isNaN(item) || item < 0 || item > 255)
                    return false;
            }
            return true;
        }
        return false;
    };
    EmailValidator.DISALLOWED_CHARS = "()<>,;:\\\"[] `~!#$%^&*+={}|/?'";
    EmailValidator.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    EmailValidator.ctorParameters = function () { return []; };
    return EmailValidator;
}());
export { EmailValidator };
if (false) {
    /**
     * @type {?}
     * @private
     */
    EmailValidator.DISALLOWED_CHARS;
    /**
     *  Storage for the missingAtSignError property.
     * @type {?}
     * @private
     */
    EmailValidator.prototype._missingAtSignError;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZW1haWwtdmFsaWRhdG9yLnNlcnZpY2UuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvdXRpbHMvZW1haWwtdmFsaWRhdG9yLnNlcnZpY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE9BQU8sRUFBRSxVQUFVLEVBQUUsTUFBTSxlQUFlLENBQUM7O0FBRzNDO0lBUUU7SUFBZ0IsQ0FBQztJQUVqQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O09Ba0JHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUNZLDRCQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBQTNCLFVBQTRCLFNBQXdCLEVBQUUsS0FBWSxFQUFFLFNBQWdCOztZQUU3RSxPQUFPLEdBQUcsSUFBSSxLQUFLLEVBQUU7Ozs7Ozs7WUFRckIsUUFBUSxHQUFVLE1BQU0sQ0FBQyxLQUFLLENBQUM7O1lBQy9CLFFBQVEsR0FBVSxFQUFFOztZQUNwQixNQUFNLEdBQVUsRUFBRTs7WUFDbEIsQ0FBUTs7WUFDUixDQUFROzs7WUFHUixNQUFNLEdBQVUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUM7UUFDekMsSUFBSSxNQUFNLElBQUksQ0FBQyxDQUFDLEVBQ2hCO1lBQ0ksT0FBTyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUM5QixPQUFPLE9BQU8sQ0FBQztTQUNsQjtRQUNELG1DQUFtQzthQUM5QixJQUFJLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsRUFDaEQ7WUFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFDL0IsT0FBTyxPQUFPLENBQUM7U0FDbEI7UUFFRCxpREFBaUQ7UUFDakQsUUFBUSxHQUFHLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sR0FBRyxRQUFRLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQzs7OztZQUlwQyxXQUFXLEdBQVUsUUFBUSxDQUFDLE1BQU07UUFDeEMsSUFBSSxXQUFXLElBQUksQ0FBQyxFQUNwQjtZQUNJLE9BQU8sQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUNoQyxPQUFPLE9BQU8sQ0FBQztTQUNsQjtRQUVELEtBQUssQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsV0FBVyxFQUFFLENBQUMsRUFBRSxFQUNoQztZQUNJLElBQUksSUFBSSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQzNEO2dCQUNJLE9BQU8sQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7Z0JBQzVCLE9BQU8sT0FBTyxDQUFDO2FBQ2xCO1NBQ0o7O1lBRUcsU0FBUyxHQUFVLE1BQU0sQ0FBQyxNQUFNO1FBRXBDLHVCQUF1QjtRQUN2QixJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxHQUFHLENBQUMsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxFQUN0RTtZQUNJLHNCQUFzQjtZQUN0QixJQUFJLENBQUMsY0FBYyxDQUFDLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUN4RTtnQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7Z0JBQ2hDLE9BQU8sT0FBTyxDQUFDO2FBQ2xCO1NBQ0o7YUFFRDs7O2dCQUVRLFNBQVMsR0FBVSxNQUFNLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQzs7Z0JBQ3RDLGFBQWEsR0FBVSxDQUFDOztnQkFDeEIsVUFBVSxHQUFVLEVBQUU7WUFFMUIsSUFBSSxTQUFTLElBQUksQ0FBQyxDQUFDLEVBQ25CO2dCQUNJLE9BQU8sQ0FBQyxJQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQztnQkFDdEMsT0FBTyxPQUFPLENBQUM7YUFDbEI7WUFFRCxPQUFPLElBQUksRUFDWDtnQkFDSSxhQUFhLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsU0FBUyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNuRCxJQUFJLGFBQWEsSUFBSSxDQUFDLENBQUMsRUFDdkI7b0JBQ0ksVUFBVSxHQUFHLE1BQU0sQ0FBQyxTQUFTLENBQUMsU0FBUyxHQUFHLENBQUMsQ0FBQyxDQUFDO29CQUM3QyxJQUFJLFVBQVUsQ0FBQyxNQUFNLElBQUksQ0FBQzt3QkFDdEIsVUFBVSxDQUFDLE1BQU0sSUFBSSxDQUFDO3dCQUN0QixVQUFVLENBQUMsTUFBTSxJQUFJLENBQUM7d0JBQ3RCLFVBQVUsQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUMxQjt3QkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO3dCQUM5QixPQUFPLE9BQU8sQ0FBQztxQkFDbEI7b0JBQ0QsTUFBTTtpQkFDVDtxQkFDSSxJQUFJLGFBQWEsSUFBSSxTQUFTLEdBQUcsQ0FBQyxFQUN2QztvQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLENBQUM7b0JBQ3ZDLE9BQU8sT0FBTyxDQUFDO2lCQUNsQjtnQkFDRCxTQUFTLEdBQUcsYUFBYSxDQUFDO2FBQzdCO1lBRUQsNERBQTREO1lBQzVELEtBQUssQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsU0FBUyxFQUFFLENBQUMsRUFBRSxFQUM5QjtnQkFDSSxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUN6RDtvQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO29CQUM1QixPQUFPLE9BQU8sQ0FBQztpQkFDbEI7YUFDSjtZQUVELG9FQUFvRTtZQUNwRSxJQUFJLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksR0FBRyxFQUMzQjtnQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO2dCQUM5QixPQUFPLE9BQU8sQ0FBQzthQUNsQjtTQUNKO1FBRUQsT0FBTyxPQUFPLENBQUM7SUFDbkIsQ0FBQztJQUVBOzs7Ozs7T0FNRzs7Ozs7Ozs7Ozs7SUFDWSwrQkFBZ0I7Ozs7Ozs7Ozs7SUFBL0IsVUFBZ0MsTUFBYTs7WUFFckMsT0FBTyxHQUFJLElBQUksS0FBSyxFQUFFOztZQUN0QixHQUFHLEdBQVUsQ0FBQzs7WUFDZCxNQUFNLEdBQVUsQ0FBQzs7WUFDakIsSUFBVzs7WUFDWCxDQUFROztZQUNSLENBQVE7UUFFWixxQ0FBcUM7UUFDckMscUNBQXFDO1FBRXJDLElBQUksTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsRUFDN0I7Ozs7Ozs7O2dCQVNRLGlCQUFpQixHQUFXLE1BQU0sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzFELElBQUksaUJBQWlCLEVBQ3JCO2dCQUNJLE1BQU0sR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsQ0FBQztnQkFDbkMsTUFBTSxHQUFHLE1BQU0sQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDO2FBQ3ZDO1lBRUQsT0FBTyxJQUFJLEVBQ1g7Z0JBQ0ksTUFBTSxHQUFHLE1BQU0sQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDO2dCQUNsQyxJQUFJLE1BQU0sSUFBSSxDQUFDLENBQUMsRUFDaEI7b0JBQ0ksT0FBTyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBQyxNQUFNLENBQUMsQ0FBQyxDQUFDO2lCQUM5QztxQkFFRDtvQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQztvQkFDcEMsTUFBTTtpQkFDVDtnQkFDRCxHQUFHLEdBQUcsTUFBTSxHQUFHLENBQUMsQ0FBQzthQUNwQjtZQUVELENBQUMsR0FBRyxPQUFPLENBQUMsTUFBTSxDQUFDOztnQkFFYixRQUFRLEdBQVcsT0FBTyxDQUFDLENBQUMsR0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1lBRXhELElBQUksUUFBUSxFQUNaO2dCQUNJLG9DQUFvQztnQkFDcEMsNEJBQTRCO2dCQUM1QixJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7b0JBQ25FLE9BQU8sS0FBSyxDQUFDO2dCQUVqQixLQUFLLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFDdEI7b0JBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFDLENBQUMsRUFDWjt3QkFDSSxlQUFlO3dCQUNmLE9BQU8sY0FBYyxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO3FCQUN0RDtvQkFFRCxJQUFJLEdBQUcsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQztvQkFFaEMsSUFBSSxJQUFJLElBQUksQ0FBQzt3QkFDVCxPQUFPLEtBQUssQ0FBQztpQkFDcEI7YUFDSjtpQkFFRDtnQkFFSSxvQ0FBb0M7Z0JBQ3BDLDRCQUE0QjtnQkFDNUIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO29CQUNuRSxPQUFPLEtBQUssQ0FBQztnQkFFakIsS0FBSyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQ3RCO29CQUNJLElBQUksR0FBRyxRQUFRLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO29CQUVoQyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxJQUFJLEdBQUcsQ0FBQyxJQUFJLElBQUksR0FBRyxNQUFNO3dCQUN4QyxPQUFPLEtBQUssQ0FBQztpQkFDcEI7YUFDSjtZQUVELE9BQU8sSUFBSSxDQUFDO1NBQ2Y7UUFFRCxJQUFJLE1BQU0sQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQzdCO1lBQ0ksT0FBTztZQUVQLHFDQUFxQztZQUNyQyxPQUFPLElBQUksRUFDWDtnQkFDSSxNQUFNLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBQ2xDLElBQUksTUFBTSxJQUFJLENBQUMsQ0FBQyxFQUNoQjtvQkFDSSxPQUFPLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsR0FBRyxFQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7aUJBQzlDO3FCQUVEO29CQUNJLE9BQU8sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO29CQUNwQyxNQUFNO2lCQUNUO2dCQUNELEdBQUcsR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDO2FBQ3BCO1lBRUQsSUFBSSxPQUFPLENBQUMsTUFBTSxJQUFJLENBQUM7Z0JBQ25CLE9BQU8sS0FBSyxDQUFDO1lBRWpCLENBQUMsR0FBRyxPQUFPLENBQUMsTUFBTSxDQUFDO1lBQ25CLEtBQUssQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUN0QjtnQkFDSSxJQUFJLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUMxQixJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxJQUFJLEdBQUcsQ0FBQyxJQUFJLElBQUksR0FBRyxHQUFHO29CQUNyQyxPQUFPLEtBQUssQ0FBQzthQUNwQjtZQUVELE9BQU8sSUFBSSxDQUFDO1NBQ2Y7UUFFRCxPQUFPLEtBQUssQ0FBQztJQUNqQixDQUFDO0lBblJhLCtCQUFnQixHQUFVLGlDQUFpQyxDQUFDOztnQkFQNUUsVUFBVTs7OztJQTJSWCxxQkFBQztDQUFBLEFBM1JELElBMlJDO1NBMVJZLGNBQWM7Ozs7OztJQU16QixnQ0FBMkU7Ozs7OztJQUQzRSw2Q0FBbUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcblxyXG4vL0BkeW5hbWljXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIEVtYWlsVmFsaWRhdG9yIHtcclxuICAgIFxyXG4gLyoqXHJcbiAqICBTdG9yYWdlIGZvciB0aGUgbWlzc2luZ0F0U2lnbkVycm9yIHByb3BlcnR5LlxyXG4gKi9cclxuICBwcml2YXRlIF9taXNzaW5nQXRTaWduRXJyb3I6c3RyaW5nO1xyXG4gIHByaXZhdGUgc3RhdGljIERJU0FMTE9XRURfQ0hBUlM6c3RyaW5nID0gXCIoKTw+LDs6XFxcXFxcXCJbXSBgfiEjJCVeJiorPXt9fC8/J1wiO1xyXG4gIGNvbnN0cnVjdG9yKCkgeyB9XHJcblxyXG4gIC8qKlxyXG4gICAqICBDb252ZW5pZW5jZSBtZXRob2QgZm9yIGNhbGxpbmcgYSB2YWxpZGF0b3JcclxuICAgKiAgZnJvbSB3aXRoaW4gYSBjdXN0b20gdmFsaWRhdGlvbiBmdW5jdGlvbi5cclxuICAgKiAgRWFjaCBvZiB0aGUgc3RhbmRhcmQgQW5ndWxhciB2YWxpZGF0b3JzIGhhcyBhIHNpbWlsYXIgY29udmVuaWVuY2UgbWV0aG9kLlxyXG4gICAqXHJcbiAgICogIEBwYXJhbSB2YWxpZGF0b3IgVGhlIEVtYWlsVmFsaWRhdG9yIGluc3RhbmNlLlxyXG4gICAqXHJcbiAgICogIEBwYXJhbSB2YWx1ZSBBIGZpZWxkIHRvIHZhbGlkYXRlLlxyXG4gICAqXHJcbiAgICogIEBwYXJhbSBiYXNlRmllbGQgVGV4dCByZXByZXNlbnRhdGlvbiBvZiB0aGUgc3ViZmllbGRcclxuICAgKiAgc3BlY2lmaWVkIGluIHRoZSB2YWx1ZSBwYXJhbWV0ZXIuXHJcbiAgICogIEZvciBleGFtcGxlLCBpZiB0aGUgPGNvZGU+dmFsdWU8L2NvZGU+IHBhcmFtZXRlciBzcGVjaWZpZXMgdmFsdWUuZW1haWwsXHJcbiAgICogIHRoZSA8Y29kZT5iYXNlRmllbGQ8L2NvZGU+IHZhbHVlIGlzIFwiZW1haWxcIi5cclxuICAgKlxyXG4gICAqICBAcmV0dXJuIEFuIEFycmF5IG9mIFZhbGlkYXRpb25SZXN1bHQgb2JqZWN0cywgd2l0aCBvbmVcclxuICAgKiAgVmFsaWRhdGlvblJlc3VsdCBvYmplY3QgZm9yIGVhY2ggZmllbGQgZXhhbWluZWQgYnkgdGhlIHZhbGlkYXRvci4gXHJcbiAgICpcclxuICAgKiAgQHNlZSBteC52YWxpZGF0b3JzLlZhbGlkYXRpb25SZXN1bHQgIFxyXG4gICAqL1xyXG4gICBwdWJsaWMgc3RhdGljIHZhbGlkYXRlRW1haWwodmFsaWRhdG9yOkVtYWlsVmFsaWRhdG9yLCB2YWx1ZTpPYmplY3QsIGJhc2VGaWVsZDpzdHJpbmcpOmFueVtdXHJcbiAgIHtcclxuICAgICAgdmFyIHJlc3VsdHMgPSBuZXcgQXJyYXkoKTtcclxuICBcclxuICAgICAgLy8gVmFsaWRhdGUgdGhlIGRvbWFpbiBuYW1lXHJcbiAgICAgIC8vIElmIElQIGRvbWFpbiwgdGhlbiBtdXN0IGZvbGxvdyBbeC54LngueF0gZm9ybWF0XHJcbiAgICAgIC8vIENhbiBub3QgaGF2ZSBjb250aW5vdXMgcGVyaW9kcy5cclxuICAgICAgLy8gTXVzdCBoYXZlIGF0IGxlYXN0IG9uZSBwZXJpb2QuXHJcbiAgICAgIC8vIE11c3QgZW5kIGluIGEgdG9wIGxldmVsIGRvbWFpbiBuYW1lIHRoYXQgaGFzIDIsIDMsIDQsIG9yIDYgY2hhcmFjdGVycy5cclxuXHJcbiAgICAgIHZhciBlbWFpbFN0cjpzdHJpbmcgPSBTdHJpbmcodmFsdWUpO1xyXG4gICAgICB2YXIgdXNlcm5hbWU6c3RyaW5nID0gXCJcIjtcclxuICAgICAgdmFyIGRvbWFpbjpzdHJpbmcgPSBcIlwiO1xyXG4gICAgICB2YXIgbjpudW1iZXI7XHJcbiAgICAgIHZhciBpOm51bWJlcjtcclxuXHJcbiAgICAgIC8vIEZpbmQgdGhlIEBcclxuICAgICAgdmFyIGFtcFBvczpudW1iZXIgPSBlbWFpbFN0ci5pbmRleE9mKFwiQFwiKTtcclxuICAgICAgaWYgKGFtcFBvcyA9PSAtMSlcclxuICAgICAge1xyXG4gICAgICAgICAgcmVzdWx0cy5wdXNoKFwibWlzc2luZ0F0U2lnblwiKTtcclxuICAgICAgICAgIHJldHVybiByZXN1bHRzO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIE1ha2Ugc3VyZSB0aGVyZSBhcmUgbm8gZXh0cmEgQHMuXHJcbiAgICAgIGVsc2UgaWYgKGVtYWlsU3RyLmluZGV4T2YoXCJAXCIsIGFtcFBvcyArIDEpICE9IC0xKSBcclxuICAgICAgeyBcclxuICAgICAgICAgIHJlc3VsdHMucHVzaChcInRvb01hbnlBdFNpZ25zXCIpO1xyXG4gICAgICAgICAgcmV0dXJuIHJlc3VsdHM7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFNlcGFyYXRlIHRoZSBhZGRyZXNzIGludG8gdXNlcm5hbWUgYW5kIGRvbWFpbi5cclxuICAgICAgdXNlcm5hbWUgPSBlbWFpbFN0ci5zdWJzdHJpbmcoMCwgYW1wUG9zKTtcclxuICAgICAgZG9tYWluID0gZW1haWxTdHIuc3Vic3RyaW5nKGFtcFBvcyArIDEpO1xyXG5cclxuICAgICAgLy8gVmFsaWRhdGUgdXNlcm5hbWUgaGFzIG5vIGlsbGVnYWwgY2hhcmFjdGVyc1xyXG4gICAgICAvLyBhbmQgaGFzIGF0IGxlYXN0IG9uZSBjaGFyYWN0ZXIuXHJcbiAgICAgIHZhciB1c2VybmFtZUxlbjpudW1iZXIgPSB1c2VybmFtZS5sZW5ndGg7XHJcbiAgICAgIGlmICh1c2VybmFtZUxlbiA9PSAwKVxyXG4gICAgICB7XHJcbiAgICAgICAgICByZXN1bHRzLnB1c2goXCJtaXNzaW5nVXNlcm5hbWVcIik7XHJcbiAgICAgICAgICByZXR1cm4gcmVzdWx0cztcclxuICAgICAgfVxyXG5cclxuICAgICAgZm9yIChpID0gMDsgaSA8IHVzZXJuYW1lTGVuOyBpKyspXHJcbiAgICAgIHtcclxuICAgICAgICAgIGlmICh0aGlzLkRJU0FMTE9XRURfQ0hBUlMuaW5kZXhPZih1c2VybmFtZS5jaGFyQXQoaSkpICE9IC0xKVxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHJlc3VsdHMucHVzaChcImludmFsaWRDaGFyXCIpO1xyXG4gICAgICAgICAgICAgIHJldHVybiByZXN1bHRzO1xyXG4gICAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICB2YXIgZG9tYWluTGVuOm51bWJlciA9IGRvbWFpbi5sZW5ndGg7XHJcbiAgICAgIFxyXG4gICAgICAvLyBjaGVjayBmb3IgSVAgYWRkcmVzc1xyXG4gICAgICBpZiAoKGRvbWFpbi5jaGFyQXQoMCkgPT0gXCJbXCIpICYmIChkb21haW4uY2hhckF0KGRvbWFpbkxlbiAtIDEpID09IFwiXVwiKSlcclxuICAgICAge1xyXG4gICAgICAgICAgLy8gVmFsaWRhdGUgSVAgYWRkcmVzc1xyXG4gICAgICAgICAgaWYgKCFFbWFpbFZhbGlkYXRvci5pc1ZhbGlkSVBBZGRyZXNzKGRvbWFpbi5zdWJzdHJpbmcoMSwgZG9tYWluTGVuIC0gMSkpKVxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHJlc3VsdHMucHVzaChcImludmFsaWRJUERvbWFpblwiKTtcclxuICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0cztcclxuICAgICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBlbHNlXHJcbiAgICAgIHtcclxuICAgICAgICAgIC8vIE11c3QgaGF2ZSBhdCBsZWFzdCBvbmUgcGVyaW9kXHJcbiAgICAgICAgICB2YXIgcGVyaW9kUG9zOm51bWJlciA9IGRvbWFpbi5pbmRleE9mKFwiLlwiKTtcclxuICAgICAgICAgIHZhciBuZXh0UGVyaW9kUG9zOm51bWJlciA9IDA7XHJcbiAgICAgICAgICB2YXIgbGFzdERvbWFpbjpzdHJpbmcgPSBcIlwiO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAocGVyaW9kUG9zID09IC0xKVxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHJlc3VsdHMucHVzaChcIm1pc3NpbmdQZXJpb2RJbkRvbWFpblwiKTtcclxuICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0cztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICB3aGlsZSAodHJ1ZSlcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBuZXh0UGVyaW9kUG9zID0gZG9tYWluLmluZGV4T2YoXCIuXCIsIHBlcmlvZFBvcyArIDEpO1xyXG4gICAgICAgICAgICAgIGlmIChuZXh0UGVyaW9kUG9zID09IC0xKVxyXG4gICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgbGFzdERvbWFpbiA9IGRvbWFpbi5zdWJzdHJpbmcocGVyaW9kUG9zICsgMSk7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChsYXN0RG9tYWluLmxlbmd0aCAhPSAzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBsYXN0RG9tYWluLmxlbmd0aCAhPSAyICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBsYXN0RG9tYWluLmxlbmd0aCAhPSA0ICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBsYXN0RG9tYWluLmxlbmd0aCAhPSA2KVxyXG4gICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXN1bHRzLnB1c2goXCJpbnZhbGlkRG9tYWluXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdHM7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGVsc2UgaWYgKG5leHRQZXJpb2RQb3MgPT0gcGVyaW9kUG9zICsgMSlcclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaChcImludmFsaWRQZXJpb2RzSW5Eb21haW5cIik7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHRzO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBwZXJpb2RQb3MgPSBuZXh0UGVyaW9kUG9zO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIENoZWNrIHRoYXQgdGhlcmUgYXJlIG5vIGlsbGVnYWwgY2hhcmFjdGVycyBpbiB0aGUgZG9tYWluLlxyXG4gICAgICAgICAgZm9yIChpID0gMDsgaSA8IGRvbWFpbkxlbjsgaSsrKVxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlmICh0aGlzLkRJU0FMTE9XRURfQ0hBUlMuaW5kZXhPZihkb21haW4uY2hhckF0KGkpKSAhPSAtMSlcclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaChcImludmFsaWRDaGFyXCIpO1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0cztcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIENoZWNrIHRoYXQgdGhlIGNoYXJhY3RlciBpbW1lZGlhdGVseSBhZnRlciB0aGUgQCBpcyBub3QgYSBwZXJpb2QuXHJcbiAgICAgICAgICBpZiAoZG9tYWluLmNoYXJBdCgwKSA9PSBcIi5cIilcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgICByZXN1bHRzLnB1c2goXCJpbnZhbGlkRG9tYWluXCIpO1xyXG4gICAgICAgICAgICAgIHJldHVybiByZXN1bHRzO1xyXG4gICAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gcmVzdWx0cztcclxuICB9XHJcblxyXG4gICAvKipcclxuICAgICogVmFsaWRhdGUgYSBnaXZlbiBJUCBhZGRyZXNzXHJcbiAgICAqIFxyXG4gICAgKiBJZiBJUCBkb21haW4sIHRoZW4gbXVzdCBmb2xsb3cgW3gueC54LnhdIGZvcm1hdFxyXG4gICAgKiBvciBmb3IgSVB2NiwgdGhlbiBmb2xsb3cgW3g6eDp4Ong6eDp4Ong6eF0gb3IgW3g6Ong6eDp4XSBvciBzb21lXHJcbiAgICAqIElQdjQgaHlicmlkLCBsaWtlIFs6OngueC54LnhdIG9yIFswOjAwOjoxOTIuMTY4LjAuMV1cclxuICAgICovIFxyXG4gICBwcml2YXRlIHN0YXRpYyBpc1ZhbGlkSVBBZGRyZXNzKGlwQWRkcjpzdHJpbmcpOkJvb2xlYW5cclxuICAge1xyXG4gICAgICAgdmFyIGlwQXJyYXkgPSAgbmV3IEFycmF5KCk7XHJcbiAgICAgICB2YXIgcG9zOm51bWJlciA9IDA7XHJcbiAgICAgICB2YXIgbmV3cG9zOm51bWJlciA9IDA7XHJcbiAgICAgICB2YXIgaXRlbTpudW1iZXI7XHJcbiAgICAgICB2YXIgbjpudW1iZXI7XHJcbiAgICAgICB2YXIgaTpudW1iZXI7XHJcbiAgICAgICBcclxuICAgICAgIC8vIGlmIHlvdSBoYXZlIDosIHlvdSdyZSBpbiBJUHY2IG1vZGVcclxuICAgICAgIC8vIGlmIHlvdSBoYXZlIC4sIHlvdSdyZSBpbiBJUHY0IG1vZGVcclxuICAgICAgIFxyXG4gICAgICAgaWYgKGlwQWRkci5pbmRleE9mKFwiOlwiKSAhPSAtMSlcclxuICAgICAgIHtcclxuICAgICAgICAgICAvLyBJUHY2XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgLy8gdmFsaWRhdGUgYnkgc3BsaXR0aW5nIG9uIHRoZSBjb2xvbnNcclxuICAgICAgICAgICAvLyB0byBtYWtlIGl0IGVhc2llciwgc2luY2UgOjogbWVhbnMgemVyb3MsIFxyXG4gICAgICAgICAgIC8vIGxldHMgcmlkIG91cnNlbHZlcyBvZiB0aGVzZSB3aWxkY2FyZHMgaW4gdGhlIGJlZ2lubmluZ1xyXG4gICAgICAgICAgIC8vIGFuZCB0aGVuIHZhbGlkYXRlIG5vcm1hbGx5XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgLy8gZ2V0IHJpZCBvZiB1bmxpbWl0ZWQgemVyb3Mgbm90YXRpb24gc28gd2UgY2FuIHBhcnNlIGJldHRlclxyXG4gICAgICAgICAgIHZhciBoYXNVbmxpbWl0ZWRaZXJvczpCb29sZWFuID0gaXBBZGRyLmluZGV4T2YoXCI6OlwiKSAhPSAtMTtcclxuICAgICAgICAgICBpZiAoaGFzVW5saW1pdGVkWmVyb3MpXHJcbiAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICBpcEFkZHIgPSBpcEFkZHIucmVwbGFjZSgvXjo6LywgXCJcIik7XHJcbiAgICAgICAgICAgICAgIGlwQWRkciA9IGlwQWRkci5yZXBsYWNlKC86Oi9nLCBcIjpcIik7XHJcbiAgICAgICAgICAgfVxyXG4gICAgICAgICAgIFxyXG4gICAgICAgICAgIHdoaWxlICh0cnVlKVxyXG4gICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgbmV3cG9zID0gaXBBZGRyLmluZGV4T2YoXCI6XCIsIHBvcyk7XHJcbiAgICAgICAgICAgICAgIGlmIChuZXdwb3MgIT0gLTEpXHJcbiAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgIGlwQXJyYXkucHVzaChpcEFkZHIuc3Vic3RyaW5nKHBvcyxuZXdwb3MpKTtcclxuICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICBlbHNlXHJcbiAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgIGlwQXJyYXkucHVzaChpcEFkZHIuc3Vic3RyaW5nKHBvcykpO1xyXG4gICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgcG9zID0gbmV3cG9zICsgMTtcclxuICAgICAgICAgICB9XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgbiA9IGlwQXJyYXkubGVuZ3RoO1xyXG4gICAgICAgICAgIFxyXG4gICAgICAgICAgIGNvbnN0IGxhc3RJc1Y0OkJvb2xlYW4gPSBpcEFycmF5W24tMV0uaW5kZXhPZihcIi5cIikgIT0gLTE7XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgaWYgKGxhc3RJc1Y0KVxyXG4gICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgLy8gaWYgbm8gd2lsZGNhcmRzLCBsZW5ndGggbXVzdCBiZSA3XHJcbiAgICAgICAgICAgICAgIC8vIGFsd2F5cywgbmV2ZXIgbW9yZSB0aGFuIDdcclxuICAgICAgICAgICAgICAgaWYgKChpcEFycmF5Lmxlbmd0aCAhPSA3ICYmICFoYXNVbmxpbWl0ZWRaZXJvcykgfHwgKGlwQXJyYXkubGVuZ3RoID4gNykpXHJcbiAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgIFxyXG4gICAgICAgICAgICAgICBmb3IgKGkgPSAwOyBpIDwgbjsgaSsrKVxyXG4gICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICBpZiAoaSA9PSBuLTEpXHJcbiAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgLy8gSVB2NCBwYXJ0Li4uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEVtYWlsVmFsaWRhdG9yLmlzVmFsaWRJUEFkZHJlc3MoaXBBcnJheVtpXSk7XHJcbiAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgIGl0ZW0gPSBwYXJzZUludChpcEFycmF5W2ldLCAxNik7XHJcbiAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgIGlmIChpdGVtICE9IDApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgfVxyXG4gICAgICAgICAgIGVsc2VcclxuICAgICAgICAgICB7XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgIC8vIGlmIG5vIHdpbGRjYXJkcywgbGVuZ3RoIG11c3QgYmUgOFxyXG4gICAgICAgICAgICAgICAvLyBhbHdheXMsIG5ldmVyIG1vcmUgdGhhbiA4XHJcbiAgICAgICAgICAgICAgIGlmICgoaXBBcnJheS5sZW5ndGggIT0gOCAmJiAhaGFzVW5saW1pdGVkWmVyb3MpIHx8IChpcEFycmF5Lmxlbmd0aCA+IDgpKVxyXG4gICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgZm9yIChpID0gMDsgaSA8IG47IGkrKylcclxuICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgaXRlbSA9IHBhcnNlSW50KGlwQXJyYXlbaV0sIDE2KTtcclxuICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgaWYgKGlzTmFOKGl0ZW0pIHx8IGl0ZW0gPCAwIHx8IGl0ZW0gPiAweEZGRkYpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgfVxyXG4gICAgICAgICAgIFxyXG4gICAgICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgICAgfVxyXG4gICAgICAgICAgIFxyXG4gICAgICAgaWYgKGlwQWRkci5pbmRleE9mKFwiLlwiKSAhPSAtMSlcclxuICAgICAgIHtcclxuICAgICAgICAgICAvLyBJUHY0XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgLy8gdmFsaWRhdGUgYnkgc3BsbGluZyBvbiB0aGUgcGVyaW9kc1xyXG4gICAgICAgICAgIHdoaWxlICh0cnVlKVxyXG4gICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgbmV3cG9zID0gaXBBZGRyLmluZGV4T2YoXCIuXCIsIHBvcyk7XHJcbiAgICAgICAgICAgICAgIGlmIChuZXdwb3MgIT0gLTEpXHJcbiAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgIGlwQXJyYXkucHVzaChpcEFkZHIuc3Vic3RyaW5nKHBvcyxuZXdwb3MpKTtcclxuICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICBlbHNlXHJcbiAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgIGlwQXJyYXkucHVzaChpcEFkZHIuc3Vic3RyaW5nKHBvcykpO1xyXG4gICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgcG9zID0gbmV3cG9zICsgMTtcclxuICAgICAgICAgICB9XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgaWYgKGlwQXJyYXkubGVuZ3RoICE9IDQpXHJcbiAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuXHJcbiAgICAgICAgICAgbiA9IGlwQXJyYXkubGVuZ3RoO1xyXG4gICAgICAgICAgIGZvciAoaSA9IDA7IGkgPCBuOyBpKyspXHJcbiAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICBpdGVtID0gTnVtYmVyKGlwQXJyYXlbaV0pO1xyXG4gICAgICAgICAgICAgICBpZiAoaXNOYU4oaXRlbSkgfHwgaXRlbSA8IDAgfHwgaXRlbSA+IDI1NSlcclxuICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICAgICB9XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgICB9XHJcbiAgICAgICBcclxuICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgfVxyXG59XHJcbiJdfQ==