/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { SwtLabel } from "./swt-label.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef } from "@angular/core";
;
/** @type {?} */
const $ = require('jquery');
/**
 * <AUTHOR>
 * @Reviewed by Rihab.JABALLAH on Dec-2019
 * @version 1.0.0
 * @date 07/05/2018
 *
 * */
export class SwtText extends SwtLabel {
    /**
     * Constructor
     * @param {?} elem_
     * @param {?} commonService_
     */
    constructor(elem_, commonService_) {
        super(elem_, commonService_);
        this.elem_ = elem_;
        this.commonService_ = commonService_;
    }
}
SwtText.decorators = [
    { type: Component, args: [{
                selector: 'SwtText',
                template: `
        <span selector="SwtText"
               class="ellipsisEnabled"
               #labelDOM>
        </span>
  `,
                styles: [`
       :host {
          display: block;
           outline:none;
       }
       span {
         display: inline-block;
       }
      .ellipsisEnabled{
        vertical-align: bottom;
        text-overflow: ellipsis; 
        overflow: hidden; 
        white-space: nowrap;
     }
  `]
            }] }
];
/** @nocollapse */
SwtText.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtText.prototype.elem_;
    /**
     * @type {?}
     * @private
     */
    SwtText.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,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