import { OnInit, AfterViewInit, OnChang<PERSON>, EventEmitter, ElementRef } from '@angular/core';
export declare class SwtStepper implements OnInit, OnChanges, AfterViewInit {
    private elem;
    originalValue: any;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    private _toolTipPreviousObject;
    private _keyDown;
    private _keyUp;
    private _focus;
    private _focusOut;
    private _change;
    private _enabled;
    private _editable;
    private spinnerObject;
    private spinnerInut;
    private _text;
    private _width;
    private _required;
    private selector;
    private _visible;
    regex: any;
    pattern: any;
    restrict: any;
    minimum: any;
    maximum: any;
    private keyup_;
    private keyDown_;
    private focus_;
    private focusOut_;
    private change_;
    styleName: any;
    toolTip: any;
    maxChars: any;
    tabindex: any;
    stepSize: any;
    toolTipPreviousValue: any;
    width: string;
    height: string;
    id: any;
    value: number;
    enabled: boolean;
    visible: boolean;
    private firstCall;
    text: number;
    editable: boolean;
    required: any;
    private stepperDOM;
    constructor(elem: ElementRef);
    ngOnInit(): void;
    ngAfterViewInit(): void;
    private updateText;
    ngOnChanges(changes: any): void;
    /**
     * this function is fired on keyup event.
     * @param event
     */
    onkeyup(event: any): void;
    /**
     * this function is fired on keyup event.
     * @param event
     */
    onkeyDown(event: any): void;
    /**
     * this function is fired on focus event.
     * @param event
     */
    onFocus(event: any): void;
    /**
     * this function is fired on focusOut event.
     * @param event
     */
    onFocusOut(event: any): void;
    /**
     * this function is fired on change event.
     * @param event
     */
    onChange(event: any): void;
    setFocus(): void;
    /******************************************************************************************************************/
    /******************************************************************************************************************/
    keyDown: Function;
    keyUp: Function;
    focus: Function;
    focusOut: Function;
    change: Function;
    spyChanges(event: any): void;
    resetOriginalValue(): void;
}
