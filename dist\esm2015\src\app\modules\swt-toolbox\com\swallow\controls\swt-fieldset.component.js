/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input, ViewChild } from "@angular/core";
export class SwtFieldSet extends Container {
    //-------constructor-----------------------------------------------------------//
    /**
     * @param {?} elem
     * @param {?} _commonService
     */
    constructor(elem, _commonService) {
        super(elem, _commonService);
        this.elem = elem;
        this._commonService = _commonService;
        this._legendText = '';
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtFieldSet');
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set legendText(value) {
        this._legendText = value;
        $(this.legendItem.nativeElement).html(this._legendText);
    }
    /**
     * @return {?}
     */
    get legendText() {
        return this._legendText;
    }
}
SwtFieldSet.decorators = [
    { type: Component, args: [{
                selector: 'SwtFieldSet',
                template: `
    <div class="fieldset"><h1><span #legendItem>Legend</span></h1> 
    <ng-content></ng-content>
    <ng-container #_container></ng-container>
    </div>

  `,
                styles: [`
             :host {
               margin: 0px;
               display: block;
               margin-top :10px;
               outline: none;
             }

             .fieldset {
              border: 1px solid silver;
              border-top: none;
              padding: 0.5em;
              width: 100%;
              height : 100%;
          }
          .fieldset > h1 {
              font: 1em normal;
              margin: -1em -0.5em 0;
          }   
          .fieldset > h1 > span {
              float: left;
          }
          .fieldset > h1:before {
              border-top: 1px solid silver;
              content: ' ';
              float: left;
              margin: 0.5em 2px 0 -1px;
              width: 0.75em;
          }
          .fieldset > h1:after {
              border-top: 1px solid silver;
              content: ' ';
              display: block;
              left: 2px;
              margin: 0 1px 0 0;
              overflow: hidden;
              position: relative;
              top: 0.5em;
          }
           
   `]
            }] }
];
/** @nocollapse */
SwtFieldSet.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtFieldSet.propDecorators = {
    legendItem: [{ type: ViewChild, args: ['legendItem',] }],
    legendText: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtFieldSet.prototype.legendItem;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype._legendText;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,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