import { OnInit, ElementRef, EventEmitter } from '@angular/core';
/**
 * In order to improve the readability of code on the one hand and to structure the toolbox project,
 * we created a directive that loads the jquery library to do all the manipulations on the DOM
 * and that has the role of managing all that is vusuel.
 * this directive avoids the repetition of the same processing in each component such as width, height, enabled, visible ... etc
 * with this solution the components only contain the getter and setter or / and the inputs.
 * all future components should use this directive to fix the html invalid tags issue generated by angular
 * these tags cause browser compatibility issues also the css does not apply on these tags.
 * Example: if we write <VBox height="100%"> bla..bla.. </VBox> if we inspect the view we find in the DOM this code
 *
 * <vbox ng-reflect-height="100%"> <-- This tag generated by angular cause the problem
 *   <div style="height:100%"> bla..bla</div>
 * </vbox>
 *
 * So the solution is to remove all angular tags in order to obtain a valid HTML tags in the DOM.
 * <AUTHOR>
 * @version 1.0.0
 */
export declare class SwtDOMManager implements OnInit {
    private element;
    private _visible;
    private _enabled;
    private parent;
    private child;
    private _buttonMode;
    private _styleName;
    private component;
    private _toolTip;
    onClick: EventEmitter<Function>;
    onKeyDown: EventEmitter<Function>;
    onFocusOut: EventEmitter<Function>;
    onKeyUp: EventEmitter<Function>;
    mouseOver: EventEmitter<Function>;
    mouseEnter: EventEmitter<Function>;
    mouseLeave: EventEmitter<Function>;
    onFocus: EventEmitter<Function>;
    mouseDown: EventEmitter<Function>;
    mouseUp: EventEmitter<Function>;
    width: string;
    height: string;
    styleName: string;
    id: string;
    paddingTop: string;
    paddingRight: string;
    paddingBottom: string;
    paddingLeft: string;
    marginTop: string;
    marginRight: string;
    marginBottom: string;
    marginLeft: string;
    horizontalAlign: string;
    visible: boolean;
    toolTip: string;
    constructor(element: ElementRef);
    /**
     * @method ngOnInit.
     */
    ngOnInit(): void;
    private disableComponent;
    private removeListeners;
    /**
     * This method is used to set paddings to component.
     * @param component
     * @param top
     * @param right
     * @param bottom
     * @param left
     */
    private setPaddings;
    /**
     * This method is used to set margins to component.
     * @param component
     * @param top
     * @param right
     * @param bottom
     * @param left
     */
    private setMargins;
    /**
     * This method is used to align element content.
     * @param component
     * @param name
     * @param position
     */
    private alignContent;
}
