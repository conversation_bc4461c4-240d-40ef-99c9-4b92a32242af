/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Keyboard } from './../utils/keyboard.service';
import { SwtUtil } from './../utils/swt-util.service';
import { SwtAlert } from './../utils/swt-alert.service';
import { JSONReader } from './../jsonhandler/jsonreader.service';
import { HDividedBox } from './../controls/swt-hdividedbox.component';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { SwtCanvas } from './../controls/swt-canvas.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, Component, Input, Renderer2, ViewChild } from "@angular/core";
import { SwtCommonGrid } from '../controls/swt-common-grid.component';
import { SwtLabel } from '../controls/swt-label.component';
import { Logger } from '../logging/logger.service';
import { CustomTreeEvent } from '../events/swt-events.module';
import { StringUtils } from '../utils/string-utils.service';
var SwtSummary = /** @class */ (function (_super) {
    tslib_1.__extends(SwtSummary, _super);
    //-------constructor-----------------------------------------------------------//
    function SwtSummary(elem, commonService, _renderer) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._renderer = _renderer;
        _this.expandFirstLoad = true;
        /**
         * JSON Objects
         *
         */
        _this.jsonReader = new JSONReader();
        /**
         * Communication Objects
         *
         */
        _this.baseURL = "";
        _this.actionMethod = "";
        _this.actionPath = "";
        _this.facilityAccess = false;
        _this.supportAllCurrency = false;
        _this.supportAllEntity = false;
        /**
         * Summary Tree Objects
         *
         */
        _this.treeOpenedItems = [];
        _this.treeClosedItems = [];
        _this.gridOpenedItems = [];
        _this.gridVisibleItems = [];
        _this.currentDivPos = -1;
        _this.facilityId = "";
        _this.facilityName = "";
        _this.useGeneric = "";
        _this.scenarioTitle = "";
        _this.selectedscenario = "";
        _this.resetFilter = false;
        _this.columnData = [];
        _this.logger = new Logger('SwtSummary', _this.commonService.httpclient);
        _this.swtAlert = new SwtAlert(commonService);
        return _this;
    }
    /**
     * @return {?}
     */
    SwtSummary.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        this.logger.info('method [ngOnDestroy] : unscbscribe all Observables.  - START/END -');
    };
    /**
     * @return {?}
     */
    SwtSummary.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        _super.prototype.ngOnInit.call(this);
        this.summaryGrid.uniqueColumn = "expand";
        this.summaryGrid.lockedColumnCount = 1;
        this.summaryGrid.selectable = true;
        this.summaryGrid.enableRowSelection = false;
        this.setLabelField("label");
        this.hideRoot(true);
        this.showTreeDataTips(true);
        this.tree.saveTreeStateBasedOn = "id";
        this.IDField = "id";
        this.gridTitle.text = SwtUtil.getPredictMessage('scenarioSummary.selectedScen', null);
        this.gridTitle.toolTip = SwtUtil.getPredictMessage('tooltip.selectedScen', null);
        this.treeTitle.toolTip = SwtUtil.getPredictMessage('tooltip.scenTotals', null);
        this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null);
        this.tree.addEventListener(CustomTreeEvent.ITEMRENDER, (/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            /** @type {?} */
            var name = item.data.name;
            /** @type {?} */
            var ALERTABLE = "Y";
            /** @type {?} */
            var alert = item.data.alert;
            /** @type {?} */
            var isBranch = item.data.isBranch;
            if (isBranch == true) {
                item.setStyle("fontWeight", "normal");
                item.setStyle("fontSize", 12);
                item.setStyle("color", "black");
            }
            else {
                if (alert == ALERTABLE) {
                    item.setStyle("fontSize", 12);
                    item.setStyle("background", "url('assets/images/predict_images/alert.png') no-repeat 38px 0%");
                }
                else {
                    item.setStyle("fontWeight", "normal");
                    item.setStyle("fontSize", 12);
                    item.setStyle("background", "url('assets/images/predict_images/treefile.png') no-repeat 38px 0%");
                }
            }
        }));
        // adding listener on expand or collapse icon click on summaryGrid control
        this.tree.addEventListener(CustomTreeEvent.ITEMDOUBLECLICK, (/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            _this.treeKeyDownHandler(item);
        }));
        this.summaryGrid.ITEM_CLICK.subscribe((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.changeTreeData(event);
        }));
    };
    /**
     * @param {?} label
     * @return {?}
     */
    SwtSummary.prototype.setLabelField = /**
     * @param {?} label
     * @return {?}
     */
    function (label) {
        //this.tree.labelField='@' + label;
    };
    /**
     * @param {?} hide
     * @return {?}
     */
    SwtSummary.prototype.hideRoot = /**
     * @param {?} hide
     * @return {?}
     */
    function (hide) {
        if (hide == true) {
            this.tree.showRoot = false;
        }
        else {
            this.tree.showRoot = true;
        }
    };
    /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     **/
    /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     *
     * @return {?}
     */
    SwtSummary.prototype.getSortedGridColumn = /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     *
     * @return {?}
     */
    function () {
        /** @type {?} */
        var selectedSort = null;
        /** @type {?} */
        var direction = null;
        direction = this.summaryGrid.sortDirection ? "DESC" : "ASC";
        selectedSort = this.summaryGrid.sortColumnIndex + "|" + direction;
        return selectedSort;
    };
    /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     **/
    /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     *
     * @return {?}
     */
    SwtSummary.prototype.getIsScenarioAlertable = /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     *
     * @return {?}
     */
    function () {
        /** @type {?} */
        var isAlertable = "";
        if (this.tree.selectedItem) {
            /** @type {?} */
            var value = this.tree.selectedItem.data;
            isAlertable = value.alert == 'Y' ? 'Y' : 'N';
        }
        return isAlertable;
    };
    /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     **/
    /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     *
     * @return {?}
     */
    SwtSummary.prototype.getIsBranch = /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     *
     * @return {?}
     */
    function () {
        /** @type {?} */
        var isBranch = "";
        /** @type {?} */
        var value = this.tree.selectedItem.data;
        isBranch = value.isBranch == 'true' ? 'true' : 'false';
        return isBranch;
    };
    /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     **/
    /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     *
     * @return {?}
     */
    SwtSummary.prototype.getDividerPosition = /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     *
     * @return {?}
     */
    function () {
        return this.currentDivPos.toString();
    };
    /**
     * setActionPath
     *
     * @param _actionPath:String
     *
     * This method is called to set actionPath to save controls user prefrerence
     **/
    /**
     * setActionPath
     *
     * @param {?} actionPath
     * @return {?}
     */
    SwtSummary.prototype.setActionPath = /**
     * setActionPath
     *
     * @param {?} actionPath
     * @return {?}
     */
    function (actionPath) {
        this.actionPath = actionPath;
    };
    /**
     * setBaseURL
     *
     * This method is called to set baseURL
     **/
    /**
     * setBaseURL
     *
     * This method is called to set baseURL
     *
     * @param {?} baseURL
     * @return {?}
     */
    SwtSummary.prototype.setBaseURL = /**
     * setBaseURL
     *
     * This method is called to set baseURL
     *
     * @param {?} baseURL
     * @return {?}
     */
    function (baseURL) {
        this.baseURL = baseURL;
    };
    /**
     * @param {?} facilityAcc
     * @param {?} entityAcc
     * @param {?} ccyAcc
     * @return {?}
     */
    SwtSummary.prototype.setAccessRules = /**
     * @param {?} facilityAcc
     * @param {?} entityAcc
     * @param {?} ccyAcc
     * @return {?}
     */
    function (facilityAcc, entityAcc, ccyAcc) {
        // cellRenderer.properties={facilityAccess:facilityAcc,_supportAllEntity:entityAcc,_supportAllCurrency:ccyAcc};
    };
    /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     **/
    /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     *
     * @return {?}
     */
    SwtSummary.prototype.getSelectedItemID = /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     *
     * @return {?}
     */
    function () {
        // var to hold the tree selected item
        /** @type {?} */
        var selectedItem = this.tree.selectedItem;
        /* Check if  the selectedItem is not null*/
        if (selectedItem != null) {
            // check if the node is not branch
            if (selectedItem.data.isBranch == false)
                return selectedItem.data.id;
        }
        return null;
    };
    /**
     * Return the first subNode of the tree
     **/
    /**
     * Return the first subNode of the tree
     *
     * @return {?}
     */
    SwtSummary.prototype.setFirstSubNode = /**
     * Return the first subNode of the tree
     *
     * @return {?}
     */
    function () {
        if (this.jsonReader.getScreenAttributes()["selectedscenario"] !== "") {
            if (this.jsonReader.getTreeData()[0].root.node[0]) {
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node[0].node;
            }
            else {
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node.node;
            }
            this.tree.selectedIndex = 1;
        }
    };
    /**
     * selectTreeItem
     * @param newJSON:
     * This method is called to auto-select the last selected item when updating the dataprovider
     **/
    //selectTreeItem(newJSON): void {
    // let search= null;
    //  let index = -1;
    // if (this.lastSelectedItem) {
    //     console.log("this.lastSelectedItem-selectTreeItem", this.lastSelectedItem);
    //     console.log("this.lastSelectedItem-selectTreeItem",this.lastSelectedItem.data.isBranch );
    //    if (this.lastSelectedItem.data.isBranch == true) {
    //        search = newJSON.find(x=>x.data.id == this.lastSelectedItem.data.id);
    //
    //     } else {
    //        console.log("newJSON----", newJSON);
    //        console.log("lastSelectedItem----", this.lastSelectedItem.data.id, this.lastSelectedItem.data.alert);
    //          search = newJSON.find(x=>x.node.id == this.lastSelectedItem.data.id && x.node.alert == this.lastSelectedItem.data.alert);
    //          console.log("search----", search);
    //     }
    // }
    //
    //     if (search != null) {
    //         let itemsList = newJSON.node;
    //         let alertableCheck= true;
    //         if (search.isBranch =="true") {
    //             itemsList= newJSON.node;
    //             alertableCheck = false;
    //         }
    //     if(itemsList){
    //          itemsList.forEach (( item ) => {
    //              if(alertableCheck) {
    //                 if (item.id == search.id && item.alert==search.alert ) {
    //                      index = this.tree.getItemIndex(item);
    //                 }
    //             } else {
    //                 if (item.id==search.id ) {
    //                     index = this.tree.getItemIndex(item);
    //                 }
    //           }
    //         } );
    //     }
    //     }
    //     // set tree selected index
    //     this.tree.selectedIndex=index;
    // }
    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     **/
    /**
         * selectTreeItem
         * @param newJSON:
         * This method is called to auto-select the last selected item when updating the dataprovider
         **/
    //selectTreeItem(newJSON): void {
    // let search= null;
    //  let index = -1;
    // if (this.lastSelectedItem) {
    //     console.log("this.lastSelectedItem-selectTreeItem", this.lastSelectedItem);
    //     console.log("this.lastSelectedItem-selectTreeItem",this.lastSelectedItem.data.isBranch );
    //    if (this.lastSelectedItem.data.isBranch == true) {
    //        search = newJSON.find(x=>x.data.id == this.lastSelectedItem.data.id);
    //
    //     } else {
    //        console.log("newJSON----", newJSON);
    //        console.log("lastSelectedItem----", this.lastSelectedItem.data.id, this.lastSelectedItem.data.alert);
    //          search = newJSON.find(x=>x.node.id == this.lastSelectedItem.data.id && x.node.alert == this.lastSelectedItem.data.alert);
    //          console.log("search----", search);
    //     }
    // }
    //
    //     if (search != null) {
    //         let itemsList = newJSON.node;
    //         let alertableCheck= true;
    //         if (search.isBranch =="true") {
    //             itemsList= newJSON.node;
    //             alertableCheck = false;
    //         }
    //     if(itemsList){
    //          itemsList.forEach (( item ) => {
    //              if(alertableCheck) {
    //                 if (item.id == search.id && item.alert==search.alert ) {
    //                      index = this.tree.getItemIndex(item);
    //                 }
    //             } else {
    //                 if (item.id==search.id ) {
    //                     index = this.tree.getItemIndex(item);
    //                 }
    //           }
    //         } );
    //     }
    //     }
    //     // set tree selected index
    //     this.tree.selectedIndex=index;
    // }
    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     *
     * @return {?}
     */
    SwtSummary.prototype.enableTree = /**
         * selectTreeItem
         * @param newJSON:
         * This method is called to auto-select the last selected item when updating the dataprovider
         **/
    //selectTreeItem(newJSON): void {
    // let search= null;
    //  let index = -1;
    // if (this.lastSelectedItem) {
    //     console.log("this.lastSelectedItem-selectTreeItem", this.lastSelectedItem);
    //     console.log("this.lastSelectedItem-selectTreeItem",this.lastSelectedItem.data.isBranch );
    //    if (this.lastSelectedItem.data.isBranch == true) {
    //        search = newJSON.find(x=>x.data.id == this.lastSelectedItem.data.id);
    //
    //     } else {
    //        console.log("newJSON----", newJSON);
    //        console.log("lastSelectedItem----", this.lastSelectedItem.data.id, this.lastSelectedItem.data.alert);
    //          search = newJSON.find(x=>x.node.id == this.lastSelectedItem.data.id && x.node.alert == this.lastSelectedItem.data.alert);
    //          console.log("search----", search);
    //     }
    // }
    //
    //     if (search != null) {
    //         let itemsList = newJSON.node;
    //         let alertableCheck= true;
    //         if (search.isBranch =="true") {
    //             itemsList= newJSON.node;
    //             alertableCheck = false;
    //         }
    //     if(itemsList){
    //          itemsList.forEach (( item ) => {
    //              if(alertableCheck) {
    //                 if (item.id == search.id && item.alert==search.alert ) {
    //                      index = this.tree.getItemIndex(item);
    //                 }
    //             } else {
    //                 if (item.id==search.id ) {
    //                     index = this.tree.getItemIndex(item);
    //                 }
    //           }
    //         } );
    //     }
    //     }
    //     // set tree selected index
    //     this.tree.selectedIndex=index;
    // }
    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     *
     * @return {?}
     */
    function () {
        this.tree.selectable = true;
    };
    /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     **/
    /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     *
     * @return {?}
     */
    SwtSummary.prototype.disableTree = /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     *
     * @return {?}
     */
    function () {
        // disable the tree selection
        this.tree.selectable = false;
    };
    /**
     * showTreeDataTips
     * @param:boolean
     * This method is used enable/disable tooltips on tree node
     **/
    /**
     * showTreeDataTips
     * \@param:boolean
     * This method is used enable/disable tooltips on tree node
     *
     * @param {?} show
     * @return {?}
     */
    SwtSummary.prototype.showTreeDataTips = /**
     * showTreeDataTips
     * \@param:boolean
     * This method is used enable/disable tooltips on tree node
     *
     * @param {?} show
     * @return {?}
     */
    function (show) {
        // this.tree.showDataTips = show;
        this.tree.dataTipFunction = this.dataTipFunction;
    };
    /**
     * dataTipFunction
     * @param: Object
     * This function is used to set tooltip of tree node
     */
    /**
     * dataTipFunction
     * \@param: Object
     * This function is used to set tooltip of tree node
     * @param {?} item
     * @return {?}
     */
    SwtSummary.prototype.dataTipFunction = /**
     * dataTipFunction
     * \@param: Object
     * This function is used to set tooltip of tree node
     * @param {?} item
     * @return {?}
     */
    function (item) {
        return (item.data.desc) ? item.data.desc : "";
    };
    /**
     * treeNodeEventHandler
     * @param event:Event
     * This method is called when opening or closing a tree node
     **/
    /**
     * treeNodeEventHandler
     * @param {?} event
     * @return {?}
     */
    SwtSummary.prototype.treeNodeEventHandler = /**
     * treeNodeEventHandler
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.saveTreeOpenState();
    };
    /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     */
    /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     * @return {?}
     */
    SwtSummary.prototype.saveTreeOpenState = /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     * @return {?}
     */
    function () {
        // Initialise the open list array
        this.treeOpenedItems = [];
        this.treeClosedItems = [];
        if (this.tree.selectedItem != null) {
            if (this.lastSelectedItem) {
                if (this.tree.selectedItem.id !== this.lastSelectedItem.id) {
                    this.lastSelectedItem = this.tree.selectedItem;
                    this.resetFilter = true;
                }
                else {
                    this.resetFilter = false;
                }
            }
        }
        this.tree.saveTreeOpenState();
        if (this.tree.openItems.length > 0) {
            for (var i = 0; i < this.tree.openItems.length; i++) {
                this.treeOpenedItems.push(this.tree.openItems[i]);
            }
        }
        if (this.tree.closeItems.length > 0) {
            for (var i = 0; i < this.tree.closeItems.length; i++) {
                this.treeClosedItems.push(this.tree.closeItems[i]);
            }
        }
    };
    /**
     * openTreeItems
     * used to maintain the tree node
     */
    /**
     * openTreeItems
     * used to maintain the tree node
     * @return {?}
     */
    SwtSummary.prototype.openTreeItems = /**
     * openTreeItems
     * used to maintain the tree node
     * @return {?}
     */
    function () {
        this.tree.openItems = [];
        for (var i = 0; i < this.treeOpenedItems.length; i++) {
            this.tree.openItems.push(this.treeOpenedItems[i]);
        }
        this.tree.closeItems = [];
        for (var i = 0; i < this.treeClosedItems.length; i++) {
            this.tree.closeItems.push(this.treeClosedItems[i]);
        }
        this.tree.reOpenSavedState();
    };
    /**
     * treeScrollEventHandler
     * @param event:ScrollEvent
     * This method is called when a scroll event occurs on the tree
     **/
    /**
     * treeScrollEventHandler
     * @param {?} event
     * @return {?}
     */
    SwtSummary.prototype.treeScrollEventHandler = /**
     * treeScrollEventHandler
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.verticalScrollPosition = this.tree.verticalScrollPosition;
    };
    /**
     * treeItemDoubleClickHandler
     *
     * @param event:ListEvent
     *
     * This method is used to expand tree when double clicking on a node
     **/
    /**
     * treeItemDoubleClickHandler
     *
     * @param {?} event
     * @return {?}
     */
    SwtSummary.prototype.treeItemDoubleClickHandler = /**
     * treeItemDoubleClickHandler
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        /** @type {?} */
        var node = this.tree.selectedItem;
        /** @type {?} */
        var isOpen = this.isItemOpen(node);
        this.tree.expandItem(node, !isOpen);
    };
    /**
     * @param {?} item
     * @return {?}
     */
    SwtSummary.prototype.isItemOpen = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        return item.isExpanded();
    };
    /**
     * changeTreeData
     *
     * @param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     */
    /**
     * changeTreeData
     *
     * \@param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     * @param {?} event
     * @return {?}
     */
    SwtSummary.prototype.changeTreeData = /**
     * changeTreeData
     *
     * \@param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     * @param {?} event
     * @return {?}
     */
    function (event) {
        /** @type {?} */
        var colIndex = event.columnIndex;
        /** @type {?} */
        var expand = event.target.data.slickgrid_rowcontent['expand'].content == "Y";
        if (colIndex == 0 && expand) {
            /** @type {?} */
            var entity = event.target.data.entity;
            for (var i = 0; i < this.lastRecievedJSON.alert.grid.rows.size; i++) {
                /** @type {?} */
                var xmlElement = this.lastRecievedJSON.alert.grid.rows.row[i];
                if (xmlElement['entity'] && entity == xmlElement['entity'].content) {
                    this.lastRecievedJSON.alert.grid.rows.row[i].expand.opened = !xmlElement.expand.opened;
                }
                if (this.lastRecievedJSON.alert.grid.rows.row[i][this.summaryGrid.GroupId] && entity == this.lastRecievedJSON.alert.grid.rows.row[i][this.summaryGrid.GroupId].content && this.lastRecievedJSON.alert.grid.rows.row[i].expand.content != 'Y') {
                    this.lastRecievedJSON.alert.grid.rows.row[i].visible = !xmlElement.visible;
                }
            }
            if (this.lastRecievedJSON.alert.grid.rows.size != 1) {
                /** @type {?} */
                var grid2dataset_1 = [];
                JSONReader.jsonpath(this.lastRecievedJSON, '$.alert.grid.rows.row.*').forEach((/**
                 * @param {?} value
                 * @return {?}
                 */
                function (value) {
                    if (value.visible == true || value.visible == "true") {
                        if (value === Object(value)) {
                            grid2dataset_1.push(value);
                        }
                    }
                }));
                this.summaryGrid.gridData = { row: grid2dataset_1, size: grid2dataset_1.length };
            }
            else {
                this.summaryGrid.gridData = this.jsonReader.getGridData();
                this.summaryGrid.setRowSize = this.jsonReader.getRowSize();
            }
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtSummary.prototype.treeKeyDownHandler = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        /** @type {?} */
        var node = this.tree.selectedItem;
        /** @type {?} */
        var itemIsBranch = node.data.isBranch;
        // Check if event key is ENTER
        if (event.keyCode == Keyboard.ENTER || event.keyCode == Keyboard.UP || event.keyCode == Keyboard.DOWN) {
            if (!itemIsBranch) {
                // this.summaryGrid.applyFilterSort = true;
                dispatchEvent(new Event("treeItemClick"));
            }
            else {
                this.resetGridData();
            }
        }
    };
    /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     **/
    /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     *
     * @return {?}
     */
    SwtSummary.prototype.resetGridData = /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     *
     * @return {?}
     */
    function () {
        // nullify the gridData
        this.summaryGrid.gridData = null;
        // validate properties
        // this.summaryGrid.validateProperties();
    };
    /**
     * dataProvider
     *
     * @param data:XML
     *
     * This method used to set data for swtSummary controls
     **/
    /**
     * dataProvider
     *
     * @param {?} data
     * @return {?}
     */
    SwtSummary.prototype.dataProvider = /**
     * dataProvider
     *
     * @param {?} data
     * @return {?}
     */
    function (data) {
        var _this = this;
        /** @type {?} */
        var dividerPos;
        if (data != null) {
            this.lastRecievedJSON = data;
            this.jsonReader.setInputJSON(this.lastRecievedJSON);
            if (this.lastRecievedJSON != this.prevRecievedJSON) {
                /** @type {?} */
                var treeData = this.lastRecievedJSON.alert.tree.root.node;
                if (this.tree.dataProvider != treeData) {
                    if (this.tree.openItems.length > 0) {
                        // this.saveTreeOpenState();
                    }
                    else {
                        if (this.expandFirstLoad) {
                            // expand tree to first level
                            setTimeout((/**
                             * @return {?}
                             */
                            function () {
                                _this.tree.expandAll(CustomTree.LEVEL_1_STR);
                            }), 0);
                        }
                    }
                    this.tree.dataProvider = treeData;
                    this.setTreeTotals(this.jsonReader.getScreenAttributes()["total"]);
                    // this.tree.validateProperties();
                    if (this.treeOpenedItems != null) {
                        if (this.treeOpenedItems.length > 0) {
                            this.openTreeItems();
                        }
                        // this.selectTreeItem(treeData);
                    }
                    this.tree.verticalScrollPosition = this.verticalScrollPosition;
                    // validate display properties
                    // this.tree.validateProperties();
                }
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    if (_this.tree.selectedItem && _this.prevRecievedJSON && _this.lastSelectedItem != null && _this.lastSelectedItem.id == _this.tree.selectedItem.id) {
                        try {
                            //  if (!this.resetFilter) {
                            // this.summaryGrid.applyFilterSort=true;
                            _this.saveGridOpenState(_this.prevRecievedJSON.alert.grid.rows.row);
                            _this.lastRecievedJSON.alert.grid.rows.row = _this.openGridItems(_this.lastRecievedJSON.alert.grid.rows.row);
                            if (_this.summaryGrid.isFiltered) {
                                _this.lastRecievedJSON.alert.grid.rows.row = _this.showVisibleItems(_this.lastRecievedJSON.alert.grid.rows.row);
                            }
                        }
                        catch (error) {
                            console.log("SwtSummary -> dataProvider -> error", error);
                        }
                    }
                    else {
                        _this.summaryGrid.isFiltered = false;
                        _this.summaryGrid.sortColumnIndex = -1;
                    }
                    _this.lastSelectedItem = _this.deepCopy(_this.tree.selectedItem);
                    _this.facilityAccess = (_this.jsonReader.getScreenAttributes()["facilityAccess"] !== "2");
                    _this.supportAllCurrency = StringUtils.isTrue(_this.jsonReader.getScreenAttributes()["supportAllCurrency"]);
                    _this.supportAllEntity = StringUtils.isTrue(_this.jsonReader.getScreenAttributes()["supportAllEntity"]);
                    // this.summaryGrid.setAccessRules(facilityAccess,supportAllEntity,supportAllCurrency);
                    /** @type {?} */
                    var jsonlist = _this.jsonReader.getColumnData().column;
                    data.alert.grid.metadata.columns.column = jsonlist;
                    _this.columnData = data.alert.grid.metadata;
                    for (var nonFiltrable in _this.columnData['columns']['column']) {
                        if (_this.columnData['columns']['column'][nonFiltrable].dataelement === "expand") {
                            _this.columnData['columns']['column'][nonFiltrable].filterable = false;
                            _this.columnData['columns']['column'][nonFiltrable].draggable = false;
                            _this.columnData['columns']['column'][nonFiltrable].heading = "";
                        }
                    }
                    _this.summaryGrid.CustomGrid(_this.columnData);
                    _this.summaryGrid.rowClickableFunction = (/**
                     * @param {?} dataContext
                     * @param {?} dataIndex
                     * @param {?} color
                     * @return {?}
                     */
                    function (dataContext, dataIndex, color) {
                        return _this.setClickable(dataContext, dataIndex, color);
                    });
                    _this.summaryGrid.GroupId = 'entity';
                    /** @type {?} */
                    var rowData = _this.lastRecievedJSON.alert.grid.rows;
                    for (var i = 0; i < _this.summaryGrid.columnDefinitions.length; i++) {
                        /** @type {?} */
                        var column = _this.summaryGrid.columnDefinitions[i];
                        if (column.field == "entity") {
                            if (rowData.size > 1) {
                                for (var j = 0; j < rowData.row.length; j++) {
                                    rowData.row[j].entity.bold = Boolean(rowData.row[j].entity.haschildren);
                                }
                            }
                            else if (rowData.size == 1) {
                                if (rowData.row.entity) {
                                    rowData.row.entity.bold = rowData.row.entity.content == "All";
                                }
                                else {
                                    rowData.row[0].entity.bold = rowData.row[0].entity.content == "All";
                                }
                            }
                        }
                    }
                    if (_this.jsonReader.getGridData()) {
                        if (_this.jsonReader.getGridData().size > 0) {
                            if (_this.jsonReader.getGridData().size > 1) {
                                /** @type {?} */
                                var grid2dataset_2 = [];
                                JSONReader.jsonpath(data, '$.alert.grid.rows.row.*').forEach((/**
                                 * @param {?} value
                                 * @return {?}
                                 */
                                function (value) {
                                    if (value.visible == true) {
                                        if (value === Object(value)) {
                                            grid2dataset_2.push(value);
                                        }
                                    }
                                }));
                                _this.summaryGrid.gridData = { row: grid2dataset_2, size: grid2dataset_2.length };
                            }
                            else {
                                _this.summaryGrid.gridData = _this.jsonReader.getGridData();
                                _this.summaryGrid.setRowSize = _this.jsonReader.getRowSize();
                            }
                            _this.summaryGrid.saveColumnOrder = true;
                            _this.summaryGrid.saveWidths = false;
                            _this.summaryGrid.allowMultipleSelection = false;
                            _this.summaryGrid.doubleClickEnabled = true;
                            _this.summaryGrid.rowHeight = 20;
                            _this.summaryGrid.styleName = 'dataGridNormal';
                            _this.summaryGrid.colWidthURL(_this.baseURL + _this.actionPath);
                            _this.summaryGrid.colOrderURL(_this.baseURL + _this.actionPath);
                        }
                        else {
                            _this.summaryGrid.gridData = { row: [], size: 0 };
                        }
                    }
                    else {
                        _this.summaryGrid.gridData = { row: [], size: 0 };
                    }
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        _this.summaryGrid.validateNow();
                    }), 0);
                    _this.facilityId = _this.jsonReader.getScreenAttributes()["facilityid"];
                    _this.facilityName = _this.jsonReader.getScreenAttributes()["facilityname"];
                    _this.useGeneric = _this.jsonReader.getScreenAttributes()["useGeneric"];
                    _this.scenarioTitle = _this.jsonReader.getScreenAttributes()["scenarioTitle"];
                    _this.selectedscenario = _this.jsonReader.getScreenAttributes()["selectedscenario"];
                    dividerPos = _this.jsonReader.getScreenAttributes()["dividerposition"];
                    /* check if the divider position from response xml is not empty */
                    if (dividerPos != "") {
                        if (_this.currentDivPos == -1) {
                            if (!parseFloat(dividerPos)) {
                                _this.currentDivPos = parseFloat('60');
                            }
                            else {
                                _this.currentDivPos = parseFloat(dividerPos);
                            }
                            _this.divBox.setWidthLeftWithoutEvent(_this.currentDivPos + '%');
                        }
                    }
                    else {
                        // set default percent width of the summary grid
                        // this.gridContainer.width= this.currentDivPos * ( this.stage.width / 100 );
                        // validate the  grid content
                        // this.gridContainer.validateNow();
                    }
                    if (_this.jsonReader.getScreenAttributes()["selectedscenariolastran"] != "") {
                        _this.lastRanLbl.visible = true;
                        _this.setScenarioLastRan(_this.jsonReader.getScreenAttributes()["selectedscenariolastran"]);
                    }
                    else {
                        _this.lastRanLbl.visible = false;
                    }
                    // set previous XML
                    _this.prevRecievedJSON = _this.lastRecievedJSON;
                }), 0);
            }
        }
    };
    /**
     * @private
     * @param {?} lastRan
     * @return {?}
     */
    SwtSummary.prototype.setScenarioLastRan = /**
     * @private
     * @param {?} lastRan
     * @return {?}
     */
    function (lastRan) {
        this.lastRanLbl.text = StringUtils.trim(SwtUtil.getPredictMessage('scenarioSummary.selectedScenLastRan', null)) + " " + lastRan.replace('taking', SwtUtil.getPredictMessage('label.taking', null)).replace('seconds', SwtUtil.getPredictMessage('label.seconds', null));
    };
    /**
     * @param {?} dataContext
     * @param {?} index
     * @param {?} dataElement
     * @return {?}
     */
    SwtSummary.prototype.setClickable = /**
     * @param {?} dataContext
     * @param {?} index
     * @param {?} dataElement
     * @return {?}
     */
    function (dataContext, index, dataElement) {
        /** @type {?} */
        var value = dataContext.slickgrid_rowcontent.count.content;
        /** @type {?} */
        var isClickable = false;
        // if value is greater than 0
        if (parseInt(value, 10) > 0) {
            if (this.facilityAccess == false) {
                //set clickable to false
                isClickable = false;
            }
            else {
                /** @type {?} */
                var entity = dataContext.slickgrid_rowcontent.entity.content;
                /** @type {?} */
                var ccy = dataContext.slickgrid_rowcontent.ccy.content;
                //if entiy value or currency value equal "All" we need to verify if the facility supports "All" as Currency or "All" as Entity
                if (entity == "All" || ccy == "All") {
                    if ((entity == "All") && (ccy != "All")) {
                        isClickable = this.supportAllEntity;
                    }
                    else if ((entity != "All") && (ccy == "All")) {
                        isClickable = this.supportAllCurrency;
                    }
                    else if ((entity == "All") && (ccy == "All")) {
                        isClickable = this.supportAllCurrency && this.supportAllEntity;
                    }
                }
                else {
                    isClickable = true;
                }
            }
        }
        else {
            isClickable = false;
        }
        return isClickable;
    };
    /**
     * showVisibleItems
     * @param dataProvider:XMLList
     * This method is called to show visible rows in the datagrid after filter selection
     **/
    /**
     * showVisibleItems
     * @param {?} dataProvider
     * @return {?}
     */
    SwtSummary.prototype.showVisibleItems = /**
     * showVisibleItems
     * @param {?} dataProvider
     * @return {?}
     */
    function (dataProvider) {
        if (dataProvider) {
            for (var i = 0; i < this.gridVisibleItems.length; i++) {
                for (var j = 0; j < dataProvider.length; j++) {
                    /** @type {?} */
                    var element = dataProvider[j];
                    element.visible = true;
                    if (element == this.gridVisibleItems[i]) {
                        dataProvider[j] = element;
                    }
                }
            }
        }
        return dataProvider;
    };
    /**
     * @param {?} e
     * @return {?}
     */
    SwtSummary.prototype.saveGridVisibleState = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        /** @type {?} */
        var visibleItems = null;
        this.gridVisibleItems = [];
        visibleItems = this.deepCopy(this.lastRecievedJSON.alert.grid.rows.row).filter((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return x.visible == true; }));
        for (var i = 0; i < visibleItems.length; i++) {
            this.gridVisibleItems[i] = visibleItems[i];
        }
    };
    /**
     * saveGridOpenState
     * @param newXML
     * used to save opened grid items
     */
    /**
     * saveGridOpenState
     * @param {?} newXML
     * used to save opened grid items
     * @return {?}
     */
    SwtSummary.prototype.saveGridOpenState = /**
     * saveGridOpenState
     * @param {?} newXML
     * used to save opened grid items
     * @return {?}
     */
    function (newXML) {
        this.gridOpenedItems = [];
        if (newXML) {
            this.gridOpenedItems = this.deepCopy(newXML).filter((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.expand.content == "Y" && x.expand.opened == true; }));
        }
    };
    /**
     * @param {?} mainObj
     * @return {?}
     */
    SwtSummary.prototype.deepCopy = /**
     * @param {?} mainObj
     * @return {?}
     */
    function (mainObj) {
        /** @type {?} */
        var objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        var key;
        for (key in mainObj) {
            objCopy[key] = mainObj[key]; // copies each property to the objCopy object
        }
        return objCopy;
    };
    /**
     * openGridItems
     *
     * @param dataProvider:XMLList
     *
     * used to maintain opened grid items when refreshing
     */
    /**
     * openGridItems
     *
     * @private
     * @param {?} dataProvider
     * @return {?}
     */
    SwtSummary.prototype.openGridItems = /**
     * openGridItems
     *
     * @private
     * @param {?} dataProvider
     * @return {?}
     */
    function (dataProvider) {
        var _this = this;
        if (dataProvider) {
            if (!(dataProvider instanceof Array)) {
                /** @type {?} */
                var array = [];
                array[0] = dataProvider;
                dataProvider = array;
            }
            if (this.gridOpenedItems && this.gridOpenedItems.length > 0) {
                var _loop_1 = function (i) {
                    if (dataProvider.filter((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == _this.gridOpenedItems[i].entity.content; })).length > 0) {
                        if (dataProvider.filter((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == _this.gridOpenedItems[i].entity.content; }))[i]) {
                            dataProvider.filter((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == _this.gridOpenedItems[i].entity.content; }))[i].expand.opened = true;
                            for (var j = 0; j < dataProvider.filter((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return x.entity.content.toString() == _this.gridOpenedItems[i].entity.content; })).length; j++) {
                                dataProvider.filter((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return x.entity.content.toString() == _this.gridOpenedItems[i].entity.content; }))[j].visible = true;
                            }
                        }
                    }
                };
                for (var i = 0; i < this.gridOpenedItems.length; i++) {
                    _loop_1(i);
                }
            }
        }
        return dataProvider;
    };
    /**
     * @param {?} total
     * @return {?}
     */
    SwtSummary.prototype.setTreeTotals = /**
     * @param {?} total
     * @return {?}
     */
    function (total) {
        this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null) + " (" + total + ") ";
    };
    SwtSummary.decorators = [
        { type: Component, args: [{
                    selector: 'SwtSummary',
                    template: "        \n        <VBox #mainHGroup id=\"mainHGroup\"  width=\"100%\" height=\"100%\">\n            <HDividedBox id=\"divBox\" #divBox  width=\"100%\" height=\"90%\">\n                <VBox id=\"treeContainer\" #treeContainer width=\"40%\" height=\"100%\" class=\"left\">\n                    <SwtLabel  id=\"treeTitle\" #treeTitle\n                               fontWeight=\"normal\"\n                               height=\"8%\"\n                               paddingBottom=\"10\"\n                               paddingLeft=\"5\">\n                    </SwtLabel>\n                    <CustomTree id=\"tree\" #tree\n                                width=\"100%\"\n                                height=\"92%\"\n                                doubleClickEnabled=\"true\"\n                                (keyDown)=\"treeKeyDownHandler($event)\"\n                                (itemDoubleClick)=\"treeItemDoubleClickHandler($event)\">\n                    </CustomTree>\n                </VBox>\n                <VBox height=\"100%\"  width=\"60%\" id=\"gridContainer\" #gridContainer  class=\"right\">\n                    <SwtLabel id=\"gridTitle\"\n                              #gridTitle\n                              height=\"8%\"\n                              fontWeight=\"normal\"\n                              paddingBottom=\"10\">\n                    </SwtLabel>\n                    <SwtCanvas id=\"customGrid\" #customGrid width=\"100%\" height=\"92%\" borderStyle=\"solid\" cornerRadius=\"4\" dropShadowEnabled=\"true\" borderColor=\"#f9f9f9\" >\n                        <SwtCommonGrid \n                                (onFilterChanged)=\"saveGridVisibleState($event)\"\n                                id=\"summaryGrid\" \n                                #summaryGrid \n                                width=\"100%\" \n                                height=\"100%\">\n                        </SwtCommonGrid>\n                    </SwtCanvas>\n                </VBox>\n            </HDividedBox>\n            <HBox width=\"100%\" height=\"10%\"\n                  horizontalAlign=\"right\"\n                  paddingTop=\"6\"\n                  paddingRight=\"10\">\n                <SwtLabel id=\"lastRanLbl\"  #lastRanLbl  fontWeight=\"normal\" visible=\"false\">\n                </SwtLabel>\n            </HBox>\n        </VBox>\n  "
                }] }
    ];
    /** @nocollapse */
    SwtSummary.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    SwtSummary.propDecorators = {
        styleName: [{ type: Input, args: ['styleName',] }],
        IDField: [{ type: Input, args: ['IDField',] }],
        expandFirstLoad: [{ type: Input, args: ['expandFirstLoad',] }],
        gridTitle: [{ type: ViewChild, args: ['gridTitle',] }],
        treeTitle: [{ type: ViewChild, args: ['treeTitle',] }],
        lastRanLbl: [{ type: ViewChild, args: ['lastRanLbl',] }],
        divBox: [{ type: ViewChild, args: ["divBox",] }],
        tree: [{ type: ViewChild, args: ['tree',] }],
        mainHGroup: [{ type: ViewChild, args: ['mainHGroup',] }],
        treeContainer: [{ type: ViewChild, args: ['treeContainer',] }],
        customGrid: [{ type: ViewChild, args: ['customGrid',] }],
        summaryGrid: [{ type: ViewChild, args: ['summaryGrid',] }],
        gridContainer: [{ type: ViewChild, args: ['gridContainer',] }]
    };
    return SwtSummary;
}(Container));
export { SwtSummary };
if (false) {
    /** @type {?} */
    SwtSummary.prototype.styleName;
    /** @type {?} */
    SwtSummary.prototype.IDField;
    /** @type {?} */
    SwtSummary.prototype.expandFirstLoad;
    /** @type {?} */
    SwtSummary.prototype.gridTitle;
    /** @type {?} */
    SwtSummary.prototype.treeTitle;
    /** @type {?} */
    SwtSummary.prototype.lastRanLbl;
    /** @type {?} */
    SwtSummary.prototype.divBox;
    /** @type {?} */
    SwtSummary.prototype.tree;
    /** @type {?} */
    SwtSummary.prototype.mainHGroup;
    /** @type {?} */
    SwtSummary.prototype.treeContainer;
    /** @type {?} */
    SwtSummary.prototype.customGrid;
    /** @type {?} */
    SwtSummary.prototype.summaryGrid;
    /** @type {?} */
    SwtSummary.prototype.gridContainer;
    /**
     * JSON Objects
     *
     * @type {?}
     * @private
     */
    SwtSummary.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.lastRecievedJSON;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.prevRecievedJSON;
    /**
     * Communication Objects
     *
     * @type {?}
     */
    SwtSummary.prototype.baseURL;
    /** @type {?} */
    SwtSummary.prototype.actionMethod;
    /** @type {?} */
    SwtSummary.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.facilityAccess;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.supportAllCurrency;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.supportAllEntity;
    /**
     * Summary Tree Objects
     *
     * @type {?}
     */
    SwtSummary.prototype.treeOpenedItems;
    /** @type {?} */
    SwtSummary.prototype.treeClosedItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.gridOpenedItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.gridVisibleItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.verticalScrollPosition;
    /** @type {?} */
    SwtSummary.prototype.lastSelectedItem;
    /** @type {?} */
    SwtSummary.prototype.currentDivPos;
    /** @type {?} */
    SwtSummary.prototype.facilityId;
    /** @type {?} */
    SwtSummary.prototype.facilityName;
    /** @type {?} */
    SwtSummary.prototype.useGeneric;
    /** @type {?} */
    SwtSummary.prototype.scenarioTitle;
    /** @type {?} */
    SwtSummary.prototype.selectedscenario;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.resetFilter;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.columnData;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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