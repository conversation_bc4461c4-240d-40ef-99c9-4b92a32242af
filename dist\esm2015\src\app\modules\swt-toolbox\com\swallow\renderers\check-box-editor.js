/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
export class CheckBoxEditor {
    /*------------------------------------------ FUNCTIONS -------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        /*------------------------------------------ Private PARAMETERS ---------------------------------------------------------*/
        this.isBool = false; //isBool : variable to test the checkbox's value type (boolean or string)
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.row_index = -1;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.logger = new Logger('CheckBoxEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * init: function to be loaded once the item render is activated by the first click on the cell.
     * @return {?}
     */
    init() {
        this.logger.info('Method [init] -START- enabledFlag :', this.enableFlag);
        // First : create the Dom element.
        if (this.showHideCells) {
            this.$input = $(`<input  type="checkbox"  ${(this.enableFlag == false) ? 'disabled' : ''}  class="editor-checkbox"    />`);
        }
        else {
            this.$input = $(``);
        }
        $(this.args.container).empty().append(this.$input);
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        this.row_index = -1;
        this.logger.info('Method [init] -END-  ');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * loadValue : loading the value from the data provider into the input.
     * @param {?} item : the selected item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('Method [loadValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            this.defaultValue = item[this.args.column.field];
            this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']] != undefined ? this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field] : ""; // test if the loaded value is boolean (true/false) or string (N/Y)
            if (this.commonGrid.selectable) {
                if (this.defaultValue == 'true' || this.defaultValue == 'false' || this.defaultValue == true || this.defaultValue == false) {
                    this.isBool = true;
                }
                if (this.defaultValue == false || this.defaultValue === 'false' || this.defaultValue === 'N') {
                    this.$input.prop('checked', false);
                }
                else if (this.defaultValue == true || this.defaultValue === 'true' || this.defaultValue === 'Y') {
                    this.$input.prop('checked', true);
                }
            }
        }
        this.logger.info('Method [loadValue] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * serializeValue : return the state of the checkbox (checked or not)
     * @return {?}
     */
    serializeValue() {
        this.logger.info('Method [serializeValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            /** @type {?} */
            let item = this.args.item;
            //For the first click on the render , the CheckBox will change the value within the opposite precede state.
            if (this.enableFlag == true) {
                this.$input.prop('checked', !this.$input.prop('checked'));
            }
            //CheckBox's Event change handler
            this.$input.change((/**
             * @return {?}
             */
            () => {
                if (this.enableFlag == true) {
                    if (this.$input.prop('checked') === true && this.isBool) {
                        item[this.args.column.field] = 'true';
                        item.slickgrid_rowcontent[this.args.column.field].content = "true";
                    }
                    else if (this.$input.prop('checked') === true && !this.isBool) {
                        item[this.args.column.field] = 'Y';
                        item.slickgrid_rowcontent[this.args.column.field].content = 'Y';
                    }
                    else if (this.$input.prop('checked') === false && this.isBool) {
                        item[this.args.column.field] = 'false';
                        item.slickgrid_rowcontent[this.args.column.field].content = "false";
                    }
                    else if (this.$input.prop('checked') === false && !this.isBool) {
                        item[this.args.column.field] = 'N';
                        item.slickgrid_rowcontent[this.args.column.field].content = 'N';
                    }
                    this.commonGrid.columnSelectChanged(this.args.item);
                    if (this.commonGrid.selectable /* && this.row_index  == "0" && this.args.item['id'] == "0"*/) {
                        setTimeout((/**
                         * @return {?}
                         */
                        () => {
                            /** @type {?} */
                            var target = {
                                name: this.args.column.name,
                                field: this.args.column.field,
                                editor: (this.args.column.editor != null && this.args.column.editor != undefined) ? this.args.column.editor.name : null,
                                formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                                data: this.args.item
                            };
                            /** @type {?} */
                            var ListEvent = {
                                rowIndex: this.args.item['id'],
                                cellIndex: this.args.column['columnorder'],
                                columnIndex: this.args.column['columnorder'],
                                target: target
                            };
                            this.commonGrid.ITEM_CLICK.emit(ListEvent);
                        }), 0);
                    }
                    // -- check if changed
                    this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => ((x.crud_data.id == this.args.item.id))));
                    ;
                    if (this.CRUD_CHANGES_DATA != undefined && this.CRUD_CHANGES_DATA != null) {
                        this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : null;
                    }
                    /*console.log('===> this.originalDefaultValue :',this.originalDefaultValue)
                    console.log('===> this.defaultValue :',this.defaultValue)
                    console.log('===>  this.args.item['+this.args.column.field+']) :', this.args.item[this.args.column.field])
                    console.log('===>  this.originalDefaultValue == this.args.item[this.args.column.field] :', (this.originalDefaultValue == this.args.item[this.args.column.field]))
                    */
                    if ((this.originalDefaultValue == null && this.defaultValue != this.args.item[this.args.column.field]) || ((this.originalDefaultValue != null) && (this.originalDefaultValue != this.args.item[this.args.column.field]))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                            if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field] = { 'content': this.defaultValue };
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            this.commonGrid.spyChanges({ field: this.args.column.field });
                            this.commonGrid.updateCrud(updatedObject);
                            SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                            //ITEM_CHANGED
                            /** @type {?} */
                            var event = {
                                rowIndex: this.args.item.id,
                                target: this.args.column.editor.name,
                                dataField: this.args.column.field,
                                listData: Object.assign({}, updatedObject)
                            };
                            this.commonGrid.ITEM_CHANGED.emit(event);
                        }
                    }
                    else if (this.originalDefaultValue == this.args.item[this.args.column.field]) {
                        /** @type {?} */
                        var crudChange = this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => ((x.crud_data.id == this.args.item.id))));
                        /** @type {?} */
                        var ch = String("U(" + this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => ((x.crud_data.id == this.args.item.id))));
                                this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        //- Do not emit SpyNoChanges on the grid unless there is other changes.
                        if (this.commonGrid.changes.getValues().length == 0) {
                            this.commonGrid.spyNoChanges({ field: this.args.column.field });
                        }
                        //- if the column is displayed as checkbox , then we have to emit ITEM_CHANGE event even there is no changed within the current row , because this event will detect the check/uncheck of the header column.
                        if (this.args.column.checkBoxVisibility) {
                            this.commonGrid.ITEM_CHANGED.emit(null);
                        }
                        SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                    }
                    if (this.row_index == -1 && this.args.item.id == "0") {
                        this.row_index = this.args.item.id;
                    }
                }
            }));
            this.$input.change();
        }
        this.logger.info('Method [serializeValue] -END-');
        return this.$input.prop('checked');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * focus : the input gets the focus in.
     * @return {?}
     */
    focus() {
        this.logger.info('Method [focus] -START/END-');
        this.$input.focus();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * applyValue : will update the value of the checkbox in the data provider (N/Y) or (true/false).
     * @param {?} item : the selected current item
     * @param {?} state : the state of the checkbox (true/false)
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('Method [applyValue] -START/END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * isValueChanged : dispatch the change event.
     * @return {?}
     */
    isValueChanged() {
        this.logger.info('Method [isValueChanged] -START/END-');
        return false;
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * validate :
     * @return {?}
     */
    validate() {
        this.logger.info('Method [validate] -START/END-');
        return { valid: true, msg: null };
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * destroy : Called once the focus is out and the item render become hidden.
     * @return {?}
     */
    destroy() {
        this.logger.info('Method [destroy] -START/END-');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.isBool;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.row_index;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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