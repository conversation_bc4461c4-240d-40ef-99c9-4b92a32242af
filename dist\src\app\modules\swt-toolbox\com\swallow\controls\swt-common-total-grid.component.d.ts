import { AfterViewInit, OnInit, On<PERSON><PERSON>roy, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, AutoTooltipExtension, CollectionService } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
export declare class SwtTotalCommonGrid extends SwtCommonGrid implements OnInit, AfterViewInit, OnDestroy {
    protected el: ElementRef;
    protected commonService: CommonService;
    protected autoTooltipExtension: AutoTooltipExtension;
    protected extensionUtility: ExtensionUtility;
    protected sharedService: SharedService;
    protected translate: TranslateService;
    constructor(el: ElementRef, commonService: CommonService, autoTooltipExtension: AutoTooltipExtension, extensionUtility: ExtensionUtility, sharedService: SharedService, collectionService: CollectionService, translate: TranslateService);
    initialColumnsToSkip: number;
}
