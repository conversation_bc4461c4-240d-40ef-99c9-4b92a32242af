/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, Input, ElementRef } from "@angular/core";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtCanvas = /** @class */ (function (_super) {
    tslib_1.__extends(SwtCanvas, _super);
    function SwtCanvas(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions------------------------------------------------------------------------------------------------
        _this.border = true;
        $($(_this.elem.nativeElement)[0]).attr('selector', 'SwtCanvas');
        return _this;
    }
    /**
     * @return {?}
     */
    SwtCanvas.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        if (String(this.border) === "false") {
            $($(this.elem.nativeElement).children()[0]).removeClass('canvasControlBar').addClass('canvasControlBarWithoutBorder');
        }
        else {
            $($(this.elem.nativeElement).children()[0]).removeClass('canvasControlBarWithoutBorder').addClass('canvasControlBar');
        }
    };
    SwtCanvas.decorators = [
        { type: Component, args: [{
                    selector: 'SwtCanvas',
                    template: "\n     <div  fxLayout=\"row\" fxLayoutAlign=\"{{horizontalAlign}} {{verticalAlign}} \" fxLayoutGap=\"{{horizontalGap}}\" class=\"canvasLayout  {{styleName}}\" tabindex=\"-1\">\n        <ng-content></ng-content>\n        <ng-container #_container></ng-container>\n     </div>\n  ",
                    styles: ["\n             :host {\n               margin: 0px 0px 5px 0px;\n               display: block;\n               outline: none;\n             }\n             .canvasLayout {\n               box-sizing: border-box;\n               width: 100%;\n               outline:none;\n             }\n   "]
                }] }
    ];
    /** @nocollapse */
    SwtCanvas.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtCanvas.propDecorators = {
        border: [{ type: Input, args: ['border',] }],
        styleName: [{ type: Input, args: ['styleName',] }]
    };
    return SwtCanvas;
}(Container));
export { SwtCanvas };
if (false) {
    /** @type {?} */
    SwtCanvas.prototype.border;
    /** @type {?} */
    SwtCanvas.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    SwtCanvas.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtCanvas.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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