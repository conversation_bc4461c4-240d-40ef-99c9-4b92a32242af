/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
var 
//@dynamic
InputTextEditor = /** @class */ (function () {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    function InputTextEditor(args) {
        this.args = args;
        this.validationResult = true;
        this.logger = null;
        //public static commonGrid :any;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.commonGrid = this.args.column.params.grid;
        this.columnDef = this.args.column;
        this.maxChars = this.columnDef.maxChars;
        this.logger = new Logger('InputTextEditor', null, 0);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.init = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        var _this = this;
        if (this.columnDef.params.enableDisableCells) {
            this.enableFlag = this.columnDef.params.enableDisableCells(this.args.item, this.args.column.field);
        }
        else {
            this.enableFlag = true;
        }
        if (this.columnDef['properties'] && this.columnDef['properties']['enabled']) {
            this.enableFlag = this.columnDef['properties']['enabled'];
        }
        this.logger.info('method [init] -START- enableFlag :', this.enableFlag);
        this.loadValue(this.args.item);
        if (this.showHideCells) {
            if (this.args.item.property && this.args.item.property.toLowerCase().indexOf("password") != -1) {
                this.$input = $("<input id=\"input\" type=\"password\"   value=\"" + this.defaultValue + "\" ></input>");
            }
            else
                this.$input = $("<input  id=\"input\" class=\"text-style renderAsInput  " + ((this.enableFlag == false) ? 'disabled-text-editor' : '') + "\" type=\"text\" style=\"" + ((this.enableFlag == true) ? 'background-color: white !important;' : 'background-color: rgba(255, 204, 102, 1) !important;') + "\"    value=\"" + this.defaultValue + "\" ></input>");
        }
        else {
            this.$input = $("");
        }
        this.$input.appendTo(this.args.container);
        if (this.enableFlag) {
            this.$input.focus();
            this.$input.select();
        }
        this.$input.keypress((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            if (!document.getSelection().toString() && document.getElementById("input")['selectionStart'] == document.getElementById("input")['selectionEnd'] && event.target.value.length >= _this.maxChars)
                return false;
        }));
        this.$input.focusout((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            if (event.handleObj.type == "focusout") {
                _this.destroy();
                _this.commonGrid.gridObj.getEditorLock().commitCurrentEdit();
            }
        }));
        /** @type {?} */
        var target = {
            name: this.args.column.name,
            field: this.args.column.field,
            editor: this.args.column.editor,
            formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
            data: this.args.item
        };
        /** @type {?} */
        var ListEvent = {
            rowIndex: this.args.item.id,
            cellIndex: this.args.column.columnorder,
            columnIndex: this.args.column.columnorder,
            target: target
        };
        if (this.commonGrid.ITEM_FOCUS_IN.observers.length > 1) {
            /** @type {?} */
            var x = this.commonGrid.ITEM_FOCUS_IN.observers[0];
            this.commonGrid.ITEM_FOCUS_IN.observers = [];
            this.commonGrid.ITEM_FOCUS_IN.observers[0] = x;
        }
        this.commonGrid.ITEM_FOCUS_IN.emit(ListEvent);
        this.logger.info('method [init] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.destroy = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [destroy] -START-');
        this.applyValue(this.args.item, this.getValue());
        this.isValueChanged();
        if (this.showHideCells) {
            // focusout event .
            /** @type {?} */
            var target = {
                name: this.args.column.name,
                field: this.args.column.field,
                editor: this.args.column.editor,
                formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                data: this.args.item
            };
            /** @type {?} */
            var ListEvent = {
                rowIndex: this.args.item.id,
                cellIndex: this.args.column.columnorder,
                columnIndex: this.args.column.columnorder,
                target: target
            };
            if (this.commonGrid.ITEM_FOCUS_OUT.observers.length > 1) {
                /** @type {?} */
                var x = this.commonGrid.ITEM_FOCUS_OUT.observers[0];
                this.commonGrid.ITEM_FOCUS_OUT.observers = [];
                this.commonGrid.ITEM_FOCUS_OUT.observers[0] = x;
            }
            this.commonGrid.ITEM_FOCUS_OUT.emit(ListEvent);
            // if(this.$input) this.$input.remove();
        }
        this.logger.info('method [destroy] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.focus = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [focus] -START/END-');
        this.$input.focus();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.getValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [getValue] -START/END-');
        return this.$input.val();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    InputTextEditor.prototype.setValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    function (val) {
        this.logger.info('method [setValue] -START/END-');
        this.$input.val(val);
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    InputTextEditor.prototype.loadValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('method [loadValue] -START-');
        if (this.showHideCells) {
            this.defaultValue = item[this.args.column.field] != undefined ? item[this.args.column.field] : '';
            if (this.commonGrid.originalDataprovider && this.commonGrid.originalDataprovider.size > 0)
                this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']] != undefined ? this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field] : "";
        }
        this.logger.info('method [loadValue] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.save = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [save] - START/END');
        if (this.showHideCells) {
            this.args.commitChanges();
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.serializeValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [serializeValue] - START/END');
        // if($('input[type=password]').length != 1)
        return this.$input.val();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    InputTextEditor.prototype.applyValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        var _this = this;
        this.logger.info('method [applyValue] - START/END');
        if (this.showHideCells && this.enableFlag == true) {
            item[this.args.column.field] = state;
            item.slickgrid_rowcontent[this.args.column.field] = { 'content': state };
            /** @type {?} */
            var crudChange = this.commonGrid.changes.getValues().find((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
            //console.log('-------crudChange :',crudChange);
            if (crudChange)
                crudChange['crud_data'][this.args.column.field] = state;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.isValueChanged = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        var _this = this;
        if (this.showHideCells) {
            setTimeout((/**
             * @return {?}
             */
            function () {
                /** @type {?} */
                var isChanged = (_this.$input.val() !== _this.defaultValue);
                _this.logger.info('method [isValueChanged] , returned value: "' + isChanged + '" - START/END');
                if (_this.enableFlag == true) {
                    _this.CRUD_CHANGES_DATA = _this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                    if (_this.CRUD_CHANGES_DATA != undefined && _this.CRUD_CHANGES_DATA != null) {
                        _this.originalDefaultValue = _this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? _this.CRUD_CHANGES_DATA['crud_original_data'][_this.args.column.field] : undefined;
                        if (_this.originalDefaultValue == undefined)
                            _this.originalDefaultValue = "";
                    }
                    if ((_this.originalDefaultValue == null && isChanged) || (((_this.originalDefaultValue != null)) && (_this.originalDefaultValue != _this.$input.val()))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (_this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = _this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return ((x.crud_data.id == _this.args.item.id) && (x.crud_operation == "I")); }));
                            if (crudInsert != undefined && crudInsert[_this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (var key in _this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = _this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (var key in _this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, _this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[_this.args.column.field] = _this.defaultValue;
                            original_row['slickgrid_rowcontent'][_this.args.column.field].content = _this.defaultValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: _this.args.item.id,
                                columnIndex: _this.args.column.columnorder,
                                new_row: _this.args.item,
                                original_row: original_row,
                                changedColumn: _this.args.column.field,
                                oldValue: _this.defaultValue,
                                newValue: _this.args.item[_this.args.column.field]
                            };
                            _this.validationResult = _this.commonGrid.validate(_this.args.item, original_row, false);
                            /* console.log('[InputTextEditor] validationResult newValue :',this.args.item)
                             console.log('[InputTextEditor] validationResult oldValue :',original_row)
                             console.log('[InputTextEditor] validationResult resultat :',this.validationResult )*/
                            if (_this.validationResult) {
                                _this.commonGrid.updateCrud(updatedObject);
                                _this.commonGrid.spyChanges({ field: _this.args.column.field });
                                //ITEM_CHANGED
                                /** @type {?} */
                                var event = {
                                    rowIndex: _this.args.item.id,
                                    target: _this.args.column.editor,
                                    dataField: _this.args.column.field,
                                    listData: tslib_1.__assign({}, updatedObject)
                                };
                                if (_this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                                    /** @type {?} */
                                    var x = _this.commonGrid.ITEM_CHANGED.observers[0];
                                    _this.commonGrid.ITEM_CHANGED.observers = [];
                                    _this.commonGrid.ITEM_CHANGED.observers[0] = x;
                                }
                                _this.commonGrid.ITEM_CHANGED.emit(event);
                                if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                    /** @type {?} */
                                    var x = SwtCommonGridItemRenderChanges.observers[0];
                                    SwtCommonGridItemRenderChanges.observers = [];
                                    SwtCommonGridItemRenderChanges.observers[0] = x;
                                }
                                SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.$input.val(), id: _this.args.item.id });
                            }
                            else {
                                isChanged = false;
                                if (_this.originalDefaultValue == _this.$input.val()) {
                                    /** @type {?} */
                                    var crudChange = _this.commonGrid.changes.getValues().find((/**
                                     * @param {?} x
                                     * @return {?}
                                     */
                                    function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                    /** @type {?} */
                                    var ch = String("U(" + _this.args.column.field + ")");
                                    if (crudChange) {
                                        if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                                        }
                                        if (crudChange['crud_operation'] == "") {
                                            /** @type {?} */
                                            var crudChangeIndex = _this.commonGrid.changes.getValues().findIndex((/**
                                             * @param {?} x
                                             * @return {?}
                                             */
                                            function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                            _this.commonGrid.changes.remove(_this.commonGrid.changes.getKeys()[crudChangeIndex]);
                                        }
                                    }
                                }
                                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                                if (_this.commonGrid.changes.size() == 0)
                                    _this.commonGrid.spyNoChanges({ field: _this.args.column.field });
                                if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                    /** @type {?} */
                                    var x = SwtCommonGridItemRenderChanges.observers[0];
                                    SwtCommonGridItemRenderChanges.observers = [];
                                    SwtCommonGridItemRenderChanges.observers[0] = x;
                                }
                                SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.$input.val(), id: _this.args.item.id });
                            }
                        }
                    }
                    else if (_this.originalDefaultValue == _this.$input.val()) {
                        if (_this.commonGrid.changes.size() == 0)
                            _this.commonGrid.spyNoChanges({ field: _this.args.column.field });
                        /** @type {?} */
                        var crudChange = _this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                        /** @type {?} */
                        var ch = String("U(" + _this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = _this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                _this.commonGrid.changes.remove(_this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        if (isChanged) {
                            /** @type {?} */
                            var original_row = [];
                            for (var key in _this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = _this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (var key in _this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, _this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[_this.args.column.field] = _this.defaultValue;
                            original_row['slickgrid_rowcontent'][_this.args.column.field].content = _this.defaultValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: _this.args.item.id,
                                columnIndex: _this.args.column.columnorder,
                                new_row: _this.args.item,
                                original_row: original_row,
                                changedColumn: _this.args.column.field,
                                oldValue: _this.defaultValue,
                                newValue: _this.args.item[_this.args.column.field]
                            };
                            //ITEM_CHANGED
                            /** @type {?} */
                            var event = {
                                rowIndex: _this.args.item.id,
                                target: _this.args.column.editor,
                                dataField: _this.args.column.field,
                                listData: tslib_1.__assign({}, updatedObject)
                            };
                            if (_this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                                /** @type {?} */
                                var x = _this.commonGrid.ITEM_CHANGED.observers[0];
                                _this.commonGrid.ITEM_CHANGED.observers = [];
                                _this.commonGrid.ITEM_CHANGED.observers[0] = x;
                            }
                            _this.commonGrid.ITEM_CHANGED.emit(event);
                        }
                    }
                }
                //  console.log('this.commonGrid.changes', this.commonGrid.changes)
                return isChanged;
            }), 0);
        }
        else {
            return false;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    InputTextEditor.prototype.validate = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [validate] - START/END');
        return {
            valid: true,
            msg: null
        };
    };
    return InputTextEditor;
}());
//@dynamic
export { InputTextEditor };
if (false) {
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.validationResult;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.logger;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_DATA;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    InputTextEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.columnDef;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.maxChars;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5wdXQtdGV4dC1lZGl0b3IuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvcmVuZGVyZXJzL2lucHV0LXRleHQtZWRpdG9yLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7OztJQUVNLENBQUMsR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFDO0FBQzNCLE9BQU8sMEJBQTBCLENBQUM7QUFJbEMsT0FBTyxFQUFFLE1BQU0sRUFBRSxNQUFNLDJCQUEyQixDQUFDO0FBQ25ELE9BQU8sRUFBRSw4QkFBOEIsRUFBRSxNQUFNLDZCQUE2QixDQUFDOztBQUU3RTs7O0lBaUJJLGlJQUFpSTtJQUNqSSx5QkFBb0IsSUFBUztRQUFULFNBQUksR0FBSixJQUFJLENBQUs7UUFoQnJCLHFCQUFnQixHQUFHLElBQUksQ0FBQztRQUd4QixXQUFNLEdBQVcsSUFBSSxDQUFDOztRQUV0QixtQkFBYyxHQUFVLGdCQUFnQixDQUFDO1FBQ3pDLGNBQVMsR0FBVSxXQUFXLENBQUM7UUFDL0IsdUJBQWtCLEdBQVUsb0JBQW9CLENBQUM7UUFDakQsc0JBQWlCLEdBQUUsSUFBSSxDQUFDO1FBQ3hCLHlCQUFvQixHQUFFLElBQUksQ0FBQztRQUUzQixrQkFBYSxHQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDOUYsZUFBVSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUM7UUFDMUMsY0FBUyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDO1FBQzdCLGFBQVEsR0FBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQztRQUd0QyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLGlCQUFpQixFQUFFLElBQUksRUFBQyxDQUFDLENBQUMsQ0FBQztRQUNwRCxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUM7SUFDaEIsQ0FBQztJQUNELGlJQUFpSTs7Ozs7SUFDMUgsOEJBQUk7Ozs7SUFBWDtRQUFBLGlCQWdFQztRQS9ERyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLGtCQUFrQixFQUM1QztZQUNJLElBQUksQ0FBQyxVQUFVLEdBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsa0JBQWtCLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7U0FDdEc7YUFFRDtZQUNJLElBQUksQ0FBQyxVQUFVLEdBQUMsSUFBSSxDQUFDO1NBQ3hCO1FBQ0QsSUFBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDeEUsSUFBSSxDQUFDLFVBQVUsR0FBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1NBQzVEO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsb0NBQW9DLEVBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBRSxDQUFDO1FBRXhFLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMvQixJQUFHLElBQUksQ0FBQyxhQUFhLEVBQ3JCO1lBQ0ksSUFBRyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTtnQkFDM0YsSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMscURBQThDLElBQUksQ0FBQyxZQUFZLGlCQUFhLENBQUMsQ0FBQzthQUNqRzs7Z0JBQ0QsSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsNkRBQXdELENBQUMsSUFBSSxDQUFDLFVBQVUsSUFBSSxLQUFLLENBQUUsQ0FBRSxDQUFDLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxDQUFDLEVBQUUsbUNBQXlCLENBQUMsSUFBSSxDQUFDLFVBQVUsSUFBSSxJQUFJLENBQUUsQ0FBQyxDQUFDLENBQUMscUNBQXFDLENBQUMsQ0FBQyxDQUFDLHNEQUFzRCx1QkFBZSxJQUFJLENBQUMsWUFBWSxpQkFBYSxDQUFDLENBQUM7U0FDeFU7YUFBSTtZQUNELElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1NBQ3ZCO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUMxQyxJQUFHLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDaEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFBO1NBQ3ZCO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFROzs7O1FBQUMsVUFBQyxLQUFLO1lBQ3ZCLElBQUcsQ0FBQyxRQUFRLENBQUMsWUFBWSxFQUFFLENBQUMsUUFBUSxFQUFFLElBQUksUUFBUSxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLFFBQVEsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUMsY0FBYyxDQUFDLElBQUssS0FBSyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsTUFBTSxJQUFHLEtBQUksQ0FBQyxRQUFRO2dCQUFFLE9BQU8sS0FBSyxDQUFDO1FBQ2pOLENBQUMsRUFBQyxDQUFDO1FBQ0gsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFROzs7O1FBQUMsVUFBQyxLQUFLO1lBQ3ZCLElBQUcsS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLElBQUksVUFBVSxFQUFFO2dCQUNuQyxLQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7Z0JBQ2YsS0FBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsYUFBYSxFQUFFLENBQUMsaUJBQWlCLEVBQUUsQ0FBQzthQUUvRDtRQUVMLENBQUMsRUFBQyxDQUFDOztZQUNDLE1BQU0sR0FBRztZQUNULElBQUksRUFBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJO1lBQzFCLEtBQUssRUFBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLO1lBQzVCLE1BQU0sRUFBRyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNO1lBQ2hDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsSUFBRyxJQUFJLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxJQUFJLFNBQVMsQ0FBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJO1lBQ25JLElBQUksRUFBRyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUk7U0FDeEI7O1lBQ0csU0FBUyxHQUFFO1lBQ1gsUUFBUSxFQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDMUIsU0FBUyxFQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVc7WUFDdEMsV0FBVyxFQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVc7WUFDeEMsTUFBTSxFQUFDLE1BQU07U0FDaEI7UUFDRCxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDOztnQkFDN0MsQ0FBQyxHQUFnQixJQUFJLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO1lBQy9ELElBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLFNBQVMsR0FBQyxFQUFFLENBQUE7WUFDMUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxHQUFDLENBQUMsQ0FBQztTQUNoRDtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUU5QyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO0lBQzVDLENBQUM7SUFDRCxpSUFBaUk7Ozs7O0lBQzFILGlDQUFPOzs7O0lBQWQ7UUFDSSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywwQkFBMEIsQ0FBQyxDQUFDO1FBQzdDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFFLENBQUM7UUFDbEQsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDO1FBQ3RCLElBQUcsSUFBSSxDQUFDLGFBQWEsRUFDckI7OztnQkFHUSxNQUFNLEdBQUc7Z0JBQ1QsSUFBSSxFQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUk7Z0JBQzFCLEtBQUssRUFBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLO2dCQUM1QixNQUFNLEVBQUssSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTTtnQkFDbEMsU0FBUyxFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxJQUFHLElBQUksSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLElBQUksU0FBUyxDQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUk7Z0JBQ25JLElBQUksRUFBRyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUk7YUFDeEI7O2dCQUNHLFNBQVMsR0FBRTtnQkFDWCxRQUFRLEVBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDMUIsU0FBUyxFQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVc7Z0JBQ3RDLFdBQVcsRUFBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXO2dCQUN4QyxNQUFNLEVBQUMsTUFBTTthQUNoQjtZQUNELElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBQyxDQUFDLEVBQUM7O29CQUM5QyxDQUFDLEdBQWdCLElBQUksQ0FBQyxVQUFVLENBQUMsY0FBYyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUM7Z0JBQ2hFLElBQUksQ0FBQyxVQUFVLENBQUMsY0FBYyxDQUFDLFNBQVMsR0FBQyxFQUFFLENBQUE7Z0JBQzNDLElBQUksQ0FBQyxVQUFVLENBQUMsY0FBYyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsR0FBQyxDQUFDLENBQUM7YUFDakQ7WUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFL0Msd0NBQXdDO1NBQzNDO1FBR0QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsd0JBQXdCLENBQUMsQ0FBQztJQUMvQyxDQUFDO0lBQ0QsaUlBQWlJOzs7OztJQUMxSCwrQkFBSzs7OztJQUFaO1FBQ0ksSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsNEJBQTRCLENBQUMsQ0FBQztRQUMvQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDO0lBQ3hCLENBQUM7SUFDRCxpSUFBaUk7Ozs7O0lBQzFILGtDQUFROzs7O0lBQWY7UUFDSSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywrQkFBK0IsQ0FBQyxDQUFDO1FBQ2xELE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQztJQUM3QixDQUFDO0lBQ0QsaUlBQWlJOzs7Ozs7SUFDMUgsa0NBQVE7Ozs7O0lBQWYsVUFBZ0IsR0FBVztRQUN2QixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywrQkFBK0IsQ0FBQyxDQUFDO1FBQ2xELElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ3pCLENBQUM7SUFDRCxpSUFBaUk7Ozs7OztJQUMxSCxtQ0FBUzs7Ozs7SUFBaEIsVUFBaUIsSUFBUztRQUN0QixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDO1FBQy9DLElBQUcsSUFBSSxDQUFDLGFBQWEsRUFBQztZQUNsQixJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBRyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1lBQ2pHLElBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxvQkFBb0IsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLG9CQUFvQixDQUFDLElBQUksR0FBQyxDQUFDO2dCQUNsRixJQUFJLENBQUMsb0JBQW9CLEdBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7U0FBUztRQUNoTixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywwQkFBMEIsQ0FBQyxDQUFDO0lBQ2pELENBQUM7SUFDRCxpSUFBaUk7Ozs7O0lBQzFILDhCQUFJOzs7O0lBQVg7UUFDSSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywyQkFBMkIsQ0FBQyxDQUFDO1FBQzlDLElBQUcsSUFBSSxDQUFDLGFBQWEsRUFBQztZQUNsQixJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1NBQzdCO0lBQ0wsQ0FBQztJQUNELGlJQUFpSTs7Ozs7SUFDMUgsd0NBQWM7Ozs7SUFBckI7UUFDSSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxxQ0FBcUMsQ0FBQyxDQUFDO1FBQ3pELDRDQUE0QztRQUMzQyxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUM7SUFDN0IsQ0FBQztJQUNELGlJQUFpSTs7Ozs7OztJQUMxSCxvQ0FBVTs7Ozs7O0lBQWpCLFVBQWtCLElBQVMsRUFBRSxLQUFVO1FBQXZDLGlCQVNDO1FBUkcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsaUNBQWlDLENBQUMsQ0FBQztRQUNwRCxJQUFHLElBQUksQ0FBQyxhQUFhLElBQUksSUFBSSxDQUFDLFVBQVUsSUFBSSxJQUFJLEVBQUM7WUFDN0MsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQztZQUNyQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBQyxTQUFTLEVBQUcsS0FBSyxFQUFFLENBQUM7O2dCQUNyRSxVQUFVLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLENBQUMsSUFBSTs7OztZQUFDLFVBQUEsQ0FBQyxJQUFFLE9BQUEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxJQUFJLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFJLEVBQTFDLENBQTBDLEVBQUM7WUFDeEcsZ0RBQWdEO1lBQ2hELElBQUcsVUFBVTtnQkFBQyxVQUFVLENBQUMsV0FBVyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUMsS0FBSyxDQUFDO1NBQ3ZFO0lBQ0wsQ0FBQztJQUNELGlJQUFpSTs7Ozs7SUFDMUgsd0NBQWM7Ozs7SUFBckI7UUFBQSxpQkF3TEM7UUF2TEcsSUFBRyxJQUFJLENBQUMsYUFBYSxFQUNyQjtZQUNJLFVBQVU7OztZQUFDOztvQkFFUCxTQUFTLEdBQUcsQ0FBQyxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxLQUFLLEtBQUksQ0FBQyxZQUFZLENBQUM7Z0JBQ3pELEtBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLDZDQUE2QyxHQUFDLFNBQVMsR0FBQyxlQUFlLENBQUMsQ0FBQztnQkFFMUYsSUFBRyxLQUFJLENBQUMsVUFBVSxJQUFJLElBQUksRUFBQztvQkFDdkIsS0FBSSxDQUFDLGlCQUFpQixHQUFDLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUk7Ozs7b0JBQUMsVUFBQSxDQUFDLElBQUUsT0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLElBQUksS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUcsRUFBekMsQ0FBeUMsRUFBQyxDQUFDO29CQUM5RyxJQUFHLEtBQUksQ0FBQyxpQkFBaUIsSUFBSSxTQUFTLElBQUksS0FBSSxDQUFDLGlCQUFpQixJQUFJLElBQUksRUFBQzt3QkFDckUsS0FBSSxDQUFDLG9CQUFvQixHQUFFLEtBQUksQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLFNBQVMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixDQUFDLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUEsQ0FBQyxDQUFDLFNBQVMsQ0FBQzt3QkFDdkssSUFBRyxLQUFJLENBQUMsb0JBQW9CLElBQUksU0FBUzs0QkFBRSxLQUFJLENBQUMsb0JBQW9CLEdBQUUsRUFBRSxDQUFDO3FCQUM1RTtvQkFFRCxJQUFJLENBQUMsS0FBSSxDQUFDLG9CQUFvQixJQUFJLElBQUksSUFBSyxTQUFTLENBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsb0JBQW9CLElBQUksSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUksQ0FBQyxvQkFBb0IsSUFBSSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUUsRUFDdEo7OzRCQUVRLGFBQWEsR0FBQyxLQUFLO3dCQUN2QixJQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7O2dDQUMzQyxVQUFVLEdBQUcsS0FBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLENBQUMsSUFBSTs7Ozs0QkFBQyxVQUFBLENBQUMsSUFBRSxPQUFBLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsSUFBSSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxjQUFjLElBQUksR0FBRyxDQUFDLENBQUcsRUFBdEUsQ0FBc0UsRUFBQzs0QkFDcEksSUFBRyxVQUFVLElBQUcsU0FBUyxJQUFJLFVBQVUsQ0FBQyxLQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUM7Z0NBQUUsYUFBYSxHQUFFLElBQUksQ0FBQzt5QkFDdkc7d0JBQ0QsSUFBRyxDQUFDLGFBQWEsRUFBQzs7O2dDQUVWLFlBQVksR0FBRyxFQUFFOzRCQUNyQixLQUFJLElBQUksR0FBRyxJQUFJLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFDO2dDQUMxQixJQUFHLEdBQUcsSUFBSSxzQkFBc0IsRUFBRTtvQ0FDOUIsWUFBWSxDQUFDLEdBQUcsQ0FBQyxHQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lDQUN6QztxQ0FBSTtvQ0FDRCxNQUFNO2lDQUNUOzZCQUNKOzRCQUNELFlBQVksQ0FBQyxzQkFBc0IsQ0FBQyxHQUFJLEVBQUUsQ0FBQzs0QkFDM0MsS0FBSSxJQUFJLEdBQUcsSUFBSSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxFQUFFO2dDQUVuRCxZQUFZLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxHQUFHLENBQUMsd0JBQU8sS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDOzZCQUVoRzs0QkFDRCxZQUFZLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUMsS0FBSSxDQUFDLFlBQVksQ0FBQzs0QkFDdkQsWUFBWSxDQUFDLHNCQUFzQixDQUFDLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxHQUFDLEtBQUksQ0FBQyxZQUFZLENBQUM7O2dDQUNuRixhQUFhLEdBQUU7Z0NBQ2YsUUFBUSxFQUFFLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0NBQzNCLFdBQVcsRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXO2dDQUN6QyxPQUFPLEVBQUcsS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJO2dDQUN4QixZQUFZLEVBQUUsWUFBWTtnQ0FDMUIsYUFBYSxFQUFHLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUs7Z0NBQ3RDLFFBQVEsRUFBQyxLQUFJLENBQUMsWUFBWTtnQ0FDMUIsUUFBUSxFQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQzs2QkFDbEQ7NEJBRUQsS0FBSSxDQUFDLGdCQUFnQixHQUFJLEtBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFDLFlBQVksRUFBQyxLQUFLLENBQUMsQ0FBSzs0QkFDekY7O2tIQUVzRjs0QkFFdEYsSUFBRyxLQUFJLENBQUMsZ0JBQWdCLEVBQUM7Z0NBQ3JCLEtBQUksQ0FBQyxVQUFVLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dDQUMxQyxLQUFJLENBQUMsVUFBVSxDQUFDLFVBQVUsQ0FBQyxFQUFFLEtBQUssRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDOzs7b0NBRzFELEtBQUssR0FBRTtvQ0FDUCxRQUFRLEVBQUUsS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtvQ0FDM0IsTUFBTSxFQUFJLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU07b0NBQ2pDLFNBQVMsRUFBRyxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLO29DQUNsQyxRQUFRLHVCQUFNLGFBQWEsQ0FBQztpQ0FDL0I7Z0NBQ0QsSUFBSSxLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFDLENBQUMsRUFBQzs7d0NBQzVDLENBQUMsR0FBZ0IsS0FBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQztvQ0FDOUQsS0FBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsU0FBUyxHQUFDLEVBQUUsQ0FBQTtvQ0FDekMsS0FBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxHQUFDLENBQUMsQ0FBQztpQ0FDL0M7Z0NBQ0QsS0FBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dDQUN6QyxJQUFJLDhCQUE4QixDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDOzt3Q0FDOUMsQ0FBQyxHQUFnQiw4QkFBOEIsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO29DQUNoRSw4QkFBOEIsQ0FBQyxTQUFTLEdBQUMsRUFBRSxDQUFBO29DQUMzQyw4QkFBOEIsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUMsQ0FBQyxDQUFDO2lDQUNqRDtnQ0FDRCw4QkFBOEIsQ0FBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUUsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxFQUFHLEVBQUUsRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDOzZCQUUzSDtpQ0FBSTtnQ0FDRCxTQUFTLEdBQUMsS0FBSyxDQUFDO2dDQUNoQixJQUFHLEtBQUksQ0FBQyxvQkFBb0IsSUFBSSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxFQUNqRDs7d0NBQ1EsVUFBVSxHQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUk7Ozs7b0NBQUMsVUFBQSxDQUFDLElBQUUsT0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLElBQUksS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUksRUFBMUMsQ0FBMEMsRUFBQzs7d0NBQ3BHLEVBQUUsR0FBSSxNQUFNLENBQUMsSUFBSSxHQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssR0FBQyxHQUFHLENBQUM7b0NBQ2pELElBQUcsVUFBVSxFQUFDO3dDQUNWLElBQUssTUFBTSxDQUFDLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsR0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTs0Q0FDN0QsVUFBVSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsTUFBTSxDQUFDLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsR0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLENBQUU7eUNBQzVGOzZDQUFLLElBQUssTUFBTSxDQUFDLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsR0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTs0Q0FDbkUsVUFBVSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsTUFBTSxDQUFDLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsR0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUU7eUNBQzVGOzZDQUFLLElBQUssTUFBTSxDQUFDLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFOzRDQUMvRCxVQUFVLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFFO3lDQUN4Rjt3Q0FDRCxJQUFHLFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsRUFBRTs7Z0RBQy9CLGVBQWUsR0FBRyxLQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxTQUFTLEVBQUUsQ0FBQyxTQUFTOzs7OzRDQUFDLFVBQUEsQ0FBQyxJQUFFLE9BQUEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxJQUFJLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFJLEVBQTFDLENBQTBDLEVBQUM7NENBQ2xILEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxLQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsQ0FBQyxlQUFlLENBQUUsQ0FBQyxDQUFDO3lDQUN2RjtxQ0FDSjtpQ0FDSjtnQ0FDRCx1RUFBdUU7Z0NBQ3ZFLElBQUcsS0FBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQztvQ0FBRyxLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxFQUFFLEtBQUssRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDO2dDQUN6RyxJQUFJLDhCQUE4QixDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDOzt3Q0FDOUMsQ0FBQyxHQUFnQiw4QkFBOEIsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO29DQUNoRSw4QkFBOEIsQ0FBQyxTQUFTLEdBQUMsRUFBRSxDQUFBO29DQUMzQyw4QkFBOEIsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUMsQ0FBQyxDQUFDO2lDQUNqRDtnQ0FDRCw4QkFBOEIsQ0FBQyxJQUFJLENBQUMsRUFBQyxLQUFLLEVBQUUsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxFQUFHLEVBQUUsRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDOzZCQUMzSDt5QkFFSjtxQkFDSjt5QkFBSyxJQUFHLEtBQUksQ0FBQyxvQkFBb0IsSUFBSSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxFQUN2RDt3QkFDSSxJQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUM7NEJBQ2xDLEtBQUksQ0FBQyxVQUFVLENBQUMsWUFBWSxDQUFDLEVBQUUsS0FBSyxFQUFFLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUM7OzRCQUVoRSxVQUFVLEdBQUcsS0FBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLENBQUMsSUFBSTs7Ozt3QkFBQyxVQUFBLENBQUMsSUFBRSxPQUFBLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsSUFBSSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBSSxFQUExQyxDQUEwQyxFQUFDOzs0QkFDcEcsRUFBRSxHQUFJLE1BQU0sQ0FBQyxJQUFJLEdBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxHQUFDLEdBQUcsQ0FBQzt3QkFDakQsSUFBRyxVQUFVLEVBQUM7NEJBQ1YsSUFBSyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxHQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO2dDQUM3RCxVQUFVLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxHQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBRTs2QkFDNUY7aUNBQUssSUFBSyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsR0FBRyxHQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO2dDQUNuRSxVQUFVLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsR0FBRyxHQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBRTs2QkFDNUY7aUNBQUssSUFBSyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUU7Z0NBQy9ELFVBQVUsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxVQUFVLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUU7NkJBQ3hGOzRCQUVELElBQUcsVUFBVSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxFQUFFOztvQ0FDL0IsZUFBZSxHQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxDQUFDLFNBQVM7Ozs7Z0NBQUMsVUFBQSxDQUFDLElBQUUsT0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLElBQUksS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUksRUFBMUMsQ0FBMEMsRUFBQztnQ0FDbEgsS0FBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEtBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxDQUFDLGVBQWUsQ0FBRSxDQUFDLENBQUM7NkJBQ3ZGO3lCQUNKO3dCQUNELElBQUcsU0FBUyxFQUFDOztnQ0FDRCxZQUFZLEdBQUcsRUFBRTs0QkFDakIsS0FBSSxJQUFJLEdBQUcsSUFBSSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksRUFBQztnQ0FDMUIsSUFBRyxHQUFHLElBQUksc0JBQXNCLEVBQUU7b0NBQzlCLFlBQVksQ0FBQyxHQUFHLENBQUMsR0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztpQ0FDekM7cUNBQUk7b0NBQ0QsTUFBTTtpQ0FDVDs2QkFDSjs0QkFDRCxZQUFZLENBQUMsc0JBQXNCLENBQUMsR0FBSSxFQUFFLENBQUM7NEJBQzNDLEtBQUksSUFBSSxHQUFHLElBQUksS0FBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsRUFBRTtnQ0FFbkQsWUFBWSxDQUFDLHNCQUFzQixDQUFDLENBQUMsR0FBRyxDQUFDLHdCQUFPLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQzs2QkFFaEc7NEJBQ0QsWUFBWSxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFDLEtBQUksQ0FBQyxZQUFZLENBQUM7NEJBQ3ZELFlBQVksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sR0FBQyxLQUFJLENBQUMsWUFBWSxDQUFDOztnQ0FDbkYsYUFBYSxHQUFFO2dDQUNmLFFBQVEsRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFO2dDQUMzQixXQUFXLEVBQUUsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVztnQ0FDekMsT0FBTyxFQUFHLEtBQUksQ0FBQyxJQUFJLENBQUMsSUFBSTtnQ0FDeEIsWUFBWSxFQUFFLFlBQVk7Z0NBQzFCLGFBQWEsRUFBRyxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLO2dDQUN0QyxRQUFRLEVBQUMsS0FBSSxDQUFDLFlBQVk7Z0NBQzFCLFFBQVEsRUFBQyxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUM7NkJBQ2xEOzs7Z0NBR0csS0FBSyxHQUFFO2dDQUNQLFFBQVEsRUFBRSxLQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFO2dDQUMzQixNQUFNLEVBQUksS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTTtnQ0FDakMsU0FBUyxFQUFHLEtBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUs7Z0NBQ2xDLFFBQVEsdUJBQU0sYUFBYSxDQUFDOzZCQUMvQjs0QkFDRCxJQUFJLEtBQUksQ0FBQyxVQUFVLENBQUMsWUFBWSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDOztvQ0FDNUMsQ0FBQyxHQUFnQixLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO2dDQUM5RCxLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxTQUFTLEdBQUMsRUFBRSxDQUFBO2dDQUN6QyxLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUMsQ0FBQyxDQUFDOzZCQUMvQzs0QkFHRCxLQUFJLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7eUJBQzVDO3FCQUVaO2lCQUNKO2dCQUNILG1FQUFtRTtnQkFDakUsT0FBTyxTQUFTLENBQUM7WUFDckIsQ0FBQyxHQUFFLENBQUMsQ0FBQyxDQUFDO1NBQ0w7YUFBSTtZQUNELE9BQU8sS0FBSyxDQUFDO1NBQ2hCO0lBQ0wsQ0FBQztJQUNELGlJQUFpSTs7Ozs7SUFDMUgsa0NBQVE7Ozs7SUFBZjtRQUNJLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLCtCQUErQixDQUFDLENBQUM7UUFDbEQsT0FBTztZQUNILEtBQUssRUFBRSxJQUFJO1lBQ1gsR0FBRyxFQUFFLElBQUk7U0FDWixDQUFDO0lBQ04sQ0FBQztJQUdMLHNCQUFDO0FBQUQsQ0FBQyxBQS9XRCxJQStXQzs7Ozs7Ozs7SUE3V0csMkNBQWdDOzs7OztJQUNoQyxpQ0FBb0I7Ozs7O0lBQ3BCLHVDQUEwQjs7Ozs7SUFDMUIsaUNBQThCOztJQUU5Qix5Q0FBaUQ7O0lBQ2pELG9DQUF1Qzs7SUFDdkMsNkNBQXlEOztJQUN6RCw0Q0FBZ0M7O0lBQ2hDLCtDQUFtQzs7Ozs7SUFDbkMscUNBQW9COzs7OztJQUNwQix3Q0FBc0c7Ozs7O0lBQ3RHLHFDQUFrRDs7Ozs7SUFDbEQsb0NBQXFDOzs7OztJQUNyQyxtQ0FBMEM7Ozs7O0lBRTlCLCtCQUFpQiIsInNvdXJjZXNDb250ZW50IjpbIi8vaW1wb3J0ICQgZnJvbSAnanF1ZXJ5JztcclxuZGVjbGFyZSB2YXIgcmVxdWlyZTogYW55O1xyXG5jb25zdCAkID0gcmVxdWlyZSgnanF1ZXJ5Jyk7XHJcbmltcG9ydCAnanF1ZXJ5LXVpLWRpc3QvanF1ZXJ5LXVpJztcclxuaW1wb3J0IHsgT2JzZXJ2ZXIgfSBmcm9tICdyeGpzL29ic2VydmVyJztcclxuXHJcbmltcG9ydCB7IEVkaXRvciwgRmllbGRUeXBlLCBLZXlDb2RlIH0gZnJvbSAnYW5ndWxhci1zbGlja2dyaWQnO1xyXG5pbXBvcnQgeyBMb2dnZXIgfSBmcm9tIFwiLi4vbG9nZ2luZy9sb2dnZXIuc2VydmljZVwiO1xyXG5pbXBvcnQgeyBTd3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMgfSBmcm9tIFwiLi4vZXZlbnRzL3N3dC1ldmVudHMubW9kdWxlXCI7XHJcbi8vQGR5bmFtaWNcclxuZXhwb3J0IGNsYXNzIElucHV0VGV4dEVkaXRvciBpbXBsZW1lbnRzIEVkaXRvciB7XHJcblxyXG4gICAgcHJpdmF0ZSB2YWxpZGF0aW9uUmVzdWx0ID0gdHJ1ZTtcclxuICAgIHByaXZhdGUgJGlucHV0OiBhbnk7XHJcbiAgICBwcml2YXRlIGRlZmF1bHRWYWx1ZTogYW55O1xyXG4gICAgcHJpdmF0ZSBsb2dnZXI6IExvZ2dlciA9IG51bGw7XHJcbiAgICAvL3B1YmxpYyBzdGF0aWMgY29tbW9uR3JpZCA6YW55O1xyXG4gICAgcHVibGljICBDUlVEX09QRVJBVElPTjpzdHJpbmcgPSBcImNydWRfb3BlcmF0aW9uXCI7XHJcbiAgICBwdWJsaWMgIENSVURfREFUQTpzdHJpbmcgPSBcImNydWRfZGF0YVwiO1xyXG4gICAgcHVibGljICBDUlVEX09SSUdJTkFMX0RBVEE6c3RyaW5nID0gXCJjcnVkX29yaWdpbmFsX2RhdGFcIjtcclxuICAgIHB1YmxpYyAgQ1JVRF9DSEFOR0VTX0RBVEE9IG51bGw7XHJcbiAgICBwdWJsaWMgIG9yaWdpbmFsRGVmYXVsdFZhbHVlPSBudWxsO1xyXG4gICAgcHJpdmF0ZSBlbmFibGVGbGFnIDtcclxuICAgIHByaXZhdGUgc2hvd0hpZGVDZWxscz0gdGhpcy5hcmdzLmNvbHVtbi5wYXJhbXMuc2hvd0hpZGVDZWxscyggdGhpcy5hcmdzLml0ZW0sIHRoaXMuYXJncy5jb2x1bW4uZmllbGQpO1xyXG4gICAgcHJpdmF0ZSBjb21tb25HcmlkID0gdGhpcy5hcmdzLmNvbHVtbi5wYXJhbXMuZ3JpZDtcclxuICAgIHByaXZhdGUgY29sdW1uRGVmID0gdGhpcy5hcmdzLmNvbHVtbjtcclxuICAgIHByaXZhdGUgbWF4Q2hhcnM9IHRoaXMuY29sdW1uRGVmLm1heENoYXJzO1xyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIGFyZ3M6IGFueSkge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyID0gbmV3IExvZ2dlcignSW5wdXRUZXh0RWRpdG9yJywgbnVsbCwwKTtcclxuICAgICAgICB0aGlzLmluaXQoKTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIGluaXQoKTogdm9pZCB7XHJcbiAgICAgICAgaWYgKHRoaXMuY29sdW1uRGVmLnBhcmFtcy5lbmFibGVEaXNhYmxlQ2VsbHMpXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB0aGlzLmVuYWJsZUZsYWc9IHRoaXMuY29sdW1uRGVmLnBhcmFtcy5lbmFibGVEaXNhYmxlQ2VsbHMoIHRoaXMuYXJncy5pdGVtLCB0aGlzLmFyZ3MuY29sdW1uLmZpZWxkKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZVxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgdGhpcy5lbmFibGVGbGFnPXRydWU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmKHRoaXMuY29sdW1uRGVmWydwcm9wZXJ0aWVzJ10gJiYgdGhpcy5jb2x1bW5EZWZbJ3Byb3BlcnRpZXMnXVsnZW5hYmxlZCddICl7XHJcbiAgICAgICAgICAgIHRoaXMuZW5hYmxlRmxhZz0gdGhpcy5jb2x1bW5EZWZbJ3Byb3BlcnRpZXMnXVsnZW5hYmxlZCddO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbygnbWV0aG9kIFtpbml0XSAtU1RBUlQtIGVuYWJsZUZsYWcgOicsdGhpcy5lbmFibGVGbGFnICk7XHJcblxyXG4gICAgICAgIHRoaXMubG9hZFZhbHVlKHRoaXMuYXJncy5pdGVtKTtcclxuICAgICAgICBpZih0aGlzLnNob3dIaWRlQ2VsbHMpXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBpZih0aGlzLmFyZ3MuaXRlbS5wcm9wZXJ0eSAmJiB0aGlzLmFyZ3MuaXRlbS5wcm9wZXJ0eS50b0xvd2VyQ2FzZSgpLmluZGV4T2YoXCJwYXNzd29yZFwiKSAhPSAtMSkge1xyXG4gICAgICAgICAgICAgICAgdGhpcy4kaW5wdXQgPSAkKGA8aW5wdXQgaWQ9XCJpbnB1dFwiIHR5cGU9XCJwYXNzd29yZFwiICAgdmFsdWU9XCIke3RoaXMuZGVmYXVsdFZhbHVlfVwiID48L2lucHV0PmApO1xyXG4gICAgICAgICAgICB9IGVsc2VcclxuICAgICAgICAgICAgdGhpcy4kaW5wdXQgPSAkKGA8aW5wdXQgIGlkPVwiaW5wdXRcIiBjbGFzcz1cInRleHQtc3R5bGUgcmVuZGVyQXNJbnB1dCAgJHsgKHRoaXMuZW5hYmxlRmxhZyA9PSBmYWxzZSApICA/ICdkaXNhYmxlZC10ZXh0LWVkaXRvcicgOiAnJyB9XCIgdHlwZT1cInRleHRcIiBzdHlsZT1cIiR7KHRoaXMuZW5hYmxlRmxhZyA9PSB0cnVlICkgPyAnYmFja2dyb3VuZC1jb2xvcjogd2hpdGUgIWltcG9ydGFudDsnIDogJ2JhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyMDQsIDEwMiwgMSkgIWltcG9ydGFudDsnfVwiICAgIHZhbHVlPVwiJHt0aGlzLmRlZmF1bHRWYWx1ZX1cIiA+PC9pbnB1dD5gKTtcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgdGhpcy4kaW5wdXQgPSAkKGBgKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHRoaXMuJGlucHV0LmFwcGVuZFRvKHRoaXMuYXJncy5jb250YWluZXIpO1xyXG4gICAgICAgIGlmKHRoaXMuZW5hYmxlRmxhZykge1xyXG4gICAgICAgICAgICB0aGlzLiRpbnB1dC5mb2N1cygpO1xyXG4gICAgICAgICAgICB0aGlzLiRpbnB1dC5zZWxlY3QoKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdGhpcy4kaW5wdXQua2V5cHJlc3MoKGV2ZW50KT0+IHtcclxuICAgICAgICAgICAgaWYoIWRvY3VtZW50LmdldFNlbGVjdGlvbigpLnRvU3RyaW5nKCkgJiYgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXCJpbnB1dFwiKVsnc2VsZWN0aW9uU3RhcnQnXSA9PSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcImlucHV0XCIpWydzZWxlY3Rpb25FbmQnXSAgJiYgZXZlbnQudGFyZ2V0LnZhbHVlLmxlbmd0aD49IHRoaXMubWF4Q2hhcnMpIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9KTtcclxuICAgICAgICB0aGlzLiRpbnB1dC5mb2N1c291dCgoZXZlbnQpPT4ge1xyXG4gICAgICAgICAgICBpZihldmVudC5oYW5kbGVPYmoudHlwZSA9PSBcImZvY3Vzb3V0XCIpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGVzdHJveSgpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLmdyaWRPYmouZ2V0RWRpdG9yTG9jaygpLmNvbW1pdEN1cnJlbnRFZGl0KCk7XHJcblxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHZhciB0YXJnZXQgPSB7XHJcbiAgICAgICAgICAgIG5hbWU6dGhpcy5hcmdzLmNvbHVtbi5uYW1lLFxyXG4gICAgICAgICAgICBmaWVsZDp0aGlzLmFyZ3MuY29sdW1uLmZpZWxkLFxyXG4gICAgICAgICAgICBlZGl0b3I6ICB0aGlzLmFyZ3MuY29sdW1uLmVkaXRvcixcclxuICAgICAgICAgICAgZm9ybWF0dGVyOiAodGhpcy5hcmdzLmNvbHVtbi5mb3JtYXR0ZXIhPSBudWxsICYmIHRoaXMuYXJncy5jb2x1bW4uZm9ybWF0dGVyICE9IHVuZGVmaW5lZCApID8gdGhpcy5hcmdzLmNvbHVtbi5mb3JtYXR0ZXIubmFtZSA6IG51bGwsXHJcbiAgICAgICAgICAgIGRhdGEgOiB0aGlzLmFyZ3MuaXRlbVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgdmFyIExpc3RFdmVudCA9e1xyXG4gICAgICAgICAgICByb3dJbmRleDp0aGlzLmFyZ3MuaXRlbS5pZCxcclxuICAgICAgICAgICAgY2VsbEluZGV4OnRoaXMuYXJncy5jb2x1bW4uY29sdW1ub3JkZXIsXHJcbiAgICAgICAgICAgIGNvbHVtbkluZGV4OnRoaXMuYXJncy5jb2x1bW4uY29sdW1ub3JkZXIsXHJcbiAgICAgICAgICAgIHRhcmdldDp0YXJnZXRcclxuICAgICAgICB9O1xyXG4gICAgICAgIGlmKCB0aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19JTi5vYnNlcnZlcnMubGVuZ3RoPjEpe1xyXG4gICAgICAgICAgICB2YXIgeDogT2JzZXJ2ZXI8YW55Pj10aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19JTi5vYnNlcnZlcnNbMF07XHJcbiAgICAgICAgICAgIHRoaXMuY29tbW9uR3JpZC5JVEVNX0ZPQ1VTX0lOLm9ic2VydmVycz1bXVxyXG4gICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19JTi5vYnNlcnZlcnNbMF09eDtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5jb21tb25HcmlkLklURU1fRk9DVVNfSU4uZW1pdChMaXN0RXZlbnQpO1xyXG5cclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKCdtZXRob2QgW2luaXRdIC1FTkQtJyk7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHB1YmxpYyBkZXN0cm95KCk6IHZvaWQge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oJ21ldGhvZCBbZGVzdHJveV0gLVNUQVJULScpO1xyXG4gICAgICAgIHRoaXMuYXBwbHlWYWx1ZSh0aGlzLmFyZ3MuaXRlbSwgdGhpcy5nZXRWYWx1ZSgpICk7XHJcbiAgICAgICAgdGhpcy5pc1ZhbHVlQ2hhbmdlZCgpO1xyXG4gICAgICAgIGlmKHRoaXMuc2hvd0hpZGVDZWxscylcclxuICAgICAgICB7XHJcblxyXG4gICAgICAgICAgICAvLyBmb2N1c291dCBldmVudCAuXHJcbiAgICAgICAgICAgIHZhciB0YXJnZXQgPSB7XHJcbiAgICAgICAgICAgICAgICBuYW1lOnRoaXMuYXJncy5jb2x1bW4ubmFtZSxcclxuICAgICAgICAgICAgICAgIGZpZWxkOnRoaXMuYXJncy5jb2x1bW4uZmllbGQsXHJcbiAgICAgICAgICAgICAgICBlZGl0b3I6ICAgIHRoaXMuYXJncy5jb2x1bW4uZWRpdG9yLFxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVyOiAodGhpcy5hcmdzLmNvbHVtbi5mb3JtYXR0ZXIhPSBudWxsICYmIHRoaXMuYXJncy5jb2x1bW4uZm9ybWF0dGVyICE9IHVuZGVmaW5lZCApID8gdGhpcy5hcmdzLmNvbHVtbi5mb3JtYXR0ZXIubmFtZSA6IG51bGwsXHJcbiAgICAgICAgICAgICAgICBkYXRhIDogdGhpcy5hcmdzLml0ZW1cclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgdmFyIExpc3RFdmVudCA9e1xyXG4gICAgICAgICAgICAgICAgcm93SW5kZXg6dGhpcy5hcmdzLml0ZW0uaWQsXHJcbiAgICAgICAgICAgICAgICBjZWxsSW5kZXg6dGhpcy5hcmdzLmNvbHVtbi5jb2x1bW5vcmRlcixcclxuICAgICAgICAgICAgICAgIGNvbHVtbkluZGV4OnRoaXMuYXJncy5jb2x1bW4uY29sdW1ub3JkZXIsXHJcbiAgICAgICAgICAgICAgICB0YXJnZXQ6dGFyZ2V0XHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIGlmKCB0aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19PVVQub2JzZXJ2ZXJzLmxlbmd0aD4xKXtcclxuICAgICAgICAgICAgICAgIHZhciB4OiBPYnNlcnZlcjxhbnk+PXRoaXMuY29tbW9uR3JpZC5JVEVNX0ZPQ1VTX09VVC5vYnNlcnZlcnNbMF07XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19PVVQub2JzZXJ2ZXJzPVtdXHJcbiAgICAgICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuSVRFTV9GT0NVU19PVVQub2JzZXJ2ZXJzWzBdPXg7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLklURU1fRk9DVVNfT1VULmVtaXQoTGlzdEV2ZW50KTtcclxuXHJcbiAgICAgICAgICAgIC8vIGlmKHRoaXMuJGlucHV0KSB0aGlzLiRpbnB1dC5yZW1vdmUoKTtcclxuICAgICAgICB9XHJcblxyXG5cclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKCdtZXRob2QgW2Rlc3Ryb3ldIC1FTkQtJyk7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHB1YmxpYyBmb2N1cygpOiB2b2lkIHtcclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKCdtZXRob2QgW2ZvY3VzXSAtU1RBUlQvRU5ELScpO1xyXG4gICAgICAgIHRoaXMuJGlucHV0LmZvY3VzKCk7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHB1YmxpYyBnZXRWYWx1ZSgpIHtcclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKCdtZXRob2QgW2dldFZhbHVlXSAtU1RBUlQvRU5ELScpO1xyXG4gICAgICAgIHJldHVybiB0aGlzLiRpbnB1dC52YWwoKTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIHNldFZhbHVlKHZhbDogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbygnbWV0aG9kIFtzZXRWYWx1ZV0gLVNUQVJUL0VORC0nKTtcclxuICAgICAgICB0aGlzLiRpbnB1dC52YWwodmFsKTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIGxvYWRWYWx1ZShpdGVtOiBhbnkpIHtcclxuICAgICAgICB0aGlzLmxvZ2dlci5pbmZvKCdtZXRob2QgW2xvYWRWYWx1ZV0gLVNUQVJULScpO1xyXG4gICAgICAgIGlmKHRoaXMuc2hvd0hpZGVDZWxscyl7XHJcbiAgICAgICAgICAgIHRoaXMuZGVmYXVsdFZhbHVlID0gaXRlbVt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXSE9IHVuZGVmaW5lZCA/IGl0ZW1bdGhpcy5hcmdzLmNvbHVtbi5maWVsZF0gOiAnJztcclxuICAgICAgICAgICAgaWYodGhpcy5jb21tb25HcmlkLm9yaWdpbmFsRGF0YXByb3ZpZGVyICYmIHRoaXMuY29tbW9uR3JpZC5vcmlnaW5hbERhdGFwcm92aWRlci5zaXplPjApXHJcbiAgICAgICAgICAgICAgICB0aGlzLm9yaWdpbmFsRGVmYXVsdFZhbHVlID0gIHRoaXMuY29tbW9uR3JpZC5vcmlnaW5hbERhdGFwcm92aWRlclt0aGlzLmFyZ3MuaXRlbVsnaWQnXV0gIT0gdW5kZWZpbmVkID8gdGhpcy5jb21tb25HcmlkLm9yaWdpbmFsRGF0YXByb3ZpZGVyW3RoaXMuYXJncy5pdGVtWydpZCddXVt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXSA6IFwiXCI7ICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbygnbWV0aG9kIFtsb2FkVmFsdWVdIC1FTkQtJyk7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHB1YmxpYyBzYXZlKCkge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oJ21ldGhvZCBbc2F2ZV0gLSBTVEFSVC9FTkQnKTtcclxuICAgICAgICBpZih0aGlzLnNob3dIaWRlQ2VsbHMpe1xyXG4gICAgICAgICAgICB0aGlzLmFyZ3MuY29tbWl0Q2hhbmdlcygpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIHNlcmlhbGl6ZVZhbHVlKCkge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oJ21ldGhvZCBbc2VyaWFsaXplVmFsdWVdIC0gU1RBUlQvRU5EJyk7XHJcbiAgICAgICAvLyBpZigkKCdpbnB1dFt0eXBlPXBhc3N3b3JkXScpLmxlbmd0aCAhPSAxKVxyXG4gICAgICAgIHJldHVybiB0aGlzLiRpbnB1dC52YWwoKTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIGFwcGx5VmFsdWUoaXRlbTogYW55LCBzdGF0ZTogYW55KSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIuaW5mbygnbWV0aG9kIFthcHBseVZhbHVlXSAtIFNUQVJUL0VORCcpO1xyXG4gICAgICAgIGlmKHRoaXMuc2hvd0hpZGVDZWxscyAmJiB0aGlzLmVuYWJsZUZsYWcgPT0gdHJ1ZSl7XHJcbiAgICAgICAgICAgIGl0ZW1bdGhpcy5hcmdzLmNvbHVtbi5maWVsZF0gPSBzdGF0ZTtcclxuICAgICAgICAgICAgaXRlbS5zbGlja2dyaWRfcm93Y29udGVudFt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXSA9IHsnY29udGVudCcgOiBzdGF0ZSB9O1xyXG4gICAgICAgICAgICB2YXIgY3J1ZENoYW5nZSA9IHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldFZhbHVlcygpLmZpbmQoeD0+KCh4LmNydWRfZGF0YS5pZCA9PSB0aGlzLmFyZ3MuaXRlbS5pZCkgICApKTtcclxuICAgICAgICAgICAgLy9jb25zb2xlLmxvZygnLS0tLS0tLWNydWRDaGFuZ2UgOicsY3J1ZENoYW5nZSk7XHJcbiAgICAgICAgICAgIGlmKGNydWRDaGFuZ2UpY3J1ZENoYW5nZVsnY3J1ZF9kYXRhJ11bdGhpcy5hcmdzLmNvbHVtbi5maWVsZF09c3RhdGU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBwdWJsaWMgaXNWYWx1ZUNoYW5nZWQoKSB7XHJcbiAgICAgICAgaWYodGhpcy5zaG93SGlkZUNlbGxzKVxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBsZXQgaXNDaGFuZ2VkID0gKHRoaXMuJGlucHV0LnZhbCgpICE9PSB0aGlzLmRlZmF1bHRWYWx1ZSk7XHJcbiAgICAgICAgICAgIHRoaXMubG9nZ2VyLmluZm8oJ21ldGhvZCBbaXNWYWx1ZUNoYW5nZWRdICwgcmV0dXJuZWQgdmFsdWU6IFwiJytpc0NoYW5nZWQrJ1wiIC0gU1RBUlQvRU5EJyk7XHJcblxyXG4gICAgICAgICAgICBpZih0aGlzLmVuYWJsZUZsYWcgPT0gdHJ1ZSl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLkNSVURfQ0hBTkdFU19EQVRBPXRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldFZhbHVlcygpLmZpbmQoeD0+KCh4LmNydWRfZGF0YS5pZCA9PSB0aGlzLmFyZ3MuaXRlbS5pZCkgICkpO1xyXG4gICAgICAgICAgICAgICAgaWYodGhpcy5DUlVEX0NIQU5HRVNfREFUQSAhPSB1bmRlZmluZWQgJiYgdGhpcy5DUlVEX0NIQU5HRVNfREFUQSAhPSBudWxsKXtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLm9yaWdpbmFsRGVmYXVsdFZhbHVlID10aGlzLkNSVURfQ0hBTkdFU19EQVRBWydjcnVkX29yaWdpbmFsX2RhdGEnXSAhPSB1bmRlZmluZWQgPyB0aGlzLkNSVURfQ0hBTkdFU19EQVRBWydjcnVkX29yaWdpbmFsX2RhdGEnXVt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXTogdW5kZWZpbmVkO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmKHRoaXMub3JpZ2luYWxEZWZhdWx0VmFsdWUgPT0gdW5kZWZpbmVkKSB0aGlzLm9yaWdpbmFsRGVmYXVsdFZhbHVlID1cIlwiO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIGlmKCAodGhpcy5vcmlnaW5hbERlZmF1bHRWYWx1ZSA9PSBudWxsICYmICBpc0NoYW5nZWQgKSB8fCAoKCh0aGlzLm9yaWdpbmFsRGVmYXVsdFZhbHVlICE9IG51bGwpKSAmJiAodGhpcy5vcmlnaW5hbERlZmF1bHRWYWx1ZSAhPSB0aGlzLiRpbnB1dC52YWwoKSkgKSApXHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgdmFyIHRoZXJlSXNJbnNlcnQ9ZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5jb21tb25HcmlkLmNoYW5nZXMuZ2V0VmFsdWVzKCkubGVuZ3RoID4gMCApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgY3J1ZEluc2VydCA9IHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldFZhbHVlcygpLmZpbmQoeD0+KCh4LmNydWRfZGF0YS5pZCA9PSB0aGlzLmFyZ3MuaXRlbS5pZCkgJiYgKHguY3J1ZF9vcGVyYXRpb24gPT0gXCJJXCIpICApKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYoY3J1ZEluc2VydCE9IHVuZGVmaW5lZCAmJiBjcnVkSW5zZXJ0W3RoaXMuQ1JVRF9PUEVSQVRJT05dLmluZGV4T2YoJ0knKSA9PSAwKSB0aGVyZUlzSW5zZXJ0ID10cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBpZighdGhlcmVJc0luc2VydCl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vY29uc29sZS5sb2coJ2lzIGNoYW5nZWQgc28gZXhlY3V0ZSBpdGVtIGNoYW5nZWQgZnVuY3Rpb24nIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG9yaWdpbmFsX3JvdyA9IFtdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGtleSBpbiB0aGlzLmFyZ3MuaXRlbSl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihrZXkgIT0gJ3NsaWNrZ3JpZF9yb3djb250ZW50JyApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsX3Jvd1trZXldPXRoaXMuYXJncy5pdGVtW2tleV07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvcmlnaW5hbF9yb3dbJ3NsaWNrZ3JpZF9yb3djb250ZW50J10gID0ge307XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvcihsZXQga2V5IGluIHRoaXMuYXJncy5pdGVtWydzbGlja2dyaWRfcm93Y29udGVudCddICl7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3JpZ2luYWxfcm93WydzbGlja2dyaWRfcm93Y29udGVudCddW2tleV0gPSB7Li4udGhpcy5hcmdzLml0ZW1bJ3NsaWNrZ3JpZF9yb3djb250ZW50J11ba2V5XX07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsX3Jvd1t0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXT10aGlzLmRlZmF1bHRWYWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3JpZ2luYWxfcm93WydzbGlja2dyaWRfcm93Y29udGVudCddW3RoaXMuYXJncy5jb2x1bW4uZmllbGRdLmNvbnRlbnQ9dGhpcy5kZWZhdWx0VmFsdWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciB1cGRhdGVkT2JqZWN0PSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dJbmRleDogdGhpcy5hcmdzLml0ZW0uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW5JbmRleDogdGhpcy5hcmdzLmNvbHVtbi5jb2x1bW5vcmRlcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld19yb3cgOiB0aGlzLmFyZ3MuaXRlbSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsX3Jvdzogb3JpZ2luYWxfcm93LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlZENvbHVtbiA6IHRoaXMuYXJncy5jb2x1bW4uZmllbGQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbGRWYWx1ZTp0aGlzLmRlZmF1bHRWYWx1ZSAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdWYWx1ZTp0aGlzLmFyZ3MuaXRlbVt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0aW9uUmVzdWx0ID0gIHRoaXMuY29tbW9uR3JpZC52YWxpZGF0ZSh0aGlzLmFyZ3MuaXRlbSxvcmlnaW5hbF9yb3csZmFsc2UpICAgIDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLyogY29uc29sZS5sb2coJ1tJbnB1dFRleHRFZGl0b3JdIHZhbGlkYXRpb25SZXN1bHQgbmV3VmFsdWUgOicsdGhpcy5hcmdzLml0ZW0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW0lucHV0VGV4dEVkaXRvcl0gdmFsaWRhdGlvblJlc3VsdCBvbGRWYWx1ZSA6JyxvcmlnaW5hbF9yb3cpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW0lucHV0VGV4dEVkaXRvcl0gdmFsaWRhdGlvblJlc3VsdCByZXN1bHRhdCA6Jyx0aGlzLnZhbGlkYXRpb25SZXN1bHQgKSovXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZih0aGlzLnZhbGlkYXRpb25SZXN1bHQpe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLnVwZGF0ZUNydWQodXBkYXRlZE9iamVjdCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuc3B5Q2hhbmdlcyh7IGZpZWxkOiB0aGlzLmFyZ3MuY29sdW1uLmZpZWxkIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vSVRFTV9DSEFOR0VEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXIgZXZlbnQgPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dJbmRleDogdGhpcy5hcmdzLml0ZW0uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0IDogIHRoaXMuYXJncy5jb2x1bW4uZWRpdG9yLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFGaWVsZCA6IHRoaXMuYXJncy5jb2x1bW4uZmllbGQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGlzdERhdGEgOnsuLi51cGRhdGVkT2JqZWN0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKCB0aGlzLmNvbW1vbkdyaWQuSVRFTV9DSEFOR0VELm9ic2VydmVycy5sZW5ndGg+MSl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHg6IE9ic2VydmVyPGFueT49dGhpcy5jb21tb25HcmlkLklURU1fQ0hBTkdFRC5vYnNlcnZlcnNbMF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLklURU1fQ0hBTkdFRC5vYnNlcnZlcnM9W11cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuSVRFTV9DSEFOR0VELm9ic2VydmVyc1swXT14O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLklURU1fQ0hBTkdFRC5lbWl0KGV2ZW50KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKCBTd3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMub2JzZXJ2ZXJzLmxlbmd0aD4xKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXIgeDogT2JzZXJ2ZXI8YW55Pj1Td3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMub2JzZXJ2ZXJzWzBdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN3dENvbW1vbkdyaWRJdGVtUmVuZGVyQ2hhbmdlcy5vYnNlcnZlcnM9W11cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTd3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMub2JzZXJ2ZXJzWzBdPXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTd3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMuZW1pdCh7ZmllbGQ6IHRoaXMuYXJncy5jb2x1bW4uZmllbGQsIHZhbHVlOiB0aGlzLiRpbnB1dC52YWwoKSAsIGlkOiB0aGlzLmFyZ3MuaXRlbS5pZCB9KTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDaGFuZ2VkPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5vcmlnaW5hbERlZmF1bHRWYWx1ZSA9PSB0aGlzLiRpbnB1dC52YWwoKSApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGNydWRDaGFuZ2UgPSB0aGlzLmNvbW1vbkdyaWQuY2hhbmdlcy5nZXRWYWx1ZXMoKS5maW5kKHg9PigoeC5jcnVkX2RhdGEuaWQgPT0gdGhpcy5hcmdzLml0ZW0uaWQpICAgKSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGNoID0gIFN0cmluZyhcIlUoXCIrdGhpcy5hcmdzLmNvbHVtbi5maWVsZCtcIilcIikgO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGNydWRDaGFuZ2Upe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiggIFN0cmluZyhjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddKS5pbmRleE9mKGNoK1wiPlwiKSAhPSAtMSApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSA9IFN0cmluZyhjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddKS5yZXBsYWNlKGNoK1wiPlwiLCBcIlwiKSA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKCAgU3RyaW5nKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10pLmluZGV4T2YoXCI+XCIrY2gpICE9IC0xICl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddID0gU3RyaW5nKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10pLnJlcGxhY2UoXCI+XCIrY2gsIFwiXCIpIDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWVsc2UgaWYoICBTdHJpbmcoY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSkuaW5kZXhPZihjaCkgIT0gLTEgKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10gPSBTdHJpbmcoY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSkucmVwbGFjZShjaCwgXCJcIikgO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10gPT0gXCJcIiApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGNydWRDaGFuZ2VJbmRleCA9IHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldFZhbHVlcygpLmZpbmRJbmRleCh4PT4oKHguY3J1ZF9kYXRhLmlkID09IHRoaXMuYXJncy5pdGVtLmlkKSAgICkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLmNoYW5nZXMucmVtb3ZlKHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldEtleXMoKVtjcnVkQ2hhbmdlSW5kZXggXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLy0gRG8gbm90IGVtaXQgU3B5Tm9DaGFuZ2VzIG9uIHRoZSBncmlkIHVubGVzcyB0aGVyZSBpcyBvdGhlciBjaGFuZ2VzLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5jb21tb25HcmlkLmNoYW5nZXMuc2l6ZSgpID09IDAgKSB0aGlzLmNvbW1vbkdyaWQuc3B5Tm9DaGFuZ2VzKHsgZmllbGQ6IHRoaXMuYXJncy5jb2x1bW4uZmllbGQgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiggU3d0Q29tbW9uR3JpZEl0ZW1SZW5kZXJDaGFuZ2VzLm9ic2VydmVycy5sZW5ndGg+MSl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHg6IE9ic2VydmVyPGFueT49U3d0Q29tbW9uR3JpZEl0ZW1SZW5kZXJDaGFuZ2VzLm9ic2VydmVyc1swXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTd3RDb21tb25HcmlkSXRlbVJlbmRlckNoYW5nZXMub2JzZXJ2ZXJzPVtdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU3d0Q29tbW9uR3JpZEl0ZW1SZW5kZXJDaGFuZ2VzLm9ic2VydmVyc1swXT14O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU3d0Q29tbW9uR3JpZEl0ZW1SZW5kZXJDaGFuZ2VzLmVtaXQoe2ZpZWxkOiB0aGlzLmFyZ3MuY29sdW1uLmZpZWxkLCB2YWx1ZTogdGhpcy4kaW5wdXQudmFsKCkgLCBpZDogdGhpcy5hcmdzLml0ZW0uaWQgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfWVsc2UgaWYodGhpcy5vcmlnaW5hbERlZmF1bHRWYWx1ZSA9PSB0aGlzLiRpbnB1dC52YWwoKSApXHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5jb21tb25HcmlkLmNoYW5nZXMuc2l6ZSgpID09IDAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbW1vbkdyaWQuc3B5Tm9DaGFuZ2VzKHsgZmllbGQ6IHRoaXMuYXJncy5jb2x1bW4uZmllbGQgfSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHZhciBjcnVkQ2hhbmdlID0gdGhpcy5jb21tb25HcmlkLmNoYW5nZXMuZ2V0VmFsdWVzKCkuZmluZCh4PT4oKHguY3J1ZF9kYXRhLmlkID09IHRoaXMuYXJncy5pdGVtLmlkKSAgICkpO1xyXG4gICAgICAgICAgICAgICAgICAgIHZhciBjaCA9ICBTdHJpbmcoXCJVKFwiK3RoaXMuYXJncy5jb2x1bW4uZmllbGQrXCIpXCIpIDtcclxuICAgICAgICAgICAgICAgICAgICBpZihjcnVkQ2hhbmdlKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYoICBTdHJpbmcoY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSkuaW5kZXhPZihjaCtcIj5cIikgIT0gLTEgKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10gPSBTdHJpbmcoY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSkucmVwbGFjZShjaCtcIj5cIiwgXCJcIikgO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZiggIFN0cmluZyhjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddKS5pbmRleE9mKFwiPlwiK2NoKSAhPSAtMSApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3J1ZENoYW5nZVsnY3J1ZF9vcGVyYXRpb24nXSA9IFN0cmluZyhjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddKS5yZXBsYWNlKFwiPlwiK2NoLCBcIlwiKSA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKCAgU3RyaW5nKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10pLmluZGV4T2YoY2gpICE9IC0xICl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcnVkQ2hhbmdlWydjcnVkX29wZXJhdGlvbiddID0gU3RyaW5nKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10pLnJlcGxhY2UoY2gsIFwiXCIpIDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmKGNydWRDaGFuZ2VbJ2NydWRfb3BlcmF0aW9uJ10gPT0gXCJcIiApe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGNydWRDaGFuZ2VJbmRleCA9IHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldFZhbHVlcygpLmZpbmRJbmRleCh4PT4oKHguY3J1ZF9kYXRhLmlkID09IHRoaXMuYXJncy5pdGVtLmlkKSAgICkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb21tb25HcmlkLmNoYW5nZXMucmVtb3ZlKHRoaXMuY29tbW9uR3JpZC5jaGFuZ2VzLmdldEtleXMoKVtjcnVkQ2hhbmdlSW5kZXggXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgaWYoaXNDaGFuZ2VkKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhciBvcmlnaW5hbF9yb3cgPSBbXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGtleSBpbiB0aGlzLmFyZ3MuaXRlbSl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGtleSAhPSAnc2xpY2tncmlkX3Jvd2NvbnRlbnQnICl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcmlnaW5hbF9yb3dba2V5XT10aGlzLmFyZ3MuaXRlbVtrZXldO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsX3Jvd1snc2xpY2tncmlkX3Jvd2NvbnRlbnQnXSAgPSB7fTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGtleSBpbiB0aGlzLmFyZ3MuaXRlbVsnc2xpY2tncmlkX3Jvd2NvbnRlbnQnXSApe1xyXG4gICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcmlnaW5hbF9yb3dbJ3NsaWNrZ3JpZF9yb3djb250ZW50J11ba2V5XSA9IHsuLi50aGlzLmFyZ3MuaXRlbVsnc2xpY2tncmlkX3Jvd2NvbnRlbnQnXVtrZXldfTtcclxuICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3JpZ2luYWxfcm93W3RoaXMuYXJncy5jb2x1bW4uZmllbGRdPXRoaXMuZGVmYXVsdFZhbHVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsX3Jvd1snc2xpY2tncmlkX3Jvd2NvbnRlbnQnXVt0aGlzLmFyZ3MuY29sdW1uLmZpZWxkXS5jb250ZW50PXRoaXMuZGVmYXVsdFZhbHVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhciB1cGRhdGVkT2JqZWN0PSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd0luZGV4OiB0aGlzLmFyZ3MuaXRlbS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sdW1uSW5kZXg6IHRoaXMuYXJncy5jb2x1bW4uY29sdW1ub3JkZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld19yb3cgOiB0aGlzLmFyZ3MuaXRlbSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3JpZ2luYWxfcm93OiBvcmlnaW5hbF9yb3csXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoYW5nZWRDb2x1bW4gOiB0aGlzLmFyZ3MuY29sdW1uLmZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbGRWYWx1ZTp0aGlzLmRlZmF1bHRWYWx1ZSAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld1ZhbHVlOnRoaXMuYXJncy5pdGVtW3RoaXMuYXJncy5jb2x1bW4uZmllbGRdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL0lURU1fQ0hBTkdFRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhciBldmVudCA9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dJbmRleDogdGhpcy5hcmdzLml0ZW0uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldCA6ICB0aGlzLmFyZ3MuY29sdW1uLmVkaXRvcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YUZpZWxkIDogdGhpcy5hcmdzLmNvbHVtbi5maWVsZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGlzdERhdGEgOnsuLi51cGRhdGVkT2JqZWN0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoIHRoaXMuY29tbW9uR3JpZC5JVEVNX0NIQU5HRUQub2JzZXJ2ZXJzLmxlbmd0aD4xKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHg6IE9ic2VydmVyPGFueT49dGhpcy5jb21tb25HcmlkLklURU1fQ0hBTkdFRC5vYnNlcnZlcnNbMF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29tbW9uR3JpZC5JVEVNX0NIQU5HRUQub2JzZXJ2ZXJzPVtdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29tbW9uR3JpZC5JVEVNX0NIQU5HRUQub2JzZXJ2ZXJzWzBdPXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29tbW9uR3JpZC5JVEVNX0NIQU5HRUQuZW1pdChldmVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIC8vICBjb25zb2xlLmxvZygndGhpcy5jb21tb25HcmlkLmNoYW5nZXMnLCB0aGlzLmNvbW1vbkdyaWQuY2hhbmdlcylcclxuICAgICAgICAgICAgcmV0dXJuIGlzQ2hhbmdlZDtcclxuICAgICAgICB9LCAwKTtcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgcHVibGljIHZhbGlkYXRlKCkge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmluZm8oJ21ldGhvZCBbdmFsaWRhdGVdIC0gU1RBUlQvRU5EJyk7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgdmFsaWQ6IHRydWUsXHJcbiAgICAgICAgICAgIG1zZzogbnVsbFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuXHJcbn0iXX0=