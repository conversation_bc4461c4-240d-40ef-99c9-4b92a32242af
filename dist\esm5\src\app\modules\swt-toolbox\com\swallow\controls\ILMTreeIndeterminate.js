/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, Input } from '@angular/core';
import { CustomTree } from './swt-custom-tree.component';
import { CommonService } from '../utils/common.service';
import { SwtUtil } from '../utils/swt-util.service';
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
var fancytree = require('jquery.fancytree');
var ILMTreeIndeterminate = /** @class */ (function (_super) {
    tslib_1.__extends(ILMTreeIndeterminate, _super);
    // tree constructor.
    function ILMTreeIndeterminate(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.hideFunction = (/**
         * @param {?} item
         * @return {?}
         */
        function (item) { return _this.customHideFunction(item); });
        _this.saveTreeStateBasedOn = 'id';
        return _this;
    }
    //  return (item.@hideActual==true)||(item.@hideAccumulated==true) || 
    //(item.@visible!=true && (item.@type == "group" || item.@type == "scenario" ) && item.@isBranch=="true");
    /**
   * accessTreeHide
   *
   * @param xmlNode:XML
   *
   * This method is called to hide some nodes in access Tree view
   **/
    //  return (item.@hideActual==true)||(item.@hideAccumulated==true) || 
    //(item.@visible!=true && (item.@type == "group" || item.@type == "scenario" ) && item.@isBranch=="true");
    /**
     * accessTreeHide
     *
     * @private
     * @param {?} xmlNode
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.customHideFunction = 
    //  return (item.@hideActual==true)||(item.@hideAccumulated==true) || 
    //(item.@visible!=true && (item.@type == "group" || item.@type == "scenario" ) && item.@isBranch=="true");
    /**
     * accessTreeHide
     *
     * @private
     * @param {?} xmlNode
     * @return {?}
     */
    function (xmlNode) {
        // hideActual==true)||(item.@hideAccumulated==true)
        if (xmlNode.data.hideActual == true || xmlNode.data.hideAccumulated == true) {
            xmlNode.data.visible = false;
            return true;
        }
        else if (xmlNode.data.visible == false) {
            // xmlNode.data.visible = false;
            return true;
        }
        else {
            if (!this.isVisibleNode(xmlNode)) {
                // xmlNode.data.visible = false;
                return true;
            }
            else {
                // xmlNode.data.visible = true;
                return false;
            }
        }
    };
    /**
     *  Remove all the children nodes without branch that are not included in actual datasets<br>
     *	Actuals datasets are thresholds, actual balance and actual inflow/outflow
        * @param xmlList
        *
        */
    /**
     *  Remove all the children nodes without branch that are not included in actual datasets<br>
     * 	Actuals datasets are thresholds, actual balance and actual inflow/outflow
     * @param {?} xmlList
     *
     * @param {?} inActualData
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.showActualDatasetsOnly = /**
     *  Remove all the children nodes without branch that are not included in actual datasets<br>
     * 	Actuals datasets are thresholds, actual balance and actual inflow/outflow
     * @param {?} xmlList
     *
     * @param {?} inActualData
     * @return {?}
     */
    function (xmlList, inActualData) {
        for (var i = 0; i < xmlList.length; i++) {
            if (xmlList[i].isBranch == false) {
                // //var label:String = xmlList[i].@label;
                /** @type {?} */
                var yFiled = xmlList[i].yField;
                // // If the label of the node don't have neither Thresholds nor atcual then it will be deleted
                if (yFiled.indexOf('.Thresholds') == -1 && yFiled.indexOf('.ab') == -1 && yFiled.indexOf('.aac') == -1 && yFiled.indexOf('.aad') == -1) {
                    if (yFiled)
                        for (var j = 0; j < yFiled.split(",").length; j++) {
                            inActualData.push(yFiled.split(",")[j]);
                        }
                    // set hideActual flag to true for nodes without branch if it isn't an actual dataset 
                    xmlList[i].hideActual = true;
                }
                // Iterate recursively into the children node if it is a branch 
            }
            else {
                this.showActualDatasetsOnly(xmlList[i].children, inActualData);
            }
        }
    };
    /**
     * @param {?} xmlList
     * @param {?} inBalanceData
     * @param {?=} parentNode
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.showBalanceDatasetsOnly = /**
     * @param {?} xmlList
     * @param {?} inBalanceData
     * @param {?=} parentNode
     * @return {?}
     */
    function (xmlList, inBalanceData, parentNode) {
        for (var i = 0; i < xmlList.length; i++) {
            if (xmlList[i].isBranch == false) {
                //var label:String = xmlList[i].@label;
                /** @type {?} */
                var yFiled = xmlList[i].yField;
                // If the label of the node don't have neither Thresholds nor atcual then it will be deleted
                if (yFiled.indexOf('.Thresholds') == -1 && yFiled.indexOf('.ab') == -1 && yFiled.indexOf('.fbb') == -1 && yFiled.indexOf('.fbia') == -1) {
                    if (yFiled)
                        for (var j = 0; j < yFiled.split(",").length; j++) {
                            inBalanceData.push(yFiled.split(",")[j]);
                        }
                    // set hideAccumulated flag to true for nodes without branch if it isn't an balance dataset 
                    xmlList[i].hideAccumulated = true;
                    parentNode.hideAccumulated = true;
                }
                // Iterate recursively into the children node if it is a branch 
            }
            else
                this.showBalanceDatasetsOnly(xmlList[i].children, inBalanceData, xmlList[i]);
        }
    };
    /**
         * Shows or hides a group or a scenario based on what is checked on group/scenario bottom datagrid
         **/
    /**
     * Shows or hides a group or a scenario based on what is checked on group/scenario bottom datagrid
     *
     * @param {?} data
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.showHideGroupScenarioNodes = /**
     * Shows or hides a group or a scenario based on what is checked on group/scenario bottom datagrid
     *
     * @param {?} data
     * @return {?}
     */
    function (data) {
        /** @type {?} */
        var openedItems = this.openItems;
        // var listNode:XMLList = (dataProvider as XMLListCollection).source;
        for (var index = 0; index < this.dataProvider.length; index++) {
            /** @type {?} */
            var group = this.dataProvider[index];
            if (data["group"]) {
                /** @type {?} */
                var groupId = data["group_id"].toString();
                if (group.type == "group" && group.label == groupId) {
                    group.selected = false;
                    group.indeterminate = false;
                    // deselectNodes(group.children());
                    group.visible = data.isSelected;
                    /** @type {?} */
                    var node = ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(group.key);
                    /** @type {?} */
                    var nodeData = node.data;
                    nodeData.selected = false;
                    nodeData.indeterminate = false;
                    nodeData.visible = data.isSelected;
                    node.setSelected(false);
                    node.render(false, true);
                    break;
                }
            }
            else {
                /** @type {?} */
                var groupNode = ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(group.key);
                /** @type {?} */
                var grouNodeData = groupNode.data;
                for (var j = 0; j < group.children.length; j++) {
                    /** @type {?} */
                    var scenario = group.children[j];
                    /** @type {?} */
                    var scenarioId = data["scenario"].toString();
                    /** @type {?} */
                    var scenarioNode = ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(scenario.key);
                    if (scenario.label == scenarioId) {
                        /** @type {?} */
                        var nodeData = scenarioNode.data;
                        scenario.visible = nodeData.visible = data.isSelected;
                        if (data.isSelected) {
                            // deselectNodes(scenario.children())
                            scenarioNode.setSelected(false);
                            scenario.selected = nodeData.selected = false;
                            scenario.indeterminate = nodeData.indeterminate = false;
                            // group.selected = grouNodeData.selected = false;
                        }
                    }
                }
                groupNode.render(false, true);
            }
        }
    };
    /**
         * get list of selected nodes
         * */
    /**
     * get list of selected nodes
     *
     * @param {?} selectedNodes
     * @param {?} listNode
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getSelectedNodes = /**
     * get list of selected nodes
     *
     * @param {?} selectedNodes
     * @param {?} listNode
     * @return {?}
     */
    function (selectedNodes, listNode) {
        // var selKeys = $.map( ($(this.treeContainer.nativeElement) as any).fancytree('getTree').getSelectedNodes(), function(node){
        //     return node.data;
        // });
        if (!selectedNodes)
            selectedNodes = [];
        if (!listNode)
            listNode = { children: this.dataProvider };
        if (listNode.children) {
            for (var i = 0; i < listNode.children.length; i++) {
                if ((listNode.children[i].isBranch == false) && (listNode.children)[i].selected == true) {
                    /** @type {?} */
                    var yFiled = listNode.children[i].yField;
                    /** @type {?} */
                    var isSelected = listNode.children[i].selected;
                    if (isSelected == true && yFiled != "") {
                        if (yFiled.indexOf(",") > 0)
                            for (var j = 0; j < yFiled.split(",").length; j++)
                                selectedNodes.push(yFiled.split(",")[j]);
                        else
                            selectedNodes.push(yFiled);
                    }
                }
                else {
                    this.getSelectedNodes(selectedNodes, listNode.children[i]);
                }
            }
        }
    };
    /**
     * Based on the event data transfert object, this functions returns an Array of datasets that will be shown or hedden
     * */
    /**
     * Based on the event data transfert object, this functions returns an Array of datasets that will be shown or hedden
     *
     * @param {?} data
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.checkedGroupScenarioCharts = /**
     * Based on the event data transfert object, this functions returns an Array of datasets that will be shown or hedden
     *
     * @param {?} data
     * @return {?}
     */
    function (data) {
        // var listNode: any;
        // var listNode: any;
        // if(!listNode)
        //     listNode = {children:this.dataProvider};
        /** @type {?} */
        var checkedNodes = [];
        for (var index = 0; index < this.dataProvider.length; index++) {
            /** @type {?} */
            var group = this.dataProvider[index];
            if (data["group"]) {
                /** @type {?} */
                var groupId = data["group_id"].toString();
                if (group.type == "group" && group.label == groupId) {
                    this.getCheckedScenarioNodes(checkedNodes, group.children);
                    break;
                }
            }
            else if (group.visible == true) {
                for (var j = 0; j < group.children.length; j++) {
                    /** @type {?} */
                    var scenario = group.children[j];
                    /** @type {?} */
                    var scenarioId = data["scenario"].toString();
                    if (scenario.label == scenarioId)
                        this.getCheckedScenarioNodes(checkedNodes, scenario.children);
                }
            }
        }
        return checkedNodes;
    };
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     * */
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getCheckedScenarioNodes = /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    function (checkedNodes, listNodes) {
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var node = listNodes[index];
            if (node.isBranch == false) {
                /** @type {?} */
                var yFiled = node.yField;
                for (var j = 0; j < yFiled.split(",").length; j++) {
                    checkedNodes.push(yFiled.split(",")[j]);
                }
            }
            else
                this.getCheckedScenarioNodes(checkedNodes, node.children);
        }
    };
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     * */
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getCheckedScenarioNodesTree = /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    function (checkedNodes, listNodes) {
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var node = listNodes[index];
            if (node.isBranch == false) {
                if (node.selected == true) {
                    /** @type {?} */
                    var yFiled = node.yField;
                    checkedNodes.push(yFiled);
                }
            }
            else if (node.visible == true) {
                this.getCheckedScenarioNodesTree(checkedNodes, node.children);
            }
        }
    };
    /**
* Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
* */
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.setCheckedScenarioNodesTree = /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     *
     * @param {?} checkedNodes
     * @param {?} listNodes
     * @return {?}
     */
    function (checkedNodes, listNodes) {
        listNodes = SwtUtil.convertObjectToArray(listNodes);
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var node = listNodes[index];
            if (node.isBranch == false) {
                if (checkedNodes.indexOf(node.yField) != -1) {
                    node.selected = true;
                }
                else {
                    node.selected = false;
                }
            }
            else {
                this.setCheckedScenarioNodesTree(checkedNodes, node.node);
            }
        }
    };
    /**
 * Returns the list of displayed scenarios in the tree,
 * this will be useful to update the dataprovider of the whole ILM line chart
 * */
    /**
     * Returns the list of displayed scenarios in the tree,
     * this will be useful to update the dataprovider of the whole ILM line chart
     *
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getDisplayedScenarios = /**
     * Returns the list of displayed scenarios in the tree,
     * this will be useful to update the dataprovider of the whole ILM line chart
     *
     * @return {?}
     */
    function () {
        /** @type {?} */
        var rtn = "";
        /** @type {?} */
        var listNodes = this.dataProvider.filter((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return x.visible == true; }));
        if (!listNodes.length) {
            listNodes = [listNodes];
        }
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var group = listNodes[index];
            for (var j = 0; j < group.children.length; j++) {
                /** @type {?} */
                var scenario = group.children[j];
                if (scenario.visible == true && scenario.label != 'Thresholds') {
                    rtn += (group.label + ":" + scenario.label + "|");
                }
            }
        }
        if (rtn.length > 1)
            rtn = rtn.substr(0, rtn.length - 1);
        return rtn;
    };
    /**
     * @private
     * @param {?} xmlNode
     * @param {?} yFieldNames
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.findYFields = /**
     * @private
     * @param {?} xmlNode
     * @param {?} yFieldNames
     * @return {?}
     */
    function (xmlNode, yFieldNames) {
        if (xmlNode.isBranch) {
            // for each (var child:* in xmlNode.children())
            for (var j = 0; j < xmlNode.children.length; j++) {
                /** @type {?} */
                var child = xmlNode.children[j];
                this.findYFields(child, yFieldNames);
            }
        }
        /** @type {?} */
        var yFiled = xmlNode.yField;
        if (yFiled && xmlNode.selected == true && xmlNode.hideActual != true && xmlNode.hideAccumulated != true) {
            for (var i = 0; i < yFiled.split(",").length; i++) {
                yFieldNames.push(yFiled.split(",")[i]);
            }
        }
    };
    /**
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getVisibleYFields = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var yFiledsNames = [];
        // var listNode:XMLList=(dataProvider as XMLListCollection).source;
        for (var i = 0; i < this.dataProvider.length; i++) {
            // for each (var node:XML in listNode.children())
            /** @type {?} */
            var node = this.dataProvider[i];
            if (node.visible == true)
                // for each (var child:XML in node.children())
                for (var j = 0; j < node.children.length; j++) {
                    /** @type {?} */
                    var child = node.children[j];
                    if (child.visible == true) {
                        this.findYFields(child, yFiledsNames);
                    }
                }
        }
        return yFiledsNames;
    };
    /**
     * Returns the list of displayed thresholds by seraching in the tree structure
     * */
    /**
     * Returns the list of displayed thresholds by seraching in the tree structure
     *
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getDisplayedThresolds = /**
     * Returns the list of displayed thresholds by seraching in the tree structure
     *
     * @return {?}
     */
    function () {
        /** @type {?} */
        var groupNames = [];
        // var listNode:XMLList = (dataProvider as XMLListCollection).source;
        /** @type {?} */
        var listNodes = this.dataProvider.filter((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return x.visible == true; }));
        if (!listNodes.length) {
            listNodes = [listNodes];
        }
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var group = listNodes[index];
            for (var j = 0; j < group.children.length; j++) {
                /** @type {?} */
                var scenario = group.children[j];
                if (scenario.visible == true && scenario.label == 'Thresholds' && scenario.selected == true) {
                    groupNames.push(group.label + ".Thresholds");
                }
            }
        }
        return groupNames;
    };
    /**
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getDisplayedThresoldsArray = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var groupNames = [];
        // var listNode:XMLList = (dataProvider as XMLListCollection).source;
        /** @type {?} */
        var listNodes = this.dataProvider.filter((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return x.visible == true; }));
        if (!listNodes.length) {
            listNodes = [listNodes];
        }
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var group = listNodes[index];
            for (var j = 0; j < group.children.length; j++) {
                /** @type {?} */
                var scenario = group.children[j];
                if (scenario.visible == true && scenario.label == 'Thresholds' && scenario.selected == true) {
                    groupNames.push(group.label);
                }
            }
        }
        return groupNames;
    };
    /**
     * @param {?} data
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.getGroupScenarioCombinations = /**
     * @param {?} data
     * @return {?}
     */
    function (data) {
        /** @type {?} */
        var selectedFigures = [];
        /** @type {?} */
        var listNodes = data.filter((/**
         * @param {?} x
         * @return {?}
         */
        function (x) { return x.visible == true; }));
        if (!listNodes.length) {
            listNodes = [listNodes];
        }
        for (var index = 0; index < listNodes.length; index++) {
            /** @type {?} */
            var group = listNodes[index];
            for (var j = 0; j < group.children.length; j++) {
                /** @type {?} */
                var scenario = group.children[j];
                // for each (var scenario:* in group.children().(@visible == "true").(@isBranch == "true"))
                if (scenario.visible == true && scenario.isBranch == true && (scenario.indeterminate == true || scenario.selected == true)) {
                    selectedFigures.push(String(group.id).split(".")[0] + ":" + scenario.label);
                }
            }
        }
        return selectedFigures;
    };
    Object.defineProperty(ILMTreeIndeterminate.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return (/** @type {?} */ (Reflect.get(CustomTree.prototype, 'dataProvider', this)));
        },
        /**
         * Override dataProvider to save tree opened elements
         **/
        // Input to hold tree dataProvider.
        set: /**
         * Override dataProvider to save tree opened elements
         *
         * @param {?} value
         * @return {?}
         */
        // Input to hold tree dataProvider.
        function (value) {
            /** @type {?} */
            var treeCreated = !this.firstLoad;
            if (treeCreated)
                this.saveTreeOpenState();
            (/** @type {?} */ (Reflect.set(CustomTree.prototype, 'dataProvider', value, this)));
            if (treeCreated)
                this.reOpenSavedState();
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to expand all tree nodes.
     * @param expandToLvl
     */
    /**
     * This method is used to expand all tree nodes.
     * @param {?=} groupId
     * @return {?}
     */
    ILMTreeIndeterminate.prototype.reloadNodeById = /**
     * This method is used to expand all tree nodes.
     * @param {?=} groupId
     * @return {?}
     */
    function (groupId) {
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var hide;
        console.log(groupId);
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                if (node.data.label == groupId) {
                    try {
                        node.render();
                    }
                    catch (e) {
                    }
                }
            }));
        }
        catch (error) {
            console.log(error);
            this.logger.error('[ expandAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandAll ] END.');
    };
    ILMTreeIndeterminate.decorators = [
        { type: Component, args: [{
                    selector: 'ILMTreeIndeterminate',
                    template: "\n        <!--<div id=\"{{id}}\" selector=\"SwtCustomTree\" #treeContainer class=\"treeContainer {{ styleName }}\"></div>-->\n        <div class=\"customTreeWarapper\">\n            <table #treeContainer class=\"treeContainer {{ styleName }}\">\n                <tr style=\"height: 23px\">\n                    <td></td>\n                </tr>\n            </table>\n        </div>\n        <span #treeTipHolder></span>\n    ",
                    styles: ["\n        :host {\n            display: block;\n            /*background-color: #FFF;*/\n            /*overflow: auto;*/\n            width: 300px;\n            height: 450px;\n        }\n\n        .customTreeWarapper {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            background-color: #FFF;\n            border: 1px solid #B7BABC;\n            line-height: initial;\n        }\n\n        .customTreeWarapper table td, table th{\n            padding : 0px;\n        }\n\n        .treeContainer {\n            width: 100%;\n            padding-left: 3px;\n        }\n\n        .fancytree-container {\n            outline: none;\n            border: none;\n        }\n\n        .fancytree-container:focus {\n            outline: none;\n            border: none;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    ILMTreeIndeterminate.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    ILMTreeIndeterminate.propDecorators = {
        dataProvider: [{ type: Input }]
    };
    return ILMTreeIndeterminate;
}(CustomTree));
export { ILMTreeIndeterminate };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    ILMTreeIndeterminate.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    ILMTreeIndeterminate.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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