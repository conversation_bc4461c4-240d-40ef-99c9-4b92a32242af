/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Input, ElementRef, Component } from "@angular/core";
import 'jquery-ui-dist/jquery-ui';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
var $ = require('jquery');
var SwtRadioItem = /** @class */ (function (_super) {
    tslib_1.__extends(SwtRadioItem, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function SwtRadioItem(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        _this._value = null;
        _this.parentGroup = null;
        return _this;
    }
    Object.defineProperty(SwtRadioItem.prototype, "tabIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._tabIndex;
        },
        //---tabIndex-------------------------------------------------------------------------------------------------------
        set: 
        //---tabIndex-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._tabIndex = String(value);
                if (this.selected)
                    this.addTabIndex($($($(this.elem.nativeElement).children()[0]).children()[0]), this._tabIndex);
            }
            catch (error) {
                console.error('method [ SwtRadioItem set tabIndex] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "styleName", {
        //---Inputs--------------------------------------------------------------------------------------------------------
        set: 
        //---Inputs--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if ($(this.elem.nativeElement))
                $($($(this.elem.nativeElement).children()[0]).children()[1]).addClass(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "groupName", {
        get: /**
         * @return {?}
         */
        function () {
            return this._groupName;
        },
        //---groupName-----------------------------------------------------------------------------------------------------
        set: 
        //---groupName-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._groupName = value;
            /** @type {?} */
            var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
            if (item.length > 0) {
                $(item).attr('name', this._groupName);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        //---Label---------------------------------------------------------------------------------------------------------
        set: 
        //---Label---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._label = value;
            if ($(this.elem.nativeElement))
                $($($(this.elem.nativeElement).children()[0]).children()[1]).text(this._label);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "value", {
        get: /**
         * @return {?}
         */
        function () {
            return this._value;
        },
        //---value---------------------------------------------------------------------------------------------------------
        set: 
        //---value---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
            if (item.length > 0) {
                $(item).val(String(value));
            }
            if (this.selected && this.parentGroup && this.parentGroup.originalValue !== value) {
                this.parentGroup.originalRadioItem = this.id;
                this.parentGroup.originalValue = value;
            }
            this._value = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "selected", {
        get: /**
         * @return {?}
         */
        function () {
            return this.elem.nativeElement.firstElementChild.children[0].checked;
        },
        //---selected------------------------------------------------------------------------------------------------------
        set: 
        //---selected------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.elem.nativeElement.firstElementChild.children[0].checked = value;
            // set selectedValue of correspondent SwtButtonGroup.
            if (value && this.parentGroup) {
                this.parentGroup.selectedRadioId = this.id;
                this.parentGroup.selectedValue = this.value;
            }
            if (value && this.tabIndex) {
                this.addTabIndex($($($(this.elem.nativeElement).children()[0]).children()[0]), this._tabIndex);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioItem.prototype, "fontSize", {
        //---FontSize------------------------------------------------------------------------------------------------------
        set: 
        //---FontSize------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if ($(this.elem.nativeElement))
                this.setStyle("font-size", this.adaptUnit(String(value)), $($($(this.elem.nativeElement).children()[0]).children()[1]));
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtRadioItem.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtRadioItem.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRadioItem');
    };
    /**
     * Destroy all event listeners
     */
    /**
     * Destroy all event listeners
     * @return {?}
     */
    SwtRadioItem.prototype.ngOnDestroy = /**
     * Destroy all event listeners
     * @return {?}
     */
    function () {
        try {
            //console.log('[SwtRadioItem] ngOnDestroy ');
            delete this._value;
            delete this._label;
            delete this._groupName;
            delete this._tabIndex;
            delete this.parentGroup;
        }
        catch (error) {
            console.error('method [ngOnDestroy] - error :', error);
        }
    };
    SwtRadioItem.decorators = [
        { type: Component, args: [{
                    selector: 'SwtRadioItem',
                    template: "      \n    <div  class=\"radio-item\"  tabindex=\"-1\"  >\n        <input [disabled]=\"parentGroup && !parentGroup.enabled\" type=\"radio\"/>\n        <label class=\"rblabel\" ></label>\n    </div>\n     \n  ",
                    styles: ["\n           :host {\n               outline: none;\n             }\n           .rblabel{\n               font-size: 11px;\n               color: #173553;\n               height: auto;\n               margin: 5px 4px 0;\n               position: relative;\n               top:1px;\n               font-weight: normal;\n               overflow: hidden;\n               text-overflow: ellipsis;\n             }\n           \n             div.radio-item > input:disabled{\n               background-color: transparent!important;\n             }\n           \n             @media all and (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {\n                 .selector:not(*:root), .rblabel{\n                     margin: 1px 4px 0!important;\n                 }\n             }\n\n            .label{\n                font-weight: bold!important;\n             }\n            .radio-item{\n                width : 100%;\n                display: flex;\n                outline: none;\n            }\n            \n         "]
                }] }
    ];
    /** @nocollapse */
    SwtRadioItem.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtRadioItem.propDecorators = {
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        styleName: [{ type: Input }],
        toolTip: [{ type: Input, args: ['toolTip',] }],
        groupName: [{ type: Input }],
        label: [{ type: Input, args: ['label',] }],
        value: [{ type: Input }],
        selected: [{ type: Input, args: ['selected',] }],
        fontSize: [{ type: Input, args: ['fontSize',] }]
    };
    return SwtRadioItem;
}(Container));
export { SwtRadioItem };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._value;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._groupName;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._tabIndex;
    /** @type {?} */
    SwtRadioItem.prototype.parentGroup;
    /** @type {?} */
    SwtRadioItem.prototype.toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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