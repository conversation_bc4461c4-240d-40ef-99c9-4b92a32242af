/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Directive, ElementRef, Input, Output, EventEmitter } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
/**
 * In order to improve the readability of code on the one hand and to structure the toolbox project,
 * we created a directive that loads the jquery library to do all the manipulations on the DOM
 * and that has the role of managing all that is vusuel.
 * this directive avoids the repetition of the same processing in each component such as width, height, enabled, visible ... etc
 * with this solution the components only contain the getter and setter or / and the inputs.
 * all future components should use this directive to fix the html invalid tags issue generated by angular
 * these tags cause browser compatibility issues also the css does not apply on these tags.
 * Example: if we write <VBox height="100%"> bla..bla.. </VBox> if we inspect the view we find in the DOM this code
 *
 * <vbox ng-reflect-height="100%"> <-- This tag generated by angular cause the problem
 *   <div style="height:100%"> bla..bla</div>
 * </vbox>
 *
 * So the solution is to remove all angular tags in order to obtain a valid HTML tags in the DOM.
 * <AUTHOR>
 * @version 1.0.0
 */
export class SwtDOMManager {
    /**
     * @param {?} element
     */
    constructor(element) {
        this.element = element;
        // variable to handle visibility  
        this._visible = true;
        // variable to handle visibility  
        this._enabled = true;
        // private variable to store button mode.
        this._buttonMode = true;
        // private variable to store style name
        this._styleName = "";
        //private variable to store component name.
        this.component = "";
        // private variable to set toolTip to component.
        this._toolTip = "";
        // output to handle click event.
        this.onClick = new EventEmitter();
        // output to handle KeyDown event.
        this.onKeyDown = new EventEmitter();
        // output to handle FocusOut event.
        this.onFocusOut = new EventEmitter();
        // output to handle onKeyUp event.
        this.onKeyUp = new EventEmitter();
        // output to handle mouseOver event.
        this.mouseOver = new EventEmitter();
        // output to handle mouseEnter event.
        this.mouseEnter = new EventEmitter();
        // output to handle mouseLeave event.
        this.mouseLeave = new EventEmitter();
        // output to handle Focus event.
        this.onFocus = new EventEmitter();
        // output to handle mouseDown event.
        this.mouseDown = new EventEmitter();
        // output to handle onmouseUp event.
        this.mouseUp = new EventEmitter();
        // Input to handle component width.
        this.width = "";
        //Input to handle component height.
        this.height = "";
        //Input to handle component styleName.
        this.styleName = "";
        //Input to handle component id.
        this.id = "";
        //Input to handle component paddingTop.
        this.paddingTop = "";
        //Input to handle component paddingRight.
        this.paddingRight = "";
        //Input to handle component paddingBottom.
        this.paddingBottom = "";
        //Input to handle component paddingLeft.
        this.paddingLeft = "";
        //Input to handle component marginTop.
        this.marginTop = "";
        //Input to handle component marginRight.
        this.marginRight = "";
        //Input to handle component marginBottom.
        this.marginBottom = "";
        //Input to handle component marginLeft.
        this.marginLeft = "";
        // Input to handle content align.
        this.horizontalAlign = "";
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            if (typeof (value) === "string") {
                if (value === "false") {
                    this._visible = false;
                    $(this.child).hide();
                }
                else {
                    this._visible = true;
                    $(this.child).show();
                }
            }
            else {
                this._visible = value;
                if (value) {
                    $(this.child).show();
                }
                else {
                    $(this.child).hide();
                }
            }
        }), 0);
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    //  @Input()
    //  set enabled(value: boolean) {
    //      setTimeout(() => {
    //          console.log('--------------SwtDOMManager--------------')
    //          if (typeof(value) === "string") {
    //              if(value === "false") {
    //                  this._enabled = false;
    //                  this.disableComponent(this.component, false);
    //              } else {
    //                  this._enabled = true;
    //                  this.disableComponent(this.component, true);
    //              }
    //          } else {
    //              this._enabled = value;
    //              if(!value) {
    //                  this.disableComponent(this.component, false);
    //              } else {
    //                  this.disableComponent(this.component, true);
    //              }
    //          }
    //      }, 0);
    //  }
    //  get enabled() {
    //      return this._enabled;
    //  }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTip(value) {
        this._toolTip = value;
        //   setTimeout(() => {
        //       $(this.element.nativeElement).attr("title",value);
        //   }, 0);
    }
    /**
     * @return {?}
     */
    get toolTip() {
        return this._toolTip;
    }
    /**
     * \@method ngOnInit.
     * @return {?}
     */
    ngOnInit() {
        try {
            // add toolTip to component
            // $(this.element.nativeElement).tooltip({
            //     position: { my: "left+20 center", at: "right-20 bottom+20" }
            // });
            $(this.element.nativeElement).on("focusout", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
            }));
            // This variable to store parent of current component.
            this.parent = $(this.element.nativeElement).parent()[0];
            // This variable to store the current component DOM.
            this.child = $(this.element.nativeElement)[0];
            // get component name.
            this.component = $(this.parent).prop("tagName");
            // remove angular tag from the DOM.
            $(this.parent).replaceWith(this.child);
            // if width not contain % sign so append px.
            this.width.indexOf("%") === -1 ? this.width += "px" : null;
            // if height not contain % sign so append px.
            this.height.indexOf("%") === -1 ? this.height += "px" : null;
            // set component height.
            $(this.child).height(this.height);
            //set component width
            $(this.child).width(this.width);
            // set component styleName.
            $(this.child).addClass(this.styleName);
            // set component styleName.
            $(this.child).attr("id", this.id);
            // if component is HBOX so display flex.
            if (this.component === "HBOX") {
                //                if(!this.enabled) {
                //                    $(this.child).addClass("disabled-container");
                //                } else {
                //                    $(this.child).removeClass("disabled-container");
                //                }
                // if HBOX is visible display content flex. 
                this.visible === true ? $(this.child).css("display", "flex") : $(this.child).css("display", "none");
                // set HBOX width.
                $(this.child).width(this.width);
                // set VBOX paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set VBOX margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // set horizontal position.
                this.alignContent(this.child, "HBOX", this.horizontalAlign);
            }
            // if component is HBOX so display flex.
            if (this.component === "VBOX") {
                //            if(!this.enabled) {
                //                $(this.child).addClass("disabled-container");
                //            } else {
                //                $(this.child).removeClass("disabled-container");
                //            }
                // set component width
                /** @type {?} */
                var className = $($(this.child).parent()[0]).attr("class");
                if (className) {
                    if (className === "hbox1") {
                        $(this.child).width(this.width).css('width', '+=' + (Number(this.paddingLeft) + Number(this.paddingRight)) + 'px');
                    }
                    else {
                        $(this.child).width(this.width).css('width', '-=' + (Number(this.paddingLeft) + Number(this.paddingRight)) + 'px');
                    }
                }
                // set VBOX paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set VBOX margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // set horizontal position.
                this.alignContent(this.child, "VBOX", this.horizontalAlign);
            }
            if (this.component === "SWTCANVAS") {
                //            if(!this.enabled) {
                //                $(this.child).addClass("disabled-container");
                //            } else {
                //                $(this.child).removeClass("disabled-container");
                //            }
                // set SWTCANVAS paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set SWTCANVAS margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // subtract margins and paddings from width to conserve canvas in the center
                $(this.child).width(this.width)
                    .css('width', '-=' + (Number(this.marginRight) + Number(this.marginLeft) + Number(this.paddingRight) + Number(this.paddingLeft) + 2) + 'px');
            }
            if (this.component === "SWTPANEL") {
                /** @type {?} */
                var panel = $($($(this.child)[0])[0])[0];
                /** @type {?} */
                var panelbody = $($($(this.child)[0])[0]).children()[0];
                /** @type {?} */
                var parent = $($(this.child)[0]).parent()[0];
                /** @type {?} */
                var parentName = $(panel).prop("class");
                // set SWTPANEL paddings
                this.setPaddings(this.child, this.paddingTop, this.paddingRight, this.paddingBottom, this.paddingLeft);
                // set SWTPANEL margins
                this.setMargins(this.child, this.marginTop, this.marginRight, this.marginBottom, this.marginLeft);
                // subtract margins and paddings from width to conserve canvas in the center
                $(this.child).width(this.width)
                    .css('width', '-=' + (Number(this.paddingRight) + Number(this.paddingLeft) + Number(this.marginRight) + Number(this.marginLeft)) + 'px');
                /** @type {?} */
                var paddingBottom = $(parent).css("padding-bottom");
                /** @type {?} */
                var paddingTop = $(parent).css("padding-top");
                /** @type {?} */
                var marginBottom = $(parent).css("margin-bottom");
                /** @type {?} */
                var marginTop = $(parent).css("margin-top");
                $(panel).height(this.height)
                    .css('height', '-=' + (Number(this.marginTop) + Number(this.marginBottom)) + 'px')
                    .css("height", "-=" + paddingBottom).css("height", "-=" + paddingTop)
                    .css("height", "-=" + marginBottom)
                    .css("height", "-=" + marginTop);
                $(panelbody).height($(panel).height()).css('height', '-=' + (Number(this.paddingTop) + Number(this.paddingBottom) + 12) + 'px');
                /*
                 * TO DO add case when panel exist in other panel
                 * in the level 3 is not stable
                 * if (parentName === "panelInsideFormLayout") {
                    $(panelbody).height($(panel).height()).css('height', '-='+(Number(this.paddingTop)+Number(this.paddingBottom) + 12)+'px');
                }*/
            }
            if (this.component === "SWTBUTTON") {
                //            $(this.child).height(this.height+"px");
            }
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - ngOnInit method error:", error);
        }
    }
    /**
     * @private
     * @param {?} component
     * @param {?} enable
     * @return {?}
     */
    disableComponent(component, enable) {
        //      if (component === "SWTBUTTON") {
        //          
        //      } else {
        //          if(enable) {
        //              $(this.child).removeClass("disabled-container");
        //          } else {
        //              $(this.child).addClass("disabled-container"); 
        //          }
        //      }
    }
    /**
     * @private
     * @param {?} component
     * @return {?}
     */
    removeListeners(component) {
        //      $(component).off();
    }
    /**
     * This method is used to set paddings to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    setPaddings(component, top, right, bottom, left) {
        try {
            //          top.indexOf("%") === -1 ? top+= "px":null; 
            //          right.indexOf("%") === -1 ? right+= "px":null; 
            //          bottom.indexOf("%") === -1 ? bottom+= "px":null; 
            //          left.indexOf("%") === -1 ? left+= "px":null;
            //          //set component paddings.
            //          $(component).css("padding-top", top)
            //          .css("padding-right",right)
            //          .css("padding-bottom", bottom)
            //          .css("padding-left",left); 
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - setPaddings method error :", error);
        }
    }
    /**
     * This method is used to set margins to component.
     * @private
     * @param {?} component
     * @param {?=} top
     * @param {?=} right
     * @param {?=} bottom
     * @param {?=} left
     * @return {?}
     */
    setMargins(component, top, right, bottom, left) {
        try {
            //         top.indexOf("%") === -1 ? top+= "px":null; 
            //         right.indexOf("%") === -1 ? right+= "px":null; 
            //         bottom.indexOf("%") === -1 ? bottom+= "px":null; 
            //         left.indexOf("%") === -1 ? left+= "px":null;
            //         //set component margins.
            //         $(component).css("margin-top", top)
            //         .css("margin-right",right)
            //         .css("margin-bottom", bottom)
            //         .css("margin-left",left);
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - setMargins method error :", error);
        }
    }
    /**
     * This method is used to align element content.
     * @private
     * @param {?} component
     * @param {?} name
     * @param {?} position
     * @return {?}
     */
    alignContent(component, name, position) {
        try {
            //        if(name.toUpperCase() === "VBOX") {
            //            if (position.toUpperCase() === "RIGHT") {
            //                position = "flex-end";
            //            }
            //            $(component).css("display", "flex")
            //            .css("align-items",position);
            //        }
            //        if(name.toUpperCase() === "HBOX") {
            //            if (position.toUpperCase() === "RIGHT") {
            //                position = "flex-end";
            //            }
            //            $(component).css("justify-content",position);
            //        }
        }
        catch (error) {
            console.error("[ SwtDomhandler ] - alignContent method error :", error);
        }
    }
}
SwtDOMManager.decorators = [
    { type: Directive, args: [{
                selector: '[SwtDOMManager]'
            },] }
];
/** @nocollapse */
SwtDOMManager.ctorParameters = () => [
    { type: ElementRef }
];
SwtDOMManager.propDecorators = {
    onClick: [{ type: Output, args: ["onClick",] }],
    onKeyDown: [{ type: Output, args: ["onKeyDown",] }],
    onFocusOut: [{ type: Output, args: ["onFocusOut",] }],
    onKeyUp: [{ type: Output, args: ["onKeyUp",] }],
    mouseOver: [{ type: Output, args: ["mouseOver",] }],
    mouseEnter: [{ type: Output, args: ["mouseEnter",] }],
    mouseLeave: [{ type: Output, args: ["mouseLeave",] }],
    onFocus: [{ type: Output, args: ["onFocus",] }],
    mouseDown: [{ type: Output, args: ["mouseDown",] }],
    mouseUp: [{ type: Output, args: ["mouseUp",] }],
    width: [{ type: Input, args: ["width",] }],
    height: [{ type: Input, args: ["height",] }],
    styleName: [{ type: Input, args: ["styleName",] }],
    id: [{ type: Input, args: ["id",] }],
    paddingTop: [{ type: Input, args: ["paddingTop",] }],
    paddingRight: [{ type: Input, args: ["paddingRight",] }],
    paddingBottom: [{ type: Input, args: ["paddingBottom",] }],
    paddingLeft: [{ type: Input, args: ["paddingLeft",] }],
    marginTop: [{ type: Input, args: ["marginTop",] }],
    marginRight: [{ type: Input, args: ["marginRight",] }],
    marginBottom: [{ type: Input, args: ["marginBottom",] }],
    marginLeft: [{ type: Input, args: ["marginLeft",] }],
    horizontalAlign: [{ type: Input, args: ["horizontalAlign",] }],
    visible: [{ type: Input }],
    toolTip: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.child;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._buttonMode;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.component;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype._toolTip;
    /** @type {?} */
    SwtDOMManager.prototype.onClick;
    /** @type {?} */
    SwtDOMManager.prototype.onKeyDown;
    /** @type {?} */
    SwtDOMManager.prototype.onFocusOut;
    /** @type {?} */
    SwtDOMManager.prototype.onKeyUp;
    /** @type {?} */
    SwtDOMManager.prototype.mouseOver;
    /** @type {?} */
    SwtDOMManager.prototype.mouseEnter;
    /** @type {?} */
    SwtDOMManager.prototype.mouseLeave;
    /** @type {?} */
    SwtDOMManager.prototype.onFocus;
    /** @type {?} */
    SwtDOMManager.prototype.mouseDown;
    /** @type {?} */
    SwtDOMManager.prototype.mouseUp;
    /** @type {?} */
    SwtDOMManager.prototype.width;
    /** @type {?} */
    SwtDOMManager.prototype.height;
    /** @type {?} */
    SwtDOMManager.prototype.styleName;
    /** @type {?} */
    SwtDOMManager.prototype.id;
    /** @type {?} */
    SwtDOMManager.prototype.paddingTop;
    /** @type {?} */
    SwtDOMManager.prototype.paddingRight;
    /** @type {?} */
    SwtDOMManager.prototype.paddingBottom;
    /** @type {?} */
    SwtDOMManager.prototype.paddingLeft;
    /** @type {?} */
    SwtDOMManager.prototype.marginTop;
    /** @type {?} */
    SwtDOMManager.prototype.marginRight;
    /** @type {?} */
    SwtDOMManager.prototype.marginBottom;
    /** @type {?} */
    SwtDOMManager.prototype.marginLeft;
    /** @type {?} */
    SwtDOMManager.prototype.horizontalAlign;
    /**
     * @type {?}
     * @private
     */
    SwtDOMManager.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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