/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var Spacer = /** @class */ (function (_super) {
    tslib_1.__extends(Spacer, _super);
    /**
     * Constructor.
     */
    function Spacer(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //-START- Added by Rihab.J   - needed to be used in dynamically added Spacer.
        $($(_this.elem.nativeElement)[0]).attr('selector', 'spacer');
        return _this;
    }
    Spacer.decorators = [
        { type: Component, args: [{
                    selector: 'spacer',
                    template: "\n    <div class=\"spacer\"></div>\n  "
                }] }
    ];
    /** @nocollapse */
    Spacer.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return Spacer;
}(Container));
export { Spacer };
if (false) {
    /**
     * @type {?}
     * @private
     */
    Spacer.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    Spacer.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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