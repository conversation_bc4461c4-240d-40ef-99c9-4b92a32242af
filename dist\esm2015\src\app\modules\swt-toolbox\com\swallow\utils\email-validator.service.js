/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
export class EmailValidator {
    constructor() { }
    /**
     *  Convenience method for calling a validator
     *  from within a custom validation function.
     *  Each of the standard Angular validators has a similar convenience method.
     *
     * @see mx.validators.ValidationResult
     * @param {?} validator The EmailValidator instance.
     *
     * @param {?} value A field to validate.
     *
     * @param {?} baseField Text representation of the subfield
     *  specified in the value parameter.
     *  For example, if the <code>value</code> parameter specifies value.email,
     *  the <code>baseField</code> value is "email".
     *
     * @return {?} An Array of ValidationResult objects, with one
     *  ValidationResult object for each field examined by the validator.
     *
     */
    static validateEmail(validator, value, baseField) {
        /** @type {?} */
        var results = new Array();
        // Validate the domain name
        // If IP domain, then must follow [x.x.x.x] format
        // Can not have continous periods.
        // Must have at least one period.
        // Must end in a top level domain name that has 2, 3, 4, or 6 characters.
        /** @type {?} */
        var emailStr = String(value);
        /** @type {?} */
        var username = "";
        /** @type {?} */
        var domain = "";
        /** @type {?} */
        var n;
        /** @type {?} */
        var i;
        // Find the @
        /** @type {?} */
        var ampPos = emailStr.indexOf("@");
        if (ampPos == -1) {
            results.push("missingAtSign");
            return results;
        }
        // Make sure there are no extra @s.
        else if (emailStr.indexOf("@", ampPos + 1) != -1) {
            results.push("tooManyAtSigns");
            return results;
        }
        // Separate the address into username and domain.
        username = emailStr.substring(0, ampPos);
        domain = emailStr.substring(ampPos + 1);
        // Validate username has no illegal characters
        // and has at least one character.
        /** @type {?} */
        var usernameLen = username.length;
        if (usernameLen == 0) {
            results.push("missingUsername");
            return results;
        }
        for (i = 0; i < usernameLen; i++) {
            if (this.DISALLOWED_CHARS.indexOf(username.charAt(i)) != -1) {
                results.push("invalidChar");
                return results;
            }
        }
        /** @type {?} */
        var domainLen = domain.length;
        // check for IP address
        if ((domain.charAt(0) == "[") && (domain.charAt(domainLen - 1) == "]")) {
            // Validate IP address
            if (!EmailValidator.isValidIPAddress(domain.substring(1, domainLen - 1))) {
                results.push("invalidIPDomain");
                return results;
            }
        }
        else {
            // Must have at least one period
            /** @type {?} */
            var periodPos = domain.indexOf(".");
            /** @type {?} */
            var nextPeriodPos = 0;
            /** @type {?} */
            var lastDomain = "";
            if (periodPos == -1) {
                results.push("missingPeriodInDomain");
                return results;
            }
            while (true) {
                nextPeriodPos = domain.indexOf(".", periodPos + 1);
                if (nextPeriodPos == -1) {
                    lastDomain = domain.substring(periodPos + 1);
                    if (lastDomain.length != 3 &&
                        lastDomain.length != 2 &&
                        lastDomain.length != 4 &&
                        lastDomain.length != 6) {
                        results.push("invalidDomain");
                        return results;
                    }
                    break;
                }
                else if (nextPeriodPos == periodPos + 1) {
                    results.push("invalidPeriodsInDomain");
                    return results;
                }
                periodPos = nextPeriodPos;
            }
            // Check that there are no illegal characters in the domain.
            for (i = 0; i < domainLen; i++) {
                if (this.DISALLOWED_CHARS.indexOf(domain.charAt(i)) != -1) {
                    results.push("invalidChar");
                    return results;
                }
            }
            // Check that the character immediately after the @ is not a period.
            if (domain.charAt(0) == ".") {
                results.push("invalidDomain");
                return results;
            }
        }
        return results;
    }
    /**
     * Validate a given IP address
     *
     * If IP domain, then must follow [x.x.x.x] format
     * or for IPv6, then follow [x:x:x:x:x:x:x:x] or [x::x:x:x] or some
     * IPv4 hybrid, like [::x.x.x.x] or [0:00::***********]
     * @private
     * @param {?} ipAddr
     * @return {?}
     */
    static isValidIPAddress(ipAddr) {
        /** @type {?} */
        var ipArray = new Array();
        /** @type {?} */
        var pos = 0;
        /** @type {?} */
        var newpos = 0;
        /** @type {?} */
        var item;
        /** @type {?} */
        var n;
        /** @type {?} */
        var i;
        // if you have :, you're in IPv6 mode
        // if you have ., you're in IPv4 mode
        if (ipAddr.indexOf(":") != -1) {
            // IPv6
            // validate by splitting on the colons
            // to make it easier, since :: means zeros, 
            // lets rid ourselves of these wildcards in the beginning
            // and then validate normally
            // get rid of unlimited zeros notation so we can parse better
            /** @type {?} */
            var hasUnlimitedZeros = ipAddr.indexOf("::") != -1;
            if (hasUnlimitedZeros) {
                ipAddr = ipAddr.replace(/^::/, "");
                ipAddr = ipAddr.replace(/::/g, ":");
            }
            while (true) {
                newpos = ipAddr.indexOf(":", pos);
                if (newpos != -1) {
                    ipArray.push(ipAddr.substring(pos, newpos));
                }
                else {
                    ipArray.push(ipAddr.substring(pos));
                    break;
                }
                pos = newpos + 1;
            }
            n = ipArray.length;
            /** @type {?} */
            const lastIsV4 = ipArray[n - 1].indexOf(".") != -1;
            if (lastIsV4) {
                // if no wildcards, length must be 7
                // always, never more than 7
                if ((ipArray.length != 7 && !hasUnlimitedZeros) || (ipArray.length > 7))
                    return false;
                for (i = 0; i < n; i++) {
                    if (i == n - 1) {
                        // IPv4 part...
                        return EmailValidator.isValidIPAddress(ipArray[i]);
                    }
                    item = parseInt(ipArray[i], 16);
                    if (item != 0)
                        return false;
                }
            }
            else {
                // if no wildcards, length must be 8
                // always, never more than 8
                if ((ipArray.length != 8 && !hasUnlimitedZeros) || (ipArray.length > 8))
                    return false;
                for (i = 0; i < n; i++) {
                    item = parseInt(ipArray[i], 16);
                    if (isNaN(item) || item < 0 || item > 0xFFFF)
                        return false;
                }
            }
            return true;
        }
        if (ipAddr.indexOf(".") != -1) {
            // IPv4
            // validate by splling on the periods
            while (true) {
                newpos = ipAddr.indexOf(".", pos);
                if (newpos != -1) {
                    ipArray.push(ipAddr.substring(pos, newpos));
                }
                else {
                    ipArray.push(ipAddr.substring(pos));
                    break;
                }
                pos = newpos + 1;
            }
            if (ipArray.length != 4)
                return false;
            n = ipArray.length;
            for (i = 0; i < n; i++) {
                item = Number(ipArray[i]);
                if (isNaN(item) || item < 0 || item > 255)
                    return false;
            }
            return true;
        }
        return false;
    }
}
EmailValidator.DISALLOWED_CHARS = "()<>,;:\\\"[] `~!#$%^&*+={}|/?'";
EmailValidator.decorators = [
    { type: Injectable }
];
/** @nocollapse */
EmailValidator.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    EmailValidator.DISALLOWED_CHARS;
    /**
     *  Storage for the missingAtSignError property.
     * @type {?}
     * @private
     */
    EmailValidator.prototype._missingAtSignError;
}
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************