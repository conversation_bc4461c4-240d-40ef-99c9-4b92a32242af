import { OnInit, AfterContentChecked, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { HttpClient } from '@angular/common/http';
import { GridOption } from 'angular-slickgrid';
/**
 * Custom pagination component: It allows editing the page number manually
 *  << < Page [1] of 5 > >>
 *
 * <AUTHOR> <EMAIL>
 */
export declare class SwtCommonGridPagination implements OnInit, AfterContentChecked {
    private httpClient;
    private elem;
    private logger;
    maximum: number;
    minimum_: number;
    value: number;
    horizontalAlign: string;
    marginLeft: string;
    marginRight: string;
    totalItems: number;
    processing: boolean;
    styleObject: any;
    realPagination: boolean;
    _gridPaginationOptions: GridOption;
    commonGrid: SwtCommonGrid;
    gridPaginationOptions: GridOption;
    constructor(httpClient: HttpClient, elem: ElementRef);
    ngOnInit(): void;
    ngAfterContentChecked(): void;
    minimum: any;
    changeToFirstPage(event: any): void;
    changeToLastPage(event: any): void;
    changeToNextPage(event: any): void;
    changeToPreviousPage(event: any): void;
    changeToCurrentPage(event: any): void;
    onPageChanged(event?: Event, pageNumber?: number): void;
    private _enabled;
    enabled: boolean;
}
