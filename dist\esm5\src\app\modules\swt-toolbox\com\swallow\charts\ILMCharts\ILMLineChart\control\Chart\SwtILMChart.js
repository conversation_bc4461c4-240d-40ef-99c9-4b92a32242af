/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, ViewChild } from '@angular/core';
import { Container } from '../../../../../containers/swt-container.component';
import { CommonService } from '../../../../../utils/common.service';
import * as Highcharts from 'highcharts';
/** @type {?} */
var Boost = require('highcharts/modules/boost');
/** @type {?} */
var noData = require('highcharts/modules/no-data-to-display');
/** @type {?} */
var More = require('highcharts/highcharts-more');
/** @type {?} */
var patternFill = require('highcharts/modules/pattern-fill');
/** @type {?} */
var Exporting = require("highcharts/modules/exporting");
import HC_exportData from 'highcharts/modules/export-data';
import customWrap from './customWrap';
import { ExternalInterface } from '../../../../../utils/external-interface.service';
Exporting(Highcharts);
HC_exportData(Highcharts);
Boost(Highcharts);
noData(Highcharts);
More(Highcharts);
noData(Highcharts);
customWrap(Highcharts);
patternFill(Highcharts);
var SwtILMChart = /** @class */ (function (_super) {
    tslib_1.__extends(SwtILMChart, _super);
    function SwtILMChart(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.chart = null;
        _this.mousePosition = [];
        _this.invisibleLegend = [];
        _this.tooltipValues = [];
        _this.sourceOfLiquidityChecked = false;
        _this.useCcyMulitplierChecked = false;
        _this.isEntityTimeFrameChecked = false;
        _this.showActualDatasetOnlyChecked = false;
        _this.entityTimeDifference = 0;
        _this.firstLoadDataZones = [];
        _this.zoomFromTime = null;
        _this.zoomToTime = null;
        _this.inData = null;
        _this.redrawFunctionIsEmpty = false;
        _this.visibleLinesInChart = [];
        _this.allThresholds = [];
        _this.updateTempVariable = false;
        _this.currencyFormat = '';
        _this.currencyMutiplierValue = 1;
        _this.currencyDecimalPlaces = 2;
        _this.dateFormatAsString = "";
        _this.saveHighligtedCharts = false;
        _this.highlightedSeries = [];
        _this.callerTabName = '';
        _this.seriesMouseover = [];
        _this.symbol = [];
        _this.markers = [];
        _this.series = [];
        //FIXME:
        //   (function (H) {
        //     Highcharts.Chart.prototype.callbacks.push(function (chart) {
        //         H.addEvent(chart.xAxis[0], 'afterSetExtremes', function (e) {
        //             window.parent.postMessage(['updateNowDate', [callerTabName]], "*");
        //         });
        //     });
        // }(Highcharts));
        //List of variables
        _this.notChecked = false;
        _this.isSODClicked = true;
        _this.isThresholderClicked = true;
        _this.hasPlotBand = false;
        _this.hasCheckedCurrency = false;
        _this.chartNotDrawn = true;
        _this.chartNotDrawnNewdata = null;
        _this.chartNotDrawntabName = null;
        _this.listOfBandsIds = ['plot-band-0', 'plot-band-1', 'plot-band-2', 'plot-band-3', 'plot-line-1'];
        _this.log10 = Math.log(10);
        _this.lastExportType = null;
        _this.each = Highcharts.each;
        _this.pick = Highcharts.pick;
        //check if needed
        _this.seriesTypes = Highcharts.seriesType;
        _this.downloadAttrSupported = document.createElement('a').download !== undefined;
        return _this;
        // if (window.addEventListener) {
        //   window.addEventListener("message", this.receiver.bind(this), false);
        // } else {
        //   (<any>window).attachEvent("onmessage", this.receiver.bind(this));
        // }
    }
    /**
     * @param {?} methodName
     * @param {?} methodArguments
     * @return {?}
     */
    SwtILMChart.prototype.callMethodByName = /**
     * @param {?} methodName
     * @param {?} methodArguments
     * @return {?}
     */
    function (methodName, methodArguments) {
        if (this[methodName]) {
            this[methodName].apply(this, methodArguments);
        }
    };
    /**
     * @param {?} newData
     * @param {?} mergeData
     * @param {?} updateTempVar
     * @param {?} tabName
     * @return {?}
     */
    SwtILMChart.prototype.setILMData = /**
     * @param {?} newData
     * @param {?} mergeData
     * @param {?} updateTempVar
     * @param {?} tabName
     * @return {?}
     */
    function (newData, mergeData, updateTempVar, tabName) {
        if (newData != "" && newData != null && newData.length > 0) {
            if (mergeData) {
                this.inData = this.inData.concat(JSON.parse(newData));
                this.mergeNewCharts(JSON.parse(newData));
            }
            else {
                if (!this.chartNotDrawn) {
                    this.firstLoad(newData, true, tabName);
                }
                else {
                    this.chartNotDrawnNewdata = newData;
                    this.chartNotDrawntabName = tabName;
                }
                //                     firstLoad(parent.getJSONData(window.frameElement.id), true);
                return;
            }
        }
        this.updateTempVariable = updateTempVar;
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.forceDrawChartIfNotDrawn = /**
     * @return {?}
     */
    function () {
        if (this.chartNotDrawn) {
            this.firstLoad(this.chartNotDrawnNewdata, true, this.chartNotDrawntabName);
            this.chartNotDrawn = false;
        }
    };
    /**
     * @param {?} imageName
     * @param {?} color
     * @return {?}
     */
    SwtILMChart.prototype.getFillPatternForImange = /**
     * @param {?} imageName
     * @param {?} color
     * @return {?}
     */
    function (imageName, color) {
        /** @type {?} */
        var patternWidth = 4;
        /** @type {?} */
        var patternHeight = 4;
        /** @type {?} */
        var patternValue;
        if (imageName == "b.png" || imageName == "a.png" || imageName == "aqua_45.png" || imageName == "deeppink_45") {
            patternValue = 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11';
        }
        else if (imageName == "greenyellow.png" || imageName == "indianred.png" || imageName == "magenta.png") {
            patternValue = "M 2 5 L 5 2 L 8 5 L 5 8 Z";
        }
        else if (imageName == "goldenrod.png" || imageName == "green.png") {
            patternValue = 'M 0 3 L 10 3 M 0 8 L 10 8';
        }
        //   pattern = {
        //     path: {
        //         d: 'M 3 0 L 3 10 M 8 0 L 8 10',
        //         strokeWidth: 1
        //     },
        //     width: 4,
        //     height: 4,
        //     color: color,
        //     opacity: 0.8
        // }
        return {
            pattern: {
                path: {
                    d: patternValue,
                    strokeWidth: 1
                },
                width: patternWidth,
                height: patternHeight,
                color: color,
                opacity: 0.8
            }
        };
    };
    /**
     * @param {?} dataFromParent
     * @param {?} resetAll
     * @param {?} tabName
     * @return {?}
     */
    SwtILMChart.prototype.firstLoad = /**
     * @param {?} dataFromParent
     * @param {?} resetAll
     * @param {?} tabName
     * @return {?}
     */
    function (dataFromParent, resetAll, tabName) {
        this.invisibleLegend = [];
        this.tooltipValues = [];
        this.isSODClicked = false;
        this.sourceOfLiquidityChecked = false;
        this.useCcyMulitplierChecked = false;
        this.isEntityTimeFrameChecked = false;
        this.showActualDatasetOnlyChecked = false;
        this.entityTimeDifference = 0;
        this.firstLoadDataZones = [];
        this.zoomFromTime = null;
        this.zoomToTime = null;
        this.inData = null;
        this.visibleLinesInChart = [];
        this.allThresholds = [];
        this.callerTabName = tabName;
        if (dataFromParent) {
            if (dataFromParent[0])
                this.inData = JSON.parse(dataFromParent[0]);
            this.isSODClicked = dataFromParent[1];
            this.sourceOfLiquidityChecked = dataFromParent[2];
            this.firstLoadDataZones = dataFromParent[3];
            this.useCcyMulitplierChecked = dataFromParent[4];
            this.isEntityTimeFrameChecked = dataFromParent[5];
            this.zoomFromTime = dataFromParent[6];
            this.zoomToTime = dataFromParent[7];
            this.showActualDatasetOnlyChecked = dataFromParent[8];
            this.currencyFormat = dataFromParent[9];
            this.currencyMutiplierValue = dataFromParent[10];
            this.currencyDecimalPlaces = dataFromParent[11];
            this.dateFormatAsString = dataFromParent[12];
            this.saveHighligtedCharts = dataFromParent[13];
            this.highlightedSeries = dataFromParent[14].split(',');
            this.saveUncheckedItems = dataFromParent[15];
            this.uncheckedItemsFromParent = dataFromParent[16];
            this.updateTempVariable = false;
            // var chart = $("#container").highcharts();
            this.chart = Highcharts.chart(this.containerHighChart.nativeElement, {});
            if (this.chart) {
                // $('#container').unbind('click');
                $(this.containerHighChart.nativeElement).unbind('click');
                this.chart.destroy();
                this.chart = null;
            }
            this.createILMChart();
        }
        //         window.parent.postMessage({
        //             'func': 'parentFuncName',
        //             'message': 'Message text from iframe.'
        //         }, "*");
    };
    /**
     * @param {?} dataZonesJSON
     * @return {?}
     */
    SwtILMChart.prototype.setILMDataZones = /**
     * @param {?} dataZonesJSON
     * @return {?}
     */
    function (dataZonesJSON) {
        this.dataZones = dataZonesJSON;
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.destroyChart = /**
     * @return {?}
     */
    function () {
        if (this.chart) {
            try {
                $(this.containerHighChart.nativeElement).unbind('click');
                this.chart.destroy();
                this.chart = null;
            }
            catch (error) {
            }
            // $('#container').unbind('click');
        }
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.getData = /**
     * @return {?}
     */
    function () {
        return [this.inData, this.dataZones, this.updateTempVariable];
    };
    /**
     * @param {?} newDataAsJSON
     * @return {?}
     */
    SwtILMChart.prototype.mergeNewCharts = /**
     * @param {?} newDataAsJSON
     * @return {?}
     */
    function (newDataAsJSON) {
        // var chart = $("#container").highcharts();
        /** @type {?} */
        var serieNumber;
        /** @type {?} */
        var timestamp;
        /** @type {?} */
        var VALUE = 0;
        /** @type {?} */
        var type = '';
        /** @type {?} */
        var color = '';
        /** @type {?} */
        var yAxis = 1;
        /** @type {?} */
        var fillColor = {};
        /** @type {?} */
        var pointInterval = 0;
        /** @type {?} */
        var pointStart = 0;
        /** @type {?} */
        var dashStyle = '';
        /** @type {?} */
        var isClicked = false;
        /** @type {?} */
        var zIndex = 999;
        /** @type {?} */
        var isAreaDashed = '';
        for (serieNumber = 0; serieNumber < newDataAsJSON.length; serieNumber++) {
            if (newDataAsJSON[serieNumber].type == 'area') {
                yAxis = 0;
            }
            else {
                yAxis = 1;
            }
            if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            }
            else if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            }
            else {
                dashStyle = '';
            }
            if (newDataAsJSON[serieNumber].type == 'area' && newDataAsJSON[serieNumber].color.indexOf('png') != -1) {
                fillColor = this.getFillPatternForImange(newDataAsJSON[serieNumber].color, newDataAsJSON[serieNumber].borderColor);
                // {
                //   pattern: {
                //     path: {
                //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                //       strokeWidth: 1
                //     },
                //     width: 4,
                //     height: 4,
                //     color: newDataAsJSON[serieNumber].borderColor,
                //   }
                // }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';
            }
            if (newDataAsJSON[serieNumber].type == 'line') {
                this.series.push({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                    "zIndex": 2,
                    "lineWidth": 1.5
                });
                this.chart.addSeries({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                    "zIndex": 2
                });
            }
            else if (newDataAsJSON[serieNumber].type == 'area') {
                if (newDataAsJSON[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    this.series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                    this.chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                }
                else {
                    this.series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                    this.chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                }
            }
            else if (newDataAsJSON[serieNumber].type == 'Threshold') {
                if (newDataAsJSON[serieNumber].visibility == "true") {
                    this.visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name,
                        "tooltipText": newDataAsJSON[serieNumber].name,
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": newDataAsJSON[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                    });
                    this.visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name + '.Double',
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                this.allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name,
                    "tooltipText": newDataAsJSON[serieNumber].name,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": newDataAsJSON[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"
                    // "className": 'highcharts-color-1'
                });
                this.allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name + '.Double',
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"
                });
            }
            if (newDataAsJSON[serieNumber].visibility != 'true') {
                this.invisibleLegend.push(newDataAsJSON[serieNumber].name);
            }
        }
        this.disableAutoRedraw();
        if (!this.isSODClicked) {
            this.uncheckSODandalignScale();
        }
        /* if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
             showLiquiditySource(true, null);
         }*/
        this.updateVisibleThresholds();
        this.chart.yAxis[1].update({
            plotLines: this.visibleLinesInChart.slice()
        });
        this.enableAutoredrawAndRedrawChart();
        /*  if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
               showLiquiditySource(true, firstLoadDataZones);
           }
    
           enableAutoredrawAndRedrawChart();*/
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.createILMChart = /**
     * @return {?}
     */
    function () {
        this.series = [];
        /** @type {?} */
        var that = this;
        // $("#container").empty();
        /** @type {?} */
        var $tooltipBands = $('#tooltip_bands');
        $tooltipBands.hide();
        /** @type {?} */
        var $text = $('#tooltiptext');
        //FIXME:CHECK if works or needed
        /** @type {?} */
        var displayTooltip = (/**
         * @param {?} text
         * @param {?} left
         * @param {?} x
         * @param {?} y
         * @return {?}
         */
        function (text, left, x, y) {
            $text.text(text);
            $tooltipBands.show();
            $tooltipBands.css('left', parseInt(x) + 'px');
            $tooltipBands.css('top', parseInt(y) + 100 + 'px');
        });
        /** @type {?} */
        var hideTooltip = (/**
         * @return {?}
         */
        function () {
            clearTimeout(this.timer);
            this.timer = setTimeout((/**
             * @return {?}
             */
            function () {
                $tooltipBands.fadeOut();
            }), 1000);
        });
        /** @type {?} */
        var gfxPath = 'https://raw.githubusercontent.com/highcharts/pattern-fill/master/graphics/';
        /** @type {?} */
        var inData = this.getData()[0];
        /** @type {?} */
        var serieNumber;
        /** @type {?} */
        var timestamp;
        /** @type {?} */
        var VALUE = 0;
        /** @type {?} */
        var type = '';
        /** @type {?} */
        var color = '';
        /** @type {?} */
        var yAxis = 1;
        /** @type {?} */
        var fillColor = {};
        /** @type {?} */
        var pointInterval = 0;
        /** @type {?} */
        var pointStart = 0;
        /** @type {?} */
        var dashStyle = '';
        /** @type {?} */
        var isClicked = false;
        /** @type {?} */
        var zIndex = 999;
        /** @type {?} */
        var isAreaDashed = '';
        /** @type {?} */
        var data = [];
        // marker = []
        /** @type {?} */
        var marker = {
            symbol: 'circle',
            states: {
                hover: { enabled: false }
            }
        };
        this.visibleLinesInChart = [],
            this.allThresholds = [];
        for (serieNumber = 0; serieNumber < inData.length; serieNumber++) {
            if (inData[serieNumber].type == 'area') {
                yAxis = 0;
            }
            else {
                yAxis = 1;
            }
            if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            }
            else if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            }
            else {
                dashStyle = '';
            }
            if (inData[serieNumber].type == 'area' && inData[serieNumber].color.indexOf('png') != -1) {
                fillColor = this.getFillPatternForImange(inData[serieNumber].color, inData[serieNumber].borderColor);
                // {
                //   pattern: {
                //     path: {
                //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                //       strokeWidth: 1
                //     },
                //     width: 4,
                //     height: 4,
                //     color: inData[serieNumber].borderColor,
                //   }
                // }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';
            }
            /** @type {?} */
            var isVisibleItem = (inData[serieNumber].visibility == 'true' && this.uncheckedItemsFromParent.indexOf(inData[serieNumber].name) == -1);
            if (isVisibleItem) {
                inData[serieNumber].visibility = 'true';
            }
            else {
                inData[serieNumber].visibility = 'false';
            }
            if (inData[serieNumber].type == 'line') {
                this.series.push({
                    "name": inData[serieNumber].chartlegendDisplayName,
                    "tooltipName": inData[serieNumber].chartDisplayLabel,
                    "chartID": inData[serieNumber].chartDisplayName,
                    "data": inData[serieNumber].data.slice(),
                    "type": inData[serieNumber].type,
                    "valueId": inData[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                    "pointInterval": inData[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": inData[serieNumber].color,
                    "visible": (inData[serieNumber].visibility == 'true'),
                    "zIndex": 2,
                    "lineWidth": 1.5
                });
            }
            else if (inData[serieNumber].type == 'area') {
                if (inData[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    this.series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                }
                else {
                    this.series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(inData[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(inData[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(inData[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                }
            }
            else if (inData[serieNumber].type == 'Threshold') {
                /** @type {?} */
                var groupName = inData[serieNumber].name.split('.')[0];
                /** @type {?} */
                var isVisibleItem_1 = (inData[serieNumber].visibility == 'true' && this.uncheckedItemsFromParent.indexOf(groupName + ".Threshold") == -1);
                if (isVisibleItem_1) {
                    inData[serieNumber].visibility = 'true';
                }
                else {
                    inData[serieNumber].visibility = 'false';
                }
                if (inData[serieNumber].visibility == "true") {
                    this.visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name,
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": inData[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                    });
                    this.visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name + '.Double',
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                this.allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name,
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": inData[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"
                    // "className": 'highcharts-color-1'
                });
                this.allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name + '.Double',
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"
                });
            }
            if (inData[serieNumber].visibility != 'true') {
                this.invisibleLegend.push(inData[serieNumber].name);
            }
        }
        Highcharts.setOptions({
            // lang: {numericSymbols: ['k', 'M', 'B', 'T', 'P', 'E']},
            time: {
                useUTC: false
            }
        });
        /** @type {?} */
        var colorSerie1 = localStorage.getItem("colorSerie1");
        /** @type {?} */
        var markersSeries;
        this.options = {
            chart: {
                //animation: false,
                alignTicks: false,
                spacingLeft: 0,
                spacingRight: 0,
                //spacingBottom: 0,
                // marginTop: 150,
                events: {
                    load: (/**
                     * @return {?}
                     */
                    function () {
                        // window.parent.postMessage(['chartCreationCompleteHandler', [that.callerTabName]], "*");
                    }),
                },
            },
            title: {
                text: ''
            },
            legend: {
                enabled: false,
                layout: 'horizontal',
                align: 'center',
                verticalAlign: 'bottom',
                floating: true,
                symbolWidth: 30,
                backgroundColor: 'transparent',
                x: 0,
                y: 20
            },
            boost: {
                useGPUTranslations: true,
                usePreAllocated: true,
            },
            xAxis: [{
                    type: 'datetime',
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return Highcharts.dateFormat('%H:%M', this.value);
                        }),
                        style: {
                            fontSize: "10px"
                        },
                    },
                    lineWidth: 2,
                    gridLineWidth: 1,
                    ordinal: false,
                    startOnTick: false,
                    endOnTick: false,
                    minPadding: 0,
                    maxPadding: 0,
                    tickPixelInterval: 55,
                    /*tickInterval: 3600 * 1000,
                            maxTickInterval: 3600 * 1000,*/
                    //                         title: {
                    //                             useHTML: true,
                    //                             text: "Time" + " " + "<button id ='xAxisCurrency' class='button-link'>(Currency)</button>"
                    //                         }
                    title: {
                        useHTML: false,
                        text: ""
                    },
                }],
            yAxis: [{
                    // Primary yAxis
                    lineWidth: 3,
                    offset: 0,
                    tickWidth: 1,
                    title: {
                        text: 'Accumulated D/C',
                        margin: 0,
                        style: {
                            color: "black"
                        }
                    },
                    tickPixelInterval: 40,
                    opposite: true,
                    showEmpty: false,
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return that.rightVerticalAxisFormatter(this.value);
                        }),
                        style: {
                            fontSize: "10px",
                            color: "black"
                        },
                    },
                }, {
                    // Secondary yAxis
                    gridLineWidth: 0,
                    showEmpty: false,
                    lineWidth: 3,
                    offset: 0,
                    //I put offset here because ilm zones at 00:00 hide a part of this zone     
                    tickWidth: 1,
                    title: {
                        text: 'Balance',
                        margin: 0,
                        style: {
                            color: "black"
                        }
                    },
                    tickPixelInterval: 40,
                    plotLines: this.visibleLinesInChart.slice(),
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return that.leftVerticalAxisFormatter(this.value);
                        }),
                        style: {
                            fontSize: "10px",
                            color: "black"
                        },
                    },
                }
            ],
            optimize: {
                enableTooltip: true
            },
            tooltip: {
                useHTML: true,
                borderWidth: 0,
                backgroundColor: "rgba(255,255,255,0)",
                borderRadius: 0,
                shadow: false,
                padding: 2,
                positioner: (/**
                 * @param {?} labelWidth
                 * @param {?} labelHeight
                 * @param {?} point
                 * @return {?}
                 */
                function (labelWidth, labelHeight, point) {
                    try {
                        /** @type {?} */
                        var tooltipX;
                        /** @type {?} */
                        var tooltipY;
                        that.mousePosition = [];
                        if (point.plotX + labelWidth > that.chart.plotWidth) {
                            tooltipX = point.plotX + that.chart.plotLeft - labelWidth - 20;
                        }
                        else {
                            tooltipX = point.plotX + that.chart.plotLeft + 20;
                        }
                        tooltipY = point.plotY + that.chart.plotTop - 20;
                        if (tooltipY + labelHeight > $(that.containerHighChart.nativeElement).height()) {
                            tooltipY = 5;
                        }
                        that.mousePosition.push({ "positionX": point.plotX, "positionY": point.plotY });
                        return {
                            x: tooltipX,
                            y: tooltipY + 50
                        };
                    }
                    catch (error) {
                        console.log("SwtILMChart -> createILMChart  positioner-> error", error);
                        that.mousePosition.push({ "positionX": 0, "positionY": 0 });
                        return {
                            x: 0,
                            y: 50
                        };
                    }
                }),
                formatter: (/**
                 * @return {?}
                 */
                function () {
                    try {
                        /** @type {?} */
                        var t = [];
                        /** @type {?} */
                        var values = [];
                        /** @type {?} */
                        var isDashed = '\u25CF';
                        /** @type {?} */
                        var yAxisNumber;
                        tooltipArrays = [];
                        /** @type {?} */
                        var xMax = that.chart.plotWidth;
                        /** @type {?} */
                        var DataTh = [];
                        /** @type {?} */
                        var formattedValue;
                        /** @type {?} */
                        var dateAsString;
                        /** @type {?} */
                        var tooltipArrays = [];
                        $.each(this.points, (/**
                         * @param {?} i
                         * @param {?} point
                         * @return {?}
                         */
                        function (i, point) {
                            if (point.series.userOptions.type == 'line' && that.invisibleLegend.indexOf(point.series.userOptions.valueId) == -1) {
                                tooltipArrays.push({ "y": point.series.yData[this.point.index], "name": point.series.userOptions.valueId });
                                if (dateAsString == null) {
                                    if (that.dateFormatAsString.toUpperCase() == "DD/MM/YYYY") {
                                        dateAsString = Highcharts.dateFormat('%d/%m/%Y %H:%M:%S', this.x);
                                    }
                                    else {
                                        dateAsString = Highcharts.dateFormat('%m/%d/%Y %H:%M:%S', this.x);
                                    }
                                }
                            }
                            if (this.point.series.userOptions.type == 'area') {
                                yAxisNumber = 0;
                                formattedValue = that.rightVerticalAxisFormatter(point.y);
                            }
                            else {
                                yAxisNumber = 1;
                                if (that.useCcyMulitplierChecked == true) {
                                    formattedValue = that.commonAxisFormatter(point.y, 3);
                                }
                                else {
                                    formattedValue = that.leftVerticalAxisFormatter(point.y);
                                }
                            }
                            if (point.series.userOptions.dashStyle == "Dash" || (point.series.userOptions.isAreaDashed == 'dashed')) {
                                //Dashed
                                isDashed = '\u25CD';
                            }
                            else if (point.series.userOptions.dashStyle == "shortdot") {
                                //Dotted  \u25CC
                                isDashed = '\u2687';
                            }
                            else {
                                isDashed = '\u25CF';
                            }
                            if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && point.point.plotX > that.mousePosition[0].positionX - 10 && point.point.plotX < that.mousePosition[0].positionX + 10 && point.point.plotY > that.mousePosition[0].positionY - 10 && point.point.plotY < that.mousePosition[0].positionY + 10) {
                                t.push('<span style="color:' + point.color + '">' + isDashed + '</span> ' + point.series.options.tooltipName + ' (' + Highcharts.dateFormat('%H:%M', this.x) + ';' +
                                    formattedValue + ')' + '<span>' + '</br>');
                            }
                        }));
                        //thresholders markers and tooltip
                        for (var i = 0; i < that.chart.yAxis[1].plotLinesAndBands.length; i = i + 2) {
                            DataTh = that.chart.yAxis[1].plotLinesAndBands[i].options.value;
                            /** @type {?} */
                            var space = (that.chart.yAxis[1].plotLinesAndBands[i].options.id == "plot-line-1") ? '<br/>' : '';
                            if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && that.chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && that.mousePosition[0].positionX > 0 && that.mousePosition[0].positionX < 10
                                && that.mousePosition[0].positionY + that.chart.plotTop - 10 <= that.chart.yAxis[1].toPixels(DataTh) && that.chart.yAxis[1].toPixels(DataTh) <= that.mousePosition[0].positionY + that.chart.plotTop + 10) {
                                t.push('<span class="circle" style="color:black"></span> ' + that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + space + '(' + that.toDate(that.chart.xAxis[0].toValue(that.mousePosition[0].positionX + that.chart.plotLeft)) + '; ' +
                                    that.leftVerticalAxisFormatter(DataTh) + ')' + '<span>' + '</br>');
                                if (that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] == null) {
                                    that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] = that.chart.renderer.label('\u29BF', that.chart.plotLeft - 5, that.chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                        fontSize: '9pt',
                                        color: 'black'
                                    }).attr({
                                        zIndex: 999,
                                    }).add();
                                }
                            }
                            else if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && that.chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && that.mousePosition[0].positionY + that.chart.plotTop <= that.chart.yAxis[1].toPixels(DataTh) && that.mousePosition[0].positionY + that.chart.plotTop >= that.chart.yAxis[1].toPixels(DataTh) - 10 && that.mousePosition[0].positionX > xMax - 20 && that.mousePosition[0].positionX <= xMax) {
                                t.push('<span class="circle" style="color: black"></span> ' + that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + space + '(' + that.toDate(that.chart.xAxis[0].toValue(that.mousePosition[0].positionX + that.chart.plotLeft)) + '; ' +
                                    that.leftVerticalAxisFormatter(DataTh) + ')' + '<span>' + '</br>');
                                if (that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] == null) {
                                    that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] = that.chart.renderer.label('\u29BF', xMax + that.chart.plotLeft - 10, that.chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                        fontSize: '9pt',
                                        color: 'black'
                                    }).attr({
                                        zIndex: 999,
                                    }).add();
                                }
                            }
                        }
                        for (var j in that.symbol) {
                            /** @type {?} */
                            var value = that.symbol[j];
                            if (!that.isEmpty(value) && value !== 'undefined' && value !== null) {
                                if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition)) {
                                    if (that.mousePosition[0].positionX + that.chart.plotLeft >= value.x + 15 || that.mousePosition[0].positionX + that.chart.plotLeft <= value.x - 5
                                        || that.mousePosition[0].positionY + that.chart.plotTop >= value.y + 15 || that.mousePosition[0].positionY + that.chart.plotTop <= value.y - 10) {
                                        value.destroy();
                                        delete that.symbol[j];
                                    }
                                }
                            }
                        }
                        //                         parent.updateChartsLiveValues(window.frameElement.id,tooltipArrays, tooltipArrays.length, dateAsString);
                        //                         window.parent.postMessage({
                        //                             'func': 'updateChartsLiveValues',
                        //                             'message': 'Message text from iframe.'
                        //                         }, "*");
                        window.parent.postMessage(['updateChartsLiveValues', [that.callerTabName, tooltipArrays, tooltipArrays.length, dateAsString]], "*");
                        return t;
                    }
                    catch (error) {
                        console.log("SwtILMChart -> createILMChart -> formatter error", error);
                    }
                }),
                shared: true
            },
            credits: {
                enabled: false
            },
            plotOptions: {
                series: {
                    animation: {
                        duration: 200
                    },
                    point: {
                        events: {
                            mouseOver: (/**
                             * @return {?}
                             */
                            function () {
                                that.seriesMouseover = this.series;
                            }),
                            mouseOut: (/**
                             * @return {?}
                             */
                            function () {
                                that.seriesMouseover = [];
                                this.series.chart.tooltip.hide();
                            })
                        }
                    },
                    states: {
                        hover: {
                            halo: {
                                size: 0
                            },
                            lineWidthPlus: 0,
                            marker: {
                                enabled: true,
                                radius: 2,
                                symbol: "circle"
                            },
                        },
                    },
                    marker: {
                        radius: 2,
                        symbol: "circle"
                    },
                    fillOpacity: 0.3
                },
            },
            exporting: {
                enabled: false
            },
            series: that.series,
        };
        this.chart = Highcharts.chart(this.containerHighChart.nativeElement, this.options);
        //updateClock();
        // $('#container').bind('click', function (e) {
        $(this.containerHighChart.nativeElement).bind('click', (/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            if (that.seriesMouseover.length != 0) {
                if (that.seriesMouseover["userOptions"].isClicked == false) {
                    if (that.seriesMouseover["userOptions"].type == 'line') {
                        that.seriesMouseover['update']({
                            lineWidth: 3
                        });
                    }
                    else if (that.seriesMouseover["userOptions"].type == 'area') {
                        if (!that.seriesMouseover) {
                            console.warn('seriesMouseover is undefined');
                            return;
                        }
                        try {
                            // Check if required properties exist
                            if (!that.seriesMouseover["userOptions"] || !that.seriesMouseover["area"] || !that.seriesMouseover["color"]) {
                                console.warn('Missing required properties in seriesMouseover');
                                return;
                            }
                            if (that.seriesMouseover["userOptions"].isAreaDashed === 'dashed') {
                                that.seriesMouseover["area"].attr("fill", that.seriesMouseover["color"]);
                            }
                            else {
                                /** @type {?} */
                                var rgbaCol = 'rgba(' +
                                    parseInt(that.seriesMouseover["color"].slice(-6, -4), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-4, -2), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-2), 16) + ',1)';
                                that.seriesMouseover["area"].attr("fill", rgbaCol);
                            }
                        }
                        catch (error) {
                            console.error('Error processing seriesMouseover:', error);
                        }
                    }
                    that.seriesMouseover["userOptions"].isClicked = true;
                    //                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover["userOptions"].valueId, true);
                    window.parent.postMessage(['highlightLegendFromHighChart', [that.callerTabName, that.seriesMouseover["userOptions"].valueId, true]], "*");
                }
                else if (that.seriesMouseover["userOptions"].isClicked == true) {
                    if (that.seriesMouseover["userOptions"].type == 'line') {
                        that.seriesMouseover['update']({
                            lineWidth: 1.5
                        });
                        // seriesMouseover.options.lineWidth = 0
                    }
                    else if (that.seriesMouseover["userOptions"].type == 'area') {
                        if (!that.seriesMouseover) {
                            console.warn('seriesMouseover is undefined');
                            return;
                        }
                        try {
                            // Check if required properties exist
                            if (!that.seriesMouseover["userOptions"] || !that.seriesMouseover["area"] || !that.seriesMouseover["color"]) {
                                console.warn('Missing required properties in seriesMouseover');
                                return;
                            }
                            if (that.seriesMouseover["userOptions"].isAreaDashed === 'dashed') {
                                if (!that.seriesMouseover["userOptions"].fillColor) {
                                    console.warn('fillColor is undefined');
                                    return;
                                }
                                that.seriesMouseover["area"].attr("fill", that.seriesMouseover["userOptions"].fillColor);
                            }
                            else {
                                /** @type {?} */
                                var rgbaCol = 'rgba(' +
                                    parseInt(that.seriesMouseover["color"].slice(-6, -4), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-4, -2), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-2), 16) + ',0.7)';
                                that.seriesMouseover["area"].attr("fill", rgbaCol);
                            }
                        }
                        catch (error) {
                            console.error('Error processing seriesMouseover:', error);
                        }
                    }
                    that.seriesMouseover["userOptions"].isClicked = false;
                    //                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover["userOptions"].valueId,false);
                    window.parent.postMessage(['highlightLegendFromHighChart', [that.callerTabName, that.seriesMouseover["userOptions"].valueId, false]], "*");
                }
            }
            $(window).focus();
        }));
        if (that.zoomFromTime && that.zoomToTime) {
            that.zoom(that.zoomFromTime, that.zoomToTime);
        }
        else {
            // Do nothing,no zooming is required
        }
        that.disableAutoRedraw();
        if (!that.isSODClicked) {
            that.uncheckSODandalignScale();
        }
        if (that.sourceOfLiquidityChecked && this.firstLoadDataZones) {
            that.showLiquiditySource(true, that.firstLoadDataZones);
        }
        if (that.saveHighligtedCharts) {
            if (that.highlightedSeries) {
                for (var i = 0; i < that.highlightedSeries.length; i++) {
                    //FIXME:TEST IT
                    that.highlightSerie(that.highlightedSeries[i], false);
                }
            }
        }
        that.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} dateAsDateStamp
     * @return {?}
     */
    SwtILMChart.prototype.updateClock = /**
     * @param {?} dateAsDateStamp
     * @return {?}
     */
    function (dateAsDateStamp) {
        if (dateAsDateStamp) {
            if (this.chart) {
                /** @type {?} */
                var now = new Date(dateAsDateStamp);
                // current date
                /** @type {?} */
                var time = this.addZero(now.getHours()) + ':' + this.addZero(now.getMinutes());
                /** @type {?} */
                var idLine = "nowPlotline";
                //var newDate = new Date(2008, 9, 1, now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds()).getTime();
                this.chart.xAxis[0].removePlotLine(idLine);
                this.chart.xAxis[0].addPlotLine({
                    color: 'red',
                    value: now,
                    width: 1.5,
                    zIndex: 20,
                    label: {
                        text: time,
                        style: {
                            color: 'red',
                            fontSize: "11px",
                            fontWeight: "Normal"
                        },
                        y: 10,
                        x: 10,
                        rotation: 0,
                        verticalAlign: 'top',
                        textAlign: 'left',
                    },
                    id: idLine
                });
            }
        }
    };
    /**
     * @param {?} showHide
     * @param {?} visibleYFields
     * @return {?}
     */
    SwtILMChart.prototype.showHideActualDataSet = /**
     * @param {?} showHide
     * @param {?} visibleYFields
     * @return {?}
     */
    function (showHide, visibleYFields) {
        this.disableAutoRedraw();
        if (showHide) {
            this.checkDataSet(visibleYFields);
        }
        else {
            this.unCheckDataSet(visibleYFields);
        }
        this.enableAutoredrawAndRedrawChart();
        if (!this.isSODClicked)
            //FIXME:
            this.alignScale(false);
    };
    /**
     * @param {?} showHide
     * @param {?} groupId
     * @return {?}
     */
    SwtILMChart.prototype.showHideThreshold = /**
     * @param {?} showHide
     * @param {?} groupId
     * @return {?}
     */
    function (showHide, groupId) {
        if (!showHide) {
            this.hideThreshold(groupId);
        }
        else {
            this.showThreshold(groupId);
        }
    };
    /**
     * @param {?} alignScale
     * @return {?}
     */
    SwtILMChart.prototype.alignScaleWithSOD = /**
     * @param {?} alignScale
     * @return {?}
     */
    function (alignScale) {
        this.disableAutoRedraw();
        if (!alignScale) {
            this.uncheckSODandalignScale();
        }
        else {
            this.applySODandUnalignScale();
        }
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} showHide
     * @param {?} valuesUpdated
     * @param {?} dataZonesJSON
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    SwtILMChart.prototype.showHideSourcesOfLiquidity = /**
     * @param {?} showHide
     * @param {?} valuesUpdated
     * @param {?} dataZonesJSON
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    function (showHide, valuesUpdated, dataZonesJSON, visibleItemsInTree) {
        this.disableAutoRedraw();
        if (showHide) {
            this.showLiquiditySource(valuesUpdated, dataZonesJSON);
        }
        else {
            this.hideLiquiditySource(visibleItemsInTree);
        }
        this.enableAutoredrawAndRedrawChart();
    };
    ///List of functions
    //Hide thresholdes
    ///List of functions
    //Hide thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    SwtILMChart.prototype.hideThreshold = 
    ///List of functions
    //Hide thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    function (groupName) {
        /** @type {?} */
        var chart = $('#container').highcharts();
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.chart.yAxis[1].removePlotLine(this.allThresholds[i].id);
                this.allThresholds[i].visible = false;
            }
        }
        this.updateVisibleThresholds();
    };
    /**
     * @param {?} groupName
     * @return {?}
     */
    SwtILMChart.prototype.removeThreshold = /**
     * @param {?} groupName
     * @return {?}
     */
    function (groupName) {
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.chart.yAxis[1].removePlotLine(this.allThresholds[i].id);
                this.allThresholds.splice(i, 1);
                i--;
            }
        }
        this.updateVisibleThresholds();
    };
    //Show Thresholdes
    //Show Thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    SwtILMChart.prototype.showThreshold = 
    //Show Thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    function (groupName) {
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.allThresholds[i].visible = true;
            }
        }
        this.updateVisibleThresholds();
        this.chart.yAxis[1].update({
            plotLines: this.visibleLinesInChart.slice()
        });
    };
    //DataSetOnly
    //DataSetOnly
    /**
     * @param {?} visibleYFields
     * @return {?}
     */
    SwtILMChart.prototype.checkDataSet = 
    //DataSetOnly
    /**
     * @param {?} visibleYFields
     * @return {?}
     */
    function (visibleYFields) {
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) == -1) {
                if (this.chart.series[i].userOptions.chartID == 'afc' || this.chart.series[i].userOptions.chartID == 'afd' || this.chart.series[i].userOptions.chartID == 'fbb' || this.chart.series[i].userOptions.chartID == 'fbia') {
                    this.chart.series[i].hide();
                    this.unHighlightSerieFunction(this.chart.series[i]);
                    this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                }
            }
        }
        this.showActualDatasetOnlyChecked = true;
    };
    /**
     * @param {?} visibleYFields
     * @return {?}
     */
    SwtILMChart.prototype.unCheckDataSet = /**
     * @param {?} visibleYFields
     * @return {?}
     */
    function (visibleYFields) {
        if (this.invisibleLegend.length > 0 && visibleYFields != null && visibleYFields.length > 0) {
            for (var i = 0; i < this.chart.series.length; i++) {
                if (this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) != -1) {
                    //  afc  afd fbb fbia
                    //  if(!sourceOfLiquidityChecked) {
                    if (visibleYFields.indexOf(this.chart.series[i].userOptions.valueId) > -1) {
                        this.chart.series[i].show();
                        for (var j = 0; j < this.invisibleLegend.length; j++) {
                            if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                                this.invisibleLegend.splice(j, 1);
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (this.sourceOfLiquidityChecked)
            this.showLiquiditySource(true, null);
        this.showActualDatasetOnlyChecked = false;
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.enableAutoredrawAndRedrawChart = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var axesChanged = false;
        this.chart.redraw = this._redraw;
        this.chart.redraw();
        this.redrawFunctionIsEmpty = false;
        if (this.chart.yAxis[1].min == undefined && this.chart.yAxis[1].max == undefined) {
            this.chart.yAxis[1].update({
                offset: 10
            });
        }
        else {
            this.chart.yAxis[1].update({
                offset: 2
            });
        }
        if (this.chart.yAxis[0].min == undefined && this.chart.yAxis[0].max == undefined) {
            this.chart.yAxis[0].update({
                offset: 10
            });
        }
        else {
            this.chart.yAxis[0].update({
                offset: 2
            });
        }
        if (this.isSODClicked) {
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        this.adjutMinMax();
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.adjutMinMax = /**
     * @return {?}
     */
    function () {
        if (this.chart.yAxis[0].min < 0 && this.chart.yAxis[0].max <= 0) {
            this.chart.yAxis[0].update({
                max: this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[0].min >= 0 && this.chart.yAxis[0].max > 0) {
            this.chart.yAxis[0].update({
                min: 0 - this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[1].min < 0 && this.chart.yAxis[1].max <= 0) {
            this.chart.yAxis[1].update({
                max: this.chart.yAxis[1].tickInterval
            });
        }
        if (this.chart.yAxis[1].min >= 0 && this.chart.yAxis[1].max > 0) {
            this.chart.yAxis[1].update({
                min: 0 - this.chart.yAxis[1].tickInterval
            });
        }
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.enableAutoredrawOnly = /**
     * @return {?}
     */
    function () {
        this.chart.redraw = this._redraw;
        this.redrawFunctionIsEmpty = false;
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.disableAutoRedraw = /**
     * @return {?}
     */
    function () {
        this._redraw = this.chart.redraw;
        this.chart.redraw = (/**
         * @return {?}
         */
        function () {
        });
        this.redrawFunctionIsEmpty = true;
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.calculateStatistics = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var maxAxe1 = 0;
        /** @type {?} */
        var minAxe1 = 0;
        /** @type {?} */
        var maxAxe2 = 0;
        /** @type {?} */
        var minAxe2 = 0;
        /** @type {?} */
        var isLine = true;
        /** @type {?} */
        var pointYInChart = 0;
        /** @type {?} */
        var firstIndex = -1;
        /** @type {?} */
        var lastIndex = -1;
        //series.userOptions.type == 'line'
        /** @type {?} */
        var that = this;
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.chart.series[i].visible) {
                this.chart.series[i].data.forEach((/**
                 * @param {?} point
                 * @return {?}
                 */
                function (point) {
                    // Add to sum if within x-axis visible range
                    if (point.x >= that.chart.xAxis[0].min && point.x <= that.chart.xAxis[0].max) {
                        if (firstIndex == -1) {
                            firstIndex = point.index;
                        }
                        lastIndex = point.index;
                    }
                }));
                if (firstIndex != -1 && lastIndex != -1)
                    break;
            }
        }
        for (var i = 0; i < this.chart.series.length; i++) {
            pointYInChart = 0;
            if (this.chart.series[i].userOptions.type == 'line') {
                isLine = true;
            }
            else {
                isLine = false;
            }
            if (this.chart.series[i].visible) {
                for (var h = firstIndex; h < lastIndex; h++) {
                    // chart.series[i].yData[this.point.index]
                    pointYInChart = this.chart.series[i].yData[h];
                    if (isLine) {
                        if (pointYInChart > maxAxe1) {
                            maxAxe1 = pointYInChart;
                        }
                        if (pointYInChart < minAxe1) {
                            minAxe1 = pointYInChart;
                        }
                    }
                    else {
                        if (pointYInChart > maxAxe2) {
                            maxAxe2 = pointYInChart;
                        }
                        if (pointYInChart < minAxe2) {
                            minAxe2 = pointYInChart;
                        }
                    }
                }
            }
        }
        return [Math.max(Math.abs(maxAxe1), Math.abs(minAxe1)), Math.max(Math.abs(maxAxe2), Math.abs(minAxe2))];
    };
    /**
     * @param {?} forceCalculation
     * @return {?}
     */
    SwtILMChart.prototype.alignScale = /**
     * @param {?} forceCalculation
     * @return {?}
     */
    function (forceCalculation) {
        /** @type {?} */
        var calculatedExtremes = this.calculateStatistics();
        if (calculatedExtremes[0] == 0 || calculatedExtremes[1] == 0 && !forceCalculation) {
            /** @type {?} */
            var maxValueForAxis0;
            /** @type {?} */
            var maxValueForAxis1;
            try {
                this.chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                this.chart.yAxis[1].update({
                    min: null,
                    max: null
                });
                if (this.redrawFunctionIsEmpty) {
                    this.enableAutoredrawAndRedrawChart();
                }
                /** @type {?} */
                var max0 = this.chart.yAxis[0].getExtremes().max;
                /** @type {?} */
                var min0 = this.chart.yAxis[0].getExtremes().min;
                /** @type {?} */
                var max1 = this.chart.yAxis[1].getExtremes().max;
                /** @type {?} */
                var min1 = this.chart.yAxis[1].getExtremes().min;
                maxValueForAxis0 = Math.max(Math.abs(max0), Math.abs(min0));
                maxValueForAxis1 = Math.max(Math.abs(max1), Math.abs(min1));
                if (maxValueForAxis0) {
                    this.chart.yAxis[0].update({
                        min: -maxValueForAxis0 - (maxValueForAxis0 / 100),
                        max: maxValueForAxis0 + (maxValueForAxis0 / 100)
                    });
                }
                if (maxValueForAxis1) {
                    this.chart.yAxis[1].update({
                        min: -maxValueForAxis1 - (maxValueForAxis1 / 100),
                        max: maxValueForAxis1 + (maxValueForAxis1 / 100)
                    });
                }
                if (this.redrawFunctionIsEmpty) {
                    this.disableAutoRedraw();
                }
            }
            catch (err) {
                console.log(err.message);
                console.log(err.stack);
            }
        }
        else {
            if (calculatedExtremes[0]) {
                this.chart.yAxis[1].update({
                    min: -calculatedExtremes[0] - (calculatedExtremes[0] / 100),
                    max: calculatedExtremes[0] + (calculatedExtremes[0] / 100)
                });
            }
            if (calculatedExtremes[1]) {
                this.chart.yAxis[0].update({
                    min: -calculatedExtremes[1] - (calculatedExtremes[1] / 100),
                    max: calculatedExtremes[1] + (calculatedExtremes[1] / 100)
                });
            }
        }
    };
    //SOD
    //SOD
    /**
     * @return {?}
     */
    SwtILMChart.prototype.applySODandUnalignScale = 
    //SOD
    /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var k = 0;
        /** @type {?} */
        var l = 1;
        /** @type {?} */
        var maxValueForAxis0;
        /** @type {?} */
        var maxValueForAxis1;
        try {
            for (var i = 0; i < this.inData.length; i++) {
                if (this.inData[i].type !== 'Threshold') {
                    this.chart.series[j].setData(this.inData[i].data.slice());
                    j++;
                }
                else {
                    if (this.allThresholds && this.allThresholds.length > 0) {
                        this.allThresholds[k].value = this.inData[i].data.slice();
                        this.allThresholds[l].value = this.allThresholds[k].value; //duplicate the value for the dashed balck threshold
                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            this.isSODClicked = true;
            if (this.sourceOfLiquidityChecked)
                this.showLiquiditySource(true, null);
            else {
                //update all thresholds first , then create function to clean thresholds
                this.updateVisibleThresholds();
            }
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    };
    /*
    function to update the list of visible thresholds from all passed thresholds
    */
    /*
      function to update the list of visible thresholds from all passed thresholds
      */
    /**
     * @return {?}
     */
    SwtILMChart.prototype.updateVisibleThresholds = /*
      function to update the list of visible thresholds from all passed thresholds
      */
    /**
     * @return {?}
     */
    function () {
        this.visibleLinesInChart = [];
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].visible == true) {
                this.visibleLinesInChart.push(this.allThresholds[i]);
            }
        }
        if (this.sourceOfLiquidityChecked == true && this.dataZones) {
            this.visibleLinesInChart.push({
                value: this.dataZones[4].data[0],
                color: this.dataZones[4].color,
                fillColor: this.dataZones[4].color,
                width: 3,
                id: 'plot-line-1',
                zIndex: 20,
                tooltipText: this.dataZones[4].name,
            });
        }
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.uncheckSODandalignScale = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var k = 0;
        /** @type {?} */
        var l = 1;
        /** @type {?} */
        var maxValueForAxis0;
        /** @type {?} */
        var maxValueForAxis1;
        try {
            for (var i = 0; i < this.inData.length; i++) {
                if (this.inData[i].type !== 'Threshold') {
                    this.chart.series[j].setData(this.inData[i].dataSOD.slice());
                    j++;
                }
                else {
                    if (this.allThresholds && this.allThresholds.length > 0) {
                        this.allThresholds[k].value = this.inData[i].dataSOD.slice();
                        this.allThresholds[l].value = this.allThresholds[k].value; //duplicate the value for the dashed balck threshold
                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            this.isSODClicked = false;
            if (this.sourceOfLiquidityChecked)
                this.showLiquiditySource(true, null);
            else
                this.updateVisibleThresholds();
            this.alignScale(false);
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    };
    //timeStamp to hh:mm
    //timeStamp to hh:mm
    /**
     * @param {?} timestamp
     * @return {?}
     */
    SwtILMChart.prototype.toDate = 
    //timeStamp to hh:mm
    /**
     * @param {?} timestamp
     * @return {?}
     */
    function (timestamp) {
        /** @type {?} */
        var a = new Date((timestamp));
        /** @type {?} */
        var hour = this.addZero(a.getHours());
        /** @type {?} */
        var min = this.addZero(a.getMinutes());
        /** @type {?} */
        var dateFormat = hour + ':' + min;
        return dateFormat;
    };
    ;
    /**
     * @param {?} valuesUpdated
     * @param {?} dataZonesAsJSONString
     * @return {?}
     */
    SwtILMChart.prototype.showLiquiditySource = /**
     * @param {?} valuesUpdated
     * @param {?} dataZonesAsJSONString
     * @return {?}
     */
    function (valuesUpdated, dataZonesAsJSONString) {
        if (this.sourceOfLiquidityChecked && valuesUpdated == false) {
            return;
        }
        //  invisbleChartsBySourceOfLiquidity = [];
        try {
            /** @type {?} */
            var dataZonesJSON = null;
            /** @type {?} */
            var createNewDataZones = false;
            for (var i = 0; i < this.chart.series.length; i++) {
                if (this.chart.series[i].type == 'area' && this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) == -1) {
                    this.chart.series[i].hide();
                    this.unHighlightSerieFunction(this.chart.series[i]);
                    this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                }
            }
            this.chart.yAxis[0].update({
                labels: {
                    enabled: false
                },
                title: {
                    text: null
                }
            });
            if (dataZonesAsJSONString) {
                dataZonesJSON = JSON.parse(dataZonesAsJSONString);
                this.dataZones = dataZonesJSON;
            }
            if (this.dataZones != null) {
                this.chart.yAxis[1].removePlotBand('plot-band-0');
                this.chart.yAxis[1].removePlotBand('plot-band-1');
                this.chart.yAxis[1].removePlotBand('plot-band-2');
                this.chart.yAxis[1].removePlotBand('plot-band-3');
                this.chart.yAxis[1].removePlotLine('plot-line-1');
                for (i = 0; i < this.dataZones.length; i++) {
                    if (!this.isSODClicked) {
                        this.dataZones[i].data = this.dataZones[i].dataSOD;
                    }
                    else {
                        this.dataZones[i].data = this.dataZones[i].dataNoSOD;
                    }
                    if (i < this.dataZones.length - 1) {
                        this.chart.yAxis[1].addPlotBand({
                            "tooltipText": this.dataZones[i].name,
                            "from": this.dataZones[i].data[0],
                            "to": this.dataZones[i].data[1],
                            "color": this.dataZones[i].color,
                            "zIndex": 2,
                            "id": 'plot-band-' + i,
                        });
                    }
                }
            }
            // Reset Extreme points when removing the right axis and SOD is checked
            if (!this.isSODClicked) {
                this.alignScale(false);
            }
            else {
                this.chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                this.chart.yAxis[1].update({
                    min: null,
                    max: null
                });
            }
            this.sourceOfLiquidityChecked = true;
            this.updateVisibleThresholds();
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
            this.chart.yAxis[1].removePlotBand('plot-band-0');
            this.chart.yAxis[1].removePlotBand('plot-band-1');
            this.chart.yAxis[1].removePlotBand('plot-band-2');
            this.chart.yAxis[1].removePlotBand('plot-band-3');
            this.chart.yAxis[1].removePlotLine('plot-line-1');
        }
    };
    /**
     * @param {?} band
     * @param {?} hideOrshow
     * @return {?}
     */
    SwtILMChart.prototype.hideOrShowBandOrLine = /**
     * @param {?} band
     * @param {?} hideOrshow
     * @return {?}
     */
    function (band, hideOrshow) {
        if (hideOrshow) {
            band.hidden = false;
            band.svgElem.show();
        }
        else {
            band.hidden = true;
            band.svgElem.hide();
        }
    };
    /**
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    SwtILMChart.prototype.hideLiquiditySource = /**
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    function (visibleItemsInTree) {
        this.chart.yAxis[1].removePlotBand('plot-band-0');
        this.chart.yAxis[1].removePlotBand('plot-band-1');
        this.chart.yAxis[1].removePlotBand('plot-band-2');
        this.chart.yAxis[1].removePlotBand('plot-band-3');
        this.chart.yAxis[1].removePlotLine('plot-line-1');
        //  if (chart.series[i].userOptions.chartID != 'ab' && chart.series[i].userOptions.chartID != 'aac' && chart.series[i].userOptions.chartID != 'aad') {
        if (this.invisibleLegend.length > 0 && visibleItemsInTree != null && visibleItemsInTree.length > 0) {
            for (var i = 0; i < this.chart.series.length; i++) {
                if (visibleItemsInTree.indexOf(this.chart.series[i].userOptions.valueId) != -1) {
                    this.chart.series[i].show();
                    for (var j = 0; j < this.invisibleLegend.length; j++) {
                        if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                            this.invisibleLegend.splice(j, 1);
                            j--;
                            //break;
                        }
                    }
                }
            }
        }
        this.chart.yAxis[0].update({
            labels: {
                enabled: true
            },
            title: {
                text: 'Accumulated D/C'
            }
        });
        this.sourceOfLiquidityChecked = false;
        if (!this.isSODClicked)
            this.alignScale(false);
    };
    /**
     * @param {?} plotBandId
     * @return {?}
     */
    SwtILMChart.prototype.getPlotBandById = /**
     * @param {?} plotBandId
     * @return {?}
     */
    function (plotBandId) {
        for (var i = 0; i < this.chart.yAxis[1].plotLinesAndBands.length; i++) {
            if (this.chart.yAxis[1].plotLinesAndBands[i].id === plotBandId) {
                return this.chart.yAxis[1].plotLinesAndBands[i];
            }
        }
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.checkMultiplierCurrenyMultiplier = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var that = this;
        try {
            that.useCcyMulitplierChecked = true;
            that.chart.yAxis[0].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        //return this.axis.defaultLabelFormatter.call(this);
                        return that.rightVerticalAxisFormatter(this.value);
                    })
                }
            });
            that.chart.yAxis[1].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that.leftVerticalAxisFormatter(this.value);
                    })
                }
            });
        }
        catch (error) {
        }
        // that.chart.redraw();
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.uncheckMultiplierCurrenyMultiplier = /**
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var that_1 = this;
            that_1.useCcyMulitplierChecked = false;
            that_1.chart.yAxis[0].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that_1.rightVerticalAxisFormatter(this.value);
                    })
                }
            });
            that_1.chart.yAxis[1].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that_1.leftVerticalAxisFormatter(this.value);
                    })
                }
            });
            // that.chart.redraw();
        }
        catch (error) {
        }
    };
    /**
    * Returns the decimal places for the most significant digit for the smallet label in the axis
    */
    /**
     * Returns the decimal places for the most significant digit for the smallet label in the axis
     * @param {?} smallestLabel
     * @return {?}
     */
    SwtILMChart.prototype.smallestLabelSigDigitDcPlaces = /**
     * Returns the decimal places for the most significant digit for the smallet label in the axis
     * @param {?} smallestLabel
     * @return {?}
     */
    function (smallestLabel) {
        /** @type {?} */
        var decSeparator = this.currencyFormat == 'currencyPat1' ? "." : ",";
        if (smallestLabel == 0 && smallestLabel == NaN)
            smallestLabel = 0;
        /** @type {?} */
        var smallestSigDigits = this.getFirstSignificantDigit(smallestLabel, 3, false, (this.currencyFormat == 'currencyPat2'));
        if (smallestSigDigits != 0) {
            /** @type {?} */
            var smDig = "" + smallestSigDigits;
            return smDig.indexOf(decSeparator) != -1 ? (smDig.length - 2) : 0;
        }
        return 0;
    };
    /**
     * @param {?} n
     * @return {?}
     */
    SwtILMChart.prototype.getSignificantDigitCount = /**
     * @param {?} n
     * @return {?}
     */
    function (n) {
        n = String(n).replace(".", "");
        n = Math.abs(n); //remove decimal and make positive
        if (n == 0)
            return 0;
        while (n != 0 && n % 10 == 0)
            n /= 10; //kill the 0s at the end of n
        return Math.floor(Math.log(n) / this.log10) + 1; //get number of digits
    };
    /**
    * Right vertical axis formatter
    */
    /**
     * Right vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    SwtILMChart.prototype.rightVerticalAxisFormatter = /**
     * Right vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    function (labelValue) {
        /** @type {?} */
        var tickInterval = this.chart && this.chart.yAxis && this.chart.yAxis[0] ? this.chart.yAxis[0].tickInterval : 1;
        /** @type {?} */
        var sigDigitDcPlaces = labelValue < 1 ? this.getSignificantDigitCount(tickInterval) : 1;
        // var sigDigitDcPlaces = this.smallestLabelSigDigitDcPlaces((tickInterval) / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1));
        // if(labelValue < 1){
        // }
        return this.commonAxisFormatter(labelValue, sigDigitDcPlaces);
    };
    /**
    * Left vertical axis formatter
    */
    /**
     * Left vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    SwtILMChart.prototype.leftVerticalAxisFormatter = /**
     * Left vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    function (labelValue) {
        // var sigDigitDcPlaces = this.smallestLabelSigDigitDcPlaces((this.chart.yAxis[1].tickInterval) / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1));
        return this.commonAxisFormatter(labelValue, 2);
    };
    /**
    * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
    */
    /**
     * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} labelValue
     * @param {?} sigDigitDcPlaces
     * @return {?}
     */
    SwtILMChart.prototype.commonAxisFormatter = /**
     * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} labelValue
     * @param {?} sigDigitDcPlaces
     * @return {?}
     */
    function (labelValue, sigDigitDcPlaces) {
        /** @type {?} */
        var formattedAmount = "";
        // Apply the currency multiplier
        labelValue = labelValue / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1);
        if (Math.abs(labelValue) >= 1) {
            if (this.useCcyMulitplierChecked && this.currencyMutiplierValue != 1) {
                if (this.currencyFormat == 'currencyPat2')
                    formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.currencyDecimalPlaces : sigDigitDcPlaces, ",", ".");
                else {
                    formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.currencyDecimalPlaces : sigDigitDcPlaces, ".", ",");
                }
            }
            else {
                if (this.currencyFormat == 'currencyPat2')
                    formattedAmount = this.formatMoney(labelValue, 0, ",", ".");
                else {
                    formattedAmount = this.formatMoney(labelValue, 0, ".", ",");
                }
            }
            // format the amount, if sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            this.currencyFormat = this.currencyFormat;
        }
        else {
            // Format the amount based on the most significant number,eg: 0.00014 ==> is formatted into 0.0001.
            // If sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            sigDigitDcPlaces = sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0 ? 3 : sigDigitDcPlaces;
            formattedAmount = "" + this.getFirstSignificantDigit(labelValue, sigDigitDcPlaces, true, (this.currencyFormat == 'currencyPat2'));
        }
        return formattedAmount;
    };
    /**
     * @param {?} n
     * @param {?} c
     * @param {?} d
     * @param {?} t
     * @return {?}
     */
    SwtILMChart.prototype.formatMoney = /**
     * @param {?} n
     * @param {?} c
     * @param {?} d
     * @param {?} t
     * @return {?}
     */
    function (n, c, d, t) {
        /** @type {?} */
        var c = isNaN(c = Math.abs(c)) ? 2 : c;
        /** @type {?} */
        var d = d == undefined ? "." : d;
        /** @type {?} */
        var t = t == undefined ? "," : t;
        /** @type {?} */
        var s = n < 0 ? "-" : "";
        /** @type {?} */
        var i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c)));
        /** @type {?} */
        var j = (j = i.length) > 3 ? j % 3 : 0;
        return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - parseInt(i)).toFixed(c).slice(2) : "");
    };
    ;
    /**
    * This function allow getting the significant digit after the decimal
    * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
    */
    /**
     * This function allow getting the significant digit after the decimal
     * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} number
     * @param {?} maxDecimals
     * @param {?} forceDecimals
     * @param {?} siStyle
     * @return {?}
     */
    SwtILMChart.prototype.getFirstSignificantDigit = /**
     * This function allow getting the significant digit after the decimal
     * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} number
     * @param {?} maxDecimals
     * @param {?} forceDecimals
     * @param {?} siStyle
     * @return {?}
     */
    function (number, maxDecimals, forceDecimals, siStyle) {
        try {
            maxDecimals = typeof maxDecimals !== 'undefined' ? maxDecimals : 2;
            forceDecimals = typeof forceDecimals !== 'undefined' ? forceDecimals : false;
            siStyle = typeof siStyle !== 'undefined' ? siStyle : true;
            /** @type {?} */
            var i = 0;
            /** @type {?} */
            var inc = Math.pow(10, maxDecimals);
            /** @type {?} */
            var str = String(Math.round(inc * number) / inc);
            /** @type {?} */
            var sep;
            if (str != "0") {
                /** @type {?} */
                var hasSep = str.indexOf(".") == -1;
                /** @type {?} */
                var sep = hasSep ? str.length : str.indexOf(".");
                /** @type {?} */
                var ret = (hasSep && !forceDecimals ? "" : (siStyle ? "," : ".")) + str.substr(sep + 1);
                if (forceDecimals) {
                    for (var j = 0; j <= maxDecimals - (str.length - (hasSep ? sep - 1 : sep)); j++)
                        ret += "0";
                }
                while (i + 3 < (str.substr(0, 1) == "-" ? sep - 1 : sep))
                    ret = (siStyle ? "." : ",") + str.substr(sep - (i += 3), 3) + ret;
                return str.substr(0, sep - i) + ret;
            }
            else {
                return 0;
            }
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtILMChart.prototype.firstSignificant = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return Math.ceil(-Math.log10(value));
    };
    /**
     * @param {?} ccyMuliplierSelected
     * @return {?}
     */
    SwtILMChart.prototype.ccyMultiplierEventHandler = /**
     * @param {?} ccyMuliplierSelected
     * @return {?}
     */
    function (ccyMuliplierSelected) {
        this.disableAutoRedraw();
        if (ccyMuliplierSelected == true) {
            this.checkMultiplierCurrenyMultiplier();
        }
        else {
            this.uncheckMultiplierCurrenyMultiplier();
        }
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} serieName
     * @param {?} forcedValueState
     * @return {?}
     */
    SwtILMChart.prototype.highlightSerie = /**
     * @param {?} serieName
     * @param {?} forcedValueState
     * @return {?}
     */
    function (serieName, forcedValueState) {
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.chart.series[i].userOptions.valueId == serieName) {
                if (forcedValueState) {
                    if (forcedValueState == false) {
                        this.unHighlightSerieFunction(this.chart.series[i]);
                    }
                    else if (forcedValueState == true) {
                        this.highlightSerieFunction(this.chart.series[i]);
                    }
                }
                else {
                    if (this.chart.series[i].userOptions.isClicked == false) {
                        this.highlightSerieFunction(this.chart.series[i]);
                    }
                    else if (this.chart.series[i].userOptions.isClicked == true) {
                        this.unHighlightSerieFunction(this.chart.series[i]);
                    }
                }
            }
        }
        this.chart.redraw();
    };
    /**
     * @param {?} serie
     * @return {?}
     */
    SwtILMChart.prototype.highlightSerieFunction = /**
     * @param {?} serie
     * @return {?}
     */
    function (serie) {
        // First check if serie and required properties exist
        if (!serie || !serie.options) {
            console.warn('Invalid series object passed to highlightSerieFunction');
            return;
        }
        try {
            if (serie.options.type === 'line') {
                serie.update({
                    lineWidth: 3
                });
            }
            else if (serie.options.type === 'area') {
                // Check if serie and basic required properties exist
                if (!serie || !serie.options) {
                    console.warn('Invalid series object passed');
                    return;
                }
                try {
                    // Check if area property exists before accessing
                    if (!serie.area) {
                        console.warn('Area property is undefined for series');
                        return;
                    }
                    // Verify color exists before parsing
                    if (!serie.options.color) {
                        console.warn('Color property is undefined for series');
                        return;
                    }
                    if (serie.options.isAreaDashed === 'dashed') {
                        serie.area.attr("fill", serie.options.fillColor || serie.options.color); // Fallback to color if fillColor is undefined
                    }
                    else {
                        /** @type {?} */
                        var rgbaCol = void 0;
                        try {
                            rgbaCol = 'rgba(' +
                                parseInt(serie.options.color.slice(-6, -4), 16) + ',' +
                                parseInt(serie.options.color.slice(-4, -2), 16) + ',' +
                                parseInt(serie.options.color.slice(-2), 16) + ',1)';
                        }
                        catch (colorError) {
                            console.warn('Error parsing color value:', colorError);
                            return;
                        }
                        serie.area.attr("fill", rgbaCol);
                    }
                }
                catch (error) {
                    console.error('Error processing series:', error);
                }
            }
            if (serie.userOptions) {
                serie.userOptions.isClicked = true;
            }
            else {
                console.warn('userOptions is undefined for series');
            }
        }
        catch (error) {
            console.error('Error in highlightSerieFunction:', error);
        }
    };
    /**
     * @param {?} serie
     * @return {?}
     */
    SwtILMChart.prototype.unHighlightSerieFunction = /**
     * @param {?} serie
     * @return {?}
     */
    function (serie) {
        // First check if serie exists
        if (!serie || !serie.options) {
            console.warn('Invalid series object passed to unHighlightSerieFunction');
            return;
        }
        try {
            if (serie.options.type === 'line') {
                serie.update({ lineWidth: 1.5 });
            }
            else if (serie.options.type === 'area') {
                // Check if area property exists before accessing
                if (!serie.area) {
                    console.warn('Area property is undefined for series');
                    return;
                }
                if (serie.options.isAreaDashed === 'dashed') {
                    serie.area.attr("fill", serie.options.fillColor);
                }
                else {
                    // Verify color exists before parsing
                    if (!serie.options.color) {
                        console.warn('Color property is undefined for series');
                        return;
                    }
                    /** @type {?} */
                    var rgbaCol = 'rgba(' +
                        parseInt(serie.options.color.slice(-6, -4), 16) + ',' +
                        parseInt(serie.options.color.slice(-4, -2), 16) + ',' +
                        parseInt(serie.options.color.slice(-2), 16) + ',0.7)';
                    serie.area.attr("fill", rgbaCol);
                }
            }
            if (serie.userOptions) {
                serie.userOptions.isClicked = false;
            }
        }
        catch (error) {
            console.error('Error in unHighlightSerieFunction:', error);
        }
    };
    /**
     * @param {?} serieName
     * @param {?} showHide
     * @return {?}
     */
    SwtILMChart.prototype.showSerie = /**
     * @param {?} serieName
     * @param {?} showHide
     * @return {?}
     */
    function (serieName, showHide) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var lineAxeIsInvisible = false;
        /** @type {?} */
        var areaAxeIsInvisible = false;
        /** @type {?} */
        var redrawAxes = false;
        if (serieName) {
            /** @type {?} */
            var groupFromChartId;
            if (this.chart.yAxis[1].getExtremes().max == undefined) {
                lineAxeIsInvisible = true;
            }
            if (this.chart.yAxis[0].getExtremes().max == undefined) {
                areaAxeIsInvisible = true;
            }
            if (serieName.indexOf('.Thresholds') != -1) {
                groupFromChartId = serieName.split('.')[0];
                this.showHideThreshold(showHide, groupFromChartId);
            }
            else {
                for (var i = 0; i < this.chart.series.length; i++) {
                    if (this.chart.series[i].userOptions.valueId == serieName) {
                        if (!showHide) {
                            this.chart.series[i].hide();
                            //unHighlightSerieFunction(chart.series[i]);
                            this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                        }
                        else {
                            if (this.chart.series[i].type == 'line' && lineAxeIsInvisible) {
                                redrawAxes = true;
                            }
                            else if (this.chart.series[i].type == 'area' && areaAxeIsInvisible) {
                                redrawAxes = true;
                            }
                            this.chart.series[i].show();
                            for (var j = 0; j < this.invisibleLegend.length; j++) {
                                if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                                    this.invisibleLegend.splice(j, 1);
                                    j--;
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!this.isSODClicked)
            this.alignScale(false);
        else {
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} seriesToShow
     * @param {?} showHide
     * @return {?}
     */
    SwtILMChart.prototype.showOrHideMultipleSeries = /**
     * @param {?} seriesToShow
     * @param {?} showHide
     * @return {?}
     */
    function (seriesToShow, showHide) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var listOfSeries;
        /** @type {?} */
        var serieName;
        if (seriesToShow) {
            listOfSeries = seriesToShow.split(',');
            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                /** @type {?} */
                var groupFromChartId;
                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    this.showHideThreshold(showHide, groupFromChartId);
                }
                else {
                    for (var i = 0; i < this.chart.series.length; i++) {
                        if (this.chart.series[i].userOptions.valueId == serieName) {
                            if (!showHide) {
                                this.chart.series[i].hide();
                                this.unHighlightSerieFunction(this.chart.series[i]);
                                this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                            }
                            else if (this.sourceOfLiquidityChecked && this.chart.series[i].type == 'area') {
                                this.chart.series[i].hide();
                                this.unHighlightSerieFunction(this.chart.series[i]);
                                this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                            }
                            else {
                                this.chart.series[i].show();
                                for (var h = 0; h < this.invisibleLegend.length; h++) {
                                    if (this.invisibleLegend[h] == this.chart.series[i].userOptions.valueId) {
                                        this.invisibleLegend.splice(h, 1);
                                        h--;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        /* if (getData()[2] == true) {
             invisibleLegend = [];
         }*/
        if (!this.isSODClicked)
            this.alignScale(false);
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} seriesToRemove
     * @return {?}
     */
    SwtILMChart.prototype.removeMultipleCharts = /**
     * @param {?} seriesToRemove
     * @return {?}
     */
    function (seriesToRemove) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var listOfSeries;
        /** @type {?} */
        var serieName;
        if (seriesToRemove) {
            listOfSeries = seriesToRemove.split(',');
            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                /** @type {?} */
                var groupFromChartId;
                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    this.removeThreshold(groupFromChartId);
                }
                else {
                    for (var i = 0; i < this.chart.series.length; i++) {
                        if (this.chart.series[i].userOptions.valueId == serieName) {
                            this.chart.series[i].remove();
                            for (var h = this.invisibleLegend.length - 1; h >= 0; h--) {
                                if (this.invisibleLegend[h] === serieName) {
                                    this.invisibleLegend.splice(h, 1);
                                }
                            }
                        }
                    }
                }
            }
            for (var serieNumber = 0; serieNumber < this.inData.length; serieNumber++) {
                if (seriesToRemove.indexOf(this.inData[serieNumber].name) != -1) {
                    this.inData.splice(serieNumber, 1);
                    serieNumber--;
                }
                else if (this.inData[serieNumber].type == 'Threshold') {
                    if (seriesToRemove.indexOf(this.inData[serieNumber].name.split('.')[0] + "." + 'Thresholds') != -1) {
                        this.inData.splice(serieNumber, 1);
                        serieNumber--;
                    }
                }
            }
        }
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} isCurrencySelected
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    SwtILMChart.prototype.setEntityOrCurrencyTimeFrame = /**
     * @param {?} isCurrencySelected
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    function (isCurrencySelected, timeStampFrom, timeStampTo) {
        /** @type {?} */
        var that = this;
        this.disableAutoRedraw();
        /** @type {?} */
        var j = 0;
        for (var i = 0; i < this.inData.length; i++) {
            if (this.inData[i].type != 'Threshold') {
                $(this.chart.series[j]).each((/**
                 * @return {?}
                 */
                function () {
                    this.update({
                        pointStart: isCurrencySelected ? that.inData[i].pointStart : that.inData[i].pointStartEntity,
                    }, false);
                }));
                j++;
            }
        }
        this.zoom(timeStampFrom, timeStampTo);
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} UNIX_timestamp
     * @return {?}
     */
    SwtILMChart.prototype.timeConverter = /**
     * @param {?} UNIX_timestamp
     * @return {?}
     */
    function (UNIX_timestamp) {
        /** @type {?} */
        var a = new Date(UNIX_timestamp);
        /** @type {?} */
        var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        /** @type {?} */
        var year = a.getFullYear();
        /** @type {?} */
        var month = months[a.getMonth()];
        /** @type {?} */
        var date = a.getDate();
        /** @type {?} */
        var hour = a.getHours();
        /** @type {?} */
        var min = a.getMinutes();
        /** @type {?} */
        var sec = a.getSeconds();
        /** @type {?} */
        var time = date + ' ' + month + ' ' + year + ' ' + hour + ':' + min + ':' + sec;
        return time;
    };
    /**
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    SwtILMChart.prototype.zoom = /**
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    function (timeStampFrom, timeStampTo) {
        this.chart.xAxis[0].setExtremes(Number(timeStampFrom), Number(timeStampTo));
        this.chart.yAxis[1].update({
            min: null,
            max: null
        });
        this.chart.yAxis[0].update({
            min: null,
            max: null
        });
        if (!this.isSODClicked)
            this.alignScale(false);
        if (this.chart.yAxis[0].min < 0 && this.chart.yAxis[0].max <= 0) {
            this.chart.yAxis[0].update({
                max: this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[0].min >= 0 && this.chart.yAxis[0].max > 0) {
            this.chart.yAxis[0].update({
                min: 0 - this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[1].min < 0 && this.chart.yAxis[1].max <= 0) {
            this.chart.yAxis[1].update({
                max: this.chart.yAxis[1].tickInterval
            });
        }
        if (this.chart.yAxis[1].min >= 0 && this.chart.yAxis[1].max > 0) {
            this.chart.yAxis[1].update({
                min: 0 - this.chart.yAxis[1].tickInterval
            });
        }
    };
    /**
     * @return {?}
     */
    SwtILMChart.prototype.resetZoom = /**
     * @return {?}
     */
    function () {
        this.chart.zoomOut();
        if (!this.isSODClicked)
            this.alignScale(false);
    };
    /**
     * @param {?} chartSnapshot
     * @param {?} legendSnapshot
     * @param {?} dataXML
     * @param {?} exportType
     * @param {?} entityId
     * @param {?} currencyId
     * @param {?} selectedDate
     * @param {?} timeFrame
     * @return {?}
     */
    SwtILMChart.prototype.exportChart = /**
     * @param {?} chartSnapshot
     * @param {?} legendSnapshot
     * @param {?} dataXML
     * @param {?} exportType
     * @param {?} entityId
     * @param {?} currencyId
     * @param {?} selectedDate
     * @param {?} timeFrame
     * @return {?}
     */
    function (chartSnapshot, legendSnapshot, dataXML, exportType, entityId, currencyId, selectedDate, timeFrame) {
        this.lastExportType = exportType;
        this.entityIdLocal = entityId;
        this.currencyIdLocal = currencyId;
        this.selectedDateLocal = selectedDate;
        this.timeFrameLocal = timeFrame;
        /** @type {?} */
        var svgChart = this.chart.getSVG();
        /** @type {?} */
        var dataAsXML;
        /** @type {?} */
        var dataCSV;
        //FIXME:
        // canvg('canvas', svgChart , {renderCallback: function() {
        //     var theImage = canvas.toDataURL('image/png');
        //     var stringImange = theImage.split(",")[1];
        //     if(exportType == "pdf")
        //         dataAsXML = "";
        //     else {
        //         dataCSV = chart.getCSV();
        //         if(dataCSV)
        //             dataAsXML = convertToXML(chart.getCSV());
        //         else
        //             dataAsXML = "<dataprovider userId=\"\" lastUpdate=\"\">\n" +
        //                 "  <result> <timeSlot></timeSlot></result></dataprovider>";
        //     }
        //     parent.onExport(stringImange, legendSnapshot, dataAsXML, exportType, entityId, currencyId,selectedDate, timeFrame);
        // }});
        this.svg_to_png_data(svgChart, legendSnapshot);
    };
    /**
     * @param {?} svg_string
     * @param {?} legendSnapshot
     * @return {?}
     */
    SwtILMChart.prototype.svg_to_png_data = /**
     * @param {?} svg_string
     * @param {?} legendSnapshot
     * @return {?}
     */
    function (svg_string, legendSnapshot) {
        // var ctx, mycanvas,  img, child;
        var _this = this;
        // var ctx, mycanvas,  img, child;
        // // Flatten CSS styles into the SVG
        // img = new Image();
        // img.src = "data:image/svg+xml," + encodeURIComponent(svg_data);
        // // Draw the SVG image to a canvas
        // mycanvas = document.createElement('canvas');
        // mycanvas.width = 400;
        // mycanvas.height = 400;
        // ctx = mycanvas.getContext("2d");
        // ctx.drawImage(img, 0, 0);
        // // Return the canvas's data
        // return mycanvas.toDataURL("image/png");
        /** @type {?} */
        var svg = this.createElementFromHTML(svg_string);
        /** @type {?} */
        var clonedSvgElement = (/** @type {?} */ (svg.cloneNode(true)));
        /** @type {?} */
        var outerHTML = clonedSvgElement.outerHTML;
        /** @type {?} */
        var blob = new Blob([outerHTML], { type: 'image/svg+xml;charset=utf-8' });
        /** @type {?} */
        var blobURL = webkitURL.createObjectURL(blob);
        /** @type {?} */
        var image = new Image();
        image.onload = (/**
         * @return {?}
         */
        function () {
            /** @type {?} */
            var canvas = document.createElement('canvas');
            canvas.width = 950;
            canvas.height = 550;
            /** @type {?} */
            var context = canvas.getContext('2d');
            // draw image in canvas starting left-0 , top - 0  
            context.drawImage(image, 0, 0, 950, 500);
            //  downloadImage(canvas); need to implement
            /** @type {?} */
            var png = canvas.toDataURL();
            _this.runReport(png, legendSnapshot);
        });
        image.src = blobURL;
    };
    /**
     * @param {?} htmlString
     * @return {?}
     */
    SwtILMChart.prototype.createElementFromHTML = /**
     * @param {?} htmlString
     * @return {?}
     */
    function (htmlString) {
        /** @type {?} */
        var div = document.createElement('div');
        div.innerHTML = htmlString.trim();
        // Change this to div.childNodes to support multiple top-level nodes
        return div.firstChild;
    };
    /**
     * @param {?} png
     * @param {?} legend
     * @return {?}
     */
    SwtILMChart.prototype.runReport = /**
     * @param {?} png
     * @param {?} legend
     * @return {?}
     */
    function (png, legend) {
        var _this = this;
        // console.log("SwtILMChart -> runReport -> png", png)
        /** @type {?} */
        var dataAsXML;
        //let dataCSV;
        if (this.lastExportType == "pdf")
            dataAsXML = "";
        else {
            //dataCSV = this.chart.getCSV();
            dataAsXML = '<dataprovider userId="" lastUpdate="">\n';
            // Collect all categories (e.g., time)
            /** @type {?} */
            var categories_1 = new Set();
            this.chart.series.forEach((/**
             * @param {?} series
             * @return {?}
             */
            function (series) {
                series.processedXData.forEach((/**
                 * @param {?} point
                 * @return {?}
                 */
                function (point) {
                    categories_1.add(point); // Assuming `category` holds time information
                }));
            }));
            //  console.log("🚀 ~ categories.forEach ~ categories:", categories)
            /** @type {?} */
            var i_1 = 0;
            // For each category, collect all series data for that category
            categories_1.forEach((/**
             * @param {?} category
             * @return {?}
             */
            function (category) {
                /** @type {?} */
                var date = new Date(category);
                /** @type {?} */
                var formattedTime = _this.formatDate(date);
                dataAsXML += "  <result><timeSlot>" + formattedTime + "</timeSlot>\n";
                //console.log('bb-',category);
                _this.chart.series.forEach((/**
                 * @param {?} series
                 * @return {?}
                 */
                function (series) {
                    // Modify series name as per the given rules
                    /** @type {?} */
                    var heading = series.userOptions.valueId.replace(/ /g, '').replace(/\(|\)/g, "").replace(/\//g, '-');
                    //  console.log('aa-',heading);
                    // Find the point corresponding to the current category
                    /** @type {?} */
                    var point = series.processedYData[i_1];
                    dataAsXML += "    <" + heading + ">" + point + "</" + heading + ">\n";
                }));
                i_1++;
                dataAsXML += '  </result>\n';
            }));
            dataAsXML += '</dataprovider>';
            //  this.chart.export({ type: 'csv' });
            if (!dataAsXML)
                dataAsXML = "<dataprovider userId=\"\" lastUpdate=\"\">\n" +
                    "  <result> <timeSlot></timeSlot></result></dataprovider>";
        }
        // parent.onExport(png, "", dataAsXML, this.lastExportType, entityId, currencyId,selectedDate, timeFrame);
        ExternalInterface.call("onExport", png, legend, dataAsXML, this.lastExportType, this.entityIdLocal, this.currencyIdLocal, this.selectedDateLocal, this.timeFrameLocal);
    };
    /**
     * @param {?} date
     * @return {?}
     */
    SwtILMChart.prototype.formatDate = /**
     * @param {?} date
     * @return {?}
     */
    function (date) {
        /** @type {?} */
        var year = date.getFullYear();
        /** @type {?} */
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        // Zero pad month
        /** @type {?} */
        var day = ('0' + date.getDate()).slice(-2);
        // Zero pad day
        /** @type {?} */
        var hours = ('0' + date.getHours()).slice(-2);
        /** @type {?} */
        var minutes = ('0' + date.getMinutes()).slice(-2);
        /** @type {?} */
        var seconds = ('0' + date.getSeconds()).slice(-2);
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
    };
    /**
     * @param {?} csvData
     * @return {?}
     */
    SwtILMChart.prototype.convertToXML = /**
     * @param {?} csvData
     * @return {?}
     */
    function (csvData) {
        /** @type {?} */
        var dataAsXML = "";
        //console.log('------csvData',this.chart)
        try {
            /** @type {?} */
            var dataArr = csvData.split("\n");
            /** @type {?} */
            var heading = dataArr[0].split(",");
            for (var h = 0; h < heading.length; h++) {
                heading[h] = heading[h].replace(/['"]+/g, '');
                if (heading[h] == "DateTime") {
                    heading[h] = "timeSlot";
                }
                heading[h] = heading[h].replace(/ /g, '').replace(/\(|\)/g, "").replace(/\//g, '-');
            }
            /** @type {?} */
            var valueTag;
            /** @type {?} */
            var data = dataArr.splice(1, dataArr.length - 1);
            dataAsXML += "<dataprovider userId=\"\" lastUpdate=\"\">\n";
            for (var i = 0; i < data.length; i++) {
                /** @type {?} */
                var d = data[i].split(",");
                dataAsXML += "<result>";
                for (var j = 0; j < d.length; j++) {
                    if (heading[j] != "timeSlot") {
                        dataAsXML += "<" + heading[j] + ">" + d[j] + "</" + heading[j] + ">";
                    }
                    else {
                        valueTag = d[j].slice(1, -1);
                        dataAsXML += "<" + heading[j] + ">" + valueTag + "</" + heading[j] + ">";
                    }
                }
                dataAsXML += "</result>";
            }
            dataAsXML += "</dataprovider>";
            //console.log(dataAsXML);
        }
        catch (error) {
            console.log("SwtILMChart -> convertToXML -> error", error);
        }
        return dataAsXML;
    };
    /**
     * @param {?} newStyles
     * @return {?}
     */
    SwtILMChart.prototype.changeChartsStyle = /**
     * @param {?} newStyles
     * @return {?}
     */
    function (newStyles) {
        this.disableAutoRedraw();
        /** @type {?} */
        var stylesAsJson;
        /** @type {?} */
        var yField;
        /** @type {?} */
        var styleName;
        /** @type {?} */
        var colorChart;
        /** @type {?} */
        var borderColor;
        /** @type {?} */
        var chartsType;
        /** @type {?} */
        var chartTypeDetailsID;
        /** @type {?} */
        var dashStyle;
        /** @type {?} */
        var fillColor;
        /** @type {?} */
        var isAreaDashed;
        /** @type {?} */
        var associativeArray = new Array();
        if (newStyles) {
            stylesAsJson = JSON.parse(newStyles);
            for (var j = 0; j < stylesAsJson.length; j++) {
                yField = stylesAsJson[j].name;
                styleName = stylesAsJson[j].chartStyleName;
                colorChart = stylesAsJson[j].color;
                borderColor = stylesAsJson[j].borderColor;
                chartTypeDetailsID = stylesAsJson[j].typeDetails;
                chartsType = stylesAsJson[j].type;
                if (chartsType == 'line' && chartTypeDetailsID == "3") {
                    dashStyle = 'Dash';
                }
                else if (chartsType == 'line' && chartTypeDetailsID == "2") {
                    dashStyle = 'shortdot';
                }
                else {
                    dashStyle = '';
                }
                if (chartsType == 'area' && colorChart.indexOf('png') != -1) {
                    fillColor = this.getFillPatternForImange(colorChart, borderColor);
                    // {
                    //   pattern: {
                    //     path: {
                    //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                    //       strokeWidth: 1
                    //     },
                    //     width: 4,
                    //     height: 4,
                    //     color: borderColor,
                    //   }
                    // }
                    isAreaDashed = 'dashed';
                }
                else {
                    fillColor = {};
                    isAreaDashed = '';
                }
                for (var i = 0; i < this.chart.series.length; i++) {
                    if (this.chart.series[i].userOptions.valueId == yField) {
                        if (this.chart.series[i].type == 'area') {
                            if (isAreaDashed) {
                                this.chart.series[i].update({
                                    fillColor: fillColor,
                                    color: borderColor,
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, true);
                            }
                            else {
                                this.chart.series[i].update({
                                    color: borderColor,
                                    fillColor: 'rgba(' + parseInt(borderColor.slice(-6, -4), 16) + ',' + parseInt(borderColor.slice(-4, -2), 16) + ',' + parseInt(borderColor.slice(-2), 16) + ',0.7)',
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, false);
                            }
                        }
                        else if (this.chart.series[i].type == 'line') {
                            this.chart.series[i].update({
                                color: colorChart,
                                dashStyle: dashStyle,
                            }, false);
                        }
                    }
                }
            }
            //chart.redraw();
        }
        this.enableAutoredrawAndRedrawChart();
    };
    /**
     * @param {?} obj
     * @return {?}
     */
    SwtILMChart.prototype.isEmpty = /**
     * @param {?} obj
     * @return {?}
     */
    function (obj) {
        for (var key in obj) {
            if (obj.hasOwnProperty(key))
                return false;
        }
        return true;
    };
    /**
     * @param {?} i
     * @return {?}
     */
    SwtILMChart.prototype.addZero = /**
     * @param {?} i
     * @return {?}
     */
    function (i) {
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    };
    // public options: any = {
    //   chart: {
    //     type: 'scatter',
    //     height: 700
    //   },
    //   title: {
    //     text: 'Sample Scatter Plot'
    //   },
    //   credits: {
    //     enabled: false
    //   },
    //   tooltip: {
    //     formatter: function () {
    //       return 'x: ' + Highcharts.dateFormat('%e %b %y %H:%M:%S', this.x) +
    //         ' y: ' + this.y.toFixed(2);
    //     }
    //   },
    //   xAxis: {
    //     type: 'datetime',
    //     labels: {
    //       formatter: function () {
    //         return Highcharts.dateFormat('%e %b %y', this.value);
    //       }
    //     }
    //   },
    //   series: [
    //     {
    //       name: 'Normal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-01-25 18:38:31').getTime(), 2]]
    //     },
    //     {
    //       name: 'Abnormal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-02-05 18:38:31').getTime(), 7]]
    //     }
    //   ]
    // }
    // public options: any = {
    //   chart: {
    //     type: 'scatter',
    //     height: 700
    //   },
    //   title: {
    //     text: 'Sample Scatter Plot'
    //   },
    //   credits: {
    //     enabled: false
    //   },
    //   tooltip: {
    //     formatter: function () {
    //       return 'x: ' + Highcharts.dateFormat('%e %b %y %H:%M:%S', this.x) +
    //         ' y: ' + this.y.toFixed(2);
    //     }
    //   },
    //   xAxis: {
    //     type: 'datetime',
    //     labels: {
    //       formatter: function () {
    //         return Highcharts.dateFormat('%e %b %y', this.value);
    //       }
    //     }
    //   },
    //   series: [
    //     {
    //       name: 'Normal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-01-25 18:38:31').getTime(), 2]]
    //     },
    //     {
    //       name: 'Abnormal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-02-05 18:38:31').getTime(), 7]]
    //     }
    //   ]
    // }
    /**
     * @return {?}
     */
    SwtILMChart.prototype.ngOnInit = 
    // public options: any = {
    //   chart: {
    //     type: 'scatter',
    //     height: 700
    //   },
    //   title: {
    //     text: 'Sample Scatter Plot'
    //   },
    //   credits: {
    //     enabled: false
    //   },
    //   tooltip: {
    //     formatter: function () {
    //       return 'x: ' + Highcharts.dateFormat('%e %b %y %H:%M:%S', this.x) +
    //         ' y: ' + this.y.toFixed(2);
    //     }
    //   },
    //   xAxis: {
    //     type: 'datetime',
    //     labels: {
    //       formatter: function () {
    //         return Highcharts.dateFormat('%e %b %y', this.value);
    //       }
    //     }
    //   },
    //   series: [
    //     {
    //       name: 'Normal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-01-25 18:38:31').getTime(), 2]]
    //     },
    //     {
    //       name: 'Abnormal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-02-05 18:38:31').getTime(), 7]]
    //     }
    //   ]
    // }
    /**
     * @return {?}
     */
    function () {
        // this.chart = Highcharts.chart(this.containerHighChart.nativeElement, this.options);
    };
    // ngAfterViewInit() {
    //   const ro = new ResizeObserver((entries, observer) => {
    //     if (this.isVisible(this.elem.nativeElement)) {
    //       this.chart.reflow();
    //       this.chart.update({
    //         plotOptions: {
    //           series: {
    //             states: {
    //               hover: {
    //                 enabled: false
    //               },
    //               inactive: {
    //                 enabled: false
    //               }
    //             }
    //           }
    //         },
    //       })
    //     }
    //   });
    //   ro.observe(this.elem.nativeElement);
    // }
    // ngAfterViewInit() {
    //   const ro = new ResizeObserver((entries, observer) => {
    //     if (this.isVisible(this.elem.nativeElement)) {
    //       this.chart.reflow();
    //       this.chart.update({
    //         plotOptions: {
    //           series: {
    //             states: {
    //               hover: {
    //                 enabled: false
    //               },
    //               inactive: {
    //                 enabled: false
    //               }
    //             }
    //           }
    //         },
    //       })
    //     }
    //   });
    //   ro.observe(this.elem.nativeElement);
    // }
    /**
     * @param {?} height
     * @return {?}
     */
    SwtILMChart.prototype.redrawChart = 
    // ngAfterViewInit() {
    //   const ro = new ResizeObserver((entries, observer) => {
    //     if (this.isVisible(this.elem.nativeElement)) {
    //       this.chart.reflow();
    //       this.chart.update({
    //         plotOptions: {
    //           series: {
    //             states: {
    //               hover: {
    //                 enabled: false
    //               },
    //               inactive: {
    //                 enabled: false
    //               }
    //             }
    //           }
    //         },
    //       })
    //     }
    //   });
    //   ro.observe(this.elem.nativeElement);
    // }
    /**
     * @param {?} height
     * @return {?}
     */
    function (height) {
        $(this.containerHighChart.nativeElement).height(height - 12);
        if (this.chart) {
            this.chart.reflow();
            this.chart.update({
                plotOptions: {
                    series: {
                        states: {
                            hover: {
                                enabled: true
                            },
                        }
                    }
                },
            });
        }
    };
    /**
     * @param {?} e
     * @return {?}
     */
    SwtILMChart.prototype.isVisible = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    };
    SwtILMChart.decorators = [
        { type: Component, args: [{
                    selector: 'swt-ilm-chart',
                    template: "<div class=\"chartContainer\" #containerHighChart ></div>",
                    styles: [""]
                }] }
    ];
    /** @nocollapse */
    SwtILMChart.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtILMChart.propDecorators = {
        containerHighChart: [{ type: ViewChild, args: ["containerHighChart",] }]
    };
    return SwtILMChart;
}(Container));
export { SwtILMChart };
if (false) {
    /** @type {?} */
    SwtILMChart.prototype.containerHighChart;
    /** @type {?} */
    SwtILMChart.prototype.chart;
    /** @type {?} */
    SwtILMChart.prototype.mousePosition;
    /** @type {?} */
    SwtILMChart.prototype.invisibleLegend;
    /** @type {?} */
    SwtILMChart.prototype.tooltipValues;
    /** @type {?} */
    SwtILMChart.prototype.sourceOfLiquidityChecked;
    /** @type {?} */
    SwtILMChart.prototype.useCcyMulitplierChecked;
    /** @type {?} */
    SwtILMChart.prototype.isEntityTimeFrameChecked;
    /** @type {?} */
    SwtILMChart.prototype.showActualDatasetOnlyChecked;
    /** @type {?} */
    SwtILMChart.prototype.entityTimeDifference;
    /** @type {?} */
    SwtILMChart.prototype.firstLoadDataZones;
    /** @type {?} */
    SwtILMChart.prototype.zoomFromTime;
    /** @type {?} */
    SwtILMChart.prototype.zoomToTime;
    /** @type {?} */
    SwtILMChart.prototype.inData;
    /** @type {?} */
    SwtILMChart.prototype._redraw;
    /** @type {?} */
    SwtILMChart.prototype.redrawFunctionIsEmpty;
    /** @type {?} */
    SwtILMChart.prototype.visibleLinesInChart;
    /** @type {?} */
    SwtILMChart.prototype.allThresholds;
    /** @type {?} */
    SwtILMChart.prototype.dataZones;
    /** @type {?} */
    SwtILMChart.prototype.updateTempVariable;
    /** @type {?} */
    SwtILMChart.prototype.currencyFormat;
    /** @type {?} */
    SwtILMChart.prototype.currencyMutiplierValue;
    /** @type {?} */
    SwtILMChart.prototype.currencyDecimalPlaces;
    /** @type {?} */
    SwtILMChart.prototype.dateFormatAsString;
    /** @type {?} */
    SwtILMChart.prototype.saveHighligtedCharts;
    /** @type {?} */
    SwtILMChart.prototype.highlightedSeries;
    /** @type {?} */
    SwtILMChart.prototype.callerTabName;
    /** @type {?} */
    SwtILMChart.prototype.seriesMouseover;
    /** @type {?} */
    SwtILMChart.prototype.symbol;
    /** @type {?} */
    SwtILMChart.prototype.markers;
    /** @type {?} */
    SwtILMChart.prototype.timer;
    /** @type {?} */
    SwtILMChart.prototype.series;
    /** @type {?} */
    SwtILMChart.prototype.notChecked;
    /** @type {?} */
    SwtILMChart.prototype.isSODClicked;
    /** @type {?} */
    SwtILMChart.prototype.isThresholderClicked;
    /** @type {?} */
    SwtILMChart.prototype.hasPlotBand;
    /** @type {?} */
    SwtILMChart.prototype.tooltipText;
    /** @type {?} */
    SwtILMChart.prototype.hasCheckedCurrency;
    /** @type {?} */
    SwtILMChart.prototype.saveUncheckedItems;
    /** @type {?} */
    SwtILMChart.prototype.uncheckedItemsFromParent;
    /** @type {?} */
    SwtILMChart.prototype.options;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawn;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawnNewdata;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawntabName;
    /** @type {?} */
    SwtILMChart.prototype.listOfBandsIds;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.log10;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.entityIdLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.currencyIdLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.selectedDateLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.timeFrameLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.lastExportType;
    /** @type {?} */
    SwtILMChart.prototype.each;
    /** @type {?} */
    SwtILMChart.prototype.pick;
    /** @type {?} */
    SwtILMChart.prototype.seriesTypes;
    /** @type {?} */
    SwtILMChart.prototype.downloadAttrSupported;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.commonService;
    /* Skipping unhandled member: ;*/
    /* Skipping unhandled member: ;*/
}
//# sourceMappingURL=data:application/json;base64,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