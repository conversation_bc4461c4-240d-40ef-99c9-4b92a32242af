/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
var SwtTotalCommonGrid = /** @class */ (function (_super) {
    tslib_1.__extends(SwtTotalCommonGrid, _super);
    function SwtTotalCommonGrid(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        var _this = _super.call(this, el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) || this;
        _this.el = el;
        _this.commonService = commonService;
        _this.autoTooltipExtension = autoTooltipExtension;
        _this.extensionUtility = extensionUtility;
        _this.sharedService = sharedService;
        _this.translate = translate;
        _this.showHeader = false;
        _this.isTotalGrid = true;
        return _this;
    }
    Object.defineProperty(SwtTotalCommonGrid.prototype, "initialColumnsToSkip", {
        get: /**
         * @return {?}
         */
        function () {
            return this._initialColumnsToSkip;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._initialColumnsToSkip = value;
        },
        enumerable: true,
        configurable: true
    });
    SwtTotalCommonGrid.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTotalCommonGrid',
                    template: "\n    <angular-slickgrid\n            #angularSlickGrid\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n",
                    providers: [
                        TranslateService,
                        ExtensionService,
                        AutoTooltipExtension,
                        ExtensionUtility,
                        SharedService,
                        CollectionService
                    ],
                    styles: ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"]
                }] }
    ];
    /** @nocollapse */
    SwtTotalCommonGrid.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: AutoTooltipExtension },
        { type: ExtensionUtility },
        { type: SharedService },
        { type: CollectionService },
        { type: TranslateService }
    ]; };
    return SwtTotalCommonGrid;
}(SwtCommonGrid));
export { SwtTotalCommonGrid };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbW1vbi10b3RhbC1ncmlkLmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9zd3QtY29tbW9uLXRvdGFsLWdyaWQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsT0FBTyxFQUNtRCxTQUFTLEVBRTBELFVBQVUsRUFDdEksTUFBTSxlQUFlLENBQUM7QUFDdkIsT0FBTyxFQUFFLGFBQWEsRUFBRSxNQUFNLDZCQUE2QixDQUFDO0FBQzVELE9BQU8sRUE0QkgsYUFBYSxFQU1iLGdCQUFnQixFQUdoQixnQkFBZ0IsRUFDaEIsb0JBQW9CLEVBV3BCLGlCQUFpQixHQUNwQixNQUFNLG1CQUFtQixDQUFDO0FBQzNCLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLHFCQUFxQixDQUFDO0FBQ3ZELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSx5QkFBeUIsQ0FBQztBQUV4RDtJQTBDd0MsOENBQWE7SUFFakQsNEJBQXVCLEVBQWMsRUFBWSxhQUE0QixFQUFhLG9CQUEyQyxFQUFZLGdCQUFrQyxFQUFZLGFBQTZCLEVBQ3ZOLGlCQUFxQyxFQUFhLFNBQTJCO1FBRGxGLFlBRUksa0JBQU0sRUFBRSxFQUFDLGFBQWEsRUFBQyxvQkFBb0IsRUFBRSxnQkFBZ0IsRUFBQyxhQUFhLEVBQUUsaUJBQWlCLEVBQUUsU0FBUyxDQUFDLFNBRzdHO1FBTHNCLFFBQUUsR0FBRixFQUFFLENBQVk7UUFBWSxtQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUFhLDBCQUFvQixHQUFwQixvQkFBb0IsQ0FBdUI7UUFBWSxzQkFBZ0IsR0FBaEIsZ0JBQWdCLENBQWtCO1FBQVksbUJBQWEsR0FBYixhQUFhLENBQWdCO1FBQ3JLLGVBQVMsR0FBVCxTQUFTLENBQWtCO1FBRS9FLEtBQUksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO1FBQ3hCLEtBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDOztJQUMzQixDQUFDO0lBSUQsc0JBQUksb0RBQW9COzs7O1FBSXhCO1lBQ0ksT0FBTyxJQUFJLENBQUMscUJBQXFCLENBQUU7UUFDdkMsQ0FBQzs7Ozs7UUFORCxVQUEyQixLQUFhO1lBQ3BDLElBQUksQ0FBQyxxQkFBcUIsR0FBRyxLQUFLLENBQUM7UUFDdkMsQ0FBQzs7O09BQUE7O2dCQXZESixTQUFTLFNBQUU7b0JBQ1IsUUFBUSxFQUFFLG9CQUFvQjtvQkFDOUIsUUFBUSxFQUFFLHNjQWNiO29CQWdCRCxTQUFTLEVBQUU7d0JBQ1AsZ0JBQWdCO3dCQUNoQixnQkFBZ0I7d0JBQ2hCLG9CQUFvQjt3QkFDcEIsZ0JBQWdCO3dCQUNoQixhQUFhO3dCQUNiLGlCQUFpQjtxQkFDcEI7NkJBdEJRLDRQQWNSO2lCQVNBOzs7O2dCQWpHNEgsVUFBVTtnQkF1RDlILGFBQWE7Z0JBZGxCLG9CQUFvQjtnQkFKcEIsZ0JBQWdCO2dCQU5oQixhQUFhO2dCQXFCYixpQkFBaUI7Z0JBRVosZ0JBQWdCOztJQWtFekIseUJBQUM7Q0FBQSxBQS9ERCxDQTBDd0MsYUFBYSxHQXFCcEQ7U0FyQlksa0JBQWtCOzs7Ozs7SUFFZCxnQ0FBd0I7Ozs7O0lBQUUsMkNBQXNDOzs7OztJQUFHLGtEQUFxRDs7Ozs7SUFBRSw4Q0FBNEM7Ozs7O0lBQUUsMkNBQXVDOzs7OztJQUMvSyx1Q0FBcUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gICAgQWZ0ZXJDb250ZW50Q2hlY2tlZCwgQWZ0ZXJWaWV3Q2hlY2tlZCwgQWZ0ZXJWaWV3SW5pdCwgQ29tcG9uZW50LCBDb21wb25lbnRGYWN0b3J5LFxyXG4gICAgQ29tcG9uZW50RmFjdG9yeVJlc29sdmVyLCBDb21wb25lbnRSZWYsRXZlbnRFbWl0dGVyLFxyXG4gICAgSW5qZWN0YWJsZSwgSW5wdXQsIE9uQ2hhbmdlcywgT25Jbml0LCBPdXRwdXQsIFJlbmRlcmVyLCBWaWV3Q2hpbGQsIFZpZXdDb250YWluZXJSZWYsT25EZXN0cm95LCBJbmplY3RvciwgQXBwbGljYXRpb25SZWYsIEVsZW1lbnRSZWZcclxufSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuaW1wb3J0IHsgU3d0Q29tbW9uR3JpZCB9IGZyb20gJy4vc3d0LWNvbW1vbi1ncmlkLmNvbXBvbmVudCc7XHJcbmltcG9ydCB7XHJcbiAgICBBZ2dyZWdhdG9ycyxcclxuICAgIEFuZ3VsYXJHcmlkSW5zdGFuY2UsXHJcbiAgICBBbmd1bGFyU2xpY2tncmlkQ29tcG9uZW50LFxyXG4gICAgQmFja2VuZFNlcnZpY2VPcHRpb24sXHJcbiAgICBDZWxsQXJncyxcclxuICAgIENvbHVtbixcclxuICAgIENvbHVtbkZpbHRlcnMsXHJcbiAgICBFZGl0b3JzLFxyXG4gICAgRXhwb3J0U2VydmljZSxcclxuICAgIEZpZWxkVHlwZSxcclxuICAgIEZpbHRlckNoYW5nZWRBcmdzLFxyXG4gICAgRmlsdGVyRmFjdG9yeSxcclxuICAgIEZpbHRlcnMsXHJcbiAgICBGaWx0ZXJTZXJ2aWNlLFxyXG4gICAgRm9ybWF0dGVyLFxyXG4gICAgRm9ybWF0dGVycyxcclxuICAgIEdyaWRNZW51RXh0ZW5zaW9uLFxyXG4gICAgR3JpZE9wdGlvbixcclxuICAgIEdyaWRTZXJ2aWNlLFxyXG4gICAgR3JpZFN0YXRlQ2hhbmdlLFxyXG4gICAgR3JpZFN0YXRlU2VydmljZSxcclxuICAgIEdyb3VwaW5nLFxyXG4gICAgR3JvdXBUb3RhbEZvcm1hdHRlcnMsXHJcbiAgICBPbkV2ZW50QXJncyxcclxuICAgIFBhZ2luYXRpb24sXHJcbiAgICBQYWdpbmF0aW9uQ2hhbmdlZEFyZ3MsXHJcbiAgICBSZXNpemVyU2VydmljZSxcclxuICAgIFNoYXJlZFNlcnZpY2UsXHJcbiAgICBTbGlja2dyaWRDb25maWcsXHJcbiAgICBTb3J0Q2hhbmdlZEFyZ3MsXHJcbiAgICBTb3J0RGlyZWN0aW9uTnVtYmVyLFxyXG4gICAgU29ydGVycyxcclxuICAgIFNvcnRTZXJ2aWNlLFxyXG4gICAgRXh0ZW5zaW9uVXRpbGl0eSxcclxuICAgIEV4dGVuc2lvbk5hbWUsXHJcbiAgICBIZWFkZXJNZW51LFxyXG4gICAgRXh0ZW5zaW9uU2VydmljZSxcclxuICAgIEF1dG9Ub29sdGlwRXh0ZW5zaW9uLFxyXG4gICAgQ2VsbEV4dGVybmFsQ29weU1hbmFnZXJFeHRlbnNpb24sXHJcbiAgICBDb2x1bW5QaWNrZXJFeHRlbnNpb24sXHJcbiAgICBEcmFnZ2FibGVHcm91cGluZ0V4dGVuc2lvbixcclxuICAgIEhlYWRlck1lbnVFeHRlbnNpb24sXHJcbiAgICBIZWFkZXJCdXR0b25FeHRlbnNpb24sXHJcbiAgICBHcm91cEl0ZW1NZXRhUHJvdmlkZXJFeHRlbnNpb24sXHJcbiAgICBDaGVja2JveFNlbGVjdG9yRXh0ZW5zaW9uLFxyXG4gICAgUm93RGV0YWlsVmlld0V4dGVuc2lvbixcclxuICAgIFJvd01vdmVNYW5hZ2VyRXh0ZW5zaW9uLFxyXG4gICAgUm93U2VsZWN0aW9uRXh0ZW5zaW9uLFxyXG4gICAgQ29sbGVjdGlvblNlcnZpY2UsXHJcbn0gZnJvbSAnYW5ndWxhci1zbGlja2dyaWQnO1xyXG5pbXBvcnQgeyBUcmFuc2xhdGVTZXJ2aWNlIH0gZnJvbSAnQG5neC10cmFuc2xhdGUvY29yZSc7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tICcuLi91dGlscy9jb21tb24uc2VydmljZSc7XHJcblxyXG5AQ29tcG9uZW50KCB7XHJcbiAgICBzZWxlY3RvcjogJ1N3dFRvdGFsQ29tbW9uR3JpZCcsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgPGFuZ3VsYXItc2xpY2tncmlkXHJcbiAgICAgICAgICAgICNhbmd1bGFyU2xpY2tHcmlkXHJcbiAgICAgICAgICAgIGdyaWRJZD0nZ3JpZC17e2lkfX0nXHJcbiAgICAgICAgICAgIChvbkRhdGF2aWV3Q3JlYXRlZCk9XCJkYXRhdmlld1JlYWR5KCRldmVudClcIlxyXG4gICAgICAgICAgICAob25Bbmd1bGFyR3JpZENyZWF0ZWQpPVwib25Bbmd1bGFyR3JpZENyZWF0ZWQoJGV2ZW50KVwiXHJcbiAgICAgICAgICAgIFtjb2x1bW5EZWZpbml0aW9uc109XCJjb2x1bW5EZWZpbml0aW9uc1wiXHJcbiAgICAgICAgICAgIFtncmlkT3B0aW9uc109XCJncmlkT3B0aW9uc1wiXHJcbiAgICAgICAgICAgIGdyaWRIZWlnaHQ9XCIxMDAlXCJcclxuICAgICAgICAgICAgZ3JpZFdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgIFtkYXRhc2V0XT1cImRhdGFzZXRcIlxyXG4gICAgPlxyXG4gICAgPC9hbmd1bGFyLXNsaWNrZ3JpZD5cclxuXHJcbmAsXHJcbnN0eWxlczogW2BcclxuICAgIC5ncmlkQ29udGVudHtcclxuICAgICAgICBtaW4td2lkdGg6IDMwMHB4O1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgIH1cclxuICAgIDpob3N0IDo6bmctZGVlcCAuZ3JpZFBhbmUge1xyXG4gICAgICAgIG92ZXJmbG93OiBhdXRvO1xyXG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgfVxyXG4gICAgOmhvc3QgOjpuZy1kZWVwIC5zbGlja2dyaWQtY29udGFpbmVyIHtcclxuICAgICAgICBtaW4taGVpZ2h0OiAxMDAlO1xyXG4gICAgfVxyXG5cclxuXHJcbmBdLFxyXG5wcm92aWRlcnM6IFtcclxuICAgIFRyYW5zbGF0ZVNlcnZpY2UsXHJcbiAgICBFeHRlbnNpb25TZXJ2aWNlLFxyXG4gICAgQXV0b1Rvb2x0aXBFeHRlbnNpb24sXHJcbiAgICBFeHRlbnNpb25VdGlsaXR5LFxyXG4gICAgU2hhcmVkU2VydmljZSxcclxuICAgIENvbGxlY3Rpb25TZXJ2aWNlXHJcbl0sXHJcbn0gKVxyXG5cclxuZXhwb3J0IGNsYXNzIFN3dFRvdGFsQ29tbW9uR3JpZCBleHRlbmRzIFN3dENvbW1vbkdyaWQgaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQgLCBPbkRlc3Ryb3kge1xyXG4gICBcclxuICAgIGNvbnN0cnVjdG9yKCBwcm90ZWN0ZWQgZWw6IEVsZW1lbnRSZWYsIHByb3RlY3RlZCBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlICwgcHJvdGVjdGVkIGF1dG9Ub29sdGlwRXh0ZW5zaW9uIDogQXV0b1Rvb2x0aXBFeHRlbnNpb24sIHByb3RlY3RlZCBleHRlbnNpb25VdGlsaXR5IDpFeHRlbnNpb25VdGlsaXR5LCBwcm90ZWN0ZWQgc2hhcmVkU2VydmljZSA6IFNoYXJlZFNlcnZpY2VcclxuICAgICAgICAsY29sbGVjdGlvblNlcnZpY2UgOiBDb2xsZWN0aW9uU2VydmljZSAsIHByb3RlY3RlZCB0cmFuc2xhdGU6IFRyYW5zbGF0ZVNlcnZpY2UpIHtcclxuICAgICAgICBzdXBlcihlbCxjb21tb25TZXJ2aWNlLGF1dG9Ub29sdGlwRXh0ZW5zaW9uLCBleHRlbnNpb25VdGlsaXR5LHNoYXJlZFNlcnZpY2UsIGNvbGxlY3Rpb25TZXJ2aWNlLCB0cmFuc2xhdGUpO1xyXG4gICAgICAgdGhpcy5zaG93SGVhZGVyID0gZmFsc2U7XHJcbiAgICAgICB0aGlzLmlzVG90YWxHcmlkID0gdHJ1ZTtcclxuICAgIH1cclxuIFxyXG5cclxuICAgIFxyXG4gICAgc2V0IGluaXRpYWxDb2x1bW5zVG9Ta2lwICggdmFsdWU6IG51bWJlciApIHtcclxuICAgICAgICB0aGlzLl9pbml0aWFsQ29sdW1uc1RvU2tpcCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIGdldCBpbml0aWFsQ29sdW1uc1RvU2tpcCAoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2luaXRpYWxDb2x1bW5zVG9Ta2lwIDtcclxuICAgIH1cclxuXHJcblxyXG5cclxufVxyXG4iXX0=