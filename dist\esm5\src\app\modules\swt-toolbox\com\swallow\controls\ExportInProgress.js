/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "../controls/swt-module.component";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { CancelExportEvent } from "../events/cancel-export-event.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { SwtButton } from "./swt-button.component";
import { ExternalInterface } from "../utils/external-interface.service";
import { Logger } from "../logging/logger.service";
var ExportInProgress = /** @class */ (function (_super) {
    tslib_1.__extends(ExportInProgress, _super);
    function ExportInProgress(element, common) {
        var _this = _super.call(this, element, common) || this;
        _this.element = element;
        _this.common = common;
        _this.labelValue = null;
        _this.exportToken = null;
        //Timer to check if export is done correctly
        _this.exportTimer = null;
        //Httpservice variable
        _this.inputData = new HTTPComms(CommonService.instance);
        //servlet(action) path
        _this.actionPath = null;
        //to send request parameters to server
        _this.requestParams = new Array();
        _this._exportCancelFunction = _this.defaultContentFunction.bind(_this);
        _this.SwtAlert = new SwtAlert(_this.common);
        _this.logger = new Logger("ExportInProgress", _this.common.httpclient);
        return _this;
    }
    /**
     * @return {?}
     */
    ExportInProgress.prototype.initData = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    ExportInProgress.prototype.defaultContentFunction = /**
     * @return {?}
     */
    function () {
        console.log("***_exportCancelFunction");
    };
    Object.defineProperty(ExportInProgress.prototype, "exportCancelFunction", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportCancelFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            console.log("TCL: ExportInProgress -> set t   setexportCancelFunction -> exportCancelFunction");
            this._exportCancelFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to show report Progress.
     */
    /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    ExportInProgress.prototype.show = /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    function (parent) {
        //   SwtPopUpManager.addPopUp(parent, ExportInProgress,{exportToken: this.exportToken}, false);
        this.win = SwtPopUpManager.createPopUp(this, ExportInProgress, {});
        this.win.enableResize = false;
        //   this.win.title = "Export";
        this.win.width = '280';
        this.win.height = '110';
        this.win.showControls = false;
        this.win.showHeader = false;
        this.win.isModal = true;
        console.log("TCL: ExportInProgress -> show -> this._exportCancelFunction", this._exportCancelFunction);
        this.win.exportCancelFunction = this._exportCancelFunction.bind(this);
        this.win.onClose.subscribe((/**
         * @param {?} res
         * @return {?}
         */
        function (res) {
        }));
        this.win.display();
    };
    /**
     *  This method is used to hide report Progress.
     */
    /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    ExportInProgress.prototype.hide = /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    function (parent) {
        this.win.close();
    };
    /**
     * @return {?}
     */
    ExportInProgress.prototype.cancelReport = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var cancelExportEvent = new CancelExportEvent(CancelExportEvent.CANCEL_BUTTON_CLICK);
        /** @type {?} */
        var obj = new Object();
        obj["exportToken"] = this.exportToken;
        cancelExportEvent.exportDTO = obj;
        this.exportCancelFunction(null);
        //   this.cancelExport(cancelExportEvent);
        this.close();
    };
    /**
     *This function is called whenever click on the cancel button in Progress bar window
     *@param event: MouseEvent
     */
    /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    ExportInProgress.prototype.cancelExport = /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    function (event) {
        var _this = this;
        // Calls the inputDataResult function to load the datagrid
        this.inputData.cbResult = (/**
         * @return {?}
         */
        function () {
            _this.ExportCanceled;
        });
        // Sets the action path for Interface Monitor
        this.actionPath = "report!cancelReport.do";
        // Sets the full URL for Interface Monitor
        this.inputData.url = SwtUtil.getBaseURL() + this.actionPath;
        // Sets the flag for encoding URL to false
        this.inputData.encodeURL = false;
        // Sets the datagrid format
        //      this.inputData.resultFormat="e4x";
        this.requestParams["cancelExport"] = "true";
        this.requestParams["exportCookie1234"] = event.exportDTO["exportToken"];
        // Send the request to the server
        this.inputData.send(this.requestParams);
    };
    /**
     * @return {?}
     */
    ExportInProgress.prototype.initTimer = /**
     * @return {?}
     */
    function () {
        var _this = this;
        // Start export timer
        //Instanciate a timer to check coookies in a regular interval
        this.logger.info("initTimer method start");
        /** @type {?} */
        var startExport = this.getTimer();
        /** @type {?} */
        var interval = setInterval((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            // Check for the export cookie
            /** @type {?} */
            var exportResult = ExternalInterface.call('checkExportStatus', [_this.exportToken]);
            //                      
            if (exportResult != null) {
                // Stop the timer
                clearInterval(interval);
                //              parentApplication.disposeExportProgressPopup(); 
                ExternalInterface.call('disposeExport');
                //Display an alert if result contains an error
                if (exportResult.substring(0, 2) == 'KO') {
                    if (exportResult.substring(3) == 'report.design.unsupported')
                        _this.SwtAlert.info(SwtUtil.getCommonMessages('alert.design_unsupported'));
                    else
                        _this.SwtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'), SwtUtil.getCommonMessages('alert_header.error'));
                }
                else if (exportResult.substring(0, 2) == 'CA') {
                    _this.SwtAlert.info(SwtUtil.getCommonMessages('alert.export_canceled'));
                }
                ExternalInterface.call('finishExport');
            }
            else if ( /*!this.cancelButton.enabled && */(_this.getTimer() - startExport) > 8000) {
                ExternalInterface.call('disposeExport');
                clearInterval(interval);
                _this.SwtAlert.info(SwtUtil.getCommonMessages('alert.export_canceled'));
                _this.logger.warn("Report canceled!!");
            }
        }), 300);
        this.logger.info("initTimer method end");
    };
    /**
     * Export is canceled
     */
    /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    ExportInProgress.prototype.ExportCanceled = /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    function (event) {
        ExternalInterface.call('finishExport');
    };
    /**
     * @private
     * @return {?}
     */
    ExportInProgress.prototype.getTimer = /**
     * @private
     * @return {?}
     */
    function () {
        return new Date().getTime();
    };
    ExportInProgress.decorators = [
        { type: Component, args: [{
                    selector: 'SwtReportProgress',
                    template: "\n    <SwtModule (creationComplete)=\"initData()\"  (close)=\"close()\" width=\"100%\" height=\"100%\" controlBarEnabled=\"false\">\n          <VBox  paddingLeft =\"10\" paddingTop =\"10\" horizontalAlign=\"center\" verticalAlign=\"middle\" class=\"popup\" width=\"264\" height=\"100%\">\n                  <div  style=\"width:100%\" class=\"progress\">\n                      <div class=\"progress-bar progress-bar-striped active\" role=\"progressbar\"\n                      aria-valuenow=\"40\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:100%\"></div>\n                  </div>\n              <HBox paddingTop =\"10\" horizontalAlign=\"center\">\n                  <SwtButton #cancelButton label=\"Cancel\" (click)=\"cancelReport()\" style=\"margin: auto\"></SwtButton>\n              </HBox>\n          </VBox>\n    </SwtModule>\n  ",
                    styles: ["\n       .progress {\n           height: 8px;\n           border-radius: 0px;\n           margin-top: 6px;\n       }\n  \n  \n  "]
                }] }
    ];
    /** @nocollapse */
    ExportInProgress.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    ExportInProgress.propDecorators = {
        cancelButton: [{ type: ViewChild, args: ["cancelButton",] }]
    };
    return ExportInProgress;
}(SwtModule));
export { ExportInProgress };
if (false) {
    /** @type {?} */
    ExportInProgress.prototype.labelValue;
    /** @type {?} */
    ExportInProgress.prototype.exportToken;
    /** @type {?} */
    ExportInProgress.prototype.exportTimer;
    /** @type {?} */
    ExportInProgress.prototype.inputData;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.logger;
    /** @type {?} */
    ExportInProgress.prototype.win;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype._exportCancelFunction;
    /** @type {?} */
    ExportInProgress.prototype.cancelButton;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.element;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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