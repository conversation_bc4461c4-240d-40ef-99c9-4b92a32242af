/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
var Keyboard = /** @class */ (function () {
    function Keyboard() {
    }
    Keyboard.BACKSPACE = 8;
    Keyboard.CAPS_LOCK = 20;
    Keyboard.CONTROL = 17;
    Keyboard.DELETE = 46;
    Keyboard.DOWN = 40;
    Keyboard.END = 35;
    Keyboard.ENTER = 13;
    Keyboard.ESCAPE = 32;
    Keyboard.F1 = 112;
    Keyboard.F2 = 113;
    Keyboard.F3 = 114;
    Keyboard.F4 = 115;
    Keyboard.F5 = 116;
    Keyboard.F6 = 117;
    Keyboard.F7 = 118;
    Keyboard.F8 = 119;
    Keyboard.F9 = 120;
    Keyboard.F10 = 121;
    Keyboard.F11 = 122;
    Keyboard.F12 = 123;
    Keyboard.HOME = 36;
    Keyboard.INSERT = 45;
    Keyboard.LEFT = 37;
    Keyboard.NUMPAD_0 = 96;
    Keyboard.NUMPAD_1 = 97;
    Keyboard.NUMPAD_2 = 98;
    Keyboard.NUMPAD_3 = 99;
    Keyboard.NUMPAD_4 = 100;
    Keyboard.NUMPAD_5 = 101;
    Keyboard.NUMPAD_6 = 102;
    Keyboard.NUMPAD_7 = 103;
    Keyboard.NUMPAD_8 = 104;
    Keyboard.NUMPAD_9 = 105;
    Keyboard.NUMPAD_ADD = 107;
    Keyboard.NUMPAD_DECIMAL = 110;
    Keyboard.NUMPAD_MULTIPLY = 106;
    Keyboard.NUMPAD_DEVIDE = 111;
    Keyboard.NUMPAD_ENTER = 13;
    Keyboard.NUMPAD_SUBTRACT = 109;
    Keyboard.PAGE_DOWN = 34;
    Keyboard.PAGE_UP = 33;
    Keyboard.RIGHT = 39;
    Keyboard.SHIFT = 8;
    Keyboard.SPACE = 32;
    Keyboard.TAB = 9;
    Keyboard.UP = 38;
    Keyboard.capsLock = 20;
    Keyboard.numLock = 114;
    Keyboard.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    Keyboard.ctorParameters = function () { return []; };
    return Keyboard;
}());
export { Keyboard };
if (false) {
    /** @type {?} */
    Keyboard.BACKSPACE;
    /** @type {?} */
    Keyboard.CAPS_LOCK;
    /** @type {?} */
    Keyboard.CONTROL;
    /** @type {?} */
    Keyboard.DELETE;
    /** @type {?} */
    Keyboard.DOWN;
    /** @type {?} */
    Keyboard.END;
    /** @type {?} */
    Keyboard.ENTER;
    /** @type {?} */
    Keyboard.ESCAPE;
    /** @type {?} */
    Keyboard.F1;
    /** @type {?} */
    Keyboard.F2;
    /** @type {?} */
    Keyboard.F3;
    /** @type {?} */
    Keyboard.F4;
    /** @type {?} */
    Keyboard.F5;
    /** @type {?} */
    Keyboard.F6;
    /** @type {?} */
    Keyboard.F7;
    /** @type {?} */
    Keyboard.F8;
    /** @type {?} */
    Keyboard.F9;
    /** @type {?} */
    Keyboard.F10;
    /** @type {?} */
    Keyboard.F11;
    /** @type {?} */
    Keyboard.F12;
    /** @type {?} */
    Keyboard.F13;
    /** @type {?} */
    Keyboard.F14;
    /** @type {?} */
    Keyboard.F15;
    /** @type {?} */
    Keyboard.HOME;
    /** @type {?} */
    Keyboard.INSERT;
    /** @type {?} */
    Keyboard.LEFT;
    /** @type {?} */
    Keyboard.NUMPAD_0;
    /** @type {?} */
    Keyboard.NUMPAD_1;
    /** @type {?} */
    Keyboard.NUMPAD_2;
    /** @type {?} */
    Keyboard.NUMPAD_3;
    /** @type {?} */
    Keyboard.NUMPAD_4;
    /** @type {?} */
    Keyboard.NUMPAD_5;
    /** @type {?} */
    Keyboard.NUMPAD_6;
    /** @type {?} */
    Keyboard.NUMPAD_7;
    /** @type {?} */
    Keyboard.NUMPAD_8;
    /** @type {?} */
    Keyboard.NUMPAD_9;
    /** @type {?} */
    Keyboard.NUMPAD_ADD;
    /** @type {?} */
    Keyboard.NUMPAD_DECIMAL;
    /** @type {?} */
    Keyboard.NUMPAD_MULTIPLY;
    /** @type {?} */
    Keyboard.NUMPAD_DEVIDE;
    /** @type {?} */
    Keyboard.NUMPAD_ENTER;
    /** @type {?} */
    Keyboard.NUMPAD_SUBTRACT;
    /** @type {?} */
    Keyboard.PAGE_DOWN;
    /** @type {?} */
    Keyboard.PAGE_UP;
    /** @type {?} */
    Keyboard.RIGHT;
    /** @type {?} */
    Keyboard.SHIFT;
    /** @type {?} */
    Keyboard.SPACE;
    /** @type {?} */
    Keyboard.TAB;
    /** @type {?} */
    Keyboard.UP;
    /** @type {?} */
    Keyboard.capsLock;
    /** @type {?} */
    Keyboard.numLock;
}
//# sourceMappingURL=data:application/json;base64,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