/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { RootObject } from '../utils/swt-interfaces';
import { CommonService } from "../utils/common.service";
export class JSONReader {
    /**
     * @return {?}
     */
    JSONReader() {
    }
    /**
     * @param {?} inputJSON
     * @return {?}
     */
    setInputJSON(inputJSON) {
        /**
         * This bloc of code is added to add content
         * to grid data if is undefined
         * */
        JSONReader.jsonpath(inputJSON, '$..grid[*]..row[*]').forEach((/**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var column;
            for (var key in value) {
                if (value.hasOwnProperty(key)) {
                    if (typeof (value[key]) == 'object' && value[key].content == undefined) {
                        value[key].content = "";
                    }
                    else {
                        if (value["content"] == undefined && value === Object(value)) {
                            value["content"] = "";
                        }
                    }
                }
            }
        }));
        /* JSONReader.jsonpath(inputJSON,'$.*.grid.rows.row').forEach(function (row) {
         
             JSONReader.jsonpath(row,'$.*.*').forEach(function (value) {
                 if(value.content == undefined && typeof value != 'number'){
                     value.content = "";
                 }
             });
         }); */
        this.inputJSON = this.getRoot(inputJSON);
    }
    /**
     *
     * @return {?}
     */
    getInputJSON() {
        return this.inputJSON;
    }
    /**
     * @param {?} inputJSON1
     * @param {?} inputJSON2
     * @return {?}
     */
    static compareJSON(inputJSON1, inputJSON2) {
        if (!(JSON.stringify(inputJSON1) === JSON.stringify(inputJSON2))) {
            return false;
        }
        return true;
    }
    /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    static jsonpath(obj, path) {
        return CommonService.jsonpath(obj, path);
    }
    /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    static jsonpathes(obj, paths) {
        return CommonService.jsonpathes(obj, paths);
    }
    /**
     * @return {?}
     */
    getScreenAttributes() {
        /** @type {?} */
        let tmpArray = [];
        try {
            tmpArray = new Array();
            for (let i = 0; i < this.inputJSON.value.attributes.length(); i++) {
                tmpArray[this.inputJSON.value.attributes[i].name] = this.inputJSON.value.attributes[i];
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getScreenAttributes", errorLocation);
        }
        return tmpArray.length === 0 ? this.inputJSON.value : tmpArray;
    }
    /**
     * @return {?}
     */
    getRequestReplyStatus() {
        /** @type {?} */
        let request_reply_status;
        /** @type {?} */
        let reqReplay;
        try {
            if (this.inputJSON.name === 'request_reply') {
                reqReplay = (/** @type {?} */ (this.inputJSON.value));
                if (typeof (reqReplay.status_ok) == "string") {
                    request_reply_status = (reqReplay.status_ok.toLowerCase() === "true") ? true : false;
                }
                else if (typeof (reqReplay.status_ok) == "boolean") {
                    request_reply_status = (reqReplay.status_ok == true) ? true : false;
                }
            }
            else {
                reqReplay = (/** @type {?} */ (this.inputJSON.value.request_reply));
                if (typeof (reqReplay.status_ok) == "string") {
                    request_reply_status = (reqReplay.status_ok.toLowerCase() === 'true') ? true : false;
                }
                else if (typeof (reqReplay.status_ok) == "boolean") {
                    request_reply_status = (((/** @type {?} */ (reqReplay.status_ok))).toString().toLowerCase() == 'true') ? true : false;
                }
                else {
                    request_reply_status = (((/** @type {?} */ (reqReplay.status_ok))).content.toLowerCase() === "true") ? true : false;
                }
            }
            /*if (this.inputJSON.name === 'request_reply') {
                reqReplay = <RequestReply> this.inputJSON.value;
                request_reply_status = (reqReplay.status_ok.toString() == 'true') ? true : false;
            } else {
                reqReplay = <RequestReply> this.inputJSON.value.request_reply;
                request_reply_status = (reqReplay.status_ok.toString() == 'true') ? true : false;
            }*/
        }
        catch (error) {
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 
            //                        getQualifiedClassName(this) + ".mxml", "getRequestReplyStatus", errorLocation);               
        }
        return request_reply_status;
    }
    /**
     * @return {?}
     */
    getRequestReplyMessage() {
        /** @type {?} */
        let request_reply_message = null;
        /** @type {?} */
        let reqReplay;
        try {
            if (this.inputJSON.name === 'request_reply') {
                reqReplay = (/** @type {?} */ (this.inputJSON.value));
                request_reply_message = reqReplay.message.toString();
            }
            else {
                reqReplay = (/** @type {?} */ (this.inputJSON.value.request_reply));
                request_reply_message = reqReplay.message.toString();
            }
        }
        catch (error) {
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 
            //                    getQualifiedClassName(this) + ".mxml", "getRequestReplyMessage", errorLocation);
        }
        return request_reply_message;
    }
    /**
     * @return {?}
     */
    getRequestReplyLocation() {
        /** @type {?} */
        let request_reply_location = null;
        try {
            if (this.inputJSON.name === 'request_reply') {
                request_reply_location = this.inputJSON.value.location;
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getRequestReplyLocation", errorLocation);
        }
        return request_reply_location;
    }
    /**
     * @return {?}
     */
    isDataBuilding() {
        /** @type {?} */
        var dataBuilding;
        try {
            dataBuilding = (this.inputJSON.value.databuilding == "true") ? true : false;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "isDataBuilding", errorLocation);
        }
        return dataBuilding;
    }
    /**
     * @return {?}
     */
    getDateFormat() {
        /** @type {?} */
        let dateformat;
        try {
            dateformat = this.inputJSON.value.dateformat;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getDateFormat", errorLocation);
        }
        return dateformat;
    }
    /**
     * @return {?}
     */
    getRefreshRate() {
        /** @type {?} */
        let refreshRate;
        try {
            refreshRate = this.inputJSON.value.refresh;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getRefreshRate", errorLocation);
        }
        return refreshRate;
    }
    /**
     * @return {?}
     */
    getTiming() {
        /** @type {?} */
        let requestReplyTiming;
        try {
            if (this.inputJSON.name === 'request_reply') {
                requestReplyTiming = this.inputJSON.request_reply.timing;
            }
            else {
                requestReplyTiming = this.inputJSON.timing;
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getTiming", errorLocation);
        }
        return requestReplyTiming;
    }
    /**
     * @return {?}
     */
    getSingletons() {
        /** @type {?} */
        let singletons;
        try {
            singletons = this.inputJSON.value.singletons;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getSingletons", errorLocation);
        }
        return singletons;
    }
    /**
     * @return {?}
     */
    getColumnData() {
        /** @type {?} */
        var columnData;
        try {
            columnData = this.inputJSON.value.grid.metadata.columns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getSingletons", errorLocation);
        }
        return columnData;
    }
    /**
     * @return {?}
     */
    getGridData() {
        /** @type {?} */
        let columnData;
        try {
            columnData = this.inputJSON.value.grid.rows;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return columnData;
    }
    /**
     * @return {?}
     */
    getBottomGridData() {
        /** @type {?} */
        let bottomGridData;
        try {
            bottomGridData = this.inputJSON.value.bottomgrid.rows;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return bottomGridData;
    }
    /**
     * @return {?}
     */
    getGridMetaData() {
        /** @type {?} */
        let columns;
        try {
            columns = this.inputJSON.value.grid.metadata.columns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return columns;
    }
    /**
     * @return {?}
     */
    getRowSize() {
        /** @type {?} */
        let rowSize;
        try {
            rowSize = this.inputJSON.value.grid.rows.size;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getRowSize", errorLocation);
        }
        return rowSize;
    }
    /**
     * @return {?}
     */
    getTotalsData() {
        this.inputJSON.value.grid.totals.size = 1;
        /** @type {?} */
        var totalData;
        try {
            if (!this.inputJSON.value.grid.totals.row && this.inputJSON.value.grid.totals.total) {
                this.inputJSON.value.grid.totals.row = this.inputJSON.value.grid.totals.total;
                delete this.inputJSON.value.grid.totals.total;
            }
            totalData = this.inputJSON.value.grid.totals;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getTotalsData", errorLocation);               
        }
        return totalData;
    }
    /**
     * @return {?}
     */
    getSelects() {
        /** @type {?} */
        let totalData;
        try {
            totalData = this.inputJSON.value.selects;
            if (!totalData['select'].length) {
                totalData['select'] = [totalData['select']];
            }
        }
        catch (error) {
            console.error(error);
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) +
            //                    ".mxml", "getTotalsData", errorLocation);               
        }
        return totalData;
    }
    /**
     * @return {?}
     */
    getMaxPage() {
        /** @type {?} */
        let maxPage;
        try {
            maxPage = this.inputJSON.value.grid.paging.maxpage;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //         getQualifiedClassName(this) + ".mxml", "getMaxPage", errorLocation);              
        }
        return maxPage;
    }
    /**
     * @return {?}
     */
    getCurrentPage() {
        /** @type {?} */
        var currentPage;
        try {
            if (this.inputJSON.value.grid.paging == undefined) {
                return "0";
            }
            currentPage = this.inputJSON.value.grid.paging.currentpage;
            return currentPage;
        }
        catch (error) {
            console.error("error", error);
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getCurrentPage", errorLocation);              
        }
    }
    /**
     * @return {?}
     */
    getTreeData() {
        /** @type {?} */
        let treeData = null;
        try {
            treeData = JSONReader.jsonpath(this.inputJSON.value, "$.tree");
        }
        catch (error) {
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this), "getAllColumnData", errorLocation);
        }
        return treeData;
    }
    /**
     * @return {?}
     */
    getAllColumnData() {
        /** @type {?} */
        let allColumnData;
        try {
            allColumnData = this.inputJSON.value.allcolumns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getAllColumnData", errorLocation);
        }
        return allColumnData;
    }
    /* END -Added  to get allcolumn data from input XML by M.Bouraoui on 01-August-13*/
    /**
     * This method is used to get the root of a json response.
     * @param {?} result
     * @return {?}
     */
    getRoot(result) {
        /** @type {?} */
        let rootnode = new RootObject();
        /** @type {?} */
        let key;
        for (key in result) {
            if (result.hasOwnProperty(key)) {
                rootnode.name = key;
                rootnode.value = result[key];
                break;
            }
        }
        return rootnode;
    }
    /**
     * @return {?}
     */
    getprossesInfoStatus() {
        /** @type {?} */
        let process_info_status = null;
        /** @type {?} */
        let processInfo;
        try {
            if (this.inputJSON.name === 'process_info') {
                processInfo = (/** @type {?} */ (this.inputJSON.value));
                process_info_status = processInfo.process_status.toString();
            }
            else {
                processInfo = (/** @type {?} */ (this.inputJSON.value.process_info));
                process_info_status = processInfo.process_status.toString();
            }
        }
        catch (error) {
            console.log(error, "getprossesInfoStatus", "Jsonreader");
        }
        return process_info_status;
    }
    /**
     * @return {?}
     */
    getprossesInfoRunning() {
        /** @type {?} */
        let process_info_running = null;
        /** @type {?} */
        let processInfo;
        try {
            if (this.inputJSON.name === 'process_info') {
                processInfo = (/** @type {?} */ (this.inputJSON.value));
                process_info_running = processInfo.running_seqnbr.toString();
            }
            else {
                processInfo = (/** @type {?} */ (this.inputJSON.value.process_info));
                process_info_running = processInfo.running_seqnbr.toString();
            }
        }
        catch (error) {
            console.log(error, "getprossesInfoRunning", "Jsonreader");
        }
        return process_info_running;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    JSONReader.prototype.inputJSON;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoianNvbnJlYWRlci5zZXJ2aWNlLmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2pzb25oYW5kbGVyL2pzb25yZWFkZXIuc2VydmljZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFDLFVBQVUsRUFBbUQsTUFBTSx5QkFBeUIsQ0FBQztBQUVyRyxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFHeEQsTUFBTSxPQUFPLFVBQVU7Ozs7SUFJWixVQUFVO0lBQ2pCLENBQUM7Ozs7O0lBRU0sWUFBWSxDQUFDLFNBQWM7UUFDOUI7OzthQUdLO1FBQ0wsVUFBVSxDQUFDLFFBQVEsQ0FBQyxTQUFTLEVBQUUsb0JBQW9CLENBQUMsQ0FBQyxPQUFPOzs7O1FBQUMsVUFBVSxLQUFhOztnQkFDNUUsTUFBVztZQUNmLEtBQUssSUFBSSxHQUFHLElBQUksS0FBSyxFQUFFO2dCQUNuQixJQUFJLEtBQUssQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLEVBQUU7b0JBQzNCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLFFBQVEsSUFBSSxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsT0FBTyxJQUFJLFNBQVMsRUFBRTt3QkFDcEUsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sR0FBRyxFQUFFLENBQUM7cUJBQzNCO3lCQUFNO3dCQUNILElBQUksS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLFNBQVMsSUFBSSxLQUFLLEtBQUssTUFBTSxDQUFDLEtBQUssQ0FBQyxFQUFFOzRCQUMxRCxLQUFLLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBRSxDQUFDO3lCQUN6QjtxQkFDSjtpQkFDSjthQUNKO1FBQ0wsQ0FBQyxFQUFDLENBQUM7UUFFSDs7Ozs7OztlQU9PO1FBQ1AsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBQzdDLENBQUM7Ozs7O0lBS00sWUFBWTtRQUNmLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztJQUMxQixDQUFDOzs7Ozs7SUFFTSxNQUFNLENBQUMsV0FBVyxDQUFDLFVBQWUsRUFBRSxVQUFlO1FBQ3RELElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLEtBQUssSUFBSSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFO1lBQzlELE9BQU8sS0FBSyxDQUFDO1NBQ2hCO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQzs7Ozs7O0lBR00sTUFBTSxDQUFDLFFBQVEsQ0FBQyxHQUFRLEVBQUUsSUFBWTtRQUN6QyxPQUFPLGFBQWEsQ0FBQyxRQUFRLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxDQUFDO0lBQzdDLENBQUM7Ozs7OztJQUVNLE1BQU0sQ0FBQyxVQUFVLENBQUMsR0FBUSxFQUFFLEtBQWU7UUFDOUMsT0FBTyxhQUFhLENBQUMsVUFBVSxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztJQUNoRCxDQUFDOzs7O0lBSU0sbUJBQW1COztZQUNsQixRQUFRLEdBQUcsRUFBRTtRQUNqQixJQUFJO1lBQ0EsUUFBUSxHQUFHLElBQUksS0FBSyxFQUFFLENBQUM7WUFDdkIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDL0QsUUFBUSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUM7YUFDMUY7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLCtEQUErRDtZQUMvRCxtR0FBbUc7U0FDdEc7UUFDRCxPQUFPLFFBQVEsQ0FBQyxNQUFNLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDO0lBQ25FLENBQUM7Ozs7SUFDTSxxQkFBcUI7O1lBQ3BCLG9CQUE2Qjs7WUFDN0IsU0FBdUI7UUFDM0IsSUFBSTtZQUNBLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEtBQUssZUFBZSxFQUFFO2dCQUN6QyxTQUFTLEdBQUcsbUJBQWMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUEsQ0FBQztnQkFFL0MsSUFBSSxPQUFPLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxJQUFJLFFBQVEsRUFBRTtvQkFDMUMsb0JBQW9CLEdBQUcsQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLFdBQVcsRUFBRSxLQUFLLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztpQkFDeEY7cUJBQU0sSUFBSSxPQUFPLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxJQUFJLFNBQVMsRUFBRTtvQkFDbEQsb0JBQW9CLEdBQUcsQ0FBQyxTQUFTLENBQUMsU0FBUyxJQUFJLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztpQkFDdkU7YUFDSjtpQkFBTTtnQkFDSCxTQUFTLEdBQUcsbUJBQWMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsYUFBYSxFQUFBLENBQUM7Z0JBQzdELElBQUksT0FBTyxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsSUFBSSxRQUFRLEVBQUU7b0JBQzFDLG9CQUFvQixHQUFHLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxXQUFXLEVBQUUsS0FBSyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7aUJBQ3hGO3FCQUFNLElBQUksT0FBTyxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsSUFBSSxTQUFTLEVBQUU7b0JBQ2xELG9CQUFvQixHQUFHLENBQUMsQ0FBQyxtQkFBSyxTQUFTLENBQUMsU0FBUyxFQUFBLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxXQUFXLEVBQUUsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7aUJBQ3pHO3FCQUFNO29CQUNILG9CQUFvQixHQUFHLENBQUMsQ0FBQyxtQkFBSyxTQUFTLENBQUMsU0FBUyxFQUFBLENBQUMsQ0FBQyxPQUFPLENBQUMsV0FBVyxFQUFFLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDO2lCQUN2RzthQUVKO1lBQ0Q7Ozs7OztlQU1HO1NBQ047UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU87WUFDUCxxQ0FBcUM7WUFDckMsZ0VBQWdFO1lBQ2hFLHdIQUF3SDtTQUMzSDtRQUNELE9BQU8sb0JBQW9CLENBQUM7SUFDaEMsQ0FBQzs7OztJQUVNLHNCQUFzQjs7WUFDckIscUJBQXFCLEdBQVcsSUFBSTs7WUFDcEMsU0FBdUI7UUFDM0IsSUFBSTtZQUNBLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEtBQUssZUFBZSxFQUFFO2dCQUN6QyxTQUFTLEdBQUcsbUJBQWMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUEsQ0FBQztnQkFDL0MscUJBQXFCLEdBQUcsU0FBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQzthQUN4RDtpQkFBTTtnQkFDSCxTQUFTLEdBQUcsbUJBQWMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsYUFBYSxFQUFBLENBQUM7Z0JBQzdELHFCQUFxQixHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUM7YUFDeEQ7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTztZQUNQLHFDQUFxQztZQUNyQyxnRUFBZ0U7WUFDaEUsc0dBQXNHO1NBQ3pHO1FBQ0QsT0FBTyxxQkFBcUIsQ0FBQztJQUNqQyxDQUFDOzs7O0lBRU0sdUJBQXVCOztZQUV0QixzQkFBc0IsR0FBVyxJQUFJO1FBRXpDLElBQUk7WUFDQSxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxLQUFLLGVBQWUsRUFBRTtnQkFDekMsc0JBQXNCLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO2FBQzFEO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLHFDQUFxQztZQUNyQywrREFBK0Q7WUFDL0QsdUdBQXVHO1NBQzFHO1FBQ0QsT0FBTyxzQkFBc0IsQ0FBQztJQUNsQyxDQUFDOzs7O0lBRU0sY0FBYzs7WUFFYixZQUFxQjtRQUN6QixJQUFJO1lBQ0EsWUFBWSxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsWUFBWSxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQztTQUMvRTtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLHdJQUF3STtTQUMzSTtRQUNELE9BQU8sWUFBWSxDQUFDO0lBQ3hCLENBQUM7Ozs7SUFFTSxhQUFhOztZQUVaLFVBQWtCO1FBQ3RCLElBQUk7WUFDQSxVQUFVLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDO1NBQ2hEO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMsdUlBQXVJO1NBQzFJO1FBQ0QsT0FBTyxVQUFVLENBQUM7SUFDdEIsQ0FBQzs7OztJQUVNLGNBQWM7O1lBQ2IsV0FBbUI7UUFFdkIsSUFBSTtZQUNBLFdBQVcsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUM7U0FFOUM7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLHFDQUFxQztZQUNyQyx3SUFBd0k7U0FDM0k7UUFDRCxPQUFPLFdBQVcsQ0FBQztJQUN2QixDQUFDOzs7O0lBRU0sU0FBUzs7WUFFUixrQkFBdUI7UUFDM0IsSUFBSTtZQUNBLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEtBQUssZUFBZSxFQUFFO2dCQUN6QyxrQkFBa0IsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUM7YUFDNUQ7aUJBQU07Z0JBQ0gsa0JBQWtCLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUM7YUFDOUM7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLG1JQUFtSTtTQUN0STtRQUNELE9BQU8sa0JBQWtCLENBQUM7SUFDOUIsQ0FBQzs7OztJQUVNLGFBQWE7O1lBQ1osVUFBZTtRQUNuQixJQUFJO1lBQ0EsVUFBVSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQztTQUNoRDtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLHVJQUF1STtTQUMxSTtRQUNELE9BQU8sVUFBVSxDQUFDO0lBQ3RCLENBQUM7Ozs7SUFFTSxhQUFhOztZQUNaLFVBQWU7UUFDbkIsSUFBSTtZQUNBLFVBQVUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQztTQUMzRDtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLHVJQUF1STtTQUMxSTtRQUNELE9BQU8sVUFBVSxDQUFDO0lBQ3RCLENBQUM7Ozs7SUFFTSxXQUFXOztZQUNWLFVBQWU7UUFDbkIsSUFBSTtZQUNBLFVBQVUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDO1NBQy9DO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMscUlBQXFJO1NBQ3hJO1FBRUQsT0FBTyxVQUFVLENBQUM7SUFDdEIsQ0FBQzs7OztJQUNNLGlCQUFpQjs7WUFDaEIsY0FBbUI7UUFDdkIsSUFBSTtZQUNBLGNBQWMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDO1NBQ3pEO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMscUlBQXFJO1NBQ3hJO1FBRUQsT0FBTyxjQUFjLENBQUM7SUFDMUIsQ0FBQzs7OztJQUVNLGVBQWU7O1lBQ2QsT0FBWTtRQUNoQixJQUFJO1lBQ0EsT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDO1NBQ3hEO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMscUlBQXFJO1NBQ3hJO1FBRUQsT0FBTyxPQUFPLENBQUM7SUFDbkIsQ0FBQzs7OztJQUNNLFVBQVU7O1lBQ1QsT0FBZTtRQUNuQixJQUFJO1lBQ0EsT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDO1NBQ2pEO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMsb0lBQW9JO1NBQ3ZJO1FBQ0QsT0FBTyxPQUFPLENBQUM7SUFDbkIsQ0FBQzs7OztJQUdNLGFBQWE7UUFDaEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDOztZQUV0QyxTQUFjO1FBQ2xCLElBQUk7WUFDQSxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUU7Z0JBQ2pGLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDO2dCQUM5RSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDO2FBQ2pEO1lBQ0QsU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUM7U0FDaEQ7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLHFDQUFxQztZQUNyQywrREFBK0Q7WUFDL0QsNEdBQTRHO1NBQy9HO1FBQ0QsT0FBTyxTQUFTLENBQUM7SUFDckIsQ0FBQzs7OztJQUVNLFVBQVU7O1lBQ1QsU0FBb0I7UUFDeEIsSUFBSTtZQUNBLFNBQVMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUM7WUFDekMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEVBQUU7Z0JBQzdCLFNBQVMsQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFBO2FBQzlDO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFckIsT0FBTztZQUNQLHFDQUFxQztZQUNyQyw2RkFBNkY7WUFDN0YsOEVBQThFO1NBQ2pGO1FBQ0QsT0FBTyxTQUFTLENBQUM7SUFDckIsQ0FBQzs7OztJQUdNLFVBQVU7O1lBQ1QsT0FBZTtRQUNuQixJQUFJO1lBQ0EsT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDO1NBQ3REO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixxQ0FBcUM7WUFDckMsK0RBQStEO1lBQy9ELDZGQUE2RjtTQUNoRztRQUNELE9BQU8sT0FBTyxDQUFDO0lBQ25CLENBQUM7Ozs7SUFFTSxjQUFjOztZQUNiLFdBQW1CO1FBQ3ZCLElBQUk7WUFDQSxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksU0FBUyxFQUFFO2dCQUMvQyxPQUFPLEdBQUcsQ0FBQzthQUNkO1lBQ0QsV0FBVyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDO1lBQzNELE9BQU8sV0FBVyxDQUFDO1NBQ3RCO1FBQ0QsT0FBTyxLQUFLLEVBQUU7WUFDVixPQUFPLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxLQUFLLENBQUMsQ0FBQTtZQUM3QixxQ0FBcUM7WUFDckMsK0RBQStEO1lBQy9ELDRHQUE0RztTQUMvRztJQUNMLENBQUM7Ozs7SUFFTSxXQUFXOztZQUNWLFFBQVEsR0FBUSxJQUFJO1FBQ3hCLElBQUk7WUFDSSxRQUFRLEdBQUcsVUFBVSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxRQUFRLENBQUMsQ0FBQztTQUN0RTtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osZ0lBQWdJO1NBQ25JO1FBQ0QsT0FBTyxRQUFRLENBQUM7SUFDcEIsQ0FBQzs7OztJQUNNLGdCQUFnQjs7WUFDZixhQUFrQjtRQUN0QixJQUFJO1lBQ0EsYUFBYSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQztTQUNuRDtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1oscUNBQXFDO1lBQ3JDLDBJQUEwSTtTQUM3STtRQUNELE9BQU8sYUFBYSxDQUFDO0lBQ3pCLENBQUM7Ozs7Ozs7SUFPTSxPQUFPLENBQUMsTUFBYzs7WUFDckIsUUFBUSxHQUFHLElBQUksVUFBVSxFQUFFOztZQUMzQixHQUFXO1FBQ2YsS0FBSyxHQUFHLElBQUksTUFBTSxFQUFFO1lBQ2hCLElBQUksTUFBTSxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsRUFBRTtnQkFDNUIsUUFBUSxDQUFDLElBQUksR0FBRyxHQUFHLENBQUM7Z0JBQ3BCLFFBQVEsQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDO2dCQUM3QixNQUFNO2FBQ1Q7U0FDSjtRQUNELE9BQU8sUUFBUSxDQUFDO0lBQ3BCLENBQUM7Ozs7SUFHTSxvQkFBb0I7O1lBQ25CLG1CQUFtQixHQUFXLElBQUk7O1lBQ2xDLFdBQXdCO1FBQzVCLElBQUk7WUFDQSxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxLQUFLLGNBQWMsRUFBRTtnQkFDeEMsV0FBVyxHQUFHLG1CQUFhLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFBLENBQUM7Z0JBQ2hELG1CQUFtQixHQUFHLFdBQVcsQ0FBQyxjQUFjLENBQUMsUUFBUSxFQUFFLENBQUM7YUFDL0Q7aUJBQU07Z0JBQ0gsV0FBVyxHQUFHLG1CQUFhLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBQSxDQUFDO2dCQUM3RCxtQkFBbUIsR0FBRyxXQUFXLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFBRSxDQUFDO2FBQy9EO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFDLHNCQUFzQixFQUFDLFlBQVksQ0FBQyxDQUFDO1NBQzFEO1FBQ0QsT0FBTyxtQkFBbUIsQ0FBQztJQUMvQixDQUFDOzs7O0lBRU0scUJBQXFCOztZQUNwQixvQkFBb0IsR0FBVyxJQUFJOztZQUNuQyxXQUF3QjtRQUM1QixJQUFJO1lBQ0EsSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksS0FBSyxjQUFjLEVBQUU7Z0JBQ3hDLFdBQVcsR0FBRyxtQkFBYSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBQSxDQUFDO2dCQUNoRCxvQkFBb0IsR0FBRyxXQUFXLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFBRSxDQUFDO2FBQ2hFO2lCQUFNO2dCQUNILFdBQVcsR0FBRyxtQkFBYSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxZQUFZLEVBQUEsQ0FBQztnQkFDN0Qsb0JBQW9CLEdBQUcsV0FBVyxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQzthQUNoRTtTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBQyx1QkFBdUIsRUFBQyxZQUFZLENBQUMsQ0FBQztTQUMzRDtRQUNELE9BQU8sb0JBQW9CLENBQUM7SUFDaEMsQ0FBQztDQUdKOzs7Ozs7SUEzWkcsK0JBQXVCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtSb290T2JqZWN0LCBSZXF1ZXN0UmVwbHksIFNlbGVjdHMsIEpTT05SZXNwb25zZSwgUHJvY2Vzc0luZm99IGZyb20gJy4uL3V0aWxzL3N3dC1pbnRlcmZhY2VzJztcclxuaW1wb3J0IHsgU3d0VXRpbCB9IGZyb20gXCIuLi91dGlscy9zd3QtdXRpbC5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuXHJcblxyXG5leHBvcnQgY2xhc3MgSlNPTlJlYWRlciB7XHJcblxyXG4gICAgcHJpdmF0ZSBpbnB1dEpTT046IGFueTtcclxuXHJcbiAgICBwdWJsaWMgSlNPTlJlYWRlcigpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgc2V0SW5wdXRKU09OKGlucHV0SlNPTjogYW55KSB7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogVGhpcyBibG9jIG9mIGNvZGUgaXMgYWRkZWQgdG8gYWRkIGNvbnRlbnQgXHJcbiAgICAgICAgICogdG8gZ3JpZCBkYXRhIGlmIGlzIHVuZGVmaW5lZFxyXG4gICAgICAgICAqICovXHJcbiAgICAgICAgSlNPTlJlYWRlci5qc29ucGF0aChpbnB1dEpTT04sICckLi5ncmlkWypdLi5yb3dbKl0nKS5mb3JFYWNoKGZ1bmN0aW9uICh2YWx1ZTogT2JqZWN0KSB7XHJcbiAgICAgICAgICAgIHZhciBjb2x1bW46IGFueTtcclxuICAgICAgICAgICAgZm9yICh2YXIga2V5IGluIHZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUuaGFzT3duUHJvcGVydHkoa2V5KSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgKHZhbHVlW2tleV0pID09ICdvYmplY3QnICYmIHZhbHVlW2tleV0uY29udGVudCA9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVba2V5XS5jb250ZW50ID0gXCJcIjtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsdWVbXCJjb250ZW50XCJdID09IHVuZGVmaW5lZCAmJiB2YWx1ZSA9PT0gT2JqZWN0KHZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVbXCJjb250ZW50XCJdID0gXCJcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvKiBKU09OUmVhZGVyLmpzb25wYXRoKGlucHV0SlNPTiwnJC4qLmdyaWQucm93cy5yb3cnKS5mb3JFYWNoKGZ1bmN0aW9uIChyb3cpIHtcclxuICAgICAgICAgXHJcbiAgICAgICAgICAgICBKU09OUmVhZGVyLmpzb25wYXRoKHJvdywnJC4qLionKS5mb3JFYWNoKGZ1bmN0aW9uICh2YWx1ZSkge1xyXG4gICAgICAgICAgICAgICAgIGlmKHZhbHVlLmNvbnRlbnQgPT0gdW5kZWZpbmVkICYmIHR5cGVvZiB2YWx1ZSAhPSAnbnVtYmVyJyl7XHJcbiAgICAgICAgICAgICAgICAgICAgIHZhbHVlLmNvbnRlbnQgPSBcIlwiO1xyXG4gICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgIH0pOyBcclxuICAgICAgICAgfSk7ICovXHJcbiAgICAgICAgdGhpcy5pbnB1dEpTT04gPSB0aGlzLmdldFJvb3QoaW5wdXRKU09OKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgZ2V0SW5wdXRKU09OKCk6IGFueSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuaW5wdXRKU09OO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzdGF0aWMgY29tcGFyZUpTT04oaW5wdXRKU09OMTogYW55LCBpbnB1dEpTT04yOiBhbnkpOiBib29sZWFuIHtcclxuICAgICAgICBpZiAoIShKU09OLnN0cmluZ2lmeShpbnB1dEpTT04xKSA9PT0gSlNPTi5zdHJpbmdpZnkoaW5wdXRKU09OMikpKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcblxyXG5cclxuICAgIHB1YmxpYyBzdGF0aWMganNvbnBhdGgob2JqOiBhbnksIHBhdGg6IHN0cmluZyk6IGFueSB7XHJcbiAgICAgICAgcmV0dXJuIENvbW1vblNlcnZpY2UuanNvbnBhdGgob2JqLCBwYXRoKTtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIGpzb25wYXRoZXMob2JqOiBhbnksIHBhdGhzOiBzdHJpbmdbXSk6IGFueSB7XHJcbiAgICAgICAgcmV0dXJuIENvbW1vblNlcnZpY2UuanNvbnBhdGhlcyhvYmosIHBhdGhzKTtcclxuICAgIH1cclxuXHJcblxyXG5cclxuICAgIHB1YmxpYyBnZXRTY3JlZW5BdHRyaWJ1dGVzKCk6IGFueSB7XHJcbiAgICAgICAgbGV0IHRtcEFycmF5ID0gW107XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgdG1wQXJyYXkgPSBuZXcgQXJyYXkoKTtcclxuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmlucHV0SlNPTi52YWx1ZS5hdHRyaWJ1dGVzLmxlbmd0aCgpOyBpKyspIHtcclxuICAgICAgICAgICAgICAgIHRtcEFycmF5W3RoaXMuaW5wdXRKU09OLnZhbHVlLmF0dHJpYnV0ZXNbaV0ubmFtZV0gPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5hdHRyaWJ1dGVzW2ldO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCxcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRTY3JlZW5BdHRyaWJ1dGVzXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdG1wQXJyYXkubGVuZ3RoID09PSAwID8gdGhpcy5pbnB1dEpTT04udmFsdWUgOiB0bXBBcnJheTtcclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXRSZXF1ZXN0UmVwbHlTdGF0dXMoKTogYm9vbGVhbiB7XHJcbiAgICAgICAgbGV0IHJlcXVlc3RfcmVwbHlfc3RhdHVzOiBib29sZWFuO1xyXG4gICAgICAgIGxldCByZXFSZXBsYXk6IFJlcXVlc3RSZXBseTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodGhpcy5pbnB1dEpTT04ubmFtZSA9PT0gJ3JlcXVlc3RfcmVwbHknKSB7XHJcbiAgICAgICAgICAgICAgICByZXFSZXBsYXkgPSA8UmVxdWVzdFJlcGx5PnRoaXMuaW5wdXRKU09OLnZhbHVlO1xyXG5cclxuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgKHJlcVJlcGxheS5zdGF0dXNfb2spID09IFwic3RyaW5nXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0X3JlcGx5X3N0YXR1cyA9IChyZXFSZXBsYXkuc3RhdHVzX29rLnRvTG93ZXJDYXNlKCkgPT09IFwidHJ1ZVwiKSA/IHRydWUgOiBmYWxzZTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIChyZXFSZXBsYXkuc3RhdHVzX29rKSA9PSBcImJvb2xlYW5cIikge1xyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3RfcmVwbHlfc3RhdHVzID0gKHJlcVJlcGxheS5zdGF0dXNfb2sgPT0gdHJ1ZSkgPyB0cnVlIDogZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICByZXFSZXBsYXkgPSA8UmVxdWVzdFJlcGx5PnRoaXMuaW5wdXRKU09OLnZhbHVlLnJlcXVlc3RfcmVwbHk7XHJcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIChyZXFSZXBsYXkuc3RhdHVzX29rKSA9PSBcInN0cmluZ1wiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdF9yZXBseV9zdGF0dXMgPSAocmVxUmVwbGF5LnN0YXR1c19vay50b0xvd2VyQ2FzZSgpID09PSAndHJ1ZScpID8gdHJ1ZSA6IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgKHJlcVJlcGxheS5zdGF0dXNfb2spID09IFwiYm9vbGVhblwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdF9yZXBseV9zdGF0dXMgPSAoKDxhbnk+cmVxUmVwbGF5LnN0YXR1c19vaykudG9TdHJpbmcoKS50b0xvd2VyQ2FzZSgpID09ICd0cnVlJykgPyB0cnVlIDogZmFsc2U7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3RfcmVwbHlfc3RhdHVzID0gKCg8YW55PnJlcVJlcGxheS5zdGF0dXNfb2spLmNvbnRlbnQudG9Mb3dlckNhc2UoKSA9PT0gXCJ0cnVlXCIpID8gdHJ1ZSA6IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvKmlmICh0aGlzLmlucHV0SlNPTi5uYW1lID09PSAncmVxdWVzdF9yZXBseScpIHtcclxuICAgICAgICAgICAgICAgIHJlcVJlcGxheSA9IDxSZXF1ZXN0UmVwbHk+IHRoaXMuaW5wdXRKU09OLnZhbHVlO1xyXG4gICAgICAgICAgICAgICAgcmVxdWVzdF9yZXBseV9zdGF0dXMgPSAocmVxUmVwbGF5LnN0YXR1c19vay50b1N0cmluZygpID09ICd0cnVlJykgPyB0cnVlIDogZmFsc2U7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICByZXFSZXBsYXkgPSA8UmVxdWVzdFJlcGx5PiB0aGlzLmlucHV0SlNPTi52YWx1ZS5yZXF1ZXN0X3JlcGx5O1xyXG4gICAgICAgICAgICAgICAgcmVxdWVzdF9yZXBseV9zdGF0dXMgPSAocmVxUmVwbGF5LnN0YXR1c19vay50b1N0cmluZygpID09ICd0cnVlJykgPyB0cnVlIDogZmFsc2U7XHJcbiAgICAgICAgICAgIH0qL1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIFRPRE9cclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgICAgICAgICAgICAgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldFJlcXVlc3RSZXBseVN0YXR1c1wiLCBlcnJvckxvY2F0aW9uKTsgICAgICAgICAgICAgICBcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHJlcXVlc3RfcmVwbHlfc3RhdHVzO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRSZXF1ZXN0UmVwbHlNZXNzYWdlKCk6IHN0cmluZyB7XHJcbiAgICAgICAgbGV0IHJlcXVlc3RfcmVwbHlfbWVzc2FnZTogc3RyaW5nID0gbnVsbDtcclxuICAgICAgICBsZXQgcmVxUmVwbGF5OiBSZXF1ZXN0UmVwbHk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKHRoaXMuaW5wdXRKU09OLm5hbWUgPT09ICdyZXF1ZXN0X3JlcGx5Jykge1xyXG4gICAgICAgICAgICAgICAgcmVxUmVwbGF5ID0gPFJlcXVlc3RSZXBseT50aGlzLmlucHV0SlNPTi52YWx1ZTtcclxuICAgICAgICAgICAgICAgIHJlcXVlc3RfcmVwbHlfbWVzc2FnZSA9IHJlcVJlcGxheS5tZXNzYWdlLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICByZXFSZXBsYXkgPSA8UmVxdWVzdFJlcGx5PnRoaXMuaW5wdXRKU09OLnZhbHVlLnJlcXVlc3RfcmVwbHk7XHJcbiAgICAgICAgICAgICAgICByZXF1ZXN0X3JlcGx5X21lc3NhZ2UgPSByZXFSZXBsYXkubWVzc2FnZS50b1N0cmluZygpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gVE9ET1xyXG4gICAgICAgICAgICAvLyBsb2cgdGhlIGVycm9yIGluIFNfRVJST1JfTE9HIHRhYmxlXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCBcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRSZXF1ZXN0UmVwbHlNZXNzYWdlXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcmVxdWVzdF9yZXBseV9tZXNzYWdlO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRSZXF1ZXN0UmVwbHlMb2NhdGlvbigpOiBzdHJpbmcge1xyXG5cclxuICAgICAgICBsZXQgcmVxdWVzdF9yZXBseV9sb2NhdGlvbjogc3RyaW5nID0gbnVsbDtcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKHRoaXMuaW5wdXRKU09OLm5hbWUgPT09ICdyZXF1ZXN0X3JlcGx5Jykge1xyXG4gICAgICAgICAgICAgICAgcmVxdWVzdF9yZXBseV9sb2NhdGlvbiA9IHRoaXMuaW5wdXRKU09OLnZhbHVlLmxvY2F0aW9uO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCxcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRSZXF1ZXN0UmVwbHlMb2NhdGlvblwiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHJlcXVlc3RfcmVwbHlfbG9jYXRpb247XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGlzRGF0YUJ1aWxkaW5nKCk6IEJvb2xlYW4ge1xyXG5cclxuICAgICAgICB2YXIgZGF0YUJ1aWxkaW5nOiBCb29sZWFuO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGRhdGFCdWlsZGluZyA9ICh0aGlzLmlucHV0SlNPTi52YWx1ZS5kYXRhYnVpbGRpbmcgPT0gXCJ0cnVlXCIpID8gdHJ1ZSA6IGZhbHNlO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJpc0RhdGFCdWlsZGluZ1wiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGRhdGFCdWlsZGluZztcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0RGF0ZUZvcm1hdCgpOiBzdHJpbmcge1xyXG5cclxuICAgICAgICBsZXQgZGF0ZWZvcm1hdDogc3RyaW5nO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGRhdGVmb3JtYXQgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5kYXRlZm9ybWF0O1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXREYXRlRm9ybWF0XCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gZGF0ZWZvcm1hdDtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0UmVmcmVzaFJhdGUoKTogc3RyaW5nIHtcclxuICAgICAgICBsZXQgcmVmcmVzaFJhdGU6IHN0cmluZztcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgcmVmcmVzaFJhdGUgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5yZWZyZXNoO1xyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAvLyBsb2cgdGhlIGVycm9yIGluIFNfRVJST1JfTE9HIHRhYmxlXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCBnZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcykgKyBcIi5teG1sXCIsIFwiZ2V0UmVmcmVzaFJhdGVcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiByZWZyZXNoUmF0ZTtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0VGltaW5nKCk6IGFueSB7XHJcblxyXG4gICAgICAgIGxldCByZXF1ZXN0UmVwbHlUaW1pbmc6IGFueTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodGhpcy5pbnB1dEpTT04ubmFtZSA9PT0gJ3JlcXVlc3RfcmVwbHknKSB7XHJcbiAgICAgICAgICAgICAgICByZXF1ZXN0UmVwbHlUaW1pbmcgPSB0aGlzLmlucHV0SlNPTi5yZXF1ZXN0X3JlcGx5LnRpbWluZztcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHJlcXVlc3RSZXBseVRpbWluZyA9IHRoaXMuaW5wdXRKU09OLnRpbWluZztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRUaW1pbmdcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiByZXF1ZXN0UmVwbHlUaW1pbmc7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldFNpbmdsZXRvbnMoKTogYW55IHtcclxuICAgICAgICBsZXQgc2luZ2xldG9uczogYW55O1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIHNpbmdsZXRvbnMgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5zaW5nbGV0b25zO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRTaW5nbGV0b25zXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gc2luZ2xldG9ucztcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0Q29sdW1uRGF0YSgpOiBhbnkge1xyXG4gICAgICAgIHZhciBjb2x1bW5EYXRhOiBhbnk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29sdW1uRGF0YSA9IHRoaXMuaW5wdXRKU09OLnZhbHVlLmdyaWQubWV0YWRhdGEuY29sdW1ucztcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAvLyBsb2cgdGhlIGVycm9yIGluIFNfRVJST1JfTE9HIHRhYmxlXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELCBnZXRRdWFsaWZpZWRDbGFzc05hbWUodGhpcykgKyBcIi5teG1sXCIsIFwiZ2V0U2luZ2xldG9uc1wiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGNvbHVtbkRhdGE7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldEdyaWREYXRhKCk6IGFueSB7XHJcbiAgICAgICAgbGV0IGNvbHVtbkRhdGE6IGFueTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb2x1bW5EYXRhID0gdGhpcy5pbnB1dEpTT04udmFsdWUuZ3JpZC5yb3dzO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRHcmlkRGF0YVwiLCBlcnJvckxvY2F0aW9uKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiBjb2x1bW5EYXRhO1xyXG4gICAgfVxyXG4gICAgcHVibGljIGdldEJvdHRvbUdyaWREYXRhKCk6IGFueSB7XHJcbiAgICAgICAgbGV0IGJvdHRvbUdyaWREYXRhOiBhbnk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgYm90dG9tR3JpZERhdGEgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ib3R0b21ncmlkLnJvd3M7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldEdyaWREYXRhXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIGJvdHRvbUdyaWREYXRhO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRHcmlkTWV0YURhdGEoKTogYW55IHtcclxuICAgICAgICBsZXQgY29sdW1uczogYW55O1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbHVtbnMgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLm1ldGFkYXRhLmNvbHVtbnM7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldEdyaWREYXRhXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIGNvbHVtbnM7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0Um93U2l6ZSgpOiBudW1iZXIge1xyXG4gICAgICAgIGxldCByb3dTaXplOiBudW1iZXI7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgcm93U2l6ZSA9IHRoaXMuaW5wdXRKU09OLnZhbHVlLmdyaWQucm93cy5zaXplO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRSb3dTaXplXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcm93U2l6ZTtcclxuICAgIH1cclxuXHJcblxyXG4gICAgcHVibGljIGdldFRvdGFsc0RhdGEoKTogYW55IHtcclxuICAgICAgICB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnRvdGFscy5zaXplID0gMTtcclxuXHJcbiAgICAgICAgdmFyIHRvdGFsRGF0YTogYW55O1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGlmICghdGhpcy5pbnB1dEpTT04udmFsdWUuZ3JpZC50b3RhbHMucm93ICYmIHRoaXMuaW5wdXRKU09OLnZhbHVlLmdyaWQudG90YWxzLnRvdGFsKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnRvdGFscy5yb3cgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnRvdGFscy50b3RhbDtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnRvdGFscy50b3RhbDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0b3RhbERhdGEgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnRvdGFscztcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAvLyBsb2cgdGhlIGVycm9yIGluIFNfRVJST1JfTE9HIHRhYmxlXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgU3d0VXRpbC5sb2dFcnJvcihlcnJvciwgU3d0VXRpbC5TWVNURU1fTU9EVUxFX0lELFxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgICAgICAgICAgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldFRvdGFsc0RhdGFcIiwgZXJyb3JMb2NhdGlvbik7ICAgICAgICAgICAgICAgXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiB0b3RhbERhdGE7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldFNlbGVjdHMoKTogYW55IHtcclxuICAgICAgICBsZXQgdG90YWxEYXRhOiBTZWxlY3RzW107XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgdG90YWxEYXRhID0gdGhpcy5pbnB1dEpTT04udmFsdWUuc2VsZWN0cztcclxuICAgICAgICAgICAgaWYgKCF0b3RhbERhdGFbJ3NlbGVjdCddLmxlbmd0aCkge1xyXG4gICAgICAgICAgICAgICAgdG90YWxEYXRhWydzZWxlY3QnXSA9IFt0b3RhbERhdGFbJ3NlbGVjdCddXVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XHJcblxyXG4gICAgICAgICAgICAvLyBUT0RPXHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgICAgICAgICAgICBcIi5teG1sXCIsIFwiZ2V0VG90YWxzRGF0YVwiLCBlcnJvckxvY2F0aW9uKTsgICAgICAgICAgICAgICBcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHRvdGFsRGF0YTtcclxuICAgIH1cclxuXHJcblxyXG4gICAgcHVibGljIGdldE1heFBhZ2UoKTogc3RyaW5nIHtcclxuICAgICAgICBsZXQgbWF4UGFnZTogc3RyaW5nO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIG1heFBhZ2UgPSB0aGlzLmlucHV0SlNPTi52YWx1ZS5ncmlkLnBhZ2luZy5tYXhwYWdlO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIGxvZyB0aGUgZXJyb3IgaW4gU19FUlJPUl9MT0cgdGFibGVcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsXHJcbiAgICAgICAgICAgIC8vICAgICAgICAgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldE1heFBhZ2VcIiwgZXJyb3JMb2NhdGlvbik7ICAgICAgICAgICAgICBcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG1heFBhZ2U7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldEN1cnJlbnRQYWdlKCk6IHN0cmluZyB7XHJcbiAgICAgICAgdmFyIGN1cnJlbnRQYWdlOiBzdHJpbmc7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKHRoaXMuaW5wdXRKU09OLnZhbHVlLmdyaWQucGFnaW5nID09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwiMFwiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGN1cnJlbnRQYWdlID0gdGhpcy5pbnB1dEpTT04udmFsdWUuZ3JpZC5wYWdpbmcuY3VycmVudHBhZ2U7XHJcbiAgICAgICAgICAgIHJldHVybiBjdXJyZW50UGFnZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJlcnJvclwiLCBlcnJvcilcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCxcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSArIFwiLm14bWxcIiwgXCJnZXRDdXJyZW50UGFnZVwiLCBlcnJvckxvY2F0aW9uKTsgICAgICAgICAgICAgIFxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0VHJlZURhdGEoKTogYW55IHtcclxuICAgICAgICBsZXQgdHJlZURhdGE6IGFueSA9IG51bGw7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIHRyZWVEYXRhID0gSlNPTlJlYWRlci5qc29ucGF0aCh0aGlzLmlucHV0SlNPTi52YWx1ZSwgXCIkLnRyZWVcIik7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gICAgICAgICAgICBTd3RVdGlsLmxvZ0Vycm9yKGVycm9yLCBTd3RVdGlsLlNZU1RFTV9NT0RVTEVfSUQsIGdldFF1YWxpZmllZENsYXNzTmFtZSh0aGlzKSwgXCJnZXRBbGxDb2x1bW5EYXRhXCIsIGVycm9yTG9jYXRpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdHJlZURhdGE7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0QWxsQ29sdW1uRGF0YSgpOiBhbnkge1xyXG4gICAgICAgIGxldCBhbGxDb2x1bW5EYXRhOiBhbnk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgYWxsQ29sdW1uRGF0YSA9IHRoaXMuaW5wdXRKU09OLnZhbHVlLmFsbGNvbHVtbnM7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gbG9nIHRoZSBlcnJvciBpbiBTX0VSUk9SX0xPRyB0YWJsZVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgIFN3dFV0aWwubG9nRXJyb3IoZXJyb3IsIFN3dFV0aWwuU1lTVEVNX01PRFVMRV9JRCwgZ2V0UXVhbGlmaWVkQ2xhc3NOYW1lKHRoaXMpICsgXCIubXhtbFwiLCBcImdldEFsbENvbHVtbkRhdGFcIiwgZXJyb3JMb2NhdGlvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBhbGxDb2x1bW5EYXRhO1xyXG4gICAgfVxyXG4gICAgLyogRU5EIC1BZGRlZCAgdG8gZ2V0IGFsbGNvbHVtbiBkYXRhIGZyb20gaW5wdXQgWE1MIGJ5IE0uQm91cmFvdWkgb24gMDEtQXVndXN0LTEzKi9cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gZ2V0IHRoZSByb290IG9mIGEganNvbiByZXNwb25zZS5cclxuICAgICAqIEBwYXJhbSByZXN1bHRcclxuICAgICAqL1xyXG4gICAgcHVibGljIGdldFJvb3QocmVzdWx0OiBPYmplY3QpOiBSb290T2JqZWN0IHtcclxuICAgICAgICBsZXQgcm9vdG5vZGUgPSBuZXcgUm9vdE9iamVjdCgpO1xyXG4gICAgICAgIGxldCBrZXk6IHN0cmluZztcclxuICAgICAgICBmb3IgKGtleSBpbiByZXN1bHQpIHtcclxuICAgICAgICAgICAgaWYgKHJlc3VsdC5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7XHJcbiAgICAgICAgICAgICAgICByb290bm9kZS5uYW1lID0ga2V5O1xyXG4gICAgICAgICAgICAgICAgcm9vdG5vZGUudmFsdWUgPSByZXN1bHRba2V5XTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiByb290bm9kZTtcclxuICAgIH1cclxuXHJcblxyXG4gICAgcHVibGljIGdldHByb3NzZXNJbmZvU3RhdHVzKCk6IHN0cmluZyB7XHJcbiAgICAgICAgbGV0IHByb2Nlc3NfaW5mb19zdGF0dXM6IHN0cmluZyA9IG51bGw7XHJcbiAgICAgICAgbGV0IHByb2Nlc3NJbmZvOiBQcm9jZXNzSW5mbztcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodGhpcy5pbnB1dEpTT04ubmFtZSA9PT0gJ3Byb2Nlc3NfaW5mbycpIHtcclxuICAgICAgICAgICAgICAgIHByb2Nlc3NJbmZvID0gPFByb2Nlc3NJbmZvPnRoaXMuaW5wdXRKU09OLnZhbHVlO1xyXG4gICAgICAgICAgICAgICAgcHJvY2Vzc19pbmZvX3N0YXR1cyA9IHByb2Nlc3NJbmZvLnByb2Nlc3Nfc3RhdHVzLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzSW5mbyA9IDxQcm9jZXNzSW5mbz50aGlzLmlucHV0SlNPTi52YWx1ZS5wcm9jZXNzX2luZm87XHJcbiAgICAgICAgICAgICAgICBwcm9jZXNzX2luZm9fc3RhdHVzID0gcHJvY2Vzc0luZm8ucHJvY2Vzc19zdGF0dXMudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycm9yLFwiZ2V0cHJvc3Nlc0luZm9TdGF0dXNcIixcIkpzb25yZWFkZXJcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBwcm9jZXNzX2luZm9fc3RhdHVzO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRwcm9zc2VzSW5mb1J1bm5pbmcoKTogc3RyaW5nIHtcclxuICAgICAgICBsZXQgcHJvY2Vzc19pbmZvX3J1bm5pbmc6IHN0cmluZyA9IG51bGw7XHJcbiAgICAgICAgbGV0IHByb2Nlc3NJbmZvOiBQcm9jZXNzSW5mbztcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodGhpcy5pbnB1dEpTT04ubmFtZSA9PT0gJ3Byb2Nlc3NfaW5mbycpIHtcclxuICAgICAgICAgICAgICAgIHByb2Nlc3NJbmZvID0gPFByb2Nlc3NJbmZvPnRoaXMuaW5wdXRKU09OLnZhbHVlO1xyXG4gICAgICAgICAgICAgICAgcHJvY2Vzc19pbmZvX3J1bm5pbmcgPSBwcm9jZXNzSW5mby5ydW5uaW5nX3NlcW5ici50b1N0cmluZygpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcHJvY2Vzc0luZm8gPSA8UHJvY2Vzc0luZm8+dGhpcy5pbnB1dEpTT04udmFsdWUucHJvY2Vzc19pbmZvO1xyXG4gICAgICAgICAgICAgICAgcHJvY2Vzc19pbmZvX3J1bm5pbmcgPSBwcm9jZXNzSW5mby5ydW5uaW5nX3NlcW5ici50b1N0cmluZygpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coZXJyb3IsXCJnZXRwcm9zc2VzSW5mb1J1bm5pbmdcIixcIkpzb25yZWFkZXJcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBwcm9jZXNzX2luZm9fcnVubmluZztcclxuICAgIH1cclxuXHJcblxyXG59XHJcbiJdfQ==