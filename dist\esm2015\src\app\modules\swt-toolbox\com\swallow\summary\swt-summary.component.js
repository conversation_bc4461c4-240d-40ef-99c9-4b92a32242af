/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Keyboard } from './../utils/keyboard.service';
import { SwtUtil } from './../utils/swt-util.service';
import { SwtAlert } from './../utils/swt-alert.service';
import { JSONReader } from './../jsonhandler/jsonreader.service';
import { HDividedBox } from './../controls/swt-hdividedbox.component';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { SwtCanvas } from './../controls/swt-canvas.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, Component, Input, Renderer2, ViewChild } from "@angular/core";
import { SwtCommonGrid } from '../controls/swt-common-grid.component';
import { SwtLabel } from '../controls/swt-label.component';
import { Logger } from '../logging/logger.service';
import { CustomTreeEvent } from '../events/swt-events.module';
import { StringUtils } from '../utils/string-utils.service';
export class SwtSummary extends Container {
    //-------constructor-----------------------------------------------------------//
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this.expandFirstLoad = true;
        /**
         * JSON Objects
         *
         */
        this.jsonReader = new JSONReader();
        /**
         * Communication Objects
         *
         */
        this.baseURL = "";
        this.actionMethod = "";
        this.actionPath = "";
        this.facilityAccess = false;
        this.supportAllCurrency = false;
        this.supportAllEntity = false;
        /**
         * Summary Tree Objects
         *
         */
        this.treeOpenedItems = [];
        this.treeClosedItems = [];
        this.gridOpenedItems = [];
        this.gridVisibleItems = [];
        this.currentDivPos = -1;
        this.facilityId = "";
        this.facilityName = "";
        this.useGeneric = "";
        this.scenarioTitle = "";
        this.selectedscenario = "";
        this.resetFilter = false;
        this.columnData = [];
        this.logger = new Logger('SwtSummary', this.commonService.httpclient);
        this.swtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        this.logger.info('method [ngOnDestroy] : unscbscribe all Observables.  - START/END -');
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        this.summaryGrid.uniqueColumn = "expand";
        this.summaryGrid.lockedColumnCount = 1;
        this.summaryGrid.selectable = true;
        this.summaryGrid.enableRowSelection = false;
        this.setLabelField("label");
        this.hideRoot(true);
        this.showTreeDataTips(true);
        this.tree.saveTreeStateBasedOn = "id";
        this.IDField = "id";
        this.gridTitle.text = SwtUtil.getPredictMessage('scenarioSummary.selectedScen', null);
        this.gridTitle.toolTip = SwtUtil.getPredictMessage('tooltip.selectedScen', null);
        this.treeTitle.toolTip = SwtUtil.getPredictMessage('tooltip.scenTotals', null);
        this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null);
        this.tree.addEventListener(CustomTreeEvent.ITEMRENDER, (/**
         * @param {?} item
         * @return {?}
         */
        (item) => {
            /** @type {?} */
            let name = item.data.name;
            /** @type {?} */
            let ALERTABLE = "Y";
            /** @type {?} */
            let alert = item.data.alert;
            /** @type {?} */
            let isBranch = item.data.isBranch;
            if (isBranch == true) {
                item.setStyle("fontWeight", "normal");
                item.setStyle("fontSize", 12);
                item.setStyle("color", "black");
            }
            else {
                if (alert == ALERTABLE) {
                    item.setStyle("fontSize", 12);
                    item.setStyle("background", "url('assets/images/predict_images/alert.png') no-repeat 38px 0%");
                }
                else {
                    item.setStyle("fontWeight", "normal");
                    item.setStyle("fontSize", 12);
                    item.setStyle("background", "url('assets/images/predict_images/treefile.png') no-repeat 38px 0%");
                }
            }
        }));
        // adding listener on expand or collapse icon click on summaryGrid control
        this.tree.addEventListener(CustomTreeEvent.ITEMDOUBLECLICK, (/**
         * @param {?} item
         * @return {?}
         */
        (item) => {
            this.treeKeyDownHandler(item);
        }));
        this.summaryGrid.ITEM_CLICK.subscribe((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.changeTreeData(event);
        }));
    }
    /**
     * @param {?} label
     * @return {?}
     */
    setLabelField(label) {
        //this.tree.labelField='@' + label;
    }
    /**
     * @param {?} hide
     * @return {?}
     */
    hideRoot(hide) {
        if (hide == true) {
            this.tree.showRoot = false;
        }
        else {
            this.tree.showRoot = true;
        }
    }
    /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     *
     * @return {?}
     */
    getSortedGridColumn() {
        /** @type {?} */
        var selectedSort = null;
        /** @type {?} */
        var direction = null;
        direction = this.summaryGrid.sortDirection ? "DESC" : "ASC";
        selectedSort = this.summaryGrid.sortColumnIndex + "|" + direction;
        return selectedSort;
    }
    /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     *
     * @return {?}
     */
    getIsScenarioAlertable() {
        /** @type {?} */
        var isAlertable = "";
        if (this.tree.selectedItem) {
            /** @type {?} */
            var value = this.tree.selectedItem.data;
            isAlertable = value.alert == 'Y' ? 'Y' : 'N';
        }
        return isAlertable;
    }
    /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     *
     * @return {?}
     */
    getIsBranch() {
        /** @type {?} */
        var isBranch = "";
        /** @type {?} */
        var value = this.tree.selectedItem.data;
        isBranch = value.isBranch == 'true' ? 'true' : 'false';
        return isBranch;
    }
    /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     *
     * @return {?}
     */
    getDividerPosition() {
        return this.currentDivPos.toString();
    }
    /**
     * setActionPath
     *
     * @param {?} actionPath
     * @return {?}
     */
    setActionPath(actionPath) {
        this.actionPath = actionPath;
    }
    /**
     * setBaseURL
     *
     * This method is called to set baseURL
     *
     * @param {?} baseURL
     * @return {?}
     */
    setBaseURL(baseURL) {
        this.baseURL = baseURL;
    }
    /**
     * @param {?} facilityAcc
     * @param {?} entityAcc
     * @param {?} ccyAcc
     * @return {?}
     */
    setAccessRules(facilityAcc, entityAcc, ccyAcc) {
        // cellRenderer.properties={facilityAccess:facilityAcc,_supportAllEntity:entityAcc,_supportAllCurrency:ccyAcc};
    }
    /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     *
     * @return {?}
     */
    getSelectedItemID() {
        // var to hold the tree selected item
        /** @type {?} */
        var selectedItem = this.tree.selectedItem;
        /* Check if  the selectedItem is not null*/
        if (selectedItem != null) {
            // check if the node is not branch
            if (selectedItem.data.isBranch == false)
                return selectedItem.data.id;
        }
        return null;
    }
    /**
     * Return the first subNode of the tree
     *
     * @return {?}
     */
    setFirstSubNode() {
        if (this.jsonReader.getScreenAttributes()["selectedscenario"] !== "") {
            if (this.jsonReader.getTreeData()[0].root.node[0]) {
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node[0].node;
            }
            else {
                this.lastSelectedItem = this.jsonReader.getTreeData()[0].root.node.node;
            }
            this.tree.selectedIndex = 1;
        }
    }
    /**
         * selectTreeItem
         * @param newJSON:
         * This method is called to auto-select the last selected item when updating the dataprovider
         **/
    //selectTreeItem(newJSON): void {
    // let search= null;
    //  let index = -1;
    // if (this.lastSelectedItem) {
    //     console.log("this.lastSelectedItem-selectTreeItem", this.lastSelectedItem);
    //     console.log("this.lastSelectedItem-selectTreeItem",this.lastSelectedItem.data.isBranch );
    //    if (this.lastSelectedItem.data.isBranch == true) {
    //        search = newJSON.find(x=>x.data.id == this.lastSelectedItem.data.id);
    //
    //     } else {
    //        console.log("newJSON----", newJSON);
    //        console.log("lastSelectedItem----", this.lastSelectedItem.data.id, this.lastSelectedItem.data.alert);
    //          search = newJSON.find(x=>x.node.id == this.lastSelectedItem.data.id && x.node.alert == this.lastSelectedItem.data.alert);
    //          console.log("search----", search);
    //     }
    // }
    //
    //     if (search != null) {
    //         let itemsList = newJSON.node;
    //         let alertableCheck= true;
    //         if (search.isBranch =="true") {
    //             itemsList= newJSON.node;
    //             alertableCheck = false;
    //         }
    //     if(itemsList){
    //          itemsList.forEach (( item ) => {
    //              if(alertableCheck) {
    //                 if (item.id == search.id && item.alert==search.alert ) {
    //                      index = this.tree.getItemIndex(item);
    //                 }
    //             } else {
    //                 if (item.id==search.id ) {
    //                     index = this.tree.getItemIndex(item);
    //                 }
    //           }
    //         } );
    //     }
    //     }
    //     // set tree selected index
    //     this.tree.selectedIndex=index;
    // }
    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     *
     * @return {?}
     */
    enableTree() {
        this.tree.selectable = true;
    }
    /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     *
     * @return {?}
     */
    disableTree() {
        // disable the tree selection
        this.tree.selectable = false;
    }
    /**
     * showTreeDataTips
     * \@param:boolean
     * This method is used enable/disable tooltips on tree node
     *
     * @param {?} show
     * @return {?}
     */
    showTreeDataTips(show) {
        // this.tree.showDataTips = show;
        this.tree.dataTipFunction = this.dataTipFunction;
    }
    /**
     * dataTipFunction
     * \@param: Object
     * This function is used to set tooltip of tree node
     * @param {?} item
     * @return {?}
     */
    dataTipFunction(item) {
        return (item.data.desc) ? item.data.desc : "";
    }
    /**
     * treeNodeEventHandler
     * @param {?} event
     * @return {?}
     */
    treeNodeEventHandler(event) {
        this.saveTreeOpenState();
    }
    /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     * @return {?}
     */
    saveTreeOpenState() {
        // Initialise the open list array
        this.treeOpenedItems = [];
        this.treeClosedItems = [];
        if (this.tree.selectedItem != null) {
            if (this.lastSelectedItem) {
                if (this.tree.selectedItem.id !== this.lastSelectedItem.id) {
                    this.lastSelectedItem = this.tree.selectedItem;
                    this.resetFilter = true;
                }
                else {
                    this.resetFilter = false;
                }
            }
        }
        this.tree.saveTreeOpenState();
        if (this.tree.openItems.length > 0) {
            for (let i = 0; i < this.tree.openItems.length; i++) {
                this.treeOpenedItems.push(this.tree.openItems[i]);
            }
        }
        if (this.tree.closeItems.length > 0) {
            for (let i = 0; i < this.tree.closeItems.length; i++) {
                this.treeClosedItems.push(this.tree.closeItems[i]);
            }
        }
    }
    /**
     * openTreeItems
     * used to maintain the tree node
     * @return {?}
     */
    openTreeItems() {
        this.tree.openItems = [];
        for (let i = 0; i < this.treeOpenedItems.length; i++) {
            this.tree.openItems.push(this.treeOpenedItems[i]);
        }
        this.tree.closeItems = [];
        for (let i = 0; i < this.treeClosedItems.length; i++) {
            this.tree.closeItems.push(this.treeClosedItems[i]);
        }
        this.tree.reOpenSavedState();
    }
    /**
     * treeScrollEventHandler
     * @param {?} event
     * @return {?}
     */
    treeScrollEventHandler(event) {
        this.verticalScrollPosition = this.tree.verticalScrollPosition;
    }
    /**
     * treeItemDoubleClickHandler
     *
     * @param {?} event
     * @return {?}
     */
    treeItemDoubleClickHandler(event) {
        /** @type {?} */
        let node = this.tree.selectedItem;
        /** @type {?} */
        let isOpen = this.isItemOpen(node);
        this.tree.expandItem(node, !isOpen);
    }
    /**
     * @param {?} item
     * @return {?}
     */
    isItemOpen(item) {
        return item.isExpanded();
    }
    /**
     * changeTreeData
     *
     * \@param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     * @param {?} event
     * @return {?}
     */
    changeTreeData(event) {
        /** @type {?} */
        let colIndex = event.columnIndex;
        /** @type {?} */
        let expand = event.target.data.slickgrid_rowcontent['expand'].content == "Y";
        if (colIndex == 0 && expand) {
            /** @type {?} */
            let entity = event.target.data.entity;
            for (let i = 0; i < this.lastRecievedJSON.alert.grid.rows.size; i++) {
                /** @type {?} */
                var xmlElement = this.lastRecievedJSON.alert.grid.rows.row[i];
                if (xmlElement['entity'] && entity == xmlElement['entity'].content) {
                    this.lastRecievedJSON.alert.grid.rows.row[i].expand.opened = !xmlElement.expand.opened;
                }
                if (this.lastRecievedJSON.alert.grid.rows.row[i][this.summaryGrid.GroupId] && entity == this.lastRecievedJSON.alert.grid.rows.row[i][this.summaryGrid.GroupId].content && this.lastRecievedJSON.alert.grid.rows.row[i].expand.content != 'Y') {
                    this.lastRecievedJSON.alert.grid.rows.row[i].visible = !xmlElement.visible;
                }
            }
            if (this.lastRecievedJSON.alert.grid.rows.size != 1) {
                /** @type {?} */
                let grid2dataset = [];
                JSONReader.jsonpath(this.lastRecievedJSON, '$.alert.grid.rows.row.*').forEach((/**
                 * @param {?} value
                 * @return {?}
                 */
                function (value) {
                    if (value.visible == true || value.visible == "true") {
                        if (value === Object(value)) {
                            grid2dataset.push(value);
                        }
                    }
                }));
                this.summaryGrid.gridData = { row: grid2dataset, size: grid2dataset.length };
            }
            else {
                this.summaryGrid.gridData = this.jsonReader.getGridData();
                this.summaryGrid.setRowSize = this.jsonReader.getRowSize();
            }
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    treeKeyDownHandler(event) {
        /** @type {?} */
        let node = this.tree.selectedItem;
        /** @type {?} */
        let itemIsBranch = node.data.isBranch;
        // Check if event key is ENTER
        if (event.keyCode == Keyboard.ENTER || event.keyCode == Keyboard.UP || event.keyCode == Keyboard.DOWN) {
            if (!itemIsBranch) {
                // this.summaryGrid.applyFilterSort = true;
                dispatchEvent(new Event("treeItemClick"));
            }
            else {
                this.resetGridData();
            }
        }
    }
    /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     *
     * @return {?}
     */
    resetGridData() {
        // nullify the gridData
        this.summaryGrid.gridData = null;
        // validate properties
        // this.summaryGrid.validateProperties();
    }
    /**
     * dataProvider
     *
     * @param {?} data
     * @return {?}
     */
    dataProvider(data) {
        /** @type {?} */
        let dividerPos;
        if (data != null) {
            this.lastRecievedJSON = data;
            this.jsonReader.setInputJSON(this.lastRecievedJSON);
            if (this.lastRecievedJSON != this.prevRecievedJSON) {
                /** @type {?} */
                let treeData = this.lastRecievedJSON.alert.tree.root.node;
                if (this.tree.dataProvider != treeData) {
                    if (this.tree.openItems.length > 0) {
                        // this.saveTreeOpenState();
                    }
                    else {
                        if (this.expandFirstLoad) {
                            // expand tree to first level
                            setTimeout((/**
                             * @return {?}
                             */
                            () => {
                                this.tree.expandAll(CustomTree.LEVEL_1_STR);
                            }), 0);
                        }
                    }
                    this.tree.dataProvider = treeData;
                    this.setTreeTotals(this.jsonReader.getScreenAttributes()["total"]);
                    // this.tree.validateProperties();
                    if (this.treeOpenedItems != null) {
                        if (this.treeOpenedItems.length > 0) {
                            this.openTreeItems();
                        }
                        // this.selectTreeItem(treeData);
                    }
                    this.tree.verticalScrollPosition = this.verticalScrollPosition;
                    // validate display properties
                    // this.tree.validateProperties();
                }
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    if (this.tree.selectedItem && this.prevRecievedJSON && this.lastSelectedItem != null && this.lastSelectedItem.id == this.tree.selectedItem.id) {
                        try {
                            //  if (!this.resetFilter) {
                            // this.summaryGrid.applyFilterSort=true;
                            this.saveGridOpenState(this.prevRecievedJSON.alert.grid.rows.row);
                            this.lastRecievedJSON.alert.grid.rows.row = this.openGridItems(this.lastRecievedJSON.alert.grid.rows.row);
                            if (this.summaryGrid.isFiltered) {
                                this.lastRecievedJSON.alert.grid.rows.row = this.showVisibleItems(this.lastRecievedJSON.alert.grid.rows.row);
                            }
                        }
                        catch (error) {
                            console.log("SwtSummary -> dataProvider -> error", error);
                        }
                    }
                    else {
                        this.summaryGrid.isFiltered = false;
                        this.summaryGrid.sortColumnIndex = -1;
                    }
                    this.lastSelectedItem = this.deepCopy(this.tree.selectedItem);
                    this.facilityAccess = (this.jsonReader.getScreenAttributes()["facilityAccess"] !== "2");
                    this.supportAllCurrency = StringUtils.isTrue(this.jsonReader.getScreenAttributes()["supportAllCurrency"]);
                    this.supportAllEntity = StringUtils.isTrue(this.jsonReader.getScreenAttributes()["supportAllEntity"]);
                    // this.summaryGrid.setAccessRules(facilityAccess,supportAllEntity,supportAllCurrency);
                    /** @type {?} */
                    let jsonlist = this.jsonReader.getColumnData().column;
                    data.alert.grid.metadata.columns.column = jsonlist;
                    this.columnData = data.alert.grid.metadata;
                    for (let nonFiltrable in this.columnData['columns']['column']) {
                        if (this.columnData['columns']['column'][nonFiltrable].dataelement === "expand") {
                            this.columnData['columns']['column'][nonFiltrable].filterable = false;
                            this.columnData['columns']['column'][nonFiltrable].draggable = false;
                            this.columnData['columns']['column'][nonFiltrable].heading = "";
                        }
                    }
                    this.summaryGrid.CustomGrid(this.columnData);
                    this.summaryGrid.rowClickableFunction = (/**
                     * @param {?} dataContext
                     * @param {?} dataIndex
                     * @param {?} color
                     * @return {?}
                     */
                    (dataContext, dataIndex, color) => {
                        return this.setClickable(dataContext, dataIndex, color);
                    });
                    this.summaryGrid.GroupId = 'entity';
                    /** @type {?} */
                    let rowData = this.lastRecievedJSON.alert.grid.rows;
                    for (let i = 0; i < this.summaryGrid.columnDefinitions.length; i++) {
                        /** @type {?} */
                        let column = this.summaryGrid.columnDefinitions[i];
                        if (column.field == "entity") {
                            if (rowData.size > 1) {
                                for (let j = 0; j < rowData.row.length; j++) {
                                    rowData.row[j].entity.bold = Boolean(rowData.row[j].entity.haschildren);
                                }
                            }
                            else if (rowData.size == 1) {
                                if (rowData.row.entity) {
                                    rowData.row.entity.bold = rowData.row.entity.content == "All";
                                }
                                else {
                                    rowData.row[0].entity.bold = rowData.row[0].entity.content == "All";
                                }
                            }
                        }
                    }
                    if (this.jsonReader.getGridData()) {
                        if (this.jsonReader.getGridData().size > 0) {
                            if (this.jsonReader.getGridData().size > 1) {
                                /** @type {?} */
                                let grid2dataset = [];
                                JSONReader.jsonpath(data, '$.alert.grid.rows.row.*').forEach((/**
                                 * @param {?} value
                                 * @return {?}
                                 */
                                function (value) {
                                    if (value.visible == true) {
                                        if (value === Object(value)) {
                                            grid2dataset.push(value);
                                        }
                                    }
                                }));
                                this.summaryGrid.gridData = { row: grid2dataset, size: grid2dataset.length };
                            }
                            else {
                                this.summaryGrid.gridData = this.jsonReader.getGridData();
                                this.summaryGrid.setRowSize = this.jsonReader.getRowSize();
                            }
                            this.summaryGrid.saveColumnOrder = true;
                            this.summaryGrid.saveWidths = false;
                            this.summaryGrid.allowMultipleSelection = false;
                            this.summaryGrid.doubleClickEnabled = true;
                            this.summaryGrid.rowHeight = 20;
                            this.summaryGrid.styleName = 'dataGridNormal';
                            this.summaryGrid.colWidthURL(this.baseURL + this.actionPath);
                            this.summaryGrid.colOrderURL(this.baseURL + this.actionPath);
                        }
                        else {
                            this.summaryGrid.gridData = { row: [], size: 0 };
                        }
                    }
                    else {
                        this.summaryGrid.gridData = { row: [], size: 0 };
                    }
                    setTimeout((/**
                     * @return {?}
                     */
                    () => {
                        this.summaryGrid.validateNow();
                    }), 0);
                    this.facilityId = this.jsonReader.getScreenAttributes()["facilityid"];
                    this.facilityName = this.jsonReader.getScreenAttributes()["facilityname"];
                    this.useGeneric = this.jsonReader.getScreenAttributes()["useGeneric"];
                    this.scenarioTitle = this.jsonReader.getScreenAttributes()["scenarioTitle"];
                    this.selectedscenario = this.jsonReader.getScreenAttributes()["selectedscenario"];
                    dividerPos = this.jsonReader.getScreenAttributes()["dividerposition"];
                    /* check if the divider position from response xml is not empty */
                    if (dividerPos != "") {
                        if (this.currentDivPos == -1) {
                            if (!parseFloat(dividerPos)) {
                                this.currentDivPos = parseFloat('60');
                            }
                            else {
                                this.currentDivPos = parseFloat(dividerPos);
                            }
                            this.divBox.setWidthLeftWithoutEvent(this.currentDivPos + '%');
                        }
                    }
                    else {
                        // set default percent width of the summary grid
                        // this.gridContainer.width= this.currentDivPos * ( this.stage.width / 100 );
                        // validate the  grid content
                        // this.gridContainer.validateNow();
                    }
                    if (this.jsonReader.getScreenAttributes()["selectedscenariolastran"] != "") {
                        this.lastRanLbl.visible = true;
                        this.setScenarioLastRan(this.jsonReader.getScreenAttributes()["selectedscenariolastran"]);
                    }
                    else {
                        this.lastRanLbl.visible = false;
                    }
                    // set previous XML
                    this.prevRecievedJSON = this.lastRecievedJSON;
                }), 0);
            }
        }
    }
    /**
     * @private
     * @param {?} lastRan
     * @return {?}
     */
    setScenarioLastRan(lastRan) {
        this.lastRanLbl.text = StringUtils.trim(SwtUtil.getPredictMessage('scenarioSummary.selectedScenLastRan', null)) + " " + lastRan.replace('taking', SwtUtil.getPredictMessage('label.taking', null)).replace('seconds', SwtUtil.getPredictMessage('label.seconds', null));
    }
    /**
     * @param {?} dataContext
     * @param {?} index
     * @param {?} dataElement
     * @return {?}
     */
    setClickable(dataContext, index, dataElement) {
        /** @type {?} */
        const value = dataContext.slickgrid_rowcontent.count.content;
        /** @type {?} */
        let isClickable = false;
        // if value is greater than 0
        if (parseInt(value, 10) > 0) {
            if (this.facilityAccess == false) {
                //set clickable to false
                isClickable = false;
            }
            else {
                /** @type {?} */
                const entity = dataContext.slickgrid_rowcontent.entity.content;
                /** @type {?} */
                const ccy = dataContext.slickgrid_rowcontent.ccy.content;
                //if entiy value or currency value equal "All" we need to verify if the facility supports "All" as Currency or "All" as Entity
                if (entity == "All" || ccy == "All") {
                    if ((entity == "All") && (ccy != "All")) {
                        isClickable = this.supportAllEntity;
                    }
                    else if ((entity != "All") && (ccy == "All")) {
                        isClickable = this.supportAllCurrency;
                    }
                    else if ((entity == "All") && (ccy == "All")) {
                        isClickable = this.supportAllCurrency && this.supportAllEntity;
                    }
                }
                else {
                    isClickable = true;
                }
            }
        }
        else {
            isClickable = false;
        }
        return isClickable;
    }
    /**
     * showVisibleItems
     * @param {?} dataProvider
     * @return {?}
     */
    showVisibleItems(dataProvider) {
        if (dataProvider) {
            for (let i = 0; i < this.gridVisibleItems.length; i++) {
                for (let j = 0; j < dataProvider.length; j++) {
                    /** @type {?} */
                    let element = dataProvider[j];
                    element.visible = true;
                    if (element == this.gridVisibleItems[i]) {
                        dataProvider[j] = element;
                    }
                }
            }
        }
        return dataProvider;
    }
    /**
     * @param {?} e
     * @return {?}
     */
    saveGridVisibleState(e) {
        /** @type {?} */
        let visibleItems = null;
        this.gridVisibleItems = [];
        visibleItems = this.deepCopy(this.lastRecievedJSON.alert.grid.rows.row).filter((/**
         * @param {?} x
         * @return {?}
         */
        x => x.visible == true));
        for (var i = 0; i < visibleItems.length; i++) {
            this.gridVisibleItems[i] = visibleItems[i];
        }
    }
    /**
     * saveGridOpenState
     * @param {?} newXML
     * used to save opened grid items
     * @return {?}
     */
    saveGridOpenState(newXML) {
        this.gridOpenedItems = [];
        if (newXML) {
            this.gridOpenedItems = this.deepCopy(newXML).filter((/**
             * @param {?} x
             * @return {?}
             */
            x => x.expand.content == "Y" && x.expand.opened == true));
        }
    }
    /**
     * @param {?} mainObj
     * @return {?}
     */
    deepCopy(mainObj) {
        /** @type {?} */
        const objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        let key;
        for (key in mainObj) {
            objCopy[key] = mainObj[key]; // copies each property to the objCopy object
        }
        return objCopy;
    }
    /**
     * openGridItems
     *
     * @private
     * @param {?} dataProvider
     * @return {?}
     */
    openGridItems(dataProvider) {
        if (dataProvider) {
            if (!(dataProvider instanceof Array)) {
                /** @type {?} */
                var array = [];
                array[0] = dataProvider;
                dataProvider = array;
            }
            if (this.gridOpenedItems && this.gridOpenedItems.length > 0) {
                for (let i = 0; i < this.gridOpenedItems.length; i++) {
                    if (dataProvider.filter((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content)).length > 0) {
                        if (dataProvider.filter((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content))[i]) {
                            dataProvider.filter((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => x.entity.haschildren == true && x.entity.isopen == false && x.entity.content.toString() == this.gridOpenedItems[i].entity.content))[i].expand.opened = true;
                            for (let j = 0; j < dataProvider.filter((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => x.entity.content.toString() == this.gridOpenedItems[i].entity.content)).length; j++) {
                                dataProvider.filter((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => x.entity.content.toString() == this.gridOpenedItems[i].entity.content))[j].visible = true;
                            }
                        }
                    }
                }
            }
        }
        return dataProvider;
    }
    /**
     * @param {?} total
     * @return {?}
     */
    setTreeTotals(total) {
        this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null) + " (" + total + ") ";
    }
}
SwtSummary.decorators = [
    { type: Component, args: [{
                selector: 'SwtSummary',
                template: `        
        <VBox #mainHGroup id="mainHGroup"  width="100%" height="100%">
            <HDividedBox id="divBox" #divBox  width="100%" height="90%">
                <VBox id="treeContainer" #treeContainer width="40%" height="100%" class="left">
                    <SwtLabel  id="treeTitle" #treeTitle
                               fontWeight="normal"
                               height="8%"
                               paddingBottom="10"
                               paddingLeft="5">
                    </SwtLabel>
                    <CustomTree id="tree" #tree
                                width="100%"
                                height="92%"
                                doubleClickEnabled="true"
                                (keyDown)="treeKeyDownHandler($event)"
                                (itemDoubleClick)="treeItemDoubleClickHandler($event)">
                    </CustomTree>
                </VBox>
                <VBox height="100%"  width="60%" id="gridContainer" #gridContainer  class="right">
                    <SwtLabel id="gridTitle"
                              #gridTitle
                              height="8%"
                              fontWeight="normal"
                              paddingBottom="10">
                    </SwtLabel>
                    <SwtCanvas id="customGrid" #customGrid width="100%" height="92%" borderStyle="solid" cornerRadius="4" dropShadowEnabled="true" borderColor="#f9f9f9" >
                        <SwtCommonGrid 
                                (onFilterChanged)="saveGridVisibleState($event)"
                                id="summaryGrid" 
                                #summaryGrid 
                                width="100%" 
                                height="100%">
                        </SwtCommonGrid>
                    </SwtCanvas>
                </VBox>
            </HDividedBox>
            <HBox width="100%" height="10%"
                  horizontalAlign="right"
                  paddingTop="6"
                  paddingRight="10">
                <SwtLabel id="lastRanLbl"  #lastRanLbl  fontWeight="normal" visible="false">
                </SwtLabel>
            </HBox>
        </VBox>
  `
            }] }
];
/** @nocollapse */
SwtSummary.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
SwtSummary.propDecorators = {
    styleName: [{ type: Input, args: ['styleName',] }],
    IDField: [{ type: Input, args: ['IDField',] }],
    expandFirstLoad: [{ type: Input, args: ['expandFirstLoad',] }],
    gridTitle: [{ type: ViewChild, args: ['gridTitle',] }],
    treeTitle: [{ type: ViewChild, args: ['treeTitle',] }],
    lastRanLbl: [{ type: ViewChild, args: ['lastRanLbl',] }],
    divBox: [{ type: ViewChild, args: ["divBox",] }],
    tree: [{ type: ViewChild, args: ['tree',] }],
    mainHGroup: [{ type: ViewChild, args: ['mainHGroup',] }],
    treeContainer: [{ type: ViewChild, args: ['treeContainer',] }],
    customGrid: [{ type: ViewChild, args: ['customGrid',] }],
    summaryGrid: [{ type: ViewChild, args: ['summaryGrid',] }],
    gridContainer: [{ type: ViewChild, args: ['gridContainer',] }]
};
if (false) {
    /** @type {?} */
    SwtSummary.prototype.styleName;
    /** @type {?} */
    SwtSummary.prototype.IDField;
    /** @type {?} */
    SwtSummary.prototype.expandFirstLoad;
    /** @type {?} */
    SwtSummary.prototype.gridTitle;
    /** @type {?} */
    SwtSummary.prototype.treeTitle;
    /** @type {?} */
    SwtSummary.prototype.lastRanLbl;
    /** @type {?} */
    SwtSummary.prototype.divBox;
    /** @type {?} */
    SwtSummary.prototype.tree;
    /** @type {?} */
    SwtSummary.prototype.mainHGroup;
    /** @type {?} */
    SwtSummary.prototype.treeContainer;
    /** @type {?} */
    SwtSummary.prototype.customGrid;
    /** @type {?} */
    SwtSummary.prototype.summaryGrid;
    /** @type {?} */
    SwtSummary.prototype.gridContainer;
    /**
     * JSON Objects
     *
     * @type {?}
     * @private
     */
    SwtSummary.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.lastRecievedJSON;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.prevRecievedJSON;
    /**
     * Communication Objects
     *
     * @type {?}
     */
    SwtSummary.prototype.baseURL;
    /** @type {?} */
    SwtSummary.prototype.actionMethod;
    /** @type {?} */
    SwtSummary.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.facilityAccess;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.supportAllCurrency;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.supportAllEntity;
    /**
     * Summary Tree Objects
     *
     * @type {?}
     */
    SwtSummary.prototype.treeOpenedItems;
    /** @type {?} */
    SwtSummary.prototype.treeClosedItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.gridOpenedItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.gridVisibleItems;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.verticalScrollPosition;
    /** @type {?} */
    SwtSummary.prototype.lastSelectedItem;
    /** @type {?} */
    SwtSummary.prototype.currentDivPos;
    /** @type {?} */
    SwtSummary.prototype.facilityId;
    /** @type {?} */
    SwtSummary.prototype.facilityName;
    /** @type {?} */
    SwtSummary.prototype.useGeneric;
    /** @type {?} */
    SwtSummary.prototype.scenarioTitle;
    /** @type {?} */
    SwtSummary.prototype.selectedscenario;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.resetFilter;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.columnData;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtSummary.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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