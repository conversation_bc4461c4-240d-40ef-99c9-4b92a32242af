/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input } from '@angular/core';
import { SwtTextArea } from "./swt-text-area.component";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtRichTextEditor = /** @class */ (function (_super) {
    tslib_1.__extends(SwtRichTextEditor, _super);
    function SwtRichTextEditor() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.title = "";
        return _this;
    }
    SwtRichTextEditor.decorators = [
        { type: Component, args: [{
                    selector: 'SwtRichTextEditor',
                    template: "\n     <div class=\"tinymce-editor-container\">\n      <div class=\"tinymce-editor-header\">\n          <h6><b>{{ title }}</b></h6>\n      </div>\n      <div class=\"tinymce-editor-body\">\n         <textarea id=\"{{elementId}}\"></textarea>\n      </div>\n    </div>\n    \n        \n    ",
                    styles: ["\n  "]
                }] }
    ];
    SwtRichTextEditor.propDecorators = {
        title: [{ type: Input, args: ['title',] }]
    };
    return SwtRichTextEditor;
}(SwtTextArea));
export { SwtRichTextEditor };
if (false) {
    /** @type {?} */
    SwtRichTextEditor.prototype.title;
}
//# sourceMappingURL=data:application/json;base64,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