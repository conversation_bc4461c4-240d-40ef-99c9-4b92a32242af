/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
/** @type {?} */
var $ = require('jquery');
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
var 
//@dynamic
CheckBoxEditor = /** @class */ (function () {
    /*------------------------------------------ FUNCTIONS -------------------------------------------------------------------*/
    function CheckBoxEditor(args) {
        this.args = args;
        /*------------------------------------------ Private PARAMETERS ---------------------------------------------------------*/
        this.isBool = false; //isBool : variable to test the checkbox's value type (boolean or string)
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.row_index = -1;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.logger = new Logger('CheckBoxEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * init: function to be loaded once the item render is activated by the first click on the cell.
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * init: function to be loaded once the item render is activated by the first click on the cell.
     * @return {?}
     */
    CheckBoxEditor.prototype.init = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * init: function to be loaded once the item render is activated by the first click on the cell.
     * @return {?}
     */
    function () {
        this.logger.info('Method [init] -START- enabledFlag :', this.enableFlag);
        // First : create the Dom element.
        if (this.showHideCells) {
            this.$input = $("<input  type=\"checkbox\"  " + ((this.enableFlag == false) ? 'disabled' : '') + "  class=\"editor-checkbox\"    />");
        }
        else {
            this.$input = $("");
        }
        $(this.args.container).empty().append(this.$input);
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        this.row_index = -1;
        this.logger.info('Method [init] -END-  ');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * loadValue : loading the value from the data provider into the input.
     * @param item : the selected item
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * loadValue : loading the value from the data provider into the input.
     * @param {?} item : the selected item
     * @return {?}
     */
    CheckBoxEditor.prototype.loadValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * loadValue : loading the value from the data provider into the input.
     * @param {?} item : the selected item
     * @return {?}
     */
    function (item) {
        this.logger.info('Method [loadValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            this.defaultValue = item[this.args.column.field];
            this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']] != undefined ? this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field] : ""; // test if the loaded value is boolean (true/false) or string (N/Y)
            if (this.commonGrid.selectable) {
                if (this.defaultValue == 'true' || this.defaultValue == 'false' || this.defaultValue == true || this.defaultValue == false) {
                    this.isBool = true;
                }
                if (this.defaultValue == false || this.defaultValue === 'false' || this.defaultValue === 'N') {
                    this.$input.prop('checked', false);
                }
                else if (this.defaultValue == true || this.defaultValue === 'true' || this.defaultValue === 'Y') {
                    this.$input.prop('checked', true);
                }
            }
        }
        this.logger.info('Method [loadValue] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * serializeValue : return the state of the checkbox (checked or not)
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * serializeValue : return the state of the checkbox (checked or not)
     * @return {?}
     */
    CheckBoxEditor.prototype.serializeValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * serializeValue : return the state of the checkbox (checked or not)
     * @return {?}
     */
    function () {
        var _this = this;
        this.logger.info('Method [serializeValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            /** @type {?} */
            var item_1 = this.args.item;
            //For the first click on the render , the CheckBox will change the value within the opposite precede state.
            if (this.enableFlag == true) {
                this.$input.prop('checked', !this.$input.prop('checked'));
            }
            //CheckBox's Event change handler
            this.$input.change((/**
             * @return {?}
             */
            function () {
                if (_this.enableFlag == true) {
                    if (_this.$input.prop('checked') === true && _this.isBool) {
                        item_1[_this.args.column.field] = 'true';
                        item_1.slickgrid_rowcontent[_this.args.column.field].content = "true";
                    }
                    else if (_this.$input.prop('checked') === true && !_this.isBool) {
                        item_1[_this.args.column.field] = 'Y';
                        item_1.slickgrid_rowcontent[_this.args.column.field].content = 'Y';
                    }
                    else if (_this.$input.prop('checked') === false && _this.isBool) {
                        item_1[_this.args.column.field] = 'false';
                        item_1.slickgrid_rowcontent[_this.args.column.field].content = "false";
                    }
                    else if (_this.$input.prop('checked') === false && !_this.isBool) {
                        item_1[_this.args.column.field] = 'N';
                        item_1.slickgrid_rowcontent[_this.args.column.field].content = 'N';
                    }
                    _this.commonGrid.columnSelectChanged(_this.args.item);
                    if (_this.commonGrid.selectable /* && this.row_index  == "0" && this.args.item['id'] == "0"*/) {
                        setTimeout((/**
                         * @return {?}
                         */
                        function () {
                            /** @type {?} */
                            var target = {
                                name: _this.args.column.name,
                                field: _this.args.column.field,
                                editor: (_this.args.column.editor != null && _this.args.column.editor != undefined) ? _this.args.column.editor.name : null,
                                formatter: (_this.args.column.formatter != null && _this.args.column.formatter != undefined) ? _this.args.column.formatter.name : null,
                                data: _this.args.item
                            };
                            /** @type {?} */
                            var ListEvent = {
                                rowIndex: _this.args.item['id'],
                                cellIndex: _this.args.column['columnorder'],
                                columnIndex: _this.args.column['columnorder'],
                                target: target
                            };
                            _this.commonGrid.ITEM_CLICK.emit(ListEvent);
                        }), 0);
                    }
                    // -- check if changed
                    _this.CRUD_CHANGES_DATA = _this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                    ;
                    if (_this.CRUD_CHANGES_DATA != undefined && _this.CRUD_CHANGES_DATA != null) {
                        _this.originalDefaultValue = _this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? _this.CRUD_CHANGES_DATA['crud_original_data'][_this.args.column.field] : null;
                    }
                    /*console.log('===> this.originalDefaultValue :',this.originalDefaultValue)
                    console.log('===> this.defaultValue :',this.defaultValue)
                    console.log('===>  this.args.item['+this.args.column.field+']) :', this.args.item[this.args.column.field])
                    console.log('===>  this.originalDefaultValue == this.args.item[this.args.column.field] :', (this.originalDefaultValue == this.args.item[this.args.column.field]))
                    */
                    if ((_this.originalDefaultValue == null && _this.defaultValue != _this.args.item[_this.args.column.field]) || ((_this.originalDefaultValue != null) && (_this.originalDefaultValue != _this.args.item[_this.args.column.field]))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (_this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = _this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return ((x.crud_data.id == _this.args.item.id) && (x.crud_operation == "I")); }));
                            if (crudInsert != undefined && crudInsert[_this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (var key in _this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = _this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (var key in _this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, _this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[_this.args.column.field] = _this.defaultValue;
                            original_row['slickgrid_rowcontent'][_this.args.column.field] = { 'content': _this.defaultValue };
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: _this.args.item.id,
                                columnIndex: _this.args.column.columnorder,
                                new_row: _this.args.item,
                                original_row: original_row,
                                changedColumn: _this.args.column.field,
                                oldValue: _this.defaultValue,
                                newValue: _this.args.item[_this.args.column.field]
                            };
                            _this.commonGrid.spyChanges({ field: _this.args.column.field });
                            _this.commonGrid.updateCrud(updatedObject);
                            SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                            //ITEM_CHANGED
                            /** @type {?} */
                            var event = {
                                rowIndex: _this.args.item.id,
                                target: _this.args.column.editor.name,
                                dataField: _this.args.column.field,
                                listData: tslib_1.__assign({}, updatedObject)
                            };
                            _this.commonGrid.ITEM_CHANGED.emit(event);
                        }
                    }
                    else if (_this.originalDefaultValue == _this.args.item[_this.args.column.field]) {
                        /** @type {?} */
                        var crudChange = _this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                        /** @type {?} */
                        var ch = String("U(" + _this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = _this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                _this.commonGrid.changes.remove(_this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        //- Do not emit SpyNoChanges on the grid unless there is other changes.
                        if (_this.commonGrid.changes.getValues().length == 0) {
                            _this.commonGrid.spyNoChanges({ field: _this.args.column.field });
                        }
                        //- if the column is displayed as checkbox , then we have to emit ITEM_CHANGE event even there is no changed within the current row , because this event will detect the check/uncheck of the header column.
                        if (_this.args.column.checkBoxVisibility) {
                            _this.commonGrid.ITEM_CHANGED.emit(null);
                        }
                        SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                    }
                    if (_this.row_index == -1 && _this.args.item.id == "0") {
                        _this.row_index = _this.args.item.id;
                    }
                }
            }));
            this.$input.change();
        }
        this.logger.info('Method [serializeValue] -END-');
        return this.$input.prop('checked');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * focus : the input gets the focus in.
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * focus : the input gets the focus in.
     * @return {?}
     */
    CheckBoxEditor.prototype.focus = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * focus : the input gets the focus in.
     * @return {?}
     */
    function () {
        this.logger.info('Method [focus] -START/END-');
        this.$input.focus();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * applyValue : will update the value of the checkbox in the data provider (N/Y) or (true/false).
     * @param item : the selected current item
     * @param state : the state of the checkbox (true/false)
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * applyValue : will update the value of the checkbox in the data provider (N/Y) or (true/false).
     * @param {?} item : the selected current item
     * @param {?} state : the state of the checkbox (true/false)
     * @return {?}
     */
    CheckBoxEditor.prototype.applyValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * applyValue : will update the value of the checkbox in the data provider (N/Y) or (true/false).
     * @param {?} item : the selected current item
     * @param {?} state : the state of the checkbox (true/false)
     * @return {?}
     */
    function (item, state) {
        this.logger.info('Method [applyValue] -START/END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * isValueChanged : dispatch the change event.
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * isValueChanged : dispatch the change event.
     * @return {?}
     */
    CheckBoxEditor.prototype.isValueChanged = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * isValueChanged : dispatch the change event.
     * @return {?}
     */
    function () {
        this.logger.info('Method [isValueChanged] -START/END-');
        return false;
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * validate :
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * validate :
     * @return {?}
     */
    CheckBoxEditor.prototype.validate = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * validate :
     * @return {?}
     */
    function () {
        this.logger.info('Method [validate] -START/END-');
        return { valid: true, msg: null };
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * destroy : Called once the focus is out and the item render become hidden.
     */
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * destroy : Called once the focus is out and the item render become hidden.
     * @return {?}
     */
    CheckBoxEditor.prototype.destroy = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * destroy : Called once the focus is out and the item render become hidden.
     * @return {?}
     */
    function () {
        this.logger.info('Method [destroy] -START/END-');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
    };
    return CheckBoxEditor;
}());
//@dynamic
export { CheckBoxEditor };
if (false) {
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.isBool;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.row_index;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    CheckBoxEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    CheckBoxEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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