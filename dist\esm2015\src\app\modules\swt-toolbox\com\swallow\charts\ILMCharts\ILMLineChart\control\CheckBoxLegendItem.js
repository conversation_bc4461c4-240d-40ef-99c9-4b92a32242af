/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { SwtCheckBox } from '../../../../controls/swt-checkbox.component';
import { SeriesHighlightEvent, LegendItemChangedEvent } from "../../../../events/swt-events.module";
import { HBox } from '../../../../controls/swt-hbox.component';
export class CheckBoxLegendItem extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} cd
     */
    constructor(elem, commonService, cd) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.cd = cd;
        // LegendItemChangedEvent
        this.styleClassMap = {
            'DOTTED_SEGMENT_YELLOW': 'bg-DOTTED_SEGMENT_YELLOW',
            'CONT_AREA_ANTIQUE_WHITE': 'bg-CONT_AREA_ANTIQUE_WHITE',
            'CONT_AREA_APRICOT_PEACH': 'bg-CONT_AREA_APRICOT_PEACH',
            'CONT_AREA_BLACK': 'bg-CONT_AREA_BLACK',
            'CONT_AREA_BLIZZARD_BLUE': 'bg-CONT_AREA_BLIZZARD_BLUE',
            'CONT_AREA_BLUE': 'bg-CONT_AREA_BLUE',
            'CONT_AREA_COTTON_CANDY': 'bg-CONT_AREA_COTTON_CANDY',
            'CONT_AREA_DARK_SALMON': 'bg-CONT_AREA_DARK_SALMON',
            'CONT_AREA_GREEN': 'bg-CONT_AREA_GREEN',
            'CONT_AREA_GREY': 'bg-CONT_AREA_GREY',
            'CONT_AREA_INDIAN_RED': 'bg-CONT_AREA_INDIAN_RED',
            'CONT_AREA_LIGHT_BLUE': 'bg-CONT_AREA_LIGHT_BLUE',
            'CONT_AREA_LIGHT_CYAN': 'bg-CONT_AREA_LIGHT_CYAN',
            'CONT_AREA_LIGHT_GREEN': 'bg-CONT_AREA_LIGHT_GREEN',
            'CONT_AREA_LIGHT_GREY': 'bg-CONT_AREA_LIGHT_GREY',
            'CONT_AREA_LIME': 'bg-CONT_AREA_LIME',
            'CONT_AREA_MAGENTA': 'bg-CONT_AREA_MAGENTA',
            'CONT_AREA_NAVAJO_WHITE': 'bg-CONT_AREA_NAVAJO_WHITE',
            'CONT_AREA_ORANGE': 'bg-CONT_AREA_ORANGE',
            'CONT_AREA_PEACH_PUFF': 'bg-CONT_AREA_PEACH_PUFF',
            'CONT_AREA_PINK': 'bg-CONT_AREA_PINK',
            'CONT_AREA_PURPLE': 'bg-CONT_AREA_PURPLE',
            'CONT_AREA_RED': 'bg-CONT_AREA_RED',
            'CONT_AREA_ROSE_FOG': 'bg-CONT_AREA_ROSE_FOG',
            'CONT_AREA_STEEL_BLUE': 'bg-CONT_AREA_STEEL_BLUE',
            'CONT_AREA_VIOLET': 'bg-CONT_AREA_VIOLET',
            'CONT_AREA_YELLOW': 'bg-CONT_AREA_YELLOW',
            'CONT_SEGMENT_BLACK': 'bg-CONT_SEGMENT_BLACK',
            'CONT_SEGMENT_BLUE': 'bg-CONT_SEGMENT_BLUE',
            'CONT_SEGMENT_BOLD_RED': 'bg-CONT_SEGMENT_BOLD_RED',
            'CONT_SEGMENT_GREEN': 'bg-CONT_SEGMENT_GREEN',
            'CONT_SEGMENT_MAGENTA': 'bg-CONT_SEGMENT_MAGENTA',
            'CONT_SEGMENT_ORANGE': 'bg-CONT_SEGMENT_ORANGE',
            'CONT_SEGMENT_PURPLE': 'bg-CONT_SEGMENT_PURPLE',
            'CONT_SEGMENT_RED': 'bg-CONT_SEGMENT_RED',
            'CONT_SEGMENT_YELLOW': 'bg-CONT_SEGMENT_YELLOW',
            'DASHED_AQUA_AREA': 'bg-DASHED_AQUA_AREA',
            'DASHED_AREA_PERANO': 'bg-DASHED_AREA_PERANO',
            'DASHED_BLUE_AREA': 'bg-DASHED_BLUE_AREA',
            'DASHED_CORAL_AREA': 'bg-DASHED_CORAL_AREA',
            'DASHED_DEEP_PINK_AREA': 'bg-DASHED_DEEP_PINK_AREA',
            'DASHED_GOLDEN_ROD_AREA': 'bg-DASHED_GOLDEN_ROD_AREA',
            'DASHED_GREEN_AREA': 'bg-DASHED_GREEN_AREA',
            'DASHED_SEGMENT_BLACK': 'bg-DASHED_SEGMENT_BLACK',
            'DASHED_SEGMENT_BLUE': 'bg-DASHED_SEGMENT_BLUE',
            'DASHED_SEGMENT_GREEN': 'bg-DASHED_SEGMENT_GREEN',
            'DASHED_SEGMENT_MAGENTA': 'bg-DASHED_SEGMENT_MAGENTA',
            'DASHED_SEGMENT_ORANGE': 'bg-DASHED_SEGMENT_ORANGE',
            'DASHED_SEGMENT_PURPLE': 'bg-DASHED_SEGMENT_PURPLE',
            'DASHED_SEGMENT_RED': 'bg-DASHED_SEGMENT_RED',
            'DASHED_SEGMENT_YELLOW': 'bg-DASHED_SEGMENT_YELLOW',
            'DOTTED_GREEN_YELLOW_AREA': 'bg-DOTTED_GREEN_YELLOW_AREA',
            'DOTTED_INDIAN_RED_AREA': 'bg-DOTTED_INDIAN_RED_AREA',
            'DOTTED_MAGENTA_AREA': 'bg-DOTTED_MAGENTA_AREA',
            'DOTTED_SEGMENT_BLACK': 'bg-DOTTED_SEGMENT_BLACK',
            'DOTTED_SEGMENT_BLUE': 'bg-DOTTED_SEGMENT_BLUE',
            'DOTTED_SEGMENT_GREEN': 'bg-DOTTED_SEGMENT_GREEN',
            'DOTTED_SEGMENT_MAGENTA': 'bg-DOTTED_SEGMENT_MAGENTA',
            'DOTTED_SEGMENT_ORANGE': 'bg-DOTTED_SEGMENT_ORANGE',
            'DOTTED_SEGMENT_PURPLE': 'bg-DOTTED_SEGMENT_PURPLE',
            'DOTTED_SEGMENT_RED': 'bg-DOTTED_SEGMENT_RED',
        };
        this.liveValue = '';
        this._yField = '';
        this._selected = true;
        this._highlight = false;
        this._seriesStyle = '';
        this.created = false;
    }
    /**
     * @return {?}
     */
    getLiveValue() {
        return this.liveValue;
    }
    /**
     * @return {?}
     */
    get highlight() {
        return this._highlight;
    }
    //BBCCDD
    /**
     * @param {?} value
     * @return {?}
     */
    set highlight(value) {
        this._highlight = value;
        if (this._highlight) {
            this.hboxContainer.backGroundColor = '#BBCCDD';
        }
        else {
            this.hboxContainer.backGroundColor = 'transparent';
        }
    }
    /**
     * @return {?}
     */
    get seriesStyle() {
        return this._seriesStyle;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesStyle(value) {
        /** @type {?} */
        const newStyle = this.styleClassMap[value];
        if (newStyle) {
            if (this.created) {
                if (this._seriesStyle) {
                    /** @type {?} */
                    const prevStyle = this.styleClassMap[this._seriesStyle];
                    this.square.nativeElement.classList.remove(prevStyle);
                }
                this.square.nativeElement.classList.add(newStyle);
            }
            this._seriesStyle = value;
        }
        // styleClassMap
    }
    /**
     * @param {?} event
     * @return {?}
     */
    checkboxChanged(event) {
        this.selected = this.checkBox.selected;
        /** @type {?} */
        var dto = {};
        dto.selected = this.selected;
        dto.yField = this.yField;
        if ($(this.elem.nativeElement).closest('.legendsDivider')) {
            dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
        }
        // // Dispatch the event so that legends will be highlightened as well
        LegendItemChangedEvent.emit(dto);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.labelValue.nativeElement.textContent = this.liveValue;
        /** @type {?} */
        const newStyle = this.styleClassMap[this._seriesStyle];
        if (newStyle) {
            this.square.nativeElement.classList.add(newStyle);
        }
        // this.checkBox.selected = this.selected;
        this.created = true;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set yField(value) {
        this._yField = value;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
    /**
     * @return {?}
     */
    get yField() {
        return this._yField;
    }
    /**
     * @return {?}
     */
    get selected() {
        return this._selected;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selected(value) {
        this._selected = value;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
        // this.checkBox.selected = value;
    }
    /**
     * On click on the legend item label highlight the label and the line chart
     *
     * @param {?} event
     * @return {?}
     */
    legendItemClicked(event) {
        // Highlight the legend and the line chart when the user click on the Label legend
        /** @type {?} */
        var dto = {};
        this.highlight = !this.highlight;
        dto.highligh = this.highlight;
        dto.yField = this.yField;
        if ($(this.elem.nativeElement).closest('.legendsDivider')) {
            dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
        }
        // // Dispatch the event so that legends will be highlightened as well
        SeriesHighlightEvent.emit(dto);
    }
}
CheckBoxLegendItem.decorators = [
    { type: Component, args: [{
                selector: 'CheckBoxLegendItem',
                template: `
         <HBox paddingLeft="5" #hboxContainer  width="100%" height="26"> 
         <SwtCheckBox  selected='{{selected}}' (change)="checkboxChanged($event)" class='checkBoxObject'  #checkBox></SwtCheckBox>
            <div    #square class='square' (click)='legendItemClicked($event)'></div>
            <div class='labelValue' #labelValue (click)='legendItemClicked($event)' > {{ getLiveValue() }}</div>
        </HBox>
        `,
                changeDetection: ChangeDetectionStrategy.OnPush,
                styles: [` 
            .checkBoxObject{
                margin-right : 0px !important;
            }
            .labelValue{
                width :1px;
                padding-top:2px;
                padding-left:5px;
                position:relative;
                font-size:11px;
                cursor: default;
                white-space: nowrap;
            }
            .square{
                margin-right:2px !important;
                margin-top:3px;
                cursor: default;
            }
            
            .bg-DOTTED_SEGMENT_YELLOW {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -10px;
            }
            .bg-CONT_AREA_ANTIQUE_WHITE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -10px;
            }
            .bg-CONT_AREA_APRICOT_PEACH {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -46px;
            }
            .bg-CONT_AREA_BLACK {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -46px;
            }
            .bg-CONT_AREA_BLIZZARD_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -10px;
            }
            .bg-CONT_AREA_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -46px;
            }
            .bg-CONT_AREA_COTTON_CANDY {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -82px;
            }
            .bg-CONT_AREA_DARK_SALMON {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -82px;
            }
            .bg-CONT_AREA_GREEN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -82px;
            }
            .bg-CONT_AREA_GREY {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -118px;
            }
            .bg-CONT_AREA_INDIAN_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -118px;
            }
            .bg-CONT_AREA_LIGHT_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -118px;
            }
            .bg-CONT_AREA_LIGHT_CYAN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -10px;
            }
            .bg-CONT_AREA_LIGHT_GREEN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -46px;
            }
            .bg-CONT_AREA_LIGHT_GREY {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -82px;
            }
            .bg-CONT_AREA_LIME {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -118px;
            }
            .bg-CONT_AREA_MAGENTA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -154px;
            }
            .bg-CONT_AREA_NAVAJO_WHITE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -154px;
            }
            .bg-CONT_AREA_ORANGE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -154px;
            }
            .bg-CONT_AREA_PEACH_PUFF {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -154px;
            }
            .bg-CONT_AREA_PINK {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -10px;
            }
            .bg-CONT_AREA_PURPLE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -46px;
            }
            .bg-CONT_AREA_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -82px;
            }
            .bg-CONT_AREA_ROSE_FOG {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -118px;
            }
            .bg-CONT_AREA_STEEL_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -154px;
            }
            .bg-CONT_AREA_VIOLET {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -190px;
            }
            .bg-CONT_AREA_YELLOW {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -190px;
            }
            .bg-CONT_SEGMENT_BLACK {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -190px;
            }
            .bg-CONT_SEGMENT_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -190px;
            }
            .bg-CONT_SEGMENT_BOLD_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -190px;
            }
            .bg-CONT_SEGMENT_GREEN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -10px;
            }
            .bg-CONT_SEGMENT_MAGENTA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -46px;
            }
            .bg-CONT_SEGMENT_ORANGE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -82px;
            }
            .bg-CONT_SEGMENT_PURPLE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -118px;
            }
            .bg-CONT_SEGMENT_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -154px;
            }
            .bg-CONT_SEGMENT_YELLOW {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -190px;
            }
            .bg-DASHED_AQUA_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -226px;
            }
            .bg-DASHED_AREA_PERANO {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -226px;
            }
            .bg-DASHED_BLUE_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -226px;
            }
            .bg-DASHED_CORAL_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -226px;
            }
            .bg-DASHED_DEEP_PINK_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -226px;
            }
            .bg-DASHED_GOLDEN_ROD_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -226px;
            }
            .bg-DASHED_GREEN_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -262px;
            }
            .bg-DASHED_SEGMENT_BLACK {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -262px;
            }
            .bg-DASHED_SEGMENT_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -262px;
            }
            .bg-DASHED_SEGMENT_GREEN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -262px;
            }
            .bg-DASHED_SEGMENT_MAGENTA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -262px;
            }
            .bg-DASHED_SEGMENT_ORANGE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -260px -262px;
            }
            .bg-DASHED_SEGMENT_PURPLE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -10px;
            }
            .bg-DASHED_SEGMENT_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -46px;
            }
            .bg-DASHED_SEGMENT_YELLOW {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -82px;
            }
            .bg-DOTTED_GREEN_YELLOW_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -118px;
            }
            .bg-DOTTED_INDIAN_RED_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -154px;
            }
            .bg-DOTTED_MAGENTA_AREA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -190px;
            }
            .bg-DOTTED_SEGMENT_BLACK {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -226px;
            }
            .bg-DOTTED_SEGMENT_BLUE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -310px -262px;
            }
            .bg-DOTTED_SEGMENT_GREEN {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -298px;
            }
            .bg-DOTTED_SEGMENT_MAGENTA {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -298px;
            }
            .bg-DOTTED_SEGMENT_ORANGE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -110px -298px;
            }
            .bg-DOTTED_SEGMENT_PURPLE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -160px -298px;
            }
            .bg-DOTTED_SEGMENT_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -298px;
            }
        `]
            }] }
];
/** @nocollapse */
CheckBoxLegendItem.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: ChangeDetectorRef }
];
CheckBoxLegendItem.propDecorators = {
    hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
    square: [{ type: ViewChild, args: ['square',] }],
    checkBox: [{ type: ViewChild, args: ['checkBox',] }],
    labelValue: [{ type: ViewChild, args: ['labelValue',] }],
    liveValue: [{ type: Input, args: ['liveValue',] }],
    highlight: [{ type: Input, args: ['highlight',] }],
    yField: [{ type: Input, args: ['highlight',] }],
    selected: [{ type: Input, args: ['selected',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.square;
    /** @type {?} */
    CheckBoxLegendItem.prototype.checkBox;
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.labelValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.styleClassMap;
    /** @type {?} */
    CheckBoxLegendItem.prototype.liveValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._yField;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._highlight;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._seriesStyle;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.created;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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