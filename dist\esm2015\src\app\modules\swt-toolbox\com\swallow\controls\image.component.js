/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, Input } from '@angular/core';
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Container } from '../containers/swt-container.component';
import { genericEvent } from '../events/swt-events.module';
import { CommonService } from '../utils/common.service';
export class SwtImage extends Container {
    /**
     * @param {?} element
     */
    constructor(element) {
        super(element, CommonService.instance);
        this.element = element;
        this.source = "";
        this.id = "";
        this.width = 15;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        $(this.element.nativeElement.children[0])
            .width(this.width)
            .height(this.height);
        $(this.element.nativeElement.children[0]).on("mouseover", (/**
         * @param {?} evt
         * @return {?}
         */
        (evt) => {
            if (this.eventlist[genericEvent.MOUSE_OVER]) {
                this.eventlist[genericEvent.MOUSE_OVER]({ event: evt, target: this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mouseup", (/**
         * @param {?} evt
         * @return {?}
         */
        (evt) => {
            if (this.eventlist[genericEvent.MOUSE_UP]) {
                this.eventlist[genericEvent.MOUSE_UP]({ event: evt, target: this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mousedown", (/**
         * @param {?} evt
         * @return {?}
         */
        (evt) => {
            if (this.eventlist[genericEvent.MOUSE_DOWN]) {
                this.eventlist[genericEvent.MOUSE_DOWN]({ event: evt, target: this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mouseleave", (/**
         * @param {?} evt
         * @return {?}
         */
        (evt) => {
            if (this.eventlist[genericEvent.MOUSE_LEAVE]) {
                this.eventlist[genericEvent.MOUSE_LEAVE]({ event: evt, target: this });
            }
        }));
    }
}
SwtImage.decorators = [
    { type: Component, args: [{
                selector: 'SwtImage',
                template: `
    <img [src]="source" [id]="id">
  `
            }] }
];
/** @nocollapse */
SwtImage.ctorParameters = () => [
    { type: ElementRef }
];
SwtImage.propDecorators = {
    source: [{ type: Input, args: ["source",] }],
    id: [{ type: Input, args: ["id",] }],
    width: [{ type: Input, args: ["width",] }],
    height: [{ type: Input, args: ["height",] }]
};
if (false) {
    /** @type {?} */
    SwtImage.prototype.source;
    /** @type {?} */
    SwtImage.prototype.id;
    /** @type {?} */
    SwtImage.prototype.width;
    /** @type {?} */
    SwtImage.prototype.height;
    /**
     * @type {?}
     * @private
     */
    SwtImage.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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