import { HashMap } from './HashMap.service';
import { XML } from '../xmlhandler/swt-xml.service';
export declare class CommonUtil {
    private static log;
    private static changes;
    private static spyTimers;
    static CDBegin: String;
    static CDEnd: String;
    private static ignoredObjects;
    static SPY_TIME_INTERVAL: Number;
    private static gridImages;
    static USER_UPLOAD_PATH: String;
    constructor();
    /**
     * format a Date from format1 to format2
     *
     **/
    static formatDateFromString(value: string, patternIn: string, patternOut?: string, lang?: string): string;
    /**
     * unformatAmount: Supports amountDecimal
     * */
    static unformatAmount(formattedAmount: string, amountPattern: number, amountDecimal: string): string;
    /**
     * this function is used to get the current entity Id
     * */
    static getCurrentUserId(): any;
    /**
     * Converts an Oracle ISO format into a date
     * */
    static isoToDate(value: string): Date;
    /**
     * Converts a date into Oracle ISO format
     * */
    static dateToIso(value: Date, displaySeconds?: boolean): string;
    /**
     * Converts a string to a Date object
     * */
    static dateFromString(dateString: string, pattern: string): Date;
    static parseDate(dateString: string, pattern: string): Date;
    static formatDate(date: Date, patern: string): string;
    /**
     * Create dynamic report based on query on S_REPORTS by passing program id , kvparams and report group id
     * */
    static report(programId: number, kvParams: any[], filterSortParams: any[], reportGroupId: number, type: string, moduleId: string, displayFilter?: HashMap, xmlDataSource?: XML): void;
    /**
     * this function is used to get the current module Id
     * */
    static function: any;
    static getCurrentModuleId(): string;
    /**
     * this function is used to get the current entity Id
     * */
    static getCurrentEntityId(): string;
    /**
     * method that returns a list of updated columns
     * updateOperation will have the format: U(col1)>U(col2)>...>U(coln)
     * */
    static getUpdatedColumns(updateOperation: string): any;
    /**
     * Returns the first occurrence text for a given XML and tag name
     * */
    static getTextByTagName(rowData: any, tagName: string): string;
}
