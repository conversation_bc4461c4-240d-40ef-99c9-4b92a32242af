/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
var BaseObject = /** @class */ (function () {
    function BaseObject() {
    }
    /**
     * This method is used to bind attribute to the current object.
     * @param name
     * @param value
     */
    /**
     * This method is used to bind attribute to the current object.
     * @param {?} name
     * @param {?} value
     * @return {?}
     */
    BaseObject.prototype.bindAttribute = /**
     * This method is used to bind attribute to the current object.
     * @param {?} name
     * @param {?} value
     * @return {?}
     */
    function (name, value) {
        if (name) {
            this[name] = value;
        }
    };
    /**
     * This method is used to clone the current object.
     * @param source
     */
    /**
     * This method is used to clone the current object.
     * @param {?} source
     * @return {?}
     */
    BaseObject.prototype.clone = /**
     * This method is used to clone the current object.
     * @param {?} source
     * @return {?}
     */
    function (source) {
        if (Object.prototype.toString.call(source) === '[object Array]') {
            /** @type {?} */
            var clone = [];
            for (var i = 0; i < source.length; i++) {
                clone[i] = this.clone(source[i]);
            }
            return clone;
        }
        else if (typeof (source) === "object") {
            /** @type {?} */
            var clone = {};
            for (var prop in source) {
                if (source.hasOwnProperty(prop)) {
                    clone[prop] = this.clone(source[prop]);
                }
            }
            return clone;
        }
        else {
            return source;
        }
    };
    /**
     * This method is used to rename an attribute of the current object
     * @param Oldattr
     * @param newAttr
     */
    /**
     * This method is used to rename an attribute of the current object
     * @param {?=} obj
     * @param {?=} oldAttr
     * @param {?=} newAttr
     * @return {?}
     */
    BaseObject.prototype.renameAttr = /**
     * This method is used to rename an attribute of the current object
     * @param {?=} obj
     * @param {?=} oldAttr
     * @param {?=} newAttr
     * @return {?}
     */
    function (obj, oldAttr, newAttr) {
        if (obj === void 0) { obj = null; }
        try {
            if (obj) {
                // Do nothing if the names are the same
                if (oldAttr === newAttr) {
                    return obj;
                }
                // Check for the old property name to avoid
                // a ReferenceError in strict mode.
                if (obj.hasOwnProperty(oldAttr)) {
                    obj[newAttr] = obj[oldAttr];
                    delete obj[oldAttr];
                }
                return obj;
            }
            else {
                // Do nothing if the names are the same
                if (oldAttr === newAttr) {
                    return this;
                }
                // Check for the old property name to avoid
                // a ReferenceError in strict mode.
                if (this.hasOwnProperty(oldAttr)) {
                    this[newAttr] = this[oldAttr];
                    delete this[oldAttr];
                }
                return this;
            }
        }
        catch (e) {
            console.error("renameAttr: ", e);
        }
    };
    /**
     * This method extract numbers from given string.
     * @param str
     */
    /**
     * This method extract numbers from given string.
     * @param {?} str
     * @return {?}
     */
    BaseObject.prototype.getNumberFrom = /**
     * This method extract numbers from given string.
     * @param {?} str
     * @return {?}
     */
    function (str) {
        if (typeof (str) !== "string") {
            str = str + "";
        }
        return Number(str.replace(/\D/g, ''));
    };
    return BaseObject;
}());
export { BaseObject };
//# sourceMappingURL=data:application/json;base64,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