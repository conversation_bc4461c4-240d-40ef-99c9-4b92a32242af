/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
export class SwtTotalCommonGrid extends SwtCommonGrid {
    /**
     * @param {?} el
     * @param {?} commonService
     * @param {?} autoTooltipExtension
     * @param {?} extensionUtility
     * @param {?} sharedService
     * @param {?} collectionService
     * @param {?} translate
     */
    constructor(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        super(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate);
        this.el = el;
        this.commonService = commonService;
        this.autoTooltipExtension = autoTooltipExtension;
        this.extensionUtility = extensionUtility;
        this.sharedService = sharedService;
        this.translate = translate;
        this.showHeader = false;
        this.isTotalGrid = true;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set initialColumnsToSkip(value) {
        this._initialColumnsToSkip = value;
    }
    /**
     * @return {?}
     */
    get initialColumnsToSkip() {
        return this._initialColumnsToSkip;
    }
}
SwtTotalCommonGrid.decorators = [
    { type: Component, args: [{
                selector: 'SwtTotalCommonGrid',
                template: `
    <angular-slickgrid
            #angularSlickGrid
            gridId='grid-{{id}}'
            (onDataviewCreated)="dataviewReady($event)"
            (onAngularGridCreated)="onAngularGridCreated($event)"
            [columnDefinitions]="columnDefinitions"
            [gridOptions]="gridOptions"
            gridHeight="100%"
            gridWidth="100%"
            [dataset]="dataset"
    >
    </angular-slickgrid>

`,
                providers: [
                    TranslateService,
                    ExtensionService,
                    AutoTooltipExtension,
                    ExtensionUtility,
                    SharedService,
                    CollectionService
                ],
                styles: [`
    .gridContent{
        min-width: 300px;
        height: 100%;
    }
    :host ::ng-deep .gridPane {
        overflow: auto;
        display: block;
    }
    :host ::ng-deep .slickgrid-container {
        min-height: 100%;
    }


`]
            }] }
];
/** @nocollapse */
SwtTotalCommonGrid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: AutoTooltipExtension },
    { type: ExtensionUtility },
    { type: SharedService },
    { type: CollectionService },
    { type: TranslateService }
];
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtTotalCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,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