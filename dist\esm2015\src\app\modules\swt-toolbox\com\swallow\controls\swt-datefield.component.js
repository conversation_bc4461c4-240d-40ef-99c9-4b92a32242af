/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, Renderer2, ViewChild } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from '../utils/swt-util.service';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtDateField {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this._toolTipPreviousObject = null;
        /* Private attributes */
        this._toolTip = "";
        this.firstCall = true;
        this._visibility = true;
        this.logger = null;
        this._showOnFlag = "button";
        this._change = new Function();
        //variable to hold interruptComms
        this.interrupted = false;
        /*Handling output events */
        this.openEventOutPut = new EventEmitter();
        this.closeEventOutPut = new EventEmitter();
        this.keyDownEventOutPut = new EventEmitter();
        this.changeEventOutPut = new EventEmitter();
        this.focusEventOutPut = new EventEmitter();
        this.focusOutEventOutPut = new EventEmitter();
        this.keyFocusChange = new EventEmitter();
        this._enabled = true;
        this._editable = true;
        this._text = "";
        this._showYearSelect = true;
        this._showMonthSelect = false;
        this._textAlign = "justify";
        this._formatString = "mm/dd/yy";
        this._monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        this._dayNames = ["S", "M", "T", "W", "T", "F", "S"];
        /* private variable to handle events */
        this._open = new Function();
        this._close = new Function();
        this._keyDown = new Function();
        this._focus = new Function();
        this._focusOut = new Function();
        this._selectedDate = null;
        this.logger = new Logger('SwtDatefield', commonService.httpclient);
        this.logger.debug("[ SwtDatefield ] construction");
    }
    // Input to handle datePicker tabIndex
    //---ToolTip---------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTip(value) {
        this._toolTip = value;
        if ($(this.datefield.nativeElement)) {
            $($(this.datefield.nativeElement)[0]).tooltip({
                show: { duration: 800, delay: 500 },
                open: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    $(this).removeAttr('title');
                })
            });
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set textDictionaryId(value) {
        if (value) {
            this._toolTip = SwtUtil.getPredictMessage(value);
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTipPreviousValue(value) {
        this._toolTipPreviousObject = value;
    }
    /**
     * @return {?}
     */
    get toolTipPreviousValue() {
        return this._toolTipPreviousObject;
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /* enabled getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                this._enabled = false;
            }
            else {
                this._enabled = true;
            }
        }
        else {
            this._enabled = value;
        }
        if (this.datePickerObject !== undefined) {
            this.datePickerObject.datepicker("option", "disabled", !this._enabled);
            $($(this.datePickerObject)[0]).val(this.text);
        }
    }
    /**
     * @return {?}
     */
    get editable() {
        return this._editable;
    }
    /* editable getter and setter */
    /**
     * @param {?} editable
     * @return {?}
     */
    set editable(editable) {
        if (typeof (editable) === "string") {
            if (editable === "false") {
                this._editable = false;
                $(this.elem.nativeElement.children[0]).prop("readonly", true);
            }
            else {
                this._editable;
                $(this.elem.nativeElement.children[0]).prop("readonly", false);
            }
        }
        else {
            this._editable = editable;
            $(this.elem.nativeElement.children[0]).prop("readonly", !Boolean(editable));
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /* width getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        if (value !== undefined) {
            if (value.indexOf('%') === -1) {
                this._width = value + "px";
            }
            else {
                this._width = value;
            }
        }
    }
    /**
     * @return {?}
     */
    get text() {
        return this._text;
    }
    /* text getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        try {
            if (value === undefined || value === null || value === '') {
                this.datePickerObject.datepicker('setDate', null);
                this._text = null;
            }
            else if (this.parseDate(value, this._formatString) !== null) {
                this._text = value;
                /** @type {?} */
                var queryDate = this.parseDate(value, this._formatString);
                this.datePickerObject.datepicker('setDate', queryDate);
            }
            else {
                this._text = value;
                $($(this.datePickerObject)[0]).val(value);
            }
            if (this.firstCall) {
                this.originalValue = this._text;
                this.firstCall = false;
            }
        }
        catch (error) {
            this.logger.error('set text method ', error);
        }
    }
    /**
     * @param {?} interrupted
     * @return {?}
     */
    set interruptComms(interrupted) {
        this.interrupted = interrupted;
    }
    /**
     * @return {?}
     */
    get interruptComms() {
        return this.interrupted;
    }
    /**
     * @return {?}
     */
    get showYearSelect() {
        return this._showYearSelect;
    }
    /*=============================================== Getter and Setter ====================================================*/
    /**
     * @param {?} value
     * @return {?}
     */
    set showYearSelect(value) {
        this._showYearSelect = value;
    }
    /**
     * @return {?}
     */
    get showMonthSelect() {
        return this._showMonthSelect;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} value
     * @return {?}
     */
    set showMonthSelect(value) {
        this._showMonthSelect = value;
    }
    /**
     * @return {?}
     */
    get textAlign() {
        return this._textAlign;
    }
    /* @Inputs */
    /* textAlign getter and setter */
    /**
     * @param {?} align
     * @return {?}
     */
    set textAlign(align) {
        this._textAlign = align;
    }
    /**
     * @return {?}
     */
    get formatString() {
        return this._formatString;
    }
    /**
     * override function, this function sets date format string
     *
     * @param {?} value
     * @return {?}
     */
    set formatString(value) {
        value = value.toLowerCase();
        if (value.indexOf("yyyy") !== -1) {
            value = value.toLowerCase().replace("yyyy", "yy");
        }
        if (value.indexOf("mmm") !== -1) {
            value = value.toLowerCase().replace("mmm", "M");
        }
        this._formatString = value;
        if (this.datePickerObject !== undefined) {
            this.datePickerObject.datepicker("option", "dateFormat", this._formatString);
        }
    }
    /**
     * @return {?}
     */
    get monthNames() {
        return this._monthNames;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} value
     * @return {?}
     */
    set monthNames(value) {
        this._monthNames = value;
        if (this.datePickerObject !== undefined) {
            // set datePicker monthNames
            this.datePickerObject.datepicker("option", "monthNames", value);
        }
    }
    /**
     * @return {?}
     */
    get dayNames() {
        return this._dayNames;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} value
     * @return {?}
     */
    set dayNames(value) {
        this._dayNames = value;
    }
    /**
     * @return {?}
     */
    get selectableRange() {
        return this._selectableRange;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectableRange(value) {
        try {
            if (Object.keys(value).length === 1) {
                if (this.datePickerObject !== undefined) {
                    this.datePickerObject.datepicker("option", "minDate", null);
                    this.datePickerObject.datepicker("option", "maxDate", value[Object.keys(value)[0]]);
                }
                this._selectableRange = value;
            }
            else if (Object.keys(value).length === 2) {
                /** @type {?} */
                const minDate = value[Object.keys(value)[0]];
                /** @type {?} */
                const maxDate = value[Object.keys(value)[1]];
                if (this.datePickerObject !== undefined) {
                    this.datePickerObject.datepicker("option", "minDate", minDate);
                    this.datePickerObject.datepicker("option", "maxDate", maxDate);
                }
                this._selectableRange = value;
            }
            else {
                throw new Error("selectableRange must be an object with tow keys rangeStart and rangeStop");
            }
        }
        catch (e) {
            this.logger.error(" selectableRange method :", e);
        }
    }
    /**
     * @return {?}
     */
    get open() {
        return this._open;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} handler
     * @return {?}
     */
    set open(handler) {
        this._open = handler;
    }
    /**
     * @return {?}
     */
    get close() {
        return this._close;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} handler
     * @return {?}
     */
    set close(handler) {
        this._close = handler;
    }
    /**
     * @return {?}
     */
    get keyDown() {
        return this._keyDown;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} handler
     * @return {?}
     */
    set keyDown(handler) {
        this._keyDown = handler;
    }
    /**
     * @return {?}
     */
    get focus() {
        return this._focus;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} handler
     * @return {?}
     */
    set focus(handler) {
        this._focus = handler;
    }
    /**
     * @return {?}
     */
    get focusOut() {
        return this._focusOut;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} handler
     * @return {?}
     */
    set focusOut(handler) {
        this._focusOut = handler;
    }
    /**
     * @return {?}
     */
    get selectedDate() {
        return this._selectedDate;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} date
     * @return {?}
     */
    set selectedDate(date) {
        if (this.datePickerObject !== undefined && date != null) {
            this._selectedDate = new Date(date);
            this._text = this.formatDate(date);
            this.datePickerObject.datepicker('setDate', date);
        }
        else {
            this._selectedDate = null;
            this._text = "";
            this.datePickerObject.datepicker('setDate', null);
        }
    }
    /**
     * @return {?}
     */
    get showToday() {
        return this._showToDay;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set showToday(value) {
        if (typeof (value) === 'string') {
            if (value === 'true') {
                this._showToDay = true;
            }
            else {
                this._showToDay = false;
            }
        }
        else {
            this._showToDay = value;
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visibility;
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) !== 'string') {
            this._visibility = value;
            value ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
        }
        else {
            if (value === 'true') {
                this.visible = true;
                $(this.elem.nativeElement).show();
            }
            else {
                this._visibility = false;
                $(this.elem.nativeElement).hide();
            }
        }
    }
    /**
     * @return {?}
     */
    get onSelect() {
        return this._change;
    }
    /*--------------------------------------------------------*/
    /**
     * @param {?} onSelect
     * @return {?}
     */
    set onSelect(onSelect) {
        this._change = onSelect;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.logger.debug("[ ngOnInit ] eneter");
        // Initialize datePiker object
        this.datePickerObject = $(this.elem.nativeElement.children[0]);
        this.datePickerObject.datepicker({
            showOn: "button",
            buttonImage: "assets/images/datePicker-icon.png",
            buttonImageOnly: true,
            dateFormat: this._formatString,
            buttonText: "",
            changeMonth: this._showMonthSelect,
            changeYear: this._showYearSelect,
            beforeShow: (/**
             * @param {?} result
             * @return {?}
             */
            (result) => {
                this.openEventOutPut.emit(result);
                this._open(result);
            }),
            onClose: (/**
             * @param {?} result
             * @return {?}
             */
            (result) => {
                this.closeEventOutPut.emit(result);
                this._close(result);
                this._text = result;
                this.changeEventOutPut.emit(result);
                this.spyChanges(this._text);
            }),
            onSelect: (/**
             * @param {?} result
             * @return {?}
             */
            (result) => {
                this._change(result);
                this._text = result;
                this._selectedDate = this.parseDate(result, this.formatString);
            })
        });
        this.datePickerObject.on('keydown', (/**
         * @param {?} result
         * @return {?}
         */
        (result) => {
            this.keyDownEventOutPut.emit(result);
            this._keyDown(result);
        }));
        this.datePickerObject.on('keyup', (/**
         * @param {?} result
         * @return {?}
         */
        (result) => {
            this.updateDate(result);
            this.spyChanges(this._text);
        }));
        this.datePickerObject.on('focus', (/**
         * @param {?} result
         * @return {?}
         */
        (result) => {
            this.focusEventOutPut.emit(result);
            this._focus(result);
            this.keyFocusChange.emit(result);
            // update focus Manager data (focused element)
            focusManager.focusTarget = this.id;
        }));
        this.datePickerObject.on('focusout', (/**
         * @param {?} result
         * @return {?}
         */
        (result) => {
            this._selectedDate = this.parseDate(this.text, this.formatString);
            this.focusOutEventOutPut.emit(result);
            this._focusOut(result);
            this.keyFocusChange.emit(result);
        }));
        // set datePiker width
        this.datePickerObject.width(this._width);
        // set datePiker stringFormate
        this.datePickerObject.datepicker("option", "dateFormat", this._formatString);
        // Handling enabling properties
        if (this.enabled === true) {
            this.datePickerObject.datepicker("option", "disabled", false);
        }
        else {
            this.datePickerObject.datepicker("option", "disabled", true);
        }
        // Handling visibility properties  
        if (this.visible === false) {
            this.datePickerObject.addClass("hide");
        }
        else {
            this.datePickerObject.removeClass("hide");
        }
        // Handling editable properties
        if (this.editable === true) {
            this.datePickerObject.prop("readonly", false);
        }
        else {
            this.datePickerObject.prop("readonly", true);
        }
        // Handling text align 
        if (this._textAlign !== undefined) {
            this.datePickerObject.css("text-align", this._textAlign);
        }
        // set datePicker monthNames
        this.datePickerObject.datepicker("option", "monthNames", this._monthNames);
        // set datePicker dayNames
        this.datePickerObject.datepicker("option", "dayNames", this._dayNames);
        // set textAlign property
        this.datePickerObject.css("text-align", this._textAlign);
        //set selectable Range
        if (this.selectableRange !== undefined) {
            if (Object.keys(this.selectableRange).length === 1) {
                this.datePickerObject.datepicker("option", "minDate", null);
                this.datePickerObject.datepicker("option", "maxDate", this.selectableRange[Object.keys(this.selectableRange)[0]]);
            }
            else if (Object.keys(this.selectableRange).length === 2) {
                this.datePickerObject.datepicker("option", "minDate", this.selectableRange[Object.keys(this.selectableRange)[0]]);
                this.datePickerObject.datepicker("option", "maxDate", this.selectableRange[Object.keys(this.selectableRange)[1]]);
            }
            else {
            }
        }
        if (this.width && typeof (this.width) === "string" && this.width.indexOf("%") === -1) {
            $(this.datefield.nativeElement).width(this._width);
        }
        else {
            this._renderer.setStyle(this.datefield.nativeElement.parentElement, 'width', this.width);
            this._renderer.setStyle(this.datefield.nativeElement, 'width', "90%");
        }
        this.datePickerObject.datepicker('setDate', this._selectedDate);
        // Added by Rihab.J @07/12/2018 - needed to be used in dynamically added SwtDateField.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtDateField');
        // set id to SwtDateField DOM.
        if (this.id) {
            $($(this.elem.nativeElement)[0]).attr("id", this.id);
        }
        //-Add ToolTip.
        if (this._toolTip && $(this.datefield.nativeElement).length > 0) {
            $($(this.datefield.nativeElement)[0]).attr("title", this._toolTip);
        }
        $($(this.datefield.nativeElement)[0]).tooltip({
            show: { duration: 800, delay: 500 },
            open: (/**
             * @param {?} event
             * @param {?} ui
             * @return {?}
             */
            function (event, ui) {
                $(this).removeAttr('title');
            })
        });
        this.logger.debug("[ ngOnInit ] end.");
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        this.showToday ? this.selectedDate = new Date() : null;
        this.datePickerObject.datepicker("option", "currentText", "Now");
    }
    /**
     * This method is used to set visibility of the component.
     * @param {?} visibility
     * @return {?}
     */
    setVisible(visibility) {
        this._visibility = visibility;
        visibility ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
    }
    /**
     * This function is used to parse the given date string to date
     * This function converts the given date string to MM/DD/YYYY
     * format and then parse the date
     *
     * param dateString: String
     * param formatString: String
     * @param {?} dateString
     * @param {?=} formatString
     * @return {?} Date
     */
    parseDate(dateString, formatString = null) {
        /** @type {?} */
        var date = null;
        /** @type {?} */
        let dateSplitFragments = new Array();
        /** @type {?} */
        let delims = "";
        formatString == null ? formatString = this.formatString : null;
        try {
            if (dateString == null) {
                return null;
            }
            if (dateString.length != 10 || (dateString.indexOf("/") === -1 && dateString.indexOf("/") === -1)) {
                return null;
            }
            if (dateString !== null && dateString !== '' && dateString !== undefined) {
                if (formatString !== null && formatString !== undefined && formatString !== undefined) {
                    if (formatString.toUpperCase().indexOf("DD") === 0) {
                        dateSplitFragments = this.spliter(dateString)["dateSplitFragments"];
                        delims = this.spliter(dateString)["delims"];
                        if (dateSplitFragments) {
                            if (dateSplitFragments[1] > 12 || dateSplitFragments[1] < 1) {
                                date = null;
                            }
                            else if (dateSplitFragments[0] > 31 || dateSplitFragments[0] < 1) {
                                date = null;
                            }
                            else {
                                date = null;
                                date = new Date(dateSplitFragments[1] + delims + dateSplitFragments[0] + delims + dateSplitFragments[2]);
                            }
                        }
                        else {
                            date = null;
                        }
                    }
                    else {
                        dateSplitFragments = this.spliter(dateString)["dateSplitFragments"];
                        delims = this.spliter(dateString)["delims"];
                        if (Number(dateSplitFragments[0]) > 12 || Number(dateSplitFragments[1]) < 1) {
                            date = null;
                        }
                        else if (Number(dateSplitFragments[1]) > 31 || Number(dateSplitFragments[0]) < 1) {
                            date = null;
                        }
                        else {
                            date = new Date(dateString);
                        }
                    }
                }
            }
            else {
                date = null;
            }
        }
        catch (e) {
            this.logger.error("parseDate method ", e);
        }
        return date;
    }
    /**
     * This method is used to set style
     * to component
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    setStyle(attribute, value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            $(this.elem.nativeElement.children[0]).css(attribute, value);
        }), 0);
    }
    /**
     * This function formats the selected date to display
     *
     * param value: Date
     * @param {?} value
     * @return {?} String
     */
    formatDate(value) {
        this.logger.debug("Enter [formatDate] id=" + this.id + ", value=" + value);
        //Flag to identify whether the format string contains "-" as separator
        /** @type {?} */
        var flag = false;
        if (this.formatString.indexOf("-") > -1) {
            //Set format string. Replace "-" with "/" as flex does not support "-" as a separator
            this._formatString = this._formatString.replace(/-/g, "/");
            flag = true;
        }
        else {
            this._formatString = this.formatString;
        }
        //Format the date
        /** @type {?} */
        var date = this.getAbbreviationFromDate(value);
        //Convert the date string to its original format
        if (flag) {
            date = date.replace(/\//g, "-");
        }
        //Format the date and returns the same
        this.logger.debug("Exit [formatDate] id=" + this.id + ", date=" + date);
        return date;
    }
    /**
     * This method is used to show dropDown.
     * @return {?}
     */
    openDropDown() {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.datePickerObject.datepicker("show");
        }), 0);
    }
    /**
     * getAbbreviationFromDate
     *
     * This funtion is used to get abreviation from date when it is in DD-MMM-YYYY format
     * @param {?} date
     * @return {?}
     */
    getAbbreviationFromDate(date) {
        /** @type {?} */
        let formattedDate = "";
        /** @type {?} */
        let day;
        /** @type {?} */
        let month;
        /** @type {?} */
        let year;
        try {
            if (date) {
                day = date.getDate();
                month = date.getMonth();
                year = date.getFullYear();
                if (this.formatString.toUpperCase() === "DD-M-YY") {
                    formattedDate = this.loadingZero(day) + "-" + this._monthNames[month] + "-" + this.loadingZero(year);
                }
                else if (this.formatString.toUpperCase() === "MM/DD/YY") {
                    formattedDate = this.loadingZero(month + 1) + "/" + this.loadingZero(day) + "/" + this.loadingZero(year);
                }
                else if (this.formatString.toUpperCase() === "DD/MM/YY") {
                    formattedDate = this.loadingZero(day) + "/" + this.loadingZero(month + 1) + "/" + this.loadingZero(year);
                }
                else {
                    throw new Error("Specify the dateField String Format!");
                }
            }
        }
        catch (e) {
            this.logger.error("getAbbreviationFromDate method :", e);
        }
        return formattedDate;
    }
    /**
     * Returns the Date object
     * @return {?}
     */
    getDate() {
        return this.datePickerObject.datepicker("getDate");
    }
    /**
     * @return {?}
     */
    setFocus() {
        if (this.datePickerObject !== undefined) {
            this.datePickerObject.focus();
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (this.originalValue == undefined) {
            this.originalValue = "";
        }
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    }
    /**
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue = this._text;
        this.spyChanges(this._text);
    }
    /**
     * This method is used to update date.
     * @private
     * @param {?} res
     * @return {?}
     */
    updateDate(res) {
        this._text = $(this.datefield.nativeElement).val();
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    loadingZero(value) {
        /** @type {?} */
        var rtn = "";
        if (value >= 10) {
            rtn = value + "";
        }
        else if (value > 0 && value < 10) {
            rtn = "0" + value;
        }
        else {
            rtn = "00";
        }
        return rtn;
    }
    /**
     * This method is used to detect date delimiter
     * and split the dateString.
     * <AUTHOR>
     * @private
     * @param {?} dateString
     * @return {?}
     */
    spliter(dateString) {
        /** @type {?} */
        const tab = new Array();
        if (dateString.indexOf("-") !== -1) {
            tab["dateSplitFragments"] = dateString.split("-");
            tab["delims"] = "-";
        }
        else if (dateString.indexOf("/") !== -1) {
            tab["dateSplitFragments"] = dateString.split("/");
            tab["delims"] = "/";
        }
        else {
            // throw new Error("parseDate(dateString, [ERROR] -> formatString) formatString only '-' and '/' are accepted as delemeter");
        }
        return tab;
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    isValidDate(value) {
        return value.indexOf("Invalid Date") === -1;
    }
}
SwtDateField.decorators = [
    { type: Component, args: [{
                selector: 'SwtDateField',
                template: `
          <input popper="{{this.toolTipPreviousValue}}"
          [popperTrigger]="'hover'"
          [popperDisabled]="toolTipPreviousValue === null ? true : false"
          [popperPlacement]="'bottom'"
          [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}" selector="SwtDatefield" #datefield type="text" class="mdatepicker" tabindex="{{tabIndex}}">
  `,
                styles: [`
      .mdatepicker {
           height: 22px;
           border-left:1px solid #D3D5D6;
           border-right:1px solid #D3D5D6;
           border-bottom:1px solid #D3D5D6;
           border-top: 1px solid #6D6F70;
           padding: 0px 5px 0px 5px;
           font-size: 11px;
           font-family: sans-serif;
           font-weight: normal;
           margin: 0px 5px 5px 0px !important;
           color: #000; /*this line is added to set the color of datefield as black when using SwtFieldSet*/
      }

      .hide {
           display: none;
      }
  `]
            }] }
];
/** @nocollapse */
SwtDateField.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
SwtDateField.propDecorators = {
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    datefield: [{ type: ViewChild, args: ["datefield",] }],
    tabIndex: [{ type: Input, args: ["tabIndex",] }],
    restrict: [{ type: Input, args: ["restrict",] }],
    toolTip: [{ type: Input, args: ['toolTip',] }],
    textDictionaryId: [{ type: Input, args: ['tooltipDictionaryId',] }],
    toolTipPreviousValue: [{ type: Input }],
    id: [{ type: Input, args: ["id",] }],
    openEventOutPut: [{ type: Output, args: ["open",] }],
    closeEventOutPut: [{ type: Output, args: ["close",] }],
    keyDownEventOutPut: [{ type: Output, args: ["keyDown",] }],
    changeEventOutPut: [{ type: Output, args: ["change",] }],
    focusEventOutPut: [{ type: Output, args: ["focus",] }],
    focusOutEventOutPut: [{ type: Output, args: ["focusOut",] }],
    keyFocusChange: [{ type: Output, args: ["keyFocusChange",] }],
    enabled: [{ type: Input }],
    editable: [{ type: Input }],
    width: [{ type: Input }],
    text: [{ type: Input }],
    textAlign: [{ type: Input }],
    selectableRange: [{ type: Input }],
    showToday: [{ type: Input }],
    visible: [{ type: Input, args: ['visible',] }]
};
if (false) {
    /** @type {?} */
    SwtDateField.prototype.originalValue;
    /** @type {?} */
    SwtDateField.prototype.onSpyChange;
    /** @type {?} */
    SwtDateField.prototype.onSpyNoChange;
    /** @type {?} */
    SwtDateField.prototype.datefield;
    /** @type {?} */
    SwtDateField.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._toolTipPreviousObject;
    /** @type {?} */
    SwtDateField.prototype.restrict;
    /** @type {?} */
    SwtDateField.prototype.id;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.firstCall;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showOnFlag;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.datePickerObject;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showToDay;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.interrupted;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.openEventOutPut;
    /** @type {?} */
    SwtDateField.prototype.closeEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.keyDownEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.changeEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.focusEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.focusOutEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.keyFocusChange;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showYearSelect;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showMonthSelect;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._formatString;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._monthNames;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._dayNames;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._selectableRange;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._open;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._close;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._keyDown;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._selectedDate;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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