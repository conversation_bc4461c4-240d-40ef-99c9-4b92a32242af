/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
//customWrap.ts
/**
 * @param {?} Highcharts
 * @return {?}
 */
export default function (Highcharts) {
    /** @type {?} */
    var H = Highcharts;
    /** @type {?} */
    var pick = Highcharts.pick;
    H.wrap(H.Chart.prototype, "getDataRows", (/**
     * @param {?} proceed
     * @return {?}
     */
    function (proceed) {
        /** @type {?} */
        var options = (this.options.exporting || {}).csv || {};
        /** @type {?} */
        var xAxis;
        /** @type {?} */
        var xAxes = this.xAxis;
        /** @type {?} */
        var rows = {};
        /** @type {?} */
        var rowArr = [];
        /** @type {?} */
        var dataRows;
        /** @type {?} */
        var names = [];
        /** @type {?} */
        var i;
        /** @type {?} */
        var x;
        /** @type {?} */
        var xTitle;
        /** @type {?} */
        var 
        // Options
        dateFormat = options.dateFormat || '%Y-%m-%d %H:%M:%S';
        /** @type {?} */
        var columnHeaderFormatter = options.columnHeaderFormatter || (/**
         * @param {?} item
         * @param {?} key
         * @param {?} keyLength
         * @return {?}
         */
        function (item, key, keyLength) {
            if (item instanceof Highcharts.Axis) {
                return (item.options.title && item.options.title.text) ||
                    ('DateTime');
            }
            return item.options.valueId ? item.options.valueId : "";
        });
        /** @type {?} */
        var xAxisIndices = [];
        // Loop the series and index values
        i = 0;
        for (var u = 0; u < this.series.length; u++) {
            /** @type {?} */
            var series = this.series[u];
            // this.series.forEach( (series) => {
            /** @type {?} */
            var keys = series.options.keys;
            /** @type {?} */
            var pointArrayMap = keys || series.pointArrayMap || ['y'];
            /** @type {?} */
            var valueCount = pointArrayMap.length;
            /** @type {?} */
            var requireSorting = series.requireSorting;
            /** @type {?} */
            var categoryMap = {};
            /** @type {?} */
            var xAxisIndex = Highcharts.inArray(series.xAxis, xAxes);
            /** @type {?} */
            var j;
            // Map the categories for value axes
            if (pointArrayMap) {
                for (var p = 0; p < pointArrayMap.length; p++) {
                    // this.pointArrayMap.forEach( (prop) => {
                    /** @type {?} */
                    var prop_1 = pointArrayMap[p];
                    categoryMap[prop_1] = (series[prop_1 + 'Axis'] && series[prop_1 + 'Axis'].categories) || [];
                }
                ;
            }
            if (series.options.includeInCSVExport !== false && series.visible !== false) { // #55
                // Build a lookup for X axis index and the position of the first
                // series that belongs to that X axis. Includes -1 for non-axis
                // series types like pies.
                if (!Highcharts.find(xAxisIndices, (/**
                 * @param {?} index
                 * @return {?}
                 */
                function (index) {
                    return index[0] === xAxisIndex;
                }))) {
                    xAxisIndices.push([xAxisIndex, i]);
                }
                // Add the column headers, usually the same as series names
                j = 0;
                while (j < valueCount) {
                    names.push(columnHeaderFormatter(series, pointArrayMap[j], pointArrayMap.length));
                    j = j + 1;
                }
                //console.log("names", names)
                for (var pIdx = 0; pIdx < series.points.length; pIdx++) {
                    //   this.series.points.forEach( (point, pIdx) => {
                    /** @type {?} */
                    var point = series.points[pIdx];
                    /** @type {?} */
                    var key = requireSorting ? point.x : pIdx;
                    /** @type {?} */
                    var prop;
                    /** @type {?} */
                    var val;
                    j = 0;
                    if (!rows[key]) {
                        // Generate the row
                        rows[key] = [];
                        // Contain the X values from one or more X axes
                        rows[key].xValues = [];
                    }
                    rows[key].x = point.x;
                    rows[key].xValues[xAxisIndex] = point.x;
                    // Pies, funnels, geo maps etc. use point name in X row
                    if (!series.xAxis || series.exportKey === 'name') {
                        rows[key].name = point.name;
                    }
                    while (j < valueCount) {
                        prop = pointArrayMap[j]; // y, z etc
                        val = point[prop];
                        rows[key][i + j] = pick(categoryMap[prop][val], val); // Pick a Y axis category if present
                        j = j + 1;
                    }
                }
                ;
                i = i + j;
            }
        }
        // Make a sortable array
        for (x in rows) {
            if (rows.hasOwnProperty(x)) {
                rowArr.push(rows[x]);
            }
        }
        /** @type {?} */
        var binding;
        /** @type {?} */
        var xAxisIndex;
        /** @type {?} */
        var column;
        dataRows = [names];
        i = xAxisIndices.length;
        while (i--) { // Start from end to splice in
            xAxisIndex = xAxisIndices[i][0];
            column = xAxisIndices[i][1];
            xAxis = xAxes[xAxisIndex];
            // Sort it by X values
            rowArr.sort((/**
             * @param {?} a
             * @param {?} b
             * @return {?}
             */
            function (a, b) {
                return a.xValues[xAxisIndex] - b.xValues[xAxisIndex];
            }));
            // Add header row
            xTitle = columnHeaderFormatter(xAxis);
            //dataRows = [[xTitle].concat(names)];
            dataRows[0].splice(column, 0, xTitle);
            // Add the category column
            for (var h = 0; h < rowArr.length; h++) {
                /** @type {?} */
                var row = rowArr[h];
                // rowArr.forEach( (row) => {
                /** @type {?} */
                var category = row.name;
                if (!category) {
                    if (xAxis.options.type === "datetime") {
                        if (row.x instanceof Date) {
                            row.x = row.x.getTime();
                        }
                        category = Highcharts.dateFormat(dateFormat, row.x);
                    }
                    else if (xAxis.categories) {
                        category = pick(xAxis.names[row.x], xAxis.categories[row.x], row.x);
                    }
                    else {
                        category = row.x;
                    }
                }
                // Add the X/date/category
                row.splice(column, 0, category);
            }
            ;
        }
        dataRows = dataRows.concat(rowArr);
        return dataRows;
    }));
}
//# sourceMappingURL=data:application/json;base64,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