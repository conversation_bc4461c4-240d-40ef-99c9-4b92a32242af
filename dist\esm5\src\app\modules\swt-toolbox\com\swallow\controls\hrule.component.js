/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var HRule = /** @class */ (function (_super) {
    tslib_1.__extends(HRule, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function HRule(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions----------------------------------------------------------------------------------------
        _this._strokeColor = "";
        _this._shadowColor = "";
        _this._themeColor = "";
        //-START- Added by Rihab.J   - needed to be used in dynamically added HRule.
        $($(_this.elem.nativeElement)[0]).attr('selector', 'HRule');
        return _this;
    }
    Object.defineProperty(HRule.prototype, "strokeColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._strokeColor;
        },
        //----strokeColor--------------------------------------------------------------------------------------------------
        set: 
        //----strokeColor--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HRule.prototype, "shadowColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._shadowColor;
        },
        //----shadowColor--------------------------------------------------------------------------------------------------
        set: 
        //----shadowColor--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HRule.prototype, "themeColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._themeColor;
        },
        //----themeColor---------------------------------------------------------------------------------------------------
        set: 
        //----themeColor---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    HRule.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        try {
            delete this._strokeColor;
            delete this._shadowColor;
            delete this._themeColor;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    HRule.decorators = [
        { type: Component, args: [{
                    selector: 'HRule',
                    template: "\n    <div class=\"h-rule\"></div>\n  ",
                    styles: ["\n       .h-rule {\n           background-color: #000;\n           height: 1px;\n           width:100%;\n           box-shadow: 0px 1px 0px #fff;\n       }\n  "]
                }] }
    ];
    /** @nocollapse */
    HRule.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    HRule.propDecorators = {
        strokeColor: [{ type: Input }],
        shadowColor: [{ type: Input }],
        themeColor: [{ type: Input }]
    };
    return HRule;
}(Container));
export { HRule };
if (false) {
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._strokeColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._shadowColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._themeColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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