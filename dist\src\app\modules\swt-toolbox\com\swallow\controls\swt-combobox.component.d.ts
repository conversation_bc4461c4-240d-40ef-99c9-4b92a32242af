import { OnInit, EventEmitter, AfterViewInit, ElementRef, On<PERSON><PERSON>roy } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "../utils/common.service";
export declare class SwtComboBox implements OnInit, AfterViewInit, OnDestroy {
    private elem;
    private commonService;
    originalValue: any;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    mouseWeelEventHandler(targetElement: any): void;
    private logger;
    private SwtAlert;
    private _isDropdownOpen;
    private _selecedIndex;
    private _enabled;
    private _editable;
    private _visible;
    private _text;
    private _selectedItem;
    private _selectedLabel;
    private _inputClick;
    private _open;
    private _close;
    private _focus;
    private _focusout;
    private _change;
    private _onTextChange;
    private _tempData;
    private _originalData;
    private filterState;
    private _selectedValue;
    private _alertvisiblity;
    private _dataProvider;
    private _outside;
    private _insideButton;
    private _required;
    dataLabel: string;
    private toolTipObject;
    private toolTipPreviousObject;
    private interrupted;
    private _ignored_validation;
    private ignoredValidationArr;
    private _default_ignored;
    private backGroundImage;
    private onOpenSelectedIndex;
    private _shiftUp;
    showDescriptionInDropDown: boolean;
    private _toolTipPreviousObject;
    toolTip: any;
    toolTipPreviousValue: any;
    prompt: string;
    width: string;
    height: string;
    id: string;
    firstCall: boolean;
    readonly focusOutSide: boolean;
    /**
 * Setter for the _ignored_validation variable
 * */
    /**
    * Getter the _ignored_validation variable
    * */
    ignored_validation: string;
    required: any;
    interruptComms: boolean;
    inputClickHandler(event: any): void;
    inputChangeHandler(event: any): void;
    dataProvider: any[];
    open_: EventEmitter<Function>;
    inputClick_: EventEmitter<Function>;
    onTextChange: EventEmitter<any>;
    close_: EventEmitter<Function>;
    focus_: EventEmitter<Function>;
    focusout_: EventEmitter<Function>;
    change_: EventEmitter<any>;
    private filter;
    private inputGroup;
    private listitem;
    private dropDownli;
    private dropDownContainer;
    enabled: any;
    editable: any;
    shiftUp: any;
    visible: any;
    constructor(elem: ElementRef, commonService: CommonService);
    ngAfterViewInit(): void;
    ngOnInit(): void;
    ngOnDestroy(): void;
    /**
     * This method is called when arrow clicked.
     */
    private onArrowClick;
    /**
     * This method is used to filter comboBox.
     */
    private filterDropDown;
    /**
     * This method is used to filter comboBox.
     */
    private fillSelectedIndexWithFiltertext;
    /**
     * This method is used to refresh
     * comboBox view.
     */
    private showAlert;
    private setFilterFocus;
    /**
     * This method is used to show comoboBox drop Down.
     */
    private showDropDown;
    /**
     * This method is used to hide comboBox drop down.
     */
    private hideDropDown;
    /**
     * This method i used to set item to
     * dataProvioder.
     * @param item
     */
    addItem(item: any): void;
    /**
     * This method is used to add item at position.
     * @param item
     * @param position
     */
    addItemAt(item: any, position: number): void;
    /**
     * This method is used to select option.
     * @param option
     */
    selectItem(item: any, fromView?: boolean, notFireChangeEvent?: boolean): void;
    private updateSelectedItem;
    setPrompt(prompt: string): void;
    setComboData(input: any, reverse?: boolean): void;
    setComboDataAndForceSelected(input: any, reverse: boolean, selectedValue: string): void;
    private highlightItem;
    /**
     * This method is used to get index of given item.
     * @param item
     */
    getIndexOf(item: any): number;
    /**
     * This method is used to handle scrolling
     * to bottom.
     */
    private scrollToBottom;
    private reverse;
    /**
     * This method is used to handle scrolling
     * to Top.
     */
    private scrollToTop;
    selectedValue: string;
    appendOption(): void;
    selectedLabel: string;
    /**
     * This method is used to remove all element from
     * comboBox dataProvider
     */
    removeAll(): void;
    /**
     * This method is used to remove element from
     * comboBox dataProvider in specific  index.
     * @param index
     */
    removeItemAt(index: number): void;
    /**
     * This method is used to set focus
     * to comboBox.
     */
    setFocus(): void;
    getbackGroundImange(): string;
    setVisible(visibility: boolean): void;
    readonly isDropdownOpen: boolean;
    selectedIndex: number;
    text: string;
    selectedItem: any;
    open: Function;
    inputClick: Function;
    close: Function;
    focusout: Function;
    change: Function;
    resetOriginalValue(): void;
    spyChanges(event: any): void;
}
