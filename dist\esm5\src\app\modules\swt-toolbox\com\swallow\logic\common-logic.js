/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//import { moment } from 'ngx-bootstrap/chronos/test/chain';
import * as moment_ from 'moment-mini';
import { Alert } from '../utils/alert.component';
import { ExternalInterface } from '../utils/external-interface.service';
import { CommonUtil } from '../utils/common-util.service';
/** @type {?} */
var moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
//@dynamic
var CommonLogic = /** @class */ (function () {
    function CommonLogic() {
        this._testDate = "";
        this._dateFormat = "";
        this._showBuildInProgress = false;
        // End.
    }
    Object.defineProperty(CommonLogic.prototype, "testDate", {
        get: /**
         * @return {?}
         */
        function () {
            return this._testDate;
        },
        set: /**
         * @param {?} testDate
         * @return {?}
         */
        function (testDate) {
            this._testDate = this.convertUKtoUS(testDate);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} dateAsString
     * @return {?}
     */
    CommonLogic.prototype.convertDate = /**
     * @param {?} dateAsString
     * @return {?}
     */
    function (dateAsString) {
        return this.convertUKtoUS(dateAsString);
    };
    Object.defineProperty(CommonLogic.prototype, "dateFormat", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dateFormat;
        },
        set: /**
         * @param {?} dateFormat
         * @return {?}
         */
        function (dateFormat) {
            /** @type {?} */
            var str = dateFormat.toUpperCase();
            this._dateFormat = str;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} start
     * @param {?} end
     * @return {?}
     */
    CommonLogic.prototype.calculateDays = /**
     * @param {?} start
     * @param {?} end
     * @return {?}
     */
    function (start, end) {
        /** @type {?} */
        var daysInMilliseconds = 1000 * 60 * 60 * 24;
        return ((end.getTime() - start.getTime()) / daysInMilliseconds);
    };
    /**
     * @param {?} input
     * @return {?}
     */
    CommonLogic.prototype.convertUKtoUS = /**
     * @param {?} input
     * @return {?}
     */
    function (input) {
        /** @type {?} */
        var rtn = null;
        if (input) {
            if (this._dateFormat == "DD/MM/YYYY") {
                /** @type {?} */
                var dateArry = input.split("/");
                rtn = dateArry[1] + "/" + dateArry[0] + "/" + dateArry[2];
            }
            else {
                rtn = input; //Already in US format
            }
        }
        return rtn;
    };
    /**
     * @param {?} input
     * @return {?}
     */
    CommonLogic.removeLineBreaks = /**
     * @param {?} input
     * @return {?}
     */
    function (input) {
        /** @type {?} */
        var rtn = "";
        /** @type {?} */
        var postSplit = [];
        postSplit = input.split("\n");
        for (var i = 0; i < postSplit.length; i++) {
            rtn += postSplit[i];
        }
        return rtn;
    };
    /**
     * This function validates the date field - Mantis 1262. <br>
     * The date field is an editable one where the user can type the desired date.<br>
     * The date is taken as an argument and it is validate against certain rules.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010<br>
     */
    /**
     * This function validates the date field - Mantis 1262. <br>
     * The date field is an editable one where the user can type the desired date.<br>
     * The date is taken as an argument and it is validate against certain rules.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010<br>
     * @param {?} event
     * @param {?} startDay
     * @param {?} dateVal
     * @return {?}
     */
    CommonLogic.prototype.validateDate = /**
     * This function validates the date field - Mantis 1262. <br>
     * The date field is an editable one where the user can type the desired date.<br>
     * The date is taken as an argument and it is validate against certain rules.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010<br>
     * @param {?} event
     * @param {?} startDay
     * @param {?} dateVal
     * @return {?}
     */
    function (event, startDay, dateVal) {
        /** @type {?} */
        var validatDate = false;
        /** @type {?} */
        var day;
        /** @type {?} */
        var month;
        /** @type {?} */
        var year;
        /** @type {?} */
        var dateValue = dateVal.toString();
        if (dateValue != "") {
            if (startDay.formatString == "MM/DD/YYYY") {
                /** @type {?} */
                var myPattern1 = /^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/;
                if (myPattern1.test(startDay.text).toString() == "true") {
                    month = Number(dateValue.split("/")[0]);
                    day = Number(dateValue.split("/")[1]);
                    year = Number(dateValue.split("/")[2]);
                    // calling the helper function for date validation
                    if (this.validateDateHelper(day, month, year, startDay))
                        validatDate = true;
                }
                else {
                    this.showAlert(startDay);
                }
            }
            else if (startDay.formatString == "DD/MM/YYYY") {
                /** @type {?} */
                var myPattern2 = /^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/;
                if (myPattern2.test(startDay.text).toString() == "true") {
                    day = Number(dateValue.split("/")[0]);
                    month = Number(dateValue.split("/")[1]);
                    year = Number(dateValue.split("/")[2]);
                    // calling the helper function for date validation
                    if (this.validateDateHelper(day, month, year, startDay))
                        validatDate = true;
                }
                else {
                    this.showAlert(startDay);
                }
            }
        }
        else {
            this.showAlert(startDay);
        }
        return validatDate;
    };
    /* Start : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /* Start : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * @param {?} startDay
     * @return {?}
     */
    CommonLogic.prototype.showAlert = /* Start : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * @param {?} startDay
     * @return {?}
     */
    function (startDay) {
        this.swtAlert.show("Please enter a valid date", "Error", Alert.OK, null, (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            // Code modified by Marshal on 09-Mar-2011 for Mantis 1368: 
            // Description: Click on Date tabs should show corresponding date tab value in Date Field
            startDay.setFocus();
        }));
    };
    /* End : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * Helper method for validating the given date field - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     */
    /* End : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * Helper method for validating the given date field - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} day
     * @param {?} month
     * @param {?} year
     * @param {?} startDay
     * @return {?}
     */
    CommonLogic.prototype.validateDateHelper = /* End : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * Helper method for validating the given date field - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} day
     * @param {?} month
     * @param {?} year
     * @param {?} startDay
     * @return {?}
     */
    function (day, month, year, startDay) {
        /** @type {?} */
        var validateHelp = true;
        // checking the months which has only 30 days.
        if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
            validateHelp = false;
            this.showAlert(startDay);
        }
        // checking February month on leap years
        if (month == 2) {
            if (day > 29 || (day == 29 && !this.isLeapYear(year))) {
                validateHelp = false;
                this.showAlert(startDay);
            }
        }
        return validateHelp;
    };
    /**
     * Helper function for checking whether the given year is a leap year or not - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     */
    /**
     * Helper function for checking whether the given year is a leap year or not - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} year
     * @return {?}
     */
    CommonLogic.prototype.isLeapYear = /**
     * Helper function for checking whether the given year is a leap year or not - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} year
     * @return {?}
     */
    function (year) {
        /** @type {?} */
        var isLeap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
        return isLeap;
    };
    // Start: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011 
    /**
     * This function converts the given string to boolean
     *
     * @param value:String - Boolean value in string format or an expression
     * @return Boolean
     */
    // Start: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011 
    /**
     * This function converts the given string to boolean
     *
     * @param {?} value
     * @return {?} Boolean
     */
    CommonLogic.booleanValue = 
    // Start: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011 
    /**
     * This function converts the given string to boolean
     *
     * @param {?} value
     * @return {?} Boolean
     */
    function (value) {
        switch (value.toLowerCase()) {
            case "1":
            case "true":
            case "yes":
            case "y":
                return true;
            case "0":
            case "false":
            case "no":
            case "n":
                return false;
            default:
                return Boolean(value);
        }
    };
    // End: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011
    /**
     * Added for mantis by KaisBS 2016 + 1468
     * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
     * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
     * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
     *
     * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
     * 	Note that mantis concerns other screens that contain from and to date.
     **/
    // End: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011
    /**
     * Added for mantis by KaisBS 2016 + 1468
     * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
     * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
     * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
     *
     * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
     * 	Note that mantis concerns other screens that contain from and to date.
     *
     * @param {?} autoRefreshOrcheckLocalDateRange
     * @param {?} fromDate
     * @param {?} toDate
     * @param {?} showDays
     * @param {?} systemDate
     * @param {?} dateFormat
     * @param {?} updateDataFunction
     * @param {?} classObject
     * @return {?}
     */
    CommonLogic.prototype.checkDateRange = 
    // End: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011
    /**
     * Added for mantis by KaisBS 2016 + 1468
     * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
     * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
     * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
     *
     * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
     * 	Note that mantis concerns other screens that contain from and to date.
     *
     * @param {?} autoRefreshOrcheckLocalDateRange
     * @param {?} fromDate
     * @param {?} toDate
     * @param {?} showDays
     * @param {?} systemDate
     * @param {?} dateFormat
     * @param {?} updateDataFunction
     * @param {?} classObject
     * @return {?}
     */
    function (autoRefreshOrcheckLocalDateRange, fromDate, toDate, showDays, systemDate, dateFormat, updateDataFunction, classObject) {
        var _this = this;
        //classObject.inputData.addEventListener(ResultEvent.RESULT, result);
        classObject.inputData.cbResult = (/**
         * @param {?} data
         * @return {?}
         */
        function (data) {
            _this.result(data);
        });
        //classObject.inputData.addEventListener(FaultEvent.FAULT, fault);
        classObject.inputData.cbFault = (/**
         * @param {?} data
         * @return {?}
         */
        function (data) {
            _this.fault(data);
        });
        /** @type {?} */
        var dateRangeExceeded = false;
        /** @type {?} */
        var nDaysPriorToToday = ExternalInterface.call('eval', 'nDaysPriorToToday');
        /** @type {?} */
        var priorDate = new Date(CommonUtil.dateFromString(systemDate, this.dateFormat));
        priorDate.setDate(priorDate.getDate() - nDaysPriorToToday);
        /** @type {?} */
        var nDaysAheadToToday = ExternalInterface.call('eval', 'nDaysAheadToToday');
        /** @type {?} */
        var aheadDate = new Date(CommonUtil.dateFromString(systemDate, this.dateFormat));
        aheadDate.setDate(aheadDate.getDate() - nDaysAheadToToday);
        if (fromDate < priorDate || toDate > aheadDate) {
            dateRangeExceeded = true;
            /** @type {?} */
            var windowTitle = 'Microsoft Internet Explorer';
            /** @type {?} */
            var warningMessage = 'The data for this date range selection may not be available ' +
                'in the cache and will take time to be calculated. Do you want to continue?';
            this.swtAlert.confirm(warningMessage, windowTitle, Alert.OK | Alert.CANCEL, null, (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                if (event.detail == Alert.OK) {
                    // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
                    this._showBuildInProgress = true;
                    updateDataFunction(autoRefreshOrcheckLocalDateRange, true);
                }
                else
                    updateDataFunction(autoRefreshOrcheckLocalDateRange, true, true);
            }), null);
        }
        return dateRangeExceeded;
    };
    Object.defineProperty(CommonLogic.prototype, "showBuildInProgress", {
        // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
        // Begin.
        get: 
        // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
        // Begin.
        /**
         * @return {?}
         */
        function () {
            return this._showBuildInProgress;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} data
     * @return {?}
     */
    CommonLogic.prototype.result = /**
     * @param {?} data
     * @return {?}
     */
    function (data) {
        this._showBuildInProgress = false;
        // FlexGlobals.topLevelApplication.inputData.removeEventListener(ResultEvent.RESULT, this.result);
    };
    /**
     * @param {?} datat
     * @return {?}
     */
    CommonLogic.prototype.fault = /**
     * @param {?} datat
     * @return {?}
     */
    function (datat) {
        this._showBuildInProgress = false;
        // FlexGlobals.topLevelApplication.inputData.removeEventListener(FaultEvent.FAULT, fault);
    };
    CommonLogic.decorators = [
        { type: Injectable }
    ];
    return CommonLogic;
}());
export { CommonLogic };
if (false) {
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._testDate;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._dateFormat;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._showBuildInProgress;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype.swtAlert;
}
//# sourceMappingURL=data:application/json;base64,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