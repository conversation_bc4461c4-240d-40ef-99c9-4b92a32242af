/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

declare function plural(n: number): number;
declare const _default: (string | number | (string[] | undefined)[] | number[] | (string | undefined)[] | typeof plural | {
    'ARS': string[];
    'AUD': string[];
    'BEF': string[];
    'BMD': string[];
    'BND': string[];
    'BSD': string[];
    'BZD': string[];
    'CAD': string[];
    'CLP': string[];
    'CNY': (string | undefined)[];
    'COP': string[];
    'CYP': string[];
    'EGP': (string | undefined)[];
    'FJD': string[];
    'FKP': string[];
    'GBP': string[];
    'GIP': string[];
    'HKD': (string | undefined)[];
    'IEP': string[];
    'ILP': string[];
    'ITL': string[];
    'JPY': (string | undefined)[];
    'KMF': (string | undefined)[];
    'LBP': string[];
    'LUF': string[];
    'MTP': string[];
    'MXN': string[];
    'NAD': string[];
    'NIO': (string | undefined)[];
    'NZD': string[];
    'RHD': string[];
    'RON': (string | undefined)[];
    'RWF': (string | undefined)[];
    'SBD': string[];
    'SGD': string[];
    'SRD': string[];
    'TTD': string[];
    'TWD': (string | undefined)[];
    'USD': string[];
    'UYU': string[];
    'WST': string[];
    'XCD': (string | undefined)[];
    'XPF': string[];
    'ZMW': (string | undefined)[];
} | undefined)[];
export default _default;
