{"version": 3, "file": "animations.umd.js", "sources": ["../../../../../packages/animations/src/animation_builder.ts", "../../../../../packages/animations/src/animation_metadata.ts", "../../../../../packages/animations/src/util.ts", "../../../../../packages/animations/src/players/animation_player.ts", "../../../../../packages/animations/src/players/animation_group_player.ts", "../../../../../packages/animations/src/private_export.ts", "../../../../../packages/animations/src/animations.ts", "../../../../../packages/animations/public_api.ts", "../../../../../packages/animations/index.ts", "../../../../../packages/animations/animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadata, AnimationOptions} from './animation_metadata';\nimport {AnimationPlayer} from './players/animation_player';\n\n/**\n * An injectable service that produces an animation sequence programmatically within an\n * Angular component or directive.\n * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n *\n * @usageNotes\n *\n * To use this service, add it to your component or directive as a dependency.\n * The service is instantiated along with your component.\n *\n * Apps do not typically need to create their own animation players, but if you\n * do need to, follow these steps:\n *\n * 1. Use the `build()` method to create a programmatic animation using the\n * `animate()` function. The method returns an `AnimationFactory` instance.\n *\n * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n *\n * 3. Use the player object to control the animation programmatically.\n *\n * For example:\n *\n * ```ts\n * // import the service from BrowserAnimationsModule\n * import {AnimationBuilder} from '@angular/animations';\n * // require the service as a dependency\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first define a reusable animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // use the returned factory object to create a player\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport abstract class AnimationBuilder {\n  /**\n   * Builds a factory for producing a defined animation.\n   * @param animation A reusable animation definition.\n   * @returns A factory object that can create a player for the defined animation.\n   * @see `animate()`\n   */\n  abstract build(animation: AnimationMetadata|AnimationMetadata[]): AnimationFactory;\n}\n\n/**\n * A factory object returned from the `AnimationBuilder`.`build()` method.\n *\n * @publicApi\n */\nexport abstract class AnimationFactory {\n  /**\n   * Creates an `AnimationPlayer` instance for the reusable animation defined by\n   * the `AnimationBuilder`.`build()` method that created this factory.\n   * Attaches the new player a DOM element.\n   * @param element The DOM element to which to attach the animation.\n   * @param options A set of options that can include a time delay and\n   * additional developer-defined parameters.\n   */\n  abstract create(element: any, options?: AnimationOptions): AnimationPlayer;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents a set of CSS styles for use in an animation style.\n */\nexport interface ÉµStyleData { [key: string]: string|number; }\n\n/**\n * Represents animation-step timing parameters for an animation step.\n * @see `animate()`\n *\n * @publicApi\n */\nexport declare type AnimateTimings = {\n  /**\n   * The full duration of an animation step. A number and optional time unit,\n   * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n   * The default unit is milliseconds.\n   */\n  duration: number,\n  /**\n   * The delay in applying an animation step. A number and optional time unit.\n   * The default unit is milliseconds.\n   */\n  delay: number,\n  /**\n   * An easing style that controls how an animations step accelerates\n   * and decelerates during its run time. An easing function such as `cubic-bezier()`,\n   * or one of the following constants:\n   * - `ease-in`\n   * - `ease-out`\n   * - `ease-in-and-out`\n   */\n  easing: string | null\n};\n\n/**\n * @description Options that control animation styling and timing.\n *\n * The following animation functions accept `AnimationOptions` data:\n *\n * - `transition()`\n * - `sequence()`\n * - `{@link animations/group group()}`\n * - `query()`\n * - `animation()`\n * - `useAnimation()`\n * - `animateChild()`\n *\n * Programmatic animations built using the `AnimationBuilder` service also\n * make use of `AnimationOptions`.\n *\n * @publicApi\n */\nexport declare interface AnimationOptions {\n  /**\n   * Sets a time-delay for initiating an animation action.\n   * A number and optional time unit, such as \"1s\" or \"10ms\" for one second\n   * and 10 milliseconds, respectively.The default unit is milliseconds.\n   * Default value is 0, meaning no delay.\n   */\n  delay?: number|string;\n  /**\n  * A set of developer-defined parameters that modify styling and timing\n  * when an animation action starts. An array of key-value pairs, where the provided value\n  * is used as a default.\n  */\n  params?: {[name: string]: any};\n}\n\n/**\n * Adds duration options to control animation styling and timing for a child animation.\n *\n * @see `animateChild()`\n *\n * @publicApi\n */\nexport declare interface AnimateChildOptions extends AnimationOptions { duration?: number|string; }\n\n/**\n * @description Constants for the categories of parameters that can be defined for animations.\n *\n * A corresponding function defines a set of parameters for each category, and\n * collects them into a corresponding `AnimationMetadata` object.\n *\n * @publicApi\n */\nexport const enum AnimationMetadataType {\n  /**\n   * Associates a named animation state with a set of CSS styles.\n   * See `state()`\n   */\n  State = 0,\n  /**\n   * Data for a transition from one animation state to another.\n   * See `transition()`\n   */\n  Transition = 1,\n  /**\n   * Contains a set of animation steps.\n   * See `sequence()`\n   */\n  Sequence = 2,\n  /**\n   * Contains a set of animation steps.\n   * See `{@link animations/group group()}`\n   */\n  Group = 3,\n  /**\n   * Contains an animation step.\n   * See `animate()`\n   */\n  Animate = 4,\n  /**\n   * Contains a set of animation steps.\n   * See `keyframes()`\n   */\n  Keyframes = 5,\n  /**\n   * Contains a set of CSS property-value pairs into a named style.\n   * See `style()`\n   */\n  Style = 6,\n  /**\n   * Associates an animation with an entry trigger that can be attached to an element.\n   * See `trigger()`\n   */\n  Trigger = 7,\n  /**\n   * Contains a re-usable animation.\n   * See `animation()`\n   */\n  Reference = 8,\n  /**\n   * Contains data to use in executing child animations returned by a query.\n   * See `animateChild()`\n   */\n  AnimateChild = 9,\n  /**\n   * Contains animation parameters for a re-usable animation.\n   * See `useAnimation()`\n   */\n  AnimateRef = 10,\n  /**\n   * Contains child-animation query data.\n   * See `query()`\n   */\n  Query = 11,\n  /**\n   * Contains data for staggering an animation sequence.\n   * See `stagger()`\n   */\n  Stagger = 12\n}\n\n/**\n * Specifies automatic styling.\n *\n * @publicApi\n */\nexport const AUTO_STYLE = '*';\n\n/**\n * Base for animation data structures.\n *\n * @publicApi\n */\nexport interface AnimationMetadata { type: AnimationMetadataType; }\n\n/**\n * Contains an animation trigger. Instantiated and returned by the\n * `trigger()` function.\n *\n * @publicApi\n */\nexport interface AnimationTriggerMetadata extends AnimationMetadata {\n  /**\n    * The trigger name, used to associate it with an element. Unique within the component.\n    */\n  name: string;\n  /**\n   * An animation definition object, containing an array of state and transition declarations.\n   */\n  definitions: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: {params?: {[name: string]: any}}|null;\n}\n\n/**\n * Encapsulates an animation state by associating a state name with a set of CSS styles.\n * Instantiated and returned by the `state()` function.\n *\n * @publicApi\n */\nexport interface AnimationStateMetadata extends AnimationMetadata {\n  /**\n   * The state name, unique within the component.\n   */\n  name: string;\n  /**\n   *  The CSS styles associated with this state.\n   */\n  styles: AnimationStyleMetadata;\n  /**\n   * An options object containing\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation.\n   */\n  options?: {params: {[name: string]: any}};\n}\n\n/**\n * Encapsulates an animation transition. Instantiated and returned by the\n * `transition()` function.\n *\n * @publicApi\n */\nexport interface AnimationTransitionMetadata extends AnimationMetadata {\n  /**\n   * An expression that describes a state change.\n   */\n  expr: string|\n      ((fromState: string, toState: string, element?: any,\n        params?: {[key: string]: any}) => boolean);\n  /**\n   * One or more animation objects to which this transition applies.\n   */\n  animation: AnimationMetadata|AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates a reusable animation, which is a collection of individual animation steps.\n * Instantiated and returned by the `animation()` function, and\n * passed to the `useAnimation()` function.\n *\n * @publicApi\n */\nexport interface AnimationReferenceMetadata extends AnimationMetadata {\n  /**\n   *  One or more animation step objects.\n   */\n  animation: AnimationMetadata|AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates an animation query. Instantiated and returned by\n * the `query()` function.\n *\n * @publicApi\n */\nexport interface AnimationQueryMetadata extends AnimationMetadata {\n  /**\n   *  The CSS selector for this query.\n   */\n  selector: string;\n  /**\n   * One or more animation step objects.\n   */\n  animation: AnimationMetadata|AnimationMetadata[];\n  /**\n   * A query options object.\n   */\n  options: AnimationQueryOptions|null;\n}\n\n/**\n * Encapsulates a keyframes sequence. Instantiated and returned by\n * the `keyframes()` function.\n *\n * @publicApi\n */\nexport interface AnimationKeyframesSequenceMetadata extends AnimationMetadata {\n  /**\n   * An array of animation styles.\n   */\n  steps: AnimationStyleMetadata[];\n}\n\n/**\n * Encapsulates an animation style. Instantiated and returned by\n * the `style()` function.\n *\n * @publicApi\n */\nexport interface AnimationStyleMetadata extends AnimationMetadata {\n  /**\n   * A set of CSS style properties.\n   */\n  styles: '*'|{[key: string]: string | number}|Array<{[key: string]: string | number}|'*'>;\n  /**\n   * A percentage of the total animate time at which the style is to be applied.\n   */\n  offset: number|null;\n}\n\n/**\n * Encapsulates an animation step. Instantiated and returned by\n * the `animate()` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateMetadata extends AnimationMetadata {\n  /**\n   * The timing data for the step.\n   */\n  timings: string|number|AnimateTimings;\n  /**\n   * A set of styles used in the step.\n   */\n  styles: AnimationStyleMetadata|AnimationKeyframesSequenceMetadata|null;\n}\n\n/**\n * Encapsulates a child animation, that can be run explicitly when the parent is run.\n * Instantiated and returned by the `animateChild` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateChildMetadata extends AnimationMetadata {\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates a reusable animation.\n * Instantiated and returned by the `useAnimation()` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateRefMetadata extends AnimationMetadata {\n  /**\n   * An animation reference object.\n   */\n  animation: AnimationReferenceMetadata;\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates an animation sequence.\n * Instantiated and returned by the `sequence()` function.\n *\n * @publicApi\n */\nexport interface AnimationSequenceMetadata extends AnimationMetadata {\n  /**\n   *  An array of animation step objects.\n   */\n  steps: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates an animation group.\n * Instantiated and returned by the `{@link animations/group group()}` function.\n *\n * @publicApi\n */\nexport interface AnimationGroupMetadata extends AnimationMetadata {\n  /**\n   * One or more animation or style steps that form this group.\n   */\n  steps: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions|null;\n}\n\n/**\n * Encapsulates animation query options.\n * Passed to the `query()` function.\n *\n * @publicApi\n */\nexport declare interface AnimationQueryOptions extends AnimationOptions {\n  /**\n   * True if this query is optional, false if it is required. Default is false.\n   * A required query throws an error if no elements are retrieved when\n   * the query is executed. An optional query does not.\n   *\n   */\n  optional?: boolean;\n  /**\n   * A maximum total number of results to return from the query.\n   * If negative, results are limited from the end of the query list towards the beginning.\n   * By default, results are not limited.\n   */\n  limit?: number;\n}\n\n/**\n * Encapsulates parameters for staggering the start times of a set of animation steps.\n * Instantiated and returned by the `stagger()` function.\n *\n * @publicApi\n **/\nexport interface AnimationStaggerMetadata extends AnimationMetadata {\n  /**\n   * The timing data for the steps.\n   */\n  timings: string|number;\n  /**\n   * One or more animation steps.\n   */\n  animation: AnimationMetadata|AnimationMetadata[];\n}\n\n/**\n * Creates a named animation trigger, containing a  list of `state()`\n * and `transition()` entries to be evaluated when the expression\n * bound to the trigger changes.\n *\n * @param name An identifying string.\n * @param definitions  An animation definition object, containing an array of `state()`\n * and `transition()` declarations.\n *\n * @return An object that encapsulates the trigger data.\n *\n * @usageNotes\n * Define an animation trigger in the `animations` section of `@Component` metadata.\n * In the template, reference the trigger by name and bind it to a trigger expression that\n * evaluates to a defined animation state, using the following format:\n *\n * `[@triggerName]=\"expression\"`\n *\n * Animation trigger bindings convert all values to strings, and then match the\n * previous and current values against any linked transitions.\n * Booleans can be specified as `1` or `true` and `0` or `false`.\n *\n * ### Usage Example\n *\n * The following example creates an animation trigger reference based on the provided\n * name value.\n * The provided animation value is expected to be an array consisting of state and\n * transition declarations.\n *\n * ```typescript\n * @Component({\n *   selector: \"my-component\",\n *   templateUrl: \"my-component-tpl.html\",\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component makes use of the defined trigger\n * by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ### Using an inline function\n * The `transition` animation method also supports reading an inline function which can decide\n * if its associated animation should be run.\n *\n * ```typescript\n * // this method is run each time the `myAnimationTrigger` trigger value changes.\n * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n string]: any}): boolean {\n *   // notice that `element` and `params` are also available here\n *   return toState == 'yes-please-animate';\n * }\n *\n * @Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger('myAnimationTrigger', [\n *       transition(myInlineMatcherFn, [\n *         // the animation sequence code\n *       ]),\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"yes-please-animate\";\n * }\n * ```\n *\n * ### Disabling Animations\n * When true, the special animation control binding `@.disabled` binding prevents\n * all animations from rendering.\n * Place the  `@.disabled` binding on an element to disable\n * animations on the element itself, as well as any inner animation triggers\n * within the element.\n *\n * The following example shows how to use this feature:\n *\n * ```typescript\n * @Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [@.disabled]=\"isDisabled\">\n *       <div [@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n * along with any inner animations.\n *\n * ### Disable animations application-wide\n * When an area of the template is set to have animations disabled,\n * **all** inner components have their animations disabled as well.\n * This means that you can disable all animations for an app\n * by placing a host binding set on `@.disabled` on the topmost Angular component.\n *\n * ```typescript\n * import {Component, HostBinding} from '@angular/core';\n *\n * @Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   @HostBinding('@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### Overriding disablement of inner animations\n * Despite inner animations being disabled, a parent animation can `query()`\n * for inner elements located in disabled areas of the template and still animate\n * them if needed. This is also the case for when a sub animation is\n * queried by a parent and then later animated using `animateChild()`.\n *\n * ### Detecting when an animation is disabled\n * If a region of the DOM (or the entire application) has its animations disabled, the animation\n * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n * an instance of an `AnimationEvent`. If animations are disabled,\n * the `.disabled` flag on the event is true.\n *\n * @publicApi\n */\nexport function trigger(name: string, definitions: AnimationMetadata[]): AnimationTriggerMetadata {\n  return {type: AnimationMetadataType.Trigger, name, definitions, options: {}};\n}\n\n/**\n * Defines an animation step that combines styling information with timing information.\n *\n * @param timings Sets `AnimateTimings` for the parent animation.\n * A string in the format \"duration [delay] [easing]\".\n *  - Duration and delay are expressed as a number and optional time unit,\n * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n * The default unit is milliseconds.\n *  - The easing value controls how the animation accelerates and decelerates\n * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n * `ease-in-out`, or a `cubic-bezier()` function call.\n * If not supplied, no easing is applied.\n *\n * For example, the string \"1s 100ms ease-out\" specifies a duration of\n * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n * which decelerates near the end of the duration.\n * @param styles Sets AnimationStyles for the parent animation.\n * A function call to either `style()` or `keyframes()`\n * that returns a collection of CSS style entries to be applied to the parent animation.\n * When null, uses the styles from the destination state.\n * This is useful when describing an animation step that will complete an animation;\n * see \"Animating to the final state\" in `transitions()`.\n * @returns An object that encapsulates the animation step.\n *\n * @usageNotes\n * Call within an animation `sequence()`, `{@link animations/group group()}`, or\n * `transition()` call to specify an animation step\n * that applies given style data to the parent animation for a given amount of time.\n *\n * ### Syntax Examples\n * **Timing examples**\n *\n * The following examples show various `timings` specifications.\n * - `animate(500)` : Duration is 500 milliseconds.\n * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n * milliseconds, easing according to a bezier curve.\n *\n * **Style examples**\n *\n * The following example calls `style()` to set a single CSS style.\n * ```typescript\n * animate(500, style({ background: \"red\" }))\n * ```\n * The following example calls `keyframes()` to set a CSS style\n * to different values for successive keyframes.\n * ```typescript\n * animate(500, keyframes(\n *  [\n *   style({ background: \"blue\" })),\n *   style({ background: \"red\" }))\n *  ])\n * ```\n *\n * @publicApi\n */\nexport function animate(\n    timings: string | number, styles: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata |\n        null = null): AnimationAnimateMetadata {\n  return {type: AnimationMetadataType.Animate, styles, timings};\n}\n\n/**\n * @description Defines a list of animation steps to be run in parallel.\n *\n * @param steps An array of animation step objects.\n * - When steps are defined by `style()` or `animate()`\n * function calls, each call within the group is executed instantly.\n * - To specify offset styles to be applied at a later time, define steps with\n * `keyframes()`, or use `animate()` calls with a delay value.\n * For example:\n *\n * ```typescript\n * group([\n *   animate(\"1s\", style({ background: \"black\" })),\n *   animate(\"2s\", style({ color: \"white\" }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the group data.\n *\n * @usageNotes\n * Grouped animations are useful when a series of styles must be\n * animated at different starting times and closed off at different ending times.\n *\n * When called within a `sequence()` or a\n * `transition()` call, does not continue to the next\n * instruction until all of the inner animation steps have completed.\n *\n * @publicApi\n */\nexport function group(\n    steps: AnimationMetadata[], options: AnimationOptions | null = null): AnimationGroupMetadata {\n  return {type: AnimationMetadataType.Group, steps, options};\n}\n\n/**\n * Defines a list of animation steps to be run sequentially, one by one.\n *\n * @param steps An array of animation step objects.\n * - Steps defined by `style()` calls apply the styling data immediately.\n * - Steps defined by `animate()` calls apply the styling data over time\n *   as specified by the timing data.\n *\n * ```typescript\n * sequence([\n *   style({ opacity: 0 })),\n *   animate(\"1s\", style({ opacity: 1 }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the sequence data.\n *\n * @usageNotes\n * When you pass an array of steps to a\n * `transition()` call, the steps run sequentially by default.\n * Compare this to the `{@link animations/group group()}` call, which runs animation steps in parallel.\n *\n * When a sequence is used within a `{@link animations/group group()}` or a `transition()` call,\n * execution continues to the next instruction only after each of the inner animation\n * steps have completed.\n *\n * @publicApi\n **/\nexport function sequence(steps: AnimationMetadata[], options: AnimationOptions | null = null):\n    AnimationSequenceMetadata {\n  return {type: AnimationMetadataType.Sequence, steps, options};\n}\n\n/**\n * Declares a key/value object containing CSS properties/styles that\n * can then be used for an animation `state`, within an animation `sequence`,\n * or as styling data for calls to `animate()` and `keyframes()`.\n *\n * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n * The value can be any of the following:\n * - A key-value style pair associating a CSS property with a value.\n * - An array of key-value style pairs.\n * - An asterisk (*), to use auto-styling, where styles are derived from the element\n * being animated and applied to the animation when it starts.\n *\n * Auto-styling can be used to define a state that depends on layout or other\n * environmental factors.\n *\n * @return An object that encapsulates the style data.\n *\n * @usageNotes\n * The following examples create animation styles that collect a set of\n * CSS property values:\n *\n * ```typescript\n * // string values for CSS properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical pixel values\n * style({ width: 100, height: 0 })\n * ```\n *\n * The following example uses auto-styling to allow a component to animate from\n * a height of 0 up to the height of the parent element:\n *\n * ```\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * @publicApi\n **/\nexport function style(\n    tokens: '*' | {[key: string]: string | number} |\n    Array<'*'|{[key: string]: string | number}>): AnimationStyleMetadata {\n  return {type: AnimationMetadataType.Style, styles: tokens, offset: null};\n}\n\n/**\n * Declares an animation state within a trigger attached to an element.\n *\n * @param name One or more names for the defined state in a comma-separated string.\n * The following reserved state names can be supplied to define a style for specific use\n * cases:\n *\n * - `void` You can associate styles with this name to be used when\n * the element is detached from the application. For example, when an `ngIf` evaluates\n * to false, the state of the associated element is void.\n *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n * to be used as the fallback when the state that is being animated is not declared\n * within the trigger.\n *\n * @param styles A set of CSS styles associated with this state, created using the\n * `style()` function.\n * This set of styles persists on the element once the state has been reached.\n * @param options Parameters that can be passed to the state when it is invoked.\n * 0 or more key-value pairs.\n * @return An object that encapsulates the new state data.\n *\n * @usageNotes\n * Use the `trigger()` function to register states to an animation trigger.\n * Use the `transition()` function to animate between states.\n * When a state is active within a component, its associated styles persist on the element,\n * even when the animation ends.\n *\n * @publicApi\n **/\nexport function state(\n    name: string, styles: AnimationStyleMetadata,\n    options?: {params: {[name: string]: any}}): AnimationStateMetadata {\n  return {type: AnimationMetadataType.State, name, styles, options};\n}\n\n/**\n * Defines a set of animation styles, associating each style with an optional `offset` value.\n *\n * @param steps A set of animation styles with optional offset data.\n * The optional `offset` value for a style specifies a percentage of the total animation\n * time at which that style is applied.\n * @returns An object that encapsulates the keyframes data.\n *\n * @usageNotes\n * Use with the `animate()` call. Instead of applying animations\n * from the current state\n * to the destination state, keyframes describe how each style entry is applied and at what point\n * within the animation arc.\n * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n *\n * ### Usage\n *\n * In the following example, the offset values describe\n * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n * blue when 20% of the total time has elapsed.\n *\n * ```typescript\n * // the provided offset values\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * If there are no `offset` values specified in the style entries, the offsets\n * are calculated automatically.\n *\n * ```typescript\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n *```\n\n * @publicApi\n */\nexport function keyframes(steps: AnimationStyleMetadata[]): AnimationKeyframesSequenceMetadata {\n  return {type: AnimationMetadataType.Keyframes, steps};\n}\n\n/**\n * Declares an animation transition as a sequence of animation steps to run when a given\n * condition is satisfied. The condition is a Boolean expression or function that compares\n * the previous and current animation states, and returns true if this transition should occur.\n * When the state criteria of a defined transition are met, the associated animation is\n * triggered.\n *\n * @param stateChangeExpr A Boolean expression or function that compares the previous and current\n * animation states, and returns true if this transition should occur. Note that  \"true\" and \"false\"\n * match 1 and 0, respectively. An expression is evaluated each time a state change occurs in the\n * animation trigger element.\n * The animation steps run when the expression evaluates to true.\n *\n * - A state-change string takes the form \"state1 => state2\", where each side is a defined animation\n * state, or an asterix (*) to refer to a dynamic start or end state.\n *   - The expression string can contain multiple comma-separated statements;\n * for example \"state1 => state2, state3 => state4\".\n *   - Special values `:enter` and `:leave` initiate a transition on the entry and exit states,\n * equivalent to  \"void => *\"  and \"* => void\".\n *   - Special values `:increment` and `:decrement` initiate a transition when a numeric value has\n * increased or decreased in value.\n * - A function is executed each time a state change occurs in the animation trigger element.\n * The animation steps run when the function returns true.\n *\n * @param steps One or more animation objects, as returned by the `animate()` or\n * `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the animation,\n * and additional developer-defined parameters. Provided values for additional parameters are used\n * as defaults, and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the transition data.\n *\n * @usageNotes\n * The template associated with a component binds an animation trigger to an element.\n *\n * ```HTML\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * All transitions are defined within an animation trigger,\n * along with named states that the transitions change to and from.\n *\n * ```typescript\n * trigger(\"myAnimationTrigger\", [\n *  // define states\n *  state(\"on\", style({ background: \"green\" })),\n *  state(\"off\", style({ background: \"grey\" })),\n *  ...]\n * ```\n *\n * Note that when you call the `sequence()` function within a `{@link animations/group group()}`\n * or a `transition()` call, execution does not continue to the next instruction\n * until each of the inner animation steps have completed.\n *\n * ### Syntax examples\n *\n * The following examples define transitions between the two defined states (and default states),\n * using various options:\n *\n * ```typescript\n * // Transition occurs when the state value\n * // bound to \"myAnimationTrigger\" changes from \"on\" to \"off\"\n * transition(\"on => off\", animate(500))\n * // Run the same animation for both directions\n * transition(\"on <=> off\", animate(500))\n * // Define multiple state-change pairs separated by commas\n * transition(\"on => off, off => void\", animate(500))\n * ```\n *\n * ### Special values for state-change expressions\n *\n * - Catch-all state change for when an element is inserted into the page and the\n * destination state is unknown:\n *\n * ```typescript\n * transition(\"void => *\", [\n *  style({ opacity: 0 }),\n *  animate(500)\n *  ])\n * ```\n *\n * - Capture a state change between any states:\n *\n *  `transition(\"* => *\", animate(\"1s 0s\"))`\n *\n * - Entry and exit transitions:\n *\n * ```typescript\n * transition(\":enter\", [\n *   style({ opacity: 0 }),\n *   animate(500, style({ opacity: 1 }))\n *   ]),\n * transition(\":leave\", [\n *   animate(500, style({ opacity: 0 }))\n *   ])\n * ```\n *\n * - Use `:increment` and `:decrement` to initiate transitions:\n *\n * ```typescript\n * transition(\":increment\", group([\n *  query(':enter', [\n *     style({ left: '100%' }),\n *     animate('0.5s ease-out', style('*'))\n *   ]),\n *  query(':leave', [\n *     animate('0.5s ease-out', style({ left: '-100%' }))\n *  ])\n * ]))\n *\n * transition(\":decrement\", group([\n *  query(':enter', [\n *     style({ left: '100%' }),\n *     animate('0.5s ease-out', style('*'))\n *   ]),\n *  query(':leave', [\n *     animate('0.5s ease-out', style({ left: '-100%' }))\n *  ])\n * ]))\n * ```\n *\n * ### State-change functions\n *\n * Here is an example of a `fromState` specified as a state-change function that invokes an\n * animation when true:\n *\n * ```typescript\n * transition((fromState, toState) =>\n *  {\n *   return fromState == \"off\" && toState == \"on\";\n *  },\n *  animate(\"1s 0s\"))\n * ```\n *\n * ### Animating to the final state\n *\n * If the final step in a transition is a call to `animate()` that uses a timing value\n * with no style data, that step is automatically considered the final animation arc,\n * for the element to reach the final state. Angular automatically adds or removes\n * CSS styles to ensure that the element is in the correct final state.\n *\n * The following example defines a transition that starts by hiding the element,\n * then makes sure that it animates properly to whatever state is currently active for trigger:\n *\n * ```typescript\n * transition(\"void => *\", [\n *   style({ opacity: 0 }),\n *   animate(500)\n *  ])\n * ```\n * ### Boolean value matching\n * If a trigger binding value is a Boolean, it can be matched using a transition expression\n * that compares true and false or 1 and 0. For example:\n *\n * ```\n * // in the template\n * <div [@openClose]=\"open ? true : false\">...</div>\n * // in the component metadata\n * trigger('openClose', [\n *   state('true', style({ height: '*' })),\n *   state('false', style({ height: '0px' })),\n *   transition('false <=> true', animate(500))\n * ])\n * ```\n *\n * @publicApi\n **/\nexport function transition(\n    stateChangeExpr: string | ((fromState: string, toState: string, element?: any,\n                                params?: {[key: string]: any}) => boolean),\n    steps: AnimationMetadata | AnimationMetadata[],\n    options: AnimationOptions | null = null): AnimationTransitionMetadata {\n  return {type: AnimationMetadataType.Transition, expr: stateChangeExpr, animation: steps, options};\n}\n\n/**\n * Produces a reusable animation that can be invoked in another animation or sequence,\n * by calling the `useAnimation()` function.\n *\n * @param steps One or more animation objects, as returned by the `animate()`\n * or `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional developer-defined parameters.\n * Provided values for additional parameters are used as defaults,\n * and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the animation data.\n *\n * @usageNotes\n * The following example defines a reusable animation, providing some default parameter\n * values.\n *\n * ```typescript\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *   style({ opacity: '{{ end }}'}))\n *   ],\n *   { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * The following invokes the defined animation with a call to `useAnimation()`,\n * passing in override parameter values.\n *\n * ```js\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If any of the passed-in parameter values are missing from this call,\n * the default values are used. If one or more parameter values are missing before a step is\n * animated, `useAnimation()` throws an error.\n *\n * @publicApi\n */\nexport function animation(\n    steps: AnimationMetadata | AnimationMetadata[],\n    options: AnimationOptions | null = null): AnimationReferenceMetadata {\n  return {type: AnimationMetadataType.Reference, animation: steps, options};\n}\n\n/**\n * Executes a queried inner animation element within an animation sequence.\n *\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional override values for developer-defined parameters.\n * @return An object that encapsulates the child animation data.\n *\n * @usageNotes\n * Each time an animation is triggered in Angular, the parent animation\n * has priority and any child animations are blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations, and run them using this function.\n *\n * Note that this feature is designed to be used with `query()` and it will only work\n * with animations that are assigned using the Angular animation library. CSS keyframes\n * and transitions are not handled by this API.\n *\n * @publicApi\n */\nexport function animateChild(options: AnimateChildOptions | null = null):\n    AnimationAnimateChildMetadata {\n  return {type: AnimationMetadataType.AnimateChild, options};\n}\n\n/**\n * Starts a reusable animation that is created using the `animation()` function.\n *\n * @param animation The reusable animation to start.\n * @param options An options object that can contain a delay value for the start of\n * the animation, and additional override values for developer-defined parameters.\n * @return An object that contains the animation parameters.\n *\n * @publicApi\n */\nexport function useAnimation(\n    animation: AnimationReferenceMetadata,\n    options: AnimationOptions | null = null): AnimationAnimateRefMetadata {\n  return {type: AnimationMetadataType.AnimateRef, animation, options};\n}\n\n/**\n * Finds one or more inner elements within the current element that is\n * being animated within a sequence. Use with `animate()`.\n *\n * @param selector The element to query, or a set of elements that contain Angular-specific\n * characteristics, specified with one or more of the following tokens.\n *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements.\n *  - `query(\":animating\")` : Query all currently animating elements.\n *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n *  - `query(\":self\")` : Include the current element into the animation sequence.\n *\n * @param animation One or more animation steps to apply to the queried element or elements.\n * An array is treated as an animation sequence.\n * @param options An options object. Use the 'limit' field to limit the total number of\n * items to collect.\n * @return An object that encapsulates the query data.\n *\n * @usageNotes\n * Tokens can be merged into a combined query selector string. For example:\n *\n * ```typescript\n *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n * ```\n *\n * The `query()` function collects multiple elements and works internally by using\n * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n * the total number of items to be collected. For example:\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * By default, throws an error when zero items are found. Set the\n * `optional` flag to ignore this error. For example:\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Usage Example\n *\n * The following example queries for inner elements and animates them\n * individually using `animate()`. \n *\n * ```typescript\n * @Component({\n *   selector: 'inner',\n *   template: `\n *     <div [@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 }))),\n *        query('.content', animate(1000, style({ opacity: 1 }))),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport function query(\n    selector: string, animation: AnimationMetadata | AnimationMetadata[],\n    options: AnimationQueryOptions | null = null): AnimationQueryMetadata {\n  return {type: AnimationMetadataType.Query, selector, animation, options};\n}\n\n/**\n * Use within an animation `query()` call to issue a timing gap after\n * each queried item is animated.\n *\n * @param timings A delay value.\n * @param animation One ore more animation steps.\n * @returns An object that encapsulates the stagger data.\n *\n * @usageNotes\n * In the following example, a container element wraps a list of items stamped out\n * by an `ngFor`. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * Each time items are added, the opacity fade-in animation runs,\n * and each removed item is faded out.\n * When either of these animations occur, the stagger effect is\n * applied after each item's animation is started.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n *\n * Here is the component code:\n *\n * ```typescript\n * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n * @Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *     ...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *    }\n *  }\n * ```\n *\n * Here is the animation trigger code:\n *\n * ```typescript\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * @publicApi\n */\nexport function stagger(\n    timings: string | number,\n    animation: AnimationMetadata | AnimationMetadata[]): AnimationStaggerMetadata {\n  return {type: AnimationMetadataType.Stagger, timings, animation};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport function scheduleMicroTask(cb: () => any) {\n  Promise.resolve(null).then(cb);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {scheduleMicroTask} from '../util';\n\n/**\n * Provides programmatic control of a reusable animation sequence,\n * built using the `build()` method of `AnimationBuilder`. The `build()` method\n * returns a factory, whose `create()` method instantiates and initializes this interface.\n *\n * @see `AnimationBuilder`\n * @see `AnimationFactory`\n * @see `animate()`\n *\n * @publicApi\n */\nexport interface AnimationPlayer {\n  /**\n   * Provides a callback to invoke when the animation finishes.\n   * @param fn The callback function.\n   * @see `finish()`\n   */\n  onDone(fn: () => void): void;\n  /**\n   * Provides a callback to invoke when the animation starts.\n   * @param fn The callback function.\n   * @see `run()`\n   */\n  onStart(fn: () => void): void;\n  /**\n   * Provides a callback to invoke after the animation is destroyed.\n   * @param fn The callback function.\n   * @see `destroy()`\n   * @see `beforeDestroy()`\n   */\n  onDestroy(fn: () => void): void;\n  /**\n   * Initializes the animation.\n   */\n  init(): void;\n  /**\n   * Reports whether the animation has started.\n   * @returns True if the animation has started, false otherwise.\n   */\n  hasStarted(): boolean;\n  /**\n   * Runs the animation, invoking the `onStart()` callback.\n   */\n  play(): void;\n  /**\n   * Pauses the animation.\n   */\n  pause(): void;\n  /**\n   * Restarts the paused animation.\n   */\n  restart(): void;\n  /**\n   * Ends the animation, invoking the `onDone()` callback.\n   */\n  finish(): void;\n  /**\n   * Destroys the animation, after invoking the `beforeDestroy()` callback.\n   * Calls the `onDestroy()` callback when destruction is completed.\n   */\n  destroy(): void;\n  /**\n   * Resets the animation to its initial state.\n   */\n  reset(): void;\n  /**\n   * Sets the position of the animation.\n   * @param position A 0-based offset into the duration, in milliseconds.\n   */\n  setPosition(position: any /** TODO #9100 */): void;\n  /**\n   * Reports the current position of the animation.\n   * @returns A 0-based offset into the duration, in milliseconds.\n   */\n  getPosition(): number;\n  /**\n   * The parent of this player, if any.\n   */\n  parentPlayer: AnimationPlayer|null;\n  /**\n   * The total run time of the animation, in milliseconds.\n   */\n  readonly totalTime: number;\n  /**\n   * Provides a callback to invoke before the animation is destroyed.\n   */\n  beforeDestroy?: () => any;\n  /** @internal\n   * Internal\n   */\n  triggerCallback?: (phaseName: string) => void;\n  /** @internal\n   * Internal\n   */\n  disabled?: boolean;\n}\n\n/**\n * An empty programmatic controller for reusable animations.\n * Used internally when animations are disabled, to avoid\n * checking for the null case when an animation player is expected.\n *\n * @see `animate()`\n * @see `AnimationPlayer`\n * @see `GroupPlayer`\n *\n * @publicApi\n */\nexport class NoopAnimationPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n  private _started = false;\n  private _destroyed = false;\n  private _finished = false;\n  public parentPlayer: AnimationPlayer|null = null;\n  public readonly totalTime: number;\n  constructor(duration: number = 0, delay: number = 0) { this.totalTime = duration + delay; }\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  onStart(fn: () => void): void { this._onStartFns.push(fn); }\n  onDone(fn: () => void): void { this._onDoneFns.push(fn); }\n  onDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n  hasStarted(): boolean { return this._started; }\n  init(): void {}\n  play(): void {\n    if (!this.hasStarted()) {\n      this._onStart();\n      this.triggerMicrotask();\n    }\n    this._started = true;\n  }\n\n  /** @internal */\n  triggerMicrotask() { scheduleMicroTask(() => this._onFinish()); }\n\n  private _onStart() {\n    this._onStartFns.forEach(fn => fn());\n    this._onStartFns = [];\n  }\n\n  pause(): void {}\n  restart(): void {}\n  finish(): void { this._onFinish(); }\n  destroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      if (!this.hasStarted()) {\n        this._onStart();\n      }\n      this.finish();\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  reset(): void {}\n  setPosition(position: number): void {}\n  getPosition(): number { return 0; }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {scheduleMicroTask} from '../util';\nimport {AnimationPlayer} from './animation_player';\n\n/**\n * A programmatic controller for a group of reusable animations.\n * Used internally to control animations.\n *\n * @see `AnimationPlayer`\n * @see `{@link animations/group group()}`\n *\n */\nexport class AnimationGroupPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _finished = false;\n  private _started = false;\n  private _destroyed = false;\n  private _onDestroyFns: Function[] = [];\n\n  public parentPlayer: AnimationPlayer|null = null;\n  public totalTime: number = 0;\n  public readonly players: AnimationPlayer[];\n\n  constructor(_players: AnimationPlayer[]) {\n    this.players = _players;\n    let doneCount = 0;\n    let destroyCount = 0;\n    let startCount = 0;\n    const total = this.players.length;\n\n    if (total == 0) {\n      scheduleMicroTask(() => this._onFinish());\n    } else {\n      this.players.forEach(player => {\n        player.onDone(() => {\n          if (++doneCount == total) {\n            this._onFinish();\n          }\n        });\n        player.onDestroy(() => {\n          if (++destroyCount == total) {\n            this._onDestroy();\n          }\n        });\n        player.onStart(() => {\n          if (++startCount == total) {\n            this._onStart();\n          }\n        });\n      });\n    }\n\n    this.totalTime = this.players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n  }\n\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n\n  init(): void { this.players.forEach(player => player.init()); }\n\n  onStart(fn: () => void): void { this._onStartFns.push(fn); }\n\n  private _onStart() {\n    if (!this.hasStarted()) {\n      this._started = true;\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n    }\n  }\n\n  onDone(fn: () => void): void { this._onDoneFns.push(fn); }\n\n  onDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n\n  hasStarted() { return this._started; }\n\n  play() {\n    if (!this.parentPlayer) {\n      this.init();\n    }\n    this._onStart();\n    this.players.forEach(player => player.play());\n  }\n\n  pause(): void { this.players.forEach(player => player.pause()); }\n\n  restart(): void { this.players.forEach(player => player.restart()); }\n\n  finish(): void {\n    this._onFinish();\n    this.players.forEach(player => player.finish());\n  }\n\n  destroy(): void { this._onDestroy(); }\n\n  private _onDestroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._onFinish();\n      this.players.forEach(player => player.destroy());\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n\n  reset(): void {\n    this.players.forEach(player => player.reset());\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n\n  setPosition(p: number): void {\n    const timeAtPosition = p * this.totalTime;\n    this.players.forEach(player => {\n      const position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n      player.setPosition(position);\n    });\n  }\n\n  getPosition(): number {\n    let min = 0;\n    this.players.forEach(player => {\n      const p = player.getPosition();\n      min = Math.min(p, min);\n    });\n    return min;\n  }\n\n  beforeDestroy(): void {\n    this.players.forEach(player => {\n      if (player.beforeDestroy) {\n        player.beforeDestroy();\n      }\n    });\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {AnimationGroupPlayer as ÉµAnimationGroupPlayer} from './players/animation_group_player';\nexport const ÉµPRE_STYLE = '!';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation package.\n */\nexport {AnimationBuilder, AnimationFactory} from './animation_builder';\nexport {AnimationEvent} from './animation_event';\nexport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, ÉµStyleData} from './animation_metadata';\nexport {AnimationPlayer, NoopAnimationPlayer} from './players/animation_player';\n\nexport * from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/animations';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;IAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;QAAA;SAQC;QAAD,uBAAC;IAAD,CAAC,IAAA;IAED;;;;;AAKA;QAAA;SAUC;QAAD,uBAAC;IAAD,CAAC;;ICjFD;;;;;;;IAiKA;;;;;AAKA,QAAa,UAAU,GAAG,GAAG,CAAC;IAuR9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA,aAAgB,OAAO,CAAC,IAAY,EAAE,WAAgC;QACpE,OAAO,EAAC,IAAI,mBAAiC,IAAI,MAAA,EAAE,WAAW,aAAA,EAAE,OAAO,EAAE,EAAE,EAAC,CAAC;IAC/E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,aAAgB,OAAO,CACnB,OAAwB,EAAE,MACX;QADW,uBAAA,EAAA,aACX;QACjB,OAAO,EAAC,IAAI,mBAAiC,MAAM,QAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,aAAgB,KAAK,CACjB,KAA0B,EAAE,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QACrE,OAAO,EAAC,IAAI,iBAA+B,KAAK,OAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,aAAgB,QAAQ,CAAC,KAA0B,EAAE,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QAE1F,OAAO,EAAC,IAAI,oBAAkC,KAAK,OAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,aAAgB,KAAK,CACjB,MAC2C;QAC7C,OAAO,EAAC,IAAI,iBAA+B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,aAAgB,KAAK,CACjB,IAAY,EAAE,MAA8B,EAC5C,OAAyC;QAC3C,OAAO,EAAC,IAAI,iBAA+B,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,aAAgB,SAAS,CAAC,KAA+B;QACvD,OAAO,EAAC,IAAI,qBAAmC,KAAK,OAAA,EAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA,aAAgB,UAAU,CACtB,eACsE,EACtE,KAA8C,EAC9C,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QACzC,OAAO,EAAC,IAAI,sBAAoC,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,SAAA,EAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,aAAgB,SAAS,CACrB,KAA8C,EAC9C,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QACzC,OAAO,EAAC,IAAI,qBAAmC,SAAS,EAAE,KAAK,EAAE,OAAO,SAAA,EAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;;;;;;;AAmBA,aAAgB,YAAY,CAAC,OAA0C;QAA1C,wBAAA,EAAA,cAA0C;QAErE,OAAO,EAAC,IAAI,wBAAsC,OAAO,SAAA,EAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;AAUA,aAAgB,YAAY,CACxB,SAAqC,EACrC,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QACzC,OAAO,EAAC,IAAI,uBAAoC,SAAS,WAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFA,aAAgB,KAAK,CACjB,QAAgB,EAAE,SAAkD,EACpE,OAA4C;QAA5C,wBAAA,EAAA,cAA4C;QAC9C,OAAO,EAAC,IAAI,kBAA+B,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,aAAgB,OAAO,CACnB,OAAwB,EACxB,SAAkD;QACpD,OAAO,EAAC,IAAI,oBAAiC,OAAO,SAAA,EAAE,SAAS,WAAA,EAAC,CAAC;IACnE,CAAC;;IC7xCD;;;;;;;AAOA,aAAgB,iBAAiB,CAAC,EAAa;QAC7C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC;;ICTD;;;;;;;AAOA,IAmGA;;;;;;;;;;;AAWA;QASE,6BAAY,QAAoB,EAAE,KAAiB;YAAvC,yBAAA,EAAA,YAAoB;YAAE,sBAAA,EAAA,SAAiB;YAR3C,eAAU,GAAe,EAAE,CAAC;YAC5B,gBAAW,GAAe,EAAE,CAAC;YAC7B,kBAAa,GAAe,EAAE,CAAC;YAC/B,aAAQ,GAAG,KAAK,CAAC;YACjB,eAAU,GAAG,KAAK,CAAC;YACnB,cAAS,GAAG,KAAK,CAAC;YACnB,iBAAY,GAAyB,IAAI,CAAC;YAEM,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;SAAE;QACnF,uCAAS,GAAjB;YACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;aACtB;SACF;QACD,qCAAO,GAAP,UAAQ,EAAc,IAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5D,oCAAM,GAAN,UAAO,EAAc,IAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1D,uCAAS,GAAT,UAAU,EAAc,IAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChE,wCAAU,GAAV,cAAwB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC/C,kCAAI,GAAJ,eAAe;QACf,kCAAI,GAAJ;YACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;;QAGD,8CAAgB,GAAhB;YAAA,iBAAiE;YAA5C,iBAAiB,CAAC,cAAM,OAAA,KAAI,CAAC,SAAS,EAAE,GAAA,CAAC,CAAC;SAAE;QAEzD,sCAAQ,GAAhB;YACE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;SACvB;QAED,mCAAK,GAAL,eAAgB;QAChB,qCAAO,GAAP,eAAkB;QAClB,oCAAM,GAAN,cAAiB,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACpC,qCAAO,GAAP;YACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;oBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;iBACjB;gBACD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;aACzB;SACF;QACD,mCAAK,GAAL,eAAgB;QAChB,yCAAW,GAAX,UAAY,QAAgB,KAAU;QACtC,yCAAW,GAAX,cAAwB,OAAO,CAAC,CAAC,EAAE;;QAGnC,6CAAe,GAAf,UAAgB,SAAiB;YAC/B,IAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC1E,OAAO,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SACpB;QACH,0BAAC;IAAD,CAAC;;ICnLD;;;;;;;AAQA,IAGA;;;;;;;;AAQA;QAYE,8BAAY,QAA2B;YAAvC,iBA8BC;YAzCO,eAAU,GAAe,EAAE,CAAC;YAC5B,gBAAW,GAAe,EAAE,CAAC;YAC7B,cAAS,GAAG,KAAK,CAAC;YAClB,aAAQ,GAAG,KAAK,CAAC;YACjB,eAAU,GAAG,KAAK,CAAC;YACnB,kBAAa,GAAe,EAAE,CAAC;YAEhC,iBAAY,GAAyB,IAAI,CAAC;YAC1C,cAAS,GAAW,CAAC,CAAC;YAI3B,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;YACxB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAElC,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,iBAAiB,CAAC,cAAM,OAAA,KAAI,CAAC,SAAS,EAAE,GAAA,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;oBACzB,MAAM,CAAC,MAAM,CAAC;wBACZ,IAAI,EAAE,SAAS,IAAI,KAAK,EAAE;4BACxB,KAAI,CAAC,SAAS,EAAE,CAAC;yBAClB;qBACF,CAAC,CAAC;oBACH,MAAM,CAAC,SAAS,CAAC;wBACf,IAAI,EAAE,YAAY,IAAI,KAAK,EAAE;4BAC3B,KAAI,CAAC,UAAU,EAAE,CAAC;yBACnB;qBACF,CAAC,CAAC;oBACH,MAAM,CAAC,OAAO,CAAC;wBACb,IAAI,EAAE,UAAU,IAAI,KAAK,EAAE;4BACzB,KAAI,CAAC,QAAQ,EAAE,CAAC;yBACjB;qBACF,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,MAAM,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,GAAA,EAAE,CAAC,CAAC,CAAC;SAC7F;QAEO,wCAAS,GAAjB;YACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;aACtB;SACF;QAED,mCAAI,GAAJ,cAAe,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,IAAI,EAAE,GAAA,CAAC,CAAC,EAAE;QAE/D,sCAAO,GAAP,UAAQ,EAAc,IAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAEpD,uCAAQ,GAAhB;YACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;aACvB;SACF;QAED,qCAAM,GAAN,UAAO,EAAc,IAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAE1D,wCAAS,GAAT,UAAU,EAAc,IAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAEhE,yCAAU,GAAV,cAAe,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;QAEtC,mCAAI,GAAJ;YACE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;YACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,IAAI,EAAE,GAAA,CAAC,CAAC;SAC/C;QAED,oCAAK,GAAL,cAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,KAAK,EAAE,GAAA,CAAC,CAAC,EAAE;QAEjE,sCAAO,GAAP,cAAkB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC,EAAE;QAErE,qCAAM,GAAN;YACE,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,MAAM,EAAE,GAAA,CAAC,CAAC;SACjD;QAED,sCAAO,GAAP,cAAkB,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;QAE9B,yCAAU,GAAlB;YACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC;gBACjD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;aACzB;SACF;QAED,oCAAK,GAAL;YACE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,KAAK,EAAE,GAAA,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;QAED,0CAAW,GAAX,UAAY,CAAS;YACnB,IAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;gBACzB,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACvF,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC9B,CAAC,CAAC;SACJ;QAED,0CAAW,GAAX;YACE,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;gBACzB,IAAM,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC/B,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aACxB,CAAC,CAAC;YACH,OAAO,GAAG,CAAC;SACZ;QAED,4CAAa,GAAb;YACE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;gBACzB,IAAI,MAAM,CAAC,aAAa,EAAE;oBACxB,MAAM,CAAC,aAAa,EAAE,CAAC;iBACxB;aACF,CAAC,CAAC;SACJ;;QAGD,8CAAe,GAAf,UAAgB,SAAiB;YAC/B,IAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC1E,OAAO,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SACpB;QACH,2BAAC;IAAD,CAAC;;IC5JD;;;;;;;AAOA,QACa,UAAU,GAAG,GAAG;;ICR7B;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;OAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}