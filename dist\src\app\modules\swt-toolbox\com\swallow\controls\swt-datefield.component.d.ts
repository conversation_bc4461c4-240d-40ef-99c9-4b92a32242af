import { AfterViewInit, ElementRef, EventEmitter, OnInit, Renderer2 } from '@angular/core';
import { CommonService } from "../utils/common.service";
export declare class SwtDateField implements OnInit, AfterViewInit {
    private elem;
    private commonService;
    private _renderer;
    originalValue: any;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    datefield: ElementRef;
    tabIndex: any;
    private _toolTipPreviousObject;
    restrict: any;
    toolTip: string;
    textDictionaryId: any;
    toolTipPreviousValue: any;
    id: any;
    private _toolTip;
    private firstCall;
    private _visibility;
    private logger;
    private _showOnFlag;
    private datePickerObject;
    private _showToDay;
    private _change;
    private interrupted;
    private openEventOutPut;
    closeEventOutPut: EventEmitter<Function>;
    private keyDownEventOutPut;
    private changeEventOutPut;
    private focusEventOutPut;
    private focusOutEventOutPut;
    private keyFocusChange;
    constructor(elem: ElementRef, commonService: CommonService, _renderer: Renderer2);
    private _enabled;
    /* enabled getter and setter */
    enabled: boolean;
    private _editable;
    /* editable getter and setter */
    editable: boolean;
    private _width;
    /* width getter and setter */
    width: string;
    private _text;
    /* text getter and setter */
    text: string;
    interruptComms: boolean;
    private _showYearSelect;
    /*=============================================== Getter and Setter ====================================================*/
    showYearSelect: boolean;
    private _showMonthSelect;
    /*--------------------------------------------------------*/
    showMonthSelect: boolean;
    private _textAlign;
    /* @Inputs */
    /* textAlign getter and setter */
    textAlign: string;
    private _formatString;
    /**
    * override function, this function sets date format string
    *
    * @param value: String
    */
    formatString: string;
    private _monthNames;
    /*--------------------------------------------------------*/
    monthNames: any;
    private _dayNames;
    /*--------------------------------------------------------*/
    dayNames: any;
    private _selectableRange;
    selectableRange: Object;
    private _open;
    /*--------------------------------------------------------*/
    open: Function;
    private _close;
    /*--------------------------------------------------------*/
    close: Function;
    private _keyDown;
    /*--------------------------------------------------------*/
    keyDown: Function;
    private _focus;
    /*--------------------------------------------------------*/
    focus: Function;
    private _focusOut;
    /*--------------------------------------------------------*/
    focusOut: Function;
    private _selectedDate;
    /*--------------------------------------------------------*/
    selectedDate: Date;
    showToday: boolean;
    /* input to hold component visibility */
    visible: boolean;
    /*--------------------------------------------------------*/
    onSelect: Function;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    /**
     * This method is used to set visibility of the component.
     * @param visibility
     */
    setVisible(visibility: boolean): void;
    /**
     * This function is used to parse the given date string to date
     * This function converts the given date string to MM/DD/YYYY
     * format and then parse the date
     *
     * param dateString: String
     * param formatString: String
     * @return Date
     */
    parseDate(dateString: string, formatString?: string): Date;
    /**
     * This method is used to set style
     * to component
     * @param proerty
     * @param value
     */
    setStyle(attribute: string, value: string): void;
    /**
     * This function formats the selected date to display
     *
     * param value: Date
     * @return String
     */
    formatDate(value: Date): string;
    /**
     * This method is used to show dropDown.
     */
    openDropDown(): void;
    /**
     * getAbbreviationFromDate
     *
     * This funtion is used to get abreviation from date when it is in DD-MMM-YYYY format
     */
    getAbbreviationFromDate(date: Date): string;
    /**
     * Returns the Date object
     */
    getDate(): Date;
    setFocus(): void;
    spyChanges(event: any): void;
    resetOriginalValue(): void;
    /**
     * This method is used to update date.
     */
    private updateDate;
    private loadingZero;
    /**
     * This method is used to detect date delimiter
     * and split the dateString.
     * <AUTHOR>
     * @param dateString
     */
    private spliter;
    private isValidDate;
}
