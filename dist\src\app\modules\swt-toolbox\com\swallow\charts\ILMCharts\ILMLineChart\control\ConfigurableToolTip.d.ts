import { OnInit, ElementRef, AfterViewInit } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { VBox } from '../../../../controls/swt-vbox.component';
import { SwtLabel } from '../../../../controls/swt-label.component';
export declare class ConfigurableToolTip extends Container implements OnInit, AfterViewInit {
    private elem;
    private commonService;
    customTooltip: VBox;
    dataForLabel: SwtLabel;
    dataForLabelValue: SwtLabel;
    labelnumberAccounts: SwtLabel;
    labelnumberAccountsValue: SwtLabel;
    labelnewDataExistFor: SwtLabel;
    labelnewDataExistForValue: SwtLabel;
    labelincomplete: SwtLabel;
    labelincompleteValue: SwtLabel;
    labelinconsistent: SwtLabel;
    labelinconsistentValue: SwtLabel;
    labelLastUpdate: SwtLabel;
    labelLastUpdateValue: SwtLabel;
    requestRecalculation: SwtLabel;
    dataArray: any;
    parentDocument: any;
    processBox: any;
    private swtAlert;
    recalculateEnable: boolean;
    private clickable;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    constructor(elem: ElementRef, commonService: CommonService);
    private boxRollOutEventListner;
    private createCustomTip;
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     */
    recalculateDataAlert(event: any): void;
    /**
     * This function is used to listen to the alert
     *
     * @param eventObj CloseEvent
     */
    private alertListener;
}
