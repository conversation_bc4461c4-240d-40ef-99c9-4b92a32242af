{"version": 3, "sources": ["packages/animations/animations.umd.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "self", "ng", "animations", "this", "AnimationBuilder", "AnimationFactory", "scheduleMicroTask", "cb", "Promise", "resolve", "then", "NoopAnimationPlayer", "duration", "delay", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_started", "_destroyed", "_finished", "parentPlayer", "totalTime", "prototype", "_onFinish", "for<PERSON>ach", "fn", "onStart", "push", "onDone", "onDestroy", "hasStarted", "init", "play", "_onStart", "triggerMicrotask", "_this", "pause", "restart", "finish", "destroy", "reset", "setPosition", "position", "getPosition", "triggerCallback", "phaseName", "methods", "length", "AnimationGroupPlayer", "_players", "players", "doneCount", "destroyCount", "startCount", "total", "player", "_onD<PERSON>roy", "reduce", "time", "Math", "max", "p", "timeAtPosition", "min", "<PERSON><PERSON><PERSON><PERSON>", "AUTO_STYLE", "animate", "timings", "styles", "type", "animate<PERSON><PERSON><PERSON>", "options", "animation", "steps", "group", "keyframes", "query", "selector", "sequence", "stagger", "state", "name", "style", "tokens", "offset", "transition", "stateChangeExpr", "expr", "trigger", "definitions", "useAnimation", "ɵPRE_STYLE", "ɵAnimationGroupPlayer", "Object", "defineProperty", "value"], "mappings": ";;;;;CAMC,SAAUA,EAAQC,GACI,iBAAZC,SAA0C,oBAAXC,OAAyBF,EAAQC,SACrD,mBAAXE,QAAyBA,OAAOC,IAAMD,OAAO,uBAAwB,WAAYH,GAC9DA,IAAzBD,EAASA,GAAUM,MAAsBC,GAAKP,EAAOO,OAAUP,EAAOO,GAAGC,gBAH9E,CAIEC,KAAM,SAAUP,GAAW,aAgDzB,IAAIQ,EACA,SAASA,MASTC,EACA,SAASA;;;;;;;;AAk2Bb,SAASC,EAAkBC,GACvBC,QAAQC,QAAQ,MAAMC,KAAKH;;;;;;;OAqB/B,IAAII,EAAqC,WACrC,SAASA,EAAoBC,EAAUC,QAClB,IAAbD,IAAuBA,EAAW,QACxB,IAAVC,IAAoBA,EAAQ,GAChCV,KAAKW,cACLX,KAAKY,eACLZ,KAAKa,iBACLb,KAAKc,UAAW,EAChBd,KAAKe,YAAa,EAClBf,KAAKgB,WAAY,EACjBhB,KAAKiB,aAAe,KACpBjB,KAAKkB,UAAYT,EAAWC,EAqDhC,OAnDAF,EAAoBW,UAAUC,UAAY,WACjCpB,KAAKgB,YACNhB,KAAKgB,WAAY,EACjBhB,KAAKW,WAAWU,QAAQ,SAAUC,GAAM,OAAOA,MAC/CtB,KAAKW,gBAGbH,EAAoBW,UAAUI,QAAU,SAAUD,GAAMtB,KAAKY,YAAYY,KAAKF,IAC9Ed,EAAoBW,UAAUM,OAAS,SAAUH,GAAMtB,KAAKW,WAAWa,KAAKF,IAC5Ed,EAAoBW,UAAUO,UAAY,SAAUJ,GAAMtB,KAAKa,cAAcW,KAAKF,IAClFd,EAAoBW,UAAUQ,WAAa,WAAc,OAAO3B,KAAKc,UACrEN,EAAoBW,UAAUS,KAAO,aACrCpB,EAAoBW,UAAUU,KAAO,WAC5B7B,KAAK2B,eACN3B,KAAK8B,WACL9B,KAAK+B,oBAET/B,KAAKc,UAAW,GAGpBN,EAAoBW,UAAUY,iBAAmB,WAC7C,IAAIC,EAAQhC,KACZG,EAAkB,WAAc,OAAO6B,EAAMZ,eAEjDZ,EAAoBW,UAAUW,SAAW,WACrC9B,KAAKY,YAAYS,QAAQ,SAAUC,GAAM,OAAOA,MAChDtB,KAAKY,gBAETJ,EAAoBW,UAAUc,MAAQ,aACtCzB,EAAoBW,UAAUe,QAAU,aACxC1B,EAAoBW,UAAUgB,OAAS,WAAcnC,KAAKoB,aAC1DZ,EAAoBW,UAAUiB,QAAU,WAC/BpC,KAAKe,aACNf,KAAKe,YAAa,EACbf,KAAK2B,cACN3B,KAAK8B,WAET9B,KAAKmC,SACLnC,KAAKa,cAAcQ,QAAQ,SAAUC,GAAM,OAAOA,MAClDtB,KAAKa,mBAGbL,EAAoBW,UAAUkB,MAAQ,aACtC7B,EAAoBW,UAAUmB,YAAc,SAAUC,KACtD/B,EAAoBW,UAAUqB,YAAc,WAAc,OAAO,GAEjEhC,EAAoBW,UAAUsB,gBAAkB,SAAUC,GACtD,IAAIC,EAAuB,SAAbD,EAAuB1C,KAAKY,YAAcZ,KAAKW,WAC7DgC,EAAQtB,QAAQ,SAAUC,GAAM,OAAOA,MACvCqB,EAAQC,OAAS,GAEdpC,EAhE6B,GAkFpCqC,EAAsC,WACtC,SAASA,EAAqBC,GAC1B,IAAId,EAAQhC,KACZA,KAAKW,cACLX,KAAKY,eACLZ,KAAKgB,WAAY,EACjBhB,KAAKc,UAAW,EAChBd,KAAKe,YAAa,EAClBf,KAAKa,iBACLb,KAAKiB,aAAe,KACpBjB,KAAKkB,UAAY,EACjBlB,KAAK+C,QAAUD,EACf,IAAIE,EAAY,EACZC,EAAe,EACfC,EAAa,EACbC,EAAQnD,KAAK+C,QAAQH,OACZ,GAATO,EACAhD,EAAkB,WAAc,OAAO6B,EAAMZ,cAG7CpB,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAC3BA,EAAO3B,OAAO,aACJuB,GAAaG,GACfnB,EAAMZ,cAGdgC,EAAO1B,UAAU,aACPuB,GAAgBE,GAClBnB,EAAMqB,eAGdD,EAAO7B,QAAQ,aACL2B,GAAcC,GAChBnB,EAAMF,eAKtB9B,KAAKkB,UAAYlB,KAAK+C,QAAQO,OAAO,SAAUC,EAAMH,GAAU,OAAOI,KAAKC,IAAIF,EAAMH,EAAOlC,YAAe,GA8E/G,OA5EA2B,EAAqB1B,UAAUC,UAAY,WAClCpB,KAAKgB,YACNhB,KAAKgB,WAAY,EACjBhB,KAAKW,WAAWU,QAAQ,SAAUC,GAAM,OAAOA,MAC/CtB,KAAKW,gBAGbkC,EAAqB1B,UAAUS,KAAO,WAAc5B,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOxB,UAC3GiB,EAAqB1B,UAAUI,QAAU,SAAUD,GAAMtB,KAAKY,YAAYY,KAAKF,IAC/EuB,EAAqB1B,UAAUW,SAAW,WACjC9B,KAAK2B,eACN3B,KAAKc,UAAW,EAChBd,KAAKY,YAAYS,QAAQ,SAAUC,GAAM,OAAOA,MAChDtB,KAAKY,iBAGbiC,EAAqB1B,UAAUM,OAAS,SAAUH,GAAMtB,KAAKW,WAAWa,KAAKF,IAC7EuB,EAAqB1B,UAAUO,UAAY,SAAUJ,GAAMtB,KAAKa,cAAcW,KAAKF,IACnFuB,EAAqB1B,UAAUQ,WAAa,WAAc,OAAO3B,KAAKc,UACtE+B,EAAqB1B,UAAUU,KAAO,WAC7B7B,KAAKiB,cACNjB,KAAK4B,OAET5B,KAAK8B,WACL9B,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOvB,UAE3DgB,EAAqB1B,UAAUc,MAAQ,WAAcjC,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOnB,WAC5GY,EAAqB1B,UAAUe,QAAU,WAAclC,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOlB,aAC9GW,EAAqB1B,UAAUgB,OAAS,WACpCnC,KAAKoB,YACLpB,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOjB,YAE3DU,EAAqB1B,UAAUiB,QAAU,WAAcpC,KAAKqD,cAC5DR,EAAqB1B,UAAUkC,WAAa,WACnCrD,KAAKe,aACNf,KAAKe,YAAa,EAClBf,KAAKoB,YACLpB,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOhB,YACvDpC,KAAKa,cAAcQ,QAAQ,SAAUC,GAAM,OAAOA,MAClDtB,KAAKa,mBAGbgC,EAAqB1B,UAAUkB,MAAQ,WACnCrC,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAAU,OAAOA,EAAOf,UACvDrC,KAAKe,YAAa,EAClBf,KAAKgB,WAAY,EACjBhB,KAAKc,UAAW,GAEpB+B,EAAqB1B,UAAUmB,YAAc,SAAUoB,GACnD,IAAIC,EAAiBD,EAAI1D,KAAKkB,UAC9BlB,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAC3B,IAAIb,EAAWa,EAAOlC,UAAYsC,KAAKI,IAAI,EAAGD,EAAiBP,EAAOlC,WAAa,EACnFkC,EAAOd,YAAYC,MAG3BM,EAAqB1B,UAAUqB,YAAc,WACzC,IAAIoB,EAAM,EAKV,OAJA5D,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GAC3B,IAAIM,EAAIN,EAAOZ,cACfoB,EAAMJ,KAAKI,IAAIF,EAAGE,KAEfA,GAEXf,EAAqB1B,UAAU0C,cAAgB,WAC3C7D,KAAK+C,QAAQ1B,QAAQ,SAAU+B,GACvBA,EAAOS,eACPT,EAAOS,mBAKnBhB,EAAqB1B,UAAUsB,gBAAkB,SAAUC,GACvD,IAAIC,EAAuB,SAAbD,EAAuB1C,KAAKY,YAAcZ,KAAKW,WAC7DgC,EAAQtB,QAAQ,SAAUC,GAAM,OAAOA,MACvCqB,EAAQC,OAAS,GAEdC,EApH8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JzCpD,EAAQQ,iBAAmBA,EAC3BR,EAAQS,iBAAmBA,EAC3BT,EAAQqE,WAvlCS,IAwlCjBrE,EAAQsE,QAt4BR,SAASA,EAAQC,EAASC,GAEtB,YADe,IAAXA,IAAqBA,EAAS,OACzBC,KAAM,EAAiBD,OAAQA,EAAQD,QAASA,IAq4B7DvE,EAAQ0E,aAhdR,SAASA,EAAaC,GAElB,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,EAAsBE,QAASA,IA+clD3E,EAAQ4E,UAxeR,SAASA,EAAUC,EAAOF,GAEtB,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,EAAmBG,UAAWC,EAAOF,QAASA,IAuejE3E,EAAQ8E,MAp2BR,SAASA,EAAMD,EAAOF,GAElB,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,EAAeI,MAAOA,EAAOF,QAASA,IAm2BzD3E,EAAQ+E,UAtsBR,SAASA,EAAUF,GACf,OAASJ,KAAM,EAAmBI,MAAOA,IAssB7C7E,EAAQgF,MA3WR,SAASA,EAAMC,EAAUL,EAAWD,GAEhC,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,GAAgBQ,SAAUA,EAAUL,UAAWA,EAAWD,QAASA,IA0WtF3E,EAAQkF,SAn0BR,SAASA,EAASL,EAAOF,GAErB,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,EAAkBI,MAAOA,EAAOF,QAASA,IAk0B5D3E,EAAQmF,QAzRR,SAASA,EAAQZ,EAASK,GACtB,OAASH,KAAM,GAAkBF,QAASA,EAASK,UAAWA,IAyRlE5E,EAAQoF,MA1vBR,SAASA,EAAMC,EAAMb,EAAQG,GACzB,OAASF,KAAM,EAAeY,KAAMA,EAAMb,OAAQA,EAAQG,QAASA,IA0vBvE3E,EAAQsF,MA3xBR,SAASA,EAAMC,GACX,OAASd,KAAM,EAAeD,OAAQe,EAAQC,OAAQ,OA2xB1DxF,EAAQyF,WAjiBR,SAASA,EAAWC,EAAiBb,EAAOF,GAExC,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,EAAoBkB,KAAMD,EAAiBd,UAAWC,EAAOF,QAASA,IAgiBzF3E,EAAQ4F,QA98BR,SAASA,EAAQP,EAAMQ,GACnB,OAASpB,KAAM,EAAiBY,KAAMA,EAAMQ,YAAaA,EAAalB,aA88B1E3E,EAAQ8F,aA7cR,SAASA,EAAalB,EAAWD,GAE7B,YADgB,IAAZA,IAAsBA,EAAU,OAC3BF,KAAM,GAAqBG,UAAWA,EAAWD,QAASA,IA4cvE3E,EAAQe,oBAAsBA,EAC9Bf,EAAQ+F,WA/CS,IAgDjB/F,EAAQgG,sBAAwB5C,EAEhC6C,OAAOC,eAAelG,EAAS,cAAgBmG,OAAO", "sourcesContent": ["/**\n * @license Angular v7.2.16\n * (c) 2010-2019 Google LLC. https://angular.io/\n * License: MIT\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define('@angular/animations', ['exports'], factory) :\n    (global = global || self, factory((global.ng = global.ng || {}, global.ng.animations = {})));\n}(this, function (exports) { 'use strict';\n\n    /**\n     * An injectable service that produces an animation sequence programmatically within an\n     * Angular component or directive.\n     * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n     *\n     * @usageNotes\n     *\n     * To use this service, add it to your component or directive as a dependency.\n     * The service is instantiated along with your component.\n     *\n     * Apps do not typically need to create their own animation players, but if you\n     * do need to, follow these steps:\n     *\n     * 1. Use the `build()` method to create a programmatic animation using the\n     * `animate()` function. The method returns an `AnimationFactory` instance.\n     *\n     * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n     *\n     * 3. Use the player object to control the animation programmatically.\n     *\n     * For example:\n     *\n     * ```ts\n     * // import the service from BrowserAnimationsModule\n     * import {AnimationBuilder} from '@angular/animations';\n     * // require the service as a dependency\n     * class MyCmp {\n     *   constructor(private _builder: AnimationBuilder) {}\n     *\n     *   makeAnimation(element: any) {\n     *     // first define a reusable animation\n     *     const myAnimation = this._builder.build([\n     *       style({ width: 0 }),\n     *       animate(1000, style({ width: '100px' }))\n     *     ]);\n     *\n     *     // use the returned factory object to create a player\n     *     const player = myAnimation.create(element);\n     *\n     *     player.play();\n     *   }\n     * }\n     * ```\n     *\n     * @publicApi\n     */\n    var AnimationBuilder = /** @class */ (function () {\n        function AnimationBuilder() {\n        }\n        return AnimationBuilder;\n    }());\n    /**\n     * A factory object returned from the `AnimationBuilder`.`build()` method.\n     *\n     * @publicApi\n     */\n    var AnimationFactory = /** @class */ (function () {\n        function AnimationFactory() {\n        }\n        return AnimationFactory;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * Specifies automatic styling.\n     *\n     * @publicApi\n     */\n    var AUTO_STYLE = '*';\n    /**\n     * Creates a named animation trigger, containing a  list of `state()`\n     * and `transition()` entries to be evaluated when the expression\n     * bound to the trigger changes.\n     *\n     * @param name An identifying string.\n     * @param definitions  An animation definition object, containing an array of `state()`\n     * and `transition()` declarations.\n     *\n     * @return An object that encapsulates the trigger data.\n     *\n     * @usageNotes\n     * Define an animation trigger in the `animations` section of `@Component` metadata.\n     * In the template, reference the trigger by name and bind it to a trigger expression that\n     * evaluates to a defined animation state, using the following format:\n     *\n     * `[@triggerName]=\"expression\"`\n     *\n     * Animation trigger bindings convert all values to strings, and then match the\n     * previous and current values against any linked transitions.\n     * Booleans can be specified as `1` or `true` and `0` or `false`.\n     *\n     * ### Usage Example\n     *\n     * The following example creates an animation trigger reference based on the provided\n     * name value.\n     * The provided animation value is expected to be an array consisting of state and\n     * transition declarations.\n     *\n     * ```typescript\n     * @Component({\n     *   selector: \"my-component\",\n     *   templateUrl: \"my-component-tpl.html\",\n     *   animations: [\n     *     trigger(\"myAnimationTrigger\", [\n     *       state(...),\n     *       state(...),\n     *       transition(...),\n     *       transition(...)\n     *     ])\n     *   ]\n     * })\n     * class MyComponent {\n     *   myStatusExp = \"something\";\n     * }\n     * ```\n     *\n     * The template associated with this component makes use of the defined trigger\n     * by binding to an element within its template code.\n     *\n     * ```html\n     * <!-- somewhere inside of my-component-tpl.html -->\n     * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n     * ```\n     *\n     * ### Using an inline function\n     * The `transition` animation method also supports reading an inline function which can decide\n     * if its associated animation should be run.\n     *\n     * ```typescript\n     * // this method is run each time the `myAnimationTrigger` trigger value changes.\n     * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n     string]: any}): boolean {\n     *   // notice that `element` and `params` are also available here\n     *   return toState == 'yes-please-animate';\n     * }\n     *\n     * @Component({\n     *   selector: 'my-component',\n     *   templateUrl: 'my-component-tpl.html',\n     *   animations: [\n     *     trigger('myAnimationTrigger', [\n     *       transition(myInlineMatcherFn, [\n     *         // the animation sequence code\n     *       ]),\n     *     ])\n     *   ]\n     * })\n     * class MyComponent {\n     *   myStatusExp = \"yes-please-animate\";\n     * }\n     * ```\n     *\n     * ### Disabling Animations\n     * When true, the special animation control binding `@.disabled` binding prevents\n     * all animations from rendering.\n     * Place the  `@.disabled` binding on an element to disable\n     * animations on the element itself, as well as any inner animation triggers\n     * within the element.\n     *\n     * The following example shows how to use this feature:\n     *\n     * ```typescript\n     * @Component({\n     *   selector: 'my-component',\n     *   template: `\n     *     <div [@.disabled]=\"isDisabled\">\n     *       <div [@childAnimation]=\"exp\"></div>\n     *     </div>\n     *   `,\n     *   animations: [\n     *     trigger(\"childAnimation\", [\n     *       // ...\n     *     ])\n     *   ]\n     * })\n     * class MyComponent {\n     *   isDisabled = true;\n     *   exp = '...';\n     * }\n     * ```\n     *\n     * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n     * along with any inner animations.\n     *\n     * ### Disable animations application-wide\n     * When an area of the template is set to have animations disabled,\n     * **all** inner components have their animations disabled as well.\n     * This means that you can disable all animations for an app\n     * by placing a host binding set on `@.disabled` on the topmost Angular component.\n     *\n     * ```typescript\n     * import {Component, HostBinding} from '@angular/core';\n     *\n     * @Component({\n     *   selector: 'app-component',\n     *   templateUrl: 'app.component.html',\n     * })\n     * class AppComponent {\n     *   @HostBinding('@.disabled')\n     *   public animationsDisabled = true;\n     * }\n     * ```\n     *\n     * ### Overriding disablement of inner animations\n     * Despite inner animations being disabled, a parent animation can `query()`\n     * for inner elements located in disabled areas of the template and still animate\n     * them if needed. This is also the case for when a sub animation is\n     * queried by a parent and then later animated using `animateChild()`.\n     *\n     * ### Detecting when an animation is disabled\n     * If a region of the DOM (or the entire application) has its animations disabled, the animation\n     * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n     * an instance of an `AnimationEvent`. If animations are disabled,\n     * the `.disabled` flag on the event is true.\n     *\n     * @publicApi\n     */\n    function trigger(name, definitions) {\n        return { type: 7 /* Trigger */, name: name, definitions: definitions, options: {} };\n    }\n    /**\n     * Defines an animation step that combines styling information with timing information.\n     *\n     * @param timings Sets `AnimateTimings` for the parent animation.\n     * A string in the format \"duration [delay] [easing]\".\n     *  - Duration and delay are expressed as a number and optional time unit,\n     * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n     * The default unit is milliseconds.\n     *  - The easing value controls how the animation accelerates and decelerates\n     * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n     * `ease-in-out`, or a `cubic-bezier()` function call.\n     * If not supplied, no easing is applied.\n     *\n     * For example, the string \"1s 100ms ease-out\" specifies a duration of\n     * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n     * which decelerates near the end of the duration.\n     * @param styles Sets AnimationStyles for the parent animation.\n     * A function call to either `style()` or `keyframes()`\n     * that returns a collection of CSS style entries to be applied to the parent animation.\n     * When null, uses the styles from the destination state.\n     * This is useful when describing an animation step that will complete an animation;\n     * see \"Animating to the final state\" in `transitions()`.\n     * @returns An object that encapsulates the animation step.\n     *\n     * @usageNotes\n     * Call within an animation `sequence()`, `{@link animations/group group()}`, or\n     * `transition()` call to specify an animation step\n     * that applies given style data to the parent animation for a given amount of time.\n     *\n     * ### Syntax Examples\n     * **Timing examples**\n     *\n     * The following examples show various `timings` specifications.\n     * - `animate(500)` : Duration is 500 milliseconds.\n     * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n     * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n     * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n     * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n     * milliseconds, easing according to a bezier curve.\n     *\n     * **Style examples**\n     *\n     * The following example calls `style()` to set a single CSS style.\n     * ```typescript\n     * animate(500, style({ background: \"red\" }))\n     * ```\n     * The following example calls `keyframes()` to set a CSS style\n     * to different values for successive keyframes.\n     * ```typescript\n     * animate(500, keyframes(\n     *  [\n     *   style({ background: \"blue\" })),\n     *   style({ background: \"red\" }))\n     *  ])\n     * ```\n     *\n     * @publicApi\n     */\n    function animate(timings, styles) {\n        if (styles === void 0) { styles = null; }\n        return { type: 4 /* Animate */, styles: styles, timings: timings };\n    }\n    /**\n     * @description Defines a list of animation steps to be run in parallel.\n     *\n     * @param steps An array of animation step objects.\n     * - When steps are defined by `style()` or `animate()`\n     * function calls, each call within the group is executed instantly.\n     * - To specify offset styles to be applied at a later time, define steps with\n     * `keyframes()`, or use `animate()` calls with a delay value.\n     * For example:\n     *\n     * ```typescript\n     * group([\n     *   animate(\"1s\", style({ background: \"black\" })),\n     *   animate(\"2s\", style({ color: \"white\" }))\n     * ])\n     * ```\n     *\n     * @param options An options object containing a delay and\n     * developer-defined parameters that provide styling defaults and\n     * can be overridden on invocation.\n     *\n     * @return An object that encapsulates the group data.\n     *\n     * @usageNotes\n     * Grouped animations are useful when a series of styles must be\n     * animated at different starting times and closed off at different ending times.\n     *\n     * When called within a `sequence()` or a\n     * `transition()` call, does not continue to the next\n     * instruction until all of the inner animation steps have completed.\n     *\n     * @publicApi\n     */\n    function group(steps, options) {\n        if (options === void 0) { options = null; }\n        return { type: 3 /* Group */, steps: steps, options: options };\n    }\n    /**\n     * Defines a list of animation steps to be run sequentially, one by one.\n     *\n     * @param steps An array of animation step objects.\n     * - Steps defined by `style()` calls apply the styling data immediately.\n     * - Steps defined by `animate()` calls apply the styling data over time\n     *   as specified by the timing data.\n     *\n     * ```typescript\n     * sequence([\n     *   style({ opacity: 0 })),\n     *   animate(\"1s\", style({ opacity: 1 }))\n     * ])\n     * ```\n     *\n     * @param options An options object containing a delay and\n     * developer-defined parameters that provide styling defaults and\n     * can be overridden on invocation.\n     *\n     * @return An object that encapsulates the sequence data.\n     *\n     * @usageNotes\n     * When you pass an array of steps to a\n     * `transition()` call, the steps run sequentially by default.\n     * Compare this to the `{@link animations/group group()}` call, which runs animation steps in parallel.\n     *\n     * When a sequence is used within a `{@link animations/group group()}` or a `transition()` call,\n     * execution continues to the next instruction only after each of the inner animation\n     * steps have completed.\n     *\n     * @publicApi\n     **/\n    function sequence(steps, options) {\n        if (options === void 0) { options = null; }\n        return { type: 2 /* Sequence */, steps: steps, options: options };\n    }\n    /**\n     * Declares a key/value object containing CSS properties/styles that\n     * can then be used for an animation `state`, within an animation `sequence`,\n     * or as styling data for calls to `animate()` and `keyframes()`.\n     *\n     * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n     * The value can be any of the following:\n     * - A key-value style pair associating a CSS property with a value.\n     * - An array of key-value style pairs.\n     * - An asterisk (*), to use auto-styling, where styles are derived from the element\n     * being animated and applied to the animation when it starts.\n     *\n     * Auto-styling can be used to define a state that depends on layout or other\n     * environmental factors.\n     *\n     * @return An object that encapsulates the style data.\n     *\n     * @usageNotes\n     * The following examples create animation styles that collect a set of\n     * CSS property values:\n     *\n     * ```typescript\n     * // string values for CSS properties\n     * style({ background: \"red\", color: \"blue\" })\n     *\n     * // numerical pixel values\n     * style({ width: 100, height: 0 })\n     * ```\n     *\n     * The following example uses auto-styling to allow a component to animate from\n     * a height of 0 up to the height of the parent element:\n     *\n     * ```\n     * style({ height: 0 }),\n     * animate(\"1s\", style({ height: \"*\" }))\n     * ```\n     *\n     * @publicApi\n     **/\n    function style(tokens) {\n        return { type: 6 /* Style */, styles: tokens, offset: null };\n    }\n    /**\n     * Declares an animation state within a trigger attached to an element.\n     *\n     * @param name One or more names for the defined state in a comma-separated string.\n     * The following reserved state names can be supplied to define a style for specific use\n     * cases:\n     *\n     * - `void` You can associate styles with this name to be used when\n     * the element is detached from the application. For example, when an `ngIf` evaluates\n     * to false, the state of the associated element is void.\n     *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n     * to be used as the fallback when the state that is being animated is not declared\n     * within the trigger.\n     *\n     * @param styles A set of CSS styles associated with this state, created using the\n     * `style()` function.\n     * This set of styles persists on the element once the state has been reached.\n     * @param options Parameters that can be passed to the state when it is invoked.\n     * 0 or more key-value pairs.\n     * @return An object that encapsulates the new state data.\n     *\n     * @usageNotes\n     * Use the `trigger()` function to register states to an animation trigger.\n     * Use the `transition()` function to animate between states.\n     * When a state is active within a component, its associated styles persist on the element,\n     * even when the animation ends.\n     *\n     * @publicApi\n     **/\n    function state(name, styles, options) {\n        return { type: 0 /* State */, name: name, styles: styles, options: options };\n    }\n    /**\n     * Defines a set of animation styles, associating each style with an optional `offset` value.\n     *\n     * @param steps A set of animation styles with optional offset data.\n     * The optional `offset` value for a style specifies a percentage of the total animation\n     * time at which that style is applied.\n     * @returns An object that encapsulates the keyframes data.\n     *\n     * @usageNotes\n     * Use with the `animate()` call. Instead of applying animations\n     * from the current state\n     * to the destination state, keyframes describe how each style entry is applied and at what point\n     * within the animation arc.\n     * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n     *\n     * ### Usage\n     *\n     * In the following example, the offset values describe\n     * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n     * blue when 20% of the total time has elapsed.\n     *\n     * ```typescript\n     * // the provided offset values\n     * animate(\"5s\", keyframes([\n     *   style({ backgroundColor: \"red\", offset: 0 }),\n     *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n     *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n     *   style({ backgroundColor: \"black\", offset: 1 })\n     * ]))\n     * ```\n     *\n     * If there are no `offset` values specified in the style entries, the offsets\n     * are calculated automatically.\n     *\n     * ```typescript\n     * animate(\"5s\", keyframes([\n     *   style({ backgroundColor: \"red\" }) // offset = 0\n     *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n     *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n     *   style({ backgroundColor: \"black\" }) // offset = 1\n     * ]))\n     *```\n\n     * @publicApi\n     */\n    function keyframes(steps) {\n        return { type: 5 /* Keyframes */, steps: steps };\n    }\n    /**\n     * Declares an animation transition as a sequence of animation steps to run when a given\n     * condition is satisfied. The condition is a Boolean expression or function that compares\n     * the previous and current animation states, and returns true if this transition should occur.\n     * When the state criteria of a defined transition are met, the associated animation is\n     * triggered.\n     *\n     * @param stateChangeExpr A Boolean expression or function that compares the previous and current\n     * animation states, and returns true if this transition should occur. Note that  \"true\" and \"false\"\n     * match 1 and 0, respectively. An expression is evaluated each time a state change occurs in the\n     * animation trigger element.\n     * The animation steps run when the expression evaluates to true.\n     *\n     * - A state-change string takes the form \"state1 => state2\", where each side is a defined animation\n     * state, or an asterix (*) to refer to a dynamic start or end state.\n     *   - The expression string can contain multiple comma-separated statements;\n     * for example \"state1 => state2, state3 => state4\".\n     *   - Special values `:enter` and `:leave` initiate a transition on the entry and exit states,\n     * equivalent to  \"void => *\"  and \"* => void\".\n     *   - Special values `:increment` and `:decrement` initiate a transition when a numeric value has\n     * increased or decreased in value.\n     * - A function is executed each time a state change occurs in the animation trigger element.\n     * The animation steps run when the function returns true.\n     *\n     * @param steps One or more animation objects, as returned by the `animate()` or\n     * `sequence()` function, that form a transformation from one state to another.\n     * A sequence is used by default when you pass an array.\n     * @param options An options object that can contain a delay value for the start of the animation,\n     * and additional developer-defined parameters. Provided values for additional parameters are used\n     * as defaults, and override values can be passed to the caller on invocation.\n     * @returns An object that encapsulates the transition data.\n     *\n     * @usageNotes\n     * The template associated with a component binds an animation trigger to an element.\n     *\n     * ```HTML\n     * <!-- somewhere inside of my-component-tpl.html -->\n     * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n     * ```\n     *\n     * All transitions are defined within an animation trigger,\n     * along with named states that the transitions change to and from.\n     *\n     * ```typescript\n     * trigger(\"myAnimationTrigger\", [\n     *  // define states\n     *  state(\"on\", style({ background: \"green\" })),\n     *  state(\"off\", style({ background: \"grey\" })),\n     *  ...]\n     * ```\n     *\n     * Note that when you call the `sequence()` function within a `{@link animations/group group()}`\n     * or a `transition()` call, execution does not continue to the next instruction\n     * until each of the inner animation steps have completed.\n     *\n     * ### Syntax examples\n     *\n     * The following examples define transitions between the two defined states (and default states),\n     * using various options:\n     *\n     * ```typescript\n     * // Transition occurs when the state value\n     * // bound to \"myAnimationTrigger\" changes from \"on\" to \"off\"\n     * transition(\"on => off\", animate(500))\n     * // Run the same animation for both directions\n     * transition(\"on <=> off\", animate(500))\n     * // Define multiple state-change pairs separated by commas\n     * transition(\"on => off, off => void\", animate(500))\n     * ```\n     *\n     * ### Special values for state-change expressions\n     *\n     * - Catch-all state change for when an element is inserted into the page and the\n     * destination state is unknown:\n     *\n     * ```typescript\n     * transition(\"void => *\", [\n     *  style({ opacity: 0 }),\n     *  animate(500)\n     *  ])\n     * ```\n     *\n     * - Capture a state change between any states:\n     *\n     *  `transition(\"* => *\", animate(\"1s 0s\"))`\n     *\n     * - Entry and exit transitions:\n     *\n     * ```typescript\n     * transition(\":enter\", [\n     *   style({ opacity: 0 }),\n     *   animate(500, style({ opacity: 1 }))\n     *   ]),\n     * transition(\":leave\", [\n     *   animate(500, style({ opacity: 0 }))\n     *   ])\n     * ```\n     *\n     * - Use `:increment` and `:decrement` to initiate transitions:\n     *\n     * ```typescript\n     * transition(\":increment\", group([\n     *  query(':enter', [\n     *     style({ left: '100%' }),\n     *     animate('0.5s ease-out', style('*'))\n     *   ]),\n     *  query(':leave', [\n     *     animate('0.5s ease-out', style({ left: '-100%' }))\n     *  ])\n     * ]))\n     *\n     * transition(\":decrement\", group([\n     *  query(':enter', [\n     *     style({ left: '100%' }),\n     *     animate('0.5s ease-out', style('*'))\n     *   ]),\n     *  query(':leave', [\n     *     animate('0.5s ease-out', style({ left: '-100%' }))\n     *  ])\n     * ]))\n     * ```\n     *\n     * ### State-change functions\n     *\n     * Here is an example of a `fromState` specified as a state-change function that invokes an\n     * animation when true:\n     *\n     * ```typescript\n     * transition((fromState, toState) =>\n     *  {\n     *   return fromState == \"off\" && toState == \"on\";\n     *  },\n     *  animate(\"1s 0s\"))\n     * ```\n     *\n     * ### Animating to the final state\n     *\n     * If the final step in a transition is a call to `animate()` that uses a timing value\n     * with no style data, that step is automatically considered the final animation arc,\n     * for the element to reach the final state. Angular automatically adds or removes\n     * CSS styles to ensure that the element is in the correct final state.\n     *\n     * The following example defines a transition that starts by hiding the element,\n     * then makes sure that it animates properly to whatever state is currently active for trigger:\n     *\n     * ```typescript\n     * transition(\"void => *\", [\n     *   style({ opacity: 0 }),\n     *   animate(500)\n     *  ])\n     * ```\n     * ### Boolean value matching\n     * If a trigger binding value is a Boolean, it can be matched using a transition expression\n     * that compares true and false or 1 and 0. For example:\n     *\n     * ```\n     * // in the template\n     * <div [@openClose]=\"open ? true : false\">...</div>\n     * // in the component metadata\n     * trigger('openClose', [\n     *   state('true', style({ height: '*' })),\n     *   state('false', style({ height: '0px' })),\n     *   transition('false <=> true', animate(500))\n     * ])\n     * ```\n     *\n     * @publicApi\n     **/\n    function transition(stateChangeExpr, steps, options) {\n        if (options === void 0) { options = null; }\n        return { type: 1 /* Transition */, expr: stateChangeExpr, animation: steps, options: options };\n    }\n    /**\n     * Produces a reusable animation that can be invoked in another animation or sequence,\n     * by calling the `useAnimation()` function.\n     *\n     * @param steps One or more animation objects, as returned by the `animate()`\n     * or `sequence()` function, that form a transformation from one state to another.\n     * A sequence is used by default when you pass an array.\n     * @param options An options object that can contain a delay value for the start of the\n     * animation, and additional developer-defined parameters.\n     * Provided values for additional parameters are used as defaults,\n     * and override values can be passed to the caller on invocation.\n     * @returns An object that encapsulates the animation data.\n     *\n     * @usageNotes\n     * The following example defines a reusable animation, providing some default parameter\n     * values.\n     *\n     * ```typescript\n     * var fadeAnimation = animation([\n     *   style({ opacity: '{{ start }}' }),\n     *   animate('{{ time }}',\n     *   style({ opacity: '{{ end }}'}))\n     *   ],\n     *   { params: { time: '1000ms', start: 0, end: 1 }});\n     * ```\n     *\n     * The following invokes the defined animation with a call to `useAnimation()`,\n     * passing in override parameter values.\n     *\n     * ```js\n     * useAnimation(fadeAnimation, {\n     *   params: {\n     *     time: '2s',\n     *     start: 1,\n     *     end: 0\n     *   }\n     * })\n     * ```\n     *\n     * If any of the passed-in parameter values are missing from this call,\n     * the default values are used. If one or more parameter values are missing before a step is\n     * animated, `useAnimation()` throws an error.\n     *\n     * @publicApi\n     */\n    function animation(steps, options) {\n        if (options === void 0) { options = null; }\n        return { type: 8 /* Reference */, animation: steps, options: options };\n    }\n    /**\n     * Executes a queried inner animation element within an animation sequence.\n     *\n     * @param options An options object that can contain a delay value for the start of the\n     * animation, and additional override values for developer-defined parameters.\n     * @return An object that encapsulates the child animation data.\n     *\n     * @usageNotes\n     * Each time an animation is triggered in Angular, the parent animation\n     * has priority and any child animations are blocked. In order\n     * for a child animation to run, the parent animation must query each of the elements\n     * containing child animations, and run them using this function.\n     *\n     * Note that this feature is designed to be used with `query()` and it will only work\n     * with animations that are assigned using the Angular animation library. CSS keyframes\n     * and transitions are not handled by this API.\n     *\n     * @publicApi\n     */\n    function animateChild(options) {\n        if (options === void 0) { options = null; }\n        return { type: 9 /* AnimateChild */, options: options };\n    }\n    /**\n     * Starts a reusable animation that is created using the `animation()` function.\n     *\n     * @param animation The reusable animation to start.\n     * @param options An options object that can contain a delay value for the start of\n     * the animation, and additional override values for developer-defined parameters.\n     * @return An object that contains the animation parameters.\n     *\n     * @publicApi\n     */\n    function useAnimation(animation, options) {\n        if (options === void 0) { options = null; }\n        return { type: 10 /* AnimateRef */, animation: animation, options: options };\n    }\n    /**\n     * Finds one or more inner elements within the current element that is\n     * being animated within a sequence. Use with `animate()`.\n     *\n     * @param selector The element to query, or a set of elements that contain Angular-specific\n     * characteristics, specified with one or more of the following tokens.\n     *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements.\n     *  - `query(\":animating\")` : Query all currently animating elements.\n     *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n     *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n     *  - `query(\":self\")` : Include the current element into the animation sequence.\n     *\n     * @param animation One or more animation steps to apply to the queried element or elements.\n     * An array is treated as an animation sequence.\n     * @param options An options object. Use the 'limit' field to limit the total number of\n     * items to collect.\n     * @return An object that encapsulates the query data.\n     *\n     * @usageNotes\n     * Tokens can be merged into a combined query selector string. For example:\n     *\n     * ```typescript\n     *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n     * ```\n     *\n     * The `query()` function collects multiple elements and works internally by using\n     * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n     * the total number of items to be collected. For example:\n     *\n     * ```js\n     * query('div', [\n     *   animate(...),\n     *   animate(...)\n     * ], { limit: 1 })\n     * ```\n     *\n     * By default, throws an error when zero items are found. Set the\n     * `optional` flag to ignore this error. For example:\n     *\n     * ```js\n     * query('.some-element-that-may-not-be-there', [\n     *   animate(...),\n     *   animate(...)\n     * ], { optional: true })\n     * ```\n     *\n     * ### Usage Example\n     *\n     * The following example queries for inner elements and animates them\n     * individually using `animate()`.\n     *\n     * ```typescript\n     * @Component({\n     *   selector: 'inner',\n     *   template: `\n     *     <div [@queryAnimation]=\"exp\">\n     *       <h1>Title</h1>\n     *       <div class=\"content\">\n     *         Blah blah blah\n     *       </div>\n     *     </div>\n     *   `,\n     *   animations: [\n     *    trigger('queryAnimation', [\n     *      transition('* => goAnimate', [\n     *        // hide the inner elements\n     *        query('h1', style({ opacity: 0 })),\n     *        query('.content', style({ opacity: 0 })),\n     *\n     *        // animate the inner elements in, one by one\n     *        query('h1', animate(1000, style({ opacity: 1 }))),\n     *        query('.content', animate(1000, style({ opacity: 1 }))),\n     *      ])\n     *    ])\n     *  ]\n     * })\n     * class Cmp {\n     *   exp = '';\n     *\n     *   goAnimate() {\n     *     this.exp = 'goAnimate';\n     *   }\n     * }\n     * ```\n     *\n     * @publicApi\n     */\n    function query(selector, animation, options) {\n        if (options === void 0) { options = null; }\n        return { type: 11 /* Query */, selector: selector, animation: animation, options: options };\n    }\n    /**\n     * Use within an animation `query()` call to issue a timing gap after\n     * each queried item is animated.\n     *\n     * @param timings A delay value.\n     * @param animation One ore more animation steps.\n     * @returns An object that encapsulates the stagger data.\n     *\n     * @usageNotes\n     * In the following example, a container element wraps a list of items stamped out\n     * by an `ngFor`. The container element contains an animation trigger that will later be set\n     * to query for each of the inner items.\n     *\n     * Each time items are added, the opacity fade-in animation runs,\n     * and each removed item is faded out.\n     * When either of these animations occur, the stagger effect is\n     * applied after each item's animation is started.\n     *\n     * ```html\n     * <!-- list.component.html -->\n     * <button (click)=\"toggle()\">Show / Hide Items</button>\n     * <hr />\n     * <div [@listAnimation]=\"items.length\">\n     *   <div *ngFor=\"let item of items\">\n     *     {{ item }}\n     *   </div>\n     * </div>\n     * ```\n     *\n     * Here is the component code:\n     *\n     * ```typescript\n     * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n     * @Component({\n     *   templateUrl: 'list.component.html',\n     *   animations: [\n     *     trigger('listAnimation', [\n     *     ...\n     *     ])\n     *   ]\n     * })\n     * class ListComponent {\n     *   items = [];\n     *\n     *   showItems() {\n     *     this.items = [0,1,2,3,4];\n     *   }\n     *\n     *   hideItems() {\n     *     this.items = [];\n     *   }\n     *\n     *   toggle() {\n     *     this.items.length ? this.hideItems() : this.showItems();\n     *    }\n     *  }\n     * ```\n     *\n     * Here is the animation trigger code:\n     *\n     * ```typescript\n     * trigger('listAnimation', [\n     *   transition('* => *', [ // each time the binding value changes\n     *     query(':leave', [\n     *       stagger(100, [\n     *         animate('0.5s', style({ opacity: 0 }))\n     *       ])\n     *     ]),\n     *     query(':enter', [\n     *       style({ opacity: 0 }),\n     *       stagger(100, [\n     *         animate('0.5s', style({ opacity: 1 }))\n     *       ])\n     *     ])\n     *   ])\n     * ])\n     * ```\n     *\n     * @publicApi\n     */\n    function stagger(timings, animation) {\n        return { type: 12 /* Stagger */, timings: timings, animation: animation };\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    function scheduleMicroTask(cb) {\n        Promise.resolve(null).then(cb);\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * An empty programmatic controller for reusable animations.\n     * Used internally when animations are disabled, to avoid\n     * checking for the null case when an animation player is expected.\n     *\n     * @see `animate()`\n     * @see `AnimationPlayer`\n     * @see `GroupPlayer`\n     *\n     * @publicApi\n     */\n    var NoopAnimationPlayer = /** @class */ (function () {\n        function NoopAnimationPlayer(duration, delay) {\n            if (duration === void 0) { duration = 0; }\n            if (delay === void 0) { delay = 0; }\n            this._onDoneFns = [];\n            this._onStartFns = [];\n            this._onDestroyFns = [];\n            this._started = false;\n            this._destroyed = false;\n            this._finished = false;\n            this.parentPlayer = null;\n            this.totalTime = duration + delay;\n        }\n        NoopAnimationPlayer.prototype._onFinish = function () {\n            if (!this._finished) {\n                this._finished = true;\n                this._onDoneFns.forEach(function (fn) { return fn(); });\n                this._onDoneFns = [];\n            }\n        };\n        NoopAnimationPlayer.prototype.onStart = function (fn) { this._onStartFns.push(fn); };\n        NoopAnimationPlayer.prototype.onDone = function (fn) { this._onDoneFns.push(fn); };\n        NoopAnimationPlayer.prototype.onDestroy = function (fn) { this._onDestroyFns.push(fn); };\n        NoopAnimationPlayer.prototype.hasStarted = function () { return this._started; };\n        NoopAnimationPlayer.prototype.init = function () { };\n        NoopAnimationPlayer.prototype.play = function () {\n            if (!this.hasStarted()) {\n                this._onStart();\n                this.triggerMicrotask();\n            }\n            this._started = true;\n        };\n        /** @internal */\n        NoopAnimationPlayer.prototype.triggerMicrotask = function () {\n            var _this = this;\n            scheduleMicroTask(function () { return _this._onFinish(); });\n        };\n        NoopAnimationPlayer.prototype._onStart = function () {\n            this._onStartFns.forEach(function (fn) { return fn(); });\n            this._onStartFns = [];\n        };\n        NoopAnimationPlayer.prototype.pause = function () { };\n        NoopAnimationPlayer.prototype.restart = function () { };\n        NoopAnimationPlayer.prototype.finish = function () { this._onFinish(); };\n        NoopAnimationPlayer.prototype.destroy = function () {\n            if (!this._destroyed) {\n                this._destroyed = true;\n                if (!this.hasStarted()) {\n                    this._onStart();\n                }\n                this.finish();\n                this._onDestroyFns.forEach(function (fn) { return fn(); });\n                this._onDestroyFns = [];\n            }\n        };\n        NoopAnimationPlayer.prototype.reset = function () { };\n        NoopAnimationPlayer.prototype.setPosition = function (position) { };\n        NoopAnimationPlayer.prototype.getPosition = function () { return 0; };\n        /** @internal */\n        NoopAnimationPlayer.prototype.triggerCallback = function (phaseName) {\n            var methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n            methods.forEach(function (fn) { return fn(); });\n            methods.length = 0;\n        };\n        return NoopAnimationPlayer;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * A programmatic controller for a group of reusable animations.\n     * Used internally to control animations.\n     *\n     * @see `AnimationPlayer`\n     * @see `{@link animations/group group()}`\n     *\n     */\n    var AnimationGroupPlayer = /** @class */ (function () {\n        function AnimationGroupPlayer(_players) {\n            var _this = this;\n            this._onDoneFns = [];\n            this._onStartFns = [];\n            this._finished = false;\n            this._started = false;\n            this._destroyed = false;\n            this._onDestroyFns = [];\n            this.parentPlayer = null;\n            this.totalTime = 0;\n            this.players = _players;\n            var doneCount = 0;\n            var destroyCount = 0;\n            var startCount = 0;\n            var total = this.players.length;\n            if (total == 0) {\n                scheduleMicroTask(function () { return _this._onFinish(); });\n            }\n            else {\n                this.players.forEach(function (player) {\n                    player.onDone(function () {\n                        if (++doneCount == total) {\n                            _this._onFinish();\n                        }\n                    });\n                    player.onDestroy(function () {\n                        if (++destroyCount == total) {\n                            _this._onDestroy();\n                        }\n                    });\n                    player.onStart(function () {\n                        if (++startCount == total) {\n                            _this._onStart();\n                        }\n                    });\n                });\n            }\n            this.totalTime = this.players.reduce(function (time, player) { return Math.max(time, player.totalTime); }, 0);\n        }\n        AnimationGroupPlayer.prototype._onFinish = function () {\n            if (!this._finished) {\n                this._finished = true;\n                this._onDoneFns.forEach(function (fn) { return fn(); });\n                this._onDoneFns = [];\n            }\n        };\n        AnimationGroupPlayer.prototype.init = function () { this.players.forEach(function (player) { return player.init(); }); };\n        AnimationGroupPlayer.prototype.onStart = function (fn) { this._onStartFns.push(fn); };\n        AnimationGroupPlayer.prototype._onStart = function () {\n            if (!this.hasStarted()) {\n                this._started = true;\n                this._onStartFns.forEach(function (fn) { return fn(); });\n                this._onStartFns = [];\n            }\n        };\n        AnimationGroupPlayer.prototype.onDone = function (fn) { this._onDoneFns.push(fn); };\n        AnimationGroupPlayer.prototype.onDestroy = function (fn) { this._onDestroyFns.push(fn); };\n        AnimationGroupPlayer.prototype.hasStarted = function () { return this._started; };\n        AnimationGroupPlayer.prototype.play = function () {\n            if (!this.parentPlayer) {\n                this.init();\n            }\n            this._onStart();\n            this.players.forEach(function (player) { return player.play(); });\n        };\n        AnimationGroupPlayer.prototype.pause = function () { this.players.forEach(function (player) { return player.pause(); }); };\n        AnimationGroupPlayer.prototype.restart = function () { this.players.forEach(function (player) { return player.restart(); }); };\n        AnimationGroupPlayer.prototype.finish = function () {\n            this._onFinish();\n            this.players.forEach(function (player) { return player.finish(); });\n        };\n        AnimationGroupPlayer.prototype.destroy = function () { this._onDestroy(); };\n        AnimationGroupPlayer.prototype._onDestroy = function () {\n            if (!this._destroyed) {\n                this._destroyed = true;\n                this._onFinish();\n                this.players.forEach(function (player) { return player.destroy(); });\n                this._onDestroyFns.forEach(function (fn) { return fn(); });\n                this._onDestroyFns = [];\n            }\n        };\n        AnimationGroupPlayer.prototype.reset = function () {\n            this.players.forEach(function (player) { return player.reset(); });\n            this._destroyed = false;\n            this._finished = false;\n            this._started = false;\n        };\n        AnimationGroupPlayer.prototype.setPosition = function (p) {\n            var timeAtPosition = p * this.totalTime;\n            this.players.forEach(function (player) {\n                var position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n                player.setPosition(position);\n            });\n        };\n        AnimationGroupPlayer.prototype.getPosition = function () {\n            var min = 0;\n            this.players.forEach(function (player) {\n                var p = player.getPosition();\n                min = Math.min(p, min);\n            });\n            return min;\n        };\n        AnimationGroupPlayer.prototype.beforeDestroy = function () {\n            this.players.forEach(function (player) {\n                if (player.beforeDestroy) {\n                    player.beforeDestroy();\n                }\n            });\n        };\n        /** @internal */\n        AnimationGroupPlayer.prototype.triggerCallback = function (phaseName) {\n            var methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n            methods.forEach(function (fn) { return fn(); });\n            methods.length = 0;\n        };\n        return AnimationGroupPlayer;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var ɵPRE_STYLE = '!';\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * Generated bundle index. Do not edit.\n     */\n\n    exports.AnimationBuilder = AnimationBuilder;\n    exports.AnimationFactory = AnimationFactory;\n    exports.AUTO_STYLE = AUTO_STYLE;\n    exports.animate = animate;\n    exports.animateChild = animateChild;\n    exports.animation = animation;\n    exports.group = group;\n    exports.keyframes = keyframes;\n    exports.query = query;\n    exports.sequence = sequence;\n    exports.stagger = stagger;\n    exports.state = state;\n    exports.style = style;\n    exports.transition = transition;\n    exports.trigger = trigger;\n    exports.useAnimation = useAnimation;\n    exports.NoopAnimationPlayer = NoopAnimationPlayer;\n    exports.ɵPRE_STYLE = ɵPRE_STYLE;\n    exports.ɵAnimationGroupPlayer = AnimationGroupPlayer;\n\n    Object.defineProperty(exports, '__esModule', { value: true });\n\n}));\n//# sourceMappingURL=animations.umd.js.map\n"]}