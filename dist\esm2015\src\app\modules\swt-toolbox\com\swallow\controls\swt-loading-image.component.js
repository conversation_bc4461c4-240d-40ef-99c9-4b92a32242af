/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
export class SwtLoadingImage {
    /**
     * @param {?} element
     */
    constructor(element) {
        this.element = element;
        this.visibility = true;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this.visibility = true;
                $(this.element.nativeElement).css("visibility", "visible");
            }
            else {
                this.visibility = false;
                $(this.element.nativeElement).css("visibility", "hidden");
            }
        }
        else {
            this.visibility = value;
            if (this.visibility) {
                $(this.element.nativeElement).css("visibility", "visible");
            }
            else {
                $(this.element.nativeElement).css("visibility", "hidden");
            }
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this.visibility;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
    /**
     * @param {?} visibility
     * @return {?}
     */
    setVisible(visibility) {
        this.visibility = visibility;
        if (visibility) {
            $(this.element.nativeElement).css("visibility", "visible");
        }
        else {
            $(this.element.nativeElement).css("visibility", "hidden");
        }
    }
}
SwtLoadingImage.decorators = [
    { type: Component, args: [{
                selector: 'SwtLoadingImage',
                template: `
      
          <img class="swt-spinner" src="assets/images/Rolling.gif">
     
  `,
                styles: [`
      .swt-spinner {
              height:18px;
              margin: auto 0px;
              vertical-align: baseline;
      }
  `]
            }] }
];
/** @nocollapse */
SwtLoadingImage.ctorParameters = () => [
    { type: ElementRef }
];
SwtLoadingImage.propDecorators = {
    visible: [{ type: Input, args: ['visible',] }]
};
if (false) {
    /** @type {?} */
    SwtLoadingImage.prototype.visibility;
    /**
     * @type {?}
     * @private
     */
    SwtLoadingImage.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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