/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Logger } from "../../logging/logger.service";
import { genericEvent } from "../../events/swt-events.module";
import { Types } from "./types";
export class LinkItemRander extends UIComponent {
    /**
     * @param {?} linkelement
     * @param {?} common
     */
    constructor(linkelement, common) {
        super(linkelement.nativeElement, common);
        this.linkelement = linkelement;
        this.common = common;
        this.text = "";
        this.color = "";
        this.type = Types.LINK;
        this.id = 0;
        this.log = new Logger("LinkItemRander", common.httpclient);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
    }
    /**
     * This method will be fired on link item render
     * click.
     * @param {?} event
     * @return {?}
     */
    linkItemRenderClickHandler(event) {
        try {
            setTimeout((/**
             * @return {?}
             */
            () => {
                // console.log("linkItemRenderClickHandler = ", this.eventlist);
                if (this.eventlist[genericEvent.CLICK]) {
                    this.eventlist[genericEvent.CLICK].call({ target: this, event: event });
                }
            }));
        }
        catch (error) {
            this.log.error("linkItemRenderClickHandler - error: ", error);
        }
    }
}
LinkItemRander.decorators = [
    { type: Component, args: [{
                selector: 'LinkItemRander',
                template: `
        <span class="link" [id]="id" (click)="linkItemRenderClickHandler($event)">
            {{ text }}
        </span>
    `,
                styles: [`
        :host {
            display: block;
            width: 100%;
        }

        .link {
            display: block;
            width: 100%;
            text-decoration: underline;
            color: #52AEFB;
            text-align: right;
            padding: 0 5px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        .link:hover {
            cursor: pointer;
        }
    `]
            }] }
];
/** @nocollapse */
LinkItemRander.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    LinkItemRander.prototype.text;
    /** @type {?} */
    LinkItemRander.prototype.color;
    /** @type {?} */
    LinkItemRander.prototype.type;
    /** @type {?} */
    LinkItemRander.prototype.id;
    /**
     * @type {?}
     * @private
     */
    LinkItemRander.prototype.linkelement;
    /**
     * @type {?}
     * @private
     */
    LinkItemRander.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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