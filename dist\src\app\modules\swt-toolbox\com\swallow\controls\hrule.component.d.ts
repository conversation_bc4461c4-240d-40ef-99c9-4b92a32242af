import { ElementRef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class HRule extends Container implements OnDestroy {
    private elem;
    private commonService;
    private _strokeColor;
    private _shadowColor;
    private _themeColor;
    strokeColor: string;
    shadowColor: string;
    themeColor: string;
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    constructor(elem: ElementRef, commonService: CommonService);
    ngOnDestroy(): void;
}
