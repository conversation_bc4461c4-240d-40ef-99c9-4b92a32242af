/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { ContextMenu, ContextMenuItem } from "../controls/context-menu.component";
import { SwtUtil } from "./swt-util.service";
import { SwtAlert } from "./swt-alert.service";
import { CommonService } from "./common.service";
import { SwtPopUpManager } from '../managers/swt-pop-up-manager.service';
import { JSONViewer } from '../screensUtils/jsonviewer/jsonviewer.component';
/* Display informations about Screen Version
 * <AUTHOR> JABALLAH on 17/10/2018
 *
 * */
//@dynamic
export class ScreenVersion {
    /**
     * @param {?} common
     */
    constructor(common) {
        this.common = common;
        // Declare ContextMenu object
        this.svContextMenu = null;
        this._moduleName = null;
        this._versionNumber = null;
        this._releaseDate = null;
        this.swtAlert = new SwtAlert(common);
    }
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} moduleName
     * @param {?} versionNumber
     * @param {?} releaseDate
     * @return {?}
     */
    loadScreenVersion(parent, moduleName, versionNumber, releaseDate) {
        // Variable to hold the error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            // Set the screen name
            this._moduleName = moduleName;
            errorLocation = 10;
            // Set the screen version
            this._versionNumber = versionNumber;
            errorLocation = 20;
            // Set the release date of current version
            this._releaseDate = releaseDate;
            // create instance for ContextMenu
            this.svContextMenu = new ContextMenu();
            this.svContextMenu.customItems = new Array();
            /** @type {?} */
            var addMenuItem = null;
            errorLocation = 80;
            addMenuItem = new ContextMenuItem("Screen Version");
            // add the listener to addMenuItem
            addMenuItem.MenuItemSelect = this.viewScrenVersion.bind(this);
            errorLocation = 100;
            this.svContextMenu.customItems.push(addMenuItem);
            errorLocation = 110;
            parent.contextMenu = this.svContextMenu;
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, null, "loadScreenVersion", errorLocation);
        }
    }
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} jsonData
     * @return {?}
     */
    pushJsonData(parent, jsonData) {
        // Variable to hold the error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.jsonData = jsonData;
            /** @type {?} */
            var addMenuItem = null;
            errorLocation = 80;
            addMenuItem = new ContextMenuItem("Show JSON Data");
            // add the listener to addMenuItem
            addMenuItem.MenuItemSelect = this.getJsonData.bind(this);
            errorLocation = 100;
            this.svContextMenu.customItems.push(addMenuItem);
            errorLocation = 110;
            parent.contextMenu = this.svContextMenu;
            this.callerWindowObject = parent;
            console.log("pushJsonData  parent", parent);
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, null, "loadScreenVersion", errorLocation);
        }
    }
    /**
     * viewScrenVersion
     * <AUTHOR> JABALLAH on 17/10/2018
     * @private
     * @param {?} event
     * @return {?}
     */
    viewScrenVersion(event) {
        this.swtAlert = new SwtAlert(this.common);
        this.swtAlert.info("Screen Name: " + this._moduleName + "<br>" + ((this._versionNumber) ? "Screen Version: " + this._versionNumber : "") + "<br>" + ((this._releaseDate) ? "Release Date: " + this._releaseDate : ""), "Version");
    }
    /**
     * viewScrenVersion
     * <AUTHOR> Sridi on 31/10/2019
     * @private
     * @param {?} event
     * @return {?}
     */
    getJsonData(event) {
        try {
            this.win = SwtPopUpManager.createPopUp(this, JSONViewer, {
                jsonData: this.jsonData ? this.jsonData : this.callerWindowObject.lastRecievedJSON,
            });
            this.win.width = "700";
            this.win.title = "Last Received JSON";
            this.win.height = "600";
            this.win.enableResize = false;
            this.win.showControls = true;
            this.win.display();
        }
        catch (e) {
            // log the error in ERROR LOG
            // SwtUtil.logError(e, this.moduleId, 'ClassName', 'doViewMessage', this.errorLocation);
        }
    }
}
ScreenVersion.decorators = [
    { type: Injectable }
];
/** @nocollapse */
ScreenVersion.ctorParameters = () => [
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    ScreenVersion.prototype.svContextMenu;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._moduleName;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._versionNumber;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._releaseDate;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.win;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.jsonData;
    /** @type {?} */
    ScreenVersion.prototype.callerWindowObject;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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