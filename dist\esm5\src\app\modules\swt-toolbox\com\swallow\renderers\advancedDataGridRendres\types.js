/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class will contains all static fields to be
 * used in the itemRenders.
 */
var Types = /** @class */ (function () {
    function Types() {
    }
    // static field to define link number type.
    Types.LINK_NUM = "link:num";
    // static field to define link:str type.
    Types.LINK = "link";
    // static field to define str type.
    Types.STR = "str";
    // static field to define combo type.
    Types.COMBO = "combo";
    // static field to define checkbox type.
    Types.CHECKBOX = "checkbox";
    // static field to define radio type.
    Types.RADIO = "radio";
    // static field to define date type.
    Types.DATE = "date";
    // static field to define number type.
    Types.NUM = "num";
    // static field to define time type.
    Types.TIME = "time";
    // static field to define input type.
    Types.INPUT = "input";
    return Types;
}());
export { Types };
if (false) {
    /** @type {?} */
    Types.LINK_NUM;
    /** @type {?} */
    Types.LINK;
    /** @type {?} */
    Types.STR;
    /** @type {?} */
    Types.COMBO;
    /** @type {?} */
    Types.CHECKBOX;
    /** @type {?} */
    Types.RADIO;
    /** @type {?} */
    Types.DATE;
    /** @type {?} */
    Types.NUM;
    /** @type {?} */
    Types.TIME;
    /** @type {?} */
    Types.INPUT;
}
//# sourceMappingURL=data:application/json;base64,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