import { CommonService } from "./common.service";
import 'jquery-ui-dist/jquery-ui';
export declare class ModuleLoader {
    private common;
    private _percentHeight;
    private _percentWidth;
    private _loaded;
    private _ready;
    private _url;
    private _listeners;
    private logger;
    private progress;
    private componentReference;
    private overlay;
    private mLoaderContent;
    private event;
    constructor(common: CommonService);
    loadModule(url: string): void;
    /**
     * This method is used to initialize module loader attributes
     * in case of error.
     */
    private dispose;
    /**
     * This method is used to load component instance from its lazy module path.
     * @param url
     */
    private load;
    /**
     * This method is used to add event listener to module loader.
     * @param name
     * @param callback
     */
    addEventListener(name: string, callback: Function): void;
    /**
     *   Unloads the module.
     *  Flash Player and AIR will not fully unload and garbage collect this module if
     *  there are any outstanding references to definitions inside the
     *  module.
     */
    unload(): void;
    /**
     * This method is used to dispatch event with given name.
     * @param name
     */
    private dispatchEvent;
    percentHeight: number;
    percentWidth: number;
    readonly loaded: boolean;
    readonly ready: boolean;
    readonly url: string;
}
