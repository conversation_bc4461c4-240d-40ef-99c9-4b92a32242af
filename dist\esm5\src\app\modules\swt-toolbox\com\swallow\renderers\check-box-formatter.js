/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CellBackgroundColor, CustomCell } from "./cellItemRenderUtilities";
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
export var CheckBoxFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var negative = false;
    /** @type {?} */
    var color = '#173553';
    /** @type {?} */
    var text;
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    var selectable = columnDef.params.grid.selectable;
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    /** @type {?} */
    var backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    var backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    var enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    var style = CustomCell(dataContext, field);
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        style += 'background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (showHideCells) {
        if (value === 'N' || value === 'false' || value === false || value == undefined) {
            // non checked
            text = "<div class=\"containerCheckBox\" style='" + style + "' ><input " + ((!selectable || !enabledFlag || !columnDef.params.grid.enabled) ? 'disabled' : '') + "   type='checkbox' style='" + style + "'  class='formator-checkbox'  /></div>";
        }
        else {
            // checked
            text = "<div class=\"containerCheckBox\" style='" + style + "' > <input " + ((!selectable || !enabledFlag || !columnDef.params.grid.enabled) ? 'disabled' : '') + " checked type='checkbox' style='" + style + "'    class='formator-checkbox'    /> </div> ";
        }
    }
    else {
        text = "";
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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