/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { VBox } from '../../../../controls/swt-vbox.component';
import { AssetsLegendItem } from './AssetsLegendItem';
var AssetsLegend = /** @class */ (function (_super) {
    tslib_1.__extends(AssetsLegend, _super);
    function AssetsLegend(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._seriesList = [];
        _this._dataProvider = [];
        return _this;
    }
    /**
     * @return {?}
     */
    AssetsLegend.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
        //Add 'implements OnInit' to the class.
    };
    Object.defineProperty(AssetsLegend.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dataProvider = value;
            this.refreshLegends(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(AssetsLegend.prototype, "seriesList", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesList;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesList = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} seriesList
     * @return {?}
     */
    AssetsLegend.prototype.refreshLegends = /**
     * @param {?} seriesList
     * @return {?}
     */
    function (seriesList) {
        this.legendContainer.removeAllChildren();
        for (var index = 0; index < seriesList.length; index++) {
            /** @type {?} */
            var element = seriesList[index];
            /** @type {?} */
            var assetsLegendItem = (/** @type {?} */ (this.legendContainer.addChild(AssetsLegendItem)));
            assetsLegendItem.yField = element.yField;
            assetsLegendItem.seriesStyle = element.appliedStyle;
            /** @type {?} */
            var assetsArray = element.legendDisplayName.split("|");
            /** @type {?} */
            var assetLabel = assetsArray[0];
            /** @type {?} */
            var assetValue = assetsArray[1];
            if (assetValue.length == 1)
                assetValue = "     " + assetValue;
            // assetValueAsLabel.text = assetValue;
            assetsLegendItem.labelValue = assetLabel;
            assetsLegendItem.liveValue = assetValue;
        }
    };
    /**
     * @param {?} series
     * @return {?}
     */
    AssetsLegend.prototype.getLegendItem = /**
     * @param {?} series
     * @return {?}
     */
    function (series) {
        /** @type {?} */
        var found = null;
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'AssetsLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                // if (item && (item.element == series)) {
                if (item && (item.yField === series.yField)) {
                    found = item;
                    break;
                }
            }
        }
        return found;
    };
    AssetsLegend.decorators = [
        { type: Component, args: [{
                    selector: 'AssetsLegend',
                    template: "\n        <VBox  #legendContainer width=\"100%\"> \n        </VBox>\n  ",
                    styles: ["\n      "]
                }] }
    ];
    /** @nocollapse */
    AssetsLegend.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    AssetsLegend.propDecorators = {
        legendContainer: [{ type: ViewChild, args: ['legendContainer',] }]
    };
    return AssetsLegend;
}(Container));
export { AssetsLegend };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    AssetsLegend.prototype.legendContainer;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype._seriesList;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQXNzZXRzTGVnZW5kLmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NoYXJ0cy9JTE1DaGFydHMvSUxNTGluZUNoYXJ0L2NvbnRyb2wvQXNzZXRzTGVnZW5kLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQVUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQ3pFLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSxnREFBZ0QsQ0FBQztBQUMzRSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0sa0NBQWtDLENBQUM7QUFHakUsT0FBTyxFQUFFLElBQUksRUFBRSxNQUFNLHlDQUF5QyxDQUFDO0FBQy9ELE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLG9CQUFvQixDQUFDO0FBQ3REO0lBU2tDLHdDQUFTO0lBZ0J2QyxzQkFBb0IsSUFBZ0IsRUFBVSxhQUE0QjtRQUExRSxZQUNJLGtCQUFNLElBQUksRUFBRSxhQUFhLENBQUMsU0FDN0I7UUFGbUIsVUFBSSxHQUFKLElBQUksQ0FBWTtRQUFVLG1CQUFhLEdBQWIsYUFBYSxDQUFlO1FBZGxFLGlCQUFXLEdBQUcsRUFBRSxDQUFDO1FBQ2pCLG1CQUFhLEdBQUcsRUFBRSxDQUFDOztJQWUzQixDQUFDOzs7O0lBZEQsK0JBQVE7OztJQUFSO1FBQ0ksaUdBQWlHO1FBQ2pHLHVDQUF1QztJQUMzQyxDQUFDO0lBQ0Qsc0JBQVcsc0NBQVk7Ozs7UUFBdkI7WUFDSSxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUM7UUFDOUIsQ0FBQzs7Ozs7UUFDRCxVQUF3QixLQUFLO1lBQ3pCLElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBQzNCLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDL0IsQ0FBQzs7O09BSkE7SUFVRCxzQkFBSSxvQ0FBVTs7OztRQUdkO1lBQ0ksT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBQzVCLENBQUM7Ozs7O1FBTEQsVUFBZSxLQUFLO1lBQ2hCLElBQUksQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDO1FBQzdCLENBQUM7OztPQUFBOzs7OztJQUtELHFDQUFjOzs7O0lBQWQsVUFBZSxVQUFVO1FBQ3JCLElBQUksQ0FBQyxlQUFlLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUN6QyxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsVUFBVSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTs7Z0JBQzlDLE9BQU8sR0FBVyxVQUFVLENBQUMsS0FBSyxDQUFDOztnQkFDckMsZ0JBQWdCLEdBQUcsbUJBQWtCLElBQUksQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLGdCQUFnQixDQUFDLEVBQUE7WUFDeEYsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUM7WUFDekMsZ0JBQWdCLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQyxZQUFZLENBQUM7O2dCQUdoRCxXQUFXLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7O2dCQUNsRCxVQUFVLEdBQUcsV0FBVyxDQUFDLENBQUMsQ0FBQzs7Z0JBQzNCLFVBQVUsR0FBRyxXQUFXLENBQUMsQ0FBQyxDQUFDO1lBRS9CLElBQUksVUFBVSxDQUFDLE1BQU0sSUFBSSxDQUFDO2dCQUN0QixVQUFVLEdBQUcsT0FBTyxHQUFHLFVBQVUsQ0FBQztZQUV0Qyx1Q0FBdUM7WUFFdkMsZ0JBQWdCLENBQUMsVUFBVSxHQUFHLFVBQVUsQ0FBQztZQUN6QyxnQkFBZ0IsQ0FBQyxTQUFTLEdBQUcsVUFBVSxDQUFDO1NBRTNDO0lBRUwsQ0FBQzs7Ozs7SUFHTSxvQ0FBYTs7OztJQUFwQixVQUFxQixNQUFjOztZQUMzQixLQUFLLEdBQXFCLElBQUk7UUFDbEMsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsV0FBVyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ2hFLElBQUksSUFBSSxDQUFDLGVBQWUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxJQUFJLGtCQUFrQixFQUFFOztvQkFDaEUsSUFBSSxHQUFxQixDQUFDLG1CQUFBLElBQUksQ0FBQyxlQUFlLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsRUFBb0IsQ0FBQztnQkFDL0YsMENBQTBDO2dCQUMxQyxJQUFJLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDLE1BQU0sQ0FBQyxFQUFFO29CQUN6QyxLQUFLLEdBQUcsSUFBSSxDQUFDO29CQUNiLE1BQU07aUJBQ1Q7YUFDSjtTQUNKO1FBQ0QsT0FBTyxLQUFLLENBQUM7SUFDakIsQ0FBQzs7Z0JBM0VKLFNBQVMsU0FBQztvQkFDUCxRQUFRLEVBQUUsY0FBYztvQkFDeEIsUUFBUSxFQUFFLHlFQUdYOzZCQUNVLFVBQ047aUJBQ047Ozs7Z0JBZnNDLFVBQVU7Z0JBRXhDLGFBQWE7OztrQ0FlakIsU0FBUyxTQUFDLGlCQUFpQjs7SUFzRWhDLG1CQUFDO0NBQUEsQUFoRkQsQ0FTa0MsU0FBUyxHQXVFMUM7U0F2RVksWUFBWTs7Ozs7O0lBQ3JCLHVDQUE4RDs7Ozs7SUFDOUQsbUNBQXlCOzs7OztJQUN6QixxQ0FBMkI7Ozs7O0lBYWYsNEJBQXdCOzs7OztJQUFFLHFDQUFvQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudCwgVmlld0NoaWxkLCBPbkluaXQsIEVsZW1lbnRSZWYgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuaW1wb3J0IHsgQ29udGFpbmVyIH0gZnJvbSAnLi4vLi4vLi4vLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudCc7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tICcuLi8uLi8uLi8uLi91dGlscy9jb21tb24uc2VydmljZSc7XHJcbmltcG9ydCB7IENoZWNrQm94TGVnZW5kSXRlbSB9IGZyb20gJy4vQ2hlY2tCb3hMZWdlbmRJdGVtJztcclxuaW1wb3J0IHsgU2VyaWVzIH0gZnJvbSAnLi9TZXJpZXMnO1xyXG5pbXBvcnQgeyBWQm94IH0gZnJvbSAnLi4vLi4vLi4vLi4vY29udHJvbHMvc3d0LXZib3guY29tcG9uZW50JztcclxuaW1wb3J0IHsgQXNzZXRzTGVnZW5kSXRlbSB9IGZyb20gJy4vQXNzZXRzTGVnZW5kSXRlbSc7XHJcbkBDb21wb25lbnQoe1xyXG4gICAgc2VsZWN0b3I6ICdBc3NldHNMZWdlbmQnLFxyXG4gICAgdGVtcGxhdGU6IGBcclxuICAgICAgICA8VkJveCAgI2xlZ2VuZENvbnRhaW5lciB3aWR0aD1cIjEwMCVcIj4gXHJcbiAgICAgICAgPC9WQm94PlxyXG4gIGAsXHJcbiAgICBzdHlsZXM6IFtgXHJcbiAgICAgIGBdXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBBc3NldHNMZWdlbmQgZXh0ZW5kcyBDb250YWluZXIgaW1wbGVtZW50cyBPbkluaXQge1xyXG4gICAgQFZpZXdDaGlsZCgnbGVnZW5kQ29udGFpbmVyJykgcHJvdGVjdGVkIGxlZ2VuZENvbnRhaW5lcjogVkJveDtcclxuICAgIHByaXZhdGUgX3Nlcmllc0xpc3QgPSBbXTtcclxuICAgIHByaXZhdGUgX2RhdGFQcm92aWRlciA9IFtdO1xyXG4gICAgbmdPbkluaXQoKTogdm9pZCB7XHJcbiAgICAgICAgLy9DYWxsZWQgYWZ0ZXIgdGhlIGNvbnN0cnVjdG9yLCBpbml0aWFsaXppbmcgaW5wdXQgcHJvcGVydGllcywgYW5kIHRoZSBmaXJzdCBjYWxsIHRvIG5nT25DaGFuZ2VzLlxyXG4gICAgICAgIC8vQWRkICdpbXBsZW1lbnRzIE9uSW5pdCcgdG8gdGhlIGNsYXNzLlxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBkYXRhUHJvdmlkZXIoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2RhdGFQcm92aWRlcjtcclxuICAgIH1cclxuICAgIHB1YmxpYyBzZXQgZGF0YVByb3ZpZGVyKHZhbHVlKSB7XHJcbiAgICAgICAgdGhpcy5fZGF0YVByb3ZpZGVyID0gdmFsdWU7XHJcbiAgICAgICAgdGhpcy5yZWZyZXNoTGVnZW5kcyh2YWx1ZSk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtOiBFbGVtZW50UmVmLCBwcml2YXRlIGNvbW1vblNlcnZpY2U6IENvbW1vblNlcnZpY2UpIHtcclxuICAgICAgICBzdXBlcihlbGVtLCBjb21tb25TZXJ2aWNlKVxyXG4gICAgfVxyXG5cclxuICAgIHNldCBzZXJpZXNMaXN0KHZhbHVlKSB7XHJcbiAgICAgICAgdGhpcy5fc2VyaWVzTGlzdCA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgZ2V0IHNlcmllc0xpc3QoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Nlcmllc0xpc3Q7XHJcbiAgICB9XHJcblxyXG4gICAgcmVmcmVzaExlZ2VuZHMoc2VyaWVzTGlzdCkge1xyXG4gICAgICAgIHRoaXMubGVnZW5kQ29udGFpbmVyLnJlbW92ZUFsbENoaWxkcmVuKCk7XHJcbiAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHNlcmllc0xpc3QubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGVsZW1lbnQ6IFNlcmllcyA9IHNlcmllc0xpc3RbaW5kZXhdO1xyXG4gICAgICAgICAgICB2YXIgYXNzZXRzTGVnZW5kSXRlbSA9IDxBc3NldHNMZWdlbmRJdGVtPnRoaXMubGVnZW5kQ29udGFpbmVyLmFkZENoaWxkKEFzc2V0c0xlZ2VuZEl0ZW0pO1xyXG4gICAgICAgICAgICBhc3NldHNMZWdlbmRJdGVtLnlGaWVsZCA9IGVsZW1lbnQueUZpZWxkO1xyXG4gICAgICAgICAgICBhc3NldHNMZWdlbmRJdGVtLnNlcmllc1N0eWxlID0gZWxlbWVudC5hcHBsaWVkU3R5bGU7XHJcblxyXG5cclxuICAgICAgICAgICAgdmFyIGFzc2V0c0FycmF5ID0gZWxlbWVudC5sZWdlbmREaXNwbGF5TmFtZS5zcGxpdChcInxcIik7XHJcbiAgICAgICAgICAgIGxldCBhc3NldExhYmVsID0gYXNzZXRzQXJyYXlbMF07XHJcbiAgICAgICAgICAgIGxldCBhc3NldFZhbHVlID0gYXNzZXRzQXJyYXlbMV07XHJcblxyXG4gICAgICAgICAgICBpZiAoYXNzZXRWYWx1ZS5sZW5ndGggPT0gMSlcclxuICAgICAgICAgICAgICAgIGFzc2V0VmFsdWUgPSBcIiAgICAgXCIgKyBhc3NldFZhbHVlO1xyXG5cclxuICAgICAgICAgICAgLy8gYXNzZXRWYWx1ZUFzTGFiZWwudGV4dCA9IGFzc2V0VmFsdWU7XHJcblxyXG4gICAgICAgICAgICBhc3NldHNMZWdlbmRJdGVtLmxhYmVsVmFsdWUgPSBhc3NldExhYmVsO1xyXG4gICAgICAgICAgICBhc3NldHNMZWdlbmRJdGVtLmxpdmVWYWx1ZSA9IGFzc2V0VmFsdWU7XHJcblxyXG4gICAgICAgIH1cclxuXHJcbiAgICB9XHJcblxyXG5cclxuICAgIHB1YmxpYyBnZXRMZWdlbmRJdGVtKHNlcmllczogU2VyaWVzKTogQXNzZXRzTGVnZW5kSXRlbSB7XHJcbiAgICAgICAgdmFyIGZvdW5kOiBBc3NldHNMZWdlbmRJdGVtID0gbnVsbDtcclxuICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMubGVnZW5kQ29udGFpbmVyLmdldENoaWxkcmVuKCkubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICAgICAgaWYgKHRoaXMubGVnZW5kQ29udGFpbmVyLmdldENoaWxkQXQoaSkuY2hpbGRUeXBlID09ICdBc3NldHNMZWdlbmRJdGVtJykge1xyXG4gICAgICAgICAgICAgICAgdmFyIGl0ZW06IEFzc2V0c0xlZ2VuZEl0ZW0gPSAodGhpcy5sZWdlbmRDb250YWluZXIuZ2V0Q2hpbGRBdChpKS5jb21wb25lbnQgYXMgQXNzZXRzTGVnZW5kSXRlbSk7XHJcbiAgICAgICAgICAgICAgICAvLyBpZiAoaXRlbSAmJiAoaXRlbS5lbGVtZW50ID09IHNlcmllcykpIHtcclxuICAgICAgICAgICAgICAgIGlmIChpdGVtICYmIChpdGVtLnlGaWVsZCA9PT0gc2VyaWVzLnlGaWVsZCkpIHtcclxuICAgICAgICAgICAgICAgICAgICBmb3VuZCA9IGl0ZW07XHJcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGZvdW5kO1xyXG4gICAgfVxyXG5cclxuXHJcblxyXG5cclxufSJdfQ==