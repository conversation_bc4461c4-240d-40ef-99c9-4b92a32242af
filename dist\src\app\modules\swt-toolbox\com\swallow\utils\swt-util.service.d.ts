import { CommonService } from "./common.service";
import { TranslateService } from "@ngx-translate/core";
export declare class SwtUtil {
    private common;
    static CommonServiceInstance: any;
    static COMMON_MODULE_ID: string;
    static SYSTEM_MODULE_ID: string;
    static AML_MODULE_ID: string;
    static DUP_MODULE_ID: string;
    static ARC_MODULE_ID: string;
    static INPUT_MODULE_ID: string;
    static CASH_MODULE_ID: string;
    static FATCA_MODULE_ID: string;
    static PREDICT_MODULE_ID: string;
    static PCM_MODULE_ID: string;
    static LOGIN_ID: string;
    static lang: string;
    static translate: TranslateService;
    static screenWidth: any;
    static screenHeight: any;
    private static actionMethod;
    private static actionPath;
    private static inputData;
    private static requestParams;
    private httpcomms;
    constructor(common: CommonService);
    /**
     * obtainURL
     * @return String - base url
     * This function is used to return the base url of the application.
     */
    static obtainURL(): Location;
    static getBaseURL(): string;
    static isVisible(e: any): boolean;
    static isEmpty(value: any): boolean;
    /**
     * getSystemMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the systemMessages resource Bundle.
     */
    static getSystemMessages(key: string, object?: Object): any;
    static convertObjectToArray(object: any): any;
    /**
     * getCommonMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the commonMessages resource Bundle.
     */
    static getCommonMessages(key: string, object?: Object): any;
    /**
     * getLoginMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the LoginMessages resource Bundle.
     */
    static getLoginMessages(key: string, object?: Object): any;
    /**
   * getAMLMessages
   *
   * @return String - value
   *
   * This function is used to return the value for the key passed
   * to it from the AMLMessages resource Bundle.
   */
    static getAMLMessages(key: string, object?: Object): any;
    /**
    * getDUPMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the DUPMessages resource Bundle.
    */
    static getDUPMessages(key: string, object?: Object): any;
    /**
    * getARCMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the ARCMessages resource Bundle.
    */
    static getARCMessages(key: string, object?: Object): any;
    /**
    * getInputMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the InputMessages resource Bundle.
    */
    static getInputMessages(key: string, object?: Object): any;
    /**
   * getCashMessages
   *
   * @return String - value
   *
   * This function is used to return the value for the key passed
   * to it from the InputMessages resource Bundle.
   */
    static getCashMessages(key: string, object?: Object): any;
    /**
    * getFatcaMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the FatcaMessages resource Bundle.
    */
    static getFatcaMessages(key: string, object?: Object): any;
    /**
    * getPredictMessage
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the PredictMessage resource Bundle.
    */
    static getPredictMessage(key: string, lang?: string): any;
    /**
    * This method converts the given array to string separated by given
    * separator.
    *
    * @param arrValue: Array
    * @param separator: String
    * @return String
    */
    static arrToStr(arrValue: any[], separator: string): string;
    /**
    * logError
    *
    * @return String - value
    *
    * This function is used to log the flex error in ERROR LOG.
    *
    * when  running the application in debugger version of flash player, this function will log the
    * error trace(location of error in src file) and when using non debugger version of flash player
    * this function will log the source file name and method name as the error Tace will be null.
    *
    */
    static logError(error: Error, moduleId: string, className: string, methodName: string, errorLocation: number): void;
    /**
    * this function is used to get the language of the user from jsp side
    * */
    static getUserLanguage(): string;
    /**
    * this function is used to get the current entityId
    * */
    static getCurrEntityId(): string;
    /**
    * this function is used to get the default entityId
    * */
    static getDefaultEntityId(): string;
    /**
     * getAMLMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    static getMessages(moduleId: string, key: string, object?: Object): any;
    /**
     * result
     *
     * @param event: ResultEvent
     *
     * This is a callback method, to handle reponse event
     */
    private static inputResult;
    /**
     * Default constructor
     **/
    SwtUtil(): void;
}
