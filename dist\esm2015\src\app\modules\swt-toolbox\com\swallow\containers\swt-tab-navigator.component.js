/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { TabCloseEvent } from './../events/swt-events.module';
import { Component, Input, ElementRef, ViewChild, Output, EventEmitter, ContentChildren, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
import { TabSelectEvent } from "../events/swt-events.module";
/**
 *
 * Tab component.
 * *
 */
//@dynamic
export class Tab extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions------------------------------------------------------------------------------------------------
        this.parent = null;
        this._active = false;
        this._visible = true;
        this._display = false;
        this._closable = false;
        this._order = 0;
    }
    //---label----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        this._label = value;
    }
    /**
     * @return {?}
     */
    get label() {
        return this._label;
    }
    //---label----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set order(value) {
        this._order = value;
    }
    /**
     * @return {?}
     */
    get order() {
        return this._order;
    }
    //---visible----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        this._visible = this.adaptValueAsBoolean(value);
        if (!this._visible) {
            this.display = this._visible;
        }
        else if (this.active) {
            this.display = true;
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    //---active----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set active(value) {
        if (value) {
            if (this.parent) {
                /** @type {?} */
                var indexActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.active == true));
                if (indexActive >= 0) {
                    this.parent.tabChildrenArray[indexActive].active = false;
                    this.parent.tabChildrenArray[indexActive].display = false;
                }
            }
            this.display = true;
        }
        this._active = this.adaptValueAsBoolean(value);
    }
    /**
     * @return {?}
     */
    get active() {
        return this._active;
    }
    //---display----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set display(value) {
        this._display = this.adaptValueAsBoolean(value);
        if (this._display) {
            $(this.elem.nativeElement).show();
        }
        else {
            $(this.elem.nativeElement).hide();
        }
    }
    /**
     * @return {?}
     */
    get display() {
        return this._display;
    }
    //---Closable----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set closable(value) {
        this._closable = this.adaptValueAsBoolean(value);
    }
    /**
     * @return {?}
     */
    get closable() {
        return this._closable;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        try {
            if (this.id == undefined) {
                throw new Error(">>> PROGRAMMING Error [mandotroy parameter] : you might forgot to add an id the the Tab created from the Html template , something like <... id='tab_id'..>  ");
            }
            else if (this.parent && this.parent.tabChildrenArray.length > 0) {
                // dynamic tabs
                /** @type {?} */
                let indexOfActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.active == true));
                if (indexOfActive == -1) {
                    // there is no active tab
                    this.parent.tabChildrenArray[0].active = true;
                    this.parent.tabChildrenArray[0].display = true;
                }
                else if (this.active == true) {
                    // there is already an active tab
                    this.parent.tabChildrenArray.forEach((/**
                     * @param {?} tab
                     * @return {?}
                     */
                    function (tab) {
                        if (tab != this) {
                            tab.active = false;
                            tab.display = false;
                        }
                    }));
                    this.active = true;
                    this.display = true;
                }
                else {
                    this.active = false;
                    this.display = false;
                }
            }
            else if (!this.active) {
                this.display = false;
            }
        }
        catch (error) {
            console.error('method [ngOnInit] - error:', error);
        }
    }
    /**
     * @param {?} e
     * @return {?}
     */
    isVisible(e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    }
    /**
     * Sets a style to the tab's header.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    setTabHeaderStyle(prop, value) {
        try {
            setTimeout((/**
             * @return {?}
             */
            () => {
                /** @type {?} */
                var header = $('#header_' + this.id);
                $(header[0]).attr("style", prop + ":" + value + "!important");
            }), 0);
        }
        catch (error) {
            console.error('error :', error);
        }
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this.parent;
            delete this._active;
            delete this._visible;
            delete this._label;
            delete this._display;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
    /**
     * setUndockPolicy
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    setUndockPolicy(index, value) {
        if (this.id >= index + 1) {
            this.getChildAt(index).undockPolicy = value;
        }
    }
}
Tab.decorators = [
    { type: Component, args: [{
                selector: 'SwtTab',
                template: `
        <div  class="swttab" >
            <ng-content></ng-content>
             <div  #_container ></div>
        </div> 
    `,
                styles: [`
         :host {
              margin:  0px;
              width: 100%;
              height: 100%;
              display:block;
              outline: none;
         }
        .swttab {
              box-sizing: border-box;
              display: flex;
              display: -moz-flex;
              display: -o-flex;
              display: -webkit-flex;
              flex-direction: column;
              width: 100%;
              height: 100%;
              outline: none;
        }
    `]
            }] }
];
/** @nocollapse */
Tab.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
Tab.propDecorators = {
    label: [{ type: Input, args: ["label",] }],
    order: [{ type: Input, args: ["order",] }],
    visible: [{ type: Input, args: ['visible',] }],
    active: [{ type: Input, args: ['active',] }],
    closable: [{ type: Input, args: ["closable",] }]
};
if (false) {
    /** @type {?} */
    Tab.prototype.parent;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._active;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._visible;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._label;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._display;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._closable;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._order;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype.commonService;
}
/**
 *
 * Tab component.
 * *
 */
//@dynamic
export class TabPushStategy extends Tab {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     * @param {?} cd
     */
    constructor(elem, commonService, cd) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.cd = cd;
    }
    //---active----------------------------------------------------------------------------------------------------------------- //
    /**
     * @param {?} value
     * @return {?}
     */
    set active(value) {
        if (value) {
            if (this.parent) {
                /** @type {?} */
                var indexActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.active == true));
                if (indexActive >= 0) {
                    this.parent.tabChildrenArray[indexActive].active = false;
                    this.parent.tabChildrenArray[indexActive].display = false;
                }
            }
            this.display = true;
        }
        this._active = this.adaptValueAsBoolean(value);
        if (this._active) {
            if (!((/** @type {?} */ (this.cd))).destroyed) {
                this.cd.markForCheck();
            }
        }
    }
    /**
     * @return {?}
     */
    get active() {
        return this._active;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        try {
            if (this.id == undefined) {
                throw new Error(">>> PROGRAMMING Error [mandotroy parameter] : you might forgot to add an id the the Tab created from the Html template , something like <... id='tab_id'..>  ");
            }
            else if (this.parent && this.parent.tabChildrenArray.length > 0) {
                // dynamic tabs
                /** @type {?} */
                let indexOfActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.active == true));
                if (indexOfActive == -1) {
                    // there is no active tab
                    this.parent.tabChildrenArray[0].active = true;
                    this.parent.tabChildrenArray[0].display = true;
                }
                else if (this.active == true) {
                    // there is already an active tab
                    this.parent.tabChildrenArray.forEach((/**
                     * @param {?} tab
                     * @return {?}
                     */
                    function (tab) {
                        if (tab != this) {
                            tab.active = false;
                            tab.display = false;
                        }
                    }));
                    this.active = true;
                    this.display = true;
                }
                else {
                    this.active = false;
                    this.display = false;
                }
            }
            else if (!this.active) {
                this.display = false;
            }
            TabSelectEvent.subscribe((/**
             * @param {?} tab
             * @return {?}
             */
            (tab) => {
                if (this.isVisible(this.elem.nativeElement)) {
                    if (!((/** @type {?} */ (this.cd))).destroyed) {
                        this.cd.markForCheck();
                    }
                }
            }));
        }
        catch (error) {
            console.error('method [ngOnInit] - error:', error);
        }
    }
}
TabPushStategy.decorators = [
    { type: Component, args: [{
                selector: 'SwtTabPushStrategy',
                template: `
        <div  class="swttab" >
            <ng-content></ng-content>
             <div  #_container ></div>
        </div> 
    `,
                providers: [{ provide: Tab, useExisting: TabPushStategy }],
                changeDetection: ChangeDetectionStrategy.OnPush,
                styles: [`
         :host {
              margin:  0px;
              width: 100%;
              height: 100%;
              display:block;
              outline: none;
         }
        .swttab {
              box-sizing: border-box;
              display: flex;
              display: -moz-flex;
              display: -o-flex;
              display: -webkit-flex;
              flex-direction: column;
              width: 100%;
              height: 100%;
              outline: none;
        }
    `]
            }] }
];
/** @nocollapse */
TabPushStategy.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: ChangeDetectorRef }
];
TabPushStategy.propDecorators = {
    active: [{ type: Input, args: ['active',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.cd;
}
//@dynamic
export class SwtTabNavigator extends Container {
    /**
     * constructor
     * @param {?} elem
     * @param {?} commonService
     * @param {?} cdr
     */
    constructor(elem, commonService, cdr) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.cdr = cdr;
        //---Properties definitions------------------------------------------------------------------------------------------------
        this.selectedTabId = 0;
        this.tabChildrenArray = [];
        this._selectedIndex = 0;
        this._selectedLabel = null;
        this._selectedTab = null;
        this._borderBottom = true;
        this._borderTop = true;
        this._onChange = new Function();
        this.MOUSE_DOWN = new Function();
        //@Output('MOUSE_DOWN') MOUSE_DOWN: EventEmitter<any> = new EventEmitter<any>();
        this.aboutActive = true;
        this.clientWidth = 0;
        this.scrollWidth = 0;
        this.scrollValue = 0;
        this.maxScroll = 0;
        // Make panelTop DOM reference.
        //---Outputs------------------------------------------------------------------------------------------------------
        this.onChange_ = new EventEmitter();
        this._showDropDown = false;
        this._applyOrder = false;
    }
    /**
     * @return {?}
     */
    sortTabChildrenArray() {
        if (this.applyOrder)
            return this.tabChildrenArray.sort((/**
             * @param {?} a
             * @param {?} b
             * @return {?}
             */
            (a, b) => a['order'] > b['order'] ? 1 : a['order'] === b['order'] ? 0 : -1));
        else
            return this.tabChildrenArray;
    }
    /**
     * @return {?}
     */
    calculateHeigt() {
        return this.containerNavigator.nativeElement.offsetHeight - this.swtTabNavigator.nativeElement.offsetHeight;
    }
    //---ContentChildren------------------------------------------------------------------------------------------------
    /**
     * @param {?} items
     * @return {?}
     */
    set tabChildren(items) {
        items._results.forEach((/**
         * @param {?} tab
         * @return {?}
         */
        (tab) => {
            tab.parent = this;
        }));
        this.tabChildrenArray = items._results.filter((/**
         * @param {?} x
         * @return {?}
         */
        x => x.parent.id == this.id));
    }
    //---selectedTab--------------------------------------------------------------------------------------------------
    /**
     * @param {?} tab
     * @return {?}
     */
    set selectedTab(tab) {
        /** @type {?} */
        var index = this.tabChildrenArray.findIndex((/**
         * @param {?} x
         * @return {?}
         */
        x => ((x.id == tab.id) && tab.visible)));
        if (index >= 0) {
            this.tabChildrenArray[index].active = true;
            if (this.selectedIndex != index) {
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    this.onChange_.emit();
                    this._onChange();
                }), 0);
            }
            this.selectedIndex = index;
            this._selectedTab = tab;
        }
        else {
            throw new Error(">>> PROGRAMMING Error [wrong Tab] : equivalent Tab does not exist in TabNavigator.  ");
        }
    }
    /**
     * @return {?}
     */
    get selectedTab() {
        return this.tabChildrenArray[this.selectedIndex];
    }
    //---selectedChild--------------------------------------------------------------------------------------------------
    /**
     * @param {?} tab
     * @return {?}
     */
    set selectedChild(tab) {
        /** @type {?} */
        var index = this.tabChildrenArray.findIndex((/**
         * @param {?} x
         * @return {?}
         */
        x => x.id == tab.id));
        if (index >= 0) {
            this.tabChildrenArray[index].active = true;
            this.selectedIndex = index;
            this._selectedTab = tab;
        }
        else {
            throw new Error(">>> PROGRAMMING Error [wrong Tab] : equivalent Tab does not exist in TabNavigator.  ");
        }
    }
    /**
     * @return {?}
     */
    get selectedChild() {
        return this._selectedTab;
    }
    //---selectedLabel------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedLabel(value) {
        /** @type {?} */
        var index = this.tabChildrenArray.findIndex((/**
         * @param {?} x
         * @return {?}
         */
        x => x.label == value));
        if (index >= 0) {
            this.tabChildrenArray[index].active = true;
            this._selectedLabel = value;
        }
        else {
            throw new Error(">>> PROGRAMMING Error [wrong Label] : Tab with correspondent Label not exists.  ");
        }
    }
    /**
     * @return {?}
     */
    get selectedLabel() {
        if (this.selectedTab != null) {
            return this.selectedTab.label;
        }
        return undefined;
    }
    //---onChange-----------------------------------------------------------------------------------------------------
    /**
     * @return {?}
     */
    get onChange() {
        return this._onChange;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set onChange(value) {
        this._onChange = value;
    }
    //---selectedIndex------------------------------------------------------------------------------------------------
    /**
     * @param {?} index
     * @return {?}
     */
    set selectedIndex(index) {
        this._selectedIndex = index;
        if (this.tabChildrenArray && this.tabChildrenArray[index]) {
            this.tabChildrenArray[index].active = true;
            this._selectedTab = this.tabChildrenArray[index];
            this._selectedLabel = this.tabChildrenArray[index].label;
            this.selectedTabId = this.tabChildrenArray[index].id;
        }
    }
    /**
     * @return {?}
     */
    get selectedIndex() {
        return this._selectedIndex;
    }
    //---BorderBottom------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set borderBottom(value) {
        this._borderBottom = this.adaptValueAsBoolean(value);
        if (!this._borderBottom)
            $($($(this.elem.nativeElement).children()[0]).children()[1]).css("border-bottom", 0);
    }
    /**
     * @return {?}
     */
    get borderBottom() {
        return this._borderBottom;
    }
    //---BorderBottom------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set showDropDown(value) {
        this._showDropDown = this.adaptValueAsBoolean(value);
    }
    /**
     * @return {?}
     */
    get showDropDown() {
        return this._showDropDown;
    }
    //---BorderBottom------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set applyOrder(value) {
        this._applyOrder = this.adaptValueAsBoolean(value);
    }
    /**
     * @return {?}
     */
    get applyOrder() {
        return this._applyOrder;
    }
    //---BorderTop----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set borderTop(value) {
        this._borderTop = this.adaptValueAsBoolean(value);
        if (!this._borderTop)
            $($($(this.elem.nativeElement).children()[0]).children()[1]).css("border-top", 0);
    }
    /**
     * @return {?}
     */
    get borderTop() {
        return this._borderTop;
    }
    // ngAfterViewChecked(){
    //     //your code to update the model
    //     setTimeout(() => {
    //         this.cdr.detectChanges();
    //     }, 0);
    //  }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        this.tabChildrenArray = [];
        if (!this.id)
            this.id = "dynamic-" + Math.random().toString(36).substr(2, 5);
        this.navBarContainer = $($($(this.elem.nativeElement).children()[0]).children()[0]);
        this.navTabs = $(this.navBarContainer).find('.nav-tabs');
        $($($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('#test-dropdown-btn')).click((/**
         * @return {?}
         */
        () => {
            $($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle();
            /** @type {?} */
            const isVisible = $($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').is(':visible');
            setTimeout((/**
             * @return {?}
             */
            () => {
                if (isVisible) {
                    $("body").on("click.tabNavigator" + this.id, (/**
                     * @param {?} e
                     * @return {?}
                     */
                    (e) => {
                        $("body").off("click.tabNavigator" + this.id);
                        $($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle(false);
                    }));
                }
                else {
                    $($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle(true);
                }
            }), 0);
        }));
    }
    /**
     * ngAfterViewInit
     * @return {?}
     */
    ngAfterViewInit() {
        try {
            /** @type {?} */
            let intervalId = setInterval((/**
             * @return {?}
             */
            () => {
                if (this.tabChildrenArray && this.tabChildrenArray.length > 0) {
                    clearInterval(intervalId);
                    /** @type {?} */
                    let indexOfActive = this.tabChildrenArray.findIndex((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => (x.active == true && x.visible)));
                    if (indexOfActive == -1 && this.tabChildrenArray.length > 0) {
                        /** @type {?} */
                        var arr = this.tabChildrenArray.filter((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => x['visible'] == true));
                        arr.forEach((/**
                         * @param {?} tab
                         * @return {?}
                         */
                        function (tab) {
                            tab.active = false;
                            tab.display = false;
                        }));
                        if (arr && arr.length > 0)
                            arr[0].active = true;
                    }
                }
            }), 1000);
            this.navBarContainer = $($($(this.elem.nativeElement).children()[0]).children()[0]);
            this.navTabs = $(this.navBarContainer).find('.nav-tabs');
            this.navTabsLi = $(this.navTabs).find('li');
            $(this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
            $(this.navBarContainer).find('.leftArrow').css('width', '0px');
            $(this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
            this.scrollWidth = Number($(this.navTabs)[0].scrollWidth);
            this.checkValue();
        }
        catch (error) {
            console.error('method [ngAfterViewInit] - error:', error);
        }
    }
    /**
     * setSelectedTab : handle active/inactive tab
     * @param {?} tab
     * @return {?}
     */
    setSelectedTab(tab) {
        try {
            if (tab && tab.id != this.selectedTab.id) {
                this.tabChildrenArray.forEach((/**
                 * @param {?} swttab
                 * @return {?}
                 */
                function (swttab) {
                    if (tab != swttab) {
                        swttab.active = false;
                        swttab.display = false;
                    }
                    else {
                        tab.active = true;
                        tab.display = true;
                    }
                }));
                TabSelectEvent.emit(tab);
                /** @type {?} */
                let index = this.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.id == tab.id));
                if (this.selectedIndex != index) {
                    this._selectedIndex = index;
                    this._selectedLabel = this.tabChildrenArray[this.selectedIndex].label;
                    this._selectedTab = this.tabChildrenArray[this.selectedIndex];
                    this.onChange_.emit();
                    this._onChange();
                }
                else {
                    this._selectedIndex = index;
                    this._selectedTab = this.tabChildrenArray[this.selectedIndex];
                    this._selectedLabel = this.tabChildrenArray[this.selectedIndex].label;
                }
            }
        }
        catch (error) {
            console.error('method [setSelectedTab] error :', error);
        }
    }
    /**
     * @return {?}
     */
    getSelectedTab() {
        try {
            if (this.selectedTab) {
                return this.selectedTab;
            }
            return null;
        }
        catch (error) {
            console.error('method [getSelectedTab] error :', error);
        }
    }
    /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    addChild(type) {
        try {
            /** @type {?} */
            var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
            /** @type {?} */
            var tab = new Tab(this.elem, this.commonService);
            /** @type {?} */
            var componentInstance = this._container.createComponent(comp);
            /* Push the component so that we can keep track of which components are created */
            this.components.push(componentInstance);
            /** @type {?} */
            let id = "dynamic-" + Math.random().toString(36).substr(2, 5);
            $($($(componentInstance.instance)[0])[0].elem.nativeElement).attr('id', id);
            tab = (/** @type {?} */ (componentInstance.instance));
            tab.id = id;
            tab.parent = this;
            tab.display = false;
            this.tabChildrenArray.push(tab);
            this.checkValue();
            return (tab);
        }
        catch (error) {
            console.error('method [addChild] error :', error);
        }
    }
    /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    addChildPushStategy(type) {
        try {
            /** @type {?} */
            var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
            /** @type {?} */
            var tab = new TabPushStategy(this.elem, this.commonService, this.cdr);
            /** @type {?} */
            var componentInstance = this._container.createComponent(comp);
            /* Push the component so that we can keep track of which components are created */
            this.components.push(componentInstance);
            /** @type {?} */
            var id = "dynamic-" + Math.random().toString(36).substr(2, 5);
            $($($(componentInstance.instance)[0])[0].elem.nativeElement).attr('id', id);
            tab = (/** @type {?} */ (componentInstance.instance));
            tab.id = id;
            tab.parent = this;
            tab.display = false;
            this.tabChildrenArray.push(tab);
            this.checkValue();
            return (tab);
        }
        catch (error) {
            console.error('method [addChild] error :', error);
        }
    }
    /**
     * removeChild : removes a tab dynamically
     * @param {?} componentClass
     * @return {?}
     */
    removeChild(componentClass) {
        try {
            /** @type {?} */
            var index = this.tabChildrenArray.findIndex((/**
             * @param {?} x
             * @return {?}
             */
            x => x == componentClass));
            // const component = this.components.find((component) => component.instance.id == componentClass.id);
            if (index !== -1) {
                this.tabChildrenArray[index].display = false;
                this.tabChildrenArray[index].active = false;
                //   if(component)
                //     this._container.remove(this._container.indexOf(component));
                super.removeChild(componentClass);
                this.tabChildrenArray.splice(index, 1);
                this.components.splice(index, 1);
            }
            // $("#"+ $.escapeSelector(componentClass.id)).remove();
        }
        catch (error) {
            console.error('method [removeChild] error :', error);
        }
    }
    /**
     * getChildAt : returns the id of a child Tab in a specific index.
     * @param {?} index
     * @return {?}
     */
    getChildAt(index) {
        try {
            if (index < this.tabChildrenArray.length) {
                return this.tabChildrenArray[index];
            }
            return null;
        }
        catch (error) {
            console.error('method [getChildAt] error :', error);
        }
    }
    /**
     * getTabChildren : returns tabNavigator's children
     * @return {?}
     */
    getTabChildren() {
        return this.tabChildrenArray;
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            delete this._selectedIndex;
            delete this._selectedLabel;
            delete this._selectedTab;
            delete this.selectedTabId;
            delete this._borderBottom;
            delete this._borderTop;
            delete this.tabChildrenArray;
            delete this._onChange;
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    }
    /**
     * setUndockPolicyForTab
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    setUndockPolicyForTab(index, value) {
        /** @type {?} */
        var tab = new Tab(this.elem, this.commonService);
        tab.setUndockPolicy(index, value);
    }
    //-Fix M4869/ISS-008.
    /**
     * @param {?} tab
     * @param {?} event
     * @return {?}
     */
    onMousedown(tab, event) {
        try {
            event['_currentTab'] = this.selectedTab;
            event['_target'] = tab;
            if (tab && this.MOUSE_DOWN.name != 'anonymous') {
                /** @type {?} */
                var result = this.MOUSE_DOWN(event);
                if (result)
                    this.setSelectedTab(tab);
            }
            else {
                this.setSelectedTab(tab);
            }
            this.checkValue();
        }
        catch (error) {
            console.error('method [onMousedown] - error :', error);
        }
    }
    //------------------------------------------------------------------------------------------------------------
    /**
     * @param {?} side
     * @return {?}
     */
    scrollTabs(side) {
        this.dispose();
        this.timer = setInterval((/**
         * @return {?}
         */
        () => {
            if (side === 'right') {
                if (this.scrollValue + 10 < this.maxScroll) {
                    this.scrollValue += 10;
                }
                else {
                    this.scrollValue = this.maxScroll;
                }
            }
            else if (side === 'left') {
                if (this.scrollValue - 10 > 0) {
                    this.scrollValue -= 10;
                }
                else {
                    this.scrollValue = 0;
                }
            }
            $($(this.navTabs)[0]).scrollLeft(this.scrollValue);
            if (this.scrollValue == this.maxScroll) {
                $(this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                $(this.navBarContainer).find('.leftArrow').css('width', '20px');
                $(this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                $(this.navBarContainer).find('.rightArrow').css('visibility', 'hidden');
                this.dispose();
            }
            if (this.scrollValue == 0) {
                $(this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
                $(this.navBarContainer).find('.leftArrow').css('width', '0px');
                $(this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
                $(this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
                this.dispose();
            }
            if (this.scrollValue !== 0 && this.scrollValue !== this.maxScroll) {
                $(this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                $(this.navBarContainer).find('.leftArrow').css('width', '20px');
                $(this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                $(this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
            }
            this.checkValue();
        }), 20);
    }
    /**
     * @return {?}
     */
    dispose() {
        clearInterval(this.timer);
    }
    /**
     * @param {?} tab
     * @return {?}
     */
    removeContentTab(tab) {
        if (tab.enabled) {
            /** @type {?} */
            const index = this.tabChildrenArray.indexOf(tab);
            this.removeChild(tab);
            if (this.selectedIndex == index) {
                if (this.tabChildrenArray.length > 1) {
                    for (var j = ((index < this.tabChildrenArray.length) ? index : index - 1); j > 0; j--) {
                        if (this.tabChildrenArray[j].enabled)
                            break;
                    }
                    this.selectedTab = this.tabChildrenArray[j];
                    this.onMousedown(this.tabChildrenArray[j], event);
                    event.preventDefault();
                }
            }
            this.checkValue();
            TabCloseEvent.emit(tab.id);
        }
    }
    /**
     * @param {?} tab
     * @return {?}
     */
    scrollToTabFromCombo(tab) {
        /** @type {?} */
        const offsetLeft = Number($(this.navTabsLi)[this.tabChildrenArray.indexOf(tab)].offsetLeft);
        /** @type {?} */
        const offsetWidth = Number($(this.navTabsLi)[this.tabChildrenArray.indexOf(tab)].offsetWidth);
        /** @type {?} */
        const scrollToTab = (offsetLeft - Number(this.scrollWidth)) + offsetWidth;
        this.scrollValue = scrollToTab;
        this.selectedTab = tab;
        if (this.clientWidth > this.scrollWidth) {
            this.timer = setInterval((/**
             * @return {?}
             */
            () => {
                $($(this.navTabs)[0]).scrollLeft(this.scrollValue);
                if (offsetLeft < this.scrollWidth) {
                    this.scrollValue = 0;
                    $(this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
                    $(this.navBarContainer).find('.leftArrow').css('width', '0px');
                    $(this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
                    $(this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
                    this.dispose();
                }
                if ((offsetLeft + offsetWidth) == this.scrollWidth) {
                    $(this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                    $(this.navBarContainer).find('.leftArrow').css('width', '20px');
                    $(this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                    $(this.navBarContainer).find('.rightArrow').css('visibility', 'hidden');
                    this.dispose();
                }
                if (offsetLeft > 23 && (offsetLeft + offsetWidth) > this.scrollWidth) {
                    $(this.navTabs).find('.leftArrow').css('visibility', 'visible');
                    $(this.navTabs).find('.leftArrow').css('width', '20px');
                    $(this.navTabs).find('.leftArrow').css('margin-left', '2px');
                    $(this.navTabs).find('.rightArrow').css('visibility', 'visible');
                    this.dispose();
                }
            }), 20);
        }
        $($(this.navBarContainer).find('.btn-group')).find('.dropdown-menu').toggle();
    }
    /**
     * @return {?}
     */
    checkValue() {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.navBarContainer = $($($(this.elem.nativeElement).children()[0]).children()[0]);
            this.navTabs = $(this.navBarContainer).find('.nav-tabs');
            this.navTabsLi = $(this.navTabs).find('li');
            if ($(this.navTabs)[0] != undefined) {
                /** @type {?} */
                var scrollWidth = Number($(this.navTabs)[0].scrollWidth);
                if ($(this.navTabsLi)[this.tabChildrenArray.length - 1] != undefined) {
                    this.clientWidth = Number($($(this.navTabsLi)[this.tabChildrenArray.length - 1])[0].offsetLeft) +
                        Number($($(this.navTabsLi)[this.tabChildrenArray.length - 1])[0].offsetWidth);
                }
                this.maxScroll = Number($(this.navTabs)[0].scrollWidth) - Number($(this.navTabs)[0].clientWidth);
            }
        }), 0);
    }
}
SwtTabNavigator.decorators = [
    { type: Component, args: [{
                selector: 'SwtTabNavigator',
                template: `
      <div >
          <div class="nav-bar-container">
              <div class="btn leftArrow" (mousedown)="scrollTabs('left')" (mouseup)="dispose()"><span><b>&#9666;</b></span></div>
              <ul *ngIf="aboutActive" (mouseup)="dispose()"   id="swtTabNavigator" class="nav nav-tabs ">
              <ng-container   *ngFor="let tab of sortTabChildrenArray() let act = index"  >
                    <li *ngIf="tab.visible && tab.label"  
                        id='header_{{tab.id}}'
                        [class.active]="tab.active"
                        [title]="tab.toolTip" 
                        [class.disabled-container]="!tab.enabled"
                        class="tabNavigator-tabs"
                            (click)="onMousedown(tab, $event)"
                             >
                            <span style="display:flex;">
                          {{tab.label}}
                               <i *ngIf="tab.closable"   class="fa fa-times closeFav" aria-hidden="true" (click)="removeContentTab(tab)"></i>
                        </span>

                    </li>
               </ng-container>
          </ul>
            
              <div class="btn rightArrow" [ngStyle]="{'visibility': ( tabChildrenArray.length == 0  || clientWidth <  scrollWidth  || scrollValue ==  maxScroll ) ? 'hidden':'visible'}" (mousedown)="scrollTabs('right')" (mouseup)="dispose()"><span><b>&#9656;</b></span></div>
                <div class="btn-group"[ngStyle]="{'visibility': tabChildrenArray.length > 0 && showDropDown ? 'visible':'hidden'}" >
                <a id="test-dropdown-btn" class="input-group-addon dropdownBtn dropdown-toggle"  >
                        <i _ngcontent-c8="" class="glyphicon glyphicon-triangle-bottom"></i>
                    </a>
                    <ul  class="dropdown-menu" role="menu"   >
                        <li role="menuitem" *ngFor="let tab of tabChildrenArray">
                            <a class="dropdown-item" (click)="scrollToTabFromCombo(tab)">
                                <b>{{ tab.label }}</b>
                            </a>
                        </li>
                  </ul> 
                  
                </div>

          </div>
          <div  class="tabNavigator-content" style="width:100%; height:calc(100% - 20px);">
              <ng-content></ng-content>
              <div #_container ></div>
          </div>
    </div>
  `,
                styles: [`
         :host {
               display: block;
               margin: 0px 0px 5px 0px;
               width: 100%;
               height: 100%;
               outline: none;
         }
       .dropdownBtn{
           height: 20px;
           width: 20px;
           border: 1px solid #9C9FA1;
           padding: 0px;
           cursor: default;
           margin-top: 1px;
           border-radius: 2px;
           text-decoration: none;
           color: black;
           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);
           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);
           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);
           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);
           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff );
       }
       .leftArrow, .rightArrow {
           height: 20px;
           width: 20px;
           border: 1px solid #9C9FA1;
           padding: 0px; 
           cursor: default;
           margin-top: 1px;
           border-radius: 2px;
           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);
           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);
           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);
           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);
           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff);
       }
       .leftArrow:hover, .rightArrow:hover {
           border: 1px solid #009DFF;
           background-image: -webkit-linear-gradient(top,#FFFFFF, #EEEEEE);
           background-image: -moz-linear-gradient(top, #FFFFFF, #EEEEEE);
           background-image: -ms-linear-gradient(top, #FFFFFF, #EEEEEE);
           background-image: -o-linear-gradient(top,#FFFFFF, #EEEEEE);
           background-image: linear-gradient(to bottom,#FFFFFF, #EEEEEE);
       }
       .leftArrow>span, .rightArrow>span {
           font-size: 14px;
       }
       .nav-bar-container{
           width:100%;
           display: flex;
           margin: 0px 0px 0px -2px !important;
           display:-ms-flexbox;
           -ms-flex-wrap: nowrap;
       }
       
       .icon {
           width: 15px;
           margin-top: -3px;
       }
       .nav {
           padding: 0px;
           height: 20px;
           overflow-x: auto;
           overflow-y: hidden;
           margin-top: 1px;
           flex-grow:1;
           width: max-content;
       }
       /* hide scroll bar and keep div scrollable */
       .nav::-webkit-scrollbar {
           width: 0px;
       }
       /* width */
       .nav::-webkit-scrollbar:horizontal {
           height: 0px;
       }
        
       .nav-tabs{
            padding: 0px;
            height: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            margin-top: 1px;
            -ms-flex-positive: 1;
            flex-grow: 1;
            display:flex;
            width: max-content;
       }

       .closeFav{
            color: #EB5946;
            border-radius: 2px;
            font-size: 12px;
            padding: 1px;
            padding-top: 0px;
            position: relative;
            left: 6px;
            top: 1px;
       } 
       .btn-group> ul {
           margin-top: -1px;
           margin-left: -139px;
           width: -webkit-stretch;
       }
       .btn-group> ul> li {
           font-size: 11px;
           font-weight: bold;
       }
       .btn-group> ul> li> a:hover {
           background-color: #b2e1ff !important;
           cursor: default;
       }



  `]
            }] }
];
/** @nocollapse */
SwtTabNavigator.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: ChangeDetectorRef }
];
SwtTabNavigator.propDecorators = {
    containerNavigator: [{ type: ViewChild, args: ["containerNavigator",] }],
    swtTabNavigator: [{ type: ViewChild, args: ["swtTabNavigator",] }],
    onChange_: [{ type: Output, args: ['onChange',] }],
    tabChildren: [{ type: ContentChildren, args: [Tab,] }],
    borderBottom: [{ type: Input, args: ['borderBottom',] }],
    showDropDown: [{ type: Input, args: ['showDropDown',] }],
    applyOrder: [{ type: Input, args: ['applyOrder',] }],
    borderTop: [{ type: Input, args: ['borderTop',] }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.selectedTabId;
    /** @type {?} */
    SwtTabNavigator.prototype.tabChildrenArray;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedTab;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._borderBottom;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._borderTop;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._onChange;
    /** @type {?} */
    SwtTabNavigator.prototype.MOUSE_DOWN;
    /** @type {?} */
    SwtTabNavigator.prototype.aboutActive;
    /** @type {?} */
    SwtTabNavigator.prototype.clientWidth;
    /** @type {?} */
    SwtTabNavigator.prototype.scrollWidth;
    /** @type {?} */
    SwtTabNavigator.prototype.timer;
    /** @type {?} */
    SwtTabNavigator.prototype.scrollValue;
    /** @type {?} */
    SwtTabNavigator.prototype.maxScroll;
    /** @type {?} */
    SwtTabNavigator.prototype.navBarContainer;
    /** @type {?} */
    SwtTabNavigator.prototype.navTabs;
    /** @type {?} */
    SwtTabNavigator.prototype.navTabsLi;
    /** @type {?} */
    SwtTabNavigator.prototype.containerNavigator;
    /** @type {?} */
    SwtTabNavigator.prototype.swtTabNavigator;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.onChange_;
    /** @type {?} */
    SwtTabNavigator.prototype._showDropDown;
    /** @type {?} */
    SwtTabNavigator.prototype._applyOrder;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.cdr;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXRhYi1uYXZpZ2F0b3IuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRhaW5lcnMvc3d0LXRhYi1uYXZpZ2F0b3IuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0sK0JBQStCLENBQUM7QUFDOUQsT0FBTyxFQUFFLFNBQVMsRUFBVSxLQUFLLEVBQUUsVUFBVSxFQUFpQixTQUFTLEVBQStFLE1BQU0sRUFBRSxZQUFZLEVBQUUsZUFBZSxFQUF1SCxpQkFBaUIsRUFBRSx1QkFBdUIsRUFBRSxNQUFNLGVBQWUsQ0FBQzs7O01BSTlXLENBQUMsR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFDO0FBRTNCLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSwyQkFBMkIsQ0FBQztBQUN0RCxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFDeEQsT0FBTyxFQUFFLGNBQWMsRUFBRSxNQUFNLDZCQUE2QixDQUFDOzs7Ozs7O0FBdUM3RCxNQUFNLE9BQU8sR0FBSSxTQUFRLFNBQVM7Ozs7OztJQTRGOUIsWUFBc0IsSUFBZ0IsRUFBYSxhQUE0QjtRQUMzRSxLQUFLLENBQUMsSUFBSSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1FBRFQsU0FBSSxHQUFKLElBQUksQ0FBWTtRQUFhLGtCQUFhLEdBQWIsYUFBYSxDQUFlOztRQXpGeEUsV0FBTSxHQUFHLElBQUksQ0FBQztRQUNYLFlBQU8sR0FBRyxLQUFLLENBQUM7UUFDaEIsYUFBUSxHQUFHLElBQUksQ0FBQztRQUVoQixhQUFRLEdBQUcsS0FBSyxDQUFDO1FBQ2pCLGNBQVMsR0FBRyxLQUFLLENBQUM7UUFDbEIsV0FBTSxHQUFVLENBQUMsQ0FBQztJQXFGNUIsQ0FBQzs7Ozs7O0lBbEZELElBQ0ksS0FBSyxDQUFDLEtBQVU7UUFDaEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7SUFDeEIsQ0FBQzs7OztJQUVELElBQUksS0FBSztRQUNMLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztJQUN2QixDQUFDOzs7Ozs7SUFHQSxJQUNJLEtBQUssQ0FBQyxLQUFVO1FBQ2hCLElBQUksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO0lBQ3hCLENBQUM7Ozs7SUFFRCxJQUFJLEtBQUs7UUFDTCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDdkIsQ0FBQzs7Ozs7O0lBR0YsSUFDSSxPQUFPLENBQUMsS0FBVTtRQUNsQixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoRCxJQUFHLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBQztZQUNkLElBQUksQ0FBQyxPQUFPLEdBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQztTQUM5QjthQUFLLElBQUcsSUFBSSxDQUFDLE1BQU0sRUFBQztZQUNqQixJQUFJLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQztTQUNyQjtJQUNMLENBQUM7Ozs7SUFDRCxJQUFJLE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7SUFDekIsQ0FBQzs7Ozs7O0lBR0QsSUFDSSxNQUFNLENBQUMsS0FBVTtRQUNqQixJQUFHLEtBQUssRUFBQztZQUNMLElBQUcsSUFBSSxDQUFDLE1BQU0sRUFBQzs7b0JBQ1AsV0FBVyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsU0FBUzs7OztnQkFBQyxDQUFDLENBQUEsRUFBRSxDQUFBLENBQUMsQ0FBQyxNQUFNLElBQUksSUFBSSxFQUFFO2dCQUM5RSxJQUFHLFdBQVcsSUFBSSxDQUFDLEVBQUU7b0JBQ2pCLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxDQUFDLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztvQkFDekQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxXQUFXLENBQUMsQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO2lCQUM3RDthQUNKO1lBQ0QsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7U0FDdkI7UUFDRCxJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUNuRCxDQUFDOzs7O0lBQ0QsSUFBSSxNQUFNO1FBQ04sT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO0lBQ3hCLENBQUM7Ozs7OztJQUlELElBQUksT0FBTyxDQUFDLEtBQVU7UUFDbEIsSUFBSSxDQUFDLFFBQVEsR0FBRSxJQUFJLENBQUMsbUJBQW1CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDL0MsSUFBRyxJQUFJLENBQUMsUUFBUSxFQUFDO1lBQ2IsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUM7U0FDckM7YUFBSTtZQUNELENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO1NBQ3JDO0lBQ0wsQ0FBQzs7OztJQUNELElBQUksT0FBTztRQUNQLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztJQUN6QixDQUFDOzs7Ozs7SUFHRCxJQUNJLFFBQVEsQ0FBQyxLQUFVO1FBQ25CLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ3JELENBQUM7Ozs7SUFFRCxJQUFJLFFBQVE7UUFDUixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7SUFDMUIsQ0FBQzs7Ozs7SUFhRCxRQUFRO1FBQ0osSUFBRztZQUNDLElBQUcsSUFBSSxDQUFDLEVBQUUsSUFBSyxTQUFTLEVBQUU7Z0JBQ3RCLE1BQU0sSUFBSSxLQUFLLENBQUMsK0pBQStKLENBQUMsQ0FBQzthQUNwTDtpQkFBSyxJQUFHLElBQUksQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFOzs7b0JBRXpELGFBQWEsR0FBRSxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFNBQVM7Ozs7Z0JBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsTUFBTSxJQUFJLElBQUksRUFBQztnQkFDOUUsSUFBRyxhQUFhLElBQUksQ0FBQyxDQUFDLEVBQUM7b0JBQ25CLHlCQUF5QjtvQkFDekIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEdBQUUsSUFBSSxDQUFDO29CQUM3QyxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sR0FBRSxJQUFJLENBQUM7aUJBQ2pEO3FCQUFLLElBQUksSUFBSSxDQUFDLE1BQU0sSUFBSSxJQUFJLEVBQUU7b0JBQzNCLGlDQUFpQztvQkFDakMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxPQUFPOzs7O29CQUFDLFVBQVUsR0FBUTt3QkFDbkQsSUFBRyxHQUFHLElBQUksSUFBSSxFQUFFOzRCQUNaLEdBQUcsQ0FBQyxNQUFNLEdBQUUsS0FBSyxDQUFDOzRCQUNsQixHQUFHLENBQUMsT0FBTyxHQUFFLEtBQUssQ0FBQzt5QkFDdEI7b0JBQ0wsQ0FBQyxFQUFDLENBQUM7b0JBQ0gsSUFBSSxDQUFDLE1BQU0sR0FBRSxJQUFJLENBQUM7b0JBQ2xCLElBQUksQ0FBQyxPQUFPLEdBQUUsSUFBSSxDQUFDO2lCQUN0QjtxQkFBSTtvQkFDRCxJQUFJLENBQUMsTUFBTSxHQUFDLEtBQUssQ0FBQztvQkFDbEIsSUFBSSxDQUFDLE9BQU8sR0FBQyxLQUFLLENBQUM7aUJBQ3RCO2FBQ0o7aUJBQ0ksSUFBRyxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUM7Z0JBQ2pCLElBQUksQ0FBQyxPQUFPLEdBQUMsS0FBSyxDQUFDO2FBQ3RCO1NBQ0o7UUFBQSxPQUFNLEtBQUssRUFBQztZQUNULE9BQU8sQ0FBQyxLQUFLLENBQUMsNEJBQTRCLEVBQUMsS0FBSyxDQUFDLENBQUM7U0FDckQ7SUFDTCxDQUFDOzs7OztJQUdELFNBQVMsQ0FBQyxDQUFDO1FBQ1AsT0FBTyxDQUFDLENBQUMsQ0FBRSxDQUFDLENBQUMsV0FBVyxJQUFJLENBQUMsQ0FBQyxZQUFZLElBQUksQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLE1BQU0sQ0FBRSxDQUFDO0lBQzlFLENBQUM7Ozs7Ozs7SUFNTSxpQkFBaUIsQ0FBQyxJQUFJLEVBQUcsS0FBSztRQUNqQyxJQUFHO1lBQ0MsVUFBVTs7O1lBQUMsR0FBRyxFQUFFOztvQkFDUixNQUFNLEdBQUcsQ0FBQyxDQUFDLFVBQVUsR0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDO2dCQUNsQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxJQUFJLEdBQUMsR0FBRyxHQUFDLEtBQUssR0FBQyxZQUFZLENBQUMsQ0FBQztZQUM1RCxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7U0FDVDtRQUFBLE9BQU0sS0FBSyxFQUFDO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsS0FBSyxDQUFDLENBQUE7U0FDbEM7SUFDTCxDQUFDOzs7OztJQUtELFdBQVc7UUFDUCxJQUFHO1lBQ0MsSUFBSSxDQUFDLDhCQUE4QixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7WUFDaEUsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFFO1lBQ3BCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztZQUNwQixPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7WUFDckIsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDO1lBQ25CLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztTQUV4QjtRQUFBLE9BQU0sS0FBSyxFQUFFO1lBQ1YsT0FBTyxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUMsS0FBSyxDQUFDLENBQUE7U0FDakM7SUFDTCxDQUFDOzs7Ozs7OztJQVVNLGVBQWUsQ0FBQyxLQUFZLEVBQUUsS0FBWTtRQUM3QyxJQUFHLElBQUksQ0FBQyxFQUFFLElBQUssS0FBSyxHQUFHLENBQUMsRUFBRTtZQUV0QixJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksR0FBRyxLQUFLLENBQUM7U0FFL0M7SUFFTCxDQUFDOzs7WUF0TkosU0FBUyxTQUFDO2dCQUNQLFFBQVEsRUFBRSxRQUFRO2dCQUNsQixRQUFRLEVBQUU7Ozs7O0tBS1Q7eUJBQ1E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7S0FtQlI7YUFDSjs7OztZQTlDa0MsVUFBVTtZQU9wQyxhQUFhOzs7b0JBb0RqQixLQUFLLFNBQUMsT0FBTztvQkFVWixLQUFLLFNBQUMsT0FBTztzQkFVZCxLQUFLLFNBQUMsU0FBUztxQkFjZixLQUFLLFNBQUMsUUFBUTt1QkFpQ2QsS0FBSyxTQUFDLFVBQVU7Ozs7SUE1RWpCLHFCQUFxQjs7Ozs7SUFDckIsc0JBQTBCOzs7OztJQUMxQix1QkFBMEI7Ozs7O0lBQzFCLHFCQUF5Qjs7Ozs7SUFDekIsdUJBQTJCOzs7OztJQUMzQix3QkFBNEI7Ozs7O0lBQzVCLHFCQUE0Qjs7Ozs7SUFtRmhCLG1CQUEwQjs7Ozs7SUFBRSw0QkFBdUM7Ozs7Ozs7O0FBb0luRixNQUFNLE9BQU8sY0FBZSxTQUFRLEdBQUc7Ozs7Ozs7SUErQnRDLFlBQXNCLElBQWdCLEVBQWEsYUFBNEIsRUFBWSxFQUFxQjtRQUM1RyxLQUFLLENBQUMsSUFBSSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1FBRFQsU0FBSSxHQUFKLElBQUksQ0FBWTtRQUFhLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBQVksT0FBRSxHQUFGLEVBQUUsQ0FBbUI7SUFFaEgsQ0FBQzs7Ozs7O0lBOUJELElBQ0ksTUFBTSxDQUFDLEtBQVU7UUFDakIsSUFBRyxLQUFLLEVBQUM7WUFDTCxJQUFHLElBQUksQ0FBQyxNQUFNLEVBQUM7O29CQUNQLFdBQVcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFNBQVM7Ozs7Z0JBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsTUFBTSxJQUFJLElBQUksRUFBRTtnQkFDOUUsSUFBRyxXQUFXLElBQUksQ0FBQyxFQUFFO29CQUNqQixJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFdBQVcsQ0FBQyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7b0JBQ3pELElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxDQUFDLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztpQkFDN0Q7YUFDSjtZQUNELElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO1NBQ3ZCO1FBQ0QsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsbUJBQW1CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDL0MsSUFBRyxJQUFJLENBQUMsT0FBTyxFQUFDO1lBQ1osSUFBSSxDQUFDLENBQUMsbUJBQUEsSUFBSSxDQUFDLEVBQUUsRUFBTyxDQUFDLENBQUMsU0FBUyxFQUFFO2dCQUM3QixJQUFJLENBQUMsRUFBRSxDQUFDLFlBQVksRUFBRSxDQUFDO2FBQzFCO1NBRUo7SUFDTCxDQUFDOzs7O0lBQ0QsSUFBSSxNQUFNO1FBQ1AsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO0lBQ3hCLENBQUM7Ozs7O0lBYUEsUUFBUTtRQUNKLElBQUc7WUFDQyxJQUFHLElBQUksQ0FBQyxFQUFFLElBQUssU0FBUyxFQUFFO2dCQUN0QixNQUFNLElBQUksS0FBSyxDQUFDLCtKQUErSixDQUFDLENBQUM7YUFDcEw7aUJBQUssSUFBRyxJQUFJLENBQUMsTUFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTs7O29CQUV6RCxhQUFhLEdBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTOzs7O2dCQUFDLENBQUMsQ0FBQSxFQUFFLENBQUEsQ0FBQyxDQUFDLE1BQU0sSUFBSSxJQUFJLEVBQUM7Z0JBQzlFLElBQUcsYUFBYSxJQUFJLENBQUMsQ0FBQyxFQUFDO29CQUNuQix5QkFBeUI7b0JBQ3pCLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxHQUFFLElBQUksQ0FBQztvQkFDN0MsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLEdBQUUsSUFBSSxDQUFDO2lCQUNqRDtxQkFBSyxJQUFJLElBQUksQ0FBQyxNQUFNLElBQUksSUFBSSxFQUFFO29CQUMzQixpQ0FBaUM7b0JBQ2pDLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsT0FBTzs7OztvQkFBQyxVQUFVLEdBQVE7d0JBQ25ELElBQUcsR0FBRyxJQUFJLElBQUksRUFBRTs0QkFDWixHQUFHLENBQUMsTUFBTSxHQUFFLEtBQUssQ0FBQzs0QkFDbEIsR0FBRyxDQUFDLE9BQU8sR0FBRSxLQUFLLENBQUM7eUJBQ3RCO29CQUNMLENBQUMsRUFBQyxDQUFDO29CQUNILElBQUksQ0FBQyxNQUFNLEdBQUUsSUFBSSxDQUFDO29CQUNsQixJQUFJLENBQUMsT0FBTyxHQUFFLElBQUksQ0FBQztpQkFDdEI7cUJBQUk7b0JBQ0QsSUFBSSxDQUFDLE1BQU0sR0FBQyxLQUFLLENBQUM7b0JBQ2xCLElBQUksQ0FBQyxPQUFPLEdBQUMsS0FBSyxDQUFDO2lCQUN0QjthQUNKO2lCQUNJLElBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFDO2dCQUNqQixJQUFJLENBQUMsT0FBTyxHQUFDLEtBQUssQ0FBQzthQUN0QjtZQUNELGNBQWMsQ0FBQyxTQUFTOzs7O1lBQUMsQ0FBQyxHQUFHLEVBQUUsRUFBRTtnQkFDN0IsSUFBRyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLEVBQUM7b0JBQ3ZDLElBQUksQ0FBQyxDQUFDLG1CQUFBLElBQUksQ0FBQyxFQUFFLEVBQU8sQ0FBQyxDQUFDLFNBQVMsRUFBRTt3QkFDN0IsSUFBSSxDQUFDLEVBQUUsQ0FBQyxZQUFZLEVBQUUsQ0FBQztxQkFDMUI7aUJBQ0o7WUFDTCxDQUFDLEVBQUMsQ0FBQztTQUNOO1FBQUEsT0FBTSxLQUFLLEVBQUM7WUFDVCxPQUFPLENBQUMsS0FBSyxDQUFDLDRCQUE0QixFQUFDLEtBQUssQ0FBQyxDQUFDO1NBQ3JEO0lBQ0wsQ0FBQzs7O1lBNUdELFNBQVMsU0FBQztnQkFDUCxRQUFRLEVBQUUsb0JBQW9CO2dCQUM5QixRQUFRLEVBQUU7Ozs7O0tBS1Q7Z0JBcUJELFNBQVMsRUFBRSxDQUFDLEVBQUMsT0FBTyxFQUFFLEdBQUcsRUFBRSxXQUFXLEVBQUUsY0FBYyxFQUFDLENBQUM7Z0JBQ3hELGVBQWUsRUFBRSx1QkFBdUIsQ0FBQyxNQUFNO3lCQXJCdEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7S0FtQlI7YUFHSjs7OztZQTlRa0MsVUFBVTtZQU9wQyxhQUFhO1lBUDRSLGlCQUFpQjs7O3FCQWtSalUsS0FBSyxTQUFDLFFBQVE7Ozs7Ozs7SUE0QkgsOEJBQTBCOzs7OztJQUFFLHVDQUF1Qzs7Ozs7SUFBRSw0QkFBK0I7OztBQTBOakgsTUFBTSxPQUFPLGVBQWdCLFNBQVEsU0FBUzs7Ozs7OztJQW9MMUMsWUFBb0IsSUFBZ0IsRUFBVSxhQUE0QixFQUFXLEdBQXNCO1FBQ3ZHLEtBQUssQ0FBQyxJQUFJLEVBQUMsYUFBYSxDQUFDLENBQUM7UUFEVixTQUFJLEdBQUosSUFBSSxDQUFZO1FBQVUsa0JBQWEsR0FBYixhQUFhLENBQWU7UUFBVyxRQUFHLEdBQUgsR0FBRyxDQUFtQjs7UUFqTG5HLGtCQUFhLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLHFCQUFnQixHQUFHLEVBQUUsQ0FBQztRQUNyQixtQkFBYyxHQUFFLENBQUMsQ0FBQztRQUNsQixtQkFBYyxHQUFDLElBQUksQ0FBQztRQUNwQixpQkFBWSxHQUFDLElBQUksQ0FBQztRQUNsQixrQkFBYSxHQUFFLElBQUksQ0FBQztRQUNwQixlQUFVLEdBQUUsSUFBSSxDQUFDO1FBQ2pCLGNBQVMsR0FBSyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBRS9CLGVBQVUsR0FBSyxJQUFJLFFBQVEsRUFBRSxDQUFDOztRQUc3QixnQkFBVyxHQUFHLElBQUksQ0FBQztRQUNuQixnQkFBVyxHQUFXLENBQUMsQ0FBQztRQUN4QixnQkFBVyxHQUFXLENBQUMsQ0FBQztRQUV4QixnQkFBVyxHQUFXLENBQUMsQ0FBQztRQUN4QixjQUFTLEdBQVcsQ0FBQyxDQUFDOzs7UUFVRSxjQUFTLEdBQUssSUFBSSxZQUFZLEVBQU8sQ0FBQztRQUNyRSxrQkFBYSxHQUFZLEtBQUssQ0FBQztRQUMvQixnQkFBVyxHQUFZLEtBQUssQ0FBQztJQXNKN0IsQ0FBQzs7OztJQXBKRCxvQkFBb0I7UUFDaEIsSUFBRyxJQUFJLENBQUMsVUFBVTtZQUNkLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUk7Ozs7O1lBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQzs7WUFFOUcsT0FBTyxJQUFJLENBQUMsZ0JBQWdCLENBQUE7SUFDbEMsQ0FBQzs7OztJQUVILGNBQWM7UUFDVixPQUFPLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxhQUFhLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsYUFBYSxDQUFDLFlBQVksQ0FBQztJQUNoSCxDQUFDOzs7Ozs7SUFFRCxJQUNJLFdBQVcsQ0FBQyxLQUFLO1FBQ2pCLEtBQUssQ0FBQyxRQUFRLENBQUMsT0FBTzs7OztRQUFDLENBQUMsR0FBRyxFQUFFLEVBQUU7WUFDM0IsR0FBRyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFDdEIsQ0FBQyxFQUFDLENBQUM7UUFDSCxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsS0FBSyxDQUFDLFFBQVEsQ0FBQyxNQUFNOzs7O1FBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxJQUFJLENBQUMsRUFBRSxFQUFDLENBQUM7SUFDL0UsQ0FBQzs7Ozs7O0lBR0QsSUFBVyxXQUFXLENBQUMsR0FBUTs7WUFFckIsS0FBSyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTOzs7O1FBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsSUFBSyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksR0FBRyxDQUFDLE9BQU8sQ0FBQyxFQUFFO1FBQ3JGLElBQUcsS0FBSyxJQUFJLENBQUMsRUFBQztZQUNWLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUMsSUFBSSxDQUFDO1lBQ3pDLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSyxLQUFLLEVBQUc7Z0JBQy9CLFVBQVU7OztnQkFBQyxHQUFHLEVBQUU7b0JBQ1osSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDdEIsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO2dCQUVyQixDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7YUFDVDtZQUVELElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBQzNCLElBQUksQ0FBQyxZQUFZLEdBQUcsR0FBRyxDQUFDO1NBQzNCO2FBQUk7WUFDRCxNQUFNLElBQUksS0FBSyxDQUFDLHNGQUFzRixDQUFDLENBQUM7U0FDM0c7SUFDTCxDQUFDOzs7O0lBRUQsSUFBVyxXQUFXO1FBQ2xCLE9BQVEsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztJQUN0RCxDQUFDOzs7Ozs7SUFHRCxJQUFXLGFBQWEsQ0FBQyxHQUFROztZQUN6QixLQUFLLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFNBQVM7Ozs7UUFBQyxDQUFDLENBQUEsRUFBRSxDQUFBLENBQUMsQ0FBQyxFQUFFLElBQUssR0FBRyxDQUFDLEVBQUUsRUFBSTtRQUNsRSxJQUFHLEtBQUssSUFBSSxDQUFDLEVBQUM7WUFDVixJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFDLElBQUksQ0FBQztZQUN6QyxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQztZQUMzQixJQUFJLENBQUMsWUFBWSxHQUFHLEdBQUcsQ0FBQztTQUMzQjthQUFJO1lBQ0QsTUFBTSxJQUFJLEtBQUssQ0FBQyxzRkFBc0YsQ0FBQyxDQUFDO1NBQy9HO0lBQ0QsQ0FBQzs7OztJQUVELElBQVcsYUFBYTtRQUNwQixPQUFRLElBQUksQ0FBQyxZQUFZLENBQUM7SUFDOUIsQ0FBQzs7Ozs7O0lBRUQsSUFBSSxhQUFhLENBQUMsS0FBYTs7WUFDdkIsS0FBSyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTOzs7O1FBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsS0FBSyxJQUFJLEtBQUssRUFBRTtRQUNqRSxJQUFHLEtBQUssSUFBSSxDQUFDLEVBQUM7WUFDVixJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFDLElBQUksQ0FBQztZQUN6QyxJQUFJLENBQUMsY0FBYyxHQUFHLEtBQUssQ0FBQztTQUMvQjthQUFJO1lBQ0QsTUFBTSxJQUFJLEtBQUssQ0FBQyxrRkFBa0YsQ0FBQyxDQUFDO1NBQ3ZHO0lBQ0wsQ0FBQzs7OztJQUVELElBQUksYUFBYTtRQUNiLElBQUcsSUFBSSxDQUFDLFdBQVcsSUFBSSxJQUFJLEVBQUM7WUFDeEIsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQztTQUNqQztRQUNELE9BQU8sU0FBUyxDQUFDO0lBQ3JCLENBQUM7Ozs7O0lBR0QsSUFBSSxRQUFRO1FBQ1IsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO0lBQzFCLENBQUM7Ozs7O0lBQ0QsSUFBSSxRQUFRLENBQUUsS0FBZTtRQUN6QixJQUFJLENBQUMsU0FBUyxHQUFFLEtBQUssQ0FBQztJQUMxQixDQUFDOzs7Ozs7SUFHRCxJQUFXLGFBQWEsQ0FBRSxLQUFhO1FBQ25DLElBQUksQ0FBQyxjQUFjLEdBQUcsS0FBSyxDQUFDO1FBQzVCLElBQUcsSUFBSSxDQUFDLGdCQUFnQixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsRUFBQztZQUVyRCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFFLElBQUksQ0FBQztZQUMxQyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNqRCxJQUFJLENBQUMsY0FBYyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLENBQUM7WUFDekQsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDO1NBQ3hEO0lBRUwsQ0FBQzs7OztJQUNELElBQVcsYUFBYTtRQUNwQixPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7SUFDL0IsQ0FBQzs7Ozs7O0lBR0QsSUFDSSxZQUFZLENBQUMsS0FBVztRQUN4QixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNyRCxJQUFHLENBQUMsSUFBSSxDQUFDLGFBQWE7WUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsZUFBZSxFQUFDLENBQUMsQ0FBQyxDQUFDO0lBRWhILENBQUM7Ozs7SUFDRCxJQUFJLFlBQVk7UUFDWixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUM7SUFDOUIsQ0FBQzs7Ozs7O0lBRUQsSUFDSSxZQUFZLENBQUMsS0FBVztRQUN4QixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUN6RCxDQUFDOzs7O0lBQ0QsSUFBSSxZQUFZO1FBQ1osT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO0lBQzlCLENBQUM7Ozs7OztJQUdBLElBQ0ksVUFBVSxDQUFDLEtBQVc7UUFDdEIsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsbUJBQW1CLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDdkQsQ0FBQzs7OztJQUNELElBQUksVUFBVTtRQUNWLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztJQUM1QixDQUFDOzs7Ozs7SUFHRixJQUNJLFNBQVMsQ0FBQyxLQUFXO1FBQ3JCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2xELElBQUcsQ0FBQyxJQUFJLENBQUMsVUFBVTtZQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUMsQ0FBQyxDQUFDLENBQUM7SUFFMUcsQ0FBQzs7OztJQUNELElBQUksU0FBUztRQUNULE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztJQUMzQixDQUFDOzs7Ozs7Ozs7OztJQXVCRCxRQUFRO1FBQ0osS0FBSyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2pCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRSxFQUFFLENBQUM7UUFDMUIsSUFBRyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQUUsSUFBSSxDQUFDLEVBQUUsR0FBSSxVQUFVLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFFO1FBRTVFLElBQUksQ0FBQyxlQUFlLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDcEYsSUFBSSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUV6RCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLENBQUMsS0FBSzs7O1FBQUMsR0FBRSxFQUFFO1lBQ3ZILENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQzs7a0JBQzdHLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQztZQUMxSSxVQUFVOzs7WUFBQyxHQUFHLEVBQUU7Z0JBQ1osSUFBRyxTQUFTLEVBQUM7b0JBQ1QsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxvQkFBb0IsR0FBQyxJQUFJLENBQUMsRUFBRTs7OztvQkFBRSxDQUFDLENBQUMsRUFBRyxFQUFFO3dCQUM5QyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLG9CQUFvQixHQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQzt3QkFDNUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztvQkFDMUgsQ0FBQyxFQUFDLENBQUM7aUJBQ047cUJBQUs7b0JBQ0YsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztpQkFDMUg7WUFFTCxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7UUFDWixDQUFDLEVBQUMsQ0FBQTtJQUNSLENBQUM7Ozs7O0lBS0QsZUFBZTtRQUNYLElBQUc7O2dCQUNLLFVBQVUsR0FBRyxXQUFXOzs7WUFBQyxHQUFHLEVBQUU7Z0JBQzlCLElBQUcsSUFBSSxDQUFDLGdCQUFnQixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFDO29CQUN6RCxhQUFhLENBQUMsVUFBVSxDQUFDLENBQUM7O3dCQUN2QixhQUFhLEdBQUUsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFNBQVM7Ozs7b0JBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsQ0FBQyxNQUFNLElBQUksSUFBSSxJQUFJLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBQztvQkFDckYsSUFBRyxhQUFhLElBQUksQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7OzRCQUNyRCxHQUFHLEdBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU07Ozs7d0JBQUMsQ0FBQyxDQUFBLEVBQUUsQ0FBQSxDQUFDLENBQUMsU0FBUyxDQUFDLElBQUcsSUFBSSxFQUFDO3dCQUM1RCxHQUFHLENBQUMsT0FBTzs7Ozt3QkFBQyxVQUFVLEdBQVE7NEJBQ3pCLEdBQUcsQ0FBQyxNQUFNLEdBQUUsS0FBSyxDQUFDOzRCQUNsQixHQUFHLENBQUMsT0FBTyxHQUFFLEtBQUssQ0FBQzt3QkFFdkIsQ0FBQyxFQUFDLENBQUM7d0JBQ0osSUFBRyxHQUFHLElBQUksR0FBRyxDQUFDLE1BQU0sR0FBQyxDQUFDOzRCQUFFLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO3FCQUMvQztpQkFDSjtZQUNMLENBQUMsR0FBRSxJQUFJLENBQUM7WUFFVCxJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3BGLElBQUksQ0FBQyxPQUFPLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDekQsSUFBSSxDQUFDLFNBQVMsR0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUUxQyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBQ3ZFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDL0QsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsR0FBRyxDQUFDLGFBQWEsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUVyRSxJQUFJLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBRTFELElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQztTQUVwQjtRQUFBLE9BQU0sS0FBSyxFQUFDO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQ0FBbUMsRUFBQyxLQUFLLENBQUMsQ0FBQztTQUM1RDtJQUNMLENBQUM7Ozs7OztJQVFNLGNBQWMsQ0FBQyxHQUFHO1FBQ3JCLElBQUc7WUFDRCxJQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRSxFQUFDO2dCQUNsQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsT0FBTzs7OztnQkFBQyxVQUFVLE1BQVc7b0JBQy9DLElBQUcsR0FBRyxJQUFJLE1BQU0sRUFBQzt3QkFDYixNQUFNLENBQUMsTUFBTSxHQUFFLEtBQUssQ0FBQzt3QkFDckIsTUFBTSxDQUFDLE9BQU8sR0FBRSxLQUFLLENBQUM7cUJBQ3pCO3lCQUFJO3dCQUNELEdBQUcsQ0FBQyxNQUFNLEdBQUMsSUFBSSxDQUFDO3dCQUNoQixHQUFHLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQztxQkFDcEI7Z0JBQ0wsQ0FBQyxFQUFDLENBQUM7Z0JBRUgsY0FBYyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQzs7b0JBQ3JCLEtBQUssR0FBRSxJQUFJLENBQUMsZ0JBQWdCLENBQUMsU0FBUzs7OztnQkFBQyxDQUFDLENBQUEsRUFBRSxDQUFBLENBQUMsQ0FBQyxFQUFFLElBQUksR0FBRyxDQUFDLEVBQUUsRUFBQztnQkFFN0QsSUFBSSxJQUFJLENBQUMsYUFBYSxJQUFLLEtBQUssRUFBRztvQkFDL0IsSUFBSSxDQUFDLGNBQWMsR0FBSSxLQUFLLENBQUM7b0JBQzdCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxLQUFLLENBQUM7b0JBQ3RFLElBQUksQ0FBQyxZQUFZLEdBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztvQkFDNUQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDdEIsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO2lCQUNwQjtxQkFBSTtvQkFDRCxJQUFJLENBQUMsY0FBYyxHQUFJLEtBQUssQ0FBQztvQkFDN0IsSUFBSSxDQUFDLFlBQVksR0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO29CQUM1RCxJQUFJLENBQUMsY0FBYyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsS0FBSyxDQUFDO2lCQUN6RTthQUNKO1NBRUo7UUFBQSxPQUFNLEtBQUssRUFBQztZQUNULE9BQU8sQ0FBQyxLQUFLLENBQUMsaUNBQWlDLEVBQUMsS0FBSyxDQUFDLENBQUM7U0FDMUQ7SUFDTCxDQUFDOzs7O0lBRU0sY0FBYztRQUNqQixJQUFHO1lBQ0MsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO2dCQUNsQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7YUFDM0I7WUFDRCxPQUFPLElBQUksQ0FBQztTQUNmO1FBQUEsT0FBTSxLQUFLLEVBQUM7WUFDVCxPQUFPLENBQUMsS0FBSyxDQUFDLGlDQUFpQyxFQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3pEO0lBQ0wsQ0FBQzs7Ozs7O0lBTU0sUUFBUSxDQUFDLElBQUk7UUFFaEIsSUFBRzs7Z0JBQ0ssSUFBSSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsd0JBQXdCLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDOztnQkFDaEYsR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQzs7Z0JBRTFDLGlCQUFpQixHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQztZQUM5RCxrRkFBa0Y7WUFDbEYsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQzs7Z0JBQ2xDLEVBQUUsR0FBRSxVQUFVLEdBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUM1RCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFDLEVBQUUsQ0FBQyxDQUFDO1lBRTNFLEdBQUcsR0FBRyxtQkFBTSxpQkFBaUIsQ0FBQyxRQUFRLEVBQUEsQ0FBQztZQUN2QyxHQUFHLENBQUMsRUFBRSxHQUFFLEVBQUUsQ0FBQztZQUNYLEdBQUcsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1lBQ2xCLEdBQUcsQ0FBQyxPQUFPLEdBQUMsS0FBSyxDQUFDO1lBRWpCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDakMsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQ2xCLE9BQU8sQ0FBRSxHQUFHLENBQUMsQ0FBQztTQUNqQjtRQUFBLE9BQU0sS0FBSyxFQUFDO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQywyQkFBMkIsRUFBQyxLQUFLLENBQUMsQ0FBQTtTQUNuRDtJQUNMLENBQUM7Ozs7OztJQUtNLG1CQUFtQixDQUFDLElBQUk7UUFFM0IsSUFBRzs7Z0JBQ0ssSUFBSSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsd0JBQXdCLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDOztnQkFDaEYsR0FBRyxHQUFHLElBQUksY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLGFBQWEsRUFBQyxJQUFJLENBQUMsR0FBRyxDQUFDOztnQkFFOUQsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDO1lBQzlELGtGQUFrRjtZQUNsRixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDOztnQkFDcEMsRUFBRSxHQUFFLFVBQVUsR0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQzFELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUMsRUFBRSxDQUFDLENBQUM7WUFFM0UsR0FBRyxHQUFHLG1CQUFpQixpQkFBaUIsQ0FBQyxRQUFRLEVBQUEsQ0FBQztZQUNsRCxHQUFHLENBQUMsRUFBRSxHQUFFLEVBQUUsQ0FBQztZQUNYLEdBQUcsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1lBQ2xCLEdBQUcsQ0FBQyxPQUFPLEdBQUMsS0FBSyxDQUFDO1lBQ2pCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDaEMsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQ25CLE9BQU8sQ0FBRSxHQUFHLENBQUMsQ0FBQztTQUNqQjtRQUFBLE9BQU0sS0FBSyxFQUFDO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQywyQkFBMkIsRUFBQyxLQUFLLENBQUMsQ0FBQTtTQUNuRDtJQUNMLENBQUM7Ozs7OztJQU1NLFdBQVcsQ0FBQyxjQUFjO1FBQzdCLElBQUc7O2dCQUNLLEtBQUssR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsU0FBUzs7OztZQUFDLENBQUMsQ0FBQSxFQUFFLENBQUEsQ0FBQyxJQUFJLGNBQWMsRUFBRTtZQUNwRSxxR0FBcUc7WUFDckcsSUFBSSxLQUFLLEtBQUssQ0FBQyxDQUFDLEVBQUM7Z0JBQ2IsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sR0FBRSxLQUFLLENBQUM7Z0JBQzVDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUUsS0FBSyxDQUFDO2dCQUMvQyxrQkFBa0I7Z0JBQ2xCLGtFQUFrRTtnQkFDOUQsS0FBSyxDQUFDLFdBQVcsQ0FBQyxjQUFjLENBQUMsQ0FBQztnQkFFcEMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3JDLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQzthQUNwQztZQUVELHdEQUF3RDtTQUUzRDtRQUFBLE9BQU0sS0FBSyxFQUFDO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsRUFBQyxLQUFLLENBQUMsQ0FBQztTQUN2RDtJQUNMLENBQUM7Ozs7OztJQU9NLFVBQVUsQ0FBQyxLQUFZO1FBQzFCLElBQUc7WUFDQyxJQUFHLEtBQUssR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxFQUFFO2dCQUNyQyxPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN2QztZQUNELE9BQU8sSUFBSSxDQUFDO1NBQ2Y7UUFBQSxPQUFNLEtBQUssRUFBQztZQUNULE9BQU8sQ0FBQyxLQUFLLENBQUMsNkJBQTZCLEVBQUMsS0FBSyxDQUFDLENBQUM7U0FDdEQ7SUFDTCxDQUFDOzs7OztJQUtNLGNBQWM7UUFDakIsT0FBTyxJQUFJLENBQUMsZ0JBQWdCLENBQUM7SUFDakMsQ0FBQzs7Ozs7SUFLRCxXQUFXO1FBQ1AsSUFBRztZQUNDLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQztZQUMzQixPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7WUFDM0IsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ3pCLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztZQUMxQixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUM7WUFDMUIsT0FBTyxJQUFJLENBQUMsVUFBVSxDQUFDO1lBQ3ZCLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO1lBQzdCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztTQUN6QjtRQUFBLE9BQU0sS0FBSyxFQUFFO1lBQ1YsT0FBTyxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsRUFBQyxLQUFLLENBQUMsQ0FBQTtTQUN0RDtJQUNMLENBQUM7Ozs7Ozs7O0lBV08scUJBQXFCLENBQUMsS0FBYSxFQUFFLEtBQWE7O1lBRWpELEdBQUcsR0FBRyxJQUFJLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxhQUFhLENBQUM7UUFDL0MsR0FBRyxDQUFDLGVBQWUsQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7SUFFdkMsQ0FBQzs7Ozs7OztJQUdPLFdBQVcsQ0FBQyxHQUFHLEVBQUUsS0FBSztRQUN6QixJQUFHO1lBQ0ssS0FBSyxDQUFDLGFBQWEsQ0FBQyxHQUFDLElBQUksQ0FBQyxXQUFXLENBQUM7WUFDdEMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxHQUFDLEdBQUcsQ0FBQztZQUVyQixJQUFHLEdBQUcsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksSUFBRyxXQUFXLEVBQUU7O29CQUV0QyxNQUFNLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUM7Z0JBQ25DLElBQUksTUFBTTtvQkFDTixJQUFJLENBQUMsY0FBYyxDQUFDLEdBQUcsQ0FBQyxDQUFDO2FBQ2hDO2lCQUFJO2dCQUNELElBQUksQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDNUI7WUFDRCxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUM7U0FFekI7UUFBQSxPQUFNLEtBQUssRUFBQztZQUNULE9BQU8sQ0FBQyxLQUFLLENBQUMsZ0NBQWdDLEVBQUMsS0FBSyxDQUFDLENBQUM7U0FDekQ7SUFFTCxDQUFDOzs7Ozs7SUFLQyxVQUFVLENBQUMsSUFBWTtRQUNyQixJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDZixJQUFJLENBQUMsS0FBSyxHQUFHLFdBQVc7OztRQUFDLEdBQUcsRUFBRTtZQUM1QixJQUFJLElBQUksS0FBSyxPQUFPLEVBQUU7Z0JBQ3BCLElBQUksSUFBSSxDQUFDLFdBQVcsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsRUFBRTtvQkFDMUMsSUFBSSxDQUFDLFdBQVcsSUFBSSxFQUFFLENBQUM7aUJBQ3hCO3FCQUFNO29CQUNMLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQztpQkFDL0I7YUFFRjtpQkFBTSxJQUFJLElBQUksS0FBSyxNQUFNLEVBQUU7Z0JBQzFCLElBQUksSUFBSSxDQUFDLFdBQVcsR0FBRyxFQUFFLEdBQUcsQ0FBQyxFQUFFO29CQUM3QixJQUFJLENBQUMsV0FBVyxJQUFJLEVBQUUsQ0FBQztpQkFDeEI7cUJBQU07b0JBQ0wsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUM7aUJBQ3RCO2FBQ0Y7WUFDRCxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7WUFFbkQsSUFBSSxJQUFJLENBQUMsV0FBVyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7Z0JBQ3BDLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7Z0JBQ3hFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7Z0JBQ2hFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQ3JFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7Z0JBQ3hFLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQzthQUNqQztZQUNjLElBQUksSUFBSSxDQUFDLFdBQVcsSUFBSSxDQUFDLEVBQUU7Z0JBQ3ZCLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7Z0JBQ3ZFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQy9ELENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQ3JFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7Z0JBQ3pFLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQzthQUNsQjtZQUNELElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxJQUFJLENBQUMsU0FBUyxFQUFFO2dCQUMvRCxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLFNBQVMsQ0FBQyxDQUFDO2dCQUN4RSxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dCQUNoRSxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUNyRSxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLFNBQVMsQ0FBQyxDQUFDO2FBQzVFO1lBRUQsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBRXhCLENBQUMsR0FBRSxFQUFFLENBQUMsQ0FBQztJQUNULENBQUM7Ozs7SUFFRCxPQUFPO1FBQ0wsYUFBYSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUM1QixDQUFDOzs7OztJQUdELGdCQUFnQixDQUFDLEdBQVE7UUFDeEIsSUFBRyxHQUFHLENBQUMsT0FBTyxFQUFDOztrQkFDRixLQUFLLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUM7WUFDaEQsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUN0QixJQUFHLElBQUksQ0FBQyxhQUFhLElBQUksS0FBSyxFQUFDO2dCQUMzQixJQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO29CQUNqQyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssR0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO3dCQUNqRixJQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPOzRCQUMvQixNQUFNO3FCQUNiO29CQUNELElBQUksQ0FBQyxXQUFXLEdBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBRSxDQUFDO29CQUMzQyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUUsRUFBRSxLQUFLLENBQUMsQ0FBQztvQkFDbkQsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFDO2lCQUMxQjthQUNKO1lBQ0wsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQ2xCLGFBQWEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1NBQ2xDO0lBQ0csQ0FBQzs7Ozs7SUFFRCxvQkFBb0IsQ0FBQyxHQUFHOztjQUNoQixVQUFVLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQzs7Y0FDckYsV0FBVyxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUM7O2NBQ3ZGLFdBQVcsR0FBRyxDQUFDLFVBQVUsR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEdBQUcsV0FBVztRQUN6RSxJQUFJLENBQUMsV0FBVyxHQUFHLFdBQVcsQ0FBQztRQUUvQixJQUFJLENBQUMsV0FBVyxHQUFDLEdBQUcsQ0FBQztRQUVyQixJQUFJLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRTtZQUN2QyxJQUFJLENBQUMsS0FBSyxHQUFHLFdBQVc7OztZQUFDLEdBQUcsRUFBRTtnQkFDNUIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO2dCQUNwRCxJQUFJLFVBQVUsR0FBSSxJQUFJLENBQUMsV0FBVyxFQUFJO29CQUNsQyxJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQztvQkFDckIsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsQ0FBQztvQkFDdkUsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxLQUFLLENBQUMsQ0FBQztvQkFDL0QsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsR0FBRyxDQUFDLGFBQWEsRUFBRSxLQUFLLENBQUMsQ0FBQztvQkFDckUsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSxTQUFTLENBQUMsQ0FBQztvQkFDekUsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO2lCQUNsQjtnQkFDRCxJQUFJLENBQUMsVUFBVSxHQUFHLFdBQVcsQ0FBQyxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7b0JBQ2hELENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQ3hFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7b0JBQ2hFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7b0JBQ3JFLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7b0JBQ3hFLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQztpQkFDbEI7Z0JBQ0QsSUFBSSxVQUFVLEdBQUcsRUFBRSxJQUFJLENBQUMsVUFBVSxHQUFHLFdBQVcsQ0FBQyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUU7b0JBQ2xFLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQ2hFLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7b0JBQ3hELENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7b0JBQzdELENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQ2pFLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQztpQkFDbEI7WUFDSCxDQUFDLEdBQUUsRUFBRSxDQUFDLENBQUM7U0FDUjtRQUNELENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDO0lBRWhGLENBQUM7Ozs7SUFFRCxVQUFVO1FBQ1AsVUFBVTs7O1FBQUMsR0FBRyxFQUFFO1lBQ1gsSUFBSSxDQUFDLGVBQWUsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNwRixJQUFJLENBQUMsT0FBTyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQ3pELElBQUksQ0FBQyxTQUFTLEdBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFMUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLFNBQVMsRUFBRTs7b0JBQzdCLFdBQVcsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUM7Z0JBQ3hELElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxJQUFJLFNBQVMsRUFBRTtvQkFDbEUsSUFBSSxDQUFDLFdBQVcsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQzt3QkFDN0YsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsQ0FBQztpQkFDbkY7Z0JBQ0QsSUFBSSxDQUFDLFNBQVMsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsQ0FBQzthQUNwRztRQUNOLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztJQUNULENBQUM7OztZQTN2QlAsU0FBUyxTQUFDO2dCQUNQLFFBQVEsRUFBRSxpQkFBaUI7Z0JBQzNCLFFBQVEsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0E0Q1g7eUJBQ1U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQXFIVjthQUNGOzs7O1lBdmdCa0MsVUFBVTtZQU9wQyxhQUFhO1lBUDRSLGlCQUFpQjs7O2lDQWtpQjlULFNBQVMsU0FBQyxvQkFBb0I7OEJBQzlCLFNBQVMsU0FBQyxpQkFBaUI7d0JBRzNCLE1BQU0sU0FBQyxVQUFVOzBCQWVqQixlQUFlLFNBQUMsR0FBRzsyQkEyRm5CLEtBQUssU0FBQyxjQUFjOzJCQVVwQixLQUFLLFNBQUMsY0FBYzt5QkFTbkIsS0FBSyxTQUFDLFlBQVk7d0JBU25CLEtBQUssU0FBQyxXQUFXOzs7Ozs7O0lBaktsQix3Q0FBMEI7O0lBQzFCLDJDQUE2Qjs7Ozs7SUFDN0IseUNBQTBCOzs7OztJQUMxQix5Q0FBNEI7Ozs7O0lBQzVCLHVDQUEwQjs7Ozs7SUFDMUIsd0NBQTRCOzs7OztJQUM1QixxQ0FBeUI7Ozs7O0lBQ3pCLG9DQUFxQzs7SUFFdkMscUNBQXNDOztJQUdwQyxzQ0FBMEI7O0lBQzFCLHNDQUErQjs7SUFDL0Isc0NBQStCOztJQUMvQixnQ0FBa0I7O0lBQ2xCLHNDQUErQjs7SUFDL0Isb0NBQTZCOztJQUU3QiwwQ0FBd0I7O0lBQ3hCLGtDQUFnQjs7SUFDaEIsb0NBQWlCOztJQUVqQiw2Q0FBZ0U7O0lBQ2hFLDBDQUEwRDs7Ozs7SUFHMUQsb0NBQXFFOztJQUNyRSx3Q0FBK0I7O0lBQy9CLHNDQUE2Qjs7Ozs7SUFvSmpCLCtCQUF3Qjs7Ozs7SUFBRSx3Q0FBb0M7Ozs7O0lBQUcsOEJBQThCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFiQ2xvc2VFdmVudCB9IGZyb20gJy4vLi4vZXZlbnRzL3N3dC1ldmVudHMubW9kdWxlJztcclxuaW1wb3J0IHsgQ29tcG9uZW50LCBPbkluaXQsIElucHV0LCBFbGVtZW50UmVmLCBBZnRlclZpZXdJbml0LCBWaWV3Q2hpbGQsIFZpZXdDb250YWluZXJSZWYsIENvbXBvbmVudEZhY3RvcnlSZXNvbHZlciwgRGlyZWN0aXZlLCBSZW5kZXJlcjIsIE9uQ2hhbmdlcywgT3V0cHV0LCBFdmVudEVtaXR0ZXIsIENvbnRlbnRDaGlsZHJlbiwgVmlld0NoaWxkcmVuLCBRdWVyeUxpc3QsIEFmdGVyQ29udGVudENoZWNrZWQsIEFmdGVyQ29udGVudEluaXQsIEFmdGVyVmlld0NoZWNrZWQsIFNpbXBsZUNoYW5nZXMsIERvQ2hlY2ssIE9uRGVzdHJveSwgQ2hhbmdlRGV0ZWN0b3JSZWYsIENoYW5nZURldGVjdGlvblN0cmF0ZWd5IH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcblxyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbi8vaW1wb3J0ICQgZnJvbSAnanF1ZXJ5JztcclxuY29uc3QgJCA9IHJlcXVpcmUoJ2pxdWVyeScpO1xyXG5cclxuaW1wb3J0IHsgQ29udGFpbmVyIH0gZnJvbSBcIi4vc3d0LWNvbnRhaW5lci5jb21wb25lbnRcIjtcclxuaW1wb3J0IHsgQ29tbW9uU2VydmljZSB9IGZyb20gXCIuLi91dGlscy9jb21tb24uc2VydmljZVwiO1xyXG5pbXBvcnQgeyBUYWJTZWxlY3RFdmVudCB9IGZyb20gXCIuLi9ldmVudHMvc3d0LWV2ZW50cy5tb2R1bGVcIjtcclxuaW1wb3J0IHsgbG9nIH0gZnJvbSAndXRpbCc7XHJcblxyXG5cclxuLyoqXHJcbiAqXHJcbiAqIFRhYiBjb21wb25lbnQuXHJcbiAqICoqL1xyXG5cclxuLy9AZHluYW1pY1xyXG5AQ29tcG9uZW50KHtcclxuICAgIHNlbGVjdG9yOiAnU3d0VGFiJyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICAgICAgPGRpdiAgY2xhc3M9XCJzd3R0YWJcIiA+XHJcbiAgICAgICAgICAgIDxuZy1jb250ZW50PjwvbmctY29udGVudD5cclxuICAgICAgICAgICAgIDxkaXYgICNfY29udGFpbmVyID48L2Rpdj5cclxuICAgICAgICA8L2Rpdj4gXHJcbiAgICBgLFxyXG4gICAgc3R5bGVzOiBbYFxyXG4gICAgICAgICA6aG9zdCB7XHJcbiAgICAgICAgICAgICAgbWFyZ2luOiAgMHB4O1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgICBkaXNwbGF5OmJsb2NrO1xyXG4gICAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgIH1cclxuICAgICAgICAuc3d0dGFiIHtcclxuICAgICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogLW1vei1mbGV4O1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IC1vLWZsZXg7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1mbGV4O1xyXG4gICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgYF0sXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBUYWIgZXh0ZW5kcyBDb250YWluZXIgaW1wbGVtZW50cyBPbkluaXQgLCBPbkRlc3Ryb3l7XHJcblxyXG4gICAgLy8tLS1Qcm9wZXJ0aWVzIGRlZmluaXRpb25zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBwdWJsaWMgcGFyZW50ID0gbnVsbDtcclxuICAgIHByb3RlY3RlZCBfYWN0aXZlID0gZmFsc2U7XHJcbiAgICBwcm90ZWN0ZWQgX3Zpc2libGUgPSB0cnVlO1xyXG4gICAgcHJvdGVjdGVkIF9sYWJlbDogc3RyaW5nO1xyXG4gICAgcHJvdGVjdGVkIF9kaXNwbGF5ID0gZmFsc2U7XHJcbiAgICBwcm90ZWN0ZWQgX2Nsb3NhYmxlID0gZmFsc2U7XHJcbiAgICBwcm90ZWN0ZWQgX29yZGVyOm51bWJlciA9IDA7XHJcblxyXG4gICAgLy8tLS1sYWJlbC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIC8vXHJcbiAgICBASW5wdXQoXCJsYWJlbFwiKVxyXG4gICAgc2V0IGxhYmVsKHZhbHVlOiBhbnkpIHtcclxuICAgICAgICB0aGlzLl9sYWJlbCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIGdldCBsYWJlbCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fbGFiZWw7XHJcbiAgICB9XHJcblxyXG4gICAgIC8vLS0tbGFiZWwtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xyXG4gICAgIEBJbnB1dChcIm9yZGVyXCIpXHJcbiAgICAgc2V0IG9yZGVyKHZhbHVlOiBhbnkpIHtcclxuICAgICAgICAgdGhpcy5fb3JkZXIgPSB2YWx1ZTtcclxuICAgICB9XHJcbiBcclxuICAgICBnZXQgb3JkZXIoKSB7XHJcbiAgICAgICAgIHJldHVybiB0aGlzLl9vcmRlcjtcclxuICAgICB9XHJcblxyXG4gICAgLy8tLS12aXNpYmxlLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gLy9cclxuICAgIEBJbnB1dCgndmlzaWJsZScpXHJcbiAgICBzZXQgdmlzaWJsZSh2YWx1ZTogYW55KSB7XHJcbiAgICAgICAgdGhpcy5fdmlzaWJsZSA9IHRoaXMuYWRhcHRWYWx1ZUFzQm9vbGVhbih2YWx1ZSk7XHJcbiAgICAgICAgaWYoIXRoaXMuX3Zpc2libGUpe1xyXG4gICAgICAgICAgICB0aGlzLmRpc3BsYXk9dGhpcy5fdmlzaWJsZTtcclxuICAgICAgICB9ZWxzZSBpZih0aGlzLmFjdGl2ZSl7XHJcbiAgICAgICAgICAgIHRoaXMuZGlzcGxheT10cnVlO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCB2aXNpYmxlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl92aXNpYmxlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tYWN0aXZlLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gLy9cclxuICAgIEBJbnB1dCgnYWN0aXZlJylcclxuICAgIHNldCBhY3RpdmUodmFsdWU6IGFueSkge1xyXG4gICAgICAgIGlmKHZhbHVlKXtcclxuICAgICAgICAgICAgaWYodGhpcy5wYXJlbnQpe1xyXG4gICAgICAgICAgICAgICAgdmFyIGluZGV4QWN0aXZlID0gdGhpcy5wYXJlbnQudGFiQ2hpbGRyZW5BcnJheS5maW5kSW5kZXgoeD0+eC5hY3RpdmUgPT0gdHJ1ZSApO1xyXG4gICAgICAgICAgICAgICAgaWYoaW5kZXhBY3RpdmUgPj0gMCApe1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhBY3RpdmVdLmFjdGl2ZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhBY3RpdmVdLmRpc3BsYXkgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aGlzLmRpc3BsYXkgPSB0cnVlO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLl9hY3RpdmUgPSB0aGlzLmFkYXB0VmFsdWVBc0Jvb2xlYW4odmFsdWUpO1xyXG4gICAgfVxyXG4gICAgZ2V0IGFjdGl2ZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fYWN0aXZlO1xyXG4gICAgfVxyXG5cclxuXHJcbiAgICAvLy0tLWRpc3BsYXktLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xyXG4gICAgc2V0IGRpc3BsYXkodmFsdWU6IGFueSkge1xyXG4gICAgICAgIHRoaXMuX2Rpc3BsYXk9IHRoaXMuYWRhcHRWYWx1ZUFzQm9vbGVhbih2YWx1ZSk7XHJcbiAgICAgICAgaWYodGhpcy5fZGlzcGxheSl7XHJcbiAgICAgICAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLnNob3coKTtcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuaGlkZSgpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCBkaXNwbGF5KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9kaXNwbGF5O1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tQ2xvc2FibGUtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xyXG4gICAgQElucHV0KFwiY2xvc2FibGVcIikgXHJcbiAgICBzZXQgY2xvc2FibGUodmFsdWU6IGFueSkge1xyXG4gICAgICAgIHRoaXMuX2Nsb3NhYmxlID0gdGhpcy5hZGFwdFZhbHVlQXNCb29sZWFuKHZhbHVlKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgZ2V0IGNsb3NhYmxlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9jbG9zYWJsZTtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogQ29uc3RydWN0b3JcclxuICAgICAqIEBwYXJhbSBlbGVtXHJcbiAgICAgKiBAcGFyYW0gY29tbW9uU2VydmljZVxyXG4gICAgICovXHJcbiAgICBjb25zdHJ1Y3Rvcihwcm90ZWN0ZWQgZWxlbTogRWxlbWVudFJlZiwgcHJvdGVjdGVkICBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlKSB7XHJcbiAgICAgICAgc3VwZXIoZWxlbSwgY29tbW9uU2VydmljZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBuZ09uSW5pdFxyXG4gICAgICovXHJcbiAgICBuZ09uSW5pdCgpIHtcclxuICAgICAgICB0cnl7XHJcbiAgICAgICAgICAgIGlmKHRoaXMuaWQgID09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiPj4+IFBST0dSQU1NSU5HIEVycm9yIFttYW5kb3Ryb3kgcGFyYW1ldGVyXSA6IHlvdSBtaWdodCBmb3Jnb3QgdG8gYWRkIGFuIGlkIHRoZSB0aGUgVGFiIGNyZWF0ZWQgZnJvbSB0aGUgSHRtbCB0ZW1wbGF0ZSAsIHNvbWV0aGluZyBsaWtlIDwuLi4gaWQ9J3RhYl9pZCcuLj4gIFwiKTtcclxuICAgICAgICAgICAgfWVsc2UgaWYodGhpcy5wYXJlbnQgJiYgdGhpcy5wYXJlbnQudGFiQ2hpbGRyZW5BcnJheS5sZW5ndGggPiAwICl7XHJcbiAgICAgICAgICAgICAgICAvLyBkeW5hbWljIHRhYnNcclxuICAgICAgICAgICAgICAgIGxldCBpbmRleE9mQWN0aXZlPSB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5LmZpbmRJbmRleCh4PT54LmFjdGl2ZSA9PSB0cnVlKTtcclxuICAgICAgICAgICAgICAgIGlmKGluZGV4T2ZBY3RpdmUgPT0gLTEpe1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIHRoZXJlIGlzIG5vIGFjdGl2ZSB0YWJcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5WzBdLmFjdGl2ZSA9dHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5WzBdLmRpc3BsYXkgPXRydWU7XHJcbiAgICAgICAgICAgICAgICB9ZWxzZSBpZiggdGhpcy5hY3RpdmUgPT0gdHJ1ZSApe1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIHRoZXJlIGlzIGFscmVhZHkgYW4gYWN0aXZlIHRhYlxyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucGFyZW50LnRhYkNoaWxkcmVuQXJyYXkuZm9yRWFjaChmdW5jdGlvbiAodGFiOiBUYWIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYodGFiICE9IHRoaXMgKXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYi5hY3RpdmUgPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFiLmRpc3BsYXkgPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3RpdmUgPXRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kaXNwbGF5ID10cnVlO1xyXG4gICAgICAgICAgICAgICAgfWVsc2V7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3RpdmU9ZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kaXNwbGF5PWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVsc2UgaWYoIXRoaXMuYWN0aXZlKXtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGlzcGxheT1mYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1jYXRjaChlcnJvcil7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ21ldGhvZCBbbmdPbkluaXRdIC0gZXJyb3I6JyxlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIFxyXG4gICAgaXNWaXNpYmxlKGUpIHtcclxuICAgICAgICByZXR1cm4gISEoIGUub2Zmc2V0V2lkdGggfHwgZS5vZmZzZXRIZWlnaHQgfHwgZS5nZXRDbGllbnRSZWN0cygpLmxlbmd0aCApO1xyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBTZXRzIGEgc3R5bGUgdG8gdGhlIHRhYidzIGhlYWRlci5cclxuICAgICAqIEBwYXJhbSBwcm9wXHJcbiAgICAgKiBAcGFyYW0gdmFsdWVcclxuICAgICAqL1xyXG4gICAgcHVibGljIHNldFRhYkhlYWRlclN0eWxlKHByb3AgLCB2YWx1ZSl7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAgIHZhciBoZWFkZXIgPSAkKCcjaGVhZGVyXycrdGhpcy5pZCk7XHJcbiAgICAgICAgICAgICAgICAkKGhlYWRlclswXSkuYXR0cihcInN0eWxlXCIsIHByb3ArXCI6XCIrdmFsdWUrXCIhaW1wb3J0YW50XCIpO1xyXG4gICAgICAgICAgICB9LCAwKTtcclxuICAgICAgICB9Y2F0Y2goZXJyb3Ipe1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdlcnJvciA6JywgZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogbmdPbkRlc3Ryb3lcclxuICAgICAqL1xyXG4gICAgbmdPbkRlc3Ryb3koKTogdm9pZCB7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICB0aGlzLnJlbW92ZUFsbE91cHV0c0V2ZW50c0xpc3RlbmVycygkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSk7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnBhcmVudCA7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9hY3RpdmU7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl92aXNpYmxlO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fbGFiZWw7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9kaXNwbGF5O1xyXG5cclxuICAgICAgICB9Y2F0Y2goZXJyb3IgKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBzZXRVbmRvY2tQb2xpY3lcclxuICAgICAqIFxyXG4gICAgICogQHBhcmFtIGluZGV4OmludFxyXG4gICAgICogXHJcbiAgICAgKiBAcGFyYW0gdmFsdWU6U3RyaW5nXHJcbiAgICAgKiBcclxuICAgICAqIE1ldGhvZCB0byBzZXQgdW5kb2NrIHBvbGljeSBmb3IgdGFiXHJcbiAgICAgKi8gIFxyXG4gICAgcHVibGljIHNldFVuZG9ja1BvbGljeShpbmRleDpudW1iZXIsIHZhbHVlOnN0cmluZyk6dm9pZCB7XHJcbiAgICAgICAgaWYodGhpcy5pZCAgPj0gaW5kZXggKyAxKSB7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB0aGlzLmdldENoaWxkQXQoaW5kZXgpLnVuZG9ja1BvbGljeSA9IHZhbHVlO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICB9XHJcbiAgICAgXHJcbiAgICB9XHJcbn1cclxuLyoqXHJcbiAqXHJcbiAqIFRhYiBjb21wb25lbnQuXHJcbiAqICoqL1xyXG5cclxuLy9AZHluYW1pY1xyXG5AQ29tcG9uZW50KHtcclxuICAgIHNlbGVjdG9yOiAnU3d0VGFiUHVzaFN0cmF0ZWd5JyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICAgICAgPGRpdiAgY2xhc3M9XCJzd3R0YWJcIiA+XHJcbiAgICAgICAgICAgIDxuZy1jb250ZW50PjwvbmctY29udGVudD5cclxuICAgICAgICAgICAgIDxkaXYgICNfY29udGFpbmVyID48L2Rpdj5cclxuICAgICAgICA8L2Rpdj4gXHJcbiAgICBgLFxyXG4gICAgc3R5bGVzOiBbYFxyXG4gICAgICAgICA6aG9zdCB7XHJcbiAgICAgICAgICAgICAgbWFyZ2luOiAgMHB4O1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgICBkaXNwbGF5OmJsb2NrO1xyXG4gICAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgIH1cclxuICAgICAgICAuc3d0dGFiIHtcclxuICAgICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogLW1vei1mbGV4O1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IC1vLWZsZXg7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1mbGV4O1xyXG4gICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgYF0sXHJcbiAgICBwcm92aWRlcnM6IFt7cHJvdmlkZTogVGFiLCB1c2VFeGlzdGluZzogVGFiUHVzaFN0YXRlZ3l9XSxcclxuICAgIGNoYW5nZURldGVjdGlvbjogQ2hhbmdlRGV0ZWN0aW9uU3RyYXRlZ3kuT25QdXNoXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBUYWJQdXNoU3RhdGVneSBleHRlbmRzIFRhYiBpbXBsZW1lbnRzIE9uSW5pdCAsIE9uRGVzdHJveXtcclxuXHJcbiAvLy0tLWFjdGl2ZS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIC8vXHJcbiBASW5wdXQoJ2FjdGl2ZScpXHJcbiBzZXQgYWN0aXZlKHZhbHVlOiBhbnkpIHtcclxuICAgICBpZih2YWx1ZSl7XHJcbiAgICAgICAgIGlmKHRoaXMucGFyZW50KXtcclxuICAgICAgICAgICAgIHZhciBpbmRleEFjdGl2ZSA9IHRoaXMucGFyZW50LnRhYkNoaWxkcmVuQXJyYXkuZmluZEluZGV4KHg9PnguYWN0aXZlID09IHRydWUgKTtcclxuICAgICAgICAgICAgIGlmKGluZGV4QWN0aXZlID49IDAgKXtcclxuICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5W2luZGV4QWN0aXZlXS5hY3RpdmUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5W2luZGV4QWN0aXZlXS5kaXNwbGF5ID0gZmFsc2U7XHJcbiAgICAgICAgICAgICB9XHJcbiAgICAgICAgIH1cclxuICAgICAgICAgdGhpcy5kaXNwbGF5ID0gdHJ1ZTtcclxuICAgICB9XHJcbiAgICAgdGhpcy5fYWN0aXZlID0gdGhpcy5hZGFwdFZhbHVlQXNCb29sZWFuKHZhbHVlKTtcclxuICAgICBpZih0aGlzLl9hY3RpdmUpe1xyXG4gICAgICAgICBpZiAoISh0aGlzLmNkIGFzIGFueSkuZGVzdHJveWVkKSB7XHJcbiAgICAgICAgICAgICB0aGlzLmNkLm1hcmtGb3JDaGVjaygpO1xyXG4gICAgICAgICB9XHJcbiAgICAgICAgIFxyXG4gICAgIH1cclxuIH1cclxuIGdldCBhY3RpdmUoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5fYWN0aXZlO1xyXG59XHJcbiAvKipcclxuICAqIENvbnN0cnVjdG9yXHJcbiAgKiBAcGFyYW0gZWxlbVxyXG4gICogQHBhcmFtIGNvbW1vblNlcnZpY2VcclxuICAqL1xyXG4gY29uc3RydWN0b3IocHJvdGVjdGVkIGVsZW06IEVsZW1lbnRSZWYsIHByb3RlY3RlZCAgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSwgcHJvdGVjdGVkIGNkOiBDaGFuZ2VEZXRlY3RvclJlZikge1xyXG4gICAgIHN1cGVyKGVsZW0sIGNvbW1vblNlcnZpY2UpO1xyXG4gfVxyXG5cclxuIC8qKlxyXG4gICogbmdPbkluaXRcclxuICAqL1xyXG4gbmdPbkluaXQoKSB7XHJcbiAgICAgdHJ5e1xyXG4gICAgICAgICBpZih0aGlzLmlkICA9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIj4+PiBQUk9HUkFNTUlORyBFcnJvciBbbWFuZG90cm95IHBhcmFtZXRlcl0gOiB5b3UgbWlnaHQgZm9yZ290IHRvIGFkZCBhbiBpZCB0aGUgdGhlIFRhYiBjcmVhdGVkIGZyb20gdGhlIEh0bWwgdGVtcGxhdGUgLCBzb21ldGhpbmcgbGlrZSA8Li4uIGlkPSd0YWJfaWQnLi4+ICBcIik7XHJcbiAgICAgICAgIH1lbHNlIGlmKHRoaXMucGFyZW50ICYmIHRoaXMucGFyZW50LnRhYkNoaWxkcmVuQXJyYXkubGVuZ3RoID4gMCApe1xyXG4gICAgICAgICAgICAgLy8gZHluYW1pYyB0YWJzXHJcbiAgICAgICAgICAgICBsZXQgaW5kZXhPZkFjdGl2ZT0gdGhpcy5wYXJlbnQudGFiQ2hpbGRyZW5BcnJheS5maW5kSW5kZXgoeD0+eC5hY3RpdmUgPT0gdHJ1ZSk7XHJcbiAgICAgICAgICAgICBpZihpbmRleE9mQWN0aXZlID09IC0xKXtcclxuICAgICAgICAgICAgICAgICAvLyB0aGVyZSBpcyBubyBhY3RpdmUgdGFiXHJcbiAgICAgICAgICAgICAgICAgdGhpcy5wYXJlbnQudGFiQ2hpbGRyZW5BcnJheVswXS5hY3RpdmUgPXRydWU7XHJcbiAgICAgICAgICAgICAgICAgdGhpcy5wYXJlbnQudGFiQ2hpbGRyZW5BcnJheVswXS5kaXNwbGF5ID10cnVlO1xyXG4gICAgICAgICAgICAgfWVsc2UgaWYoIHRoaXMuYWN0aXZlID09IHRydWUgKXtcclxuICAgICAgICAgICAgICAgICAvLyB0aGVyZSBpcyBhbHJlYWR5IGFuIGFjdGl2ZSB0YWJcclxuICAgICAgICAgICAgICAgICB0aGlzLnBhcmVudC50YWJDaGlsZHJlbkFycmF5LmZvckVhY2goZnVuY3Rpb24gKHRhYjogVGFiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgIGlmKHRhYiAhPSB0aGlzICl7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICB0YWIuYWN0aXZlID1mYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgIHRhYi5kaXNwbGF5ID1mYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuYWN0aXZlID10cnVlO1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuZGlzcGxheSA9dHJ1ZTtcclxuICAgICAgICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuYWN0aXZlPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuZGlzcGxheT1mYWxzZTtcclxuICAgICAgICAgICAgIH1cclxuICAgICAgICAgfVxyXG4gICAgICAgICBlbHNlIGlmKCF0aGlzLmFjdGl2ZSl7XHJcbiAgICAgICAgICAgICB0aGlzLmRpc3BsYXk9ZmFsc2U7XHJcbiAgICAgICAgIH1cclxuICAgICAgICAgVGFiU2VsZWN0RXZlbnQuc3Vic2NyaWJlKCh0YWIpID0+IHtcclxuICAgICAgICAgICAgIGlmKHRoaXMuaXNWaXNpYmxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSl7XHJcbiAgICAgICAgICAgICAgICAgaWYgKCEodGhpcy5jZCBhcyBhbnkpLmRlc3Ryb3llZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICB0aGlzLmNkLm1hcmtGb3JDaGVjaygpO1xyXG4gICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgIH1cclxuICAgICAgICAgfSk7XHJcbiAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtuZ09uSW5pdF0gLSBlcnJvcjonLGVycm9yKTtcclxuICAgICB9XHJcbiB9XHJcbiBcclxuXHJcbn1cclxuXHJcbi8vQGR5bmFtaWNcclxuQENvbXBvbmVudCh7XHJcbiAgICBzZWxlY3RvcjogJ1N3dFRhYk5hdmlnYXRvcicsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgICA8ZGl2ID5cclxuICAgICAgICAgIDxkaXYgY2xhc3M9XCJuYXYtYmFyLWNvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJidG4gbGVmdEFycm93XCIgKG1vdXNlZG93bik9XCJzY3JvbGxUYWJzKCdsZWZ0JylcIiAobW91c2V1cCk9XCJkaXNwb3NlKClcIj48c3Bhbj48Yj4mIzk2NjY7PC9iPjwvc3Bhbj48L2Rpdj5cclxuICAgICAgICAgICAgICA8dWwgKm5nSWY9XCJhYm91dEFjdGl2ZVwiIChtb3VzZXVwKT1cImRpc3Bvc2UoKVwiICAgaWQ9XCJzd3RUYWJOYXZpZ2F0b3JcIiBjbGFzcz1cIm5hdiBuYXYtdGFicyBcIj5cclxuICAgICAgICAgICAgICA8bmctY29udGFpbmVyICAgKm5nRm9yPVwibGV0IHRhYiBvZiBzb3J0VGFiQ2hpbGRyZW5BcnJheSgpIGxldCBhY3QgPSBpbmRleFwiICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPGxpICpuZ0lmPVwidGFiLnZpc2libGUgJiYgdGFiLmxhYmVsXCIgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD0naGVhZGVyX3t7dGFiLmlkfX0nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFtjbGFzcy5hY3RpdmVdPVwidGFiLmFjdGl2ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFt0aXRsZV09XCJ0YWIudG9vbFRpcFwiIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBbY2xhc3MuZGlzYWJsZWQtY29udGFpbmVyXT1cIiF0YWIuZW5hYmxlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPVwidGFiTmF2aWdhdG9yLXRhYnNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNsaWNrKT1cIm9uTW91c2Vkb3duKHRhYiwgJGV2ZW50KVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9XCJkaXNwbGF5OmZsZXg7XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3t0YWIubGFiZWx9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGkgKm5nSWY9XCJ0YWIuY2xvc2FibGVcIiAgIGNsYXNzPVwiZmEgZmEtdGltZXMgY2xvc2VGYXZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAoY2xpY2spPVwicmVtb3ZlQ29udGVudFRhYih0YWIpXCI+PC9pPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgIDwvbmctY29udGFpbmVyPlxyXG4gICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cImJ0biByaWdodEFycm93XCIgW25nU3R5bGVdPVwieyd2aXNpYmlsaXR5JzogKCB0YWJDaGlsZHJlbkFycmF5Lmxlbmd0aCA9PSAwICB8fCBjbGllbnRXaWR0aCA8ICBzY3JvbGxXaWR0aCAgfHwgc2Nyb2xsVmFsdWUgPT0gIG1heFNjcm9sbCApID8gJ2hpZGRlbic6J3Zpc2libGUnfVwiIChtb3VzZWRvd24pPVwic2Nyb2xsVGFicygncmlnaHQnKVwiIChtb3VzZXVwKT1cImRpc3Bvc2UoKVwiPjxzcGFuPjxiPiYjOTY1Njs8L2I+PC9zcGFuPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cImJ0bi1ncm91cFwiW25nU3R5bGVdPVwieyd2aXNpYmlsaXR5JzogdGFiQ2hpbGRyZW5BcnJheS5sZW5ndGggPiAwICYmIHNob3dEcm9wRG93biA/ICd2aXNpYmxlJzonaGlkZGVuJ31cIiA+XHJcbiAgICAgICAgICAgICAgICA8YSBpZD1cInRlc3QtZHJvcGRvd24tYnRuXCIgY2xhc3M9XCJpbnB1dC1ncm91cC1hZGRvbiBkcm9wZG93bkJ0biBkcm9wZG93bi10b2dnbGVcIiAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8aSBfbmdjb250ZW50LWM4PVwiXCIgY2xhc3M9XCJnbHlwaGljb24gZ2x5cGhpY29uLXRyaWFuZ2xlLWJvdHRvbVwiPjwvaT5cclxuICAgICAgICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgICAgICAgICAgPHVsICBjbGFzcz1cImRyb3Bkb3duLW1lbnVcIiByb2xlPVwibWVudVwiICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGkgcm9sZT1cIm1lbnVpdGVtXCIgKm5nRm9yPVwibGV0IHRhYiBvZiB0YWJDaGlsZHJlbkFycmF5XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBjbGFzcz1cImRyb3Bkb3duLWl0ZW1cIiAoY2xpY2spPVwic2Nyb2xsVG9UYWJGcm9tQ29tYm8odGFiKVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxiPnt7IHRhYi5sYWJlbCB9fTwvYj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgPC91bD4gXHJcbiAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2ICBjbGFzcz1cInRhYk5hdmlnYXRvci1jb250ZW50XCIgc3R5bGU9XCJ3aWR0aDoxMDAlOyBoZWlnaHQ6Y2FsYygxMDAlIC0gMjBweCk7XCI+XHJcbiAgICAgICAgICAgICAgPG5nLWNvbnRlbnQ+PC9uZy1jb250ZW50PlxyXG4gICAgICAgICAgICAgIDxkaXYgI19jb250YWluZXIgPjwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICBgLFxyXG4gICAgc3R5bGVzOiBbYFxyXG4gICAgICAgICA6aG9zdCB7XHJcbiAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICBtYXJnaW46IDBweCAwcHggNXB4IDBweDtcclxuICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcclxuICAgICAgICAgfVxyXG4gICAgICAgLmRyb3Bkb3duQnRue1xyXG4gICAgICAgICAgIGhlaWdodDogMjBweDtcclxuICAgICAgICAgICB3aWR0aDogMjBweDtcclxuICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjOUM5RkExO1xyXG4gICAgICAgICAgIHBhZGRpbmc6IDBweDtcclxuICAgICAgICAgICBjdXJzb3I6IGRlZmF1bHQ7XHJcbiAgICAgICAgICAgbWFyZ2luLXRvcDogMXB4O1xyXG4gICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcclxuICAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICAgICAgICAgY29sb3I6IGJsYWNrO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC13ZWJraXQtbGluZWFyLWdyYWRpZW50KHRvcCwjRkZGRkZGLCAjY2NlY2ZmKTtcclxuICAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiAtbW96LWxpbmVhci1ncmFkaWVudCh0b3AsICNGRkZGRkYsICNjY2VjZmYpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC1tcy1saW5lYXItZ3JhZGllbnQodG9wLCAjRkZGRkZGLCAjY2NlY2ZmKTtcclxuICAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiAtby1saW5lYXItZ3JhZGllbnQodG9wLCNGRkZGRkYsICNjY2VjZmYpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sI0ZGRkZGRiwgI2NjZWNmZiApO1xyXG4gICAgICAgfVxyXG4gICAgICAgLmxlZnRBcnJvdywgLnJpZ2h0QXJyb3cge1xyXG4gICAgICAgICAgIGhlaWdodDogMjBweDtcclxuICAgICAgICAgICB3aWR0aDogMjBweDtcclxuICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjOUM5RkExO1xyXG4gICAgICAgICAgIHBhZGRpbmc6IDBweDsgXHJcbiAgICAgICAgICAgY3Vyc29yOiBkZWZhdWx0O1xyXG4gICAgICAgICAgIG1hcmdpbi10b3A6IDFweDtcclxuICAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogLXdlYmtpdC1saW5lYXItZ3JhZGllbnQodG9wLCNGRkZGRkYsICNjY2VjZmYpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC1tb3otbGluZWFyLWdyYWRpZW50KHRvcCwgI0ZGRkZGRiwgI2NjZWNmZik7XHJcbiAgICAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogLW1zLWxpbmVhci1ncmFkaWVudCh0b3AsICNGRkZGRkYsICNjY2VjZmYpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC1vLWxpbmVhci1ncmFkaWVudCh0b3AsI0ZGRkZGRiwgI2NjZWNmZik7XHJcbiAgICAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwjRkZGRkZGLCAjY2NlY2ZmKTtcclxuICAgICAgIH1cclxuICAgICAgIC5sZWZ0QXJyb3c6aG92ZXIsIC5yaWdodEFycm93OmhvdmVyIHtcclxuICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjMDA5REZGO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC13ZWJraXQtbGluZWFyLWdyYWRpZW50KHRvcCwjRkZGRkZGLCAjRUVFRUVFKTtcclxuICAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiAtbW96LWxpbmVhci1ncmFkaWVudCh0b3AsICNGRkZGRkYsICNFRUVFRUUpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IC1tcy1saW5lYXItZ3JhZGllbnQodG9wLCAjRkZGRkZGLCAjRUVFRUVFKTtcclxuICAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiAtby1saW5lYXItZ3JhZGllbnQodG9wLCNGRkZGRkYsICNFRUVFRUUpO1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sI0ZGRkZGRiwgI0VFRUVFRSk7XHJcbiAgICAgICB9XHJcbiAgICAgICAubGVmdEFycm93PnNwYW4sIC5yaWdodEFycm93PnNwYW4ge1xyXG4gICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgIH1cclxuICAgICAgIC5uYXYtYmFyLWNvbnRhaW5lcntcclxuICAgICAgICAgICB3aWR0aDoxMDAlO1xyXG4gICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgbWFyZ2luOiAwcHggMHB4IDBweCAtMnB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgZGlzcGxheTotbXMtZmxleGJveDtcclxuICAgICAgICAgICAtbXMtZmxleC13cmFwOiBub3dyYXA7XHJcbiAgICAgICB9XHJcbiAgICAgICBcclxuICAgICAgIC5pY29uIHtcclxuICAgICAgICAgICB3aWR0aDogMTVweDtcclxuICAgICAgICAgICBtYXJnaW4tdG9wOiAtM3B4O1xyXG4gICAgICAgfVxyXG4gICAgICAgLm5hdiB7XHJcbiAgICAgICAgICAgcGFkZGluZzogMHB4O1xyXG4gICAgICAgICAgIGhlaWdodDogMjBweDtcclxuICAgICAgICAgICBvdmVyZmxvdy14OiBhdXRvO1xyXG4gICAgICAgICAgIG92ZXJmbG93LXk6IGhpZGRlbjtcclxuICAgICAgICAgICBtYXJnaW4tdG9wOiAxcHg7XHJcbiAgICAgICAgICAgZmxleC1ncm93OjE7XHJcbiAgICAgICAgICAgd2lkdGg6IG1heC1jb250ZW50O1xyXG4gICAgICAgfVxyXG4gICAgICAgLyogaGlkZSBzY3JvbGwgYmFyIGFuZCBrZWVwIGRpdiBzY3JvbGxhYmxlICovXHJcbiAgICAgICAubmF2Ojotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICAgICAgICAgd2lkdGg6IDBweDtcclxuICAgICAgIH1cclxuICAgICAgIC8qIHdpZHRoICovXHJcbiAgICAgICAubmF2Ojotd2Via2l0LXNjcm9sbGJhcjpob3Jpem9udGFsIHtcclxuICAgICAgICAgICBoZWlnaHQ6IDBweDtcclxuICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgIC5uYXYtdGFic3tcclxuICAgICAgICAgICAgcGFkZGluZzogMHB4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgIG92ZXJmbG93LXg6IGF1dG87XHJcbiAgICAgICAgICAgIG92ZXJmbG93LXk6IGhpZGRlbjtcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogMXB4O1xyXG4gICAgICAgICAgICAtbXMtZmxleC1wb3NpdGl2ZTogMTtcclxuICAgICAgICAgICAgZmxleC1ncm93OiAxO1xyXG4gICAgICAgICAgICBkaXNwbGF5OmZsZXg7XHJcbiAgICAgICAgICAgIHdpZHRoOiBtYXgtY29udGVudDtcclxuICAgICAgIH1cclxuXHJcbiAgICAgICAuY2xvc2VGYXZ7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjRUI1OTQ2O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICAgICAgcGFkZGluZzogMXB4O1xyXG4gICAgICAgICAgICBwYWRkaW5nLXRvcDogMHB4O1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICAgIGxlZnQ6IDZweDtcclxuICAgICAgICAgICAgdG9wOiAxcHg7XHJcbiAgICAgICB9IFxyXG4gICAgICAgLmJ0bi1ncm91cD4gdWwge1xyXG4gICAgICAgICAgIG1hcmdpbi10b3A6IC0xcHg7XHJcbiAgICAgICAgICAgbWFyZ2luLWxlZnQ6IC0xMzlweDtcclxuICAgICAgICAgICB3aWR0aDogLXdlYmtpdC1zdHJldGNoO1xyXG4gICAgICAgfVxyXG4gICAgICAgLmJ0bi1ncm91cD4gdWw+IGxpIHtcclxuICAgICAgICAgICBmb250LXNpemU6IDExcHg7XHJcbiAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICAgICB9XHJcbiAgICAgICAuYnRuLWdyb3VwPiB1bD4gbGk+IGE6aG92ZXIge1xyXG4gICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNiMmUxZmYgIWltcG9ydGFudDtcclxuICAgICAgICAgICBjdXJzb3I6IGRlZmF1bHQ7XHJcbiAgICAgICB9XHJcblxyXG5cclxuXHJcbiAgYF1cclxufSlcclxuZXhwb3J0IGNsYXNzIFN3dFRhYk5hdmlnYXRvciBleHRlbmRzIENvbnRhaW5lciBpbXBsZW1lbnRzIE9uSW5pdCAsIEFmdGVyVmlld0luaXQsIE9uRGVzdHJveSB7XHJcblxyXG4gICAgLy8tLS1Qcm9wZXJ0aWVzIGRlZmluaXRpb25zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBwcml2YXRlIHNlbGVjdGVkVGFiSWQgPSAwO1xyXG4gICAgcHVibGljIHRhYkNoaWxkcmVuQXJyYXkgPSBbXTtcclxuICAgIHByaXZhdGUgX3NlbGVjdGVkSW5kZXg9IDA7XHJcbiAgICBwcml2YXRlIF9zZWxlY3RlZExhYmVsPW51bGw7XHJcbiAgICBwcml2YXRlIF9zZWxlY3RlZFRhYj1udWxsO1xyXG4gICAgcHJpdmF0ZSBfYm9yZGVyQm90dG9tID10cnVlO1xyXG4gICAgcHJpdmF0ZSBfYm9yZGVyVG9wID10cnVlO1xyXG4gICAgcHJpdmF0ZSBfb25DaGFuZ2UgICA9IG5ldyBGdW5jdGlvbigpO1xyXG5cclxuICBwdWJsaWMgIE1PVVNFX0RPV04gICA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgLy9AT3V0cHV0KCdNT1VTRV9ET1dOJykgTU9VU0VfRE9XTjogRXZlbnRFbWl0dGVyPGFueT4gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcclxuXHJcbiAgICBwdWJsaWMgYWJvdXRBY3RpdmUgPSB0cnVlO1xyXG4gICAgcHVibGljIGNsaWVudFdpZHRoOiBudW1iZXIgPSAwO1xyXG4gICAgcHVibGljIHNjcm9sbFdpZHRoOiBudW1iZXIgPSAwO1xyXG4gICAgcHVibGljIHRpbWVyOiBhbnk7XHJcbiAgICBwdWJsaWMgc2Nyb2xsVmFsdWU6IG51bWJlciA9IDA7XHJcbiAgICBwdWJsaWMgbWF4U2Nyb2xsOiBudW1iZXIgPSAwO1xyXG4gICAgXHJcbiAgICBwdWJsaWMgbmF2QmFyQ29udGFpbmVyIDtcclxuICAgIHB1YmxpYyBuYXZUYWJzIDtcclxuICAgIHB1YmxpYyBuYXZUYWJzTGk7XHJcblxyXG4gICAgQFZpZXdDaGlsZChcImNvbnRhaW5lck5hdmlnYXRvclwiKSBjb250YWluZXJOYXZpZ2F0b3I6IEVsZW1lbnRSZWY7Ly8gTWFrZSBwYW5lbFRvcCBET00gcmVmZXJlbmNlLlxyXG4gICAgQFZpZXdDaGlsZChcInN3dFRhYk5hdmlnYXRvclwiKSBzd3RUYWJOYXZpZ2F0b3I6IEVsZW1lbnRSZWY7Ly8gTWFrZSBwYW5lbFRvcCBET00gcmVmZXJlbmNlLlxyXG5cclxuICAgIC8vLS0tT3V0cHV0cy0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQE91dHB1dCgnb25DaGFuZ2UnKSAgICBwcml2YXRlIG9uQ2hhbmdlXyAgID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgICBfc2hvd0Ryb3BEb3duOiBib29sZWFuID0gZmFsc2U7XHJcbiAgICBfYXBwbHlPcmRlcjogYm9vbGVhbiA9IGZhbHNlO1xyXG4gICAgXHJcbiAgICBzb3J0VGFiQ2hpbGRyZW5BcnJheSgpIHtcclxuICAgICAgICBpZih0aGlzLmFwcGx5T3JkZXIpXHJcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnRhYkNoaWxkcmVuQXJyYXkuc29ydCgoYSwgYikgPT4gYVsnb3JkZXInXSA+IGJbJ29yZGVyJ10gPyAxIDogYVsnb3JkZXInXSA9PT0gYlsnb3JkZXInXSA/IDAgOiAtMSk7XHJcbiAgICAgICAgZWxzZVxyXG4gICAgICAgICAgICByZXR1cm4gdGhpcy50YWJDaGlsZHJlbkFycmF5XHJcbiAgICAgIH1cclxuICAgIFxyXG4gICAgY2FsY3VsYXRlSGVpZ3QoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5jb250YWluZXJOYXZpZ2F0b3IubmF0aXZlRWxlbWVudC5vZmZzZXRIZWlnaHQgLSB0aGlzLnN3dFRhYk5hdmlnYXRvci5uYXRpdmVFbGVtZW50Lm9mZnNldEhlaWdodDtcclxuICAgIH1cclxuICAgIC8vLS0tQ29udGVudENoaWxkcmVuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBAQ29udGVudENoaWxkcmVuKFRhYilcclxuICAgIHNldCB0YWJDaGlsZHJlbihpdGVtcykge1xyXG4gICAgICAgIGl0ZW1zLl9yZXN1bHRzLmZvckVhY2goKHRhYikgPT57XHJcbiAgICAgICAgICAgIHRhYi5wYXJlbnQgPSB0aGlzO1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHRoaXMudGFiQ2hpbGRyZW5BcnJheSA9IGl0ZW1zLl9yZXN1bHRzLmZpbHRlcih4ID0+IHgucGFyZW50LmlkID09IHRoaXMuaWQpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tc2VsZWN0ZWRUYWItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgcHVibGljIHNldCBzZWxlY3RlZFRhYih0YWI6IFRhYikge1xyXG5cclxuICAgICAgICAgIHZhciBpbmRleCA9IHRoaXMudGFiQ2hpbGRyZW5BcnJheS5maW5kSW5kZXgoeD0+KCh4LmlkID09ICB0YWIuaWQpICYmIHRhYi52aXNpYmxlKSApO1xyXG4gICAgICAgIGlmKGluZGV4ID49IDApe1xyXG4gICAgICAgICAgICB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdLmFjdGl2ZT10cnVlO1xyXG4gICAgICAgICAgICBpZiggdGhpcy5zZWxlY3RlZEluZGV4ICAhPSBpbmRleCAgKXtcclxuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMub25DaGFuZ2VfLmVtaXQoKTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9vbkNoYW5nZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgfSwgMCk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IGluZGV4O1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZFRhYiA9IHRhYjtcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiPj4+IFBST0dSQU1NSU5HIEVycm9yIFt3cm9uZyBUYWJdIDogZXF1aXZhbGVudCBUYWIgZG9lcyBub3QgZXhpc3QgaW4gVGFiTmF2aWdhdG9yLiAgXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH0gXHJcblxyXG4gICAgcHVibGljIGdldCBzZWxlY3RlZFRhYigpIHtcclxuICAgICAgICByZXR1cm4gIHRoaXMudGFiQ2hpbGRyZW5BcnJheVt0aGlzLnNlbGVjdGVkSW5kZXhdO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tc2VsZWN0ZWRDaGlsZC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBwdWJsaWMgc2V0IHNlbGVjdGVkQ2hpbGQodGFiOiBUYWIpIHtcclxuICAgICAgICB2YXIgaW5kZXggPSB0aGlzLnRhYkNoaWxkcmVuQXJyYXkuZmluZEluZGV4KHg9PnguaWQgPT0gIHRhYi5pZCAgICk7XHJcbiAgICAgICAgaWYoaW5kZXggPj0gMCl7XHJcbiAgICAgICAgICAgIHRoaXMudGFiQ2hpbGRyZW5BcnJheVtpbmRleF0uYWN0aXZlPXRydWU7XHJcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IGluZGV4O1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZFRhYiA9IHRhYjtcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiPj4+IFBST0dSQU1NSU5HIEVycm9yIFt3cm9uZyBUYWJdIDogZXF1aXZhbGVudCBUYWIgZG9lcyBub3QgZXhpc3QgaW4gVGFiTmF2aWdhdG9yLiAgXCIpO1xyXG4gICAgfVxyXG4gICAgfSBcclxuXHJcbiAgICBwdWJsaWMgZ2V0IHNlbGVjdGVkQ2hpbGQoKSB7XHJcbiAgICAgICAgcmV0dXJuICB0aGlzLl9zZWxlY3RlZFRhYjtcclxuICAgIH1cclxuICAgIC8vLS0tc2VsZWN0ZWRMYWJlbC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgc2V0IHNlbGVjdGVkTGFiZWwodmFsdWU6IHN0cmluZykge1xyXG4gICAgICAgIHZhciBpbmRleCA9IHRoaXMudGFiQ2hpbGRyZW5BcnJheS5maW5kSW5kZXgoeD0+eC5sYWJlbCA9PSB2YWx1ZSApO1xyXG4gICAgICAgIGlmKGluZGV4ID49IDApe1xyXG4gICAgICAgICAgICB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdLmFjdGl2ZT10cnVlO1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZExhYmVsID0gdmFsdWU7XHJcbiAgICAgICAgfWVsc2V7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIj4+PiBQUk9HUkFNTUlORyBFcnJvciBbd3JvbmcgTGFiZWxdIDogVGFiIHdpdGggY29ycmVzcG9uZGVudCBMYWJlbCBub3QgZXhpc3RzLiAgXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBnZXQgc2VsZWN0ZWRMYWJlbCgpIHtcclxuICAgICAgICBpZih0aGlzLnNlbGVjdGVkVGFiICE9IG51bGwpe1xyXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZFRhYi5sYWJlbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLW9uQ2hhbmdlLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIGdldCBvbkNoYW5nZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fb25DaGFuZ2U7XHJcbiAgICB9XHJcbiAgICBzZXQgb25DaGFuZ2UoIHZhbHVlOiBGdW5jdGlvbil7XHJcbiAgICAgICAgdGhpcy5fb25DaGFuZ2U9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tc2VsZWN0ZWRJbmRleC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgcHVibGljIHNldCBzZWxlY3RlZEluZGV4KCBpbmRleDogbnVtYmVyICkge1xyXG4gICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kZXggPSBpbmRleDtcclxuICAgICAgICBpZih0aGlzLnRhYkNoaWxkcmVuQXJyYXkgJiYgdGhpcy50YWJDaGlsZHJlbkFycmF5W2luZGV4XSl7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdLmFjdGl2ZSA9dHJ1ZTtcclxuICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRUYWIgPSB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdO1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZExhYmVsID0gdGhpcy50YWJDaGlsZHJlbkFycmF5W2luZGV4XS5sYWJlbDtcclxuICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFRhYklkID0gdGhpcy50YWJDaGlsZHJlbkFycmF5W2luZGV4XS5pZDtcclxuICAgICAgICB9XHJcblxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBzZWxlY3RlZEluZGV4KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9zZWxlY3RlZEluZGV4O1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tQm9yZGVyQm90dG9tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ2JvcmRlckJvdHRvbScpXHJcbiAgICBzZXQgYm9yZGVyQm90dG9tKHZhbHVlIDogYW55KSB7XHJcbiAgICAgICAgdGhpcy5fYm9yZGVyQm90dG9tID0gdGhpcy5hZGFwdFZhbHVlQXNCb29sZWFuKHZhbHVlKTtcclxuICAgICAgICBpZighdGhpcy5fYm9yZGVyQm90dG9tKSAkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVsxXSkuY3NzKFwiYm9yZGVyLWJvdHRvbVwiLDApO1xyXG5cclxuICAgIH1cclxuICAgIGdldCBib3JkZXJCb3R0b20oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2JvcmRlckJvdHRvbTtcclxuICAgIH1cclxuICAgIC8vLS0tQm9yZGVyQm90dG9tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ3Nob3dEcm9wRG93bicpXHJcbiAgICBzZXQgc2hvd0Ryb3BEb3duKHZhbHVlIDogYW55KSB7XHJcbiAgICAgICAgdGhpcy5fc2hvd0Ryb3BEb3duID0gdGhpcy5hZGFwdFZhbHVlQXNCb29sZWFuKHZhbHVlKTtcclxuICAgIH1cclxuICAgIGdldCBzaG93RHJvcERvd24oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Nob3dEcm9wRG93bjtcclxuICAgIH1cclxuXHJcbiAgICAgLy8tLS1Cb3JkZXJCb3R0b20tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgICBASW5wdXQoJ2FwcGx5T3JkZXInKVxyXG4gICAgIHNldCBhcHBseU9yZGVyKHZhbHVlIDogYW55KSB7XHJcbiAgICAgICAgIHRoaXMuX2FwcGx5T3JkZXIgPSB0aGlzLmFkYXB0VmFsdWVBc0Jvb2xlYW4odmFsdWUpO1xyXG4gICAgIH1cclxuICAgICBnZXQgYXBwbHlPcmRlcigpIHtcclxuICAgICAgICAgcmV0dXJuIHRoaXMuX2FwcGx5T3JkZXI7XHJcbiAgICAgfVxyXG5cclxuICAgIC8vLS0tQm9yZGVyVG9wLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCdib3JkZXJUb3AnKVxyXG4gICAgc2V0IGJvcmRlclRvcCh2YWx1ZSA6IGFueSkge1xyXG4gICAgICAgIHRoaXMuX2JvcmRlclRvcCA9IHRoaXMuYWRhcHRWYWx1ZUFzQm9vbGVhbih2YWx1ZSk7XHJcbiAgICAgICAgaWYoIXRoaXMuX2JvcmRlclRvcCkgJCgkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNoaWxkcmVuKClbMV0pLmNzcyhcImJvcmRlci10b3BcIiwwKTtcclxuXHJcbiAgICB9XHJcbiAgICBnZXQgYm9yZGVyVG9wKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9ib3JkZXJUb3A7XHJcbiAgICB9XHJcblxyXG5cclxuICAgIC8qKlxyXG4gICAgICogY29uc3RydWN0b3JcclxuICAgICAqIEBwYXJhbSBlbGVtXHJcbiAgICAgKiBAcGFyYW0gY29tbW9uU2VydmljZVxyXG4gICAgICovXHJcbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIGVsZW06IEVsZW1lbnRSZWYsIHByaXZhdGUgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSAsIHByaXZhdGUgY2RyOiBDaGFuZ2VEZXRlY3RvclJlZikge1xyXG4gICAgICAgIHN1cGVyKGVsZW0sY29tbW9uU2VydmljZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gbmdBZnRlclZpZXdDaGVja2VkKCl7XHJcbiAgICAvLyAgICAgLy95b3VyIGNvZGUgdG8gdXBkYXRlIHRoZSBtb2RlbFxyXG4gICAgLy8gICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBcclxuICAgIC8vICAgICAgICAgdGhpcy5jZHIuZGV0ZWN0Q2hhbmdlcygpO1xyXG4gICAgLy8gICAgIH0sIDApO1xyXG4gICAgLy8gIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIG5nT25Jbml0XHJcbiAgICAgKi9cclxuICAgIG5nT25Jbml0KCkge1xyXG4gICAgICAgIHN1cGVyLm5nT25Jbml0KCk7XHJcbiAgICAgICAgdGhpcy50YWJDaGlsZHJlbkFycmF5ID1bXTtcclxuICAgICAgICBpZighdGhpcy5pZCkgdGhpcy5pZCA9ICBcImR5bmFtaWMtXCIgKyBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgNSkgO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICB0aGlzLm5hdkJhckNvbnRhaW5lciA9ICQoJCgkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jaGlsZHJlbigpWzBdKTtcclxuICAgICAgICAgIHRoaXMubmF2VGFicyA9ICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5uYXYtdGFicycpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAkKCQoJCgkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNoaWxkcmVuKClbMF0pLmZpbmQoJy5idG4tZ3JvdXAnKSkuZmluZCgnI3Rlc3QtZHJvcGRvd24tYnRuJykpLmNsaWNrKCgpPT57XHJcbiAgICAgICAgICAgICAgJCgkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVswXSkuZmluZCgnLmJ0bi1ncm91cCcpKS5maW5kKCcuZHJvcGRvd24tbWVudScpLnRvZ2dsZSgpO1xyXG4gICAgICAgICAgICAgIGNvbnN0IGlzVmlzaWJsZSA9ICQoJCgkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNoaWxkcmVuKClbMF0pLmZpbmQoJy5idG4tZ3JvdXAnKSkuZmluZCgnLmRyb3Bkb3duLW1lbnUnKS5pcygnOnZpc2libGUnKTtcclxuICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgaWYoaXNWaXNpYmxlKXtcclxuICAgICAgICAgICAgICAgICAgICAgICQoXCJib2R5XCIpLm9uKFwiY2xpY2sudGFiTmF2aWdhdG9yXCIrdGhpcy5pZCwgKGUpICA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgJChcImJvZHlcIikub2ZmKFwiY2xpY2sudGFiTmF2aWdhdG9yXCIrdGhpcy5pZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVswXSkuZmluZCgnLmJ0bi1ncm91cCcpKS5maW5kKCcuZHJvcGRvd24tbWVudScpLnRvZ2dsZShmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgIH1lbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgJCgkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVswXSkuZmluZCgnLmJ0bi1ncm91cCcpKS5maW5kKCcuZHJvcGRvd24tbWVudScpLnRvZ2dsZSh0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB9LCAwKTtcclxuICAgICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBuZ0FmdGVyVmlld0luaXRcclxuICAgICAqL1xyXG4gICAgbmdBZnRlclZpZXdJbml0KCl7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICBsZXQgaW50ZXJ2YWxJZCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICAgICAgICAgIGlmKHRoaXMudGFiQ2hpbGRyZW5BcnJheSAmJiB0aGlzLnRhYkNoaWxkcmVuQXJyYXkubGVuZ3RoID4gMCl7XHJcbiAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbElkKTtcclxuICAgICAgICAgICAgICAgICAgIGxldCBpbmRleE9mQWN0aXZlPSB0aGlzLnRhYkNoaWxkcmVuQXJyYXkuZmluZEluZGV4KHg9Pih4LmFjdGl2ZSA9PSB0cnVlICYmIHgudmlzaWJsZSkpO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmKGluZGV4T2ZBY3RpdmUgPT0gLTEgJiYgdGhpcy50YWJDaGlsZHJlbkFycmF5Lmxlbmd0aCA+IDAgKXtcclxuICAgICAgICAgICAgICAgICAgICAgICB2YXIgYXJyPXRoaXMudGFiQ2hpbGRyZW5BcnJheS5maWx0ZXIoeD0+eFsndmlzaWJsZSddPT0gdHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgYXJyLmZvckVhY2goZnVuY3Rpb24gKHRhYjogVGFiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YWIuYWN0aXZlID1mYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYi5kaXNwbGF5ID1mYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgIGlmKGFyciAmJiBhcnIubGVuZ3RoPjApIGFyclswXS5hY3RpdmUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSwgMTAwMClcclxuXHJcbiAgICAgICAgICAgdGhpcy5uYXZCYXJDb250YWluZXIgPSAkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVswXSk7XHJcbiAgICAgICAgICAgdGhpcy5uYXZUYWJzID0gJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLm5hdi10YWJzJyk7XHJcbiAgICAgICAgICAgdGhpcy5uYXZUYWJzTGk9JCh0aGlzLm5hdlRhYnMpLmZpbmQoJ2xpJyk7XHJcbiAgICAgICAgICAgXHJcbiAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICdoaWRkZW4nKTtcclxuICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd3aWR0aCcsICcwcHgnKTtcclxuICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCdtYXJnaW4tbGVmdCcsICcwcHgnKTsgXHJcblxyXG4gICAgICAgICAgIHRoaXMuc2Nyb2xsV2lkdGggPSBOdW1iZXIoJCh0aGlzLm5hdlRhYnMpWzBdLnNjcm9sbFdpZHRoKTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgIHRoaXMuY2hlY2tWYWx1ZSgpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtuZ0FmdGVyVmlld0luaXRdIC0gZXJyb3I6JyxlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuXHJcbiAgICAvKipcclxuICAgICAqIHNldFNlbGVjdGVkVGFiIDogaGFuZGxlIGFjdGl2ZS9pbmFjdGl2ZSB0YWJcclxuICAgICAqIEBwYXJhbSBldmVudFxyXG4gICAgICogQHBhcmFtIGxhYmVsXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBzZXRTZWxlY3RlZFRhYih0YWIpIHtcclxuICAgICAgICB0cnl7XHJcbiAgICAgICAgICBpZih0YWIgJiYgdGFiLmlkICE9IHRoaXMuc2VsZWN0ZWRUYWIuaWQpe1xyXG4gICAgICAgICAgICAgICAgdGhpcy50YWJDaGlsZHJlbkFycmF5LmZvckVhY2goZnVuY3Rpb24gKHN3dHRhYjogVGFiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYodGFiICE9IHN3dHRhYil7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN3dHRhYi5hY3RpdmUgPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzd3R0YWIuZGlzcGxheSA9ZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgfWVsc2V7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRhYi5hY3RpdmU9dHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGFiLmRpc3BsYXk9dHJ1ZTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgICBUYWJTZWxlY3RFdmVudC5lbWl0KHRhYik7XHJcbiAgICAgICAgICAgICAgICBsZXQgaW5kZXg9IHRoaXMudGFiQ2hpbGRyZW5BcnJheS5maW5kSW5kZXgoeD0+eC5pZCA9PSB0YWIuaWQpO1xyXG5cclxuICAgICAgICAgICAgICAgIGlmKCB0aGlzLnNlbGVjdGVkSW5kZXggICE9IGluZGV4ICApe1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kZXggPSAgaW5kZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRMYWJlbCA9IHRoaXMudGFiQ2hpbGRyZW5BcnJheVt0aGlzLnNlbGVjdGVkSW5kZXhdLmxhYmVsO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkVGFiPXRoaXMudGFiQ2hpbGRyZW5BcnJheVt0aGlzLnNlbGVjdGVkSW5kZXhdO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMub25DaGFuZ2VfLmVtaXQoKTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9vbkNoYW5nZSgpO1xyXG4gICAgICAgICAgICAgICAgfWVsc2V7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRleCA9ICBpbmRleDtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZFRhYj10aGlzLnRhYkNoaWxkcmVuQXJyYXlbdGhpcy5zZWxlY3RlZEluZGV4XTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZExhYmVsID0gdGhpcy50YWJDaGlsZHJlbkFycmF5W3RoaXMuc2VsZWN0ZWRJbmRleF0ubGFiZWw7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtzZXRTZWxlY3RlZFRhYl0gZXJyb3IgOicsZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0U2VsZWN0ZWRUYWIoKSB7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICBpZiggdGhpcy5zZWxlY3RlZFRhYiApe1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRUYWI7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtnZXRTZWxlY3RlZFRhYl0gZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogYWRkQ2hpbGQgOiBhZGRzIGEgY2hpbGQgdGFiIGR5bmFtaWNhbGx5XHJcbiAgICAgKiBAcGFyYW0gdHlwZVxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgYWRkQ2hpbGQodHlwZSl7XHJcblxyXG4gICAgICAgIHRyeXtcclxuICAgICAgICAgICAgdmFyIGNvbXAgPSB0aGlzLmNvbW1vblNlcnZpY2UuY29tcG9uZW50RmFjdG9yeVJlc29sdmVyLnJlc29sdmVDb21wb25lbnRGYWN0b3J5KHR5cGUpO1xyXG4gICAgICAgICAgICB2YXIgdGFiID0gbmV3IFRhYih0aGlzLmVsZW0sdGhpcy5jb21tb25TZXJ2aWNlKTtcclxuXHJcbiAgICAgICAgICAgIHZhciAgY29tcG9uZW50SW5zdGFuY2UgPSB0aGlzLl9jb250YWluZXIuY3JlYXRlQ29tcG9uZW50KGNvbXApO1xyXG4gICAgICAgICAgICAvKiBQdXNoIHRoZSBjb21wb25lbnQgc28gdGhhdCB3ZSBjYW4ga2VlcCB0cmFjayBvZiB3aGljaCBjb21wb25lbnRzIGFyZSBjcmVhdGVkICovXHJcbiAgICAgICAgICAgIHRoaXMuY29tcG9uZW50cy5wdXNoKGNvbXBvbmVudEluc3RhbmNlKTtcclxuICAgICAgICAgICAgbGV0ICAgaWQgPVwiZHluYW1pYy1cIitNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgNSkgO1xyXG4gICAgICAgICAgICAkKCQoJChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZSlbMF0pWzBdLmVsZW0ubmF0aXZlRWxlbWVudCkuYXR0cignaWQnLGlkKTtcclxuXHJcbiAgICAgICAgICAgIHRhYiA9IDxUYWI+IGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlO1xyXG4gICAgICAgICAgICB0YWIuaWQ9IGlkO1xyXG4gICAgICAgICAgICB0YWIucGFyZW50ID0gdGhpcztcclxuICAgICAgICAgICAgdGFiLmRpc3BsYXk9ZmFsc2U7XHJcblxyXG4gICAgICAgICAgICAgdGhpcy50YWJDaGlsZHJlbkFycmF5LnB1c2godGFiKTtcclxuICAgICAgICAgICAgdGhpcy5jaGVja1ZhbHVlKCk7XHJcbiAgICAgICAgICAgIHJldHVybiAoIHRhYik7XHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFthZGRDaGlsZF0gZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBhZGRDaGlsZCA6IGFkZHMgYSBjaGlsZCB0YWIgZHluYW1pY2FsbHlcclxuICAgICAqIEBwYXJhbSB0eXBlXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBhZGRDaGlsZFB1c2hTdGF0ZWd5KHR5cGUpe1xyXG5cclxuICAgICAgICB0cnl7XHJcbiAgICAgICAgICAgIHZhciBjb21wID0gdGhpcy5jb21tb25TZXJ2aWNlLmNvbXBvbmVudEZhY3RvcnlSZXNvbHZlci5yZXNvbHZlQ29tcG9uZW50RmFjdG9yeSh0eXBlKTtcclxuICAgICAgICAgICAgdmFyIHRhYiA9IG5ldyBUYWJQdXNoU3RhdGVneSh0aGlzLmVsZW0sdGhpcy5jb21tb25TZXJ2aWNlLHRoaXMuY2RyKTtcclxuXHJcbiAgICAgICAgICAgIHZhciAgY29tcG9uZW50SW5zdGFuY2UgPSB0aGlzLl9jb250YWluZXIuY3JlYXRlQ29tcG9uZW50KGNvbXApO1xyXG4gICAgICAgICAgICAvKiBQdXNoIHRoZSBjb21wb25lbnQgc28gdGhhdCB3ZSBjYW4ga2VlcCB0cmFjayBvZiB3aGljaCBjb21wb25lbnRzIGFyZSBjcmVhdGVkICovXHJcbiAgICAgICAgICAgIHRoaXMuY29tcG9uZW50cy5wdXNoKGNvbXBvbmVudEluc3RhbmNlKTtcclxuICAgICAgICAgICAgdmFyIGlkID1cImR5bmFtaWMtXCIrTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDUpIDtcclxuICAgICAgICAgICAgJCgkKCQoY29tcG9uZW50SW5zdGFuY2UuaW5zdGFuY2UpWzBdKVswXS5lbGVtLm5hdGl2ZUVsZW1lbnQpLmF0dHIoJ2lkJyxpZCk7XHJcblxyXG4gICAgICAgICAgICB0YWIgPSA8VGFiUHVzaFN0YXRlZ3k+IGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlO1xyXG4gICAgICAgICAgICB0YWIuaWQ9IGlkO1xyXG4gICAgICAgICAgICB0YWIucGFyZW50ID0gdGhpcztcclxuICAgICAgICAgICAgdGFiLmRpc3BsYXk9ZmFsc2U7XHJcbiAgICAgICAgICAgICB0aGlzLnRhYkNoaWxkcmVuQXJyYXkucHVzaCh0YWIpO1xyXG4gICAgICAgICAgICAgdGhpcy5jaGVja1ZhbHVlKCk7XHJcbiAgICAgICAgICAgIHJldHVybiAoIHRhYik7XHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFthZGRDaGlsZF0gZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogcmVtb3ZlQ2hpbGQgOiByZW1vdmVzIGEgdGFiIGR5bmFtaWNhbGx5XHJcbiAgICAgKiBAcGFyYW0gY29tcG9uZW50Q2xhc3NcclxuICAgICAqL1xyXG4gICAgcHVibGljIHJlbW92ZUNoaWxkKGNvbXBvbmVudENsYXNzKSB7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICB2YXIgaW5kZXggPSB0aGlzLnRhYkNoaWxkcmVuQXJyYXkuZmluZEluZGV4KHg9PnggPT0gY29tcG9uZW50Q2xhc3MgKTtcclxuICAgICAgICAgICAgLy8gY29uc3QgY29tcG9uZW50ID0gdGhpcy5jb21wb25lbnRzLmZpbmQoKGNvbXBvbmVudCkgPT4gY29tcG9uZW50Lmluc3RhbmNlLmlkID09IGNvbXBvbmVudENsYXNzLmlkKTtcclxuICAgICAgICAgICAgaWYgKGluZGV4ICE9PSAtMSl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdLmRpc3BsYXkgPWZhbHNlO1xyXG4gICAgICAgICAgICAgICAgdGhpcy50YWJDaGlsZHJlbkFycmF5W2luZGV4XS5hY3RpdmUgPWZhbHNlO1xyXG4gICAgICAgICAgICAvLyAgIGlmKGNvbXBvbmVudClcclxuICAgICAgICAgICAgLy8gICAgIHRoaXMuX2NvbnRhaW5lci5yZW1vdmUodGhpcy5fY29udGFpbmVyLmluZGV4T2YoY29tcG9uZW50KSk7XHJcbiAgICAgICAgICAgICAgICBzdXBlci5yZW1vdmVDaGlsZChjb21wb25lbnRDbGFzcyk7XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgdGhpcy50YWJDaGlsZHJlbkFycmF5LnNwbGljZShpbmRleCwgMSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNvbXBvbmVudHMuc3BsaWNlKGluZGV4LCAxKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gJChcIiNcIisgJC5lc2NhcGVTZWxlY3Rvcihjb21wb25lbnRDbGFzcy5pZCkpLnJlbW92ZSgpO1xyXG5cclxuICAgICAgICB9Y2F0Y2goZXJyb3Ipe1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtZXRob2QgW3JlbW92ZUNoaWxkXSBlcnJvciA6JyxlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuXHJcbiAgICAvKipcclxuICAgICAqIGdldENoaWxkQXQgOiByZXR1cm5zIHRoZSBpZCBvZiBhIGNoaWxkIFRhYiBpbiBhIHNwZWNpZmljIGluZGV4LlxyXG4gICAgICogQHBhcmFtIGluZGV4XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBnZXRDaGlsZEF0KGluZGV4Om51bWJlcil7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICBpZihpbmRleCA8IHRoaXMudGFiQ2hpbGRyZW5BcnJheS5sZW5ndGggKXtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnRhYkNoaWxkcmVuQXJyYXlbaW5kZXhdO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgIH1jYXRjaChlcnJvcil7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ21ldGhvZCBbZ2V0Q2hpbGRBdF0gZXJyb3IgOicsZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIGdldFRhYkNoaWxkcmVuIDogcmV0dXJucyB0YWJOYXZpZ2F0b3IncyBjaGlsZHJlblxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgZ2V0VGFiQ2hpbGRyZW4oKXtcclxuICAgICAgICByZXR1cm4gdGhpcy50YWJDaGlsZHJlbkFycmF5O1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogbmdPbkRlc3Ryb3lcclxuICAgICAqL1xyXG4gICAgbmdPbkRlc3Ryb3koKTogdm9pZCB7XHJcbiAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fc2VsZWN0ZWRJbmRleDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX3NlbGVjdGVkTGFiZWw7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9zZWxlY3RlZFRhYjtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuc2VsZWN0ZWRUYWJJZDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX2JvcmRlckJvdHRvbTtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX2JvcmRlclRvcDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMudGFiQ2hpbGRyZW5BcnJheTtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX29uQ2hhbmdlO1xyXG4gICAgICAgIH1jYXRjaChlcnJvciApe1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtZXRob2QgW25nT25EZXN0cm95XSBlcnJvciA6JyxlcnJvcilcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBzZXRVbmRvY2tQb2xpY3lGb3JUYWJcclxuICAgICAqIFxyXG4gICAgICogQHBhcmFtIGluZGV4OiBudW1iZXJcclxuICAgICAqIFxyXG4gICAgICogQHBhcmFtIHZhbHVlOiBTdHJpbmdcclxuICAgICAqIFxyXG4gICAgICogTWV0aG9kIHRvIHNldCB1bmRvayBwb2xpY3kgZm9yIHRhYlxyXG4gICAgICovXHJcbiAgICAgcHVibGljIHNldFVuZG9ja1BvbGljeUZvclRhYihpbmRleDogbnVtYmVyLCB2YWx1ZTogc3RyaW5nKTogdm9pZCB7XHJcbiAgICAgICAgIFxyXG4gICAgICAgICB2YXIgdGFiID0gbmV3IFRhYih0aGlzLmVsZW0sdGhpcy5jb21tb25TZXJ2aWNlKTtcclxuICAgICAgICAgdGFiLnNldFVuZG9ja1BvbGljeShpbmRleCwgdmFsdWUpO1xyXG4gICAgICAgIFxyXG4gICAgfVxyXG4gICAgIFxyXG4gICAgIC8vLUZpeCBNNDg2OS9JU1MtMDA4LlxyXG4gICAgIHB1YmxpYyBvbk1vdXNlZG93bih0YWIsIGV2ZW50KXtcclxuICAgICAgICAgdHJ5e1xyXG4gICAgICAgICAgICAgICAgIGV2ZW50WydfY3VycmVudFRhYiddPXRoaXMuc2VsZWN0ZWRUYWI7XHJcbiAgICAgICAgICAgICAgICAgZXZlbnRbJ190YXJnZXQnXT10YWI7XHJcbiAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgaWYodGFiICYmIHRoaXMuTU9VU0VfRE9XTi5uYW1lIT0gJ2Fub255bW91cycgKXtcclxuICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgIHZhciByZXN1bHQgPSB0aGlzLk1PVVNFX0RPV04oZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgICAgICBpZiggcmVzdWx0IClcclxuICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0U2VsZWN0ZWRUYWIodGFiKTtcclxuICAgICAgICAgICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRTZWxlY3RlZFRhYih0YWIpO1xyXG4gICAgICAgICAgICAgICAgIH0gXHJcbiAgICAgICAgICAgICAgICAgdGhpcy5jaGVja1ZhbHVlKCk7XHJcblxyXG4gICAgICAgICB9Y2F0Y2goZXJyb3Ipe1xyXG4gICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtvbk1vdXNlZG93bl0gLSBlcnJvciA6JyxlcnJvcik7XHJcbiAgICAgICAgIH1cclxuICAgICAgIFxyXG4gICAgIH1cclxuICAgICAgICAgXHJcbiAgICAgICAgIFxyXG4gICAgIC8vLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBcclxuICAgICAgIHNjcm9sbFRhYnMoc2lkZTogc3RyaW5nKSB7XHJcbiAgICAgICAgIHRoaXMuZGlzcG9zZSgpO1xyXG4gICAgICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICAgICAgIGlmIChzaWRlID09PSAncmlnaHQnKSB7XHJcbiAgICAgICAgICAgICBpZiAodGhpcy5zY3JvbGxWYWx1ZSArIDEwIDwgdGhpcy5tYXhTY3JvbGwpIHtcclxuICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxWYWx1ZSArPSAxMDtcclxuICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgIHRoaXMuc2Nyb2xsVmFsdWUgPSB0aGlzLm1heFNjcm9sbDtcclxuICAgICAgICAgICAgICAgICB9XHJcbiAgICBcclxuICAgICAgICAgICAgICAgfSBlbHNlIGlmIChzaWRlID09PSAnbGVmdCcpIHtcclxuICAgICAgICAgICAgICAgICBpZiAodGhpcy5zY3JvbGxWYWx1ZSAtIDEwID4gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxWYWx1ZSAtPSAxMDtcclxuICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxWYWx1ZSA9IDA7XHJcbiAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICQoJCh0aGlzLm5hdlRhYnMpWzBdKS5zY3JvbGxMZWZ0KHRoaXMuc2Nyb2xsVmFsdWUpO1xyXG4gICAgIFxyXG4gICAgICAgICAgICAgICBpZiAodGhpcy5zY3JvbGxWYWx1ZSA9PSB0aGlzLm1heFNjcm9sbCkge1xyXG4gICAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICd2aXNpYmxlJyk7XHJcbiAgICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd3aWR0aCcsICcyMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCdtYXJnaW4tbGVmdCcsICcycHgnKTtcclxuICAgICAgICAgICAgICAgICAgICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5yaWdodEFycm93JykuY3NzKCd2aXNpYmlsaXR5JywgJ2hpZGRlbicpO1xyXG4gICAgICAgICAgICAgICAgICAgdGhpcy5kaXNwb3NlKCk7XHJcbn1cclxuICAgICAgICAgICAgICAgaWYgKHRoaXMuc2Nyb2xsVmFsdWUgPT0gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICdoaWRkZW4nKTtcclxuICAgICAgICAgICAgICAgICAgICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5sZWZ0QXJyb3cnKS5jc3MoJ3dpZHRoJywgJzBweCcpO1xyXG4gICAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygnbWFyZ2luLWxlZnQnLCAnMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcucmlnaHRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICd2aXNpYmxlJyk7XHJcbiAgICAgICAgICAgICAgICAgICB0aGlzLmRpc3Bvc2UoKTtcclxuICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICBpZiAodGhpcy5zY3JvbGxWYWx1ZSAhPT0gMCAmJiB0aGlzLnNjcm9sbFZhbHVlICE9PSB0aGlzLm1heFNjcm9sbCkge1xyXG4gICAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICd2aXNpYmxlJyk7XHJcbiAgICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd3aWR0aCcsICcyMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCdtYXJnaW4tbGVmdCcsICcycHgnKTtcclxuICAgICAgICAgICAgICAgICAgICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5yaWdodEFycm93JykuY3NzKCd2aXNpYmlsaXR5JywgJ3Zpc2libGUnKTtcclxuICAgICAgICAgICAgICAgfVxyXG4gICAgXHJcbiAgICAgICAgICAgICAgIHRoaXMuY2hlY2tWYWx1ZSgpO1xyXG4gICAgXHJcbiAgICAgICAgIH0sIDIwKTtcclxuICAgICAgIH1cclxuICAgIFxyXG4gICAgICAgZGlzcG9zZSgpIHtcclxuICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTtcclxuICAgICAgIH1cclxuICAgICAgIFxyXG4gICAgICAgXHJcbiAgICAgICByZW1vdmVDb250ZW50VGFiKHRhYjogVGFiKSB7XHJcbiAgICAgICAgaWYodGFiLmVuYWJsZWQpe1xyXG4gICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMudGFiQ2hpbGRyZW5BcnJheS5pbmRleE9mKHRhYik7XHJcbiAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlQ2hpbGQodGFiKTtcclxuICAgICAgICAgICAgICAgaWYodGhpcy5zZWxlY3RlZEluZGV4ID09IGluZGV4KXtcclxuICAgICAgICAgICAgICAgICAgIGlmKHRoaXMudGFiQ2hpbGRyZW5BcnJheS5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgZm9yICh2YXIgaiA9ICgoaW5kZXggPCB0aGlzLnRhYkNoaWxkcmVuQXJyYXkubGVuZ3RoKSA/IGluZGV4IDogaW5kZXgtMSk7IGogPiAwOyBqLS0pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYodGhpcy50YWJDaGlsZHJlbkFycmF5W2pdLmVuYWJsZWQpIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRUYWI9dGhpcy50YWJDaGlsZHJlbkFycmF5W2ogXTtcclxuICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9uTW91c2Vkb3duKHRoaXMudGFiQ2hpbGRyZW5BcnJheVtqIF0sIGV2ZW50KTtcclxuICAgICAgICAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgfSBcclxuICAgICAgICAgICAgICAgfSBcclxuICAgICAgICAgICB0aGlzLmNoZWNrVmFsdWUoKTtcclxuICAgICAgICAgICBUYWJDbG9zZUV2ZW50LmVtaXQodGFiLmlkKTtcclxuICAgfVxyXG4gICAgICAgfVxyXG4gICAgICAgXHJcbiAgICAgICBzY3JvbGxUb1RhYkZyb21Db21ibyh0YWIpe1xyXG4gICAgICAgICBjb25zdCBvZmZzZXRMZWZ0ID0gTnVtYmVyKCQodGhpcy5uYXZUYWJzTGkpW3RoaXMudGFiQ2hpbGRyZW5BcnJheS5pbmRleE9mKHRhYildLm9mZnNldExlZnQpO1xyXG4gICAgICAgICBjb25zdCBvZmZzZXRXaWR0aCA9IE51bWJlcigkKHRoaXMubmF2VGFic0xpKVt0aGlzLnRhYkNoaWxkcmVuQXJyYXkuaW5kZXhPZih0YWIpXS5vZmZzZXRXaWR0aCk7XHJcbiAgICAgICAgIGNvbnN0IHNjcm9sbFRvVGFiID0gKG9mZnNldExlZnQgLSBOdW1iZXIodGhpcy5zY3JvbGxXaWR0aCkpICsgb2Zmc2V0V2lkdGg7XHJcbiAgICAgICAgIHRoaXMuc2Nyb2xsVmFsdWUgPSBzY3JvbGxUb1RhYjtcclxuICAgICAgICAgXHJcbiAgICAgICAgIHRoaXMuc2VsZWN0ZWRUYWI9dGFiO1xyXG4gICAgICAgICBcclxuICAgICAgICAgaWYgKHRoaXMuY2xpZW50V2lkdGggPiB0aGlzLnNjcm9sbFdpZHRoKSB7XHJcbiAgICAgICAgICAgdGhpcy50aW1lciA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICAgICAgICQoJCh0aGlzLm5hdlRhYnMpWzBdKS5zY3JvbGxMZWZ0KCB0aGlzLnNjcm9sbFZhbHVlKTtcclxuICAgICAgICAgICAgIGlmIChvZmZzZXRMZWZ0ICA8IHRoaXMuc2Nyb2xsV2lkdGggICkge1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuc2Nyb2xsVmFsdWUgPSAwO1xyXG4gICAgICAgICAgICAgICAgICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5sZWZ0QXJyb3cnKS5jc3MoJ3Zpc2liaWxpdHknLCAnaGlkZGVuJyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygnd2lkdGgnLCAnMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygnbWFyZ2luLWxlZnQnLCAnMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLnJpZ2h0QXJyb3cnKS5jc3MoJ3Zpc2liaWxpdHknLCAndmlzaWJsZScpO1xyXG4gICAgICAgICAgICAgICAgIHRoaXMuZGlzcG9zZSgpO1xyXG4gICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgaWYgKChvZmZzZXRMZWZ0ICsgb2Zmc2V0V2lkdGgpID09IHRoaXMuc2Nyb2xsV2lkdGgpIHtcclxuICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd2aXNpYmlsaXR5JywgJ3Zpc2libGUnKTtcclxuICAgICAgICAgICAgICAgICAkKHRoaXMubmF2QmFyQ29udGFpbmVyKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd3aWR0aCcsICcyMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmxlZnRBcnJvdycpLmNzcygnbWFyZ2luLWxlZnQnLCAnMnB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLnJpZ2h0QXJyb3cnKS5jc3MoJ3Zpc2liaWxpdHknLCAnaGlkZGVuJyk7XHJcbiAgICAgICAgICAgICAgICAgdGhpcy5kaXNwb3NlKCk7XHJcbiAgICAgICAgICAgICB9ICAgICAgICAgICBcclxuICAgICAgICAgICAgIGlmIChvZmZzZXRMZWZ0ID4gMjMgJiYgKG9mZnNldExlZnQgKyBvZmZzZXRXaWR0aCkgPiB0aGlzLnNjcm9sbFdpZHRoKSB7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdlRhYnMpLmZpbmQoJy5sZWZ0QXJyb3cnKS5jc3MoJ3Zpc2liaWxpdHknLCAndmlzaWJsZScpO1xyXG4gICAgICAgICAgICAgICAgICQodGhpcy5uYXZUYWJzKS5maW5kKCcubGVmdEFycm93JykuY3NzKCd3aWR0aCcsICcyMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgJCh0aGlzLm5hdlRhYnMpLmZpbmQoJy5sZWZ0QXJyb3cnKS5jc3MoJ21hcmdpbi1sZWZ0JywgJzJweCcpO1xyXG4gICAgICAgICAgICAgICAgICQodGhpcy5uYXZUYWJzKS5maW5kKCcucmlnaHRBcnJvdycpLmNzcygndmlzaWJpbGl0eScsICd2aXNpYmxlJyk7XHJcbiAgICAgICAgICAgICAgICAgdGhpcy5kaXNwb3NlKCk7XHJcbiAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgfSwgMjApO1xyXG4gICAgICAgICB9XHJcbiAgICAgICAgICQoJCh0aGlzLm5hdkJhckNvbnRhaW5lcikuZmluZCgnLmJ0bi1ncm91cCcpKS5maW5kKCcuZHJvcGRvd24tbWVudScpLnRvZ2dsZSgpO1xyXG4gICAgICAgICBcclxuICAgICAgIH1cclxuICAgXHJcbiAgICAgICBjaGVja1ZhbHVlKCl7XHJcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAgdGhpcy5uYXZCYXJDb250YWluZXIgPSAkKCQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKVswXSk7XHJcbiAgICAgICAgICAgICAgIHRoaXMubmF2VGFicyA9ICQodGhpcy5uYXZCYXJDb250YWluZXIpLmZpbmQoJy5uYXYtdGFicycpO1xyXG4gICAgICAgICAgICAgICB0aGlzLm5hdlRhYnNMaT0kKHRoaXMubmF2VGFicykuZmluZCgnbGknKTtcclxuICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgIGlmICgkKHRoaXMubmF2VGFicylbMF0gIT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICB2YXIgc2Nyb2xsV2lkdGggPSBOdW1iZXIoJCh0aGlzLm5hdlRhYnMpWzBdLnNjcm9sbFdpZHRoKTtcclxuICAgICAgICAgICAgICAgICAgIGlmICgkKHRoaXMubmF2VGFic0xpKVt0aGlzLnRhYkNoaWxkcmVuQXJyYXkubGVuZ3RoIC0gMV0gIT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGllbnRXaWR0aCA9IE51bWJlcigkKCQodGhpcy5uYXZUYWJzTGkpW3RoaXMudGFiQ2hpbGRyZW5BcnJheS5sZW5ndGggLSAxXSlbMF0ub2Zmc2V0TGVmdCkgK1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgTnVtYmVyKCQoJCh0aGlzLm5hdlRhYnNMaSlbdGhpcy50YWJDaGlsZHJlbkFycmF5Lmxlbmd0aCAtIDFdKVswXS5vZmZzZXRXaWR0aCk7XHJcbiAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICB0aGlzLm1heFNjcm9sbCA9IE51bWJlcigkKHRoaXMubmF2VGFicylbMF0uc2Nyb2xsV2lkdGgpIC0gTnVtYmVyKCQodGhpcy5uYXZUYWJzKVswXS5jbGllbnRXaWR0aCk7XHJcbiAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sIDApO1xyXG4gICAgICAgfVxyXG4gICBcclxuICB9XHJcblxyXG5cclxuIl19