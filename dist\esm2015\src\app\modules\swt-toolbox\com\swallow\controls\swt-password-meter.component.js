/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild, Input } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
/** @type {?} */
const $ = require('jquery');
/**
 * SwtPasswordMeter Component
 *
 * <pre>
 * The Password Meter control is used to validate the password strength.
 *
 * It categorizes the password strength as,
 *  - Very weak (20%)
 *  - Weak (40%)
 *  - Medium (60%)
 *  - Strong (80%)
 *  - Very strong (100%)
 * </pre>
 *
 * <AUTHOR> Chiheb 23-Nov-2018
 */
export class SwtPasswordMeter {
    constructor() {
        this._label = "";
        this._includeInLayout = false;
        this._visible = true;
        this.includeInlayout = false;
        //Target text input field, to validate the strength
        this._target = null;
        //callback function after calculate the password strength
        this._labelFunction = null;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this._labelFunction = this.getStrengthLabel;
    }
    /**
     * This function calculate the strength of the target input field value
     * @private
     * @return {?}
     */
    calcuateStrength() {
        //Password
        /** @type {?} */
        var password = this._target.text;
        //Default value
        this._strength = 0;
        if (password.length > 0) {
            //Minimum unique spl characters
            /** @type {?} */
            var uniqueSplChars = 2;
            //Minimum alphabets
            /** @type {?} */
            var uniqueAlpha = 4;
            //Mininum numbers
            /** @type {?} */
            var uniqueNumber = 2;
            //Password valid flag, check the password against password rule
            /** @type {?} */
            var validFlag = true;
            //Special characters count
            /** @type {?} */
            var splCharCount = 0;
            //lower case characters count
            /** @type {?} */
            var lowerCount = 0;
            //upper case characters count
            /** @type {?} */
            var upperCount = 0;
            //numbers count
            /** @type {?} */
            var numCount = 0;
            //Unique special characters count
            /** @type {?} */
            var splCharUniqueCount = 0;
            //Unique lower case characters count
            /** @type {?} */
            var lowerUniqueCount = 0;
            //Unique upper case characters count
            /** @type {?} */
            var upperUniqueCount = 0;
            //Unique numbers count
            /** @type {?} */
            var numUniqueCount = 0;
            //Temporary variable to hold unique chars
            /** @type {?} */
            var uniqueChar = "";
            //Temporary variable to add unique chars
            /** @type {?} */
            var uniqueFlag;
            //Special characters
            /** @type {?} */
            var splChar = "~#!@$%^&*()-_=+[]:;'\",<.>/?";
            //Numbers
            /** @type {?} */
            var numbers = "0123456789";
            //Lower case characters
            /** @type {?} */
            var lowerChars = "abcdefghijklmnopqrstuvwxyz";
            //Upper case characters
            /** @type {?} */
            var upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            //Validate password strength
            for (var i = 0; i < password.length; i++) {
                /** @type {?} */
                var char = password.charAt(i);
                uniqueFlag = false;
                if (splChar.indexOf(char) >= 0) {
                    splCharCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        splCharUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (lowerChars.indexOf(char) >= 0) {
                    lowerCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        lowerUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (upperChars.indexOf(char) >= 0) {
                    upperCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        upperUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                else if (numbers.indexOf(char) >= 0) {
                    numCount++;
                    if (uniqueChar.indexOf(char) == -1) {
                        numUniqueCount++;
                        uniqueFlag = true;
                    }
                }
                if (uniqueFlag) {
                    uniqueChar += char;
                }
            }
            //Check password length
            if (password.length < this._minLength) {
                validFlag = false;
            }
            //Check Special characters count
            if (splCharCount < this._minSplChar) {
                validFlag = false;
            }
            //Check alphabets count
            if ((upperCount + lowerCount) < this._minAlpha) {
                validFlag = false;
            }
            //Check mixed case count
            if (this._mixedCase) {
                if (upperCount == 0 || lowerCount == 0) {
                    validFlag = false;
                }
            }
            //Check numbers count
            if (numCount < this._minNumber) {
                validFlag = false;
            }
            //If the password validation is success against the rule, 
            //then calculate its strength 
            if (validFlag) {
                //Check password minimum length
                if (password.length >= (uniqueSplChars + uniqueAlpha + uniqueNumber)) {
                    this._strength = this._strength + 20;
                }
                //Check unique special characters count
                if (splCharUniqueCount >= uniqueSplChars) {
                    this._strength = this._strength + 20;
                }
                //Check unique alphabets count
                if ((upperUniqueCount + lowerUniqueCount) >= uniqueAlpha) {
                    this._strength = this._strength + 20;
                }
                //Check unique mixed case count
                if (upperUniqueCount > 0 && lowerUniqueCount > 0) {
                    this._strength = this._strength + 20;
                }
                //Check unique numbers count
                if (numUniqueCount >= uniqueNumber) {
                    this._strength = this._strength + 20;
                }
                //Very weak password
                if (this._strength == 0) {
                    this._strength = 20;
                }
            }
            else {
                //Invalid password
                this._strength = -1;
            }
        }
        if (this._strength == -1) {
            //Set bar color for invalid password
            this.setStyle("background-color", "#FF0000");
            //update progress 
            this.setProgress(100, 100);
        }
        else {
            //Set bar color
            this.setStyle("background-color", "#1FA9FF");
            //update progress 
            this.setProgress(this._strength, 100);
        }
    }
    /**
     * This method is used to set style property to
     * SwtPasswordMetter.
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    setStyle(attribute, value) {
        $(this.passMetterObject.nativeElement.children[0]).css(attribute, value);
    }
    /**
     * @param {?} value
     * @param {?} total
     * @return {?}
     */
    setProgress(value, total) {
        $(this.passMetterObject.nativeElement.children[0]).css("width", value + "%");
    }
    /**
     * This function resets the strength value to clear the progress bar
     * @return {?}
     */
    clearProgress() {
        //reset the value 
        this._strength = 0;
        //Clear progress bar
        this.setProgress(this._strength, 100);
    }
    /**
     * Calculate strength of the password, whenever the value is changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    onValueChange(event) {
        this.calcuateStrength();
        this.label = this._labelFunction(this._strength);
    }
    /**
     * <pre>
     * This function is used to get the strength of the password in word
     *  - Invalid Password:- Validation fail against password rule
     *  - Very weak (20%)
     *  - Weak (40%)
     *  - Medium (60%)
     *  - Strong (80%)
     *  - Very strong (100%)
     * </pre>
     *
     * @private
     * @param {?} strength - password strength value in percentage
     * @return {?} String - password strengh in word
     */
    getStrengthLabel(strength) {
        /** @type {?} */
        var strStrength = "";
        //Get password strength in word based on strength value
        switch (strength) {
            case -1:
                strStrength = "Invalid - Too weak";
                break;
            case 20:
                strStrength = "Very weak";
                break;
            case 40:
                strStrength = "Weak";
                break;
            case 60:
                strStrength = "Medium";
                break;
            case 80:
                strStrength = "Strong";
                break;
            case 100:
                strStrength = "Very strong";
                break;
        }
        //Returns password strength in word
        return strStrength;
    }
    //////////////////////////////////////////////////////////////////////
    //      GETTERS AND SETTERS
    //////////////////////////////////////////////////////////////////////
    /**
     * Setter method of target
     * @param {?} target
     * @return {?}
     */
    set target(target) {
        this._target = target;
        target.keyUp = (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onValueChange(event);
        });
    }
    /**
     * Getter method of target
     * @return {?}
     */
    get target() {
        return this._target;
    }
    /**
     * Getter method of strength
     * @return {?}
     */
    get strength() {
        return this._strength;
    }
    /**
     * Setter method of minLength
     * @param {?} minLength
     * @return {?}
     */
    set minLength(minLength) {
        this._minLength = minLength;
    }
    /**
     * Getter method of minLength
     * @return {?}
     */
    get minLength() {
        return this._minLength;
    }
    /**
     * Setter method of minSplChar
     * @param {?} minSplChar
     * @return {?}
     */
    set minSplChar(minSplChar) {
        this._minSplChar = minSplChar;
    }
    /**
     * Getter method of minSplChar
     * @return {?}
     */
    get minSplChar() {
        return this._minSplChar;
    }
    /**
     * Setter method of minAlpha
     * @param {?} minAlpha
     * @return {?}
     */
    set minAlpha(minAlpha) {
        this._minAlpha = minAlpha;
    }
    /**
     * Getter method of minAlpha
     * @return {?}
     */
    get minAlpha() {
        return this._minAlpha;
    }
    /**
     * Setter method of minNumber
     * @param {?} minNumber
     * @return {?}
     */
    set minNumber(minNumber) {
        this._minNumber = minNumber;
    }
    /**
     * Getter method of minNumber
     * @return {?}
     */
    get minNumber() {
        return this._minNumber;
    }
    /**
     * Setter method of mixedCase
     * @param {?} mixedCase
     * @return {?}
     */
    set mixedCase(mixedCase) {
        this._mixedCase = mixedCase;
    }
    /**
     * Getter method of mixedCase
     * @return {?}
     */
    get mixedCase() {
        return this._mixedCase;
    }
    /**
     * Setter method of labelFunction function
     * @param {?} labelFunction
     * @return {?}
     */
    set labelFunction(labelFunction) {
        this._labelFunction = labelFunction;
    }
    /**
     * Getter method of labelFunction function
     * @return {?}
     */
    get labelFunction() {
        return this._labelFunction;
    }
    /**
     * Setter method of width
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        this._width = value;
        $(this.passMetterObject.nativeElement).width(value).css('width', '-=' + 2 + "px");
    }
    /**
     * Getter method of width
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /**
     * Setter method of height
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = value;
    }
    /**
     * Getter method of height
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /**
     * Setter method of visible
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        this._visible = value;
        if (typeof (value) == "string") {
            if (this.includeInLayout) {
                if (value == "true") {
                    $($(this.passMetterObject.nativeElement)[0]).css("visibility", "visible");
                }
                else {
                    $($(this.passMetterObject.nativeElement)[0]).css("visibility", "hidden");
                }
            }
            else {
                if (value == "true") {
                    $(this.passMetterObject.nativeElement).show();
                }
                else {
                    $(this.passMetterObject.nativeElement).hide();
                }
            }
        }
        else {
            if (this.includeInLayout) {
                if (value) {
                    $($(this.passMetterObject.nativeElement)[0]).css("visibility", "visible");
                }
                else {
                    $($(this.passMetterObject.nativeElement)[0]).css("visibility", "hidden");
                }
            }
            else {
                if (value) {
                    $(this.passMetterObject.nativeElement).show();
                }
                else {
                    $(this.passMetterObject.nativeElement).hide();
                }
            }
        }
    }
    /**
     * Getter method of visible
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    /**
     * Setter method of visible
     * @param {?} value
     * @return {?}
     */
    set includeInLayout(value) {
        this._includeInLayout = value;
    }
    /**
     * Getter method of visible
     * @return {?}
     */
    get includeInLayout() {
        return this._includeInLayout;
    }
    /**
     * Setter method of visible
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        this._label = value;
        $($(this.passMetterObject.nativeElement.children[1])[0]).text(value);
    }
    /**
     * Getter method of visible
     * @return {?}
     */
    get label() {
        return this._label;
    }
}
SwtPasswordMeter.decorators = [
    { type: Component, args: [{
                selector: 'SwtPasswordMeter',
                template: `
    <div #passMetterObject class="password-meter-progressBar">
      <div class="password-meter-progress"></div>
      <span id="label"></span>
    </div>
  `,
                styles: [`
           .password-meter-progressBar {
              width: 100%;
              height: 9px;
              margin-left:5px;
              background-image: -webkit-gradient(linear, left top, left bottom, from(#F5F8FA), to(#A9D2E4));
              background-image: -webkit-linear-gradient(top, #F5F8FA, #A9D2E4);
              background-image: -moz-linear-gradient(top, #F5F8FA, #A9D2E4);
              background-image: -ms-linear-gradient(top, #F5F8FA, #A9D2E4);
              background-image: -o-linear-gradient(top, #F5F8FA, #A9D2E4);
              background-image: linear-gradient(to bottom, #F5F8FA, #A9D2E4);
              border-top: 1px solid #6B6B6B;
              border-left: 1px solid #818181;
              border-right: 1px solid #7D7D7D;
              border-bottom: 1px solid #949494;
            }
            span {
                display: block;
                position: relative;
                top:-9px;
                width:100%;
                text-align: center;
                color: #000;
                font-size: 9px;
                font-weight: bold;
                font-family: verdana,helvetica;
            }
            .password-meter-progress {
              width: 0%;
              height: 100%;
              border-radius: 0px;
            }
  `]
            }] }
];
/** @nocollapse */
SwtPasswordMeter.ctorParameters = () => [];
SwtPasswordMeter.propDecorators = {
    passMetterObject: [{ type: ViewChild, args: ["passMetterObject",] }],
    target: [{ type: Input }],
    labelFunction: [{ type: Input }],
    width: [{ type: Input }],
    height: [{ type: Input }],
    visible: [{ type: Input }],
    includeInLayout: [{ type: Input }],
    label: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._includeInLayout;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype.includeInlayout;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._target;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._strength;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minLength;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minSplChar;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minAlpha;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._minNumber;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._mixedCase;
    /**
     * @type {?}
     * @private
     */
    SwtPasswordMeter.prototype._labelFunction;
    /** @type {?} */
    SwtPasswordMeter.prototype.passMetterObject;
}
//# sourceMappingURL=data:application/json;base64,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