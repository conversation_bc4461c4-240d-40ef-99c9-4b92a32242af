/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { CommonService } from "../utils/common.service";
import { SwtAlert } from "../utils/swt-alert.service";
var SwtMultiselectCombobox = /** @class */ (function () {
    function SwtMultiselectCombobox(elem, commonService) {
        this.elem = elem;
        this.commonService = commonService;
        this.ITEM_SELECT = new EventEmitter();
        this.ITEM_DESELECT = new EventEmitter();
        this.SELECT_ALL = new EventEmitter();
        this.DESELECT_ALL = new EventEmitter();
        this.placeholder = '';
        this.isDropdownDisabled = false;
        this.dataProvider = [];
        this.defaultSelectedItems = [];
        this.selectedItems = [];
        this.dropdownSettings = {};
        this.selects = [];
        this.tooltipValue = [];
        this._visibility = true;
        this._shiftUp = 0;
        this._showAbove = false;
        // Input to store width.
        this.width = "200";
        // Input to store height.
        this.height = "70";
        // Input to store dropDown btn height.
        this.dropHeight = "30";
        // initialize logger.
        this.logger = new Logger('SwtMultiselectCombobox', commonService.httpclient, 6);
        // initialize setter.
        this.SwtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        this.logger.info("[ngOnDestroy] Method not implemented.");
    };
    /**
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        this.logger.info("[ngAfterViewInit] Method not implemented.");
        if (this.shiftUp != 0) {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "-" + this.shiftUp + "px");
        }
        else {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "0px");
        }
        if (this.showAbove) {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("bottom", "100%");
        }
        else {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "0px");
        }
        // set multiselection comboBox width
        $(this.elem.nativeElement.children[0]).width(this.width);
        // set multiselection comboBox dropdownlist  height
        $(this.elem.nativeElement.children[0].children[0].children[1].children[1]).css("max-height", this.height + "px");
        // set dropdown-btn height
        $(this.elem.nativeElement.children[0].children[0].children[0].children[0]).css("height", this.dropHeight + "px");
    };
    /**
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.selects = this.defaultSelectedItems;
        this.dropdownSettings = {
            singleSelection: false,
            idField: 'value',
            textField: 'content',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: this.itemLimit,
            maxHeight: 60,
            allowSearchFilter: true,
        };
    };
    Object.defineProperty(SwtMultiselectCombobox.prototype, "shiftUp", {
        get: /**
         * @return {?}
         */
        function () {
            return this._shiftUp;
        },
        // enabled getter and setter
        set: 
        // enabled getter and setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._shiftUp = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtMultiselectCombobox.prototype, "showAbove", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showAbove;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._showAbove = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtMultiselectCombobox.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visibility;
        },
        /* input to hold component visibility */
        set: /* input to hold component visibility */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) !== 'string') {
                this._visibility = value;
                value ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
            }
            else {
                if (value === 'true') {
                    this.visible = true;
                    $(this.elem.nativeElement).show();
                }
                else {
                    this._visibility = false;
                    $(this.elem.nativeElement).hide();
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtMultiselectCombobox.prototype, "toolTip", {
        get: /**
         * @return {?}
         */
        function () {
            return this.toolTip;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.toolTipObject = $(this.elem.nativeElement);
            /** @type {?} */
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.toolTipObject.attr("title", value);
                _this_1.toolTipObject.tooltip({
                    position: { my: "left+5 center", at: "right-250 bottom-10" },
                    show: { duration: 600, delay: 500 },
                    open: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        $(_this.toolTipObject).removeAttr('title');
                    })
                });
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} item
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.onItemSelect = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        //this.selectedItem = item["item_text"];
        this.selects.push(item);
        this.tooltipValue.push(item.content);
        this.logger.info("[onItemSelect]---selected items", this.selects);
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        var target = {
            tooltip: this.tooltipValue.toString()
        };
        this.ITEM_SELECT.emit(target);
    };
    /**
     * @param {?} items
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.onSelectAll = /**
     * @param {?} items
     * @return {?}
     */
    function (items) {
        this.tooltipValue = [];
        this.logger.info("[onSelectAll]---all items", items);
        for (var i = 0; i < items.length; i++) {
            if (i % 5 == 0) {
                this.tooltipValue.push("$#$");
            }
            this.tooltipValue.push(items[i].content);
        }
        this.toolTip = (this.tooltipValue.toString()).split("$#$,").join("\n");
        this.selects = items;
        /** @type {?} */
        var target = {
            tooltip: this.tooltipValue.toString()
        };
        this.SELECT_ALL.emit(target);
    };
    /**
     * @param {?} item
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.onItemDeSelect = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info("[onItemDeSelect]---item desselected", item);
        /** @type {?} */
        var index = this.selects.findIndex((/**
         * @param {?} i
         * @return {?}
         */
        function (i) { return i.value === item["value"]; }));
        this.logger.info("[onItemDeSelect]---item desselected idex", index);
        if (index > -1) {
            this.selects.splice(index, 1);
            this.tooltipValue.splice(index, 1);
        }
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        var target = {
            tooltip: this.tooltipValue.toString()
        };
        this.ITEM_DESELECT.emit(target);
        this.logger.info("[onItemDeSelect]---new data", this.selects);
    };
    /**
     * @param {?} items
     * @return {?}
     */
    SwtMultiselectCombobox.prototype.onDeSelectAll = /**
     * @param {?} items
     * @return {?}
     */
    function (items) {
        this.selects = [];
        this.tooltipValue = [];
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        var target = {
            tooltip: ""
        };
        this.DESELECT_ALL.emit(target);
    };
    SwtMultiselectCombobox.decorators = [
        { type: Component, args: [{
                    selector: 'SwtMultiselectCombobox',
                    template: "<ng-multiselect-dropdown-angular7 class=\"multiselect\"\n    [placeholder]=\"placeholder\"\n    [disabled] =\"isDropdownDisabled\"\n    [(ngModel)]=\"defaultSelectedItems\"\n    [data]=\"dataProvider\"\n    [settings]=\"dropdownSettings\"\n    (onSelect)=\"onItemSelect($event)\"\n    (onSelectAll)=\"onSelectAll($event)\"\n    (onDeSelect) = \"onItemDeSelect($event)\"\n    (onDeSelectAll) = \"onDeSelectAll($event)\"\n  >\n  </ng-multiselect-dropdown-angular7>\n  ",
                    encapsulation: ViewEncapsulation.None,
                    styles: ["\n\n       .dropdown-btn {\n         display: inline-block;\n         background: #ffffff;\n         padding-top: 2px !important;\n         padding-right: 0px !important;\n         padding-bottom: 1px !important;\n         padding-left: 4px !important;\n         border-top: 1px solid #7F9DB9 !important;\n         border-left: 1px solid #7F9DB9 !important;\n         border-right: 1px solid #7F9DB9 !important;\n         border-bottom: 1px solid #7F9DB9 !important;\n         color:#808080; /*to over write fieldset color*/\n         background-image: linear-gradient(to left, #ccddea 1px, #A7C6DE 20px,#ffffff 2px, #ffffff) !important;\n       }\n\n       .multiselect-dropdown .dropdown-btn .dropdown-down {\n            display: inline-block;\n            margin-top: 6px;\n            border-top: 5px solid #555 !important;\n            border-left: 5px solid transparent !important;\n            border-right: 5px solid transparent !important;\n          }\n\n        .multiselect-dropdown .dropdown-btn .dropdown-up {\n          display: inline-block;\n          margin-top: 5px;\n          border-bottom: 5px solid #555 !important;\n          border-left: 5px solid transparent !important;\n          border-right: 5px solid transparent !important;\n        }\n        .multiselect {\n         display: inline-block;\n         width: 200px;\n         height: 70px;\n         padding: 6px 12px;\n         margin-bottom: 0;\n         font-weight: 400;\n         line-height: 1.52857143;\n         text-align: left;\n         vertical-align: middle;\n         cursor: pointer;\n         background-image: none;\n         border-radius: 10px;\n        }\n        .dropdown-list{\n          position: absolute;\n          padding-top: 1px !important;\n          width: 100%;\n          z-index: 9999;\n          border: 1px solid #ccc;\n          border-radius: 3px;\n          background: #fff;\n          box-shadow: 0 1px 5px #959595; \n        }\n        .multiselect-item-checkbox{\n          padding-top: 2px !important;\n          padding-right: 3px !important;\n          padding-bottom: 2px !important;\n          padding-left: 3px !important;\n        }\n        .filter-textbox{\n          padding-top: 1px !important;\n          padding-right: 1px !important;\n          padding-bottom: 1px !important;\n          padding-left: 1px !important;\n        }\n        .ng-pristine.ng-valid.ng-touched{\n          padding-top: 0px !important;\n          padding-right: 0px !important;\n          padding-bottom: 0px !important;\n          padding-left: 30px !important;\n          \n        }\n\n        div.tooltip-inner{\n          background-color: #F1F1DE !important;\n          width: auto !important;\n          max-width:1200px !important;\n          min-width: 10px !important;\n          margin-top: -10px !important;\n          -moz-transition-delay: 0s !important;\n          transition-delay: 0s !important;\n          box-shadow: 0px 5px 8px #7A8D99  !important;\n          padding: 3px 5px 3px 5px !important;\n          border-radius: 5px;\n          transition: opacity  5s linear 0s !important;\n          transition-delay: 0s !important;\n          color: #000;\n          font-size: 10px;\n        }\n\n        div.tooltip-arrow{\n          border-left: 0 solid #000000 !important;\n          border-right: 0 solid #000000 !important;\n          border-bottom: 0 solid #000000 !important;\n          border-top: 0 solid #000000 !important;\n        }\n        .selected-item{\n          margin-right: 4px !important;\n          margin-bottom: 4px !important;\n        }\n        "]
                }] }
    ];
    /** @nocollapse */
    SwtMultiselectCombobox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtMultiselectCombobox.propDecorators = {
        ITEM_SELECT: [{ type: Output, args: ['ITEM_SELECT',] }],
        ITEM_DESELECT: [{ type: Output, args: ['ITEM_DESELECT',] }],
        SELECT_ALL: [{ type: Output, args: ['SELECT_ALL',] }],
        DESELECT_ALL: [{ type: Output, args: ['DESELECT_ALL',] }],
        width: [{ type: Input, args: ["width",] }],
        height: [{ type: Input, args: ["height",] }],
        dropHeight: [{ type: Input, args: ["dropHeight",] }],
        shiftUp: [{ type: Input }],
        showAbove: [{ type: Input }],
        visible: [{ type: Input, args: ['visible',] }],
        toolTip: [{ type: Input }]
    };
    return SwtMultiselectCombobox;
}());
export { SwtMultiselectCombobox };
if (false) {
    /** @type {?} */
    SwtMultiselectCombobox.prototype.ITEM_SELECT;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.ITEM_DESELECT;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.SELECT_ALL;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.DESELECT_ALL;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.placeholder;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.isDropdownDisabled;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dataProvider;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.defaultSelectedItems;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.selectedItems;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dropdownSettings;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.itemLimit;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.selectedItem;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.selects;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.tooltipValue;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._shiftUp;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._showAbove;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.width;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.height;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dropHeight;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LW11bHRpU2VsZWN0LWNvbWJvYm94LmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9zd3QtbXVsdGlTZWxlY3QtY29tYm9ib3guY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQWlCLFNBQVMsRUFBRSxVQUFVLEVBQUUsS0FBSyxFQUFxQixpQkFBaUIsRUFBRSxNQUFNLEVBQUUsWUFBWSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQ3hJLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSwyQkFBMkIsQ0FBQztBQUNuRCxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFDeEQsT0FBTyxFQUFFLFFBQVEsRUFBRSxNQUFNLDRCQUE0QixDQUFDO0FBQ3REO0lBK0hFLGdDQUFvQixJQUFnQixFQUFVLGFBQTRCO1FBQXRELFNBQUksR0FBSixJQUFJLENBQVk7UUFBVSxrQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUpqRCxnQkFBVyxHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDO1FBQ3ZELGtCQUFhLEdBQXNCLElBQUksWUFBWSxFQUFPLENBQUM7UUFDOUQsZUFBVSxHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDO1FBQ3RELGlCQUFZLEdBQXNCLElBQUksWUFBWSxFQUFPLENBQUM7UUFrQzdFLGdCQUFXLEdBQUcsRUFBRSxDQUFDO1FBQ2pCLHVCQUFrQixHQUFDLEtBQUssQ0FBQztRQUN6QixpQkFBWSxHQUFHLEVBQUUsQ0FBQztRQUNsQix5QkFBb0IsR0FBRyxFQUFFLENBQUM7UUFDMUIsa0JBQWEsR0FBRyxFQUFFLENBQUM7UUFDbkIscUJBQWdCLEdBQUcsRUFBRSxDQUFDO1FBSXRCLFlBQU8sR0FBRyxFQUFFLENBQUM7UUFDYixpQkFBWSxHQUFHLEVBQUUsQ0FBQztRQUNqQixnQkFBVyxHQUFHLElBQUksQ0FBQztRQU9uQixhQUFRLEdBQVcsQ0FBQyxDQUFDO1FBQ3JCLGVBQVUsR0FBVyxLQUFLLENBQUM7O1FBRW5CLFVBQUssR0FBVyxLQUFLLENBQUM7O1FBRXJCLFdBQU0sR0FBVyxJQUFJLENBQUM7O1FBRWxCLGVBQVUsR0FBVyxJQUFJLENBQUM7UUF6RDdDLHFCQUFxQjtRQUNwQixJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLHdCQUF3QixFQUFFLGFBQWEsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDakYscUJBQXFCO1FBQ3BCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxRQUFRLENBQUMsYUFBYSxDQUFDLENBQUM7SUFDL0MsQ0FBQzs7OztJQUNELDRDQUFXOzs7SUFBWDtRQUNFLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLHVDQUF1QyxDQUFDLENBQUM7SUFDNUQsQ0FBQzs7OztJQUNELGdEQUFlOzs7SUFBZjtRQUNFLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLDJDQUEyQyxDQUFDLENBQUM7UUFDOUQsSUFBRyxJQUFJLENBQUMsT0FBTyxJQUFJLENBQUMsRUFBRTtZQUNwQixDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLEdBQUcsR0FBQyxJQUFJLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQyxDQUFDO1NBQ3pHO2FBQUk7WUFDSCxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLEtBQUssQ0FBQyxDQUFDO1NBQ3pGO1FBRUQsSUFBRyxJQUFJLENBQUMsU0FBUyxFQUFDO1lBQ2QsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQztTQUN0RjthQUFJO1lBQ0gsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSxLQUFLLENBQUMsQ0FBQztTQUMzRjtRQUVELG9DQUFvQztRQUNwQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN6RCxtREFBbUQ7UUFDbkQsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsQ0FBQztRQUNqSCwwQkFBMEI7UUFDMUIsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUcsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsQ0FBQztJQUVwSCxDQUFDOzs7O0lBNkJELHlDQUFROzs7SUFBUjtRQUNFLElBQUksQ0FBQyxPQUFPLEdBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDO1FBQ3ZDLElBQUksQ0FBQyxnQkFBZ0IsR0FBRztZQUN0QixlQUFlLEVBQUUsS0FBSztZQUN0QixPQUFPLEVBQUUsT0FBTztZQUNoQixTQUFTLEVBQUUsU0FBUztZQUNwQixhQUFhLEVBQUUsWUFBWTtZQUMzQixlQUFlLEVBQUUsY0FBYztZQUMvQixjQUFjLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDOUIsU0FBUyxFQUFHLEVBQUU7WUFDZCxpQkFBaUIsRUFBRSxJQUFJO1NBQ3hCLENBQUM7SUFDSixDQUFDO0lBR0Qsc0JBQ0ksMkNBQU87Ozs7UUFJWDtZQUNFLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztRQUN2QixDQUFDO1FBUkQsNEJBQTRCOzs7Ozs7O1FBQzVCLFVBQ1ksS0FBSztZQUNmLElBQUksQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFDO1FBQ3hCLENBQUM7OztPQUFBO0lBT0Qsc0JBQ0ksNkNBQVM7Ozs7UUFJYjtZQUNFLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztRQUN6QixDQUFDOzs7OztRQVBELFVBQ2MsS0FBSztZQUNqQixJQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztRQUMxQixDQUFDOzs7T0FBQTtJQVFILHNCQUFJLDJDQUFPOzs7O1FBQVg7WUFDRSxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7UUFDMUIsQ0FBQztRQUNELHdDQUF3Qzs7Ozs7O1FBQ3hDLFVBQ1ksS0FBSztZQUNmLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDL0IsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUM7Z0JBQ3pCLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO2FBQy9FO2lCQUFNO2dCQUNMLElBQUksS0FBSyxLQUFLLE1BQU0sRUFBRTtvQkFDcEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7b0JBQ3BCLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO2lCQUNuQztxQkFBTTtvQkFDTCxJQUFJLENBQUMsV0FBVyxHQUFHLEtBQUssQ0FBQztvQkFDekIsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUM7aUJBQ25DO2FBQ0Y7UUFDSCxDQUFDOzs7T0FoQkE7SUFtQkQsc0JBQ0ksMkNBQU87Ozs7UUFlWDtZQUNJLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztRQUN4QixDQUFDOzs7OztRQWxCRCxVQUNZLEtBQUs7WUFEakIsbUJBY0M7WUFaRyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDOztnQkFDNUMsS0FBSyxHQUFHLElBQUk7WUFDaEIsVUFBVTs7O1lBQUM7Z0JBQ1AsT0FBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUN4QyxPQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQztvQkFDdkIsUUFBUSxFQUFFLEVBQUUsRUFBRSxFQUFFLGVBQWUsRUFBRSxFQUFFLEVBQUUscUJBQXFCLEVBQUU7b0JBQzVELElBQUksRUFBRSxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUUsS0FBSyxFQUFHLEdBQUcsRUFBRTtvQkFDcEMsSUFBSTs7Ozs7b0JBQUUsVUFBVSxLQUFLLEVBQUUsRUFBRTt3QkFDckIsQ0FBQyxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQy9DLENBQUMsQ0FBQTtpQkFDSCxDQUFDLENBQUE7WUFDUCxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7UUFDVixDQUFDOzs7T0FBQTs7Ozs7SUFPQyw2Q0FBWTs7OztJQUFaLFVBQWEsSUFBUztRQUNwQix3Q0FBd0M7UUFDeEMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDeEIsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLGlDQUFpQyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNsRSxJQUFJLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxFQUFFLENBQUM7O1lBQ3BDLE1BQU0sR0FBRztZQUNiLE9BQU8sRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRTtTQUN0QztRQUNELElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBRWhDLENBQUM7Ozs7O0lBR0QsNENBQVc7Ozs7SUFBWCxVQUFZLEtBQVU7UUFDcEIsSUFBSSxDQUFDLFlBQVksR0FBQyxFQUFFLENBQUM7UUFDckIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsMkJBQTJCLEVBQUMsS0FBSyxDQUFDLENBQUM7UUFDcEQsS0FBSyxJQUFJLENBQUMsR0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUM7WUFDL0IsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFHLENBQUMsRUFBQztnQkFDZCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUM3QjtZQUNGLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQztTQUMxQztRQUNELElBQUksQ0FBQyxPQUFPLEdBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNyRSxJQUFJLENBQUMsT0FBTyxHQUFDLEtBQUssQ0FBQzs7WUFDYixNQUFNLEdBQUc7WUFDYixPQUFPLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUU7U0FDdEM7UUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUMvQixDQUFDOzs7OztJQUlELCtDQUFjOzs7O0lBQWQsVUFBZSxJQUFTO1FBQ3RCLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLHFDQUFxQyxFQUFDLElBQUksQ0FBQyxDQUFDOztZQUN2RCxLQUFLLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxTQUFTOzs7O1FBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsS0FBSyxLQUFLLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBekIsQ0FBeUIsRUFBQztRQUNwRSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQywwQ0FBMEMsRUFBQyxLQUFLLENBQUMsQ0FBQztRQUNuRSxJQUFJLEtBQUssR0FBRyxDQUFDLENBQUMsRUFBRTtZQUNkLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQztZQUM5QixJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7U0FDcEM7UUFDRCxJQUFJLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxFQUFFLENBQUM7O1lBQ3BDLE1BQU0sR0FBRztZQUNiLE9BQU8sRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRTtTQUN0QztRQUNELElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ2hDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLDZCQUE2QixFQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUMvRCxDQUFDOzs7OztJQUlELDhDQUFhOzs7O0lBQWIsVUFBYyxLQUFVO1FBQ3RCLElBQUksQ0FBQyxPQUFPLEdBQUMsRUFBRSxDQUFDO1FBQ2hCLElBQUksQ0FBQyxZQUFZLEdBQUMsRUFBRSxDQUFDO1FBQ3JCLElBQUksQ0FBQyxPQUFPLEdBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsQ0FBQzs7WUFDcEMsTUFBTSxHQUFHO1lBQ2IsT0FBTyxFQUFFLEVBQUU7U0FDWjtRQUNELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQ2pDLENBQUM7O2dCQW5VRixTQUFTLFNBQUM7b0JBQ1QsUUFBUSxFQUFFLHdCQUF3QjtvQkFDbEMsUUFBUSxFQUFFLG9kQVlUO29CQTBHRCxhQUFhLEVBQUUsaUJBQWlCLENBQUMsSUFBSTs2QkF4RzVCLDBoSEF1R0Y7aUJBRVI7Ozs7Z0JBN0hrQyxVQUFVO2dCQUVwQyxhQUFhOzs7OEJBNkhuQixNQUFNLFNBQUUsYUFBYTtnQ0FDckIsTUFBTSxTQUFFLGVBQWU7NkJBQ3ZCLE1BQU0sU0FBRSxZQUFZOytCQUNwQixNQUFNLFNBQUUsY0FBYzt3QkF1RHRCLEtBQUssU0FBQyxPQUFPO3lCQUViLEtBQUssU0FBQyxRQUFROzZCQUVkLEtBQUssU0FBQyxZQUFZOzBCQWdCbEIsS0FBSzs0QkFVTCxLQUFLOzBCQWVQLEtBQUssU0FBQyxTQUFTOzBCQWlCZixLQUFLOztJQW1GTiw2QkFBQztDQUFBLEFBdFVELElBc1VDO1NBNU1ZLHNCQUFzQjs7O0lBQ2pDLDZDQUFrRjs7SUFDbEYsK0NBQXNGOztJQUN0Riw0Q0FBZ0Y7O0lBQ2hGLDhDQUFvRjs7SUFrQ3BGLDZDQUF3Qjs7SUFDeEIsb0RBQWdDOztJQUNoQyw4Q0FBeUI7O0lBQ3pCLHNEQUFpQzs7SUFDakMsK0NBQTBCOztJQUMxQixrREFBNkI7O0lBRTdCLDJDQUFpQjs7Ozs7SUFDakIsOENBQXFCOztJQUNyQix5Q0FBb0I7O0lBQ3BCLDhDQUF5Qjs7Ozs7SUFDekIsNkNBQTJCOzs7OztJQUUzQix3Q0FBdUI7Ozs7O0lBRXZCLDBDQUEyQjs7Ozs7SUFFM0IsK0NBQTJCOzs7OztJQUMzQiwwQ0FBNkI7Ozs7O0lBQzdCLDRDQUFtQzs7SUFFbkMsdUNBQXNDOztJQUV0Qyx3Q0FBdUM7O0lBRXZDLDRDQUErQzs7Ozs7SUExRG5DLHNDQUF3Qjs7Ozs7SUFBRSwrQ0FBb0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBZnRlclZpZXdJbml0LCBDb21wb25lbnQsIEVsZW1lbnRSZWYsIElucHV0LCBPbkRlc3Ryb3ksIE9uSW5pdCwgVmlld0VuY2Fwc3VsYXRpb24sIE91dHB1dCwgRXZlbnRFbWl0dGVyIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IExvZ2dlciB9IGZyb20gXCIuLi9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHsgU3d0QWxlcnQgfSBmcm9tIFwiLi4vdXRpbHMvc3d0LWFsZXJ0LnNlcnZpY2VcIjtcclxuQENvbXBvbmVudCh7XHJcbiAgc2VsZWN0b3I6ICdTd3RNdWx0aXNlbGVjdENvbWJvYm94JyxcclxuICB0ZW1wbGF0ZTogYDxuZy1tdWx0aXNlbGVjdC1kcm9wZG93bi1hbmd1bGFyNyBjbGFzcz1cIm11bHRpc2VsZWN0XCJcclxuICAgIFtwbGFjZWhvbGRlcl09XCJwbGFjZWhvbGRlclwiXHJcbiAgICBbZGlzYWJsZWRdID1cImlzRHJvcGRvd25EaXNhYmxlZFwiXHJcbiAgICBbKG5nTW9kZWwpXT1cImRlZmF1bHRTZWxlY3RlZEl0ZW1zXCJcclxuICAgIFtkYXRhXT1cImRhdGFQcm92aWRlclwiXHJcbiAgICBbc2V0dGluZ3NdPVwiZHJvcGRvd25TZXR0aW5nc1wiXHJcbiAgICAob25TZWxlY3QpPVwib25JdGVtU2VsZWN0KCRldmVudClcIlxyXG4gICAgKG9uU2VsZWN0QWxsKT1cIm9uU2VsZWN0QWxsKCRldmVudClcIlxyXG4gICAgKG9uRGVTZWxlY3QpID0gXCJvbkl0ZW1EZVNlbGVjdCgkZXZlbnQpXCJcclxuICAgIChvbkRlU2VsZWN0QWxsKSA9IFwib25EZVNlbGVjdEFsbCgkZXZlbnQpXCJcclxuICA+XHJcbiAgPC9uZy1tdWx0aXNlbGVjdC1kcm9wZG93bi1hbmd1bGFyNz5cclxuICBgLFxyXG5cclxuICBzdHlsZXM6IFtgXHJcblxyXG4gICAgICAgLmRyb3Bkb3duLWJ0biB7XHJcbiAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjtcclxuICAgICAgICAgcGFkZGluZy10b3A6IDJweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICBwYWRkaW5nLXJpZ2h0OiAwcHggIWltcG9ydGFudDtcclxuICAgICAgICAgcGFkZGluZy1ib3R0b206IDFweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICBwYWRkaW5nLWxlZnQ6IDRweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICBib3JkZXItdG9wOiAxcHggc29saWQgIzdGOURCOSAhaW1wb3J0YW50O1xyXG4gICAgICAgICBib3JkZXItbGVmdDogMXB4IHNvbGlkICM3RjlEQjkgIWltcG9ydGFudDtcclxuICAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgIzdGOURCOSAhaW1wb3J0YW50O1xyXG4gICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgIzdGOURCOSAhaW1wb3J0YW50O1xyXG4gICAgICAgICBjb2xvcjojODA4MDgwOyAvKnRvIG92ZXIgd3JpdGUgZmllbGRzZXQgY29sb3IqL1xyXG4gICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQodG8gbGVmdCwgI2NjZGRlYSAxcHgsICNBN0M2REUgMjBweCwjZmZmZmZmIDJweCwgI2ZmZmZmZikgIWltcG9ydGFudDtcclxuICAgICAgIH1cclxuXHJcbiAgICAgICAubXVsdGlzZWxlY3QtZHJvcGRvd24gLmRyb3Bkb3duLWJ0biAuZHJvcGRvd24tZG93biB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogNnB4O1xyXG4gICAgICAgICAgICBib3JkZXItdG9wOiA1cHggc29saWQgIzU1NSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBib3JkZXItbGVmdDogNXB4IHNvbGlkIHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIGJvcmRlci1yaWdodDogNXB4IHNvbGlkIHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgIC5tdWx0aXNlbGVjdC1kcm9wZG93biAuZHJvcGRvd24tYnRuIC5kcm9wZG93bi11cCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICAgICAgICBtYXJnaW4tdG9wOiA1cHg7XHJcbiAgICAgICAgICBib3JkZXItYm90dG9tOiA1cHggc29saWQgIzU1NSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLWxlZnQ6IDVweCBzb2xpZCB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLXJpZ2h0OiA1cHggc29saWQgdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICAgICAgLm11bHRpc2VsZWN0IHtcclxuICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gICAgICAgICB3aWR0aDogMjAwcHg7XHJcbiAgICAgICAgIGhlaWdodDogNzBweDtcclxuICAgICAgICAgcGFkZGluZzogNnB4IDEycHg7XHJcbiAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7XHJcbiAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjUyODU3MTQzO1xyXG4gICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gICAgICAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG4gICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IG5vbmU7XHJcbiAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5kcm9wZG93bi1saXN0e1xyXG4gICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgcGFkZGluZy10b3A6IDFweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICB6LWluZGV4OiA5OTk5O1xyXG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2NjYztcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDFweCA1cHggIzk1OTU5NTsgXHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5tdWx0aXNlbGVjdC1pdGVtLWNoZWNrYm94e1xyXG4gICAgICAgICAgcGFkZGluZy10b3A6IDJweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgcGFkZGluZy1yaWdodDogM3B4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMnB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDNweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuICAgICAgICAuZmlsdGVyLXRleHRib3h7XHJcbiAgICAgICAgICBwYWRkaW5nLXRvcDogMXB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiAxcHggIWltcG9ydGFudDtcclxuICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxcHggIWltcG9ydGFudDtcclxuICAgICAgICAgIHBhZGRpbmctbGVmdDogMXB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5uZy1wcmlzdGluZS5uZy12YWxpZC5uZy10b3VjaGVke1xyXG4gICAgICAgICAgcGFkZGluZy10b3A6IDBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgcGFkZGluZy1yaWdodDogMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDMwcHggIWltcG9ydGFudDtcclxuICAgICAgICAgIFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZGl2LnRvb2x0aXAtaW5uZXJ7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjFGMURFICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICB3aWR0aDogYXV0byAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgbWF4LXdpZHRoOjEyMDBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgbWluLXdpZHRoOiAxMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBtYXJnaW4tdG9wOiAtMTBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgLW1vei10cmFuc2l0aW9uLWRlbGF5OiAwcyAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgdHJhbnNpdGlvbi1kZWxheTogMHMgIWltcG9ydGFudDtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDBweCA1cHggOHB4ICM3QThEOTkgICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwYWRkaW5nOiAzcHggNXB4IDNweCA1cHggIWltcG9ydGFudDtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDtcclxuICAgICAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgIDVzIGxpbmVhciAwcyAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgdHJhbnNpdGlvbi1kZWxheTogMHMgIWltcG9ydGFudDtcclxuICAgICAgICAgIGNvbG9yOiAjMDAwO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxMHB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZGl2LnRvb2x0aXAtYXJyb3d7XHJcbiAgICAgICAgICBib3JkZXItbGVmdDogMCBzb2xpZCAjMDAwMDAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBib3JkZXItcmlnaHQ6IDAgc29saWQgIzAwMDAwMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgYm9yZGVyLWJvdHRvbTogMCBzb2xpZCAjMDAwMDAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBib3JkZXItdG9wOiAwIHNvbGlkICMwMDAwMDAgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICAgICAgLnNlbGVjdGVkLWl0ZW17XHJcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDRweCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGBdLFxyXG4gIGVuY2Fwc3VsYXRpb246IFZpZXdFbmNhcHN1bGF0aW9uLk5vbmVcclxufSlcclxuZXhwb3J0IGNsYXNzIFN3dE11bHRpc2VsZWN0Q29tYm9ib3ggaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQsIE9uRGVzdHJveSB7XHJcbiAgQE91dHB1dCggJ0lURU1fU0VMRUNUJyApIElURU1fU0VMRUNUOiBFdmVudEVtaXR0ZXI8YW55PiA9IG5ldyBFdmVudEVtaXR0ZXI8YW55PigpO1xyXG4gIEBPdXRwdXQoICdJVEVNX0RFU0VMRUNUJyApIElURU1fREVTRUxFQ1Q6IEV2ZW50RW1pdHRlcjxhbnk+ID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgQE91dHB1dCggJ1NFTEVDVF9BTEwnICkgU0VMRUNUX0FMTDogRXZlbnRFbWl0dGVyPGFueT4gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcclxuICBAT3V0cHV0KCAnREVTRUxFQ1RfQUxMJyApIERFU0VMRUNUX0FMTDogRXZlbnRFbWl0dGVyPGFueT4gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIGVsZW06IEVsZW1lbnRSZWYsIHByaXZhdGUgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSkge1xyXG4gICAgLy8gaW5pdGlhbGl6ZSBsb2dnZXIuXHJcbiAgICAgdGhpcy5sb2dnZXIgPSBuZXcgTG9nZ2VyKCdTd3RNdWx0aXNlbGVjdENvbWJvYm94JywgY29tbW9uU2VydmljZS5odHRwY2xpZW50LCA2KTtcclxuICAgIC8vIGluaXRpYWxpemUgc2V0dGVyLlxyXG4gICAgIHRoaXMuU3d0QWxlcnQgPSBuZXcgU3d0QWxlcnQoY29tbW9uU2VydmljZSk7ICBcclxuICB9XHJcbiAgbmdPbkRlc3Ryb3koKTogdm9pZCB7XHJcbiAgICB0aGlzLmxvZ2dlci5pbmZvKFwiW25nT25EZXN0cm95XSBNZXRob2Qgbm90IGltcGxlbWVudGVkLlwiKTtcclxuICB9XHJcbiAgbmdBZnRlclZpZXdJbml0KCk6IHZvaWQge1xyXG4gICAgdGhpcy5sb2dnZXIuaW5mbyhcIltuZ0FmdGVyVmlld0luaXRdIE1ldGhvZCBub3QgaW1wbGVtZW50ZWQuXCIpO1xyXG4gICAgaWYodGhpcy5zaGlmdFVwICE9IDApIHtcclxuICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXS5jaGlsZHJlblswXS5jaGlsZHJlblsxXSkuY3NzKFwibWFyZ2luLXRvcFwiLCBcIi1cIit0aGlzLnNoaWZ0VXArXCJweFwiKTsgICAgIFxyXG4gICAgfWVsc2V7XHJcbiAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMV0pLmNzcyhcIm1hcmdpbi10b3BcIiwgXCIwcHhcIik7IFxyXG4gICAgfVxyXG5cclxuICAgIGlmKHRoaXMuc2hvd0Fib3ZlKXtcclxuICAgICAgICAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuWzFdKS5jc3MoXCJib3R0b21cIiwgXCIxMDAlXCIpOyAgICAgXHJcbiAgICAgIH1lbHNle1xyXG4gICAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMV0pLmNzcyhcIm1hcmdpbi10b3BcIiwgXCIwcHhcIik7IFxyXG4gICAgfVxyXG5cclxuICAgIC8vIHNldCBtdWx0aXNlbGVjdGlvbiBjb21ib0JveCB3aWR0aFxyXG4gICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkud2lkdGgodGhpcy53aWR0aCk7XHJcbiAgICAvLyBzZXQgbXVsdGlzZWxlY3Rpb24gY29tYm9Cb3ggZHJvcGRvd25saXN0ICBoZWlnaHRcclxuICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMV0uY2hpbGRyZW5bMV0pLmNzcyhcIm1heC1oZWlnaHRcIiwgdGhpcy5oZWlnaHQgKyBcInB4XCIpO1xyXG4gICAgLy8gc2V0IGRyb3Bkb3duLWJ0biBoZWlnaHRcclxuICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0pLmNzcyhcImhlaWdodFwiICwgdGhpcy5kcm9wSGVpZ2h0ICsgXCJweFwiKTtcclxuICAgIFxyXG4gIH1cclxuXHJcblxyXG4gIHB1YmxpYyBwbGFjZWhvbGRlciA9ICcnO1xyXG4gIHB1YmxpYyBpc0Ryb3Bkb3duRGlzYWJsZWQ9ZmFsc2U7XHJcbiAgcHVibGljIGRhdGFQcm92aWRlciA9IFtdO1xyXG4gIHB1YmxpYyBkZWZhdWx0U2VsZWN0ZWRJdGVtcyA9IFtdO1xyXG4gIHB1YmxpYyBzZWxlY3RlZEl0ZW1zID0gW107XHJcbiAgcHVibGljIGRyb3Bkb3duU2V0dGluZ3MgPSB7fTtcclxuICAvLyBpdGVtIGxpbWl0IG51bWJlciBcclxuICBwdWJsaWMgaXRlbUxpbWl0O1xyXG4gIHByaXZhdGUgc2VsZWN0ZWRJdGVtO1xyXG4gIHB1YmxpYyBzZWxlY3RzID0gW107XHJcbiAgcHVibGljIHRvb2x0aXBWYWx1ZSA9IFtdO1xyXG4gIHByaXZhdGUgX3Zpc2liaWxpdHkgPSB0cnVlO1xyXG4gIC8vIEluaXRpYWxpemUgbG9nZ2VyMS5cclxuICBwcml2YXRlIGxvZ2dlcjogTG9nZ2VyO1xyXG4gIC8vIEluaXRpYWxpemUgU3d0QWxlcnRcclxuICBwcml2YXRlIFN3dEFsZXJ0OiBTd3RBbGVydDtcclxuICAvKiBJbnB1dCB0byBob2xkIGlucHV0IHRvb2xUaXAqL1xyXG4gIHByaXZhdGUgdG9vbFRpcE9iamVjdDogYW55O1xyXG4gIHByaXZhdGUgX3NoaWZ0VXA6IG51bWJlciA9IDA7XHJcbiAgcHJpdmF0ZSBfc2hvd0Fib3ZlOmJvb2xlYW4gPSBmYWxzZTtcclxuICAvLyBJbnB1dCB0byBzdG9yZSB3aWR0aC5cclxuICBASW5wdXQoXCJ3aWR0aFwiKSB3aWR0aDogc3RyaW5nID0gXCIyMDBcIjtcclxuICAvLyBJbnB1dCB0byBzdG9yZSBoZWlnaHQuXHJcbiAgQElucHV0KFwiaGVpZ2h0XCIpIGhlaWdodDogc3RyaW5nID0gXCI3MFwiO1xyXG4gIC8vIElucHV0IHRvIHN0b3JlIGRyb3BEb3duIGJ0biBoZWlnaHQuXHJcbiAgQElucHV0KFwiZHJvcEhlaWdodFwiKSBkcm9wSGVpZ2h0OiBzdHJpbmcgPSBcIjMwXCI7XHJcbiAgbmdPbkluaXQoKSB7XHJcbiAgICB0aGlzLnNlbGVjdHM9dGhpcy5kZWZhdWx0U2VsZWN0ZWRJdGVtcztcclxuICAgIHRoaXMuZHJvcGRvd25TZXR0aW5ncyA9IHtcclxuICAgICAgc2luZ2xlU2VsZWN0aW9uOiBmYWxzZSxcclxuICAgICAgaWRGaWVsZDogJ3ZhbHVlJyxcclxuICAgICAgdGV4dEZpZWxkOiAnY29udGVudCcsXHJcbiAgICAgIHNlbGVjdEFsbFRleHQ6ICdTZWxlY3QgQWxsJyxcclxuICAgICAgdW5TZWxlY3RBbGxUZXh0OiAnVW5TZWxlY3QgQWxsJyxcclxuICAgICAgaXRlbXNTaG93TGltaXQ6IHRoaXMuaXRlbUxpbWl0LFxyXG4gICAgICBtYXhIZWlnaHQgOiA2MCxcclxuICAgICAgYWxsb3dTZWFyY2hGaWx0ZXI6IHRydWUsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLy8gZW5hYmxlZCBnZXR0ZXIgYW5kIHNldHRlclxyXG4gIEBJbnB1dCgpXHJcbiAgc2V0IHNoaWZ0VXAodmFsdWUpIHtcclxuICAgIHRoaXMuX3NoaWZ0VXAgPSB2YWx1ZTtcclxuICB9XHJcblxyXG4gIGdldCBzaGlmdFVwKCkge1xyXG4gICAgcmV0dXJuIHRoaXMuX3NoaWZ0VXA7XHJcbiAgfVxyXG5cclxuXHJcbiAgQElucHV0KClcclxuICBzZXQgc2hvd0Fib3ZlKHZhbHVlKSB7XHJcbiAgICB0aGlzLl9zaG93QWJvdmUgPSB2YWx1ZTtcclxuICB9XHJcblxyXG4gIGdldCBzaG93QWJvdmUoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5fc2hvd0Fib3ZlO1xyXG4gIH1cclxuXHJcbiAgXHJcblxyXG5nZXQgdmlzaWJsZSgpIHtcclxuICByZXR1cm4gdGhpcy5fdmlzaWJpbGl0eTtcclxufVxyXG4vKiBpbnB1dCB0byBob2xkIGNvbXBvbmVudCB2aXNpYmlsaXR5ICovXHJcbkBJbnB1dCgndmlzaWJsZScpXHJcbnNldCB2aXNpYmxlKHZhbHVlKSB7XHJcbiAgaWYgKHR5cGVvZiAodmFsdWUpICE9PSAnc3RyaW5nJykge1xyXG4gICAgdGhpcy5fdmlzaWJpbGl0eSA9IHZhbHVlO1xyXG4gICAgdmFsdWUgPyAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5zaG93KCkgOiAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgfSBlbHNlIHtcclxuICAgIGlmICh2YWx1ZSA9PT0gJ3RydWUnKSB7XHJcbiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7XHJcbiAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLnNob3coKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHRoaXMuX3Zpc2liaWxpdHkgPSBmYWxzZTtcclxuICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuaGlkZSgpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuXHJcbkBJbnB1dCgpXHJcbnNldCB0b29sVGlwKHZhbHVlKSB7XHJcbiAgICB0aGlzLnRvb2xUaXBPYmplY3QgPSAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KTtcclxuICAgIGxldCBfdGhpcyA9IHRoaXM7XHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICB0aGlzLnRvb2xUaXBPYmplY3QuYXR0cihcInRpdGxlXCIsIHZhbHVlKTtcclxuICAgICAgICB0aGlzLnRvb2xUaXBPYmplY3QudG9vbHRpcCh7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiB7IG15OiBcImxlZnQrNSBjZW50ZXJcIiwgYXQ6IFwicmlnaHQtMjUwIGJvdHRvbS0xMFwiIH0sXHJcbiAgICAgICAgICAgIHNob3c6IHsgZHVyYXRpb246IDYwMCwgZGVsYXkgOiA1MDAgfSxcclxuICAgICAgICAgICAgb3BlbjogZnVuY3Rpb24oIGV2ZW50LCB1aSApIHtcclxuICAgICAgICAgICAgICAgICQoX3RoaXMudG9vbFRpcE9iamVjdCkucmVtb3ZlQXR0cigndGl0bGUnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICB9KVxyXG4gICAgfSwgMCk7XHJcbn1cclxuXHJcbmdldCB0b29sVGlwKCkge1xyXG4gICAgcmV0dXJuIHRoaXMudG9vbFRpcDtcclxufVxyXG5cclxuXHJcbiAgb25JdGVtU2VsZWN0KGl0ZW06IGFueSkge1xyXG4gICAgLy90aGlzLnNlbGVjdGVkSXRlbSA9IGl0ZW1bXCJpdGVtX3RleHRcIl07XHJcbiAgICB0aGlzLnNlbGVjdHMucHVzaChpdGVtKTtcclxuICAgIHRoaXMudG9vbHRpcFZhbHVlLnB1c2goaXRlbS5jb250ZW50KTtcclxuICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbb25JdGVtU2VsZWN0XS0tLXNlbGVjdGVkIGl0ZW1zXCIsIHRoaXMuc2VsZWN0cyk7XHJcbiAgICB0aGlzLnRvb2xUaXA9dGhpcy50b29sdGlwVmFsdWUudG9TdHJpbmcoKTtcclxuICAgIGNvbnN0IHRhcmdldCA9IHtcclxuICAgICAgdG9vbHRpcDogdGhpcy50b29sdGlwVmFsdWUudG9TdHJpbmcoKVxyXG4gICAgfTtcclxuICAgIHRoaXMuSVRFTV9TRUxFQ1QuZW1pdCh0YXJnZXQpO1xyXG5cclxuICB9XHJcblxyXG5cclxuICBvblNlbGVjdEFsbChpdGVtczogYW55KSB7XHJcbiAgICB0aGlzLnRvb2x0aXBWYWx1ZT1bXTtcclxuICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbb25TZWxlY3RBbGxdLS0tYWxsIGl0ZW1zXCIsaXRlbXMpO1xyXG4gICAgZm9yIChsZXQgaT0wOyBpPGl0ZW1zLmxlbmd0aDsgaSsrKXtcclxuICAgICAgIGlmIChpICUgNT09IDApe1xyXG4gICAgICAgdGhpcy50b29sdGlwVmFsdWUucHVzaChcIiQjJFwiKTtcclxuICAgICAgIH1cclxuICAgICAgdGhpcy50b29sdGlwVmFsdWUucHVzaChpdGVtc1tpXS5jb250ZW50KTtcclxuICAgIH1cclxuICAgIHRoaXMudG9vbFRpcD0odGhpcy50b29sdGlwVmFsdWUudG9TdHJpbmcoKSkuc3BsaXQoXCIkIyQsXCIpLmpvaW4oXCJcXG5cIik7XHJcbiAgICB0aGlzLnNlbGVjdHM9aXRlbXM7XHJcbiAgICBjb25zdCB0YXJnZXQgPSB7XHJcbiAgICAgIHRvb2x0aXA6IHRoaXMudG9vbHRpcFZhbHVlLnRvU3RyaW5nKClcclxuICAgIH07XHJcbiAgICB0aGlzLlNFTEVDVF9BTEwuZW1pdCh0YXJnZXQpO1xyXG4gIH1cclxuXHJcblxyXG5cclxuICBvbkl0ZW1EZVNlbGVjdChpdGVtOiBhbnkpIHtcclxuICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbb25JdGVtRGVTZWxlY3RdLS0taXRlbSBkZXNzZWxlY3RlZFwiLGl0ZW0pO1xyXG4gICAgY29uc3QgaW5kZXggPSB0aGlzLnNlbGVjdHMuZmluZEluZGV4KGkgPT4gaS52YWx1ZSA9PT0gaXRlbVtcInZhbHVlXCJdKTtcclxuICAgIHRoaXMubG9nZ2VyLmluZm8oXCJbb25JdGVtRGVTZWxlY3RdLS0taXRlbSBkZXNzZWxlY3RlZCBpZGV4XCIsaW5kZXgpO1xyXG4gICAgaWYgKGluZGV4ID4gLTEpIHtcclxuICAgICAgdGhpcy5zZWxlY3RzLnNwbGljZShpbmRleCwgMSk7XHJcbiAgICAgIHRoaXMudG9vbHRpcFZhbHVlLnNwbGljZShpbmRleCwgMSk7XHJcbiAgICB9XHJcbiAgICB0aGlzLnRvb2xUaXA9dGhpcy50b29sdGlwVmFsdWUudG9TdHJpbmcoKTtcclxuICAgIGNvbnN0IHRhcmdldCA9IHtcclxuICAgICAgdG9vbHRpcDogdGhpcy50b29sdGlwVmFsdWUudG9TdHJpbmcoKVxyXG4gICAgfTtcclxuICAgIHRoaXMuSVRFTV9ERVNFTEVDVC5lbWl0KHRhcmdldCk7XHJcbiAgICB0aGlzLmxvZ2dlci5pbmZvKFwiW29uSXRlbURlU2VsZWN0XS0tLW5ldyBkYXRhXCIsdGhpcy5zZWxlY3RzKTtcclxuICB9XHJcblxyXG5cclxuXHJcbiAgb25EZVNlbGVjdEFsbChpdGVtczogYW55KXtcclxuICAgIHRoaXMuc2VsZWN0cz1bXTtcclxuICAgIHRoaXMudG9vbHRpcFZhbHVlPVtdO1xyXG4gICAgdGhpcy50b29sVGlwPXRoaXMudG9vbHRpcFZhbHVlLnRvU3RyaW5nKCk7XHJcbiAgICBjb25zdCB0YXJnZXQgPSB7XHJcbiAgICAgIHRvb2x0aXA6IFwiXCJcclxuICAgIH07XHJcbiAgICB0aGlzLkRFU0VMRUNUX0FMTC5lbWl0KHRhcmdldCk7XHJcbiAgfVxyXG4gICAgXHJcbiAgICAgICBcclxufVxyXG4iXX0=