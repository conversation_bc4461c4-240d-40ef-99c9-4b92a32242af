/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CodemirrorComponent } from '@ctrl/ngx-codemirror';
import * as CodeMirror from 'codemirror';
/**
 * <AUTHOR>
 */
var SwtPrettyPrintTextArea = /** @class */ (function () {
    function SwtPrettyPrintTextArea(elem, cd) {
        this.elem = elem;
        this.cd = cd;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this._scroll = new Function();
        this._focusIn = new Function();
        this._focusOut = new Function();
        this._change = new Function();
        this._text = '';
        this._initialtxt = '';
        this.firstCall = true;
        this.CUSTOM = 99;
        this.id = '';
        // @Input("editable") editable: string;
        this.change_ = new EventEmitter();
        this.focusOut_ = new EventEmitter();
        this.focusIn_ = new EventEmitter();
        this.listCodeWordsInBD = ['P${ACCOUNT_FULLNAME}', 'P${AMOUNT}', 'P${CORRESP_EXT_ID}', 'P${CURRENCY}', 'P${ENTITY_EXT_ID}', 'P${ENTITY_NAME}', 'P${FOLLOWUP_ID}', 'P${MATCH_BALANCE}', 'P${MATCH_DATE}',
            'P${MATCH_ID}', 'P${OUR_REFERENCE}', 'P${USER NAME}', 'P${USERNAME}', 'P${VALUE_DATE}', 'T${azaz}', 'T${CHAR_WORDS}', 'T${template_fr}', 'T${test1}', 'T${test2}',
            'T${test208}', 'T${test', '208_210}', 'T${aaaaa}', 'T${Template1}', 'T${Template2}', 'T${Template3}',
            'T${Template4}', 'T${t}', 'T${test_208210}', 'T${zzzzzzzzzzzztest}', 'T${TEST1}', 'T${testSeif2}',
            'T${Imed}', 'T${testIkram}', 'T${marwen}', 'T${test210}', 'T${test', 'case', '108}'];
        this.options = {
            lineNumbers: false,
            lineWrapping: true,
            readOnly: false,
            theme: 'neat',
            mode: 'codewords',
        };
    }
    /**
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.ngDoCheck = /**
     * @return {?}
     */
    function () {
        if (this.editable == 'true' || this.editable == true) {
            this.options['readOnly'] = false;
        }
        else if (this.editable == 'false' || this.editable == false) {
            this.options['readOnly'] = true;
        }
    };
    /**
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.defineMode('codewords', this.listCodeWordsInBD);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.handleChange = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this._text = event;
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.focusChange = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event == true) {
            this.focusIn_.emit(event);
            if (this.text != this._initialtxt) {
                this.change_.emit(event);
            }
        }
        else if (event == false) {
            this.focusOut_.emit(event);
            if (this.text != this._initialtxt) {
                this.change_.emit(event);
            }
        }
    };
    /**
     * @param {?} language
     * @param {?} keywords
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.defineMode = /**
     * @param {?} language
     * @param {?} keywords
     * @return {?}
     */
    function (language, keywords) {
        // Definir un language dite "codewords"
        // Exemple: https://github.com/converge/brackets-nasm-syntax-highlighter/blob/master/lib/nasm/nasm.js
        // Liens utils:
        // https://codemirror.net/demo/theme.html#3024-day
        // https://github.com/TypeCtrl/ngx-codemirror
        // https://codemirror.net/demo/simplemode.html
        //  keywords = this.listCodeWordsInBD.match(/("[^"]+"|[^"\s]+)/g);
        //  console.log("defineMode---------listCodeWordsInBD", this.listCodeWordsInBD);
        //  console.log("defineMode----------language", language, "keywords", keywords);
        if (language != undefined && keywords != undefined) {
            CodeMirror.defineMode(language, (/**
             * @return {?}
             */
            function () {
                /**
                 * @param {?} str
                 * @return {?}
                 */
                function words(str) {
                    /** @type {?} */
                    var obj = {};
                    /** @type {?} */
                    var words = keywords;
                    for (var i = 0; i < words.length; ++i) {
                        obj[words[i].toLowerCase()] = true;
                    }
                    return obj;
                }
                // instructions http://www.nasm.us/doc/nasmdocb.html
                /** @type {?} */
                var keywordsTab = words(keywords);
                return {
                    startState: /**
                     * @return {?}
                     */
                    function () {
                        return {
                            tokenize: null,
                        };
                    },
                    token: /**
                     * @param {?} stream
                     * @param {?} state
                     * @return {?}
                     */
                    function (stream, state) {
                        if (state.tokenize) {
                            return state.tokenize(stream, state);
                        }
                        /** @type {?} */
                        var cur;
                        /** @type {?} */
                        var ch = stream.next();
                        // labels and sections/segments
                        if (/\S/.test(ch)) {
                            stream.eatWhile(/\S/);
                            cur = stream.current().toLowerCase();
                            // Dans certain cas, le keyword contient de l'espace ! , il faut tout consommer
                            if (cur.indexOf('p${', 0) >= 0 && cur.indexOf('}', 0) < 0) {
                                stream.eatSpace();
                                stream.eatWhile(/\S/);
                                cur = stream.current().toLowerCase();
                            }
                        }
                        if (keywordsTab.propertyIsEnumerable(cur)) {
                            stream.eatWhile(/\w/);
                            return 'keyword';
                        }
                    },
                };
            }));
        }
    };
    /**
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        if (this.codemirror) {
            this.codemirror = this.ngxCodeMirror.codeMirror;
        }
    };
    /**
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.ngAfterViewChecked = /**
     * @return {?}
     */
    function () {
        this.ngxCodeMirror.writeValue(this.text);
    };
    /**
     * @param {?} syntax
     * @param {?=} keywords
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.registerSyntax = /**
     * @param {?} syntax
     * @param {?=} keywords
     * @return {?}
     */
    function (syntax, keywords) {
        if (syntax == 99) {
            this.listCodeWordsInBD = keywords.match(/("[^"]+"|[^"\s]+)/g);
        }
    };
    // /* enabled getter and setter */
    // @Input()
    // set enabled(value: boolean) {
    //     if (typeof (value) !== 'string') {
    //         this._enabled = value;
    //     } else {
    //         if (value + '' === 'true') {
    //             this._enabled = true;
    //         } else {
    //             this._enabled = false;
    //         }
    //     }
    // }
    // get enabled() {
    //     return this._enabled;
    // }
    /* setFocus function */
    // /* enabled getter and setter */
    // @Input()
    // set enabled(value: boolean) {
    //     if (typeof (value) !== 'string') {
    //         this._enabled = value;
    //     } else {
    //         if (value + '' === 'true') {
    //             this._enabled = true;
    //         } else {
    //             this._enabled = false;
    //         }
    //     }
    // }
    // get enabled() {
    //     return this._enabled;
    // }
    /* setFocus function */
    /**
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.setFocus = 
    // /* enabled getter and setter */
    // @Input()
    // set enabled(value: boolean) {
    //     if (typeof (value) !== 'string') {
    //         this._enabled = value;
    //     } else {
    //         if (value + '' === 'true') {
    //             this._enabled = true;
    //         } else {
    //             this._enabled = false;
    //         }
    //     }
    // }
    // get enabled() {
    //     return this._enabled;
    // }
    /* setFocus function */
    /**
     * @return {?}
     */
    function () {
        this.elem.nativeElement.focus();
    };
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        /* height getter and setter */
        set: /* height getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._height = value + 'px';
            }
            else {
                this._height = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        /* width getter and setter */
        set: /* width getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._width = value + 'px';
            }
            else {
                this._width = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "paddingTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._paddingTop;
        },
        /* paddingTop getter and setter */
        set: /* paddingTop getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._paddingTop = value + 'px';
            }
            else {
                this._paddingTop = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "paddingRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._paddingRight;
        },
        /* paddingRight getter and setter */
        set: /* paddingRight getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._paddingRight = value + 'px';
            }
            else {
                this._paddingRight = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "marginLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this._marginLeft;
        },
        /* marginLeft getter and setter */
        set: /* marginLeft getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._marginLeft = value + 'px';
            }
            else {
                this._marginLeft = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "marginBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._marginBottom;
        },
        /* marginBottom getter and setter */
        set: /* marginBottom getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._marginBottom = value + 'px';
            }
            else {
                this._marginBottom = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "marginRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._marginRight;
        },
        /* marginRight getter and setter */
        set: /* marginRight getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._marginRight = value + 'px';
            }
            else {
                this._marginRight = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "marginTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._marginTop;
        },
        /* marginTop getter and setter */
        set: /* marginTop getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._marginTop = value + 'px';
            }
            else {
                this._marginTop = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "paddingLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this._paddingLeft;
        },
        /* paddingLeft getter and setter */
        set: /* paddingLeft getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._paddingLeft = value + 'px';
            }
            else {
                this._paddingLeft = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "paddingBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._paddingBottom;
        },
        /* paddingBottom getter and setter */
        set: /* paddingBottom getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined && value.indexOf('%') === -1) {
                this._paddingBottom = value + 'px';
            }
            else {
                this._paddingBottom = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "scroll", {
        get: /**
         * @return {?}
         */
        function () {
            return this._scroll;
        },
        /* scroll getter and setter */
        set: /* scroll getter and setter */
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._scroll = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "focusOut", {
        get: /**
         * @return {?}
         */
        function () {
            return this._focusOut;
        },
        /* focusOut getter and setter */
        set: /* focusOut getter and setter */
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._focusOut = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "focusIn", {
        get: /**
         * @return {?}
         */
        function () {
            return this._focusIn;
        },
        /* focusIn getter and setter */
        set: /* focusIn getter and setter */
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._focusIn = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "change", {
        get: /**
         * @return {?}
         */
        function () {
            return this._change;
        },
        /* change getter and setter */
        set: /* change getter and setter */
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._change = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            return this._text;
        },
        /* text getter and setter */
        set: /* text getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this._text = value;
            }
            else {
                this._text = '';
            }
            if (this.firstCall) {
                this.originalValue = this._text;
                this.firstCall = false;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPrettyPrintTextArea.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._editable;
        },
        /* editable getter and setter */
        set: /* editable getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === 'string') {
                if (value === 'true') {
                    this._editable = 'true';
                }
                else {
                    this._editable = 'false';
                }
            }
            else {
                this._editable = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    /* Spy change function */
    /* Spy change function */
    /**
     * @param {?} event
     * @return {?}
     */
    SwtPrettyPrintTextArea.prototype.spyChanges = /* Spy change function */
    /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ target: this, value: event });
        }
        else {
            this.onSpyChange.emit({ target: this, value: event });
        }
    };
    SwtPrettyPrintTextArea.decorators = [
        { type: Component, args: [{
                    selector: 'SwtPrettyPrintTextArea',
                    template: "\n      <div class=\"swtPrettyPrintTextArea\"\n      [ngStyle]=\"{\n        'width'         :width,\n        'height'        :height,\n        'padding-top'   :paddingTop,\n        'padding-right' :paddingRight,\n        'padding-bottom':paddingBottom,\n        'padding-left'  :paddingLeft,\n        'margin-top'    :marginTop,\n        'margin-right'  :marginRight,\n        'margin-bottom' :marginBottom,\n        'margin-left'   :marginLeft\n      }\"\n     >\n      <ngx-codemirror #ngxCodeMirror [options]= \"options\" [(ngModel)]=\"contentValue\"\n      (ngModelChange)=\"handleChange($event)\"\n      (focusChange)=\"focusChange($event)\">\n      </ngx-codemirror>\n      </div >\n      ",
                    styles: ["\n      textarea {\n          resize: none;\n                }\n    .swtPrettyPrintTextArea {\n                    -moz-appearance: textfield-multiline;\n                    -webkit-appearance: textarea;\n                    background-color: #FFF;\n                    overflow: auto;\n                    border: 1px solid #A9A9A9;\n                    padding: 5px;\n                    font-size: 11px;\n                    font-weight: bold;\n                    font-family: sans-serif;\n                }\n          "]
                }] }
    ];
    /** @nocollapse */
    SwtPrettyPrintTextArea.ctorParameters = function () { return [
        { type: ElementRef },
        { type: ChangeDetectorRef }
    ]; };
    SwtPrettyPrintTextArea.propDecorators = {
        onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
        onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
        backgroundColor: [{ type: Input }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        id: [{ type: Input, args: ['id',] }],
        toolTip: [{ type: Input, args: ['toolTip',] }],
        enabled: [{ type: Input, args: ['enabled',] }],
        change_: [{ type: Output, args: ['change',] }],
        focusOut_: [{ type: Output, args: ['focusOut',] }],
        focusIn_: [{ type: Output, args: ['focusIn',] }],
        swtPrettyPrintTextArea: [{ type: ViewChild, args: ['swtPrettyPrintTextArea',] }],
        ngxCodeMirror: [{ type: ViewChild, args: ['ngxCodeMirror',] }],
        height: [{ type: Input }],
        width: [{ type: Input }],
        paddingTop: [{ type: Input }],
        paddingRight: [{ type: Input }],
        marginLeft: [{ type: Input }],
        marginBottom: [{ type: Input }],
        marginRight: [{ type: Input }],
        marginTop: [{ type: Input }],
        paddingLeft: [{ type: Input }],
        paddingBottom: [{ type: Input }],
        text: [{ type: Input }],
        editable: [{ type: Input }]
    };
    return SwtPrettyPrintTextArea;
}());
export { SwtPrettyPrintTextArea };
if (false) {
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.originalValue;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.onSpyChange;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.onSpyNoChange;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingTop;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingRight;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingBottom;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._paddingLeft;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginTop;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginRight;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginBottom;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._marginLeft;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._scroll;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._focusIn;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype._initialtxt;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.firstCall;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.CUSTOM;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.contentValue;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.backgroundColor;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.tabIndex;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.id;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.toolTip;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.enabled;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.change_;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.focusOut_;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.focusIn_;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.swtPrettyPrintTextArea;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.ngxCodeMirror;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.codemirror;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.listCodeWordsInBD;
    /** @type {?} */
    SwtPrettyPrintTextArea.prototype.options;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtPrettyPrintTextArea.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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