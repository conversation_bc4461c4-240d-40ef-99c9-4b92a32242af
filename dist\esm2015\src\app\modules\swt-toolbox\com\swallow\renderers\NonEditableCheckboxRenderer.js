/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CellBackgroundColor, CustomCell } from "./cellItemRenderUtilities";
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
export const NonEditableCheckboxRenderer = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let field = columnDef.field;
    // return `<input type="checkbox" value="false" class="editor-checkbox" hideFocus />`;
    /** @type {?} */
    let text;
    /** @type {?} */
    let backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    /** @type {?} */
    let negative = false;
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        style += 'background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (value === 'N' || value === 'false') {
        // non checked
        //text  = '&#x2716;';
        text = `<div class="containerCheckBox" style='${style}' ><input disabled   type='checkbox' value='false' class='formator-checkbox' hideFocus /></div>`;
    }
    else {
        // checked
        //text = '&#x2714;';
        text = `<div class="containerCheckBox" style='${style}' ><input disabled checked  type='checkbox' value='false' class='formator-checkbox' hideFocus /></div>`;
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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