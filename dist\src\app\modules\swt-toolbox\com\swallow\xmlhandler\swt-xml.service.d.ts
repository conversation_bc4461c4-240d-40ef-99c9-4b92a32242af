export declare class XML {
    private xmlString;
    private xpath;
    private dom;
    private xml;
    constructor(xmlString: string);
    /**
     * This method is used to convert the entered String (xmlString) to XML .
     * returns XML
     */
    fromXMLString(): any;
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     */
    toXMLString(): any;
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     */
    toString(): any;
    /**
     * appends xml or string to the existing xml.
     * @param value : it can be string or xml(SwtXml)
     */
    appendChild(value: any): void;
    /**
     *
     * @param xml
     */
    removeChild(xmlToDelete: any): boolean;
    /**
     *
     */
    parent(): any;
    /**
     *
     */
    children(): any;
}
export declare class XMLListCollection {
    private list;
    constructor();
    /**
     * returns string
     */
    toString(): any;
    toXMLString(): any;
}
