/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Observable } from 'rxjs';
import { first, take } from 'rxjs/operators';
import * as moment_ from 'moment-mini';
import { FieldType, OperatorType } from "angular-slickgrid";
/** @type {?} */
var moment = moment_;
/**
 * Simple function to which will loop and create as demanded the number of white spaces,
 * this will be used in the Excel export
 * @param {?} nbSpaces
 * @return {?}
 */
export function addWhiteSpaces(nbSpaces) {
    /** @type {?} */
    var result = '';
    for (var i = 0; i < nbSpaces; i++) {
        result += ' ';
    }
    return result;
}
/**
 * HTML encode using jQuery
 * @param {?} value
 * @return {?}
 */
export function htmlEncode(value) {
    // create a in-memory div, set it's inner text(which j<PERSON><PERSON><PERSON> automatically encodes)
    // then grab the encoded contents back out.  The div never exists on the page.
    return $('<div/>').text(value).html();
}
/**
 * HTML decode using jQuery
 * @param {?} value
 * @return {?}
 */
export function htmlDecode(value) {
    return $('<div/>').html(value).text();
}
/**
 * decode text into html entity
 * @param {?} input
 * @return {?}
 */
export function htmlEntityDecode(input) {
    return input.replace(/&#(\d+);/g, (/**
     * @param {?} match
     * @param {?} dec
     * @return {?}
     */
    function (match, dec) {
        return String.fromCharCode(dec);
    }));
}
/**
 * decode text into html entity
 * @param {?} input
 * @return {?}
 */
export function htmlEntityEncode(input) {
    /** @type {?} */
    var buf = [];
    for (var i = input.length - 1; i >= 0; i--) {
        buf.unshift(['&#', input[i].charCodeAt(), ';'].join(''));
    }
    return buf.join('');
}
/**
 * Compares two arrays to determine if all the items are equal
 * @param {?} a first array
 * @param {?} b second array to compare with a
 * @param {?=} orderMatters
 * @return {?} boolean true if equal, else false
 */
export function arraysEqual(a, b, orderMatters) {
    if (orderMatters === void 0) { orderMatters = false; }
    if (a === b) {
        return true;
    }
    if (!a || !b) {
        return false;
    }
    if (a.length !== b.length) {
        return false;
    }
    if (!orderMatters) {
        a.sort();
        b.sort();
    }
    for (var i = 0; i < a.length; ++i) {
        if (a[i] !== b[i]) {
            return false;
        }
    }
    return true;
}
/**
 * Try casting an input of type Promise | Observable into a Promise type.
 * @template T
 * @param {?} input
 * @param {?=} fromServiceName string representing the caller service name and will be used if we throw a casting problem error
 * @return {?}
 */
export function castToPromise(input, fromServiceName) {
    if (fromServiceName === void 0) { fromServiceName = ''; }
    /** @type {?} */
    var promise = input;
    if (input instanceof Promise) {
        // if it's already a Promise then return it
        return input;
    }
    else if (input instanceof Observable) {
        promise = input.pipe(first()).toPromise();
        if (!(promise instanceof Promise)) {
            promise = input.pipe(take(1)).toPromise();
        }
        if (!(promise instanceof Promise)) {
            throw new Error("Something went wrong, Angular-Slickgrid " + fromServiceName + " is not able to convert the Observable into a Promise.\n        If you are using Angular HttpClient, you could try converting your http call to a Promise with \".toPromise()\"\n        for example::  this.http.post('graphql', { query: graphqlQuery }).toPromise()\n        ");
        }
    }
    return promise;
}
/**
 * Uses the logic function to find an item in an array or returns the default
 * value provided (empty object by default)
 * @param {?} array
 * @param {?} logic
 * @param {?=} defaultVal
 * @return {?} object the found object or default value
 */
export function findOrDefault(array, logic, defaultVal) {
    if (defaultVal === void 0) { defaultVal = {}; }
    return array.find(logic) || defaultVal;
}
/**
 * Take a number (or a string) and display it as a formatted decimal string with defined minimum and maximum decimals
 * @param {?} input
 * @param {?=} minDecimal
 * @param {?=} maxDecimal
 * @return {?}
 */
export function decimalFormatted(input, minDecimal, maxDecimal) {
    if (isNaN(+input)) {
        return input;
    }
    /** @type {?} */
    var minDec = (minDecimal === undefined) ? 2 : minDecimal;
    /** @type {?} */
    var maxDec = (maxDecimal === undefined) ? 2 : maxDecimal;
    /** @type {?} */
    var amount = String(Math.round(+input * Math.pow(10, maxDec)) / Math.pow(10, maxDec));
    if (amount.indexOf('.') < 0) {
        amount += '.';
    }
    while ((amount.length - amount.indexOf('.')) <= minDec) {
        amount += '0';
    }
    return amount;
}
/**
 * From a dot (.) notation find and return a property within an object given a path
 * @param {?} obj
 * @param {?} path
 * @return {?}
 */
export function getDescendantProperty(obj, path) {
    return path.split('.').reduce((/**
     * @param {?} acc
     * @param {?} part
     * @return {?}
     */
    function (acc, part) { return acc && acc[part]; }), obj);
}
/**
 * Get the browser's scrollbar width, this is different to each browser
 * @return {?}
 */
export function getScrollBarWidth() {
    /** @type {?} */
    var $outer = $('<div>').css({ visibility: 'hidden', width: 100, overflow: 'scroll' }).appendTo('body');
    /** @type {?} */
    var widthWithScroll = $('<div>').css({ width: '100%' }).appendTo($outer).outerWidth();
    $outer.remove();
    return Math.ceil(100 - widthWithScroll);
}
/**
 * From a Date FieldType, return it's equivalent moment.js format
 * refer to moment.js for the format standard used: https://momentjs.com/docs/#/parsing/string-format/
 * @param {?} fieldType
 * @return {?}
 */
export function mapMomentDateFormatWithFieldType(fieldType) {
    /** @type {?} */
    var map;
    switch (fieldType) {
        case FieldType.dateTime:
        case FieldType.dateTimeIso:
            map = 'YYYY-MM-DD HH:mm:ss';
            break;
        case FieldType.dateTimeShortIso:
            map = 'YYYY-MM-DD HH:mm';
            break;
        case FieldType.dateTimeIsoAmPm:
            map = 'YYYY-MM-DD hh:mm:ss a';
            break;
        case FieldType.dateTimeIsoAM_PM:
            map = 'YYYY-MM-DD hh:mm:ss A';
            break;
        case FieldType.dateUs:
            map = 'MM/DD/YYYY';
            break;
        case FieldType.dateUsShort:
            map = 'M/D/YY';
            break;
        case FieldType.dateTimeUs:
            map = 'MM/DD/YYYY HH:mm:ss';
            break;
        case FieldType.dateTimeShortUs:
            map = 'MM/DD/YYYY HH:mm';
            break;
        case FieldType.dateTimeUsAmPm:
            map = 'MM/DD/YYYY hh:mm:ss a';
            break;
        case FieldType.dateTimeUsAM_PM:
            map = 'MM/DD/YYYY hh:mm:ss A';
            break;
        case FieldType.dateTimeUsShort:
            map = 'M/D/YY H:m:s';
            break;
        case FieldType.dateTimeUsShortAmPm:
            map = 'M/D/YY h:m:s a';
            break;
        case FieldType.dateUtc:
            map = 'YYYY-MM-DDTHH:mm:ss.SSSZ';
            break;
        case FieldType.date:
        case FieldType.dateIso:
        default:
            map = 'YYYY-MM-DD';
            break;
    }
    return map;
}
/**
 * From a Date FieldType, return it's equivalent Flatpickr format
 * refer to Flatpickr for the format standard used: https://chmln.github.io/flatpickr/formatting/#date-formatting-tokens
 * also note that they seem very similar to PHP format (except for am/pm): http://php.net/manual/en/function.date.php
 * @param {?} fieldType
 * @return {?}
 */
export function mapFlatpickrDateFormatWithFieldType(fieldType) {
    /*
          d: Day of the month, 2 digits with leading zeros    01 to 31
          D: A textual representation of a day    Mon through Sun
          l: (lowercase 'L')  A full textual representation of the day of the week    Sunday through Saturday
          j: Day of the month without leading zeros   1 to 31
          J: Day of the month without leading zeros and ordinal suffix    1st, 2nd, to 31st
          w: Numeric representation of the day of the week    0 (for Sunday) through 6 (for Saturday)
          F: A full textual representation of a month January through December
          m: Numeric representation of a month, with leading zero 01 through 12
          n: Numeric representation of a month, without leading zeros 1 through 12
          M: A short textual representation of a month    Jan through Dec
          U: The number of seconds since the Unix Epoch   1413704993
          y: A two digit representation of a year 99 or 03
          Y: A full numeric representation of a year, 4 digits    1999 or 2003
          H: Hours (24 hours) 00 to 23
          h: Hours    1 to 12
          i: Minutes  00 to 59
          S: Seconds, 2 digits    00 to 59
          s: Seconds  0, 1 to 59
          K: AM/PM    AM or PM
        */
    /** @type {?} */
    var map;
    switch (fieldType) {
        case FieldType.dateTime:
        case FieldType.dateTimeIso:
            map = 'Y-m-d H:i:S';
            break;
        case FieldType.dateTimeIsoAmPm:
            map = 'Y-m-d h:i:S K'; // there is no lowercase in Flatpickr :(
            break;
        case FieldType.dateTimeIsoAM_PM:
            map = 'Y-m-d h:i:S K';
            break;
        case FieldType.dateUs:
            map = 'm/d/Y';
            break;
        case FieldType.dateUsShort:
            map = 'm/d/y';
            break;
        case FieldType.dateTimeUs:
            map = 'm/d/Y H:i:S';
            break;
        case FieldType.dateTimeUsAmPm:
            map = 'm/d/Y h:i:S K'; // there is no lowercase in Flatpickr :(
            break;
        case FieldType.dateTimeUsAM_PM:
            map = 'm/d/Y h:i:s K';
            break;
        case FieldType.dateTimeUsShort:
            map = 'm/d/y H:i:s';
            break;
        case FieldType.dateTimeUsShortAmPm:
            map = 'm/d/y h:i:s K'; // there is no lowercase in Flatpickr :(
            break;
        case FieldType.dateUtc:
            map = 'Z';
            break;
        case FieldType.date:
        case FieldType.dateIso:
        default:
            map = 'Y-m-d';
            break;
    }
    return map;
}
/**
 * Mapper for query operators (ex.: <= is "le", > is "gt")
 * @param {?} operator
 * @return {?} string map
 */
export function mapOperatorType(operator) {
    /** @type {?} */
    var map;
    switch (operator) {
        case '<':
            map = OperatorType.lessThan;
            break;
        case '<=':
            map = OperatorType.lessThanOrEqual;
            break;
        case '>':
            map = OperatorType.greaterThan;
            break;
        case '>=':
            map = OperatorType.greaterThanOrEqual;
            break;
        case '<>':
        case '!=':
        case 'neq':
        case 'NEQ':
            map = OperatorType.notEqual;
            break;
        case '*':
        case '.*':
        case 'startsWith':
            map = OperatorType.startsWith;
            break;
        case '*.':
        case 'endsWith':
            map = OperatorType.endsWith;
            break;
        case '=':
        case '==':
        case 'eq':
        case 'EQ':
            map = OperatorType.equal;
            break;
        case 'in':
        case 'IN':
            map = OperatorType.in;
            break;
        case 'notIn':
        case 'NIN':
        case 'NOT_IN':
            map = OperatorType.notIn;
            break;
        default:
            map = OperatorType.contains;
            break;
    }
    return map;
}
/**
 * Mapper for query operator by a Filter Type
 * For example a multiple-select typically uses 'IN' operator
 * @param {?} fieldType
 * @return {?} string map
 */
export function mapOperatorByFieldType(fieldType) {
    /** @type {?} */
    var map;
    switch (fieldType) {
        case FieldType.string:
        case FieldType.unknown:
            map = OperatorType.contains;
            break;
        case FieldType.float:
        case FieldType.number:
        case FieldType.date:
        case FieldType.dateIso:
        case FieldType.date:
        case FieldType.dateUtc:
        case FieldType.dateTime:
        case FieldType.dateTimeIso:
        case FieldType.dateTimeIsoAmPm:
        case FieldType.dateTimeIsoAM_PM:
        case FieldType.dateUs:
        case FieldType.dateUsShort:
        case FieldType.dateTimeUs:
        case FieldType.dateTimeUsAmPm:
        case FieldType.dateTimeUsAM_PM:
        case FieldType.dateTimeUsShort:
        case FieldType.dateTimeUsShortAmPm:
        case FieldType.dateTimeUsShortAM_PM:
        default:
            map = OperatorType.equal;
            break;
    }
    return map;
}
/**
 * Parse a date passed as a string and return a Date object (if valid)
 * @param {?} inputDateString
 * @param {?} useUtc
 * @return {?} string date formatted
 */
export function parseUtcDate(inputDateString, useUtc) {
    /** @type {?} */
    var date = null;
    //  if (/^[0-9\-\/]*$/.test(inputDateString)) {
    //    // get the UTC datetime with moment.js but we need to decode the value so that it's valid text
    //    const dateString = decodeURIComponent(inputDateString);
    //    const dateMoment = moment(new Date(dateString));
    //    if (dateMoment.isValid() && dateMoment.year().toString().length === 4) {
    //      date = (useUtc) ? dateMoment.utc().format() : dateMoment.format();
    //    }
    //  }
    return date;
}
/**
 * Sanitize, return only the text without HTML tags
 * \@input htmlString
 * @param {?} htmlString
 * @return {?} text
 */
export function sanitizeHtmlToText(htmlString) {
    /** @type {?} */
    var temp = document.createElement('div');
    temp.innerHTML = htmlString;
    return temp.textContent || temp.innerText;
}
/**
 * Title case the complete sentence (upper case first char of each word while changing everything else to lower case)
 * @param {?} string
 * @return {?} string
 */
export function titleCase(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}
/**
 * Converts a string to camel case
 * @param {?} str the string to convert
 * @return {?} the string in camel case
 */
export function toCamelCase(str) {
    return str.replace(/(?:^\w|[A-Z]|\b\w|[\s+\-_\/])/g, (/**
     * @param {?} match
     * @param {?} offset
     * @return {?}
     */
    function (match, offset) {
        // remove white space or hypens or underscores
        if (/[\s+\-_\/]/.test(match)) {
            return '';
        }
        return offset === 0 ? match.toLowerCase() : match.toUpperCase();
    }));
}
/**
 * Converts a string to kebab (hypen) case
 * @param {?} str the string to convert
 * @return {?} the string in kebab case
 */
export function toKebabCase(str) {
    return toCamelCase(str).replace(/([A-Z])/g, '-$1').toLowerCase();
}
/**
 * Takes an input array and makes sure the array has unique values by removing duplicates
 * @param {?} arr
 * @return {?} array output without duplicates
 */
export function uniqueArray(arr) {
    return arr.filter((/**
     * @param {?} item
     * @param {?} index
     * @return {?}
     */
    function (item, index) {
        return arr.indexOf(item) >= index;
    }));
}
/**
 * Unsubscribe all Observables Subscriptions
 * It will return an empty array if it all went well
 * @param {?} subscriptions
 * @return {?}
 */
export function unsubscribeAllObservables(subscriptions) {
    if (Array.isArray(subscriptions)) {
        subscriptions.forEach((/**
         * @param {?} subscription
         * @return {?}
         */
        function (subscription) {
            if (subscription && subscription.unsubscribe) {
                subscription.unsubscribe();
            }
        }));
        subscriptions = [];
    }
    return subscriptions;
}
//# sourceMappingURL=data:application/json;base64,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