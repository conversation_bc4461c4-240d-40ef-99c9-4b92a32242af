/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { StringUtils } from './string-utils.service';
import { HashMap } from './HashMap.service';
import { ExternalInterface } from './external-interface.service';
/**
 * CommonUtil contains utility functions for the Common module
 *
 * This class is the base class of additional operations on string
 *
 * <AUTHOR> JABALLAH, SwallowTech Tunisia  on 05/10/2018
 * @type {?}
 */
var _ = parent;
//@dynamic
var CommonUtil = /** @class */ (function () {
    function CommonUtil() {
    }
    /**
     * format a Date from format1 to format2
     *
     **/
    /**
     * format a Date from format1 to format2
     *
     *
     * @param {?} value
     * @param {?} patternIn
     * @param {?=} patternOut
     * @param {?=} lang
     * @return {?}
     */
    CommonUtil.formatDateFromString = /**
     * format a Date from format1 to format2
     *
     *
     * @param {?} value
     * @param {?} patternIn
     * @param {?=} patternOut
     * @param {?=} lang
     * @return {?}
     */
    function (value, patternIn, patternOut, lang) {
        if (patternOut === void 0) { patternOut = null; }
        if (lang === void 0) { lang = 'en'; }
        // replace
        patternIn = patternIn ? patternIn.toUpperCase().replace(':MM', ':mm') : 'DD/MM/YYYY HH:mm:SS';
        patternOut = patternOut ? patternOut.toUpperCase().replace(':MM', ':mm') : 'YYYY-MM-DD HH:mm:SS';
        return _.formatDate(value, patternIn, patternOut, lang); // ExternalInterface.call("formatDate", value, patternIn,patternOut,lang);
    };
    /**
     * unformatAmount: Supports amountDecimal
     * */
    /**
     * unformatAmount: Supports amountDecimal
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} amountDecimal
     * @return {?}
     */
    CommonUtil.unformatAmount = /**
     * unformatAmount: Supports amountDecimal
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} amountDecimal
     * @return {?}
     */
    function (formattedAmount, amountPattern, amountDecimal) {
        /** @type {?} */
        var unformattedAmount = StringUtils.unformatAmount(formattedAmount, amountPattern);
        // If the amountDecimal is provided, then use the correct decimal separator
        if (amountDecimal) {
            unformattedAmount = unformattedAmount.replace(/(\.|,)/g, amountDecimal);
        }
        return unformattedAmount;
    };
    /**
     * this function is used to get the current entity Id
     * */
    /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    CommonUtil.getCurrentUserId = /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    function () {
        // get the current user Id from JSP
        return ExternalInterface.call('eval', 'userId');
    };
    /**
     * Converts an Oracle ISO format into a date
     * */
    /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    CommonUtil.isoToDate = /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var dateStr = value;
        dateStr = dateStr.replace(/\-/g, '/');
        dateStr = dateStr.replace('T', ' ');
        dateStr = dateStr.replace('Z', ' GMT-0000');
        return new Date(Date.parse(dateStr));
    };
    // implemented by Khalil.B
    /**
     * Converts a date into Oracle ISO format
     * */
    // implemented by Khalil.B
    /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    CommonUtil.dateToIso = 
    // implemented by Khalil.B
    /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    function (value, displaySeconds) {
        if (displaySeconds === void 0) { displaySeconds = true; }
        /** @type {?} */
        var hours = '' + value.getHours();
        if (hours.length < 2)
            hours = '0' + hours;
        /** @type {?} */
        var minutes = '' + value.getMinutes();
        if (minutes.length < 2)
            minutes = '0' + minutes;
        /** @type {?} */
        var seconds = '' + value.getSeconds();
        if (seconds.length < 2)
            seconds = '0' + seconds;
        /** @type {?} */
        var months = '' + (value.getMonth() + 1);
        /** @type {?} */
        var days = '' + value.getDate();
        if (months.length < 2)
            months = '0' + months;
        if (days.length < 2)
            days = '0' + days;
        /** @type {?} */
        var date = value.getFullYear() + '-' + months + '-' + days;
        return (date + ' ' + hours + ':' + minutes + (displaySeconds ? (':' + seconds) : ''));
    };
    /**
     * Converts a string to a Date object
     * */
    /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    CommonUtil.dateFromString = /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    function (dateString, pattern) {
        return new Date(moment(dateString, pattern).toString());
    };
    /**
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    CommonUtil.parseDate = /**
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    function (dateString, pattern) {
        return new Date(moment(dateString, pattern, true).toDate());
    };
    /*
    <AUTHOR> SwallowTech TN ??
      **Format date to specified date format
      **@params: *Date: date to format
                 *patern: output date format
    */
    /*
      <AUTHOR> SwallowTech TN ??
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    /**
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    CommonUtil.formatDate = /*
      <AUTHOR> SwallowTech TN ??
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    /**
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    function (date, patern) {
        /** @type {?} */
        var regex = new RegExp('[^a-z0-9A-Z]');
        /** @type {?} */
        var d = new Date(date);
        /** @type {?} */
        var month = '' + (d.getMonth() + 1);
        /** @type {?} */
        var day = '' + d.getDate();
        /** @type {?} */
        var year = d.getFullYear();
        /** @type {?} */
        var separator = patern[patern.search(regex)];
        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;
        if (patern == 'YYYY' + separator + 'MM' + separator + 'DD') {
            return [year, month, day].join(separator);
        }
        else if (patern == 'DD' + separator + 'MM' + separator + 'YYYY' || patern == 'DDMMYYYY') {
            return [day, month, year].join(separator);
        }
        else if (patern == 'MM' + separator + 'DD' + separator + 'YYYY') {
            return [month, day, year].join(separator);
        }
        else if (patern == 'DD' + separator + 'MMM' + separator + 'YYYY') {
            /** @type {?} */
            var month_names_short = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            month = month_names_short[Number(month) - 1];
            return [day, month, year].join('-');
        }
        else
            return '';
    };
    /**
     * Create dynamic report based on query on S_REPORTS by passing program id , kvparams and report group id
     * */
    /**
     * Create dynamic report based on query on S_REPORTS by passing program id , kvparams and report group id
     *
     * @param {?} programId
     * @param {?} kvParams
     * @param {?} filterSortParams
     * @param {?} reportGroupId
     * @param {?} type
     * @param {?} moduleId
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    CommonUtil.report = /**
     * Create dynamic report based on query on S_REPORTS by passing program id , kvparams and report group id
     *
     * @param {?} programId
     * @param {?} kvParams
     * @param {?} filterSortParams
     * @param {?} reportGroupId
     * @param {?} type
     * @param {?} moduleId
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    function (programId, kvParams, filterSortParams, reportGroupId, type, moduleId, displayFilter, xmlDataSource) {
        if (displayFilter === void 0) { displayFilter = null; }
        if (xmlDataSource === void 0) { xmlDataSource = null; }
        if (filterSortParams != null) {
            if (filterSortParams['_sqlQueryFilter_'] != undefined) {
                kvParams['_sqlQueryFilter_'] = filterSortParams['_sqlQueryFilter_'];
            }
            if (filterSortParams['_sqlQuerySort_'] != undefined) {
                kvParams['_sqlQuerySort_'] = filterSortParams['_sqlQuerySort_'];
            }
            if (filterSortParams['_sqlQueryDirection_'] != undefined) {
                kvParams['_sqlQueryDirection_'] = filterSortParams['_sqlQueryDirection_'];
            }
        }
        /** @type {?} */
        var actionPathReport = 'report!exportData.do?';
        /** @type {?} */
        var kvAsXML = StringUtils.getKVTypeTabAsXML(kvParams, '', '', '');
        /** @type {?} */
        var displayFilterAsXMLStr = StringUtils.getKVTypeTabAsXML(displayFilter, '', '', '').toXMLString();
        /** @type {?} */
        var parentKVParams = kvAsXML.toXMLString();
        /** @type {?} */
        var actionPath = actionPathReport;
        //
        /** @type {?} */
        var actionParams = 'type=' + type;
        actionParams = actionParams + '&action=EXPORT&print=ALL&currentModuleId=' + moduleId;
        actionParams = actionParams + '&programId=' + programId + '&reportGroupId=' + reportGroupId + '&kvParams=' + StringUtils.encode64(parentKVParams);
        actionParams = actionParams + '&displayFilter=' + StringUtils.encode64(displayFilterAsXMLStr);
        //Calls a method from mainflex.jsp file
        //ExternalInterface.call("getReportsAndProgress", actionPath + actionParams, xmlDataSource!=null?StringUtils.encode64(xmlDataSource.toXMLString()):"");
    };
    /**
     * @return {?}
     */
    CommonUtil.getCurrentModuleId = /**
     * @return {?}
     */
    function () {
        // get the current module Id from JSP
        return String(ExternalInterface.call('getCurrentModuleId')).toUpperCase();
    };
    /**
     * this function is used to get the current entity Id
     * */
    /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    CommonUtil.getCurrentEntityId = /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    function () {
        // get the current entity Id from JSP
        return ExternalInterface.call('eval', 'currEntityId');
    };
    /**
     * method that returns a list of updated columns
     * updateOperation will have the format: U(col1)>U(col2)>...>U(coln)
     * */
    /**
     * method that returns a list of updated columns
     * updateOperation will have the format: U(col1)>U(col2)>...>U(coln)
     *
     * @param {?} updateOperation
     * @return {?}
     */
    CommonUtil.getUpdatedColumns = /**
     * method that returns a list of updated columns
     * updateOperation will have the format: U(col1)>U(col2)>...>U(coln)
     *
     * @param {?} updateOperation
     * @return {?}
     */
    function (updateOperation) {
        /** @type {?} */
        var result = updateOperation.split(/>+/);
        /** @type {?} */
        var value = '';
        for (var i = 0; i < result.length; i++) {
            value = result[i];
            value = value.substr(2, value.length - 3);
            result[i] = value;
        }
        return result;
    };
    /**
     * Returns the first occurrence text for a given XML and tag name
     * */
    /**
     * Returns the first occurrence text for a given XML and tag name
     *
     * @param {?} rowData
     * @param {?} tagName
     * @return {?}
     */
    CommonUtil.getTextByTagName = /**
     * Returns the first occurrence text for a given XML and tag name
     *
     * @param {?} rowData
     * @param {?} tagName
     * @return {?}
     */
    function (rowData, tagName) {
        try {
            /** @type {?} */
            var result = (rowData != null && rowData != undefined && rowData[tagName] != undefined) ? rowData[tagName] : null;
            return result;
        }
        catch (error) {
            console.error('method [getTextByTagName] error : ', error);
        }
    };
    CommonUtil.changes = new HashMap();
    CommonUtil.spyTimers = new HashMap();
    CommonUtil.CDBegin = '<!' + '[CDATA[';
    CommonUtil.CDEnd = ']]' + '>';
    CommonUtil.ignoredObjects = new Array();
    // Containers spying refresh rate
    CommonUtil.SPY_TIME_INTERVAL = 1000;
    CommonUtil.gridImages /*Of ByteArray*/ = [];
    //    private static  base64Dec:Base64Decoder = new Base64Decoder();
    //Httpservice iable
    //    public static  inputData:HTTPComms=new HTTPComms();
    // Current user's upload location
    CommonUtil.USER_UPLOAD_PATH = '/uploads/' + CommonUtil.getCurrentUserId();
    CommonUtil.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    CommonUtil.ctorParameters = function () { return []; };
    return CommonUtil;
}());
export { CommonUtil };
if (false) {
    /**
     * @type {?}
     * @private
     */
    CommonUtil.log;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.changes;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.spyTimers;
    /** @type {?} */
    CommonUtil.CDBegin;
    /** @type {?} */
    CommonUtil.CDEnd;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.ignoredObjects;
    /** @type {?} */
    CommonUtil.SPY_TIME_INTERVAL;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.gridImages;
    /** @type {?} */
    CommonUtil.USER_UPLOAD_PATH;
    /**
     * this function is used to get the current module Id
     *
     * @type {?}
     */
    CommonUtil.function;
}
//# sourceMappingURL=data:application/json;base64,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