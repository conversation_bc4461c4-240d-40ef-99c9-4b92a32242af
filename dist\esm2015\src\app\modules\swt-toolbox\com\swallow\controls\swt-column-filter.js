/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { OperatorType, unsubscribeAllObservables, castToPromise, getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
import { Subject } from 'rxjs';
/** @type {?} */
const o = DOMPurify_;
export class SwtColumnFilter {
    /**
     * Initialize the Filter
     * @param {?} translate
     * @param {?} collectionService
     */
    constructor(translate, collectionService) {
        this.translate = translate;
        this.collectionService = collectionService;
        this.isFilled = false;
        this.lastSelectedValue = undefined;
        this.enableTranslateLabel = false;
        this.subscriptions = [];
        this.scroll = false;
        this._clearFilterTriggered = false;
        this.FilterInputSearch = false;
        this._shouldTriggerQuery = true;
        this.isOpened = false;
        this.checkboxContainer = null;
    }
    /**
     * @return {?}
     */
    refreshFilterValues() {
        if (this.columnFilter) {
            /** @type {?} */
            const newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // Ensure clear button exists after refresh
            if (this.isMultipleSelect) {
                this.ensureClearButtonExists();
            }
        }
    }
    /**
     * Getter for the Column Filter itself
     * @protected
     * @return {?}
     */
    get columnFilter() {
        return this.columnDef && this.columnDef.filter;
    }
    /**
     * Getter for the Collection Options
     * @protected
     * @return {?}
     */
    get collectionOptions() {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.collectionOptions;
    }
    /**
     * Getter for the Custom Structure if exist
     * @protected
     * @return {?}
     */
    get customStructure() {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.customStructure;
    }
    /**
     * Getter for the Grid Options pulled through the Grid Object
     * @protected
     * @return {?}
     */
    get gridOptions() {
        return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
    }
    /**
     * Getter for the filter operator
     * @return {?}
     */
    get operator() {
        if (this.columnDef && this.columnDef.filter && this.columnDef.filter.operator) {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.operator;
        }
        return this.isMultipleSelect ? OperatorType.in : OperatorType.equal;
    }
    /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    init(args) {
        try {
            this.grid = args.grid;
            this.callback = args.callback;
            this.columnDef = args.columnDef;
            this.searchTerms = args.searchTerms || [];
            this.isMultipleSelect = this.columnDef['FilterType'] == "MultipleSelect" ? true : false;
            this.FilterInputSearch = this.columnDef['FilterInputSearch'];
            this.setFilterOptions();
            if (!this.grid || !this.columnDef || !this.columnFilter || (!this.columnFilter.collection && !this.columnFilter.collectionAsync)) {
                throw new Error(`[Angular-SlickGrid] You need to pass a "collection" (or "collectionAsync") for the MultipleSelect/SingleSelect Filter to work correctly. Also each option should include a value/label pair (or value/labelKey when using Locale). For example:: { filter: model: Filters.multipleSelect, collection: [{ value: true, label: 'True' }, { value: false, label: 'False'}] }`);
            }
            this.enableTranslateLabel = this.columnFilter.enableTranslateLabel;
            this.labelName = this.customStructure && this.customStructure.label || 'label';
            this.labelPrefixName = this.customStructure && this.customStructure.labelPrefix || 'labelPrefix';
            this.labelSuffixName = this.customStructure && this.customStructure.labelSuffix || 'labelSuffix';
            this.optionLabel = this.customStructure && this.customStructure.optionLabel || 'value';
            this.valueName = this.customStructure && this.customStructure.value || 'value';
            if (this.enableTranslateLabel && (!this.translate || typeof this.translate.instant !== 'function')) {
                throw new Error(`[select-editor] The ngx-translate TranslateService is required for the Select Filter to work correctly`);
            }
            // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
            // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
            /** @type {?} */
            const newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // on every Filter which have a "collection" or a "collectionAsync"
            // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
            // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
            /** @type {?} */
            const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
            if (collectionAsync) {
                this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
            }
            // step 3, subscribe to the keyup event and run the callback when that happens
            this.$filterElm.keyup((/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                /** @type {?} */
                let value = e && e.target && e.target.value || '';
                /** @type {?} */
                const enableWhiteSpaceTrim = this.gridOptions.enableFilterTrimWhiteSpace || this.columnFilter.enableTrimWhiteSpace;
                if (typeof value === 'string' && enableWhiteSpaceTrim) {
                    value = value.trim();
                }
                if (this._clearFilterTriggered) {
                    this.callback(e, { columnDef: this.columnDef, clearFilterTriggered: this._clearFilterTriggered, shouldTriggerQuery: this._shouldTriggerQuery });
                    this.$filterElm.removeClass('filled');
                }
                else {
                    value === '' ? this.$filterElm.removeClass('filled') : this.$filterElm.addClass('filled');
                    this.callback(e, { columnDef: this.columnDef, searchTerms: [value], shouldTriggerQuery: this._shouldTriggerQuery });
                }
                // reset both flags for next use
                this._clearFilterTriggered = false;
                this._shouldTriggerQuery = true;
            }));
        }
        catch (error) {
            console.error('method [ init] error :', error);
        }
    }
    /**
     * @return {?}
     */
    refreshHeaderOnly() {
        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        /** @type {?} */
        const newCollection = this.columnFilter.collection || [];
        this.renderDomElement(newCollection);
        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        /** @type {?} */
        const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if (collectionAsync) {
            this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
        }
        // Ensure clear button exists after header refresh
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    clear(shouldTriggerQuery = true) {
        console.log("run clear function !");
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            // For multiselect, we need to clear selections using the multipleSelect API
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                this.$filterElm.multipleSelect('setSelects', []);
            }
            else {
                this.$filterElm.val('');
            }
            this.searchTerms = [];
            // For multiselect, trigger the onClose event which will call the callback
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                // Directly call the callback to clear the filter
                this.callback(undefined, {
                    columnDef: this.columnDef,
                    operator: this.operator,
                    searchTerms: [],
                    shouldTriggerQuery: true
                });
                // Remove filled class if present
                this.$filterElm.removeClass('filled');
            }
            else {
                // For regular input, trigger keyup which will call the callback
                this.$filterElm.trigger('keyup');
            }
        }
    }
    /**
     * destroy the filter
     * @return {?}
     */
    destroy() {
        if (this.$filterElm) {
            // remove event watcher
            this.$filterElm.off().remove();
        }
        this.$filterElm.multipleSelect('destroy');
        // also dispose of all Subscriptions
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    }
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    setValues(values) {
        if (values) {
            values = Array.isArray(values) ? values : [values];
            this.$filterElm.multipleSelect('setSelects', values);
        }
    }
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    filterCollection(inputCollection) {
        /** @type {?} */
        let outputCollection = inputCollection;
        // user might want to filter certain items of the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionFilterBy) {
            /** @type {?} */
            const filterBy = this.columnFilter.collectionFilterBy;
            /** @type {?} */
            const filterCollectionBy = this.columnFilter.collectionOptions && this.columnFilter.collectionOptions.filterResultAfterEachPass || null;
            outputCollection = this.collectionService.filterCollection(outputCollection, filterBy, filterCollectionBy);
        }
        return outputCollection;
    }
    /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    sortCollection(inputCollection) {
        /** @type {?} */
        let outputCollection = inputCollection;
        // user might want to sort the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionSortBy) {
            /** @type {?} */
            const sortBy = this.columnFilter.collectionSortBy;
            outputCollection = this.collectionService.sortCollection(this.columnDef, outputCollection, sortBy, this.enableTranslateLabel);
        }
        return outputCollection;
    }
    /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    renderOptionsAsync(collectionAsync) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            /** @type {?} */
            let awaitedCollection = [];
            if (collectionAsync) {
                awaitedCollection = yield castToPromise(collectionAsync);
                this.renderDomElementFromCollectionAsync(awaitedCollection);
                // because we accept Promises & HttpClient Observable only execute once
                // we will re-create an RxJs Subject which will replace the "collectionAsync" which got executed once anyway
                // doing this provide the user a way to call a "collectionAsync.next()"
                this.createCollectionAsyncSubject();
            }
        });
    }
    /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    createCollectionAsyncSubject() {
        /** @type {?} */
        const newCollectionAsync = new Subject();
        this.columnFilter.collectionAsync = newCollectionAsync;
        this.subscriptions.push(newCollectionAsync.subscribe((/**
         * @param {?} collection
         * @return {?}
         */
        collection => this.renderDomElementFromCollectionAsync(collection))));
    }
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    renderDomElementFromCollectionAsync(collection) {
        if (this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('Something went wrong while trying to pull the collection from the "collectionAsync" call in the Select Filter, the collection is not a valid array.');
        }
        // copy over the array received from the async call to the "collection" as the new collection to use
        // this has to be BEFORE the `collectionObserver().subscribe` to avoid going into an infinite loop
        this.columnFilter.collection = collection;
        // recreate Multiple Select after getting async collection
        this.renderDomElement(collection);
        // Ensure clear button exists after async collection update
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }
    /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    renderDomElement(collection) {
        if (!Array.isArray(collection) && this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('The "collection" passed to the Select Filter is not a valid array');
        }
        // user can optionally add a blank entry at the beginning of the collection
        if (this.collectionOptions && this.collectionOptions.addBlankEntry) {
            collection.unshift(this.createBlankEntry());
        }
        /** @type {?} */
        let newCollection = collection;
        // user might want to filter and/or sort certain items of the collection
        newCollection = this.filterCollection(newCollection);
        newCollection = this.sortCollection(newCollection);
        // step 1, create HTML string template
        /** @type {?} */
        const filterTemplate = this.buildTemplateHtmlString(newCollection, this.searchTerms);
        // step 2, create the DOM Element of the filter & pre-load search terms
        // also subscribe to the onClose event
        this.createDomElement(filterTemplate);
    }
    /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    buildTemplateHtmlString(optionCollection, searchTerms) {
        /** @type {?} */
        let options = '';
        /** @type {?} */
        const fieldId = this.columnDef && this.columnDef.id;
        /** @type {?} */
        const separatorBetweenLabels = this.collectionOptions && this.collectionOptions.separatorBetweenTextLabels || '';
        /** @type {?} */
        const isRenderHtmlEnabled = this.columnFilter && this.columnFilter.enableRenderHtml || false;
        /** @type {?} */
        const sanitizedOptions = this.gridOptions && this.gridOptions.sanitizeHtmlOptions || {};
        // collection could be an Array of Strings OR Objects
        if (optionCollection.every((/**
         * @param {?} x
         * @return {?}
         */
        x => typeof x === 'string'))) {
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            (option) => {
                /** @type {?} */
                const selected = (searchTerms.findIndex((/**
                 * @param {?} term
                 * @return {?}
                 */
                (term) => term === option)) >= 0) ? 'selected' : '';
                options += `<option value="${option}" label="${option}" ${selected}>${option}</option>`;
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    this.isFilled = true;
                }
            }));
        }
        else {
            // array of objects will require a label/value pair unless a customStructure is passed
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            (option) => {
                if (!option || (option[this.labelName] === undefined && option.labelKey === undefined)) {
                    throw new Error(`[select-filter] A collection with value/label (or value/labelKey when using Locale) is required to populate the Select list, for example:: { filter: model: Filters.multipleSelect, collection: [ { value: '1', label: 'One' } ]')`);
                }
                /** @type {?} */
                const labelKey = (/** @type {?} */ ((option.labelKey || option[this.labelName])));
                /** @type {?} */
                const selected = (searchTerms.length > 0) ? 'selected' : '';
                /** @type {?} */
                const labelText = ((option.labelKey || this.enableTranslateLabel) && labelKey) ? this.translate.instant(labelKey || ' ') : labelKey;
                /** @type {?} */
                let prefixText = option[this.labelPrefixName] || '';
                /** @type {?} */
                let suffixText = option[this.labelSuffixName] || '';
                /** @type {?} */
                let optionLabel = option[this.optionLabel] || '';
                optionLabel = optionLabel.toString().replace(/\"/g, '\''); // replace double quotes by single quotes to avoid interfering with regular html
                // also translate prefix/suffix if enableTranslateLabel is true and text is a string
                prefixText = (this.enableTranslateLabel && prefixText && typeof prefixText === 'string') ? this.translate.instant(prefixText || ' ') : prefixText;
                suffixText = (this.enableTranslateLabel && suffixText && typeof suffixText === 'string') ? this.translate.instant(suffixText || ' ') : suffixText;
                optionLabel = (this.enableTranslateLabel && optionLabel && typeof optionLabel === 'string') ? this.translate.instant(optionLabel || ' ') : optionLabel;
                // add to a temp array for joining purpose and filter out empty text
                /** @type {?} */
                const tmpOptionArray = [prefixText, labelText, suffixText].filter((/**
                 * @param {?} text
                 * @return {?}
                 */
                (text) => text));
                /** @type {?} */
                let optionText = tmpOptionArray.join(separatorBetweenLabels);
                // if user specifically wants to render html text, he needs to opt-in else it will stripped out by default
                // also, the 3rd party lib will saninitze any html code unless it's encoded, so we'll do that
                if (isRenderHtmlEnabled) {
                    // sanitize any unauthorized html tags like script and others
                    // for the remaining allowed tags we'll permit all attributes
                    /** @type {?} */
                    const sanitizedText = DOMPurify_.sanitize(optionText, sanitizedOptions);
                    optionText = htmlEncode(sanitizedText);
                }
                // html text of each select option
                options += `<option value="${option[this.valueName]}" label="${optionLabel}" ${selected}>${optionText}</option>`;
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    this.isFilled = true;
                }
            }));
        }
        return `<select class="ms-filter search-filter filter-${fieldId}" ${this.isMultipleSelect ? 'multiple="multiple"' : ''}>${options}</select>`;
    }
    /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    createBlankEntry() {
        /** @type {?} */
        const blankEntry = {
            [this.labelName]: '',
            [this.valueName]: ''
        };
        if (this.labelPrefixName) {
            blankEntry[this.labelPrefixName] = '';
        }
        if (this.labelSuffixName) {
            blankEntry[this.labelSuffixName] = '';
        }
        return blankEntry;
    }
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    createDomElement(filterTemplate) {
        /** @type {?} */
        const fieldId = this.columnDef && this.columnDef.id;
        // provide the name attribute to the DOM element which will be needed to auto-adjust drop position (dropup / dropdown)
        this.elementName = `filter-${fieldId}`;
        this.defaultOptions.name = this.elementName;
        /** @type {?} */
        const $headerElm = this.grid.getHeaderColumn(fieldId);
        // create the DOM element & add an ID and filter class
        this.$filterElm = $(filterTemplate);
        if (typeof this.$filterElm.multipleSelect !== 'function') {
            throw new Error(`multiple-select.js was not found, make sure to modify your "angular-cli.json" file and include "../node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js" and it's css or SASS file`);
        }
        this.$filterElm.attr('id', this.elementName);
        this.$filterElm.data('columnId', fieldId);
        // if there's a search term, we will add the "filled" class for styling purposes
        if (this.isFilled) {
            this.$filterElm.addClass('filled');
        }
        // append the new DOM element to the header row
        if (this.$filterElm && typeof this.$filterElm.appendTo === 'function') {
            this.$filterElm.appendTo($headerElm);
            $('.slick-header-column > .ms-parent').click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                event.stopPropagation();
            }));
        }
        // merge options & attach multiSelect
        /** @type {?} */
        const elementOptions = Object.assign({}, this.defaultOptions, this.columnFilter.filterOptions);
        this.filterElmOptions = Object.assign({}, this.defaultOptions, elementOptions);
        this.$filterElm = this.$filterElm.multipleSelect(this.filterElmOptions);
        // Ensure clear button is added for multiple select filters after DOM is ready
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method is called both during DOM creation and when the dropdown opens
     * to handle cases where the grid is refreshed or server-side sorting occurs
     * @private
     * @return {?}
     */
    ensureClearButtonExists() {
        if (!this.isMultipleSelect) {
            return;
        }
        // Use a more robust approach to find the checkbox container
        // Try multiple times with increasing delays to handle async DOM updates
        /** @type {?} */
        const attempts = [0, 50, 100, 200, 500];
        // milliseconds
        /** @type {?} */
        const tryAddClearButton = (/**
         * @param {?} attemptIndex
         * @return {?}
         */
        (attemptIndex) => {
            if (attemptIndex >= attempts.length) {
                console.warn('Failed to add clear button after all attempts for column:', this.columnDef.id);
                return;
            }
            setTimeout((/**
             * @return {?}
             */
            () => {
                // Find the container using multiple selectors to be more robust
                /** @type {?} */
                let container = $(`div[name=filter-${this.columnDef.id}]`);
                // If not found, try alternative selectors
                if (!container.length) {
                    container = $(`.ms-drop[data-name=filter-${this.columnDef.id}]`);
                }
                if (!container.length) {
                    container = $(`.ms-drop:has(.ms-choice[data-name=filter-${this.columnDef.id}])`);
                }
                // If container exists and clear button doesn't exist, add it
                if (container.length && container.find('.clear-filter-btn').length === 0) {
                    // Create clear filter button with an inline SVG icon on the right
                    /** @type {?} */
                    const clearBtn = $(`<button class="ms-ok-button clear-filter-btn">
                            <span style="display: inline-flex; align-items: center;">
                                Clear Filter
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-left: 5px;">
                                    <path d="M3 4h18l-7 8v8h-4v-8l-7-8z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                                    <path d="M5 5L19 19" stroke="red" stroke-width="2"/>
                                </svg>
                            </span>
                        </button>`);
                    // Insert at the very beginning of the dropdown container
                    container.prepend(clearBtn);
                    // Add click handler to clear button
                    clearBtn.on('click', (/**
                     * @param {?} e
                     * @return {?}
                     */
                    (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        // Call the clear method
                        this.clear(true);
                        // Close the dropdown menu
                        if (this.$filterElm && this.$filterElm.multipleSelect) {
                            this.$filterElm.multipleSelect('close');
                        }
                    }));
                    console.log('Clear button successfully added for column:', this.columnDef.id);
                }
                else if (!container.length) {
                    // Container not found, try next attempt
                    tryAddClearButton(attemptIndex + 1);
                }
                // If container exists but clear button already exists, we're done
            }), attempts[attemptIndex]);
        });
        // Start the first attempt
        tryAddClearButton(0);
    }
    /**
     * @private
     * @return {?}
     */
    setFilterOptions() {
        try {
            /** @type {?} */
            const clickHandler = (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                /** @type {?} */
                const clickedCheckbox = event.target;
                /** @type {?} */
                const name = clickedCheckbox.dataset ? clickedCheckbox.dataset.name : "";
                if (this.checkboxContainer && clickedCheckbox.value === "(NOT EMPTY)") {
                    this.checkboxContainer.find("input[type=checkbox][value='(EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && clickedCheckbox.value === "(EMPTY)") {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && name.includes("selectAllfilter")) {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                // Add your desired code here to handle the checkbox click event
            });
            /** @type {?} */
            const options = {
                autoAdjustDropHeight: true,
                autoAdjustDropPosition: true,
                autoAdjustDropWidthByTextSize: true,
                container: 'body',
                filter: this.FilterInputSearch,
                maxHeight: 275,
                minWidth: this.columnDef.width,
                //-Fix M6549:try to enhance the design of the filter in case of short values (case of sign).
                filterAcceptOnEnter: true,
                single: !this.isMultipleSelect,
                //animate: 'slide',
                textTemplate: (/**
                 * @param {?} $elm
                 * @return {?}
                 */
                ($elm) => {
                    // render HTML code or not, by default it is sanitized and won't be rendered
                    /** @type {?} */
                    const isRenderHtmlEnabled = this.columnDef && this.columnDef.filter && this.columnDef.filter.enableRenderHtml || false;
                    return isRenderHtmlEnabled ? $elm.text() : $elm.html();
                }),
                onClose: (/**
                 * @return {?}
                 */
                () => {
                    try {
                        //console.log('-----onClose----------', this.elementName);
                        // we will subscribe to the onClose event for triggering our callback
                        // also add/remove "filled" class for styling purposes
                        if ((!this.isMultipleSelect && this.lastSelectedValue != undefined) || this.isMultipleSelect) {
                            /** @type {?} */
                            let selectedItems = this.$filterElm.multipleSelect('getSelects');
                            if (Array.isArray(selectedItems) && selectedItems.length > 0 && this.lastSelectedValue != this.columnDef.params.grid.all) {
                                this.isFilled = true;
                                this.$filterElm.addClass('filled').siblings('div .search-filter').addClass('filled');
                            }
                            else {
                                selectedItems = [];
                                this.isFilled = false;
                                this.$filterElm.removeClass('filled').siblings('div .search-filter').removeClass('filled');
                            }
                            //-Fix M6549:Filter with an empty string doesn't exist.
                            if (selectedItems.length == 1 && selectedItems[0] == '')
                                selectedItems.push('');
                            this.callback(undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: selectedItems, shouldTriggerQuery: true });
                            $(document).off('click');
                        }
                        if (event)
                            event.stopPropagation();
                        if (this.checkboxContainer)
                            this.checkboxContainer.find("input[type=checkbox]").off("click", clickHandler);
                    }
                    catch (error) {
                        console.error(' error', error);
                    }
                }),
                onOpen: (/**
                 * @return {?}
                 */
                () => {
                    console.log('-----onOpen----------', this.columnDef.width);
                    if (!this.isMultipleSelect) {
                        console.log('02020');
                        this.lastSelectedValue = undefined;
                        //console.log('-----onOpen----------');
                        $("div[name^=filter-]").each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        (index, item) => {
                            /** @type {?} */
                            let name = $(item).attr('name');
                            if (name != this.elementName && $(item).css('display') == "block") {
                                //console.log('-----onOpen---------- slideUp ')
                                $(item).slideUp();
                            }
                        }));
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            $("div[name=filter-" + this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        /** @type {?} */
                        var ul = $($($("div[name=filter-" + this.columnDef.id + "]").children()[0]));
                        $(document).on('click', (/**
                         * @param {?} event
                         * @return {?}
                         */
                        (event) => {
                            /** @type {?} */
                            var target = $(event.target);
                            if (!target.is(ul[0])) {
                                $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                                 * @return {?}
                                 */
                                () => {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        (e) => {
                            $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            () => {
                                $(document).off('click');
                            }));
                        }));
                        event.stopPropagation();
                    }
                    else {
                        this.checkboxContainer = $("div[name=filter-" + this.columnDef.id + "]");
                        // Ensure clear button exists using the centralized method
                        this.ensureClearButtonExists();
                        // Add a small delay to ensure the DOM is ready for checkbox handlers
                        setTimeout((/**
                         * @return {?}
                         */
                        () => {
                            // Attach the click event handler to checkboxes
                            if (this.checkboxContainer) {
                                this.checkboxContainer.find("input[type=checkbox]").click(clickHandler);
                            }
                        }), 50);
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            // console.log("🚀 ~ file: swt-column-filter.ts:591 ~ setFilterOptions ~ newposLeft:", newposLeft)
                            $("div[name=filter-" + this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        //-Fix M6549: Select a filter then scroll left, the filter is still open.
                        this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        (e) => {
                            $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            () => {
                                $(document).off('click');
                            }));
                        }));
                        $('div[name^="filter-"]').each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        (index, item) => {
                            /** @type {?} */
                            const name = $(item).attr('name');
                            if (name != "filter-" + this.columnDef.id && $(item).css('display') == "block") {
                                $(item).slideUp((/**
                                 * @return {?}
                                 */
                                () => {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        event.stopPropagation();
                    }
                }),
                onClick: (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.lastSelectedValue = event.label;
                    //Commented is not needed check if it's working fine 
                    /*if ( event.label == this.columnDef.params.grid.all ) {
                        if ( !this.columnDef.params.grid.paginationComponent.realPagination && this.columnDef.params.grid.GroupId == null){
                            this.clear(true);
                        }
                    }*/
                })
            };
            if (this.isMultipleSelect) {
                options.single = false;
                options.okButton = true;
                options.addTitle = true; // show tooltip of all selected items while hovering the filter
                options.countSelected = this.translate.instant('X_OF_Y_SELECTED');
                options.allSelected = this.translate.instant('ALL_SELECTED');
                options.selectAllText = this.translate.instant('ALL');
                options.selectAllDelimiter = ['', '']; // remove default square brackets of default text "[Select All]" => "Select All"
            }
            this.defaultOptions = options;
        }
        catch (error) {
            console.error('method [setFilterOptions] error :', error);
        }
    }
}
if (false) {
    /**
     * DOM Element Name, useful for auto-detecting positioning (dropup / dropdown)
     * @type {?}
     */
    SwtColumnFilter.prototype.elementName;
    /**
     * Filter Multiple-Select options
     * @type {?}
     */
    SwtColumnFilter.prototype.filterElmOptions;
    /**
     * The JQuery DOM element
     * @type {?}
     */
    SwtColumnFilter.prototype.$filterElm;
    /** @type {?} */
    SwtColumnFilter.prototype.grid;
    /** @type {?} */
    SwtColumnFilter.prototype.searchTerms;
    /** @type {?} */
    SwtColumnFilter.prototype.columnDef;
    /** @type {?} */
    SwtColumnFilter.prototype.callback;
    /** @type {?} */
    SwtColumnFilter.prototype.defaultOptions;
    /** @type {?} */
    SwtColumnFilter.prototype.isFilled;
    /** @type {?} */
    SwtColumnFilter.prototype.lastSelectedValue;
    /** @type {?} */
    SwtColumnFilter.prototype.labelName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelPrefixName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelSuffixName;
    /** @type {?} */
    SwtColumnFilter.prototype.optionLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.valueName;
    /** @type {?} */
    SwtColumnFilter.prototype.enableTranslateLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.scroll;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._clearFilterTriggered;
    /** @type {?} */
    SwtColumnFilter.prototype.isMultipleSelect;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.FilterInputSearch;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.isOpened;
    /** @type {?} */
    SwtColumnFilter.prototype.checkboxContainer;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.translate;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.collectionService;
}
//# sourceMappingURL=data:application/json;base64,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