/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { OperatorType, unsubscribeAllObservables, castToPromise, getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
import { Subject } from 'rxjs';
/** @type {?} */
const o = DOMPurify_;
export class SwtColumnFilter {
    /**
     * Initialize the Filter
     * @param {?} translate
     * @param {?} collectionService
     */
    constructor(translate, collectionService) {
        this.translate = translate;
        this.collectionService = collectionService;
        this.isFilled = false;
        this.lastSelectedValue = undefined;
        this.enableTranslateLabel = false;
        this.subscriptions = [];
        this.scroll = false;
        this._clearFilterTriggered = false;
        this.FilterInputSearch = false;
        this._shouldTriggerQuery = true;
        this.isOpened = false;
        this.checkboxContainer = null;
    }
    /**
     * @return {?}
     */
    refreshFilterValues() {
        if (this.columnFilter) {
            /** @type {?} */
            const newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // Ensure clear button exists after refresh
            if (this.isMultipleSelect) {
                this.ensureClearButtonExists();
            }
        }
    }
    /**
     * Getter for the Column Filter itself
     * @protected
     * @return {?}
     */
    get columnFilter() {
        return this.columnDef && this.columnDef.filter;
    }
    /**
     * Getter for the Collection Options
     * @protected
     * @return {?}
     */
    get collectionOptions() {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.collectionOptions;
    }
    /**
     * Getter for the Custom Structure if exist
     * @protected
     * @return {?}
     */
    get customStructure() {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.customStructure;
    }
    /**
     * Getter for the Grid Options pulled through the Grid Object
     * @protected
     * @return {?}
     */
    get gridOptions() {
        return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
    }
    /**
     * Getter for the filter operator
     * @return {?}
     */
    get operator() {
        if (this.columnDef && this.columnDef.filter && this.columnDef.filter.operator) {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.operator;
        }
        return this.isMultipleSelect ? OperatorType.in : OperatorType.equal;
    }
    /**
     * Initialize the filter template
     * @param {?} args
     * @return {?}
     */
    init(args) {
        try {
            this.grid = args.grid;
            this.callback = args.callback;
            this.columnDef = args.columnDef;
            this.searchTerms = args.searchTerms || [];
            this.isMultipleSelect = this.columnDef['FilterType'] == "MultipleSelect" ? true : false;
            this.FilterInputSearch = this.columnDef['FilterInputSearch'];
            this.setFilterOptions();
            if (!this.grid || !this.columnDef || !this.columnFilter || (!this.columnFilter.collection && !this.columnFilter.collectionAsync)) {
                throw new Error(`[Angular-SlickGrid] You need to pass a "collection" (or "collectionAsync") for the MultipleSelect/SingleSelect Filter to work correctly. Also each option should include a value/label pair (or value/labelKey when using Locale). For example:: { filter: model: Filters.multipleSelect, collection: [{ value: true, label: 'True' }, { value: false, label: 'False'}] }`);
            }
            this.enableTranslateLabel = this.columnFilter.enableTranslateLabel;
            this.labelName = this.customStructure && this.customStructure.label || 'label';
            this.labelPrefixName = this.customStructure && this.customStructure.labelPrefix || 'labelPrefix';
            this.labelSuffixName = this.customStructure && this.customStructure.labelSuffix || 'labelSuffix';
            this.optionLabel = this.customStructure && this.customStructure.optionLabel || 'value';
            this.valueName = this.customStructure && this.customStructure.value || 'value';
            if (this.enableTranslateLabel && (!this.translate || typeof this.translate.instant !== 'function')) {
                throw new Error(`[select-editor] The ngx-translate TranslateService is required for the Select Filter to work correctly`);
            }
            // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
            // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
            /** @type {?} */
            const newCollection = this.columnFilter.collection || [];
            this.renderDomElement(newCollection);
            // on every Filter which have a "collection" or a "collectionAsync"
            // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
            // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
            /** @type {?} */
            const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
            if (collectionAsync) {
                this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
            }
            // step 3, subscribe to the keyup event and run the callback when that happens
            this.$filterElm.keyup((/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                /** @type {?} */
                let value = e && e.target && e.target.value || '';
                /** @type {?} */
                const enableWhiteSpaceTrim = this.gridOptions.enableFilterTrimWhiteSpace || this.columnFilter.enableTrimWhiteSpace;
                if (typeof value === 'string' && enableWhiteSpaceTrim) {
                    value = value.trim();
                }
                if (this._clearFilterTriggered) {
                    this.callback(e, { columnDef: this.columnDef, clearFilterTriggered: this._clearFilterTriggered, shouldTriggerQuery: this._shouldTriggerQuery });
                    this.$filterElm.removeClass('filled');
                }
                else {
                    value === '' ? this.$filterElm.removeClass('filled') : this.$filterElm.addClass('filled');
                    this.callback(e, { columnDef: this.columnDef, searchTerms: [value], shouldTriggerQuery: this._shouldTriggerQuery });
                }
                // reset both flags for next use
                this._clearFilterTriggered = false;
                this._shouldTriggerQuery = true;
            }));
        }
        catch (error) {
            console.error('method [ init] error :', error);
        }
    }
    /**
     * @return {?}
     */
    refreshHeaderOnly() {
        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        /** @type {?} */
        const newCollection = this.columnFilter.collection || [];
        this.renderDomElement(newCollection);
        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        /** @type {?} */
        const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if (collectionAsync) {
            this.renderOptionsAsync(collectionAsync); // create Subject after resolve (createCollectionAsyncSubject)
        }
        // Ensure clear button exists after header refresh
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }
    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    clear(shouldTriggerQuery = true) {
        console.log("run clear function !");
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            // For multiselect, we need to clear selections using the multipleSelect API
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                this.$filterElm.multipleSelect('setSelects', []);
            }
            else {
                this.$filterElm.val('');
            }
            this.searchTerms = [];
            // For multiselect, trigger the onClose event which will call the callback
            if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
                // Directly call the callback to clear the filter
                this.callback(undefined, {
                    columnDef: this.columnDef,
                    operator: this.operator,
                    searchTerms: [],
                    shouldTriggerQuery: true
                });
                // Remove filled class if present
                this.$filterElm.removeClass('filled');
            }
            else {
                // For regular input, trigger keyup which will call the callback
                this.$filterElm.trigger('keyup');
            }
        }
    }
    /**
     * destroy the filter
     * @return {?}
     */
    destroy() {
        if (this.$filterElm) {
            // remove event watcher
            this.$filterElm.off().remove();
        }
        this.$filterElm.multipleSelect('destroy');
        // also dispose of all Subscriptions
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    }
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    setValues(values) {
        if (values) {
            values = Array.isArray(values) ? values : [values];
            this.$filterElm.multipleSelect('setSelects', values);
        }
    }
    //
    // protected functions
    // ------------------
    /**
     * user might want to filter certain items of the collection
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    filterCollection(inputCollection) {
        /** @type {?} */
        let outputCollection = inputCollection;
        // user might want to filter certain items of the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionFilterBy) {
            /** @type {?} */
            const filterBy = this.columnFilter.collectionFilterBy;
            /** @type {?} */
            const filterCollectionBy = this.columnFilter.collectionOptions && this.columnFilter.collectionOptions.filterResultAfterEachPass || null;
            outputCollection = this.collectionService.filterCollection(outputCollection, filterBy, filterCollectionBy);
        }
        return outputCollection;
    }
    /**
     * user might want to sort the collection in a certain way
     * @protected
     * @param {?} inputCollection
     * @return {?} outputCollection filtered and/or sorted collection
     */
    sortCollection(inputCollection) {
        /** @type {?} */
        let outputCollection = inputCollection;
        // user might want to sort the collection
        if (this.columnDef && this.columnFilter && this.columnFilter.collectionSortBy) {
            /** @type {?} */
            const sortBy = this.columnFilter.collectionSortBy;
            outputCollection = this.collectionService.sortCollection(this.columnDef, outputCollection, sortBy, this.enableTranslateLabel);
        }
        return outputCollection;
    }
    /**
     * @protected
     * @param {?} collectionAsync
     * @return {?}
     */
    renderOptionsAsync(collectionAsync) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            /** @type {?} */
            let awaitedCollection = [];
            if (collectionAsync) {
                awaitedCollection = yield castToPromise(collectionAsync);
                this.renderDomElementFromCollectionAsync(awaitedCollection);
                // because we accept Promises & HttpClient Observable only execute once
                // we will re-create an RxJs Subject which will replace the "collectionAsync" which got executed once anyway
                // doing this provide the user a way to call a "collectionAsync.next()"
                this.createCollectionAsyncSubject();
            }
        });
    }
    /**
     * Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it
     * @protected
     * @return {?}
     */
    createCollectionAsyncSubject() {
        /** @type {?} */
        const newCollectionAsync = new Subject();
        this.columnFilter.collectionAsync = newCollectionAsync;
        this.subscriptions.push(newCollectionAsync.subscribe((/**
         * @param {?} collection
         * @return {?}
         */
        collection => this.renderDomElementFromCollectionAsync(collection))));
    }
    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     * @protected
     * @param {?} collection
     * @return {?}
     */
    renderDomElementFromCollectionAsync(collection) {
        if (this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('Something went wrong while trying to pull the collection from the "collectionAsync" call in the Select Filter, the collection is not a valid array.');
        }
        // copy over the array received from the async call to the "collection" as the new collection to use
        // this has to be BEFORE the `collectionObserver().subscribe` to avoid going into an infinite loop
        this.columnFilter.collection = collection;
        // recreate Multiple Select after getting async collection
        this.renderDomElement(collection);
        // Ensure clear button exists after async collection update
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }
    /**
     * @protected
     * @param {?} collection
     * @return {?}
     */
    renderDomElement(collection) {
        if (!Array.isArray(collection) && this.collectionOptions && this.collectionOptions.collectionInObjectProperty) {
            collection = getDescendantProperty(collection, this.collectionOptions.collectionInObjectProperty);
        }
        if (!Array.isArray(collection)) {
            throw new Error('The "collection" passed to the Select Filter is not a valid array');
        }
        // user can optionally add a blank entry at the beginning of the collection
        if (this.collectionOptions && this.collectionOptions.addBlankEntry) {
            collection.unshift(this.createBlankEntry());
        }
        /** @type {?} */
        let newCollection = collection;
        // user might want to filter and/or sort certain items of the collection
        newCollection = this.filterCollection(newCollection);
        newCollection = this.sortCollection(newCollection);
        // step 1, create HTML string template
        /** @type {?} */
        const filterTemplate = this.buildTemplateHtmlString(newCollection, this.searchTerms);
        // step 2, create the DOM Element of the filter & pre-load search terms
        // also subscribe to the onClose event
        this.createDomElement(filterTemplate);
    }
    /**
     * Create the HTML template as a string
     * @protected
     * @param {?} optionCollection
     * @param {?} searchTerms
     * @return {?}
     */
    buildTemplateHtmlString(optionCollection, searchTerms) {
        /** @type {?} */
        let options = '';
        /** @type {?} */
        const fieldId = this.columnDef && this.columnDef.id;
        /** @type {?} */
        const separatorBetweenLabels = this.collectionOptions && this.collectionOptions.separatorBetweenTextLabels || '';
        /** @type {?} */
        const isRenderHtmlEnabled = this.columnFilter && this.columnFilter.enableRenderHtml || false;
        /** @type {?} */
        const sanitizedOptions = this.gridOptions && this.gridOptions.sanitizeHtmlOptions || {};
        // collection could be an Array of Strings OR Objects
        if (optionCollection.every((/**
         * @param {?} x
         * @return {?}
         */
        x => typeof x === 'string'))) {
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            (option) => {
                /** @type {?} */
                const selected = (searchTerms.findIndex((/**
                 * @param {?} term
                 * @return {?}
                 */
                (term) => term === option)) >= 0) ? 'selected' : '';
                options += `<option value="${option}" label="${option}" ${selected}>${option}</option>`;
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    this.isFilled = true;
                }
            }));
        }
        else {
            // array of objects will require a label/value pair unless a customStructure is passed
            optionCollection.forEach((/**
             * @param {?} option
             * @return {?}
             */
            (option) => {
                if (!option || (option[this.labelName] === undefined && option.labelKey === undefined)) {
                    throw new Error(`[select-filter] A collection with value/label (or value/labelKey when using Locale) is required to populate the Select list, for example:: { filter: model: Filters.multipleSelect, collection: [ { value: '1', label: 'One' } ]')`);
                }
                /** @type {?} */
                const labelKey = (/** @type {?} */ ((option.labelKey || option[this.labelName])));
                /** @type {?} */
                const selected = (searchTerms.length > 0) ? 'selected' : '';
                /** @type {?} */
                const labelText = ((option.labelKey || this.enableTranslateLabel) && labelKey) ? this.translate.instant(labelKey || ' ') : labelKey;
                /** @type {?} */
                let prefixText = option[this.labelPrefixName] || '';
                /** @type {?} */
                let suffixText = option[this.labelSuffixName] || '';
                /** @type {?} */
                let optionLabel = option[this.optionLabel] || '';
                optionLabel = optionLabel.toString().replace(/\"/g, '\''); // replace double quotes by single quotes to avoid interfering with regular html
                // also translate prefix/suffix if enableTranslateLabel is true and text is a string
                prefixText = (this.enableTranslateLabel && prefixText && typeof prefixText === 'string') ? this.translate.instant(prefixText || ' ') : prefixText;
                suffixText = (this.enableTranslateLabel && suffixText && typeof suffixText === 'string') ? this.translate.instant(suffixText || ' ') : suffixText;
                optionLabel = (this.enableTranslateLabel && optionLabel && typeof optionLabel === 'string') ? this.translate.instant(optionLabel || ' ') : optionLabel;
                // add to a temp array for joining purpose and filter out empty text
                /** @type {?} */
                const tmpOptionArray = [prefixText, labelText, suffixText].filter((/**
                 * @param {?} text
                 * @return {?}
                 */
                (text) => text));
                /** @type {?} */
                let optionText = tmpOptionArray.join(separatorBetweenLabels);
                // if user specifically wants to render html text, he needs to opt-in else it will stripped out by default
                // also, the 3rd party lib will saninitze any html code unless it's encoded, so we'll do that
                if (isRenderHtmlEnabled) {
                    // sanitize any unauthorized html tags like script and others
                    // for the remaining allowed tags we'll permit all attributes
                    /** @type {?} */
                    const sanitizedText = DOMPurify_.sanitize(optionText, sanitizedOptions);
                    optionText = htmlEncode(sanitizedText);
                }
                // html text of each select option
                options += `<option value="${option[this.valueName]}" label="${optionLabel}" ${selected}>${optionText}</option>`;
                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if (selected) {
                    this.isFilled = true;
                }
            }));
        }
        return `<select class="ms-filter search-filter filter-${fieldId}" ${this.isMultipleSelect ? 'multiple="multiple"' : ''}>${options}</select>`;
    }
    /**
     * Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be
     * @protected
     * @return {?}
     */
    createBlankEntry() {
        /** @type {?} */
        const blankEntry = {
            [this.labelName]: '',
            [this.valueName]: ''
        };
        if (this.labelPrefixName) {
            blankEntry[this.labelPrefixName] = '';
        }
        if (this.labelSuffixName) {
            blankEntry[this.labelSuffixName] = '';
        }
        return blankEntry;
    }
    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @protected
     * @param {?} filterTemplate
     * @return {?}
     */
    createDomElement(filterTemplate) {
        /** @type {?} */
        const fieldId = this.columnDef && this.columnDef.id;
        // provide the name attribute to the DOM element which will be needed to auto-adjust drop position (dropup / dropdown)
        this.elementName = `filter-${fieldId}`;
        this.defaultOptions.name = this.elementName;
        /** @type {?} */
        const $headerElm = this.grid.getHeaderColumn(fieldId);
        // create the DOM element & add an ID and filter class
        this.$filterElm = $(filterTemplate);
        if (typeof this.$filterElm.multipleSelect !== 'function') {
            throw new Error(`multiple-select.js was not found, make sure to modify your "angular-cli.json" file and include "../node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js" and it's css or SASS file`);
        }
        this.$filterElm.attr('id', this.elementName);
        this.$filterElm.data('columnId', fieldId);
        // if there's a search term, we will add the "filled" class for styling purposes
        if (this.isFilled) {
            this.$filterElm.addClass('filled');
        }
        // append the new DOM element to the header row
        if (this.$filterElm && typeof this.$filterElm.appendTo === 'function') {
            this.$filterElm.appendTo($headerElm);
            $('.slick-header-column > .ms-parent').click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                event.stopPropagation();
            }));
        }
        // merge options & attach multiSelect
        /** @type {?} */
        const elementOptions = Object.assign({}, this.defaultOptions, this.columnFilter.filterOptions);
        this.filterElmOptions = Object.assign({}, this.defaultOptions, elementOptions);
        this.$filterElm = this.$filterElm.multipleSelect(this.filterElmOptions);
        // Ensure clear button is added for multiple select filters after DOM is ready
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
            // Also start monitoring to catch cases where the filter is recreated
            this.monitorAndEnsureClearButton();
        }
    }
    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method checks for ms-select-all element as a trigger and adds clear button if missing
     * Called whenever the filter is recreated or refreshed
     * @private
     * @return {?}
     */
    ensureClearButtonExists() {
        if (!this.isMultipleSelect) {
            return;
        }
        // Use a more robust approach to find the checkbox container
        // Try multiple times with increasing delays to handle async DOM updates
        /** @type {?} */
        const attempts = [0, 50, 100, 200, 500, 1000];
        // milliseconds
        /** @type {?} */
        const tryAddClearButton = (/**
         * @param {?} attemptIndex
         * @return {?}
         */
        (attemptIndex) => {
            if (attemptIndex >= attempts.length) {
                console.warn('Failed to add clear button after all attempts for column:', this.columnDef.id);
                return;
            }
            setTimeout((/**
             * @return {?}
             */
            () => {
                // Find the container using multiple selectors to be more robust
                /** @type {?} */
                let container = $(`div[name=filter-${this.columnDef.id}]`);
                // If not found, try alternative selectors
                if (!container.length) {
                    container = $(`.ms-drop[data-name=filter-${this.columnDef.id}]`);
                }
                if (!container.length) {
                    container = $(`.ms-drop:has(.ms-choice[data-name=filter-${this.columnDef.id}])`);
                }
                // Check if ms-select-all exists as a trigger - this indicates the dropdown is properly initialized
                /** @type {?} */
                const hasSelectAll = container.find('.ms-select-all').length > 0;
                console.log("🚀 ~ setTimeout ~ hasSelectAll:", hasSelectAll);
                // If container exists, has ms-select-all, and clear button doesn't exist, add it
                // Create clear filter button with an inline SVG icon on the right
                /** @type {?} */
                const clearBtn = $(`<button class="ms-ok-button clear-filter-btn">
                            <span style="display: inline-flex; align-items: center;">
                                Clear Filter
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-left: 5px;">
                                    <path d="M3 4h18l-7 8v8h-4v-8l-7-8z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                                    <path d="M5 5L19 19" stroke="red" stroke-width="2"/>
                                </svg>
                            </span>
                        </button>`);
                // Insert at the very beginning of the dropdown container
                container.prepend(clearBtn);
                // Add click handler to clear button
                clearBtn.on('click', (/**
                 * @param {?} e
                 * @return {?}
                 */
                (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Call the clear method
                    this.clear(true);
                    // Close the dropdown menu
                    if (this.$filterElm && this.$filterElm.multipleSelect) {
                        this.$filterElm.multipleSelect('close');
                    }
                }));
                console.log('Clear button successfully added for column:', this.columnDef.id);
                // If container exists, has select-all, but clear button already exists, we're done
            }), attempts[attemptIndex]);
        });
        // Start the first attempt
        tryAddClearButton(0);
    }
    /**
     * Monitors for the existence of ms-select-all and ensures clear button is added
     * This is a more aggressive approach for cases where the filter is frequently recreated
     * @private
     * @return {?}
     */
    monitorAndEnsureClearButton() {
        if (!this.isMultipleSelect) {
            return;
        }
        /** @type {?} */
        const checkInterval = setInterval((/**
         * @return {?}
         */
        () => {
            /** @type {?} */
            const container = $(`div[name=filter-${this.columnDef.id}]`);
            /** @type {?} */
            const hasSelectAll = container.find('.ms-select-all').length > 0;
            /** @type {?} */
            const hasClearButton = container.find('.clear-filter-btn').length > 0;
            // If we have select-all but no clear button, add it
            if (container.length && hasSelectAll && !hasClearButton) {
                // Create clear filter button
                /** @type {?} */
                const clearBtn = $(`<button class="ms-ok-button clear-filter-btn">
                        <span style="display: inline-flex; align-items: center;">
                            Clear Filter
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-left: 5px;">
                                <path d="M3 4h18l-7 8v8h-4v-8l-7-8z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                                <path d="M5 5L19 19" stroke="red" stroke-width="2"/>
                            </svg>
                        </span>
                    </button>`);
                // Insert at the very beginning of the dropdown container
                container.prepend(clearBtn);
                // Add click handler to clear button
                clearBtn.on('click', (/**
                 * @param {?} e
                 * @return {?}
                 */
                (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Call the clear method
                    this.clear(true);
                    // Close the dropdown menu
                    if (this.$filterElm && this.$filterElm.multipleSelect) {
                        this.$filterElm.multipleSelect('close');
                    }
                }));
                console.log('Clear button added via monitoring for column:', this.columnDef.id);
            }
            // If container no longer exists, stop monitoring
            if (!container.length) {
                clearInterval(checkInterval);
            }
        }), 100);
        // Stop monitoring after 10 seconds to prevent memory leaks
        setTimeout((/**
         * @return {?}
         */
        () => {
            clearInterval(checkInterval);
        }), 10000);
    }
    /**
     * @private
     * @return {?}
     */
    setFilterOptions() {
        try {
            /** @type {?} */
            const clickHandler = (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                /** @type {?} */
                const clickedCheckbox = event.target;
                /** @type {?} */
                const name = clickedCheckbox.dataset ? clickedCheckbox.dataset.name : "";
                if (this.checkboxContainer && clickedCheckbox.value === "(NOT EMPTY)") {
                    this.checkboxContainer.find("input[type=checkbox][value='(EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && clickedCheckbox.value === "(EMPTY)") {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && name.includes("selectAllfilter")) {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                // Add your desired code here to handle the checkbox click event
            });
            /** @type {?} */
            const options = {
                autoAdjustDropHeight: true,
                autoAdjustDropPosition: true,
                autoAdjustDropWidthByTextSize: true,
                container: 'body',
                filter: this.FilterInputSearch,
                maxHeight: 275,
                minWidth: this.columnDef.width,
                //-Fix M6549:try to enhance the design of the filter in case of short values (case of sign).
                filterAcceptOnEnter: true,
                single: !this.isMultipleSelect,
                //animate: 'slide',
                textTemplate: (/**
                 * @param {?} $elm
                 * @return {?}
                 */
                ($elm) => {
                    // render HTML code or not, by default it is sanitized and won't be rendered
                    /** @type {?} */
                    const isRenderHtmlEnabled = this.columnDef && this.columnDef.filter && this.columnDef.filter.enableRenderHtml || false;
                    return isRenderHtmlEnabled ? $elm.text() : $elm.html();
                }),
                onClose: (/**
                 * @return {?}
                 */
                () => {
                    try {
                        //console.log('-----onClose----------', this.elementName);
                        // we will subscribe to the onClose event for triggering our callback
                        // also add/remove "filled" class for styling purposes
                        if ((!this.isMultipleSelect && this.lastSelectedValue != undefined) || this.isMultipleSelect) {
                            /** @type {?} */
                            let selectedItems = this.$filterElm.multipleSelect('getSelects');
                            if (Array.isArray(selectedItems) && selectedItems.length > 0 && this.lastSelectedValue != this.columnDef.params.grid.all) {
                                this.isFilled = true;
                                this.$filterElm.addClass('filled').siblings('div .search-filter').addClass('filled');
                            }
                            else {
                                selectedItems = [];
                                this.isFilled = false;
                                this.$filterElm.removeClass('filled').siblings('div .search-filter').removeClass('filled');
                            }
                            //-Fix M6549:Filter with an empty string doesn't exist.
                            if (selectedItems.length == 1 && selectedItems[0] == '')
                                selectedItems.push('');
                            this.callback(undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: selectedItems, shouldTriggerQuery: true });
                            $(document).off('click');
                        }
                        if (event)
                            event.stopPropagation();
                        if (this.checkboxContainer)
                            this.checkboxContainer.find("input[type=checkbox]").off("click", clickHandler);
                    }
                    catch (error) {
                        console.error(' error', error);
                    }
                }),
                onOpen: (/**
                 * @return {?}
                 */
                () => {
                    console.log('-----onOpen----------', this.columnDef.width);
                    if (!this.isMultipleSelect) {
                        console.log('02020');
                        this.lastSelectedValue = undefined;
                        //console.log('-----onOpen----------');
                        $("div[name^=filter-]").each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        (index, item) => {
                            /** @type {?} */
                            let name = $(item).attr('name');
                            if (name != this.elementName && $(item).css('display') == "block") {
                                //console.log('-----onOpen---------- slideUp ')
                                $(item).slideUp();
                            }
                        }));
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            $("div[name=filter-" + this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        /** @type {?} */
                        var ul = $($($("div[name=filter-" + this.columnDef.id + "]").children()[0]));
                        $(document).on('click', (/**
                         * @param {?} event
                         * @return {?}
                         */
                        (event) => {
                            /** @type {?} */
                            var target = $(event.target);
                            if (!target.is(ul[0])) {
                                $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                                 * @return {?}
                                 */
                                () => {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        (e) => {
                            $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            () => {
                                $(document).off('click');
                            }));
                        }));
                        event.stopPropagation();
                    }
                    else {
                        this.checkboxContainer = $("div[name=filter-" + this.columnDef.id + "]");
                        // Ensure clear button exists using the centralized method
                        this.ensureClearButtonExists();
                        // Also start monitoring to catch any recreation of the filter
                        this.monitorAndEnsureClearButton();
                        // Add a small delay to ensure the DOM is ready for checkbox handlers
                        setTimeout((/**
                         * @return {?}
                         */
                        () => {
                            // Attach the click event handler to checkboxes
                            if (this.checkboxContainer) {
                                this.checkboxContainer.find("input[type=checkbox]").click(clickHandler);
                            }
                        }), 50);
                        event.preventDefault();
                        event.stopImmediatePropagation();
                        /** @type {?} */
                        var left = $("div[name=filter-" + this.columnDef.id + "]").position().left;
                        /** @type {?} */
                        var width = $("div[name=filter-" + this.columnDef.id + "]").width();
                        if (left >= width) {
                            /** @type {?} */
                            var newposLeft = (left - width) + 14;
                            // console.log("🚀 ~ file: swt-column-filter.ts:591 ~ setFilterOptions ~ newposLeft:", newposLeft)
                            $("div[name=filter-" + this.columnDef.id + "]").css({ left: newposLeft });
                        }
                        //-Fix M6549: Select a filter then scroll left, the filter is still open.
                        this.columnDef.params.grid.gridObj.onScroll.subscribe((/**
                         * @param {?} e
                         * @return {?}
                         */
                        (e) => {
                            $("div[name=filter-" + this.columnDef.id + "]").slideUp((/**
                             * @return {?}
                             */
                            () => {
                                $(document).off('click');
                            }));
                        }));
                        $('div[name^="filter-"]').each((/**
                         * @param {?} index
                         * @param {?} item
                         * @return {?}
                         */
                        (index, item) => {
                            /** @type {?} */
                            const name = $(item).attr('name');
                            if (name != "filter-" + this.columnDef.id && $(item).css('display') == "block") {
                                $(item).slideUp((/**
                                 * @return {?}
                                 */
                                () => {
                                    $(document).off('click');
                                }));
                            }
                        }));
                        event.stopPropagation();
                    }
                }),
                onClick: (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.lastSelectedValue = event.label;
                    //Commented is not needed check if it's working fine 
                    /*if ( event.label == this.columnDef.params.grid.all ) {
                        if ( !this.columnDef.params.grid.paginationComponent.realPagination && this.columnDef.params.grid.GroupId == null){
                            this.clear(true);
                        }
                    }*/
                })
            };
            if (this.isMultipleSelect) {
                options.single = false;
                options.okButton = true;
                options.addTitle = true; // show tooltip of all selected items while hovering the filter
                options.countSelected = this.translate.instant('X_OF_Y_SELECTED');
                options.allSelected = this.translate.instant('ALL_SELECTED');
                options.selectAllText = this.translate.instant('ALL');
                options.selectAllDelimiter = ['', '']; // remove default square brackets of default text "[Select All]" => "Select All"
            }
            this.defaultOptions = options;
        }
        catch (error) {
            console.error('method [setFilterOptions] error :', error);
        }
    }
}
if (false) {
    /**
     * DOM Element Name, useful for auto-detecting positioning (dropup / dropdown)
     * @type {?}
     */
    SwtColumnFilter.prototype.elementName;
    /**
     * Filter Multiple-Select options
     * @type {?}
     */
    SwtColumnFilter.prototype.filterElmOptions;
    /**
     * The JQuery DOM element
     * @type {?}
     */
    SwtColumnFilter.prototype.$filterElm;
    /** @type {?} */
    SwtColumnFilter.prototype.grid;
    /** @type {?} */
    SwtColumnFilter.prototype.searchTerms;
    /** @type {?} */
    SwtColumnFilter.prototype.columnDef;
    /** @type {?} */
    SwtColumnFilter.prototype.callback;
    /** @type {?} */
    SwtColumnFilter.prototype.defaultOptions;
    /** @type {?} */
    SwtColumnFilter.prototype.isFilled;
    /** @type {?} */
    SwtColumnFilter.prototype.lastSelectedValue;
    /** @type {?} */
    SwtColumnFilter.prototype.labelName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelPrefixName;
    /** @type {?} */
    SwtColumnFilter.prototype.labelSuffixName;
    /** @type {?} */
    SwtColumnFilter.prototype.optionLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.valueName;
    /** @type {?} */
    SwtColumnFilter.prototype.enableTranslateLabel;
    /** @type {?} */
    SwtColumnFilter.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.scroll;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._clearFilterTriggered;
    /** @type {?} */
    SwtColumnFilter.prototype.isMultipleSelect;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.FilterInputSearch;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    SwtColumnFilter.prototype.isOpened;
    /** @type {?} */
    SwtColumnFilter.prototype.checkboxContainer;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.translate;
    /**
     * @type {?}
     * @protected
     */
    SwtColumnFilter.prototype.collectionService;
}
//# sourceMappingURL=data:application/json;base64,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