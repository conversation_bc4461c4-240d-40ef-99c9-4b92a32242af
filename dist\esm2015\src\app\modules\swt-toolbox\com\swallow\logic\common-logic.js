/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//import { moment } from 'ngx-bootstrap/chronos/test/chain';
import * as moment_ from 'moment-mini';
import { Alert } from '../utils/alert.component';
import { ExternalInterface } from '../utils/external-interface.service';
import { CommonUtil } from '../utils/common-util.service';
/** @type {?} */
const moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
//@dynamic
/**
 * CommonLogic.as
 *
 * This class is the base class of additional operations on string
 *
 * <AUTHOR> SwallowTech Tunisia
 * @updated by Rihab JABALLAH on 18/09/2018
 */
export class CommonLogic {
    constructor() {
        this._testDate = "";
        this._dateFormat = "";
        this._showBuildInProgress = false;
        // End.
    }
    /**
     * @param {?} testDate
     * @return {?}
     */
    set testDate(testDate) {
        this._testDate = this.convertUKtoUS(testDate);
    }
    /**
     * @return {?}
     */
    get testDate() {
        return this._testDate;
    }
    /**
     * @param {?} dateAsString
     * @return {?}
     */
    convertDate(dateAsString) {
        return this.convertUKtoUS(dateAsString);
    }
    /**
     * @param {?} dateFormat
     * @return {?}
     */
    set dateFormat(dateFormat) {
        /** @type {?} */
        var str = dateFormat.toUpperCase();
        this._dateFormat = str;
    }
    /**
     * @return {?}
     */
    get dateFormat() {
        return this._dateFormat;
    }
    /**
     * @param {?} start
     * @param {?} end
     * @return {?}
     */
    calculateDays(start, end) {
        /** @type {?} */
        var daysInMilliseconds = 1000 * 60 * 60 * 24;
        return ((end.getTime() - start.getTime()) / daysInMilliseconds);
    }
    /**
     * @param {?} input
     * @return {?}
     */
    convertUKtoUS(input) {
        /** @type {?} */
        var rtn = null;
        if (input) {
            if (this._dateFormat == "DD/MM/YYYY") {
                /** @type {?} */
                var dateArry = input.split("/");
                rtn = dateArry[1] + "/" + dateArry[0] + "/" + dateArry[2];
            }
            else {
                rtn = input; //Already in US format
            }
        }
        return rtn;
    }
    /**
     * @param {?} input
     * @return {?}
     */
    static removeLineBreaks(input) {
        /** @type {?} */
        var rtn = "";
        /** @type {?} */
        var postSplit = [];
        postSplit = input.split("\n");
        for (var i = 0; i < postSplit.length; i++) {
            rtn += postSplit[i];
        }
        return rtn;
    }
    /**
     * This function validates the date field - Mantis 1262. <br>
     * The date field is an editable one where the user can type the desired date.<br>
     * The date is taken as an argument and it is validate against certain rules.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010<br>
     * @param {?} event
     * @param {?} startDay
     * @param {?} dateVal
     * @return {?}
     */
    validateDate(event, startDay, dateVal) {
        /** @type {?} */
        var validatDate = false;
        /** @type {?} */
        var day;
        /** @type {?} */
        var month;
        /** @type {?} */
        var year;
        /** @type {?} */
        var dateValue = dateVal.toString();
        if (dateValue != "") {
            if (startDay.formatString == "MM/DD/YYYY") {
                /** @type {?} */
                var myPattern1 = /^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/;
                if (myPattern1.test(startDay.text).toString() == "true") {
                    month = Number(dateValue.split("/")[0]);
                    day = Number(dateValue.split("/")[1]);
                    year = Number(dateValue.split("/")[2]);
                    // calling the helper function for date validation
                    if (this.validateDateHelper(day, month, year, startDay))
                        validatDate = true;
                }
                else {
                    this.showAlert(startDay);
                }
            }
            else if (startDay.formatString == "DD/MM/YYYY") {
                /** @type {?} */
                var myPattern2 = /^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/;
                if (myPattern2.test(startDay.text).toString() == "true") {
                    day = Number(dateValue.split("/")[0]);
                    month = Number(dateValue.split("/")[1]);
                    year = Number(dateValue.split("/")[2]);
                    // calling the helper function for date validation
                    if (this.validateDateHelper(day, month, year, startDay))
                        validatDate = true;
                }
                else {
                    this.showAlert(startDay);
                }
            }
        }
        else {
            this.showAlert(startDay);
        }
        return validatDate;
    }
    /* Start : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * @param {?} startDay
     * @return {?}
     */
    showAlert(startDay) {
        this.swtAlert.show("Please enter a valid date", "Error", Alert.OK, null, (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            // Code modified by Marshal on 09-Mar-2011 for Mantis 1368: 
            // Description: Click on Date tabs should show corresponding date tab value in Date Field
            startDay.setFocus();
        }));
    }
    /* End : Function added for Mantis 1262 : Make date fields editable in all Predict screens - by Marshal on 03-Jan-2011*/
    /**
     * Helper method for validating the given date field - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} day
     * @param {?} month
     * @param {?} year
     * @param {?} startDay
     * @return {?}
     */
    validateDateHelper(day, month, year, startDay) {
        /** @type {?} */
        var validateHelp = true;
        // checking the months which has only 30 days.
        if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
            validateHelp = false;
            this.showAlert(startDay);
        }
        // checking February month on leap years
        if (month == 2) {
            if (day > 29 || (day == 29 && !this.isLeapYear(year))) {
                validateHelp = false;
                this.showAlert(startDay);
            }
        }
        return validateHelp;
    }
    /**
     * Helper function for checking whether the given year is a leap year or not - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     * @param {?} year
     * @return {?}
     */
    isLeapYear(year) {
        /** @type {?} */
        var isLeap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
        return isLeap;
    }
    // Start: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011 
    /**
     * This function converts the given string to boolean
     *
     * @param {?} value
     * @return {?} Boolean
     */
    static booleanValue(value) {
        switch (value.toLowerCase()) {
            case "1":
            case "true":
            case "yes":
            case "y":
                return true;
            case "0":
            case "false":
            case "no":
            case "n":
                return false;
            default:
                return Boolean(value);
        }
    }
    // End: added by Bala for Mantis 1355 - New Entity Monitor Screen on 01-Mar-2011
    /**
     * Added for mantis by KaisBS 2016 + 1468
     * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
     * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
     * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
     *
     * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
     * 	Note that mantis concerns other screens that contain from and to date.
     *
     * @param {?} autoRefreshOrcheckLocalDateRange
     * @param {?} fromDate
     * @param {?} toDate
     * @param {?} showDays
     * @param {?} systemDate
     * @param {?} dateFormat
     * @param {?} updateDataFunction
     * @param {?} classObject
     * @return {?}
     */
    checkDateRange(autoRefreshOrcheckLocalDateRange, fromDate, toDate, showDays, systemDate, dateFormat, updateDataFunction, classObject) {
        //classObject.inputData.addEventListener(ResultEvent.RESULT, result);
        classObject.inputData.cbResult = (/**
         * @param {?} data
         * @return {?}
         */
        (data) => {
            this.result(data);
        });
        //classObject.inputData.addEventListener(FaultEvent.FAULT, fault);
        classObject.inputData.cbFault = (/**
         * @param {?} data
         * @return {?}
         */
        (data) => {
            this.fault(data);
        });
        /** @type {?} */
        var dateRangeExceeded = false;
        /** @type {?} */
        var nDaysPriorToToday = ExternalInterface.call('eval', 'nDaysPriorToToday');
        /** @type {?} */
        var priorDate = new Date(CommonUtil.dateFromString(systemDate, this.dateFormat));
        priorDate.setDate(priorDate.getDate() - nDaysPriorToToday);
        /** @type {?} */
        var nDaysAheadToToday = ExternalInterface.call('eval', 'nDaysAheadToToday');
        /** @type {?} */
        var aheadDate = new Date(CommonUtil.dateFromString(systemDate, this.dateFormat));
        aheadDate.setDate(aheadDate.getDate() - nDaysAheadToToday);
        if (fromDate < priorDate || toDate > aheadDate) {
            dateRangeExceeded = true;
            /** @type {?} */
            var windowTitle = 'Microsoft Internet Explorer';
            /** @type {?} */
            var warningMessage = 'The data for this date range selection may not be available ' +
                'in the cache and will take time to be calculated. Do you want to continue?';
            this.swtAlert.confirm(warningMessage, windowTitle, Alert.OK | Alert.CANCEL, null, (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                if (event.detail == Alert.OK) {
                    // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
                    this._showBuildInProgress = true;
                    updateDataFunction(autoRefreshOrcheckLocalDateRange, true);
                }
                else
                    updateDataFunction(autoRefreshOrcheckLocalDateRange, true, true);
            }), null);
        }
        return dateRangeExceeded;
    }
    // Added by KaisBS for mantis 2015: 'Data Build In Progress' when fetching dates not currently available
    // Begin.
    /**
     * @return {?}
     */
    get showBuildInProgress() {
        return this._showBuildInProgress;
    }
    /**
     * @param {?} data
     * @return {?}
     */
    result(data) {
        this._showBuildInProgress = false;
        // FlexGlobals.topLevelApplication.inputData.removeEventListener(ResultEvent.RESULT, this.result);
    }
    /**
     * @param {?} datat
     * @return {?}
     */
    fault(datat) {
        this._showBuildInProgress = false;
        // FlexGlobals.topLevelApplication.inputData.removeEventListener(FaultEvent.FAULT, fault);
    }
}
CommonLogic.decorators = [
    { type: Injectable }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._testDate;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._dateFormat;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype._showBuildInProgress;
    /**
     * @type {?}
     * @private
     */
    CommonLogic.prototype.swtAlert;
}
//# sourceMappingURL=data:application/json;base64,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