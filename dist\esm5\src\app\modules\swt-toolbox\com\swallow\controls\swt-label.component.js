/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { ViewChild, ElementRef, Input, Component } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from '../utils/swt-util.service';
/** @type {?} */
var $ = require('jquery');
var SwtLabel = /** @class */ (function (_super) {
    tslib_1.__extends(SwtLabel, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function SwtLabel(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions--------------------------------------------------------------------------------------
        _this._text = "";
        _this._truncate = true;
        _this._htmlText = "";
        _this._fontSize = "11";
        _this._fontWeight = "";
        _this._width = "";
        _this._buttonMode = false;
        return _this;
    }
    Object.defineProperty(SwtLabel.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._width = this.adaptUnit(value, "auto");
                this.setStyle("width", this._width);
                $(this.elem.nativeElement.children[0]).css({
                    "width": "100%"
                });
            }
            catch (error) {
                console.error('method [set width] - error :', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "truncate", {
        get: /**
         * @return {?}
         */
        function () {
            return this._truncate;
        },
        //---Truncate---------------------------------------------------------------------------------------------------
        set: 
        //---Truncate---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._truncate = this.adaptValueAsBoolean(value);
            if (this._truncate) {
                $(this.elem.nativeElement.children[0]).removeClass('ellipsisDisabled').addClass('ellipsisEnabled');
            }
            else {
                $(this.elem.nativeElement.children[0]).removeClass('ellipsisEnabled').addClass('ellipsisDisabled');
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "styleName", {
        //---styleName--------------------------------------------------------------------------------------------------
        set: 
        //---styleName--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(this.elem.nativeElement.children[0]).addClass(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "textAlign", {
        //---TextAlign---------------------------------------------------------------------------------------------------
        set: 
        //---TextAlign---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this.labelDOM.nativeElement.parentElement.style.textAlign = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "htmlText", {
        get: /**
         * @return {?}
         */
        function () {
            return this._htmlText;
        },
        //---HtmlText--------------------------------------------------------------------------------------------------
        set: 
        //---HtmlText--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (this.firstCall) {
                this.originalValue = value;
                this.firstCall = false;
            }
            else {
                this._spyChanges(value);
            }
            this._htmlText = value;
            //        this._htmlText= this._htmlText.replace(/&<;/ig, '&#60').replace(/>/g, '&#62');
            this._htmlText = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\r\n|\r|\n)/g, '<br>');
            $(this.elem.nativeElement.children[0]).removeClass('ellipsisEnabled');
            $(this.elem.nativeElement.children[0]).html(this._htmlText);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            return this._text;
        },
        //---Text-----------------------------------------------------------------------------------------------------
        set: 
        //---Text-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (this.firstCall) {
                this.originalValue = value;
                this.firstCall = false;
            }
            else {
                this._spyChanges(value);
            }
            this._text = value;
            $(this.elem.nativeElement.children[0]).text(value);
            // set tooltip to label if the text is too long and it will be hidden.
            setTimeout((/**
             * @return {?}
             */
            function () {
                if (_this.elem.nativeElement.children[0].scrollWidth > _this.elem.nativeElement.children[0].clientWidth)
                    _this.toolTip = _this.text;
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "fontSize", {
        get: /**
         * @return {?}
         */
        function () {
            return this._fontSize;
        },
        //---FontSize--------------------------------------------------------------------------------------------------
        set: 
        //---FontSize--------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._fontSize = value;
            $(this.elem.nativeElement.children[0]).css('font-size', value + "px");
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "fontWeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._fontWeight;
        },
        //---FontWeight------------------------------------------------------------------------------------------------
        set: 
        //---FontWeight------------------------------------------------------------------------------------------------
        /**
         * @param {?} fontWeight
         * @return {?}
         */
        function (fontWeight) {
            this._fontWeight = fontWeight;
            this.setStyle('font-weight', fontWeight, this.elem.nativeElement.children[0]);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "color", {
        get: /**
         * @return {?}
         */
        function () {
            return this._color;
        },
        //---Color-----------------------------------------------------------------------------------------------------
        set: 
        //---Color-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} color
         * @return {?}
         */
        function (color) {
            this._color = color;
            this.setStyle('color', color, this.elem.nativeElement.children[0]);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtLabel.prototype, "buttonMode", {
        get: /**
         * @return {?}
         */
        function () {
            return this._buttonMode;
        },
        //---Color-----------------------------------------------------------------------------------------------------
        set: 
        //---Color-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === 'string') {
                if (value === 'true') {
                    this._buttonMode = true;
                }
                else {
                    this._buttonMode = false;
                }
            }
            else {
                this._buttonMode = value;
            }
            if (this._buttonMode) {
                $(this.elem.nativeElement.children[0]).addClass('linkbutton');
                this.onClick = this.emtpyOnnClick;
            }
            else {
                $(this.elem.nativeElement.children[0]).removeClass('linkbutton');
                this.onClick = this.defaultOnnClick;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    SwtLabel.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        if (this.constructor.name.toUpperCase() == "SWTLABEL") {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtLabel');
        }
        else {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtText');
        }
    };
    Object.defineProperty(SwtLabel.prototype, "textDictionaryId", {
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this.text = SwtUtil.getPredictMessage(value);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param event
     */
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    SwtLabel.prototype.defaultOnnClick = /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    function (event) {
        event.preventDefault();
        event.stopPropagation();
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtLabel.prototype.emtpyOnnClick = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
    };
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param event
     */
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    SwtLabel.prototype.onClick = /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param {?} event
     * @return {?}
     */
    function (event) {
        event.preventDefault();
        event.stopPropagation();
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    SwtLabel.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            delete this.labelDOM;
            delete this._text;
            delete this._color;
            delete this._truncate;
            delete this._htmlText;
            delete this._fontSize;
            delete this.firstCall;
            delete this._fontWeight;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    SwtLabel.decorators = [
        { type: Component, args: [{
                    selector: 'SwtLabel',
                    template: "\n        <label  \n               (click)=\"onClick($event)\"\n               class=\"ellipsisEnabled\"\n               #labelDOM>\n        </label>\n    ",
                    styles: ["\n          :host {\n              outline:none;\n              display: block;\n           }\n           \n           label{\n             font-size:11px;\n             color: black;\n             height: 23px;\n              line-height: 22px;\n             font-family: verdana,helvetica;\n             vertical-align: bottom;\n            /* margin: 0px 0px 5px 0px;*/\n             pointer-events: auto!important; \n           }\n          .ellipsisEnabled{\n             text-overflow: ellipsis;\n             overflow: hidden;\n             white-space: nowrap;\n          }\n          .ellipsisDisabled{\n             text-overflow: clip;\n          }\n   "]
                }] }
    ];
    /** @nocollapse */
    SwtLabel.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtLabel.propDecorators = {
        labelDOM: [{ type: ViewChild, args: ['labelDOM',] }],
        width: [{ type: Input, args: ['width',] }],
        truncate: [{ type: Input, args: ["truncate",] }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        styleName: [{ type: Input, args: ['styleName',] }],
        textAlign: [{ type: Input, args: ['textAlign',] }],
        htmlText: [{ type: Input }],
        text: [{ type: Input }],
        fontSize: [{ type: Input, args: ["fontSize",] }],
        fontWeight: [{ type: Input }],
        color: [{ type: Input }],
        buttonMode: [{ type: Input }],
        textDictionaryId: [{ type: Input, args: ['textDictionaryId',] }]
    };
    return SwtLabel;
}(Container));
export { SwtLabel };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.labelDOM;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._color;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._truncate;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._htmlText;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._fontSize;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype._buttonMode;
    /** @type {?} */
    SwtLabel.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtLabel.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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