/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { SwtUtil } from '../utils/swt-util.service';
var RemoteTransaction = /** @class */ (function () {
    // private headers = new HttpHeaders();
    /**
     * constructor
     *
     */
    function RemoteTransaction(httpComms_, programId_, uniqueIdentifier_) {
        this.httpComms_ = httpComms_;
        this.programId_ = programId_;
        this.uniqueIdentifier_ = uniqueIdentifier_;
        this.httpComms = httpComms_;
        this.programId = programId_;
        this.uniqueIdentifier = uniqueIdentifier_;
        this.startTime = this.getTimer();
    }
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.start = /**
     * @return {?}
     */
    function () {
        this.httpComms.setTransactionUId(this.programId, this.uniqueIdentifier);
        /** @type {?} */
        var result = this.remoteTransaction("start", this.httpComms.getTransactionUId());
        if (result === "true") {
            this.status = "active";
            RemoteTransaction.remoteTransactions.push(this);
        }
        return result === "true";
    };
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.commit = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var result = this.remoteTransaction("commit", this.httpComms.getTransactionUId() + "&response=json");
        if (result === "true") {
            this.status = "committed";
            RemoteTransaction.remoteTransactions.splice(RemoteTransaction.remoteTransactions.indexOf(this), 1);
        }
        return result === "true";
    };
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.rollback = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var result = this.remoteTransaction("rollback", this.httpComms.getTransactionUId() + "&response=json");
        if (result === "true") {
            this.status = "rolledback";
            RemoteTransaction.remoteTransactions.splice(RemoteTransaction.remoteTransactions.indexOf(this), 1);
        }
        return result === "true";
    };
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.isActive = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var result = this.remoteTransaction("status", this.httpComms.getTransactionUId() + "&response=json");
        if (result !== this.status) {
            throw new Error("Transactions is in illegal status");
        }
        return result === "active";
    };
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.wasCommitted = /**
     * @return {?}
     */
    function () {
        return this.status === "committed";
    };
    /**
     * @return {?}
     */
    RemoteTransaction.prototype.wasRolledBack = /**
     * @return {?}
     */
    function () {
        return this.status === "rolledback";
    };
    /**
     * @private
     * @return {?}
     */
    RemoteTransaction.prototype.getTimer = /**
     * @private
     * @return {?}
     */
    function () {
        return new Date().getTime();
    };
    /*
    ===========================================================================
    Aded by Khalil.B to handle all types of transactions Requests
    (this method replace the ajax function in "taglib.jsp" wich have the same name)
    ===========================================================================
    */
    /**
     * Remote transactions list of actions
     *
     */
    /*
        ===========================================================================
        Aded by Khalil.B to handle all types of transactions Requests
        (this method replace the ajax function in "taglib.jsp" wich have the same name)
        ===========================================================================
        */
    /**
     * Remote transactions list of actions
     *
     * @private
     * @param {?} action
     * @param {?} screenUniqueTransactionId
     * @return {?}
     */
    RemoteTransaction.prototype.remoteTransaction = /*
        ===========================================================================
        Aded by Khalil.B to handle all types of transactions Requests
        (this method replace the ajax function in "taglib.jsp" wich have the same name)
        ===========================================================================
        */
    /**
     * Remote transactions list of actions
     *
     * @private
     * @param {?} action
     * @param {?} screenUniqueTransactionId
     * @return {?}
     */
    function (action, screenUniqueTransactionId) {
        var _this = this;
        /** @type {?} */
        var requestURL = SwtUtil.getBaseURL();
        /** @type {?} */
        var url = requestURL + "common!";
        // Update url according to the action
        if (action === "start") {
            url += "startTransactionRemotely.do";
        }
        else if (action === "commit") {
            url += "commitTransactionRemotely.do";
        }
        else if (action === "rollback") {
            url += "rollbackTransactionRemotely.do";
        }
        else if (action === "status") {
            url += "getTransactionsStatus.do";
        }
        // Set the transaction identifier
        url += "?screenUniqueTransactionId=" + screenUniqueTransactionId;
        this.httpComms.sendTransaction(url).subscribe((/**
         * @param {?} response
         * @return {?}
         */
        function (response) {
            _this.remoteTransactionResult = response;
        }));
        return this.remoteTransactionResult;
    };
    RemoteTransaction.remoteTransactions = new Array() /*of RemoteTransaction*/;
    return RemoteTransaction;
}());
export { RemoteTransaction };
if (false) {
    /** @type {?} */
    RemoteTransaction.remoteTransactions;
    /** @type {?} */
    RemoteTransaction.prototype.remoteTransactionResult;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.httpComms;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.programId;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.uniqueIdentifier;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.startTime;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.status;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.httpComms_;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.programId_;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.uniqueIdentifier_;
}
//# sourceMappingURL=data:application/json;base64,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