/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { isClickable, isBlink, isNegative, CustomCell, CellBackgroundColor, isBold } from "./cellItemRenderUtilities";
/** @type {?} */
export const NumberAmountFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let type = columnDef['columnType'];
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let colorLink = '';
    /** @type {?} */
    let blink_me = isBlink(value);
    /** @type {?} */
    let negative = isNegative(dataContext, field);
    /** @type {?} */
    let isLink = columnDef["clickable"] || isClickable(dataContext, field);
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    /** @type {?} */
    let bold = columnDef['bold'] ? columnDef['bold'] : isBold(dataContext, field);
    /** @type {?} */
    let extraContent = '';
    if (isLink && columnDef.params.rowClickableFunction) {
        /** @type {?} */
        let clickableFromFunction = columnDef.params.rowClickableFunction(dataContext, row, columnDef.field);
        isLink = clickableFromFunction;
        colorLink = '#52aefb';
    }
    /** @type {?} */
    let backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    //-there is no custom style for the cell
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        if (backgroundColor && backgroundColor.toString().indexOf('|') > -1) {
            // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
            /** @type {?} */
            const colorList = backgroundColor.split('|');
            style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
        }
        else {
            style += ' background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        }
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null || field == "expand") {
            value = "";
        }
        if (typeof (value) == "string") {
            value = value.replace('<', '&lt;');
            value = value.replace('>', '&gt;');
        }
        extraContent = columnDef.params.extraHTMLContentFunction(dataContext, columnDef.field, value, type);
        value = columnDef.params.customContentFunction(dataContext, columnDef.field, value, type);
        return `<div class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''}  ${(String(type) == "input" && (enabledFlag == false || !columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''} "  style='${style} padding-right: 5px;  position: relative; text-align: right;' >${isLink ? '<a class="strLinkRender" ' + (negative ? 'style="color: #ff0000 !important;"' : colorLink ? 'style="color: ' + colorLink + ' !important;"' : '') + '  >' + value + '</a>' : value}</div>${extraContent}`;
    }
    return ``;
});
//# sourceMappingURL=data:application/json;base64,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