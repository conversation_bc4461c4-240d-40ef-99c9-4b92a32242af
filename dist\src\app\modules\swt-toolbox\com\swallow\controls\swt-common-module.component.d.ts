import { ElementRef, EventEmitter } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { CommonService } from "../utils/common.service";
import { VBox } from "./swt-vbox.component";
import { J<PERSON><PERSON>eader } from "../jsonhandler/jsonreader.service";
import { HashMap } from "../utils/HashMap.service";
import { XML } from "../xmlhandler/swt-xml.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { SwtCanvas } from "./swt-canvas.component";
import { HBox } from "./swt-hbox.component";
import { SwtButton } from "./swt-button.component";
import { SwtLoadingImage } from "./swt-loading-image.component";
import { SwtHelpButton } from "./swt-helpButton.component";
import { SwtCommonGrid } from "./swt-common-grid.component";
import { ModuleLoader } from "../utils/module-loader.service";
import { TitleWindow } from './title-window.component';
import { Logger } from '../logging/logger.service';
export declare class SwtCommonModule extends SwtModule {
    private _element;
    private _commonService;
    _creationComplete: EventEmitter<SwtCommonModule>;
    _preinitialize: EventEmitter<SwtCommonModule>;
    _width: any;
    _height: any;
    hboxButtons: SwtCanvas;
    customGrid: SwtCanvas;
    reportbuttons: HBox;
    _loadingImage: SwtLoadingImage;
    _settingButton: SwtButton;
    _csv: SwtButton;
    _excel: SwtButton;
    _pdf: SwtButton;
    _fatcaReport: SwtButton;
    _helpIcon: SwtHelpButton;
    buttonsTemplate: ElementRef;
    commonTemplate: ElementRef;
    buttons: string;
    actionPathReport: string;
    addParamReport: string;
    swtKVParams: string;
    baseURL: string;
    actionMethod: string;
    actionPath: string;
    requestParams: any[];
    urlReportSetting: string;
    gridReport: Object;
    menuAccess: string;
    programId: string;
    undockWindow: string;
    initXValue: number;
    initYValue: number;
    componentId: string;
    reportGroupId: string;
    newVBox: VBox;
    SwtAlert: SwtAlert;
    jsonReader: JSONReader;
    lastRecievedJSON: any;
    prevRecievedJSON: any;
    addButton: SwtButton;
    changeButton: SwtButton;
    viewButton: SwtButton;
    duplicateButton: SwtButton;
    deleteButton: SwtButton;
    searchButton: SwtButton;
    cancelButton: SwtButton;
    saveButton: SwtButton;
    printIcon: SwtButton;
    closeButton: SwtButton;
    deleteMethod: Function;
    duplicateMethod: Function;
    popupClosedEvent: Function;
    kvParamsMethod: Function;
    super: SwtCommonModule;
    urlDetailsPathAdd: string;
    urlDetailsPathChange: string;
    urlDetailsPathView: string;
    urlDetailsPathDelete: string;
    urlDetailsPathDuplicate: string;
    urlDetailsPathSave: string;
    childPanelTitleAdd: string;
    childPanelTitleChange: string;
    childPanelTitleView: string;
    childPanelTitleDelete: string;
    childPanelTitleDuplicate: string;
    addComponent: any;
    changeComponent: any;
    viewComponent: any;
    deleteComponent: any;
    duplicateComponent: any;
    reportSettingComponent: any;
    saveComponent: any;
    vBoxCustomGridPaddingTop: number;
    lockedColumnCount: number;
    uniqueColumn: string;
    swtCommonGrid: SwtCommonGrid;
    eventString: string;
    screenName: string;
    versionNumber: string;
    availableScreen: string;
    objectType: string;
    recordId: string;
    partyType: string;
    closeURL: string;
    protected mLoader: ModuleLoader;
    protected childPanel: TitleWindow;
    protected logger: Logger;
    private _inputData;
    private showJSON;
    private popUpScreen;
    context: any;
    constructor(_element: ElementRef, _commonService: CommonService);
    /**
     * This method fired on commonModule load.
     */
    onCommonModuleInit(event: any): void;
    onCommonModuleBeforeInit(event: any): void;
    /**
     * This is a report icon action handler method
     * @param type
     * @param displayFilter
     * @param xmlDataSource
     */
    report(type: string, displayFilter?: HashMap, xmlDataSource?: XML): void;
    /**
     * keyDownHandlerEvent
     *
     * @param event:  KeyboardEvent
     *
     * This is a key event listener, used to perform the operation
     * when hit the enter key based on the currently focused property(Button)
     */
    keyDownHandlerEvent(event: any): void;
    /**
     * getSetting
     *
     * @param none
     *
     * This method used to create the child report setting screen
     */
    getSetting(): void;
    /**
     * This method is used to show help
     * page.
     */
    showhelp(): void;
    /**
     * kvReportParams function that should be overriden by child classes
     */
    protected kvReportParams(): any;
    /**
     * Constructs the report parameters as xml string
     */
    protected getKVParams(): any;
    /**
     * This method is used
     * @param button
     */
    protected clickHandler(event: any): void;
    /**
     * loadModule
     *
     * This method is used to open child module
     */
    protected loadModule(title: string, url: string): void;
    /**
     * moduleReadyEventHandler
     *
     * @param event: ModuleEvent
     *
     * This method is used to load view user screen as a popup, once it is ready to load.
     */
    protected moduleReadyEventHandler(event: any): void;
    /**
     * popupClosedEventHandler
     *
     * @param event :Event
     *
     * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
     *
     */
    protected popupClosedEventHandler(event: any): void;
    /**
     * dispose
     *
     * This is a event handler, used to close the current tab/window
     */
    protected dispose(): void;
    /**
     * closeScreenHandler
     * @param event: Event
     * Function called to close fields to check maintenance screen.
     */
    protected closeScreenHandler(event: any): void;
    /**
     * moduleReadyEventHandlerReportSetting
     *
     * @param event: ModuleEvent
     *
     * This method is used to load report setting screen as a popup, once it is ready to load.
     */
    protected moduleReadyEventHandlerReportSetting(event: any): void;
    /**
     * popupClosedEventHandlerReportSetting
     *
     * @param event: Event
     *
     * This function is used to handle the child window close event
     */
    protected popupClosedEventHandlerReportSetting(event: any): void;
    /**
     * deleteAction
     */
    protected deleteAction(): void;
    /**
     * duplicateAction
     */
    protected duplicateAction(): void;
    private setDynamicButtons;
    /**
     * This method is used to split url and
     * get params from this last one.
     * @param url
     */
    private getDataFromUrl;
}
