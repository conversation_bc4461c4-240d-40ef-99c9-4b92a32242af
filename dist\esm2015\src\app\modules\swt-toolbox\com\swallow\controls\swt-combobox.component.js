/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, HostListener } from '@angular/core';
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "../utils/common.service";
import { Logger } from "../logging/logger.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { StringUtils } from '../utils/string-utils.service';
import { focusManager } from '../managers/focus-manager.service';
export class SwtComboBox {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        this.elem = elem;
        this.commonService = commonService;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        // private variable to handle drop down visibility status.
        this._isDropdownOpen = false;
        // private variable to handle selected index.
        this._selecedIndex = -1;
        // private variable to handle enabled status.
        this._enabled = true;
        // private variable to handle editable status.
        this._editable = true;
        // private variable to handle visibility.
        this._visible = true;
        // private variable to store selected item.
        this._selectedItem = { content: '', value: '', type: '', selected: 0 };
        // private function to handle drop down open.
        this._inputClick = new Function();
        // private function to handle drop down open.
        this._open = new Function();
        // private function to handle drop down close.
        this._close = new Function();
        // private function to handle comboBox focus.
        this._focus = new Function();
        // private function to handle comboBox focus out.
        this._focusout = new Function();
        // private function to handle comboBox change.
        this._change = new Function();
        // private function to handle comboBox change.
        this._onTextChange = new Function();
        // private variable to hold filter state
        this.filterState = false;
        //private variable to handle alert show
        this._alertvisiblity = false;
        //private variable to store combo data provider.
        this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
        //private variable to handle drop down closing.
        this._outside = true;
        //private variable to handle drop down closing.
        this._insideButton = false;
        //private variable to handle required fields
        this._required = false;
        // Input to handle dataLabel.
        this.dataLabel = "";
        //variable to hold interruptComms
        this.interrupted = false;
        this._ignored_validation = "";
        this.ignoredValidationArr = [];
        this._default_ignored = "closeButton,cancelButton,btnCancel,btnClose";
        this.backGroundImage = "url('assets/images/pdfUp.jpg')";
        this.onOpenSelectedIndex = -1;
        this._shiftUp = 0;
        this.showDescriptionInDropDown = false;
        this._toolTipPreviousObject = null;
        // Input to store width.
        this.prompt = "";
        // Input to store width.
        this.width = "200";
        // Input to store component id.
        this.id = "";
        this.firstCall = true;
        // Output to fire open event.
        this.open_ = new EventEmitter();
        // Output to fire open event.
        this.inputClick_ = new EventEmitter();
        // Output to fire open event.
        this.onTextChange = new EventEmitter();
        // Output to fire close event.
        this.close_ = new EventEmitter();
        // Output to fire focus event.
        this.focus_ = new EventEmitter();
        // Output to fire focus out event.
        this.focusout_ = new EventEmitter();
        // Output to fire change event.
        this.change_ = new EventEmitter();
        // initialize logger.
        this.logger = new Logger('SwtComboBox', commonService.httpclient, 6);
        // initialize setter.
        this.SwtAlert = new SwtAlert(commonService);
    }
    /**
     * @param {?} targetElement
     * @return {?}
     */
    mouseWeelEventHandler(targetElement) {
        /** @type {?} */
        let position = $(this.elem.nativeElement.children[0]).offset();
        position.top = position.top + 24;
        $(this.elem.nativeElement.children[0].children[1]).css(position);
        /** @type {?} */
        const listScrolling = this.elem.nativeElement.contains(targetElement);
        if (!listScrolling) {
            this.hideDropDown(this);
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTip(value) {
        this.toolTipObject = $(this.elem.nativeElement);
        /** @type {?} */
        let _this = this;
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.toolTipObject.attr("title", value);
            this.toolTipObject.tooltip({
                position: { my: "left+20 center", at: "right-20 bottom+20" },
                show: { duration: 800, delay: 500 },
                open: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    $(_this.toolTipObject).removeAttr('title');
                })
            });
        }), 0);
    }
    /**
     * @return {?}
     */
    get toolTip() {
        return this.toolTip;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTipPreviousValue(value) {
        this._toolTipPreviousObject = value;
    }
    /**
     * @return {?}
     */
    get toolTipPreviousValue() {
        return this._toolTipPreviousObject;
    }
    /**
     * @return {?}
     */
    get focusOutSide() {
        return this._outside;
    }
    /**
     * Setter for the _ignored_validation variable
     *
     * @param {?} value
     * @return {?}
     */
    set ignored_validation(value) {
        this._ignored_validation = value;
    }
    /**
     * Getter the _ignored_validation variable
     *
     * @return {?}
     */
    get ignored_validation() {
        return this._ignored_validation;
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set required(value) {
        if (typeof (value) == "string") {
            if (value === 'true') {
                this._required = true;
            }
            else {
                this._required = false;
            }
        }
        else {
            this._required = value;
        }
    }
    /**
     * @return {?}
     */
    get required() {
        return this._required;
    }
    /**
     * @param {?} interrupted
     * @return {?}
     */
    set interruptComms(interrupted) {
        this.interrupted = interrupted;
    }
    /**
     * @return {?}
     */
    get interruptComms() {
        return this.interrupted;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    inputClickHandler(event) {
        // emit open event.
        this.inputClick_.emit(event);
        // execute callback.
        this._inputClick(event);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    inputChangeHandler(event) {
        if (String($(this.filter.nativeElement).val()).length > 0 && this.dataProvider.length > 0 && this._outside) {
            /** @type {?} */
            let index = this.getIndexOf($(this.filter.nativeElement).val());
            if (index == -1 && this.dataProvider.length > 0) {
                this.selectedItem = this._dataProvider[0];
                this.selectItem(this._dataProvider[0], true);
            }
            else if (index == -1) {
                this.selectedIndex = -1;
            }
            else {
                this.selectedIndex = index;
            }
        }
        else if (String($(this.filter.nativeElement).val()).length == 0) {
            this.selectedIndex = -1;
        }
        else {
            this.selectedIndex = -1;
        }
    }
    // Input to hold comboBox dataProvider
    /**
     * @param {?} dataProvider
     * @return {?}
     */
    set dataProvider(dataProvider) {
        try {
            if (dataProvider !== null && dataProvider.length > 0) {
                for (var i = 0; i < dataProvider.length; i++) {
                    if (dataProvider[i].content === undefined) {
                        dataProvider[i]["content"] = "";
                    }
                }
                if (dataProvider.length > 0) {
                    // update combo dataProvider.
                    this._dataProvider = dataProvider;
                    if (this.selectedIndex == 0) {
                        // update selected label and text
                        this._selectedLabel = this._text = dataProvider[0];
                    }
                    // store copy of data provider.
                    this._originalData = dataProvider;
                    // store copy of data provider.
                    this._tempData = dataProvider;
                    // update selected item.
                    this.updateSelectedItem(dataProvider[0]);
                }
            }
            else {
                this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
                $(this.filter.nativeElement).val("");
            }
        }
        catch (error) {
            this.logger.error("method [ dataProvider ] - error :", error);
        }
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    // enabled getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._enabled = true;
                this.filter.nativeElement.disabled = false;
                $(this.elem.nativeElement.children[0].children[0].children[1]).bind("click");
                $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).removeClass("disable");
                $(this.elem.nativeElement.children[0].children[0].children[1]).removeClass("input-group-addon-disable");
            }
            else {
                this._enabled = false;
                this.filter.nativeElement.disabled = true;
                $(this.elem.nativeElement.children[0].children[0].children[1]).unbind("click");
                $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).addClass("disable");
                $(this.elem.nativeElement.children[0].children[0].children[1]).addClass("input-group-addon-disable");
            }
        }
        else {
            this.filter.nativeElement.disabled = !value;
            this._enabled = value;
            if (value) {
                $(this.elem.nativeElement.children[0].children[0].children[1]).click((/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.onArrowClick(event);
                }));
                $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).removeClass("disable");
                $(this.elem.nativeElement.children[0].children[0].children[1]).removeClass("input-group-addon-disable");
            }
            else {
                $(this.elem.nativeElement.children[0].children[0].children[1]).unbind("click");
                $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).addClass("disable");
                $(this.elem.nativeElement.children[0].children[0].children[1]).addClass("input-group-addon-disable");
            }
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    // enabled getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._editable = true;
                this.filter.nativeElement.readOnly = false;
            }
            else {
                this._editable = false;
                this.filter.nativeElement.readOnly = true;
            }
        }
        else {
            this.filter.nativeElement.readOnly = !value;
            this._editable = value;
        }
    }
    /**
     * @return {?}
     */
    get editable() {
        return this._enabled;
    }
    // enabled getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set shiftUp(value) {
        this._shiftUp = value;
    }
    /**
     * @return {?}
     */
    get shiftUp() {
        return this._shiftUp;
    }
    // enabled getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._visible = true;
                $(this.elem.nativeElement).show();
            }
            else {
                this._visible = false;
                $(this.elem.nativeElement).hide();
            }
        }
        else {
            if (value) {
                $(this.elem.nativeElement).show();
            }
            else {
                $(this.elem.nativeElement).hide();
            }
            this._visible = value;
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    // This method will be fired after vieww init.
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        if (this.shiftUp != 0) {
            $(this.dropDownContainer.nativeElement).css("margin-top", "-" + this.shiftUp + "px");
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        try {
            $($(this.listitem.nativeElement)[0]).on("mouseenter", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this._outside = false;
            }));
            $($(this.listitem.nativeElement)[0]).on("mouseleave", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this._outside = true;
            }));
            $(this.elem.nativeElement.children[0].children[0].children[1]).on("mouseenter", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this._insideButton = true;
            }));
            $(this.elem.nativeElement.children[0].children[0].children[1]).on("mouseleave", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this._insideButton = false;
            }));
            // show drop down list in filter.
            $(this.elem.nativeElement.children[0].children[0].children[0]).keydown((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                // if arrow up clicked.
                if (event.keyCode === 38) {
                    if (this.selectedIndex > 0) {
                        this.selectedIndex--;
                    }
                    if (!this._isDropdownOpen) {
                        this.change_.emit({ target: this, event: event });
                    }
                    // update the selected item.
                    this.selectedIndex > 0 ? this.updateSelectedItem(this.dataProvider[this.selectedIndex]) : null;
                    // synchronize the scrolling.
                    this.scrollToTop();
                }
                else if (event.keyCode === 40) {
                    if (this.selectedIndex < this.dataProvider.length - 1) {
                        this.selectedIndex++;
                    }
                    if (!this._isDropdownOpen) {
                        this.change_.emit({ target: this, event: event });
                    }
                    // update the selected item.
                    this.selectedIndex < this.dataProvider.length - 1 ? this.updateSelectedItem(this.dataProvider[this.selectedIndex]) : null;
                    // synchronize the scrolling.
                    this.scrollToBottom();
                    // if ENTER key clicked.
                }
                else if (event.keyCode === 13) {
                    // select the current item.
                    if (($(this.filter.nativeElement).val()).length > 0) {
                        this.fillSelectedIndexWithFiltertext();
                    }
                    this.selectItem(this.dataProvider[this.selectedIndex], false, true);
                    if (this.selectedIndex == -1)
                        this.showAlert();
                    this.updateSelectedItem(this.selectedItem);
                    this._dataProvider = this._originalData;
                    this.change_.emit({ target: this, event: event });
                }
                else if (event.keyCode === 27) {
                    this.hideDropDown(event);
                }
                else if (event.keyCode === 9) {
                    this.hideDropDown(event);
                }
                else {
                    this.filterDropDown(event, true);
                }
                // if arrow down clicked.
                this.onTextChange.emit({ target: this, event: event });
            }));
            //show drop down on arrow click.
            if (this.enabled) {
                $(this.elem.nativeElement.children[0].children[0].children[1]).click((/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.onArrowClick(event);
                }));
            }
            else {
                $(this.elem.nativeElement.children[0].children[0].children[1]).css("backround-color", "red");
            }
            // handle comboBox focus look and feel.
            $(this.elem.nativeElement.children[0].children[0].children[0]).focus((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.onOpenSelectedIndex = this.selectedIndex;
                // set comboBox border.
                //              $($(this.elem.nativeElement.children[0].children[0])[0]).css("border","1px solid #49B9FF");
                // remove comboBox border.
                //              $($(this.elem.nativeElement.children[0].children[0])[0]).css("border-radius","3px");
                // emit focus event.
                this.focus_.emit(event);
                // execute callback
                this._focus(event);
                if (this.id != "exportDataComponent") {
                    $(this.elem.nativeElement.children[0]).css("outline-style", "solid");
                    $(this.elem.nativeElement.children[0]).css("outline-color", "#49B9FF");
                    $(this.elem.nativeElement.children[0]).css("outline-width", "2px");
                }
                else {
                    this.filter.nativeElement.readOnly = true;
                    this.filter.nativeElement.blur();
                }
            }));
            // handle comboBox focus out.
            $(this.elem.nativeElement.children[0].children[0].children[0]).focusout((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                /** @type {?} */
                let tempIndex = this.selectedIndex;
                if (String($(this.filter.nativeElement).val()).length > 0 && this.dataProvider.length > 0 && this._outside) {
                    /** @type {?} */
                    let index = this.getIndexOf($(this.filter.nativeElement).val());
                    if (index == -1) {
                        this.selectedItem = this._dataProvider[0];
                        this.selectItem(this._dataProvider[0], true);
                    }
                    else {
                        this.selectedIndex = index;
                    }
                }
                else if (String($(this.filter.nativeElement).val()).length == 0) {
                    // update selected item.
                    this._selectedItem = null;
                    // update selected value
                    this._selectedValue = null;
                    // update selected label.
                    this._selectedLabel = null;
                    // update text
                    this._text = null;
                    //update selected index.
                    this._selecedIndex = -1;
                }
                // emit focus out event.
                this.focusout_.emit(event);
                //execute callback
                this._focusout(event);
                this.showAlert();
                $(this.elem.nativeElement.children[0]).css("outline", "none");
            }));
            $(this.elem.nativeElement.children[0].children[0].children[0]).focusout((/**
             * @return {?}
             */
            () => {
                // remove comboBox border.
                $($(this.elem.nativeElement.children[0].children[0])[0]).css("border", "none");
                // remove ComboBox Border radius.
                $($(this.elem.nativeElement.children[0].children[0])[0]).css("border-radius", "3px");
            }));
            // close drop down by clicking outside.
            $(document).on('mouseup.' + this.id, (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                if (this._isDropdownOpen) {
                    if (!$($(this.elem.nativeElement.children[0].children[0])[0]).is(event.target)
                        && $(this.elem.nativeElement.children[0].children[1].children[0]).has(event.target).length === 0) {
                        if (this._outside) {
                            // hide drop down.
                            this.hideDropDown(event);
                        }
                    }
                }
            }));
            // set comboBox width
            $(this.elem.nativeElement.children[0]).width(this.width);
            $(this.elem.nativeElement.children[0].children[1]).width(this.width);
            //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtComboBox.
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtComboBox');
            // set id to button DOM.
            if (this.id) {
                $($(this.elem.nativeElement)[0]).attr("id", this.id);
            }
            //-END-.
        }
        catch (error) {
            this.logger.error("method [ ngOnInit ] - error :", error);
        }
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        $((/** @type {?} */ (document))).on('mouseup.' + this.id).off('mouseup.' + this.id);
        try {
            if (this.elem.nativeElement.children && this.elem.nativeElement.children[0] && this.elem.nativeElement.children[0].children[0]) { }
            $(this.elem.nativeElement.children[0].children[0].children[0]).off();
            $(this.elem.nativeElement.children[0].children[0].children[1]).off();
            $($(this.listitem.nativeElement)[0]).off();
        }
        catch (error) {
        }
    }
    /**
     * This method is called when arrow clicked.
     * @private
     * @param {?} event
     * @return {?}
     */
    onArrowClick(event) {
        // check drop down visibility
        if ($(this.elem.nativeElement.children[0].children[1].children[0]).is(":visible")) {
            // hide drop down if is open.
            this.hideDropDown(event);
        }
        else {
            // show drop down if is closed.
            this.showDropDown(event);
            // set comboBox filter focus.
            $(this.filter.nativeElement).focus();
        }
        if (this.filterState) {
            this._dataProvider = this._originalData;
            this.filterState = false;
        }
    }
    /**
     * This method is used to filter comboBox.
     * @private
     * @param {?} event
     * @param {?=} fromKeyEvent
     * @return {?}
     */
    filterDropDown(event, fromKeyEvent = false) {
        try {
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.filterState = true;
                /** @type {?} */
                const value = $(this.filter.nativeElement).val();
                /** @type {?} */
                const filtredDp = this._tempData.filter((/**
                 * @param {?} item
                 * @return {?}
                 */
                (item) => {
                    if (this.showDescriptionInDropDown) {
                        return (String(item.content).toUpperCase().indexOf(String($(this.filter.nativeElement).val()).toUpperCase())) > -1 || (String(item.value).toUpperCase().indexOf(String($(this.filter.nativeElement).val()).toUpperCase())) > -1;
                    }
                    else {
                        return (String(item.content).toUpperCase().indexOf(String($(this.filter.nativeElement).val()).toUpperCase())) > -1;
                    }
                }));
                this._dataProvider = filtredDp;
                if (fromKeyEvent)
                    this.showDropDown(event, 0);
                else {
                    this.highlightItem(0);
                    this.showDropDown(event);
                }
            }), 0);
        }
        catch (error) {
            this.logger.error("method [ filterDropDown ] - error :", error);
        }
    }
    /**
     * This method is used to filter comboBox.
     * @private
     * @return {?}
     */
    fillSelectedIndexWithFiltertext() {
        try {
            this.filterState = true;
            /** @type {?} */
            const value = $(this.filter.nativeElement).val();
            /** @type {?} */
            const filtredDp = this._tempData.filter((/**
             * @param {?} item
             * @return {?}
             */
            (item) => {
                return String(item.content).toUpperCase().startsWith(String($(this.filter.nativeElement).val()).toUpperCase());
            }));
            this._dataProvider = filtredDp;
            if (filtredDp.length > 0)
                this._selecedIndex = 0;
            else
                this._selecedIndex = -1;
        }
        catch (error) {
            this.logger.error("method [ filterDropDown ] - error :", error);
        }
    }
    /**
     * This method is used to refresh
     * comboBox view.
     * @private
     * @return {?}
     */
    showAlert() {
        // See if the focus is given to close button, If 'yes', then do not not validate
        if (this.ignoredValidationArr.length == 0) {
            /** @type {?} */
            var arr = this._ignored_validation != "" ? this._ignored_validation.split(",") : this._default_ignored.split(",");
            this.ignoredValidationArr = [];
            arr.forEach((/**
             * @param {?} element
             * @return {?}
             */
            (element) => {
                this.ignoredValidationArr.push(StringUtils.trim(element));
            }));
        }
        //Currently focused property name
        /** @type {?} */
        let focusControl = focusManager.getHoverButton();
        if ((focusControl && this.ignoredValidationArr.indexOf(focusControl.id) != -1) || (this._tempData && this._tempData.length == 0))
            return;
        try {
            /** @type {?} */
            let entryExist = false;
            for (let i = 0; i < this.dataProvider.length; i++) {
                this.dataProvider[i].content = this.dataProvider[i].content || '';
                if (this.dataProvider[i] && String($(this.filter.nativeElement).val()) == this.dataProvider[i].content) {
                    entryExist = true;
                    break;
                }
            }
            if (!entryExist && (!this._insideButton && this._outside) && !this.prompt) {
                this.SwtAlert.warning("Please select the valid value", "Warning", 4, this, (/**
                 * @return {?}
                 */
                () => {
                    this.setFocus();
                }), 4);
            }
        }
        catch (error) {
        }
    }
    /**
     * @private
     * @return {?}
     */
    setFilterFocus() {
        $(this.filter.nativeElement).focus();
        this._alertvisiblity = false;
    }
    /**
     * This method is used to show comoboBox drop Down.
     * @private
     * @param {?} event
     * @param {?=} highlightIndex
     * @return {?}
     */
    showDropDown(event, highlightIndex = -1) {
        try {
            /** @type {?} */
            let position = $(this.elem.nativeElement.children[0]).offset();
            position.top = position.top + 24;
            $(this.elem.nativeElement.children[0].children[1]).css(position);
            setTimeout((/**
             * @return {?}
             */
            () => {
                // make drop down list reference.
                /** @type {?} */
                const list = this.listitem.nativeElement;
                // scroll to the selected item.
                list.scrollTop = this.selectedIndex * 21;
                // Highlight selected item.
                if (highlightIndex > -1)
                    this.highlightItem(highlightIndex);
                else
                    this.highlightItem(this.selectedIndex);
                if (!this._isDropdownOpen) {
                    // emit open event.
                    this.onOpenSelectedIndex = this.selectedIndex;
                    event.currentTargetObject = this;
                    this.open_.emit(event);
                    // execute callback.
                    this._open(event);
                }
                // show comboBox items with animation.
                if (this.dataProvider.length > 0) {
                    $(this.elem.nativeElement.children[0].children[1]).slideDown(150);
                }
                // update flag.
                this._isDropdownOpen = true;
            }), 0);
        }
        catch (error) {
            this.logger.error("method [ showDropDown ] - error :", error);
        }
    }
    /**
     * This method is used to hide comboBox drop down.
     * @private
     * @param {?} event
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    hideDropDown(event, notFireChangeEvent = false) {
        try {
            // hide comboBox items with animation
            $(this.elem.nativeElement.children[0].children[1]).slideUp(10);
            if (this._isDropdownOpen) {
                // emit open event.
                event.currentTargetObject = this;
                // emit close event.
                this.close_.emit(event);
                // execute callback.
                this._close(event);
                if (this.onOpenSelectedIndex != this.selectedIndex || (this.id && this.id == "filterComboMSD")) {
                    if (!notFireChangeEvent)
                        this.change_.emit({ target: this });
                    // console.log(this.firstCall);
                    if (this.firstCall) {
                        this.originalValue = this.dataProvider[this.onOpenSelectedIndex];
                        this.firstCall = false;
                    }
                    /** @type {?} */
                    let item = this.dataProvider[this.selectedIndex];
                    //execute callback.
                    this._change(item);
                    this.spyChanges(item);
                }
            }
            // remove select behavior from all items.
            $("li").removeClass("selected");
            // update flag.
            this._isDropdownOpen = false;
        }
        catch (error) {
            this.logger.error("method [ hideDropDown ] - error :", error);
        }
    }
    /**
     * This method i used to set item to
     * dataProvioder.
     * @param {?} item
     * @return {?}
     */
    addItem(item) {
        this.addItemAt(item, this._dataProvider.length);
    }
    /**
     * This method is used to add item at position.
     * @param {?} item
     * @param {?} position
     * @return {?}
     */
    addItemAt(item, position) {
        this._dataProvider.splice(position, 0, item);
        if (position === 0) {
            this.updateSelectedItem(item);
        }
    }
    /**
     * This method is used to select option.
     * @param {?} item
     * @param {?=} fromView
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    selectItem(item, fromView = false, notFireChangeEvent = false) {
        try {
            // Update item
            this.updateSelectedItem(item, fromView);
            // hide drop down.
            // Create the event.
            /** @type {?} */
            var event = document.createEvent('Event');
            // Define that the event name is 'build'.
            event.initEvent('build', true, true);
            this.hideDropDown(event, notFireChangeEvent);
        }
        catch (error) {
            this.logger.error("method [ slectOption ] - error :", error);
        }
    }
    /**
     * @private
     * @param {?} item
     * @param {?=} fromView
     * @return {?}
     */
    updateSelectedItem(item, fromView = false) {
        this.logger.info("updateSelectedItem START");
        try {
            // temporary variable to store selected index.
            /** @type {?} */
            let tempIndex = this.selectedIndex;
            // get filter input reference.
            /** @type {?} */
            let filter = this.filter.nativeElement;
            if (item !== undefined && item !== null) {
                // update selected item.
                this._selectedItem = item;
                // update selected value
                this._selectedValue = item.value;
                // update selected label.
                this._selectedLabel = item.content;
                // update text
                this._text = item.content;
                // highlight selected item.
                this.highlightItem(this.getIndexOf(item.content));
                //update selected index.
                this._selecedIndex = this.getIndexOf(item.content);
                // set value if item exist
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    $(filter).val(item.content);
                }), 0);
            }
            else {
                // set value if item exist
                filter.value = "";
                // update selected item.
                this._selectedItem = null;
                // update selected value
                this._selectedValue = null;
                // update selected label.
                this._selectedLabel = null;
                // update text
                this._text = null;
                //update selected index.
                this._selecedIndex = -1;
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    $(filter).val("");
                }), 0);
            }
            // emit change event if selected item changed.
            //   if (tempIndex !== this.selectedIndex && fromView) {
            //         this.change_.emit( {target: this});
            //           if(this.firstCall){
            //               this.originalValue = this.dataProvider[tempIndex];
            //               this.firstCall = false;
            //           }
            //           //execute callback.
            //           this._change(item);
            //         this.spyChanges(item);
            //   }
        }
        catch (error) {
            this.logger.error("method [updateSelectedItem] - error ", error);
        }
        this.logger.info("updateSelectedItem END");
    }
    /**
     * @param {?} prompt
     * @return {?}
     */
    setPrompt(prompt) {
        $(this.filter.nativeElement).attr("placeholder", prompt);
    }
    /**
     * @param {?} input
     * @param {?=} reverse
     * @return {?}
     */
    setComboData(input, reverse = false) {
        this.setComboDataAndForceSelected(input, reverse, null);
    }
    /**
     * @param {?} input
     * @param {?=} reverse
     * @param {?=} selectedValue
     * @return {?}
     */
    setComboDataAndForceSelected(input, reverse = false, selectedValue) {
        try {
            this.logger.info("setComboData START");
            /* This time out is added to fix issue that when ComboBox created dynamically
                     * and when we set datProvider using setComboData function we obtain dropDown list
                     * but the selected item not appear in filter of comboBox.
                     */
            //          setTimeout(() => {
            // temporary array to inverse dataProvider.
            /** @type {?} */
            let tempData = new Array();
            // variable to store selected item.
            /** @type {?} */
            let selected;
            if (!input) {
                this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
                $(this.filter.nativeElement).val("");
                return;
            }
            this._selectedItem = input[0];
            if (!reverse) {
                // find the correspondent dataProvider from selects
                if (JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")]').length > 0) {
                    // update dataProvider.
                    /** @type {?} */
                    var data = JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0];
                    if (data) {
                        if (data.length) {
                            this._dataProvider = this._tempData = this.reverse(JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]);
                        }
                        else {
                            this._dataProvider = this._tempData = this.reverse([JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]]);
                        }
                    }
                    // find the selected item.
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
                else {
                    if (input.length === undefined) {
                        input = [input];
                    }
                    this._dataProvider = this.reverse(input);
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
            }
            else {
                // find the correspondent dataProvider from selects
                if (JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")]').length > 0) {
                    // update dataProvider.
                    /** @type {?} */
                    var data = JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0];
                    if (data) {
                        if (data.length) {
                            this._dataProvider = this._tempData = [...JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]];
                        }
                        else {
                            this._dataProvider = this._tempData = [...[JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]]];
                        }
                    }
                    // find the selected item.
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
                else {
                    if (input.length === undefined) {
                        input = [input];
                    }
                    this._dataProvider = [...input];
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
            }
            // store copy of data provider.
            this._originalData = [...this.dataProvider];
            //          }, 0);
            this.logger.info("setComboData END");
        }
        catch (error) {
            this.logger.error("method [setComboData] - error ", error);
        }
        this.logger.debug('method [setComboData] Exit');
    }
    // handle highlighting
    /**
     * @private
     * @param {?} index
     * @return {?}
     */
    highlightItem(index) {
        try {
            // make reference to item.
            /** @type {?} */
            let list = this.listitem.nativeElement.children;
            // remove the select behavior from all items.
            $("li").removeClass("selected");
            // select the item in the given index.
            $(list[index]).addClass("selected");
        }
        catch (error) {
            this.logger.error("method [highlightItem] - error ", error);
        }
    }
    /**
     * This method is used to get index of given item.
     * @param {?} item
     * @return {?}
     */
    getIndexOf(item) {
        try {
            // return the item index.
            if (this._tempData)
                return this._tempData.findIndex((/**
                 * @param {?} option
                 * @return {?}
                 */
                option => option.content == item));
            else
                return -1;
        }
        catch (error) {
            this.logger.error("method [getIndexOf] - error ", error);
        }
    }
    /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    scrollToBottom() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.listitem.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[itemIndex];
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    let viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error("method [scrollToBottom] - error ", error);
        }
    }
    /**
     * @private
     * @param {?} data
     * @return {?}
     */
    reverse(data) {
        try {
            /** @type {?} */
            let tempData = new Array();
            /** @type {?} */
            let tempVar;
            for (var index = 0; index < data.length; index++) {
                tempVar = new Object;
                tempVar.content = data[index].value;
                tempVar.value = data[index].content;
                tempVar.selected = data[index].selected;
                tempVar.type = data[index].type;
                tempData.push(tempVar);
            }
            return tempData;
        }
        catch (error) {
            this.logger.error("method [reverse] - error ", error);
        }
    }
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    scrollToTop() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.listitem.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[itemIndex];
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error("method [scrollToTop] - error ", error);
        }
    }
    // selected value setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedValue(value) {
        try {
            /** @type {?} */
            var itemIndex = 0;
            for (var index = 0; index < this.dataProvider.length; index++) {
                if (String(this.dataProvider[index].value).toUpperCase() === value.toUpperCase()) {
                    itemIndex = index;
                    break;
                }
            }
            this.updateSelectedItem(this.dataProvider[itemIndex]);
        }
        catch (error) {
            this.logger.error("method [selectedValue] - error ", error);
        }
    }
    /**
     * @return {?}
     */
    appendOption() {
        /** @type {?} */
        var item = $(this.dropDownli.nativeElement)[0];
        /** @type {?} */
        var option = item.append(new Option("option test example", "test"));
        $(this.listitem.nativeElement)[0].append(option);
    }
    // selected label getter.
    /**
     * @return {?}
     */
    get selectedValue() {
        if (this._selectedItem)
            if (this._selectedItem.value != undefined && this._selectedItem.value != null)
                return String(this._selectedItem.value);
            else
                return this._selectedItem.value;
        else
            return null;
    }
    // selected label setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedLabel(value) {
        try {
            /** @type {?} */
            var itemIndex = 0;
            for (var index = 0; index < this.dataProvider.length; index++) {
                if (String(this.dataProvider[index].content).toUpperCase() === value.toUpperCase()) {
                    itemIndex = index;
                    break;
                }
            }
            this.updateSelectedItem(this.dataProvider[itemIndex]);
        }
        catch (error) {
            this.logger.error("method [selectedValue] - error ", error);
        }
    }
    /**
     * This method is used to remove all element from
     * comboBox dataProvider
     * @return {?}
     */
    removeAll() {
        this._dataProvider = new Array();
        this.selectedIndex = -1;
    }
    /**
     * This method is used to remove element from
     * comboBox dataProvider in specific  index.
     * @param {?} index
     * @return {?}
     */
    removeItemAt(index) {
        this._dataProvider.splice(index, 1);
        if (index > 0) {
            this.selectItem(this._dataProvider[index - 1], false);
        }
        else {
            this.selectItem(this._dataProvider[0], false);
        }
    }
    /**
     * This method is used to set focus
     * to comboBox.
     * @return {?}
     */
    setFocus() {
        $(this.filter.nativeElement).focus();
    }
    // selected value getter.
    /**
     * @return {?}
     */
    get selectedLabel() {
        if (this._selectedItem)
            if (this._selectedItem.content != undefined && this._selectedItem.content != null)
                return String(this._selectedItem.content);
            else
                return this._selectedItem.content;
        else
            return null;
    }
    /**
     * @return {?}
     */
    getbackGroundImange() {
        if (this._selectedItem && this._selectedItem.iconImage)
            if (!this._selectedItem.iconImage.startsWith('url('))
                return 'url(' + this._selectedItem.iconImage + ")";
            else
                return "";
    }
    // method to set combo visibility.
    /**
     * @param {?} visibility
     * @return {?}
     */
    setVisible(visibility) {
        this._visible = visibility;
        this.visible = visibility;
    }
    // comboBox getter and setter
    /**
     * @return {?}
     */
    get isDropdownOpen() {
        return this._isDropdownOpen;
    }
    //selected index setter.
    /**
     * @param {?} index
     * @return {?}
     */
    set selectedIndex(index) {
        try {
            /* This timeout is added to fix the issue
             * that if we set selectedIndex always the first item is selected
             * for example:
             * this.comboBox.selecctedIndex = x;
             * the selected item is still unchanged.(in the view we get the first item selected).
             * same problem of selectedLabel
             * */
            //          setTimeout(() => {
            // verify that index not null and not undefined.
            if (index == -1) {
                this.updateSelectedItem(null);
                // update selected Index variable.
                this._selecedIndex = index;
                if (this.firstCall) {
                    this.originalValue = null;
                    this.firstCall = false;
                }
            }
            else if (index <= this.dataProvider.length) {
                // select item in the given index.
                this.updateSelectedItem(this.dataProvider[index]);
                // update selected Index variable.
                this._selecedIndex = index;
                if (this.firstCall) {
                    this.originalValue = this.dataProvider[index];
                    this.firstCall = false;
                }
            }
            else {
                this.updateSelectedItem(this.dataProvider[this.selectedIndex]);
            }
            //          }, 0);
        }
        catch (error) {
            console.error("[ SwtComboBox ] - selectedIndex method - error :", error);
        }
    }
    // selected index getter.
    /**
     * @return {?}
     */
    get selectedIndex() {
        return this._selecedIndex;
    }
    // text getter
    /**
     * @return {?}
     */
    get text() {
        return this._text;
    }
    // text setter
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            $(this.filter.nativeElement).val(value);
        }));
    }
    /**
     * @param {?} item
     * @return {?}
     */
    set selectedItem(item) {
        this._selectedItem = item;
    }
    // selectedItem getter
    /**
     * @return {?}
     */
    get selectedItem() {
        return this._selectedItem;
    }
    /**
     * @param {?} callback
     * @return {?}
     */
    set open(callback) {
        this._open = callback;
    }
    /**
     * @return {?}
     */
    get open() {
        return this._open;
    }
    /**
     * @param {?} callback
     * @return {?}
     */
    set inputClick(callback) {
        this._inputClick = callback;
    }
    /**
     * @return {?}
     */
    get inputClick() {
        return this._inputClick;
    }
    /**
     * @param {?} callback
     * @return {?}
     */
    set close(callback) {
        this._close = callback;
    }
    /**
     * @return {?}
     */
    get close() {
        return this._close;
    }
    //  set focus(callback: Function) {
    //      this._focus = callback;
    //  }
    //  get focus() {
    //      return this._focus;
    //  }
    /**
     * @param {?} callback
     * @return {?}
     */
    set focusout(callback) {
        this._focusout = callback;
    }
    /**
     * @return {?}
     */
    get focusout() {
        return this._focusout;
    }
    /**
     * @param {?} callback
     * @return {?}
     */
    set change(callback) {
        this._change = callback;
    }
    /**
     * @return {?}
     */
    get change() {
        return this._change;
    }
    /**
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue = this._selectedItem;
        this.spyChanges(this._selectedItem);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (!this.originalValue) {
            this.originalValue = this.dataProvider[this._selecedIndex];
            this.firstCall = false;
        }
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    }
}
SwtComboBox.decorators = [
    { type: Component, args: [{
                selector: 'SwtComboBox',
                template: `
       <div class="SwtComboBox-container" 
       popper="{{this.toolTipPreviousValue}}"
             [popperTrigger]="'hover'"
             [popperDisabled]="toolTipPreviousValue === null ? true : false"
       [popperPlacement]="'bottom'" >
          <div #inputGroup class="input-group">
            <input #filter type="text"
            (click)=inputClickHandler($event)
            [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}"
            (change)="inputChangeHandler($event)"
            placeholder="{{prompt}}"
            [style.background-image]="this.getbackGroundImange()"
            [class.requiredInput]= "required==true && enabled==true && !this.filter.value"
            class="form-control swtcombobox-filter-input" >
            <div class="input-group-addon"><i class="glyphicon glyphicon-triangle-bottom"></i></div> 
          </div>
          <div #dropDownContainer class="swtcomboBox-dropDown" >
              <ul #listitem class="swtcomboBox-dropDown-ul">
                  <li #dropDownli *ngFor="let item of dataProvider let count = index" 
                      [ngClass]="{'selected':count == 0}"
                      class="swtcomboBox-dropDown-li"
                      (click)="selectItem(item, true)">
                        <a *ngIf="item.iconImage">
                            <img src="{{item.iconImage }}" alt="Facebook Icon"  />
                        </a>
                        <option class="combo-option" value="{{ item.value }}" style="display: inline-block;"> 
                        {{ item.content }}
                      </option>{{ showDescriptionInDropDown && item.value? '  '+item.value : '' }}
                  </li>
                  <!--<li *ngIf="_exist">{{ notFound }}</li>-->
              </ul>
          </div>
        </div>
  `,
                styles: [`
      .SwtComboBox-container {
         /*width: 260px;  this width must be updated with jquery*/
            margin: 0 5px 5px 0;
      }

      .input-group {
         width: 100%;
      }

      .form-control {
          background-repeat: no-repeat;
          background-position: center; 
          background-size: 100% 100%;
          padding: 0px 0px 0px 10px;
          background-color: #FFF;
            z-index: 0;
      }

      .disable {
          color: #919999;
      }

      .swtcomboBox-dropDown-ul {
          padding: 0px;
          width: auto;
          background-color: white;
          margin: 0px;
          max-height: 150px;
          overflow-y: auto;
          overflow-x: hidden;
          padding-bottom: 3px;
          cursor: default;
          word-wrap: break-word;
      }

      .swtcomboBox-dropDown-li {
         list-style-type: none;
         width: 100%;
         height: auto;
         padding-left: 10px;
         font-size: 11px;
         height: 21px;
         line-height: 21px;
      }

      .swtcomboBox-dropDown-li:hover {
         background-color: #0000A0;
         color: #FFF;
      }
      
      .combo-option:hover {
        background-color: #0000A0;
        color: #FFF;
      }

      li.selected {
        background-color: #0000A0;
        color: #FFF;
      }

      .swtcomboBox-selectedItem {
          background-color: #0000A0;
          color: #FFF;
      }

      .swtcomboBox-dropDown-li> a {
          font-family: verdana,helvetica;
          font-size: 11px;
      }

      .input-group-addon {
          width: 22px;
          height:22px;
            border-top: 1px solid #7F9DB9;
          border-right:1px solid #637A90;
          border-bottom:1px solid #415160;
            border-left: 1px solid #637A90;
          padding: 0px;
          margin: 0px;
          font-size: 8px;
            background-image: -webkit-linear-gradient(top, #DBF1FF, #A7C6DE);
          background-image: -moz-linear-gradient(top, #DBF1FF, #A7C6DE);
          background-image: -ms-linear-gradient(top, #DBF1FF, #A7C6DE);
          background-image: -o-linear-gradient(top, #DBF1FF, #A7C6DE);
            background-image: linear-gradient(to bottom, #DBF1FF, #A7C6DE);
      }

      .input-group-addon-disable {
          width: 22px;
          height:22px;
          border-top:1px solid #A5C4DC;
          border-right:1px solid #96B1C6;
          border-bottom:1px solid #869EB0;
          border-left:1px solid #96B1C6;
          padding: 0px;
          margin: 0px;
          font-size: 8px;
          background-image: -webkit-linear-gradient(top, #CAEBFE, #B1D0E7);
          background-image: -moz-linear-gradient(top, #CAEBFE, #B1D0E7);
          background-image: -ms-linear-gradient(top, #CAEBFE, #B1D0E7);
          background-image: -o-linear-gradient(top, #CAEBFE, #B1D0E7);
          background-image: linear-gradient(to bottom, #CAEBFE, #B1D0E7);
      }

      .glyphicon {
         font-size: 9px !important;
         position: relative;
         top: 2px;
         cursor: default;
      }

      .swtcombobox-filter-input {
        height: 22px;
        font-size: 12px;
        border-radius: 0px;
        border-top: 1px solid #4C5E6F;
        border-right: 1px solid #9FB5CA;
        border-bottom:1px solid #B2C4D5;
        border-left:1px solid #9FB5CA;
        font-family: verdana,helvetica;
            font-size: 11px;
      }

      .swtcomboBox-dropDown {
         position: fixed;
         z-index: 999;
         background-color: #FFF;
         display: none;
         border: 1px solid #D9D9D9;
         box-shadow: 0px 4px 5px #999999;
         width: 260px; /* this width must be updated with jquery*/
      }
    
  `]
            }] }
];
/** @nocollapse */
SwtComboBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtComboBox.propDecorators = {
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    mouseWeelEventHandler: [{ type: HostListener, args: ["window:mousewheel", ["$event.target"],] }],
    dataLabel: [{ type: Input, args: ["dataLabel",] }],
    toolTip: [{ type: Input }],
    toolTipPreviousValue: [{ type: Input }],
    prompt: [{ type: Input, args: ["prompt",] }],
    width: [{ type: Input, args: ["width",] }],
    height: [{ type: Input, args: ["height",] }],
    id: [{ type: Input, args: ["id",] }],
    required: [{ type: Input, args: ['required',] }],
    dataProvider: [{ type: Input }],
    open_: [{ type: Output, args: ["open",] }],
    inputClick_: [{ type: Output, args: ["inputClick",] }],
    onTextChange: [{ type: Output, args: ["onTextChange",] }],
    close_: [{ type: Output, args: ["close",] }],
    focus_: [{ type: Output, args: ["focus",] }],
    focusout_: [{ type: Output, args: ["focusout",] }],
    change_: [{ type: Output, args: ["change",] }],
    filter: [{ type: ViewChild, args: ["filter",] }],
    inputGroup: [{ type: ViewChild, args: ["inputGroup",] }],
    listitem: [{ type: ViewChild, args: ["listitem",] }],
    dropDownli: [{ type: ViewChild, args: ["dropDownli",] }],
    dropDownContainer: [{ type: ViewChild, args: ["dropDownContainer",] }],
    enabled: [{ type: Input }],
    editable: [{ type: Input }],
    shiftUp: [{ type: Input }],
    visible: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtComboBox.prototype.originalValue;
    /** @type {?} */
    SwtComboBox.prototype.onSpyChange;
    /** @type {?} */
    SwtComboBox.prototype.onSpyNoChange;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._isDropdownOpen;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selecedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._inputClick;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._open;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._close;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._focusout;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._onTextChange;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._tempData;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._originalData;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.filterState;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._alertvisiblity;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._outside;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._insideButton;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._required;
    /** @type {?} */
    SwtComboBox.prototype.dataLabel;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.toolTipPreviousObject;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.interrupted;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._ignored_validation;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.ignoredValidationArr;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._default_ignored;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.backGroundImage;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.onOpenSelectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._shiftUp;
    /** @type {?} */
    SwtComboBox.prototype.showDescriptionInDropDown;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._toolTipPreviousObject;
    /** @type {?} */
    SwtComboBox.prototype.prompt;
    /** @type {?} */
    SwtComboBox.prototype.width;
    /** @type {?} */
    SwtComboBox.prototype.height;
    /** @type {?} */
    SwtComboBox.prototype.id;
    /** @type {?} */
    SwtComboBox.prototype.firstCall;
    /** @type {?} */
    SwtComboBox.prototype.open_;
    /** @type {?} */
    SwtComboBox.prototype.inputClick_;
    /** @type {?} */
    SwtComboBox.prototype.onTextChange;
    /** @type {?} */
    SwtComboBox.prototype.close_;
    /** @type {?} */
    SwtComboBox.prototype.focus_;
    /** @type {?} */
    SwtComboBox.prototype.focusout_;
    /** @type {?} */
    SwtComboBox.prototype.change_;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.filter;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.inputGroup;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.listitem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.dropDownli;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.dropDownContainer;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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