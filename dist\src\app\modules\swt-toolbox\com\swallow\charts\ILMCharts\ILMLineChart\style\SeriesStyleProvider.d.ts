/**
 * This class contain all module events
 */
export declare class SeriesStyleProvider {
    private static _instance;
    static LINE: number;
    static LINE_SOLID: number;
    static LINE_DOTTED: number;
    static LINE_DASHED: number;
    static AREA: number;
    static AREA_SOLID: number;
    static AREA_DASHED: number;
    static AREA_DASHED45: number;
    static AREA_DASHED135: number;
    static AREA_DOTTED: number;
    static CONT_AREA_BLIZZARD_BLUE: string;
    static CONT_AREA_COTTON_CANDY: string;
    static DASHED_AREA_PERANO: string;
    static DASHED_AREA_SEA_PINK: string;
    static CONT_AREA_BLACK: string;
    static CONT_AREA_GREY: string;
    static CONT_AREA_LIGHT_GREY: string;
    static CONT_AREA_RED: string;
    static CONT_AREA_INDIAN_RED: string;
    static CONT_AREA_PINK: string;
    static CONT_AREA_ORANGE: string;
    static CONT_AREA_PEACH_PUFF: string;
    static CONT_AREA_YELLOW: string;
    static CONT_AREA_GREEN: string;
    static CONT_AREA_LIME: string;
    static CONT_AREA_LIGHT_GREEN: string;
    static CONT_AREA_BLUE: string;
    static CONT_AREA_STEEL_BLUE: string;
    static CONT_AREA_LIGHT_BLUE: string;
    static CONT_AREA_LIGHT_CYAN: string;
    static CONT_AREA_MAGENTA: string;
    static CONT_AREA_PURPLE: string;
    static CONT_AREA_VIOLET: string;
    static DASHED_CORAL_AREA: string;
    static DASHED_BLUE_AREA: string;
    static DASHED_AQUA_AREA: string;
    static DASHED_DEEP_PINK_AREA: string;
    static DASHED_GOLDEN_ROD_AREA: string;
    static DASHED_GREEN_AREA: string;
    static DOTTED_GREEN_YELLOW_AREA: string;
    static DOTTED_INDIAN_RED_AREA: string;
    static DOTTED_MAGENTA_AREA: string;
    static CONT_AREA_ANTIQUE_WHITE: string;
    static CONT_AREA_APRICOT_PEACH: string;
    static CONT_AREA_NAVAJO_WHITE: string;
    static CONT_AREA_ROSE_FOG: string;
    static CONT_AREA_DARK_SALMON: string;
    static CONT_SEGMENT_LYNCH: string;
    static DASHED_SEGMENT_SAN_MARINO: string;
    static CONT_SEGMENT_BLACK: string;
    static DASHED_SEGMENT_BLACK: string;
    static DOTTED_SEGMENT_BLACK: string;
    static CONT_SEGMENT_GREY: string;
    static DASHED_SEGMENT_GREY: string;
    static DOTTED_SEGMENT_GREY: string;
    static CONT_SEGMENT_RED: string;
    static DASHED_SEGMENT_RED: string;
    static DOTTED_SEGMENT_RED: string;
    static CONT_SEGMENT_BOLD_RED: string;
    static CONT_SEGMENT_YELLOW: string;
    static DASHED_SEGMENT_YELLOW: string;
    static DOTTED_SEGMENT_YELLOW: string;
    static CONT_SEGMENT_GREEN: string;
    static DASHED_SEGMENT_GREEN: string;
    static DOTTED_SEGMENT_GREEN: string;
    static CONT_SEGMENT_BLUE: string;
    static DASHED_SEGMENT_BLUE: string;
    static DOTTED_SEGMENT_BLUE: string;
    static CONT_SEGMENT_ORANGE: string;
    static DASHED_SEGMENT_ORANGE: string;
    static DOTTED_SEGMENT_ORANGE: string;
    static CONT_SEGMENT_MAGENTA: string;
    static DASHED_SEGMENT_MAGENTA: string;
    static DOTTED_SEGMENT_MAGENTA: string;
    static CONT_SEGMENT_PURPLE: string;
    static DASHED_SEGMENT_PURPLE: string;
    static DOTTED_SEGMENT_PURPLE: string;
    static SIMILAR_COLORS: string[][];
    static DASHED_PATTERN_REAL_COLOR: Object;
    allStyles: any[];
    /**
     * Get one instance of the SeriesStyleProvider class
     * */
    static readonly instance: SeriesStyleProvider;
    /**
     * Private constructor: initialize styles
     * */
    constructor();
    /**
         * Get the style name from its Id
         * */
    static getStyleName(styleId: string): string;
    static getStyleColor(styleId: string): string;
    static getStyleColorAsSring(styleId: string): string;
    static getStyleBorderColorAsSring(styleId: string): string;
    static hex2css(colorUint: number): string;
    static getStyleType(styleId: string): Number;
}
