/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild } from '@angular/core';
import { Container } from '../../../../../containers/swt-container.component';
import { CommonService } from '../../../../../utils/common.service';
import * as Highcharts from 'highcharts';
/** @type {?} */
let Boost = require('highcharts/modules/boost');
/** @type {?} */
let noData = require('highcharts/modules/no-data-to-display');
/** @type {?} */
let More = require('highcharts/highcharts-more');
/** @type {?} */
let patternFill = require('highcharts/modules/pattern-fill');
/** @type {?} */
const Exporting = require("highcharts/modules/exporting");
import HC_exportData from 'highcharts/modules/export-data';
import customWrap from './customWrap';
import { ExternalInterface } from '../../../../../utils/external-interface.service';
Exporting(Highcharts);
HC_exportData(Highcharts);
Boost(Highcharts);
noData(Highcharts);
More(Highcharts);
noData(Highcharts);
customWrap(Highcharts);
patternFill(Highcharts);
export class SwtILMChart extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.chart = null;
        this.mousePosition = [];
        this.invisibleLegend = [];
        this.tooltipValues = [];
        this.sourceOfLiquidityChecked = false;
        this.useCcyMulitplierChecked = false;
        this.isEntityTimeFrameChecked = false;
        this.showActualDatasetOnlyChecked = false;
        this.entityTimeDifference = 0;
        this.firstLoadDataZones = [];
        this.zoomFromTime = null;
        this.zoomToTime = null;
        this.inData = null;
        this.redrawFunctionIsEmpty = false;
        this.visibleLinesInChart = [];
        this.allThresholds = [];
        this.updateTempVariable = false;
        this.currencyFormat = '';
        this.currencyMutiplierValue = 1;
        this.currencyDecimalPlaces = 2;
        this.dateFormatAsString = "";
        this.saveHighligtedCharts = false;
        this.highlightedSeries = [];
        this.callerTabName = '';
        this.seriesMouseover = [];
        this.symbol = [];
        this.markers = [];
        this.series = [];
        //FIXME:
        //   (function (H) {
        //     Highcharts.Chart.prototype.callbacks.push(function (chart) {
        //         H.addEvent(chart.xAxis[0], 'afterSetExtremes', function (e) {
        //             window.parent.postMessage(['updateNowDate', [callerTabName]], "*");
        //         });
        //     });
        // }(Highcharts));
        //List of variables
        this.notChecked = false;
        this.isSODClicked = true;
        this.isThresholderClicked = true;
        this.hasPlotBand = false;
        this.hasCheckedCurrency = false;
        this.chartNotDrawn = true;
        this.chartNotDrawnNewdata = null;
        this.chartNotDrawntabName = null;
        this.listOfBandsIds = ['plot-band-0', 'plot-band-1', 'plot-band-2', 'plot-band-3', 'plot-line-1'];
        this.log10 = Math.log(10);
        this.lastExportType = null;
        this.each = Highcharts.each;
        this.pick = Highcharts.pick;
        //check if needed
        this.seriesTypes = Highcharts.seriesType;
        this.downloadAttrSupported = document.createElement('a').download !== undefined;
        // if (window.addEventListener) {
        //   window.addEventListener("message", this.receiver.bind(this), false);
        // } else {
        //   (<any>window).attachEvent("onmessage", this.receiver.bind(this));
        // }
    }
    /**
     * @param {?} methodName
     * @param {?} methodArguments
     * @return {?}
     */
    callMethodByName(methodName, methodArguments) {
        if (this[methodName]) {
            this[methodName].apply(this, methodArguments);
        }
    }
    /**
     * @param {?} newData
     * @param {?} mergeData
     * @param {?} updateTempVar
     * @param {?} tabName
     * @return {?}
     */
    setILMData(newData, mergeData, updateTempVar, tabName) {
        if (newData != "" && newData != null && newData.length > 0) {
            if (mergeData) {
                this.inData = this.inData.concat(JSON.parse(newData));
                this.mergeNewCharts(JSON.parse(newData));
            }
            else {
                if (!this.chartNotDrawn) {
                    this.firstLoad(newData, true, tabName);
                }
                else {
                    this.chartNotDrawnNewdata = newData;
                    this.chartNotDrawntabName = tabName;
                }
                //                     firstLoad(parent.getJSONData(window.frameElement.id), true);
                return;
            }
        }
        this.updateTempVariable = updateTempVar;
    }
    /**
     * @return {?}
     */
    forceDrawChartIfNotDrawn() {
        if (this.chartNotDrawn) {
            this.firstLoad(this.chartNotDrawnNewdata, true, this.chartNotDrawntabName);
            this.chartNotDrawn = false;
        }
    }
    /**
     * @param {?} imageName
     * @param {?} color
     * @return {?}
     */
    getFillPatternForImange(imageName, color) {
        /** @type {?} */
        let patternWidth = 4;
        /** @type {?} */
        let patternHeight = 4;
        /** @type {?} */
        let patternValue;
        if (imageName == "b.png" || imageName == "a.png" || imageName == "aqua_45.png" || imageName == "deeppink_45") {
            patternValue = 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11';
        }
        else if (imageName == "greenyellow.png" || imageName == "indianred.png" || imageName == "magenta.png") {
            patternValue = "M 2 5 L 5 2 L 8 5 L 5 8 Z";
        }
        else if (imageName == "goldenrod.png" || imageName == "green.png") {
            patternValue = 'M 0 3 L 10 3 M 0 8 L 10 8';
        }
        //   pattern = {
        //     path: {
        //         d: 'M 3 0 L 3 10 M 8 0 L 8 10',
        //         strokeWidth: 1
        //     },
        //     width: 4,
        //     height: 4,
        //     color: color,
        //     opacity: 0.8
        // }
        return {
            pattern: {
                path: {
                    d: patternValue,
                    strokeWidth: 1
                },
                width: patternWidth,
                height: patternHeight,
                color: color,
                opacity: 0.8
            }
        };
    }
    /**
     * @param {?} dataFromParent
     * @param {?} resetAll
     * @param {?} tabName
     * @return {?}
     */
    firstLoad(dataFromParent, resetAll, tabName) {
        this.invisibleLegend = [];
        this.tooltipValues = [];
        this.isSODClicked = false;
        this.sourceOfLiquidityChecked = false;
        this.useCcyMulitplierChecked = false;
        this.isEntityTimeFrameChecked = false;
        this.showActualDatasetOnlyChecked = false;
        this.entityTimeDifference = 0;
        this.firstLoadDataZones = [];
        this.zoomFromTime = null;
        this.zoomToTime = null;
        this.inData = null;
        this.visibleLinesInChart = [];
        this.allThresholds = [];
        this.callerTabName = tabName;
        if (dataFromParent) {
            if (dataFromParent[0])
                this.inData = JSON.parse(dataFromParent[0]);
            this.isSODClicked = dataFromParent[1];
            this.sourceOfLiquidityChecked = dataFromParent[2];
            this.firstLoadDataZones = dataFromParent[3];
            this.useCcyMulitplierChecked = dataFromParent[4];
            this.isEntityTimeFrameChecked = dataFromParent[5];
            this.zoomFromTime = dataFromParent[6];
            this.zoomToTime = dataFromParent[7];
            this.showActualDatasetOnlyChecked = dataFromParent[8];
            this.currencyFormat = dataFromParent[9];
            this.currencyMutiplierValue = dataFromParent[10];
            this.currencyDecimalPlaces = dataFromParent[11];
            this.dateFormatAsString = dataFromParent[12];
            this.saveHighligtedCharts = dataFromParent[13];
            this.highlightedSeries = dataFromParent[14].split(',');
            this.saveUncheckedItems = dataFromParent[15];
            this.uncheckedItemsFromParent = dataFromParent[16];
            this.updateTempVariable = false;
            // var chart = $("#container").highcharts();
            this.chart = Highcharts.chart(this.containerHighChart.nativeElement, {});
            if (this.chart) {
                // $('#container').unbind('click');
                $(this.containerHighChart.nativeElement).unbind('click');
                this.chart.destroy();
                this.chart = null;
            }
            this.createILMChart();
        }
        //         window.parent.postMessage({
        //             'func': 'parentFuncName',
        //             'message': 'Message text from iframe.'
        //         }, "*");
    }
    /**
     * @param {?} dataZonesJSON
     * @return {?}
     */
    setILMDataZones(dataZonesJSON) {
        this.dataZones = dataZonesJSON;
    }
    /**
     * @return {?}
     */
    destroyChart() {
        if (this.chart) {
            try {
                $(this.containerHighChart.nativeElement).unbind('click');
                this.chart.destroy();
                this.chart = null;
            }
            catch (error) {
            }
            // $('#container').unbind('click');
        }
    }
    /**
     * @return {?}
     */
    getData() {
        return [this.inData, this.dataZones, this.updateTempVariable];
    }
    /**
     * @param {?} newDataAsJSON
     * @return {?}
     */
    mergeNewCharts(newDataAsJSON) {
        // var chart = $("#container").highcharts();
        /** @type {?} */
        var serieNumber;
        /** @type {?} */
        var timestamp;
        /** @type {?} */
        var VALUE = 0;
        /** @type {?} */
        var type = '';
        /** @type {?} */
        var color = '';
        /** @type {?} */
        var yAxis = 1;
        /** @type {?} */
        var fillColor = {};
        /** @type {?} */
        var pointInterval = 0;
        /** @type {?} */
        var pointStart = 0;
        /** @type {?} */
        var dashStyle = '';
        /** @type {?} */
        var isClicked = false;
        /** @type {?} */
        var zIndex = 999;
        /** @type {?} */
        var isAreaDashed = '';
        for (serieNumber = 0; serieNumber < newDataAsJSON.length; serieNumber++) {
            if (newDataAsJSON[serieNumber].type == 'area') {
                yAxis = 0;
            }
            else {
                yAxis = 1;
            }
            if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            }
            else if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            }
            else {
                dashStyle = '';
            }
            if (newDataAsJSON[serieNumber].type == 'area' && newDataAsJSON[serieNumber].color.indexOf('png') != -1) {
                fillColor = this.getFillPatternForImange(newDataAsJSON[serieNumber].color, newDataAsJSON[serieNumber].borderColor);
                // {
                //   pattern: {
                //     path: {
                //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                //       strokeWidth: 1
                //     },
                //     width: 4,
                //     height: 4,
                //     color: newDataAsJSON[serieNumber].borderColor,
                //   }
                // }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';
            }
            if (newDataAsJSON[serieNumber].type == 'line') {
                this.series.push({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                    "zIndex": 2,
                    "lineWidth": 1.5
                });
                this.chart.addSeries({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                    "zIndex": 2
                });
            }
            else if (newDataAsJSON[serieNumber].type == 'area') {
                if (newDataAsJSON[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    this.series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                    this.chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                }
                else {
                    this.series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                    this.chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                }
            }
            else if (newDataAsJSON[serieNumber].type == 'Threshold') {
                if (newDataAsJSON[serieNumber].visibility == "true") {
                    this.visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name,
                        "tooltipText": newDataAsJSON[serieNumber].name,
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": newDataAsJSON[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                    });
                    this.visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name + '.Double',
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                this.allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name,
                    "tooltipText": newDataAsJSON[serieNumber].name,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": newDataAsJSON[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"
                    // "className": 'highcharts-color-1'
                });
                this.allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name + '.Double',
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"
                });
            }
            if (newDataAsJSON[serieNumber].visibility != 'true') {
                this.invisibleLegend.push(newDataAsJSON[serieNumber].name);
            }
        }
        this.disableAutoRedraw();
        if (!this.isSODClicked) {
            this.uncheckSODandalignScale();
        }
        /* if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
             showLiquiditySource(true, null);
         }*/
        this.updateVisibleThresholds();
        this.chart.yAxis[1].update({
            plotLines: this.visibleLinesInChart.slice()
        });
        this.enableAutoredrawAndRedrawChart();
        /*  if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
               showLiquiditySource(true, firstLoadDataZones);
           }
    
           enableAutoredrawAndRedrawChart();*/
    }
    /**
     * @return {?}
     */
    createILMChart() {
        this.series = [];
        /** @type {?} */
        const that = this;
        // $("#container").empty();
        /** @type {?} */
        var $tooltipBands = $('#tooltip_bands');
        $tooltipBands.hide();
        /** @type {?} */
        let $text = $('#tooltiptext');
        //FIXME:CHECK if works or needed
        /** @type {?} */
        const displayTooltip = (/**
         * @param {?} text
         * @param {?} left
         * @param {?} x
         * @param {?} y
         * @return {?}
         */
        function (text, left, x, y) {
            $text.text(text);
            $tooltipBands.show();
            $tooltipBands.css('left', parseInt(x) + 'px');
            $tooltipBands.css('top', parseInt(y) + 100 + 'px');
        });
        /** @type {?} */
        const hideTooltip = (/**
         * @return {?}
         */
        function () {
            clearTimeout(this.timer);
            this.timer = setTimeout((/**
             * @return {?}
             */
            function () {
                $tooltipBands.fadeOut();
            }), 1000);
        });
        /** @type {?} */
        var gfxPath = 'https://raw.githubusercontent.com/highcharts/pattern-fill/master/graphics/';
        /** @type {?} */
        let inData = this.getData()[0];
        /** @type {?} */
        let serieNumber;
        /** @type {?} */
        let timestamp;
        /** @type {?} */
        let VALUE = 0;
        /** @type {?} */
        let type = '';
        /** @type {?} */
        let color = '';
        /** @type {?} */
        let yAxis = 1;
        /** @type {?} */
        let fillColor = {};
        /** @type {?} */
        let pointInterval = 0;
        /** @type {?} */
        let pointStart = 0;
        /** @type {?} */
        let dashStyle = '';
        /** @type {?} */
        let isClicked = false;
        /** @type {?} */
        let zIndex = 999;
        /** @type {?} */
        let isAreaDashed = '';
        /** @type {?} */
        let data = [];
        // marker = []
        /** @type {?} */
        let marker = {
            symbol: 'circle',
            states: {
                hover: { enabled: false }
            }
        };
        this.visibleLinesInChart = [],
            this.allThresholds = [];
        for (serieNumber = 0; serieNumber < inData.length; serieNumber++) {
            if (inData[serieNumber].type == 'area') {
                yAxis = 0;
            }
            else {
                yAxis = 1;
            }
            if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            }
            else if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            }
            else {
                dashStyle = '';
            }
            if (inData[serieNumber].type == 'area' && inData[serieNumber].color.indexOf('png') != -1) {
                fillColor = this.getFillPatternForImange(inData[serieNumber].color, inData[serieNumber].borderColor);
                // {
                //   pattern: {
                //     path: {
                //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                //       strokeWidth: 1
                //     },
                //     width: 4,
                //     height: 4,
                //     color: inData[serieNumber].borderColor,
                //   }
                // }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';
            }
            /** @type {?} */
            let isVisibleItem = (inData[serieNumber].visibility == 'true' && this.uncheckedItemsFromParent.indexOf(inData[serieNumber].name) == -1);
            if (isVisibleItem) {
                inData[serieNumber].visibility = 'true';
            }
            else {
                inData[serieNumber].visibility = 'false';
            }
            if (inData[serieNumber].type == 'line') {
                this.series.push({
                    "name": inData[serieNumber].chartlegendDisplayName,
                    "tooltipName": inData[serieNumber].chartDisplayLabel,
                    "chartID": inData[serieNumber].chartDisplayName,
                    "data": inData[serieNumber].data.slice(),
                    "type": inData[serieNumber].type,
                    "valueId": inData[serieNumber].name,
                    "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                    "pointInterval": inData[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": inData[serieNumber].color,
                    "visible": (inData[serieNumber].visibility == 'true'),
                    "zIndex": 2,
                    "lineWidth": 1.5
                });
            }
            else if (inData[serieNumber].type == 'area') {
                if (inData[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    this.series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                }
                else {
                    this.series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !this.isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(inData[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(inData[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(inData[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });
                }
            }
            else if (inData[serieNumber].type == 'Threshold') {
                /** @type {?} */
                let groupName = inData[serieNumber].name.split('.')[0];
                /** @type {?} */
                let isVisibleItem = (inData[serieNumber].visibility == 'true' && this.uncheckedItemsFromParent.indexOf(groupName + ".Threshold") == -1);
                if (isVisibleItem) {
                    inData[serieNumber].visibility = 'true';
                }
                else {
                    inData[serieNumber].visibility = 'false';
                }
                if (inData[serieNumber].visibility == "true") {
                    this.visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name,
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": inData[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                    });
                    this.visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name + '.Double',
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                this.allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name,
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": inData[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"
                    // "className": 'highcharts-color-1'
                });
                this.allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name + '.Double',
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"
                });
            }
            if (inData[serieNumber].visibility != 'true') {
                this.invisibleLegend.push(inData[serieNumber].name);
            }
        }
        Highcharts.setOptions({
            // lang: {numericSymbols: ['k', 'M', 'B', 'T', 'P', 'E']},
            time: {
                useUTC: false
            }
        });
        /** @type {?} */
        var colorSerie1 = localStorage.getItem("colorSerie1");
        /** @type {?} */
        var markersSeries;
        this.options = {
            chart: {
                //animation: false,
                alignTicks: false,
                spacingLeft: 0,
                spacingRight: 0,
                //spacingBottom: 0,
                // marginTop: 150,
                events: {
                    load: (/**
                     * @return {?}
                     */
                    function () {
                        // window.parent.postMessage(['chartCreationCompleteHandler', [that.callerTabName]], "*");
                    }),
                },
            },
            title: {
                text: ''
            },
            legend: {
                enabled: false,
                layout: 'horizontal',
                align: 'center',
                verticalAlign: 'bottom',
                floating: true,
                symbolWidth: 30,
                backgroundColor: 'transparent',
                x: 0,
                y: 20
            },
            boost: {
                useGPUTranslations: true,
                usePreAllocated: true,
            },
            xAxis: [{
                    type: 'datetime',
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return Highcharts.dateFormat('%H:%M', this.value);
                        }),
                        style: {
                            fontSize: "10px"
                        },
                    },
                    lineWidth: 2,
                    gridLineWidth: 1,
                    ordinal: false,
                    startOnTick: false,
                    endOnTick: false,
                    minPadding: 0,
                    maxPadding: 0,
                    tickPixelInterval: 55,
                    /*tickInterval: 3600 * 1000,
                            maxTickInterval: 3600 * 1000,*/
                    //                         title: {
                    //                             useHTML: true,
                    //                             text: "Time" + " " + "<button id ='xAxisCurrency' class='button-link'>(Currency)</button>"
                    //                         }
                    title: {
                        useHTML: false,
                        text: ""
                    },
                }],
            yAxis: [{
                    // Primary yAxis
                    lineWidth: 3,
                    offset: 0,
                    tickWidth: 1,
                    title: {
                        text: 'Accumulated D/C',
                        margin: 0,
                        style: {
                            color: "black"
                        }
                    },
                    tickPixelInterval: 40,
                    opposite: true,
                    showEmpty: false,
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return that.rightVerticalAxisFormatter(this.value);
                        }),
                        style: {
                            fontSize: "10px",
                            color: "black"
                        },
                    },
                }, {
                    // Secondary yAxis
                    gridLineWidth: 0,
                    showEmpty: false,
                    lineWidth: 3,
                    offset: 0,
                    //I put offset here because ilm zones at 00:00 hide a part of this zone     
                    tickWidth: 1,
                    title: {
                        text: 'Balance',
                        margin: 0,
                        style: {
                            color: "black"
                        }
                    },
                    tickPixelInterval: 40,
                    plotLines: this.visibleLinesInChart.slice(),
                    labels: {
                        formatter: (/**
                         * @return {?}
                         */
                        function () {
                            return that.leftVerticalAxisFormatter(this.value);
                        }),
                        style: {
                            fontSize: "10px",
                            color: "black"
                        },
                    },
                }
            ],
            optimize: {
                enableTooltip: true
            },
            tooltip: {
                useHTML: true,
                borderWidth: 0,
                backgroundColor: "rgba(255,255,255,0)",
                borderRadius: 0,
                shadow: false,
                padding: 2,
                positioner: (/**
                 * @param {?} labelWidth
                 * @param {?} labelHeight
                 * @param {?} point
                 * @return {?}
                 */
                function (labelWidth, labelHeight, point) {
                    try {
                        /** @type {?} */
                        var tooltipX;
                        /** @type {?} */
                        var tooltipY;
                        that.mousePosition = [];
                        if (point.plotX + labelWidth > that.chart.plotWidth) {
                            tooltipX = point.plotX + that.chart.plotLeft - labelWidth - 20;
                        }
                        else {
                            tooltipX = point.plotX + that.chart.plotLeft + 20;
                        }
                        tooltipY = point.plotY + that.chart.plotTop - 20;
                        if (tooltipY + labelHeight > $(that.containerHighChart.nativeElement).height()) {
                            tooltipY = 5;
                        }
                        that.mousePosition.push({ "positionX": point.plotX, "positionY": point.plotY });
                        return {
                            x: tooltipX,
                            y: tooltipY + 50
                        };
                    }
                    catch (error) {
                        console.log("SwtILMChart -> createILMChart  positioner-> error", error);
                        that.mousePosition.push({ "positionX": 0, "positionY": 0 });
                        return {
                            x: 0,
                            y: 50
                        };
                    }
                }),
                formatter: (/**
                 * @return {?}
                 */
                function () {
                    try {
                        /** @type {?} */
                        var t = [];
                        /** @type {?} */
                        var values = [];
                        /** @type {?} */
                        var isDashed = '\u25CF';
                        /** @type {?} */
                        var yAxisNumber;
                        tooltipArrays = [];
                        /** @type {?} */
                        var xMax = that.chart.plotWidth;
                        /** @type {?} */
                        var DataTh = [];
                        /** @type {?} */
                        var formattedValue;
                        /** @type {?} */
                        var dateAsString;
                        /** @type {?} */
                        var tooltipArrays = [];
                        $.each(this.points, (/**
                         * @param {?} i
                         * @param {?} point
                         * @return {?}
                         */
                        function (i, point) {
                            if (point.series.userOptions.type == 'line' && that.invisibleLegend.indexOf(point.series.userOptions.valueId) == -1) {
                                tooltipArrays.push({ "y": point.series.yData[this.point.index], "name": point.series.userOptions.valueId });
                                if (dateAsString == null) {
                                    if (that.dateFormatAsString.toUpperCase() == "DD/MM/YYYY") {
                                        dateAsString = Highcharts.dateFormat('%d/%m/%Y %H:%M:%S', this.x);
                                    }
                                    else {
                                        dateAsString = Highcharts.dateFormat('%m/%d/%Y %H:%M:%S', this.x);
                                    }
                                }
                            }
                            if (this.point.series.userOptions.type == 'area') {
                                yAxisNumber = 0;
                                formattedValue = that.rightVerticalAxisFormatter(point.y);
                            }
                            else {
                                yAxisNumber = 1;
                                if (that.useCcyMulitplierChecked == true) {
                                    formattedValue = that.commonAxisFormatter(point.y, 3);
                                }
                                else {
                                    formattedValue = that.leftVerticalAxisFormatter(point.y);
                                }
                            }
                            if (point.series.userOptions.dashStyle == "Dash" || (point.series.userOptions.isAreaDashed == 'dashed')) {
                                //Dashed
                                isDashed = '\u25CD';
                            }
                            else if (point.series.userOptions.dashStyle == "shortdot") {
                                //Dotted  \u25CC
                                isDashed = '\u2687';
                            }
                            else {
                                isDashed = '\u25CF';
                            }
                            if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && point.point.plotX > that.mousePosition[0].positionX - 10 && point.point.plotX < that.mousePosition[0].positionX + 10 && point.point.plotY > that.mousePosition[0].positionY - 10 && point.point.plotY < that.mousePosition[0].positionY + 10) {
                                t.push('<span style="color:' + point.color + '">' + isDashed + '</span> ' + point.series.options.tooltipName + ' (' + Highcharts.dateFormat('%H:%M', this.x) + ';' +
                                    formattedValue + ')' + '<span>' + '</br>');
                            }
                        }));
                        //thresholders markers and tooltip
                        for (var i = 0; i < that.chart.yAxis[1].plotLinesAndBands.length; i = i + 2) {
                            DataTh = that.chart.yAxis[1].plotLinesAndBands[i].options.value;
                            /** @type {?} */
                            var space = (that.chart.yAxis[1].plotLinesAndBands[i].options.id == "plot-line-1") ? '<br/>' : '';
                            if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && that.chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && that.mousePosition[0].positionX > 0 && that.mousePosition[0].positionX < 10
                                && that.mousePosition[0].positionY + that.chart.plotTop - 10 <= that.chart.yAxis[1].toPixels(DataTh) && that.chart.yAxis[1].toPixels(DataTh) <= that.mousePosition[0].positionY + that.chart.plotTop + 10) {
                                t.push('<span class="circle" style="color:black"></span> ' + that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + space + '(' + that.toDate(that.chart.xAxis[0].toValue(that.mousePosition[0].positionX + that.chart.plotLeft)) + '; ' +
                                    that.leftVerticalAxisFormatter(DataTh) + ')' + '<span>' + '</br>');
                                if (that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] == null) {
                                    that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] = that.chart.renderer.label('\u29BF', that.chart.plotLeft - 5, that.chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                        fontSize: '9pt',
                                        color: 'black'
                                    }).attr({
                                        zIndex: 999,
                                    }).add();
                                }
                            }
                            else if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition) && that.chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && that.mousePosition[0].positionY + that.chart.plotTop <= that.chart.yAxis[1].toPixels(DataTh) && that.mousePosition[0].positionY + that.chart.plotTop >= that.chart.yAxis[1].toPixels(DataTh) - 10 && that.mousePosition[0].positionX > xMax - 20 && that.mousePosition[0].positionX <= xMax) {
                                t.push('<span class="circle" style="color: black"></span> ' + that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + space + '(' + that.toDate(that.chart.xAxis[0].toValue(that.mousePosition[0].positionX + that.chart.plotLeft)) + '; ' +
                                    that.leftVerticalAxisFormatter(DataTh) + ')' + '<span>' + '</br>');
                                if (that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] == null) {
                                    that.symbol[that.chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] = that.chart.renderer.label('\u29BF', xMax + that.chart.plotLeft - 10, that.chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                        fontSize: '9pt',
                                        color: 'black'
                                    }).attr({
                                        zIndex: 999,
                                    }).add();
                                }
                            }
                        }
                        for (var j in that.symbol) {
                            /** @type {?} */
                            var value = that.symbol[j];
                            if (!that.isEmpty(value) && value !== 'undefined' && value !== null) {
                                if (that.mousePosition !== undefined && !that.isEmpty(that.mousePosition)) {
                                    if (that.mousePosition[0].positionX + that.chart.plotLeft >= value.x + 15 || that.mousePosition[0].positionX + that.chart.plotLeft <= value.x - 5
                                        || that.mousePosition[0].positionY + that.chart.plotTop >= value.y + 15 || that.mousePosition[0].positionY + that.chart.plotTop <= value.y - 10) {
                                        value.destroy();
                                        delete that.symbol[j];
                                    }
                                }
                            }
                        }
                        //                         parent.updateChartsLiveValues(window.frameElement.id,tooltipArrays, tooltipArrays.length, dateAsString);
                        //                         window.parent.postMessage({
                        //                             'func': 'updateChartsLiveValues',
                        //                             'message': 'Message text from iframe.'
                        //                         }, "*");
                        window.parent.postMessage(['updateChartsLiveValues', [that.callerTabName, tooltipArrays, tooltipArrays.length, dateAsString]], "*");
                        return t;
                    }
                    catch (error) {
                        console.log("SwtILMChart -> createILMChart -> formatter error", error);
                    }
                }),
                shared: true
            },
            credits: {
                enabled: false
            },
            plotOptions: {
                series: {
                    animation: {
                        duration: 200
                    },
                    point: {
                        events: {
                            mouseOver: (/**
                             * @return {?}
                             */
                            function () {
                                that.seriesMouseover = this.series;
                            }),
                            mouseOut: (/**
                             * @return {?}
                             */
                            function () {
                                that.seriesMouseover = [];
                                this.series.chart.tooltip.hide();
                            })
                        }
                    },
                    states: {
                        hover: {
                            halo: {
                                size: 0
                            },
                            lineWidthPlus: 0,
                            marker: {
                                enabled: true,
                                radius: 2,
                                symbol: "circle"
                            },
                        },
                    },
                    marker: {
                        radius: 2,
                        symbol: "circle"
                    },
                    fillOpacity: 0.3
                },
            },
            exporting: {
                enabled: false
            },
            series: that.series,
        };
        this.chart = Highcharts.chart(this.containerHighChart.nativeElement, this.options);
        //updateClock();
        // $('#container').bind('click', function (e) {
        $(this.containerHighChart.nativeElement).bind('click', (/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            if (that.seriesMouseover.length != 0) {
                if (that.seriesMouseover["userOptions"].isClicked == false) {
                    if (that.seriesMouseover["userOptions"].type == 'line') {
                        that.seriesMouseover['update']({
                            lineWidth: 3
                        });
                    }
                    else if (that.seriesMouseover["userOptions"].type == 'area') {
                        if (!that.seriesMouseover) {
                            console.warn('seriesMouseover is undefined');
                            return;
                        }
                        try {
                            // Check if required properties exist
                            if (!that.seriesMouseover["userOptions"] || !that.seriesMouseover["area"] || !that.seriesMouseover["color"]) {
                                console.warn('Missing required properties in seriesMouseover');
                                return;
                            }
                            if (that.seriesMouseover["userOptions"].isAreaDashed === 'dashed') {
                                that.seriesMouseover["area"].attr("fill", that.seriesMouseover["color"]);
                            }
                            else {
                                /** @type {?} */
                                var rgbaCol = 'rgba(' +
                                    parseInt(that.seriesMouseover["color"].slice(-6, -4), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-4, -2), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-2), 16) + ',1)';
                                that.seriesMouseover["area"].attr("fill", rgbaCol);
                            }
                        }
                        catch (error) {
                            console.error('Error processing seriesMouseover:', error);
                        }
                    }
                    that.seriesMouseover["userOptions"].isClicked = true;
                    //                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover["userOptions"].valueId, true);
                    window.parent.postMessage(['highlightLegendFromHighChart', [that.callerTabName, that.seriesMouseover["userOptions"].valueId, true]], "*");
                }
                else if (that.seriesMouseover["userOptions"].isClicked == true) {
                    if (that.seriesMouseover["userOptions"].type == 'line') {
                        that.seriesMouseover['update']({
                            lineWidth: 1.5
                        });
                        // seriesMouseover.options.lineWidth = 0
                    }
                    else if (that.seriesMouseover["userOptions"].type == 'area') {
                        if (!that.seriesMouseover) {
                            console.warn('seriesMouseover is undefined');
                            return;
                        }
                        try {
                            // Check if required properties exist
                            if (!that.seriesMouseover["userOptions"] || !that.seriesMouseover["area"] || !that.seriesMouseover["color"]) {
                                console.warn('Missing required properties in seriesMouseover');
                                return;
                            }
                            if (that.seriesMouseover["userOptions"].isAreaDashed === 'dashed') {
                                if (!that.seriesMouseover["userOptions"].fillColor) {
                                    console.warn('fillColor is undefined');
                                    return;
                                }
                                that.seriesMouseover["area"].attr("fill", that.seriesMouseover["userOptions"].fillColor);
                            }
                            else {
                                /** @type {?} */
                                var rgbaCol = 'rgba(' +
                                    parseInt(that.seriesMouseover["color"].slice(-6, -4), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-4, -2), 16) + ',' +
                                    parseInt(that.seriesMouseover["color"].slice(-2), 16) + ',0.7)';
                                that.seriesMouseover["area"].attr("fill", rgbaCol);
                            }
                        }
                        catch (error) {
                            console.error('Error processing seriesMouseover:', error);
                        }
                    }
                    that.seriesMouseover["userOptions"].isClicked = false;
                    //                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover["userOptions"].valueId,false);
                    window.parent.postMessage(['highlightLegendFromHighChart', [that.callerTabName, that.seriesMouseover["userOptions"].valueId, false]], "*");
                }
            }
            $(window).focus();
        }));
        if (that.zoomFromTime && that.zoomToTime) {
            that.zoom(that.zoomFromTime, that.zoomToTime);
        }
        else {
            // Do nothing,no zooming is required
        }
        that.disableAutoRedraw();
        if (!that.isSODClicked) {
            that.uncheckSODandalignScale();
        }
        if (that.sourceOfLiquidityChecked && this.firstLoadDataZones) {
            that.showLiquiditySource(true, that.firstLoadDataZones);
        }
        if (that.saveHighligtedCharts) {
            if (that.highlightedSeries) {
                for (var i = 0; i < that.highlightedSeries.length; i++) {
                    //FIXME:TEST IT
                    that.highlightSerie(that.highlightedSeries[i], false);
                }
            }
        }
        that.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} dateAsDateStamp
     * @return {?}
     */
    updateClock(dateAsDateStamp) {
        if (dateAsDateStamp) {
            if (this.chart) {
                /** @type {?} */
                const now = new Date(dateAsDateStamp);
                // current date
                /** @type {?} */
                const time = this.addZero(now.getHours()) + ':' + this.addZero(now.getMinutes());
                /** @type {?} */
                const idLine = "nowPlotline";
                //var newDate = new Date(2008, 9, 1, now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds()).getTime();
                this.chart.xAxis[0].removePlotLine(idLine);
                this.chart.xAxis[0].addPlotLine({
                    color: 'red',
                    value: now,
                    width: 1.5,
                    zIndex: 20,
                    label: {
                        text: time,
                        style: {
                            color: 'red',
                            fontSize: "11px",
                            fontWeight: "Normal"
                        },
                        y: 10,
                        x: 10,
                        rotation: 0,
                        verticalAlign: 'top',
                        textAlign: 'left',
                    },
                    id: idLine
                });
            }
        }
    }
    /**
     * @param {?} showHide
     * @param {?} visibleYFields
     * @return {?}
     */
    showHideActualDataSet(showHide, visibleYFields) {
        this.disableAutoRedraw();
        if (showHide) {
            this.checkDataSet(visibleYFields);
        }
        else {
            this.unCheckDataSet(visibleYFields);
        }
        this.enableAutoredrawAndRedrawChart();
        if (!this.isSODClicked)
            //FIXME:
            this.alignScale(false);
    }
    /**
     * @param {?} showHide
     * @param {?} groupId
     * @return {?}
     */
    showHideThreshold(showHide, groupId) {
        if (!showHide) {
            this.hideThreshold(groupId);
        }
        else {
            this.showThreshold(groupId);
        }
    }
    /**
     * @param {?} alignScale
     * @return {?}
     */
    alignScaleWithSOD(alignScale) {
        this.disableAutoRedraw();
        if (!alignScale) {
            this.uncheckSODandalignScale();
        }
        else {
            this.applySODandUnalignScale();
        }
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} showHide
     * @param {?} valuesUpdated
     * @param {?} dataZonesJSON
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    showHideSourcesOfLiquidity(showHide, valuesUpdated, dataZonesJSON, visibleItemsInTree) {
        this.disableAutoRedraw();
        if (showHide) {
            this.showLiquiditySource(valuesUpdated, dataZonesJSON);
        }
        else {
            this.hideLiquiditySource(visibleItemsInTree);
        }
        this.enableAutoredrawAndRedrawChart();
    }
    ///List of functions
    //Hide thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    hideThreshold(groupName) {
        /** @type {?} */
        var chart = $('#container').highcharts();
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.chart.yAxis[1].removePlotLine(this.allThresholds[i].id);
                this.allThresholds[i].visible = false;
            }
        }
        this.updateVisibleThresholds();
    }
    /**
     * @param {?} groupName
     * @return {?}
     */
    removeThreshold(groupName) {
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.chart.yAxis[1].removePlotLine(this.allThresholds[i].id);
                this.allThresholds.splice(i, 1);
                i--;
            }
        }
        this.updateVisibleThresholds();
    }
    //Show Thresholdes
    /**
     * @param {?} groupName
     * @return {?}
     */
    showThreshold(groupName) {
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].group == groupName) {
                this.allThresholds[i].visible = true;
            }
        }
        this.updateVisibleThresholds();
        this.chart.yAxis[1].update({
            plotLines: this.visibleLinesInChart.slice()
        });
    }
    //DataSetOnly
    /**
     * @param {?} visibleYFields
     * @return {?}
     */
    checkDataSet(visibleYFields) {
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) == -1) {
                if (this.chart.series[i].userOptions.chartID == 'afc' || this.chart.series[i].userOptions.chartID == 'afd' || this.chart.series[i].userOptions.chartID == 'fbb' || this.chart.series[i].userOptions.chartID == 'fbia') {
                    this.chart.series[i].hide();
                    this.unHighlightSerieFunction(this.chart.series[i]);
                    this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                }
            }
        }
        this.showActualDatasetOnlyChecked = true;
    }
    /**
     * @param {?} visibleYFields
     * @return {?}
     */
    unCheckDataSet(visibleYFields) {
        if (this.invisibleLegend.length > 0 && visibleYFields != null && visibleYFields.length > 0) {
            for (var i = 0; i < this.chart.series.length; i++) {
                if (this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) != -1) {
                    //  afc  afd fbb fbia
                    //  if(!sourceOfLiquidityChecked) {
                    if (visibleYFields.indexOf(this.chart.series[i].userOptions.valueId) > -1) {
                        this.chart.series[i].show();
                        for (var j = 0; j < this.invisibleLegend.length; j++) {
                            if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                                this.invisibleLegend.splice(j, 1);
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (this.sourceOfLiquidityChecked)
            this.showLiquiditySource(true, null);
        this.showActualDatasetOnlyChecked = false;
    }
    /**
     * @return {?}
     */
    enableAutoredrawAndRedrawChart() {
        /** @type {?} */
        var axesChanged = false;
        this.chart.redraw = this._redraw;
        this.chart.redraw();
        this.redrawFunctionIsEmpty = false;
        if (this.chart.yAxis[1].min == undefined && this.chart.yAxis[1].max == undefined) {
            this.chart.yAxis[1].update({
                offset: 10
            });
        }
        else {
            this.chart.yAxis[1].update({
                offset: 2
            });
        }
        if (this.chart.yAxis[0].min == undefined && this.chart.yAxis[0].max == undefined) {
            this.chart.yAxis[0].update({
                offset: 10
            });
        }
        else {
            this.chart.yAxis[0].update({
                offset: 2
            });
        }
        if (this.isSODClicked) {
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        this.adjutMinMax();
    }
    /**
     * @return {?}
     */
    adjutMinMax() {
        if (this.chart.yAxis[0].min < 0 && this.chart.yAxis[0].max <= 0) {
            this.chart.yAxis[0].update({
                max: this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[0].min >= 0 && this.chart.yAxis[0].max > 0) {
            this.chart.yAxis[0].update({
                min: 0 - this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[1].min < 0 && this.chart.yAxis[1].max <= 0) {
            this.chart.yAxis[1].update({
                max: this.chart.yAxis[1].tickInterval
            });
        }
        if (this.chart.yAxis[1].min >= 0 && this.chart.yAxis[1].max > 0) {
            this.chart.yAxis[1].update({
                min: 0 - this.chart.yAxis[1].tickInterval
            });
        }
    }
    /**
     * @return {?}
     */
    enableAutoredrawOnly() {
        this.chart.redraw = this._redraw;
        this.redrawFunctionIsEmpty = false;
    }
    /**
     * @return {?}
     */
    disableAutoRedraw() {
        this._redraw = this.chart.redraw;
        this.chart.redraw = (/**
         * @return {?}
         */
        function () {
        });
        this.redrawFunctionIsEmpty = true;
    }
    /**
     * @return {?}
     */
    calculateStatistics() {
        /** @type {?} */
        var maxAxe1 = 0;
        /** @type {?} */
        var minAxe1 = 0;
        /** @type {?} */
        var maxAxe2 = 0;
        /** @type {?} */
        var minAxe2 = 0;
        /** @type {?} */
        var isLine = true;
        /** @type {?} */
        var pointYInChart = 0;
        /** @type {?} */
        var firstIndex = -1;
        /** @type {?} */
        var lastIndex = -1;
        //series.userOptions.type == 'line'
        /** @type {?} */
        const that = this;
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.chart.series[i].visible) {
                this.chart.series[i].data.forEach((/**
                 * @param {?} point
                 * @return {?}
                 */
                function (point) {
                    // Add to sum if within x-axis visible range
                    if (point.x >= that.chart.xAxis[0].min && point.x <= that.chart.xAxis[0].max) {
                        if (firstIndex == -1) {
                            firstIndex = point.index;
                        }
                        lastIndex = point.index;
                    }
                }));
                if (firstIndex != -1 && lastIndex != -1)
                    break;
            }
        }
        for (var i = 0; i < this.chart.series.length; i++) {
            pointYInChart = 0;
            if (this.chart.series[i].userOptions.type == 'line') {
                isLine = true;
            }
            else {
                isLine = false;
            }
            if (this.chart.series[i].visible) {
                for (var h = firstIndex; h < lastIndex; h++) {
                    // chart.series[i].yData[this.point.index]
                    pointYInChart = this.chart.series[i].yData[h];
                    if (isLine) {
                        if (pointYInChart > maxAxe1) {
                            maxAxe1 = pointYInChart;
                        }
                        if (pointYInChart < minAxe1) {
                            minAxe1 = pointYInChart;
                        }
                    }
                    else {
                        if (pointYInChart > maxAxe2) {
                            maxAxe2 = pointYInChart;
                        }
                        if (pointYInChart < minAxe2) {
                            minAxe2 = pointYInChart;
                        }
                    }
                }
            }
        }
        return [Math.max(Math.abs(maxAxe1), Math.abs(minAxe1)), Math.max(Math.abs(maxAxe2), Math.abs(minAxe2))];
    }
    /**
     * @param {?} forceCalculation
     * @return {?}
     */
    alignScale(forceCalculation) {
        /** @type {?} */
        var calculatedExtremes = this.calculateStatistics();
        if (calculatedExtremes[0] == 0 || calculatedExtremes[1] == 0 && !forceCalculation) {
            /** @type {?} */
            var maxValueForAxis0;
            /** @type {?} */
            var maxValueForAxis1;
            try {
                this.chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                this.chart.yAxis[1].update({
                    min: null,
                    max: null
                });
                if (this.redrawFunctionIsEmpty) {
                    this.enableAutoredrawAndRedrawChart();
                }
                /** @type {?} */
                var max0 = this.chart.yAxis[0].getExtremes().max;
                /** @type {?} */
                var min0 = this.chart.yAxis[0].getExtremes().min;
                /** @type {?} */
                var max1 = this.chart.yAxis[1].getExtremes().max;
                /** @type {?} */
                var min1 = this.chart.yAxis[1].getExtremes().min;
                maxValueForAxis0 = Math.max(Math.abs(max0), Math.abs(min0));
                maxValueForAxis1 = Math.max(Math.abs(max1), Math.abs(min1));
                if (maxValueForAxis0) {
                    this.chart.yAxis[0].update({
                        min: -maxValueForAxis0 - (maxValueForAxis0 / 100),
                        max: maxValueForAxis0 + (maxValueForAxis0 / 100)
                    });
                }
                if (maxValueForAxis1) {
                    this.chart.yAxis[1].update({
                        min: -maxValueForAxis1 - (maxValueForAxis1 / 100),
                        max: maxValueForAxis1 + (maxValueForAxis1 / 100)
                    });
                }
                if (this.redrawFunctionIsEmpty) {
                    this.disableAutoRedraw();
                }
            }
            catch (err) {
                console.log(err.message);
                console.log(err.stack);
            }
        }
        else {
            if (calculatedExtremes[0]) {
                this.chart.yAxis[1].update({
                    min: -calculatedExtremes[0] - (calculatedExtremes[0] / 100),
                    max: calculatedExtremes[0] + (calculatedExtremes[0] / 100)
                });
            }
            if (calculatedExtremes[1]) {
                this.chart.yAxis[0].update({
                    min: -calculatedExtremes[1] - (calculatedExtremes[1] / 100),
                    max: calculatedExtremes[1] + (calculatedExtremes[1] / 100)
                });
            }
        }
    }
    //SOD
    /**
     * @return {?}
     */
    applySODandUnalignScale() {
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var k = 0;
        /** @type {?} */
        var l = 1;
        /** @type {?} */
        var maxValueForAxis0;
        /** @type {?} */
        var maxValueForAxis1;
        try {
            for (var i = 0; i < this.inData.length; i++) {
                if (this.inData[i].type !== 'Threshold') {
                    this.chart.series[j].setData(this.inData[i].data.slice());
                    j++;
                }
                else {
                    if (this.allThresholds && this.allThresholds.length > 0) {
                        this.allThresholds[k].value = this.inData[i].data.slice();
                        this.allThresholds[l].value = this.allThresholds[k].value; //duplicate the value for the dashed balck threshold
                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            this.isSODClicked = true;
            if (this.sourceOfLiquidityChecked)
                this.showLiquiditySource(true, null);
            else {
                //update all thresholds first , then create function to clean thresholds
                this.updateVisibleThresholds();
            }
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    }
    /*
      function to update the list of visible thresholds from all passed thresholds
      */
    /**
     * @return {?}
     */
    updateVisibleThresholds() {
        this.visibleLinesInChart = [];
        for (var i = 0; i < this.allThresholds.length; i++) {
            if (this.allThresholds[i].visible == true) {
                this.visibleLinesInChart.push(this.allThresholds[i]);
            }
        }
        if (this.sourceOfLiquidityChecked == true && this.dataZones) {
            this.visibleLinesInChart.push({
                value: this.dataZones[4].data[0],
                color: this.dataZones[4].color,
                fillColor: this.dataZones[4].color,
                width: 3,
                id: 'plot-line-1',
                zIndex: 20,
                tooltipText: this.dataZones[4].name,
            });
        }
    }
    /**
     * @return {?}
     */
    uncheckSODandalignScale() {
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var k = 0;
        /** @type {?} */
        var l = 1;
        /** @type {?} */
        var maxValueForAxis0;
        /** @type {?} */
        var maxValueForAxis1;
        try {
            for (var i = 0; i < this.inData.length; i++) {
                if (this.inData[i].type !== 'Threshold') {
                    this.chart.series[j].setData(this.inData[i].dataSOD.slice());
                    j++;
                }
                else {
                    if (this.allThresholds && this.allThresholds.length > 0) {
                        this.allThresholds[k].value = this.inData[i].dataSOD.slice();
                        this.allThresholds[l].value = this.allThresholds[k].value; //duplicate the value for the dashed balck threshold
                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            this.isSODClicked = false;
            if (this.sourceOfLiquidityChecked)
                this.showLiquiditySource(true, null);
            else
                this.updateVisibleThresholds();
            this.alignScale(false);
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    }
    //timeStamp to hh:mm
    /**
     * @param {?} timestamp
     * @return {?}
     */
    toDate(timestamp) {
        /** @type {?} */
        var a = new Date((timestamp));
        /** @type {?} */
        var hour = this.addZero(a.getHours());
        /** @type {?} */
        var min = this.addZero(a.getMinutes());
        /** @type {?} */
        var dateFormat = hour + ':' + min;
        return dateFormat;
    }
    ;
    /**
     * @param {?} valuesUpdated
     * @param {?} dataZonesAsJSONString
     * @return {?}
     */
    showLiquiditySource(valuesUpdated, dataZonesAsJSONString) {
        if (this.sourceOfLiquidityChecked && valuesUpdated == false) {
            return;
        }
        //  invisbleChartsBySourceOfLiquidity = [];
        try {
            /** @type {?} */
            var dataZonesJSON = null;
            /** @type {?} */
            var createNewDataZones = false;
            for (var i = 0; i < this.chart.series.length; i++) {
                if (this.chart.series[i].type == 'area' && this.invisibleLegend.indexOf(this.chart.series[i].userOptions.valueId) == -1) {
                    this.chart.series[i].hide();
                    this.unHighlightSerieFunction(this.chart.series[i]);
                    this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                }
            }
            this.chart.yAxis[0].update({
                labels: {
                    enabled: false
                },
                title: {
                    text: null
                }
            });
            if (dataZonesAsJSONString) {
                dataZonesJSON = JSON.parse(dataZonesAsJSONString);
                this.dataZones = dataZonesJSON;
            }
            if (this.dataZones != null) {
                this.chart.yAxis[1].removePlotBand('plot-band-0');
                this.chart.yAxis[1].removePlotBand('plot-band-1');
                this.chart.yAxis[1].removePlotBand('plot-band-2');
                this.chart.yAxis[1].removePlotBand('plot-band-3');
                this.chart.yAxis[1].removePlotLine('plot-line-1');
                for (i = 0; i < this.dataZones.length; i++) {
                    if (!this.isSODClicked) {
                        this.dataZones[i].data = this.dataZones[i].dataSOD;
                    }
                    else {
                        this.dataZones[i].data = this.dataZones[i].dataNoSOD;
                    }
                    if (i < this.dataZones.length - 1) {
                        this.chart.yAxis[1].addPlotBand({
                            "tooltipText": this.dataZones[i].name,
                            "from": this.dataZones[i].data[0],
                            "to": this.dataZones[i].data[1],
                            "color": this.dataZones[i].color,
                            "zIndex": 2,
                            "id": 'plot-band-' + i,
                        });
                    }
                }
            }
            // Reset Extreme points when removing the right axis and SOD is checked
            if (!this.isSODClicked) {
                this.alignScale(false);
            }
            else {
                this.chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                this.chart.yAxis[1].update({
                    min: null,
                    max: null
                });
            }
            this.sourceOfLiquidityChecked = true;
            this.updateVisibleThresholds();
            this.chart.yAxis[1].update({
                plotLines: this.visibleLinesInChart.slice()
            });
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
            this.chart.yAxis[1].removePlotBand('plot-band-0');
            this.chart.yAxis[1].removePlotBand('plot-band-1');
            this.chart.yAxis[1].removePlotBand('plot-band-2');
            this.chart.yAxis[1].removePlotBand('plot-band-3');
            this.chart.yAxis[1].removePlotLine('plot-line-1');
        }
    }
    /**
     * @param {?} band
     * @param {?} hideOrshow
     * @return {?}
     */
    hideOrShowBandOrLine(band, hideOrshow) {
        if (hideOrshow) {
            band.hidden = false;
            band.svgElem.show();
        }
        else {
            band.hidden = true;
            band.svgElem.hide();
        }
    }
    /**
     * @param {?} visibleItemsInTree
     * @return {?}
     */
    hideLiquiditySource(visibleItemsInTree) {
        this.chart.yAxis[1].removePlotBand('plot-band-0');
        this.chart.yAxis[1].removePlotBand('plot-band-1');
        this.chart.yAxis[1].removePlotBand('plot-band-2');
        this.chart.yAxis[1].removePlotBand('plot-band-3');
        this.chart.yAxis[1].removePlotLine('plot-line-1');
        //  if (chart.series[i].userOptions.chartID != 'ab' && chart.series[i].userOptions.chartID != 'aac' && chart.series[i].userOptions.chartID != 'aad') {
        if (this.invisibleLegend.length > 0 && visibleItemsInTree != null && visibleItemsInTree.length > 0) {
            for (var i = 0; i < this.chart.series.length; i++) {
                if (visibleItemsInTree.indexOf(this.chart.series[i].userOptions.valueId) != -1) {
                    this.chart.series[i].show();
                    for (var j = 0; j < this.invisibleLegend.length; j++) {
                        if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                            this.invisibleLegend.splice(j, 1);
                            j--;
                            //break;
                        }
                    }
                }
            }
        }
        this.chart.yAxis[0].update({
            labels: {
                enabled: true
            },
            title: {
                text: 'Accumulated D/C'
            }
        });
        this.sourceOfLiquidityChecked = false;
        if (!this.isSODClicked)
            this.alignScale(false);
    }
    /**
     * @param {?} plotBandId
     * @return {?}
     */
    getPlotBandById(plotBandId) {
        for (var i = 0; i < this.chart.yAxis[1].plotLinesAndBands.length; i++) {
            if (this.chart.yAxis[1].plotLinesAndBands[i].id === plotBandId) {
                return this.chart.yAxis[1].plotLinesAndBands[i];
            }
        }
    }
    /**
     * @return {?}
     */
    checkMultiplierCurrenyMultiplier() {
        /** @type {?} */
        const that = this;
        try {
            that.useCcyMulitplierChecked = true;
            that.chart.yAxis[0].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        //return this.axis.defaultLabelFormatter.call(this);
                        return that.rightVerticalAxisFormatter(this.value);
                    })
                }
            });
            that.chart.yAxis[1].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that.leftVerticalAxisFormatter(this.value);
                    })
                }
            });
        }
        catch (error) {
        }
        // that.chart.redraw();
    }
    /**
     * @return {?}
     */
    uncheckMultiplierCurrenyMultiplier() {
        try {
            /** @type {?} */
            const that = this;
            that.useCcyMulitplierChecked = false;
            that.chart.yAxis[0].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that.rightVerticalAxisFormatter(this.value);
                    })
                }
            });
            that.chart.yAxis[1].update({
                labels: {
                    formatter: (/**
                     * @return {?}
                     */
                    function () {
                        return that.leftVerticalAxisFormatter(this.value);
                    })
                }
            });
            // that.chart.redraw();
        }
        catch (error) {
        }
    }
    /**
     * Returns the decimal places for the most significant digit for the smallet label in the axis
     * @param {?} smallestLabel
     * @return {?}
     */
    smallestLabelSigDigitDcPlaces(smallestLabel) {
        /** @type {?} */
        var decSeparator = this.currencyFormat == 'currencyPat1' ? "." : ",";
        if (smallestLabel == 0 && smallestLabel == NaN)
            smallestLabel = 0;
        /** @type {?} */
        let smallestSigDigits = this.getFirstSignificantDigit(smallestLabel, 3, false, (this.currencyFormat == 'currencyPat2'));
        if (smallestSigDigits != 0) {
            /** @type {?} */
            const smDig = "" + smallestSigDigits;
            return smDig.indexOf(decSeparator) != -1 ? (smDig.length - 2) : 0;
        }
        return 0;
    }
    /**
     * @param {?} n
     * @return {?}
     */
    getSignificantDigitCount(n) {
        n = String(n).replace(".", "");
        n = Math.abs(n); //remove decimal and make positive
        if (n == 0)
            return 0;
        while (n != 0 && n % 10 == 0)
            n /= 10; //kill the 0s at the end of n
        return Math.floor(Math.log(n) / this.log10) + 1; //get number of digits
    }
    /**
     * Right vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    rightVerticalAxisFormatter(labelValue) {
        /** @type {?} */
        const tickInterval = this.chart && this.chart.yAxis && this.chart.yAxis[0] ? this.chart.yAxis[0].tickInterval : 1;
        /** @type {?} */
        const sigDigitDcPlaces = labelValue < 1 ? this.getSignificantDigitCount(tickInterval) : 1;
        // var sigDigitDcPlaces = this.smallestLabelSigDigitDcPlaces((tickInterval) / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1));
        // if(labelValue < 1){
        // }
        return this.commonAxisFormatter(labelValue, sigDigitDcPlaces);
    }
    /**
     * Left vertical axis formatter
     * @param {?} labelValue
     * @return {?}
     */
    leftVerticalAxisFormatter(labelValue) {
        // var sigDigitDcPlaces = this.smallestLabelSigDigitDcPlaces((this.chart.yAxis[1].tickInterval) / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1));
        return this.commonAxisFormatter(labelValue, 2);
    }
    /**
     * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} labelValue
     * @param {?} sigDigitDcPlaces
     * @return {?}
     */
    commonAxisFormatter(labelValue, sigDigitDcPlaces) {
        /** @type {?} */
        var formattedAmount = "";
        // Apply the currency multiplier
        labelValue = labelValue / (this.useCcyMulitplierChecked ? this.currencyMutiplierValue : 1);
        if (Math.abs(labelValue) >= 1) {
            if (this.useCcyMulitplierChecked && this.currencyMutiplierValue != 1) {
                if (this.currencyFormat == 'currencyPat2')
                    formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.currencyDecimalPlaces : sigDigitDcPlaces, ",", ".");
                else {
                    formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.currencyDecimalPlaces : sigDigitDcPlaces, ".", ",");
                }
            }
            else {
                if (this.currencyFormat == 'currencyPat2')
                    formattedAmount = this.formatMoney(labelValue, 0, ",", ".");
                else {
                    formattedAmount = this.formatMoney(labelValue, 0, ".", ",");
                }
            }
            // format the amount, if sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            this.currencyFormat = this.currencyFormat;
        }
        else {
            // Format the amount based on the most significant number,eg: 0.00014 ==> is formatted into 0.0001.
            // If sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            sigDigitDcPlaces = sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0 ? 3 : sigDigitDcPlaces;
            formattedAmount = "" + this.getFirstSignificantDigit(labelValue, sigDigitDcPlaces, true, (this.currencyFormat == 'currencyPat2'));
        }
        return formattedAmount;
    }
    /**
     * @param {?} n
     * @param {?} c
     * @param {?} d
     * @param {?} t
     * @return {?}
     */
    formatMoney(n, c, d, t) {
        /** @type {?} */
        var c = isNaN(c = Math.abs(c)) ? 2 : c;
        /** @type {?} */
        var d = d == undefined ? "." : d;
        /** @type {?} */
        var t = t == undefined ? "," : t;
        /** @type {?} */
        var s = n < 0 ? "-" : "";
        /** @type {?} */
        var i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c)));
        /** @type {?} */
        var j = (j = i.length) > 3 ? j % 3 : 0;
        return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - parseInt(i)).toFixed(c).slice(2) : "");
    }
    ;
    /**
     * This function allow getting the significant digit after the decimal
     * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     * @param {?} number
     * @param {?} maxDecimals
     * @param {?} forceDecimals
     * @param {?} siStyle
     * @return {?}
     */
    getFirstSignificantDigit(number, maxDecimals, forceDecimals, siStyle) {
        try {
            maxDecimals = typeof maxDecimals !== 'undefined' ? maxDecimals : 2;
            forceDecimals = typeof forceDecimals !== 'undefined' ? forceDecimals : false;
            siStyle = typeof siStyle !== 'undefined' ? siStyle : true;
            /** @type {?} */
            var i = 0;
            /** @type {?} */
            var inc = Math.pow(10, maxDecimals);
            /** @type {?} */
            var str = String(Math.round(inc * number) / inc);
            /** @type {?} */
            var sep;
            if (str != "0") {
                /** @type {?} */
                var hasSep = str.indexOf(".") == -1;
                /** @type {?} */
                var sep = hasSep ? str.length : str.indexOf(".");
                /** @type {?} */
                var ret = (hasSep && !forceDecimals ? "" : (siStyle ? "," : ".")) + str.substr(sep + 1);
                if (forceDecimals) {
                    for (var j = 0; j <= maxDecimals - (str.length - (hasSep ? sep - 1 : sep)); j++)
                        ret += "0";
                }
                while (i + 3 < (str.substr(0, 1) == "-" ? sep - 1 : sep))
                    ret = (siStyle ? "." : ",") + str.substr(sep - (i += 3), 3) + ret;
                return str.substr(0, sep - i) + ret;
            }
            else {
                return 0;
            }
        }
        catch (err) {
            console.log(err.message);
            console.log(err.stack);
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    firstSignificant(value) {
        return Math.ceil(-Math.log10(value));
    }
    /**
     * @param {?} ccyMuliplierSelected
     * @return {?}
     */
    ccyMultiplierEventHandler(ccyMuliplierSelected) {
        this.disableAutoRedraw();
        if (ccyMuliplierSelected == true) {
            this.checkMultiplierCurrenyMultiplier();
        }
        else {
            this.uncheckMultiplierCurrenyMultiplier();
        }
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} serieName
     * @param {?} forcedValueState
     * @return {?}
     */
    highlightSerie(serieName, forcedValueState) {
        for (var i = 0; i < this.chart.series.length; i++) {
            if (this.chart.series[i].userOptions.valueId == serieName) {
                if (forcedValueState) {
                    if (forcedValueState == false) {
                        this.unHighlightSerieFunction(this.chart.series[i]);
                    }
                    else if (forcedValueState == true) {
                        this.highlightSerieFunction(this.chart.series[i]);
                    }
                }
                else {
                    if (this.chart.series[i].userOptions.isClicked == false) {
                        this.highlightSerieFunction(this.chart.series[i]);
                    }
                    else if (this.chart.series[i].userOptions.isClicked == true) {
                        this.unHighlightSerieFunction(this.chart.series[i]);
                    }
                }
            }
        }
        this.chart.redraw();
    }
    /**
     * @param {?} serie
     * @return {?}
     */
    highlightSerieFunction(serie) {
        // First check if serie and required properties exist
        if (!serie || !serie.options) {
            console.warn('Invalid series object passed to highlightSerieFunction');
            return;
        }
        try {
            if (serie.options.type === 'line') {
                serie.update({
                    lineWidth: 3
                });
            }
            else if (serie.options.type === 'area') {
                // Check if serie and basic required properties exist
                if (!serie || !serie.options) {
                    console.warn('Invalid series object passed');
                    return;
                }
                try {
                    // Check if area property exists before accessing
                    if (!serie.area) {
                        console.warn('Area property is undefined for series');
                        return;
                    }
                    // Verify color exists before parsing
                    if (!serie.options.color) {
                        console.warn('Color property is undefined for series');
                        return;
                    }
                    if (serie.options.isAreaDashed === 'dashed') {
                        serie.area.attr("fill", serie.options.fillColor || serie.options.color); // Fallback to color if fillColor is undefined
                    }
                    else {
                        /** @type {?} */
                        let rgbaCol;
                        try {
                            rgbaCol = 'rgba(' +
                                parseInt(serie.options.color.slice(-6, -4), 16) + ',' +
                                parseInt(serie.options.color.slice(-4, -2), 16) + ',' +
                                parseInt(serie.options.color.slice(-2), 16) + ',1)';
                        }
                        catch (colorError) {
                            console.warn('Error parsing color value:', colorError);
                            return;
                        }
                        serie.area.attr("fill", rgbaCol);
                    }
                }
                catch (error) {
                    console.error('Error processing series:', error);
                }
            }
            if (serie.userOptions) {
                serie.userOptions.isClicked = true;
            }
            else {
                console.warn('userOptions is undefined for series');
            }
        }
        catch (error) {
            console.error('Error in highlightSerieFunction:', error);
        }
    }
    /**
     * @param {?} serie
     * @return {?}
     */
    unHighlightSerieFunction(serie) {
        // First check if serie exists
        if (!serie || !serie.options) {
            console.warn('Invalid series object passed to unHighlightSerieFunction');
            return;
        }
        try {
            if (serie.options.type === 'line') {
                serie.update({ lineWidth: 1.5 });
            }
            else if (serie.options.type === 'area') {
                // Check if area property exists before accessing
                if (!serie.area) {
                    console.warn('Area property is undefined for series');
                    return;
                }
                if (serie.options.isAreaDashed === 'dashed') {
                    serie.area.attr("fill", serie.options.fillColor);
                }
                else {
                    // Verify color exists before parsing
                    if (!serie.options.color) {
                        console.warn('Color property is undefined for series');
                        return;
                    }
                    /** @type {?} */
                    var rgbaCol = 'rgba(' +
                        parseInt(serie.options.color.slice(-6, -4), 16) + ',' +
                        parseInt(serie.options.color.slice(-4, -2), 16) + ',' +
                        parseInt(serie.options.color.slice(-2), 16) + ',0.7)';
                    serie.area.attr("fill", rgbaCol);
                }
            }
            if (serie.userOptions) {
                serie.userOptions.isClicked = false;
            }
        }
        catch (error) {
            console.error('Error in unHighlightSerieFunction:', error);
        }
    }
    /**
     * @param {?} serieName
     * @param {?} showHide
     * @return {?}
     */
    showSerie(serieName, showHide) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var lineAxeIsInvisible = false;
        /** @type {?} */
        var areaAxeIsInvisible = false;
        /** @type {?} */
        var redrawAxes = false;
        if (serieName) {
            /** @type {?} */
            var groupFromChartId;
            if (this.chart.yAxis[1].getExtremes().max == undefined) {
                lineAxeIsInvisible = true;
            }
            if (this.chart.yAxis[0].getExtremes().max == undefined) {
                areaAxeIsInvisible = true;
            }
            if (serieName.indexOf('.Thresholds') != -1) {
                groupFromChartId = serieName.split('.')[0];
                this.showHideThreshold(showHide, groupFromChartId);
            }
            else {
                for (var i = 0; i < this.chart.series.length; i++) {
                    if (this.chart.series[i].userOptions.valueId == serieName) {
                        if (!showHide) {
                            this.chart.series[i].hide();
                            //unHighlightSerieFunction(chart.series[i]);
                            this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                        }
                        else {
                            if (this.chart.series[i].type == 'line' && lineAxeIsInvisible) {
                                redrawAxes = true;
                            }
                            else if (this.chart.series[i].type == 'area' && areaAxeIsInvisible) {
                                redrawAxes = true;
                            }
                            this.chart.series[i].show();
                            for (var j = 0; j < this.invisibleLegend.length; j++) {
                                if (this.invisibleLegend[j] == this.chart.series[i].userOptions.valueId) {
                                    this.invisibleLegend.splice(j, 1);
                                    j--;
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!this.isSODClicked)
            this.alignScale(false);
        else {
            this.chart.yAxis[0].update({
                min: null,
                max: null
            });
            this.chart.yAxis[1].update({
                min: null,
                max: null
            });
        }
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} seriesToShow
     * @param {?} showHide
     * @return {?}
     */
    showOrHideMultipleSeries(seriesToShow, showHide) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var listOfSeries;
        /** @type {?} */
        var serieName;
        if (seriesToShow) {
            listOfSeries = seriesToShow.split(',');
            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                /** @type {?} */
                var groupFromChartId;
                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    this.showHideThreshold(showHide, groupFromChartId);
                }
                else {
                    for (var i = 0; i < this.chart.series.length; i++) {
                        if (this.chart.series[i].userOptions.valueId == serieName) {
                            if (!showHide) {
                                this.chart.series[i].hide();
                                this.unHighlightSerieFunction(this.chart.series[i]);
                                this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                            }
                            else if (this.sourceOfLiquidityChecked && this.chart.series[i].type == 'area') {
                                this.chart.series[i].hide();
                                this.unHighlightSerieFunction(this.chart.series[i]);
                                this.invisibleLegend.push(this.chart.series[i].userOptions.valueId);
                            }
                            else {
                                this.chart.series[i].show();
                                for (var h = 0; h < this.invisibleLegend.length; h++) {
                                    if (this.invisibleLegend[h] == this.chart.series[i].userOptions.valueId) {
                                        this.invisibleLegend.splice(h, 1);
                                        h--;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        /* if (getData()[2] == true) {
             invisibleLegend = [];
         }*/
        if (!this.isSODClicked)
            this.alignScale(false);
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} seriesToRemove
     * @return {?}
     */
    removeMultipleCharts(seriesToRemove) {
        this.disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        /** @type {?} */
        var listOfSeries;
        /** @type {?} */
        var serieName;
        if (seriesToRemove) {
            listOfSeries = seriesToRemove.split(',');
            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                /** @type {?} */
                var groupFromChartId;
                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    this.removeThreshold(groupFromChartId);
                }
                else {
                    for (var i = 0; i < this.chart.series.length; i++) {
                        if (this.chart.series[i].userOptions.valueId == serieName) {
                            this.chart.series[i].remove();
                            for (var h = this.invisibleLegend.length - 1; h >= 0; h--) {
                                if (this.invisibleLegend[h] === serieName) {
                                    this.invisibleLegend.splice(h, 1);
                                }
                            }
                        }
                    }
                }
            }
            for (let serieNumber = 0; serieNumber < this.inData.length; serieNumber++) {
                if (seriesToRemove.indexOf(this.inData[serieNumber].name) != -1) {
                    this.inData.splice(serieNumber, 1);
                    serieNumber--;
                }
                else if (this.inData[serieNumber].type == 'Threshold') {
                    if (seriesToRemove.indexOf(this.inData[serieNumber].name.split('.')[0] + "." + 'Thresholds') != -1) {
                        this.inData.splice(serieNumber, 1);
                        serieNumber--;
                    }
                }
            }
        }
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} isCurrencySelected
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    setEntityOrCurrencyTimeFrame(isCurrencySelected, timeStampFrom, timeStampTo) {
        /** @type {?} */
        const that = this;
        this.disableAutoRedraw();
        /** @type {?} */
        var j = 0;
        for (var i = 0; i < this.inData.length; i++) {
            if (this.inData[i].type != 'Threshold') {
                $(this.chart.series[j]).each((/**
                 * @return {?}
                 */
                function () {
                    this.update({
                        pointStart: isCurrencySelected ? that.inData[i].pointStart : that.inData[i].pointStartEntity,
                    }, false);
                }));
                j++;
            }
        }
        this.zoom(timeStampFrom, timeStampTo);
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} UNIX_timestamp
     * @return {?}
     */
    timeConverter(UNIX_timestamp) {
        /** @type {?} */
        var a = new Date(UNIX_timestamp);
        /** @type {?} */
        var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        /** @type {?} */
        var year = a.getFullYear();
        /** @type {?} */
        var month = months[a.getMonth()];
        /** @type {?} */
        var date = a.getDate();
        /** @type {?} */
        var hour = a.getHours();
        /** @type {?} */
        var min = a.getMinutes();
        /** @type {?} */
        var sec = a.getSeconds();
        /** @type {?} */
        var time = date + ' ' + month + ' ' + year + ' ' + hour + ':' + min + ':' + sec;
        return time;
    }
    /**
     * @param {?} timeStampFrom
     * @param {?} timeStampTo
     * @return {?}
     */
    zoom(timeStampFrom, timeStampTo) {
        this.chart.xAxis[0].setExtremes(Number(timeStampFrom), Number(timeStampTo));
        this.chart.yAxis[1].update({
            min: null,
            max: null
        });
        this.chart.yAxis[0].update({
            min: null,
            max: null
        });
        if (!this.isSODClicked)
            this.alignScale(false);
        if (this.chart.yAxis[0].min < 0 && this.chart.yAxis[0].max <= 0) {
            this.chart.yAxis[0].update({
                max: this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[0].min >= 0 && this.chart.yAxis[0].max > 0) {
            this.chart.yAxis[0].update({
                min: 0 - this.chart.yAxis[0].tickInterval
            });
        }
        if (this.chart.yAxis[1].min < 0 && this.chart.yAxis[1].max <= 0) {
            this.chart.yAxis[1].update({
                max: this.chart.yAxis[1].tickInterval
            });
        }
        if (this.chart.yAxis[1].min >= 0 && this.chart.yAxis[1].max > 0) {
            this.chart.yAxis[1].update({
                min: 0 - this.chart.yAxis[1].tickInterval
            });
        }
    }
    /**
     * @return {?}
     */
    resetZoom() {
        this.chart.zoomOut();
        if (!this.isSODClicked)
            this.alignScale(false);
    }
    /**
     * @param {?} chartSnapshot
     * @param {?} legendSnapshot
     * @param {?} dataXML
     * @param {?} exportType
     * @param {?} entityId
     * @param {?} currencyId
     * @param {?} selectedDate
     * @param {?} timeFrame
     * @return {?}
     */
    exportChart(chartSnapshot, legendSnapshot, dataXML, exportType, entityId, currencyId, selectedDate, timeFrame) {
        this.lastExportType = exportType;
        this.entityIdLocal = entityId;
        this.currencyIdLocal = currencyId;
        this.selectedDateLocal = selectedDate;
        this.timeFrameLocal = timeFrame;
        /** @type {?} */
        var svgChart = this.chart.getSVG();
        /** @type {?} */
        var dataAsXML;
        /** @type {?} */
        var dataCSV;
        //FIXME:
        // canvg('canvas', svgChart , {renderCallback: function() {
        //     var theImage = canvas.toDataURL('image/png');
        //     var stringImange = theImage.split(",")[1];
        //     if(exportType == "pdf")
        //         dataAsXML = "";
        //     else {
        //         dataCSV = chart.getCSV();
        //         if(dataCSV)
        //             dataAsXML = convertToXML(chart.getCSV());
        //         else
        //             dataAsXML = "<dataprovider userId=\"\" lastUpdate=\"\">\n" +
        //                 "  <result> <timeSlot></timeSlot></result></dataprovider>";
        //     }
        //     parent.onExport(stringImange, legendSnapshot, dataAsXML, exportType, entityId, currencyId,selectedDate, timeFrame);
        // }});
        this.svg_to_png_data(svgChart, legendSnapshot);
    }
    /**
     * @param {?} svg_string
     * @param {?} legendSnapshot
     * @return {?}
     */
    svg_to_png_data(svg_string, legendSnapshot) {
        // var ctx, mycanvas,  img, child;
        // var ctx, mycanvas,  img, child;
        // // Flatten CSS styles into the SVG
        // img = new Image();
        // img.src = "data:image/svg+xml," + encodeURIComponent(svg_data);
        // // Draw the SVG image to a canvas
        // mycanvas = document.createElement('canvas');
        // mycanvas.width = 400;
        // mycanvas.height = 400;
        // ctx = mycanvas.getContext("2d");
        // ctx.drawImage(img, 0, 0);
        // // Return the canvas's data
        // return mycanvas.toDataURL("image/png");
        /** @type {?} */
        const svg = this.createElementFromHTML(svg_string);
        /** @type {?} */
        let clonedSvgElement = (/** @type {?} */ (svg.cloneNode(true)));
        /** @type {?} */
        let outerHTML = clonedSvgElement.outerHTML;
        /** @type {?} */
        let blob = new Blob([outerHTML], { type: 'image/svg+xml;charset=utf-8' });
        /** @type {?} */
        let blobURL = webkitURL.createObjectURL(blob);
        /** @type {?} */
        let image = new Image();
        image.onload = (/**
         * @return {?}
         */
        () => {
            /** @type {?} */
            let canvas = document.createElement('canvas');
            canvas.width = 950;
            canvas.height = 550;
            /** @type {?} */
            let context = canvas.getContext('2d');
            // draw image in canvas starting left-0 , top - 0  
            context.drawImage(image, 0, 0, 950, 500);
            //  downloadImage(canvas); need to implement
            /** @type {?} */
            let png = canvas.toDataURL();
            this.runReport(png, legendSnapshot);
        });
        image.src = blobURL;
    }
    /**
     * @param {?} htmlString
     * @return {?}
     */
    createElementFromHTML(htmlString) {
        /** @type {?} */
        var div = document.createElement('div');
        div.innerHTML = htmlString.trim();
        // Change this to div.childNodes to support multiple top-level nodes
        return div.firstChild;
    }
    /**
     * @param {?} png
     * @param {?} legend
     * @return {?}
     */
    runReport(png, legend) {
        // console.log("SwtILMChart -> runReport -> png", png)
        /** @type {?} */
        let dataAsXML;
        //let dataCSV;
        if (this.lastExportType == "pdf")
            dataAsXML = "";
        else {
            //dataCSV = this.chart.getCSV();
            dataAsXML = '<dataprovider userId="" lastUpdate="">\n';
            // Collect all categories (e.g., time)
            /** @type {?} */
            const categories = new Set();
            this.chart.series.forEach((/**
             * @param {?} series
             * @return {?}
             */
            (series) => {
                series.processedXData.forEach((/**
                 * @param {?} point
                 * @return {?}
                 */
                (point) => {
                    categories.add(point); // Assuming `category` holds time information
                }));
            }));
            //  console.log("🚀 ~ categories.forEach ~ categories:", categories)
            /** @type {?} */
            let i = 0;
            // For each category, collect all series data for that category
            categories.forEach((/**
             * @param {?} category
             * @return {?}
             */
            (category) => {
                /** @type {?} */
                const date = new Date(category);
                /** @type {?} */
                const formattedTime = this.formatDate(date);
                dataAsXML += `  <result><timeSlot>${formattedTime}</timeSlot>\n`;
                //console.log('bb-',category);
                this.chart.series.forEach((/**
                 * @param {?} series
                 * @return {?}
                 */
                (series) => {
                    // Modify series name as per the given rules
                    /** @type {?} */
                    let heading = series.userOptions.valueId.replace(/ /g, '').replace(/\(|\)/g, "").replace(/\//g, '-');
                    //  console.log('aa-',heading);
                    // Find the point corresponding to the current category
                    /** @type {?} */
                    const point = series.processedYData[i];
                    dataAsXML += `    <${heading}>${point}</${heading}>\n`;
                }));
                i++;
                dataAsXML += '  </result>\n';
            }));
            dataAsXML += '</dataprovider>';
            //  this.chart.export({ type: 'csv' });
            if (!dataAsXML)
                dataAsXML = "<dataprovider userId=\"\" lastUpdate=\"\">\n" +
                    "  <result> <timeSlot></timeSlot></result></dataprovider>";
        }
        // parent.onExport(png, "", dataAsXML, this.lastExportType, entityId, currencyId,selectedDate, timeFrame);
        ExternalInterface.call("onExport", png, legend, dataAsXML, this.lastExportType, this.entityIdLocal, this.currencyIdLocal, this.selectedDateLocal, this.timeFrameLocal);
    }
    /**
     * @param {?} date
     * @return {?}
     */
    formatDate(date) {
        /** @type {?} */
        const year = date.getFullYear();
        /** @type {?} */
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        // Zero pad month
        /** @type {?} */
        const day = ('0' + date.getDate()).slice(-2);
        // Zero pad day
        /** @type {?} */
        const hours = ('0' + date.getHours()).slice(-2);
        /** @type {?} */
        const minutes = ('0' + date.getMinutes()).slice(-2);
        /** @type {?} */
        const seconds = ('0' + date.getSeconds()).slice(-2);
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    /**
     * @param {?} csvData
     * @return {?}
     */
    convertToXML(csvData) {
        /** @type {?} */
        var dataAsXML = "";
        //console.log('------csvData',this.chart)
        try {
            /** @type {?} */
            var dataArr = csvData.split("\n");
            /** @type {?} */
            var heading = dataArr[0].split(",");
            for (var h = 0; h < heading.length; h++) {
                heading[h] = heading[h].replace(/['"]+/g, '');
                if (heading[h] == "DateTime") {
                    heading[h] = "timeSlot";
                }
                heading[h] = heading[h].replace(/ /g, '').replace(/\(|\)/g, "").replace(/\//g, '-');
            }
            /** @type {?} */
            var valueTag;
            /** @type {?} */
            var data = dataArr.splice(1, dataArr.length - 1);
            dataAsXML += "<dataprovider userId=\"\" lastUpdate=\"\">\n";
            for (var i = 0; i < data.length; i++) {
                /** @type {?} */
                var d = data[i].split(",");
                dataAsXML += "<result>";
                for (var j = 0; j < d.length; j++) {
                    if (heading[j] != "timeSlot") {
                        dataAsXML += "<" + heading[j] + ">" + d[j] + "</" + heading[j] + ">";
                    }
                    else {
                        valueTag = d[j].slice(1, -1);
                        dataAsXML += "<" + heading[j] + ">" + valueTag + "</" + heading[j] + ">";
                    }
                }
                dataAsXML += "</result>";
            }
            dataAsXML += "</dataprovider>";
            //console.log(dataAsXML);
        }
        catch (error) {
            console.log("SwtILMChart -> convertToXML -> error", error);
        }
        return dataAsXML;
    }
    /**
     * @param {?} newStyles
     * @return {?}
     */
    changeChartsStyle(newStyles) {
        this.disableAutoRedraw();
        /** @type {?} */
        var stylesAsJson;
        /** @type {?} */
        var yField;
        /** @type {?} */
        var styleName;
        /** @type {?} */
        var colorChart;
        /** @type {?} */
        var borderColor;
        /** @type {?} */
        var chartsType;
        /** @type {?} */
        var chartTypeDetailsID;
        /** @type {?} */
        var dashStyle;
        /** @type {?} */
        var fillColor;
        /** @type {?} */
        var isAreaDashed;
        /** @type {?} */
        var associativeArray = new Array();
        if (newStyles) {
            stylesAsJson = JSON.parse(newStyles);
            for (var j = 0; j < stylesAsJson.length; j++) {
                yField = stylesAsJson[j].name;
                styleName = stylesAsJson[j].chartStyleName;
                colorChart = stylesAsJson[j].color;
                borderColor = stylesAsJson[j].borderColor;
                chartTypeDetailsID = stylesAsJson[j].typeDetails;
                chartsType = stylesAsJson[j].type;
                if (chartsType == 'line' && chartTypeDetailsID == "3") {
                    dashStyle = 'Dash';
                }
                else if (chartsType == 'line' && chartTypeDetailsID == "2") {
                    dashStyle = 'shortdot';
                }
                else {
                    dashStyle = '';
                }
                if (chartsType == 'area' && colorChart.indexOf('png') != -1) {
                    fillColor = this.getFillPatternForImange(colorChart, borderColor);
                    // {
                    //   pattern: {
                    //     path: {
                    //       d: 'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',
                    //       strokeWidth: 1
                    //     },
                    //     width: 4,
                    //     height: 4,
                    //     color: borderColor,
                    //   }
                    // }
                    isAreaDashed = 'dashed';
                }
                else {
                    fillColor = {};
                    isAreaDashed = '';
                }
                for (var i = 0; i < this.chart.series.length; i++) {
                    if (this.chart.series[i].userOptions.valueId == yField) {
                        if (this.chart.series[i].type == 'area') {
                            if (isAreaDashed) {
                                this.chart.series[i].update({
                                    fillColor: fillColor,
                                    color: borderColor,
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, true);
                            }
                            else {
                                this.chart.series[i].update({
                                    color: borderColor,
                                    fillColor: 'rgba(' + parseInt(borderColor.slice(-6, -4), 16) + ',' + parseInt(borderColor.slice(-4, -2), 16) + ',' + parseInt(borderColor.slice(-2), 16) + ',0.7)',
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, false);
                            }
                        }
                        else if (this.chart.series[i].type == 'line') {
                            this.chart.series[i].update({
                                color: colorChart,
                                dashStyle: dashStyle,
                            }, false);
                        }
                    }
                }
            }
            //chart.redraw();
        }
        this.enableAutoredrawAndRedrawChart();
    }
    /**
     * @param {?} obj
     * @return {?}
     */
    isEmpty(obj) {
        for (var key in obj) {
            if (obj.hasOwnProperty(key))
                return false;
        }
        return true;
    }
    /**
     * @param {?} i
     * @return {?}
     */
    addZero(i) {
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }
    // public options: any = {
    //   chart: {
    //     type: 'scatter',
    //     height: 700
    //   },
    //   title: {
    //     text: 'Sample Scatter Plot'
    //   },
    //   credits: {
    //     enabled: false
    //   },
    //   tooltip: {
    //     formatter: function () {
    //       return 'x: ' + Highcharts.dateFormat('%e %b %y %H:%M:%S', this.x) +
    //         ' y: ' + this.y.toFixed(2);
    //     }
    //   },
    //   xAxis: {
    //     type: 'datetime',
    //     labels: {
    //       formatter: function () {
    //         return Highcharts.dateFormat('%e %b %y', this.value);
    //       }
    //     }
    //   },
    //   series: [
    //     {
    //       name: 'Normal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-01-25 18:38:31').getTime(), 2]]
    //     },
    //     {
    //       name: 'Abnormal',
    //       turboThreshold: 500000,
    //       data: [[new Date('2018-02-05 18:38:31').getTime(), 7]]
    //     }
    //   ]
    // }
    /**
     * @return {?}
     */
    ngOnInit() {
        // this.chart = Highcharts.chart(this.containerHighChart.nativeElement, this.options);
    }
    // ngAfterViewInit() {
    //   const ro = new ResizeObserver((entries, observer) => {
    //     if (this.isVisible(this.elem.nativeElement)) {
    //       this.chart.reflow();
    //       this.chart.update({
    //         plotOptions: {
    //           series: {
    //             states: {
    //               hover: {
    //                 enabled: false
    //               },
    //               inactive: {
    //                 enabled: false
    //               }
    //             }
    //           }
    //         },
    //       })
    //     }
    //   });
    //   ro.observe(this.elem.nativeElement);
    // }
    /**
     * @param {?} height
     * @return {?}
     */
    redrawChart(height) {
        $(this.containerHighChart.nativeElement).height(height - 12);
        if (this.chart) {
            this.chart.reflow();
            this.chart.update({
                plotOptions: {
                    series: {
                        states: {
                            hover: {
                                enabled: true
                            },
                        }
                    }
                },
            });
        }
    }
    /**
     * @param {?} e
     * @return {?}
     */
    isVisible(e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    }
}
SwtILMChart.decorators = [
    { type: Component, args: [{
                selector: 'swt-ilm-chart',
                template: "<div class=\"chartContainer\" #containerHighChart ></div>",
                styles: [""]
            }] }
];
/** @nocollapse */
SwtILMChart.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtILMChart.propDecorators = {
    containerHighChart: [{ type: ViewChild, args: ["containerHighChart",] }]
};
if (false) {
    /** @type {?} */
    SwtILMChart.prototype.containerHighChart;
    /** @type {?} */
    SwtILMChart.prototype.chart;
    /** @type {?} */
    SwtILMChart.prototype.mousePosition;
    /** @type {?} */
    SwtILMChart.prototype.invisibleLegend;
    /** @type {?} */
    SwtILMChart.prototype.tooltipValues;
    /** @type {?} */
    SwtILMChart.prototype.sourceOfLiquidityChecked;
    /** @type {?} */
    SwtILMChart.prototype.useCcyMulitplierChecked;
    /** @type {?} */
    SwtILMChart.prototype.isEntityTimeFrameChecked;
    /** @type {?} */
    SwtILMChart.prototype.showActualDatasetOnlyChecked;
    /** @type {?} */
    SwtILMChart.prototype.entityTimeDifference;
    /** @type {?} */
    SwtILMChart.prototype.firstLoadDataZones;
    /** @type {?} */
    SwtILMChart.prototype.zoomFromTime;
    /** @type {?} */
    SwtILMChart.prototype.zoomToTime;
    /** @type {?} */
    SwtILMChart.prototype.inData;
    /** @type {?} */
    SwtILMChart.prototype._redraw;
    /** @type {?} */
    SwtILMChart.prototype.redrawFunctionIsEmpty;
    /** @type {?} */
    SwtILMChart.prototype.visibleLinesInChart;
    /** @type {?} */
    SwtILMChart.prototype.allThresholds;
    /** @type {?} */
    SwtILMChart.prototype.dataZones;
    /** @type {?} */
    SwtILMChart.prototype.updateTempVariable;
    /** @type {?} */
    SwtILMChart.prototype.currencyFormat;
    /** @type {?} */
    SwtILMChart.prototype.currencyMutiplierValue;
    /** @type {?} */
    SwtILMChart.prototype.currencyDecimalPlaces;
    /** @type {?} */
    SwtILMChart.prototype.dateFormatAsString;
    /** @type {?} */
    SwtILMChart.prototype.saveHighligtedCharts;
    /** @type {?} */
    SwtILMChart.prototype.highlightedSeries;
    /** @type {?} */
    SwtILMChart.prototype.callerTabName;
    /** @type {?} */
    SwtILMChart.prototype.seriesMouseover;
    /** @type {?} */
    SwtILMChart.prototype.symbol;
    /** @type {?} */
    SwtILMChart.prototype.markers;
    /** @type {?} */
    SwtILMChart.prototype.timer;
    /** @type {?} */
    SwtILMChart.prototype.series;
    /** @type {?} */
    SwtILMChart.prototype.notChecked;
    /** @type {?} */
    SwtILMChart.prototype.isSODClicked;
    /** @type {?} */
    SwtILMChart.prototype.isThresholderClicked;
    /** @type {?} */
    SwtILMChart.prototype.hasPlotBand;
    /** @type {?} */
    SwtILMChart.prototype.tooltipText;
    /** @type {?} */
    SwtILMChart.prototype.hasCheckedCurrency;
    /** @type {?} */
    SwtILMChart.prototype.saveUncheckedItems;
    /** @type {?} */
    SwtILMChart.prototype.uncheckedItemsFromParent;
    /** @type {?} */
    SwtILMChart.prototype.options;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawn;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawnNewdata;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.chartNotDrawntabName;
    /** @type {?} */
    SwtILMChart.prototype.listOfBandsIds;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.log10;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.entityIdLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.currencyIdLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.selectedDateLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.timeFrameLocal;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.lastExportType;
    /** @type {?} */
    SwtILMChart.prototype.each;
    /** @type {?} */
    SwtILMChart.prototype.pick;
    /** @type {?} */
    SwtILMChart.prototype.seriesTypes;
    /** @type {?} */
    SwtILMChart.prototype.downloadAttrSupported;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtILMChart.prototype.commonService;
    /* Skipping unhandled member: ;*/
    /* Skipping unhandled member: ;*/
}
//# sourceMappingURL=data:application/json;base64,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