import { OnInit, ElementRef } from '@angular/core';
import { Container } from '../../../../../containers/swt-container.component';
import { CommonService } from '../../../../../utils/common.service';
import * as Highcharts from 'highcharts';
export declare class SwtILMChart extends Container implements OnInit {
    private elem;
    private commonService;
    containerHighChart: ElementRef;
    chart: any;
    mousePosition: any[];
    invisibleLegend: any[];
    tooltipValues: any[];
    sourceOfLiquidityChecked: boolean;
    useCcyMulitplierChecked: boolean;
    isEntityTimeFrameChecked: boolean;
    showActualDatasetOnlyChecked: boolean;
    entityTimeDifference: number;
    firstLoadDataZones: any[];
    zoomFromTime: any;
    zoomToTime: any;
    inData: any;
    _redraw: any;
    redrawFunctionIsEmpty: boolean;
    visibleLinesInChart: any[];
    allThresholds: any[];
    dataZones: any;
    updateTempVariable: boolean;
    currencyFormat: string;
    currencyMutiplierValue: number;
    currencyDecimalPlaces: number;
    dateFormatAsString: string;
    saveHighligtedCharts: boolean;
    highlightedSeries: any[];
    callerTabName: string;
    seriesMouseover: any[];
    symbol: any[];
    markers: any[];
    timer: any;
    series: any[];
    notChecked: boolean;
    isSODClicked: boolean;
    isThresholderClicked: boolean;
    hasPlotBand: boolean;
    tooltipText: any;
    hasCheckedCurrency: boolean;
    saveUncheckedItems: any;
    uncheckedItemsFromParent: any;
    options: any;
    constructor(elem: ElementRef, commonService: CommonService);
    callMethodByName(methodName: any, methodArguments: any): void;
    private chartNotDrawn;
    private chartNotDrawnNewdata;
    private chartNotDrawntabName;
    setILMData(newData: any, mergeData: any, updateTempVar: any, tabName: any): void;
    forceDrawChartIfNotDrawn(): void;
    getFillPatternForImange(imageName: any, color: any): {
        pattern: {
            path: {
                d: any;
                strokeWidth: number;
            };
            width: number;
            height: number;
            color: any;
            opacity: number;
        };
    };
    firstLoad(dataFromParent: any, resetAll: any, tabName: any): void;
    setILMDataZones(dataZonesJSON: any): void;
    destroyChart(): void;
    getData(): any[];
    mergeNewCharts(newDataAsJSON: any): void;
    createILMChart(): void;
    updateClock(dateAsDateStamp: any): void;
    showHideActualDataSet(showHide: any, visibleYFields: any): void;
    showHideThreshold(showHide: any, groupId: any): void;
    alignScaleWithSOD(alignScale: any): void;
    showHideSourcesOfLiquidity(showHide: any, valuesUpdated: any, dataZonesJSON: any, visibleItemsInTree: any): void;
    hideThreshold(groupName: any): void;
    removeThreshold(groupName: any): void;
    showThreshold(groupName: any): void;
    checkDataSet(visibleYFields: any): void;
    unCheckDataSet(visibleYFields: any): void;
    enableAutoredrawAndRedrawChart(): void;
    adjutMinMax(): void;
    enableAutoredrawOnly(): void;
    disableAutoRedraw(): void;
    calculateStatistics(): number[];
    alignScale(forceCalculation: any): void;
    applySODandUnalignScale(): void;
    updateVisibleThresholds(): void;
    uncheckSODandalignScale(): void;
    toDate(timestamp: any): string;
    showLiquiditySource(valuesUpdated: any, dataZonesAsJSONString: any): void;
    hideOrShowBandOrLine(band: any, hideOrshow: any): void;
    listOfBandsIds: string[];
    hideLiquiditySource(visibleItemsInTree: any): void;
    getPlotBandById(plotBandId: any): any;
    checkMultiplierCurrenyMultiplier(): void;
    uncheckMultiplierCurrenyMultiplier(): void;
    /**
    * Returns the decimal places for the most significant digit for the smallet label in the axis
    */
    smallestLabelSigDigitDcPlaces(smallestLabel: any): number;
    private log10;
    getSignificantDigitCount(n: any): number;
    /**
    * Right vertical axis formatter
    */
    rightVerticalAxisFormatter(labelValue: any): string;
    /**
    * Left vertical axis formatter
    */
    leftVerticalAxisFormatter(labelValue: any): string;
    /**
    * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
    */
    commonAxisFormatter(labelValue: any, sigDigitDcPlaces: any): string;
    formatMoney(n: any, c: any, d: any, t: any): string;
    /**
    * This function allow getting the significant digit after the decimal
    * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
    */
    getFirstSignificantDigit(number: any, maxDecimals: any, forceDecimals: any, siStyle: any): string | 0;
    firstSignificant(value: any): number;
    ccyMultiplierEventHandler(ccyMuliplierSelected: any): void;
    highlightSerie(serieName: any, forcedValueState: any): void;
    highlightSerieFunction(serie: any): void;
    unHighlightSerieFunction(serie: any): void;
    showSerie(serieName: any, showHide: any): void;
    showOrHideMultipleSeries(seriesToShow: any, showHide: any): void;
    removeMultipleCharts(seriesToRemove: any): void;
    setEntityOrCurrencyTimeFrame(isCurrencySelected: any, timeStampFrom: any, timeStampTo: any): void;
    timeConverter(UNIX_timestamp: any): string;
    zoom(timeStampFrom: any, timeStampTo: any): void;
    resetZoom(): void;
    private entityIdLocal;
    private currencyIdLocal;
    private selectedDateLocal;
    private timeFrameLocal;
    exportChart(chartSnapshot: any, legendSnapshot: any, dataXML: any, exportType: any, entityId: any, currencyId: any, selectedDate: any, timeFrame: any): void;
    svg_to_png_data(svg_string: any, legendSnapshot: any): void;
    createElementFromHTML(htmlString: any): ChildNode;
    private lastExportType;
    runReport(png: any, legend: any): void;
    formatDate(date: Date): string;
    convertToXML(csvData: any): string;
    changeChartsStyle(newStyles: any): void;
    isEmpty(obj: any): boolean;
    addZero(i: any): any;
    each: typeof Highcharts.each;
    pick: typeof Highcharts.pick;
    seriesTypes: typeof Highcharts.seriesType;
    downloadAttrSupported: boolean;
    ngOnInit(): void;
    redrawChart(height: any): void;
    isVisible(e: any): boolean;
}
