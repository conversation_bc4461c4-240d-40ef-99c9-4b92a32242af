/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import { isNegative, isClickable, CustomCell, isBold } from './cellItemRenderUtilities';
/** @type {?} */
export var SwttreeFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @param {?} grid
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext, grid) {
    /** @type {?} */
    var dataView = grid && grid.getData();
    /** @type {?} */
    var gridOptions = grid && (/** @type {?} */ (grid.getOptions()));
    /** @type {?} */
    var treeDataOptions = gridOptions && gridOptions.treeDataOptions;
    /** @type {?} */
    var treeLevelPropName = treeDataOptions && treeDataOptions.levelPropName || '__treeLevel';
    /** @type {?} */
    var indentMarginLeft = treeDataOptions && treeDataOptions.indentMarginLeft || 15;
    /** @type {?} */
    var outputValue = value;
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    var negative = isNegative(dataContext, field);
    /** @type {?} */
    var isLink = isClickable(dataContext, field);
    /** @type {?} */
    var bold = columnDef['bold'] ? columnDef['bold'] : isBold(dataContext, field);
    /** @type {?} */
    var style = CustomCell(dataContext, field);
    /** @type {?} */
    var enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    var type = columnDef['columnType'];
    if (style == "") {
        if (backgroundColor == undefined) {
            backgroundColor = 'transparent';
        }
        if (backgroundColor && backgroundColor.toString().indexOf('|') > -1) {
            // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
            /** @type {?} */
            var colorList = backgroundColor.split('|');
            style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
        }
        else {
            style += ' background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        }
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (columnDef.params.firstAfterSkipedColumn == true)
        style += ' padding-right: 5px; text-align: right; ';
    if (typeof columnDef.queryFieldNameGetterFn === 'function') {
        /** @type {?} */
        var fieldName = columnDef.queryFieldNameGetterFn(dataContext);
        if (fieldName && fieldName.indexOf('.') >= 0) {
            outputValue = getDescendantProperty(dataContext, fieldName);
        }
        else {
            outputValue = dataContext.hasOwnProperty(fieldName) ? dataContext[fieldName] : value;
        }
    }
    if (outputValue === null || outputValue === undefined || dataContext === undefined) {
        return '';
    }
    if (!dataContext.hasOwnProperty(treeLevelPropName)) {
        throw new Error('You must provide valid "treeDataOptions" in your Grid Options and it seems that there are no tree level found in this row');
    }
    if (dataView && dataView.getIdxById && dataView.getItemByIdx) {
        if (typeof outputValue === 'string') {
            outputValue = htmlEncode(outputValue);
        }
        /** @type {?} */
        var identifierPropName = dataView.getIdPropertyName() || 'id';
        /** @type {?} */
        var spacer = "<span style=\"display:inline-block; width:" + indentMarginLeft * dataContext[treeLevelPropName] + "px;\"></span>";
        /** @type {?} */
        var idx = dataView.getIdxById(dataContext[identifierPropName]);
        /** @type {?} */
        var nextItemRow = dataView.getItemByIdx(idx + 1);
        outputValue = columnDef.params.customContentFunction(dataContext, columnDef.field, outputValue, type);
        if (nextItemRow && nextItemRow[treeLevelPropName] > dataContext[treeLevelPropName]) {
            if (dataContext.__collapsed) {
                return "<div class=\"strFormatterDiv\"  style='" + style + "' >" + spacer + "<span class=\"slick-group-toggle collapsed\"></span>&nbsp;" + (isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue) + "</div>";
            }
            else {
                return "<div class=\"strFormatterDiv\"  style='" + style + "' >" + spacer + "<span class=\"slick-group-toggle expanded\"></span>&nbsp;" + (isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue) + "</div>";
            }
        }
        return "<div class=\"strFormatterDiv\"  style='" + style + "' >" + spacer + "<span class=\"slick-group-toggle\"></span>&nbsp;" + (isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue) + "</div>";
    }
    return '';
});
//# sourceMappingURL=data:application/json;base64,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