/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
var SwtGroupedTotalCommonGrid = /** @class */ (function (_super) {
    tslib_1.__extends(SwtGroupedTotalCommonGrid, _super);
    function SwtGroupedTotalCommonGrid(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        var _this = _super.call(this, el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) || this;
        _this.el = el;
        _this.commonService = commonService;
        _this.autoTooltipExtension = autoTooltipExtension;
        _this.extensionUtility = extensionUtility;
        _this.sharedService = sharedService;
        _this.translate = translate;
        _this.showHeader = false;
        _this.isGroupedHeaderGrid = false;
        _this.isTotalGrid = true;
        return _this;
    }
    Object.defineProperty(SwtGroupedTotalCommonGrid.prototype, "initialColumnsToSkip", {
        get: /**
         * @return {?}
         */
        function () {
            return this._initialColumnsToSkip;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._initialColumnsToSkip = value;
        },
        enumerable: true,
        configurable: true
    });
    SwtGroupedTotalCommonGrid.decorators = [
        { type: Component, args: [{
                    selector: 'SwtGroupedTotalCommonGrid',
                    template: "\n    <angular-slickgrid\n    class=\"commonSlickGrid\"\n            #angularSlickGrid\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n",
                    providers: [
                        TranslateService,
                        ExtensionService,
                        AutoTooltipExtension,
                        ExtensionUtility,
                        SharedService,
                        CollectionService
                    ],
                    styles: ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"]
                }] }
    ];
    /** @nocollapse */
    SwtGroupedTotalCommonGrid.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: AutoTooltipExtension },
        { type: ExtensionUtility },
        { type: SharedService },
        { type: CollectionService },
        { type: TranslateService }
    ]; };
    return SwtGroupedTotalCommonGrid;
}(SwtCommonGrid));
export { SwtGroupedTotalCommonGrid };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbW1vbi1ncm91cGVkLXRvdGFsLWdyaWQuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1jb21tb24tZ3JvdXBlZC10b3RhbC1ncmlkLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7OztBQUFBLE9BQU8sRUFDbUQsU0FBUyxFQUUwRCxVQUFVLEVBQ3RJLE1BQU0sZUFBZSxDQUFDO0FBQ3ZCLE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSw2QkFBNkIsQ0FBQztBQUM1RCxPQUFPLEVBNEJILGFBQWEsRUFNYixnQkFBZ0IsRUFHaEIsZ0JBQWdCLEVBQ2hCLG9CQUFvQixFQVdwQixpQkFBaUIsR0FDcEIsTUFBTSxtQkFBbUIsQ0FBQztBQUMzQixPQUFPLEVBQUUsZ0JBQWdCLEVBQUUsTUFBTSxxQkFBcUIsQ0FBQztBQUN2RCxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFFeEQ7SUEyQytDLHFEQUFhO0lBRXhELG1DQUF1QixFQUFjLEVBQVksYUFBNEIsRUFBYSxvQkFBMkMsRUFBWSxnQkFBa0MsRUFBWSxhQUE2QixFQUN2TixpQkFBcUMsRUFBYSxTQUEyQjtRQURsRixZQUVJLGtCQUFNLEVBQUUsRUFBQyxhQUFhLEVBQUMsb0JBQW9CLEVBQUUsZ0JBQWdCLEVBQUMsYUFBYSxFQUFFLGlCQUFpQixFQUFFLFNBQVMsQ0FBQyxTQUk1RztRQU5xQixRQUFFLEdBQUYsRUFBRSxDQUFZO1FBQVksbUJBQWEsR0FBYixhQUFhLENBQWU7UUFBYSwwQkFBb0IsR0FBcEIsb0JBQW9CLENBQXVCO1FBQVksc0JBQWdCLEdBQWhCLGdCQUFnQixDQUFrQjtRQUFZLG1CQUFhLEdBQWIsYUFBYSxDQUFnQjtRQUNySyxlQUFTLEdBQVQsU0FBUyxDQUFrQjtRQUU5RSxLQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztRQUN4QixLQUFJLENBQUMsbUJBQW1CLEdBQUcsS0FBSyxDQUFDO1FBQ2pDLEtBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDOztJQUMzQixDQUFDO0lBSUQsc0JBQUksMkRBQW9COzs7O1FBSXhCO1lBQ0ksT0FBTyxJQUFJLENBQUMscUJBQXFCLENBQUU7UUFDdkMsQ0FBQzs7Ozs7UUFORCxVQUEyQixLQUFhO1lBQ3BDLElBQUksQ0FBQyxxQkFBcUIsR0FBRyxLQUFLLENBQUM7UUFDdkMsQ0FBQzs7O09BQUE7O2dCQXpETCxTQUFTLFNBQUU7b0JBQ1IsUUFBUSxFQUFFLDJCQUEyQjtvQkFDckMsUUFBUSxFQUFFLHFlQWViO29CQWdCRCxTQUFTLEVBQUU7d0JBQ1AsZ0JBQWdCO3dCQUNoQixnQkFBZ0I7d0JBQ2hCLG9CQUFvQjt3QkFDcEIsZ0JBQWdCO3dCQUNoQixhQUFhO3dCQUNiLGlCQUFpQjtxQkFDcEI7NkJBdEJRLDRQQWNSO2lCQVNBOzs7O2dCQWxHNEgsVUFBVTtnQkF1RDlILGFBQWE7Z0JBZGxCLG9CQUFvQjtnQkFKcEIsZ0JBQWdCO2dCQU5oQixhQUFhO2dCQXFCYixpQkFBaUI7Z0JBRVosZ0JBQWdCOztJQWlFekIsZ0NBQUM7Q0FBQSxBQTlERCxDQTJDK0MsYUFBYSxHQW1CM0Q7U0FuQlkseUJBQXlCOzs7Ozs7SUFFckIsdUNBQXdCOzs7OztJQUFFLGtEQUFzQzs7Ozs7SUFBRyx5REFBcUQ7Ozs7O0lBQUUscURBQTRDOzs7OztJQUFFLGtEQUF1Qzs7Ozs7SUFDL0ssOENBQXFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICAgIEFmdGVyQ29udGVudENoZWNrZWQsIEFmdGVyVmlld0NoZWNrZWQsIEFmdGVyVmlld0luaXQsIENvbXBvbmVudCwgQ29tcG9uZW50RmFjdG9yeSxcclxuICAgIENvbXBvbmVudEZhY3RvcnlSZXNvbHZlciwgQ29tcG9uZW50UmVmLEV2ZW50RW1pdHRlcixcclxuICAgIEluamVjdGFibGUsIElucHV0LCBPbkNoYW5nZXMsIE9uSW5pdCwgT3V0cHV0LCBSZW5kZXJlciwgVmlld0NoaWxkLCBWaWV3Q29udGFpbmVyUmVmLE9uRGVzdHJveSwgSW5qZWN0b3IsIEFwcGxpY2F0aW9uUmVmLCBFbGVtZW50UmVmXHJcbn0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IFN3dENvbW1vbkdyaWQgfSBmcm9tICcuL3N3dC1jb21tb24tZ3JpZC5jb21wb25lbnQnO1xyXG5pbXBvcnQge1xyXG4gICAgQWdncmVnYXRvcnMsXHJcbiAgICBBbmd1bGFyR3JpZEluc3RhbmNlLFxyXG4gICAgQW5ndWxhclNsaWNrZ3JpZENvbXBvbmVudCxcclxuICAgIEJhY2tlbmRTZXJ2aWNlT3B0aW9uLFxyXG4gICAgQ2VsbEFyZ3MsXHJcbiAgICBDb2x1bW4sXHJcbiAgICBDb2x1bW5GaWx0ZXJzLFxyXG4gICAgRWRpdG9ycyxcclxuICAgIEV4cG9ydFNlcnZpY2UsXHJcbiAgICBGaWVsZFR5cGUsXHJcbiAgICBGaWx0ZXJDaGFuZ2VkQXJncyxcclxuICAgIEZpbHRlckZhY3RvcnksXHJcbiAgICBGaWx0ZXJzLFxyXG4gICAgRmlsdGVyU2VydmljZSxcclxuICAgIEZvcm1hdHRlcixcclxuICAgIEZvcm1hdHRlcnMsXHJcbiAgICBHcmlkTWVudUV4dGVuc2lvbixcclxuICAgIEdyaWRPcHRpb24sXHJcbiAgICBHcmlkU2VydmljZSxcclxuICAgIEdyaWRTdGF0ZUNoYW5nZSxcclxuICAgIEdyaWRTdGF0ZVNlcnZpY2UsXHJcbiAgICBHcm91cGluZyxcclxuICAgIEdyb3VwVG90YWxGb3JtYXR0ZXJzLFxyXG4gICAgT25FdmVudEFyZ3MsXHJcbiAgICBQYWdpbmF0aW9uLFxyXG4gICAgUGFnaW5hdGlvbkNoYW5nZWRBcmdzLFxyXG4gICAgUmVzaXplclNlcnZpY2UsXHJcbiAgICBTaGFyZWRTZXJ2aWNlLFxyXG4gICAgU2xpY2tncmlkQ29uZmlnLFxyXG4gICAgU29ydENoYW5nZWRBcmdzLFxyXG4gICAgU29ydERpcmVjdGlvbk51bWJlcixcclxuICAgIFNvcnRlcnMsXHJcbiAgICBTb3J0U2VydmljZSxcclxuICAgIEV4dGVuc2lvblV0aWxpdHksXHJcbiAgICBFeHRlbnNpb25OYW1lLFxyXG4gICAgSGVhZGVyTWVudSxcclxuICAgIEV4dGVuc2lvblNlcnZpY2UsXHJcbiAgICBBdXRvVG9vbHRpcEV4dGVuc2lvbixcclxuICAgIENlbGxFeHRlcm5hbENvcHlNYW5hZ2VyRXh0ZW5zaW9uLFxyXG4gICAgQ29sdW1uUGlja2VyRXh0ZW5zaW9uLFxyXG4gICAgRHJhZ2dhYmxlR3JvdXBpbmdFeHRlbnNpb24sXHJcbiAgICBIZWFkZXJNZW51RXh0ZW5zaW9uLFxyXG4gICAgSGVhZGVyQnV0dG9uRXh0ZW5zaW9uLFxyXG4gICAgR3JvdXBJdGVtTWV0YVByb3ZpZGVyRXh0ZW5zaW9uLFxyXG4gICAgQ2hlY2tib3hTZWxlY3RvckV4dGVuc2lvbixcclxuICAgIFJvd0RldGFpbFZpZXdFeHRlbnNpb24sXHJcbiAgICBSb3dNb3ZlTWFuYWdlckV4dGVuc2lvbixcclxuICAgIFJvd1NlbGVjdGlvbkV4dGVuc2lvbixcclxuICAgIENvbGxlY3Rpb25TZXJ2aWNlLFxyXG59IGZyb20gJ2FuZ3VsYXItc2xpY2tncmlkJztcclxuaW1wb3J0IHsgVHJhbnNsYXRlU2VydmljZSB9IGZyb20gJ0BuZ3gtdHJhbnNsYXRlL2NvcmUnO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSAnLi4vdXRpbHMvY29tbW9uLnNlcnZpY2UnO1xyXG5cclxuQENvbXBvbmVudCgge1xyXG4gICAgc2VsZWN0b3I6ICdTd3RHcm91cGVkVG90YWxDb21tb25HcmlkJyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICA8YW5ndWxhci1zbGlja2dyaWRcclxuICAgIGNsYXNzPVwiY29tbW9uU2xpY2tHcmlkXCJcclxuICAgICAgICAgICAgI2FuZ3VsYXJTbGlja0dyaWRcclxuICAgICAgICAgICAgZ3JpZElkPSdncmlkLXt7aWR9fSdcclxuICAgICAgICAgICAgKG9uRGF0YXZpZXdDcmVhdGVkKT1cImRhdGF2aWV3UmVhZHkoJGV2ZW50KVwiXHJcbiAgICAgICAgICAgIChvbkFuZ3VsYXJHcmlkQ3JlYXRlZCk9XCJvbkFuZ3VsYXJHcmlkQ3JlYXRlZCgkZXZlbnQpXCJcclxuICAgICAgICAgICAgW2NvbHVtbkRlZmluaXRpb25zXT1cImNvbHVtbkRlZmluaXRpb25zXCJcclxuICAgICAgICAgICAgW2dyaWRPcHRpb25zXT1cImdyaWRPcHRpb25zXCJcclxuICAgICAgICAgICAgZ3JpZEhlaWdodD1cIjEwMCVcIlxyXG4gICAgICAgICAgICBncmlkV2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgW2RhdGFzZXRdPVwiZGF0YXNldFwiXHJcbiAgICA+XHJcbiAgICA8L2FuZ3VsYXItc2xpY2tncmlkPlxyXG5cclxuYCxcclxuc3R5bGVzOiBbYFxyXG4gICAgLmdyaWRDb250ZW50e1xyXG4gICAgICAgIG1pbi13aWR0aDogMzAwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgfVxyXG4gICAgOmhvc3QgOjpuZy1kZWVwIC5ncmlkUGFuZSB7XHJcbiAgICAgICAgb3ZlcmZsb3c6IGF1dG87XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB9XHJcbiAgICA6aG9zdCA6Om5nLWRlZXAgLnNsaWNrZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgIG1pbi1oZWlnaHQ6IDEwMCU7XHJcbiAgICB9XHJcblxyXG5cclxuYF0sXHJcbnByb3ZpZGVyczogW1xyXG4gICAgVHJhbnNsYXRlU2VydmljZSxcclxuICAgIEV4dGVuc2lvblNlcnZpY2UsXHJcbiAgICBBdXRvVG9vbHRpcEV4dGVuc2lvbixcclxuICAgIEV4dGVuc2lvblV0aWxpdHksXHJcbiAgICBTaGFyZWRTZXJ2aWNlLFxyXG4gICAgQ29sbGVjdGlvblNlcnZpY2VcclxuXSxcclxufSApXHJcblxyXG5leHBvcnQgY2xhc3MgU3d0R3JvdXBlZFRvdGFsQ29tbW9uR3JpZCBleHRlbmRzIFN3dENvbW1vbkdyaWQgaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQgLCBPbkRlc3Ryb3kge1xyXG4gICBcclxuICAgIGNvbnN0cnVjdG9yKCBwcm90ZWN0ZWQgZWw6IEVsZW1lbnRSZWYsIHByb3RlY3RlZCBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlICwgcHJvdGVjdGVkIGF1dG9Ub29sdGlwRXh0ZW5zaW9uIDogQXV0b1Rvb2x0aXBFeHRlbnNpb24sIHByb3RlY3RlZCBleHRlbnNpb25VdGlsaXR5IDpFeHRlbnNpb25VdGlsaXR5LCBwcm90ZWN0ZWQgc2hhcmVkU2VydmljZSA6IFNoYXJlZFNlcnZpY2VcclxuICAgICAgICAsY29sbGVjdGlvblNlcnZpY2UgOiBDb2xsZWN0aW9uU2VydmljZSAsIHByb3RlY3RlZCB0cmFuc2xhdGU6IFRyYW5zbGF0ZVNlcnZpY2UpIHtcclxuICAgICAgICBzdXBlcihlbCxjb21tb25TZXJ2aWNlLGF1dG9Ub29sdGlwRXh0ZW5zaW9uLCBleHRlbnNpb25VdGlsaXR5LHNoYXJlZFNlcnZpY2UsIGNvbGxlY3Rpb25TZXJ2aWNlLCB0cmFuc2xhdGUpO1xyXG4gICAgICAgIHRoaXMuc2hvd0hlYWRlciA9IGZhbHNlO1xyXG4gICAgICAgIHRoaXMuaXNHcm91cGVkSGVhZGVyR3JpZCA9IGZhbHNlO1xyXG4gICAgICAgIHRoaXMuaXNUb3RhbEdyaWQgPSB0cnVlO1xyXG4gICAgIH1cclxuICBcclxuIFxyXG4gICAgIFxyXG4gICAgIHNldCBpbml0aWFsQ29sdW1uc1RvU2tpcCAoIHZhbHVlOiBudW1iZXIgKSB7XHJcbiAgICAgICAgIHRoaXMuX2luaXRpYWxDb2x1bW5zVG9Ta2lwID0gdmFsdWU7XHJcbiAgICAgfVxyXG4gXHJcbiAgICAgZ2V0IGluaXRpYWxDb2x1bW5zVG9Ta2lwICgpIHtcclxuICAgICAgICAgcmV0dXJuIHRoaXMuX2luaXRpYWxDb2x1bW5zVG9Ta2lwIDtcclxuICAgICB9XHJcbn1cclxuIl19