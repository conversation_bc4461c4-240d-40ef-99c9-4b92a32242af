import { CommonService } from '../utils/common.service';
import { Container } from "../containers/swt-container.component";
import 'jquery-ui-dist/jquery-ui';
import { ElementRef, OnInit } from "@angular/core";
export declare class SwtTextInput extends Container implements OnInit {
    private elem;
    private commonService;
    textfieldDOM: ElementRef;
    private _textAlign;
    private _tabIndex;
    private _displayAsPassword;
    private _editable;
    private _dataProvider;
    private _text;
    private _required;
    required: any;
    textAlign: any;
    tabIndex: any;
    styleName: any;
    displayAsPassword: any;
    editable: any;
    text: any;
    dataProvider: any[];
    /**
     * constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
    ngOnInit(): void;
    /**
     * onPaste
     * @param event
     */
    onPaste(event: any): void;
    /**
     * focus
     * @param event
     */
    setFocusAndSelect(): void;
}
