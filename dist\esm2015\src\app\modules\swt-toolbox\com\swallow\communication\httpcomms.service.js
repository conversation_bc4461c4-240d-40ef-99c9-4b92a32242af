/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { HttpHeaders, HttpParams } from '@angular/common/http';
import { RemoteTransaction } from './RemoteTransaction';
import { SwtAlert } from '../utils/swt-alert.service';
import { Alert } from '../utils/alert.component';
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { Logger } from "../logging/logger.service";
import { CommonService } from "../utils/common.service";
import { saveAs } from 'file-saver/FileSaver';
import { Observable } from 'rxjs';
export class HTTPComms {
    //////////////////////////////////////////////////////////////////////////////////////////
    /**
     * @param {?} common
     */
    constructor(common) {
        this.common = common;
        // Callback functions
        this.cbFault = new Function();
        this.cbResult = new Function();
        this.cbStart = new Function();
        this.cbStop = new Function();
        this.headers = new HttpHeaders();
        this.stopTimers = new Function();
        this.busy = false;
        this.encode = true;
        this._start = 0;
        this._result = 0;
        this._localStart = 0;
        this.alertFlag = false;
        this._cancelRequest = false;
        this.screenUniqueTransactionId_ = null;
        this._responsetype = '?response=json';
        this.jsonReader = new JSONReader();
        this._profling = false;
        //////////////////////////////////////////////////////////////////////////////////////////
        //                               HTTPCOMMS CONSTRUCTOR                                  //
        this._serverDelay = 0;
        this._localDelay = 0;
        this.encodeURL = false;
        this._method = 'POST';
        this.logger = new Logger('HTTPComms', this.common.httpclient);
        this.swtAlert = new SwtAlert(common);
    }
    /**
     * Get profiling condition
     *
     * @return {?}
     */
    get profling() {
        return this._profling;
    }
    /**
     * Set profiling condition
     * @param {?} value
     *
     * @return {?}
     */
    set profling(value) {
        this._profling = value;
        this._start = this.getTimer();
        this._localDelay = 0;
        this._serverDelay = 0;
    }
    /**
     * Get delay of last HTTP request/response
     *
     * @return {?}
     */
    get serverDelay() {
        return this._serverDelay;
    }
    /**
     * @return {?}
     */
    get localDelay() {
        return this._localDelay;
    }
    /**
     * @return {?}
     */
    get url() {
        return this._url;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set url(value) {
        this._url = value;
    }
    /**
     * @return {?}
     */
    get encodeURL() {
        return this.encode;
    }
    /**
     * @param {?} encode
     * @return {?}
     */
    set encodeURL(encode) {
        this.encode = encode;
    }
    /**
     * Overrides the main send function so that the busy flag is set
     * The start callback function is called
     * and
     * Then sending the data, encoding it - So be aware that it needs to be decoded at the server end
     * @param {?=} parameters
     * @param {?=} json
     * @return {?}
     */
    send(parameters = null, json) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            // check if parameter is null or undefined and remove it.
            for (var attr in parameters) {
                if (parameters[attr] === undefined || parameters[attr] == null) {
                    delete parameters[attr];
                }
            }
            // check if URL contain parameters.
            // var qm:number= this.url.lastIndexOf("?");
            // if (qm !== -1) {
            //     errorLocation=10;
            //     var query:string= this.url.substr(qm + 1);
            //     var params = query.split("&");
            //     for (var i = 0; i < params.length; i++) {
            //         errorLocation=20;
            //         var param:string=params[i];
            //         var nameValue = param.split("=");
            //         if (nameValue.length === 2) {
            //             errorLocation=30;
            //             var key = nameValue[0];
            //             var val = nameValue[1];
            //             parameters[key] = val;
            //         }
            //     }
            // }
            if (!this.busy) {
                this.busy = true;
                this.onStart();
                // If screen unique transactionId is set, then send it as a parameter to the server
                if (this.screenUniqueTransactionId_ && parameters) {
                    parameters["screenUniqueTransactionId"] = this.screenUniqueTransactionId_;
                }
                // If encode is enabled then encode the parameters, then send
                if (this.encode) {
                    this.sendRequest(this.encodeData(parameters), json);
                }
                else {
                    /** @type {?} */
                    const dNoCache = new Date();
                    if (parameters) {
                        parameters.nocache = dNoCache.getTime().toString();
                    }
                    this.sendRequest(parameters, json);
                }
            }
            else {
                this.cancel();
            }
        }
        catch (error) {
            this.logger.error("[ send ] METHOD ERROR:", error);
        }
    }
    /**
     * @param {?=} id
     * @return {?}
     */
    cancel(id = null) {
        this.onFinish();
        this._cancelRequest = true;
    }
    /**
     *  Use this function to send arrays to the server
     * @param {?} params
     * @param {?} name
     * @param {?} needQ
     * @return {?}
     */
    arraySend(params, name, needQ) {
        this.originalURL = this.url;
        this.url += needQ ? '?' : '';
        this.url += this.arrayToGetParams(params, name);
        this.busy = true;
        // Alert.show(""+this.url);
        this.send();
        this.url = this.originalURL;
        this.originalURL = '';
    }
    //////////////////////////////////////////////////////////////////////////////////////////
    /**
     *  Use to check the state of the request
     * @return {?}
     */
    isBusy() {
        return this.busy;
    }
    /**
     * Reset the transaction unique Id by setting it to null value
     *
     * @return {?}
     */
    resetTransactionUId() {
        this.screenUniqueTransactionId_ = null;
    }
    //////////////////////////////////////////////////////////////////////////////////////////
    //                            HTTPCOMMS GETTER AND SETTER                               //
    /**
     * @return {?}
     */
    getTransactionUId() {
        return this.screenUniqueTransactionId_;
    }
    /**
     * Sets the screen transaction unique Id
     * @param {?} programId
     * @param {?} uniqueIdentifier
     * @return {?}
     */
    setTransactionUId(programId, uniqueIdentifier) {
        this.screenUniqueTransactionId_ = programId + ':' + ((uniqueIdentifier != null) ? uniqueIdentifier : '');
    }
    /**
     * Remotely start a transaction
     * @param {?} programId
     * @param {?=} uniqueIdentifier
     * @return {?}
     */
    startRemoteTransaction(programId, uniqueIdentifier = null) {
        /** @type {?} */
        const trx = new RemoteTransaction(this, programId, uniqueIdentifier);
        trx.start();
        return trx;
    }
    /*
        ===========================================================================|
        Aded by Khalil.B to to send transactions Requests                          |
        ===========================================================================|
        */
    /**
     * @param {?} url
     * @return {?}
     */
    sendTransaction(url) {
        try {
            /** @type {?} */
            let reponseType = '&response=json';
            /** @type {?} */
            let headers = new HttpHeaders();
            headers = headers.append('Content-Type', 'application/x-www-form-urlencoded; charset=utf-8');
            headers = headers.append('Accept', 'application/json, text/plain, */*');
            /** @type {?} */
            let body = new HttpParams();
            return Observable.create((/**
             * @param {?} observer
             * @return {?}
             */
            observer => {
                this.common.httpclient.post(url + reponseType, body, { headers: headers })
                    .subscribe((/**
                 * @param {?} data
                 * @return {?}
                 */
                data => {
                    observer.next(data);
                }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                error => this.fault(error)));
            }));
        }
        catch (error) {
            console.error("sendTransaction ERROR: ", error);
        }
    }
    // Start : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    result(event) {
        this.onFinish();
        try {
            if (event != null && event !== '') {
                if (this.jsonReader.getRequestReplyMessage() !== undefined
                    && this.jsonReader.getRequestReplyMessage() === 'session.expired') {
                    if (!this.alertFlag) {
                        this.alertFlag = true;
                        this.swtAlert.warning('Your session has been timed-out due to inactivity. Click OK to log in', 'Warning', Alert.OK, this.showLogon);
                        this.stopTimers();
                    }
                    else if (this.jsonReader.getRequestReplyMessage() !== undefined
                        && this.jsonReader.getRequestReplyMessage() === 'alert.sessionKilled.sceen') {
                        /*  SwtAlert.info(StringUtil.
                         * substitute(SwtUtil.getCommonMessages('alert.sessionKilled.sceen'),(event.result as XML).@programName)
                                  , SwtUtil.getCommonMessages('alert_header.warning'),null,null);*/
                        this.swtAlert.error('Session killed', 'Session killed', Alert.OK, this.showLogon);
                    }
                    else {
                        if (this.jsonReader.getRequestReplyStatus() === false) {
                            // Temporarly display error message
                            this.logger.error("[ result ] method : request replay message :", event.request_reply.message, " | ", event.message);
                        }
                        if (this.profling) {
                            this._localStart = this.getTimer();
                        }
                        this.cbResult(event);
                        if (this.profling) {
                            this._localDelay = this.getTimer() - this._localStart;
                        }
                    }
                }
                else {
                    if (this.profling) {
                        this._localStart = this.getTimer();
                    }
                    this.cbResult(event);
                    if (this.profling) {
                        this._localDelay = this.getTimer() - this._localStart;
                    }
                }
            }
            else {
                //Added By Seif Boubakri :
                //I add this code to fix the problem of return an empty response in Scheduler Screen.
                //Begin
                if (this.profling) {
                    this._localStart = this.getTimer();
                }
                this.cbResult(event);
                if (this.profling) {
                    this._localDelay = this.getTimer() - this._localStart;
                }
                //End
                //Commented as some request does not need to have a response message only status "200" is required
                //this.logger.error('Server side error, empty response is received.. See server logs');
            }
        }
        catch (error) {
            console.error("result error", error);
        }
    }
    // Start : Added to give custom message for fault event of a request by chiheb on 25 Jan 2018
    /**
     * @private
     * @return {?}
     */
    showLogon() {
        // TODO
    }
    // End : Modified to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    fault(event) {
        this.logger.error('url  = ' + this._url + '   System Fault: ' + event.message);
        this.onFinish();
        this.cbFault(event);
    }
    // End : Added to give custom message for fault event of a request by chiheb on 18/01/2018
    /**
     * @private
     * @return {?}
     */
    onStart() {
        if (this._profling) {
            this._start = this.getTimer();
        }
        this.cbStart();
    }
    /**
     * @private
     * @param {?} result
     * @return {?}
     */
    getResultObject(result) {
        /** @type {?} */
        let rootnode = '';
        /** @type {?} */
        let key;
        for (key in result) {
            if (result.hasOwnProperty(key)) {
                rootnode = key;
                break;
            }
        }
        return result[rootnode];
    }
    /**
     *  Function encodes the parameters that are passed
     * @private
     * @param {?} data
     * @return {?}
     */
    encodeData(data) {
        /** @type {?} */
        const rtn = new Object();
        // Variable for errorLocation
        /** @type {?} */
        let errorLocation = 0;
        try {
            errorLocation = 10;
            if (data) {
                errorLocation = 20;
                /** @type {?} */
                const dNoCache = new Date();
                /** @type {?} */
                let variable;
                /** @type {?} */
                let name;
                data.nocache = dNoCache.getTime().toString();
                for (name in data) {
                    if (data.hasOwnProperty(name)) {
                        errorLocation = 30;
                        variable = encodeURIComponent(data[name]);
                        rtn[name] = variable.toString();
                    }
                }
            }
            errorLocation = 40;
        }
        catch (error) {
            //          SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".as", "encodeData", errorLocation);
        }
        return rtn;
    }
    /**
     *  Executes an HTTPService request. The parameters are optional, but if specified should
     *  be an Object containing name-value pairs or an XML object depending on the <code>contentType</code>.
     * @private
     * @param {?=} params
     * @param {?=} json
     * @return {?} An object representing the asynchronous completion token. It is the same object
     *  available in the <code>result</code> or <code>fault</code> event's <code>token</code> property.
     */
    sendRequest(params = new Object(), json) {
        try {
            /** @type {?} */
            let body = new HttpParams();
            /** @type {?} */
            let param;
            /** @type {?} */
            let reponseType = this._responsetype;
            /*response=json*/
            // TODO: it may be a case where no parameters are defined => ?response=json should be used instead of &response=json
            if (json === false) {
                reponseType = '&response=binary';
            }
            if (this._url.indexOf("?") !== -1) {
                reponseType = '&response=json';
            }
            for (param in params) {
                if (params.hasOwnProperty(param)) {
                    body = body.append(param, params[param].toString().replace(/\+/g, "%2B"));
                }
            }
            if (json === false) {
                this.common.httpclient.get(this._url + reponseType, { responseType: 'blob', observe: 'response' })
                    .subscribe((/**
                 * @param {?} response
                 * @return {?}
                 */
                response => {
                    /** @type {?} */
                    let contentDispositionHeader = response.headers.get('Content-Disposition');
                    /** @type {?} */
                    let parts = contentDispositionHeader.split(';');
                    /** @type {?} */
                    let filename = parts[1].split('=')[1];
                    /** @type {?} */
                    let blob = new Blob([((/** @type {?} */ (response.body)))], { type: 'application/octet-stream' });
                    saveAs(blob, filename);
                    // Added by Rihab JABALLAH to handle the finish of the report by the progress bar.
                    this.onFinish();
                }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                error => {
                    this.logger.error('Blob repose error !', error);
                }));
            }
            else {
                this.common.httpclient.post(this._url + reponseType, body)
                    .subscribe((/**
                 * @param {?} data
                 * @return {?}
                 */
                data => this.result(data)), (/**
                 * @param {?} error
                 * @return {?}
                 */
                error => this.fault(error)));
            }
        }
        catch (error) {
            console.error("[ sendRequest ] METHOD ERROR:", error);
        }
    }
    /**
     * @private
     * @return {?}
     */
    onFinish() {
        this.busy = false;
        this.cbStop();
        if (this._profling) {
            this._serverDelay = this.getTimer() - this._start;
        }
    }
    /**
     *  Use when sending an array it needs to be split
     * @private
     * @param {?} array
     * @param {?} name
     * @return {?}
     */
    arrayToGetParams(array, name) {
        return name + '[]=' + array.join('&' + name + '[]=');
    }
    /**
     * @private
     * @return {?}
     */
    getTimer() {
        return new Date().getTime();
    }
}
HTTPComms.decorators = [
    { type: Injectable }
];
/** @nocollapse */
HTTPComms.ctorParameters = () => [
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    HTTPComms.prototype.cbFault;
    /** @type {?} */
    HTTPComms.prototype.cbResult;
    /** @type {?} */
    HTTPComms.prototype.cbStart;
    /** @type {?} */
    HTTPComms.prototype.cbStop;
    /** @type {?} */
    HTTPComms.prototype.headers;
    /** @type {?} */
    HTTPComms.prototype.stopTimers;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.originalURL;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.busy;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.encode;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._start;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._result;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._method;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._localStart;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.alertFlag;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._cancelRequest;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.screenUniqueTransactionId_;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._responsetype;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._profling;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._serverDelay;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._localDelay;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype._url;
    /**
     * @type {?}
     * @private
     */
    HTTPComms.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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