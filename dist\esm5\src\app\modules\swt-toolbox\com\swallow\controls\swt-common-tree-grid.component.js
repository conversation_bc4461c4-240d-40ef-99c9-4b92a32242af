/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
var SwtTreeCommonGrid = /** @class */ (function (_super) {
    tslib_1.__extends(SwtTreeCommonGrid, _super);
    function SwtTreeCommonGrid(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        var _this = _super.call(this, el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) || this;
        _this.el = el;
        _this.commonService = commonService;
        _this.autoTooltipExtension = autoTooltipExtension;
        _this.extensionUtility = extensionUtility;
        _this.sharedService = sharedService;
        _this.translate = translate;
        _this.isTreeGrid = true;
        return _this;
    }
    SwtTreeCommonGrid.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTreeCommonGrid',
                    template: "\n    <angular-slickgrid\n            #angularSlickGrid\n            class=\"commonSlickGrid\"\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n",
                    providers: [
                        TranslateService,
                        ExtensionService,
                        AutoTooltipExtension,
                        ExtensionUtility,
                        SharedService,
                        CollectionService
                    ],
                    styles: ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"]
                }] }
    ];
    /** @nocollapse */
    SwtTreeCommonGrid.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: AutoTooltipExtension },
        { type: ExtensionUtility },
        { type: SharedService },
        { type: CollectionService },
        { type: TranslateService }
    ]; };
    return SwtTreeCommonGrid;
}(SwtCommonGrid));
export { SwtTreeCommonGrid };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,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