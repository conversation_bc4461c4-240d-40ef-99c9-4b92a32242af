/**
 * This class define the JSON response typing;
 */
export declare class JSONResponse {
    request_reply: RequestReply;
    selects: Selects;
    sysitem: string;
    singletons: Singletons;
}
/**
 * This class define a root object typing.
 */
export declare class RootObject {
    menus: Menus;
    name: string;
    value: JSONResponse;
}
/**
 * This class define the request relay typing.
 */
export declare class ProcessInfo {
    process_status: string;
    running_seqnbr: string;
}
/**
 * This class define the request relay typing.
 */
export declare class RequestReply {
    message: string;
    status_ok: string;
}
/**
 * This class define selects typing.
 */
export declare class Selects {
    select: Select[];
}
/**
 * This class define the Select typing.
 */
export declare class Select {
    id: string;
    option: Option[];
}
/**
 * This class define combo box option
 */
export declare class Option {
    content: string;
    selected: number;
    value: string;
    type: string;
}
export declare class Menus {
    favourites: Favourites;
    request_reply: RequestReply;
    alert: Alert;
    about: About;
    singletons: Singletons;
    modules: Modules;
}
export declare class Favourites {
    favourite: Favourite[];
}
export declare class Alert {
    itemid: number;
    menuaccess: number;
    undock: string;
    label: string;
    programid: number;
    url: string;
}
export declare class About {
    itemid: number;
}
export declare class Singletons {
    rolename: string;
    alertCounter: string;
    hostid: string;
    userid: string;
}
export declare class Modules {
    module: Module[];
}
export declare class Favourite {
    itemid: number;
    menuaccess: number;
    icon: string;
    undock: string;
    label: string;
    menuaction: string;
    screenname: string;
    moduleid: string;
    programid: number;
}
export declare class Module {
    icon: string;
    id: string;
    label: string;
    menu: Menu[];
}
export declare class Menu {
    label: string;
    menuitem: MenuItem[];
}
export declare class MenuItem {
    itemid: number;
    menuaccess: number;
    width: number;
    undock: string;
    label: string;
    menuaction: string;
    screenname: string;
    moduleid: string;
    programid: number;
    menuOrderGroup: number;
    height: number;
    type: string;
    menuitem: MenuItem[];
    tabIndex: number;
}
export declare class TabItem {
    icon: string;
    itemid: number;
    menuaccess: number;
    width: number;
    undock: string;
    label: string;
    menuaction: string;
    screenname: string;
    moduleid: string;
    programid: number;
    menuOrderGroup: number;
    height: number;
    type: string;
    menuitem: MenuItem[];
    active: boolean;
    disabled: boolean;
    tabIndex: number;
}
export declare class UserData {
    currentEntity: string;
    baseUrl: string;
    localeDir: string;
    userid: string;
    languageid: string;
    systemLanguage: string;
    request_reply: RequestReply;
}
