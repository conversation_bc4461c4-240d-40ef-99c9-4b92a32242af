import { OnInit, ElementRef } from '@angular/core';
import { SwtButton } from "./swt-button.component";
import { CommonService } from "../utils/common.service";
export declare class SwtHelpButton extends SwtButton implements OnInit {
    private eleme;
    private commonService_;
    moduleId: any;
    toolTip: string;
    helpFile: string;
    /**
     * constructor
     * @param eleme
     * @param commonService_
     */
    constructor(eleme: ElementRef, commonService_: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     *  @param event: Event
     * This is a event handler,used to open the help  window
     */
    doHelp(): void;
}
