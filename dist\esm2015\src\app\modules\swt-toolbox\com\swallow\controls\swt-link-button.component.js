/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { SwtButton } from "./swt-button.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input } from "@angular/core";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class LinkButton extends SwtButton {
    /**
     * @param {?} eleme
     * @param {?} commonService_
     */
    constructor(eleme, commonService_) {
        super(eleme, commonService_);
        this.eleme = eleme;
        this.commonService_ = commonService_;
        $($(this.eleme.nativeElement)[0]).attr('selector', 'SwtHelpButton');
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        /** @type {?} */
        var element = $($(this.swtbutton)[0].nativeElement).find('.buttonLabel');
        if (element.length > 0) {
            $(element).text(value);
        }
        this._label = value;
    }
    /**
     * @return {?}
     */
    get label() {
        return this._label;
    }
}
LinkButton.decorators = [
    { type: Component, args: [{
                selector: 'LinkButton',
                template: `
       <div selector="LinkButton"
            #swtbutton  
            class="minWidthBtn linkbutton">
            <span  class="truncate buttonLabel" ></span>
       </div>
  `,
                styles: [`
            :host{
                outline:none;
            }
           .disabledLinkButton{
                color: #ABB5B5!important;
                user-select: none;
                pointer-events: none;
                outline: none;
           }
  `]
            }] }
];
/** @nocollapse */
LinkButton.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
LinkButton.propDecorators = {
    label: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    LinkButton.prototype.eleme;
    /**
     * @type {?}
     * @private
     */
    LinkButton.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWxpbmstYnV0dG9uLmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9zd3QtbGluay1idXR0b24uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsU0FBUyxFQUFFLE1BQU0sd0JBQXdCLENBQUM7QUFHbkQsT0FBTyxFQUFFLGFBQWEsRUFBRSxNQUFNLHlCQUF5QixDQUFDO0FBQ3hELE9BQU8sRUFBRSxTQUFTLEVBQUcsVUFBVSxFQUFFLEtBQUssRUFBRSxNQUFNLGVBQWUsQ0FBQzs7O01BSXhELENBQUMsR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFDO0FBd0IzQixNQUFNLE9BQU8sVUFBVyxTQUFRLFNBQVM7Ozs7O0lBRXJDLFlBQW9CLEtBQWtCLEVBQVUsY0FBNkI7UUFDekUsS0FBSyxDQUFDLEtBQUssRUFBRSxjQUFjLENBQUMsQ0FBQztRQURiLFVBQUssR0FBTCxLQUFLLENBQWE7UUFBVSxtQkFBYyxHQUFkLGNBQWMsQ0FBZTtRQUV6RSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLGVBQWUsQ0FBQyxDQUFBO0lBQ3ZFLENBQUM7Ozs7O0lBRUQsSUFDSSxLQUFLLENBQUMsS0FBSzs7WUFDUixPQUFPLEdBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQztRQUN2RSxJQUFHLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFDO1lBQ2xCLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDekI7UUFDQSxJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtJQUN2QixDQUFDOzs7O0lBQ0QsSUFBSSxLQUFLO1FBQ0wsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFBO0lBQ3RCLENBQUM7OztZQXZDSixTQUFTLFNBQUM7Z0JBQ1QsUUFBUSxFQUFFLFlBQVk7Z0JBQ3RCLFFBQVEsRUFBRTs7Ozs7O0dBTVQ7eUJBQ1E7Ozs7Ozs7Ozs7R0FVUjthQUVGOzs7O1lBM0JvQixVQUFVO1lBRHRCLGFBQWE7OztvQkFvQ2pCLEtBQUs7Ozs7Ozs7SUFMTSwyQkFBMEI7Ozs7O0lBQUUsb0NBQXFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3d0QnV0dG9uIH0gZnJvbSBcIi4vc3d0LWJ1dHRvbi5jb21wb25lbnRcIjtcclxuaW1wb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSBcIi4uL21hbmFnZXJzL2ZvY3VzLW1hbmFnZXIuc2VydmljZVwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IENvbXBvbmVudCwgIEVsZW1lbnRSZWYsIElucHV0IH0gZnJvbSBcIkBhbmd1bGFyL2NvcmVcIjtcclxuXHJcbmRlY2xhcmUgdmFyIHJlcXVpcmU6IGFueTtcclxuLy9pbXBvcnQgJCBmcm9tICdqcXVlcnknO1xyXG5jb25zdCAkID0gcmVxdWlyZSgnanF1ZXJ5Jyk7XHJcblxyXG5AQ29tcG9uZW50KHtcclxuICBzZWxlY3RvcjogJ0xpbmtCdXR0b24nLFxyXG4gIHRlbXBsYXRlOiBgXHJcbiAgICAgICA8ZGl2IHNlbGVjdG9yPVwiTGlua0J1dHRvblwiXHJcbiAgICAgICAgICAgICNzd3RidXR0b24gIFxyXG4gICAgICAgICAgICBjbGFzcz1cIm1pbldpZHRoQnRuIGxpbmtidXR0b25cIj5cclxuICAgICAgICAgICAgPHNwYW4gIGNsYXNzPVwidHJ1bmNhdGUgYnV0dG9uTGFiZWxcIiA+PC9zcGFuPlxyXG4gICAgICAgPC9kaXY+XHJcbiAgYCxcclxuICBzdHlsZXM6IFtgXHJcbiAgICAgICAgICAgIDpob3N0e1xyXG4gICAgICAgICAgICAgICAgb3V0bGluZTpub25lO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgLmRpc2FibGVkTGlua0J1dHRvbntcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjQUJCNUI1IWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIHVzZXItc2VsZWN0OiBub25lO1xyXG4gICAgICAgICAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgICAgIH1cclxuICBgXVxyXG4gXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBMaW5rQnV0dG9uIGV4dGVuZHMgU3d0QnV0dG9uICAge1xyXG4gICAgXHJcbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIGVsZW1lIDogRWxlbWVudFJlZiwgcHJpdmF0ZSBjb21tb25TZXJ2aWNlXzogQ29tbW9uU2VydmljZSkge1xyXG4gICAgICAgIHN1cGVyKGVsZW1lLCBjb21tb25TZXJ2aWNlXyk7XHJcbiAgICAgICAgJCgkKHRoaXMuZWxlbWUubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoJ3NlbGVjdG9yJywgJ1N3dEhlbHBCdXR0b24nKVxyXG4gICAgfVxyXG4gICAgIFxyXG4gICAgQElucHV0KClcclxuICAgIHNldCBsYWJlbCh2YWx1ZSkge1xyXG4gICAgICAgdmFyIGVsZW1lbnQgPSQoJCh0aGlzLnN3dGJ1dHRvbilbMF0ubmF0aXZlRWxlbWVudCkuZmluZCgnLmJ1dHRvbkxhYmVsJyk7XHJcbiAgICAgICBpZihlbGVtZW50Lmxlbmd0aCA+IDApe1xyXG4gICAgICAgICAgICQoZWxlbWVudCkudGV4dCh2YWx1ZSlcclxuICAgICAgIH1cclxuICAgICAgICB0aGlzLl9sYWJlbCA9IHZhbHVlXHJcbiAgICB9XHJcbiAgICBnZXQgbGFiZWwoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2xhYmVsXHJcbiAgICB9XHJcbiAgIFxyXG59XHJcbiJdfQ==