import { <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, OnInit, ElementRef, EventEmitter, Injector, ComponentFactoryResolver } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class HDividedBox extends Container implements OnInit, AfterViewInit, OnDestroy {
    private elem;
    private commonService;
    private injector;
    private componentFactoryResolver;
    DIVIDER_DRAG_COMPLETE: EventEmitter<any>;
    DIVIDER_BUTTON_CLICK: EventEmitter<any>;
    private _resize;
    private _resizeStart;
    private _resizeStop;
    private _height;
    private _width;
    private _widthRight;
    private _widthLeft;
    widthLeftPixel: any;
    widthRightPixel: any;
    private _dividersAnimation;
    private _extendedDividedBox;
    private doResize;
    componentRef: any;
    private RightContent;
    private leftContent;
    private _liveDrag;
    private startDrag;
    private lastValidValue;
    private _maxWidthRight;
    private _minWidthRight;
    private _maxWidthLeft;
    private _minWidthLeft;
    maxWidthRight: any;
    minWidthRight: any;
    maxWidthLeft: any;
    minWidthLeft: any;
    hdividedboxContainer: ElementRef;
    panelLeft: ElementRef;
    panelRight: ElementRef;
    splitter: ElementRef;
    private resize_;
    private resizeStart_;
    private resizeStop_;
    prevLeftWidth: any;
    _prevRightWidth: any;
    private defaultIcon;
    private hdividerClosed;
    private hdividerOpened;
    resize: Function;
    prevRightWidth: any;
    resizeStart: Function;
    resizeStop: Function;
    height: string;
    width: string;
    onButtonClickHandler(): void;
    widthRight: string;
    private forceNoEvent;
    setWidthRightWithoutEvent(value: string): void;
    setWidthLeftWithoutEvent(value: string): void;
    dividersAnimation: string;
    extendedDividedBox: any;
    liveDrag: any;
    widthLeft: string;
    /**
     * constructor
     * @param elem
     * @param commonService
     * @param _rendred
     */
    constructor(elem: ElementRef, commonService: CommonService, injector: Injector, componentFactoryResolver: ComponentFactoryResolver);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     * ngAfterViewInit
     */
    ngAfterViewInit(): void;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
