/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
export class InputTextEditor {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.validationResult = true;
        this.logger = null;
        //public static commonGrid :any;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.commonGrid = this.args.column.params.grid;
        this.columnDef = this.args.column;
        this.maxChars = this.columnDef.maxChars;
        this.logger = new Logger('InputTextEditor', null, 0);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    init() {
        if (this.columnDef.params.enableDisableCells) {
            this.enableFlag = this.columnDef.params.enableDisableCells(this.args.item, this.args.column.field);
        }
        else {
            this.enableFlag = true;
        }
        if (this.columnDef['properties'] && this.columnDef['properties']['enabled']) {
            this.enableFlag = this.columnDef['properties']['enabled'];
        }
        this.logger.info('method [init] -START- enableFlag :', this.enableFlag);
        this.loadValue(this.args.item);
        if (this.showHideCells) {
            if (this.args.item.property && this.args.item.property.toLowerCase().indexOf("password") != -1) {
                this.$input = $(`<input id="input" type="password"   value="${this.defaultValue}" ></input>`);
            }
            else
                this.$input = $(`<input  id="input" class="text-style renderAsInput  ${(this.enableFlag == false) ? 'disabled-text-editor' : ''}" type="text" style="${(this.enableFlag == true) ? 'background-color: white !important;' : 'background-color: rgba(255, 204, 102, 1) !important;'}"    value="${this.defaultValue}" ></input>`);
        }
        else {
            this.$input = $(``);
        }
        this.$input.appendTo(this.args.container);
        if (this.enableFlag) {
            this.$input.focus();
            this.$input.select();
        }
        this.$input.keypress((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (!document.getSelection().toString() && document.getElementById("input")['selectionStart'] == document.getElementById("input")['selectionEnd'] && event.target.value.length >= this.maxChars)
                return false;
        }));
        this.$input.focusout((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (event.handleObj.type == "focusout") {
                this.destroy();
                this.commonGrid.gridObj.getEditorLock().commitCurrentEdit();
            }
        }));
        /** @type {?} */
        var target = {
            name: this.args.column.name,
            field: this.args.column.field,
            editor: this.args.column.editor,
            formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
            data: this.args.item
        };
        /** @type {?} */
        var ListEvent = {
            rowIndex: this.args.item.id,
            cellIndex: this.args.column.columnorder,
            columnIndex: this.args.column.columnorder,
            target: target
        };
        if (this.commonGrid.ITEM_FOCUS_IN.observers.length > 1) {
            /** @type {?} */
            var x = this.commonGrid.ITEM_FOCUS_IN.observers[0];
            this.commonGrid.ITEM_FOCUS_IN.observers = [];
            this.commonGrid.ITEM_FOCUS_IN.observers[0] = x;
        }
        this.commonGrid.ITEM_FOCUS_IN.emit(ListEvent);
        this.logger.info('method [init] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    destroy() {
        this.logger.info('method [destroy] -START-');
        this.applyValue(this.args.item, this.getValue());
        this.isValueChanged();
        if (this.showHideCells) {
            // focusout event .
            /** @type {?} */
            var target = {
                name: this.args.column.name,
                field: this.args.column.field,
                editor: this.args.column.editor,
                formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                data: this.args.item
            };
            /** @type {?} */
            var ListEvent = {
                rowIndex: this.args.item.id,
                cellIndex: this.args.column.columnorder,
                columnIndex: this.args.column.columnorder,
                target: target
            };
            if (this.commonGrid.ITEM_FOCUS_OUT.observers.length > 1) {
                /** @type {?} */
                var x = this.commonGrid.ITEM_FOCUS_OUT.observers[0];
                this.commonGrid.ITEM_FOCUS_OUT.observers = [];
                this.commonGrid.ITEM_FOCUS_OUT.observers[0] = x;
            }
            this.commonGrid.ITEM_FOCUS_OUT.emit(ListEvent);
            // if(this.$input) this.$input.remove();
        }
        this.logger.info('method [destroy] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    focus() {
        this.logger.info('method [focus] -START/END-');
        this.$input.focus();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    getValue() {
        this.logger.info('method [getValue] -START/END-');
        return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    setValue(val) {
        this.logger.info('method [setValue] -START/END-');
        this.$input.val(val);
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('method [loadValue] -START-');
        if (this.showHideCells) {
            this.defaultValue = item[this.args.column.field] != undefined ? item[this.args.column.field] : '';
            if (this.commonGrid.originalDataprovider && this.commonGrid.originalDataprovider.size > 0)
                this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']] != undefined ? this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field] : "";
        }
        this.logger.info('method [loadValue] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    save() {
        this.logger.info('method [save] - START/END');
        if (this.showHideCells) {
            this.args.commitChanges();
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    serializeValue() {
        this.logger.info('method [serializeValue] - START/END');
        // if($('input[type=password]').length != 1)
        return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('method [applyValue] - START/END');
        if (this.showHideCells && this.enableFlag == true) {
            item[this.args.column.field] = state;
            item.slickgrid_rowcontent[this.args.column.field] = { 'content': state };
            /** @type {?} */
            var crudChange = this.commonGrid.changes.getValues().find((/**
             * @param {?} x
             * @return {?}
             */
            x => ((x.crud_data.id == this.args.item.id))));
            //console.log('-------crudChange :',crudChange);
            if (crudChange)
                crudChange['crud_data'][this.args.column.field] = state;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    isValueChanged() {
        if (this.showHideCells) {
            setTimeout((/**
             * @return {?}
             */
            () => {
                /** @type {?} */
                let isChanged = (this.$input.val() !== this.defaultValue);
                this.logger.info('method [isValueChanged] , returned value: "' + isChanged + '" - START/END');
                if (this.enableFlag == true) {
                    this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => ((x.crud_data.id == this.args.item.id))));
                    if (this.CRUD_CHANGES_DATA != undefined && this.CRUD_CHANGES_DATA != null) {
                        this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : undefined;
                        if (this.originalDefaultValue == undefined)
                            this.originalDefaultValue = "";
                    }
                    if ((this.originalDefaultValue == null && isChanged) || (((this.originalDefaultValue != null)) && (this.originalDefaultValue != this.$input.val()))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                            if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field].content = this.defaultValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            this.validationResult = this.commonGrid.validate(this.args.item, original_row, false);
                            /* console.log('[InputTextEditor] validationResult newValue :',this.args.item)
                             console.log('[InputTextEditor] validationResult oldValue :',original_row)
                             console.log('[InputTextEditor] validationResult resultat :',this.validationResult )*/
                            if (this.validationResult) {
                                this.commonGrid.updateCrud(updatedObject);
                                this.commonGrid.spyChanges({ field: this.args.column.field });
                                //ITEM_CHANGED
                                /** @type {?} */
                                var event = {
                                    rowIndex: this.args.item.id,
                                    target: this.args.column.editor,
                                    dataField: this.args.column.field,
                                    listData: Object.assign({}, updatedObject)
                                };
                                if (this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                                    /** @type {?} */
                                    var x = this.commonGrid.ITEM_CHANGED.observers[0];
                                    this.commonGrid.ITEM_CHANGED.observers = [];
                                    this.commonGrid.ITEM_CHANGED.observers[0] = x;
                                }
                                this.commonGrid.ITEM_CHANGED.emit(event);
                                if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                    /** @type {?} */
                                    var x = SwtCommonGridItemRenderChanges.observers[0];
                                    SwtCommonGridItemRenderChanges.observers = [];
                                    SwtCommonGridItemRenderChanges.observers[0] = x;
                                }
                                SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.$input.val(), id: this.args.item.id });
                            }
                            else {
                                isChanged = false;
                                if (this.originalDefaultValue == this.$input.val()) {
                                    /** @type {?} */
                                    var crudChange = this.commonGrid.changes.getValues().find((/**
                                     * @param {?} x
                                     * @return {?}
                                     */
                                    x => ((x.crud_data.id == this.args.item.id))));
                                    /** @type {?} */
                                    var ch = String("U(" + this.args.column.field + ")");
                                    if (crudChange) {
                                        if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                                        }
                                        if (crudChange['crud_operation'] == "") {
                                            /** @type {?} */
                                            var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                             * @param {?} x
                                             * @return {?}
                                             */
                                            x => ((x.crud_data.id == this.args.item.id))));
                                            this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                                        }
                                    }
                                }
                                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                                if (this.commonGrid.changes.size() == 0)
                                    this.commonGrid.spyNoChanges({ field: this.args.column.field });
                                if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                    /** @type {?} */
                                    var x = SwtCommonGridItemRenderChanges.observers[0];
                                    SwtCommonGridItemRenderChanges.observers = [];
                                    SwtCommonGridItemRenderChanges.observers[0] = x;
                                }
                                SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.$input.val(), id: this.args.item.id });
                            }
                        }
                    }
                    else if (this.originalDefaultValue == this.$input.val()) {
                        if (this.commonGrid.changes.size() == 0)
                            this.commonGrid.spyNoChanges({ field: this.args.column.field });
                        /** @type {?} */
                        var crudChange = this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => ((x.crud_data.id == this.args.item.id))));
                        /** @type {?} */
                        var ch = String("U(" + this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => ((x.crud_data.id == this.args.item.id))));
                                this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        if (isChanged) {
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field].content = this.defaultValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            //ITEM_CHANGED
                            /** @type {?} */
                            var event = {
                                rowIndex: this.args.item.id,
                                target: this.args.column.editor,
                                dataField: this.args.column.field,
                                listData: Object.assign({}, updatedObject)
                            };
                            if (this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                                /** @type {?} */
                                var x = this.commonGrid.ITEM_CHANGED.observers[0];
                                this.commonGrid.ITEM_CHANGED.observers = [];
                                this.commonGrid.ITEM_CHANGED.observers[0] = x;
                            }
                            this.commonGrid.ITEM_CHANGED.emit(event);
                        }
                    }
                }
                //  console.log('this.commonGrid.changes', this.commonGrid.changes)
                return isChanged;
            }), 0);
        }
        else {
            return false;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    validate() {
        this.logger.info('method [validate] - START/END');
        return {
            valid: true,
            msg: null
        };
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.validationResult;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.logger;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_DATA;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    InputTextEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    InputTextEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.columnDef;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.maxChars;
    /**
     * @type {?}
     * @private
     */
    InputTextEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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