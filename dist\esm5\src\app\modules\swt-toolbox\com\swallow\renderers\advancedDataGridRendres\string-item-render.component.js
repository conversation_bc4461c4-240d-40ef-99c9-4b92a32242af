/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Types } from "./types";
var StringItemRender = /** @class */ (function (_super) {
    tslib_1.__extends(StringItemRender, _super);
    function StringItemRender(stringelement, common) {
        var _this = _super.call(this, stringelement, common) || this;
        _this.stringelement = stringelement;
        _this.common = common;
        _this.text = "";
        _this.color = "";
        _this.type = Types.STR;
        _this.id = 0;
        return _this;
    }
    /**
     * @return {?}
     */
    StringItemRender.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    StringItemRender.decorators = [
        { type: Component, args: [{
                    selector: 'StringItemRender',
                    template: "\n        <span class=\"string-item-render\" [id]=\"id\">\n            {{ text }}\n        </span>\n    ",
                    styles: ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .string-item-render {\n            display: block;\n            width: 100%;\n            text-align: justify;\n            padding: 0 5px;\n            margin: 0px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    StringItemRender.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return StringItemRender;
}(UIComponent));
export { StringItemRender };
if (false) {
    /** @type {?} */
    StringItemRender.prototype.text;
    /** @type {?} */
    StringItemRender.prototype.color;
    /** @type {?} */
    StringItemRender.prototype.type;
    /** @type {?} */
    StringItemRender.prototype.id;
    /**
     * @type {?}
     * @private
     */
    StringItemRender.prototype.stringelement;
    /**
     * @type {?}
     * @private
     */
    StringItemRender.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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