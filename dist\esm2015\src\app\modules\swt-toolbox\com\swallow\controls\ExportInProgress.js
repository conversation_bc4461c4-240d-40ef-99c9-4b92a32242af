/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "../controls/swt-module.component";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { CancelExportEvent } from "../events/cancel-export-event.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { SwtButton } from "./swt-button.component";
import { ExternalInterface } from "../utils/external-interface.service";
import { Logger } from "../logging/logger.service";
export class ExportInProgress extends SwtModule {
    /**
     * @param {?} element
     * @param {?} common
     */
    constructor(element, common) {
        super(element, common);
        this.element = element;
        this.common = common;
        this.labelValue = null;
        this.exportToken = null;
        //Timer to check if export is done correctly
        this.exportTimer = null;
        //Httpservice variable
        this.inputData = new HTTPComms(CommonService.instance);
        //servlet(action) path
        this.actionPath = null;
        //to send request parameters to server
        this.requestParams = new Array();
        this._exportCancelFunction = this.defaultContentFunction.bind(this);
        this.SwtAlert = new SwtAlert(this.common);
        this.logger = new Logger("ExportInProgress", this.common.httpclient);
    }
    /**
     * @return {?}
     */
    initData() {
    }
    /**
     * @return {?}
     */
    defaultContentFunction() {
        console.log("***_exportCancelFunction");
    }
    /**
     * @return {?}
     */
    get exportCancelFunction() {
        return this._exportCancelFunction;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set exportCancelFunction(value) {
        console.log("TCL: ExportInProgress -> set t   setexportCancelFunction -> exportCancelFunction");
        this._exportCancelFunction = value;
    }
    /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    show(parent) {
        //   SwtPopUpManager.addPopUp(parent, ExportInProgress,{exportToken: this.exportToken}, false);
        this.win = SwtPopUpManager.createPopUp(this, ExportInProgress, {});
        this.win.enableResize = false;
        //   this.win.title = "Export";
        this.win.width = '280';
        this.win.height = '110';
        this.win.showControls = false;
        this.win.showHeader = false;
        this.win.isModal = true;
        console.log("TCL: ExportInProgress -> show -> this._exportCancelFunction", this._exportCancelFunction);
        this.win.exportCancelFunction = this._exportCancelFunction.bind(this);
        this.win.onClose.subscribe((/**
         * @param {?} res
         * @return {?}
         */
        (res) => {
        }));
        this.win.display();
    }
    /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    hide(parent) {
        this.win.close();
    }
    /**
     * @return {?}
     */
    cancelReport() {
        /** @type {?} */
        var cancelExportEvent = new CancelExportEvent(CancelExportEvent.CANCEL_BUTTON_CLICK);
        /** @type {?} */
        var obj = new Object();
        obj["exportToken"] = this.exportToken;
        cancelExportEvent.exportDTO = obj;
        this.exportCancelFunction(null);
        //   this.cancelExport(cancelExportEvent);
        this.close();
    }
    /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    cancelExport(event) {
        // Calls the inputDataResult function to load the datagrid
        this.inputData.cbResult = (/**
         * @return {?}
         */
        () => {
            this.ExportCanceled;
        });
        // Sets the action path for Interface Monitor
        this.actionPath = "report!cancelReport.do";
        // Sets the full URL for Interface Monitor
        this.inputData.url = SwtUtil.getBaseURL() + this.actionPath;
        // Sets the flag for encoding URL to false
        this.inputData.encodeURL = false;
        // Sets the datagrid format
        //      this.inputData.resultFormat="e4x";
        this.requestParams["cancelExport"] = "true";
        this.requestParams["exportCookie1234"] = event.exportDTO["exportToken"];
        // Send the request to the server
        this.inputData.send(this.requestParams);
    }
    /**
     * @return {?}
     */
    initTimer() {
        // Start export timer
        //Instanciate a timer to check coookies in a regular interval
        this.logger.info("initTimer method start");
        /** @type {?} */
        var startExport = this.getTimer();
        /** @type {?} */
        var interval = setInterval((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            // Check for the export cookie
            /** @type {?} */
            var exportResult = ExternalInterface.call('checkExportStatus', [this.exportToken]);
            //                      
            if (exportResult != null) {
                // Stop the timer
                clearInterval(interval);
                //              parentApplication.disposeExportProgressPopup(); 
                ExternalInterface.call('disposeExport');
                //Display an alert if result contains an error
                if (exportResult.substring(0, 2) == 'KO') {
                    if (exportResult.substring(3) == 'report.design.unsupported')
                        this.SwtAlert.info(SwtUtil.getCommonMessages('alert.design_unsupported'));
                    else
                        this.SwtAlert.error(SwtUtil.getCommonMessages('alert.generic_exception'), SwtUtil.getCommonMessages('alert_header.error'));
                }
                else if (exportResult.substring(0, 2) == 'CA') {
                    this.SwtAlert.info(SwtUtil.getCommonMessages('alert.export_canceled'));
                }
                ExternalInterface.call('finishExport');
            }
            else if ( /*!this.cancelButton.enabled && */(this.getTimer() - startExport) > 8000) {
                ExternalInterface.call('disposeExport');
                clearInterval(interval);
                this.SwtAlert.info(SwtUtil.getCommonMessages('alert.export_canceled'));
                this.logger.warn("Report canceled!!");
            }
        }), 300);
        this.logger.info("initTimer method end");
    }
    /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    ExportCanceled(event) {
        ExternalInterface.call('finishExport');
    }
    /**
     * @private
     * @return {?}
     */
    getTimer() {
        return new Date().getTime();
    }
}
ExportInProgress.decorators = [
    { type: Component, args: [{
                selector: 'SwtReportProgress',
                template: `
    <SwtModule (creationComplete)="initData()"  (close)="close()" width="100%" height="100%" controlBarEnabled="false">
          <VBox  paddingLeft ="10" paddingTop ="10" horizontalAlign="center" verticalAlign="middle" class="popup" width="264" height="100%">
                  <div  style="width:100%" class="progress">
                      <div class="progress-bar progress-bar-striped active" role="progressbar"
                      aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width:100%"></div>
                  </div>
              <HBox paddingTop ="10" horizontalAlign="center">
                  <SwtButton #cancelButton label="Cancel" (click)="cancelReport()" style="margin: auto"></SwtButton>
              </HBox>
          </VBox>
    </SwtModule>
  `,
                styles: [`
       .progress {
           height: 8px;
           border-radius: 0px;
           margin-top: 6px;
       }
  
  
  `]
            }] }
];
/** @nocollapse */
ExportInProgress.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
ExportInProgress.propDecorators = {
    cancelButton: [{ type: ViewChild, args: ["cancelButton",] }]
};
if (false) {
    /** @type {?} */
    ExportInProgress.prototype.labelValue;
    /** @type {?} */
    ExportInProgress.prototype.exportToken;
    /** @type {?} */
    ExportInProgress.prototype.exportTimer;
    /** @type {?} */
    ExportInProgress.prototype.inputData;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.logger;
    /** @type {?} */
    ExportInProgress.prototype.win;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype._exportCancelFunction;
    /** @type {?} */
    ExportInProgress.prototype.cancelButton;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.element;
    /**
     * @type {?}
     * @private
     */
    ExportInProgress.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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