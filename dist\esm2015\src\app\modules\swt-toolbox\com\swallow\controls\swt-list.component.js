/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, Renderer2 } from '@angular/core';
//import { SwtAbstract } from "./swt-abstract";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { focusManager } from '../managers/focus-manager.service';
// event.target.offsetWidth
// event.target.scrollWidth
/*
    SwtList
    <AUTHOR> Swallow TN 😎
*/
export class SwtList extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this._selectedIndices = [];
        this._selectable = true;
        this._selectedItems = [];
        this._enabled = true;
        this._allowMultipleSelection = false;
        this._dataProvider = [];
        this.dataProviderOld = new Array();
        this.coloredIndex = [false];
        this.multipleSelectNav = [false];
        this.focusedIndex = 0;
        this.index = 0;
        /* set change handler */
        this.click_ = new EventEmitter();
        //    @Output('doubleClick') private doubleClick_ = new EventEmitter<any>();
        this.toolTip = "";
        this.showDataTip = false;
        this.doubleClickEnabled = true;
        this.firstCall = true;
        this.id = "";
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.dataProviderOld = [];
        this.originalValue = [];
        this.firstShown = 0;
        if (this.enabled == undefined) {
            this.enabled = true;
        }
        if (this.selectable == undefined) {
            this.selectable = true;
        }
        if (!this.dataProvider) {
            this.dataProvider = [];
        }
        if (this.enabled === true) {
            for (let index = 1; index < this.dataProvider.length; index++) {
                this.coloredIndex[index] = false;
            }
        }
    }
    /* @Inputs */
    /**
     * @param {?} value
     * @return {?}
     */
    set allowMultipleSelection(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._allowMultipleSelection = true;
            }
            else {
                this._allowMultipleSelection = false;
            }
        }
        else {
            this._allowMultipleSelection = value;
        }
    }
    /**
     * @return {?}
     */
    get allowMultipleSelection() {
        return this._allowMultipleSelection;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        try {
            this._dataProvider = [];
            if (value.length == undefined) {
                value = [value];
            }
            if ((!value) || (!value[0])) {
                this._dataProvider = [];
                if (this.firstCall) {
                    this.originalValue = [];
                    this.firstCall = false;
                }
            }
            else if (value[0].length) {
                /** @type {?} */
                var temp = { "type": "", "value": "", "selected": 0, "content": "" };
                for (let index = 0; index < value.length; index++) {
                    temp.value = value[index];
                    temp.content = value[index];
                    this._dataProvider[index] = Object.assign({}, temp);
                    if (this.firstCall) {
                        this.originalValue[index] = Object.assign({}, temp);
                        if (index == value.length - 1) {
                            this.firstCall = false;
                        }
                    }
                }
            }
            else {
                this._dataProvider = [...value];
                if (this.firstCall) {
                    this.originalValue = [...value];
                    this.firstCall = false;
                }
            }
            this.onChange(event);
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @param {?} event
     * @param {?} item
     * @return {?}
     */
    setToolTip(event, item) {
        this.toolTip = this.showDataTip && event.target.scrollWidth > event.target.offsetWidth ? item.content : '';
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedIndex(value) {
        this.coloredIndex.fill(false);
        this.multipleSelectNav.fill(false);
        if (value != undefined && value != null && value != -1) {
            this.coloredIndex[value] = true;
            this.multipleSelectNav[value] = true;
            this.focusedIndex = value;
            this._selectedIndex = value;
            this._selectedItem = this.dataProvider[value].value;
        }
        else {
            this.selectedItem = null;
            this._selectedIndex = -1;
            this.selectedIndices = [];
        }
    }
    /**
     * @return {?}
     */
    get selectedIndex() {
        /** @type {?} */
        let found = false;
        /** @type {?} */
        let i = 0;
        if (this.selectedIndices.length <= 1) {
            while (i < this.coloredIndex.length && !found) {
                if (this.coloredIndex[i] == true) {
                    this._selectedIndex = i;
                    found = true;
                }
                i++;
            }
            if (!found) {
                this._selectedIndex = -1;
            }
        }
        return this._selectedIndex;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedIndices(value) {
        this.coloredIndex.fill(false);
        for (let index = 0; index < value.length; index++) {
            this.coloredIndex[value[index]] = true;
        }
        this._selectedIndices = [...value];
    }
    /**
     * @return {?}
     */
    get selectedIndices() {
        this._selectedIndices = [];
        for (let index = 0; index < this.coloredIndex.length; index++) {
            if (this.coloredIndex[index] == true) {
                this._selectedIndices.push(index);
            }
        }
        return this._selectedIndices;
    }
    /* width getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        if (value !== undefined) {
            if (value.indexOf('%') === -1) {
                this._width = value + "px";
            }
            else {
                this._width = value;
            }
        }
        this._renderer.setStyle(this.elem.nativeElement, 'width', this._width);
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /* @Inputs */
    /* height getter and setter */
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        if (value !== undefined) {
            if (value.indexOf('%') === -1) {
                this._height = value + "px";
            }
            else {
                this._height = value;
            }
        }
        this._renderer.setStyle(this.elem.nativeElement, 'height', this._height);
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onChange(event) {
        if (this.dataProvider.length == this.dataProviderOld.length) {
            /** @type {?} */
            let change = !(JSON.stringify(this.dataProvider) === JSON.stringify(this.dataProviderOld));
            if (change) {
                for (let index = 0; index < this.dataProvider.length; index++) {
                    this.dataProviderOld[index] = this.dataProvider[index];
                }
                this.change_.emit(this.dataProvider);
            }
        }
        else {
            this.change_.emit(this.dataProvider);
            if (this.dataProvider.length == 0) {
                this.dataProviderOld = [];
            }
            else {
                for (let index = 0; index < this.dataProvider.length; index++) {
                    this.dataProviderOld[index] = this.dataProvider[index];
                }
            }
        }
        this.spyChanges(this.dataProvider);
    }
    // function used to handle keybords keys events
    /**
     * @param {?} event
     * @return {?}
     */
    testkey(event) {
        if (event.keyCode === 38) {
            if (!event.ctrlKey) {
                if (this.focusedIndex != this.selectedIndex) {
                    this.selectedIndex = this.focusedIndex;
                }
                event.preventDefault();
                this.scrollToTop();
                if (this.selectedIndex != 0) {
                    this.selectedIndex--;
                }
            }
            else if (event.keyCode === 38 && event.ctrlKey && this.allowMultipleSelection) {
                event.preventDefault();
                this.scrollFocusToTop();
                if (this.focusedIndex != 0) {
                    for (let index = 0; index < this.coloredIndex.length; index++) {
                        this.multipleSelectNav[index] = false;
                    }
                    this.multipleSelectNav[this.focusedIndex - 1] = true;
                    this.focusedIndex = this.focusedIndex - 1;
                }
            }
        }
        else if (event.keyCode === 40) {
            if (!event.ctrlKey) {
                if (this.focusedIndex != this.selectedIndex) {
                    this.selectedIndex = this.focusedIndex;
                }
                event.preventDefault();
                this.scrollToBottom();
                if (this.selectedIndex < this.coloredIndex.length - 1) {
                    this.selectedIndex++;
                }
            }
            else if (event.keyCode === 40 && event.ctrlKey && this.allowMultipleSelection) {
                event.preventDefault();
                this.scrollFocusToBottom();
                if (this.focusedIndex < this.coloredIndex.length - 1) {
                    for (let index = 0; index < this.coloredIndex.length; index++) {
                        this.multipleSelectNav[index] = false;
                    }
                    this.multipleSelectNav[this.focusedIndex + 1] = true;
                    this.focusedIndex = this.focusedIndex + 1;
                }
            }
        }
        if ((event.ctrlKey && event.keyCode == 32) && this.allowMultipleSelection === true) {
            this.coloredIndex[this.focusedIndex] = !this.coloredIndex[this.focusedIndex];
            if (this.coloredIndex[this.focusedIndex]) {
                this.selectedIndices.push(this.focusedIndex);
            }
        }
    }
    /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    scrollToBottom() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    let viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    scrollToTop() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    /* This method is used to handle scrolling
     * to bottom.
     */
    /**
     * @private
     * @return {?}
     */
    scrollFocusToBottom() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.focusedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    let viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    scrollFocusToTop() {
        try {
            // make reference to list items.
            /** @type {?} */
            let list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            let listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            let itemIndex = this.focusedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                let item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    let itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    let itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    let itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    addItem(value) {
        this.dataProvider.push(value);
        this.onChange(event);
    }
    // remove single item using it's own index
    /**
     * @param {?} index
     * @return {?}
     */
    removeItem(index) {
        this.dataProvider.splice(index, 1);
        if (this.dataProvider.length > 0 && index < this.dataProvider.length) {
            this.selectedIndex = index;
        }
        else if (index > 0) {
            this.selectedIndex = index - 1;
        }
        else if (index == 0) {
            this.selectedIndex = -1;
        }
        this.onChange(event);
    }
    //this method is used to remove multiple items at once 
    // @param: indexes: array of indexes
    /**
     * @param {?} indexes
     * @return {?}
     */
    removeItems(indexes) {
        /** @type {?} */
        let x = this.dataProvider;
        for (let index = 0; index < indexes.length; index++) {
            delete x[indexes[index]];
        }
        /** @type {?} */
        var filtered = x.filter((/**
         * @param {?} value
         * @param {?} index
         * @param {?} arr
         * @return {?}
         */
        function (value, index, arr) {
            return value != undefined;
        }));
        this.dataProvider = filtered;
        this.selectedIndex = indexes[0] - 1;
    }
    /**
     * @return {?}
     */
    removeAll() {
        this.dataProvider = [];
        this.onChange(event);
    }
    // function used to do multiple selection with ctrl & click
    /**
     * @param {?} event
     * @param {?} i
     * @return {?}
     */
    clickHandler(event, i) {
        if (this.enabled === true) {
            if (event.ctrlKey && this.allowMultipleSelection == true) {
                this._selectedItems = new Array();
                this.coloredIndex[i] = !this.coloredIndex[i];
            }
            else if (event.shiftKey && this.allowMultipleSelection == true && this.selectedIndex != -1) {
                if (this.selectedIndex < i) {
                    /** @type {?} */
                    let temp = [];
                    /** @type {?} */
                    let lastSelected = this.selectedIndex;
                    for (let index = lastSelected; index <= i; index++) {
                        temp.push(index);
                    }
                    this.coloredIndex.fill(false);
                    this.selectedIndices = temp;
                }
                else {
                    /** @type {?} */
                    let temp = [];
                    /** @type {?} */
                    let lastSelected = this.selectedIndex;
                    for (let index = i; index <= lastSelected; index++) {
                        temp.push(index);
                    }
                    this.coloredIndex.fill(false);
                    this.selectedIndices = temp;
                }
            }
            else {
                // for (let index = 0; index < this.dataProvider.length; index++) {
                //     this.coloredIndex[index] = false;
                // }
                this._selectedItems = new Array();
                this.coloredIndex.fill(false, 0, this.coloredIndex.length - 1);
                this.selectedIndex = i;
                this.index = i;
                this.coloredIndex[i] = true;
            }
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedItems(value) {
    }
    /**
     * @return {?}
     */
    get selectedItems() {
        this._selectedItems = [];
        if (this.enabled === true) {
            for (let index = 0; index < this.coloredIndex.length; index++) {
                if (this.coloredIndex[index] === true) {
                    this._selectedItems.push(this.dataProvider[index]);
                }
            }
            return this._selectedItems;
        }
        else {
            return null;
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedItem(value) {
        /** @type {?} */
        let i = 0;
        /** @type {?} */
        let found = false;
        while (i < this.dataProvider.length && !found) {
            if (this.dataProvider[i].value == value) {
                this.selectedIndex = i;
                this._selectedItem = this.dataProvider[i].value;
                found = true;
            }
            i++;
        }
        if (i == this.dataProvider.length - 1 && !found) {
            this.selectedIndex = -1;
            this._selectedItem = null;
        }
    }
    /**
     * @return {?}
     */
    get selectedItem() {
        return this._selectedItem;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (value.toString() == "true") {
            this._enabled = true;
        }
        else if (value.toString() == "false")
            this._enabled = false;
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectable(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._selectable = true;
            }
            else {
                this._selectable = false;
            }
        }
        else {
            this._selectable = value;
        }
    }
    /**
     * @return {?}
     */
    get selectable() {
        return this._selectable;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (JSON.stringify(this.originalValue) == JSON.stringify(event)) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    }
    /**
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue = [...this.dataProvider];
        this.spyChanges(this.dataProvider);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onClick(event) {
        focusManager.focusTarget = this.id;
        this.click_.emit(event);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    doubleClicked(event) {
        if (this.doubleClickEnabled) {
            this.doubleClick_.emit(event);
        }
    }
}
SwtList.decorators = [
    { type: Component, args: [{
                selector: 'SwtList',
                template: `
    <div #list id="ecran" class="boxbox" [ngClass]="{'disabl':enabled==false}">
    <ul #swtlistItems class="list-group" (window:keydown)="testkey($event)">
        <div *ngIf="enabled==true&&selectable==true">
            <li (click)="clickHandler($event,i);onClick($event)" (mouseenter)="setToolTip($event,item)"
                data-placement="right" (dblclick)="doubleClicked($event)" class="list-group-item"
                [ngClass]="{active:coloredIndex[i]==true,hover:coloredIndex!=i,multipleSelectNav:multipleSelectNav[i]==true}"
                *ngFor="let item of dataProvider; let i = index">
                <p  >{{ item.content }}</p>
                <img *ngIf="item.img" src="{{ item.img }}"  style="height:20px;width:20px"
                    class="icon-item">
            </li>
        </div>
        <div *ngIf="!enabled || !selectable">
            <li *ngFor="let item of dataProvider; let i = index" class="list-group-item-disabled" (mouseenter)="setToolTip($event,item)"
                 data-placement="right">
                <p>{{ item.content }}</p> 
                <img *ngIf="item.img" src="{{ item.img }}" style="height:20px;width:20px" class="item-img">
            </li>
        </div>
    </ul>
</div>

  `,
                styles: [`
    .icon-item{
        float:right; 
        margin-right:10px
    }
    .item-img{
        float:right; 
        margin-right:10px
    }
    .list-group-item-disabled{
        height: 25px;
        font-size: 14px;
        font-family: verdana,helvetica;
        padding: 0px 0px 0px 10px;
        margin: 0px;
        line-height: 25px;
        border: none;
        text-overflow: ellipsis; 
        white-space: nowrap;
        overflow:hidden
    }
    .list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
        color: black;  
    } 
    .list-group-item.active{
        background-color: #7FCEFF;
    }
    .list-group-item.multipleSelectNav{
        border-color: #7FCEFF;
        border-width: thin;
        border-style:solid;
    }
    .list-group-item:focus {
         background-color: red; 
    }
    .list-group{
        margin-bottom:0px !important;
    }
    .list-group-item:hover {
       background-color: #B2E1FF;
       cursor: default;
    }
    .list-group-item {
        height: 25px;
        font-size: 14px;
        font-family: verdana,helvetica;
        padding: 0px 0px 0px 10px;
        margin: 0px;
        line-height: 25px;
        border: none;
        text-overflow: ellipsis; 
        white-space: nowrap;
        overflow:hidden
    }
    
    .boxbox{
        height:100%;
        width:100%;
        border: 1px solid #ddd; 
        overflow: auto;
        background-color: white;
    }
    .boxbox:hover{
        cursor: default;
    }
    .disabl{
        z-index: 1;
        background-color: #ddd;
        opacity: 0.3;
    }
`]
            }] }
];
/** @nocollapse */
SwtList.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
SwtList.propDecorators = {
    click_: [{ type: Output, args: ['itemClick',] }],
    toolTip: [{ type: Input, args: ['toolTip',] }],
    showDataTip: [{ type: Input, args: ['showDataTips',] }],
    doubleClickEnabled: [{ type: Input, args: ['doubleClickEnabled',] }],
    allowMultipleSelection: [{ type: Input }],
    id: [{ type: Input }, { type: Input, args: ["id",] }],
    selectedIndex: [{ type: Input }],
    selectedIndices: [{ type: Input }],
    width: [{ type: Input }],
    height: [{ type: Input }],
    swtlistItems: [{ type: ViewChild, args: ["swtlistItems",] }],
    list: [{ type: ViewChild, args: ["list",] }],
    enabled: [{ type: Input }],
    selectable: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedIndices;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectable;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedItems;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.SwtListObject;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._allowMultipleSelection;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.dataProviderOld;
    /** @type {?} */
    SwtList.prototype.coloredIndex;
    /** @type {?} */
    SwtList.prototype.multipleSelectNav;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.focusedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.index;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.firstShown;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.click_;
    /** @type {?} */
    SwtList.prototype.toolTip;
    /** @type {?} */
    SwtList.prototype.showDataTip;
    /** @type {?} */
    SwtList.prototype.doubleClickEnabled;
    /** @type {?} */
    SwtList.prototype.firstCall;
    /** @type {?} */
    SwtList.prototype.id;
    /** @type {?} */
    SwtList.prototype.swtlistItems;
    /** @type {?} */
    SwtList.prototype.list;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWxpc3QuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1saXN0LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBVSxLQUFLLEVBQWEsTUFBTSxFQUFFLFlBQVksRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUE0QixTQUFTLEVBQUUsTUFBTSxlQUFlLENBQUM7O0FBR3RKLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx1Q0FBdUMsQ0FBQztBQUNsRSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFDeEQsT0FBTyxFQUFFLFlBQVksRUFBRSxNQUFNLG1DQUFtQyxDQUFDOzs7Ozs7O0FBeUdqRSxNQUFNLE9BQU8sT0FBUSxTQUFRLFNBQVM7Ozs7OztJQWtCbEMsWUFBb0IsSUFBZ0IsRUFBVSxhQUE0QixFQUFVLFNBQW9CO1FBQ3BHLEtBQUssQ0FBQyxJQUFJLEVBQUUsYUFBYSxDQUFDLENBQUM7UUFEWCxTQUFJLEdBQUosSUFBSSxDQUFZO1FBQVUsa0JBQWEsR0FBYixhQUFhLENBQWU7UUFBVSxjQUFTLEdBQVQsU0FBUyxDQUFXO1FBakJoRyxxQkFBZ0IsR0FBUSxFQUFFLENBQUM7UUFFM0IsZ0JBQVcsR0FBUSxJQUFJLENBQUM7UUFFeEIsbUJBQWMsR0FBRyxFQUFFLENBQUM7UUFJcEIsYUFBUSxHQUFZLElBQUksQ0FBQTtRQUN4Qiw0QkFBdUIsR0FBRyxLQUFLLENBQUM7UUFDaEMsa0JBQWEsR0FBUSxFQUFFLENBQUE7UUFDdkIsb0JBQWUsR0FBRyxJQUFJLEtBQUssRUFBRSxDQUFDO1FBQy9CLGlCQUFZLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUN0QixzQkFBaUIsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQzFCLGlCQUFZLEdBQUcsQ0FBQyxDQUFBO1FBQ2hCLFVBQUssR0FBRyxDQUFDLENBQUE7O1FBeUJZLFdBQU0sR0FBRyxJQUFJLFlBQVksRUFBTyxDQUFDOztRQUU1QyxZQUFPLEdBQVcsRUFBRSxDQUFDO1FBQ2hCLGdCQUFXLEdBQVksS0FBSyxDQUFDO1FBQ3ZCLHVCQUFrQixHQUFZLElBQUksQ0FBQztRQWlCaEUsY0FBUyxHQUFZLElBQUksQ0FBQztRQUViLE9BQUUsR0FBVyxFQUFFLENBQUM7SUE1QzdCLENBQUM7Ozs7SUFDRCxRQUFRO1FBQ0osSUFBSSxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUE7UUFDekIsSUFBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUE7UUFDdkIsSUFBSSxDQUFDLFVBQVUsR0FBRyxDQUFDLENBQUE7UUFDbkIsSUFBSSxJQUFJLENBQUMsT0FBTyxJQUFJLFNBQVMsRUFBRTtZQUMzQixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQTtTQUN0QjtRQUNELElBQUksSUFBSSxDQUFDLFVBQVUsSUFBSSxTQUFTLEVBQUU7WUFDOUIsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUE7U0FDekI7UUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNwQixJQUFJLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQTtTQUN6QjtRQUNELElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQUU7WUFDdkIsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO2dCQUMzRCxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQTthQUNuQztTQUNKO0lBQ0wsQ0FBQzs7Ozs7O0lBUUQsSUFDSSxzQkFBc0IsQ0FBQyxLQUFLO1FBQzVCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtZQUM3QixJQUFJLEtBQUssS0FBSyxNQUFNLEVBQUU7Z0JBQ2xCLElBQUksQ0FBQyx1QkFBdUIsR0FBRyxJQUFJLENBQUM7YUFDdkM7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLHVCQUF1QixHQUFHLEtBQUssQ0FBQzthQUN4QztTQUNKO2FBQU07WUFDSCxJQUFJLENBQUMsdUJBQXVCLEdBQUcsS0FBSyxDQUFDO1NBQ3hDO0lBQ0wsQ0FBQzs7OztJQUNELElBQUksc0JBQXNCO1FBQ3RCLE9BQU8sSUFBSSxDQUFDLHVCQUF1QixDQUFDO0lBQ3hDLENBQUM7Ozs7O0lBSUQsSUFBSSxZQUFZLENBQUMsS0FBSztRQUNsQixJQUFJO1lBQ0EsSUFBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUE7WUFDdkIsSUFBSSxLQUFLLENBQUMsTUFBTSxJQUFJLFNBQVMsRUFBRTtnQkFDM0IsS0FBSyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUE7YUFDbEI7WUFDRCxJQUFJLENBQUMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUU7Z0JBQ3pCLElBQUksQ0FBQyxhQUFhLEdBQUcsRUFBRSxDQUFBO2dCQUN2QixJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7b0JBQ2hCLElBQUksQ0FBQyxhQUFhLEdBQUcsRUFBRSxDQUFDO29CQUN4QixJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztpQkFDMUI7YUFDSjtpQkFDSSxJQUFJLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUU7O29CQUNsQixJQUFJLEdBQUcsRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFLE9BQU8sRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLENBQUMsRUFBRSxTQUFTLEVBQUUsRUFBRSxFQUFFO2dCQUNwRSxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtvQkFDL0MsSUFBSSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7b0JBQ3pCLElBQUksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO29CQUMzQixJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxxQkFBUSxJQUFJLENBQUUsQ0FBQTtvQkFDdkMsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO3dCQUNoQixJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxxQkFBUSxJQUFJLENBQUUsQ0FBQTt3QkFDdkMsSUFBSSxLQUFLLElBQUksS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7NEJBQzNCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO3lCQUMxQjtxQkFDSjtpQkFDSjthQUNKO2lCQUNJO2dCQUNELElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFDO2dCQUNoQyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7b0JBQ2hCLElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFDO29CQUNoQyxJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztpQkFDMUI7YUFDSjtZQUNELElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDdkI7UUFDRCxPQUFPLEtBQUssRUFBRTtZQUNWLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUM7U0FDeEI7SUFDTCxDQUFDOzs7O0lBQ0QsSUFBSSxZQUFZO1FBQ1osT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO0lBQzlCLENBQUM7Ozs7OztJQUNELFVBQVUsQ0FBQyxLQUFLLEVBQUUsSUFBSTtRQUNsQixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxXQUFXLElBQUksS0FBSyxDQUFDLE1BQU0sQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQTtJQUM5RyxDQUFDOzs7OztJQUNELElBQ0ksYUFBYSxDQUFDLEtBQUs7UUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDN0IsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUNsQyxJQUFJLEtBQUssSUFBSSxTQUFTLElBQUksS0FBSyxJQUFJLElBQUksSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEVBQUU7WUFDcEQsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUE7WUFDL0IsSUFBSSxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxHQUFHLElBQUksQ0FBQTtZQUNwQyxJQUFJLENBQUMsWUFBWSxHQUFHLEtBQUssQ0FBQTtZQUN6QixJQUFJLENBQUMsY0FBYyxHQUFHLEtBQUssQ0FBQTtZQUMzQixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUMsS0FBSyxDQUFBO1NBQ3REO2FBQ0k7WUFDRCxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQTtZQUN4QixJQUFJLENBQUMsY0FBYyxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQ3hCLElBQUksQ0FBQyxlQUFlLEdBQUcsRUFBRSxDQUFBO1NBQzVCO0lBQ0wsQ0FBQzs7OztJQUNELElBQUksYUFBYTs7WUFDVCxLQUFLLEdBQUcsS0FBSzs7WUFBRSxDQUFDLEdBQUcsQ0FBQztRQUN4QixJQUFJLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxJQUFJLENBQUMsRUFBRTtZQUNsQyxPQUFPLENBQUMsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sSUFBSSxDQUFDLEtBQUssRUFBRTtnQkFDM0MsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksRUFBRTtvQkFDOUIsSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLENBQUE7b0JBQ3ZCLEtBQUssR0FBRyxJQUFJLENBQUE7aUJBQ2Y7Z0JBQ0QsQ0FBQyxFQUFFLENBQUE7YUFDTjtZQUNELElBQUksQ0FBQyxLQUFLLEVBQUU7Z0JBQ1IsSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLENBQUMsQ0FBQTthQUMzQjtTQUNKO1FBQ0QsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO0lBQy9CLENBQUM7Ozs7O0lBQ0QsSUFDSSxlQUFlLENBQUMsS0FBSztRQUNyQixJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUM3QixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtZQUMvQyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQTtTQUN6QztRQUNELElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUE7SUFDdEMsQ0FBQzs7OztJQUNELElBQUksZUFBZTtRQUNmLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxFQUFFLENBQUE7UUFDMUIsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO1lBQzNELElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsSUFBSSxJQUFJLEVBQUU7Z0JBQ2xDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7YUFDcEM7U0FDSjtRQUNELE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO0lBQ2pDLENBQUM7Ozs7OztJQUVELElBQ0ksS0FBSyxDQUFDLEtBQWE7UUFDbkIsSUFBSSxLQUFLLEtBQUssU0FBUyxFQUFFO1lBQ3JCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtnQkFDM0IsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2FBQzlCO2lCQUFNO2dCQUNILElBQUksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO2FBQ3ZCO1NBQ0o7UUFDRCxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxPQUFPLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO0lBQzFFLENBQUM7Ozs7SUFDRCxJQUFJLEtBQUs7UUFDTCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDdkIsQ0FBQzs7Ozs7OztJQUdELElBQ0ksTUFBTSxDQUFDLEtBQWE7UUFDcEIsSUFBSSxLQUFLLEtBQUssU0FBUyxFQUFFO1lBQ3JCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtnQkFDM0IsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2FBQy9CO2lCQUFNO2dCQUNILElBQUksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO2FBQ3hCO1NBQ0o7UUFDRCxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxRQUFRLEVBQUUsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFBO0lBQzVFLENBQUM7Ozs7SUFDRCxJQUFJLE1BQU07UUFDTixPQUFPLElBQUksQ0FBQyxPQUFPLENBQUM7SUFDeEIsQ0FBQzs7Ozs7SUFHRCxRQUFRLENBQUMsS0FBSztRQUNWLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLEVBQUU7O2dCQUNyRCxNQUFNLEdBQVksQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1lBQ25HLElBQUksTUFBTSxFQUFFO2dCQUNSLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtvQkFDM0QsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFBO2lCQUN6RDtnQkFDRCxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7YUFDdkM7U0FDSjthQUNJO1lBQ0QsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO1lBQ3BDLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUFFO2dCQUMvQixJQUFJLENBQUMsZUFBZSxHQUFHLEVBQUUsQ0FBQTthQUM1QjtpQkFDSTtnQkFDRCxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7b0JBQzNELElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQTtpQkFDekQ7YUFDSjtTQUNKO1FBQ0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7SUFDdkMsQ0FBQzs7Ozs7O0lBRUQsT0FBTyxDQUFDLEtBQUs7UUFDVCxJQUFJLEtBQUssQ0FBQyxPQUFPLEtBQUssRUFBRSxFQUFFO1lBQ3RCLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFO2dCQUNoQixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRTtvQkFDekMsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFBO2lCQUN6QztnQkFDRCxLQUFLLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQ3ZCLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQztnQkFDbkIsSUFBSSxJQUFJLENBQUMsYUFBYSxJQUFJLENBQUMsRUFBRTtvQkFDekIsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO2lCQUN4QjthQUNKO2lCQUNJLElBQUksS0FBSyxDQUFDLE9BQU8sS0FBSyxFQUFFLElBQUksS0FBSyxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsc0JBQXNCLEVBQUU7Z0JBQzNFLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQztnQkFDdkIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7Z0JBQ3hCLElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLEVBQUU7b0JBQ3hCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTt3QkFDM0QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQTtxQkFDeEM7b0JBQ0QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFBO29CQUNwRCxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFBO2lCQUM1QzthQUNKO1NBQ0o7YUFDSSxJQUFJLEtBQUssQ0FBQyxPQUFPLEtBQUssRUFBRSxFQUFFO1lBQzNCLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFO2dCQUNoQixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRTtvQkFDekMsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFBO2lCQUN6QztnQkFDRCxLQUFLLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQ3ZCLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQztnQkFDdEIsSUFBSSxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtvQkFDbkQsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO2lCQUN4QjthQUNKO2lCQUNJLElBQUksS0FBSyxDQUFDLE9BQU8sS0FBSyxFQUFFLElBQUksS0FBSyxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsc0JBQXNCLEVBQUU7Z0JBQzNFLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQztnQkFDdkIsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7Z0JBQzNCLElBQUksSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7b0JBQ2xELEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTt3QkFDM0QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQTtxQkFDeEM7b0JBQ0QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFBO29CQUNwRCxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFBO2lCQUM1QzthQUNKO1NBQ0o7UUFDRCxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxLQUFLLENBQUMsT0FBTyxJQUFJLEVBQUUsQ0FBQyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsS0FBSyxJQUFJLEVBQUU7WUFDaEYsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQTtZQUM1RSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFO2dCQUN0QyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7YUFDL0M7U0FDSjtJQUNMLENBQUM7Ozs7Ozs7SUFLTyxjQUFjO1FBQ2xCLElBQUk7OztnQkFFSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhOzs7Z0JBRTlCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O2dCQUU5QixTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDO1lBQ3RDLHlCQUF5QjtZQUN6QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTs7O29CQUVaLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDO2dCQUMzRCwyQkFBMkI7O2dCQUEzQiwyQkFBMkI7Z0JBQzNCLElBQUksSUFBSSxLQUFLLFNBQVMsRUFBRTs7O3dCQUVoQixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7Ozt3QkFFOUIsT0FBTyxHQUFHLFNBQVMsR0FBRyxVQUFVOzs7d0JBRWhDLFVBQVUsR0FBRyxPQUFPLEdBQUcsVUFBVTs7O3dCQUVqQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVO29CQUM1Qyw4QkFBOEI7b0JBQzlCLElBQUksVUFBVSxHQUFHLFVBQVUsRUFBRTt3QkFDekIsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVLEdBQUcsVUFBVSxDQUFDO3FCQUM1QztpQkFDSjthQUNKO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDdkI7SUFDTCxDQUFDOzs7Ozs7O0lBS08sV0FBVztRQUNmLElBQUk7OztnQkFFSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhOzs7Z0JBRTlCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O2dCQUU5QixTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDO1lBQ3RDLHlCQUF5QjtZQUN6QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTs7O29CQUVaLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDO2dCQUMzRCwyQkFBMkI7O2dCQUEzQiwyQkFBMkI7Z0JBQzNCLElBQUksSUFBSSxLQUFLLFNBQVMsRUFBRTs7O3dCQUVoQixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7Ozt3QkFFOUIsT0FBTyxHQUFHLFNBQVMsR0FBRyxVQUFVOzs7d0JBRWhDLFVBQVUsR0FBRyxPQUFPLEdBQUcsVUFBVTtvQkFDckMsOEJBQThCO29CQUM5QixJQUFJLE9BQU8sR0FBRyxJQUFJLENBQUMsU0FBUyxFQUFFO3dCQUMxQixJQUFJLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQztxQkFDNUI7aUJBQ0o7YUFDSjtTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3ZCO0lBQ0wsQ0FBQzs7Ozs7Ozs7SUFJTyxtQkFBbUI7UUFDdkIsSUFBSTs7O2dCQUVJLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWE7OztnQkFFOUIsVUFBVSxHQUFHLElBQUksQ0FBQyxZQUFZOzs7Z0JBRTlCLFNBQVMsR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUM7WUFDckMseUJBQXlCO1lBQ3pCLElBQUksU0FBUyxHQUFHLENBQUMsQ0FBQyxFQUFFOzs7b0JBRVosSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUM7Z0JBQzNELDJCQUEyQjs7Z0JBQTNCLDJCQUEyQjtnQkFDM0IsSUFBSSxJQUFJLEtBQUssU0FBUyxFQUFFOzs7d0JBRWhCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O3dCQUU5QixPQUFPLEdBQUcsU0FBUyxHQUFHLFVBQVU7Ozt3QkFFaEMsVUFBVSxHQUFHLE9BQU8sR0FBRyxVQUFVOzs7d0JBRWpDLFVBQVUsR0FBRyxJQUFJLENBQUMsU0FBUyxHQUFHLFVBQVU7b0JBQzVDLDhCQUE4QjtvQkFDOUIsSUFBSSxVQUFVLEdBQUcsVUFBVSxFQUFFO3dCQUN6QixJQUFJLENBQUMsU0FBUyxHQUFHLFVBQVUsR0FBRyxVQUFVLENBQUM7cUJBQzVDO2lCQUNKO2FBQ0o7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUN2QjtJQUNMLENBQUM7Ozs7Ozs7SUFLTyxnQkFBZ0I7UUFDcEIsSUFBSTs7O2dCQUVJLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWE7OztnQkFFOUIsVUFBVSxHQUFHLElBQUksQ0FBQyxZQUFZOzs7Z0JBRTlCLFNBQVMsR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUM7WUFDckMseUJBQXlCO1lBQ3pCLElBQUksU0FBUyxHQUFHLENBQUMsQ0FBQyxFQUFFOzs7b0JBRVosSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUM7Z0JBQzNELDJCQUEyQjs7Z0JBQTNCLDJCQUEyQjtnQkFDM0IsSUFBSSxJQUFJLEtBQUssU0FBUyxFQUFFOzs7d0JBRWhCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O3dCQUU5QixPQUFPLEdBQUcsU0FBUyxHQUFHLFVBQVU7Ozt3QkFFaEMsVUFBVSxHQUFHLE9BQU8sR0FBRyxVQUFVO29CQUNyQyw4QkFBOEI7b0JBQzlCLElBQUksT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLEVBQUU7d0JBQzFCLElBQUksQ0FBQyxTQUFTLEdBQUcsT0FBTyxDQUFDO3FCQUM1QjtpQkFDSjthQUNKO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDdkI7SUFDTCxDQUFDOzs7OztJQUNELE9BQU8sQ0FBQyxLQUFLO1FBQ1QsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDN0IsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUN4QixDQUFDOzs7Ozs7SUFFRCxVQUFVLENBQUMsS0FBSztRQUNaLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQTtRQUNsQyxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUU7WUFDbEUsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUE7U0FDN0I7YUFDSSxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUU7WUFDaEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLEdBQUcsQ0FBQyxDQUFBO1NBQ2pDO2FBQ0ksSUFBSSxLQUFLLElBQUksQ0FBQyxFQUFFO1lBQ2pCLElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFDLENBQUE7U0FDMUI7UUFDRCxJQUFJLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFBO0lBQ3hCLENBQUM7Ozs7Ozs7SUFHRCxXQUFXLENBQUMsT0FBTzs7WUFDWCxDQUFDLEdBQUcsSUFBSSxDQUFDLFlBQVk7UUFDekIsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLE9BQU8sQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7WUFDakQsT0FBTyxDQUFDLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUE7U0FDM0I7O1lBQ0csUUFBUSxHQUFHLENBQUMsQ0FBQyxNQUFNOzs7Ozs7UUFBQyxVQUFVLEtBQUssRUFBRSxLQUFLLEVBQUUsR0FBRztZQUMvQyxPQUFPLEtBQUssSUFBSSxTQUFTLENBQUM7UUFDOUIsQ0FBQyxFQUFDO1FBQ0YsSUFBSSxDQUFDLFlBQVksR0FBRyxRQUFRLENBQUE7UUFDNUIsSUFBSSxDQUFDLGFBQWEsR0FBSSxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQ3hDLENBQUM7Ozs7SUFDRCxTQUFTO1FBQ0wsSUFBSSxDQUFDLFlBQVksR0FBRyxFQUFFLENBQUE7UUFDdEIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUN4QixDQUFDOzs7Ozs7O0lBRUQsWUFBWSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ2pCLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQUU7WUFDdkIsSUFBSSxLQUFLLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsSUFBSSxJQUFJLEVBQUU7Z0JBQ3RELElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxLQUFLLEVBQUUsQ0FBQztnQkFDbEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUE7YUFDL0M7aUJBQ0ksSUFBSSxLQUFLLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxzQkFBc0IsSUFBSSxJQUFJLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxDQUFDLENBQUMsRUFBRTtnQkFDeEYsSUFBSSxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsRUFBRTs7d0JBQ3BCLElBQUksR0FBRyxFQUFFOzt3QkFDVCxZQUFZLEdBQUcsSUFBSSxDQUFDLGFBQWE7b0JBQ3JDLEtBQUssSUFBSSxLQUFLLEdBQUcsWUFBWSxFQUFFLEtBQUssSUFBSSxDQUFDLEVBQUUsS0FBSyxFQUFFLEVBQUU7d0JBQ2hELElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7cUJBQ25CO29CQUNELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO29CQUM3QixJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQTtpQkFDOUI7cUJBQU07O3dCQUNDLElBQUksR0FBRyxFQUFFOzt3QkFDVCxZQUFZLEdBQUcsSUFBSSxDQUFDLGFBQWE7b0JBQ3JDLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssSUFBSSxZQUFZLEVBQUUsS0FBSyxFQUFFLEVBQUU7d0JBQ2hELElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7cUJBQ25CO29CQUNELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO29CQUM3QixJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQTtpQkFDOUI7YUFDSjtpQkFDSTtnQkFDRCxtRUFBbUU7Z0JBQ25FLHdDQUF3QztnQkFDeEMsSUFBSTtnQkFDSixJQUFJLENBQUMsY0FBYyxHQUFHLElBQUksS0FBSyxFQUFFLENBQUM7Z0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUE7Z0JBQzlELElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFDO2dCQUN2QixJQUFJLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTtnQkFDZCxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQTthQUM5QjtTQUVKO0lBQ0wsQ0FBQzs7Ozs7SUFDRCxJQUFJLGFBQWEsQ0FBQyxLQUFLO0lBQ3ZCLENBQUM7Ozs7SUFDRCxJQUFJLGFBQWE7UUFDYixJQUFJLENBQUMsY0FBYyxHQUFHLEVBQUUsQ0FBQTtRQUN4QixJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssSUFBSSxFQUFFO1lBQ3ZCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtnQkFDM0QsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxLQUFLLElBQUksRUFBRTtvQkFDbkMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBO2lCQUNyRDthQUNKO1lBQ0QsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO1NBQzlCO2FBQ0k7WUFDRCxPQUFPLElBQUksQ0FBQTtTQUNkO0lBQ0wsQ0FBQzs7Ozs7SUFDRCxJQUFJLFlBQVksQ0FBQyxLQUFLOztZQUNkLENBQUMsR0FBRyxDQUFDOztZQUNMLEtBQUssR0FBRyxLQUFLO1FBQ2pCLE9BQU8sQ0FBQyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQzNDLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLElBQUksS0FBSyxFQUFFO2dCQUNyQyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQTtnQkFDdEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQTtnQkFDL0MsS0FBSyxHQUFHLElBQUksQ0FBQTthQUNmO1lBQ0QsQ0FBQyxFQUFFLENBQUE7U0FDTjtRQUNELElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRTtZQUM3QyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQ3ZCLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFBO1NBQzVCO0lBQ0wsQ0FBQzs7OztJQUNELElBQUksWUFBWTtRQUNaLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUM5QixDQUFDOzs7OztJQUNELElBQ0ksT0FBTyxDQUFDLEtBQUs7UUFDYixJQUFJLEtBQUssQ0FBQyxRQUFRLEVBQUUsSUFBSSxNQUFNLEVBQUU7WUFDNUIsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUE7U0FDdkI7YUFDRyxJQUFJLEtBQUssQ0FBQyxRQUFRLEVBQUUsSUFBSSxPQUFPO1lBQzNCLElBQUksQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFBO0lBQ2pDLENBQUM7Ozs7SUFDRCxJQUFJLE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7SUFDekIsQ0FBQzs7Ozs7SUFDRCxJQUNJLFVBQVUsQ0FBQyxLQUFLO1FBQ2hCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtZQUM3QixJQUFJLEtBQUssS0FBSyxNQUFNLEVBQUU7Z0JBQ2xCLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDO2FBQzNCO2lCQUFNO2dCQUNILElBQUksQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDO2FBQzVCO1NBQ0o7YUFBTTtZQUNILElBQUksQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDO1NBQzVCO0lBQ0wsQ0FBQzs7OztJQUNELElBQUksVUFBVTtRQUNWLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztJQUM1QixDQUFDOzs7OztJQUNELFVBQVUsQ0FBQyxLQUFVO1FBQ2pCLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUM3RCxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7U0FDL0Q7YUFDSTtZQUNELElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztTQUM3RDtRQUFBLENBQUM7SUFDTixDQUFDOzs7O0lBQ0Qsa0JBQWtCO1FBQ2QsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO1FBQzNDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQ3RDLENBQUM7Ozs7O0lBQ0QsT0FBTyxDQUFDLEtBQUs7UUFDVCxZQUFZLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUM7UUFDbkMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDM0IsQ0FBQzs7Ozs7SUFDRCxhQUFhLENBQUMsS0FBSztRQUNmLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFO1lBQ3pCLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ2hDO0lBQ0wsQ0FBQzs7O1lBeHBCSixTQUFTLFNBQUM7Z0JBQ1AsUUFBUSxFQUFFLFNBQVM7Z0JBQ25CLFFBQVEsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0F1Qlg7eUJBQ1U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FzRVo7YUFDQTs7OztZQTdHOEUsVUFBVTtZQUloRixhQUFhO1lBSitGLFNBQVM7OztxQkF1SnpILE1BQU0sU0FBQyxXQUFXO3NCQUVsQixLQUFLLFNBQUMsU0FBUzswQkFDZixLQUFLLFNBQUMsY0FBYztpQ0FDcEIsS0FBSyxTQUFDLG9CQUFvQjtxQ0FFMUIsS0FBSztpQkFnQkwsS0FBSyxZQUNMLEtBQUssU0FBQyxJQUFJOzRCQStDVixLQUFLOzhCQWlDTCxLQUFLO29CQWtCTCxLQUFLO3FCQWdCTCxLQUFLOzJCQWNMLFNBQVMsU0FBQyxjQUFjO21CQUN4QixTQUFTLFNBQUMsTUFBTTtzQkF1VWhCLEtBQUs7eUJBV0wsS0FBSzs7Ozs7OztJQWxoQk4sbUNBQW1DOzs7OztJQUNuQyxpQ0FBNEI7Ozs7O0lBQzVCLDhCQUFnQzs7Ozs7SUFDaEMsMEJBQXdCOzs7OztJQUN4QixpQ0FBNEI7Ozs7O0lBQzVCLGdDQUFzQjs7Ozs7SUFDdEIsZ0NBQTJCOzs7OztJQUMzQix5QkFBdUI7Ozs7O0lBQ3ZCLDJCQUFnQzs7Ozs7SUFDaEMsMENBQXdDOzs7OztJQUN4QyxnQ0FBK0I7Ozs7O0lBQy9CLGtDQUFzQzs7SUFDdEMsK0JBQTZCOztJQUM3QixvQ0FBa0M7Ozs7O0lBQ2xDLCtCQUF3Qjs7Ozs7SUFDeEIsd0JBQWlCOzs7OztJQUNqQiw2QkFBa0I7Ozs7O0lBd0JsQix5QkFBOEQ7O0lBRTlELDBCQUF1Qzs7SUFDdkMsOEJBQW9EOztJQUNwRCxxQ0FBZ0U7O0lBaUJoRSw0QkFBMEI7O0lBQzFCLHFCQUM2Qjs7SUFnSTdCLCtCQUFvRDs7SUFDcEQsdUJBQW9DOzs7OztJQS9LeEIsdUJBQXdCOzs7OztJQUFFLGdDQUFvQzs7Ozs7SUFBRSw0QkFBNEIiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb21wb25lbnQsIE9uSW5pdCwgSW5wdXQsIE9uQ2hhbmdlcywgT3V0cHV0LCBFdmVudEVtaXR0ZXIsIFZpZXdDaGlsZCwgRWxlbWVudFJlZiwgQ29tcG9uZW50RmFjdG9yeVJlc29sdmVyLCBSZW5kZXJlcjIgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcbmltcG9ydCB7IEVsZW1lbnREZWYgfSBmcm9tICdAYW5ndWxhci9jb3JlL3NyYy92aWV3Jztcbi8vaW1wb3J0IHsgU3d0QWJzdHJhY3QgfSBmcm9tIFwiLi9zd3QtYWJzdHJhY3RcIjtcbmltcG9ydCB7IENvbnRhaW5lciB9IGZyb20gXCIuLi9jb250YWluZXJzL3N3dC1jb250YWluZXIuY29tcG9uZW50XCI7XG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XG5pbXBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tICcuLi9tYW5hZ2Vycy9mb2N1cy1tYW5hZ2VyLnNlcnZpY2UnO1xuLy8gZXZlbnQudGFyZ2V0Lm9mZnNldFdpZHRoXG4vLyBldmVudC50YXJnZXQuc2Nyb2xsV2lkdGhcbi8qIFxuICAgIFN3dExpc3QgXG4gICAgQEF1dGhvciBLaGFsaWwuQiBTd2FsbG93IFROIPCfmI5cbiovXG5AQ29tcG9uZW50KHtcbiAgICBzZWxlY3RvcjogJ1N3dExpc3QnLFxuICAgIHRlbXBsYXRlOiBgXG4gICAgPGRpdiAjbGlzdCBpZD1cImVjcmFuXCIgY2xhc3M9XCJib3hib3hcIiBbbmdDbGFzc109XCJ7J2Rpc2FibCc6ZW5hYmxlZD09ZmFsc2V9XCI+XG4gICAgPHVsICNzd3RsaXN0SXRlbXMgY2xhc3M9XCJsaXN0LWdyb3VwXCIgKHdpbmRvdzprZXlkb3duKT1cInRlc3RrZXkoJGV2ZW50KVwiPlxuICAgICAgICA8ZGl2ICpuZ0lmPVwiZW5hYmxlZD09dHJ1ZSYmc2VsZWN0YWJsZT09dHJ1ZVwiPlxuICAgICAgICAgICAgPGxpIChjbGljayk9XCJjbGlja0hhbmRsZXIoJGV2ZW50LGkpO29uQ2xpY2soJGV2ZW50KVwiIChtb3VzZWVudGVyKT1cInNldFRvb2xUaXAoJGV2ZW50LGl0ZW0pXCJcbiAgICAgICAgICAgICAgICBkYXRhLXBsYWNlbWVudD1cInJpZ2h0XCIgKGRibGNsaWNrKT1cImRvdWJsZUNsaWNrZWQoJGV2ZW50KVwiIGNsYXNzPVwibGlzdC1ncm91cC1pdGVtXCJcbiAgICAgICAgICAgICAgICBbbmdDbGFzc109XCJ7YWN0aXZlOmNvbG9yZWRJbmRleFtpXT09dHJ1ZSxob3Zlcjpjb2xvcmVkSW5kZXghPWksbXVsdGlwbGVTZWxlY3ROYXY6bXVsdGlwbGVTZWxlY3ROYXZbaV09PXRydWV9XCJcbiAgICAgICAgICAgICAgICAqbmdGb3I9XCJsZXQgaXRlbSBvZiBkYXRhUHJvdmlkZXI7IGxldCBpID0gaW5kZXhcIj5cbiAgICAgICAgICAgICAgICA8cCAgPnt7IGl0ZW0uY29udGVudCB9fTwvcD5cbiAgICAgICAgICAgICAgICA8aW1nICpuZ0lmPVwiaXRlbS5pbWdcIiBzcmM9XCJ7eyBpdGVtLmltZyB9fVwiICBzdHlsZT1cImhlaWdodDoyMHB4O3dpZHRoOjIwcHhcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzcz1cImljb24taXRlbVwiPlxuICAgICAgICAgICAgPC9saT5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgKm5nSWY9XCIhZW5hYmxlZCB8fCAhc2VsZWN0YWJsZVwiPlxuICAgICAgICAgICAgPGxpICpuZ0Zvcj1cImxldCBpdGVtIG9mIGRhdGFQcm92aWRlcjsgbGV0IGkgPSBpbmRleFwiIGNsYXNzPVwibGlzdC1ncm91cC1pdGVtLWRpc2FibGVkXCIgKG1vdXNlZW50ZXIpPVwic2V0VG9vbFRpcCgkZXZlbnQsaXRlbSlcIlxuICAgICAgICAgICAgICAgICBkYXRhLXBsYWNlbWVudD1cInJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgPHA+e3sgaXRlbS5jb250ZW50IH19PC9wPiBcbiAgICAgICAgICAgICAgICA8aW1nICpuZ0lmPVwiaXRlbS5pbWdcIiBzcmM9XCJ7eyBpdGVtLmltZyB9fVwiIHN0eWxlPVwiaGVpZ2h0OjIwcHg7d2lkdGg6MjBweFwiIGNsYXNzPVwiaXRlbS1pbWdcIj5cbiAgICAgICAgICAgIDwvbGk+XG4gICAgICAgIDwvZGl2PlxuICAgIDwvdWw+XG48L2Rpdj5cblxuICBgLFxuICAgIHN0eWxlczogW2BcbiAgICAuaWNvbi1pdGVte1xuICAgICAgICBmbG9hdDpyaWdodDsgXG4gICAgICAgIG1hcmdpbi1yaWdodDoxMHB4XG4gICAgfVxuICAgIC5pdGVtLWltZ3tcbiAgICAgICAgZmxvYXQ6cmlnaHQ7IFxuICAgICAgICBtYXJnaW4tcmlnaHQ6MTBweFxuICAgIH1cbiAgICAubGlzdC1ncm91cC1pdGVtLWRpc2FibGVke1xuICAgICAgICBoZWlnaHQ6IDI1cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgZm9udC1mYW1pbHk6IHZlcmRhbmEsaGVsdmV0aWNhO1xuICAgICAgICBwYWRkaW5nOiAwcHggMHB4IDBweCAxMHB4O1xuICAgICAgICBtYXJnaW46IDBweDtcbiAgICAgICAgbGluZS1oZWlnaHQ6IDI1cHg7XG4gICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7IFxuICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgICBvdmVyZmxvdzpoaWRkZW5cbiAgICB9XG4gICAgLmxpc3QtZ3JvdXAtaXRlbS5hY3RpdmUsIC5saXN0LWdyb3VwLWl0ZW0uYWN0aXZlOmZvY3VzLCAubGlzdC1ncm91cC1pdGVtLmFjdGl2ZTpob3ZlciB7XG4gICAgICAgIGNvbG9yOiBibGFjazsgIFxuICAgIH0gXG4gICAgLmxpc3QtZ3JvdXAtaXRlbS5hY3RpdmV7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM3RkNFRkY7XG4gICAgfVxuICAgIC5saXN0LWdyb3VwLWl0ZW0ubXVsdGlwbGVTZWxlY3ROYXZ7XG4gICAgICAgIGJvcmRlci1jb2xvcjogIzdGQ0VGRjtcbiAgICAgICAgYm9yZGVyLXdpZHRoOiB0aGluO1xuICAgICAgICBib3JkZXItc3R5bGU6c29saWQ7XG4gICAgfVxuICAgIC5saXN0LWdyb3VwLWl0ZW06Zm9jdXMge1xuICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmVkOyBcbiAgICB9XG4gICAgLmxpc3QtZ3JvdXB7XG4gICAgICAgIG1hcmdpbi1ib3R0b206MHB4ICFpbXBvcnRhbnQ7XG4gICAgfVxuICAgIC5saXN0LWdyb3VwLWl0ZW06aG92ZXIge1xuICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNCMkUxRkY7XG4gICAgICAgY3Vyc29yOiBkZWZhdWx0O1xuICAgIH1cbiAgICAubGlzdC1ncm91cC1pdGVtIHtcbiAgICAgICAgaGVpZ2h0OiAyNXB4O1xuICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgIGZvbnQtZmFtaWx5OiB2ZXJkYW5hLGhlbHZldGljYTtcbiAgICAgICAgcGFkZGluZzogMHB4IDBweCAwcHggMTBweDtcbiAgICAgICAgbWFyZ2luOiAwcHg7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4O1xuICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyBcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgb3ZlcmZsb3c6aGlkZGVuXG4gICAgfVxuICAgIFxuICAgIC5ib3hib3h7XG4gICAgICAgIGhlaWdodDoxMDAlO1xuICAgICAgICB3aWR0aDoxMDAlO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOyBcbiAgICAgICAgb3ZlcmZsb3c6IGF1dG87XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xuICAgIH1cbiAgICAuYm94Ym94OmhvdmVye1xuICAgICAgICBjdXJzb3I6IGRlZmF1bHQ7XG4gICAgfVxuICAgIC5kaXNhYmx7XG4gICAgICAgIHotaW5kZXg6IDE7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNkZGQ7XG4gICAgICAgIG9wYWNpdHk6IDAuMztcbiAgICB9XG5gXVxufSlcbmV4cG9ydCBjbGFzcyBTd3RMaXN0IGV4dGVuZHMgQ29udGFpbmVyIGltcGxlbWVudHMgT25Jbml0IHtcbiAgICBwcml2YXRlIF9zZWxlY3RlZEluZGljZXM6IGFueSA9IFtdO1xuICAgIHByaXZhdGUgX3NlbGVjdGVkSW5kZXg6IGFueTtcbiAgICBwcml2YXRlIF9zZWxlY3RhYmxlOiBhbnkgPSB0cnVlO1xuICAgIHByaXZhdGUgX2hlaWdodDogc3RyaW5nO1xuICAgIHByaXZhdGUgX3NlbGVjdGVkSXRlbXMgPSBbXTtcbiAgICBwcml2YXRlIF9zZWxlY3RlZEl0ZW07XG4gICAgcHJpdmF0ZSBTd3RMaXN0T2JqZWN0OiBhbnk7XG4gICAgcHJpdmF0ZSBfd2lkdGg6IHN0cmluZztcbiAgICBwcml2YXRlIF9lbmFibGVkOiBib29sZWFuID0gdHJ1ZVxuICAgIHByaXZhdGUgX2FsbG93TXVsdGlwbGVTZWxlY3Rpb24gPSBmYWxzZTtcbiAgICBwcml2YXRlIF9kYXRhUHJvdmlkZXI6IGFueSA9IFtdXG4gICAgcHJpdmF0ZSBkYXRhUHJvdmlkZXJPbGQgPSBuZXcgQXJyYXkoKTtcbiAgICBwdWJsaWMgY29sb3JlZEluZGV4ID0gW2ZhbHNlXVxuICAgIHB1YmxpYyBtdWx0aXBsZVNlbGVjdE5hdiA9IFtmYWxzZV1cbiAgICBwcml2YXRlIGZvY3VzZWRJbmRleCA9IDBcbiAgICBwcml2YXRlIGluZGV4ID0gMFxuICAgIHByaXZhdGUgZmlyc3RTaG93blxuICAgIGNvbnN0cnVjdG9yKHByaXZhdGUgZWxlbTogRWxlbWVudFJlZiwgcHJpdmF0ZSBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlLCBwcml2YXRlIF9yZW5kZXJlcjogUmVuZGVyZXIyKSB7XG4gICAgICAgIHN1cGVyKGVsZW0sIGNvbW1vblNlcnZpY2UpO1xuICAgIH1cbiAgICBuZ09uSW5pdCgpIHtcbiAgICAgICAgdGhpcy5kYXRhUHJvdmlkZXJPbGQgPSBbXVxuICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWUgPSBbXVxuICAgICAgICB0aGlzLmZpcnN0U2hvd24gPSAwXG4gICAgICAgIGlmICh0aGlzLmVuYWJsZWQgPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmVuYWJsZWQgPSB0cnVlXG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0YWJsZSA9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0YWJsZSA9IHRydWVcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuZGF0YVByb3ZpZGVyKSB7XG4gICAgICAgICAgICB0aGlzLmRhdGFQcm92aWRlciA9IFtdXG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZW5hYmxlZCA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAxOyBpbmRleCA8IHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4W2luZGV4XSA9IGZhbHNlXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyogc2V0IGNoYW5nZSBoYW5kbGVyICovXG4gICAgQE91dHB1dCgnaXRlbUNsaWNrJykgcHJpdmF0ZSBjbGlja18gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcbi8vICAgIEBPdXRwdXQoJ2RvdWJsZUNsaWNrJykgcHJpdmF0ZSBkb3VibGVDbGlja18gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcbiAgICBASW5wdXQoJ3Rvb2xUaXAnKSB0b29sVGlwOiBzdHJpbmcgPSBcIlwiO1xuICAgIEBJbnB1dCgnc2hvd0RhdGFUaXBzJykgc2hvd0RhdGFUaXA6IGJvb2xlYW4gPSBmYWxzZTtcbiAgICBASW5wdXQoJ2RvdWJsZUNsaWNrRW5hYmxlZCcpIGRvdWJsZUNsaWNrRW5hYmxlZDogYm9vbGVhbiA9IHRydWU7XG4gICAgLyogQElucHV0cyAqL1xuICAgIEBJbnB1dCgpXG4gICAgc2V0IGFsbG93TXVsdGlwbGVTZWxlY3Rpb24odmFsdWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiAodmFsdWUpID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IFwidHJ1ZVwiKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fYWxsb3dNdWx0aXBsZVNlbGVjdGlvbiA9IHRydWU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuX2FsbG93TXVsdGlwbGVTZWxlY3Rpb24gPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuX2FsbG93TXVsdGlwbGVTZWxlY3Rpb24gPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgYWxsb3dNdWx0aXBsZVNlbGVjdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2FsbG93TXVsdGlwbGVTZWxlY3Rpb247XG4gICAgfVxuICAgIGZpcnN0Q2FsbDogYm9vbGVhbiA9IHRydWU7XG4gICAgQElucHV0KClcbiAgICBASW5wdXQoXCJpZFwiKSBpZDogc3RyaW5nID0gXCJcIjtcbiAgICBzZXQgZGF0YVByb3ZpZGVyKHZhbHVlKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB0aGlzLl9kYXRhUHJvdmlkZXIgPSBbXVxuICAgICAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCA9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICB2YWx1ZSA9IFt2YWx1ZV1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgoIXZhbHVlKSB8fCAoIXZhbHVlWzBdKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX2RhdGFQcm92aWRlciA9IFtdXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZmlyc3RDYWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMub3JpZ2luYWxWYWx1ZSA9IFtdO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZpcnN0Q2FsbCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHZhbHVlWzBdLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHZhciB0ZW1wID0geyBcInR5cGVcIjogXCJcIiwgXCJ2YWx1ZVwiOiBcIlwiLCBcInNlbGVjdGVkXCI6IDAsIFwiY29udGVudFwiOiBcIlwiIH1cbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdmFsdWUubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICAgICAgICAgIHRlbXAudmFsdWUgPSB2YWx1ZVtpbmRleF1cbiAgICAgICAgICAgICAgICAgICAgdGVtcC5jb250ZW50ID0gdmFsdWVbaW5kZXhdXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2RhdGFQcm92aWRlcltpbmRleF0gPSB7IC4uLnRlbXAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5maXJzdENhbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub3JpZ2luYWxWYWx1ZVtpbmRleF0gPSB7IC4uLnRlbXAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGluZGV4ID09IHZhbHVlLmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZpcnN0Q2FsbCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fZGF0YVByb3ZpZGVyID0gWy4uLnZhbHVlXTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5maXJzdENhbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gWy4uLnZhbHVlXTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5maXJzdENhbGwgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLm9uQ2hhbmdlKGV2ZW50KVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0IGRhdGFQcm92aWRlcigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2RhdGFQcm92aWRlcjtcbiAgICB9XG4gICAgc2V0VG9vbFRpcChldmVudCwgaXRlbSkge1xuICAgICAgICB0aGlzLnRvb2xUaXAgPSB0aGlzLnNob3dEYXRhVGlwICYmIGV2ZW50LnRhcmdldC5zY3JvbGxXaWR0aCA+IGV2ZW50LnRhcmdldC5vZmZzZXRXaWR0aCA/IGl0ZW0uY29udGVudCA6ICcnXG4gICAgfVxuICAgIEBJbnB1dCgpXG4gICAgc2V0IHNlbGVjdGVkSW5kZXgodmFsdWUpIHtcbiAgICAgICAgdGhpcy5jb2xvcmVkSW5kZXguZmlsbChmYWxzZSlcbiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdE5hdi5maWxsKGZhbHNlKVxuICAgICAgICBpZiAodmFsdWUgIT0gdW5kZWZpbmVkICYmIHZhbHVlICE9IG51bGwgJiYgdmFsdWUgIT0gLTEpIHtcbiAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4W3ZhbHVlXSA9IHRydWVcbiAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3ROYXZbdmFsdWVdID0gdHJ1ZVxuICAgICAgICAgICAgdGhpcy5mb2N1c2VkSW5kZXggPSB2YWx1ZVxuICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRleCA9IHZhbHVlXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEl0ZW0gPSB0aGlzLmRhdGFQcm92aWRlclt2YWx1ZV0udmFsdWVcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJdGVtID0gbnVsbFxuICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRleCA9IC0xXG4gICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kaWNlcyA9IFtdXG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0IHNlbGVjdGVkSW5kZXgoKSB7XG4gICAgICAgIGxldCBmb3VuZCA9IGZhbHNlLCBpID0gMFxuICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEluZGljZXMubGVuZ3RoIDw9IDEpIHtcbiAgICAgICAgICAgIHdoaWxlIChpIDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoICYmICFmb3VuZCkge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmNvbG9yZWRJbmRleFtpXSA9PSB0cnVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kZXggPSBpXG4gICAgICAgICAgICAgICAgICAgIGZvdW5kID0gdHJ1ZVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpKytcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghZm91bmQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEluZGV4ID0gLTFcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fc2VsZWN0ZWRJbmRleDtcbiAgICB9XG4gICAgQElucHV0KClcbiAgICBzZXQgc2VsZWN0ZWRJbmRpY2VzKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuY29sb3JlZEluZGV4LmZpbGwoZmFsc2UpXG4gICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB2YWx1ZS5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4W3ZhbHVlW2luZGV4XV0gPSB0cnVlXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRpY2VzID0gWy4uLnZhbHVlXVxuICAgIH1cbiAgICBnZXQgc2VsZWN0ZWRJbmRpY2VzKCkge1xuICAgICAgICB0aGlzLl9zZWxlY3RlZEluZGljZXMgPSBbXVxuICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5jb2xvcmVkSW5kZXhbaW5kZXhdID09IHRydWUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEluZGljZXMucHVzaChpbmRleClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fc2VsZWN0ZWRJbmRpY2VzO1xuICAgIH1cbiAgICAvKiB3aWR0aCBnZXR0ZXIgYW5kIHNldHRlciAqL1xuICAgIEBJbnB1dCgpXG4gICAgc2V0IHdpZHRoKHZhbHVlOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKHZhbHVlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKCclJykgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fd2lkdGggPSB2YWx1ZSArIFwicHhcIjtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fd2lkdGggPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9yZW5kZXJlci5zZXRTdHlsZSh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCwgJ3dpZHRoJywgdGhpcy5fd2lkdGgpXG4gICAgfVxuICAgIGdldCB3aWR0aCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3dpZHRoO1xuICAgIH1cbiAgICAvKiBASW5wdXRzICovXG4gICAgLyogaGVpZ2h0IGdldHRlciBhbmQgc2V0dGVyICovXG4gICAgQElucHV0KClcbiAgICBzZXQgaGVpZ2h0KHZhbHVlOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKHZhbHVlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKCclJykgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5faGVpZ2h0ID0gdmFsdWUgKyBcInB4XCI7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuX2hlaWdodCA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuX3JlbmRlcmVyLnNldFN0eWxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LCAnaGVpZ2h0JywgdGhpcy5faGVpZ2h0KVxuICAgIH1cbiAgICBnZXQgaGVpZ2h0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5faGVpZ2h0O1xuICAgIH1cbiAgICBAVmlld0NoaWxkKFwic3d0bGlzdEl0ZW1zXCIpIHN3dGxpc3RJdGVtczogRWxlbWVudFJlZjtcbiAgICBAVmlld0NoaWxkKFwibGlzdFwiKSBsaXN0OiBFbGVtZW50UmVmO1xuICAgIG9uQ2hhbmdlKGV2ZW50KSB7XG4gICAgICAgIGlmICh0aGlzLmRhdGFQcm92aWRlci5sZW5ndGggPT0gdGhpcy5kYXRhUHJvdmlkZXJPbGQubGVuZ3RoKSB7XG4gICAgICAgICAgICBsZXQgY2hhbmdlOiBib29sZWFuID0gIShKU09OLnN0cmluZ2lmeSh0aGlzLmRhdGFQcm92aWRlcikgPT09IEpTT04uc3RyaW5naWZ5KHRoaXMuZGF0YVByb3ZpZGVyT2xkKSk7XG4gICAgICAgICAgICBpZiAoY2hhbmdlKSB7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmRhdGFQcm92aWRlck9sZFtpbmRleF0gPSB0aGlzLmRhdGFQcm92aWRlcltpbmRleF1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VfLmVtaXQodGhpcy5kYXRhUHJvdmlkZXIpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmNoYW5nZV8uZW1pdCh0aGlzLmRhdGFQcm92aWRlcilcbiAgICAgICAgICAgIGlmICh0aGlzLmRhdGFQcm92aWRlci5sZW5ndGggPT0gMCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyT2xkID0gW11cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kYXRhUHJvdmlkZXJPbGRbaW5kZXhdID0gdGhpcy5kYXRhUHJvdmlkZXJbaW5kZXhdXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuc3B5Q2hhbmdlcyh0aGlzLmRhdGFQcm92aWRlcik7XG4gICAgfVxuICAgIC8vIGZ1bmN0aW9uIHVzZWQgdG8gaGFuZGxlIGtleWJvcmRzIGtleXMgZXZlbnRzXG4gICAgdGVzdGtleShldmVudCkge1xuICAgICAgICBpZiAoZXZlbnQua2V5Q29kZSA9PT0gMzgpIHtcbiAgICAgICAgICAgIGlmICghZXZlbnQuY3RybEtleSkge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvY3VzZWRJbmRleCAhPSB0aGlzLnNlbGVjdGVkSW5kZXgpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gdGhpcy5mb2N1c2VkSW5kZXhcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnNjcm9sbFRvVG9wKCk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbmRleCAhPSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleC0tO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGV2ZW50LmtleUNvZGUgPT09IDM4ICYmIGV2ZW50LmN0cmxLZXkgJiYgdGhpcy5hbGxvd011bHRpcGxlU2VsZWN0aW9uKSB7XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnNjcm9sbEZvY3VzVG9Ub3AoKTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5mb2N1c2VkSW5kZXggIT0gMCkge1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0TmF2W2luZGV4XSA9IGZhbHNlXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdE5hdlt0aGlzLmZvY3VzZWRJbmRleCAtIDFdID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmZvY3VzZWRJbmRleCA9IHRoaXMuZm9jdXNlZEluZGV4IC0gMVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChldmVudC5rZXlDb2RlID09PSA0MCkge1xuICAgICAgICAgICAgaWYgKCFldmVudC5jdHJsS2V5KSB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9jdXNlZEluZGV4ICE9IHRoaXMuc2VsZWN0ZWRJbmRleCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSB0aGlzLmZvY3VzZWRJbmRleFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIHRoaXMuc2Nyb2xsVG9Cb3R0b20oKTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEluZGV4IDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoIC0gMSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXgrKztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChldmVudC5rZXlDb2RlID09PSA0MCAmJiBldmVudC5jdHJsS2V5ICYmIHRoaXMuYWxsb3dNdWx0aXBsZVNlbGVjdGlvbikge1xuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxGb2N1c1RvQm90dG9tKCk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9jdXNlZEluZGV4IDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoIC0gMSkge1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0TmF2W2luZGV4XSA9IGZhbHNlXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdE5hdlt0aGlzLmZvY3VzZWRJbmRleCArIDFdID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmZvY3VzZWRJbmRleCA9IHRoaXMuZm9jdXNlZEluZGV4ICsgMVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoKGV2ZW50LmN0cmxLZXkgJiYgZXZlbnQua2V5Q29kZSA9PSAzMikgJiYgdGhpcy5hbGxvd011bHRpcGxlU2VsZWN0aW9uID09PSB0cnVlKSB7XG4gICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleFt0aGlzLmZvY3VzZWRJbmRleF0gPSAhdGhpcy5jb2xvcmVkSW5kZXhbdGhpcy5mb2N1c2VkSW5kZXhdXG4gICAgICAgICAgICBpZiAodGhpcy5jb2xvcmVkSW5kZXhbdGhpcy5mb2N1c2VkSW5kZXhdKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGljZXMucHVzaCh0aGlzLmZvY3VzZWRJbmRleClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gaGFuZGxlIHNjcm9sbGluZ1xuICogdG8gYm90dG9tLlxuICovXG4gICAgcHJpdmF0ZSBzY3JvbGxUb0JvdHRvbSgpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIG1ha2UgcmVmZXJlbmNlIHRvIGxpc3QgaXRlbXMuXG4gICAgICAgICAgICBsZXQgbGlzdCA9IHRoaXMubGlzdC5uYXRpdmVFbGVtZW50O1xuICAgICAgICAgICAgLy8gZ2V0IGxpc3QgaXRlbXMgaGVpZ2h0LlxuICAgICAgICAgICAgbGV0IGxpc3RIZWlnaHQgPSBsaXN0Lm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgIC8vIGdldCBpdGVtIGluZGV4LlxuICAgICAgICAgICAgbGV0IGl0ZW1JbmRleCA9IHRoaXMuc2VsZWN0ZWRJbmRleCArIDE7XG4gICAgICAgICAgICAvLyBpZiBpdGVtIGluZGV4ID4gLTEgZG8uXG4gICAgICAgICAgICBpZiAoaXRlbUluZGV4ID4gLTEpIHtcbiAgICAgICAgICAgICAgICAvLyBnZXQgY3VycmVudCBpdGVtLlxuICAgICAgICAgICAgICAgIGxldCBpdGVtID0gbGlzdC5jaGlsZHJlblswXS5jaGlsZHJlblswXS5jaGlsZHJlbltpdGVtSW5kZXhdXG4gICAgICAgICAgICAgICAgLy8gY2hlY2sgZXhpc3RlbmNlIG9mIGl0ZW0uXG4gICAgICAgICAgICAgICAgaWYgKGl0ZW0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBnZXQgaXRlbSBoZWlnaHQuXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtSGVpZ2h0ID0gaXRlbS5vZmZzZXRIZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSBpdGVtIHRvcCBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1Ub3AgPSBpdGVtSW5kZXggKiBpdGVtSGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgaXRlbSBib3R0b20gcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtQm90dG9tID0gaXRlbVRvcCArIGl0ZW1IZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSB0aGUgc2Nyb2xsIHZhbHVlLlxuICAgICAgICAgICAgICAgICAgICBsZXQgdmlld0JvdHRvbSA9IGxpc3Quc2Nyb2xsVG9wICsgbGlzdEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gdXBkYXRlIHNjcm9sbCBiYXIgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtQm90dG9tID4gdmlld0JvdHRvbSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGlzdC5zY3JvbGxUb3AgPSBpdGVtQm90dG9tIC0gbGlzdEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBoYW5kbGUgc2Nyb2xsaW5nXG4gICogdG8gVG9wLlxuICAqL1xuICAgIHByaXZhdGUgc2Nyb2xsVG9Ub3AoKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBtYWtlIHJlZmVyZW5jZSB0byBsaXN0IGl0ZW1zLlxuICAgICAgICAgICAgbGV0IGxpc3QgPSB0aGlzLmxpc3QubmF0aXZlRWxlbWVudDtcbiAgICAgICAgICAgIC8vIGdldCBsaXN0IGl0ZW1zIGhlaWdodC5cbiAgICAgICAgICAgIGxldCBsaXN0SGVpZ2h0ID0gbGlzdC5vZmZzZXRIZWlnaHQ7XG4gICAgICAgICAgICAvLyBnZXQgaXRlbSBpbmRleC5cbiAgICAgICAgICAgIGxldCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdGVkSW5kZXggLSAxO1xuICAgICAgICAgICAgLy8gaWYgaXRlbSBpbmRleCA+IC0xIGRvLlxuICAgICAgICAgICAgaWYgKGl0ZW1JbmRleCA+IC0xKSB7XG4gICAgICAgICAgICAgICAgLy8gZ2V0IGN1cnJlbnQgaXRlbS5cbiAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IGxpc3QuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5baXRlbUluZGV4XVxuICAgICAgICAgICAgICAgIC8vIGNoZWNrIGV4aXN0ZW5jZSBvZiBpdGVtLlxuICAgICAgICAgICAgICAgIGlmIChpdGVtICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gZ2V0IGl0ZW0gaGVpZ2h0LlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUhlaWdodCA9IGl0ZW0ub2Zmc2V0SGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgaXRlbSB0b3AgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtVG9wID0gaXRlbUluZGV4ICogaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gYm90dG9tIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUJvdHRvbSA9IGl0ZW1Ub3AgKyBpdGVtSGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyB1cGRhdGUgc2Nyb2xsIGJhciBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW1Ub3AgPCBsaXN0LnNjcm9sbFRvcCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGlzdC5zY3JvbGxUb3AgPSBpdGVtVG9wO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcilcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGhhbmRsZSBzY3JvbGxpbmdcbiAqIHRvIGJvdHRvbS5cbiAqL1xuICAgIHByaXZhdGUgc2Nyb2xsRm9jdXNUb0JvdHRvbSgpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIG1ha2UgcmVmZXJlbmNlIHRvIGxpc3QgaXRlbXMuXG4gICAgICAgICAgICBsZXQgbGlzdCA9IHRoaXMubGlzdC5uYXRpdmVFbGVtZW50O1xuICAgICAgICAgICAgLy8gZ2V0IGxpc3QgaXRlbXMgaGVpZ2h0LlxuICAgICAgICAgICAgbGV0IGxpc3RIZWlnaHQgPSBsaXN0Lm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgIC8vIGdldCBpdGVtIGluZGV4LlxuICAgICAgICAgICAgbGV0IGl0ZW1JbmRleCA9IHRoaXMuZm9jdXNlZEluZGV4ICsgMTtcbiAgICAgICAgICAgIC8vIGlmIGl0ZW0gaW5kZXggPiAtMSBkby5cbiAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkge1xuICAgICAgICAgICAgICAgIC8vIGdldCBjdXJyZW50IGl0ZW0uXG4gICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSBsaXN0LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuW2l0ZW1JbmRleF1cbiAgICAgICAgICAgICAgICAvLyBjaGVjayBleGlzdGVuY2Ugb2YgaXRlbS5cbiAgICAgICAgICAgICAgICBpZiAoaXRlbSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGdldCBpdGVtIGhlaWdodC5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1IZWlnaHQgPSBpdGVtLm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gdG9wIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbVRvcCA9IGl0ZW1JbmRleCAqIGl0ZW1IZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSBpdGVtIGJvdHRvbSBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1Cb3R0b20gPSBpdGVtVG9wICsgaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIHRoZSBzY3JvbGwgdmFsdWUuXG4gICAgICAgICAgICAgICAgICAgIGxldCB2aWV3Qm90dG9tID0gbGlzdC5zY3JvbGxUb3AgKyBsaXN0SGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyB1cGRhdGUgc2Nyb2xsIGJhciBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW1Cb3R0b20gPiB2aWV3Qm90dG9tKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsaXN0LnNjcm9sbFRvcCA9IGl0ZW1Cb3R0b20gLSBsaXN0SGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcilcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGhhbmRsZSBzY3JvbGxpbmdcbiAgKiB0byBUb3AuXG4gICovXG4gICAgcHJpdmF0ZSBzY3JvbGxGb2N1c1RvVG9wKCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gbWFrZSByZWZlcmVuY2UgdG8gbGlzdCBpdGVtcy5cbiAgICAgICAgICAgIGxldCBsaXN0ID0gdGhpcy5saXN0Lm5hdGl2ZUVsZW1lbnQ7XG4gICAgICAgICAgICAvLyBnZXQgbGlzdCBpdGVtcyBoZWlnaHQuXG4gICAgICAgICAgICBsZXQgbGlzdEhlaWdodCA9IGxpc3Qub2Zmc2V0SGVpZ2h0O1xuICAgICAgICAgICAgLy8gZ2V0IGl0ZW0gaW5kZXguXG4gICAgICAgICAgICBsZXQgaXRlbUluZGV4ID0gdGhpcy5mb2N1c2VkSW5kZXggLSAxO1xuICAgICAgICAgICAgLy8gaWYgaXRlbSBpbmRleCA+IC0xIGRvLlxuICAgICAgICAgICAgaWYgKGl0ZW1JbmRleCA+IC0xKSB7XG4gICAgICAgICAgICAgICAgLy8gZ2V0IGN1cnJlbnQgaXRlbS5cbiAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IGxpc3QuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5baXRlbUluZGV4XVxuICAgICAgICAgICAgICAgIC8vIGNoZWNrIGV4aXN0ZW5jZSBvZiBpdGVtLlxuICAgICAgICAgICAgICAgIGlmIChpdGVtICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gZ2V0IGl0ZW0gaGVpZ2h0LlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUhlaWdodCA9IGl0ZW0ub2Zmc2V0SGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgaXRlbSB0b3AgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtVG9wID0gaXRlbUluZGV4ICogaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gYm90dG9tIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUJvdHRvbSA9IGl0ZW1Ub3AgKyBpdGVtSGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyB1cGRhdGUgc2Nyb2xsIGJhciBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW1Ub3AgPCBsaXN0LnNjcm9sbFRvcCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGlzdC5zY3JvbGxUb3AgPSBpdGVtVG9wO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcilcbiAgICAgICAgfVxuICAgIH1cbiAgICBhZGRJdGVtKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyLnB1c2godmFsdWUpXG4gICAgICAgIHRoaXMub25DaGFuZ2UoZXZlbnQpXG4gICAgfVxuICAgIC8vIHJlbW92ZSBzaW5nbGUgaXRlbSB1c2luZyBpdCdzIG93biBpbmRleFxuICAgIHJlbW92ZUl0ZW0oaW5kZXgpIHtcbiAgICAgICAgdGhpcy5kYXRhUHJvdmlkZXIuc3BsaWNlKGluZGV4LCAxKVxuICAgICAgICBpZiAodGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoID4gMCAmJiBpbmRleCA8IHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aCkge1xuICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gaW5kZXhcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpbmRleCA+IDApIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IGluZGV4IC0gMVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGluZGV4ID09IDApIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IC0xXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5vbkNoYW5nZShldmVudClcbiAgICB9XG4gICAgLy90aGlzIG1ldGhvZCBpcyB1c2VkIHRvIHJlbW92ZSBtdWx0aXBsZSBpdGVtcyBhdCBvbmNlIFxuICAgIC8vIEBwYXJhbTogaW5kZXhlczogYXJyYXkgb2YgaW5kZXhlc1xuICAgIHJlbW92ZUl0ZW1zKGluZGV4ZXMpIHtcbiAgICAgICAgbGV0IHggPSB0aGlzLmRhdGFQcm92aWRlclxuICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgaW5kZXhlcy5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgIGRlbGV0ZSB4W2luZGV4ZXNbaW5kZXhdXVxuICAgICAgICB9XG4gICAgICAgIHZhciBmaWx0ZXJlZCA9IHguZmlsdGVyKGZ1bmN0aW9uICh2YWx1ZSwgaW5kZXgsIGFycikge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlICE9IHVuZGVmaW5lZDtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyID0gZmlsdGVyZWRcbiAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gIGluZGV4ZXNbMF0gLSAxIFxuICAgIH1cbiAgICByZW1vdmVBbGwoKSB7XG4gICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyID0gW11cbiAgICAgICAgdGhpcy5vbkNoYW5nZShldmVudClcbiAgICB9XG4gICAgLy8gZnVuY3Rpb24gdXNlZCB0byBkbyBtdWx0aXBsZSBzZWxlY3Rpb24gd2l0aCBjdHJsICYgY2xpY2tcbiAgICBjbGlja0hhbmRsZXIoZXZlbnQsIGkpIHtcbiAgICAgICAgaWYgKHRoaXMuZW5hYmxlZCA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgaWYgKGV2ZW50LmN0cmxLZXkgJiYgdGhpcy5hbGxvd011bHRpcGxlU2VsZWN0aW9uID09IHRydWUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEl0ZW1zID0gbmV3IEFycmF5KCk7XG4gICAgICAgICAgICAgICAgdGhpcy5jb2xvcmVkSW5kZXhbaV0gPSAhdGhpcy5jb2xvcmVkSW5kZXhbaV1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGV2ZW50LnNoaWZ0S2V5ICYmIHRoaXMuYWxsb3dNdWx0aXBsZVNlbGVjdGlvbiA9PSB0cnVlICYmIHRoaXMuc2VsZWN0ZWRJbmRleCAhPSAtMSkge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkSW5kZXggPCBpKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCB0ZW1wID0gW11cbiAgICAgICAgICAgICAgICAgICAgbGV0IGxhc3RTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRJbmRleFxuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IGxhc3RTZWxlY3RlZDsgaW5kZXggPD0gaTsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGVtcC5wdXNoKGluZGV4KVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4LmZpbGwoZmFsc2UpXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRpY2VzID0gdGVtcFxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCB0ZW1wID0gW11cbiAgICAgICAgICAgICAgICAgICAgbGV0IGxhc3RTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRJbmRleFxuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IGk7IGluZGV4IDw9IGxhc3RTZWxlY3RlZDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGVtcC5wdXNoKGluZGV4KVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4LmZpbGwoZmFsc2UpXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRpY2VzID0gdGVtcFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy5jb2xvcmVkSW5kZXhbaW5kZXhdID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgLy8gfVxuICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSXRlbXMgPSBuZXcgQXJyYXkoKTtcbiAgICAgICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleC5maWxsKGZhbHNlLCAwLCB0aGlzLmNvbG9yZWRJbmRleC5sZW5ndGggLSAxKVxuICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IGk7XG4gICAgICAgICAgICAgICAgdGhpcy5pbmRleCA9IGlcbiAgICAgICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleFtpXSA9IHRydWVcbiAgICAgICAgICAgIH1cblxuICAgICAgICB9XG4gICAgfVxuICAgIHNldCBzZWxlY3RlZEl0ZW1zKHZhbHVlKSB7XG4gICAgfVxuICAgIGdldCBzZWxlY3RlZEl0ZW1zKCkge1xuICAgICAgICB0aGlzLl9zZWxlY3RlZEl0ZW1zID0gW11cbiAgICAgICAgaWYgKHRoaXMuZW5hYmxlZCA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmNvbG9yZWRJbmRleFtpbmRleF0gPT09IHRydWUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtcy5wdXNoKHRoaXMuZGF0YVByb3ZpZGVyW2luZGV4XSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fc2VsZWN0ZWRJdGVtcztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cbiAgICB9XG4gICAgc2V0IHNlbGVjdGVkSXRlbSh2YWx1ZSkge1xuICAgICAgICBsZXQgaSA9IDBcbiAgICAgICAgbGV0IGZvdW5kID0gZmFsc2VcbiAgICAgICAgd2hpbGUgKGkgPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGggJiYgIWZvdW5kKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5kYXRhUHJvdmlkZXJbaV0udmFsdWUgPT0gdmFsdWUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSBpXG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtID0gdGhpcy5kYXRhUHJvdmlkZXJbaV0udmFsdWVcbiAgICAgICAgICAgICAgICBmb3VuZCA9IHRydWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGkrK1xuICAgICAgICB9XG4gICAgICAgIGlmIChpID09IHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aCAtIDEgJiYgIWZvdW5kKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSAtMVxuICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtID0gbnVsbFxuICAgICAgICB9XG4gICAgfVxuICAgIGdldCBzZWxlY3RlZEl0ZW0oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zZWxlY3RlZEl0ZW07XG4gICAgfVxuICAgIEBJbnB1dCgpXG4gICAgc2V0IGVuYWJsZWQodmFsdWUpIHtcbiAgICAgICAgaWYgKHZhbHVlLnRvU3RyaW5nKCkgPT0gXCJ0cnVlXCIpIHtcbiAgICAgICAgICAgIHRoaXMuX2VuYWJsZWQgPSB0cnVlXG4gICAgICAgIH0gZWxzZVxuICAgICAgICAgICAgaWYgKHZhbHVlLnRvU3RyaW5nKCkgPT0gXCJmYWxzZVwiKVxuICAgICAgICAgICAgICAgIHRoaXMuX2VuYWJsZWQgPSBmYWxzZVxuICAgIH1cbiAgICBnZXQgZW5hYmxlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2VuYWJsZWQ7XG4gICAgfVxuICAgIEBJbnB1dCgpXG4gICAgc2V0IHNlbGVjdGFibGUodmFsdWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiAodmFsdWUpID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IFwidHJ1ZVwiKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0YWJsZSA9IHRydWU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGFibGUgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuX3NlbGVjdGFibGUgPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgc2VsZWN0YWJsZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3NlbGVjdGFibGU7XG4gICAgfVxuICAgIHNweUNoYW5nZXMoZXZlbnQ6IGFueSk6IHZvaWQge1xuICAgICAgICBpZiAoSlNPTi5zdHJpbmdpZnkodGhpcy5vcmlnaW5hbFZhbHVlKSA9PSBKU09OLnN0cmluZ2lmeShldmVudCkpIHtcbiAgICAgICAgICAgIHRoaXMub25TcHlOb0NoYW5nZS5lbWl0KHsgXCJ0YXJnZXRcIjogdGhpcywgXCJ2YWx1ZVwiOiBldmVudCB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMub25TcHlDaGFuZ2UuZW1pdCh7IFwidGFyZ2V0XCI6IHRoaXMsIFwidmFsdWVcIjogZXZlbnQgfSk7XG4gICAgICAgIH07XG4gICAgfVxuICAgIHJlc2V0T3JpZ2luYWxWYWx1ZSgpIHtcbiAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gWy4uLnRoaXMuZGF0YVByb3ZpZGVyXVxuICAgICAgICB0aGlzLnNweUNoYW5nZXModGhpcy5kYXRhUHJvdmlkZXIpXG4gICAgfVxuICAgIG9uQ2xpY2soZXZlbnQpIHtcbiAgICAgICAgZm9jdXNNYW5hZ2VyLmZvY3VzVGFyZ2V0ID0gdGhpcy5pZDtcbiAgICAgICAgdGhpcy5jbGlja18uZW1pdChldmVudClcbiAgICB9XG4gICAgZG91YmxlQ2xpY2tlZChldmVudCkge1xuICAgICAgICBpZiAodGhpcy5kb3VibGVDbGlja0VuYWJsZWQpIHtcbiAgICAgICAgICAgIHRoaXMuZG91YmxlQ2xpY2tfLmVtaXQoZXZlbnQpXG4gICAgICAgIH1cbiAgICB9XG59XG4iXX0=