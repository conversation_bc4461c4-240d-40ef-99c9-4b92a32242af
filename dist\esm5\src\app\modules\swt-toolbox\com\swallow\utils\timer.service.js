/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
var Timer = /** @class */ (function () {
    function Timer(duration, repeatCount) {
        if (repeatCount === void 0) { repeatCount = 0; }
        this.duration = duration;
        this._isRunning = false;
        this._callback = new Function();
    }
    /**
     * This method is used to start timer.
     */
    /**
     * This method is used to start timer.
     * @return {?}
     */
    Timer.prototype.start = /**
     * This method is used to start timer.
     * @return {?}
     */
    function () {
        var _this = this;
        this._timer = setInterval((/**
         * @return {?}
         */
        function () {
            if (_this._callback) {
                _this._callback();
            }
        }), this.duration);
        this._isRunning = true;
    };
    /**
     * This method is used to stop timer
     */
    /**
     * This method is used to stop timer
     * @return {?}
     */
    Timer.prototype.stop = /**
     * This method is used to stop timer
     * @return {?}
     */
    function () {
        clearInterval(this._timer);
        this._isRunning = false;
    };
    /**
     * This method is used to set timer delay.
     * @param number
     */
    /**
     * This method is used to set timer delay.
     * @param {?} duration
     * @return {?}
     */
    Timer.prototype.delay = /**
     * This method is used to set timer delay.
     * @param {?} duration
     * @return {?}
     */
    function (duration) {
        this.stop();
        this.duration = duration;
        this.start();
    };
    Object.defineProperty(Timer.prototype, "running", {
        /**
         * This method is used to check timer state
         * <code>timer.running()</code> if true the timer
         * is already lunched.
         */
        get: /**
         * This method is used to check timer state
         * <code>timer.running()</code> if true the timer
         * is already lunched.
         * @return {?}
         */
        function () {
            return this._isRunning;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to add event listener to
     * timer instance.
     * @param name
     * @param callBack
     */
    /**
     * This method is used to add event listener to
     * timer instance.
     * @param {?} name
     * @param {?} callBack
     * @return {?}
     */
    Timer.prototype.addEventListener = /**
     * This method is used to add event listener to
     * timer instance.
     * @param {?} name
     * @param {?} callBack
     * @return {?}
     */
    function (name, callBack) {
        this._callback = callBack;
    };
    Timer.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    Timer.ctorParameters = function () { return [
        { type: Number },
        { type: Number }
    ]; };
    return Timer;
}());
export { Timer };
if (false) {
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._isRunning;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._callback;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype._timer;
    /**
     * @type {?}
     * @private
     */
    Timer.prototype.duration;
}
//# sourceMappingURL=data:application/json;base64,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