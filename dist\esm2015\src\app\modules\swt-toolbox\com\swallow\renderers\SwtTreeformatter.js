/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { getDescendantProperty, htmlEncode } from 'angular-slickgrid';
import { isNegative, isClickable, CustomCell, isBold } from './cellItemRenderUtilities';
/** @type {?} */
export const SwttreeFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @param {?} grid
 * @return {?}
 */
(row, cell, value, columnDef, dataContext, grid) => {
    /** @type {?} */
    const dataView = grid && grid.getData();
    /** @type {?} */
    const gridOptions = grid && (/** @type {?} */ (grid.getOptions()));
    /** @type {?} */
    const treeDataOptions = gridOptions && gridOptions.treeDataOptions;
    /** @type {?} */
    const treeLevelPropName = treeDataOptions && treeDataOptions.levelPropName || '__treeLevel';
    /** @type {?} */
    const indentMarginLeft = treeDataOptions && treeDataOptions.indentMarginLeft || 15;
    /** @type {?} */
    let outputValue = value;
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    let negative = isNegative(dataContext, field);
    /** @type {?} */
    let isLink = isClickable(dataContext, field);
    /** @type {?} */
    let bold = columnDef['bold'] ? columnDef['bold'] : isBold(dataContext, field);
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    let type = columnDef['columnType'];
    if (style == "") {
        if (backgroundColor == undefined) {
            backgroundColor = 'transparent';
        }
        if (backgroundColor && backgroundColor.toString().indexOf('|') > -1) {
            // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
            /** @type {?} */
            const colorList = backgroundColor.split('|');
            style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
        }
        else {
            style += ' background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        }
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (columnDef.params.firstAfterSkipedColumn == true)
        style += ' padding-right: 5px; text-align: right; ';
    if (typeof columnDef.queryFieldNameGetterFn === 'function') {
        /** @type {?} */
        const fieldName = columnDef.queryFieldNameGetterFn(dataContext);
        if (fieldName && fieldName.indexOf('.') >= 0) {
            outputValue = getDescendantProperty(dataContext, fieldName);
        }
        else {
            outputValue = dataContext.hasOwnProperty(fieldName) ? dataContext[fieldName] : value;
        }
    }
    if (outputValue === null || outputValue === undefined || dataContext === undefined) {
        return '';
    }
    if (!dataContext.hasOwnProperty(treeLevelPropName)) {
        throw new Error('You must provide valid "treeDataOptions" in your Grid Options and it seems that there are no tree level found in this row');
    }
    if (dataView && dataView.getIdxById && dataView.getItemByIdx) {
        if (typeof outputValue === 'string') {
            outputValue = htmlEncode(outputValue);
        }
        /** @type {?} */
        const identifierPropName = dataView.getIdPropertyName() || 'id';
        /** @type {?} */
        const spacer = `<span style="display:inline-block; width:${indentMarginLeft * dataContext[treeLevelPropName]}px;"></span>`;
        /** @type {?} */
        const idx = dataView.getIdxById(dataContext[identifierPropName]);
        /** @type {?} */
        const nextItemRow = dataView.getItemByIdx(idx + 1);
        outputValue = columnDef.params.customContentFunction(dataContext, columnDef.field, outputValue, type);
        if (nextItemRow && nextItemRow[treeLevelPropName] > dataContext[treeLevelPropName]) {
            if (dataContext.__collapsed) {
                return `<div class="strFormatterDiv"  style='${style}' >${spacer}<span class="slick-group-toggle collapsed"></span>&nbsp;${isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue}</div>`;
            }
            else {
                return `<div class="strFormatterDiv"  style='${style}' >${spacer}<span class="slick-group-toggle expanded"></span>&nbsp;${isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue}</div>`;
            }
        }
        return `<div class="strFormatterDiv"  style='${style}' >${spacer}<span class="slick-group-toggle"></span>&nbsp;${isLink ? '<a class="strLinkRender"  >' + outputValue + '</a>' : outputValue}</div>`;
    }
    return '';
});
//# sourceMappingURL=data:application/json;base64,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