import 'jquery-ui-dist/jquery-ui';
import { Editor } from 'angular-slickgrid';
export declare class RadioButtonEditor implements Editor {
    private args;
    private isBool;
    private logger;
    private $input;
    private defaultValue;
    private enableFlag;
    private showHideCells;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    CRUD_ORIGINAL_DATA: string;
    CRUD_CHANGES_DATA: any;
    originalDefaultValue: any;
    private commonGrid;
    constructor(args: any);
    init(): void;
    destroy(): void;
    loadValue(item: any): void;
    focus(): void;
    serializeValue(): boolean;
    applyValue(item: any, state: any): void;
    isValueChanged(): boolean;
    validate(): {
        valid: boolean;
        msg: any;
    };
}
