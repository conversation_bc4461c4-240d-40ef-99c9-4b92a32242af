/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef, Output, EventEmitter, Input, Injector, ComponentFactoryResolver } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { HDividedEndResizeEvent, TabSelectEvent, DividerResizeComplete } from "../events/swt-events.module";
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
var $ = require('jquery');
var HDividedBox = /** @class */ (function (_super) {
    tslib_1.__extends(HDividedBox, _super);
    /**
     * constructor
     * @param elem
     * @param commonService
     * @param _rendred
     */
    function HDividedBox(elem, commonService, injector, componentFactoryResolver) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.injector = injector;
        _this.componentFactoryResolver = componentFactoryResolver;
        _this.DIVIDER_DRAG_COMPLETE = new EventEmitter();
        _this.DIVIDER_BUTTON_CLICK = new EventEmitter();
        //---Properties definitions---------------------------------------------------------------------------------------
        _this._resize = new Function();
        _this._resizeStart = new Function();
        _this._resizeStop = new Function();
        _this._widthRight = null;
        _this._widthLeft = null;
        _this.widthLeftPixel = null;
        _this.widthRightPixel = null;
        _this._dividersAnimation = 'W';
        _this._extendedDividedBox = false;
        _this.doResize = false;
        _this.RightContent = null;
        _this.leftContent = null;
        _this._liveDrag = false;
        _this.startDrag = false;
        _this.lastValidValue = null;
        _this._maxWidthRight = null;
        _this._minWidthRight = null;
        _this._maxWidthLeft = null;
        _this._minWidthLeft = null;
        // Make panelRight DOM reference.
        //---Outputs------------------------------------------------------------------------------------------------------
        _this.resize_ = new EventEmitter();
        _this.resizeStart_ = new EventEmitter();
        _this.resizeStop_ = new EventEmitter();
        _this.defaultIcon = 'assets/images/hresize-handler.png';
        _this.hdividerClosed = 'assets/images/hdividerClosed.png';
        _this.hdividerOpened = 'assets/images/hdividerOpened.png';
        _this.forceNoEvent = false;
        $($(_this.elem.nativeElement)[0]).attr('selector', 'HDividedBox');
        return _this;
    }
    Object.defineProperty(HDividedBox.prototype, "maxWidthRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxWidthRight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxWidthRight = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "minWidthRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minWidthRight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minWidthRight = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "maxWidthLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxWidthLeft;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxWidthLeft = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "minWidthLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minWidthLeft;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minWidthLeft = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "resize", {
        get: /**
         * @return {?}
         */
        function () {
            return this._resize;
        },
        //---resize getter and setter-------------------------------------------------------------------------------------
        set: 
        //---resize getter and setter-------------------------------------------------------------------------------------
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resize = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "prevRightWidth", {
        get: /**
         * @return {?}
         */
        function () {
            return this._prevRightWidth;
        },
        //---resize getter and setter-------------------------------------------------------------------------------------
        set: 
        //---resize getter and setter-------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._prevRightWidth = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "resizeStart", {
        get: /**
         * @return {?}
         */
        function () {
            return this._resizeStart;
        },
        //---resize Start getter and setter-------------------------------------------------------------------------------
        set: 
        //---resize Start getter and setter-------------------------------------------------------------------------------
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resizeStart = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "resizeStop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._resizeStop;
        },
        //--- resize Stop getter and setter-------------------------------------------------------------------------------
        set: 
        //--- resize Stop getter and setter-------------------------------------------------------------------------------
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resizeStop = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        //---height-------------------------------------------------------------------------------------------------------
        set: 
        //---height-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = this.adaptUnit(value, 'auto');
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._width = this.adaptUnit(value, 'auto');
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    HDividedBox.prototype.onButtonClickHandler = /**
     * @return {?}
     */
    function () {
        if (!this.startDrag) {
            if (this.extendedDividedBox) {
                /** @type {?} */
                var isClosed = false;
                if (this._dividersAnimation == 'W') {
                    if (this.widthLeft == '0') {
                        $($(this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                        this.widthLeft = this.prevLeftWidth;
                        isClosed = false;
                    }
                    else {
                        this.widthLeft = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                    }
                }
                else {
                    if (this.widthRight == '0') {
                        $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                        this.widthRight = this.prevRightWidth;
                        isClosed = false;
                    }
                    else {
                        this.widthRight = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                    }
                }
                this.DIVIDER_BUTTON_CLICK.emit({ id: this.id, isClosed: isClosed, type: 'DIVIDER_BUTTON_CLICK' });
            }
            DividerResizeComplete.emit({ id: this.id });
        }
    };
    Object.defineProperty(HDividedBox.prototype, "widthRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._widthRight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            console.log("HDividedBox -> setwidthRight -> this.prevRightWidth start width", this.prevRightWidth, this.widthRight);
            if ('0' !== this.widthRight && '0%' !== this.widthRight)
                this.prevRightWidth = this.widthRight ? "" + this.widthRight : "" + this.prevRightWidth;
            this._widthRight = value;
            if (('' + this._widthRight).indexOf("%") > -1) {
                /** @type {?} */
                var tmp = Number(('' + this._widthRight).replace('%', ''));
                /** @type {?} */
                var newLeftWidth = $($(this.panelLeft.nativeElement).parent()).width() * ((100 - tmp) / 100);
                $(this.panelLeft.nativeElement).resizable("resizeTo", { width: newLeftWidth - 10 });
            }
            else {
                /** @type {?} */
                var newLeftWidth = ($($(this.panelLeft.nativeElement).parent()).width() - this._widthRight);
                $(this.panelLeft.nativeElement).resizable("resizeTo", { width: Number(newLeftWidth - 10) });
            }
            if (value === '0' || value === '0%') {
                this.widthRightPixel = 0;
                // this.forceZeroWidth = true;
                $($(this.panelRight.nativeElement).children()[0]).css('display', 'none');
                $(this.panelLeft.nativeElement).css("width", '100%');
                $(this.panelLeft.nativeElement).css("margin-left", '-10px');
                $(this.panelLeft.nativeElement).css("padding-left", '10px');
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} value
     * @return {?}
     */
    HDividedBox.prototype.setWidthRightWithoutEvent = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.forceNoEvent = true;
        this.widthRight = value;
    };
    /**
     * @param {?} value
     * @return {?}
     */
    HDividedBox.prototype.setWidthLeftWithoutEvent = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.forceNoEvent = true;
        this.widthLeft = value;
    };
    Object.defineProperty(HDividedBox.prototype, "dividersAnimation", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dividersAnimation;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this._dividersAnimation = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "extendedDividedBox", {
        get: /**
         * @return {?}
         */
        function () {
            return this._extendedDividedBox;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = StringUtils.isTrue(value);
            if (value) {
                this._extendedDividedBox = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "liveDrag", {
        get: /**
         * @return {?}
         */
        function () {
            return this._liveDrag;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = StringUtils.isTrue(value);
            if (value) {
                this._liveDrag = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(HDividedBox.prototype, "widthLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this._widthLeft;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if ('0' !== this.widthLeft && '0%' !== this.widthLeft)
                this.prevLeftWidth = this.widthLeft;
            this._widthLeft = value;
            if (value === '0') {
                // $(this.panelLeft.nativeElement).css('display','none');
                $($(this.panelLeft.nativeElement).children()[0]).css('display', 'none');
                this.widthLeftPixel = 0;
                // this.forceZeroWidth = true;
                // $(this.panelLeft.nativeElement).resizable("resizeTo", {  width: Number(0) });
                $(this.panelLeft.nativeElement).css("width", '0%');
                $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                $(this.panelLeft.nativeElement).css("margin-left", '-10px');
                $(this.panelLeft.nativeElement).css("padding-left", '10px');
            }
            else {
                if (('' + this._widthLeft).indexOf("%") > -1) {
                    /** @type {?} */
                    var tmp = Number(('' + this._widthLeft).replace('%', ''));
                    /** @type {?} */
                    var newLeftWidth = $($(this.panelLeft.nativeElement).parent()).width() * (tmp / 100);
                    $(this.panelLeft.nativeElement).resizable("resizeTo", { width: newLeftWidth });
                }
                else {
                    // this.setStyle("width", this._widthLeft , $(this.panelLeft.nativeElement))
                    $(this.panelLeft.nativeElement).resizable("resizeTo", { width: Number(value) });
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    HDividedBox.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        try {
            this.setStyle("width", this.width, $(this.hdividedboxContainer.nativeElement.parentElement));
            this.setStyle("height", this.height, $(this.hdividedboxContainer.nativeElement.parentElement));
            this.setStyle("height", "100%", $(this.hdividedboxContainer.nativeElement));
            this._widthLeft = $($(this.panelLeft.nativeElement).children()[0]).attr("width");
            this._widthRight = $($(this.panelRight.nativeElement).children()[0]).attr("width");
            if (this._widthLeft)
                this.setStyle("width", this._widthLeft, $(this.panelLeft.nativeElement));
            ;
            if (this._widthRight)
                this.setStyle("width", this._widthRight, $(this.panelRight.nativeElement));
            ;
            if (this.extendedDividedBox) {
                if (this._dividersAnimation == 'W') {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                }
                else {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                }
            }
        }
        catch (error) {
        }
    };
    /**
     * ngAfterViewInit
     */
    /**
     * ngAfterViewInit
     * @return {?}
     */
    HDividedBox.prototype.ngAfterViewInit = /**
     * ngAfterViewInit
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var componentFactory = this.componentFactoryResolver.resolveComponentFactory(Container);
        this.componentRef = componentFactory.create(this.injector);
        try {
            this.leftContent = $($(this.panelLeft.nativeElement).children()[0]);
            this.RightContent = $($(this.panelRight.nativeElement).children()[0]);
            $(this.leftContent).css("width", "100%");
            $(this.RightContent).css("width", "100%");
            /** @type {?} */
            var parentWidth = $($(this.panelLeft.nativeElement).parent()).width();
            $(this.panelLeft.nativeElement).resizable({
                handleSelector: ".splitter",
                resizeHeight: false,
                handles: 'e',
                helper: this._liveDrag ? false : "hdividedbox-resizable-helper",
                maxWidth: parentWidth - 10,
                minWidth: 1,
                resize: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    _this._resize(event);
                    _this.resize_.emit(event);
                    if (_this._dividersAnimation == 'W') {
                        if ($($(_this.panelLeft.nativeElement).children()[0]).css('display') == 'none') {
                            setTimeout((/**
                             * @return {?}
                             */
                            function () {
                                $($(_this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                $(_this.panelLeft.nativeElement).css("margin-left", '0px');
                                $(_this.panelLeft.nativeElement).css("padding-left", '0px');
                                TabSelectEvent.emit(null);
                            }), 0);
                        }
                    }
                    else {
                        if ($($(_this.panelRight.nativeElement).children()[0]).css('display') == 'none') {
                            setTimeout((/**
                             * @return {?}
                             */
                            function () {
                                $($(_this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                $(_this.panelLeft.nativeElement).css("margin-left", '0px');
                                $(_this.panelLeft.nativeElement).css("padding-left", '0px');
                                TabSelectEvent.emit(null);
                            }), 0);
                        }
                    }
                }),
                create: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    if (_this.extendedDividedBox) {
                        $($(_this.panelLeft.nativeElement).children('.ui-resizable-handle')[0]).click((/**
                         * @return {?}
                         */
                        function () {
                            _this.onButtonClickHandler();
                        }));
                    }
                }),
                start: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    _this.startDrag = true;
                    $('iframe').css('pointer-events', 'none');
                    _this.prevLeftWidth = _this._widthLeft;
                    /** @type {?} */
                    var maxWidthLeftTemp = 0;
                    /** @type {?} */
                    var minWidthLeftTemp = 0;
                    if (_this.maxWidthLeft) {
                        maxWidthLeftTemp = _this.maxWidthLeft;
                    }
                    else {
                        maxWidthLeftTemp = !_this.minWidthRight ? $($(_this.panelLeft.nativeElement).parent()).width() - 10 : $($(_this.panelLeft.nativeElement).parent()).width() - _this.minWidthRight - 10;
                    }
                    if (_this.minWidthLeft) {
                        minWidthLeftTemp = _this.minWidthLeft;
                    }
                    else {
                        minWidthLeftTemp = !_this.maxWidthRight ? 1 : $($(_this.panelLeft.nativeElement).parent()).width() - _this.maxWidthRight;
                    }
                    $(_this.panelLeft.nativeElement).resizable("option", "maxWidth", maxWidthLeftTemp);
                    $(_this.panelLeft.nativeElement).resizable("option", "minWidth", minWidthLeftTemp);
                    _this._resizeStart(event);
                    _this.resizeStart_.emit(event);
                }),
                stop: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        $('iframe').css('pointer-events', 'auto');
                        _this.startDrag = false;
                        /** @type {?} */
                        var cellPercentWidth = 100 * ui.originalElement.outerWidth() / $($(_this.panelLeft.nativeElement).parent()).innerWidth();
                        ui.originalElement.css('width', cellPercentWidth + '%');
                        /** @type {?} */
                        var nextCell = ui.originalElement.next().next();
                        /** @type {?} */
                        var nextPercentWidth = 100 * nextCell.outerWidth() / $($(_this.panelLeft.nativeElement).parent()).innerWidth();
                        nextCell.css('width', nextPercentWidth + '%');
                        $(_this.panelLeft.nativeElement).css("height", '');
                        if (_this.extendedDividedBox) {
                            if (ui.size.width == 1) {
                                _this.prevLeftWidth = ui.originalSize.width;
                                _this.widthLeft = '0';
                                _this._widthRight = '100%';
                            }
                            else {
                                if (cellPercentWidth > 98) {
                                    if (!_this.doResize) {
                                        _this.doResize = true;
                                        /** @type {?} */
                                        var percentOrginalWidth = 100 * ui.originalSize.width / $($(_this.panelLeft.nativeElement).parent()).innerWidth();
                                        if (percentOrginalWidth < 98)
                                            _this.prevRightWidth = Math.round((100 - percentOrginalWidth)) + '%';
                                        _this.widthRight = '0';
                                        _this._widthLeft = '100%';
                                    }
                                    else {
                                        _this.doResize = false;
                                    }
                                }
                                else {
                                    _this.prevLeftWidth = isNaN(_this._widthLeft = Math.round(cellPercentWidth)) ? _this.prevLeftWidth : _this._widthLeft = Math.round(cellPercentWidth) + '%';
                                    _this.prevRightWidth = isNaN(_this._widthRight = nextPercentWidth) ? _this.prevRightWidth : _this._widthRight = nextPercentWidth + '%';
                                    if (_this._dividersAnimation == 'W') {
                                        setTimeout((/**
                                         * @return {?}
                                         */
                                        function () {
                                            if (Math.round(cellPercentWidth) < 98 || nextPercentWidth < 98) {
                                                $($(_this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                                $($(_this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                                $(_this.panelLeft.nativeElement).css("margin-left", '0px');
                                                $(_this.panelLeft.nativeElement).css("padding-left", '0px');
                                                TabSelectEvent.emit(null);
                                            }
                                        }), 0);
                                    }
                                    else {
                                        setTimeout((/**
                                         * @return {?}
                                         */
                                        function () {
                                            if (Math.round(cellPercentWidth) < 98 || nextPercentWidth < 98) {
                                                $($(_this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                                $($(_this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                                $(_this.panelLeft.nativeElement).css("margin-left", '0px');
                                                $(_this.panelLeft.nativeElement).css("padding-left", '0px');
                                                TabSelectEvent.emit(null);
                                            }
                                        }), 0);
                                    }
                                }
                            }
                        }
                        else {
                            _this.prevLeftWidth = _this._widthLeft = Math.round(cellPercentWidth) + '%';
                            _this.prevRightWidth = _this._widthRight = nextPercentWidth + '%';
                        }
                        if (_this.widthLeft == '0') {
                            _this.widthLeftPixel = 0;
                        }
                        else {
                            _this.widthLeftPixel = Math.round(ui.originalElement.outerWidth()) || 0;
                        }
                        if (_this.widthRight == '0') {
                            _this.widthRightPixel = 0;
                        }
                        else {
                            _this.widthRightPixel = Math.round(nextCell.outerWidth()) || 0;
                        }
                        if (!_this.forceNoEvent) {
                            _this._resizeStop();
                            _this.resizeStop_.emit(event);
                            HDividedEndResizeEvent.emit(event);
                            _this.DIVIDER_DRAG_COMPLETE.emit({ id: _this.id, type: 'DIVIDER_DRAG_COMPLETE' });
                        }
                        else {
                            _this.forceNoEvent = false;
                        }
                        if (_this.extendedDividedBox) {
                            if (_this._dividersAnimation == 'W') {
                                if (_this.widthLeftPixel == 0) {
                                    _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.hdividerClosed + ')';
                                }
                                else {
                                    _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.hdividerOpened + ')';
                                }
                            }
                            else {
                                if (_this.widthRightPixel == 0) {
                                    _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.hdividerOpened + ')';
                                }
                                else {
                                    _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.hdividerClosed + ')';
                                }
                            }
                        }
                        DividerResizeComplete.emit({ id: _this.id });
                    }), 0);
                })
            });
        }
        catch (error) {
        }
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    HDividedBox.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this._resize;
            delete this._resizeStart;
            delete this._resizeStop;
            delete this._height;
            delete this._width;
            delete this.widthRight;
            delete this.widthLeft;
            delete this.RightContent;
            delete this.leftContent;
            delete this.hdividedboxContainer;
            delete this.panelLeft;
            delete this.panelRight;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    HDividedBox.decorators = [
        { type: Component, args: [{
                    selector: 'HDividedBox',
                    template: "\n         <div #hdividedboxContainer  fxLayout=\"row\" class=\"panel-container\">\n            <div #panelLeft  fxLayout=\"row\" fxLayoutGap=\"{{horizontalGap}}\" class=\"panel-left\" >\n                <ng-content  select=\".left\"></ng-content>\n            </div>\n            <div  class=\"splitter\" #splitter (click)=\"onButtonClickHandler()\"></div>\n            <div #panelRight  fxLayout=\"row\" fxLayoutGap=\"{{horizontalGap}}\" class=\"panel-right\" >\n                <ng-content select=\".right\"></ng-content>\n            </div>\n        </div>\n         \n    ",
                    styles: ["\n\n        .panel-container {\n            overflow: hidden;\n            background-color: #D6E3FE;\n            xtouch-action: none;\n         }\n\n        .panel-left {\n            flex: 0 0 auto;\n            height:100%;\n            min-height: 20px;\n            min-width: 10px;\n            padding-bottom : 5px;\n        }\n\n        .splitter {\n            flex: 0 0 auto;\n            width: 10px !important;\n            background-image: url(\"assets/images/hresize-handler.png\");\n            background-repeat: no-repeat;\n            background-position: center;\n        }\n\n        .panel-right {\n            flex: 1 1 auto;\n            height:100%;\n            min-height: 20px;\n            min-width: 20px;\n            padding-bottom : 5px;\n        }\n\n\n    "]
                }] }
    ];
    /** @nocollapse */
    HDividedBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Injector },
        { type: ComponentFactoryResolver }
    ]; };
    HDividedBox.propDecorators = {
        DIVIDER_DRAG_COMPLETE: [{ type: Output, args: ['DIVIDER_DRAG_COMPLETE',] }],
        DIVIDER_BUTTON_CLICK: [{ type: Output, args: ['DIVIDER_BUTTON_CLICK',] }],
        maxWidthRight: [{ type: Input }],
        minWidthRight: [{ type: Input }],
        maxWidthLeft: [{ type: Input }],
        minWidthLeft: [{ type: Input }],
        hdividedboxContainer: [{ type: ViewChild, args: ["hdividedboxContainer",] }],
        panelLeft: [{ type: ViewChild, args: ["panelLeft",] }],
        panelRight: [{ type: ViewChild, args: ["panelRight",] }],
        splitter: [{ type: ViewChild, args: ["splitter",] }],
        resize_: [{ type: Output, args: ['resize',] }],
        resizeStart_: [{ type: Output, args: ['resizeStart',] }],
        resizeStop_: [{ type: Output, args: ['resizeStop',] }],
        height: [{ type: Input }],
        width: [{ type: Input }],
        widthRight: [{ type: Input }],
        dividersAnimation: [{ type: Input }],
        extendedDividedBox: [{ type: Input }],
        liveDrag: [{ type: Input }],
        widthLeft: [{ type: Input }]
    };
    return HDividedBox;
}(Container));
export { HDividedBox };
if (false) {
    /** @type {?} */
    HDividedBox.prototype.DIVIDER_DRAG_COMPLETE;
    /** @type {?} */
    HDividedBox.prototype.DIVIDER_BUTTON_CLICK;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resize;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resizeStart;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resizeStop;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._height;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._width;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._widthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._widthLeft;
    /** @type {?} */
    HDividedBox.prototype.widthLeftPixel;
    /** @type {?} */
    HDividedBox.prototype.widthRightPixel;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._dividersAnimation;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._extendedDividedBox;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.doResize;
    /** @type {?} */
    HDividedBox.prototype.componentRef;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.RightContent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.leftContent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._liveDrag;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.startDrag;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.lastValidValue;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._maxWidthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._minWidthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._maxWidthLeft;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._minWidthLeft;
    /** @type {?} */
    HDividedBox.prototype.hdividedboxContainer;
    /** @type {?} */
    HDividedBox.prototype.panelLeft;
    /** @type {?} */
    HDividedBox.prototype.panelRight;
    /** @type {?} */
    HDividedBox.prototype.splitter;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resize_;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resizeStart_;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resizeStop_;
    /** @type {?} */
    HDividedBox.prototype.prevLeftWidth;
    /** @type {?} */
    HDividedBox.prototype._prevRightWidth;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.defaultIcon;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.hdividerClosed;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.hdividerOpened;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.forceNoEvent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.injector;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.componentFactoryResolver;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWhkaXZpZGVkYm94LmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9zd3QtaGRpdmlkZWRib3guY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBRUEsT0FBTyxFQUFFLFNBQVMsRUFBb0MsU0FBUyxFQUFFLFVBQVUsRUFBRSxNQUFNLEVBQUUsWUFBWSxFQUFFLEtBQUssRUFBRSxRQUFRLEVBQUUsd0JBQXdCLEVBQUUsTUFBTSxlQUFlLENBQUM7QUFDcEssT0FBTyxFQUFFLFNBQVMsRUFBRSxNQUFNLHVDQUF1QyxDQUFDO0FBQ2xFLE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSx5QkFBeUIsQ0FBQztBQUN4RCxPQUFPLEVBQUUsc0JBQXNCLEVBQUUsY0FBYyxFQUFHLHFCQUFxQixFQUFDLE1BQU0sNkJBQTZCLENBQUM7QUFDNUcsT0FBTyxFQUFFLFdBQVcsRUFBRSxNQUFNLCtCQUErQixDQUFDOztJQUd0RCxDQUFDLEdBQUcsT0FBTyxDQUFDLFFBQVEsQ0FBQztBQUUzQjtJQWlEaUMsdUNBQVM7SUErUXRDOzs7OztPQUtHO0lBQ0gscUJBQW9CLElBQWdCLEVBQVUsYUFBNEIsRUFBVSxRQUFrQixFQUFVLHdCQUFrRDtRQUFsSyxZQUNJLGtCQUFNLElBQUksRUFBRSxhQUFhLENBQUMsU0FHN0I7UUFKbUIsVUFBSSxHQUFKLElBQUksQ0FBWTtRQUFVLG1CQUFhLEdBQWIsYUFBYSxDQUFlO1FBQVUsY0FBUSxHQUFSLFFBQVEsQ0FBVTtRQUFVLDhCQUF3QixHQUF4Qix3QkFBd0IsQ0FBMEI7UUFwUmpJLDJCQUFxQixHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDO1FBQ3BFLDBCQUFvQixHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDOztRQUcxRixhQUFPLEdBQUcsSUFBSSxRQUFRLEVBQUUsQ0FBQztRQUN6QixrQkFBWSxHQUFHLElBQUksUUFBUSxFQUFFLENBQUM7UUFDOUIsaUJBQVcsR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBRzdCLGlCQUFXLEdBQUcsSUFBSSxDQUFDO1FBQ25CLGdCQUFVLEdBQUcsSUFBSSxDQUFDO1FBQ25CLG9CQUFjLEdBQUcsSUFBSSxDQUFDO1FBQ3RCLHFCQUFlLEdBQUcsSUFBSSxDQUFDO1FBQ3RCLHdCQUFrQixHQUFHLEdBQUcsQ0FBQztRQUN6Qix5QkFBbUIsR0FBRyxLQUFLLENBQUM7UUFDNUIsY0FBUSxHQUFZLEtBQUssQ0FBQztRQUUxQixrQkFBWSxHQUFHLElBQUksQ0FBQztRQUNwQixpQkFBVyxHQUFHLElBQUksQ0FBQztRQUVuQixlQUFTLEdBQUcsS0FBSyxDQUFDO1FBQ2xCLGVBQVMsR0FBWSxLQUFLLENBQUM7UUFDM0Isb0JBQWMsR0FBRyxJQUFJLENBQUM7UUFHdEIsb0JBQWMsR0FBRyxJQUFJLENBQUM7UUFDdEIsb0JBQWMsR0FBRyxJQUFJLENBQUM7UUFDdEIsbUJBQWEsR0FBRyxJQUFJLENBQUM7UUFDckIsbUJBQWEsR0FBRyxJQUFJLENBQUM7OztRQXVDSCxhQUFPLEdBQUcsSUFBSSxZQUFZLEVBQVksQ0FBQztRQUNsQyxrQkFBWSxHQUFHLElBQUksWUFBWSxFQUFZLENBQUM7UUFDN0MsaUJBQVcsR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDO1FBSWpFLGlCQUFXLEdBQUcsbUNBQW1DLENBQUM7UUFDbEQsb0JBQWMsR0FBRyxrQ0FBa0MsQ0FBQztRQUNwRCxvQkFBYyxHQUFHLGtDQUFrQyxDQUFDO1FBa0hwRCxrQkFBWSxHQUFHLEtBQUssQ0FBQztRQXlGekIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxhQUFhLENBQUMsQ0FBQzs7SUFFckUsQ0FBQztJQXpQRCxzQkFBVyxzQ0FBYTs7OztRQUF4QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQztRQUMvQixDQUFDOzs7OztRQUNELFVBQ3lCLEtBQUs7WUFDMUIsSUFBSSxDQUFDLGNBQWMsR0FBRyxLQUFLLENBQUM7UUFDaEMsQ0FBQzs7O09BSkE7SUFLRCxzQkFBVyxzQ0FBYTs7OztRQUF4QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQztRQUMvQixDQUFDOzs7OztRQUNELFVBQ3lCLEtBQUs7WUFDMUIsSUFBSSxDQUFDLGNBQWMsR0FBRyxLQUFLLENBQUM7UUFDaEMsQ0FBQzs7O09BSkE7SUFLRCxzQkFBVyxxQ0FBWTs7OztRQUF2QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDOzs7OztRQUNELFVBQ3dCLEtBQUs7WUFDekIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUM7UUFDL0IsQ0FBQzs7O09BSkE7SUFLRCxzQkFBVyxxQ0FBWTs7OztRQUF2QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDOzs7OztRQUNELFVBQ3dCLEtBQUs7WUFDekIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUM7UUFDL0IsQ0FBQzs7O09BSkE7SUF5QkQsc0JBQUksK0JBQU07Ozs7UUFHVjtZQUNJLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztRQUN4QixDQUFDO1FBTkQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQVcsT0FBaUI7WUFDeEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUM7UUFDM0IsQ0FBQzs7O09BQUE7SUFLRCxzQkFBSSx1Q0FBYzs7OztRQUdsQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQztRQUNoQyxDQUFDO1FBTkQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQW1CLEtBQUs7WUFDcEIsSUFBSSxDQUFDLGVBQWUsR0FBRyxLQUFLLENBQUM7UUFDakMsQ0FBQzs7O09BQUE7SUFNRCxzQkFBSSxvQ0FBVzs7OztRQUdmO1lBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQzdCLENBQUM7UUFORCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFBZ0IsT0FBaUI7WUFDN0IsSUFBSSxDQUFDLFlBQVksR0FBRyxPQUFPLENBQUM7UUFDaEMsQ0FBQzs7O09BQUE7SUFNRCxzQkFBSSxtQ0FBVTs7OztRQUdkO1lBQ0ksT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBQzVCLENBQUM7UUFORCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFBZSxPQUFpQjtZQUM1QixJQUFJLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQztRQUMvQixDQUFDOzs7T0FBQTtJQU1ELHNCQUNJLCtCQUFNOzs7O1FBR1Y7WUFDSSxPQUFPLElBQUksQ0FBQyxPQUFPLENBQUM7UUFDeEIsQ0FBQztRQVBELGtIQUFrSDs7Ozs7OztRQUNsSCxVQUNXLEtBQWE7WUFDcEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQztRQUNqRCxDQUFDOzs7T0FBQTtJQU1ELHNCQUNJLDhCQUFLOzs7O1FBR1Q7WUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDdkIsQ0FBQztRQVJELGtIQUFrSDs7Ozs7OztRQUVsSCxVQUNVLEtBQWE7WUFDbkIsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQztRQUNoRCxDQUFDOzs7T0FBQTs7OztJQUtELDBDQUFvQjs7O0lBQXBCO1FBQ0ksSUFBRyxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUM7WUFDZixJQUFJLElBQUksQ0FBQyxrQkFBa0IsRUFBRTs7b0JBQ3JCLFFBQVEsR0FBRyxLQUFLO2dCQUVwQixJQUFJLElBQUksQ0FBQyxrQkFBa0IsSUFBSSxHQUFHLEVBQUU7b0JBQ2hDLElBQUksSUFBSSxDQUFDLFNBQVMsSUFBSSxHQUFHLEVBQUU7d0JBQ3ZCLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLENBQUE7d0JBQ3ZFLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQzt3QkFDcEMsUUFBUSxHQUFHLEtBQUssQ0FBQztxQkFDcEI7eUJBQU07d0JBQ0gsSUFBSSxDQUFDLFNBQVMsR0FBRyxHQUFHLENBQUM7d0JBQ3JCLFFBQVEsR0FBRyxJQUFJLENBQUM7d0JBQ2hCLElBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxlQUFlLEdBQUcsTUFBTSxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsR0FBRyxDQUFDO3FCQUMxRjtpQkFDSjtxQkFDSTtvQkFDRCxJQUFJLElBQUksQ0FBQyxVQUFVLElBQUksR0FBRyxFQUFFO3dCQUV4QixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFBO3dCQUN4RSxJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUM7d0JBQ3RDLFFBQVEsR0FBRyxLQUFLLENBQUM7cUJBQ3BCO3lCQUFNO3dCQUNILElBQUksQ0FBQyxVQUFVLEdBQUcsR0FBRyxDQUFDO3dCQUN0QixRQUFRLEdBQUcsSUFBSSxDQUFDO3dCQUNoQixJQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsZUFBZSxHQUFHLE1BQU0sR0FBRyxJQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQztxQkFDMUY7aUJBRUo7Z0JBQ0QsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLHNCQUFzQixFQUFFLENBQUMsQ0FBQzthQUNyRztZQUNELHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFDLEVBQUUsRUFBQyxJQUFJLENBQUMsRUFBRSxFQUFDLENBQUMsQ0FBQztTQUM1QztJQUNMLENBQUM7SUFFRCxzQkFDSSxtQ0FBVTs7OztRQXFDZDtZQUNJLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUM1QixDQUFDOzs7OztRQXhDRCxVQUNlLEtBQWE7WUFDeEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpRUFBaUUsRUFBRSxJQUFJLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtZQUNwSCxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksS0FBSyxJQUFJLENBQUMsVUFBVTtnQkFDbkQsSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFBLENBQUMsQ0FBQSxFQUFFLEdBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQSxDQUFDLENBQUEsRUFBRSxHQUFDLElBQUksQ0FBQyxjQUFjLENBQUM7WUFDcEYsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUM7WUFDekIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFOztvQkFDdkMsR0FBRyxHQUFHLE1BQU0sQ0FBQyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQzs7b0JBQ3RELFlBQVksR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQztnQkFDNUYsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsU0FBUyxDQUFDLFVBQVUsRUFBRSxFQUFFLEtBQUssRUFBRSxZQUFZLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQzthQUd2RjtpQkFBTTs7b0JBQ0MsWUFBWSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUMsS0FBSyxFQUFFLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFDM0YsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsU0FBUyxDQUFDLFVBQVUsRUFBRSxFQUFFLEtBQUssRUFBRSxNQUFNLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQzthQUMvRjtZQUNELElBQUksS0FBSyxLQUFLLEdBQUcsSUFBSSxLQUFLLEtBQUssSUFBSSxFQUFFO2dCQUNqQyxJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQztnQkFDekIsOEJBQThCO2dCQUM5QixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dCQUN6RSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dCQUNyRCxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxFQUFFLE9BQU8sQ0FBQyxDQUFDO2dCQUM1RCxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsY0FBYyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2FBRS9EO1FBQ0wsQ0FBQzs7O09BQUE7Ozs7O0lBR0QsK0NBQXlCOzs7O0lBQXpCLFVBQTBCLEtBQWE7UUFDbkMsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUM7UUFDekIsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7SUFDNUIsQ0FBQzs7Ozs7SUFFRCw4Q0FBd0I7Ozs7SUFBeEIsVUFBeUIsS0FBYTtRQUNsQyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQztRQUN6QixJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztJQUMzQixDQUFDO0lBS0Qsc0JBQ0ksMENBQWlCOzs7O1FBS3JCO1lBQ0ksT0FBTyxJQUFJLENBQUMsa0JBQWtCLENBQUM7UUFDbkMsQ0FBQzs7Ozs7UUFSRCxVQUNzQixLQUFhO1lBQy9CLElBQUksS0FBSyxFQUFFO2dCQUNQLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxLQUFLLENBQUM7YUFDbkM7UUFDTCxDQUFDOzs7T0FBQTtJQUtELHNCQUNJLDJDQUFrQjs7OztRQU10QjtZQUNJLE9BQU8sSUFBSSxDQUFDLG1CQUFtQixDQUFDO1FBQ3BDLENBQUM7Ozs7O1FBVEQsVUFDdUIsS0FBVTtZQUM3QixLQUFLLEdBQUcsV0FBVyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNsQyxJQUFJLEtBQUssRUFBRTtnQkFDUCxJQUFJLENBQUMsbUJBQW1CLEdBQUcsS0FBSyxDQUFDO2FBQ3BDO1FBQ0wsQ0FBQzs7O09BQUE7SUFLRCxzQkFDSSxpQ0FBUTs7OztRQU1aO1lBQ0ksT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1FBQzFCLENBQUM7Ozs7O1FBVEQsVUFDYSxLQUFTO1lBQ2xCLEtBQUssR0FBRyxXQUFXLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ2xDLElBQUcsS0FBSyxFQUFJO2dCQUNSLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO2FBQzFCO1FBQ0wsQ0FBQzs7O09BQUE7SUFPRCxzQkFDSSxrQ0FBUzs7OztRQTZCYjtZQUNJLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztRQUMzQixDQUFDOzs7OztRQWhDRCxVQUNjLEtBQWE7WUFDdkIsSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLEtBQUssSUFBSSxDQUFDLFNBQVM7Z0JBQ2pELElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQztZQUd4QyxJQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztZQUN4QixJQUFJLEtBQUssS0FBSyxHQUFHLEVBQUU7Z0JBQ2YseURBQXlEO2dCQUN6RCxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dCQUN4RSxJQUFJLENBQUMsY0FBYyxHQUFHLENBQUMsQ0FBQztnQkFFeEIsOEJBQThCO2dCQUM5QixnRkFBZ0Y7Z0JBQ2hGLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBQ25ELENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLENBQUM7Z0JBQ3pFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsT0FBTyxDQUFDLENBQUM7Z0JBQzVELENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsTUFBTSxDQUFDLENBQUM7YUFDL0Q7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFOzt3QkFDdEMsR0FBRyxHQUFHLE1BQU0sQ0FBQyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQzs7d0JBQ3JELFlBQVksR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLEdBQUcsR0FBRyxHQUFHLENBQUM7b0JBQ3BGLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxVQUFVLEVBQUUsRUFBRSxLQUFLLEVBQUUsWUFBWSxFQUFFLENBQUMsQ0FBQztpQkFFbEY7cUJBQU07b0JBQ0gsNEVBQTRFO29CQUM1RSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxTQUFTLENBQUMsVUFBVSxFQUFFLEVBQUUsS0FBSyxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLENBQUM7aUJBQ25GO2FBQ0o7UUFDTCxDQUFDOzs7T0FBQTtJQWlCRDs7T0FFRzs7Ozs7SUFDSCw4QkFBUTs7OztJQUFSO1FBQ0ksSUFBSTtZQUNBLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQztZQUM3RixJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7WUFDL0YsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLEVBQUUsTUFBTSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQztZQUU1RSxJQUFJLENBQUMsVUFBVSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUNqRixJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUVuRixJQUFJLElBQUksQ0FBQyxVQUFVO2dCQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQztZQUFBLENBQUM7WUFDL0YsSUFBSSxJQUFJLENBQUMsV0FBVztnQkFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7WUFBQSxDQUFDO1lBQ2xHLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFO2dCQUN6QixJQUFJLElBQUksQ0FBQyxrQkFBa0IsSUFBSSxHQUFHLEVBQUU7b0JBQ2hDLElBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxlQUFlLEdBQUcsTUFBTSxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsR0FBRyxDQUFDO2lCQUMxRjtxQkFBTTtvQkFDSCxJQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsZUFBZSxHQUFHLE1BQU0sR0FBRyxJQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQztpQkFDMUY7YUFDSjtTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7U0FDZjtJQUVMLENBQUM7SUFFRDs7T0FFRzs7Ozs7SUFDSCxxQ0FBZTs7OztJQUFmO1FBQUEsaUJBeUxDOztZQXhMUyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsd0JBQXdCLENBQUMsdUJBQXVCLENBQUMsU0FBUyxDQUFDO1FBQ3pGLElBQUksQ0FBQyxZQUFZLEdBQUcsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUczRCxJQUFJO1lBQ0EsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNwRSxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXRFLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxNQUFNLENBQUMsQ0FBQztZQUN6QyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7O2dCQUV0QyxXQUFXLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUMsS0FBSyxFQUFFO1lBQ3JFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFNBQVMsQ0FBQztnQkFDdEMsY0FBYyxFQUFFLFdBQVc7Z0JBQzNCLFlBQVksRUFBRSxLQUFLO2dCQUNuQixPQUFPLEVBQUUsR0FBRztnQkFDWixNQUFNLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQSxDQUFDLENBQUEsS0FBSyxDQUFBLENBQUMsQ0FBQyw4QkFBOEI7Z0JBQzVELFFBQVEsRUFBRSxXQUFXLEdBQUcsRUFBRTtnQkFDMUIsUUFBUSxFQUFFLENBQUM7Z0JBQ1gsTUFBTTs7Ozs7Z0JBQUUsVUFBQyxLQUFLLEVBQUUsRUFBRTtvQkFFZCxLQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDO29CQUNwQixLQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztvQkFFekIsSUFBSSxLQUFJLENBQUMsa0JBQWtCLElBQUksR0FBRyxFQUFFO3dCQUNoQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLENBQUMsSUFBSSxNQUFNLEVBQUU7NEJBQzNFLFVBQVU7Ozs0QkFBQztnQ0FDUCxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dDQUN4RSxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFDO2dDQUMxRCxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsY0FBYyxFQUFFLEtBQUssQ0FBQyxDQUFDO2dDQUMzRCxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDOzRCQUM5QixDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7eUJBQ1Q7cUJBQ0o7eUJBQU07d0JBQ0gsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksTUFBTSxFQUFFOzRCQUM1RSxVQUFVOzs7NEJBQUM7Z0NBQ1AsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxNQUFNLENBQUMsQ0FBQztnQ0FDekUsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsR0FBRyxDQUFDLGFBQWEsRUFBRSxLQUFLLENBQUMsQ0FBQztnQ0FDMUQsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsR0FBRyxDQUFDLGNBQWMsRUFBRSxLQUFLLENBQUMsQ0FBQztnQ0FDM0QsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQzs0QkFFOUIsQ0FBQyxHQUFFLENBQUMsQ0FBQyxDQUFDO3lCQUNUO3FCQUVKO2dCQUVMLENBQUMsQ0FBQTtnQkFDRCxNQUFNOzs7OztnQkFBRSxVQUFDLEtBQUssRUFBRSxFQUFFO29CQUNkLElBQUksS0FBSSxDQUFDLGtCQUFrQixFQUFFO3dCQUN6QixDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxDQUFDLHNCQUFzQixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLOzs7d0JBQUM7NEJBQ3pFLEtBQUksQ0FBQyxvQkFBb0IsRUFBRSxDQUFDO3dCQUNoQyxDQUFDLEVBQUMsQ0FBQztxQkFDTjtnQkFDTCxDQUFDLENBQUE7Z0JBQ0QsS0FBSzs7Ozs7Z0JBQUUsVUFBQyxLQUFLLEVBQUUsRUFBRTtvQkFDYixLQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztvQkFDdEIsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsQ0FBQztvQkFDMUMsS0FBSSxDQUFDLGFBQWEsR0FBRyxLQUFJLENBQUMsVUFBVSxDQUFDOzt3QkFDakMsZ0JBQWdCLEdBQUcsQ0FBQzs7d0JBQ3BCLGdCQUFnQixHQUFHLENBQUM7b0JBRXhCLElBQUcsS0FBSSxDQUFDLFlBQVksRUFBQzt3QkFDakIsZ0JBQWdCLEdBQUcsS0FBSSxDQUFDLFlBQVksQ0FBQztxQkFDeEM7eUJBQUs7d0JBQ0YsZ0JBQWdCLEdBQUcsQ0FBQyxLQUFJLENBQUMsYUFBYSxDQUFBLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxLQUFLLEVBQUUsR0FBRyxFQUFFLENBQUEsQ0FBQyxDQUFBLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRSxHQUFHLEtBQUksQ0FBQyxhQUFhLEdBQUcsRUFBRSxDQUFDO3FCQUNsTDtvQkFDRixJQUFHLEtBQUksQ0FBQyxZQUFZLEVBQUM7d0JBQ2hCLGdCQUFnQixHQUFHLEtBQUksQ0FBQyxZQUFZLENBQUM7cUJBQ3pDO3lCQUFLO3dCQUNELGdCQUFnQixHQUFHLENBQUMsS0FBSSxDQUFDLGFBQWEsQ0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFBLENBQUMsQ0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxLQUFLLEVBQUUsR0FBRyxLQUFJLENBQUMsYUFBYSxDQUFDO3FCQUN2SDtvQkFHQSxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxTQUFTLENBQUMsUUFBUSxFQUFFLFVBQVUsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDO29CQUNsRixDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxTQUFTLENBQUMsUUFBUSxFQUFFLFVBQVUsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDO29CQUNsRixLQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO29CQUN6QixLQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDbEMsQ0FBQyxDQUFBO2dCQUNELElBQUk7Ozs7O2dCQUFFLFVBQUMsS0FBSyxFQUFFLEVBQUU7b0JBQ1osVUFBVTs7O29CQUFDO3dCQUNQLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLEVBQUUsTUFBTSxDQUFDLENBQUM7d0JBQzFDLEtBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDOzs0QkFDbkIsZ0JBQWdCLEdBQUcsR0FBRyxHQUFHLEVBQUUsQ0FBQyxlQUFlLENBQUMsVUFBVSxFQUFFLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUMsVUFBVSxFQUFFO3dCQUN2SCxFQUFFLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsZ0JBQWdCLEdBQUcsR0FBRyxDQUFDLENBQUM7OzRCQUNwRCxRQUFRLEdBQUcsRUFBRSxDQUFDLGVBQWUsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxJQUFJLEVBQUU7OzRCQUMzQyxnQkFBZ0IsR0FBRyxHQUFHLEdBQUcsUUFBUSxDQUFDLFVBQVUsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLFVBQVUsRUFBRTt3QkFDN0csUUFBUSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsZ0JBQWdCLEdBQUcsR0FBRyxDQUFDLENBQUM7d0JBQzlDLENBQUMsQ0FBQyxLQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLENBQUM7d0JBQ2xELElBQUksS0FBSSxDQUFDLGtCQUFrQixFQUFFOzRCQUN6QixJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUMsS0FBSyxJQUFJLENBQUMsRUFBRTtnQ0FDcEIsS0FBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQztnQ0FDM0MsS0FBSSxDQUFDLFNBQVMsR0FBRyxHQUFHLENBQUM7Z0NBQ3JCLEtBQUksQ0FBQyxXQUFXLEdBQUcsTUFBTSxDQUFDOzZCQUM3QjtpQ0FBTTtnQ0FDSCxJQUFJLGdCQUFnQixHQUFHLEVBQUUsRUFBRTtvQ0FDdkIsSUFBSSxDQUFDLEtBQUksQ0FBQyxRQUFRLEVBQUU7d0NBQ2hCLEtBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDOzs0Q0FDakIsbUJBQW1CLEdBQUcsR0FBRyxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLFVBQVUsRUFBRTt3Q0FDaEgsSUFBRyxtQkFBbUIsR0FBQyxFQUFFOzRDQUNyQixLQUFJLENBQUMsY0FBYyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLEdBQUcsbUJBQW1CLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQzt3Q0FDeEUsS0FBSSxDQUFDLFVBQVUsR0FBRyxHQUFHLENBQUM7d0NBQ3RCLEtBQUksQ0FBQyxVQUFVLEdBQUcsTUFBTSxDQUFDO3FDQUM1Qjt5Q0FBTTt3Q0FDSCxLQUFJLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQztxQ0FDekI7aUNBQ0o7cUNBQU07b0NBQ0gsS0FBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUMsS0FBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQSxDQUFDLENBQUEsS0FBSSxDQUFDLGFBQWEsQ0FBQSxDQUFDLENBQUMsS0FBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDLEdBQUUsR0FBRyxDQUFDO29DQUNuSixLQUFJLENBQUMsY0FBYyxHQUFHLEtBQUssQ0FBQyxLQUFJLENBQUMsV0FBVyxHQUFHLGdCQUFnQixDQUFDLENBQUEsQ0FBQyxDQUFBLEtBQUksQ0FBQyxjQUFjLENBQUEsQ0FBQyxDQUFDLEtBQUksQ0FBQyxXQUFXLEdBQUcsZ0JBQWdCLEdBQUUsR0FBRyxDQUFDO29DQUMvSCxJQUFJLEtBQUksQ0FBQyxrQkFBa0IsSUFBSSxHQUFHLEVBQUU7d0NBQ2hDLFVBQVU7Ozt3Q0FBQzs0Q0FDWCxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxFQUFFLElBQUssZ0JBQWdCLEdBQUcsRUFBRSxFQUFFO2dEQUM3RCxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dEQUN4RSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxVQUFVLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dEQUN6RSxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFDO2dEQUMxRCxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLENBQUMsY0FBYyxFQUFFLEtBQUssQ0FBQyxDQUFDO2dEQUMzRCxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDOzZDQUM3Qjt3Q0FFTCxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7cUNBQ0w7eUNBQU07d0NBQ0gsVUFBVTs7O3dDQUFDOzRDQUVQLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLEVBQUUsSUFBSyxnQkFBZ0IsR0FBRyxFQUFFLEVBQUU7Z0RBQzdELENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFVBQVUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLENBQUM7Z0RBQ3pFLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLENBQUM7Z0RBQ3hFLENBQUMsQ0FBQyxLQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0RBQzFELENBQUMsQ0FBQyxLQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0RBQzNELGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7NkNBQzdCO3dDQUNMLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztxQ0FFVDtpQ0FDSjs2QkFFSjt5QkFDSjs2QkFBTTs0QkFDSCxLQUFJLENBQUMsYUFBYSxHQUFHLEtBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLEdBQUcsQ0FBQzs0QkFDMUUsS0FBSSxDQUFDLGNBQWMsR0FBRyxLQUFJLENBQUMsV0FBVyxHQUFHLGdCQUFnQixHQUFHLEdBQUcsQ0FBQzt5QkFDbkU7d0JBQ0QsSUFBSSxLQUFJLENBQUMsU0FBUyxJQUFJLEdBQUcsRUFBRTs0QkFDdkIsS0FBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLENBQUM7eUJBQzNCOzZCQUFNOzRCQUNILEtBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsZUFBZSxDQUFDLFVBQVUsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO3lCQUMxRTt3QkFFRCxJQUFJLEtBQUksQ0FBQyxVQUFVLElBQUksR0FBRyxFQUFFOzRCQUN4QixLQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQzt5QkFDNUI7NkJBQU07NEJBQ0gsS0FBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQzt5QkFDakU7d0JBQ0QsSUFBSSxDQUFDLEtBQUksQ0FBQyxZQUFZLEVBQUU7NEJBQ3BCLEtBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQzs0QkFDbkIsS0FBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7NEJBQzdCLHNCQUFzQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzs0QkFDbkMsS0FBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxLQUFJLENBQUMsRUFBRSxFQUFFLElBQUksRUFBRSx1QkFBdUIsRUFBRSxDQUFDLENBQUM7eUJBRW5GOzZCQUFNOzRCQUNILEtBQUksQ0FBQyxZQUFZLEdBQUcsS0FBSyxDQUFDO3lCQUM3Qjt3QkFDRCxJQUFJLEtBQUksQ0FBQyxrQkFBa0IsRUFBRTs0QkFDekIsSUFBSSxLQUFJLENBQUMsa0JBQWtCLElBQUksR0FBRyxFQUFFO2dDQUNoQyxJQUFJLEtBQUksQ0FBQyxjQUFjLElBQUksQ0FBQyxFQUFFO29DQUMxQixLQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsZUFBZSxHQUFHLE1BQU0sR0FBRyxLQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQztpQ0FDMUY7cUNBQU07b0NBQ0gsS0FBSSxDQUFDLFFBQVEsQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLGVBQWUsR0FBRyxNQUFNLEdBQUcsS0FBSSxDQUFDLGNBQWMsR0FBRyxHQUFHLENBQUM7aUNBQzFGOzZCQUNKO2lDQUFNO2dDQUNILElBQUksS0FBSSxDQUFDLGVBQWUsSUFBSSxDQUFDLEVBQUU7b0NBQzNCLEtBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxlQUFlLEdBQUcsTUFBTSxHQUFHLEtBQUksQ0FBQyxjQUFjLEdBQUcsR0FBRyxDQUFDO2lDQUMxRjtxQ0FBTTtvQ0FDSCxLQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsZUFBZSxHQUFHLE1BQU0sR0FBRyxLQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQztpQ0FDMUY7NkJBQ0o7eUJBQ0o7d0JBRUQscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUMsRUFBRSxFQUFDLEtBQUksQ0FBQyxFQUFFLEVBQUMsQ0FBQyxDQUFDO29CQUM3QyxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBRVYsQ0FBQyxDQUFBO2FBRUosQ0FBQyxDQUFDO1NBQ047UUFBQyxPQUFPLEtBQUssRUFBRTtTQUNmO0lBRUwsQ0FBQztJQUVEOztPQUVHOzs7OztJQUNILGlDQUFXOzs7O0lBQVg7UUFDSSxJQUFJO1lBRUEsSUFBSSxDQUFDLDhCQUE4QixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7WUFFaEUsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO1lBQ3BCLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztZQUN6QixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7WUFDeEIsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO1lBQ3BCLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztZQUNuQixPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7WUFDdkIsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1lBQ3RCLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztZQUN6QixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7WUFFeEIsT0FBTyxJQUFJLENBQUMsb0JBQW9CLENBQUM7WUFDakMsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1lBQ3RCLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztTQUUxQjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTyxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsS0FBSyxDQUFDLENBQUE7U0FDbEM7SUFDTCxDQUFDOztnQkE5akJKLFNBQVMsU0FBQztvQkFDUCxRQUFRLEVBQUUsYUFBYTtvQkFDdkIsUUFBUSxFQUFFLG1rQkFXVDs2QkFDUSwweEJBaUNSO2lCQUNKOzs7O2dCQXpEZ0UsVUFBVTtnQkFFbEUsYUFBYTtnQkFGb0YsUUFBUTtnQkFBRSx3QkFBd0I7Ozt3Q0EyRHZJLE1BQU0sU0FBQyx1QkFBdUI7dUNBQzlCLE1BQU0sU0FBQyxzQkFBc0I7Z0NBaUM3QixLQUFLO2dDQU9MLEtBQUs7K0JBT0wsS0FBSzsrQkFPTCxLQUFLO3VDQU1MLFNBQVMsU0FBQyxzQkFBc0I7NEJBQ2hDLFNBQVMsU0FBQyxXQUFXOzZCQUNyQixTQUFTLFNBQUMsWUFBWTsyQkFDdEIsU0FBUyxTQUFDLFVBQVU7MEJBR3BCLE1BQU0sU0FBQyxRQUFROytCQUNmLE1BQU0sU0FBQyxhQUFhOzhCQUNwQixNQUFNLFNBQUMsWUFBWTt5QkF5Q25CLEtBQUs7d0JBU0wsS0FBSzs2QkEyQ0wsS0FBSztvQ0F5Q0wsS0FBSztxQ0FVTCxLQUFLOzJCQVdMLEtBQUs7NEJBYUwsS0FBSzs7SUFnU1Ysa0JBQUM7Q0FBQSxBQS9qQkQsQ0FpRGlDLFNBQVMsR0E4Z0J6QztTQTlnQlksV0FBVzs7O0lBQ3BCLDRDQUFvRzs7SUFDcEcsMkNBQWtHOzs7OztJQUdsRyw4QkFBaUM7Ozs7O0lBQ2pDLG1DQUFzQzs7Ozs7SUFDdEMsa0NBQXFDOzs7OztJQUNyQyw4QkFBd0I7Ozs7O0lBQ3hCLDZCQUF1Qjs7Ozs7SUFDdkIsa0NBQTJCOzs7OztJQUMzQixpQ0FBMEI7O0lBQzFCLHFDQUE2Qjs7SUFDN0Isc0NBQThCOzs7OztJQUM5Qix5Q0FBaUM7Ozs7O0lBQ2pDLDBDQUFvQzs7Ozs7SUFDcEMsK0JBQWtDOztJQUNsQyxtQ0FBb0I7Ozs7O0lBQ3BCLG1DQUE0Qjs7Ozs7SUFDNUIsa0NBQTJCOzs7OztJQUUzQixnQ0FBMEI7Ozs7O0lBQzFCLGdDQUFtQzs7Ozs7SUFDbkMscUNBQThCOzs7OztJQUc5QixxQ0FBOEI7Ozs7O0lBQzlCLHFDQUE4Qjs7Ozs7SUFDOUIsb0NBQTZCOzs7OztJQUM3QixvQ0FBNkI7O0lBaUM3QiwyQ0FBb0U7O0lBQ3BFLGdDQUE4Qzs7SUFDOUMsaUNBQWdEOztJQUNoRCwrQkFBNEM7Ozs7O0lBRzVDLDhCQUFpRTs7Ozs7SUFDakUsbUNBQTJFOzs7OztJQUMzRSxrQ0FBeUU7O0lBQ3pFLG9DQUFtQjs7SUFDbkIsc0NBQXFCOzs7OztJQUVyQixrQ0FBMEQ7Ozs7O0lBQzFELHFDQUE0RDs7Ozs7SUFDNUQscUNBQTREOzs7OztJQWtINUQsbUNBQTZCOzs7OztJQXVGakIsMkJBQXdCOzs7OztJQUFFLG9DQUFvQzs7Ozs7SUFBRSwrQkFBMEI7Ozs7O0lBQUUsK0NBQTBEIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3d0VXRpbCB9IGZyb20gJy4vLi4vdXRpbHMvc3d0LXV0aWwuc2VydmljZSc7XHJcbmltcG9ydCB7IENvbW1vblV0aWwgfSBmcm9tICcuLy4uL3V0aWxzL2NvbW1vbi11dGlsLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBDb21wb25lbnQsIE9uRGVzdHJveSwgQWZ0ZXJWaWV3SW5pdCwgT25Jbml0LCBWaWV3Q2hpbGQsIEVsZW1lbnRSZWYsIE91dHB1dCwgRXZlbnRFbWl0dGVyLCBJbnB1dCwgSW5qZWN0b3IsIENvbXBvbmVudEZhY3RvcnlSZXNvbHZlciB9IGZyb20gXCJAYW5ndWxhci9jb3JlXCI7XHJcbmltcG9ydCB7IENvbnRhaW5lciB9IGZyb20gXCIuLi9jb250YWluZXJzL3N3dC1jb250YWluZXIuY29tcG9uZW50XCI7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHsgSERpdmlkZWRFbmRSZXNpemVFdmVudCwgVGFiU2VsZWN0RXZlbnQgLCBEaXZpZGVyUmVzaXplQ29tcGxldGV9IGZyb20gXCIuLi9ldmVudHMvc3d0LWV2ZW50cy5tb2R1bGVcIjtcclxuaW1wb3J0IHsgU3RyaW5nVXRpbHMgfSBmcm9tICcuLi91dGlscy9zdHJpbmctdXRpbHMuc2VydmljZSc7XHJcblxyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbmNvbnN0ICQgPSByZXF1aXJlKCdqcXVlcnknKTtcclxuXHJcbkBDb21wb25lbnQoe1xyXG4gICAgc2VsZWN0b3I6ICdIRGl2aWRlZEJveCcsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgICAgICA8ZGl2ICNoZGl2aWRlZGJveENvbnRhaW5lciAgZnhMYXlvdXQ9XCJyb3dcIiBjbGFzcz1cInBhbmVsLWNvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICA8ZGl2ICNwYW5lbExlZnQgIGZ4TGF5b3V0PVwicm93XCIgZnhMYXlvdXRHYXA9XCJ7e2hvcml6b250YWxHYXB9fVwiIGNsYXNzPVwicGFuZWwtbGVmdFwiID5cclxuICAgICAgICAgICAgICAgIDxuZy1jb250ZW50ICBzZWxlY3Q9XCIubGVmdFwiPjwvbmctY29udGVudD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgIGNsYXNzPVwic3BsaXR0ZXJcIiAjc3BsaXR0ZXIgKGNsaWNrKT1cIm9uQnV0dG9uQ2xpY2tIYW5kbGVyKClcIj48L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiAjcGFuZWxSaWdodCAgZnhMYXlvdXQ9XCJyb3dcIiBmeExheW91dEdhcD1cInt7aG9yaXpvbnRhbEdhcH19XCIgY2xhc3M9XCJwYW5lbC1yaWdodFwiID5cclxuICAgICAgICAgICAgICAgIDxuZy1jb250ZW50IHNlbGVjdD1cIi5yaWdodFwiPjwvbmctY29udGVudD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgIFxyXG4gICAgYCxcclxuICAgIHN0eWxlczogW2BcclxuXHJcbiAgICAgICAgLnBhbmVsLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNENkUzRkU7XHJcbiAgICAgICAgICAgIHh0b3VjaC1hY3Rpb246IG5vbmU7XHJcbiAgICAgICAgIH1cclxuXHJcbiAgICAgICAgLnBhbmVsLWxlZnQge1xyXG4gICAgICAgICAgICBmbGV4OiAwIDAgYXV0bztcclxuICAgICAgICAgICAgaGVpZ2h0OjEwMCU7XHJcbiAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgIG1pbi13aWR0aDogMTBweDtcclxuICAgICAgICAgICAgcGFkZGluZy1ib3R0b20gOiA1cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc3BsaXR0ZXIge1xyXG4gICAgICAgICAgICBmbGV4OiAwIDAgYXV0bztcclxuICAgICAgICAgICAgd2lkdGg6IDEwcHggIWltcG9ydGFudDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFwiYXNzZXRzL2ltYWdlcy9ocmVzaXplLWhhbmRsZXIucG5nXCIpO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucGFuZWwtcmlnaHQge1xyXG4gICAgICAgICAgICBmbGV4OiAxIDEgYXV0bztcclxuICAgICAgICAgICAgaGVpZ2h0OjEwMCU7XHJcbiAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgIG1pbi13aWR0aDogMjBweDtcclxuICAgICAgICAgICAgcGFkZGluZy1ib3R0b20gOiA1cHg7XHJcbiAgICAgICAgfVxyXG5cclxuXHJcbiAgICBgXVxyXG59KVxyXG5leHBvcnQgY2xhc3MgSERpdmlkZWRCb3ggZXh0ZW5kcyBDb250YWluZXIgaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQsIE9uRGVzdHJveSB7XHJcbiAgICBAT3V0cHV0KCdESVZJREVSX0RSQUdfQ09NUExFVEUnKSBESVZJREVSX0RSQUdfQ09NUExFVEU6IEV2ZW50RW1pdHRlcjxhbnk+ID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgICBAT3V0cHV0KCdESVZJREVSX0JVVFRPTl9DTElDSycpIERJVklERVJfQlVUVE9OX0NMSUNLOiBFdmVudEVtaXR0ZXI8YW55PiA9IG5ldyBFdmVudEVtaXR0ZXI8YW55PigpO1xyXG5cclxuICAgIC8vLS0tUHJvcGVydGllcyBkZWZpbml0aW9ucy0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgcHJpdmF0ZSBfcmVzaXplID0gbmV3IEZ1bmN0aW9uKCk7XHJcbiAgICBwcml2YXRlIF9yZXNpemVTdGFydCA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgcHJpdmF0ZSBfcmVzaXplU3RvcCA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgcHJpdmF0ZSBfaGVpZ2h0OiBzdHJpbmc7XHJcbiAgICBwcml2YXRlIF93aWR0aDogc3RyaW5nO1xyXG4gICAgcHJpdmF0ZSBfd2lkdGhSaWdodCA9IG51bGw7XHJcbiAgICBwcml2YXRlIF93aWR0aExlZnQgPSBudWxsO1xyXG4gICAgcHVibGljIHdpZHRoTGVmdFBpeGVsID0gbnVsbDtcclxuICAgIHB1YmxpYyB3aWR0aFJpZ2h0UGl4ZWwgPSBudWxsO1xyXG4gICAgcHJpdmF0ZSBfZGl2aWRlcnNBbmltYXRpb24gPSAnVyc7XHJcbiAgICBwcml2YXRlIF9leHRlbmRlZERpdmlkZWRCb3ggPSBmYWxzZTtcclxuICAgIHByaXZhdGUgZG9SZXNpemU6IGJvb2xlYW4gPSBmYWxzZTtcclxuICAgIHB1YmxpYyBjb21wb25lbnRSZWY7XHJcbiAgICBwcml2YXRlIFJpZ2h0Q29udGVudCA9IG51bGw7XHJcbiAgICBwcml2YXRlIGxlZnRDb250ZW50ID0gbnVsbDtcclxuXHJcbiAgICBwcml2YXRlIF9saXZlRHJhZyA9IGZhbHNlO1xyXG4gICAgcHJpdmF0ZSBzdGFydERyYWcgOmJvb2xlYW4gPSBmYWxzZTtcclxuICAgIHByaXZhdGUgbGFzdFZhbGlkVmFsdWUgPSBudWxsO1xyXG5cclxuXHJcbiAgICBwcml2YXRlIF9tYXhXaWR0aFJpZ2h0ID0gbnVsbDtcclxuICAgIHByaXZhdGUgX21pbldpZHRoUmlnaHQgPSBudWxsO1xyXG4gICAgcHJpdmF0ZSBfbWF4V2lkdGhMZWZ0ID0gbnVsbDtcclxuICAgIHByaXZhdGUgX21pbldpZHRoTGVmdCA9IG51bGw7XHJcblxyXG5cclxuICAgIHB1YmxpYyBnZXQgbWF4V2lkdGhSaWdodCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fbWF4V2lkdGhSaWdodDtcclxuICAgIH1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IG1heFdpZHRoUmlnaHQodmFsdWUpIHtcclxuICAgICAgICB0aGlzLl9tYXhXaWR0aFJpZ2h0ID0gdmFsdWU7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG1pbldpZHRoUmlnaHQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21pbldpZHRoUmlnaHQ7XHJcbiAgICB9XHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCBtaW5XaWR0aFJpZ2h0KHZhbHVlKSB7XHJcbiAgICAgICAgdGhpcy5fbWluV2lkdGhSaWdodCA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBtYXhXaWR0aExlZnQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21heFdpZHRoTGVmdDtcclxuICAgIH1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IG1heFdpZHRoTGVmdCh2YWx1ZSkge1xyXG4gICAgICAgIHRoaXMuX21heFdpZHRoTGVmdCA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBtaW5XaWR0aExlZnQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21pbldpZHRoTGVmdDtcclxuICAgIH1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IG1pbldpZHRoTGVmdCh2YWx1ZSkge1xyXG4gICAgICAgIHRoaXMuX21pbldpZHRoTGVmdCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tVmlld0NoaWxkLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQFZpZXdDaGlsZChcImhkaXZpZGVkYm94Q29udGFpbmVyXCIpIGhkaXZpZGVkYm94Q29udGFpbmVyOiBFbGVtZW50UmVmOy8vIE1ha2UgSERpdmlkZWRCb3ggRE9NIHJlZmVyZW5jZS5cclxuICAgIEBWaWV3Q2hpbGQoXCJwYW5lbExlZnRcIikgcGFuZWxMZWZ0OiBFbGVtZW50UmVmOy8vIE1ha2UgcGFuZWxMZWZ0IERPTSByZWZlcmVuY2UuXHJcbiAgICBAVmlld0NoaWxkKFwicGFuZWxSaWdodFwiKSBwYW5lbFJpZ2h0OiBFbGVtZW50UmVmOy8vIE1ha2UgcGFuZWxSaWdodCBET00gcmVmZXJlbmNlLlxyXG4gICAgQFZpZXdDaGlsZChcInNwbGl0dGVyXCIpIHNwbGl0dGVyOiBFbGVtZW50UmVmOy8vIE1ha2UgcGFuZWxSaWdodCBET00gcmVmZXJlbmNlLlxyXG5cclxuICAgIC8vLS0tT3V0cHV0cy0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQE91dHB1dCgncmVzaXplJykgcHJpdmF0ZSByZXNpemVfID0gbmV3IEV2ZW50RW1pdHRlcjxGdW5jdGlvbj4oKTtcclxuICAgIEBPdXRwdXQoJ3Jlc2l6ZVN0YXJ0JykgcHJpdmF0ZSByZXNpemVTdGFydF8gPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgQE91dHB1dCgncmVzaXplU3RvcCcpIHByaXZhdGUgcmVzaXplU3RvcF8gPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgcHJldkxlZnRXaWR0aDogYW55O1xyXG4gICAgX3ByZXZSaWdodFdpZHRoOiBhbnk7XHJcblxyXG4gICAgcHJpdmF0ZSBkZWZhdWx0SWNvbiA9ICdhc3NldHMvaW1hZ2VzL2hyZXNpemUtaGFuZGxlci5wbmcnO1xyXG4gICAgcHJpdmF0ZSBoZGl2aWRlckNsb3NlZCA9ICdhc3NldHMvaW1hZ2VzL2hkaXZpZGVyQ2xvc2VkLnBuZyc7XHJcbiAgICBwcml2YXRlIGhkaXZpZGVyT3BlbmVkID0gJ2Fzc2V0cy9pbWFnZXMvaGRpdmlkZXJPcGVuZWQucG5nJztcclxuXHJcblxyXG4gICAgLy8tLS1yZXNpemUgZ2V0dGVyIGFuZCBzZXR0ZXItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBzZXQgcmVzaXplKGhhbmRsZXI6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fcmVzaXplID0gaGFuZGxlcjtcclxuICAgIH1cclxuICAgIGdldCByZXNpemUoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Jlc2l6ZTtcclxuICAgIH1cclxuICAgIC8vLS0tcmVzaXplIGdldHRlciBhbmQgc2V0dGVyLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgc2V0IHByZXZSaWdodFdpZHRoKHZhbHVlKSB7XHJcbiAgICAgICAgdGhpcy5fcHJldlJpZ2h0V2lkdGggPSB2YWx1ZTtcclxuICAgIH1cclxuICAgIGdldCBwcmV2UmlnaHRXaWR0aCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fcHJldlJpZ2h0V2lkdGg7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1yZXNpemUgU3RhcnQgZ2V0dGVyIGFuZCBzZXR0ZXItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBzZXQgcmVzaXplU3RhcnQoaGFuZGxlcjogRnVuY3Rpb24pIHtcclxuICAgICAgICB0aGlzLl9yZXNpemVTdGFydCA9IGhhbmRsZXI7XHJcbiAgICB9XHJcbiAgICBnZXQgcmVzaXplU3RhcnQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Jlc2l6ZVN0YXJ0O1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tIHJlc2l6ZSBTdG9wIGdldHRlciBhbmQgc2V0dGVyLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgc2V0IHJlc2l6ZVN0b3AoaGFuZGxlcjogRnVuY3Rpb24pIHtcclxuICAgICAgICB0aGlzLl9yZXNpemVTdG9wID0gaGFuZGxlcjtcclxuICAgIH1cclxuICAgIGdldCByZXNpemVTdG9wKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9yZXNpemVTdG9wO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0taGVpZ2h0LS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KClcclxuICAgIHNldCBoZWlnaHQodmFsdWU6IHN0cmluZykge1xyXG4gICAgICAgIHRoaXMuX2hlaWdodCA9IHRoaXMuYWRhcHRVbml0KHZhbHVlLCAnYXV0bycpO1xyXG4gICAgfVxyXG4gICAgZ2V0IGhlaWdodCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5faGVpZ2h0O1xyXG4gICAgfVxyXG4gICAgLy8tLS13aWR0aC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG4gICAgQElucHV0KClcclxuICAgIHNldCB3aWR0aCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5fd2lkdGggPSB0aGlzLmFkYXB0VW5pdCh2YWx1ZSwgJ2F1dG8nKTtcclxuICAgIH1cclxuICAgIGdldCB3aWR0aCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fd2lkdGg7XHJcbiAgICB9XHJcblxyXG4gICAgb25CdXR0b25DbGlja0hhbmRsZXIoKSB7XHJcbiAgICAgICAgaWYoIXRoaXMuc3RhcnREcmFnKXtcclxuICAgICAgICAgICAgaWYgKHRoaXMuZXh0ZW5kZWREaXZpZGVkQm94KSB7XHJcbiAgICAgICAgICAgICAgICBsZXQgaXNDbG9zZWQgPSBmYWxzZTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5fZGl2aWRlcnNBbmltYXRpb24gPT0gJ1cnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMud2lkdGhMZWZ0ID09ICcwJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY3NzKCdkaXNwbGF5JywgJ2ZsZXgnKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLndpZHRoTGVmdCA9IHRoaXMucHJldkxlZnRXaWR0aDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXNDbG9zZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLndpZHRoTGVmdCA9ICcwJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXNDbG9zZWQgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNwbGl0dGVyLm5hdGl2ZUVsZW1lbnQuc3R5bGUuYmFja2dyb3VuZEltYWdlID0gJ3VybCgnICsgdGhpcy5oZGl2aWRlckNsb3NlZCArICcpJztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy53aWR0aFJpZ2h0ID09ICcwJykge1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jc3MoJ2Rpc3BsYXknLCAnZmxleCcpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2lkdGhSaWdodCA9IHRoaXMucHJldlJpZ2h0V2lkdGg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQ2xvc2VkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy53aWR0aFJpZ2h0ID0gJzAnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0Nsb3NlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3BsaXR0ZXIubmF0aXZlRWxlbWVudC5zdHlsZS5iYWNrZ3JvdW5kSW1hZ2UgPSAndXJsKCcgKyB0aGlzLmhkaXZpZGVyT3BlbmVkICsgJyknO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB0aGlzLkRJVklERVJfQlVUVE9OX0NMSUNLLmVtaXQoeyBpZDogdGhpcy5pZCwgaXNDbG9zZWQ6IGlzQ2xvc2VkLCB0eXBlOiAnRElWSURFUl9CVVRUT05fQ0xJQ0snIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIERpdmlkZXJSZXNpemVDb21wbGV0ZS5lbWl0KHtpZDp0aGlzLmlkfSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgd2lkdGhSaWdodCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJIRGl2aWRlZEJveCAtPiBzZXR3aWR0aFJpZ2h0IC0+IHRoaXMucHJldlJpZ2h0V2lkdGggc3RhcnQgd2lkdGhcIiwgdGhpcy5wcmV2UmlnaHRXaWR0aCwgdGhpcy53aWR0aFJpZ2h0KVxyXG4gICAgICAgIGlmICgnMCcgIT09IHRoaXMud2lkdGhSaWdodCAmJiAnMCUnICE9PSB0aGlzLndpZHRoUmlnaHQpXHJcbiAgICAgICAgICAgIHRoaXMucHJldlJpZ2h0V2lkdGggPSB0aGlzLndpZHRoUmlnaHQ/XCJcIit0aGlzLndpZHRoUmlnaHQ6XCJcIit0aGlzLnByZXZSaWdodFdpZHRoO1xyXG4gICAgICAgIHRoaXMuX3dpZHRoUmlnaHQgPSB2YWx1ZTtcclxuICAgICAgICBpZiAoKCcnICsgdGhpcy5fd2lkdGhSaWdodCkuaW5kZXhPZihcIiVcIikgPiAtMSkge1xyXG4gICAgICAgICAgICBsZXQgdG1wID0gTnVtYmVyKCgnJyArIHRoaXMuX3dpZHRoUmlnaHQpLnJlcGxhY2UoJyUnLCAnJykpO1xyXG4gICAgICAgICAgICBsZXQgbmV3TGVmdFdpZHRoID0gJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnBhcmVudCgpKS53aWR0aCgpICogKCgxMDAgLSB0bXApIC8gMTAwKTtcclxuICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5yZXNpemFibGUoXCJyZXNpemVUb1wiLCB7IHdpZHRoOiBuZXdMZWZ0V2lkdGggLSAxMCB9KTtcclxuXHJcblxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGxldCBuZXdMZWZ0V2lkdGggPSAoJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnBhcmVudCgpKS53aWR0aCgpIC0gdGhpcy5fd2lkdGhSaWdodCk7XHJcbiAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucmVzaXphYmxlKFwicmVzaXplVG9cIiwgeyB3aWR0aDogTnVtYmVyKG5ld0xlZnRXaWR0aCAtIDEwKSB9KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHZhbHVlID09PSAnMCcgfHwgdmFsdWUgPT09ICcwJScpIHtcclxuICAgICAgICAgICAgdGhpcy53aWR0aFJpZ2h0UGl4ZWwgPSAwO1xyXG4gICAgICAgICAgICAvLyB0aGlzLmZvcmNlWmVyb1dpZHRoID0gdHJ1ZTtcclxuICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jc3MoJ2Rpc3BsYXknLCAnbm9uZScpO1xyXG4gICAgICAgICAgICAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNzcyhcIndpZHRoXCIsICcxMDAlJyk7XHJcbiAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwibWFyZ2luLWxlZnRcIiwgJy0xMHB4Jyk7XHJcbiAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwicGFkZGluZy1sZWZ0XCIsICcxMHB4Jyk7XHJcblxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIGZvcmNlTm9FdmVudCA9IGZhbHNlO1xyXG4gICAgc2V0V2lkdGhSaWdodFdpdGhvdXRFdmVudCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5mb3JjZU5vRXZlbnQgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMud2lkdGhSaWdodCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIHNldFdpZHRoTGVmdFdpdGhvdXRFdmVudCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5mb3JjZU5vRXZlbnQgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMud2lkdGhMZWZ0ID0gdmFsdWU7XHJcbiAgICB9XHJcblxyXG4gICAgZ2V0IHdpZHRoUmlnaHQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3dpZHRoUmlnaHQ7XHJcbiAgICB9XHJcbiAgICBASW5wdXQoKVxyXG4gICAgc2V0IGRpdmlkZXJzQW5pbWF0aW9uKHZhbHVlOiBzdHJpbmcpIHtcclxuICAgICAgICBpZiAodmFsdWUpIHtcclxuICAgICAgICAgICAgdGhpcy5fZGl2aWRlcnNBbmltYXRpb24gPSB2YWx1ZTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBnZXQgZGl2aWRlcnNBbmltYXRpb24oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2RpdmlkZXJzQW5pbWF0aW9uO1xyXG4gICAgfVxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgZXh0ZW5kZWREaXZpZGVkQm94KHZhbHVlOiBhbnkpIHtcclxuICAgICAgICB2YWx1ZSA9IFN0cmluZ1V0aWxzLmlzVHJ1ZSh2YWx1ZSk7XHJcbiAgICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX2V4dGVuZGVkRGl2aWRlZEJveCA9IHZhbHVlO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCBleHRlbmRlZERpdmlkZWRCb3goKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2V4dGVuZGVkRGl2aWRlZEJveDtcclxuICAgIH1cclxuXHJcbiAgICBASW5wdXQoKVxyXG4gICAgc2V0IGxpdmVEcmFnKHZhbHVlOmFueSkge1xyXG4gICAgICAgIHZhbHVlID0gU3RyaW5nVXRpbHMuaXNUcnVlKHZhbHVlKTtcclxuICAgICAgICBpZih2YWx1ZSkgICB7XHJcbiAgICAgICAgICAgIHRoaXMuX2xpdmVEcmFnID0gdmFsdWU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0IGxpdmVEcmFnKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9saXZlRHJhZztcclxuICAgIH1cclxuICAgXHJcblxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgd2lkdGhMZWZ0KHZhbHVlOiBzdHJpbmcpIHtcclxuICAgICAgICBpZiAoJzAnICE9PSB0aGlzLndpZHRoTGVmdCAmJiAnMCUnICE9PSB0aGlzLndpZHRoTGVmdClcclxuICAgICAgICAgICAgdGhpcy5wcmV2TGVmdFdpZHRoID0gdGhpcy53aWR0aExlZnQ7XHJcblxyXG5cclxuICAgICAgICB0aGlzLl93aWR0aExlZnQgPSB2YWx1ZTtcclxuICAgICAgICBpZiAodmFsdWUgPT09ICcwJykge1xyXG4gICAgICAgICAgICAvLyAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNzcygnZGlzcGxheScsJ25vbmUnKTtcclxuICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScsICdub25lJyk7ICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHRoaXMud2lkdGhMZWZ0UGl4ZWwgPSAwO1xyXG5cclxuICAgICAgICAgICAgLy8gdGhpcy5mb3JjZVplcm9XaWR0aCA9IHRydWU7XHJcbiAgICAgICAgICAgIC8vICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucmVzaXphYmxlKFwicmVzaXplVG9cIiwgeyAgd2lkdGg6IE51bWJlcigwKSB9KTtcclxuICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5jc3MoXCJ3aWR0aFwiLCAnMCUnKTtcclxuICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jc3MoJ2Rpc3BsYXknLCAnZmxleCcpO1xyXG4gICAgICAgICAgICAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNzcyhcIm1hcmdpbi1sZWZ0XCIsICctMTBweCcpO1xyXG4gICAgICAgICAgICAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNzcyhcInBhZGRpbmctbGVmdFwiLCAnMTBweCcpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmICgoJycgKyB0aGlzLl93aWR0aExlZnQpLmluZGV4T2YoXCIlXCIpID4gLTEpIHtcclxuICAgICAgICAgICAgICAgIGxldCB0bXAgPSBOdW1iZXIoKCcnICsgdGhpcy5fd2lkdGhMZWZ0KS5yZXBsYWNlKCclJywgJycpKTtcclxuICAgICAgICAgICAgICAgIGxldCBuZXdMZWZ0V2lkdGggPSAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLndpZHRoKCkgKiAodG1wIC8gMTAwKTtcclxuICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucmVzaXphYmxlKFwicmVzaXplVG9cIiwgeyB3aWR0aDogbmV3TGVmdFdpZHRoIH0pO1xyXG5cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIC8vIHRoaXMuc2V0U3R5bGUoXCJ3aWR0aFwiLCB0aGlzLl93aWR0aExlZnQgLCAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpKVxyXG4gICAgICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5yZXNpemFibGUoXCJyZXNpemVUb1wiLCB7IHdpZHRoOiBOdW1iZXIodmFsdWUpIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0IHdpZHRoTGVmdCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fd2lkdGhMZWZ0O1xyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBjb25zdHJ1Y3RvclxyXG4gICAgICogQHBhcmFtIGVsZW1cclxuICAgICAqIEBwYXJhbSBjb21tb25TZXJ2aWNlXHJcbiAgICAgKiBAcGFyYW0gX3JlbmRyZWRcclxuICAgICAqL1xyXG4gICAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtOiBFbGVtZW50UmVmLCBwcml2YXRlIGNvbW1vblNlcnZpY2U6IENvbW1vblNlcnZpY2UsIHByaXZhdGUgaW5qZWN0b3I6IEluamVjdG9yLCBwcml2YXRlIGNvbXBvbmVudEZhY3RvcnlSZXNvbHZlcjogQ29tcG9uZW50RmFjdG9yeVJlc29sdmVyKSB7XHJcbiAgICAgICAgc3VwZXIoZWxlbSwgY29tbW9uU2VydmljZSk7XHJcbiAgICAgICAgJCgkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KVswXSkuYXR0cignc2VsZWN0b3InLCAnSERpdmlkZWRCb3gnKTtcclxuXHJcbiAgICB9XHJcblxyXG5cclxuICAgIC8qKlxyXG4gICAgICogbmdPbkluaXRcclxuICAgICAqL1xyXG4gICAgbmdPbkluaXQoKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIndpZHRoXCIsIHRoaXMud2lkdGgsICQodGhpcy5oZGl2aWRlZGJveENvbnRhaW5lci5uYXRpdmVFbGVtZW50LnBhcmVudEVsZW1lbnQpKTtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcImhlaWdodFwiLCB0aGlzLmhlaWdodCwgJCh0aGlzLmhkaXZpZGVkYm94Q29udGFpbmVyLm5hdGl2ZUVsZW1lbnQucGFyZW50RWxlbWVudCkpO1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKFwiaGVpZ2h0XCIsIFwiMTAwJVwiLCAkKHRoaXMuaGRpdmlkZWRib3hDb250YWluZXIubmF0aXZlRWxlbWVudCkpO1xyXG5cclxuICAgICAgICAgICAgdGhpcy5fd2lkdGhMZWZ0ID0gJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmF0dHIoXCJ3aWR0aFwiKTtcclxuICAgICAgICAgICAgdGhpcy5fd2lkdGhSaWdodCA9ICQoJCh0aGlzLnBhbmVsUmlnaHQubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuYXR0cihcIndpZHRoXCIpO1xyXG5cclxuICAgICAgICAgICAgaWYgKHRoaXMuX3dpZHRoTGVmdCkgdGhpcy5zZXRTdHlsZShcIndpZHRoXCIsIHRoaXMuX3dpZHRoTGVmdCwgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KSk7O1xyXG4gICAgICAgICAgICBpZiAodGhpcy5fd2lkdGhSaWdodCkgdGhpcy5zZXRTdHlsZShcIndpZHRoXCIsIHRoaXMuX3dpZHRoUmlnaHQsICQodGhpcy5wYW5lbFJpZ2h0Lm5hdGl2ZUVsZW1lbnQpKTs7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLmV4dGVuZGVkRGl2aWRlZEJveCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuX2RpdmlkZXJzQW5pbWF0aW9uID09ICdXJykge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3BsaXR0ZXIubmF0aXZlRWxlbWVudC5zdHlsZS5iYWNrZ3JvdW5kSW1hZ2UgPSAndXJsKCcgKyB0aGlzLmhkaXZpZGVyT3BlbmVkICsgJyknO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnNwbGl0dGVyLm5hdGl2ZUVsZW1lbnQuc3R5bGUuYmFja2dyb3VuZEltYWdlID0gJ3VybCgnICsgdGhpcy5oZGl2aWRlckNsb3NlZCArICcpJztcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgfVxyXG5cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIG5nQWZ0ZXJWaWV3SW5pdFxyXG4gICAgICovXHJcbiAgICBuZ0FmdGVyVmlld0luaXQoKTogdm9pZCB7XHJcbiAgICAgICAgY29uc3QgY29tcG9uZW50RmFjdG9yeSA9IHRoaXMuY29tcG9uZW50RmFjdG9yeVJlc29sdmVyLnJlc29sdmVDb21wb25lbnRGYWN0b3J5KENvbnRhaW5lcik7XHJcbiAgICAgICAgdGhpcy5jb21wb25lbnRSZWYgPSBjb21wb25lbnRGYWN0b3J5LmNyZWF0ZSh0aGlzLmluamVjdG9yKTtcclxuXHJcblxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIHRoaXMubGVmdENvbnRlbnQgPSAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSk7XHJcbiAgICAgICAgICAgIHRoaXMuUmlnaHRDb250ZW50ID0gJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKTtcclxuXHJcbiAgICAgICAgICAgICQodGhpcy5sZWZ0Q29udGVudCkuY3NzKFwid2lkdGhcIiwgXCIxMDAlXCIpO1xyXG4gICAgICAgICAgICAkKHRoaXMuUmlnaHRDb250ZW50KS5jc3MoXCJ3aWR0aFwiLCBcIjEwMCVcIik7XHJcblxyXG4gICAgICAgICAgICB2YXIgcGFyZW50V2lkdGggPSAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLndpZHRoKCk7XHJcbiAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucmVzaXphYmxlKHtcclxuICAgICAgICAgICAgICAgIGhhbmRsZVNlbGVjdG9yOiBcIi5zcGxpdHRlclwiLFxyXG4gICAgICAgICAgICAgICAgcmVzaXplSGVpZ2h0OiBmYWxzZSxcclxuICAgICAgICAgICAgICAgIGhhbmRsZXM6ICdlJyxcclxuICAgICAgICAgICAgICAgIGhlbHBlcjogdGhpcy5fbGl2ZURyYWc/ZmFsc2U6IFwiaGRpdmlkZWRib3gtcmVzaXphYmxlLWhlbHBlclwiLFxyXG4gICAgICAgICAgICAgICAgbWF4V2lkdGg6IHBhcmVudFdpZHRoIC0gMTAsXHJcbiAgICAgICAgICAgICAgICBtaW5XaWR0aDogMSxcclxuICAgICAgICAgICAgICAgIHJlc2l6ZTogKGV2ZW50LCB1aSkgPT4ge1xyXG5cclxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9yZXNpemUoZXZlbnQpO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVzaXplXy5lbWl0KGV2ZW50KTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuX2RpdmlkZXJzQW5pbWF0aW9uID09ICdXJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScpID09ICdub25lJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScsICdmbGV4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5jc3MoXCJtYXJnaW4tbGVmdFwiLCAnMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5jc3MoXCJwYWRkaW5nLWxlZnRcIiwgJzBweCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRhYlNlbGVjdEV2ZW50LmVtaXQobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LCAwKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICgkKCQodGhpcy5wYW5lbFJpZ2h0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScpID09ICdub25lJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jc3MoJ2Rpc3BsYXknLCAnZmxleCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwibWFyZ2luLWxlZnRcIiwgJzBweCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwicGFkZGluZy1sZWZ0XCIsICcwcHgnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUYWJTZWxlY3RFdmVudC5lbWl0KG51bGwpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwgMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBjcmVhdGU6IChldmVudCwgdWkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5leHRlbmRlZERpdmlkZWRCb3gpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKCcudWktcmVzaXphYmxlLWhhbmRsZScpWzBdKS5jbGljaygoKSA9PiB7IC8vIG1pc3NpbmcgdGhlICggISFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub25CdXR0b25DbGlja0hhbmRsZXIoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHN0YXJ0OiAoZXZlbnQsIHVpKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGFydERyYWcgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgICQoJ2lmcmFtZScpLmNzcygncG9pbnRlci1ldmVudHMnLCAnbm9uZScpO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucHJldkxlZnRXaWR0aCA9IHRoaXMuX3dpZHRoTGVmdDtcclxuICAgICAgICAgICAgICAgICAgICBsZXQgbWF4V2lkdGhMZWZ0VGVtcCA9IDA7XHJcbiAgICAgICAgICAgICAgICAgICAgbGV0IG1pbldpZHRoTGVmdFRlbXAgPSAwO1xyXG4gICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgaWYodGhpcy5tYXhXaWR0aExlZnQpe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aExlZnRUZW1wID0gdGhpcy5tYXhXaWR0aExlZnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgfWVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aExlZnRUZW1wID0gIXRoaXMubWluV2lkdGhSaWdodD8gJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnBhcmVudCgpKS53aWR0aCgpIC0gMTA6JCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnBhcmVudCgpKS53aWR0aCgpIC0gdGhpcy5taW5XaWR0aFJpZ2h0IC0gMTA7IFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgIGlmKHRoaXMubWluV2lkdGhMZWZ0KXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGhMZWZ0VGVtcCA9IHRoaXMubWluV2lkdGhMZWZ0O1xyXG4gICAgICAgICAgICAgICAgICAgfWVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aExlZnRUZW1wID0gIXRoaXMubWF4V2lkdGhSaWdodD8gMTokKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLndpZHRoKCkgLSB0aGlzLm1heFdpZHRoUmlnaHQ7IFxyXG4gICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5yZXNpemFibGUoXCJvcHRpb25cIiwgXCJtYXhXaWR0aFwiLCBtYXhXaWR0aExlZnRUZW1wKTtcclxuICAgICAgICAgICAgICAgICAgICAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnJlc2l6YWJsZShcIm9wdGlvblwiLCBcIm1pbldpZHRoXCIsIG1pbldpZHRoTGVmdFRlbXApO1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3Jlc2l6ZVN0YXJ0KGV2ZW50KTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlc2l6ZVN0YXJ0Xy5lbWl0KGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBzdG9wOiAoZXZlbnQsIHVpKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICQoJ2lmcmFtZScpLmNzcygncG9pbnRlci1ldmVudHMnLCAnYXV0bycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXJ0RHJhZyA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgY2VsbFBlcmNlbnRXaWR0aCA9IDEwMCAqIHVpLm9yaWdpbmFsRWxlbWVudC5vdXRlcldpZHRoKCkgLyAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLmlubmVyV2lkdGgoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdWkub3JpZ2luYWxFbGVtZW50LmNzcygnd2lkdGgnLCBjZWxsUGVyY2VudFdpZHRoICsgJyUnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG5leHRDZWxsID0gdWkub3JpZ2luYWxFbGVtZW50Lm5leHQoKS5uZXh0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBuZXh0UGVyY2VudFdpZHRoID0gMTAwICogbmV4dENlbGwub3V0ZXJXaWR0aCgpIC8gJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLnBhcmVudCgpKS5pbm5lcldpZHRoKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHRDZWxsLmNzcygnd2lkdGgnLCBuZXh0UGVyY2VudFdpZHRoICsgJyUnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgJCh0aGlzLnBhbmVsTGVmdC5uYXRpdmVFbGVtZW50KS5jc3MoXCJoZWlnaHRcIiwgJycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5leHRlbmRlZERpdmlkZWRCb3gpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh1aS5zaXplLndpZHRoID09IDEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnByZXZMZWZ0V2lkdGggPSB1aS5vcmlnaW5hbFNpemUud2lkdGg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy53aWR0aExlZnQgPSAnMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fd2lkdGhSaWdodCA9ICcxMDAlJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNlbGxQZXJjZW50V2lkdGggPiA5OCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZG9SZXNpemUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZG9SZXNpemUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHBlcmNlbnRPcmdpbmFsV2lkdGggPSAxMDAgKiB1aS5vcmlnaW5hbFNpemUud2lkdGggLyAkKCQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLmlubmVyV2lkdGgoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKHBlcmNlbnRPcmdpbmFsV2lkdGg8OTgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcmV2UmlnaHRXaWR0aCA9IE1hdGgucm91bmQoKDEwMCAtIHBlcmNlbnRPcmdpbmFsV2lkdGgpKSArICclJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2lkdGhSaWdodCA9ICcwJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3dpZHRoTGVmdCA9ICcxMDAlJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZG9SZXNpemUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJldkxlZnRXaWR0aCA9IGlzTmFOKHRoaXMuX3dpZHRoTGVmdCA9IE1hdGgucm91bmQoY2VsbFBlcmNlbnRXaWR0aCkpP3RoaXMucHJldkxlZnRXaWR0aDogdGhpcy5fd2lkdGhMZWZ0ID0gTWF0aC5yb3VuZChjZWxsUGVyY2VudFdpZHRoKSsgJyUnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnByZXZSaWdodFdpZHRoID0gaXNOYU4odGhpcy5fd2lkdGhSaWdodCA9IG5leHRQZXJjZW50V2lkdGgpP3RoaXMucHJldlJpZ2h0V2lkdGg6IHRoaXMuX3dpZHRoUmlnaHQgPSBuZXh0UGVyY2VudFdpZHRoKyAnJSc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLl9kaXZpZGVyc0FuaW1hdGlvbiA9PSAnVycpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKE1hdGgucm91bmQoY2VsbFBlcmNlbnRXaWR0aCkgPCA5OCAgfHwgbmV4dFBlcmNlbnRXaWR0aCA8IDk4KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScsICdmbGV4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxSaWdodC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jc3MoJ2Rpc3BsYXknLCAnZmxleCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwibWFyZ2luLWxlZnRcIiwgJzBweCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwicGFkZGluZy1sZWZ0XCIsICcwcHgnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUYWJTZWxlY3RFdmVudC5lbWl0KG51bGwpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKE1hdGgucm91bmQoY2VsbFBlcmNlbnRXaWR0aCkgPCA5OCAgfHwgbmV4dFBlcmNlbnRXaWR0aCA8IDk4KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQoJCh0aGlzLnBhbmVsUmlnaHQubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY3NzKCdkaXNwbGF5JywgJ2ZsZXgnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJCgkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNzcygnZGlzcGxheScsICdmbGV4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQodGhpcy5wYW5lbExlZnQubmF0aXZlRWxlbWVudCkuY3NzKFwibWFyZ2luLWxlZnRcIiwgJzBweCcpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAkKHRoaXMucGFuZWxMZWZ0Lm5hdGl2ZUVsZW1lbnQpLmNzcyhcInBhZGRpbmctbGVmdFwiLCAnMHB4Jyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRhYlNlbGVjdEV2ZW50LmVtaXQobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwgMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsgICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnByZXZMZWZ0V2lkdGggPSB0aGlzLl93aWR0aExlZnQgPSBNYXRoLnJvdW5kKGNlbGxQZXJjZW50V2lkdGgpICsgJyUnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcmV2UmlnaHRXaWR0aCA9IHRoaXMuX3dpZHRoUmlnaHQgPSBuZXh0UGVyY2VudFdpZHRoICsgJyUnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLndpZHRoTGVmdCA9PSAnMCcpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2lkdGhMZWZ0UGl4ZWwgPSAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy53aWR0aExlZnRQaXhlbCA9IE1hdGgucm91bmQodWkub3JpZ2luYWxFbGVtZW50Lm91dGVyV2lkdGgoKSkgfHwgMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMud2lkdGhSaWdodCA9PSAnMCcpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2lkdGhSaWdodFBpeGVsID0gMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2lkdGhSaWdodFBpeGVsID0gTWF0aC5yb3VuZChuZXh0Q2VsbC5vdXRlcldpZHRoKCkpIHx8IDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLmZvcmNlTm9FdmVudCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fcmVzaXplU3RvcCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXNpemVTdG9wXy5lbWl0KGV2ZW50KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEhEaXZpZGVkRW5kUmVzaXplRXZlbnQuZW1pdChldmVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLkRJVklERVJfRFJBR19DT01QTEVURS5lbWl0KHsgaWQ6IHRoaXMuaWQsIHR5cGU6ICdESVZJREVSX0RSQUdfQ09NUExFVEUnIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcmNlTm9FdmVudCA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmV4dGVuZGVkRGl2aWRlZEJveCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuX2RpdmlkZXJzQW5pbWF0aW9uID09ICdXJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLndpZHRoTGVmdFBpeGVsID09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zcGxpdHRlci5uYXRpdmVFbGVtZW50LnN0eWxlLmJhY2tncm91bmRJbWFnZSA9ICd1cmwoJyArIHRoaXMuaGRpdmlkZXJDbG9zZWQgKyAnKSc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zcGxpdHRlci5uYXRpdmVFbGVtZW50LnN0eWxlLmJhY2tncm91bmRJbWFnZSA9ICd1cmwoJyArIHRoaXMuaGRpdmlkZXJPcGVuZWQgKyAnKSc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy53aWR0aFJpZ2h0UGl4ZWwgPT0gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNwbGl0dGVyLm5hdGl2ZUVsZW1lbnQuc3R5bGUuYmFja2dyb3VuZEltYWdlID0gJ3VybCgnICsgdGhpcy5oZGl2aWRlck9wZW5lZCArICcpJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNwbGl0dGVyLm5hdGl2ZUVsZW1lbnQuc3R5bGUuYmFja2dyb3VuZEltYWdlID0gJ3VybCgnICsgdGhpcy5oZGl2aWRlckNsb3NlZCArICcpJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIERpdmlkZXJSZXNpemVDb21wbGV0ZS5lbWl0KHtpZDp0aGlzLmlkfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfSwgMCk7ICBcclxuXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICB9XHJcblxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogbmdPbkRlc3Ryb3lcclxuICAgICAqL1xyXG4gICAgbmdPbkRlc3Ryb3koKTogdm9pZCB7XHJcbiAgICAgICAgdHJ5IHtcclxuXHJcbiAgICAgICAgICAgIHRoaXMucmVtb3ZlQWxsT3VwdXRzRXZlbnRzTGlzdGVuZXJzKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpKTtcclxuXHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9yZXNpemU7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9yZXNpemVTdGFydDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX3Jlc2l6ZVN0b3A7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9oZWlnaHQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl93aWR0aDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMud2lkdGhSaWdodDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMud2lkdGhMZWZ0O1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5SaWdodENvbnRlbnQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLmxlZnRDb250ZW50O1xyXG5cclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuaGRpdmlkZWRib3hDb250YWluZXI7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnBhbmVsTGVmdDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMucGFuZWxSaWdodDtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignZXJyb3IgOicsIGVycm9yKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG4iXX0=