/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Input, ElementRef, Component } from "@angular/core";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService } from "../utils/common.service";
import { Container } from "../containers/swt-container.component";
/** @type {?} */
const $ = require('jquery');
export class SwtCheckBox extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        this._selected = false;
        this._fontWeight = "100";
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        /** @type {?} */
        const popperContentEl = this.elem.nativeElement.querySelector('popper-content');
        if (popperContentEl)
            this.elem.nativeElement.appendChild(popperContentEl);
    }
    //---Label--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        this._label = value;
        if ($(this.elem.nativeElement))
            $($(this.elem.nativeElement).children()[0]).append("<span class='checkboxLabel'>" + this._label + "</span>");
    }
    /**
     * @return {?}
     */
    get label() {
        return this._label;
    }
    //---value--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set value(value) {
        try {
            this._value = value;
            /** @type {?} */
            var item = $(this.elem.nativeElement) ? $(this.elem.nativeElement).find('.item') : null;
            if (item.length > 0) {
                $(item).val(this._value);
            }
        }
        catch (error) {
            console.error('method [ set value] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get value() {
        return this._value;
    }
    //---tabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set tabIndex(value) {
        try {
            this._tabIndex = String(value);
            /** @type {?} */
            var checkmark = $(this.elem.nativeElement) ? $(this.elem.nativeElement).find('.checkmark') : null;
            if (checkmark.length > 0) {
                this.addTabIndex(checkmark, this._tabIndex);
            }
        }
        catch (error) {
            console.error('method [ set tabIndex] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get tabIndex() {
        return this._tabIndex;
    }
    //---selected-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selected(value) {
        try {
            value = this.adaptValueAsBoolean(value);
            /** @type {?} */
            var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
            if (item.length > 0) {
                $(item).prop('checked', value);
                this._selected = value;
            }
            if (this.firstCall) {
                this.originalValue = value;
                this.firstCall = false;
            }
            this._spyChanges(this._selected);
        }
        catch (error) {
            console.error('method [set selected] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get selected() {
        return this._selected;
    }
    //---FontWeight------------------------------------------------------------------------------------------------
    /**
     * @param {?} fontWeight
     * @return {?}
     */
    set fontWeight(fontWeight) {
        try {
            this._fontWeight = fontWeight;
            this.setStyle('font-weight', fontWeight, this.elem.nativeElement.children[0]);
        }
        catch (error) {
            console.error('method [ set fontWeight ] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get fontWeight() {
        return this._fontWeight;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtCheckBox.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtCheckBox');
        //-Set selected value to checkbox for Spy changes .
        if (this.firstCall) {
            this.selected = this._selected;
        }
    }
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the change event.
     *
     * @param {?} event
     * @return {?}
     */
    ChangeEventHandler(event) {
        try {
            this._selected = event.target.checked;
            event.component = this;
            this.change_.emit(event);
            this.change(event);
            this._spyChanges(this.selected);
            event.stopImmediatePropagation(); // to prevent jQuery Click fires twice  
        }
        catch (error) {
            console.error('method [ChangeEventHandler] error :', error);
        }
    }
    /**
     * Click
     * @param {?} event
     * @return {?}
     */
    emitClickEvent(event) {
        try {
            this._selected = event.target.checked;
            event.stopImmediatePropagation(); // to prevent jQuery Click fires twice when clicking on checkbox
            // update focus Manager data (focused elem)
            focusManager.focusTarget = this.id;
        }
        catch (error) {
            console.error('method [emitClickEvent] error :', error);
        }
    }
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the keyDown event.
     *
     * @param {?} event
     * @return {?}
     */
    KeyDownEventHandler(event) {
        try {
            //prevent click enter on checkbox when it's disabled
            if (event.keyCode == 13 && !this.enabled) {
                event.preventDefault();
            }
            else if (this.enabled) {
                this.onKeyDown_.emit(event);
                this.keyDown(event);
                this.selected = !this.selected;
            }
        }
        catch (error) {
            console.error('method [KeyDownEventHandler] error :', error);
        }
    }
    /**
     * resetOriginalValue
     * @return {?}
     */
    resetOriginalValue() {
        try {
            this.selected = this.originalValue;
            this._spyChanges(this.selected);
        }
        catch (error) {
            console.error('method [spyChanges] error :', error);
        }
    }
}
SwtCheckBox.decorators = [
    { type: Component, args: [{
                selector: 'SwtCheckBox',
                template: `
  
     <label   class="SwtCheckBox-container" tabindex="-1" >
          <input (change)="ChangeEventHandler($event)" 
        
                 (keydown.Enter)="KeyDownEventHandler($event)"
                 (click)="emitClickEvent($event)" 
                 type    ="checkbox" 
                 class   ="item" 
                 tabindex="-1"  >
          <span    popper="{{this.toolTipPreviousValue}}"
          [popperTrigger]="'hover'"
          [popperDisabled]="toolTipPreviousValue === null ? true : false"
          [popperPlacement]="'bottom'"
          [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}" class="checkmark" (keydown.Enter)="KeyDownEventHandler($event)"></span>
      </label>   
     
     `,
                styles: [`
            :host {
                /*overflow: hidden;*/
                width: fit-content;
                outline: none!important;
                flex-direction: row;
                box-sizing: border-box;
                display: flex;
                place-content: stretch flex-start;
                align-items: stretch;
                height:5px;
                line-height: 22px;
            }
            .swtcheck-container{
               /* margin: 0px 0px 5px 0px;*/
                outline: none !important;
             }
            .cblabel{
                position: relative;
                top: -3px;
             }
            .disabled{
                color :#AAB3B3;
             }
            /* The container */
            .SwtCheckBox-container {
                display: block;
                position: relative;
                padding-left: 20px;
                cursor: pointer;
                outline: none;
                font-size: 11px;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }
            
            /* Hide the browser's default checkbox */
            .SwtCheckBox-container input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }
            
            /* Create a custom checkbox */
            .checkmark {
                position: absolute;
                top: 5px;
                left: 0;
                height: 13px;
                width: 13px;
                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);
                border:1px solid #B7BABC;
            }
            
            /* On mouse-over, add a grey background color */
            .SwtCheckBox-container:hover input ~ .checkmark {
               background-image: -webkit-linear-gradient(top, #F6FBFF, #E2EEF4);
                background-image: -moz-linear-gradient(top, #F6FBFF, #E2EEF4);
                background-image: -ms-linear-gradient(top, #F6FBFF, #E2EEF4);
                background-image: -o-linear-gradient(top, #F6FBFF, #E2EEF4);
                background-image: linear-gradient(to bottom, #F6FBFF, #E2EEF4);
                border: 1px solid #009DFF;
                cursor: default;
            }
            
            /* When the checkbox is checked, add a blue background */
            .SwtCheckBox-container input:checked ~ .checkmark {
                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);
                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);
            }
            .SwtCheckBox-container input:active ~ .checkmark {
               background-image: -webkit-linear-gradient(top, #E3F4FF, #9ED9FF);
                background-image: -moz-linear-gradient(top, #E3F4FF, #9ED9FF);
                background-image: -ms-linear-gradient(top, #E3F4FF, #9ED9FF);
                background-image: -o-linear-gradient(top, #E3F4FF, #9ED9FF);
                background-image: linear-gradient(to bottom, #E3F4FF, #9ED9FF);
            }
            
            /* Create the checkmark/indicator (hidden when not checked) */
            .checkmark:after {
                content: "";
                position: absolute;
                display: none;
            }
            
            /* Show the checkmark when checked */
            .SwtCheckBox-container input:checked ~ .checkmark:after {
                display: block;
            }
            
            /* Style the checkmark/indicator */
            .SwtCheckBox-container .checkmark:after {
                 left: 3px;
                 top: 0px;
                 width: 5px;
                 height: 10px;
                 border: solid #525960;
                 border-width: 0 3px 3px 0;
                 -webkit-transform: rotate(45deg);
                 -ms-transform: rotate(45deg);
                 transform: rotate(45deg);
            }
           
            label {
                font-weight: 100;
                line-height:12px;
            }
            label:hover {
                cursor: default;
            }
            
         `]
            }] }
];
/** @nocollapse */
SwtCheckBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtCheckBox.propDecorators = {
    label: [{ type: Input, args: ['label',] }],
    value: [{ type: Input, args: ['value',] }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    selected: [{ type: Input, args: ['selected',] }],
    fontWeight: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._value;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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