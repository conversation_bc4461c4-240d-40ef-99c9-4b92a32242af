import { ContextMenu } from "../controls/context-menu.component";
import { CommonService } from "./common.service";
export declare class ScreenVersion {
    private common;
    svContextMenu: ContextMenu;
    private _moduleName;
    private _versionNumber;
    private _releaseDate;
    private swtAlert;
    private win;
    private jsonData;
    callerWindowObject: any;
    constructor(common: CommonService);
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    loadScreenVersion(parent: any, moduleName: string, versionNumber: string, releaseDate: string): void;
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    pushJsonData(parent: any, jsonData: any): void;
    /**
     * viewScrenVersion
     * @param event:ContextMenuEvent
     * Method used to display informations about Screen Version
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    private viewScrenVersion;
    /**
     * viewScrenVersion
     * @param event:ContextMenuEvent
     * Method used to display informations about Screen Version
     * <AUTHOR> <PERSON><PERSON> on 31/10/2019
     */
    private getJsonData;
}
