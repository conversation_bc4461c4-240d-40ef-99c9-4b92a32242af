/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { RootObject } from '../utils/swt-interfaces';
import { CommonService } from "../utils/common.service";
var JSONReader = /** @class */ (function () {
    function JSONReader() {
    }
    /**
     * @return {?}
     */
    JSONReader.prototype.JSONReader = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @param {?} inputJSON
     * @return {?}
     */
    JSONReader.prototype.setInputJSON = /**
     * @param {?} inputJSON
     * @return {?}
     */
    function (inputJSON) {
        /**
         * This bloc of code is added to add content
         * to grid data if is undefined
         * */
        JSONReader.jsonpath(inputJSON, '$..grid[*]..row[*]').forEach((/**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var column;
            for (var key in value) {
                if (value.hasOwnProperty(key)) {
                    if (typeof (value[key]) == 'object' && value[key].content == undefined) {
                        value[key].content = "";
                    }
                    else {
                        if (value["content"] == undefined && value === Object(value)) {
                            value["content"] = "";
                        }
                    }
                }
            }
        }));
        /* JSONReader.jsonpath(inputJSON,'$.*.grid.rows.row').forEach(function (row) {
         
             JSONReader.jsonpath(row,'$.*.*').forEach(function (value) {
                 if(value.content == undefined && typeof value != 'number'){
                     value.content = "";
                 }
             });
         }); */
        this.inputJSON = this.getRoot(inputJSON);
    };
    /**
     *
     */
    /**
     *
     * @return {?}
     */
    JSONReader.prototype.getInputJSON = /**
     *
     * @return {?}
     */
    function () {
        return this.inputJSON;
    };
    /**
     * @param {?} inputJSON1
     * @param {?} inputJSON2
     * @return {?}
     */
    JSONReader.compareJSON = /**
     * @param {?} inputJSON1
     * @param {?} inputJSON2
     * @return {?}
     */
    function (inputJSON1, inputJSON2) {
        if (!(JSON.stringify(inputJSON1) === JSON.stringify(inputJSON2))) {
            return false;
        }
        return true;
    };
    /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    JSONReader.jsonpath = /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    function (obj, path) {
        return CommonService.jsonpath(obj, path);
    };
    /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    JSONReader.jsonpathes = /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    function (obj, paths) {
        return CommonService.jsonpathes(obj, paths);
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getScreenAttributes = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var tmpArray = [];
        try {
            tmpArray = new Array();
            for (var i = 0; i < this.inputJSON.value.attributes.length(); i++) {
                tmpArray[this.inputJSON.value.attributes[i].name] = this.inputJSON.value.attributes[i];
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getScreenAttributes", errorLocation);
        }
        return tmpArray.length === 0 ? this.inputJSON.value : tmpArray;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getRequestReplyStatus = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var request_reply_status;
        /** @type {?} */
        var reqReplay;
        try {
            if (this.inputJSON.name === 'request_reply') {
                reqReplay = (/** @type {?} */ (this.inputJSON.value));
                if (typeof (reqReplay.status_ok) == "string") {
                    request_reply_status = (reqReplay.status_ok.toLowerCase() === "true") ? true : false;
                }
                else if (typeof (reqReplay.status_ok) == "boolean") {
                    request_reply_status = (reqReplay.status_ok == true) ? true : false;
                }
            }
            else {
                reqReplay = (/** @type {?} */ (this.inputJSON.value.request_reply));
                if (typeof (reqReplay.status_ok) == "string") {
                    request_reply_status = (reqReplay.status_ok.toLowerCase() === 'true') ? true : false;
                }
                else if (typeof (reqReplay.status_ok) == "boolean") {
                    request_reply_status = (((/** @type {?} */ (reqReplay.status_ok))).toString().toLowerCase() == 'true') ? true : false;
                }
                else {
                    request_reply_status = (((/** @type {?} */ (reqReplay.status_ok))).content.toLowerCase() === "true") ? true : false;
                }
            }
            /*if (this.inputJSON.name === 'request_reply') {
                reqReplay = <RequestReply> this.inputJSON.value;
                request_reply_status = (reqReplay.status_ok.toString() == 'true') ? true : false;
            } else {
                reqReplay = <RequestReply> this.inputJSON.value.request_reply;
                request_reply_status = (reqReplay.status_ok.toString() == 'true') ? true : false;
            }*/
        }
        catch (error) {
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 
            //                        getQualifiedClassName(this) + ".mxml", "getRequestReplyStatus", errorLocation);               
        }
        return request_reply_status;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getRequestReplyMessage = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var request_reply_message = null;
        /** @type {?} */
        var reqReplay;
        try {
            if (this.inputJSON.name === 'request_reply') {
                reqReplay = (/** @type {?} */ (this.inputJSON.value));
                request_reply_message = reqReplay.message.toString();
            }
            else {
                reqReplay = (/** @type {?} */ (this.inputJSON.value.request_reply));
                request_reply_message = reqReplay.message.toString();
            }
        }
        catch (error) {
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 
            //                    getQualifiedClassName(this) + ".mxml", "getRequestReplyMessage", errorLocation);
        }
        return request_reply_message;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getRequestReplyLocation = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var request_reply_location = null;
        try {
            if (this.inputJSON.name === 'request_reply') {
                request_reply_location = this.inputJSON.value.location;
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getRequestReplyLocation", errorLocation);
        }
        return request_reply_location;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.isDataBuilding = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var dataBuilding;
        try {
            dataBuilding = (this.inputJSON.value.databuilding == "true") ? true : false;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "isDataBuilding", errorLocation);
        }
        return dataBuilding;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getDateFormat = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var dateformat;
        try {
            dateformat = this.inputJSON.value.dateformat;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getDateFormat", errorLocation);
        }
        return dateformat;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getRefreshRate = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var refreshRate;
        try {
            refreshRate = this.inputJSON.value.refresh;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getRefreshRate", errorLocation);
        }
        return refreshRate;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getTiming = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var requestReplyTiming;
        try {
            if (this.inputJSON.name === 'request_reply') {
                requestReplyTiming = this.inputJSON.request_reply.timing;
            }
            else {
                requestReplyTiming = this.inputJSON.timing;
            }
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getTiming", errorLocation);
        }
        return requestReplyTiming;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getSingletons = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var singletons;
        try {
            singletons = this.inputJSON.value.singletons;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getSingletons", errorLocation);
        }
        return singletons;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getColumnData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var columnData;
        try {
            columnData = this.inputJSON.value.grid.metadata.columns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getSingletons", errorLocation);
        }
        return columnData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getGridData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var columnData;
        try {
            columnData = this.inputJSON.value.grid.rows;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return columnData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getBottomGridData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var bottomGridData;
        try {
            bottomGridData = this.inputJSON.value.bottomgrid.rows;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return bottomGridData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getGridMetaData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var columns;
        try {
            columns = this.inputJSON.value.grid.metadata.columns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getGridData", errorLocation);
        }
        return columns;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getRowSize = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var rowSize;
        try {
            rowSize = this.inputJSON.value.grid.rows.size;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getRowSize", errorLocation);
        }
        return rowSize;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getTotalsData = /**
     * @return {?}
     */
    function () {
        this.inputJSON.value.grid.totals.size = 1;
        /** @type {?} */
        var totalData;
        try {
            if (!this.inputJSON.value.grid.totals.row && this.inputJSON.value.grid.totals.total) {
                this.inputJSON.value.grid.totals.row = this.inputJSON.value.grid.totals.total;
                delete this.inputJSON.value.grid.totals.total;
            }
            totalData = this.inputJSON.value.grid.totals;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getTotalsData", errorLocation);               
        }
        return totalData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getSelects = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var totalData;
        try {
            totalData = this.inputJSON.value.selects;
            if (!totalData['select'].length) {
                totalData['select'] = [totalData['select']];
            }
        }
        catch (error) {
            console.error(error);
            // TODO
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) +
            //                    ".mxml", "getTotalsData", errorLocation);               
        }
        return totalData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getMaxPage = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var maxPage;
        try {
            maxPage = this.inputJSON.value.grid.paging.maxpage;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //         getQualifiedClassName(this) + ".mxml", "getMaxPage", errorLocation);              
        }
        return maxPage;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getCurrentPage = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var currentPage;
        try {
            if (this.inputJSON.value.grid.paging == undefined) {
                return "0";
            }
            currentPage = this.inputJSON.value.grid.paging.currentpage;
            return currentPage;
        }
        catch (error) {
            console.error("error", error);
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID,
            //                    getQualifiedClassName(this) + ".mxml", "getCurrentPage", errorLocation);              
        }
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getTreeData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var treeData = null;
        try {
            treeData = JSONReader.jsonpath(this.inputJSON.value, "$.tree");
        }
        catch (error) {
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this), "getAllColumnData", errorLocation);
        }
        return treeData;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getAllColumnData = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var allColumnData;
        try {
            allColumnData = this.inputJSON.value.allcolumns;
        }
        catch (error) {
            // log the error in S_ERROR_LOG table
            //            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this) + ".mxml", "getAllColumnData", errorLocation);
        }
        return allColumnData;
    };
    /* END -Added  to get allcolumn data from input XML by M.Bouraoui on 01-August-13*/
    /**
     * This method is used to get the root of a json response.
     * @param result
     */
    /* END -Added  to get allcolumn data from input XML by M.Bouraoui on 01-August-13*/
    /**
     * This method is used to get the root of a json response.
     * @param {?} result
     * @return {?}
     */
    JSONReader.prototype.getRoot = /* END -Added  to get allcolumn data from input XML by M.Bouraoui on 01-August-13*/
    /**
     * This method is used to get the root of a json response.
     * @param {?} result
     * @return {?}
     */
    function (result) {
        /** @type {?} */
        var rootnode = new RootObject();
        /** @type {?} */
        var key;
        for (key in result) {
            if (result.hasOwnProperty(key)) {
                rootnode.name = key;
                rootnode.value = result[key];
                break;
            }
        }
        return rootnode;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getprossesInfoStatus = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var process_info_status = null;
        /** @type {?} */
        var processInfo;
        try {
            if (this.inputJSON.name === 'process_info') {
                processInfo = (/** @type {?} */ (this.inputJSON.value));
                process_info_status = processInfo.process_status.toString();
            }
            else {
                processInfo = (/** @type {?} */ (this.inputJSON.value.process_info));
                process_info_status = processInfo.process_status.toString();
            }
        }
        catch (error) {
            console.log(error, "getprossesInfoStatus", "Jsonreader");
        }
        return process_info_status;
    };
    /**
     * @return {?}
     */
    JSONReader.prototype.getprossesInfoRunning = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var process_info_running = null;
        /** @type {?} */
        var processInfo;
        try {
            if (this.inputJSON.name === 'process_info') {
                processInfo = (/** @type {?} */ (this.inputJSON.value));
                process_info_running = processInfo.running_seqnbr.toString();
            }
            else {
                processInfo = (/** @type {?} */ (this.inputJSON.value.process_info));
                process_info_running = processInfo.running_seqnbr.toString();
            }
        }
        catch (error) {
            console.log(error, "getprossesInfoRunning", "Jsonreader");
        }
        return process_info_running;
    };
    return JSONReader;
}());
export { JSONReader };
if (false) {
    /**
     * @type {?}
     * @private
     */
    JSONReader.prototype.inputJSON;
}
//# sourceMappingURL=data:application/json;base64,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