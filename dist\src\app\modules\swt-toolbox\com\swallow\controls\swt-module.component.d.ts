import { AfterViewInit, <PERSON>ementRef, <PERSON><PERSON><PERSON>ter, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { HashMap } from "../utils/HashMap.service";
import { ContextMenu } from "../controls/context-menu.component";
import { CommonService } from "../utils/common.service";
import { TitleWindow } from "./title-window.component";
import { Container } from '../containers/swt-container.component';
export declare class SwtModule extends Container implements AfterViewInit, OnInit, OnDestroy {
    elementRef: ElementRef;
    private comService;
    parentDocument: any;
    titleWindow: TitleWindow;
    changes: HashMap;
    creationComplete: EventEmitter<SwtModule>;
    preinitialize: EventEmitter<SwtModule>;
    title: string;
    private contextmenuItems;
    private _components;
    constructor(elementRef: ElementRef, comService: CommonService);
    private _contextMenu;
    /*- Parameters to handle context menu of screenVersion screen -START- Added by <PERSON><PERSON>ab JABALLAH on 17/10/2018  */
    contextMenu: ContextMenu;
    result: any;
    ngAfterViewInit(): void;
    ngOnDestroy(): void;
    ngOnInit(): void;
    /**
     * This method is used to close title window.
     */
    close(): void;
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param classObject
     */
    getQualifiedClassName(classObject: any): string;
    getSystemMessages(key: string, object?: Object): any;
    getCommonMessages(key: string, object?: Object): any;
    getLoginMessages(key: string, object?: Object): any;
    getAMLMessages(key: string, object?: Object): any;
    getDUPMessages(key: string, object?: Object): any;
    getARCMessages(key: string, object?: Object): any;
    getInputMessages(key: string, object?: Object): any;
    getCashMessages(key: string, object?: Object): any;
    getFatcaMessages(key: string, object?: Object): any;
    resetSpy(): void;
    subscribeSpy(components: any[]): void;
    onComponentChanged(event: any): void;
    onComponentNotChanged(event: any): void;
    onRightClick(event: any): void;
}
