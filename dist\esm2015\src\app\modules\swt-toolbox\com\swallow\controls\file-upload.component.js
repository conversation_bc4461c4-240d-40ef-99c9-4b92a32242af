/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtLabel } from "./swt-label.component";
import { SwtProgressBar } from "./progress-bar.component";
import { FileUploader } from 'ng2-file-upload';
import { SwtTextInput } from "./swt-text-input.component";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { Logger } from "../logging/logger.service";
import { StringUtils } from "../utils/string-utils.service";
import { SwtAlert } from '../utils/swt-alert.service';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class FileUpload extends SwtModule {
    /**
     * @param {?} element
     * @param {?} commonService
     */
    constructor(element, commonService) {
        super(element, commonService);
        this.element = element;
        this.commonService = commonService;
        this.value = 0;
        this._refUploadFile = new FileUploader({ url: SwtUtil.getBaseURL() + "core/file!upload.do?response=json", headers: [] });
        this.hasBaseDropZoneOver = false;
        this.hasAnotherDropZoneOver = false;
        this.uploadCompletedData = new Function();
        this.name = "";
        this.fileDeleted = false;
        this.jsonReader = new JSONReader();
        this.inputData = new HTTPComms(this.commonService);
        this.baseURL = "";
        this.actionMethod = "";
        this.actionPath = "";
        this.requestParams = new Array();
        this._arrUploadFiles = new Array();
        this._numCurrentUpload = 0;
        this._fileObj = { name: "", size: "" };
        this._validFiles = new Array();
        this._arrayFiles = new Array();
        this.uploadWindow = null;
        // Variable to set the number of current file
        this.fileNumber = 0;
        this._validExtensions = new Array();
        this._valideFilesDesc = "";
        this._tableName = "";
        this._columnName = "";
        this.logger = new Logger("FileUpload", this.commonService.httpclient);
        // initialize setter.
        this.SwtAlert = new SwtAlert(commonService);
    }
    //Set valid extensions
    /**
     * @param {?} value
     * @return {?}
     */
    set validExtensions(value) {
        this._validExtensions = value;
    }
    // Sets the valid files name
    /**
     * @param {?} value
     * @return {?}
     */
    set valideFilesDesc(value) {
        this._valideFilesDesc = value;
    }
    // Set table Name
    /**
     * @param {?} tableName
     * @return {?}
     */
    set tableName(tableName) {
        this._tableName = tableName;
    }
    // Set column Name
    /**
     * @param {?} columnName
     * @return {?}
     */
    set columnName(columnName) {
        this._columnName = columnName;
    }
    // Set uploadUrl
    /**
     * @param {?} strUploadUrl
     * @return {?}
     */
    set uploadUrl(strUploadUrl) {
        this._strUploadUrl = strUploadUrl;
    }
    // Set downloadUrl
    /**
     * @param {?} strDownloadUrl
     * @return {?}
     */
    set downloadUrl(strDownloadUrl) {
        this._strDownloadUrl = strDownloadUrl;
    }
    // Set deleteUrl
    /**
     * @param {?} strDeleteUrl
     * @return {?}
     */
    set deleteUrl(strDeleteUrl) {
        this._strDeleteUrl = strDeleteUrl;
    }
    /**
     * This method called when file upload initialized.
     * @return {?}
     */
    onLoad() {
        this._refUploadFile.onProgressItem = (/**
         * @param {?} fileItem
         * @param {?} progress
         * @return {?}
         */
        (fileItem, progress) => {
            this.updateProgBar(progress);
        });
        this._refUploadFile.onErrorItem = (/**
         * @param {?} item
         * @param {?} response
         * @param {?} status
         * @param {?} headers
         * @return {?}
         */
        (item, response, status, headers) => {
            this.onErrorItem(item, response, status, headers);
        });
        this._refUploadFile.onSuccessItem = (/**
         * @param {?} item
         * @param {?} response
         * @param {?} status
         * @param {?} headers
         * @return {?}
         */
        (item, response, status, headers) => {
            this.onSuccessItem(item, response, status, headers);
            // On multiFile select upload one by one.
            if (this._numCurrentUpload < this._refUploadFile.queue.length - 1) {
                this._numCurrentUpload++;
                this.progBar.value = 0;
                this.progBar.label = "";
                this.lblFomattedSize.text = this.formatFileSize(this._refUploadFile.queue[this._numCurrentUpload].file.size);
                this.toUploadFile.text = this._refUploadFile.queue[this._numCurrentUpload].file.name;
                this._refUploadFile.options.headers = [{
                        name: 'Uploadfile',
                        value: this._refUploadFile.queue[this._numCurrentUpload].file.name
                    }];
                this._refUploadFile.queue[this._numCurrentUpload].upload();
                this._arrayFiles.push(this._refUploadFile.queue[this._numCurrentUpload].file);
            }
            this.fileNumber--;
        });
        this._refUploadFile.onCompleteAll = (/**
         * @return {?}
         */
        () => {
            this.uploadCompletedData({ arrayFiles: this._arrayFiles });
        });
        $("input:file").change((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.progBar.value = 0;
            this.progBar.label = "";
            this.lblFomattedSize.text = this.formatFileSize(this._refUploadFile.queue[this._numCurrentUpload].file.size);
            this.toUploadFile.text = this._refUploadFile.queue[this._numCurrentUpload].file.name;
            this._refUploadFile.options.headers = [{
                    name: 'Uploadfile',
                    value: this._refUploadFile.queue[this._numCurrentUpload].file.name
                }];
            this._refUploadFile.queue[this._numCurrentUpload].upload();
            this._arrayFiles.push(this._refUploadFile.queue[this._numCurrentUpload].file);
            this.fileNumber = this._refUploadFile.queue.length - 1;
        }));
    }
    /**
     * This method is used to close file upload.
     * @param {?} event
     * @return {?}
     */
    closeHandler(event) {
        this.close();
    }
    /**
     * @param {?} parent
     * @param {?} callback
     * @return {?}
     */
    startUpload(parent, callback) {
        try {
            this.uploadWindow = SwtPopUpManager.createPopUp(parent, FileUpload, {
                title: SwtUtil.getCommonMessages('fileUpload.lblTitle.label'),
                uploadCompletedData: callback
            });
            this.uploadWindow.enableResize = false;
            this.uploadWindow.width = "510";
            this.uploadWindow.height = "180";
            this.uploadWindow.display();
        }
        catch (e) {
            this.logger.error("startUpload error: ", e);
        }
    }
    /**
     * @param {?} e
     * @return {?}
     */
    fileOverBase(e) {
        this.hasBaseDropZoneOver = e;
    }
    /**
     * @param {?} e
     * @return {?}
     */
    fileOverAnother(e) {
        this.hasAnotherDropZoneOver = e;
    }
    /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    onSuccessItem(item, response, status, headers) {
        /** @type {?} */
        const result = JSON.parse(response).fileUpload;
        // Show alert if file exist on server.
        if (result.singletons.message === 'FILE_ALREADY_EXISTS') {
            this.SwtAlert.info(StringUtils.substitute(SwtUtil.getCommonMessages('alert.file_already_exists'), this._fileObj.name, result.singletons.fileToStore), SwtUtil.getCommonMessages('alert_header.info'));
            if (this.fileNumber === 0) {
                //   SwtPopUpManager.removePopUp(this);
                this.close();
            }
        }
        else {
            if (this.fileNumber === 0) {
                this.SwtAlert.info(SwtUtil.getCommonMessages('alert.upload_data_info'), SwtUtil.getCommonMessages('alert_header.info'));
                //  SwtPopUpManager.removePopUp(this);
                this.close();
            }
        }
    }
    /**
     * @private
     * @param {?} item
     * @param {?} response
     * @param {?} status
     * @param {?} headers
     * @return {?}
     */
    onErrorItem(item, response, status, headers) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            //Security Error in uploading file.
            this.SwtAlert.error(SwtUtil.getCommonMessages('alert.security_error'), SwtUtil.getCommonMessages('alert_header.error'));
            errorLocation = 10;
            this.progBar.value = 0;
            this.progBar.label = "0%";
            errorLocation = 20;
            this.clearUpload();
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "onUploadIoError", errorLocation);
        }
    }
    //Cancel and clear eventlisteners on last upload
    /**
     * @private
     * @return {?}
     */
    clearUpload() {
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            if (this._refUploadFile) {
                errorLocation = 20;
                this._refUploadFile.cancelAll();
                errorLocation = 30;
                this._refUploadFile.clearQueue();
            }
            errorLocation = 40;
            this._numCurrentUpload = 0;
            this.updateProgBar();
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "clearUpload", errorLocation);
        }
    }
    /**
     * @private
     * @param {?=} numPerc
     * @return {?}
     */
    updateProgBar(numPerc = 0) {
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var strLabel = "";
        try {
            errorLocation = 10;
            strLabel = (this._numCurrentUpload + 1 <= this._arrUploadFiles.length && numPerc > 0 && numPerc < 100) ? numPerc + "% - " + strLabel : strLabel;
            errorLocation = 15;
            strLabel = (this._numCurrentUpload + 1 === this._arrUploadFiles.length && numPerc === 100) ? "Upload Complete - " + strLabel : strLabel;
            errorLocation = 20;
            strLabel = (this._arrUploadFiles.length === 0) ? "" : strLabel;
            errorLocation = 25;
            this.progBar.label = strLabel;
            this.progBar.value = numPerc;
            errorLocation = 30;
            // progBar.validateNow();
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "updateProgBar", errorLocation);
            this.logger.error("[ updateProgBar ] method error: ", error, " errorLocation : ", errorLocation);
        }
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    formatFileSize(value) {
        /** @type {?} */
        var _size = "";
        if (value < Math.pow(10, 3)) {
            _size = value + " " + SwtUtil.getCommonMessages("fileUpload.lblSize.unit");
        }
        else if (value > Math.pow(10, 3) && value < Math.pow(10, 6)) {
            _size = (value / Math.pow(10, 3)).toFixed(1) + " KB";
        }
        else if (value > Math.pow(10, 6) && value < Math.pow(10, 9)) {
            _size = (value / Math.pow(10, 6)).toFixed(1) + " Mo";
        }
        else {
            _size = (value / Math.pow(10, 9)).toFixed(1) + " Go";
        }
        return (_size);
    }
}
FileUpload.decorators = [
    { type: Component, args: [{
                selector: 'FileUpload',
                template: `
        <SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
            <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
                <SwtPanel id="lblTitle" title=" " width="100%" height="132">
                    <HBox paddingLeft="8">
                        <SwtLabel id="lblnameFile"
                                  text="{{ getCommonMessages('fileUpload.lblnameFile.label') }}"></SwtLabel>
                        <SwtTextInput #toUploadFile id="toUploadFile" editable="false" width="330" marginTop="5"></SwtTextInput>
                        <!--<SwtButton #btnAdd id="btnAdd" width="50" label="..." (onClick)="addFiles()"
                         toolTip="{{ getCommonMessages('fileUpload.btnAdd.tooltip') }}"></SwtButton>-->

                        <div class="file_Upload" >
                            <span>...</span>
                            <input type="file" class="upload"  ng2FileSelect [uploader]="_refUploadFile"   multiple />
                        </div>
                    </HBox>
                    <HBox>
                        <SwtLabel #lblSize id="lblSize" width="48" textAlign="right"
                                  text="{{ getCommonMessages('fileUpload.lblSize.label') }}"></SwtLabel>
                        <SwtLabel #lblFomattedSize id="lblFomattedSize" width="105" text=""></SwtLabel>
                    </HBox>
                    <HBox>
                        <SwtProgressBar #progBar marginLeft="15" marginBottom="5"  width="445"></SwtProgressBar>
                    </HBox>
                    <HBox>
                        <spacer width="405"></spacer>
                        <SwtButton #buttonClose
                                   id="buttonClose"
                                   label="{{ getCommonMessages('button.close') }}"
                                   (click)="close()"
                                   toolTip="{{ getCommonMessages('button.tooltip.close') }}"></SwtButton>
                    </HBox>
                </SwtPanel>
            </VBox>
        </SwtModule>
    `,
                styles: [`
        .file_Upload {
            position: relative;
            overflow: hidden;
            margin: 10px;
            border-bottom:1px solid #52869a!important ;
            border-top:1px solid #90b6c4!important;
            border-left:1px solid #52869a!important;
            border-right:1px solid #52869a!important;
            border-radius: 5px;
            font-size:12px;
            letter-spacing:0.2px;
            height: 23px;
            margin : 5px!important;
            font-weight:bolder;
            color: #173553;
            width: auto;
            padding: 0px 10px;
            background-color: #C2E5FF;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#C2E5FF), to(#92ACBF));
            background-image: -webkit-linear-gradient(top, #C2E5FF, #92ACBF);
            background-image: -moz-linear-gradient(top, #C2E5FF, #92ACBF);
            background-image: -ms-linear-gradient(top, #C2E5FF, #92ACBF);
            background-image: -o-linear-gradient(top, #C2E5FF, #92ACBF);
            background-image: linear-gradient(to bottom, #C2E5FF, #92ACBF);
            background-image: url("assets/images/button_bg.png");
        }

        .file_Upload input.upload {
            position: absolute;
            top: 0;
            right: 0;
            margin: 0;
            padding: 0;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            filter: alpha(opacity=0);
        }

        #lblnameFile {
            margin-top: 5px;
        }
    `]
            }] }
];
/** @nocollapse */
FileUpload.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
FileUpload.propDecorators = {
    progBar: [{ type: ViewChild, args: ["progBar",] }],
    lblFomattedSize: [{ type: ViewChild, args: ["lblFomattedSize",] }],
    toUploadFile: [{ type: ViewChild, args: ["toUploadFile",] }]
};
if (false) {
    /** @type {?} */
    FileUpload.prototype.id;
    /** @type {?} */
    FileUpload.prototype.value;
    /** @type {?} */
    FileUpload.prototype.progBar;
    /** @type {?} */
    FileUpload.prototype.lblFomattedSize;
    /** @type {?} */
    FileUpload.prototype.toUploadFile;
    /** @type {?} */
    FileUpload.prototype._refUploadFile;
    /** @type {?} */
    FileUpload.prototype.hasBaseDropZoneOver;
    /** @type {?} */
    FileUpload.prototype.hasAnotherDropZoneOver;
    /** @type {?} */
    FileUpload.prototype.uploadCompletedData;
    /** @type {?} */
    FileUpload.prototype.name;
    /** @type {?} */
    FileUpload.prototype.fileDeleted;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._fileName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.jsonReader;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.lastRecievedXML;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.prevRecievedXML;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.inputData;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.baseURL;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.actionMethod;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strUploadUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strDownloadUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._strDeleteUrl;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._arrUploadFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._numCurrentUpload;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._fileObj;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._validFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._arrayFiles;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.uploadWindow;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.fileNumber;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._validExtensions;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._valideFilesDesc;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._tableName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype._columnName;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.element;
    /**
     * @type {?}
     * @private
     */
    FileUpload.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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