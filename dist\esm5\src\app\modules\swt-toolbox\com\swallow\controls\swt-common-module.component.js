/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { CommonService } from "../utils/common.service";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { StringUtils } from "../utils/string-utils.service";
import { parentApplication } from "../utils/parent-application.service";
import { ExternalInterface } from "../utils/external-interface.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtCanvas } from "./swt-canvas.component";
import { HBox } from "./swt-hbox.component";
import { SwtButton } from "./swt-button.component";
import { SwtLoadingImage } from "./swt-loading-image.component";
import { SwtHelpButton } from "./swt-helpButton.component";
import { Keyboard } from "../utils/keyboard.service";
import { focusManager } from "../managers/focus-manager.service";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { SwtTabNavigatorHandler } from "../utils/swt-tabnavigator.service";
import { ModuleLoader } from "../utils/module-loader.service";
import { ModuleEvent } from "../events/swt-events.module";
import { Logger } from '../logging/logger.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtCommonModule = /** @class */ (function (_super) {
    tslib_1.__extends(SwtCommonModule, _super);
    function SwtCommonModule(_element, _commonService) {
        var _this = _super.call(this, _element, _commonService) || this;
        _this._element = _element;
        _this._commonService = _commonService;
        _this._creationComplete = new EventEmitter();
        //set preinitialize event
        _this._preinitialize = new EventEmitter();
        // @Input to set dynamic buttons.
        _this.buttons = "";
        _this.actionPathReport = "report!exportData.do?";
        _this.addParamReport = "";
        _this.swtKVParams = null;
        _this.baseURL = "";
        _this.actionMethod = "";
        _this.actionPath = "";
        _this.requestParams = new Array();
        //To identify the url path for report setting
        _this.urlReportSetting = null;
        // iable to identify a grid Object
        _this.gridReport = null;
        //To store menu access
        _this.menuAccess = null;
        //set program
        _this.programId = null;
        //To identify the module window status
        _this.undockWindow = null;
        /* iable to hold init X value to load child screen */
        _this.initXValue = 300;
        //hold componentId
        _this.componentId = null;
        _this.reportGroupId = null;
        //VBox to view report setting
        _this.newVBox = null;
        // SwtAlert to show errors
        _this.SwtAlert = null;
        _this.jsonReader = new JSONReader();
        //Buttons
        _this.addButton = null;
        _this.changeButton = null;
        _this.viewButton = null;
        _this.duplicateButton = null;
        _this.deleteButton = null;
        _this.searchButton = null;
        _this.cancelButton = null;
        _this.saveButton = null;
        _this.printIcon = null;
        _this.closeButton = null;
        _this.deleteMethod = null;
        _this.duplicateMethod = null;
        _this.popupClosedEvent = null;
        _this.kvParamsMethod = null;
        //To identify the url path
        _this.urlDetailsPathAdd = null;
        //To identify the url path
        _this.urlDetailsPathChange = null;
        //To identify the url path
        _this.urlDetailsPathView = null;
        //To identify the url path
        _this.urlDetailsPathDelete = null;
        //To identify the url path
        _this.urlDetailsPathDuplicate = null;
        //To identify the url path
        _this.urlDetailsPathSave = null;
        //To identify the title of child window
        _this.childPanelTitleAdd = null;
        //To identify the title of child window
        _this.childPanelTitleChange = null;
        //To identify the title of child window
        _this.childPanelTitleView = null;
        //To identify the title of child window
        _this.childPanelTitleDelete = null;
        //To identify the title of child window
        _this.childPanelTitleDuplicate = null;
        //To identify the component Add.
        _this.addComponent = null;
        //To identify the component Change.
        _this.changeComponent = null;
        //To identify the component View.
        _this.viewComponent = null;
        //To identify the component Delete.
        _this.deleteComponent = null;
        //To identify the component Duplicate.
        _this.duplicateComponent = null;
        //To identify the component ReportSetting.
        _this.reportSettingComponent = null;
        //To identify the title of child window
        _this.saveComponent = null;
        _this.vBoxCustomGridPaddingTop = 5;
        _this.lockedColumnCount = 0;
        _this.uniqueColumn = "";
        _this.eventString = "";
        _this.screenName = "";
        _this.versionNumber = "";
        _this.availableScreen = null;
        _this.objectType = null;
        _this.recordId = null;
        _this.partyType = null;
        _this.closeURL = null;
        // Initialize ModuleLoader to load child windows
        _this.mLoader = null;
        //Panel to view child window
        _this.childPanel = null;
        _this._inputData = new HTTPComms(_this._commonService);
        _this.popUpScreen = null;
        _this.SwtAlert = new SwtAlert(_commonService);
        _this.logger = new Logger("SwtCommonModule", _commonService.httpclient);
        return _this;
    }
    /**
     * This method fired on commonModule load.
     */
    /**
     * This method fired on commonModule load.
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.onCommonModuleInit = /**
     * This method fired on commonModule load.
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this._creationComplete.emit(this);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.onCommonModuleBeforeInit = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.buttons) {
            $(this.commonTemplate.nativeElement).hide();
            $(this.buttonsTemplate.nativeElement).show();
            this.setDynamicButtons();
        }
        else {
            $(this.commonTemplate.nativeElement).show();
            $(this.buttonsTemplate.nativeElement).hide();
        }
        this._preinitialize.emit(this);
    };
    /**
     * This is a report icon action handler method
     * @param type
     * @param displayFilter
     * @param xmlDataSource
     */
    /**
     * This is a report icon action handler method
     * @param {?} type
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    SwtCommonModule.prototype.report = /**
     * This is a report icon action handler method
     * @param {?} type
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    function (type, displayFilter, xmlDataSource) {
        if (displayFilter === void 0) { displayFilter = null; }
        if (xmlDataSource === void 0) { xmlDataSource = null; }
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var moduleId = null;
        /** @type {?} */
        var actionParams = "";
        /** @type {?} */
        var displayFilterAsJSON;
        this.logger.info("[ report ] method START");
        try {
            errorLocation = 10;
            /** @type {?} */
            var parentKVParams = this.getKVParams();
            errorLocation = 20;
            displayFilterAsJSON = StringUtils.getKVTypeTabAsXML(displayFilter, '', '', '');
            errorLocation = 30;
            //Get the current Module Id.
            moduleId = parentApplication.getCurrentModuleId();
            errorLocation = 40;
            //set the action path
            this.actionPath = this.actionPathReport;
            actionParams = "type=" + type;
            actionParams = actionParams + "&action=EXPORT&print=ALL&currentModuleId=" + moduleId + "&response=json";
            actionParams = actionParams + "&programId=" + this.programId + "&reportGroupId=" + this.reportGroupId
                + "&kvParams=" + StringUtils.encode64(parentKVParams).replace(/\+/gi, ")").replace(/\=/gi, "(")
                + "&displayFilter=" + StringUtils.encode64(displayFilterAsJSON.toXMLString()).replace(/\+/gi, ")").replace(/\=/gi, "(");
            errorLocation = 50;
            //Calls a method from mainflex.jsp file
            ExternalInterface.call("getReportsAndProgress", this.actionPath + actionParams, xmlDataSource != null ? StringUtils.encode64(xmlDataSource.toXMLString()) : "");
        }
        catch (error) {
            this.logger.error("[ report ] method - error ", error);
            this.SwtAlert.error(error.message, 'Programming Error');
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "report", errorLocation);
        }
        this.logger.info("[ report ] method END");
    };
    /**
     * keyDownHandlerEvent
     *
     * @param event:  KeyboardEvent
     *
     * This is a key event listener, used to perform the operation
     * when hit the enter key based on the currently focused property(Button)
     */
    /**
     * keyDownHandlerEvent
     *
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.keyDownHandlerEvent = /**
     * keyDownHandlerEvent
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // Variable Number Error Location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 0;
            //Currently focussed property name
            /** @type {?} */
            var eventString = focusManager.getFocus().name;
            errorLocation = 10;
            if ((event.keyCode === Keyboard.ENTER)) {
                errorLocation = 20;
                if (eventString === "closeButton") {
                    errorLocation = 30;
                    this.closeScreenHandler(event);
                }
                else if (eventString === "csv") {
                    errorLocation = 40;
                    this.report('csv');
                }
                else if (eventString === "excel") {
                    errorLocation = 50;
                    this.report('xls');
                }
                else if (eventString === "pdf") {
                    errorLocation = 60;
                    this.report('pdf');
                }
                else if (eventString === 'settingButton') {
                    errorLocation = 70;
                    this.getSetting();
                }
                else if (eventString === "addButton") {
                    errorLocation = 30;
                    this.clickHandler(event);
                }
                else if (eventString === "changeButton") {
                    errorLocation = 40;
                    this.clickHandler(event);
                }
                else if (eventString === "viewButton") {
                    errorLocation = 50;
                    this.clickHandler(event);
                }
                else if (eventString === "duplicateButton") {
                    errorLocation = 60;
                    this.clickHandler(event);
                }
                else if (eventString === "printIcon") {
                    errorLocation = 70;
                    this.clickHandler(event);
                }
            }
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "keyDownHandlerEvent", errorLocation);
        }
    };
    /**
     * getSetting
     *
     * @param none
     *
     * This method used to create the child report setting screen
     */
    /**
     * getSetting
     *
     * @return {?}
     */
    SwtCommonModule.prototype.getSetting = /**
     * getSetting
     *
     * @return {?}
     */
    function () {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.swtKVParams = this.getKVParams();
            errorLocation = 10;
            // Get component  Id from xml
            this.componentId = this.lastRecievedJSON.screenid;
            errorLocation = 20;
            //Add listener to moduleReadyEventHandler
            this.moduleReadyEventHandlerReportSetting(event);
            /** @type {?} */
            var title = SwtUtil.getCommonMessages('report.windowtitle.title');
            errorLocation = 30;
            // set details screen title.
            /** @type {?} */
            var data = this.getDataFromUrl(this.urlReportSetting);
            // Load view screen
            // this.loadModule(title, this.reportSettingComponent, this.urlReportSetting, data);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "getSetting", errorLocation);
        }
    };
    /**
     * This method is used to show help
     * page.
     */
    /**
     * This method is used to show help
     * page.
     * @return {?}
     */
    SwtCommonModule.prototype.showhelp = /**
     * This method is used to show help
     * page.
     * @return {?}
     */
    function () {
    };
    /**
     * kvReportParams function that should be overriden by child classes
     */
    /**
     * kvReportParams function that should be overriden by child classes
     * @protected
     * @return {?}
     */
    SwtCommonModule.prototype.kvReportParams = /**
     * kvReportParams function that should be overriden by child classes
     * @protected
     * @return {?}
     */
    function () {
        if (this.kvParamsMethod) {
            return this.kvParamsMethod();
        }
        else {
            throw new Error("Programming Error: function kvReportParams() must be overriden in child classes implementing SwtCommonModule !!");
        }
    };
    /**
     * Constructs the report parameters as xml string
     */
    /**
     * Constructs the report parameters as xml string
     * @protected
     * @return {?}
     */
    SwtCommonModule.prototype.getKVParams = /**
     * Constructs the report parameters as xml string
     * @protected
     * @return {?}
     */
    function () {
        /** @type {?} */
        var paramsList = this.kvReportParams();
        /** @type {?} */
        var kvAsXML = StringUtils.getKVTypeTabAsXML(paramsList, '', '', '');
        return kvAsXML.toXMLString();
    };
    /**
     * This method is used
     * @param button
     */
    /**
     * This method is used
     * @protected
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.clickHandler = /**
     * This method is used
     * @protected
     * @param {?} event
     * @return {?}
     */
    function (event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        // String to hold the current target of the event
        /** @type {?} */
        var eventString = null;
        this.logger.info("[ clickHandler ] method START");
        try {
            //Currently focussed property name
            eventString = focusManager.getFocus().name;
            if (eventString === "addButton" && this.urlDetailsPathAdd != null) {
                // Load view screen
                this.loadModule(this.childPanelTitleAdd, this.urlDetailsPathAdd);
            }
            else if (eventString === "changeButton" && this.urlDetailsPathChange != null) {
                // Load change screen
                this.loadModule(this.childPanelTitleChange, this.urlDetailsPathChange);
            }
            else if (eventString === "viewButton" && this.urlDetailsPathView != null) {
                // Load view screen
                this.loadModule(this.childPanelTitleView, this.urlDetailsPathView);
            }
            if (eventString === "deleteButton") {
                // Load view screen
                this.deleteAction();
            }
            else if (eventString === "duplicateButton") {
                this.duplicateAction();
            }
            else if (eventString === "printIcon") {
                //duplicateAction(); TODO
            }
            else if (eventString === "closeButton") {
                /** @type {?} */
                var res = this._commonService.Router.url.split(";");
                SwtTabNavigatorHandler.closeTab(res[0].substr(1, res[0].length));
                this.closeScreenHandler(event);
            }
        }
        catch (error) {
            this.logger.error("[ clickHandler ] method - error ", error);
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "clickHandler", errorLocation);
        }
        this.logger.info("[ clickHandler ] method END");
    };
    /**
     * loadModule
     *
     * This method is used to open child module
     */
    /**
     * loadModule
     *
     * This method is used to open child module
     * @protected
     * @param {?} title
     * @param {?} url
     * @return {?}
     */
    SwtCommonModule.prototype.loadModule = /**
     * loadModule
     *
     * This method is used to open child module
     * @protected
     * @param {?} title
     * @param {?} url
     * @return {?}
     */
    function (title, url) {
        var _this = this;
        this.logger.info("[ loadModule ] method START");
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (this.mLoader === null) {
                this.childPanel = SwtPopUpManager.createPopUp(this.context);
                this.childPanel.title = title;
                this.childPanel.isModal = true;
                this.childPanel.showControls = true;
                this.childPanel.enableResize = true;
                this.mLoader = new ModuleLoader(this._commonService);
                this.mLoader.percentHeight = 50;
                this.mLoader.percentWidth = 50;
                errorLocation = 40;
                /* Load Add screen module */
                this.mLoader.addEventListener(ModuleEvent.READY, (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) { return _this.moduleReadyEventHandler(event); }));
                errorLocation = 50;
                this.mLoader.loadModule(url);
            }
            else {
                SwtPopUpManager.bringToFront(null); // TODO add popup instance to be set in the top.
            }
        }
        catch (error) {
            this.logger.error("[ loadModule ] method - error ", error);
        }
        this.logger.info("[ loadModule ] method END");
    };
    /**
     * moduleReadyEventHandler
     *
     * @param event: ModuleEvent
     *
     * This method is used to load view user screen as a popup, once it is ready to load.
     */
    /**
     * moduleReadyEventHandler
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.moduleReadyEventHandler = /**
     * moduleReadyEventHandler
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    function (event) {
        var _this = this;
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.childPanel.addChild(event.target);
            errorLocation = 20;
            this.childPanel.onClose.subscribe((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.popupClosedEventHandler(event);
            }));
            this.childPanel.display();
            errorLocation = 30;
            /* Disable buttons while child screen is loaded */
            this._helpIcon.enabled = false;
            this._helpIcon.buttonMode = false;
            this._pdf.enabled = false;
            this._pdf.buttonMode = false;
            this._csv.enabled = false;
            this._csv.buttonMode = false;
            this._excel.enabled = false;
            this._excel.buttonMode = false;
            this._settingButton.enabled = false;
            this._settingButton.buttonMode = false;
            errorLocation = 40;
            // check if addButton is not null
            if (this.addButton != null) {
                this.addButton.enabled = false;
                this.addButton.buttonMode = false;
            }
            // check if changeButton is not null
            if (this.changeButton != null) {
                // disable change button
                this.changeButton.enabled = false;
                this.changeButton.buttonMode = false;
            }
            // check if viewButton button is not null
            if (this.viewButton != null) {
                // disable view button
                this.viewButton.enabled = false;
                this.viewButton.buttonMode = false;
            }
            // check if deleteButton button is not null
            if (this.deleteButton != null) {
                // disable delete button
                this.deleteButton.enabled = false;
                this.deleteButton.buttonMode = false;
            }
            // check if duplicateButton is not null
            if (this.duplicateButton != null) {
                // disable duplicate button
                this.duplicateButton.enabled = false;
                this.duplicateButton.buttonMode = false;
            }
            if (this.searchButton != null) {
                this.searchButton.enabled = false;
                this.searchButton.buttonMode = false;
            }
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "moduleReadyEventHandler", errorLocation);
        }
    };
    /**
     * popupClosedEventHandler
     *
     * @param event :Event
     *
     * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
     *
     */
    /**
     * popupClosedEventHandler
     *
     * @protected
     * @param {?} event :Event
     *
     * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
     *
     * @return {?}
     */
    SwtCommonModule.prototype.popupClosedEventHandler = /**
     * popupClosedEventHandler
     *
     * @protected
     * @param {?} event :Event
     *
     * Method called when pop up is closed to refresh Grid and Enable Buttons after pop up closed
     *
     * @return {?}
     */
    function (event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        this.logger.info("[ popupClosedEventHandler ] method START");
        try {
            errorLocation = 10;
            this._helpIcon.enabled = true;
            this._helpIcon.buttonMode = true;
            this._pdf.enabled = true;
            this._pdf.buttonMode = true;
            this._csv.enabled = true;
            this._csv.buttonMode = true;
            this._excel.enabled = true;
            this._excel.buttonMode = true;
            this._settingButton.enabled = true;
            this._settingButton.buttonMode = true;
            errorLocation = 20;
            if (this.searchButton != null) {
                this.searchButton.enabled = true;
                this.searchButton.buttonMode = true;
            }
            errorLocation = 30;
            if (this.swtCommonGrid) {
                if (this.swtCommonGrid.selectedIndex !== -1) {
                    this.viewButton.enabled = true;
                    this.viewButton.buttonMode = true;
                    if (this.menuAccess === "0") {
                        if (this.changeButton != null) {
                            this.changeButton.enabled = true;
                            this.changeButton.buttonMode = true;
                        }
                        if (this.deleteButton != null) {
                            this.deleteButton.enabled = true;
                            this.deleteButton.buttonMode = true;
                        }
                        if (this.duplicateButton != null) {
                            this.duplicateButton.enabled = true;
                            this.duplicateButton.buttonMode = true;
                        }
                    }
                    else {
                        if (this.changeButton != null) {
                            this.changeButton.enabled = false;
                            this.changeButton.buttonMode = false;
                        }
                        if (this.deleteButton != null) {
                            this.deleteButton.enabled = false;
                            this.deleteButton.buttonMode = false;
                        }
                        if (this.duplicateButton != null) {
                            this.duplicateButton.enabled = false;
                            this.duplicateButton.buttonMode = false;
                        }
                    }
                }
                else {
                    errorLocation = 40;
                    this.viewButton.enabled = false;
                    this.viewButton.buttonMode = false;
                    if (this.changeButton != null) {
                        this.changeButton.enabled = false;
                        this.changeButton.buttonMode = false;
                    }
                    if (this.deleteButton != null) {
                        this.deleteButton.enabled = false;
                        this.deleteButton.buttonMode = false;
                    }
                    if (this.duplicateButton != null) {
                        this.duplicateButton.enabled = false;
                        this.duplicateButton.buttonMode = false;
                    }
                }
            }
            // check for Add button
            if (this.menuAccess === '0') {
                this.addButton.enabled = true;
                this.addButton.buttonMode = true;
                if (this.duplicateButton != null) {
                    this.duplicateButton.enabled = true;
                    this.duplicateButton.buttonMode = true;
                }
            }
            else {
                this.addButton.enabled = false;
                this.addButton.buttonMode = false;
            }
            // initialize mloader and childPanel.
            this.mLoader = null;
            this.childPanel = null;
        }
        catch (error) {
            this.logger.error("[ popupClosedEventHandler ] method - error ", error);
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "popupClosedEventHandler", errorLocation);
        }
        this.logger.info("[ popupClosedEventHandler ] method END");
    };
    /**
     * dispose
     *
     * This is a event handler, used to close the current tab/window
     */
    /**
     * dispose
     *
     * This is a event handler, used to close the current tab/window
     * @protected
     * @return {?}
     */
    SwtCommonModule.prototype.dispose = /**
     * dispose
     *
     * This is a event handler, used to close the current tab/window
     * @protected
     * @return {?}
     */
    function () {
        // remove event listeners on buttons
        if (this.addButton != null) {
            this.addButton.click = null;
            this.addButton = null;
        }
        if (this.viewButton != null) {
            this.viewButton.click = null;
            this.viewButton = null;
        }
        if (this.changeButton != null) {
            this.changeButton.click = null;
            this.changeButton = null;
        }
        if (this.deleteButton != null) {
            this.deleteButton.click = null;
            this.deleteButton = null;
        }
        if (this.duplicateButton != null) {
            this.duplicateButton.click = null;
            this.duplicateButton = null;
        }
        if (this.printIcon != null) {
            this.printIcon.click = null;
            this.printIcon = null;
        }
        //
        // this.removeEventListener(FocusEvent.KEY_FOCUS_CHANGE, focusChangeHandler, true);
        // this.removeAllChildren();
        // this._loadingImage.unloadAndStop(true);
        this.requestParams = null;
        this._inputData = null;
        this.jsonReader = null;
        this.lastRecievedJSON = null;
        this.prevRecievedJSON = null;
        this.baseURL = null;
        this.programId = null;
        this.mLoader = null;
        this.childPanel = null;
    };
    /**
     * closeScreenHandler
     * @param event: Event
     * Function called to close fields to check maintenance screen.
     */
    /**
     * closeScreenHandler
     * @protected
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.closeScreenHandler = /**
     * closeScreenHandler
     * @protected
     * @param {?} event
     * @return {?}
     */
    function (event) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.dispose();
            errorLocation = 20;
            /* If screen is undocked call javascript close */
            if (this.undockWindow === "true") {
                errorLocation = 30;
                ExternalInterface.call("closeScreen");
            }
            else if (this.popUpScreen === "popUp") {
                errorLocation = 30;
            }
            else {
                errorLocation = 40;
                parentApplication.navigator.removeChildAt(parentApplication.navigator.selectedIndex);
            }
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.AML_MODULE_ID, this.getQualifiedClassName(this), "closeScreenHandler", errorLocation);
        }
    };
    /**
     * moduleReadyEventHandlerReportSetting
     *
     * @param event: ModuleEvent
     *
     * This method is used to load report setting screen as a popup, once it is ready to load.
     */
    /**
     * moduleReadyEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.moduleReadyEventHandlerReportSetting = /**
     * moduleReadyEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    function (event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            this.popupClosedEventHandler(event);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this), "moduleReadyEventHandlerReportSetting", errorLocation);
        }
    };
    /**
     * popupClosedEventHandlerReportSetting
     *
     * @param event: Event
     *
     * This function is used to handle the child window close event
     */
    /**
     * popupClosedEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    SwtCommonModule.prototype.popupClosedEventHandlerReportSetting = /**
     * popupClosedEventHandlerReportSetting
     *
     * @protected
     * @param {?} event
     * @return {?}
     */
    function (event) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.getQualifiedClassName(this) + ".mxml", "popupClosedEventHandlerReportSetting", errorLocation);
        }
    };
    /**
     * deleteAction
     */
    /**
     * deleteAction
     * @protected
     * @return {?}
     */
    SwtCommonModule.prototype.deleteAction = /**
     * deleteAction
     * @protected
     * @return {?}
     */
    function () {
        if (this.deleteMethod) {
            this.deleteMethod();
        }
        else {
            throw new Error("you must set deleteMethod in child class.");
        }
    };
    /**
     * duplicateAction
     */
    /**
     * duplicateAction
     * @protected
     * @return {?}
     */
    SwtCommonModule.prototype.duplicateAction = /**
     * duplicateAction
     * @protected
     * @return {?}
     */
    function () {
        if (this.duplicateMethod) {
            this.duplicateMethod();
        }
        else {
            throw new Error("you must set duplicateMethod in child class.");
        }
    };
    /**
     * @private
     * @return {?}
     */
    SwtCommonModule.prototype.setDynamicButtons = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        // set dynamic buttons.
        /** @type {?} */
        var buttons = this.buttons.split(",");
        // loop on buttons array.
        for (var index in buttons) {
            if (buttons[index] === "add") {
                this.addButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.addButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.addButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.addButton.tabIndex = Number(index + 1);
                this.addButton.id = buttons[index] + "Button";
                this.addButton.name = buttons[index] + "Button";
                this.addButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "change") {
                this.changeButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.changeButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.changeButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.changeButton.tabIndex = Number(index + 1);
                this.changeButton.id = buttons[index] + "Button";
                this.changeButton.name = buttons[index] + "Button";
                this.changeButton.enabled = false;
                this.changeButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "view") {
                this.viewButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.viewButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.viewButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.viewButton.tabIndex = Number(index + 1);
                this.viewButton.id = buttons[index] + "Button";
                this.viewButton.name = buttons[index] + "Button";
                this.viewButton.enabled = false;
                this.viewButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "delete") {
                this.deleteButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.deleteButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.deleteButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.deleteButton.tabIndex = Number(index + 1);
                this.deleteButton.id = buttons[index] + "Button";
                this.deleteButton.name = buttons[index] + "Button";
                this.deleteButton.enabled = false;
                this.deleteButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "duplicate") {
                this.duplicateButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.duplicateButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.duplicateButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.duplicateButton.tabIndex = Number(index + 1);
                this.duplicateButton.id = buttons[index] + "Button";
                this.duplicateButton.name = buttons[index] + "Button";
                this.duplicateButton.enabled = false;
                this.duplicateButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "search") {
                this.searchButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.searchButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.searchButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.searchButton.tabIndex = Number(index + 1);
                this.searchButton.id = buttons[index] + "Button";
                this.searchButton.name = buttons[index] + "Button";
                this.searchButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "cancel") {
                this.cancelButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.cancelButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.cancelButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.cancelButton.tabIndex = Number(index + 1);
                this.cancelButton.id = buttons[index] + "Button";
                this.cancelButton.name = buttons[index] + "Button";
                this.cancelButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "save") {
                this.saveButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.saveButton.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.saveButton.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.saveButton.tabIndex = Number(index + 1);
                this.saveButton.id = buttons[index] + "Button";
                this.saveButton.name = buttons[index] + "Button";
                this.saveButton.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
            else if (buttons[index] === "print") {
                this.printIcon = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
                this.printIcon.label = SwtUtil.getCommonMessages("button." + buttons[index]);
                this.printIcon.toolTip = SwtUtil.getCommonMessages("button.tooltip." + buttons[index]);
                this.printIcon.tabIndex = Number(index + 1);
                this.printIcon.id = buttons[index] + "Button";
                this.printIcon.name = buttons[index] + "Button";
                this.printIcon.click = (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this.clickHandler(event);
                });
            }
        }
        // add close button to template.
        this.closeButton = (/** @type {?} */ (this.hboxButtons.addChild(SwtButton)));
        this.closeButton.label = SwtUtil.getCommonMessages("button.close");
        this.closeButton.toolTip = SwtUtil.getCommonMessages("button.tooltip.close");
        this.closeButton.tabIndex = 10;
        this.closeButton.id = "closeButton";
        this.closeButton.click = (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.clickHandler(event);
        });
    };
    /**
     * This method is used to split url and
     * get params from this last one.
     * @param url
     */
    /**
     * This method is used to split url and
     * get params from this last one.
     * @private
     * @param {?} url
     * @return {?}
     */
    SwtCommonModule.prototype.getDataFromUrl = /**
     * This method is used to split url and
     * get params from this last one.
     * @private
     * @param {?} url
     * @return {?}
     */
    function (url) {
        /** @type {?} */
        var data = new Array();
        /** @type {?} */
        var errorLocation;
        this.logger.info("[ getDataFromUrl ] method START");
        try {
            /** @type {?} */
            var qm = url.lastIndexOf("?");
            errorLocation = 10;
            if (qm !== -1) {
                errorLocation = 20;
                /** @type {?} */
                var query = url.substr(qm + 1);
                /** @type {?} */
                var params = query.split("&");
                errorLocation = 30;
                for (var i = 0; i < params.length; i++) {
                    errorLocation = 40;
                    /** @type {?} */
                    var param = params[i];
                    /** @type {?} */
                    var nameValue = param.split("=");
                    errorLocation = 60;
                    if (nameValue.length === 2) {
                        errorLocation = 70;
                        /** @type {?} */
                        var key = nameValue[0];
                        /** @type {?} */
                        var val = nameValue[1];
                        errorLocation = 80;
                        if (key === "access") {
                            errorLocation = 90;
                            //Get access right
                            data["menuAccess"] = val;
                        }
                        if (key === "programId") {
                            errorLocation = 100;
                            //Get programId
                            data["programId"] = val;
                        }
                        if (key === "popUpScreen") {
                            errorLocation = 110;
                            //Get popUpScreen
                            data["popUpScreen"] = val;
                        }
                        if (key === "availableScreen") {
                            errorLocation = 120;
                            //Get availabeScreen
                            data["availableScreen"] = val;
                        }
                        if (key === "recordId") {
                            errorLocation = 130;
                            data["recordId"] = val;
                        }
                        if (key === "objectType") {
                            errorLocation = 140;
                            data["objectType"] = val;
                        }
                        if (key === "pv_party_type") {
                            errorLocation = 150;
                            data["partyType"] = val;
                        }
                    }
                }
            }
            parentApplication.loaderInfo.url = url;
        }
        catch (error) {
            this.logger.error("[ getDataFromUrl ] method - error ", error);
        }
        this.logger.info("[ getDataFromUrl ] method END");
        return data;
    };
    SwtCommonModule.decorators = [
        { type: Component, args: [{
                    selector: 'SwtCommonModule',
                    template: "\n        <SwtModule (creationComplete)=\"onCommonModuleInit($event)\" (preinitialize)=\"onCommonModuleBeforeInit($event)\"\n                   width=\"{{ _width }}\" height=\"{{ _height }}\">\n            <div #commonTemplate [style.width.px]=\"_width\" [style.height.px]=\"_height\">\n                <ng-content></ng-content>\n            </div>\n            <div #buttonsTemplate class=\"swtmodule\" [style.width.px]=\"_width\" [style.height.px]=\"_height\">\n                <SwtCanvas #canvasContainer width=\"100%\" height=\"90%\" paddingLeft=\"10\" paddingRight=\"10\" paddingTop=\"10\">\n\n                    <SwtCanvas #customGrid width=\"100%\" height=\"87%\">\n                        <ng-content select=\".commonModule\"></ng-content>\n                    </SwtCanvas>\n                    <SwtCanvas width=\"100%\" marginTop=\"10\">\n                        <HBox width=\"100%\" height=\"100%\">\n                            <HBox #hboxButtons width=\"100%\"></HBox>\n                            <HBox #hboxExport horizontalAlign=\"right\">\n                                <SwtLoadingImage #loadingImage id=\"loadingImage\"></SwtLoadingImage>\n                                <SwtButton #settingButton\n                                           buttonMode=\"true\"\n                                           styleName=\"reportIcon\"\n                                           tabIndex=\"11\"\n                                           id=\"settingButton\"\n                                           (click)=\"getSetting()\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                           toolTip=\"{{ getCommonMessages('button.report_setting') }}\"></SwtButton>\n                                <SwtButton #csv\n                                           buttonMode=\"true\"\n                                           tabIndex=\"12\"\n                                           id=\"csv\"\n                                           enabled=\"true\"\n                                           styleName=\"csvIcon\"\n                                           (click)=\"report('csv')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.csv') }}\"></SwtButton>\n                                <SwtButton #excel\n                                           buttonMode=\"true\"\n                                           tabIndex=\"13\"\n                                           id=\"excel\"\n                                           enabled=\"true\"\n                                           styleName=\"excelIcon\"\n                                           (click)=\"report('xls')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.excel') }}\"></SwtButton>\n                                <SwtButton #pdf\n                                           buttonMode=\"true\"\n                                           tabIndex=\"14\"\n                                           id=\"pdf\"\n                                           enabled=\"true\"\n                                           styleName=\"pdfIcon\"\n                                           (click)=\"report('pdf')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.pdf') }}\"></SwtButton>\n                                <SwtButton #fatcaReport\n                                           buttonMode=\"true\"\n                                           tabIndex=\"15\"\n                                           id=\"fatcaReport\"\n                                           includeInLayout=\"false\"\n                                           visible=\"false\"\n                                           enabled=\"true\"\n                                           styleName=\"fatcaReportIcon\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.pdf') }}\"></SwtButton>\n                                <SwtHelpButton #helpIcon\n                                               id=\"helpIcon\"\n                                               tabIndex=\"16\"\n                                               buttonMode=\"true\"\n                                               (click)=\"showhelp()\"\n                                               enabled=\"true\"\n                                               styleName=\"helpIcon\"></SwtHelpButton>\n                            </HBox>\n                        </HBox>\n                    </SwtCanvas>\n                </SwtCanvas>\n            </div>\n        </SwtModule>\n    "
                }] }
    ];
    /** @nocollapse */
    SwtCommonModule.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtCommonModule.propDecorators = {
        _creationComplete: [{ type: Output, args: ["creationComplete",] }],
        _preinitialize: [{ type: Output, args: ['preinitialize',] }],
        _width: [{ type: Input, args: ["width",] }],
        _height: [{ type: Input, args: ["height",] }],
        hboxButtons: [{ type: ViewChild, args: ["hboxButtons",] }],
        customGrid: [{ type: ViewChild, args: ["customGrid",] }],
        reportbuttons: [{ type: ViewChild, args: ["reportbuttons",] }],
        _loadingImage: [{ type: ViewChild, args: ["loadingImage",] }],
        _settingButton: [{ type: ViewChild, args: ["settingButton",] }],
        _csv: [{ type: ViewChild, args: ["csv",] }],
        _excel: [{ type: ViewChild, args: ["excel",] }],
        _pdf: [{ type: ViewChild, args: ["pdf",] }],
        _fatcaReport: [{ type: ViewChild, args: ["fatcaReport",] }],
        _helpIcon: [{ type: ViewChild, args: ["helpIcon",] }],
        buttonsTemplate: [{ type: ViewChild, args: ["buttonsTemplate",] }],
        commonTemplate: [{ type: ViewChild, args: ["commonTemplate",] }],
        buttons: [{ type: Input, args: ['buttons',] }]
    };
    return SwtCommonModule;
}(SwtModule));
export { SwtCommonModule };
if (false) {
    /** @type {?} */
    SwtCommonModule.prototype._creationComplete;
    /** @type {?} */
    SwtCommonModule.prototype._preinitialize;
    /** @type {?} */
    SwtCommonModule.prototype._width;
    /** @type {?} */
    SwtCommonModule.prototype._height;
    /** @type {?} */
    SwtCommonModule.prototype.hboxButtons;
    /** @type {?} */
    SwtCommonModule.prototype.customGrid;
    /** @type {?} */
    SwtCommonModule.prototype.reportbuttons;
    /** @type {?} */
    SwtCommonModule.prototype._loadingImage;
    /** @type {?} */
    SwtCommonModule.prototype._settingButton;
    /** @type {?} */
    SwtCommonModule.prototype._csv;
    /** @type {?} */
    SwtCommonModule.prototype._excel;
    /** @type {?} */
    SwtCommonModule.prototype._pdf;
    /** @type {?} */
    SwtCommonModule.prototype._fatcaReport;
    /** @type {?} */
    SwtCommonModule.prototype._helpIcon;
    /** @type {?} */
    SwtCommonModule.prototype.buttonsTemplate;
    /** @type {?} */
    SwtCommonModule.prototype.commonTemplate;
    /** @type {?} */
    SwtCommonModule.prototype.buttons;
    /** @type {?} */
    SwtCommonModule.prototype.actionPathReport;
    /** @type {?} */
    SwtCommonModule.prototype.addParamReport;
    /** @type {?} */
    SwtCommonModule.prototype.swtKVParams;
    /** @type {?} */
    SwtCommonModule.prototype.baseURL;
    /** @type {?} */
    SwtCommonModule.prototype.actionMethod;
    /** @type {?} */
    SwtCommonModule.prototype.actionPath;
    /** @type {?} */
    SwtCommonModule.prototype.requestParams;
    /** @type {?} */
    SwtCommonModule.prototype.urlReportSetting;
    /** @type {?} */
    SwtCommonModule.prototype.gridReport;
    /** @type {?} */
    SwtCommonModule.prototype.menuAccess;
    /** @type {?} */
    SwtCommonModule.prototype.programId;
    /** @type {?} */
    SwtCommonModule.prototype.undockWindow;
    /** @type {?} */
    SwtCommonModule.prototype.initXValue;
    /** @type {?} */
    SwtCommonModule.prototype.initYValue;
    /** @type {?} */
    SwtCommonModule.prototype.componentId;
    /** @type {?} */
    SwtCommonModule.prototype.reportGroupId;
    /** @type {?} */
    SwtCommonModule.prototype.newVBox;
    /** @type {?} */
    SwtCommonModule.prototype.SwtAlert;
    /** @type {?} */
    SwtCommonModule.prototype.jsonReader;
    /** @type {?} */
    SwtCommonModule.prototype.lastRecievedJSON;
    /** @type {?} */
    SwtCommonModule.prototype.prevRecievedJSON;
    /** @type {?} */
    SwtCommonModule.prototype.addButton;
    /** @type {?} */
    SwtCommonModule.prototype.changeButton;
    /** @type {?} */
    SwtCommonModule.prototype.viewButton;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateButton;
    /** @type {?} */
    SwtCommonModule.prototype.deleteButton;
    /** @type {?} */
    SwtCommonModule.prototype.searchButton;
    /** @type {?} */
    SwtCommonModule.prototype.cancelButton;
    /** @type {?} */
    SwtCommonModule.prototype.saveButton;
    /** @type {?} */
    SwtCommonModule.prototype.printIcon;
    /** @type {?} */
    SwtCommonModule.prototype.closeButton;
    /** @type {?} */
    SwtCommonModule.prototype.deleteMethod;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateMethod;
    /** @type {?} */
    SwtCommonModule.prototype.popupClosedEvent;
    /** @type {?} */
    SwtCommonModule.prototype.kvParamsMethod;
    /** @type {?} */
    SwtCommonModule.prototype.super;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathAdd;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathChange;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathView;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathDelete;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathDuplicate;
    /** @type {?} */
    SwtCommonModule.prototype.urlDetailsPathSave;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleAdd;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleChange;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleView;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleDelete;
    /** @type {?} */
    SwtCommonModule.prototype.childPanelTitleDuplicate;
    /** @type {?} */
    SwtCommonModule.prototype.addComponent;
    /** @type {?} */
    SwtCommonModule.prototype.changeComponent;
    /** @type {?} */
    SwtCommonModule.prototype.viewComponent;
    /** @type {?} */
    SwtCommonModule.prototype.deleteComponent;
    /** @type {?} */
    SwtCommonModule.prototype.duplicateComponent;
    /** @type {?} */
    SwtCommonModule.prototype.reportSettingComponent;
    /** @type {?} */
    SwtCommonModule.prototype.saveComponent;
    /** @type {?} */
    SwtCommonModule.prototype.vBoxCustomGridPaddingTop;
    /** @type {?} */
    SwtCommonModule.prototype.lockedColumnCount;
    /** @type {?} */
    SwtCommonModule.prototype.uniqueColumn;
    /** @type {?} */
    SwtCommonModule.prototype.swtCommonGrid;
    /** @type {?} */
    SwtCommonModule.prototype.eventString;
    /** @type {?} */
    SwtCommonModule.prototype.screenName;
    /** @type {?} */
    SwtCommonModule.prototype.versionNumber;
    /** @type {?} */
    SwtCommonModule.prototype.availableScreen;
    /** @type {?} */
    SwtCommonModule.prototype.objectType;
    /** @type {?} */
    SwtCommonModule.prototype.recordId;
    /** @type {?} */
    SwtCommonModule.prototype.partyType;
    /** @type {?} */
    SwtCommonModule.prototype.closeURL;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.mLoader;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.childPanel;
    /**
     * @type {?}
     * @protected
     */
    SwtCommonModule.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._inputData;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype.showJSON;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype.popUpScreen;
    /** @type {?} */
    SwtCommonModule.prototype.context;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._element;
    /**
     * @type {?}
     * @private
     */
    SwtCommonModule.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,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