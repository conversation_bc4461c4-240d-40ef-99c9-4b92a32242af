/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
/** @type {?} */
var select2 = require('select2');
//@dynamic
var 
//@dynamic
LinkEditor = /** @class */ (function () {
    function LinkEditor(args) {
        this.args = args;
        this.commonGrid = this.args.column.params.grid;
        this.logger = null;
        this.logger = new Logger('LinkEditor', null);
        this.init();
    }
    /**
     * @return {?}
     */
    LinkEditor.prototype.init = /**
     * @return {?}
     */
    function () {
        try {
            this.logger.debug('method [init] - START/END');
            this.loadValue(this.args.item);
            this.$input = $("<a style=\"color:#03A9F4!important; text-decoration: underline; cursor: pointer;\" >\n                            <p style=\"padding: 0 5px 0 5px;\"> " + this.defaultValue + "</p>\n                     </a> \n      ");
            this.$input.appendTo(this.args.container);
            this.commonGrid.CellLinkClick();
            // - remove Highlighting .
            /** @type {?} */
            var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
            if (selectedCells.length > 0) {
                for (var index = 0; index < selectedCells.length; index++) {
                    /** @type {?} */
                    var item = selectedCells[index];
                    $(item).removeClass('selected');
                }
            }
            //- add Highlighting 
            /** @type {?} */
            var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
            if (activeRow && activeRow.children().length > 0) {
                for (var index = 0; index < activeRow.children().length; index++) {
                    /** @type {?} */
                    var item = activeRow.children()[index];
                    $(item).addClass('selected');
                }
            }
        }
        catch (error) {
            console.log('method [init] - Error:', error);
        }
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.focus = /**
     * @return {?}
     */
    function () {
        this.logger.debug('method [focus] - START/END');
        this.$input.focus();
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.getValue = /**
     * @return {?}
     */
    function () {
        return this.$input.val();
    };
    /**
     * @param {?} val
     * @return {?}
     */
    LinkEditor.prototype.setValue = /**
     * @param {?} val
     * @return {?}
     */
    function (val) {
        this.logger.debug('method [setValue] - START/END');
        //this.$input.val(val);
    };
    /**
     * @param {?} item
     * @return {?}
     */
    LinkEditor.prototype.loadValue = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('method [loadValue] - START/END', this.args.column.field);
        this.defaultValue = item[this.args.column.field] || '';
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.save = /**
     * @return {?}
     */
    function () {
        this.logger.debug('method [save] - START/END');
        this.args.commitChanges();
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.serializeValue = /**
     * @return {?}
     */
    function () {
        return this.$input.val();
    };
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    LinkEditor.prototype.applyValue = /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        // item[this.args.column.field] = state;
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.isValueChanged = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var isChanged = (!(this.$input.val() === '' && this.defaultValue == null)) && (this.$input.val() !== this.defaultValue);
        this.logger.debug('method [isValueChanged] , returned value: "' + isChanged + '" - START/END');
        if ((isChanged && this.args && this.args.column.params && this.args.column.params.owner)) {
            this.args.column.params.owner.spyChanges({ field: this.args.column.field });
        }
        return isChanged;
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.validate = /**
     * @return {?}
     */
    function () {
        this.logger.debug('method [validate] - START/END');
        return {
            valid: true,
            msg: null
        };
    };
    /**
     * @return {?}
     */
    LinkEditor.prototype.destroy = /**
     * @return {?}
     */
    function () {
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.$input.remove();
    };
    return LinkEditor;
}());
//@dynamic
export { LinkEditor };
if (false) {
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.commonGrid;
    /** @type {?} */
    LinkEditor.prototype.$input;
    /** @type {?} */
    LinkEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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