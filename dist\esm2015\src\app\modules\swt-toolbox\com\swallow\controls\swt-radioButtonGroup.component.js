/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ContentChildren, ElementRef, Input } from "@angular/core";
import { Logger } from "../logging/logger.service";
import { SwtRadioItem } from "./swt-radioItem.component";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { HttpClient } from "@angular/common/http";
/** @type {?} */
const $ = require('jquery');
export class SwtRadioButtonGroup extends Container {
    /**
     * Constructor
     * @param {?} httpClient
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(httpClient, elem, commonService) {
        super(elem, commonService);
        this.httpClient = httpClient;
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        this.logger = null;
        this.originalRadioItem = null;
        this.radioItemsArray = [];
        this._selectedRadioId = null;
        this.logger = new Logger('SwtRadioButtonGroup', httpClient, 6);
        this.logger.info("[ SwtRadioButtonGroup ] construction - START/END");
    }
    //---ContentChildren-----------------------------------------------------------------------------------------------
    /**
     * @param {?} items
     * @return {?}
     */
    set radioItems(items) {
        if (items._results.length > 0)
            this.radioItemsArray = items._results;
        /** @type {?} */
        var __this = this;
        this.radioItemsArray.forEach((/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            item.parentGroup = __this;
            if (!item.value) {
                item.value = item.id;
            }
            if (item.selected) {
                __this.selectedValue = item.value;
            }
        }));
    }
    //---Align---------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set align(value) {
        this._align = value;
        if (this._align == 'horizontal') {
            $($(this.elem.nativeElement).children()[0]).addClass('verticalHorizontalAlign');
        }
        else {
            $($(this.elem.nativeElement).children()[0]).removeClass('verticalHorizontalAlign');
        }
    }
    /**
     * @return {?}
     */
    get align() {
        return this._align;
    }
    //---selectedValue-------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedValue(value) {
        try {
            /** @type {?} */
            var selectedItem = null;
            if (this.selectedRadioId != null && this._selectedValue == value) {
                selectedItem = this.radioItemsArray.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.id == this.selectedRadioId));
            }
            else {
                selectedItem = this.radioItemsArray.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => (x.value == value)));
            }
            if (selectedItem && !selectedItem.selected)
                selectedItem.selected = true;
            this._selectedValue = value;
            if (this.firstCall) {
                this.originalValue = selectedItem.value;
                this.originalRadioItem = selectedItem.id;
                this.firstCall = false;
            }
            if ((this.originalValue != this.selectedValue) || (this.originalRadioItem != selectedItem.id)) {
                this._spyChanges(value);
            }
            else {
                this.spyNoChanges(value);
            }
        }
        catch (error) {
            this.logger.error('set selectedValue method ', error);
        }
    }
    /**
     * @return {?}
     */
    get selectedValue() {
        return this._selectedValue;
    }
    //----selectedRadioId----------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedRadioId(value) {
        this._selectedRadioId = value;
    }
    /**
     * @return {?}
     */
    get selectedRadioId() {
        return this._selectedRadioId;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtRadioButtonGroup.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRadioButtonGroup');
    }
    /**
     * Change
     * @param {?} event
     * @return {?}
     */
    Change(event) {
        this.logger.info("[ SwtRadioButtonGroup ] Change - START/END - ", event.target.id);
        this.selectedRadioId = event.target.id;
        this.selectedValue = ((/** @type {?} */ (event.target.value))) == "" ? null : event.target.value;
        if (this.change != undefined) {
            this.change(event);
        }
    }
    /**
     * spyChanges
     * \@Added by Khalil.B
     * \@Reviewed By Rihab.Jaballah \@23/04/2019.
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        this.onSpyChange.emit({ "target": this, "value": event });
    }
    /**
     * @param {?} event
     * @return {?}
     */
    spyNoChanges(event) {
        this.onSpyNoChange.emit({ "target": this, "value": event });
    }
    /**
     * resetOriginalValue
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue = this._selectedValue;
        this.originalRadioItem = this._selectedRadioId;
        this.spyNoChanges(this._selectedValue);
    }
    /**
     * Destroy all event listeners
     * @return {?}
     */
    ngOnDestroy() {
        try {
            //console.log('[SwtRadioButtonGroup] ngOnDestroy ');
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this.logger;
            delete this.firstCall;
            delete this.originalRadioItem;
            delete this.radioItemsArray;
            delete this._align;
            delete this._selectedValue;
            delete this._selectedRadioId;
            delete this.change;
        }
        catch (error) {
            console.error('method [ngOnDestroy] - error :', error);
        }
    }
}
SwtRadioButtonGroup.decorators = [
    { type: Component, args: [{
                selector: 'SwtRadioButtonGroup',
                template: `
    <div  
         tabindex="-1"  
         popper="{{this.toolTipPreviousValue}}"
         [popperTrigger]="'hover'"
         [popperDisabled]="toolTipPreviousValue === null ? true : false"
         [popperPlacement]="'bottom'"
         [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}"
         class="inline-field SwtRadioButtonGroup"   
         (change)="Change($event)" >
          <ng-content  ></ng-content>
          <ng-container #_container></ng-container>
    </div>    
  `,
                styles: [` 
            :host {
               display: block;
               outline: none;
             }
             .SwtRadioButtonGroup{
                  outline: none;
             }
             .verticalHorizontalAlign{
                 display : flex;
                
             }

            .SwtRadioButtonGroup.disabled-container{
                opacity: unset!important;
            }
     `]
            }] }
];
/** @nocollapse */
SwtRadioButtonGroup.ctorParameters = () => [
    { type: HttpClient },
    { type: ElementRef },
    { type: CommonService }
];
SwtRadioButtonGroup.propDecorators = {
    toolTip: [{ type: Input, args: ['toolTip',] }],
    radioItems: [{ type: ContentChildren, args: [SwtRadioItem,] }],
    align: [{ type: Input, args: ['align',] }],
    selectedValue: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.originalRadioItem;
    /** @type {?} */
    SwtRadioButtonGroup.prototype.radioItemsArray;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._align;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._selectedRadioId;
    /** @type {?} */
    SwtRadioButtonGroup.prototype.toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.httpClient;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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