/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { CommonService } from "../utils/common.service";
import { SwtAlert } from "../utils/swt-alert.service";
export class SwtMultiselectCombobox {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        this.elem = elem;
        this.commonService = commonService;
        this.ITEM_SELECT = new EventEmitter();
        this.ITEM_DESELECT = new EventEmitter();
        this.SELECT_ALL = new EventEmitter();
        this.DESELECT_ALL = new EventEmitter();
        this.placeholder = '';
        this.isDropdownDisabled = false;
        this.dataProvider = [];
        this.defaultSelectedItems = [];
        this.selectedItems = [];
        this.dropdownSettings = {};
        this.selects = [];
        this.tooltipValue = [];
        this._visibility = true;
        this._shiftUp = 0;
        this._showAbove = false;
        // Input to store width.
        this.width = "200";
        // Input to store height.
        this.height = "70";
        // Input to store dropDown btn height.
        this.dropHeight = "30";
        // initialize logger.
        this.logger = new Logger('SwtMultiselectCombobox', commonService.httpclient, 6);
        // initialize setter.
        this.SwtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        this.logger.info("[ngOnDestroy] Method not implemented.");
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        this.logger.info("[ngAfterViewInit] Method not implemented.");
        if (this.shiftUp != 0) {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "-" + this.shiftUp + "px");
        }
        else {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "0px");
        }
        if (this.showAbove) {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("bottom", "100%");
        }
        else {
            $(this.elem.nativeElement.children[0].children[0].children[1]).css("margin-top", "0px");
        }
        // set multiselection comboBox width
        $(this.elem.nativeElement.children[0]).width(this.width);
        // set multiselection comboBox dropdownlist  height
        $(this.elem.nativeElement.children[0].children[0].children[1].children[1]).css("max-height", this.height + "px");
        // set dropdown-btn height
        $(this.elem.nativeElement.children[0].children[0].children[0].children[0]).css("height", this.dropHeight + "px");
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.selects = this.defaultSelectedItems;
        this.dropdownSettings = {
            singleSelection: false,
            idField: 'value',
            textField: 'content',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: this.itemLimit,
            maxHeight: 60,
            allowSearchFilter: true,
        };
    }
    // enabled getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set shiftUp(value) {
        this._shiftUp = value;
    }
    /**
     * @return {?}
     */
    get shiftUp() {
        return this._shiftUp;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set showAbove(value) {
        this._showAbove = value;
    }
    /**
     * @return {?}
     */
    get showAbove() {
        return this._showAbove;
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visibility;
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) !== 'string') {
            this._visibility = value;
            value ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
        }
        else {
            if (value === 'true') {
                this.visible = true;
                $(this.elem.nativeElement).show();
            }
            else {
                this._visibility = false;
                $(this.elem.nativeElement).hide();
            }
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTip(value) {
        this.toolTipObject = $(this.elem.nativeElement);
        /** @type {?} */
        let _this = this;
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.toolTipObject.attr("title", value);
            this.toolTipObject.tooltip({
                position: { my: "left+5 center", at: "right-250 bottom-10" },
                show: { duration: 600, delay: 500 },
                open: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    $(_this.toolTipObject).removeAttr('title');
                })
            });
        }), 0);
    }
    /**
     * @return {?}
     */
    get toolTip() {
        return this.toolTip;
    }
    /**
     * @param {?} item
     * @return {?}
     */
    onItemSelect(item) {
        //this.selectedItem = item["item_text"];
        this.selects.push(item);
        this.tooltipValue.push(item.content);
        this.logger.info("[onItemSelect]---selected items", this.selects);
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        const target = {
            tooltip: this.tooltipValue.toString()
        };
        this.ITEM_SELECT.emit(target);
    }
    /**
     * @param {?} items
     * @return {?}
     */
    onSelectAll(items) {
        this.tooltipValue = [];
        this.logger.info("[onSelectAll]---all items", items);
        for (let i = 0; i < items.length; i++) {
            if (i % 5 == 0) {
                this.tooltipValue.push("$#$");
            }
            this.tooltipValue.push(items[i].content);
        }
        this.toolTip = (this.tooltipValue.toString()).split("$#$,").join("\n");
        this.selects = items;
        /** @type {?} */
        const target = {
            tooltip: this.tooltipValue.toString()
        };
        this.SELECT_ALL.emit(target);
    }
    /**
     * @param {?} item
     * @return {?}
     */
    onItemDeSelect(item) {
        this.logger.info("[onItemDeSelect]---item desselected", item);
        /** @type {?} */
        const index = this.selects.findIndex((/**
         * @param {?} i
         * @return {?}
         */
        i => i.value === item["value"]));
        this.logger.info("[onItemDeSelect]---item desselected idex", index);
        if (index > -1) {
            this.selects.splice(index, 1);
            this.tooltipValue.splice(index, 1);
        }
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        const target = {
            tooltip: this.tooltipValue.toString()
        };
        this.ITEM_DESELECT.emit(target);
        this.logger.info("[onItemDeSelect]---new data", this.selects);
    }
    /**
     * @param {?} items
     * @return {?}
     */
    onDeSelectAll(items) {
        this.selects = [];
        this.tooltipValue = [];
        this.toolTip = this.tooltipValue.toString();
        /** @type {?} */
        const target = {
            tooltip: ""
        };
        this.DESELECT_ALL.emit(target);
    }
}
SwtMultiselectCombobox.decorators = [
    { type: Component, args: [{
                selector: 'SwtMultiselectCombobox',
                template: `<ng-multiselect-dropdown-angular7 class="multiselect"
    [placeholder]="placeholder"
    [disabled] ="isDropdownDisabled"
    [(ngModel)]="defaultSelectedItems"
    [data]="dataProvider"
    [settings]="dropdownSettings"
    (onSelect)="onItemSelect($event)"
    (onSelectAll)="onSelectAll($event)"
    (onDeSelect) = "onItemDeSelect($event)"
    (onDeSelectAll) = "onDeSelectAll($event)"
  >
  </ng-multiselect-dropdown-angular7>
  `,
                encapsulation: ViewEncapsulation.None,
                styles: [`

       .dropdown-btn {
         display: inline-block;
         background: #ffffff;
         padding-top: 2px !important;
         padding-right: 0px !important;
         padding-bottom: 1px !important;
         padding-left: 4px !important;
         border-top: 1px solid #7F9DB9 !important;
         border-left: 1px solid #7F9DB9 !important;
         border-right: 1px solid #7F9DB9 !important;
         border-bottom: 1px solid #7F9DB9 !important;
         color:#808080; /*to over write fieldset color*/
         background-image: linear-gradient(to left, #ccddea 1px, #A7C6DE 20px,#ffffff 2px, #ffffff) !important;
       }

       .multiselect-dropdown .dropdown-btn .dropdown-down {
            display: inline-block;
            margin-top: 6px;
            border-top: 5px solid #555 !important;
            border-left: 5px solid transparent !important;
            border-right: 5px solid transparent !important;
          }

        .multiselect-dropdown .dropdown-btn .dropdown-up {
          display: inline-block;
          margin-top: 5px;
          border-bottom: 5px solid #555 !important;
          border-left: 5px solid transparent !important;
          border-right: 5px solid transparent !important;
        }
        .multiselect {
         display: inline-block;
         width: 200px;
         height: 70px;
         padding: 6px 12px;
         margin-bottom: 0;
         font-weight: 400;
         line-height: 1.52857143;
         text-align: left;
         vertical-align: middle;
         cursor: pointer;
         background-image: none;
         border-radius: 10px;
        }
        .dropdown-list{
          position: absolute;
          padding-top: 1px !important;
          width: 100%;
          z-index: 9999;
          border: 1px solid #ccc;
          border-radius: 3px;
          background: #fff;
          box-shadow: 0 1px 5px #959595; 
        }
        .multiselect-item-checkbox{
          padding-top: 2px !important;
          padding-right: 3px !important;
          padding-bottom: 2px !important;
          padding-left: 3px !important;
        }
        .filter-textbox{
          padding-top: 1px !important;
          padding-right: 1px !important;
          padding-bottom: 1px !important;
          padding-left: 1px !important;
        }
        .ng-pristine.ng-valid.ng-touched{
          padding-top: 0px !important;
          padding-right: 0px !important;
          padding-bottom: 0px !important;
          padding-left: 30px !important;
          
        }

        div.tooltip-inner{
          background-color: #F1F1DE !important;
          width: auto !important;
          max-width:1200px !important;
          min-width: 10px !important;
          margin-top: -10px !important;
          -moz-transition-delay: 0s !important;
          transition-delay: 0s !important;
          box-shadow: 0px 5px 8px #7A8D99  !important;
          padding: 3px 5px 3px 5px !important;
          border-radius: 5px;
          transition: opacity  5s linear 0s !important;
          transition-delay: 0s !important;
          color: #000;
          font-size: 10px;
        }

        div.tooltip-arrow{
          border-left: 0 solid #000000 !important;
          border-right: 0 solid #000000 !important;
          border-bottom: 0 solid #000000 !important;
          border-top: 0 solid #000000 !important;
        }
        .selected-item{
          margin-right: 4px !important;
          margin-bottom: 4px !important;
        }
        `]
            }] }
];
/** @nocollapse */
SwtMultiselectCombobox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtMultiselectCombobox.propDecorators = {
    ITEM_SELECT: [{ type: Output, args: ['ITEM_SELECT',] }],
    ITEM_DESELECT: [{ type: Output, args: ['ITEM_DESELECT',] }],
    SELECT_ALL: [{ type: Output, args: ['SELECT_ALL',] }],
    DESELECT_ALL: [{ type: Output, args: ['DESELECT_ALL',] }],
    width: [{ type: Input, args: ["width",] }],
    height: [{ type: Input, args: ["height",] }],
    dropHeight: [{ type: Input, args: ["dropHeight",] }],
    shiftUp: [{ type: Input }],
    showAbove: [{ type: Input }],
    visible: [{ type: Input, args: ['visible',] }],
    toolTip: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtMultiselectCombobox.prototype.ITEM_SELECT;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.ITEM_DESELECT;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.SELECT_ALL;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.DESELECT_ALL;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.placeholder;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.isDropdownDisabled;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dataProvider;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.defaultSelectedItems;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.selectedItems;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dropdownSettings;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.itemLimit;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.selectedItem;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.selects;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.tooltipValue;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._shiftUp;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype._showAbove;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.width;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.height;
    /** @type {?} */
    SwtMultiselectCombobox.prototype.dropHeight;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtMultiselectCombobox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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