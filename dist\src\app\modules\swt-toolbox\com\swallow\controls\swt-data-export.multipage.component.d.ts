import { OnInit, AfterViewInit } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { SwtDataExport } from './swt-data-export.component';
import { SwtPagesToExport } from './PagesToExport';
export declare class DataExportMultiPage extends SwtDataExport implements OnInit, AfterViewInit {
    private _reportType;
    private _currentPage;
    private _totalPages;
    private _maxPages;
    private reportProgress;
    pagesToExport: SwtPagesToExport;
    private _exportCancelFunction;
    private _exportFunction;
    private _closePopupWindow;
    ngOnInit(): void;
    /**
    *
    */
    exportCancelFunction: Function;
    /**
    *
    */
    exportFunction: Function;
    /**
    *
    */
    closePopupWindow: Function;
    onDataExportClick(event: any): void;
    createPopup(): void;
    /**
     * This function closes the option popup
     */
    private closePopup;
    showReportProgress(): void;
    /**
     * This function is used to generate report on click of
     * OK button on confim dialog
     *
     * @param event: CloseEvent
     */
    private onConfimClose;
    private generateReport;
    closeCancelPopup(): void;
    /**
         * This function is used to create option popup window
         */
    progresswinPopup(): void;
    /**
     *This function is called whenever click on the cancel button in Progress bar window
     *@param event: MouseEvent
     */
    onProgressCancelClick(event: any): void;
    /**
     * Setter function for currentPage
     */
    /**
    * Getter function for currentPage
    */
    currentPage: number;
    /**
     * Setter function for totalPages
     */
    /**
    * Getter function for totalPages
    */
    totalPages: number;
    /**
     * Setter function for maxPages
     */
    /**
    * Getter function for maxPages
    */
    maxPages: number;
}
