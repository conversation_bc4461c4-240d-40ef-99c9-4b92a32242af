import { OnInit, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { CheckBoxLegendItem } from './CheckBoxLegendItem';
import { Series } from './Series';
import { VBox } from '../../../../controls/swt-vbox.component';
export declare class CheckBoxLegend extends Container implements OnInit {
    private elem;
    private commonService;
    protected legendContainer: VBox;
    private _seriesList;
    private _dataProvider;
    ngOnInit(): void;
    dataProvider: any[];
    constructor(elem: ElementRef, commonService: CommonService);
    seriesList: any;
    refreshLegends(seriesList: any): void;
    getLegendItem(series: Series): CheckBoxLegendItem;
    getUncheckedLegends(): any[];
    setCheckBoxLegendSelected(series: Series, isSelected: boolean): void;
    /**
     * To highlight the correct legend item
     * */
    highlighTrueFalse(event: any): void;
}
