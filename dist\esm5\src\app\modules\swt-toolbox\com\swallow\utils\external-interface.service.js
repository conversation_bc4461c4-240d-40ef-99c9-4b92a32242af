/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Injectable } from '@angular/core';
/** @type {?} */
var _ = parent;
//@dynamic
var ExternalInterface = /** @class */ (function () {
    function ExternalInterface() {
    }
    /**
     * @param {?} functionName
     * @param {...?} params
     * @return {?}
     */
    ExternalInterface.call = /**
     * @param {?} functionName
     * @param {...?} params
     * @return {?}
     */
    function (functionName) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        if (functionName.toUpperCase() == "EVAL") {
            return _[params[0]];
        }
        else {
            return _[functionName].apply(_, tslib_1.__spread(params));
        }
    };
    ExternalInterface.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    ExternalInterface.ctorParameters = function () { return []; };
    return ExternalInterface;
}());
export { ExternalInterface };
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXh0ZXJuYWwtaW50ZXJmYWNlLnNlcnZpY2UuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvdXRpbHMvZXh0ZXJuYWwtaW50ZXJmYWNlLnNlcnZpY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxPQUFPLEVBQUUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDOztJQUNyQyxDQUFDLEdBQVEsTUFBTTs7QUFFckI7SUFHRTtJQUFnQixDQUFDOzs7Ozs7SUFFSCxzQkFBSTs7Ozs7SUFBbEIsVUFBbUIsWUFBb0I7UUFBRSxnQkFBZ0I7YUFBaEIsVUFBZ0IsRUFBaEIscUJBQWdCLEVBQWhCLElBQWdCO1lBQWhCLCtCQUFnQjs7UUFDckQsSUFBRyxZQUFZLENBQUMsV0FBVyxFQUFFLElBQUksTUFBTSxFQUFFO1lBQ3JDLE9BQU8sQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1NBQ3ZCO2FBQU07WUFDSixPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsT0FBZixDQUFDLG1CQUFrQixNQUFNLEdBQUU7U0FDcEM7SUFDTCxDQUFDOztnQkFYRixVQUFVOzs7O0lBWVgsd0JBQUM7Q0FBQSxBQVpELElBWUM7U0FYWSxpQkFBaUIiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmNvbnN0IF86IGFueSA9IHBhcmVudDtcclxuLy9AZHluYW1pY1xyXG5ASW5qZWN0YWJsZSgpXHJcbmV4cG9ydCBjbGFzcyBFeHRlcm5hbEludGVyZmFjZSB7XHJcblxyXG4gIGNvbnN0cnVjdG9yKCkgeyB9XHJcblxyXG4gIHB1YmxpYyBzdGF0aWMgY2FsbChmdW5jdGlvbk5hbWU6IHN0cmluZywgLi4ucGFyYW1zOiBhbnlbXSk6IGFueSB7XHJcbiAgICAgIGlmKGZ1bmN0aW9uTmFtZS50b1VwcGVyQ2FzZSgpID09IFwiRVZBTFwiKSB7XHJcbiAgICAgICAgICByZXR1cm4gX1twYXJhbXNbMF1dO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgICByZXR1cm4gX1tmdW5jdGlvbk5hbWVdKC4uLnBhcmFtcyk7XHJcbiAgICAgIH1cclxuICB9XHJcbn1cclxuIl19