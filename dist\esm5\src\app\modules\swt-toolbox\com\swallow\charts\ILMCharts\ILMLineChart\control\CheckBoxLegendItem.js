/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { SwtCheckBox } from '../../../../controls/swt-checkbox.component';
import { SeriesHighlightEvent, LegendItemChangedEvent } from "../../../../events/swt-events.module";
import { HBox } from '../../../../controls/swt-hbox.component';
var CheckBoxLegendItem = /** @class */ (function (_super) {
    tslib_1.__extends(CheckBoxLegendItem, _super);
    function CheckBoxLegendItem(elem, commonService, cd) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.cd = cd;
        // LegendItemChangedEvent
        _this.styleClassMap = {
            'DOTTED_SEGMENT_YELLOW': 'bg-DOTTED_SEGMENT_YELLOW',
            'CONT_AREA_ANTIQUE_WHITE': 'bg-CONT_AREA_ANTIQUE_WHITE',
            'CONT_AREA_APRICOT_PEACH': 'bg-CONT_AREA_APRICOT_PEACH',
            'CONT_AREA_BLACK': 'bg-CONT_AREA_BLACK',
            'CONT_AREA_BLIZZARD_BLUE': 'bg-CONT_AREA_BLIZZARD_BLUE',
            'CONT_AREA_BLUE': 'bg-CONT_AREA_BLUE',
            'CONT_AREA_COTTON_CANDY': 'bg-CONT_AREA_COTTON_CANDY',
            'CONT_AREA_DARK_SALMON': 'bg-CONT_AREA_DARK_SALMON',
            'CONT_AREA_GREEN': 'bg-CONT_AREA_GREEN',
            'CONT_AREA_GREY': 'bg-CONT_AREA_GREY',
            'CONT_AREA_INDIAN_RED': 'bg-CONT_AREA_INDIAN_RED',
            'CONT_AREA_LIGHT_BLUE': 'bg-CONT_AREA_LIGHT_BLUE',
            'CONT_AREA_LIGHT_CYAN': 'bg-CONT_AREA_LIGHT_CYAN',
            'CONT_AREA_LIGHT_GREEN': 'bg-CONT_AREA_LIGHT_GREEN',
            'CONT_AREA_LIGHT_GREY': 'bg-CONT_AREA_LIGHT_GREY',
            'CONT_AREA_LIME': 'bg-CONT_AREA_LIME',
            'CONT_AREA_MAGENTA': 'bg-CONT_AREA_MAGENTA',
            'CONT_AREA_NAVAJO_WHITE': 'bg-CONT_AREA_NAVAJO_WHITE',
            'CONT_AREA_ORANGE': 'bg-CONT_AREA_ORANGE',
            'CONT_AREA_PEACH_PUFF': 'bg-CONT_AREA_PEACH_PUFF',
            'CONT_AREA_PINK': 'bg-CONT_AREA_PINK',
            'CONT_AREA_PURPLE': 'bg-CONT_AREA_PURPLE',
            'CONT_AREA_RED': 'bg-CONT_AREA_RED',
            'CONT_AREA_ROSE_FOG': 'bg-CONT_AREA_ROSE_FOG',
            'CONT_AREA_STEEL_BLUE': 'bg-CONT_AREA_STEEL_BLUE',
            'CONT_AREA_VIOLET': 'bg-CONT_AREA_VIOLET',
            'CONT_AREA_YELLOW': 'bg-CONT_AREA_YELLOW',
            'CONT_SEGMENT_BLACK': 'bg-CONT_SEGMENT_BLACK',
            'CONT_SEGMENT_BLUE': 'bg-CONT_SEGMENT_BLUE',
            'CONT_SEGMENT_BOLD_RED': 'bg-CONT_SEGMENT_BOLD_RED',
            'CONT_SEGMENT_GREEN': 'bg-CONT_SEGMENT_GREEN',
            'CONT_SEGMENT_MAGENTA': 'bg-CONT_SEGMENT_MAGENTA',
            'CONT_SEGMENT_ORANGE': 'bg-CONT_SEGMENT_ORANGE',
            'CONT_SEGMENT_PURPLE': 'bg-CONT_SEGMENT_PURPLE',
            'CONT_SEGMENT_RED': 'bg-CONT_SEGMENT_RED',
            'CONT_SEGMENT_YELLOW': 'bg-CONT_SEGMENT_YELLOW',
            'DASHED_AQUA_AREA': 'bg-DASHED_AQUA_AREA',
            'DASHED_AREA_PERANO': 'bg-DASHED_AREA_PERANO',
            'DASHED_BLUE_AREA': 'bg-DASHED_BLUE_AREA',
            'DASHED_CORAL_AREA': 'bg-DASHED_CORAL_AREA',
            'DASHED_DEEP_PINK_AREA': 'bg-DASHED_DEEP_PINK_AREA',
            'DASHED_GOLDEN_ROD_AREA': 'bg-DASHED_GOLDEN_ROD_AREA',
            'DASHED_GREEN_AREA': 'bg-DASHED_GREEN_AREA',
            'DASHED_SEGMENT_BLACK': 'bg-DASHED_SEGMENT_BLACK',
            'DASHED_SEGMENT_BLUE': 'bg-DASHED_SEGMENT_BLUE',
            'DASHED_SEGMENT_GREEN': 'bg-DASHED_SEGMENT_GREEN',
            'DASHED_SEGMENT_MAGENTA': 'bg-DASHED_SEGMENT_MAGENTA',
            'DASHED_SEGMENT_ORANGE': 'bg-DASHED_SEGMENT_ORANGE',
            'DASHED_SEGMENT_PURPLE': 'bg-DASHED_SEGMENT_PURPLE',
            'DASHED_SEGMENT_RED': 'bg-DASHED_SEGMENT_RED',
            'DASHED_SEGMENT_YELLOW': 'bg-DASHED_SEGMENT_YELLOW',
            'DOTTED_GREEN_YELLOW_AREA': 'bg-DOTTED_GREEN_YELLOW_AREA',
            'DOTTED_INDIAN_RED_AREA': 'bg-DOTTED_INDIAN_RED_AREA',
            'DOTTED_MAGENTA_AREA': 'bg-DOTTED_MAGENTA_AREA',
            'DOTTED_SEGMENT_BLACK': 'bg-DOTTED_SEGMENT_BLACK',
            'DOTTED_SEGMENT_BLUE': 'bg-DOTTED_SEGMENT_BLUE',
            'DOTTED_SEGMENT_GREEN': 'bg-DOTTED_SEGMENT_GREEN',
            'DOTTED_SEGMENT_MAGENTA': 'bg-DOTTED_SEGMENT_MAGENTA',
            'DOTTED_SEGMENT_ORANGE': 'bg-DOTTED_SEGMENT_ORANGE',
            'DOTTED_SEGMENT_PURPLE': 'bg-DOTTED_SEGMENT_PURPLE',
            'DOTTED_SEGMENT_RED': 'bg-DOTTED_SEGMENT_RED',
        };
        _this.liveValue = '';
        _this._yField = '';
        _this._selected = true;
        _this._highlight = false;
        _this._seriesStyle = '';
        _this.created = false;
        return _this;
    }
    /**
     * @return {?}
     */
    CheckBoxLegendItem.prototype.getLiveValue = /**
     * @return {?}
     */
    function () {
        return this.liveValue;
    };
    Object.defineProperty(CheckBoxLegendItem.prototype, "highlight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._highlight;
        },
        //BBCCDD
        set: 
        //BBCCDD
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._highlight = value;
            if (this._highlight) {
                this.hboxContainer.backGroundColor = '#BBCCDD';
            }
            else {
                this.hboxContainer.backGroundColor = 'transparent';
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CheckBoxLegendItem.prototype, "seriesStyle", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesStyle;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var newStyle = this.styleClassMap[value];
            if (newStyle) {
                if (this.created) {
                    if (this._seriesStyle) {
                        /** @type {?} */
                        var prevStyle = this.styleClassMap[this._seriesStyle];
                        this.square.nativeElement.classList.remove(prevStyle);
                    }
                    this.square.nativeElement.classList.add(newStyle);
                }
                this._seriesStyle = value;
            }
            // styleClassMap
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    CheckBoxLegendItem.prototype.checkboxChanged = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.selected = this.checkBox.selected;
        /** @type {?} */
        var dto = {};
        dto.selected = this.selected;
        dto.yField = this.yField;
        if ($(this.elem.nativeElement).closest('.legendsDivider')) {
            dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
        }
        // // Dispatch the event so that legends will be highlightened as well
        LegendItemChangedEvent.emit(dto);
    };
    /**
     * @return {?}
     */
    CheckBoxLegendItem.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.labelValue.nativeElement.textContent = this.liveValue;
        /** @type {?} */
        var newStyle = this.styleClassMap[this._seriesStyle];
        if (newStyle) {
            this.square.nativeElement.classList.add(newStyle);
        }
        // this.checkBox.selected = this.selected;
        this.created = true;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    };
    Object.defineProperty(CheckBoxLegendItem.prototype, "yField", {
        get: /**
         * @return {?}
         */
        function () {
            return this._yField;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._yField = value;
            if (!((/** @type {?} */ (this.cd))).destroyed) {
                this.cd.markForCheck();
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CheckBoxLegendItem.prototype, "selected", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selected;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selected = value;
            if (!((/** @type {?} */ (this.cd))).destroyed) {
                this.cd.markForCheck();
            }
            // this.checkBox.selected = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
 * On click on the legend item label highlight the label and the line chart
 * */
    /**
     * On click on the legend item label highlight the label and the line chart
     *
     * @param {?} event
     * @return {?}
     */
    CheckBoxLegendItem.prototype.legendItemClicked = /**
     * On click on the legend item label highlight the label and the line chart
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // Highlight the legend and the line chart when the user click on the Label legend
        /** @type {?} */
        var dto = {};
        this.highlight = !this.highlight;
        dto.highligh = this.highlight;
        dto.yField = this.yField;
        if ($(this.elem.nativeElement).closest('.legendsDivider')) {
            dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
        }
        // // Dispatch the event so that legends will be highlightened as well
        SeriesHighlightEvent.emit(dto);
    };
    CheckBoxLegendItem.decorators = [
        { type: Component, args: [{
                    selector: 'CheckBoxLegendItem',
                    template: "\n         <HBox paddingLeft=\"5\" #hboxContainer  width=\"100%\" height=\"26\"> \n         <SwtCheckBox  selected='{{selected}}' (change)=\"checkboxChanged($event)\" class='checkBoxObject'  #checkBox></SwtCheckBox>\n            <div    #square class='square' (click)='legendItemClicked($event)'></div>\n            <div class='labelValue' #labelValue (click)='legendItemClicked($event)' > {{ getLiveValue() }}</div>\n        </HBox>\n        ",
                    changeDetection: ChangeDetectionStrategy.OnPush,
                    styles: [" \n            .checkBoxObject{\n                margin-right : 0px !important;\n            }\n            .labelValue{\n                width :1px;\n                padding-top:2px;\n                padding-left:5px;\n                position:relative;\n                font-size:11px;\n                cursor: default;\n                white-space: nowrap;\n            }\n            .square{\n                margin-right:2px !important;\n                margin-top:3px;\n                cursor: default;\n            }\n            \n            .bg-DOTTED_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -10px;\n            }\n            .bg-CONT_AREA_ANTIQUE_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -10px;\n            }\n            .bg-CONT_AREA_APRICOT_PEACH {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -46px;\n            }\n            .bg-CONT_AREA_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -46px;\n            }\n            .bg-CONT_AREA_BLIZZARD_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -10px;\n            }\n            .bg-CONT_AREA_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -46px;\n            }\n            .bg-CONT_AREA_COTTON_CANDY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -82px;\n            }\n            .bg-CONT_AREA_DARK_SALMON {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -82px;\n            }\n            .bg-CONT_AREA_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -82px;\n            }\n            .bg-CONT_AREA_GREY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -118px;\n            }\n            .bg-CONT_AREA_INDIAN_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -118px;\n            }\n            .bg-CONT_AREA_LIGHT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -118px;\n            }\n            .bg-CONT_AREA_LIGHT_CYAN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -10px;\n            }\n            .bg-CONT_AREA_LIGHT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -46px;\n            }\n            .bg-CONT_AREA_LIGHT_GREY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -82px;\n            }\n            .bg-CONT_AREA_LIME {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -118px;\n            }\n            .bg-CONT_AREA_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -154px;\n            }\n            .bg-CONT_AREA_NAVAJO_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -154px;\n            }\n            .bg-CONT_AREA_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -154px;\n            }\n            .bg-CONT_AREA_PEACH_PUFF {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -154px;\n            }\n            .bg-CONT_AREA_PINK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -10px;\n            }\n            .bg-CONT_AREA_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -46px;\n            }\n            .bg-CONT_AREA_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -82px;\n            }\n            .bg-CONT_AREA_ROSE_FOG {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -118px;\n            }\n            .bg-CONT_AREA_STEEL_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -154px;\n            }\n            .bg-CONT_AREA_VIOLET {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -190px;\n            }\n            .bg-CONT_AREA_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -190px;\n            }\n            .bg-CONT_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -190px;\n            }\n            .bg-CONT_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -190px;\n            }\n            .bg-CONT_SEGMENT_BOLD_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -190px;\n            }\n            .bg-CONT_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -10px;\n            }\n            .bg-CONT_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -46px;\n            }\n            .bg-CONT_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -82px;\n            }\n            .bg-CONT_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -118px;\n            }\n            .bg-CONT_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -154px;\n            }\n            .bg-CONT_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -190px;\n            }\n            .bg-DASHED_AQUA_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -226px;\n            }\n            .bg-DASHED_AREA_PERANO {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -226px;\n            }\n            .bg-DASHED_BLUE_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -226px;\n            }\n            .bg-DASHED_CORAL_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -226px;\n            }\n            .bg-DASHED_DEEP_PINK_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -226px;\n            }\n            .bg-DASHED_GOLDEN_ROD_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -226px;\n            }\n            .bg-DASHED_GREEN_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -262px;\n            }\n            .bg-DASHED_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -262px;\n            }\n            .bg-DASHED_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -262px;\n            }\n            .bg-DASHED_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -262px;\n            }\n            .bg-DASHED_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -262px;\n            }\n            .bg-DASHED_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -262px;\n            }\n            .bg-DASHED_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -10px;\n            }\n            .bg-DASHED_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -46px;\n            }\n            .bg-DASHED_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -82px;\n            }\n            .bg-DOTTED_GREEN_YELLOW_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -118px;\n            }\n            .bg-DOTTED_INDIAN_RED_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -154px;\n            }\n            .bg-DOTTED_MAGENTA_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -190px;\n            }\n            .bg-DOTTED_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -226px;\n            }\n            .bg-DOTTED_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -262px;\n            }\n            .bg-DOTTED_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -298px;\n            }\n            .bg-DOTTED_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -298px;\n            }\n            .bg-DOTTED_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -298px;\n            }\n            .bg-DOTTED_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -298px;\n            }\n            .bg-DOTTED_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -298px;\n            }\n        "]
                }] }
    ];
    /** @nocollapse */
    CheckBoxLegendItem.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: ChangeDetectorRef }
    ]; };
    CheckBoxLegendItem.propDecorators = {
        hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
        square: [{ type: ViewChild, args: ['square',] }],
        checkBox: [{ type: ViewChild, args: ['checkBox',] }],
        labelValue: [{ type: ViewChild, args: ['labelValue',] }],
        liveValue: [{ type: Input, args: ['liveValue',] }],
        highlight: [{ type: Input, args: ['highlight',] }],
        yField: [{ type: Input, args: ['highlight',] }],
        selected: [{ type: Input, args: ['selected',] }]
    };
    return CheckBoxLegendItem;
}(Container));
export { CheckBoxLegendItem };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.square;
    /** @type {?} */
    CheckBoxLegendItem.prototype.checkBox;
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegendItem.prototype.labelValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.styleClassMap;
    /** @type {?} */
    CheckBoxLegendItem.prototype.liveValue;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._yField;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._highlight;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype._seriesStyle;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.created;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegendItem.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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