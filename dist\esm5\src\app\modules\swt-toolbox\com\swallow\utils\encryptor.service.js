/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { Md5 } from 'ts-md5/dist/md5';
import { ExternalInterface } from './external-interface.service';
/** @type {?} */
var aesjs = require('aes-js');
/** @type {?} */
var CryptoJS = require("crypto-js");
/** @type {?} */
var AES = require("crypto-js/aes");
/** @type {?} */
var SHA256 = require("crypto-js/sha256");
/**
 * This service is used to encrypt data for
 * security purpose
 * <AUTHOR>
 */
var Encryptor = /** @class */ (function () {
    function Encryptor() {
    }
    /**
     * Calculates a hash in MD5 and returns result as Hex string
     * Project: http://code.google.com/p/as3crypto/
     *
     * See also flex/Java encryption on: http://groups.adobe.com/index.cfm?event=post.display&postid=29193
     * */
    /**
     * Calculates a hash in MD5 and returns result as Hex string
     * Project: http://code.google.com/p/as3crypto/
     *
     * See also flex/Java encryption on: http://groups.adobe.com/index.cfm?event=post.display&postid=29193
     *
     * @param {?} value
     * @return {?}
     */
    Encryptor.hash = /**
     * Calculates a hash in MD5 and returns result as Hex string
     * Project: http://code.google.com/p/as3crypto/
     *
     * See also flex/Java encryption on: http://groups.adobe.com/index.cfm?event=post.display&postid=29193
     *
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return Md5.hashStr(value).toString();
    };
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     * */
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} clear
     * @return {?}
     */
    Encryptor.encrypt = /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} clear
     * @return {?}
     */
    function (clear) {
        /** @type {?} */
        var key = CryptoJS.enc.Utf8.parse('swallowtechltdtn');
        /** @type {?} */
        var iv = CryptoJS.enc.Utf8.parse('swallowtechltdtn');
        /** @type {?} */
        var encrypted = CryptoJS.AES.encrypt(clear, key, {
            keySize: 16,
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        return encrypted;
    };
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     * */
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} jsessionid
     * @param {?} userId
     * @param {?} clear
     * @return {?}
     */
    Encryptor.encryptPredict = /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} jsessionid
     * @param {?} userId
     * @param {?} clear
     * @return {?}
     */
    function (jsessionid, userId, clear) {
        /** @type {?} */
        var encrypted;
        try {
            encrypted = ExternalInterface.call('encryptPass', userId, clear);
        }
        catch (error) {
        }
        if (!encrypted) {
            /** @type {?} */
            var key = jsessionid.substring(0, jsessionid.length > 6 ? 6 : jsessionid.length);
            key += userId.substring(0, userId.length > 4 ? 4 : userId.length);
            // key = this.hash(key);
            key = CryptoJS.enc.Utf8.parse(key);
            /** @type {?} */
            var iv = CryptoJS.enc.Utf8.parse(key);
            encrypted = CryptoJS.AES.encrypt(clear, key, {
                keySize: 16,
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
        }
        return encrypted;
    };
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     * */
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    Encryptor.encode64 = /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    function (text) {
        /** @type {?} */
        var result = btoa(text);
        result = this.replaceAll(result, {
            '=': '(',
            '+': ')'
        });
        return result;
    };
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     **/
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    Encryptor.decode64 = /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    function (text) {
        /** @type {?} */
        var result = this.replaceAll(text, {
            '(': '=',
            ')': '+'
        });
        result = atob(result);
        return result;
    };
    /**
     * Replace all occurences of a string
     **/
    /**
     * Replace all occurences of a string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    Encryptor.replaceAll = /**
     * Replace all occurences of a string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    function (source, map) {
        for (var replaceToken in map) {
            source = source.split(replaceToken).join(map[replaceToken]);
        }
        return source;
    };
    Encryptor.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    Encryptor.ctorParameters = function () { return []; };
    return Encryptor;
}());
export { Encryptor };
//# sourceMappingURL=data:application/json;base64,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