/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from "@angular/core";
import { fromPromise } from "rxjs-compat/observable/fromPromise";
import { CommonService } from "./common.service";
import { ModuleEvent } from "../events/swt-events.module";
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from '../logging/logger.service';
import { parentApplication } from './parent-application.service';
//@dynamic
export class ModuleLoader {
    /**
     * @param {?} common
     */
    constructor(common) {
        this.common = common;
        this._percentHeight = 100;
        this._percentWidth = 100;
        this._loaded = false;
        this._ready = false;
        this._url = "";
        this._listeners = new Array();
        this.logger = new Logger("ModuleLoader", this.common.httpclient);
        this.overlay = document.createElement('div');
        this.mLoaderContent = document.createElement('div');
    }
    /**
     * @param {?} url
     * @return {?}
     */
    loadModule(url) {
        this.logger.info("[ loadModule ] method START");
        try {
            // set url.
            this._url = url;
            parentApplication.setParams(url);
            // dispatch progress event each 100 ms.
            this.progress = setInterval((/**
             * @return {?}
             */
            () => {
                this.dispatchEvent(ModuleEvent.PROGRESS);
            }), 1000);
            // load module.
            this.load(url).subscribe((/**
             * @param {?} component
             * @return {?}
             */
            (component) => {
                this.componentReference = component.create(this.common.injector);
                // attach component to current application.
                this.event = { target: { component: this.componentReference, url: url } };
                clearInterval(this.progress);
                this.dispatchEvent(ModuleEvent.READY, this.event);
                this._ready = true;
                this._loaded = true;
            }), (/**
             * @param {?} error
             * @return {?}
             */
            (error) => {
                this.dispatchEvent(ModuleEvent.ERROR, error);
                this.dispose();
                throw new Error(error);
            }));
        }
        catch (error) {
            this.dispose();
            this.dispatchEvent(ModuleEvent.ERROR, error);
            this.logger.error("[ loadModule ] - method ", error);
        }
        this.logger.info("[ loadModule ] method END");
    }
    /**
     * This method is used to initialize module loader attributes
     * in case of error.
     * @private
     * @return {?}
     */
    dispose() {
        clearInterval(this.progress);
        this._ready = false;
        this._loaded = false;
    }
    /**
     * This method is used to load component instance from its lazy module path.
     * @private
     * @param {?} url
     * @return {?}
     */
    load(url) {
        /** @type {?} */
        const _comp_url = url.indexOf("?") ? url.split("?")[0] : url;
        /** @type {?} */
        const manifest = this.common.manifests
            .find((/**
         * @param {?} m
         * @return {?}
         */
        (m) => m.path === _comp_url));
        /** @type {?} */
        const p = this.common.loader.load(manifest.loadChildren)
            .then((/**
         * @param {?} ngModuleFactory
         * @return {?}
         */
        (ngModuleFactory) => {
            /** @type {?} */
            const moduleRef = ngModuleFactory.create(this.common.injector);
            // Read from the moduleRef injector and locate the dynamic component type
            /** @type {?} */
            const dynamicComponentType = moduleRef.injector.get(_comp_url);
            // Resolve this component factory
            return moduleRef.componentFactoryResolver.resolveComponentFactory(dynamicComponentType);
        }));
        return fromPromise(p);
    }
    /**
     * This method is used to add event listener to module loader.
     * @param {?} name
     * @param {?} callback
     * @return {?}
     */
    addEventListener(name, callback) {
        this._listeners[name] = callback;
    }
    /**
     *   Unloads the module.
     *  Flash Player and AIR will not fully unload and garbage collect this module if
     *  there are any outstanding references to definitions inside the
     *  module.
     * @return {?}
     */
    unload() {
        this.logger.info("unload START");
        try {
            this.componentReference.destroy();
            this.common.applicationRef.detachView(this.componentReference.hostView);
            clearInterval(this.progress);
            this._ready = false;
            this._loaded = false;
            this.dispatchEvent(ModuleEvent.UNLOAD);
        }
        catch (error) {
            this.logger.error("[ unload ] - method error: ", error);
        }
        this.logger.info("unload END");
    }
    /**
     * This method is used to dispatch event with given name.
     * @private
     * @param {?} name
     * @param {?=} args
     * @return {?}
     */
    dispatchEvent(name, args) {
        for (const lstner in this._listeners) {
            if (lstner === name) {
                this._listeners[lstner](args);
            }
        }
    }
    /**
     * @return {?}
     */
    get percentHeight() {
        return this._percentHeight;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set percentHeight(value) {
        this._percentHeight = value;
    }
    /**
     * @return {?}
     */
    get percentWidth() {
        return this._percentWidth;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set percentWidth(value) {
        this._percentWidth = value;
    }
    /**
     * @return {?}
     */
    get loaded() {
        return this._loaded;
    }
    /**
     * @return {?}
     */
    get ready() {
        return this._ready;
    }
    /**
     * @return {?}
     */
    get url() {
        return this._url;
    }
}
ModuleLoader.decorators = [
    { type: Injectable }
];
/** @nocollapse */
ModuleLoader.ctorParameters = () => [
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._percentHeight;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._percentWidth;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._loaded;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._ready;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._url;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._listeners;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.progress;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.componentReference;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.overlay;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.mLoaderContent;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.event;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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