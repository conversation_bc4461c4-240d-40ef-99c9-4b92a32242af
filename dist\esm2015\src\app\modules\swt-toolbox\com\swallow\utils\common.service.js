/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ApplicationRef, ComponentFactoryResolver, Inject, Injectable, Injector, NgModuleFactoryLoader } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import 'jquery-ui-dist/jquery-ui';
import { WindowManager } from '../managers/window-manager.service';
/** @type {?} */
var jp = require('jsonpath/jsonpath.min');
/**
 * This service is a hub of service.
 * it used to resolve the problem of dependency injection.
 * <AUTHOR>
 */
//@dynamic
export class CommonService {
    /**
     * @param {?} httpclient
     * @param {?} Router
     * @param {?} componentFactoryResolver
     * @param {?} windowManager
     * @param {?} resolver
     * @param {?} manifests
     * @param {?} loader
     * @param {?} injector
     * @param {?} applicationRef
     */
    constructor(httpclient, Router, componentFactoryResolver, windowManager, resolver, manifests, loader, injector, applicationRef) {
        this.httpclient = httpclient;
        this.Router = Router;
        this.componentFactoryResolver = componentFactoryResolver;
        this.windowManager = windowManager;
        this.resolver = resolver;
        this.manifests = manifests;
        this.loader = loader;
        this.injector = injector;
        this.applicationRef = applicationRef;
        //      if(CommonService.instance){
        //          throw new Error("Programming Error: Only one instance of CommonService service is allowed at once !");
        //      }
        CommonService.instance = this;
        CommonService.WindowManager = windowManager;
    }
    /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    static jsonpath(obj, path) {
        return jp.query(obj, path);
    }
    /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    static jsonpathes(obj, paths) {
        /** @type {?} */
        let res = obj;
        for (let i = 0; i < paths.length; i++) {
            /** @type {?} */
            const currpath = paths[i];
            res = CommonService.jsonpath(res, currpath);
        }
        return res;
    }
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    getQualifiedClassName(classObject) {
        return classObject.constructor.name + '.ts';
    }
}
CommonService.instance = null;
CommonService.decorators = [
    { type: Injectable }
];
/** @nocollapse */
CommonService.ctorParameters = () => [
    { type: HttpClient },
    { type: Router },
    { type: ComponentFactoryResolver },
    { type: WindowManager },
    { type: ComponentFactoryResolver },
    { type: Array, decorators: [{ type: Inject, args: ["routes",] }] },
    { type: NgModuleFactoryLoader },
    { type: Injector },
    { type: ApplicationRef }
];
if (false) {
    /** @type {?} */
    CommonService.WindowManager;
    /** @type {?} */
    CommonService.instance;
    /** @type {?} */
    CommonService.prototype.httpclient;
    /** @type {?} */
    CommonService.prototype.Router;
    /** @type {?} */
    CommonService.prototype.componentFactoryResolver;
    /** @type {?} */
    CommonService.prototype.windowManager;
    /** @type {?} */
    CommonService.prototype.resolver;
    /** @type {?} */
    CommonService.prototype.manifests;
    /** @type {?} */
    CommonService.prototype.loader;
    /** @type {?} */
    CommonService.prototype.injector;
    /** @type {?} */
    CommonService.prototype.applicationRef;
}
/**
 * Unsubscribe all Observables Subscriptions
 * It will return an empty array if it all went well
 * @param {?} subscriptions
 * @return {?}
 */
export function unsubscribeAllObservables(subscriptions) {
    if (Array.isArray(subscriptions)) {
        subscriptions.forEach((/**
         * @param {?} subscription
         * @return {?}
         */
        (subscription) => {
            if (subscription && subscription.unsubscribe) {
                subscription.unsubscribe();
            }
        }));
        subscriptions = [];
    }
    return subscriptions;
}
//# sourceMappingURL=data:application/json;base64,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