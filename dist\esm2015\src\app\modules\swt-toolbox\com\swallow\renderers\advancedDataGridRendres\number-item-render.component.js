/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Types } from "./types";
export class NumberItemRender extends UIComponent {
    /**
     * @param {?} numberelement
     * @param {?} common
     */
    constructor(numberelement, common) {
        super(numberelement.nativeElement, common);
        this.numberelement = numberelement;
        this.common = common;
        this.text = "";
        this.color = "";
        this.type = Types.NUM;
        this.id = 0;
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        if (this.text) {
            if (this.text.toString().indexOf("-") !== -1 && this.text.indexOf("-") === 0) {
                // this.color = "red";
                this.setStyle("color", "red");
            }
        }
    }
}
NumberItemRender.decorators = [
    { type: Component, args: [{
                selector: 'NumberItemRender',
                template: `
        <span class="number-item-render">
            {{ text }}
        </span>
    `,
                styles: [`
        :host {
            display: block;
            width: 100%;
        }

        .number-item-render {
            display: block;
            width: 100%;
            text-align: right;
            padding: 0 5px;
            margin: 0px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    `]
            }] }
];
/** @nocollapse */
NumberItemRender.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    NumberItemRender.prototype.text;
    /** @type {?} */
    NumberItemRender.prototype.color;
    /** @type {?} */
    NumberItemRender.prototype.type;
    /** @type {?} */
    NumberItemRender.prototype.id;
    /**
     * @type {?}
     * @private
     */
    NumberItemRender.prototype.numberelement;
    /**
     * @type {?}
     * @private
     */
    NumberItemRender.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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