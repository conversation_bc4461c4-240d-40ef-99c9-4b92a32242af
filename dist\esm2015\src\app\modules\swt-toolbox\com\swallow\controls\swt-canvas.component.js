/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, Input, ElementRef } from "@angular/core";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtCanvas extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions------------------------------------------------------------------------------------------------
        this.border = true;
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtCanvas');
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        if (String(this.border) === "false") {
            $($(this.elem.nativeElement).children()[0]).removeClass('canvasControlBar').addClass('canvasControlBarWithoutBorder');
        }
        else {
            $($(this.elem.nativeElement).children()[0]).removeClass('canvasControlBarWithoutBorder').addClass('canvasControlBar');
        }
    }
}
SwtCanvas.decorators = [
    { type: Component, args: [{
                selector: 'SwtCanvas',
                template: `
     <div  fxLayout="row" fxLayoutAlign="{{horizontalAlign}} {{verticalAlign}} " fxLayoutGap="{{horizontalGap}}" class="canvasLayout  {{styleName}}" tabindex="-1">
        <ng-content></ng-content>
        <ng-container #_container></ng-container>
     </div>
  `,
                styles: [`
             :host {
               margin: 0px 0px 5px 0px;
               display: block;
               outline: none;
             }
             .canvasLayout {
               box-sizing: border-box;
               width: 100%;
               outline:none;
             }
   `]
            }] }
];
/** @nocollapse */
SwtCanvas.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtCanvas.propDecorators = {
    border: [{ type: Input, args: ['border',] }],
    styleName: [{ type: Input, args: ['styleName',] }]
};
if (false) {
    /** @type {?} */
    SwtCanvas.prototype.border;
    /** @type {?} */
    SwtCanvas.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    SwtCanvas.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtCanvas.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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