/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
export class SwtHelpWindow {
    constructor() { }
    /**
     * @param {?} url
     * @return {?}
     */
    static open(url) {
        window.open(url, this.content, "toolbar=" + this._toolbar + ",scrollbars=" + this._scrollbars + "," +
            "resizable=" + this._resizable + ",top=" + this.top + "," +
            "left=" + this.left + ",width=" + this.width + "," +
            "height=" + this.height);
    }
    /*
      public static close() {
       //   this.helpwindow.close();
      }
      */
    /**
     * @param {?} value
     * @return {?}
     */
    static toolbar(value) {
        if (value) {
            this._toolbar = "yes";
        }
        else {
            this._toolbar = "no";
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    static scrollbars(value) {
        if (value) {
            this._scrollbars = "yes";
        }
        else {
            this._scrollbars = "no";
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    static resizable(value) {
        if (value) {
            this._resizable = "yes";
        }
        else {
            this._resizable = "no";
        }
    }
}
SwtHelpWindow._toolbar = "yes";
SwtHelpWindow._scrollbars = "yes";
SwtHelpWindow._resizable = "yes";
SwtHelpWindow.width = 500;
SwtHelpWindow.height = 400;
SwtHelpWindow.top = 50;
SwtHelpWindow.left = 50;
SwtHelpWindow.content = "_blank";
SwtHelpWindow.BLANK = "_blank";
SwtHelpWindow.decorators = [
    { type: Injectable }
];
/** @nocollapse */
SwtHelpWindow.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._toolbar;
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._scrollbars;
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._resizable;
    /** @type {?} */
    SwtHelpWindow.width;
    /** @type {?} */
    SwtHelpWindow.height;
    /** @type {?} */
    SwtHelpWindow.top;
    /** @type {?} */
    SwtHelpWindow.left;
    /** @type {?} */
    SwtHelpWindow.content;
    /** @type {?} */
    SwtHelpWindow.BLANK;
}
//# sourceMappingURL=data:application/json;base64,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