/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CustomCell, isBlink, isBold, isClickable, isNegative } from "./cellItemRenderUtilities";
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
export const strFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let type = columnDef['columnType'];
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let showTooltip = columnDef.params.customTooltipFunction(dataContext, row, columnDef.field);
    /** @type {?} */
    let blink_me = isBlink(value);
    /** @type {?} */
    let negative = isNegative(dataContext, field);
    /** @type {?} */
    let isLink = isClickable(dataContext, field);
    /** @type {?} */
    let extraContent = '';
    /** @type {?} */
    let bold = columnDef['bold'] ? columnDef['bold'] : isBold(dataContext, field);
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    //-there is no custom style for the cell
    if (style == "") {
        if (backgroundColor == undefined) {
            backgroundColor = 'transparent';
        }
        if (backgroundColor && backgroundColor.toString().indexOf('|') > -1) {
            // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
            /** @type {?} */
            const colorList = backgroundColor.split('|');
            style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
        }
        else {
            style += ' background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        }
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (columnDef.params.firstAfterSkipedColumn == true)
        style += ' padding-right: 5px; text-align: right; ';
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null || field == "expand") {
            value = "";
        }
        if (typeof (value) == "string") {
            value = StringUtils.replaceAll(value, { '<': '&lt;', '>': '&gt;', '\'': '&apos;', '"': '&quot;', '/': '&sol;' });
            value = value.replace(/\n|\r|(\n\r)/g, ' ');
        }
        value = columnDef.params.customContentFunction(dataContext, columnDef.field, value, type);
        extraContent = columnDef.params.extraHTMLContentFunction(dataContext, columnDef.field, value, type);
        //add tooltip to grid row
        if (showTooltip) {
            showTooltip = ((showTooltip.split("$#$").join("&nbsp;")).split("&@&").join("&#13;")).split("&_&").join("&#44;");
            return `<div data-toggle="tooltip"  title = ${showTooltip} class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''} ${(String(type) == "input" && (enabledFlag == false || !columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''} "  style='${style}' >${isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value}</div>${extraContent}`;
        }
        else
            return `<div class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''} ${(String(type) == "input" && (enabledFlag == false || !columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''} "  style='${style}' >${isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value}</div>${extraContent}`;
    }
    return ``;
});
//# sourceMappingURL=data:application/json;base64,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