import { ElementRef, EventEmitter, OnInit } from '@angular/core';
import { UIComponent } from "./UIComponent.service";
import { CommonService } from "../utils/common.service";
export declare class AdvancedDataGrid extends UIComponent implements OnInit, IAdvancedDataGrid {
    private elemnt;
    private commonS;
    protected treegrid: ElementRef;
    width: string;
    columnDefinition: any[];
    visibleColumn: any[];
    treeData: any;
    headerWidth: string;
    treeWidth: number;
    showTreeHeader: boolean;
    treeName: string;
    displayDisclosureIcon: boolean;
    displayItemsExpanded: boolean;
    firstVisibleItem: any;
    groupIconFunction: Function;
    groupItemRenderer: any;
    groupLabelFunction: Function;
    groupRowHeight: number;
    groupedColumns: any[];
    hierarchicalCollectionView: any;
    itemIcons: any;
    lockedColumnCount: number;
    lockedRowCount: number;
    rendererProviders: any[];
    selectedCells: any[];
    treeColumn: AdvancedDataGridColumn;
    AdvancedDataGridHeaders: any;
    rowClick: EventEmitter<any>;
    rowDbClick: EventEmitter<any>;
    itemExpand: EventEmitter<any>;
    itemCollapse: EventEmitter<any>;
    activate: EventEmitter<any>;
    private _selectedColumn;
    private _selectedRow;
    private _selectedCell;
    private _treeInstance;
    private _firstload;
    private savedState;
    private _rowHeight;
    private scrollable;
    private y;
    private tempWidth;
    private isInternetExplorer;
    constructor(elemnt: ElementRef, commonS: CommonService);
    ngAfterViewInit(): void;
    private _verticalScrollPosition;
    private _height;
    height: any;
    private _dataProvider;
    dataProvider: any[];
    private scrollableContentHeight;
    private advancedDatagridContentHeight;
    calculateDivHeight(): void;
    ngOnInit(): void;
    /**
     * This method is used to paint advanced data grid columns
     */
    private paintColumns;
    /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     **/
    private getScrollbarWidth;
    getTreeStates(): any[];
    setTreeStates(value: any): void;
    /**
     * This method is used to get tree instance.
     */
    getTreeInstance(): any;
    /**
     * This method is used to set metaData to advanced dataGrid instance.
     * @param metadata
     */
    setAdvancedGridMetaData(metadata: any[]): void;
    /**
     * This method is used to get capture of the
     * current tree state and save it in the
     * saved tree state array.
     */
    saveOpenTreeState(): any[];
    /**
     * This method is used to open saved tree state
     * or open a given state.
     * @param state
     */
    openSavedTreeState(state?: any): void;
    /**
     * get all the advancedDataGrid meta data if index is undefined
     * else return the metaData in the given index.
     * @param index
     */
    getAdvancedGridMetaData(index?: number): any;
    setConnectors(value: boolean): void;
    /**
     * This method is used to handle double click event
     * in advanced DataGrid.
     * @param event
     * @param data
     */
    advancedDataGridRowDbClick(event: any, data: any): void;
    /**
     * This method is used to handle click event
     * in advanced DataGrid.
     * @param event
     * @param data
     */
    advancedDataGridRowClick(event: any, row: any): void;
    /**
     * This method called on each group item render.
     * @param row
     */
    onGroupItemRender(row: AdvancedDataGridRow): void;
    /**
     * This method is used to get the selected row.
     */
    getSelectedRow(): AdvancedDataGridRow;
    /**
     * This method is used to set selected row.
     * @param value
     */
    setSelectedRow(value: AdvancedDataGridRow): void;
    /**
     * This method is used to get the selected column.
     */
    getSelectedColumn(): AdvancedDataGridColumn;
    /**
     * This method is used to get the selected
     * cell.
     */
    getSelectedCell(): AdvancedDataGridCell;
    /**
     * Collapses all the nodes of the navigation tree.
     */
    collapseAll(): void;
    /**
     *  Expands all the nodes of the navigation tree in the control.
     */
    expandAll(): void;
    clone(source: any): any;
    refresh(): void;
    /**
     *  Opens or closes all the nodes of the navigation tree below the specified item.
     * @param item
     * @param open
     */
    expandChildrenOf(item: any, open: boolean): void;
    /**
     *  Opens or closes a branch node of the navigation tree.
     * @param item
     * @param open
     * @param animate
     * @param dispatchEvent
     * @param cause
     */
    expandItem(item: any, open: boolean, animate?: boolean, dispatchEvent?: boolean, cause?: any): void;
    /**
     *  Returns the parent of a child item.
     * @param item
     */
    getParentItem(item: any): void;
    /**
     *  Returns true if the specified branch node is open.
     * @param item
     */
    isItemOpen(item: any): void;
    /**
     *  Sets the associated icon in the navigation tree for the item.
     * @param item
     * @param iconID
     * @param iconID2
     */
    setItemIcon(item: any, iconID: string, iconID2: string): void;
    /**
     * This method returns true if is the first load of
     * grid.
     */
    isFirstLoad(): boolean;
    private deepCopy;
    private rowHeight;
    /**
     * This method is used to synchronize the position
     * of headers with the corresponding column
     */
    private synchronizeHeaderLayout;
    verticalScrollPosition: number;
    private isIE;
    private componentToHex;
    private rgbToHex;
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @param value
     */
    private recursive;
    /**
     * This method is used to initialize params to default values.
     */
    initialize(): void;
    itemRenderList: any[];
    private init;
}
export declare class AdvancedDataGridRow extends UIComponent {
    private adelement;
    private commonServ;
    node: any;
    private _cellList;
    private _rowData;
    private _parent;
    private _expanded;
    private _selected;
    private _toolTip;
    private _children;
    itemRenderListFromParent: any[];
    constructor(adelement: any, commonServ: CommonService);
    private _title;
    /**
    * set advancedDataGridRow title.
    * @param value
    */
    title: string;
    private _icon;
    /**
    * set advancedDataGridRow icon.
    * @param value
    */
    icon: string;
    private _key;
    /**
    * set advancedDataGridRow key.
    * @param value
    */
    key: string;
    private _folder;
    /**
    * set advancedDataGridRow icon type.
    * @param value
    */
    folder: boolean;
    private _width;
    /**
     * read only property.
     */
    readonly width: number;
    private _height;
    height: number;
    /**
     * this method return is the current row is expanded or not.
     */
    isExpanded(): boolean;
    /**
     * This method return if the current row is selected or not.
     */
    isSelected(): boolean;
    /**
     * This method is used to get a given attribute from
     * selected row data.
     * @param attr
     */
    getDataAttribute(attr: string): any;
    /**
     * This method is used to create AdvancedDataGridCell
     * Do not use this method because it is used internally.
     */
    createCells(node: any, columns: any): void;
    /**
     * This method will return the parent of selected
     * row.
     */
    getParentItem(): AdvancedDataGridRow;
    getRowData(): any;
    getCells(): AdvancedDataGridCell[];
    getCellAt(index: number): AdvancedDataGridCell;
    /**
     * This method is used to expand the current
     * item.
     */
    expand(): void;
    /**
     * This method is used to collapse the
     * current item.
     */
    collapse(): void;
}
export declare class AdvancedDataGridCell extends UIComponent {
    private cellelement;
    private comServ;
    private _itemRander;
    private _parentRow;
    constructor(cellelement: any, comServ: CommonService);
    private _columnHeader;
    /**
     * This method is used to set the cell column header.
     * @param value
     */
    columnHeader: any;
    /**
     * This method is used to return the parent row of the current cell.
     */
    getParentRow(): AdvancedDataGridRow;
    /**
     * This method is used to set parent row for the current cell.
     * @param row
     */
    setParentRow(row: AdvancedDataGridRow): void;
    /**
     * This method is used to create rander in this
     * grid cell.
     * @param itemRender
     */
    renderCell(itemRender: any): {};
    /**
     * This method is used to get the cell item render.
     */
    getItemRander(): UIComponent;
    /**
     * This method is used to get the cell column header.
     */
    getColumnHeader(): any;
}
export declare class AdvancedDataGridColumn extends UIComponent {
    private adelement;
    private commonServ;
    private _listCell;
    constructor(adelement: any, commonServ: CommonService);
    private _index;
    /**
     * This method is used to return the index of column
     * in the advancedDataGrid.
     */
    readonly index: number;
    private _metaData;
    /**
     * This method is used to get the metaData of this column.
     */
    /**
    * This method is used to set new metaData to current column.
    * @param value
    */
    metaData: IAdvancedDataGridMetaData;
    /**
     * This method is used to get the list of cell in the current
     * column.
     */
    getCells(): AdvancedDataGridCell[];
    /**
     * This method is used to get the cell in the given index.
     * @param index
     */
    getCellAt(index: number): AdvancedDataGridCell;
}
/**
 * Interface to declare all attribute of advancedDataGrid.
 */
export interface IAdvancedDataGrid {
    displayDisclosureIcon: boolean;
    displayItemsExpanded: boolean;
    firstVisibleItem: any;
    groupedColumns: any[];
    groupIconFunction: Function;
    groupItemRenderer: any;
    groupLabelFunction: Function;
    groupRowHeight: number;
    hierarchicalCollectionView: any;
    itemIcons: any;
    lockedColumnCount: number;
    lockedRowCount: number;
    rendererProviders: any[];
    selectedCells: any[];
    treeColumn: AdvancedDataGridColumn;
}
/**
 * This interface define the aAdvancedDataGrid metaData.
 */
export interface IAdvancedDataGridMetaData {
    dataelement: string;
    heading: string;
    type: string;
    columnorder: number;
    width: number;
    draggable: boolean;
    filterable: boolean;
    visible: boolean;
    visible_default: boolean;
    editable: boolean;
    sort: boolean;
}
