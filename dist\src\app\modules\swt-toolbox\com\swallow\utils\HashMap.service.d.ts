export declare class HashMap {
    private keys;
    private values;
    constructor();
    /**
     * This method is used to check if HashMap contain given key.
     * @param key
     */
    containsKey(key: any): boolean;
    /**
     * This method is used to check if HashMap contain given value.
     * @param value
     */
    containsValue(value: any): boolean;
    /**
     * This method is used to return HashMap keys.
     */
    getKeys(): any[];
    /**
     * This method is used to return HashMap values.
     */
    getValues(): any[];
    /**
     * This method is used to return value of given key.
     * @param key
     */
    getValue(key: any): any;
    /**
     * This method is used to get value as object.
     * @param key
     */
    getValueAsObject(key: any): any;
    /**
     * This method is used to get key of given value.
     * @param value
     */
    getKey(value: any): any;
    /**
     * This method is used to add new entry to HashMap.
     * @param key
     * @param value
     */
    put(key: any, value: any): void;
    /**
     * This method is used to add more then one entry to HashMap.
     * @param map
     */
    putAll(map: Object): void;
    /**
    * This method is used to clear the HashMap.
    */
    clear(): void;
    /**
     * This method is used to remove HashMap entry with given key.
     * @param key
     */
    remove(key: any): void;
    /**
     * This method is used to get HashMap size.
     */
    size(): number;
    /**
     * This method is used to check if HashMap is empty or not.
     */
    isEmpty(): boolean;
    /**
     * This method is used to find given key.
     * @param key
     */
    findKey(key: any): number;
    /**
     * This method is used to find given value.
     * @param value
     */
    findValue(value: any): number;
}
