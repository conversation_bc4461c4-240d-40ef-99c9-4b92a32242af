/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { XML } from '../xmlhandler/swt-xml.service';
import { HashMap } from './HashMap.service';
import * as moment_ from 'moment-mini';
/** @type {?} */
const moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
//@dynamic
/**
 * StringUtils.as
 *
 * This class is the base class of additional operations on string
 *
 * <AUTHOR> SwallowTech TN ??
 * @updated by Rihab JABALLAH on 18/09/2018
 */
export class StringUtils {
    constructor() { }
    /**
     * 	unformats a formatted amount
     *
     * \@amountPattern: 0 or others : e.g 12,345,698.50
     *                      1: e.g 12.345.698,50
     *                      2: e.g 12 345 698.50
     *                      3: e.g 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @return {?} string The unformatted amount
     *
     */
    static unformatAmount(formattedAmount, amountPattern) {
        // No problem if precision was lost
        /** @type {?} */
        var unformattedAmount = formattedAmount;
        // If "," is decimal separator
        if (amountPattern == StringUtils.AMOUNT_PATTERN1 || amountPattern == StringUtils.AMOUNT_PATTERN3) {
            unformattedAmount = unformattedAmount.replace(/\./g, "");
            unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        }
        // If "." is decimal separator
        else {
            unformattedAmount = unformattedAmount.replace(/,/g, "");
            unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        }
        return unformattedAmount;
    }
    /**
     * function used to format amout with precision
     * used to unformat format amount but keep the last '.'
     * for the decimal part
     * the block of "," decimal seperator is removed because the "," is replaced by "." in expandMBTAmount()
     *  please see formatAmount() function
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} precision
     * @return {?}
     */
    static unformatAmountWithPrecision(formattedAmount, amountPattern, precision) {
        // No problem if precision was lost
        /** @type {?} */
        var unformattedAmount = formattedAmount;
        //check if the decimal part is 0
        if (formattedAmount.substr(unformattedAmount.length - 3, unformattedAmount.length) == ".00") {
            unformattedAmount = unformattedAmount.substr(0, unformattedAmount.length - (precision + 1));
        }
        else {
            unformattedAmount = formattedAmount;
        }
        //remove all seprator except last one
        unformattedAmount = unformattedAmount.replace(/\.(?=.*\.)/g, "");
        unformattedAmount = unformattedAmount.replace(/\s*/g, "");
        return unformattedAmount;
    }
    /**
     * 	Formats an amount
     *
     * \@formattingOption: 0 or others : 12345698.50 -> 12,345,698.50
     *                      1: 12345698.50 -> 12.345.698,50
     *                      2: 12345698.50 -> 12 345 698.50
     *                      3: 12345698.50 -> 12 345 698,50
     * \@langversion Angular
     *
     * @param {?} amount The amount string to be formatted
     * @param {?} decimals
     * @param {?} amountPattern
     * @return {?} string The formatted amount
     *
     */
    static formatAmount(amount, decimals, amountPattern) {
        /** @type {?} */
        var formattedAmount = amount;
        //var cf: CurrencyFormatter = new CurrencyFormatter();
        try {
            formattedAmount = formattedAmount.toUpperCase();
            formattedAmount = StringUtils.expandMBTAmount(formattedAmount, amountPattern);
            // let rounding = "none";
            // let useThousandsSeparator = "true";
            // let useNegativeSign = "true";
            // let precision = decimals;
            // let currencySymbol = "";
            if (amountPattern == StringUtils.AMOUNT_PATTERN1) {
                this.decimalSeparatorTo = ",";
                this.thousandsSeparatorTo = ".";
            }
            else if (amountPattern == StringUtils.AMOUNT_PATTERN2) {
                this.decimalSeparatorTo = ".";
                this.thousandsSeparatorTo = " ";
            }
            else if (amountPattern == StringUtils.AMOUNT_PATTERN3) {
                this.decimalSeparatorTo = ",";
                this.thousandsSeparatorTo = " ";
            }
            else {
                this.decimalSeparatorTo = ".";
                this.thousandsSeparatorTo = ",";
            }
            // Call currency formatter
            //var numberFormattedAmount: number = Number(formattedAmount);
            if (decimals) {
                formattedAmount = StringUtils.format(Number(formattedAmount).toFixed(decimals));
            }
            else {
                formattedAmount = StringUtils.format(Number(formattedAmount));
            }
        }
        catch (error) {
            formattedAmount = amount;
        }
        //cf= null;
        return formattedAmount;
    }
    /**
     * @private
     * @param {?} number
     * @return {?}
     */
    static format(number) {
        /** @type {?} */
        let result;
        result = number.toString().replace(".", this.decimalSeparatorTo);
        result = result.replace(/\B(?=(\d{3})+(?!\d))/g, this.thousandsSeparatorTo);
        return result;
    }
    /**
     *  Expands 0 to an amount regarding to MBT marker
     * \@langversion Angular
     *
     * @private
     * @param {?} amount
     * @param {?} amountPattern
     * @return {?} string The expanded amount
     *
     */
    static expandMBTAmount(amount, amountPattern) {
        /** @type {?} */
        var marker = (amount.charAt(amount.length - 1)).toString().toUpperCase();
        /** @type {?} */
        var amountNumber;
        // If , is decimal separator
        amount = amount.replace(/\,/g, ".");
        // Extract amount without MBT marker
        amountNumber = Number(StringUtils.isNumeric(marker) ? amount : amount.substr(0, amount.length - 1));
        // Multiply with markers
        if (marker == 'M') {
            amountNumber = amountNumber * 1000000;
        }
        else if (marker == 'B') {
            amountNumber = amountNumber * 1000000000;
        }
        else if (marker == 'K' || marker == 'T') {
            amountNumber = amountNumber * 1000;
        }
        return amountNumber.toString();
    }
    /**
     * 	Determines whether the specified string is numeric.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string.
     *
     * @return {?} Boolean
     *
     */
    static isNumeric(p_string) {
        if (p_string == null) {
            return false;
        }
        /** @type {?} */
        var regx = /^[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?$/;
        return regx.test(p_string);
    }
    /**
     * 	Determines whether the specified string contains any characters.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string to check
     *
     * @return {?} Boolean
     *
     */
    static isEmpty(p_string) {
        if (p_string == null) {
            return true;
        }
        return !p_string.length;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    static isTrue(value) {
        if (typeof (value) === 'string') {
            if (value === 'true') {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            return value;
        }
    }
    /**
     * Pads p_string with specified character to a specified length from the left.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    static padLeft(p_string, p_padChar, p_length) {
        /** @type {?} */
        var s = p_string;
        while (s.length < p_length) {
            s = p_padChar + s;
        }
        return s;
    }
    /**
     * Pads p_string with specified character to a specified length from the right.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string string to pad
     *
     * @param {?} p_padChar Character for pad.
     *
     * @param {?} p_length Length to pad to.
     *
     * @return {?} string
     *
     */
    static padRight(p_string, p_padChar, p_length) {
        /** @type {?} */
        var s = p_string;
        while (s.length < p_length) {
            s += p_padChar;
        }
        return s;
    }
    /**
     * 	Removes extraneous whitespace (extra spaces, tabs, line breaks, etc) from the
     * 	specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose extraneous whitespace will be removed.
     *
     * @return {?} string
     *
     */
    static removeExtraWhitespace(p_string) {
        if (p_string == null) {
            return '';
        }
        /** @type {?} */
        var str = StringUtils.trim(p_string);
        return str.replace(/\s+/g, ' ');
    }
    /**
     * 	Removes whitespace from the front and the end of the specified
     * 	string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning and ending whitespace will
     * 	will be removed.
     *
     * @return {?} string
     *
     */
    static trim(p_string) {
        if (p_string == null) {
            return '';
        }
        return p_string.replace(/^\s+|\s+$/g, '');
    }
    /**
     * 	Removes whitespace from the front (left-side) of the specified string.
     *
     * \@langversion Angular
     *
     * \@tiptext
     * @param {?} p_string The string whose beginning whitespace will be removed.
     *
     * @return {?} string
     *
     */
    static trimLeft(p_string) {
        if (p_string == null) {
            return '';
        }
        return p_string.replace(/^\s+/, '');
    }
    /**
     * 	Removes whitespace from the end (right-side) of the specified string.
     *
     * \@langversion ActionScript 3.0
     * \@playerversion Flash 9.0
     * \@tiptext
     * @param {?} p_string The string whose ending whitespace will be removed.
     *
     * @param {?=} trimChar
     * @return {?} string	.
     *
     */
    static trimRight(p_string, trimChar = '\\s') {
        if (p_string == null) {
            return '';
        }
        /** @type {?} */
        var regex = new RegExp(StringUtils.fixRegex(trimChar) + '+$');
        return p_string.replace(regex, '');
    }
    /**
     * This part of code is used to ignore reserved chars inside a regex
     * @param {?} regex
     * @return {?}
     */
    static fixRegex(regex) {
        /** @type {?} */
        var rtn = regex;
        if (regex != null) {
            /** @type {?} */
            var rsv = ['$', '(', ')', '*', '+', '-', '.', '?', '[', ']', '^', '{', '|', '}'];
            rsv.forEach((/**
             * @param {?} c
             * @return {?}
             */
            c => {
                rtn = rtn.replace(c, "\\" + c);
            }));
        }
        return rtn;
    }
    /**
     * decodeXmlChars
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @return {?}
     */
    static decodeXmlChars(text) {
        /** @type {?} */
        var result = StringUtils.replaceAll(text, {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&apos;': "'",
            '&quot;': '"'
        });
        return result;
    }
    /**
     * replaceAll
     *
     * <AUTHOR> SwallowTech Tunisia
     *
     * Replace all occurences of the map values in a given string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    static replaceAll(source, map) {
        for (var replaceToken in map) {
            source = source.replace(new RegExp(replaceToken, "g"), map[replaceToken]);
        }
        return source;
    }
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    static encode64(text) {
        // btoa() is used to convert string to base64
        /** @type {?} */
        var result = btoa(text);
        result = StringUtils.replaceAll(result, {
            '=': '(',
            '\\+': ')'
        });
        return result;
    }
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    static decode64(text) {
        /** @type {?} */
        var result = StringUtils.replaceAll(text, {
            '\\(': '=',
            '\\)': '+'
        });
        result = atob(result);
        return result;
    }
    /**
     * codeXmlChars
     *
     * this method is used to clean a string from unwanted encoded characters
     *
     * @param {?} text
     * @param {?=} keepHtml
     * @return {?}
     */
    static codeXmlChars(text, keepHtml = false) {
        /** @type {?} */
        var result = null;
        if (!text) {
            return '';
        }
        if (!keepHtml) {
            result = StringUtils.replaceAll(text, { '<': '&lt;', '>': '&gt;', '\'': '&apos;', '"': '&quot;' });
        }
        else {
            // Find Html tags in the provided text
            /** @type {?} */
            var htmlTags /*of string*/ = text.match(StringUtils.htmlTagsRegex);
            // Apply classic XML replacements
            result = StringUtils.codeXmlChars(text, false);
            // Revert back the html tags
            for (var i = 0; i < htmlTags.length; i++) {
                result = result.replace(StringUtils.codeXmlChars(htmlTags[i], false), htmlTags[i]);
            }
        }
        return result;
    }
    //TODO
    // =========================================================<TODO>==============================================================
    /**
    * Removes an XML node from its parent root
    * */
    /* public static deleteXMLNode(xmlToDelete):boolean
      {
          var cn:XMLList = XMLList(xmlToDelete.parent()).children();
          for ( var i:Number = 0 ; i < cn.length() ; i++ )
          {
            if ( cn[i] == xmlToDelete )
            {
             delete cn[i];
            return true;
            }
          }
          return false;
      } */
    /**
    * Basic convertion of XMLList into an Array
    * the resulting array will contain either value or child XML for each node
    * */
    /* 		public static function xmlListToArray(xml:XMLList):Array {
            var array:Array=[], i:string;
            for (i in xml)
              array[i] = xml[i];
            
            return array;
          } */
    /**
     * Converts an XML into an Objects array collection
     * here datafield is your xml tag
     *
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    /* 	public static function convertXmlToArrayCollection( file:string, datafield:string ):ArrayCollection
            {
                var xml:XMLDocument = new XMLDocument( file );
                
                var decoder:SimpleXMLDecoder = new SimpleXMLDecoder();
                var data:Object = decoder.decodeXML( xml );
                var array:Array = ArrayUtil.toArray( data[datafield] );
                return new ArrayCollection( array );
        } */
    // =======================================================</TODO>=============================================================================
    /*
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    static formatDate(date, patern) {
        /** @type {?} */
        let regex = new RegExp('[^a-z0-9A-Z]');
        /** @type {?} */
        var d = new Date(date);
        /** @type {?} */
        var month = '' + (d.getMonth() + 1);
        /** @type {?} */
        var day = '' + d.getDate();
        /** @type {?} */
        var year = d.getFullYear();
        /** @type {?} */
        let separator = patern[patern.search(regex)];
        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;
        if (patern == 'YYYY' + separator + 'MM' + separator + 'DD') {
            return [year, month, day].join(separator);
        }
        else if (patern == 'DD' + separator + 'MM' + separator + 'YYYY' || patern == 'DDMMYYYY') {
            return [day, month, year].join(separator);
        }
        else if (patern == 'MM' + separator + 'DD' + separator + 'YYYY') {
            return [month, day, year].join(separator);
        }
        else if (patern == 'DD' + separator + 'MMM' + separator + 'YYYY') {
            /** @type {?} */
            let month_names_short = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            month = month_names_short[Number(month) - 1];
            return [day, month, year].join('-');
        }
        else
            return '';
    }
    // ======================================== TODO =========================================================================================================
    /**
       * Converts all url parameters into string
       */
    /* public static encodeUrlParams(params):string {
        var paramsAsObj:Object = new Object();
        var classInfo:Object = ObjectUtil.getClassInfo(params);
        var properties:Array = classInfo.properties;
        var pCount:uint = properties.length;
        var fieldName:string ;
        
        // Convert array to object
        for (var p:uint = 0; p < pCount; p++)
        {
          fieldName = properties[p].localName;
          paramsAsObj[fieldName] = params[fieldName];
        }
        
        //Convert object to XML string
        var paramsAxXMLStr:string = StringUtils.convertObjectToXML(paramsAsObj, ["*"], "object", false).toString();
        
        // Return base64 encoded parameters
        return StringUtils.encode64(paramsAxXMLStr);
      } */
    /**
    * Converts an object to XML without using SimpleXMLEncoder class
    *
    * <AUTHOR> SwallowTech Tunisia
    * */
    /* 		public static function cdata(data:string):XML {
            return new XML("<![CDATA[" + data + "]]>");
          }
            
          public static function convertObjectToXML(obj:Object, acceptAttributes:Array, tag:string="object", addCdata:Boolean=false):XML {
            try
            {
              var xml:XML = <{tag}/>;
              var classInfo:Object = ObjectUtil.getClassInfo(obj);
              var properties:Array = classInfo.properties;
                
              var pCount:uint = properties.length;
              var fieldName:string ;
              var xmlNode:XML;
              var text:string;
              var acceptAll:Boolean = false;
                
              if(!acceptAttributes)
                acceptAttributes = new Array();
              else if(acceptAttributes.length==1 && acceptAttributes[0]=='*')
                acceptAll = true;
                
              var acceptAttributesColl:ArrayCollection = new ArrayCollection(acceptAttributes);
              var createdTagsColl:ArrayCollection = new ArrayCollection([]);
              // Loop on all elements of the object
              for (var p:uint = 0; p < pCount; p++)
              {
                fieldName = properties[p].localName;
                // If atribute is to ignore, then do not include in the resulting XML
                if(acceptAll||acceptAttributesColl.contains(fieldName)){
                  text = obj[fieldName].toString();
                  if(addCdata)
                  {
                    xmlNode = <{fieldName}>{cdata(text)}</{fieldName}>
                  }
                  else
                  {
                    xmlNode = <{fieldName}>{text}</{fieldName}>
                  }
        
                  xml.appendChild(xmlNode);
                    
                  // Push the fieldName into createdTagsColl, this will allow adding empty tags for remaining elements later
                  if(!acceptAll)
                    createdTagsColl.addItem(fieldName);
                }
              }
                
            
                
              // Some entries exist on acceptAttributesColl but not on obj properties, So we should add empty tags
              for each (var element:string in acceptAttributes){
                if(!createdTagsColl.contains(element) && element != "*"){
                  text = "";
                  xmlNode = <{element}>{text}</{element}>
                  xml.appendChild(xmlNode);
                }
              }
        
              return xml;
            }
            catch(error:Error)
            {
              var err:string = "Error: " + error.message+", Object: "+obj;
              var xml:XML = <{tag}>{err}</{tag}>;
              return xml;
            }
            return null;
          } */
    /**
    * Converts all url parameters string into an array
    */
    /* public static function decodeUrlParams(encodedParamsStr:string):Array {
        var paramsStr:string = StringUtils.decode64(encodedParamsStr);
        var paramsXml:XML = new XML(paramsStr);
        var paramsArr:Array = new Array();
        for each(var tag:XML in paramsXml.*){
          paramsArr[tag.name()] = tag.text();
        }
        return paramsArr;
      } */
    // ===========================================================================================================================================================    
    /**
     * Get url params as indexed array
     *
     * @param {?} url
     * @return {?}
     */
    static getUrlParams(url) {
        /** @type {?} */
        var qm = url.lastIndexOf("?");
        /** @type {?} */
        var result = [];
        if (qm != -1) {
            /** @type {?} */
            var query = url.substr(qm + 1);
            /** @type {?} */
            var params = query.split("&");
            //Get the parameter key and value  
            for (var i = 0; i < params.length; i++) {
                /** @type {?} */
                var param = params[i];
                /** @type {?} */
                var nameValue = param.split("=");
                if (nameValue.length == 2) {
                    // set key 
                    /** @type {?} */
                    var key = nameValue[0];
                    // set value 
                    /** @type {?} */
                    var val = nameValue[1];
                    // Add to result
                    result[key] = val;
                }
            }
        }
        return result;
    }
    /**
     * Clones an object and returns the cloned instance
     *
     * @param {?} obj
     * @return {?}
     */
    static cloneObject(obj) {
        /** @type {?} */
        var copy = Object.assign({}, obj);
        return copy;
    }
    /**
     * counts number of occurences of pattern inside a given string
     *
     * @param {?} pattern
     * @param {?} target
     * @return {?}
     */
    static countOccurences(pattern, target) {
        // to hold number of occurences of pattern inside a given string 
        /** @type {?} */
        var count = 0;
        /** @type {?} */
        var index = -1;
        // loop through str
        while ((index = target.indexOf(pattern, index + 1)) >= 0) {
            count++;
        }
        return count;
    }
    /**
     *  Substitutes "{n}" tokens within the specified string
     *  with the respective arguments passed in.
     *
     * \@example
     *
     *  var stringParam:String = "here is some info '{0}' and {1}";
     *  trace(StringUtil.substitute(stringParam, 15.4, true));
     *
     *  // this will output the following string:
     *  // "here is some info '15.4' and true"
     * @param {?} stringParam The string to make substitutions in.
     *  This string can contain special tokens of the form
     *  <code>{n}</code>, where <code>n</code> is a zero based index,
     *  that will be replaced with the additional parameters
     *  found at that index if specified.
     *
     * @param {...?} words Additional parameters that can be substituted
     *  in the <code>stringParam</code> parameter at each <code>{n}</code>
     *  location, where <code>n</code> is an integer (zero based)
     *  index value into the array of values specified.
     *  If the first parameter is an array this array will be used as
     *  a parameter list.
     *  This allows reuse of this routine in other methods that want to
     *  use the ... words signature.
     *  For example <pre>
     *     public function myTracer(stringParam:String, ... words):void
     *     {
     *         label.text += StringUtil.substitute(stringParam, words) + "\n";
     *     } </pre>
     *
     * @return {?} New string with all of the <code>{n}</code> tokens
     *  replaced with the respective arguments specified.
     *
     */
    static substitute(stringParam, ...words) {
        try {
            /** @type {?} */
            var result = stringParam.toString();
            for (let index = 0; index < words.length; index++) {
                result = result.replace("{" + index.toString() + "}", words[index]);
            }
            return result;
        }
        catch (error) {
            console.log(error);
        }
    }
    /**
     *  Returns <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     *
     * @param {?} character
     * @return {?} <code>true</code> if the specified string is
     *  a single space, tab, carriage return, newline, or formfeed character.
     */
    static isWhitespace(character) {
        try {
            switch (character) {
                case " ":
                case "\t":
                case "\r":
                case "\n":
                case "\f":
                    return true;
                default:
                    return false;
            }
        }
        catch (error) {
            console.log(error);
        }
    }
    /**
     * Converts an associative array of parameters into an XML string,
     * The goal is to use Jaxb marshalling on java side.
     *
     * <AUTHOR> JABALLAH, SwallowTech Tunisia
     *
     * @param {?} params
     * @param {?} tableName
     * @param {?} operation
     * @param {?} tableLevel
     * @return {?}
     */
    static getKVTypeTabAsXML(params, tableName, operation, tableLevel) {
        /** @type {?} */
        var tab = (tableName != '') ? "tableName='" + tableName + "'" : "tableName=''";
        /** @type {?} */
        var oper = (operation != '') ? "operation='" + operation + "'" : "operation=''";
        /** @type {?} */
        var lev = (tableLevel != '') ? "tableLevel='" + tableLevel + "'" : "tableLevel=''";
        /** @type {?} */
        var xml = new XML('<tabKVType ' + tab + ' ' + oper + ' ' + lev + '/>');
        /** @type {?} */
        var kvTypeNode;
        if (params instanceof Array) {
            //For each key of the array params
            for (let keyname in params) {
                /** @type {?} */
                var value = params[keyname] == undefined ? '' : params[keyname];
                kvTypeNode = new XML('<kvType/>');
                // Append nodes
                kvTypeNode.appendChild('<key>' + keyname + '</key>');
                kvTypeNode.appendChild('<value>' + value + '</value>');
                xml.appendChild(kvTypeNode);
            }
        }
        else if (params instanceof HashMap) {
            for (let key in ((/** @type {?} */ (params))).getKeys()) {
                kvTypeNode = new XML('<kvType/>');
                kvTypeNode.appendChild('<key>' + key + '</key>');
                kvTypeNode.appendChild('<value>' + ((/** @type {?} */ (params))).getValue(key) + '</value>');
                xml.appendChild(kvTypeNode);
            }
        }
        return xml;
    }
    /**
     * Removes an XML node from its parent root
     * @param {?} parentXml : XML
     * @param {?} xmlToDelete :XML
     * @return {?}
     */
    static deleteXMLNode(parentXml, xmlToDelete) {
        if (parentXml.removeChild(xmlToDelete)) {
            return true;
        }
        return false;
    }
}
StringUtils.AMOUNT_PATTERN0 = 0;
StringUtils.AMOUNT_PATTERN1 = 1;
StringUtils.AMOUNT_PATTERN2 = 2;
StringUtils.AMOUNT_PATTERN3 = 3;
StringUtils.htmlTagsRegex = new RegExp("(<style[^>]*/>|<style[^>]*>|</style>|<font[^>]*>|</font>|<br>|<br/>|<a[^>]*>|</a>|<b>|</b>|<li>|</li>|<i>|</i>|<u>|</u>|<p[^>]*>|</p>|<span[^>]*>|</span>)", "ig");
StringUtils.decorators = [
    { type: Injectable }
];
/** @nocollapse */
StringUtils.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    StringUtils.decimalSeparatorTo;
    /**
     * @type {?}
     * @private
     */
    StringUtils.thousandsSeparatorTo;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN0;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN1;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN2;
    /** @type {?} */
    StringUtils.AMOUNT_PATTERN3;
    /**
     * @type {?}
     * @private
     */
    StringUtils.htmlTagsRegex;
}
//# sourceMappingURL=data:application/json;base64,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