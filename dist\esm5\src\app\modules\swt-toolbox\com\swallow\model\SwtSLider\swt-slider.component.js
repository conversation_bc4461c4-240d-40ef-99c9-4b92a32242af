/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { StringUtils } from '../../utils/string-utils.service';
var SwtSlider = /** @class */ (function () {
    function SwtSlider() {
        this.change_ = new EventEmitter();
        this.dateRange = this.createDateRange();
        this.start = 1547512616; // start time in milli Seconds
        // start time in milli Seconds
        this.end = 15475126720; // end time in milli Seconds
        // end time in milli Seconds
        this.options = {
            floor: this.start,
            ceil: this.end,
            step: 1000,
            translate: (/**
             * @param {?} value
             * @param {?} label
             * @return {?}
             */
            function (value, label) {
                return new Date(value).toTimeString(); // this will translate label to time stamp.
            })
        };
        this.miValue = this.start;
        this.maValue = this.end;
        this.value = this.minimum;
        this.firstValue = this.minimum;
        this.lastValue = this.maximum;
        this.numberOfDays = this.maximum - this.minimum;
        this._dataTipFormatString = "DD/MM/YYYY";
    }
    // options: Options = {
    //   stepsArray: this.dateRange.map((date: Date) => {
    //     return { value: date.getTime() };
    //   }),
    //   translate: (value: number, label: LabelType): string => {
    //     return StringUtils.formatDate(new Date(value), this.dataTipFormatString);
    //   },
    //   disabled: false,
    // };
    // options: Options = {
    //   stepsArray: this.dateRange.map((date: Date) => {
    //     return { value: date.getTime() };
    //   }),
    //   translate: (value: number, label: LabelType): string => {
    //     return StringUtils.formatDate(new Date(value), this.dataTipFormatString);
    //   },
    //   disabled: false,
    // };
    /**
     * @private
     * @return {?}
     */
    SwtSlider.prototype.createDateRange = 
    // options: Options = {
    //   stepsArray: this.dateRange.map((date: Date) => {
    //     return { value: date.getTime() };
    //   }),
    //   translate: (value: number, label: LabelType): string => {
    //     return StringUtils.formatDate(new Date(value), this.dataTipFormatString);
    //   },
    //   disabled: false,
    // };
    /**
     * @private
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var dates = [];
            /** @type {?} */
            var diffTime = Math.abs(this.maximum - this.minimum);
            /** @type {?} */
            var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24) + 1);
            /** @type {?} */
            var minAsDate = new Date(this.minimum);
            for (var i = minAsDate.getDate(); i <= (minAsDate.getDate() + diffDays - 1); i++) {
                dates.push(new Date(minAsDate.getFullYear(), minAsDate.getMonth(), i));
            }
            return dates;
        }
        catch (error) {
            console.error("SwtSlider [createDateRange Method] ERROR: ", error);
        }
    };
    /**
     * @return {?}
     */
    SwtSlider.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    Object.defineProperty(SwtSlider.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return !this.options.disabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value == "true") {
                    this.options.disabled = false;
                }
                if (value == "false") {
                    this.options.disabled = true;
                }
            }
            else {
                this.options.disabled = !value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtSlider.prototype, "minimum", {
        get: /**
         * @return {?}
         */
        function () {
            return this.firstValue;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.firstValue = value;
                if (_this.maximum) {
                    _this.updateData();
                }
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtSlider.prototype, "maximum", {
        get: /**
         * @return {?}
         */
        function () {
            return this.lastValue;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.lastValue = value;
                if (_this.minimum) {
                    _this.updateData();
                }
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtSlider.prototype, "values", {
        get: /**
         * @return {?}
         */
        function () {
            return [this.minValue, this.maxValue];
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.minValue = value[0];
                _this.maxValue = value[1];
                _this.updateData();
            }), 5);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtSlider.prototype, "dataTipFormatString", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataTipFormatString;
        },
        //Date format string 
        set: 
        //Date format string 
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dataTipFormatString = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @private
     * @return {?}
     */
    SwtSlider.prototype.updateData = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            this.dateRange = this.createDateRange();
            /** @type {?} */
            var newOptions = Object.assign({}, {
                stepsArray: this.dateRange.map((/**
                 * @param {?} date
                 * @return {?}
                 */
                function (date) {
                    return { value: date.getTime() };
                })),
                translate: (/**
                 * @param {?} value
                 * @param {?} label
                 * @return {?}
                 */
                function (value, label) {
                    return StringUtils.formatDate(new Date(value), _this.dataTipFormatString);
                }),
                disabled: !this.enabled,
            });
            // newOptions.ceil = newCeil;
            this.options = tslib_1.__assign({}, newOptions);
        }
        catch (error) {
            console.error("SwtSlider [updateData METHOD] ERROR: ", error);
        }
    };
    /**
     * @param {?} e
     * @return {?}
     */
    SwtSlider.prototype.onUserChange = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        this.change_.emit(e);
    };
    SwtSlider.decorators = [
        { type: Component, args: [{
                    selector: 'SwtSlider',
                    template: "<div  class=\"slider-container range\">\n    <input matInput class=\"sliderInputs\" [(ngModel)]=\"start\" type=\"number\" [disabled]=\"false\">\n    <ng5-slider [(value)]=\"start\" [(highValue)]=\"end\"        [options]=\"options\" (userChange)=\"onUserChange($event)\"></ng5-slider>\n    <input matInput class=\"sliderInputs\" [(ngModel)]=\"end\"  type=\"number\" [disabled]=\"false\">\n     </div>\n    ",
                    styles: ["", "\n  .sliderInputs {\n    width : 25px;\n\n  }\n  .slider-container{\n    width : 275px;\n  }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtSlider.ctorParameters = function () { return []; };
    SwtSlider.propDecorators = {
        change_: [{ type: Output, args: ['change',] }],
        enabled: [{ type: Input }],
        minimum: [{ type: Input }],
        maximum: [{ type: Input }],
        values: [{ type: Input }],
        dataTipFormatString: [{ type: Input }]
    };
    return SwtSlider;
}());
export { SwtSlider };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.change_;
    /** @type {?} */
    SwtSlider.prototype.minValue;
    /** @type {?} */
    SwtSlider.prototype.maxValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.dateRange;
    /** @type {?} */
    SwtSlider.prototype.start;
    /** @type {?} */
    SwtSlider.prototype.end;
    /** @type {?} */
    SwtSlider.prototype.options;
    /** @type {?} */
    SwtSlider.prototype.miValue;
    /** @type {?} */
    SwtSlider.prototype.maValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.value;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.firstValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.lastValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.numberOfDays;
    /** @type {?} */
    SwtSlider.prototype._dataTipFormatString;
}
//# sourceMappingURL=data:application/json;base64,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