/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
export class AssetsLegendItem extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} cd
     */
    constructor(elem, commonService, cd) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.cd = cd;
        this.styleClassMap = {
            'CONT_AREA_ANTIQUE_WHITE': 'bg-CONT_AREA_ANTIQUE_WHITE',
            'CONT_AREA_APRICOT_PEACH': 'bg-CONT_AREA_APRICOT_PEACH',
            'CONT_AREA_NAVAJO_WHITE': 'bg-CONT_AREA_NAVAJO_WHITE',
            'CONT_AREA_ROSE_FOG': 'bg-CONT_AREA_ROSE_FOG',
            'CONT_SEGMENT_BOLD_RED': 'bg-CONT_SEGMENT_BOLD_RED',
        };
        this.liveValue = '';
        this._labelValue = '';
        this._yField = '';
        this._seriesStyle = '';
        this.created = false;
    }
    /**
     * @return {?}
     */
    get labelValue() {
        return this._labelValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set labelValue(value) {
        this._labelValue = value;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
    /**
     * @return {?}
     */
    get seriesStyle() {
        return this._seriesStyle;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesStyle(value) {
        /** @type {?} */
        const newStyle = this.styleClassMap[value];
        if (newStyle) {
            if (this.created) {
                if (this._seriesStyle) {
                    /** @type {?} */
                    const prevStyle = this.styleClassMap[this._seriesStyle];
                    this.square.nativeElement.classList.remove(prevStyle);
                }
                this.square.nativeElement.classList.add(newStyle);
            }
            this._seriesStyle = value;
        }
        // styleClassMap
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
    // <img  id="circle" #circle src="assets/images/Rolling.gif">
    /**
     * @return {?}
     */
    ngOnInit() {
        // this.liveValue = '-32.798.284.008';
        // this.labelValue.nativeElement.textContent = 'FC*.OwnStress.CentralBank'
        // this.square.nativeElement.classList.add("bg-CONT_AREA_BLIZZARD_BLUE");
        this.assetLabel.nativeElement.textContent = this.labelValue;
        this.assetslabelValue.nativeElement.textContent = this.liveValue;
        /** @type {?} */
        const newStyle = this.styleClassMap[this._seriesStyle];
        if (newStyle) {
            this.square.nativeElement.classList.add(newStyle);
        }
        this.created = true;
    }
    /**
     * @return {?}
     */
    get yField() {
        return this._yField;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set yField(value) {
        this._yField = value;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
}
AssetsLegendItem.decorators = [
    { type: Component, args: [{
                selector: 'AssetsLegendItem',
                template: `
         <HBox #hboxContainer  width="210" height="25"> 
            <div    #square class='square' ></div>
            <div class='assetLabel' #assetLabel ></div>
            <div class='assetslabelValue' #assetslabelValue ></div>
        </HBox>
        `,
                changeDetection: ChangeDetectionStrategy.OnPush,
                styles: [`
            .assetLabel{
                padding-top:2px;
                padding-left:5px;
                position:relative;
                font-size:11px;
                width: 100px;
            }
            .assetslabelValue{
                padding-top:2px;
                padding-left:5px;
                position:relative;
                font-size:11px;
                width: 40%;
                text-align: right;
            }
            .square{
                margin-right:2px !important;
                margin-top:3px;
            }
            
            .bg-CONT_AREA_ANTIQUE_WHITE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -10px;
            }
            .bg-CONT_AREA_APRICOT_PEACH {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -10px -46px;
            }

            .bg-CONT_AREA_NAVAJO_WHITE {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -60px -154px;
            }

            .bg-CONT_AREA_ROSE_FOG {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -118px;
            }


            .bg-CONT_SEGMENT_BOLD_RED {
                width: 30px; height: 16px;
                background: url('assets/ILM/sprites/legendRectangle.png') -210px -190px;
            }





        `]
            }] }
];
/** @nocollapse */
AssetsLegendItem.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: ChangeDetectorRef }
];
AssetsLegendItem.propDecorators = {
    hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
    square: [{ type: ViewChild, args: ['square',] }],
    assetLabel: [{ type: ViewChild, args: ['assetLabel',] }],
    assetslabelValue: [{ type: ViewChild, args: ['assetslabelValue',] }],
    labelValue: [{ type: Input, args: ['labelValue',] }],
    seriesStyle: [{ type: Input, args: ['seriesStyle',] }],
    yField: [{ type: Input, args: ['yField',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    AssetsLegendItem.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    AssetsLegendItem.prototype.square;
    /**
     * @type {?}
     * @protected
     */
    AssetsLegendItem.prototype.assetLabel;
    /**
     * @type {?}
     * @protected
     */
    AssetsLegendItem.prototype.assetslabelValue;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype.styleClassMap;
    /** @type {?} */
    AssetsLegendItem.prototype.liveValue;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype._labelValue;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype._yField;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype._seriesStyle;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype.created;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    AssetsLegendItem.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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