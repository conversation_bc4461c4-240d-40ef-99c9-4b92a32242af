/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { SwtUtil } from '../../../../utils/swt-util.service';
import { CommonService } from '../../../../utils/common.service';
import { SwtAlert } from '../../../../utils/swt-alert.service';
import { ExternalInterface } from '../../../../utils/external-interface.service';
import { SwtPopUpManager } from "../../../../managers/swt-pop-up-manager.service";
import { ConfigurableToolTip } from './ConfigurableToolTip';
export class ProcessStatusBox extends Container {
    /**
     * @param {?} commonService
     * @param {?} element
     */
    constructor(commonService, element) {
        super(element, commonService);
        this.commonService = commonService;
        this.element = element;
        this.redIcon = 'assets/images/red.png';
        this.greenImage = 'assets/images/green.png';
        this.amberIcon = 'assets/images/amber.png';
        this.inProgress = 'assets/images/Gear.gif';
        this.parentDocument = null;
        this.GREEN_STATE = "green";
        this.AMBER_STATE = "amber";
        this.RED_STATE = "red";
        this.customTooltip = null;
        this.recalculateEnable = false;
        this.state = this.GREEN_STATE;
        this.dataArray = [];
        this.swtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        jQuery(this.image.nativeElement).mouseenter((/**
         * @return {?}
         */
        () => {
            this.createTooltip();
        }));
    }
    /**
     * @return {?}
     */
    createTooltip() {
        /** @type {?} */
        let calclulatedXPosition = 0;
        /** @type {?} */
        let calclulatedYPosition = 0;
        /** @type {?} */
        const toolTipWidth = 400;
        this.customTooltip = SwtPopUpManager.createPopUp(parent, ConfigurableToolTip, {});
        this.customTooltip.enableResize = false;
        this.customTooltip.width = '' + toolTipWidth;
        this.customTooltip.height = "160";
        this.customTooltip.enableResize = false;
        this.customTooltip.showControls = false;
        // this.uploadWindow.initX = 10;
        // this.uploadWindow.initY = 10;
        /** @type {?} */
        const boxPosition = this.element.nativeElement.getBoundingClientRect();
        if (boxPosition.x) {
            calclulatedXPosition = boxPosition.x - (toolTipWidth - 30);
            calclulatedYPosition = boxPosition.y;
        }
        this.customTooltip.setWindowXY('' + calclulatedXPosition, '' + calclulatedYPosition);
        this.customTooltip.showHeader = false;
        this.customTooltip.dataArray = this.dataArray;
        this.customTooltip.parentDocument = this.parentDocument;
        this.customTooltip.processBox = this;
        this.customTooltip.recalculateEnable = this.recalculateEnable;
        this.customTooltip.display();
    }
    /**
     * @return {?}
     */
    removeTooltip() {
        this.customTooltip.close();
    }
    /**
     * @return {?}
     */
    setCalculatingState() {
        this.image.nativeElement.src = this.inProgress;
    }
    /**
     * @return {?}
     */
    setRed() {
        this.image.nativeElement.src = this.redIcon;
    }
    /**
     * @param {?=} dataAsXML
     * @return {?}
     */
    setStyleFuction(dataAsXML = null) {
        this.dataArray = [];
        /** @type {?} */
        let recalculateState = false;
        /** @type {?} */
        const data = SwtUtil.convertObjectToArray(dataAsXML.element);
        for (let index = 0; index < data.length; index++) {
            /** @type {?} */
            const element = data[index];
            this.dataArray[element.name] = element.content;
        }
        if (this.dataArray["RECALCULATE"] == 'Y') {
            recalculateState = true;
        }
        if (this.parentDocument.isCalculationFinished == true) {
            if ((parseInt(this.dataArray["ACCOUNTS_INCOMPLETE"]) > 0) || (parseInt(this.dataArray["ACCOUNTS_INCONSISTENT"]) > 0)) {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.redIcon;
                if (this.state != this.RED_STATE) {
                    this.parentDocument.isRecalculateAlertShown = false;
                }
                //FIXME:CHECK IF WORKING
                this.parentDocument.recalculateData = recalculateState;
                this.parentDocument.showDataNotUpdatedAlert(recalculateState);
                this.state = this.RED_STATE;
                this.recalculateEnable = true;
            }
            else if (parseInt(this.dataArray["ACCOUNTS_NEW_DATA"]) > 0) {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.amberIcon;
                if (this.state != this.AMBER_STATE) {
                    this.parentDocument.isRecalculateAlertShown = false;
                }
                this.parentDocument.recalculateData = recalculateState;
                this.parentDocument.showDataNotUpdatedAlert(recalculateState);
                this.state = this.AMBER_STATE;
                this.recalculateEnable = true;
            }
            else {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.greenImage;
                this.state = this.GREEN_STATE;
                this.recalculateEnable = false;
            }
            if (this.dataArray["RECALCULATE"] == "N") {
                if (!this.parentDocument.isRecalculateAlertShown) {
                    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-NodataForSelection', 'The selected date is outside the retention period, No updated data will be displayed'));
                    this.parentDocument.isRecalculateAlertShown = true;
                }
            }
        }
    }
}
ProcessStatusBox.decorators = [
    { type: Component, args: [{
                selector: 'ProcessStatusBox',
                template: `
    <img class="processStatusBox" #image src="assets/images/green.png">
  `,
                styles: [`
        .processStatusBox {
                height:20px;
                width : 20px;
                margin: auto 0px;
                box-shadow: 1px 1px 1px 1px #888888;
                border: 1px solid white;
        }
      `]
            }] }
];
/** @nocollapse */
ProcessStatusBox.ctorParameters = () => [
    { type: CommonService },
    { type: ElementRef }
];
ProcessStatusBox.propDecorators = {
    image: [{ type: ViewChild, args: ['image',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    ProcessStatusBox.prototype.image;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.redIcon;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.greenImage;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.amberIcon;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.inProgress;
    /** @type {?} */
    ProcessStatusBox.prototype.parentDocument;
    /** @type {?} */
    ProcessStatusBox.prototype.dataFromXMLParent;
    /** @type {?} */
    ProcessStatusBox.prototype.GREEN_STATE;
    /** @type {?} */
    ProcessStatusBox.prototype.AMBER_STATE;
    /** @type {?} */
    ProcessStatusBox.prototype.RED_STATE;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.customTooltip;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.recalculateEnable;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.state;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.swtAlert;
    /** @type {?} */
    ProcessStatusBox.prototype.dataArray;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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