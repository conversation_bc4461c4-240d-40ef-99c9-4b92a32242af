/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { focusManager } from "../managers/focus-manager.service";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtStepper {
    /**
     * @param {?} elem
     */
    constructor(elem) {
        this.elem = elem;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this._toolTipPreviousObject = null;
        /*private variable to hold key down event */
        this._keyDown = new Function();
        /*private variable to hold key down event */
        this._keyUp = new Function();
        /* private variable to hold focus event */
        this._focus = new Function();
        /* private variable to hold focus out event */
        this._focusOut = new Function();
        /* private variable to hold chnage event */
        this._change = new Function();
        /* Private variable to hold enabled property */
        this._enabled = true;
        /* Private variable to hold editable property */
        this._editable = true;
        this._required = false;
        this.selector = "SwtStepper";
        // private variable to handle component visibility.
        this._visible = true;
        /* to handle keyup event */
        this.keyup_ = new EventEmitter();
        /* Input to handle keyDonw event */
        this.keyDown_ = new EventEmitter();
        /* Input to handle focus event */
        this.focus_ = new EventEmitter();
        /* Input to handle focus out event */
        this.focusOut_ = new EventEmitter();
        /* Input to handle change event */
        this.change_ = new EventEmitter();
        this.height = "22px";
        //text getter and setter
        this.firstCall = true;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set toolTipPreviousValue(value) {
        this._toolTipPreviousObject = value;
    }
    /**
     * @return {?}
     */
    get toolTipPreviousValue() {
        return this._toolTipPreviousObject;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        if (value !== undefined) {
            if (value.indexOf('%') === -1) {
                this._width = value + "px";
            }
            else {
                this._width = value;
            }
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /* Input to hold numeric input id */
    //text getter and setter
    /**
     * @param {?} value
     * @return {?}
     */
    set value(value) {
        this.text = value;
        if (this.spinnerObject !== undefined) {
            this.spinnerObject.spinner("value", Number(value));
        }
    }
    /**
     * @return {?}
     */
    get value() {
        /** @type {?} */
        let rtntext = this._text;
        if (this.spinnerObject && this.spinnerObject.spinner) {
            rtntext = this.spinnerObject.spinner().spinner("value");
        }
        return rtntext;
    }
    //enabled getter and setter.
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) !== 'string') {
            this._enabled = value;
            if (this.spinnerObject) {
                this.spinnerObject.spinner("option", "disabled", !this._enabled);
            }
        }
        else {
            if (value + '' === 'true') {
                this._enabled = true;
            }
            else {
                this._enabled = false;
            }
            if (this.spinnerObject) {
                this.spinnerObject.spinner("option", "disabled", !this._enabled);
            }
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) !== 'string') {
            this._enabled = value;
            value ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
        }
        else {
            if (value + '' === 'true') {
                this._visible = true;
                $(this.elem.nativeElement).show();
            }
            else {
                this._visible = false;
                $(this.elem.nativeElement).hide();
            }
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        this._text = value;
        if (this.spinnerObject !== undefined) {
            this.spinnerObject.spinner("value", Number(value));
        }
        if (this.firstCall) {
            this.originalValue = value;
            this.firstCall = false;
        }
        this.spyChanges(value);
    }
    /**
     * @return {?}
     */
    get text() {
        /** @type {?} */
        let rtntext = this._text;
        if (this.spinnerObject !== undefined) {
            rtntext = this.spinnerObject.spinner("value");
        }
        return rtntext;
    }
    /* editable getter and setter */
    /**
     * @return {?}
     */
    get editable() {
        return this._editable;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        if (typeof (value) !== 'string') {
            this._editable = value;
        }
        else {
            if (value + '' === 'true') {
                this._editable = true;
            }
            else {
                this._editable = false;
            }
        }
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set required(value) {
        if (typeof (value) == "string") {
            if (value === 'true') {
                this._required = true;
            }
            else {
                this._required = false;
            }
        }
        else {
            this._required = value;
        }
    }
    /**
     * @return {?}
     */
    get required() {
        return this._required;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.spinnerObject = $(this.elem.nativeElement.children[0]);
        //        $($(this.elem.nativeElement)[0]).replaceWith(this.spinnerObject);
        this.spinnerObject.spinner({
            min: this.minimum,
            max: this.maximum,
            step: this.stepSize,
            spin: (/**
             * @param {?} event
             * @param {?} ui
             * @return {?}
             */
            (event, ui) => {
                this.spyChanges(ui.value);
                this.change_.emit(event);
            })
        });
        // $(this.elem.nativeElement).width(this.width);
        // set enable / disable property
        if (this.enabled === true) {
            this.spinnerObject.spinner("enable");
        }
        else {
            this.spinnerObject.spinner({
                disabled: true
            });
        }
        $(this.spinnerObject).width(this.width);
        //-START- set default text if not null or empty
        if (this._text !== null || this._text !== undefined) {
            this.spinnerObject.spinner("value", this._text);
        }
        // Added by Rihab.J @07/12/2018 - needed to be used in dynamically added SwtStepper.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtStepper');
        // set id to button DOM.
        if (this.id) {
            $($(this.elem.nativeElement)[0]).attr("id", this.id);
        }
        //-END-
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        /** @type {?} */
        const popperContentEl = this.elem.nativeElement.querySelector('popper-content');
        if (popperContentEl)
            this.elem.nativeElement.appendChild(popperContentEl);
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    updateText(event) {
        this.value = event.target.value;
    }
    /**
     * @param {?} changes
     * @return {?}
     */
    ngOnChanges(changes) {
    }
    /**
     * this function is fired on keyup event.
     * @param {?} event
     * @return {?}
     */
    onkeyup(event) {
        this.keyup_.emit(event);
        this._keyUp(event);
        if ((this.value !== event.target.value) && this.value != null) {
            this.value = event.target.value;
        }
        this.spyChanges(event.target.value);
    }
    /**
     * this function is fired on keyup event.
     * @param {?} event
     * @return {?}
     */
    onkeyDown(event) {
        this.keyDown_.emit(event);
        this._keyDown(event);
    }
    /**
     * this function is fired on focus event.
     * @param {?} event
     * @return {?}
     */
    onFocus(event) {
        this.focus_.emit(event);
        this._focus(event);
        focusManager.focusTarget = this.id;
    }
    /**
     * this function is fired on focusOut event.
     * @param {?} event
     * @return {?}
     */
    onFocusOut(event) {
        this.focusOut_.emit(event);
        this._focusOut(event);
    }
    /**
     * this function is fired on change event.
     * @param {?} event
     * @return {?}
     */
    onChange(event) {
        this.change_.emit(event);
        this._change(event);
    }
    /**
     * @return {?}
     */
    setFocus() {
    }
    /******************************************************************************************************************/
    /*                                           GETTER AND SETTER                                                    */
    /**
     * **************************************************************************************************************
     * @return {?}
     */
    get keyDown() {
        return this._keyDown;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set keyDown(value) {
        this._keyDown = value;
    }
    /*-----------------------------------*/
    /**
     * @return {?}
     */
    get keyUp() {
        return this._keyUp;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set keyUp(value) {
        this._keyUp = value;
    }
    /*-----------------------------------*/
    /**
     * @return {?}
     */
    get focus() {
        return this._focus;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set focus(value) {
        this._focus = value;
    }
    /*-----------------------------------*/
    /**
     * @return {?}
     */
    get focusOut() {
        return this._focusOut;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set focusOut(value) {
        this._focusOut = value;
    }
    /*-----------------------------------*/
    /**
     * @return {?}
     */
    get change() {
        return this._change;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set change(value) {
        this._change = value;
    }
    // added by Khalil.B spy method
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
    }
    /**
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue = this._text;
        this.spyChanges(this._text);
    }
}
SwtStepper.decorators = [
    { type: Component, args: [{
                selector: 'SwtStepper',
                template: `
              <input #stepperinput 
               selector="SwtStepper"
               type="number"
               value="{{ value }}"
               id="{{ id }}"
               min="{{ minimum}}"  
               max="{{ maximum }}" 
               step="{{stepSize}}"
               (keydown)="onkeyDown($event)"
               (change)="onChange($event)"
               (focus)="onFocus($event)"
               (focusout)="onFocusOut($event)"
               (keyup)="onkeyup($event)"
               class="{{ styleName }}"
               Swtrestrict="[0-9]"
               tabindex="{{ tabindex }}"
               [attr.disabled]= "enabled? null: true" 
               [attr.readOnly]= "editable? null: true"
                popper="{{this.toolTipPreviousValue}}"
     [popperTrigger]="'hover'"
     [popperDisabled]="toolTipPreviousValue === null ? true : false"
     [popperPlacement]="'bottom'"
     [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}"
               [class.requiredInput]= "this.required==true && (this.text == null)  && enabled==true"  
               placement="top">
           <!--<input Swtregex="[0-9]*" name="value">    -->
  `,
                styles: [`
      input {
          /* padding-left: 2px;
           padding-right: 10px;
           border: 1px solid #7f9db9; 
           width: 100%; 
           height: 20px;
           padding-right: 0px;*/
      }
      input:disabled {
          color: gray;
      }


  `]
            }] }
];
/** @nocollapse */
SwtStepper.ctorParameters = () => [
    { type: ElementRef }
];
SwtStepper.propDecorators = {
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    regex: [{ type: Input, args: ['regex',] }],
    pattern: [{ type: Input, args: ['pattern',] }],
    restrict: [{ type: Input, args: ['restrict',] }],
    minimum: [{ type: Input, args: ['minimum',] }],
    maximum: [{ type: Input, args: ['maximum',] }],
    keyup_: [{ type: Output, args: ['keyUp',] }],
    keyDown_: [{ type: Output, args: ['keyDown',] }],
    focus_: [{ type: Output, args: ['focus',] }],
    focusOut_: [{ type: Output, args: ['focusOut',] }],
    change_: [{ type: Output, args: ['change',] }],
    styleName: [{ type: Input, args: ['styleName',] }],
    toolTip: [{ type: Input, args: ['toolTip',] }],
    maxChars: [{ type: Input, args: ['maxChars',] }],
    tabindex: [{ type: Input, args: ['tabIndex',] }],
    stepSize: [{ type: Input, args: ["stepSize",] }],
    toolTipPreviousValue: [{ type: Input }],
    width: [{ type: Input }],
    height: [{ type: Input }, { type: Input, args: ['height',] }],
    id: [{ type: Input, args: ['id',] }],
    value: [{ type: Input }],
    enabled: [{ type: Input }],
    visible: [{ type: Input }],
    text: [{ type: Input }],
    editable: [{ type: Input }],
    required: [{ type: Input, args: ['required',] }],
    stepperDOM: [{ type: ViewChild, args: ['stepperinput',] }]
};
if (false) {
    /** @type {?} */
    SwtStepper.prototype.originalValue;
    /** @type {?} */
    SwtStepper.prototype.onSpyChange;
    /** @type {?} */
    SwtStepper.prototype.onSpyNoChange;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._toolTipPreviousObject;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._keyDown;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._keyUp;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.spinnerObject;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.spinnerInut;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._required;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.selector;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype._visible;
    /** @type {?} */
    SwtStepper.prototype.regex;
    /** @type {?} */
    SwtStepper.prototype.pattern;
    /** @type {?} */
    SwtStepper.prototype.restrict;
    /** @type {?} */
    SwtStepper.prototype.minimum;
    /** @type {?} */
    SwtStepper.prototype.maximum;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.keyup_;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.keyDown_;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.focus_;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.focusOut_;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.change_;
    /** @type {?} */
    SwtStepper.prototype.styleName;
    /** @type {?} */
    SwtStepper.prototype.toolTip;
    /** @type {?} */
    SwtStepper.prototype.maxChars;
    /** @type {?} */
    SwtStepper.prototype.tabindex;
    /** @type {?} */
    SwtStepper.prototype.stepSize;
    /** @type {?} */
    SwtStepper.prototype.height;
    /** @type {?} */
    SwtStepper.prototype.id;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.firstCall;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.stepperDOM;
    /**
     * @type {?}
     * @private
     */
    SwtStepper.prototype.elem;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXN0ZXBwZXIuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1zdGVwcGVyLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBVSxLQUFLLEVBQUUsU0FBUyxFQUE0QixNQUFNLEVBQUUsWUFBWSxFQUFFLFVBQVUsRUFBNEIsTUFBTSxlQUFlLENBQUM7QUFJMUosT0FBTyxFQUFFLFlBQVksRUFBRSxNQUFNLG1DQUFtQyxDQUFDOzs7TUFNM0QsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQUM7QUFnRDNCLE1BQU0sT0FBTyxVQUFVOzs7O0lBa05uQixZQUFvQixJQUFnQjtRQUFoQixTQUFJLEdBQUosSUFBSSxDQUFZO1FBL01iLGdCQUFXLEdBQXNCLElBQUksWUFBWSxFQUFPLENBQUM7UUFDdkQsa0JBQWEsR0FBc0IsSUFBSSxZQUFZLEVBQU8sQ0FBQztRQUM1RSwyQkFBc0IsR0FBUSxJQUFJLENBQUM7O1FBRW5DLGFBQVEsR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDOztRQUUxQixXQUFNLEdBQUcsSUFBSSxRQUFRLEVBQUUsQ0FBQzs7UUFFeEIsV0FBTSxHQUFHLElBQUksUUFBUSxFQUFFLENBQUM7O1FBRXhCLGNBQVMsR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDOztRQUUzQixZQUFPLEdBQUcsSUFBSSxRQUFRLEVBQUUsQ0FBQzs7UUFFekIsYUFBUSxHQUFHLElBQUksQ0FBQzs7UUFFaEIsY0FBUyxHQUFHLElBQUksQ0FBQztRQVVqQixjQUFTLEdBQVksS0FBSyxDQUFDO1FBRTNCLGFBQVEsR0FBQyxZQUFZLENBQUM7O1FBRXRCLGFBQVEsR0FBWSxJQUFJLENBQUM7O1FBWVIsV0FBTSxHQUFHLElBQUksWUFBWSxFQUFZLENBQUM7O1FBRXBDLGFBQVEsR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDOztRQUUxQyxXQUFNLEdBQUcsSUFBSSxZQUFZLEVBQVksQ0FBQzs7UUFFbkMsY0FBUyxHQUFHLElBQUksWUFBWSxFQUFZLENBQUM7O1FBRTNDLFlBQU8sR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDO1FBdUNoRCxXQUFNLEdBQUcsTUFBTSxDQUFDOztRQTZEekIsY0FBUyxHQUFZLElBQUksQ0FBQTtJQTBEakMsQ0FBQzs7Ozs7SUFqSkQsSUFDSSxvQkFBb0IsQ0FBQyxLQUFLO1FBQzFCLElBQUksQ0FBQyxzQkFBc0IsR0FBRyxLQUFLLENBQUM7SUFDeEMsQ0FBQzs7OztJQUVELElBQUksb0JBQW9CO1FBQ3BCLE9BQU8sSUFBSSxDQUFDLHNCQUFzQixDQUFDO0lBQ3ZDLENBQUM7Ozs7O0lBR0QsSUFDSSxLQUFLLENBQUMsS0FBYTtRQUNuQixJQUFJLEtBQUssS0FBSyxTQUFTLEVBQUU7WUFDckIsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO2dCQUMzQixJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssR0FBRyxJQUFJLENBQUM7YUFDOUI7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7YUFDdkI7U0FDSjtJQUNMLENBQUM7Ozs7SUFDRCxJQUFJLEtBQUs7UUFDTCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDdkIsQ0FBQzs7Ozs7OztJQVNELElBQ0ksS0FBSyxDQUFDLEtBQWE7UUFFbkIsSUFBSSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUM7UUFDbEIsSUFBSSxJQUFJLENBQUMsYUFBYSxLQUFLLFNBQVMsRUFBRTtZQUNsQyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7U0FDdEQ7SUFDTCxDQUFDOzs7O0lBQ0QsSUFBSSxLQUFLOztZQUNELE9BQU8sR0FBRyxJQUFJLENBQUMsS0FBSztRQUN4QixJQUFJLElBQUksQ0FBQyxhQUFhLElBQUksSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUU7WUFDbEQsT0FBTyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxFQUFFLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1NBQzNEO1FBQ0QsT0FBTyxPQUFPLENBQUM7SUFDbkIsQ0FBQzs7Ozs7O0lBRUQsSUFDSSxPQUFPLENBQUMsS0FBYztRQUN0QixJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLEVBQUU7WUFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUM7WUFDdEIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO2dCQUNwQixJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBRSxDQUFDO2FBQ3RFO1NBQ0o7YUFBTTtZQUNILElBQUksS0FBSyxHQUFHLEVBQUUsS0FBSyxNQUFNLEVBQUU7Z0JBQ3ZCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO2FBQ3hCO2lCQUFNO2dCQUNILElBQUksQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFDO2FBQ3pCO1lBQ0QsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO2dCQUNwQixJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBRSxDQUFDO2FBQ3RFO1NBQ0o7SUFDTCxDQUFDOzs7O0lBQ0QsSUFBSSxPQUFPO1FBQ1AsT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDO0lBQ3pCLENBQUM7Ozs7O0lBQ0QsSUFDSSxPQUFPLENBQUMsS0FBYztRQUN0QixJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLEVBQUU7WUFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUM7WUFDdEIsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUM7U0FDakY7YUFBTTtZQUNILElBQUksS0FBSyxHQUFHLEVBQUUsS0FBSyxNQUFNLEVBQUU7Z0JBQ3ZCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO2dCQUNyQixDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQTthQUNwQztpQkFBTTtnQkFDSCxJQUFJLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQztnQkFDdEIsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUE7YUFDcEM7U0FDSjtJQUNMLENBQUM7Ozs7SUFDRCxJQUFJLE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7SUFDekIsQ0FBQzs7Ozs7SUFHRCxJQUNJLElBQUksQ0FBQyxLQUFhO1FBQ2xCLElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFDO1FBQ25CLElBQUksSUFBSSxDQUFDLGFBQWEsS0FBSyxTQUFTLEVBQUU7WUFDbEMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO1NBQ3REO1FBQ0QsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ2hCLElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBQzNCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO1NBQzFCO1FBQ0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUMzQixDQUFDOzs7O0lBQ0QsSUFBSSxJQUFJOztZQUNBLE9BQU8sR0FBRyxJQUFJLENBQUMsS0FBSztRQUN4QixJQUFJLElBQUksQ0FBQyxhQUFhLEtBQUssU0FBUyxFQUFFO1lBQ2xDLE9BQU8sR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQztTQUNqRDtRQUNELE9BQU8sT0FBTyxDQUFDO0lBQ25CLENBQUM7Ozs7O0lBRUQsSUFBSSxRQUFRO1FBQ1IsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO0lBQzFCLENBQUM7Ozs7O0lBRUQsSUFDSSxRQUFRLENBQUMsS0FBYztRQUN2QixJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLEVBQUU7WUFDN0IsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7U0FDMUI7YUFBTTtZQUNILElBQUksS0FBSyxHQUFHLEVBQUUsS0FBSyxNQUFNLEVBQUU7Z0JBQ3ZCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO2FBQ3pCO2lCQUFNO2dCQUNILElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO2FBQzFCO1NBQ0o7SUFDTCxDQUFDOzs7Ozs7SUFHRSxJQUNJLFFBQVEsQ0FBRSxLQUFVO1FBQ3BCLElBQUcsT0FBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLFFBQVEsRUFBQztZQUN6QixJQUFHLEtBQUssS0FBRyxNQUFNLEVBQUM7Z0JBQ2QsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUM7YUFDekI7aUJBQUs7Z0JBQ0YsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7YUFDMUI7U0FDSjthQUFLO1lBQ0YsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7U0FDMUI7SUFDTCxDQUFDOzs7O0lBRUQsSUFBSSxRQUFRO1FBQ1IsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO0lBQzFCLENBQUM7Ozs7SUFNSixRQUFRO1FBRUosSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDcEUsMkVBQTJFO1FBQ25FLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDO1lBQ3ZCLEdBQUcsRUFBRSxJQUFJLENBQUMsT0FBTztZQUNqQixHQUFHLEVBQUUsSUFBSSxDQUFDLE9BQU87WUFDakIsSUFBSSxFQUFDLElBQUksQ0FBQyxRQUFRO1lBQ2xCLElBQUk7Ozs7O1lBQUUsQ0FBQyxLQUFLLEVBQUUsRUFBRSxFQUFFLEVBQUU7Z0JBQ2hCLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFBO2dCQUN6QixJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUM3QixDQUFDLENBQUE7U0FDSixDQUFDLENBQUM7UUFDSixnREFBZ0Q7UUFDL0MsZ0NBQWdDO1FBQ2hDLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQUU7WUFDdkIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7U0FDeEM7YUFBTTtZQUNILElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDO2dCQUN2QixRQUFRLEVBQUUsSUFBSTthQUNmLENBQUMsQ0FBQztTQUNSO1FBQ0QsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ3hDLCtDQUErQztRQUMvQyxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssSUFBSSxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxFQUFFO1lBQ2pELElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7U0FDbkQ7UUFDRCxvRkFBb0Y7UUFDcEYsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBQyxZQUFZLENBQUMsQ0FBQTtRQUM5RCx3QkFBd0I7UUFDeEIsSUFBSSxJQUFJLENBQUMsRUFBRSxFQUFFO1lBQ1YsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7U0FDdkQ7UUFDRCxPQUFPO0lBQ1gsQ0FBQzs7OztJQUNELGVBQWU7O2NBQ0wsZUFBZSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLGFBQWEsQ0FBQyxnQkFBZ0IsQ0FBQztRQUMvRSxJQUFHLGVBQWU7WUFDZCxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxXQUFXLENBQUMsZUFBZSxDQUFDLENBQUM7SUFFN0QsQ0FBQzs7Ozs7O0lBQ08sVUFBVSxDQUFDLEtBQUs7UUFDcEIsSUFBSSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQztJQUNwQyxDQUFDOzs7OztJQUNELFdBQVcsQ0FBQyxPQUFPO0lBRW5CLENBQUM7Ozs7OztJQUtELE9BQU8sQ0FBQyxLQUFLO1FBQ1QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDeEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNuQixJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssS0FBSyxLQUFLLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxFQUFFO1lBQzNELElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUM7U0FDbkM7UUFDRixJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDdEMsQ0FBQzs7Ozs7O0lBS0QsU0FBUyxDQUFDLEtBQUs7UUFDWCxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUMxQixJQUFJLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ3pCLENBQUM7Ozs7OztJQUtELE9BQU8sQ0FBQyxLQUFLO1FBQ1QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDeEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNuQixZQUFZLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUM7SUFDdkMsQ0FBQzs7Ozs7O0lBS0QsVUFBVSxDQUFDLEtBQUs7UUFDWixJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUMzQixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQzFCLENBQUM7Ozs7OztJQUtELFFBQVEsQ0FBQyxLQUFLO1FBQ1YsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDekIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUN4QixDQUFDOzs7O0lBRUQsUUFBUTtJQUNSLENBQUM7Ozs7Ozs7SUFNRCxJQUFJLE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7SUFDekIsQ0FBQzs7Ozs7SUFDRCxJQUFJLE9BQU8sQ0FBQyxLQUFlO1FBQ3ZCLElBQUksQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFDO0lBQzFCLENBQUM7Ozs7O0lBRUQsSUFBSSxLQUFLO1FBQ0wsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDO0lBQ3ZCLENBQUM7Ozs7O0lBQ0QsSUFBSSxLQUFLLENBQUMsS0FBZTtRQUNyQixJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztJQUN4QixDQUFDOzs7OztJQUVELElBQUksS0FBSztRQUNMLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztJQUN2QixDQUFDOzs7OztJQUNELElBQUksS0FBSyxDQUFDLEtBQWU7UUFDckIsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7SUFDeEIsQ0FBQzs7Ozs7SUFFRCxJQUFJLFFBQVE7UUFDUixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7SUFDMUIsQ0FBQzs7Ozs7SUFDRCxJQUFJLFFBQVEsQ0FBQyxLQUFlO1FBQ3hCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO0lBQzNCLENBQUM7Ozs7O0lBRUQsSUFBSSxNQUFNO1FBQ04sT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO0lBQ3hCLENBQUM7Ozs7O0lBQ0QsSUFBSSxNQUFNLENBQUMsS0FBZTtRQUN0QixJQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztJQUN6QixDQUFDOzs7Ozs7SUFFRCxVQUFVLENBQUMsS0FBVTtRQUNqQixJQUFJLElBQUksQ0FBQyxhQUFhLElBQUksS0FBSyxFQUFFO1lBQzdCLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztTQUMvRDthQUNJO1lBQ0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1NBQzdEO0lBQ0wsQ0FBQzs7OztJQUNELGtCQUFrQjtRQUNkLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtRQUMvQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUMvQixDQUFDOzs7WUFyWkosU0FBUyxTQUFDO2dCQUNQLFFBQVEsRUFBRSxZQUFZO2dCQUN0QixRQUFRLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQTJCWDt5QkFDVTs7Ozs7Ozs7Ozs7Ozs7R0FjVjthQUNGOzs7O1lBekQ2RixVQUFVOzs7MEJBNkRuRyxNQUFNLFNBQUMsYUFBYTs0QkFDcEIsTUFBTSxTQUFDLGVBQWU7b0JBK0J0QixLQUFLLFNBQUMsT0FBTztzQkFFYixLQUFLLFNBQUMsU0FBUzt1QkFFZixLQUFLLFNBQUMsVUFBVTtzQkFFaEIsS0FBSyxTQUFDLFNBQVM7c0JBRWYsS0FBSyxTQUFDLFNBQVM7cUJBRWYsTUFBTSxTQUFDLE9BQU87dUJBRWQsTUFBTSxTQUFDLFNBQVM7cUJBRWhCLE1BQU0sU0FBQyxPQUFPO3dCQUVkLE1BQU0sU0FBQyxVQUFVO3NCQUVqQixNQUFNLFNBQUMsUUFBUTt3QkFHZixLQUFLLFNBQUMsV0FBVztzQkFFakIsS0FBSyxTQUFDLFNBQVM7dUJBRWYsS0FBSyxTQUFDLFVBQVU7dUJBRWhCLEtBQUssU0FBQyxVQUFVO3VCQUVoQixLQUFLLFNBQUMsVUFBVTttQ0FFaEIsS0FBSztvQkFVTCxLQUFLO3FCQWFMLEtBQUssWUFHTCxLQUFLLFNBQUMsUUFBUTtpQkFFZCxLQUFLLFNBQUMsSUFBSTtvQkFHVixLQUFLO3NCQWdCTCxLQUFLO3NCQXFCTCxLQUFLO21CQW9CTCxLQUFLO3VCQXdCTCxLQUFLO3VCQWNGLEtBQUssU0FBRSxVQUFVO3lCQWlCcEIsU0FBUyxTQUFDLGNBQWM7Ozs7SUEvTXpCLG1DQUFrQjs7SUFDbEIsaUNBQWdGOztJQUNoRixtQ0FBb0Y7Ozs7O0lBQ3BGLDRDQUEyQzs7Ozs7SUFFM0MsOEJBQWtDOzs7OztJQUVsQyw0QkFBZ0M7Ozs7O0lBRWhDLDRCQUFnQzs7Ozs7SUFFaEMsK0JBQW1DOzs7OztJQUVuQyw2QkFBaUM7Ozs7O0lBRWpDLDhCQUF3Qjs7Ozs7SUFFeEIsK0JBQXlCOzs7OztJQUV6QixtQ0FBMkI7Ozs7O0lBRTNCLGlDQUF5Qjs7Ozs7SUFFekIsMkJBQXNCOzs7OztJQUV0Qiw0QkFBZTs7Ozs7SUFFZiwrQkFBbUM7Ozs7O0lBRW5DLDhCQUE4Qjs7Ozs7SUFFOUIsOEJBQWlDOztJQUVqQywyQkFBc0I7O0lBRXRCLDZCQUEwQjs7SUFFMUIsOEJBQTRCOztJQUU1Qiw2QkFBMEI7O0lBRTFCLDZCQUEwQjs7Ozs7SUFFMUIsNEJBQStEOzs7OztJQUUvRCw4QkFBbUU7Ozs7O0lBRW5FLDRCQUErRDs7Ozs7SUFFL0QsK0JBQXFFOzs7OztJQUVyRSw2QkFBaUU7O0lBR2pFLCtCQUE4Qjs7SUFFOUIsNkJBQTBCOztJQUUxQiw4QkFBNEI7O0lBRTVCLDhCQUE0Qjs7SUFFNUIsOEJBQTRCOztJQXlCNUIsNEJBR2lDOztJQUVqQyx3QkFBZ0I7Ozs7O0lBMkRoQiwrQkFBaUM7Ozs7O0lBd0RqQyxnQ0FBMEQ7Ozs7O0lBQzlDLDBCQUF3QiIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudCwgT25Jbml0LCBJbnB1dCwgVmlld0NoaWxkLCBBZnRlclZpZXdJbml0LCBPbkNoYW5nZXMsIE91dHB1dCwgRXZlbnRFbWl0dGVyLCBFbGVtZW50UmVmLCBDb21wb25lbnRGYWN0b3J5UmVzb2x2ZXIgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuLy9pbXBvcnQgeyBTd3RBYnN0cmFjdCB9IGZyb20gJy4vc3d0LWFic3RyYWN0JztcclxuaW1wb3J0IHsgQ29udGFpbmVyIH0gZnJvbSBcIi4uL2NvbnRhaW5lcnMvc3d0LWNvbnRhaW5lci5jb21wb25lbnRcIjtcclxuXHJcbmltcG9ydCB7IGZvY3VzTWFuYWdlciB9IGZyb20gXCIuLi9tYW5hZ2Vycy9mb2N1cy1tYW5hZ2VyLnNlcnZpY2VcIjtcclxuXHJcblxyXG4vKiBJbXBvcnQgSlF1ZXJ5IGFuZCBKUXVlcnkgdWkgKi9cclxuZGVjbGFyZSB2YXIgcmVxdWlyZTogYW55O1xyXG4vL2ltcG9ydCAkIGZyb20gJ2pxdWVyeSc7XHJcbmNvbnN0ICQgPSByZXF1aXJlKCdqcXVlcnknKTtcclxuXHJcbkBDb21wb25lbnQoe1xyXG4gICAgc2VsZWN0b3I6ICdTd3RTdGVwcGVyJyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICAgICAgICAgICAgPGlucHV0ICNzdGVwcGVyaW5wdXQgXHJcbiAgICAgICAgICAgICAgIHNlbGVjdG9yPVwiU3d0U3RlcHBlclwiXHJcbiAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICB2YWx1ZT1cInt7IHZhbHVlIH19XCJcclxuICAgICAgICAgICAgICAgaWQ9XCJ7eyBpZCB9fVwiXHJcbiAgICAgICAgICAgICAgIG1pbj1cInt7IG1pbmltdW19fVwiICBcclxuICAgICAgICAgICAgICAgbWF4PVwie3sgbWF4aW11bSB9fVwiIFxyXG4gICAgICAgICAgICAgICBzdGVwPVwie3tzdGVwU2l6ZX19XCJcclxuICAgICAgICAgICAgICAgKGtleWRvd24pPVwib25rZXlEb3duKCRldmVudClcIlxyXG4gICAgICAgICAgICAgICAoY2hhbmdlKT1cIm9uQ2hhbmdlKCRldmVudClcIlxyXG4gICAgICAgICAgICAgICAoZm9jdXMpPVwib25Gb2N1cygkZXZlbnQpXCJcclxuICAgICAgICAgICAgICAgKGZvY3Vzb3V0KT1cIm9uRm9jdXNPdXQoJGV2ZW50KVwiXHJcbiAgICAgICAgICAgICAgIChrZXl1cCk9XCJvbmtleXVwKCRldmVudClcIlxyXG4gICAgICAgICAgICAgICBjbGFzcz1cInt7IHN0eWxlTmFtZSB9fVwiXHJcbiAgICAgICAgICAgICAgIFN3dHJlc3RyaWN0PVwiWzAtOV1cIlxyXG4gICAgICAgICAgICAgICB0YWJpbmRleD1cInt7IHRhYmluZGV4IH19XCJcclxuICAgICAgICAgICAgICAgW2F0dHIuZGlzYWJsZWRdPSBcImVuYWJsZWQ/IG51bGw6IHRydWVcIiBcclxuICAgICAgICAgICAgICAgW2F0dHIucmVhZE9ubHldPSBcImVkaXRhYmxlPyBudWxsOiB0cnVlXCJcclxuICAgICAgICAgICAgICAgIHBvcHBlcj1cInt7dGhpcy50b29sVGlwUHJldmlvdXNWYWx1ZX19XCJcclxuICAgICBbcG9wcGVyVHJpZ2dlcl09XCInaG92ZXInXCJcclxuICAgICBbcG9wcGVyRGlzYWJsZWRdPVwidG9vbFRpcFByZXZpb3VzVmFsdWUgPT09IG51bGwgPyB0cnVlIDogZmFsc2VcIlxyXG4gICAgIFtwb3BwZXJQbGFjZW1lbnRdPVwiJ2JvdHRvbSdcIlxyXG4gICAgIFtuZ0NsYXNzXT1cInsnYm9yZGVyLW9yYW5nZS1wcmV2aW91cyc6IHRvb2xUaXBQcmV2aW91c1ZhbHVlICE9IG51bGx9XCJcclxuICAgICAgICAgICAgICAgW2NsYXNzLnJlcXVpcmVkSW5wdXRdPSBcInRoaXMucmVxdWlyZWQ9PXRydWUgJiYgKHRoaXMudGV4dCA9PSBudWxsKSAgJiYgZW5hYmxlZD09dHJ1ZVwiICBcclxuICAgICAgICAgICAgICAgcGxhY2VtZW50PVwidG9wXCI+XHJcbiAgICAgICAgICAgPCEtLTxpbnB1dCBTd3RyZWdleD1cIlswLTldKlwiIG5hbWU9XCJ2YWx1ZVwiPiAgICAtLT5cclxuICBgLFxyXG4gICAgc3R5bGVzOiBbYFxyXG4gICAgICBpbnB1dCB7XHJcbiAgICAgICAgICAvKiBwYWRkaW5nLWxlZnQ6IDJweDtcclxuICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiAxMHB4O1xyXG4gICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICM3ZjlkYjk7IFxyXG4gICAgICAgICAgIHdpZHRoOiAxMDAlOyBcclxuICAgICAgICAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgcGFkZGluZy1yaWdodDogMHB4OyovXHJcbiAgICAgIH1cclxuICAgICAgaW5wdXQ6ZGlzYWJsZWQge1xyXG4gICAgICAgICAgY29sb3I6IGdyYXk7XHJcbiAgICAgIH1cclxuXHJcblxyXG4gIGBdXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBTd3RTdGVwcGVyICBpbXBsZW1lbnRzIE9uSW5pdCwgT25DaGFuZ2VzLCBBZnRlclZpZXdJbml0IHtcclxuXHJcbiAgICBvcmlnaW5hbFZhbHVlOmFueTtcclxuICAgIEBPdXRwdXQoJ29uU3B5Q2hhbmdlJykgb25TcHlDaGFuZ2U6IEV2ZW50RW1pdHRlcjxhbnk+ID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgICBAT3V0cHV0KCdvblNweU5vQ2hhbmdlJykgb25TcHlOb0NoYW5nZTogRXZlbnRFbWl0dGVyPGFueT4gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcclxuICAgIHByaXZhdGUgX3Rvb2xUaXBQcmV2aW91c09iamVjdDogYW55ID0gbnVsbDtcclxuICAgIC8qcHJpdmF0ZSB2YXJpYWJsZSB0byBob2xkIGtleSBkb3duIGV2ZW50ICovXHJcbiAgICBwcml2YXRlIF9rZXlEb3duID0gbmV3IEZ1bmN0aW9uKCk7XHJcbiAgICAvKnByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBrZXkgZG93biBldmVudCAqL1xyXG4gICAgcHJpdmF0ZSBfa2V5VXAgPSBuZXcgRnVuY3Rpb24oKTtcclxuICAgIC8qIHByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBmb2N1cyBldmVudCAqL1xyXG4gICAgcHJpdmF0ZSBfZm9jdXMgPSBuZXcgRnVuY3Rpb24oKTtcclxuICAgIC8qIHByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBmb2N1cyBvdXQgZXZlbnQgKi9cclxuICAgIHByaXZhdGUgX2ZvY3VzT3V0ID0gbmV3IEZ1bmN0aW9uKCk7XHJcbiAgICAvKiBwcml2YXRlIHZhcmlhYmxlIHRvIGhvbGQgY2huYWdlIGV2ZW50ICovXHJcbiAgICBwcml2YXRlIF9jaGFuZ2UgPSBuZXcgRnVuY3Rpb24oKTtcclxuICAgIC8qIFByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBlbmFibGVkIHByb3BlcnR5ICovXHJcbiAgICBwcml2YXRlIF9lbmFibGVkID0gdHJ1ZTtcclxuICAgIC8qIFByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBlZGl0YWJsZSBwcm9wZXJ0eSAqL1xyXG4gICAgcHJpdmF0ZSBfZWRpdGFibGUgPSB0cnVlO1xyXG4gICAgLy8gcHJpdmF0ZSB2YXJpYWJsZSB0byBob2xkIHNwaW5uZXIgb2JqZWN0XHJcbiAgICBwcml2YXRlIHNwaW5uZXJPYmplY3Q6IGFueTtcclxuICAgIC8vIHByaXZhdGUgdmFyaWFibGUgdG8gaG9sZCBzcGlubmVyIGlucHV0XHJcbiAgICBwcml2YXRlIHNwaW5uZXJJbnV0OiBhbnk7XHJcbiAgICAvLyB2YXJpYWJsZSB0byBoYW5kbGUgc3RlcHBlciB0ZXh0XHJcbiAgICBwcml2YXRlIF90ZXh0OiBudW1iZXI7XHJcbiAgICAvLyB2YXJpYWJsZSB0byBob2xkIHdpZHRoLlxyXG4gICAgcHJpdmF0ZSBfd2lkdGg7XHJcblxyXG4gICAgcHJpdmF0ZSBfcmVxdWlyZWQ6IGJvb2xlYW4gPSBmYWxzZTtcclxuXHJcbiAgICBwcml2YXRlIHNlbGVjdG9yPVwiU3d0U3RlcHBlclwiO1xyXG4gICAgLy8gcHJpdmF0ZSB2YXJpYWJsZSB0byBoYW5kbGUgY29tcG9uZW50IHZpc2liaWxpdHkuXHJcbiAgICBwcml2YXRlIF92aXNpYmxlOiBib29sZWFuID0gdHJ1ZTtcclxuICAgIC8qIElucHV0IHRvIHNldCBtaW5pbXVtICovXHJcbiAgICBASW5wdXQoJ3JlZ2V4JykgcmVnZXg7XHJcbiAgICAvKiBJbnB1dCB0byBzZXQgbWF4aW11bSAqL1xyXG4gICAgQElucHV0KCdwYXR0ZXJuJykgcGF0dGVybjtcclxuICAgIC8qIElucHV0IHRvIHNldCBvbkNoYW5nZSAqL1xyXG4gICAgQElucHV0KCdyZXN0cmljdCcpIHJlc3RyaWN0O1xyXG4gICAgLyogSW5wdXQgdG8gc2V0IG1pbmltdW0gKi9cclxuICAgIEBJbnB1dCgnbWluaW11bScpIG1pbmltdW07XHJcbiAgICAvKiBJbnB1dCB0byBzZXQgbWF4aW11bSAqL1xyXG4gICAgQElucHV0KCdtYXhpbXVtJykgbWF4aW11bTtcclxuICAgIC8qIHRvIGhhbmRsZSBrZXl1cCBldmVudCAqL1xyXG4gICAgQE91dHB1dCgna2V5VXAnKSBwcml2YXRlIGtleXVwXyA9IG5ldyBFdmVudEVtaXR0ZXI8RnVuY3Rpb24+KCk7XHJcbiAgICAvKiBJbnB1dCB0byBoYW5kbGUga2V5RG9udyBldmVudCAqL1xyXG4gICAgQE91dHB1dCgna2V5RG93bicpIHByaXZhdGUga2V5RG93bl8gPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgLyogSW5wdXQgdG8gaGFuZGxlIGZvY3VzIGV2ZW50ICovXHJcbiAgICBAT3V0cHV0KCdmb2N1cycpIHByaXZhdGUgZm9jdXNfID0gbmV3IEV2ZW50RW1pdHRlcjxGdW5jdGlvbj4oKTtcclxuICAgIC8qIElucHV0IHRvIGhhbmRsZSBmb2N1cyBvdXQgZXZlbnQgKi9cclxuICAgIEBPdXRwdXQoJ2ZvY3VzT3V0JykgcHJpdmF0ZSBmb2N1c091dF8gPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgLyogSW5wdXQgdG8gaGFuZGxlIGNoYW5nZSBldmVudCAqL1xyXG4gICAgQE91dHB1dCgnY2hhbmdlJykgcHJpdmF0ZSBjaGFuZ2VfID0gbmV3IEV2ZW50RW1pdHRlcjxGdW5jdGlvbj4oKTtcclxuXHJcbiAgICAvKiBJbnB1dCB0byBob2xkIGlucHV0IHN0eWxlTmFtZSAqL1xyXG4gICAgQElucHV0KCdzdHlsZU5hbWUnKSBzdHlsZU5hbWU7XHJcbiAgICAvKiBJbnB1dCB0byBob2xkIG51bWVyaWMgaW5wdXQgdG9vbFRpcCovXHJcbiAgICBASW5wdXQoJ3Rvb2xUaXAnKSB0b29sVGlwO1xyXG4gICAgLyogSW5wdXQgdG8gaG9sZCBudW1lcmljIGlucHV0IG1heENoYXJzICovXHJcbiAgICBASW5wdXQoJ21heENoYXJzJykgbWF4Q2hhcnM7XHJcbiAgICAvKiBJbnB1dCB0byBob2xkIG51bWVyaWMgaW5wdXQgdGFiSW5kZXggKi9cclxuICAgIEBJbnB1dCgndGFiSW5kZXgnKSB0YWJpbmRleDtcclxuICAgIC8qIHdpZHRoIGdldHRlciBhbmQgc2V0dGVyICovXHJcbiAgICBASW5wdXQoXCJzdGVwU2l6ZVwiKSBzdGVwU2l6ZTtcclxuICAgIFxyXG4gICAgQElucHV0KClcclxuICAgIHNldCB0b29sVGlwUHJldmlvdXNWYWx1ZSh2YWx1ZSkge1xyXG4gICAgICAgIHRoaXMuX3Rvb2xUaXBQcmV2aW91c09iamVjdCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIGdldCB0b29sVGlwUHJldmlvdXNWYWx1ZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdG9vbFRpcFByZXZpb3VzT2JqZWN0O1xyXG4gICAgfVxyXG5cclxuICAgIFxyXG4gICAgQElucHV0KClcclxuICAgIHNldCB3aWR0aCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgaWYgKHZhbHVlICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlLmluZGV4T2YoJyUnKSA9PT0gLTEpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3dpZHRoID0gdmFsdWUgKyBcInB4XCI7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl93aWR0aCA9IHZhbHVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0IHdpZHRoKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl93aWR0aDtcclxuICAgIH1cclxuICAgIEBJbnB1dCgpXHJcblxyXG4gICAgLyogQ29tcG9uZW50IHRvIGhvbGQgY29tcG9uZW50IGhlaWdodCAqL1xyXG4gICAgQElucHV0KCdoZWlnaHQnKSBoZWlnaHQgPSBcIjIycHhcIjtcclxuICAgIC8qIElucHV0IHRvIGhvbGQgbnVtZXJpYyBpbnB1dCBpZCAqL1xyXG4gICAgQElucHV0KCdpZCcpIGlkO1xyXG4gICAgLyogSW5wdXQgdG8gaG9sZCBudW1lcmljIGlucHV0IGlkICovXHJcbiAgICAvL3RleHQgZ2V0dGVyIGFuZCBzZXR0ZXJcclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgdmFsdWUodmFsdWU6IG51bWJlcikge1xyXG5cclxuICAgICAgICB0aGlzLnRleHQgPSB2YWx1ZTtcclxuICAgICAgICBpZiAodGhpcy5zcGlubmVyT2JqZWN0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIoXCJ2YWx1ZVwiLCBOdW1iZXIodmFsdWUpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBnZXQgdmFsdWUoKSB7XHJcbiAgICAgICAgbGV0IHJ0bnRleHQgPSB0aGlzLl90ZXh0O1xyXG4gICAgICAgIGlmICh0aGlzLnNwaW5uZXJPYmplY3QgJiYgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIpIHtcclxuICAgICAgICAgICAgcnRudGV4dCA9IHRoaXMuc3Bpbm5lck9iamVjdC5zcGlubmVyKCkuc3Bpbm5lcihcInZhbHVlXCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcnRudGV4dDtcclxuICAgIH1cclxuICAgIC8vZW5hYmxlZCBnZXR0ZXIgYW5kIHNldHRlci5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgZW5hYmxlZCh2YWx1ZTogYm9vbGVhbikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSAhPT0gJ3N0cmluZycpIHtcclxuICAgICAgICAgICAgdGhpcy5fZW5hYmxlZCA9IHZhbHVlO1xyXG4gICAgICAgICAgICBpZiAodGhpcy5zcGlubmVyT2JqZWN0KSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNwaW5uZXJPYmplY3Quc3Bpbm5lciggXCJvcHRpb25cIiwgXCJkaXNhYmxlZFwiLCAhdGhpcy5fZW5hYmxlZCApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlICsgJycgPT09ICd0cnVlJykge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHRoaXMuc3Bpbm5lck9iamVjdCkge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIoIFwib3B0aW9uXCIsIFwiZGlzYWJsZWRcIiwgIXRoaXMuX2VuYWJsZWQgKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCBlbmFibGVkKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9lbmFibGVkO1xyXG4gICAgfVxyXG4gICAgQElucHV0KClcclxuICAgIHNldCB2aXNpYmxlKHZhbHVlOiBib29sZWFuKSB7XHJcbiAgICAgICAgaWYgKHR5cGVvZiAodmFsdWUpICE9PSAnc3RyaW5nJykge1xyXG4gICAgICAgICAgICB0aGlzLl9lbmFibGVkID0gdmFsdWU7XHJcbiAgICAgICAgICAgIHZhbHVlID8gJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuc2hvdygpIDogJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuaGlkZSgpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmICh2YWx1ZSArICcnID09PSAndHJ1ZScpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3Zpc2libGUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuc2hvdygpXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl92aXNpYmxlID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5oaWRlKClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCB2aXNpYmxlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl92aXNpYmxlO1xyXG4gICAgfVxyXG4gICAgLy90ZXh0IGdldHRlciBhbmQgc2V0dGVyXHJcbiAgICBwcml2YXRlIGZpcnN0Q2FsbDogYm9vbGVhbiA9IHRydWVcclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgdGV4dCh2YWx1ZTogbnVtYmVyKSB7XHJcbiAgICAgICAgdGhpcy5fdGV4dCA9IHZhbHVlO1xyXG4gICAgICAgIGlmICh0aGlzLnNwaW5uZXJPYmplY3QgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICB0aGlzLnNwaW5uZXJPYmplY3Quc3Bpbm5lcihcInZhbHVlXCIsIE51bWJlcih2YWx1ZSkpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodGhpcy5maXJzdENhbGwpIHtcclxuICAgICAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gdmFsdWU7XHJcbiAgICAgICAgICAgIHRoaXMuZmlyc3RDYWxsID0gZmFsc2U7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuc3B5Q2hhbmdlcyh2YWx1ZSk7XHJcbiAgICB9XHJcbiAgICBnZXQgdGV4dCgpIHtcclxuICAgICAgICBsZXQgcnRudGV4dCA9IHRoaXMuX3RleHQ7XHJcbiAgICAgICAgaWYgKHRoaXMuc3Bpbm5lck9iamVjdCAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIHJ0bnRleHQgPSB0aGlzLnNwaW5uZXJPYmplY3Quc3Bpbm5lcihcInZhbHVlXCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcnRudGV4dDtcclxuICAgIH1cclxuICAgIC8qIGVkaXRhYmxlIGdldHRlciBhbmQgc2V0dGVyICovXHJcbiAgICBnZXQgZWRpdGFibGUoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2VkaXRhYmxlO1xyXG4gICAgfVxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgZWRpdGFibGUodmFsdWU6IGJvb2xlYW4pIHtcclxuICAgICAgICBpZiAodHlwZW9mICh2YWx1ZSkgIT09ICdzdHJpbmcnKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX2VkaXRhYmxlID0gdmFsdWU7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlICsgJycgPT09ICd0cnVlJykge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZWRpdGFibGUgPSB0cnVlO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZWRpdGFibGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAgICAvKiBpbnB1dCB0byBob2xkIGNvbXBvbmVudCB2aXNpYmlsaXR5ICovXHJcbiAgICAgICBASW5wdXQoICdyZXF1aXJlZCcgKVxyXG4gICAgICAgc2V0IHJlcXVpcmVkKCB2YWx1ZTogYW55ICkge1xyXG4gICAgICAgICAgIGlmKHR5cGVvZih2YWx1ZSkgPT0gXCJzdHJpbmdcIil7XHJcbiAgICAgICAgICAgICAgIGlmKHZhbHVlPT09J3RydWUnKXtcclxuICAgICAgICAgICAgICAgICAgIHRoaXMuX3JlcXVpcmVkID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgfWVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgdGhpcy5fcmVxdWlyZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgIH1lbHNlIHtcclxuICAgICAgICAgICAgICAgdGhpcy5fcmVxdWlyZWQgPSB2YWx1ZTtcclxuICAgICAgICAgICB9XHJcbiAgICAgICB9XHJcblxyXG4gICAgICAgZ2V0IHJlcXVpcmVkKCkge1xyXG4gICAgICAgICAgIHJldHVybiB0aGlzLl9yZXF1aXJlZDtcclxuICAgICAgIH1cclxuICAgIC8qIFZpZXdDaGlsZCB0byBnZXQgRE9NIG9mIGNvbXBvbmVudCAqL1xyXG4gICAgQFZpZXdDaGlsZCgnc3RlcHBlcmlucHV0JykgcHJpdmF0ZSBzdGVwcGVyRE9NOiBFbGVtZW50UmVmO1xyXG4gICAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtOiBFbGVtZW50UmVmICkge1xyXG4gICAgfVxyXG5cclxuICAgIG5nT25Jbml0KCkge1xyXG5cclxuICAgICAgICB0aGlzLnNwaW5uZXJPYmplY3QgPSAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKTtcclxuLy8gICAgICAgICQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudClbMF0pLnJlcGxhY2VXaXRoKHRoaXMuc3Bpbm5lck9iamVjdCk7XHJcbiAgICAgICAgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIoe1xyXG4gICAgICAgICAgICBtaW46IHRoaXMubWluaW11bSxcclxuICAgICAgICAgICAgbWF4OiB0aGlzLm1heGltdW0sXHJcbiAgICAgICAgICAgIHN0ZXA6dGhpcy5zdGVwU2l6ZSxcclxuICAgICAgICAgICAgc3BpbjogKGV2ZW50LCB1aSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zcHlDaGFuZ2VzKHVpLnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VfLmVtaXQoZXZlbnQpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAvLyAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS53aWR0aCh0aGlzLndpZHRoKTtcclxuICAgICAgICAvLyBzZXQgZW5hYmxlIC8gZGlzYWJsZSBwcm9wZXJ0eVxyXG4gICAgICAgIGlmICh0aGlzLmVuYWJsZWQgPT09IHRydWUpIHtcclxuICAgICAgICAgICAgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIoXCJlbmFibGVcIik7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhpcy5zcGlubmVyT2JqZWN0LnNwaW5uZXIoe1xyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ6IHRydWVcclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgJCh0aGlzLnNwaW5uZXJPYmplY3QpLndpZHRoKHRoaXMud2lkdGgpO1xyXG4gICAgICAgIC8vLVNUQVJULSBzZXQgZGVmYXVsdCB0ZXh0IGlmIG5vdCBudWxsIG9yIGVtcHR5XHJcbiAgICAgICAgaWYgKHRoaXMuX3RleHQgIT09IG51bGwgfHwgdGhpcy5fdGV4dCAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuc3Bpbm5lck9iamVjdC5zcGlubmVyKFwidmFsdWVcIiwgdGhpcy5fdGV4dCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIEFkZGVkIGJ5IFJpaGFiLkogQDA3LzEyLzIwMTggLSBuZWVkZWQgdG8gYmUgdXNlZCBpbiBkeW5hbWljYWxseSBhZGRlZCBTd3RTdGVwcGVyLlxyXG4gICAgICAgICQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoJ3NlbGVjdG9yJywnU3d0U3RlcHBlcicpXHJcbiAgICAgICAgLy8gc2V0IGlkIHRvIGJ1dHRvbiBET00uXHJcbiAgICAgICAgaWYoIHRoaXMuaWQpIHtcclxuICAgICAgICAgICAkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpWzBdKS5hdHRyKFwiaWRcIiwgdGhpcy5pZCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vLUVORC1cclxuICAgIH1cclxuICAgIG5nQWZ0ZXJWaWV3SW5pdCgpIHtcclxuICAgICAgICBjb25zdCBwb3BwZXJDb250ZW50RWwgPSB0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5xdWVyeVNlbGVjdG9yKCdwb3BwZXItY29udGVudCcpO1xyXG4gICAgICAgIGlmKHBvcHBlckNvbnRlbnRFbClcclxuICAgICAgICAgICAgdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuYXBwZW5kQ2hpbGQocG9wcGVyQ29udGVudEVsKTtcclxuXHJcbiAgICB9XHJcbiAgICBwcml2YXRlIHVwZGF0ZVRleHQoZXZlbnQpIHtcclxuICAgICAgICB0aGlzLnZhbHVlID0gZXZlbnQudGFyZ2V0LnZhbHVlO1xyXG4gICAgfVxyXG4gICAgbmdPbkNoYW5nZXMoY2hhbmdlcyk6IHZvaWQge1xyXG5cclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogdGhpcyBmdW5jdGlvbiBpcyBmaXJlZCBvbiBrZXl1cCBldmVudC5cclxuICAgICAqIEBwYXJhbSBldmVudFxyXG4gICAgICovXHJcbiAgICBvbmtleXVwKGV2ZW50KSB7XHJcbiAgICAgICAgdGhpcy5rZXl1cF8uZW1pdChldmVudCk7XHJcbiAgICAgICAgdGhpcy5fa2V5VXAoZXZlbnQpO1xyXG4gICAgICAgIGlmICgodGhpcy52YWx1ZSAhPT0gZXZlbnQudGFyZ2V0LnZhbHVlKSAmJiB0aGlzLnZhbHVlICE9IG51bGwpIHtcclxuICAgICAgICAgICAgdGhpcy52YWx1ZSA9IGV2ZW50LnRhcmdldC52YWx1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICB0aGlzLnNweUNoYW5nZXMoZXZlbnQudGFyZ2V0LnZhbHVlKVxyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiB0aGlzIGZ1bmN0aW9uIGlzIGZpcmVkIG9uIGtleXVwIGV2ZW50LlxyXG4gICAgICogQHBhcmFtIGV2ZW50XHJcbiAgICAgKi9cclxuICAgIG9ua2V5RG93bihldmVudCkge1xyXG4gICAgICAgIHRoaXMua2V5RG93bl8uZW1pdChldmVudCk7XHJcbiAgICAgICAgdGhpcy5fa2V5RG93bihldmVudCk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIHRoaXMgZnVuY3Rpb24gaXMgZmlyZWQgb24gZm9jdXMgZXZlbnQuXHJcbiAgICAgKiBAcGFyYW0gZXZlbnRcclxuICAgICAqL1xyXG4gICAgb25Gb2N1cyhldmVudCkge1xyXG4gICAgICAgIHRoaXMuZm9jdXNfLmVtaXQoZXZlbnQpO1xyXG4gICAgICAgIHRoaXMuX2ZvY3VzKGV2ZW50KTtcclxuICAgICAgICBmb2N1c01hbmFnZXIuZm9jdXNUYXJnZXQgPSB0aGlzLmlkO1xyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiB0aGlzIGZ1bmN0aW9uIGlzIGZpcmVkIG9uIGZvY3VzT3V0IGV2ZW50LlxyXG4gICAgICogQHBhcmFtIGV2ZW50XHJcbiAgICAgKi9cclxuICAgIG9uRm9jdXNPdXQoZXZlbnQpIHtcclxuICAgICAgICB0aGlzLmZvY3VzT3V0Xy5lbWl0KGV2ZW50KTtcclxuICAgICAgICB0aGlzLl9mb2N1c091dChldmVudCk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIHRoaXMgZnVuY3Rpb24gaXMgZmlyZWQgb24gY2hhbmdlIGV2ZW50LlxyXG4gICAgICogQHBhcmFtIGV2ZW50XHJcbiAgICAgKi9cclxuICAgIG9uQ2hhbmdlKGV2ZW50KSB7XHJcbiAgICAgICAgdGhpcy5jaGFuZ2VfLmVtaXQoZXZlbnQpO1xyXG4gICAgICAgIHRoaXMuX2NoYW5nZShldmVudCk7XHJcbiAgICB9XHJcblxyXG4gICAgc2V0Rm9jdXMoKSB7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKi9cclxuICAgIC8qICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEdFVFRFUiBBTkQgU0VUVEVSICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICovXHJcbiAgICAvKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqL1xyXG5cclxuICAgIGdldCBrZXlEb3duKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9rZXlEb3duO1xyXG4gICAgfVxyXG4gICAgc2V0IGtleURvd24odmFsdWU6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fa2V5RG93biA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBnZXQga2V5VXAoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2tleVVwO1xyXG4gICAgfVxyXG4gICAgc2V0IGtleVVwKHZhbHVlOiBGdW5jdGlvbikge1xyXG4gICAgICAgIHRoaXMuX2tleVVwID0gdmFsdWU7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIGdldCBmb2N1cygpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZm9jdXM7XHJcbiAgICB9XHJcbiAgICBzZXQgZm9jdXModmFsdWU6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fZm9jdXMgPSB2YWx1ZTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgZ2V0IGZvY3VzT3V0KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9mb2N1c091dDtcclxuICAgIH1cclxuICAgIHNldCBmb2N1c091dCh2YWx1ZTogRnVuY3Rpb24pIHtcclxuICAgICAgICB0aGlzLl9mb2N1c091dCA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBnZXQgY2hhbmdlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9jaGFuZ2U7XHJcbiAgICB9XHJcbiAgICBzZXQgY2hhbmdlKHZhbHVlOiBGdW5jdGlvbikge1xyXG4gICAgICAgIHRoaXMuX2NoYW5nZSA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgLy8gYWRkZWQgYnkgS2hhbGlsLkIgc3B5IG1ldGhvZFxyXG4gICAgc3B5Q2hhbmdlcyhldmVudDogYW55KTogdm9pZCB7XHJcbiAgICAgICAgaWYgKHRoaXMub3JpZ2luYWxWYWx1ZSA9PSBldmVudCkge1xyXG4gICAgICAgICAgICB0aGlzLm9uU3B5Tm9DaGFuZ2UuZW1pdCh7IFwidGFyZ2V0XCI6IHRoaXMsIFwidmFsdWVcIjogZXZlbnQgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLm9uU3B5Q2hhbmdlLmVtaXQoeyBcInRhcmdldFwiOiB0aGlzLCBcInZhbHVlXCI6IGV2ZW50IH0pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJlc2V0T3JpZ2luYWxWYWx1ZSgpIHtcclxuICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWUgPSB0aGlzLl90ZXh0XHJcbiAgICAgICAgdGhpcy5zcHlDaGFuZ2VzKHRoaXMuX3RleHQpXHJcbiAgICB9XHJcbn1cclxuIl19