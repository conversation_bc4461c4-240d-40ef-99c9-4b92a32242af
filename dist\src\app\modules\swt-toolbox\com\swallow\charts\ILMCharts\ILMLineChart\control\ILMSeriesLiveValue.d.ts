import { On<PERSON>nit, ElementRef, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
export declare class ILMSeriesLiveValue extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    private cd;
    protected hboxContainer: ElementRef;
    protected circle: ElementRef;
    protected labelValue: ElementRef;
    private styleClassMap;
    private _seriesValue;
    private _seriesId;
    private _seriesStyle;
    private _isTimeLiveItem;
    seriesStyle: any;
    seriesId: any;
    seriesValue: string;
    ngOnInit(): void;
    isTimeLiveItem: any;
    removeLiveValue(): void;
    constructor(elem: ElementRef, commonService: CommonService, cd: ChangeDetectorRef);
}
