export declare class ContextMenu {
    components: any;
    customItems: ContextMenuItem[];
    constructor();
    hideBuiltInItems(): void;
}
export declare class ContextMenuItem {
    private var1?;
    private var2?;
    private var3?;
    private itemDOM;
    label: string;
    private _MenuItemSelect;
    MenuItemSelect: Function;
    constructor(label: string, var1?: boolean, var2?: boolean, var3?: boolean);
}
