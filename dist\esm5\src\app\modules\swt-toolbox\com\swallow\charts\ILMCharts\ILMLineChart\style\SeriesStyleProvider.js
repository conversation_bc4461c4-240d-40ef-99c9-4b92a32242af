/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { SeriesStyle } from './SeriesStyle';
/**
 * This class contain all module events
 */
// @dynamic
var SeriesStyleProvider = /** @class */ (function () {
    /**
     * Private constructor: initialize styles
     * */
    function SeriesStyleProvider() {
        this.allStyles = [];
        //Cont Blizzard Blue
        /** @type {?} */
        var contAreaBlizardBlue = new SeriesStyle(SeriesStyleProvider.CONT_AREA_BLIZZARD_BLUE, /*ExternalInterface.call('eval','CONT_AREA_BLIZZARD_BLUE')*/ 'Area Blizzed Blue', SeriesStyleProvider.AREA_SOLID);
        contAreaBlizardBlue.color = '#9BDDE8';
        this.allStyles[SeriesStyleProvider.CONT_AREA_BLIZZARD_BLUE] = contAreaBlizardBlue;
        //Cont Cotton Candy
        /** @type {?} */
        var contAreaCottonCandy = new SeriesStyle(SeriesStyleProvider.CONT_AREA_COTTON_CANDY, 'Area Cotton Candy', SeriesStyleProvider.AREA_SOLID);
        contAreaCottonCandy.color = '#FFB7CD';
        this.allStyles[SeriesStyleProvider.CONT_AREA_COTTON_CANDY] = contAreaCottonCandy;
        /** @type {?} */
        var contAreaBlack = new SeriesStyle(SeriesStyleProvider.CONT_AREA_BLACK, 'Black Area', SeriesStyleProvider.AREA_SOLID);
        contAreaBlack.color = '#00000';
        this.allStyles[SeriesStyleProvider.CONT_AREA_BLACK] = contAreaBlack;
        /** @type {?} */
        var contAreaGrey = new SeriesStyle(SeriesStyleProvider.CONT_AREA_GREY, 'Grey Area', SeriesStyleProvider.AREA_SOLID);
        contAreaGrey.color = '#808080';
        this.allStyles[SeriesStyleProvider.CONT_AREA_GREY] = contAreaGrey;
        /** @type {?} */
        var contAreaLightGrey = new SeriesStyle(SeriesStyleProvider.CONT_AREA_LIGHT_GREY, 'Light Grey Area', SeriesStyleProvider.AREA_SOLID);
        contAreaLightGrey.color = '#D3D3D3';
        this.allStyles[SeriesStyleProvider.CONT_AREA_LIGHT_GREY] = contAreaLightGrey;
        /** @type {?} */
        var contAreaRed = new SeriesStyle(SeriesStyleProvider.CONT_AREA_RED, 'Red Area', SeriesStyleProvider.AREA_SOLID);
        contAreaRed.color = '#FF0000';
        this.allStyles[SeriesStyleProvider.CONT_AREA_RED] = contAreaRed;
        /** @type {?} */
        var contAreaIndianRed = new SeriesStyle(SeriesStyleProvider.CONT_AREA_INDIAN_RED, 'Red Indian Area', SeriesStyleProvider.AREA_SOLID);
        contAreaIndianRed.color = '#FCD5C5C';
        this.allStyles[SeriesStyleProvider.CONT_AREA_INDIAN_RED] = contAreaIndianRed;
        /** @type {?} */
        var contAreaPink = new SeriesStyle(SeriesStyleProvider.CONT_AREA_PINK, 'Pink Area', SeriesStyleProvider.AREA_SOLID);
        contAreaPink.color = '#FFC0CB';
        this.allStyles[SeriesStyleProvider.CONT_AREA_PINK] = contAreaPink;
        /** @type {?} */
        var contAreaOrange = new SeriesStyle(SeriesStyleProvider.CONT_AREA_ORANGE, 'Orange Area', SeriesStyleProvider.AREA_SOLID);
        contAreaOrange.color = '#FFA500';
        this.allStyles[SeriesStyleProvider.CONT_AREA_ORANGE] = contAreaOrange;
        /** @type {?} */
        var contAreaPeachPuff = new SeriesStyle(SeriesStyleProvider.CONT_AREA_PEACH_PUFF, 'Peach Puff Area', SeriesStyleProvider.AREA_SOLID);
        contAreaPeachPuff.color = '#FFDAB9';
        this.allStyles[SeriesStyleProvider.CONT_AREA_PEACH_PUFF] = contAreaPeachPuff;
        /** @type {?} */
        var contAreaYellow = new SeriesStyle(SeriesStyleProvider.CONT_AREA_YELLOW, 'Yellow Area', SeriesStyleProvider.AREA_SOLID);
        contAreaYellow.color = '#FFFF00';
        this.allStyles[SeriesStyleProvider.CONT_AREA_YELLOW] = contAreaYellow;
        /** @type {?} */
        var contAreaGreen = new SeriesStyle(SeriesStyleProvider.CONT_AREA_GREEN, 'Green Area', SeriesStyleProvider.AREA_SOLID);
        contAreaGreen.color = '#008000';
        this.allStyles[SeriesStyleProvider.CONT_AREA_GREEN] = contAreaGreen;
        /** @type {?} */
        var contAreaLime = new SeriesStyle(SeriesStyleProvider.CONT_AREA_LIME, 'Lime Area', SeriesStyleProvider.AREA_SOLID);
        contAreaLime.color = '#00ff00';
        this.allStyles[SeriesStyleProvider.CONT_AREA_LIME] = contAreaLime;
        /** @type {?} */
        var contAreaLightGreen = new SeriesStyle(SeriesStyleProvider.CONT_AREA_LIGHT_GREEN, 'Light Green Area', SeriesStyleProvider.AREA_SOLID);
        contAreaLightGreen.color = '#90EE90';
        this.allStyles[SeriesStyleProvider.CONT_AREA_LIGHT_GREEN] = contAreaLightGreen;
        /** @type {?} */
        var contAreaBlue = new SeriesStyle(SeriesStyleProvider.CONT_AREA_BLUE, 'Blue Area', SeriesStyleProvider.AREA_SOLID);
        contAreaBlue.color = '#0000FF';
        this.allStyles[SeriesStyleProvider.CONT_AREA_BLUE] = contAreaBlue;
        /** @type {?} */
        var contAreaSteelBlue = new SeriesStyle(SeriesStyleProvider.CONT_AREA_STEEL_BLUE, 'Steel Blue Area', SeriesStyleProvider.AREA_SOLID);
        contAreaSteelBlue.color = '#4682B4';
        this.allStyles[SeriesStyleProvider.CONT_AREA_STEEL_BLUE] = contAreaSteelBlue;
        /** @type {?} */
        var contAreaLightBlue = new SeriesStyle(SeriesStyleProvider.CONT_AREA_LIGHT_BLUE, 'Light Blue Area', SeriesStyleProvider.AREA_SOLID);
        contAreaLightBlue.color = '#ADD8E6';
        this.allStyles[SeriesStyleProvider.CONT_AREA_LIGHT_BLUE] = contAreaLightBlue;
        /** @type {?} */
        var contAreaLightCyan = new SeriesStyle(SeriesStyleProvider.CONT_AREA_LIGHT_CYAN, 'Light Cyan Area', SeriesStyleProvider.AREA_SOLID);
        contAreaLightCyan.color = '#E0FFFF';
        this.allStyles[SeriesStyleProvider.CONT_AREA_LIGHT_CYAN] = contAreaLightCyan;
        /** @type {?} */
        var contAreaMagenta = new SeriesStyle(SeriesStyleProvider.CONT_AREA_MAGENTA, 'Magenta Area', SeriesStyleProvider.AREA_SOLID);
        contAreaMagenta.color = '#FF00FF';
        this.allStyles[SeriesStyleProvider.CONT_AREA_MAGENTA] = contAreaMagenta;
        /** @type {?} */
        var contAreaPurple = new SeriesStyle(SeriesStyleProvider.CONT_AREA_PURPLE, 'Purple Area', SeriesStyleProvider.AREA_SOLID);
        contAreaPurple.color = '#800080';
        this.allStyles[SeriesStyleProvider.CONT_AREA_PURPLE] = contAreaPurple;
        //Cont Violet
        /** @type {?} */
        var contAreaViolet = new SeriesStyle(SeriesStyleProvider.CONT_AREA_VIOLET, 'Violet Area', SeriesStyleProvider.AREA_SOLID);
        contAreaViolet.color = '#EE82EE';
        this.allStyles[SeriesStyleProvider.CONT_AREA_VIOLET] = contAreaViolet;
        /** @type {?} */
        var contAreaAntiqueWhite = new SeriesStyle(SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE, 'Antique White Area', SeriesStyleProvider.AREA_SOLID);
        contAreaAntiqueWhite.color = '#FDEBDD';
        this.allStyles[SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE] = contAreaAntiqueWhite;
        /** @type {?} */
        var contAreaApricotPeach = new SeriesStyle(SeriesStyleProvider.CONT_AREA_APRICOT_PEACH, 'Apricot Peach Area', SeriesStyleProvider.AREA_SOLID);
        contAreaApricotPeach.color = '#FBD7BB';
        this.allStyles[SeriesStyleProvider.CONT_AREA_APRICOT_PEACH] = contAreaApricotPeach;
        /** @type {?} */
        var contAreaNavajoWhite = new SeriesStyle(SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE, 'Navajo White Area', SeriesStyleProvider.AREA_SOLID);
        contAreaNavajoWhite.color = '#F9C499';
        this.allStyles[SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE] = contAreaNavajoWhite;
        /** @type {?} */
        var contAreaRoseFog = new SeriesStyle(SeriesStyleProvider.CONT_AREA_ROSE_FOG, 'Rose Fog Area', SeriesStyleProvider.AREA_SOLID);
        contAreaRoseFog.color = '#E5B9B5';
        this.allStyles[SeriesStyleProvider.CONT_AREA_ROSE_FOG] = contAreaRoseFog;
        /** @type {?} */
        var contAreaDarkSalmon = new SeriesStyle(SeriesStyleProvider.CONT_AREA_DARK_SALMON, 'Dark Salmon Area', SeriesStyleProvider.AREA_SOLID);
        contAreaDarkSalmon.color = '#D99690';
        this.allStyles[SeriesStyleProvider.CONT_AREA_DARK_SALMON] = contAreaDarkSalmon;
        //Dashed Area Perano
        /** @type {?} */
        var dashedAreaPerano = new SeriesStyle(SeriesStyleProvider.DASHED_AREA_PERANO, 'Dashed Area Perano', SeriesStyleProvider.AREA_DASHED45, "a.png");
        dashedAreaPerano.form = "segment";
        dashedAreaPerano.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_AREA_PERANO] = dashedAreaPerano;
        //Dashed Coral Area 
        /** @type {?} */
        var dashedAreaCoral = new SeriesStyle(SeriesStyleProvider.DASHED_CORAL_AREA, 'Dashed Coral Area', SeriesStyleProvider.AREA_DASHED45, "b.png");
        dashedAreaCoral.form = "segment";
        dashedAreaCoral.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_CORAL_AREA] = dashedAreaCoral;
        //Dashed Blue Area 
        /** @type {?} */
        var dashedAreaBlue = new SeriesStyle(SeriesStyleProvider.DASHED_BLUE_AREA, 'Dashed Blue Area', SeriesStyleProvider.AREA_DASHED45, "blue_45.png");
        dashedAreaBlue.form = "segment";
        dashedAreaBlue.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_BLUE_AREA] = dashedAreaBlue;
        //Dashed Aqua Area 
        /** @type {?} */
        var dashedAreaAqua = new SeriesStyle(SeriesStyleProvider.DASHED_AQUA_AREA, 'Dashed Aqua Area', SeriesStyleProvider.AREA_DASHED45, "aqua_45.png");
        dashedAreaAqua.form = "segment";
        dashedAreaAqua.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_AQUA_AREA] = dashedAreaAqua;
        //Dashed Deep Pink Area 
        /** @type {?} */
        var dashedAreaDeepPink = new SeriesStyle(SeriesStyleProvider.DASHED_DEEP_PINK_AREA, 'Dashed Deep Pink Area', SeriesStyleProvider.AREA_DASHED45, "deeppink_45.png");
        dashedAreaDeepPink.form = "segment";
        dashedAreaDeepPink.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_DEEP_PINK_AREA] = dashedAreaDeepPink;
        //Dashed Golden Rod Area 
        /** @type {?} */
        var dashedAreaGoldenRod = new SeriesStyle(SeriesStyleProvider.DASHED_GOLDEN_ROD_AREA, 'Dashed Golden Rod Area', SeriesStyleProvider.AREA_DASHED, "goldenrod.png");
        dashedAreaGoldenRod.form = "segment";
        dashedAreaGoldenRod.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_GOLDEN_ROD_AREA] = dashedAreaGoldenRod;
        //Dashed Green Area 
        /** @type {?} */
        var dashedAreaGreen = new SeriesStyle(SeriesStyleProvider.DASHED_GREEN_AREA, 'Dashed Green Area', SeriesStyleProvider.AREA_DASHED, "green.png");
        dashedAreaGreen.form = "segment";
        dashedAreaGreen.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DASHED_GREEN_AREA] = dashedAreaGreen;
        //Dashed Green Yellow Area 
        /** @type {?} */
        var dottedAreaGreenYellow = new SeriesStyle(SeriesStyleProvider.DOTTED_GREEN_YELLOW_AREA, 'Dotted Green Yellow Area', SeriesStyleProvider.AREA_DOTTED, "greenyellow.png");
        dottedAreaGreenYellow.form = "segment";
        dottedAreaGreenYellow.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DOTTED_GREEN_YELLOW_AREA] = dottedAreaGreenYellow;
        //Dashed Indian Red Area 
        /** @type {?} */
        var dottedAreaIndianRed = new SeriesStyle(SeriesStyleProvider.DOTTED_INDIAN_RED_AREA, 'Dotted Indian Red Area', SeriesStyleProvider.AREA_DOTTED, "indianred.png");
        dottedAreaIndianRed.form = "segment";
        dottedAreaIndianRed.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DOTTED_INDIAN_RED_AREA] = dottedAreaIndianRed;
        //Dotted Magenta Area 
        /** @type {?} */
        var dottedAreaMagenta = new SeriesStyle(SeriesStyleProvider.DOTTED_MAGENTA_AREA, 'Dotted Magenta Area ', SeriesStyleProvider.AREA_DOTTED, "magenta.png");
        dottedAreaMagenta.form = "segment";
        dottedAreaMagenta.isBitmap = true;
        this.allStyles[SeriesStyleProvider.DOTTED_MAGENTA_AREA] = dottedAreaMagenta;
        //Cont Line Black
        /** @type {?} */
        var contSegmentBlack = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_BLACK, 'Black Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentBlack.form = "segment";
        contSegmentBlack.color = '#000000';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_BLACK] = contSegmentBlack;
        //Black Dashed Line 
        /** @type {?} */
        var dashedSegmentBlack = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_BLACK, 'Black Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentBlack.dashPattern = [5, 5, 5, 5];
        dashedSegmentBlack.color = '#000000';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_BLACK] = dashedSegmentBlack;
        //Black Dotted Line 
        /** @type {?} */
        var dottedSegmentBlack = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_BLACK, 'Black Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentBlack.dashPattern = [1, 1, 1, 1];
        dottedSegmentBlack.color = '#000000';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_BLACK] = dottedSegmentBlack;
        //Cont Red Line 
        /** @type {?} */
        var contSegmentRed = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_RED, 'Red Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentRed.form = "segment";
        contSegmentRed.color = '#FF0000';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_RED] = contSegmentRed;
        //Cont  Bold Red Line 
        /** @type {?} */
        var contSegmentBoldRed = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_BOLD_RED, 'Bold Red Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentBoldRed.form = "segment";
        contSegmentBoldRed.color = '#FF0000';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_BOLD_RED] = contSegmentBoldRed;
        //Red Dashed Line 
        /** @type {?} */
        var dashedSegmentRed = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_RED, 'Red Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentRed.dashPattern = [5, 5, 5, 5];
        dashedSegmentRed.color = '#FF0000';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_RED] = dashedSegmentRed;
        //Red Dotted Line 
        /** @type {?} */
        var dottedSegmentRed = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_RED, 'Red Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentRed.dashPattern = [1, 1, 1, 1];
        dottedSegmentRed.color = '#FF0000';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_RED] = dottedSegmentRed;
        //Cont Green Line 
        /** @type {?} */
        var contSegmentGreen = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_GREEN, 'Green Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentGreen.form = "segment";
        contSegmentGreen.color = '#008000';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_GREEN] = contSegmentGreen;
        //Green Dashed Line 
        /** @type {?} */
        var dashedSegmentGreen = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_GREEN, 'Green Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentGreen.dashPattern = [5, 5, 5, 5];
        dashedSegmentGreen.color = '#008000';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_GREEN] = dashedSegmentGreen;
        //Green Dotted Line 
        /** @type {?} */
        var dottedSegmentGreen = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_GREEN, 'Green Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentGreen.dashPattern = [1, 1, 1, 1];
        dottedSegmentGreen.color = '#008000';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_GREEN] = dottedSegmentGreen;
        //Cont Blue Line 
        /** @type {?} */
        var contSegmentBlue = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_BLUE, 'Blue Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentBlue.form = "segment";
        contSegmentBlue.color = '#0000FF';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_BLUE] = contSegmentBlue;
        //Blue Dashed Line
        /** @type {?} */
        var dashedSegmentBlue = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_BLUE, 'Blue Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentBlue.dashPattern = [5, 5, 5, 5];
        dashedSegmentBlue.color = '#0000FF';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_BLUE] = dashedSegmentBlue;
        //Blue Dotted Line 
        /** @type {?} */
        var dottedSegmentBlue = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_BLUE, 'Blue Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentBlue.dashPattern = [1, 1, 1, 1];
        dottedSegmentBlue.color = '#0000FF';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_BLUE] = dottedSegmentBlue;
        //Cont Yellow Line 
        /** @type {?} */
        var contSegmentYellow = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_YELLOW, 'Yellow Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentYellow.form = "segment";
        contSegmentYellow.color = '#FFFF00';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_YELLOW] = contSegmentYellow;
        //Yellow Dashed Line 
        /** @type {?} */
        var dashedSegmentYellow = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_YELLOW, 'Yellow Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentYellow.dashPattern = [5, 5, 5, 5];
        dashedSegmentYellow.color = '#FFFF00';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_YELLOW] = dashedSegmentYellow;
        //Yellow Dotted Line 
        /** @type {?} */
        var dottedSegmentYellow = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_YELLOW, 'Yellow Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentYellow.dashPattern = [1, 1, 1, 1];
        dottedSegmentYellow.color = '#FFFF00';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_YELLOW] = dottedSegmentYellow;
        //Cont Orange Line 
        /** @type {?} */
        var contSegmentOrange = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_ORANGE, 'Orange Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentOrange.form = "segment";
        contSegmentOrange.color = '#FFA500';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_ORANGE] = contSegmentOrange;
        //Orange Dashed Line 
        /** @type {?} */
        var dashedSegmentOrange = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_ORANGE, 'Orange Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentOrange.dashPattern = [5, 5, 5, 5];
        dashedSegmentOrange.color = '#FFA500';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_ORANGE] = dashedSegmentOrange;
        //Orange Dotted Line 
        /** @type {?} */
        var dottedSegmentOrange = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_ORANGE, 'Orange Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentOrange.dashPattern = [1, 1, 1, 1];
        dottedSegmentOrange.color = '#FFA500';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_ORANGE] = dottedSegmentOrange;
        //Cont Magenta Line 
        /** @type {?} */
        var contSegmentMagenta = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_MAGENTA, 'Magenta Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentMagenta.form = "segment";
        contSegmentMagenta.color = '#FF00FF';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_MAGENTA] = contSegmentMagenta;
        //Magenta Dashed Line 
        /** @type {?} */
        var dashedSegmentMagenta = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_MAGENTA, 'Magenta Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentMagenta.dashPattern = [5, 5, 5, 5];
        dashedSegmentMagenta.color = '#FF00FF';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_MAGENTA] = dashedSegmentMagenta;
        //Magenta Dotted Line 
        /** @type {?} */
        var dottedSegmentMagenta = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_MAGENTA, 'Magenta Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentMagenta.dashPattern = [1, 1, 1, 1];
        dottedSegmentMagenta.color = '#FF00FF';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_MAGENTA] = dottedSegmentMagenta;
        //Cont Purple Line 
        /** @type {?} */
        var contSegmentPurple = new SeriesStyle(SeriesStyleProvider.CONT_SEGMENT_PURPLE, 'Purple Line', SeriesStyleProvider.AREA_DOTTED);
        contSegmentPurple.form = "segment";
        contSegmentPurple.color = '#800080';
        this.allStyles[SeriesStyleProvider.CONT_SEGMENT_PURPLE] = contSegmentPurple;
        //Purple Dashed Line 
        /** @type {?} */
        var dashedSegmentPurple = new SeriesStyle(SeriesStyleProvider.DASHED_SEGMENT_PURPLE, 'Purple Dashed Line', SeriesStyleProvider.LINE_DASHED);
        dashedSegmentPurple.dashPattern = [5, 5, 5, 5];
        dashedSegmentPurple.color = '#800080';
        this.allStyles[SeriesStyleProvider.DASHED_SEGMENT_PURPLE] = dashedSegmentPurple;
        //Purple Dotted Line 
        /** @type {?} */
        var dottedSegmentPurple = new SeriesStyle(SeriesStyleProvider.DOTTED_SEGMENT_PURPLE, 'Purple Dotted Line', SeriesStyleProvider.LINE_DOTTED);
        dottedSegmentPurple.dashPattern = [1, 1, 1, 1];
        dottedSegmentPurple.color = '#800080';
        this.allStyles[SeriesStyleProvider.DOTTED_SEGMENT_PURPLE] = dottedSegmentPurple;
        // TODO complete the rest
    }
    Object.defineProperty(SeriesStyleProvider, "instance", {
        /**
         * Get one instance of the SeriesStyleProvider class
         * */
        get: /**
         * Get one instance of the SeriesStyleProvider class
         *
         * @return {?}
         */
        function () {
            if (this._instance == null)
                this._instance = new SeriesStyleProvider();
            return this._instance;
        },
        enumerable: true,
        configurable: true
    });
    /**
         * Get the style name from its Id
         * */
    /**
     * Get the style name from its Id
     *
     * @param {?} styleId
     * @return {?}
     */
    SeriesStyleProvider.getStyleName = /**
     * Get the style name from its Id
     *
     * @param {?} styleId
     * @return {?}
     */
    function (styleId) {
        /** @type {?} */
        var style = SeriesStyleProvider.instance.allStyles[styleId];
        return style != null ? style.name : null;
    };
    //FIXME:ADD color to each style
    //FIXME:ADD color to each style
    /**
     * @param {?} styleId
     * @return {?}
     */
    SeriesStyleProvider.getStyleColor = 
    //FIXME:ADD color to each style
    /**
     * @param {?} styleId
     * @return {?}
     */
    function (styleId) {
        /** @type {?} */
        var style = SeriesStyleProvider.instance.allStyles[styleId];
        return style != null ? style.color : null;
    };
    //FIXME:
    //FIXME:
    /**
     * @param {?} styleId
     * @return {?}
     */
    SeriesStyleProvider.getStyleColorAsSring = 
    //FIXME:
    /**
     * @param {?} styleId
     * @return {?}
     */
    function (styleId) {
        /** @type {?} */
        var style = SeriesStyleProvider.instance.allStyles[styleId];
        if (style.imageName != null) {
            return style.imageName;
        }
        else {
            // if(style.lineStroke != null) {
            // 	return hex2css(style.lineStroke.color);
            // }else {
            // 	if(style.areaFill != null) {
            // 		return hex2css(style.areaFill.color);
            // 	}else {
            // 		return "";
            // 	}
            // }
            return style.color;
        }
    };
    /**
     * @param {?} styleId
     * @return {?}
     */
    SeriesStyleProvider.getStyleBorderColorAsSring = /**
     * @param {?} styleId
     * @return {?}
     */
    function (styleId) {
        /** @type {?} */
        var style = SeriesStyleProvider.instance.allStyles[styleId];
        if (style.imageName != null) {
            return SeriesStyleProvider.DASHED_PATTERN_REAL_COLOR[styleId];
        }
        else {
            //FIXME:CHECK IF NEEDED
            // if(style.lineStroke != null) {
            // 	return hex2css(style.lineStroke.color);
            // }else {
            // 	if(style.areaFill != null) {
            // 		return hex2css(style.areaFill.color);
            // 	}else {
            // 		return "";
            // 	}
            // }
            return style.color;
        }
    };
    /**
     * @param {?} colorUint
     * @return {?}
     */
    SeriesStyleProvider.hex2css = /**
     * @param {?} colorUint
     * @return {?}
     */
    function (colorUint) {
        /** @type {?} */
        var color = colorUint.toString(16);
        if (color.length < 6) {
            /** @type {?} */
            var razlika = 6 - color.length;
            /** @type {?} */
            var temp_color = '';
            for (var i = 0; i < razlika; i++) {
                temp_color += '0';
            }
            temp_color += color;
            color = temp_color;
        }
        return "#" + color.toUpperCase();
    };
    /**
     * @param {?} styleId
     * @return {?}
     */
    SeriesStyleProvider.getStyleType = /**
     * @param {?} styleId
     * @return {?}
     */
    function (styleId) {
        /** @type {?} */
        var style = SeriesStyleProvider.instance.allStyles[styleId];
        return style != null ? style.type : -1;
    };
    SeriesStyleProvider._instance = null;
    SeriesStyleProvider.LINE = 0;
    SeriesStyleProvider.LINE_SOLID = 1;
    SeriesStyleProvider.LINE_DOTTED = 2;
    SeriesStyleProvider.LINE_DASHED = 3;
    SeriesStyleProvider.AREA = 10;
    SeriesStyleProvider.AREA_SOLID = 11;
    SeriesStyleProvider.AREA_DASHED = 12;
    SeriesStyleProvider.AREA_DASHED45 = 13;
    SeriesStyleProvider.AREA_DASHED135 = 14;
    SeriesStyleProvider.AREA_DOTTED = 15;
    SeriesStyleProvider.CONT_AREA_BLIZZARD_BLUE = "CONT_AREA_BLIZZARD_BLUE";
    SeriesStyleProvider.CONT_AREA_COTTON_CANDY = "CONT_AREA_COTTON_CANDY";
    SeriesStyleProvider.DASHED_AREA_PERANO = "DASHED_AREA_PERANO";
    SeriesStyleProvider.DASHED_AREA_SEA_PINK = "DASHED_AREA_SEA_PINK";
    SeriesStyleProvider.CONT_AREA_BLACK = "CONT_AREA_BLACK";
    SeriesStyleProvider.CONT_AREA_GREY = "CONT_AREA_GREY";
    SeriesStyleProvider.CONT_AREA_LIGHT_GREY = "CONT_AREA_LIGHT_GREY";
    SeriesStyleProvider.CONT_AREA_RED = "CONT_AREA_RED";
    SeriesStyleProvider.CONT_AREA_INDIAN_RED = "CONT_AREA_INDIAN_RED";
    SeriesStyleProvider.CONT_AREA_PINK = "CONT_AREA_PINK";
    SeriesStyleProvider.CONT_AREA_ORANGE = "CONT_AREA_ORANGE";
    SeriesStyleProvider.CONT_AREA_PEACH_PUFF = "CONT_AREA_PEACH_PUFF";
    SeriesStyleProvider.CONT_AREA_YELLOW = "CONT_AREA_YELLOW";
    SeriesStyleProvider.CONT_AREA_GREEN = "CONT_AREA_GREEN";
    SeriesStyleProvider.CONT_AREA_LIME = "CONT_AREA_LIME";
    SeriesStyleProvider.CONT_AREA_LIGHT_GREEN = "CONT_AREA_LIGHT_GREEN";
    SeriesStyleProvider.CONT_AREA_BLUE = "CONT_AREA_BLUE";
    SeriesStyleProvider.CONT_AREA_STEEL_BLUE = "CONT_AREA_STEEL_BLUE";
    SeriesStyleProvider.CONT_AREA_LIGHT_BLUE = "CONT_AREA_LIGHT_BLUE";
    SeriesStyleProvider.CONT_AREA_LIGHT_CYAN = "CONT_AREA_LIGHT_CYAN";
    SeriesStyleProvider.CONT_AREA_MAGENTA = "CONT_AREA_MAGENTA";
    SeriesStyleProvider.CONT_AREA_PURPLE = "CONT_AREA_PURPLE";
    SeriesStyleProvider.CONT_AREA_VIOLET = "CONT_AREA_VIOLET";
    SeriesStyleProvider.DASHED_CORAL_AREA = "DASHED_CORAL_AREA";
    SeriesStyleProvider.DASHED_BLUE_AREA = "DASHED_BLUE_AREA";
    SeriesStyleProvider.DASHED_AQUA_AREA = "DASHED_AQUA_AREA";
    SeriesStyleProvider.DASHED_DEEP_PINK_AREA = "DASHED_DEEP_PINK_AREA";
    SeriesStyleProvider.DASHED_GOLDEN_ROD_AREA = "DASHED_GOLDEN_ROD_AREA";
    SeriesStyleProvider.DASHED_GREEN_AREA = "DASHED_GREEN_AREA";
    SeriesStyleProvider.DOTTED_GREEN_YELLOW_AREA = "DOTTED_GREEN_YELLOW_AREA";
    SeriesStyleProvider.DOTTED_INDIAN_RED_AREA = "DOTTED_INDIAN_RED_AREA";
    SeriesStyleProvider.DOTTED_MAGENTA_AREA = "DOTTED_MAGENTA_AREA";
    SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE = "CONT_AREA_ANTIQUE_WHITE";
    SeriesStyleProvider.CONT_AREA_APRICOT_PEACH = "CONT_AREA_APRICOT_PEACH";
    SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE = "CONT_AREA_NAVAJO_WHITE";
    SeriesStyleProvider.CONT_AREA_ROSE_FOG = "CONT_AREA_ROSE_FOG";
    SeriesStyleProvider.CONT_AREA_DARK_SALMON = "CONT_AREA_DARK_SALMON";
    SeriesStyleProvider.CONT_SEGMENT_LYNCH = "CONT_SEGMENT_LYNCH";
    SeriesStyleProvider.DASHED_SEGMENT_SAN_MARINO = "DASHED_SEGMENT_SAN_MARINO";
    SeriesStyleProvider.CONT_SEGMENT_BLACK = "CONT_SEGMENT_BLACK";
    SeriesStyleProvider.DASHED_SEGMENT_BLACK = "DASHED_SEGMENT_BLACK";
    SeriesStyleProvider.DOTTED_SEGMENT_BLACK = "DOTTED_SEGMENT_BLACK";
    SeriesStyleProvider.CONT_SEGMENT_GREY = "CONT_SEGMENT_GREY";
    SeriesStyleProvider.DASHED_SEGMENT_GREY = "DASHED_SEGMENT_GREY";
    SeriesStyleProvider.DOTTED_SEGMENT_GREY = "DOTTED_SEGMENT_GREY";
    SeriesStyleProvider.CONT_SEGMENT_RED = "CONT_SEGMENT_RED";
    SeriesStyleProvider.DASHED_SEGMENT_RED = "DASHED_SEGMENT_RED";
    SeriesStyleProvider.DOTTED_SEGMENT_RED = "DOTTED_SEGMENT_RED";
    SeriesStyleProvider.CONT_SEGMENT_BOLD_RED = "CONT_SEGMENT_BOLD_RED";
    SeriesStyleProvider.CONT_SEGMENT_YELLOW = "CONT_SEGMENT_YELLOW";
    SeriesStyleProvider.DASHED_SEGMENT_YELLOW = "DASHED_SEGMENT_YELLOW";
    SeriesStyleProvider.DOTTED_SEGMENT_YELLOW = "DOTTED_SEGMENT_YELLOW";
    SeriesStyleProvider.CONT_SEGMENT_GREEN = "CONT_SEGMENT_GREEN";
    SeriesStyleProvider.DASHED_SEGMENT_GREEN = "DASHED_SEGMENT_GREEN";
    SeriesStyleProvider.DOTTED_SEGMENT_GREEN = "DOTTED_SEGMENT_GREEN";
    SeriesStyleProvider.CONT_SEGMENT_BLUE = "CONT_SEGMENT_BLUE";
    SeriesStyleProvider.DASHED_SEGMENT_BLUE = "DASHED_SEGMENT_BLUE";
    SeriesStyleProvider.DOTTED_SEGMENT_BLUE = "DOTTED_SEGMENT_BLUE";
    SeriesStyleProvider.CONT_SEGMENT_ORANGE = "CONT_SEGMENT_ORANGE";
    SeriesStyleProvider.DASHED_SEGMENT_ORANGE = "DASHED_SEGMENT_ORANGE";
    SeriesStyleProvider.DOTTED_SEGMENT_ORANGE = "DOTTED_SEGMENT_ORANGE";
    SeriesStyleProvider.CONT_SEGMENT_MAGENTA = "CONT_SEGMENT_MAGENTA";
    SeriesStyleProvider.DASHED_SEGMENT_MAGENTA = "DASHED_SEGMENT_MAGENTA";
    SeriesStyleProvider.DOTTED_SEGMENT_MAGENTA = "DOTTED_SEGMENT_MAGENTA";
    SeriesStyleProvider.CONT_SEGMENT_PURPLE = "CONT_SEGMENT_PURPLE";
    SeriesStyleProvider.DASHED_SEGMENT_PURPLE = "DASHED_SEGMENT_PURPLE";
    SeriesStyleProvider.DOTTED_SEGMENT_PURPLE = "DOTTED_SEGMENT_PURPLE";
    //TODO: waiting for STL opinion to put the requiered colors 
    SeriesStyleProvider.SIMILAR_COLORS = [["CONT_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"],
        ["DASHED_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"],
        ["DOTTED_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"],
        ["CONT_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"],
        ["DASHED_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"],
        ["DOTTED_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"]
    ];
    SeriesStyleProvider.DASHED_PATTERN_REAL_COLOR = {
        "DASHED_AREA_PERANO": "#748DF2",
        "DASHED_CORAL_AREA": "#E6755E",
        "DASHED_BLUE_AREA": "#0000FF",
        "DASHED_AQUA_AREA": "#00FFFF",
        "DASHED_DEEP_PINK_AREA": "#F51FCA",
        "DASHED_GOLDEN_ROD_AREA": "#F7BE1F",
        "DASHED_GREEN_AREA": "#008000",
        "DOTTED_GREEN_YELLOW_AREA": "#90DC12",
        "DOTTED_INDIAN_RED_AREA": "#E18470",
        "DOTTED_MAGENTA_AREA": "#FF00FF"
    };
    return SeriesStyleProvider;
}());
export { SeriesStyleProvider };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SeriesStyleProvider._instance;
    /** @type {?} */
    SeriesStyleProvider.LINE;
    /** @type {?} */
    SeriesStyleProvider.LINE_SOLID;
    /** @type {?} */
    SeriesStyleProvider.LINE_DOTTED;
    /** @type {?} */
    SeriesStyleProvider.LINE_DASHED;
    /** @type {?} */
    SeriesStyleProvider.AREA;
    /** @type {?} */
    SeriesStyleProvider.AREA_SOLID;
    /** @type {?} */
    SeriesStyleProvider.AREA_DASHED;
    /** @type {?} */
    SeriesStyleProvider.AREA_DASHED45;
    /** @type {?} */
    SeriesStyleProvider.AREA_DASHED135;
    /** @type {?} */
    SeriesStyleProvider.AREA_DOTTED;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_BLIZZARD_BLUE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_COTTON_CANDY;
    /** @type {?} */
    SeriesStyleProvider.DASHED_AREA_PERANO;
    /** @type {?} */
    SeriesStyleProvider.DASHED_AREA_SEA_PINK;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_BLACK;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_GREY;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_LIGHT_GREY;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_RED;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_INDIAN_RED;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_PINK;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_ORANGE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_PEACH_PUFF;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_YELLOW;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_GREEN;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_LIME;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_LIGHT_GREEN;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_BLUE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_STEEL_BLUE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_LIGHT_BLUE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_LIGHT_CYAN;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_MAGENTA;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_PURPLE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_VIOLET;
    /** @type {?} */
    SeriesStyleProvider.DASHED_CORAL_AREA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_BLUE_AREA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_AQUA_AREA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_DEEP_PINK_AREA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_GOLDEN_ROD_AREA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_GREEN_AREA;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_GREEN_YELLOW_AREA;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_INDIAN_RED_AREA;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_MAGENTA_AREA;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_APRICOT_PEACH;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_ROSE_FOG;
    /** @type {?} */
    SeriesStyleProvider.CONT_AREA_DARK_SALMON;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_LYNCH;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_SAN_MARINO;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_BLACK;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_BLACK;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_BLACK;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_GREY;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_GREY;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_GREY;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_RED;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_RED;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_RED;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_BOLD_RED;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_YELLOW;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_YELLOW;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_YELLOW;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_GREEN;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_GREEN;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_GREEN;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_BLUE;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_BLUE;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_BLUE;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_ORANGE;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_ORANGE;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_ORANGE;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_MAGENTA;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_MAGENTA;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_MAGENTA;
    /** @type {?} */
    SeriesStyleProvider.CONT_SEGMENT_PURPLE;
    /** @type {?} */
    SeriesStyleProvider.DASHED_SEGMENT_PURPLE;
    /** @type {?} */
    SeriesStyleProvider.DOTTED_SEGMENT_PURPLE;
    /** @type {?} */
    SeriesStyleProvider.SIMILAR_COLORS;
    /** @type {?} */
    SeriesStyleProvider.DASHED_PATTERN_REAL_COLOR;
    /** @type {?} */
    SeriesStyleProvider.prototype.allStyles;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU2VyaWVzU3R5bGVQcm92aWRlci5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jaGFydHMvSUxNQ2hhcnRzL0lMTUxpbmVDaGFydC9zdHlsZS9TZXJpZXNTdHlsZVByb3ZpZGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsV0FBVyxFQUFFLE1BQU0sZUFBZSxDQUFDOzs7OztBQU01QztJQXVJSTs7U0FFSztJQUNMO1FBZE8sY0FBUyxHQUFHLEVBQUUsQ0FBQzs7O1lBZ0JkLG1CQUFtQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyx1QkFBdUIsRUFBQyw0REFBNEQsQ0FBQSxtQkFBbUIsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDbk4sbUJBQW1CLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUN0QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHVCQUF1QixDQUFDLEdBQUcsbUJBQW1CLENBQUM7OztZQUc5RSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsc0JBQXNCLEVBQUUsbUJBQW1CLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBRXZKLG1CQUFtQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFJLG1CQUFtQixDQUFDOztZQUU5RSxhQUFhLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGVBQWUsRUFBRSxZQUFZLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ25JLGFBQWEsQ0FBQyxLQUFLLEdBQUcsUUFBUSxDQUFDO1FBQy9CLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsZUFBZSxDQUFDLEdBQUcsYUFBYSxDQUFDOztZQUVoRSxZQUFZLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGNBQWMsRUFBRSxXQUFXLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ2hJLFlBQVksQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQy9CLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsY0FBYyxDQUFDLEdBQUcsWUFBWSxDQUFDOztZQUU5RCxpQkFBaUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ2pKLGlCQUFpQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsQ0FBQyxHQUFHLGlCQUFpQixDQUFDOztZQUV6RSxXQUFXLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGFBQWEsRUFBRSxVQUFVLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQzdILFdBQVcsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQzlCLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsYUFBYSxDQUFDLEdBQUcsV0FBVyxDQUFDOztZQUU1RCxpQkFBaUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ2pKLGlCQUFpQixDQUFDLEtBQUssR0FBRyxVQUFVLENBQUM7UUFDckMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsQ0FBQyxHQUFHLGlCQUFpQixDQUFDOztZQUV6RSxZQUFZLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGNBQWMsRUFBRSxXQUFXLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ2hJLFlBQVksQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQy9CLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsY0FBYyxDQUFDLEdBQUcsWUFBWSxDQUFDOztZQUU5RCxjQUFjLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGdCQUFnQixFQUFFLGFBQWEsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDdEksY0FBYyxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDakMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLGNBQWMsQ0FBQzs7WUFFbEUsaUJBQWlCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixFQUFFLGlCQUFpQixFQUFFLG1CQUFtQixDQUFDLFVBQVUsQ0FBQztRQUNqSixpQkFBaUIsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs7WUFFekUsY0FBYyxHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxnQkFBZ0IsRUFBRSxhQUFhLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ3RJLGNBQWMsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ2pDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxjQUFjLENBQUM7O1lBR2xFLGFBQWEsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsZUFBZSxFQUFFLFlBQVksRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDbkksYUFBYSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDaEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxlQUFlLENBQUMsR0FBRyxhQUFhLENBQUM7O1lBRWhFLFlBQVksR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsY0FBYyxFQUFFLFdBQVcsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDaEksWUFBWSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDL0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxjQUFjLENBQUMsR0FBRSxZQUFZLENBQUM7O1lBRTdELGtCQUFrQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsRUFBRSxrQkFBa0IsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDcEosa0JBQWtCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNyQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDLEdBQUcsa0JBQWtCLENBQUM7O1lBRTNFLFlBQVksR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsY0FBYyxFQUFFLFdBQVcsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDaEksWUFBWSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDL0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxjQUFjLENBQUMsR0FBRyxZQUFZLENBQUM7O1lBRTlELGlCQUFpQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDakosaUJBQWlCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNwQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLEdBQUcsaUJBQWlCLENBQUM7O1lBRXpFLGlCQUFpQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDakosaUJBQWlCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNwQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLEdBQUUsaUJBQWlCLENBQUM7O1lBRXhFLGlCQUFpQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDakosaUJBQWlCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNwQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLEdBQUUsaUJBQWlCLENBQUM7O1lBRXhFLGVBQWUsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsaUJBQWlCLEVBQUUsY0FBYyxFQUFFLG1CQUFtQixDQUFDLFVBQVUsQ0FBQztRQUN6SSxlQUFlLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNsQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGlCQUFpQixDQUFDLEdBQUcsZUFBZSxDQUFDOztZQUVwRSxjQUFjLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGdCQUFnQixFQUFFLGFBQWEsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDdEksY0FBYyxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDakMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLGNBQWMsQ0FBQzs7O1lBR2xFLGNBQWMsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLEVBQUUsYUFBYSxFQUFFLG1CQUFtQixDQUFDLFVBQVUsQ0FBQztRQUN0SSxjQUFjLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNqQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGdCQUFnQixDQUFDLEdBQUcsY0FBYyxDQUFDOztZQUdsRSxvQkFBb0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsdUJBQXVCLEVBQUUsb0JBQW9CLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQzFKLG9CQUFvQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdkMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyx1QkFBdUIsQ0FBQyxHQUFHLG9CQUFvQixDQUFDOztZQUUvRSxvQkFBb0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsdUJBQXVCLEVBQUUsb0JBQW9CLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQzFKLG9CQUFvQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdkMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyx1QkFBdUIsQ0FBQyxHQUFHLG9CQUFvQixDQUFDOztZQUUvRSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsc0JBQXNCLEVBQUUsbUJBQW1CLEVBQUUsbUJBQW1CLENBQUMsVUFBVSxDQUFDO1FBQ3ZKLG1CQUFtQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLG1CQUFtQixDQUFDOztZQUU3RSxlQUFlLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGtCQUFrQixFQUFFLGVBQWUsRUFBRSxtQkFBbUIsQ0FBQyxVQUFVLENBQUM7UUFDM0ksZUFBZSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDbEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxrQkFBa0IsQ0FBQyxHQUFFLGVBQWUsQ0FBQzs7WUFFcEUsa0JBQWtCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixFQUFFLGtCQUFrQixFQUFFLG1CQUFtQixDQUFDLFVBQVUsQ0FBQztRQUNwSixrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLENBQUMsR0FBRyxrQkFBa0IsQ0FBQzs7O1lBSTNFLGdCQUFnQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxrQkFBa0IsRUFBRSxvQkFBb0IsRUFBRSxtQkFBbUIsQ0FBQyxhQUFhLEVBQUUsT0FBTyxDQUFDO1FBQzdKLGdCQUFnQixDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDbEMsZ0JBQWdCLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztRQUNqQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGtCQUFrQixDQUFDLEdBQUcsZ0JBQWdCLENBQUM7OztZQUd0RSxlQUFlLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGlCQUFpQixFQUFFLG1CQUFtQixFQUFFLG1CQUFtQixDQUFDLGFBQWEsRUFBRSxPQUFPLENBQUM7UUFDMUosZUFBZSxDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDakMsZUFBZSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUM7UUFDaEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLGVBQWUsQ0FBQzs7O1lBTXBFLGNBQWMsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLEVBQUUsa0JBQWtCLEVBQUUsbUJBQW1CLENBQUMsYUFBYSxFQUFFLGFBQWEsQ0FBQztRQUM3SixjQUFjLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNoQyxjQUFjLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztRQUMvQixJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGdCQUFnQixDQUFDLEdBQUcsY0FBYyxDQUFDOzs7WUFHbEUsY0FBYyxHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxnQkFBZ0IsRUFBRSxrQkFBa0IsRUFBRSxtQkFBbUIsQ0FBQyxhQUFhLEVBQUUsYUFBYSxDQUFDO1FBQzdKLGNBQWMsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ2hDLGNBQWMsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO1FBQy9CLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxjQUFjLENBQUM7OztZQUdsRSxrQkFBa0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLEVBQUUsdUJBQXVCLEVBQUUsbUJBQW1CLENBQUMsYUFBYSxFQUFFLGlCQUFpQixDQUFDO1FBQy9LLGtCQUFrQixDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDcEMsa0JBQWtCLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztRQUNuQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDLEdBQUcsa0JBQWtCLENBQUM7OztZQUczRSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsc0JBQXNCLEVBQUUsd0JBQXdCLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxFQUFFLGVBQWUsQ0FBQztRQUM5SyxtQkFBbUIsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLG1CQUFtQixDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUM7UUFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLG1CQUFtQixDQUFDOzs7WUFHN0UsZUFBZSxHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBaUIsRUFBRSxtQkFBbUIsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLEVBQUUsV0FBVyxDQUFDO1FBQzVKLGVBQWUsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ2pDLGVBQWUsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO1FBQ2hDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsaUJBQWlCLENBQUMsR0FBRyxlQUFlLENBQUM7OztZQUdwRSxxQkFBcUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsd0JBQXdCLEVBQUUsMEJBQTBCLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxFQUFFLGlCQUFpQixDQUFDO1FBQ3RMLHFCQUFxQixDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDdkMscUJBQXFCLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztRQUN0QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHdCQUF3QixDQUFDLEdBQUcscUJBQXFCLENBQUM7OztZQUdqRixtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsc0JBQXNCLEVBQUUsd0JBQXdCLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxFQUFFLGVBQWUsQ0FBQztRQUM5SyxtQkFBbUIsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLG1CQUFtQixDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUM7UUFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLG1CQUFtQixDQUFDOzs7WUFHN0UsaUJBQWlCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixFQUFFLHNCQUFzQixFQUFFLG1CQUFtQixDQUFDLFdBQVcsRUFBRSxhQUFhLENBQUM7UUFDckssaUJBQWlCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNuQyxpQkFBaUIsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO1FBQ2xDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs7O1lBTXhFLGdCQUFnQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxrQkFBa0IsRUFBRSxZQUFZLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQzFJLGdCQUFnQixDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDbEMsZ0JBQWdCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNuQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGtCQUFrQixDQUFDLEdBQUcsZ0JBQWdCLENBQUM7OztZQUd0RSxrQkFBa0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLEVBQUUsbUJBQW1CLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQ3JKLGtCQUFrQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQzlDLGtCQUFrQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDckMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsQ0FBQyxHQUFHLGtCQUFrQixDQUFDOzs7WUFFMUUsa0JBQWtCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixFQUFFLG1CQUFtQixFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUNySixrQkFBa0IsQ0FBQyxXQUFXLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUM5QyxrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLENBQUMsR0FBRyxrQkFBa0IsQ0FBQzs7O1lBRzFFLGNBQWMsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLEVBQUUsVUFBVSxFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUNwSSxjQUFjLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNoQyxjQUFjLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUFBLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxjQUFjLENBQUM7OztZQUVuRyxrQkFBa0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLEVBQUUsZUFBZSxFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUNsSixrQkFBa0IsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ3BDLGtCQUFrQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDckMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsQ0FBQyxHQUFHLGtCQUFrQixDQUFDOzs7WUFFM0UsZ0JBQWdCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGtCQUFrQixFQUFFLGlCQUFpQixFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUMvSSxnQkFBZ0IsQ0FBQyxXQUFXLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUM1QyxnQkFBZ0IsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ25DLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsa0JBQWtCLENBQUMsR0FBRyxnQkFBZ0IsQ0FBQzs7O1lBRXRFLGdCQUFnQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxrQkFBa0IsRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDL0ksZ0JBQWdCLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDNUMsZ0JBQWdCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNuQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLGtCQUFrQixDQUFDLEdBQUcsZ0JBQWdCLENBQUM7OztZQUd0RSxnQkFBZ0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsa0JBQWtCLEVBQUUsWUFBWSxFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUMxSSxnQkFBZ0IsQ0FBQyxJQUFJLEdBQUcsU0FBUyxDQUFDO1FBQ2xDLGdCQUFnQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDbkMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLGdCQUFnQixDQUFDOzs7WUFFdEUsa0JBQWtCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixFQUFFLG1CQUFtQixFQUFFLG1CQUFtQixDQUFDLFdBQVcsQ0FBQztRQUNySixrQkFBa0IsQ0FBQyxXQUFXLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUM5QyxrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLENBQUMsR0FBRyxrQkFBa0IsQ0FBQzs7O1lBRTFFLGtCQUFrQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBb0IsRUFBRSxtQkFBbUIsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDckosa0JBQWtCLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDOUMsa0JBQWtCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNyQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLEdBQUUsa0JBQWtCLENBQUM7OztZQUd6RSxlQUFlLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLGlCQUFpQixFQUFFLFdBQVcsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDdkksZUFBZSxDQUFDLElBQUksR0FBRyxTQUFTLENBQUM7UUFDakMsZUFBZSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDbEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLGVBQWUsQ0FBQzs7O1lBRXBFLGlCQUFpQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxtQkFBbUIsRUFBRSxrQkFBa0IsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDbEosaUJBQWlCLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDN0MsaUJBQWlCLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUNwQyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixDQUFDLEdBQUcsaUJBQWlCLENBQUM7OztZQUV4RSxpQkFBaUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLEVBQUUsa0JBQWtCLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQ2xKLGlCQUFpQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQzdDLGlCQUFpQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxtQkFBbUIsQ0FBQyxHQUFHLGlCQUFpQixDQUFDOzs7WUFHeEUsaUJBQWlCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixFQUFFLGFBQWEsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDN0ksaUJBQWlCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNuQyxpQkFBaUIsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs7O1lBRXhFLG1CQUFtQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsRUFBRSxvQkFBb0IsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDeEosbUJBQW1CLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDL0MsbUJBQW1CLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUN0QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDLEdBQUcsbUJBQW1CLENBQUM7OztZQUU1RSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLEVBQUUsb0JBQW9CLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQ3hKLG1CQUFtQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQy9DLG1CQUFtQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsQ0FBQyxHQUFFLG1CQUFtQixDQUFDOzs7WUFHM0UsaUJBQWlCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixFQUFFLGFBQWEsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDN0ksaUJBQWlCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNuQyxpQkFBaUIsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLENBQUMsR0FBRSxpQkFBaUIsQ0FBQzs7O1lBRXZFLG1CQUFtQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsRUFBRSxvQkFBb0IsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDeEosbUJBQW1CLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDL0MsbUJBQW1CLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUN0QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDLEdBQUUsbUJBQW1CLENBQUM7OztZQUUzRSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLEVBQUUsb0JBQW9CLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQ3hKLG1CQUFtQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQy9DLG1CQUFtQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsQ0FBQyxHQUFFLG1CQUFtQixDQUFDOzs7WUFHM0Usa0JBQWtCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixFQUFFLGNBQWMsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDaEosa0JBQWtCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNwQyxrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsb0JBQW9CLENBQUMsR0FBRSxrQkFBa0IsQ0FBQzs7O1lBRXpFLG9CQUFvQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsRUFBRSxxQkFBcUIsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDM0osb0JBQW9CLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDaEQsb0JBQW9CLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUN2QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHNCQUFzQixDQUFDLEdBQUUsb0JBQW9CLENBQUM7OztZQUU3RSxvQkFBb0IsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMsc0JBQXNCLEVBQUUscUJBQXFCLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQzNKLG9CQUFvQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ2hELG9CQUFvQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdkMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFFLG9CQUFvQixDQUFDOzs7WUFJN0UsaUJBQWlCLEdBQWdCLElBQUksV0FBVyxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixFQUFFLGFBQWEsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDN0ksaUJBQWlCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQztRQUNuQyxpQkFBaUIsQ0FBQyxLQUFLLEdBQUcsU0FBUyxDQUFDO1FBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs7O1lBRXhFLG1CQUFtQixHQUFnQixJQUFJLFdBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsRUFBRSxvQkFBb0IsRUFBRSxtQkFBbUIsQ0FBQyxXQUFXLENBQUM7UUFDeEosbUJBQW1CLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDL0MsbUJBQW1CLENBQUMsS0FBSyxHQUFHLFNBQVMsQ0FBQztRQUN0QyxJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDLEdBQUcsbUJBQW1CLENBQUM7OztZQUU1RSxtQkFBbUIsR0FBZ0IsSUFBSSxXQUFXLENBQUMsbUJBQW1CLENBQUMscUJBQXFCLEVBQUUsb0JBQW9CLEVBQUUsbUJBQW1CLENBQUMsV0FBVyxDQUFDO1FBQ3hKLG1CQUFtQixDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQy9DLG1CQUFtQixDQUFDLEtBQUssR0FBRyxTQUFTLENBQUM7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxxQkFBcUIsQ0FBQyxHQUFHLG1CQUFtQixDQUFDO1FBTWhGLHlCQUF5QjtJQUc3QixDQUFDO0lBeFVELHNCQUFrQiwrQkFBUTtRQUgxQjs7YUFFSzs7Ozs7O1FBQ0w7WUFDSSxJQUFJLElBQUksQ0FBQyxTQUFTLElBQUksSUFBSTtnQkFDdEIsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLG1CQUFtQixFQUFFLENBQUM7WUFDL0MsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1FBQzFCLENBQUM7OztPQUFBO0lBdVVEOzthQUVHOzs7Ozs7O0lBQ1MsZ0NBQVk7Ozs7OztJQUExQixVQUEyQixPQUFjOztZQUNwQyxLQUFLLEdBQWUsbUJBQW1CLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUM7UUFDdkUsT0FBTyxLQUFLLElBQUUsSUFBSSxDQUFBLENBQUMsQ0FBQSxLQUFLLENBQUMsSUFBSSxDQUFBLENBQUMsQ0FBQSxJQUFJLENBQUM7SUFDOUIsQ0FBQztJQUVELCtCQUErQjs7Ozs7O0lBQ3ZCLGlDQUFhOzs7Ozs7SUFBM0IsVUFBNEIsT0FBYzs7WUFDckMsS0FBSyxHQUFlLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDO1FBQ3ZFLE9BQU8sS0FBSyxJQUFFLElBQUksQ0FBQSxDQUFDLENBQUEsS0FBSyxDQUFDLEtBQUssQ0FBQSxDQUFDLENBQUEsSUFBSSxDQUFDO0lBQ3JDLENBQUM7SUFFSyxRQUFROzs7Ozs7SUFDQyx3Q0FBb0I7Ozs7OztJQUFuQyxVQUFvQyxPQUFjOztZQUU3QyxLQUFLLEdBQWUsbUJBQW1CLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUM7UUFDdkUsSUFBRyxLQUFLLENBQUMsU0FBUyxJQUFJLElBQUksRUFBRTtZQUMzQixPQUFPLEtBQUssQ0FBQyxTQUFTLENBQUM7U0FDdkI7YUFBSztZQUNMLGlDQUFpQztZQUNqQywyQ0FBMkM7WUFDM0MsVUFBVTtZQUNWLGdDQUFnQztZQUNoQywwQ0FBMEM7WUFDMUMsV0FBVztZQUNYLGVBQWU7WUFDZixLQUFLO1lBQ08sSUFBSTtZQUNKLE9BQU8sS0FBSyxDQUFDLEtBQUssQ0FBQztTQUUvQjtJQUNGLENBQUM7Ozs7O0lBRWEsOENBQTBCOzs7O0lBQXhDLFVBQXlDLE9BQWM7O1lBQ2xELEtBQUssR0FBZSxtQkFBbUIsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQztRQUN2RSxJQUFHLEtBQUssQ0FBQyxTQUFTLElBQUksSUFBSSxFQUFFO1lBQzNCLE9BQU8sbUJBQW1CLENBQUMseUJBQXlCLENBQUMsT0FBTyxDQUFDLENBQUM7U0FDOUQ7YUFBSztZQUNPLHVCQUF1QjtZQUNuQyxpQ0FBaUM7WUFDakMsMkNBQTJDO1lBQzNDLFVBQVU7WUFDVixnQ0FBZ0M7WUFDaEMsMENBQTBDO1lBQzFDLFdBQVc7WUFDWCxlQUFlO1lBQ2YsS0FBSztZQUNMLElBQUk7WUFDUSxPQUFPLEtBQUssQ0FBQyxLQUFLLENBQUM7U0FDL0I7SUFDRixDQUFDOzs7OztJQUlhLDJCQUFPOzs7O0lBQXJCLFVBQXVCLFNBQWtCOztZQUVwQyxLQUFLLEdBQVUsU0FBUyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7UUFDekMsSUFBSSxLQUFLLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBQzs7Z0JBQ2hCLE9BQU8sR0FBVSxDQUFDLEdBQUMsS0FBSyxDQUFDLE1BQU07O2dCQUMvQixVQUFVLEdBQVUsRUFBRTtZQUMxQixLQUFLLElBQUksQ0FBQyxHQUFRLENBQUMsRUFBRSxDQUFDLEdBQUMsT0FBTyxFQUFFLENBQUMsRUFBRSxFQUFDO2dCQUNuQyxVQUFVLElBQUksR0FBRyxDQUFDO2FBQ2xCO1lBQ0QsVUFBVSxJQUFJLEtBQUssQ0FBQztZQUNwQixLQUFLLEdBQUcsVUFBVSxDQUFBO1NBQ2xCO1FBR0QsT0FBTyxHQUFHLEdBQUcsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDO0lBQ2xDLENBQUM7Ozs7O0lBQ2EsZ0NBQVk7Ozs7SUFBMUIsVUFBMkIsT0FBYzs7WUFDcEMsS0FBSyxHQUFlLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDO1FBQ3ZFLE9BQU8sS0FBSyxJQUFFLElBQUksQ0FBQSxDQUFDLENBQUEsS0FBSyxDQUFDLElBQUksQ0FBQSxDQUFDLENBQUEsQ0FBQyxDQUFDLENBQUM7SUFDbEMsQ0FBQztJQXRoQmdCLDZCQUFTLEdBQXdCLElBQUksQ0FBQztJQUV2Qyx3QkFBSSxHQUFXLENBQUMsQ0FBQztJQUNqQiw4QkFBVSxHQUFXLENBQUMsQ0FBQztJQUN2QiwrQkFBVyxHQUFXLENBQUMsQ0FBQztJQUN4QiwrQkFBVyxHQUFXLENBQUMsQ0FBQztJQUV4Qix3QkFBSSxHQUFXLEVBQUUsQ0FBQztJQUNsQiw4QkFBVSxHQUFXLEVBQUUsQ0FBQztJQUN4QiwrQkFBVyxHQUFXLEVBQUUsQ0FBQztJQUN6QixpQ0FBYSxHQUFXLEVBQUUsQ0FBQztJQUMzQixrQ0FBYyxHQUFXLEVBQUUsQ0FBQztJQUM1QiwrQkFBVyxHQUFXLEVBQUUsQ0FBQztJQUd6QiwyQ0FBdUIsR0FBVyx5QkFBeUIsQ0FBQztJQUM1RCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUMxRCxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUV0RCxtQ0FBZSxHQUFXLGlCQUFpQixDQUFDO0lBQzVDLGtDQUFjLEdBQVcsZ0JBQWdCLENBQUM7SUFDMUMsd0NBQW9CLEdBQVcsc0JBQXNCLENBQUM7SUFDdEQsaUNBQWEsR0FBVyxlQUFlLENBQUM7SUFDeEMsd0NBQW9CLEdBQVcsc0JBQXNCLENBQUM7SUFDdEQsa0NBQWMsR0FBVyxnQkFBZ0IsQ0FBQztJQUMxQyxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5Qyx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5QyxtQ0FBZSxHQUFXLGlCQUFpQixDQUFDO0lBQzVDLGtDQUFjLEdBQVcsZ0JBQWdCLENBQUM7SUFDMUMseUNBQXFCLEdBQVcsdUJBQXVCLENBQUM7SUFDeEQsa0NBQWMsR0FBVyxnQkFBZ0IsQ0FBQztJQUMxQyx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCxxQ0FBaUIsR0FBVyxtQkFBbUIsQ0FBQztJQUNoRCxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5QyxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5QyxxQ0FBaUIsR0FBVyxtQkFBbUIsQ0FBQztJQUNoRCxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5QyxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5Qyx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUN4RCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUMxRCxxQ0FBaUIsR0FBVyxtQkFBbUIsQ0FBQztJQUNoRCw0Q0FBd0IsR0FBVywwQkFBMEIsQ0FBQztJQUM5RCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUMxRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUVwRCwyQ0FBdUIsR0FBVyx5QkFBeUIsQ0FBQztJQUM1RCwyQ0FBdUIsR0FBVyx5QkFBeUIsQ0FBQztJQUM1RCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUMxRCxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUV4RCxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCw2Q0FBeUIsR0FBVywyQkFBMkIsQ0FBQztJQUVoRSxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUV0RCxxQ0FBaUIsR0FBVyxtQkFBbUIsQ0FBQztJQUNoRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUNwRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUVwRCxvQ0FBZ0IsR0FBVyxrQkFBa0IsQ0FBQztJQUM5QyxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUl4RCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUNwRCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUN4RCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUd4RCxzQ0FBa0IsR0FBVyxvQkFBb0IsQ0FBQztJQUNsRCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUd0RCxxQ0FBaUIsR0FBVyxtQkFBbUIsQ0FBQztJQUNoRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUNwRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUdwRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUNwRCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUN4RCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUd4RCx3Q0FBb0IsR0FBVyxzQkFBc0IsQ0FBQztJQUN0RCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUMxRCwwQ0FBc0IsR0FBVyx3QkFBd0IsQ0FBQztJQUcxRCx1Q0FBbUIsR0FBVyxxQkFBcUIsQ0FBQztJQUNwRCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQztJQUN4RCx5Q0FBcUIsR0FBVyx1QkFBdUIsQ0FBQzs7SUFFeEQsa0NBQWMsR0FBRyxDQUFDLENBQUMscUJBQXFCLEVBQUUsd0JBQXdCLENBQUM7UUFDakYsQ0FBQyx1QkFBdUIsRUFBRSx3QkFBd0IsQ0FBQztRQUNuRCxDQUFDLHVCQUF1QixFQUFFLHdCQUF3QixDQUFDO1FBQ25ELENBQUMscUJBQXFCLEVBQUUsdUJBQXVCLENBQUM7UUFDaEQsQ0FBQyx1QkFBdUIsRUFBRSx1QkFBdUIsQ0FBQztRQUNsRCxDQUFDLHVCQUF1QixFQUFFLHVCQUF1QixDQUFDO0tBQ2pELENBQUM7SUFDWSw2Q0FBeUIsR0FBVztRQUM5QyxvQkFBb0IsRUFBRSxTQUFTO1FBQy9CLG1CQUFtQixFQUFFLFNBQVM7UUFDOUIsa0JBQWtCLEVBQUUsU0FBUztRQUM3QixrQkFBa0IsRUFBRSxTQUFTO1FBQzdCLHVCQUF1QixFQUFFLFNBQVM7UUFDbEMsd0JBQXdCLEVBQUUsU0FBUztRQUNuQyxtQkFBbUIsRUFBRSxTQUFTO1FBQzlCLDBCQUEwQixFQUFFLFNBQVM7UUFDckMsd0JBQXdCLEVBQUUsU0FBUztRQUNuQyxxQkFBcUIsRUFBRSxTQUFTO0tBQ25DLENBQUM7SUErWk4sMEJBQUM7Q0FBQSxBQXhoQkQsSUF3aEJDO1NBeGhCWSxtQkFBbUI7Ozs7OztJQUM1Qiw4QkFBcUQ7O0lBRXJELHlCQUErQjs7SUFDL0IsK0JBQXFDOztJQUNyQyxnQ0FBc0M7O0lBQ3RDLGdDQUFzQzs7SUFFdEMseUJBQWdDOztJQUNoQywrQkFBc0M7O0lBQ3RDLGdDQUF1Qzs7SUFDdkMsa0NBQXlDOztJQUN6QyxtQ0FBMEM7O0lBQzFDLGdDQUF1Qzs7SUFHdkMsNENBQTBFOztJQUMxRSwyQ0FBd0U7O0lBQ3hFLHVDQUFnRTs7SUFDaEUseUNBQW9FOztJQUVwRSxvQ0FBMEQ7O0lBQzFELG1DQUF3RDs7SUFDeEQseUNBQW9FOztJQUNwRSxrQ0FBc0Q7O0lBQ3RELHlDQUFvRTs7SUFDcEUsbUNBQXdEOztJQUN4RCxxQ0FBNEQ7O0lBQzVELHlDQUFvRTs7SUFDcEUscUNBQTREOztJQUM1RCxvQ0FBMEQ7O0lBQzFELG1DQUF3RDs7SUFDeEQsMENBQXNFOztJQUN0RSxtQ0FBd0Q7O0lBQ3hELHlDQUFvRTs7SUFDcEUseUNBQW9FOztJQUNwRSx5Q0FBb0U7O0lBQ3BFLHNDQUE4RDs7SUFDOUQscUNBQTREOztJQUM1RCxxQ0FBNEQ7O0lBQzVELHNDQUE4RDs7SUFDOUQscUNBQTREOztJQUM1RCxxQ0FBNEQ7O0lBQzVELDBDQUFzRTs7SUFDdEUsMkNBQXdFOztJQUN4RSxzQ0FBOEQ7O0lBQzlELDZDQUE0RTs7SUFDNUUsMkNBQXdFOztJQUN4RSx3Q0FBa0U7O0lBRWxFLDRDQUEwRTs7SUFDMUUsNENBQTBFOztJQUMxRSwyQ0FBd0U7O0lBQ3hFLHVDQUFnRTs7SUFDaEUsMENBQXNFOztJQUV0RSx1Q0FBZ0U7O0lBQ2hFLDhDQUE4RTs7SUFFOUUsdUNBQWdFOztJQUNoRSx5Q0FBb0U7O0lBQ3BFLHlDQUFvRTs7SUFFcEUsc0NBQThEOztJQUM5RCx3Q0FBa0U7O0lBQ2xFLHdDQUFrRTs7SUFFbEUscUNBQTREOztJQUM1RCx1Q0FBZ0U7O0lBQ2hFLHVDQUFnRTs7SUFDaEUsMENBQXNFOztJQUl0RSx3Q0FBa0U7O0lBQ2xFLDBDQUFzRTs7SUFDdEUsMENBQXNFOztJQUd0RSx1Q0FBZ0U7O0lBQ2hFLHlDQUFvRTs7SUFDcEUseUNBQW9FOztJQUdwRSxzQ0FBOEQ7O0lBQzlELHdDQUFrRTs7SUFDbEUsd0NBQWtFOztJQUdsRSx3Q0FBa0U7O0lBQ2xFLDBDQUFzRTs7SUFDdEUsMENBQXNFOztJQUd0RSx5Q0FBb0U7O0lBQ3BFLDJDQUF3RTs7SUFDeEUsMkNBQXdFOztJQUd4RSx3Q0FBa0U7O0lBQ2xFLDBDQUFzRTs7SUFDdEUsMENBQXNFOztJQUV0RSxtQ0FNRTs7SUFDRiw4Q0FXRTs7SUFHRix3Q0FBc0IiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXJpZXNTdHlsZSB9IGZyb20gJy4vU2VyaWVzU3R5bGUnO1xyXG5cclxuLyoqXHJcbiAqIFRoaXMgY2xhc3MgY29udGFpbiBhbGwgbW9kdWxlIGV2ZW50c1xyXG4gKi9cclxuLy8gQGR5bmFtaWNcclxuZXhwb3J0IGNsYXNzIFNlcmllc1N0eWxlUHJvdmlkZXIge1xyXG4gICAgcHJpdmF0ZSBzdGF0aWMgX2luc3RhbmNlOiBTZXJpZXNTdHlsZVByb3ZpZGVyID0gbnVsbDtcclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIExJTkU6IG51bWJlciA9IDA7XHJcbiAgICBwdWJsaWMgc3RhdGljIExJTkVfU09MSUQ6IG51bWJlciA9IDE7XHJcbiAgICBwdWJsaWMgc3RhdGljIExJTkVfRE9UVEVEOiBudW1iZXIgPSAyO1xyXG4gICAgcHVibGljIHN0YXRpYyBMSU5FX0RBU0hFRDogbnVtYmVyID0gMztcclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIEFSRUE6IG51bWJlciA9IDEwO1xyXG4gICAgcHVibGljIHN0YXRpYyBBUkVBX1NPTElEOiBudW1iZXIgPSAxMTtcclxuICAgIHB1YmxpYyBzdGF0aWMgQVJFQV9EQVNIRUQ6IG51bWJlciA9IDEyO1xyXG4gICAgcHVibGljIHN0YXRpYyBBUkVBX0RBU0hFRDQ1OiBudW1iZXIgPSAxMztcclxuICAgIHB1YmxpYyBzdGF0aWMgQVJFQV9EQVNIRUQxMzU6IG51bWJlciA9IDE0O1xyXG4gICAgcHVibGljIHN0YXRpYyBBUkVBX0RPVFRFRDogbnVtYmVyID0gMTU7XHJcblxyXG5cclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX0JMSVpaQVJEX0JMVUU6IHN0cmluZyA9IFwiQ09OVF9BUkVBX0JMSVpaQVJEX0JMVUVcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX0NPVFRPTl9DQU5EWTogc3RyaW5nID0gXCJDT05UX0FSRUFfQ09UVE9OX0NBTkRZXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9BUkVBX1BFUkFOTzogc3RyaW5nID0gXCJEQVNIRURfQVJFQV9QRVJBTk9cIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX0FSRUFfU0VBX1BJTks6IHN0cmluZyA9IFwiREFTSEVEX0FSRUFfU0VBX1BJTktcIjtcclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9CTEFDSzogc3RyaW5nID0gXCJDT05UX0FSRUFfQkxBQ0tcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX0dSRVk6IHN0cmluZyA9IFwiQ09OVF9BUkVBX0dSRVlcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX0xJR0hUX0dSRVk6IHN0cmluZyA9IFwiQ09OVF9BUkVBX0xJR0hUX0dSRVlcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX1JFRDogc3RyaW5nID0gXCJDT05UX0FSRUFfUkVEXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9JTkRJQU5fUkVEOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9JTkRJQU5fUkVEXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9QSU5LOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9QSU5LXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9PUkFOR0U6IHN0cmluZyA9IFwiQ09OVF9BUkVBX09SQU5HRVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfUEVBQ0hfUFVGRjogc3RyaW5nID0gXCJDT05UX0FSRUFfUEVBQ0hfUFVGRlwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfWUVMTE9XOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9ZRUxMT1dcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9BUkVBX0dSRUVOOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9HUkVFTlwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfTElNRTogc3RyaW5nID0gXCJDT05UX0FSRUFfTElNRVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfTElHSFRfR1JFRU46IHN0cmluZyA9IFwiQ09OVF9BUkVBX0xJR0hUX0dSRUVOXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9CTFVFOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9CTFVFXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9TVEVFTF9CTFVFOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9TVEVFTF9CTFVFXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9MSUdIVF9CTFVFOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9MSUdIVF9CTFVFXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9MSUdIVF9DWUFOOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9MSUdIVF9DWUFOXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9NQUdFTlRBOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9NQUdFTlRBXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9QVVJQTEU6IHN0cmluZyA9IFwiQ09OVF9BUkVBX1BVUlBMRVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfVklPTEVUOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9WSU9MRVRcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX0NPUkFMX0FSRUE6IHN0cmluZyA9IFwiREFTSEVEX0NPUkFMX0FSRUFcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX0JMVUVfQVJFQTogc3RyaW5nID0gXCJEQVNIRURfQkxVRV9BUkVBXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9BUVVBX0FSRUE6IHN0cmluZyA9IFwiREFTSEVEX0FRVUFfQVJFQVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBEQVNIRURfREVFUF9QSU5LX0FSRUE6IHN0cmluZyA9IFwiREFTSEVEX0RFRVBfUElOS19BUkVBXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9HT0xERU5fUk9EX0FSRUE6IHN0cmluZyA9IFwiREFTSEVEX0dPTERFTl9ST0RfQVJFQVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBEQVNIRURfR1JFRU5fQVJFQTogc3RyaW5nID0gXCJEQVNIRURfR1JFRU5fQVJFQVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBET1RURURfR1JFRU5fWUVMTE9XX0FSRUE6IHN0cmluZyA9IFwiRE9UVEVEX0dSRUVOX1lFTExPV19BUkVBXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERPVFRFRF9JTkRJQU5fUkVEX0FSRUE6IHN0cmluZyA9IFwiRE9UVEVEX0lORElBTl9SRURfQVJFQVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBET1RURURfTUFHRU5UQV9BUkVBOiBzdHJpbmcgPSBcIkRPVFRFRF9NQUdFTlRBX0FSRUFcIjtcclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9BTlRJUVVFX1dISVRFOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9BTlRJUVVFX1dISVRFXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9BUFJJQ09UX1BFQUNIOiBzdHJpbmcgPSBcIkNPTlRfQVJFQV9BUFJJQ09UX1BFQUNIXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9OQVZBSk9fV0hJVEU6IHN0cmluZyA9IFwiQ09OVF9BUkVBX05BVkFKT19XSElURVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX0FSRUFfUk9TRV9GT0c6IHN0cmluZyA9IFwiQ09OVF9BUkVBX1JPU0VfRk9HXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfQVJFQV9EQVJLX1NBTE1PTjogc3RyaW5nID0gXCJDT05UX0FSRUFfREFSS19TQUxNT05cIjtcclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfU0VHTUVOVF9MWU5DSDogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfTFlOQ0hcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX1NFR01FTlRfU0FOX01BUklOTzogc3RyaW5nID0gXCJEQVNIRURfU0VHTUVOVF9TQU5fTUFSSU5PXCI7XHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX1NFR01FTlRfQkxBQ0s6IHN0cmluZyA9IFwiQ09OVF9TRUdNRU5UX0JMQUNLXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9TRUdNRU5UX0JMQUNLOiBzdHJpbmcgPSBcIkRBU0hFRF9TRUdNRU5UX0JMQUNLXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERPVFRFRF9TRUdNRU5UX0JMQUNLOiBzdHJpbmcgPSBcIkRPVFRFRF9TRUdNRU5UX0JMQUNLXCI7XHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX1NFR01FTlRfR1JFWTogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfR1JFWVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBEQVNIRURfU0VHTUVOVF9HUkVZOiBzdHJpbmcgPSBcIkRBU0hFRF9TRUdNRU5UX0dSRVlcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgRE9UVEVEX1NFR01FTlRfR1JFWTogc3RyaW5nID0gXCJET1RURURfU0VHTUVOVF9HUkVZXCI7XHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX1NFR01FTlRfUkVEOiBzdHJpbmcgPSBcIkNPTlRfU0VHTUVOVF9SRURcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX1NFR01FTlRfUkVEOiBzdHJpbmcgPSBcIkRBU0hFRF9TRUdNRU5UX1JFRFwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBET1RURURfU0VHTUVOVF9SRUQ6IHN0cmluZyA9IFwiRE9UVEVEX1NFR01FTlRfUkVEXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfU0VHTUVOVF9CT0xEX1JFRDogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfQk9MRF9SRURcIjtcclxuXHJcblxyXG5cclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9TRUdNRU5UX1lFTExPVzogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfWUVMTE9XXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9TRUdNRU5UX1lFTExPVzogc3RyaW5nID0gXCJEQVNIRURfU0VHTUVOVF9ZRUxMT1dcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgRE9UVEVEX1NFR01FTlRfWUVMTE9XOiBzdHJpbmcgPSBcIkRPVFRFRF9TRUdNRU5UX1lFTExPV1wiO1xyXG5cclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfU0VHTUVOVF9HUkVFTjogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfR1JFRU5cIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX1NFR01FTlRfR1JFRU46IHN0cmluZyA9IFwiREFTSEVEX1NFR01FTlRfR1JFRU5cIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgRE9UVEVEX1NFR01FTlRfR1JFRU46IHN0cmluZyA9IFwiRE9UVEVEX1NFR01FTlRfR1JFRU5cIjtcclxuXHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX1NFR01FTlRfQkxVRTogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfQkxVRVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBEQVNIRURfU0VHTUVOVF9CTFVFOiBzdHJpbmcgPSBcIkRBU0hFRF9TRUdNRU5UX0JMVUVcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgRE9UVEVEX1NFR01FTlRfQkxVRTogc3RyaW5nID0gXCJET1RURURfU0VHTUVOVF9CTFVFXCI7XHJcblxyXG5cclxuICAgIHB1YmxpYyBzdGF0aWMgQ09OVF9TRUdNRU5UX09SQU5HRTogc3RyaW5nID0gXCJDT05UX1NFR01FTlRfT1JBTkdFXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9TRUdNRU5UX09SQU5HRTogc3RyaW5nID0gXCJEQVNIRURfU0VHTUVOVF9PUkFOR0VcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgRE9UVEVEX1NFR01FTlRfT1JBTkdFOiBzdHJpbmcgPSBcIkRPVFRFRF9TRUdNRU5UX09SQU5HRVwiO1xyXG5cclxuXHJcbiAgICBwdWJsaWMgc3RhdGljIENPTlRfU0VHTUVOVF9NQUdFTlRBOiBzdHJpbmcgPSBcIkNPTlRfU0VHTUVOVF9NQUdFTlRBXCI7XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9TRUdNRU5UX01BR0VOVEE6IHN0cmluZyA9IFwiREFTSEVEX1NFR01FTlRfTUFHRU5UQVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBET1RURURfU0VHTUVOVF9NQUdFTlRBOiBzdHJpbmcgPSBcIkRPVFRFRF9TRUdNRU5UX01BR0VOVEFcIjtcclxuXHJcblxyXG4gICAgcHVibGljIHN0YXRpYyBDT05UX1NFR01FTlRfUFVSUExFOiBzdHJpbmcgPSBcIkNPTlRfU0VHTUVOVF9QVVJQTEVcIjtcclxuICAgIHB1YmxpYyBzdGF0aWMgREFTSEVEX1NFR01FTlRfUFVSUExFOiBzdHJpbmcgPSBcIkRBU0hFRF9TRUdNRU5UX1BVUlBMRVwiO1xyXG4gICAgcHVibGljIHN0YXRpYyBET1RURURfU0VHTUVOVF9QVVJQTEU6IHN0cmluZyA9IFwiRE9UVEVEX1NFR01FTlRfUFVSUExFXCI7XHJcbiAgICAvL1RPRE86IHdhaXRpbmcgZm9yIFNUTCBvcGluaW9uIHRvIHB1dCB0aGUgcmVxdWllcmVkIGNvbG9ycyBcclxuICAgIHB1YmxpYyBzdGF0aWMgU0lNSUxBUl9DT0xPUlMgPSBbW1wiQ09OVF9TRUdNRU5UX09SQU5HRVwiLCBcIkNPTlRfQVJFQV9OQVZBSk9fV0hJVEVcIl0sXHJcbiAgICBbXCJEQVNIRURfU0VHTUVOVF9PUkFOR0VcIiwgXCJDT05UX0FSRUFfTkFWQUpPX1dISVRFXCJdLFxyXG4gICAgW1wiRE9UVEVEX1NFR01FTlRfT1JBTkdFXCIsIFwiQ09OVF9BUkVBX05BVkFKT19XSElURVwiXSxcclxuICAgIFtcIkNPTlRfU0VHTUVOVF9QVVJQTEVcIiwgXCJDT05UX0FSRUFfREFSS19TQUxNT05cIl0sXHJcbiAgICBbXCJEQVNIRURfU0VHTUVOVF9QVVJQTEVcIiwgXCJDT05UX0FSRUFfREFSS19TQUxNT05cIl0sXHJcbiAgICBbXCJET1RURURfU0VHTUVOVF9QVVJQTEVcIiwgXCJDT05UX0FSRUFfREFSS19TQUxNT05cIl1cclxuICAgIF07XHJcbiAgICBwdWJsaWMgc3RhdGljIERBU0hFRF9QQVRURVJOX1JFQUxfQ09MT1I6IE9iamVjdCA9IHtcclxuICAgICAgICBcIkRBU0hFRF9BUkVBX1BFUkFOT1wiOiBcIiM3NDhERjJcIixcclxuICAgICAgICBcIkRBU0hFRF9DT1JBTF9BUkVBXCI6IFwiI0U2NzU1RVwiLFxyXG4gICAgICAgIFwiREFTSEVEX0JMVUVfQVJFQVwiOiBcIiMwMDAwRkZcIixcclxuICAgICAgICBcIkRBU0hFRF9BUVVBX0FSRUFcIjogXCIjMDBGRkZGXCIsXHJcbiAgICAgICAgXCJEQVNIRURfREVFUF9QSU5LX0FSRUFcIjogXCIjRjUxRkNBXCIsXHJcbiAgICAgICAgXCJEQVNIRURfR09MREVOX1JPRF9BUkVBXCI6IFwiI0Y3QkUxRlwiLFxyXG4gICAgICAgIFwiREFTSEVEX0dSRUVOX0FSRUFcIjogXCIjMDA4MDAwXCIsXHJcbiAgICAgICAgXCJET1RURURfR1JFRU5fWUVMTE9XX0FSRUFcIjogXCIjOTBEQzEyXCIsXHJcbiAgICAgICAgXCJET1RURURfSU5ESUFOX1JFRF9BUkVBXCI6IFwiI0UxODQ3MFwiLFxyXG4gICAgICAgIFwiRE9UVEVEX01BR0VOVEFfQVJFQVwiOiBcIiNGRjAwRkZcIlxyXG4gICAgfTtcclxuXHJcblxyXG4gICAgcHVibGljIGFsbFN0eWxlcyA9IFtdO1xyXG5cclxuICAgIC8qKlxyXG4gICAgICogR2V0IG9uZSBpbnN0YW5jZSBvZiB0aGUgU2VyaWVzU3R5bGVQcm92aWRlciBjbGFzc1xyXG4gICAgICogKi9cclxuICAgIHB1YmxpYyBzdGF0aWMgZ2V0IGluc3RhbmNlKCk6IFNlcmllc1N0eWxlUHJvdmlkZXIge1xyXG4gICAgICAgIGlmICh0aGlzLl9pbnN0YW5jZSA9PSBudWxsKVxyXG4gICAgICAgICAgICB0aGlzLl9pbnN0YW5jZSA9IG5ldyBTZXJpZXNTdHlsZVByb3ZpZGVyKCk7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2luc3RhbmNlO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogUHJpdmF0ZSBjb25zdHJ1Y3RvcjogaW5pdGlhbGl6ZSBzdHlsZXNcclxuICAgICAqICovXHJcbiAgICBjb25zdHJ1Y3RvciAoKSB7XHJcbiAgICAgICAgLy9Db250IEJsaXp6YXJkIEJsdWVcclxuICAgICAgICB2YXIgY29udEFyZWFCbGl6YXJkQmx1ZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQkxJWlpBUkRfQkxVRSwvKkV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2V2YWwnLCdDT05UX0FSRUFfQkxJWlpBUkRfQkxVRScpKi8nQXJlYSBCbGl6emVkIEJsdWUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhQmxpemFyZEJsdWUuY29sb3IgPSAnIzlCRERFOCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQkxJWlpBUkRfQkxVRV0gPSBjb250QXJlYUJsaXphcmRCbHVlO1xyXG5cclxuICAgICAgICAvL0NvbnQgQ290dG9uIENhbmR5XHJcbiAgICAgICAgdmFyIGNvbnRBcmVhQ290dG9uQ2FuZHk6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0NPVFRPTl9DQU5EWSwgJ0FyZWEgQ290dG9uIENhbmR5JywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBcclxuICAgICAgICBjb250QXJlYUNvdHRvbkNhbmR5LmNvbG9yID0gJyNGRkI3Q0QnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0NPVFRPTl9DQU5EWV0gPSAgY29udEFyZWFDb3R0b25DYW5keTtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhQmxhY2s6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0JMQUNLLCAnQmxhY2sgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFCbGFjay5jb2xvciA9ICcjMDAwMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0JMQUNLXSA9IGNvbnRBcmVhQmxhY2s7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUdyZXk6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0dSRVksICdHcmV5IEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhR3JleS5jb2xvciA9ICcjODA4MDgwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9HUkVZXSA9IGNvbnRBcmVhR3JleTtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhTGlnaHRHcmV5OiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9MSUdIVF9HUkVZLCAnTGlnaHQgR3JleSBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYUxpZ2h0R3JleS5jb2xvciA9ICcjRDNEM0QzJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9MSUdIVF9HUkVZXSA9IGNvbnRBcmVhTGlnaHRHcmV5O1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFSZWQ6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1JFRCwgJ1JlZCBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYVJlZC5jb2xvciA9ICcjRkYwMDAwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9SRURdID0gY29udEFyZWFSZWQ7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUluZGlhblJlZDogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfSU5ESUFOX1JFRCwgJ1JlZCBJbmRpYW4gQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFJbmRpYW5SZWQuY29sb3IgPSAnI0ZDRDVDNUMnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0lORElBTl9SRURdID0gY29udEFyZWFJbmRpYW5SZWQ7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYVBpbms6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1BJTkssICdQaW5rIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhUGluay5jb2xvciA9ICcjRkZDMENCJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9QSU5LXSA9IGNvbnRBcmVhUGluaztcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhT3JhbmdlOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9PUkFOR0UsICdPcmFuZ2UgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFPcmFuZ2UuY29sb3IgPSAnI0ZGQTUwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfT1JBTkdFXSA9IGNvbnRBcmVhT3JhbmdlO1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFQZWFjaFB1ZmY6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1BFQUNIX1BVRkYsICdQZWFjaCBQdWZmIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhUGVhY2hQdWZmLmNvbG9yID0gJyNGRkRBQjknO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1BFQUNIX1BVRkZdID0gY29udEFyZWFQZWFjaFB1ZmY7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYVllbGxvdzogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfWUVMTE9XLCAnWWVsbG93IEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhWWVsbG93LmNvbG9yID0gJyNGRkZGMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1lFTExPV10gPSBjb250QXJlYVllbGxvdztcclxuXHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUdyZWVuOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9HUkVFTiwgJ0dyZWVuIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhR3JlZW4uY29sb3IgPSAnIzAwODAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfR1JFRU5dID0gY29udEFyZWFHcmVlbjtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhTGltZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfTElNRSwgJ0xpbWUgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFMaW1lLmNvbG9yID0gJyMwMGZmMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0xJTUVdID1jb250QXJlYUxpbWU7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUxpZ2h0R3JlZW46IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0xJR0hUX0dSRUVOLCAnTGlnaHQgR3JlZW4gQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFMaWdodEdyZWVuLmNvbG9yID0gJyM5MEVFOTAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0xJR0hUX0dSRUVOXSA9IGNvbnRBcmVhTGlnaHRHcmVlbjtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhQmx1ZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQkxVRSwgJ0JsdWUgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFCbHVlLmNvbG9yID0gJyMwMDAwRkYnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0JMVUVdID0gY29udEFyZWFCbHVlO1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFTdGVlbEJsdWU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1NURUVMX0JMVUUsICdTdGVlbCBCbHVlIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhU3RlZWxCbHVlLmNvbG9yID0gJyM0NjgyQjQnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1NURUVMX0JMVUVdID0gY29udEFyZWFTdGVlbEJsdWU7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUxpZ2h0Qmx1ZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfTElHSFRfQkxVRSwgJ0xpZ2h0IEJsdWUgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFMaWdodEJsdWUuY29sb3IgPSAnI0FERDhFNic7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfTElHSFRfQkxVRV0gPWNvbnRBcmVhTGlnaHRCbHVlO1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFMaWdodEN5YW46IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0xJR0hUX0NZQU4sICdMaWdodCBDeWFuIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhTGlnaHRDeWFuLmNvbG9yID0gJyNFMEZGRkYnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0xJR0hUX0NZQU5dID1jb250QXJlYUxpZ2h0Q3lhbjtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhTWFnZW50YTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfTUFHRU5UQSwgJ01hZ2VudGEgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFNYWdlbnRhLmNvbG9yID0gJyNGRjAwRkYnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX01BR0VOVEFdID0gY29udEFyZWFNYWdlbnRhO1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFQdXJwbGU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1BVUlBMRSwgJ1B1cnBsZSBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYVB1cnBsZS5jb2xvciA9ICcjODAwMDgwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9QVVJQTEVdID0gY29udEFyZWFQdXJwbGU7XHJcblxyXG4gICAgICAgIC8vQ29udCBWaW9sZXRcclxuICAgICAgICB2YXIgY29udEFyZWFWaW9sZXQ6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX1ZJT0xFVCwgJ1Zpb2xldCBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYVZpb2xldC5jb2xvciA9ICcjRUU4MkVFJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9WSU9MRVRdID0gY29udEFyZWFWaW9sZXQ7XHJcblxyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFBbnRpcXVlV2hpdGU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0FOVElRVUVfV0hJVEUsICdBbnRpcXVlIFdoaXRlIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhQW50aXF1ZVdoaXRlLmNvbG9yID0gJyNGREVCREQnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX0FOVElRVUVfV0hJVEVdID0gY29udEFyZWFBbnRpcXVlV2hpdGU7XHJcblxyXG4gICAgICAgIHZhciBjb250QXJlYUFwcmljb3RQZWFjaDogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQVBSSUNPVF9QRUFDSCwgJ0Fwcmljb3QgUGVhY2ggQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9TT0xJRCk7XHJcbiAgICAgICAgY29udEFyZWFBcHJpY290UGVhY2guY29sb3IgPSAnI0ZCRDdCQic7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQVBSSUNPVF9QRUFDSF0gPSBjb250QXJlYUFwcmljb3RQZWFjaDtcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhTmF2YWpvV2hpdGU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX05BVkFKT19XSElURSwgJ05hdmFqbyBXaGl0ZSBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYU5hdmFqb1doaXRlLmNvbG9yID0gJyNGOUM0OTknO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9BUkVBX05BVkFKT19XSElURV0gPSBjb250QXJlYU5hdmFqb1doaXRlO1xyXG5cclxuICAgICAgICB2YXIgY29udEFyZWFSb3NlRm9nOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9ST1NFX0ZPRywgJ1Jvc2UgRm9nIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfU09MSUQpO1xyXG4gICAgICAgIGNvbnRBcmVhUm9zZUZvZy5jb2xvciA9ICcjRTVCOUI1JztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfQVJFQV9ST1NFX0ZPR10gPWNvbnRBcmVhUm9zZUZvZztcclxuXHJcbiAgICAgICAgdmFyIGNvbnRBcmVhRGFya1NhbG1vbjogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfREFSS19TQUxNT04sICdEYXJrIFNhbG1vbiBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX1NPTElEKTtcclxuICAgICAgICBjb250QXJlYURhcmtTYWxtb24uY29sb3IgPSAnI0Q5OTY5MCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfREFSS19TQUxNT05dID0gY29udEFyZWFEYXJrU2FsbW9uO1xyXG5cclxuXHJcbiAgICAgICAgLy9EYXNoZWQgQXJlYSBQZXJhbm9cclxuICAgICAgICB2YXIgZGFzaGVkQXJlYVBlcmFubzogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfQVJFQV9QRVJBTk8sICdEYXNoZWQgQXJlYSBQZXJhbm8nLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfREFTSEVENDUsIFwiYS5wbmdcIik7XHJcbiAgICAgICAgZGFzaGVkQXJlYVBlcmFuby5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgZGFzaGVkQXJlYVBlcmFuby5pc0JpdG1hcCA9IHRydWU7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfQVJFQV9QRVJBTk9dID0gZGFzaGVkQXJlYVBlcmFubztcclxuXHJcbiAgICAgICAgLy9EYXNoZWQgQ29yYWwgQXJlYSBcclxuICAgICAgICB2YXIgZGFzaGVkQXJlYUNvcmFsOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9DT1JBTF9BUkVBLCAnRGFzaGVkIENvcmFsIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfREFTSEVENDUsIFwiYi5wbmdcIik7XHJcbiAgICAgICAgZGFzaGVkQXJlYUNvcmFsLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBkYXNoZWRBcmVhQ29yYWwuaXNCaXRtYXAgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX0NPUkFMX0FSRUFdID0gZGFzaGVkQXJlYUNvcmFsO1xyXG5cclxuXHJcblxyXG5cclxuICAgICAgICAvL0Rhc2hlZCBCbHVlIEFyZWEgXHJcbiAgICAgICAgdmFyIGRhc2hlZEFyZWFCbHVlOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9CTFVFX0FSRUEsICdEYXNoZWQgQmx1ZSBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX0RBU0hFRDQ1LCBcImJsdWVfNDUucG5nXCIpO1xyXG4gICAgICAgIGRhc2hlZEFyZWFCbHVlLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBkYXNoZWRBcmVhQmx1ZS5pc0JpdG1hcCA9IHRydWU7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfQkxVRV9BUkVBXSA9IGRhc2hlZEFyZWFCbHVlO1xyXG5cclxuICAgICAgICAvL0Rhc2hlZCBBcXVhIEFyZWEgXHJcbiAgICAgICAgdmFyIGRhc2hlZEFyZWFBcXVhOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9BUVVBX0FSRUEsICdEYXNoZWQgQXF1YSBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX0RBU0hFRDQ1LCBcImFxdWFfNDUucG5nXCIpO1xyXG4gICAgICAgIGRhc2hlZEFyZWFBcXVhLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBkYXNoZWRBcmVhQXF1YS5pc0JpdG1hcCA9IHRydWU7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfQVFVQV9BUkVBXSA9IGRhc2hlZEFyZWFBcXVhO1xyXG5cclxuICAgICAgICAvL0Rhc2hlZCBEZWVwIFBpbmsgQXJlYSBcclxuICAgICAgICB2YXIgZGFzaGVkQXJlYURlZXBQaW5rOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9ERUVQX1BJTktfQVJFQSwgJ0Rhc2hlZCBEZWVwIFBpbmsgQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9EQVNIRUQ0NSwgXCJkZWVwcGlua180NS5wbmdcIik7XHJcbiAgICAgICAgZGFzaGVkQXJlYURlZXBQaW5rLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBkYXNoZWRBcmVhRGVlcFBpbmsuaXNCaXRtYXAgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX0RFRVBfUElOS19BUkVBXSA9IGRhc2hlZEFyZWFEZWVwUGluaztcclxuXHJcbiAgICAgICAgLy9EYXNoZWQgR29sZGVuIFJvZCBBcmVhIFxyXG4gICAgICAgIHZhciBkYXNoZWRBcmVhR29sZGVuUm9kOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9HT0xERU5fUk9EX0FSRUEsICdEYXNoZWQgR29sZGVuIFJvZCBBcmVhJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX0RBU0hFRCwgXCJnb2xkZW5yb2QucG5nXCIpO1xyXG4gICAgICAgIGRhc2hlZEFyZWFHb2xkZW5Sb2QuZm9ybSA9IFwic2VnbWVudFwiO1xyXG4gICAgICAgIGRhc2hlZEFyZWFHb2xkZW5Sb2QuaXNCaXRtYXAgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX0dPTERFTl9ST0RfQVJFQV0gPSBkYXNoZWRBcmVhR29sZGVuUm9kO1xyXG5cclxuICAgICAgICAvL0Rhc2hlZCBHcmVlbiBBcmVhIFxyXG4gICAgICAgIHZhciBkYXNoZWRBcmVhR3JlZW46IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX0dSRUVOX0FSRUEsICdEYXNoZWQgR3JlZW4gQXJlYScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9EQVNIRUQsIFwiZ3JlZW4ucG5nXCIpO1xyXG4gICAgICAgIGRhc2hlZEFyZWFHcmVlbi5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgZGFzaGVkQXJlYUdyZWVuLmlzQml0bWFwID0gdHJ1ZTtcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9HUkVFTl9BUkVBXSA9IGRhc2hlZEFyZWFHcmVlbjtcclxuXHJcbiAgICAgICAgLy9EYXNoZWQgR3JlZW4gWWVsbG93IEFyZWEgXHJcbiAgICAgICAgdmFyIGRvdHRlZEFyZWFHcmVlblllbGxvdzogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfR1JFRU5fWUVMTE9XX0FSRUEsICdEb3R0ZWQgR3JlZW4gWWVsbG93IEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfRE9UVEVELCBcImdyZWVueWVsbG93LnBuZ1wiKTtcclxuICAgICAgICBkb3R0ZWRBcmVhR3JlZW5ZZWxsb3cuZm9ybSA9IFwic2VnbWVudFwiO1xyXG4gICAgICAgIGRvdHRlZEFyZWFHcmVlblllbGxvdy5pc0JpdG1hcCA9IHRydWU7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfR1JFRU5fWUVMTE9XX0FSRUFdID0gZG90dGVkQXJlYUdyZWVuWWVsbG93O1xyXG5cclxuICAgICAgICAvL0Rhc2hlZCBJbmRpYW4gUmVkIEFyZWEgXHJcbiAgICAgICAgdmFyIGRvdHRlZEFyZWFJbmRpYW5SZWQ6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX0lORElBTl9SRURfQVJFQSwgJ0RvdHRlZCBJbmRpYW4gUmVkIEFyZWEnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfRE9UVEVELCBcImluZGlhbnJlZC5wbmdcIik7XHJcbiAgICAgICAgZG90dGVkQXJlYUluZGlhblJlZC5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgZG90dGVkQXJlYUluZGlhblJlZC5pc0JpdG1hcCA9IHRydWU7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfSU5ESUFOX1JFRF9BUkVBXSA9IGRvdHRlZEFyZWFJbmRpYW5SZWQ7XHJcblxyXG4gICAgICAgIC8vRG90dGVkIE1hZ2VudGEgQXJlYSBcclxuICAgICAgICB2YXIgZG90dGVkQXJlYU1hZ2VudGE6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX01BR0VOVEFfQVJFQSwgJ0RvdHRlZCBNYWdlbnRhIEFyZWEgJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX0RPVFRFRCwgXCJtYWdlbnRhLnBuZ1wiKTtcclxuICAgICAgICBkb3R0ZWRBcmVhTWFnZW50YS5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgZG90dGVkQXJlYU1hZ2VudGEuaXNCaXRtYXAgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX01BR0VOVEFfQVJFQV0gPSBkb3R0ZWRBcmVhTWFnZW50YTtcclxuXHJcblxyXG5cclxuXHJcbiAgICAgICAgLy9Db250IExpbmUgQmxhY2tcclxuICAgICAgICB2YXIgY29udFNlZ21lbnRCbGFjazogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfQkxBQ0ssICdCbGFjayBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5BUkVBX0RPVFRFRCk7XHJcbiAgICAgICAgY29udFNlZ21lbnRCbGFjay5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgY29udFNlZ21lbnRCbGFjay5jb2xvciA9ICcjMDAwMDAwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9CTEFDS10gPSBjb250U2VnbWVudEJsYWNrO1xyXG5cclxuICAgICAgICAvL0JsYWNrIERhc2hlZCBMaW5lIFxyXG4gICAgICAgIHZhciBkYXNoZWRTZWdtZW50QmxhY2s6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfQkxBQ0ssICdCbGFjayBEYXNoZWQgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuTElORV9EQVNIRUQpO1xyXG4gICAgICAgIGRhc2hlZFNlZ21lbnRCbGFjay5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50QmxhY2suY29sb3IgPSAnIzAwMDAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9CTEFDS10gPSBkYXNoZWRTZWdtZW50QmxhY2s7XHJcbiAgICAgICAgLy9CbGFjayBEb3R0ZWQgTGluZSBcclxuICAgICAgICB2YXIgZG90dGVkU2VnbWVudEJsYWNrOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRPVFRFRF9TRUdNRU5UX0JMQUNLLCAnQmxhY2sgRG90dGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfRE9UVEVEKTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50QmxhY2suZGFzaFBhdHRlcm4gPSBbMSwgMSwgMSwgMV07XHJcbiAgICAgICAgZG90dGVkU2VnbWVudEJsYWNrLmNvbG9yID0gJyMwMDAwMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfQkxBQ0tdID0gZG90dGVkU2VnbWVudEJsYWNrO1xyXG5cclxuICAgICAgICAvL0NvbnQgUmVkIExpbmUgXHJcbiAgICAgICAgdmFyIGNvbnRTZWdtZW50UmVkOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9SRUQsICdSZWQgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50UmVkLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBjb250U2VnbWVudFJlZC5jb2xvciA9ICcjRkYwMDAwJzt0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9SRURdID0gY29udFNlZ21lbnRSZWQ7XHJcbiAgICAgICAgLy9Db250ICBCb2xkIFJlZCBMaW5lIFxyXG4gICAgICAgIHZhciBjb250U2VnbWVudEJvbGRSZWQ6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9TRUdNRU5UX0JPTERfUkVELCAnQm9sZCBSZWQgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50Qm9sZFJlZC5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgY29udFNlZ21lbnRCb2xkUmVkLmNvbG9yID0gJyNGRjAwMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9TRUdNRU5UX0JPTERfUkVEXSA9IGNvbnRTZWdtZW50Qm9sZFJlZDtcclxuICAgICAgICAvL1JlZCBEYXNoZWQgTGluZSBcclxuICAgICAgICB2YXIgZGFzaGVkU2VnbWVudFJlZDogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9SRUQsICdSZWQgRGFzaGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfREFTSEVEKTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50UmVkLmRhc2hQYXR0ZXJuID0gWzUsIDUsIDUsIDVdO1xyXG4gICAgICAgIGRhc2hlZFNlZ21lbnRSZWQuY29sb3IgPSAnI0ZGMDAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9SRURdID0gZGFzaGVkU2VnbWVudFJlZDtcclxuICAgICAgICAvL1JlZCBEb3R0ZWQgTGluZSBcclxuICAgICAgICB2YXIgZG90dGVkU2VnbWVudFJlZDogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9SRUQsICdSZWQgRG90dGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfRE9UVEVEKTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50UmVkLmRhc2hQYXR0ZXJuID0gWzEsIDEsIDEsIDFdO1xyXG4gICAgICAgIGRvdHRlZFNlZ21lbnRSZWQuY29sb3IgPSAnI0ZGMDAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9SRURdID0gZG90dGVkU2VnbWVudFJlZDtcclxuXHJcbiAgICAgICAgLy9Db250IEdyZWVuIExpbmUgXHJcbiAgICAgICAgdmFyIGNvbnRTZWdtZW50R3JlZW46IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9TRUdNRU5UX0dSRUVOLCAnR3JlZW4gTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50R3JlZW4uZm9ybSA9IFwic2VnbWVudFwiO1xyXG4gICAgICAgIGNvbnRTZWdtZW50R3JlZW4uY29sb3IgPSAnIzAwODAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfR1JFRU5dID0gY29udFNlZ21lbnRHcmVlbjtcclxuICAgICAgICAvL0dyZWVuIERhc2hlZCBMaW5lIFxyXG4gICAgICAgIHZhciBkYXNoZWRTZWdtZW50R3JlZW46IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfR1JFRU4sICdHcmVlbiBEYXNoZWQgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuTElORV9EQVNIRUQpO1xyXG4gICAgICAgIGRhc2hlZFNlZ21lbnRHcmVlbi5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50R3JlZW4uY29sb3IgPSAnIzAwODAwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9HUkVFTl0gPSBkYXNoZWRTZWdtZW50R3JlZW47XHJcbiAgICAgICAgLy9HcmVlbiBEb3R0ZWQgTGluZSBcclxuICAgICAgICB2YXIgZG90dGVkU2VnbWVudEdyZWVuOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRPVFRFRF9TRUdNRU5UX0dSRUVOLCAnR3JlZW4gRG90dGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfRE9UVEVEKTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50R3JlZW4uZGFzaFBhdHRlcm4gPSBbMSwgMSwgMSwgMV07XHJcbiAgICAgICAgZG90dGVkU2VnbWVudEdyZWVuLmNvbG9yID0gJyMwMDgwMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfR1JFRU5dID1kb3R0ZWRTZWdtZW50R3JlZW47XHJcblxyXG4gICAgICAgIC8vQ29udCBCbHVlIExpbmUgXHJcbiAgICAgICAgdmFyIGNvbnRTZWdtZW50Qmx1ZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfQkxVRSwgJ0JsdWUgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50Qmx1ZS5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgY29udFNlZ21lbnRCbHVlLmNvbG9yID0gJyMwMDAwRkYnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9TRUdNRU5UX0JMVUVdID0gY29udFNlZ21lbnRCbHVlO1xyXG4gICAgICAgIC8vQmx1ZSBEYXNoZWQgTGluZVxyXG4gICAgICAgIHZhciBkYXNoZWRTZWdtZW50Qmx1ZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9CTFVFLCAnQmx1ZSBEYXNoZWQgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuTElORV9EQVNIRUQpO1xyXG4gICAgICAgIGRhc2hlZFNlZ21lbnRCbHVlLmRhc2hQYXR0ZXJuID0gWzUsIDUsIDUsIDVdO1xyXG4gICAgICAgIGRhc2hlZFNlZ21lbnRCbHVlLmNvbG9yID0gJyMwMDAwRkYnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfQkxVRV0gPSBkYXNoZWRTZWdtZW50Qmx1ZTtcclxuICAgICAgICAvL0JsdWUgRG90dGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRvdHRlZFNlZ21lbnRCbHVlOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRPVFRFRF9TRUdNRU5UX0JMVUUsICdCbHVlIERvdHRlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RPVFRFRCk7XHJcbiAgICAgICAgZG90dGVkU2VnbWVudEJsdWUuZGFzaFBhdHRlcm4gPSBbMSwgMSwgMSwgMV07XHJcbiAgICAgICAgZG90dGVkU2VnbWVudEJsdWUuY29sb3IgPSAnIzAwMDBGRic7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9CTFVFXSA9IGRvdHRlZFNlZ21lbnRCbHVlO1xyXG5cclxuICAgICAgICAvL0NvbnQgWWVsbG93IExpbmUgXHJcbiAgICAgICAgdmFyIGNvbnRTZWdtZW50WWVsbG93OiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9ZRUxMT1csICdZZWxsb3cgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50WWVsbG93LmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBjb250U2VnbWVudFllbGxvdy5jb2xvciA9ICcjRkZGRjAwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9ZRUxMT1ddID0gY29udFNlZ21lbnRZZWxsb3c7XHJcbiAgICAgICAgLy9ZZWxsb3cgRGFzaGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRhc2hlZFNlZ21lbnRZZWxsb3c6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfWUVMTE9XLCAnWWVsbG93IERhc2hlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RBU0hFRCk7XHJcbiAgICAgICAgZGFzaGVkU2VnbWVudFllbGxvdy5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50WWVsbG93LmNvbG9yID0gJyNGRkZGMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfWUVMTE9XXSA9IGRhc2hlZFNlZ21lbnRZZWxsb3c7XHJcbiAgICAgICAgLy9ZZWxsb3cgRG90dGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRvdHRlZFNlZ21lbnRZZWxsb3c6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfWUVMTE9XLCAnWWVsbG93IERvdHRlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RPVFRFRCk7XHJcbiAgICAgICAgZG90dGVkU2VnbWVudFllbGxvdy5kYXNoUGF0dGVybiA9IFsxLCAxLCAxLCAxXTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50WWVsbG93LmNvbG9yID0gJyNGRkZGMDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfWUVMTE9XXSA9ZG90dGVkU2VnbWVudFllbGxvdztcclxuXHJcbiAgICAgICAgLy9Db250IE9yYW5nZSBMaW5lIFxyXG4gICAgICAgIHZhciBjb250U2VnbWVudE9yYW5nZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfT1JBTkdFLCAnT3JhbmdlIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfRE9UVEVEKTtcclxuICAgICAgICBjb250U2VnbWVudE9yYW5nZS5mb3JtID0gXCJzZWdtZW50XCI7XHJcbiAgICAgICAgY29udFNlZ21lbnRPcmFuZ2UuY29sb3IgPSAnI0ZGQTUwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfT1JBTkdFXSA9Y29udFNlZ21lbnRPcmFuZ2U7XHJcbiAgICAgICAgLy9PcmFuZ2UgRGFzaGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRhc2hlZFNlZ21lbnRPcmFuZ2U6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfT1JBTkdFLCAnT3JhbmdlIERhc2hlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RBU0hFRCk7XHJcbiAgICAgICAgZGFzaGVkU2VnbWVudE9yYW5nZS5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50T3JhbmdlLmNvbG9yID0gJyNGRkE1MDAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfT1JBTkdFXSA9ZGFzaGVkU2VnbWVudE9yYW5nZTtcclxuICAgICAgICAvL09yYW5nZSBEb3R0ZWQgTGluZSBcclxuICAgICAgICB2YXIgZG90dGVkU2VnbWVudE9yYW5nZTogU2VyaWVzU3R5bGUgPSBuZXcgU2VyaWVzU3R5bGUoU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9PUkFOR0UsICdPcmFuZ2UgRG90dGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfRE9UVEVEKTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50T3JhbmdlLmRhc2hQYXR0ZXJuID0gWzEsIDEsIDEsIDFdO1xyXG4gICAgICAgIGRvdHRlZFNlZ21lbnRPcmFuZ2UuY29sb3IgPSAnI0ZGQTUwMCc7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9PUkFOR0VdID1kb3R0ZWRTZWdtZW50T3JhbmdlO1xyXG5cclxuICAgICAgICAvL0NvbnQgTWFnZW50YSBMaW5lIFxyXG4gICAgICAgIHZhciBjb250U2VnbWVudE1hZ2VudGE6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuQ09OVF9TRUdNRU5UX01BR0VOVEEsICdNYWdlbnRhIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkFSRUFfRE9UVEVEKTtcclxuICAgICAgICBjb250U2VnbWVudE1hZ2VudGEuZm9ybSA9IFwic2VnbWVudFwiO1xyXG4gICAgICAgIGNvbnRTZWdtZW50TWFnZW50YS5jb2xvciA9ICcjRkYwMEZGJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9NQUdFTlRBXSA9Y29udFNlZ21lbnRNYWdlbnRhO1xyXG4gICAgICAgIC8vTWFnZW50YSBEYXNoZWQgTGluZSBcclxuICAgICAgICB2YXIgZGFzaGVkU2VnbWVudE1hZ2VudGE6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfTUFHRU5UQSwgJ01hZ2VudGEgRGFzaGVkIExpbmUnLCBTZXJpZXNTdHlsZVByb3ZpZGVyLkxJTkVfREFTSEVEKTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50TWFnZW50YS5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50TWFnZW50YS5jb2xvciA9ICcjRkYwMEZGJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9TRUdNRU5UX01BR0VOVEFdID1kYXNoZWRTZWdtZW50TWFnZW50YTtcclxuICAgICAgICAvL01hZ2VudGEgRG90dGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRvdHRlZFNlZ21lbnRNYWdlbnRhOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkRPVFRFRF9TRUdNRU5UX01BR0VOVEEsICdNYWdlbnRhIERvdHRlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RPVFRFRCk7XHJcbiAgICAgICAgZG90dGVkU2VnbWVudE1hZ2VudGEuZGFzaFBhdHRlcm4gPSBbMSwgMSwgMSwgMV07XHJcbiAgICAgICAgZG90dGVkU2VnbWVudE1hZ2VudGEuY29sb3IgPSAnI0ZGMDBGRic7XHJcbiAgICAgICAgdGhpcy5hbGxTdHlsZXNbU2VyaWVzU3R5bGVQcm92aWRlci5ET1RURURfU0VHTUVOVF9NQUdFTlRBXSA9ZG90dGVkU2VnbWVudE1hZ2VudGE7XHJcblxyXG5cclxuICAgICAgICAvL0NvbnQgUHVycGxlIExpbmUgXHJcbiAgICAgICAgdmFyIGNvbnRTZWdtZW50UHVycGxlOiBTZXJpZXNTdHlsZSA9IG5ldyBTZXJpZXNTdHlsZShTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9QVVJQTEUsICdQdXJwbGUgTGluZScsIFNlcmllc1N0eWxlUHJvdmlkZXIuQVJFQV9ET1RURUQpO1xyXG4gICAgICAgIGNvbnRTZWdtZW50UHVycGxlLmZvcm0gPSBcInNlZ21lbnRcIjtcclxuICAgICAgICBjb250U2VnbWVudFB1cnBsZS5jb2xvciA9ICcjODAwMDgwJztcclxuICAgICAgICB0aGlzLmFsbFN0eWxlc1tTZXJpZXNTdHlsZVByb3ZpZGVyLkNPTlRfU0VHTUVOVF9QVVJQTEVdID0gY29udFNlZ21lbnRQdXJwbGU7XHJcbiAgICAgICAgLy9QdXJwbGUgRGFzaGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRhc2hlZFNlZ21lbnRQdXJwbGU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfUFVSUExFLCAnUHVycGxlIERhc2hlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RBU0hFRCk7XHJcbiAgICAgICAgZGFzaGVkU2VnbWVudFB1cnBsZS5kYXNoUGF0dGVybiA9IFs1LCA1LCA1LCA1XTtcclxuICAgICAgICBkYXNoZWRTZWdtZW50UHVycGxlLmNvbG9yID0gJyM4MDAwODAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuREFTSEVEX1NFR01FTlRfUFVSUExFXSA9IGRhc2hlZFNlZ21lbnRQdXJwbGU7XHJcbiAgICAgICAgLy9QdXJwbGUgRG90dGVkIExpbmUgXHJcbiAgICAgICAgdmFyIGRvdHRlZFNlZ21lbnRQdXJwbGU6IFNlcmllc1N0eWxlID0gbmV3IFNlcmllc1N0eWxlKFNlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfUFVSUExFLCAnUHVycGxlIERvdHRlZCBMaW5lJywgU2VyaWVzU3R5bGVQcm92aWRlci5MSU5FX0RPVFRFRCk7XHJcbiAgICAgICAgZG90dGVkU2VnbWVudFB1cnBsZS5kYXNoUGF0dGVybiA9IFsxLCAxLCAxLCAxXTtcclxuICAgICAgICBkb3R0ZWRTZWdtZW50UHVycGxlLmNvbG9yID0gJyM4MDAwODAnO1xyXG4gICAgICAgIHRoaXMuYWxsU3R5bGVzW1Nlcmllc1N0eWxlUHJvdmlkZXIuRE9UVEVEX1NFR01FTlRfUFVSUExFXSA9IGRvdHRlZFNlZ21lbnRQdXJwbGU7XHJcblxyXG5cclxuXHJcblxyXG5cclxuICAgICAgICAvLyBUT0RPIGNvbXBsZXRlIHRoZSByZXN0XHJcblxyXG5cclxuICAgIH1cclxuXHJcblxyXG4gICAgLyoqXHJcblx0XHQgKiBHZXQgdGhlIHN0eWxlIG5hbWUgZnJvbSBpdHMgSWRcclxuXHRcdCAqICovXHJcblx0XHRwdWJsaWMgc3RhdGljIGdldFN0eWxlTmFtZShzdHlsZUlkOnN0cmluZyk6c3RyaW5ne1xyXG5cdFx0XHR2YXIgc3R5bGU6U2VyaWVzU3R5bGUgPSBTZXJpZXNTdHlsZVByb3ZpZGVyLmluc3RhbmNlLmFsbFN0eWxlc1tzdHlsZUlkXTtcclxuXHRcdFx0cmV0dXJuIHN0eWxlIT1udWxsP3N0eWxlLm5hbWU6bnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy9GSVhNRTpBREQgY29sb3IgdG8gZWFjaCBzdHlsZVxyXG5cdFx0cHVibGljIHN0YXRpYyBnZXRTdHlsZUNvbG9yKHN0eWxlSWQ6c3RyaW5nKTpzdHJpbmd7XHJcblx0XHRcdHZhciBzdHlsZTpTZXJpZXNTdHlsZSA9IFNlcmllc1N0eWxlUHJvdmlkZXIuaW5zdGFuY2UuYWxsU3R5bGVzW3N0eWxlSWRdO1xyXG5cdFx0XHRyZXR1cm4gc3R5bGUhPW51bGw/c3R5bGUuY29sb3I6bnVsbDtcclxuXHRcdH1cclxuICAgICAgICBcclxuICAgICAgICAvL0ZJWE1FOlxyXG5cdFx0cHVibGljIHN0YXRpYyAgZ2V0U3R5bGVDb2xvckFzU3Jpbmcoc3R5bGVJZDpzdHJpbmcpOnN0cmluZ3tcclxuICAgICAgICAgICAgXHJcblx0XHRcdHZhciBzdHlsZTpTZXJpZXNTdHlsZSA9IFNlcmllc1N0eWxlUHJvdmlkZXIuaW5zdGFuY2UuYWxsU3R5bGVzW3N0eWxlSWRdO1xyXG5cdFx0XHRpZihzdHlsZS5pbWFnZU5hbWUgIT0gbnVsbCkge1xyXG5cdFx0XHRcdHJldHVybiBzdHlsZS5pbWFnZU5hbWU7XHJcblx0XHRcdH1lbHNlIHtcclxuXHRcdFx0XHQvLyBpZihzdHlsZS5saW5lU3Ryb2tlICE9IG51bGwpIHtcclxuXHRcdFx0XHQvLyBcdHJldHVybiBoZXgyY3NzKHN0eWxlLmxpbmVTdHJva2UuY29sb3IpO1xyXG5cdFx0XHRcdC8vIH1lbHNlIHtcclxuXHRcdFx0XHQvLyBcdGlmKHN0eWxlLmFyZWFGaWxsICE9IG51bGwpIHtcclxuXHRcdFx0XHQvLyBcdFx0cmV0dXJuIGhleDJjc3Moc3R5bGUuYXJlYUZpbGwuY29sb3IpO1xyXG5cdFx0XHRcdC8vIFx0fWVsc2Uge1xyXG5cdFx0XHRcdC8vIFx0XHRyZXR1cm4gXCJcIjtcclxuXHRcdFx0XHQvLyBcdH1cclxuICAgICAgICAgICAgICAgIC8vIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiBzdHlsZS5jb2xvcjtcclxuXHRcdFx0XHRcclxuXHRcdFx0fVxyXG5cdFx0fVxyXG5cdFx0XHJcblx0XHRwdWJsaWMgc3RhdGljIGdldFN0eWxlQm9yZGVyQ29sb3JBc1NyaW5nKHN0eWxlSWQ6c3RyaW5nKTpzdHJpbmd7XHJcblx0XHRcdHZhciBzdHlsZTpTZXJpZXNTdHlsZSA9IFNlcmllc1N0eWxlUHJvdmlkZXIuaW5zdGFuY2UuYWxsU3R5bGVzW3N0eWxlSWRdO1xyXG5cdFx0XHRpZihzdHlsZS5pbWFnZU5hbWUgIT0gbnVsbCkge1xyXG5cdFx0XHRcdHJldHVybiBTZXJpZXNTdHlsZVByb3ZpZGVyLkRBU0hFRF9QQVRURVJOX1JFQUxfQ09MT1Jbc3R5bGVJZF07XHJcblx0XHRcdH1lbHNlIHtcclxuICAgICAgICAgICAgICAgIC8vRklYTUU6Q0hFQ0sgSUYgTkVFREVEXHJcblx0XHRcdFx0Ly8gaWYoc3R5bGUubGluZVN0cm9rZSAhPSBudWxsKSB7XHJcblx0XHRcdFx0Ly8gXHRyZXR1cm4gaGV4MmNzcyhzdHlsZS5saW5lU3Ryb2tlLmNvbG9yKTtcclxuXHRcdFx0XHQvLyB9ZWxzZSB7XHJcblx0XHRcdFx0Ly8gXHRpZihzdHlsZS5hcmVhRmlsbCAhPSBudWxsKSB7XHJcblx0XHRcdFx0Ly8gXHRcdHJldHVybiBoZXgyY3NzKHN0eWxlLmFyZWFGaWxsLmNvbG9yKTtcclxuXHRcdFx0XHQvLyBcdH1lbHNlIHtcclxuXHRcdFx0XHQvLyBcdFx0cmV0dXJuIFwiXCI7XHJcblx0XHRcdFx0Ly8gXHR9XHJcblx0XHRcdFx0Ly8gfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0eWxlLmNvbG9yO1xyXG5cdFx0XHR9XHJcblx0XHR9XHJcblx0XHRcclxuXHRcdFxyXG5cdFx0XHJcblx0XHRwdWJsaWMgc3RhdGljIGhleDJjc3MoIGNvbG9yVWludCA6IG51bWJlciApIDogc3RyaW5nXHJcblx0XHR7XHJcblx0XHRcdHZhciBjb2xvcjpzdHJpbmcgPSBjb2xvclVpbnQudG9TdHJpbmcoMTYpO1xyXG5cdFx0XHRpZiAoY29sb3IubGVuZ3RoIDwgNil7XHJcblx0XHRcdFx0dmFyIHJhemxpa2E6bnVtYmVyID0gNi1jb2xvci5sZW5ndGg7XHJcblx0XHRcdFx0dmFyIHRlbXBfY29sb3I6c3RyaW5nID0gJyc7XHJcblx0XHRcdFx0Zm9yICh2YXIgaTpudW1iZXI9MDsgaTxyYXpsaWthOyBpKyspe1xyXG5cdFx0XHRcdFx0dGVtcF9jb2xvciArPSAnMCc7XHJcblx0XHRcdFx0fVxyXG5cdFx0XHRcdHRlbXBfY29sb3IgKz0gY29sb3I7XHJcblx0XHRcdFx0Y29sb3IgPSB0ZW1wX2NvbG9yIFxyXG5cdFx0XHR9XHJcblx0XHRcdFx0XHJcblx0XHRcdFx0XHJcblx0XHRcdHJldHVybiBcIiNcIiArIGNvbG9yLnRvVXBwZXJDYXNlKCk7XHJcblx0XHR9XHJcblx0XHRwdWJsaWMgc3RhdGljIGdldFN0eWxlVHlwZShzdHlsZUlkOnN0cmluZyk6TnVtYmVye1xyXG5cdFx0XHR2YXIgc3R5bGU6U2VyaWVzU3R5bGUgPSBTZXJpZXNTdHlsZVByb3ZpZGVyLmluc3RhbmNlLmFsbFN0eWxlc1tzdHlsZUlkXTtcclxuXHRcdFx0cmV0dXJuIHN0eWxlIT1udWxsP3N0eWxlLnR5cGU6LTE7XHJcblx0XHR9XHJcbn0iXX0=