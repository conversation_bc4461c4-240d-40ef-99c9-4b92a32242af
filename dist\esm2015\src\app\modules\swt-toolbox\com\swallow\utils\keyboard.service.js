/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
export class Keyboard {
    constructor() { }
}
Keyboard.BACKSPACE = 8;
Keyboard.CAPS_LOCK = 20;
Keyboard.CONTROL = 17;
Keyboard.DELETE = 46;
Keyboard.DOWN = 40;
Keyboard.END = 35;
Keyboard.ENTER = 13;
Keyboard.ESCAPE = 32;
Keyboard.F1 = 112;
Keyboard.F2 = 113;
Keyboard.F3 = 114;
Keyboard.F4 = 115;
Keyboard.F5 = 116;
Keyboard.F6 = 117;
Keyboard.F7 = 118;
Keyboard.F8 = 119;
Keyboard.F9 = 120;
Keyboard.F10 = 121;
Keyboard.F11 = 122;
Keyboard.F12 = 123;
Keyboard.HOME = 36;
Keyboard.INSERT = 45;
Keyboard.LEFT = 37;
Keyboard.NUMPAD_0 = 96;
Keyboard.NUMPAD_1 = 97;
Keyboard.NUMPAD_2 = 98;
Keyboard.NUMPAD_3 = 99;
Keyboard.NUMPAD_4 = 100;
Keyboard.NUMPAD_5 = 101;
Keyboard.NUMPAD_6 = 102;
Keyboard.NUMPAD_7 = 103;
Keyboard.NUMPAD_8 = 104;
Keyboard.NUMPAD_9 = 105;
Keyboard.NUMPAD_ADD = 107;
Keyboard.NUMPAD_DECIMAL = 110;
Keyboard.NUMPAD_MULTIPLY = 106;
Keyboard.NUMPAD_DEVIDE = 111;
Keyboard.NUMPAD_ENTER = 13;
Keyboard.NUMPAD_SUBTRACT = 109;
Keyboard.PAGE_DOWN = 34;
Keyboard.PAGE_UP = 33;
Keyboard.RIGHT = 39;
Keyboard.SHIFT = 8;
Keyboard.SPACE = 32;
Keyboard.TAB = 9;
Keyboard.UP = 38;
Keyboard.capsLock = 20;
Keyboard.numLock = 114;
Keyboard.decorators = [
    { type: Injectable }
];
/** @nocollapse */
Keyboard.ctorParameters = () => [];
if (false) {
    /** @type {?} */
    Keyboard.BACKSPACE;
    /** @type {?} */
    Keyboard.CAPS_LOCK;
    /** @type {?} */
    Keyboard.CONTROL;
    /** @type {?} */
    Keyboard.DELETE;
    /** @type {?} */
    Keyboard.DOWN;
    /** @type {?} */
    Keyboard.END;
    /** @type {?} */
    Keyboard.ENTER;
    /** @type {?} */
    Keyboard.ESCAPE;
    /** @type {?} */
    Keyboard.F1;
    /** @type {?} */
    Keyboard.F2;
    /** @type {?} */
    Keyboard.F3;
    /** @type {?} */
    Keyboard.F4;
    /** @type {?} */
    Keyboard.F5;
    /** @type {?} */
    Keyboard.F6;
    /** @type {?} */
    Keyboard.F7;
    /** @type {?} */
    Keyboard.F8;
    /** @type {?} */
    Keyboard.F9;
    /** @type {?} */
    Keyboard.F10;
    /** @type {?} */
    Keyboard.F11;
    /** @type {?} */
    Keyboard.F12;
    /** @type {?} */
    Keyboard.F13;
    /** @type {?} */
    Keyboard.F14;
    /** @type {?} */
    Keyboard.F15;
    /** @type {?} */
    Keyboard.HOME;
    /** @type {?} */
    Keyboard.INSERT;
    /** @type {?} */
    Keyboard.LEFT;
    /** @type {?} */
    Keyboard.NUMPAD_0;
    /** @type {?} */
    Keyboard.NUMPAD_1;
    /** @type {?} */
    Keyboard.NUMPAD_2;
    /** @type {?} */
    Keyboard.NUMPAD_3;
    /** @type {?} */
    Keyboard.NUMPAD_4;
    /** @type {?} */
    Keyboard.NUMPAD_5;
    /** @type {?} */
    Keyboard.NUMPAD_6;
    /** @type {?} */
    Keyboard.NUMPAD_7;
    /** @type {?} */
    Keyboard.NUMPAD_8;
    /** @type {?} */
    Keyboard.NUMPAD_9;
    /** @type {?} */
    Keyboard.NUMPAD_ADD;
    /** @type {?} */
    Keyboard.NUMPAD_DECIMAL;
    /** @type {?} */
    Keyboard.NUMPAD_MULTIPLY;
    /** @type {?} */
    Keyboard.NUMPAD_DEVIDE;
    /** @type {?} */
    Keyboard.NUMPAD_ENTER;
    /** @type {?} */
    Keyboard.NUMPAD_SUBTRACT;
    /** @type {?} */
    Keyboard.PAGE_DOWN;
    /** @type {?} */
    Keyboard.PAGE_UP;
    /** @type {?} */
    Keyboard.RIGHT;
    /** @type {?} */
    Keyboard.SHIFT;
    /** @type {?} */
    Keyboard.SPACE;
    /** @type {?} */
    Keyboard.TAB;
    /** @type {?} */
    Keyboard.UP;
    /** @type {?} */
    Keyboard.capsLock;
    /** @type {?} */
    Keyboard.numLock;
}
//# sourceMappingURL=data:application/json;base64,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