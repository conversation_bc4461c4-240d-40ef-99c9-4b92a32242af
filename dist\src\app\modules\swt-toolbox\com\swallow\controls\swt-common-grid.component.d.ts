import 'jquery-ui-dist/jquery-ui';
import 'slickgrid/lib/jquery.mousewheel';
import { AfterViewInit, ElementRef, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AngularGridInstance, AngularSlickgridComponent, AutoTooltipExtension, BackendServiceOption, CollectionService, Column, ExtensionUtility, FieldType, FilterChangedArgs, GridOption, GridStateChange, Pagination, PaginationChangedArgs, SharedService, SortChangedArgs } from 'angular-slickgrid';
import { Container } from '../containers/swt-container.component';
import { CommonService } from '../utils/common.service';
import { HashMap } from '../utils/HashMap.service';
import { SwtAlert } from '../utils/swt-alert.service';
import { SwtCommonGridPagination } from './swt-common-grid-pagination.component';
export declare class SwtCommonGrid extends Container implements OnInit, AfterViewInit, OnDestroy {
    protected el: ElementRef;
    protected commonService: CommonService;
    protected autoTooltipExtension: AutoTooltipExtension;
    protected extensionUtility: ExtensionUtility;
    protected sharedService: SharedService;
    protected translate: TranslateService;
    paneWidth: any;
    private subscriptions;
    private forceFitColumns;
    private slickPaneRightHeight;
    private containerScrollHeight;
    private containerScrollWidth;
    private slickPaneLeftHeight;
    private parentHeight;
    private parentWidth;
    private parent;
    private lastColumnWidthChanged;
    private tooltipHorizontal;
    private tooltipVertical;
    private prevSelected;
    private gridUID;
    FilteredColumn: any;
    private firstCallGroupingDp;
    private firstCallGroupingOriginalDp;
    static CRUD_OPERATION: string;
    static CRUD_DATA: string;
    static CRUD_ORIGINAL_DATA: string;
    gridContainer: any;
    private _width;
    private _height;
    private _editable;
    private _styleName;
    angularSlickGrid: AngularSlickgridComponent;
    columnDefinitions: Column[];
    invisibleColumnDefinitions: Column[];
    private _columns;
    private _allColumnsData;
    dataset: any[];
    gridObj: any;
    dataviewObj: any;
    pageSize: number;
    private _isAutoEdit;
    private updatedObject;
    setRowSize: number;
    protected _initialColumnsToSkip: number;
    protected isTotalGrid: boolean;
    protected isTreeGrid: boolean;
    protected isGroupedHeaderGrid: boolean;
    protected collectionService: CollectionService<any>;
    protected translateService: TranslateService;
    forceSameColumnSize: boolean;
    forceSameColumnException: any[];
    forceCollopseAllItems: boolean;
    forceExpandAllItems: boolean;
    forceNoAlignRightTotalGrid: boolean;
    fireHorizontalScrollEvent: boolean;
    fireVerticalScrollEvent: boolean;
    private _listenHorizontalScrollEvent;
    private _listenVerticalScrollEvent;
    SwtAlert: SwtAlert;
    private correspondentFieldCode;
    selectedItems: any[];
    _selectedIndices: any[];
    all: string;
    empty: string;
    notEmpty: string;
    private previousColumnData;
    private logger;
    private _selectable;
    private _enableRowSelection;
    private _addPreHeaderBackGroundColor;
    private _rowHeight;
    private _enabled;
    private _allowMultipleSelection;
    private _selectedItem;
    private lengthItem;
    private _filteredDataset;
    private _doubleClickEnabled;
    private _resetOriginalDp;
    private firstCallDataprovider;
    private paneRight;
    private paneLeft;
    private nbColumn;
    idColumnComboFilterable: any;
    nbRowsColumnComboFilterable: any;
    private _selectedIndex;
    private _updateSelectedIndexFromGrid;
    onDataGridReady: Function;
    private _EditedItemPosition;
    private _originalDataprovider;
    metaData: any;
    columnData: any;
    rowsData: any;
    columnOptions: any[];
    pendingCustomGrid: any;
    pendingGridData: any;
    options: BackendServiceOption;
    pagination: Pagination;
    sortedGridColumnId: string;
    sortedGridColumn: string;
    msdSortedGridColumn: string;
    sortDirection: any;
    sortColumnIndex: number;
    currentPage: number;
    filteredGridColumns: string;
    currentFilterColumns: any[];
    isFiltered: boolean;
    parentTabId: any;
    private filterColumns;
    private sortColumns;
    private _showHeader;
    private _showFilter;
    private _enableFilter;
    private _enableSort;
    private _labelCodeCorrespEnable;
    private _labelCodeCorrespondance;
    private _flagKeyCtrlDown;
    private _uniqueColumn;
    _clientSideSort: boolean;
    _clientSideFilter: boolean;
    private _onRowDoubleClick;
    private _onRowClick;
    private _CheckChange;
    columnSelectChanged: Function;
    private _onFilterChanged;
    private _onCustomMouseEnter;
    private _onCustomMouseLeave;
    private _filterUpdate;
    private _onSortChanged;
    private _sortUpdate;
    private _onPaginationChanged;
    private _customContentFunction;
    private _extraHTMLContentFunction;
    private _customTooltipFunction;
    private _rowColorFunction;
    private _rowClickableFunction;
    private _validate;
    private _CellLinkClick;
    private _RadioButtonChange;
    private _enableDisableCells;
    private _showHideCells;
    private _disabledRow;
    private _moduleId;
    private _entityId;
    private programId;
    componentID: string;
    GroupId: any;
    private dataProviderGroupingGridDataCpy;
    checkBoxHeaderActive: boolean;
    private pendingSelectedIndex;
    onFilterChanged_: EventEmitter<any>;
    filterUpdate_: EventEmitter<any>;
    onPaginationChanged_: EventEmitter<any>;
    onSortChanged_: EventEmitter<any>;
    sortUpdate_: EventEmitter<any>;
    cellClick: EventEmitter<any>;
    ITEM_CHANGED: EventEmitter<any>;
    ITEM_FOCUS_OUT: EventEmitter<any>;
    ITEM_FOCUS_IN: EventEmitter<any>;
    onDoubleClick: EventEmitter<any>;
    ITEM_DOUBLE_CLICK: EventEmitter<any>;
    ITEM_CLICK: EventEmitter<any>;
    radioButtonChange: EventEmitter<any>;
    columnWidthChanged: EventEmitter<any>;
    columnOrderChanged: EventEmitter<any>;
    cellLinkClick: EventEmitter<any>;
    onFocusOut: EventEmitter<any>;
    keyup: EventEmitter<any>;
    amountFormat: string;
    amountDecimal: string;
    dateFormat: string;
    currencyFormat: string;
    hideHorizontalScrollBar: boolean;
    hideVerticalScrollBar: boolean;
    private _mandatoryColumns;
    systemLocale: string;
    private isoDateFormat;
    private _lockedColumnCount;
    private frozenColumnCount;
    private _lockedRowCount;
    private gridMenuExtension;
    private _useHandCursor;
    private _addEmptyNotEmptyFilter;
    private _useDummyColumn;
    private _treeMaxLevel;
    private filterComponents;
    private dataRows;
    searchTerms: any;
    private _dict;
    private nonResizableCol;
    protected _selects: any;
    angularGridInstance: AngularGridInstance;
    private _changes;
    private local_sequence;
    private lastSeq;
    saveColumnOrder: boolean;
    saveWidths: boolean;
    forceHeaderRefresh: boolean;
    private lockColumnCount;
    private lockcolumn;
    private initialRawDataSize;
    private colWidth;
    private colOrder;
    private defaultWidth;
    private colHidden;
    private paneBottomLeft;
    private paneBottomLeft_height;
    private paneBottomLeft_top;
    private viewportTopLeft_width;
    private lockedColumnsWidths;
    private hasGroupedColumns;
    private groupedHeadersChildrenLengh;
    gridOptions: GridOption;
    private _paginationComponent;
    clientSideSort: boolean;
    clientSideFilter: boolean;
    useHandCursor: boolean;
    addEmptyNotEmptyFilter: boolean;
    width: number;
    height: any;
    mandatoryColumns: string;
    useDummyColumn: boolean;
    treeMaxLevel: number;
    constructor(el: ElementRef, commonService: CommonService, autoTooltipExtension: AutoTooltipExtension, extensionUtility: ExtensionUtility, sharedService: SharedService, collectionService: CollectionService<any>, translate: TranslateService);
    handleOnMouseEnter(e: any): void;
    handleOnMouseLeave(): void;
    gridData: any;
    protected setFilterListAfterData: boolean;
    toObject(arr: any): {};
    paginationComponent: SwtCommonGridPagination;
    listenHorizontalScrollEvent: any;
    listenVerticalScrollEvent: any;
    selectedItem: any;
    selectedIndex: number;
    selectedIndices: any;
    setSelectedIndices(indices: any): void;
    onRowDoubleClick: Function;
    onRowClick: Function;
    CheckChange: Function;
    editable: boolean;
    styleName: string;
    allowMultipleSelection: boolean;
    selectable: boolean;
    enableRowSelection: boolean;
    addPreHeaderBackGroundColor: boolean;
    enabled: boolean;
    columns: any[];
    moduleId: string;
    /**
     * showHeader : Grid with/without header
     * value : boolean
     */
    showHeader: any;
    private headerHeightBeforeHide;
    /**
     * showFilter : Grid with/without header
     * value : boolean
     */
    showFilter: any;
    /**
     * showFilter : Grid with/without filter
     * value : boolean
     */
    enableFilter: any;
    /**
     * showFilter : Grid with/without header
     * value : boolean
     */
    enableSort: any;
    /**
     * doubleClickEnabled : enable/disable double Click event on the grid
     * value : boolean
     */
    doubleClickEnabled: boolean;
    dataProvider: any;
    originalDataprovider: any;
    /**
     * set labelCodeCorrespEnable
     * param enabled :Boolean
     * To set labelCodeCorrespEnable
     **/
    /**
    * get labelCodeCorrespEnable
    * @return enabled :Boolean
    * To set labelCodeCorrespEnable
    **/
    labelCodeCorrespEnable: boolean;
    /**
    *
    */
    labelCodeCorrespondance: any;
    /**
    *
    */
    onFilterChanged: Function;
    /**
    *
    */
    onCustomMouseEnter: Function;
    /**
    *
    */
    onCustomMouseLeave: Function;
    /**
    *
    */
    filterUpdate: Function;
    /**
    *
    */
    onSortChanged: Function;
    /**
    *
    */
    sortUpdate: Function;
    /**
    *
    */
    onPaginationChanged: Function;
    /**
     * Change the Row's Background Color .
     **/
    /**
    *
    */
    rowColorFunction: Function;
    /**
     * Change the Row's Background Color .
     **/
    /**
    *
    */
    rowClickableFunction: Function;
    /**
     * Change the cell content based on function .
     **/
    /**
    *
    */
    customContentFunction: Function;
    /**
  * Change the cell content based on function .
  **/
    /**
    *
    */
    extraHTMLContentFunction: Function;
    /**
  * Add the row tooltip based on function .
  **/
    /**
    *
    */
    customTooltipFunction: Function;
    /**
     * validate a field .
     **/
    /**
    *
    */
    validate: Function;
    /**
    *
    */
    CellLinkClick: Function;
    /**
     *
     */
    RadioButtonChange: Function;
    /**
     * render the cell editable on double click (activate the editor)
     *   value
     */
    editedItemPosition: any;
    /**
     * _changes getter
     * */
    /**
    * _changes setter
    * */
    changes: HashMap;
    /**
     *
     * */
    /**
    *
    * */
    rowHeight: number;
    /**
     *
     * */
    /**
    *
    * */
    lockedColumnCount: number;
    private drawLockedColumns;
    /**
     *
     * */
    /**
    *
    * */
    lockedRowCount: number;
    private refreshPaneFrozenRows;
    resetOriginalDp: boolean;
    dict: any;
    /**
     * setter of enableDisableCells
     * You have to note that the call to this function must be implemented like so
     *    this.commonGrid1.enableDisableCells=(row,field)=>{
     *            // function implemented by the developer
     *            return this.enableOrDisabledCheckBoxs(row,field);
     *    };
     * */
    /**
    * getter of enableDisableCells
    * */
    enableDisableCells: Function;
    /**
     * setter of showHideCells
     * */
    /**
    * getter of showHideCells
    * */
    showHideCells: Function;
    /**
     * If an entityId is specified on the page then this needs to be set to enable column saving
     **/
    entityID: string;
    /**
     * set screenID
     *
     * param programId :String
     * If an programId is specified on the page then this needs to be set
     *  to enable column saving
     **/
    /**
    * get screenID
    *
    * @return String -  programId
    *
    * to get programId
    **/
    screenID: any;
    uniqueColumn: string;
    filters: any;
    sorters: any;
    gridTypesArray: any[];
    /**
     * CustomGrid constructor
     * @param columnData
     */
    CustomGrid(tmpcolumnData: any): void;
    sorterNumeric(a: any, b: any, c: any): 1 | 0 | -1;
    protected drawGridBackgroundColor(): void;
    protected drawGridVerticalLines(): void;
    /**
     * re-initialize selectedItem=null , selectedIndex=-1, selectedIndices=[] ...
     */
    private clearAll;
    private translateHeader;
    parentElementJquery: any;
    ngOnInit(): void;
    shallowEqual(object1: any, object2: any): boolean;
    groupedCollapesedColumns: any[];
    private lastCalculatedContentRect;
    ngAfterViewInit(): void;
    isVisible(e: any): boolean;
    private isIE;
    dataviewReady(dataview: any): void;
    onAngularGridCreated(angularGrid: AngularGridInstance): void;
    gridReady(grid: any): void;
    sortTreeGrid(): void;
    convertHierarchicalViewToParentChildArrayLocal<T = any>(hierarchicalArray: T[], options?: {
        parentPropName?: string;
        childrenPropName?: string;
        identifierPropName?: string;
    }): T[];
    /**
     * Convert a hierarchical array (with children) into a flat array structure array but using the array as the output (the array is the pointer reference)
     * @param hierarchicalArray - input hierarchical array
     * @param outputArrayRef - output array passed (and modified) by reference
     * @param options - you can provide "childrenPropName" (defaults to "children")
     * @param treeLevel - tree level number
     * @param parentId - parent ID
     */
    convertHierarchicalViewToParentChildArrayByReferenceLocal<T = any>(hierarchicalArray: T[], outputArrayRef: T[], options?: {
        childrenPropName?: string;
        parentPropName?: string;
        hasChildrenFlagPropName?: string;
        treeLevelPropName?: string;
        identifierPropName?: string;
    }, treeLevel?: number, parentId?: string): void;
    convertParentChildArrayToHierarchicalViewLocal<T = any>(flatArray: T[], options?: {
        parentPropName?: string;
        childrenPropName?: string;
        identifierPropName?: string;
    }): T[];
    setRefreshColumnWidths(columnDefArray: any): void;
    protected setNewFiltercollection(data: any[], collection: any[], field: string): any[];
    protected fullCollectionList: any[];
    refreshFilters(refreshLayout?: boolean): void;
    removeDuplicates(collection: any[], field: string): any[];
    /**
     * Sort items in a collection
     * @param collection
     * @param sortBy
     * @param columnDef
     * @param translate
     */
    sortCollection(collection: any[], property: string, fieldType: FieldType, sortDirection: boolean): any[];
    sortByFieldType(value1: any, value2: any, fieldType: FieldType, sortDirection: number): number;
    /**
     * CommonGrid constructor
     * @param columnsData
     * @param lockedColumnCount
     * @param uniqueColumn
     * @param baseURL
     * @param programId
     * @param componentId
     * @param enableRenders
     * @param colValidationMap
     * @param checkHeader
     * @param cboLinked
     */
    CommonGrid(columnsData: any, lockedColumnCount: number, uniqueColumn: string, baseURL: string, programId: string, componentId: string, enableRenders?: boolean, colValidationMap?: any, checkHeader?: boolean, cboLinked?: boolean): void;
    /**
     *
     * This method will be invoked for the onload combo details
     * @param rawData :XMLList
     * @param flag:Boolean
     * */
    gridComboDataProviders(rawData: any): void;
    scrollToIndex(selectedIndex: number): void;
    /**
     * gridStateChanged : Dispatched event of a Grid State Changed event
     * @param gridState
     */
    gridStateChanged(gridState: GridStateChange): void;
    private treeOpenedItem;
    private escapeRegExp;
    buildQuery(): string;
    init(options: BackendServiceOption, pagination?: Pagination): void;
    resetPaginationOptions(): void;
    updateOptions(serviceOptions?: BackendServiceOption): void;
    /**
     * FILTERING EMIT EVENT
     * @param event
     * @param args
     */
    processOnFilterChanged(event: Event, args: FilterChangedArgs): Promise<string>;
    /**
     * SORT EMIT EVENT
     * @param event
     * @param args
     */
    processOnSortChanged(event: Event, args: SortChangedArgs): string;
    /**
     * PAGINATION EMIT EVENT
     * @param event
     * @param args
     */
    processOnPaginationChanged(event: Event, args: PaginationChangedArgs): string;
    /**
     * getFilteredGridColumns : returns filtered columns as String.
     */
    getFilteredGridColumns(): string;
    /**
     * getFilteredGridColumns : returns filtered columns as String.
     */
    getCurrentFilter(): any[];
    /**
     * getSortedGridColumn :  returns sorted columns as String.
     */
    getSortedGridColumn(): string;
    /**
     * getMsdSortedGridColumn :  returns sorted columns as String.
     */
    getMsdSortedGridColumn(): string;
    /**
     * getFilterColumns : returns sorted columns as Column.
     */
    getFilterColumns(): any[];
    /**
     * getSortColumns : returns sorted columns as Column.
     */
    getSortColumns(): any[];
    getFilteredItems(): any;
    getFilteredData(): any;
    onKeyDown(event: any): void;
    /**
     * We will only be able to spy changes, but it is not wise to spy the NO CHANGES ...
     * But if you want to do, you just have to keep track of original values of each cell ... which not good at all
     * @param event
     */
    spyChanges(event: any): void;
    spyNoChanges(event: any): void;
    /**
     * Set the params to be sent when exporting the report
     * _sqlQueryFilter_ should be like USER_ID:ADMIN_at_S|ROLE_ID:1_at_N (S is String and N is number)
     * _sqlQuerySort_ should be like col1, col2...
     * _sqlQueryDirection_ should be like ASC, DESCs
     **/
    kvFilterSortParams(): any[];
    /**
     * Set the params to be sent
     * _sqlQueryFilter_ should be like USER_ID:ADMIN@S|ROLE_ID:1@N (S is String and N is number)
     * _sqlQuerySort_ should be like col1, col2...
     * _sqlQueryDirection_ should be like ASC, DESCs
     **/
    filterSortParamsKVStr(base64?: boolean): string;
    /**
     * kvFilterSortAsQuery
     *
     * Set the params to be sent as an sql query
     * _sqlQueryFilter_ should be like USER_ID = 'ADMIN' AND ROLE_ID = 1
     * _sqlQuerySort_ should be like col1 , col2
     * _sqlQueryDirection_ should be like ASC, DESCs
     **/
    kvFilterSortAsQuery(): any[];
    /**
     *  implicitUpdate
     *  <AUTHOR>
     *  This method is used to dispatch a custom commonGridEvent directly from MXML
     * */
    implicitUpdate(cellDTO: Object): void;
    /**
     * Item changed handler, this method will be responsible of tracing changes on each cell
     * */
    updateCrud(updatedObject?: any, implicitUpdate?: boolean): void;
    /**
     * converts an item to KVTYPE format.
     * @param item
     */
    itemToKvtype(item: any): any;
    /**
     * defaultEnableDisableCells
     *
     * param raw : any
     * param dataField : String
     *
     * This Method is used to enable cell renders by default
     **/
    defaultEnableDisableCells(raw: any, dataField: string): boolean;
    defaultValidate(newRow: any, previousRow: any, bool: boolean): boolean;
    /**
     * defaultShowHideCells
     *
     * param raw : any
     * param dataField : string
     *
     * This Method is used to enable cell renders by default
     **/
    defaultShowHideCells(raw: any, dataField: string): boolean;
    defaultRowColorFunction(raw: any, dataField: string): string;
    defaultRowClickableFunction(raw: any, dataField: string): string;
    defaultDisabledRow(raw: any): boolean;
    defaultContentFunction(raw: any, dataField: string, value: string, type: string): string;
    defaultextraHTMLContentFunction(raw: any, dataField: string, value: string, type: string): string;
    defaultTooltipFunction(raw: any): string;
    defaultColWidthURL(colOrderActionPath: string): void;
    private saveResult;
    private saveFault;
    /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     **/
    private getScrollbarWidth;
    private gridContainerWidth;
    private gridContainerHeight;
    private updateWidths;
    /**
     * Saves the column widths to the database
     **/
    private updateColumnWidths;
    colWidthURL(colWidthActionPath: string): void;
    colOrderURL(colOrderActionPath: string): void;
    /**
     * Saves the column order to the database
     **/
    private updateColumnOrder;
    deepCopy(mainObj: any): any[];
    /**
     *  enable/disable only checkbox columns within grid
     */
    enableColumn(columnName: any, state: boolean): void;
    /**
     * refresh : update the grid data .
     */
    refresh(): void;
    private deepIsEqual;
    /**
     * Cleans crud changes
     * */
    clearChanges(): void;
    /**
     *
     * @param item
     * @param columnIndex
     * @param where
     */
    Filter(item: string, columnIndex?: any, where?: string): void;
    /**
     * Deletes the selected row from datagrid
     *
     * */
    removeSelected(): boolean;
    /**
     * Add new item in the grid
     * @param newItem
     */
    appendRow(rowData?: any, notSelected?: boolean, notScrollToIndex?: boolean): boolean;
    /**
     * Add a new list of items to the grid
     * @param rowsData : array of json data.
     */
    appendRows(rowsData: any[]): boolean;
    /**
     * updates a row in the grid.
     * @param rowIndex : index of the row.
     * @param column   : column to be updated.
     * @param value    : new value to get the column.
     */
    updateRow(rowIndex: number, column: any, value: any): void;
    /**
     * updates a row in the grid.
     * @param rowIndex : index of the row.
     * @param column   : column to be updated.
     * @param value    : new value to get the column.
     */
    updateRowByCondition(columnToFind: string, valueToFind: any, column: any, value: any): void;
    /**
     * updates a row in the grid.
     * @param rowIndex : index of the row.
     * @param column   : column to be updated.
     * @param value    : new value to get the column.
     */
    getRowByCondition(columnToFind: string, valueToFind: any): any;
    private onCellClicked;
    onSelectedRowsChanged(e: any, args: any): void;
    onCellChanged(e: any, args: any): void;
    gridInitialized(event: any): void;
    /**
     * makes the column's combobox  filterable from 'nbRows' rows  .
     * @param columnId : column to be updated.
     * @param nbRows   : number of rows .
     */
    setColumnComboFiltredRows(columnId: any, nbRows: number): void;
    /**
     * This method is used to validate data grid after rendering
     * added by Chiheb to fix the display issue in tabNavigator.
     */
    validateNow(): void;
    ngOnDestroy(): void;
    navigatorUserAgentDetect(): "Opera" | "Chrome" | "Safari" | "Firefox" | "IE" | "unknown";
    /**
     * Hide a column from the grid
     * @param column : column to be hidden.
     */
    private hideColumn;
    private displayColumn;
    /**
     *  Clear Filter & Sort before setting new Data.
     */
    clearSortFilter(): void;
    resetFilter(): void;
    private refreshGridDataIds;
    /**
     * Add a Checkbox to the header of a column with checkBoxVisibility=true .
     */
    addCheckBoxHeaderColumn(): void;
    setFocus(): void;
    /**
     * setter of disabledRow
     * */
    /**
    * getter of showHideCells
    * */
    disabledRow: Function;
    sortGridColumnByColOrder(): any[];
    displayHideScrollBar(): void;
    private StrToBool;
    private updateCheckboxHeader;
    thereIsVerticalScrollbar(): boolean;
    thereIsHorizontalScrollbar(): boolean;
    resizeGrid(): void;
    refrehHeaderFiters(): void;
    private sortByProperty;
    collapseAllTreeLevels(): void;
    expandAllTreeLevels(): void;
    expandTreeToLevel(level: number): void;
    refreshLastOpenedTreeItems(): void;
    private addMultipleFilterStyle;
}
