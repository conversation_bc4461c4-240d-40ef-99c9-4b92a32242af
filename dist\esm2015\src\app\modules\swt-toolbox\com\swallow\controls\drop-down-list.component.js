/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';
export class DropDownList {
    /**
     * @param {?} elementRef
     */
    constructor(elementRef) {
        this.elementRef = elementRef;
        this.onItemClick = new EventEmitter();
        this.onItemChange = new EventEmitter();
        this.onClickOutSide = new EventEmitter();
        this.onItemNavigation = new EventEmitter();
        this.highlightIndex = -1;
        this.dropDownContainer = null;
        this.dropDownItems = null;
        this.virtualScroll = null;
        this.scrollHandler = null;
        this.highlightedItem = null;
        this.pointer = 0;
        this.previousPointer = 0;
        this.isFilterActive = false;
        this.originalDataSource = [];
        this.firstLoad = true;
        this._activeItemIndex = -1;
        this._selectedIndex = -1;
        this._selectedItem = null;
        this._selectedLabel = null;
        this._selectedValue = null;
        this._dataLabel = 'content';
        this._dataSource = [];
    }
    /**
     * @return {?}
     */
    get activeItemIndex() {
        return this._activeItemIndex;
    }
    /**
     * @return {?}
     */
    get activeItem() {
        return this.dataSource[this._activeItemIndex];
    }
    /**
     * @return {?}
     */
    get selectedIndex() {
        return this._selectedIndex;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedIndex(value) {
        if (value > -1 && value < this.dataSource.length) {
            this._activeItemIndex = this._selectedIndex = value;
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.scrollToIndex(value);
            }), 0);
        }
    }
    /**
     * @return {?}
     */
    get selectedItem() {
        return this._selectedItem;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedItem(value) {
        this._selectedItem = value;
    }
    /**
     * @return {?}
     */
    get selectedLabel() {
        return this._selectedLabel;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedLabel(value) {
        this._selectedLabel = value;
    }
    /**
     * @return {?}
     */
    get selectedValue() {
        return this._selectedValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedValue(value) {
        this._selectedValue = value;
    }
    /**
     * @return {?}
     */
    get dataLabel() {
        return this._dataLabel;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataLabel(value) {
        if (!value) {
            return;
        }
        this._dataLabel = value;
    }
    /**
     * @return {?}
     */
    get dataSource() {
        return this._dataSource;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataSource(value) {
        if (!value && !value.length) {
            return;
        }
        this._dataSource = value;
        this.pointer = 0;
        if ($(this.dropDownItems)[0]) {
            this.applyDataSource(value);
        }
        else {
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.applyDataSource(value);
            }), 0);
        }
        if (!this.isFilterActive && this.firstLoad) {
            // store copy of data source.
            this.originalDataSource = [...value];
            this.firstLoad = false;
        }
        else {
            this.highlightIndex = -1;
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.dropDownContainer = $(this.elementRef.nativeElement).find('div.swt-dropdown-container');
        this.dropDownItems = $(this.elementRef.nativeElement).find('div.swt-dropdown-items');
        this.virtualScroll = $(this.elementRef.nativeElement).find('div.swt-virtual-scroll');
        this.scrollHandler = $(this.elementRef.nativeElement).find('div.swt-dropdown-scrollhandler');
        $(this.virtualScroll).scroll((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.pointer = Math.ceil($(this.virtualScroll).scrollTop() / 22);
            this.validateNow();
        }));
        // bind mouse wheel event listener to native element.
        this.elementRef.nativeElement.addEventListener('mousewheel', this.onMouseWheelMotion.bind(this));
    }
    /**
     * @param {?} event
     * @return {?}
     */
    clickoutEventHandler(event) {
        // if user clicked outside the close dropdown.
        if (!this.elementRef.nativeElement.contains(event.target)) {
            this.onClickOutSide.emit(event);
        }
        else {
            if ($(event.target).is('div.swt-dropdown-item')) {
                this._selectedIndex = !isNaN(Number($(event.target).attr('index'))) ? Number($(event.target).attr('index')) : -1;
                this._selectedItem = this._selectedIndex !== -1 ? this.originalDataSource[this._selectedIndex] : null;
                this._selectedValue = this.selectedItem !== null ? this.selectedItem.value : null;
                this._selectedLabel = this.selectedItem !== null ? this.selectedItem[this.dataLabel] : null;
                $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
                $(this.dropDownItems).find("div#" + event.target.id).addClass('swt-selected-dropdown-item');
                this.highlightIndex = Number(event.target.id);
                event.selectedItem = this.selectedItem;
                this.onItemClick.emit(event);
            }
        }
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        try {
            $((/** @type {?} */ (window))).off();
            $(this.dropDownContainer).off();
            $(this.dropDownItems).off();
            $(this.virtualScroll).off();
            $(this.scrollHandler).off();
            $(this.dropDownContainer).remove();
            $(this.dropDownItems).remove();
            $(this.virtualScroll).remove();
            $(this.scrollHandler).remove();
            this.pointer = 0;
            this.onClickOutSide.unsubscribe();
            this.onItemChange.unsubscribe();
            this.onItemClick.unsubscribe();
            this.elementRef.nativeElement.removeEventListener('mousewheel', this.onMouseWheelMotion.bind(this));
        }
        catch (e) {
            console.error('method [ngOnDestroy] - error: ', e);
        }
    }
    // @HostListener('mousewheel', ['$event'])
    /**
     * @param {?} event
     * @return {?}
     */
    onMouseWheelMotion(event) {
        if (event.wheelDelta > 0 && this.pointer > 0) {
            this.pointer--;
        }
        if (event.wheelDelta < 0 && this.pointer < this.dataSource.length - 7) {
            this.pointer++;
        }
        if (this.pointer !== this.previousPointer) {
            if (this.pointer > this.selectedIndex) {
                this.highlightedItem = "";
            }
            else {
                this.highlightedItem = $(this.elementRef.nativeElement).find('#' + 0);
            }
            this.validateNow();
            $(this.virtualScroll).scrollTop(this.pointer * 21);
        }
        this.previousPointer = this.pointer;
    }
    /**
     * @param {?} index
     * @return {?}
     */
    scrollToIndex(index) {
        if (index > -1 && index < this.dataSource.length) {
            if (this.dataSource.length < DropDownList.DEFAULT_ITEM_NUMBER) {
                this.highlightIndex = this.pointer = 0;
                this.highlightedItem = $(this.elementRef.nativeElement).find('#' + index);
                this.validateNow();
            }
            else {
                if (index > this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER) {
                    this.pointer = this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER;
                    this.highlightIndex = DropDownList.DEFAULT_ITEM_NUMBER - (this.dataSource.length - index);
                    this.highlightedItem = $(this.elementRef.nativeElement).find('#' + this.highlightIndex);
                }
                else {
                    this.pointer = index;
                    this.highlightIndex = 0;
                    this.highlightedItem = $(this.elementRef.nativeElement).find('div#0');
                }
                $(this.virtualScroll).scrollTop(this.pointer * 21);
                this.validateNow();
            }
        }
    }
    /**
     * @return {?}
     */
    navigateDown() {
        if (this.highlightIndex < $(this.dropDownItems).children().length - 1) {
            this.highlightIndex++;
            $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
            this.highlightedItem = $(this.dropDownItems).find("div#" + this.highlightIndex);
            this.highlightedItem.addClass('swt-selected-dropdown-item');
        }
        else {
            if (this.pointer < this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER) {
                this.pointer++;
                this.validateNow();
            }
        }
        // increment active item index while is less than datasource length.
        this._activeItemIndex = Number(this.highlightedItem.attr('index'));
        $(this.virtualScroll).scrollTop(this.pointer * 21);
        this.onItemNavigation.emit({ direction: 'down', highlightIndex: this.dataSource[this.highlightIndex] });
    }
    /**
     * @return {?}
     */
    navigateUp() {
        if (this.highlightIndex > 0) {
            this.highlightIndex--;
            $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
            this.highlightedItem = $(this.dropDownItems).find("div#" + this.highlightIndex);
            this.highlightedItem.addClass('swt-selected-dropdown-item');
        }
        else {
            if (this.pointer > 0) {
                this.pointer--;
                this.validateNow();
            }
        }
        // decrement active item index while is greater than 0.
        this._activeItemIndex = Number(this.highlightedItem.attr('index'));
        $(this.virtualScroll).scrollTop(this.pointer * 21);
        this.onItemNavigation.emit({ direction: 'up', highlightIndex: this.dataSource[this.highlightIndex] });
    }
    /**
     * @return {?}
     */
    validateNow() {
        try {
            /** @type {?} */
            let item;
            /** @type {?} */
            let object = null;
            /** @type {?} */
            let loopEnd = this.dataSource.length > DropDownList.DEFAULT_ITEM_NUMBER ? 7 : this.dataSource.length;
            for (let i = 0; i < loopEnd; i++) {
                item = $(this.elementRef.nativeElement).find('#' + i);
                object = this.dataSource[this.pointer + i];
                $(item).text(object[this.dataLabel]);
                $(item).attr('index', this.originalDataSource.indexOf(object));
                $(item).attr('title', object[this.dataLabel]);
            }
            if (this.highlightedItem) {
                /** @type {?} */
                const previous = $(this.elementRef.nativeElement).find('div.swt-selected-dropdown-item');
                $(previous).removeClass('swt-selected-dropdown-item');
                $(this.highlightedItem).addClass('swt-selected-dropdown-item');
            }
            else {
                $(this.dropDownItems).find('div.swt-selected-dropdown-item').removeClass('swt-selected-dropdown-item');
            }
        }
        catch (e) {
            console.error('method [validateNow] - error: ', e);
        }
    }
    /**
     * This method is used to filter the current
     * list with a given word.
     * @param {?} word
     * @return {?}
     */
    filter(word) {
        this.clearFilter();
        /** @type {?} */
        const result = this.dataSource.filter((/**
         * @param {?} item
         * @return {?}
         */
        (item) => {
            return String(item[this.dataLabel]).toUpperCase().startsWith(word.toUpperCase());
        }));
        this.isFilterActive = true;
        this.highlightIndex = this.pointer = 0;
        this.dataSource = result;
    }
    /**
     * This method is used to clear the filter
     * @return {?}
     */
    clearFilter() {
        if (this.isFilterActive) {
            this.dataSource = [...this.originalDataSource];
            this.isFilterActive = false;
        }
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    applyDataSource(value) {
        try {
            $(this.dropDownItems).empty();
            if (value.length <= DropDownList.DEFAULT_ITEM_NUMBER) {
                for (let i = 0; i < value.length; i++) {
                    $(this.dropDownItems).append(`<div class="swt-dropdown-item" id="${i}"></div>`);
                }
                $(this.virtualScroll).hide();
                $(this.virtualScroll).height(value.length * 21 + 'px');
                $(this.dropDownItems).css('width', '100%');
                this.validateNow();
            }
            else {
                for (let i = 0; i < DropDownList.DEFAULT_ITEM_NUMBER; i++) {
                    $(this.dropDownItems).append(`<div class="swt-dropdown-item" id="${i}"></div>`);
                }
                $(this.virtualScroll).show();
                $(this.virtualScroll).height('147px');
                this.validateNow();
                $(this.scrollHandler).height(value.length * 21);
            }
        }
        catch (e) {
            console.error('method [applyDataSource ] - error: ', e);
        }
    }
}
DropDownList.DEFAULT_ITEM_NUMBER = 7;
DropDownList.decorators = [
    { type: Component, args: [{
                selector: 'swt-drop-down-list',
                template: `
        <div class="swt-dropdown-container">
            <div class="swt-dropdown-items">
                <!--<div class="swt-dropdown-item" id="0"></div>-->
            </div>
            <div class="swt-virtual-scroll">
                <div class="swt-dropdown-scrollhandler"></div>
            </div>
        </div>
    `,
                styles: [`
        .swt-dropdown-container {
            display: flex;
            outline: 1px solid #cccccc;
            max-height: 147px;
            width: 100%;
            box-sizing: border-box;
        }

        .swt-dropdown-items, .swt-virtual-scroll {
            height: 100%;
        }

        .swt-dropdown-items {
            background-color: #FFF;
            width: calc(100% - 13px);
            display: block;
        }

        .swt-virtual-scroll {
            /*border: 1px solid green;*/
            width: 13px;
            overflow-x: hidden;
            overflow-y: auto;
            display: none;
        }

        .swt-dropdown-scrollhandler {
            /*border: 1px solid purple;*/
            width: 2px;
        }
    `]
            }] }
];
/** @nocollapse */
DropDownList.ctorParameters = () => [
    { type: ElementRef }
];
DropDownList.propDecorators = {
    onItemClick: [{ type: Output }],
    onItemChange: [{ type: Output }],
    onClickOutSide: [{ type: Output }],
    onItemNavigation: [{ type: Output }],
    selectedIndex: [{ type: Input }],
    selectedItem: [{ type: Input }],
    selectedLabel: [{ type: Input }],
    selectedValue: [{ type: Input }],
    dataLabel: [{ type: Input }],
    dataSource: [{ type: Input }],
    clickoutEventHandler: [{ type: HostListener, args: ['document:click', ['$event'],] }]
};
if (false) {
    /** @type {?} */
    DropDownList.DEFAULT_ITEM_NUMBER;
    /** @type {?} */
    DropDownList.prototype.onItemClick;
    /** @type {?} */
    DropDownList.prototype.onItemChange;
    /** @type {?} */
    DropDownList.prototype.onClickOutSide;
    /** @type {?} */
    DropDownList.prototype.onItemNavigation;
    /** @type {?} */
    DropDownList.prototype.highlightIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.dropDownContainer;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.dropDownItems;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.virtualScroll;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.scrollHandler;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.highlightedItem;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.pointer;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.previousPointer;
    /** @type {?} */
    DropDownList.prototype.isFilterActive;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.originalDataSource;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.firstLoad;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._activeItemIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._dataLabel;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._dataSource;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.elementRef;
}
//# sourceMappingURL=data:application/json;base64,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