/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { SwtUtil } from '../utils/swt-util.service';
export class RemoteTransaction {
    // private headers = new HttpHeaders();
    /**
     * constructor
     *
     * @param {?} httpComms_
     * @param {?} programId_
     * @param {?} uniqueIdentifier_
     */
    constructor(httpComms_, programId_, uniqueIdentifier_) {
        this.httpComms_ = httpComms_;
        this.programId_ = programId_;
        this.uniqueIdentifier_ = uniqueIdentifier_;
        this.httpComms = httpComms_;
        this.programId = programId_;
        this.uniqueIdentifier = uniqueIdentifier_;
        this.startTime = this.getTimer();
    }
    /**
     * @return {?}
     */
    start() {
        this.httpComms.setTransactionUId(this.programId, this.uniqueIdentifier);
        /** @type {?} */
        const result = this.remoteTransaction("start", this.httpComms.getTransactionUId());
        if (result === "true") {
            this.status = "active";
            RemoteTransaction.remoteTransactions.push(this);
        }
        return result === "true";
    }
    /**
     * @return {?}
     */
    commit() {
        /** @type {?} */
        const result = this.remoteTransaction("commit", this.httpComms.getTransactionUId() + "&response=json");
        if (result === "true") {
            this.status = "committed";
            RemoteTransaction.remoteTransactions.splice(RemoteTransaction.remoteTransactions.indexOf(this), 1);
        }
        return result === "true";
    }
    /**
     * @return {?}
     */
    rollback() {
        /** @type {?} */
        const result = this.remoteTransaction("rollback", this.httpComms.getTransactionUId() + "&response=json");
        if (result === "true") {
            this.status = "rolledback";
            RemoteTransaction.remoteTransactions.splice(RemoteTransaction.remoteTransactions.indexOf(this), 1);
        }
        return result === "true";
    }
    /**
     * @return {?}
     */
    isActive() {
        /** @type {?} */
        const result = this.remoteTransaction("status", this.httpComms.getTransactionUId() + "&response=json");
        if (result !== this.status) {
            throw new Error("Transactions is in illegal status");
        }
        return result === "active";
    }
    /**
     * @return {?}
     */
    wasCommitted() {
        return this.status === "committed";
    }
    /**
     * @return {?}
     */
    wasRolledBack() {
        return this.status === "rolledback";
    }
    /**
     * @private
     * @return {?}
     */
    getTimer() {
        return new Date().getTime();
    }
    /*
        ===========================================================================
        Aded by Khalil.B to handle all types of transactions Requests
        (this method replace the ajax function in "taglib.jsp" wich have the same name)
        ===========================================================================
        */
    /**
     * Remote transactions list of actions
     *
     * @private
     * @param {?} action
     * @param {?} screenUniqueTransactionId
     * @return {?}
     */
    remoteTransaction(action, screenUniqueTransactionId) {
        /** @type {?} */
        const requestURL = SwtUtil.getBaseURL();
        /** @type {?} */
        var url = requestURL + "common!";
        // Update url according to the action
        if (action === "start") {
            url += "startTransactionRemotely.do";
        }
        else if (action === "commit") {
            url += "commitTransactionRemotely.do";
        }
        else if (action === "rollback") {
            url += "rollbackTransactionRemotely.do";
        }
        else if (action === "status") {
            url += "getTransactionsStatus.do";
        }
        // Set the transaction identifier
        url += "?screenUniqueTransactionId=" + screenUniqueTransactionId;
        this.httpComms.sendTransaction(url).subscribe((/**
         * @param {?} response
         * @return {?}
         */
        (response) => {
            this.remoteTransactionResult = response;
        }));
        return this.remoteTransactionResult;
    }
}
RemoteTransaction.remoteTransactions = new Array() /*of RemoteTransaction*/;
if (false) {
    /** @type {?} */
    RemoteTransaction.remoteTransactions;
    /** @type {?} */
    RemoteTransaction.prototype.remoteTransactionResult;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.httpComms;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.programId;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.uniqueIdentifier;
    /**
     * @type {?}
     * @protected
     */
    RemoteTransaction.prototype.startTime;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.status;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.httpComms_;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.programId_;
    /**
     * @type {?}
     * @private
     */
    RemoteTransaction.prototype.uniqueIdentifier_;
}
//# sourceMappingURL=data:application/json;base64,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