/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 *
 * <AUTHOR>
 */
//@dynamic
var DateUtils = /** @class */ (function () {
    function DateUtils() {
    }
    /**
     * Compare two dates and return:
     * <ul>
     *	<li>0, if date1 > date2</li>
     *	<li>1, if date1 < date2</li>
     *	<li>-1, if date1 = date2</li>
     *	</ul>
     **/
    /**
     * Compare two dates and return:
     * <ul>
     * 	<li>0, if date1 > date2</li>
     * 	<li>1, if date1 < date2</li>
     * 	<li>-1, if date1 = date2</li>
     * 	</ul>
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?=} includeHours
     * @return {?}
     */
    DateUtils.compareDates = /**
     * Compare two dates and return:
     * <ul>
     * 	<li>0, if date1 > date2</li>
     * 	<li>1, if date1 < date2</li>
     * 	<li>-1, if date1 = date2</li>
     * 	</ul>
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?=} includeHours
     * @return {?}
     */
    function (date1, date2, includeHours) {
        if (includeHours === void 0) { includeHours = true; }
        if (includeHours) {
            /** @type {?} */
            var date1Timestamp = date1.getTime();
            /** @type {?} */
            var date2Timestamp = date2.getTime();
            if (date1Timestamp > date2Timestamp)
                return 0;
            else if (date1Timestamp < date2Timestamp)
                return 1;
            else if (date1Timestamp == date2Timestamp)
                return -1;
        }
        else {
            // TODO: compare two dates without hours .. 
        }
        return 0;
    };
    /**
     * Compare two dates with string format following the dateFormat and return:
     * - 0, if date1>date2
     * - 1, if date1=date2
     * - -1, if date1<date2
     **/
    /**
     * Compare two dates with string format following the dateFormat and return:
     * - 0, if date1>date2
     * - 1, if date1=date2
     * - -1, if date1<date2
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?} dateFormat
     * @return {?}
     */
    DateUtils.compareDatesAsString = /**
     * Compare two dates with string format following the dateFormat and return:
     * - 0, if date1>date2
     * - 1, if date1=date2
     * - -1, if date1<date2
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?} dateFormat
     * @return {?}
     */
    function (date1, date2, dateFormat) {
        // TODO: complete the work for al dateFormat 
        switch (dateFormat) {
            case "MM/DD/YYYY":
            case "DD/MM/YYYY":
            case "DD-MM-YYYY":
            case "MM-DD-YYYY":
            case "YYYY/MM/DD":
            case "YYYY/DD/MM":
        }
        return 0;
    };
    /**
     * Return the date as string with giving the date object and its format
     **/
    /**
     * Return the date as string with giving the date object and its format
     *
     * @param {?} date
     * @return {?}
     */
    DateUtils.getDateAsString = /**
     * Return the date as string with giving the date object and its format
     *
     * @param {?} date
     * @return {?}
     */
    function (date) {
        /** @type {?} */
        var dateAsString = "";
        /** @type {?} */
        var selectedDate = date.selectedDate;
        if (date.formatString == "DD/MM/YYYY")
            dateAsString = selectedDate ? (selectedDate.getDate() < 10 ? "0" + selectedDate.getDate() : selectedDate.getDate()) + "/" + (selectedDate.getMonth() + 1 < 10 ? "0" + (selectedDate.getMonth() + 1) : selectedDate.getMonth() + 1) + "/" + selectedDate.getFullYear() : null;
        else
            dateAsString = selectedDate ? ((selectedDate.getMonth() + 1) < 10 ? "0" + (selectedDate.getMonth() + 1) : (selectedDate.getMonth() + 1)) + "/" + (selectedDate.getDate() < 10 ? "0" + (selectedDate.getDate()) : selectedDate.getDate()) + "/" + selectedDate.getFullYear() : null;
        return dateAsString;
    };
    /**
     * Used to valide time
     * */
    /**
     * Used to valide time
     *
     * @param {?} timeStr
     * @return {?}
     */
    DateUtils.validateTime = /**
     * Used to valide time
     *
     * @param {?} timeStr
     * @return {?}
     */
    function (timeStr) {
        /** @type {?} */
        var exc;
        /** @type {?} */
        var resultStr = "ERROR";
        /** @type {?} */
        var res = false;
        if (exc = this.timepat1.exec(timeStr)) {
            /** @type {?} */
            var time = (exc[1].length == 2 ? "" : "0") + exc[1] + ":" + exc[2];
            resultStr = time;
        }
        else if (exc = this.timepat3.exec(timeStr)) {
            /** @type {?} */
            var time = (exc[1].length == 2 ? "" : "0") + exc[1] + ":00";
            resultStr = time;
        }
        return resultStr;
    };
    /**
     * Converts a string to a Date object
     * */
    /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    DateUtils.dateFromString = /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    function (dateString, pattern) {
        return this.stringToDate(dateString, pattern);
    };
    /**
     * Converts an Oracle ISO format into a date
     * */
    /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    DateUtils.isoToDate = /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var dateStr = value;
        dateStr = dateStr.replace(/\-/g, "/");
        dateStr = dateStr.replace("T", " ");
        dateStr = dateStr.replace("Z", " GMT-0000");
        return new Date(Date.parse(dateStr));
    };
    /**
     * Converts a date into Oracle ISO format
     * */
    /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    DateUtils.dateToIso = /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    function (value, displaySeconds) {
        if (displaySeconds === void 0) { displaySeconds = true; }
        /** @type {?} */
        var hours = '' + value.getHours();
        if (hours.length < 2)
            hours = "0" + hours;
        /** @type {?} */
        var minutes = '' + value.getMinutes();
        if (minutes.length < 2)
            minutes = "0" + minutes;
        /** @type {?} */
        var seconds = '' + value.getSeconds();
        if (seconds.length < 2)
            seconds = "0" + seconds;
        return this.dateToString(value, 'YYYY-MM-DD') + " " + hours + ":" + minutes + (displaySeconds ? (":" + seconds) : "");
    };
    /**
     *  Parses a String object that contains a date, and returns a Date
     *  object corresponding to the String.
     *  The <code>inputFormat</code> argument contains the pattern
     *  in which the <code>valueString</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  <p>The function does not check for the validity of the Date object.
     *  If the value of the date, month, or year is NaN, this method returns null.</p>
     *
     *  <p>For example:
     *  <pre>var dob:Date = DateField.stringToDate("06/30/2005", "MM/DD/YYYY");</pre>
     *  </p>
     *
     *  @param valueString Date value to format.
     *
     *  @param inputFormat String defining the date format.
     *
     *  @return The formatted date as a Date object.
     *
     */
    /**
     *  Parses a String object that contains a date, and returns a Date
     *  object corresponding to the String.
     *  The <code>inputFormat</code> argument contains the pattern
     *  in which the <code>valueString</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  <p>The function does not check for the validity of the Date object.
     *  If the value of the date, month, or year is NaN, this method returns null.</p>
     *
     *  <p>For example:
     *  <pre>var dob:Date = DateField.stringToDate("06/30/2005", "MM/DD/YYYY");</pre>
     *  </p>
     *
     * @param {?} valueString Date value to format.
     *
     * @param {?} inputFormat String defining the date format.
     *
     * @return {?} The formatted date as a Date object.
     *
     */
    DateUtils.stringToDate = /**
     *  Parses a String object that contains a date, and returns a Date
     *  object corresponding to the String.
     *  The <code>inputFormat</code> argument contains the pattern
     *  in which the <code>valueString</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  <p>The function does not check for the validity of the Date object.
     *  If the value of the date, month, or year is NaN, this method returns null.</p>
     *
     *  <p>For example:
     *  <pre>var dob:Date = DateField.stringToDate("06/30/2005", "MM/DD/YYYY");</pre>
     *  </p>
     *
     * @param {?} valueString Date value to format.
     *
     * @param {?} inputFormat String defining the date format.
     *
     * @return {?} The formatted date as a Date object.
     *
     */
    function (valueString, inputFormat) {
        /** @type {?} */
        var mask;
        /** @type {?} */
        var temp;
        /** @type {?} */
        var dateString = "";
        /** @type {?} */
        var monthString = "";
        /** @type {?} */
        var yearString = "";
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var n = inputFormat.length;
        for (var i = 0; i < n; i++, j++) {
            temp = "" + valueString.charAt(j);
            mask = "" + inputFormat.charAt(i);
            if (mask == "M") {
                if (isNaN(Number(temp)) || temp == " ")
                    j--;
                else
                    monthString += temp;
            }
            else if (mask == "D") {
                if (isNaN(Number(temp)) || temp == " ")
                    j--;
                else
                    dateString += temp;
            }
            else if (mask == "Y") {
                yearString += temp;
            }
            else if (!isNaN(Number(temp)) && temp != " ") {
                return null;
            }
        }
        temp = "" + valueString.charAt(inputFormat.length - i + j);
        if (!(temp == "") && (temp != " "))
            return null;
        /** @type {?} */
        var monthNum = Number(monthString);
        /** @type {?} */
        var dayNum = Number(dateString);
        /** @type {?} */
        var yearNum = Number(yearString);
        if (isNaN(yearNum) || isNaN(monthNum) || isNaN(dayNum))
            return null;
        if (yearString.length == 2 && yearNum < 70)
            yearNum += 2000;
        /** @type {?} */
        var newDate = new Date(yearNum, monthNum - 1, dayNum);
        if (dayNum != newDate.getDate() || (monthNum - 1) != newDate.getMonth())
            return null;
        return newDate;
    };
    /**
     *  Formats a Date into a String according to the <code>outputFormat</code> argument.
     *  The <code>outputFormat</code> argument contains a pattern in which
     *  the <code>value</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  @param value Date value to format.
     *
     *  @param outputFormat String defining the date format.
     *
     *  @return The formatted date as a String.
     *
     *  @example <pre>var todaysDate:String = DateField.dateToString(new Date(), "MM/DD/YYYY");</pre>
     */
    /**
     *  Formats a Date into a String according to the <code>outputFormat</code> argument.
     *  The <code>outputFormat</code> argument contains a pattern in which
     *  the <code>value</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     * \@example <pre>var todaysDate:String = DateField.dateToString(new Date(), "MM/DD/YYYY");</pre>
     * @param {?} value Date value to format.
     *
     * @param {?} outputFormat String defining the date format.
     *
     * @return {?} The formatted date as a String.
     *
     */
    DateUtils.dateToString = /**
     *  Formats a Date into a String according to the <code>outputFormat</code> argument.
     *  The <code>outputFormat</code> argument contains a pattern in which
     *  the <code>value</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     * \@example <pre>var todaysDate:String = DateField.dateToString(new Date(), "MM/DD/YYYY");</pre>
     * @param {?} value Date value to format.
     *
     * @param {?} outputFormat String defining the date format.
     *
     * @return {?} The formatted date as a String.
     *
     */
    function (value, outputFormat) {
        if (!value)
            return "";
        /** @type {?} */
        var date = String(value.getDate());
        if (date.length < 2)
            date = "0" + date;
        /** @type {?} */
        var month = String(value.getMonth() + 1);
        if (month.length < 2)
            month = "0" + month;
        /** @type {?} */
        var year = String(value.getFullYear());
        /** @type {?} */
        var output = "";
        /** @type {?} */
        var mask;
        // outputFormat will be null if there are no resources.
        /** @type {?} */
        var n = outputFormat != null ? outputFormat.length : 0;
        for (var i = 0; i < n; i++) {
            mask = outputFormat.charAt(i);
            if (mask == "M") {
                if (outputFormat.charAt(i + 1) == "/" && value.getMonth() < 9) {
                    output += month.substring(1) + "/";
                }
                else {
                    output += month;
                }
                i++;
            }
            else if (mask == "D") {
                if (outputFormat.charAt(i + 1) == "/" && value.getDate() < 10) {
                    output += date.substring(1) + "/";
                }
                else {
                    output += date;
                }
                i++;
            }
            else if (mask == "Y") {
                if (outputFormat.charAt(i + 2) == "Y") {
                    output += year;
                    i += 3;
                }
                else {
                    output += year.substring(2, 4);
                    i++;
                }
            }
            else {
                output += mask;
            }
        }
        return output;
    };
    DateUtils.timepat1 = /^\s*([01]?\d|2[0-3]):?([0-5]\d)\s*$/;
    DateUtils.timepat2 = /^\s*([01]?\d|2[0-3]):?([0-5]\d):?([0-5]\d)\s*$/;
    DateUtils.timepat3 = /^\s*([01]?\d|2[0-3])\s*$/;
    return DateUtils;
}());
export { DateUtils };
if (false) {
    /** @type {?} */
    DateUtils.timepat1;
    /** @type {?} */
    DateUtils.timepat2;
    /** @type {?} */
    DateUtils.timepat3;
}
//# sourceMappingURL=data:application/json;base64,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