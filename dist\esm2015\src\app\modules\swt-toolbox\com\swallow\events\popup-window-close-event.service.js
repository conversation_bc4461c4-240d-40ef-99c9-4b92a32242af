/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
export class PopupWindowCloseEvent {
    /**
     * @param {?} type
     * @param {?=} bubbles
     * @param {?=} cancelable
     */
    constructor(type, bubbles = false, cancelable = false) {
        this.type = type;
        this.bubbles = bubbles;
        this.cancelable = cancelable;
    }
    /**
     * @return {?}
     */
    get popupDataDTO() {
        return this._popupDataDTO;
    }
    /**
     * @param {?} popupDataDTO
     * @return {?}
     */
    set popupDataDTO(popupDataDTO) {
        this._popupDataDTO = popupDataDTO;
    }
    /**
     * @return {?}
     */
    clone() {
        return new PopupWindowCloseEvent(this.type, this.bubbles, this.cancelable);
    }
}
PopupWindowCloseEvent.decorators = [
    { type: Injectable }
];
/** @nocollapse */
PopupWindowCloseEvent.ctorParameters = () => [
    { type: String },
    { type: Boolean },
    { type: Boolean }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype._popupDataDTO;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.type;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.bubbles;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.cancelable;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicG9wdXAtd2luZG93LWNsb3NlLWV2ZW50LnNlcnZpY2UuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvZXZlbnRzL3BvcHVwLXdpbmRvdy1jbG9zZS1ldmVudC5zZXJ2aWNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBRzNDLE1BQU0sT0FBTyxxQkFBcUI7Ozs7OztJQUM5QixZQUFvQixJQUFZLEVBQVcsVUFBbUIsS0FBSyxFQUFXLGFBQXNCLEtBQUs7UUFBckYsU0FBSSxHQUFKLElBQUksQ0FBUTtRQUFXLFlBQU8sR0FBUCxPQUFPLENBQWlCO1FBQVcsZUFBVSxHQUFWLFVBQVUsQ0FBaUI7SUFDekcsQ0FBQzs7OztJQUlELElBQUksWUFBWTtRQUNaLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUNoQyxDQUFDOzs7OztJQUVDLElBQUksWUFBWSxDQUFDLFlBQWlCO1FBQ2hDLElBQUksQ0FBQyxhQUFhLEdBQUcsWUFBWSxDQUFDO0lBQ3RDLENBQUM7Ozs7SUFFUSxLQUFLO1FBQ1IsT0FBTyxJQUFJLHFCQUFxQixDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7SUFDakYsQ0FBQzs7O1lBakJGLFVBQVU7Ozs7Ozs7Ozs7Ozs7SUFLUCw4Q0FBMkI7Ozs7O0lBSGYscUNBQW9COzs7OztJQUFFLHdDQUFpQzs7Ozs7SUFBRSwyQ0FBb0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcblxyXG5ASW5qZWN0YWJsZSgpXHJcbmV4cG9ydCBjbGFzcyBQb3B1cFdpbmRvd0Nsb3NlRXZlbnQge1xyXG4gICAgY29uc3RydWN0b3IocHJpdmF0ZSB0eXBlOiBzdHJpbmcsIHByaXZhdGUgIGJ1YmJsZXM6IGJvb2xlYW4gPSBmYWxzZSwgcHJpdmF0ZSAgY2FuY2VsYWJsZTogYm9vbGVhbiA9IGZhbHNlKSB7XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBfcG9wdXBEYXRhRFRPOiBhbnk7XHJcbiAgXHJcbiAgICBnZXQgcG9wdXBEYXRhRFRPKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9wb3B1cERhdGFEVE87XHJcbiAgfVxyXG5cclxuICAgIHNldCBwb3B1cERhdGFEVE8ocG9wdXBEYXRhRFRPOiBhbnkpIHtcclxuICAgICAgdGhpcy5fcG9wdXBEYXRhRFRPID0gcG9wdXBEYXRhRFRPO1xyXG4gIH1cclxuXHJcbiAgICBwdWJsaWMgY2xvbmUoKSB7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBQb3B1cFdpbmRvd0Nsb3NlRXZlbnQodGhpcy50eXBlLCB0aGlzLmJ1YmJsZXMsIHRoaXMuY2FuY2VsYWJsZSk7XHJcbiAgfVxyXG59XHJcbiJdfQ==