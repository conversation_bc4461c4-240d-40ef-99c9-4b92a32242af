/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { Container } from '../../../containers/swt-container.component';
import { CommonService } from '../../../utils/common.service';
import { SeriesStyleProvider } from './style/SeriesStyleProvider';
import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { HashMap } from '../../../utils/HashMap.service';
import { Series } from './control/Series';
import { ExternalInterface } from '../../../utils/external-interface.service';
import { StringUtils } from '../../../utils/string-utils.service';
import { ILMSeriesLiveValue } from './control/ILMSeriesLiveValue';
import { SeriesHighlightEvent } from "../../../events/swt-events.module";
import { SwtUtil } from '../../../utils/swt-util.service';
import { SwtILMChart } from './control/Chart/SwtILMChart';
import { DividerResizeComplete } from "../../../events/swt-events.module";
var ILMLineChart = /** @class */ (function (_super) {
    tslib_1.__extends(ILMLineChart, _super);
    function ILMLineChart(elem, commonService, _renderer) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._renderer = _renderer;
        _this.liquidityZonesListValues = [];
        _this.liquidityZonesListLimits = new HashMap;
        _this.highlightedLiquiditySeries = [];
        _this.stopUpdateLegend = false;
        _this._visibleThresholds = [];
        _this.NoSOD_Dataprovider = [];
        _this.dataProvider = [];
        _this.isEntityTimeframe = false;
        _this.includeSOD = false;
        _this.sumExternalSodMap = [];
        _this.lowesetAxisValue = NaN;
        _this.lastSelectedGroupForZones = [];
        _this.lastSelectScenarioForZones = [];
        _this.JsonLiquditiyZonesNOSOD = null;
        _this._jsonData = [];
        _this.datasets = [];
        _this.JSONDataNOSOD = new HashMap;
        _this.JSONDataSOD = new HashMap;
        _this.seriesList = new HashMap;
        _this.JSONDataSODAsString = '';
        _this.timeRangeArray = [];
        _this.timeRangeArrayEntity = [];
        _this.canCall = true;
        _this.redrawChart = (/**
         * @param {?} delay
         * @return {?}
         */
        function (delay) {
            if (!_this.canCall)
                return;
            _this.callMethodInIframe("redrawChart", [$(_this.elem.nativeElement)[0].clientHeight]);
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.callMethodInIframe("redrawChart", [$(_this.elem.nativeElement)[0].clientHeight]);
            }), delay);
            _this.canCall = false;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.canCall = true;
            }), delay);
        });
        _this.debounce = (/**
         * @param {?} fn
         * @param {?} delay
         * @return {?}
         */
        function (fn, delay) {
            /** @type {?} */
            var timer;
            return (/**
             * @param {...?} args
             * @return {?}
             */
            function () {
                var args = [];
                for (var _i = 0; _i < arguments.length; _i++) {
                    args[_i] = arguments[_i];
                }
                if (timer)
                    clearTimeout(timer);
                timer = setTimeout((/**
                 * @return {?}
                 */
                function () {
                    fn.apply(null, args);
                }), delay);
            });
        });
        _this.previousRct = {
            left: 0,
            top: 0,
            width: 0,
            height: 0
        };
        return _this;
        // if (window.addEventListener) {
        //   window.addEventListener("message", this.receiveMessage.bind(this), false);
        // } else {
        //    (<any>window).attachEvent("onmessage", this.receiveMessage.bind(this));
        // }
    }
    Object.defineProperty(ILMLineChart.prototype, "balanceLabel", {
        get: /**
         * @return {?}
         */
        function () {
            return this._balanceLabel;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._balanceLabel = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "chartValuesContainer", {
        get: /**
         * @return {?}
         */
        function () {
            return this._chartValuesContainer;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._chartValuesContainer = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "timeDynamicValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._timeDynamicValue;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._timeDynamicValue = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "jsonData", {
        get: /**
         * @return {?}
         */
        function () {
            return this._jsonData;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._jsonData = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "accumLabel", {
        get: /**
         * @return {?}
         */
        function () {
            return this._accumLabel;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._accumLabel = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "accumulatedDCLegend", {
        get: /**
         * @return {?}
         */
        function () {
            return this._accumulatedDCLegend;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._accumulatedDCLegend = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "balancesLegend", {
        get: /**
         * @return {?}
         */
        function () {
            return this._balancesLegend;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._balancesLegend = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMLineChart.prototype, "assetsLegend", {
        get: /**
         * @return {?}
         */
        function () {
            return this._assetsLegend;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._assetsLegend = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    ILMLineChart.prototype.receiveMessage = /**
     * @return {?}
     */
    function () {
    };
    /**
           * Removes completely all the series in the chart
           *
           * */
    /**
     * Removes completely all the series in the chart
     *
     *
     * @return {?}
     */
    ILMLineChart.prototype.removeAllSeries = /**
     * Removes completely all the series in the chart
     *
     *
     * @return {?}
     */
    function () {
        // Remove all the chartvalues
        // for each (var item:ILMSeriesLiveValue in chartValuesContainer.getChildren())
        // this.chartValuesContainer.removeChild(item);
        for (var i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            var serie = this.seriesList.getValues()[i];
            if (serie.seriesLabel) {
                serie.seriesLabel.removeLiveValue();
            }
        }
        // Init the series array
        this.seriesList = new HashMap;
        this.dataProvider = [];
        this.NoSOD_Dataprovider = [];
        // Remove datasets metadata as well
        this.datasets = [];
        // Update the area and line legends 
        if (this._accumulatedDCLegend)
            this.updateAreaLegend();
        if (this._balancesLegend)
            this.updateLineLegend();
        //refreshHorizontalAxis();
        //refreshVerticalAxis();
        this.callMethodInIframe("destroyChart", []);
    };
    /**
     * @return {?}
     */
    ILMLineChart.prototype.resetILMLineChart = /**
     * @return {?}
     */
    function () {
        this.dataProvider = [];
        this.removeAllSeries();
        this.liquidityZonesListValues = [];
        this.liquidityZonesListLimits.clear();
        this.seriesList = new HashMap;
    };
    /**
           * Adds a new Charts (LineSeries or AreaSeries) element
           * */
    /**
     * Adds a new Charts (LineSeries or AreaSeries) element
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    ILMLineChart.prototype.addChart = /**
     * Adds a new Charts (LineSeries or AreaSeries) element
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    function (yField, visible) {
        if (visible === void 0) { visible = true; }
        if (yField.indexOf("Thresholds") == -1)
            return this.createSeries(yField, visible);
        else {
            return this.createThreshold(yField);
        }
    };
    /**
           * Creates a threshold from cached DTO
           * */
    /**
     * Creates a threshold from cached DTO
     *
     * @param {?} yField
     * @return {?}
     */
    ILMLineChart.prototype.createThreshold = /**
     * Creates a threshold from cached DTO
     *
     * @param {?} yField
     * @return {?}
     */
    function (yField) {
        /** @type {?} */
        var dto = this.datasets[yField];
        if (!dto) {
            //throw Error("Programming error: method [updateDataSets] should be called first !");	
            return false;
        }
        /** @type {?} */
        var thresholds = dto["thresholds"];
        /** @type {?} */
        var styleColor = dto["thresholdsColor"];
        /** @type {?} */
        var threasholdValue;
        // for each (var chart:XML in thresholds.children()){
        for (var chart in thresholds) {
            threasholdValue = '' + thresholds[chart];
            // threasholdValue = chart.valueOf();
            if (threasholdValue.charAt(0) == '.') {
                threasholdValue = '0' + threasholdValue;
            }
            this.addThreshold(yField, chart, threasholdValue, styleColor);
        }
        return true;
    };
    /**
     * Add a threshold
     * */
    /**
     * Add a threshold
     *
     * @private
     * @param {?} yField
     * @param {?} minmax
     * @param {?} value
     * @param {?} style
     * @return {?}
     */
    ILMLineChart.prototype.addThreshold = /**
     * Add a threshold
     *
     * @private
     * @param {?} yField
     * @param {?} minmax
     * @param {?} value
     * @param {?} style
     * @return {?}
     */
    function (yField, minmax, value, style) {
        if (!this.dataProvider || this.dataProvider.length == 0 || String(value) == "" || isNaN(value)) {
            if (this.seriesList.getValue(yField + "." + minmax) != null) {
                this.seriesList.remove(yField + "." + minmax);
            }
            return;
        }
        else {
            value = Number(value);
            /** @type {?} */
            var lineSeries = new Series;
            lineSeries.seriesType = style;
            lineSeries.displayName = (yField + "." + minmax);
            // Setting the lineSeries xfield accordind to EntityTimeFrame value 
            if (this.isEntityTimeframe)
                lineSeries.xField = "timeSlotE";
            else
                lineSeries.xField = "timeSlot";
            lineSeries.yField = (yField + "." + minmax);
            //FIXME:CHECK IF NEEDED
            // Add dummy values for first and last dp objects
            // var first:Object=this.dataProvider[0];
            // first[lineSeries.yField]= includeSOD ? value : value - sumExternalSodMap.getValue(yField.split(".")[0]);
            // var last:Object=dataProvider.getItemAt(dataProvider.length - 1);
            // last[lineSeries.yField]=includeSOD ? value : value - sumExternalSodMap.getValue(yField.split(".")[0]);
            // var firstNoSOD:Object=NoSOD_Dataprovider.getItemAt(0);
            // firstNoSOD[lineSeries.yField]=includeSOD? value - sumExternalSodMap.getValue(yField.split(".")[0]) : value ;
            // var lastNoSOD:Object=NoSOD_Dataprovider.getItemAt(dataProvider.length - 1);
            // lastNoSOD[lineSeries.yField]=includeSOD ? value - sumExternalSodMap.getValue(yField.split(".")[0]): value ;
            // // _yFieldVisibleThresholds.push(lineSeries.yField);
            // ArrayCollection(dataProvider).setItemAt(first, 0);
            // ArrayCollection(dataProvider).setItemAt(last, dataProvider.length - 1);
            // ArrayCollection(NoSOD_Dataprovider).setItemAt(firstNoSOD, 0);
            // ArrayCollection(NoSOD_Dataprovider).setItemAt(lastNoSOD, dataProvider.length - 1);
            this.seriesList.put(lineSeries.yField, lineSeries);
            // thresholdsSanityCheck(lineSeries.yField);
        }
    };
    /**
     * Remove the series with its legend
     * */
    /**
     * Remove the series with its legend
     *
     * @param {?} yField
     * @return {?}
     */
    ILMLineChart.prototype.removeSeries = /**
     * Remove the series with its legend
     *
     * @param {?} yField
     * @return {?}
     */
    function (yField) {
        if (yField.indexOf("Thresholds") != -1) {
            for (var type in ILMLineChart.THRESHOLD_TYPE) {
                this.seriesList.remove(yField + "." + type);
            }
        }
        else {
            // super.removeSeries(yField);
            /** @type {?} */
            var series = this.seriesList.getValue(yField);
            if (series) {
                /** @type {?} */
                var seriesType = series.seriesType;
                if (series.seriesLabel) {
                    series.seriesLabel.removeLiveValue();
                }
                this.seriesList.remove(yField);
                // Remove the legend
                if (seriesType == 'line')
                    this.updateLineLegend();
                else
                    this.updateAreaLegend();
            }
        }
    };
    /**
     * Create and adds a line/area Series to line chart based on its yField
     * */
    /**
     * Create and adds a line/area Series to line chart based on its yField
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    ILMLineChart.prototype.createSeries = /**
     * Create and adds a line/area Series to line chart based on its yField
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    function (yField, visible) {
        if (visible === void 0) { visible = true; }
        try {
            /** @type {?} */
            var arr = yField.split(".");
            /** @type {?} */
            var groupId = arr[0];
            /** @type {?} */
            var entityId = this.selectedEntityId;
            /** @type {?} */
            var currencyId = this.selectedCurrencyId;
            /** @type {?} */
            var groupId = arr[0];
            /** @type {?} */
            var scenarioId = arr[1];
            /** @type {?} */
            var dataElement = arr[2];
            /** @type {?} */
            var dto = this.datasets[groupId + '.' + scenarioId];
            if (!dto) {
                //trace("Chart not exist and to plot it need to send request to DB side");
                //throw Error("Programming error: method [updateDataSets] should be called first !");
                return false;
            }
            /** @type {?} */
            var metadata = dto['charts'];
            if (currencyId != metadata.currencyId || entityId != metadata.entityId) {
                return false;
            }
            /** @type {?} */
            var _yField = "";
            for (var i = 0; i < metadata.chart.length; i++) {
                // // Loop on all received datasets
                /** @type {?} */
                var chart = metadata.chart[i];
                if (chart.dataelement == dataElement) {
                    // Now create the timeseries (area or line)
                    /** @type {?} */
                    var chartType = chart.type;
                    _yField = (groupId + "." + scenarioId + ".") + chart.dataelement;
                    /* if the series already exist, change only their styles if have been changed
                              once the data changed */
                    // var series:Series = this.getSeriesByYField(yField);
                    /** @type {?} */
                    var series = this.seriesList.getValue(yField);
                    if (series) {
                        // if (series is CustomAreaSeries)
                        if (series.seriesType == 'area') {
                            // var area:CustomAreaSeries = series as CustomAreaSeries;
                            /** @type {?} */
                            var seriesStyle = series.appliedStyle;
                            if (chart.seriesStyle != seriesStyle) {
                                series.appliedStyle = chart.seriesStyle;
                            }
                        }
                        else {
                            /** @type {?} */
                            var seriesStyle = series.appliedStyle;
                            if (chart.seriesStyle != seriesStyle) {
                                series.appliedStyle = chart.seriesStyle;
                            }
                        }
                    }
                    else {
                        if (chartType == "area") {
                            /** @type {?} */
                            var areaSeries = new Series;
                            areaSeries.appliedStyle = chart.seriesStyle;
                            // areaSeries.displayName = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']');
                            areaSeries.displayName = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement);
                            if (this.isEntityTimeframe)
                                areaSeries.xField = "timeSlotE";
                            else
                                areaSeries.xField = "timeSlot";
                            //Set tooltip for legend in this stanadard :Tooltip: <Dataset description>.<scenario name> <Group name>		E.g.  "Forecast Outflow.Scenario 1.Global Euro"
                            // areaSeries.legendTooltip = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']') + "." + chart.legendTooltip;
                            areaSeries.legendTooltip = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "." + chart.legendTooltip;
                            areaSeries.yField = _yField;
                            areaSeries.legendDisplayName = chart.chartId;
                            areaSeries.visible = visible;
                            areaSeries.seriesType = 'area';
                            this.seriesList.put(_yField, areaSeries);
                            // setTimeout(() => {
                            this.updateAreaLegend(true);
                            // }, 0);
                        }
                        else {
                            // var lineSeries:ILMCustomLineSeries = new ILMCustomLineSeries(); 	
                            /** @type {?} */
                            var lineSeries = new Series;
                            // SeriesStyleProvider.applyLineSeriesStyle(lineSeries,chart.@seriesStyle);
                            lineSeries.appliedStyle = chart.seriesStyle;
                            // lineSeries.displayName = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']');
                            lineSeries.displayName = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement);
                            if (this.isEntityTimeframe)
                                lineSeries.xField = "timeSlotE";
                            else
                                lineSeries.xField = "timeSlot";
                            lineSeries.yField = _yField;
                            //FIXME:CHECK utility
                            // lineSeries.interpolateValues = true;
                            //Set tooltip for legend in this stanadard :Tooltip: <Dataset description>.<scenario name> <Group name>		E.g.  "Forecast Outflow.Scenario 1.Global Euro"
                            // lineSeries.legendTooltip = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']') + "." + chart.legendTooltip;
                            lineSeries.legendTooltip = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "." + chart.legendTooltip;
                            // lineSeries.verticalAxis = vAxisLeft;						
                            lineSeries.legendDisplayName = chart.chartId;
                            /* 	Add the renderer in the chart values container which will contain the live value when moving
                                          the mouse in the chart */
                            /** @type {?} */
                            var seriesLabel = (/** @type {?} */ (this.chartValuesContainer.addChild(ILMSeriesLiveValue)));
                            // var seriesLabel:ILMSeriesLiveValue = new ILMSeriesLiveValue();
                            // the id of the serie 
                            seriesLabel.seriesId = lineSeries.yField;
                            // the value is set to empty on the first add
                            if (this.parentDocument.currencyFormat == 'currencyPat1') {
                                seriesLabel.seriesValue = "0.00";
                            }
                            else {
                                seriesLabel.seriesValue = "0,00";
                            }
                            seriesLabel.seriesStyle = chart.seriesStyle;
                            // the cercle fill for the renderer
                            // seriesLabel.cercleFill = cercleFill;
                            // We have always the cercle for the series label 
                            seriesLabel.isTimeLiveItem = false;
                            // Set the new defined renderer to the requested line series
                            lineSeries.seriesLabel = seriesLabel;
                            if (!visible) {
                                lineSeries.seriesLabel.visible = false;
                                lineSeries.seriesLabel.includeInLayout = false;
                            }
                            lineSeries.visible = visible;
                            lineSeries.seriesType = 'line';
                            // Add the renderer to the container if it is the first add of the line series
                            this.seriesList.put(_yField, lineSeries);
                            // }
                            if (!visible) {
                                lineSeries.seriesLabel.visible = false;
                            }
                            // updateLineLegend needs to be called twice to validate properties
                            this.updateLineLegend(true);
                        }
                    }
                    break;
                }
            }
            return true;
        }
        catch (e) {
        }
    };
    /**
   *
   * Remove a group from list of data sets
   *
   **/
    /**
     *
     * Remove a group from list of data sets
     *
     *
     * @param {?} groupId
     * @return {?}
     */
    ILMLineChart.prototype.removeFromDataSet = /**
     *
     * Remove a group from list of data sets
     *
     *
     * @param {?} groupId
     * @return {?}
     */
    function (groupId) {
        for (var key in this.datasets) {
            if (key.indexOf(groupId) != -1) {
                delete this.datasets[key];
            }
        }
    };
    /**
     * Draws sorted legend items for line series
     * */
    /**
     * Draws sorted legend items for line series
     *
     * @param {?=} recall
     * @return {?}
     */
    ILMLineChart.prototype.updateLineLegend = /**
     * Draws sorted legend items for line series
     *
     * @param {?=} recall
     * @return {?}
     */
    function (recall) {
        var _this = this;
        if (recall === void 0) { recall = false; }
        if (!this.stopUpdateLegend) {
            /** @type {?} */
            var isEmpty = true;
            /** @type {?} */
            var tmp = [];
            for (var i = 0; i < this.seriesList.getValues().length; i++) {
                /** @type {?} */
                var serie = this.seriesList.getValues()[i];
                if (serie && serie.seriesType === 'line' && serie.yField.indexOf("NO_MORE_LIQUIDITY_AVAILABLE") == -1) {
                    isEmpty = false;
                    tmp.push(serie);
                }
            }
            this._balancesLegend.dataProvider = tmp;
            if (isEmpty) {
                this.balanceLabel.visible = false;
                this.balanceLabel.includeInLayout = false;
            }
            else {
                this.balanceLabel.visible = true;
                this.balanceLabel.includeInLayout = true;
            }
            // updateLineLegend needs to be called twice to validate properties
            if (recall)
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this.updateLineLegend(false);
                }), 0);
        }
    };
    /**
           * Updates the threshold color when series color changes
           * */
    //FIXME: need tp be dp,e
    /**
     * Updates the threshold color when series color changes
     *
     * @param {?} groupId
     * @param {?} styleName
     * @return {?}
     */
    //FIXME: need tp be dp,e
    ILMLineChart.prototype.updateThresholdStyleColor = /**
     * Updates the threshold color when series color changes
     *
     * @param {?} groupId
     * @param {?} styleName
     * @return {?}
     */
    //FIXME: need tp be dp,e
    function (groupId, styleName) {
        /** @type {?} */
        var dto = this.datasets[groupId.concat(".Thresholds")];
        if (dto) {
            dto["thresholdsColor"] = styleName;
            /** @type {?} */
            var color = SeriesStyleProvider.getStyleColor(styleName);
            for (var type in ILMLineChart.THRESHOLD_TYPE) {
                /** @type {?} */
                var yField = groupId.concat(".Thresholds.").concat(type);
                /** @type {?} */
                var thresholdSeries = this.seriesList.getValue(yField);
                if (thresholdSeries) {
                    // var theresholdShadow: uint = 0XFF8C00;
                    // //Glow effect attributes
                    // var alpha: Number = 0.3;
                    // var blurX: Number = 6;
                    // var blurY: Number = 2;
                    // var strength: Number = 100;
                    // var quality: Number = 1;
                    // // set color to red if the threshold is max2 or min 2
                    // if (type.indexOf("2") != -1) {
                    //   theresholdShadow = 0xFF0000;
                    // }
                    // var seriesStyle: SeriesStyle = SeriesStyleProvider.drawDashedLineSeriesStyle(0.5, color);
                    // SeriesStyleProvider.setLineSeriesStyle(thresholdSeries, seriesStyle);
                    // //Add glow effect to thresholds
                    // var glowFilter: GlowFilter = new GlowFilter(theresholdShadow, alpha, blurX, blurY, strength, quality);
                    // thresholdSeries.filters = [glowFilter];
                }
            }
        }
    };
    /**
  * Draws sorted legend items for area series
  * */
    /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    ILMLineChart.prototype.updateAreaLegend = /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    function (recall) {
        var _this = this;
        if (recall === void 0) { recall = false; }
        if (!this.stopUpdateLegend) {
            /** @type {?} */
            var isEmpty = true;
            /** @type {?} */
            var tmp = [];
            // for (var i:int = 0 ; i < this.series.length ; i++) 
            for (var i = 0; i < this.seriesList.getValues().length; i++) {
                /** @type {?} */
                var serie = this.seriesList.getValues()[i];
                if (serie.seriesType == 'area') {
                    // var areaSerie:CustomAreaSeries = 	(this.series[i] as CustomAreaSeries);
                    if (StringUtils.isEmpty(serie.minField)) {
                        isEmpty = false;
                        tmp.push(serie);
                    }
                }
            }
            this._accumulatedDCLegend.dataProvider = tmp;
            // updateAreaLegend needs to be called twice to validate properties
            if (recall)
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this.updateAreaLegend(false);
                }), 0);
            if (isEmpty) {
                this.accumLabel.visible = false;
                this.accumLabel.includeInLayout = false;
            }
            else {
                this.accumLabel.visible = true;
                this.accumLabel.includeInLayout = true;
            }
        }
    };
    /**
           * Draws sorted legend items for area series
           * */
    /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    ILMLineChart.prototype.updateLiquidityZonesLegend = /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    function (recall) {
        var _this = this;
        if (recall === void 0) { recall = false; }
        /** @type {?} */
        var tmp = [];
        if (this.parentDocument.showSourceLiquidity) {
            this.accumLabel.visible = false;
            this.accumLabel.includeInLayout = false;
        }
        else {
            this.accumLabel.visible = true;
            this.accumLabel.includeInLayout = true;
        }
        for (var i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            var serie = this.seriesList.getValues()[i];
            if (serie.seriesType == 'area') {
                if (!StringUtils.isEmpty(serie.minField)) {
                    tmp.push(serie);
                }
            }
        }
        // console.trace();
        this._assetsLegend.dataProvider = tmp;
        // updateAreaLegend needs to be called twice to validate properties
        if (recall)
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.updateLiquidityZonesLegend(false);
            }), 0);
        /*if (isEmpty){
        accumLabel.visible = false;
        accumLabel.includeInLayout = false;
        }
        else{
        accumLabel.visible = true;
        accumLabel.includeInLayout = true;
        }*/
    };
    /**
     * Updates local datasets
     * value: datasets.dataset
     * */
    /**
     * Updates local datasets
     * value: datasets.dataset
     *
     * @param {?} metadatas
     * @return {?}
     */
    ILMLineChart.prototype.updateMetadatas = /**
     * Updates local datasets
     * value: datasets.dataset
     *
     * @param {?} metadatas
     * @return {?}
     */
    function (metadatas) {
        /** @type {?} */
        var metaDataList = [];
        /** @type {?} */
        var chartsArray = [];
        // if(metadatas.metadata && !metadatas.metadata.length)
        //    metaDataList.push(metadatas.metadata);
        // else 
        //   metaDataList = metadatas.metadata;
        metaDataList = SwtUtil.convertObjectToArray(metadatas.metadata);
        for (var i = 0; i < metaDataList.length; i++) {
            // Loop on all received datasets
            /** @type {?} */
            var thresholdsColorStyle = "";
            /** @type {?} */
            var xmlData = metaDataList[i];
            /** @type {?} */
            var groupId = xmlData.groupId;
            if (xmlData.charts && !xmlData.charts.length)
                chartsArray.push(xmlData.charts);
            else
                chartsArray = xmlData.charts;
            for (var k = 0; k < chartsArray.length; k++) {
                /** @type {?} */
                var charts = chartsArray[k];
                /** @type {?} */
                var scenarioId = charts.scenarioId;
                /** @type {?} */
                var dto = new Object();
                if (charts.scenarioId == "Standard")
                    thresholdsColorStyle = this.getThresholdsColorStyle(charts);
                dto['charts'] = charts;
                this.datasets[groupId + '.' + scenarioId] = dto;
            }
            /** @type {?} */
            var thresholds = new Object();
            thresholds["thresholds"] = xmlData.thresholds;
            thresholds['thresholdsColor'] = thresholdsColorStyle;
            this.datasets[groupId + '.Thresholds'] = thresholds;
        }
    };
    /**
           * Updates the dataprovider
           * */
    /**
     * Updates the dataprovider
     *
     * @param {?} xmlDatasets
     * @param {?} SODSelected
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @param {?=} uncheckedThresholdFromProfile
     * @return {?}
     */
    ILMLineChart.prototype.updateDataprovider = /**
     * Updates the dataprovider
     *
     * @param {?} xmlDatasets
     * @param {?} SODSelected
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @param {?=} uncheckedThresholdFromProfile
     * @return {?}
     */
    function (xmlDatasets, SODSelected, selectedItemsIntree, uncheckedItemFromLegends, uncheckedThresholdFromProfile) {
        if (selectedItemsIntree === void 0) { selectedItemsIntree = null; }
        if (uncheckedItemFromLegends === void 0) { uncheckedItemFromLegends = null; }
        if (uncheckedThresholdFromProfile === void 0) { uncheckedThresholdFromProfile = null; }
        /** @type {?} */
        var newDp = [];
        /** @type {?} */
        var noSodDp = [];
        this.JSONDataNOSOD = new HashMap;
        this.JSONDataSOD = new HashMap;
        this.JSONDataSODAsString = '';
        /** @type {?} */
        var listOfGroups = [];
        if (this.dataProvider != null && this.dataProvider.length > 0) {
            if (SODSelected) {
                // newDp = (this.dataProvider as ArrayCollection).toArray();
                // noSodDp = (this.NoSOD_Dataprovider as ArrayCollection).toArray();
                newDp = this.dataProvider;
                noSodDp = this.NoSOD_Dataprovider;
            }
            else {
                newDp = this.NoSOD_Dataprovider;
                noSodDp = this.dataProvider;
            }
        }
        if (selectedItemsIntree == null)
            selectedItemsIntree = [];
        if (uncheckedItemFromLegends == null)
            uncheckedItemFromLegends = [];
        if (uncheckedThresholdFromProfile == null)
            uncheckedThresholdFromProfile = [];
        /** @type {?} */
        var dataToPass;
        /** @type {?} */
        var dataToPassSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartColor;
        /** @type {?} */
        var valueThresholdAsNumber;
        /** @type {?} */
        var valueThresholdAsNumberIncSOD;
        /** @type {?} */
        var xmlDatasetsAsArray = SwtUtil.convertObjectToArray(xmlDatasets.dataset);
        for (var k = 0; k < xmlDatasetsAsArray.length; k++) {
            /** @type {?} */
            var dataset = xmlDatasetsAsArray[k];
            /** @type {?} */
            var groupId = dataset.groupId;
            /** @type {?} */
            var scenarioId = dataset.scenarioId;
            /** @type {?} */
            var data = dataset.data;
            if (data) {
                data.parent = dataset;
                this.updateDataProviderFor(groupId, scenarioId, data, newDp, noSodDp, selectedItemsIntree, uncheckedItemFromLegends);
                if (listOfGroups.indexOf(groupId) == -1) {
                    listOfGroups.push(groupId);
                }
            }
        }
        // for each (var thresholdGroupId:string in listOfGroups)
        for (var k = 0; k < listOfGroups.length; k++) {
            /** @type {?} */
            var thresholdGroupId = listOfGroups[k];
            /** @type {?} */
            var dto = this.datasets[thresholdGroupId + '.Thresholds'];
            if (dto) {
                /** @type {?} */
                var thresholds = dto["thresholds"];
                /** @type {?} */
                var styleColor = dto["thresholdsColor"];
                /** @type {?} */
                var threasholdValue;
                /** @type {?} */
                var chartNameAsString;
                /** @type {?} */
                var visiblity;
                /** @type {?} */
                var visiblityAsString;
                // for each (var chart:XML in thresholds.children()){
                for (var chart in thresholds) {
                    threasholdValue = '' + thresholds[chart];
                    if (('' + threasholdValue).charAt(0) == '.') {
                        threasholdValue = '0' + threasholdValue;
                    }
                    if (threasholdValue != null && threasholdValue.length > 0) {
                        valueThresholdAsNumber = Number(threasholdValue);
                        valueThresholdAsNumberIncSOD = valueThresholdAsNumber - this.sumExternalSodMap[thresholdGroupId];
                        chartNameAsString = chart;
                        chartName = "\"name\":\"" + thresholdGroupId + '.' + chartNameAsString + "\"";
                        chartTypeToDisplay = "\"type\":\"Threshold\"";
                        chartStyleName = "\"chartStyleName\":\"" + styleColor + "\"";
                        /*visiblity = (uncheckedThresholdFromProfile.indexOf(thresholdGroupId) == -1);*/
                        if (selectedItemsIntree.indexOf(thresholdGroupId + '.Thresholds') == -1) {
                            visiblity = false;
                        }
                        else {
                            if (uncheckedThresholdFromProfile.indexOf(thresholdGroupId) == -1) {
                                visiblity = true;
                            }
                            else {
                                visiblity = false;
                            }
                        }
                        visiblityAsString = "\"visibility\":\"" + visiblity + "\"";
                        if (chartNameAsString.indexOf("2") != -1) {
                            chartColor = "\"color\":\"hsla(0, 100%, 50%, 0.3)\"";
                        }
                        else {
                            chartColor = "\"color\":\"hsla(33, 100%, 50%, 0.3)\"";
                        }
                        dataToPass = "\"data\":[" + valueThresholdAsNumber + "]";
                        dataToPassSOD = "\"dataSOD\":[" + valueThresholdAsNumberIncSOD + "]";
                        if (this.JSONDataSODAsString.length == 0)
                            this.JSONDataSODAsString += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + visiblityAsString + "," + chartColor + "," + dataToPass + "," + dataToPassSOD + "}";
                        else
                            this.JSONDataSODAsString += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + visiblityAsString + "," + chartColor + "," + dataToPass + "," + dataToPassSOD + "}";
                    }
                }
            }
        }
        this.dataProvider = newDp;
        this.NoSOD_Dataprovider = noSodDp;
    };
    /**
     * Updates the dataprovider for group/scenario
     * Returns false if an error on timeSlots, otherwise true
     * value: dataset.data
     * */
    /**
     * Updates the dataprovider for group/scenario
     * Returns false if an error on timeSlots, otherwise true
     * value: dataset.data
     *
     * @private
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?} value
     * @param {?} newDp
     * @param {?} noSodDp
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @return {?}
     */
    ILMLineChart.prototype.updateDataProviderFor = /**
     * Updates the dataprovider for group/scenario
     * Returns false if an error on timeSlots, otherwise true
     * value: dataset.data
     *
     * @private
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?} value
     * @param {?} newDp
     * @param {?} noSodDp
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @return {?}
     */
    function (groupId, scenarioId, value, newDp, noSodDp, selectedItemsIntree, uncheckedItemFromLegends) {
        if (selectedItemsIntree === void 0) { selectedItemsIntree = null; }
        if (uncheckedItemFromLegends === void 0) { uncheckedItemFromLegends = null; }
        /** @type {?} */
        var valueOfHashMapElement = [];
        /** @type {?} */
        var valueOfHashMapElementNoSOD = [];
        this.JSONDataNOSOD = new HashMap;
        this.JSONDataSOD = new HashMap;
        this.timeRangeArray = [];
        this.timeRangeArrayEntity = [];
        /** @type {?} */
        var decRegex = new RegExp(',');
        /** @type {?} */
        var tempSOD = '' + value.parent.SUM_FORECAST_SOD;
        /** @type {?} */
        var sumCreditLineTotal = value.parent.SUM_CREDIT_LINE_TOTAL || 0;
        /** @type {?} */
        var grpScenKey = (groupId + "." + scenarioId + ".");
        this.liquidityZonesListValues[grpScenKey + "SUM_CREDIT_LINE_TOTAL"] = sumCreditLineTotal;
        /** @type {?} */
        var sumCollateral = value.parent.SUM_COLLATERAL || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_COLLATERAL"] = sumCollateral;
        /** @type {?} */
        var sumUnLiquidAssets = value.parent.SUM_UN_LIQUID_ASSETS || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_UN_LIQUID_ASSETS"] = sumUnLiquidAssets;
        /** @type {?} */
        var sumOtherTotal = value.parent.SUM_OTHER_TOTAL || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_OTHER_TOTAL"] = sumOtherTotal;
        tempSOD = tempSOD.replace(decRegex, '.');
        if (tempSOD.charAt(0) == '.') {
            tempSOD = '0' + tempSOD;
        }
        /** @type {?} */
        var sumForecastSod = Number(tempSOD);
        tempSOD = '' + value.parent.SUM_EXTERNAL_SOD;
        tempSOD = tempSOD.replace(decRegex, '.');
        if (tempSOD.charAt(0) == '.') {
            tempSOD = '0' + tempSOD;
        }
        /** @type {?} */
        var sumExternalSod = Number(tempSOD);
        this.sumExternalSodMap[groupId] = sumExternalSod;
        /** @type {?} */
        var idx = 0;
        /** @type {?} */
        var partOfDataProvider = [];
        /** @type {?} */
        var partOfDataProviderNOSOD = [];
        /** @type {?} */
        var firstTimeToShow;
        /** @type {?} */
        var firstTimeToShowWithEntityTimeFrame;
        // Complexity should be O(n)x8x(currentDp[i] time) => complexity=O(nLog(n))
        // for each(var resultXml: XML in value.result)
        for (var k = 0; k < value.result.length; k++) {
            /** @type {?} */
            var resultXml = value.result[k];
            /** @type {?} */
            var resultObj = partOfDataProvider.length == 0 ? new Object() : partOfDataProvider[idx];
            resultObj = !resultObj ? new Object() : resultObj;
            /** @type {?} */
            var resultObjNoSOD = partOfDataProviderNOSOD.length == 0 ? new Object() : partOfDataProviderNOSOD[idx];
            resultObjNoSOD = !resultObjNoSOD ? new Object() : resultObjNoSOD;
            /** @type {?} */
            var slotsAmountsArr = resultXml.toString().split(";");
            if (slotsAmountsArr.length == 11) {
                tempSOD = slotsAmountsArr[10];
                tempSOD = tempSOD.replace(decRegex, '.');
                if (tempSOD.charAt(0) == '.') {
                    tempSOD = '0' + tempSOD;
                }
                sumForecastSod = Number(tempSOD);
                tempSOD = '' + slotsAmountsArr[9];
                tempSOD = tempSOD.replace(decRegex, '.');
                if (tempSOD.charAt(0) == '.') {
                    tempSOD = '0' + tempSOD;
                }
                sumExternalSod = Number(tempSOD);
            }
            for (var i = 0; i < slotsAmountsArr.length; i++) {
                if ((slotsAmountsArr.length == 11) && (i == 9 || i == 10))
                    continue;
                // var dataField: string = ILMLineChart.ARRAY_COLLECTION_FROM_SERIES.getItemAt(i).toString();
                /** @type {?} */
                var dataField = ILMLineChart.ARRAY_COLLECTION_FROM_SERIES[i];
                /** @type {?} */
                var xmlValue = '' + slotsAmountsArr[i];
                xmlValue = xmlValue.replace(decRegex, '.');
                /** @type {?} */
                var sodType = dataField;
                // keep timeSlot data field not changed
                if (dataField != 'timeSlot' && dataField != 'timeSlotE') {
                    dataField = (groupId + "." + scenarioId + ".") + dataField;
                }
                // If not the first and second (timeSlot and timeSlotE)
                if (i != 0 && i != 1) {
                    if (xmlValue && xmlValue != '') {
                        if (xmlValue.charAt(0) == '.') {
                            xmlValue = '0' + xmlValue;
                        }
                        /** @type {?} */
                        var num = Number(xmlValue);
                        if (this.JSONDataSOD.getValue(dataField) == null) {
                            valueOfHashMapElement = new Array;
                        }
                        else {
                            valueOfHashMapElement = this.JSONDataSOD.getValue(dataField);
                        }
                        if (this.JSONDataNOSOD.getValue(dataField) == null) {
                            valueOfHashMapElementNoSOD = new Array;
                        }
                        else {
                            valueOfHashMapElementNoSOD = this.JSONDataNOSOD.getValue(dataField);
                        }
                        resultObj[dataField] = num;
                        valueOfHashMapElement.push("" + num);
                        this.JSONDataSOD.put(dataField, valueOfHashMapElement);
                        if ((sodType == ILMLineChart.FORECAST_BALANCE_TAG) || (sodType == ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG)) {
                            resultObjNoSOD[dataField] = num - sumForecastSod;
                            valueOfHashMapElementNoSOD.push("" + (num - sumForecastSod));
                        }
                        else if (sodType == ILMLineChart.ACTUAL_BALANCE_TAG) {
                            resultObjNoSOD[dataField] = num - sumExternalSod;
                            valueOfHashMapElementNoSOD.push("" + (num - sumExternalSod));
                        }
                        else {
                            resultObjNoSOD[dataField] = num;
                            valueOfHashMapElementNoSOD.push("" + num);
                        }
                        this.JSONDataNOSOD.put(dataField, valueOfHashMapElementNoSOD);
                    }
                }
                else {
                    if (isNaN(firstTimeToShow) && dataField == 'timeSlot')
                        firstTimeToShow = moment(xmlValue).toDate().getTime();
                    // firstTimeToShow = new Date(xmlValue).getTime();
                    // firstTimeToShow = DateFormatter.parseDateString(xmlValue).getTime();
                    if (isNaN(firstTimeToShowWithEntityTimeFrame) && dataField == 'timeSlotE')
                        firstTimeToShowWithEntityTimeFrame = moment(xmlValue).toDate().getTime();
                    //firstTimeToShowWithEntityTimeFrame = DateFormatter.parseDateString(xmlValue).getTime();
                    // Timeslots
                    resultObj[dataField] = xmlValue;
                    resultObjNoSOD[dataField] = xmlValue;
                    if (dataField == 'timeSlot') {
                        this.timeRangeArray.push(xmlValue);
                    }
                    else if (dataField == 'timeSlotE') {
                        this.timeRangeArrayEntity.push(xmlValue);
                    }
                }
            }
            partOfDataProvider[idx] = resultObj;
            partOfDataProviderNOSOD[idx] = resultObjNoSOD;
            idx++;
        }
        newDp[0] = partOfDataProvider[0];
        noSodDp[0] = partOfDataProviderNOSOD[0];
        newDp[1] = partOfDataProvider[idx - 1];
        noSodDp[1] = partOfDataProviderNOSOD[idx - 1];
        /** @type {?} */
        var pointStart = "\"pointStart\":" + firstTimeToShow;
        /** @type {?} */
        var pointStartEntity = "\"pointStartEntity\":" + firstTimeToShowWithEntityTimeFrame;
        /** @type {?} */
        var pointInterval = "\"pointInterval\":60000";
        /** @type {?} */
        var dataLength;
        /** @type {?} */
        var dataToPass;
        /** @type {?} */
        var dataToPassSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartTypeDetailsToDisplay;
        /** @type {?} */
        var chartlegendDisplayName;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartDisplayName;
        /** @type {?} */
        var chartDisplayLabel;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartStyleColor;
        /** @type {?} */
        var chartStyleBorderColor;
        /** @type {?} */
        var isVisibleChart;
        /** @type {?} */
        var dto = this.datasets[groupId + '.' + scenarioId];
        if (!dto) {
            //trace("Chart not exist and to plot it need to send request to DB side");
            //throw Error("Programming error: method [updateDataSets] should be called first !");
            return false;
        }
        /** @type {?} */
        var metadata = dto['charts'];
        /*if(currencyId != metadata.@currencyId || entityId != metadata.@entityId)
            {
            return false;
            }*/
        /** @type {?} */
        var _yField = "";
        /** @type {?} */
        var isVisibleFromLegend = true;
        //for each(var str:string in JSONDataSOD.getKeys()){
        // for (var str in this.JSONDataSOD) {
        for (var i_1 = 0; i_1 < this.JSONDataSOD.getKeys().length; i_1++) {
            //	if(selectedItemsIntree.indexOf(str) != -1 ) {
            /** @type {?} */
            var str = this.JSONDataSOD.getKeys()[i_1];
            dataLength = "\"dataLength\":" + this.JSONDataSOD.getValue(str).length;
            chartName = "\"name\":\"" + str + "\"";
            if (selectedItemsIntree.indexOf(str) == -1) {
                isVisibleFromLegend = false;
            }
            else {
                if (uncheckedItemFromLegends.indexOf(str) == -1) {
                    isVisibleFromLegend = true;
                }
                else {
                    isVisibleFromLegend = false;
                }
            }
            /** @type {?} */
            var chartIdParts = str.split(".");
            // for each (var chart:XML in metadata.children()){
            for (var k = 0; k < metadata.chart.length; k++) {
                /** @type {?} */
                var chart = metadata.chart[k];
                if (chart.dataelement == chartIdParts[2]) {
                    // Now create the timeseries (area or line)
                    /** @type {?} */
                    var chartType = chart.type;
                    chartTypeToDisplay = "\"type\":\"" + chart.type + "\"";
                    chartTypeDetailsToDisplay = "\"typeDetails\":\"" + SeriesStyleProvider.getStyleType(chart.seriesStyle) + "\"";
                    _yField = (groupId + "." + scenarioId + ".") + chart.dataelement;
                    chartStyleName = "\"chartStyleName\":\"" + SeriesStyleProvider.getStyleName(chart.seriesStyle) + "\"";
                    chartStyleColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(chart.seriesStyle) + "\"";
                    chartStyleBorderColor = "\"borderColor\":\"" + SeriesStyleProvider.getStyleBorderColorAsSring(chart.seriesStyle) + "\"";
                    chartDisplayName = "\"chartDisplayName\":\"" + chart.dataelement + "\"";
                    chartDisplayLabel = "\"chartDisplayLabel\":\"" + ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "\"";
                    // chartDisplayLabel = "\"chartDisplayLabel\":\"" + chart.dataelement + "\"";
                    chartlegendDisplayName = "\"chartlegendDisplayName\":\"" + chart.chartId + "\"";
                    isVisibleChart = "\"visibility\":\"" + isVisibleFromLegend + "\"";
                }
            }
            dataToPass = "\"data\":[" + this.JSONDataSOD.getValue(str) + "]";
            dataToPassSOD = "\"dataSOD\":[" + this.JSONDataNOSOD.getValue(str) + "]";
            if (this.JSONDataSODAsString.length == 0)
                this.JSONDataSODAsString += "{" + pointStart + "," + pointStartEntity + "," + pointInterval + "," + dataLength + "," + chartName + "," + isVisibleChart + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartDisplayName + "," + chartDisplayLabel + "," + chartlegendDisplayName + "," + dataToPass + "," + dataToPassSOD + "}";
            else
                this.JSONDataSODAsString += ",{" + pointStart + "," + pointStartEntity + "," + pointInterval + "," + dataLength + "," + chartName + "," + isVisibleChart + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartDisplayName + "," + chartDisplayLabel + "," + chartlegendDisplayName + "," + dataToPass + "," + dataToPassSOD + "}";
        }
        return true;
    };
    /**
   * Show and hide thresholds legend
   */
    /**
     * Show and hide thresholds legend
     * @param {?} groupId
     * @param {?} selected
     * @return {?}
     */
    ILMLineChart.prototype.showHideThreshold = /**
     * Show and hide thresholds legend
     * @param {?} groupId
     * @param {?} selected
     * @return {?}
     */
    function (groupId, selected) {
        // for each (var series:Series in this.series){
        for (var i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            var series = this.seriesList.getValues()[i];
            if (series.seriesType == 'line') {
                if ((series.yField).indexOf("Thresholds") != -1 && (series.yField).indexOf(groupId) != -1) {
                    series.visible = selected;
                }
            }
        }
    };
    /**
     * @private
     * @param {?} charts
     * @return {?}
     */
    ILMLineChart.prototype.getThresholdsColorStyle = /**
     * @private
     * @param {?} charts
     * @return {?}
     */
    function (charts) {
        //default color
        /** @type {?} */
        var styleColor = SeriesStyleProvider.DASHED_SEGMENT_BLACK;
        /** @type {?} */
        var chartsList = SwtUtil.convertObjectToArray(charts.chart);
        // for each(var chart :XML in charts.children()){
        for (var k = 0; k < chartsList.length; k++) {
            /** @type {?} */
            var chart = chartsList[k];
            if (chart.dataelement == ILMLineChart.ACTUAL_BALANCE_TAG) {
                styleColor = chart.seriesStyle;
                break;
            }
        }
        return styleColor;
    };
    /**
     * @return {?}
     */
    ILMLineChart.prototype.switchDataProvider = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var tempDp = this.dataProvider;
        this.dataProvider = this.NoSOD_Dataprovider;
        this.NoSOD_Dataprovider = tempDp;
    };
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    ILMLineChart.prototype.calculateLiquidityZonesLimites = /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    function (groupId, scenarioId) {
        /** @type {?} */
        var zoneStartValue = NaN;
        /** @type {?} */
        var zoneStartValueSOD = NaN;
        /** @type {?} */
        var zoneEndValue = NaN;
        /** @type {?} */
        var zoneEndValueSOD = NaN;
        /** @type {?} */
        var dataToPassNOSOD;
        /** @type {?} */
        var dataToPasSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartColor;
        /** @type {?} */
        var limitValue = 0;
        /** @type {?} */
        var noMoreValues = 0;
        /** @type {?} */
        var grpScenKey = (groupId + "." + scenarioId + ".");
        /** @type {?} */
        var sumExternalSod = this.sumExternalSodMap[groupId] || 0;
        /** @type {?} */
        var valuesChanged = false;
        /** @type {?} */
        var prevuousJSONSent = this.JsonLiquditiyZonesNOSOD;
        this.JsonLiquditiyZonesNOSOD = "";
        // for each(var asset:String in LIQUIDITY_ZONES_LIST){
        for (var index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
            /** @type {?} */
            var asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
            /** @type {?} */
            var assetsValueAsNumber = this.liquidityZonesListValues[grpScenKey + asset] ? this.liquidityZonesListValues[grpScenKey + asset] : 0;
            if (asset != "NO_MORE_LIQUIDITY_AVAILABLE") {
                zoneEndValue = Number(limitValue + assetsValueAsNumber) * (-1);
                zoneEndValueSOD = -limitValue - Number(assetsValueAsNumber) - sumExternalSod;
                zoneStartValue = -limitValue;
                zoneStartValueSOD = -limitValue - sumExternalSod;
                chartName = "\"name\":\"" + groupId + "." + scenarioId + "." + asset + "\"";
                chartTypeToDisplay = "\"type\":\"area\"";
                chartStyleName = "\"chartStyleName\":\"" + this.getLiquidityRegionColor(asset) + "\"";
                chartColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(this.getLiquidityRegionColor(asset)) + "\"";
                dataToPassNOSOD = "\"dataNoSOD\":[" + zoneStartValue + "," + zoneEndValue + "]";
                dataToPasSOD = "\"dataSOD\":[" + zoneStartValueSOD + "," + zoneEndValueSOD + "]";
                if (this.JsonLiquditiyZonesNOSOD.length == 0)
                    this.JsonLiquditiyZonesNOSOD += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                else
                    this.JsonLiquditiyZonesNOSOD += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                if (asset == "SUM_OTHER_TOTAL") {
                    noMoreValues = Number(limitValue + assetsValueAsNumber) * (-1);
                }
                limitValue = Number(limitValue + assetsValueAsNumber);
            }
            else {
                chartName = "\"name\":\"" + groupId + "." + scenarioId + "." + asset + "\"";
                chartTypeToDisplay = "\"type\":\"line\"";
                chartStyleName = "\"chartStyleName\":\"" + this.getLiquidityRegionColor(asset) + "\"";
                chartColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(this.getLiquidityRegionColor(asset)) + "\"";
                dataToPasSOD = "\"dataSOD\":[" + (noMoreValues - sumExternalSod) + "," + (noMoreValues - sumExternalSod) + "]";
                dataToPassNOSOD = "\"dataNoSOD\":[" + (noMoreValues) + "," + (noMoreValues) + "]";
                if (this.JsonLiquditiyZonesNOSOD.length == 0)
                    this.JsonLiquditiyZonesNOSOD += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                else
                    this.JsonLiquditiyZonesNOSOD += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
            }
        }
        if (prevuousJSONSent != this.JsonLiquditiyZonesNOSOD) {
            return true;
        }
        else {
            return false;
        }
    };
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    ILMLineChart.prototype.removeLiquidityRegion = /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    function (groupId, scenarioId) {
        /** @type {?} */
        var yField = groupId + "." + scenarioId + ".";
        /** @type {?} */
        var asset = null;
        for (var index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
            /** @type {?} */
            var asset_1 = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
            this.seriesList.remove(yField + asset_1);
        }
        //Remove highlighting caused by similar colors in the legend
        for (var index = 0; index < this.highlightedLiquiditySeries.length; index++) {
            /** @type {?} */
            var serie = this.highlightedLiquiditySeries[index];
            /** @type {?} */
            var dto = {};
            dto.highligh = false;
            dto.yField = serie.yField;
            if ($(this.elem.nativeElement).closest('.legendsDivider')) {
                dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
            }
            SeriesHighlightEvent.emit(dto);
            // Dispatch the event so that legends will delete highlight
        }
        this.highlightedLiquiditySeries.pop();
    };
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} withSOD
     * @param {?=} visiblity
     * @return {?}
     */
    ILMLineChart.prototype.drawLiquidityZones = /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} withSOD
     * @param {?=} visiblity
     * @return {?}
     */
    function (groupId, scenarioId, lastSelectedGroup, lastSelectScenario, withSOD, visiblity) {
        if (lastSelectedGroup === void 0) { lastSelectedGroup = null; }
        if (lastSelectScenario === void 0) { lastSelectScenario = null; }
        if (withSOD === void 0) { withSOD = false; }
        if (visiblity === void 0) { visiblity = true; }
        try {
            if (this.dataProvider.length && groupId && scenarioId) {
                /** @type {?} */
                var grpScenKey = (groupId + "." + scenarioId + ".");
                /** @type {?} */
                var sumExternalSod = this.sumExternalSodMap[groupId];
                // var firstItem_before:Object=dataProvider.getItemAt(0);
                // var lastItem_before:Object=dataProvider.getItemAt(dataProvider.length - 1);
                // var firstItemSod_before:Object = NoSOD_Dataprovider.getItemAt(0);
                // var lastItemSod_before:Object = NoSOD_Dataprovider.getItemAt(NoSOD_Dataprovider.length - 1);
                /** @type {?} */
                var firstItem_before = this.dataProvider[0];
                /** @type {?} */
                var lastItem_before = this.dataProvider[this.dataProvider.length - 1];
                /** @type {?} */
                var firstItemSod_before = this.NoSOD_Dataprovider[0];
                /** @type {?} */
                var lastItemSod_before = this.NoSOD_Dataprovider[this.dataProvider.length - 1];
                /** @type {?} */
                var limitValue = 0;
                /** @type {?} */
                var noMoreValues = 0;
                // (this.vAxisLeft as CustomLinearAxis).recalculateMaxMin();
                // min = vAxisLeft.minimum;
                /** @type {?} */
                var zoneStartValue = NaN;
                /** @type {?} */
                var zoneStartValueSOD = NaN;
                /** @type {?} */
                var zoneEndValue = NaN;
                /** @type {?} */
                var zoneEndValueSOD = NaN;
                for (var index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
                    /** @type {?} */
                    var asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
                    if (asset != "NO_MORE_LIQUIDITY_AVAILABLE") {
                        zoneEndValue = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]) * (-1);
                        zoneEndValueSOD = -limitValue - Number(this.liquidityZonesListValues[grpScenKey + asset]) - sumExternalSod;
                        zoneStartValue = -limitValue;
                        zoneStartValueSOD = -limitValue - sumExternalSod;
                        //FIXME:CHECK IF NEEDED
                        // if((zoneEndValue < min && zoneStartValue > min))
                        // 	zoneEndValue = min;
                        // if((zoneEndValueSOD < min && zoneStartValueSOD > min))
                        // 	zoneEndValueSOD = min;
                        if (this.includeSOD) {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                        }
                        else {
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                        }
                        if (asset == "SUM_OTHER_TOTAL") {
                            noMoreValues = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]) * (-1);
                        }
                        if (this.includeSOD) {
                            firstItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                            lastItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                        }
                        else {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            firstItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            lastItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                        }
                        this.liquidityZonesListLimits.put(groupId + "." + scenarioId + "." + asset, limitValue);
                        limitValue = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]);
                    }
                    else {
                        if (this.includeSOD) {
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                        }
                        else {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                        }
                        this.liquidityZonesListValues[grpScenKey + "NO_MORE_LIQUIDITY_AVAILABLE"] = noMoreValues;
                    }
                }
                this.drawLiquidityRegion(groupId, scenarioId, lastSelectedGroup, lastSelectScenario, visiblity);
                //Run action on IFRAME
                /** @type {?} */
                var isUpdated = this.calculateLiquidityZonesLimites(groupId, scenarioId);
                this.parentDocument.refreshSourceOfLiquidityForIFrame(isUpdated);
            }
        }
        catch (err) {
        }
    };
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} visiblity
     * @return {?}
     */
    ILMLineChart.prototype.drawLiquidityRegion = /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} visiblity
     * @return {?}
     */
    function (groupId, scenarioId, lastSelectedGroup, lastSelectScenario, visiblity) {
        if (lastSelectedGroup === void 0) { lastSelectedGroup = null; }
        if (lastSelectScenario === void 0) { lastSelectScenario = null; }
        if (visiblity === void 0) { visiblity = true; }
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (groupId != null && scenarioId != null) {
                /** @type {?} */
                var yField = groupId + "." + scenarioId + ".";
                /** @type {?} */
                var assetValue;
                for (var index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
                    /** @type {?} */
                    var asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
                    if (asset != 'NO_MORE_LIQUIDITY_AVAILABLE') {
                        // var areaSeries:CustomAreaSeries = new CustomAreaSeries(); 	
                        /** @type {?} */
                        var areaSeries = new Series;
                        areaSeries.visible = visiblity;
                        if (this.parentDocument.currencyFormat == 'currencyPat1') {
                            errorLocation = 27;
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            // assetValue = this.leftVerticalAxisFormatter(Number(amount));
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), ',');
                            errorLocation = 2;
                        }
                        else {
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            // assetValue = this.leftVerticalAxisFormatter(Number(amount));
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), '.');
                        }
                        areaSeries.legendDisplayName = ExternalInterface.call('getBundle', 'text', asset, asset) + "|" + assetValue;
                        /** @type {?} */
                        var areaColor = this.getLiquidityRegionColor(asset);
                        /** @type {?} */
                        var minField = yField + asset + "Limit";
                        if (this.isEntityTimeframe)
                            areaSeries.xField = "timeSlotE";
                        else
                            areaSeries.xField = "timeSlot";
                        areaSeries.appliedStyle = areaColor;
                        areaSeries.yField = yField + asset;
                        areaSeries.minField = minField;
                        areaSeries.seriesType = 'area';
                        this.seriesList.put(yField + asset, areaSeries);
                        errorLocation = 5;
                    }
                    else {
                        /** @type {?} */
                        var lineSeries = new Series;
                        lineSeries.visible = visiblity;
                        if (this.parentDocument.currencyFormat == 'currencyPat1') {
                            errorLocation = 27;
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), ',');
                            errorLocation = 2;
                        }
                        else {
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), '.');
                        }
                        lineSeries.displayName = yField + asset;
                        lineSeries.legendDisplayName = ExternalInterface.call('getBundle', 'text', asset, asset) + "|" + assetValue;
                        /** @type {?} */
                        var lineColor = this.getLiquidityRegionColor(asset);
                        lineSeries.appliedStyle = lineColor;
                        lineSeries.seriesType = 'line';
                        if (this.isEntityTimeframe)
                            lineSeries.xField = "timeSlotE";
                        else
                            lineSeries.xField = "timeSlot";
                        lineSeries.yField = yField + asset;
                        this.seriesList.put(yField + asset, lineSeries);
                    }
                }
                if (!StringUtils.isEmpty(lastSelectedGroup) && !StringUtils.isEmpty(lastSelectScenario)) {
                    this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
                }
                this.updateLiquidityZonesLegend(true);
                //FIXME:
                // callLater(distinctSimilarColorSeries);
            }
            else {
                if (lastSelectedGroup != null && lastSelectScenario != null) {
                    this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
                }
                this.updateLiquidityZonesLegend(false);
            }
        }
        catch (error) {
            if (!StringUtils.isEmpty(lastSelectedGroup) && !StringUtils.isEmpty(lastSelectScenario)) {
                this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
            }
            this._assetsLegend.dataProvider = new Array();
            // Alert.show("No data are availables , need to select (at least)one figure for the correspond group ["+groupId+"] and scenario [ "+scenarioId+"]")
            ExternalInterface.call("console.log", "No data are availables , need to select (at least)one figure for the correspond group: " + groupId);
        }
    };
    /**
     * @param {?} method
     * @param {?} data
     * @return {?}
     */
    ILMLineChart.prototype.callMethodInIframe = /**
     * @param {?} method
     * @param {?} data
     * @return {?}
     */
    function (method, data) {
        // var o = document.getElementsByTagName('iframe')[0];
        this.chartElement.callMethodByName(method, data);
        // if (this.iframeContaier && this.iframeContaier.contentWindow) {
        //   this.iframeContaier.contentWindow.postMessage([method, data], '*');
        // }
    };
    // getIframeWindow(iframe_object) {
    //   var doc;
    //   if (iframe_object.contentWindow) {
    //     return iframe_object.contentWindow;
    //   }
    //   if (iframe_object.window) {
    //     return iframe_object.window;
    //   }
    //   if (!doc && iframe_object.contentDocument) {
    //     doc = iframe_object.contentDocument;
    //   }
    //   if (!doc && iframe_object.document) {
    //     doc = iframe_object.document;
    //   }
    //   if (doc && doc.defaultView) {
    //     return doc.defaultView;
    //   }
    //   if (doc && doc.parentWindow) {
    //     return doc.parentWindow;
    //   }
    //   return undefined;
    // }
    // getIframeWindow(iframe_object) {
    //   var doc;
    //   if (iframe_object.contentWindow) {
    //     return iframe_object.contentWindow;
    //   }
    //   if (iframe_object.window) {
    //     return iframe_object.window;
    //   }
    //   if (!doc && iframe_object.contentDocument) {
    //     doc = iframe_object.contentDocument;
    //   }
    //   if (!doc && iframe_object.document) {
    //     doc = iframe_object.document;
    //   }
    //   if (doc && doc.defaultView) {
    //     return doc.defaultView;
    //   }
    //   if (doc && doc.parentWindow) {
    //     return doc.parentWindow;
    //   }
    //   return undefined;
    // }
    /**
     * @private
     * @param {?} assetName
     * @return {?}
     */
    ILMLineChart.prototype.getLiquidityRegionColor = 
    // getIframeWindow(iframe_object) {
    //   var doc;
    //   if (iframe_object.contentWindow) {
    //     return iframe_object.contentWindow;
    //   }
    //   if (iframe_object.window) {
    //     return iframe_object.window;
    //   }
    //   if (!doc && iframe_object.contentDocument) {
    //     doc = iframe_object.contentDocument;
    //   }
    //   if (!doc && iframe_object.document) {
    //     doc = iframe_object.document;
    //   }
    //   if (doc && doc.defaultView) {
    //     return doc.defaultView;
    //   }
    //   if (doc && doc.parentWindow) {
    //     return doc.parentWindow;
    //   }
    //   return undefined;
    // }
    /**
     * @private
     * @param {?} assetName
     * @return {?}
     */
    function (assetName) {
        /** @type {?} */
        var color = "";
        if (assetName == "SUM_CREDIT_LINE_TOTAL")
            color = SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE;
        else if (assetName == "SUM_COLLATERAL")
            color = SeriesStyleProvider.CONT_AREA_APRICOT_PEACH;
        else if (assetName == "SUM_UN_LIQUID_ASSETS")
            color = SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE;
        else if (assetName == "SUM_OTHER_TOTAL")
            color = SeriesStyleProvider.CONT_AREA_ROSE_FOG;
        else
            color = SeriesStyleProvider.CONT_SEGMENT_BOLD_RED;
        return color;
    };
    /**
           * Updates the style metadata when style popup OK button is clicked.
           * */
    /**
     * Updates the style metadata when style popup OK button is clicked.
     *
     * @param {?} yField
     * @param {?} styleId
     * @return {?}
     */
    ILMLineChart.prototype.updateStyleMetadata = /**
     * Updates the style metadata when style popup OK button is clicked.
     *
     * @param {?} yField
     * @param {?} styleId
     * @return {?}
     */
    function (yField, styleId) {
        /** @type {?} */
        var erroLocation = 0;
        /** @type {?} */
        var arr = yField.split(".");
        /** @type {?} */
        var groupId = arr[0];
        /** @type {?} */
        var scenarioId = arr[1];
        /** @type {?} */
        var dataElement = arr[2];
        /** @type {?} */
        var dto = this.datasets[groupId + '.' + scenarioId];
        // Do this test as 'updateStyleMetadata' can be called from SeriesStylePopup.mxml to serve both group and global tabs
        if (dto) {
            /** @type {?} */
            var charts = dto['charts'];
            /** @type {?} */
            var chartsList = SwtUtil.convertObjectToArray(charts.chart);
            // for each(var chart :XML in charts.children()){
            for (var k = 0; k < chartsList.length; k++) {
                /** @type {?} */
                var chart = chartsList[k];
                if (chart.dataelement == dataElement) {
                    // Update the seriesStyle attribute
                    chart.seriesStyle = styleId;
                }
            }
        }
    };
    /**
     * @return {?}
     */
    ILMLineChart.prototype.getChartStyleSeriesStyleAsString = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var chartsStyles = "";
        /** @type {?} */
        var seriesStyle = "";
        /** @type {?} */
        var yField = "";
        for (var i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            var serie = this.seriesList.getValues()[i];
            if (serie.seriesType == 'area') {
                yField = serie.yField;
                if (yField.indexOf('SUM_UN_LIQUID_ASSETS') == -1 && yField.indexOf('SUM_OTHER_TOTAL') == -1
                    && yField.indexOf('SUM_COLLATERAL') == -1
                    && yField.indexOf('SUM_CREDIT_LINE_TOTAL') == -1) {
                    seriesStyle = serie.appliedStyle;
                    chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
                }
                //FIXME:CECK
            }
            else if (serie.seriesType == 'line' && serie.legendTooltip) {
                yField = serie.yField;
                if (yField.indexOf('NO_MORE_LIQUIDITY_AVAILABLE') == -1) {
                    seriesStyle = serie.appliedStyle;
                    chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
                }
            }
        }
        if (chartsStyles.length > 1)
            chartsStyles = chartsStyles.substr(0, chartsStyles.length - 1);
        return chartsStyles;
    };
    /**
     * @return {?}
     */
    ILMLineChart.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        // this.iframeContaier = document.createElement('iframe');
        // this.iframeContaier.style.cssText = "position: absolute;top:0px;left: 0px; height: 0px;width: 0px; border: 1px solid";
        // this.iframeContaier.setAttribute("class", "iframeClass");
        // this.iframeContaier.src = SwtUtil.getBaseURL()+"charts.htm";
        // document.body.appendChild(this.iframeContaier);
        $(window).resize((/**
         * @return {?}
         */
        function () {
            clearTimeout(_this.resizeFinished);
            _this.resizeFinished = setTimeout((/**
             * @return {?}
             */
            function () {
                if (_this.isVisible(_this.elem.nativeElement)) {
                    _this.callMethodInIframe("redrawChart", [$(_this.elem.nativeElement)[0].clientHeight]);
                }
            }), 250);
        }));
    };
    /**
     * @return {?}
     */
    ILMLineChart.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        //   let mutationObserver = new window.MutationObserver(this.checkDiff)
        //   mutationObserver.observe(this.elem.nativeElement, {
        //     attributes: true,
        //     attributeFilter: ['style', 'class','width', 'x','y']
        //   })
        var _this = this;
        //   this.elem.nativeElement.classList.add('testtesttest');
        //   const ro = new ResizeObserver((entries, observer) => {
        //     if (this.isVisible(this.elem.nativeElement)) {
        //       this.iframeContaier.style.display = 'none';
        //       // this.iframeContaier.style.visibility = 'visible';
        //       var popupRect = this.elem.nativeElement.getBoundingClientRect();
        //       if(popupRect.height < 15){
        //         this.iframeContaier.style.display = 'none';
        //         return;
        //       }
        //       if (this.previousRct.height !== popupRect.height) {
        //         this.iframeContaier.style.height = popupRect.height + "px";
        //       }
        //       if (this.previousRct.left !== popupRect.left) {
        //         this.iframeContaier.style.left = popupRect.left + "px";
        //       }
        //       if (this.previousRct.width !== popupRect.width) {
        //         this.iframeContaier.style.width = popupRect.width + "px";
        //       }
        //       if (this.previousRct.top !== popupRect.top) {
        //         this.iframeContaier.style.top = popupRect.top + "px";
        //       }
        //       //this.elem.nativeElement.getBoundingClientRect()
        //       this.previousRct = { left: this.iframeContaier.style.left, top: this.iframeContaier.top, height: this.iframeContaier.style.height, width: this.iframeContaier.style.width };
        //     } else {
        //       this.iframeContaier.style.display = 'none';
        //     }
        //  });
        // ro.observe(this.elem.nativeElement);
        // let parentHeight = this.elem.nativeElement.offsetParent.clientHeight;
        // const ro = new ResizeObserver((entries, observer) => {
        //   if (this.isVisible(this.elem.nativeElement)) {
        //     //Call redraw function in the chart but limit the call to once per 100 militsecond to win perfermance as 
        //     //ResizeObserver can be callaed too many times in 
        //     // this.redrawChart(100);
        //     // this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
        //     //     this.chart.reflow();
        //     //     this.chart.update({
        //     //       plotOptions: {
        //     //         series: {
        //     //           states: {
        //     //             hover: {
        //     //               enabled: false
        //     //             },
        //     //             inactive: {
        //     //               enabled: false
        //     //             }
        //     //           }
        //     //         }
        //     //       },
        //     //     })
        //   }
        // });
        // ro.observe(this.elem.nativeElement);
        DividerResizeComplete.subscribe((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            // const isSelectedTab =  this.tabName == "GlobalView" && (this.parentDocument.tabNavigator.selectedIndex == 0)
            if (event.id === "legendsDivider" || event.id === "gridDivider" || event.id === "treeDivider") {
                if (_this.isVisible(_this.elem.nativeElement)) {
                    _this.callMethodInIframe("redrawChart", [$(_this.elem.nativeElement)[0].clientHeight]);
                }
            }
        }));
    };
    /**
     * @param {?} e
     * @return {?}
     */
    ILMLineChart.prototype.isVisible = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    };
    // public iframeContaier;
    ILMLineChart.ACCUM_ACTUAL_INFLOW_TAG = "aac";
    ILMLineChart.ACCUM_ACTUAL_OUTFLOWS_TAG = "aad";
    ILMLineChart.ACCUM_FORECAST_OUTFLOWS_TAG = "afd";
    ILMLineChart.ACCUM_FORECAST_INFLOWS_TAG = "afc";
    ILMLineChart.ACTUAL_BALANCE_TAG = "ab";
    ILMLineChart.FORECAST_BALANCE_TAG = "fbb";
    ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG = "fbia";
    ILMLineChart.THRESHOLD_TYPE = ["min1", "min2", "max1", "max2"];
    ILMLineChart.SERIES_LIST = new Array("timeSlot", "timeSlotE", "afd", "afc", "aad", "aac", "ab", "fbb", "fbia", "sumExSOD", "sumForcastSOD");
    // static ARRAY_COLLECTION_FROM_SERIES:ArrayCollection = new ArrayCollection(SERIES_LIST);
    ILMLineChart.ARRAY_COLLECTION_FROM_SERIES = ILMLineChart.SERIES_LIST;
    ILMLineChart.LIQUIDITY_ZONES_LIST = ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE"];
    ILMLineChart.SERIES_IGNORE_HIGHLIGH = ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE", "Thresholds"];
    ILMLineChart.decorators = [
        { type: Component, args: [{
                    selector: 'ILMLineChart',
                    template: "<!--<iframe title=\"charts\" class=\"iframeiframe\" #iframeContaier src=\"http://localhost:8080/swallowtech/charts.htm\" style=\"position: absolute;left: 20px;right: 20px; height: 150px;width: 150px;\" seamless></iframe>-->\r\n<swt-ilm-chart width=\"100%\" height=\"300\"  #chartElement ></swt-ilm-chart>",
                    styles: [""]
                }] }
    ];
    /** @nocollapse */
    ILMLineChart.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    ILMLineChart.propDecorators = {
        chartElement: [{ type: ViewChild, args: ['chartElement',] }]
    };
    return ILMLineChart;
}(Container));
export { ILMLineChart };
if (false) {
    /** @type {?} */
    ILMLineChart.ACCUM_ACTUAL_INFLOW_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_ACTUAL_OUTFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_FORECAST_OUTFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_FORECAST_INFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACTUAL_BALANCE_TAG;
    /** @type {?} */
    ILMLineChart.FORECAST_BALANCE_TAG;
    /** @type {?} */
    ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG;
    /** @type {?} */
    ILMLineChart.THRESHOLD_TYPE;
    /** @type {?} */
    ILMLineChart.SERIES_LIST;
    /** @type {?} */
    ILMLineChart.ARRAY_COLLECTION_FROM_SERIES;
    /** @type {?} */
    ILMLineChart.LIQUIDITY_ZONES_LIST;
    /** @type {?} */
    ILMLineChart.SERIES_IGNORE_HIGHLIGH;
    /** @type {?} */
    ILMLineChart.prototype.chartElement;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.liquidityZonesListValues;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.liquidityZonesListLimits;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.seriesStyleProperty;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.highlightedLiquiditySeries;
    /** @type {?} */
    ILMLineChart.prototype.selectedCurrencyId;
    /** @type {?} */
    ILMLineChart.prototype.selectedEntityId;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._accumulatedDCLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._balancesLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._assetsLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._accumLabel;
    /** @type {?} */
    ILMLineChart.prototype.stopUpdateLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._chartValuesContainer;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._timeDynamicValue;
    /** @type {?} */
    ILMLineChart.prototype.parentDocument;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._visibleThresholds;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.NoSOD_Dataprovider;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.dataProvider;
    /** @type {?} */
    ILMLineChart.prototype.isEntityTimeframe;
    /** @type {?} */
    ILMLineChart.prototype.includeSOD;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.timeFromAsInt;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.timeToAsInt;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.sumExternalSodMap;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._balanceLabel;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._ilmTree;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lowesetAxisValue;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lastSelectedGroupForZones;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lastSelectScenarioForZones;
    /** @type {?} */
    ILMLineChart.prototype.JsonLiquditiyZonesNOSOD;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._jsonData;
    /** @type {?} */
    ILMLineChart.prototype.datasets;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataNOSOD;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataSOD;
    /** @type {?} */
    ILMLineChart.prototype.seriesList;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataSODAsString;
    /** @type {?} */
    ILMLineChart.prototype.timeRangeArray;
    /** @type {?} */
    ILMLineChart.prototype.timeRangeArrayEntity;
    /** @type {?} */
    ILMLineChart.prototype.resizeFinished;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.canCall;
    /** @type {?} */
    ILMLineChart.prototype.redrawChart;
    /** @type {?} */
    ILMLineChart.prototype.debounce;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.previousRct;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiSUxNTGluZUNoYXJ0LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NoYXJ0cy9JTE1DaGFydHMvSUxNTGluZUNoYXJ0L0lMTUxpbmVDaGFydC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7OztBQUFBLE9BQU8sRUFBRSxTQUFTLEVBQVUsVUFBVSxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQVUsTUFBTSxlQUFlLENBQUM7QUFDNUYsT0FBTyxFQUFFLFNBQVMsRUFBRSxNQUFNLDZDQUE2QyxDQUFDO0FBQ3hFLE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSwrQkFBK0IsQ0FBQztBQUU5RCxPQUFPLEVBQUUsbUJBQW1CLEVBQUUsTUFBTSw2QkFBNkIsQ0FBQztBQUlsRSxPQUFPLEVBQUUsTUFBTSxFQUFFLE1BQU0sa0NBQWtDLENBQUM7QUFDMUQsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLGdDQUFnQyxDQUFDO0FBRXpELE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxrQkFBa0IsQ0FBQztBQUMxQyxPQUFPLEVBQUUsaUJBQWlCLEVBQUUsTUFBTSwyQ0FBMkMsQ0FBQztBQUM5RSxPQUFPLEVBQUUsV0FBVyxFQUFFLE1BQU0scUNBQXFDLENBQUM7QUFHbEUsT0FBTyxFQUFFLGtCQUFrQixFQUFFLE1BQU0sOEJBQThCLENBQUM7QUFHbEUsT0FBTyxFQUFFLG9CQUFvQixFQUFFLE1BQU0sbUNBQW1DLENBQUM7QUFDekUsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLGlDQUFpQyxDQUFDO0FBRTFELE9BQU8sRUFBRSxXQUFXLEVBQUUsTUFBTSw2QkFBNkIsQ0FBQztBQUMxRCxPQUFPLEVBQTJDLHFCQUFxQixFQUF5QixNQUFNLG1DQUFtQyxDQUFDO0FBRzFJO0lBS2tDLHdDQUFTO0lBMkh6QyxzQkFBb0IsSUFBZ0IsRUFBVSxhQUE0QixFQUFVLFNBQW9CO1FBQXhHLFlBQ0Usa0JBQU0sSUFBSSxFQUFFLGFBQWEsQ0FBQyxTQU0zQjtRQVBtQixVQUFJLEdBQUosSUFBSSxDQUFZO1FBQVUsbUJBQWEsR0FBYixhQUFhLENBQWU7UUFBVSxlQUFTLEdBQVQsU0FBUyxDQUFXO1FBeEdoRyw4QkFBd0IsR0FBRyxFQUFFLENBQUM7UUFDOUIsOEJBQXdCLEdBQVksSUFBSSxPQUFPLENBQUM7UUFFaEQsZ0NBQTBCLEdBQUcsRUFBRSxDQUFDO1FBU2pDLHNCQUFnQixHQUFHLEtBQUssQ0FBQztRQUt4Qix3QkFBa0IsR0FBRyxFQUFFLENBQUM7UUFFeEIsd0JBQWtCLEdBQUcsRUFBRSxDQUFDO1FBQ3hCLGtCQUFZLEdBQUcsRUFBRSxDQUFDO1FBRW5CLHVCQUFpQixHQUFZLEtBQUssQ0FBQztRQUNuQyxnQkFBVSxHQUFZLEtBQUssQ0FBQztRQUkzQix1QkFBaUIsR0FBRyxFQUFFLENBQUM7UUFVdkIsc0JBQWdCLEdBQVcsR0FBRyxDQUFDO1FBQy9CLCtCQUF5QixHQUFHLEVBQUUsQ0FBQztRQUMvQixnQ0FBMEIsR0FBRyxFQUFFLENBQUM7UUFDakMsNkJBQXVCLEdBQVcsSUFBSSxDQUFDO1FBSXRDLGVBQVMsR0FBRyxFQUFFLENBQUM7UUFDdkIsY0FBUSxHQUFHLEVBQUUsQ0FBQztRQTRrQlAsbUJBQWEsR0FBWSxJQUFJLE9BQU8sQ0FBQztRQUNyQyxpQkFBVyxHQUFZLElBQUksT0FBTyxDQUFDO1FBQ25DLGdCQUFVLEdBQVksSUFBSSxPQUFPLENBQUM7UUFDbEMseUJBQW1CLEdBQUcsRUFBRSxDQUFDO1FBQ3pCLG9CQUFjLEdBQUcsRUFBRSxDQUFDO1FBQ3BCLDBCQUFvQixHQUFHLEVBQUUsQ0FBQztRQWs3QnpCLGFBQU8sR0FBRyxJQUFJLENBQUM7UUFDdkIsaUJBQVc7Ozs7UUFBRyxVQUFDLEtBQUs7WUFDbEIsSUFBSSxDQUFDLEtBQUksQ0FBQyxPQUFPO2dCQUNmLE9BQU87WUFDVCxLQUFJLENBQUMsa0JBQWtCLENBQUMsYUFBYSxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQztZQUNyRixVQUFVOzs7WUFBQztnQkFDVCxLQUFJLENBQUMsa0JBQWtCLENBQUMsYUFBYSxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQztZQUV2RixDQUFDLEdBQUUsS0FBSyxDQUFDLENBQUM7WUFDVixLQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztZQUNyQixVQUFVOzs7WUFBQztnQkFDVCxLQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztZQUN0QixDQUFDLEdBQUUsS0FBSyxDQUFDLENBQUM7UUFFWixDQUFDLEVBQUE7UUFFRCxjQUFROzs7OztRQUFHLFVBQUMsRUFBRSxFQUFFLEtBQUs7O2dCQUNmLEtBQUs7WUFDVDs7OztZQUFPO2dCQUFDLGNBQU87cUJBQVAsVUFBTyxFQUFQLHFCQUFPLEVBQVAsSUFBTztvQkFBUCx5QkFBTzs7Z0JBQ2IsSUFBSSxLQUFLO29CQUFFLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQTtnQkFDOUIsS0FBSyxHQUFHLFVBQVU7OztnQkFBQztvQkFDakIsRUFBRSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUE7Z0JBQ3RCLENBQUMsR0FBRSxLQUFLLENBQUMsQ0FBQTtZQUNYLENBQUMsRUFBQTtRQUNILENBQUMsRUFBQTtRQUVPLGlCQUFXLEdBQUc7WUFDcEIsSUFBSSxFQUFFLENBQUM7WUFDUCxHQUFHLEVBQUUsQ0FBQztZQUNOLEtBQUssRUFBRSxDQUFDO1lBQ1IsTUFBTSxFQUFFLENBQUM7U0FFVixDQUFDOztRQXQrQ0EsaUNBQWlDO1FBQ2pDLCtFQUErRTtRQUMvRSxXQUFXO1FBQ1gsNkVBQTZFO1FBQzdFLElBQUk7SUFDTixDQUFDO0lBaEVELHNCQUFXLHNDQUFZOzs7O1FBQXZCO1lBQ0UsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1FBQzVCLENBQUM7Ozs7O1FBQ0QsVUFBd0IsS0FBZTtZQUNyQyxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQztRQUM3QixDQUFDOzs7T0FIQTtJQUtELHNCQUFXLDhDQUFvQjs7OztRQUEvQjtZQUNFLE9BQU8sSUFBSSxDQUFDLHFCQUFxQixDQUFDO1FBQ3BDLENBQUM7Ozs7O1FBQ0QsVUFBZ0MsS0FBVztZQUN6QyxJQUFJLENBQUMscUJBQXFCLEdBQUcsS0FBSyxDQUFDO1FBQ3JDLENBQUM7OztPQUhBO0lBSUQsc0JBQVcsMENBQWdCOzs7O1FBQTNCO1lBQ0UsT0FBTyxJQUFJLENBQUMsaUJBQWlCLENBQUM7UUFDaEMsQ0FBQzs7Ozs7UUFDRCxVQUE0QixLQUF5QjtZQUNuRCxJQUFJLENBQUMsaUJBQWlCLEdBQUcsS0FBSyxDQUFDO1FBQ2pDLENBQUM7OztPQUhBO0lBT0Qsc0JBQVcsa0NBQVE7Ozs7UUFBbkI7WUFDRSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7UUFDeEIsQ0FBQzs7Ozs7UUFDRCxVQUFvQixLQUFLO1lBQ3ZCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO1FBQ3pCLENBQUM7OztPQUhBO0lBS0Qsc0JBQVcsb0NBQVU7Ozs7UUFBckI7WUFDRSxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7UUFDMUIsQ0FBQzs7Ozs7UUFDRCxVQUFzQixLQUFlO1lBQ25DLElBQUksQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDO1FBQzNCLENBQUM7OztPQUhBO0lBS0Qsc0JBQVcsNkNBQW1COzs7O1FBQTlCO1lBQ0UsT0FBTyxJQUFJLENBQUMsb0JBQW9CLENBQUM7UUFDbkMsQ0FBQzs7Ozs7UUFDRCxVQUErQixLQUFxQjtZQUNsRCxJQUFJLENBQUMsb0JBQW9CLEdBQUcsS0FBSyxDQUFDO1FBQ3BDLENBQUM7OztPQUhBO0lBSUQsc0JBQVcsd0NBQWM7Ozs7UUFBekI7WUFDRSxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7UUFDOUIsQ0FBQzs7Ozs7UUFDRCxVQUEwQixLQUFxQjtZQUM3QyxJQUFJLENBQUMsZUFBZSxHQUFHLEtBQUssQ0FBQztRQUMvQixDQUFDOzs7T0FIQTtJQUlELHNCQUFXLHNDQUFZOzs7O1FBQXZCO1lBQ0UsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1FBQzVCLENBQUM7Ozs7O1FBQ0QsVUFBd0IsS0FBbUI7WUFDekMsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUM7UUFDN0IsQ0FBQzs7O09BSEE7Ozs7SUFnQk0scUNBQWM7OztJQUFyQjtJQUNBLENBQUM7SUFFRDs7O2VBR0s7Ozs7Ozs7SUFDRSxzQ0FBZTs7Ozs7O0lBQXRCO1FBRUUsNkJBQTZCO1FBQzdCLCtFQUErRTtRQUMvRSwrQ0FBK0M7UUFDL0MsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztnQkFDdkQsS0FBSyxHQUFXLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ2xELElBQUksS0FBSyxDQUFDLFdBQVcsRUFBRTtnQkFDckIsS0FBSyxDQUFDLFdBQVcsQ0FBQyxlQUFlLEVBQUUsQ0FBQzthQUNyQztTQUNGO1FBQ0Qsd0JBQXdCO1FBQ3hCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxPQUFPLENBQUM7UUFFOUIsSUFBSSxDQUFDLFlBQVksR0FBRyxFQUFFLENBQUM7UUFDdkIsSUFBSSxDQUFDLGtCQUFrQixHQUFHLEVBQUUsQ0FBQztRQUU3QixtQ0FBbUM7UUFDbkMsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDbkIsb0NBQW9DO1FBQ3BDLElBQUksSUFBSSxDQUFDLG9CQUFvQjtZQUMzQixJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztRQUMxQixJQUFJLElBQUksQ0FBQyxlQUFlO1lBQ3RCLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1FBQzFCLDBCQUEwQjtRQUMxQix3QkFBd0I7UUFDeEIsSUFBSSxDQUFDLGtCQUFrQixDQUFDLGNBQWMsRUFBRSxFQUFFLENBQUMsQ0FBQTtJQUM3QyxDQUFDOzs7O0lBR00sd0NBQWlCOzs7SUFBeEI7UUFDRSxJQUFJLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQztRQUN2QixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7UUFDdkIsSUFBSSxDQUFDLHdCQUF3QixHQUFHLEVBQUUsQ0FBQztRQUNuQyxJQUFJLENBQUMsd0JBQXdCLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDdEMsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLE9BQU8sQ0FBQztJQUNoQyxDQUFDO0lBR0Q7O2VBRUs7Ozs7Ozs7O0lBQ0UsK0JBQVE7Ozs7Ozs7SUFBZixVQUFnQixNQUFjLEVBQUUsT0FBdUI7UUFBdkIsd0JBQUEsRUFBQSxjQUF1QjtRQUNyRCxJQUFJLE1BQU0sQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3BDLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsT0FBTyxDQUFDLENBQUM7YUFDdkM7WUFDSCxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxDQUFDLENBQUM7U0FDckM7SUFDSCxDQUFDO0lBRUQ7O2VBRUs7Ozs7Ozs7SUFDRSxzQ0FBZTs7Ozs7O0lBQXRCLFVBQXVCLE1BQWM7O1lBQy9CLEdBQUcsR0FBVyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztRQUN2QyxJQUFJLENBQUMsR0FBRyxFQUFFO1lBQ1Isc0ZBQXNGO1lBQ3RGLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7O1lBQ0csVUFBVSxHQUFRLEdBQUcsQ0FBQyxZQUFZLENBQUM7O1lBQ25DLFVBQVUsR0FBVyxHQUFHLENBQUMsaUJBQWlCLENBQUM7O1lBQzNDLGVBQXVCO1FBQzNCLHFEQUFxRDtRQUNyRCxLQUFLLElBQUksS0FBSyxJQUFJLFVBQVUsRUFBRTtZQUM1QixlQUFlLEdBQUcsRUFBRSxHQUFHLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN6QyxxQ0FBcUM7WUFDckMsSUFBSSxlQUFlLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsRUFBRTtnQkFDcEMsZUFBZSxHQUFHLEdBQUcsR0FBRyxlQUFlLENBQUM7YUFDekM7WUFDRCxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsZUFBZSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1NBQy9EO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBR0Q7O1NBRUs7Ozs7Ozs7Ozs7O0lBQ0csbUNBQVk7Ozs7Ozs7Ozs7SUFBcEIsVUFBcUIsTUFBYyxFQUFFLE1BQWMsRUFBRSxLQUFVLEVBQUUsS0FBYTtRQUM1RSxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sSUFBSSxDQUFDLElBQUksTUFBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsSUFBSSxLQUFLLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDOUYsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLE1BQU0sQ0FBQyxJQUFJLElBQUksRUFBRTtnQkFDM0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLEdBQUcsR0FBRyxNQUFNLENBQUMsQ0FBQzthQUMvQztZQUNELE9BQU87U0FDUjthQUNJO1lBRUgsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQzs7Z0JBQ2xCLFVBQVUsR0FBVyxJQUFJLE1BQU07WUFDbkMsVUFBVSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7WUFDOUIsVUFBVSxDQUFDLFdBQVcsR0FBRyxDQUFDLE1BQU0sR0FBRyxHQUFHLEdBQUcsTUFBTSxDQUFDLENBQUM7WUFFakQsb0VBQW9FO1lBQ3BFLElBQUksSUFBSSxDQUFDLGlCQUFpQjtnQkFDeEIsVUFBVSxDQUFDLE1BQU0sR0FBRyxXQUFXLENBQUM7O2dCQUVoQyxVQUFVLENBQUMsTUFBTSxHQUFHLFVBQVUsQ0FBQztZQUNqQyxVQUFVLENBQUMsTUFBTSxHQUFHLENBQUMsTUFBTSxHQUFHLEdBQUcsR0FBRyxNQUFNLENBQUMsQ0FBQztZQUU1Qyx1QkFBdUI7WUFDdkIsaURBQWlEO1lBQ2pELHlDQUF5QztZQUN6QywyR0FBMkc7WUFDM0csbUVBQW1FO1lBQ25FLHlHQUF5RztZQUV6Ryx5REFBeUQ7WUFDekQsK0dBQStHO1lBRS9HLDhFQUE4RTtZQUM5RSw4R0FBOEc7WUFFOUcsdURBQXVEO1lBQ3ZELHFEQUFxRDtZQUNyRCwwRUFBMEU7WUFDMUUsZ0VBQWdFO1lBQ2hFLHFGQUFxRjtZQUVyRixJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1lBQ25ELDRDQUE0QztTQUM3QztJQUNILENBQUM7SUFFRDs7U0FFSzs7Ozs7OztJQUNMLG1DQUFZOzs7Ozs7SUFBWixVQUFhLE1BQWM7UUFDekIsSUFBSSxNQUFNLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO1lBQ3RDLEtBQUssSUFBSSxJQUFJLElBQUksWUFBWSxDQUFDLGNBQWMsRUFBRTtnQkFDNUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsQ0FBQzthQUM3QztTQUNGO2FBQ0k7OztnQkFFRyxNQUFNLEdBQVcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDO1lBQ3ZELElBQUksTUFBTSxFQUFFOztvQkFDSixVQUFVLEdBQUcsTUFBTSxDQUFDLFVBQVU7Z0JBQ3BDLElBQUksTUFBTSxDQUFDLFdBQVcsRUFBRTtvQkFDdEIsTUFBTSxDQUFDLFdBQVcsQ0FBQyxlQUFlLEVBQUUsQ0FBQztpQkFDdEM7Z0JBQ0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQy9CLG9CQUFvQjtnQkFDcEIsSUFBSSxVQUFVLElBQUksTUFBTTtvQkFDdEIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7O29CQUV4QixJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQzthQUMzQjtTQUVGO0lBQ0gsQ0FBQztJQUVEOztTQUVLOzs7Ozs7OztJQUNFLG1DQUFZOzs7Ozs7O0lBQW5CLFVBQW9CLE1BQWMsRUFBRSxPQUF1QjtRQUF2Qix3QkFBQSxFQUFBLGNBQXVCO1FBQ3pELElBQUk7O2dCQUdFLEdBQUcsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQzs7Z0JBQ3ZCLE9BQU8sR0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDOztnQkFDeEIsUUFBUSxHQUFXLElBQUksQ0FBQyxnQkFBZ0I7O2dCQUN4QyxVQUFVLEdBQVcsSUFBSSxDQUFDLGtCQUFrQjs7Z0JBQzVDLE9BQU8sR0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDOztnQkFDeEIsVUFBVSxHQUFXLEdBQUcsQ0FBQyxDQUFDLENBQUM7O2dCQUMzQixXQUFXLEdBQVcsR0FBRyxDQUFDLENBQUMsQ0FBQzs7Z0JBQzVCLEdBQUcsR0FBVyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxDQUFDO1lBRTNELElBQUksQ0FBQyxHQUFHLEVBQUU7Z0JBQ1IsMEVBQTBFO2dCQUMxRSxxRkFBcUY7Z0JBQ3JGLE9BQU8sS0FBSyxDQUFDO2FBQ2Q7O2dCQUlHLFFBQVEsR0FBUSxHQUFHLENBQUMsUUFBUSxDQUFDO1lBQ2pDLElBQUksVUFBVSxJQUFJLFFBQVEsQ0FBQyxVQUFVLElBQUksUUFBUSxJQUFJLFFBQVEsQ0FBQyxRQUFRLEVBQUU7Z0JBQ3RFLE9BQU8sS0FBSyxDQUFDO2FBQ2Q7O2dCQUNHLE9BQU8sR0FBVyxFQUFFO1lBRXhCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxRQUFRLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTs7O29CQUUxQyxLQUFLLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQzdCLElBQUksS0FBSyxDQUFDLFdBQVcsSUFBSSxXQUFXLEVBQUU7Ozt3QkFFaEMsU0FBUyxHQUFXLEtBQUssQ0FBQyxJQUFJO29CQUNsQyxPQUFPLEdBQUcsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLENBQUMsR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDOzs7Ozt3QkFLN0QsTUFBTSxHQUFXLElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztvQkFDckQsSUFBSSxNQUFNLEVBQUU7d0JBQ1Ysa0NBQWtDO3dCQUNsQyxJQUFJLE1BQU0sQ0FBQyxVQUFVLElBQUksTUFBTSxFQUFFOzs7Z0NBRTNCLFdBQVcsR0FBVyxNQUFNLENBQUMsWUFBWTs0QkFDN0MsSUFBSSxLQUFLLENBQUMsV0FBVyxJQUFJLFdBQVcsRUFBRTtnQ0FDcEMsTUFBTSxDQUFDLFlBQVksR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDOzZCQUN6Qzt5QkFDRjs2QkFDSTs7Z0NBQ0MsV0FBVyxHQUFXLE1BQU0sQ0FBQyxZQUFZOzRCQUM3QyxJQUFJLEtBQUssQ0FBQyxXQUFXLElBQUksV0FBVyxFQUFFO2dDQUNwQyxNQUFNLENBQUMsWUFBWSxHQUFHLEtBQUssQ0FBQyxXQUFXLENBQUM7NkJBQ3pDO3lCQUNGO3FCQUNGO3lCQUNJO3dCQUNILElBQUksU0FBUyxJQUFJLE1BQU0sRUFBRTs7Z0NBQ25CLFVBQVUsR0FBVyxJQUFJLE1BQU07NEJBQ25DLFVBQVUsQ0FBQyxZQUFZLEdBQUcsS0FBSyxDQUFDLFdBQVcsQ0FBQzs0QkFDNUMsNkdBQTZHOzRCQUM3RyxVQUFVLENBQUMsV0FBVyxHQUFHLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsTUFBTSxFQUFFLEtBQUssQ0FBQyxXQUFXLEVBQUUsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDOzRCQUMzRyxJQUFJLElBQUksQ0FBQyxpQkFBaUI7Z0NBQ3hCLFVBQVUsQ0FBQyxNQUFNLEdBQUcsV0FBVyxDQUFDOztnQ0FFaEMsVUFBVSxDQUFDLE1BQU0sR0FBRyxVQUFVLENBQUM7NEJBQ2pDLHdKQUF3Sjs0QkFDeEosMklBQTJJOzRCQUMzSSxVQUFVLENBQUMsYUFBYSxHQUFHLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsTUFBTSxFQUFFLEtBQUssQ0FBQyxXQUFXLEVBQUUsS0FBSyxDQUFDLFdBQVcsQ0FBQyxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsYUFBYSxDQUFDOzRCQUV6SSxVQUFVLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQzs0QkFDNUIsVUFBVSxDQUFDLGlCQUFpQixHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUM7NEJBQzdDLFVBQVUsQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDOzRCQUM3QixVQUFVLENBQUMsVUFBVSxHQUFHLE1BQU0sQ0FBQzs0QkFDL0IsSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLFVBQVUsQ0FBQyxDQUFBOzRCQUN4QyxxQkFBcUI7NEJBQ3JCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQzs0QkFDNUIsU0FBUzt5QkFFVjs2QkFDSTs7O2dDQUVDLFVBQVUsR0FBVyxJQUFJLE1BQU07NEJBQ25DLDJFQUEyRTs0QkFDM0UsVUFBVSxDQUFDLFlBQVksR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDOzRCQUM1Qyw2R0FBNkc7NEJBQzdHLFVBQVUsQ0FBQyxXQUFXLEdBQUcsaUJBQWlCLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxNQUFNLEVBQUUsS0FBSyxDQUFDLFdBQVcsRUFBRSxLQUFLLENBQUMsV0FBVyxDQUFDLENBQUM7NEJBQzNHLElBQUksSUFBSSxDQUFDLGlCQUFpQjtnQ0FDeEIsVUFBVSxDQUFDLE1BQU0sR0FBRyxXQUFXLENBQUM7O2dDQUVoQyxVQUFVLENBQUMsTUFBTSxHQUFHLFVBQVUsQ0FBQzs0QkFDakMsVUFBVSxDQUFDLE1BQU0sR0FBRyxPQUFPLENBQUM7NEJBQzVCLHFCQUFxQjs0QkFDckIsdUNBQXVDOzRCQUV2Qyx3SkFBd0o7NEJBQ3hKLDJJQUEySTs0QkFDM0ksVUFBVSxDQUFDLGFBQWEsR0FBRyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLE1BQU0sRUFBRSxLQUFLLENBQUMsV0FBVyxFQUFFLEtBQUssQ0FBQyxXQUFXLENBQUMsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLGFBQWEsQ0FBQzs0QkFFekksNkNBQTZDOzRCQUM3QyxVQUFVLENBQUMsaUJBQWlCLEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQzs7OztnQ0FLekMsV0FBVyxHQUFHLG1CQUFvQixJQUFJLENBQUMsb0JBQW9CLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLEVBQUE7NEJBQzVGLGlFQUFpRTs0QkFDakUsdUJBQXVCOzRCQUN2QixXQUFXLENBQUMsUUFBUSxHQUFHLFVBQVUsQ0FBQyxNQUFNLENBQUM7NEJBQ3pDLDZDQUE2Qzs0QkFDN0MsSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLGNBQWMsSUFBSSxjQUFjLEVBQUU7Z0NBQ3hELFdBQVcsQ0FBQyxXQUFXLEdBQUcsTUFBTSxDQUFDOzZCQUNsQztpQ0FBTTtnQ0FDTCxXQUFXLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQzs2QkFDbEM7NEJBQ0QsV0FBVyxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDOzRCQUM1QyxtQ0FBbUM7NEJBQ25DLHVDQUF1Qzs0QkFDdkMsa0RBQWtEOzRCQUNsRCxXQUFXLENBQUMsY0FBYyxHQUFHLEtBQUssQ0FBQzs0QkFDbkMsNERBQTREOzRCQUM1RCxVQUFVLENBQUMsV0FBVyxHQUFHLFdBQVcsQ0FBQzs0QkFDckMsSUFBSSxDQUFDLE9BQU8sRUFBRTtnQ0FDWixVQUFVLENBQUMsV0FBVyxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0NBQ3ZDLFVBQVUsQ0FBQyxXQUFXLENBQUMsZUFBZSxHQUFHLEtBQUssQ0FBQzs2QkFDaEQ7NEJBQ0QsVUFBVSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUM7NEJBQzdCLFVBQVUsQ0FBQyxVQUFVLEdBQUcsTUFBTSxDQUFDOzRCQUMvQiw4RUFBOEU7NEJBQzlFLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQTs0QkFFeEMsSUFBSTs0QkFDSixJQUFJLENBQUMsT0FBTyxFQUFFO2dDQUNaLFVBQVUsQ0FBQyxXQUFXLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQzs2QkFDeEM7NEJBQ0QsbUVBQW1FOzRCQUNuRSxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7eUJBQzdCO3FCQUVGO29CQUNELE1BQU07aUJBQ1A7YUFDRjtZQUNELE9BQU8sSUFBSSxDQUFDO1NBQ2I7UUFBQyxPQUFPLENBQUMsRUFBRTtTQUdYO0lBQ0gsQ0FBQztJQUVEOzs7O01BSUU7Ozs7Ozs7OztJQUNLLHdDQUFpQjs7Ozs7Ozs7SUFBeEIsVUFBeUIsT0FBZTtRQUN0QyxLQUFLLElBQU0sR0FBRyxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUU7WUFDL0IsSUFBSSxHQUFHLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO2dCQUM5QixPQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDMUI7U0FDRjtJQUNILENBQUM7SUFHRDs7U0FFSzs7Ozs7OztJQUNFLHVDQUFnQjs7Ozs7O0lBQXZCLFVBQXdCLE1BQXVCO1FBQS9DLGlCQTZCQztRQTdCdUIsdUJBQUEsRUFBQSxjQUF1QjtRQUM3QyxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFOztnQkFDdEIsT0FBTyxHQUFZLElBQUk7O2dCQUN2QixHQUFHLEdBQUcsRUFBRTtZQUNaLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLFNBQVMsRUFBRSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTs7b0JBQ3ZELEtBQUssR0FBVyxJQUFJLENBQUMsVUFBVSxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsQ0FBQztnQkFDbEQsSUFBSSxLQUFLLElBQUksS0FBSyxDQUFDLFVBQVUsS0FBSyxNQUFNLElBQUksS0FBSyxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsNkJBQTZCLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTtvQkFDckcsT0FBTyxHQUFHLEtBQUssQ0FBQztvQkFDaEIsR0FBRyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDakI7YUFDRjtZQUNELElBQUksQ0FBQyxlQUFlLENBQUMsWUFBWSxHQUFHLEdBQUcsQ0FBQztZQUd4QyxJQUFJLE9BQU8sRUFBRTtnQkFDWCxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7Z0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsZUFBZSxHQUFHLEtBQUssQ0FBQzthQUMzQztpQkFDSTtnQkFDSCxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7Z0JBQ2pDLElBQUksQ0FBQyxZQUFZLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQzthQUMxQztZQUVELG1FQUFtRTtZQUNuRSxJQUFJLE1BQU07Z0JBQ1IsVUFBVTs7O2dCQUFDO29CQUNULEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDL0IsQ0FBQyxHQUFFLENBQUMsQ0FBQyxDQUFDO1NBQ1Q7SUFDSCxDQUFDO0lBRUQ7O2VBRUs7SUFDTCx3QkFBd0I7Ozs7Ozs7OztJQUNqQixnREFBeUI7Ozs7Ozs7O0lBQWhDLFVBQWlDLE9BQWUsRUFBRSxTQUFpQjs7WUFDN0QsR0FBRyxHQUFXLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM5RCxJQUFJLEdBQUcsRUFBRTtZQUNQLEdBQUcsQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLFNBQVMsQ0FBQzs7Z0JBQy9CLEtBQUssR0FBRyxtQkFBbUIsQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDO1lBQ3hELEtBQUssSUFBSSxJQUFJLElBQUksWUFBWSxDQUFDLGNBQWMsRUFBRTs7b0JBRXhDLE1BQU0sR0FBVyxPQUFPLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUM7O29CQUM1RCxlQUFlLEdBQVcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDO2dCQUM5RCxJQUFJLGVBQWUsRUFBRTtvQkFFbkIseUNBQXlDO29CQUN6QywyQkFBMkI7b0JBQzNCLDJCQUEyQjtvQkFDM0IseUJBQXlCO29CQUN6Qix5QkFBeUI7b0JBQ3pCLDhCQUE4QjtvQkFDOUIsMkJBQTJCO29CQUMzQix3REFBd0Q7b0JBQ3hELGlDQUFpQztvQkFDakMsaUNBQWlDO29CQUNqQyxJQUFJO29CQUNKLDRGQUE0RjtvQkFDNUYsd0VBQXdFO29CQUN4RSxrQ0FBa0M7b0JBQ2xDLHlHQUF5RztvQkFDekcsMENBQTBDO2lCQUMzQzthQUNGO1NBQ0Y7SUFDSCxDQUFDO0lBRUQ7O01BRUU7Ozs7Ozs7SUFDSyx1Q0FBZ0I7Ozs7OztJQUF2QixVQUF3QixNQUF1QjtRQUEvQyxpQkFpQ0M7UUFqQ3VCLHVCQUFBLEVBQUEsY0FBdUI7UUFDN0MsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTs7Z0JBRXRCLE9BQU8sR0FBWSxJQUFJOztnQkFDdkIsR0FBRyxHQUFHLEVBQUU7WUFDWixzREFBc0Q7WUFDdEQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztvQkFDdkQsS0FBSyxHQUFXLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxDQUFDO2dCQUNsRCxJQUFJLEtBQUssQ0FBQyxVQUFVLElBQUksTUFBTSxFQUFFO29CQUM5QiwwRUFBMEU7b0JBQzFFLElBQUksV0FBVyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLEVBQUU7d0JBQ3ZDLE9BQU8sR0FBRyxLQUFLLENBQUM7d0JBQ2hCLEdBQUcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7cUJBQ2pCO2lCQUNGO2FBQ0Y7WUFDRCxJQUFJLENBQUMsb0JBQW9CLENBQUMsWUFBWSxHQUFHLEdBQUcsQ0FBQztZQUU3QyxtRUFBbUU7WUFDbkUsSUFBSSxNQUFNO2dCQUNSLFVBQVU7OztnQkFBQztvQkFDVCxLQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQy9CLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztZQUVSLElBQUksT0FBTyxFQUFFO2dCQUNYLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztnQkFDaEMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxlQUFlLEdBQUcsS0FBSyxDQUFDO2FBQ3pDO2lCQUNJO2dCQUNILElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztnQkFDL0IsSUFBSSxDQUFDLFVBQVUsQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDO2FBQ3hDO1NBQ0Y7SUFDSCxDQUFDO0lBRUQ7O2VBRUs7Ozs7Ozs7SUFDRSxpREFBMEI7Ozs7OztJQUFqQyxVQUFrQyxNQUF1QjtRQUF6RCxpQkFxQ0M7UUFyQ2lDLHVCQUFBLEVBQUEsY0FBdUI7O1lBRW5ELEdBQUcsR0FBRyxFQUFFO1FBQ1osSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLG1CQUFtQixFQUFFO1lBQzNDLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztZQUNoQyxJQUFJLENBQUMsVUFBVSxDQUFDLGVBQWUsR0FBRyxLQUFLLENBQUM7U0FDekM7YUFBTTtZQUNMLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztZQUMvQixJQUFJLENBQUMsVUFBVSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUM7U0FDeEM7UUFDRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7O2dCQUN2RCxLQUFLLEdBQVcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDbEQsSUFBSSxLQUFLLENBQUMsVUFBVSxJQUFJLE1BQU0sRUFBRTtnQkFDOUIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxFQUFFO29CQUN4QyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUVqQjthQUNGO1NBRUY7UUFDRCxtQkFBbUI7UUFFbkIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxZQUFZLEdBQUcsR0FBRyxDQUFDO1FBRXRDLG1FQUFtRTtRQUNuRSxJQUFJLE1BQU07WUFDUixVQUFVOzs7WUFBQztnQkFDVCxLQUFJLENBQUMsMEJBQTBCLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDekMsQ0FBQyxHQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ1I7Ozs7Ozs7V0FPRztJQUNMLENBQUM7SUFJRDs7O1NBR0s7Ozs7Ozs7O0lBQ0Usc0NBQWU7Ozs7Ozs7SUFBdEIsVUFBdUIsU0FBUzs7WUFDMUIsWUFBWSxHQUFHLEVBQUU7O1lBQ2pCLFdBQVcsR0FBRyxFQUFFO1FBQ3BCLHVEQUF1RDtRQUN2RCw0Q0FBNEM7UUFDNUMsUUFBUTtRQUNSLHVDQUF1QztRQUV2QyxZQUFZLEdBQUcsT0FBTyxDQUFDLG9CQUFvQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUUvRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsWUFBWSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTs7O2dCQUd4QyxvQkFBb0IsR0FBVyxFQUFFOztnQkFDakMsT0FBTyxHQUFHLFlBQVksQ0FBQyxDQUFDLENBQUM7O2dCQUN6QixPQUFPLEdBQVcsT0FBTyxDQUFDLE9BQU87WUFFckMsSUFBSSxPQUFPLENBQUMsTUFBTSxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxNQUFNO2dCQUMxQyxXQUFXLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQzs7Z0JBRWpDLFdBQVcsR0FBRyxPQUFPLENBQUMsTUFBTSxDQUFDO1lBRS9CLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxXQUFXLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztvQkFDdkMsTUFBTSxHQUFHLFdBQVcsQ0FBQyxDQUFDLENBQUM7O29CQUV2QixVQUFVLEdBQVcsTUFBTSxDQUFDLFVBQVU7O29CQUN0QyxHQUFHLEdBQVcsSUFBSSxNQUFNLEVBQUU7Z0JBQzlCLElBQUksTUFBTSxDQUFDLFVBQVUsSUFBSSxVQUFVO29CQUNqQyxvQkFBb0IsR0FBRyxJQUFJLENBQUMsdUJBQXVCLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQzlELEdBQUcsQ0FBQyxRQUFRLENBQUMsR0FBRyxNQUFNLENBQUM7Z0JBQ3ZCLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLENBQUMsR0FBRyxHQUFHLENBQUM7YUFDakQ7O2dCQUNHLFVBQVUsR0FBVyxJQUFJLE1BQU0sRUFBRTtZQUNyQyxVQUFVLENBQUMsWUFBWSxDQUFDLEdBQUcsT0FBTyxDQUFDLFVBQVUsQ0FBQztZQUM5QyxVQUFVLENBQUMsaUJBQWlCLENBQUMsR0FBRyxvQkFBb0IsQ0FBQztZQUVyRCxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxhQUFhLENBQUMsR0FBRyxVQUFVLENBQUM7U0FDckQ7SUFDSCxDQUFDO0lBUUQ7O2VBRUs7Ozs7Ozs7Ozs7O0lBQ0UseUNBQWtCOzs7Ozs7Ozs7O0lBQXpCLFVBQTBCLFdBQVcsRUFBRSxXQUFvQixFQUFFLG1CQUEwQixFQUFFLHdCQUErQixFQUFFLDZCQUFvQztRQUFqRyxvQ0FBQSxFQUFBLDBCQUEwQjtRQUFFLHlDQUFBLEVBQUEsK0JBQStCO1FBQUUsOENBQUEsRUFBQSxvQ0FBb0M7O1lBQ3hKLEtBQUssR0FBRyxFQUFFOztZQUNWLE9BQU8sR0FBRyxFQUFFO1FBRWhCLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxPQUFPLENBQUM7UUFDakMsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLE9BQU8sQ0FBQztRQUMvQixJQUFJLENBQUMsbUJBQW1CLEdBQUcsRUFBRSxDQUFDOztZQUMxQixZQUFZLEdBQUcsRUFBRTtRQUdyQixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUM3RCxJQUFJLFdBQVcsRUFBRTtnQkFDZiw0REFBNEQ7Z0JBQzVELG9FQUFvRTtnQkFDcEUsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUM7Z0JBQzFCLE9BQU8sR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUM7YUFDbkM7aUJBQ0k7Z0JBQ0gsS0FBSyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQztnQkFDaEMsT0FBTyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUM7YUFDN0I7U0FDRjtRQUVELElBQUksbUJBQW1CLElBQUksSUFBSTtZQUM3QixtQkFBbUIsR0FBRyxFQUFFLENBQUM7UUFDM0IsSUFBSSx3QkFBd0IsSUFBSSxJQUFJO1lBQ2xDLHdCQUF3QixHQUFHLEVBQUUsQ0FBQztRQUNoQyxJQUFJLDZCQUE2QixJQUFJLElBQUk7WUFDdkMsNkJBQTZCLEdBQUcsRUFBRSxDQUFDOztZQUVqQyxVQUFrQjs7WUFDbEIsYUFBcUI7O1lBQ3JCLFNBQWlCOztZQUNqQixrQkFBMEI7O1lBQzFCLFdBQW1COztZQUNuQixjQUFzQjs7WUFDdEIsVUFBa0I7O1lBQ2xCLHNCQUE4Qjs7WUFDOUIsNEJBQW9DOztZQUVwQyxrQkFBa0IsR0FBRyxPQUFPLENBQUMsb0JBQW9CLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQztRQUUxRSxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsa0JBQWtCLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztnQkFDOUMsT0FBTyxHQUFHLGtCQUFrQixDQUFDLENBQUMsQ0FBQzs7Z0JBQy9CLE9BQU8sR0FBVyxPQUFPLENBQUMsT0FBTzs7Z0JBQ2pDLFVBQVUsR0FBVyxPQUFPLENBQUMsVUFBVTs7Z0JBQ3ZDLElBQUksR0FBRyxPQUFPLENBQUMsSUFBSTtZQUN2QixJQUFJLElBQUksRUFBRTtnQkFDUixJQUFJLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztnQkFDdEIsSUFBSSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsbUJBQW1CLEVBQUUsd0JBQXdCLENBQUMsQ0FBQztnQkFDckgsSUFBSSxZQUFZLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO29CQUN2QyxZQUFZLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2lCQUM1QjthQUNGO1NBQ0Y7UUFLRCx5REFBeUQ7UUFDekQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFlBQVksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7O2dCQUN4QyxnQkFBZ0IsR0FBRyxZQUFZLENBQUMsQ0FBQyxDQUFDOztnQkFFbEMsR0FBRyxHQUFXLElBQUksQ0FBQyxRQUFRLENBQUMsZ0JBQWdCLEdBQUcsYUFBYSxDQUFDO1lBQ2pFLElBQUksR0FBRyxFQUFFOztvQkFDSCxVQUFVLEdBQVEsR0FBRyxDQUFDLFlBQVksQ0FBQzs7b0JBQ25DLFVBQVUsR0FBVyxHQUFHLENBQUMsaUJBQWlCLENBQUM7O29CQUMzQyxlQUF1Qjs7b0JBQ3ZCLGlCQUF5Qjs7b0JBQ3pCLFNBQWtCOztvQkFDbEIsaUJBQXlCO2dCQUM3QixxREFBcUQ7Z0JBQ3JELEtBQUssSUFBSSxLQUFLLElBQUksVUFBVSxFQUFFO29CQUM1QixlQUFlLEdBQUcsRUFBRSxHQUFHLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztvQkFDekMsSUFBSSxDQUFDLEVBQUUsR0FBRyxlQUFlLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksR0FBRyxFQUFFO3dCQUMzQyxlQUFlLEdBQUcsR0FBRyxHQUFHLGVBQWUsQ0FBQztxQkFDekM7b0JBQ0QsSUFBSSxlQUFlLElBQUksSUFBSSxJQUFJLGVBQWUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO3dCQUN6RCxzQkFBc0IsR0FBRyxNQUFNLENBQUMsZUFBZSxDQUFDLENBQUM7d0JBQ2pELDRCQUE0QixHQUFHLHNCQUFzQixHQUFHLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO3dCQUdqRyxpQkFBaUIsR0FBRyxLQUFLLENBQUM7d0JBQzFCLFNBQVMsR0FBRyxhQUFhLEdBQUcsZ0JBQWdCLEdBQUcsR0FBRyxHQUFHLGlCQUFpQixHQUFHLElBQUksQ0FBQzt3QkFDOUUsa0JBQWtCLEdBQUcsd0JBQXdCLENBQUM7d0JBQzlDLGNBQWMsR0FBRyx1QkFBdUIsR0FBRyxVQUFVLEdBQUcsSUFBSSxDQUFDO3dCQUM3RCxnRkFBZ0Y7d0JBRWhGLElBQUksbUJBQW1CLENBQUMsT0FBTyxDQUFDLGdCQUFnQixHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFOzRCQUN2RSxTQUFTLEdBQUcsS0FBSyxDQUFDO3lCQUNuQjs2QkFBTTs0QkFDTCxJQUFJLDZCQUE2QixDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO2dDQUNqRSxTQUFTLEdBQUcsSUFBSSxDQUFDOzZCQUNsQjtpQ0FBTTtnQ0FDTCxTQUFTLEdBQUcsS0FBSyxDQUFDOzZCQUNuQjt5QkFDRjt3QkFFRCxpQkFBaUIsR0FBRyxtQkFBbUIsR0FBRyxTQUFTLEdBQUcsSUFBSSxDQUFDO3dCQUMzRCxJQUFJLGlCQUFpQixDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTs0QkFDeEMsVUFBVSxHQUFHLHVDQUF1QyxDQUFDO3lCQUN0RDs2QkFBTTs0QkFDTCxVQUFVLEdBQUcsd0NBQXdDLENBQUM7eUJBQ3ZEO3dCQUVELFVBQVUsR0FBRyxZQUFZLEdBQUcsc0JBQXNCLEdBQUcsR0FBRyxDQUFDO3dCQUN6RCxhQUFhLEdBQUcsZUFBZSxHQUFHLDRCQUE0QixHQUFHLEdBQUcsQ0FBQzt3QkFFckUsSUFBSSxJQUFJLENBQUMsbUJBQW1CLENBQUMsTUFBTSxJQUFJLENBQUM7NEJBQ3RDLElBQUksQ0FBQyxtQkFBbUIsSUFBSSxHQUFHLEdBQUcsU0FBUyxHQUFHLEdBQUcsR0FBRyxrQkFBa0IsR0FBRyxHQUFHLEdBQUcsY0FBYyxHQUFHLEdBQUcsR0FBRyxpQkFBaUIsR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLGFBQWEsR0FBRyxHQUFHLENBQUM7OzRCQUUxTCxJQUFJLENBQUMsbUJBQW1CLElBQUksSUFBSSxHQUFHLFNBQVMsR0FBRyxHQUFHLEdBQUcsa0JBQWtCLEdBQUcsR0FBRyxHQUFHLGNBQWMsR0FBRyxHQUFHLEdBQUcsaUJBQWlCLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxhQUFhLEdBQUcsR0FBRyxDQUFDO3FCQUU5TDtpQkFFRjthQUNGO1NBRUY7UUFFRCxJQUFJLENBQUMsWUFBWSxHQUFHLEtBQUssQ0FBQztRQUMxQixJQUFJLENBQUMsa0JBQWtCLEdBQUcsT0FBTyxDQUFDO0lBRXBDLENBQUM7SUFHRDs7OztTQUlLOzs7Ozs7Ozs7Ozs7Ozs7O0lBQ0csNENBQXFCOzs7Ozs7Ozs7Ozs7Ozs7SUFBN0IsVUFBOEIsT0FBZSxFQUFFLFVBQWtCLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsbUJBQTBCLEVBQUUsd0JBQStCO1FBQTNELG9DQUFBLEVBQUEsMEJBQTBCO1FBQUUseUNBQUEsRUFBQSwrQkFBK0I7O1lBQy9JLHFCQUFxQixHQUFHLEVBQUU7O1lBQzFCLDBCQUEwQixHQUFHLEVBQUU7UUFDbkMsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLE9BQU8sQ0FBQztRQUNqQyxJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksT0FBTyxDQUFDO1FBQy9CLElBQUksQ0FBQyxjQUFjLEdBQUcsRUFBRSxDQUFDO1FBQ3pCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxFQUFFLENBQUM7O1lBRTNCLFFBQVEsR0FBVyxJQUFJLE1BQU0sQ0FBQyxHQUFHLENBQUM7O1lBQ2xDLE9BQU8sR0FBVyxFQUFFLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0I7O1lBQ3BELGtCQUFrQixHQUFXLEtBQUssQ0FBQyxNQUFNLENBQUMscUJBQXFCLElBQUksQ0FBQzs7WUFDcEUsVUFBVSxHQUFXLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxDQUFDO1FBQzNELElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxVQUFVLEdBQUcsdUJBQXVCLENBQUMsR0FBRyxrQkFBa0IsQ0FBQzs7WUFDckYsYUFBYSxHQUFXLEtBQUssQ0FBQyxNQUFNLENBQUMsY0FBYyxJQUFJLENBQUM7UUFDNUQsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxnQkFBZ0IsQ0FBQyxHQUFHLGFBQWEsQ0FBQzs7WUFDekUsaUJBQWlCLEdBQVcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsSUFBSSxDQUFDO1FBQ3RFLElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxVQUFVLEdBQUcsc0JBQXNCLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs7WUFDbkYsYUFBYSxHQUFXLEtBQUssQ0FBQyxNQUFNLENBQUMsZUFBZSxJQUFJLENBQUM7UUFDN0QsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxpQkFBaUIsQ0FBQyxHQUFHLGFBQWEsQ0FBQztRQUk5RSxPQUFPLEdBQUcsT0FBTyxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDekMsSUFBSSxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsRUFBRTtZQUM1QixPQUFPLEdBQUcsR0FBRyxHQUFHLE9BQU8sQ0FBQztTQUN6Qjs7WUFDRyxjQUFjLEdBQVcsTUFBTSxDQUFDLE9BQU8sQ0FBQztRQUc1QyxPQUFPLEdBQUcsRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7UUFDN0MsT0FBTyxHQUFHLE9BQU8sQ0FBQyxPQUFPLENBQUMsUUFBUSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQ3pDLElBQUksT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUFHLEVBQUU7WUFDNUIsT0FBTyxHQUFHLEdBQUcsR0FBRyxPQUFPLENBQUM7U0FDekI7O1lBQ0csY0FBYyxHQUFXLE1BQU0sQ0FBQyxPQUFPLENBQUM7UUFDNUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLE9BQU8sQ0FBQyxHQUFHLGNBQWMsQ0FBQzs7WUFFN0MsR0FBRyxHQUFXLENBQUM7O1lBQ2Ysa0JBQWtCLEdBQUcsRUFBRTs7WUFDdkIsdUJBQXVCLEdBQUcsRUFBRTs7WUFDNUIsZUFBdUI7O1lBQ3ZCLGtDQUEwQztRQUM5QywyRUFBMkU7UUFDM0UsK0NBQStDO1FBQy9DLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTs7Z0JBQ3hDLFNBQVMsR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQzs7Z0JBQzNCLFNBQVMsR0FBVyxrQkFBa0IsQ0FBQyxNQUFNLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLE1BQU0sRUFBRSxDQUFDLENBQUMsQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLENBQUM7WUFDL0YsU0FBUyxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLE1BQU0sRUFBRSxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUM7O2dCQUU5QyxjQUFjLEdBQVcsdUJBQXVCLENBQUMsTUFBTSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsdUJBQXVCLENBQUMsR0FBRyxDQUFDO1lBQzlHLGNBQWMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsSUFBSSxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsY0FBYyxDQUFDOztnQkFFN0QsZUFBZSxHQUFHLFNBQVMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQ3JELElBQUcsZUFBZSxDQUFDLE1BQU0sSUFBRSxFQUFFLEVBQUM7Z0JBQzVCLE9BQU8sR0FBRyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUM7Z0JBQzlCLE9BQU8sR0FBRyxPQUFPLENBQUMsT0FBTyxDQUFDLFFBQVEsRUFBRSxHQUFHLENBQUMsQ0FBQztnQkFDekMsSUFBSSxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsRUFBRTtvQkFDNUIsT0FBTyxHQUFHLEdBQUcsR0FBRyxPQUFPLENBQUM7aUJBQ3pCO2dCQUNELGNBQWMsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7Z0JBR2pDLE9BQU8sR0FBRyxFQUFFLEdBQUcsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUNsQyxPQUFPLEdBQUcsT0FBTyxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBQ3pDLElBQUksT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUFHLEVBQUU7b0JBQzVCLE9BQU8sR0FBRyxHQUFHLEdBQUcsT0FBTyxDQUFDO2lCQUN6QjtnQkFDRCxjQUFjLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2FBQ3BDO1lBR0MsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLGVBQWUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7Z0JBQy9DLElBQUcsQ0FBQyxlQUFlLENBQUMsTUFBTSxJQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFFLENBQUMsSUFBRyxDQUFDLElBQUUsRUFBRSxDQUFDO29CQUMvQyxTQUFTOzs7b0JBRVAsU0FBUyxHQUFXLFlBQVksQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDLENBQUM7O29CQUNoRSxRQUFRLEdBQVcsRUFBRSxHQUFHLGVBQWUsQ0FBQyxDQUFDLENBQUM7Z0JBQzlDLFFBQVEsR0FBRyxRQUFRLENBQUMsT0FBTyxDQUFDLFFBQVEsRUFBRSxHQUFHLENBQUMsQ0FBQzs7b0JBRXZDLE9BQU8sR0FBVyxTQUFTO2dCQUUvQix1Q0FBdUM7Z0JBQ3ZDLElBQUksU0FBUyxJQUFJLFVBQVUsSUFBSSxTQUFTLElBQUksV0FBVyxFQUFFO29CQUN2RCxTQUFTLEdBQUcsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLENBQUMsR0FBRyxTQUFTLENBQUM7aUJBQzVEO2dCQUVELHVEQUF1RDtnQkFDdkQsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3BCLElBQUksUUFBUSxJQUFJLFFBQVEsSUFBSSxFQUFFLEVBQUU7d0JBQzlCLElBQUksUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUFHLEVBQUU7NEJBQzdCLFFBQVEsR0FBRyxHQUFHLEdBQUcsUUFBUSxDQUFDO3lCQUMzQjs7NEJBRUcsR0FBRyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUM7d0JBRTFCLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxFQUFFOzRCQUNoRCxxQkFBcUIsR0FBRyxJQUFJLEtBQUssQ0FBQzt5QkFDbkM7NkJBQU07NEJBQ0wscUJBQXFCLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUE7eUJBQzdEO3dCQUdELElBQUksSUFBSSxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxFQUFFOzRCQUNsRCwwQkFBMEIsR0FBRyxJQUFJLEtBQUssQ0FBQzt5QkFDeEM7NkJBQU07NEJBQ0wsMEJBQTBCLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUE7eUJBQ3BFO3dCQUdELFNBQVMsQ0FBQyxTQUFTLENBQUMsR0FBRyxHQUFHLENBQUM7d0JBQzNCLHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7d0JBQ3JDLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxxQkFBcUIsQ0FBQyxDQUFDO3dCQUd2RCxJQUFJLENBQUMsT0FBTyxJQUFJLFlBQVksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsT0FBTyxJQUFJLFlBQVksQ0FBQyw0QkFBNEIsQ0FBQyxFQUFFOzRCQUM1RyxjQUFjLENBQUMsU0FBUyxDQUFDLEdBQUcsR0FBRyxHQUFHLGNBQWMsQ0FBQzs0QkFDakQsMEJBQTBCLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRyxDQUFDLEdBQUcsR0FBRyxjQUFjLENBQUMsQ0FBQyxDQUFDO3lCQUM5RDs2QkFBTSxJQUFJLE9BQU8sSUFBSSxZQUFZLENBQUMsa0JBQWtCLEVBQUU7NEJBQ3JELGNBQWMsQ0FBQyxTQUFTLENBQUMsR0FBRyxHQUFHLEdBQUcsY0FBYyxDQUFDOzRCQUNqRCwwQkFBMEIsQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBRyxHQUFHLGNBQWMsQ0FBQyxDQUFDLENBQUM7eUJBQzlEOzZCQUNJOzRCQUNILGNBQWMsQ0FBQyxTQUFTLENBQUMsR0FBRyxHQUFHLENBQUM7NEJBQ2hDLDBCQUEwQixDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7eUJBQzNDO3dCQUVELElBQUksQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSwwQkFBMEIsQ0FBQyxDQUFDO3FCQUMvRDtpQkFFRjtxQkFDSTtvQkFDSCxJQUFJLEtBQUssQ0FBQyxlQUFlLENBQUMsSUFBSSxTQUFTLElBQUksVUFBVTt3QkFDbkQsZUFBZSxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxPQUFPLEVBQUUsQ0FBQztvQkFDeEQsa0RBQWtEO29CQUNsRCx1RUFBdUU7b0JBRXZFLElBQUksS0FBSyxDQUFDLGtDQUFrQyxDQUFDLElBQUksU0FBUyxJQUFJLFdBQVc7d0JBQ3ZFLGtDQUFrQyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxPQUFPLEVBQUUsQ0FBQztvQkFDM0UseUZBQXlGO29CQUd6RixZQUFZO29CQUNaLFNBQVMsQ0FBQyxTQUFTLENBQUMsR0FBRyxRQUFRLENBQUM7b0JBQ2hDLGNBQWMsQ0FBQyxTQUFTLENBQUMsR0FBRyxRQUFRLENBQUM7b0JBQ3JDLElBQUksU0FBUyxJQUFJLFVBQVUsRUFBRTt3QkFFM0IsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7cUJBQ3BDO3lCQUNJLElBQUksU0FBUyxJQUFJLFdBQVcsRUFBRTt3QkFDakMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztxQkFDMUM7aUJBQ0Y7YUFDRjtZQUNELGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxHQUFHLFNBQVMsQ0FBQztZQUNwQyx1QkFBdUIsQ0FBQyxHQUFHLENBQUMsR0FBRyxjQUFjLENBQUM7WUFDOUMsR0FBRyxFQUFFLENBQUM7U0FDUDtRQUdELEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNqQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsdUJBQXVCLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFeEMsS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLGtCQUFrQixDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUN2QyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsdUJBQXVCLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDOztZQUUxQyxVQUFVLEdBQVcsaUJBQWlCLEdBQUcsZUFBZTs7WUFDeEQsZ0JBQWdCLEdBQVcsdUJBQXVCLEdBQUcsa0NBQWtDOztZQUN2RixhQUFhLEdBQVcseUJBQXlCOztZQUNqRCxVQUFrQjs7WUFJbEIsVUFBa0I7O1lBQ2xCLGFBQXFCOztZQUNyQixTQUFpQjs7WUFDakIsa0JBQTBCOztZQUMxQix5QkFBaUM7O1lBQ2pDLHNCQUE4Qjs7WUFDOUIsV0FBbUI7O1lBQ25CLGdCQUF3Qjs7WUFDeEIsaUJBQXlCOztZQUN6QixjQUFzQjs7WUFDdEIsZUFBdUI7O1lBQ3ZCLHFCQUE2Qjs7WUFDN0IsY0FBc0I7O1lBRXRCLEdBQUcsR0FBVyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxDQUFDO1FBRTNELElBQUksQ0FBQyxHQUFHLEVBQUU7WUFDUiwwRUFBMEU7WUFDMUUscUZBQXFGO1lBQ3JGLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7O1lBQ0csUUFBUSxHQUFRLEdBQUcsQ0FBQyxRQUFRLENBQUM7Ozs7OztZQU03QixPQUFPLEdBQVcsRUFBRTs7WUFDcEIsbUJBQW1CLEdBQVksSUFBSTtRQUd2QyxvREFBb0Q7UUFDcEQsc0NBQXNDO1FBQ3RDLEtBQUssSUFBSSxHQUFDLEdBQUcsQ0FBQyxFQUFFLEdBQUMsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxDQUFDLE1BQU0sRUFBRSxHQUFDLEVBQUUsRUFBRTs7O2dCQUV0RCxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxHQUFDLENBQUM7WUFDdkMsVUFBVSxHQUFHLGlCQUFpQixHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sQ0FBQztZQUV2RSxTQUFTLEdBQUcsYUFBYSxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUM7WUFFdkMsSUFBSSxtQkFBbUIsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUU7Z0JBQzFDLG1CQUFtQixHQUFHLEtBQUssQ0FBQzthQUM3QjtpQkFBTTtnQkFDTCxJQUFJLHdCQUF3QixDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRTtvQkFDL0MsbUJBQW1CLEdBQUcsSUFBSSxDQUFDO2lCQUM1QjtxQkFBTTtvQkFDTCxtQkFBbUIsR0FBRyxLQUFLLENBQUM7aUJBQzdCO2FBQ0Y7O2dCQUNHLFlBQVksR0FBRyxHQUFHLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQztZQUNqQyxtREFBbUQ7WUFDbkQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztvQkFDMUMsS0FBSyxHQUFHLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2dCQUM3QixJQUFJLEtBQUssQ0FBQyxXQUFXLElBQUksWUFBWSxDQUFDLENBQUMsQ0FBQyxFQUFFOzs7d0JBRXBDLFNBQVMsR0FBVyxLQUFLLENBQUMsSUFBSTtvQkFDbEMsa0JBQWtCLEdBQUcsYUFBYSxHQUFHLEtBQUssQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDO29CQUN2RCx5QkFBeUIsR0FBRyxvQkFBb0IsR0FBRyxtQkFBbUIsQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxHQUFHLElBQUksQ0FBQztvQkFDOUcsT0FBTyxHQUFHLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxDQUFDLEdBQUcsS0FBSyxDQUFDLFdBQVcsQ0FBQztvQkFDakUsY0FBYyxHQUFHLHVCQUF1QixHQUFHLG1CQUFtQixDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLEdBQUcsSUFBSSxDQUFDO29CQUN0RyxlQUFlLEdBQUcsY0FBYyxHQUFHLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsR0FBRyxJQUFJLENBQUM7b0JBQ3RHLHFCQUFxQixHQUFHLG9CQUFvQixHQUFHLG1CQUFtQixDQUFDLDBCQUEwQixDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsR0FBRyxJQUFJLENBQUM7b0JBQ3hILGdCQUFnQixHQUFHLHlCQUF5QixHQUFHLEtBQUssQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDO29CQUN4RSxpQkFBaUIsR0FBRywwQkFBMEIsR0FBRyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLE1BQU0sRUFBRSxLQUFLLENBQUMsV0FBVyxFQUFFLEtBQUssQ0FBQyxXQUFXLENBQUMsR0FBRyxJQUFJLENBQUM7b0JBQzFJLDZFQUE2RTtvQkFDN0Usc0JBQXNCLEdBQUcsK0JBQStCLEdBQUcsS0FBSyxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7b0JBQ2hGLGNBQWMsR0FBRyxtQkFBbUIsR0FBRyxtQkFBbUIsR0FBRyxJQUFJLENBQUM7aUJBR25FO2FBQ0Y7WUFDRCxVQUFVLEdBQUcsWUFBWSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQztZQUNqRSxhQUFhLEdBQUcsZUFBZSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQztZQUN6RSxJQUFJLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLElBQUksQ0FBQztnQkFDdEMsSUFBSSxDQUFDLG1CQUFtQixJQUFJLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLGdCQUFnQixHQUFHLEdBQUcsR0FBRyxhQUFhLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsU0FBUyxHQUFHLEdBQUcsR0FBRyxjQUFjLEdBQUcsR0FBRyxHQUFHLGtCQUFrQixHQUFHLEdBQUcsR0FBRyx5QkFBeUIsR0FBRyxHQUFHLEdBQUcsY0FBYyxHQUFHLEdBQUcsR0FBRyxlQUFlLEdBQUcsR0FBRyxHQUFHLHFCQUFxQixHQUFHLEdBQUcsR0FBRyxnQkFBZ0IsR0FBRyxHQUFHLEdBQUcsaUJBQWlCLEdBQUcsR0FBRyxHQUFHLHNCQUFzQixHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLGFBQWEsR0FBRyxHQUFHLENBQUM7O2dCQUVuYSxJQUFJLENBQUMsbUJBQW1CLElBQUksSUFBSSxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsZ0JBQWdCLEdBQUcsR0FBRyxHQUFHLGFBQWEsR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxTQUFTLEdBQUcsR0FBRyxHQUFHLGNBQWMsR0FBRyxHQUFHLEdBQUcsa0JBQWtCLEdBQUcsR0FBRyxHQUFHLHlCQUF5QixHQUFHLEdBQUcsR0FBRyxjQUFjLEdBQUcsR0FBRyxHQUFHLGVBQWUsR0FBRyxHQUFHLEdBQUcscUJBQXFCLEdBQUcsR0FBRyxHQUFHLGdCQUFnQixHQUFHLEdBQUcsR0FBRyxpQkFBaUIsR0FBRyxHQUFHLEdBQUcsc0JBQXNCLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsYUFBYSxHQUFHLEdBQUcsQ0FBQztTQUd2YTtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOztLQUVDOzs7Ozs7O0lBQ00sd0NBQWlCOzs7Ozs7SUFBeEIsVUFBeUIsT0FBZSxFQUFFLFFBQWlCO1FBRXpELCtDQUErQztRQUMvQyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7O2dCQUN2RCxNQUFNLEdBQVcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDbkQsSUFBSSxNQUFNLENBQUMsVUFBVSxJQUFJLE1BQU0sRUFBRTtnQkFDL0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO29CQUN6RixNQUFNLENBQUMsT0FBTyxHQUFHLFFBQVEsQ0FBQztpQkFDM0I7YUFDRjtTQUNGO0lBQ0gsQ0FBQzs7Ozs7O0lBRU8sOENBQXVCOzs7OztJQUEvQixVQUFnQyxNQUFNOzs7WUFFaEMsVUFBVSxHQUFXLG1CQUFtQixDQUFDLG9CQUFvQjs7WUFDN0QsVUFBVSxHQUFHLE9BQU8sQ0FBQyxvQkFBb0IsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDO1FBQzNELGlEQUFpRDtRQUNqRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsVUFBVSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTs7Z0JBQ3RDLEtBQUssR0FBRyxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBQ3pCLElBQUksS0FBSyxDQUFDLFdBQVcsSUFBSSxZQUFZLENBQUMsa0JBQWtCLEVBQUU7Z0JBQ3hELFVBQVUsR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDO2dCQUMvQixNQUFNO2FBQ1A7U0FDRjtRQUNELE9BQU8sVUFBVSxDQUFDO0lBQ3BCLENBQUM7Ozs7SUFFTSx5Q0FBa0I7OztJQUF6Qjs7WUFDTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFlBQVk7UUFDOUIsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUM7UUFDNUMsSUFBSSxDQUFDLGtCQUFrQixHQUFHLE1BQU0sQ0FBQztJQUVuQyxDQUFDOzs7Ozs7SUFJTSxxREFBOEI7Ozs7O0lBQXJDLFVBQXNDLE9BQWUsRUFBRSxVQUFrQjs7WUFDbkUsY0FBYyxHQUFXLEdBQUc7O1lBQzVCLGlCQUFpQixHQUFXLEdBQUc7O1lBQy9CLFlBQVksR0FBVyxHQUFHOztZQUMxQixlQUFlLEdBQVcsR0FBRzs7WUFFN0IsZUFBdUI7O1lBQ3ZCLFlBQW9COztZQUNwQixTQUFpQjs7WUFDakIsa0JBQTBCOztZQUMxQixXQUFtQjs7WUFDbkIsY0FBc0I7O1lBQ3RCLFVBQWtCOztZQUNsQixVQUFVLEdBQVcsQ0FBQzs7WUFDdEIsWUFBWSxHQUFXLENBQUM7O1lBRXhCLFVBQVUsR0FBVyxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsQ0FBQzs7WUFDdkQsY0FBYyxHQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDOztZQUM3RCxhQUFhLEdBQVksS0FBSzs7WUFDOUIsZ0JBQWdCLEdBQVcsSUFBSSxDQUFDLHVCQUF1QjtRQUMzRCxJQUFJLENBQUMsdUJBQXVCLEdBQUcsRUFBRSxDQUFDO1FBQ2xDLHNEQUFzRDtRQUN0RCxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsWUFBWSxDQUFDLG9CQUFvQixDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTs7Z0JBQ3ZFLEtBQUssR0FBRyxZQUFZLENBQUMsb0JBQW9CLENBQUMsS0FBSyxDQUFDOztnQkFDaEQsbUJBQW1CLEdBQUcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNySSxJQUFJLEtBQUssSUFBSSw2QkFBNkIsRUFBRTtnQkFDMUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxVQUFVLEdBQUcsbUJBQW1CLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQy9ELGVBQWUsR0FBRyxDQUFDLFVBQVUsR0FBRyxNQUFNLENBQUMsbUJBQW1CLENBQUMsR0FBRyxjQUFjLENBQUM7Z0JBQzdFLGNBQWMsR0FBRyxDQUFDLFVBQVUsQ0FBQztnQkFDN0IsaUJBQWlCLEdBQUcsQ0FBQyxVQUFVLEdBQUcsY0FBYyxDQUFDO2dCQUdqRCxTQUFTLEdBQUcsYUFBYSxHQUFHLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2dCQUM1RSxrQkFBa0IsR0FBRyxtQkFBbUIsQ0FBQztnQkFDekMsY0FBYyxHQUFHLHVCQUF1QixHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUM7Z0JBQ3RGLFVBQVUsR0FBRyxjQUFjLEdBQUcsbUJBQW1CLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLHVCQUF1QixDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDO2dCQUduSCxlQUFlLEdBQUcsaUJBQWlCLEdBQUcsY0FBYyxHQUFHLEdBQUcsR0FBRyxZQUFZLEdBQUcsR0FBRyxDQUFDO2dCQUVoRixZQUFZLEdBQUcsZUFBZSxHQUFHLGlCQUFpQixHQUFHLEdBQUcsR0FBRyxlQUFlLEdBQUcsR0FBRyxDQUFDO2dCQUVqRixJQUFJLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxNQUFNLElBQUksQ0FBQztvQkFDMUMsSUFBSSxDQUFDLHVCQUF1QixJQUFJLEdBQUcsR0FBRyxTQUFTLEdBQUcsR0FBRyxHQUFHLGtCQUFrQixHQUFHLEdBQUcsR0FBRyxjQUFjLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsZUFBZSxHQUFHLEdBQUcsR0FBRyxZQUFZLEdBQUcsR0FBRyxDQUFDOztvQkFFeEssSUFBSSxDQUFDLHVCQUF1QixJQUFJLElBQUksR0FBRyxTQUFTLEdBQUcsR0FBRyxHQUFHLGtCQUFrQixHQUFHLEdBQUcsR0FBRyxjQUFjLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsZUFBZSxHQUFHLEdBQUcsR0FBRyxZQUFZLEdBQUcsR0FBRyxDQUFDO2dCQUczSyxJQUFJLEtBQUssSUFBSSxpQkFBaUIsRUFBRTtvQkFDOUIsWUFBWSxHQUFHLE1BQU0sQ0FBQyxVQUFVLEdBQUcsbUJBQW1CLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7aUJBQ2hFO2dCQUNELFVBQVUsR0FBRyxNQUFNLENBQUMsVUFBVSxHQUFHLG1CQUFtQixDQUFDLENBQUM7YUFLdkQ7aUJBQU07Z0JBQ0wsU0FBUyxHQUFHLGFBQWEsR0FBRyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLElBQUksQ0FBQztnQkFDNUUsa0JBQWtCLEdBQUcsbUJBQW1CLENBQUM7Z0JBQ3pDLGNBQWMsR0FBRyx1QkFBdUIsR0FBRyxJQUFJLENBQUMsdUJBQXVCLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFDO2dCQUN0RixVQUFVLEdBQUcsY0FBYyxHQUFHLG1CQUFtQixDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQztnQkFDbkgsWUFBWSxHQUFHLGVBQWUsR0FBRyxDQUFDLFlBQVksR0FBRyxjQUFjLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQyxZQUFZLEdBQUcsY0FBYyxDQUFDLEdBQUcsR0FBRyxDQUFDO2dCQUUvRyxlQUFlLEdBQUcsaUJBQWlCLEdBQUcsQ0FBQyxZQUFZLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQyxZQUFZLENBQUMsR0FBRyxHQUFHLENBQUM7Z0JBRWxGLElBQUksSUFBSSxDQUFDLHVCQUF1QixDQUFDLE1BQU0sSUFBSSxDQUFDO29CQUMxQyxJQUFJLENBQUMsdUJBQXVCLElBQUksR0FBRyxHQUFHLFNBQVMsR0FBRyxHQUFHLEdBQUcsa0JBQWtCLEdBQUcsR0FBRyxHQUFHLGNBQWMsR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxlQUFlLEdBQUcsR0FBRyxHQUFHLFlBQVksR0FBRyxHQUFHLENBQUM7O29CQUV4SyxJQUFJLENBQUMsdUJBQXVCLElBQUksSUFBSSxHQUFHLFNBQVMsR0FBRyxHQUFHLEdBQUcsa0JBQWtCLEdBQUcsR0FBRyxHQUFHLGNBQWMsR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxlQUFlLEdBQUcsR0FBRyxHQUFHLFlBQVksR0FBRyxHQUFHLENBQUM7YUFDNUs7U0FFRjtRQUNELElBQUksZ0JBQWdCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQ3BELE9BQU8sSUFBSSxDQUFDO1NBQ2I7YUFBTTtZQUNMLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7SUFFSCxDQUFDOzs7Ozs7SUFFTSw0Q0FBcUI7Ozs7O0lBQTVCLFVBQTZCLE9BQWUsRUFBRSxVQUFrQjs7WUFDMUQsTUFBTSxHQUFXLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUc7O1lBQ2pELEtBQUssR0FBVyxJQUFJO1FBQ3hCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxZQUFZLENBQUMsb0JBQW9CLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFOztnQkFDdkUsT0FBSyxHQUFHLFlBQVksQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLENBQUM7WUFDdEQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLE9BQUssQ0FBQyxDQUFDO1NBQ3hDO1FBQ0QsNERBQTREO1FBQzVELEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsMEJBQTBCLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFOztnQkFDckUsS0FBSyxHQUFHLElBQUksQ0FBQywwQkFBMEIsQ0FBQyxLQUFLLENBQUM7O2dCQUNoRCxHQUFHLEdBQVEsRUFBRTtZQUNqQixHQUFHLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQztZQUNyQixHQUFHLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUM7WUFDMUIsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsRUFBRTtnQkFDekQsR0FBRyxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7YUFDcEY7WUFDRCxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDL0IsMkRBQTJEO1NBQzVEO1FBQ0QsSUFBSSxDQUFDLDBCQUEwQixDQUFDLEdBQUcsRUFBRSxDQUFDO0lBQ3hDLENBQUM7Ozs7Ozs7Ozs7SUFJTSx5Q0FBa0I7Ozs7Ozs7OztJQUF6QixVQUEwQixPQUFlLEVBQUUsVUFBa0IsRUFBRSxpQkFBZ0MsRUFBRSxrQkFBaUMsRUFBRSxPQUF3QixFQUFFLFNBQXlCO1FBQXhILGtDQUFBLEVBQUEsd0JBQWdDO1FBQUUsbUNBQUEsRUFBQSx5QkFBaUM7UUFBRSx3QkFBQSxFQUFBLGVBQXdCO1FBQUUsMEJBQUEsRUFBQSxnQkFBeUI7UUFDckwsSUFBSTtZQUNGLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLElBQUksT0FBTyxJQUFJLFVBQVUsRUFBRTs7b0JBRWpELFVBQVUsR0FBVyxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsQ0FBQzs7b0JBQ3ZELGNBQWMsR0FBVyxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxDQUFDOzs7Ozs7b0JBS3hELGdCQUFnQixHQUFXLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDOztvQkFDL0MsZUFBZSxHQUFXLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDOztvQkFDekUsbUJBQW1CLEdBQVcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQzs7b0JBQ3hELGtCQUFrQixHQUFXLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7O29CQUNsRixVQUFVLEdBQVcsQ0FBQzs7b0JBQ3RCLFlBQVksR0FBVyxDQUFDOzs7O29CQUt4QixjQUFjLEdBQVcsR0FBRzs7b0JBQzVCLGlCQUFpQixHQUFXLEdBQUc7O29CQUMvQixZQUFZLEdBQVcsR0FBRzs7b0JBQzFCLGVBQWUsR0FBVyxHQUFHO2dCQUdqQyxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsWUFBWSxDQUFDLG9CQUFvQixDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTs7d0JBQ3ZFLEtBQUssR0FBRyxZQUFZLENBQUMsb0JBQW9CLENBQUMsS0FBSyxDQUFDO29CQUN0RCxJQUFJLEtBQUssSUFBSSw2QkFBNkIsRUFBRTt3QkFDMUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzt3QkFDN0YsZUFBZSxHQUFHLENBQUMsVUFBVSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsd0JBQXdCLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQyxDQUFDLEdBQUcsY0FBYyxDQUFDO3dCQUMzRyxjQUFjLEdBQUcsQ0FBQyxVQUFVLENBQUM7d0JBQzdCLGlCQUFpQixHQUFHLENBQUMsVUFBVSxHQUFHLGNBQWMsQ0FBQzt3QkFDakQsdUJBQXVCO3dCQUN2QixtREFBbUQ7d0JBQ25ELHVCQUF1Qjt3QkFFdkIseURBQXlEO3dCQUN6RCwwQkFBMEI7d0JBRTFCLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTs0QkFDbkIsbUJBQW1CLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssQ0FBQyxHQUFHLGVBQWUsQ0FBQzs0QkFDaEYsZ0JBQWdCLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssQ0FBQyxHQUFHLFlBQVksQ0FBQzs0QkFDMUUsa0JBQWtCLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssQ0FBQyxHQUFHLGVBQWUsQ0FBQzs0QkFDL0UsZUFBZSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxZQUFZLENBQUM7eUJBQzFFOzZCQUFNOzRCQUNMLGdCQUFnQixDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxlQUFlLENBQUM7NEJBQzdFLG1CQUFtQixDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxZQUFZLENBQUM7NEJBQzdFLGVBQWUsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsZUFBZSxDQUFDOzRCQUM1RSxrQkFBa0IsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsWUFBWSxDQUFDO3lCQUM3RTt3QkFHRCxJQUFJLEtBQUssSUFBSSxpQkFBaUIsRUFBRTs0QkFDOUIsWUFBWSxHQUFHLE1BQU0sQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzt5QkFDOUY7d0JBRUQsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFOzRCQUNuQixnQkFBZ0IsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLE9BQU8sQ0FBQyxHQUFHLGNBQWMsQ0FBQzs0QkFDdEYsbUJBQW1CLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssR0FBRyxPQUFPLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs0QkFDNUYsZUFBZSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLEdBQUcsT0FBTyxDQUFDLEdBQUcsY0FBYyxDQUFDOzRCQUNyRixrQkFBa0IsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLE9BQU8sQ0FBQyxHQUFHLGlCQUFpQixDQUFDO3lCQUM1Rjs2QkFBTTs0QkFDTCxtQkFBbUIsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLE9BQU8sQ0FBQyxHQUFHLGNBQWMsQ0FBQzs0QkFDekYsZ0JBQWdCLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssR0FBRyxPQUFPLENBQUMsR0FBRyxpQkFBaUIsQ0FBQzs0QkFDekYsa0JBQWtCLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssR0FBRyxPQUFPLENBQUMsR0FBRyxjQUFjLENBQUM7NEJBQ3hGLGVBQWUsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLE9BQU8sQ0FBQyxHQUFHLGlCQUFpQixDQUFDO3lCQUN6Rjt3QkFFRCxJQUFJLENBQUMsd0JBQXdCLENBQUMsR0FBRyxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLEVBQUUsVUFBVSxDQUFDLENBQUM7d0JBQ3hGLFVBQVUsR0FBRyxNQUFNLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQztxQkFLckY7eUJBQU07d0JBQ0wsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFOzRCQUNuQixnQkFBZ0IsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsWUFBWSxDQUFDOzRCQUMxRSxtQkFBbUIsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsWUFBWSxHQUFHLGNBQWMsQ0FBQzs0QkFDOUYsZUFBZSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxZQUFZLENBQUM7NEJBQ3pFLGtCQUFrQixDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxZQUFZLEdBQUcsY0FBYyxDQUFDO3lCQUM5Rjs2QkFBTTs0QkFDTCxtQkFBbUIsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsWUFBWSxDQUFDOzRCQUM3RSxnQkFBZ0IsQ0FBQyxPQUFPLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFDLEdBQUcsWUFBWSxHQUFHLGNBQWMsQ0FBQzs0QkFDM0Ysa0JBQWtCLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLEdBQUcsR0FBRyxHQUFHLEtBQUssQ0FBQyxHQUFHLFlBQVksQ0FBQzs0QkFDNUUsZUFBZSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxZQUFZLEdBQUcsY0FBYyxDQUFDO3lCQUUzRjt3QkFFRCxJQUFJLENBQUMsd0JBQXdCLENBQUMsVUFBVSxHQUFHLDZCQUE2QixDQUFDLEdBQUcsWUFBWSxDQUFDO3FCQUMxRjtpQkFDRjtnQkFDRCxJQUFJLENBQUMsbUJBQW1CLENBQUMsT0FBTyxFQUFFLFVBQVUsRUFBRSxpQkFBaUIsRUFBRSxrQkFBa0IsRUFBRSxTQUFTLENBQUMsQ0FBQzs7O29CQUU1RixTQUFTLEdBQVksSUFBSSxDQUFDLDhCQUE4QixDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUM7Z0JBQ2pGLElBQUksQ0FBQyxjQUFjLENBQUMsaUNBQWlDLENBQUMsU0FBUyxDQUFDLENBQUM7YUFHbEU7U0FFRjtRQUFDLE9BQU8sR0FBRyxFQUFFO1NBQ2I7SUFDSCxDQUFDOzs7Ozs7Ozs7SUFDTSwwQ0FBbUI7Ozs7Ozs7O0lBQTFCLFVBQTJCLE9BQWUsRUFBRSxVQUFrQixFQUFFLGlCQUFnQyxFQUFFLGtCQUFpQyxFQUFFLFNBQXlCO1FBQTlGLGtDQUFBLEVBQUEsd0JBQWdDO1FBQUUsbUNBQUEsRUFBQSx5QkFBaUM7UUFBRSwwQkFBQSxFQUFBLGdCQUF5Qjs7WUFDeEosYUFBYSxHQUFXLENBQUM7UUFDN0IsSUFBSTtZQUNGLElBQUksT0FBTyxJQUFJLElBQUksSUFBSSxVQUFVLElBQUksSUFBSSxFQUFFOztvQkFDckMsTUFBTSxHQUFXLE9BQU8sR0FBRyxHQUFHLEdBQUcsVUFBVSxHQUFHLEdBQUc7O29CQUNqRCxVQUFrQjtnQkFFdEIsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLFlBQVksQ0FBQyxvQkFBb0IsQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7O3dCQUN2RSxLQUFLLEdBQUcsWUFBWSxDQUFDLG9CQUFvQixDQUFDLEtBQUssQ0FBQztvQkFDdEQsSUFBSSxLQUFLLElBQUksNkJBQTZCLEVBQUU7Ozs0QkFHdEMsVUFBVSxHQUFXLElBQUksTUFBTTt3QkFDbkMsVUFBVSxDQUFDLE9BQU8sR0FBRyxTQUFTLENBQUM7d0JBQy9CLElBQUksSUFBSSxDQUFDLGNBQWMsQ0FBQyxjQUFjLElBQUksY0FBYyxFQUFFOzRCQUN4RCxhQUFhLEdBQUcsRUFBRSxDQUFDOztnQ0FDZixNQUFNLEdBQVcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUMsSUFBSSxDQUFDOzRCQUFDLENBQUM7NEJBQ3pFLCtEQUErRDs0QkFDL0QsVUFBVSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMseUJBQXlCLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7NEJBQzNFLFVBQVUsR0FBRyxVQUFVLENBQUMsT0FBTyxDQUFDLElBQUksTUFBTSxDQUFDLEtBQUssRUFBRSxHQUFHLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQzs0QkFDN0QsYUFBYSxHQUFHLENBQUMsQ0FBQzt5QkFDbkI7NkJBQU07O2dDQUNELE1BQU0sR0FBVyxJQUFJLENBQUMsd0JBQXdCLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQyxJQUFJLENBQUM7NEJBQUMsQ0FBQzs0QkFDekUsK0RBQStEOzRCQUMvRCxVQUFVLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyx5QkFBeUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQzs0QkFDM0UsVUFBVSxHQUFHLFVBQVUsQ0FBQyxPQUFPLENBQUMsSUFBSSxNQUFNLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO3lCQUM5RDt3QkFDRCxVQUFVLENBQUMsaUJBQWlCLEdBQUcsaUJBQWlCLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxHQUFHLEdBQUcsR0FBRyxVQUFVLENBQUM7OzRCQUV4RyxTQUFTLEdBQVcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLEtBQUssQ0FBQzs7NEJBQ3ZELFFBQVEsR0FBVyxNQUFNLEdBQUcsS0FBSyxHQUFHLE9BQU87d0JBQy9DLElBQUksSUFBSSxDQUFDLGlCQUFpQjs0QkFDeEIsVUFBVSxDQUFDLE1BQU0sR0FBRyxXQUFXLENBQUM7OzRCQUVoQyxVQUFVLENBQUMsTUFBTSxHQUFHLFVBQVUsQ0FBQzt3QkFFakMsVUFBVSxDQUFDLFlBQVksR0FBRyxTQUFTLENBQUM7d0JBQ3BDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsTUFBTSxHQUFHLEtBQUssQ0FBQzt3QkFDbkMsVUFBVSxDQUFDLFFBQVEsR0FBRyxRQUFRLENBQUM7d0JBQy9CLFVBQVUsQ0FBQyxVQUFVLEdBQUcsTUFBTSxDQUFDO3dCQUMvQixJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEdBQUcsS0FBSyxFQUFFLFVBQVUsQ0FBQyxDQUFDO3dCQUVoRCxhQUFhLEdBQUcsQ0FBQyxDQUFDO3FCQUNuQjt5QkFDSTs7NEJBQ0MsVUFBVSxHQUFXLElBQUksTUFBTTt3QkFDbkMsVUFBVSxDQUFDLE9BQU8sR0FBRyxTQUFTLENBQUM7d0JBQy9CLElBQUksSUFBSSxDQUFDLGNBQWMsQ0FBQyxjQUFjLElBQUksY0FBYyxFQUFFOzRCQUN4RCxhQUFhLEdBQUcsRUFBRSxDQUFDOztnQ0FDZixNQUFNLEdBQVcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUMsSUFBSSxDQUFDOzRCQUFDLENBQUM7NEJBQ3pFLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLHlCQUF5QixDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDOzRCQUMzRSxVQUFVLEdBQUcsVUFBVSxDQUFDLE9BQU8sQ0FBQyxJQUFJLE1BQU0sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7NEJBQzdELGFBQWEsR0FBRyxDQUFDLENBQUM7eUJBQ25COzZCQUFNOztnQ0FDRCxNQUFNLEdBQVcsSUFBSSxDQUFDLHdCQUF3QixDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUMsSUFBSSxDQUFDOzRCQUFDLENBQUM7NEJBQ3pFLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLHlCQUF5QixDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDOzRCQUMzRSxVQUFVLEdBQUcsVUFBVSxDQUFDLE9BQU8sQ0FBQyxJQUFJLE1BQU0sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7eUJBQzlEO3dCQUNELFVBQVUsQ0FBQyxXQUFXLEdBQUcsTUFBTSxHQUFHLEtBQUssQ0FBQzt3QkFDeEMsVUFBVSxDQUFDLGlCQUFpQixHQUFHLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxLQUFLLENBQUMsR0FBRyxHQUFHLEdBQUcsVUFBVSxDQUFDOzs0QkFDeEcsU0FBUyxHQUFXLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUM7d0JBQzNELFVBQVUsQ0FBQyxZQUFZLEdBQUcsU0FBUyxDQUFDO3dCQUNwQyxVQUFVLENBQUMsVUFBVSxHQUFHLE1BQU0sQ0FBQzt3QkFDL0IsSUFBSSxJQUFJLENBQUMsaUJBQWlCOzRCQUN4QixVQUFVLENBQUMsTUFBTSxHQUFHLFdBQVcsQ0FBQzs7NEJBRWhDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsVUFBVSxDQUFDO3dCQUNqQyxVQUFVLENBQUMsTUFBTSxHQUFHLE1BQU0sR0FBRyxLQUFLLENBQUM7d0JBQ25DLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLE1BQU0sR0FBRyxLQUFLLEVBQUUsVUFBVSxDQUFDLENBQUM7cUJBQ2pEO2lCQUNGO2dCQUVELElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLGtCQUFrQixDQUFDLEVBQUU7b0JBQ3ZGLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxpQkFBaUIsRUFBRSxrQkFBa0IsQ0FBQyxDQUFDO2lCQUNuRTtnQkFFRCxJQUFJLENBQUMsMEJBQTBCLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RDLFFBQVE7Z0JBQ1IseUNBQXlDO2FBQzFDO2lCQUFNO2dCQUNMLElBQUksaUJBQWlCLElBQUksSUFBSSxJQUFJLGtCQUFrQixJQUFJLElBQUksRUFBRTtvQkFDM0QsSUFBSSxDQUFDLHFCQUFxQixDQUFDLGlCQUFpQixFQUFFLGtCQUFrQixDQUFDLENBQUM7aUJBQ25FO2dCQUNELElBQUksQ0FBQywwQkFBMEIsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN4QztTQUVGO1FBQ0QsT0FBTyxLQUFLLEVBQUU7WUFDWixJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFO2dCQUN2RixJQUFJLENBQUMscUJBQXFCLENBQUMsaUJBQWlCLEVBQUUsa0JBQWtCLENBQUMsQ0FBQzthQUNuRTtZQUNELElBQUksQ0FBQyxhQUFhLENBQUMsWUFBWSxHQUFHLElBQUksS0FBSyxFQUFFLENBQUM7WUFDOUMsbUpBQW1KO1lBQ25KLGlCQUFpQixDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUseUZBQXlGLEdBQUcsT0FBTyxDQUFDLENBQUM7U0FDNUk7SUFFSCxDQUFDOzs7Ozs7SUFHRCx5Q0FBa0I7Ozs7O0lBQWxCLFVBQW1CLE1BQU0sRUFBRSxJQUFTO1FBQ2xDLHNEQUFzRDtRQUN0RCxJQUFJLENBQUMsWUFBWSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUNoRCxrRUFBa0U7UUFDbEUsd0VBQXdFO1FBQ3hFLElBQUk7SUFDTixDQUFDO0lBR0QsbUNBQW1DO0lBQ25DLGFBQWE7SUFFYix1Q0FBdUM7SUFDdkMsMENBQTBDO0lBQzFDLE1BQU07SUFFTixnQ0FBZ0M7SUFDaEMsbUNBQW1DO0lBQ25DLE1BQU07SUFFTixpREFBaUQ7SUFDakQsMkNBQTJDO0lBQzNDLE1BQU07SUFFTiwwQ0FBMEM7SUFDMUMsb0NBQW9DO0lBQ3BDLE1BQU07SUFFTixrQ0FBa0M7SUFDbEMsOEJBQThCO0lBQzlCLE1BQU07SUFFTixtQ0FBbUM7SUFDbkMsK0JBQStCO0lBQy9CLE1BQU07SUFFTixzQkFBc0I7SUFDdEIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUVJLDhDQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUEvQixVQUFnQyxTQUFpQjs7WUFDM0MsS0FBSyxHQUFXLEVBQUU7UUFDdEIsSUFBSSxTQUFTLElBQUksdUJBQXVCO1lBQ3RDLEtBQUssR0FBRyxtQkFBbUIsQ0FBQyx1QkFBdUIsQ0FBQzthQUNqRCxJQUFJLFNBQVMsSUFBSSxnQkFBZ0I7WUFDcEMsS0FBSyxHQUFHLG1CQUFtQixDQUFDLHVCQUF1QixDQUFDO2FBQ2pELElBQUksU0FBUyxJQUFJLHNCQUFzQjtZQUMxQyxLQUFLLEdBQUcsbUJBQW1CLENBQUMsc0JBQXNCLENBQUM7YUFDaEQsSUFBSSxTQUFTLElBQUksaUJBQWlCO1lBQ3JDLEtBQUssR0FBRyxtQkFBbUIsQ0FBQyxrQkFBa0IsQ0FBQzs7WUFFL0MsS0FBSyxHQUFHLG1CQUFtQixDQUFDLHFCQUFxQixDQUFDO1FBQ3BELE9BQU8sS0FBSyxDQUFBO0lBQ2QsQ0FBQztJQUVEOztlQUVLOzs7Ozs7OztJQUNFLDBDQUFtQjs7Ozs7OztJQUExQixVQUEyQixNQUFjLEVBQUUsT0FBZTs7WUFDcEQsWUFBWSxHQUFXLENBQUM7O1lBRXhCLEdBQUcsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQzs7WUFDdkIsT0FBTyxHQUFXLEdBQUcsQ0FBQyxDQUFDLENBQUM7O1lBQ3hCLFVBQVUsR0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDOztZQUMzQixXQUFXLEdBQVcsR0FBRyxDQUFDLENBQUMsQ0FBQzs7WUFDNUIsR0FBRyxHQUFXLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxHQUFHLEdBQUcsR0FBRyxVQUFVLENBQUM7UUFFM0QscUhBQXFIO1FBQ3JILElBQUksR0FBRyxFQUFFOztnQkFDSCxNQUFNLEdBQUcsR0FBRyxDQUFDLFFBQVEsQ0FBQzs7Z0JBRXRCLFVBQVUsR0FBRyxPQUFPLENBQUMsb0JBQW9CLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQztZQUMzRCxpREFBaUQ7WUFDakQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFVBQVUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7O29CQUN0QyxLQUFLLEdBQUcsVUFBVSxDQUFDLENBQUMsQ0FBQztnQkFDekIsSUFBSSxLQUFLLENBQUMsV0FBVyxJQUFJLFdBQVcsRUFBRTtvQkFDcEMsbUNBQW1DO29CQUNuQyxLQUFLLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQztpQkFDN0I7YUFDRjtTQUNGO0lBQ0gsQ0FBQzs7OztJQUdNLHVEQUFnQzs7O0lBQXZDOztZQUNNLFlBQVksR0FBVyxFQUFFOztZQUN6QixXQUFXLEdBQVcsRUFBRTs7WUFDeEIsTUFBTSxHQUFXLEVBQUU7UUFFdkIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFOztnQkFDdkQsS0FBSyxHQUFXLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ2xELElBQUksS0FBSyxDQUFDLFVBQVUsSUFBSSxNQUFNLEVBQUU7Z0JBQzlCLE1BQU0sR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDO2dCQUN0QixJQUFJLE1BQU0sQ0FBQyxPQUFPLENBQUMsc0JBQXNCLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxNQUFNLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxDQUFDO3VCQUN0RixNQUFNLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDO3VCQUN0QyxNQUFNLENBQUMsT0FBTyxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUU7b0JBQ2xELFdBQVcsR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDO29CQUNqQyxZQUFZLEdBQUcsWUFBWSxHQUFHLE1BQU0sR0FBRyxHQUFHLEdBQUcsV0FBVyxHQUFHLEdBQUcsQ0FBQztpQkFDaEU7Z0JBQ0QsWUFBWTthQUNiO2lCQUFNLElBQUksS0FBSyxDQUFDLFVBQVUsSUFBSSxNQUFNLElBQUksS0FBSyxDQUFDLGFBQWEsRUFBRTtnQkFDNUQsTUFBTSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUM7Z0JBQ3RCLElBQUksTUFBTSxDQUFDLE9BQU8sQ0FBQyw2QkFBNkIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO29CQUN2RCxXQUFXLEdBQUcsS0FBSyxDQUFDLFlBQVksQ0FBQztvQkFDakMsWUFBWSxHQUFHLFlBQVksR0FBRyxNQUFNLEdBQUcsR0FBRyxHQUFHLFdBQVcsR0FBRyxHQUFHLENBQUM7aUJBQ2hFO2FBQ0Y7U0FDRjtRQUVELElBQUksWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDO1lBQ3pCLFlBQVksR0FBRyxZQUFZLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBRWpFLE9BQU8sWUFBWSxDQUFDO0lBQ3RCLENBQUM7Ozs7SUFFRCwrQkFBUTs7O0lBQVI7UUFBQSxpQkFlQztRQWJDLDBEQUEwRDtRQUMxRCx5SEFBeUg7UUFDekgsNERBQTREO1FBQzVELCtEQUErRDtRQUMvRCxrREFBa0Q7UUFDbEQsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU07OztRQUFDO1lBQ2YsWUFBWSxDQUFDLEtBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUNsQyxLQUFJLENBQUMsY0FBYyxHQUFHLFVBQVU7OztZQUFDO2dCQUMvQixJQUFJLEtBQUksQ0FBQyxTQUFTLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsRUFBRTtvQkFDM0MsS0FBSSxDQUFDLGtCQUFrQixDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUM7aUJBQ3RGO1lBQ0gsQ0FBQyxHQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQ1osQ0FBQyxFQUFDLENBQUM7SUFDSCxDQUFDOzs7O0lBSUQsc0NBQWU7OztJQUFmO1FBRUUsdUVBQXVFO1FBQ3ZFLHdEQUF3RDtRQUN4RCx3QkFBd0I7UUFDeEIsMkRBQTJEO1FBQzNELE9BQU87UUFOVCxpQkE0RUM7UUFwRUMsMkRBQTJEO1FBRTNELDJEQUEyRDtRQUMzRCxxREFBcUQ7UUFDckQsb0RBQW9EO1FBQ3BELDZEQUE2RDtRQUM3RCx5RUFBeUU7UUFDekUsbUNBQW1DO1FBQ25DLHNEQUFzRDtRQUN0RCxrQkFBa0I7UUFDbEIsVUFBVTtRQUNWLDREQUE0RDtRQUM1RCxzRUFBc0U7UUFDdEUsVUFBVTtRQUNWLHdEQUF3RDtRQUN4RCxrRUFBa0U7UUFDbEUsVUFBVTtRQUNWLDBEQUEwRDtRQUMxRCxvRUFBb0U7UUFDcEUsVUFBVTtRQUNWLHNEQUFzRDtRQUN0RCxnRUFBZ0U7UUFDaEUsVUFBVTtRQUNWLDBEQUEwRDtRQUMxRCxxTEFBcUw7UUFDckwsZUFBZTtRQUNmLG9EQUFvRDtRQUNwRCxRQUFRO1FBQ1IsT0FBTztRQUNQLHVDQUF1QztRQUV2Qyx3RUFBd0U7UUFDeEUseURBQXlEO1FBQ3pELG1EQUFtRDtRQUNuRCxnSEFBZ0g7UUFDaEgseURBQXlEO1FBQ3pELGdDQUFnQztRQUNoQywrRkFBK0Y7UUFDL0Ysa0NBQWtDO1FBQ2xDLGlDQUFpQztRQUNqQyw4QkFBOEI7UUFDOUIsMkJBQTJCO1FBQzNCLDZCQUE2QjtRQUM3Qiw4QkFBOEI7UUFDOUIsc0NBQXNDO1FBQ3RDLHdCQUF3QjtRQUN4QixpQ0FBaUM7UUFDakMsc0NBQXNDO1FBQ3RDLHVCQUF1QjtRQUN2QixxQkFBcUI7UUFDckIsbUJBQW1CO1FBQ25CLGtCQUFrQjtRQUNsQixnQkFBZ0I7UUFDaEIsTUFBTTtRQUNOLE1BQU07UUFDTix1Q0FBdUM7UUFFdkMscUJBQXFCLENBQUMsU0FBUzs7OztRQUFDLFVBQUMsS0FBSztZQUN0QywrR0FBK0c7WUFDL0csSUFBRyxLQUFLLENBQUMsRUFBRSxLQUFJLGdCQUFnQixJQUFJLEtBQUssQ0FBQyxFQUFFLEtBQU0sYUFBYSxJQUFJLEtBQUssQ0FBQyxFQUFFLEtBQUksYUFBYSxFQUFDO2dCQUV4RixJQUFJLEtBQUksQ0FBQyxTQUFTLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsRUFBRTtvQkFDekMsS0FBSSxDQUFDLGtCQUFrQixDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUM7aUJBQ3hGO2FBQ0Y7UUFDSCxDQUFDLEVBQUMsQ0FBQztJQUdMLENBQUM7Ozs7O0lBbUNELGdDQUFTOzs7O0lBQVQsVUFBVSxDQUFDO1FBQ1QsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsV0FBVyxJQUFJLENBQUMsQ0FBQyxZQUFZLElBQUksQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQzFFLENBQUM7O0lBam1ETSxvQ0FBdUIsR0FBVyxLQUFLLENBQUM7SUFDeEMsc0NBQXlCLEdBQVcsS0FBSyxDQUFDO0lBQzFDLHdDQUEyQixHQUFXLEtBQUssQ0FBQztJQUM1Qyx1Q0FBMEIsR0FBVyxLQUFLLENBQUM7SUFDM0MsK0JBQWtCLEdBQVcsSUFBSSxDQUFDO0lBQ2xDLGlDQUFvQixHQUFXLEtBQUssQ0FBQztJQUNyQyx5Q0FBNEIsR0FBVyxNQUFNLENBQUM7SUFDOUMsMkJBQWMsR0FBRyxDQUFDLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO0lBQ2xELHdCQUFXLEdBQUcsSUFBSSxLQUFLLENBQUMsVUFBVSxFQUFFLFdBQVcsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUMsVUFBVSxFQUFDLGVBQWUsQ0FBQyxDQUFDOztJQUU3SCx5Q0FBNEIsR0FBRyxZQUFZLENBQUMsV0FBVyxDQUFDO0lBQ3hELGlDQUFvQixHQUFHLENBQUMsdUJBQXVCLEVBQUUsZ0JBQWdCLEVBQUUsc0JBQXNCLEVBQUUsaUJBQWlCLEVBQUUsNkJBQTZCLENBQUMsQ0FBQztJQUM3SSxtQ0FBc0IsR0FBRyxDQUFDLHVCQUF1QixFQUFFLGdCQUFnQixFQUFFLHNCQUFzQixFQUFFLGlCQUFpQixFQUFFLDZCQUE2QixFQUFFLFlBQVksQ0FBQyxDQUFDOztnQkF2QnJLLFNBQVMsU0FBQztvQkFDVCxRQUFRLEVBQUUsY0FBYztvQkFDeEIsNFRBQWtDOztpQkFFbkM7Ozs7Z0JBOUIyQixVQUFVO2dCQUU3QixhQUFhO2dCQUZrQixTQUFTOzs7K0JBaUM5QyxTQUFTLFNBQUMsY0FBYzs7SUF3bUQzQixtQkFBQztDQUFBLEFBL21ERCxDQUtrQyxTQUFTLEdBMG1EMUM7U0ExbURZLFlBQVk7OztJQU12QixxQ0FBK0M7O0lBQy9DLHVDQUFpRDs7SUFDakQseUNBQW1EOztJQUNuRCx3Q0FBa0Q7O0lBQ2xELGdDQUF5Qzs7SUFDekMsa0NBQTRDOztJQUM1QywwQ0FBcUQ7O0lBQ3JELDRCQUF5RDs7SUFDekQseUJBQW9JOztJQUVwSSwwQ0FBK0Q7O0lBQy9ELGtDQUFvSjs7SUFDcEosb0NBQW9LOztJQWhCcEssb0NBQTREOzs7OztJQWlCNUQsZ0RBQXNDOzs7OztJQUN0QyxnREFBd0Q7Ozs7O0lBQ3hELDJDQUF1Qzs7Ozs7SUFDdkMsa0RBQXdDOztJQUV4QywwQ0FBa0M7O0lBQ2xDLHdDQUFnQzs7Ozs7SUFDaEMsNENBQTZDOzs7OztJQUM3Qyx1Q0FBd0M7Ozs7O0lBQ3hDLHFDQUFvQzs7Ozs7SUFDcEMsbUNBQThCOztJQUU5Qix3Q0FBZ0M7Ozs7O0lBQ2hDLDZDQUFvQzs7Ozs7SUFDcEMseUNBQThDOztJQUM5QyxzQ0FBc0I7Ozs7O0lBRXRCLDBDQUFnQzs7Ozs7SUFFaEMsMENBQWdDOzs7OztJQUNoQyxvQ0FBMEI7O0lBRTFCLHlDQUEwQzs7SUFDMUMsa0NBQW1DOzs7OztJQUVuQyxxQ0FBOEI7Ozs7O0lBQzlCLG1DQUE0Qjs7Ozs7SUFDNUIseUNBQStCOzs7OztJQUUvQixxQ0FBZ0M7Ozs7O0lBSWhDLGdDQUF1Qzs7Ozs7SUFJdkMsd0NBQXVDOzs7OztJQUN2QyxpREFBdUM7Ozs7O0lBQ3ZDLGtEQUF3Qzs7SUFDeEMsK0NBQThDOzs7OztJQUk5QyxpQ0FBdUI7O0lBQ3ZCLGdDQUFjOztJQTRrQmQscUNBQTRDOztJQUM1QyxtQ0FBMEM7O0lBQzFDLGtDQUF5Qzs7SUFDekMsMkNBQWdDOztJQUNoQyxzQ0FBMkI7O0lBQzNCLDRDQUFpQzs7SUFtMkJqQyxzQ0FBZTs7Ozs7SUErRWYsK0JBQXVCOztJQUN2QixtQ0FhQzs7SUFFRCxnQ0FRQzs7Ozs7SUFFRCxtQ0FNRTs7Ozs7SUF4K0NVLDRCQUF3Qjs7Ozs7SUFBRSxxQ0FBb0M7Ozs7O0lBQUUsaUNBQTRCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29tcG9uZW50LCBPbkluaXQsIEVsZW1lbnRSZWYsIFJlbmRlcmVyMiwgVmlld0NoaWxkLCBPdXRwdXQgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuaW1wb3J0IHsgQ29udGFpbmVyIH0gZnJvbSAnLi4vLi4vLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudCc7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tICcuLi8uLi8uLi91dGlscy9jb21tb24uc2VydmljZSc7XHJcblxyXG5pbXBvcnQgeyBTZXJpZXNTdHlsZVByb3ZpZGVyIH0gZnJvbSAnLi9zdHlsZS9TZXJpZXNTdHlsZVByb3ZpZGVyJztcclxuaW1wb3J0IHsgSFRUUENvbW1zIH0gZnJvbSAnLi4vLi4vLi4vY29tbXVuaWNhdGlvbi9odHRwY29tbXMuc2VydmljZSc7XHJcbmltcG9ydCB7IFN3dExhYmVsIH0gZnJvbSAnLi4vLi4vLi4vY29udHJvbHMvc3d0LWxhYmVsLmNvbXBvbmVudCc7XHJcbmltcG9ydCB7IElMTVRyZWVJbmRldGVybWluYXRlIH0gZnJvbSAnLi4vLi4vLi4vY29udHJvbHMvSUxNVHJlZUluZGV0ZXJtaW5hdGUnO1xyXG5pbXBvcnQgeyBtb21lbnQgfSBmcm9tICduZ3gtYm9vdHN0cmFwL2Nocm9ub3MvdGVzdC9jaGFpbic7XHJcbmltcG9ydCB7IEhhc2hNYXAgfSBmcm9tICcuLi8uLi8uLi91dGlscy9IYXNoTWFwLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBTd3RDb21ib0JveCB9IGZyb20gJy4uLy4uLy4uL2NvbnRyb2xzL3N3dC1jb21ib2JveC5jb21wb25lbnQnO1xyXG5pbXBvcnQgeyBTZXJpZXMgfSBmcm9tICcuL2NvbnRyb2wvU2VyaWVzJztcclxuaW1wb3J0IHsgRXh0ZXJuYWxJbnRlcmZhY2UgfSBmcm9tICcuLi8uLi8uLi91dGlscy9leHRlcm5hbC1pbnRlcmZhY2Uuc2VydmljZSc7XHJcbmltcG9ydCB7IFN0cmluZ1V0aWxzIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvc3RyaW5nLXV0aWxzLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBDaGVja0JveExlZ2VuZCB9IGZyb20gJy4vY29udHJvbC9DaGVja0JveExlZ2VuZCc7XHJcbmltcG9ydCB7IFZCb3ggfSBmcm9tICcuLi8uLi8uLi9jb250cm9scy9zd3QtdmJveC5jb21wb25lbnQnO1xyXG5pbXBvcnQgeyBJTE1TZXJpZXNMaXZlVmFsdWUgfSBmcm9tICcuL2NvbnRyb2wvSUxNU2VyaWVzTGl2ZVZhbHVlJztcclxuaW1wb3J0IHsgSEJveCB9IGZyb20gJy4uLy4uLy4uL2NvbnRyb2xzL3N3dC1oYm94LmNvbXBvbmVudCc7XHJcbmltcG9ydCB7IEFzc2V0c0xlZ2VuZCB9IGZyb20gJy4vY29udHJvbC9Bc3NldHNMZWdlbmQnO1xyXG5pbXBvcnQgeyBTZXJpZXNIaWdobGlnaHRFdmVudCB9IGZyb20gXCIuLi8uLi8uLi9ldmVudHMvc3d0LWV2ZW50cy5tb2R1bGVcIjtcclxuaW1wb3J0IHsgU3d0VXRpbCB9IGZyb20gJy4uLy4uLy4uL3V0aWxzL3N3dC11dGlsLnNlcnZpY2UnO1xyXG5pbXBvcnQgUmVzaXplT2JzZXJ2ZXIgZnJvbSAncmVzaXplLW9ic2VydmVyLXBvbHlmaWxsJztcclxuaW1wb3J0IHsgU3d0SUxNQ2hhcnQgfSBmcm9tICcuL2NvbnRyb2wvQ2hhcnQvU3d0SUxNQ2hhcnQnO1xyXG5pbXBvcnQgeyBIRGl2aWRlZEVuZFJlc2l6ZUV2ZW50LCBUYWJTZWxlY3RFdmVudCAsIERpdmlkZXJSZXNpemVDb21wbGV0ZSwgVkRpdmlkZWRFbmRSZXNpemVFdmVudH0gZnJvbSBcIi4uLy4uLy4uL2V2ZW50cy9zd3QtZXZlbnRzLm1vZHVsZVwiO1xyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcblxyXG5AQ29tcG9uZW50KHtcclxuICBzZWxlY3RvcjogJ0lMTUxpbmVDaGFydCcsXHJcbiAgdGVtcGxhdGVVcmw6ICcuL0lMTUxpbmVDaGFydC5odG1sJyxcclxuICBzdHlsZVVybHM6IFsnLi9JTE1MaW5lQ2hhcnQuY3NzJ11cclxufSlcclxuZXhwb3J0IGNsYXNzIElMTUxpbmVDaGFydCBleHRlbmRzIENvbnRhaW5lciBpbXBsZW1lbnRzIE9uSW5pdCB7XHJcbiAgLy8gQFZpZXdDaGlsZCgnaWZyYW1lQ29udGFpZXInKSBwdWJsaWMgaWZyYW1lQ29udGFpZXI6IEVsZW1lbnRSZWY7XHJcbiAgQFZpZXdDaGlsZCgnY2hhcnRFbGVtZW50JykgcHVibGljIGNoYXJ0RWxlbWVudDogU3d0SUxNQ2hhcnQ7XHJcbiAgLy8gcHVibGljIGlmcmFtZUNvbnRhaWVyO1xyXG5cclxuXHJcbiAgc3RhdGljIEFDQ1VNX0FDVFVBTF9JTkZMT1dfVEFHOiBzdHJpbmcgPSBcImFhY1wiO1xyXG4gIHN0YXRpYyBBQ0NVTV9BQ1RVQUxfT1VURkxPV1NfVEFHOiBzdHJpbmcgPSBcImFhZFwiO1xyXG4gIHN0YXRpYyBBQ0NVTV9GT1JFQ0FTVF9PVVRGTE9XU19UQUc6IHN0cmluZyA9IFwiYWZkXCI7XHJcbiAgc3RhdGljIEFDQ1VNX0ZPUkVDQVNUX0lORkxPV1NfVEFHOiBzdHJpbmcgPSBcImFmY1wiO1xyXG4gIHN0YXRpYyBBQ1RVQUxfQkFMQU5DRV9UQUc6IHN0cmluZyA9IFwiYWJcIjtcclxuICBzdGF0aWMgRk9SRUNBU1RfQkFMQU5DRV9UQUc6IHN0cmluZyA9IFwiZmJiXCI7XHJcbiAgc3RhdGljIEZPUkVDQVNUX0JBTF9JTkNfQUNUVUFMU19UQUc6IHN0cmluZyA9IFwiZmJpYVwiO1xyXG4gIHN0YXRpYyBUSFJFU0hPTERfVFlQRSA9IFtcIm1pbjFcIiwgXCJtaW4yXCIsIFwibWF4MVwiLCBcIm1heDJcIl07XHJcbiAgc3RhdGljIFNFUklFU19MSVNUID0gbmV3IEFycmF5KFwidGltZVNsb3RcIiwgXCJ0aW1lU2xvdEVcIiwgXCJhZmRcIiwgXCJhZmNcIiwgXCJhYWRcIiwgXCJhYWNcIiwgXCJhYlwiLCBcImZiYlwiLCBcImZiaWFcIixcInN1bUV4U09EXCIsXCJzdW1Gb3JjYXN0U09EXCIpO1xyXG4gIC8vIHN0YXRpYyBBUlJBWV9DT0xMRUNUSU9OX0ZST01fU0VSSUVTOkFycmF5Q29sbGVjdGlvbiA9IG5ldyBBcnJheUNvbGxlY3Rpb24oU0VSSUVTX0xJU1QpO1xyXG4gIHN0YXRpYyBBUlJBWV9DT0xMRUNUSU9OX0ZST01fU0VSSUVTID0gSUxNTGluZUNoYXJ0LlNFUklFU19MSVNUO1xyXG4gIHN0YXRpYyBMSVFVSURJVFlfWk9ORVNfTElTVCA9IFtcIlNVTV9DUkVESVRfTElORV9UT1RBTFwiLCBcIlNVTV9DT0xMQVRFUkFMXCIsIFwiU1VNX1VOX0xJUVVJRF9BU1NFVFNcIiwgXCJTVU1fT1RIRVJfVE9UQUxcIiwgXCJOT19NT1JFX0xJUVVJRElUWV9BVkFJTEFCTEVcIl07XHJcbiAgc3RhdGljIFNFUklFU19JR05PUkVfSElHSExJR0ggPSBbXCJTVU1fQ1JFRElUX0xJTkVfVE9UQUxcIiwgXCJTVU1fQ09MTEFURVJBTFwiLCBcIlNVTV9VTl9MSVFVSURfQVNTRVRTXCIsIFwiU1VNX09USEVSX1RPVEFMXCIsIFwiTk9fTU9SRV9MSVFVSURJVFlfQVZBSUxBQkxFXCIsIFwiVGhyZXNob2xkc1wiXTtcclxuICBwcml2YXRlIGxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlcyA9IFtdO1xyXG4gIHByaXZhdGUgbGlxdWlkaXR5Wm9uZXNMaXN0TGltaXRzOiBIYXNoTWFwID0gbmV3IEhhc2hNYXA7XHJcbiAgcHJpdmF0ZSBzZXJpZXNTdHlsZVByb3BlcnR5OiBIVFRQQ29tbXM7XHJcbiAgcHJpdmF0ZSBoaWdobGlnaHRlZExpcXVpZGl0eVNlcmllcyA9IFtdO1xyXG5cclxuICBwdWJsaWMgc2VsZWN0ZWRDdXJyZW5jeUlkOiBzdHJpbmc7XHJcbiAgcHVibGljIHNlbGVjdGVkRW50aXR5SWQ6IHN0cmluZztcclxuICBwcml2YXRlIF9hY2N1bXVsYXRlZERDTGVnZW5kOiBDaGVja0JveExlZ2VuZDtcclxuICBwcml2YXRlIF9iYWxhbmNlc0xlZ2VuZDogQ2hlY2tCb3hMZWdlbmQ7XHJcbiAgcHJpdmF0ZSBfYXNzZXRzTGVnZW5kOiBBc3NldHNMZWdlbmQ7XHJcbiAgcHJpdmF0ZSBfYWNjdW1MYWJlbDogU3d0TGFiZWw7XHJcblxyXG4gIHB1YmxpYyBzdG9wVXBkYXRlTGVnZW5kID0gZmFsc2U7XHJcbiAgcHJpdmF0ZSBfY2hhcnRWYWx1ZXNDb250YWluZXI6IEhCb3g7XHJcbiAgcHJpdmF0ZSBfdGltZUR5bmFtaWNWYWx1ZTogSUxNU2VyaWVzTGl2ZVZhbHVlO1xyXG4gIHB1YmxpYyBwYXJlbnREb2N1bWVudDtcclxuXHJcbiAgcHJpdmF0ZSBfdmlzaWJsZVRocmVzaG9sZHMgPSBbXTtcclxuXHJcbiAgcHJpdmF0ZSBOb1NPRF9EYXRhcHJvdmlkZXIgPSBbXTtcclxuICBwcml2YXRlIGRhdGFQcm92aWRlciA9IFtdO1xyXG5cclxuICBwdWJsaWMgaXNFbnRpdHlUaW1lZnJhbWU6IGJvb2xlYW4gPSBmYWxzZTtcclxuICBwdWJsaWMgaW5jbHVkZVNPRDogYm9vbGVhbiA9IGZhbHNlO1xyXG5cclxuICBwcml2YXRlIHRpbWVGcm9tQXNJbnQ6IG51bWJlcjtcclxuICBwcml2YXRlIHRpbWVUb0FzSW50OiBudW1iZXI7XHJcbiAgcHJpdmF0ZSBzdW1FeHRlcm5hbFNvZE1hcCA9IFtdO1xyXG5cclxuICBwcml2YXRlIF9iYWxhbmNlTGFiZWw6IFN3dExhYmVsO1xyXG5cclxuXHJcbiAgLy8gUmVmZWUgdG8gdGhlIElMTVRyZWVcclxuICBwcml2YXRlIF9pbG1UcmVlOiBJTE1UcmVlSW5kZXRlcm1pbmF0ZTtcclxuXHJcblxyXG5cclxuICBwcml2YXRlIGxvd2VzZXRBeGlzVmFsdWU6IE51bWJlciA9IE5hTjtcclxuICBwcml2YXRlIGxhc3RTZWxlY3RlZEdyb3VwRm9yWm9uZXMgPSBbXTtcclxuICBwcml2YXRlIGxhc3RTZWxlY3RTY2VuYXJpb0ZvclpvbmVzID0gW107XHJcbiAgcHVibGljIEpzb25MaXF1ZGl0aXlab25lc05PU09EOiBzdHJpbmcgPSBudWxsO1xyXG5cclxuXHJcblxyXG4gIHByaXZhdGUgX2pzb25EYXRhID0gW107XHJcbiAgZGF0YXNldHMgPSBbXTtcclxuXHJcbiAgcHVibGljIGdldCBiYWxhbmNlTGFiZWwoKTogU3d0TGFiZWwge1xyXG4gICAgcmV0dXJuIHRoaXMuX2JhbGFuY2VMYWJlbDtcclxuICB9XHJcbiAgcHVibGljIHNldCBiYWxhbmNlTGFiZWwodmFsdWU6IFN3dExhYmVsKSB7XHJcbiAgICB0aGlzLl9iYWxhbmNlTGFiZWwgPSB2YWx1ZTtcclxuICB9XHJcblxyXG4gIHB1YmxpYyBnZXQgY2hhcnRWYWx1ZXNDb250YWluZXIoKTogSEJveCB7XHJcbiAgICByZXR1cm4gdGhpcy5fY2hhcnRWYWx1ZXNDb250YWluZXI7XHJcbiAgfVxyXG4gIHB1YmxpYyBzZXQgY2hhcnRWYWx1ZXNDb250YWluZXIodmFsdWU6IEhCb3gpIHtcclxuICAgIHRoaXMuX2NoYXJ0VmFsdWVzQ29udGFpbmVyID0gdmFsdWU7XHJcbiAgfVxyXG4gIHB1YmxpYyBnZXQgdGltZUR5bmFtaWNWYWx1ZSgpOiBJTE1TZXJpZXNMaXZlVmFsdWUge1xyXG4gICAgcmV0dXJuIHRoaXMuX3RpbWVEeW5hbWljVmFsdWU7XHJcbiAgfVxyXG4gIHB1YmxpYyBzZXQgdGltZUR5bmFtaWNWYWx1ZSh2YWx1ZTogSUxNU2VyaWVzTGl2ZVZhbHVlKSB7XHJcbiAgICB0aGlzLl90aW1lRHluYW1pY1ZhbHVlID0gdmFsdWU7XHJcbiAgfVxyXG5cclxuXHJcblxyXG4gIHB1YmxpYyBnZXQganNvbkRhdGEoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5fanNvbkRhdGE7XHJcbiAgfVxyXG4gIHB1YmxpYyBzZXQganNvbkRhdGEodmFsdWUpIHtcclxuICAgIHRoaXMuX2pzb25EYXRhID0gdmFsdWU7XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgZ2V0IGFjY3VtTGFiZWwoKTogU3d0TGFiZWwge1xyXG4gICAgcmV0dXJuIHRoaXMuX2FjY3VtTGFiZWw7XHJcbiAgfVxyXG4gIHB1YmxpYyBzZXQgYWNjdW1MYWJlbCh2YWx1ZTogU3d0TGFiZWwpIHtcclxuICAgIHRoaXMuX2FjY3VtTGFiZWwgPSB2YWx1ZTtcclxuICB9XHJcblxyXG4gIHB1YmxpYyBnZXQgYWNjdW11bGF0ZWREQ0xlZ2VuZCgpOiBDaGVja0JveExlZ2VuZCB7XHJcbiAgICByZXR1cm4gdGhpcy5fYWNjdW11bGF0ZWREQ0xlZ2VuZDtcclxuICB9XHJcbiAgcHVibGljIHNldCBhY2N1bXVsYXRlZERDTGVnZW5kKHZhbHVlOiBDaGVja0JveExlZ2VuZCkge1xyXG4gICAgdGhpcy5fYWNjdW11bGF0ZWREQ0xlZ2VuZCA9IHZhbHVlO1xyXG4gIH1cclxuICBwdWJsaWMgZ2V0IGJhbGFuY2VzTGVnZW5kKCk6IENoZWNrQm94TGVnZW5kIHtcclxuICAgIHJldHVybiB0aGlzLl9iYWxhbmNlc0xlZ2VuZDtcclxuICB9XHJcbiAgcHVibGljIHNldCBiYWxhbmNlc0xlZ2VuZCh2YWx1ZTogQ2hlY2tCb3hMZWdlbmQpIHtcclxuICAgIHRoaXMuX2JhbGFuY2VzTGVnZW5kID0gdmFsdWU7XHJcbiAgfVxyXG4gIHB1YmxpYyBnZXQgYXNzZXRzTGVnZW5kKCk6IEFzc2V0c0xlZ2VuZCB7XHJcbiAgICByZXR1cm4gdGhpcy5fYXNzZXRzTGVnZW5kO1xyXG4gIH1cclxuICBwdWJsaWMgc2V0IGFzc2V0c0xlZ2VuZCh2YWx1ZTogQXNzZXRzTGVnZW5kKSB7XHJcbiAgICB0aGlzLl9hc3NldHNMZWdlbmQgPSB2YWx1ZTtcclxuICB9XHJcblxyXG5cclxuXHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtOiBFbGVtZW50UmVmLCBwcml2YXRlIGNvbW1vblNlcnZpY2U6IENvbW1vblNlcnZpY2UsIHByaXZhdGUgX3JlbmRlcmVyOiBSZW5kZXJlcjIpIHtcclxuICAgIHN1cGVyKGVsZW0sIGNvbW1vblNlcnZpY2UpO1xyXG4gICAgLy8gaWYgKHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKSB7XHJcbiAgICAvLyAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwibWVzc2FnZVwiLCB0aGlzLnJlY2VpdmVNZXNzYWdlLmJpbmQodGhpcyksIGZhbHNlKTtcclxuICAgIC8vIH0gZWxzZSB7XHJcbiAgICAvLyAgICAoPGFueT53aW5kb3cpLmF0dGFjaEV2ZW50KFwib25tZXNzYWdlXCIsIHRoaXMucmVjZWl2ZU1lc3NhZ2UuYmluZCh0aGlzKSk7XHJcbiAgICAvLyB9XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgcmVjZWl2ZU1lc3NhZ2UoKSB7XHJcbiAgfVxyXG5cclxuICAvKipcclxuXHRcdCAqIFJlbW92ZXMgY29tcGxldGVseSBhbGwgdGhlIHNlcmllcyBpbiB0aGUgY2hhcnRcclxuXHRcdCAqIFxyXG5cdFx0ICogKi9cclxuICBwdWJsaWMgcmVtb3ZlQWxsU2VyaWVzKCk6IHZvaWQge1xyXG5cclxuICAgIC8vIFJlbW92ZSBhbGwgdGhlIGNoYXJ0dmFsdWVzXHJcbiAgICAvLyBmb3IgZWFjaCAodmFyIGl0ZW06SUxNU2VyaWVzTGl2ZVZhbHVlIGluIGNoYXJ0VmFsdWVzQ29udGFpbmVyLmdldENoaWxkcmVuKCkpXHJcbiAgICAvLyB0aGlzLmNoYXJ0VmFsdWVzQ29udGFpbmVyLnJlbW92ZUNoaWxkKGl0ZW0pO1xyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNlcmllc0xpc3QuZ2V0VmFsdWVzKCkubGVuZ3RoOyBpKyspIHtcclxuICAgICAgbGV0IHNlcmllOiBTZXJpZXMgPSB0aGlzLnNlcmllc0xpc3QuZ2V0VmFsdWVzKClbaV07XHJcbiAgICAgIGlmIChzZXJpZS5zZXJpZXNMYWJlbCkge1xyXG4gICAgICAgIHNlcmllLnNlcmllc0xhYmVsLnJlbW92ZUxpdmVWYWx1ZSgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICAvLyBJbml0IHRoZSBzZXJpZXMgYXJyYXlcclxuICAgIHRoaXMuc2VyaWVzTGlzdCA9IG5ldyBIYXNoTWFwO1xyXG5cclxuICAgIHRoaXMuZGF0YVByb3ZpZGVyID0gW107XHJcbiAgICB0aGlzLk5vU09EX0RhdGFwcm92aWRlciA9IFtdO1xyXG5cclxuICAgIC8vIFJlbW92ZSBkYXRhc2V0cyBtZXRhZGF0YSBhcyB3ZWxsXHJcbiAgICB0aGlzLmRhdGFzZXRzID0gW107XHJcbiAgICAvLyBVcGRhdGUgdGhlIGFyZWEgYW5kIGxpbmUgbGVnZW5kcyBcclxuICAgIGlmICh0aGlzLl9hY2N1bXVsYXRlZERDTGVnZW5kKVxyXG4gICAgICB0aGlzLnVwZGF0ZUFyZWFMZWdlbmQoKTtcclxuICAgIGlmICh0aGlzLl9iYWxhbmNlc0xlZ2VuZClcclxuICAgICAgdGhpcy51cGRhdGVMaW5lTGVnZW5kKCk7XHJcbiAgICAvL3JlZnJlc2hIb3Jpem9udGFsQXhpcygpO1xyXG4gICAgLy9yZWZyZXNoVmVydGljYWxBeGlzKCk7XHJcbiAgICB0aGlzLmNhbGxNZXRob2RJbklmcmFtZShcImRlc3Ryb3lDaGFydFwiLCBbXSlcclxuICB9XHJcblxyXG5cclxuICBwdWJsaWMgcmVzZXRJTE1MaW5lQ2hhcnQoKTogdm9pZCB7XHJcbiAgICB0aGlzLmRhdGFQcm92aWRlciA9IFtdO1xyXG4gICAgdGhpcy5yZW1vdmVBbGxTZXJpZXMoKTtcclxuICAgIHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0VmFsdWVzID0gW107XHJcbiAgICB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdExpbWl0cy5jbGVhcigpO1xyXG4gICAgdGhpcy5zZXJpZXNMaXN0ID0gbmV3IEhhc2hNYXA7XHJcbiAgfVxyXG5cclxuXHJcbiAgLyoqXHJcblx0XHQgKiBBZGRzIGEgbmV3IENoYXJ0cyAoTGluZVNlcmllcyBvciBBcmVhU2VyaWVzKSBlbGVtZW50XHJcblx0XHQgKiAqL1xyXG4gIHB1YmxpYyBhZGRDaGFydCh5RmllbGQ6IHN0cmluZywgdmlzaWJsZTogYm9vbGVhbiA9IHRydWUpOiBib29sZWFuIHtcclxuICAgIGlmICh5RmllbGQuaW5kZXhPZihcIlRocmVzaG9sZHNcIikgPT0gLTEpXHJcbiAgICAgIHJldHVybiB0aGlzLmNyZWF0ZVNlcmllcyh5RmllbGQsIHZpc2libGUpO1xyXG4gICAgZWxzZSB7XHJcbiAgICAgIHJldHVybiB0aGlzLmNyZWF0ZVRocmVzaG9sZCh5RmllbGQpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcblx0XHQgKiBDcmVhdGVzIGEgdGhyZXNob2xkIGZyb20gY2FjaGVkIERUT1xyXG5cdFx0ICogKi9cclxuICBwdWJsaWMgY3JlYXRlVGhyZXNob2xkKHlGaWVsZDogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICB2YXIgZHRvOiBPYmplY3QgPSB0aGlzLmRhdGFzZXRzW3lGaWVsZF07XHJcbiAgICBpZiAoIWR0bykge1xyXG4gICAgICAvL3Rocm93IEVycm9yKFwiUHJvZ3JhbW1pbmcgZXJyb3I6IG1ldGhvZCBbdXBkYXRlRGF0YVNldHNdIHNob3VsZCBiZSBjYWxsZWQgZmlyc3QgIVwiKTtcdFxyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICB2YXIgdGhyZXNob2xkczogYW55ID0gZHRvW1widGhyZXNob2xkc1wiXTtcclxuICAgIHZhciBzdHlsZUNvbG9yOiBzdHJpbmcgPSBkdG9bXCJ0aHJlc2hvbGRzQ29sb3JcIl07XHJcbiAgICB2YXIgdGhyZWFzaG9sZFZhbHVlOiBzdHJpbmc7XHJcbiAgICAvLyBmb3IgZWFjaCAodmFyIGNoYXJ0OlhNTCBpbiB0aHJlc2hvbGRzLmNoaWxkcmVuKCkpe1xyXG4gICAgZm9yICh2YXIgY2hhcnQgaW4gdGhyZXNob2xkcykge1xyXG4gICAgICB0aHJlYXNob2xkVmFsdWUgPSAnJyArIHRocmVzaG9sZHNbY2hhcnRdO1xyXG4gICAgICAvLyB0aHJlYXNob2xkVmFsdWUgPSBjaGFydC52YWx1ZU9mKCk7XHJcbiAgICAgIGlmICh0aHJlYXNob2xkVmFsdWUuY2hhckF0KDApID09ICcuJykge1xyXG4gICAgICAgIHRocmVhc2hvbGRWYWx1ZSA9ICcwJyArIHRocmVhc2hvbGRWYWx1ZTtcclxuICAgICAgfVxyXG4gICAgICB0aGlzLmFkZFRocmVzaG9sZCh5RmllbGQsIGNoYXJ0LCB0aHJlYXNob2xkVmFsdWUsIHN0eWxlQ29sb3IpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfVxyXG5cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIGEgdGhyZXNob2xkXHJcbiAgICogKi9cclxuICBwcml2YXRlIGFkZFRocmVzaG9sZCh5RmllbGQ6IHN0cmluZywgbWlubWF4OiBzdHJpbmcsIHZhbHVlOiBhbnksIHN0eWxlOiBzdHJpbmcpOiB2b2lkIHtcdC8vIE5vIG5lZWQgdG8gcGxvdCB0aHJlc2hvbGQgaW4gdGhlc2UgY2FzZXMgXHJcbiAgICBpZiAoIXRoaXMuZGF0YVByb3ZpZGVyIHx8IHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aCA9PSAwIHx8IFN0cmluZyh2YWx1ZSkgPT0gXCJcIiB8fCBpc05hTih2YWx1ZSkpIHtcclxuICAgICAgaWYgKHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZSh5RmllbGQgKyBcIi5cIiArIG1pbm1heCkgIT0gbnVsbCkge1xyXG4gICAgICAgIHRoaXMuc2VyaWVzTGlzdC5yZW1vdmUoeUZpZWxkICsgXCIuXCIgKyBtaW5tYXgpO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIGVsc2Uge1xyXG5cclxuICAgICAgdmFsdWUgPSBOdW1iZXIodmFsdWUpO1xyXG4gICAgICB2YXIgbGluZVNlcmllczogU2VyaWVzID0gbmV3IFNlcmllcztcclxuICAgICAgbGluZVNlcmllcy5zZXJpZXNUeXBlID0gc3R5bGU7XHJcbiAgICAgIGxpbmVTZXJpZXMuZGlzcGxheU5hbWUgPSAoeUZpZWxkICsgXCIuXCIgKyBtaW5tYXgpO1xyXG5cclxuICAgICAgLy8gU2V0dGluZyB0aGUgbGluZVNlcmllcyB4ZmllbGQgYWNjb3JkaW5kIHRvIEVudGl0eVRpbWVGcmFtZSB2YWx1ZSBcclxuICAgICAgaWYgKHRoaXMuaXNFbnRpdHlUaW1lZnJhbWUpXHJcbiAgICAgICAgbGluZVNlcmllcy54RmllbGQgPSBcInRpbWVTbG90RVwiO1xyXG4gICAgICBlbHNlXHJcbiAgICAgICAgbGluZVNlcmllcy54RmllbGQgPSBcInRpbWVTbG90XCI7XHJcbiAgICAgIGxpbmVTZXJpZXMueUZpZWxkID0gKHlGaWVsZCArIFwiLlwiICsgbWlubWF4KTtcclxuXHJcbiAgICAgIC8vRklYTUU6Q0hFQ0sgSUYgTkVFREVEXHJcbiAgICAgIC8vIEFkZCBkdW1teSB2YWx1ZXMgZm9yIGZpcnN0IGFuZCBsYXN0IGRwIG9iamVjdHNcclxuICAgICAgLy8gdmFyIGZpcnN0Ok9iamVjdD10aGlzLmRhdGFQcm92aWRlclswXTtcclxuICAgICAgLy8gZmlyc3RbbGluZVNlcmllcy55RmllbGRdPSBpbmNsdWRlU09EID8gdmFsdWUgOiB2YWx1ZSAtIHN1bUV4dGVybmFsU29kTWFwLmdldFZhbHVlKHlGaWVsZC5zcGxpdChcIi5cIilbMF0pO1xyXG4gICAgICAvLyB2YXIgbGFzdDpPYmplY3Q9ZGF0YVByb3ZpZGVyLmdldEl0ZW1BdChkYXRhUHJvdmlkZXIubGVuZ3RoIC0gMSk7XHJcbiAgICAgIC8vIGxhc3RbbGluZVNlcmllcy55RmllbGRdPWluY2x1ZGVTT0QgPyB2YWx1ZSA6IHZhbHVlIC0gc3VtRXh0ZXJuYWxTb2RNYXAuZ2V0VmFsdWUoeUZpZWxkLnNwbGl0KFwiLlwiKVswXSk7XHJcblxyXG4gICAgICAvLyB2YXIgZmlyc3ROb1NPRDpPYmplY3Q9Tm9TT0RfRGF0YXByb3ZpZGVyLmdldEl0ZW1BdCgwKTtcclxuICAgICAgLy8gZmlyc3ROb1NPRFtsaW5lU2VyaWVzLnlGaWVsZF09aW5jbHVkZVNPRD8gdmFsdWUgLSBzdW1FeHRlcm5hbFNvZE1hcC5nZXRWYWx1ZSh5RmllbGQuc3BsaXQoXCIuXCIpWzBdKSA6IHZhbHVlIDtcclxuXHJcbiAgICAgIC8vIHZhciBsYXN0Tm9TT0Q6T2JqZWN0PU5vU09EX0RhdGFwcm92aWRlci5nZXRJdGVtQXQoZGF0YVByb3ZpZGVyLmxlbmd0aCAtIDEpO1xyXG4gICAgICAvLyBsYXN0Tm9TT0RbbGluZVNlcmllcy55RmllbGRdPWluY2x1ZGVTT0QgPyB2YWx1ZSAtIHN1bUV4dGVybmFsU29kTWFwLmdldFZhbHVlKHlGaWVsZC5zcGxpdChcIi5cIilbMF0pOiB2YWx1ZSA7XHJcblxyXG4gICAgICAvLyAvLyBfeUZpZWxkVmlzaWJsZVRocmVzaG9sZHMucHVzaChsaW5lU2VyaWVzLnlGaWVsZCk7XHJcbiAgICAgIC8vIEFycmF5Q29sbGVjdGlvbihkYXRhUHJvdmlkZXIpLnNldEl0ZW1BdChmaXJzdCwgMCk7XHJcbiAgICAgIC8vIEFycmF5Q29sbGVjdGlvbihkYXRhUHJvdmlkZXIpLnNldEl0ZW1BdChsYXN0LCBkYXRhUHJvdmlkZXIubGVuZ3RoIC0gMSk7XHJcbiAgICAgIC8vIEFycmF5Q29sbGVjdGlvbihOb1NPRF9EYXRhcHJvdmlkZXIpLnNldEl0ZW1BdChmaXJzdE5vU09ELCAwKTtcclxuICAgICAgLy8gQXJyYXlDb2xsZWN0aW9uKE5vU09EX0RhdGFwcm92aWRlcikuc2V0SXRlbUF0KGxhc3ROb1NPRCwgZGF0YVByb3ZpZGVyLmxlbmd0aCAtIDEpO1xyXG5cclxuICAgICAgdGhpcy5zZXJpZXNMaXN0LnB1dChsaW5lU2VyaWVzLnlGaWVsZCwgbGluZVNlcmllcyk7XHJcbiAgICAgIC8vIHRocmVzaG9sZHNTYW5pdHlDaGVjayhsaW5lU2VyaWVzLnlGaWVsZCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBSZW1vdmUgdGhlIHNlcmllcyB3aXRoIGl0cyBsZWdlbmRcclxuICAgKiAqL1xyXG4gIHJlbW92ZVNlcmllcyh5RmllbGQ6IHN0cmluZyk6IHZvaWQge1xyXG4gICAgaWYgKHlGaWVsZC5pbmRleE9mKFwiVGhyZXNob2xkc1wiKSAhPSAtMSkge1xyXG4gICAgICBmb3IgKGxldCB0eXBlIGluIElMTUxpbmVDaGFydC5USFJFU0hPTERfVFlQRSkge1xyXG4gICAgICAgIHRoaXMuc2VyaWVzTGlzdC5yZW1vdmUoeUZpZWxkICsgXCIuXCIgKyB0eXBlKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgZWxzZSB7XHJcbiAgICAgIC8vIHN1cGVyLnJlbW92ZVNlcmllcyh5RmllbGQpO1xyXG4gICAgICBjb25zdCBzZXJpZXM6IFNlcmllcyA9IHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZSh5RmllbGQpO1xyXG4gICAgICBpZiAoc2VyaWVzKSB7XHJcbiAgICAgICAgY29uc3Qgc2VyaWVzVHlwZSA9IHNlcmllcy5zZXJpZXNUeXBlO1xyXG4gICAgICAgIGlmIChzZXJpZXMuc2VyaWVzTGFiZWwpIHtcclxuICAgICAgICAgIHNlcmllcy5zZXJpZXNMYWJlbC5yZW1vdmVMaXZlVmFsdWUoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5zZXJpZXNMaXN0LnJlbW92ZSh5RmllbGQpO1xyXG4gICAgICAgIC8vIFJlbW92ZSB0aGUgbGVnZW5kXHJcbiAgICAgICAgaWYgKHNlcmllc1R5cGUgPT0gJ2xpbmUnKVxyXG4gICAgICAgICAgdGhpcy51cGRhdGVMaW5lTGVnZW5kKCk7XHJcbiAgICAgICAgZWxzZVxyXG4gICAgICAgICAgdGhpcy51cGRhdGVBcmVhTGVnZW5kKCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYW5kIGFkZHMgYSBsaW5lL2FyZWEgU2VyaWVzIHRvIGxpbmUgY2hhcnQgYmFzZWQgb24gaXRzIHlGaWVsZFxyXG4gICAqICovXHJcbiAgcHVibGljIGNyZWF0ZVNlcmllcyh5RmllbGQ6IHN0cmluZywgdmlzaWJsZTogYm9vbGVhbiA9IHRydWUpOiBib29sZWFuIHtcclxuICAgIHRyeSB7XHJcblxyXG5cclxuICAgICAgdmFyIGFyciA9IHlGaWVsZC5zcGxpdChcIi5cIik7XHJcbiAgICAgIHZhciBncm91cElkOiBzdHJpbmcgPSBhcnJbMF07XHJcbiAgICAgIHZhciBlbnRpdHlJZDogc3RyaW5nID0gdGhpcy5zZWxlY3RlZEVudGl0eUlkO1xyXG4gICAgICB2YXIgY3VycmVuY3lJZDogc3RyaW5nID0gdGhpcy5zZWxlY3RlZEN1cnJlbmN5SWQ7XHJcbiAgICAgIHZhciBncm91cElkOiBzdHJpbmcgPSBhcnJbMF07XHJcbiAgICAgIHZhciBzY2VuYXJpb0lkOiBzdHJpbmcgPSBhcnJbMV07XHJcbiAgICAgIHZhciBkYXRhRWxlbWVudDogc3RyaW5nID0gYXJyWzJdO1xyXG4gICAgICB2YXIgZHRvOiBPYmplY3QgPSB0aGlzLmRhdGFzZXRzW2dyb3VwSWQgKyAnLicgKyBzY2VuYXJpb0lkXTtcclxuXHJcbiAgICAgIGlmICghZHRvKSB7XHJcbiAgICAgICAgLy90cmFjZShcIkNoYXJ0IG5vdCBleGlzdCBhbmQgdG8gcGxvdCBpdCBuZWVkIHRvIHNlbmQgcmVxdWVzdCB0byBEQiBzaWRlXCIpO1xyXG4gICAgICAgIC8vdGhyb3cgRXJyb3IoXCJQcm9ncmFtbWluZyBlcnJvcjogbWV0aG9kIFt1cGRhdGVEYXRhU2V0c10gc2hvdWxkIGJlIGNhbGxlZCBmaXJzdCAhXCIpO1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG5cclxuXHJcblxyXG4gICAgICB2YXIgbWV0YWRhdGE6IGFueSA9IGR0b1snY2hhcnRzJ107XHJcbiAgICAgIGlmIChjdXJyZW5jeUlkICE9IG1ldGFkYXRhLmN1cnJlbmN5SWQgfHwgZW50aXR5SWQgIT0gbWV0YWRhdGEuZW50aXR5SWQpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgICAgdmFyIF95RmllbGQ6IHN0cmluZyA9IFwiXCI7XHJcblxyXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1ldGFkYXRhLmNoYXJ0Lmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgLy8gLy8gTG9vcCBvbiBhbGwgcmVjZWl2ZWQgZGF0YXNldHNcclxuICAgICAgICBsZXQgY2hhcnQgPSBtZXRhZGF0YS5jaGFydFtpXTtcclxuICAgICAgICBpZiAoY2hhcnQuZGF0YWVsZW1lbnQgPT0gZGF0YUVsZW1lbnQpIHtcclxuICAgICAgICAgIC8vIE5vdyBjcmVhdGUgdGhlIHRpbWVzZXJpZXMgKGFyZWEgb3IgbGluZSlcclxuICAgICAgICAgIHZhciBjaGFydFR5cGU6IHN0cmluZyA9IGNoYXJ0LnR5cGU7XHJcbiAgICAgICAgICBfeUZpZWxkID0gKGdyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIikgKyBjaGFydC5kYXRhZWxlbWVudDtcclxuXHJcbiAgICAgICAgICAvKiBpZiB0aGUgc2VyaWVzIGFscmVhZHkgZXhpc3QsIGNoYW5nZSBvbmx5IHRoZWlyIHN0eWxlcyBpZiBoYXZlIGJlZW4gY2hhbmdlZFxyXG4gICAgICAgICAgb25jZSB0aGUgZGF0YSBjaGFuZ2VkICovXHJcbiAgICAgICAgICAvLyB2YXIgc2VyaWVzOlNlcmllcyA9IHRoaXMuZ2V0U2VyaWVzQnlZRmllbGQoeUZpZWxkKTtcclxuICAgICAgICAgIHZhciBzZXJpZXM6IFNlcmllcyA9IHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZSh5RmllbGQpO1xyXG4gICAgICAgICAgaWYgKHNlcmllcykge1xyXG4gICAgICAgICAgICAvLyBpZiAoc2VyaWVzIGlzIEN1c3RvbUFyZWFTZXJpZXMpXHJcbiAgICAgICAgICAgIGlmIChzZXJpZXMuc2VyaWVzVHlwZSA9PSAnYXJlYScpIHtcclxuICAgICAgICAgICAgICAvLyB2YXIgYXJlYTpDdXN0b21BcmVhU2VyaWVzID0gc2VyaWVzIGFzIEN1c3RvbUFyZWFTZXJpZXM7XHJcbiAgICAgICAgICAgICAgdmFyIHNlcmllc1N0eWxlOiBzdHJpbmcgPSBzZXJpZXMuYXBwbGllZFN0eWxlO1xyXG4gICAgICAgICAgICAgIGlmIChjaGFydC5zZXJpZXNTdHlsZSAhPSBzZXJpZXNTdHlsZSkge1xyXG4gICAgICAgICAgICAgICAgc2VyaWVzLmFwcGxpZWRTdHlsZSA9IGNoYXJ0LnNlcmllc1N0eWxlO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICB2YXIgc2VyaWVzU3R5bGU6IHN0cmluZyA9IHNlcmllcy5hcHBsaWVkU3R5bGU7XHJcbiAgICAgICAgICAgICAgaWYgKGNoYXJ0LnNlcmllc1N0eWxlICE9IHNlcmllc1N0eWxlKSB7XHJcbiAgICAgICAgICAgICAgICBzZXJpZXMuYXBwbGllZFN0eWxlID0gY2hhcnQuc2VyaWVzU3R5bGU7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgaWYgKGNoYXJ0VHlwZSA9PSBcImFyZWFcIikge1xyXG4gICAgICAgICAgICAgIHZhciBhcmVhU2VyaWVzOiBTZXJpZXMgPSBuZXcgU2VyaWVzO1xyXG4gICAgICAgICAgICAgIGFyZWFTZXJpZXMuYXBwbGllZFN0eWxlID0gY2hhcnQuc2VyaWVzU3R5bGU7XHJcbiAgICAgICAgICAgICAgLy8gYXJlYVNlcmllcy5kaXNwbGF5TmFtZSA9IEV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2V2YWwnLCAnbGFiZWxbXFwndGV4dFxcJ11bXFwnJyArIGNoYXJ0LmRhdGFlbGVtZW50ICsgJ1xcJ10nKTtcclxuICAgICAgICAgICAgICBhcmVhU2VyaWVzLmRpc3BsYXlOYW1lID0gRXh0ZXJuYWxJbnRlcmZhY2UuY2FsbCgnZ2V0QnVuZGxlJywgJ3RleHQnLCBjaGFydC5kYXRhZWxlbWVudCwgY2hhcnQuZGF0YWVsZW1lbnQpO1xyXG4gICAgICAgICAgICAgIGlmICh0aGlzLmlzRW50aXR5VGltZWZyYW1lKVxyXG4gICAgICAgICAgICAgICAgYXJlYVNlcmllcy54RmllbGQgPSBcInRpbWVTbG90RVwiO1xyXG4gICAgICAgICAgICAgIGVsc2VcclxuICAgICAgICAgICAgICAgIGFyZWFTZXJpZXMueEZpZWxkID0gXCJ0aW1lU2xvdFwiO1xyXG4gICAgICAgICAgICAgIC8vU2V0IHRvb2x0aXAgZm9yIGxlZ2VuZCBpbiB0aGlzIHN0YW5hZGFyZCA6VG9vbHRpcDogPERhdGFzZXQgZGVzY3JpcHRpb24+LjxzY2VuYXJpbyBuYW1lPiA8R3JvdXAgbmFtZT5cdFx0RS5nLiAgXCJGb3JlY2FzdCBPdXRmbG93LlNjZW5hcmlvIDEuR2xvYmFsIEV1cm9cIlxyXG4gICAgICAgICAgICAgIC8vIGFyZWFTZXJpZXMubGVnZW5kVG9vbHRpcCA9IEV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2V2YWwnLCAnbGFiZWxbXFwndGV4dFxcJ11bXFwnJyArIGNoYXJ0LmRhdGFlbGVtZW50ICsgJ1xcJ10nKSArIFwiLlwiICsgY2hhcnQubGVnZW5kVG9vbHRpcDtcclxuICAgICAgICAgICAgICBhcmVhU2VyaWVzLmxlZ2VuZFRvb2x0aXAgPSBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdnZXRCdW5kbGUnLCAndGV4dCcsIGNoYXJ0LmRhdGFlbGVtZW50LCBjaGFydC5kYXRhZWxlbWVudCkgKyBcIi5cIiArIGNoYXJ0LmxlZ2VuZFRvb2x0aXA7XHJcblxyXG4gICAgICAgICAgICAgIGFyZWFTZXJpZXMueUZpZWxkID0gX3lGaWVsZDtcclxuICAgICAgICAgICAgICBhcmVhU2VyaWVzLmxlZ2VuZERpc3BsYXlOYW1lID0gY2hhcnQuY2hhcnRJZDtcclxuICAgICAgICAgICAgICBhcmVhU2VyaWVzLnZpc2libGUgPSB2aXNpYmxlO1xyXG4gICAgICAgICAgICAgIGFyZWFTZXJpZXMuc2VyaWVzVHlwZSA9ICdhcmVhJztcclxuICAgICAgICAgICAgICB0aGlzLnNlcmllc0xpc3QucHV0KF95RmllbGQsIGFyZWFTZXJpZXMpXHJcbiAgICAgICAgICAgICAgLy8gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgdGhpcy51cGRhdGVBcmVhTGVnZW5kKHRydWUpO1xyXG4gICAgICAgICAgICAgIC8vIH0sIDApO1xyXG5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAvLyB2YXIgbGluZVNlcmllczpJTE1DdXN0b21MaW5lU2VyaWVzID0gbmV3IElMTUN1c3RvbUxpbmVTZXJpZXMoKTsgXHRcclxuICAgICAgICAgICAgICB2YXIgbGluZVNlcmllczogU2VyaWVzID0gbmV3IFNlcmllcztcclxuICAgICAgICAgICAgICAvLyBTZXJpZXNTdHlsZVByb3ZpZGVyLmFwcGx5TGluZVNlcmllc1N0eWxlKGxpbmVTZXJpZXMsY2hhcnQuQHNlcmllc1N0eWxlKTtcclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLmFwcGxpZWRTdHlsZSA9IGNoYXJ0LnNlcmllc1N0eWxlO1xyXG4gICAgICAgICAgICAgIC8vIGxpbmVTZXJpZXMuZGlzcGxheU5hbWUgPSBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdldmFsJywgJ2xhYmVsW1xcJ3RleHRcXCddW1xcJycgKyBjaGFydC5kYXRhZWxlbWVudCArICdcXCddJyk7XHJcbiAgICAgICAgICAgICAgbGluZVNlcmllcy5kaXNwbGF5TmFtZSA9IEV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2dldEJ1bmRsZScsICd0ZXh0JywgY2hhcnQuZGF0YWVsZW1lbnQsIGNoYXJ0LmRhdGFlbGVtZW50KTtcclxuICAgICAgICAgICAgICBpZiAodGhpcy5pc0VudGl0eVRpbWVmcmFtZSlcclxuICAgICAgICAgICAgICAgIGxpbmVTZXJpZXMueEZpZWxkID0gXCJ0aW1lU2xvdEVcIjtcclxuICAgICAgICAgICAgICBlbHNlXHJcbiAgICAgICAgICAgICAgICBsaW5lU2VyaWVzLnhGaWVsZCA9IFwidGltZVNsb3RcIjtcclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLnlGaWVsZCA9IF95RmllbGQ7XHJcbiAgICAgICAgICAgICAgLy9GSVhNRTpDSEVDSyB1dGlsaXR5XHJcbiAgICAgICAgICAgICAgLy8gbGluZVNlcmllcy5pbnRlcnBvbGF0ZVZhbHVlcyA9IHRydWU7XHJcblxyXG4gICAgICAgICAgICAgIC8vU2V0IHRvb2x0aXAgZm9yIGxlZ2VuZCBpbiB0aGlzIHN0YW5hZGFyZCA6VG9vbHRpcDogPERhdGFzZXQgZGVzY3JpcHRpb24+LjxzY2VuYXJpbyBuYW1lPiA8R3JvdXAgbmFtZT5cdFx0RS5nLiAgXCJGb3JlY2FzdCBPdXRmbG93LlNjZW5hcmlvIDEuR2xvYmFsIEV1cm9cIlxyXG4gICAgICAgICAgICAgIC8vIGxpbmVTZXJpZXMubGVnZW5kVG9vbHRpcCA9IEV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2V2YWwnLCAnbGFiZWxbXFwndGV4dFxcJ11bXFwnJyArIGNoYXJ0LmRhdGFlbGVtZW50ICsgJ1xcJ10nKSArIFwiLlwiICsgY2hhcnQubGVnZW5kVG9vbHRpcDtcclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLmxlZ2VuZFRvb2x0aXAgPSBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdnZXRCdW5kbGUnLCAndGV4dCcsIGNoYXJ0LmRhdGFlbGVtZW50LCBjaGFydC5kYXRhZWxlbWVudCkgKyBcIi5cIiArIGNoYXJ0LmxlZ2VuZFRvb2x0aXA7XHJcblxyXG4gICAgICAgICAgICAgIC8vIGxpbmVTZXJpZXMudmVydGljYWxBeGlzID0gdkF4aXNMZWZ0O1x0XHRcdFx0XHRcdFxyXG4gICAgICAgICAgICAgIGxpbmVTZXJpZXMubGVnZW5kRGlzcGxheU5hbWUgPSBjaGFydC5jaGFydElkO1xyXG5cclxuXHJcbiAgICAgICAgICAgICAgLyogXHRBZGQgdGhlIHJlbmRlcmVyIGluIHRoZSBjaGFydCB2YWx1ZXMgY29udGFpbmVyIHdoaWNoIHdpbGwgY29udGFpbiB0aGUgbGl2ZSB2YWx1ZSB3aGVuIG1vdmluZyBcclxuICAgICAgICAgICAgICB0aGUgbW91c2UgaW4gdGhlIGNoYXJ0ICovXHJcbiAgICAgICAgICAgICAgdmFyIHNlcmllc0xhYmVsID0gPElMTVNlcmllc0xpdmVWYWx1ZT50aGlzLmNoYXJ0VmFsdWVzQ29udGFpbmVyLmFkZENoaWxkKElMTVNlcmllc0xpdmVWYWx1ZSk7XHJcbiAgICAgICAgICAgICAgLy8gdmFyIHNlcmllc0xhYmVsOklMTVNlcmllc0xpdmVWYWx1ZSA9IG5ldyBJTE1TZXJpZXNMaXZlVmFsdWUoKTtcclxuICAgICAgICAgICAgICAvLyB0aGUgaWQgb2YgdGhlIHNlcmllIFxyXG4gICAgICAgICAgICAgIHNlcmllc0xhYmVsLnNlcmllc0lkID0gbGluZVNlcmllcy55RmllbGQ7XHJcbiAgICAgICAgICAgICAgLy8gdGhlIHZhbHVlIGlzIHNldCB0byBlbXB0eSBvbiB0aGUgZmlyc3QgYWRkXHJcbiAgICAgICAgICAgICAgaWYgKHRoaXMucGFyZW50RG9jdW1lbnQuY3VycmVuY3lGb3JtYXQgPT0gJ2N1cnJlbmN5UGF0MScpIHtcclxuICAgICAgICAgICAgICAgIHNlcmllc0xhYmVsLnNlcmllc1ZhbHVlID0gXCIwLjAwXCI7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHNlcmllc0xhYmVsLnNlcmllc1ZhbHVlID0gXCIwLDAwXCI7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHNlcmllc0xhYmVsLnNlcmllc1N0eWxlID0gY2hhcnQuc2VyaWVzU3R5bGU7XHJcbiAgICAgICAgICAgICAgLy8gdGhlIGNlcmNsZSBmaWxsIGZvciB0aGUgcmVuZGVyZXJcclxuICAgICAgICAgICAgICAvLyBzZXJpZXNMYWJlbC5jZXJjbGVGaWxsID0gY2VyY2xlRmlsbDtcclxuICAgICAgICAgICAgICAvLyBXZSBoYXZlIGFsd2F5cyB0aGUgY2VyY2xlIGZvciB0aGUgc2VyaWVzIGxhYmVsIFxyXG4gICAgICAgICAgICAgIHNlcmllc0xhYmVsLmlzVGltZUxpdmVJdGVtID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgLy8gU2V0IHRoZSBuZXcgZGVmaW5lZCByZW5kZXJlciB0byB0aGUgcmVxdWVzdGVkIGxpbmUgc2VyaWVzXHJcbiAgICAgICAgICAgICAgbGluZVNlcmllcy5zZXJpZXNMYWJlbCA9IHNlcmllc0xhYmVsO1xyXG4gICAgICAgICAgICAgIGlmICghdmlzaWJsZSkge1xyXG4gICAgICAgICAgICAgICAgbGluZVNlcmllcy5zZXJpZXNMYWJlbC52aXNpYmxlID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICBsaW5lU2VyaWVzLnNlcmllc0xhYmVsLmluY2x1ZGVJbkxheW91dCA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLnZpc2libGUgPSB2aXNpYmxlO1xyXG4gICAgICAgICAgICAgIGxpbmVTZXJpZXMuc2VyaWVzVHlwZSA9ICdsaW5lJztcclxuICAgICAgICAgICAgICAvLyBBZGQgdGhlIHJlbmRlcmVyIHRvIHRoZSBjb250YWluZXIgaWYgaXQgaXMgdGhlIGZpcnN0IGFkZCBvZiB0aGUgbGluZSBzZXJpZXNcclxuICAgICAgICAgICAgICB0aGlzLnNlcmllc0xpc3QucHV0KF95RmllbGQsIGxpbmVTZXJpZXMpXHJcblxyXG4gICAgICAgICAgICAgIC8vIH1cclxuICAgICAgICAgICAgICBpZiAoIXZpc2libGUpIHtcclxuICAgICAgICAgICAgICAgIGxpbmVTZXJpZXMuc2VyaWVzTGFiZWwudmlzaWJsZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvLyB1cGRhdGVMaW5lTGVnZW5kIG5lZWRzIHRvIGJlIGNhbGxlZCB0d2ljZSB0byB2YWxpZGF0ZSBwcm9wZXJ0aWVzXHJcbiAgICAgICAgICAgICAgdGhpcy51cGRhdGVMaW5lTGVnZW5kKHRydWUpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSBjYXRjaCAoZSkge1xyXG5cclxuXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICpcclxuICogUmVtb3ZlIGEgZ3JvdXAgZnJvbSBsaXN0IG9mIGRhdGEgc2V0cyBcclxuICogXHJcbiAqKi9cclxuICBwdWJsaWMgcmVtb3ZlRnJvbURhdGFTZXQoZ3JvdXBJZDogc3RyaW5nKTogdm9pZCB7XHJcbiAgICBmb3IgKGNvbnN0IGtleSBpbiB0aGlzLmRhdGFzZXRzKSB7XHJcbiAgICAgIGlmIChrZXkuaW5kZXhPZihncm91cElkKSAhPSAtMSkge1xyXG4gICAgICAgIGRlbGV0ZSB0aGlzLmRhdGFzZXRzW2tleV1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcblxyXG4gIC8qKlxyXG4gICAqIERyYXdzIHNvcnRlZCBsZWdlbmQgaXRlbXMgZm9yIGxpbmUgc2VyaWVzIFxyXG4gICAqICovXHJcbiAgcHVibGljIHVwZGF0ZUxpbmVMZWdlbmQocmVjYWxsOiBib29sZWFuID0gZmFsc2UpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5zdG9wVXBkYXRlTGVnZW5kKSB7XHJcbiAgICAgIHZhciBpc0VtcHR5OiBib29sZWFuID0gdHJ1ZTtcclxuICAgICAgdmFyIHRtcCA9IFtdO1xyXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZXMoKS5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgIGxldCBzZXJpZTogU2VyaWVzID0gdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpW2ldO1xyXG4gICAgICAgIGlmIChzZXJpZSAmJiBzZXJpZS5zZXJpZXNUeXBlID09PSAnbGluZScgJiYgc2VyaWUueUZpZWxkLmluZGV4T2YoXCJOT19NT1JFX0xJUVVJRElUWV9BVkFJTEFCTEVcIikgPT0gLTEpIHtcclxuICAgICAgICAgIGlzRW1wdHkgPSBmYWxzZTtcclxuICAgICAgICAgIHRtcC5wdXNoKHNlcmllKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgdGhpcy5fYmFsYW5jZXNMZWdlbmQuZGF0YVByb3ZpZGVyID0gdG1wO1xyXG5cclxuXHJcbiAgICAgIGlmIChpc0VtcHR5KSB7XHJcbiAgICAgICAgdGhpcy5iYWxhbmNlTGFiZWwudmlzaWJsZSA9IGZhbHNlO1xyXG4gICAgICAgIHRoaXMuYmFsYW5jZUxhYmVsLmluY2x1ZGVJbkxheW91dCA9IGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICAgIGVsc2Uge1xyXG4gICAgICAgIHRoaXMuYmFsYW5jZUxhYmVsLnZpc2libGUgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYmFsYW5jZUxhYmVsLmluY2x1ZGVJbkxheW91dCA9IHRydWU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIHVwZGF0ZUxpbmVMZWdlbmQgbmVlZHMgdG8gYmUgY2FsbGVkIHR3aWNlIHRvIHZhbGlkYXRlIHByb3BlcnRpZXNcclxuICAgICAgaWYgKHJlY2FsbClcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIHRoaXMudXBkYXRlTGluZUxlZ2VuZChmYWxzZSk7XHJcbiAgICAgICAgfSwgMCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuXHRcdCAqIFVwZGF0ZXMgdGhlIHRocmVzaG9sZCBjb2xvciB3aGVuIHNlcmllcyBjb2xvciBjaGFuZ2VzXHJcblx0XHQgKiAqL1xyXG4gIC8vRklYTUU6IG5lZWQgdHAgYmUgZHAsZVxyXG4gIHB1YmxpYyB1cGRhdGVUaHJlc2hvbGRTdHlsZUNvbG9yKGdyb3VwSWQ6IHN0cmluZywgc3R5bGVOYW1lOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIHZhciBkdG86IE9iamVjdCA9IHRoaXMuZGF0YXNldHNbZ3JvdXBJZC5jb25jYXQoXCIuVGhyZXNob2xkc1wiKV07XHJcbiAgICBpZiAoZHRvKSB7XHJcbiAgICAgIGR0b1tcInRocmVzaG9sZHNDb2xvclwiXSA9IHN0eWxlTmFtZTtcclxuICAgICAgdmFyIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5nZXRTdHlsZUNvbG9yKHN0eWxlTmFtZSk7XHJcbiAgICAgIGZvciAobGV0IHR5cGUgaW4gSUxNTGluZUNoYXJ0LlRIUkVTSE9MRF9UWVBFKSB7XHJcblxyXG4gICAgICAgIHZhciB5RmllbGQ6IHN0cmluZyA9IGdyb3VwSWQuY29uY2F0KFwiLlRocmVzaG9sZHMuXCIpLmNvbmNhdCh0eXBlKTtcclxuICAgICAgICB2YXIgdGhyZXNob2xkU2VyaWVzOiBTZXJpZXMgPSB0aGlzLnNlcmllc0xpc3QuZ2V0VmFsdWUoeUZpZWxkKTtcclxuICAgICAgICBpZiAodGhyZXNob2xkU2VyaWVzKSB7XHJcblxyXG4gICAgICAgICAgLy8gdmFyIHRoZXJlc2hvbGRTaGFkb3c6IHVpbnQgPSAwWEZGOEMwMDtcclxuICAgICAgICAgIC8vIC8vR2xvdyBlZmZlY3QgYXR0cmlidXRlc1xyXG4gICAgICAgICAgLy8gdmFyIGFscGhhOiBOdW1iZXIgPSAwLjM7XHJcbiAgICAgICAgICAvLyB2YXIgYmx1clg6IE51bWJlciA9IDY7XHJcbiAgICAgICAgICAvLyB2YXIgYmx1clk6IE51bWJlciA9IDI7XHJcbiAgICAgICAgICAvLyB2YXIgc3RyZW5ndGg6IE51bWJlciA9IDEwMDtcclxuICAgICAgICAgIC8vIHZhciBxdWFsaXR5OiBOdW1iZXIgPSAxO1xyXG4gICAgICAgICAgLy8gLy8gc2V0IGNvbG9yIHRvIHJlZCBpZiB0aGUgdGhyZXNob2xkIGlzIG1heDIgb3IgbWluIDJcclxuICAgICAgICAgIC8vIGlmICh0eXBlLmluZGV4T2YoXCIyXCIpICE9IC0xKSB7XHJcbiAgICAgICAgICAvLyAgIHRoZXJlc2hvbGRTaGFkb3cgPSAweEZGMDAwMDtcclxuICAgICAgICAgIC8vIH1cclxuICAgICAgICAgIC8vIHZhciBzZXJpZXNTdHlsZTogU2VyaWVzU3R5bGUgPSBTZXJpZXNTdHlsZVByb3ZpZGVyLmRyYXdEYXNoZWRMaW5lU2VyaWVzU3R5bGUoMC41LCBjb2xvcik7XHJcbiAgICAgICAgICAvLyBTZXJpZXNTdHlsZVByb3ZpZGVyLnNldExpbmVTZXJpZXNTdHlsZSh0aHJlc2hvbGRTZXJpZXMsIHNlcmllc1N0eWxlKTtcclxuICAgICAgICAgIC8vIC8vQWRkIGdsb3cgZWZmZWN0IHRvIHRocmVzaG9sZHNcclxuICAgICAgICAgIC8vIHZhciBnbG93RmlsdGVyOiBHbG93RmlsdGVyID0gbmV3IEdsb3dGaWx0ZXIodGhlcmVzaG9sZFNoYWRvdywgYWxwaGEsIGJsdXJYLCBibHVyWSwgc3RyZW5ndGgsIHF1YWxpdHkpO1xyXG4gICAgICAgICAgLy8gdGhyZXNob2xkU2VyaWVzLmZpbHRlcnMgPSBbZ2xvd0ZpbHRlcl07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuKiBEcmF3cyBzb3J0ZWQgbGVnZW5kIGl0ZW1zIGZvciBhcmVhIHNlcmllcyBcclxuKiAqL1xyXG4gIHB1YmxpYyB1cGRhdGVBcmVhTGVnZW5kKHJlY2FsbDogYm9vbGVhbiA9IGZhbHNlKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMuc3RvcFVwZGF0ZUxlZ2VuZCkge1xyXG5cclxuICAgICAgdmFyIGlzRW1wdHk6IGJvb2xlYW4gPSB0cnVlO1xyXG4gICAgICB2YXIgdG1wID0gW107XHJcbiAgICAgIC8vIGZvciAodmFyIGk6aW50ID0gMCA7IGkgPCB0aGlzLnNlcmllcy5sZW5ndGggOyBpKyspIFxyXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZXMoKS5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgIGxldCBzZXJpZTogU2VyaWVzID0gdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpW2ldO1xyXG4gICAgICAgIGlmIChzZXJpZS5zZXJpZXNUeXBlID09ICdhcmVhJykge1xyXG4gICAgICAgICAgLy8gdmFyIGFyZWFTZXJpZTpDdXN0b21BcmVhU2VyaWVzID0gXHQodGhpcy5zZXJpZXNbaV0gYXMgQ3VzdG9tQXJlYVNlcmllcyk7XHJcbiAgICAgICAgICBpZiAoU3RyaW5nVXRpbHMuaXNFbXB0eShzZXJpZS5taW5GaWVsZCkpIHtcclxuICAgICAgICAgICAgaXNFbXB0eSA9IGZhbHNlO1xyXG4gICAgICAgICAgICB0bXAucHVzaChzZXJpZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIHRoaXMuX2FjY3VtdWxhdGVkRENMZWdlbmQuZGF0YVByb3ZpZGVyID0gdG1wO1xyXG5cclxuICAgICAgLy8gdXBkYXRlQXJlYUxlZ2VuZCBuZWVkcyB0byBiZSBjYWxsZWQgdHdpY2UgdG8gdmFsaWRhdGUgcHJvcGVydGllc1xyXG4gICAgICBpZiAocmVjYWxsKVxyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgdGhpcy51cGRhdGVBcmVhTGVnZW5kKGZhbHNlKTtcclxuICAgICAgICB9LCAwKTtcclxuXHJcbiAgICAgIGlmIChpc0VtcHR5KSB7XHJcbiAgICAgICAgdGhpcy5hY2N1bUxhYmVsLnZpc2libGUgPSBmYWxzZTtcclxuICAgICAgICB0aGlzLmFjY3VtTGFiZWwuaW5jbHVkZUluTGF5b3V0ID0gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgICAgZWxzZSB7XHJcbiAgICAgICAgdGhpcy5hY2N1bUxhYmVsLnZpc2libGUgPSB0cnVlO1xyXG4gICAgICAgIHRoaXMuYWNjdW1MYWJlbC5pbmNsdWRlSW5MYXlvdXQgPSB0cnVlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuXHRcdCAqIERyYXdzIHNvcnRlZCBsZWdlbmQgaXRlbXMgZm9yIGFyZWEgc2VyaWVzXHJcblx0XHQgKiAqL1xyXG4gIHB1YmxpYyB1cGRhdGVMaXF1aWRpdHlab25lc0xlZ2VuZChyZWNhbGw6IGJvb2xlYW4gPSBmYWxzZSk6IHZvaWQge1xyXG5cclxuICAgIHZhciB0bXAgPSBbXTtcclxuICAgIGlmICh0aGlzLnBhcmVudERvY3VtZW50LnNob3dTb3VyY2VMaXF1aWRpdHkpIHtcclxuICAgICAgdGhpcy5hY2N1bUxhYmVsLnZpc2libGUgPSBmYWxzZTtcclxuICAgICAgdGhpcy5hY2N1bUxhYmVsLmluY2x1ZGVJbkxheW91dCA9IGZhbHNlO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgdGhpcy5hY2N1bUxhYmVsLnZpc2libGUgPSB0cnVlO1xyXG4gICAgICB0aGlzLmFjY3VtTGFiZWwuaW5jbHVkZUluTGF5b3V0ID0gdHJ1ZTtcclxuICAgIH1cclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgIGxldCBzZXJpZTogU2VyaWVzID0gdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpW2ldO1xyXG4gICAgICBpZiAoc2VyaWUuc2VyaWVzVHlwZSA9PSAnYXJlYScpIHtcclxuICAgICAgICBpZiAoIVN0cmluZ1V0aWxzLmlzRW1wdHkoc2VyaWUubWluRmllbGQpKSB7XHJcbiAgICAgICAgICB0bXAucHVzaChzZXJpZSk7XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgIH1cclxuICAgIC8vIGNvbnNvbGUudHJhY2UoKTtcclxuXHJcbiAgICB0aGlzLl9hc3NldHNMZWdlbmQuZGF0YVByb3ZpZGVyID0gdG1wO1xyXG5cclxuICAgIC8vIHVwZGF0ZUFyZWFMZWdlbmQgbmVlZHMgdG8gYmUgY2FsbGVkIHR3aWNlIHRvIHZhbGlkYXRlIHByb3BlcnRpZXNcclxuICAgIGlmIChyZWNhbGwpXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHRoaXMudXBkYXRlTGlxdWlkaXR5Wm9uZXNMZWdlbmQoZmFsc2UpO1xyXG4gICAgICB9LCAwKTtcclxuICAgIC8qaWYgKGlzRW1wdHkpe1xyXG4gICAgYWNjdW1MYWJlbC52aXNpYmxlID0gZmFsc2U7XHJcbiAgICBhY2N1bUxhYmVsLmluY2x1ZGVJbkxheW91dCA9IGZhbHNlO1xyXG4gICAgfVxyXG4gICAgZWxzZXtcclxuICAgIGFjY3VtTGFiZWwudmlzaWJsZSA9IHRydWU7XHJcbiAgICBhY2N1bUxhYmVsLmluY2x1ZGVJbkxheW91dCA9IHRydWU7XHJcbiAgICB9Ki9cclxuICB9XHJcblxyXG5cclxuXHJcbiAgLyoqXHJcbiAgICogVXBkYXRlcyBsb2NhbCBkYXRhc2V0c1xyXG4gICAqIHZhbHVlOiBkYXRhc2V0cy5kYXRhc2V0XHJcbiAgICogKi9cclxuICBwdWJsaWMgdXBkYXRlTWV0YWRhdGFzKG1ldGFkYXRhcyk6IHZvaWQge1xyXG4gICAgbGV0IG1ldGFEYXRhTGlzdCA9IFtdO1xyXG4gICAgbGV0IGNoYXJ0c0FycmF5ID0gW107XHJcbiAgICAvLyBpZihtZXRhZGF0YXMubWV0YWRhdGEgJiYgIW1ldGFkYXRhcy5tZXRhZGF0YS5sZW5ndGgpXHJcbiAgICAvLyAgICBtZXRhRGF0YUxpc3QucHVzaChtZXRhZGF0YXMubWV0YWRhdGEpO1xyXG4gICAgLy8gZWxzZSBcclxuICAgIC8vICAgbWV0YURhdGFMaXN0ID0gbWV0YWRhdGFzLm1ldGFkYXRhO1xyXG5cclxuICAgIG1ldGFEYXRhTGlzdCA9IFN3dFV0aWwuY29udmVydE9iamVjdFRvQXJyYXkobWV0YWRhdGFzLm1ldGFkYXRhKVxyXG5cclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbWV0YURhdGFMaXN0Lmxlbmd0aDsgaSsrKSB7XHJcblxyXG4gICAgICAvLyBMb29wIG9uIGFsbCByZWNlaXZlZCBkYXRhc2V0c1xyXG4gICAgICB2YXIgdGhyZXNob2xkc0NvbG9yU3R5bGU6IHN0cmluZyA9IFwiXCI7XHJcbiAgICAgIGxldCB4bWxEYXRhID0gbWV0YURhdGFMaXN0W2ldO1xyXG4gICAgICB2YXIgZ3JvdXBJZDogc3RyaW5nID0geG1sRGF0YS5ncm91cElkO1xyXG5cclxuICAgICAgaWYgKHhtbERhdGEuY2hhcnRzICYmICF4bWxEYXRhLmNoYXJ0cy5sZW5ndGgpXHJcbiAgICAgICAgY2hhcnRzQXJyYXkucHVzaCh4bWxEYXRhLmNoYXJ0cyk7XHJcbiAgICAgIGVsc2VcclxuICAgICAgICBjaGFydHNBcnJheSA9IHhtbERhdGEuY2hhcnRzO1xyXG5cclxuICAgICAgZm9yIChsZXQgayA9IDA7IGsgPCBjaGFydHNBcnJheS5sZW5ndGg7IGsrKykge1xyXG4gICAgICAgIGxldCBjaGFydHMgPSBjaGFydHNBcnJheVtrXTtcclxuXHJcbiAgICAgICAgdmFyIHNjZW5hcmlvSWQ6IHN0cmluZyA9IGNoYXJ0cy5zY2VuYXJpb0lkO1xyXG4gICAgICAgIHZhciBkdG86IE9iamVjdCA9IG5ldyBPYmplY3QoKTtcclxuICAgICAgICBpZiAoY2hhcnRzLnNjZW5hcmlvSWQgPT0gXCJTdGFuZGFyZFwiKVxyXG4gICAgICAgICAgdGhyZXNob2xkc0NvbG9yU3R5bGUgPSB0aGlzLmdldFRocmVzaG9sZHNDb2xvclN0eWxlKGNoYXJ0cyk7XHJcbiAgICAgICAgZHRvWydjaGFydHMnXSA9IGNoYXJ0cztcclxuICAgICAgICB0aGlzLmRhdGFzZXRzW2dyb3VwSWQgKyAnLicgKyBzY2VuYXJpb0lkXSA9IGR0bztcclxuICAgICAgfVxyXG4gICAgICB2YXIgdGhyZXNob2xkczogT2JqZWN0ID0gbmV3IE9iamVjdCgpO1xyXG4gICAgICB0aHJlc2hvbGRzW1widGhyZXNob2xkc1wiXSA9IHhtbERhdGEudGhyZXNob2xkcztcclxuICAgICAgdGhyZXNob2xkc1sndGhyZXNob2xkc0NvbG9yJ10gPSB0aHJlc2hvbGRzQ29sb3JTdHlsZTtcclxuXHJcbiAgICAgIHRoaXMuZGF0YXNldHNbZ3JvdXBJZCArICcuVGhyZXNob2xkcyddID0gdGhyZXNob2xkcztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHB1YmxpYyBKU09ORGF0YU5PU09EOiBIYXNoTWFwID0gbmV3IEhhc2hNYXA7XHJcbiAgcHVibGljIEpTT05EYXRhU09EOiBIYXNoTWFwID0gbmV3IEhhc2hNYXA7XHJcbiAgcHVibGljIHNlcmllc0xpc3Q6IEhhc2hNYXAgPSBuZXcgSGFzaE1hcDtcclxuICBwdWJsaWMgSlNPTkRhdGFTT0RBc1N0cmluZyA9ICcnO1xyXG4gIHB1YmxpYyB0aW1lUmFuZ2VBcnJheSA9IFtdO1xyXG4gIHB1YmxpYyB0aW1lUmFuZ2VBcnJheUVudGl0eSA9IFtdO1xyXG4gIC8qKlxyXG5cdFx0ICogVXBkYXRlcyB0aGUgZGF0YXByb3ZpZGVyXHJcblx0XHQgKiAqL1xyXG4gIHB1YmxpYyB1cGRhdGVEYXRhcHJvdmlkZXIoeG1sRGF0YXNldHMsIFNPRFNlbGVjdGVkOiBib29sZWFuLCBzZWxlY3RlZEl0ZW1zSW50cmVlID0gbnVsbCwgdW5jaGVja2VkSXRlbUZyb21MZWdlbmRzID0gbnVsbCwgdW5jaGVja2VkVGhyZXNob2xkRnJvbVByb2ZpbGUgPSBudWxsKTogdm9pZCB7XHJcbiAgICB2YXIgbmV3RHAgPSBbXTtcclxuICAgIHZhciBub1NvZERwID0gW107XHJcblxyXG4gICAgdGhpcy5KU09ORGF0YU5PU09EID0gbmV3IEhhc2hNYXA7XHJcbiAgICB0aGlzLkpTT05EYXRhU09EID0gbmV3IEhhc2hNYXA7XHJcbiAgICB0aGlzLkpTT05EYXRhU09EQXNTdHJpbmcgPSAnJztcclxuICAgIHZhciBsaXN0T2ZHcm91cHMgPSBbXTtcclxuXHJcblxyXG4gICAgaWYgKHRoaXMuZGF0YVByb3ZpZGVyICE9IG51bGwgJiYgdGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoID4gMCkge1xyXG4gICAgICBpZiAoU09EU2VsZWN0ZWQpIHtcclxuICAgICAgICAvLyBuZXdEcCA9ICh0aGlzLmRhdGFQcm92aWRlciBhcyBBcnJheUNvbGxlY3Rpb24pLnRvQXJyYXkoKTtcclxuICAgICAgICAvLyBub1NvZERwID0gKHRoaXMuTm9TT0RfRGF0YXByb3ZpZGVyIGFzIEFycmF5Q29sbGVjdGlvbikudG9BcnJheSgpO1xyXG4gICAgICAgIG5ld0RwID0gdGhpcy5kYXRhUHJvdmlkZXI7XHJcbiAgICAgICAgbm9Tb2REcCA9IHRoaXMuTm9TT0RfRGF0YXByb3ZpZGVyO1xyXG4gICAgICB9XHJcbiAgICAgIGVsc2Uge1xyXG4gICAgICAgIG5ld0RwID0gdGhpcy5Ob1NPRF9EYXRhcHJvdmlkZXI7XHJcbiAgICAgICAgbm9Tb2REcCA9IHRoaXMuZGF0YVByb3ZpZGVyO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkSXRlbXNJbnRyZWUgPT0gbnVsbClcclxuICAgICAgc2VsZWN0ZWRJdGVtc0ludHJlZSA9IFtdO1xyXG4gICAgaWYgKHVuY2hlY2tlZEl0ZW1Gcm9tTGVnZW5kcyA9PSBudWxsKVxyXG4gICAgICB1bmNoZWNrZWRJdGVtRnJvbUxlZ2VuZHMgPSBbXTtcclxuICAgIGlmICh1bmNoZWNrZWRUaHJlc2hvbGRGcm9tUHJvZmlsZSA9PSBudWxsKVxyXG4gICAgICB1bmNoZWNrZWRUaHJlc2hvbGRGcm9tUHJvZmlsZSA9IFtdO1xyXG5cclxuICAgIHZhciBkYXRhVG9QYXNzOiBzdHJpbmc7XHJcbiAgICB2YXIgZGF0YVRvUGFzc1NPRDogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0TmFtZTogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0VHlwZVRvRGlzcGxheTogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0eUZpZWxkOiBzdHJpbmc7XHJcbiAgICB2YXIgY2hhcnRTdHlsZU5hbWU6IHN0cmluZztcclxuICAgIHZhciBjaGFydENvbG9yOiBzdHJpbmc7XHJcbiAgICB2YXIgdmFsdWVUaHJlc2hvbGRBc051bWJlcjogbnVtYmVyO1xyXG4gICAgdmFyIHZhbHVlVGhyZXNob2xkQXNOdW1iZXJJbmNTT0Q6IG51bWJlcjtcclxuXHJcbiAgICBsZXQgeG1sRGF0YXNldHNBc0FycmF5ID0gU3d0VXRpbC5jb252ZXJ0T2JqZWN0VG9BcnJheSh4bWxEYXRhc2V0cy5kYXRhc2V0KTtcclxuXHJcbiAgICBmb3IgKGxldCBrID0gMDsgayA8IHhtbERhdGFzZXRzQXNBcnJheS5sZW5ndGg7IGsrKykge1xyXG4gICAgICBsZXQgZGF0YXNldCA9IHhtbERhdGFzZXRzQXNBcnJheVtrXTtcclxuICAgICAgdmFyIGdyb3VwSWQ6IHN0cmluZyA9IGRhdGFzZXQuZ3JvdXBJZDtcclxuICAgICAgdmFyIHNjZW5hcmlvSWQ6IHN0cmluZyA9IGRhdGFzZXQuc2NlbmFyaW9JZDtcclxuICAgICAgbGV0IGRhdGEgPSBkYXRhc2V0LmRhdGE7XHJcbiAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgZGF0YS5wYXJlbnQgPSBkYXRhc2V0O1xyXG4gICAgICAgIHRoaXMudXBkYXRlRGF0YVByb3ZpZGVyRm9yKGdyb3VwSWQsIHNjZW5hcmlvSWQsIGRhdGEsIG5ld0RwLCBub1NvZERwLCBzZWxlY3RlZEl0ZW1zSW50cmVlLCB1bmNoZWNrZWRJdGVtRnJvbUxlZ2VuZHMpO1xyXG4gICAgICAgIGlmIChsaXN0T2ZHcm91cHMuaW5kZXhPZihncm91cElkKSA9PSAtMSkge1xyXG4gICAgICAgICAgbGlzdE9mR3JvdXBzLnB1c2goZ3JvdXBJZCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG5cclxuXHJcblxyXG4gICAgLy8gZm9yIGVhY2ggKHZhciB0aHJlc2hvbGRHcm91cElkOnN0cmluZyBpbiBsaXN0T2ZHcm91cHMpXHJcbiAgICBmb3IgKGxldCBrID0gMDsgayA8IGxpc3RPZkdyb3Vwcy5sZW5ndGg7IGsrKykge1xyXG4gICAgICBsZXQgdGhyZXNob2xkR3JvdXBJZCA9IGxpc3RPZkdyb3Vwc1trXTtcclxuXHJcbiAgICAgIHZhciBkdG86IE9iamVjdCA9IHRoaXMuZGF0YXNldHNbdGhyZXNob2xkR3JvdXBJZCArICcuVGhyZXNob2xkcyddO1xyXG4gICAgICBpZiAoZHRvKSB7XHJcbiAgICAgICAgdmFyIHRocmVzaG9sZHM6IGFueSA9IGR0b1tcInRocmVzaG9sZHNcIl07XHJcbiAgICAgICAgdmFyIHN0eWxlQ29sb3I6IHN0cmluZyA9IGR0b1tcInRocmVzaG9sZHNDb2xvclwiXTtcclxuICAgICAgICB2YXIgdGhyZWFzaG9sZFZhbHVlOiBzdHJpbmc7XHJcbiAgICAgICAgdmFyIGNoYXJ0TmFtZUFzU3RyaW5nOiBzdHJpbmc7XHJcbiAgICAgICAgdmFyIHZpc2libGl0eTogYm9vbGVhbjtcclxuICAgICAgICB2YXIgdmlzaWJsaXR5QXNTdHJpbmc6IHN0cmluZztcclxuICAgICAgICAvLyBmb3IgZWFjaCAodmFyIGNoYXJ0OlhNTCBpbiB0aHJlc2hvbGRzLmNoaWxkcmVuKCkpe1xyXG4gICAgICAgIGZvciAodmFyIGNoYXJ0IGluIHRocmVzaG9sZHMpIHtcclxuICAgICAgICAgIHRocmVhc2hvbGRWYWx1ZSA9ICcnICsgdGhyZXNob2xkc1tjaGFydF07XHJcbiAgICAgICAgICBpZiAoKCcnICsgdGhyZWFzaG9sZFZhbHVlKS5jaGFyQXQoMCkgPT0gJy4nKSB7XHJcbiAgICAgICAgICAgIHRocmVhc2hvbGRWYWx1ZSA9ICcwJyArIHRocmVhc2hvbGRWYWx1ZTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGlmICh0aHJlYXNob2xkVmFsdWUgIT0gbnVsbCAmJiB0aHJlYXNob2xkVmFsdWUubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICB2YWx1ZVRocmVzaG9sZEFzTnVtYmVyID0gTnVtYmVyKHRocmVhc2hvbGRWYWx1ZSk7XHJcbiAgICAgICAgICAgIHZhbHVlVGhyZXNob2xkQXNOdW1iZXJJbmNTT0QgPSB2YWx1ZVRocmVzaG9sZEFzTnVtYmVyIC0gdGhpcy5zdW1FeHRlcm5hbFNvZE1hcFt0aHJlc2hvbGRHcm91cElkXTtcclxuXHJcblxyXG4gICAgICAgICAgICBjaGFydE5hbWVBc1N0cmluZyA9IGNoYXJ0O1xyXG4gICAgICAgICAgICBjaGFydE5hbWUgPSBcIlxcXCJuYW1lXFxcIjpcXFwiXCIgKyB0aHJlc2hvbGRHcm91cElkICsgJy4nICsgY2hhcnROYW1lQXNTdHJpbmcgKyBcIlxcXCJcIjtcclxuICAgICAgICAgICAgY2hhcnRUeXBlVG9EaXNwbGF5ID0gXCJcXFwidHlwZVxcXCI6XFxcIlRocmVzaG9sZFxcXCJcIjtcclxuICAgICAgICAgICAgY2hhcnRTdHlsZU5hbWUgPSBcIlxcXCJjaGFydFN0eWxlTmFtZVxcXCI6XFxcIlwiICsgc3R5bGVDb2xvciArIFwiXFxcIlwiO1xyXG4gICAgICAgICAgICAvKnZpc2libGl0eSA9ICh1bmNoZWNrZWRUaHJlc2hvbGRGcm9tUHJvZmlsZS5pbmRleE9mKHRocmVzaG9sZEdyb3VwSWQpID09IC0xKTsqL1xyXG5cclxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkSXRlbXNJbnRyZWUuaW5kZXhPZih0aHJlc2hvbGRHcm91cElkICsgJy5UaHJlc2hvbGRzJykgPT0gLTEpIHtcclxuICAgICAgICAgICAgICB2aXNpYmxpdHkgPSBmYWxzZTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBpZiAodW5jaGVja2VkVGhyZXNob2xkRnJvbVByb2ZpbGUuaW5kZXhPZih0aHJlc2hvbGRHcm91cElkKSA9PSAtMSkge1xyXG4gICAgICAgICAgICAgICAgdmlzaWJsaXR5ID0gdHJ1ZTtcclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdmlzaWJsaXR5ID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB2aXNpYmxpdHlBc1N0cmluZyA9IFwiXFxcInZpc2liaWxpdHlcXFwiOlxcXCJcIiArIHZpc2libGl0eSArIFwiXFxcIlwiO1xyXG4gICAgICAgICAgICBpZiAoY2hhcnROYW1lQXNTdHJpbmcuaW5kZXhPZihcIjJcIikgIT0gLTEpIHtcclxuICAgICAgICAgICAgICBjaGFydENvbG9yID0gXCJcXFwiY29sb3JcXFwiOlxcXCJoc2xhKDAsIDEwMCUsIDUwJSwgMC4zKVxcXCJcIjtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBjaGFydENvbG9yID0gXCJcXFwiY29sb3JcXFwiOlxcXCJoc2xhKDMzLCAxMDAlLCA1MCUsIDAuMylcXFwiXCI7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGRhdGFUb1Bhc3MgPSBcIlxcXCJkYXRhXFxcIjpbXCIgKyB2YWx1ZVRocmVzaG9sZEFzTnVtYmVyICsgXCJdXCI7XHJcbiAgICAgICAgICAgIGRhdGFUb1Bhc3NTT0QgPSBcIlxcXCJkYXRhU09EXFxcIjpbXCIgKyB2YWx1ZVRocmVzaG9sZEFzTnVtYmVySW5jU09EICsgXCJdXCI7XHJcblxyXG4gICAgICAgICAgICBpZiAodGhpcy5KU09ORGF0YVNPREFzU3RyaW5nLmxlbmd0aCA9PSAwKVxyXG4gICAgICAgICAgICAgIHRoaXMuSlNPTkRhdGFTT0RBc1N0cmluZyArPSBcIntcIiArIGNoYXJ0TmFtZSArIFwiLFwiICsgY2hhcnRUeXBlVG9EaXNwbGF5ICsgXCIsXCIgKyBjaGFydFN0eWxlTmFtZSArIFwiLFwiICsgdmlzaWJsaXR5QXNTdHJpbmcgKyBcIixcIiArIGNoYXJ0Q29sb3IgKyBcIixcIiArIGRhdGFUb1Bhc3MgKyBcIixcIiArIGRhdGFUb1Bhc3NTT0QgKyBcIn1cIjtcclxuICAgICAgICAgICAgZWxzZVxyXG4gICAgICAgICAgICAgIHRoaXMuSlNPTkRhdGFTT0RBc1N0cmluZyArPSBcIix7XCIgKyBjaGFydE5hbWUgKyBcIixcIiArIGNoYXJ0VHlwZVRvRGlzcGxheSArIFwiLFwiICsgY2hhcnRTdHlsZU5hbWUgKyBcIixcIiArIHZpc2libGl0eUFzU3RyaW5nICsgXCIsXCIgKyBjaGFydENvbG9yICsgXCIsXCIgKyBkYXRhVG9QYXNzICsgXCIsXCIgKyBkYXRhVG9QYXNzU09EICsgXCJ9XCI7XHJcblxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5kYXRhUHJvdmlkZXIgPSBuZXdEcDtcclxuICAgIHRoaXMuTm9TT0RfRGF0YXByb3ZpZGVyID0gbm9Tb2REcDtcclxuXHJcbiAgfVxyXG5cclxuXHJcbiAgLyoqXHJcbiAgICogVXBkYXRlcyB0aGUgZGF0YXByb3ZpZGVyIGZvciBncm91cC9zY2VuYXJpb1xyXG4gICAqIFJldHVybnMgZmFsc2UgaWYgYW4gZXJyb3Igb24gdGltZVNsb3RzLCBvdGhlcndpc2UgdHJ1ZVxyXG4gICAqIHZhbHVlOiBkYXRhc2V0LmRhdGFcclxuICAgKiAqL1xyXG4gIHByaXZhdGUgdXBkYXRlRGF0YVByb3ZpZGVyRm9yKGdyb3VwSWQ6IHN0cmluZywgc2NlbmFyaW9JZDogc3RyaW5nLCB2YWx1ZSwgbmV3RHAsIG5vU29kRHAsIHNlbGVjdGVkSXRlbXNJbnRyZWUgPSBudWxsLCB1bmNoZWNrZWRJdGVtRnJvbUxlZ2VuZHMgPSBudWxsKTogYm9vbGVhbiB7XHJcbiAgICB2YXIgdmFsdWVPZkhhc2hNYXBFbGVtZW50ID0gW107XHJcbiAgICB2YXIgdmFsdWVPZkhhc2hNYXBFbGVtZW50Tm9TT0QgPSBbXTtcclxuICAgIHRoaXMuSlNPTkRhdGFOT1NPRCA9IG5ldyBIYXNoTWFwO1xyXG4gICAgdGhpcy5KU09ORGF0YVNPRCA9IG5ldyBIYXNoTWFwO1xyXG4gICAgdGhpcy50aW1lUmFuZ2VBcnJheSA9IFtdO1xyXG4gICAgdGhpcy50aW1lUmFuZ2VBcnJheUVudGl0eSA9IFtdO1xyXG5cclxuICAgIHZhciBkZWNSZWdleDogUmVnRXhwID0gbmV3IFJlZ0V4cCgnLCcpO1xyXG4gICAgdmFyIHRlbXBTT0Q6IHN0cmluZyA9ICcnICsgdmFsdWUucGFyZW50LlNVTV9GT1JFQ0FTVF9TT0Q7XHJcbiAgICB2YXIgc3VtQ3JlZGl0TGluZVRvdGFsOiBudW1iZXIgPSB2YWx1ZS5wYXJlbnQuU1VNX0NSRURJVF9MSU5FX1RPVEFMIHx8IDA7XHJcbiAgICB2YXIgZ3JwU2NlbktleTogc3RyaW5nID0gKGdyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIik7XHJcbiAgICB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgXCJTVU1fQ1JFRElUX0xJTkVfVE9UQUxcIl0gPSBzdW1DcmVkaXRMaW5lVG90YWw7XHJcbiAgICB2YXIgc3VtQ29sbGF0ZXJhbDogbnVtYmVyID0gdmFsdWUucGFyZW50LlNVTV9DT0xMQVRFUkFMIHx8IDA7XHJcbiAgICB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgXCJTVU1fQ09MTEFURVJBTFwiXSA9IHN1bUNvbGxhdGVyYWw7XHJcbiAgICB2YXIgc3VtVW5MaXF1aWRBc3NldHM6IG51bWJlciA9IHZhbHVlLnBhcmVudC5TVU1fVU5fTElRVUlEX0FTU0VUUyB8fCAwO1xyXG4gICAgdGhpcy5saXF1aWRpdHlab25lc0xpc3RWYWx1ZXNbZ3JwU2NlbktleSArIFwiU1VNX1VOX0xJUVVJRF9BU1NFVFNcIl0gPSBzdW1VbkxpcXVpZEFzc2V0cztcclxuICAgIHZhciBzdW1PdGhlclRvdGFsOiBudW1iZXIgPSB2YWx1ZS5wYXJlbnQuU1VNX09USEVSX1RPVEFMIHx8IDA7XHJcbiAgICB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgXCJTVU1fT1RIRVJfVE9UQUxcIl0gPSBzdW1PdGhlclRvdGFsO1xyXG5cclxuXHJcblxyXG4gICAgdGVtcFNPRCA9IHRlbXBTT0QucmVwbGFjZShkZWNSZWdleCwgJy4nKTtcclxuICAgIGlmICh0ZW1wU09ELmNoYXJBdCgwKSA9PSAnLicpIHtcclxuICAgICAgdGVtcFNPRCA9ICcwJyArIHRlbXBTT0Q7XHJcbiAgICB9XHJcbiAgICB2YXIgc3VtRm9yZWNhc3RTb2Q6IG51bWJlciA9IE51bWJlcih0ZW1wU09EKTtcclxuXHJcblxyXG4gICAgdGVtcFNPRCA9ICcnICsgdmFsdWUucGFyZW50LlNVTV9FWFRFUk5BTF9TT0Q7XHJcbiAgICB0ZW1wU09EID0gdGVtcFNPRC5yZXBsYWNlKGRlY1JlZ2V4LCAnLicpO1xyXG4gICAgaWYgKHRlbXBTT0QuY2hhckF0KDApID09ICcuJykge1xyXG4gICAgICB0ZW1wU09EID0gJzAnICsgdGVtcFNPRDtcclxuICAgIH1cclxuICAgIHZhciBzdW1FeHRlcm5hbFNvZDogbnVtYmVyID0gTnVtYmVyKHRlbXBTT0QpO1xyXG4gICAgdGhpcy5zdW1FeHRlcm5hbFNvZE1hcFtncm91cElkXSA9IHN1bUV4dGVybmFsU29kO1xyXG5cclxuICAgIHZhciBpZHg6IG51bWJlciA9IDA7XHJcbiAgICB2YXIgcGFydE9mRGF0YVByb3ZpZGVyID0gW107XHJcbiAgICB2YXIgcGFydE9mRGF0YVByb3ZpZGVyTk9TT0QgPSBbXTtcclxuICAgIHZhciBmaXJzdFRpbWVUb1Nob3c6IG51bWJlcjtcclxuICAgIHZhciBmaXJzdFRpbWVUb1Nob3dXaXRoRW50aXR5VGltZUZyYW1lOiBudW1iZXI7XHJcbiAgICAvLyBDb21wbGV4aXR5IHNob3VsZCBiZSBPKG4peDh4KGN1cnJlbnREcFtpXSB0aW1lKSA9PiBjb21wbGV4aXR5PU8obkxvZyhuKSlcclxuICAgIC8vIGZvciBlYWNoKHZhciByZXN1bHRYbWw6IFhNTCBpbiB2YWx1ZS5yZXN1bHQpXHJcbiAgICBmb3IgKGxldCBrID0gMDsgayA8IHZhbHVlLnJlc3VsdC5sZW5ndGg7IGsrKykge1xyXG4gICAgICBsZXQgcmVzdWx0WG1sID0gdmFsdWUucmVzdWx0W2tdO1xyXG4gICAgICB2YXIgcmVzdWx0T2JqOiBPYmplY3QgPSBwYXJ0T2ZEYXRhUHJvdmlkZXIubGVuZ3RoID09IDAgPyBuZXcgT2JqZWN0KCkgOiBwYXJ0T2ZEYXRhUHJvdmlkZXJbaWR4XTsvL1N0cmluZ1V0aWxzLmNsb25lT2JqZWN0KG5ld0RwW2lkeF0pO1xyXG4gICAgICByZXN1bHRPYmogPSAhcmVzdWx0T2JqID8gbmV3IE9iamVjdCgpIDogcmVzdWx0T2JqO1xyXG5cclxuICAgICAgdmFyIHJlc3VsdE9iak5vU09EOiBPYmplY3QgPSBwYXJ0T2ZEYXRhUHJvdmlkZXJOT1NPRC5sZW5ndGggPT0gMCA/IG5ldyBPYmplY3QoKSA6IHBhcnRPZkRhdGFQcm92aWRlck5PU09EW2lkeF07Ly9TdHJpbmdVdGlscy5jbG9uZU9iamVjdChub1NvZERwW2lkeF0pO1xyXG4gICAgICByZXN1bHRPYmpOb1NPRCA9ICFyZXN1bHRPYmpOb1NPRCA/IG5ldyBPYmplY3QoKSA6IHJlc3VsdE9iak5vU09EO1xyXG5cclxuICAgICAgdmFyIHNsb3RzQW1vdW50c0FyciA9IHJlc3VsdFhtbC50b1N0cmluZygpLnNwbGl0KFwiO1wiKTtcclxuICAgICAgaWYoc2xvdHNBbW91bnRzQXJyLmxlbmd0aD09MTEpe1xyXG4gICAgICAgIHRlbXBTT0QgPSBzbG90c0Ftb3VudHNBcnJbMTBdO1xyXG4gICAgICAgIHRlbXBTT0QgPSB0ZW1wU09ELnJlcGxhY2UoZGVjUmVnZXgsICcuJyk7XHJcbiAgICAgICAgaWYgKHRlbXBTT0QuY2hhckF0KDApID09ICcuJykge1xyXG4gICAgICAgICAgdGVtcFNPRCA9ICcwJyArIHRlbXBTT0Q7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHN1bUZvcmVjYXN0U29kID0gTnVtYmVyKHRlbXBTT0QpO1xyXG4gICAgXHJcbiAgICBcclxuICAgICAgICB0ZW1wU09EID0gJycgKyBzbG90c0Ftb3VudHNBcnJbOV07XHJcbiAgICAgICAgdGVtcFNPRCA9IHRlbXBTT0QucmVwbGFjZShkZWNSZWdleCwgJy4nKTtcclxuICAgICAgICBpZiAodGVtcFNPRC5jaGFyQXQoMCkgPT0gJy4nKSB7XHJcbiAgICAgICAgICB0ZW1wU09EID0gJzAnICsgdGVtcFNPRDtcclxuICAgICAgICB9XHJcbiAgICAgICAgc3VtRXh0ZXJuYWxTb2QgPSBOdW1iZXIodGVtcFNPRCk7XHJcbiAgICB9XHJcblxyXG4gICAgICBcclxuICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBzbG90c0Ftb3VudHNBcnIubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICBpZigoc2xvdHNBbW91bnRzQXJyLmxlbmd0aD09MTEpICYmIChpPT05IHx8aT09MTApKVxyXG4gICAgICAgICAgY29udGludWU7XHJcbiAgICAgICAgLy8gdmFyIGRhdGFGaWVsZDogc3RyaW5nID0gSUxNTGluZUNoYXJ0LkFSUkFZX0NPTExFQ1RJT05fRlJPTV9TRVJJRVMuZ2V0SXRlbUF0KGkpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgdmFyIGRhdGFGaWVsZDogc3RyaW5nID0gSUxNTGluZUNoYXJ0LkFSUkFZX0NPTExFQ1RJT05fRlJPTV9TRVJJRVNbaV07XHJcbiAgICAgICAgdmFyIHhtbFZhbHVlOiBzdHJpbmcgPSAnJyArIHNsb3RzQW1vdW50c0FycltpXTtcclxuICAgICAgICB4bWxWYWx1ZSA9IHhtbFZhbHVlLnJlcGxhY2UoZGVjUmVnZXgsICcuJyk7XHJcblxyXG4gICAgICAgIHZhciBzb2RUeXBlOiBzdHJpbmcgPSBkYXRhRmllbGQ7XHJcblxyXG4gICAgICAgIC8vIGtlZXAgdGltZVNsb3QgZGF0YSBmaWVsZCBub3QgY2hhbmdlZFxyXG4gICAgICAgIGlmIChkYXRhRmllbGQgIT0gJ3RpbWVTbG90JyAmJiBkYXRhRmllbGQgIT0gJ3RpbWVTbG90RScpIHtcclxuICAgICAgICAgIGRhdGFGaWVsZCA9IChncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIpICsgZGF0YUZpZWxkO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gSWYgbm90IHRoZSBmaXJzdCBhbmQgc2Vjb25kICh0aW1lU2xvdCBhbmQgdGltZVNsb3RFKVxyXG4gICAgICAgIGlmIChpICE9IDAgJiYgaSAhPSAxKSB7XHJcbiAgICAgICAgICBpZiAoeG1sVmFsdWUgJiYgeG1sVmFsdWUgIT0gJycpIHtcclxuICAgICAgICAgICAgaWYgKHhtbFZhbHVlLmNoYXJBdCgwKSA9PSAnLicpIHtcclxuICAgICAgICAgICAgICB4bWxWYWx1ZSA9ICcwJyArIHhtbFZhbHVlO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB2YXIgbnVtID0gTnVtYmVyKHhtbFZhbHVlKTtcclxuXHJcbiAgICAgICAgICAgIGlmICh0aGlzLkpTT05EYXRhU09ELmdldFZhbHVlKGRhdGFGaWVsZCkgPT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgIHZhbHVlT2ZIYXNoTWFwRWxlbWVudCA9IG5ldyBBcnJheTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICB2YWx1ZU9mSGFzaE1hcEVsZW1lbnQgPSB0aGlzLkpTT05EYXRhU09ELmdldFZhbHVlKGRhdGFGaWVsZClcclxuICAgICAgICAgICAgfVxyXG5cclxuXHJcbiAgICAgICAgICAgIGlmICh0aGlzLkpTT05EYXRhTk9TT0QuZ2V0VmFsdWUoZGF0YUZpZWxkKSA9PSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgdmFsdWVPZkhhc2hNYXBFbGVtZW50Tm9TT0QgPSBuZXcgQXJyYXk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgdmFsdWVPZkhhc2hNYXBFbGVtZW50Tm9TT0QgPSB0aGlzLkpTT05EYXRhTk9TT0QuZ2V0VmFsdWUoZGF0YUZpZWxkKVxyXG4gICAgICAgICAgICB9XHJcblxyXG5cclxuICAgICAgICAgICAgcmVzdWx0T2JqW2RhdGFGaWVsZF0gPSBudW07XHJcbiAgICAgICAgICAgIHZhbHVlT2ZIYXNoTWFwRWxlbWVudC5wdXNoKFwiXCIgKyBudW0pO1xyXG4gICAgICAgICAgICB0aGlzLkpTT05EYXRhU09ELnB1dChkYXRhRmllbGQsIHZhbHVlT2ZIYXNoTWFwRWxlbWVudCk7XHJcblxyXG5cclxuICAgICAgICAgICAgaWYgKChzb2RUeXBlID09IElMTUxpbmVDaGFydC5GT1JFQ0FTVF9CQUxBTkNFX1RBRykgfHwgKHNvZFR5cGUgPT0gSUxNTGluZUNoYXJ0LkZPUkVDQVNUX0JBTF9JTkNfQUNUVUFMU19UQUcpKSB7XHJcbiAgICAgICAgICAgICAgcmVzdWx0T2JqTm9TT0RbZGF0YUZpZWxkXSA9IG51bSAtIHN1bUZvcmVjYXN0U29kO1xyXG4gICAgICAgICAgICAgIHZhbHVlT2ZIYXNoTWFwRWxlbWVudE5vU09ELnB1c2goXCJcIiArIChudW0gLSBzdW1Gb3JlY2FzdFNvZCkpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHNvZFR5cGUgPT0gSUxNTGluZUNoYXJ0LkFDVFVBTF9CQUxBTkNFX1RBRykge1xyXG4gICAgICAgICAgICAgIHJlc3VsdE9iak5vU09EW2RhdGFGaWVsZF0gPSBudW0gLSBzdW1FeHRlcm5hbFNvZDtcclxuICAgICAgICAgICAgICB2YWx1ZU9mSGFzaE1hcEVsZW1lbnROb1NPRC5wdXNoKFwiXCIgKyAobnVtIC0gc3VtRXh0ZXJuYWxTb2QpKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICByZXN1bHRPYmpOb1NPRFtkYXRhRmllbGRdID0gbnVtO1xyXG4gICAgICAgICAgICAgIHZhbHVlT2ZIYXNoTWFwRWxlbWVudE5vU09ELnB1c2goXCJcIiArIG51bSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHRoaXMuSlNPTkRhdGFOT1NPRC5wdXQoZGF0YUZpZWxkLCB2YWx1ZU9mSGFzaE1hcEVsZW1lbnROb1NPRCk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIHtcclxuICAgICAgICAgIGlmIChpc05hTihmaXJzdFRpbWVUb1Nob3cpICYmIGRhdGFGaWVsZCA9PSAndGltZVNsb3QnKVxyXG4gICAgICAgICAgICBmaXJzdFRpbWVUb1Nob3cgPSBtb21lbnQoeG1sVmFsdWUpLnRvRGF0ZSgpLmdldFRpbWUoKTtcclxuICAgICAgICAgIC8vIGZpcnN0VGltZVRvU2hvdyA9IG5ldyBEYXRlKHhtbFZhbHVlKS5nZXRUaW1lKCk7XHJcbiAgICAgICAgICAvLyBmaXJzdFRpbWVUb1Nob3cgPSBEYXRlRm9ybWF0dGVyLnBhcnNlRGF0ZVN0cmluZyh4bWxWYWx1ZSkuZ2V0VGltZSgpO1xyXG5cclxuICAgICAgICAgIGlmIChpc05hTihmaXJzdFRpbWVUb1Nob3dXaXRoRW50aXR5VGltZUZyYW1lKSAmJiBkYXRhRmllbGQgPT0gJ3RpbWVTbG90RScpXHJcbiAgICAgICAgICAgIGZpcnN0VGltZVRvU2hvd1dpdGhFbnRpdHlUaW1lRnJhbWUgPSBtb21lbnQoeG1sVmFsdWUpLnRvRGF0ZSgpLmdldFRpbWUoKTtcclxuICAgICAgICAgIC8vZmlyc3RUaW1lVG9TaG93V2l0aEVudGl0eVRpbWVGcmFtZSA9IERhdGVGb3JtYXR0ZXIucGFyc2VEYXRlU3RyaW5nKHhtbFZhbHVlKS5nZXRUaW1lKCk7XHJcblxyXG5cclxuICAgICAgICAgIC8vIFRpbWVzbG90c1xyXG4gICAgICAgICAgcmVzdWx0T2JqW2RhdGFGaWVsZF0gPSB4bWxWYWx1ZTtcclxuICAgICAgICAgIHJlc3VsdE9iak5vU09EW2RhdGFGaWVsZF0gPSB4bWxWYWx1ZTtcclxuICAgICAgICAgIGlmIChkYXRhRmllbGQgPT0gJ3RpbWVTbG90Jykge1xyXG5cclxuICAgICAgICAgICAgdGhpcy50aW1lUmFuZ2VBcnJheS5wdXNoKHhtbFZhbHVlKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGVsc2UgaWYgKGRhdGFGaWVsZCA9PSAndGltZVNsb3RFJykge1xyXG4gICAgICAgICAgICB0aGlzLnRpbWVSYW5nZUFycmF5RW50aXR5LnB1c2goeG1sVmFsdWUpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBwYXJ0T2ZEYXRhUHJvdmlkZXJbaWR4XSA9IHJlc3VsdE9iajtcclxuICAgICAgcGFydE9mRGF0YVByb3ZpZGVyTk9TT0RbaWR4XSA9IHJlc3VsdE9iak5vU09EO1xyXG4gICAgICBpZHgrKztcclxuICAgIH1cclxuXHJcblxyXG4gICAgbmV3RHBbMF0gPSBwYXJ0T2ZEYXRhUHJvdmlkZXJbMF07XHJcbiAgICBub1NvZERwWzBdID0gcGFydE9mRGF0YVByb3ZpZGVyTk9TT0RbMF07XHJcblxyXG4gICAgbmV3RHBbMV0gPSBwYXJ0T2ZEYXRhUHJvdmlkZXJbaWR4IC0gMV07XHJcbiAgICBub1NvZERwWzFdID0gcGFydE9mRGF0YVByb3ZpZGVyTk9TT0RbaWR4IC0gMV07XHJcblxyXG4gICAgdmFyIHBvaW50U3RhcnQ6IHN0cmluZyA9IFwiXFxcInBvaW50U3RhcnRcXFwiOlwiICsgZmlyc3RUaW1lVG9TaG93O1xyXG4gICAgdmFyIHBvaW50U3RhcnRFbnRpdHk6IHN0cmluZyA9IFwiXFxcInBvaW50U3RhcnRFbnRpdHlcXFwiOlwiICsgZmlyc3RUaW1lVG9TaG93V2l0aEVudGl0eVRpbWVGcmFtZTtcclxuICAgIHZhciBwb2ludEludGVydmFsOiBzdHJpbmcgPSBcIlxcXCJwb2ludEludGVydmFsXFxcIjo2MDAwMFwiO1xyXG4gICAgdmFyIGRhdGFMZW5ndGg6IHN0cmluZztcclxuXHJcblxyXG5cclxuICAgIHZhciBkYXRhVG9QYXNzOiBzdHJpbmc7XHJcbiAgICB2YXIgZGF0YVRvUGFzc1NPRDogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0TmFtZTogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0VHlwZVRvRGlzcGxheTogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0VHlwZURldGFpbHNUb0Rpc3BsYXk6IHN0cmluZztcclxuICAgIHZhciBjaGFydGxlZ2VuZERpc3BsYXlOYW1lOiBzdHJpbmc7XHJcbiAgICB2YXIgY2hhcnR5RmllbGQ6IHN0cmluZztcclxuICAgIHZhciBjaGFydERpc3BsYXlOYW1lOiBzdHJpbmc7XHJcbiAgICB2YXIgY2hhcnREaXNwbGF5TGFiZWw6IHN0cmluZztcclxuICAgIHZhciBjaGFydFN0eWxlTmFtZTogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0U3R5bGVDb2xvcjogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0U3R5bGVCb3JkZXJDb2xvcjogc3RyaW5nO1xyXG4gICAgdmFyIGlzVmlzaWJsZUNoYXJ0OiBzdHJpbmc7XHJcblxyXG4gICAgdmFyIGR0bzogT2JqZWN0ID0gdGhpcy5kYXRhc2V0c1tncm91cElkICsgJy4nICsgc2NlbmFyaW9JZF07XHJcblxyXG4gICAgaWYgKCFkdG8pIHtcclxuICAgICAgLy90cmFjZShcIkNoYXJ0IG5vdCBleGlzdCBhbmQgdG8gcGxvdCBpdCBuZWVkIHRvIHNlbmQgcmVxdWVzdCB0byBEQiBzaWRlXCIpO1xyXG4gICAgICAvL3Rocm93IEVycm9yKFwiUHJvZ3JhbW1pbmcgZXJyb3I6IG1ldGhvZCBbdXBkYXRlRGF0YVNldHNdIHNob3VsZCBiZSBjYWxsZWQgZmlyc3QgIVwiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgdmFyIG1ldGFkYXRhOiBhbnkgPSBkdG9bJ2NoYXJ0cyddO1xyXG5cclxuICAgIC8qaWYoY3VycmVuY3lJZCAhPSBtZXRhZGF0YS5AY3VycmVuY3lJZCB8fCBlbnRpdHlJZCAhPSBtZXRhZGF0YS5AZW50aXR5SWQpXHJcbiAgICB7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9Ki9cclxuICAgIHZhciBfeUZpZWxkOiBzdHJpbmcgPSBcIlwiO1xyXG4gICAgdmFyIGlzVmlzaWJsZUZyb21MZWdlbmQ6IGJvb2xlYW4gPSB0cnVlO1xyXG5cclxuXHJcbiAgICAvL2ZvciBlYWNoKHZhciBzdHI6c3RyaW5nIGluIEpTT05EYXRhU09ELmdldEtleXMoKSl7XHJcbiAgICAvLyBmb3IgKHZhciBzdHIgaW4gdGhpcy5KU09ORGF0YVNPRCkge1xyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLkpTT05EYXRhU09ELmdldEtleXMoKS5sZW5ndGg7IGkrKykge1xyXG4gICAgICAvL1x0aWYoc2VsZWN0ZWRJdGVtc0ludHJlZS5pbmRleE9mKHN0cikgIT0gLTEgKSB7XHJcbiAgICAgIGxldCBzdHIgPSB0aGlzLkpTT05EYXRhU09ELmdldEtleXMoKVtpXTtcclxuICAgICAgZGF0YUxlbmd0aCA9IFwiXFxcImRhdGFMZW5ndGhcXFwiOlwiICsgdGhpcy5KU09ORGF0YVNPRC5nZXRWYWx1ZShzdHIpLmxlbmd0aDtcclxuXHJcbiAgICAgIGNoYXJ0TmFtZSA9IFwiXFxcIm5hbWVcXFwiOlxcXCJcIiArIHN0ciArIFwiXFxcIlwiO1xyXG5cclxuICAgICAgaWYgKHNlbGVjdGVkSXRlbXNJbnRyZWUuaW5kZXhPZihzdHIpID09IC0xKSB7XHJcbiAgICAgICAgaXNWaXNpYmxlRnJvbUxlZ2VuZCA9IGZhbHNlO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGlmICh1bmNoZWNrZWRJdGVtRnJvbUxlZ2VuZHMuaW5kZXhPZihzdHIpID09IC0xKSB7XHJcbiAgICAgICAgICBpc1Zpc2libGVGcm9tTGVnZW5kID0gdHJ1ZTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgaXNWaXNpYmxlRnJvbUxlZ2VuZCA9IGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICB2YXIgY2hhcnRJZFBhcnRzID0gc3RyLnNwbGl0KFwiLlwiKTtcclxuICAgICAgLy8gZm9yIGVhY2ggKHZhciBjaGFydDpYTUwgaW4gbWV0YWRhdGEuY2hpbGRyZW4oKSl7XHJcbiAgICAgIGZvciAobGV0IGsgPSAwOyBrIDwgbWV0YWRhdGEuY2hhcnQubGVuZ3RoOyBrKyspIHtcclxuICAgICAgICBsZXQgY2hhcnQgPSBtZXRhZGF0YS5jaGFydFtrXTtcclxuICAgICAgICBpZiAoY2hhcnQuZGF0YWVsZW1lbnQgPT0gY2hhcnRJZFBhcnRzWzJdKSB7XHJcbiAgICAgICAgICAvLyBOb3cgY3JlYXRlIHRoZSB0aW1lc2VyaWVzIChhcmVhIG9yIGxpbmUpXHJcbiAgICAgICAgICB2YXIgY2hhcnRUeXBlOiBzdHJpbmcgPSBjaGFydC50eXBlO1xyXG4gICAgICAgICAgY2hhcnRUeXBlVG9EaXNwbGF5ID0gXCJcXFwidHlwZVxcXCI6XFxcIlwiICsgY2hhcnQudHlwZSArIFwiXFxcIlwiO1xyXG4gICAgICAgICAgY2hhcnRUeXBlRGV0YWlsc1RvRGlzcGxheSA9IFwiXFxcInR5cGVEZXRhaWxzXFxcIjpcXFwiXCIgKyBTZXJpZXNTdHlsZVByb3ZpZGVyLmdldFN0eWxlVHlwZShjaGFydC5zZXJpZXNTdHlsZSkgKyBcIlxcXCJcIjtcclxuICAgICAgICAgIF95RmllbGQgPSAoZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiKSArIGNoYXJ0LmRhdGFlbGVtZW50O1xyXG4gICAgICAgICAgY2hhcnRTdHlsZU5hbWUgPSBcIlxcXCJjaGFydFN0eWxlTmFtZVxcXCI6XFxcIlwiICsgU2VyaWVzU3R5bGVQcm92aWRlci5nZXRTdHlsZU5hbWUoY2hhcnQuc2VyaWVzU3R5bGUpICsgXCJcXFwiXCI7XHJcbiAgICAgICAgICBjaGFydFN0eWxlQ29sb3IgPSBcIlxcXCJjb2xvclxcXCI6XFxcIlwiICsgU2VyaWVzU3R5bGVQcm92aWRlci5nZXRTdHlsZUNvbG9yQXNTcmluZyhjaGFydC5zZXJpZXNTdHlsZSkgKyBcIlxcXCJcIjtcclxuICAgICAgICAgIGNoYXJ0U3R5bGVCb3JkZXJDb2xvciA9IFwiXFxcImJvcmRlckNvbG9yXFxcIjpcXFwiXCIgKyBTZXJpZXNTdHlsZVByb3ZpZGVyLmdldFN0eWxlQm9yZGVyQ29sb3JBc1NyaW5nKGNoYXJ0LnNlcmllc1N0eWxlKSArIFwiXFxcIlwiO1xyXG4gICAgICAgICAgY2hhcnREaXNwbGF5TmFtZSA9IFwiXFxcImNoYXJ0RGlzcGxheU5hbWVcXFwiOlxcXCJcIiArIGNoYXJ0LmRhdGFlbGVtZW50ICsgXCJcXFwiXCI7XHJcbiAgICAgICAgICBjaGFydERpc3BsYXlMYWJlbCA9IFwiXFxcImNoYXJ0RGlzcGxheUxhYmVsXFxcIjpcXFwiXCIgKyBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdnZXRCdW5kbGUnLCAndGV4dCcsIGNoYXJ0LmRhdGFlbGVtZW50LCBjaGFydC5kYXRhZWxlbWVudCkgKyBcIlxcXCJcIjtcclxuICAgICAgICAgIC8vIGNoYXJ0RGlzcGxheUxhYmVsID0gXCJcXFwiY2hhcnREaXNwbGF5TGFiZWxcXFwiOlxcXCJcIiArIGNoYXJ0LmRhdGFlbGVtZW50ICsgXCJcXFwiXCI7XHJcbiAgICAgICAgICBjaGFydGxlZ2VuZERpc3BsYXlOYW1lID0gXCJcXFwiY2hhcnRsZWdlbmREaXNwbGF5TmFtZVxcXCI6XFxcIlwiICsgY2hhcnQuY2hhcnRJZCArIFwiXFxcIlwiO1xyXG4gICAgICAgICAgaXNWaXNpYmxlQ2hhcnQgPSBcIlxcXCJ2aXNpYmlsaXR5XFxcIjpcXFwiXCIgKyBpc1Zpc2libGVGcm9tTGVnZW5kICsgXCJcXFwiXCI7XHJcblxyXG5cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgZGF0YVRvUGFzcyA9IFwiXFxcImRhdGFcXFwiOltcIiArIHRoaXMuSlNPTkRhdGFTT0QuZ2V0VmFsdWUoc3RyKSArIFwiXVwiO1xyXG4gICAgICBkYXRhVG9QYXNzU09EID0gXCJcXFwiZGF0YVNPRFxcXCI6W1wiICsgdGhpcy5KU09ORGF0YU5PU09ELmdldFZhbHVlKHN0cikgKyBcIl1cIjtcclxuICAgICAgaWYgKHRoaXMuSlNPTkRhdGFTT0RBc1N0cmluZy5sZW5ndGggPT0gMClcclxuICAgICAgICB0aGlzLkpTT05EYXRhU09EQXNTdHJpbmcgKz0gXCJ7XCIgKyBwb2ludFN0YXJ0ICsgXCIsXCIgKyBwb2ludFN0YXJ0RW50aXR5ICsgXCIsXCIgKyBwb2ludEludGVydmFsICsgXCIsXCIgKyBkYXRhTGVuZ3RoICsgXCIsXCIgKyBjaGFydE5hbWUgKyBcIixcIiArIGlzVmlzaWJsZUNoYXJ0ICsgXCIsXCIgKyBjaGFydFR5cGVUb0Rpc3BsYXkgKyBcIixcIiArIGNoYXJ0VHlwZURldGFpbHNUb0Rpc3BsYXkgKyBcIixcIiArIGNoYXJ0U3R5bGVOYW1lICsgXCIsXCIgKyBjaGFydFN0eWxlQ29sb3IgKyBcIixcIiArIGNoYXJ0U3R5bGVCb3JkZXJDb2xvciArIFwiLFwiICsgY2hhcnREaXNwbGF5TmFtZSArIFwiLFwiICsgY2hhcnREaXNwbGF5TGFiZWwgKyBcIixcIiArIGNoYXJ0bGVnZW5kRGlzcGxheU5hbWUgKyBcIixcIiArIGRhdGFUb1Bhc3MgKyBcIixcIiArIGRhdGFUb1Bhc3NTT0QgKyBcIn1cIjtcclxuICAgICAgZWxzZVxyXG4gICAgICAgIHRoaXMuSlNPTkRhdGFTT0RBc1N0cmluZyArPSBcIix7XCIgKyBwb2ludFN0YXJ0ICsgXCIsXCIgKyBwb2ludFN0YXJ0RW50aXR5ICsgXCIsXCIgKyBwb2ludEludGVydmFsICsgXCIsXCIgKyBkYXRhTGVuZ3RoICsgXCIsXCIgKyBjaGFydE5hbWUgKyBcIixcIiArIGlzVmlzaWJsZUNoYXJ0ICsgXCIsXCIgKyBjaGFydFR5cGVUb0Rpc3BsYXkgKyBcIixcIiArIGNoYXJ0VHlwZURldGFpbHNUb0Rpc3BsYXkgKyBcIixcIiArIGNoYXJ0U3R5bGVOYW1lICsgXCIsXCIgKyBjaGFydFN0eWxlQ29sb3IgKyBcIixcIiArIGNoYXJ0U3R5bGVCb3JkZXJDb2xvciArIFwiLFwiICsgY2hhcnREaXNwbGF5TmFtZSArIFwiLFwiICsgY2hhcnREaXNwbGF5TGFiZWwgKyBcIixcIiArIGNoYXJ0bGVnZW5kRGlzcGxheU5hbWUgKyBcIixcIiArIGRhdGFUb1Bhc3MgKyBcIixcIiArIGRhdGFUb1Bhc3NTT0QgKyBcIn1cIjtcclxuXHJcblxyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICogU2hvdyBhbmQgaGlkZSB0aHJlc2hvbGRzIGxlZ2VuZFxyXG4gKi9cclxuICBwdWJsaWMgc2hvd0hpZGVUaHJlc2hvbGQoZ3JvdXBJZDogc3RyaW5nLCBzZWxlY3RlZDogYm9vbGVhbik6IHZvaWQge1xyXG5cclxuICAgIC8vIGZvciBlYWNoICh2YXIgc2VyaWVzOlNlcmllcyBpbiB0aGlzLnNlcmllcyl7XHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuc2VyaWVzTGlzdC5nZXRWYWx1ZXMoKS5sZW5ndGg7IGkrKykge1xyXG4gICAgICBsZXQgc2VyaWVzOiBTZXJpZXMgPSB0aGlzLnNlcmllc0xpc3QuZ2V0VmFsdWVzKClbaV07XHJcbiAgICAgIGlmIChzZXJpZXMuc2VyaWVzVHlwZSA9PSAnbGluZScpIHtcclxuICAgICAgICBpZiAoKHNlcmllcy55RmllbGQpLmluZGV4T2YoXCJUaHJlc2hvbGRzXCIpICE9IC0xICYmIChzZXJpZXMueUZpZWxkKS5pbmRleE9mKGdyb3VwSWQpICE9IC0xKSB7XHJcbiAgICAgICAgICBzZXJpZXMudmlzaWJsZSA9IHNlbGVjdGVkO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXRUaHJlc2hvbGRzQ29sb3JTdHlsZShjaGFydHMpOiBzdHJpbmcge1xyXG4gICAgLy9kZWZhdWx0IGNvbG9yXHJcbiAgICB2YXIgc3R5bGVDb2xvcjogc3RyaW5nID0gU2VyaWVzU3R5bGVQcm92aWRlci5EQVNIRURfU0VHTUVOVF9CTEFDSztcclxuICAgIGxldCBjaGFydHNMaXN0ID0gU3d0VXRpbC5jb252ZXJ0T2JqZWN0VG9BcnJheShjaGFydHMuY2hhcnQpO1xyXG4gICAgLy8gZm9yIGVhY2godmFyIGNoYXJ0IDpYTUwgaW4gY2hhcnRzLmNoaWxkcmVuKCkpe1xyXG4gICAgZm9yIChsZXQgayA9IDA7IGsgPCBjaGFydHNMaXN0Lmxlbmd0aDsgaysrKSB7XHJcbiAgICAgIGxldCBjaGFydCA9IGNoYXJ0c0xpc3Rba107XHJcbiAgICAgIGlmIChjaGFydC5kYXRhZWxlbWVudCA9PSBJTE1MaW5lQ2hhcnQuQUNUVUFMX0JBTEFOQ0VfVEFHKSB7XHJcbiAgICAgICAgc3R5bGVDb2xvciA9IGNoYXJ0LnNlcmllc1N0eWxlO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICByZXR1cm4gc3R5bGVDb2xvcjtcclxuICB9XHJcblxyXG4gIHB1YmxpYyBzd2l0Y2hEYXRhUHJvdmlkZXIoKTogdm9pZCB7XHJcbiAgICB2YXIgdGVtcERwID0gdGhpcy5kYXRhUHJvdmlkZXI7XHJcbiAgICB0aGlzLmRhdGFQcm92aWRlciA9IHRoaXMuTm9TT0RfRGF0YXByb3ZpZGVyO1xyXG4gICAgdGhpcy5Ob1NPRF9EYXRhcHJvdmlkZXIgPSB0ZW1wRHA7XHJcblxyXG4gIH1cclxuXHJcblxyXG5cclxuICBwdWJsaWMgY2FsY3VsYXRlTGlxdWlkaXR5Wm9uZXNMaW1pdGVzKGdyb3VwSWQ6IHN0cmluZywgc2NlbmFyaW9JZDogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICB2YXIgem9uZVN0YXJ0VmFsdWU6IG51bWJlciA9IE5hTjtcclxuICAgIHZhciB6b25lU3RhcnRWYWx1ZVNPRDogbnVtYmVyID0gTmFOO1xyXG4gICAgdmFyIHpvbmVFbmRWYWx1ZTogbnVtYmVyID0gTmFOO1xyXG4gICAgdmFyIHpvbmVFbmRWYWx1ZVNPRDogbnVtYmVyID0gTmFOO1xyXG5cclxuICAgIHZhciBkYXRhVG9QYXNzTk9TT0Q6IHN0cmluZztcclxuICAgIHZhciBkYXRhVG9QYXNTT0Q6IHN0cmluZztcclxuICAgIHZhciBjaGFydE5hbWU6IHN0cmluZztcclxuICAgIHZhciBjaGFydFR5cGVUb0Rpc3BsYXk6IHN0cmluZztcclxuICAgIHZhciBjaGFydHlGaWVsZDogc3RyaW5nO1xyXG4gICAgdmFyIGNoYXJ0U3R5bGVOYW1lOiBzdHJpbmc7XHJcbiAgICB2YXIgY2hhcnRDb2xvcjogc3RyaW5nO1xyXG4gICAgdmFyIGxpbWl0VmFsdWU6IG51bWJlciA9IDA7XHJcbiAgICB2YXIgbm9Nb3JlVmFsdWVzOiBudW1iZXIgPSAwO1xyXG5cclxuICAgIHZhciBncnBTY2VuS2V5OiBzdHJpbmcgPSAoZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiKTtcclxuICAgIHZhciBzdW1FeHRlcm5hbFNvZDogbnVtYmVyID0gdGhpcy5zdW1FeHRlcm5hbFNvZE1hcFtncm91cElkXSB8fCAwO1xyXG4gICAgdmFyIHZhbHVlc0NoYW5nZWQ6IGJvb2xlYW4gPSBmYWxzZTtcclxuICAgIHZhciBwcmV2dW91c0pTT05TZW50OiBzdHJpbmcgPSB0aGlzLkpzb25MaXF1ZGl0aXlab25lc05PU09EO1xyXG4gICAgdGhpcy5Kc29uTGlxdWRpdGl5Wm9uZXNOT1NPRCA9IFwiXCI7XHJcbiAgICAvLyBmb3IgZWFjaCh2YXIgYXNzZXQ6U3RyaW5nIGluIExJUVVJRElUWV9aT05FU19MSVNUKXtcclxuICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCBJTE1MaW5lQ2hhcnQuTElRVUlESVRZX1pPTkVTX0xJU1QubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgIGNvbnN0IGFzc2V0ID0gSUxNTGluZUNoYXJ0LkxJUVVJRElUWV9aT05FU19MSVNUW2luZGV4XTtcclxuICAgICAgY29uc3QgYXNzZXRzVmFsdWVBc051bWJlciA9IHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0VmFsdWVzW2dycFNjZW5LZXkgKyBhc3NldF0gPyB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgYXNzZXRdIDogMDtcclxuICAgICAgaWYgKGFzc2V0ICE9IFwiTk9fTU9SRV9MSVFVSURJVFlfQVZBSUxBQkxFXCIpIHtcclxuICAgICAgICB6b25lRW5kVmFsdWUgPSBOdW1iZXIobGltaXRWYWx1ZSArIGFzc2V0c1ZhbHVlQXNOdW1iZXIpICogKC0xKTtcclxuICAgICAgICB6b25lRW5kVmFsdWVTT0QgPSAtbGltaXRWYWx1ZSAtIE51bWJlcihhc3NldHNWYWx1ZUFzTnVtYmVyKSAtIHN1bUV4dGVybmFsU29kO1xyXG4gICAgICAgIHpvbmVTdGFydFZhbHVlID0gLWxpbWl0VmFsdWU7XHJcbiAgICAgICAgem9uZVN0YXJ0VmFsdWVTT0QgPSAtbGltaXRWYWx1ZSAtIHN1bUV4dGVybmFsU29kO1xyXG5cclxuXHJcbiAgICAgICAgY2hhcnROYW1lID0gXCJcXFwibmFtZVxcXCI6XFxcIlwiICsgZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXQgKyBcIlxcXCJcIjtcclxuICAgICAgICBjaGFydFR5cGVUb0Rpc3BsYXkgPSBcIlxcXCJ0eXBlXFxcIjpcXFwiYXJlYVxcXCJcIjtcclxuICAgICAgICBjaGFydFN0eWxlTmFtZSA9IFwiXFxcImNoYXJ0U3R5bGVOYW1lXFxcIjpcXFwiXCIgKyB0aGlzLmdldExpcXVpZGl0eVJlZ2lvbkNvbG9yKGFzc2V0KSArIFwiXFxcIlwiO1xyXG4gICAgICAgIGNoYXJ0Q29sb3IgPSBcIlxcXCJjb2xvclxcXCI6XFxcIlwiICsgU2VyaWVzU3R5bGVQcm92aWRlci5nZXRTdHlsZUNvbG9yQXNTcmluZyh0aGlzLmdldExpcXVpZGl0eVJlZ2lvbkNvbG9yKGFzc2V0KSkgKyBcIlxcXCJcIjtcclxuXHJcblxyXG4gICAgICAgIGRhdGFUb1Bhc3NOT1NPRCA9IFwiXFxcImRhdGFOb1NPRFxcXCI6W1wiICsgem9uZVN0YXJ0VmFsdWUgKyBcIixcIiArIHpvbmVFbmRWYWx1ZSArIFwiXVwiO1xyXG5cclxuICAgICAgICBkYXRhVG9QYXNTT0QgPSBcIlxcXCJkYXRhU09EXFxcIjpbXCIgKyB6b25lU3RhcnRWYWx1ZVNPRCArIFwiLFwiICsgem9uZUVuZFZhbHVlU09EICsgXCJdXCI7XHJcblxyXG4gICAgICAgIGlmICh0aGlzLkpzb25MaXF1ZGl0aXlab25lc05PU09ELmxlbmd0aCA9PSAwKVxyXG4gICAgICAgICAgdGhpcy5Kc29uTGlxdWRpdGl5Wm9uZXNOT1NPRCArPSBcIntcIiArIGNoYXJ0TmFtZSArIFwiLFwiICsgY2hhcnRUeXBlVG9EaXNwbGF5ICsgXCIsXCIgKyBjaGFydFN0eWxlTmFtZSArIFwiLFwiICsgY2hhcnRDb2xvciArIFwiLFwiICsgZGF0YVRvUGFzc05PU09EICsgXCIsXCIgKyBkYXRhVG9QYXNTT0QgKyBcIn1cIjtcclxuICAgICAgICBlbHNlXHJcbiAgICAgICAgICB0aGlzLkpzb25MaXF1ZGl0aXlab25lc05PU09EICs9IFwiLHtcIiArIGNoYXJ0TmFtZSArIFwiLFwiICsgY2hhcnRUeXBlVG9EaXNwbGF5ICsgXCIsXCIgKyBjaGFydFN0eWxlTmFtZSArIFwiLFwiICsgY2hhcnRDb2xvciArIFwiLFwiICsgZGF0YVRvUGFzc05PU09EICsgXCIsXCIgKyBkYXRhVG9QYXNTT0QgKyBcIn1cIjtcclxuXHJcblxyXG4gICAgICAgIGlmIChhc3NldCA9PSBcIlNVTV9PVEhFUl9UT1RBTFwiKSB7XHJcbiAgICAgICAgICBub01vcmVWYWx1ZXMgPSBOdW1iZXIobGltaXRWYWx1ZSArIGFzc2V0c1ZhbHVlQXNOdW1iZXIpICogKC0xKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgbGltaXRWYWx1ZSA9IE51bWJlcihsaW1pdFZhbHVlICsgYXNzZXRzVmFsdWVBc051bWJlcik7XHJcblxyXG5cclxuXHJcblxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNoYXJ0TmFtZSA9IFwiXFxcIm5hbWVcXFwiOlxcXCJcIiArIGdyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0ICsgXCJcXFwiXCI7XHJcbiAgICAgICAgY2hhcnRUeXBlVG9EaXNwbGF5ID0gXCJcXFwidHlwZVxcXCI6XFxcImxpbmVcXFwiXCI7XHJcbiAgICAgICAgY2hhcnRTdHlsZU5hbWUgPSBcIlxcXCJjaGFydFN0eWxlTmFtZVxcXCI6XFxcIlwiICsgdGhpcy5nZXRMaXF1aWRpdHlSZWdpb25Db2xvcihhc3NldCkgKyBcIlxcXCJcIjtcclxuICAgICAgICBjaGFydENvbG9yID0gXCJcXFwiY29sb3JcXFwiOlxcXCJcIiArIFNlcmllc1N0eWxlUHJvdmlkZXIuZ2V0U3R5bGVDb2xvckFzU3JpbmcodGhpcy5nZXRMaXF1aWRpdHlSZWdpb25Db2xvcihhc3NldCkpICsgXCJcXFwiXCI7XHJcbiAgICAgICAgZGF0YVRvUGFzU09EID0gXCJcXFwiZGF0YVNPRFxcXCI6W1wiICsgKG5vTW9yZVZhbHVlcyAtIHN1bUV4dGVybmFsU29kKSArIFwiLFwiICsgKG5vTW9yZVZhbHVlcyAtIHN1bUV4dGVybmFsU29kKSArIFwiXVwiO1xyXG5cclxuICAgICAgICBkYXRhVG9QYXNzTk9TT0QgPSBcIlxcXCJkYXRhTm9TT0RcXFwiOltcIiArIChub01vcmVWYWx1ZXMpICsgXCIsXCIgKyAobm9Nb3JlVmFsdWVzKSArIFwiXVwiO1xyXG5cclxuICAgICAgICBpZiAodGhpcy5Kc29uTGlxdWRpdGl5Wm9uZXNOT1NPRC5sZW5ndGggPT0gMClcclxuICAgICAgICAgIHRoaXMuSnNvbkxpcXVkaXRpeVpvbmVzTk9TT0QgKz0gXCJ7XCIgKyBjaGFydE5hbWUgKyBcIixcIiArIGNoYXJ0VHlwZVRvRGlzcGxheSArIFwiLFwiICsgY2hhcnRTdHlsZU5hbWUgKyBcIixcIiArIGNoYXJ0Q29sb3IgKyBcIixcIiArIGRhdGFUb1Bhc3NOT1NPRCArIFwiLFwiICsgZGF0YVRvUGFzU09EICsgXCJ9XCI7XHJcbiAgICAgICAgZWxzZVxyXG4gICAgICAgICAgdGhpcy5Kc29uTGlxdWRpdGl5Wm9uZXNOT1NPRCArPSBcIix7XCIgKyBjaGFydE5hbWUgKyBcIixcIiArIGNoYXJ0VHlwZVRvRGlzcGxheSArIFwiLFwiICsgY2hhcnRTdHlsZU5hbWUgKyBcIixcIiArIGNoYXJ0Q29sb3IgKyBcIixcIiArIGRhdGFUb1Bhc3NOT1NPRCArIFwiLFwiICsgZGF0YVRvUGFzU09EICsgXCJ9XCI7XHJcbiAgICAgIH1cclxuXHJcbiAgICB9XHJcbiAgICBpZiAocHJldnVvdXNKU09OU2VudCAhPSB0aGlzLkpzb25MaXF1ZGl0aXlab25lc05PU09EKSB7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICB9XHJcblxyXG4gIHB1YmxpYyByZW1vdmVMaXF1aWRpdHlSZWdpb24oZ3JvdXBJZDogc3RyaW5nLCBzY2VuYXJpb0lkOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIHZhciB5RmllbGQ6IHN0cmluZyA9IGdyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIjtcclxuICAgIHZhciBhc3NldDogc3RyaW5nID0gbnVsbDtcclxuICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCBJTE1MaW5lQ2hhcnQuTElRVUlESVRZX1pPTkVTX0xJU1QubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgIGNvbnN0IGFzc2V0ID0gSUxNTGluZUNoYXJ0LkxJUVVJRElUWV9aT05FU19MSVNUW2luZGV4XTtcclxuICAgICAgdGhpcy5zZXJpZXNMaXN0LnJlbW92ZSh5RmllbGQgKyBhc3NldCk7XHJcbiAgICB9XHJcbiAgICAvL1JlbW92ZSBoaWdobGlnaHRpbmcgY2F1c2VkIGJ5IHNpbWlsYXIgY29sb3JzIGluIHRoZSBsZWdlbmRcclxuICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmhpZ2hsaWdodGVkTGlxdWlkaXR5U2VyaWVzLmxlbmd0aDsgaW5kZXgrKykge1xyXG4gICAgICBjb25zdCBzZXJpZSA9IHRoaXMuaGlnaGxpZ2h0ZWRMaXF1aWRpdHlTZXJpZXNbaW5kZXhdO1xyXG4gICAgICB2YXIgZHRvOiBhbnkgPSB7fTtcclxuICAgICAgZHRvLmhpZ2hsaWdoID0gZmFsc2U7XHJcbiAgICAgIGR0by55RmllbGQgPSBzZXJpZS55RmllbGQ7XHJcbiAgICAgIGlmICgkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5jbG9zZXN0KCcubGVnZW5kc0RpdmlkZXInKSkge1xyXG4gICAgICAgIGR0by5wYXJlbnRUYWIgPSAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5jbG9zZXN0KCcubGVnZW5kc0RpdmlkZXInKS5hdHRyKCduYW1lJyk7XHJcbiAgICAgIH1cclxuICAgICAgU2VyaWVzSGlnaGxpZ2h0RXZlbnQuZW1pdChkdG8pO1xyXG4gICAgICAvLyBEaXNwYXRjaCB0aGUgZXZlbnQgc28gdGhhdCBsZWdlbmRzIHdpbGwgZGVsZXRlIGhpZ2hsaWdodFxyXG4gICAgfVxyXG4gICAgdGhpcy5oaWdobGlnaHRlZExpcXVpZGl0eVNlcmllcy5wb3AoKTtcclxuICB9XHJcblxyXG5cclxuXHJcbiAgcHVibGljIGRyYXdMaXF1aWRpdHlab25lcyhncm91cElkOiBzdHJpbmcsIHNjZW5hcmlvSWQ6IHN0cmluZywgbGFzdFNlbGVjdGVkR3JvdXA6IHN0cmluZyA9IG51bGwsIGxhc3RTZWxlY3RTY2VuYXJpbzogc3RyaW5nID0gbnVsbCwgd2l0aFNPRDogYm9vbGVhbiA9IGZhbHNlLCB2aXNpYmxpdHk6IGJvb2xlYW4gPSB0cnVlKTogdm9pZCB7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAodGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoICYmIGdyb3VwSWQgJiYgc2NlbmFyaW9JZCkge1xyXG5cclxuICAgICAgICB2YXIgZ3JwU2NlbktleTogc3RyaW5nID0gKGdyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIilcclxuICAgICAgICB2YXIgc3VtRXh0ZXJuYWxTb2Q6IG51bWJlciA9IHRoaXMuc3VtRXh0ZXJuYWxTb2RNYXBbZ3JvdXBJZF07XHJcbiAgICAgICAgLy8gdmFyIGZpcnN0SXRlbV9iZWZvcmU6T2JqZWN0PWRhdGFQcm92aWRlci5nZXRJdGVtQXQoMCk7XHJcbiAgICAgICAgLy8gdmFyIGxhc3RJdGVtX2JlZm9yZTpPYmplY3Q9ZGF0YVByb3ZpZGVyLmdldEl0ZW1BdChkYXRhUHJvdmlkZXIubGVuZ3RoIC0gMSk7XHJcbiAgICAgICAgLy8gdmFyIGZpcnN0SXRlbVNvZF9iZWZvcmU6T2JqZWN0ID0gTm9TT0RfRGF0YXByb3ZpZGVyLmdldEl0ZW1BdCgwKTtcclxuICAgICAgICAvLyB2YXIgbGFzdEl0ZW1Tb2RfYmVmb3JlOk9iamVjdCA9IE5vU09EX0RhdGFwcm92aWRlci5nZXRJdGVtQXQoTm9TT0RfRGF0YXByb3ZpZGVyLmxlbmd0aCAtIDEpO1xyXG4gICAgICAgIHZhciBmaXJzdEl0ZW1fYmVmb3JlOiBPYmplY3QgPSB0aGlzLmRhdGFQcm92aWRlclswXTtcclxuICAgICAgICB2YXIgbGFzdEl0ZW1fYmVmb3JlOiBPYmplY3QgPSB0aGlzLmRhdGFQcm92aWRlclt0aGlzLmRhdGFQcm92aWRlci5sZW5ndGggLSAxXTtcclxuICAgICAgICB2YXIgZmlyc3RJdGVtU29kX2JlZm9yZTogT2JqZWN0ID0gdGhpcy5Ob1NPRF9EYXRhcHJvdmlkZXJbMF07XHJcbiAgICAgICAgdmFyIGxhc3RJdGVtU29kX2JlZm9yZTogT2JqZWN0ID0gdGhpcy5Ob1NPRF9EYXRhcHJvdmlkZXJbdGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoIC0gMV07XHJcbiAgICAgICAgdmFyIGxpbWl0VmFsdWU6IG51bWJlciA9IDA7XHJcbiAgICAgICAgdmFyIG5vTW9yZVZhbHVlczogbnVtYmVyID0gMDtcclxuXHJcbiAgICAgICAgLy8gKHRoaXMudkF4aXNMZWZ0IGFzIEN1c3RvbUxpbmVhckF4aXMpLnJlY2FsY3VsYXRlTWF4TWluKCk7XHJcbiAgICAgICAgLy8gbWluID0gdkF4aXNMZWZ0Lm1pbmltdW07XHJcblxyXG4gICAgICAgIHZhciB6b25lU3RhcnRWYWx1ZTogbnVtYmVyID0gTmFOO1xyXG4gICAgICAgIHZhciB6b25lU3RhcnRWYWx1ZVNPRDogbnVtYmVyID0gTmFOO1xyXG4gICAgICAgIHZhciB6b25lRW5kVmFsdWU6IG51bWJlciA9IE5hTjtcclxuICAgICAgICB2YXIgem9uZUVuZFZhbHVlU09EOiBudW1iZXIgPSBOYU47XHJcblxyXG5cclxuICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgSUxNTGluZUNoYXJ0LkxJUVVJRElUWV9aT05FU19MSVNULmxlbmd0aDsgaW5kZXgrKykge1xyXG4gICAgICAgICAgY29uc3QgYXNzZXQgPSBJTE1MaW5lQ2hhcnQuTElRVUlESVRZX1pPTkVTX0xJU1RbaW5kZXhdO1xyXG4gICAgICAgICAgaWYgKGFzc2V0ICE9IFwiTk9fTU9SRV9MSVFVSURJVFlfQVZBSUxBQkxFXCIpIHtcclxuICAgICAgICAgICAgem9uZUVuZFZhbHVlID0gTnVtYmVyKGxpbWl0VmFsdWUgKyB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgYXNzZXRdKSAqICgtMSk7XHJcbiAgICAgICAgICAgIHpvbmVFbmRWYWx1ZVNPRCA9IC1saW1pdFZhbHVlIC0gTnVtYmVyKHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0VmFsdWVzW2dycFNjZW5LZXkgKyBhc3NldF0pIC0gc3VtRXh0ZXJuYWxTb2Q7XHJcbiAgICAgICAgICAgIHpvbmVTdGFydFZhbHVlID0gLWxpbWl0VmFsdWU7XHJcbiAgICAgICAgICAgIHpvbmVTdGFydFZhbHVlU09EID0gLWxpbWl0VmFsdWUgLSBzdW1FeHRlcm5hbFNvZDtcclxuICAgICAgICAgICAgLy9GSVhNRTpDSEVDSyBJRiBORUVERURcclxuICAgICAgICAgICAgLy8gaWYoKHpvbmVFbmRWYWx1ZSA8IG1pbiAmJiB6b25lU3RhcnRWYWx1ZSA+IG1pbikpXHJcbiAgICAgICAgICAgIC8vIFx0em9uZUVuZFZhbHVlID0gbWluO1xyXG5cclxuICAgICAgICAgICAgLy8gaWYoKHpvbmVFbmRWYWx1ZVNPRCA8IG1pbiAmJiB6b25lU3RhcnRWYWx1ZVNPRCA+IG1pbikpXHJcbiAgICAgICAgICAgIC8vIFx0em9uZUVuZFZhbHVlU09EID0gbWluO1xyXG5cclxuICAgICAgICAgICAgaWYgKHRoaXMuaW5jbHVkZVNPRCkge1xyXG4gICAgICAgICAgICAgIGZpcnN0SXRlbVNvZF9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gem9uZUVuZFZhbHVlU09EO1xyXG4gICAgICAgICAgICAgIGZpcnN0SXRlbV9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gem9uZUVuZFZhbHVlO1xyXG4gICAgICAgICAgICAgIGxhc3RJdGVtU29kX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldF0gPSB6b25lRW5kVmFsdWVTT0Q7XHJcbiAgICAgICAgICAgICAgbGFzdEl0ZW1fYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IHpvbmVFbmRWYWx1ZTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1fYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IHpvbmVFbmRWYWx1ZVNPRDtcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1Tb2RfYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IHpvbmVFbmRWYWx1ZTtcclxuICAgICAgICAgICAgICBsYXN0SXRlbV9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gem9uZUVuZFZhbHVlU09EO1xyXG4gICAgICAgICAgICAgIGxhc3RJdGVtU29kX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldF0gPSB6b25lRW5kVmFsdWU7XHJcbiAgICAgICAgICAgIH1cclxuXHJcblxyXG4gICAgICAgICAgICBpZiAoYXNzZXQgPT0gXCJTVU1fT1RIRVJfVE9UQUxcIikge1xyXG4gICAgICAgICAgICAgIG5vTW9yZVZhbHVlcyA9IE51bWJlcihsaW1pdFZhbHVlICsgdGhpcy5saXF1aWRpdHlab25lc0xpc3RWYWx1ZXNbZ3JwU2NlbktleSArIGFzc2V0XSkgKiAoLTEpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAodGhpcy5pbmNsdWRlU09EKSB7XHJcbiAgICAgICAgICAgICAgZmlyc3RJdGVtX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldCArIFwiTGltaXRcIl0gPSB6b25lU3RhcnRWYWx1ZTtcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1Tb2RfYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0ICsgXCJMaW1pdFwiXSA9IHpvbmVTdGFydFZhbHVlU09EO1xyXG4gICAgICAgICAgICAgIGxhc3RJdGVtX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldCArIFwiTGltaXRcIl0gPSB6b25lU3RhcnRWYWx1ZTtcclxuICAgICAgICAgICAgICBsYXN0SXRlbVNvZF9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXQgKyBcIkxpbWl0XCJdID0gem9uZVN0YXJ0VmFsdWVTT0Q7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgZmlyc3RJdGVtU29kX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldCArIFwiTGltaXRcIl0gPSB6b25lU3RhcnRWYWx1ZTtcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1fYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0ICsgXCJMaW1pdFwiXSA9IHpvbmVTdGFydFZhbHVlU09EO1xyXG4gICAgICAgICAgICAgIGxhc3RJdGVtU29kX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldCArIFwiTGltaXRcIl0gPSB6b25lU3RhcnRWYWx1ZTtcclxuICAgICAgICAgICAgICBsYXN0SXRlbV9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXQgKyBcIkxpbWl0XCJdID0gem9uZVN0YXJ0VmFsdWVTT0Q7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0TGltaXRzLnB1dChncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldCwgbGltaXRWYWx1ZSk7XHJcbiAgICAgICAgICAgIGxpbWl0VmFsdWUgPSBOdW1iZXIobGltaXRWYWx1ZSArIHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0VmFsdWVzW2dycFNjZW5LZXkgKyBhc3NldF0pO1xyXG5cclxuXHJcblxyXG5cclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLmluY2x1ZGVTT0QpIHtcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1fYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IG5vTW9yZVZhbHVlcztcclxuICAgICAgICAgICAgICBmaXJzdEl0ZW1Tb2RfYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IG5vTW9yZVZhbHVlcyAtIHN1bUV4dGVybmFsU29kO1xyXG4gICAgICAgICAgICAgIGxhc3RJdGVtX2JlZm9yZVtncm91cElkICsgXCIuXCIgKyBzY2VuYXJpb0lkICsgXCIuXCIgKyBhc3NldF0gPSBub01vcmVWYWx1ZXM7XHJcbiAgICAgICAgICAgICAgbGFzdEl0ZW1Tb2RfYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IG5vTW9yZVZhbHVlcyAtIHN1bUV4dGVybmFsU29kO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGZpcnN0SXRlbVNvZF9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gbm9Nb3JlVmFsdWVzO1xyXG4gICAgICAgICAgICAgIGZpcnN0SXRlbV9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gbm9Nb3JlVmFsdWVzIC0gc3VtRXh0ZXJuYWxTb2Q7XHJcbiAgICAgICAgICAgICAgbGFzdEl0ZW1Tb2RfYmVmb3JlW2dyb3VwSWQgKyBcIi5cIiArIHNjZW5hcmlvSWQgKyBcIi5cIiArIGFzc2V0XSA9IG5vTW9yZVZhbHVlcztcclxuICAgICAgICAgICAgICBsYXN0SXRlbV9iZWZvcmVbZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiICsgYXNzZXRdID0gbm9Nb3JlVmFsdWVzIC0gc3VtRXh0ZXJuYWxTb2Q7XHJcblxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1tncnBTY2VuS2V5ICsgXCJOT19NT1JFX0xJUVVJRElUWV9BVkFJTEFCTEVcIl0gPSBub01vcmVWYWx1ZXM7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuZHJhd0xpcXVpZGl0eVJlZ2lvbihncm91cElkLCBzY2VuYXJpb0lkLCBsYXN0U2VsZWN0ZWRHcm91cCwgbGFzdFNlbGVjdFNjZW5hcmlvLCB2aXNpYmxpdHkpO1xyXG4gICAgICAgIC8vUnVuIGFjdGlvbiBvbiBJRlJBTUVcclxuICAgICAgICB2YXIgaXNVcGRhdGVkOiBib29sZWFuID0gdGhpcy5jYWxjdWxhdGVMaXF1aWRpdHlab25lc0xpbWl0ZXMoZ3JvdXBJZCwgc2NlbmFyaW9JZCk7XHJcbiAgICAgICAgdGhpcy5wYXJlbnREb2N1bWVudC5yZWZyZXNoU291cmNlT2ZMaXF1aWRpdHlGb3JJRnJhbWUoaXNVcGRhdGVkKTtcclxuXHJcblxyXG4gICAgICB9XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICB9XHJcbiAgfVxyXG4gIHB1YmxpYyBkcmF3TGlxdWlkaXR5UmVnaW9uKGdyb3VwSWQ6IHN0cmluZywgc2NlbmFyaW9JZDogc3RyaW5nLCBsYXN0U2VsZWN0ZWRHcm91cDogc3RyaW5nID0gbnVsbCwgbGFzdFNlbGVjdFNjZW5hcmlvOiBzdHJpbmcgPSBudWxsLCB2aXNpYmxpdHk6IGJvb2xlYW4gPSB0cnVlKTogdm9pZCB7XHJcbiAgICB2YXIgZXJyb3JMb2NhdGlvbjogbnVtYmVyID0gMDtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmIChncm91cElkICE9IG51bGwgJiYgc2NlbmFyaW9JZCAhPSBudWxsKSB7XHJcbiAgICAgICAgdmFyIHlGaWVsZDogc3RyaW5nID0gZ3JvdXBJZCArIFwiLlwiICsgc2NlbmFyaW9JZCArIFwiLlwiO1xyXG4gICAgICAgIHZhciBhc3NldFZhbHVlOiBzdHJpbmc7XHJcblxyXG4gICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCBJTE1MaW5lQ2hhcnQuTElRVUlESVRZX1pPTkVTX0xJU1QubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgICBjb25zdCBhc3NldCA9IElMTUxpbmVDaGFydC5MSVFVSURJVFlfWk9ORVNfTElTVFtpbmRleF07XHJcbiAgICAgICAgICBpZiAoYXNzZXQgIT0gJ05PX01PUkVfTElRVUlESVRZX0FWQUlMQUJMRScpIHtcclxuXHJcbiAgICAgICAgICAgIC8vIHZhciBhcmVhU2VyaWVzOkN1c3RvbUFyZWFTZXJpZXMgPSBuZXcgQ3VzdG9tQXJlYVNlcmllcygpOyBcdFxyXG4gICAgICAgICAgICBsZXQgYXJlYVNlcmllczogU2VyaWVzID0gbmV3IFNlcmllcztcclxuICAgICAgICAgICAgYXJlYVNlcmllcy52aXNpYmxlID0gdmlzaWJsaXR5O1xyXG4gICAgICAgICAgICBpZiAodGhpcy5wYXJlbnREb2N1bWVudC5jdXJyZW5jeUZvcm1hdCA9PSAnY3VycmVuY3lQYXQxJykge1xyXG4gICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAyNztcclxuICAgICAgICAgICAgICB2YXIgYW1vdW50OiBzdHJpbmcgPSB0aGlzLmxpcXVpZGl0eVpvbmVzTGlzdFZhbHVlc1t5RmllbGQgKyBhc3NldF0gfHwgMDs7XHJcbiAgICAgICAgICAgICAgLy8gYXNzZXRWYWx1ZSA9IHRoaXMubGVmdFZlcnRpY2FsQXhpc0Zvcm1hdHRlcihOdW1iZXIoYW1vdW50KSk7XHJcbiAgICAgICAgICAgICAgYXNzZXRWYWx1ZSA9IHRoaXMucGFyZW50RG9jdW1lbnQubGVmdFZlcnRpY2FsQXhpc0Zvcm1hdHRlcihOdW1iZXIoYW1vdW50KSk7XHJcbiAgICAgICAgICAgICAgYXNzZXRWYWx1ZSA9IGFzc2V0VmFsdWUucmVwbGFjZShuZXcgUmVnRXhwKCdcXFxcICcsICdnJyksICcsJyk7XHJcbiAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDI7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgdmFyIGFtb3VudDogc3RyaW5nID0gdGhpcy5saXF1aWRpdHlab25lc0xpc3RWYWx1ZXNbeUZpZWxkICsgYXNzZXRdIHx8IDA7O1xyXG4gICAgICAgICAgICAgIC8vIGFzc2V0VmFsdWUgPSB0aGlzLmxlZnRWZXJ0aWNhbEF4aXNGb3JtYXR0ZXIoTnVtYmVyKGFtb3VudCkpO1xyXG4gICAgICAgICAgICAgIGFzc2V0VmFsdWUgPSB0aGlzLnBhcmVudERvY3VtZW50LmxlZnRWZXJ0aWNhbEF4aXNGb3JtYXR0ZXIoTnVtYmVyKGFtb3VudCkpO1xyXG4gICAgICAgICAgICAgIGFzc2V0VmFsdWUgPSBhc3NldFZhbHVlLnJlcGxhY2UobmV3IFJlZ0V4cCgnXFxcXCAnLCAnZycpLCAnLicpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGFyZWFTZXJpZXMubGVnZW5kRGlzcGxheU5hbWUgPSBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdnZXRCdW5kbGUnLCAndGV4dCcsIGFzc2V0LCBhc3NldCkgKyBcInxcIiArIGFzc2V0VmFsdWU7XHJcblxyXG4gICAgICAgICAgICB2YXIgYXJlYUNvbG9yOiBzdHJpbmcgPSB0aGlzLmdldExpcXVpZGl0eVJlZ2lvbkNvbG9yKGFzc2V0KTtcclxuICAgICAgICAgICAgdmFyIG1pbkZpZWxkOiBzdHJpbmcgPSB5RmllbGQgKyBhc3NldCArIFwiTGltaXRcIjtcclxuICAgICAgICAgICAgaWYgKHRoaXMuaXNFbnRpdHlUaW1lZnJhbWUpXHJcbiAgICAgICAgICAgICAgYXJlYVNlcmllcy54RmllbGQgPSBcInRpbWVTbG90RVwiO1xyXG4gICAgICAgICAgICBlbHNlXHJcbiAgICAgICAgICAgICAgYXJlYVNlcmllcy54RmllbGQgPSBcInRpbWVTbG90XCI7XHJcblxyXG4gICAgICAgICAgICBhcmVhU2VyaWVzLmFwcGxpZWRTdHlsZSA9IGFyZWFDb2xvcjtcclxuICAgICAgICAgICAgYXJlYVNlcmllcy55RmllbGQgPSB5RmllbGQgKyBhc3NldDtcclxuICAgICAgICAgICAgYXJlYVNlcmllcy5taW5GaWVsZCA9IG1pbkZpZWxkO1xyXG4gICAgICAgICAgICBhcmVhU2VyaWVzLnNlcmllc1R5cGUgPSAnYXJlYSc7XHJcbiAgICAgICAgICAgIHRoaXMuc2VyaWVzTGlzdC5wdXQoeUZpZWxkICsgYXNzZXQsIGFyZWFTZXJpZXMpO1xyXG5cclxuICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgbGV0IGxpbmVTZXJpZXM6IFNlcmllcyA9IG5ldyBTZXJpZXM7XHJcbiAgICAgICAgICAgIGxpbmVTZXJpZXMudmlzaWJsZSA9IHZpc2libGl0eTtcclxuICAgICAgICAgICAgaWYgKHRoaXMucGFyZW50RG9jdW1lbnQuY3VycmVuY3lGb3JtYXQgPT0gJ2N1cnJlbmN5UGF0MScpIHtcclxuICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMjc7XHJcbiAgICAgICAgICAgICAgdmFyIGFtb3VudDogc3RyaW5nID0gdGhpcy5saXF1aWRpdHlab25lc0xpc3RWYWx1ZXNbeUZpZWxkICsgYXNzZXRdIHx8IDA7O1xyXG4gICAgICAgICAgICAgIGFzc2V0VmFsdWUgPSB0aGlzLnBhcmVudERvY3VtZW50LmxlZnRWZXJ0aWNhbEF4aXNGb3JtYXR0ZXIoTnVtYmVyKGFtb3VudCkpO1xyXG4gICAgICAgICAgICAgIGFzc2V0VmFsdWUgPSBhc3NldFZhbHVlLnJlcGxhY2UobmV3IFJlZ0V4cCgnXFxcXCAnLCAnZycpLCAnLCcpO1xyXG4gICAgICAgICAgICAgIGVycm9yTG9jYXRpb24gPSAyO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIHZhciBhbW91bnQ6IHN0cmluZyA9IHRoaXMubGlxdWlkaXR5Wm9uZXNMaXN0VmFsdWVzW3lGaWVsZCArIGFzc2V0XSB8fCAwOztcclxuICAgICAgICAgICAgICBhc3NldFZhbHVlID0gdGhpcy5wYXJlbnREb2N1bWVudC5sZWZ0VmVydGljYWxBeGlzRm9ybWF0dGVyKE51bWJlcihhbW91bnQpKTtcclxuICAgICAgICAgICAgICBhc3NldFZhbHVlID0gYXNzZXRWYWx1ZS5yZXBsYWNlKG5ldyBSZWdFeHAoJ1xcXFwgJywgJ2cnKSwgJy4nKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBsaW5lU2VyaWVzLmRpc3BsYXlOYW1lID0geUZpZWxkICsgYXNzZXQ7XHJcbiAgICAgICAgICAgIGxpbmVTZXJpZXMubGVnZW5kRGlzcGxheU5hbWUgPSBFeHRlcm5hbEludGVyZmFjZS5jYWxsKCdnZXRCdW5kbGUnLCAndGV4dCcsIGFzc2V0LCBhc3NldCkgKyBcInxcIiArIGFzc2V0VmFsdWU7XHJcbiAgICAgICAgICAgIHZhciBsaW5lQ29sb3I6IHN0cmluZyA9IHRoaXMuZ2V0TGlxdWlkaXR5UmVnaW9uQ29sb3IoYXNzZXQpO1xyXG4gICAgICAgICAgICBsaW5lU2VyaWVzLmFwcGxpZWRTdHlsZSA9IGxpbmVDb2xvcjtcclxuICAgICAgICAgICAgbGluZVNlcmllcy5zZXJpZXNUeXBlID0gJ2xpbmUnO1xyXG4gICAgICAgICAgICBpZiAodGhpcy5pc0VudGl0eVRpbWVmcmFtZSlcclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLnhGaWVsZCA9IFwidGltZVNsb3RFXCI7XHJcbiAgICAgICAgICAgIGVsc2VcclxuICAgICAgICAgICAgICBsaW5lU2VyaWVzLnhGaWVsZCA9IFwidGltZVNsb3RcIjtcclxuICAgICAgICAgICAgbGluZVNlcmllcy55RmllbGQgPSB5RmllbGQgKyBhc3NldDtcclxuICAgICAgICAgICAgdGhpcy5zZXJpZXNMaXN0LnB1dCh5RmllbGQgKyBhc3NldCwgbGluZVNlcmllcyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoIVN0cmluZ1V0aWxzLmlzRW1wdHkobGFzdFNlbGVjdGVkR3JvdXApICYmICFTdHJpbmdVdGlscy5pc0VtcHR5KGxhc3RTZWxlY3RTY2VuYXJpbykpIHtcclxuICAgICAgICAgIHRoaXMucmVtb3ZlTGlxdWlkaXR5UmVnaW9uKGxhc3RTZWxlY3RlZEdyb3VwLCBsYXN0U2VsZWN0U2NlbmFyaW8pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdGhpcy51cGRhdGVMaXF1aWRpdHlab25lc0xlZ2VuZCh0cnVlKTtcclxuICAgICAgICAvL0ZJWE1FOlxyXG4gICAgICAgIC8vIGNhbGxMYXRlcihkaXN0aW5jdFNpbWlsYXJDb2xvclNlcmllcyk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgaWYgKGxhc3RTZWxlY3RlZEdyb3VwICE9IG51bGwgJiYgbGFzdFNlbGVjdFNjZW5hcmlvICE9IG51bGwpIHtcclxuICAgICAgICAgIHRoaXMucmVtb3ZlTGlxdWlkaXR5UmVnaW9uKGxhc3RTZWxlY3RlZEdyb3VwLCBsYXN0U2VsZWN0U2NlbmFyaW8pO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLnVwZGF0ZUxpcXVpZGl0eVpvbmVzTGVnZW5kKGZhbHNlKTtcclxuICAgICAgfVxyXG5cclxuICAgIH1cclxuICAgIGNhdGNoIChlcnJvcikge1xyXG4gICAgICBpZiAoIVN0cmluZ1V0aWxzLmlzRW1wdHkobGFzdFNlbGVjdGVkR3JvdXApICYmICFTdHJpbmdVdGlscy5pc0VtcHR5KGxhc3RTZWxlY3RTY2VuYXJpbykpIHtcclxuICAgICAgICB0aGlzLnJlbW92ZUxpcXVpZGl0eVJlZ2lvbihsYXN0U2VsZWN0ZWRHcm91cCwgbGFzdFNlbGVjdFNjZW5hcmlvKTtcclxuICAgICAgfVxyXG4gICAgICB0aGlzLl9hc3NldHNMZWdlbmQuZGF0YVByb3ZpZGVyID0gbmV3IEFycmF5KCk7XHJcbiAgICAgIC8vIEFsZXJ0LnNob3coXCJObyBkYXRhIGFyZSBhdmFpbGFibGVzICwgbmVlZCB0byBzZWxlY3QgKGF0IGxlYXN0KW9uZSBmaWd1cmUgZm9yIHRoZSBjb3JyZXNwb25kIGdyb3VwIFtcIitncm91cElkK1wiXSBhbmQgc2NlbmFyaW8gWyBcIitzY2VuYXJpb0lkK1wiXVwiKVxyXG4gICAgICBFeHRlcm5hbEludGVyZmFjZS5jYWxsKFwiY29uc29sZS5sb2dcIiwgXCJObyBkYXRhIGFyZSBhdmFpbGFibGVzICwgbmVlZCB0byBzZWxlY3QgKGF0IGxlYXN0KW9uZSBmaWd1cmUgZm9yIHRoZSBjb3JyZXNwb25kIGdyb3VwOiBcIiArIGdyb3VwSWQpO1xyXG4gICAgfVxyXG5cclxuICB9XHJcblxyXG5cclxuICBjYWxsTWV0aG9kSW5JZnJhbWUobWV0aG9kLCBkYXRhOiBhbnkpIHtcclxuICAgIC8vIHZhciBvID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeVRhZ05hbWUoJ2lmcmFtZScpWzBdO1xyXG4gICAgdGhpcy5jaGFydEVsZW1lbnQuY2FsbE1ldGhvZEJ5TmFtZShtZXRob2QsIGRhdGEpXHJcbiAgICAvLyBpZiAodGhpcy5pZnJhbWVDb250YWllciAmJiB0aGlzLmlmcmFtZUNvbnRhaWVyLmNvbnRlbnRXaW5kb3cpIHtcclxuICAgIC8vICAgdGhpcy5pZnJhbWVDb250YWllci5jb250ZW50V2luZG93LnBvc3RNZXNzYWdlKFttZXRob2QsIGRhdGFdLCAnKicpO1xyXG4gICAgLy8gfVxyXG4gIH1cclxuXHJcblxyXG4gIC8vIGdldElmcmFtZVdpbmRvdyhpZnJhbWVfb2JqZWN0KSB7XHJcbiAgLy8gICB2YXIgZG9jO1xyXG5cclxuICAvLyAgIGlmIChpZnJhbWVfb2JqZWN0LmNvbnRlbnRXaW5kb3cpIHtcclxuICAvLyAgICAgcmV0dXJuIGlmcmFtZV9vYmplY3QuY29udGVudFdpbmRvdztcclxuICAvLyAgIH1cclxuXHJcbiAgLy8gICBpZiAoaWZyYW1lX29iamVjdC53aW5kb3cpIHtcclxuICAvLyAgICAgcmV0dXJuIGlmcmFtZV9vYmplY3Qud2luZG93O1xyXG4gIC8vICAgfVxyXG5cclxuICAvLyAgIGlmICghZG9jICYmIGlmcmFtZV9vYmplY3QuY29udGVudERvY3VtZW50KSB7XHJcbiAgLy8gICAgIGRvYyA9IGlmcmFtZV9vYmplY3QuY29udGVudERvY3VtZW50O1xyXG4gIC8vICAgfVxyXG5cclxuICAvLyAgIGlmICghZG9jICYmIGlmcmFtZV9vYmplY3QuZG9jdW1lbnQpIHtcclxuICAvLyAgICAgZG9jID0gaWZyYW1lX29iamVjdC5kb2N1bWVudDtcclxuICAvLyAgIH1cclxuXHJcbiAgLy8gICBpZiAoZG9jICYmIGRvYy5kZWZhdWx0Vmlldykge1xyXG4gIC8vICAgICByZXR1cm4gZG9jLmRlZmF1bHRWaWV3O1xyXG4gIC8vICAgfVxyXG5cclxuICAvLyAgIGlmIChkb2MgJiYgZG9jLnBhcmVudFdpbmRvdykge1xyXG4gIC8vICAgICByZXR1cm4gZG9jLnBhcmVudFdpbmRvdztcclxuICAvLyAgIH1cclxuXHJcbiAgLy8gICByZXR1cm4gdW5kZWZpbmVkO1xyXG4gIC8vIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXRMaXF1aWRpdHlSZWdpb25Db2xvcihhc3NldE5hbWU6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgICB2YXIgY29sb3I6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBpZiAoYXNzZXROYW1lID09IFwiU1VNX0NSRURJVF9MSU5FX1RPVEFMXCIpXHJcbiAgICAgIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQU5USVFVRV9XSElURTtcclxuICAgIGVsc2UgaWYgKGFzc2V0TmFtZSA9PSBcIlNVTV9DT0xMQVRFUkFMXCIpXHJcbiAgICAgIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfQVBSSUNPVF9QRUFDSDtcclxuICAgIGVsc2UgaWYgKGFzc2V0TmFtZSA9PSBcIlNVTV9VTl9MSVFVSURfQVNTRVRTXCIpXHJcbiAgICAgIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfTkFWQUpPX1dISVRFO1xyXG4gICAgZWxzZSBpZiAoYXNzZXROYW1lID09IFwiU1VNX09USEVSX1RPVEFMXCIpXHJcbiAgICAgIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX0FSRUFfUk9TRV9GT0c7XHJcbiAgICBlbHNlXHJcbiAgICAgIGNvbG9yID0gU2VyaWVzU3R5bGVQcm92aWRlci5DT05UX1NFR01FTlRfQk9MRF9SRUQ7XHJcbiAgICByZXR1cm4gY29sb3JcclxuICB9XHJcblxyXG4gIC8qKlxyXG5cdFx0ICogVXBkYXRlcyB0aGUgc3R5bGUgbWV0YWRhdGEgd2hlbiBzdHlsZSBwb3B1cCBPSyBidXR0b24gaXMgY2xpY2tlZC5cclxuXHRcdCAqICovXHJcbiAgcHVibGljIHVwZGF0ZVN0eWxlTWV0YWRhdGEoeUZpZWxkOiBzdHJpbmcsIHN0eWxlSWQ6IHN0cmluZyk6IHZvaWQge1xyXG4gICAgdmFyIGVycm9Mb2NhdGlvbjogTnVtYmVyID0gMDtcclxuXHJcbiAgICB2YXIgYXJyID0geUZpZWxkLnNwbGl0KFwiLlwiKTtcclxuICAgIHZhciBncm91cElkOiBTdHJpbmcgPSBhcnJbMF07XHJcbiAgICB2YXIgc2NlbmFyaW9JZDogU3RyaW5nID0gYXJyWzFdO1xyXG4gICAgdmFyIGRhdGFFbGVtZW50OiBTdHJpbmcgPSBhcnJbMl07XHJcbiAgICB2YXIgZHRvOiBPYmplY3QgPSB0aGlzLmRhdGFzZXRzW2dyb3VwSWQgKyAnLicgKyBzY2VuYXJpb0lkXTtcclxuXHJcbiAgICAvLyBEbyB0aGlzIHRlc3QgYXMgJ3VwZGF0ZVN0eWxlTWV0YWRhdGEnIGNhbiBiZSBjYWxsZWQgZnJvbSBTZXJpZXNTdHlsZVBvcHVwLm14bWwgdG8gc2VydmUgYm90aCBncm91cCBhbmQgZ2xvYmFsIHRhYnNcclxuICAgIGlmIChkdG8pIHtcclxuICAgICAgdmFyIGNoYXJ0cyA9IGR0b1snY2hhcnRzJ107XHJcblxyXG4gICAgICBsZXQgY2hhcnRzTGlzdCA9IFN3dFV0aWwuY29udmVydE9iamVjdFRvQXJyYXkoY2hhcnRzLmNoYXJ0KTtcclxuICAgICAgLy8gZm9yIGVhY2godmFyIGNoYXJ0IDpYTUwgaW4gY2hhcnRzLmNoaWxkcmVuKCkpe1xyXG4gICAgICBmb3IgKGxldCBrID0gMDsgayA8IGNoYXJ0c0xpc3QubGVuZ3RoOyBrKyspIHtcclxuICAgICAgICBsZXQgY2hhcnQgPSBjaGFydHNMaXN0W2tdO1xyXG4gICAgICAgIGlmIChjaGFydC5kYXRhZWxlbWVudCA9PSBkYXRhRWxlbWVudCkge1xyXG4gICAgICAgICAgLy8gVXBkYXRlIHRoZSBzZXJpZXNTdHlsZSBhdHRyaWJ1dGVcclxuICAgICAgICAgIGNoYXJ0LnNlcmllc1N0eWxlID0gc3R5bGVJZDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG5cclxuICBwdWJsaWMgZ2V0Q2hhcnRTdHlsZVNlcmllc1N0eWxlQXNTdHJpbmcoKTogc3RyaW5nIHtcclxuICAgIHZhciBjaGFydHNTdHlsZXM6IHN0cmluZyA9IFwiXCI7XHJcbiAgICB2YXIgc2VyaWVzU3R5bGU6IHN0cmluZyA9IFwiXCI7XHJcbiAgICB2YXIgeUZpZWxkOiBzdHJpbmcgPSBcIlwiO1xyXG5cclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgIGxldCBzZXJpZTogU2VyaWVzID0gdGhpcy5zZXJpZXNMaXN0LmdldFZhbHVlcygpW2ldO1xyXG4gICAgICBpZiAoc2VyaWUuc2VyaWVzVHlwZSA9PSAnYXJlYScpIHtcclxuICAgICAgICB5RmllbGQgPSBzZXJpZS55RmllbGQ7XHJcbiAgICAgICAgaWYgKHlGaWVsZC5pbmRleE9mKCdTVU1fVU5fTElRVUlEX0FTU0VUUycpID09IC0xICYmIHlGaWVsZC5pbmRleE9mKCdTVU1fT1RIRVJfVE9UQUwnKSA9PSAtMVxyXG4gICAgICAgICAgJiYgeUZpZWxkLmluZGV4T2YoJ1NVTV9DT0xMQVRFUkFMJykgPT0gLTFcclxuICAgICAgICAgICYmIHlGaWVsZC5pbmRleE9mKCdTVU1fQ1JFRElUX0xJTkVfVE9UQUwnKSA9PSAtMSkge1xyXG4gICAgICAgICAgc2VyaWVzU3R5bGUgPSBzZXJpZS5hcHBsaWVkU3R5bGU7XHJcbiAgICAgICAgICBjaGFydHNTdHlsZXMgPSBjaGFydHNTdHlsZXMgKyB5RmllbGQgKyBcIjpcIiArIHNlcmllc1N0eWxlICsgXCJ8XCI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vRklYTUU6Q0VDS1xyXG4gICAgICB9IGVsc2UgaWYgKHNlcmllLnNlcmllc1R5cGUgPT0gJ2xpbmUnICYmIHNlcmllLmxlZ2VuZFRvb2x0aXApIHtcclxuICAgICAgICB5RmllbGQgPSBzZXJpZS55RmllbGQ7XHJcbiAgICAgICAgaWYgKHlGaWVsZC5pbmRleE9mKCdOT19NT1JFX0xJUVVJRElUWV9BVkFJTEFCTEUnKSA9PSAtMSkge1xyXG4gICAgICAgICAgc2VyaWVzU3R5bGUgPSBzZXJpZS5hcHBsaWVkU3R5bGU7XHJcbiAgICAgICAgICBjaGFydHNTdHlsZXMgPSBjaGFydHNTdHlsZXMgKyB5RmllbGQgKyBcIjpcIiArIHNlcmllc1N0eWxlICsgXCJ8XCI7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGNoYXJ0c1N0eWxlcy5sZW5ndGggPiAxKVxyXG4gICAgICBjaGFydHNTdHlsZXMgPSBjaGFydHNTdHlsZXMuc3Vic3RyKDAsIGNoYXJ0c1N0eWxlcy5sZW5ndGggLSAxKTtcclxuXHJcbiAgICByZXR1cm4gY2hhcnRzU3R5bGVzO1xyXG4gIH1cclxuXHJcbiAgbmdPbkluaXQoKSB7XHJcblxyXG4gICAgLy8gdGhpcy5pZnJhbWVDb250YWllciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lmcmFtZScpO1xyXG4gICAgLy8gdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5jc3NUZXh0ID0gXCJwb3NpdGlvbjogYWJzb2x1dGU7dG9wOjBweDtsZWZ0OiAwcHg7IGhlaWdodDogMHB4O3dpZHRoOiAwcHg7IGJvcmRlcjogMXB4IHNvbGlkXCI7XHJcbiAgICAvLyB0aGlzLmlmcmFtZUNvbnRhaWVyLnNldEF0dHJpYnV0ZShcImNsYXNzXCIsIFwiaWZyYW1lQ2xhc3NcIik7XHJcbiAgICAvLyB0aGlzLmlmcmFtZUNvbnRhaWVyLnNyYyA9IFN3dFV0aWwuZ2V0QmFzZVVSTCgpK1wiY2hhcnRzLmh0bVwiO1xyXG4gICAgLy8gZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZCh0aGlzLmlmcmFtZUNvbnRhaWVyKTtcclxuICAgICQod2luZG93KS5yZXNpemUoKCkgPT4ge1xyXG4gICAgICBjbGVhclRpbWVvdXQodGhpcy5yZXNpemVGaW5pc2hlZCk7XHJcbiAgICAgIHRoaXMucmVzaXplRmluaXNoZWQgPSBzZXRUaW1lb3V0KCgpPT57XHJcbiAgICAgICAgaWYgKHRoaXMuaXNWaXNpYmxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSkge1xyXG4gICAgICAgICAgdGhpcy5jYWxsTWV0aG9kSW5JZnJhbWUoXCJyZWRyYXdDaGFydFwiLCBbJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudClbMF0uY2xpZW50SGVpZ2h0XSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAyNTApO1xyXG4gIH0pO1xyXG4gIH1cclxuXHJcbiAgcmVzaXplRmluaXNoZWQ7XHJcblxyXG4gIG5nQWZ0ZXJWaWV3SW5pdCgpIHtcclxuXHJcbiAgICAvLyAgIGxldCBtdXRhdGlvbk9ic2VydmVyID0gbmV3IHdpbmRvdy5NdXRhdGlvbk9ic2VydmVyKHRoaXMuY2hlY2tEaWZmKVxyXG4gICAgLy8gICBtdXRhdGlvbk9ic2VydmVyLm9ic2VydmUodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQsIHtcclxuICAgIC8vICAgICBhdHRyaWJ1dGVzOiB0cnVlLFxyXG4gICAgLy8gICAgIGF0dHJpYnV0ZUZpbHRlcjogWydzdHlsZScsICdjbGFzcycsJ3dpZHRoJywgJ3gnLCd5J11cclxuICAgIC8vICAgfSlcclxuXHJcbiAgICAvLyAgIHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ3Rlc3R0ZXN0dGVzdCcpO1xyXG5cclxuICAgIC8vICAgY29uc3Qgcm8gPSBuZXcgUmVzaXplT2JzZXJ2ZXIoKGVudHJpZXMsIG9ic2VydmVyKSA9PiB7XHJcbiAgICAvLyAgICAgaWYgKHRoaXMuaXNWaXNpYmxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSkge1xyXG4gICAgLy8gICAgICAgdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xyXG4gICAgLy8gICAgICAgLy8gdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS52aXNpYmlsaXR5ID0gJ3Zpc2libGUnO1xyXG4gICAgLy8gICAgICAgdmFyIHBvcHVwUmVjdCA9IHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xyXG4gICAgLy8gICAgICAgaWYocG9wdXBSZWN0LmhlaWdodCA8IDE1KXtcclxuICAgIC8vICAgICAgICAgdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xyXG4gICAgLy8gICAgICAgICByZXR1cm47XHJcbiAgICAvLyAgICAgICB9XHJcbiAgICAvLyAgICAgICBpZiAodGhpcy5wcmV2aW91c1JjdC5oZWlnaHQgIT09IHBvcHVwUmVjdC5oZWlnaHQpIHtcclxuICAgIC8vICAgICAgICAgdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5oZWlnaHQgPSBwb3B1cFJlY3QuaGVpZ2h0ICsgXCJweFwiO1xyXG4gICAgLy8gICAgICAgfVxyXG4gICAgLy8gICAgICAgaWYgKHRoaXMucHJldmlvdXNSY3QubGVmdCAhPT0gcG9wdXBSZWN0LmxlZnQpIHtcclxuICAgIC8vICAgICAgICAgdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5sZWZ0ID0gcG9wdXBSZWN0LmxlZnQgKyBcInB4XCI7XHJcbiAgICAvLyAgICAgICB9XHJcbiAgICAvLyAgICAgICBpZiAodGhpcy5wcmV2aW91c1JjdC53aWR0aCAhPT0gcG9wdXBSZWN0LndpZHRoKSB7XHJcbiAgICAvLyAgICAgICAgIHRoaXMuaWZyYW1lQ29udGFpZXIuc3R5bGUud2lkdGggPSBwb3B1cFJlY3Qud2lkdGggKyBcInB4XCI7XHJcbiAgICAvLyAgICAgICB9XHJcbiAgICAvLyAgICAgICBpZiAodGhpcy5wcmV2aW91c1JjdC50b3AgIT09IHBvcHVwUmVjdC50b3ApIHtcclxuICAgIC8vICAgICAgICAgdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS50b3AgPSBwb3B1cFJlY3QudG9wICsgXCJweFwiO1xyXG4gICAgLy8gICAgICAgfVxyXG4gICAgLy8gICAgICAgLy90aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKVxyXG4gICAgLy8gICAgICAgdGhpcy5wcmV2aW91c1JjdCA9IHsgbGVmdDogdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS5sZWZ0LCB0b3A6IHRoaXMuaWZyYW1lQ29udGFpZXIudG9wLCBoZWlnaHQ6IHRoaXMuaWZyYW1lQ29udGFpZXIuc3R5bGUuaGVpZ2h0LCB3aWR0aDogdGhpcy5pZnJhbWVDb250YWllci5zdHlsZS53aWR0aCB9O1xyXG4gICAgLy8gICAgIH0gZWxzZSB7XHJcbiAgICAvLyAgICAgICB0aGlzLmlmcmFtZUNvbnRhaWVyLnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XHJcbiAgICAvLyAgICAgfVxyXG4gICAgLy8gIH0pO1xyXG4gICAgLy8gcm8ub2JzZXJ2ZSh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCk7XHJcblxyXG4gICAgLy8gbGV0IHBhcmVudEhlaWdodCA9IHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50Lm9mZnNldFBhcmVudC5jbGllbnRIZWlnaHQ7XHJcbiAgICAvLyBjb25zdCBybyA9IG5ldyBSZXNpemVPYnNlcnZlcigoZW50cmllcywgb2JzZXJ2ZXIpID0+IHtcclxuICAgIC8vICAgaWYgKHRoaXMuaXNWaXNpYmxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSkge1xyXG4gICAgLy8gICAgIC8vQ2FsbCByZWRyYXcgZnVuY3Rpb24gaW4gdGhlIGNoYXJ0IGJ1dCBsaW1pdCB0aGUgY2FsbCB0byBvbmNlIHBlciAxMDAgbWlsaXRzZWNvbmQgdG8gd2luIHBlcmZlcm1hbmNlIGFzIFxyXG4gICAgLy8gICAgIC8vUmVzaXplT2JzZXJ2ZXIgY2FuIGJlIGNhbGxhZWQgdG9vIG1hbnkgdGltZXMgaW4gXHJcbiAgICAvLyAgICAgLy8gdGhpcy5yZWRyYXdDaGFydCgxMDApO1xyXG4gICAgLy8gICAgIC8vIHRoaXMuY2FsbE1ldGhvZEluSWZyYW1lKFwicmVkcmF3Q2hhcnRcIiwgWyQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpWzBdLmNsaWVudEhlaWdodF0pO1xyXG4gICAgLy8gICAgIC8vICAgICB0aGlzLmNoYXJ0LnJlZmxvdygpO1xyXG4gICAgLy8gICAgIC8vICAgICB0aGlzLmNoYXJ0LnVwZGF0ZSh7XHJcbiAgICAvLyAgICAgLy8gICAgICAgcGxvdE9wdGlvbnM6IHtcclxuICAgIC8vICAgICAvLyAgICAgICAgIHNlcmllczoge1xyXG4gICAgLy8gICAgIC8vICAgICAgICAgICBzdGF0ZXM6IHtcclxuICAgIC8vICAgICAvLyAgICAgICAgICAgICBob3Zlcjoge1xyXG4gICAgLy8gICAgIC8vICAgICAgICAgICAgICAgZW5hYmxlZDogZmFsc2VcclxuICAgIC8vICAgICAvLyAgICAgICAgICAgICB9LFxyXG4gICAgLy8gICAgIC8vICAgICAgICAgICAgIGluYWN0aXZlOiB7XHJcbiAgICAvLyAgICAgLy8gICAgICAgICAgICAgICBlbmFibGVkOiBmYWxzZVxyXG4gICAgLy8gICAgIC8vICAgICAgICAgICAgIH1cclxuICAgIC8vICAgICAvLyAgICAgICAgICAgfVxyXG4gICAgLy8gICAgIC8vICAgICAgICAgfVxyXG4gICAgLy8gICAgIC8vICAgICAgIH0sXHJcbiAgICAvLyAgICAgLy8gICAgIH0pXHJcbiAgICAvLyAgIH1cclxuICAgIC8vIH0pO1xyXG4gICAgLy8gcm8ub2JzZXJ2ZSh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCk7XHJcblxyXG4gICAgRGl2aWRlclJlc2l6ZUNvbXBsZXRlLnN1YnNjcmliZSgoZXZlbnQpID0+IHtcclxuICAgIC8vIGNvbnN0IGlzU2VsZWN0ZWRUYWIgPSAgdGhpcy50YWJOYW1lID09IFwiR2xvYmFsVmlld1wiICYmICh0aGlzLnBhcmVudERvY3VtZW50LnRhYk5hdmlnYXRvci5zZWxlY3RlZEluZGV4ID09IDApXHJcbiAgICBpZihldmVudC5pZCA9PT1cImxlZ2VuZHNEaXZpZGVyXCIgfHwgZXZlbnQuaWQgID09PSBcImdyaWREaXZpZGVyXCIgfHwgZXZlbnQuaWQgPT09XCJ0cmVlRGl2aWRlclwiKXtcclxuICAgICAgXHJcbiAgICAgICAgaWYgKHRoaXMuaXNWaXNpYmxlKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSkge1xyXG4gICAgICAgICAgICB0aGlzLmNhbGxNZXRob2RJbklmcmFtZShcInJlZHJhd0NoYXJ0XCIsIFskKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KVswXS5jbGllbnRIZWlnaHRdKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG5cclxuXHJcbiAgfVxyXG4gIHByaXZhdGUgY2FuQ2FsbCA9IHRydWU7XHJcbiAgcmVkcmF3Q2hhcnQgPSAoZGVsYXkpID0+IHtcclxuICAgIGlmICghdGhpcy5jYW5DYWxsKVxyXG4gICAgICByZXR1cm47XHJcbiAgICB0aGlzLmNhbGxNZXRob2RJbklmcmFtZShcInJlZHJhd0NoYXJ0XCIsIFskKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KVswXS5jbGllbnRIZWlnaHRdKTtcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICB0aGlzLmNhbGxNZXRob2RJbklmcmFtZShcInJlZHJhd0NoYXJ0XCIsIFskKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KVswXS5jbGllbnRIZWlnaHRdKTtcclxuXHJcbiAgICB9LCBkZWxheSk7XHJcbiAgICB0aGlzLmNhbkNhbGwgPSBmYWxzZTtcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICB0aGlzLmNhbkNhbGwgPSB0cnVlO1xyXG4gICAgfSwgZGVsYXkpO1xyXG5cclxuICB9XHJcblxyXG4gIGRlYm91bmNlID0gKGZuLCBkZWxheSkgPT4ge1xyXG4gICAgbGV0IHRpbWVyXHJcbiAgICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcclxuICAgICAgaWYgKHRpbWVyKSBjbGVhclRpbWVvdXQodGltZXIpXHJcbiAgICAgIHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgZm4uYXBwbHkobnVsbCwgYXJncylcclxuICAgICAgfSwgZGVsYXkpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHByZXZpb3VzUmN0ID0ge1xyXG4gICAgbGVmdDogMCxcclxuICAgIHRvcDogMCxcclxuICAgIHdpZHRoOiAwLFxyXG4gICAgaGVpZ2h0OiAwXHJcblxyXG4gIH07XHJcblxyXG4gIGlzVmlzaWJsZShlKSB7XHJcbiAgICByZXR1cm4gISEoZS5vZmZzZXRXaWR0aCB8fCBlLm9mZnNldEhlaWdodCB8fCBlLmdldENsaWVudFJlY3RzKCkubGVuZ3RoKTtcclxuICB9XHJcblxyXG5cclxufVxyXG4iXX0=