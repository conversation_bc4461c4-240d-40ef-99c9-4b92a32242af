import { AfterViewInit, ElementRef, EventEmitter, OnInit } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from '../utils/common.service';
export declare class SwtAdvSlider extends Container implements OnInit, AfterViewInit {
    private elem;
    private commonService;
    protected sliderFromInput: ElementRef;
    protected sliderToInput: ElementRef;
    ZOOM_EVENT_ZOOMED: EventEmitter<any>;
    protected hboxContainer: ElementRef;
    protected slider: ElementRef;
    constructor(elem: ElementRef, commonService: CommonService);
    private localId;
    private _timeData;
    private swtalert;
    valueFrom: string;
    valueTo: string;
    private _maximum;
    private _minimum;
    private _values;
    private RESTRICT_CHARS;
    private _calculateFunction;
    private _callbackFunction;
    private _onstatechangeFunction;
    startDate: string;
    endDate: string;
    valueDate: string;
    private options;
    defaultFunction(value: any): void;
    defaultReturnFunction(value: any): any;
    timeData: any[];
    values: any[];
    /**
         * Get the start and end value for the first load
         **/
    sliderComp(): void;
    /**
     *
     * Change the cell content based on function .
     **/
    /**
    *
    */
    calculateFunction: Function;
    /**
     *
     * Change the cell content based on function .
     **/
    /**
    *
    */
    callbackFunction: Function;
    /**
     *
     * Change the cell content based on function .
     **/
    /**
    *
    */
    onstatechangeFunction: Function;
    scale: any;
    private step;
    maximum: any;
    /**
 * Init Slider labels after completing the creation of the slider
 **/
    initTextInputs(): void;
    minimum: any;
    generateDynamicId(): string;
    private tooltipOnLeftTrigger;
    ngAfterViewInit(): void;
    private toolTipSlider;
    private leftTriggerTooltipValue;
    private rightTriggerTooltipValue;
    ngOnInit(): void;
    calculateTime(value: any): void;
    refreshSliderValues(): void;
    validateTime(textInput: any): any;
}
