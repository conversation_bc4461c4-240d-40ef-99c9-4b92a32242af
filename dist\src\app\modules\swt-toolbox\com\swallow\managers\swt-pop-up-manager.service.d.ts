import { TitleWindow } from '../controls/title-window.component';
/**
 * SwtPopUpManager version 2.0
 */
export declare class SwtPopUpManager {
    static modalId: number;
    private static position;
    constructor();
    /**
     *  Creates a top-level window and places it above other windows in the
     *  z-order.
     *  It is good practice to call the <code>removePopUp()</code> method
     *  to remove popups created by using the <code>createPopUp()</code> method.
     */
    static createPopUp(parent: any, childComponent?: any, data?: any, modal?: boolean): TitleWindow;
    static load(url: any): TitleWindow;
    static getPopUpById(id: string): TitleWindow;
    static close(window: TitleWindow): void;
    /**
     *  Pops up a top-level window.
     *  It is good practice to call <code>removePopUp()</code>
     *
     * @deprecated since version 2.0
     *
     */
    /**
     * This method is used to set the PopUp display position.
     * @param left
     * @param top
     */
    static setPopUpLocation(left: any, top: any): void;
    /**
     * this method is used to hide the popup.
     * <AUTHOR>
     */
    static hide(): void;
    /**
     * This method is used to show popup
     * <AUTHOR>
     */
    static show(): void;
    /**
     *  Centers a popup window over whatever window was used in the call
     *  to the <code>createPopUp()</code> or <code>addPopUp()</code> method.
     */
    static centerPopUp(popUp: any): void;
    /**
     *  Makes sure a popup window is higher than other objects in its child list
     *  The SystemManager does this automatically if the popup is a top level window
     *  and is moused on,
     *  but otherwise you have to take care of this yourself.
     */
    static bringToFront(popUp: any): void;
    /**
     * Draws a Mask that hides all components that belongs to the parent
     */
    private static drawMask;
    private static removeMask;
    /**
     *
     *  Set by PopUpManager on modal windows to make sure they cover the whole parent
     */
    private static resizeHandler;
    /**
     *
     *  Returns the PopUpData (or null) for a given popupInfo.owner
     */
    private static findPopupInfoByWindow;
}
