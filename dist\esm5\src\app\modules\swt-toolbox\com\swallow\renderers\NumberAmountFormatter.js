/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { isClickable, isBlink, isNegative, CustomCell, CellBackgroundColor, isBold } from "./cellItemRenderUtilities";
/** @type {?} */
export var NumberAmountFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    var type = columnDef['columnType'];
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var colorLink = '';
    /** @type {?} */
    var blink_me = isBlink(value);
    /** @type {?} */
    var negative = isNegative(dataContext, field);
    /** @type {?} */
    var isLink = columnDef["clickable"] || isClickable(dataContext, field);
    /** @type {?} */
    var style = CustomCell(dataContext, field);
    /** @type {?} */
    var bold = columnDef['bold'] ? columnDef['bold'] : isBold(dataContext, field);
    /** @type {?} */
    var extraContent = '';
    if (isLink && columnDef.params.rowClickableFunction) {
        /** @type {?} */
        var clickableFromFunction = columnDef.params.rowClickableFunction(dataContext, row, columnDef.field);
        isLink = clickableFromFunction;
        colorLink = '#52aefb';
    }
    /** @type {?} */
    var backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    var enableRowSelection = columnDef.params.grid.enableRowSelection;
    //-there is no custom style for the cell
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        if (backgroundColor && backgroundColor.toString().indexOf('|') > -1) {
            // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
            /** @type {?} */
            var colorList = backgroundColor.split('|');
            style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
        }
        else {
            style += ' background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        }
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (bold) {
        style += 'font-weight: bold!important;';
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null || field == "expand") {
            value = "";
        }
        if (typeof (value) == "string") {
            value = value.replace('<', '&lt;');
            value = value.replace('>', '&gt;');
        }
        extraContent = columnDef.params.extraHTMLContentFunction(dataContext, columnDef.field, value, type);
        value = columnDef.params.customContentFunction(dataContext, columnDef.field, value, type);
        return "<div class=\"strFormatterDiv " + (blink_me == true ? 'blink_me' : '') + "  " + ((String(type) == "input" && (enabledFlag == false || !columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : '') + " \"  style='" + style + " padding-right: 5px;  position: relative; text-align: right;' >" + (isLink ? '<a class="strLinkRender" ' + (negative ? 'style="color: #ff0000 !important;"' : colorLink ? 'style="color: ' + colorLink + ' !important;"' : '') + '  >' + value + '</a>' : value) + "</div>" + extraContent;
    }
    return "";
});
//# sourceMappingURL=data:application/json;base64,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