import { HttpHeaders } from '@angular/common/http';
import { RemoteTransaction } from './RemoteTransaction';
import { CommonService } from "../utils/common.service";
export declare class HTTPComms {
    private common;
    cbFault: Function;
    cbResult: Function;
    cbStart: Function;
    cbStop: Function;
    headers: HttpHeaders;
    stopTimers: Function;
    private logger;
    private originalURL;
    private busy;
    private encode;
    private _start;
    private _result;
    private _method;
    private _localStart;
    private alertFlag;
    private _cancelRequest;
    private screenUniqueTransactionId_;
    private _responsetype;
    private jsonReader;
    private swtAlert;
    constructor(common: CommonService);
    private _profling;
    /**
     * Get profiling condition
     * */
    /**
    * Set profiling condition
    * @param value
    * */
    profling: boolean;
    private _serverDelay;
    /**
     * Get delay of last HTTP request/response
     * */
    readonly serverDelay: Number;
    private _localDelay;
    readonly localDelay: number;
    private _url;
    url: string;
    encodeURL: boolean;
    /**
     * Overrides the main send function so that the busy flag is set
     * The start callback function is called
     * and
     * Then sending the data, encoding it - So be aware that it needs to be decoded at the server end
     */
    send(parameters?: any, json?: any): void;
    cancel(id?: string): void;
    /**
     *  Use this function to send arrays to the server
     */
    arraySend(params: any[], name: string, needQ: boolean): void;
    /**
     *  Use to check the state of the request
     */
    isBusy(): Boolean;
    /**
     * Reset the transaction unique Id by setting it to null value
     **/
    resetTransactionUId(): void;
    getTransactionUId(): string;
    /**
     * Sets the screen transaction unique Id
     */
    setTransactionUId(programId: number, uniqueIdentifier: string): void;
    /**
     * Remotely start a transaction
     */
    startRemoteTransaction(programId: number, uniqueIdentifier?: string): RemoteTransaction;
    sendTransaction(url: any): any;
    private result;
    private showLogon;
    private fault;
    private onStart;
    private getResultObject;
    /**
     *  Function encodes the parameters that are passed
     */
    private encodeData;
    /**
     *  Executes an HTTPService request. The parameters are optional, but if specified should
     *  be an Object containing name-value pairs or an XML object depending on the <code>contentType</code>.
     *  @return An object representing the asynchronous completion token. It is the same object
     *  available in the <code>result</code> or <code>fault</code> event's <code>token</code> property.
     */
    private sendRequest;
    private onFinish;
    /**
     *  Use when sending an array it needs to be split
     */
    private arrayToGetParams;
    private getTimer;
}
