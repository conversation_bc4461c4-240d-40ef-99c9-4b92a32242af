/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { focusManager } from "../managers/focus-manager.service";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ViewChild, ElementRef, Input, Component } from "@angular/core";
import { SwtUtil } from '../utils/swt-util.service';
/** @type {?} */
var $ = require('jquery');
// This list to store all styleNames that not contain Icon in the end.
/** @type {?} */
var styleNameList = ["arrowUp", "arrowDown", "upButtonEnable", "downButtonEnable"];
var SwtButton = /** @class */ (function (_super) {
    tslib_1.__extends(SwtButton, _super);
    /**
     * Constructor.
     */
    function SwtButton(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        _this._enabled = true;
        _this._styleName = "";
        _this._buttonMode = true;
        _this._width = "auto";
        _this._label = "";
        //-START- Added by Rihab.J   - needed to be used in dynamically added SwtButton.
        $($(_this.elem.nativeElement)[0]).attr('selector', 'SwtButton');
        return _this;
    }
    Object.defineProperty(SwtButton.prototype, "tabIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._tabIndex;
        },
        //---tabIndex-----------------------------------------------------------------------------------------------------
        set: 
        //---tabIndex-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._tabIndex = String(value);
                if ($(this.button))
                    $(this.button).attr("tabindex", this._tabIndex);
            }
            catch (error) {
                console.error('method [ set tabIndex] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (Number(value) < 60) {
                $(this.swtbutton.nativeElement).removeClass("minWidthBtn");
            }
            this._width = this.adaptUnit(value, "auto");
            if (this._width.indexOf("px") !== -1) {
                this.setStyle("width", this._width, this.button);
            }
            else {
                this.setStyle("width", this._width);
                this.setStyle("width", "100%", this.button);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "styleName", {
        get: /**
         * @return {?}
         */
        function () {
            return this._styleName;
        },
        //---styleName----------------------------------------------------------------------------------------------------
        set: 
        //---styleName----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value && (value.indexOf("Icon") != -1 || styleNameList.indexOf(value) != -1)) {
                $(this.button).removeClass().addClass(value);
            }
            else {
                $(this.button).addClass(value);
            }
            this._styleName = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        //---label--------------------------------------------------------------------------------------------------------
        set: 
        //---label--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var element = $($(this.swtbutton)[0].nativeElement).find('.buttonLabel');
            if (element.length > 0) {
                $(element).text(value);
            }
            this._label = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "textDictionaryId", {
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this.label = SwtUtil.getPredictMessage(value);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "buttonMode", {
        get: /**
         * @return {?}
         */
        function () {
            return this._buttonMode;
        },
        //---ButtonMode---------------------------------------------------------------------------------------------------
        set: 
        //---ButtonMode---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._buttonMode = this.adaptValue(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtButton.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        //---enabled------------------------------------------------------------------------------------------------------
        set: 
        //---enabled------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (String(this._enabled) != String(value)) {
                this._enabled = this.adaptValue(value);
                if (this._enabled) {
                    this.addAllOutputsEventsListeners($(this.elem.nativeElement));
                }
                else {
                    this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
                }
                /** @type {?} */
                var minWidthBtn = $(this.button).hasClass("minWidthBtn");
                if (this.hasIcon()) {
                    // - its a button with ICON.
                    if (this._enabled) {
                        $(this.button).removeClass().addClass(this.styleName);
                    }
                    else {
                        $(this.button).removeClass().addClass(this.styleName + "Disabled");
                    }
                }
                else {
                    // - its a simple button.
                    if (this._enabled) {
                        if (this.getComponentName() == "LINKBUTTON") {
                            $(this.button).removeClass("disabledLinkButton");
                        }
                        else {
                            $(this.button).removeClass().addClass("swtbtn");
                        }
                    }
                    else {
                        if (this.getComponentName() == "LINKBUTTON") {
                            $(this.button).addClass("disabledLinkButton");
                        }
                        else {
                            $(this.button).removeClass().addClass("swtbtn-disabled");
                        }
                    }
                    if (minWidthBtn) {
                        $(this.button).addClass("minWidthBtn");
                    }
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit.
     */
    /**
     * ngOnInit.
     * @return {?}
     */
    SwtButton.prototype.ngOnInit = /**
     * ngOnInit.
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        //Remove all events on hostElement
        this.removeEventsListeners(this.hostElement);
        $(this.button).hover((/**
         * @return {?}
         */
        function () {
            focusManager.hoverButton = this;
        }), (/**
         * @return {?}
         */
        function () {
            setTimeout((/**
             * @return {?}
             */
            function () {
                focusManager.hoverButton = null;
            }), 0);
        }));
        if (this._enabled) {
            this.addAllOutputsEventsListeners(this.hostElement);
        }
    };
    /**
     * Removes all handlers attached to the element.
     */
    /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    SwtButton.prototype.removeEventsListeners = /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    function (element) {
        _super.prototype.removeAllOuputsEventsListeners.call(this, $(element));
        this.buttonMode = false;
    };
    /**
     * This method is used to change button view.
     * @param state
     */
    /**
     * This method is used to change button view.
     * @private
     * @param {?=} state
     * @return {?}
     */
    SwtButton.prototype.setButtonState = /**
     * This method is used to change button view.
     * @private
     * @param {?=} state
     * @return {?}
     */
    function (state) {
        if (this.hasIcon()) {
            if ((this.styleName + state) != "printIconOver")
                $(this.button).removeClass().addClass(this.styleName + state);
            this.buttonMode = this._buttonMode;
        }
    };
    /**
     * This method is used to set focus to button.
     */
    /**
     * This method is used to set focus to button.
     * @return {?}
     */
    SwtButton.prototype.setFocus = /**
     * This method is used to set focus to button.
     * @return {?}
     */
    function () { };
    /**
     * @param {?} visibility
     * @return {?}
     */
    SwtButton.prototype.setVisible = /**
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        this.visible = visibility;
    };
    Object.defineProperty(SwtButton.prototype, "button", {
        /**
          * returns DOM of the button element.
          */
        get: /**
         * returns DOM of the button element.
         * @return {?}
         */
        function () {
            return this.swtbutton ? $(this.swtbutton.nativeElement)[0] : null;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * convert entered value as string to boolean.
     * @param value
     */
    /**
     * convert entered value as string to boolean.
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtButton.prototype.adaptValue = /**
     * convert entered value as string to boolean.
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                value = false;
            }
            else {
                value = true;
            }
        }
        return value;
    };
    /**
     * Check if the button has an icon or not.
     */
    /**
     * Check if the button has an icon or not.
     * @private
     * @return {?}
     */
    SwtButton.prototype.hasIcon = /**
     * Check if the button has an icon or not.
     * @private
     * @return {?}
     */
    function () {
        if (this.styleName != "" && (this.styleName.indexOf("Icon") != -1 || styleNameList.indexOf(this.styleName) != -1)) {
            return true;
        }
        return false;
    };
    /**
     * Destroy all event listeners
     */
    /**
     * Destroy all event listeners
     * @return {?}
     */
    SwtButton.prototype.ngOnDestroy = /**
     * Destroy all event listeners
     * @return {?}
     */
    function () {
        try {
            console.log('[SwtButton] ngOnDestroy ');
            this.removeEventsListeners(this.button);
            delete this.swtbutton;
            delete this._enabled;
            delete this._styleName;
            delete this._buttonMode;
            delete this._width;
            delete this._label;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    SwtButton.decorators = [
        { type: Component, args: [{
                    selector: 'SwtButton',
                    template: "<div \n                    #swtbutton  \n                    class=\"swtbtn minWidthBtn \">\n                    <span class=\"truncate buttonLabel\" ></span>\n               </div>\n    ",
                    styles: ["\n         :host{\n             outline:none;\n         }\n        .truncate{\n            margin:0px 2px 1px 2px; \n            text-overflow: ellipsis; \n            overflow: hidden; \n            white-space: nowrap;\n            display:block;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    SwtButton.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtButton.propDecorators = {
        swtbutton: [{ type: ViewChild, args: ['swtbutton',] }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        width: [{ type: Input }],
        styleName: [{ type: Input, args: ['styleName',] }],
        label: [{ type: Input }],
        textDictionaryId: [{ type: Input, args: ['textDictionaryId',] }],
        buttonMode: [{ type: Input }],
        enabled: [{ type: Input }]
    };
    return SwtButton;
}(Container));
export { SwtButton };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtButton.prototype.swtbutton;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.hostButton;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._buttonMode;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._width;
    /**
     * @type {?}
     * @protected
     */
    SwtButton.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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