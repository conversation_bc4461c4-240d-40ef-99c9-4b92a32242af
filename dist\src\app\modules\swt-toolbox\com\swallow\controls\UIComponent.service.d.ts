import { EventEmitter, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonService } from "../utils/common.service";
import { EventDispatcher } from "../events/event-dispatcher.service";
export declare class UIComponent extends EventDispatcher implements OnD<PERSON>roy, IUIComponent {
    private uielement;
    private __commonService;
    name: any;
    styleName: any;
    private _id;
    private _restrict;
    private _maxChars;
    maxChars: any;
    restrict: any;
    id: any;
    onClick_: EventEmitter<any>;
    dbClick_: EventEmitter<any>;
    doubleClick_: EventEmitter<any>;
    itemDoubleClick_: EventEmitter<any>;
    onKeyDown_: EventEmitter<any>;
    onKeyUp_: EventEmitter<any>;
    mouseUp_: EventEmitter<any>;
    mouseOver_: EventEmitter<any>;
    mouseDown_: EventEmitter<any>;
    mouseEnter_: EventEmitter<any>;
    mouseLeave_: EventEmitter<any>;
    mouseOut_: EventEmitter<any>;
    mouseIn_: EventEmitter<any>;
    mouseMove_: EventEmitter<any>;
    focus_: EventEmitter<any>;
    focusIn_: EventEmitter<any>;
    onFocusOut_: EventEmitter<any>;
    keyFocusChange_: EventEmitter<any>;
    change_: EventEmitter<any>;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    scroll_: EventEmitter<any>;
    private _click;
    private _dbClick;
    private _doubleClick;
    private __itemDoubleClick;
    private _keyDown;
    private _keyUp;
    private _mouseUp;
    private _mouseOver;
    private _mouseDown;
    private _mouseEnter;
    private _mouseLeave;
    private _mouseOut;
    private _mouseIn;
    private _mouseMove;
    private _focus;
    private _focusIn;
    private _focusOut;
    private _keyFocusChange;
    private _change;
    private _scroll;
    click: Function;
    dbClick: Function;
    doubleClick: Function;
    itemDoubleClick: Function;
    keyDown: Function;
    keyUp: Function;
    mouseUp: Function;
    mouseOver: Function;
    mouseDown: Function;
    mouseEnter: Function;
    mouseIn: Function;
    mouseMove: Function;
    mouseLeave: Function;
    mouseOut: Function;
    focus: Function;
    focusIn: Function;
    focusOut: Function;
    keyFocusChange: Function;
    change: Function;
    scroll: Function;
    protected eventlist: any[];
    protected cursorLocation: {
        x: number;
        y: number;
    };
    constructor(uielement: any, __commonService: CommonService);
    /**
     * Adds a non-visual style client to this component instance.
     * @param styleClient
     */
    addStyleClient(styleClient: any): void;
    /**
     * This method is used to get the mouse
     * pointer location.
     */
    getCursorLocation(): {
        x: number;
        y: number;
    };
    /**
     * Queues a function to be called later.
     * @param method
     * @param args
     */
    callLater(method: string, args?: any[]): void;
    /**
     * Deletes a style property from this component instance.
     * @param styleProp
     */
    clearStyle(styleProp: string): void;
    /**
     * Converts a Point object from content coordinates to global coordinates.
     * @param point
     */
    contentToGlobal(point: any): void;
    /**
     * Converts a Point object from content to local coordinates.
     * @param point
     */
    contentToLocal(point: any): void;
    /**
     * Returns a set of properties that identify the child within this container.
     * @param child
     */
    createAutomationIDPart(child: any): void;
    /**
     * Returns a set of properties that identify the child within this container.
     * @param child
     * @param properties
     */
    createAutomationIDPartWithRequiredProperties(child: any, properties: any[]): void;
    /**
     * Creates an id reference to this IUIComponent object on its parent document object.
     * @param parentDocument
     */
    createReferenceOnParentDocument(parentDocument: any): void;
    /**
     * Deletes the id reference to this IUIComponent object on its parent document object.
     * @param parentDocument
     */
    deleteReferenceOnParentDocument(parentDocument: any): void;
    /**
     * Returns a UITextFormat object corresponding to the text styles for this UIComponent.
     */
    determineTextFormatFromStyles(): void;
    /**
     * Shows or hides the focus indicator around this component.
     * @param isFocused
     */
    drawFocus(isFocused: boolean): void;
    /**
     * Programmatically draws a rectangle into this skin's Graphics object.
     * @param x
     * @param y
     * @param w
     * @param h
     * @param r
     * @param c
     * @param alpha
     * @param rot
     * @param gradient
     * @param ratios
     * @param hole
     */
    drawRoundRect(x: number, y: number, w: number, h: number, r?: any, c?: any, alpha?: any, rot?: any, gradient?: string, ratios?: any[], hole?: any): void;
    /**
     * Called by the effect instance when it stops playing on the component.
     * @param effectInst
     */
    effectFinished(effectInst: any): void;
    /**
     * Called by the effect instance when it starts playing on the component.
     * @param effectInst
     */
    effectStarted(effectInst: any): void;
    /**
     * Ends all currently playing effects on the component.
     */
    endEffectsStarted(): void;
    /**
     * Executes all the bindings for which the UIComponent object is the destination.
     * @param recurse
     */
    executeBindings(recurse?: boolean): void;
    /**
     * Called after printing is complete.
     * @param obj
     * @param target
     */
    finishPrint(obj: any, target: any): void;
    /**
     * Provides the automation object at the specified index.
     * @param index
     */
    getAutomationChildAt(index: number): void;
    /**
     * Provides the automation object list .
     */
    getAutomationChildren(): void;
    /**
     * Returns the x coordinate of the element's bounds at the specified element size.
     * @param width
     * @param height
     * @param postLayoutTransform
     */
    getBoundsXAtSize(width: number, height: number, postLayoutTransform?: boolean): void;
    /**
     * Returns the y coordinate of the element's bounds at the specified element size.
     * @param width
     * @param height
     * @param postLayoutTransform
     */
    getBoundsYAtSize(width: number, height: number, postLayoutTransform?: boolean): void;
    /**
     * Finds the type selectors for this UIComponent instance.
     */
    getClassStyleDeclarations(): void;
    /**
     * Returns a layout constraint value, which is the same as getting the constraint style for this component.
     * @param constraintName
     */
    getConstraintValue(constraintName: string): void;
    /**
     * A convenience method for determining whether to use the explicit or measured height
     */
    getExplicitOrMeasuredHeight(): void;
    /**
     * A convenience method for determining whether to use the explicit or measured width
     */
    getExplicitOrMeasuredWidth(): void;
    /**
     * Gets the object that currently has focus.
     */
    getFocus(): void;
    /**
     * Returns the element's layout height.
     * @param postLayoutTransform
     */
    getLayoutBoundsHeight(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's layout width.
     * @param postLayoutTransform
     */
    getLayoutBoundsWidth(postLayoutTransform?: boolean): void;
    /**
     * Returns the x coordinate that the element uses to draw on screen.
     * @param postLayoutTransform
     */
    getLayoutBoundsX(postLayoutTransform?: boolean): void;
    /**
     * Returns the y coordinate that the element uses to draw on screen.
     * @param postLayoutTransform
     */
    getLayoutBoundsY(postLayoutTransform?: boolean): void;
    /**
     * Returns the transform matrix that is used to calculate the component's layout relative to its siblings.
     */
    getLayoutMatrix(): void;
    /**
     * Returns the layout transform Matrix3D for this element.
     */
    getLayoutMatrix3D(): void;
    /**
     * Returns the element's maximum height.
     * @param postLayoutTransform
     */
    getMaxBoundsHeight(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's maximum width.
     * @param postLayoutTransform
     */
    getMaxBoundsWidth(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's minimum height.
     * @param postLayoutTransform
     */
    getMinBoundsHeight(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's minimum width.
     * @param postLayoutTransform
     */
    getMinBoundsWidth(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's preferred height.
     * @param postLayoutTransform
     */
    getPreferredBoundsHeight(postLayoutTransform?: boolean): void;
    /**
     * Returns the element's preferred width.
     * @param postLayoutTransform
     */
    getPreferredBoundsWidth(postLayoutTransform?: boolean): void;
    /**
     * Returns the item in the dataProvider that was used by the specified Repeater to produce this Repeater, or null if this Repeater isn't repeated.
     * @param whichRepeater
     */
    getRepeaterItem(whichRepeater?: number): void;
    /**
     * Gets a style property that has been set anywhere in this component's style lookup chain.
     * @param styleProp
     */
    getStyle(styleProp: string): void;
    /**
     * Converts a Point object from global to content coordinates.
     * @param point
     */
    globalToContent(point: number): void;
    /**
     * Returns true if currentCSSState is not null.
     */
    hasCSSState(): void;
    /**
     * Determines whether the specified state has been defined on this UIComponent.
     * @param stateName
     */
    hasState(stateName: string): void;
    /**
     * This method will return the browser type.
     */
    getBroserType(): string;
    /**
     * Returns a box Matrix which can be passed to the drawRoundRect() method as the rot parameter when drawing a horizontal gradient.
     * @param x
     * @param y
     * @param width
     * @param height
     */
    horizontalGradientMatrix(x: number, y: number, width: number, height: number): void;
    /**
     * Initializes the internal structure of this component.
     */
    initialize(): void;
    /**
     *  Sets a style property on this component instance.
     *
     *  <p>This can override a style that was set globally.</p>
     *
     *  <p>Calling the <code>setStyle()</code> method can result in decreased performance.
     *  Use it only when necessary.</p>
     *
     *  @param styleProp Name of the style property.
     *
     *  @param newValue New value for the style.
     */
    setStyle(styleProp: string, newValue: string): void;
    /**
     *  Sets the focus to this component.
     *  The component can in turn pass focus to a subcomponent.
     *  <p><b>Note:</b> Only the TextInput and TextArea controls show a highlight
     *  when this method sets the focus.
     *  All controls show a highlight when the user tabs to the control.</p>
     */
    setFocus(): void;
    /**
     *  Called when the <code>visible</code> property changes.
     *  Set the <code>visible</code> property to show or hide
     *  a component instead of calling this method directly.
     *
     *  @param value The new value of the <code>visible</code> property.
     *  Specify <code>true</code> to show the component, and <code>false</code> to hide it.
     *
     *  @param noEvent If <code>true</code>, do not dispatch an event.
     *  If <code>false</code>, dispatch a <code>show</code> event when
     *  the component becomes visible, and a <code>hide</code> event when
     *  the component becomes invisible.
     */
    setVisible(value: boolean, noEvent?: boolean): void;
    /**
     * This method is used to add child to current
     * view.
     */
    addChild(child: any): void;
    /**
    * This method is used to add button events listeners.
    */
    addEventsListenersForTooltip(element?: Element): void;
    /**
     * This method is used to add button events listeners.
     */
    addAllOutputsEventsListeners(element?: Element): void;
    /**
     * Removes all handlers attached to the element.
     */
    removeAllOuputsEventsListeners(element: any): void;
    getComponentName(): any;
    /**
     * This function used to adapt layout measure
     * it will append px if no % sign.
     * @param value
     * @param defaultValue
     */
    adaptUnit(value: any, defaultValue?: string): string;
    /**
     * convert entered value as string to boolean.
     * @param value
     */
    adaptValueAsBoolean(value: any): any;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
export interface IUIComponent {
    name: any;
    id: any;
    styleName: any;
}
