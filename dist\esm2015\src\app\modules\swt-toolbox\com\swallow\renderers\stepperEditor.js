/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
export class stepperEditor {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.mouseDownFlag = false;
        this.mouseUpFlag = false;
        this.logger = null;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.logger = new Logger('stepperEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    init() {
        this.loadValue(this.args.item);
        this.logger.info('Method [stepperEditor] init - START- ');
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting 
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        if (this.showHideCells) {
            this.$input = $(`<input class="main-input " ${this.enableFlag ? '' : 'disabled'} style="text-align: right; padding-right: 2px!important; width:50px;height: 98%; background-color: white!important;" type="number" min="0" max="2000" step="1" value ="${this.defaultValue}"/>
                  <span class="stepper-up-container renderAsInput ${this.enableFlag ? '' : 'disabled-container'}" id="up"    ><span  class=" render-stepper-arrow-up" ></span></span>
                  <span class="stepper-down-container renderAsInput ${this.enableFlag ? '' : 'disabled-container'}" id="down" ><span  class="  render-stepper-arrow-down" ></span></span>
          `);
            this.$input.appendTo(this.args.container);
            this.$input.focus();
            if (this.enableFlag) {
                //focus 
                $(this.$input[2]).css('background-color', '#EED299');
                $(this.$input[4]).css('background-color', '#EED299');
                $(this.$input[0]).css('outline', '1px solid #49B9FF');
                $(this.$input[2]).css('box-shadow', '0 0 3px #49B9FF');
                $(this.$input[4]).css('box-shadow', '0 0 3px #49B9FF');
                /** @type {?} */
                var _this = this;
                /** @type {?} */
                var grid = $(this.args.container.parentElement.parentElement);
                /** @type {?} */
                var parent = $(this.args.container.parentElement);
                // - Stepper Up click
                $(document).ready((/**
                 * @return {?}
                 */
                function () {
                    // - Stepper UP .
                    $(parent).on('mousedown', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = true;
                        $(_this.$input[2]).css('background-color', '#EDEDED');
                    }));
                    $(parent)
                        .on('mouseup', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = false;
                        $(_this.$input[2]).css('background-color', '#F7E2B5');
                    }))
                        .on('mouseleave', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = false;
                        $(_this.$input[2]).css('background-color', '#F7E2B5');
                    }));
                    /** @type {?} */
                    var checkUpExist = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.mouseUpFlag) {
                            /** @type {?} */
                            var val = _this.getValue();
                            if (val >= 0) {
                                val++;
                                _this.setValue(String(val));
                            }
                        }
                    }), 100);
                    // - Stepper DOWN .
                    $(parent).on('mousedown', '.stepper-down-container', (/**
                     * @param {?} e
                     * @return {?}
                     */
                    function (e) {
                        _this.mouseDownFlag = true;
                        $(_this.$input[4]).css('background-color', '#EDEDED');
                    }));
                    $(parent)
                        .on('mouseup', '.stepper-down-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseDownFlag = false;
                        $(_this.$input[4]).css('background-color', '#F7E2B5');
                    }))
                        .on('mouseleave', '.stepper-down-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseDownFlag = false;
                        $(_this.$input[4]).css('background-color', '#F7E2B5');
                    }));
                    /** @type {?} */
                    var checkDownExist = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.mouseDownFlag) {
                            /** @type {?} */
                            var val = _this.getValue();
                            if (val > 0) {
                                val--;
                                _this.setValue(String(val));
                            }
                        }
                    }), 100);
                    $(parent).on('focusout', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.setValue(String(_this.getValue()));
                        event.stopPropagation();
                    }));
                }));
            }
        }
        else {
            this.$input = $(``);
        }
        this.logger.info('Method [stepperEditor] init - END - ');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    destroy() {
        this.logger.info('Method [destroy] init - START - ');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.logger.info('Method [destroy] init - END - ');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    focus() {
        this.logger.info('Method [focus] - START/END');
        this.$input.focus();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    getValue() {
        return Number(this.$input.val());
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    setValue(val) {
        this.logger.info('method [setValue] -START-');
        if (this.showHideCells && this.enableFlag) {
            this.$input.val(val);
            this.applyValue(this.args.item, val);
            /*console.log('this.commonGrid.changes.size() = ',this.commonGrid.changes.size() )
            console.log('this.this.defaultValue = ',this.defaultValue )
            console.log('this.originalDefaultValue = ',this.originalDefaultValue )
            console.log('this.args.item[this.args.column.field] =',this.args.item[this.args.column.field])*/
            // -- check if changed
            this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
             * @param {?} x
             * @return {?}
             */
            x => ((x.crud_data.id == this.args.item.id))));
            if (this.CRUD_CHANGES_DATA) {
                this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : null;
            }
            if ((this.originalDefaultValue == null && this.defaultValue != this.args.item[this.args.column.field]) || ((this.originalDefaultValue != null) && (this.originalDefaultValue != this.args.item[this.args.column.field]))) {
                /** @type {?} */
                var thereIsInsert = false;
                if (this.commonGrid.changes.size() > 0) {
                    /** @type {?} */
                    var crudInsert = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                    if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                        thereIsInsert = true;
                }
                if (!thereIsInsert) {
                    //console.log('is changed so execute item changed function' )
                    /** @type {?} */
                    var original_row = [];
                    for (let key in this.args.item) {
                        if (key != 'slickgrid_rowcontent') {
                            original_row[key] = this.args.item[key];
                        }
                        else {
                            break;
                        }
                    }
                    original_row['slickgrid_rowcontent'] = {};
                    for (let key in this.args.item['slickgrid_rowcontent']) {
                        original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                    }
                    original_row[this.args.column.field] = this.defaultValue;
                    original_row['slickgrid_rowcontent'][this.args.column.field] = { content: this.defaultValue };
                    /** @type {?} */
                    var updatedObject = {
                        rowIndex: this.args.item.id,
                        columnIndex: this.args.column.columnorder,
                        new_row: this.args.item,
                        original_row: original_row,
                        changedColumn: this.args.column.field,
                        oldValue: this.defaultValue,
                        newValue: this.getValue()
                    };
                    this.commonGrid.spyChanges({ field: this.args.column.field });
                    this.commonGrid.updateCrud(updatedObject);
                    this.commonGrid.columnSelectChanged(this.args.item);
                    SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                }
            }
            else if ((this.originalDefaultValue == this.args.item[this.args.column.field])) {
                /** @type {?} */
                var crudChange = this.commonGrid.changes.getValues().find((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => ((x.crud_data.id == this.args.item.id))));
                if (crudChange) {
                    /** @type {?} */
                    var ch = String("U(" + this.args.column.field + ")");
                    if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                    }
                    else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                    }
                    else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                    }
                    if (crudChange['crud_operation'] == "") {
                        /** @type {?} */
                        var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => ((x.crud_data.id == this.args.item.id))));
                        this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                    }
                }
                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                if (this.commonGrid.changes.size() == 0)
                    this.commonGrid.spyNoChanges({ field: this.args.column.field });
                SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
            }
        }
        this.logger.debug('method [setValue] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('Method [loadValue] - START/END -');
        this.defaultValue = item[this.args.column.field] || '';
        this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field];
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    save() {
        this.logger.info('Method [save] - START/END -');
        if (this.showHideCells)
            this.args.commitChanges();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    serializeValue() {
        this.logger.info('Method [serializeValue] - START/END - ');
        return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('method [applyValue] - START/END -');
        if (this.showHideCells && this.enableFlag) {
            item[this.args.column.field] = state;
            item.slickgrid_rowcontent[this.args.column.field] = { content: state };
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    isValueChanged() {
        this.logger.info('method [isValueChanged] - START/END -');
        if (this.showHideCells && this.enableFlag) {
            /** @type {?} */
            let isChanged = (!(this.$input.val() === '' && this.defaultValue == null)) && (this.$input.val() !== this.defaultValue);
            return isChanged;
        }
        else {
            return false;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    validate() {
        this.logger.info('method [validate] - START/END -');
        return {
            valid: true,
            msg: null
        };
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.mouseDownFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.mouseUpFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.logger;
    /** @type {?} */
    stepperEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    stepperEditor.prototype.CRUD_DATA;
    /** @type {?} */
    stepperEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    stepperEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    stepperEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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