import 'jquery-ui-dist/jquery-ui';
import { Editor } from 'angular-slickgrid';
export declare class NumberAmountEditor implements Editor {
    private args;
    private validationResult;
    private $input;
    private defaultValue;
    private logger;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    CRUD_ORIGINAL_DATA: string;
    CRUD_CHANGES_DATA: any;
    originalDefaultValue: any;
    private enableFlag;
    private showHideCells;
    private commonGrid;
    private columnDef;
    private maxChars;
    constructor(args: any);
    init(): void;
    destroy(): void;
    focus(): void;
    getValue(): any;
    setValue(val: string): void;
    loadValue(item: any): void;
    save(): void;
    serializeValue(): any;
    applyValue(item: any, state: any): void;
    isValueChanged(): boolean;
    validate(): {
        valid: boolean;
        msg: any;
    };
}
