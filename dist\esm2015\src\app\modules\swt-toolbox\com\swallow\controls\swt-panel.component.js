/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ViewChild, ElementRef, ViewContainerRef, Renderer2 } from "@angular/core";
import { focusManager } from "../managers/focus-manager.service";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
const $ = require('jquery');
export class SwtPanel extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this._title = "";
    }
    /**
     * addChild : append a dynamically created component.
     * @param {?} type : Component type
     * @return {?}
     */
    addChild(type) {
        /** @type {?} */
        var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
        /** @type {?} */
        var componentInstance = this.container.createComponent(comp);
        /* Push the component so that we can keep track of which components are created */
        this.components.push(componentInstance);
        $(componentInstance.instance).attr('id', "dynamic-" + Math.random().toString(36).substr(2, 5));
        return (componentInstance.instance);
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        $(this.elem.nativeElement.children[0]).off('click.' + this.id);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        /*
         * when mouse hover to panel a default toolTip appear.
         * this line will remove this behavior.
         * */
        $(this.elem.nativeElement).attr("title", null);
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtPanel');
        // - Setting Title.
        if (this.title) {
            /** @type {?} */
            var panelTitle = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]).find('.panelTitle') : null;
            if (panelTitle.length > 0) {
                $(panelTitle).removeClass('hidden');
                $(panelTitle).text(String(this.title));
            }
        }
        // - Setting StyleName.
        if (this.styleName) {
            /** @type {?} */
            var panel = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]) : null;
            if (panel.length > 0) {
                $(panel).addClass(this.styleName);
            }
        }
        console.log('panel there');
        // update focus Manager data (focused element)
        $(this.elem.nativeElement.children[0]).on('click.' + this.id, (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            focusManager.focusTarget = this.id;
        }));
    }
    /*-----------------------------------*/
    /**
     * @param {?} value
     * @return {?}
     */
    set styleName(value) {
        this._styleName = String(value);
        /** @type {?} */
        var panel = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]) : null;
        if (panel.length > 0) {
            $(panel).addClass(this._styleName);
        }
    }
    /**
     * @return {?}
     */
    get styleName() {
        return this._styleName;
    }
    /*-----------------------------------*/
    /**
     * @param {?} value
     * @return {?}
     */
    set title(value) {
        this._title = String(value);
        /** @type {?} */
        var panelTitle = $($(this.elem.nativeElement).children()[0]).find('.panelTitle');
        if (panelTitle.length > 0 && this._title) {
            $(panelTitle).removeClass('hidden');
            $(panelTitle).text(String(value));
        }
    }
    /**
     * @return {?}
     */
    get title() {
        return this._title;
    }
}
SwtPanel.decorators = [
    { type: Component, args: [{
                selector: 'SwtPanel',
                template: `
    <div fxLayout="column"  class="panelLayout panelInsideFormLayout" tabindex="-1" >
         <div class="panelTitle hidden" tabindex="-1"> </div>
         <div fxLayout="column" fxLayoutAlign="{{verticalAlign}} {{horizontalAlign}}"  fxLayoutGap="{{verticalGap}}"   class="panelBody" tabindex="-1">
             <ng-content ></ng-content>
             <ng-container #container></ng-container> 
        </div>
    </div>
  `,
                styles: [`
             :host {
               margin: 0 0px 5px 0;
               display: block;
             }
             .panelLayout {
               box-sizing: border-box;
               width: 100%;
               height: 100%;
               outline: none;
             }
   `]
            }] }
];
/** @nocollapse */
SwtPanel.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
SwtPanel.propDecorators = {
    container: [{ type: ViewChild, args: ['container', { read: ViewContainerRef },] }],
    styleName: [{ type: Input, args: ['styleName',] }],
    title: [{ type: Input, args: ["title",] }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._title;
    /** @type {?} */
    SwtPanel.prototype.container;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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