/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, HostListener } from '@angular/core';
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "../utils/common.service";
import { Logger } from "../logging/logger.service";
import { SwtAlert } from "../utils/swt-alert.service";
import { JSONReader } from "../jsonhandler/jsonreader.service";
import { StringUtils } from '../utils/string-utils.service';
import { focusManager } from '../managers/focus-manager.service';
var SwtComboBox = /** @class */ (function () {
    function SwtComboBox(elem, commonService) {
        this.elem = elem;
        this.commonService = commonService;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        // private variable to handle drop down visibility status.
        this._isDropdownOpen = false;
        // private variable to handle selected index.
        this._selecedIndex = -1;
        // private variable to handle enabled status.
        this._enabled = true;
        // private variable to handle editable status.
        this._editable = true;
        // private variable to handle visibility.
        this._visible = true;
        // private variable to store selected item.
        this._selectedItem = { content: '', value: '', type: '', selected: 0 };
        // private function to handle drop down open.
        this._inputClick = new Function();
        // private function to handle drop down open.
        this._open = new Function();
        // private function to handle drop down close.
        this._close = new Function();
        // private function to handle comboBox focus.
        this._focus = new Function();
        // private function to handle comboBox focus out.
        this._focusout = new Function();
        // private function to handle comboBox change.
        this._change = new Function();
        // private function to handle comboBox change.
        this._onTextChange = new Function();
        // private variable to hold filter state
        this.filterState = false;
        //private variable to handle alert show
        this._alertvisiblity = false;
        //private variable to store combo data provider.
        this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
        //private variable to handle drop down closing.
        this._outside = true;
        //private variable to handle drop down closing.
        this._insideButton = false;
        //private variable to handle required fields
        this._required = false;
        // Input to handle dataLabel.
        this.dataLabel = "";
        //variable to hold interruptComms
        this.interrupted = false;
        this._ignored_validation = "";
        this.ignoredValidationArr = [];
        this._default_ignored = "closeButton,cancelButton,btnCancel,btnClose";
        this.backGroundImage = "url('assets/images/pdfUp.jpg')";
        this.onOpenSelectedIndex = -1;
        this._shiftUp = 0;
        this.showDescriptionInDropDown = false;
        this._toolTipPreviousObject = null;
        // Input to store width.
        this.prompt = "";
        // Input to store width.
        this.width = "200";
        // Input to store component id.
        this.id = "";
        this.firstCall = true;
        // Output to fire open event.
        this.open_ = new EventEmitter();
        // Output to fire open event.
        this.inputClick_ = new EventEmitter();
        // Output to fire open event.
        this.onTextChange = new EventEmitter();
        // Output to fire close event.
        this.close_ = new EventEmitter();
        // Output to fire focus event.
        this.focus_ = new EventEmitter();
        // Output to fire focus out event.
        this.focusout_ = new EventEmitter();
        // Output to fire change event.
        this.change_ = new EventEmitter();
        // initialize logger.
        this.logger = new Logger('SwtComboBox', commonService.httpclient, 6);
        // initialize setter.
        this.SwtAlert = new SwtAlert(commonService);
    }
    /**
     * @param {?} targetElement
     * @return {?}
     */
    SwtComboBox.prototype.mouseWeelEventHandler = /**
     * @param {?} targetElement
     * @return {?}
     */
    function (targetElement) {
        /** @type {?} */
        var position = $(this.elem.nativeElement.children[0]).offset();
        position.top = position.top + 24;
        $(this.elem.nativeElement.children[0].children[1]).css(position);
        /** @type {?} */
        var listScrolling = this.elem.nativeElement.contains(targetElement);
        if (!listScrolling) {
            this.hideDropDown(this);
        }
    };
    Object.defineProperty(SwtComboBox.prototype, "toolTip", {
        get: /**
         * @return {?}
         */
        function () {
            return this.toolTip;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.toolTipObject = $(this.elem.nativeElement);
            /** @type {?} */
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.toolTipObject.attr("title", value);
                _this_1.toolTipObject.tooltip({
                    position: { my: "left+20 center", at: "right-20 bottom+20" },
                    show: { duration: 800, delay: 500 },
                    open: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        $(_this.toolTipObject).removeAttr('title');
                    })
                });
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "toolTipPreviousValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._toolTipPreviousObject;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTipPreviousObject = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "focusOutSide", {
        get: /**
         * @return {?}
         */
        function () {
            return this._outside;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "ignored_validation", {
        /**
         * Getter the _ignored_validation variable
         * */
        get: /**
         * Getter the _ignored_validation variable
         *
         * @return {?}
         */
        function () {
            return this._ignored_validation;
        },
        /**
     * Setter for the _ignored_validation variable
     * */
        set: /**
         * Setter for the _ignored_validation variable
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._ignored_validation = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "required", {
        get: /**
         * @return {?}
         */
        function () {
            return this._required;
        },
        /* input to hold component visibility */
        set: /* input to hold component visibility */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) == "string") {
                if (value === 'true') {
                    this._required = true;
                }
                else {
                    this._required = false;
                }
            }
            else {
                this._required = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "interruptComms", {
        get: /**
         * @return {?}
         */
        function () {
            return this.interrupted;
        },
        set: /**
         * @param {?} interrupted
         * @return {?}
         */
        function (interrupted) {
            this.interrupted = interrupted;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    SwtComboBox.prototype.inputClickHandler = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // emit open event.
        this.inputClick_.emit(event);
        // execute callback.
        this._inputClick(event);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtComboBox.prototype.inputChangeHandler = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (String($(this.filter.nativeElement).val()).length > 0 && this.dataProvider.length > 0 && this._outside) {
            /** @type {?} */
            var index = this.getIndexOf($(this.filter.nativeElement).val());
            if (index == -1 && this.dataProvider.length > 0) {
                this.selectedItem = this._dataProvider[0];
                this.selectItem(this._dataProvider[0], true);
            }
            else if (index == -1) {
                this.selectedIndex = -1;
            }
            else {
                this.selectedIndex = index;
            }
        }
        else if (String($(this.filter.nativeElement).val()).length == 0) {
            this.selectedIndex = -1;
        }
        else {
            this.selectedIndex = -1;
        }
    };
    Object.defineProperty(SwtComboBox.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        // Input to hold comboBox dataProvider
        set: 
        // Input to hold comboBox dataProvider
        /**
         * @param {?} dataProvider
         * @return {?}
         */
        function (dataProvider) {
            try {
                if (dataProvider !== null && dataProvider.length > 0) {
                    for (var i = 0; i < dataProvider.length; i++) {
                        if (dataProvider[i].content === undefined) {
                            dataProvider[i]["content"] = "";
                        }
                    }
                    if (dataProvider.length > 0) {
                        // update combo dataProvider.
                        this._dataProvider = dataProvider;
                        if (this.selectedIndex == 0) {
                            // update selected label and text
                            this._selectedLabel = this._text = dataProvider[0];
                        }
                        // store copy of data provider.
                        this._originalData = dataProvider;
                        // store copy of data provider.
                        this._tempData = dataProvider;
                        // update selected item.
                        this.updateSelectedItem(dataProvider[0]);
                    }
                }
                else {
                    this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
                    $(this.filter.nativeElement).val("");
                }
            }
            catch (error) {
                this.logger.error("method [ dataProvider ] - error :", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        // enabled getter and setter
        set: 
        // enabled getter and setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._enabled = true;
                    this.filter.nativeElement.disabled = false;
                    $(this.elem.nativeElement.children[0].children[0].children[1]).bind("click");
                    $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).removeClass("disable");
                    $(this.elem.nativeElement.children[0].children[0].children[1]).removeClass("input-group-addon-disable");
                }
                else {
                    this._enabled = false;
                    this.filter.nativeElement.disabled = true;
                    $(this.elem.nativeElement.children[0].children[0].children[1]).unbind("click");
                    $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).addClass("disable");
                    $(this.elem.nativeElement.children[0].children[0].children[1]).addClass("input-group-addon-disable");
                }
            }
            else {
                this.filter.nativeElement.disabled = !value;
                this._enabled = value;
                if (value) {
                    $(this.elem.nativeElement.children[0].children[0].children[1]).click((/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this_1.onArrowClick(event);
                    }));
                    $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).removeClass("disable");
                    $(this.elem.nativeElement.children[0].children[0].children[1]).removeClass("input-group-addon-disable");
                }
                else {
                    $(this.elem.nativeElement.children[0].children[0].children[1]).unbind("click");
                    $(this.elem.nativeElement.children[0].children[0].children[1].children[0]).addClass("disable");
                    $(this.elem.nativeElement.children[0].children[0].children[1]).addClass("input-group-addon-disable");
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        // enabled getter and setter
        set: 
        // enabled getter and setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._editable = true;
                    this.filter.nativeElement.readOnly = false;
                }
                else {
                    this._editable = false;
                    this.filter.nativeElement.readOnly = true;
                }
            }
            else {
                this.filter.nativeElement.readOnly = !value;
                this._editable = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "shiftUp", {
        get: /**
         * @return {?}
         */
        function () {
            return this._shiftUp;
        },
        // enabled getter and setter
        set: 
        // enabled getter and setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._shiftUp = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visible;
        },
        // enabled getter and setter
        set: 
        // enabled getter and setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._visible = true;
                    $(this.elem.nativeElement).show();
                }
                else {
                    this._visible = false;
                    $(this.elem.nativeElement).hide();
                }
            }
            else {
                if (value) {
                    $(this.elem.nativeElement).show();
                }
                else {
                    $(this.elem.nativeElement).hide();
                }
                this._visible = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    // This method will be fired after vieww init.
    // This method will be fired after vieww init.
    /**
     * @return {?}
     */
    SwtComboBox.prototype.ngAfterViewInit = 
    // This method will be fired after vieww init.
    /**
     * @return {?}
     */
    function () {
        if (this.shiftUp != 0) {
            $(this.dropDownContainer.nativeElement).css("margin-top", "-" + this.shiftUp + "px");
        }
    };
    /**
     * @return {?}
     */
    SwtComboBox.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this_1 = this;
        try {
            $($(this.listitem.nativeElement)[0]).on("mouseenter", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this_1._outside = false;
            }));
            $($(this.listitem.nativeElement)[0]).on("mouseleave", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this_1._outside = true;
            }));
            $(this.elem.nativeElement.children[0].children[0].children[1]).on("mouseenter", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this_1._insideButton = true;
            }));
            $(this.elem.nativeElement.children[0].children[0].children[1]).on("mouseleave", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this_1._insideButton = false;
            }));
            // show drop down list in filter.
            $(this.elem.nativeElement.children[0].children[0].children[0]).keydown((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                // if arrow up clicked.
                if (event.keyCode === 38) {
                    if (_this_1.selectedIndex > 0) {
                        _this_1.selectedIndex--;
                    }
                    if (!_this_1._isDropdownOpen) {
                        _this_1.change_.emit({ target: _this_1, event: event });
                    }
                    // update the selected item.
                    _this_1.selectedIndex > 0 ? _this_1.updateSelectedItem(_this_1.dataProvider[_this_1.selectedIndex]) : null;
                    // synchronize the scrolling.
                    _this_1.scrollToTop();
                }
                else if (event.keyCode === 40) {
                    if (_this_1.selectedIndex < _this_1.dataProvider.length - 1) {
                        _this_1.selectedIndex++;
                    }
                    if (!_this_1._isDropdownOpen) {
                        _this_1.change_.emit({ target: _this_1, event: event });
                    }
                    // update the selected item.
                    _this_1.selectedIndex < _this_1.dataProvider.length - 1 ? _this_1.updateSelectedItem(_this_1.dataProvider[_this_1.selectedIndex]) : null;
                    // synchronize the scrolling.
                    _this_1.scrollToBottom();
                    // if ENTER key clicked.
                }
                else if (event.keyCode === 13) {
                    // select the current item.
                    if (($(_this_1.filter.nativeElement).val()).length > 0) {
                        _this_1.fillSelectedIndexWithFiltertext();
                    }
                    _this_1.selectItem(_this_1.dataProvider[_this_1.selectedIndex], false, true);
                    if (_this_1.selectedIndex == -1)
                        _this_1.showAlert();
                    _this_1.updateSelectedItem(_this_1.selectedItem);
                    _this_1._dataProvider = _this_1._originalData;
                    _this_1.change_.emit({ target: _this_1, event: event });
                }
                else if (event.keyCode === 27) {
                    _this_1.hideDropDown(event);
                }
                else if (event.keyCode === 9) {
                    _this_1.hideDropDown(event);
                }
                else {
                    _this_1.filterDropDown(event, true);
                }
                // if arrow down clicked.
                _this_1.onTextChange.emit({ target: _this_1, event: event });
            }));
            //show drop down on arrow click.
            if (this.enabled) {
                $(this.elem.nativeElement.children[0].children[0].children[1]).click((/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    _this_1.onArrowClick(event);
                }));
            }
            else {
                $(this.elem.nativeElement.children[0].children[0].children[1]).css("backround-color", "red");
            }
            // handle comboBox focus look and feel.
            $(this.elem.nativeElement.children[0].children[0].children[0]).focus((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this_1.onOpenSelectedIndex = _this_1.selectedIndex;
                // set comboBox border.
                //              $($(this.elem.nativeElement.children[0].children[0])[0]).css("border","1px solid #49B9FF");
                // remove comboBox border.
                //              $($(this.elem.nativeElement.children[0].children[0])[0]).css("border-radius","3px");
                // emit focus event.
                _this_1.focus_.emit(event);
                // execute callback
                _this_1._focus(event);
                if (_this_1.id != "exportDataComponent") {
                    $(_this_1.elem.nativeElement.children[0]).css("outline-style", "solid");
                    $(_this_1.elem.nativeElement.children[0]).css("outline-color", "#49B9FF");
                    $(_this_1.elem.nativeElement.children[0]).css("outline-width", "2px");
                }
                else {
                    _this_1.filter.nativeElement.readOnly = true;
                    _this_1.filter.nativeElement.blur();
                }
            }));
            // handle comboBox focus out.
            $(this.elem.nativeElement.children[0].children[0].children[0]).focusout((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                /** @type {?} */
                var tempIndex = _this_1.selectedIndex;
                if (String($(_this_1.filter.nativeElement).val()).length > 0 && _this_1.dataProvider.length > 0 && _this_1._outside) {
                    /** @type {?} */
                    var index = _this_1.getIndexOf($(_this_1.filter.nativeElement).val());
                    if (index == -1) {
                        _this_1.selectedItem = _this_1._dataProvider[0];
                        _this_1.selectItem(_this_1._dataProvider[0], true);
                    }
                    else {
                        _this_1.selectedIndex = index;
                    }
                }
                else if (String($(_this_1.filter.nativeElement).val()).length == 0) {
                    // update selected item.
                    _this_1._selectedItem = null;
                    // update selected value
                    _this_1._selectedValue = null;
                    // update selected label.
                    _this_1._selectedLabel = null;
                    // update text
                    _this_1._text = null;
                    //update selected index.
                    _this_1._selecedIndex = -1;
                }
                // emit focus out event.
                _this_1.focusout_.emit(event);
                //execute callback
                _this_1._focusout(event);
                _this_1.showAlert();
                $(_this_1.elem.nativeElement.children[0]).css("outline", "none");
            }));
            $(this.elem.nativeElement.children[0].children[0].children[0]).focusout((/**
             * @return {?}
             */
            function () {
                // remove comboBox border.
                $($(_this_1.elem.nativeElement.children[0].children[0])[0]).css("border", "none");
                // remove ComboBox Border radius.
                $($(_this_1.elem.nativeElement.children[0].children[0])[0]).css("border-radius", "3px");
            }));
            // close drop down by clicking outside.
            $(document).on('mouseup.' + this.id, (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                if (_this_1._isDropdownOpen) {
                    if (!$($(_this_1.elem.nativeElement.children[0].children[0])[0]).is(event.target)
                        && $(_this_1.elem.nativeElement.children[0].children[1].children[0]).has(event.target).length === 0) {
                        if (_this_1._outside) {
                            // hide drop down.
                            _this_1.hideDropDown(event);
                        }
                    }
                }
            }));
            // set comboBox width
            $(this.elem.nativeElement.children[0]).width(this.width);
            $(this.elem.nativeElement.children[0].children[1]).width(this.width);
            //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtComboBox.
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtComboBox');
            // set id to button DOM.
            if (this.id) {
                $($(this.elem.nativeElement)[0]).attr("id", this.id);
            }
            //-END-.
        }
        catch (error) {
            this.logger.error("method [ ngOnInit ] - error :", error);
        }
    };
    /**
     * @return {?}
     */
    SwtComboBox.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        $((/** @type {?} */ (document))).on('mouseup.' + this.id).off('mouseup.' + this.id);
        try {
            if (this.elem.nativeElement.children && this.elem.nativeElement.children[0] && this.elem.nativeElement.children[0].children[0]) { }
            $(this.elem.nativeElement.children[0].children[0].children[0]).off();
            $(this.elem.nativeElement.children[0].children[0].children[1]).off();
            $($(this.listitem.nativeElement)[0]).off();
        }
        catch (error) {
        }
    };
    /**
     * This method is called when arrow clicked.
     */
    /**
     * This method is called when arrow clicked.
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtComboBox.prototype.onArrowClick = /**
     * This method is called when arrow clicked.
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // check drop down visibility
        if ($(this.elem.nativeElement.children[0].children[1].children[0]).is(":visible")) {
            // hide drop down if is open.
            this.hideDropDown(event);
        }
        else {
            // show drop down if is closed.
            this.showDropDown(event);
            // set comboBox filter focus.
            $(this.filter.nativeElement).focus();
        }
        if (this.filterState) {
            this._dataProvider = this._originalData;
            this.filterState = false;
        }
    };
    /**
     * This method is used to filter comboBox.
     */
    /**
     * This method is used to filter comboBox.
     * @private
     * @param {?} event
     * @param {?=} fromKeyEvent
     * @return {?}
     */
    SwtComboBox.prototype.filterDropDown = /**
     * This method is used to filter comboBox.
     * @private
     * @param {?} event
     * @param {?=} fromKeyEvent
     * @return {?}
     */
    function (event, fromKeyEvent) {
        var _this_1 = this;
        if (fromKeyEvent === void 0) { fromKeyEvent = false; }
        try {
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.filterState = true;
                /** @type {?} */
                var value = $(_this_1.filter.nativeElement).val();
                /** @type {?} */
                var filtredDp = _this_1._tempData.filter((/**
                 * @param {?} item
                 * @return {?}
                 */
                function (item) {
                    if (_this_1.showDescriptionInDropDown) {
                        return (String(item.content).toUpperCase().indexOf(String($(_this_1.filter.nativeElement).val()).toUpperCase())) > -1 || (String(item.value).toUpperCase().indexOf(String($(_this_1.filter.nativeElement).val()).toUpperCase())) > -1;
                    }
                    else {
                        return (String(item.content).toUpperCase().indexOf(String($(_this_1.filter.nativeElement).val()).toUpperCase())) > -1;
                    }
                }));
                _this_1._dataProvider = filtredDp;
                if (fromKeyEvent)
                    _this_1.showDropDown(event, 0);
                else {
                    _this_1.highlightItem(0);
                    _this_1.showDropDown(event);
                }
            }), 0);
        }
        catch (error) {
            this.logger.error("method [ filterDropDown ] - error :", error);
        }
    };
    /**
     * This method is used to filter comboBox.
     */
    /**
     * This method is used to filter comboBox.
     * @private
     * @return {?}
     */
    SwtComboBox.prototype.fillSelectedIndexWithFiltertext = /**
     * This method is used to filter comboBox.
     * @private
     * @return {?}
     */
    function () {
        var _this_1 = this;
        try {
            this.filterState = true;
            /** @type {?} */
            var value = $(this.filter.nativeElement).val();
            /** @type {?} */
            var filtredDp = this._tempData.filter((/**
             * @param {?} item
             * @return {?}
             */
            function (item) {
                return String(item.content).toUpperCase().startsWith(String($(_this_1.filter.nativeElement).val()).toUpperCase());
            }));
            this._dataProvider = filtredDp;
            if (filtredDp.length > 0)
                this._selecedIndex = 0;
            else
                this._selecedIndex = -1;
        }
        catch (error) {
            this.logger.error("method [ filterDropDown ] - error :", error);
        }
    };
    /**
     * This method is used to refresh
     * comboBox view.
     */
    /**
     * This method is used to refresh
     * comboBox view.
     * @private
     * @return {?}
     */
    SwtComboBox.prototype.showAlert = /**
     * This method is used to refresh
     * comboBox view.
     * @private
     * @return {?}
     */
    function () {
        var _this_1 = this;
        // See if the focus is given to close button, If 'yes', then do not not validate
        if (this.ignoredValidationArr.length == 0) {
            /** @type {?} */
            var arr = this._ignored_validation != "" ? this._ignored_validation.split(",") : this._default_ignored.split(",");
            this.ignoredValidationArr = [];
            arr.forEach((/**
             * @param {?} element
             * @return {?}
             */
            function (element) {
                _this_1.ignoredValidationArr.push(StringUtils.trim(element));
            }));
        }
        //Currently focused property name
        /** @type {?} */
        var focusControl = focusManager.getHoverButton();
        if ((focusControl && this.ignoredValidationArr.indexOf(focusControl.id) != -1) || (this._tempData && this._tempData.length == 0))
            return;
        try {
            /** @type {?} */
            var entryExist = false;
            for (var i = 0; i < this.dataProvider.length; i++) {
                this.dataProvider[i].content = this.dataProvider[i].content || '';
                if (this.dataProvider[i] && String($(this.filter.nativeElement).val()) == this.dataProvider[i].content) {
                    entryExist = true;
                    break;
                }
            }
            if (!entryExist && (!this._insideButton && this._outside) && !this.prompt) {
                this.SwtAlert.warning("Please select the valid value", "Warning", 4, this, (/**
                 * @return {?}
                 */
                function () {
                    _this_1.setFocus();
                }), 4);
            }
        }
        catch (error) {
        }
    };
    /**
     * @private
     * @return {?}
     */
    SwtComboBox.prototype.setFilterFocus = /**
     * @private
     * @return {?}
     */
    function () {
        $(this.filter.nativeElement).focus();
        this._alertvisiblity = false;
    };
    /**
     * This method is used to show comoboBox drop Down.
     */
    /**
     * This method is used to show comoboBox drop Down.
     * @private
     * @param {?} event
     * @param {?=} highlightIndex
     * @return {?}
     */
    SwtComboBox.prototype.showDropDown = /**
     * This method is used to show comoboBox drop Down.
     * @private
     * @param {?} event
     * @param {?=} highlightIndex
     * @return {?}
     */
    function (event, highlightIndex) {
        var _this_1 = this;
        if (highlightIndex === void 0) { highlightIndex = -1; }
        try {
            /** @type {?} */
            var position = $(this.elem.nativeElement.children[0]).offset();
            position.top = position.top + 24;
            $(this.elem.nativeElement.children[0].children[1]).css(position);
            setTimeout((/**
             * @return {?}
             */
            function () {
                // make drop down list reference.
                /** @type {?} */
                var list = _this_1.listitem.nativeElement;
                // scroll to the selected item.
                list.scrollTop = _this_1.selectedIndex * 21;
                // Highlight selected item.
                if (highlightIndex > -1)
                    _this_1.highlightItem(highlightIndex);
                else
                    _this_1.highlightItem(_this_1.selectedIndex);
                if (!_this_1._isDropdownOpen) {
                    // emit open event.
                    _this_1.onOpenSelectedIndex = _this_1.selectedIndex;
                    event.currentTargetObject = _this_1;
                    _this_1.open_.emit(event);
                    // execute callback.
                    _this_1._open(event);
                }
                // show comboBox items with animation.
                if (_this_1.dataProvider.length > 0) {
                    $(_this_1.elem.nativeElement.children[0].children[1]).slideDown(150);
                }
                // update flag.
                _this_1._isDropdownOpen = true;
            }), 0);
        }
        catch (error) {
            this.logger.error("method [ showDropDown ] - error :", error);
        }
    };
    /**
     * This method is used to hide comboBox drop down.
     */
    /**
     * This method is used to hide comboBox drop down.
     * @private
     * @param {?} event
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    SwtComboBox.prototype.hideDropDown = /**
     * This method is used to hide comboBox drop down.
     * @private
     * @param {?} event
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    function (event, notFireChangeEvent) {
        if (notFireChangeEvent === void 0) { notFireChangeEvent = false; }
        try {
            // hide comboBox items with animation
            $(this.elem.nativeElement.children[0].children[1]).slideUp(10);
            if (this._isDropdownOpen) {
                // emit open event.
                event.currentTargetObject = this;
                // emit close event.
                this.close_.emit(event);
                // execute callback.
                this._close(event);
                if (this.onOpenSelectedIndex != this.selectedIndex || (this.id && this.id == "filterComboMSD")) {
                    if (!notFireChangeEvent)
                        this.change_.emit({ target: this });
                    // console.log(this.firstCall);
                    if (this.firstCall) {
                        this.originalValue = this.dataProvider[this.onOpenSelectedIndex];
                        this.firstCall = false;
                    }
                    /** @type {?} */
                    var item = this.dataProvider[this.selectedIndex];
                    //execute callback.
                    this._change(item);
                    this.spyChanges(item);
                }
            }
            // remove select behavior from all items.
            $("li").removeClass("selected");
            // update flag.
            this._isDropdownOpen = false;
        }
        catch (error) {
            this.logger.error("method [ hideDropDown ] - error :", error);
        }
    };
    /**
     * This method i used to set item to
     * dataProvioder.
     * @param item
     */
    /**
     * This method i used to set item to
     * dataProvioder.
     * @param {?} item
     * @return {?}
     */
    SwtComboBox.prototype.addItem = /**
     * This method i used to set item to
     * dataProvioder.
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.addItemAt(item, this._dataProvider.length);
    };
    /**
     * This method is used to add item at position.
     * @param item
     * @param position
     */
    /**
     * This method is used to add item at position.
     * @param {?} item
     * @param {?} position
     * @return {?}
     */
    SwtComboBox.prototype.addItemAt = /**
     * This method is used to add item at position.
     * @param {?} item
     * @param {?} position
     * @return {?}
     */
    function (item, position) {
        this._dataProvider.splice(position, 0, item);
        if (position === 0) {
            this.updateSelectedItem(item);
        }
    };
    /**
     * This method is used to select option.
     * @param option
     */
    /**
     * This method is used to select option.
     * @param {?} item
     * @param {?=} fromView
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    SwtComboBox.prototype.selectItem = /**
     * This method is used to select option.
     * @param {?} item
     * @param {?=} fromView
     * @param {?=} notFireChangeEvent
     * @return {?}
     */
    function (item, fromView, notFireChangeEvent) {
        if (fromView === void 0) { fromView = false; }
        if (notFireChangeEvent === void 0) { notFireChangeEvent = false; }
        try {
            // Update item
            this.updateSelectedItem(item, fromView);
            // hide drop down.
            // Create the event.
            /** @type {?} */
            var event = document.createEvent('Event');
            // Define that the event name is 'build'.
            event.initEvent('build', true, true);
            this.hideDropDown(event, notFireChangeEvent);
        }
        catch (error) {
            this.logger.error("method [ slectOption ] - error :", error);
        }
    };
    /**
     * @private
     * @param {?} item
     * @param {?=} fromView
     * @return {?}
     */
    SwtComboBox.prototype.updateSelectedItem = /**
     * @private
     * @param {?} item
     * @param {?=} fromView
     * @return {?}
     */
    function (item, fromView) {
        if (fromView === void 0) { fromView = false; }
        this.logger.info("updateSelectedItem START");
        try {
            // temporary variable to store selected index.
            /** @type {?} */
            var tempIndex = this.selectedIndex;
            // get filter input reference.
            /** @type {?} */
            var filter_1 = this.filter.nativeElement;
            if (item !== undefined && item !== null) {
                // update selected item.
                this._selectedItem = item;
                // update selected value
                this._selectedValue = item.value;
                // update selected label.
                this._selectedLabel = item.content;
                // update text
                this._text = item.content;
                // highlight selected item.
                this.highlightItem(this.getIndexOf(item.content));
                //update selected index.
                this._selecedIndex = this.getIndexOf(item.content);
                // set value if item exist
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    $(filter_1).val(item.content);
                }), 0);
            }
            else {
                // set value if item exist
                filter_1.value = "";
                // update selected item.
                this._selectedItem = null;
                // update selected value
                this._selectedValue = null;
                // update selected label.
                this._selectedLabel = null;
                // update text
                this._text = null;
                //update selected index.
                this._selecedIndex = -1;
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    $(filter_1).val("");
                }), 0);
            }
            // emit change event if selected item changed.
            //   if (tempIndex !== this.selectedIndex && fromView) {
            //         this.change_.emit( {target: this});
            //           if(this.firstCall){
            //               this.originalValue = this.dataProvider[tempIndex];
            //               this.firstCall = false;
            //           }
            //           //execute callback.
            //           this._change(item);
            //         this.spyChanges(item);
            //   }
        }
        catch (error) {
            this.logger.error("method [updateSelectedItem] - error ", error);
        }
        this.logger.info("updateSelectedItem END");
    };
    /**
     * @param {?} prompt
     * @return {?}
     */
    SwtComboBox.prototype.setPrompt = /**
     * @param {?} prompt
     * @return {?}
     */
    function (prompt) {
        $(this.filter.nativeElement).attr("placeholder", prompt);
    };
    /**
     * @param {?} input
     * @param {?=} reverse
     * @return {?}
     */
    SwtComboBox.prototype.setComboData = /**
     * @param {?} input
     * @param {?=} reverse
     * @return {?}
     */
    function (input, reverse) {
        if (reverse === void 0) { reverse = false; }
        this.setComboDataAndForceSelected(input, reverse, null);
    };
    /**
     * @param {?} input
     * @param {?=} reverse
     * @param {?=} selectedValue
     * @return {?}
     */
    SwtComboBox.prototype.setComboDataAndForceSelected = /**
     * @param {?} input
     * @param {?=} reverse
     * @param {?=} selectedValue
     * @return {?}
     */
    function (input, reverse, selectedValue) {
        if (reverse === void 0) { reverse = false; }
        try {
            this.logger.info("setComboData START");
            /* This time out is added to fix issue that when ComboBox created dynamically
                     * and when we set datProvider using setComboData function we obtain dropDown list
                     * but the selected item not appear in filter of comboBox.
                     */
            //          setTimeout(() => {
            // temporary array to inverse dataProvider.
            /** @type {?} */
            var tempData = new Array();
            // variable to store selected item.
            /** @type {?} */
            var selected = void 0;
            if (!input) {
                this._dataProvider = [{ content: '', value: '', type: '', selected: 0 }];
                $(this.filter.nativeElement).val("");
                return;
            }
            this._selectedItem = input[0];
            if (!reverse) {
                // find the correspondent dataProvider from selects
                if (JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")]').length > 0) {
                    // update dataProvider.
                    /** @type {?} */
                    var data = JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0];
                    if (data) {
                        if (data.length) {
                            this._dataProvider = this._tempData = this.reverse(JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]);
                        }
                        else {
                            this._dataProvider = this._tempData = this.reverse([JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]]);
                        }
                    }
                    // find the selected item.
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
                else {
                    if (input.length === undefined) {
                        input = [input];
                    }
                    this._dataProvider = this.reverse(input);
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
            }
            else {
                // find the correspondent dataProvider from selects
                if (JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")]').length > 0) {
                    // update dataProvider.
                    /** @type {?} */
                    var data = JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0];
                    if (data) {
                        if (data.length) {
                            this._dataProvider = this._tempData = tslib_1.__spread(JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]);
                        }
                        else {
                            this._dataProvider = this._tempData = tslib_1.__spread([JSONReader.jsonpath(input, '$.select[?(@.id=="' + this.dataLabel + '")].option')[0]]);
                        }
                    }
                    // find the selected item.
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
                else {
                    if (input.length === undefined) {
                        input = [input];
                    }
                    this._dataProvider = tslib_1.__spread(input);
                    if (selectedValue == null) {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.selected=="1")]');
                        if (selected.length == 0) {
                            selected = [this.dataProvider[0]];
                        }
                    }
                    else if (selectedValue != "") {
                        selected = JSONReader.jsonpath(this.dataProvider, '$..[?(@.content=="' + selectedValue + '")]');
                    }
                    // select selected item if exist else select the first item.
                    if (selected && selected.length > 0) {
                        // update the selected item.
                        this.updateSelectedItem(selected[0]);
                    }
                }
            }
            // store copy of data provider.
            this._originalData = tslib_1.__spread(this.dataProvider);
            //          }, 0);
            this.logger.info("setComboData END");
        }
        catch (error) {
            this.logger.error("method [setComboData] - error ", error);
        }
        this.logger.debug('method [setComboData] Exit');
    };
    // handle highlighting
    // handle highlighting
    /**
     * @private
     * @param {?} index
     * @return {?}
     */
    SwtComboBox.prototype.highlightItem = 
    // handle highlighting
    /**
     * @private
     * @param {?} index
     * @return {?}
     */
    function (index) {
        try {
            // make reference to item.
            /** @type {?} */
            var list = this.listitem.nativeElement.children;
            // remove the select behavior from all items.
            $("li").removeClass("selected");
            // select the item in the given index.
            $(list[index]).addClass("selected");
        }
        catch (error) {
            this.logger.error("method [highlightItem] - error ", error);
        }
    };
    /**
     * This method is used to get index of given item.
     * @param item
     */
    /**
     * This method is used to get index of given item.
     * @param {?} item
     * @return {?}
     */
    SwtComboBox.prototype.getIndexOf = /**
     * This method is used to get index of given item.
     * @param {?} item
     * @return {?}
     */
    function (item) {
        try {
            // return the item index.
            if (this._tempData)
                return this._tempData.findIndex((/**
                 * @param {?} option
                 * @return {?}
                 */
                function (option) { return option.content == item; }));
            else
                return -1;
        }
        catch (error) {
            this.logger.error("method [getIndexOf] - error ", error);
        }
    };
    /**
     * This method is used to handle scrolling
     * to bottom.
     */
    /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    SwtComboBox.prototype.scrollToBottom = /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.listitem.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[itemIndex];
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    var viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error("method [scrollToBottom] - error ", error);
        }
    };
    /**
     * @private
     * @param {?} data
     * @return {?}
     */
    SwtComboBox.prototype.reverse = /**
     * @private
     * @param {?} data
     * @return {?}
     */
    function (data) {
        try {
            /** @type {?} */
            var tempData = new Array();
            /** @type {?} */
            var tempVar = void 0;
            for (var index = 0; index < data.length; index++) {
                tempVar = new Object;
                tempVar.content = data[index].value;
                tempVar.value = data[index].content;
                tempVar.selected = data[index].selected;
                tempVar.type = data[index].type;
                tempData.push(tempVar);
            }
            return tempData;
        }
        catch (error) {
            this.logger.error("method [reverse] - error ", error);
        }
    };
    /**
     * This method is used to handle scrolling
     * to Top.
     */
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    SwtComboBox.prototype.scrollToTop = /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.listitem.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[itemIndex];
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error("method [scrollToTop] - error ", error);
        }
    };
    Object.defineProperty(SwtComboBox.prototype, "selectedValue", {
        // selected label getter.
        get: 
        // selected label getter.
        /**
         * @return {?}
         */
        function () {
            if (this._selectedItem)
                if (this._selectedItem.value != undefined && this._selectedItem.value != null)
                    return String(this._selectedItem.value);
                else
                    return this._selectedItem.value;
            else
                return null;
        },
        // selected value setter.
        set: 
        // selected value setter.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                /** @type {?} */
                var itemIndex = 0;
                for (var index = 0; index < this.dataProvider.length; index++) {
                    if (String(this.dataProvider[index].value).toUpperCase() === value.toUpperCase()) {
                        itemIndex = index;
                        break;
                    }
                }
                this.updateSelectedItem(this.dataProvider[itemIndex]);
            }
            catch (error) {
                this.logger.error("method [selectedValue] - error ", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtComboBox.prototype.appendOption = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var item = $(this.dropDownli.nativeElement)[0];
        /** @type {?} */
        var option = item.append(new Option("option test example", "test"));
        $(this.listitem.nativeElement)[0].append(option);
    };
    Object.defineProperty(SwtComboBox.prototype, "selectedLabel", {
        // selected value getter.
        get: 
        // selected value getter.
        /**
         * @return {?}
         */
        function () {
            if (this._selectedItem)
                if (this._selectedItem.content != undefined && this._selectedItem.content != null)
                    return String(this._selectedItem.content);
                else
                    return this._selectedItem.content;
            else
                return null;
        },
        // selected label setter.
        set: 
        // selected label setter.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                /** @type {?} */
                var itemIndex = 0;
                for (var index = 0; index < this.dataProvider.length; index++) {
                    if (String(this.dataProvider[index].content).toUpperCase() === value.toUpperCase()) {
                        itemIndex = index;
                        break;
                    }
                }
                this.updateSelectedItem(this.dataProvider[itemIndex]);
            }
            catch (error) {
                this.logger.error("method [selectedValue] - error ", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to remove all element from
     * comboBox dataProvider
     */
    /**
     * This method is used to remove all element from
     * comboBox dataProvider
     * @return {?}
     */
    SwtComboBox.prototype.removeAll = /**
     * This method is used to remove all element from
     * comboBox dataProvider
     * @return {?}
     */
    function () {
        this._dataProvider = new Array();
        this.selectedIndex = -1;
    };
    /**
     * This method is used to remove element from
     * comboBox dataProvider in specific  index.
     * @param index
     */
    /**
     * This method is used to remove element from
     * comboBox dataProvider in specific  index.
     * @param {?} index
     * @return {?}
     */
    SwtComboBox.prototype.removeItemAt = /**
     * This method is used to remove element from
     * comboBox dataProvider in specific  index.
     * @param {?} index
     * @return {?}
     */
    function (index) {
        this._dataProvider.splice(index, 1);
        if (index > 0) {
            this.selectItem(this._dataProvider[index - 1], false);
        }
        else {
            this.selectItem(this._dataProvider[0], false);
        }
    };
    /**
     * This method is used to set focus
     * to comboBox.
     */
    /**
     * This method is used to set focus
     * to comboBox.
     * @return {?}
     */
    SwtComboBox.prototype.setFocus = /**
     * This method is used to set focus
     * to comboBox.
     * @return {?}
     */
    function () {
        $(this.filter.nativeElement).focus();
    };
    /**
     * @return {?}
     */
    SwtComboBox.prototype.getbackGroundImange = /**
     * @return {?}
     */
    function () {
        if (this._selectedItem && this._selectedItem.iconImage)
            if (!this._selectedItem.iconImage.startsWith('url('))
                return 'url(' + this._selectedItem.iconImage + ")";
            else
                return "";
    };
    // method to set combo visibility.
    // method to set combo visibility.
    /**
     * @param {?} visibility
     * @return {?}
     */
    SwtComboBox.prototype.setVisible = 
    // method to set combo visibility.
    /**
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        this._visible = visibility;
        this.visible = visibility;
    };
    Object.defineProperty(SwtComboBox.prototype, "isDropdownOpen", {
        // comboBox getter and setter
        get: 
        // comboBox getter and setter
        /**
         * @return {?}
         */
        function () {
            return this._isDropdownOpen;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "selectedIndex", {
        // selected index getter.
        get: 
        // selected index getter.
        /**
         * @return {?}
         */
        function () {
            return this._selecedIndex;
        },
        //selected index setter.
        set: 
        //selected index setter.
        /**
         * @param {?} index
         * @return {?}
         */
        function (index) {
            try {
                /* This timeout is added to fix the issue
                 * that if we set selectedIndex always the first item is selected
                 * for example:
                 * this.comboBox.selecctedIndex = x;
                 * the selected item is still unchanged.(in the view we get the first item selected).
                 * same problem of selectedLabel
                 * */
                //          setTimeout(() => {
                // verify that index not null and not undefined.
                if (index == -1) {
                    this.updateSelectedItem(null);
                    // update selected Index variable.
                    this._selecedIndex = index;
                    if (this.firstCall) {
                        this.originalValue = null;
                        this.firstCall = false;
                    }
                }
                else if (index <= this.dataProvider.length) {
                    // select item in the given index.
                    this.updateSelectedItem(this.dataProvider[index]);
                    // update selected Index variable.
                    this._selecedIndex = index;
                    if (this.firstCall) {
                        this.originalValue = this.dataProvider[index];
                        this.firstCall = false;
                    }
                }
                else {
                    this.updateSelectedItem(this.dataProvider[this.selectedIndex]);
                }
                //          }, 0);
            }
            catch (error) {
                console.error("[ SwtComboBox ] - selectedIndex method - error :", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "text", {
        // text getter
        get: 
        // text getter
        /**
         * @return {?}
         */
        function () {
            return this._text;
        },
        // text setter
        set: 
        // text setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                $(_this_1.filter.nativeElement).val(value);
            }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "selectedItem", {
        // selectedItem getter
        get: 
        // selectedItem getter
        /**
         * @return {?}
         */
        function () {
            return this._selectedItem;
        },
        set: /**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            this._selectedItem = item;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "open", {
        get: /**
         * @return {?}
         */
        function () {
            return this._open;
        },
        set: /**
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._open = callback;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "inputClick", {
        get: /**
         * @return {?}
         */
        function () {
            return this._inputClick;
        },
        set: /**
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._inputClick = callback;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "close", {
        get: /**
         * @return {?}
         */
        function () {
            return this._close;
        },
        set: /**
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._close = callback;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "focusout", {
        get: /**
         * @return {?}
         */
        function () {
            return this._focusout;
        },
        //  set focus(callback: Function) {
        //      this._focus = callback;
        //  }
        //  get focus() {
        //      return this._focus;
        //  }
        set: 
        //  set focus(callback: Function) {
        //      this._focus = callback;
        //  }
        //  get focus() {
        //      return this._focus;
        //  }
        /**
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._focusout = callback;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtComboBox.prototype, "change", {
        get: /**
         * @return {?}
         */
        function () {
            return this._change;
        },
        set: /**
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._change = callback;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtComboBox.prototype.resetOriginalValue = /**
     * @return {?}
     */
    function () {
        this.originalValue = this._selectedItem;
        this.spyChanges(this._selectedItem);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtComboBox.prototype.spyChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (!this.originalValue) {
            this.originalValue = this.dataProvider[this._selecedIndex];
            this.firstCall = false;
        }
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    };
    SwtComboBox.decorators = [
        { type: Component, args: [{
                    selector: 'SwtComboBox',
                    template: "\n       <div class=\"SwtComboBox-container\" \n       popper=\"{{this.toolTipPreviousValue}}\"\n             [popperTrigger]=\"'hover'\"\n             [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n       [popperPlacement]=\"'bottom'\" >\n          <div #inputGroup class=\"input-group\">\n            <input #filter type=\"text\"\n            (click)=inputClickHandler($event)\n            [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n            (change)=\"inputChangeHandler($event)\"\n            placeholder=\"{{prompt}}\"\n            [style.background-image]=\"this.getbackGroundImange()\"\n            [class.requiredInput]= \"required==true && enabled==true && !this.filter.value\"\n            class=\"form-control swtcombobox-filter-input\" >\n            <div class=\"input-group-addon\"><i class=\"glyphicon glyphicon-triangle-bottom\"></i></div> \n          </div>\n          <div #dropDownContainer class=\"swtcomboBox-dropDown\" >\n              <ul #listitem class=\"swtcomboBox-dropDown-ul\">\n                  <li #dropDownli *ngFor=\"let item of dataProvider let count = index\" \n                      [ngClass]=\"{'selected':count == 0}\"\n                      class=\"swtcomboBox-dropDown-li\"\n                      (click)=\"selectItem(item, true)\">\n                        <a *ngIf=\"item.iconImage\">\n                            <img src=\"{{item.iconImage }}\" alt=\"Facebook Icon\"  />\n                        </a>\n                        <option class=\"combo-option\" value=\"{{ item.value }}\" style=\"display: inline-block;\"> \n                        {{ item.content }}\n                      </option>{{ showDescriptionInDropDown && item.value? '  '+item.value : '' }}\n                  </li>\n                  <!--<li *ngIf=\"_exist\">{{ notFound }}</li>-->\n              </ul>\n          </div>\n        </div>\n  ",
                    styles: ["\n      .SwtComboBox-container {\n         /*width: 260px;  this width must be updated with jquery*/\n            margin: 0 5px 5px 0;\n      }\n\n      .input-group {\n         width: 100%;\n      }\n\n      .form-control {\n          background-repeat: no-repeat;\n          background-position: center; \n          background-size: 100% 100%;\n          padding: 0px 0px 0px 10px;\n          background-color: #FFF;\n            z-index: 0;\n      }\n\n      .disable {\n          color: #919999;\n      }\n\n      .swtcomboBox-dropDown-ul {\n          padding: 0px;\n          width: auto;\n          background-color: white;\n          margin: 0px;\n          max-height: 150px;\n          overflow-y: auto;\n          overflow-x: hidden;\n          padding-bottom: 3px;\n          cursor: default;\n          word-wrap: break-word;\n      }\n\n      .swtcomboBox-dropDown-li {\n         list-style-type: none;\n         width: 100%;\n         height: auto;\n         padding-left: 10px;\n         font-size: 11px;\n         height: 21px;\n         line-height: 21px;\n      }\n\n      .swtcomboBox-dropDown-li:hover {\n         background-color: #0000A0;\n         color: #FFF;\n      }\n      \n      .combo-option:hover {\n        background-color: #0000A0;\n        color: #FFF;\n      }\n\n      li.selected {\n        background-color: #0000A0;\n        color: #FFF;\n      }\n\n      .swtcomboBox-selectedItem {\n          background-color: #0000A0;\n          color: #FFF;\n      }\n\n      .swtcomboBox-dropDown-li> a {\n          font-family: verdana,helvetica;\n          font-size: 11px;\n      }\n\n      .input-group-addon {\n          width: 22px;\n          height:22px;\n            border-top: 1px solid #7F9DB9;\n          border-right:1px solid #637A90;\n          border-bottom:1px solid #415160;\n            border-left: 1px solid #637A90;\n          padding: 0px;\n          margin: 0px;\n          font-size: 8px;\n            background-image: -webkit-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -moz-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -ms-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -o-linear-gradient(top, #DBF1FF, #A7C6DE);\n            background-image: linear-gradient(to bottom, #DBF1FF, #A7C6DE);\n      }\n\n      .input-group-addon-disable {\n          width: 22px;\n          height:22px;\n          border-top:1px solid #A5C4DC;\n          border-right:1px solid #96B1C6;\n          border-bottom:1px solid #869EB0;\n          border-left:1px solid #96B1C6;\n          padding: 0px;\n          margin: 0px;\n          font-size: 8px;\n          background-image: -webkit-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -moz-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -ms-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -o-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: linear-gradient(to bottom, #CAEBFE, #B1D0E7);\n      }\n\n      .glyphicon {\n         font-size: 9px !important;\n         position: relative;\n         top: 2px;\n         cursor: default;\n      }\n\n      .swtcombobox-filter-input {\n        height: 22px;\n        font-size: 12px;\n        border-radius: 0px;\n        border-top: 1px solid #4C5E6F;\n        border-right: 1px solid #9FB5CA;\n        border-bottom:1px solid #B2C4D5;\n        border-left:1px solid #9FB5CA;\n        font-family: verdana,helvetica;\n            font-size: 11px;\n      }\n\n      .swtcomboBox-dropDown {\n         position: fixed;\n         z-index: 999;\n         background-color: #FFF;\n         display: none;\n         border: 1px solid #D9D9D9;\n         box-shadow: 0px 4px 5px #999999;\n         width: 260px; /* this width must be updated with jquery*/\n      }\n    \n  "]
                }] }
    ];
    /** @nocollapse */
    SwtComboBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtComboBox.propDecorators = {
        onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
        onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
        mouseWeelEventHandler: [{ type: HostListener, args: ["window:mousewheel", ["$event.target"],] }],
        dataLabel: [{ type: Input, args: ["dataLabel",] }],
        toolTip: [{ type: Input }],
        toolTipPreviousValue: [{ type: Input }],
        prompt: [{ type: Input, args: ["prompt",] }],
        width: [{ type: Input, args: ["width",] }],
        height: [{ type: Input, args: ["height",] }],
        id: [{ type: Input, args: ["id",] }],
        required: [{ type: Input, args: ['required',] }],
        dataProvider: [{ type: Input }],
        open_: [{ type: Output, args: ["open",] }],
        inputClick_: [{ type: Output, args: ["inputClick",] }],
        onTextChange: [{ type: Output, args: ["onTextChange",] }],
        close_: [{ type: Output, args: ["close",] }],
        focus_: [{ type: Output, args: ["focus",] }],
        focusout_: [{ type: Output, args: ["focusout",] }],
        change_: [{ type: Output, args: ["change",] }],
        filter: [{ type: ViewChild, args: ["filter",] }],
        inputGroup: [{ type: ViewChild, args: ["inputGroup",] }],
        listitem: [{ type: ViewChild, args: ["listitem",] }],
        dropDownli: [{ type: ViewChild, args: ["dropDownli",] }],
        dropDownContainer: [{ type: ViewChild, args: ["dropDownContainer",] }],
        enabled: [{ type: Input }],
        editable: [{ type: Input }],
        shiftUp: [{ type: Input }],
        visible: [{ type: Input }]
    };
    return SwtComboBox;
}());
export { SwtComboBox };
if (false) {
    /** @type {?} */
    SwtComboBox.prototype.originalValue;
    /** @type {?} */
    SwtComboBox.prototype.onSpyChange;
    /** @type {?} */
    SwtComboBox.prototype.onSpyNoChange;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._isDropdownOpen;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selecedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._inputClick;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._open;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._close;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._focusout;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._onTextChange;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._tempData;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._originalData;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.filterState;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._alertvisiblity;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._outside;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._insideButton;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._required;
    /** @type {?} */
    SwtComboBox.prototype.dataLabel;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.toolTipPreviousObject;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.interrupted;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._ignored_validation;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.ignoredValidationArr;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._default_ignored;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.backGroundImage;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.onOpenSelectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._shiftUp;
    /** @type {?} */
    SwtComboBox.prototype.showDescriptionInDropDown;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype._toolTipPreviousObject;
    /** @type {?} */
    SwtComboBox.prototype.prompt;
    /** @type {?} */
    SwtComboBox.prototype.width;
    /** @type {?} */
    SwtComboBox.prototype.height;
    /** @type {?} */
    SwtComboBox.prototype.id;
    /** @type {?} */
    SwtComboBox.prototype.firstCall;
    /** @type {?} */
    SwtComboBox.prototype.open_;
    /** @type {?} */
    SwtComboBox.prototype.inputClick_;
    /** @type {?} */
    SwtComboBox.prototype.onTextChange;
    /** @type {?} */
    SwtComboBox.prototype.close_;
    /** @type {?} */
    SwtComboBox.prototype.focus_;
    /** @type {?} */
    SwtComboBox.prototype.focusout_;
    /** @type {?} */
    SwtComboBox.prototype.change_;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.filter;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.inputGroup;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.listitem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.dropDownli;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.dropDownContainer;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtComboBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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