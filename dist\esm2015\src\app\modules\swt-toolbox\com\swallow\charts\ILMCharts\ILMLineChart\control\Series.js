/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class contain all module events
 */
export class Series {
    constructor() {
        this._yField = "";
        this._name = "";
        this._seriesType = '';
        this._visible = true;
        this._appliedStyle = '';
        this._displayName = '';
        this._legendDisplayName = '';
        this._xField = '';
        this._legendTooltip = '';
        this._minField = '';
        this._highlighted = false;
    }
    /**
     * @return {?}
     */
    get highlighted() {
        return this._highlighted;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set highlighted(value) {
        this._highlighted = value;
    }
    /**
     * @return {?}
     */
    get seriesLabel() {
        return this._seriesLabel;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesLabel(value) {
        this._seriesLabel = value;
    }
    /**
     * @return {?}
     */
    get minField() {
        return this._minField;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set minField(value) {
        this._minField = value;
    }
    /**
     * @return {?}
     */
    get legendTooltip() {
        return this._legendTooltip;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set legendTooltip(value) {
        this._legendTooltip = value;
    }
    /**
     * @return {?}
     */
    get xField() {
        return this._xField;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set xField(value) {
        this._xField = value;
    }
    /**
     * @return {?}
     */
    get displayName() {
        return this._displayName;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set displayName(value) {
        this._displayName = value;
    }
    /**
     * @return {?}
     */
    get legendDisplayName() {
        return this._legendDisplayName;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set legendDisplayName(value) {
        this._legendDisplayName = value;
    }
    /**
     * @return {?}
     */
    get appliedStyle() {
        return this._appliedStyle;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set appliedStyle(value) {
        this._appliedStyle = value;
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visible;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        this._visible = value;
    }
    /**
     * @return {?}
     */
    get seriesType() {
        return this._seriesType;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesType(value) {
        this._seriesType = value;
    }
    /**
     * @return {?}
     */
    get yField() {
        return this._yField;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set yField(value) {
        this._yField = value;
    }
    /**
     * @return {?}
     */
    get name() {
        return this._name;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set name(value) {
        this._name = value;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    Series.prototype._yField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._name;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._seriesType;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._appliedStyle;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._displayName;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._legendDisplayName;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._xField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._legendTooltip;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._minField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._seriesLabel;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._highlighted;
}
//# sourceMappingURL=data:application/json;base64,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