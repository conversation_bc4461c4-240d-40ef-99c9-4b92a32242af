import { OnIni<PERSON>, ElementRef, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
/**
 *
 * Tab component.
 * **/
export declare class Tab extends Container implements OnInit, OnDestroy {
    protected elem: ElementRef;
    protected commonService: CommonService;
    parent: any;
    protected _active: boolean;
    protected _visible: boolean;
    protected _label: string;
    protected _display: boolean;
    protected _closable: boolean;
    protected _order: number;
    label: any;
    order: any;
    visible: any;
    active: any;
    display: any;
    closable: any;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    isVisible(e: any): boolean;
    /**
     * Sets a style to the tab's header.
     * @param prop
     * @param value
     */
    setTabHeaderStyle(prop: any, value: any): void;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
    /**
     * setUndockPolicy
     *
     * @param index:int
     *
     * @param value:String
     *
     * Method to set undock policy for tab
     */
    setUndockPolicy(index: number, value: string): void;
}
/**
 *
 * Tab component.
 * **/
export declare class TabPushStategy extends Tab implements OnInit, OnDestroy {
    protected elem: ElementRef;
    protected commonService: CommonService;
    protected cd: ChangeDetectorRef;
    active: any;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService, cd: ChangeDetectorRef);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
}
export declare class SwtTabNavigator extends Container implements OnInit, AfterViewInit, OnDestroy {
    private elem;
    private commonService;
    private cdr;
    private selectedTabId;
    tabChildrenArray: any[];
    private _selectedIndex;
    private _selectedLabel;
    private _selectedTab;
    private _borderBottom;
    private _borderTop;
    private _onChange;
    MOUSE_DOWN: Function;
    aboutActive: boolean;
    clientWidth: number;
    scrollWidth: number;
    timer: any;
    scrollValue: number;
    maxScroll: number;
    navBarContainer: any;
    navTabs: any;
    navTabsLi: any;
    containerNavigator: ElementRef;
    swtTabNavigator: ElementRef;
    private onChange_;
    _showDropDown: boolean;
    _applyOrder: boolean;
    sortTabChildrenArray(): any[];
    calculateHeigt(): number;
    tabChildren: any;
    selectedTab: Tab;
    selectedChild: Tab;
    selectedLabel: string;
    onChange: Function;
    selectedIndex: number;
    borderBottom: any;
    showDropDown: any;
    applyOrder: any;
    borderTop: any;
    /**
     * constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService, cdr: ChangeDetectorRef);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     * ngAfterViewInit
     */
    ngAfterViewInit(): void;
    /**
     * setSelectedTab : handle active/inactive tab
     * @param event
     * @param label
     */
    setSelectedTab(tab: any): void;
    getSelectedTab(): Tab;
    /**
     * addChild : adds a child tab dynamically
     * @param type
     */
    addChild(type: any): Tab;
    /**
     * addChild : adds a child tab dynamically
     * @param type
     */
    addChildPushStategy(type: any): TabPushStategy;
    /**
     * removeChild : removes a tab dynamically
     * @param componentClass
     */
    removeChild(componentClass: any): void;
    /**
     * getChildAt : returns the id of a child Tab in a specific index.
     * @param index
     */
    getChildAt(index: number): any;
    /**
     * getTabChildren : returns tabNavigator's children
     */
    getTabChildren(): any[];
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
    /**
     * setUndockPolicyForTab
     *
     * @param index: number
     *
     * @param value: String
     *
     * Method to set undok policy for tab
     */
    setUndockPolicyForTab(index: number, value: string): void;
    onMousedown(tab: any, event: any): void;
    scrollTabs(side: string): void;
    dispose(): void;
    removeContentTab(tab: Tab): void;
    scrollToTabFromCombo(tab: any): void;
    checkValue(): void;
}
