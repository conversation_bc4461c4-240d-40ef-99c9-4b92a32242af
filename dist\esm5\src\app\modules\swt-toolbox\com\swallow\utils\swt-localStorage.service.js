/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
var SwtLocalStorage = /** @class */ (function () {
    function SwtLocalStorage() {
    }
    /**
     * addRecord is used to set a new record to
     * localStorage.
     * @param key
     * @param value
     */
    /**
     * addRecord is used to set a new record to
     * localStorage.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    SwtLocalStorage.addRecord = /**
     * addRecord is used to set a new record to
     * localStorage.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    function (key, value) {
        if (localStorage.getItem(key) === null) {
            try {
                /** @type {?} */
                var record = JSON.parse(value);
                localStorage.setItem(key, JSON.stringify(record));
            }
            catch (e) {
                localStorage.setItem(key, value);
            }
        }
        else {
            console.error('[', this.name, '] -> addRecord(key, value)', 'The', key, 'key already exists.');
        }
    };
    /**
     * getRecordCount is used to get the number
     * of records saved in the localStorage.
     */
    /**
     * getRecordCount is used to get the number
     * of records saved in the localStorage.
     * @return {?}
     */
    SwtLocalStorage.getRecordsCount = /**
     * getRecordCount is used to get the number
     * of records saved in the localStorage.
     * @return {?}
     */
    function () {
        return localStorage.length;
    };
    /**
     * clearAll is used to clear all
     * records.
     */
    /**
     * clearAll is used to clear all
     * records.
     * @return {?}
     */
    SwtLocalStorage.clearAll = /**
     * clearAll is used to clear all
     * records.
     * @return {?}
     */
    function () {
        localStorage.clear();
    };
    /**
     * this method is used to remove record.
     * @param key
     */
    /**
     * this method is used to remove record.
     * @param {?} key
     * @return {?}
     */
    SwtLocalStorage.removeRecord = /**
     * this method is used to remove record.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        localStorage.removeItem(key);
    };
    /**
     * this method is used to get record.
     * @param key
     */
    /**
     * this method is used to get record.
     * @param {?} key
     * @return {?}
     */
    SwtLocalStorage.getRecord = /**
     * this method is used to get record.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        return localStorage.getItem(key);
    };
    /**
     * this method is used to get the key in the
     * index passed as parameter.
     * @param index
     */
    /**
     * this method is used to get the key in the
     * index passed as parameter.
     * @param {?} index
     * @return {?}
     */
    SwtLocalStorage.getRecordKey = /**
     * this method is used to get the key in the
     * index passed as parameter.
     * @param {?} index
     * @return {?}
     */
    function (index) {
        return localStorage.key(index);
    };
    /**
     * this method is used to get all records.
     * return object.
     */
    /**
     * this method is used to get all records.
     * return object.
     * @return {?}
     */
    SwtLocalStorage.getRecords = /**
     * this method is used to get all records.
     * return object.
     * @return {?}
     */
    function () {
        /** @type {?} */
        var records = {};
        for (var index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            /** @type {?} */
            var key = SwtLocalStorage.getRecordKey(index);
            /** @type {?} */
            var value = SwtLocalStorage.getRecord(key);
            try {
                records[key] = JSON.parse(value);
            }
            catch (e) {
                records[key] = value;
            }
        }
        return records;
    };
    /**
     * this method is used to return the list of keys.
     */
    /**
     * this method is used to return the list of keys.
     * @return {?}
     */
    SwtLocalStorage.getRecordKeys = /**
     * this method is used to return the list of keys.
     * @return {?}
     */
    function () {
        /** @type {?} */
        var keys = [];
        for (var index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            keys.push(SwtLocalStorage.getRecordKey(index));
        }
        return keys;
    };
    /**
     * this method is used to get the list of values
     */
    /**
     * this method is used to get the list of values
     * @return {?}
     */
    SwtLocalStorage.getRecordValues = /**
     * this method is used to get the list of values
     * @return {?}
     */
    function () {
        /** @type {?} */
        var values = new Array();
        for (var index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            try {
                /** @type {?} */
                var value = JSON.parse(SwtLocalStorage.getRecord(SwtLocalStorage.getRecordKey(index)));
                values.push(value);
            }
            catch (e) {
                values.push(SwtLocalStorage.getRecord(SwtLocalStorage.getRecordKey(index)));
            }
        }
        return values;
    };
    return SwtLocalStorage;
}());
export { SwtLocalStorage };
//# sourceMappingURL=data:application/json;base64,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