import { AfterViewChecked, AfterViewInit, ChangeDetectorRef, Do<PERSON>he<PERSON>, ElementRef, EventEmitter, OnInit } from '@angular/core';
import { CodemirrorComponent } from '@ctrl/ngx-codemirror';
import { EditorFromTextArea } from 'codemirror';
/**
 * <AUTHOR>
 *
 */
export declare class SwtPrettyPrintTextArea implements OnInit, AfterViewInit, AfterViewChecked, DoCheck {
    private elem;
    private cd;
    originalValue: any;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    private _height;
    private _width;
    private _paddingTop;
    private _paddingRight;
    private _paddingBottom;
    private _paddingLeft;
    private _marginTop;
    private _marginRight;
    private _marginBottom;
    private _marginLeft;
    private _editable;
    private _scroll;
    private _focusIn;
    private _focusOut;
    private _change;
    private _text;
    private _initialtxt;
    firstCall: boolean;
    CUSTOM: number;
    contentValue: string;
    backgroundColor: string;
    tabIndex: number;
    id: string;
    toolTip: string;
    enabled: string;
    private change_;
    private focusOut_;
    private focusIn_;
    swtPrettyPrintTextArea: ElementRef;
    ngxCodeMirror: CodemirrorComponent;
    codemirror: EditorFromTextArea;
    listCodeWordsInBD: string[];
    options: any;
    constructor(elem: ElementRef, cd: ChangeDetectorRef);
    ngDoCheck(): void;
    ngOnInit(): void;
    handleChange(event: any): void;
    focusChange(event: any): void;
    defineMode(language: string, keywords: any): void;
    ngAfterViewInit(): void;
    ngAfterViewChecked(): void;
    registerSyntax(syntax: number, keywords?: string): void;
    setFocus(): void;
    height: string;
    width: string;
    paddingTop: string;
    paddingRight: string;
    marginLeft: string;
    marginBottom: string;
    marginRight: string;
    marginTop: string;
    paddingLeft: string;
    paddingBottom: string;
    scroll: Function;
    focusOut: Function;
    focusIn: Function;
    change: Function;
    text: any;
    editable: any;
    spyChanges(event: any): void;
}
