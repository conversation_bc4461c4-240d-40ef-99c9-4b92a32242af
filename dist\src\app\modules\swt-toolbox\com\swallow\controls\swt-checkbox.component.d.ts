import { OnInit, ElementRef, AfterViewInit } from "@angular/core";
import { CommonService } from "../utils/common.service";
import { Container } from "../containers/swt-container.component";
export declare class SwtCheckBox extends Container implements OnInit, AfterViewInit {
    private elem;
    private commonService;
    private _selected;
    private _fontWeight;
    private _tabIndex;
    private _value;
    private _label;
    ngAfterViewInit(): void;
    label: string;
    value: string;
    tabIndex: any;
    selected: any;
    fontWeight: string;
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    constructor(elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the change event.
     * */
    ChangeEventHandler(event: any): void;
    /**
     * Click
     * @param event
     */
    emitClickEvent(event: any): void;
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the keyDown event.
     * */
    KeyDownEventHandler(event: any): void;
    /**
     * resetOriginalValue
     */
    resetOriginalValue(): void;
}
