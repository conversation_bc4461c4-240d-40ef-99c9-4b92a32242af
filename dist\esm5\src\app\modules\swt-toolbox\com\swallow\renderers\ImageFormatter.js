/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CustomCell, isClickable, isNegative, isBlink } from "./cellItemRenderUtilities";
/** @type {?} */
export var ImageFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var datagrid = columnDef.params.grid;
    /** @type {?} */
    var backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent');
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    var type = columnDef['columnType'] ? String(columnDef['columnType']) : null;
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var blink_me = isBlink(value);
    /** @type {?} */
    var negative = isNegative(dataContext, field);
    /** @type {?} */
    var isLink = isClickable(dataContext, field);
    /** @type {?} */
    var style = CustomCell(dataContext, field);
    /** @type {?} */
    var imageSource = columnDef.properties ? columnDef.properties.imageSource : null;
    //-there is no custom style for the cell
    if (style == "") {
        if (backgroundColor == undefined) {
            backgroundColor = 'transparent';
        }
        style += ' background-color:' + backgroundColor + '; ';
        if (negative) {
            style += 'color: #ff0000; ';
        }
        style += (columnDef.properties ? columnDef.properties.style : '');
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null || field == "expand") {
            value = "";
        }
        value = value.toString().replace('<', '&lt;');
        value = value.toString().replace('>', '&gt;');
        return "<img src=\"" + imageSource + "\" class=\"" + (blink_me == true ? 'blink_me' : '') + " " + (enabledFlag == false || !datagrid.enabled ? 'imageIsDisabled' : '') + "\" style=\"" + style + "\" >\n                </img>";
    }
    return "";
});
//# sourceMappingURL=data:application/json;base64,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