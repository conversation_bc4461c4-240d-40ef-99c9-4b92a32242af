/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Types } from "./types";
export class StringItemRender extends UIComponent {
    /**
     * @param {?} stringelement
     * @param {?} common
     */
    constructor(stringelement, common) {
        super(stringelement, common);
        this.stringelement = stringelement;
        this.common = common;
        this.text = "";
        this.color = "";
        this.type = Types.STR;
        this.id = 0;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
}
StringItemRender.decorators = [
    { type: Component, args: [{
                selector: 'StringItemRender',
                template: `
        <span class="string-item-render" [id]="id">
            {{ text }}
        </span>
    `,
                styles: [`
        :host {
            display: block;
            width: 100%;
        }

        .string-item-render {
            display: block;
            width: 100%;
            text-align: justify;
            padding: 0 5px;
            margin: 0px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    `]
            }] }
];
/** @nocollapse */
StringItemRender.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    StringItemRender.prototype.text;
    /** @type {?} */
    StringItemRender.prototype.color;
    /** @type {?} */
    StringItemRender.prototype.type;
    /** @type {?} */
    StringItemRender.prototype.id;
    /**
     * @type {?}
     * @private
     */
    StringItemRender.prototype.stringelement;
    /**
     * @type {?}
     * @private
     */
    StringItemRender.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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