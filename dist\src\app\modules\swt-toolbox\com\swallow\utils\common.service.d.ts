import { ApplicationRef, ComponentFactoryResolver, Injector, NgModuleFactoryLoader } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import 'jquery-ui-dist/jquery-ui';
import { WindowManager } from '../managers/window-manager.service';
import { Subscription } from "rxjs";
/**
 * This service is a hub of service.
 * it used to resolve the problem of dependency injection.
 * <AUTHOR>
 */
export declare class CommonService {
    httpclient: HttpClient;
    Router: Router;
    componentFactoryResolver: ComponentFactoryResolver;
    windowManager: WindowManager;
    resolver: ComponentFactoryResolver;
    manifests: any[];
    loader: NgModuleFactoryLoader;
    injector: Injector;
    applicationRef: ApplicationRef;
    static WindowManager: WindowManager;
    static instance: CommonService;
    constructor(httpclient: HttpClient, Router: Router, componentFactoryResolver: ComponentFactoryResolver, windowManager: WindowManager, resolver: ComponentFactoryResolver, manifests: any[], loader: NgModuleFactoryLoader, injector: Injector, applicationRef: ApplicationRef);
    static jsonpath(obj: any, path: string): any;
    static jsonpathes(obj: any, paths: string[]): any;
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param classObject
     */
    getQualifiedClassName(classObject: any): string;
}
/**
 * Unsubscribe all Observables Subscriptions
 * It will return an empty array if it all went well
 * @param subscriptions
 */
export declare function unsubscribeAllObservables(subscriptions: Subscription[]): Subscription[];
