/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
var SwtLoadingImage = /** @class */ (function () {
    function SwtLoadingImage(element) {
        this.element = element;
        this.visibility = true;
    }
    Object.defineProperty(SwtLoadingImage.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this.visibility;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this.visibility = true;
                    $(this.element.nativeElement).css("visibility", "visible");
                }
                else {
                    this.visibility = false;
                    $(this.element.nativeElement).css("visibility", "hidden");
                }
            }
            else {
                this.visibility = value;
                if (this.visibility) {
                    $(this.element.nativeElement).css("visibility", "visible");
                }
                else {
                    $(this.element.nativeElement).css("visibility", "hidden");
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtLoadingImage.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @param {?} visibility
     * @return {?}
     */
    SwtLoadingImage.prototype.setVisible = /**
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        this.visibility = visibility;
        if (visibility) {
            $(this.element.nativeElement).css("visibility", "visible");
        }
        else {
            $(this.element.nativeElement).css("visibility", "hidden");
        }
    };
    SwtLoadingImage.decorators = [
        { type: Component, args: [{
                    selector: 'SwtLoadingImage',
                    template: "\n      \n          <img class=\"swt-spinner\" src=\"assets/images/Rolling.gif\">\n     \n  ",
                    styles: ["\n      .swt-spinner {\n              height:18px;\n              margin: auto 0px;\n              vertical-align: baseline;\n      }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtLoadingImage.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    SwtLoadingImage.propDecorators = {
        visible: [{ type: Input, args: ['visible',] }]
    };
    return SwtLoadingImage;
}());
export { SwtLoadingImage };
if (false) {
    /** @type {?} */
    SwtLoadingImage.prototype.visibility;
    /**
     * @type {?}
     * @private
     */
    SwtLoadingImage.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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