import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { ContextMenu } from "./context-menu.component";
import { CommonService } from "../utils/common.service";
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/autoresize';
import 'tinymce/themes/silver';
export declare class SwtTextArea extends Container implements AfterViewInit, OnD<PERSON>roy, OnInit {
    private elem;
    private commonService;
    [x: string]: any;
    private _width;
    private _height;
    private _text;
    private _htmlText;
    private _editable;
    private _enabled;
    private __fontWeight;
    private _fontSize;
    private _textColor;
    private __color;
    private _textAlign;
    private _doubleClickEnabled;
    private __backgroundColor;
    private __verticalScrollPolicy;
    private __horizontalScrollPolicy;
    private zone;
    editor: any;
    elementId: String;
    _verticalScrollPosition: number;
    _selectionBeginIndex: number;
    private _contextMenu;
    private contextmenuItems;
    private toolTipObject;
    private id_contextMenu;
    private subscriptions;
    private _required;
    contextMenu: ContextMenu;
    tabIndex: any;
    selectionBeginIndex: any;
    verticalScrollPosition: any;
    width: any;
    height: any;
    required: any;
    doubleClickEnabled: any;
    text: any;
    htmlToText(text: any): any;
    htmlText: any;
    verticalScrollPolicy: string;
    horizontalScrollPolicy: string;
    editable: any;
    enabled: any;
    fontWeight: string;
    fontSize: any;
    textColor: string;
    color: string;
    textAlign: string;
    backgroundColor: string;
    constructor(elem: ElementRef, commonService: CommonService, zone: NgZone);
    ngAfterViewInit(): void;
    private isHTML;
    onRightClickTextArea(event: any): void;
    private isVisible;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
    ngOnInit(): void;
}
