/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class contain all module events
 */
export class SeriesStyle {
    /**
     * @param {?} id
     * @param {?} name
     * @param {?} type
     * @param {?=} imageName
     */
    constructor(id, name, type, imageName = null) {
        this.id = "";
        this.name = "";
        this._form = "segment"; // Allowed values are segment,step,reverseStep,vertical,horizontal,curve
        // Allowed values are segment,step,reverseStep,vertical,horizontal,curve
        this.isBitmap = false;
        this.dashPattern = [];
        this.type = type;
        this.id = id;
        this.name = name;
        this.imageName = imageName;
    }
    /**
     * @return {?}
     */
    get form() {
        return this._form;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set form(value) {
        /** @type {?} */
        var allowedvalues = ['segment', 'step', 'reverseStep', 'vertical', 'horizontal', 'curve'];
        if (allowedvalues.indexOf(value) == -1)
            throw new Error('Could not set form, allowed values are:segment,step,reverseStep,vertical,horizontal and curve');
        this._form = value;
    }
}
if (false) {
    /** @type {?} */
    SeriesStyle.prototype.id;
    /** @type {?} */
    SeriesStyle.prototype.name;
    /**
     * @type {?}
     * @private
     */
    SeriesStyle.prototype._form;
    /** @type {?} */
    SeriesStyle.prototype.isBitmap;
    /** @type {?} */
    SeriesStyle.prototype.dashPattern;
    /** @type {?} */
    SeriesStyle.prototype.type;
    /** @type {?} */
    SeriesStyle.prototype.color;
    /** @type {?} */
    SeriesStyle.prototype.imageName;
}
//# sourceMappingURL=data:application/json;base64,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