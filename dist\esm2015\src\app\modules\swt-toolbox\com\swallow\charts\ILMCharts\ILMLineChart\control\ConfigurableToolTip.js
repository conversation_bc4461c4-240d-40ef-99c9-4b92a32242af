/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { VBox } from '../../../../controls/swt-vbox.component';
import { SwtLabel } from '../../../../controls/swt-label.component';
import { ExternalInterface } from '../../../../utils/external-interface.service';
import { SwtAlert } from '../../../../utils/swt-alert.service';
import { Alert } from '../../../../utils/alert.component';
export class ConfigurableToolTip extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.recalculateEnable = false;
        this.clickable = false;
        this.swtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.dataForLabel.text = ExternalInterface.call('getBundle', 'text', 'label-dataFor', 'Data For');
        this.labelnumberAccounts.text = ExternalInterface.call('getBundle', 'text', 'label-numberAccounts', 'Number of accounts');
        this.labelnewDataExistFor.text = ExternalInterface.call('getBundle', 'text', 'label-newDataExistFor', 'New data exist for');
        this.labelincomplete.text = ExternalInterface.call('getBundle', 'text', 'label-incomplete', 'Incomplete');
        this.labelinconsistent.text = ExternalInterface.call('getBundle', 'text', 'label-inconsistent', 'Inconsistent');
        this.labelLastUpdate.text = ExternalInterface.call('getBundle', 'text', 'label-LastUpdate', 'Last Update');
        // this.customTooltip.focusOut = this.boxRollOutEventListner;
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        this.createCustomTip();
        jQuery(this.customTooltip.domElement).mouseleave((/**
         * @return {?}
         */
        () => {
            jQuery(this.customTooltip.domElement).off('mouseleave');
            this.processBox.removeTooltip();
        }));
    }
    /**
     * @private
     * @return {?}
     */
    boxRollOutEventListner() {
        console.log("ConfigurableToolTip -> boxRollOutEventListner -> boxRollOutEventListner");
    }
    /**
     * @private
     * @return {?}
     */
    createCustomTip() {
        try {
            this.clickable = false;
            if (this.dataArray['ENTITY']) {
                this.dataForLabelValue.text = this.dataArray['ENTITY'] + ' / ' + this.dataArray['CURRENCY'];
                this.labelnumberAccountsValue.text = this.dataArray['ACCOUNTS_NUMBER'];
                this.labelnewDataExistForValue.text = this.dataArray['ACCOUNTS_NEW_DATA'];
                if (parseInt(this.dataArray['ACCOUNTS_NEW_DATA'])) {
                    this.labelnewDataExistForValue.color = '#FF9900';
                }
                this.labelincompleteValue.text = this.dataArray['ACCOUNTS_INCOMPLETE'];
                if (parseInt(this.dataArray['ACCOUNTS_INCOMPLETE'])) {
                    this.labelincompleteValue.color = 'red';
                }
                this.labelinconsistentValue.text = this.dataArray['ACCOUNTS_INCONSISTENT'];
                if (parseInt(this.dataArray['ACCOUNTS_INCONSISTENT'])) {
                    this.labelinconsistentValue.color = 'red';
                }
                this.labelLastUpdateValue.text = this.dataArray['END_DATE'];
                if (this.recalculateEnable && this.dataArray['RECALCULATE'] === 'Y') {
                    if (this.dataArray['IS_ALREADY_RUNNING'] === 'Y') {
                        this.requestRecalculation.text = ExternalInterface.call('getBundle', 'text', 'label-recalculationInProgress', 'Calculation in progress');
                        // this.requestRecalculation.enabled = false;
                        this.requestRecalculation.color = "grey";
                    }
                    else {
                        this.clickable = true;
                        this.requestRecalculation.text = ExternalInterface.call('getBundle', 'text', 'label-requestRecalculation', 'A data recalculation is needed');
                        this.requestRecalculation.color = "red";
                        // this.requestRecalculation.enabled = false;
                    }
                }
            }
        }
        catch (error) {
        }
    }
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    recalculateDataAlert(event) {
        if (this.clickable) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-recalculateConfirm', 'Are you sure? This may take some time?'), //text
            ExternalInterface.call('getBundle', 'text', 'label-recalculateConfirmAlertTitle', 'Are you sure? This may take some time?'), Alert.OK | Alert.CANCEL, this, //parent
            this.alertListener.bind(this), //close handler
            null, Alert.CANCEL); //icon and default button
        }
    }
    /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    alertListener(eventObj) {
        // Checks for Alert OK
        if (eventObj.detail == Alert.OK) {
            // Recalculate data if "OK" is clicked
            this.parentDocument.recalculateDataFunction();
        }
    }
}
ConfigurableToolTip.decorators = [
    { type: Component, args: [{
                selector: 'ConfigurableToolTip',
                template: `
        <VBox  paddingLeft="3"  paddingRight="3" paddingTop="3" #customTooltip width="100%" height="100%" verticalGap="2"> 
            <HBox> <SwtLabel  #dataForLabel text='Date For'    fontWeight="normal">  </SwtLabel> <SwtLabel  #dataForLabelValue    fontWeight="normal">  </SwtLabel> </HBox>
            <HBox> 
            <VBox  width="55%" style="border-right: 1px solid black">
            <HBox ><SwtLabel  #labelnumberAccounts text='Number of accounts'    fontWeight="normal" width="70%">  </SwtLabel> <SwtLabel  #labelnumberAccountsValue    fontWeight="normal">  </SwtLabel></HBox>
            <HBox ><SwtLabel  #labelnewDataExistFor text='New data exist for'    fontWeight="normal" width="70%">  </SwtLabel> <SwtLabel #labelnewDataExistForValue    fontWeight="normal">  </SwtLabel></HBox>
            </VBox>
            
            <VBox  width="45%">
            <HBox> <SwtLabel  #labelincomplete text='Incomplete'  width="50%"  fontWeight="normal">  </SwtLabel> <SwtLabel  #labelincompleteValue    fontWeight="normal">  </SwtLabel></HBox>
            <HBox> <SwtLabel  #labelinconsistent text='Inconsistent'  width="50%"   fontWeight="normal">  </SwtLabel> <SwtLabel #labelinconsistentValue    fontWeight="normal">  </SwtLabel></HBox>
            </VBox>
            </HBox>
            <HBox> <SwtLabel  #labelLastUpdate text='Last Update'    fontWeight="normal">  </SwtLabel> <SwtLabel  #labelLastUpdateValue    fontWeight="normal">  </SwtLabel></HBox>
            <HBox  horizontalAlign="center">
                <SwtLabel  id="requestRecalculation" #requestRecalculation 
                color="black" fontWeight="bold" label="">
            </SwtLabel>
             </HBox>
        </VBox>
  `,
                styles: [`
      `]
            }] }
];
/** @nocollapse */
ConfigurableToolTip.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
ConfigurableToolTip.propDecorators = {
    customTooltip: [{ type: ViewChild, args: ['customTooltip',] }],
    dataForLabel: [{ type: ViewChild, args: ['dataForLabel',] }],
    dataForLabelValue: [{ type: ViewChild, args: ['dataForLabelValue',] }],
    labelnumberAccounts: [{ type: ViewChild, args: ['labelnumberAccounts',] }],
    labelnumberAccountsValue: [{ type: ViewChild, args: ['labelnumberAccountsValue',] }],
    labelnewDataExistFor: [{ type: ViewChild, args: ['labelnewDataExistFor',] }],
    labelnewDataExistForValue: [{ type: ViewChild, args: ['labelnewDataExistForValue',] }],
    labelincomplete: [{ type: ViewChild, args: ['labelincomplete',] }],
    labelincompleteValue: [{ type: ViewChild, args: ['labelincompleteValue',] }],
    labelinconsistent: [{ type: ViewChild, args: ['labelinconsistent',] }],
    labelinconsistentValue: [{ type: ViewChild, args: ['labelinconsistentValue',] }],
    labelLastUpdate: [{ type: ViewChild, args: ['labelLastUpdate',] }],
    labelLastUpdateValue: [{ type: ViewChild, args: ['labelLastUpdateValue',] }],
    requestRecalculation: [{ type: ViewChild, args: ['requestRecalculation',] }]
};
if (false) {
    /** @type {?} */
    ConfigurableToolTip.prototype.customTooltip;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataForLabel;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataForLabelValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnumberAccounts;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnumberAccountsValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnewDataExistFor;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnewDataExistForValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelincomplete;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelincompleteValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelinconsistent;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelinconsistentValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelLastUpdate;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelLastUpdateValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.requestRecalculation;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataArray;
    /** @type {?} */
    ConfigurableToolTip.prototype.parentDocument;
    /** @type {?} */
    ConfigurableToolTip.prototype.processBox;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.swtAlert;
    /** @type {?} */
    ConfigurableToolTip.prototype.recalculateEnable;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.clickable;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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