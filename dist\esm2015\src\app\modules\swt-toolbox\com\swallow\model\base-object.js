/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
export class BaseObject {
    constructor() { }
    /**
     * This method is used to bind attribute to the current object.
     * @param {?} name
     * @param {?} value
     * @return {?}
     */
    bindAttribute(name, value) {
        if (name) {
            this[name] = value;
        }
    }
    /**
     * This method is used to clone the current object.
     * @param {?} source
     * @return {?}
     */
    clone(source) {
        if (Object.prototype.toString.call(source) === '[object Array]') {
            /** @type {?} */
            const clone = [];
            for (let i = 0; i < source.length; i++) {
                clone[i] = this.clone(source[i]);
            }
            return clone;
        }
        else if (typeof (source) === "object") {
            /** @type {?} */
            const clone = {};
            for (const prop in source) {
                if (source.hasOwnProperty(prop)) {
                    clone[prop] = this.clone(source[prop]);
                }
            }
            return clone;
        }
        else {
            return source;
        }
    }
    /**
     * This method is used to rename an attribute of the current object
     * @param {?=} obj
     * @param {?=} oldAttr
     * @param {?=} newAttr
     * @return {?}
     */
    renameAttr(obj = null, oldAttr, newAttr) {
        try {
            if (obj) {
                // Do nothing if the names are the same
                if (oldAttr === newAttr) {
                    return obj;
                }
                // Check for the old property name to avoid
                // a ReferenceError in strict mode.
                if (obj.hasOwnProperty(oldAttr)) {
                    obj[newAttr] = obj[oldAttr];
                    delete obj[oldAttr];
                }
                return obj;
            }
            else {
                // Do nothing if the names are the same
                if (oldAttr === newAttr) {
                    return this;
                }
                // Check for the old property name to avoid
                // a ReferenceError in strict mode.
                if (this.hasOwnProperty(oldAttr)) {
                    this[newAttr] = this[oldAttr];
                    delete this[oldAttr];
                }
                return this;
            }
        }
        catch (e) {
            console.error("renameAttr: ", e);
        }
    }
    /**
     * This method extract numbers from given string.
     * @param {?} str
     * @return {?}
     */
    getNumberFrom(str) {
        if (typeof (str) !== "string") {
            str = str + "";
        }
        return Number(str.replace(/\D/g, ''));
    }
}
//# sourceMappingURL=data:application/json;base64,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