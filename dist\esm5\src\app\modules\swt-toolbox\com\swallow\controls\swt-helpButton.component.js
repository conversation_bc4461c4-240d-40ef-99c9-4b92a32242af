/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, ElementRef } from '@angular/core';
import { SwtUtil } from '../utils/swt-util.service';
import { ExternalInterface } from '../utils/external-interface.service';
import { CommonUtil } from '../utils/common-util.service';
import { SwtButton } from "./swt-button.component";
import { CommonService } from "../utils/common.service";
var SwtHelpButton = /** @class */ (function (_super) {
    tslib_1.__extends(SwtHelpButton, _super);
    /**
     * constructor
     * @param eleme
     * @param commonService_
     */
    function SwtHelpButton(eleme, commonService_) {
        var _this = _super.call(this, eleme, commonService_) || this;
        _this.eleme = eleme;
        _this.commonService_ = commonService_;
        $($(_this.eleme.nativeElement)[0]).attr('selector', 'SwtHelpButton');
        return _this;
    }
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    SwtHelpButton.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        if (!this.toolTip) {
            this.toolTip = SwtUtil.getPredictMessage('button.help');
        }
    };
    /**
     *  @param event: Event
     * This is a event handler,used to open the help  window
     */
    /**
     * @return {?}
     */
    SwtHelpButton.prototype.doHelp = /**
     * @return {?}
     */
    function () {
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            //Get the user language locale path
            /** @type {?} */
            var urlLanguage = SwtUtil.getBaseURL() + "logon!getUserLanguage.do";
            errorLocation = 20;
            // get user language
            /** @type {?} */
            var userLang = ExternalInterface.call('getUserLanguageId', urlLanguage);
            errorLocation = 30;
            // If Module Id is null
            if (this.moduleId == null) {
                // Get module ID
                //moduleId = String(parentApplication.getCurrentModuleId()).toLowerCase();
                errorLocation = 40;
                this.moduleId = CommonUtil.getCurrentModuleId().toLowerCase();
            }
            //set http url
            errorLocation = 50;
            ExternalInterface.call("loadHelpPage", "about!helpPage.do?help_id=" + this.helpFile + "&module_id=" + this.moduleId + "&language_id=" + userLang.split("/")[1]);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, "SwtHelpButton", "doHelp", errorLocation);
        }
    };
    SwtHelpButton.decorators = [
        { type: Component, args: [{
                    selector: 'SwtHelpButton',
                    template: " \n         <div   \n                #swtbutton  \n                (click)    =\"doHelp()\" \n                class=\"helpIcon inline-field\">\n                <span class=\"truncate buttonLabel\" ></span>\n         </div>\n    ",
                    styles: ["\n    \n             :host{\n                 outline:none;\n             }\n            .helpIcon    {\n                height: 16px; \n                width: 16px;                     \n                border: 0;\n                overflow: hidden;\n                color: transparent;\n                background:transparent url('assets/images/swtHelp.png') no-repeat;                  \n                border-radius: 15px;   \n                cursor: pointer;\n            }            \n             .helpIcon:hover{\n                 background:transparent url('assets/images/swtHelpOver.png') no-repeat;\n              }\n             .helpIcon:focus {\n                outline: 0px auto -webkit-focus-ring-color;\n                background:transparent url('assets/images/swtHelpDown.png') no-repeat;\n             }\n            .helpIconDisabled {\n                height: 16px; \n                width: 16px;                     \n                border: 0;\n                overflow: hidden;\n                color: transparent;\n                background:transparent url('assets/images/swtHelp.png') no-repeat;                  \n                border-radius: 15px;   \n                opacity: 0.6;\n                pointer-events: none;    \n                cursor: default;      \n             }      \n            /*========================================= Styling Tootltip  ======================================*/\n            \n             :host >>> .tooltip-inner {\n                 background-color: rgba(250, 250, 230, 1);\n                 font-size:9px;\n                 color: #0b333c;\n                 border: 1px solid #0b333c;\n                 box-shadow: 1px 2px 5px #888888;\n             }\n         "]
                }] }
    ];
    /** @nocollapse */
    SwtHelpButton.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtHelpButton.propDecorators = {
        toolTip: [{ type: Input, args: ['toolTip',] }],
        helpFile: [{ type: Input, args: ['helpFile',] }]
    };
    return SwtHelpButton;
}(SwtButton));
export { SwtHelpButton };
if (false) {
    /** @type {?} */
    SwtHelpButton.prototype.moduleId;
    /** @type {?} */
    SwtHelpButton.prototype.toolTip;
    /** @type {?} */
    SwtHelpButton.prototype.helpFile;
    /**
     * @type {?}
     * @private
     */
    SwtHelpButton.prototype.eleme;
    /**
     * @type {?}
     * @private
     */
    SwtHelpButton.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,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