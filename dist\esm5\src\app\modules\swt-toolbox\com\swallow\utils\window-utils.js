/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
var WindowUtils = /** @class */ (function () {
    function WindowUtils() {
    }
    WindowUtils.winOrder = 100;
    WindowUtils.winid = 'win_0';
    return WindowUtils;
}());
export { WindowUtils };
if (false) {
    /** @type {?} */
    WindowUtils.winOrder;
    /** @type {?} */
    WindowUtils.winid;
}
var Position = /** @class */ (function () {
    function Position(_x, _y) {
        this._x = _x;
        this._y = _y;
    }
    Object.defineProperty(Position.prototype, "x", {
        get: /**
         * @return {?}
         */
        function () {
            return this._x;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._x = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Position.prototype, "y", {
        get: /**
         * @return {?}
         */
        function () {
            return this._y;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._y = value;
        },
        enumerable: true,
        configurable: true
    });
    Position.CENTER = "center";
    Position.TOPLEFT = "topleft";
    Position.TOPRIGHT = "topright";
    Position.BOTTOMLEFT = "bottomleft";
    Position.BOTTOMRIGHT = "bottomright";
    Position.TOPCENTER = "topcenter";
    Position.BOTTOMCENTER = "bottomcenter";
    Position.LEFTCENTER = "leftcenter";
    Position.RIGHTCENTER = "rightcenter";
    return Position;
}());
export { Position };
if (false) {
    /** @type {?} */
    Position.CENTER;
    /** @type {?} */
    Position.TOPLEFT;
    /** @type {?} */
    Position.TOPRIGHT;
    /** @type {?} */
    Position.BOTTOMLEFT;
    /** @type {?} */
    Position.BOTTOMRIGHT;
    /** @type {?} */
    Position.TOPCENTER;
    /** @type {?} */
    Position.BOTTOMCENTER;
    /** @type {?} */
    Position.LEFTCENTER;
    /** @type {?} */
    Position.RIGHTCENTER;
    /**
     * @type {?}
     * @private
     */
    Position.prototype._x;
    /**
     * @type {?}
     * @private
     */
    Position.prototype._y;
}
//# sourceMappingURL=data:application/json;base64,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