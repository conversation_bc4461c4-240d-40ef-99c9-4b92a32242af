import { SwtDateField } from '../controls/swt-datefield.component';
/**
 *
 * <AUTHOR>
 */
export declare class DateUtils {
    static timepat1: RegExp;
    static timepat2: RegExp;
    static timepat3: RegExp;
    constructor();
    /**
     * Compare two dates and return:
     * <ul>
     *	<li>0, if date1 > date2</li>
     *	<li>1, if date1 < date2</li>
     *	<li>-1, if date1 = date2</li>
     *	</ul>
     **/
    static compareDates(date1: Date, date2: Date, includeHours?: boolean): number;
    /**
     * Compare two dates with string format following the dateFormat and return:
     * - 0, if date1>date2
     * - 1, if date1=date2
     * - -1, if date1<date2
     **/
    static compareDatesAsString(date1: string, date2: string, dateFormat: string): Number;
    /**
     * Return the date as string with giving the date object and its format
     **/
    static getDateAsString(date: SwtDateField): string;
    /**
     * Used to valide time
     * */
    static validateTime(timeStr: string): string;
    /**
     * Converts a string to a Date object
     * */
    static dateFromString(dateString: string, pattern: string): Date;
    /**
     * Converts an Oracle ISO format into a date
     * */
    static isoToDate(value: string): Date;
    /**
     * Converts a date into Oracle ISO format
     * */
    static dateToIso(value: Date, displaySeconds?: boolean): string;
    /**
     *  Parses a String object that contains a date, and returns a Date
     *  object corresponding to the String.
     *  The <code>inputFormat</code> argument contains the pattern
     *  in which the <code>valueString</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  <p>The function does not check for the validity of the Date object.
     *  If the value of the date, month, or year is NaN, this method returns null.</p>
     *
     *  <p>For example:
     *  <pre>var dob:Date = DateField.stringToDate("06/30/2005", "MM/DD/YYYY");</pre>
     *  </p>
     *
     *  @param valueString Date value to format.
     *
     *  @param inputFormat String defining the date format.
     *
     *  @return The formatted date as a Date object.
     *
     */
    static stringToDate(valueString: string, inputFormat: string): Date;
    /**
     *  Formats a Date into a String according to the <code>outputFormat</code> argument.
     *  The <code>outputFormat</code> argument contains a pattern in which
     *  the <code>value</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  @param value Date value to format.
     *
     *  @param outputFormat String defining the date format.
     *
     *  @return The formatted date as a String.
     *
     *  @example <pre>var todaysDate:String = DateField.dateToString(new Date(), "MM/DD/YYYY");</pre>
     */
    static dateToString(value: Date, outputFormat: string): string;
}
