/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, Input, ViewChild, ViewContainerRef, EventEmitter } from "@angular/core";
import { CommonService } from "../utils/common.service";
import { fromPromise } from "rxjs-compat/observable/fromPromise";
import { UIComponent } from "../controls/UIComponent.service";
import { FormControl, Validators } from "@angular/forms";
import { SwtUtil } from '../utils/swt-util.service';
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
var $ = require('jquery');
/**
 * \@auth : Rihab JABALLAH created on June-2018
 */
var Container = /** @class */ (function (_super) {
    tslib_1.__extends(Container, _super);
    //----------------------------------------------------------------------------------------------------------------
    function Container(__element, __common) {
        var _this_1 = _super.call(this, __element, __common) || this;
        _this_1.__element = __element;
        _this_1.__common = __common;
        _this_1.X = 0;
        _this_1.Y = 0;
        _this_1.urlLoadingStart = new EventEmitter();
        _this_1.urlLoadingEnd = new EventEmitter();
        _this_1.components = []; // Keep track of list of generated components for removal purposes
        _this_1.firstCall = true;
        _this_1._IsIncludeInLayout = true;
        _this_1._isVisible = true;
        _this_1._isEnabled = true;
        _this_1._toolTip = "";
        _this_1._toolTipPreviousValue = null;
        _this_1.__paddingTop = 0;
        _this_1.__paddingLeft = 0;
        _this_1.__paddingRight = 0;
        _this_1.__paddingBottom = 0;
        _this_1._isloaded = false;
        _this_1.__horizontalGap = "0";
        _this_1.__verticalGap = "0";
        _this_1.__right = "0";
        _this_1.__left = "0";
        _this_1.__dropShadowEnabled = "true";
        _this_1._showScrollBar = false;
        return _this_1;
    }
    /**
     * @return {?}
     */
    Container.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        //-Add ToolTip.
        if (this._toolTip && $(this.__element.nativeElement).length > 0) {
            $($(this.__element.nativeElement)[0]).attr("title", this._toolTip);
            /** @type {?} */
            var _this_2 = this;
            $(this.domElement).tooltip({
                show: { duration: 800, delay: 500 },
                open: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                function (event, ui) {
                    $($(_this_2.__element.nativeElement)[0]).removeAttr('title');
                })
            });
        }
    };
    Object.defineProperty(Container.prototype, "dropShadowEnabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__dropShadowEnabled;
        },
        //---dropShadowEnabled ---------------------------------------------------------------------------------------------------
        set: 
        //---dropShadowEnabled ---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__dropShadowEnabled = this.adaptValueAsBoolean(value);
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('box-shadow', _this_1.__dropShadowEnabled ? '0 4px 5px 0 rgba(0, 0, 0, 0.6), 0 0px 0px 0 rgba(0, 0, 0, 0.5)' : 'none', _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "cornerRadius", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__cornerRadius;
        },
        //---cornerRadius---------------------------------------------------------------------------------------------------
        set: 
        //---cornerRadius---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__cornerRadius = this.adaptUnit(value);
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('border-radius', _this_1.__cornerRadius, _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "borderThickness", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__borderThickness;
        },
        //---borderThickness---------------------------------------------------------------------------------------------------
        set: 
        //---borderThickness---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__borderThickness = this.adaptUnit(value);
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('border-width', _this_1.__borderThickness, _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "borderStyle", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__borderStyle;
        },
        //---borderStyle---------------------------------------------------------------------------------------------------
        set: 
        //---borderStyle---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__borderStyle = value;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('border-style', value, _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "borderColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__borderColor;
        },
        //---borderColor---------------------------------------------------------------------------------------------------
        set: 
        //---borderColor---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__borderColor = value;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('border-color', value, _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "backGroundColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__backGroundColor;
        },
        //---borderColor---------------------------------------------------------------------------------------------------
        set: 
        //---borderColor---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            this.__backGroundColor = value;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this_1.setStyle('background-color', value, _this_1.domElement);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "right", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__right;
        },
        //---right---------------------------------------------------------------------------------------------------
        set: 
        //---right---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this_1 = this;
            if (this.__element.nativeElement) {
                /** @type {?} */
                var parentWidth = $($(this.__element.nativeElement).parent()).width();
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this_1.setStyle('display', 'flex');
                    _this_1.setStyle('position', 'sticky');
                    _this_1.__right = _this_1.adaptUnit(Number(parentWidth) - Number(value) - $(_this_1.__element.nativeElement)[0].scrollWidth, "auto");
                    _this_1.setStyle('left', _this_1.__right);
                }), 0);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "left", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__left;
        },
        //---Left---------------------------------------------------------------------------------------------------
        set: 
        //---Left---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.paddingLeft = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "bottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingBottom;
        },
        //---bottom------------------------------------------------------------------------------------------------
        set: 
        //---bottom------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.paddingBottom = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "top", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingTop;
        },
        //---top------------------------------------------------------------------------------------------------
        set: 
        //---top------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.paddingTop = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "horizontalGap", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__horizontalGap;
        },
        //---horizontalGap---------------------------------------------------------------------------------------------------
        set: 
        //---horizontalGap---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__horizontalGap = this.adaptUnit(value, "auto");
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "verticalGap", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__verticalGap;
        },
        //---verticalGap---------------------------------------------------------------------------------------------------
        set: 
        //---verticalGap---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__verticalGap = this.adaptUnit(value, "auto");
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "textAlign", {
        //---TextAlign---------------------------------------------------------------------------------------------------
        set: 
        //---TextAlign---------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this.__element.nativeElement.style.textAlign = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "hostElement", {
        //---HostElement-----------------------------------------------------------------------------------------------------
        get: 
        //---HostElement-----------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._hostElement;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "domElement", {
        //---DomElement-----------------------------------------------------------------------------------------------------
        get: 
        //---DomElement-----------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._domElement;
        },
        enumerable: true,
        configurable: true
    });
    //---TabIndex-----------------------------------------------------------------------------------------------------
    //---TabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} element
     * @param {?} value
     * @return {?}
     */
    Container.prototype.addTabIndex = 
    //---TabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} element
     * @param {?} value
     * @return {?}
     */
    function (element, value) {
        if ($(element))
            $(element).attr("tabindex", String(value));
    };
    /**
     * @param {?} element
     * @return {?}
     */
    Container.prototype.removeTabIndex = /**
     * @param {?} element
     * @return {?}
     */
    function (element) {
        if ($(element))
            $(element).attr("tabindex", "-1");
    };
    Object.defineProperty(Container.prototype, "toolTip", {
        get: /**
         * @return {?}
         */
        function () {
            return this._toolTip;
        },
        //---ToolTip---------------------------------------------------------------------------------------------------------
        set: 
        //---ToolTip---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTip = value;
            if ($(this.__element.nativeElement)) {
                $($(this.__element.nativeElement)[0]).tooltip({
                    show: { duration: 800, delay: 500 },
                    open: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        $(this).removeAttr('title');
                    })
                });
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "toolTipPreviousValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._toolTipPreviousValue;
        },
        //---ToolTip---------------------------------------------------------------------------------------------------------
        set: 
        //---ToolTip---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            console.log("🚀 ~ file: swt-container.component.ts:282 ~ Container ~ settoolTipPreviousValue ~ value:", value);
            this._toolTipPreviousValue = value;
            //    if($(this.__element.nativeElement)){
            //        $($(this.__element.nativeElement)[0]).tooltip({
            //            show: { duration: 800, delay : 500 },
            //            open: function( event, ui ) {
            //                $(this).removeAttr('title');
            //            }
            //        })
            //    }
            // this.renderer.setAttribute(div, 'popper', 'As text');
            // this.renderer.setAttribute(div, 'popperTrigger', 'hover');
            // this.renderer.setAttribute(div, 'popperPlacement', 'bottom');
            if ($(this.__element.nativeElement)) {
                console.log("---**");
                $($(this.__element.nativeElement)[0]).attr('popper', 'As text');
                $($(this.__element.nativeElement)[0]).attr('popperTrigger', 'hover');
                $($(this.__element.nativeElement)[0]).attr('popperPlacement', 'bottom');
            }
            if (!this.enabled) {
                // this.addAllOutputsEventsListeners();
                $($(this.__element.nativeElement)[0].children[0]).removeClass('disabled-container');
                $($(this.__element.nativeElement)[0].children[0]).addClass('disabled-container-tooltip');
                $($(this.__element.nativeElement)[0]).removeClass('disablePointerEvents');
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "textDictionaryId", {
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this._toolTip = SwtUtil.getPredictMessage(value);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "name", {
        get: /**
         * @return {?}
         */
        function () {
            return this._name;
        },
        //---Name---------------------------------------------------------------------------------------------------------
        set: 
        //---Name---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            // set name to the component's DOM.
            this._name = value;
            if ($(this.__element.nativeElement))
                $($(this.__element.nativeElement)[0]).attr("name", this._name);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "styleName", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__styleName;
        },
        //---styleName----------------------------------------------------------------------------------------------------
        set: 
        //---styleName----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__styleName = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "horizontalAlign", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__horizontalAlign;
        },
        //---horizontalAlign----------------------------------------------------------------------------------------------
        set: 
        //---horizontalAlign----------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value == "center") {
                this.__horizontalAlign = "center";
            }
            else if (value == "left") {
                this.__horizontalAlign = "flex-start";
            }
            else if (value == "right") {
                this.__horizontalAlign = "flex-end";
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "verticalAlign", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__verticalAlign;
        },
        //---verticalAlign------------------------------------------------------------------------------------------------
        set: 
        //---verticalAlign------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value == "middle")
                this.__verticalAlign = "center";
            else if (value == "bottom")
                this.__verticalAlign = "flex-end";
            else if (value == "top")
                this.__verticalAlign = "flex-start";
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__width;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this.__width = this.adaptUnit(value, "auto");
                if (String(this.__width).indexOf("px") !== -1) {
                    //en px
                    $(this.__element.nativeElement.children[0]).css({
                        "width": this.adaptUnit(value, "auto")
                    });
                }
                else {
                    //en %
                    this.setStyle("width", this.__width);
                    $(this.__element.nativeElement.children[0]).css({
                        "width": "100%"
                    });
                }
            }
            catch (error) {
                console.error('method [set width] - error :', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "showScrollBar", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showScrollBar;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._showScrollBar = StringUtils.isTrue(value);
                //en %
                this.setStyle("overflow", 'auto');
                $(this.__element.nativeElement.children[0]).css({
                    "overflow": "auto"
                });
            }
            catch (error) {
                console.error('method [set showScrollBar] - error :', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__height;
        },
        //---height-------------------------------------------------------------------------------------------------------
        set: 
        //---height-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__height = this.adaptUnit(value, "auto");
            if (String(this.__height).indexOf("px") !== -1) {
                //en px
                $(this.__element.nativeElement.children[0]).css({
                    "height": this.__height
                });
                if (this.getComponentName() == "SWTLABEL" || this.getComponentName() == "SWTTEXT") {
                    $(this.__element.nativeElement.children[0]).css({
                        "line-height": this.__height
                    });
                }
            }
            else {
                //en %
                this.setStyle("height", this.__height);
                $(this.__element.nativeElement.children[0]).css({
                    "height": "100%"
                });
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "minHeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minHeight;
        },
        //---minHeight-------------------------------------------------------------------------------------------------------
        set: 
        //---minHeight-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minHeight = value;
            this.setStyle('min-height', this._minHeight + 'px', this.hostElement);
            this.setStyle('min-height', this._minHeight + 'px', this.domElement);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "minWidth", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minWidth;
        },
        //---minWidth-------------------------------------------------------------------------------------------------------
        set: 
        //---minWidth-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minWidth = value;
            this.setStyle('min-width', this._minWidth + 'px', this.hostElement);
            this.setStyle('min-width', this._minWidth + 'px', this.domElement);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "maxHeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxHeight;
        },
        //---minHeight-------------------------------------------------------------------------------------------------------
        set: 
        //---minHeight-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxHeight = value;
            this.setStyle('max-height', this._maxHeight + 'px', this.hostElement);
            this.setStyle('max-height', this._maxHeight + 'px', this.domElement);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "maxWidth", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxWidth;
        },
        //---minWidth-------------------------------------------------------------------------------------------------------
        set: 
        //---minWidth-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxWidth = value;
            this.setStyle('max-width', this._maxWidth + 'px', this.hostElement);
            this.setStyle('max-width', this._maxWidth + 'px', this.domElement);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "includeInLayout", {
        get: /**
         * @return {?}
         */
        function () {
            return this._IsIncludeInLayout;
        },
        //---includeInLayout----------------------------------------------------------------------------------------------
        set: 
        //---includeInLayout----------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "false") {
                    this._IsIncludeInLayout = false;
                }
                else {
                    this._IsIncludeInLayout = true;
                }
            }
            else {
                this._IsIncludeInLayout = value;
            }
            if (this.visible.toString() == 'false') {
                if (!this._IsIncludeInLayout)
                    this.setStyle("display", "none");
                else {
                    this.setStyle("visibility", "hidden");
                    this.setStyle("display", "block");
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._isVisible;
        },
        //---visible------------------------------------------------------------------------------------------------------
        set: 
        //---visible------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (this._isVisible != this.adaptValueAsBoolean(value)) {
                this._isVisible = this.adaptValueAsBoolean(value);
                if (!this._isVisible) {
                    if (this.includeInLayout == true) {
                        this.setStyle("visibility", "hidden");
                    }
                    else {
                        $(this.__element.nativeElement).hide();
                    }
                }
                else {
                    this.setStyle("visibility", "visible");
                    $(this.__element.nativeElement).show();
                    $(this.__element.nativeElement).removeClass("hidden");
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._isEnabled;
        },
        //---enabled------------------------------------------------------------------------------------------------------
        set: 
        //---enabled------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (!this.hostElement)
                this._hostElement = $(this.__element.nativeElement) ? $(this.__element.nativeElement) : null;
            if (!this.domElement)
                this._domElement = this.hostElement ? $($(this.__element.nativeElement)[0].children[0]) : null;
            if (String(this._isEnabled) != String(value)) {
                this._isEnabled = this.adaptValueAsBoolean(value);
                if (this._isEnabled) {
                    $($(this.__element.nativeElement)[0].children[0]).removeClass('disabled-container');
                    $($(this.__element.nativeElement)[0]).removeClass('disablePointerEvents');
                }
                else {
                    $($(this.__element.nativeElement)[0].children[0]).addClass('disabled-container');
                    $($(this.__element.nativeElement)[0]).addClass('disablePointerEvents');
                }
                if (this._isEnabled) {
                    this.addAllOutputsEventsListeners(this.hostElement);
                }
                else {
                    this.removeAllOuputsEventsListeners(this.hostElement);
                    this.removeTabIndex(this.hostElement);
                    this.removeTabIndex(this.domElement);
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "paddingTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingTop;
        },
        //---Paddings------------------------------------------------------------------------------------------------
        set: 
        //---Paddings------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__paddingTop = this.adaptUnit(value);
            if (this.getComponentName() == "SWTPANEL") {
                /** @type {?} */
                var panelBody = $(this.__element.nativeElement).find('.panelBody');
                if (panelBody) {
                    this.setStyle('padding-top', this.__paddingTop, panelBody);
                }
            }
            else if (this.getComponentName() == 'SWTTABNAVIGATOR') {
                /** @type {?} */
                var tabNavigatorContent = $(this.__element.nativeElement.children[0]) ? $(this.__element.nativeElement.children[0]).find('.tabNavigator-content') : null;
                if ($(tabNavigatorContent).length > 0) {
                    this.setStyle('padding-top', this.__paddingTop, $(tabNavigatorContent));
                }
            }
            else {
                this.setStyle('padding-top', this.__paddingTop, $(this.__element.nativeElement.children[0]));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "paddingBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingBottom;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__paddingBottom = this.adaptUnit(value);
            if (this.getComponentName() == "SWTPANEL") {
                /** @type {?} */
                var panelBody = $(this.__element.nativeElement).find('.panelBody');
                if (panelBody) {
                    this.setStyle('padding-bottom', this.__paddingBottom, panelBody);
                }
            }
            else if (this.getComponentName() == 'SWTTABNAVIGATOR') {
                /** @type {?} */
                var tabNavigatorContent = $(this.__element.nativeElement.children[0]) ? $(this.__element.nativeElement.children[0]).find('.tabNavigator-content') : null;
                if ($(tabNavigatorContent).length > 0) {
                    this.setStyle('padding-bottom', this.__paddingBottom, $(tabNavigatorContent));
                }
            }
            else {
                this.setStyle('padding-bottom', this.__paddingBottom, $(this.__element.nativeElement.children[0]));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "paddingLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingLeft;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__paddingLeft = this.adaptUnit(value);
            if (this.getComponentName() == "SWTPANEL") {
                /** @type {?} */
                var panelBody = $(this.__element.nativeElement).find('.panelBody');
                if (panelBody) {
                    this.setStyle('padding-left', this.__paddingLeft, panelBody);
                }
            }
            else if (this.getComponentName() == 'SWTTABNAVIGATOR') {
                /** @type {?} */
                var tabNavigatorContent = $(this.__element.nativeElement.children[0]) ? $(this.__element.nativeElement.children[0]).find('.tabNavigator-content') : null;
                if ($(tabNavigatorContent).length > 0) {
                    this.setStyle('padding-left', this.__paddingLeft, $(tabNavigatorContent));
                }
            }
            else {
                this.setStyle('padding-left', this.__paddingLeft, $(this.__element.nativeElement.children[0]));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "paddingRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__paddingRight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__paddingRight = this.adaptUnit(value);
            if (this.getComponentName() == "SWTPANEL") {
                /** @type {?} */
                var panelBody = $(this.__element.nativeElement).find('.panelBody');
                if (panelBody) {
                    this.setStyle('padding-right', this.__paddingRight, panelBody);
                }
            }
            else if (this.getComponentName() == 'SWTTABNAVIGATOR') {
                /** @type {?} */
                var tabNavigatorContent = $(this.__element.nativeElement.children[0]) ? $(this.__element.nativeElement.children[0]).find('.tabNavigator-content') : null;
                if ($(tabNavigatorContent).length > 0) {
                    this.setStyle('padding-right', this.__paddingRight, $(tabNavigatorContent));
                }
            }
            else {
                this.setStyle('padding-right', this.__paddingRight, $(this.__element.nativeElement.children[0]));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "marginTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__marginTop;
        },
        //---Margins------------------------------------------------------------------------------------------------
        set: 
        //---Margins------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__marginTop = this.adaptUnit(value);
            if (this.getComponentName() != "SWTPANEL") {
                this.setStyle("margin-top", this.__marginTop, $(this.__element.nativeElement.children[0]));
            }
            else {
                this.setStyle("margin-top", this.__marginTop);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "marginBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__marginBottom;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__marginBottom = this.adaptUnit(value);
            if (this.getComponentName() != "SWTPANEL") {
                this.setStyle("margin-bottom", this.__marginBottom, $(this.__element.nativeElement.children[0]));
            }
            else {
                this.setStyle("margin-bottom", this.__marginBottom);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "marginLeft", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__marginLeft;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__marginLeft = this.adaptUnit(value);
            if (this.getComponentName() != "SWTPANEL") {
                this.setStyle("margin-left", this.__marginLeft, $(this.__element.nativeElement.children[0]));
            }
            else {
                this.setStyle("margin-left", this.__marginLeft);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Container.prototype, "marginRight", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__marginRight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__marginRight = this.adaptUnit(value);
            if (this.getComponentName() != "SWTPANEL") {
                this.setStyle("margin-right", this.__marginRight, $(this.__element.nativeElement.children[0]));
            }
            else {
                this.setStyle("margin-right", this.__marginRight);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    Container.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        //-Getting host element.
        this._hostElement = $(this.__element.nativeElement) ? $(this.__element.nativeElement) : null;
        this._domElement = this.hostElement ? $($(this.__element.nativeElement)[0].children[0]) : null;
        //Remove all events on hostElement
        this.removeAllOuputsEventsListeners(this.hostElement);
        //-Remove tabIndex on hot element.
        this.removeTabIndex($(this.hostElement));
        //-Exclude "SwtButton" because it handles his own events by it self.
        if (this._isEnabled && this.getComponentName() != "SWTBUTTON") {
            this.addAllOutputsEventsListeners(this.hostElement);
        }
    };
    /**
     * @return {?}
     */
    Container.prototype.getRealHeight = /**
     * @return {?}
     */
    function () {
        return this._hostElement.offsetHeight;
    };
    /**
     * addChild : append a dynamically created component.
     * @param type : Component type
     */
    /**
     * addChild : append a dynamically created component.
     * @param {?} value
     * @return {?}
     */
    Container.prototype.addChild = /**
     * addChild : append a dynamically created component.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        var _this_1 = this;
        /** @type {?} */
        var componentInstance;
        try {
            if (typeof value === "string") {
                this.load(value).subscribe((/**
                 * @param {?} component
                 * @return {?}
                 */
                function (component) {
                    _this_1.urlLoadingStart.emit(new Date().getMilliseconds());
                    componentInstance = _this_1._container.createComponent(component, 0);
                    _this_1.urlLoadingEnd.emit(new Date().getMilliseconds());
                    _this_1._isloaded = true;
                }), (/**
                 * @param {?} error
                 * @return {?}
                 */
                function (error) {
                    _this_1._isloaded = false;
                }));
            }
            else {
                /** @type {?} */
                var comp = this.__common.componentFactoryResolver.resolveComponentFactory(value);
                componentInstance = this._container.createComponent(comp, 0);
                /** @type {?} */
                var children = $($(this.__element.nativeElement).children()[0]).children();
                if (componentInstance.instance['element'] !== undefined) {
                    //its a component.
                    $(componentInstance.instance['element'].nativeElement).insertAfter($(children[children.length - 1]));
                }
                else if (componentInstance.instance['elementRef'] !== undefined) {
                    //its a screen SwtModule.
                    $(componentInstance.instance['elementRef'].nativeElement).insertAfter($(children[children.length - 1]));
                }
                else if (componentInstance.instance['elem'] !== undefined) {
                    $(componentInstance.instance['elem'].nativeElement).insertAfter($(children[children.length - 1]));
                }
                this.components.push(componentInstance);
                $(componentInstance.instance).attr('id', "dynamic-" + Math.random().toString(36).substr(2, 5));
                $(componentInstance.instance).attr('selector', comp.selector);
                if ($(this.__element.nativeElement)[0].nodeName == "SWTRADIOBUTTONGROUP") {
                    componentInstance.instance.parentGroup = this;
                    componentInstance.instance.parentGroup.radioItemsArray.push(componentInstance.instance);
                }
                return (componentInstance.instance);
            }
        }
        catch (error) {
            console.log(error);
            throw new Error('Error :' + error);
        }
    };
    /**
     * addChildAt : append a dynamically created component at a specific index.
     * @param index : index
     */
    /**
     * addChildAt : append a dynamically created component at a specific index.
     * @param {?} type
     * @param {?} index : index
     * @return {?}
     */
    Container.prototype.addChildAt = /**
     * addChildAt : append a dynamically created component at a specific index.
     * @param {?} type
     * @param {?} index : index
     * @return {?}
     */
    function (type, index) {
        try {
            /** @type {?} */
            var OriginalChildren = tslib_1.__assign({}, $(this.__element.nativeElement.children[0]).children());
            if (index === 0 && OriginalChildren.length === 0) {
                return this.addChild(type);
            }
            else if (index < this.getChildren().length) {
                /** @type {?} */
                var comp = this.__common.componentFactoryResolver.resolveComponentFactory(type);
                /** @type {?} */
                var component = this._container.createComponent(comp, index);
                /* Push the component so that we can keep track of which components are created */
                this.components.push(component);
                $(component.instance).attr('id', "dynamic-" + Math.random().toString(36).substr(2, 5));
                $(component.instance).attr('selector', comp.selector);
                /* setTimeout(() => {
                     const NewChildren = $($(this.__element.nativeElement).children()[0]).children();
                     const newAddedComponent = $(NewChildren.toArray().find(x => x.id === component.instance['id']));
 
                     const componentToBeReplacedBefore = $(OriginalChildren).eq(index);
                     $(newAddedComponent).insertBefore($(componentToBeReplacedBefore));
                 }, 0);*/
                return component.instance;
            }
            else {
                console.error(">>> PROGRAMMATION ERROR :  index is outside the bounds of components array. ");
            }
        }
        catch (error) {
            console.log("e", error);
            throw new Error('Error :' + error);
        }
    };
    /**
     * removeChild : Delete a component child
     * @param componentClass : the component to remove
     */
    /**
     * removeChild : Delete a component child
     * @param {?} componentClass : the component to remove
     * @return {?}
     */
    Container.prototype.removeChild = /**
     * removeChild : Delete a component child
     * @param {?} componentClass : the component to remove
     * @return {?}
     */
    function (componentClass) {
        try {
            /** @type {?} */
            var component;
            /** @type {?} */
            var componentIndex = -1;
            //dynamic child
            for (var index = 0; index < this.components.length; index++) {
                if (this.components[index].instance === componentClass) {
                    componentIndex = index;
                    component = this.components[index];
                }
            }
            //static child
            if (componentIndex !== -1) {
                /* Remove component from both view and array */
                this._container.remove(this._container.indexOf(component));
                this.components.splice(componentIndex, 1);
                $("#" + $.escapeSelector(componentClass.id)).remove();
            }
            else {
                if (componentClass != undefined && !componentClass.id) {
                    $(componentClass.element.nativeElement).empty();
                }
                else {
                    $("#" + $.escapeSelector(componentClass.id)).remove();
                }
            }
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * removeAllChildren : Deletes all the statically
     * & dynamically child components.
     */
    /**
     * removeAllChildren : Deletes all the statically
     * & dynamically child components.
     * @return {?}
     */
    Container.prototype.removeAllChildren = /**
     * removeAllChildren : Deletes all the statically
     * & dynamically child components.
     * @return {?}
     */
    function () {
        try {
            //remove all static children
            /** @type {?} */
            var children = $($(this.__element.nativeElement).children()[0]).children();
            for (var index = 0; index < children.length; index++) {
                $(children[index]).remove();
            }
            this.components = [];
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * getChildren : get children of a statically
     * & dynamically child components.
     */
    /**
     * getChildren : get children of a statically
     * & dynamically child components.
     * @return {?}
     */
    Container.prototype.getChildren = /**
     * getChildren : get children of a statically
     * & dynamically child components.
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var findIndex = -1;
            /** @type {?} */
            var childrenList = new Array();
            /** @type {?} */
            var children = $($(this.__element.nativeElement).children()[0]).children();
            //- Getting children
            for (var index = 0; index < children.length; index++) {
                //- check if its a dynamic child.
                if ((findIndex = this.components.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.instance.id == $(children[index])[0].id; }))) != -1) {
                    childrenList.push({
                        childType: this.components[findIndex].instance.selector,
                        id: this.components[findIndex].instance.id,
                        component: this.components[findIndex].instance
                    });
                }
                else {
                    //- its a static child.
                    childrenList.push({ childType: $($(children[index])[0]).attr('selector'),
                        id: $(children[index])[0].id
                    });
                }
            }
            return childrenList;
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * contains : return if the component exists in the container or not
     */
    /**
     * contains : return if the component exists in the container or not
     * @param {?} componentClass
     * @return {?}
     */
    Container.prototype.contains = /**
     * contains : return if the component exists in the container or not
     * @param {?} componentClass
     * @return {?}
     */
    function (componentClass) {
        try {
            /** @type {?} */
            var children = this.getChildren();
            /** @type {?} */
            var child = (componentClass != undefined && children.length > 0) ? children.find((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.id == componentClass.id; })) : null;
            /** @type {?} */
            var contain = child != null ? true : false;
            return contain;
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * returns a component at a specific key index
     * @param key
     */
    /**
     * returns a component at a specific key index
     * @param {?} key
     * @return {?}
     */
    Container.prototype.getChildAt = /**
     * returns a component at a specific key index
     * @param {?} key
     * @return {?}
     */
    function (key) {
        /** @type {?} */
        var childrenList = this.getChildren();
        try {
            if (key < childrenList.length && (childrenList != null && childrenList != undefined) && (childrenList[key] != null && childrenList[key] != undefined)) {
                return childrenList[key];
            }
            return undefined;
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     *
     */
    /**
     *
     * @param {?} name
     * @return {?}
     */
    Container.prototype.getChildByName = /**
     *
     * @param {?} name
     * @return {?}
     */
    function (name) {
        /** @type {?} */
        var childrenList = this.getChildren();
        try {
            /** @type {?} */
            var childcomponent = (childrenList != undefined && childrenList.length > 0) ? childrenList.find((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.id == name; })) : undefined;
            return childcomponent;
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     *
     */
    /**
     *
     * @param {?} index
     * @return {?}
     */
    Container.prototype.removeChildAt = /**
     *
     * @param {?} index
     * @return {?}
     */
    function (index) {
        /** @type {?} */
        var childrenList = this.getChildren();
        try {
            if (childrenList != undefined && childrenList.length > 0 && index < childrenList.length) {
                /** @type {?} */
                var childcomponent = childrenList[index];
                if (childcomponent != undefined) {
                    if (childcomponent.component != undefined) {
                        this.removeChild(childcomponent.component);
                    }
                    else if (childcomponent.id != undefined) {
                        /** @type {?} */
                        var child = this.getChildByName(childcomponent.id);
                        if (child != undefined && child != null) {
                            this.removeChild(child);
                        }
                    }
                }
            }
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * returns  component'index if exists otherwise -1.
     * @param component
     */
    /**
     * returns  component'index if exists otherwise -1.
     * @param {?} component
     * @return {?}
     */
    Container.prototype.getChildIndex = /**
     * returns  component'index if exists otherwise -1.
     * @param {?} component
     * @return {?}
     */
    function (component) {
        /** @type {?} */
        var childrenList = this.getChildren();
        try {
            /** @type {?} */
            var index = -1;
            if (component != undefined && component != null) {
                index = childrenList.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return ((x.component == component) || (x.id == component.id)); }));
            }
            return index;
        }
        catch (error) {
            throw new Error('Error :' + error);
        }
    };
    /**
     * This method is used to load component instance from its lazy module path.
     * @param url
     */
    /**
     * This method is used to load component instance from its lazy module path.
     * @param {?} url
     * @return {?}
     */
    Container.prototype.load = /**
     * This method is used to load component instance from its lazy module path.
     * @param {?} url
     * @return {?}
     */
    function (url) {
        var _this_1 = this;
        /** @type {?} */
        var _comp_url = url.indexOf("?") ? url.split("?")[0] : url;
        /** @type {?} */
        var manifest = this.__common.manifests
            .find((/**
         * @param {?} m
         * @return {?}
         */
        function (m) { return m.path === _comp_url; }));
        /** @type {?} */
        var p = this.__common.loader.load(manifest.loadChildren)
            .then((/**
         * @param {?} ngModuleFactory
         * @return {?}
         */
        function (ngModuleFactory) {
            /** @type {?} */
            var moduleRef = ngModuleFactory.create(_this_1.__common.injector);
            // Read from the moduleRef injector and locate the dynamic component type
            /** @type {?} */
            var dynamicComponentType = moduleRef.injector.get(_comp_url);
            //this.mapDataObject(dynamicComponentType, this.getUrlQuery(this.url));
            // Resolve this component factory
            return moduleRef.componentFactoryResolver.resolveComponentFactory(dynamicComponentType);
        }));
        return fromPromise(p);
    };
    /**
     * This method is used to clear container
     */
    /**
     * This method is used to clear container
     * @return {?}
     */
    Container.prototype.clean = /**
     * This method is used to clear container
     * @return {?}
     */
    function () {
        this._container.clear();
    };
    /**
     * This method is used to check if current container
     * already contains DOM.
     */
    /**
     * This method is used to check if current container
     * already contains DOM.
     * @return {?}
     */
    Container.prototype.isloaded = /**
     * This method is used to check if current container
     * already contains DOM.
     * @return {?}
     */
    function () {
        return this._isloaded;
    };
    /**
     * @return {?}
     */
    Container.prototype.unload = /**
     * @return {?}
     */
    function () {
        this._isloaded = false;
        this._container.clear();
    };
    /**
     *
     * @param prop
     * @param value
     */
    /**
     *
     * @param {?} prop
     * @param {?} value
     * @param {?=} elem
     * @return {?}
     */
    Container.prototype.setStyle = /**
     *
     * @param {?} prop
     * @param {?} value
     * @param {?=} elem
     * @return {?}
     */
    function (prop, value, elem) {
        try {
            if (!elem && this.__element.nativeElement) {
                this.__element.nativeElement.style[prop] = value;
            }
            else if ($(elem)[0]) {
                $(elem)[0].style[prop] = value;
            }
        }
        catch (error) {
            console.error("method [setStyle] - error :", error);
        }
    };
    /**
     *
     * @param visibility
     */
    /**
     *
     * @param {?} visibility
     * @return {?}
     */
    Container.prototype.setVisible = /**
     *
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        try {
            this.visible = visibility;
        }
        catch (error) {
            console.error("setVisible method", error);
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    Container.prototype._spyChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    Container.prototype.resetOriginalValue = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.originalValue = value;
        this._spyChanges(value);
    };
    /**
     * @return {?}
     */
    Container.prototype.clone = /**
     * @return {?}
     */
    function () {
        // TODO...
    };
    /**
     * @param {?} text
     * @return {?}
     */
    Container.prototype.validateRestrict = /**
     * @param {?} text
     * @return {?}
     */
    function (text) {
        if (this.restrict && text) {
            text = text.toString();
            /** @type {?} */
            var control = new FormControl(text, Validators.pattern('[' + this.restrict + ']'));
            /** @type {?} */
            var permittedChars = '[' + this.restrict + ']';
            return ((text.match(new RegExp(permittedChars, "gi"))) == null ? "" : (text.match(new RegExp(permittedChars, "gi"))).join(''));
        }
        return text;
    };
    /**
     * @param {?} text
     * @return {?}
     */
    Container.prototype.validateMaxChar = /**
     * @param {?} text
     * @return {?}
     */
    function (text) {
        /** @type {?} */
        var control = new FormControl(text, Validators.maxLength(this.maxChars));
        if (!control.valid) {
            return text.substr(0, this.maxChars);
        }
        return text;
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    Container.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        console.log("Container -> ngOnDestroy -> ngOnDestroy *------------------------------");
        try {
            this.removeAllOuputsEventsListeners(this._hostElement);
            delete this.__element;
            delete this.__common;
            delete this.components;
            delete this._domElement;
            delete this.originalValue;
            delete this._isloaded;
            delete this._IsIncludeInLayout;
            delete this._isVisible;
            delete this._isEnabled;
            delete this.__paddingTop;
            delete this.__paddingLeft;
            delete this.__paddingBottom;
            delete this.__paddingRight;
            delete this.__marginBottom;
            delete this.__marginLeft;
            delete this.__marginRight;
            delete this.__marginTop;
            delete this.__width;
            delete this.__height;
            delete this.__left;
            delete this.__right;
            delete this.__horizontalGap;
            delete this.__verticalGap;
            delete this.__horizontalAlign;
            delete this.__verticalAlign;
            delete this._name;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    Container.decorators = [
        { type: Component, args: [{
                    selector: 'Container',
                    template: "  \n    "
                }] }
    ];
    /** @nocollapse */
    Container.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    Container.propDecorators = {
        _container: [{ type: ViewChild, args: ['_container', { read: ViewContainerRef },] }],
        dropShadowEnabled: [{ type: Input, args: ['dropShadowEnabled',] }],
        cornerRadius: [{ type: Input, args: ['cornerRadius',] }],
        borderThickness: [{ type: Input, args: ['borderThickness',] }],
        borderStyle: [{ type: Input, args: ['borderStyle',] }],
        borderColor: [{ type: Input, args: ['borderColor',] }],
        backGroundColor: [{ type: Input, args: ['backGroundColor',] }],
        right: [{ type: Input, args: ['right',] }],
        left: [{ type: Input, args: ['left',] }],
        bottom: [{ type: Input }],
        top: [{ type: Input }],
        horizontalGap: [{ type: Input, args: ['horizontalGap',] }],
        verticalGap: [{ type: Input, args: ['verticalGap',] }],
        textAlign: [{ type: Input, args: ['textAlign',] }],
        toolTip: [{ type: Input, args: ['toolTip',] }],
        toolTipPreviousValue: [{ type: Input, args: ['toolTipPreviousValue',] }],
        textDictionaryId: [{ type: Input, args: ['tooltipDictionaryId',] }],
        name: [{ type: Input, args: ['name',] }],
        styleName: [{ type: Input, args: ['styleName',] }],
        horizontalAlign: [{ type: Input }],
        verticalAlign: [{ type: Input }],
        width: [{ type: Input, args: ['width',] }],
        showScrollBar: [{ type: Input, args: ['showScrollBar',] }],
        height: [{ type: Input, args: ['height',] }],
        minHeight: [{ type: Input, args: ['minHeight',] }],
        minWidth: [{ type: Input, args: ['minWidth',] }],
        maxHeight: [{ type: Input, args: ['maxHeight',] }],
        maxWidth: [{ type: Input, args: ['maxWidth',] }],
        includeInLayout: [{ type: Input }],
        visible: [{ type: Input }],
        enabled: [{ type: Input }],
        paddingTop: [{ type: Input }],
        paddingBottom: [{ type: Input }],
        paddingLeft: [{ type: Input }],
        paddingRight: [{ type: Input }],
        marginTop: [{ type: Input }],
        marginBottom: [{ type: Input }],
        marginLeft: [{ type: Input }],
        marginRight: [{ type: Input }],
        maxChars: [{ type: Input, args: ["maxChars",] }]
    };
    return Container;
}(UIComponent));
export { Container };
if (false) {
    /** @type {?} */
    Container.prototype._container;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._hostElement;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._domElement;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.isCancelUsed;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.X;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.Y;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.urlLoadingStart;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.urlLoadingEnd;
    /** @type {?} */
    Container.prototype.components;
    /** @type {?} */
    Container.prototype.originalValue;
    /** @type {?} */
    Container.prototype.firstCall;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._IsIncludeInLayout;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._isVisible;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._isEnabled;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._name;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._toolTipPreviousValue;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__paddingTop;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__paddingLeft;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__paddingRight;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__paddingBottom;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__marginBottom;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__marginLeft;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__marginRight;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__marginTop;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__horizontalAlign;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__verticalAlign;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__width;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._minWidth;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._maxWidth;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__height;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._minHeight;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._maxHeight;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._isloaded;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__styleName;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__horizontalGap;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__verticalGap;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__right;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__left;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__cornerRadius;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__borderColor;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__backGroundColor;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__borderStyle;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__borderThickness;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__dropShadowEnabled;
    /**
     * @type {?}
     * @private
     */
    Container.prototype._showScrollBar;
    /** @type {?} */
    Container.prototype.maxChars;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__element;
    /**
     * @type {?}
     * @private
     */
    Container.prototype.__common;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbnRhaW5lci5jb21wb25lbnQuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7OztBQUFBLE9BQU8sRUFBQyxTQUFTLEVBQTRDLFVBQVUsRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFFLGdCQUFnQixFQUFFLFlBQVksRUFBK0IsTUFBTSxlQUFlLENBQUM7QUFDOUssT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLHlCQUF5QixDQUFDO0FBQ3RELE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxvQ0FBb0MsQ0FBQztBQUMvRCxPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0saUNBQWlDLENBQUM7QUFJNUQsT0FBTyxFQUFFLFdBQVcsRUFBRSxVQUFVLEVBQUUsTUFBTSxnQkFBZ0IsQ0FBQztBQUN6RCxPQUFPLEVBQUUsT0FBTyxFQUFFLE1BQU0sMkJBQTJCLENBQUM7QUFDcEQsT0FBTyxFQUFFLFdBQVcsRUFBRSxNQUFNLCtCQUErQixDQUFDOztJQUt0RCxDQUFDLEdBQUcsT0FBTyxDQUFDLFFBQVEsQ0FBQzs7OztBQU0zQjtJQU8rQixxQ0FBVztJQW9yQnRDLGtIQUFrSDtJQUdsSCxtQkFBb0IsU0FBcUIsRUFBVSxRQUF1QjtRQUExRSxjQUNJLGtCQUFNLFNBQVMsRUFBRSxRQUFRLENBQUMsU0FDN0I7UUFGbUIsaUJBQVMsR0FBVCxTQUFTLENBQVk7UUFBVSxnQkFBUSxHQUFSLFFBQVEsQ0FBZTtRQS9wQmxFLFNBQUMsR0FBTSxDQUFDLENBQUM7UUFDVCxTQUFDLEdBQU0sQ0FBQyxDQUFDO1FBRVQsdUJBQWUsR0FBRyxJQUFJLFlBQVksRUFBTyxDQUFDO1FBQzFDLHFCQUFhLEdBQUcsSUFBSSxZQUFZLEVBQU8sQ0FBQztRQUV6QyxrQkFBVSxHQUFHLEVBQUUsQ0FBQyxDQUFBLGtFQUFrRTtRQUVsRixpQkFBUyxHQUFFLElBQUksQ0FBQztRQUNmLDBCQUFrQixHQUFRLElBQUksQ0FBQztRQUMvQixrQkFBVSxHQUFRLElBQUksQ0FBQztRQUN2QixrQkFBVSxHQUFRLElBQUksQ0FBQztRQUV2QixnQkFBUSxHQUFNLEVBQUUsQ0FBQztRQUNqQiw2QkFBcUIsR0FBTSxJQUFJLENBQUM7UUFFaEMsb0JBQVksR0FBVSxDQUFDLENBQUU7UUFDekIscUJBQWEsR0FBUyxDQUFDLENBQUU7UUFDekIsc0JBQWMsR0FBUSxDQUFDLENBQUU7UUFDekIsdUJBQWUsR0FBTyxDQUFDLENBQUU7UUFpQnpCLGlCQUFTLEdBQUcsS0FBSyxDQUFDO1FBRWxCLHVCQUFlLEdBQU0sR0FBRyxDQUFFO1FBQzFCLHFCQUFhLEdBQU0sR0FBRyxDQUFFO1FBQ3hCLGVBQU8sR0FBTSxHQUFHLENBQUU7UUFDbEIsY0FBTSxHQUFNLEdBQUcsQ0FBRTtRQU1qQiwyQkFBbUIsR0FBTSxNQUFNLENBQUU7UUErU2pDLHNCQUFjLEdBQUcsS0FBSyxDQUFDOztJQW1VL0IsQ0FBQzs7OztJQXhyQkQsbUNBQWU7OztJQUFmO1FBQ0ssZUFBZTtRQUNmLElBQUcsSUFBSSxDQUFDLFFBQVEsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEdBQUUsQ0FBQyxFQUFDO1lBQzNELENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDOztnQkFDL0QsT0FBSyxHQUFHLElBQUk7WUFFaEIsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxPQUFPLENBQUM7Z0JBQ3ZCLElBQUksRUFBRSxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUUsS0FBSyxFQUFHLEdBQUcsRUFBRTtnQkFDcEMsSUFBSTs7Ozs7Z0JBQUUsVUFBVSxLQUFLLEVBQUUsRUFBRTtvQkFDckIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFLLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUMvRCxDQUFDLENBQUE7YUFDSixDQUFDLENBQUM7U0FDTjtJQUNMLENBQUM7SUE0REQsc0JBQ0ksd0NBQWlCOzs7O1FBTXJCO1lBQ0ksT0FBTyxJQUFJLENBQUMsbUJBQW1CLENBQUM7UUFDcEMsQ0FBQztRQVZELDBIQUEwSDs7Ozs7OztRQUMxSCxVQUNzQixLQUFTO1lBRC9CLG1CQU1DO1lBSkcsSUFBSSxDQUFDLG1CQUFtQixHQUFFLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMxRCxVQUFVOzs7WUFBQztnQkFDUCxPQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksRUFBRSxPQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDLGdFQUFnRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUcsT0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQ3hKLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksbUNBQVk7Ozs7UUFNaEI7WUFDSSxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7UUFDL0IsQ0FBQztRQVZELG9IQUFvSDs7Ozs7OztRQUNwSCxVQUNpQixLQUFTO1lBRDFCLG1CQU1DO1lBSkcsSUFBSSxDQUFDLGNBQWMsR0FBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzNDLFVBQVU7OztZQUFDO2dCQUNQLE9BQUksQ0FBQyxRQUFRLENBQUMsZUFBZSxFQUFFLE9BQUksQ0FBQyxjQUFjLEVBQUcsT0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzFFLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksc0NBQWU7Ozs7UUFNbkI7WUFDSSxPQUFPLElBQUksQ0FBQyxpQkFBaUIsQ0FBQztRQUNsQyxDQUFDO1FBVkQsdUhBQXVIOzs7Ozs7O1FBQ3ZILFVBQ29CLEtBQVk7WUFEaEMsbUJBTUM7WUFKRyxJQUFJLENBQUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMvQyxVQUFVOzs7WUFBQztnQkFDUCxPQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRSxPQUFJLENBQUMsaUJBQWlCLEVBQUksT0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzdFLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksa0NBQVc7Ozs7UUFNZjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDO1FBVkQsbUhBQW1IOzs7Ozs7O1FBQ25ILFVBQ2dCLEtBQVk7WUFENUIsbUJBTUM7WUFKRyxJQUFJLENBQUMsYUFBYSxHQUFFLEtBQUssQ0FBQztZQUMxQixVQUFVOzs7WUFBQztnQkFDUCxPQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRSxLQUFLLEVBQUcsT0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzNELENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksa0NBQVc7Ozs7UUFNZjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDO1FBVkQsbUhBQW1IOzs7Ozs7O1FBQ25ILFVBQ2dCLEtBQVk7WUFENUIsbUJBTUM7WUFKRyxJQUFJLENBQUMsYUFBYSxHQUFFLEtBQUssQ0FBQztZQUMxQixVQUFVOzs7WUFBQztnQkFDUCxPQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRSxLQUFLLEVBQUcsT0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzNELENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7OztPQUFBO0lBS0Qsc0JBQ0ksc0NBQWU7Ozs7UUFNbkI7WUFDSSxPQUFPLElBQUksQ0FBQyxpQkFBaUIsQ0FBQztRQUNsQyxDQUFDO1FBVkQsbUhBQW1IOzs7Ozs7O1FBQ25ILFVBQ29CLEtBQVk7WUFEaEMsbUJBTUM7WUFKRyxJQUFJLENBQUMsaUJBQWlCLEdBQUUsS0FBSyxDQUFDO1lBQzlCLFVBQVU7OztZQUFDO2dCQUNQLE9BQUksQ0FBQyxRQUFRLENBQUMsa0JBQWtCLEVBQUUsS0FBSyxFQUFHLE9BQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMvRCxDQUFDLEdBQUUsQ0FBQyxDQUFDLENBQUM7UUFDVixDQUFDOzs7T0FBQTtJQU1ELHNCQUNJLDRCQUFLOzs7O1FBV1Q7WUFDSSxPQUFPLElBQUksQ0FBQyxPQUFPLENBQUM7UUFDeEIsQ0FBQztRQWZELDZHQUE2Rzs7Ozs7OztRQUM3RyxVQUNVLEtBQVM7WUFEbkIsbUJBV0M7WUFURyxJQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxFQUFDOztvQkFDeEIsV0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRTtnQkFDckUsVUFBVTs7O2dCQUFDO29CQUNQLE9BQUksQ0FBQyxRQUFRLENBQUMsU0FBUyxFQUFHLE1BQU0sQ0FBQyxDQUFDO29CQUNsQyxPQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsRUFBRyxRQUFRLENBQUMsQ0FBQztvQkFDckMsT0FBSSxDQUFDLE9BQU8sR0FBRyxPQUFJLENBQUMsU0FBUyxDQUFHLE1BQU0sQ0FBQyxXQUFXLENBQUMsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLE9BQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsV0FBVyxFQUFHLE1BQU0sQ0FBQyxDQUFDO29CQUMvSCxPQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sRUFBRyxPQUFJLENBQUMsT0FBTyxDQUFHLENBQUM7Z0JBQzNDLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQzthQUNUO1FBQ0wsQ0FBQzs7O09BQUE7SUFLRCxzQkFDSSwyQkFBSTs7OztRQUlSO1lBQ0ksT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDO1FBQ3ZCLENBQUM7UUFSRCw0R0FBNEc7Ozs7Ozs7UUFDNUcsVUFDUyxLQUFTO1lBRWQsSUFBSSxDQUFDLFdBQVcsR0FBRSxLQUFLLENBQUM7UUFDNUIsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVyw2QkFBTTs7OztRQUdqQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQztRQUNoQyxDQUFDO1FBUEQsMkdBQTJHOzs7Ozs7O1FBQzNHLFVBQ2tCLEtBQUs7WUFDbkIsSUFBSSxDQUFDLGFBQWEsR0FBRSxLQUFLLENBQUM7UUFDOUIsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVywwQkFBRzs7OztRQUdkO1lBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQzdCLENBQUM7UUFQRCx3R0FBd0c7Ozs7Ozs7UUFDeEcsVUFDZSxLQUFLO1lBQ2hCLElBQUksQ0FBQyxVQUFVLEdBQUUsS0FBSyxDQUFDO1FBQzNCLENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksb0NBQWE7Ozs7UUFJakI7WUFDSSxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7UUFDaEMsQ0FBQztRQVJELHFIQUFxSDs7Ozs7OztRQUNySCxVQUNrQixLQUFZO1lBQzFCLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFFekQsQ0FBQzs7O09BQUE7SUFNRCxzQkFDSSxrQ0FBVzs7OztRQUdmO1lBQ0ksT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1FBQzlCLENBQUM7UUFQRCxtSEFBbUg7Ozs7Ozs7UUFDbkgsVUFDZ0IsS0FBWTtZQUN4QixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQ3ZELENBQUM7OztPQUFBO0lBTUQsc0JBQ0ksZ0NBQVM7UUFGYixpSEFBaUg7Ozs7Ozs7UUFDakgsVUFDYyxLQUFZO1lBQ3RCLElBQUksS0FBSyxFQUFFO2dCQUNQLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO2FBQ3hEO1FBQ0wsQ0FBQzs7O09BQUE7SUFFRCxzQkFBSSxrQ0FBVztRQURmLHFIQUFxSDs7Ozs7O1FBQ3JIO1lBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQzdCLENBQUM7OztPQUFBO0lBR0Qsc0JBQUksaUNBQVU7UUFEZCxvSEFBb0g7Ozs7OztRQUNwSDtZQUNJLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUM1QixDQUFDOzs7T0FBQTtJQUVELGtIQUFrSDs7Ozs7OztJQUMzRywrQkFBVzs7Ozs7OztJQUFsQixVQUFtQixPQUFZLEVBQUUsS0FBVTtRQUN2QyxJQUFHLENBQUMsQ0FBQyxPQUFPLENBQUM7WUFBRSxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztJQUM5RCxDQUFDOzs7OztJQUNNLGtDQUFjOzs7O0lBQXJCLFVBQXNCLE9BQU87UUFDekIsSUFBRyxDQUFDLENBQUMsT0FBTyxDQUFDO1lBQUUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDckQsQ0FBQztJQUdELHNCQUNXLDhCQUFPOzs7O1FBWWxCO1lBQ0ksT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFFO1FBQzFCLENBQUM7UUFoQkQscUhBQXFIOzs7Ozs7O1FBQ3JILFVBQ21CLEtBQWE7WUFDNUIsSUFBSSxDQUFDLFFBQVEsR0FBRSxLQUFLLENBQUM7WUFDckIsSUFBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsRUFBQztnQkFDL0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUMxQyxJQUFJLEVBQUUsRUFBRSxRQUFRLEVBQUUsR0FBRyxFQUFFLEtBQUssRUFBRyxHQUFHLEVBQUU7b0JBQ3BDLElBQUk7Ozs7O29CQUFFLFVBQVUsS0FBSyxFQUFFLEVBQUU7d0JBQ3JCLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQ2hDLENBQUMsQ0FBQTtpQkFDSixDQUFDLENBQUE7YUFDTDtRQUVMLENBQUM7OztPQUFBO0lBT0Usc0JBQ1csMkNBQW9COzs7O1FBK0IvQjtZQUNJLE9BQU8sSUFBSSxDQUFDLHFCQUFxQixDQUFFO1FBQ3ZDLENBQUM7UUFuQ0QscUhBQXFIOzs7Ozs7O1FBQ3JILFVBQ2dDLEtBQWE7WUFDekMsT0FBTyxDQUFDLEdBQUcsQ0FBQywwRkFBMEYsRUFBRSxLQUFLLENBQUMsQ0FBQTtZQUM5RyxJQUFJLENBQUMscUJBQXFCLEdBQUUsS0FBSyxDQUFDO1lBQ3JDLDBDQUEwQztZQUMxQyx5REFBeUQ7WUFDekQsbURBQW1EO1lBQ25ELDJDQUEyQztZQUMzQyw4Q0FBOEM7WUFDOUMsZUFBZTtZQUNmLFlBQVk7WUFDWixPQUFPO1lBQ1Asd0RBQXdEO1lBQ3hELDZEQUE2RDtZQUM3RCxnRUFBZ0U7WUFDaEUsSUFBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsRUFBQztnQkFDL0IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFFckIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxTQUFTLENBQUMsQ0FBQztnQkFDaEUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRSxPQUFPLENBQUMsQ0FBQztnQkFDckUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGlCQUFpQixFQUFFLFFBQVEsQ0FBQyxDQUFDO2FBRzNFO1lBQ0QsSUFBRyxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUM7Z0JBQ2IsdUNBQXVDO2dCQUN2QyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFDLG9CQUFvQixDQUFDLENBQUM7Z0JBQ3BGLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsNEJBQTRCLENBQUMsQ0FBQztnQkFDekYsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFDLHNCQUFzQixDQUFDLENBQUM7YUFFN0U7UUFDRixDQUFDOzs7T0FBQTtJQU1KLHNCQUFrQyx1Q0FBZ0I7Ozs7O1FBQWxELFVBQW1ELEtBQUs7WUFDcEQsSUFBRyxLQUFLLEVBQUU7Z0JBQ04sSUFBSSxDQUFDLFFBQVEsR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsS0FBSyxDQUFDLENBQUE7YUFDbkQ7UUFDTCxDQUFDOzs7T0FBQTtJQUdELHNCQUNXLDJCQUFJOzs7O1FBTWY7WUFDSSxPQUFPLElBQUksQ0FBQyxLQUFLLENBQUM7UUFDdEIsQ0FBQztRQVZELGtIQUFrSDs7Ozs7OztRQUNsSCxVQUNnQixLQUFhO1lBQ3pCLG1DQUFtQztZQUNuQyxJQUFJLENBQUMsS0FBSyxHQUFFLEtBQUssQ0FBQztZQUNsQixJQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQztnQkFDOUIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDdkUsQ0FBQzs7O09BQUE7SUFNRCxzQkFDSSxnQ0FBUzs7OztRQUdiO1lBQ0ksT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFBO1FBQzNCLENBQUM7UUFQRCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFDZSxLQUFZO1lBQ3ZCLElBQUksQ0FBQyxXQUFXLEdBQUUsS0FBSyxDQUFDO1FBQzVCLENBQUM7OztPQUFBO0lBTUQsc0JBQ1csc0NBQWU7Ozs7UUFVMUI7WUFDSSxPQUFRLElBQUksQ0FBQyxpQkFBaUIsQ0FBRTtRQUNwQyxDQUFDO1FBZEQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQzJCLEtBQUs7WUFDNUIsSUFBSSxLQUFLLElBQUksUUFBUSxFQUFFO2dCQUNuQixJQUFJLENBQUMsaUJBQWlCLEdBQUcsUUFBUSxDQUFDO2FBQ3JDO2lCQUFNLElBQUksS0FBSyxJQUFJLE1BQU0sRUFBRTtnQkFDeEIsSUFBSSxDQUFDLGlCQUFpQixHQUFHLFlBQVksQ0FBQzthQUN6QztpQkFBTSxJQUFJLEtBQUssSUFBSSxPQUFPLEVBQUU7Z0JBQ3pCLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxVQUFVLENBQUM7YUFDdkM7UUFDTCxDQUFDOzs7T0FBQTtJQU9ELHNCQUNXLG9DQUFhOzs7O1FBVXhCO1lBQ0ksT0FBUSxJQUFJLENBQUMsZUFBZSxDQUFFO1FBQ2xDLENBQUM7UUFkRCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFDeUIsS0FBSztZQUUxQixJQUFJLEtBQUssSUFBSSxRQUFRO2dCQUNqQixJQUFJLENBQUMsZUFBZSxHQUFHLFFBQVEsQ0FBQztpQkFDL0IsSUFBSSxLQUFLLElBQUksUUFBUTtnQkFDdEIsSUFBSSxDQUFDLGVBQWUsR0FBRyxVQUFVLENBQUM7aUJBQ2pDLElBQUksS0FBSyxJQUFJLEtBQUs7Z0JBQ25CLElBQUksQ0FBQyxlQUFlLEdBQUcsWUFBWSxDQUFDO1FBQzVDLENBQUM7OztPQUFBO0lBT0Qsc0JBQ1csNEJBQUs7Ozs7UUFxQmhCO1lBQ0ksT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO1FBQ3hCLENBQUM7UUF6QkQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQ2lCLEtBQUs7WUFDbEIsSUFBRztnQkFFQyxJQUFJLENBQUMsT0FBTyxHQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2dCQUMzQyxJQUFJLE1BQU0sQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFDO29CQUMxQyxPQUFPO29CQUNQLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7d0JBQzVDLE9BQU8sRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUM7cUJBQ3pDLENBQUMsQ0FBQztpQkFDTjtxQkFBSTtvQkFDRCxNQUFNO29CQUNOLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztvQkFDdEMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQzt3QkFDNUMsT0FBTyxFQUFFLE1BQU07cUJBQ2xCLENBQUMsQ0FBQztpQkFDTjthQUVKO1lBQUEsT0FBTSxLQUFLLEVBQUM7Z0JBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsRUFBQyxLQUFLLENBQUMsQ0FBQTthQUN0RDtRQUNMLENBQUM7OztPQUFBO0lBTUQsc0JBQ1csb0NBQWE7Ozs7UUFheEI7WUFDSSxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7UUFDL0IsQ0FBQztRQWpCRCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFDeUIsS0FBSztZQUMxQixJQUFHO2dCQUNDLElBQUksQ0FBQyxjQUFjLEdBQUcsV0FBVyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDaEQsTUFBTTtnQkFDTixJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsRUFBRyxNQUFNLENBQUMsQ0FBQztnQkFDbkMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQztvQkFDNUMsVUFBVSxFQUFFLE1BQU07aUJBQ3JCLENBQUMsQ0FBQzthQUVOO1lBQUEsT0FBTSxLQUFLLEVBQUM7Z0JBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQ0FBc0MsRUFBQyxLQUFLLENBQUMsQ0FBQTthQUM5RDtRQUNMLENBQUM7OztPQUFBO0lBTUQsc0JBQ1csNkJBQU07Ozs7UUF1QmpCO1lBQ0ksT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDO1FBQ3pCLENBQUM7UUEzQkQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQ2tCLEtBQUs7WUFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQztZQUM1QyxJQUFJLE1BQU0sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFDO2dCQUMzQyxPQUFPO2dCQUNQLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7b0JBQzVDLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUTtpQkFDMUIsQ0FBQyxDQUFDO2dCQUNILElBQUksSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksVUFBVSxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLFNBQVMsRUFBRTtvQkFDL0UsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQzt3QkFDNUMsYUFBYSxFQUFDLElBQUksQ0FBQyxRQUFRO3FCQUM5QixDQUFDLENBQUM7aUJBQ047YUFFSjtpQkFBSTtnQkFDRCxNQUFNO2dCQUNOLElBQUksQ0FBQyxRQUFRLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDdkMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQztvQkFDNUMsUUFBUSxFQUFFLE1BQU07aUJBQ25CLENBQUMsQ0FBQzthQUNOO1FBR0wsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVyxnQ0FBUzs7OztRQUtwQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztRQUMzQixDQUFDO1FBVEQscUhBQXFIOzs7Ozs7O1FBQ3JILFVBQ3FCLEtBQVk7WUFDN0IsSUFBSSxDQUFDLFVBQVUsR0FBQyxLQUFLLENBQUM7WUFDdEIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLFVBQVUsR0FBQyxJQUFJLEVBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQ3RFLElBQUksQ0FBQyxRQUFRLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxVQUFVLEdBQUMsSUFBSSxFQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUN6RSxDQUFDOzs7T0FBQTtJQU1ELHNCQUNXLCtCQUFROzs7O1FBS25CO1lBQ0ksT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1FBQzFCLENBQUM7UUFURCxvSEFBb0g7Ozs7Ozs7UUFDcEgsVUFDb0IsS0FBWTtZQUM1QixJQUFJLENBQUMsU0FBUyxHQUFDLEtBQUssQ0FBQztZQUNyQixJQUFJLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsU0FBUyxHQUFDLElBQUksRUFBSSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDcEUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFNBQVMsR0FBQyxJQUFJLEVBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3ZFLENBQUM7OztPQUFBO0lBS0Qsc0JBQ1csZ0NBQVM7Ozs7UUFLcEI7WUFDSSxPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7UUFDM0IsQ0FBQztRQVRELHFIQUFxSDs7Ozs7OztRQUNySCxVQUNxQixLQUFZO1lBQzdCLElBQUksQ0FBQyxVQUFVLEdBQUMsS0FBSyxDQUFDO1lBQ3RCLElBQUksQ0FBQyxRQUFRLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxVQUFVLEdBQUMsSUFBSSxFQUFJLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUN0RSxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsVUFBVSxHQUFDLElBQUksRUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDekUsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVywrQkFBUTs7OztRQUtuQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztRQUMxQixDQUFDO1FBVEQsb0hBQW9IOzs7Ozs7O1FBQ3BILFVBQ29CLEtBQVk7WUFDNUIsSUFBSSxDQUFDLFNBQVMsR0FBQyxLQUFLLENBQUM7WUFDckIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFNBQVMsR0FBQyxJQUFJLEVBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQ3BFLElBQUksQ0FBQyxRQUFRLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxTQUFTLEdBQUMsSUFBSSxFQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUN2RSxDQUFDOzs7T0FBQTtJQU1ELHNCQUNXLHNDQUFlOzs7O1FBb0IxQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGtCQUFrQixDQUFDO1FBQ25DLENBQUM7UUF4QkQsa0hBQWtIOzs7Ozs7O1FBQ2xILFVBQzJCLEtBQWM7WUFDckMsSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssUUFBUSxFQUFFO2dCQUM3QixJQUFJLEtBQUssS0FBSyxPQUFPLEVBQUU7b0JBQ25CLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxLQUFLLENBQUM7aUJBQ25DO3FCQUFNO29CQUNILElBQUksQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUM7aUJBQ2xDO2FBQ0o7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLGtCQUFrQixHQUFHLEtBQUssQ0FBQzthQUNuQztZQUNELElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsSUFBSSxPQUFPLEVBQUU7Z0JBQ3BDLElBQUksQ0FBQyxJQUFJLENBQUMsa0JBQWtCO29CQUN4QixJQUFJLENBQUMsUUFBUSxDQUFFLFNBQVMsRUFBRSxNQUFNLENBQUMsQ0FBQTtxQkFDaEM7b0JBQ0QsSUFBSSxDQUFDLFFBQVEsQ0FBRSxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUE7b0JBQ3RDLElBQUksQ0FBQyxRQUFRLENBQUUsU0FBUyxFQUFFLE9BQU8sQ0FBQyxDQUFBO2lCQUNyQzthQUNKO1FBRUwsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVyw4QkFBTzs7OztRQWlCbEI7WUFDSSxPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7UUFDM0IsQ0FBQztRQXJCRCxrSEFBa0g7Ozs7Ozs7UUFDbEgsVUFDbUIsS0FBVTtZQUV6QixJQUFHLElBQUksQ0FBQyxVQUFVLElBQUksSUFBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssQ0FBQyxFQUFDO2dCQUNsRCxJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDbEQsSUFBRyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUU7b0JBQ2pCLElBQUksSUFBSSxDQUFDLGVBQWUsSUFBSSxJQUFJLEVBQUU7d0JBQzlCLElBQUksQ0FBQyxRQUFRLENBQUUsWUFBWSxFQUFFLFFBQVEsQ0FBQyxDQUFDO3FCQUMxQzt5QkFBTTt3QkFDSCxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztxQkFDMUM7aUJBQ0o7cUJBQUk7b0JBQ0QsSUFBSSxDQUFDLFFBQVEsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQ3ZDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO29CQUN2QyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUM7aUJBQ3pEO2FBQ0o7UUFDTCxDQUFDOzs7T0FBQTtJQU1ELHNCQUNXLDhCQUFPOzs7O1FBMEJsQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztRQUMzQixDQUFDO1FBOUJELGtIQUFrSDs7Ozs7OztRQUNsSCxVQUNtQixLQUFVO1lBRXpCLElBQUcsQ0FBQyxJQUFJLENBQUMsV0FBVztnQkFBRSxJQUFJLENBQUMsWUFBWSxHQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBRSxDQUFDLENBQUMsSUFBSSxDQUFDO1lBQ3JILElBQUcsQ0FBQyxJQUFJLENBQUMsVUFBVTtnQkFBRyxJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBRSxDQUFDLENBQUEsSUFBSSxDQUFDO1lBRXJILElBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxNQUFNLENBQUMsS0FBSyxDQUFDLEVBQUM7Z0JBQ3hDLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUNsRCxJQUFHLElBQUksQ0FBQyxVQUFVLEVBQUM7b0JBQ2YsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO29CQUNwRixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsc0JBQXNCLENBQUMsQ0FBQztpQkFDN0U7cUJBQUk7b0JBQ0QsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO29CQUNqRixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsc0JBQXNCLENBQUMsQ0FBQztpQkFDMUU7Z0JBRUQsSUFBRyxJQUFJLENBQUMsVUFBVSxFQUFDO29CQUNmLElBQUksQ0FBQyw0QkFBNEIsQ0FBRSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7aUJBRXhEO3FCQUFJO29CQUNELElBQUksQ0FBQyw4QkFBOEIsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFFLENBQUM7b0JBQ3ZELElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO29CQUN0QyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztpQkFDeEM7YUFDSjtRQUVMLENBQUM7OztPQUFBO0lBTUQsc0JBQ1csaUNBQVU7Ozs7UUFrQnJCO1lBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQzdCLENBQUM7UUF0QkQsNkdBQTZHOzs7Ozs7O1FBQzdHLFVBQ3NCLEtBQUs7WUFDdkIsSUFBSSxDQUFDLFlBQVksR0FBSSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzNDLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksVUFBVSxFQUFDOztvQkFDakMsU0FBUyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUM7Z0JBQ2xFLElBQUcsU0FBUyxFQUFDO29CQUNULElBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDLENBQUE7aUJBQzdEO2FBQ0o7aUJBQUssSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxpQkFBaUIsRUFBQzs7b0JBRTlDLG1CQUFtQixHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQSxDQUFDLENBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFBLENBQUMsQ0FBQyxJQUFJO2dCQUN2SixJQUFHLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLE1BQU0sR0FBQyxDQUFDLEVBQUM7b0JBQy9CLElBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQTtpQkFDMUU7YUFDSjtpQkFDRztnQkFDQSxJQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQy9GO1FBQ0wsQ0FBQzs7O09BQUE7SUFLRCxzQkFDVyxvQ0FBYTs7OztRQWlCeEI7WUFDSSxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7UUFDaEMsQ0FBQzs7Ozs7UUFwQkQsVUFDeUIsS0FBSztZQUMxQixJQUFJLENBQUMsZUFBZSxHQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDOUMsSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxVQUFVLEVBQUM7O29CQUNqQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQztnQkFDbEUsSUFBRyxTQUFTLEVBQUM7b0JBQ1QsSUFBSSxDQUFDLFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsZUFBZSxFQUFFLFNBQVMsQ0FBQyxDQUFBO2lCQUNuRTthQUNKO2lCQUFLLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksaUJBQWlCLEVBQUM7O29CQUM5QyxtQkFBbUIsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUEsQ0FBQyxDQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQSxDQUFDLENBQUMsSUFBSTtnQkFDdkosSUFBRyxDQUFDLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDO29CQUMvQixJQUFJLENBQUMsUUFBUSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQyxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQTtpQkFDaEY7YUFDSjtpQkFDRztnQkFDQSxJQUFJLENBQUMsUUFBUSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7YUFDckc7UUFDTCxDQUFDOzs7T0FBQTtJQUtELHNCQUNXLGtDQUFXOzs7O1FBaUJ0QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDOzs7OztRQXBCRCxVQUN1QixLQUFLO1lBQ3hCLElBQUksQ0FBQyxhQUFhLEdBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUM1QyxJQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLFVBQVUsRUFBQzs7b0JBQ2pDLFNBQVMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDO2dCQUNsRSxJQUFHLFNBQVMsRUFBQztvQkFDVCxJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLFNBQVMsQ0FBQyxDQUFBO2lCQUMvRDthQUNKO2lCQUFLLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksaUJBQWlCLEVBQUM7O29CQUM5QyxtQkFBbUIsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUEsQ0FBQyxDQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQSxDQUFDLENBQUMsSUFBSTtnQkFDdkosSUFBRyxDQUFDLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxNQUFNLEdBQUMsQ0FBQyxFQUFDO29CQUMvQixJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLENBQUE7aUJBQzVFO2FBQ0o7aUJBQ0c7Z0JBQ0EsSUFBSSxDQUFDLFFBQVEsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTthQUNqRztRQUNMLENBQUM7OztPQUFBO0lBS0Qsc0JBQ1csbUNBQVk7Ozs7UUFpQnZCO1lBQ0ksT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO1FBQy9CLENBQUM7Ozs7O1FBcEJELFVBQ3dCLEtBQUs7WUFDekIsSUFBSSxDQUFDLGNBQWMsR0FBSSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzdDLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksVUFBVSxFQUFDOztvQkFDakMsU0FBUyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUM7Z0JBQ2xFLElBQUcsU0FBUyxFQUFDO29CQUNULElBQUksQ0FBQyxRQUFRLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxjQUFjLEVBQUUsU0FBUyxDQUFDLENBQUE7aUJBQ2pFO2FBQ0o7aUJBQUssSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxpQkFBaUIsRUFBQzs7b0JBQzlDLG1CQUFtQixHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQSxDQUFDLENBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFBLENBQUMsQ0FBQyxJQUFJO2dCQUN2SixJQUFHLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLE1BQU0sR0FBQyxDQUFDLEVBQUM7b0JBQy9CLElBQUksQ0FBQyxRQUFRLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQTtpQkFDOUU7YUFDSjtpQkFDRztnQkFDQSxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQ25HO1FBQ0wsQ0FBQzs7O09BQUE7SUFNRCxzQkFDVyxnQ0FBUzs7OztRQVFwQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUM1QixDQUFDO1FBWkQsNEdBQTRHOzs7Ozs7O1FBQzVHLFVBQ3FCLEtBQUs7WUFDdEIsSUFBSSxDQUFDLFdBQVcsR0FBSSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzFDLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUcsVUFBVSxFQUFDO2dCQUNwQyxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksRUFBRyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQzlGO2lCQUFJO2dCQUNELElBQUksQ0FBQyxRQUFRLENBQUMsWUFBWSxFQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTthQUNqRDtRQUNMLENBQUM7OztPQUFBO0lBS0Qsc0JBQ1csbUNBQVk7Ozs7UUFRdkI7WUFDSSxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7UUFDL0IsQ0FBQzs7Ozs7UUFYRCxVQUN3QixLQUFLO1lBQ3pCLElBQUksQ0FBQyxjQUFjLEdBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUM3QyxJQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFHLFVBQVUsRUFBQztnQkFDcEMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxlQUFlLEVBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTthQUNwRztpQkFBSTtnQkFDRCxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsRUFBRyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUE7YUFDdkQ7UUFDTCxDQUFDOzs7T0FBQTtJQUtELHNCQUNXLGlDQUFVOzs7O1FBUXJCO1lBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQzdCLENBQUM7Ozs7O1FBWEQsVUFDc0IsS0FBSztZQUN2QixJQUFJLENBQUMsWUFBWSxHQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDM0MsSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBRyxVQUFVLEVBQUM7Z0JBQ3BDLElBQUksQ0FBQyxRQUFRLENBQUMsYUFBYSxFQUFHLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7YUFDaEc7aUJBQUk7Z0JBQ0QsSUFBSSxDQUFDLFFBQVEsQ0FBQyxhQUFhLEVBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO2FBQ25EO1FBQ0wsQ0FBQzs7O09BQUE7SUFLRCxzQkFDVyxrQ0FBVzs7OztRQVF0QjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDOzs7OztRQVhELFVBQ3VCLEtBQUs7WUFDeEIsSUFBSSxDQUFDLGFBQWEsR0FBSSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzVDLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUcsVUFBVSxFQUFDO2dCQUNwQyxJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsRUFBRyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQ2xHO2lCQUFJO2dCQUNELElBQUksQ0FBQyxRQUFRLENBQUMsY0FBYyxFQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQTthQUNyRDtRQUNMLENBQUM7OztPQUFBOzs7O0lBZUQsNEJBQVE7OztJQUFSO1FBRUksd0JBQXdCO1FBQ3hCLElBQUksQ0FBQyxZQUFZLEdBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFFLENBQUMsQ0FBQyxJQUFJLENBQUM7UUFDL0YsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBLElBQUksQ0FBQztRQUU5RixrQ0FBa0M7UUFDbEMsSUFBSSxDQUFDLDhCQUE4QixDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUV0RCxrQ0FBa0M7UUFDbEMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUM7UUFFekMsb0VBQW9FO1FBQ3BFLElBQUcsSUFBSSxDQUFDLFVBQVUsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxXQUFXLEVBQUM7WUFDekQsSUFBSSxDQUFDLDRCQUE0QixDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztTQUN2RDtJQUNMLENBQUM7Ozs7SUFDRCxpQ0FBYTs7O0lBQWI7UUFDSSxPQUFPLElBQUksQ0FBQyxZQUFZLENBQUMsWUFBWSxDQUFDO0lBQzFDLENBQUM7SUFFRDs7O09BR0c7Ozs7OztJQUNJLDRCQUFROzs7OztJQUFmLFVBQWdCLEtBQVU7UUFBMUIsbUJBMENDOztZQXpDTyxpQkFBc0I7UUFDMUIsSUFBSTtZQUNBLElBQUksT0FBTyxLQUFLLEtBQUssUUFBUSxFQUFFO2dCQUMzQixJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLFNBQVM7Ozs7Z0JBQUMsVUFBQyxTQUFTO29CQUM3QixPQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksRUFBRSxDQUFDLGVBQWUsRUFBRSxDQUFDLENBQUM7b0JBQ3hELGlCQUFpQixHQUFHLE9BQUksQ0FBQyxVQUFVLENBQUMsZUFBZSxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsQ0FBQztvQkFDbEUsT0FBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsSUFBSSxJQUFJLEVBQUUsQ0FBQyxlQUFlLEVBQUUsQ0FBQyxDQUFDO29CQUN0RCxPQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztnQkFDMUIsQ0FBQzs7OztnQkFDRCxVQUFDLEtBQUs7b0JBQ0YsT0FBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7Z0JBQzNCLENBQUMsRUFBQyxDQUFDO2FBQ1Y7aUJBQU07O29CQUNHLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLHdCQUF3QixDQUFDLHVCQUF1QixDQUFDLEtBQUssQ0FBQztnQkFDbEYsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxlQUFlLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDOztvQkFDdkQsUUFBUSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRTtnQkFDNUUsSUFBSSxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEtBQUssU0FBUyxFQUFFO29CQUNyRCxrQkFBa0I7b0JBQ2xCLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7aUJBQ3hHO3FCQUFNLElBQUksaUJBQWlCLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxLQUFLLFNBQVMsRUFBRTtvQkFDL0QseUJBQXlCO29CQUN6QixDQUFDLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO2lCQUMzRztxQkFBSyxJQUFJLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsS0FBSyxTQUFTLEVBQUU7b0JBQ3hELENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7aUJBQ3JHO2dCQUNELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7Z0JBQ3hDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLFVBQVUsR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDL0YsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO2dCQUU5RCxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsSUFBTSxxQkFBcUIsRUFBRTtvQkFDeEUsaUJBQWlCLENBQUMsUUFBUSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUM7b0JBQzlDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxXQUFXLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsQ0FBQztpQkFDM0Y7Z0JBQ0QsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDO2FBQ3ZDO1NBRUo7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFbkIsTUFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDO0lBRUQ7OztPQUdHOzs7Ozs7O0lBQ0ksOEJBQVU7Ozs7OztJQUFqQixVQUFrQixJQUFJLEVBQUUsS0FBSztRQUN6QixJQUFJOztnQkFDTSxnQkFBZ0Isd0JBQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBRXBGLElBQUksS0FBSyxLQUFLLENBQUMsSUFBSSxnQkFBZ0IsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO2dCQUM5QyxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDOUI7aUJBQU0sSUFBSSxLQUFLLEdBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDLE1BQU0sRUFBRTs7b0JBQ25DLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLHdCQUF3QixDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQzs7b0JBQzVFLFNBQVMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLGVBQWUsQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDO2dCQUM5RCxrRkFBa0Y7Z0JBQ2xGLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUNoQyxDQUFDLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUN2RixDQUFDLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO2dCQUV0RDs7Ozs7O3lCQU1TO2dCQUVULE9BQU8sU0FBUyxDQUFDLFFBQVEsQ0FBQzthQUM3QjtpQkFBTTtnQkFDSCxPQUFPLENBQUMsS0FBSyxDQUFDLDhFQUE4RSxDQUFDLENBQUM7YUFDakc7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUMsS0FBSyxDQUFDLENBQUM7WUFFdkIsTUFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDO0lBRUQ7OztPQUdHOzs7Ozs7SUFDSSwrQkFBVzs7Ozs7SUFBbEIsVUFBbUIsY0FBYztRQUM3QixJQUFJOztnQkFDSSxTQUFTOztnQkFDVCxjQUFjLEdBQUcsQ0FBQyxDQUFDO1lBQ3ZCLGVBQWU7WUFDZixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7Z0JBQ3pELElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxRQUFRLEtBQUssY0FBYyxFQUFFO29CQUNwRCxjQUFjLEdBQUcsS0FBSyxDQUFDO29CQUN2QixTQUFTLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDdEM7YUFDSjtZQUNELGNBQWM7WUFDZCxJQUFJLGNBQWMsS0FBSyxDQUFDLENBQUMsRUFBRTtnQkFDdkIsK0NBQStDO2dCQUMvQyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO2dCQUMzRCxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQzFDLENBQUMsQ0FBQyxHQUFHLEdBQUUsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxjQUFjLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQzthQUN4RDtpQkFBTTtnQkFDSCxJQUFJLGNBQWMsSUFBSSxTQUFTLElBQUksQ0FBQyxjQUFjLENBQUMsRUFBRSxFQUFFO29CQUNuRCxDQUFDLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztpQkFDbkQ7cUJBQU07b0JBQ0gsQ0FBQyxDQUFDLEdBQUcsR0FBRSxDQUFDLENBQUMsY0FBYyxDQUFDLGNBQWMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sRUFBRSxDQUFDO2lCQUN4RDthQUNKO1NBRUo7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE1BQU0sSUFBSSxLQUFLLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQyxDQUFDO1NBQ3RDO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRzs7Ozs7O0lBQ0kscUNBQWlCOzs7OztJQUF4QjtRQUNJLElBQUk7OztnQkFFSSxRQUFRLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFO1lBQzFFLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxRQUFRLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO2dCQUNsRCxDQUFDLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUM7YUFDL0I7WUFDRCxJQUFJLENBQUMsVUFBVSxHQUFHLEVBQUUsQ0FBQztTQUV4QjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osTUFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDO0lBR0Q7OztPQUdHOzs7Ozs7SUFDSSwrQkFBVzs7Ozs7SUFBbEI7UUFDSSxJQUFJOztnQkFDSSxTQUFTLEdBQUcsQ0FBQyxDQUFDOztnQkFDZCxZQUFZLEdBQUcsSUFBSSxLQUFLLEVBQUU7O2dCQUMxQixRQUFRLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFO1lBQzFFLG9CQUFvQjtZQUNwQixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsUUFBUSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtnQkFFbEQsaUNBQWlDO2dCQUNqQyxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUzs7OztnQkFBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQXpDLENBQXlDLEVBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO29CQUMvRixZQUFZLENBQUMsSUFBSSxDQUFDO3dCQUNkLFNBQVMsRUFBQyxJQUFJLENBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxRQUFRO3dCQUN0RCxFQUFFLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBRTt3QkFDMUMsU0FBUyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLENBQUMsUUFBUTtxQkFDakQsQ0FBQyxDQUFDO2lCQUNOO3FCQUFNO29CQUNILHVCQUF1QjtvQkFDdkIsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFFLFNBQVMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQzt3QkFDcEUsRUFBRSxFQUFFLENBQUMsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFO3FCQUMvQixDQUFDLENBQUM7aUJBQ047YUFFSjtZQUNELE9BQU8sWUFBWSxDQUFDO1NBQ3ZCO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixNQUFNLElBQUksS0FBSyxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUMsQ0FBQztTQUN0QztJQUNMLENBQUM7SUFFRDs7T0FFRzs7Ozs7O0lBQ0ksNEJBQVE7Ozs7O0lBQWYsVUFBZ0IsY0FBYztRQUMxQixJQUFJOztnQkFDSSxRQUFRLEdBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRTs7Z0JBQzdCLEtBQUssR0FBRyxDQUFDLGNBQWMsSUFBSSxTQUFTLElBQUksUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLElBQUk7Ozs7WUFBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxFQUFFLElBQUksY0FBYyxDQUFDLEVBQUUsRUFBekIsQ0FBeUIsRUFBQyxDQUFDLENBQUMsQ0FBQyxJQUFJOztnQkFDbkgsT0FBTyxHQUFZLEtBQUssSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsS0FBSztZQUVuRCxPQUFPLE9BQU8sQ0FBQztTQUVsQjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osTUFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDO0lBRUQ7OztPQUdHOzs7Ozs7SUFDSSw4QkFBVTs7Ozs7SUFBakIsVUFBa0IsR0FBUTs7WUFFbEIsWUFBWSxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUU7UUFDckMsSUFBSTtZQUNBLElBQUksR0FBRyxHQUFHLFlBQVksQ0FBQyxNQUFNLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxJQUFJLFlBQVksSUFBSSxTQUFTLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxHQUFHLENBQUMsSUFBSSxJQUFJLElBQUksWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFJLFNBQVMsQ0FBQyxFQUFFO2dCQUNuSixPQUFPLFlBQVksQ0FBQyxHQUFHLENBQUMsQ0FBQzthQUM1QjtZQUNELE9BQU8sU0FBUyxDQUFDO1NBQ3BCO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixNQUFNLElBQUksS0FBSyxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUMsQ0FBQztTQUN0QztJQUNMLENBQUM7SUFFRDs7T0FFRzs7Ozs7O0lBQ0ksa0NBQWM7Ozs7O0lBQXJCLFVBQXNCLElBQVM7O1lBQ3ZCLFlBQVksR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFO1FBQ3JDLElBQUk7O2dCQUNJLGNBQWMsR0FBRyxDQUFDLFlBQVksSUFBSSxTQUFTLElBQUksWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLElBQUk7Ozs7WUFBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxFQUFFLElBQUksSUFBSSxFQUFaLENBQVksRUFBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1lBQzlILE9BQU8sY0FBYyxDQUFDO1NBQ3pCO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixNQUFNLElBQUksS0FBSyxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUMsQ0FBQztTQUN0QztJQUNMLENBQUM7SUFFRDs7T0FFRzs7Ozs7O0lBQ0ksaUNBQWE7Ozs7O0lBQXBCLFVBQXFCLEtBQVU7O1lBRXZCLFlBQVksR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFO1FBQ3JDLElBQUk7WUFDQSxJQUFJLFlBQVksSUFBSSxTQUFTLElBQUksWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksS0FBSyxHQUFHLFlBQVksQ0FBQyxNQUFNLEVBQUU7O29CQUNqRixjQUFjLEdBQUcsWUFBWSxDQUFDLEtBQUssQ0FBQztnQkFDeEMsSUFBSSxjQUFjLElBQUksU0FBUyxFQUFFO29CQUM3QixJQUFJLGNBQWMsQ0FBQyxTQUFTLElBQUksU0FBUyxFQUFFO3dCQUN2QyxJQUFJLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxTQUFTLENBQUMsQ0FBQztxQkFDOUM7eUJBQU0sSUFBSSxjQUFjLENBQUMsRUFBRSxJQUFJLFNBQVMsRUFBRTs7NEJBQ25DLEtBQUssR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLGNBQWMsQ0FBQyxFQUFFLENBQUM7d0JBQ2xELElBQUksS0FBSyxJQUFJLFNBQVMsSUFBSSxLQUFLLElBQUksSUFBSSxFQUFFOzRCQUNyQyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFDO3lCQUMzQjtxQkFDSjtpQkFDSjthQUNKO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE1BQU0sSUFBSSxLQUFLLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQyxDQUFDO1NBQ3RDO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRzs7Ozs7O0lBQ0ksaUNBQWE7Ozs7O0lBQXBCLFVBQXFCLFNBQWM7O1lBQzNCLFlBQVksR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFO1FBQ3JDLElBQUk7O2dCQUNJLEtBQUssR0FBRyxDQUFDLENBQUM7WUFDZCxJQUFJLFNBQVMsSUFBSSxTQUFTLElBQUksU0FBUyxJQUFJLElBQUksRUFBRTtnQkFDN0MsS0FBSyxHQUFHLFlBQVksQ0FBQyxTQUFTOzs7O2dCQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLElBQUksU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLFNBQVMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUF0RCxDQUFzRCxFQUFDLENBQUM7YUFDL0Y7WUFDRCxPQUFPLEtBQUssQ0FBQztTQUNoQjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osTUFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDO0lBRUQ7OztPQUdHOzs7Ozs7SUFDSSx3QkFBSTs7Ozs7SUFBWCxVQUFZLEdBQVc7UUFBdkIsbUJBZUM7O1lBZFMsU0FBUyxHQUFHLEdBQUcsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUc7O1lBQ3RELFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFNBQVM7YUFDbkMsSUFBSTs7OztRQUFDLFVBQUMsQ0FBQyxJQUFLLE9BQUEsQ0FBQyxDQUFDLElBQUksS0FBSyxTQUFTLEVBQXBCLENBQW9CLEVBQUM7O1lBQ2hDLENBQUMsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQzthQUNyRCxJQUFJOzs7O1FBQUMsVUFBQyxlQUFlOztnQkFDWixTQUFTLEdBQUcsZUFBZSxDQUFDLE1BQU0sQ0FBQyxPQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQzs7O2dCQUUxRCxvQkFBb0IsR0FBRyxTQUFTLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxTQUFTLENBQUM7WUFDOUQsdUVBQXVFO1lBQ3ZFLGlDQUFpQztZQUNqQyxPQUFPLFNBQVMsQ0FBQyx3QkFBd0IsQ0FBQyx1QkFBdUIsQ0FBTSxvQkFBb0IsQ0FBQyxDQUFDO1FBQ2pHLENBQUMsRUFBQztRQUVOLE9BQU8sV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQzFCLENBQUM7SUFFRDs7T0FFRzs7Ozs7SUFDSSx5QkFBSzs7OztJQUFaO1FBQ0ksSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUM1QixDQUFDO0lBRUQ7OztPQUdHOzs7Ozs7SUFDSSw0QkFBUTs7Ozs7SUFBZjtRQUNJLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztJQUMxQixDQUFDOzs7O0lBRU0sMEJBQU07OztJQUFiO1FBQ0ksSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7UUFDdkIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUM1QixDQUFDO0lBSUQ7Ozs7T0FJRzs7Ozs7Ozs7SUFDSSw0QkFBUTs7Ozs7OztJQUFmLFVBQWlCLElBQVMsRUFBRSxLQUFVLEVBQUcsSUFBVTtRQUMvQyxJQUFJO1lBQ0EsSUFBRyxDQUFDLElBQUksSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsRUFBQztnQkFDckMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxHQUFHLEtBQUssQ0FBQzthQUNwRDtpQkFBSyxJQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBQztnQkFDaEIsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBRyxLQUFLLENBQUM7YUFDbEM7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTyxDQUFDLEtBQUssQ0FBQyw2QkFBNkIsRUFBRyxLQUFLLENBQUMsQ0FBQztTQUN4RDtJQUNMLENBQUM7SUFFRDs7O09BR0c7Ozs7OztJQUNJLDhCQUFVOzs7OztJQUFqQixVQUFrQixVQUFtQjtRQUNqQyxJQUFJO1lBQ0EsSUFBSSxDQUFDLE9BQU8sR0FBRSxVQUFVLENBQUM7U0FDNUI7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLEVBQUcsS0FBSyxDQUFDLENBQUM7U0FDOUM7SUFDTCxDQUFDOzs7OztJQUdNLCtCQUFXOzs7O0lBQWxCLFVBQW1CLEtBQVU7UUFDekIsSUFBSSxJQUFJLENBQUMsYUFBYSxJQUFJLEtBQUssRUFBRTtZQUM3QixJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7U0FDL0Q7YUFDSTtZQUNELElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztTQUM3RDtJQUNMLENBQUM7Ozs7O0lBRU0sc0NBQWtCOzs7O0lBQXpCLFVBQTBCLEtBQUs7UUFDM0IsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUM7UUFDM0IsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUM1QixDQUFDOzs7O0lBRU0seUJBQUs7OztJQUFaO1FBQ0ksVUFBVTtJQUNkLENBQUM7Ozs7O0lBRU0sb0NBQWdCOzs7O0lBQXZCLFVBQXdCLElBQUk7UUFDeEIsSUFBRyxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksRUFBSTtZQUN4QixJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDOztnQkFDakIsT0FBTyxHQUFHLElBQUksV0FBVyxDQUFDLElBQUksRUFBRyxVQUFVLENBQUMsT0FBTyxDQUFDLEdBQUcsR0FBQyxJQUFJLENBQUMsUUFBUSxHQUFDLEdBQUcsQ0FBQyxDQUFDOztnQkFDN0UsY0FBYyxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxHQUFHLEdBQUc7WUFDOUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLENBQUUsQ0FBQyxJQUFJLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxDQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUcsQ0FBQztTQUN4STtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7Ozs7O0lBRU0sbUNBQWU7Ozs7SUFBdEIsVUFBdUIsSUFBSTs7WUFDakIsT0FBTyxHQUFHLElBQUksV0FBVyxDQUFDLElBQUksRUFBRyxVQUFVLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUMzRSxJQUFHLENBQUMsT0FBTyxDQUFDLEtBQUssRUFBQztZQUNkLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1NBQ3ZDO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQztJQUlEOztPQUVHOzs7OztJQUNILCtCQUFXOzs7O0lBQVg7UUFDSSxPQUFPLENBQUMsR0FBRyxDQUFDLHlFQUF5RSxDQUFDLENBQUE7UUFDdEYsSUFBRztZQUNDLElBQUksQ0FBQyw4QkFBOEIsQ0FBRSxJQUFJLENBQUMsWUFBWSxDQUFFLENBQUM7WUFFekQsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDO1lBQ3RCLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztZQUNyQixPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7WUFDdkIsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ3hCLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztZQUMxQixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7WUFDdEIsT0FBTyxJQUFJLENBQUMsa0JBQWtCLENBQUM7WUFDL0IsT0FBTyxJQUFJLENBQUMsVUFBVSxDQUFDO1lBQ3ZCLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztZQUN2QixPQUFPLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDekIsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1lBQzFCLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQztZQUM1QixPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7WUFDM0IsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO1lBQzNCLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztZQUN6QixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUM7WUFDMUIsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ3hCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztZQUNwQixPQUFPLElBQUksQ0FBQyxRQUFRLENBQUM7WUFDckIsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDO1lBQ25CLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztZQUNwQixPQUFPLElBQUksQ0FBQyxlQUFlLENBQUc7WUFDOUIsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFHO1lBQzVCLE9BQU8sSUFBSSxDQUFDLGlCQUFpQixDQUFFO1lBQy9CLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBRTtZQUM3QixPQUFPLElBQUksQ0FBQyxLQUFLLENBQUM7U0FDckI7UUFBQSxPQUFNLEtBQUssRUFBRTtZQUNWLE9BQU8sQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ2pDO0lBQ0wsQ0FBQzs7Z0JBL21DSixTQUFTLFNBQUM7b0JBQ1AsUUFBUSxFQUFFLFdBQVc7b0JBQ3JCLFFBQVEsRUFBRSxVQUNUO2lCQUVKOzs7O2dCQXpCNEQsVUFBVTtnQkFDL0QsYUFBYTs7OzZCQTRDaEIsU0FBUyxTQUFDLFlBQVksRUFBRSxFQUFDLElBQUksRUFBRSxnQkFBZ0IsRUFBQztvQ0F3RGhELEtBQUssU0FBQyxtQkFBbUI7K0JBWXpCLEtBQUssU0FBQyxjQUFjO2tDQVlwQixLQUFLLFNBQUMsaUJBQWlCOzhCQVl2QixLQUFLLFNBQUMsYUFBYTs4QkFZbkIsS0FBSyxTQUFDLGFBQWE7a0NBV25CLEtBQUssU0FBQyxpQkFBaUI7d0JBWXZCLEtBQUssU0FBQyxPQUFPO3VCQWdCYixLQUFLLFNBQUMsTUFBTTt5QkFVWixLQUFLO3NCQVNMLEtBQUs7Z0NBU0wsS0FBSyxTQUFDLGVBQWU7OEJBVXJCLEtBQUssU0FBQyxhQUFhOzRCQVNuQixLQUFLLFNBQUMsV0FBVzswQkF5QmpCLEtBQUssU0FBQyxTQUFTO3VDQW1CWixLQUFLLFNBQUMsc0JBQXNCO21DQXFDL0IsS0FBSyxTQUFDLHFCQUFxQjt1QkFPM0IsS0FBSyxTQUFDLE1BQU07NEJBWVosS0FBSyxTQUFDLFdBQVc7a0NBU2pCLEtBQUs7Z0NBZ0JMLEtBQUs7d0JBZ0JMLEtBQUssU0FBQyxPQUFPO2dDQTJCYixLQUFLLFNBQUMsZUFBZTt5QkFtQnJCLEtBQUssU0FBQyxRQUFROzRCQTZCZCxLQUFLLFNBQUMsV0FBVzsyQkFXakIsS0FBSyxTQUFDLFVBQVU7NEJBVWhCLEtBQUssU0FBQyxXQUFXOzJCQVdqQixLQUFLLFNBQUMsVUFBVTtrQ0FXaEIsS0FBSzswQkEwQkwsS0FBSzswQkF1QkwsS0FBSzs2QkFnQ0wsS0FBSztnQ0F1QkwsS0FBSzs4QkFzQkwsS0FBSzsrQkFzQkwsS0FBSzs0QkF1QkwsS0FBSzsrQkFhTCxLQUFLOzZCQWFMLEtBQUs7OEJBYUwsS0FBSzsyQkFjTCxLQUFLLFNBQUMsVUFBVTs7SUF1YnJCLGdCQUFDO0NBQUEsQUFqbkNELENBTytCLFdBQVcsR0EwbUN6QztTQTFtQ1ksU0FBUzs7O0lBa0JsQiwrQkFBZ0Y7Ozs7O0lBRWhGLGlDQUF3Qjs7Ozs7SUFDeEIsZ0NBQXdCOzs7OztJQUV4QixpQ0FBOEI7Ozs7O0lBQzlCLHNCQUFpQjs7Ozs7SUFDakIsc0JBQWlCOzs7OztJQUVqQixvQ0FBa0Q7Ozs7O0lBQ2xELGtDQUFnRDs7SUFFaEQsK0JBQXVCOztJQUN2QixrQ0FBMEI7O0lBQzFCLDhCQUF1Qjs7Ozs7SUFDdkIsdUNBQXVDOzs7OztJQUN2QywrQkFBK0I7Ozs7O0lBQy9CLCtCQUErQjs7Ozs7SUFDL0IsMEJBQW1COzs7OztJQUNuQiw2QkFBeUI7Ozs7O0lBQ3pCLDBDQUF3Qzs7Ozs7SUFFeEMsaUNBQWlDOzs7OztJQUNqQyxrQ0FBaUM7Ozs7O0lBQ2pDLG1DQUFpQzs7Ozs7SUFDakMsb0NBQWlDOzs7OztJQUVqQyxtQ0FBNEI7Ozs7O0lBQzVCLGlDQUEwQjs7Ozs7SUFDMUIsa0NBQTJCOzs7OztJQUMzQixnQ0FBeUI7Ozs7O0lBRXpCLHNDQUFnQzs7Ozs7SUFDaEMsb0NBQTZCOzs7OztJQUU3Qiw0QkFBb0I7Ozs7O0lBQ3BCLDhCQUFzQjs7Ozs7SUFDdEIsOEJBQXNCOzs7OztJQUN0Qiw2QkFBcUI7Ozs7O0lBQ3JCLCtCQUF1Qjs7Ozs7SUFDdkIsK0JBQXVCOzs7OztJQUV2Qiw4QkFBMEI7Ozs7O0lBQzFCLGdDQUFxQjs7Ozs7SUFDckIsb0NBQWtDOzs7OztJQUNsQyxrQ0FBZ0M7Ozs7O0lBQ2hDLDRCQUEwQjs7Ozs7SUFDMUIsMkJBQXlCOzs7OztJQUN6QixtQ0FBNEI7Ozs7O0lBQzVCLGtDQUEyQjs7Ozs7SUFDM0Isc0NBQStCOzs7OztJQUMvQixrQ0FBMkI7Ozs7O0lBQzNCLHNDQUErQjs7Ozs7SUFDL0Isd0NBQXlDOzs7OztJQStTekMsbUNBQStCOztJQTZUL0IsNkJBQTRCOzs7OztJQUloQiw4QkFBNkI7Ozs7O0lBQUUsNkJBQStCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtDb21wb25lbnQsSG9zdEJpbmRpbmcsIE9uSW5pdCxSZW5kZXJlcjIsICBPbkRlc3Ryb3ksIEVsZW1lbnRSZWYsIElucHV0LCBWaWV3Q2hpbGQsIFZpZXdDb250YWluZXJSZWYsIEV2ZW50RW1pdHRlciwgSG9zdExpc3RlbmVyLCBBZnRlclZpZXdJbml0IH0gZnJvbSBcIkBhbmd1bGFyL2NvcmVcIjtcclxuaW1wb3J0IHtDb21tb25TZXJ2aWNlfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHtmcm9tUHJvbWlzZX0gZnJvbSBcInJ4anMtY29tcGF0L29ic2VydmFibGUvZnJvbVByb21pc2VcIjtcclxuaW1wb3J0IHtVSUNvbXBvbmVudH0gZnJvbSBcIi4uL2NvbnRyb2xzL1VJQ29tcG9uZW50LnNlcnZpY2VcIjtcclxuaW1wb3J0IHtmb2N1c01hbmFnZXJ9IGZyb20gXCIuLi9tYW5hZ2Vycy9mb2N1cy1tYW5hZ2VyLnNlcnZpY2VcIjtcclxuaW1wb3J0IHsgT2JzZXJ2YWJsZSB9IGZyb20gXCJyeGpzL2ludGVybmFsL09ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgZ2VuZXJpY0V2ZW50IH0gZnJvbSBcIi4uL2V2ZW50cy9zd3QtZXZlbnRzLm1vZHVsZVwiO1xyXG5pbXBvcnQgeyBGb3JtQ29udHJvbCwgVmFsaWRhdG9ycyB9IGZyb20gXCJAYW5ndWxhci9mb3Jtc1wiO1xyXG5pbXBvcnQgeyBTd3RVdGlsIH0gZnJvbSAnLi4vdXRpbHMvc3d0LXV0aWwuc2VydmljZSc7XHJcbmltcG9ydCB7IFN0cmluZ1V0aWxzIH0gZnJvbSAnLi4vdXRpbHMvc3RyaW5nLXV0aWxzLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBUaW1lciB9IGZyb20gJy4uL3V0aWxzL3RpbWVyLnNlcnZpY2UnO1xyXG5cclxuLy9pbXBvcnQgJCBmcm9tICdqcXVlcnknO1xyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbmNvbnN0ICQgPSByZXF1aXJlKCdqcXVlcnknKTtcclxuXHJcbi8qKlxyXG4gKiBAYXV0aCA6IFJpaGFiIEpBQkFMTEFIIGNyZWF0ZWQgb24gSnVuZS0yMDE4XHJcbiAqL1xyXG5cclxuQENvbXBvbmVudCh7XHJcbiAgICBzZWxlY3RvcjogJ0NvbnRhaW5lcicsXHJcbiAgICB0ZW1wbGF0ZTogYCAgXHJcbiAgICBgLFxyXG4gICAgc3R5bGVzOiBbXSxcclxufSlcclxuLy8gQEF1dG9VbnN1YnNjcmliZVxyXG5leHBvcnQgY2xhc3MgQ29udGFpbmVyIGV4dGVuZHMgVUlDb21wb25lbnQgaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQsIE9uRGVzdHJveSB7XHJcbiAgICBuZ0FmdGVyVmlld0luaXQoKTogdm9pZCB7XHJcbiAgICAgICAgIC8vLUFkZCBUb29sVGlwLlxyXG4gICAgICAgICBpZih0aGlzLl90b29sVGlwICYmICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkubGVuZ3RoID4wKXtcclxuICAgICAgICAgICAgJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdKS5hdHRyKFwidGl0bGVcIiwgdGhpcy5fdG9vbFRpcCk7XHJcbiAgICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7XHJcblxyXG4gICAgICAgICAgICAkKHRoaXMuZG9tRWxlbWVudCkudG9vbHRpcCh7XHJcbiAgICAgICAgICAgICAgICBzaG93OiB7IGR1cmF0aW9uOiA4MDAsIGRlbGF5IDogNTAwIH0sXHJcbiAgICAgICAgICAgICAgICBvcGVuOiBmdW5jdGlvbiggZXZlbnQsIHVpICkge1xyXG4gICAgICAgICAgICAgICAgICAgICQoJChfdGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLnJlbW92ZUF0dHIoJ3RpdGxlJyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcblxyXG4gICAgLy8tLS1Qcm9wZXJ0aWVzIGRlZmluaXRpb25zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBAVmlld0NoaWxkKCdfY29udGFpbmVyJywge3JlYWQ6IFZpZXdDb250YWluZXJSZWZ9KSBfY29udGFpbmVyOiBWaWV3Q29udGFpbmVyUmVmO1xyXG5cclxuICAgIHByaXZhdGUgX2hvc3RFbGVtZW50OmFueVxyXG4gICAgcHJpdmF0ZSBfZG9tRWxlbWVudDphbnk7XHJcblxyXG4gICAgcHJpdmF0ZSBpc0NhbmNlbFVzZWQ6IGJvb2xlYW47XHJcbiAgICBwcml2YXRlIFg6IGFueT0wO1xyXG4gICAgcHJpdmF0ZSBZOiBhbnk9MDtcclxuXHJcbiAgICBwcml2YXRlIHVybExvYWRpbmdTdGFydCA9IG5ldyBFdmVudEVtaXR0ZXI8YW55PigpO1xyXG4gICAgcHJpdmF0ZSB1cmxMb2FkaW5nRW5kID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcblxyXG4gICAgcHVibGljIGNvbXBvbmVudHMgPSBbXTsvLyBLZWVwIHRyYWNrIG9mIGxpc3Qgb2YgZ2VuZXJhdGVkIGNvbXBvbmVudHMgZm9yIHJlbW92YWwgcHVycG9zZXNcclxuICAgIHB1YmxpYyBvcmlnaW5hbFZhbHVlOiBhbnk7XHJcbiAgICBwdWJsaWMgZmlyc3RDYWxsID10cnVlO1xyXG4gICAgcHJpdmF0ZSBfSXNJbmNsdWRlSW5MYXlvdXQ6IGFueSA9IHRydWU7XHJcbiAgICBwcml2YXRlIF9pc1Zpc2libGU6IGFueSA9IHRydWU7XHJcbiAgICBwcml2YXRlIF9pc0VuYWJsZWQ6IGFueSA9IHRydWU7XHJcbiAgICBwcml2YXRlIF9uYW1lOiBhbnk7XHJcbiAgICBwcml2YXRlIF90b29sVGlwOiBhbnk9XCJcIjtcclxuICAgIHByaXZhdGUgX3Rvb2xUaXBQcmV2aW91c1ZhbHVlOmFueT0gbnVsbDtcclxuXHJcbiAgICBwcml2YXRlIF9fcGFkZGluZ1RvcDphbnkgICAgPSAwIDtcclxuICAgIHByaXZhdGUgX19wYWRkaW5nTGVmdDphbnkgICA9IDAgO1xyXG4gICAgcHJpdmF0ZSBfX3BhZGRpbmdSaWdodDphbnkgID0gMCA7XHJcbiAgICBwcml2YXRlIF9fcGFkZGluZ0JvdHRvbTphbnkgPSAwIDtcclxuXHJcbiAgICBwcml2YXRlIF9fbWFyZ2luQm90dG9tOmFueSA7XHJcbiAgICBwcml2YXRlIF9fbWFyZ2luTGVmdDphbnkgO1xyXG4gICAgcHJpdmF0ZSBfX21hcmdpblJpZ2h0OmFueSA7XHJcbiAgICBwcml2YXRlIF9fbWFyZ2luVG9wOmFueSA7XHJcblxyXG4gICAgcHJpdmF0ZSBfX2hvcml6b250YWxBbGlnbjphbnkgIDtcclxuICAgIHByaXZhdGUgX192ZXJ0aWNhbEFsaWduOmFueSA7XHJcblxyXG4gICAgcHJpdmF0ZSBfX3dpZHRoOmFueTtcclxuICAgIHByaXZhdGUgX21pbldpZHRoOmFueTtcclxuICAgIHByaXZhdGUgX21heFdpZHRoOmFueTtcclxuICAgIHByaXZhdGUgX19oZWlnaHQ6YW55O1xyXG4gICAgcHJpdmF0ZSBfbWluSGVpZ2h0OmFueTtcclxuICAgIHByaXZhdGUgX21heEhlaWdodDphbnk7XHJcblxyXG4gICAgcHJpdmF0ZSBfaXNsb2FkZWQgPSBmYWxzZTtcclxuICAgIHByaXZhdGUgX19zdHlsZU5hbWUgO1xyXG4gICAgcHJpdmF0ZSBfX2hvcml6b250YWxHYXA6YW55ID1cIjBcIiA7XHJcbiAgICBwcml2YXRlIF9fdmVydGljYWxHYXA6YW55ID1cIjBcIiA7XHJcbiAgICBwcml2YXRlIF9fcmlnaHQ6YW55ID1cIjBcIiA7XHJcbiAgICBwcml2YXRlIF9fbGVmdDphbnkgPVwiMFwiIDtcclxuICAgIHByaXZhdGUgX19jb3JuZXJSYWRpdXM6YW55IDtcclxuICAgIHByaXZhdGUgX19ib3JkZXJDb2xvcjphbnkgO1xyXG4gICAgcHJpdmF0ZSBfX2JhY2tHcm91bmRDb2xvcjphbnkgO1xyXG4gICAgcHJpdmF0ZSBfX2JvcmRlclN0eWxlOmFueSA7XHJcbiAgICBwcml2YXRlIF9fYm9yZGVyVGhpY2tuZXNzOmFueSA7XHJcbiAgICBwcml2YXRlIF9fZHJvcFNoYWRvd0VuYWJsZWQ6YW55ID1cInRydWVcIiA7XHJcblxyXG4gICAgLy8tLS1kcm9wU2hhZG93RW5hYmxlZCAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnZHJvcFNoYWRvd0VuYWJsZWQnKVxyXG4gICAgc2V0IGRyb3BTaGFkb3dFbmFibGVkKHZhbHVlOmFueSl7XHJcbiAgICAgICAgdGhpcy5fX2Ryb3BTaGFkb3dFbmFibGVkID10aGlzLmFkYXB0VmFsdWVBc0Jvb2xlYW4odmFsdWUpO1xyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdib3gtc2hhZG93JywgdGhpcy5fX2Ryb3BTaGFkb3dFbmFibGVkID8gJzAgNHB4IDVweCAwIHJnYmEoMCwgMCwgMCwgMC42KSwgMCAwcHggMHB4IDAgcmdiYSgwLCAwLCAwLCAwLjUpJyA6ICdub25lJyAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICAgICAgfSwgMCk7XHJcbiAgICB9XHJcbiAgICBnZXQgZHJvcFNoYWRvd0VuYWJsZWQoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX2Ryb3BTaGFkb3dFbmFibGVkO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tY29ybmVyUmFkaXVzLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ2Nvcm5lclJhZGl1cycpXHJcbiAgICBzZXQgY29ybmVyUmFkaXVzKHZhbHVlOmFueSl7XHJcbiAgICAgICAgdGhpcy5fX2Nvcm5lclJhZGl1cyA9dGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdib3JkZXItcmFkaXVzJywgdGhpcy5fX2Nvcm5lclJhZGl1cyAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICAgICAgfSwgMCk7XHJcbiAgICB9XHJcbiAgICBnZXQgY29ybmVyUmFkaXVzKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19jb3JuZXJSYWRpdXM7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1ib3JkZXJUaGlja25lc3MtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnYm9yZGVyVGhpY2tuZXNzJylcclxuICAgIHNldCBib3JkZXJUaGlja25lc3ModmFsdWU6c3RyaW5nKXtcclxuICAgICAgICB0aGlzLl9fYm9yZGVyVGhpY2tuZXNzID0gdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdib3JkZXItd2lkdGgnLCB0aGlzLl9fYm9yZGVyVGhpY2tuZXNzICAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICAgICAgfSwgMCk7XHJcbiAgICB9XHJcbiAgICBnZXQgYm9yZGVyVGhpY2tuZXNzKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19ib3JkZXJUaGlja25lc3M7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1ib3JkZXJTdHlsZS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCdib3JkZXJTdHlsZScpXHJcbiAgICBzZXQgYm9yZGVyU3R5bGUodmFsdWU6c3RyaW5nKXtcclxuICAgICAgICB0aGlzLl9fYm9yZGVyU3R5bGUgPXZhbHVlO1xyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdib3JkZXItc3R5bGUnLCB2YWx1ZSAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICAgICAgfSwgMCk7XHJcbiAgICB9XHJcbiAgICBnZXQgYm9yZGVyU3R5bGUoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX2JvcmRlclN0eWxlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tYm9yZGVyQ29sb3ItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnYm9yZGVyQ29sb3InKVxyXG4gICAgc2V0IGJvcmRlckNvbG9yKHZhbHVlOnN0cmluZyl7XHJcbiAgICAgICAgdGhpcy5fX2JvcmRlckNvbG9yID12YWx1ZTtcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgnYm9yZGVyLWNvbG9yJywgdmFsdWUgICx0aGlzLmRvbUVsZW1lbnQpO1xyXG4gICAgICAgIH0sIDApO1xyXG4gICAgfVxyXG4gICAgZ2V0IGJvcmRlckNvbG9yKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19ib3JkZXJDb2xvcjtcclxuICAgIH1cclxuICAgIC8vLS0tYm9yZGVyQ29sb3ItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnYmFja0dyb3VuZENvbG9yJylcclxuICAgIHNldCBiYWNrR3JvdW5kQ29sb3IodmFsdWU6c3RyaW5nKXtcclxuICAgICAgICB0aGlzLl9fYmFja0dyb3VuZENvbG9yID12YWx1ZTtcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgnYmFja2dyb3VuZC1jb2xvcicsIHZhbHVlICAsdGhpcy5kb21FbGVtZW50KTtcclxuICAgICAgICB9LCAwKTtcclxuICAgIH1cclxuICAgIGdldCBiYWNrR3JvdW5kQ29sb3IoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX2JhY2tHcm91bmRDb2xvcjtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLXJpZ2h0LS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ3JpZ2h0JylcclxuICAgIHNldCByaWdodCh2YWx1ZTphbnkpe1xyXG4gICAgICAgIGlmKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpe1xyXG4gICAgICAgICAgICB2YXIgcGFyZW50V2lkdGggPSAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkucGFyZW50KCkpLndpZHRoKCk7XHJcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgnZGlzcGxheScgLCAnZmxleCcpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgncG9zaXRpb24nICwgJ3N0aWNreScpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fX3JpZ2h0ID0gdGhpcy5hZGFwdFVuaXQoICBOdW1iZXIocGFyZW50V2lkdGgpIC0gTnVtYmVyKHZhbHVlKSAtICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uc2Nyb2xsV2lkdGggLCBcImF1dG9cIik7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdsZWZ0JyAsIHRoaXMuX19yaWdodCAgKTtcclxuICAgICAgICAgICAgfSwgMCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0IHJpZ2h0KCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19yaWdodDtcclxuICAgIH1cclxuICAgIC8vLS0tTGVmdC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCdsZWZ0JylcclxuICAgIHNldCBsZWZ0KHZhbHVlOmFueSl7XHJcblxyXG4gICAgICAgIHRoaXMucGFkZGluZ0xlZnQ9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgZ2V0IGxlZnQoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX2xlZnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1ib3R0b20tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IGJvdHRvbSh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5wYWRkaW5nQm90dG9tID12YWx1ZTtcclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgYm90dG9tICgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9fcGFkZGluZ0JvdHRvbTtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLXRvcC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KClcclxuICAgIHB1YmxpYyBzZXQgdG9wKHZhbHVlKXtcclxuICAgICAgICB0aGlzLnBhZGRpbmdUb3AgPXZhbHVlO1xyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCB0b3AgKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19wYWRkaW5nVG9wO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0taG9yaXpvbnRhbEdhcC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCdob3Jpem9udGFsR2FwJylcclxuICAgIHNldCBob3Jpem9udGFsR2FwKHZhbHVlOnN0cmluZyl7XHJcbiAgICAgICAgdGhpcy5fX2hvcml6b250YWxHYXAgPSB0aGlzLmFkYXB0VW5pdCh2YWx1ZSwgXCJhdXRvXCIpO1xyXG5cclxuICAgIH1cclxuICAgIGdldCBob3Jpem9udGFsR2FwKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19ob3Jpem9udGFsR2FwO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tdmVydGljYWxHYXAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgndmVydGljYWxHYXAnKVxyXG4gICAgc2V0IHZlcnRpY2FsR2FwKHZhbHVlOnN0cmluZyl7XHJcbiAgICAgICAgdGhpcy5fX3ZlcnRpY2FsR2FwID0gdGhpcy5hZGFwdFVuaXQodmFsdWUsIFwiYXV0b1wiKTtcclxuICAgIH1cclxuICAgIGdldCB2ZXJ0aWNhbEdhcCgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9fdmVydGljYWxHYXA7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1UZXh0QWxpZ24tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgndGV4dEFsaWduJylcclxuICAgIHNldCB0ZXh0QWxpZ24odmFsdWU6c3RyaW5nKXtcclxuICAgICAgICBpZiAodmFsdWUpIHtcclxuICAgICAgICAgICAgdGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5zdHlsZS50ZXh0QWxpZ24gPSB2YWx1ZTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICAvLy0tLUhvc3RFbGVtZW50LS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIGdldCBob3N0RWxlbWVudCgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9ob3N0RWxlbWVudDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLURvbUVsZW1lbnQtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgZ2V0IGRvbUVsZW1lbnQoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZG9tRWxlbWVudDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLVRhYkluZGV4LS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIHB1YmxpYyBhZGRUYWJJbmRleChlbGVtZW50IDphbnksIHZhbHVlIDphbnkpe1xyXG4gICAgICAgIGlmKCQoZWxlbWVudCkpICQoZWxlbWVudCkuYXR0cihcInRhYmluZGV4XCIsIFN0cmluZyh2YWx1ZSkpO1xyXG4gICAgfVxyXG4gICAgcHVibGljIHJlbW92ZVRhYkluZGV4KGVsZW1lbnQpe1xyXG4gICAgICAgIGlmKCQoZWxlbWVudCkpICQoZWxlbWVudCkuYXR0cihcInRhYmluZGV4XCIsIFwiLTFcIik7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1Ub29sVGlwLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ3Rvb2xUaXAnKVxyXG4gICAgcHVibGljIHNldCB0b29sVGlwKHZhbHVlOiBzdHJpbmcpe1xyXG4gICAgICAgIHRoaXMuX3Rvb2xUaXA9IHZhbHVlO1xyXG4gICAgICAgIGlmKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkpe1xyXG4gICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLnRvb2x0aXAoe1xyXG4gICAgICAgICAgICAgICAgc2hvdzogeyBkdXJhdGlvbjogODAwLCBkZWxheSA6IDUwMCB9LFxyXG4gICAgICAgICAgICAgICAgb3BlbjogZnVuY3Rpb24oIGV2ZW50LCB1aSApIHtcclxuICAgICAgICAgICAgICAgICAgICAkKHRoaXMpLnJlbW92ZUF0dHIoJ3RpdGxlJyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG5cclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgdG9vbFRpcCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdG9vbFRpcCA7XHJcbiAgICB9XHJcblxyXG5cclxuICAgICAgIC8vLS0tVG9vbFRpcC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgICAgQElucHV0KCd0b29sVGlwUHJldmlvdXNWYWx1ZScpXHJcbiAgICAgICBwdWJsaWMgc2V0IHRvb2xUaXBQcmV2aW91c1ZhbHVlKHZhbHVlOiBzdHJpbmcpe1xyXG4gICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+agCB+IGZpbGU6IHN3dC1jb250YWluZXIuY29tcG9uZW50LnRzOjI4MiB+IENvbnRhaW5lciB+IHNldHRvb2xUaXBQcmV2aW91c1ZhbHVlIH4gdmFsdWU6XCIsIHZhbHVlKVxyXG4gICAgICAgICAgIHRoaXMuX3Rvb2xUaXBQcmV2aW91c1ZhbHVlPSB2YWx1ZTtcclxuICAgICAgICAvLyAgICBpZigkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpKXtcclxuICAgICAgICAvLyAgICAgICAgJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdKS50b29sdGlwKHtcclxuICAgICAgICAvLyAgICAgICAgICAgIHNob3c6IHsgZHVyYXRpb246IDgwMCwgZGVsYXkgOiA1MDAgfSxcclxuICAgICAgICAvLyAgICAgICAgICAgIG9wZW46IGZ1bmN0aW9uKCBldmVudCwgdWkgKSB7XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgJCh0aGlzKS5yZW1vdmVBdHRyKCd0aXRsZScpO1xyXG4gICAgICAgIC8vICAgICAgICAgICAgfVxyXG4gICAgICAgIC8vICAgICAgICB9KVxyXG4gICAgICAgIC8vICAgIH1cclxuICAgICAgICAvLyB0aGlzLnJlbmRlcmVyLnNldEF0dHJpYnV0ZShkaXYsICdwb3BwZXInLCAnQXMgdGV4dCcpO1xyXG4gICAgICAgIC8vIHRoaXMucmVuZGVyZXIuc2V0QXR0cmlidXRlKGRpdiwgJ3BvcHBlclRyaWdnZXInLCAnaG92ZXInKTtcclxuICAgICAgICAvLyB0aGlzLnJlbmRlcmVyLnNldEF0dHJpYnV0ZShkaXYsICdwb3BwZXJQbGFjZW1lbnQnLCAnYm90dG9tJyk7XHJcbiAgICAgICAgaWYoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KSl7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiLS0tKipcIik7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoJ3BvcHBlcicsICdBcyB0ZXh0Jyk7XHJcbiAgICAgICAgICAgICQoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXSkuYXR0cigncG9wcGVyVHJpZ2dlcicsICdob3ZlcicpO1xyXG4gICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoJ3BvcHBlclBsYWNlbWVudCcsICdib3R0b20nKTtcclxuXHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgIH1cclxuICAgICAgICBpZighdGhpcy5lbmFibGVkKXtcclxuICAgICAgICAgICAgLy8gdGhpcy5hZGRBbGxPdXRwdXRzRXZlbnRzTGlzdGVuZXJzKCk7XHJcbiAgICAgICAgICAgICQoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5jaGlsZHJlblswXSkucmVtb3ZlQ2xhc3MoJ2Rpc2FibGVkLWNvbnRhaW5lcicpO1xyXG4gICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uY2hpbGRyZW5bMF0pLmFkZENsYXNzKCdkaXNhYmxlZC1jb250YWluZXItdG9vbHRpcCcpO1xyXG4gICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLnJlbW92ZUNsYXNzKCdkaXNhYmxlUG9pbnRlckV2ZW50cycpO1xyXG5cclxuICAgICAgICB9XHJcbiAgICAgICB9XHJcbiAgICAgICBwdWJsaWMgZ2V0IHRvb2xUaXBQcmV2aW91c1ZhbHVlKCkge1xyXG4gICAgICAgICAgIHJldHVybiB0aGlzLl90b29sVGlwUHJldmlvdXNWYWx1ZSA7XHJcbiAgICAgICB9XHJcblxyXG5cclxuICAgIEBJbnB1dCgndG9vbHRpcERpY3Rpb25hcnlJZCcpIHNldCB0ZXh0RGljdGlvbmFyeUlkKHZhbHVlKSB7XHJcbiAgICAgICAgaWYodmFsdWUpIHtcclxuICAgICAgICAgICAgdGhpcy5fdG9vbFRpcCA9IFN3dFV0aWwuZ2V0UHJlZGljdE1lc3NhZ2UodmFsdWUpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tTmFtZS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCduYW1lJylcclxuICAgIHB1YmxpYyBzZXQgbmFtZSh2YWx1ZTogc3RyaW5nKXtcclxuICAgICAgICAvLyBzZXQgbmFtZSB0byB0aGUgY29tcG9uZW50J3MgRE9NLlxyXG4gICAgICAgIHRoaXMuX25hbWU9IHZhbHVlO1xyXG4gICAgICAgIGlmKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkpXHJcbiAgICAgICAgICAgICQoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXSkuYXR0cihcIm5hbWVcIiwgdGhpcy5fbmFtZSk7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG5hbWUoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX25hbWU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1zdHlsZU5hbWUtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ3N0eWxlTmFtZScpXHJcbiAgICBzZXQgc3R5bGVOYW1lICh2YWx1ZTpzdHJpbmcpe1xyXG4gICAgICAgIHRoaXMuX19zdHlsZU5hbWU9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgZ2V0IHN0eWxlTmFtZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX3N0eWxlTmFtZVxyXG4gICAgfVxyXG5cclxuICAgIC8vLS0taG9yaXpvbnRhbEFsaWduLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KClcclxuICAgIHB1YmxpYyBzZXQgaG9yaXpvbnRhbEFsaWduKHZhbHVlKXtcclxuICAgICAgICBpZiAodmFsdWUgPT0gXCJjZW50ZXJcIikge1xyXG4gICAgICAgICAgICB0aGlzLl9faG9yaXpvbnRhbEFsaWduID0gXCJjZW50ZXJcIjtcclxuICAgICAgICB9IGVsc2UgaWYgKHZhbHVlID09IFwibGVmdFwiKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX19ob3Jpem9udGFsQWxpZ24gPSBcImZsZXgtc3RhcnRcIjtcclxuICAgICAgICB9IGVsc2UgaWYgKHZhbHVlID09IFwicmlnaHRcIikge1xyXG4gICAgICAgICAgICB0aGlzLl9faG9yaXpvbnRhbEFsaWduID0gXCJmbGV4LWVuZFwiO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0IGhvcml6b250YWxBbGlnbigpe1xyXG4gICAgICAgIHJldHVybiAgdGhpcy5fX2hvcml6b250YWxBbGlnbiA7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS12ZXJ0aWNhbEFsaWduLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCB2ZXJ0aWNhbEFsaWduKHZhbHVlKXtcclxuXHJcbiAgICAgICAgaWYgKHZhbHVlID09IFwibWlkZGxlXCIpXHJcbiAgICAgICAgICAgIHRoaXMuX192ZXJ0aWNhbEFsaWduID0gXCJjZW50ZXJcIjtcclxuICAgICAgICBlbHNlIGlmICh2YWx1ZSA9PSBcImJvdHRvbVwiKVxyXG4gICAgICAgICAgICB0aGlzLl9fdmVydGljYWxBbGlnbiA9IFwiZmxleC1lbmRcIjtcclxuICAgICAgICBlbHNlIGlmICh2YWx1ZSA9PSBcInRvcFwiKVxyXG4gICAgICAgICAgICB0aGlzLl9fdmVydGljYWxBbGlnbiA9IFwiZmxleC1zdGFydFwiO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXQgdmVydGljYWxBbGlnbigpe1xyXG4gICAgICAgIHJldHVybiAgdGhpcy5fX3ZlcnRpY2FsQWxpZ24gO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0td2lkdGgtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCd3aWR0aCcpXHJcbiAgICBwdWJsaWMgc2V0IHdpZHRoKHZhbHVlKXtcclxuICAgICAgICB0cnl7XHJcblxyXG4gICAgICAgICAgICB0aGlzLl9fd2lkdGg9dGhpcy5hZGFwdFVuaXQodmFsdWUsIFwiYXV0b1wiKTtcclxuICAgICAgICAgICAgaWYgKFN0cmluZyh0aGlzLl9fd2lkdGgpLmluZGV4T2YoXCJweFwiKSAhPT0gLTEpe1xyXG4gICAgICAgICAgICAgICAgLy9lbiBweFxyXG4gICAgICAgICAgICAgICAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jc3Moe1xyXG4gICAgICAgICAgICAgICAgICAgIFwid2lkdGhcIjogdGhpcy5hZGFwdFVuaXQodmFsdWUsIFwiYXV0b1wiKVxyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICAgICAgLy9lbiAlXHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFN0eWxlKFwid2lkdGhcIiwgIHRoaXMuX193aWR0aCk7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pLmNzcyh7XHJcbiAgICAgICAgICAgICAgICAgICAgXCJ3aWR0aFwiOiBcIjEwMCVcIlxyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgfWNhdGNoKGVycm9yKXtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFtzZXQgd2lkdGhdIC0gZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCB3aWR0aCgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9fd2lkdGg7XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIF9zaG93U2Nyb2xsQmFyID0gZmFsc2U7XHJcbiAgICAvLy0tLXdpZHRoLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnc2hvd1Njcm9sbEJhcicpXHJcbiAgICBwdWJsaWMgc2V0IHNob3dTY3JvbGxCYXIodmFsdWUpe1xyXG4gICAgICAgIHRyeXtcclxuICAgICAgICAgICAgdGhpcy5fc2hvd1Njcm9sbEJhciA9IFN0cmluZ1V0aWxzLmlzVHJ1ZSh2YWx1ZSk7XHJcbiAgICAgICAgICAgIC8vZW4gJVxyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKFwib3ZlcmZsb3dcIiwgICdhdXRvJyk7XHJcbiAgICAgICAgICAgICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkuY3NzKHtcclxuICAgICAgICAgICAgICAgIFwib3ZlcmZsb3dcIjogXCJhdXRvXCJcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIH1jYXRjaChlcnJvcil7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ21ldGhvZCBbc2V0IHNob3dTY3JvbGxCYXJdIC0gZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBzaG93U2Nyb2xsQmFyKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Nob3dTY3JvbGxCYXI7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1oZWlnaHQtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ2hlaWdodCcpXHJcbiAgICBwdWJsaWMgc2V0IGhlaWdodCh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX2hlaWdodD10aGlzLmFkYXB0VW5pdCh2YWx1ZSwgXCJhdXRvXCIpO1xyXG4gICAgICAgIGlmIChTdHJpbmcodGhpcy5fX2hlaWdodCkuaW5kZXhPZihcInB4XCIpICE9PSAtMSl7XHJcbiAgICAgICAgICAgIC8vZW4gcHhcclxuICAgICAgICAgICAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jc3Moe1xyXG4gICAgICAgICAgICAgICAgXCJoZWlnaHRcIjogdGhpcy5fX2hlaWdodFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgaWYoIHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09IFwiU1dUTEFCRUxcIiB8fCB0aGlzLmdldENvbXBvbmVudE5hbWUoKSA9PSBcIlNXVFRFWFRcIiApe1xyXG4gICAgICAgICAgICAgICAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jc3Moe1xyXG4gICAgICAgICAgICAgICAgICAgIFwibGluZS1oZWlnaHRcIjp0aGlzLl9faGVpZ2h0XHJcbiAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgLy9lbiAlXHJcbiAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoXCJoZWlnaHRcIiwgdGhpcy5fX2hlaWdodCk7XHJcbiAgICAgICAgICAgICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkuY3NzKHtcclxuICAgICAgICAgICAgICAgIFwiaGVpZ2h0XCI6IFwiMTAwJVwiXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuXHJcblxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBoZWlnaHQoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX2hlaWdodDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLW1pbkhlaWdodC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnbWluSGVpZ2h0JylcclxuICAgIHB1YmxpYyBzZXQgbWluSGVpZ2h0KHZhbHVlOm51bWJlcil7XHJcbiAgICAgICAgdGhpcy5fbWluSGVpZ2h0PXZhbHVlO1xyXG4gICAgICAgIHRoaXMuc2V0U3R5bGUoJ21pbi1oZWlnaHQnLCB0aGlzLl9taW5IZWlnaHQrJ3B4JyAgICx0aGlzLmhvc3RFbGVtZW50KTtcclxuICAgICAgICB0aGlzLnNldFN0eWxlKCdtaW4taGVpZ2h0JywgdGhpcy5fbWluSGVpZ2h0KydweCcgICAsdGhpcy5kb21FbGVtZW50KTtcclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgbWluSGVpZ2h0KCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21pbkhlaWdodDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLW1pbldpZHRoLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KCdtaW5XaWR0aCcpXHJcbiAgICBwdWJsaWMgc2V0IG1pbldpZHRoKHZhbHVlOm51bWJlcil7XHJcbiAgICAgICAgdGhpcy5fbWluV2lkdGg9dmFsdWU7XHJcbiAgICAgICAgdGhpcy5zZXRTdHlsZSgnbWluLXdpZHRoJywgdGhpcy5fbWluV2lkdGgrJ3B4JyAgICx0aGlzLmhvc3RFbGVtZW50KTtcclxuICAgICAgICB0aGlzLnNldFN0eWxlKCdtaW4td2lkdGgnLCB0aGlzLl9taW5XaWR0aCsncHgnICAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG1pbldpZHRoKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21pbldpZHRoO1xyXG4gICAgfVxyXG4gICAgLy8tLS1taW5IZWlnaHQtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ21heEhlaWdodCcpXHJcbiAgICBwdWJsaWMgc2V0IG1heEhlaWdodCh2YWx1ZTpudW1iZXIpe1xyXG4gICAgICAgIHRoaXMuX21heEhlaWdodD12YWx1ZTtcclxuICAgICAgICB0aGlzLnNldFN0eWxlKCdtYXgtaGVpZ2h0JywgdGhpcy5fbWF4SGVpZ2h0KydweCcgICAsdGhpcy5ob3N0RWxlbWVudCk7XHJcbiAgICAgICAgdGhpcy5zZXRTdHlsZSgnbWF4LWhlaWdodCcsIHRoaXMuX21heEhlaWdodCsncHgnICAgLHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG1heEhlaWdodCgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9tYXhIZWlnaHQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1taW5XaWR0aC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnbWF4V2lkdGgnKVxyXG4gICAgcHVibGljIHNldCBtYXhXaWR0aCh2YWx1ZTpudW1iZXIpe1xyXG4gICAgICAgIHRoaXMuX21heFdpZHRoPXZhbHVlO1xyXG4gICAgICAgIHRoaXMuc2V0U3R5bGUoJ21heC13aWR0aCcsIHRoaXMuX21heFdpZHRoKydweCcgICAsdGhpcy5ob3N0RWxlbWVudCk7XHJcbiAgICAgICAgdGhpcy5zZXRTdHlsZSgnbWF4LXdpZHRoJywgdGhpcy5fbWF4V2lkdGgrJ3B4JyAgICx0aGlzLmRvbUVsZW1lbnQpO1xyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBtYXhXaWR0aCgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9tYXhXaWR0aDtcclxuICAgIH1cclxuXHJcbiAgICAvLy0tLWluY2x1ZGVJbkxheW91dC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IGluY2x1ZGVJbkxheW91dCh2YWx1ZTogYm9vbGVhbikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSA9PT0gXCJzdHJpbmdcIikge1xyXG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IFwiZmFsc2VcIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fSXNJbmNsdWRlSW5MYXlvdXQgPSBmYWxzZTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX0lzSW5jbHVkZUluTGF5b3V0ID0gdHJ1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuX0lzSW5jbHVkZUluTGF5b3V0ID0gdmFsdWU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh0aGlzLnZpc2libGUudG9TdHJpbmcoKSA9PSAnZmFsc2UnKSB7XHJcbiAgICAgICAgICAgIGlmICghdGhpcy5fSXNJbmNsdWRlSW5MYXlvdXQgKVxyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSggXCJkaXNwbGF5XCIsIFwibm9uZVwiKVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoIFwidmlzaWJpbGl0eVwiLCBcImhpZGRlblwiKVxyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSggXCJkaXNwbGF5XCIsIFwiYmxvY2tcIilcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IGluY2x1ZGVJbkxheW91dCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fSXNJbmNsdWRlSW5MYXlvdXQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS12aXNpYmxlLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCB2aXNpYmxlKHZhbHVlOiBhbnkpIHtcclxuXHJcbiAgICAgICAgaWYodGhpcy5faXNWaXNpYmxlICE9IHRoaXMuYWRhcHRWYWx1ZUFzQm9vbGVhbih2YWx1ZSkpe1xyXG4gICAgICAgICAgICB0aGlzLl9pc1Zpc2libGUgPSB0aGlzLmFkYXB0VmFsdWVBc0Jvb2xlYW4odmFsdWUpO1xyXG4gICAgICAgICAgICBpZighdGhpcy5faXNWaXNpYmxlICl7XHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5pbmNsdWRlSW5MYXlvdXQgPT0gdHJ1ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoIFwidmlzaWJpbGl0eVwiLCBcImhpZGRlblwiKTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcInZpc2liaWxpdHlcIiwgXCJ2aXNpYmxlXCIpO1xyXG4gICAgICAgICAgICAgICAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KS5zaG93KCk7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLnJlbW92ZUNsYXNzKFwiaGlkZGVuXCIpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCB2aXNpYmxlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9pc1Zpc2libGU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1lbmFibGVkLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCBlbmFibGVkKHZhbHVlOiBhbnkpIHtcclxuXHJcbiAgICAgICAgaWYoIXRoaXMuaG9zdEVsZW1lbnQpIHRoaXMuX2hvc3RFbGVtZW50ID0gICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkgPyAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpICA6IG51bGw7XHJcbiAgICAgICAgaWYoIXRoaXMuZG9tRWxlbWVudCkgIHRoaXMuX2RvbUVsZW1lbnQgPSB0aGlzLmhvc3RFbGVtZW50ID8gJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdKSAgOm51bGw7XHJcblxyXG4gICAgICAgIGlmKFN0cmluZyh0aGlzLl9pc0VuYWJsZWQpICE9IFN0cmluZyh2YWx1ZSkpe1xyXG4gICAgICAgICAgICB0aGlzLl9pc0VuYWJsZWQgPSB0aGlzLmFkYXB0VmFsdWVBc0Jvb2xlYW4odmFsdWUpO1xyXG4gICAgICAgICAgICBpZih0aGlzLl9pc0VuYWJsZWQpe1xyXG4gICAgICAgICAgICAgICAgJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdKS5yZW1vdmVDbGFzcygnZGlzYWJsZWQtY29udGFpbmVyJyk7XHJcbiAgICAgICAgICAgICAgICAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudClbMF0pLnJlbW92ZUNsYXNzKCdkaXNhYmxlUG9pbnRlckV2ZW50cycpO1xyXG4gICAgICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgICAgICQoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5jaGlsZHJlblswXSkuYWRkQ2xhc3MoJ2Rpc2FibGVkLWNvbnRhaW5lcicpO1xyXG4gICAgICAgICAgICAgICAgJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdKS5hZGRDbGFzcygnZGlzYWJsZVBvaW50ZXJFdmVudHMnKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgaWYodGhpcy5faXNFbmFibGVkKXtcclxuICAgICAgICAgICAgICAgIHRoaXMuYWRkQWxsT3V0cHV0c0V2ZW50c0xpc3RlbmVycyggdGhpcy5ob3N0RWxlbWVudCk7XHJcblxyXG4gICAgICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlQWxsT3VwdXRzRXZlbnRzTGlzdGVuZXJzKHRoaXMuaG9zdEVsZW1lbnQgKTtcclxuICAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlVGFiSW5kZXgodGhpcy5ob3N0RWxlbWVudCk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZVRhYkluZGV4KHRoaXMuZG9tRWxlbWVudCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBlbmFibGVkKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9pc0VuYWJsZWQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1QYWRkaW5ncy0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KClcclxuICAgIHB1YmxpYyBzZXQgcGFkZGluZ1RvcCh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX3BhZGRpbmdUb3AgPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09IFwiU1dUUEFORUxcIil7XHJcbiAgICAgICAgICAgIHZhciBwYW5lbEJvZHkgPSAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmZpbmQoJy5wYW5lbEJvZHknKTtcclxuICAgICAgICAgICAgaWYocGFuZWxCb2R5KXtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoJ3BhZGRpbmctdG9wJywgdGhpcy5fX3BhZGRpbmdUb3AsIHBhbmVsQm9keSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1lbHNlIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09ICdTV1RUQUJOQVZJR0FUT1InKXtcclxuXHJcbiAgICAgICAgICAgIHZhciB0YWJOYXZpZ2F0b3JDb250ZW50ID0gJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKT8gICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkuZmluZCgnLnRhYk5hdmlnYXRvci1jb250ZW50Jyk6IG51bGw7XHJcbiAgICAgICAgICAgIGlmKCQodGFiTmF2aWdhdG9yQ29udGVudCkubGVuZ3RoPjApe1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgncGFkZGluZy10b3AnLCB0aGlzLl9fcGFkZGluZ1RvcCwgJCh0YWJOYXZpZ2F0b3JDb250ZW50KSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNle1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdwYWRkaW5nLXRvcCcsIHRoaXMuX19wYWRkaW5nVG9wLCAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgcGFkZGluZ1RvcCAoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX3BhZGRpbmdUb3A7XHJcbiAgICB9XHJcblxyXG4gICAgQElucHV0KClcclxuICAgIHB1YmxpYyBzZXQgcGFkZGluZ0JvdHRvbSh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX3BhZGRpbmdCb3R0b20gPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09IFwiU1dUUEFORUxcIil7XHJcbiAgICAgICAgICAgIHZhciBwYW5lbEJvZHkgPSAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmZpbmQoJy5wYW5lbEJvZHknKTtcclxuICAgICAgICAgICAgaWYocGFuZWxCb2R5KXtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoJ3BhZGRpbmctYm90dG9tJywgdGhpcy5fX3BhZGRpbmdCb3R0b20sIHBhbmVsQm9keSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1lbHNlIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09ICdTV1RUQUJOQVZJR0FUT1InKXtcclxuICAgICAgICAgICAgdmFyIHRhYk5hdmlnYXRvckNvbnRlbnQgPSAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pPyAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5maW5kKCcudGFiTmF2aWdhdG9yLWNvbnRlbnQnKTogbnVsbDtcclxuICAgICAgICAgICAgaWYoJCh0YWJOYXZpZ2F0b3JDb250ZW50KS5sZW5ndGg+MCl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdwYWRkaW5nLWJvdHRvbScsIHRoaXMuX19wYWRkaW5nQm90dG9tLCAkKHRhYk5hdmlnYXRvckNvbnRlbnQpKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2V7XHJcbiAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoJ3BhZGRpbmctYm90dG9tJywgdGhpcy5fX3BhZGRpbmdCb3R0b20sICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBwYWRkaW5nQm90dG9tICgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9fcGFkZGluZ0JvdHRvbTtcclxuICAgIH1cclxuXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCBwYWRkaW5nTGVmdCh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX3BhZGRpbmdMZWZ0ID0gIHRoaXMuYWRhcHRVbml0KHZhbHVlKTtcclxuICAgICAgICBpZih0aGlzLmdldENvbXBvbmVudE5hbWUoKSA9PSBcIlNXVFBBTkVMXCIpe1xyXG4gICAgICAgICAgICB2YXIgcGFuZWxCb2R5ID0gJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KS5maW5kKCcucGFuZWxCb2R5Jyk7XHJcbiAgICAgICAgICAgIGlmKHBhbmVsQm9keSl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdwYWRkaW5nLWxlZnQnLCB0aGlzLl9fcGFkZGluZ0xlZnQsIHBhbmVsQm9keSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1lbHNlIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09ICdTV1RUQUJOQVZJR0FUT1InKXtcclxuICAgICAgICAgICAgdmFyIHRhYk5hdmlnYXRvckNvbnRlbnQgPSAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pPyAgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5maW5kKCcudGFiTmF2aWdhdG9yLWNvbnRlbnQnKTogbnVsbDtcclxuICAgICAgICAgICAgaWYoJCh0YWJOYXZpZ2F0b3JDb250ZW50KS5sZW5ndGg+MCl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFN0eWxlKCdwYWRkaW5nLWxlZnQnLCB0aGlzLl9fcGFkZGluZ0xlZnQsICQodGFiTmF2aWdhdG9yQ29udGVudCkpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgncGFkZGluZy1sZWZ0JywgdGhpcy5fX3BhZGRpbmdMZWZ0LCAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgcGFkZGluZ0xlZnQgKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19wYWRkaW5nTGVmdDtcclxuICAgIH1cclxuXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCBwYWRkaW5nUmlnaHQodmFsdWUpe1xyXG4gICAgICAgIHRoaXMuX19wYWRkaW5nUmlnaHQgPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpID09IFwiU1dUUEFORUxcIil7XHJcbiAgICAgICAgICAgIHZhciBwYW5lbEJvZHkgPSAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmZpbmQoJy5wYW5lbEJvZHknKTtcclxuICAgICAgICAgICAgaWYocGFuZWxCb2R5KXtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoJ3BhZGRpbmctcmlnaHQnLCB0aGlzLl9fcGFkZGluZ1JpZ2h0LCBwYW5lbEJvZHkpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9ZWxzZSBpZih0aGlzLmdldENvbXBvbmVudE5hbWUoKSA9PSAnU1dUVEFCTkFWSUdBVE9SJyl7XHJcbiAgICAgICAgICAgIHZhciB0YWJOYXZpZ2F0b3JDb250ZW50ID0gJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKT8gICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkuZmluZCgnLnRhYk5hdmlnYXRvci1jb250ZW50Jyk6IG51bGw7XHJcbiAgICAgICAgICAgIGlmKCQodGFiTmF2aWdhdG9yQ29udGVudCkubGVuZ3RoPjApe1xyXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgncGFkZGluZy1yaWdodCcsIHRoaXMuX19wYWRkaW5nUmlnaHQsICQodGFiTmF2aWdhdG9yQ29udGVudCkpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZSgncGFkZGluZy1yaWdodCcsIHRoaXMuX19wYWRkaW5nUmlnaHQsICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBwYWRkaW5nUmlnaHQgKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19wYWRkaW5nUmlnaHQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8tLS1NYXJnaW5zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoKVxyXG4gICAgcHVibGljIHNldCBtYXJnaW5Ub3AodmFsdWUpe1xyXG4gICAgICAgIHRoaXMuX19tYXJnaW5Ub3AgPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpIT0gXCJTV1RQQU5FTFwiKXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIm1hcmdpbi10b3BcIiAsIHRoaXMuX19tYXJnaW5Ub3AsICQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkpXHJcbiAgICAgICAgfWVsc2V7XHJcbiAgICAgICAgICAgIHRoaXMuc2V0U3R5bGUoXCJtYXJnaW4tdG9wXCIgLCB0aGlzLl9fbWFyZ2luVG9wKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHB1YmxpYyBnZXQgbWFyZ2luVG9wICgpe1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9fbWFyZ2luVG9wO1xyXG4gICAgfVxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IG1hcmdpbkJvdHRvbSh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX21hcmdpbkJvdHRvbSA9ICB0aGlzLmFkYXB0VW5pdCh2YWx1ZSk7XHJcbiAgICAgICAgaWYodGhpcy5nZXRDb21wb25lbnROYW1lKCkhPSBcIlNXVFBBTkVMXCIpe1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKFwibWFyZ2luLWJvdHRvbVwiICwgdGhpcy5fX21hcmdpbkJvdHRvbSwgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKSlcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIm1hcmdpbi1ib3R0b21cIiAsIHRoaXMuX19tYXJnaW5Cb3R0b20pXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBtYXJnaW5Cb3R0b20gKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19tYXJnaW5Cb3R0b207XHJcbiAgICB9XHJcblxyXG4gICAgQElucHV0KClcclxuICAgIHB1YmxpYyBzZXQgbWFyZ2luTGVmdCh2YWx1ZSl7XHJcbiAgICAgICAgdGhpcy5fX21hcmdpbkxlZnQgPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpIT0gXCJTV1RQQU5FTFwiKXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIm1hcmdpbi1sZWZ0XCIgLCB0aGlzLl9fbWFyZ2luTGVmdCwgJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKSlcclxuICAgICAgICB9ZWxzZXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIm1hcmdpbi1sZWZ0XCIgLCB0aGlzLl9fbWFyZ2luTGVmdClcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG1hcmdpbkxlZnQgKCl7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX19tYXJnaW5MZWZ0O1xyXG4gICAgfVxyXG5cclxuICAgIEBJbnB1dCgpXHJcbiAgICBwdWJsaWMgc2V0IG1hcmdpblJpZ2h0KHZhbHVlKXtcclxuICAgICAgICB0aGlzLl9fbWFyZ2luUmlnaHQgPSAgdGhpcy5hZGFwdFVuaXQodmFsdWUpO1xyXG4gICAgICAgIGlmKHRoaXMuZ2V0Q29tcG9uZW50TmFtZSgpIT0gXCJTV1RQQU5FTFwiKXtcclxuICAgICAgICAgICAgdGhpcy5zZXRTdHlsZShcIm1hcmdpbi1yaWdodFwiICwgdGhpcy5fX21hcmdpblJpZ2h0LCAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pKVxyXG4gICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICB0aGlzLnNldFN0eWxlKFwibWFyZ2luLXJpZ2h0XCIgLCB0aGlzLl9fbWFyZ2luUmlnaHQpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcHVibGljIGdldCBtYXJnaW5SaWdodCAoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5fX21hcmdpblJpZ2h0O1xyXG4gICAgfVxyXG5cclxuICAgIC8vLS0tTWF4Q2hhcnMtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dChcIm1heENoYXJzXCIpIG1heENoYXJzO1xyXG4gICAgLy8tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5cclxuICAgIGNvbnN0cnVjdG9yKHByaXZhdGUgX19lbGVtZW50OiBFbGVtZW50UmVmLCBwcml2YXRlIF9fY29tbW9uOiBDb21tb25TZXJ2aWNlKSB7XHJcbiAgICAgICAgc3VwZXIoX19lbGVtZW50LCBfX2NvbW1vbik7XHJcbiAgICB9XHJcblxyXG5cclxuICAgIG5nT25Jbml0KCkge1xyXG5cclxuICAgICAgICAvLy1HZXR0aW5nIGhvc3QgZWxlbWVudC5cclxuICAgICAgICB0aGlzLl9ob3N0RWxlbWVudCA9ICAkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpID8gJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KSAgOiBudWxsO1xyXG4gICAgICAgIHRoaXMuX2RvbUVsZW1lbnQgPSB0aGlzLmhvc3RFbGVtZW50ID8gJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdKSA6bnVsbDtcclxuXHJcbiAgICAgICAgLy9SZW1vdmUgYWxsIGV2ZW50cyBvbiBob3N0RWxlbWVudFxyXG4gICAgICAgIHRoaXMucmVtb3ZlQWxsT3VwdXRzRXZlbnRzTGlzdGVuZXJzKHRoaXMuaG9zdEVsZW1lbnQpO1xyXG5cclxuICAgICAgICAvLy1SZW1vdmUgdGFiSW5kZXggb24gaG90IGVsZW1lbnQuXHJcbiAgICAgICAgdGhpcy5yZW1vdmVUYWJJbmRleCgkKHRoaXMuaG9zdEVsZW1lbnQpKTtcclxuXHJcbiAgICAgICAgLy8tRXhjbHVkZSBcIlN3dEJ1dHRvblwiIGJlY2F1c2UgaXQgaGFuZGxlcyBoaXMgb3duIGV2ZW50cyBieSBpdCBzZWxmLlxyXG4gICAgICAgIGlmKHRoaXMuX2lzRW5hYmxlZCAmJiB0aGlzLmdldENvbXBvbmVudE5hbWUoKSAhPSBcIlNXVEJVVFRPTlwiKXtcclxuICAgICAgICAgICAgdGhpcy5hZGRBbGxPdXRwdXRzRXZlbnRzTGlzdGVuZXJzKHRoaXMuaG9zdEVsZW1lbnQpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldFJlYWxIZWlnaHQoKXtcclxuICAgICAgICByZXR1cm4gdGhpcy5faG9zdEVsZW1lbnQub2Zmc2V0SGVpZ2h0O1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogYWRkQ2hpbGQgOiBhcHBlbmQgYSBkeW5hbWljYWxseSBjcmVhdGVkIGNvbXBvbmVudC5cclxuICAgICAqIEBwYXJhbSB0eXBlIDogQ29tcG9uZW50IHR5cGVcclxuICAgICAqL1xyXG4gICAgcHVibGljIGFkZENoaWxkKHZhbHVlOiBhbnkpIHtcclxuICAgICAgICB2YXIgY29tcG9uZW50SW5zdGFuY2U6IGFueTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmxvYWQodmFsdWUpLnN1YnNjcmliZSgoY29tcG9uZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudXJsTG9hZGluZ1N0YXJ0LmVtaXQobmV3IERhdGUoKS5nZXRNaWxsaXNlY29uZHMoKSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBvbmVudEluc3RhbmNlID0gdGhpcy5fY29udGFpbmVyLmNyZWF0ZUNvbXBvbmVudChjb21wb25lbnQsIDApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVybExvYWRpbmdFbmQuZW1pdChuZXcgRGF0ZSgpLmdldE1pbGxpc2Vjb25kcygpKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5faXNsb2FkZWQgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX2lzbG9hZGVkID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjb21wID0gdGhpcy5fX2NvbW1vbi5jb21wb25lbnRGYWN0b3J5UmVzb2x2ZXIucmVzb2x2ZUNvbXBvbmVudEZhY3RvcnkodmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50SW5zdGFuY2UgPSB0aGlzLl9jb250YWluZXIuY3JlYXRlQ29tcG9uZW50KGNvbXAsIDApO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY2hpbGRyZW4gPSAkKCQodGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuY2hpbGRyZW4oKTtcclxuICAgICAgICAgICAgICAgIGlmIChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZVsnZWxlbWVudCddICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAvL2l0cyBhIGNvbXBvbmVudC5cclxuICAgICAgICAgICAgICAgICAgICAkKGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlWydlbGVtZW50J10ubmF0aXZlRWxlbWVudCkuaW5zZXJ0QWZ0ZXIoJChjaGlsZHJlbltjaGlsZHJlbi5sZW5ndGggLSAxXSkpO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZVsnZWxlbWVudFJlZiddICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAvL2l0cyBhIHNjcmVlbiBTd3RNb2R1bGUuXHJcbiAgICAgICAgICAgICAgICAgICAgJChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZVsnZWxlbWVudFJlZiddLm5hdGl2ZUVsZW1lbnQpLmluc2VydEFmdGVyKCQoY2hpbGRyZW5bY2hpbGRyZW4ubGVuZ3RoIC0gMV0pKTtcclxuICAgICAgICAgICAgICAgIH1lbHNlIGlmIChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZVsnZWxlbSddICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAkKGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlWydlbGVtJ10ubmF0aXZlRWxlbWVudCkuaW5zZXJ0QWZ0ZXIoJChjaGlsZHJlbltjaGlsZHJlbi5sZW5ndGggLSAxXSkpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgdGhpcy5jb21wb25lbnRzLnB1c2goY29tcG9uZW50SW5zdGFuY2UpO1xyXG4gICAgICAgICAgICAgICAgJChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZSkuYXR0cignaWQnLCBcImR5bmFtaWMtXCIgKyBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgNSkpO1xyXG4gICAgICAgICAgICAgICAgJChjb21wb25lbnRJbnN0YW5jZS5pbnN0YW5jZSkuYXR0cignc2VsZWN0b3InLCBjb21wLnNlbGVjdG9yKTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiggJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5ub2RlTmFtZSA9PSAgIFwiU1dUUkFESU9CVVRUT05HUk9VUFwiICl7XHJcbiAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50SW5zdGFuY2UuaW5zdGFuY2UucGFyZW50R3JvdXAgPSB0aGlzO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlLnBhcmVudEdyb3VwLnJhZGlvSXRlbXNBcnJheS5wdXNoKGNvbXBvbmVudEluc3RhbmNlLmluc3RhbmNlKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiAoY29tcG9uZW50SW5zdGFuY2UuaW5zdGFuY2UpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycm9yKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRXJyb3IgOicgKyBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogYWRkQ2hpbGRBdCA6IGFwcGVuZCBhIGR5bmFtaWNhbGx5IGNyZWF0ZWQgY29tcG9uZW50IGF0IGEgc3BlY2lmaWMgaW5kZXguXHJcbiAgICAgKiBAcGFyYW0gaW5kZXggOiBpbmRleFxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgYWRkQ2hpbGRBdCh0eXBlLCBpbmRleCkge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IE9yaWdpbmFsQ2hpbGRyZW4gPSB7Li4uJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jaGlsZHJlbigpfTtcclxuXHJcbiAgICAgICAgICAgIGlmIChpbmRleCA9PT0gMCAmJiBPcmlnaW5hbENoaWxkcmVuLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuYWRkQ2hpbGQodHlwZSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaW5kZXggPCB0aGlzLmdldENoaWxkcmVuKCkubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAgY29uc3QgY29tcCA9IHRoaXMuX19jb21tb24uY29tcG9uZW50RmFjdG9yeVJlc29sdmVyLnJlc29sdmVDb21wb25lbnRGYWN0b3J5KHR5cGUpO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY29tcG9uZW50ID0gdGhpcy5fY29udGFpbmVyLmNyZWF0ZUNvbXBvbmVudChjb21wLCBpbmRleCk7XHJcbiAgICAgICAgICAgICAgICAvKiBQdXNoIHRoZSBjb21wb25lbnQgc28gdGhhdCB3ZSBjYW4ga2VlcCB0cmFjayBvZiB3aGljaCBjb21wb25lbnRzIGFyZSBjcmVhdGVkICovXHJcbiAgICAgICAgICAgICAgICB0aGlzLmNvbXBvbmVudHMucHVzaChjb21wb25lbnQpO1xyXG4gICAgICAgICAgICAgICAgJChjb21wb25lbnQuaW5zdGFuY2UpLmF0dHIoJ2lkJywgXCJkeW5hbWljLVwiICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDUpKTtcclxuICAgICAgICAgICAgICAgICQoY29tcG9uZW50Lmluc3RhbmNlKS5hdHRyKCdzZWxlY3RvcicsIGNvbXAuc2VsZWN0b3IpO1xyXG5cclxuICAgICAgICAgICAgICAgIC8qIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICBjb25zdCBOZXdDaGlsZHJlbiA9ICQoJCh0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKS5jaGlsZHJlbigpO1xyXG4gICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdBZGRlZENvbXBvbmVudCA9ICQoTmV3Q2hpbGRyZW4udG9BcnJheSgpLmZpbmQoeCA9PiB4LmlkID09PSBjb21wb25lbnQuaW5zdGFuY2VbJ2lkJ10pKTtcclxuIFxyXG4gICAgICAgICAgICAgICAgICAgICBjb25zdCBjb21wb25lbnRUb0JlUmVwbGFjZWRCZWZvcmUgPSAkKE9yaWdpbmFsQ2hpbGRyZW4pLmVxKGluZGV4KTtcclxuICAgICAgICAgICAgICAgICAgICAgJChuZXdBZGRlZENvbXBvbmVudCkuaW5zZXJ0QmVmb3JlKCQoY29tcG9uZW50VG9CZVJlcGxhY2VkQmVmb3JlKSk7XHJcbiAgICAgICAgICAgICAgICAgfSwgMCk7Ki9cclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gY29tcG9uZW50Lmluc3RhbmNlO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIj4+PiBQUk9HUkFNTUFUSU9OIEVSUk9SIDogIGluZGV4IGlzIG91dHNpZGUgdGhlIGJvdW5kcyBvZiBjb21wb25lbnRzIGFycmF5LiBcIik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcImVcIixlcnJvcik7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Vycm9yIDonICsgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIHJlbW92ZUNoaWxkIDogRGVsZXRlIGEgY29tcG9uZW50IGNoaWxkXHJcbiAgICAgKiBAcGFyYW0gY29tcG9uZW50Q2xhc3MgOiB0aGUgY29tcG9uZW50IHRvIHJlbW92ZVxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgcmVtb3ZlQ2hpbGQoY29tcG9uZW50Q2xhc3MpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICB2YXIgY29tcG9uZW50O1xyXG4gICAgICAgICAgICB2YXIgY29tcG9uZW50SW5kZXggPSAtMTtcclxuICAgICAgICAgICAgLy9keW5hbWljIGNoaWxkXHJcbiAgICAgICAgICAgIGZvciAodmFyIGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmNvbXBvbmVudHMubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5jb21wb25lbnRzW2luZGV4XS5pbnN0YW5jZSA9PT0gY29tcG9uZW50Q2xhc3MpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb21wb25lbnRJbmRleCA9IGluZGV4O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbXBvbmVudCA9IHRoaXMuY29tcG9uZW50c1tpbmRleF07XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLy9zdGF0aWMgY2hpbGRcclxuICAgICAgICAgICAgaWYgKGNvbXBvbmVudEluZGV4ICE9PSAtMSkge1xyXG4gICAgICAgICAgICAgICAgLyogUmVtb3ZlIGNvbXBvbmVudCBmcm9tIGJvdGggdmlldyBhbmQgYXJyYXkgKi9cclxuICAgICAgICAgICAgICAgIHRoaXMuX2NvbnRhaW5lci5yZW1vdmUodGhpcy5fY29udGFpbmVyLmluZGV4T2YoY29tcG9uZW50KSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNvbXBvbmVudHMuc3BsaWNlKGNvbXBvbmVudEluZGV4LCAxKTtcclxuICAgICAgICAgICAgICAgICQoXCIjXCIrICQuZXNjYXBlU2VsZWN0b3IoY29tcG9uZW50Q2xhc3MuaWQpKS5yZW1vdmUoKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGlmIChjb21wb25lbnRDbGFzcyAhPSB1bmRlZmluZWQgJiYgIWNvbXBvbmVudENsYXNzLmlkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgJChjb21wb25lbnRDbGFzcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmVtcHR5KCk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICQoXCIjXCIrICQuZXNjYXBlU2VsZWN0b3IoY29tcG9uZW50Q2xhc3MuaWQpKS5yZW1vdmUoKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Vycm9yIDonICsgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIHJlbW92ZUFsbENoaWxkcmVuIDogRGVsZXRlcyBhbGwgdGhlIHN0YXRpY2FsbHlcclxuICAgICAqICYgZHluYW1pY2FsbHkgY2hpbGQgY29tcG9uZW50cy5cclxuICAgICAqL1xyXG4gICAgcHVibGljIHJlbW92ZUFsbENoaWxkcmVuKCkge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vcmVtb3ZlIGFsbCBzdGF0aWMgY2hpbGRyZW5cclxuICAgICAgICAgICAgdmFyIGNoaWxkcmVuID0gJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNoaWxkcmVuKCk7XHJcbiAgICAgICAgICAgIGZvciAodmFyIGluZGV4ID0gMDsgaW5kZXggPCBjaGlsZHJlbi5sZW5ndGg7IGluZGV4KyspIHtcclxuICAgICAgICAgICAgICAgICQoY2hpbGRyZW5baW5kZXhdKS5yZW1vdmUoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aGlzLmNvbXBvbmVudHMgPSBbXTtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciA6JyArIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG5cclxuICAgIC8qKlxyXG4gICAgICogZ2V0Q2hpbGRyZW4gOiBnZXQgY2hpbGRyZW4gb2YgYSBzdGF0aWNhbGx5XHJcbiAgICAgKiAmIGR5bmFtaWNhbGx5IGNoaWxkIGNvbXBvbmVudHMuXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBnZXRDaGlsZHJlbigpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICB2YXIgZmluZEluZGV4ID0gLTE7XHJcbiAgICAgICAgICAgIHZhciBjaGlsZHJlbkxpc3QgPSBuZXcgQXJyYXkoKTtcclxuICAgICAgICAgICAgdmFyIGNoaWxkcmVuID0gJCgkKHRoaXMuX19lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmNoaWxkcmVuKCk7XHJcbiAgICAgICAgICAgIC8vLSBHZXR0aW5nIGNoaWxkcmVuXHJcbiAgICAgICAgICAgIGZvciAodmFyIGluZGV4ID0gMDsgaW5kZXggPCBjaGlsZHJlbi5sZW5ndGg7IGluZGV4KyspIHtcclxuXHJcbiAgICAgICAgICAgICAgICAvLy0gY2hlY2sgaWYgaXRzIGEgZHluYW1pYyBjaGlsZC5cclxuICAgICAgICAgICAgICAgIGlmICgoZmluZEluZGV4ID0gdGhpcy5jb21wb25lbnRzLmZpbmRJbmRleCh4ID0+IHguaW5zdGFuY2UuaWQgPT0gJChjaGlsZHJlbltpbmRleF0pWzBdLmlkKSkgIT0gLTEpIHtcclxuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbkxpc3QucHVzaCh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkVHlwZTp0aGlzLmNvbXBvbmVudHNbZmluZEluZGV4XS5pbnN0YW5jZS5zZWxlY3RvcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHRoaXMuY29tcG9uZW50c1tmaW5kSW5kZXhdLmluc3RhbmNlLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb21wb25lbnQ6IHRoaXMuY29tcG9uZW50c1tmaW5kSW5kZXhdLmluc3RhbmNlXHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vLSBpdHMgYSBzdGF0aWMgY2hpbGQuXHJcbiAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW5MaXN0LnB1c2goeyBjaGlsZFR5cGU6ICQoJChjaGlsZHJlbltpbmRleF0pWzBdKS5hdHRyKCdzZWxlY3RvcicpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZDogJChjaGlsZHJlbltpbmRleF0pWzBdLmlkXHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiBjaGlsZHJlbkxpc3Q7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciA6JyArIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBjb250YWlucyA6IHJldHVybiBpZiB0aGUgY29tcG9uZW50IGV4aXN0cyBpbiB0aGUgY29udGFpbmVyIG9yIG5vdFxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgY29udGFpbnMoY29tcG9uZW50Q2xhc3MpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICB2YXIgY2hpbGRyZW4gPSB0aGlzLmdldENoaWxkcmVuKCk7XHJcbiAgICAgICAgICAgIHZhciBjaGlsZCA9IChjb21wb25lbnRDbGFzcyAhPSB1bmRlZmluZWQgJiYgY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyBjaGlsZHJlbi5maW5kKHggPT4geC5pZCA9PSBjb21wb25lbnRDbGFzcy5pZCkgOiBudWxsO1xyXG4gICAgICAgICAgICB2YXIgY29udGFpbjogYm9vbGVhbiA9IGNoaWxkICE9IG51bGwgPyB0cnVlIDogZmFsc2U7XHJcblxyXG4gICAgICAgICAgICByZXR1cm4gY29udGFpbjtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciA6JyArIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiByZXR1cm5zIGEgY29tcG9uZW50IGF0IGEgc3BlY2lmaWMga2V5IGluZGV4XHJcbiAgICAgKiBAcGFyYW0ga2V5XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBnZXRDaGlsZEF0KGtleTogYW55KSB7XHJcblxyXG4gICAgICAgIHZhciBjaGlsZHJlbkxpc3QgPSB0aGlzLmdldENoaWxkcmVuKCk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKGtleSA8IGNoaWxkcmVuTGlzdC5sZW5ndGggJiYgKGNoaWxkcmVuTGlzdCAhPSBudWxsICYmIGNoaWxkcmVuTGlzdCAhPSB1bmRlZmluZWQpICYmIChjaGlsZHJlbkxpc3Rba2V5XSAhPSBudWxsICYmIGNoaWxkcmVuTGlzdFtrZXldICE9IHVuZGVmaW5lZCkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBjaGlsZHJlbkxpc3Rba2V5XTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRXJyb3IgOicgKyBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICpcclxuICAgICAqL1xyXG4gICAgcHVibGljIGdldENoaWxkQnlOYW1lKG5hbWU6IGFueSkge1xyXG4gICAgICAgIHZhciBjaGlsZHJlbkxpc3QgPSB0aGlzLmdldENoaWxkcmVuKCk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgdmFyIGNoaWxkY29tcG9uZW50ID0gKGNoaWxkcmVuTGlzdCAhPSB1bmRlZmluZWQgJiYgY2hpbGRyZW5MaXN0Lmxlbmd0aCA+IDApID8gY2hpbGRyZW5MaXN0LmZpbmQoeCA9PiB4LmlkID09IG5hbWUpIDogdW5kZWZpbmVkO1xyXG4gICAgICAgICAgICByZXR1cm4gY2hpbGRjb21wb25lbnQ7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciA6JyArIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgcmVtb3ZlQ2hpbGRBdChpbmRleDogYW55KSB7XHJcblxyXG4gICAgICAgIHZhciBjaGlsZHJlbkxpc3QgPSB0aGlzLmdldENoaWxkcmVuKCk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKGNoaWxkcmVuTGlzdCAhPSB1bmRlZmluZWQgJiYgY2hpbGRyZW5MaXN0Lmxlbmd0aCA+IDAgJiYgaW5kZXggPCBjaGlsZHJlbkxpc3QubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICB2YXIgY2hpbGRjb21wb25lbnQgPSBjaGlsZHJlbkxpc3RbaW5kZXhdO1xyXG4gICAgICAgICAgICAgICAgaWYgKGNoaWxkY29tcG9uZW50ICE9IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChjaGlsZGNvbXBvbmVudC5jb21wb25lbnQgIT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlQ2hpbGQoY2hpbGRjb21wb25lbnQuY29tcG9uZW50KTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGNoaWxkY29tcG9uZW50LmlkICE9IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgY2hpbGQgPSB0aGlzLmdldENoaWxkQnlOYW1lKGNoaWxkY29tcG9uZW50LmlkKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNoaWxkICE9IHVuZGVmaW5lZCAmJiBjaGlsZCAhPSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZUNoaWxkKGNoaWxkKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRXJyb3IgOicgKyBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogcmV0dXJucyAgY29tcG9uZW50J2luZGV4IGlmIGV4aXN0cyBvdGhlcndpc2UgLTEuXHJcbiAgICAgKiBAcGFyYW0gY29tcG9uZW50XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBnZXRDaGlsZEluZGV4KGNvbXBvbmVudDogYW55KTogbnVtYmVyIHtcclxuICAgICAgICB2YXIgY2hpbGRyZW5MaXN0ID0gdGhpcy5nZXRDaGlsZHJlbigpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIHZhciBpbmRleCA9IC0xO1xyXG4gICAgICAgICAgICBpZiAoY29tcG9uZW50ICE9IHVuZGVmaW5lZCAmJiBjb21wb25lbnQgIT0gbnVsbCkge1xyXG4gICAgICAgICAgICAgICAgaW5kZXggPSBjaGlsZHJlbkxpc3QuZmluZEluZGV4KHggPT4gKCh4LmNvbXBvbmVudCA9PSBjb21wb25lbnQpIHx8ICh4LmlkID09IGNvbXBvbmVudC5pZCkpKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gaW5kZXg7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciA6JyArIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGxvYWQgY29tcG9uZW50IGluc3RhbmNlIGZyb20gaXRzIGxhenkgbW9kdWxlIHBhdGguXHJcbiAgICAgKiBAcGFyYW0gdXJsXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBsb2FkKHVybDogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QgX2NvbXBfdXJsID0gdXJsLmluZGV4T2YoXCI/XCIpID8gdXJsLnNwbGl0KFwiP1wiKVswXSA6IHVybDtcclxuICAgICAgICBjb25zdCBtYW5pZmVzdCA9IHRoaXMuX19jb21tb24ubWFuaWZlc3RzXHJcbiAgICAgICAgICAgIC5maW5kKChtKSA9PiBtLnBhdGggPT09IF9jb21wX3VybCk7XHJcbiAgICAgICAgY29uc3QgcCA9IHRoaXMuX19jb21tb24ubG9hZGVyLmxvYWQobWFuaWZlc3QubG9hZENoaWxkcmVuKVxyXG4gICAgICAgICAgICAudGhlbigobmdNb2R1bGVGYWN0b3J5KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBtb2R1bGVSZWYgPSBuZ01vZHVsZUZhY3RvcnkuY3JlYXRlKHRoaXMuX19jb21tb24uaW5qZWN0b3IpO1xyXG4gICAgICAgICAgICAgICAgLy8gUmVhZCBmcm9tIHRoZSBtb2R1bGVSZWYgaW5qZWN0b3IgYW5kIGxvY2F0ZSB0aGUgZHluYW1pYyBjb21wb25lbnQgdHlwZVxyXG4gICAgICAgICAgICAgICAgY29uc3QgZHluYW1pY0NvbXBvbmVudFR5cGUgPSBtb2R1bGVSZWYuaW5qZWN0b3IuZ2V0KF9jb21wX3VybCk7XHJcbiAgICAgICAgICAgICAgICAvL3RoaXMubWFwRGF0YU9iamVjdChkeW5hbWljQ29tcG9uZW50VHlwZSwgdGhpcy5nZXRVcmxRdWVyeSh0aGlzLnVybCkpO1xyXG4gICAgICAgICAgICAgICAgLy8gUmVzb2x2ZSB0aGlzIGNvbXBvbmVudCBmYWN0b3J5XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbW9kdWxlUmVmLmNvbXBvbmVudEZhY3RvcnlSZXNvbHZlci5yZXNvbHZlQ29tcG9uZW50RmFjdG9yeTxhbnk+KGR5bmFtaWNDb21wb25lbnRUeXBlKTtcclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHJldHVybiBmcm9tUHJvbWlzZShwKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gY2xlYXIgY29udGFpbmVyXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBjbGVhbigpIHtcclxuICAgICAgICB0aGlzLl9jb250YWluZXIuY2xlYXIoKTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gY2hlY2sgaWYgY3VycmVudCBjb250YWluZXJcclxuICAgICAqIGFscmVhZHkgY29udGFpbnMgRE9NLlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgaXNsb2FkZWQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2lzbG9hZGVkO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyB1bmxvYWQoKSB7XHJcbiAgICAgICAgdGhpcy5faXNsb2FkZWQgPSBmYWxzZTtcclxuICAgICAgICB0aGlzLl9jb250YWluZXIuY2xlYXIoKTtcclxuICAgIH1cclxuXHJcblxyXG5cclxuICAgIC8qKlxyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSBwcm9wXHJcbiAgICAgKiBAcGFyYW0gdmFsdWVcclxuICAgICAqL1xyXG4gICAgcHVibGljIHNldFN0eWxlKCBwcm9wOiBhbnksIHZhbHVlOiBhbnkgLCBlbGVtPzogYW55KTogdm9pZCB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYoIWVsZW0gJiYgdGhpcy5fX2VsZW1lbnQubmF0aXZlRWxlbWVudCl7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9fZWxlbWVudC5uYXRpdmVFbGVtZW50LnN0eWxlW3Byb3BdID0gdmFsdWU7XHJcbiAgICAgICAgICAgIH1lbHNlIGlmKCQoZWxlbSlbMF0pe1xyXG4gICAgICAgICAgICAgICAgJChlbGVtKVswXS5zdHlsZVtwcm9wXSA9IHZhbHVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIm1ldGhvZCBbc2V0U3R5bGVdIC0gZXJyb3IgOlwiICwgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0gdmlzaWJpbGl0eVxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgc2V0VmlzaWJsZSh2aXNpYmlsaXR5OiBib29sZWFuKTogdm9pZCB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgdGhpcy52aXNpYmxlPSB2aXNpYmlsaXR5O1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJzZXRWaXNpYmxlIG1ldGhvZFwiICwgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcblxyXG4gICAgcHVibGljIF9zcHlDaGFuZ2VzKGV2ZW50OiBhbnkpOiB2b2lkIHtcclxuICAgICAgICBpZiAodGhpcy5vcmlnaW5hbFZhbHVlID09IGV2ZW50KSB7XHJcbiAgICAgICAgICAgIHRoaXMub25TcHlOb0NoYW5nZS5lbWl0KHsgXCJ0YXJnZXRcIjogdGhpcywgXCJ2YWx1ZVwiOiBldmVudCB9KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMub25TcHlDaGFuZ2UuZW1pdCh7IFwidGFyZ2V0XCI6IHRoaXMsIFwidmFsdWVcIjogZXZlbnQgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyByZXNldE9yaWdpbmFsVmFsdWUodmFsdWUpIHtcclxuICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWUgPSB2YWx1ZTtcclxuICAgICAgICB0aGlzLl9zcHlDaGFuZ2VzKHZhbHVlKTtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgY2xvbmUgKCl7XHJcbiAgICAgICAgLy8gVE9ETy4uLlxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyB2YWxpZGF0ZVJlc3RyaWN0KHRleHQpe1xyXG4gICAgICAgIGlmKHRoaXMucmVzdHJpY3QgJiYgdGV4dCAgICl7XHJcbiAgICAgICAgICAgIHRleHQgPSB0ZXh0LnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnRyb2wgPSBuZXcgRm9ybUNvbnRyb2wodGV4dCAsIFZhbGlkYXRvcnMucGF0dGVybignWycrdGhpcy5yZXN0cmljdCsnXScpKTtcclxuICAgICAgICAgICAgdmFyIHBlcm1pdHRlZENoYXJzID0gJ1snICsgdGhpcy5yZXN0cmljdCArICddJztcclxuICAgICAgICAgICAgcmV0dXJuICgodGV4dC5tYXRjaCggbmV3IFJlZ0V4cChwZXJtaXR0ZWRDaGFycywgXCJnaVwiKSApKSA9PSBudWxsID8gXCJcIiA6ICh0ZXh0Lm1hdGNoKCBuZXcgUmVnRXhwKHBlcm1pdHRlZENoYXJzLCBcImdpXCIpICkpLmpvaW4oJycpICApO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdGV4dDtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgdmFsaWRhdGVNYXhDaGFyKHRleHQpe1xyXG4gICAgICAgIGNvbnN0IGNvbnRyb2wgPSBuZXcgRm9ybUNvbnRyb2wodGV4dCAsIFZhbGlkYXRvcnMubWF4TGVuZ3RoKHRoaXMubWF4Q2hhcnMpKTtcclxuICAgICAgICBpZighY29udHJvbC52YWxpZCl7XHJcbiAgICAgICAgICAgIHJldHVybiB0ZXh0LnN1YnN0cigwLHRoaXMubWF4Q2hhcnMpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdGV4dDtcclxuICAgIH1cclxuXHJcblxyXG5cclxuICAgIC8qKlxyXG4gICAgICogbmdPbkRlc3Ryb3lcclxuICAgICAqL1xyXG4gICAgbmdPbkRlc3Ryb3koKTogdm9pZCB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJDb250YWluZXIgLT4gbmdPbkRlc3Ryb3kgLT4gbmdPbkRlc3Ryb3kgKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVwiKVxyXG4gICAgICAgIHRyeXtcclxuICAgICAgICAgICAgdGhpcy5yZW1vdmVBbGxPdXB1dHNFdmVudHNMaXN0ZW5lcnMoIHRoaXMuX2hvc3RFbGVtZW50ICk7XHJcblxyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fX2VsZW1lbnQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9fY29tbW9uO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5jb21wb25lbnRzO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fZG9tRWxlbWVudDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMub3JpZ2luYWxWYWx1ZTtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX2lzbG9hZGVkO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fSXNJbmNsdWRlSW5MYXlvdXQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9pc1Zpc2libGU7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9pc0VuYWJsZWQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9fcGFkZGluZ1RvcDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX19wYWRkaW5nTGVmdDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX19wYWRkaW5nQm90dG9tO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fX3BhZGRpbmdSaWdodDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX19tYXJnaW5Cb3R0b207XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9fbWFyZ2luTGVmdDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX19tYXJnaW5SaWdodDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX19tYXJnaW5Ub3A7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9fd2lkdGg7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9faGVpZ2h0O1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fX2xlZnQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9fcmlnaHQ7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9faG9yaXpvbnRhbEdhcCAgO1xyXG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fX3ZlcnRpY2FsR2FwICA7XHJcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9faG9yaXpvbnRhbEFsaWduIDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX192ZXJ0aWNhbEFsaWduIDtcclxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX25hbWU7XHJcbiAgICAgICAgfWNhdGNoKGVycm9yICl7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2Vycm9yIDonLGVycm9yKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbn1cclxuXHJcblxyXG4vLyBleHBvcnQgZnVuY3Rpb24gQXV0b1Vuc3Vic2NyaWJlKGNvbnN0cnVjdG9yKSB7XHJcblxyXG5cclxuXHJcbi8vICAgICBjb25zdHJ1Y3Rvci5wcm90b3R5cGUubmdPbkRlc3Ryb3kgPSBmdW5jdGlvbiAoKSB7XHJcbi8vICAgICAgICAgZm9yIChsZXQgcHJvcCBpbiB0aGlzKSB7XHJcbi8vICAgICAgICAgICAgIHRyeSB7XHJcbi8vICAgICAgICAgICAgICAgICBjb25zdCBwcm9wZXJ0eSA9IHRoaXNbcHJvcF07XHJcbi8vICAgICAgICAgICAgICAgICBpZiAocHJvcGVydHkgJiYgKHR5cGVvZiBwcm9wZXJ0eS51bnN1YnNjcmliZSA9PT0gXCJmdW5jdGlvblwiKSkge1xyXG4vLyAgICAgICAgICAgICAgICAgICAgIHByb3BlcnR5LnVuc3Vic2NyaWJlKCk7XHJcbi8vICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJjb25zdHJ1Y3Rvci5wcm90b3R5cGUubmdPbkRlc3Ryb3kgLT4gcHJvcGVydHlcIiwgcHJvcClcclxuLy8gICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocHJvcGVydHkgaW5zdGFuY2VvZiBUaW1lcikge1xyXG4vLyAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHByb3AgKyBcIiBcIiArIChwcm9wZXJ0eSBpbnN0YW5jZW9mIFRpbWVyKSk7IC8vIFdpbGwgYmUgdHJ1ZVxyXG4vLyAgICAgICAgICAgICAgICAgICAgIChwcm9wZXJ0eSBhcyBUaW1lcikuc3RvcCgpO1xyXG4vLyAgICAgICAgICAgICAgICAgfVxyXG4vLyAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4vLyAgICAgICAgICAgICBjb25zb2xlLmxvZyhcImNvbnN0cnVjdG9yLnByb3RvdHlwZS5uZ09uRGVzdHJveSAtPiBlcnJvclwiLCBlcnJvcilcclxuXHJcbi8vICAgICAgICAgICAgIH1cclxuLy8gICAgICAgICB9XHJcbi8vICAgICB9O1xyXG5cclxuLy8gICAgIGNvbnN0IG9yaWdpbmFsID0gY29uc3RydWN0b3IucHJvdG90eXBlLm5nT25EZXN0cm95O1xyXG4vLyAgICAgb3JpZ2luYWwgJiYgdHlwZW9mIG9yaWdpbmFsID09PSBcImZ1bmN0aW9uXCIgJiYgb3JpZ2luYWwuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcclxuXHJcbi8vIH1cclxuIl19