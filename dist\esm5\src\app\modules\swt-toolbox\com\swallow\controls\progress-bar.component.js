/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild, Input } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtProgressBar = /** @class */ (function () {
    function SwtProgressBar() {
        // get progress.
        this.progress = null;
        // get progressBar label.
        this.progressLabel = null;
        this._width = "200";
        this._label = "";
        this._lineheight = 13;
        this._striped = false;
        this.labelColor = "#FFF";
        this.labelSize = 11;
        this._value = 0;
        this._maximum = 100;
        this._minimum = 0;
    }
    Object.defineProperty(SwtProgressBar.prototype, "striped", {
        get: /**
         * @return {?}
         */
        function () {
            return this._striped;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._striped = true;
                    $($(this.progressBar.nativeElement)[0].children[0]).addClass("stripedbg");
                    $($(this.progressBar.nativeElement)[0].children[0]).removeClass("progress");
                }
                else {
                    this._striped = false;
                    $($(this.progressBar.nativeElement)[0].children[0]).removeClass("stripedbg");
                    $($(this.progressBar.nativeElement)[0].children[0]).addClass("progress");
                }
            }
            else {
                this._striped = value;
                if (value) {
                    $($(this.progressBar.nativeElement)[0].children[0]).addClass("stripedbg");
                    $($(this.progressBar.nativeElement)[0].children[0]).removeClass("progress");
                }
                else {
                    $($(this.progressBar.nativeElement)[0].children[0]).removeClass("stripedbg");
                    $($(this.progressBar.nativeElement)[0].children[0]).addClass("progress");
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtProgressBar.prototype, "width", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () { return this._width; },
        // width setter method
        set: 
        // width setter method
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(this.progressBar.nativeElement).width(value);
            this._width = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtProgressBar.prototype, "height", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () { return this._height; },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = value;
            value.indexOf("%") === -1 ? this._lineheight = Number(value) - 2 : null;
            $(this.progressBar.nativeElement).height(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtProgressBar.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        // input to handle label
        set: 
        // input to handle label
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._label = value;
            $(this.progressLabel).text(this.label);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtProgressBar.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        setTimeout((/**
         * @return {?}
         */
        function () {
            _this.progress = _this.progressBar.nativeElement.children[0];
            _this.progressLabel = _this.progressBar.nativeElement.children[0].children[0];
            if (_this.minimum > 0) {
                _this.value = _this.minimum;
            }
            $(_this.progressBar.nativeElement).width(_this.width);
            $(_this.progressBar.nativeElement).css("margin-top", _this.marginTop + "px");
            $(_this.progressBar.nativeElement).css("margin-right", _this.marginRight + "px");
            $(_this.progressBar.nativeElement).css("margin-bottom", _this.marginBottom + "px");
            $(_this.progressBar.nativeElement).css("margin-left", _this.marginLeft + "px");
            $(_this.progressLabel).css("line-height", _this._lineheight + "px");
            $(_this.progressLabel).css("color", _this.labelColor);
            $(_this.progressLabel).css("font-size", _this.labelSize + "px");
            $(_this.progressBar.nativeElement).css("background-color", "green");
            $(_this.progress).css("background-color", _this.progressColor);
            $(_this.progressLabel).text(_this.label);
        }), 0);
    };
    Object.defineProperty(SwtProgressBar.prototype, "maximum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maximum;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (value > 0) {
                    this._maximum = value;
                }
                else {
                    throw new Error("value must be greater then 0");
                }
            }
            catch (error) {
                console.error("SwtProgressBar - [ maximum ] method error:", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtProgressBar.prototype, "minimum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minimum;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (value >= 0) {
                    this._minimum = value;
                    this.value = value;
                }
                else {
                    throw new Error("value must be greater then 0");
                }
            }
            catch (error) {
                console.error("SwtProgressBar - [ minimum ] method error:", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtProgressBar.prototype, "value", {
        get: /**
         * @return {?}
         */
        function () {
            return this._value;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (value >= 0) {
                    if (value <= this.maximum) {
                        this._value = (100 * value) / this.maximum;
                        $(this.progress).width(Math.trunc(this.value) + '%');
                    }
                    else {
                        this._value = this._maximum;
                    }
                }
                else {
                    throw new Error("value must be positive.");
                }
            }
            catch (error) {
                console.error("SwtProgressBar - [ maximum ] method error:", error);
            }
        },
        enumerable: true,
        configurable: true
    });
    SwtProgressBar.decorators = [
        { type: Component, args: [{
                    selector: 'SwtProgressBar',
                    template: "\n    <div #progressBar class=\"progressBar\">\n      <div class=\"progress\"><span id=\"label\"></span></div>\n    </div>\n  ",
                    styles: ["\n           .progressBar {\n              width: 100%;\n              height: 15px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#E7E7E7), to(#FDFDFD));\n              background-image: -webkit-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -moz-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -ms-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -o-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: linear-gradient(to bottom, #E7E7E7, #FDFDFD);\n              border: 1px solid #808283;\n            }\n            span {\n                display: block;\n                width:100%;\n                height: 15px;\n                text-align: center;\n                color: #000;\n                font-size: 11px;\n                font-weight: bold;\n                font-family: verdana,helvetica;\n            }\n            .progress {\n              width: 0%;\n              height: 100%;\n              border-radius: 0px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#5EC1FF), to(#009DFF));\n              background-image: -webkit-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -moz-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -ms-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -o-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: linear-gradient(to bottom, #5EC1FF, #009DFF);\n            }\n            .stripedbg {\n                width: 0%;\n                height: 100%;\n                border-radius: 0px;\n                background-image: repeating-linear-gradient(45deg, #70CAF8, #70CAF8 15px, #3F99E6 15px, #3F99E6 30px);\n                -webkit-animation-name: move;\n                -webkit-animation-duration: 2s;\n                -webkit-animation-timing-function: linear;\n                -webkit-animation-iteration-count: infinite;\n                animation-name: move;\n                animation-duration: 2s;\n                animation-timing-function: linear;\n                animation-iteration-count: infinite;\n            }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtProgressBar.ctorParameters = function () { return []; };
    SwtProgressBar.propDecorators = {
        progressBar: [{ type: ViewChild, args: ["progressBar",] }],
        striped: [{ type: Input }],
        width: [{ type: Input }],
        height: [{ type: Input }],
        label: [{ type: Input }],
        labelColor: [{ type: Input, args: ["labelColor",] }],
        labelSize: [{ type: Input, args: ["labelSize",] }],
        progressColor: [{ type: Input, args: ["progressColor",] }],
        background: [{ type: Input, args: ["background",] }],
        marginTop: [{ type: Input, args: ["marginTop",] }],
        marginLeft: [{ type: Input, args: ["marginLeft",] }],
        marginBottom: [{ type: Input, args: ["marginBottom",] }],
        marginRight: [{ type: Input, args: ["marginRight",] }],
        value: [{ type: Input }]
    };
    return SwtProgressBar;
}());
export { SwtProgressBar };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype.progressBar;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype.progress;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype.progressLabel;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._lineheight;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._striped;
    /** @type {?} */
    SwtProgressBar.prototype.labelColor;
    /** @type {?} */
    SwtProgressBar.prototype.labelSize;
    /** @type {?} */
    SwtProgressBar.prototype.progressColor;
    /** @type {?} */
    SwtProgressBar.prototype.background;
    /** @type {?} */
    SwtProgressBar.prototype.marginTop;
    /** @type {?} */
    SwtProgressBar.prototype.marginLeft;
    /** @type {?} */
    SwtProgressBar.prototype.marginBottom;
    /** @type {?} */
    SwtProgressBar.prototype.marginRight;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._value;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._maximum;
    /**
     * @type {?}
     * @private
     */
    SwtProgressBar.prototype._minimum;
}
//# sourceMappingURL=data:application/json;base64,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