/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component } from '@angular/core';
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
const select2 = require('select2');
import 'jquery-ui-dist/jquery-ui';
import { SwtDataExport } from './swt-data-export.component';
import { ExportInProgress } from './ExportInProgress';
import { SwtPagesToExport } from './PagesToExport';
import { ExportEvent } from '../events/swt-events.module';
import { Alert } from '../utils/alert.component';
import { StringUtils } from "../utils/string-utils.service";
import { ExternalInterface } from '../utils/external-interface.service';
export class DataExportMultiPage extends SwtDataExport {
    constructor() {
        super(...arguments);
        //Report type. CSV/Excel/PDF
        this._reportType = null;
        this._exportCancelFunction = new Function();
        //Export function (callback function)
        this._exportFunction = new Function();
        this._closePopupWindow = new Function();
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.reportProgress = new ExportInProgress(this.elementReference, this.commonServiceRef);
        this.pagesToExport = new SwtPagesToExport(this.elementReference, this.commonServiceRef);
        this.pagesToExport.onexportFunction = this.showReportProgress.bind(this);
        this.pagesToExport.exportCancelFunction = this.closeCancelPopup.bind(this);
        this.reportProgress.exportCancelFunction = this._exportCancelFunction.bind(this);
    }
    /**
     * @return {?}
     */
    get exportCancelFunction() {
        return this._exportCancelFunction;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set exportCancelFunction(value) {
        this._exportCancelFunction = value;
        this.reportProgress.exportCancelFunction = this._exportCancelFunction.bind(this);
    }
    /**
     * @return {?}
     */
    get exportFunction() {
        return this._exportFunction;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set exportFunction(value) {
        this._exportFunction = value;
        this.pagesToExport.onexportFunction = this.showReportProgress.bind(this);
    }
    /**
     * @return {?}
     */
    get closePopupWindow() {
        return this._closePopupWindow;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set closePopupWindow(value) {
        this._closePopupWindow = value;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onDataExportClick(event) {
        this._reportType = this.typeFromIndex(this.exportDataComponent.selectedIndex);
        if (this._totalPages == 1)
            this.generateReport(this._currentPage, 1);
        else {
            // if (this.pagesToExport) {
            // 	//Popup is already created, set visibility to true
            // 	this.pagesToExport.visible = true;
            // } else {
            //Popup not yet created, so create the same
            this.createPopup();
            // }
            //Show popup
            // PopUpManager.centerPopUp(this._optionPopup);
            // PopUpManager.bringToFront(this._optionPopup);
            //Set default selection and focus
            // this._rbCurrentPage.selected = true;
            // this._rbCurrentPage.setFocus();
        }
        // this.createPopup();
    }
    /**
     * @return {?}
     */
    createPopup() {
        this.pagesToExport.show(this);
    }
    /**
     * This function closes the option popup
     * @private
     * @return {?}
     */
    closePopup() {
        this.pagesToExport.hide(this);
    }
    /**
     * @return {?}
     */
    showReportProgress() {
        // this.reportProgress.show(this);
        // ExternalInterface.call('getBundle', 'text', 'label-exportMultiPages', 'First {0} pages will be exported.\nDo you want to continue?'), 
        if (this.pagesToExport.pagesToExport == "current") {
            this.generateReport(this._currentPage, 1);
        }
        else {
            if (this._totalPages > this._maxPages) {
                /** @type {?} */
                var confirmMsg = StringUtils.substitute(ExternalInterface.call('getBundle', 'text', 'label-exportMultiPages', 'First {0} pages will be exported.\nDo you want to continue?'), this._maxPages);
                // SwtAlert.getInstance().show(confirmMsg, "Confirm", 0x0003, null, onConfimClose, null, Alert.YES);	
                console.log("TCL: DataExportMultiPage -> showReportProgress -> confirmMsg", confirmMsg);
                this.SwtAlert.warning(confirmMsg, null, Alert.YES | Alert.NO, null, this.onConfimClose.bind(this), Alert.NO);
            }
            else {
                this.closePopup();
                this.generateReport(1, this._totalPages);
            }
        }
        // ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        // this._exportFunction();
    }
    /**
     * This function is used to generate report on click of
     * OK button on confim dialog
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    onConfimClose(event) {
        //Close the popup window
        if (event.detail == Alert.YES) {
            this.closePopup();
            //pop up the progress bar window
            this.progresswinPopup();
            this.generateReport(1, this._maxPages);
        }
    }
    /**
     * @private
     * @param {?} startPage
     * @param {?} noOfPages
     * @return {?}
     */
    generateReport(startPage, noOfPages) {
        /** @type {?} */
        let exportEvent = new Object();
        exportEvent['type'] = this._reportType;
        exportEvent['startPage'] = startPage;
        exportEvent['noOfPages'] = noOfPages;
        ExportEvent.emit(exportEvent);
        if (this._exportFunction != null) {
            this._exportFunction(this._reportType, startPage, noOfPages);
        }
    }
    /**
     * @return {?}
     */
    closeCancelPopup() {
        this.reportProgress.hide(this);
    }
    /**
     * This function is used to create option popup window
     * @return {?}
     */
    progresswinPopup() {
        this.reportProgress.show(this);
        /*var titleWindow:TitleWindow;
        titleWindow = PopUpManager.createPopUp(this, ExportInProgress, true) as TitleWindow;
        titleWindow.addEventListener("closeeee", onProgressCancelClick, false, 0, true);
        PopUpManager.centerPopUp(titleWindow);
*/
    }
    /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    onProgressCancelClick(event) {
        this.closeCancelPopup();
        this._exportCancelFunction(event);
    }
    ////////////////////////////////////////////////////////////////
    //	GETTERS & SETTERS
    ////////////////////////////////////////////////////////////////
    /**
     * Setter function for currentPage
     * @param {?} currentPage
     * @return {?}
     */
    set currentPage(currentPage) {
        this._currentPage = currentPage;
    }
    /**
     * Getter function for currentPage
     * @return {?}
     */
    get currentPage() {
        return this._currentPage;
    }
    /**
     * Setter function for totalPages
     * @param {?} totalPages
     * @return {?}
     */
    set totalPages(totalPages) {
        this._totalPages = totalPages;
    }
    /**
     * Getter function for totalPages
     * @return {?}
     */
    get totalPages() {
        return this._totalPages;
    }
    /**
     * Setter function for maxPages
     * @param {?} maxPages
     * @return {?}
     */
    set maxPages(maxPages) {
        this._maxPages = maxPages;
    }
    /**
     * Getter function for maxPages
     * @return {?}
     */
    get maxPages() {
        return this._maxPages;
    }
}
DataExportMultiPage.decorators = [
    { type: Component, args: [{
                selector: 'DataExportMultiPage',
                template: `
  <div>
			<SwtComboBox id="exportDataComponent" (inputClick)="onDataExportClick($event)" 
			(change)="onDataExportClick($event)" editable="false" #exportDataComponent width="43"></SwtComboBox>
  </div>
  `,
                styles: [`
  `]
            }] }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._reportType;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._currentPage;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._totalPages;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._maxPages;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype.reportProgress;
    /** @type {?} */
    DataExportMultiPage.prototype.pagesToExport;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._exportCancelFunction;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._exportFunction;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._closePopupWindow;
}
//# sourceMappingURL=data:application/json;base64,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