/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵStyleData } from '@angular/animations';
import { AnimationEngineInstruction } from '../render/animation_engine_instruction';
import { AnimationTimelineInstruction } from './animation_timeline_instruction';
export interface AnimationTransitionInstruction extends AnimationEngineInstruction {
    element: any;
    triggerName: string;
    isRemovalTransition: boolean;
    fromState: string;
    fromStyles: ɵStyleData;
    toState: string;
    toStyles: ɵStyleData;
    timelines: AnimationTimelineInstruction[];
    queriedElements: any[];
    preStyleProps: Map<any, {
        [prop: string]: boolean;
    }>;
    postStyleProps: Map<any, {
        [prop: string]: boolean;
    }>;
    totalTime: number;
    errors?: any[];
}
export declare function createTransitionInstruction(element: any, triggerName: string, fromState: string, toState: string, isRemovalTransition: boolean, fromStyles: ɵStyleData, toStyles: ɵStyleData, timelines: AnimationTimelineInstruction[], queriedElements: any[], preStyleProps: Map<any, {
    [prop: string]: boolean;
}>, postStyleProps: Map<any, {
    [prop: string]: boolean;
}>, totalTime: number, errors?: any[]): AnimationTransitionInstruction;
