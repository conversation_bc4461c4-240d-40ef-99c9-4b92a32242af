/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { StringUtils } from './../utils/string-utils.service';
import { SwtButton } from './../controls/swt-button.component';
import { SwtUtil } from './../utils/swt-util.service';
import { HTTPComms } from './../communication/httpcomms.service';
import { JSONReader } from './../jsonhandler/jsonreader.service';
import { Alert } from './../utils/alert.component';
import { ExternalInterface } from './../utils/external-interface.service';
import { CommonService } from './../utils/common.service';
import { SwtAlert } from './../utils/swt-alert.service';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from './../containers/swt-container.component';
import { Component, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { CustomTreeEvent } from '../events/swt-events.module';
import { SwtLabel } from '../controls/swt-label.component';
var EnhancedAlertingTooltip = /** @class */ (function (_super) {
    tslib_1.__extends(EnhancedAlertingTooltip, _super);
    function EnhancedAlertingTooltip(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.ITEM_CLICK = new EventEmitter();
        _this.DISPLAY_LIST_CLICK = new EventEmitter();
        _this.LINK_TO_SPECIF_CLICK = new EventEmitter();
        _this.clickable = false;
        _this.jsonReader = new JSONReader();
        /**
         * Communication Objects
         *
         */
        _this.inputData = new HTTPComms(_this.commonService);
        _this.baseURL = SwtUtil.getBaseURL();
        _this.actionMethod = "";
        _this.actionPath = "";
        _this.requestParams = [];
        _this.hostId = "";
        _this.swtAlert = new SwtAlert(commonService);
        return _this;
    }
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        // this.dataForLabel.text = ExternalInterface.call('getBundle', 'text', 'label-dataFor', 'Data For');
        // this.labelnumberAccounts.text = ExternalInterface.call('getBundle', 'text', 'label-numberAccounts', 'Number of accounts');
        // this.labelnewDataExistFor.text = ExternalInterface.call('getBundle', 'text', 'label-newDataExistFor', 'New data exist for');
        this.closeButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.closeButton', null);
        this.displayListButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.displayListButton', null);
        this.linkToSpecificButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.linkToSpecificButton', null);
        this.linkToSpecificButton.enabled = false;
        // this.customTooltip.focusOut = this.boxRollOutEventListner;
    };
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.closeTooltip = /**
     * @return {?}
     */
    function () {
        console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 118 ~ EnhancedAlertingTooltip ~ closeTooltip ~ closeTooltip");
        /** @type {?} */
        var fromJSP = ExternalInterface.call('eval', 'fromJSP');
        console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 120 ~ EnhancedAlertingTooltip ~ closeTooltip ~ fromJSP", fromJSP);
        if (StringUtils.isTrue(fromJSP)) {
            window.close();
        }
        else {
            this.processBox.removeTooltip();
        }
    };
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.displayListEventhandler = /**
     * @return {?}
     */
    function () {
        console.log(this.jsonReader.getSingletons().params);
        /** @type {?} */
        var selectedParamList = "";
        if (this.tree.selectedItem)
            selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        /** @type {?} */
        var allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        var dataArray = [];
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        var target = {
            itemParamList: selectedParamList,
            dataParams: dataArray,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            noode: this.tree.selectedItem,
            facilityId: this.parentDocument.tooltipFacilityId,
        };
        this.DISPLAY_LIST_CLICK.emit(target);
    };
    /**
     * @param {?} item
     * @param {?} paramsList
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.getParamsList = /**
     * @param {?} item
     * @param {?} paramsList
     * @return {?}
     */
    function (item, paramsList) {
        try {
            if (item) {
                if (paramsList) {
                    paramsList += " AND " + item.treeLevelName + "='" + item.treeLevelValue + "'";
                }
                else {
                    paramsList += item.treeLevelName + "='" + item.treeLevelValue + "'";
                }
                if (item.parentData) {
                    return this.getParamsList(item.parentData, paramsList);
                }
            }
            return paramsList;
        }
        catch (error) {
            console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 130 ~ EnhancedAlertingTooltip ~ getParamsList ~ error", error);
        }
    };
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.linkToSpecifiEventHandler = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var selectedParamList = "";
        selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        /** @type {?} */
        var allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        var dataArray = [];
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        var target = {
            hostId: this.hostId,
            itemParamList: selectedParamList,
            dataParams: dataArray,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            noode: this.tree.selectedItem,
        };
        this.LINK_TO_SPECIF_CLICK.emit(target);
    };
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.createCustomTip();
        /*jQuery(this.customTooltip.domElement).mouseleave(() => {
            jQuery(this.customTooltip.domElement).off('mouseleave');
            this.processBox.removeTooltip();
        });*/
        this.tree.ITEM_ACTIVATE.subscribe((/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            _this.treeEventHandler(item);
        }));
        this.tree.addEventListener(CustomTreeEvent.ITEMRENDER, (/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            /** @type {?} */
            var name = item.data.name;
            /** @type {?} */
            var ALERTABLE = "Y";
            /** @type {?} */
            var alert = item.data.alert;
            /** @type {?} */
            var isBranch = item.data.isBranch;
            if (isBranch == true) {
                item.setStyle("fontWeight", "normal");
                item.setStyle("fontSize", 12);
                item.setStyle("color", "black");
            }
            else {
                if (alert == ALERTABLE) {
                    item.setStyle("fontSize", 12);
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/alert.png')");
                    }), 0);
                }
                else {
                    item.setStyle("fontWeight", "normal");
                    item.setStyle("fontSize", 12);
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/treefile.png')");
                    }), 0);
                }
            }
        }));
    };
    /**
     * @param {?} item
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.treeEventHandler = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        console.log(this.jsonReader.getSingletons().params);
        /** @type {?} */
        var selectedParamList = "";
        /** @type {?} */
        var dataArray = [];
        selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        var allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        var target = {
            itemParamList: selectedParamList,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            dataParams: dataArray,
            noode: this.tree.selectedItem,
        };
        this.ITEM_CLICK.emit(target);
    };
    /**
     * @param {?} parentData
     * @param {?} dataArray
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.extractParentFilterDataFromNode = /**
     * @param {?} parentData
     * @param {?} dataArray
     * @return {?}
     */
    function (parentData, dataArray) {
        if (parentData != null && parentData != undefined && parentData.treeLevelName != null) {
            dataArray[parentData.treeLevelName] = parentData.treeLevelValue;
            dataArray = this.extractParentFilterDataFromNode(parentData.parentData, dataArray);
        }
        return dataArray;
    };
    /**
     * @private
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.boxRollOutEventListner = /**
     * @private
     * @return {?}
     */
    function () {
        console.log("ConfigurableToolTip -> boxRollOutEventListner -> boxRollOutEventListner");
    };
    /**
     * @private
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.createCustomTip = /**
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            this.requestParams = [];
            this.inputData.cbStart = this.startOfComms.bind(this);
            this.inputData.cbStop = this.endOfComms.bind(this);
            this.inputData.cbResult = (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.inputDataResult(event);
            });
            this.inputData.cbFault = this.inputDataFault.bind(this);
            this.inputData.encodeURL = false;
            this.actionPath = 'scenarioSummary.do?';
            this.actionMethod = 'method=getAlertingTooltipDetails';
            this.requestParams['entityId'] = this.parentDocument.tooltipEntityId;
            this.requestParams['currencyCode'] = this.parentDocument.tooltipCurrencyCode;
            this.requestParams['facilityId'] = this.parentDocument.tooltipFacilityId;
            this.requestParams['selectedDate'] = this.parentDocument.tooltipSelectedDate;
            this.requestParams['selectedAccountId'] = this.parentDocument.tooltipSelectedAccount;
            this.requestParams['selectedMvtId'] = this.parentDocument.tooltipMvtId;
            this.requestParams['selectedMatchId'] = this.parentDocument.tooltipMatchId;
            if (this.parentDocument.tooltipOtherParams != null) {
                for (var key in this.parentDocument.tooltipOtherParams) {
                    if (this.parentDocument.tooltipOtherParams.hasOwnProperty(key))
                        this.requestParams[key] = this.parentDocument.tooltipOtherParams[key];
                }
            }
            this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
            this.inputData.send(this.requestParams);
        }
        catch (error) {
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.inputDataResult = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        try {
            // Checks the inputData and stops the communication
            if (this.inputData.isBusy()) {
                this.inputData.cbStop();
            }
            else {
                this.lastRecievedJSON = event;
                this.jsonReader.setInputJSON(this.lastRecievedJSON);
                if (this.jsonReader.getRequestReplyStatus()) {
                    if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
                        if (!this.jsonReader.isDataBuilding()) {
                            this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null) + " (" + this.jsonReader.getSingletons().total + ") ";
                            this.facilityName.text = this.jsonReader.getSingletons().facilityName;
                            this.hostId = this.jsonReader.getSingletons().hostId;
                            /** @type {?} */
                            var parameters = (this.jsonReader.getSingletons().params).split("$#$,").join("\n");
                            /** @type {?} */
                            var parametersHtml = this.htmlEntities(parameters);
                            this.paramsList.htmlText = parametersHtml;
                            /** @type {?} */
                            var treeData = this.lastRecievedJSON.scenarioDetails.tree.root.node;
                            if (this.tree.dataProvider != treeData) {
                                this.tree.expandAll(CustomTree.LEVEL_1_STR);
                                this.tree.dataProvider = treeData;
                            }
                        }
                    }
                }
                else {
                    if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
                        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
                    }
                }
            }
        }
        catch (error) {
            console.log("EnhancedAlertingTooltip -> inputDataResult -> error", error);
        }
    };
    /**
     * @param {?} str
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.htmlEntities = /**
     * @param {?} str
     * @return {?}
     */
    function (str) {
        //Variable for errorLocation
        /** @type {?} */
        var errorLocation = 0;
        try {
            return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
                replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
        }
        catch (error) {
            console.log('error', error, str);
            SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MessageDetails.ts', "htmlEntities", errorLocation);
        }
    };
    /**
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.startOfComms = /**
     * @return {?}
     */
    function () {
    };
    /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     */
    /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.endOfComms = /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     * @return {?}
     */
    function () {
    };
    /**
     * If a fault occurs with the connection with the server then display the lost connection label
     * @param event:FaultEvent
     **/
    /**
     * If a fault occurs with the connection with the server then display the lost connection label
     * @private
     * @param {?} event
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.inputDataFault = /**
     * If a fault occurs with the connection with the server then display the lost connection label
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
        this.swtAlert.show("fault " + this._invalidComms);
    };
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     */
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.recalculateDataAlert = /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.clickable) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-recalculateConfirm', 'Are you sure? This may take some time?'), //text
            ExternalInterface.call('getBundle', 'text', 'label-recalculateConfirmAlertTitle', 'Are you sure? This may take some time?'), Alert.OK | Alert.CANCEL, this, //parent
            this.alertListener.bind(this), //close handler
            null, Alert.CANCEL); //icon and default button
        }
    };
    /**
     * This function is used to listen to the alert
     *
     * @param eventObj CloseEvent
     */
    /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    EnhancedAlertingTooltip.prototype.alertListener = /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    function (eventObj) {
        // Checks for Alert OK
        if (eventObj.detail == Alert.OK) {
            // Recalculate data if "OK" is clicked
            this.parentDocument.recalculateDataFunction();
        }
    };
    EnhancedAlertingTooltip.decorators = [
        { type: Component, args: [{
                    selector: 'EnhancedAlertingTooltip',
                    template: "\n        <VBox  paddingLeft=\"3\"  paddingRight=\"3\" paddingTop=\"3\" #customTooltip width=\"100%\" height=\"100%\" verticalGap=\"2\"> \n            <VBox id=\"treeContainer\" #treeContainer width=\"100%\" height=\"100%\" class=\"left\">\n            <HBox width=\"100%\" height=\"4%\">\n                <SwtLabel #ccyLabel  paddingLeft=\"5\" textDictionaryId=\"label.alertSummaryTooltip.facility\" ></SwtLabel>\n                <SwtLabel  id=\"facilityName\" #facilityName\n                        fontWeight=\"normal\"\n                        paddingLeft=\"5\">\n                </SwtLabel>\n            </HBox>\n            \n\n            <HBox height=\"8%\" width=\"100%\">\n                <SwtLabel #ccyLabel paddingLeft=\"5\" textDictionaryId=\"label.alertSummaryTooltip.parameters\" ></SwtLabel>\n                <SwtLabel  id=\"paramsList\" #paramsList\n                fontWeight=\"normal\"\n                paddingLeft=\"5\"></SwtLabel>\n            </HBox>\n            <HBox height=\"5%\" width=\"100%\" paddingBottom=\"7\">\n            <SwtLabel  id=\"treeTitle\" #treeTitle\n                    fontWeight=\"normal\"\n                    paddingLeft=\"5\">\n            </SwtLabel>\n            </HBox>\n            <CustomTree id=\"tree\" #tree\n                        width=\"100%\"\n                        height=\"75%\"\n                        doubleClickEnabled=\"true\">\n            </CustomTree>\n\n\n            <HBox height=\"6%\" width=\"100%\" paddingTop=\"7\">\n            <HBox height=\"100%\" width=\"35%\">\n                <SwtButton  buttonMode=\"true\"\n                        id=\"closeButton\" #closeButton\n                            (click)=\"closeTooltip()\"> </SwtButton>\n            </HBox>\n            <HBox height=\"100%\" width=\"65%\" horizontalAlign=\"right\">\n            <SwtButton (click)=\"displayListEventhandler()\"\n            buttonMode=\"true\"\n                            id=\"displayListButton\" #displayListButton></SwtButton>\n            <SwtButton buttonMode=\"true\" \n             id=\"linkToSpecificButton\" #linkToSpecificButton\n                        (click)=\"linkToSpecifiEventHandler()\"> </SwtButton>\n            </HBox>\n            </HBox>\n            </VBox>\n        </VBox>\n  ",
                    styles: ["\n      "]
                }] }
    ];
    /** @nocollapse */
    EnhancedAlertingTooltip.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    EnhancedAlertingTooltip.propDecorators = {
        customTooltip: [{ type: ViewChild, args: ['customTooltip',] }],
        tree: [{ type: ViewChild, args: ['tree',] }],
        treeTitle: [{ type: ViewChild, args: ['treeTitle',] }],
        facilityName: [{ type: ViewChild, args: ['facilityName',] }],
        paramsList: [{ type: ViewChild, args: ['paramsList',] }],
        closeButton: [{ type: ViewChild, args: ['closeButton',] }],
        linkToSpecificButton: [{ type: ViewChild, args: ['linkToSpecificButton',] }],
        displayListButton: [{ type: ViewChild, args: ['displayListButton',] }],
        ITEM_CLICK: [{ type: Output, args: ['ITEM_CLICK',] }],
        DISPLAY_LIST_CLICK: [{ type: Output, args: ['DISPLAY_LIST_CLICK',] }],
        LINK_TO_SPECIF_CLICK: [{ type: Output, args: ['LINK_TO_SPECIF_CLICK',] }]
    };
    return EnhancedAlertingTooltip;
}(Container));
export { EnhancedAlertingTooltip };
if (false) {
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.customTooltip;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.tree;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.treeTitle;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.facilityName;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.paramsList;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.closeButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.linkToSpecificButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.displayListButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.ITEM_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.DISPLAY_LIST_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.LINK_TO_SPECIF_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.dataArray;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.parentDocument;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.clickable;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.processBox;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.jsonReader;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.lastRecievedJSON;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.prevRecievedJSON;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.initReceivedJSON;
    /**
     * Communication Objects
     *
     * @type {?}
     */
    EnhancedAlertingTooltip.prototype.inputData;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.baseURL;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.actionMethod;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype._invalidComms;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.hostId;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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