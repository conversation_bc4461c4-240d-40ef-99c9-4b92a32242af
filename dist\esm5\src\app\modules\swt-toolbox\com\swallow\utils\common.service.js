/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ApplicationRef, ComponentFactoryResolver, Inject, Injectable, Injector, NgModuleFactoryLoader } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import 'jquery-ui-dist/jquery-ui';
import { WindowManager } from '../managers/window-manager.service';
/** @type {?} */
var jp = require('jsonpath/jsonpath.min');
/**
 * This service is a hub of service.
 * it used to resolve the problem of dependency injection.
 * <AUTHOR>
 */
//@dynamic
var CommonService = /** @class */ (function () {
    function CommonService(httpclient, Router, componentFactoryResolver, windowManager, resolver, manifests, loader, injector, applicationRef) {
        this.httpclient = httpclient;
        this.Router = Router;
        this.componentFactoryResolver = componentFactoryResolver;
        this.windowManager = windowManager;
        this.resolver = resolver;
        this.manifests = manifests;
        this.loader = loader;
        this.injector = injector;
        this.applicationRef = applicationRef;
        //      if(CommonService.instance){
        //          throw new Error("Programming Error: Only one instance of CommonService service is allowed at once !");
        //      }
        CommonService.instance = this;
        CommonService.WindowManager = windowManager;
    }
    /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    CommonService.jsonpath = /**
     * @param {?} obj
     * @param {?} path
     * @return {?}
     */
    function (obj, path) {
        return jp.query(obj, path);
    };
    /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    CommonService.jsonpathes = /**
     * @param {?} obj
     * @param {?} paths
     * @return {?}
     */
    function (obj, paths) {
        /** @type {?} */
        var res = obj;
        for (var i = 0; i < paths.length; i++) {
            /** @type {?} */
            var currpath = paths[i];
            res = CommonService.jsonpath(res, currpath);
        }
        return res;
    };
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param classObject
     */
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    CommonService.prototype.getQualifiedClassName = /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    function (classObject) {
        return classObject.constructor.name + '.ts';
    };
    CommonService.instance = null;
    CommonService.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    CommonService.ctorParameters = function () { return [
        { type: HttpClient },
        { type: Router },
        { type: ComponentFactoryResolver },
        { type: WindowManager },
        { type: ComponentFactoryResolver },
        { type: Array, decorators: [{ type: Inject, args: ["routes",] }] },
        { type: NgModuleFactoryLoader },
        { type: Injector },
        { type: ApplicationRef }
    ]; };
    return CommonService;
}());
export { CommonService };
if (false) {
    /** @type {?} */
    CommonService.WindowManager;
    /** @type {?} */
    CommonService.instance;
    /** @type {?} */
    CommonService.prototype.httpclient;
    /** @type {?} */
    CommonService.prototype.Router;
    /** @type {?} */
    CommonService.prototype.componentFactoryResolver;
    /** @type {?} */
    CommonService.prototype.windowManager;
    /** @type {?} */
    CommonService.prototype.resolver;
    /** @type {?} */
    CommonService.prototype.manifests;
    /** @type {?} */
    CommonService.prototype.loader;
    /** @type {?} */
    CommonService.prototype.injector;
    /** @type {?} */
    CommonService.prototype.applicationRef;
}
/**
 * Unsubscribe all Observables Subscriptions
 * It will return an empty array if it all went well
 * @param {?} subscriptions
 * @return {?}
 */
export function unsubscribeAllObservables(subscriptions) {
    if (Array.isArray(subscriptions)) {
        subscriptions.forEach((/**
         * @param {?} subscription
         * @return {?}
         */
        function (subscription) {
            if (subscription && subscription.unsubscribe) {
                subscription.unsubscribe();
            }
        }));
        subscriptions = [];
    }
    return subscriptions;
}
//# sourceMappingURL=data:application/json;base64,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