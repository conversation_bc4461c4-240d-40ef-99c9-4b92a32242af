/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { CommonService } from '../utils/common.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
/**
 * SwtPopUpManager version 2.0
 */
//@dynamic
var SwtPopUpManager = /** @class */ (function () {
    function SwtPopUpManager() {
    }
    /**
     *  Creates a top-level window and places it above other windows in the
     *  z-order.
     *  It is good practice to call the <code>removePopUp()</code> method
     *  to remove popups created by using the <code>createPopUp()</code> method.
     */
    /**
     *  Creates a top-level window and places it above other windows in the
     *  z-order.
     *  It is good practice to call the <code>removePopUp()</code> method
     *  to remove popups created by using the <code>createPopUp()</code> method.
     * @param {?} parent
     * @param {?=} childComponent
     * @param {?=} data
     * @param {?=} modal
     * @return {?}
     */
    SwtPopUpManager.createPopUp = /**
     *  Creates a top-level window and places it above other windows in the
     *  z-order.
     *  It is good practice to call the <code>removePopUp()</code> method
     *  to remove popups created by using the <code>createPopUp()</code> method.
     * @param {?} parent
     * @param {?=} childComponent
     * @param {?=} data
     * @param {?=} modal
     * @return {?}
     */
    function (parent, childComponent, data, modal) {
        if (modal === void 0) { modal = false; }
        return CommonService.WindowManager.createWindow(parent, childComponent, data, modal);
    };
    /**
     * @param {?} url
     * @return {?}
     */
    SwtPopUpManager.load = /**
     * @param {?} url
     * @return {?}
     */
    function (url) {
        return CommonService.WindowManager.load(url);
    };
    /**
     * @param {?} id
     * @return {?}
     */
    SwtPopUpManager.getPopUpById = /**
     * @param {?} id
     * @return {?}
     */
    function (id) {
        return CommonService.WindowManager.getWindow(id);
    };
    /**
     * @param {?} window
     * @return {?}
     */
    SwtPopUpManager.close = /**
     * @param {?} window
     * @return {?}
     */
    function (window) {
        return CommonService.WindowManager.close(window.id);
    };
    /**
     *  Pops up a top-level window.
     *  It is good practice to call <code>removePopUp()</code>
     *
     * @deprecated since version 2.0
     *
     */
    /*public static addPopUp(parent: any, childComponent: any, data: any, modal: boolean = false): Observable<any> {
        console.warn('addPopUp is a deprecated method. It will be an error in the future version of SwttoolBox try to use createPopUp');
        if (!data) {
            data = {};
        }
        // Wrap parent into data
        if (data['parentDocument']) {
            throw new Error('Programming Error: data["parentDocument"] could'
                + 'not be defined as parentDocument is a reserved keyword for SwtPopUpManager.');
        }
        if (data['_displayPositionLeft']) {
            throw new Error('Programming Error: data["_displayPositionLeft"] could'
                + 'not be defined as _displayPositionLeft is a reserved keyword for SwtPopUpManager.');
        }
        if (data['_displayPositionTop']) {
            throw new Error('Programming Error: data["_displayPositionTop"] could'
                + 'not be defined as _displayPositionTop is a reserved keyword for SwtPopUpManager.');
        }
        data['parentDocument'] = parent;
        // Wrap isModal into data
        if (data['isModal']) {
            throw new Error('Programming Error: data["isModal"] could not be defined'
                + ' as isModal is a reserved keyword for SwtPopUpManager.');
        }
        // center popup in the screen;
        data['isModal'] = modal;
        data['_position'] = this.position;
        return CommonService.ModalService.addModal(childComponent, data);
    }*/
    /**
     * This method is used to set the PopUp display position.
     * @param left
     * @param top
     */
    /**
         *  Pops up a top-level window.
         *  It is good practice to call <code>removePopUp()</code>
         *
         * @deprecated since version 2.0
         *
         */
    /*public static addPopUp(parent: any, childComponent: any, data: any, modal: boolean = false): Observable<any> {
            console.warn('addPopUp is a deprecated method. It will be an error in the future version of SwttoolBox try to use createPopUp');
            if (!data) {
                data = {};
            }
            // Wrap parent into data
            if (data['parentDocument']) {
                throw new Error('Programming Error: data["parentDocument"] could'
                    + 'not be defined as parentDocument is a reserved keyword for SwtPopUpManager.');
            }
            if (data['_displayPositionLeft']) {
                throw new Error('Programming Error: data["_displayPositionLeft"] could'
                    + 'not be defined as _displayPositionLeft is a reserved keyword for SwtPopUpManager.');
            }
            if (data['_displayPositionTop']) {
                throw new Error('Programming Error: data["_displayPositionTop"] could'
                    + 'not be defined as _displayPositionTop is a reserved keyword for SwtPopUpManager.');
            }
            data['parentDocument'] = parent;
            // Wrap isModal into data
            if (data['isModal']) {
                throw new Error('Programming Error: data["isModal"] could not be defined'
                    + ' as isModal is a reserved keyword for SwtPopUpManager.');
            }
            // center popup in the screen;
            data['isModal'] = modal;
            data['_position'] = this.position;
            return CommonService.ModalService.addModal(childComponent, data);
        }*/
    /**
     * This method is used to set the PopUp display position.
     * @param {?} left
     * @param {?} top
     * @return {?}
     */
    SwtPopUpManager.setPopUpLocation = /**
         *  Pops up a top-level window.
         *  It is good practice to call <code>removePopUp()</code>
         *
         * @deprecated since version 2.0
         *
         */
    /*public static addPopUp(parent: any, childComponent: any, data: any, modal: boolean = false): Observable<any> {
            console.warn('addPopUp is a deprecated method. It will be an error in the future version of SwttoolBox try to use createPopUp');
            if (!data) {
                data = {};
            }
            // Wrap parent into data
            if (data['parentDocument']) {
                throw new Error('Programming Error: data["parentDocument"] could'
                    + 'not be defined as parentDocument is a reserved keyword for SwtPopUpManager.');
            }
            if (data['_displayPositionLeft']) {
                throw new Error('Programming Error: data["_displayPositionLeft"] could'
                    + 'not be defined as _displayPositionLeft is a reserved keyword for SwtPopUpManager.');
            }
            if (data['_displayPositionTop']) {
                throw new Error('Programming Error: data["_displayPositionTop"] could'
                    + 'not be defined as _displayPositionTop is a reserved keyword for SwtPopUpManager.');
            }
            data['parentDocument'] = parent;
            // Wrap isModal into data
            if (data['isModal']) {
                throw new Error('Programming Error: data["isModal"] could not be defined'
                    + ' as isModal is a reserved keyword for SwtPopUpManager.');
            }
            // center popup in the screen;
            data['isModal'] = modal;
            data['_position'] = this.position;
            return CommonService.ModalService.addModal(childComponent, data);
        }*/
    /**
     * This method is used to set the PopUp display position.
     * @param {?} left
     * @param {?} top
     * @return {?}
     */
    function (left, top) {
        this.position.left = left;
        this.position.top = top;
    };
    /**
     * this method is used to hide the popup.
     * <AUTHOR>
     */
    /**
     * this method is used to hide the popup.
     * <AUTHOR>
     * @return {?}
     */
    SwtPopUpManager.hide = /**
     * this method is used to hide the popup.
     * <AUTHOR>
     * @return {?}
     */
    function () {
        $('.modal').css('display', 'none');
    };
    /**
     * This method is used to show popup
     * <AUTHOR>
     */
    /**
     * This method is used to show popup
     * <AUTHOR>
     * @return {?}
     */
    SwtPopUpManager.show = /**
     * This method is used to show popup
     * <AUTHOR>
     * @return {?}
     */
    function () {
        $('.modal').css('display', 'table');
    };
    /**
     *  Centers a popup window over whatever window was used in the call
     *  to the <code>createPopUp()</code> or <code>addPopUp()</code> method.
     */
    /**
     *  Centers a popup window over whatever window was used in the call
     *  to the <code>createPopUp()</code> or <code>addPopUp()</code> method.
     * @param {?} popUp
     * @return {?}
     */
    SwtPopUpManager.centerPopUp = /**
     *  Centers a popup window over whatever window was used in the call
     *  to the <code>createPopUp()</code> or <code>addPopUp()</code> method.
     * @param {?} popUp
     * @return {?}
     */
    function (popUp) {
    };
    /**
     *  Makes sure a popup window is higher than other objects in its child list
     *  The SystemManager does this automatically if the popup is a top level window
     *  and is moused on,
     *  but otherwise you have to take care of this yourself.
     */
    /**
     *  Makes sure a popup window is higher than other objects in its child list
     *  The SystemManager does this automatically if the popup is a top level window
     *  and is moused on,
     *  but otherwise you have to take care of this yourself.
     * @param {?} popUp
     * @return {?}
     */
    SwtPopUpManager.bringToFront = /**
     *  Makes sure a popup window is higher than other objects in its child list
     *  The SystemManager does this automatically if the popup is a top level window
     *  and is moused on,
     *  but otherwise you have to take care of this yourself.
     * @param {?} popUp
     * @return {?}
     */
    function (popUp) {
    };
    /**
     * Draws a Mask that hides all components that belongs to the parent
     */
    /**
     * Draws a Mask that hides all components that belongs to the parent
     * @private
     * @param {?} window
     * @param {?} parent
     * @return {?}
     */
    SwtPopUpManager.drawMask = /**
     * Draws a Mask that hides all components that belongs to the parent
     * @private
     * @param {?} window
     * @param {?} parent
     * @return {?}
     */
    function (window, parent) {
    };
    /**
     * @private
     * @param {?} window
     * @return {?}
     */
    SwtPopUpManager.removeMask = /**
     * @private
     * @param {?} window
     * @return {?}
     */
    function (window) {
    };
    /**
     *
     *  Set by PopUpManager on modal windows to make sure they cover the whole parent
     */
    /**
     *
     *  Set by PopUpManager on modal windows to make sure they cover the whole parent
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtPopUpManager.resizeHandler = /**
     *
     *  Set by PopUpManager on modal windows to make sure they cover the whole parent
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
    };
    /**
     *
     *  Returns the PopUpData (or null) for a given popupInfo.owner
     */
    /**
     *
     *  Returns the PopUpData (or null) for a given popupInfo.owner
     * @private
     * @param {?} window
     * @return {?}
     */
    SwtPopUpManager.findPopupInfoByWindow = /**
     *
     *  Returns the PopUpData (or null) for a given popupInfo.owner
     * @private
     * @param {?} window
     * @return {?}
     */
    function (window) {
    };
    SwtPopUpManager.modalId = -1;
    // Variable to handle PopUp position
    SwtPopUpManager.position = { left: 0, top: 0 };
    SwtPopUpManager.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    SwtPopUpManager.ctorParameters = function () { return []; };
    return SwtPopUpManager;
}());
export { SwtPopUpManager };
if (false) {
    /** @type {?} */
    SwtPopUpManager.modalId;
    /**
     * @type {?}
     * @private
     */
    SwtPopUpManager.position;
}
//# sourceMappingURL=data:application/json;base64,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