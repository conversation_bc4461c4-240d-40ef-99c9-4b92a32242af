/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { HTTPComms } from '../communication/httpcomms.service';
import { ExternalInterface } from './external-interface.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
//@dynamic
var SwtUtil = /** @class */ (function () {
    function SwtUtil(common) {
        this.common = common;
        // httpcomms variable to send requests
        this.httpcomms = new HTTPComms(this.common);
    }
    /**
     * obtainURL
     * @return String - base url
     * This function is used to return the base url of the application.
     */
    /**
     * obtainURL
     * @return {?} String - base url
     * This function is used to return the base url of the application.
     */
    SwtUtil.obtainURL = /**
     * obtainURL
     * @return {?} String - base url
     * This function is used to return the base url of the application.
     */
    function () {
        return window.location;
    };
    /**
     * @return {?}
     */
    SwtUtil.getBaseURL = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var baseUrl = "";
        try {
            baseUrl = ExternalInterface.call('getUrl');
        }
        catch (error) {
        }
        if (!baseUrl) {
            baseUrl = "";
            /** @type {?} */
            var origin = window.location.origin;
            /** @type {?} */
            var pathname = window.location.pathname;
            /** @type {?} */
            var deployName = pathname.split("\/");
            baseUrl = origin + "/" + deployName[1] + "/";
            return "http://localhost:8080/swallowtech/";
        }
        else {
            baseUrl = baseUrl + "/";
        }
        return baseUrl;
    };
    /**
     * @param {?} e
     * @return {?}
     */
    SwtUtil.isVisible = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtUtil.isEmpty = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return value === undefined || value === null || value === "";
    };
    /**
     * getSystemMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the systemMessages resource Bundle.
     */
    /**
     * getSystemMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the systemMessages resource Bundle.
     */
    SwtUtil.getSystemMessages = /**
     * getSystemMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the systemMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.SYSTEM_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
     * @param {?} object
     * @return {?}
     */
    SwtUtil.convertObjectToArray = /**
     * @param {?} object
     * @return {?}
     */
    function (object) {
        if (object) {
            if (object.length) {
                return object;
            }
            else {
                return [object];
            }
        }
        else {
            return [];
        }
    };
    /**
     * getCommonMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the commonMessages resource Bundle.
     */
    /**
     * getCommonMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the commonMessages resource Bundle.
     */
    SwtUtil.getCommonMessages = /**
     * getCommonMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the commonMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.COMMON_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
     * getLoginMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the LoginMessages resource Bundle.
     */
    /**
     * getLoginMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the LoginMessages resource Bundle.
     */
    SwtUtil.getLoginMessages = /**
     * getLoginMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the LoginMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.LOGIN_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
   * getAMLMessages
   *
   * @return String - value
   *
   * This function is used to return the value for the key passed
   * to it from the AMLMessages resource Bundle.
   */
    /**
     * getAMLMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    SwtUtil.getAMLMessages = /**
     * getAMLMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.AML_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
    * getDUPMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the DUPMessages resource Bundle.
    */
    /**
     * getDUPMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the DUPMessages resource Bundle.
     */
    SwtUtil.getDUPMessages = /**
     * getDUPMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the DUPMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.DUP_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
    * getARCMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the ARCMessages resource Bundle.
    */
    /**
     * getARCMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the ARCMessages resource Bundle.
     */
    SwtUtil.getARCMessages = /**
     * getARCMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the ARCMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.ARC_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
    * getInputMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the InputMessages resource Bundle.
    */
    /**
     * getInputMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    SwtUtil.getInputMessages = /**
     * getInputMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.INPUT_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
   * getCashMessages
   *
   * @return String - value
   *
   * This function is used to return the value for the key passed
   * to it from the InputMessages resource Bundle.
   */
    /**
     * getCashMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    SwtUtil.getCashMessages = /**
     * getCashMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.CASH_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
    * getFatcaMessages
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the FatcaMessages resource Bundle.
    */
    /**
     * getFatcaMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the FatcaMessages resource Bundle.
     */
    SwtUtil.getFatcaMessages = /**
     * getFatcaMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the FatcaMessages resource Bundle.
     */
    function (key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.FATCA_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    };
    /**
    * getPredictMessage
    *
    * @return String - value
    *
    * This function is used to return the value for the key passed
    * to it from the PredictMessage resource Bundle.
    */
    /**
     * getPredictMessage
     *
     * @param {?} key
     * @param {?=} lang
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the PredictMessage resource Bundle.
     */
    SwtUtil.getPredictMessage = /**
     * getPredictMessage
     *
     * @param {?} key
     * @param {?=} lang
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the PredictMessage resource Bundle.
     */
    function (key, lang) {
        /** @type {?} */
        var message;
        try {
            message = ExternalInterface.call('getMessage', key, lang);
        }
        catch (error) {
        }
        if (!message)
            return key;
        return message;
    };
    /**
    * This method converts the given array to string separated by given
    * separator.
    *
    * @param arrValue: Array
    * @param separator: String
    * @return String
    */
    /**
     * This method converts the given array to string separated by given
     * separator.
     *
     * @param {?} arrValue
     * @param {?} separator
     * @return {?} String
     */
    SwtUtil.arrToStr = /**
     * This method converts the given array to string separated by given
     * separator.
     *
     * @param {?} arrValue
     * @param {?} separator
     * @return {?} String
     */
    function (arrValue, separator) {
        //           //String - result value
        //           var value: String = null;
        //           try {
        //               // Set default value
        //               value = "";
        //               if (arrValue && arrValue.length > 0) {
        //                   //Append the string
        //                   for (var i: int = 0; i < arrValue.length; i++) {
        //                       if (i != 0) {
        //                           value += separator;
        //                       }
        //                       value += arrValue[i];
        //                   }
        //               }
        //           } catch (error: Error) {
        //               SwtAlert.error(error.message);
        //           }
        //           return value;
        /* return will be removed when edit this method */
        return '';
    };
    /**
    * logError
    *
    * @return String - value
    *
    * This function is used to log the flex error in ERROR LOG.
    *
    * when  running the application in debugger version of flash player, this function will log the
    * error trace(location of error in src file) and when using non debugger version of flash player
    * this function will log the source file name and method name as the error Tace will be null.
    *
    */
    /**
     * logError
     *
     * @param {?} error
     * @param {?} moduleId
     * @param {?} className
     * @param {?} methodName
     * @param {?} errorLocation
     * @return {?} String - value
     *
     * This function is used to log the flex error in ERROR LOG.
     *
     * when  running the application in debugger version of flash player, this function will log the
     * error trace(location of error in src file) and when using non debugger version of flash player
     * this function will log the source file name and method name as the error Tace will be null.
     *
     */
    SwtUtil.logError = /**
     * logError
     *
     * @param {?} error
     * @param {?} moduleId
     * @param {?} className
     * @param {?} methodName
     * @param {?} errorLocation
     * @return {?} String - value
     *
     * This function is used to log the flex error in ERROR LOG.
     *
     * when  running the application in debugger version of flash player, this function will log the
     * error trace(location of error in src file) and when using non debugger version of flash player
     * this function will log the source file name and method name as the error Tace will be null.
     *
     */
    function (error, moduleId, className, methodName, errorLocation) {
        //         //To Get the errorTracesFirst Part
        //         var errorTracesBegin:String = null;
        //         //To Get the errorTracesEnd Part
        //         var errorTracesEnd:String = null;
        //         //Error Trace
        //         var errorTrace:String = null;
        //         //Error Description
        //         var errorDescription:String = null;
        //         // Error Id
        //         var errorId:String = null; 
        //         
        //         // get the error Id
        //         errorId = error.errorID.toString();
        //         //Assign error Id to request object
        //         requestParams["errorId"] = errorId;
        //         // If error message is not null then
        //          if(error.message != null) {
        //           // Get the error description
        //           errorDescription = error.message;
        //            //Assign error Description  to request object
        //           requestParams["errorDescription"] = errorDescription;
        //         }
        //         // If error trace  is not null then
        //         if(error.getStackTrace() != null) {
        //           // Get the error Trace
        //           errorTrace = error.getStackTrace().toString();
        //           // Get the Error Traces first part
        //           errorTracesBegin = errorTrace.substr(errorTrace.indexOf("[")+1,errorTrace.length-1);
        //           // Get the error location of source file
        //           errorTracesEnd = errorTracesBegin.substr(0,errorTracesBegin.indexOf("]")-1);
        //           //Get the src file name from the error Trace
        //           errorTrace = errorTracesEnd.substr(errorTracesEnd.indexOf("com"),errorTracesEnd.length-1);
        //           // Get the class and method name of source file
        //           errorTrace = "ClassName:"+className+":MethodName:"+methodName+":ErrorLocation:"+errorLocation + "\n" + errorTrace; 
        //           //Assign error Trace  to request object
        //           requestParams["errorTrace"] = errorTrace;
        //         } else {
        //           // Get the class and method name of source file
        //           errorTrace = "ClassName:"+className+":MethodName:"+methodName+":ErrorLocation:"+errorLocation; 
        //            //Assign errorLocation  to request object
        //           requestParams["errorTrace"] = errorTrace;
        //         }
        //          //Assign moduleId  to request object
        //         requestParams["moduleId"] = moduleId;
        //         // Prepare the URL of the request
        //         inputData.url = obtainURL() + actionPath + actionMethod;
        //         inputData.cbResult = inputResult;
        //         //Send the request to the server  
        //         inputData.send(requestParams);
        //         
        //         // Log error locally
        //         log.error("Error #"+requestParams["errorId"]+":"+requestParams["errorDescription"]
        //         +", ErrorLocation:"+errorLocation+"\n"+requestParams["errorTrace"]);
    };
    /**
    * this function is used to get the language of the user from jsp side
    * */
    /**
     * this function is used to get the language of the user from jsp side
     *
     * @return {?}
     */
    SwtUtil.getUserLanguage = /**
     * this function is used to get the language of the user from jsp side
     *
     * @return {?}
     */
    function () {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'userLanguage');
        /* return will be removed when edit this method */
        return '';
    };
    /**
    * this function is used to get the current entityId
    * */
    /**
     * this function is used to get the current entityId
     *
     * @return {?}
     */
    SwtUtil.getCurrEntityId = /**
     * this function is used to get the current entityId
     *
     * @return {?}
     */
    function () {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'currEntityId');
        /* return will be removed when edit this method */
        return '';
    };
    /**
    * this function is used to get the default entityId
    * */
    /**
     * this function is used to get the default entityId
     *
     * @return {?}
     */
    SwtUtil.getDefaultEntityId = /**
     * this function is used to get the default entityId
     *
     * @return {?}
     */
    function () {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'defaultEntityId');
        /* return will be removed when edit this method */
        return '';
    };
    /**
     * getAMLMessages
     *
     * @return String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    /**
     * getAMLMessages
     *
     * @param {?} moduleId
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    SwtUtil.getMessages = /**
     * getAMLMessages
     *
     * @param {?} moduleId
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    function (moduleId, key, object) {
        if (SwtUtil.translate) {
            /** @type {?} */
            var trans = SwtUtil.translate.instant(moduleId + "." + key, object);
            // test if  the translated string really exists
            if (trans.indexOf(moduleId + ".") == -1) {
                //success of translation
                return trans;
            }
            else {
                return key;
            }
        }
        else {
            return key;
        }
    };
    /**
     * result
     *
     * @param event: ResultEvent
     *
     * This is a callback method, to handle reponse event
     */
    /**
     * result
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtUtil.inputResult = /**
     * result
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        //           //To parse the response (result) xml
        //            var xmlReader:XMLReader = new XMLReader();
        //           //If the service is busy then remove the busy cursor
        //               if (inputData.isBusy()){
        //                   inputData.cbStop();
        //               }
        //             //Parse result xml
        //             xmlReader.setInputXML(event.result as XML);
        //             //If the result status is true, then load the grid
        //              if (!xmlReader.getRequestReplyStatus()){
        //                   //Error occurs, display the error message
        //                   SwtAlert.error(getCommonMessages('alert.generic_exception'));
        //               }
        //               xmlReader = null;
    };
    /**
     * Default constructor
     **/
    /**
     * Default constructor
     *
     * @return {?}
     */
    SwtUtil.prototype.SwtUtil = /**
     * Default constructor
     *
     * @return {?}
     */
    function () {
        //          SwtUtil.inputData  = new HTTPComms()
    };
    SwtUtil.CommonServiceInstance = null; // added by Rihab JABALLAH on 17/10/2018
    // added by Rihab JABALLAH on 17/10/2018
    // Common Module Id
    SwtUtil.COMMON_MODULE_ID = 'COMMON';
    // System Module Id
    SwtUtil.SYSTEM_MODULE_ID = 'SYSTEM';
    // AML module Id
    SwtUtil.AML_MODULE_ID = 'AML';
    // DUP module Id
    SwtUtil.DUP_MODULE_ID = 'DUP';
    // ARC module Id
    SwtUtil.ARC_MODULE_ID = 'ARC';
    // INPUT module Id
    SwtUtil.INPUT_MODULE_ID = 'INPUT';
    // GENREC module Id
    SwtUtil.CASH_MODULE_ID = 'CASH';
    // FATCA module Id
    SwtUtil.FATCA_MODULE_ID = 'FATCA';
    // Predict module Id
    SwtUtil.PREDICT_MODULE_ID = 'PREDICT';
    // PCM module Id
    SwtUtil.PCM_MODULE_ID = 'PCM';
    // String login
    SwtUtil.LOGIN_ID = 'LOGIN';
    // set the language
    SwtUtil.lang = 'es';
    SwtUtil.translate = null;
    /* String variable to hold action method */
    SwtUtil.actionMethod = 'logError.do';
    /* String variable to hold action path */
    SwtUtil.actionPath = 'system/errorlog!';
    return SwtUtil;
}());
export { SwtUtil };
if (false) {
    /** @type {?} */
    SwtUtil.CommonServiceInstance;
    /** @type {?} */
    SwtUtil.COMMON_MODULE_ID;
    /** @type {?} */
    SwtUtil.SYSTEM_MODULE_ID;
    /** @type {?} */
    SwtUtil.AML_MODULE_ID;
    /** @type {?} */
    SwtUtil.DUP_MODULE_ID;
    /** @type {?} */
    SwtUtil.ARC_MODULE_ID;
    /** @type {?} */
    SwtUtil.INPUT_MODULE_ID;
    /** @type {?} */
    SwtUtil.CASH_MODULE_ID;
    /** @type {?} */
    SwtUtil.FATCA_MODULE_ID;
    /** @type {?} */
    SwtUtil.PREDICT_MODULE_ID;
    /** @type {?} */
    SwtUtil.PCM_MODULE_ID;
    /** @type {?} */
    SwtUtil.LOGIN_ID;
    /** @type {?} */
    SwtUtil.lang;
    /** @type {?} */
    SwtUtil.translate;
    /** @type {?} */
    SwtUtil.screenWidth;
    /** @type {?} */
    SwtUtil.screenHeight;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.actionMethod;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.actionPath;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.inputData;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.requestParams;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.prototype.httpcomms;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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