import { OnInit } from '@angular/core';
import { Options } from 'ng5-slider';
export declare class SwtSlider implements OnInit {
    private change_;
    minValue: any;
    maxValue: any;
    private dateRange;
    start: any;
    end: any;
    options: Options;
    miValue: number;
    maValue: number;
    private value;
    private firstValue;
    private lastValue;
    private numberOfDays;
    _dataTipFormatString: any;
    private createDateRange;
    constructor();
    ngOnInit(): void;
    enabled: any;
    minimum: any;
    maximum: any;
    values: any;
    dataTipFormatString: any;
    private updateData;
    onUserChange(e: any): void;
}
