export declare class Timer {
    private duration;
    private _isRunning;
    private _callback;
    private _timer;
    constructor(duration: number, repeatCount?: number);
    /**
     * This method is used to start timer.
     */
    start(): void;
    /**
     * This method is used to stop timer
     */
    stop(): void;
    /**
     * This method is used to set timer delay.
     * @param number
     */
    delay(duration: number): void;
    /**
     * This method is used to check timer state
     * <code>timer.running()</code> if true the timer
     * is already lunched.
     */
    readonly running: boolean;
    /**
     * This method is used to add event listener to
     * timer instance.
     * @param name
     * @param callBack
     */
    addEventListener(name: string, callBack: any): void;
}
