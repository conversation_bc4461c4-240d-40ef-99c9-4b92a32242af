/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
//import { SwtAbstract } from './swt-abstract';
import { focusManager } from "../managers/focus-manager.service";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtTimeInput = /** @class */ (function () {
    function SwtTimeInput(elem) {
        this.elem = elem;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this.id = "";
        //Outputs to handle timeInput events.
        this.KeyDown = new EventEmitter();
        this.keyUp = new EventEmitter();
        this.focus = new EventEmitter();
        this.stepperChange = new EventEmitter();
        this.creationComplete = new EventEmitter();
        this._hours = 0;
        this._minutes = 0;
        this._seconds = 0;
        this._selectedField = 0;
        this._visibility = true;
        this.firstCallHours = true;
        this.firstCallMin = true;
        this.firstCallSec = true;
        //private variable to enable or disable seconds
        this._secondEnable = false;
        this._separator = ":";
        this._editable = true;
        this._enabled = true;
    }
    Object.defineProperty(SwtTimeInput.prototype, "secondEnable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._secondEnable;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._secondEnable = true;
                    this.enableSeconds();
                }
                else {
                    this._secondEnable = false;
                    this.disableSeconds();
                }
            }
            else {
                this._secondEnable = value;
                !value ? this.disableSeconds() : this.enableSeconds();
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "separator", {
        get: /**
         * @return {?}
         */
        function () {
            return this._separator;
        },
        set: /**
         * @param {?} separator
         * @return {?}
         */
        function (separator) {
            this._separator = separator;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._editable = true;
                    this.hours.nativeElement.readOnly = false;
                    this.minutes.nativeElement.readOnly = false;
                    this.seconds.nativeElement.readOnly = false;
                }
                else {
                    this._editable = false;
                    this.hours.nativeElement.readOnly = true;
                    this.minutes.nativeElement.readOnly = true;
                    this.seconds.nativeElement.readOnly = true;
                }
            }
            else {
                this._editable = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._enabled = true;
                    this.hours.nativeElement.disabled = false;
                    this.minutes.nativeElement.disabled = false;
                    this.seconds.nativeElement.disabled = false;
                    //add click event listener to arrow Up.
                    $(this.uparrow.nativeElement).on("click", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.onArrowUpClick(event);
                        _this.stepperChange.emit(event);
                        _this.spyChanges({ hour: Number(_this.hour), minute: Number(_this.minute), second: Number(_this.second) });
                    }));
                    //add click event listener to arrow Down.
                    $(this.downarrow.nativeElement).on("click", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.onArrowDownClick(event);
                        _this.stepperChange.emit(event);
                        _this.spyChanges({ hour: Number(_this.hour), minute: Number(_this.minute), second: Number(_this.second) });
                    }));
                }
                else {
                    this._enabled = false;
                    this.hours.nativeElement.disabled = true;
                    this.minutes.nativeElement.disabled = true;
                    this.seconds.nativeElement.disabled = true;
                    $(this.timeInput.nativeElement).css("background-color", "#F0EFEF");
                    $(this.timeInput.nativeElement.children[0]).css("opacity", "0.5");
                }
            }
            else {
                this._enabled = value;
                this.hours.nativeElement.disabled = !value;
                this.minutes.nativeElement.disabled = !value;
                this.seconds.nativeElement.disabled = !value;
                $(this.timeInput.nativeElement).css("background-color", value ? "#FFF" : "#F0EFEF");
                $(this.timeInput.nativeElement.children[0]).css("opacity", value ? "1" : "0.5");
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visibility;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._visibility = true;
                    $(this.timeInput.nativeElement).show();
                }
                else {
                    this._visibility = false;
                    $(this.timeInput.nativeElement).hide();
                }
            }
            else {
                this._visibility = value;
                value ? $(this.timeInput.nativeElement).show() :
                    $(this.timeInput.nativeElement).hide();
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "time", {
        get: /**
         * @return {?}
         */
        function () {
            return { "hour": this.loadingZero(this.hour + ""), "minute": this.loadingZero(this.minute + ""), "second": this.loadingZero(this._seconds + "") };
        },
        set: /**
         * @param {?} time
         * @return {?}
         */
        function (time) {
            if (time.hour !== undefined && time.hour >= 0 && time.hour <= 23) {
                this.hour = time.hour;
                $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
            }
            else {
                console.log("[ time ] method error : time.hour = ", time.hour);
            }
            if (time.minute !== undefined && time.minute >= 0 && time.minute <= 59) {
                this.minute = time.minute;
                $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
            }
            else {
                console.log("[ time ] method error : time.minute = ", time.minute);
            }
            if (time.second !== undefined && time.second >= 0 && time.second <= 59) {
                this.second = time.second;
                $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
            }
            else {
                console.log("[ time ] method error : time.second = ", time.second);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "hour", {
        get: /**
         * @return {?}
         */
        function () {
            return this._hours;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._hours = value;
            if (this.firstCallHours) {
                this.originalValue["hours"] = Number(value);
                this.firstCallHours = false;
            }
            this.spyChanges(this.time);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "minute", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minutes;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minutes = value;
            if (this.firstCallMin) {
                this.originalValue["minutes"] = Number(value);
                this.firstCallMin = false;
            }
            this.spyChanges(this.time);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTimeInput.prototype, "second", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seconds;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seconds = value;
            if (this.firstCallSec) {
                this.originalValue["seconds"] = Number(value);
                this.firstCallSec = false;
            }
            this.spyChanges(this.time);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtTimeInput.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.originalValue = {};
        //**************************set timeInput separator.**************************
        $(this.separatorspan1.nativeElement).text(this._separator);
        $(this.separatorspan2.nativeElement).text(this._separator);
        //**************************hours input field events.**************************
        //add Key Up event listener to hours input.
        $(this.hours.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onHoursKeyDown(event);
            _this.KeyDown.emit(event);
        }));
        //add Key Up event listener to hours input.
        $(this.hours.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onHoursKeyUp(event);
            _this.keyUp.emit(event);
            _this.focus.emit(event);
        }));
        //add focus event listener to hours input.
        $(this.hours.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this._selectedField = 0;
            focusManager.focusTarget = _this.id;
        }));
        //add focus out event listener to hours input.
        $(this.hours.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            $(_this.hours.nativeElement).val(_this.loadingZero(_this.hour + ""));
        }));
        // **************************minutes input field events.**************************
        //add Key Up event listener to minutes input.
        $(this.minutes.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onMinutesKeyDown(event);
            _this.KeyDown.emit(event);
        }));
        //add Key Up event listener to minutes input.
        $(this.minutes.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onMinutesKeyUp(event);
            _this.keyUp.emit(event);
        }));
        //add focus event listener to minutes input.
        $(this.minutes.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this._selectedField = 1;
            _this.focus.emit(event);
        }));
        //add focus out event listener to minutes input.
        $(this.minutes.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            $(_this.minutes.nativeElement).val(_this.loadingZero(_this.minute + ""));
        }));
        // **************************seconds input field events.**************************
        //add Key Up event listener to seconds input.
        $(this.seconds.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onSecondsKeyDown(event);
            _this.KeyDown.emit(event);
        }));
        //add Key Up event listener to seconds input.
        $(this.seconds.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.onSecondsKeyUp(event);
            _this.keyUp.emit(event);
        }));
        //add focus event listener to seconds input.
        $(this.seconds.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this._selectedField = 2;
            _this.focus.emit(event);
        }));
        //add focus out event listener to seconds input.
        $(this.seconds.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            $(_this.seconds.nativeElement).val(_this.loadingZero(_this.second + ""));
        }));
        // **************************seconds Up arrow events.**************************
        //add click event listener to arrow Up.
        $(this.uparrow.nativeElement).on("click", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            if (_this.enabled) {
                _this.onArrowUpClick(event);
                _this.stepperChange.emit(event);
                _this.spyChanges({ hour: Number(_this.hour), minute: Number(_this.minute), second: Number(_this.second) });
            }
        }));
        // **************************seconds Down arrow events.**************************
        //add click event listener to arrow Down.
        $(this.downarrow.nativeElement).on("click", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            if (_this.enabled) {
                _this.onArrowDownClick(event);
                _this.stepperChange.emit(event);
                _this.spyChanges({ hour: Number(_this.hour), minute: Number(_this.minute), second: Number(_this.second) });
            }
        }));
        // Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtTimeInput.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtTimeInput');
        // set id to button DOM.
        if (this.id) {
            $($(this.elem.nativeElement)[0]).attr("id", this.id);
        }
        //-END-
    };
    /**
     * @return {?}
     */
    SwtTimeInput.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        this.creationComplete.emit(new Function());
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.spyChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.originalValue.hours == event.hour && this.originalValue.minutes == event.minute && this.originalValue.seconds == event.second) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else if (!this.firstCallHours && !this.firstCallMin && !this.firstCallSec) {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    };
    /**
     * @return {?}
     */
    SwtTimeInput.prototype.resetOriginalValue = /**
     * @return {?}
     */
    function () {
        this.originalValue.hours = this.time.hour;
        this.originalValue.minutes = this.time.minute;
        this.originalValue.seconds = this.time.second;
        this.spyChanges(this.time);
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtTimeInput.prototype.changeStyle = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (value) {
        }
        else {
        }
    };
    /**
     * This method is used to disable timeInput seconds.
     */
    /**
     * This method is used to disable timeInput seconds.
     * @private
     * @return {?}
     */
    SwtTimeInput.prototype.disableSeconds = /**
     * This method is used to disable timeInput seconds.
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            $(this.seconds.nativeElement).hide();
            errorLocation = 20;
            $(this.separatorspan2.nativeElement).hide();
        }
        catch (error) {
            console.error("[ disableSeconds ] method error :", error, "errorLocation :", errorLocation);
        }
    };
    /**
     * This method is used to enable timeInput seconds.
     */
    /**
     * This method is used to enable timeInput seconds.
     * @private
     * @return {?}
     */
    SwtTimeInput.prototype.enableSeconds = /**
     * This method is used to enable timeInput seconds.
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            $(this.seconds.nativeElement).show();
            errorLocation = 20;
            $(this.separatorspan2.nativeElement).show();
        }
        catch (error) {
            console.error("[ enableSeconds ] method error :", error, "errorLocation :", errorLocation);
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onHoursKeyDown = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event.keyCode == 38) {
            this.hour = this.increment(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (event.keyCode == 40) {
            this.hour = this.decrement(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onHoursKeyUp = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.hour = event.target.value;
        if (this.hour > 23) {
            $(this.hours.nativeElement).val("23");
            this.hour = 23;
        }
        else if (this.hour < 0) {
            $(this.hours.nativeElement).val("00");
            this.hour = 0;
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onMinutesKeyDown = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event.keyCode == 38) {
            this.minute = this.increment(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (event.keyCode == 40) {
            this.minute = this.decrement(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onMinutesKeyUp = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.minute = event.target.value;
        if (this.minute > 59) {
            $(this.minutes.nativeElement).val("59");
            this.minute = 59;
        }
        else if (this.minute < 0) {
            $(this.minutes.nativeElement).val("00");
            this.minute = 0;
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onSecondsKeyDown = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event.keyCode == 38) {
            this.second = this.increment(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
        else if (event.keyCode == 40) {
            this.second = this.decrement(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onSecondsKeyUp = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.second = event.target.value;
        if (this.second > 59) {
            $(this.seconds.nativeElement).val("59");
            this.second = 59;
        }
        else if (this.second < 0) {
            $(this.seconds.nativeElement).val("00");
            this.second = 0;
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onArrowUpClick = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this._selectedField === 0) {
            this.hour = this.increment(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (this._selectedField === 1) {
            this.minute = this.increment(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (this._selectedField === 2) {
            this.second = this.increment(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    };
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    SwtTimeInput.prototype.onArrowDownClick = /**
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this._selectedField === 0) {
            this.hour = this.decrement(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (this._selectedField === 1) {
            this.minute = this.decrement(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (this._selectedField === 2) {
            this.second = this.decrement(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtTimeInput.prototype.loadingZero = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var str = "";
        value.length === 1 ? str = "0" + value : str = value;
        return str;
    };
    /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    SwtTimeInput.prototype.increment = /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    function (value, min, max) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (value < max) {
                value++;
            }
            else if (value >= max) {
                value = min;
            }
            return value;
        }
        catch (error) {
            console.error("[ increment ] method error :", error, "errorLocation :", errorLocation);
        }
    };
    /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    SwtTimeInput.prototype.decrement = /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    function (value, min, max) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (value > min) {
                value--;
            }
            else if (value <= min) {
                value = max;
            }
            return value;
        }
        catch (error) {
            console.error("[ decrement ] method error :", error, "errorLocation :", errorLocation);
        }
    };
    SwtTimeInput.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTimeInput',
                    template: "\n    <span #timeInput class=\"timeInput-container\"  \n              [style.margin-left.px]=\"this.marginLeft\"\n              [style.margin-right.px]=\"this.marginRight\"\n              [style.margin-top.px]=\"this.marginTop\"\n              [style.margin-bottom.px]=\"this.marginBottom\">\n    <tr>\n       <td>\n        <div class=\"timeInput-time\">  \n          <input #hours type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-hours\">\n          <span #separator1 class=\"seconds-after\"></span>\n          <input #minutes type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-minutes\">\n          <span #separator2 class=\"seconds-before\" style=\"display: none\"></span>\n          <input #seconds type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-seconds\" style=\"display: none\">\n        </div>\n       </td>\n       <td>\n        <div class=\"timeInput-spiners\">\n             <tr>\n                 <td>\n                    <button #uparrow class=\"arrow-up\">&#9652;</button>\n                 </td>\n             </tr>\n             <tr>\n                 <td>   \n                    <button #downarrow class=\"arrow-down\">&#9662;</button>\n                 </td>   \n             </tr>\n          </div>\n         </td>\n    </tr>   \n    </span>\n    ",
                    styles: ["\n\n        td {\n            line-height: 8px;\n        }\n\n        .timeInput-container {\n            height: 21px;\n            width: auto;\n            display: inline-block;\n            border: none;\n            margin: 0px 5px 5px 0px;;\n            background-color: #FFF;\n        }\n\n        .timeInput-container:focus {\n            outline: none !important;\n        }\n\n        .timeInput-time {\n            border: 1px solid #8D8F91;\n            padding-left: 5px;\n            padding-right: 5px;\n            height: 22px;\n        }\n\n        .timeInput-hours, .timeInput-minutes, .timeInput-seconds {\n            width: 15px;\n            height: 19px;\n            text-align: center;\n            border: none;\n            color: #173553;\n        }\n\n        .timeInput-hours:focus, .timeInput-minutes:focus, .timeInput-seconds:focus {\n            outline: none;\n        }\n\n        .timeInput-spiners {\n            height: 19px;\n        }\n\n        .seconds-after, .seconds-after {\n            font-family: verdana, helvetica;\n            margin: -1px;\n        }\n\n        .arrow-down, .arrow-up {\n            width: 18px;\n            padding: 0px;\n            height: 11px;\n            line-height: 4px;\n            font-size: 12px;\n            font-weight: bolder;\n            cursor: default;\n        }\n\n        .arrow-down:focus, .arrow-up:focus {\n            outline: none;\n        }\n\n        .arrow-down:hover, .arrow-up:hover {\n            border: 1px solid #0086E8;\n            background-image: -webkit-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -moz-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -ms-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -o-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: linear-gradient(to bottom, #FFFFFF, #F1F1F1);\n        }\n\n        .arrow-down {\n            border-top: 0.5px solid #8D8F91;\n            border-bottom: 1px solid #8D8F91;\n            border-left: 1px solid #8D8F91;\n            border-right: 1px solid #8D8F91;\n            border-bottom-right-radius: 4px;\n            background-image: -webkit-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -moz-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -ms-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -o-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: linear-gradient(to bottom, #D8E8F2, #CDE0EB);\n        }\n\n        .arrow-up {\n            border-top: 1px solid #8D8F91;\n            border-bottom: 1px solid transparent;\n            border-left: 1px solid #8D8F91;\n            border-right: 1px solid #8D8F91;\n            border-top-right-radius: 4px;\n            margin-top: -1px;\n            background-image: -webkit-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -moz-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -ms-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -o-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: linear-gradient(to bottom, #F1F9FF, #DDECF5);\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    SwtTimeInput.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    SwtTimeInput.propDecorators = {
        onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
        onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
        id: [{ type: Input, args: ["id",] }],
        toolTip: [{ type: Input, args: ["toolTip",] }],
        KeyDown: [{ type: Output, args: ["KeyDown",] }],
        keyUp: [{ type: Output, args: ["keyUp",] }],
        focus: [{ type: Output, args: ["focus",] }],
        stepperChange: [{ type: Output, args: ["stepperChange",] }],
        creationComplete: [{ type: Output, args: ["creationComplete",] }],
        marginTop: [{ type: Input, args: ["marginTop",] }],
        marginRight: [{ type: Input, args: ["marginRight",] }],
        marginBottom: [{ type: Input, args: ["marginBottom",] }],
        marginLeft: [{ type: Input, args: ["marginLeft",] }],
        hours: [{ type: ViewChild, args: ["hours",] }],
        minutes: [{ type: ViewChild, args: ["minutes",] }],
        seconds: [{ type: ViewChild, args: ["seconds",] }],
        uparrow: [{ type: ViewChild, args: ["uparrow",] }],
        downarrow: [{ type: ViewChild, args: ["downarrow",] }],
        separatorspan1: [{ type: ViewChild, args: ["separator1",] }],
        separatorspan2: [{ type: ViewChild, args: ["separator2",] }],
        timeInput: [{ type: ViewChild, args: ["timeInput",] }],
        secondEnable: [{ type: Input }],
        editable: [{ type: Input }],
        enabled: [{ type: Input }],
        visible: [{ type: Input }]
    };
    return SwtTimeInput;
}());
export { SwtTimeInput };
if (false) {
    /** @type {?} */
    SwtTimeInput.prototype.originalValue;
    /** @type {?} */
    SwtTimeInput.prototype.onSpyChange;
    /** @type {?} */
    SwtTimeInput.prototype.onSpyNoChange;
    /** @type {?} */
    SwtTimeInput.prototype.id;
    /** @type {?} */
    SwtTimeInput.prototype.toolTip;
    /** @type {?} */
    SwtTimeInput.prototype.KeyDown;
    /** @type {?} */
    SwtTimeInput.prototype.keyUp;
    /** @type {?} */
    SwtTimeInput.prototype.focus;
    /** @type {?} */
    SwtTimeInput.prototype.stepperChange;
    /** @type {?} */
    SwtTimeInput.prototype.creationComplete;
    /** @type {?} */
    SwtTimeInput.prototype.marginTop;
    /** @type {?} */
    SwtTimeInput.prototype.marginRight;
    /** @type {?} */
    SwtTimeInput.prototype.marginBottom;
    /** @type {?} */
    SwtTimeInput.prototype.marginLeft;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._hours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._minutes;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._seconds;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._selectedField;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.hours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.minutes;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.seconds;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.uparrow;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.downarrow;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.separatorspan1;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.separatorspan2;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.timeInput;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallHours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallMin;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallSec;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._secondEnable;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._separator;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.elem;
}
//# sourceMappingURL=data:application/json;base64,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