import { OnInit, ElementRef } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
export declare class SwtPasswordMeter implements OnInit {
    private _label;
    private _includeInLayout;
    private _width;
    private _height;
    private _visible;
    private includeInlayout;
    private _target;
    private _strength;
    private _minLength;
    private _minSplChar;
    private _minAlpha;
    private _minNumber;
    private _mixedCase;
    private _labelFunction;
    passMetterObject: ElementRef;
    constructor();
    ngOnInit(): void;
    /**
     * This function calculate the strength of the target input field value
     */
    private calcuateStrength;
    /**
     * This method is used to set style property to
     * SwtPasswordMetter.
     * @param attribute
     * @param value
     */
    setStyle(attribute: string, value: string): void;
    setProgress(value: number, total: number): void;
    /**
     * This function resets the strength value to clear the progress bar
     */
    clearProgress(): void;
    /**
     * Calculate strength of the password, whenever the value is changed
     *
     * @param event: Event
     */
    private onValueChange;
    /**
     * <pre>
     * This function is used to get the strength of the password in word
     *  - Invalid Password:- Validation fail against password rule
     *  - Very weak (20%)
     *  - Weak (40%)
     *  - Medium (60%)
     *  - Strong (80%)
     *  - Very strong (100%)
     * </pre>
     *
     * @param strength - password strength value in percentage
     * @return String - password strengh in word
     */
    private getStrengthLabel;
    /**
     * Setter method of target
     */
    /**
    * Getter method of target
    */
    target: SwtTextInput;
    /**
     * Getter method of strength
     */
    readonly strength: number;
    /**
     * Setter method of minLength
     */
    /**
    * Getter method of minLength
    */
    minLength: number;
    /**
     * Setter method of minSplChar
     */
    /**
    * Getter method of minSplChar
    */
    minSplChar: number;
    /**
     * Setter method of minAlpha
     */
    /**
    * Getter method of minAlpha
    */
    minAlpha: number;
    /**
     * Setter method of minNumber
     */
    /**
    * Getter method of minNumber
    */
    minNumber: number;
    /**
     * Setter method of mixedCase
     */
    /**
    * Getter method of mixedCase
    */
    mixedCase: boolean;
    /**
     * Setter method of labelFunction function
     */
    /**
    * Getter method of labelFunction function
    */
    labelFunction: Function;
    /**
     * Setter method of width
     */
    /**
    * Getter method of width
    */
    width: string;
    /**
     * Setter method of height
     */
    /**
    * Getter method of height
    */
    height: string;
    /**
     * Setter method of visible
     */
    /**
    * Getter method of visible
    */
    visible: boolean;
    /**
     * Setter method of visible
     */
    /**
    * Getter method of visible
    */
    includeInLayout: boolean;
    /**
     * Setter method of visible
     */
    /**
    * Getter method of visible
    */
    label: string;
}
