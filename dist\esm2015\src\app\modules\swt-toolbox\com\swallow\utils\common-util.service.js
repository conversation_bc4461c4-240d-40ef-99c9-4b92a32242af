/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { StringUtils } from './string-utils.service';
import { HashMap } from './HashMap.service';
import { ExternalInterface } from './external-interface.service';
/**
 * CommonUtil contains utility functions for the Common module
 *
 * This class is the base class of additional operations on string
 *
 * <AUTHOR> JABALLAH, SwallowTech Tunisia  on 05/10/2018
 * @type {?}
 */
const _ = parent;
//@dynamic
export class CommonUtil {
    constructor() {
    }
    /**
     * format a Date from format1 to format2
     *
     *
     * @param {?} value
     * @param {?} patternIn
     * @param {?=} patternOut
     * @param {?=} lang
     * @return {?}
     */
    static formatDateFromString(value, patternIn, patternOut = null, lang = 'en') {
        // replace
        patternIn = patternIn ? patternIn.toUpperCase().replace(':MM', ':mm') : 'DD/MM/YYYY HH:mm:SS';
        patternOut = patternOut ? patternOut.toUpperCase().replace(':MM', ':mm') : 'YYYY-MM-DD HH:mm:SS';
        return _.formatDate(value, patternIn, patternOut, lang); // ExternalInterface.call("formatDate", value, patternIn,patternOut,lang);
    }
    /**
     * unformatAmount: Supports amountDecimal
     *
     * @param {?} formattedAmount
     * @param {?} amountPattern
     * @param {?} amountDecimal
     * @return {?}
     */
    static unformatAmount(formattedAmount, amountPattern, amountDecimal) {
        /** @type {?} */
        var unformattedAmount = StringUtils.unformatAmount(formattedAmount, amountPattern);
        // If the amountDecimal is provided, then use the correct decimal separator
        if (amountDecimal) {
            unformattedAmount = unformattedAmount.replace(/(\.|,)/g, amountDecimal);
        }
        return unformattedAmount;
    }
    /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    static getCurrentUserId() {
        // get the current user Id from JSP
        return ExternalInterface.call('eval', 'userId');
    }
    /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    static isoToDate(value) {
        /** @type {?} */
        var dateStr = value;
        dateStr = dateStr.replace(/\-/g, '/');
        dateStr = dateStr.replace('T', ' ');
        dateStr = dateStr.replace('Z', ' GMT-0000');
        return new Date(Date.parse(dateStr));
    }
    // implemented by Khalil.B
    /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    static dateToIso(value, displaySeconds = true) {
        /** @type {?} */
        let hours = '' + value.getHours();
        if (hours.length < 2)
            hours = '0' + hours;
        /** @type {?} */
        let minutes = '' + value.getMinutes();
        if (minutes.length < 2)
            minutes = '0' + minutes;
        /** @type {?} */
        let seconds = '' + value.getSeconds();
        if (seconds.length < 2)
            seconds = '0' + seconds;
        /** @type {?} */
        let months = '' + (value.getMonth() + 1);
        /** @type {?} */
        let days = '' + value.getDate();
        if (months.length < 2)
            months = '0' + months;
        if (days.length < 2)
            days = '0' + days;
        /** @type {?} */
        let date = value.getFullYear() + '-' + months + '-' + days;
        return (date + ' ' + hours + ':' + minutes + (displaySeconds ? (':' + seconds) : ''));
    }
    /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    static dateFromString(dateString, pattern) {
        return new Date(moment(dateString, pattern).toString());
    }
    /**
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    static parseDate(dateString, pattern) {
        return new Date(moment(dateString, pattern, true).toDate());
    }
    /*
      <AUTHOR> SwallowTech TN ??
        **Format date to specified date format
        **@params: *Date: date to format
                   *patern: output date format
      */
    /**
     * @param {?} date
     * @param {?} patern
     * @return {?}
     */
    static formatDate(date, patern) {
        /** @type {?} */
        let regex = new RegExp('[^a-z0-9A-Z]');
        /** @type {?} */
        var d = new Date(date);
        /** @type {?} */
        var month = '' + (d.getMonth() + 1);
        /** @type {?} */
        var day = '' + d.getDate();
        /** @type {?} */
        var year = d.getFullYear();
        /** @type {?} */
        let separator = patern[patern.search(regex)];
        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;
        if (patern == 'YYYY' + separator + 'MM' + separator + 'DD') {
            return [year, month, day].join(separator);
        }
        else if (patern == 'DD' + separator + 'MM' + separator + 'YYYY' || patern == 'DDMMYYYY') {
            return [day, month, year].join(separator);
        }
        else if (patern == 'MM' + separator + 'DD' + separator + 'YYYY') {
            return [month, day, year].join(separator);
        }
        else if (patern == 'DD' + separator + 'MMM' + separator + 'YYYY') {
            /** @type {?} */
            let month_names_short = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            month = month_names_short[Number(month) - 1];
            return [day, month, year].join('-');
        }
        else
            return '';
    }
    /**
     * Create dynamic report based on query on S_REPORTS by passing program id , kvparams and report group id
     *
     * @param {?} programId
     * @param {?} kvParams
     * @param {?} filterSortParams
     * @param {?} reportGroupId
     * @param {?} type
     * @param {?} moduleId
     * @param {?=} displayFilter
     * @param {?=} xmlDataSource
     * @return {?}
     */
    static report(programId, kvParams, filterSortParams, reportGroupId, type, moduleId, displayFilter = null, xmlDataSource = null) {
        if (filterSortParams != null) {
            if (filterSortParams['_sqlQueryFilter_'] != undefined) {
                kvParams['_sqlQueryFilter_'] = filterSortParams['_sqlQueryFilter_'];
            }
            if (filterSortParams['_sqlQuerySort_'] != undefined) {
                kvParams['_sqlQuerySort_'] = filterSortParams['_sqlQuerySort_'];
            }
            if (filterSortParams['_sqlQueryDirection_'] != undefined) {
                kvParams['_sqlQueryDirection_'] = filterSortParams['_sqlQueryDirection_'];
            }
        }
        /** @type {?} */
        var actionPathReport = 'report!exportData.do?';
        /** @type {?} */
        var kvAsXML = StringUtils.getKVTypeTabAsXML(kvParams, '', '', '');
        /** @type {?} */
        var displayFilterAsXMLStr = StringUtils.getKVTypeTabAsXML(displayFilter, '', '', '').toXMLString();
        /** @type {?} */
        var parentKVParams = kvAsXML.toXMLString();
        /** @type {?} */
        var actionPath = actionPathReport;
        //
        /** @type {?} */
        var actionParams = 'type=' + type;
        actionParams = actionParams + '&action=EXPORT&print=ALL&currentModuleId=' + moduleId;
        actionParams = actionParams + '&programId=' + programId + '&reportGroupId=' + reportGroupId + '&kvParams=' + StringUtils.encode64(parentKVParams);
        actionParams = actionParams + '&displayFilter=' + StringUtils.encode64(displayFilterAsXMLStr);
        //Calls a method from mainflex.jsp file
        //ExternalInterface.call("getReportsAndProgress", actionPath + actionParams, xmlDataSource!=null?StringUtils.encode64(xmlDataSource.toXMLString()):"");
    }
    /**
     * @return {?}
     */
    static getCurrentModuleId() {
        // get the current module Id from JSP
        return String(ExternalInterface.call('getCurrentModuleId')).toUpperCase();
    }
    /**
     * this function is used to get the current entity Id
     *
     * @return {?}
     */
    static getCurrentEntityId() {
        // get the current entity Id from JSP
        return ExternalInterface.call('eval', 'currEntityId');
    }
    /**
     * method that returns a list of updated columns
     * updateOperation will have the format: U(col1)>U(col2)>...>U(coln)
     *
     * @param {?} updateOperation
     * @return {?}
     */
    static getUpdatedColumns(updateOperation) {
        /** @type {?} */
        var result = updateOperation.split(/>+/);
        /** @type {?} */
        var value = '';
        for (var i = 0; i < result.length; i++) {
            value = result[i];
            value = value.substr(2, value.length - 3);
            result[i] = value;
        }
        return result;
    }
    /**
     * Returns the first occurrence text for a given XML and tag name
     *
     * @param {?} rowData
     * @param {?} tagName
     * @return {?}
     */
    static getTextByTagName(rowData, tagName) {
        try {
            /** @type {?} */
            var result = (rowData != null && rowData != undefined && rowData[tagName] != undefined) ? rowData[tagName] : null;
            return result;
        }
        catch (error) {
            console.error('method [getTextByTagName] error : ', error);
        }
    }
}
CommonUtil.changes = new HashMap();
CommonUtil.spyTimers = new HashMap();
CommonUtil.CDBegin = '<!' + '[CDATA[';
CommonUtil.CDEnd = ']]' + '>';
CommonUtil.ignoredObjects = new Array();
// Containers spying refresh rate
CommonUtil.SPY_TIME_INTERVAL = 1000;
CommonUtil.gridImages /*Of ByteArray*/ = [];
//    private static  base64Dec:Base64Decoder = new Base64Decoder();
//Httpservice iable
//    public static  inputData:HTTPComms=new HTTPComms();
// Current user's upload location
CommonUtil.USER_UPLOAD_PATH = '/uploads/' + CommonUtil.getCurrentUserId();
CommonUtil.decorators = [
    { type: Injectable }
];
/** @nocollapse */
CommonUtil.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    CommonUtil.log;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.changes;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.spyTimers;
    /** @type {?} */
    CommonUtil.CDBegin;
    /** @type {?} */
    CommonUtil.CDEnd;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.ignoredObjects;
    /** @type {?} */
    CommonUtil.SPY_TIME_INTERVAL;
    /**
     * @type {?}
     * @private
     */
    CommonUtil.gridImages;
    /** @type {?} */
    CommonUtil.USER_UPLOAD_PATH;
    /**
     * this function is used to get the current module Id
     *
     * @type {?}
     */
    CommonUtil.function;
}
//# sourceMappingURL=data:application/json;base64,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