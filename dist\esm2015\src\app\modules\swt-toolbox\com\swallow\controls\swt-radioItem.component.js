/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Input, ElementRef, Component } from "@angular/core";
import 'jquery-ui-dist/jquery-ui';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
const $ = require('jquery');
export class SwtRadioItem extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        this._value = null;
        this.parentGroup = null;
    }
    //---tabIndex-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set tabIndex(value) {
        try {
            this._tabIndex = String(value);
            if (this.selected)
                this.addTabIndex($($($(this.elem.nativeElement).children()[0]).children()[0]), this._tabIndex);
        }
        catch (error) {
            console.error('method [ SwtRadioItem set tabIndex] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get tabIndex() {
        return this._tabIndex;
    }
    //---Inputs--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set styleName(value) {
        if ($(this.elem.nativeElement))
            $($($(this.elem.nativeElement).children()[0]).children()[1]).addClass(value);
    }
    //---groupName-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set groupName(value) {
        this._groupName = value;
        /** @type {?} */
        var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
        if (item.length > 0) {
            $(item).attr('name', this._groupName);
        }
    }
    /**
     * @return {?}
     */
    get groupName() {
        return this._groupName;
    }
    //---Label---------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        this._label = value;
        if ($(this.elem.nativeElement))
            $($($(this.elem.nativeElement).children()[0]).children()[1]).text(this._label);
    }
    /**
     * @return {?}
     */
    get label() {
        return this._label;
    }
    //---value---------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set value(value) {
        /** @type {?} */
        var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
        if (item.length > 0) {
            $(item).val(String(value));
        }
        if (this.selected && this.parentGroup && this.parentGroup.originalValue !== value) {
            this.parentGroup.originalRadioItem = this.id;
            this.parentGroup.originalValue = value;
        }
        this._value = value;
    }
    /**
     * @return {?}
     */
    get value() {
        return this._value;
    }
    //---selected------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selected(value) {
        this.elem.nativeElement.firstElementChild.children[0].checked = value;
        // set selectedValue of correspondent SwtButtonGroup.
        if (value && this.parentGroup) {
            this.parentGroup.selectedRadioId = this.id;
            this.parentGroup.selectedValue = this.value;
        }
        if (value && this.tabIndex) {
            this.addTabIndex($($($(this.elem.nativeElement).children()[0]).children()[0]), this._tabIndex);
        }
    }
    /**
     * @return {?}
     */
    get selected() {
        return this.elem.nativeElement.firstElementChild.children[0].checked;
    }
    //---FontSize------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set fontSize(value) {
        if ($(this.elem.nativeElement))
            this.setStyle("font-size", this.adaptUnit(String(value)), $($($(this.elem.nativeElement).children()[0]).children()[1]));
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtRadioItem.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRadioItem');
    }
    /**
     * Destroy all event listeners
     * @return {?}
     */
    ngOnDestroy() {
        try {
            //console.log('[SwtRadioItem] ngOnDestroy ');
            delete this._value;
            delete this._label;
            delete this._groupName;
            delete this._tabIndex;
            delete this.parentGroup;
        }
        catch (error) {
            console.error('method [ngOnDestroy] - error :', error);
        }
    }
}
SwtRadioItem.decorators = [
    { type: Component, args: [{
                selector: 'SwtRadioItem',
                template: `      
    <div  class="radio-item"  tabindex="-1"  >
        <input [disabled]="parentGroup && !parentGroup.enabled" type="radio"/>
        <label class="rblabel" ></label>
    </div>
     
  `,
                styles: [`
           :host {
               outline: none;
             }
           .rblabel{
               font-size: 11px;
               color: #173553;
               height: auto;
               margin: 5px 4px 0;
               position: relative;
               top:1px;
               font-weight: normal;
               overflow: hidden;
               text-overflow: ellipsis;
             }
           
             div.radio-item > input:disabled{
               background-color: transparent!important;
             }
           
             @media all and (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {
                 .selector:not(*:root), .rblabel{
                     margin: 1px 4px 0!important;
                 }
             }

            .label{
                font-weight: bold!important;
             }
            .radio-item{
                width : 100%;
                display: flex;
                outline: none;
            }
            
         `]
            }] }
];
/** @nocollapse */
SwtRadioItem.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtRadioItem.propDecorators = {
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    styleName: [{ type: Input }],
    toolTip: [{ type: Input, args: ['toolTip',] }],
    groupName: [{ type: Input }],
    label: [{ type: Input, args: ['label',] }],
    value: [{ type: Input }],
    selected: [{ type: Input, args: ['selected',] }],
    fontSize: [{ type: Input, args: ['fontSize',] }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._value;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._groupName;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype._tabIndex;
    /** @type {?} */
    SwtRadioItem.prototype.parentGroup;
    /** @type {?} */
    SwtRadioItem.prototype.toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtRadioItem.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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