/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { VBox } from '../../../../controls/swt-vbox.component';
import { AssetsLegendItem } from './AssetsLegendItem';
export class AssetsLegend extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._seriesList = [];
        this._dataProvider = [];
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
        //Add 'implements OnInit' to the class.
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        this._dataProvider = value;
        this.refreshLegends(value);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesList(value) {
        this._seriesList = value;
    }
    /**
     * @return {?}
     */
    get seriesList() {
        return this._seriesList;
    }
    /**
     * @param {?} seriesList
     * @return {?}
     */
    refreshLegends(seriesList) {
        this.legendContainer.removeAllChildren();
        for (let index = 0; index < seriesList.length; index++) {
            /** @type {?} */
            const element = seriesList[index];
            /** @type {?} */
            var assetsLegendItem = (/** @type {?} */ (this.legendContainer.addChild(AssetsLegendItem)));
            assetsLegendItem.yField = element.yField;
            assetsLegendItem.seriesStyle = element.appliedStyle;
            /** @type {?} */
            var assetsArray = element.legendDisplayName.split("|");
            /** @type {?} */
            let assetLabel = assetsArray[0];
            /** @type {?} */
            let assetValue = assetsArray[1];
            if (assetValue.length == 1)
                assetValue = "     " + assetValue;
            // assetValueAsLabel.text = assetValue;
            assetsLegendItem.labelValue = assetLabel;
            assetsLegendItem.liveValue = assetValue;
        }
    }
    /**
     * @param {?} series
     * @return {?}
     */
    getLegendItem(series) {
        /** @type {?} */
        var found = null;
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'AssetsLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                // if (item && (item.element == series)) {
                if (item && (item.yField === series.yField)) {
                    found = item;
                    break;
                }
            }
        }
        return found;
    }
}
AssetsLegend.decorators = [
    { type: Component, args: [{
                selector: 'AssetsLegend',
                template: `
        <VBox  #legendContainer width="100%"> 
        </VBox>
  `,
                styles: [`
      `]
            }] }
];
/** @nocollapse */
AssetsLegend.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
AssetsLegend.propDecorators = {
    legendContainer: [{ type: ViewChild, args: ['legendContainer',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    AssetsLegend.prototype.legendContainer;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype._seriesList;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    AssetsLegend.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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