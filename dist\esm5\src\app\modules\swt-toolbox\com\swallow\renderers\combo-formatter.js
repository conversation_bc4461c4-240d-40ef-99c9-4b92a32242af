/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export var ComboFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var data = columnDef.params.selectDataSource;
    /** @type {?} */
    var defaultValue = '';
    /** @type {?} */
    var enabledFlag;
    if (columnDef.params.enableDisableCells) {
        enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    }
    else {
        enabledFlag = true;
    }
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    var text;
    //- if dataSource is an object
    if (!(data instanceof Array)) {
        /** @type {?} */
        var array = [];
        array[0] = data;
        data = array;
    }
    for (var index = 0; index < data.length; index++) {
        if (data[index].value == value) {
            defaultValue = data[index].content ? data[index].content : "";
            break;
        }
    }
    if (defaultValue == '' && value != undefined && value != '') {
        defaultValue = value;
    }
    if (showHideCells) {
        text = "\n                <div   class=\"renderAsInput selectDiv js-example-basic-single " + ((enabledFlag == false || !columnDef.params.grid.enabled) ? 'disabled-combo' : 'select2') + "\" >\n                    <span class=\"renderAsInput\" style=\"width:80%; text-overflow: ellipsis!important;\n                                overflow: hidden!important;\n                                white-space: nowrap!important;\n                                display: inline-block!important; padding-left:2px;\">" + defaultValue + "</span>\n                </div>\n            ";
    }
    else {
        text = "";
    }
    return text;
});
//# sourceMappingURL=data:application/json;base64,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