/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, Renderer2, ViewChild } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from '../utils/swt-util.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtDateField = /** @class */ (function () {
    function SwtDateField(elem, commonService, _renderer) {
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this._toolTipPreviousObject = null;
        /* Private attributes */
        this._toolTip = "";
        this.firstCall = true;
        this._visibility = true;
        this.logger = null;
        this._showOnFlag = "button";
        this._change = new Function();
        //variable to hold interruptComms
        this.interrupted = false;
        /*Handling output events */
        this.openEventOutPut = new EventEmitter();
        this.closeEventOutPut = new EventEmitter();
        this.keyDownEventOutPut = new EventEmitter();
        this.changeEventOutPut = new EventEmitter();
        this.focusEventOutPut = new EventEmitter();
        this.focusOutEventOutPut = new EventEmitter();
        this.keyFocusChange = new EventEmitter();
        this._enabled = true;
        this._editable = true;
        this._text = "";
        this._showYearSelect = true;
        this._showMonthSelect = false;
        this._textAlign = "justify";
        this._formatString = "mm/dd/yy";
        this._monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        this._dayNames = ["S", "M", "T", "W", "T", "F", "S"];
        /* private variable to handle events */
        this._open = new Function();
        this._close = new Function();
        this._keyDown = new Function();
        this._focus = new Function();
        this._focusOut = new Function();
        this._selectedDate = null;
        this.logger = new Logger('SwtDatefield', commonService.httpclient);
        this.logger.debug("[ SwtDatefield ] construction");
    }
    Object.defineProperty(SwtDateField.prototype, "toolTip", {
        // Input to handle datePicker tabIndex
        //---ToolTip---------------------------------------------------------------------------------------------------------
        set: 
        // Input to handle datePicker tabIndex
        //---ToolTip---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTip = value;
            if ($(this.datefield.nativeElement)) {
                $($(this.datefield.nativeElement)[0]).tooltip({
                    show: { duration: 800, delay: 500 },
                    open: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        $(this).removeAttr('title');
                    })
                });
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "textDictionaryId", {
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this._toolTip = SwtUtil.getPredictMessage(value);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "toolTipPreviousValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._toolTipPreviousObject;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTipPreviousObject = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        /* enabled getter and setter */
        set: /* enabled getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "false") {
                    this._enabled = false;
                }
                else {
                    this._enabled = true;
                }
            }
            else {
                this._enabled = value;
            }
            if (this.datePickerObject !== undefined) {
                this.datePickerObject.datepicker("option", "disabled", !this._enabled);
                $($(this.datePickerObject)[0]).val(this.text);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._editable;
        },
        /* editable getter and setter */
        set: /* editable getter and setter */
        /**
         * @param {?} editable
         * @return {?}
         */
        function (editable) {
            if (typeof (editable) === "string") {
                if (editable === "false") {
                    this._editable = false;
                    $(this.elem.nativeElement.children[0]).prop("readonly", true);
                }
                else {
                    this._editable;
                    $(this.elem.nativeElement.children[0]).prop("readonly", false);
                }
            }
            else {
                this._editable = editable;
                $(this.elem.nativeElement.children[0]).prop("readonly", !Boolean(editable));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        /* width getter and setter */
        set: /* width getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined) {
                if (value.indexOf('%') === -1) {
                    this._width = value + "px";
                }
                else {
                    this._width = value;
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            return this._text;
        },
        /* text getter and setter */
        set: /* text getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (value === undefined || value === null || value === '') {
                    this.datePickerObject.datepicker('setDate', null);
                    this._text = null;
                }
                else if (this.parseDate(value, this._formatString) !== null) {
                    this._text = value;
                    /** @type {?} */
                    var queryDate = this.parseDate(value, this._formatString);
                    this.datePickerObject.datepicker('setDate', queryDate);
                }
                else {
                    this._text = value;
                    $($(this.datePickerObject)[0]).val(value);
                }
                if (this.firstCall) {
                    this.originalValue = this._text;
                    this.firstCall = false;
                }
            }
            catch (error) {
                this.logger.error('set text method ', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "interruptComms", {
        get: /**
         * @return {?}
         */
        function () {
            return this.interrupted;
        },
        set: /**
         * @param {?} interrupted
         * @return {?}
         */
        function (interrupted) {
            this.interrupted = interrupted;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "showYearSelect", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showYearSelect;
        },
        /*=============================================== Getter and Setter ====================================================*/
        set: /*=============================================== Getter and Setter ====================================================*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._showYearSelect = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "showMonthSelect", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showMonthSelect;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._showMonthSelect = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "textAlign", {
        get: /**
         * @return {?}
         */
        function () {
            return this._textAlign;
        },
        /* @Inputs */
        /* textAlign getter and setter */
        set: /* @Inputs */
        /* textAlign getter and setter */
        /**
         * @param {?} align
         * @return {?}
         */
        function (align) {
            this._textAlign = align;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "formatString", {
        get: /**
         * @return {?}
         */
        function () {
            return this._formatString;
        },
        /**
         * override function, this function sets date format string
         *
         * @param value: String
         */
        set: /**
         * override function, this function sets date format string
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = value.toLowerCase();
            if (value.indexOf("yyyy") !== -1) {
                value = value.toLowerCase().replace("yyyy", "yy");
            }
            if (value.indexOf("mmm") !== -1) {
                value = value.toLowerCase().replace("mmm", "M");
            }
            this._formatString = value;
            if (this.datePickerObject !== undefined) {
                this.datePickerObject.datepicker("option", "dateFormat", this._formatString);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "monthNames", {
        get: /**
         * @return {?}
         */
        function () {
            return this._monthNames;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._monthNames = value;
            if (this.datePickerObject !== undefined) {
                // set datePicker monthNames
                this.datePickerObject.datepicker("option", "monthNames", value);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "dayNames", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dayNames;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dayNames = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "selectableRange", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectableRange;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (Object.keys(value).length === 1) {
                    if (this.datePickerObject !== undefined) {
                        this.datePickerObject.datepicker("option", "minDate", null);
                        this.datePickerObject.datepicker("option", "maxDate", value[Object.keys(value)[0]]);
                    }
                    this._selectableRange = value;
                }
                else if (Object.keys(value).length === 2) {
                    /** @type {?} */
                    var minDate = value[Object.keys(value)[0]];
                    /** @type {?} */
                    var maxDate = value[Object.keys(value)[1]];
                    if (this.datePickerObject !== undefined) {
                        this.datePickerObject.datepicker("option", "minDate", minDate);
                        this.datePickerObject.datepicker("option", "maxDate", maxDate);
                    }
                    this._selectableRange = value;
                }
                else {
                    throw new Error("selectableRange must be an object with tow keys rangeStart and rangeStop");
                }
            }
            catch (e) {
                this.logger.error(" selectableRange method :", e);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "open", {
        get: /**
         * @return {?}
         */
        function () {
            return this._open;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._open = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "close", {
        get: /**
         * @return {?}
         */
        function () {
            return this._close;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._close = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "keyDown", {
        get: /**
         * @return {?}
         */
        function () {
            return this._keyDown;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._keyDown = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "focus", {
        get: /**
         * @return {?}
         */
        function () {
            return this._focus;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._focus = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "focusOut", {
        get: /**
         * @return {?}
         */
        function () {
            return this._focusOut;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._focusOut = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "selectedDate", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedDate;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} date
         * @return {?}
         */
        function (date) {
            if (this.datePickerObject !== undefined && date != null) {
                this._selectedDate = new Date(date);
                this._text = this.formatDate(date);
                this.datePickerObject.datepicker('setDate', date);
            }
            else {
                this._selectedDate = null;
                this._text = "";
                this.datePickerObject.datepicker('setDate', null);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "showToday", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showToDay;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === 'string') {
                if (value === 'true') {
                    this._showToDay = true;
                }
                else {
                    this._showToDay = false;
                }
            }
            else {
                this._showToDay = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visibility;
        },
        /* input to hold component visibility */
        set: /* input to hold component visibility */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) !== 'string') {
                this._visibility = value;
                value ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
            }
            else {
                if (value === 'true') {
                    this.visible = true;
                    $(this.elem.nativeElement).show();
                }
                else {
                    this._visibility = false;
                    $(this.elem.nativeElement).hide();
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtDateField.prototype, "onSelect", {
        get: /**
         * @return {?}
         */
        function () {
            return this._change;
        },
        /*--------------------------------------------------------*/
        set: /*--------------------------------------------------------*/
        /**
         * @param {?} onSelect
         * @return {?}
         */
        function (onSelect) {
            this._change = onSelect;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtDateField.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.logger.debug("[ ngOnInit ] eneter");
        // Initialize datePiker object
        this.datePickerObject = $(this.elem.nativeElement.children[0]);
        this.datePickerObject.datepicker({
            showOn: "button",
            buttonImage: "assets/images/datePicker-icon.png",
            buttonImageOnly: true,
            dateFormat: this._formatString,
            buttonText: "",
            changeMonth: this._showMonthSelect,
            changeYear: this._showYearSelect,
            beforeShow: (/**
             * @param {?} result
             * @return {?}
             */
            function (result) {
                _this.openEventOutPut.emit(result);
                _this._open(result);
            }),
            onClose: (/**
             * @param {?} result
             * @return {?}
             */
            function (result) {
                _this.closeEventOutPut.emit(result);
                _this._close(result);
                _this._text = result;
                _this.changeEventOutPut.emit(result);
                _this.spyChanges(_this._text);
            }),
            onSelect: (/**
             * @param {?} result
             * @return {?}
             */
            function (result) {
                _this._change(result);
                _this._text = result;
                _this._selectedDate = _this.parseDate(result, _this.formatString);
            })
        });
        this.datePickerObject.on('keydown', (/**
         * @param {?} result
         * @return {?}
         */
        function (result) {
            _this.keyDownEventOutPut.emit(result);
            _this._keyDown(result);
        }));
        this.datePickerObject.on('keyup', (/**
         * @param {?} result
         * @return {?}
         */
        function (result) {
            _this.updateDate(result);
            _this.spyChanges(_this._text);
        }));
        this.datePickerObject.on('focus', (/**
         * @param {?} result
         * @return {?}
         */
        function (result) {
            _this.focusEventOutPut.emit(result);
            _this._focus(result);
            _this.keyFocusChange.emit(result);
            // update focus Manager data (focused element)
            focusManager.focusTarget = _this.id;
        }));
        this.datePickerObject.on('focusout', (/**
         * @param {?} result
         * @return {?}
         */
        function (result) {
            _this._selectedDate = _this.parseDate(_this.text, _this.formatString);
            _this.focusOutEventOutPut.emit(result);
            _this._focusOut(result);
            _this.keyFocusChange.emit(result);
        }));
        // set datePiker width
        this.datePickerObject.width(this._width);
        // set datePiker stringFormate
        this.datePickerObject.datepicker("option", "dateFormat", this._formatString);
        // Handling enabling properties
        if (this.enabled === true) {
            this.datePickerObject.datepicker("option", "disabled", false);
        }
        else {
            this.datePickerObject.datepicker("option", "disabled", true);
        }
        // Handling visibility properties  
        if (this.visible === false) {
            this.datePickerObject.addClass("hide");
        }
        else {
            this.datePickerObject.removeClass("hide");
        }
        // Handling editable properties
        if (this.editable === true) {
            this.datePickerObject.prop("readonly", false);
        }
        else {
            this.datePickerObject.prop("readonly", true);
        }
        // Handling text align 
        if (this._textAlign !== undefined) {
            this.datePickerObject.css("text-align", this._textAlign);
        }
        // set datePicker monthNames
        this.datePickerObject.datepicker("option", "monthNames", this._monthNames);
        // set datePicker dayNames
        this.datePickerObject.datepicker("option", "dayNames", this._dayNames);
        // set textAlign property
        this.datePickerObject.css("text-align", this._textAlign);
        //set selectable Range
        if (this.selectableRange !== undefined) {
            if (Object.keys(this.selectableRange).length === 1) {
                this.datePickerObject.datepicker("option", "minDate", null);
                this.datePickerObject.datepicker("option", "maxDate", this.selectableRange[Object.keys(this.selectableRange)[0]]);
            }
            else if (Object.keys(this.selectableRange).length === 2) {
                this.datePickerObject.datepicker("option", "minDate", this.selectableRange[Object.keys(this.selectableRange)[0]]);
                this.datePickerObject.datepicker("option", "maxDate", this.selectableRange[Object.keys(this.selectableRange)[1]]);
            }
            else {
            }
        }
        if (this.width && typeof (this.width) === "string" && this.width.indexOf("%") === -1) {
            $(this.datefield.nativeElement).width(this._width);
        }
        else {
            this._renderer.setStyle(this.datefield.nativeElement.parentElement, 'width', this.width);
            this._renderer.setStyle(this.datefield.nativeElement, 'width', "90%");
        }
        this.datePickerObject.datepicker('setDate', this._selectedDate);
        // Added by Rihab.J @07/12/2018 - needed to be used in dynamically added SwtDateField.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtDateField');
        // set id to SwtDateField DOM.
        if (this.id) {
            $($(this.elem.nativeElement)[0]).attr("id", this.id);
        }
        //-Add ToolTip.
        if (this._toolTip && $(this.datefield.nativeElement).length > 0) {
            $($(this.datefield.nativeElement)[0]).attr("title", this._toolTip);
        }
        $($(this.datefield.nativeElement)[0]).tooltip({
            show: { duration: 800, delay: 500 },
            open: (/**
             * @param {?} event
             * @param {?} ui
             * @return {?}
             */
            function (event, ui) {
                $(this).removeAttr('title');
            })
        });
        this.logger.debug("[ ngOnInit ] end.");
    };
    /**
     * @return {?}
     */
    SwtDateField.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        this.showToday ? this.selectedDate = new Date() : null;
        this.datePickerObject.datepicker("option", "currentText", "Now");
    };
    /**
     * This method is used to set visibility of the component.
     * @param visibility
     */
    /**
     * This method is used to set visibility of the component.
     * @param {?} visibility
     * @return {?}
     */
    SwtDateField.prototype.setVisible = /**
     * This method is used to set visibility of the component.
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        this._visibility = visibility;
        visibility ? $(this.elem.nativeElement).show() : $(this.elem.nativeElement).hide();
    };
    /**
     * This function is used to parse the given date string to date
     * This function converts the given date string to MM/DD/YYYY
     * format and then parse the date
     *
     * param dateString: String
     * param formatString: String
     * @return Date
     */
    /**
     * This function is used to parse the given date string to date
     * This function converts the given date string to MM/DD/YYYY
     * format and then parse the date
     *
     * param dateString: String
     * param formatString: String
     * @param {?} dateString
     * @param {?=} formatString
     * @return {?} Date
     */
    SwtDateField.prototype.parseDate = /**
     * This function is used to parse the given date string to date
     * This function converts the given date string to MM/DD/YYYY
     * format and then parse the date
     *
     * param dateString: String
     * param formatString: String
     * @param {?} dateString
     * @param {?=} formatString
     * @return {?} Date
     */
    function (dateString, formatString) {
        if (formatString === void 0) { formatString = null; }
        /** @type {?} */
        var date = null;
        /** @type {?} */
        var dateSplitFragments = new Array();
        /** @type {?} */
        var delims = "";
        formatString == null ? formatString = this.formatString : null;
        try {
            if (dateString == null) {
                return null;
            }
            if (dateString.length != 10 || (dateString.indexOf("/") === -1 && dateString.indexOf("/") === -1)) {
                return null;
            }
            if (dateString !== null && dateString !== '' && dateString !== undefined) {
                if (formatString !== null && formatString !== undefined && formatString !== undefined) {
                    if (formatString.toUpperCase().indexOf("DD") === 0) {
                        dateSplitFragments = this.spliter(dateString)["dateSplitFragments"];
                        delims = this.spliter(dateString)["delims"];
                        if (dateSplitFragments) {
                            if (dateSplitFragments[1] > 12 || dateSplitFragments[1] < 1) {
                                date = null;
                            }
                            else if (dateSplitFragments[0] > 31 || dateSplitFragments[0] < 1) {
                                date = null;
                            }
                            else {
                                date = null;
                                date = new Date(dateSplitFragments[1] + delims + dateSplitFragments[0] + delims + dateSplitFragments[2]);
                            }
                        }
                        else {
                            date = null;
                        }
                    }
                    else {
                        dateSplitFragments = this.spliter(dateString)["dateSplitFragments"];
                        delims = this.spliter(dateString)["delims"];
                        if (Number(dateSplitFragments[0]) > 12 || Number(dateSplitFragments[1]) < 1) {
                            date = null;
                        }
                        else if (Number(dateSplitFragments[1]) > 31 || Number(dateSplitFragments[0]) < 1) {
                            date = null;
                        }
                        else {
                            date = new Date(dateString);
                        }
                    }
                }
            }
            else {
                date = null;
            }
        }
        catch (e) {
            this.logger.error("parseDate method ", e);
        }
        return date;
    };
    /**
     * This method is used to set style
     * to component
     * @param proerty
     * @param value
     */
    /**
     * This method is used to set style
     * to component
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    SwtDateField.prototype.setStyle = /**
     * This method is used to set style
     * to component
     * @param {?} attribute
     * @param {?} value
     * @return {?}
     */
    function (attribute, value) {
        var _this = this;
        setTimeout((/**
         * @return {?}
         */
        function () {
            $(_this.elem.nativeElement.children[0]).css(attribute, value);
        }), 0);
    };
    /**
     * This function formats the selected date to display
     *
     * param value: Date
     * @return String
     */
    /**
     * This function formats the selected date to display
     *
     * param value: Date
     * @param {?} value
     * @return {?} String
     */
    SwtDateField.prototype.formatDate = /**
     * This function formats the selected date to display
     *
     * param value: Date
     * @param {?} value
     * @return {?} String
     */
    function (value) {
        this.logger.debug("Enter [formatDate] id=" + this.id + ", value=" + value);
        //Flag to identify whether the format string contains "-" as separator
        /** @type {?} */
        var flag = false;
        if (this.formatString.indexOf("-") > -1) {
            //Set format string. Replace "-" with "/" as flex does not support "-" as a separator
            this._formatString = this._formatString.replace(/-/g, "/");
            flag = true;
        }
        else {
            this._formatString = this.formatString;
        }
        //Format the date
        /** @type {?} */
        var date = this.getAbbreviationFromDate(value);
        //Convert the date string to its original format
        if (flag) {
            date = date.replace(/\//g, "-");
        }
        //Format the date and returns the same
        this.logger.debug("Exit [formatDate] id=" + this.id + ", date=" + date);
        return date;
    };
    /**
     * This method is used to show dropDown.
     */
    /**
     * This method is used to show dropDown.
     * @return {?}
     */
    SwtDateField.prototype.openDropDown = /**
     * This method is used to show dropDown.
     * @return {?}
     */
    function () {
        var _this = this;
        setTimeout((/**
         * @return {?}
         */
        function () {
            _this.datePickerObject.datepicker("show");
        }), 0);
    };
    /**
     * getAbbreviationFromDate
     *
     * This funtion is used to get abreviation from date when it is in DD-MMM-YYYY format
     */
    /**
     * getAbbreviationFromDate
     *
     * This funtion is used to get abreviation from date when it is in DD-MMM-YYYY format
     * @param {?} date
     * @return {?}
     */
    SwtDateField.prototype.getAbbreviationFromDate = /**
     * getAbbreviationFromDate
     *
     * This funtion is used to get abreviation from date when it is in DD-MMM-YYYY format
     * @param {?} date
     * @return {?}
     */
    function (date) {
        /** @type {?} */
        var formattedDate = "";
        /** @type {?} */
        var day;
        /** @type {?} */
        var month;
        /** @type {?} */
        var year;
        try {
            if (date) {
                day = date.getDate();
                month = date.getMonth();
                year = date.getFullYear();
                if (this.formatString.toUpperCase() === "DD-M-YY") {
                    formattedDate = this.loadingZero(day) + "-" + this._monthNames[month] + "-" + this.loadingZero(year);
                }
                else if (this.formatString.toUpperCase() === "MM/DD/YY") {
                    formattedDate = this.loadingZero(month + 1) + "/" + this.loadingZero(day) + "/" + this.loadingZero(year);
                }
                else if (this.formatString.toUpperCase() === "DD/MM/YY") {
                    formattedDate = this.loadingZero(day) + "/" + this.loadingZero(month + 1) + "/" + this.loadingZero(year);
                }
                else {
                    throw new Error("Specify the dateField String Format!");
                }
            }
        }
        catch (e) {
            this.logger.error("getAbbreviationFromDate method :", e);
        }
        return formattedDate;
    };
    /**
     * Returns the Date object
     */
    /**
     * Returns the Date object
     * @return {?}
     */
    SwtDateField.prototype.getDate = /**
     * Returns the Date object
     * @return {?}
     */
    function () {
        return this.datePickerObject.datepicker("getDate");
    };
    /**
     * @return {?}
     */
    SwtDateField.prototype.setFocus = /**
     * @return {?}
     */
    function () {
        if (this.datePickerObject !== undefined) {
            this.datePickerObject.focus();
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtDateField.prototype.spyChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.originalValue == undefined) {
            this.originalValue = "";
        }
        if (this.originalValue == event) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    };
    /**
     * @return {?}
     */
    SwtDateField.prototype.resetOriginalValue = /**
     * @return {?}
     */
    function () {
        this.originalValue = this._text;
        this.spyChanges(this._text);
    };
    /**
     * This method is used to update date.
     */
    /**
     * This method is used to update date.
     * @private
     * @param {?} res
     * @return {?}
     */
    SwtDateField.prototype.updateDate = /**
     * This method is used to update date.
     * @private
     * @param {?} res
     * @return {?}
     */
    function (res) {
        this._text = $(this.datefield.nativeElement).val();
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtDateField.prototype.loadingZero = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var rtn = "";
        if (value >= 10) {
            rtn = value + "";
        }
        else if (value > 0 && value < 10) {
            rtn = "0" + value;
        }
        else {
            rtn = "00";
        }
        return rtn;
    };
    /**
     * This method is used to detect date delimiter
     * and split the dateString.
     * <AUTHOR>
     * @param dateString
     */
    /**
     * This method is used to detect date delimiter
     * and split the dateString.
     * <AUTHOR>
     * @private
     * @param {?} dateString
     * @return {?}
     */
    SwtDateField.prototype.spliter = /**
     * This method is used to detect date delimiter
     * and split the dateString.
     * <AUTHOR>
     * @private
     * @param {?} dateString
     * @return {?}
     */
    function (dateString) {
        /** @type {?} */
        var tab = new Array();
        if (dateString.indexOf("-") !== -1) {
            tab["dateSplitFragments"] = dateString.split("-");
            tab["delims"] = "-";
        }
        else if (dateString.indexOf("/") !== -1) {
            tab["dateSplitFragments"] = dateString.split("/");
            tab["delims"] = "/";
        }
        else {
            // throw new Error("parseDate(dateString, [ERROR] -> formatString) formatString only '-' and '/' are accepted as delemeter");
        }
        return tab;
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    SwtDateField.prototype.isValidDate = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return value.indexOf("Invalid Date") === -1;
    };
    SwtDateField.decorators = [
        { type: Component, args: [{
                    selector: 'SwtDateField',
                    template: "\n          <input popper=\"{{this.toolTipPreviousValue}}\"\n          [popperTrigger]=\"'hover'\"\n          [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n          [popperPlacement]=\"'bottom'\"\n          [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\" selector=\"SwtDatefield\" #datefield type=\"text\" class=\"mdatepicker\" tabindex=\"{{tabIndex}}\">\n  ",
                    styles: ["\n      .mdatepicker {\n           height: 22px;\n           border-left:1px solid #D3D5D6;\n           border-right:1px solid #D3D5D6;\n           border-bottom:1px solid #D3D5D6;\n           border-top: 1px solid #6D6F70;\n           padding: 0px 5px 0px 5px;\n           font-size: 11px;\n           font-family: sans-serif;\n           font-weight: normal;\n           margin: 0px 5px 5px 0px !important;\n           color: #000; /*this line is added to set the color of datefield as black when using SwtFieldSet*/\n      }\n\n      .hide {\n           display: none;\n      }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtDateField.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    SwtDateField.propDecorators = {
        onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
        onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
        datefield: [{ type: ViewChild, args: ["datefield",] }],
        tabIndex: [{ type: Input, args: ["tabIndex",] }],
        restrict: [{ type: Input, args: ["restrict",] }],
        toolTip: [{ type: Input, args: ['toolTip',] }],
        textDictionaryId: [{ type: Input, args: ['tooltipDictionaryId',] }],
        toolTipPreviousValue: [{ type: Input }],
        id: [{ type: Input, args: ["id",] }],
        openEventOutPut: [{ type: Output, args: ["open",] }],
        closeEventOutPut: [{ type: Output, args: ["close",] }],
        keyDownEventOutPut: [{ type: Output, args: ["keyDown",] }],
        changeEventOutPut: [{ type: Output, args: ["change",] }],
        focusEventOutPut: [{ type: Output, args: ["focus",] }],
        focusOutEventOutPut: [{ type: Output, args: ["focusOut",] }],
        keyFocusChange: [{ type: Output, args: ["keyFocusChange",] }],
        enabled: [{ type: Input }],
        editable: [{ type: Input }],
        width: [{ type: Input }],
        text: [{ type: Input }],
        textAlign: [{ type: Input }],
        selectableRange: [{ type: Input }],
        showToday: [{ type: Input }],
        visible: [{ type: Input, args: ['visible',] }]
    };
    return SwtDateField;
}());
export { SwtDateField };
if (false) {
    /** @type {?} */
    SwtDateField.prototype.originalValue;
    /** @type {?} */
    SwtDateField.prototype.onSpyChange;
    /** @type {?} */
    SwtDateField.prototype.onSpyNoChange;
    /** @type {?} */
    SwtDateField.prototype.datefield;
    /** @type {?} */
    SwtDateField.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._toolTipPreviousObject;
    /** @type {?} */
    SwtDateField.prototype.restrict;
    /** @type {?} */
    SwtDateField.prototype.id;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.firstCall;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showOnFlag;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.datePickerObject;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showToDay;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._change;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.interrupted;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.openEventOutPut;
    /** @type {?} */
    SwtDateField.prototype.closeEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.keyDownEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.changeEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.focusEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.focusOutEventOutPut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.keyFocusChange;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showYearSelect;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._showMonthSelect;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._formatString;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._monthNames;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._dayNames;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._selectableRange;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._open;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._close;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._keyDown;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._selectedDate;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtDateField.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWRhdGVmaWVsZC5jb21wb25lbnQuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvY29udHJvbHMvc3d0LWRhdGVmaWVsZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE9BQU8sRUFFSCxTQUFTLEVBQ1QsVUFBVSxFQUNWLFlBQVksRUFDWixLQUFLLEVBRUwsTUFBTSxFQUNOLFNBQVMsRUFDVCxTQUFTLEVBQ1osTUFBTSxlQUFlLENBQUM7QUFDdkIsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLDJCQUEyQixDQUFDO0FBQ2pELE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSxtQ0FBbUMsQ0FBQztBQUMvRCxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0seUJBQXlCLENBQUM7QUFDdEQsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLDJCQUEyQixDQUFDOzs7SUFLOUMsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQUM7QUFFM0I7SUE0Rkksc0JBQW9CLElBQWdCLEVBQVUsYUFBNEIsRUFBVSxTQUFvQjtRQUFwRixTQUFJLEdBQUosSUFBSSxDQUFZO1FBQVUsa0JBQWEsR0FBYixhQUFhLENBQWU7UUFBVSxjQUFTLEdBQVQsU0FBUyxDQUFXO1FBN0RqRixnQkFBVyxHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDO1FBQ3ZELGtCQUFhLEdBQXNCLElBQUksWUFBWSxFQUFPLENBQUM7UUFJNUUsMkJBQXNCLEdBQVEsSUFBSSxDQUFDOztRQXFDbkMsYUFBUSxHQUFNLEVBQUUsQ0FBQztRQUNqQixjQUFTLEdBQVksSUFBSSxDQUFDO1FBQzFCLGdCQUFXLEdBQUcsSUFBSSxDQUFDO1FBQ25CLFdBQU0sR0FBVyxJQUFJLENBQUM7UUFDdEIsZ0JBQVcsR0FBRyxRQUFRLENBQUM7UUFHdkIsWUFBTyxHQUFHLElBQUksUUFBUSxFQUFFLENBQUM7O1FBRXpCLGdCQUFXLEdBQVcsS0FBSyxDQUFDOztRQUdaLG9CQUFlLEdBQUcsSUFBSSxZQUFZLEVBQVksQ0FBQztRQUMvQyxxQkFBZ0IsR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDO1FBQzdDLHVCQUFrQixHQUFHLElBQUksWUFBWSxFQUFZLENBQUM7UUFDbkQsc0JBQWlCLEdBQUcsSUFBSSxZQUFZLEVBQVksQ0FBQztRQUNsRCxxQkFBZ0IsR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDO1FBQzdDLHdCQUFtQixHQUFHLElBQUksWUFBWSxFQUFZLENBQUM7UUFDN0MsbUJBQWMsR0FBRyxJQUFJLFlBQVksRUFBWSxDQUFDO1FBS3hFLGFBQVEsR0FBRyxJQUFJLENBQUM7UUFxQmhCLGNBQVMsR0FBRyxJQUFJLENBQUM7UUFtQ2pCLFVBQUssR0FBVyxFQUFFLENBQUM7UUFxQ25CLG9CQUFlLEdBQUcsSUFBSSxDQUFDO1FBUXZCLHFCQUFnQixHQUFHLEtBQUssQ0FBQztRQVF6QixlQUFVLEdBQUcsU0FBUyxDQUFDO1FBVXZCLGtCQUFhLEdBQUcsVUFBVSxDQUFDO1FBc0IzQixnQkFBVyxHQUFHLENBQUMsU0FBUyxFQUFFLFVBQVUsRUFBRSxPQUFPLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxXQUFXLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVLENBQUMsQ0FBQztRQVl6SSxjQUFTLEdBQUcsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQzs7UUFxQ2hELFVBQUssR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBUXZCLFdBQU0sR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBUXhCLGFBQVEsR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBUTFCLFdBQU0sR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBUXhCLGNBQVMsR0FBRyxJQUFJLFFBQVEsRUFBRSxDQUFDO1FBUTNCLGtCQUFhLEdBQVMsSUFBSSxDQUFDO1FBek8vQixJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLGNBQWMsRUFBRSxhQUFhLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDbkUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsK0JBQStCLENBQUMsQ0FBQztJQUN2RCxDQUFDO0lBdkRBLHNCQUNXLGlDQUFPO1FBSG5CLHNDQUFzQztRQUNyQyxxSEFBcUg7Ozs7Ozs7O1FBQ3JILFVBQ21CLEtBQWE7WUFDNUIsSUFBSSxDQUFDLFFBQVEsR0FBRSxLQUFLLENBQUM7WUFDckIsSUFBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsRUFBQztnQkFDL0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUMzQyxJQUFJLEVBQUUsRUFBRSxRQUFRLEVBQUUsR0FBRyxFQUFFLEtBQUssRUFBRyxHQUFHLEVBQUU7b0JBQ3BDLElBQUk7Ozs7O29CQUFFLFVBQVUsS0FBSyxFQUFFLEVBQUU7d0JBQ3JCLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQ2hDLENBQUMsQ0FBQTtpQkFDSixDQUFDLENBQUM7YUFDTDtRQUVMLENBQUM7OztPQUFBO0lBRUYsc0JBQWtDLDBDQUFnQjs7Ozs7UUFBbEQsVUFBbUQsS0FBSztZQUNwRCxJQUFHLEtBQUssRUFBRTtnQkFDTixJQUFJLENBQUMsUUFBUSxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQTthQUNuRDtRQUNMLENBQUM7OztPQUFBO0lBRUQsc0JBQ0ksOENBQW9COzs7O1FBSXhCO1lBQ0ksT0FBTyxJQUFJLENBQUMsc0JBQXNCLENBQUM7UUFDdkMsQ0FBQzs7Ozs7UUFQRCxVQUN5QixLQUFLO1lBQzFCLElBQUksQ0FBQyxzQkFBc0IsR0FBRyxLQUFLLENBQUM7UUFDeEMsQ0FBQzs7O09BQUE7SUFrQ0Qsc0JBQUksaUNBQU87Ozs7UUFBWDtZQUNJLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztRQUN6QixDQUFDO1FBQ0QsK0JBQStCOzs7Ozs7UUFDL0IsVUFDWSxLQUFjO1lBQ3RCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDN0IsSUFBSSxLQUFLLEtBQUssT0FBTyxFQUFFO29CQUNuQixJQUFJLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQztpQkFDekI7cUJBQU07b0JBQ0gsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUM7aUJBQ3BCO2FBQ0o7aUJBQU07Z0JBQ1AsSUFBSSxDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUM7YUFDckI7WUFDTCxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsS0FBSyxTQUFTLEVBQUU7Z0JBQ3JDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLFVBQVUsRUFBRSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDdkUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDakQ7UUFDTCxDQUFDOzs7T0FqQkE7SUFtQkQsc0JBQUksa0NBQVE7Ozs7UUFBWjtZQUNJLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztRQUMxQixDQUFDO1FBQ0QsZ0NBQWdDOzs7Ozs7UUFDaEMsVUFDYSxRQUFpQjtZQUMxQixJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSyxRQUFRLEVBQUU7Z0JBQ2hDLElBQUksUUFBUSxLQUFLLE9BQU8sRUFBRTtvQkFDdEIsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7b0JBQ3ZCLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxDQUFDO2lCQUNqRTtxQkFBTTtvQkFDSCxJQUFJLENBQUMsU0FBUyxDQUFDO29CQUNmLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLEtBQUssQ0FBQyxDQUFDO2lCQUNsRTthQUNKO2lCQUFNO2dCQUNILElBQUksQ0FBQyxTQUFTLEdBQUcsUUFBUSxDQUFDO2dCQUMxQixDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO2FBQy9FO1FBQ0wsQ0FBQzs7O09BaEJBO0lBa0JELHNCQUFJLCtCQUFLOzs7O1FBQVQ7WUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDdkIsQ0FBQztRQUNELDZCQUE2Qjs7Ozs7O1FBQzdCLFVBQ1UsS0FBYTtZQUNuQixJQUFJLEtBQUssS0FBSyxTQUFTLEVBQUU7Z0JBQ3JCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtvQkFDM0IsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2lCQUM5QjtxQkFBTTtvQkFDSCxJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztpQkFDdkI7YUFDSjtRQUNMLENBQUM7OztPQVhBO0lBYUQsc0JBQUksOEJBQUk7Ozs7UUFBUjtZQUNJLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQztRQUN0QixDQUFDO1FBQ0QsNEJBQTRCOzs7Ozs7UUFDNUIsVUFDUyxLQUFhO1lBQ2xCLElBQUk7Z0JBQ0EsSUFBSSxLQUFLLEtBQUssU0FBUyxJQUFJLEtBQUssS0FBSyxJQUFJLElBQUksS0FBSyxLQUFLLEVBQUUsRUFBRTtvQkFDdkQsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLENBQUM7b0JBQ2xELElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO2lCQUNyQjtxQkFBTSxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxhQUFhLENBQUMsS0FBSyxJQUFJLEVBQUU7b0JBQzNELElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFDOzt3QkFDZixTQUFTLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLGFBQWEsQ0FBQztvQkFDekQsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsU0FBUyxDQUFDLENBQUM7aUJBQzFEO3FCQUFNO29CQUNILElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFDO29CQUNuQixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUM3QztnQkFDRCxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7b0JBQ2hCLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQztvQkFDaEMsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7aUJBQzFCO2FBQ0o7WUFBQyxPQUFPLEtBQUssRUFBRTtnQkFDWixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsRUFBRSxLQUFLLENBQUMsQ0FBQzthQUNoRDtRQUNMLENBQUM7OztPQXZCQTtJQXlCRCxzQkFBSSx3Q0FBYzs7OztRQUlsQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUM1QixDQUFDOzs7OztRQU5ELFVBQW1CLFdBQW9CO1lBQ25DLElBQUksQ0FBQyxXQUFXLEdBQUcsV0FBVyxDQUFDO1FBQ25DLENBQUM7OztPQUFBO0lBUUQsc0JBQUksd0NBQWM7Ozs7UUFBbEI7WUFDSSxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7UUFDaEMsQ0FBQztRQUNELDBIQUEwSDs7Ozs7O1FBQzFILFVBQW1CLEtBQWM7WUFDN0IsSUFBSSxDQUFDLGVBQWUsR0FBRyxLQUFLLENBQUM7UUFDakMsQ0FBQzs7O09BSkE7SUFNRCxzQkFBSSx5Q0FBZTs7OztRQUFuQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO1FBQ2pDLENBQUM7UUFDRCw0REFBNEQ7Ozs7OztRQUM1RCxVQUFvQixLQUFjO1lBQzlCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxLQUFLLENBQUM7UUFDbEMsQ0FBQzs7O09BSkE7SUFNRCxzQkFBSSxtQ0FBUzs7OztRQUFiO1lBQ0ksT0FBTyxJQUFJLENBQUMsVUFBVSxDQUFDO1FBQzNCLENBQUM7UUFDRCxhQUFhO1FBQ2IsaUNBQWlDOzs7Ozs7O1FBQ2pDLFVBQ2MsS0FBYTtZQUN2QixJQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztRQUM1QixDQUFDOzs7T0FOQTtJQVFELHNCQUFJLHNDQUFZOzs7O1FBQWhCO1lBQ0ksT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1FBQzlCLENBQUM7UUFDRDs7OztXQUlHOzs7Ozs7O1FBQ0gsVUFBaUIsS0FBYTtZQUMxQixLQUFLLEdBQUcsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQzVCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtnQkFDOUIsS0FBSyxHQUFHLEtBQUssQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxDQUFDO2FBQ3JEO1lBQ0QsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO2dCQUM3QixLQUFLLEdBQUcsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUM7YUFDbkQ7WUFDRCxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQztZQUMzQixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsS0FBSyxTQUFTLEVBQUU7Z0JBQ3JDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLFlBQVksRUFBRSxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7YUFDaEY7UUFDTCxDQUFDOzs7T0FsQkE7SUFvQkQsc0JBQUksb0NBQVU7Ozs7UUFBZDtZQUNJLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztRQUM1QixDQUFDO1FBQ0QsNERBQTREOzs7Ozs7UUFDNUQsVUFBZSxLQUFVO1lBQ3JCLElBQUksQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDO1lBQ3pCLElBQUksSUFBSSxDQUFDLGdCQUFnQixLQUFLLFNBQVMsRUFBRTtnQkFDckMsNEJBQTRCO2dCQUM1QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxZQUFZLEVBQUUsS0FBSyxDQUFDLENBQUM7YUFDbkU7UUFDTCxDQUFDOzs7T0FSQTtJQVVELHNCQUFJLGtDQUFROzs7O1FBQVo7WUFDSSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7UUFDMUIsQ0FBQztRQUNELDREQUE0RDs7Ozs7O1FBQzVELFVBQWEsS0FBVTtZQUNuQixJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztRQUMzQixDQUFDOzs7T0FKQTtJQU1ELHNCQUFJLHlDQUFlOzs7O1FBQW5CO1lBQ0ksT0FBTyxJQUFJLENBQUMsZ0JBQWdCLENBQUM7UUFDakMsQ0FBQzs7Ozs7UUFDRCxVQUNvQixLQUFhO1lBQzdCLElBQUk7Z0JBQ0EsSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7b0JBQ2pDLElBQUksSUFBSSxDQUFDLGdCQUFnQixLQUFLLFNBQVMsRUFBRTt3QkFDckMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM1RCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO3FCQUN2RjtvQkFDRCxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsS0FBSyxDQUFDO2lCQUNqQztxQkFBTSxJQUFJLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTs7d0JBQ2xDLE9BQU8sR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzs7d0JBQ3RDLE9BQU8sR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztvQkFDNUMsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEtBQUssU0FBUyxFQUFFO3dCQUNyQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsT0FBTyxDQUFDLENBQUM7d0JBQy9ELElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLFNBQVMsRUFBRSxPQUFPLENBQUMsQ0FBQztxQkFDbEU7b0JBQ0QsSUFBSSxDQUFDLGdCQUFnQixHQUFHLEtBQUssQ0FBQztpQkFDakM7cUJBQU07b0JBQ0gsTUFBTSxJQUFJLEtBQUssQ0FBQywwRUFBMEUsQ0FBQyxDQUFDO2lCQUMvRjthQUNKO1lBQUMsT0FBTyxDQUFDLEVBQUU7Z0JBQ1IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsMkJBQTJCLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDckQ7UUFDTCxDQUFDOzs7T0F4QkE7SUEyQkQsc0JBQUksOEJBQUk7Ozs7UUFBUjtZQUNJLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQztRQUN0QixDQUFDO1FBQ0QsNERBQTREOzs7Ozs7UUFDNUQsVUFBUyxPQUFpQjtZQUN0QixJQUFJLENBQUMsS0FBSyxHQUFHLE9BQU8sQ0FBQztRQUN6QixDQUFDOzs7T0FKQTtJQU1ELHNCQUFJLCtCQUFLOzs7O1FBQVQ7WUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDdkIsQ0FBQztRQUNELDREQUE0RDs7Ozs7O1FBQzVELFVBQVUsT0FBaUI7WUFDdkIsSUFBSSxDQUFDLE1BQU0sR0FBRyxPQUFPLENBQUM7UUFDMUIsQ0FBQzs7O09BSkE7SUFNRCxzQkFBSSxpQ0FBTzs7OztRQUFYO1lBQ0ksT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDO1FBQ3pCLENBQUM7UUFDRCw0REFBNEQ7Ozs7OztRQUM1RCxVQUFZLE9BQWlCO1lBQ3pCLElBQUksQ0FBQyxRQUFRLEdBQUcsT0FBTyxDQUFDO1FBQzVCLENBQUM7OztPQUpBO0lBTUQsc0JBQUksK0JBQUs7Ozs7UUFBVDtZQUNJLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUN2QixDQUFDO1FBQ0QsNERBQTREOzs7Ozs7UUFDNUQsVUFBVSxPQUFpQjtZQUN2QixJQUFJLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztRQUMxQixDQUFDOzs7T0FKQTtJQU1ELHNCQUFJLGtDQUFROzs7O1FBQVo7WUFDSSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7UUFDMUIsQ0FBQztRQUNELDREQUE0RDs7Ozs7O1FBQzVELFVBQWEsT0FBaUI7WUFDMUIsSUFBSSxDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUM7UUFDckIsQ0FBQzs7O09BSlI7SUFNRCxzQkFBSSxzQ0FBWTs7OztRQUFoQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUMxQixDQUFDO1FBQ0wsNERBQTREOzs7Ozs7UUFDNUQsVUFBaUIsSUFBVTtZQUN2QixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsS0FBSyxTQUFTLElBQUksSUFBSSxJQUFJLElBQUksRUFBRztnQkFDdEQsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDcEMsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUNuQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQzthQUNyRDtpQkFBSTtnQkFDRCxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQztnQkFDMUIsSUFBSSxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUM7Z0JBQ2hCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxDQUFDO2FBQ3JEO1FBQ0wsQ0FBQzs7O09BWkk7SUFhTCxzQkFBSSxtQ0FBUzs7OztRQUFiO1lBQ0ksT0FBTyxJQUFJLENBQUMsVUFBVSxDQUFDO1FBQzNCLENBQUM7Ozs7O1FBQ0QsVUFDYyxLQUFjO1lBQ3hCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDN0IsSUFBSSxLQUFLLEtBQUssTUFBTSxFQUFFO29CQUNsQixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztpQkFDMUI7cUJBQU07b0JBQ0gsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7aUJBQzNCO2FBQ0E7aUJBQU07Z0JBQ1AsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7YUFDM0I7UUFDTCxDQUFDOzs7T0FaQTtJQWFELHNCQUFJLGlDQUFPOzs7O1FBQVg7WUFDSSxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7UUFDcEIsQ0FBQztRQUNULHdDQUF3Qzs7Ozs7O1FBQ3hDLFVBQ1ksS0FBSztZQUNiLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDN0IsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUM7Z0JBQ3pCLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO2FBQ2pGO2lCQUFNO2dCQUNILElBQUksS0FBSyxLQUFLLE1BQU0sRUFBRTtvQkFDbEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7b0JBQ3BCLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO2lCQUN6QztxQkFBTTtvQkFDQyxJQUFJLENBQUMsV0FBVyxHQUFHLEtBQUssQ0FBQztvQkFDekIsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUM7aUJBQ3pDO2FBQ0E7UUFDTCxDQUFDOzs7T0FoQlE7SUFpQlQsc0JBQUksa0NBQVE7Ozs7UUFBWjtZQUNJLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztRQUN4QixDQUFDO1FBQ0QsNERBQTREOzs7Ozs7UUFDNUQsVUFBYSxRQUFrQjtZQUMzQixJQUFJLENBQUMsT0FBTyxHQUFHLFFBQVEsQ0FBQztRQUM1QixDQUFDOzs7T0FKQTs7OztJQUtELCtCQUFROzs7SUFBUjtRQUFBLGlCQTJIQztRQTFIRyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1FBQ3pDLDhCQUE4QjtRQUM5QixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQy9ELElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUM7WUFDN0IsTUFBTSxFQUFFLFFBQVE7WUFDaEIsV0FBVyxFQUFFLG1DQUFtQztZQUNoRCxlQUFlLEVBQUUsSUFBSTtZQUNyQixVQUFVLEVBQUUsSUFBSSxDQUFDLGFBQWE7WUFDOUIsVUFBVSxFQUFFLEVBQUU7WUFDZCxXQUFXLEVBQUUsSUFBSSxDQUFDLGdCQUFnQjtZQUNsQyxVQUFVLEVBQUUsSUFBSSxDQUFDLGVBQWU7WUFDaEMsVUFBVTs7OztZQUFFLFVBQUMsTUFBTTtnQkFDZixLQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztnQkFDbEMsS0FBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN2QixDQUFDLENBQUE7WUFDRCxPQUFPOzs7O1lBQUUsVUFBQyxNQUFNO2dCQUNaLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQ25DLEtBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQ3BCLEtBQUksQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDO2dCQUNwQixLQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO2dCQUNwQyxLQUFJLENBQUMsVUFBVSxDQUFDLEtBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNoQyxDQUFDLENBQUE7WUFDRCxRQUFROzs7O1lBQUUsVUFBQyxNQUFNO2dCQUNiLEtBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQ3JCLEtBQUksQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDO2dCQUNwQixLQUFJLENBQUMsYUFBYSxHQUFHLEtBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxFQUFFLEtBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUNuRSxDQUFDLENBQUE7U0FDSixDQUFDLENBQUM7UUFDSCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsRUFBRSxDQUFDLFNBQVM7Ozs7UUFBRSxVQUFDLE1BQU07WUFDdkMsS0FBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNyQyxLQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzFCLENBQUMsRUFBQyxDQUFDO1FBQ0gsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEVBQUUsQ0FBQyxPQUFPOzs7O1FBQUUsVUFBQyxNQUFNO1lBQ3JDLEtBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDeEIsS0FBSSxDQUFDLFVBQVUsQ0FBQyxLQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDaEMsQ0FBQyxFQUFDLENBQUM7UUFDSCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsRUFBRSxDQUFDLE9BQU87Ozs7UUFBRSxVQUFDLE1BQU07WUFDckMsS0FBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNuQyxLQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3BCLEtBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ2pDLDhDQUE4QztZQUM5QyxZQUFZLENBQUMsV0FBVyxHQUFHLEtBQUksQ0FBQyxFQUFFLENBQUM7UUFDdkMsQ0FBQyxFQUFDLENBQUM7UUFFSCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsRUFBRSxDQUFDLFVBQVU7Ozs7UUFBRSxVQUFDLE1BQU07WUFDeEMsS0FBSSxDQUFDLGFBQWEsR0FBRyxLQUFJLENBQUMsU0FBUyxDQUFDLEtBQUksQ0FBQyxJQUFJLEVBQUUsS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWxFLEtBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDdEMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN2QixLQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUNyQyxDQUFDLEVBQUMsQ0FBQztRQUVILHNCQUFzQjtRQUN0QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUN6Qyw4QkFBOEI7UUFDOUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsWUFBWSxFQUFFLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM3RSwrQkFBK0I7UUFDL0IsSUFBSSxJQUFJLENBQUMsT0FBTyxLQUFLLElBQUksRUFBRTtZQUN2QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxVQUFVLEVBQUUsS0FBSyxDQUFDLENBQUM7U0FDakU7YUFBTTtZQUNILElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLFVBQVUsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUNoRTtRQUNELG1DQUFtQztRQUNuQyxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssS0FBSyxFQUFFO1lBQ3hCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUM7U0FDMUM7YUFBTTtZQUNILElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFDLENBQUM7U0FDN0M7UUFDRCwrQkFBK0I7UUFDL0IsSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLElBQUksRUFBRTtZQUN4QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUNqRDthQUFNO1lBQ0gsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDaEQ7UUFDRCx1QkFBdUI7UUFDdkIsSUFBSSxJQUFJLENBQUMsVUFBVSxLQUFLLFNBQVMsRUFBRTtZQUMvQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7U0FDNUQ7UUFDRCw0QkFBNEI7UUFDNUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsWUFBWSxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUMzRSwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsVUFBVSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUN2RSx5QkFBeUI7UUFDekIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3pELHNCQUFzQjtRQUN0QixJQUFJLElBQUksQ0FBQyxlQUFlLEtBQUssU0FBUyxFQUFFO1lBQ3BDLElBQUksTUFBTSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDaEQsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxDQUFDO2dCQUM1RCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7YUFDckg7aUJBQU0sSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO2dCQUN2RCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQ2xILElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzthQUNySDtpQkFBTTthQUNOO1NBQ0o7UUFDRCxJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksT0FBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUU7WUFDbkYsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztTQUNwRDthQUFNO1lBQ0gsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsYUFBYSxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDekYsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsT0FBTyxFQUFFLEtBQUssQ0FBQyxDQUFDO1NBQ3pFO1FBQ0QsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBQ2hFLHNGQUFzRjtRQUN0RixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLGNBQWMsQ0FBQyxDQUFDO1FBQ2xFLDhCQUE4QjtRQUM5QixJQUFJLElBQUksQ0FBQyxFQUFFLEVBQUU7WUFDVixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztTQUN0RDtRQUVDLGVBQWU7UUFDbEIsSUFBRyxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLE1BQU0sR0FBRSxDQUFDLEVBQUM7WUFDMUQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7U0FDdEU7UUFFRCxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUM7WUFDMUMsSUFBSSxFQUFFLEVBQUUsUUFBUSxFQUFFLEdBQUcsRUFBRSxLQUFLLEVBQUcsR0FBRyxFQUFFO1lBQ3BDLElBQUk7Ozs7O1lBQUUsVUFBVSxLQUFLLEVBQUUsRUFBRTtnQkFDckIsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUNoQyxDQUFDLENBQUE7U0FDSixDQUFDLENBQUM7UUFFSCxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO0lBQzNDLENBQUM7Ozs7SUFDRCxzQ0FBZTs7O0lBQWY7UUFDSSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQztRQUN2RCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7SUFDckUsQ0FBQztJQUNEOzs7T0FHRzs7Ozs7O0lBQ0ksaUNBQVU7Ozs7O0lBQWpCLFVBQWtCLFVBQW1CO1FBQ2pDLElBQUksQ0FBQyxXQUFXLEdBQUcsVUFBVSxDQUFDO1FBQzlCLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO0lBQ3ZGLENBQUM7SUFDRDs7Ozs7Ozs7T0FRRzs7Ozs7Ozs7Ozs7O0lBQ0ksZ0NBQVM7Ozs7Ozs7Ozs7O0lBQWhCLFVBQWlCLFVBQWtCLEVBQUUsWUFBMkI7UUFBM0IsNkJBQUEsRUFBQSxtQkFBMkI7O1lBQ3hELElBQUksR0FBRyxJQUFJOztZQUNYLGtCQUFrQixHQUFHLElBQUksS0FBSyxFQUFFOztZQUNoQyxNQUFNLEdBQVcsRUFBRTtRQUN2QixZQUFZLElBQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1FBQy9ELElBQUk7WUFDQSxJQUFJLFVBQVUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3BCLE9BQU8sSUFBSSxDQUFDO2FBQ2Y7WUFDRCxJQUFJLFVBQVUsQ0FBQyxNQUFNLElBQUksRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxVQUFVLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUU7Z0JBQy9GLE9BQU8sSUFBSSxDQUFDO2FBQ2Y7WUFDRCxJQUFJLFVBQVUsS0FBSyxJQUFJLElBQUksVUFBVSxLQUFLLEVBQUUsSUFBSSxVQUFVLEtBQUssU0FBUyxFQUFFO2dCQUV0RSxJQUFJLFlBQVksS0FBSyxJQUFJLElBQUksWUFBWSxLQUFLLFNBQVMsSUFBSSxZQUFZLEtBQUssU0FBUyxFQUFFO29CQUNuRixJQUFJLFlBQVksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO3dCQUNoRCxrQkFBa0IsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLENBQUM7d0JBQ3BFLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDO3dCQUM1QyxJQUFJLGtCQUFrQixFQUFFOzRCQUNwQixJQUFJLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsSUFBSSxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUU7Z0NBQ3pELElBQUksR0FBRyxJQUFJLENBQUM7NkJBQ2Y7aUNBQU0sSUFBSSxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLElBQUksa0JBQWtCLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO2dDQUNoRSxJQUFJLEdBQUcsSUFBSSxDQUFDOzZCQUNmO2lDQUFNO2dDQUNILElBQUksR0FBRyxJQUFJLENBQUM7Z0NBQ1osSUFBSSxHQUFHLElBQUksSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxHQUFHLE1BQU0sR0FBRyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsR0FBRyxNQUFNLEdBQUcsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzs2QkFDNUc7eUJBQ0o7NkJBQU07NEJBQ0gsSUFBSSxHQUFHLElBQUksQ0FBQzt5QkFDZjtxQkFDSjt5QkFBTTt3QkFDSCxrQkFBa0IsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLENBQUM7d0JBQ3BFLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDO3dCQUM1QyxJQUFJLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsSUFBSSxNQUFNLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUU7NEJBQ3pFLElBQUksR0FBRyxJQUFJLENBQUM7eUJBQ2Y7NkJBQU0sSUFBSSxNQUFNLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLElBQUksTUFBTSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFOzRCQUNoRixJQUFJLEdBQUcsSUFBSSxDQUFDO3lCQUNmOzZCQUFNOzRCQUNILElBQUksR0FBRyxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQzt5QkFDL0I7cUJBQ0o7aUJBQ0o7YUFDSjtpQkFBTTtnQkFDSCxJQUFJLEdBQUcsSUFBSSxDQUFDO2FBQ2Y7U0FDSjtRQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQ1IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLEVBQUUsQ0FBQyxDQUFDLENBQUM7U0FDN0M7UUFDRCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDO0lBQ0E7Ozs7O09BS0c7Ozs7Ozs7O0lBQ0gsK0JBQVE7Ozs7Ozs7SUFBUixVQUFTLFNBQWlCLEVBQUUsS0FBYTtRQUF6QyxpQkFJQztRQUhHLFVBQVU7OztRQUFDO1lBQ1AsQ0FBQyxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUMsS0FBSyxDQUFDLENBQUM7UUFDaEUsQ0FBQyxHQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ1YsQ0FBQztJQUNGOzs7OztPQUtHOzs7Ozs7OztJQUNJLGlDQUFVOzs7Ozs7O0lBQWpCLFVBQWtCLEtBQVc7UUFDekIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsd0JBQXdCLEdBQUcsSUFBSSxDQUFDLEVBQUUsR0FBRyxVQUFVLEdBQUcsS0FBSyxDQUFDLENBQUM7OztZQUV2RSxJQUFJLEdBQVksS0FBSztRQUN6QixJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFO1lBQ3JDLHFGQUFxRjtZQUNyRixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FBQztZQUMzRCxJQUFJLEdBQUcsSUFBSSxDQUFDO1NBQ2Y7YUFBTTtZQUNILElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQztTQUMxQzs7O1lBRUcsSUFBSSxHQUFXLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUM7UUFDdEQsZ0RBQWdEO1FBQ2hELElBQUksSUFBSSxFQUFFO1lBQ04sSUFBSSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQ25DO1FBQ0Qsc0NBQXNDO1FBQ3RDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLHVCQUF1QixHQUFHLElBQUksQ0FBQyxFQUFFLEdBQUcsU0FBUyxHQUFHLElBQUksQ0FBQyxDQUFDO1FBQ3hFLE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFDRDs7T0FFRzs7Ozs7SUFDSSxtQ0FBWTs7OztJQUFuQjtRQUFBLGlCQUlDO1FBSEcsVUFBVTs7O1FBQUM7WUFDUCxLQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzdDLENBQUMsR0FBRSxDQUFDLENBQUMsQ0FBQztJQUNWLENBQUM7SUFDRDs7OztPQUlHOzs7Ozs7OztJQUNJLDhDQUF1Qjs7Ozs7OztJQUE5QixVQUErQixJQUFVOztZQUNqQyxhQUFhLEdBQVcsRUFBRTs7WUFDMUIsR0FBVzs7WUFDWCxLQUFhOztZQUNiLElBQVk7UUFDaEIsSUFBSTtZQUNBLElBQUksSUFBSSxFQUFFO2dCQUNWLEdBQUcsR0FBRyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7Z0JBQ3JCLEtBQUssR0FBRyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7Z0JBQ3hCLElBQUksR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7Z0JBQzFCLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxXQUFXLEVBQUUsS0FBSyxTQUFTLEVBQUU7b0JBQy9DLGFBQWEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDO2lCQUN4RztxQkFBTSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsV0FBVyxFQUFFLEtBQUssVUFBVSxFQUFFO29CQUN2RCxhQUFhLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7aUJBQzVHO3FCQUFNLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxXQUFXLEVBQUUsS0FBSyxVQUFVLEVBQUU7b0JBQ3ZELGFBQWEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQztpQkFDNUc7cUJBQU07b0JBQ0gsTUFBTSxJQUFJLEtBQUssQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO2lCQUMzRDthQUNBO1NBQ0o7UUFBQyxPQUFPLENBQUMsRUFBRTtZQUNSLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGtDQUFrQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1NBQzVEO1FBQ0QsT0FBTyxhQUFhLENBQUM7SUFDekIsQ0FBQztJQUNEOztPQUVHOzs7OztJQUNJLDhCQUFPOzs7O0lBQWQ7UUFDSSxPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDdkQsQ0FBQzs7OztJQUNNLCtCQUFROzs7SUFBZjtRQUNJLElBQUksSUFBSSxDQUFDLGdCQUFnQixLQUFLLFNBQVMsRUFBRTtZQUNyQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxFQUFFLENBQUM7U0FDakM7SUFDTCxDQUFDOzs7OztJQUNELGlDQUFVOzs7O0lBQVYsVUFBVyxLQUFVO1FBQ2pCLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxTQUFTLEVBQUU7WUFDakMsSUFBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUM7U0FDM0I7UUFDRCxJQUFJLElBQUksQ0FBQyxhQUFhLElBQUksS0FBSyxFQUFFO1lBQzdCLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEVBQUMsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFDLENBQUMsQ0FBQztTQUM3RDthQUNJO1lBQ0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsRUFBQyxRQUFRLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUMsQ0FBQyxDQUFDO1NBQzNEO1FBQ0QsQ0FBQztJQUNMLENBQUM7Ozs7SUFDRCx5Q0FBa0I7OztJQUFsQjtRQUNJLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNoQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUNoQyxDQUFDO0lBQ0Q7O09BRUc7Ozs7Ozs7SUFDSyxpQ0FBVTs7Ozs7O0lBQWxCLFVBQW1CLEdBQUc7UUFDbEIsSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztJQUN2RCxDQUFDOzs7Ozs7SUFDTyxrQ0FBVzs7Ozs7SUFBbkIsVUFBb0IsS0FBYTs7WUFDekIsR0FBRyxHQUFXLEVBQUU7UUFDcEIsSUFBSSxLQUFLLElBQUksRUFBRSxFQUFFO1lBQ2IsR0FBRyxHQUFHLEtBQUssR0FBRyxFQUFFLENBQUM7U0FDcEI7YUFBTSxJQUFJLEtBQUssR0FBRyxDQUFDLElBQUksS0FBSyxHQUFHLEVBQUUsRUFBRTtZQUNoQyxHQUFHLEdBQUcsR0FBRyxHQUFHLEtBQUssQ0FBQztTQUNyQjthQUFNO1lBQ0gsR0FBRyxHQUFHLElBQUksQ0FBQztTQUNkO1FBQ0QsT0FBTyxHQUFHLENBQUM7SUFDZixDQUFDO0lBQ0Q7Ozs7O09BS0c7Ozs7Ozs7OztJQUNLLDhCQUFPOzs7Ozs7OztJQUFmLFVBQWdCLFVBQWtCOztZQUN4QixHQUFHLEdBQUcsSUFBSSxLQUFLLEVBQUU7UUFDdkIsSUFBSSxVQUFVLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO1lBQ2hDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxHQUFHLFVBQVUsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEQsR0FBRyxDQUFDLFFBQVEsQ0FBQyxHQUFHLEdBQUcsQ0FBQztTQUN2QjthQUFNLElBQUksVUFBVSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtZQUN2QyxHQUFHLENBQUMsb0JBQW9CLENBQUMsR0FBRyxVQUFVLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xELEdBQUcsQ0FBQyxRQUFRLENBQUMsR0FBRyxHQUFHLENBQUM7U0FDdkI7YUFBTTtZQUNILDZIQUE2SDtTQUNoSTtRQUNELE9BQU8sR0FBRyxDQUFDO0lBQ2YsQ0FBQzs7Ozs7O0lBQ08sa0NBQVc7Ozs7O0lBQW5CLFVBQW9CLEtBQWE7UUFDN0IsT0FBTyxLQUFLLENBQUMsT0FBTyxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0lBQ2hELENBQUM7O2dCQS9zQkosU0FBUyxTQUFDO29CQUNQLFFBQVEsRUFBRSxjQUFjO29CQUN4QixRQUFRLEVBQUUscVpBTVg7NkJBQ1UsMGtCQWtCVjtpQkFDRjs7OztnQkE5Q0csVUFBVTtnQkFVTixhQUFhO2dCQUxqQixTQUFTOzs7OEJBNENSLE1BQU0sU0FBQyxhQUFhO2dDQUNwQixNQUFNLFNBQUMsZUFBZTs0QkFDdEIsU0FBUyxTQUFDLFdBQVc7MkJBRXJCLEtBQUssU0FBQyxVQUFVOzJCQUVoQixLQUFLLFNBQUMsVUFBVTswQkFHZixLQUFLLFNBQUMsU0FBUzttQ0FjaEIsS0FBSyxTQUFDLHFCQUFxQjt1Q0FNM0IsS0FBSztxQkFXTCxLQUFLLFNBQUMsSUFBSTtrQ0FjVixNQUFNLFNBQUMsTUFBTTttQ0FDYixNQUFNLFNBQUMsT0FBTztxQ0FDZCxNQUFNLFNBQUMsU0FBUztvQ0FDaEIsTUFBTSxTQUFDLFFBQVE7bUNBQ2YsTUFBTSxTQUFDLE9BQU87c0NBQ2QsTUFBTSxTQUFDLFVBQVU7aUNBQ2pCLE1BQU0sU0FBQyxnQkFBZ0I7MEJBVXZCLEtBQUs7MkJBcUJMLEtBQUs7d0JBb0JMLEtBQUs7dUJBZUwsS0FBSzs0QkFzREwsS0FBSztrQ0FrREwsS0FBSzs0QkFvRkwsS0FBSzswQkFnQkwsS0FBSyxTQUFDLFNBQVM7O0lBdVdwQixtQkFBQztDQUFBLEFBaHRCRCxJQWd0QkM7U0FuckJZLFlBQVk7OztJQUNyQixxQ0FBa0I7O0lBQ2xCLG1DQUFnRjs7SUFDaEYscUNBQW9GOztJQUNwRixpQ0FBOEM7O0lBRTlDLGdDQUE0Qjs7Ozs7SUFDNUIsOENBQTJDOztJQUMzQyxnQ0FBNEI7O0lBa0M1QiwwQkFBZ0I7Ozs7O0lBRWhCLGdDQUF5Qjs7Ozs7SUFDekIsaUNBQWtDOzs7OztJQUNsQyxtQ0FBMkI7Ozs7O0lBQzNCLDhCQUE4Qjs7Ozs7SUFDOUIsbUNBQStCOzs7OztJQUMvQix3Q0FBOEI7Ozs7O0lBQzlCLGtDQUE0Qjs7Ozs7SUFDNUIsK0JBQWlDOzs7OztJQUVqQyxtQ0FBb0M7Ozs7O0lBR3BDLHVDQUF1RTs7SUFDdkUsd0NBQXdFOzs7OztJQUN4RSwwQ0FBNkU7Ozs7O0lBQzdFLHlDQUEyRTs7Ozs7SUFDM0Usd0NBQXlFOzs7OztJQUN6RSwyQ0FBK0U7Ozs7O0lBQy9FLHNDQUFnRjs7Ozs7SUFLaEYsZ0NBQXdCOzs7OztJQXFCeEIsaUNBQXlCOzs7OztJQW9CekIsOEJBQXVCOzs7OztJQWV2Qiw2QkFBMkI7Ozs7O0lBcUMzQix1Q0FBK0I7Ozs7O0lBUS9CLHdDQUFpQzs7Ozs7SUFRakMsa0NBQStCOzs7OztJQVUvQixxQ0FBbUM7Ozs7O0lBc0JuQyxtQ0FBaUo7Ozs7O0lBWWpKLGlDQUF3RDs7Ozs7SUFReEQsd0NBQWlDOzs7OztJQTZCakMsNkJBQStCOzs7OztJQVEvQiw4QkFBZ0M7Ozs7O0lBUWhDLGdDQUFrQzs7Ozs7SUFRbEMsOEJBQWdDOzs7OztJQVFoQyxpQ0FBbUM7Ozs7O0lBUW5DLHFDQUFtQzs7Ozs7SUExT3ZCLDRCQUF3Qjs7Ozs7SUFBRSxxQ0FBb0M7Ozs7O0lBQUUsaUNBQTRCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICAgIEFmdGVyVmlld0luaXQsXHJcbiAgICBDb21wb25lbnQsXHJcbiAgICBFbGVtZW50UmVmLFxyXG4gICAgRXZlbnRFbWl0dGVyLFxyXG4gICAgSW5wdXQsXHJcbiAgICBPbkluaXQsXHJcbiAgICBPdXRwdXQsXHJcbiAgICBSZW5kZXJlcjIsXHJcbiAgICBWaWV3Q2hpbGRcclxufSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuaW1wb3J0IHtMb2dnZXJ9IGZyb20gXCIuLi9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7Zm9jdXNNYW5hZ2VyfSBmcm9tIFwiLi4vbWFuYWdlcnMvZm9jdXMtbWFuYWdlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7Q29tbW9uU2VydmljZX0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dFV0aWwgfSBmcm9tICcuLi91dGlscy9zd3QtdXRpbC5zZXJ2aWNlJztcclxuXHJcbi8qIEltcG9ydCBKUXVlcnkgYW5kIEpRdWVyeSB1aSAqL1xyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbi8vaW1wb3J0ICQgZnJvbSAnanF1ZXJ5JztcclxuY29uc3QgJCA9IHJlcXVpcmUoJ2pxdWVyeScpO1xyXG5cclxuQENvbXBvbmVudCh7XHJcbiAgICBzZWxlY3RvcjogJ1N3dERhdGVGaWVsZCcsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgICAgICAgPGlucHV0IHBvcHBlcj1cInt7dGhpcy50b29sVGlwUHJldmlvdXNWYWx1ZX19XCJcclxuICAgICAgICAgIFtwb3BwZXJUcmlnZ2VyXT1cIidob3ZlcidcIlxyXG4gICAgICAgICAgW3BvcHBlckRpc2FibGVkXT1cInRvb2xUaXBQcmV2aW91c1ZhbHVlID09PSBudWxsID8gdHJ1ZSA6IGZhbHNlXCJcclxuICAgICAgICAgIFtwb3BwZXJQbGFjZW1lbnRdPVwiJ2JvdHRvbSdcIlxyXG4gICAgICAgICAgW25nQ2xhc3NdPVwieydib3JkZXItb3JhbmdlLXByZXZpb3VzJzogdG9vbFRpcFByZXZpb3VzVmFsdWUgIT0gbnVsbH1cIiBzZWxlY3Rvcj1cIlN3dERhdGVmaWVsZFwiICNkYXRlZmllbGQgdHlwZT1cInRleHRcIiBjbGFzcz1cIm1kYXRlcGlja2VyXCIgdGFiaW5kZXg9XCJ7e3RhYkluZGV4fX1cIj5cclxuICBgLFxyXG4gICAgc3R5bGVzOiBbYFxyXG4gICAgICAubWRhdGVwaWNrZXIge1xyXG4gICAgICAgICAgIGhlaWdodDogMjJweDtcclxuICAgICAgICAgICBib3JkZXItbGVmdDoxcHggc29saWQgI0QzRDVENjtcclxuICAgICAgICAgICBib3JkZXItcmlnaHQ6MXB4IHNvbGlkICNEM0Q1RDY7XHJcbiAgICAgICAgICAgYm9yZGVyLWJvdHRvbToxcHggc29saWQgI0QzRDVENjtcclxuICAgICAgICAgICBib3JkZXItdG9wOiAxcHggc29saWQgIzZENkY3MDtcclxuICAgICAgICAgICBwYWRkaW5nOiAwcHggNXB4IDBweCA1cHg7XHJcbiAgICAgICAgICAgZm9udC1zaXplOiAxMXB4O1xyXG4gICAgICAgICAgIGZvbnQtZmFtaWx5OiBzYW5zLXNlcmlmO1xyXG4gICAgICAgICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7XHJcbiAgICAgICAgICAgbWFyZ2luOiAwcHggNXB4IDVweCAwcHggIWltcG9ydGFudDtcclxuICAgICAgICAgICBjb2xvcjogIzAwMDsgLyp0aGlzIGxpbmUgaXMgYWRkZWQgdG8gc2V0IHRoZSBjb2xvciBvZiBkYXRlZmllbGQgYXMgYmxhY2sgd2hlbiB1c2luZyBTd3RGaWVsZFNldCovXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5oaWRlIHtcclxuICAgICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICB9XHJcbiAgYF1cclxufSlcclxuZXhwb3J0IGNsYXNzIFN3dERhdGVGaWVsZCAgaW1wbGVtZW50cyBPbkluaXQsIEFmdGVyVmlld0luaXQge1xyXG4gICAgb3JpZ2luYWxWYWx1ZTphbnk7XHJcbiAgICBAT3V0cHV0KCdvblNweUNoYW5nZScpIG9uU3B5Q2hhbmdlOiBFdmVudEVtaXR0ZXI8YW55PiA9IG5ldyBFdmVudEVtaXR0ZXI8YW55PigpO1xyXG4gICAgQE91dHB1dCgnb25TcHlOb0NoYW5nZScpIG9uU3B5Tm9DaGFuZ2U6IEV2ZW50RW1pdHRlcjxhbnk+ID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgICBAVmlld0NoaWxkKFwiZGF0ZWZpZWxkXCIpIGRhdGVmaWVsZDogRWxlbWVudFJlZjtcclxuICAgIC8vIElucHV0IHRvIGhhbmRsZSBkYXRlUGlja2VyXHJcbiAgICBASW5wdXQoXCJ0YWJJbmRleFwiKSB0YWJJbmRleDtcclxuICAgIHByaXZhdGUgX3Rvb2xUaXBQcmV2aW91c09iamVjdDogYW55ID0gbnVsbDtcclxuICAgIEBJbnB1dChcInJlc3RyaWN0XCIpIHJlc3RyaWN0O1xyXG4gICAgLy8gSW5wdXQgdG8gaGFuZGxlIGRhdGVQaWNrZXIgdGFiSW5kZXhcclxuICAgICAvLy0tLVRvb2xUaXAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgICBASW5wdXQoJ3Rvb2xUaXAnKVxyXG4gICAgIHB1YmxpYyBzZXQgdG9vbFRpcCh2YWx1ZTogc3RyaW5nKXtcclxuICAgICAgICAgdGhpcy5fdG9vbFRpcD0gdmFsdWU7XHJcbiAgICAgICAgIGlmKCQodGhpcy5kYXRlZmllbGQubmF0aXZlRWxlbWVudCkpe1xyXG4gICAgICAgICAgICAgJCgkKHRoaXMuZGF0ZWZpZWxkLm5hdGl2ZUVsZW1lbnQpWzBdKS50b29sdGlwKHtcclxuICAgICAgICAgICAgICAgIHNob3c6IHsgZHVyYXRpb246IDgwMCwgZGVsYXkgOiA1MDAgfSxcclxuICAgICAgICAgICAgICAgIG9wZW46IGZ1bmN0aW9uKCBldmVudCwgdWkgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgJCh0aGlzKS5yZW1vdmVBdHRyKCd0aXRsZScpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgfVxyXG4gXHJcbiAgICAgfVxyXG4gICAgIFxyXG4gICAgQElucHV0KCd0b29sdGlwRGljdGlvbmFyeUlkJykgc2V0IHRleHREaWN0aW9uYXJ5SWQodmFsdWUpIHtcclxuICAgICAgICBpZih2YWx1ZSkge1xyXG4gICAgICAgICAgICB0aGlzLl90b29sVGlwID0gU3d0VXRpbC5nZXRQcmVkaWN0TWVzc2FnZSh2YWx1ZSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgQElucHV0KClcclxuICAgIHNldCB0b29sVGlwUHJldmlvdXNWYWx1ZSh2YWx1ZSkge1xyXG4gICAgICAgIHRoaXMuX3Rvb2xUaXBQcmV2aW91c09iamVjdCA9IHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIGdldCB0b29sVGlwUHJldmlvdXNWYWx1ZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdG9vbFRpcFByZXZpb3VzT2JqZWN0O1xyXG4gICAgfVxyXG5cclxuXHJcbiAgICAvLyBJbnB1dCB0byBoYW5kbGUgZGF0ZVBpY2tlciBpZFxyXG4gICAgQElucHV0KFwiaWRcIikgaWQ7XHJcbiAgICAvKiBQcml2YXRlIGF0dHJpYnV0ZXMgKi9cclxuICAgIHByaXZhdGUgX3Rvb2xUaXA6IGFueT1cIlwiO1xyXG4gICAgcHJpdmF0ZSBmaXJzdENhbGw6IGJvb2xlYW4gPSB0cnVlO1xyXG4gICAgcHJpdmF0ZSBfdmlzaWJpbGl0eSA9IHRydWU7XHJcbiAgICBwcml2YXRlIGxvZ2dlcjogTG9nZ2VyID0gbnVsbDtcclxuICAgIHByaXZhdGUgX3Nob3dPbkZsYWcgPSBcImJ1dHRvblwiO1xyXG4gICAgcHJpdmF0ZSBkYXRlUGlja2VyT2JqZWN0OiBhbnk7XHJcbiAgICBwcml2YXRlIF9zaG93VG9EYXk6IGJvb2xlYW47XHJcbiAgICBwcml2YXRlIF9jaGFuZ2UgPSBuZXcgRnVuY3Rpb24oKTtcclxuICAgIC8vdmFyaWFibGUgdG8gaG9sZCBpbnRlcnJ1cHRDb21tc1xyXG4gICAgcHJpdmF0ZSBpbnRlcnJ1cHRlZDpib29sZWFuID0gZmFsc2U7XHJcblxyXG4gICAgLypIYW5kbGluZyBvdXRwdXQgZXZlbnRzICovXHJcbiAgICBAT3V0cHV0KFwib3BlblwiKSBwcml2YXRlIG9wZW5FdmVudE91dFB1dCA9IG5ldyBFdmVudEVtaXR0ZXI8RnVuY3Rpb24+KCk7XHJcbiAgICBAT3V0cHV0KFwiY2xvc2VcIikgcHVibGljIGNsb3NlRXZlbnRPdXRQdXQgPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgQE91dHB1dChcImtleURvd25cIikgcHJpdmF0ZSBrZXlEb3duRXZlbnRPdXRQdXQgPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgQE91dHB1dChcImNoYW5nZVwiKSBwcml2YXRlIGNoYW5nZUV2ZW50T3V0UHV0ID0gbmV3IEV2ZW50RW1pdHRlcjxGdW5jdGlvbj4oKTtcclxuICAgIEBPdXRwdXQoXCJmb2N1c1wiKSBwcml2YXRlIGZvY3VzRXZlbnRPdXRQdXQgPSBuZXcgRXZlbnRFbWl0dGVyPEZ1bmN0aW9uPigpO1xyXG4gICAgQE91dHB1dChcImZvY3VzT3V0XCIpIHByaXZhdGUgZm9jdXNPdXRFdmVudE91dFB1dCA9IG5ldyBFdmVudEVtaXR0ZXI8RnVuY3Rpb24+KCk7XHJcbiAgICBAT3V0cHV0KFwia2V5Rm9jdXNDaGFuZ2VcIikgcHJpdmF0ZSBrZXlGb2N1c0NoYW5nZSA9IG5ldyBFdmVudEVtaXR0ZXI8RnVuY3Rpb24+KCk7XHJcbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIGVsZW06IEVsZW1lbnRSZWYsIHByaXZhdGUgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSwgcHJpdmF0ZSBfcmVuZGVyZXI6IFJlbmRlcmVyMikge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyID0gbmV3IExvZ2dlcignU3d0RGF0ZWZpZWxkJywgY29tbW9uU2VydmljZS5odHRwY2xpZW50KTtcclxuICAgICAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhcIlsgU3d0RGF0ZWZpZWxkIF0gY29uc3RydWN0aW9uXCIpO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBfZW5hYmxlZCA9IHRydWU7XHJcbiAgICBnZXQgZW5hYmxlZCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZW5hYmxlZDtcclxuICAgIH1cclxuICAgIC8qIGVuYWJsZWQgZ2V0dGVyIGFuZCBzZXR0ZXIgKi9cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgZW5hYmxlZCh2YWx1ZTogYm9vbGVhbikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSA9PT0gXCJzdHJpbmdcIikge1xyXG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IFwiZmFsc2VcIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZW5hYmxlZCA9IGZhbHNlO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuX2VuYWJsZWQgPSB2YWx1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIGlmICh0aGlzLmRhdGVQaWNrZXJPYmplY3QgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRpc2FibGVkXCIsICF0aGlzLl9lbmFibGVkKTtcclxuICAgICAgICAgICAgJCgkKHRoaXMuZGF0ZVBpY2tlck9iamVjdClbMF0pLnZhbCh0aGlzLnRleHQpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHByaXZhdGUgX2VkaXRhYmxlID0gdHJ1ZTtcclxuICAgIGdldCBlZGl0YWJsZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZWRpdGFibGU7XHJcbiAgICB9XHJcbiAgICAvKiBlZGl0YWJsZSBnZXR0ZXIgYW5kIHNldHRlciAqL1xyXG4gICAgQElucHV0KClcclxuICAgIHNldCBlZGl0YWJsZShlZGl0YWJsZTogYm9vbGVhbikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgKGVkaXRhYmxlKSA9PT0gXCJzdHJpbmdcIikge1xyXG4gICAgICAgICAgICBpZiAoZWRpdGFibGUgPT09IFwiZmFsc2VcIikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fZWRpdGFibGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pLnByb3AoXCJyZWFkb25seVwiLCB0cnVlKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX2VkaXRhYmxlO1xyXG4gICAgICAgICAgICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSkucHJvcChcInJlYWRvbmx5XCIsIGZhbHNlKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuX2VkaXRhYmxlID0gZWRpdGFibGU7XHJcbiAgICAgICAgICAgICQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0pLnByb3AoXCJyZWFkb25seVwiLCAhQm9vbGVhbihlZGl0YWJsZSkpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHByaXZhdGUgX3dpZHRoOiBzdHJpbmc7XHJcbiAgICBnZXQgd2lkdGgoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3dpZHRoO1xyXG4gICAgfVxyXG4gICAgLyogd2lkdGggZ2V0dGVyIGFuZCBzZXR0ZXIgKi9cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgd2lkdGgodmFsdWU6IHN0cmluZykge1xyXG4gICAgICAgIGlmICh2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKCclJykgPT09IC0xKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl93aWR0aCA9IHZhbHVlICsgXCJweFwiO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fd2lkdGggPSB2YWx1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHByaXZhdGUgX3RleHQ6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBnZXQgdGV4dCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdGV4dDtcclxuICAgIH1cclxuICAgIC8qIHRleHQgZ2V0dGVyIGFuZCBzZXR0ZXIgKi9cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgdGV4dCh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQgfHwgdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcignc2V0RGF0ZScsIG51bGwpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fdGV4dCA9IG51bGw7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5wYXJzZURhdGUodmFsdWUsIHRoaXMuX2Zvcm1hdFN0cmluZykgIT09IG51bGwpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3RleHQgPSB2YWx1ZTtcclxuICAgICAgICAgICAgICAgIHZhciBxdWVyeURhdGUgPSB0aGlzLnBhcnNlRGF0ZSh2YWx1ZSwgdGhpcy5fZm9ybWF0U3RyaW5nKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5kYXRlcGlja2VyKCdzZXREYXRlJywgcXVlcnlEYXRlKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3RleHQgPSB2YWx1ZTtcclxuICAgICAgICAgICAgICAgICQoJCh0aGlzLmRhdGVQaWNrZXJPYmplY3QpWzBdKS52YWwodmFsdWUpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmICh0aGlzLmZpcnN0Q2FsbCkge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gdGhpcy5fdGV4dDtcclxuICAgICAgICAgICAgICAgIHRoaXMuZmlyc3RDYWxsID0gZmFsc2U7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICB0aGlzLmxvZ2dlci5lcnJvcignc2V0IHRleHQgbWV0aG9kICcsIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgc2V0IGludGVycnVwdENvbW1zKGludGVycnVwdGVkIDpib29sZWFuKSB7XHJcbiAgICAgICAgdGhpcy5pbnRlcnJ1cHRlZCA9IGludGVycnVwdGVkO1xyXG4gICAgfVxyXG5cclxuICAgIGdldCBpbnRlcnJ1cHRDb21tcygpIDpib29sZWFuIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5pbnRlcnJ1cHRlZDtcclxuICAgIH1cclxuXHJcblxyXG4gICAgcHJpdmF0ZSBfc2hvd1llYXJTZWxlY3QgPSB0cnVlO1xyXG4gICAgZ2V0IHNob3dZZWFyU2VsZWN0KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9zaG93WWVhclNlbGVjdDtcclxuICAgIH1cclxuICAgIC8qPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gR2V0dGVyIGFuZCBTZXR0ZXIgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSovXHJcbiAgICBzZXQgc2hvd1llYXJTZWxlY3QodmFsdWU6IGJvb2xlYW4pIHtcclxuICAgICAgICB0aGlzLl9zaG93WWVhclNlbGVjdCA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBfc2hvd01vbnRoU2VsZWN0ID0gZmFsc2U7XHJcbiAgICBnZXQgc2hvd01vbnRoU2VsZWN0KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9zaG93TW9udGhTZWxlY3Q7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBzaG93TW9udGhTZWxlY3QodmFsdWU6IGJvb2xlYW4pIHtcclxuICAgICAgICB0aGlzLl9zaG93TW9udGhTZWxlY3QgPSB2YWx1ZTtcclxuICAgIH1cclxuICAgIHByaXZhdGUgX3RleHRBbGlnbiA9IFwianVzdGlmeVwiO1xyXG4gICAgZ2V0IHRleHRBbGlnbigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdGV4dEFsaWduO1xyXG4gICAgfVxyXG4gICAgLyogQElucHV0cyAqL1xyXG4gICAgLyogdGV4dEFsaWduIGdldHRlciBhbmQgc2V0dGVyICovXHJcbiAgICBASW5wdXQoKVxyXG4gICAgc2V0IHRleHRBbGlnbihhbGlnbjogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5fdGV4dEFsaWduID0gYWxpZ247XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIF9mb3JtYXRTdHJpbmcgPSBcIm1tL2RkL3l5XCI7XHJcbiAgICBnZXQgZm9ybWF0U3RyaW5nKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9mb3JtYXRTdHJpbmc7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIG92ZXJyaWRlIGZ1bmN0aW9uLCB0aGlzIGZ1bmN0aW9uIHNldHMgZGF0ZSBmb3JtYXQgc3RyaW5nXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIHZhbHVlOiBTdHJpbmdcclxuICAgICAqL1xyXG4gICAgc2V0IGZvcm1hdFN0cmluZyh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdmFsdWUgPSB2YWx1ZS50b0xvd2VyQ2FzZSgpO1xyXG4gICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKFwieXl5eVwiKSAhPT0gLTEpIHtcclxuICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoXCJ5eXl5XCIsIFwieXlcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKFwibW1tXCIpICE9PSAtMSkge1xyXG4gICAgICAgICAgICB2YWx1ZSA9IHZhbHVlLnRvTG93ZXJDYXNlKCkucmVwbGFjZShcIm1tbVwiLCBcIk1cIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuX2Zvcm1hdFN0cmluZyA9IHZhbHVlO1xyXG4gICAgICAgIGlmICh0aGlzLmRhdGVQaWNrZXJPYmplY3QgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRhdGVGb3JtYXRcIiwgdGhpcy5fZm9ybWF0U3RyaW5nKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIF9tb250aE5hbWVzID0gW1wiSmFudWFyeVwiLCBcIkZlYnJ1YXJ5XCIsIFwiTWFyY2hcIiwgXCJBcHJpbFwiLCBcIk1heVwiLCBcIkp1bmVcIiwgXCJKdWx5XCIsIFwiQXVndXN0XCIsIFwiU2VwdGVtYmVyXCIsIFwiT2N0b2JlclwiLCBcIk5vdmVtYmVyXCIsIFwiRGVjZW1iZXJcIl07XHJcbiAgICBnZXQgbW9udGhOYW1lcygpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fbW9udGhOYW1lcztcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgc2V0IG1vbnRoTmFtZXModmFsdWU6IGFueSkge1xyXG4gICAgICAgIHRoaXMuX21vbnRoTmFtZXMgPSB2YWx1ZTtcclxuICAgICAgICBpZiAodGhpcy5kYXRlUGlja2VyT2JqZWN0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgLy8gc2V0IGRhdGVQaWNrZXIgbW9udGhOYW1lc1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1vbnRoTmFtZXNcIiwgdmFsdWUpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHByaXZhdGUgX2RheU5hbWVzID0gW1wiU1wiLCBcIk1cIiwgXCJUXCIsIFwiV1wiLCBcIlRcIiwgXCJGXCIsIFwiU1wiXTtcclxuICAgIGdldCBkYXlOYW1lcygpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZGF5TmFtZXM7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBkYXlOYW1lcyh2YWx1ZTogYW55KSB7XHJcbiAgICAgICAgdGhpcy5fZGF5TmFtZXMgPSB2YWx1ZTtcclxuICAgIH1cclxuICAgIHByaXZhdGUgX3NlbGVjdGFibGVSYW5nZTogT2JqZWN0O1xyXG4gICAgZ2V0IHNlbGVjdGFibGVSYW5nZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fc2VsZWN0YWJsZVJhbmdlO1xyXG4gICAgfVxyXG4gICAgQElucHV0KClcclxuICAgIHNldCBzZWxlY3RhYmxlUmFuZ2UodmFsdWU6IE9iamVjdCkge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGlmIChPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RoID09PSAxKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5kYXRlUGlja2VyT2JqZWN0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1pbkRhdGVcIiwgbnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmRhdGVwaWNrZXIoXCJvcHRpb25cIiwgXCJtYXhEYXRlXCIsIHZhbHVlW09iamVjdC5rZXlzKHZhbHVlKVswXV0pO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0YWJsZVJhbmdlID0gdmFsdWU7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoT2JqZWN0LmtleXModmFsdWUpLmxlbmd0aCA9PT0gMikge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgbWluRGF0ZSA9IHZhbHVlW09iamVjdC5rZXlzKHZhbHVlKVswXV07XHJcbiAgICAgICAgICAgICAgICBjb25zdCBtYXhEYXRlID0gdmFsdWVbT2JqZWN0LmtleXModmFsdWUpWzFdXTtcclxuICAgICAgICAgICAgICAgIGlmICh0aGlzLmRhdGVQaWNrZXJPYmplY3QgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5kYXRlcGlja2VyKFwib3B0aW9uXCIsIFwibWluRGF0ZVwiLCBtaW5EYXRlKTtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1heERhdGVcIiwgbWF4RGF0ZSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RhYmxlUmFuZ2UgPSB2YWx1ZTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcInNlbGVjdGFibGVSYW5nZSBtdXN0IGJlIGFuIG9iamVjdCB3aXRoIHRvdyBrZXlzIHJhbmdlU3RhcnQgYW5kIHJhbmdlU3RvcFwiKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoXCIgc2VsZWN0YWJsZVJhbmdlIG1ldGhvZCA6XCIsIGUpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIC8qIHByaXZhdGUgdmFyaWFibGUgdG8gaGFuZGxlIGV2ZW50cyAqL1xyXG4gICAgcHJpdmF0ZSBfb3BlbiA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgZ2V0IG9wZW4oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX29wZW47XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBvcGVuKGhhbmRsZXI6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fb3BlbiA9IGhhbmRsZXI7XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIF9jbG9zZSA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgZ2V0IGNsb3NlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9jbG9zZTtcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgc2V0IGNsb3NlKGhhbmRsZXI6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fY2xvc2UgPSBoYW5kbGVyO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBfa2V5RG93biA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgZ2V0IGtleURvd24oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX2tleURvd247XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBrZXlEb3duKGhhbmRsZXI6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fa2V5RG93biA9IGhhbmRsZXI7XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIF9mb2N1cyA9IG5ldyBGdW5jdGlvbigpO1xyXG4gICAgZ2V0IGZvY3VzKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9mb2N1cztcclxuICAgIH1cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgc2V0IGZvY3VzKGhhbmRsZXI6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fZm9jdXMgPSBoYW5kbGVyO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBfZm9jdXNPdXQgPSBuZXcgRnVuY3Rpb24oKTtcclxuICAgIGdldCBmb2N1c091dCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZm9jdXNPdXQ7XHJcbiAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBmb2N1c091dChoYW5kbGVyOiBGdW5jdGlvbikge1xyXG4gICAgICAgIHRoaXMuX2ZvY3VzT3V0ID0gaGFuZGxlcjtcclxuICAgICAgICAgICAgfVxyXG4gICAgcHJpdmF0ZSBfc2VsZWN0ZWREYXRlOiBEYXRlID0gbnVsbDtcclxuICAgIGdldCBzZWxlY3RlZERhdGUoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3NlbGVjdGVkRGF0ZTtcclxuICAgICAgICB9XHJcbiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cclxuICAgIHNldCBzZWxlY3RlZERhdGUoZGF0ZTogRGF0ZSkge1xyXG4gICAgICAgIGlmICh0aGlzLmRhdGVQaWNrZXJPYmplY3QgIT09IHVuZGVmaW5lZCAmJiBkYXRlICE9IG51bGwgKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkRGF0ZSA9IG5ldyBEYXRlKGRhdGUpO1xyXG4gICAgICAgICAgICB0aGlzLl90ZXh0ID0gdGhpcy5mb3JtYXREYXRlKGRhdGUpO1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcignc2V0RGF0ZScsIGRhdGUpO1xyXG4gICAgICAgIH1lbHNle1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZERhdGUgPSBudWxsO1xyXG4gICAgICAgICAgICB0aGlzLl90ZXh0ID0gXCJcIjtcclxuICAgICAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmRhdGVwaWNrZXIoJ3NldERhdGUnLCBudWxsKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBnZXQgc2hvd1RvZGF5KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9zaG93VG9EYXk7XHJcbiAgICB9XHJcbiAgICBASW5wdXQoKVxyXG4gICAgc2V0IHNob3dUb2RheSh2YWx1ZTogYm9vbGVhbikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlID09PSAndHJ1ZScpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3Nob3dUb0RheSA9IHRydWU7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9zaG93VG9EYXkgPSBmYWxzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLl9zaG93VG9EYXkgPSB2YWx1ZTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBnZXQgdmlzaWJsZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdmlzaWJpbGl0eTtcclxuICAgICAgICAgICAgfVxyXG4gICAgLyogaW5wdXQgdG8gaG9sZCBjb21wb25lbnQgdmlzaWJpbGl0eSAqL1xyXG4gICAgQElucHV0KCd2aXNpYmxlJylcclxuICAgIHNldCB2aXNpYmxlKHZhbHVlKSB7XHJcbiAgICAgICAgaWYgKHR5cGVvZiAodmFsdWUpICE9PSAnc3RyaW5nJykge1xyXG4gICAgICAgICAgICB0aGlzLl92aXNpYmlsaXR5ID0gdmFsdWU7XHJcbiAgICAgICAgICAgIHZhbHVlID8gJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuc2hvdygpIDogJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuaGlkZSgpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gJ3RydWUnKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuc2hvdygpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl92aXNpYmlsaXR5ID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCBvblNlbGVjdCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fY2hhbmdlO1xyXG4gICAgfVxyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBzZXQgb25TZWxlY3Qob25TZWxlY3Q6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fY2hhbmdlID0gb25TZWxlY3Q7XHJcbiAgICB9XHJcbiAgICBuZ09uSW5pdCgpIHtcclxuICAgICAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhcIlsgbmdPbkluaXQgXSBlbmV0ZXJcIik7XHJcbiAgICAgICAgLy8gSW5pdGlhbGl6ZSBkYXRlUGlrZXIgb2JqZWN0XHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0ID0gJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSk7XHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmRhdGVwaWNrZXIoe1xyXG4gICAgICAgICAgICBzaG93T246IFwiYnV0dG9uXCIsXHJcbiAgICAgICAgICAgIGJ1dHRvbkltYWdlOiBcImFzc2V0cy9pbWFnZXMvZGF0ZVBpY2tlci1pY29uLnBuZ1wiLFxyXG4gICAgICAgICAgICBidXR0b25JbWFnZU9ubHk6IHRydWUsXHJcbiAgICAgICAgICAgIGRhdGVGb3JtYXQ6IHRoaXMuX2Zvcm1hdFN0cmluZyxcclxuICAgICAgICAgICAgYnV0dG9uVGV4dDogXCJcIixcclxuICAgICAgICAgICAgY2hhbmdlTW9udGg6IHRoaXMuX3Nob3dNb250aFNlbGVjdCxcclxuICAgICAgICAgICAgY2hhbmdlWWVhcjogdGhpcy5fc2hvd1llYXJTZWxlY3QsXHJcbiAgICAgICAgICAgIGJlZm9yZVNob3c6IChyZXN1bHQpID0+IHtcclxuICAgICAgICAgICAgICAgIHRoaXMub3BlbkV2ZW50T3V0UHV0LmVtaXQocmVzdWx0KTtcclxuICAgICAgICAgICAgICAgIHRoaXMuX29wZW4ocmVzdWx0KTtcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25DbG9zZTogKHJlc3VsdCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jbG9zZUV2ZW50T3V0UHV0LmVtaXQocmVzdWx0KTtcclxuICAgICAgICAgICAgICAgIHRoaXMuX2Nsb3NlKHJlc3VsdCk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl90ZXh0ID0gcmVzdWx0O1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VFdmVudE91dFB1dC5lbWl0KHJlc3VsdCk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNweUNoYW5nZXModGhpcy5fdGV4dCk7XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG9uU2VsZWN0OiAocmVzdWx0KSA9PiB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9jaGFuZ2UocmVzdWx0KTtcclxuICAgICAgICAgICAgICAgIHRoaXMuX3RleHQgPSByZXN1bHQ7XHJcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZERhdGUgPSB0aGlzLnBhcnNlRGF0ZShyZXN1bHQsIHRoaXMuZm9ybWF0U3RyaW5nKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5vbigna2V5ZG93bicsIChyZXN1bHQpID0+IHtcclxuICAgICAgICAgICAgdGhpcy5rZXlEb3duRXZlbnRPdXRQdXQuZW1pdChyZXN1bHQpO1xyXG4gICAgICAgICAgICB0aGlzLl9rZXlEb3duKHJlc3VsdCk7XHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0Lm9uKCdrZXl1cCcsIChyZXN1bHQpID0+IHtcclxuICAgICAgICAgICAgdGhpcy51cGRhdGVEYXRlKHJlc3VsdCk7XHJcbiAgICAgICAgICAgIHRoaXMuc3B5Q2hhbmdlcyh0aGlzLl90ZXh0KTtcclxuICAgICAgICB9KTtcclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3Qub24oJ2ZvY3VzJywgKHJlc3VsdCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLmZvY3VzRXZlbnRPdXRQdXQuZW1pdChyZXN1bHQpO1xyXG4gICAgICAgICAgICB0aGlzLl9mb2N1cyhyZXN1bHQpO1xyXG4gICAgICAgICAgICB0aGlzLmtleUZvY3VzQ2hhbmdlLmVtaXQocmVzdWx0KTtcclxuICAgICAgICAgICAgLy8gdXBkYXRlIGZvY3VzIE1hbmFnZXIgZGF0YSAoZm9jdXNlZCBlbGVtZW50KVxyXG4gICAgICAgICAgICBmb2N1c01hbmFnZXIuZm9jdXNUYXJnZXQgPSB0aGlzLmlkO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3Qub24oJ2ZvY3Vzb3V0JywgKHJlc3VsdCkgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RlZERhdGUgPSB0aGlzLnBhcnNlRGF0ZSh0aGlzLnRleHQsIHRoaXMuZm9ybWF0U3RyaW5nKTtcclxuXHJcbiAgICAgICAgICAgIHRoaXMuZm9jdXNPdXRFdmVudE91dFB1dC5lbWl0KHJlc3VsdCk7XHJcbiAgICAgICAgICAgIHRoaXMuX2ZvY3VzT3V0KHJlc3VsdCk7XHJcbiAgICAgICAgICAgIHRoaXMua2V5Rm9jdXNDaGFuZ2UuZW1pdChyZXN1bHQpO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBzZXQgZGF0ZVBpa2VyIHdpZHRoXHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LndpZHRoKHRoaXMuX3dpZHRoKTtcclxuICAgICAgICAvLyBzZXQgZGF0ZVBpa2VyIHN0cmluZ0Zvcm1hdGVcclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRhdGVGb3JtYXRcIiwgdGhpcy5fZm9ybWF0U3RyaW5nKTtcclxuICAgICAgICAvLyBIYW5kbGluZyBlbmFibGluZyBwcm9wZXJ0aWVzXHJcbiAgICAgICAgaWYgKHRoaXMuZW5hYmxlZCA9PT0gdHJ1ZSkge1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRpc2FibGVkXCIsIGZhbHNlKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRpc2FibGVkXCIsIHRydWUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICAvLyBIYW5kbGluZyB2aXNpYmlsaXR5IHByb3BlcnRpZXMgIFxyXG4gICAgICAgIGlmICh0aGlzLnZpc2libGUgPT09IGZhbHNlKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5hZGRDbGFzcyhcImhpZGVcIik7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LnJlbW92ZUNsYXNzKFwiaGlkZVwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gSGFuZGxpbmcgZWRpdGFibGUgcHJvcGVydGllc1xyXG4gICAgICAgIGlmICh0aGlzLmVkaXRhYmxlID09PSB0cnVlKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5wcm9wKFwicmVhZG9ubHlcIiwgZmFsc2UpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5wcm9wKFwicmVhZG9ubHlcIiwgdHJ1ZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIEhhbmRsaW5nIHRleHQgYWxpZ24gXHJcbiAgICAgICAgaWYgKHRoaXMuX3RleHRBbGlnbiAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5jc3MoXCJ0ZXh0LWFsaWduXCIsIHRoaXMuX3RleHRBbGlnbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIHNldCBkYXRlUGlja2VyIG1vbnRoTmFtZXNcclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1vbnRoTmFtZXNcIiwgdGhpcy5fbW9udGhOYW1lcyk7XHJcbiAgICAgICAgLy8gc2V0IGRhdGVQaWNrZXIgZGF5TmFtZXNcclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcImRheU5hbWVzXCIsIHRoaXMuX2RheU5hbWVzKTtcclxuICAgICAgICAvLyBzZXQgdGV4dEFsaWduIHByb3BlcnR5XHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmNzcyhcInRleHQtYWxpZ25cIiwgdGhpcy5fdGV4dEFsaWduKTtcclxuICAgICAgICAvL3NldCBzZWxlY3RhYmxlIFJhbmdlXHJcbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0YWJsZVJhbmdlICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0YWJsZVJhbmdlKS5sZW5ndGggPT09IDEpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5kYXRlcGlja2VyKFwib3B0aW9uXCIsIFwibWluRGF0ZVwiLCBudWxsKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5kYXRlcGlja2VyKFwib3B0aW9uXCIsIFwibWF4RGF0ZVwiLCB0aGlzLnNlbGVjdGFibGVSYW5nZVtPYmplY3Qua2V5cyh0aGlzLnNlbGVjdGFibGVSYW5nZSlbMF1dKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChPYmplY3Qua2V5cyh0aGlzLnNlbGVjdGFibGVSYW5nZSkubGVuZ3RoID09PSAyKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1pbkRhdGVcIiwgdGhpcy5zZWxlY3RhYmxlUmFuZ2VbT2JqZWN0LmtleXModGhpcy5zZWxlY3RhYmxlUmFuZ2UpWzBdXSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcihcIm9wdGlvblwiLCBcIm1heERhdGVcIiwgdGhpcy5zZWxlY3RhYmxlUmFuZ2VbT2JqZWN0LmtleXModGhpcy5zZWxlY3RhYmxlUmFuZ2UpWzFdXSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRoaXMud2lkdGggJiYgdHlwZW9mKHRoaXMud2lkdGgpID09PSBcInN0cmluZ1wiICYmIHRoaXMud2lkdGguaW5kZXhPZihcIiVcIikgPT09IC0xKSB7XHJcbiAgICAgICAgICAkKHRoaXMuZGF0ZWZpZWxkLm5hdGl2ZUVsZW1lbnQpLndpZHRoKHRoaXMuX3dpZHRoKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLl9yZW5kZXJlci5zZXRTdHlsZSh0aGlzLmRhdGVmaWVsZC5uYXRpdmVFbGVtZW50LnBhcmVudEVsZW1lbnQsICd3aWR0aCcsIHRoaXMud2lkdGgpO1xyXG4gICAgICAgICAgICB0aGlzLl9yZW5kZXJlci5zZXRTdHlsZSh0aGlzLmRhdGVmaWVsZC5uYXRpdmVFbGVtZW50LCAnd2lkdGgnLCBcIjkwJVwiKTtcclxuICAgICAgICB9ICAgICAgICBcclxuICAgICAgICB0aGlzLmRhdGVQaWNrZXJPYmplY3QuZGF0ZXBpY2tlcignc2V0RGF0ZScsIHRoaXMuX3NlbGVjdGVkRGF0ZSk7XHJcbiAgICAgICAgLy8gQWRkZWQgYnkgUmloYWIuSiBAMDcvMTIvMjAxOCAtIG5lZWRlZCB0byBiZSB1c2VkIGluIGR5bmFtaWNhbGx5IGFkZGVkIFN3dERhdGVGaWVsZC5cclxuICAgICAgICAkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpWzBdKS5hdHRyKCdzZWxlY3RvcicsICdTd3REYXRlRmllbGQnKTtcclxuICAgICAgICAvLyBzZXQgaWQgdG8gU3d0RGF0ZUZpZWxkIERPTS5cclxuICAgICAgICBpZiggdGhpcy5pZCkge1xyXG4gICAgICAgICAgICQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoXCJpZFwiLCB0aGlzLmlkKTtcclxuICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAvLy1BZGQgVG9vbFRpcC5cclxuICAgICAgICBpZih0aGlzLl90b29sVGlwICYmICQodGhpcy5kYXRlZmllbGQubmF0aXZlRWxlbWVudCkubGVuZ3RoID4wKXtcclxuICAgICAgICAgICAgJCgkKHRoaXMuZGF0ZWZpZWxkLm5hdGl2ZUVsZW1lbnQpWzBdKS5hdHRyKFwidGl0bGVcIiwgdGhpcy5fdG9vbFRpcCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAkKCQodGhpcy5kYXRlZmllbGQubmF0aXZlRWxlbWVudClbMF0pLnRvb2x0aXAoe1xyXG4gICAgICAgICAgICBzaG93OiB7IGR1cmF0aW9uOiA4MDAsIGRlbGF5IDogNTAwIH0sXHJcbiAgICAgICAgICAgIG9wZW46IGZ1bmN0aW9uKCBldmVudCwgdWkgKSB7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMpLnJlbW92ZUF0dHIoJ3RpdGxlJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgdGhpcy5sb2dnZXIuZGVidWcoXCJbIG5nT25Jbml0IF0gZW5kLlwiKTtcclxuICAgIH1cclxuICAgIG5nQWZ0ZXJWaWV3SW5pdCgpOiB2b2lkIHtcclxuICAgICAgICB0aGlzLnNob3dUb2RheSA/IHRoaXMuc2VsZWN0ZWREYXRlID0gbmV3IERhdGUoKSA6IG51bGw7XHJcbiAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmRhdGVwaWNrZXIoXCJvcHRpb25cIiwgXCJjdXJyZW50VGV4dFwiLCBcIk5vd1wiKTtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBzZXQgdmlzaWJpbGl0eSBvZiB0aGUgY29tcG9uZW50LlxyXG4gICAgICogQHBhcmFtIHZpc2liaWxpdHlcclxuICAgICAqL1xyXG4gICAgcHVibGljIHNldFZpc2libGUodmlzaWJpbGl0eTogYm9vbGVhbikge1xyXG4gICAgICAgIHRoaXMuX3Zpc2liaWxpdHkgPSB2aXNpYmlsaXR5O1xyXG4gICAgICAgIHZpc2liaWxpdHkgPyAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5zaG93KCkgOiAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5oaWRlKCk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgZnVuY3Rpb24gaXMgdXNlZCB0byBwYXJzZSB0aGUgZ2l2ZW4gZGF0ZSBzdHJpbmcgdG8gZGF0ZVxyXG4gICAgICogVGhpcyBmdW5jdGlvbiBjb252ZXJ0cyB0aGUgZ2l2ZW4gZGF0ZSBzdHJpbmcgdG8gTU0vREQvWVlZWSBcclxuICAgICAqIGZvcm1hdCBhbmQgdGhlbiBwYXJzZSB0aGUgZGF0ZVxyXG4gICAgICogXHJcbiAgICAgKiBwYXJhbSBkYXRlU3RyaW5nOiBTdHJpbmdcclxuICAgICAqIHBhcmFtIGZvcm1hdFN0cmluZzogU3RyaW5nXHJcbiAgICAgKiBAcmV0dXJuIERhdGVcclxuICAgICAqL1xyXG4gICAgcHVibGljIHBhcnNlRGF0ZShkYXRlU3RyaW5nOiBzdHJpbmcsIGZvcm1hdFN0cmluZzogc3RyaW5nID0gbnVsbCk6IERhdGUge1xyXG4gICAgICAgIHZhciBkYXRlID0gbnVsbDtcclxuICAgICAgICBsZXQgZGF0ZVNwbGl0RnJhZ21lbnRzID0gbmV3IEFycmF5KCk7XHJcbiAgICAgICAgbGV0IGRlbGltczogc3RyaW5nID0gXCJcIjtcclxuICAgICAgICBmb3JtYXRTdHJpbmcgPT0gbnVsbCA/IGZvcm1hdFN0cmluZyA9IHRoaXMuZm9ybWF0U3RyaW5nIDogbnVsbDtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiggZGF0ZVN0cmluZyA9PSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoZGF0ZVN0cmluZy5sZW5ndGggIT0gMTAgfHwgKGRhdGVTdHJpbmcuaW5kZXhPZihcIi9cIikgPT09IC0xICYmIGRhdGVTdHJpbmcuaW5kZXhPZihcIi9cIikgPT09IC0xKSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKGRhdGVTdHJpbmcgIT09IG51bGwgJiYgZGF0ZVN0cmluZyAhPT0gJycgJiYgZGF0ZVN0cmluZyAhPT0gdW5kZWZpbmVkKSB7XHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKGZvcm1hdFN0cmluZyAhPT0gbnVsbCAmJiBmb3JtYXRTdHJpbmcgIT09IHVuZGVmaW5lZCAmJiBmb3JtYXRTdHJpbmcgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChmb3JtYXRTdHJpbmcudG9VcHBlckNhc2UoKS5pbmRleE9mKFwiRERcIikgPT09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZVNwbGl0RnJhZ21lbnRzID0gdGhpcy5zcGxpdGVyKGRhdGVTdHJpbmcpW1wiZGF0ZVNwbGl0RnJhZ21lbnRzXCJdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkZWxpbXMgPSB0aGlzLnNwbGl0ZXIoZGF0ZVN0cmluZylbXCJkZWxpbXNcIl07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChkYXRlU3BsaXRGcmFnbWVudHMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChkYXRlU3BsaXRGcmFnbWVudHNbMV0gPiAxMiB8fCBkYXRlU3BsaXRGcmFnbWVudHNbMV0gPCAxKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZSA9IG51bGw7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGVTcGxpdEZyYWdtZW50c1swXSA+IDMxIHx8IGRhdGVTcGxpdEZyYWdtZW50c1swXSA8IDEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlID0gbnVsbDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZSA9IG51bGw7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTcGxpdEZyYWdtZW50c1sxXSArIGRlbGltcyArIGRhdGVTcGxpdEZyYWdtZW50c1swXSArIGRlbGltcyArIGRhdGVTcGxpdEZyYWdtZW50c1syXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlID0gbnVsbDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGVTcGxpdEZyYWdtZW50cyA9IHRoaXMuc3BsaXRlcihkYXRlU3RyaW5nKVtcImRhdGVTcGxpdEZyYWdtZW50c1wiXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGVsaW1zID0gdGhpcy5zcGxpdGVyKGRhdGVTdHJpbmcpW1wiZGVsaW1zXCJdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoTnVtYmVyKGRhdGVTcGxpdEZyYWdtZW50c1swXSkgPiAxMiB8fCBOdW1iZXIoZGF0ZVNwbGl0RnJhZ21lbnRzWzFdKSA8IDEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUgPSBudWxsO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKE51bWJlcihkYXRlU3BsaXRGcmFnbWVudHNbMV0pID4gMzEgfHwgTnVtYmVyKGRhdGVTcGxpdEZyYWdtZW50c1swXSkgPCAxKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlID0gbnVsbDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRhdGUgPSBudWxsO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgICB0aGlzLmxvZ2dlci5lcnJvcihcInBhcnNlRGF0ZSBtZXRob2QgXCIsIGUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gZGF0ZTtcclxuICAgIH1cclxuICAgICAvKipcclxuICAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIHNldCBzdHlsZVxyXG4gICAgICAqIHRvIGNvbXBvbmVudFxyXG4gICAgICAqIEBwYXJhbSBwcm9lcnR5XHJcbiAgICAgICogQHBhcmFtIHZhbHVlXHJcbiAgICAgICovXHJcbiAgICAgc2V0U3R5bGUoYXR0cmlidXRlOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpIHtcclxuICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAkKHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jc3MoYXR0cmlidXRlLHZhbHVlKTtcclxuICAgICAgICAgfSwgMCk7XHJcbiAgICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIGZ1bmN0aW9uIGZvcm1hdHMgdGhlIHNlbGVjdGVkIGRhdGUgdG8gZGlzcGxheVxyXG4gICAgICogXHJcbiAgICAgKiBwYXJhbSB2YWx1ZTogRGF0ZVxyXG4gICAgICogQHJldHVybiBTdHJpbmdcclxuICAgICAqL1xyXG4gICAgcHVibGljIGZvcm1hdERhdGUodmFsdWU6IERhdGUpOiBzdHJpbmcge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmRlYnVnKFwiRW50ZXIgW2Zvcm1hdERhdGVdIGlkPVwiICsgdGhpcy5pZCArIFwiLCB2YWx1ZT1cIiArIHZhbHVlKTtcclxuICAgICAgICAvL0ZsYWcgdG8gaWRlbnRpZnkgd2hldGhlciB0aGUgZm9ybWF0IHN0cmluZyBjb250YWlucyBcIi1cIiBhcyBzZXBhcmF0b3JcclxuICAgICAgICB2YXIgZmxhZzogQm9vbGVhbiA9IGZhbHNlO1xyXG4gICAgICAgIGlmICh0aGlzLmZvcm1hdFN0cmluZy5pbmRleE9mKFwiLVwiKSA+IC0xKSB7XHJcbiAgICAgICAgICAgIC8vU2V0IGZvcm1hdCBzdHJpbmcuIFJlcGxhY2UgXCItXCIgd2l0aCBcIi9cIiBhcyBmbGV4IGRvZXMgbm90IHN1cHBvcnQgXCItXCIgYXMgYSBzZXBhcmF0b3JcclxuICAgICAgICAgICAgdGhpcy5fZm9ybWF0U3RyaW5nID0gdGhpcy5fZm9ybWF0U3RyaW5nLnJlcGxhY2UoLy0vZywgXCIvXCIpO1xyXG4gICAgICAgICAgICBmbGFnID0gdHJ1ZTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLl9mb3JtYXRTdHJpbmcgPSB0aGlzLmZvcm1hdFN0cmluZztcclxuICAgICAgICB9XHJcbiAgICAgICAgLy9Gb3JtYXQgdGhlIGRhdGVcclxuICAgICAgICB2YXIgZGF0ZTogc3RyaW5nID0gdGhpcy5nZXRBYmJyZXZpYXRpb25Gcm9tRGF0ZSh2YWx1ZSk7XHJcbiAgICAgICAgLy9Db252ZXJ0IHRoZSBkYXRlIHN0cmluZyB0byBpdHMgb3JpZ2luYWwgZm9ybWF0XHJcbiAgICAgICAgaWYgKGZsYWcpIHtcclxuICAgICAgICAgICAgZGF0ZSA9IGRhdGUucmVwbGFjZSgvXFwvL2csIFwiLVwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy9Gb3JtYXQgdGhlIGRhdGUgYW5kIHJldHVybnMgdGhlIHNhbWVcclxuICAgICAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhcIkV4aXQgW2Zvcm1hdERhdGVdIGlkPVwiICsgdGhpcy5pZCArIFwiLCBkYXRlPVwiICsgZGF0ZSk7XHJcbiAgICAgICAgcmV0dXJuIGRhdGU7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gc2hvdyBkcm9wRG93bi5cclxuICAgICAqL1xyXG4gICAgcHVibGljIG9wZW5Ecm9wRG93bigpIHtcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmRhdGVwaWNrZXIoXCJzaG93XCIpO1xyXG4gICAgICAgIH0sIDApO1xyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBnZXRBYmJyZXZpYXRpb25Gcm9tRGF0ZVxyXG4gICAgICpcclxuICAgICAqIFRoaXMgZnVudGlvbiBpcyB1c2VkIHRvIGdldCBhYnJldmlhdGlvbiBmcm9tIGRhdGUgd2hlbiBpdCBpcyBpbiBERC1NTU0tWVlZWSBmb3JtYXRcclxuICAgICAqL1xyXG4gICAgcHVibGljIGdldEFiYnJldmlhdGlvbkZyb21EYXRlKGRhdGU6IERhdGUpOiBzdHJpbmcge1xyXG4gICAgICAgIGxldCBmb3JtYXR0ZWREYXRlOiBzdHJpbmcgPSBcIlwiO1xyXG4gICAgICAgIGxldCBkYXk6IG51bWJlcjtcclxuICAgICAgICBsZXQgbW9udGg6IG51bWJlcjtcclxuICAgICAgICBsZXQgeWVhcjogbnVtYmVyO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGlmIChkYXRlKSB7XHJcbiAgICAgICAgICAgIGRheSA9IGRhdGUuZ2V0RGF0ZSgpO1xyXG4gICAgICAgICAgICBtb250aCA9IGRhdGUuZ2V0TW9udGgoKTtcclxuICAgICAgICAgICAgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTtcclxuICAgICAgICAgICAgaWYgKHRoaXMuZm9ybWF0U3RyaW5nLnRvVXBwZXJDYXNlKCkgPT09IFwiREQtTS1ZWVwiKSB7XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREYXRlID0gdGhpcy5sb2FkaW5nWmVybyhkYXkpICsgXCItXCIgKyB0aGlzLl9tb250aE5hbWVzW21vbnRoXSArIFwiLVwiICsgdGhpcy5sb2FkaW5nWmVybyh5ZWFyKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm1hdFN0cmluZy50b1VwcGVyQ2FzZSgpID09PSBcIk1NL0REL1lZXCIpIHtcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERhdGUgPSB0aGlzLmxvYWRpbmdaZXJvKG1vbnRoICsgMSkgKyBcIi9cIiArIHRoaXMubG9hZGluZ1plcm8oZGF5KSArIFwiL1wiICsgdGhpcy5sb2FkaW5nWmVybyh5ZWFyKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm1hdFN0cmluZy50b1VwcGVyQ2FzZSgpID09PSBcIkREL01NL1lZXCIpIHtcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERhdGUgPSB0aGlzLmxvYWRpbmdaZXJvKGRheSkgKyBcIi9cIiArIHRoaXMubG9hZGluZ1plcm8obW9udGggKyAxKSArIFwiL1wiICsgdGhpcy5sb2FkaW5nWmVybyh5ZWFyKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlNwZWNpZnkgdGhlIGRhdGVGaWVsZCBTdHJpbmcgRm9ybWF0IVwiKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgICB0aGlzLmxvZ2dlci5lcnJvcihcImdldEFiYnJldmlhdGlvbkZyb21EYXRlIG1ldGhvZCA6XCIsIGUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gZm9ybWF0dGVkRGF0ZTtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogUmV0dXJucyB0aGUgRGF0ZSBvYmplY3RcclxuICAgICAqL1xyXG4gICAgcHVibGljIGdldERhdGUoKTogRGF0ZSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuZGF0ZVBpY2tlck9iamVjdC5kYXRlcGlja2VyKFwiZ2V0RGF0ZVwiKTtcclxuICAgIH1cclxuICAgIHB1YmxpYyBzZXRGb2N1cygpIHtcclxuICAgICAgICBpZiAodGhpcy5kYXRlUGlja2VyT2JqZWN0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5kYXRlUGlja2VyT2JqZWN0LmZvY3VzKCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3B5Q2hhbmdlcyhldmVudDogYW55KTogdm9pZCB7XHJcbiAgICAgICAgaWYgKHRoaXMub3JpZ2luYWxWYWx1ZSA9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gXCJcIjtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRoaXMub3JpZ2luYWxWYWx1ZSA9PSBldmVudCkge1xyXG4gICAgICAgICAgICB0aGlzLm9uU3B5Tm9DaGFuZ2UuZW1pdCh7XCJ0YXJnZXRcIjogdGhpcywgXCJ2YWx1ZVwiOiBldmVudH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgdGhpcy5vblNweUNoYW5nZS5lbWl0KHtcInRhcmdldFwiOiB0aGlzLCBcInZhbHVlXCI6IGV2ZW50fSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIDtcclxuICAgIH1cclxuICAgIHJlc2V0T3JpZ2luYWxWYWx1ZSgpIHtcclxuICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWUgPSB0aGlzLl90ZXh0O1xyXG4gICAgICAgIHRoaXMuc3B5Q2hhbmdlcyh0aGlzLl90ZXh0KTtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byB1cGRhdGUgZGF0ZS5cclxuICAgICAqL1xyXG4gICAgcHJpdmF0ZSB1cGRhdGVEYXRlKHJlcykge1xyXG4gICAgICAgIHRoaXMuX3RleHQgPSAkKHRoaXMuZGF0ZWZpZWxkLm5hdGl2ZUVsZW1lbnQpLnZhbCgpO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBsb2FkaW5nWmVybyh2YWx1ZTogbnVtYmVyKSB7XHJcbiAgICAgICAgdmFyIHJ0bjogc3RyaW5nID0gXCJcIjtcclxuICAgICAgICBpZiAodmFsdWUgPj0gMTApIHtcclxuICAgICAgICAgICAgcnRuID0gdmFsdWUgKyBcIlwiO1xyXG4gICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPiAwICYmIHZhbHVlIDwgMTApIHtcclxuICAgICAgICAgICAgcnRuID0gXCIwXCIgKyB2YWx1ZTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBydG4gPSBcIjAwXCI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBydG47XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gZGV0ZWN0IGRhdGUgZGVsaW1pdGVyXHJcbiAgICAgKiBhbmQgc3BsaXQgdGhlIGRhdGVTdHJpbmcuXHJcbiAgICAgKiBAYXV0aG9yIENoaWhlYlxyXG4gICAgICogQHBhcmFtIGRhdGVTdHJpbmdcclxuICAgICAqL1xyXG4gICAgcHJpdmF0ZSBzcGxpdGVyKGRhdGVTdHJpbmc6IHN0cmluZyk6IGFueSB7XHJcbiAgICAgICAgY29uc3QgdGFiID0gbmV3IEFycmF5KCk7XHJcbiAgICAgICAgaWYgKGRhdGVTdHJpbmcuaW5kZXhPZihcIi1cIikgIT09IC0xKSB7XHJcbiAgICAgICAgICAgIHRhYltcImRhdGVTcGxpdEZyYWdtZW50c1wiXSA9IGRhdGVTdHJpbmcuc3BsaXQoXCItXCIpO1xyXG4gICAgICAgICAgICB0YWJbXCJkZWxpbXNcIl0gPSBcIi1cIjtcclxuICAgICAgICB9IGVsc2UgaWYgKGRhdGVTdHJpbmcuaW5kZXhPZihcIi9cIikgIT09IC0xKSB7XHJcbiAgICAgICAgICAgIHRhYltcImRhdGVTcGxpdEZyYWdtZW50c1wiXSA9IGRhdGVTdHJpbmcuc3BsaXQoXCIvXCIpO1xyXG4gICAgICAgICAgICB0YWJbXCJkZWxpbXNcIl0gPSBcIi9cIjtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyB0aHJvdyBuZXcgRXJyb3IoXCJwYXJzZURhdGUoZGF0ZVN0cmluZywgW0VSUk9SXSAtPiBmb3JtYXRTdHJpbmcpIGZvcm1hdFN0cmluZyBvbmx5ICctJyBhbmQgJy8nIGFyZSBhY2NlcHRlZCBhcyBkZWxlbWV0ZXJcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiB0YWI7XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIGlzVmFsaWREYXRlKHZhbHVlOiBzdHJpbmcpIHtcclxuICAgICAgICByZXR1cm4gdmFsdWUuaW5kZXhPZihcIkludmFsaWQgRGF0ZVwiKSA9PT0gLTE7XHJcbiAgICB9XHJcbn1cclxuIl19