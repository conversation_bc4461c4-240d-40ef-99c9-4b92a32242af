/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export const ComboFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let data = columnDef.params.selectDataSource;
    /** @type {?} */
    let defaultValue = '';
    /** @type {?} */
    let enabledFlag;
    if (columnDef.params.enableDisableCells) {
        enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    }
    else {
        enabledFlag = true;
    }
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let text;
    //- if dataSource is an object
    if (!(data instanceof Array)) {
        /** @type {?} */
        var array = [];
        array[0] = data;
        data = array;
    }
    for (let index = 0; index < data.length; index++) {
        if (data[index].value == value) {
            defaultValue = data[index].content ? data[index].content : "";
            break;
        }
    }
    if (defaultValue == '' && value != undefined && value != '') {
        defaultValue = value;
    }
    if (showHideCells) {
        text = `
                <div   class="renderAsInput selectDiv js-example-basic-single ${(enabledFlag == false || !columnDef.params.grid.enabled) ? 'disabled-combo' : 'select2'}" >
                    <span class="renderAsInput" style="width:80%; text-overflow: ellipsis!important;
                                overflow: hidden!important;
                                white-space: nowrap!important;
                                display: inline-block!important; padding-left:2px;">${defaultValue}</span>
                </div>
            `;
    }
    else {
        text = ``;
    }
    return text;
});
//# sourceMappingURL=data:application/json;base64,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