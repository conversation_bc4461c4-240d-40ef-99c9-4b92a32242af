/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { AdvancedExportEvent } from './../events/swt-events.module';
import { Component, Input, ViewChild, ElementRef } from '@angular/core';
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
const select2 = require('select2');
import 'jquery-ui-dist/jquery-ui';
import { SwtComboBox } from './swt-combobox.component';
import { ExportEvent } from '../events/swt-events.module';
import { CommonLogic } from '../logic/common-logic';
import { ExternalInterface } from '../utils/external-interface.service';
import { SwtModule } from './swt-module.component';
import { CommonService } from '../utils/common.service';
import { SwtAlert } from '../utils/swt-alert.service';
/** @type {?} */
var convert = require('xml-js');
export class SwtDataExport extends SwtModule {
    /**
     * @param {?} commonService
     * @param {?} element
     */
    constructor(commonService, element) {
        super(element, commonService);
        this.commonService = commonService;
        this.element = element;
        this.dp = [
            { type: "", value: 'PDF', selected: 0, content: "PDF", iconImage: "assets/images/pdfUp.jpg" },
            { type: "", value: 'Excel', selected: 1, content: "Excel", iconImage: "assets/images/excelUp.jpg" },
            { type: "", value: 'CSV', selected: 0, content: "CSV", iconImage: "assets/images/csvUp.jpg" }
        ];
        this.dpDisabled = [
            { type: "", value: 'PDF', selected: 0, content: "", iconImage: "assets/images/pdfDisabled.jpg" },
            { type: "", value: 'Excel', selected: 1, content: "", iconImage: "assets/images/excelDisabled.jpg" },
            { type: "", value: 'CSV', selected: 0, content: "", iconImage: "assets/images/csvDisabled.jpg" }
        ];
        this.accountStartPattern = /<account clickable="false">/g;
        this.accountStartWithoutAttributesPattern = /<account>/g;
        this.nameStartPattern = /<name clickable="false">/g;
        this.nameStartWithoutAttributesPattern = /<name>/g;
        this.accountEndPattern = /<\/account>/g;
        this.nameEndPattern = /<\/name>/g;
        this.interfaceStartPattern = /<interface clickable="false">/g;
        this.interfaceStartWithoutAttributesPattern = /<interface>/g;
        this.interfaceEndPattern = /<\/interface>/g;
        // variable to hold the assumption start pattern
        this.assumptionStartPattern = /<assumption>/g;
        // variable to hold the assumption end pattern  
        this.assumptionEndPattern = /<\/assumption>/g;
        // private variable to handle enabled status.
        this._enabled = true;
        this.elementReference = element;
        this.commonServiceRef = commonService;
        this.SwtAlert = new SwtAlert(this.commonService);
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onDataExportClick(event) {
        ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        /** @type {?} */
        let eventToSend = { type: this.typeFromIndex(this.exportDataComponent.selectedIndex), id: this.id };
        AdvancedExportEvent.emit(eventToSend);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onDataExportChange(event) {
        ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        /** @type {?} */
        let eventToSend = { type: this.typeFromIndex(this.exportDataComponent.selectedIndex), id: this.id };
        AdvancedExportEvent.emit(eventToSend);
    }
    // enabled getter and setter 
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                value = false;
            }
            else {
                value = true;
            }
        }
        this.exportDataComponent.enabled = value;
        if (value == false) {
            this.exportDataComponent.dataProvider = this.dpDisabled;
        }
        else {
            this.exportDataComponent.dataProvider = this.dp;
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        if (!(this.exportDataComponent.dataProvider.length == 3))
            this.exportDataComponent.dataProvider = this.dp;
    }
    /**
     * @param {?} index
     * @return {?}
     */
    typeFromIndex(index) {
        /** @type {?} */
        var type = "";
        if (index == 0)
            type = "pdf";
        else if (index == 1)
            type = "excel";
        else if (index == 2)
            type = "csv";
        return type;
    }
    /**
     * @param {?} mainObj
     * @return {?}
     */
    deepCopy(mainObj) {
        /** @type {?} */
        const objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        let key;
        for (key in mainObj) {
            objCopy[key] = mainObj[key]; // copies each property to the objCopy object
        }
        return objCopy;
    }
    /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    convertArrayToXML(josnData, objectName, parentName, attributeList) {
        /** @type {?} */
        let xmlData = parentName ? "<" + parentName + ">" : "";
        try {
            /** @type {?} */
            var options = { compact: true, ignoreComment: true, spaces: 4 };
            for (let i = 0; i < josnData.length; i++) {
                delete josnData[i].id;
                delete josnData[i].content;
                delete josnData[i].contains;
                delete josnData[i].remove;
                if (attributeList) {
                    josnData[i]._attributes = {};
                    for (let index = 0; index < attributeList.length; index++) {
                        /** @type {?} */
                        const element = attributeList[index];
                        josnData[i]._attributes[element] = josnData[i][element];
                        delete josnData[i][element];
                    }
                    xmlData += convert.js2xml({ [objectName]: (/** @type {?} */ (josnData[i])) }, options);
                }
                else {
                    if (josnData[i].slickgrid_rowcontent) {
                        for (var tagName in josnData[i]) {
                            if (josnData[i].slickgrid_rowcontent[tagName]) {
                                /** @type {?} */
                                let slickgridRowContent = josnData[i].slickgrid_rowcontent[tagName];
                                delete slickgridRowContent.contains;
                                delete slickgridRowContent.remove;
                                delete slickgridRowContent.content;
                                /** @type {?} */
                                var valueText = josnData[i][tagName];
                                delete josnData[i][tagName];
                                josnData[i][tagName] = new Object;
                                josnData[i][tagName]['_text'] = valueText;
                                josnData[i][tagName]._attributes = {};
                                for (let name in josnData[i]) {
                                    if (name.includes("asNum")) {
                                        delete josnData[i][name];
                                    }
                                }
                                for (var attributeName in slickgridRowContent) {
                                    if (attributeName != 'content' && attributeName != 'contains' && attributeName != 'remove') {
                                        josnData[i][tagName]._attributes[attributeName] = slickgridRowContent[attributeName];
                                    }
                                }
                            }
                        }
                    }
                    delete josnData[i].id;
                    delete josnData[i].content;
                    delete josnData[i].remove;
                    delete josnData[i].contains;
                    delete josnData[i].slickgrid_rowcontent;
                    xmlData += "<" + objectName + ">" + convert.js2xml(josnData[i], options) + "</" + objectName + ">";
                }
            }
            xmlData += parentName ? "</" + parentName + ">" : "";
        }
        catch (e) {
            console.log("e", e);
        }
        return xmlData;
    }
    /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    convertArraysToXML(josnData, objectName, parentName, attributeList) {
        /** @type {?} */
        let xmlData = parentName ? "<" + parentName + ">" : "";
        try {
            /** @type {?} */
            var options = { compact: true, ignoreComment: true, spaces: 4 };
            delete josnData[0][1];
            josnData[0].length = 1;
            for (let j = 0; j < josnData.length; j++) {
                for (let i = 0; i < josnData[j].length; i++) {
                    delete josnData[j][i].id;
                    delete josnData[j][i].content;
                    delete josnData[j][i].contains;
                    delete josnData[j][i].remove;
                    if (attributeList) {
                        if ((josnData[j][i]._attributes == undefined))
                            josnData[j][i]._attributes = {};
                        if (objectName.split(",")[j] == "group") {
                            if (josnData[j][i].column.length) {
                                for (let k = 0; k < josnData[j][i].column.length; k++)
                                    josnData[j][i].column[k]._attributes = {}; //josnData[j][i].column ;
                            }
                            else
                                josnData[j][i].column._attributes = {};
                        }
                        for (let index = 0; index < attributeList.length; index++) {
                            /** @type {?} */
                            const element = attributeList[index];
                            //if(objectName.split(",")[j] =="group" )
                            if (josnData[j][i][element] || objectName.split(",")[j] == "group") {
                                josnData[j][i]._attributes[element] = josnData[j][i][element];
                                if (objectName.split(",")[j] == "group") {
                                    if (josnData[j][i].column.length) {
                                        for (let k = 0; k < josnData[j][i].column.length; k++) {
                                            if (element == "heading") {
                                                josnData[j][i].column[k]._attributes[element] = josnData[j][i]["heading"] + '-' + josnData[j][i].column[k][element]; //josnData[j][i].column ;
                                            }
                                            else
                                                josnData[j][i].column[k]._attributes[element] = josnData[j][i].column[k][element]; //josnData[j][i].column ;
                                            delete josnData[j][i].column[k][element];
                                        }
                                    }
                                    else {
                                        if (element == "heading") {
                                            josnData[j][i].column._attributes[element] = josnData[j][i]["heading"] + '-' + josnData[j][i].column[element];
                                        }
                                        else
                                            josnData[j][i].column._attributes[element] = josnData[j][i].column[element];
                                        delete josnData[j][i].column[element];
                                    }
                                }
                                if (josnData[j][i][element])
                                    delete josnData[j][i][element];
                                //if(josnData[j][i].column)
                            }
                        }
                        xmlData += convert.js2xml({ [objectName.split(",")[j]]: (/** @type {?} */ (josnData[j][i])) }, options);
                    }
                    else {
                        if (josnData[j][i].slickgrid_rowcontent) {
                            for (var tagName in josnData[i]) {
                                if (josnData[j][i].slickgrid_rowcontent[tagName]) {
                                    /** @type {?} */
                                    let slickgridRowContent = josnData[j][i].slickgrid_rowcontent[tagName];
                                    delete slickgridRowContent.contains;
                                    delete slickgridRowContent.remove;
                                    delete slickgridRowContent.content;
                                    /** @type {?} */
                                    var valueText = josnData[j][i][tagName];
                                    delete josnData[j][i][tagName];
                                    josnData[j][i][tagName] = new Object;
                                    josnData[j][i][tagName]['_text'] = valueText;
                                    josnData[j][i][tagName]._attributes = {};
                                    for (var attributeName in slickgridRowContent) {
                                        if (attributeName != 'content' && attributeName != 'contains' && attributeName != 'remove') {
                                            josnData[j][i][tagName]._attributes[attributeName] = slickgridRowContent[attributeName];
                                        }
                                    }
                                }
                            }
                        }
                        delete josnData[j][i].id;
                        delete josnData[j][i].content;
                        delete josnData[j][i].remove;
                        delete josnData[j][i].contains;
                        delete josnData[j][i].slickgrid_rowcontent;
                        xmlData += "<" + objectName.split(",")[j] + ">" + convert.js2xml(josnData[j][i], options) + "</" + objectName.split(",")[j] + ">";
                    }
                }
            }
            xmlData += parentName ? "</" + parentName + ">" : "";
        }
        catch (e) {
            console.log("e", e);
        }
        return xmlData;
    }
    /**
     *  This method is used to convert the xmlData to String and handle the special character
     *
     * @param {?} cMetaData :XMLList
     * @param {?} cGrid :CustomGrid
     * @param {?} tData :XMLList
     * @param {?} selects :Array
     * @param {?=} type :String
     * @param {?=} isTotalGrid
     * @param {?=} forceAllData
     * @return {?}
     */
    convertData(cMetaData, cGrid, tData, selects, type = "pdf", isTotalGrid = false, forceAllData = false) {
        /** @type {?} */
        var str = "<data>";
        /** @type {?} */
        var totals = "";
        /** @type {?} */
        var columnMetaData = JSON.parse(JSON.stringify(cMetaData.column));
        /** @type {?} */
        var columnMetaDataGroup = "";
        /** @type {?} */
        var columnMetaDataGroupAsString = "";
        if (JSON.stringify(cMetaData.group)) {
            columnMetaDataGroup = JSON.parse(JSON.stringify(cMetaData.group));
            /** @type {?} */
            let columnMetaDataGroupAsArray = Array.of(columnMetaDataGroup);
            if (columnMetaDataGroupAsArray[0].length > 1) {
                columnMetaDataGroupAsArray = columnMetaDataGroupAsArray[0];
            }
            columnMetaDataGroupAsString = this.convertArraysToXML([columnMetaData, columnMetaDataGroupAsArray], "column,group", "columns", ['collapsable', 'headerTooltip', 'heading', 'filterable', 'dataelement', 'draggable', 'width', 'type', 'holiday', 'resizable', 'headerColor', 'clickable']);
        }
        /** @type {?} */
        let tempData = null;
        if (forceAllData) {
            tempData = $.extend(true, [], cGrid.dataset);
        }
        else {
            tempData = $.extend(true, [], cGrid.getFilteredItems());
        }
        /** @type {?} */
        var rowData = this.convertArrayToXML(tempData, "row", "rows");
        /** @type {?} */
        var columnMetaDataAsString = this.convertArrayToXML(columnMetaData, "column", "columns", ['filterable', 'dataelement', 'draggable', 'heading', 'width', 'type', 'holiday', 'visible', 'clickable', 'sort', 'resizable', 'headerTooltip', 'editable', 'columntype']);
        if (columnMetaDataGroup != "") {
            str += CommonLogic.removeLineBreaks(columnMetaDataGroupAsString);
        }
        else {
            str += CommonLogic.removeLineBreaks(columnMetaDataAsString);
        }
        str += CommonLogic.removeLineBreaks(rowData);
        if (str.search(this.accountStartPattern) > -1 || str.search(this.accountStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.accountStartPattern, '<account clickable="false"><![CDATA[');
            str = str.replace(this.accountStartWithoutAttributesPattern, '<account><![CDATA[');
            str = str.replace(this.accountEndPattern, "\]\]></account>");
        }
        if (str.search(this.nameStartPattern) > -1 || str.search(this.nameStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.nameStartPattern, '<name clickable="false"><![CDATA[');
            str = str.replace(this.nameStartWithoutAttributesPattern, '<name><![CDATA[');
            str = str.replace(this.nameEndPattern, "\]\]></name>");
        }
        if (str.search(this.interfaceStartPattern) > -1 || str.search(this.interfaceStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.interfaceStartPattern, '<interface clickable="false"><![CDATA[');
            str = str.replace(this.interfaceStartWithoutAttributesPattern, '<interface><![CDATA[');
            str = str.replace(this.interfaceEndPattern, "\]\]></interface>");
        }
        // Start: Code modified by Bala on 09-Sep-2011 for Issue found in 1053 Beta testing - Assumption report not opened when special character present in Assumption  
        if (str.search(this.assumptionStartPattern) > -1) {
            str = str.replace(this.assumptionStartPattern, '<assumption><![CDATA[');
            str = str.replace(this.assumptionEndPattern, "\]\]></assumption>");
        }
        // End: Code modified by Bala on 09-Sep-2011 for Issue found in 1053 Beta testing - Assumption report not opened when special character present in Assumption  
        /*str = str.split("\\").join("BACKSLASH_REPLACE");
        str = str.split("'").join("\\\\'");
        str = str.split("&amp;").join("\\&");
        str = str.split("%").join("PERCENTAGE_REPLACE");
        str = str.split("&lt;").join("\\<");
        str = str.split("&gt;").join("\\>");
        str = str.split("+").join("PLUSSYMBOL_REPLACE");*/
        if (tData != null && isTotalGrid) {
            /** @type {?} */
            var totalData = this.convertArrayToXML($.extend(true, [], tData), "total");
            totals = CommonLogic.removeLineBreaks(totalData);
        }
        //totals = "<total>" + totals + "</total>";
        str += totals;
        /** @type {?} */
        var filters = "<filters>";
        if (selects != null) {
            for (var k = 0; k < selects.length; k++) {
                filters += "<filter id=\"" + selects[k].split("=")[0] + "\">" + selects[k].split("=")[1] + "</filter>";
            }
        }
        if (cGrid.isFiltered) {
            // console.log("cGrid.getFilteredItems()=",cGrid.getFilteredItems());
            // for (var i: number = 0; i < cGrid.getCurrentFilter().length; i++) {
            /** @type {?} */
            var filterVal = "";
            /*for (var j in 10) {
                if(j != 'content' && j != 'contains' && j != 'remove'){
                    filters += "<filter id=\"Column Filter " + j + "\"><![CDATA[" + filterVal[j] + "\]\]></filter>";

                }
                // Special Characters included in selected filter.
                // filters = filters.split("\\").join("BACKSLASH_REPLACE");
                // filters = filters.split("'").join("\\\\'");
                // filters = filters.split("&amp;").join("\\&");
                // filters = filters.split("%").join("PERCENTAGE_REPLACE");
                // filters = filters.split("&lt;").join("\\<");
                // filters = filters.split("&gt;").join("\\>");
                // filters = filters.split("+").join("PLUSSYMBOL_REPLACE");
            }*/
            // }
        }
        filters += "</filters>";
        str += filters;
        str += "</data>";
        // ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').action=\'flexdataexport.do?method=' + type + '\';');
        // ExternalInterface.call('eval', 'document.getElementById(\'exportData\').value=\'' + str + '\';');
        // ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').submit();');
        ExternalInterface.call('exportData', type, str);
    }
}
SwtDataExport.decorators = [
    { type: Component, args: [{
                selector: 'DataExport',
                template: `
  <div>
			<SwtComboBox id="exportDataComponent" (inputClick)="onDataExportClick($event)" 
			(change)="onDataExportChange($event)" editable="false" #exportDataComponent width="43"></SwtComboBox>
  </div>
  `,
                styles: [`
  `]
            }] }
];
/** @nocollapse */
SwtDataExport.ctorParameters = () => [
    { type: CommonService },
    { type: ElementRef }
];
SwtDataExport.propDecorators = {
    exportDataComponent: [{ type: ViewChild, args: ['exportDataComponent',] }],
    enabled: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtDataExport.prototype.exportDataComponent;
    /** @type {?} */
    SwtDataExport.prototype.dp;
    /** @type {?} */
    SwtDataExport.prototype.dpDisabled;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.assumptionStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.assumptionEndPattern;
    /**
     * @type {?}
     * @protected
     */
    SwtDataExport.prototype.elementReference;
    /**
     * @type {?}
     * @protected
     */
    SwtDataExport.prototype.commonServiceRef;
    /** @type {?} */
    SwtDataExport.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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