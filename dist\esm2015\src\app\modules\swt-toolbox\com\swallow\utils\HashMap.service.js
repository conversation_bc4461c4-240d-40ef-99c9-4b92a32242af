/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
/**
 * HashMap is used to store data as key value format
 * <AUTHOR>
 **/
export class HashMap {
    //HashMap constructor.
    constructor() {
        // initialize keys
        this.keys = new Array();
        // initialize values.
        this.values = new Array();
    }
    /**
     * This method is used to check if HashMap contain given key.
     * @param {?} key
     * @return {?}
     */
    containsKey(key) {
        return (this.findKey(key) > -1);
    }
    /**
     * This method is used to check if HashMap contain given value.
     * @param {?} value
     * @return {?}
     */
    containsValue(value) {
        return (this.values.indexOf(value) > -1);
    }
    /**
     * This method is used to return HashMap keys.
     * @return {?}
     */
    getKeys() {
        return (this.keys.slice());
    }
    /**
     * This method is used to return HashMap values.
     * @return {?}
     */
    getValues() {
        return (this.values.slice());
    }
    /**
     * This method is used to return value of given key.
     * @param {?} key
     * @return {?}
     */
    getValue(key) {
        return (this.values[this.keys.indexOf(key)] ? this.values[this.keys.indexOf(key)] : null);
    }
    /**
     * This method is used to get value as object.
     * @param {?} key
     * @return {?}
     */
    getValueAsObject(key) {
        return (this.values[this.keys.indexOf(key)]);
    }
    /**
     * This method is used to get key of given value.
     * @param {?} value
     * @return {?}
     */
    getKey(value) {
        return (this.keys[this.values.indexOf(value)]);
    }
    /**
     * This method is used to add new entry to HashMap.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    put(key, value) {
        /** @type {?} */
        var oldKey;
        /** @type {?} */
        var theKey = this.keys.indexOf(key);
        if (theKey < 0) {
            this.keys.push(key);
            this.values.push(value !== undefined && value !== '' && value !== null ? value : '');
        }
        else {
            this.values[theKey] = value !== value !== undefined && value !== '' && value !== null ? value : '';
        }
    }
    /**
     * This method is used to add more then one entry to HashMap.
     * @param {?} map
     * @return {?}
     */
    putAll(map) {
        for (var key in map) {
            if (map.hasOwnProperty(key)) {
                this.keys.push(key);
                this.values.push(map[key]);
            }
        }
    }
    /**
     * This method is used to clear the HashMap.
     * @return {?}
     */
    clear() {
        this.keys = new Array();
        this.values = new Array();
    }
    /**
     * This method is used to remove HashMap entry with given key.
     * @param {?} key
     * @return {?}
     */
    remove(key) {
        /** @type {?} */
        var theKey = this.keys.indexOf(key);
        if (theKey > -1) {
            /** @type {?} */
            var theValue = this.values[theKey];
            this.values.splice(theKey, 1);
            this.keys.splice(theKey, 1);
        }
    }
    /**
     * This method is used to get HashMap size.
     * @return {?}
     */
    size() {
        return (this.keys.length);
    }
    /**
     * This method is used to check if HashMap is empty or not.
     * @return {?}
     */
    isEmpty() {
        return (this.size() < 1);
    }
    /**
     * This method is used to find given key.
     * @param {?} key
     * @return {?}
     */
    findKey(key) {
        return (this.keys.indexOf(key));
    }
    /**
     * This method is used to find given value.
     * @param {?} value
     * @return {?}
     */
    findValue(value) {
        return (this.values.indexOf(value));
    }
}
HashMap.decorators = [
    { type: Injectable }
];
/** @nocollapse */
HashMap.ctorParameters = () => [];
if (false) {
    /**
     * @type {?}
     * @private
     */
    HashMap.prototype.keys;
    /**
     * @type {?}
     * @private
     */
    HashMap.prototype.values;
}
//# sourceMappingURL=data:application/json;base64,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