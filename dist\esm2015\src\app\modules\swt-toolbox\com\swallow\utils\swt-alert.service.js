/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { Alert } from "./alert.component";
import { CommonService } from "./common.service";
import { Logger } from '../logging/logger.service';
export class SwtAlert extends Alert {
    /**
     * @param {?} commonService
     */
    constructor(commonService) {
        super(commonService);
        this.commonService = commonService;
        this.alertErrorImage = "assets/images//error.png";
        this.alertInfoImage = "assets/images/info.png";
        this.alertQuestionImage = "assets/images/question-mark.png";
        this.alertWarningImage = "assets/images/warning.png";
        this.alert = new Alert(commonService);
        this.logger = new Logger("SwtAlert", commonService.httpclient);
    }
    /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    info(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ info ] method START");
        try {
            if (title == null) {
                title = this.infoMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertInfoImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ info ] method - error ", error);
        }
        this.logger.info("[ info ] method END");
    }
    /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    question(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ info ] method START");
        try {
            if (title == null) {
                title = this.infoMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertQuestionImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ info ] method - error ", error);
        }
        this.logger.info("[ info ] method END");
    }
    /**
     * error
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the error Alert.
     */
    error(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ error ] method START");
        try {
            if (title == null) {
                title = this.errorMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertErrorImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ error ] method - error ", error);
        }
        this.logger.info("[ error ] method END");
    }
    /**
     * warning
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    warning(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ warning ] method START");
        try {
            if (title == null) {
                title = this.warningMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertWarningImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ warning ] method - error ", error);
        }
        this.logger.info("[ warning ] method END");
    }
    /**
     * confirm
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    confirm(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ confirm ] method START");
        try {
            if (title == null) {
                title = this.confirmMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertWarningImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ confirm ] method - error ", error);
        }
        this.logger.info("[ confirm ] method END");
    }
    /**
     * invalid
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     *  This function is used to return the invalid Alert.
     */
    invalid(text = "", title = null, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        this.logger.info("[ invalid ] method START");
        try {
            if (title == null) {
                title = this.invalidMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertErrorImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ invalid ] method - error ", error);
        }
        this.logger.info("[ invalid ] method END");
    }
}
SwtAlert.decorators = [
    { type: Injectable }
];
/** @nocollapse */
SwtAlert.ctorParameters = () => [
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alert;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertErrorImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertInfoImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertQuestionImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertWarningImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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