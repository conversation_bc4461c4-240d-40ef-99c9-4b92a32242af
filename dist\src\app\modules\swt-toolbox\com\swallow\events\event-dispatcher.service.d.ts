import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Logger } from "../../../com/swallow/logging/logger.service";
import { CommonService } from "../../../com/swallow/utils/common.service";
import { BaseObject } from "../model/base-object";
/**
 * <AUTHOR>
 * @date 28/05/2019
 * Language Version:    TypeScript 3.2
 * The EventDispatcher service is the base class for all classes that dispatch events.
 * The EventDispatcher service is the base class for all other classes.
 * The EventDispatcher service allows any object on the subclasses to be an event target and as such, to use the methods of the IEventDispatcher interface.
 * Event targets are an important part of the SwtToolBox event model.
 * The event target serves as the focal point for how events flow through the subclasses hierarchy.
 * When an event such as a mouse click or a keypress occurs, SwtToolbox dispatches an event object into the event flow from the root of the subclasses.
 * The event object then makes its way through the subclasses until it reaches the event target, at which point it begins its return trip through the subclasses.
 * This round-trip journey to the event target is conceptually divided into three phases:
 * the capture phase comprises the journey from the root to the last node before the event target's node, the target phase comprises only the event target node,
 * and the bubbling phase comprises any subsequent nodes encountered on the return trip to the root of the subclasses.
 * In general, the easiest way for a user-defined class to gain event dispatching capabilities is to extend EventDispatcher.
 * If this is impossible (that is, if the class is already extending another class), you can instead implement the IEventDispatcher interface,
 * create an EventDispatcher member, and write simple hooks to route calls into the aggregated EventDispatcher.
 */
export declare class EventDispatcher extends BaseObject implements IEventDispatcher, OnDestroy {
    private target;
    private __targetService;
    protected log: Logger;
    protected eventlist: any[];
    constructor(target: any, __targetService: CommonService);
    /**
     * Registers an event listener object with an EventDispatcher object so
     * that the listener receives notification of an event.
     * @param type
     * @param listener
     * @param useCapture
     * @param priority
     * @param useWeakReference
     */
    addEventListener(type: string, listener: Function, useCapture?: boolean, priority?: number, useWeakReference?: boolean, target?: any): void;
    /**
     * Dispatches an event into the event flow.
     * @param event
     */
    dispatchEvent(event: any): void;
    /**
     * Checks whether the EventDispatcher object has any listeners registered for a specific type of event.
     * @param type
     */
    hasEventListener(type: string): void;
    /**
     * Removes a listener from the EventDispatcher object.
     * @param type
     * @param listener
     * @param useCapture
     */
    removeEventListener(type: string, listener?: Function, useCapture?: boolean): void;
    /**
     * Checks whether an event listener is registered with this EventDispatcher
     * object or any of its ancestors for the specified event type.
     * @param type
     */
    willTrigger(type: string): void;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
export interface IEventDispatcher {
    addEventListener: Function;
    dispatchEvent: Function;
    hasEventListener: Function;
    removeEventListener: Function;
    willTrigger: Function;
}
