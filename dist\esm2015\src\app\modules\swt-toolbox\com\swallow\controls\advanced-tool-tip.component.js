/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
const $ = require('jquery');
export class AdvancedToolTip extends Container {
    /**
     * @param {?} tipelement
     * @param {?} tipcommon
     */
    constructor(tipelement, tipcommon) {
        super(tipelement, tipcommon);
        this.tipelement = tipelement;
        this.tipcommon = tipcommon;
        this._top = 0;
        this._left = 0;
        this._destroyTimeOut = 20000;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        setTimeout((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.destroy();
        }), this.timeOut);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set top(value) {
        $(this.tipelement.nativeElement.children[0]).css("top", value);
        this._top = value;
    }
    /**
     * @return {?}
     */
    get top() {
        return this._top;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set left(value) {
        $(this.tipelement.nativeElement.children[0]).css("left", value);
        this._left = value;
    }
    /**
     * @return {?}
     */
    get left() {
        return this._left;
    }
    /**
     * @return {?}
     */
    destroy() {
        $(this.tipelement.nativeElement.children[0]).hide();
    }
    /**
     * @return {?}
     */
    display() {
        //console.log("this.tipelement.nativeElement.children[0] = ", this.tipelement.nativeElement.children[0]);
        $(this.tipelement.nativeElement.children[0]).show();
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set timeOut(value) {
        this._destroyTimeOut = value;
    }
    /**
     * @return {?}
     */
    get timeOut() {
        return this._destroyTimeOut;
    }
    /**
     * @return {?}
     */
    getChild() {
        return this.child;
    }
}
AdvancedToolTip.decorators = [
    { type: Component, args: [{
                selector: 'AdvancedToolTip',
                template: `
        <div class="advanced-tooltip">
            <ng-container #container></ng-container>
        </div>
    `,
                animations: [],
                styles: [`
            .advanced-tooltip {
                width: auto;
                height: auto;
                position: fixed;
                background-color: transparent;
                display: none;
            }
    `]
            }] }
];
/** @nocollapse */
AdvancedToolTip.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._top;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._left;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype._destroyTimeOut;
    /** @type {?} */
    AdvancedToolTip.prototype.child;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype.tipelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedToolTip.prototype.tipcommon;
}
//# sourceMappingURL=data:application/json;base64,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