/**
 * @license Angular v7.2.16
 * (c) 2010-2019 Google LLC. https://angular.io/
 * License: MIT
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@angular/animations"),require("@angular/core")):"function"==typeof define&&define.amd?define("@angular/animations/browser",["exports","@angular/animations","@angular/core"],e):e(((t=t||self).ng=t.ng||{},t.ng.animations=t.ng.animations||{},t.ng.animations.browser={}),t.ng.animations,t.ng.core)}(this,function(t,e,n){"use strict";var r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function i(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return(o=Object.assign||function t(e){for(var n,r=1,i=arguments.length;r<i;r++)for(var o in n=arguments[r])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)};function s(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s}function a(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(s(arguments[e]));return t}function u(){return"undefined"!=typeof process}function l(t){switch(t.length){case 0:return new e.NoopAnimationPlayer;case 1:return t[0];default:return new e.ɵAnimationGroupPlayer(t)}}function h(t,n,r,i,o,s){void 0===o&&(o={}),void 0===s&&(s={});var a=[],u=[],l=-1,h=null;if(i.forEach(function(t){var r=t.offset,i=r==l,c=i&&h||{};Object.keys(t).forEach(function(r){var i=r,u=t[r];if("offset"!==r)switch(i=n.normalizePropertyName(i,a),u){case e.ɵPRE_STYLE:u=o[r];break;case e.AUTO_STYLE:u=s[r];break;default:u=n.normalizeStyleValue(r,i,u,a)}c[i]=u}),i||u.push(c),h=c,l=r}),a.length)throw new Error("Unable to animate due to the following errors:\n - "+a.join("\n - "));return u}function c(t,e,n,r){switch(e){case"start":t.onStart(function(){return r(n&&p(n,"start",t))});break;case"done":t.onDone(function(){return r(n&&p(n,"done",t))});break;case"destroy":t.onDestroy(function(){return r(n&&p(n,"destroy",t))})}}function p(t,e,n){var r=n.totalTime,i=f(t.element,t.triggerName,t.fromState,t.toState,e||t.phaseName,void 0==r?t.totalTime:r,!!n.disabled),o=t._data;return null!=o&&(i._data=o),i}function f(t,e,n,r,i,o,s){return void 0===i&&(i=""),void 0===o&&(o=0),{element:t,triggerName:e,fromState:n,toState:r,phaseName:i,totalTime:o,disabled:!!s}}function y(t,e,n){var r;return t instanceof Map?(r=t.get(e))||t.set(e,r=n):(r=t[e])||(r=t[e]=n),r}function m(t){var e=t.indexOf(":");return[t.substring(1,e),t.substr(e+1)]}var d=function(t,e){return!1},v=function(t,e){return!1},g=function(t,e,n){return[]},_=u();if(_||"undefined"!=typeof Element){if(d=function(t,e){return t.contains(e)},_||Element.prototype.matches)v=function(t,e){return t.matches(e)};else{var S=Element.prototype,E=S.matchesSelector||S.mozMatchesSelector||S.msMatchesSelector||S.oMatchesSelector||S.webkitMatchesSelector;E&&(v=function(t,e){return E.apply(t,[e])})}g=function(t,e,n){var r=[];if(n)r.push.apply(r,a(t.querySelectorAll(e)));else{var i=t.querySelector(e);i&&r.push(i)}return r}}var b=null,T=!1;function w(t){b||(b=function e(){return"undefined"!=typeof document?document.body:null}()||{},T=!!b.style&&"WebkitAppearance"in b.style);var n=!0;return b.style&&!function r(t){return"ebkit"==t.substring(1,6)}(t)&&!(n=t in b.style)&&T&&(n="Webkit"+t.charAt(0).toUpperCase()+t.substr(1)in b.style),n}var P=v,k=d,A=g;function N(t){var e={};return Object.keys(t).forEach(function(n){var r=n.replace(/([a-z])([A-Z])/g,"$1-$2");e[r]=t[n]}),e}var O=function(){function t(){}return t.prototype.validateStyleProperty=function(t){return w(t)},t.prototype.matchesElement=function(t,e){return P(t,e)},t.prototype.containsElement=function(t,e){return k(t,e)},t.prototype.query=function(t,e,n){return A(t,e,n)},t.prototype.computeStyle=function(t,e,n){return n||""},t.prototype.animate=function(t,n,r,i,o,s,a){return void 0===s&&(s=[]),new e.NoopAnimationPlayer(r,i)},function r(t,e,n,i){var o,s=arguments.length,a=s<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,i);else for(var u=t.length-1;u>=0;u--)(o=t[u])&&(a=(s<3?o(a):s>3?o(e,n,a):o(e,n))||a);return s>3&&a&&Object.defineProperty(e,n,a),a}([n.Injectable()],t)}(),C=function(){function t(){}return t.NOOP=new O,t}(),F=1e3;function D(t){if("number"==typeof t)return t;var e=t.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:L(parseFloat(e[1]),e[2])}function L(t,e){switch(e){case"s":return t*F;default:return t}}function j(t,e,n){return t.hasOwnProperty("duration")?t:function r(t,e,n){var r,i=0,o="";if("string"==typeof t){var s=t.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===s)return e.push('The provided timing value "'+t+'" is invalid.'),{duration:0,delay:0,easing:""};r=L(parseFloat(s[1]),s[2]);var a=s[3];null!=a&&(i=L(parseFloat(a),s[4]));var u=s[5];u&&(o=u)}else r=t;if(!n){var l=!1,h=e.length;r<0&&(e.push("Duration values below 0 are not allowed for this animation step."),l=!0),i<0&&(e.push("Delay values below 0 are not allowed for this animation step."),l=!0),l&&e.splice(h,0,'The provided timing value "'+t+'" is invalid.')}return{duration:r,delay:i,easing:o}}(t,e,n)}function x(t,e){return void 0===e&&(e={}),Object.keys(t).forEach(function(n){e[n]=t[n]}),e}function q(t){var e={};return Array.isArray(t)?t.forEach(function(t){return R(t,!1,e)}):R(t,!1,e),e}function R(t,e,n){if(void 0===n&&(n={}),e)for(var r in t)n[r]=t[r];else x(t,n);return n}function I(t,e,n){return n?e+":"+n+";":""}function K(t){for(var e="",n=0;n<t.style.length;n++)e+=I(0,r=t.style.item(n),t.style.getPropertyValue(r));for(var r in t.style)t.style.hasOwnProperty(r)&&!r.startsWith("_")&&(e+=I(0,r.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),t.style[r]));t.setAttribute("style",e)}function Q(t,e,n){t.style&&(Object.keys(e).forEach(function(r){var i=$(r);n&&!n.hasOwnProperty(r)&&(n[r]=t.style[i]),t.style[i]=e[r]}),u()&&K(t))}function B(t,e){t.style&&(Object.keys(e).forEach(function(e){var n=$(e);t.style[n]=""}),u()&&K(t))}function M(t){return Array.isArray(t)?1==t.length?t[0]:e.sequence(t):t}var z=new RegExp("{{\\s*(.+?)\\s*}}","g");function U(t){var e=[];if("string"==typeof t){for(var n=t.toString(),r=void 0;r=z.exec(n);)e.push(r[1]);z.lastIndex=0}return e}function W(t,e,n){var r=t.toString(),i=r.replace(z,function(t,r){var i=e[r];return e.hasOwnProperty(r)||(n.push("Please provide a value for the animation param "+r),i=""),i.toString()});return i==r?t:i}function Y(t){for(var e=[],n=t.next();!n.done;)e.push(n.value),n=t.next();return e}var H=/-+([a-z0-9])/g;function $(t){return t.replace(H,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t[1].toUpperCase()})}function V(t,e){return 0===t||0===e}function G(t,e,n){var r=Object.keys(n);if(r.length&&e.length){var i=e[0],o=[];if(r.forEach(function(t){i.hasOwnProperty(t)||o.push(t),i[t]=n[t]}),o.length)for(var s=function(){var n=e[a];o.forEach(function(e){n[e]=J(t,e)})},a=1;a<e.length;a++)s()}return e}function Z(t,e,n){switch(e.type){case 7:return t.visitTrigger(e,n);case 0:return t.visitState(e,n);case 1:return t.visitTransition(e,n);case 2:return t.visitSequence(e,n);case 3:return t.visitGroup(e,n);case 4:return t.visitAnimate(e,n);case 5:return t.visitKeyframes(e,n);case 6:return t.visitStyle(e,n);case 8:return t.visitReference(e,n);case 9:return t.visitAnimateChild(e,n);case 10:return t.visitAnimateRef(e,n);case 11:return t.visitQuery(e,n);case 12:return t.visitStagger(e,n);default:throw new Error("Unable to resolve animation metadata node #"+e.type)}}function J(t,e){return window.getComputedStyle(t)[e]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var X="*",tt=new Set(["true","1"]),et=new Set(["false","0"]);function nt(t,e){var n=tt.has(t)||et.has(t),r=tt.has(e)||et.has(e);return function(i,o){var s=t==X||t==i,a=e==X||e==o;return!s&&n&&"boolean"==typeof i&&(s=i?tt.has(t):et.has(t)),!a&&r&&"boolean"==typeof o&&(a=o?tt.has(e):et.has(e)),s&&a}}var rt=new RegExp("s*:selfs*,?","g");function it(t,e,n){return new ot(t).build(e,n)}var ot=function(){function t(t){this._driver=t}return t.prototype.build=function(t,e){var n=new st(e);return this._resetContextStyleTimingState(n),Z(this,M(t),n)},t.prototype._resetContextStyleTimingState=function(t){t.currentQuerySelector="",t.collectedStyles={},t.collectedStyles[""]={},t.currentTime=0},t.prototype.visitTrigger=function(t,e){var n=this,r=e.queryCount=0,i=e.depCount=0,o=[],s=[];return"@"==t.name.charAt(0)&&e.errors.push("animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))"),t.definitions.forEach(function(t){if(n._resetContextStyleTimingState(e),0==t.type){var a=t,u=a.name;u.toString().split(/\s*,\s*/).forEach(function(t){a.name=t,o.push(n.visitState(a,e))}),a.name=u}else if(1==t.type){var l=n.visitTransition(t,e);r+=l.queryCount,i+=l.depCount,s.push(l)}else e.errors.push("only state() and transition() definitions can sit inside of a trigger()")}),{type:7,name:t.name,states:o,transitions:s,queryCount:r,depCount:i,options:null}},t.prototype.visitState=function(t,e){var n=this.visitStyle(t.styles,e),r=t.options&&t.options.params||null;if(n.containsDynamicStyles){var i=new Set,o=r||{};if(n.styles.forEach(function(t){if(at(t)){var e=t;Object.keys(e).forEach(function(t){U(e[t]).forEach(function(t){o.hasOwnProperty(t)||i.add(t)})})}}),i.size){var s=Y(i.values());e.errors.push('state("'+t.name+'", ...) must define default values for all the following style substitutions: '+s.join(", "))}}return{type:0,name:t.name,style:n,options:r?{params:r}:null}},t.prototype.visitTransition=function(t,e){e.queryCount=0,e.depCount=0;var n=Z(this,M(t.animation),e);return{type:1,matchers:function r(t,e){var n=[];return"string"==typeof t?t.split(/\s*,\s*/).forEach(function(t){return function r(t,e,n){if(":"==t[0]){var r=function i(t,e){switch(t){case":enter":return"void => *";case":leave":return"* => void";case":increment":return function(t,e){return parseFloat(e)>parseFloat(t)};case":decrement":return function(t,e){return parseFloat(e)<parseFloat(t)};default:return e.push('The transition alias value "'+t+'" is not supported'),"* => *"}}(t,n);if("function"==typeof r)return void e.push(r);t=r}var o=t.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==o||o.length<4)return n.push('The provided transition expression "'+t+'" is not supported'),e;var s=o[1],a=o[2],u=o[3];e.push(nt(s,u)),"<"!=a[0]||s==X&&u==X||e.push(nt(u,s))}(t,n,e)}):n.push(t),n}(t.expr,e.errors),animation:n,queryCount:e.queryCount,depCount:e.depCount,options:ut(t.options)}},t.prototype.visitSequence=function(t,e){var n=this;return{type:2,steps:t.steps.map(function(t){return Z(n,t,e)}),options:ut(t.options)}},t.prototype.visitGroup=function(t,e){var n=this,r=e.currentTime,i=0,o=t.steps.map(function(t){e.currentTime=r;var o=Z(n,t,e);return i=Math.max(i,e.currentTime),o});return e.currentTime=i,{type:3,steps:o,options:ut(t.options)}},t.prototype.visitAnimate=function(t,n){var r,i=function o(t,e){var n=null;if(t.hasOwnProperty("duration"))n=t;else if("number"==typeof t)return lt(j(t,e).duration,0,"");var r=t;if(r.split(/\s+/).some(function(t){return"{"==t.charAt(0)&&"{"==t.charAt(1)})){var i=lt(0,0,"");return i.dynamic=!0,i.strValue=r,i}return lt((n=n||j(r,e)).duration,n.delay,n.easing)}(t.timings,n.errors);n.currentAnimateTimings=i;var s=t.styles?t.styles:e.style({});if(5==s.type)r=this.visitKeyframes(s,n);else{var a=t.styles,u=!1;if(!a){u=!0;var l={};i.easing&&(l.easing=i.easing),a=e.style(l)}n.currentTime+=i.duration+i.delay;var h=this.visitStyle(a,n);h.isEmptyStep=u,r=h}return n.currentAnimateTimings=null,{type:4,timings:i,style:r,options:null}},t.prototype.visitStyle=function(t,e){var n=this._makeStyleAst(t,e);return this._validateStyleAst(n,e),n},t.prototype._makeStyleAst=function(t,n){var r=[];Array.isArray(t.styles)?t.styles.forEach(function(t){"string"==typeof t?t==e.AUTO_STYLE?r.push(t):n.errors.push("The provided style string value "+t+" is not allowed."):r.push(t)}):r.push(t.styles);var i=!1,o=null;return r.forEach(function(t){if(at(t)){var e=t,n=e.easing;if(n&&(o=n,delete e.easing),!i)for(var r in e)if(e[r].toString().indexOf("{{")>=0){i=!0;break}}}),{type:6,styles:r,easing:o,offset:t.offset,containsDynamicStyles:i,options:null}},t.prototype._validateStyleAst=function(t,e){var n=this,r=e.currentAnimateTimings,i=e.currentTime,o=e.currentTime;r&&o>0&&(o-=r.duration+r.delay),t.styles.forEach(function(t){"string"!=typeof t&&Object.keys(t).forEach(function(r){if(n._driver.validateStyleProperty(r)){var s=e.collectedStyles[e.currentQuerySelector],a=s[r],u=!0;a&&(o!=i&&o>=a.startTime&&i<=a.endTime&&(e.errors.push('The CSS property "'+r+'" that exists between the times of "'+a.startTime+'ms" and "'+a.endTime+'ms" is also being animated in a parallel animation between the times of "'+o+'ms" and "'+i+'ms"'),u=!1),o=a.startTime),u&&(s[r]={startTime:o,endTime:i}),e.options&&function l(t,e,n){var r=e.params||{},i=U(t);i.length&&i.forEach(function(t){r.hasOwnProperty(t)||n.push("Unable to resolve the local animation param "+t+" in the given list of values")})}(t[r],e.options,e.errors)}else e.errors.push('The provided animation property "'+r+'" is not a supported CSS property for animations')})})},t.prototype.visitKeyframes=function(t,e){var n=this,r={type:5,styles:[],options:null};if(!e.currentAnimateTimings)return e.errors.push("keyframes() must be placed inside of a call to animate()"),r;var i=0,o=[],s=!1,a=!1,u=0,l=t.steps.map(function(t){var r=n._makeStyleAst(t,e),l=null!=r.offset?r.offset:function h(t){if("string"==typeof t)return null;var e=null;if(Array.isArray(t))t.forEach(function(t){if(at(t)&&t.hasOwnProperty("offset")){var n=t;e=parseFloat(n.offset),delete n.offset}});else if(at(t)&&t.hasOwnProperty("offset")){var n=t;e=parseFloat(n.offset),delete n.offset}return e}(r.styles),c=0;return null!=l&&(i++,c=r.offset=l),a=a||c<0||c>1,s=s||c<u,u=c,o.push(c),r});a&&e.errors.push("Please ensure that all keyframe offsets are between 0 and 1"),s&&e.errors.push("Please ensure that all keyframe offsets are in order");var h=t.steps.length,c=0;i>0&&i<h?e.errors.push("Not all style() steps within the declared keyframes() contain offsets"):0==i&&(c=1/(h-1));var p=h-1,f=e.currentTime,y=e.currentAnimateTimings,m=y.duration;return l.forEach(function(t,i){var s=c>0?i==p?1:c*i:o[i],a=s*m;e.currentTime=f+y.delay+a,y.duration=a,n._validateStyleAst(t,e),t.offset=s,r.styles.push(t)}),r},t.prototype.visitReference=function(t,e){return{type:8,animation:Z(this,M(t.animation),e),options:ut(t.options)}},t.prototype.visitAnimateChild=function(t,e){return e.depCount++,{type:9,options:ut(t.options)}},t.prototype.visitAnimateRef=function(t,e){return{type:10,animation:this.visitReference(t.animation,e),options:ut(t.options)}},t.prototype.visitQuery=function(t,e){var n=e.currentQuerySelector,r=t.options||{};e.queryCount++,e.currentQuery=t;var i=s(function o(t){var e=!!t.split(/\s*,\s*/).find(function(t){return":self"==t});return e&&(t=t.replace(rt,"")),[t=t.replace(/@\*/g,".ng-trigger").replace(/@\w+/g,function(t){return".ng-trigger-"+t.substr(1)}).replace(/:animating/g,".ng-animating"),e]}(t.selector),2),a=i[0],u=i[1];e.currentQuerySelector=n.length?n+" "+a:a,y(e.collectedStyles,e.currentQuerySelector,{});var l=Z(this,M(t.animation),e);return e.currentQuery=null,e.currentQuerySelector=n,{type:11,selector:a,limit:r.limit||0,optional:!!r.optional,includeSelf:u,animation:l,originalSelector:t.selector,options:ut(t.options)}},t.prototype.visitStagger=function(t,e){e.currentQuery||e.errors.push("stagger() can only be used inside of query()");var n="full"===t.timings?{duration:0,delay:0,easing:"full"}:j(t.timings,e.errors,!0);return{type:12,animation:Z(this,M(t.animation),e),timings:n,options:null}},t}(),st=function st(t){this.errors=t,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles={},this.options=null};function at(t){return!Array.isArray(t)&&"object"==typeof t}function ut(t){return t?(t=x(t)).params&&(t.params=function e(t){return t?x(t):null}(t.params)):t={},t}function lt(t,e,n){return{duration:t,delay:e,easing:n}}function ht(t,e,n,r,i,o,s,a){return void 0===s&&(s=null),void 0===a&&(a=!1),{type:1,element:t,keyframes:e,preStyleProps:n,postStyleProps:r,duration:i,delay:o,totalTime:i+o,easing:s,subTimeline:a}}var ct=function(){function t(){this._map=new Map}return t.prototype.consume=function(t){var e=this._map.get(t);return e?this._map.delete(t):e=[],e},t.prototype.append=function(t,e){var n=this._map.get(t);n||this._map.set(t,n=[]),n.push.apply(n,a(e))},t.prototype.has=function(t){return this._map.has(t)},t.prototype.clear=function(){this._map.clear()},t}(),pt=new RegExp(":enter","g"),ft=new RegExp(":leave","g");function yt(t,e,n,r,i,o,s,a,u,l){return void 0===o&&(o={}),void 0===s&&(s={}),void 0===l&&(l=[]),(new mt).buildKeyframes(t,e,n,r,i,o,s,a,u,l)}var mt=function(){function t(){}return t.prototype.buildKeyframes=function(t,e,n,r,i,o,s,a,u,l){void 0===l&&(l=[]),u=u||new ct;var h=new vt(t,e,u,r,i,l,[]);h.options=a,h.currentTimeline.setStyles([o],null,h.errors,a),Z(this,n,h);var c=h.timelines.filter(function(t){return t.containsAnimation()});if(c.length&&Object.keys(s).length){var p=c[c.length-1];p.allowOnlyTimelineStyles()||p.setStyles([s],null,h.errors,a)}return c.length?c.map(function(t){return t.buildKeyframes()}):[ht(e,[],[],[],0,0,"",!1)]},t.prototype.visitTrigger=function(t,e){},t.prototype.visitState=function(t,e){},t.prototype.visitTransition=function(t,e){},t.prototype.visitAnimateChild=function(t,e){var n=e.subInstructions.consume(e.element);if(n){var r=e.createSubContext(t.options),i=e.currentTimeline.currentTime,o=this._visitSubInstructions(n,r,r.options);i!=o&&e.transformIntoNewTimeline(o)}e.previousNode=t},t.prototype.visitAnimateRef=function(t,e){var n=e.createSubContext(t.options);n.transformIntoNewTimeline(),this.visitReference(t.animation,n),e.transformIntoNewTimeline(n.currentTimeline.currentTime),e.previousNode=t},t.prototype._visitSubInstructions=function(t,e,n){var r=e.currentTimeline.currentTime,i=null!=n.duration?D(n.duration):null,o=null!=n.delay?D(n.delay):null;return 0!==i&&t.forEach(function(t){var n=e.appendInstructionToTimeline(t,i,o);r=Math.max(r,n.duration+n.delay)}),r},t.prototype.visitReference=function(t,e){e.updateOptions(t.options,!0),Z(this,t.animation,e),e.previousNode=t},t.prototype.visitSequence=function(t,e){var n=this,r=e.subContextCount,i=e,o=t.options;if(o&&(o.params||o.delay)&&((i=e.createSubContext(o)).transformIntoNewTimeline(),null!=o.delay)){6==i.previousNode.type&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=dt);var s=D(o.delay);i.delayNextStep(s)}t.steps.length&&(t.steps.forEach(function(t){return Z(n,t,i)}),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>r&&i.transformIntoNewTimeline()),e.previousNode=t},t.prototype.visitGroup=function(t,e){var n=this,r=[],i=e.currentTimeline.currentTime,o=t.options&&t.options.delay?D(t.options.delay):0;t.steps.forEach(function(s){var a=e.createSubContext(t.options);o&&a.delayNextStep(o),Z(n,s,a),i=Math.max(i,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(function(t){return e.currentTimeline.mergeTimelineCollectedStyles(t)}),e.transformIntoNewTimeline(i),e.previousNode=t},t.prototype._visitTiming=function(t,e){if(t.dynamic){var n=t.strValue;return j(e.params?W(n,e.params,e.errors):n,e.errors)}return{duration:t.duration,delay:t.delay,easing:t.easing}},t.prototype.visitAnimate=function(t,e){var n=e.currentAnimateTimings=this._visitTiming(t.timings,e),r=e.currentTimeline;n.delay&&(e.incrementTime(n.delay),r.snapshotCurrentStyles());var i=t.style;5==i.type?this.visitKeyframes(i,e):(e.incrementTime(n.duration),this.visitStyle(i,e),r.applyStylesToKeyframe()),e.currentAnimateTimings=null,e.previousNode=t},t.prototype.visitStyle=function(t,e){var n=e.currentTimeline,r=e.currentAnimateTimings;!r&&n.getCurrentStyleProperties().length&&n.forwardFrame();var i=r&&r.easing||t.easing;t.isEmptyStep?n.applyEmptyStep(i):n.setStyles(t.styles,i,e.errors,e.options),e.previousNode=t},t.prototype.visitKeyframes=function(t,e){var n=e.currentAnimateTimings,r=e.currentTimeline.duration,i=n.duration,o=e.createSubContext().currentTimeline;o.easing=n.easing,t.styles.forEach(function(t){o.forwardTime((t.offset||0)*i),o.setStyles(t.styles,t.easing,e.errors,e.options),o.applyStylesToKeyframe()}),e.currentTimeline.mergeTimelineCollectedStyles(o),e.transformIntoNewTimeline(r+i),e.previousNode=t},t.prototype.visitQuery=function(t,e){var n=this,r=e.currentTimeline.currentTime,i=t.options||{},o=i.delay?D(i.delay):0;o&&(6===e.previousNode.type||0==r&&e.currentTimeline.getCurrentStyleProperties().length)&&(e.currentTimeline.snapshotCurrentStyles(),e.previousNode=dt);var s=r,a=e.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!i.optional,e.errors);e.currentQueryTotal=a.length;var u=null;a.forEach(function(r,i){e.currentQueryIndex=i;var a=e.createSubContext(t.options,r);o&&a.delayNextStep(o),r===e.element&&(u=a.currentTimeline),Z(n,t.animation,a),a.currentTimeline.applyStylesToKeyframe(),s=Math.max(s,a.currentTimeline.currentTime)}),e.currentQueryIndex=0,e.currentQueryTotal=0,e.transformIntoNewTimeline(s),u&&(e.currentTimeline.mergeTimelineCollectedStyles(u),e.currentTimeline.snapshotCurrentStyles()),e.previousNode=t},t.prototype.visitStagger=function(t,e){var n=e.parentContext,r=e.currentTimeline,i=t.timings,o=Math.abs(i.duration),s=o*(e.currentQueryTotal-1),a=o*e.currentQueryIndex;switch(i.duration<0?"reverse":i.easing){case"reverse":a=s-a;break;case"full":a=n.currentStaggerTime}var u=e.currentTimeline;a&&u.delayNextStep(a);var l=u.currentTime;Z(this,t.animation,e),e.previousNode=t,n.currentStaggerTime=r.currentTime-l+(r.startTime-n.currentTimeline.startTime)},t}(),dt={},vt=function(){function t(t,e,n,r,i,o,s,a){this._driver=t,this.element=e,this.subInstructions=n,this._enterClassName=r,this._leaveClassName=i,this.errors=o,this.timelines=s,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=dt,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=a||new gt(this._driver,e,0),s.push(this.currentTimeline)}return Object.defineProperty(t.prototype,"params",{get:function(){return this.options.params},enumerable:!0,configurable:!0}),t.prototype.updateOptions=function(t,e){var n=this;if(t){var r=t,i=this.options;null!=r.duration&&(i.duration=D(r.duration)),null!=r.delay&&(i.delay=D(r.delay));var o=r.params;if(o){var s=i.params;s||(s=this.options.params={}),Object.keys(o).forEach(function(t){e&&s.hasOwnProperty(t)||(s[t]=W(o[t],s,n.errors))})}}},t.prototype._copyOptions=function(){var t={};if(this.options){var e=this.options.params;if(e){var n=t.params={};Object.keys(e).forEach(function(t){n[t]=e[t]})}}return t},t.prototype.createSubContext=function(e,n,r){void 0===e&&(e=null);var i=n||this.element,o=new t(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,r||0));return o.previousNode=this.previousNode,o.currentAnimateTimings=this.currentAnimateTimings,o.options=this._copyOptions(),o.updateOptions(e),o.currentQueryIndex=this.currentQueryIndex,o.currentQueryTotal=this.currentQueryTotal,o.parentContext=this,this.subContextCount++,o},t.prototype.transformIntoNewTimeline=function(t){return this.previousNode=dt,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline},t.prototype.appendInstructionToTimeline=function(t,e,n){var r={duration:null!=e?e:t.duration,delay:this.currentTimeline.currentTime+(null!=n?n:0)+t.delay,easing:""},i=new _t(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,r,t.stretchStartingKeyframe);return this.timelines.push(i),r},t.prototype.incrementTime=function(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)},t.prototype.delayNextStep=function(t){t>0&&this.currentTimeline.delayNextStep(t)},t.prototype.invokeQuery=function(t,e,n,r,i,o){var s=[];if(r&&s.push(this.element),t.length>0){t=(t=t.replace(pt,"."+this._enterClassName)).replace(ft,"."+this._leaveClassName);var u=this._driver.query(this.element,t,1!=n);0!==n&&(u=n<0?u.slice(u.length+n,u.length):u.slice(0,n)),s.push.apply(s,a(u))}return i||0!=s.length||o.push('`query("'+e+'")` returned zero elements. (Use `query("'+e+'", { optional: true })` if you wish to allow this.)'),s},t}(),gt=function(){function t(t,e,n,r){this._driver=t,this.element=e,this.startTime=n,this._elementTimelineStylesLookup=r,this.duration=0,this._previousKeyframe={},this._currentKeyframe={},this._keyframes=new Map,this._styleSummary={},this._pendingStyles={},this._backFill={},this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._localTimelineStyles=Object.create(this._backFill,{}),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(e),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(e,this._localTimelineStyles)),this._loadKeyframe()}return t.prototype.containsAnimation=function(){switch(this._keyframes.size){case 0:return!1;case 1:return this.getCurrentStyleProperties().length>0;default:return!0}},t.prototype.getCurrentStyleProperties=function(){return Object.keys(this._currentKeyframe)},Object.defineProperty(t.prototype,"currentTime",{get:function(){return this.startTime+this.duration},enumerable:!0,configurable:!0}),t.prototype.delayNextStep=function(t){var e=1==this._keyframes.size&&Object.keys(this._pendingStyles).length;this.duration||e?(this.forwardTime(this.currentTime+t),e&&this.snapshotCurrentStyles()):this.startTime+=t},t.prototype.fork=function(e,n){return this.applyStylesToKeyframe(),new t(this._driver,e,n||this.currentTime,this._elementTimelineStylesLookup)},t.prototype._loadKeyframe=function(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=Object.create(this._backFill,{}),this._keyframes.set(this.duration,this._currentKeyframe))},t.prototype.forwardFrame=function(){this.duration+=1,this._loadKeyframe()},t.prototype.forwardTime=function(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()},t.prototype._updateStyle=function(t,e){this._localTimelineStyles[t]=e,this._globalTimelineStyles[t]=e,this._styleSummary[t]={time:this.currentTime,value:e}},t.prototype.allowOnlyTimelineStyles=function(){return this._currentEmptyStepKeyframe!==this._currentKeyframe},t.prototype.applyEmptyStep=function(t){var n=this;t&&(this._previousKeyframe.easing=t),Object.keys(this._globalTimelineStyles).forEach(function(t){n._backFill[t]=n._globalTimelineStyles[t]||e.AUTO_STYLE,n._currentKeyframe[t]=e.AUTO_STYLE}),this._currentEmptyStepKeyframe=this._currentKeyframe},t.prototype.setStyles=function(t,n,r,i){var o=this;n&&(this._previousKeyframe.easing=n);var s=i&&i.params||{},a=function u(t,n){var r,i={};return t.forEach(function(t){"*"===t?(r=r||Object.keys(n)).forEach(function(t){i[t]=e.AUTO_STYLE}):R(t,!1,i)}),i}(t,this._globalTimelineStyles);Object.keys(a).forEach(function(t){var n=W(a[t],s,r);o._pendingStyles[t]=n,o._localTimelineStyles.hasOwnProperty(t)||(o._backFill[t]=o._globalTimelineStyles.hasOwnProperty(t)?o._globalTimelineStyles[t]:e.AUTO_STYLE),o._updateStyle(t,n)})},t.prototype.applyStylesToKeyframe=function(){var t=this,e=this._pendingStyles,n=Object.keys(e);0!=n.length&&(this._pendingStyles={},n.forEach(function(n){t._currentKeyframe[n]=e[n]}),Object.keys(this._localTimelineStyles).forEach(function(e){t._currentKeyframe.hasOwnProperty(e)||(t._currentKeyframe[e]=t._localTimelineStyles[e])}))},t.prototype.snapshotCurrentStyles=function(){var t=this;Object.keys(this._localTimelineStyles).forEach(function(e){var n=t._localTimelineStyles[e];t._pendingStyles[e]=n,t._updateStyle(e,n)})},t.prototype.getFinalKeyframe=function(){return this._keyframes.get(this.duration)},Object.defineProperty(t.prototype,"properties",{get:function(){var t=[];for(var e in this._currentKeyframe)t.push(e);return t},enumerable:!0,configurable:!0}),t.prototype.mergeTimelineCollectedStyles=function(t){var e=this;Object.keys(t._styleSummary).forEach(function(n){var r=e._styleSummary[n],i=t._styleSummary[n];(!r||i.time>r.time)&&e._updateStyle(n,i.value)})},t.prototype.buildKeyframes=function(){var t=this;this.applyStylesToKeyframe();var n=new Set,r=new Set,i=1===this._keyframes.size&&0===this.duration,o=[];this._keyframes.forEach(function(s,a){var u=R(s,!0);Object.keys(u).forEach(function(t){var i=u[t];i==e.ɵPRE_STYLE?n.add(t):i==e.AUTO_STYLE&&r.add(t)}),i||(u.offset=a/t.duration),o.push(u)});var s=n.size?Y(n.values()):[],a=r.size?Y(r.values()):[];if(i){var u=o[0],l=x(u);u.offset=0,l.offset=1,o=[u,l]}return ht(this.element,o,s,a,this.duration,this.startTime,this.easing,!1)},t}(),_t=function(t){function e(e,n,r,i,o,s,a){void 0===a&&(a=!1);var u=t.call(this,e,n,s.delay)||this;return u.element=n,u.keyframes=r,u.preStyleProps=i,u.postStyleProps=o,u._stretchStartingKeyframe=a,u.timings={duration:s.duration,delay:s.delay,easing:s.easing},u}return i(e,t),e.prototype.containsAnimation=function(){return this.keyframes.length>1},e.prototype.buildKeyframes=function(){var t=this.keyframes,e=this.timings,n=e.delay,r=e.duration,i=e.easing;if(this._stretchStartingKeyframe&&n){var o=[],s=r+n,a=n/s,u=R(t[0],!1);u.offset=0,o.push(u);var l=R(t[0],!1);l.offset=St(a),o.push(l);for(var h=t.length-1,c=1;c<=h;c++){var p=R(t[c],!1);p.offset=St((n+p.offset*r)/s),o.push(p)}r=s,n=0,i="",t=o}return ht(this.element,t,this.preStyleProps,this.postStyleProps,r,n,i,!0)},e}(gt);function St(t,e){void 0===e&&(e=3);var n=Math.pow(10,e-1);return Math.round(t*n)/n}var Et=function(){function t(t,e){this._driver=t;var n=[],r=it(t,e,n);if(n.length){var i="animation validation failed:\n"+n.join("\n");throw new Error(i)}this._animationAst=r}return t.prototype.buildTimelines=function(t,e,n,r,i){var o=Array.isArray(e)?q(e):e,s=Array.isArray(n)?q(n):n,a=[];i=i||new ct;var u=yt(this._driver,t,this._animationAst,"ng-enter","ng-leave",o,s,r,i,a);if(a.length){var l="animation building failed:\n"+a.join("\n");throw new Error(l)}return u},t}(),bt=function bt(){},Tt=function(){function t(){}return t.prototype.normalizePropertyName=function(t,e){return t},t.prototype.normalizeStyleValue=function(t,e,n,r){return n},t}(),wt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.normalizePropertyName=function(t,e){return $(t)},e.prototype.normalizeStyleValue=function(t,e,n,r){var i="",o=n.toString().trim();if(Pt[e]&&0!==n&&"0"!==n)if("number"==typeof n)i="px";else{var s=n.match(/^[+-]?[\d\.]+([a-z]*)$/);s&&0==s[1].length&&r.push("Please provide a CSS unit value for "+t+":"+n)}return o+i},e}(bt),Pt=function kt(t){var e={};return t.forEach(function(t){return e[t]=!0}),e}("width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective".split(","));
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function At(t,e,n,r,i,o,s,a,u,l,h,c,p){return{type:0,element:t,triggerName:e,isRemovalTransition:i,fromState:n,fromStyles:o,toState:r,toStyles:s,timelines:a,queriedElements:u,preStyleProps:l,postStyleProps:h,totalTime:c,errors:p}}var Nt={},Ot=function(){function t(t,e,n){this._triggerName=t,this.ast=e,this._stateStyles=n}return t.prototype.match=function(t,e,n,r){return function i(t,e,n,r,o){return t.some(function(t){return t(e,n,r,o)})}(this.ast.matchers,t,e,n,r)},t.prototype.buildStyles=function(t,e,n){var r=this._stateStyles["*"],i=this._stateStyles[t],o=r?r.buildStyles(e,n):{};return i?i.buildStyles(e,n):o},t.prototype.build=function(t,e,n,r,i,s,a,u,l,h){var c=[],p=this.ast.options&&this.ast.options.params||Nt,f=this.buildStyles(n,a&&a.params||Nt,c),m=u&&u.params||Nt,d=this.buildStyles(r,m,c),v=new Set,g=new Map,_=new Map,S="void"===r,E={params:o({},p,m)},b=h?[]:yt(t,e,this.ast.animation,i,s,f,d,E,l,c),T=0;if(b.forEach(function(t){T=Math.max(t.duration+t.delay,T)}),c.length)return At(e,this._triggerName,n,r,S,f,d,[],[],g,_,T,c);b.forEach(function(t){var n=t.element,r=y(g,n,{});t.preStyleProps.forEach(function(t){return r[t]=!0});var i=y(_,n,{});t.postStyleProps.forEach(function(t){return i[t]=!0}),n!==e&&v.add(n)});var w=Y(v.values());return At(e,this._triggerName,n,r,S,f,d,b,w,g,_,T)},t}(),Ct=function(){function t(t,e){this.styles=t,this.defaultParams=e}return t.prototype.buildStyles=function(t,e){var n={},r=x(this.defaultParams);return Object.keys(t).forEach(function(e){var n=t[e];null!=n&&(r[e]=n)}),this.styles.styles.forEach(function(t){if("string"!=typeof t){var i=t;Object.keys(i).forEach(function(t){var o=i[t];o.length>1&&(o=W(o,r,e)),n[t]=o})}}),n},t}(),Ft=function(){function t(t,e){var n=this;this.name=t,this.ast=e,this.transitionFactories=[],this.states={},e.states.forEach(function(t){n.states[t.name]=new Ct(t.style,t.options&&t.options.params||{})}),Dt(this.states,"true","1"),Dt(this.states,"false","0"),e.transitions.forEach(function(e){n.transitionFactories.push(new Ot(t,e,n.states))}),this.fallbackTransition=function r(t,e){return new Ot(t,{type:1,animation:{type:2,steps:[],options:null},matchers:[function(t,e){return!0}],options:null,queryCount:0,depCount:0},e)}(t,this.states)}return Object.defineProperty(t.prototype,"containsQueries",{get:function(){return this.ast.queryCount>0},enumerable:!0,configurable:!0}),t.prototype.matchTransition=function(t,e,n,r){return this.transitionFactories.find(function(i){return i.match(t,e,n,r)})||null},t.prototype.matchStyles=function(t,e,n){return this.fallbackTransition.buildStyles(t,e,n)},t}();function Dt(t,e,n){t.hasOwnProperty(e)?t.hasOwnProperty(n)||(t[n]=t[e]):t.hasOwnProperty(n)&&(t[e]=t[n])}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Lt=new ct,jt=function(){function t(t,e,n){this.bodyNode=t,this._driver=e,this._normalizer=n,this._animations={},this._playersById={},this.players=[]}return t.prototype.register=function(t,e){var n=[],r=it(this._driver,e,n);if(n.length)throw new Error("Unable to build the animation due to the following errors: "+n.join("\n"));this._animations[t]=r},t.prototype._buildPlayer=function(t,e,n){var r=t.element,i=h(0,this._normalizer,0,t.keyframes,e,n);return this._driver.animate(r,i,t.duration,t.delay,t.easing,[],!0)},t.prototype.create=function(t,n,r){var i=this;void 0===r&&(r={});var o,s=[],a=this._animations[t],u=new Map;if(a?(o=yt(this._driver,n,a,"ng-enter","ng-leave",{},{},r,Lt,s)).forEach(function(t){var e=y(u,t.element,{});t.postStyleProps.forEach(function(t){return e[t]=null})}):(s.push("The requested animation doesn't exist or has already been destroyed"),o=[]),s.length)throw new Error("Unable to create the animation due to the following errors: "+s.join("\n"));u.forEach(function(t,n){Object.keys(t).forEach(function(r){t[r]=i._driver.computeStyle(n,r,e.AUTO_STYLE)})});var h=l(o.map(function(t){var e=u.get(t.element);return i._buildPlayer(t,{},e)}));return this._playersById[t]=h,h.onDestroy(function(){return i.destroy(t)}),this.players.push(h),h},t.prototype.destroy=function(t){var e=this._getPlayer(t);e.destroy(),delete this._playersById[t];var n=this.players.indexOf(e);n>=0&&this.players.splice(n,1)},t.prototype._getPlayer=function(t){var e=this._playersById[t];if(!e)throw new Error("Unable to find the timeline player referenced by "+t);return e},t.prototype.listen=function(t,e,n,r){var i=f(e,"","","");return c(this._getPlayer(t),n,i,r),function(){}},t.prototype.command=function(t,e,n,r){if("register"!=n)if("create"!=n){var i=this._getPlayer(t);switch(n){case"play":i.play();break;case"pause":i.pause();break;case"reset":i.reset();break;case"restart":i.restart();break;case"finish":i.finish();break;case"init":i.init();break;case"setPosition":i.setPosition(parseFloat(r[0]));break;case"destroy":this.destroy(t)}}else this.create(t,e,r[0]||{});else this.register(t,r[0])},t}(),xt=[],qt={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},Rt={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},It="__ng_removed",Kt=function(){function t(t,e){void 0===e&&(e=""),this.namespaceId=e;var n=t&&t.hasOwnProperty("value");if(this.value=function r(t){return null!=t?t:null}(n?t.value:t),n){var i=x(t);delete i.value,this.options=i}else this.options={};this.options.params||(this.options.params={})}return Object.defineProperty(t.prototype,"params",{get:function(){return this.options.params},enumerable:!0,configurable:!0}),t.prototype.absorbOptions=function(t){var e=t.params;if(e){var n=this.options.params;Object.keys(e).forEach(function(t){null==n[t]&&(n[t]=e[t])})}},t}(),Qt=new Kt("void"),Bt=function(){function t(t,e,n){this.id=t,this.hostElement=e,this._engine=n,this.players=[],this._triggers={},this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+t,Vt(e,this._hostClassName)}return t.prototype.listen=function(t,e,n,r){var i=this;if(!this._triggers.hasOwnProperty(e))throw new Error('Unable to listen on the animation trigger event "'+n+'" because the animation trigger "'+e+"\" doesn't exist!");if(null==n||0==n.length)throw new Error('Unable to listen on the animation trigger "'+e+'" because the provided event is undefined!');if(!function o(t){return"start"==t||"done"==t}(n))throw new Error('The provided animation trigger event "'+n+'" for the animation trigger "'+e+'" is not supported!');var s=y(this._elementListeners,t,[]),a={name:e,phase:n,callback:r};s.push(a);var u=y(this._engine.statesByElement,t,{});return u.hasOwnProperty(e)||(Vt(t,"ng-trigger"),Vt(t,"ng-trigger-"+e),u[e]=Qt),function(){i._engine.afterFlush(function(){var t=s.indexOf(a);t>=0&&s.splice(t,1),i._triggers[e]||delete u[e]})}},t.prototype.register=function(t,e){return!this._triggers[t]&&(this._triggers[t]=e,!0)},t.prototype._getTrigger=function(t){var e=this._triggers[t];if(!e)throw new Error('The provided animation trigger "'+t+'" has not been registered!');return e},t.prototype.trigger=function(t,e,n,r){var i=this;void 0===r&&(r=!0);var o=this._getTrigger(e),s=new zt(this.id,e,t),a=this._engine.statesByElement.get(t);a||(Vt(t,"ng-trigger"),Vt(t,"ng-trigger-"+e),this._engine.statesByElement.set(t,a={}));var u=a[e],l=new Kt(n,this.id);if(!(n&&n.hasOwnProperty("value"))&&u&&l.absorbOptions(u.options),a[e]=l,u||(u=Qt),"void"===l.value||u.value!==l.value){var h=y(this._engine.playersByElement,t,[]);h.forEach(function(t){t.namespaceId==i.id&&t.triggerName==e&&t.queued&&t.destroy()});var c=o.matchTransition(u.value,l.value,t,l.params),p=!1;if(!c){if(!r)return;c=o.fallbackTransition,p=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:e,transition:c,fromState:u,toState:l,player:s,isFallbackTransition:p}),p||(Vt(t,"ng-animate-queued"),s.onStart(function(){Gt(t,"ng-animate-queued")})),s.onDone(function(){var e=i.players.indexOf(s);e>=0&&i.players.splice(e,1);var n=i._engine.playersByElement.get(t);if(n){var r=n.indexOf(s);r>=0&&n.splice(r,1)}}),this.players.push(s),h.push(s),s}if(!function f(t,e){var n=Object.keys(t),r=Object.keys(e);if(n.length!=r.length)return!1;for(var i=0;i<n.length;i++){var o=n[i];if(!e.hasOwnProperty(o)||t[o]!==e[o])return!1}return!0}(u.params,l.params)){var m=[],d=o.matchStyles(u.value,u.params,m),v=o.matchStyles(l.value,l.params,m);m.length?this._engine.reportError(m):this._engine.afterFlush(function(){B(t,d),Q(t,v)})}},t.prototype.deregister=function(t){var e=this;delete this._triggers[t],this._engine.statesByElement.forEach(function(e,n){delete e[t]}),this._elementListeners.forEach(function(n,r){e._elementListeners.set(r,n.filter(function(e){return e.name!=t}))})},t.prototype.clearElementCache=function(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);var e=this._engine.playersByElement.get(t);e&&(e.forEach(function(t){return t.destroy()}),this._engine.playersByElement.delete(t))},t.prototype._signalRemovalForInnerTriggers=function(t,e,n){var r=this;void 0===n&&(n=!1),this._engine.driver.query(t,".ng-trigger",!0).forEach(function(t){if(!t[It]){var n=r._engine.fetchNamespacesByElement(t);n.size?n.forEach(function(n){return n.triggerLeaveAnimation(t,e,!1,!0)}):r.clearElementCache(t)}})},t.prototype.triggerLeaveAnimation=function(t,e,n,r){var i=this,o=this._engine.statesByElement.get(t);if(o){var s=[];if(Object.keys(o).forEach(function(e){if(i._triggers[e]){var n=i.trigger(t,e,"void",r);n&&s.push(n)}}),s.length)return this._engine.markElementAsRemoved(this.id,t,!0,e),n&&l(s).onDone(function(){return i._engine.processLeaveNode(t)}),!0}return!1},t.prototype.prepareLeaveAnimationListeners=function(t){var e=this,n=this._elementListeners.get(t);if(n){var r=new Set;n.forEach(function(n){var i=n.name;if(!r.has(i)){r.add(i);var o=e._triggers[i].fallbackTransition,s=e._engine.statesByElement.get(t)[i]||Qt,a=new Kt("void"),u=new zt(e.id,i,t);e._engine.totalQueuedPlayers++,e._queue.push({element:t,triggerName:i,transition:o,fromState:s,toState:a,player:u,isFallbackTransition:!0})}})}},t.prototype.removeNode=function(t,e){var n=this,r=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,e,!0),!this.triggerLeaveAnimation(t,e,!0)){var i=!1;if(r.totalAnimations){var o=r.players.length?r.playersByQueriedElement.get(t):[];if(o&&o.length)i=!0;else for(var s=t;s=s.parentNode;)if(r.statesByElement.get(s)){i=!0;break}}this.prepareLeaveAnimationListeners(t),i?r.markElementAsRemoved(this.id,t,!1,e):(r.afterFlush(function(){return n.clearElementCache(t)}),r.destroyInnerAnimations(t),r._onRemovalComplete(t,e))}},t.prototype.insertNode=function(t,e){Vt(t,this._hostClassName)},t.prototype.drainQueuedTransitions=function(t){var e=this,n=[];return this._queue.forEach(function(r){var i=r.player;if(!i.destroyed){var o=r.element,s=e._elementListeners.get(o);s&&s.forEach(function(e){if(e.name==r.triggerName){var n=f(o,r.triggerName,r.fromState.value,r.toState.value);n._data=t,c(r.player,e.phase,n,e.callback)}}),i.markedForDestroy?e._engine.afterFlush(function(){i.destroy()}):n.push(r)}}),this._queue=[],n.sort(function(t,n){var r=t.transition.ast.depCount,i=n.transition.ast.depCount;return 0==r||0==i?r-i:e._engine.driver.containsElement(t.element,n.element)?1:-1})},t.prototype.destroy=function(t){this.players.forEach(function(t){return t.destroy()}),this._signalRemovalForInnerTriggers(this.hostElement,t)},t.prototype.elementContainsData=function(t){var e=!1;return this._elementListeners.has(t)&&(e=!0),!!this._queue.find(function(e){return e.element===t})||e},t}(),Mt=function(){function t(t,e,n){this.bodyNode=t,this.driver=e,this._normalizer=n,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=function(t,e){}}return t.prototype._onRemovalComplete=function(t,e){this.onRemovalComplete(t,e)},Object.defineProperty(t.prototype,"queuedPlayers",{get:function(){var t=[];return this._namespaceList.forEach(function(e){e.players.forEach(function(e){e.queued&&t.push(e)})}),t},enumerable:!0,configurable:!0}),t.prototype.createNamespace=function(t,e){var n=new Bt(t,e,this);return e.parentNode?this._balanceNamespaceList(n,e):(this.newHostElements.set(e,n),this.collectEnterElement(e)),this._namespaceLookup[t]=n},t.prototype._balanceNamespaceList=function(t,e){var n=this._namespaceList.length-1;if(n>=0){for(var r=!1,i=n;i>=0;i--)if(this.driver.containsElement(this._namespaceList[i].hostElement,e)){this._namespaceList.splice(i+1,0,t),r=!0;break}r||this._namespaceList.splice(0,0,t)}else this._namespaceList.push(t);return this.namespacesByHostElement.set(e,t),t},t.prototype.register=function(t,e){var n=this._namespaceLookup[t];return n||(n=this.createNamespace(t,e)),n},t.prototype.registerTrigger=function(t,e,n){var r=this._namespaceLookup[t];r&&r.register(e,n)&&this.totalAnimations++},t.prototype.destroy=function(t,e){var n=this;if(t){var r=this._fetchNamespace(t);this.afterFlush(function(){n.namespacesByHostElement.delete(r.hostElement),delete n._namespaceLookup[t];var e=n._namespaceList.indexOf(r);e>=0&&n._namespaceList.splice(e,1)}),this.afterFlushAnimationsDone(function(){return r.destroy(e)})}},t.prototype._fetchNamespace=function(t){return this._namespaceLookup[t]},t.prototype.fetchNamespacesByElement=function(t){var e=new Set,n=this.statesByElement.get(t);if(n)for(var r=Object.keys(n),i=0;i<r.length;i++){var o=n[r[i]].namespaceId;if(o){var s=this._fetchNamespace(o);s&&e.add(s)}}return e},t.prototype.trigger=function(t,e,n,r){if(Ut(e)){var i=this._fetchNamespace(t);if(i)return i.trigger(e,n,r),!0}return!1},t.prototype.insertNode=function(t,e,n,r){if(Ut(e)){var i=e[It];if(i&&i.setForRemoval){i.setForRemoval=!1,i.setForMove=!0;var o=this.collectedLeaveElements.indexOf(e);o>=0&&this.collectedLeaveElements.splice(o,1)}if(t){var s=this._fetchNamespace(t);s&&s.insertNode(e,n)}r&&this.collectEnterElement(e)}},t.prototype.collectEnterElement=function(t){this.collectedEnterElements.push(t)},t.prototype.markElementAsDisabled=function(t,e){e?this.disabledNodes.has(t)||(this.disabledNodes.add(t),Vt(t,"ng-animate-disabled")):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Gt(t,"ng-animate-disabled"))},t.prototype.removeNode=function(t,e,n){if(Ut(e)){var r=t?this._fetchNamespace(t):null;r?r.removeNode(e,n):this.markElementAsRemoved(t,e,!1,n)}else this._onRemovalComplete(e,n)},t.prototype.markElementAsRemoved=function(t,e,n,r){this.collectedLeaveElements.push(e),e[It]={namespaceId:t,setForRemoval:r,hasAnimation:n,removedBeforeQueried:!1}},t.prototype.listen=function(t,e,n,r,i){return Ut(e)?this._fetchNamespace(t).listen(e,n,r,i):function(){}},t.prototype._buildInstruction=function(t,e,n,r,i){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,n,r,t.fromState.options,t.toState.options,e,i)},t.prototype.destroyInnerAnimations=function(t){var e=this,n=this.driver.query(t,".ng-trigger",!0);n.forEach(function(t){return e.destroyActiveAnimationsForElement(t)}),0!=this.playersByQueriedElement.size&&(n=this.driver.query(t,".ng-animating",!0)).forEach(function(t){return e.finishActiveQueriedAnimationOnElement(t)})},t.prototype.destroyActiveAnimationsForElement=function(t){var e=this.playersByElement.get(t);e&&e.forEach(function(t){t.queued?t.markedForDestroy=!0:t.destroy()})},t.prototype.finishActiveQueriedAnimationOnElement=function(t){var e=this.playersByQueriedElement.get(t);e&&e.forEach(function(t){return t.finish()})},t.prototype.whenRenderingDone=function(){var t=this;return new Promise(function(e){if(t.players.length)return l(t.players).onDone(function(){return e()});e()})},t.prototype.processLeaveNode=function(t){var e=this,n=t[It];if(n&&n.setForRemoval){if(t[It]=qt,n.namespaceId){this.destroyInnerAnimations(t);var r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(t)}this._onRemovalComplete(t,n.setForRemoval)}this.driver.matchesElement(t,".ng-animate-disabled")&&this.markElementAsDisabled(t,!1),this.driver.query(t,".ng-animate-disabled",!0).forEach(function(t){e.markElementAsDisabled(t,!1)})},t.prototype.flush=function(t){var e=this;void 0===t&&(t=-1);var n=[];if(this.newHostElements.size&&(this.newHostElements.forEach(function(t,n){return e._balanceNamespaceList(t,n)}),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(var r=0;r<this.collectedEnterElements.length;r++)Vt(this.collectedEnterElements[r],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){var i=[];try{n=this._flushAnimations(i,t)}finally{for(r=0;r<i.length;r++)i[r]()}}else for(r=0;r<this.collectedLeaveElements.length;r++)this.processLeaveNode(this.collectedLeaveElements[r]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(function(t){return t()}),this._flushFns=[],this._whenQuietFns.length){var o=this._whenQuietFns;this._whenQuietFns=[],n.length?l(n).onDone(function(){o.forEach(function(t){return t()})}):o.forEach(function(t){return t()})}},t.prototype.reportError=function(t){throw new Error("Unable to process animations due to the following failed trigger transitions\n "+t.join("\n"))},t.prototype._flushAnimations=function(t,n){var r=this,i=new ct,s=[],u=new Map,h=[],c=new Map,p=new Map,f=new Map,m=new Set;this.disabledNodes.forEach(function(t){m.add(t);for(var e=r.driver.query(t,".ng-animate-queued",!0),n=0;n<e.length;n++)m.add(e[n])});var d=this.bodyNode,v=Array.from(this.statesByElement.keys()),g=Ht(v,this.collectedEnterElements),_=new Map,S=0;g.forEach(function(t,e){var n="ng-enter"+S++;_.set(e,n),t.forEach(function(t){return Vt(t,n)})});for(var E=[],b=new Set,T=new Set,w=0;w<this.collectedLeaveElements.length;w++)(z=(M=this.collectedLeaveElements[w])[It])&&z.setForRemoval&&(E.push(M),b.add(M),z.hasAnimation?this.driver.query(M,".ng-star-inserted",!0).forEach(function(t){return b.add(t)}):T.add(M));var P=new Map,k=Ht(v,Array.from(b));k.forEach(function(t,e){var n="ng-leave"+S++;P.set(e,n),t.forEach(function(t){return Vt(t,n)})}),t.push(function(){g.forEach(function(t,e){var n=_.get(e);t.forEach(function(t){return Gt(t,n)})}),k.forEach(function(t,e){var n=P.get(e);t.forEach(function(t){return Gt(t,n)})}),E.forEach(function(t){r.processLeaveNode(t)})});for(var A=[],N=[],O=this._namespaceList.length-1;O>=0;O--)this._namespaceList[O].drainQueuedTransitions(n).forEach(function(t){var e=t.player,n=t.element;if(A.push(e),r.collectedEnterElements.length){var o=n[It];if(o&&o.setForMove)return void e.destroy()}var a=!d||!r.driver.containsElement(d,n),u=P.get(n),l=_.get(n),m=r._buildInstruction(t,i,l,u,a);if(m.errors&&m.errors.length)N.push(m);else{if(a)return e.onStart(function(){return B(n,m.fromStyles)}),e.onDestroy(function(){return Q(n,m.toStyles)}),void s.push(e);if(t.isFallbackTransition)return e.onStart(function(){return B(n,m.fromStyles)}),e.onDestroy(function(){return Q(n,m.toStyles)}),void s.push(e);m.timelines.forEach(function(t){return t.stretchStartingKeyframe=!0}),i.append(n,m.timelines),h.push({instruction:m,player:e,element:n}),m.queriedElements.forEach(function(t){return y(c,t,[]).push(e)}),m.preStyleProps.forEach(function(t,e){var n=Object.keys(t);if(n.length){var r=p.get(e);r||p.set(e,r=new Set),n.forEach(function(t){return r.add(t)})}}),m.postStyleProps.forEach(function(t,e){var n=Object.keys(t),r=f.get(e);r||f.set(e,r=new Set),n.forEach(function(t){return r.add(t)})})}});if(N.length){var C=[];N.forEach(function(t){C.push("@"+t.triggerName+" has failed due to:\n"),t.errors.forEach(function(t){return C.push("- "+t+"\n")})}),A.forEach(function(t){return t.destroy()}),this.reportError(C)}var F=new Map,D=new Map;h.forEach(function(t){var e=t.element;i.has(e)&&(D.set(e,e),r._beforeAnimationBuild(t.player.namespaceId,t.instruction,F))}),s.forEach(function(t){var e=t.element;r._getPreviousPlayers(e,!1,t.namespaceId,t.triggerName,null).forEach(function(t){y(F,e,[]).push(t),t.destroy()})});var L=E.filter(function(t){return Jt(t,p,f)}),j=new Map;Yt(j,this.driver,T,f,e.AUTO_STYLE).forEach(function(t){Jt(t,p,f)&&L.push(t)});var x=new Map;g.forEach(function(t,n){Yt(x,r.driver,new Set(t),p,e.ɵPRE_STYLE)}),L.forEach(function(t){var e=j.get(t),n=x.get(t);j.set(t,o({},e,n))});var q=[],R=[],I={};h.forEach(function(t){var e=t.element,n=t.player,o=t.instruction;if(i.has(e)){if(m.has(e))return n.onDestroy(function(){return Q(e,o.toStyles)}),n.disabled=!0,n.overrideTotalTime(o.totalTime),void s.push(n);var a=I;if(D.size>1){for(var h=e,c=[];h=h.parentNode;){var p=D.get(h);if(p){a=p;break}c.push(h)}c.forEach(function(t){return D.set(t,a)})}var f=r._buildAnimation(n.namespaceId,o,F,u,x,j);if(n.setRealPlayer(f),a===I)q.push(n);else{var y=r.playersByElement.get(a);y&&y.length&&(n.parentPlayer=l(y)),s.push(n)}}else B(e,o.fromStyles),n.onDestroy(function(){return Q(e,o.toStyles)}),R.push(n),m.has(e)&&s.push(n)}),R.forEach(function(t){var e=u.get(t.element);if(e&&e.length){var n=l(e);t.setRealPlayer(n)}}),s.forEach(function(t){t.parentPlayer?t.syncPlayerEvents(t.parentPlayer):t.destroy()});for(var K=0;K<E.length;K++){var M,z=(M=E[K])[It];if(Gt(M,"ng-leave"),!z||!z.hasAnimation){var U=[];if(c.size){var W=c.get(M);W&&W.length&&U.push.apply(U,a(W));for(var Y=this.driver.query(M,".ng-animating",!0),H=0;H<Y.length;H++){var $=c.get(Y[H]);$&&$.length&&U.push.apply(U,a($))}}var V=U.filter(function(t){return!t.destroyed});V.length?Zt(this,M,V):this.processLeaveNode(M)}}return E.length=0,q.forEach(function(t){r.players.push(t),t.onDone(function(){t.destroy();var e=r.players.indexOf(t);r.players.splice(e,1)}),t.play()}),q},t.prototype.elementContainsData=function(t,e){var n=!1,r=e[It];return r&&r.setForRemoval&&(n=!0),this.playersByElement.has(e)&&(n=!0),this.playersByQueriedElement.has(e)&&(n=!0),this.statesByElement.has(e)&&(n=!0),this._fetchNamespace(t).elementContainsData(e)||n},t.prototype.afterFlush=function(t){this._flushFns.push(t)},t.prototype.afterFlushAnimationsDone=function(t){this._whenQuietFns.push(t)},t.prototype._getPreviousPlayers=function(t,e,n,r,i){var o=[];if(e){var s=this.playersByQueriedElement.get(t);s&&(o=s)}else{var a=this.playersByElement.get(t);if(a){var u=!i||"void"==i;a.forEach(function(t){t.queued||(u||t.triggerName==r)&&o.push(t)})}}return(n||r)&&(o=o.filter(function(t){return!(n&&n!=t.namespaceId||r&&r!=t.triggerName)})),o},t.prototype._beforeAnimationBuild=function(t,e,n){var r,i,o=e.element,s=e.isRemovalTransition?void 0:t,a=e.isRemovalTransition?void 0:e.triggerName,u=function(t){var r=t.element,i=r!==o,u=y(n,r,[]);l._getPreviousPlayers(r,i,s,a,e.toState).forEach(function(t){var e=t.getRealPlayer();e.beforeDestroy&&e.beforeDestroy(),t.destroy(),u.push(t)})},l=this;try{for(var h=function c(t){var e="function"==typeof Symbol&&t[Symbol.iterator],n=0;return e?e.call(t):{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}}}(e.timelines),p=h.next();!p.done;p=h.next())u(p.value)}catch(t){r={error:t}}finally{try{p&&!p.done&&(i=h.return)&&i.call(h)}finally{if(r)throw r.error}}B(o,e.fromStyles)},t.prototype._buildAnimation=function(t,n,r,i,o,s){var a=this,u=n.triggerName,c=n.element,p=[],f=new Set,m=new Set,d=n.timelines.map(function(n){var l=n.element;f.add(l);var y=l[It];if(y&&y.removedBeforeQueried)return new e.NoopAnimationPlayer(n.duration,n.delay);var d=l!==c,v=function g(t){var n=[];return function t(n,r){for(var i=0;i<n.length;i++){var o=n[i];o instanceof e.ɵAnimationGroupPlayer?t(o.players,r):r.push(o)}}(t,n),n}((r.get(l)||xt).map(function(t){return t.getRealPlayer()})).filter(function(t){return!!t.element&&t.element===l}),_=o.get(l),S=s.get(l),E=h(0,a._normalizer,0,n.keyframes,_,S),b=a._buildPlayer(n,E,v);if(n.subTimeline&&i&&m.add(l),d){var T=new zt(t,u,l);T.setRealPlayer(b),p.push(T)}return b});p.forEach(function(t){y(a.playersByQueriedElement,t.element,[]).push(t),t.onDone(function(){return function e(t,n,r){var i;if(t instanceof Map){if(i=t.get(n)){if(i.length){var o=i.indexOf(r);i.splice(o,1)}0==i.length&&t.delete(n)}}else(i=t[n])&&(i.length&&(o=i.indexOf(r),i.splice(o,1)),0==i.length&&delete t[n]);return i}(a.playersByQueriedElement,t.element,t)})}),f.forEach(function(t){return Vt(t,"ng-animating")});var v=l(d);return v.onDestroy(function(){f.forEach(function(t){return Gt(t,"ng-animating")}),Q(c,n.toStyles)}),m.forEach(function(t){y(i,t,[]).push(v)}),v},t.prototype._buildPlayer=function(t,n,r){return n.length>0?this.driver.animate(t.element,n,t.duration,t.delay,t.easing,r):new e.NoopAnimationPlayer(t.duration,t.delay)},t}(),zt=function(){function t(t,n,r){this.namespaceId=t,this.triggerName=n,this.element=r,this._player=new e.NoopAnimationPlayer,this._containsRealPlayer=!1,this._queuedCallbacks={},this.destroyed=!1,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}return t.prototype.setRealPlayer=function(t){var e=this;this._containsRealPlayer||(this._player=t,Object.keys(this._queuedCallbacks).forEach(function(n){e._queuedCallbacks[n].forEach(function(e){return c(t,n,void 0,e)})}),this._queuedCallbacks={},this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)},t.prototype.getRealPlayer=function(){return this._player},t.prototype.overrideTotalTime=function(t){this.totalTime=t},t.prototype.syncPlayerEvents=function(t){var e=this,n=this._player;n.triggerCallback&&t.onStart(function(){return n.triggerCallback("start")}),t.onDone(function(){return e.finish()}),t.onDestroy(function(){return e.destroy()})},t.prototype._queueEvent=function(t,e){y(this._queuedCallbacks,t,[]).push(e)},t.prototype.onDone=function(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)},t.prototype.onStart=function(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)},t.prototype.onDestroy=function(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)},t.prototype.init=function(){this._player.init()},t.prototype.hasStarted=function(){return!this.queued&&this._player.hasStarted()},t.prototype.play=function(){!this.queued&&this._player.play()},t.prototype.pause=function(){!this.queued&&this._player.pause()},t.prototype.restart=function(){!this.queued&&this._player.restart()},t.prototype.finish=function(){this._player.finish()},t.prototype.destroy=function(){this.destroyed=!0,this._player.destroy()},t.prototype.reset=function(){!this.queued&&this._player.reset()},t.prototype.setPosition=function(t){this.queued||this._player.setPosition(t)},t.prototype.getPosition=function(){return this.queued?0:this._player.getPosition()},t.prototype.triggerCallback=function(t){var e=this._player;e.triggerCallback&&e.triggerCallback(t)},t}();function Ut(t){return t&&1===t.nodeType}function Wt(t,e){var n=t.style.display;return t.style.display=null!=e?e:"none",n}function Yt(t,e,n,r,i){var o=[];n.forEach(function(t){return o.push(Wt(t))});var s=[];r.forEach(function(n,r){var o={};n.forEach(function(t){var n=o[t]=e.computeStyle(r,t,i);n&&0!=n.length||(r[It]=Rt,s.push(r))}),t.set(r,o)});var a=0;return n.forEach(function(t){return Wt(t,o[a++])}),s}function Ht(t,e){var n=new Map;if(t.forEach(function(t){return n.set(t,[])}),0==e.length)return n;var r=new Set(e),i=new Map;return e.forEach(function(t){var e=function t(e){if(!e)return 1;var o=i.get(e);if(o)return o;var s=e.parentNode;return o=n.has(s)?s:r.has(s)?1:t(s),i.set(e,o),o}(t);1!==e&&n.get(e).push(t)}),n}var $t="$$classes";function Vt(t,e){if(t.classList)t.classList.add(e);else{var n=t[$t];n||(n=t[$t]={}),n[e]=!0}}function Gt(t,e){if(t.classList)t.classList.remove(e);else{var n=t[$t];n&&delete n[e]}}function Zt(t,e,n){l(n).onDone(function(){return t.processLeaveNode(e)})}function Jt(t,e,n){var r=n.get(t);if(!r)return!1;var i=e.get(t);return i?r.forEach(function(t){return i.add(t)}):e.set(t,r),n.delete(t),!0}var Xt=function(){function t(t,e,n){var r=this;this.bodyNode=t,this._driver=e,this._triggerCache={},this.onRemovalComplete=function(t,e){},this._transitionEngine=new Mt(t,e,n),this._timelineEngine=new jt(t,e,n),this._transitionEngine.onRemovalComplete=function(t,e){return r.onRemovalComplete(t,e)}}return t.prototype.registerTrigger=function(t,e,n,r,i){var o=t+"-"+r,s=this._triggerCache[o];if(!s){var a=[],u=it(this._driver,i,a);if(a.length)throw new Error('The animation trigger "'+r+'" has failed to build due to the following errors:\n - '+a.join("\n - "));s=function l(t,e){return new Ft(t,e)}(r,u),this._triggerCache[o]=s}this._transitionEngine.registerTrigger(e,r,s)},t.prototype.register=function(t,e){this._transitionEngine.register(t,e)},t.prototype.destroy=function(t,e){this._transitionEngine.destroy(t,e)},t.prototype.onInsert=function(t,e,n,r){this._transitionEngine.insertNode(t,e,n,r)},t.prototype.onRemove=function(t,e,n){this._transitionEngine.removeNode(t,e,n)},t.prototype.disableAnimations=function(t,e){this._transitionEngine.markElementAsDisabled(t,e)},t.prototype.process=function(t,e,n,r){if("@"==n.charAt(0)){var i=s(m(n),2);this._timelineEngine.command(i[0],e,i[1],r)}else this._transitionEngine.trigger(t,e,n,r)},t.prototype.listen=function(t,e,n,r,i){if("@"==n.charAt(0)){var o=s(m(n),2);return this._timelineEngine.listen(o[0],e,o[1],i)}return this._transitionEngine.listen(t,e,n,r,i)},t.prototype.flush=function(t){void 0===t&&(t=-1),this._transitionEngine.flush(t)},Object.defineProperty(t.prototype,"players",{get:function(){return this._transitionEngine.players.concat(this._timelineEngine.players)},enumerable:!0,configurable:!0}),t.prototype.whenRenderingDone=function(){return this._transitionEngine.whenRenderingDone()},t}();
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function te(t,e){var n=null,r=null;return Array.isArray(e)&&e.length?(n=ne(e[0]),e.length>1&&(r=ne(e[e.length-1]))):e&&(n=ne(e)),n||r?new ee(t,n,r):null}var ee=function(){function t(e,n,r){this._element=e,this._startStyles=n,this._endStyles=r,this._state=0;var i=t.initialStylesByElement.get(e);i||t.initialStylesByElement.set(e,i={}),this._initialStyles=i}return t.prototype.start=function(){this._state<1&&(this._startStyles&&Q(this._element,this._startStyles,this._initialStyles),this._state=1)},t.prototype.finish=function(){this.start(),this._state<2&&(Q(this._element,this._initialStyles),this._endStyles&&(Q(this._element,this._endStyles),this._endStyles=null),this._state=1)},t.prototype.destroy=function(){this.finish(),this._state<3&&(t.initialStylesByElement.delete(this._element),this._startStyles&&(B(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(B(this._element,this._endStyles),this._endStyles=null),Q(this._element,this._initialStyles),this._state=3)},t.initialStylesByElement=new WeakMap,t}();function ne(t){for(var e=null,n=Object.keys(t),r=0;r<n.length;r++){var i=n[r];re(i)&&((e=e||{})[i]=t[i])}return e}function re(t){return"display"===t||"position"===t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var ie="animation",oe="animationend",se=function(){function t(t,e,n,r,i,o,s){var a=this;this._element=t,this._name=e,this._duration=n,this._delay=r,this._easing=i,this._fillMode=o,this._onDoneFn=s,this._finished=!1,this._destroyed=!1,this._startTime=0,this._position=0,this._eventFn=function(t){return a._handleCallback(t)}}return t.prototype.apply=function(){!function t(e,n){var r=pe(e,"").trim();r.length&&(function i(t,e){for(var n=0,r=0;r<t.length;r++)t.charAt(r)===e&&n++;return n}(r,",")+1,n=r+", "+n),ce(e,"",n)}(this._element,this._duration+"ms "+this._easing+" "+this._delay+"ms 1 normal "+this._fillMode+" "+this._name),he(this._element,this._eventFn,!1),this._startTime=Date.now()},t.prototype.pause=function(){ae(this._element,this._name,"paused")},t.prototype.resume=function(){ae(this._element,this._name,"running")},t.prototype.setPosition=function(t){var e=ue(this._element,this._name);this._position=t*this._duration,ce(this._element,"Delay","-"+this._position+"ms",e)},t.prototype.getPosition=function(){return this._position},t.prototype._handleCallback=function(t){var e=t._ngTestManualTimestamp||Date.now(),n=1e3*parseFloat(t.elapsedTime.toFixed(3));t.animationName==this._name&&Math.max(e-this._startTime,0)>=this._delay&&n>=this._duration&&this.finish()},t.prototype.finish=function(){this._finished||(this._finished=!0,this._onDoneFn(),he(this._element,this._eventFn,!0))},t.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.finish(),function t(e,n){var r=pe(e,"").split(","),i=le(r,n);i>=0&&(r.splice(i,1),ce(e,"",r.join(",")))}(this._element,this._name))},t}();function ae(t,e,n){ce(t,"PlayState",n,ue(t,e))}function ue(t,e){var n=pe(t,"");return n.indexOf(",")>0?le(n.split(","),e):le([n],e)}function le(t,e){for(var n=0;n<t.length;n++)if(t[n].indexOf(e)>=0)return n;return-1}function he(t,e,n){n?t.removeEventListener(oe,e):t.addEventListener(oe,e)}function ce(t,e,n,r){var i=ie+e;if(null!=r){var o=t.style[i];if(o.length){var s=o.split(",");s[r]=n,n=s.join(",")}}t.style[i]=n}function pe(t,e){return t.style[ie+e]}var fe="linear",ye=function(){function t(t,e,n,r,i,o,s,a){this.element=t,this.keyframes=e,this.animationName=n,this._duration=r,this._delay=i,this._finalStyles=s,this._specialStyles=a,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._started=!1,this.currentSnapshot={},this._state=0,this.easing=o||fe,this.totalTime=r+i,this._buildStyler()}return t.prototype.onStart=function(t){this._onStartFns.push(t)},t.prototype.onDone=function(t){this._onDoneFns.push(t)},t.prototype.onDestroy=function(t){this._onDestroyFns.push(t)},t.prototype.destroy=function(){this.init(),this._state>=4||(this._state=4,this._styler.destroy(),this._flushStartFns(),this._flushDoneFns(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(function(t){return t()}),this._onDestroyFns=[])},t.prototype._flushDoneFns=function(){this._onDoneFns.forEach(function(t){return t()}),this._onDoneFns=[]},t.prototype._flushStartFns=function(){this._onStartFns.forEach(function(t){return t()}),this._onStartFns=[]},t.prototype.finish=function(){this.init(),this._state>=3||(this._state=3,this._styler.finish(),this._flushStartFns(),this._specialStyles&&this._specialStyles.finish(),this._flushDoneFns())},t.prototype.setPosition=function(t){this._styler.setPosition(t)},t.prototype.getPosition=function(){return this._styler.getPosition()},t.prototype.hasStarted=function(){return this._state>=2},t.prototype.init=function(){this._state>=1||(this._state=1,this._styler.apply(),this._delay&&this._styler.pause())},t.prototype.play=function(){this.init(),this.hasStarted()||(this._flushStartFns(),this._state=2,this._specialStyles&&this._specialStyles.start()),this._styler.resume()},t.prototype.pause=function(){this.init(),this._styler.pause()},t.prototype.restart=function(){this.reset(),this.play()},t.prototype.reset=function(){this._styler.destroy(),this._buildStyler(),this._styler.apply()},t.prototype._buildStyler=function(){var t=this;this._styler=new se(this.element,this.animationName,this._duration,this._delay,this.easing,"forwards",function(){return t.finish()})},t.prototype.triggerCallback=function(t){var e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(function(t){return t()}),e.length=0},t.prototype.beforeDestroy=function(){var t=this;this.init();var e={};if(this.hasStarted()){var n=this._state>=3;Object.keys(this._finalStyles).forEach(function(r){"offset"!=r&&(e[r]=n?t._finalStyles[r]:J(t.element,r))})}this.currentSnapshot=e},t}(),me=function(t){function e(e,n){var r=t.call(this)||this;return r.element=e,r._startingStyles={},r.__initialized=!1,r._styles=N(n),r}return i(e,t),e.prototype.init=function(){var e=this;!this.__initialized&&this._startingStyles&&(this.__initialized=!0,Object.keys(this._styles).forEach(function(t){e._startingStyles[t]=e.element.style[t]}),t.prototype.init.call(this))},e.prototype.play=function(){var e=this;this._startingStyles&&(this.init(),Object.keys(this._styles).forEach(function(t){return e.element.style.setProperty(t,e._styles[t])}),t.prototype.play.call(this))},e.prototype.destroy=function(){var e=this;this._startingStyles&&(Object.keys(this._startingStyles).forEach(function(t){var n=e._startingStyles[t];n?e.element.style.setProperty(t,n):e.element.style.removeProperty(t)}),this._startingStyles=null,t.prototype.destroy.call(this))},e}(e.NoopAnimationPlayer),de=function(){function t(){this._count=0,this._head=document.querySelector("head"),this._warningIssued=!1}return t.prototype.validateStyleProperty=function(t){return w(t)},t.prototype.matchesElement=function(t,e){return P(t,e)},t.prototype.containsElement=function(t,e){return k(t,e)},t.prototype.query=function(t,e,n){return A(t,e,n)},t.prototype.computeStyle=function(t,e,n){return window.getComputedStyle(t)[e]},t.prototype.buildKeyframeElement=function(t,e,n){var r="@keyframes "+e+" {\n",i="";(n=n.map(function(t){return N(t)})).forEach(function(t){i=" ";var e=parseFloat(t.offset);r+=""+i+100*e+"% {\n",i+=" ",Object.keys(t).forEach(function(e){var n=t[e];switch(e){case"offset":return;case"easing":return void(n&&(r+=i+"animation-timing-function: "+n+";\n"));default:return void(r+=""+i+e+": "+n+";\n")}}),r+=i+"}\n"}),r+="}\n";var o=document.createElement("style");return o.innerHTML=r,o},t.prototype.animate=function(t,e,n,r,i,o,s){void 0===o&&(o=[]),s&&this._notifyFaultyScrubber();var a=o.filter(function(t){return t instanceof ye}),u={};V(n,r)&&a.forEach(function(t){var e=t.currentSnapshot;Object.keys(e).forEach(function(t){return u[t]=e[t]})});var l=function h(t){var e={};return t&&(Array.isArray(t)?t:[t]).forEach(function(t){Object.keys(t).forEach(function(n){"offset"!=n&&"easing"!=n&&(e[n]=t[n])})}),e}(e=G(t,e,u));if(0==n)return new me(t,l);var c="gen_css_kf_"+this._count++,p=this.buildKeyframeElement(t,c,e);document.querySelector("head").appendChild(p);var f=te(t,e),y=new ye(t,e,c,n,r,i,l,f);return y.onDestroy(function(){return function t(e){e.parentNode.removeChild(e)}(p)}),y},t.prototype._notifyFaultyScrubber=function(){this._warningIssued||(console.warn("@angular/animations: please load the web-animations.js polyfill to allow programmatic access...\n","  visit http://bit.ly/IWukam to learn more about using the web-animation-js polyfill."),this._warningIssued=!0)},t}(),ve=function(){function t(t,e,n,r){this.element=t,this.keyframes=e,this.options=n,this._specialStyles=r,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this.time=0,this.parentPlayer=null,this.currentSnapshot={},this._duration=n.duration,this._delay=n.delay||0,this.time=this._duration+this._delay}return t.prototype._onFinish=function(){this._finished||(this._finished=!0,this._onDoneFns.forEach(function(t){return t()}),this._onDoneFns=[])},t.prototype.init=function(){this._buildPlayer(),this._preparePlayerBeforeStart()},t.prototype._buildPlayer=function(){var t=this;if(!this._initialized){this._initialized=!0;var e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:{},this.domPlayer.addEventListener("finish",function(){return t._onFinish()})}},t.prototype._preparePlayerBeforeStart=function(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()},t.prototype._triggerWebAnimation=function(t,e,n){return t.animate(e,n)},t.prototype.onStart=function(t){this._onStartFns.push(t)},t.prototype.onDone=function(t){this._onDoneFns.push(t)},t.prototype.onDestroy=function(t){this._onDestroyFns.push(t)},t.prototype.play=function(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(function(t){return t()}),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()},t.prototype.pause=function(){this.init(),this.domPlayer.pause()},t.prototype.finish=function(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()},t.prototype.reset=function(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1},t.prototype._resetDomPlayerState=function(){this.domPlayer&&this.domPlayer.cancel()},t.prototype.restart=function(){this.reset(),this.play()},t.prototype.hasStarted=function(){return this._started},t.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(function(t){return t()}),this._onDestroyFns=[])},t.prototype.setPosition=function(t){this.domPlayer.currentTime=t*this.time},t.prototype.getPosition=function(){return this.domPlayer.currentTime/this.time},Object.defineProperty(t.prototype,"totalTime",{get:function(){return this._delay+this._duration},enumerable:!0,configurable:!0}),t.prototype.beforeDestroy=function(){var t=this,e={};this.hasStarted()&&Object.keys(this._finalKeyframe).forEach(function(n){"offset"!=n&&(e[n]=t._finished?t._finalKeyframe[n]:J(t.element,n))}),this.currentSnapshot=e},t.prototype.triggerCallback=function(t){var e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(function(t){return t()}),e.length=0},t}(),ge=function(){function t(){this._isNativeImpl=/\{\s*\[native\s+code\]\s*\}/.test(_e().toString()),this._cssKeyframesDriver=new de}return t.prototype.validateStyleProperty=function(t){return w(t)},t.prototype.matchesElement=function(t,e){return P(t,e)},t.prototype.containsElement=function(t,e){return k(t,e)},t.prototype.query=function(t,e,n){return A(t,e,n)},t.prototype.computeStyle=function(t,e,n){return window.getComputedStyle(t)[e]},t.prototype.overrideWebAnimationsSupport=function(t){this._isNativeImpl=t},t.prototype.animate=function(t,e,n,r,i,o,s){if(void 0===o&&(o=[]),!s&&!this._isNativeImpl)return this._cssKeyframesDriver.animate(t,e,n,r,i,o);var a={duration:n,delay:r,fill:0==r?"both":"forwards"};i&&(a.easing=i);var u={},l=o.filter(function(t){return t instanceof ve});V(n,r)&&l.forEach(function(t){var e=t.currentSnapshot;Object.keys(e).forEach(function(t){return u[t]=e[t]})});var h=te(t,e=G(t,e=e.map(function(t){return R(t,!1)}),u));return new ve(t,e,a,h)},t}();function _e(){return function t(){return"undefined"!=typeof window&&void 0!==window.document}()&&Element.prototype.animate||{}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */t.ɵangular_packages_animations_browser_browser_a=ee,t.AnimationDriver=C,t.ɵAnimationDriver=C,t.ɵAnimation=Et,t.ɵAnimationStyleNormalizer=bt,t.ɵNoopAnimationStyleNormalizer=Tt,t.ɵWebAnimationsStyleNormalizer=wt,t.ɵNoopAnimationDriver=O,t.ɵAnimationEngine=Xt,t.ɵCssKeyframesDriver=de,t.ɵCssKeyframesPlayer=ye,t.ɵcontainsElement=k,t.ɵinvokeQuery=A,t.ɵmatchesElement=P,t.ɵvalidateStyleProperty=w,t.ɵWebAnimationsDriver=ge,t.ɵsupportsWebAnimations=function Se(){return"function"==typeof _e()},t.ɵWebAnimationsPlayer=ve,t.ɵallowPreviousPlayerStylesMerge=V,Object.defineProperty(t,"__esModule",{value:!0})});