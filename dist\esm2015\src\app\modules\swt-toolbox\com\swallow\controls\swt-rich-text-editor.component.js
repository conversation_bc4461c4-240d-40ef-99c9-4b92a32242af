/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input } from '@angular/core';
import { SwtTextArea } from "./swt-text-area.component";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtRichTextEditor extends SwtTextArea {
    constructor() {
        super(...arguments);
        this.title = "";
    }
}
SwtRichTextEditor.decorators = [
    { type: Component, args: [{
                selector: 'SwtRichTextEditor',
                template: `
     <div class="tinymce-editor-container">
      <div class="tinymce-editor-header">
          <h6><b>{{ title }}</b></h6>
      </div>
      <div class="tinymce-editor-body">
         <textarea id="{{elementId}}"></textarea>
      </div>
    </div>
    
        
    `,
                styles: [`
  `]
            }] }
];
SwtRichTextEditor.propDecorators = {
    title: [{ type: Input, args: ['title',] }]
};
if (false) {
    /** @type {?} */
    SwtRichTextEditor.prototype.title;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXJpY2gtdGV4dC1lZGl0b3IuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1yaWNoLXRleHQtZWRpdG9yLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBVSxLQUFLLEVBQXVGLE1BQU0sZUFBZSxDQUFDO0FBRzlJLE9BQU8sRUFBRSxXQUFXLEVBQUUsTUFBTSwyQkFBMkIsQ0FBQzs7O01BS2xELENBQUMsR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFDO0FBc0IzQixNQUFNLE9BQU8saUJBQWtCLFNBQVEsV0FBVztJQWpCbEQ7O1FBbUJvQixVQUFLLEdBQVcsRUFBRSxDQUFDO0lBQ3ZDLENBQUM7OztZQXBCQSxTQUFTLFNBQUM7Z0JBQ1AsUUFBUSxFQUFFLG1CQUFtQjtnQkFDN0IsUUFBUSxFQUFFOzs7Ozs7Ozs7OztLQVdUO3lCQUNRO0dBQ1Y7YUFDRjs7O29CQUdJLEtBQUssU0FBQyxPQUFPOzs7O0lBQWQsa0NBQW1DIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29tcG9uZW50LCBPbkluaXQsIElucHV0LCBFbGVtZW50UmVmLCBBZnRlclZpZXdJbml0LCBDb21wb25lbnRGYWN0b3J5UmVzb2x2ZXIsT3V0cHV0ICwgRXZlbnRFbWl0dGVyLCBSZW5kZXJlcjJ9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dFRleHRBcmVhIH0gZnJvbSBcIi4vc3d0LXRleHQtYXJlYS5jb21wb25lbnRcIjtcclxuXHJcbi8qIEltcG9ydCBKUXVlcnkgYW5kIEpRdWVyeSB1aSAqL1xyXG5kZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbi8vaW1wb3J0ICQgZnJvbSAnanF1ZXJ5JztcclxuY29uc3QgJCA9IHJlcXVpcmUoJ2pxdWVyeScpO1xyXG5kZWNsYXJlIHZhciB3aW5kb3c6IGFueTtcclxuZGVjbGFyZSBsZXQgdGlueW1jZTogYW55O1xyXG5cclxuXHJcbkBDb21wb25lbnQoe1xyXG4gICAgc2VsZWN0b3I6ICdTd3RSaWNoVGV4dEVkaXRvcicsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgIDxkaXYgY2xhc3M9XCJ0aW55bWNlLWVkaXRvci1jb250YWluZXJcIj5cclxuICAgICAgPGRpdiBjbGFzcz1cInRpbnltY2UtZWRpdG9yLWhlYWRlclwiPlxyXG4gICAgICAgICAgPGg2PjxiPnt7IHRpdGxlIH19PC9iPjwvaDY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzPVwidGlueW1jZS1lZGl0b3ItYm9keVwiPlxyXG4gICAgICAgICA8dGV4dGFyZWEgaWQ9XCJ7e2VsZW1lbnRJZH19XCI+PC90ZXh0YXJlYT5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICAgIFxyXG4gICAgICAgIFxyXG4gICAgYCxcclxuICAgIHN0eWxlczogW2BcclxuICBgXVxyXG59KVxyXG5leHBvcnQgY2xhc3MgU3d0UmljaFRleHRFZGl0b3IgZXh0ZW5kcyBTd3RUZXh0QXJlYSAgICB7XHJcbiAgXHJcbiAgICBASW5wdXQoJ3RpdGxlJykgdGl0bGU6IHN0cmluZyA9IFwiXCI7XHJcbn1cclxuIl19