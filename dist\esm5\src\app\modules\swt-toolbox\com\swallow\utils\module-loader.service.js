/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from "@angular/core";
import { fromPromise } from "rxjs-compat/observable/fromPromise";
import { CommonService } from "./common.service";
import { ModuleEvent } from "../events/swt-events.module";
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from '../logging/logger.service';
import { parentApplication } from './parent-application.service';
//@dynamic
var ModuleLoader = /** @class */ (function () {
    function ModuleLoader(common) {
        this.common = common;
        this._percentHeight = 100;
        this._percentWidth = 100;
        this._loaded = false;
        this._ready = false;
        this._url = "";
        this._listeners = new Array();
        this.logger = new Logger("ModuleLoader", this.common.httpclient);
        this.overlay = document.createElement('div');
        this.mLoaderContent = document.createElement('div');
    }
    /**
     * @param {?} url
     * @return {?}
     */
    ModuleLoader.prototype.loadModule = /**
     * @param {?} url
     * @return {?}
     */
    function (url) {
        var _this = this;
        this.logger.info("[ loadModule ] method START");
        try {
            // set url.
            this._url = url;
            parentApplication.setParams(url);
            // dispatch progress event each 100 ms.
            this.progress = setInterval((/**
             * @return {?}
             */
            function () {
                _this.dispatchEvent(ModuleEvent.PROGRESS);
            }), 1000);
            // load module.
            this.load(url).subscribe((/**
             * @param {?} component
             * @return {?}
             */
            function (component) {
                _this.componentReference = component.create(_this.common.injector);
                // attach component to current application.
                _this.event = { target: { component: _this.componentReference, url: url } };
                clearInterval(_this.progress);
                _this.dispatchEvent(ModuleEvent.READY, _this.event);
                _this._ready = true;
                _this._loaded = true;
            }), (/**
             * @param {?} error
             * @return {?}
             */
            function (error) {
                _this.dispatchEvent(ModuleEvent.ERROR, error);
                _this.dispose();
                throw new Error(error);
            }));
        }
        catch (error) {
            this.dispose();
            this.dispatchEvent(ModuleEvent.ERROR, error);
            this.logger.error("[ loadModule ] - method ", error);
        }
        this.logger.info("[ loadModule ] method END");
    };
    /**
     * This method is used to initialize module loader attributes
     * in case of error.
     */
    /**
     * This method is used to initialize module loader attributes
     * in case of error.
     * @private
     * @return {?}
     */
    ModuleLoader.prototype.dispose = /**
     * This method is used to initialize module loader attributes
     * in case of error.
     * @private
     * @return {?}
     */
    function () {
        clearInterval(this.progress);
        this._ready = false;
        this._loaded = false;
    };
    /**
     * This method is used to load component instance from its lazy module path.
     * @param url
     */
    /**
     * This method is used to load component instance from its lazy module path.
     * @private
     * @param {?} url
     * @return {?}
     */
    ModuleLoader.prototype.load = /**
     * This method is used to load component instance from its lazy module path.
     * @private
     * @param {?} url
     * @return {?}
     */
    function (url) {
        var _this = this;
        /** @type {?} */
        var _comp_url = url.indexOf("?") ? url.split("?")[0] : url;
        /** @type {?} */
        var manifest = this.common.manifests
            .find((/**
         * @param {?} m
         * @return {?}
         */
        function (m) { return m.path === _comp_url; }));
        /** @type {?} */
        var p = this.common.loader.load(manifest.loadChildren)
            .then((/**
         * @param {?} ngModuleFactory
         * @return {?}
         */
        function (ngModuleFactory) {
            /** @type {?} */
            var moduleRef = ngModuleFactory.create(_this.common.injector);
            // Read from the moduleRef injector and locate the dynamic component type
            /** @type {?} */
            var dynamicComponentType = moduleRef.injector.get(_comp_url);
            // Resolve this component factory
            return moduleRef.componentFactoryResolver.resolveComponentFactory(dynamicComponentType);
        }));
        return fromPromise(p);
    };
    /**
     * This method is used to add event listener to module loader.
     * @param name
     * @param callback
     */
    /**
     * This method is used to add event listener to module loader.
     * @param {?} name
     * @param {?} callback
     * @return {?}
     */
    ModuleLoader.prototype.addEventListener = /**
     * This method is used to add event listener to module loader.
     * @param {?} name
     * @param {?} callback
     * @return {?}
     */
    function (name, callback) {
        this._listeners[name] = callback;
    };
    /**
     *   Unloads the module.
     *  Flash Player and AIR will not fully unload and garbage collect this module if
     *  there are any outstanding references to definitions inside the
     *  module.
     */
    /**
     *   Unloads the module.
     *  Flash Player and AIR will not fully unload and garbage collect this module if
     *  there are any outstanding references to definitions inside the
     *  module.
     * @return {?}
     */
    ModuleLoader.prototype.unload = /**
     *   Unloads the module.
     *  Flash Player and AIR will not fully unload and garbage collect this module if
     *  there are any outstanding references to definitions inside the
     *  module.
     * @return {?}
     */
    function () {
        this.logger.info("unload START");
        try {
            this.componentReference.destroy();
            this.common.applicationRef.detachView(this.componentReference.hostView);
            clearInterval(this.progress);
            this._ready = false;
            this._loaded = false;
            this.dispatchEvent(ModuleEvent.UNLOAD);
        }
        catch (error) {
            this.logger.error("[ unload ] - method error: ", error);
        }
        this.logger.info("unload END");
    };
    /**
     * This method is used to dispatch event with given name.
     * @param name
     */
    /**
     * This method is used to dispatch event with given name.
     * @private
     * @param {?} name
     * @param {?=} args
     * @return {?}
     */
    ModuleLoader.prototype.dispatchEvent = /**
     * This method is used to dispatch event with given name.
     * @private
     * @param {?} name
     * @param {?=} args
     * @return {?}
     */
    function (name, args) {
        for (var lstner in this._listeners) {
            if (lstner === name) {
                this._listeners[lstner](args);
            }
        }
    };
    Object.defineProperty(ModuleLoader.prototype, "percentHeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._percentHeight;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._percentHeight = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ModuleLoader.prototype, "percentWidth", {
        get: /**
         * @return {?}
         */
        function () {
            return this._percentWidth;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._percentWidth = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ModuleLoader.prototype, "loaded", {
        get: /**
         * @return {?}
         */
        function () {
            return this._loaded;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ModuleLoader.prototype, "ready", {
        get: /**
         * @return {?}
         */
        function () {
            return this._ready;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ModuleLoader.prototype, "url", {
        get: /**
         * @return {?}
         */
        function () {
            return this._url;
        },
        enumerable: true,
        configurable: true
    });
    ModuleLoader.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    ModuleLoader.ctorParameters = function () { return [
        { type: CommonService }
    ]; };
    return ModuleLoader;
}());
export { ModuleLoader };
if (false) {
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._percentHeight;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._percentWidth;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._loaded;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._ready;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._url;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype._listeners;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.progress;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.componentReference;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.overlay;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.mLoaderContent;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.event;
    /**
     * @type {?}
     * @private
     */
    ModuleLoader.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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