/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
//import * as $ from "jquery";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
export class ListPickerItemEditor {
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.init();
    }
    /**
     * @return {?}
     */
    init() {
        this.$input = $(`<input type="text" style="width: 70%; "    hideFocus />
                       <SwtButton    id       ="SwtNoteIconButton"
                                    label     ="..."
                                    styleName = "button"
                                    toolTip   ="note"
                                    [enabled]   ="true"
                                    tabindex  ="1"
                                    [onClick]   =""
                                    [onKeyDown] =""
                                    [onFocusOut]="">
     `);
        this.$input.appendTo(this.args.container);
        this.$input.focus();
    }
    /**
     * @return {?}
     */
    destroy() {
        this.$input.remove();
    }
    /**
     * @return {?}
     */
    focus() {
        this.$input.focus();
    }
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.defaultValue = !!item[this.args.column.field];
        if (this.defaultValue) {
            this.$input.prop('checked', true);
        }
        else {
            this.$input.prop('checked', false);
        }
    }
    /**
     * @return {?}
     */
    preClick() {
        this.$input.prop('checked', !this.$input.prop('checked'));
    }
    /**
     * @return {?}
     */
    serializeValue() {
        return this.$input.prop('checked');
    }
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        item[this.args.column.field] = state;
    }
    /**
     * @return {?}
     */
    isValueChanged() {
        return (this.serializeValue() !== this.defaultValue);
    }
    /**
     * @return {?}
     */
    validate() {
        return {
            valid: true,
            msg: null
        };
    }
}
if (false) {
    /** @type {?} */
    ListPickerItemEditor.prototype.$input;
    /** @type {?} */
    ListPickerItemEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    ListPickerItemEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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