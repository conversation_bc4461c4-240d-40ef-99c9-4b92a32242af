/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CustomCell, isClickable, isNegative, isBlink } from "./cellItemRenderUtilities";
/** @type {?} */
export const ImageFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    var datagrid = columnDef.params.grid;
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent');
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let type = columnDef['columnType'] ? String(columnDef['columnType']) : null;
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let blink_me = isBlink(value);
    /** @type {?} */
    let negative = isNegative(dataContext, field);
    /** @type {?} */
    let isLink = isClickable(dataContext, field);
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    /** @type {?} */
    let imageSource = columnDef.properties ? columnDef.properties.imageSource : null;
    //-there is no custom style for the cell
    if (style == "") {
        if (backgroundColor == undefined) {
            backgroundColor = 'transparent';
        }
        style += ' background-color:' + backgroundColor + '; ';
        if (negative) {
            style += 'color: #ff0000; ';
        }
        style += (columnDef.properties ? columnDef.properties.style : '');
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null || field == "expand") {
            value = "";
        }
        value = value.toString().replace('<', '&lt;');
        value = value.toString().replace('>', '&gt;');
        return `<img src="${imageSource}" class="${blink_me == true ? 'blink_me' : ''} ${enabledFlag == false || !datagrid.enabled ? 'imageIsDisabled' : ''}" style="${style}" >
                </img>`;
    }
    return ``;
});
//# sourceMappingURL=data:application/json;base64,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