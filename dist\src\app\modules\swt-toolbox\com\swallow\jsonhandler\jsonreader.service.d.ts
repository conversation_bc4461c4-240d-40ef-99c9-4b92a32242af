import { RootObject } from '../utils/swt-interfaces';
export declare class J<PERSON><PERSON>eader {
    private inputJSON;
    JSONReader(): void;
    setInputJSON(inputJSON: any): void;
    /**
     *
     */
    getInputJSON(): any;
    static compareJSON(inputJSON1: any, inputJSON2: any): boolean;
    static jsonpath(obj: any, path: string): any;
    static jsonpathes(obj: any, paths: string[]): any;
    getScreenAttributes(): any;
    getRequestReplyStatus(): boolean;
    getRequestReplyMessage(): string;
    getRequestReplyLocation(): string;
    isDataBuilding(): Boolean;
    getDateFormat(): string;
    getRefreshRate(): string;
    getTiming(): any;
    getSingletons(): any;
    getColumnData(): any;
    getGridData(): any;
    getBottomGridData(): any;
    getGridMetaData(): any;
    getRowSize(): number;
    getTotalsData(): any;
    getSelects(): any;
    getMaxPage(): string;
    getCurrentPage(): string;
    getTreeData(): any;
    getAllColumnData(): any;
    /**
     * This method is used to get the root of a json response.
     * @param result
     */
    getRoot(result: Object): RootObject;
    getprossesInfoStatus(): string;
    getprossesInfoRunning(): string;
}
