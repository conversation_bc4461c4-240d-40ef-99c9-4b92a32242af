/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as moment_ from 'moment-mini';
import { FieldType, mapMomentDateFormatWithFieldType } from "angular-slickgrid";
import { isClickable } from './cellItemRenderUtilities';
/** @type {?} */
var moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
/** @type {?} */
var FORMAT = mapMomentDateFormatWithFieldType(FieldType.dateIso);
/** @type {?} */
export var dateFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var type = columnDef['columnType'];
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var negative = false;
    /** @type {?} */
    var defaultColor = 'transparent';
    /** @type {?} */
    var dataIndex = dataContext['id'];
    /** @type {?} */
    var color = columnDef.params.rowColorFunction(dataContext, dataIndex, defaultColor, columnDef.field);
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    var blink_me = false;
    /** @type {?} */
    var isLink = isClickable(dataContext, field);
    /** @type {?} */
    var enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    var style = "";
    if (color == undefined) {
        color = defaultColor;
    }
    if (color && color.toString().indexOf('|') > -1) {
        // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
        /** @type {?} */
        var colorList = color.split('|');
        style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
    }
    else {
        style += ' background-color:' + color + (!enableRowSelection ? ' !important; ' : ';');
    }
    //- Return the formatter based on 'negative' value and background row 'color' .
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
        if (typeof (negative) == 'string') {
            if (negative == 'true') {
                negative = true;
            }
            else {
                negative = false;
            }
        }
    }
    if ((String(value)).indexOf('blink') != -1) {
        /** @type {?} */
        var val1 = (value.split(">"))[2];
        /** @type {?} */
        var val2 = val1.split("<");
        value = val2[0];
        blink_me = true;
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null) {
            return "<div class=\"strFormatterDiv " + (blink_me == true ? 'blink_me' : '') + " " + ((( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : '') + "\" style='padding-left: 5px; height: 100%; " + style + " text-align: center; ' ></div>";
        }
        else {
            value = columnDef.params.customContentFunction(dataContext, columnDef.field, value, type);
            if (!negative) {
                return "<div class=\"strFormatterDiv " + (blink_me == true ? 'blink_me' : '') + " " + ((( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : '') + " \" style='padding-left: 5px; height: 100%; position: relative; " + style + " text-align: center;'>" + (isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value) + "</div>";
            }
            else {
                return "<div class=\"strFormatterDiv " + (blink_me == true ? 'blink_me' : '') + " " + ((( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : '') + "\" style=\"padding-left: 5px; height: 100%; position: relative; color: red; " + style + " text-align: center;\">" + (isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value) + "</div>";
            }
        }
    }
    else {
        return "";
    }
    //  return (value && isDateValid) ? moment(value).format(FORMAT) : value;
});
//# sourceMappingURL=data:application/json;base64,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