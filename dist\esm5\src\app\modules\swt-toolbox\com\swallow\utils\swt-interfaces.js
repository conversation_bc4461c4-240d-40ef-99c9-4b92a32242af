/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class define the JSON response typing;
 */
var /**
 * This class define the JSON response typing;
 */
JSONResponse = /** @class */ (function () {
    function JSONResponse() {
        this.request_reply = undefined;
        this.sysitem = undefined;
        this.singletons = undefined;
    }
    return JSONResponse;
}());
/**
 * This class define the JSON response typing;
 */
export { JSONResponse };
if (false) {
    /** @type {?} */
    JSONResponse.prototype.request_reply;
    /** @type {?} */
    JSONResponse.prototype.selects;
    /** @type {?} */
    JSONResponse.prototype.sysitem;
    /** @type {?} */
    JSONResponse.prototype.singletons;
}
/**
 * This class define a root object typing.
 */
var /**
 * This class define a root object typing.
 */
RootObject = /** @class */ (function () {
    function RootObject() {
        this.menus = undefined;
        this.name = undefined;
        this.value = undefined;
    }
    return RootObject;
}());
/**
 * This class define a root object typing.
 */
export { RootObject };
if (false) {
    /** @type {?} */
    RootObject.prototype.menus;
    /** @type {?} */
    RootObject.prototype.name;
    /** @type {?} */
    RootObject.prototype.value;
}
/**
 * This class define the request relay typing.
 */
var /**
 * This class define the request relay typing.
 */
ProcessInfo = /** @class */ (function () {
    function ProcessInfo() {
        this.process_status = undefined;
        this.running_seqnbr = undefined;
    }
    return ProcessInfo;
}());
/**
 * This class define the request relay typing.
 */
export { ProcessInfo };
if (false) {
    /** @type {?} */
    ProcessInfo.prototype.process_status;
    /** @type {?} */
    ProcessInfo.prototype.running_seqnbr;
}
/**
 * This class define the request relay typing.
 */
var /**
 * This class define the request relay typing.
 */
RequestReply = /** @class */ (function () {
    function RequestReply() {
        this.message = undefined;
        this.status_ok = undefined;
    }
    return RequestReply;
}());
/**
 * This class define the request relay typing.
 */
export { RequestReply };
if (false) {
    /** @type {?} */
    RequestReply.prototype.message;
    /** @type {?} */
    RequestReply.prototype.status_ok;
}
/**
 * This class define selects typing.
 */
var /**
 * This class define selects typing.
 */
Selects = /** @class */ (function () {
    function Selects() {
        this.select = undefined;
    }
    return Selects;
}());
/**
 * This class define selects typing.
 */
export { Selects };
if (false) {
    /** @type {?} */
    Selects.prototype.select;
}
/**
 * This class define the Select typing.
 */
var /**
 * This class define the Select typing.
 */
Select = /** @class */ (function () {
    function Select() {
        this.id = undefined;
        this.option = undefined;
    }
    return Select;
}());
/**
 * This class define the Select typing.
 */
export { Select };
if (false) {
    /** @type {?} */
    Select.prototype.id;
    /** @type {?} */
    Select.prototype.option;
}
/**
 * This class define combo box option
 */
var /**
 * This class define combo box option
 */
Option = /** @class */ (function () {
    function Option() {
        this.content = undefined;
        this.selected = undefined;
        this.value = undefined;
        this.type = undefined;
    }
    return Option;
}());
/**
 * This class define combo box option
 */
export { Option };
if (false) {
    /** @type {?} */
    Option.prototype.content;
    /** @type {?} */
    Option.prototype.selected;
    /** @type {?} */
    Option.prototype.value;
    /** @type {?} */
    Option.prototype.type;
}
var Menus = /** @class */ (function () {
    function Menus() {
        this.favourites = undefined;
        this.request_reply = undefined;
        this.alert = undefined;
        this.about = undefined;
        this.singletons = undefined;
        this.modules = undefined;
    }
    return Menus;
}());
export { Menus };
if (false) {
    /** @type {?} */
    Menus.prototype.favourites;
    /** @type {?} */
    Menus.prototype.request_reply;
    /** @type {?} */
    Menus.prototype.alert;
    /** @type {?} */
    Menus.prototype.about;
    /** @type {?} */
    Menus.prototype.singletons;
    /** @type {?} */
    Menus.prototype.modules;
}
var Favourites = /** @class */ (function () {
    function Favourites() {
        this.favourite = undefined;
    }
    return Favourites;
}());
export { Favourites };
if (false) {
    /** @type {?} */
    Favourites.prototype.favourite;
}
var Alert = /** @class */ (function () {
    function Alert() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.programid = undefined;
        this.url = undefined;
    }
    return Alert;
}());
export { Alert };
if (false) {
    /** @type {?} */
    Alert.prototype.itemid;
    /** @type {?} */
    Alert.prototype.menuaccess;
    /** @type {?} */
    Alert.prototype.undock;
    /** @type {?} */
    Alert.prototype.label;
    /** @type {?} */
    Alert.prototype.programid;
    /** @type {?} */
    Alert.prototype.url;
}
var About = /** @class */ (function () {
    function About() {
        this.itemid = undefined;
    }
    return About;
}());
export { About };
if (false) {
    /** @type {?} */
    About.prototype.itemid;
}
var Singletons = /** @class */ (function () {
    function Singletons() {
        this.rolename = undefined;
        this.alertCounter = undefined;
        this.hostid = undefined;
        this.userid = undefined;
    }
    return Singletons;
}());
export { Singletons };
if (false) {
    /** @type {?} */
    Singletons.prototype.rolename;
    /** @type {?} */
    Singletons.prototype.alertCounter;
    /** @type {?} */
    Singletons.prototype.hostid;
    /** @type {?} */
    Singletons.prototype.userid;
}
var Modules = /** @class */ (function () {
    function Modules() {
        this.module = undefined;
    }
    return Modules;
}());
export { Modules };
if (false) {
    /** @type {?} */
    Modules.prototype.module;
}
var Favourite = /** @class */ (function () {
    function Favourite() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.icon = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
    }
    return Favourite;
}());
export { Favourite };
if (false) {
    /** @type {?} */
    Favourite.prototype.itemid;
    /** @type {?} */
    Favourite.prototype.menuaccess;
    /** @type {?} */
    Favourite.prototype.icon;
    /** @type {?} */
    Favourite.prototype.undock;
    /** @type {?} */
    Favourite.prototype.label;
    /** @type {?} */
    Favourite.prototype.menuaction;
    /** @type {?} */
    Favourite.prototype.screenname;
    /** @type {?} */
    Favourite.prototype.moduleid;
    /** @type {?} */
    Favourite.prototype.programid;
}
var Module = /** @class */ (function () {
    function Module() {
        this.icon = undefined;
        this.id = undefined;
        this.label = undefined;
        this.menu = undefined;
    }
    return Module;
}());
export { Module };
if (false) {
    /** @type {?} */
    Module.prototype.icon;
    /** @type {?} */
    Module.prototype.id;
    /** @type {?} */
    Module.prototype.label;
    /** @type {?} */
    Module.prototype.menu;
}
var Menu = /** @class */ (function () {
    function Menu() {
        this.label = undefined;
        this.menuitem = undefined;
    }
    return Menu;
}());
export { Menu };
if (false) {
    /** @type {?} */
    Menu.prototype.label;
    /** @type {?} */
    Menu.prototype.menuitem;
}
var MenuItem = /** @class */ (function () {
    function MenuItem() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.width = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
        this.menuOrderGroup = undefined;
        this.height = undefined;
        this.type = undefined;
        this.menuitem = undefined;
        this.tabIndex = undefined;
    }
    return MenuItem;
}());
export { MenuItem };
if (false) {
    /** @type {?} */
    MenuItem.prototype.itemid;
    /** @type {?} */
    MenuItem.prototype.menuaccess;
    /** @type {?} */
    MenuItem.prototype.width;
    /** @type {?} */
    MenuItem.prototype.undock;
    /** @type {?} */
    MenuItem.prototype.label;
    /** @type {?} */
    MenuItem.prototype.menuaction;
    /** @type {?} */
    MenuItem.prototype.screenname;
    /** @type {?} */
    MenuItem.prototype.moduleid;
    /** @type {?} */
    MenuItem.prototype.programid;
    /** @type {?} */
    MenuItem.prototype.menuOrderGroup;
    /** @type {?} */
    MenuItem.prototype.height;
    /** @type {?} */
    MenuItem.prototype.type;
    /** @type {?} */
    MenuItem.prototype.menuitem;
    /** @type {?} */
    MenuItem.prototype.tabIndex;
}
var TabItem = /** @class */ (function () {
    function TabItem() {
        this.icon = undefined;
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.width = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
        this.menuOrderGroup = undefined;
        this.height = undefined;
        this.type = undefined;
        this.menuitem = undefined;
        this.active = undefined;
        this.disabled = undefined;
        this.tabIndex = undefined;
    }
    return TabItem;
}());
export { TabItem };
if (false) {
    /** @type {?} */
    TabItem.prototype.icon;
    /** @type {?} */
    TabItem.prototype.itemid;
    /** @type {?} */
    TabItem.prototype.menuaccess;
    /** @type {?} */
    TabItem.prototype.width;
    /** @type {?} */
    TabItem.prototype.undock;
    /** @type {?} */
    TabItem.prototype.label;
    /** @type {?} */
    TabItem.prototype.menuaction;
    /** @type {?} */
    TabItem.prototype.screenname;
    /** @type {?} */
    TabItem.prototype.moduleid;
    /** @type {?} */
    TabItem.prototype.programid;
    /** @type {?} */
    TabItem.prototype.menuOrderGroup;
    /** @type {?} */
    TabItem.prototype.height;
    /** @type {?} */
    TabItem.prototype.type;
    /** @type {?} */
    TabItem.prototype.menuitem;
    /** @type {?} */
    TabItem.prototype.active;
    /** @type {?} */
    TabItem.prototype.disabled;
    /** @type {?} */
    TabItem.prototype.tabIndex;
}
var UserData = /** @class */ (function () {
    function UserData() {
        this.currentEntity = undefined;
        this.baseUrl = undefined;
        this.localeDir = undefined;
        this.userid = undefined;
        this.languageid = undefined;
        this.systemLanguage = undefined;
        this.request_reply = undefined;
    }
    return UserData;
}());
export { UserData };
if (false) {
    /** @type {?} */
    UserData.prototype.currentEntity;
    /** @type {?} */
    UserData.prototype.baseUrl;
    /** @type {?} */
    UserData.prototype.localeDir;
    /** @type {?} */
    UserData.prototype.userid;
    /** @type {?} */
    UserData.prototype.languageid;
    /** @type {?} */
    UserData.prototype.systemLanguage;
    /** @type {?} */
    UserData.prototype.request_reply;
}
//# sourceMappingURL=data:application/json;base64,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