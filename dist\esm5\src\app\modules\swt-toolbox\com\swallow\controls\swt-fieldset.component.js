/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input, ViewChild } from "@angular/core";
var SwtFieldSet = /** @class */ (function (_super) {
    tslib_1.__extends(SwtFieldSet, _super);
    //-------constructor-----------------------------------------------------------//
    function SwtFieldSet(elem, _commonService) {
        var _this = _super.call(this, elem, _commonService) || this;
        _this.elem = elem;
        _this._commonService = _commonService;
        _this._legendText = '';
        $($(_this.elem.nativeElement)[0]).attr('selector', 'SwtFieldSet');
        return _this;
    }
    Object.defineProperty(SwtFieldSet.prototype, "legendText", {
        get: /**
         * @return {?}
         */
        function () {
            return this._legendText;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._legendText = value;
            $(this.legendItem.nativeElement).html(this._legendText);
        },
        enumerable: true,
        configurable: true
    });
    SwtFieldSet.decorators = [
        { type: Component, args: [{
                    selector: 'SwtFieldSet',
                    template: "\n    <div class=\"fieldset\"><h1><span #legendItem>Legend</span></h1> \n    <ng-content></ng-content>\n    <ng-container #_container></ng-container>\n    </div>\n\n  ",
                    styles: ["\n             :host {\n               margin: 0px;\n               display: block;\n               margin-top :10px;\n               outline: none;\n             }\n\n             .fieldset {\n              border: 1px solid silver;\n              border-top: none;\n              padding: 0.5em;\n              width: 100%;\n              height : 100%;\n          }\n          .fieldset > h1 {\n              font: 1em normal;\n              margin: -1em -0.5em 0;\n          }   \n          .fieldset > h1 > span {\n              float: left;\n          }\n          .fieldset > h1:before {\n              border-top: 1px solid silver;\n              content: ' ';\n              float: left;\n              margin: 0.5em 2px 0 -1px;\n              width: 0.75em;\n          }\n          .fieldset > h1:after {\n              border-top: 1px solid silver;\n              content: ' ';\n              display: block;\n              left: 2px;\n              margin: 0 1px 0 0;\n              overflow: hidden;\n              position: relative;\n              top: 0.5em;\n          }\n           \n   "]
                }] }
    ];
    /** @nocollapse */
    SwtFieldSet.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtFieldSet.propDecorators = {
        legendItem: [{ type: ViewChild, args: ['legendItem',] }],
        legendText: [{ type: Input }]
    };
    return SwtFieldSet;
}(Container));
export { SwtFieldSet };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtFieldSet.prototype.legendItem;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype._legendText;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtFieldSet.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,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