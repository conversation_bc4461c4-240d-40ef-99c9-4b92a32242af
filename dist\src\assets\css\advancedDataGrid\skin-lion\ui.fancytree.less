 /*!
 * Fancytree "Lion" skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

/*
 Lion colors:
	gray highlight bar: #D4D4D4
	blue highlight-bar and -border #3875D7

*/
// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/

// Override the variable after the import.
// NOTE: Variables are always resolved as the last definition, even if it is
// after where it is used.
@fancy-use-sprites: true;      // false: suppress all background images (i.e. icons)

@fancy-icon-width: 16px;
@fancy-icon-height: 16px;
@fancy-line-height: 16px;
@fancy-icon-spacing: 3px;

// We need to define this variable here (not in skin-common.less) to make it
// work with grunt and webpack:
@fancy-image-prefix: "./skin-lion/";

// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
// @fancy-loading-url: data-uri("@{fancy-image-prefix}loading.gif");
// Set to `true` to use `data-uri(...)` which will embed icons.gif into CSS
// instead of linking to that file:
// @fancy-inline-sprites: true;


/*******************************************************************************
 * Node titles
 */
span.fancytree-title {
	border: 1px solid transparent; // reserve some space for status borders
	border-radius: 0;
}
span.fancytree-focused span.fancytree-title {
	outline: 1px dotted black;
}
span.fancytree-selected span.fancytree-title,
span.fancytree-active span.fancytree-title {
	background-color: #D4D4D4; // gray
}
span.fancytree-selected span.fancytree-title {
	font-style: italic;
}
.fancytree-treefocus span.fancytree-selected span.fancytree-title,
.fancytree-treefocus span.fancytree-active span.fancytree-title {
	color: white;
	background-color: #3875D7; // blue
}

/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table {
	border-collapse: collapse;
	tbody {
	    tr.fancytree-focused {
			background-color: #99DEFD;
		}
	    tr.fancytree-active {
			background-color: royalblue;
		}
	    tr.fancytree-selected {
			background-color: #99DEFD;
		}
	}
}

/*******************************************************************************
 * 'columnview' extension
 */

table.fancytree-ext-columnview tbody tr td {
	border: 1px solid gray;
}
table.fancytree-ext-columnview span.fancytree-node.fancytree-expanded {
	background-color: #ccc;
}
table.fancytree-ext-columnview span.fancytree-node.fancytree-active {
	background-color: royalblue;
}
