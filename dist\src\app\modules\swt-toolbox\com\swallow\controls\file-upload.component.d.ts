import { ElementRef } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { CommonService } from "../utils/common.service";
import { SwtLabel } from "./swt-label.component";
import { SwtProgressBar } from "./progress-bar.component";
import { FileUploader } from 'ng2-file-upload';
import { SwtTextInput } from "./swt-text-input.component";
export declare class FileUpload extends SwtModule {
    private element;
    private commonService;
    id: any;
    value: number;
    progBar: SwtProgressBar;
    lblFomattedSize: SwtLabel;
    toUploadFile: SwtTextInput;
    _refUploadFile: FileUploader;
    hasBaseDropZoneOver: boolean;
    hasAnotherDropZoneOver: boolean;
    uploadCompletedData: Function;
    name: string;
    fileDeleted: boolean;
    private _fileName;
    private logger;
    private jsonReader;
    private lastRecievedXML;
    private prevRecievedXML;
    private inputData;
    private baseURL;
    private actionMethod;
    private actionPath;
    private requestParams;
    private _strUploadUrl;
    private _strDownloadUrl;
    private _strDeleteUrl;
    private _arrUploadFiles;
    private _numCurrentUpload;
    private _fileObj;
    private _validFiles;
    private _arrayFiles;
    private uploadWindow;
    private SwtAlert;
    private fileNumber;
    constructor(element: ElementRef, commonService: CommonService);
    private _validExtensions;
    validExtensions: any[];
    private _valideFilesDesc;
    valideFilesDesc: string;
    private _tableName;
    tableName: string;
    private _columnName;
    columnName: string;
    uploadUrl: string;
    downloadUrl: string;
    deleteUrl: string;
    /**
     * This method called when file upload initialized.
     */
    onLoad(): void;
    /**
     * This method is used to close file upload.
     * @param event
     */
    closeHandler(event: any): void;
    startUpload(parent: any, callback: Function): void;
    fileOverBase(e: any): void;
    fileOverAnother(e: any): void;
    private onSuccessItem;
    private onErrorItem;
    private clearUpload;
    private updateProgBar;
    private formatFileSize;
}
