/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
export class SwtGroupedCommonGrid extends SwtCommonGrid {
    /**
     * @param {?} el
     * @param {?} commonService
     * @param {?} autoTooltipExtension
     * @param {?} extensionUtility
     * @param {?} sharedService
     * @param {?} collectionService
     * @param {?} translate
     */
    constructor(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        super(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate);
        this.el = el;
        this.commonService = commonService;
        this.autoTooltipExtension = autoTooltipExtension;
        this.extensionUtility = extensionUtility;
        this.sharedService = sharedService;
        this.translate = translate;
        this.isGroupedHeaderGrid = true;
    }
}
SwtGroupedCommonGrid.decorators = [
    { type: Component, args: [{
                selector: 'SwtGroupedCommonGrid',
                template: `
    <angular-slickgrid
    class="commonSlickGrid"
            #angularSlickGrid
            gridId='grid-{{id}}'
            (onDataviewCreated)="dataviewReady($event)"
            (onAngularGridCreated)="onAngularGridCreated($event)"
            [columnDefinitions]="columnDefinitions"
            [gridOptions]="gridOptions"
            gridHeight="100%"
            gridWidth="100%"
            [dataset]="dataset"
    >
    </angular-slickgrid>

`,
                providers: [
                    TranslateService,
                    ExtensionService,
                    AutoTooltipExtension,
                    ExtensionUtility,
                    SharedService,
                    CollectionService
                ],
                styles: [`
    .gridContent{
        min-width: 300px;
        height: 100%;
    }
    :host ::ng-deep .gridPane {
        overflow: auto;
        display: block;
    }
    :host ::ng-deep .slickgrid-container {
        min-height: 100%;
    }


`]
            }] }
];
/** @nocollapse */
SwtGroupedCommonGrid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: AutoTooltipExtension },
    { type: ExtensionUtility },
    { type: SharedService },
    { type: CollectionService },
    { type: TranslateService }
];
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,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