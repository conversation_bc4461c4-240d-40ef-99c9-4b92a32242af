/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { TabCloseEvent } from './../events/swt-events.module';
import { Component, Input, ElementRef, ViewChild, Output, EventEmitter, ContentChildren, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
import { TabSelectEvent } from "../events/swt-events.module";
/**
 *
 * Tab component.
 * *
 */
//@dynamic
var Tab = /** @class */ (function (_super) {
    tslib_1.__extends(Tab, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function Tab(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions------------------------------------------------------------------------------------------------
        _this.parent = null;
        _this._active = false;
        _this._visible = true;
        _this._display = false;
        _this._closable = false;
        _this._order = 0;
        return _this;
    }
    Object.defineProperty(Tab.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        //---label----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---label----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._label = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Tab.prototype, "order", {
        get: /**
         * @return {?}
         */
        function () {
            return this._order;
        },
        //---label----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---label----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._order = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Tab.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visible;
        },
        //---visible----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---visible----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._visible = this.adaptValueAsBoolean(value);
            if (!this._visible) {
                this.display = this._visible;
            }
            else if (this.active) {
                this.display = true;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Tab.prototype, "active", {
        get: /**
         * @return {?}
         */
        function () {
            return this._active;
        },
        //---active----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---active----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                if (this.parent) {
                    /** @type {?} */
                    var indexActive = this.parent.tabChildrenArray.findIndex((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return x.active == true; }));
                    if (indexActive >= 0) {
                        this.parent.tabChildrenArray[indexActive].active = false;
                        this.parent.tabChildrenArray[indexActive].display = false;
                    }
                }
                this.display = true;
            }
            this._active = this.adaptValueAsBoolean(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Tab.prototype, "display", {
        get: /**
         * @return {?}
         */
        function () {
            return this._display;
        },
        //---display----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---display----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._display = this.adaptValueAsBoolean(value);
            if (this._display) {
                $(this.elem.nativeElement).show();
            }
            else {
                $(this.elem.nativeElement).hide();
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Tab.prototype, "closable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._closable;
        },
        //---Closable----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---Closable----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._closable = this.adaptValueAsBoolean(value);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    Tab.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        try {
            if (this.id == undefined) {
                throw new Error(">>> PROGRAMMING Error [mandotroy parameter] : you might forgot to add an id the the Tab created from the Html template , something like <... id='tab_id'..>  ");
            }
            else if (this.parent && this.parent.tabChildrenArray.length > 0) {
                // dynamic tabs
                /** @type {?} */
                var indexOfActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.active == true; }));
                if (indexOfActive == -1) {
                    // there is no active tab
                    this.parent.tabChildrenArray[0].active = true;
                    this.parent.tabChildrenArray[0].display = true;
                }
                else if (this.active == true) {
                    // there is already an active tab
                    this.parent.tabChildrenArray.forEach((/**
                     * @param {?} tab
                     * @return {?}
                     */
                    function (tab) {
                        if (tab != this) {
                            tab.active = false;
                            tab.display = false;
                        }
                    }));
                    this.active = true;
                    this.display = true;
                }
                else {
                    this.active = false;
                    this.display = false;
                }
            }
            else if (!this.active) {
                this.display = false;
            }
        }
        catch (error) {
            console.error('method [ngOnInit] - error:', error);
        }
    };
    /**
     * @param {?} e
     * @return {?}
     */
    Tab.prototype.isVisible = /**
     * @param {?} e
     * @return {?}
     */
    function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    };
    /**
     * Sets a style to the tab's header.
     * @param prop
     * @param value
     */
    /**
     * Sets a style to the tab's header.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    Tab.prototype.setTabHeaderStyle = /**
     * Sets a style to the tab's header.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    function (prop, value) {
        var _this = this;
        try {
            setTimeout((/**
             * @return {?}
             */
            function () {
                /** @type {?} */
                var header = $('#header_' + _this.id);
                $(header[0]).attr("style", prop + ":" + value + "!important");
            }), 0);
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    Tab.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this.parent;
            delete this._active;
            delete this._visible;
            delete this._label;
            delete this._display;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    /**
     * setUndockPolicy
     *
     * @param index:int
     *
     * @param value:String
     *
     * Method to set undock policy for tab
     */
    /**
     * setUndockPolicy
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    Tab.prototype.setUndockPolicy = /**
     * setUndockPolicy
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    function (index, value) {
        if (this.id >= index + 1) {
            this.getChildAt(index).undockPolicy = value;
        }
    };
    Tab.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTab',
                    template: "\n        <div  class=\"swttab\" >\n            <ng-content></ng-content>\n             <div  #_container ></div>\n        </div> \n    ",
                    styles: ["\n         :host {\n              margin:  0px;\n              width: 100%;\n              height: 100%;\n              display:block;\n              outline: none;\n         }\n        .swttab {\n              box-sizing: border-box;\n              display: flex;\n              display: -moz-flex;\n              display: -o-flex;\n              display: -webkit-flex;\n              flex-direction: column;\n              width: 100%;\n              height: 100%;\n              outline: none;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    Tab.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    Tab.propDecorators = {
        label: [{ type: Input, args: ["label",] }],
        order: [{ type: Input, args: ["order",] }],
        visible: [{ type: Input, args: ['visible',] }],
        active: [{ type: Input, args: ['active',] }],
        closable: [{ type: Input, args: ["closable",] }]
    };
    return Tab;
}(Container));
export { Tab };
if (false) {
    /** @type {?} */
    Tab.prototype.parent;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._active;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._visible;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._label;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._display;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._closable;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype._order;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    Tab.prototype.commonService;
}
/**
 *
 * Tab component.
 * *
 */
//@dynamic
var TabPushStategy = /** @class */ (function (_super) {
    tslib_1.__extends(TabPushStategy, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function TabPushStategy(elem, commonService, cd) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.cd = cd;
        return _this;
    }
    Object.defineProperty(TabPushStategy.prototype, "active", {
        get: /**
         * @return {?}
         */
        function () {
            return this._active;
        },
        //---active----------------------------------------------------------------------------------------------------------------- //
        set: 
        //---active----------------------------------------------------------------------------------------------------------------- //
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                if (this.parent) {
                    /** @type {?} */
                    var indexActive = this.parent.tabChildrenArray.findIndex((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return x.active == true; }));
                    if (indexActive >= 0) {
                        this.parent.tabChildrenArray[indexActive].active = false;
                        this.parent.tabChildrenArray[indexActive].display = false;
                    }
                }
                this.display = true;
            }
            this._active = this.adaptValueAsBoolean(value);
            if (this._active) {
                if (!((/** @type {?} */ (this.cd))).destroyed) {
                    this.cd.markForCheck();
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    TabPushStategy.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            if (this.id == undefined) {
                throw new Error(">>> PROGRAMMING Error [mandotroy parameter] : you might forgot to add an id the the Tab created from the Html template , something like <... id='tab_id'..>  ");
            }
            else if (this.parent && this.parent.tabChildrenArray.length > 0) {
                // dynamic tabs
                /** @type {?} */
                var indexOfActive = this.parent.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.active == true; }));
                if (indexOfActive == -1) {
                    // there is no active tab
                    this.parent.tabChildrenArray[0].active = true;
                    this.parent.tabChildrenArray[0].display = true;
                }
                else if (this.active == true) {
                    // there is already an active tab
                    this.parent.tabChildrenArray.forEach((/**
                     * @param {?} tab
                     * @return {?}
                     */
                    function (tab) {
                        if (tab != this) {
                            tab.active = false;
                            tab.display = false;
                        }
                    }));
                    this.active = true;
                    this.display = true;
                }
                else {
                    this.active = false;
                    this.display = false;
                }
            }
            else if (!this.active) {
                this.display = false;
            }
            TabSelectEvent.subscribe((/**
             * @param {?} tab
             * @return {?}
             */
            function (tab) {
                if (_this.isVisible(_this.elem.nativeElement)) {
                    if (!((/** @type {?} */ (_this.cd))).destroyed) {
                        _this.cd.markForCheck();
                    }
                }
            }));
        }
        catch (error) {
            console.error('method [ngOnInit] - error:', error);
        }
    };
    TabPushStategy.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTabPushStrategy',
                    template: "\n        <div  class=\"swttab\" >\n            <ng-content></ng-content>\n             <div  #_container ></div>\n        </div> \n    ",
                    providers: [{ provide: Tab, useExisting: TabPushStategy }],
                    changeDetection: ChangeDetectionStrategy.OnPush,
                    styles: ["\n         :host {\n              margin:  0px;\n              width: 100%;\n              height: 100%;\n              display:block;\n              outline: none;\n         }\n        .swttab {\n              box-sizing: border-box;\n              display: flex;\n              display: -moz-flex;\n              display: -o-flex;\n              display: -webkit-flex;\n              flex-direction: column;\n              width: 100%;\n              height: 100%;\n              outline: none;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    TabPushStategy.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: ChangeDetectorRef }
    ]; };
    TabPushStategy.propDecorators = {
        active: [{ type: Input, args: ['active',] }]
    };
    return TabPushStategy;
}(Tab));
export { TabPushStategy };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    TabPushStategy.prototype.cd;
}
//@dynamic
var SwtTabNavigator = /** @class */ (function (_super) {
    tslib_1.__extends(SwtTabNavigator, _super);
    /**
     * constructor
     * @param elem
     * @param commonService
     */
    function SwtTabNavigator(elem, commonService, cdr) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.cdr = cdr;
        //---Properties definitions------------------------------------------------------------------------------------------------
        _this.selectedTabId = 0;
        _this.tabChildrenArray = [];
        _this._selectedIndex = 0;
        _this._selectedLabel = null;
        _this._selectedTab = null;
        _this._borderBottom = true;
        _this._borderTop = true;
        _this._onChange = new Function();
        _this.MOUSE_DOWN = new Function();
        //@Output('MOUSE_DOWN') MOUSE_DOWN: EventEmitter<any> = new EventEmitter<any>();
        _this.aboutActive = true;
        _this.clientWidth = 0;
        _this.scrollWidth = 0;
        _this.scrollValue = 0;
        _this.maxScroll = 0;
        // Make panelTop DOM reference.
        //---Outputs------------------------------------------------------------------------------------------------------
        _this.onChange_ = new EventEmitter();
        _this._showDropDown = false;
        _this._applyOrder = false;
        return _this;
    }
    /**
     * @return {?}
     */
    SwtTabNavigator.prototype.sortTabChildrenArray = /**
     * @return {?}
     */
    function () {
        if (this.applyOrder)
            return this.tabChildrenArray.sort((/**
             * @param {?} a
             * @param {?} b
             * @return {?}
             */
            function (a, b) { return a['order'] > b['order'] ? 1 : a['order'] === b['order'] ? 0 : -1; }));
        else
            return this.tabChildrenArray;
    };
    /**
     * @return {?}
     */
    SwtTabNavigator.prototype.calculateHeigt = /**
     * @return {?}
     */
    function () {
        return this.containerNavigator.nativeElement.offsetHeight - this.swtTabNavigator.nativeElement.offsetHeight;
    };
    Object.defineProperty(SwtTabNavigator.prototype, "tabChildren", {
        //---ContentChildren------------------------------------------------------------------------------------------------
        set: 
        //---ContentChildren------------------------------------------------------------------------------------------------
        /**
         * @param {?} items
         * @return {?}
         */
        function (items) {
            var _this = this;
            items._results.forEach((/**
             * @param {?} tab
             * @return {?}
             */
            function (tab) {
                tab.parent = _this;
            }));
            this.tabChildrenArray = items._results.filter((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.parent.id == _this.id; }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "selectedTab", {
        get: /**
         * @return {?}
         */
        function () {
            return this.tabChildrenArray[this.selectedIndex];
        },
        //---selectedTab--------------------------------------------------------------------------------------------------
        set: 
        //---selectedTab--------------------------------------------------------------------------------------------------
        /**
         * @param {?} tab
         * @return {?}
         */
        function (tab) {
            var _this = this;
            /** @type {?} */
            var index = this.tabChildrenArray.findIndex((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return ((x.id == tab.id) && tab.visible); }));
            if (index >= 0) {
                this.tabChildrenArray[index].active = true;
                if (this.selectedIndex != index) {
                    setTimeout((/**
                     * @return {?}
                     */
                    function () {
                        _this.onChange_.emit();
                        _this._onChange();
                    }), 0);
                }
                this.selectedIndex = index;
                this._selectedTab = tab;
            }
            else {
                throw new Error(">>> PROGRAMMING Error [wrong Tab] : equivalent Tab does not exist in TabNavigator.  ");
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "selectedChild", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedTab;
        },
        //---selectedChild--------------------------------------------------------------------------------------------------
        set: 
        //---selectedChild--------------------------------------------------------------------------------------------------
        /**
         * @param {?} tab
         * @return {?}
         */
        function (tab) {
            /** @type {?} */
            var index = this.tabChildrenArray.findIndex((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.id == tab.id; }));
            if (index >= 0) {
                this.tabChildrenArray[index].active = true;
                this.selectedIndex = index;
                this._selectedTab = tab;
            }
            else {
                throw new Error(">>> PROGRAMMING Error [wrong Tab] : equivalent Tab does not exist in TabNavigator.  ");
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "selectedLabel", {
        get: /**
         * @return {?}
         */
        function () {
            if (this.selectedTab != null) {
                return this.selectedTab.label;
            }
            return undefined;
        },
        //---selectedLabel------------------------------------------------------------------------------------------------
        set: 
        //---selectedLabel------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var index = this.tabChildrenArray.findIndex((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x.label == value; }));
            if (index >= 0) {
                this.tabChildrenArray[index].active = true;
                this._selectedLabel = value;
            }
            else {
                throw new Error(">>> PROGRAMMING Error [wrong Label] : Tab with correspondent Label not exists.  ");
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "onChange", {
        //---onChange-----------------------------------------------------------------------------------------------------
        get: 
        //---onChange-----------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._onChange;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._onChange = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "selectedIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedIndex;
        },
        //---selectedIndex------------------------------------------------------------------------------------------------
        set: 
        //---selectedIndex------------------------------------------------------------------------------------------------
        /**
         * @param {?} index
         * @return {?}
         */
        function (index) {
            this._selectedIndex = index;
            if (this.tabChildrenArray && this.tabChildrenArray[index]) {
                this.tabChildrenArray[index].active = true;
                this._selectedTab = this.tabChildrenArray[index];
                this._selectedLabel = this.tabChildrenArray[index].label;
                this.selectedTabId = this.tabChildrenArray[index].id;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "borderBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._borderBottom;
        },
        //---BorderBottom------------------------------------------------------------------------------------------------
        set: 
        //---BorderBottom------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._borderBottom = this.adaptValueAsBoolean(value);
            if (!this._borderBottom)
                $($($(this.elem.nativeElement).children()[0]).children()[1]).css("border-bottom", 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "showDropDown", {
        get: /**
         * @return {?}
         */
        function () {
            return this._showDropDown;
        },
        //---BorderBottom------------------------------------------------------------------------------------------------
        set: 
        //---BorderBottom------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._showDropDown = this.adaptValueAsBoolean(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "applyOrder", {
        get: /**
         * @return {?}
         */
        function () {
            return this._applyOrder;
        },
        //---BorderBottom------------------------------------------------------------------------------------------------
        set: 
        //---BorderBottom------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._applyOrder = this.adaptValueAsBoolean(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTabNavigator.prototype, "borderTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._borderTop;
        },
        //---BorderTop----------------------------------------------------------------------------------------------------
        set: 
        //---BorderTop----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._borderTop = this.adaptValueAsBoolean(value);
            if (!this._borderTop)
                $($($(this.elem.nativeElement).children()[0]).children()[1]).css("border-top", 0);
        },
        enumerable: true,
        configurable: true
    });
    // ngAfterViewChecked(){
    //     //your code to update the model
    //     setTimeout(() => {
    //         this.cdr.detectChanges();
    //     }, 0);
    //  }
    /**
     * ngOnInit
     */
    // ngAfterViewChecked(){
    //     //your code to update the model
    //     setTimeout(() => {
    //         this.cdr.detectChanges();
    //     }, 0);
    //  }
    /**
     * ngOnInit
     * @return {?}
     */
    SwtTabNavigator.prototype.ngOnInit = 
    // ngAfterViewChecked(){
    //     //your code to update the model
    //     setTimeout(() => {
    //         this.cdr.detectChanges();
    //     }, 0);
    //  }
    /**
     * ngOnInit
     * @return {?}
     */
    function () {
        var _this = this;
        _super.prototype.ngOnInit.call(this);
        this.tabChildrenArray = [];
        if (!this.id)
            this.id = "dynamic-" + Math.random().toString(36).substr(2, 5);
        this.navBarContainer = $($($(this.elem.nativeElement).children()[0]).children()[0]);
        this.navTabs = $(this.navBarContainer).find('.nav-tabs');
        $($($($($(this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('#test-dropdown-btn')).click((/**
         * @return {?}
         */
        function () {
            $($($($(_this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle();
            /** @type {?} */
            var isVisible = $($($($(_this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').is(':visible');
            setTimeout((/**
             * @return {?}
             */
            function () {
                if (isVisible) {
                    $("body").on("click.tabNavigator" + _this.id, (/**
                     * @param {?} e
                     * @return {?}
                     */
                    function (e) {
                        $("body").off("click.tabNavigator" + _this.id);
                        $($($($(_this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle(false);
                    }));
                }
                else {
                    $($($($(_this.elem.nativeElement).children()[0]).children()[0]).find('.btn-group')).find('.dropdown-menu').toggle(true);
                }
            }), 0);
        }));
    };
    /**
     * ngAfterViewInit
     */
    /**
     * ngAfterViewInit
     * @return {?}
     */
    SwtTabNavigator.prototype.ngAfterViewInit = /**
     * ngAfterViewInit
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            /** @type {?} */
            var intervalId_1 = setInterval((/**
             * @return {?}
             */
            function () {
                if (_this.tabChildrenArray && _this.tabChildrenArray.length > 0) {
                    clearInterval(intervalId_1);
                    /** @type {?} */
                    var indexOfActive = _this.tabChildrenArray.findIndex((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return (x.active == true && x.visible); }));
                    if (indexOfActive == -1 && _this.tabChildrenArray.length > 0) {
                        /** @type {?} */
                        var arr = _this.tabChildrenArray.filter((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return x['visible'] == true; }));
                        arr.forEach((/**
                         * @param {?} tab
                         * @return {?}
                         */
                        function (tab) {
                            tab.active = false;
                            tab.display = false;
                        }));
                        if (arr && arr.length > 0)
                            arr[0].active = true;
                    }
                }
            }), 1000);
            this.navBarContainer = $($($(this.elem.nativeElement).children()[0]).children()[0]);
            this.navTabs = $(this.navBarContainer).find('.nav-tabs');
            this.navTabsLi = $(this.navTabs).find('li');
            $(this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
            $(this.navBarContainer).find('.leftArrow').css('width', '0px');
            $(this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
            this.scrollWidth = Number($(this.navTabs)[0].scrollWidth);
            this.checkValue();
        }
        catch (error) {
            console.error('method [ngAfterViewInit] - error:', error);
        }
    };
    /**
     * setSelectedTab : handle active/inactive tab
     * @param event
     * @param label
     */
    /**
     * setSelectedTab : handle active/inactive tab
     * @param {?} tab
     * @return {?}
     */
    SwtTabNavigator.prototype.setSelectedTab = /**
     * setSelectedTab : handle active/inactive tab
     * @param {?} tab
     * @return {?}
     */
    function (tab) {
        try {
            if (tab && tab.id != this.selectedTab.id) {
                this.tabChildrenArray.forEach((/**
                 * @param {?} swttab
                 * @return {?}
                 */
                function (swttab) {
                    if (tab != swttab) {
                        swttab.active = false;
                        swttab.display = false;
                    }
                    else {
                        tab.active = true;
                        tab.display = true;
                    }
                }));
                TabSelectEvent.emit(tab);
                /** @type {?} */
                var index = this.tabChildrenArray.findIndex((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.id == tab.id; }));
                if (this.selectedIndex != index) {
                    this._selectedIndex = index;
                    this._selectedLabel = this.tabChildrenArray[this.selectedIndex].label;
                    this._selectedTab = this.tabChildrenArray[this.selectedIndex];
                    this.onChange_.emit();
                    this._onChange();
                }
                else {
                    this._selectedIndex = index;
                    this._selectedTab = this.tabChildrenArray[this.selectedIndex];
                    this._selectedLabel = this.tabChildrenArray[this.selectedIndex].label;
                }
            }
        }
        catch (error) {
            console.error('method [setSelectedTab] error :', error);
        }
    };
    /**
     * @return {?}
     */
    SwtTabNavigator.prototype.getSelectedTab = /**
     * @return {?}
     */
    function () {
        try {
            if (this.selectedTab) {
                return this.selectedTab;
            }
            return null;
        }
        catch (error) {
            console.error('method [getSelectedTab] error :', error);
        }
    };
    /**
     * addChild : adds a child tab dynamically
     * @param type
     */
    /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    SwtTabNavigator.prototype.addChild = /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    function (type) {
        try {
            /** @type {?} */
            var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
            /** @type {?} */
            var tab = new Tab(this.elem, this.commonService);
            /** @type {?} */
            var componentInstance = this._container.createComponent(comp);
            /* Push the component so that we can keep track of which components are created */
            this.components.push(componentInstance);
            /** @type {?} */
            var id = "dynamic-" + Math.random().toString(36).substr(2, 5);
            $($($(componentInstance.instance)[0])[0].elem.nativeElement).attr('id', id);
            tab = (/** @type {?} */ (componentInstance.instance));
            tab.id = id;
            tab.parent = this;
            tab.display = false;
            this.tabChildrenArray.push(tab);
            this.checkValue();
            return (tab);
        }
        catch (error) {
            console.error('method [addChild] error :', error);
        }
    };
    /**
     * addChild : adds a child tab dynamically
     * @param type
     */
    /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    SwtTabNavigator.prototype.addChildPushStategy = /**
     * addChild : adds a child tab dynamically
     * @param {?} type
     * @return {?}
     */
    function (type) {
        try {
            /** @type {?} */
            var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
            /** @type {?} */
            var tab = new TabPushStategy(this.elem, this.commonService, this.cdr);
            /** @type {?} */
            var componentInstance = this._container.createComponent(comp);
            /* Push the component so that we can keep track of which components are created */
            this.components.push(componentInstance);
            /** @type {?} */
            var id = "dynamic-" + Math.random().toString(36).substr(2, 5);
            $($($(componentInstance.instance)[0])[0].elem.nativeElement).attr('id', id);
            tab = (/** @type {?} */ (componentInstance.instance));
            tab.id = id;
            tab.parent = this;
            tab.display = false;
            this.tabChildrenArray.push(tab);
            this.checkValue();
            return (tab);
        }
        catch (error) {
            console.error('method [addChild] error :', error);
        }
    };
    /**
     * removeChild : removes a tab dynamically
     * @param componentClass
     */
    /**
     * removeChild : removes a tab dynamically
     * @param {?} componentClass
     * @return {?}
     */
    SwtTabNavigator.prototype.removeChild = /**
     * removeChild : removes a tab dynamically
     * @param {?} componentClass
     * @return {?}
     */
    function (componentClass) {
        try {
            /** @type {?} */
            var index = this.tabChildrenArray.findIndex((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return x == componentClass; }));
            // const component = this.components.find((component) => component.instance.id == componentClass.id);
            if (index !== -1) {
                this.tabChildrenArray[index].display = false;
                this.tabChildrenArray[index].active = false;
                //   if(component)
                //     this._container.remove(this._container.indexOf(component));
                _super.prototype.removeChild.call(this, componentClass);
                this.tabChildrenArray.splice(index, 1);
                this.components.splice(index, 1);
            }
            // $("#"+ $.escapeSelector(componentClass.id)).remove();
        }
        catch (error) {
            console.error('method [removeChild] error :', error);
        }
    };
    /**
     * getChildAt : returns the id of a child Tab in a specific index.
     * @param index
     */
    /**
     * getChildAt : returns the id of a child Tab in a specific index.
     * @param {?} index
     * @return {?}
     */
    SwtTabNavigator.prototype.getChildAt = /**
     * getChildAt : returns the id of a child Tab in a specific index.
     * @param {?} index
     * @return {?}
     */
    function (index) {
        try {
            if (index < this.tabChildrenArray.length) {
                return this.tabChildrenArray[index];
            }
            return null;
        }
        catch (error) {
            console.error('method [getChildAt] error :', error);
        }
    };
    /**
     * getTabChildren : returns tabNavigator's children
     */
    /**
     * getTabChildren : returns tabNavigator's children
     * @return {?}
     */
    SwtTabNavigator.prototype.getTabChildren = /**
     * getTabChildren : returns tabNavigator's children
     * @return {?}
     */
    function () {
        return this.tabChildrenArray;
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    SwtTabNavigator.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            delete this._selectedIndex;
            delete this._selectedLabel;
            delete this._selectedTab;
            delete this.selectedTabId;
            delete this._borderBottom;
            delete this._borderTop;
            delete this.tabChildrenArray;
            delete this._onChange;
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    };
    /**
     * setUndockPolicyForTab
     *
     * @param index: number
     *
     * @param value: String
     *
     * Method to set undok policy for tab
     */
    /**
     * setUndockPolicyForTab
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    SwtTabNavigator.prototype.setUndockPolicyForTab = /**
     * setUndockPolicyForTab
     *
     * @param {?} index
     * @param {?} value
     * @return {?}
     */
    function (index, value) {
        /** @type {?} */
        var tab = new Tab(this.elem, this.commonService);
        tab.setUndockPolicy(index, value);
    };
    //-Fix M4869/ISS-008.
    //-Fix M4869/ISS-008.
    /**
     * @param {?} tab
     * @param {?} event
     * @return {?}
     */
    SwtTabNavigator.prototype.onMousedown = 
    //-Fix M4869/ISS-008.
    /**
     * @param {?} tab
     * @param {?} event
     * @return {?}
     */
    function (tab, event) {
        try {
            event['_currentTab'] = this.selectedTab;
            event['_target'] = tab;
            if (tab && this.MOUSE_DOWN.name != 'anonymous') {
                /** @type {?} */
                var result = this.MOUSE_DOWN(event);
                if (result)
                    this.setSelectedTab(tab);
            }
            else {
                this.setSelectedTab(tab);
            }
            this.checkValue();
        }
        catch (error) {
            console.error('method [onMousedown] - error :', error);
        }
    };
    //------------------------------------------------------------------------------------------------------------
    //------------------------------------------------------------------------------------------------------------
    /**
     * @param {?} side
     * @return {?}
     */
    SwtTabNavigator.prototype.scrollTabs = 
    //------------------------------------------------------------------------------------------------------------
    /**
     * @param {?} side
     * @return {?}
     */
    function (side) {
        var _this = this;
        this.dispose();
        this.timer = setInterval((/**
         * @return {?}
         */
        function () {
            if (side === 'right') {
                if (_this.scrollValue + 10 < _this.maxScroll) {
                    _this.scrollValue += 10;
                }
                else {
                    _this.scrollValue = _this.maxScroll;
                }
            }
            else if (side === 'left') {
                if (_this.scrollValue - 10 > 0) {
                    _this.scrollValue -= 10;
                }
                else {
                    _this.scrollValue = 0;
                }
            }
            $($(_this.navTabs)[0]).scrollLeft(_this.scrollValue);
            if (_this.scrollValue == _this.maxScroll) {
                $(_this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                $(_this.navBarContainer).find('.leftArrow').css('width', '20px');
                $(_this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                $(_this.navBarContainer).find('.rightArrow').css('visibility', 'hidden');
                _this.dispose();
            }
            if (_this.scrollValue == 0) {
                $(_this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
                $(_this.navBarContainer).find('.leftArrow').css('width', '0px');
                $(_this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
                $(_this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
                _this.dispose();
            }
            if (_this.scrollValue !== 0 && _this.scrollValue !== _this.maxScroll) {
                $(_this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                $(_this.navBarContainer).find('.leftArrow').css('width', '20px');
                $(_this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                $(_this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
            }
            _this.checkValue();
        }), 20);
    };
    /**
     * @return {?}
     */
    SwtTabNavigator.prototype.dispose = /**
     * @return {?}
     */
    function () {
        clearInterval(this.timer);
    };
    /**
     * @param {?} tab
     * @return {?}
     */
    SwtTabNavigator.prototype.removeContentTab = /**
     * @param {?} tab
     * @return {?}
     */
    function (tab) {
        if (tab.enabled) {
            /** @type {?} */
            var index = this.tabChildrenArray.indexOf(tab);
            this.removeChild(tab);
            if (this.selectedIndex == index) {
                if (this.tabChildrenArray.length > 1) {
                    for (var j = ((index < this.tabChildrenArray.length) ? index : index - 1); j > 0; j--) {
                        if (this.tabChildrenArray[j].enabled)
                            break;
                    }
                    this.selectedTab = this.tabChildrenArray[j];
                    this.onMousedown(this.tabChildrenArray[j], event);
                    event.preventDefault();
                }
            }
            this.checkValue();
            TabCloseEvent.emit(tab.id);
        }
    };
    /**
     * @param {?} tab
     * @return {?}
     */
    SwtTabNavigator.prototype.scrollToTabFromCombo = /**
     * @param {?} tab
     * @return {?}
     */
    function (tab) {
        var _this = this;
        /** @type {?} */
        var offsetLeft = Number($(this.navTabsLi)[this.tabChildrenArray.indexOf(tab)].offsetLeft);
        /** @type {?} */
        var offsetWidth = Number($(this.navTabsLi)[this.tabChildrenArray.indexOf(tab)].offsetWidth);
        /** @type {?} */
        var scrollToTab = (offsetLeft - Number(this.scrollWidth)) + offsetWidth;
        this.scrollValue = scrollToTab;
        this.selectedTab = tab;
        if (this.clientWidth > this.scrollWidth) {
            this.timer = setInterval((/**
             * @return {?}
             */
            function () {
                $($(_this.navTabs)[0]).scrollLeft(_this.scrollValue);
                if (offsetLeft < _this.scrollWidth) {
                    _this.scrollValue = 0;
                    $(_this.navBarContainer).find('.leftArrow').css('visibility', 'hidden');
                    $(_this.navBarContainer).find('.leftArrow').css('width', '0px');
                    $(_this.navBarContainer).find('.leftArrow').css('margin-left', '0px');
                    $(_this.navBarContainer).find('.rightArrow').css('visibility', 'visible');
                    _this.dispose();
                }
                if ((offsetLeft + offsetWidth) == _this.scrollWidth) {
                    $(_this.navBarContainer).find('.leftArrow').css('visibility', 'visible');
                    $(_this.navBarContainer).find('.leftArrow').css('width', '20px');
                    $(_this.navBarContainer).find('.leftArrow').css('margin-left', '2px');
                    $(_this.navBarContainer).find('.rightArrow').css('visibility', 'hidden');
                    _this.dispose();
                }
                if (offsetLeft > 23 && (offsetLeft + offsetWidth) > _this.scrollWidth) {
                    $(_this.navTabs).find('.leftArrow').css('visibility', 'visible');
                    $(_this.navTabs).find('.leftArrow').css('width', '20px');
                    $(_this.navTabs).find('.leftArrow').css('margin-left', '2px');
                    $(_this.navTabs).find('.rightArrow').css('visibility', 'visible');
                    _this.dispose();
                }
            }), 20);
        }
        $($(this.navBarContainer).find('.btn-group')).find('.dropdown-menu').toggle();
    };
    /**
     * @return {?}
     */
    SwtTabNavigator.prototype.checkValue = /**
     * @return {?}
     */
    function () {
        var _this = this;
        setTimeout((/**
         * @return {?}
         */
        function () {
            _this.navBarContainer = $($($(_this.elem.nativeElement).children()[0]).children()[0]);
            _this.navTabs = $(_this.navBarContainer).find('.nav-tabs');
            _this.navTabsLi = $(_this.navTabs).find('li');
            if ($(_this.navTabs)[0] != undefined) {
                /** @type {?} */
                var scrollWidth = Number($(_this.navTabs)[0].scrollWidth);
                if ($(_this.navTabsLi)[_this.tabChildrenArray.length - 1] != undefined) {
                    _this.clientWidth = Number($($(_this.navTabsLi)[_this.tabChildrenArray.length - 1])[0].offsetLeft) +
                        Number($($(_this.navTabsLi)[_this.tabChildrenArray.length - 1])[0].offsetWidth);
                }
                _this.maxScroll = Number($(_this.navTabs)[0].scrollWidth) - Number($(_this.navTabs)[0].clientWidth);
            }
        }), 0);
    };
    SwtTabNavigator.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTabNavigator',
                    template: "\n      <div >\n          <div class=\"nav-bar-container\">\n              <div class=\"btn leftArrow\" (mousedown)=\"scrollTabs('left')\" (mouseup)=\"dispose()\"><span><b>&#9666;</b></span></div>\n              <ul *ngIf=\"aboutActive\" (mouseup)=\"dispose()\"   id=\"swtTabNavigator\" class=\"nav nav-tabs \">\n              <ng-container   *ngFor=\"let tab of sortTabChildrenArray() let act = index\"  >\n                    <li *ngIf=\"tab.visible && tab.label\"  \n                        id='header_{{tab.id}}'\n                        [class.active]=\"tab.active\"\n                        [title]=\"tab.toolTip\" \n                        [class.disabled-container]=\"!tab.enabled\"\n                        class=\"tabNavigator-tabs\"\n                            (click)=\"onMousedown(tab, $event)\"\n                             >\n                            <span style=\"display:flex;\">\n                          {{tab.label}}\n                               <i *ngIf=\"tab.closable\"   class=\"fa fa-times closeFav\" aria-hidden=\"true\" (click)=\"removeContentTab(tab)\"></i>\n                        </span>\n\n                    </li>\n               </ng-container>\n          </ul>\n            \n              <div class=\"btn rightArrow\" [ngStyle]=\"{'visibility': ( tabChildrenArray.length == 0  || clientWidth <  scrollWidth  || scrollValue ==  maxScroll ) ? 'hidden':'visible'}\" (mousedown)=\"scrollTabs('right')\" (mouseup)=\"dispose()\"><span><b>&#9656;</b></span></div>\n                <div class=\"btn-group\"[ngStyle]=\"{'visibility': tabChildrenArray.length > 0 && showDropDown ? 'visible':'hidden'}\" >\n                <a id=\"test-dropdown-btn\" class=\"input-group-addon dropdownBtn dropdown-toggle\"  >\n                        <i _ngcontent-c8=\"\" class=\"glyphicon glyphicon-triangle-bottom\"></i>\n                    </a>\n                    <ul  class=\"dropdown-menu\" role=\"menu\"   >\n                        <li role=\"menuitem\" *ngFor=\"let tab of tabChildrenArray\">\n                            <a class=\"dropdown-item\" (click)=\"scrollToTabFromCombo(tab)\">\n                                <b>{{ tab.label }}</b>\n                            </a>\n                        </li>\n                  </ul> \n                  \n                </div>\n\n          </div>\n          <div  class=\"tabNavigator-content\" style=\"width:100%; height:calc(100% - 20px);\">\n              <ng-content></ng-content>\n              <div #_container ></div>\n          </div>\n    </div>\n  ",
                    styles: ["\n         :host {\n               display: block;\n               margin: 0px 0px 5px 0px;\n               width: 100%;\n               height: 100%;\n               outline: none;\n         }\n       .dropdownBtn{\n           height: 20px;\n           width: 20px;\n           border: 1px solid #9C9FA1;\n           padding: 0px;\n           cursor: default;\n           margin-top: 1px;\n           border-radius: 2px;\n           text-decoration: none;\n           color: black;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff );\n       }\n       .leftArrow, .rightArrow {\n           height: 20px;\n           width: 20px;\n           border: 1px solid #9C9FA1;\n           padding: 0px; \n           cursor: default;\n           margin-top: 1px;\n           border-radius: 2px;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff);\n       }\n       .leftArrow:hover, .rightArrow:hover {\n           border: 1px solid #009DFF;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #EEEEEE);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #EEEEEE);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #EEEEEE);\n           background-image: -o-linear-gradient(top,#FFFFFF, #EEEEEE);\n           background-image: linear-gradient(to bottom,#FFFFFF, #EEEEEE);\n       }\n       .leftArrow>span, .rightArrow>span {\n           font-size: 14px;\n       }\n       .nav-bar-container{\n           width:100%;\n           display: flex;\n           margin: 0px 0px 0px -2px !important;\n           display:-ms-flexbox;\n           -ms-flex-wrap: nowrap;\n       }\n       \n       .icon {\n           width: 15px;\n           margin-top: -3px;\n       }\n       .nav {\n           padding: 0px;\n           height: 20px;\n           overflow-x: auto;\n           overflow-y: hidden;\n           margin-top: 1px;\n           flex-grow:1;\n           width: max-content;\n       }\n       /* hide scroll bar and keep div scrollable */\n       .nav::-webkit-scrollbar {\n           width: 0px;\n       }\n       /* width */\n       .nav::-webkit-scrollbar:horizontal {\n           height: 0px;\n       }\n        \n       .nav-tabs{\n            padding: 0px;\n            height: 20px;\n            overflow-x: auto;\n            overflow-y: hidden;\n            margin-top: 1px;\n            -ms-flex-positive: 1;\n            flex-grow: 1;\n            display:flex;\n            width: max-content;\n       }\n\n       .closeFav{\n            color: #EB5946;\n            border-radius: 2px;\n            font-size: 12px;\n            padding: 1px;\n            padding-top: 0px;\n            position: relative;\n            left: 6px;\n            top: 1px;\n       } \n       .btn-group> ul {\n           margin-top: -1px;\n           margin-left: -139px;\n           width: -webkit-stretch;\n       }\n       .btn-group> ul> li {\n           font-size: 11px;\n           font-weight: bold;\n       }\n       .btn-group> ul> li> a:hover {\n           background-color: #b2e1ff !important;\n           cursor: default;\n       }\n\n\n\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtTabNavigator.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: ChangeDetectorRef }
    ]; };
    SwtTabNavigator.propDecorators = {
        containerNavigator: [{ type: ViewChild, args: ["containerNavigator",] }],
        swtTabNavigator: [{ type: ViewChild, args: ["swtTabNavigator",] }],
        onChange_: [{ type: Output, args: ['onChange',] }],
        tabChildren: [{ type: ContentChildren, args: [Tab,] }],
        borderBottom: [{ type: Input, args: ['borderBottom',] }],
        showDropDown: [{ type: Input, args: ['showDropDown',] }],
        applyOrder: [{ type: Input, args: ['applyOrder',] }],
        borderTop: [{ type: Input, args: ['borderTop',] }]
    };
    return SwtTabNavigator;
}(Container));
export { SwtTabNavigator };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.selectedTabId;
    /** @type {?} */
    SwtTabNavigator.prototype.tabChildrenArray;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._selectedTab;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._borderBottom;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._borderTop;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype._onChange;
    /** @type {?} */
    SwtTabNavigator.prototype.MOUSE_DOWN;
    /** @type {?} */
    SwtTabNavigator.prototype.aboutActive;
    /** @type {?} */
    SwtTabNavigator.prototype.clientWidth;
    /** @type {?} */
    SwtTabNavigator.prototype.scrollWidth;
    /** @type {?} */
    SwtTabNavigator.prototype.timer;
    /** @type {?} */
    SwtTabNavigator.prototype.scrollValue;
    /** @type {?} */
    SwtTabNavigator.prototype.maxScroll;
    /** @type {?} */
    SwtTabNavigator.prototype.navBarContainer;
    /** @type {?} */
    SwtTabNavigator.prototype.navTabs;
    /** @type {?} */
    SwtTabNavigator.prototype.navTabsLi;
    /** @type {?} */
    SwtTabNavigator.prototype.containerNavigator;
    /** @type {?} */
    SwtTabNavigator.prototype.swtTabNavigator;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.onChange_;
    /** @type {?} */
    SwtTabNavigator.prototype._showDropDown;
    /** @type {?} */
    SwtTabNavigator.prototype._applyOrder;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtTabNavigator.prototype.cdr;
}
//# sourceMappingURL=data:application/json;base64,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