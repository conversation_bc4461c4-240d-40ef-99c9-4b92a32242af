/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Types } from "./types";
var NumberItemRender = /** @class */ (function (_super) {
    tslib_1.__extends(NumberItemRender, _super);
    function NumberItemRender(numberelement, common) {
        var _this = _super.call(this, numberelement.nativeElement, common) || this;
        _this.numberelement = numberelement;
        _this.common = common;
        _this.text = "";
        _this.color = "";
        _this.type = Types.NUM;
        _this.id = 0;
        return _this;
    }
    /**
     * @return {?}
     */
    NumberItemRender.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    NumberItemRender.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        if (this.text) {
            if (this.text.toString().indexOf("-") !== -1 && this.text.indexOf("-") === 0) {
                // this.color = "red";
                this.setStyle("color", "red");
            }
        }
    };
    NumberItemRender.decorators = [
        { type: Component, args: [{
                    selector: 'NumberItemRender',
                    template: "\n        <span class=\"number-item-render\">\n            {{ text }}\n        </span>\n    ",
                    styles: ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .number-item-render {\n            display: block;\n            width: 100%;\n            text-align: right;\n            padding: 0 5px;\n            margin: 0px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    NumberItemRender.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return NumberItemRender;
}(UIComponent));
export { NumberItemRender };
if (false) {
    /** @type {?} */
    NumberItemRender.prototype.text;
    /** @type {?} */
    NumberItemRender.prototype.color;
    /** @type {?} */
    NumberItemRender.prototype.type;
    /** @type {?} */
    NumberItemRender.prototype.id;
    /**
     * @type {?}
     * @private
     */
    NumberItemRender.prototype.numberelement;
    /**
     * @type {?}
     * @private
     */
    NumberItemRender.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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