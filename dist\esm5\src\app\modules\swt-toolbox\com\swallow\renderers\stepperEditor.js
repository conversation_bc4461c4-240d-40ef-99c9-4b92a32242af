/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
var 
//@dynamic
stepperEditor = /** @class */ (function () {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    function stepperEditor(args) {
        this.args = args;
        this.mouseDownFlag = false;
        this.mouseUpFlag = false;
        this.logger = null;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.logger = new Logger('stepperEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.init = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.loadValue(this.args.item);
        this.logger.info('Method [stepperEditor] init - START- ');
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting 
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        if (this.showHideCells) {
            this.$input = $("<input class=\"main-input \" " + (this.enableFlag ? '' : 'disabled') + " style=\"text-align: right; padding-right: 2px!important; width:50px;height: 98%; background-color: white!important;\" type=\"number\" min=\"0\" max=\"2000\" step=\"1\" value =\"" + this.defaultValue + "\"/>\n                  <span class=\"stepper-up-container renderAsInput " + (this.enableFlag ? '' : 'disabled-container') + "\" id=\"up\"    ><span  class=\" render-stepper-arrow-up\" ></span></span>\n                  <span class=\"stepper-down-container renderAsInput " + (this.enableFlag ? '' : 'disabled-container') + "\" id=\"down\" ><span  class=\"  render-stepper-arrow-down\" ></span></span>\n          ");
            this.$input.appendTo(this.args.container);
            this.$input.focus();
            if (this.enableFlag) {
                //focus 
                $(this.$input[2]).css('background-color', '#EED299');
                $(this.$input[4]).css('background-color', '#EED299');
                $(this.$input[0]).css('outline', '1px solid #49B9FF');
                $(this.$input[2]).css('box-shadow', '0 0 3px #49B9FF');
                $(this.$input[4]).css('box-shadow', '0 0 3px #49B9FF');
                /** @type {?} */
                var _this = this;
                /** @type {?} */
                var grid = $(this.args.container.parentElement.parentElement);
                /** @type {?} */
                var parent = $(this.args.container.parentElement);
                // - Stepper Up click
                $(document).ready((/**
                 * @return {?}
                 */
                function () {
                    // - Stepper UP .
                    $(parent).on('mousedown', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = true;
                        $(_this.$input[2]).css('background-color', '#EDEDED');
                    }));
                    $(parent)
                        .on('mouseup', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = false;
                        $(_this.$input[2]).css('background-color', '#F7E2B5');
                    }))
                        .on('mouseleave', '.stepper-up-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseUpFlag = false;
                        $(_this.$input[2]).css('background-color', '#F7E2B5');
                    }));
                    /** @type {?} */
                    var checkUpExist = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.mouseUpFlag) {
                            /** @type {?} */
                            var val = _this.getValue();
                            if (val >= 0) {
                                val++;
                                _this.setValue(String(val));
                            }
                        }
                    }), 100);
                    // - Stepper DOWN .
                    $(parent).on('mousedown', '.stepper-down-container', (/**
                     * @param {?} e
                     * @return {?}
                     */
                    function (e) {
                        _this.mouseDownFlag = true;
                        $(_this.$input[4]).css('background-color', '#EDEDED');
                    }));
                    $(parent)
                        .on('mouseup', '.stepper-down-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseDownFlag = false;
                        $(_this.$input[4]).css('background-color', '#F7E2B5');
                    }))
                        .on('mouseleave', '.stepper-down-container', (/**
                     * @return {?}
                     */
                    function () {
                        _this.mouseDownFlag = false;
                        $(_this.$input[4]).css('background-color', '#F7E2B5');
                    }));
                    /** @type {?} */
                    var checkDownExist = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.mouseDownFlag) {
                            /** @type {?} */
                            var val = _this.getValue();
                            if (val > 0) {
                                val--;
                                _this.setValue(String(val));
                            }
                        }
                    }), 100);
                    $(parent).on('focusout', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.setValue(String(_this.getValue()));
                        event.stopPropagation();
                    }));
                }));
            }
        }
        else {
            this.$input = $("");
        }
        this.logger.info('Method [stepperEditor] init - END - ');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.destroy = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [destroy] init - START - ');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.logger.info('Method [destroy] init - END - ');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.focus = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [focus] - START/END');
        this.$input.focus();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.getValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        return Number(this.$input.val());
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    stepperEditor.prototype.setValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    function (val) {
        var _this_1 = this;
        this.logger.info('method [setValue] -START-');
        if (this.showHideCells && this.enableFlag) {
            this.$input.val(val);
            this.applyValue(this.args.item, val);
            /*console.log('this.commonGrid.changes.size() = ',this.commonGrid.changes.size() )
            console.log('this.this.defaultValue = ',this.defaultValue )
            console.log('this.originalDefaultValue = ',this.originalDefaultValue )
            console.log('this.args.item[this.args.column.field] =',this.args.item[this.args.column.field])*/
            // -- check if changed
            this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
             * @param {?} x
             * @return {?}
             */
            function (x) { return ((x.crud_data.id == _this_1.args.item.id)); }));
            if (this.CRUD_CHANGES_DATA) {
                this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : null;
            }
            if ((this.originalDefaultValue == null && this.defaultValue != this.args.item[this.args.column.field]) || ((this.originalDefaultValue != null) && (this.originalDefaultValue != this.args.item[this.args.column.field]))) {
                /** @type {?} */
                var thereIsInsert = false;
                if (this.commonGrid.changes.size() > 0) {
                    /** @type {?} */
                    var crudInsert = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return ((x.crud_data.id == _this_1.args.item.id) && (x.crud_operation == "I")); }));
                    if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                        thereIsInsert = true;
                }
                if (!thereIsInsert) {
                    //console.log('is changed so execute item changed function' )
                    /** @type {?} */
                    var original_row = [];
                    for (var key in this.args.item) {
                        if (key != 'slickgrid_rowcontent') {
                            original_row[key] = this.args.item[key];
                        }
                        else {
                            break;
                        }
                    }
                    original_row['slickgrid_rowcontent'] = {};
                    for (var key in this.args.item['slickgrid_rowcontent']) {
                        original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, this.args.item['slickgrid_rowcontent'][key]);
                    }
                    original_row[this.args.column.field] = this.defaultValue;
                    original_row['slickgrid_rowcontent'][this.args.column.field] = { content: this.defaultValue };
                    /** @type {?} */
                    var updatedObject = {
                        rowIndex: this.args.item.id,
                        columnIndex: this.args.column.columnorder,
                        new_row: this.args.item,
                        original_row: original_row,
                        changedColumn: this.args.column.field,
                        oldValue: this.defaultValue,
                        newValue: this.getValue()
                    };
                    this.commonGrid.spyChanges({ field: this.args.column.field });
                    this.commonGrid.updateCrud(updatedObject);
                    this.commonGrid.columnSelectChanged(this.args.item);
                    SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                }
            }
            else if ((this.originalDefaultValue == this.args.item[this.args.column.field])) {
                /** @type {?} */
                var crudChange = this.commonGrid.changes.getValues().find((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return ((x.crud_data.id == _this_1.args.item.id)); }));
                if (crudChange) {
                    /** @type {?} */
                    var ch = String("U(" + this.args.column.field + ")");
                    if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                    }
                    else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                    }
                    else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                    }
                    if (crudChange['crud_operation'] == "") {
                        /** @type {?} */
                        var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return ((x.crud_data.id == _this_1.args.item.id)); }));
                        this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                    }
                }
                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                if (this.commonGrid.changes.size() == 0)
                    this.commonGrid.spyNoChanges({ field: this.args.column.field });
                SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
            }
        }
        this.logger.debug('method [setValue] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    stepperEditor.prototype.loadValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('Method [loadValue] - START/END -');
        this.defaultValue = item[this.args.column.field] || '';
        this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field];
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.save = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [save] - START/END -');
        if (this.showHideCells)
            this.args.commitChanges();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.serializeValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [serializeValue] - START/END - ');
        return this.$input.val();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    stepperEditor.prototype.applyValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        this.logger.info('method [applyValue] - START/END -');
        if (this.showHideCells && this.enableFlag) {
            item[this.args.column.field] = state;
            item.slickgrid_rowcontent[this.args.column.field] = { content: state };
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.isValueChanged = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [isValueChanged] - START/END -');
        if (this.showHideCells && this.enableFlag) {
            /** @type {?} */
            var isChanged = (!(this.$input.val() === '' && this.defaultValue == null)) && (this.$input.val() !== this.defaultValue);
            return isChanged;
        }
        else {
            return false;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    stepperEditor.prototype.validate = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('method [validate] - START/END -');
        return {
            valid: true,
            msg: null
        };
    };
    return stepperEditor;
}());
//@dynamic
export { stepperEditor };
if (false) {
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.mouseDownFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.mouseUpFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.logger;
    /** @type {?} */
    stepperEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    stepperEditor.prototype.CRUD_DATA;
    /** @type {?} */
    stepperEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    stepperEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    stepperEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    stepperEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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