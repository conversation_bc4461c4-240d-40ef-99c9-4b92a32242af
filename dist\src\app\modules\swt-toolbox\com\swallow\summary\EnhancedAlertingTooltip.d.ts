import { SwtButton } from './../controls/swt-button.component';
import { HTTPComms } from './../communication/httpcomms.service';
import { JSONReader } from './../jsonhandler/jsonreader.service';
import { CommonService } from './../utils/common.service';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from './../containers/swt-container.component';
import { OnInit, ElementRef, AfterViewInit, EventEmitter } from '@angular/core';
import { SwtLabel } from '../controls/swt-label.component';
export declare class EnhancedAlertingTooltip extends Container implements OnInit, AfterViewInit {
    private elem;
    private commonService;
    customTooltip: VBox;
    tree: CustomTree;
    treeTitle: SwtLabel;
    facilityName: SwtLabel;
    paramsList: SwtLabel;
    closeButton: SwtButton;
    linkToSpecificButton: SwtButton;
    displayListButton: SwtButton;
    ITEM_CLICK: EventEmitter<any>;
    DISPLAY_LIST_CLICK: EventEmitter<any>;
    LINK_TO_SPECIF_CLICK: EventEmitter<any>;
    dataArray: any;
    parentDocument: any;
    private swtAlert;
    private clickable;
    processBox: any;
    jsonReader: JSONReader;
    lastRecievedJSON: any;
    prevRecievedJSON: any;
    initReceivedJSON: any;
    /**
      * Communication Objects
      **/
    inputData: HTTPComms;
    baseURL: string;
    private actionMethod;
    private actionPath;
    private requestParams;
    private _invalidComms;
    private hostId;
    ngOnInit(): void;
    closeTooltip(): void;
    displayListEventhandler(): void;
    getParamsList(item: any, paramsList: any): any;
    linkToSpecifiEventHandler(): void;
    ngAfterViewInit(): void;
    treeEventHandler(item: any): void;
    extractParentFilterDataFromNode(parentData: any, dataArray: any): any;
    constructor(elem: ElementRef, commonService: CommonService);
    private boxRollOutEventListner;
    private createCustomTip;
    inputDataResult(event: any): void;
    htmlEntities(str: any): string;
    startOfComms(): void;
    /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     */
    endOfComms(): void;
    /**
     * If a fault occurs with the connection with the server then display the lost connection label
     * @param event:FaultEvent
     **/
    private inputDataFault;
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     */
    recalculateDataAlert(event: any): void;
    /**
     * This function is used to listen to the alert
     *
     * @param eventObj CloseEvent
     */
    private alertListener;
}
