/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CommonService } from '../utils/common.service';
import { Container } from "../containers/swt-container.component";
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Component, ViewChild, ElementRef, Input } from "@angular/core";
export class SwtTextInput extends Container {
    /**
     * constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._required = false;
        if (this.constructor.name.toUpperCase() == "SWTTEXTINPUT") {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtTextInput');
        }
        else {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtNumericInput');
        }
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set required(value) {
        if (typeof (value) == "string") {
            if (value === 'true') {
                this._required = true;
            }
            else {
                this._required = false;
            }
        }
        else {
            this._required = value;
        }
    }
    /**
     * @return {?}
     */
    get required() {
        return this._required;
    }
    //---tabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set textAlign(value) {
        try {
            this._textAlign = String(value);
            if (value) {
                this.textfieldDOM.nativeElement.style.textAlign = value;
            }
        }
        catch (error) {
            console.error('method [ set tabIndex] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get textAlign() {
        return this._textAlign;
    }
    //---tabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set tabIndex(value) {
        try {
            this._tabIndex = String(value);
            if (this.textfieldDOM)
                $($(this.textfieldDOM)[0].nativeElement).attr("tabindex", this._tabIndex);
        }
        catch (error) {
            console.error('method [ set tabIndex] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get tabIndex() {
        return this._tabIndex;
    }
    //---styleName-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set styleName(value) {
        try {
            if (this.textfieldDOM)
                $($(this.textfieldDOM)[0].nativeElement).addClass(value);
        }
        catch (error) {
            console.error('method [ set styleName] - error:', error);
        }
    }
    //---displayAsPassword-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set displayAsPassword(value) {
        this._displayAsPassword = this.adaptValueAsBoolean(value);
        if (this._displayAsPassword) {
            if (this.textfieldDOM)
                $($(this.textfieldDOM)[0].nativeElement).attr('type', 'password');
        }
        else {
            if (this.textfieldDOM)
                $($(this.textfieldDOM)[0].nativeElement).attr('type', 'text');
        }
    }
    /**
     * @return {?}
     */
    get displayAsPassword() {
        return this._displayAsPassword;
    }
    //---editable-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        this._editable = this.adaptValueAsBoolean(value);
        if (this.textfieldDOM)
            $($(this.textfieldDOM)[0].nativeElement).prop('readonly', !this._editable);
    }
    /**
     * @return {?}
     */
    get editable() {
        return this._editable;
    }
    //---text-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        value = value.toString();
        this._text = !value ? "" : value;
        if (this.firstCall) {
            this.originalValue = value;
            this.firstCall = false;
        }
        else {
            this._spyChanges(this._text);
        }
        if (this._text != undefined && this._text != null && $(this.textfieldDOM)) {
            $($(this.textfieldDOM)[0].nativeElement).val(this.validateMaxChar(this.validateRestrict(this._text)));
        }
    }
    /**
     * @return {?}
     */
    get text() {
        return $($(this.textfieldDOM)[0].nativeElement).val();
    }
    //---dataProvider-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this._dataProvider = value;
            if (value.length > 0) {
                $($(this.textfieldDOM)[0].nativeElement).autocomplete({
                    source: value
                });
            }
        }), 0);
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        if (!this._text) {
            //-Set text to empty if there is no entered value, to detect spyChanges.
            this.text = "";
        }
        this.addEventListener('input', (/**
         * @param {?} e
         * @return {?}
         */
        (e) => {
            this.change(event);
            this.change_.emit(event);
        }));
    }
    /**
     * onPaste
     * @param {?} event
     * @return {?}
     */
    onPaste(event) {
        this.change_.emit(event);
    }
    /**
     * focus
     * @return {?}
     */
    setFocusAndSelect() {
        setTimeout((/**
         * @return {?}
         */
        () => {
            $($(this.elem.nativeElement.children['0'])[0]).select();
            // $( $( this.elem.nativeElement.children['0'] )[0] ).focus();
        }), 0);
    }
}
SwtTextInput.decorators = [
    { type: Component, args: [{
                selector: 'SwtTextInput',
                template: `
            <input #textfield
            popper="{{this.toolTipPreviousValue}}"
            [popperTrigger]="'hover'"
            [popperDisabled]="toolTipPreviousValue === null ? true : false"
            [popperPlacement]="'bottom'"
            [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}"
               (paste)="onPaste($event)"
               class="textinput"
               [class.requiredInput]= "this.required==true && !this.text"/>
    `,
                styles: [`
        :host {
             outline:none;
        }
        .textArea {
            position: fixed;
            z-index: 4;
        }
        input::placeholder {
            color: transparent;
       }
        input {
            border: 1px solid #7f9db9;
            width: 100%;
            font-size: 11px;
            height: 23px;
            line-height:23px;
            cursor: text;
            color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/
        }

        input:disabled {
            color: gray;
            height: 23px;
        }

        input:focus {
            border: 1px solid #7F9DB9;
            outline-style: solid;
            outline-width: 1px;
            outline-color: #49B9FF;
        }

        .hideInput {
            visibility: hidden;
        }

        .textinput {
            padding: 0 5px;
        }
    `]
            }] }
];
/** @nocollapse */
SwtTextInput.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtTextInput.propDecorators = {
    textfieldDOM: [{ type: ViewChild, args: ['textfield', { read: ElementRef },] }],
    required: [{ type: Input, args: ['required',] }],
    textAlign: [{ type: Input, args: ['textAlign',] }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    styleName: [{ type: Input, args: ['styleName',] }],
    displayAsPassword: [{ type: Input }],
    editable: [{ type: Input }],
    text: [{ type: Input }],
    dataProvider: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtTextInput.prototype.textfieldDOM;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._displayAsPassword;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._required;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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