/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ViewChild } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class ContextMenu {
    constructor() {
        this.components = [];
        this.customItems = [];
    }
    /**
     * @return {?}
     */
    hideBuiltInItems() {
    }
}
ContextMenu.decorators = [
    { type: Component, args: [{
                selector: 'ContextMenu',
                template: `
  
  `
            }] }
];
/** @nocollapse */
ContextMenu.ctorParameters = () => [];
if (false) {
    /** @type {?} */
    ContextMenu.prototype.components;
    /** @type {?} */
    ContextMenu.prototype.customItems;
}
export class ContextMenuItem {
    /**
     * @param {?} label
     * @param {?=} var1
     * @param {?=} var2
     * @param {?=} var3
     */
    constructor(label, var1, var2, var3) {
        this.var1 = var1;
        this.var2 = var2;
        this.var3 = var3;
        this._MenuItemSelect = new Function();
        this.label = label;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set MenuItemSelect(value) {
        this._MenuItemSelect = value;
    }
    /**
     * @return {?}
     */
    get MenuItemSelect() {
        return this._MenuItemSelect;
    }
}
ContextMenuItem.propDecorators = {
    itemDOM: [{ type: ViewChild, args: ['item',] }],
    MenuItemSelect: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    ContextMenuItem.prototype.itemDOM;
    /** @type {?} */
    ContextMenuItem.prototype.label;
    /**
     * @type {?}
     * @private
     */
    ContextMenuItem.prototype._MenuItemSelect;
    /**
     * @type {?}
     * @private
     */
    ContextMenuItem.prototype.var1;
    /**
     * @type {?}
     * @private
     */
    ContextMenuItem.prototype.var2;
    /**
     * @type {?}
     * @private
     */
    ContextMenuItem.prototype.var3;
}
//# sourceMappingURL=data:application/json;base64,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