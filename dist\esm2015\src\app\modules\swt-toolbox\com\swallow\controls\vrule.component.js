/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
const $ = require('jquery');
export class VRule extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions----------------------------------------------------------------------------------------
        this._strokeColor = "";
        this._shadowColor = "";
        this._themeColor = "";
    }
    //----strokeColor--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set strokeColor(value) {
    }
    /**
     * @return {?}
     */
    get strokeColor() {
        return this._strokeColor;
    }
    //----shadowColor--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set shadowColor(value) {
    }
    /**
     * @return {?}
     */
    get shadowColor() {
        return this._shadowColor;
    }
    //----themeColor---------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set themeColor(value) {
    }
    /**
     * @return {?}
     */
    get themeColor() {
        return this._themeColor;
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        try {
            console.log('[VRule] ngOnDestroy');
            delete this._strokeColor;
            delete this._shadowColor;
            delete this._themeColor;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
}
VRule.decorators = [
    { type: Component, args: [{
                selector: 'VRule',
                template: `
    <div class="v-rule"></div>
  `,
                styles: [`
       .v-rule {
           background-color: #000;
           height: 100%;
           width:1px;
           box-shadow: -1px 0px 0px #fff;
       }
  `]
            }] }
];
/** @nocollapse */
VRule.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
VRule.propDecorators = {
    strokeColor: [{ type: Input }],
    shadowColor: [{ type: Input }],
    themeColor: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._strokeColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._shadowColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype._themeColor;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    VRule.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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