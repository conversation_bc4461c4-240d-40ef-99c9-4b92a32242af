/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver/FileSaver';
var FileReference = /** @class */ (function () {
    function FileReference() {
    }
    /**
     * @param {?} bytearray
     * @param {?} fileName
     * @return {?}
     */
    FileReference.prototype.save = /**
     * @param {?} bytearray
     * @param {?} fileName
     * @return {?}
     */
    function (bytearray, fileName) {
        try {
            /** @type {?} */
            var file = new File(bytearray, fileName, { type: "text/plain;charset=utf-8" });
            saveAs(file);
        }
        catch (error) {
            console.error("FileReference - [ save ] method error :", error);
        }
    };
    FileReference.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    FileReference.ctorParameters = function () { return []; };
    return FileReference;
}());
export { FileReference };
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmlsZS1yZWZlcmVuY2Uuc2VydmljZS5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy91dGlscy9maWxlLXJlZmVyZW5jZS5zZXJ2aWNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQzNDLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxzQkFBc0IsQ0FBQztBQUU5QztJQUdFO0lBQWdCLENBQUM7Ozs7OztJQUVWLDRCQUFJOzs7OztJQUFYLFVBQVksU0FBZ0IsRUFBQyxRQUFnQjtRQUN6QyxJQUFJOztnQkFDSSxJQUFJLEdBQUcsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFLFFBQVEsRUFBRSxFQUFDLElBQUksRUFBRSwwQkFBMEIsRUFBQyxDQUFDO1lBQzVFLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUNoQjtRQUFDLE9BQU0sS0FBSyxFQUFFO1lBQ1gsT0FBTyxDQUFDLEtBQUssQ0FBQyx5Q0FBeUMsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUNuRTtJQUNMLENBQUM7O2dCQVpGLFVBQVU7Ozs7SUFjWCxvQkFBQztDQUFBLEFBZEQsSUFjQztTQWJZLGFBQWEiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IHNhdmVBcyB9IGZyb20gJ2ZpbGUtc2F2ZXIvRmlsZVNhdmVyJztcclxuXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIEZpbGVSZWZlcmVuY2Uge1xyXG5cclxuICBjb25zdHJ1Y3RvcigpIHsgfVxyXG5cclxuICBwdWJsaWMgc2F2ZShieXRlYXJyYXk6IGFueVtdLGZpbGVOYW1lOiBzdHJpbmcpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICAgIHZhciBmaWxlID0gbmV3IEZpbGUoYnl0ZWFycmF5LCBmaWxlTmFtZSwge3R5cGU6IFwidGV4dC9wbGFpbjtjaGFyc2V0PXV0Zi04XCJ9KTtcclxuICAgICAgICAgIHNhdmVBcyhmaWxlKTtcclxuICAgICAgfSBjYXRjaChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkZpbGVSZWZlcmVuY2UgLSBbIHNhdmUgXSBtZXRob2QgZXJyb3IgOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICB9XHJcbiAgXHJcbn1cclxuIl19