/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
export class SwtGroupedTotalCommonGrid extends SwtCommonGrid {
    /**
     * @param {?} el
     * @param {?} commonService
     * @param {?} autoTooltipExtension
     * @param {?} extensionUtility
     * @param {?} sharedService
     * @param {?} collectionService
     * @param {?} translate
     */
    constructor(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        super(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate);
        this.el = el;
        this.commonService = commonService;
        this.autoTooltipExtension = autoTooltipExtension;
        this.extensionUtility = extensionUtility;
        this.sharedService = sharedService;
        this.translate = translate;
        this.showHeader = false;
        this.isGroupedHeaderGrid = false;
        this.isTotalGrid = true;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set initialColumnsToSkip(value) {
        this._initialColumnsToSkip = value;
    }
    /**
     * @return {?}
     */
    get initialColumnsToSkip() {
        return this._initialColumnsToSkip;
    }
}
SwtGroupedTotalCommonGrid.decorators = [
    { type: Component, args: [{
                selector: 'SwtGroupedTotalCommonGrid',
                template: `
    <angular-slickgrid
    class="commonSlickGrid"
            #angularSlickGrid
            gridId='grid-{{id}}'
            (onDataviewCreated)="dataviewReady($event)"
            (onAngularGridCreated)="onAngularGridCreated($event)"
            [columnDefinitions]="columnDefinitions"
            [gridOptions]="gridOptions"
            gridHeight="100%"
            gridWidth="100%"
            [dataset]="dataset"
    >
    </angular-slickgrid>

`,
                providers: [
                    TranslateService,
                    ExtensionService,
                    AutoTooltipExtension,
                    ExtensionUtility,
                    SharedService,
                    CollectionService
                ],
                styles: [`
    .gridContent{
        min-width: 300px;
        height: 100%;
    }
    :host ::ng-deep .gridPane {
        overflow: auto;
        display: block;
    }
    :host ::ng-deep .slickgrid-container {
        min-height: 100%;
    }


`]
            }] }
];
/** @nocollapse */
SwtGroupedTotalCommonGrid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: AutoTooltipExtension },
    { type: ExtensionUtility },
    { type: SharedService },
    { type: CollectionService },
    { type: TranslateService }
];
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtGroupedTotalCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbW1vbi1ncm91cGVkLXRvdGFsLWdyaWQuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1jb21tb24tZ3JvdXBlZC10b3RhbC1ncmlkLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUNtRCxTQUFTLEVBRTBELFVBQVUsRUFDdEksTUFBTSxlQUFlLENBQUM7QUFDdkIsT0FBTyxFQUFFLGFBQWEsRUFBRSxNQUFNLDZCQUE2QixDQUFDO0FBQzVELE9BQU8sRUE0QkgsYUFBYSxFQU1iLGdCQUFnQixFQUdoQixnQkFBZ0IsRUFDaEIsb0JBQW9CLEVBV3BCLGlCQUFpQixHQUNwQixNQUFNLG1CQUFtQixDQUFDO0FBQzNCLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLHFCQUFxQixDQUFDO0FBQ3ZELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSx5QkFBeUIsQ0FBQztBQTZDeEQsTUFBTSxPQUFPLHlCQUEwQixTQUFRLGFBQWE7Ozs7Ozs7Ozs7SUFFeEQsWUFBdUIsRUFBYyxFQUFZLGFBQTRCLEVBQWEsb0JBQTJDLEVBQVksZ0JBQWtDLEVBQVksYUFBNkIsRUFDdk4saUJBQXFDLEVBQWEsU0FBMkI7UUFDOUUsS0FBSyxDQUFDLEVBQUUsRUFBQyxhQUFhLEVBQUMsb0JBQW9CLEVBQUUsZ0JBQWdCLEVBQUMsYUFBYSxFQUFFLGlCQUFpQixFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBRnhGLE9BQUUsR0FBRixFQUFFLENBQVk7UUFBWSxrQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUFhLHlCQUFvQixHQUFwQixvQkFBb0IsQ0FBdUI7UUFBWSxxQkFBZ0IsR0FBaEIsZ0JBQWdCLENBQWtCO1FBQVksa0JBQWEsR0FBYixhQUFhLENBQWdCO1FBQ3JLLGNBQVMsR0FBVCxTQUFTLENBQWtCO1FBRTlFLElBQUksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO1FBQ3hCLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxLQUFLLENBQUM7UUFDakMsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUM7SUFDM0IsQ0FBQzs7Ozs7SUFJRCxJQUFJLG9CQUFvQixDQUFHLEtBQWE7UUFDcEMsSUFBSSxDQUFDLHFCQUFxQixHQUFHLEtBQUssQ0FBQztJQUN2QyxDQUFDOzs7O0lBRUQsSUFBSSxvQkFBb0I7UUFDcEIsT0FBTyxJQUFJLENBQUMscUJBQXFCLENBQUU7SUFDdkMsQ0FBQzs7O1lBN0RMLFNBQVMsU0FBRTtnQkFDUixRQUFRLEVBQUUsMkJBQTJCO2dCQUNyQyxRQUFRLEVBQUU7Ozs7Ozs7Ozs7Ozs7OztDQWViO2dCQWdCRCxTQUFTLEVBQUU7b0JBQ1AsZ0JBQWdCO29CQUNoQixnQkFBZ0I7b0JBQ2hCLG9CQUFvQjtvQkFDcEIsZ0JBQWdCO29CQUNoQixhQUFhO29CQUNiLGlCQUFpQjtpQkFDcEI7eUJBdEJROzs7Ozs7Ozs7Ozs7OztDQWNSO2FBU0E7Ozs7WUFsRzRILFVBQVU7WUF1RDlILGFBQWE7WUFkbEIsb0JBQW9CO1lBSnBCLGdCQUFnQjtZQU5oQixhQUFhO1lBcUJiLGlCQUFpQjtZQUVaLGdCQUFnQjs7Ozs7OztJQWdEUix1Q0FBd0I7Ozs7O0lBQUUsa0RBQXNDOzs7OztJQUFHLHlEQUFxRDs7Ozs7SUFBRSxxREFBNEM7Ozs7O0lBQUUsa0RBQXVDOzs7OztJQUMvSyw4Q0FBcUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gICAgQWZ0ZXJDb250ZW50Q2hlY2tlZCwgQWZ0ZXJWaWV3Q2hlY2tlZCwgQWZ0ZXJWaWV3SW5pdCwgQ29tcG9uZW50LCBDb21wb25lbnRGYWN0b3J5LFxyXG4gICAgQ29tcG9uZW50RmFjdG9yeVJlc29sdmVyLCBDb21wb25lbnRSZWYsRXZlbnRFbWl0dGVyLFxyXG4gICAgSW5qZWN0YWJsZSwgSW5wdXQsIE9uQ2hhbmdlcywgT25Jbml0LCBPdXRwdXQsIFJlbmRlcmVyLCBWaWV3Q2hpbGQsIFZpZXdDb250YWluZXJSZWYsT25EZXN0cm95LCBJbmplY3RvciwgQXBwbGljYXRpb25SZWYsIEVsZW1lbnRSZWZcclxufSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuaW1wb3J0IHsgU3d0Q29tbW9uR3JpZCB9IGZyb20gJy4vc3d0LWNvbW1vbi1ncmlkLmNvbXBvbmVudCc7XHJcbmltcG9ydCB7XHJcbiAgICBBZ2dyZWdhdG9ycyxcclxuICAgIEFuZ3VsYXJHcmlkSW5zdGFuY2UsXHJcbiAgICBBbmd1bGFyU2xpY2tncmlkQ29tcG9uZW50LFxyXG4gICAgQmFja2VuZFNlcnZpY2VPcHRpb24sXHJcbiAgICBDZWxsQXJncyxcclxuICAgIENvbHVtbixcclxuICAgIENvbHVtbkZpbHRlcnMsXHJcbiAgICBFZGl0b3JzLFxyXG4gICAgRXhwb3J0U2VydmljZSxcclxuICAgIEZpZWxkVHlwZSxcclxuICAgIEZpbHRlckNoYW5nZWRBcmdzLFxyXG4gICAgRmlsdGVyRmFjdG9yeSxcclxuICAgIEZpbHRlcnMsXHJcbiAgICBGaWx0ZXJTZXJ2aWNlLFxyXG4gICAgRm9ybWF0dGVyLFxyXG4gICAgRm9ybWF0dGVycyxcclxuICAgIEdyaWRNZW51RXh0ZW5zaW9uLFxyXG4gICAgR3JpZE9wdGlvbixcclxuICAgIEdyaWRTZXJ2aWNlLFxyXG4gICAgR3JpZFN0YXRlQ2hhbmdlLFxyXG4gICAgR3JpZFN0YXRlU2VydmljZSxcclxuICAgIEdyb3VwaW5nLFxyXG4gICAgR3JvdXBUb3RhbEZvcm1hdHRlcnMsXHJcbiAgICBPbkV2ZW50QXJncyxcclxuICAgIFBhZ2luYXRpb24sXHJcbiAgICBQYWdpbmF0aW9uQ2hhbmdlZEFyZ3MsXHJcbiAgICBSZXNpemVyU2VydmljZSxcclxuICAgIFNoYXJlZFNlcnZpY2UsXHJcbiAgICBTbGlja2dyaWRDb25maWcsXHJcbiAgICBTb3J0Q2hhbmdlZEFyZ3MsXHJcbiAgICBTb3J0RGlyZWN0aW9uTnVtYmVyLFxyXG4gICAgU29ydGVycyxcclxuICAgIFNvcnRTZXJ2aWNlLFxyXG4gICAgRXh0ZW5zaW9uVXRpbGl0eSxcclxuICAgIEV4dGVuc2lvbk5hbWUsXHJcbiAgICBIZWFkZXJNZW51LFxyXG4gICAgRXh0ZW5zaW9uU2VydmljZSxcclxuICAgIEF1dG9Ub29sdGlwRXh0ZW5zaW9uLFxyXG4gICAgQ2VsbEV4dGVybmFsQ29weU1hbmFnZXJFeHRlbnNpb24sXHJcbiAgICBDb2x1bW5QaWNrZXJFeHRlbnNpb24sXHJcbiAgICBEcmFnZ2FibGVHcm91cGluZ0V4dGVuc2lvbixcclxuICAgIEhlYWRlck1lbnVFeHRlbnNpb24sXHJcbiAgICBIZWFkZXJCdXR0b25FeHRlbnNpb24sXHJcbiAgICBHcm91cEl0ZW1NZXRhUHJvdmlkZXJFeHRlbnNpb24sXHJcbiAgICBDaGVja2JveFNlbGVjdG9yRXh0ZW5zaW9uLFxyXG4gICAgUm93RGV0YWlsVmlld0V4dGVuc2lvbixcclxuICAgIFJvd01vdmVNYW5hZ2VyRXh0ZW5zaW9uLFxyXG4gICAgUm93U2VsZWN0aW9uRXh0ZW5zaW9uLFxyXG4gICAgQ29sbGVjdGlvblNlcnZpY2UsXHJcbn0gZnJvbSAnYW5ndWxhci1zbGlja2dyaWQnO1xyXG5pbXBvcnQgeyBUcmFuc2xhdGVTZXJ2aWNlIH0gZnJvbSAnQG5neC10cmFuc2xhdGUvY29yZSc7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tICcuLi91dGlscy9jb21tb24uc2VydmljZSc7XHJcblxyXG5AQ29tcG9uZW50KCB7XHJcbiAgICBzZWxlY3RvcjogJ1N3dEdyb3VwZWRUb3RhbENvbW1vbkdyaWQnLFxyXG4gICAgdGVtcGxhdGU6IGBcclxuICAgIDxhbmd1bGFyLXNsaWNrZ3JpZFxyXG4gICAgY2xhc3M9XCJjb21tb25TbGlja0dyaWRcIlxyXG4gICAgICAgICAgICAjYW5ndWxhclNsaWNrR3JpZFxyXG4gICAgICAgICAgICBncmlkSWQ9J2dyaWQte3tpZH19J1xyXG4gICAgICAgICAgICAob25EYXRhdmlld0NyZWF0ZWQpPVwiZGF0YXZpZXdSZWFkeSgkZXZlbnQpXCJcclxuICAgICAgICAgICAgKG9uQW5ndWxhckdyaWRDcmVhdGVkKT1cIm9uQW5ndWxhckdyaWRDcmVhdGVkKCRldmVudClcIlxyXG4gICAgICAgICAgICBbY29sdW1uRGVmaW5pdGlvbnNdPVwiY29sdW1uRGVmaW5pdGlvbnNcIlxyXG4gICAgICAgICAgICBbZ3JpZE9wdGlvbnNdPVwiZ3JpZE9wdGlvbnNcIlxyXG4gICAgICAgICAgICBncmlkSGVpZ2h0PVwiMTAwJVwiXHJcbiAgICAgICAgICAgIGdyaWRXaWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICBbZGF0YXNldF09XCJkYXRhc2V0XCJcclxuICAgID5cclxuICAgIDwvYW5ndWxhci1zbGlja2dyaWQ+XHJcblxyXG5gLFxyXG5zdHlsZXM6IFtgXHJcbiAgICAuZ3JpZENvbnRlbnR7XHJcbiAgICAgICAgbWluLXdpZHRoOiAzMDBweDtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICB9XHJcbiAgICA6aG9zdCA6Om5nLWRlZXAgLmdyaWRQYW5lIHtcclxuICAgICAgICBvdmVyZmxvdzogYXV0bztcclxuICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIH1cclxuICAgIDpob3N0IDo6bmctZGVlcCAuc2xpY2tncmlkLWNvbnRhaW5lciB7XHJcbiAgICAgICAgbWluLWhlaWdodDogMTAwJTtcclxuICAgIH1cclxuXHJcblxyXG5gXSxcclxucHJvdmlkZXJzOiBbXHJcbiAgICBUcmFuc2xhdGVTZXJ2aWNlLFxyXG4gICAgRXh0ZW5zaW9uU2VydmljZSxcclxuICAgIEF1dG9Ub29sdGlwRXh0ZW5zaW9uLFxyXG4gICAgRXh0ZW5zaW9uVXRpbGl0eSxcclxuICAgIFNoYXJlZFNlcnZpY2UsXHJcbiAgICBDb2xsZWN0aW9uU2VydmljZVxyXG5dLFxyXG59IClcclxuXHJcbmV4cG9ydCBjbGFzcyBTd3RHcm91cGVkVG90YWxDb21tb25HcmlkIGV4dGVuZHMgU3d0Q29tbW9uR3JpZCBpbXBsZW1lbnRzIE9uSW5pdCwgQWZ0ZXJWaWV3SW5pdCAsIE9uRGVzdHJveSB7XHJcbiAgIFxyXG4gICAgY29uc3RydWN0b3IoIHByb3RlY3RlZCBlbDogRWxlbWVudFJlZiwgcHJvdGVjdGVkIGNvbW1vblNlcnZpY2U6IENvbW1vblNlcnZpY2UgLCBwcm90ZWN0ZWQgYXV0b1Rvb2x0aXBFeHRlbnNpb24gOiBBdXRvVG9vbHRpcEV4dGVuc2lvbiwgcHJvdGVjdGVkIGV4dGVuc2lvblV0aWxpdHkgOkV4dGVuc2lvblV0aWxpdHksIHByb3RlY3RlZCBzaGFyZWRTZXJ2aWNlIDogU2hhcmVkU2VydmljZVxyXG4gICAgICAgICxjb2xsZWN0aW9uU2VydmljZSA6IENvbGxlY3Rpb25TZXJ2aWNlICwgcHJvdGVjdGVkIHRyYW5zbGF0ZTogVHJhbnNsYXRlU2VydmljZSkge1xyXG4gICAgICAgIHN1cGVyKGVsLGNvbW1vblNlcnZpY2UsYXV0b1Rvb2x0aXBFeHRlbnNpb24sIGV4dGVuc2lvblV0aWxpdHksc2hhcmVkU2VydmljZSwgY29sbGVjdGlvblNlcnZpY2UsIHRyYW5zbGF0ZSk7XHJcbiAgICAgICAgdGhpcy5zaG93SGVhZGVyID0gZmFsc2U7XHJcbiAgICAgICAgdGhpcy5pc0dyb3VwZWRIZWFkZXJHcmlkID0gZmFsc2U7XHJcbiAgICAgICAgdGhpcy5pc1RvdGFsR3JpZCA9IHRydWU7XHJcbiAgICAgfVxyXG4gIFxyXG4gXHJcbiAgICAgXHJcbiAgICAgc2V0IGluaXRpYWxDb2x1bW5zVG9Ta2lwICggdmFsdWU6IG51bWJlciApIHtcclxuICAgICAgICAgdGhpcy5faW5pdGlhbENvbHVtbnNUb1NraXAgPSB2YWx1ZTtcclxuICAgICB9XHJcbiBcclxuICAgICBnZXQgaW5pdGlhbENvbHVtbnNUb1NraXAgKCkge1xyXG4gICAgICAgICByZXR1cm4gdGhpcy5faW5pdGlhbENvbHVtbnNUb1NraXAgO1xyXG4gICAgIH1cclxufVxyXG4iXX0=