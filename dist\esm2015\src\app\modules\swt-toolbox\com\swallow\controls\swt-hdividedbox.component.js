/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef, Output, EventEmitter, Input, Injector, ComponentFactoryResolver } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { HDividedEndResizeEvent, TabSelectEvent, DividerResizeComplete } from "../events/swt-events.module";
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
const $ = require('jquery');
export class HDividedBox extends Container {
    /**
     * constructor
     * @param {?} elem
     * @param {?} commonService
     * @param {?} injector
     * @param {?} componentFactoryResolver
     */
    constructor(elem, commonService, injector, componentFactoryResolver) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.injector = injector;
        this.componentFactoryResolver = componentFactoryResolver;
        this.DIVIDER_DRAG_COMPLETE = new EventEmitter();
        this.DIVIDER_BUTTON_CLICK = new EventEmitter();
        //---Properties definitions---------------------------------------------------------------------------------------
        this._resize = new Function();
        this._resizeStart = new Function();
        this._resizeStop = new Function();
        this._widthRight = null;
        this._widthLeft = null;
        this.widthLeftPixel = null;
        this.widthRightPixel = null;
        this._dividersAnimation = 'W';
        this._extendedDividedBox = false;
        this.doResize = false;
        this.RightContent = null;
        this.leftContent = null;
        this._liveDrag = false;
        this.startDrag = false;
        this.lastValidValue = null;
        this._maxWidthRight = null;
        this._minWidthRight = null;
        this._maxWidthLeft = null;
        this._minWidthLeft = null;
        // Make panelRight DOM reference.
        //---Outputs------------------------------------------------------------------------------------------------------
        this.resize_ = new EventEmitter();
        this.resizeStart_ = new EventEmitter();
        this.resizeStop_ = new EventEmitter();
        this.defaultIcon = 'assets/images/hresize-handler.png';
        this.hdividerClosed = 'assets/images/hdividerClosed.png';
        this.hdividerOpened = 'assets/images/hdividerOpened.png';
        this.forceNoEvent = false;
        $($(this.elem.nativeElement)[0]).attr('selector', 'HDividedBox');
    }
    /**
     * @return {?}
     */
    get maxWidthRight() {
        return this._maxWidthRight;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set maxWidthRight(value) {
        this._maxWidthRight = value;
    }
    /**
     * @return {?}
     */
    get minWidthRight() {
        return this._minWidthRight;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set minWidthRight(value) {
        this._minWidthRight = value;
    }
    /**
     * @return {?}
     */
    get maxWidthLeft() {
        return this._maxWidthLeft;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set maxWidthLeft(value) {
        this._maxWidthLeft = value;
    }
    /**
     * @return {?}
     */
    get minWidthLeft() {
        return this._minWidthLeft;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set minWidthLeft(value) {
        this._minWidthLeft = value;
    }
    //---resize getter and setter-------------------------------------------------------------------------------------
    /**
     * @param {?} handler
     * @return {?}
     */
    set resize(handler) {
        this._resize = handler;
    }
    /**
     * @return {?}
     */
    get resize() {
        return this._resize;
    }
    //---resize getter and setter-------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set prevRightWidth(value) {
        this._prevRightWidth = value;
    }
    /**
     * @return {?}
     */
    get prevRightWidth() {
        return this._prevRightWidth;
    }
    //---resize Start getter and setter-------------------------------------------------------------------------------
    /**
     * @param {?} handler
     * @return {?}
     */
    set resizeStart(handler) {
        this._resizeStart = handler;
    }
    /**
     * @return {?}
     */
    get resizeStart() {
        return this._resizeStart;
    }
    //--- resize Stop getter and setter-------------------------------------------------------------------------------
    /**
     * @param {?} handler
     * @return {?}
     */
    set resizeStop(handler) {
        this._resizeStop = handler;
    }
    /**
     * @return {?}
     */
    get resizeStop() {
        return this._resizeStop;
    }
    //---height-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = this.adaptUnit(value, 'auto');
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    //---width--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        this._width = this.adaptUnit(value, 'auto');
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /**
     * @return {?}
     */
    onButtonClickHandler() {
        if (!this.startDrag) {
            if (this.extendedDividedBox) {
                /** @type {?} */
                let isClosed = false;
                if (this._dividersAnimation == 'W') {
                    if (this.widthLeft == '0') {
                        $($(this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                        this.widthLeft = this.prevLeftWidth;
                        isClosed = false;
                    }
                    else {
                        this.widthLeft = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                    }
                }
                else {
                    if (this.widthRight == '0') {
                        $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                        this.widthRight = this.prevRightWidth;
                        isClosed = false;
                    }
                    else {
                        this.widthRight = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                    }
                }
                this.DIVIDER_BUTTON_CLICK.emit({ id: this.id, isClosed: isClosed, type: 'DIVIDER_BUTTON_CLICK' });
            }
            DividerResizeComplete.emit({ id: this.id });
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set widthRight(value) {
        console.log("HDividedBox -> setwidthRight -> this.prevRightWidth start width", this.prevRightWidth, this.widthRight);
        if ('0' !== this.widthRight && '0%' !== this.widthRight)
            this.prevRightWidth = this.widthRight ? "" + this.widthRight : "" + this.prevRightWidth;
        this._widthRight = value;
        if (('' + this._widthRight).indexOf("%") > -1) {
            /** @type {?} */
            let tmp = Number(('' + this._widthRight).replace('%', ''));
            /** @type {?} */
            let newLeftWidth = $($(this.panelLeft.nativeElement).parent()).width() * ((100 - tmp) / 100);
            $(this.panelLeft.nativeElement).resizable("resizeTo", { width: newLeftWidth - 10 });
        }
        else {
            /** @type {?} */
            let newLeftWidth = ($($(this.panelLeft.nativeElement).parent()).width() - this._widthRight);
            $(this.panelLeft.nativeElement).resizable("resizeTo", { width: Number(newLeftWidth - 10) });
        }
        if (value === '0' || value === '0%') {
            this.widthRightPixel = 0;
            // this.forceZeroWidth = true;
            $($(this.panelRight.nativeElement).children()[0]).css('display', 'none');
            $(this.panelLeft.nativeElement).css("width", '100%');
            $(this.panelLeft.nativeElement).css("margin-left", '-10px');
            $(this.panelLeft.nativeElement).css("padding-left", '10px');
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    setWidthRightWithoutEvent(value) {
        this.forceNoEvent = true;
        this.widthRight = value;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    setWidthLeftWithoutEvent(value) {
        this.forceNoEvent = true;
        this.widthLeft = value;
    }
    /**
     * @return {?}
     */
    get widthRight() {
        return this._widthRight;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dividersAnimation(value) {
        if (value) {
            this._dividersAnimation = value;
        }
    }
    /**
     * @return {?}
     */
    get dividersAnimation() {
        return this._dividersAnimation;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set extendedDividedBox(value) {
        value = StringUtils.isTrue(value);
        if (value) {
            this._extendedDividedBox = value;
        }
    }
    /**
     * @return {?}
     */
    get extendedDividedBox() {
        return this._extendedDividedBox;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set liveDrag(value) {
        value = StringUtils.isTrue(value);
        if (value) {
            this._liveDrag = value;
        }
    }
    /**
     * @return {?}
     */
    get liveDrag() {
        return this._liveDrag;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set widthLeft(value) {
        if ('0' !== this.widthLeft && '0%' !== this.widthLeft)
            this.prevLeftWidth = this.widthLeft;
        this._widthLeft = value;
        if (value === '0') {
            // $(this.panelLeft.nativeElement).css('display','none');
            $($(this.panelLeft.nativeElement).children()[0]).css('display', 'none');
            this.widthLeftPixel = 0;
            // this.forceZeroWidth = true;
            // $(this.panelLeft.nativeElement).resizable("resizeTo", {  width: Number(0) });
            $(this.panelLeft.nativeElement).css("width", '0%');
            $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
            $(this.panelLeft.nativeElement).css("margin-left", '-10px');
            $(this.panelLeft.nativeElement).css("padding-left", '10px');
        }
        else {
            if (('' + this._widthLeft).indexOf("%") > -1) {
                /** @type {?} */
                let tmp = Number(('' + this._widthLeft).replace('%', ''));
                /** @type {?} */
                let newLeftWidth = $($(this.panelLeft.nativeElement).parent()).width() * (tmp / 100);
                $(this.panelLeft.nativeElement).resizable("resizeTo", { width: newLeftWidth });
            }
            else {
                // this.setStyle("width", this._widthLeft , $(this.panelLeft.nativeElement))
                $(this.panelLeft.nativeElement).resizable("resizeTo", { width: Number(value) });
            }
        }
    }
    /**
     * @return {?}
     */
    get widthLeft() {
        return this._widthLeft;
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        try {
            this.setStyle("width", this.width, $(this.hdividedboxContainer.nativeElement.parentElement));
            this.setStyle("height", this.height, $(this.hdividedboxContainer.nativeElement.parentElement));
            this.setStyle("height", "100%", $(this.hdividedboxContainer.nativeElement));
            this._widthLeft = $($(this.panelLeft.nativeElement).children()[0]).attr("width");
            this._widthRight = $($(this.panelRight.nativeElement).children()[0]).attr("width");
            if (this._widthLeft)
                this.setStyle("width", this._widthLeft, $(this.panelLeft.nativeElement));
            ;
            if (this._widthRight)
                this.setStyle("width", this._widthRight, $(this.panelRight.nativeElement));
            ;
            if (this.extendedDividedBox) {
                if (this._dividersAnimation == 'W') {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                }
                else {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                }
            }
        }
        catch (error) {
        }
    }
    /**
     * ngAfterViewInit
     * @return {?}
     */
    ngAfterViewInit() {
        /** @type {?} */
        const componentFactory = this.componentFactoryResolver.resolveComponentFactory(Container);
        this.componentRef = componentFactory.create(this.injector);
        try {
            this.leftContent = $($(this.panelLeft.nativeElement).children()[0]);
            this.RightContent = $($(this.panelRight.nativeElement).children()[0]);
            $(this.leftContent).css("width", "100%");
            $(this.RightContent).css("width", "100%");
            /** @type {?} */
            var parentWidth = $($(this.panelLeft.nativeElement).parent()).width();
            $(this.panelLeft.nativeElement).resizable({
                handleSelector: ".splitter",
                resizeHeight: false,
                handles: 'e',
                helper: this._liveDrag ? false : "hdividedbox-resizable-helper",
                maxWidth: parentWidth - 10,
                minWidth: 1,
                resize: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                (event, ui) => {
                    this._resize(event);
                    this.resize_.emit(event);
                    if (this._dividersAnimation == 'W') {
                        if ($($(this.panelLeft.nativeElement).children()[0]).css('display') == 'none') {
                            setTimeout((/**
                             * @return {?}
                             */
                            () => {
                                $($(this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                $(this.panelLeft.nativeElement).css("margin-left", '0px');
                                $(this.panelLeft.nativeElement).css("padding-left", '0px');
                                TabSelectEvent.emit(null);
                            }), 0);
                        }
                    }
                    else {
                        if ($($(this.panelRight.nativeElement).children()[0]).css('display') == 'none') {
                            setTimeout((/**
                             * @return {?}
                             */
                            () => {
                                $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                $(this.panelLeft.nativeElement).css("margin-left", '0px');
                                $(this.panelLeft.nativeElement).css("padding-left", '0px');
                                TabSelectEvent.emit(null);
                            }), 0);
                        }
                    }
                }),
                create: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                (event, ui) => {
                    if (this.extendedDividedBox) {
                        $($(this.panelLeft.nativeElement).children('.ui-resizable-handle')[0]).click((/**
                         * @return {?}
                         */
                        () => {
                            this.onButtonClickHandler();
                        }));
                    }
                }),
                start: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                (event, ui) => {
                    this.startDrag = true;
                    $('iframe').css('pointer-events', 'none');
                    this.prevLeftWidth = this._widthLeft;
                    /** @type {?} */
                    let maxWidthLeftTemp = 0;
                    /** @type {?} */
                    let minWidthLeftTemp = 0;
                    if (this.maxWidthLeft) {
                        maxWidthLeftTemp = this.maxWidthLeft;
                    }
                    else {
                        maxWidthLeftTemp = !this.minWidthRight ? $($(this.panelLeft.nativeElement).parent()).width() - 10 : $($(this.panelLeft.nativeElement).parent()).width() - this.minWidthRight - 10;
                    }
                    if (this.minWidthLeft) {
                        minWidthLeftTemp = this.minWidthLeft;
                    }
                    else {
                        minWidthLeftTemp = !this.maxWidthRight ? 1 : $($(this.panelLeft.nativeElement).parent()).width() - this.maxWidthRight;
                    }
                    $(this.panelLeft.nativeElement).resizable("option", "maxWidth", maxWidthLeftTemp);
                    $(this.panelLeft.nativeElement).resizable("option", "minWidth", minWidthLeftTemp);
                    this._resizeStart(event);
                    this.resizeStart_.emit(event);
                }),
                stop: (/**
                 * @param {?} event
                 * @param {?} ui
                 * @return {?}
                 */
                (event, ui) => {
                    setTimeout((/**
                     * @return {?}
                     */
                    () => {
                        $('iframe').css('pointer-events', 'auto');
                        this.startDrag = false;
                        /** @type {?} */
                        var cellPercentWidth = 100 * ui.originalElement.outerWidth() / $($(this.panelLeft.nativeElement).parent()).innerWidth();
                        ui.originalElement.css('width', cellPercentWidth + '%');
                        /** @type {?} */
                        var nextCell = ui.originalElement.next().next();
                        /** @type {?} */
                        var nextPercentWidth = 100 * nextCell.outerWidth() / $($(this.panelLeft.nativeElement).parent()).innerWidth();
                        nextCell.css('width', nextPercentWidth + '%');
                        $(this.panelLeft.nativeElement).css("height", '');
                        if (this.extendedDividedBox) {
                            if (ui.size.width == 1) {
                                this.prevLeftWidth = ui.originalSize.width;
                                this.widthLeft = '0';
                                this._widthRight = '100%';
                            }
                            else {
                                if (cellPercentWidth > 98) {
                                    if (!this.doResize) {
                                        this.doResize = true;
                                        /** @type {?} */
                                        var percentOrginalWidth = 100 * ui.originalSize.width / $($(this.panelLeft.nativeElement).parent()).innerWidth();
                                        if (percentOrginalWidth < 98)
                                            this.prevRightWidth = Math.round((100 - percentOrginalWidth)) + '%';
                                        this.widthRight = '0';
                                        this._widthLeft = '100%';
                                    }
                                    else {
                                        this.doResize = false;
                                    }
                                }
                                else {
                                    this.prevLeftWidth = isNaN(this._widthLeft = Math.round(cellPercentWidth)) ? this.prevLeftWidth : this._widthLeft = Math.round(cellPercentWidth) + '%';
                                    this.prevRightWidth = isNaN(this._widthRight = nextPercentWidth) ? this.prevRightWidth : this._widthRight = nextPercentWidth + '%';
                                    if (this._dividersAnimation == 'W') {
                                        setTimeout((/**
                                         * @return {?}
                                         */
                                        () => {
                                            if (Math.round(cellPercentWidth) < 98 || nextPercentWidth < 98) {
                                                $($(this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                                $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                                $(this.panelLeft.nativeElement).css("margin-left", '0px');
                                                $(this.panelLeft.nativeElement).css("padding-left", '0px');
                                                TabSelectEvent.emit(null);
                                            }
                                        }), 0);
                                    }
                                    else {
                                        setTimeout((/**
                                         * @return {?}
                                         */
                                        () => {
                                            if (Math.round(cellPercentWidth) < 98 || nextPercentWidth < 98) {
                                                $($(this.panelRight.nativeElement).children()[0]).css('display', 'flex');
                                                $($(this.panelLeft.nativeElement).children()[0]).css('display', 'flex');
                                                $(this.panelLeft.nativeElement).css("margin-left", '0px');
                                                $(this.panelLeft.nativeElement).css("padding-left", '0px');
                                                TabSelectEvent.emit(null);
                                            }
                                        }), 0);
                                    }
                                }
                            }
                        }
                        else {
                            this.prevLeftWidth = this._widthLeft = Math.round(cellPercentWidth) + '%';
                            this.prevRightWidth = this._widthRight = nextPercentWidth + '%';
                        }
                        if (this.widthLeft == '0') {
                            this.widthLeftPixel = 0;
                        }
                        else {
                            this.widthLeftPixel = Math.round(ui.originalElement.outerWidth()) || 0;
                        }
                        if (this.widthRight == '0') {
                            this.widthRightPixel = 0;
                        }
                        else {
                            this.widthRightPixel = Math.round(nextCell.outerWidth()) || 0;
                        }
                        if (!this.forceNoEvent) {
                            this._resizeStop();
                            this.resizeStop_.emit(event);
                            HDividedEndResizeEvent.emit(event);
                            this.DIVIDER_DRAG_COMPLETE.emit({ id: this.id, type: 'DIVIDER_DRAG_COMPLETE' });
                        }
                        else {
                            this.forceNoEvent = false;
                        }
                        if (this.extendedDividedBox) {
                            if (this._dividersAnimation == 'W') {
                                if (this.widthLeftPixel == 0) {
                                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                                }
                                else {
                                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                                }
                            }
                            else {
                                if (this.widthRightPixel == 0) {
                                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerOpened + ')';
                                }
                                else {
                                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.hdividerClosed + ')';
                                }
                            }
                        }
                        DividerResizeComplete.emit({ id: this.id });
                    }), 0);
                })
            });
        }
        catch (error) {
        }
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this._resize;
            delete this._resizeStart;
            delete this._resizeStop;
            delete this._height;
            delete this._width;
            delete this.widthRight;
            delete this.widthLeft;
            delete this.RightContent;
            delete this.leftContent;
            delete this.hdividedboxContainer;
            delete this.panelLeft;
            delete this.panelRight;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
}
HDividedBox.decorators = [
    { type: Component, args: [{
                selector: 'HDividedBox',
                template: `
         <div #hdividedboxContainer  fxLayout="row" class="panel-container">
            <div #panelLeft  fxLayout="row" fxLayoutGap="{{horizontalGap}}" class="panel-left" >
                <ng-content  select=".left"></ng-content>
            </div>
            <div  class="splitter" #splitter (click)="onButtonClickHandler()"></div>
            <div #panelRight  fxLayout="row" fxLayoutGap="{{horizontalGap}}" class="panel-right" >
                <ng-content select=".right"></ng-content>
            </div>
        </div>
         
    `,
                styles: [`

        .panel-container {
            overflow: hidden;
            background-color: #D6E3FE;
            xtouch-action: none;
         }

        .panel-left {
            flex: 0 0 auto;
            height:100%;
            min-height: 20px;
            min-width: 10px;
            padding-bottom : 5px;
        }

        .splitter {
            flex: 0 0 auto;
            width: 10px !important;
            background-image: url("assets/images/hresize-handler.png");
            background-repeat: no-repeat;
            background-position: center;
        }

        .panel-right {
            flex: 1 1 auto;
            height:100%;
            min-height: 20px;
            min-width: 20px;
            padding-bottom : 5px;
        }


    `]
            }] }
];
/** @nocollapse */
HDividedBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Injector },
    { type: ComponentFactoryResolver }
];
HDividedBox.propDecorators = {
    DIVIDER_DRAG_COMPLETE: [{ type: Output, args: ['DIVIDER_DRAG_COMPLETE',] }],
    DIVIDER_BUTTON_CLICK: [{ type: Output, args: ['DIVIDER_BUTTON_CLICK',] }],
    maxWidthRight: [{ type: Input }],
    minWidthRight: [{ type: Input }],
    maxWidthLeft: [{ type: Input }],
    minWidthLeft: [{ type: Input }],
    hdividedboxContainer: [{ type: ViewChild, args: ["hdividedboxContainer",] }],
    panelLeft: [{ type: ViewChild, args: ["panelLeft",] }],
    panelRight: [{ type: ViewChild, args: ["panelRight",] }],
    splitter: [{ type: ViewChild, args: ["splitter",] }],
    resize_: [{ type: Output, args: ['resize',] }],
    resizeStart_: [{ type: Output, args: ['resizeStart',] }],
    resizeStop_: [{ type: Output, args: ['resizeStop',] }],
    height: [{ type: Input }],
    width: [{ type: Input }],
    widthRight: [{ type: Input }],
    dividersAnimation: [{ type: Input }],
    extendedDividedBox: [{ type: Input }],
    liveDrag: [{ type: Input }],
    widthLeft: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    HDividedBox.prototype.DIVIDER_DRAG_COMPLETE;
    /** @type {?} */
    HDividedBox.prototype.DIVIDER_BUTTON_CLICK;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resize;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resizeStart;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._resizeStop;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._height;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._width;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._widthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._widthLeft;
    /** @type {?} */
    HDividedBox.prototype.widthLeftPixel;
    /** @type {?} */
    HDividedBox.prototype.widthRightPixel;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._dividersAnimation;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._extendedDividedBox;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.doResize;
    /** @type {?} */
    HDividedBox.prototype.componentRef;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.RightContent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.leftContent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._liveDrag;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.startDrag;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.lastValidValue;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._maxWidthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._minWidthRight;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._maxWidthLeft;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype._minWidthLeft;
    /** @type {?} */
    HDividedBox.prototype.hdividedboxContainer;
    /** @type {?} */
    HDividedBox.prototype.panelLeft;
    /** @type {?} */
    HDividedBox.prototype.panelRight;
    /** @type {?} */
    HDividedBox.prototype.splitter;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resize_;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resizeStart_;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.resizeStop_;
    /** @type {?} */
    HDividedBox.prototype.prevLeftWidth;
    /** @type {?} */
    HDividedBox.prototype._prevRightWidth;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.defaultIcon;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.hdividerClosed;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.hdividerOpened;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.forceNoEvent;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.injector;
    /**
     * @type {?}
     * @private
     */
    HDividedBox.prototype.componentFactoryResolver;
}
//# sourceMappingURL=data:application/json;base64,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