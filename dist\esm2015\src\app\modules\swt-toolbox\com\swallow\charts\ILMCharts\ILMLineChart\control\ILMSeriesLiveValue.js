/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { StringUtils } from '../../../../utils/string-utils.service';
export class ILMSeriesLiveValue extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} cd
     */
    constructor(elem, commonService, cd) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.cd = cd;
        this.styleClassMap = {
            "CONT_SEGMENT_BLACK": 'bg-CONT_SEGMENT_BLACK',
            "CONT_SEGMENT_BLUE": 'bg-CONT_SEGMENT_BLUE',
            "CONT_SEGMENT_BOLD_RED": 'bg-CONT_SEGMENT_BOLD_RED',
            "CONT_SEGMENT_GREEN": 'bg-CONT_SEGMENT_GREEN',
            "CONT_SEGMENT_MAGENTA": 'bg-CONT_SEGMENT_MAGENTA',
            "CONT_SEGMENT_ORANGE": 'bg-CONT_SEGMENT_ORANGE',
            "CONT_SEGMENT_PURPLE": 'bg-CONT_SEGMENT_PURPLE',
            "CONT_SEGMENT_RED": 'bg-CONT_SEGMENT_RED',
            "CONT_SEGMENT_YELLOW": 'bg-CONT_SEGMENT_YELLOW',
            "DASHED_SEGMENT_BLACK": 'bg-DASHED_SEGMENT_BLACK',
            "DASHED_SEGMENT_BLUE": 'bg-DASHED_SEGMENT_BLUE',
            "DASHED_SEGMENT_GREEN": 'bg-DASHED_SEGMENT_GREEN',
            "DASHED_SEGMENT_MAGENTA": 'bg-DASHED_SEGMENT_MAGENTA',
            "DASHED_SEGMENT_ORANGE": 'bg-DASHED_SEGMENT_ORANGE',
            "DASHED_SEGMENT_PURPLE": 'bg-DASHED_SEGMENT_PURPLE',
            "DASHED_SEGMENT_RED": 'bg-DASHED_SEGMENT_RED',
            "DASHED_SEGMENT_YELLOW": 'bg-DASHED_SEGMENT_YELLOW',
            "DOTTED_SEGMENT_BLACK": 'bg-DOTTED_SEGMENT_BLACK',
            "DOTTED_SEGMENT_BLUE": 'bg-DOTTED_SEGMENT_BLUE',
            "DOTTED_SEGMENT_GREEN": 'bg-DOTTED_SEGMENT_GREEN',
            "DOTTED_SEGMENT_MAGENTA": 'bg-DOTTED_SEGMENT_MAGENTA',
            "DOTTED_SEGMENT_ORANGE": 'bg-DOTTED_SEGMENT_ORANGE',
            "DOTTED_SEGMENT_PURPLE": 'bg-DOTTED_SEGMENT_PURPLE',
            "DOTTED_SEGMENT_RED": 'bg-DOTTED_SEGMENT_RED',
            "DOTTED_SEGMENT_YELLOW": 'bg-DOTTED_SEGMENT_YELLOW',
        };
        this._seriesValue = '';
        this._isTimeLiveItem = false;
    }
    /**
     * @return {?}
     */
    get seriesStyle() {
        return this._seriesStyle;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesStyle(value) {
        /** @type {?} */
        const newStyle = this.styleClassMap[value];
        if (newStyle) {
            if (this._seriesStyle) {
                /** @type {?} */
                const prevStyle = this.styleClassMap[this._seriesStyle];
                this.circle.nativeElement.classList.remove(prevStyle);
            }
            this.circle.nativeElement.classList.add(newStyle);
            this._seriesStyle = value;
        }
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
        // styleClassMap
    }
    /**
     * @return {?}
     */
    get seriesId() {
        return this._seriesId;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesId(value) {
        this._seriesId = value;
    }
    /**
     * @return {?}
     */
    get seriesValue() {
        return this._seriesValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesValue(value) {
        this._seriesValue = value;
        if (!((/** @type {?} */ (this.cd))).destroyed) {
            this.cd.markForCheck();
        }
    }
    // <img  id="circle" #circle src="assets/images/Rolling.gif">
    /**
     * @return {?}
     */
    ngOnInit() {
        if (this._isTimeLiveItem) {
            // this.circle.nativeElement.classList.add("timeValue");
            this.circle.nativeElement.remove();
            this.seriesValue = '';
            this.labelValue.nativeElement.classList.add("timeValue");
        }
        else {
            // this.seriesValue = '-32.798.284.008';
            // this.circle.nativeElement.classList.add("bg-DOTTED_SEGMENT_RED");
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set isTimeLiveItem(value) {
        this._isTimeLiveItem = StringUtils.isTrue(value);
    }
    /**
     * @return {?}
     */
    get isTimeLiveItem() {
        return this._isTimeLiveItem;
    }
    /**
     * @return {?}
     */
    removeLiveValue() {
        this.elem.nativeElement.remove();
    }
}
ILMSeriesLiveValue.decorators = [
    { type: Component, args: [{
                selector: 'ILMSeriesLiveValue',
                template: `
         <HBox #hboxContainer  width="100%"> 
            <div    #circle class='circle' ></div>
            <div class='labelValue' #labelValue >{{seriesValue}}</div>
        </HBox>
        `,
                changeDetection: ChangeDetectionStrategy.OnPush,
                styles: [`

        .labelValue{
            position:relative;
            font-size:11px;
            top:-2px;
        }
        .timeValue{
            padding-right :10px !important;
        }
        .circle{
            margin-right:2px !important;
        }
        
        .bg-CONT_SEGMENT_BLACK {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -10px -10px;
        }
        
        .bg-CONT_SEGMENT_BLUE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -42px -10px;
        }
        
        .bg-CONT_SEGMENT_BOLD_RED {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -10px -42px;
        }
        
        .bg-CONT_SEGMENT_GREEN {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -42px -42px;
        }
        
        .bg-CONT_SEGMENT_MAGENTA {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -74px -10px;
        }
        
        .bg-CONT_SEGMENT_ORANGE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -74px -42px;
        }
        
        .bg-CONT_SEGMENT_PURPLE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -10px -74px;
        }
        
        .bg-CONT_SEGMENT_RED {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -42px -74px;
        }
        
        .bg-CONT_SEGMENT_YELLOW {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -74px -74px;
        }
        
        .bg-DASHED_SEGMENT_BLACK {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -106px -10px;
        }
        
        .bg-DASHED_SEGMENT_BLUE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -106px -42px;
        }
        
        .bg-DASHED_SEGMENT_GREEN {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -106px -74px;
        }
        
        .bg-DASHED_SEGMENT_MAGENTA {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -10px -106px;
        }
        
        .bg-DASHED_SEGMENT_ORANGE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -42px -106px;
        }
        
        .bg-DASHED_SEGMENT_PURPLE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -74px -106px;
        }
        
        .bg-DASHED_SEGMENT_RED {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -106px -106px;
        }
        
        .bg-DASHED_SEGMENT_YELLOW {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -138px -10px;
        }
        
        .bg-DOTTED_SEGMENT_BLACK {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -138px -42px;
        }
        
        .bg-DOTTED_SEGMENT_BLUE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -138px -74px;
        }
        
        .bg-DOTTED_SEGMENT_GREEN {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -138px -106px;
        }
        
        .bg-DOTTED_SEGMENT_MAGENTA {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -10px -138px;
        }
        
        .bg-DOTTED_SEGMENT_ORANGE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -42px -138px;
        }
        
        .bg-DOTTED_SEGMENT_PURPLE {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -74px -138px;
        }
        
        .bg-DOTTED_SEGMENT_RED {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -106px -138px;
        }
        
        .bg-DOTTED_SEGMENT_YELLOW {
            width: 12px; height: 12px;
            background: url('assets/ILM/sprites/liveValues.png') -138px -138px;
        }
        `]
            }] }
];
/** @nocollapse */
ILMSeriesLiveValue.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: ChangeDetectorRef }
];
ILMSeriesLiveValue.propDecorators = {
    hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
    circle: [{ type: ViewChild, args: ['circle',] }],
    labelValue: [{ type: ViewChild, args: ['labelValue',] }],
    seriesStyle: [{ type: Input, args: ['seriesStyle',] }],
    seriesValue: [{ type: Input, args: ['seriesValue',] }],
    isTimeLiveItem: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.circle;
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.labelValue;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.styleClassMap;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesValue;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesId;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesStyle;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._isTimeLiveItem;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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