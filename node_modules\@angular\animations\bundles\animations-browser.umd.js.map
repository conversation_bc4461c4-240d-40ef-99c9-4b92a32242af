{"version": 3, "file": "animations-browser.umd.js", "sources": ["../../../../../../../../../../external/npm/node_modules/tslib/tslib.es6.js", "../../../../../../packages/animations/browser/src/render/shared.ts", "../../../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../../../packages/animations/browser/src/util.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../../../packages/animations/browser/src/render/special_cased_styles.ts", "../../../../../../packages/animations/browser/src/render/css_keyframes/element_animation_style_handler.ts", "../../../../../../packages/animations/browser/src/render/css_keyframes/css_keyframes_player.ts", "../../../../../../packages/animations/browser/src/render/css_keyframes/direct_style_player.ts", "../../../../../../packages/animations/browser/src/render/css_keyframes/css_keyframes_driver.ts", "../../../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../../../packages/animations/browser/src/private_export.ts", "../../../../../../packages/animations/browser/src/browser.ts", "../../../../../../packages/animations/browser/public_api.ts", "../../../../../../packages/animations/browser/index.ts", "../../../../../../packages/animations/browser/browser.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ÉµAnimationGroupPlayer, ÉµPRE_STYLE as PRE_STYLE, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\n// We don't include ambient node types here since @angular/animations/browser\n// is meant to target the browser so technically it should not depend on node\n// types. `process` is just declared locally here as a result.\ndeclare const process: any;\n\nexport function isBrowser() {\n  return (typeof window !== 'undefined' && typeof window.document !== 'undefined');\n}\n\nexport function isNode() {\n  return (typeof process !== 'undefined');\n}\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ÉµAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ÉµStyleData[], preStyles: ÉµStyleData = {},\n    postStyles: ÉµStyleData = {}): ÉµStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ÉµStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ÉµStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ÉµStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case PRE_STYLE:\n            normalizedValue = preStyles[prop];\n            break;\n\n          case AUTO_STYLE:\n            normalizedValue = postStyles[prop];\n            break;\n\n          default:\n            normalizedValue =\n                normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n            break;\n        }\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName: string, player: AnimationPlayer): AnimationEvent {\n  const totalTime = player.totalTime;\n  const disabled = (player as any).disabled ? true : false;\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime, disabled);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0, disabled?: boolean): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime, disabled: !!disabled};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\n// Define utility methods for browsers and platform-server(domino) where Element\n// and utility methods exist.\nconst _isNode = isNode();\nif (_isNode || typeof Element !== 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (_isNode || Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nfunction containsVendorPrefix(prop: string): boolean {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit';  // webkit or Webkit\n}\n\nlet _CACHED_BODY: {style: any}|null = null;\nlet _IS_WEBKIT = false;\nexport function validateStyleProperty(prop: string): boolean {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY !.style ? ('WebkitAppearance' in _CACHED_BODY !.style) : false;\n  }\n\n  let result = true;\n  if (_CACHED_BODY !.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY !.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n      result = camelProp in _CACHED_BODY !.style;\n    }\n  }\n\n  return result;\n}\n\nexport function getBodyNode(): any|null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n\nexport function hypenatePropsObject(object: {[key: string]: any}): {[key: string]: any} {\n  const newObj: {[key: string]: any} = {};\n  Object.keys(object).forEach(prop => {\n    const newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n    newObj[newProp] = object[prop];\n  });\n  return newObj;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\nimport {Injectable} from '@angular/core';\n\nimport {containsElement, invokeQuery, matchesElement, validateStyleProperty} from './shared';\n\n/**\n * @publicApi\n */\n@Injectable()\nexport class NoopAnimationDriver implements AnimationDriver {\n  validateStyleProperty(prop: string): boolean { return validateStyleProperty(prop); }\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = [],\n      scrubberAccessRequested?: boolean): AnimationPlayer {\n    return new NoopAnimationPlayer(duration, delay);\n  }\n}\n\n/**\n * @publicApi\n */\nexport abstract class AnimationDriver {\n  static NOOP: AnimationDriver = new NoopAnimationDriver();\n\n  abstract validateStyleProperty(prop: string): boolean;\n\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing?: string|null, previousPlayers?: any[], scrubberAccessRequested?: boolean): any;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimateTimings, AnimationMetadata, AnimationMetadataType, AnimationOptions, sequence, ÉµStyleData} from '@angular/animations';\nimport {Ast as AnimationAst, AstVisitor as AnimationAstVisitor} from './dsl/animation_ast';\nimport {AnimationDslVisitor} from './dsl/animation_dsl_visitor';\nimport {isNode} from './render/shared';\n\nexport const ONE_SECOND = 1000;\n\nexport const SUBSTITUTION_EXPR_START = '{{';\nexport const SUBSTITUTION_EXPR_END = '}}';\nexport const ENTER_CLASSNAME = 'ng-enter';\nexport const LEAVE_CLASSNAME = 'ng-leave';\nexport const ENTER_SELECTOR = '.ng-enter';\nexport const LEAVE_SELECTOR = '.ng-leave';\nexport const NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const NG_ANIMATING_SELECTOR = '.ng-animating';\n\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const matches = (value as string).match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:  // ms or something else\n      return value;\n  }\n}\n\nexport function resolveTiming(\n    timings: string | number | AnimateTimings, errors: any[], allowNegativeValues?: boolean) {\n  return timings.hasOwnProperty('duration') ?\n      <AnimateTimings>timings :\n      parseTimeExpression(<string|number>timings, errors, allowNegativeValues);\n}\n\nfunction parseTimeExpression(\n    exp: string | number, errors: string[], allowNegativeValues?: boolean): AnimateTimings {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration: number;\n  let delay: number = 0;\n  let easing: string = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(`The provided timing value \"${exp}\" is invalid.`);\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = <number>exp;\n  }\n\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(`Duration values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(`Delay values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n    }\n  }\n\n  return {duration, delay, easing};\n}\n\nexport function copyObj(\n    obj: {[key: string]: any}, destination: {[key: string]: any} = {}): {[key: string]: any} {\n  Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n  return destination;\n}\n\nexport function normalizeStyles(styles: ÉµStyleData | ÉµStyleData[]): ÉµStyleData {\n  const normalizedStyles: ÉµStyleData = {};\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, false, normalizedStyles));\n  } else {\n    copyStyles(styles, false, normalizedStyles);\n  }\n  return normalizedStyles;\n}\n\nexport function copyStyles(\n    styles: ÉµStyleData, readPrototype: boolean, destination: ÉµStyleData = {}): ÉµStyleData {\n  if (readPrototype) {\n    // we make use of a for-in loop so that the\n    // prototypically inherited properties are\n    // revealed from the backFill map\n    for (let prop in styles) {\n      destination[prop] = styles[prop];\n    }\n  } else {\n    copyObj(styles, destination);\n  }\n  return destination;\n}\n\nfunction getStyleAttributeString(element: any, key: string, value: string) {\n  // Return the key-value pair string to be added to the style attribute for the\n  // given CSS style key.\n  if (value) {\n    return key + ':' + value + ';';\n  } else {\n    return '';\n  }\n}\n\nfunction writeStyleAttribute(element: any) {\n  // Read the style property of the element and manually reflect it to the\n  // style attribute. This is needed because Domino on platform-server doesn't\n  // understand the full set of allowed CSS properties and doesn't reflect some\n  // of them automatically.\n  let styleAttrValue = '';\n  for (let i = 0; i < element.style.length; i++) {\n    const key = element.style.item(i);\n    styleAttrValue += getStyleAttributeString(element, key, element.style.getPropertyValue(key));\n  }\n  for (const key in element.style) {\n    // Skip internal Domino properties that don't need to be reflected.\n    if (!element.style.hasOwnProperty(key) || key.startsWith('_')) {\n      continue;\n    }\n    const dashKey = camelCaseToDashCase(key);\n    styleAttrValue += getStyleAttributeString(element, dashKey, element.style[key]);\n  }\n  element.setAttribute('style', styleAttrValue);\n}\n\nexport function setStyles(element: any, styles: ÉµStyleData, formerStyles?: {[key: string]: any}) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const camelProp = dashCaseToCamelCase(prop);\n      if (formerStyles && !formerStyles.hasOwnProperty(prop)) {\n        formerStyles[prop] = element.style[camelProp];\n      }\n      element.style[camelProp] = styles[prop];\n    });\n    // On the server set the 'style' attribute since it's not automatically reflected.\n    if (isNode()) {\n      writeStyleAttribute(element);\n    }\n  }\n}\n\nexport function eraseStyles(element: any, styles: ÉµStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    });\n    // On the server set the 'style' attribute since it's not automatically reflected.\n    if (isNode()) {\n      writeStyleAttribute(element);\n    }\n  }\n}\n\nexport function normalizeAnimationEntry(steps: AnimationMetadata | AnimationMetadata[]):\n    AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps as AnimationMetadata;\n}\n\nexport function validateStyleParams(\n    value: string | number, options: AnimationOptions, errors: any[]) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(\n            `Unable to resolve the local animation param ${varName} in the given list of values`);\n      }\n    });\n  }\n}\n\nconst PARAM_REGEX =\n    new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\nexport function extractStyleParams(value: string | number): string[] {\n  let params: string[] = [];\n  if (typeof value === 'string') {\n    const val = value.toString();\n\n    let match: any;\n    while (match = PARAM_REGEX.exec(val)) {\n      params.push(match[1] as string);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\n\nexport function interpolateParams(\n    value: string | number, params: {[name: string]: any}, errors: any[]): string|number {\n  const original = value.toString();\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (!params.hasOwnProperty(varName)) {\n      errors.push(`Please provide a value for the animation param ${varName}`);\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n\nexport function iteratorToArray(iterator: any): any[] {\n  const arr: any[] = [];\n  let item = iterator.next();\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n  return arr;\n}\n\nexport function mergeAnimationOptions(\n    source: AnimationOptions, destination: AnimationOptions): AnimationOptions {\n  if (source.params) {\n    const p0 = source.params;\n    if (!destination.params) {\n      destination.params = {};\n    }\n    const p1 = destination.params;\n    Object.keys(p0).forEach(param => {\n      if (!p1.hasOwnProperty(param)) {\n        p1[param] = p0[param];\n      }\n    });\n  }\n  return destination;\n}\n\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n\nfunction camelCaseToDashCase(input: string): string {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function allowPreviousPlayerStylesMerge(duration: number, delay: number) {\n  return duration === 0 || delay === 0;\n}\n\nexport function balancePreviousStylesIntoKeyframes(\n    element: any, keyframes: {[key: string]: any}[], previousStyles: {[key: string]: any}) {\n  const previousStyleProps = Object.keys(previousStyles);\n  if (previousStyleProps.length && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps: string[] = [];\n    previousStyleProps.forEach(prop => {\n      if (!startingKeyframe.hasOwnProperty(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe[prop] = previousStyles[prop];\n    });\n\n    if (missingStyleProps.length) {\n      // tslint:disable-next-line\n      for (var i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach(function(prop) { kf[prop] = computeStyle(element, prop); });\n      }\n    }\n  }\n  return keyframes;\n}\n\nexport function visitDslNode(\n    visitor: AnimationDslVisitor, node: AnimationMetadata, context: any): any;\nexport function visitDslNode(\n    visitor: AnimationAstVisitor, node: AnimationAst<AnimationMetadataType>, context: any): any;\nexport function visitDslNode(visitor: any, node: any, context: any): any {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n  }\n}\n\nexport function computeStyle(element: any, prop: string): string {\n  return (<any>window.getComputedStyle(element))[prop];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn =\n    (fromState: any, toState: any, element: any, params: {[key: string]: any}) => boolean;\n\nexport function parseTransitionExpr(\n    transitionValue: string | TransitionMatcherFn, errors: string[]): TransitionMatcherFn[] {\n  const expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    (<string>transitionValue)\n        .split(/\\s*,\\s*/)\n        .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(<TransitionMatcherFn>transitionValue);\n  }\n  return expressions;\n}\n\nfunction parseInnerTransitionStr(\n    eventStr: string, expressions: TransitionMatcherFn[], errors: string[]) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result as string;\n  }\n\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n    return expressions;\n  }\n\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n\nfunction parseAnimationAlias(alias: string, errors: string[]): string|TransitionMatcherFn {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(`The transition alias value \"${alias}\" is not supported`);\n      return '* => *';\n  }\n}\n\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set<string>(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set<string>(['false', '0']);\n\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n\n  return (fromState: any, toState: any): boolean => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, style, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {ENTER_SELECTOR, LEAVE_SELECTOR, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, copyObj, extractStyleParams, iteratorToArray, normalizeAnimationEntry, resolveTiming, validateStyleParams, visitDslNode} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationDslVisitor} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nexport function buildAnimationAst(\n    driver: AnimationDriver, metadata: AnimationMetadata | AnimationMetadata[],\n    errors: any[]): Ast<AnimationMetadataType> {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors);\n}\n\nconst ROOT_SELECTOR = '';\n\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n  constructor(private _driver: AnimationDriver) {}\n\n  build(metadata: AnimationMetadata|AnimationMetadata[], errors: any[]):\n      Ast<AnimationMetadataType> {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    return <Ast<AnimationMetadataType>>visitDslNode(\n        this, normalizeAnimationEntry(metadata), context);\n  }\n\n  private _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = {};\n    context.collectedStyles[ROOT_SELECTOR] = {};\n    context.currentTime = 0;\n  }\n\n  visitTrigger(metadata: AnimationTriggerMetadata, context: AnimationAstBuilderContext):\n      TriggerAst {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states: StateAst[] = [];\n    const transitions: TransitionAst[] = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(\n          'animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n    }\n\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def as AnimationStateMetadata;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def as AnimationTransitionMetadata, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(\n            'only state() and transition() definitions can sit inside of a trigger()');\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name, states, transitions, queryCount, depCount,\n      options: null\n    };\n  }\n\n  visitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = (metadata.options && metadata.options.params) || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set<string>();\n      const params = astParams || {};\n      styleAst.styles.forEach(value => {\n        if (isObject(value)) {\n          const stylesObj = value as any;\n          Object.keys(stylesObj).forEach(prop => {\n            extractStyleParams(stylesObj[prop]).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        const missingSubsArr = iteratorToArray(missingSubs.values());\n        context.errors.push(\n            `state(\"${metadata.name}\", ...) must define default values for all the following style substitutions: ${missingSubsArr.join(', ')}`);\n      }\n    }\n\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {params: astParams} : null\n    };\n  }\n\n  visitTransition(metadata: AnimationTransitionMetadata, context: AnimationAstBuilderContext):\n      TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitSequence(metadata: AnimationSequenceMetadata, context: AnimationAstBuilderContext):\n      SequenceAst {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimate(metadata: AnimationAnimateMetadata, context: AnimationAstBuilderContext):\n      AnimateAst {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n\n    let styleAst: StyleAst|KeyframesAst;\n    let styleMetadata: AnimationMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata as AnimationKeyframesSequenceMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles as AnimationStyleMetadata;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n\n  visitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n\n  private _makeStyleAst(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext):\n      StyleAst {\n    const styles: (ÉµStyleData | string)[] = [];\n    if (Array.isArray(metadata.styles)) {\n      (metadata.styles as(ÉµStyleData | string)[]).forEach(styleTuple => {\n        if (typeof styleTuple == 'string') {\n          if (styleTuple == AUTO_STYLE) {\n            styles.push(styleTuple as string);\n          } else {\n            context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n          }\n        } else {\n          styles.push(styleTuple as ÉµStyleData);\n        }\n      });\n    } else {\n      styles.push(metadata.styles);\n    }\n\n    let containsDynamicStyles = false;\n    let collectedEasing: string|null = null;\n    styles.forEach(styleData => {\n      if (isObject(styleData)) {\n        const styleMap = styleData as ÉµStyleData;\n        const easing = styleMap['easing'];\n        if (easing) {\n          collectedEasing = easing as string;\n          delete styleMap['easing'];\n        }\n        if (!containsDynamicStyles) {\n          for (let prop in styleMap) {\n            const value = styleMap[prop];\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset, containsDynamicStyles,\n      options: null\n    };\n  }\n\n  private _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple == 'string') return;\n\n      Object.keys(tuple).forEach(prop => {\n        if (!this._driver.validateStyleProperty(prop)) {\n          context.errors.push(\n              `The provided animation property \"${prop}\" is not a supported CSS property for animations`);\n          return;\n        }\n\n        const collectedStyles = context.collectedStyles[context.currentQuerySelector !];\n        const collectedEntry = collectedStyles[prop];\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime &&\n              endTime <= collectedEntry.endTime) {\n            context.errors.push(\n                `The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles[prop] = {startTime, endTime};\n        }\n\n        if (context.options) {\n          validateStyleParams(tuple[prop], context.options, context.errors);\n        }\n      });\n    });\n  }\n\n  visitKeyframes(metadata: AnimationKeyframesSequenceMetadata, context: AnimationAstBuilderContext):\n      KeyframesAst {\n    const ast: KeyframesAst = {type: AnimationMetadataType.Keyframes, styles: [], options: null};\n    if (!context.currentAnimateTimings) {\n      context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n      return ast;\n    }\n\n    const MAX_KEYFRAME_OFFSET = 1;\n\n    let totalKeyframesWithOffsets = 0;\n    const offsets: number[] = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset: number = 0;\n\n    const keyframes: StyleAst[] = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal: number|null =\n          style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(`Please ensure that all keyframe offsets are in order`);\n    }\n\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings !;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n\n      ast.styles.push(kf);\n    });\n\n    return ast;\n  }\n\n  visitReference(metadata: AnimationReferenceMetadata, context: AnimationAstBuilderContext):\n      ReferenceAst {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimateChild(metadata: AnimationAnimateChildMetadata, context: AnimationAstBuilderContext):\n      AnimateChildAst {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimateRef(metadata: AnimationAnimateRefMetadata, context: AnimationAstBuilderContext):\n      AnimateRefAst {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const parentSelector = context.currentQuerySelector !;\n    const options = (metadata.options || {}) as AnimationQueryOptions;\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector =\n        parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n    getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional, includeSelf, animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitStagger(metadata: AnimationStaggerMetadata, context: AnimationAstBuilderContext):\n      StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(`stagger() can only be used inside of query()`);\n    }\n    const timings = metadata.timings === 'full' ?\n        {duration: 0, delay: 0, easing: 'full'} :\n        resolveTiming(metadata.timings, context.errors, true);\n\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context), timings,\n      options: null\n    };\n  }\n}\n\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  // the :enter and :leave selectors are filled in at runtime during timeline building\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n                 .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n                 .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n\n\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any}|null {\n  return obj ? copyObj(obj) : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number; endTime: number;\n};\n\nexport class AnimationAstBuilderContext {\n  public queryCount: number = 0;\n  public depCount: number = 0;\n  public currentTransition: AnimationTransitionMetadata|null = null;\n  public currentQuery: AnimationQueryMetadata|null = null;\n  public currentQuerySelector: string|null = null;\n  public currentAnimateTimings: TimingAst|null = null;\n  public currentTime: number = 0;\n  public collectedStyles: {[selectorName: string]: {[propName: string]: StyleTimeTuple}} = {};\n  public options: AnimationOptions|null = null;\n  constructor(public errors: any[]) {}\n}\n\nfunction consumeOffset(styles: ÉµStyleData | string | (ÉµStyleData | string)[]): number|null {\n  if (typeof styles == 'string') return null;\n\n  let offset: number|null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n        const obj = styleTuple as ÉµStyleData;\n        offset = parseFloat(obj['offset'] as string);\n        delete obj['offset'];\n      }\n    });\n  } else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n    const obj = styles as ÉµStyleData;\n    offset = parseFloat(obj['offset'] as string);\n    delete obj['offset'];\n  }\n  return offset;\n}\n\nfunction isObject(value: any): boolean {\n  return !Array.isArray(value) && typeof value == 'object';\n}\n\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: any[]) {\n  let timings: AnimateTimings|null = null;\n  if (value.hasOwnProperty('duration')) {\n    timings = value as AnimateTimings;\n  } else if (typeof value == 'number') {\n    const duration = resolveTiming(value as number, errors).duration;\n    return makeTimingAst(duration as number, 0, '');\n  }\n\n  const strValue = value as string;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '') as any;\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast as DynamicTimingAst;\n  }\n\n  timings = timings || resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = copyObj(options);\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']) !;\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n\nfunction makeTimingAst(duration: number, delay: number, easing: string | null): TimingAst {\n  return {duration, delay, easing};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ÉµStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: ÉµStyleData[];\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string|null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n\nexport function createTimelineInstruction(\n    element: any, keyframes: ÉµStyleData[], preStyleProps: string[], postStyleProps: string[],\n    duration: number, delay: number, easing: string | null = null,\n    subTimeline: boolean = false): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay, easing, subTimeline\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport class ElementInstructionMap {\n  private _map = new Map<any, AnimationTimelineInstruction[]>();\n\n  consume(element: any): AnimationTimelineInstruction[] {\n    let instructions = this._map.get(element);\n    if (instructions) {\n      this._map.delete(element);\n    } else {\n      instructions = [];\n    }\n    return instructions;\n  }\n\n  append(element: any, instructions: AnimationTimelineInstruction[]) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n\n  has(element: any): boolean { return this._map.has(element); }\n\n  clear() { this._map.clear(); }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationMetadataType, AnimationOptions, AnimationQueryOptions, ÉµPRE_STYLE as PRE_STYLE, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue, visitDslNode} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, AstVisitor, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTimelineInstruction, createTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of prototypical inheritance, AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two wihtin a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergesort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill object. Given that each of the keyframe\n * styles are objects that prototypically inhert from the backFill object, this means that if a\n * value is added into the backFill then it will automatically propagate any missing values to all\n * keyframes. Therefore the missing `height` value will be properly filled into the already\n * processed keyframes.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * (For prototypically-inherited contents to be detected a `for(i in obj)` loop must be used.)\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nexport function buildAnimationTimelines(\n    driver: AnimationDriver, rootElement: any, ast: Ast<AnimationMetadataType>,\n    enterClassName: string, leaveClassName: string, startingStyles: ÉµStyleData = {},\n    finalStyles: ÉµStyleData = {}, options: AnimationOptions,\n    subInstructions?: ElementInstructionMap, errors: any[] = []): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n      driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles,\n      options, subInstructions, errors);\n}\n\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n  buildKeyframes(\n      driver: AnimationDriver, rootElement: any, ast: Ast<AnimationMetadataType>,\n      enterClassName: string, leaveClassName: string, startingStyles: ÉµStyleData,\n      finalStyles: ÉµStyleData, options: AnimationOptions, subInstructions?: ElementInstructionMap,\n      errors: any[] = []): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(\n        driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    visitDslNode(this, ast, context);\n\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    if (timelines.length && Object.keys(finalStyles).length) {\n      const tl = timelines[timelines.length - 1];\n      if (!tl.allowOnlyTimelineStyles()) {\n        tl.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n                              [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n  }\n\n  visitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const elementInstructions = context.subInstructions.consume(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(\n          elementInstructions, innerContext, innerContext.options as AnimateChildOptions);\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n\n  visitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n\n  private _visitSubInstructions(\n      instructions: AnimationTimelineInstruction[], context: AnimationTimelineContext,\n      options: AnimateChildOptions): number {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings =\n            context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime =\n            Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n\n  visitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n\n  visitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx));\n\n      // this is here just incase the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const innerTimelines: TimelineBuilder[] = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(\n        timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n\n  private _visitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if ((ast as DynamicTimingAst).dynamic) {\n      const strValue = (ast as DynamicTimingAst).strValue;\n      const timingValue =\n          context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n\n  visitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style as StyleAst, context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n\n  visitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings !;\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.getCurrentStyleProperties().length) {\n      timeline.forwardFrame();\n    }\n\n    const easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const currentAnimateTimings = context.currentAnimateTimings !;\n    const startTime = (context.currentTimeline !).duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach(step => {\n      const offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n\n  visitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = (ast.options || {}) as AnimationQueryOptions;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode.type === AnimationMetadataType.Style ||\n                  (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(\n        ast.selector, ast.originalSelector, ast.limit, ast.includeSelf,\n        options.optional ? true : false, context.errors);\n\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline: TimelineBuilder|null = null;\n    elms.forEach((element, i) => {\n\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      visitDslNode(this, ast.animation, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const parentContext = context.parentContext !;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n        (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number; value: string | number;\n};\n\nconst DEFAULT_NOOP_PREVIOUS_NODE = <Ast<AnimationMetadataType>>{};\nexport class AnimationTimelineContext {\n  public parentContext: AnimationTimelineContext|null = null;\n  public currentTimeline: TimelineBuilder;\n  public currentAnimateTimings: AnimateTimings|null = null;\n  public previousNode: Ast<AnimationMetadataType> = DEFAULT_NOOP_PREVIOUS_NODE;\n  public subContextCount = 0;\n  public options: AnimationOptions = {};\n  public currentQueryIndex: number = 0;\n  public currentQueryTotal: number = 0;\n  public currentStaggerTime: number = 0;\n\n  constructor(\n      private _driver: AnimationDriver, public element: any,\n      public subInstructions: ElementInstructionMap, private _enterClassName: string,\n      private _leaveClassName: string, public errors: any[], public timelines: TimelineBuilder[],\n      initialTimeline?: TimelineBuilder) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n\n  get params() { return this.options.params; }\n\n  updateOptions(options: AnimationOptions|null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const newOptions = options as any;\n    let optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      (optionsToUpdate as any).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate: {[name: string]: any} = optionsToUpdate.params !;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n\n  private _copyOptions() {\n    const options: AnimationOptions = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params: {[name: string]: any} = options['params'] = {};\n        Object.keys(oldParams).forEach(name => { params[name] = oldParams[name]; });\n      }\n    }\n    return options;\n  }\n\n  createSubContext(options: AnimationOptions|null = null, element?: any, newTime?: number):\n      AnimationTimelineContext {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(\n        this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName,\n        this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n\n  transformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n\n  appendInstructionToTimeline(\n      instruction: AnimationTimelineInstruction, duration: number|null,\n      delay: number|null): AnimateTimings {\n    const updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(\n        this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps,\n        instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n\n  incrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n\n  delayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n\n  invokeQuery(\n      selector: string, originalSelector: string, limit: number, includeSelf: boolean,\n      optional: boolean, errors: any[]): any[] {\n    let results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {  // if :self is only used then the selector is empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) :\n                               elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(\n          `\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n    }\n    return results;\n  }\n}\n\n\nexport class TimelineBuilder {\n  public duration: number = 0;\n  // TODO(issue/24571): remove '!'.\n  public easing !: string | null;\n  private _previousKeyframe: ÉµStyleData = {};\n  private _currentKeyframe: ÉµStyleData = {};\n  private _keyframes = new Map<number, ÉµStyleData>();\n  private _styleSummary: {[prop: string]: StyleAtTime} = {};\n  private _localTimelineStyles: ÉµStyleData;\n  private _globalTimelineStyles: ÉµStyleData;\n  private _pendingStyles: ÉµStyleData = {};\n  private _backFill: ÉµStyleData = {};\n  private _currentEmptyStepKeyframe: ÉµStyleData|null = null;\n\n  constructor(\n      private _driver: AnimationDriver, public element: any, public startTime: number,\n      private _elementTimelineStylesLookup?: Map<any, ÉµStyleData>) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ÉµStyleData>();\n    }\n\n    this._localTimelineStyles = Object.create(this._backFill, {});\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element) !;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n\n  containsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.getCurrentStyleProperties().length > 0;\n      default:\n        return true;\n    }\n  }\n\n  getCurrentStyleProperties(): string[] { return Object.keys(this._currentKeyframe); }\n\n  get currentTime() { return this.startTime + this.duration; }\n\n  delayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n\n  fork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n        this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n\n  private _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration) !;\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = Object.create(this._backFill, {});\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n\n  forwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n\n  private _updateStyle(prop: string, value: string|number) {\n    this._localTimelineStyles[prop] = value;\n    this._globalTimelineStyles[prop] = value;\n    this._styleSummary[prop] = {time: this.currentTime, value};\n  }\n\n  allowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n\n  applyEmptyStep(easing: string|null) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    Object.keys(this._globalTimelineStyles).forEach(prop => {\n      this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n      this._currentKeyframe[prop] = AUTO_STYLE;\n    });\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n\n  setStyles(\n      input: (ÉµStyleData|string)[], easing: string|null, errors: any[],\n      options?: AnimationOptions) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    const params = (options && options.params) || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    Object.keys(styles).forEach(prop => {\n      const val = interpolateParams(styles[prop], params, errors);\n      this._pendingStyles[prop] = val;\n      if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n        this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n            this._globalTimelineStyles[prop] :\n            AUTO_STYLE;\n      }\n      this._updateStyle(prop, val);\n    });\n  }\n\n  applyStylesToKeyframe() {\n    const styles = this._pendingStyles;\n    const props = Object.keys(styles);\n    if (props.length == 0) return;\n\n    this._pendingStyles = {};\n\n    props.forEach(prop => {\n      const val = styles[prop];\n      this._currentKeyframe[prop] = val;\n    });\n\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      if (!this._currentKeyframe.hasOwnProperty(prop)) {\n        this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n      }\n    });\n  }\n\n  snapshotCurrentStyles() {\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      const val = this._localTimelineStyles[prop];\n      this._pendingStyles[prop] = val;\n      this._updateStyle(prop, val);\n    });\n  }\n\n  getFinalKeyframe() { return this._keyframes.get(this.duration); }\n\n  get properties() {\n    const properties: string[] = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n\n  mergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    Object.keys(timeline._styleSummary).forEach(prop => {\n      const details0 = this._styleSummary[prop];\n      const details1 = timeline._styleSummary[prop];\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n\n  buildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set<string>();\n    const postStyleProps = new Set<string>();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let finalKeyframes: ÉµStyleData[] = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = copyStyles(keyframe, true);\n      Object.keys(finalKeyframe).forEach(prop => {\n        const value = finalKeyframe[prop];\n        if (value == PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value == AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe['offset'] = time / this.duration;\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const preProps: string[] = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const postProps: string[] = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = copyObj(kf0);\n      kf0['offset'] = 0;\n      kf1['offset'] = 1;\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n        this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime,\n        this.easing, false);\n  }\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\n  public timings: AnimateTimings;\n\n  constructor(\n      driver: AnimationDriver, public element: any, public keyframes: ÉµStyleData[],\n      public preStyleProps: string[], public postStyleProps: string[], timings: AnimateTimings,\n      private _stretchStartingKeyframe: boolean = false) {\n    super(driver, element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n\n  containsAnimation(): boolean { return this.keyframes.length > 1; }\n\n  buildKeyframes(): AnimationTimelineInstruction {\n    let keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes: ÉµStyleData[] = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = copyStyles(keyframes[0], false);\n      newFirstKeyframe['offset'] = 0;\n      newKeyframes.push(newFirstKeyframe);\n\n      const oldFirstKeyframe = copyStyles(keyframes[0], false);\n      oldFirstKeyframe['offset'] = roundOffset(startingGap);\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still renderered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = copyStyles(keyframes[i], false);\n        const oldOffset = kf['offset'] as number;\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n        this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing,\n        true);\n  }\n}\n\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n\nfunction flattenStyles(input: (ÉµStyleData | string)[], allStyles: ÉµStyleData) {\n  const styles: ÉµStyleData = {};\n  let allProperties: string[];\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || Object.keys(allStyles);\n      allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n    } else {\n      copyStyles(token as ÉµStyleData, false, styles);\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadata, AnimationMetadataType, AnimationOptions, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles} from '../util';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nexport class Animation {\n  private _animationAst: Ast<AnimationMetadataType>;\n  constructor(private _driver: AnimationDriver, input: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(_driver, input, errors);\n    if (errors.length) {\n      const errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    this._animationAst = ast;\n  }\n\n  buildTimelines(\n      element: any, startingStyles: ÉµStyleData|ÉµStyleData[],\n      destinationStyles: ÉµStyleData|ÉµStyleData[], options: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[] {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) :\n                                                  <ÉµStyleData>startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) :\n                                                    <ÉµStyleData>destinationStyles;\n    const errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(\n        this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest,\n        options, subInstructions, errors);\n    if (errors.length) {\n      const errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: string[]): string;\n  abstract normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string;\n}\n\n/**\n * @publicApi\n */\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string { return propertyName; }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\n\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    let unit: string = '';\n    const strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n\nconst DIMENSIONAL_PROP_MAP = makeBooleanMap(\n    'width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n\nfunction makeBooleanMap(keys: string[]): {[key: string]: boolean} {\n  const map: {[key: string]: boolean} = {};\n  keys.forEach(key => map[key] = true);\n  return map;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ÉµStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ÉµStyleData;\n  toState: string;\n  toStyles: ÉµStyleData;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, {[prop: string]: boolean}>;\n  postStyleProps: Map<any, {[prop: string]: boolean}>;\n  totalTime: number;\n  errors?: any[];\n}\n\nexport function createTransitionInstruction(\n    element: any, triggerName: string, fromState: string, toState: string,\n    isRemovalTransition: boolean, fromStyles: ÉµStyleData, toStyles: ÉµStyleData,\n    timelines: AnimationTimelineInstruction[], queriedElements: any[],\n    preStyleProps: Map<any, {[prop: string]: boolean}>,\n    postStyleProps: Map<any, {[prop: string]: boolean}>, totalTime: number,\n    errors?: any[]): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationOptions, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {copyObj, interpolateParams, iteratorToArray, mergeAnimationOptions} from '../util';\n\nimport {StyleAst, TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {AnimationTransitionInstruction, createTransitionInstruction} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst EMPTY_OBJECT = {};\n\nexport class AnimationTransitionFactory {\n  constructor(\n      private _triggerName: string, public ast: TransitionAst,\n      private _stateStyles: {[stateName: string]: AnimationStateStyles}) {}\n\n  match(currentState: any, nextState: any, element: any, params: {[key: string]: any}): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n\n  buildStyles(stateName: string, params: {[key: string]: any}, errors: any[]) {\n    const backupStateStyler = this._stateStyles['*'];\n    const stateStyler = this._stateStyles[stateName];\n    const backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};\n    return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;\n  }\n\n  build(\n      driver: AnimationDriver, element: any, currentState: any, nextState: any,\n      enterClassName: string, leaveClassName: string, currentOptions?: AnimationOptions,\n      nextOptions?: AnimationOptions, subInstructions?: ElementInstructionMap,\n      skipAstBuild?: boolean): AnimationTransitionInstruction {\n    const errors: any[] = [];\n\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n\n    const queriedElements = new Set<any>();\n    const preStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const postStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const isRemoval = nextState === 'void';\n\n    const animationOptions = {params: {...transitionAnimationParams, ...nextAnimationParams}};\n\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(\n                                              driver, element, this.ast.animation, enterClassName,\n                                              leaveClassName, currentStateStyles, nextStateStyles,\n                                              animationOptions, subInstructions, errors);\n\n    let totalTime = 0;\n    timelines.forEach(tl => { totalTime = Math.max(tl.duration + tl.delay, totalTime); });\n\n    if (errors.length) {\n      return createTransitionInstruction(\n          element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles,\n          nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetAsInMap(preStyleMap, elm, {});\n      tl.preStyleProps.forEach(prop => preProps[prop] = true);\n\n      const postProps = getOrSetAsInMap(postStyleMap, elm, {});\n      tl.postStyleProps.forEach(prop => postProps[prop] = true);\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    const queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(\n        element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles,\n        nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap, totalTime);\n  }\n}\n\nfunction oneOrMoreTransitionsMatch(\n    matchFns: TransitionMatcherFn[], currentState: any, nextState: any, element: any,\n    params: {[key: string]: any}): boolean {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\n\nexport class AnimationStateStyles {\n  constructor(private styles: StyleAst, private defaultParams: {[key: string]: any}) {}\n\n  buildStyles(params: {[key: string]: any}, errors: string[]): ÉµStyleData {\n    const finalStyles: ÉµStyleData = {};\n    const combinedParams = copyObj(this.defaultParams);\n    Object.keys(params).forEach(key => {\n      const value = params[key];\n      if (value != null) {\n        combinedParams[key] = value;\n      }\n    });\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        const styleObj = value as any;\n        Object.keys(styleObj).forEach(prop => {\n          let val = styleObj[prop];\n          if (val.length > 1) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          finalStyles[prop] = val;\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadataType, ÉµStyleData} from '@angular/animations';\n\nimport {copyStyles, interpolateParams} from '../util';\n\nimport {SequenceAst, StyleAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationStateStyles, AnimationTransitionFactory} from './animation_transition_factory';\n\n\n\n/**\n * @publicApi\n */\nexport function buildTrigger(name: string, ast: TriggerAst): AnimationTrigger {\n  return new AnimationTrigger(name, ast);\n}\n\n/**\n* @publicApi\n*/\nexport class AnimationTrigger {\n  public transitionFactories: AnimationTransitionFactory[] = [];\n  public fallbackTransition: AnimationTransitionFactory;\n  public states: {[stateName: string]: AnimationStateStyles} = {};\n\n  constructor(public name: string, public ast: TriggerAst) {\n    ast.states.forEach(ast => {\n      const defaultParams = (ast.options && ast.options.params) || {};\n      this.states[ast.name] = new AnimationStateStyles(ast.style, defaultParams);\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n\n  get containsQueries() { return this.ast.queryCount > 0; }\n\n  matchTransition(currentState: any, nextState: any, element: any, params: {[key: string]: any}):\n      AnimationTransitionFactory|null {\n    const entry =\n        this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n\n  matchStyles(currentState: any, params: {[key: string]: any}, errors: any[]): ÉµStyleData {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\n\nfunction createFallbackTransition(\n    triggerName: string,\n    states: {[stateName: string]: AnimationStateStyles}): AnimationTransitionFactory {\n  const matchers = [(fromState: any, toState: any) => true];\n  const animation: SequenceAst = {type: AnimationMetadataType.Sequence, steps: [], options: null};\n  const transition: TransitionAst = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n\nfunction balanceProperties(obj: {[key: string]: any}, key1: string, key2: string) {\n  if (obj.hasOwnProperty(key1)) {\n    if (!obj.hasOwnProperty(key2)) {\n      obj[key2] = obj[key1];\n    }\n  } else if (obj.hasOwnProperty(key2)) {\n    obj[key1] = obj[key2];\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationPlayer, ÉµStyleData} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\n\nexport class TimelineAnimationEngine {\n  private _animations: {[id: string]: Ast<AnimationMetadataType>} = {};\n  private _playersById: {[id: string]: AnimationPlayer} = {};\n  public players: AnimationPlayer[] = [];\n\n  constructor(\n      public bodyNode: any, private _driver: AnimationDriver,\n      private _normalizer: AnimationStyleNormalizer) {}\n\n  register(id: string, metadata: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors);\n    if (errors.length) {\n      throw new Error(\n          `Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    } else {\n      this._animations[id] = ast;\n    }\n  }\n\n  private _buildPlayer(\n      i: AnimationTimelineInstruction, preStyles: ÉµStyleData,\n      postStyles?: ÉµStyleData): AnimationPlayer {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(\n        this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n\n  create(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const errors: any[] = [];\n    const ast = this._animations[id];\n    let instructions: AnimationTimelineInstruction[];\n\n    const autoStylesMap = new Map<any, ÉµStyleData>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n          this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, {}, {}, options,\n          EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n        inst.postStyleProps.forEach(prop => styles[prop] = null);\n      });\n    } else {\n      errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw new Error(\n          `Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      Object.keys(styles).forEach(\n          prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n    });\n\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, {}, styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById[id] = player;\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n\n  destroy(id: string) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    delete this._playersById[id];\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n\n  private _getPlayer(id: string): AnimationPlayer {\n    const player = this._playersById[id];\n    if (!player) {\n      throw new Error(`Unable to find the timeline player referenced by ${id}`);\n    }\n    return player;\n  }\n\n  listen(id: string, element: string, eventName: string, callback: (event: any) => any):\n      () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n\n  command(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, args[0] as AnimationMetadata | AnimationMetadata[]);\n      return;\n    }\n\n    if (command == 'create') {\n      const options = (args[0] || {}) as AnimationOptions;\n      this.create(id, element, options);\n      return;\n    }\n\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0] as string));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, ÉµAnimationGroupPlayer as AnimationGroupPlayer, ÉµPRE_STYLE as PRE_STYLE, ÉµStyleData} from '@angular/animations';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, iteratorToArray, setStyles} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\n\nconst EMPTY_PLAYER_ARRAY: TransitionAnimationPlayer[] = [];\nconst NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\nexport interface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nexport const REMOVAL_FLAG = '__ng_removed';\n\nexport interface ElementAnimationState {\n  setForRemoval: boolean;\n  setForMove: boolean;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n}\n\nexport class StateValue {\n  public value: string;\n  public options: AnimationOptions;\n\n  get params(): {[key: string]: any} { return this.options.params as{[key: string]: any}; }\n\n  constructor(input: any, public namespaceId: string = '') {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      const options = copyObj(input as any);\n      delete options['value'];\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n\n  absorbOptions(options: AnimationOptions) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params !;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nexport const VOID_VALUE = 'void';\nexport const DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\n\nexport class AnimationTransitionNamespace {\n  public players: TransitionAnimationPlayer[] = [];\n\n  private _triggers: {[triggerName: string]: AnimationTrigger} = {};\n  private _queue: QueueInstruction[] = [];\n\n  private _elementListeners = new Map<any, TriggerListener[]>();\n\n  private _hostClassName: string;\n\n  constructor(\n      public id: string, public hostElement: any, private _engine: TransitionAnimationEngine) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n\n  listen(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.hasOwnProperty(name)) {\n      throw new Error(`Unable to listen on the animation trigger event \"${\n          phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw new Error(`Unable to listen on the animation trigger \"${\n          name}\" because the provided event is undefined!`);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw new Error(`The provided animation trigger event \"${phase}\" for the animation trigger \"${\n          name}\" is not supported!`);\n    }\n\n    const listeners = getOrSetAsInMap(this._elementListeners, element, []);\n    const data = {name, phase, callback};\n    listeners.push(data);\n\n    const triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n    if (!triggersWithStates.hasOwnProperty(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates[name] = DEFAULT_STATE_VALUE;\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers[name]) {\n          delete triggersWithStates[name];\n        }\n      });\n    };\n  }\n\n  register(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers[name]) {\n      // throw\n      return false;\n    } else {\n      this._triggers[name] = ast;\n      return true;\n    }\n  }\n\n  private _getTrigger(name: string) {\n    const trigger = this._triggers[name];\n    if (!trigger) {\n      throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n    }\n    return trigger;\n  }\n\n  trigger(element: any, triggerName: string, value: any, defaultToFallback: boolean = true):\n      TransitionAnimationPlayer|undefined {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = {});\n    }\n\n    let fromState = triggersWithStates[triggerName];\n    const toState = new StateValue(value, this.id);\n\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates[triggerName] = toState;\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n\n    const isRemoval = toState.value === VOID_VALUE;\n\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors: any[] = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n\n    const playersOnElement: TransitionAnimationPlayer[] =\n        getOrSetAsInMap(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new palyer)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let transition =\n        trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push(\n        {element, triggerName, transition, fromState, toState, player, isFallbackTransition});\n\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => { removeClass(element, QUEUED_CLASSNAME); });\n    }\n\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n\n  deregister(name: string) {\n    delete this._triggers[name];\n\n    this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n          element, listeners.filter(entry => { return entry.name != name; }));\n    });\n  }\n\n  clearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n\n  private _signalRemovalForInnerTriggers(rootElement: any, context: any, animate: boolean = false) {\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n  }\n\n  triggerLeaveAnimation(\n      element: any, context: any, destroyAfterComplete?: boolean,\n      defaultToFallback?: boolean): boolean {\n    const triggerStates = this._engine.statesByElement.get(element);\n    if (triggerStates) {\n      const players: TransitionAnimationPlayer[] = [];\n      Object.keys(triggerStates).forEach(triggerName => {\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers[triggerName]) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  prepareLeaveAnimationListeners(element: any) {\n    const listeners = this._elementListeners.get(element);\n    if (listeners) {\n      const visitedTriggers = new Set<string>();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const trigger = this._triggers[triggerName];\n        const transition = trigger.fallbackTransition;\n        const elementStates = this._engine.statesByElement.get(element) !;\n        const fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n\n  removeNode(element: any, context: any): void {\n    const engine = this._engine;\n\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context, true);\n    }\n\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers =\n          engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      // we do this after the flush has occurred such\n      // that the callbacks can be fired\n      engine.afterFlush(() => this.clearElementCache(element));\n      engine.destroyInnerAnimations(element);\n      engine._onRemovalComplete(element, context);\n    }\n  }\n\n  insertNode(element: any, parent: any): void { addClass(element, this._hostClassName); }\n\n  drainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const instructions: QueueInstruction[] = [];\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(\n                element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            (baseEvent as any)['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n\n  destroy(context: any) {\n    this.players.forEach(p => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n\n  elementContainsData(element: any): boolean {\n    let containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData =\n        (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n}\n\nexport interface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\n\nexport class TransitionAnimationEngine {\n  public players: TransitionAnimationPlayer[] = [];\n  public newHostElements = new Map<any, AnimationTransitionNamespace>();\n  public playersByElement = new Map<any, TransitionAnimationPlayer[]>();\n  public playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\n  public statesByElement = new Map<any, {[triggerName: string]: StateValue}>();\n  public disabledNodes = new Set<any>();\n\n  public totalAnimations = 0;\n  public totalQueuedPlayers = 0;\n\n  private _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\n  private _namespaceList: AnimationTransitionNamespace[] = [];\n  private _flushFns: (() => any)[] = [];\n  private _whenQuietFns: (() => any)[] = [];\n\n  public namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\n  public collectedEnterElements: any[] = [];\n  public collectedLeaveElements: any[] = [];\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  /** @internal */\n  _onRemovalComplete(element: any, context: any) { this.onRemovalComplete(element, context); }\n\n  constructor(\n      public bodyNode: any, public driver: AnimationDriver,\n      private _normalizer: AnimationStyleNormalizer) {}\n\n  get queuedPlayers(): TransitionAnimationPlayer[] {\n    const players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n\n  createNamespace(namespaceId: string, hostElement: any) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (hostElement.parentNode) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is apart of the animation code, it\n      // may or may not be inserted by a parent node that is an of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n\n  private _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const limit = this._namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      for (let i = limit; i >= 0; i--) {\n        const nextNamespace = this._namespaceList[i];\n        if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n          this._namespaceList.splice(i + 1, 0, ns);\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        this._namespaceList.splice(0, 0, ns);\n      }\n    } else {\n      this._namespaceList.push(ns);\n    }\n\n    this.namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n\n  registerTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n\n  destroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n\n    const ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n\n  private _fetchNamespace(id: string) { return this._namespaceLookup[id]; }\n\n  fetchNamespacesByElement(element: any): Set<AnimationTransitionNamespace> {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply the dedupe\n    // of namespaces incase there are multiple triggers both the elm and host\n    const namespaces = new Set<AnimationTransitionNamespace>();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      const keys = Object.keys(elementStates);\n      for (let i = 0; i < keys.length; i++) {\n        const nsId = elementStates[keys[i]].namespaceId;\n        if (nsId) {\n          const ns = this._fetchNamespace(nsId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n\n  trigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  insertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n\n  collectEnterElement(element: any) { this.collectedEnterElements.push(element); }\n\n  markElementAsDisabled(element: any, value: boolean) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n\n  removeNode(namespaceId: string, element: any, context: any): void {\n    if (!isElementNode(element)) {\n      this._onRemovalComplete(element, context);\n      return;\n    }\n\n    const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n    if (ns) {\n      ns.removeNode(element, context);\n    } else {\n      this.markElementAsRemoved(namespaceId, element, false, context);\n    }\n  }\n\n  markElementAsRemoved(namespaceId: string, element: any, hasAnimation?: boolean, context?: any) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context, hasAnimation,\n      removedBeforeQueried: false\n    };\n  }\n\n  listen(\n      namespaceId: string, element: any, name: string, phase: string,\n      callback: (event: any) => boolean): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n\n  private _buildInstruction(\n      entry: QueueInstruction, subTimelines: ElementInstructionMap, enterClassName: string,\n      leaveClassName: string, skipBuildAst?: boolean) {\n    return entry.transition.build(\n        this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName,\n        leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n\n  destroyInnerAnimations(containerElement: any) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n\n  destroyActiveAnimationsForElement(element: any) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n\n  finishActiveQueriedAnimationOnElement(element: any) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n\n  processLeaveNode(element: any) {\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n\n    if (this.driver.matchesElement(element, DISABLED_SELECTOR)) {\n      this.markElementAsDisabled(element, false);\n    }\n\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n\n  flush(microtaskId: number = -1) {\n    let players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n\n    if (this._namespaceList.length &&\n        (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns: Function[] = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n\n  reportError(errors: string[]) {\n    throw new Error(\n        `Unable to process animations due to the following failed trigger transitions\\n ${\n            errors.join('\\n')}`);\n  }\n\n  private _flushAnimations(cleanupFns: Function[], microtaskId: number):\n      TransitionAnimationPlayer[] {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers: TransitionAnimationPlayer[] = [];\n    const skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const queuedInstructions: QueuedTransition[] = [];\n    const queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const allPreStyleElements = new Map<any, Set<string>>();\n    const allPostStyleElements = new Map<any, Set<string>>();\n\n    const disabledElementsSet = new Set<any>();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map<any, string>();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n\n    const allLeaveNodes: any[] = [];\n    const mergedLeaveNodes = new Set<any>();\n    const leaveNodesWithoutAnimations = new Set<any>();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n\n    const leaveNodeMapIds = new Map<any, string>();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root) !;\n        nodes.forEach(node => removeClass(node, className));\n      });\n\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root) !;\n        nodes.forEach(node => removeClass(node, className));\n      });\n\n      allLeaveNodes.forEach(element => { this.processLeaveNode(element); });\n    });\n\n    const allPlayers: TransitionAnimationPlayer[] = [];\n    const erroneousTransitions: AnimationTransitionInstruction[] = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG] as ElementAnimationState;\n          // move animations are currently not supported...\n          if (details && details.setForMove) {\n            player.destroy();\n            return;\n          }\n        }\n\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element) !;\n        const enterClassName = enterNodeMapIds.get(element) !;\n        const instruction = this._buildInstruction(\n            entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned) !;\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n\n        // even though the element may not be apart of the DOM, it may\n        // still be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important we still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // if a unmatched transition is queued to go then it SHOULD NOT render\n        // an animation and cancel the previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub trigger\n        // then it will instruct the timeline builder to not add a player delay, but\n        // instead stretch the first keyframe gap up until the animation starts. The\n        // reason this is important is to prevent extra initialization styles from being\n        // required by the user in the animation.\n        instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n\n        subTimelines.append(element, instruction.timelines);\n\n        const tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach(\n            element => getOrSetAsInMap(queriedElements, element, []).push(player));\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          const props = Object.keys(stringMap);\n          if (props.length) {\n            let setVal: Set<string> = allPreStyleElements.get(element) !;\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set<string>());\n            }\n            props.forEach(prop => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          const props = Object.keys(stringMap);\n          let setVal: Set<string> = allPostStyleElements.get(element) !;\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set<string>());\n          }\n          props.forEach(prop => setVal.add(prop));\n        });\n      });\n    }\n\n    if (erroneousTransitions.length) {\n      const errors: string[] = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(`@${instruction.triggerName} has failed due to:\\n`);\n        instruction.errors !.forEach(error => errors.push(`- ${error}\\n`));\n      });\n\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n\n    const allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    // this map works to tell which element in the DOM tree is contained by\n    // which animation. Further down below this map will get populated once\n    // the players are built and in doing so it can efficiently figure out\n    // if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map<any, any>();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(\n            entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n      const previousPlayers =\n          this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n\n    // this is a special case for nodes that will be removed (either by)\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or auto since the element\n    // that is being getComputedStyle'd will not be visible (since * = destination)\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map<any, ÉµStyleData>();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(\n        postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map<any, ÉµStyleData>();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(\n          preStylesMap, this.driver, new Set(nodes), allPreStyleElements, PRE_STYLE);\n    });\n\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, { ...post, ...pre } as any);\n    });\n\n    const rootPlayers: TransitionAnimationPlayer[] = [];\n    const subPlayers: TransitionAnimationPlayer[] = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation: any = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd: any[] = [];\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n\n        const innerPlayer = this._buildAnimation(\n            player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap,\n            postStylesMap);\n\n        player.setRealPlayer(innerPlayer);\n\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n\n    // find all of the sub players' corresponding inner animation player\n    subPlayers.forEach(player => {\n      // even if any players are not found for a sub animation then it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      removeClass(element, LEAVE_CLASSNAME);\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let players: TransitionAnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n\n      const activePlayers = players.filter(p => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    return rootPlayers;\n  }\n\n  elementContainsData(namespaceId: string, element: any) {\n    let containsData = false;\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n\n  afterFlush(callback: () => any) { this._flushFns.push(callback); }\n\n  afterFlushAnimationsDone(callback: () => any) { this._whenQuietFns.push(callback); }\n\n  private _getPreviousPlayers(\n      element: string, isQueriedElement: boolean, namespaceId?: string, triggerName?: string,\n      toStateValue?: any): TransitionAnimationPlayer[] {\n    let players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n\n  private _beforeAnimationBuild(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId: string|undefined =\n        instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName: string|undefined =\n        instruction.isRemovalTransition ? undefined : triggerName;\n\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(\n          element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer() as any;\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n\n  private _buildAnimation(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n      skippedPlayersMap: Map<any, AnimationPlayer[]>, preStylesMap: Map<any, ÉµStyleData>,\n      postStylesMap: Map<any, ÉµStyleData>): AnimationPlayer {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const allConsumedElements = new Set<any>();\n    const allSubElements = new Set<any>();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried)\n        return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers =\n          flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY)\n                                  .map(p => p.getRealPlayer()))\n              .filter(p => {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                const pp = p as any;\n                return pp.element ? pp.element === element : false;\n              });\n\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes(\n          this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles,\n          postStyles);\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach(player => {\n      getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(\n        element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n\n    return player;\n  }\n\n  private _buildPlayer(\n      instruction: AnimationTimelineInstruction, keyframes: ÉµStyleData[],\n      previousPlayers: AnimationPlayer[]): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n          instruction.element, keyframes, instruction.duration, instruction.delay,\n          instruction.easing, previousPlayers);\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\n  private _player: AnimationPlayer = new NoopAnimationPlayer();\n  private _containsRealPlayer = false;\n\n  private _queuedCallbacks: {[name: string]: (() => any)[]} = {};\n  public readonly destroyed = false;\n  // TODO(issue/24571): remove '!'.\n  public parentPlayer !: AnimationPlayer;\n\n  public markedForDestroy: boolean = false;\n  public disabled = false;\n\n  readonly queued: boolean = true;\n  public readonly totalTime: number = 0;\n\n  constructor(public namespaceId: string, public triggerName: string, public element: any) {}\n\n  setRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    Object.keys(this._queuedCallbacks).forEach(phase => {\n      this._queuedCallbacks[phase].forEach(\n          callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks = {};\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    (this as{queued: boolean}).queued = false;\n  }\n\n  getRealPlayer() { return this._player; }\n\n  overrideTotalTime(totalTime: number) { (this as any).totalTime = totalTime; }\n\n  syncPlayerEvents(player: AnimationPlayer) {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback !('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n\n  private _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n  }\n\n  onDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n\n  onStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n\n  init(): void { this._player.init(); }\n\n  hasStarted(): boolean { return this.queued ? false : this._player.hasStarted(); }\n\n  play(): void { !this.queued && this._player.play(); }\n\n  pause(): void { !this.queued && this._player.pause(); }\n\n  restart(): void { !this.queued && this._player.restart(); }\n\n  finish(): void { this._player.finish(); }\n\n  destroy(): void {\n    (this as{destroyed: boolean}).destroyed = true;\n    this._player.destroy();\n  }\n\n  reset(): void { !this.queued && this._player.reset(); }\n\n  setPosition(p: any): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n\n  getPosition(): number { return this.queued ? 0 : this._player.getPosition(); }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\n\nfunction deleteOrUnsetInMap(map: Map<any, any[]>| {[key: string]: any}, key: any, value: any) {\n  let currentValues: any[]|null|undefined;\n  if (map instanceof Map) {\n    currentValues = map.get(key);\n    if (currentValues) {\n      if (currentValues.length) {\n        const index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        map.delete(key);\n      }\n    }\n  } else {\n    currentValues = map[key];\n    if (currentValues) {\n      if (currentValues.length) {\n        const index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        delete map[key];\n      }\n    }\n  }\n  return currentValues;\n}\n\nfunction normalizeTriggerValue(value: any): any {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\n\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n\nfunction cloakElement(element: any, value?: string) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n\nfunction cloakAndComputeStyles(\n    valuesMap: Map<any, ÉµStyleData>, driver: AnimationDriver, elements: Set<any>,\n    elementPropsMap: Map<any, Set<string>>, defaultStyle: string): any[] {\n  const cloakVals: string[] = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n\n  const failedElements: any[] = [];\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const styles: ÉµStyleData = {};\n    props.forEach(prop => {\n      const value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n\n  return failedElements;\n}\n\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots: any[], nodes: any[]): Map<any, any[]> {\n  const rootMap = new Map<any, any[]>();\n  roots.forEach(root => rootMap.set(root, []));\n\n  if (nodes.length == 0) return rootMap;\n\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map<any, any>();\n\n  function getRoot(node: any): any {\n    if (!node) return NULL_NODE;\n\n    let root = localRootMap.get(node);\n    if (root) return root;\n\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {  // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {  // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {  // recurse upwards\n      root = getRoot(parent);\n    }\n\n    localRootMap.set(node, root);\n    return root;\n  }\n\n  nodes.forEach(node => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root) !.push(node);\n    }\n  });\n\n  return rootMap;\n}\n\nconst CLASSES_CACHE_KEY = '$$classes';\nfunction containsClass(element: any, className: string): boolean {\n  if (element.classList) {\n    return element.classList.contains(className);\n  } else {\n    const classes = element[CLASSES_CACHE_KEY];\n    return classes && classes[className];\n  }\n}\n\nfunction addClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    let classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (!classes) {\n      classes = element[CLASSES_CACHE_KEY] = {};\n    }\n    classes[className] = true;\n  }\n}\n\nfunction removeClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    let classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (classes) {\n      delete classes[className];\n    }\n  }\n}\n\nfunction removeNodesAfterAnimationDone(\n    engine: TransitionAnimationEngine, element: any, players: AnimationPlayer[]) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n\nfunction flattenGroupPlayers(players: AnimationPlayer[]): AnimationPlayer[] {\n  const finalPlayers: AnimationPlayer[] = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\n\nfunction _flattenGroupPlayersRecur(players: AnimationPlayer[], finalPlayers: AnimationPlayer[]) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player as AnimationPlayer);\n    }\n  }\n}\n\nfunction objEquals(a: {[key: string]: any}, b: {[key: string]: any}): boolean {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\n\nfunction replacePostStylesAsPre(\n    element: any, allPreStyleElements: Map<any, Set<string>>,\n    allPostStyleElements: Map<any, Set<string>>): boolean {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach(data => preEntry !.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n\n  allPostStyleElements.delete(element);\n  return true;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\n\nexport class AnimationEngine {\n  private _transitionEngine: TransitionAnimationEngine;\n  private _timelineEngine: TimelineAnimationEngine;\n\n  private _triggerCache: {[key: string]: AnimationTrigger} = {};\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  constructor(\n      private bodyNode: any, private _driver: AnimationDriver,\n      normalizer: AnimationStyleNormalizer) {\n    this._transitionEngine = new TransitionAnimationEngine(bodyNode, _driver, normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(bodyNode, _driver, normalizer);\n\n    this._transitionEngine.onRemovalComplete = (element: any, context: any) =>\n        this.onRemovalComplete(element, context);\n  }\n\n  registerTrigger(\n      componentId: string, namespaceId: string, hostElement: any, name: string,\n      metadata: AnimationTriggerMetadata): void {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors: any[] = [];\n      const ast =\n          buildAnimationAst(this._driver, metadata as AnimationMetadata, errors) as TriggerAst;\n      if (errors.length) {\n        throw new Error(\n            `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n      }\n      trigger = buildTrigger(name, ast);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n\n  destroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n\n  onInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n\n  onRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n\n  disableAnimations(element: any, disable: boolean) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n\n  process(namespaceId: string, element: any, property: string, value: any) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value as any[];\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n\n  listen(\n      namespaceId: string, element: any, eventName: string, eventPhase: string,\n      callback: (event: any) => any): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n\n  flush(microtaskId: number = -1): void { this._transitionEngine.flush(microtaskId); }\n\n  get players(): AnimationPlayer[] {\n    return (this._transitionEngine.players as AnimationPlayer[])\n        .concat(this._timelineEngine.players as AnimationPlayer[]);\n  }\n\n  whenRenderingDone(): Promise<any> { return this._transitionEngine.whenRenderingDone(); }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {eraseStyles, setStyles} from '../util';\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nexport function packageNonAnimatableStyles(\n    element: any, styles: {[key: string]: any} | {[key: string]: any}[]): SpecialCasedStyles|null {\n  let startStyles: {[key: string]: any}|null = null;\n  let endStyles: {[key: string]: any}|null = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n\n  return (startStyles || endStyles) ? new SpecialCasedStyles(element, startStyles, endStyles) :\n                                      null;\n}\n\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nexport class SpecialCasedStyles {\n  static initialStylesByElement = new WeakMap<any, {[key: string]: any}>();\n\n  private _state = SpecialCasedStylesState.Pending;\n  private _initialStyles !: {[key: string]: any};\n\n  constructor(\n      private _element: any, private _startStyles: {[key: string]: any}|null,\n      private _endStyles: {[key: string]: any}|null) {\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = {});\n    }\n    this._initialStyles = initialStyles;\n  }\n\n  start() {\n    if (this._state < SpecialCasedStylesState.Started) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  finish() {\n    this.start();\n    if (this._state < SpecialCasedStylesState.Finished) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  destroy() {\n    this.finish();\n    if (this._state < SpecialCasedStylesState.Destroyed) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = SpecialCasedStylesState.Destroyed;\n    }\n  }\n}\n\n/**\n * An enum of states reflective of what the status of `SpecialCasedStyles` is.\n *\n * Depending on how `SpecialCasedStyles` is interacted with, the start and end\n * styles may not be applied in the same way. This enum ensures that if and when\n * the ending styles are applied then the starting styles are applied. It is\n * also used to reflect what the current status of the special cased styles are\n * which helps prevent the starting/ending styles not be applied twice. It is\n * also used to cleanup the styles once `SpecialCasedStyles` is destroyed.\n */\nconst enum SpecialCasedStylesState {\n  Pending = 0,\n  Started = 1,\n  Finished = 2,\n  Destroyed = 3,\n}\n\nfunction filterNonAnimatableStyles(styles: {[key: string]: any}) {\n  let result: {[key: string]: any}|null = null;\n  const props = Object.keys(styles);\n  for (let i = 0; i < props.length; i++) {\n    const prop = props[i];\n    if (isNonAnimatableStyle(prop)) {\n      result = result || {};\n      result[prop] = styles[prop];\n    }\n  }\n  return result;\n}\n\nfunction isNonAnimatableStyle(prop: string) {\n  return prop === 'display' || prop === 'position';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst ELAPSED_TIME_MAX_DECIMAL_PLACES = 3;\nconst ANIMATION_PROP = 'animation';\nconst ANIMATIONEND_EVENT = 'animationend';\nconst ONE_SECOND = 1000;\n\nexport class ElementAnimationStyleHandler {\n  private readonly _eventFn: (e: any) => any;\n  private _finished = false;\n  private _destroyed = false;\n  private _startTime = 0;\n  private _position = 0;\n\n  constructor(\n      private readonly _element: any, private readonly _name: string,\n      private readonly _duration: number, private readonly _delay: number,\n      private readonly _easing: string, private readonly _fillMode: ''|'both'|'forwards',\n      private readonly _onDoneFn: () => any) {\n    this._eventFn = (e) => this._handleCallback(e);\n  }\n\n  apply() {\n    applyKeyframeAnimation(\n        this._element,\n        `${this._duration}ms ${this._easing} ${this._delay}ms 1 normal ${this._fillMode} ${this._name}`);\n    addRemoveAnimationEvent(this._element, this._eventFn, false);\n    this._startTime = Date.now();\n  }\n\n  pause() { playPauseAnimation(this._element, this._name, 'paused'); }\n\n  resume() { playPauseAnimation(this._element, this._name, 'running'); }\n\n  setPosition(position: number) {\n    const index = findIndexForAnimation(this._element, this._name);\n    this._position = position * this._duration;\n    setAnimationStyle(this._element, 'Delay', `-${this._position}ms`, index);\n  }\n\n  getPosition() { return this._position; }\n\n  private _handleCallback(event: any) {\n    const timestamp = event._ngTestManualTimestamp || Date.now();\n    const elapsedTime =\n        parseFloat(event.elapsedTime.toFixed(ELAPSED_TIME_MAX_DECIMAL_PLACES)) * ONE_SECOND;\n    if (event.animationName == this._name &&\n        Math.max(timestamp - this._startTime, 0) >= this._delay && elapsedTime >= this._duration) {\n      this.finish();\n    }\n  }\n\n  finish() {\n    if (this._finished) return;\n    this._finished = true;\n    this._onDoneFn();\n    addRemoveAnimationEvent(this._element, this._eventFn, true);\n  }\n\n  destroy() {\n    if (this._destroyed) return;\n    this._destroyed = true;\n    this.finish();\n    removeKeyframeAnimation(this._element, this._name);\n  }\n}\n\nfunction playPauseAnimation(element: any, name: string, status: 'running' | 'paused') {\n  const index = findIndexForAnimation(element, name);\n  setAnimationStyle(element, 'PlayState', status, index);\n}\n\nfunction applyKeyframeAnimation(element: any, value: string): number {\n  const anim = getAnimationStyle(element, '').trim();\n  let index = 0;\n  if (anim.length) {\n    index = countChars(anim, ',') + 1;\n    value = `${anim}, ${value}`;\n  }\n  setAnimationStyle(element, '', value);\n  return index;\n}\n\nfunction removeKeyframeAnimation(element: any, name: string) {\n  const anim = getAnimationStyle(element, '');\n  const tokens = anim.split(',');\n  const index = findMatchingTokenIndex(tokens, name);\n  if (index >= 0) {\n    tokens.splice(index, 1);\n    const newValue = tokens.join(',');\n    setAnimationStyle(element, '', newValue);\n  }\n}\n\nfunction findIndexForAnimation(element: any, value: string) {\n  const anim = getAnimationStyle(element, '');\n  if (anim.indexOf(',') > 0) {\n    const tokens = anim.split(',');\n    return findMatchingTokenIndex(tokens, value);\n  }\n  return findMatchingTokenIndex([anim], value);\n}\n\nfunction findMatchingTokenIndex(tokens: string[], searchToken: string): number {\n  for (let i = 0; i < tokens.length; i++) {\n    if (tokens[i].indexOf(searchToken) >= 0) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nfunction addRemoveAnimationEvent(element: any, fn: (e: any) => any, doRemove: boolean) {\n  doRemove ? element.removeEventListener(ANIMATIONEND_EVENT, fn) :\n             element.addEventListener(ANIMATIONEND_EVENT, fn);\n}\n\nfunction setAnimationStyle(element: any, name: string, value: string, index?: number) {\n  const prop = ANIMATION_PROP + name;\n  if (index != null) {\n    const oldValue = element.style[prop];\n    if (oldValue.length) {\n      const tokens = oldValue.split(',');\n      tokens[index] = value;\n      value = tokens.join(',');\n    }\n  }\n  element.style[prop] = value;\n}\n\nfunction getAnimationStyle(element: any, name: string) {\n  return element.style[ANIMATION_PROP + name];\n}\n\nfunction countChars(value: string, char: string): number {\n  let count = 0;\n  for (let i = 0; i < value.length; i++) {\n    const c = value.charAt(i);\n    if (c === char) count++;\n  }\n  return count;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {computeStyle} from '../../util';\nimport {SpecialCasedStyles} from '../special_cased_styles';\nimport {ElementAnimationStyleHandler} from './element_animation_style_handler';\n\nconst DEFAULT_FILL_MODE = 'forwards';\nconst DEFAULT_EASING = 'linear';\n\nexport const enum AnimatorControlState {INITIALIZED = 1, STARTED = 2, FINISHED = 3, DESTROYED = 4}\n\nexport class CssKeyframesPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n\n  private _started = false;\n  // TODO(issue/24571): remove '!'.\n  private _styler !: ElementAnimationStyleHandler;\n\n  // TODO(issue/24571): remove '!'.\n  public parentPlayer !: AnimationPlayer;\n  public readonly totalTime: number;\n  public readonly easing: string;\n  public currentSnapshot: {[key: string]: string} = {};\n\n  private _state: AnimatorControlState = 0;\n\n  constructor(\n      public readonly element: any, public readonly keyframes: {[key: string]: string | number}[],\n      public readonly animationName: string, private readonly _duration: number,\n      private readonly _delay: number, easing: string,\n      private readonly _finalStyles: {[key: string]: any},\n      private readonly _specialStyles?: SpecialCasedStyles|null) {\n    this.easing = easing || DEFAULT_EASING;\n    this.totalTime = _duration + _delay;\n    this._buildStyler();\n  }\n\n  onStart(fn: () => void): void { this._onStartFns.push(fn); }\n\n  onDone(fn: () => void): void { this._onDoneFns.push(fn); }\n\n  onDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n\n  destroy() {\n    this.init();\n    if (this._state >= AnimatorControlState.DESTROYED) return;\n    this._state = AnimatorControlState.DESTROYED;\n    this._styler.destroy();\n    this._flushStartFns();\n    this._flushDoneFns();\n    if (this._specialStyles) {\n      this._specialStyles.destroy();\n    }\n    this._onDestroyFns.forEach(fn => fn());\n    this._onDestroyFns = [];\n  }\n\n  private _flushDoneFns() {\n    this._onDoneFns.forEach(fn => fn());\n    this._onDoneFns = [];\n  }\n\n  private _flushStartFns() {\n    this._onStartFns.forEach(fn => fn());\n    this._onStartFns = [];\n  }\n\n  finish() {\n    this.init();\n    if (this._state >= AnimatorControlState.FINISHED) return;\n    this._state = AnimatorControlState.FINISHED;\n    this._styler.finish();\n    this._flushStartFns();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._flushDoneFns();\n  }\n\n  setPosition(value: number) { this._styler.setPosition(value); }\n\n  getPosition(): number { return this._styler.getPosition(); }\n\n  hasStarted(): boolean { return this._state >= AnimatorControlState.STARTED; }\n  init(): void {\n    if (this._state >= AnimatorControlState.INITIALIZED) return;\n    this._state = AnimatorControlState.INITIALIZED;\n    const elm = this.element;\n    this._styler.apply();\n    if (this._delay) {\n      this._styler.pause();\n    }\n  }\n\n  play(): void {\n    this.init();\n    if (!this.hasStarted()) {\n      this._flushStartFns();\n      this._state = AnimatorControlState.STARTED;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this._styler.resume();\n  }\n\n  pause(): void {\n    this.init();\n    this._styler.pause();\n  }\n  restart(): void {\n    this.reset();\n    this.play();\n  }\n  reset(): void {\n    this._styler.destroy();\n    this._buildStyler();\n    this._styler.apply();\n  }\n\n  private _buildStyler() {\n    this._styler = new ElementAnimationStyleHandler(\n        this.element, this.animationName, this._duration, this._delay, this.easing,\n        DEFAULT_FILL_MODE, () => this.finish());\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n\n  beforeDestroy() {\n    this.init();\n    const styles: {[key: string]: string} = {};\n    if (this.hasStarted()) {\n      const finished = this._state >= AnimatorControlState.FINISHED;\n      Object.keys(this._finalStyles).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] = finished ? this._finalStyles[prop] : computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NoopAnimationPlayer} from '@angular/animations';\nimport {hypenatePropsObject} from '../shared';\n\nexport class DirectStylePlayer extends NoopAnimationPlayer {\n  private _startingStyles: {[key: string]: any}|null = {};\n  private __initialized = false;\n  private _styles: {[key: string]: any};\n\n  constructor(public element: any, styles: {[key: string]: any}) {\n    super();\n    this._styles = hypenatePropsObject(styles);\n  }\n\n  init() {\n    if (this.__initialized || !this._startingStyles) return;\n    this.__initialized = true;\n    Object.keys(this._styles).forEach(prop => {\n      this._startingStyles ![prop] = this.element.style[prop];\n    });\n    super.init();\n  }\n\n  play() {\n    if (!this._startingStyles) return;\n    this.init();\n    Object.keys(this._styles)\n        .forEach(prop => this.element.style.setProperty(prop, this._styles[prop]));\n    super.play();\n  }\n\n  destroy() {\n    if (!this._startingStyles) return;\n    Object.keys(this._startingStyles).forEach(prop => {\n      const value = this._startingStyles ![prop];\n      if (value) {\n        this.element.style.setProperty(prop, value);\n      } else {\n        this.element.style.removeProperty(prop);\n      }\n    });\n    this._startingStyles = null;\n    super.destroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, ÉµStyleData} from '@angular/animations';\n\nimport {allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, computeStyle} from '../../util';\nimport {AnimationDriver} from '../animation_driver';\nimport {containsElement, hypenatePropsObject, invokeQuery, matchesElement, validateStyleProperty} from '../shared';\nimport {packageNonAnimatableStyles} from '../special_cased_styles';\n\nimport {CssKeyframesPlayer} from './css_keyframes_player';\nimport {DirectStylePlayer} from './direct_style_player';\n\nconst KEYFRAMES_NAME_PREFIX = 'gen_css_kf_';\nconst TAB_SPACE = ' ';\n\nexport class CssKeyframesDriver implements AnimationDriver {\n  private _count = 0;\n  private readonly _head: any = document.querySelector('head');\n  private _warningIssued = false;\n\n  validateStyleProperty(prop: string): boolean { return validateStyleProperty(prop); }\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return (window.getComputedStyle(element) as any)[prop] as string;\n  }\n\n  buildKeyframeElement(element: any, name: string, keyframes: {[key: string]: any}[]): any {\n    keyframes = keyframes.map(kf => hypenatePropsObject(kf));\n    let keyframeStr = `@keyframes ${name} {\\n`;\n    let tab = '';\n    keyframes.forEach(kf => {\n      tab = TAB_SPACE;\n      const offset = parseFloat(kf['offset']);\n      keyframeStr += `${tab}${offset * 100}% {\\n`;\n      tab += TAB_SPACE;\n      Object.keys(kf).forEach(prop => {\n        const value = kf[prop];\n        switch (prop) {\n          case 'offset':\n            return;\n          case 'easing':\n            if (value) {\n              keyframeStr += `${tab}animation-timing-function: ${value};\\n`;\n            }\n            return;\n          default:\n            keyframeStr += `${tab}${prop}: ${value};\\n`;\n            return;\n        }\n      });\n      keyframeStr += `${tab}}\\n`;\n    });\n    keyframeStr += `}\\n`;\n\n    const kfElm = document.createElement('style');\n    kfElm.innerHTML = keyframeStr;\n    return kfElm;\n  }\n\n  animate(\n      element: any, keyframes: ÉµStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = [], scrubberAccessRequested?: boolean): AnimationPlayer {\n    if (scrubberAccessRequested) {\n      this._notifyFaultyScrubber();\n    }\n\n    const previousCssKeyframePlayers = <CssKeyframesPlayer[]>previousPlayers.filter(\n        player => player instanceof CssKeyframesPlayer);\n\n    const previousStyles: {[key: string]: any} = {};\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousCssKeyframePlayers.forEach(player => {\n        let styles = player.currentSnapshot;\n        Object.keys(styles).forEach(prop => previousStyles[prop] = styles[prop]);\n      });\n    }\n\n    keyframes = balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles);\n    const finalStyles = flattenKeyframesIntoStyles(keyframes);\n\n    // if there is no animation then there is no point in applying\n    // styles and waiting for an event to get fired. This causes lag.\n    // It's better to just directly apply the styles to the element\n    // via the direct styling animation player.\n    if (duration == 0) {\n      return new DirectStylePlayer(element, finalStyles);\n    }\n\n    const animationName = `${KEYFRAMES_NAME_PREFIX}${this._count++}`;\n    const kfElm = this.buildKeyframeElement(element, animationName, keyframes);\n    document.querySelector('head') !.appendChild(kfElm);\n\n    const specialStyles = packageNonAnimatableStyles(element, keyframes);\n    const player = new CssKeyframesPlayer(\n        element, keyframes, animationName, duration, delay, easing, finalStyles, specialStyles);\n\n    player.onDestroy(() => removeElement(kfElm));\n    return player;\n  }\n\n  private _notifyFaultyScrubber() {\n    if (!this._warningIssued) {\n      console.warn(\n          '@angular/animations: please load the web-animations.js polyfill to allow programmatic access...\\n',\n          '  visit http://bit.ly/IWukam to learn more about using the web-animation-js polyfill.');\n      this._warningIssued = true;\n    }\n  }\n}\n\nfunction flattenKeyframesIntoStyles(\n    keyframes: null | {[key: string]: any} | {[key: string]: any}[]): {[key: string]: any} {\n  let flatKeyframes: {[key: string]: any} = {};\n  if (keyframes) {\n    const kfs = Array.isArray(keyframes) ? keyframes : [keyframes];\n    kfs.forEach(kf => {\n      Object.keys(kf).forEach(prop => {\n        if (prop == 'offset' || prop == 'easing') return;\n        flatKeyframes[prop] = kf[prop];\n      });\n    });\n  }\n  return flatKeyframes;\n}\n\nfunction removeElement(node: any) {\n  node.parentNode.removeChild(node);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {computeStyle} from '../../util';\nimport {SpecialCasedStyles} from '../special_cased_styles';\n\nimport {DOMAnimation} from './dom_animation';\n\nexport class WebAnimationsPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n  private _duration: number;\n  private _delay: number;\n  private _initialized = false;\n  private _finished = false;\n  private _started = false;\n  private _destroyed = false;\n  // TODO(issue/24571): remove '!'.\n  private _finalKeyframe !: {[key: string]: string | number};\n\n  // TODO(issue/24571): remove '!'.\n  public readonly domPlayer !: DOMAnimation;\n  public time = 0;\n\n  public parentPlayer: AnimationPlayer|null = null;\n  public currentSnapshot: {[styleName: string]: string | number} = {};\n\n  constructor(\n      public element: any, public keyframes: {[key: string]: string | number}[],\n      public options: {[key: string]: string | number},\n      private _specialStyles?: SpecialCasedStyles|null) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n\n  init(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n\n  private _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const keyframes = this.keyframes;\n    (this as{domPlayer: DOMAnimation}).domPlayer =\n        this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n    this.domPlayer.addEventListener('finish', () => this._onFinish());\n  }\n\n  private _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n\n  /** @internal */\n  _triggerWebAnimation(element: any, keyframes: any[], options: any): DOMAnimation {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return element['animate'](keyframes, options) as DOMAnimation;\n  }\n\n  onStart(fn: () => void): void { this._onStartFns.push(fn); }\n\n  onDone(fn: () => void): void { this._onDoneFns.push(fn); }\n\n  onDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n\n  play(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n\n  pause(): void {\n    this.init();\n    this.domPlayer.pause();\n  }\n\n  finish(): void {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n\n  reset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n\n  private _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n\n  restart(): void {\n    this.reset();\n    this.play();\n  }\n\n  hasStarted(): boolean { return this._started; }\n\n  destroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n\n  setPosition(p: number): void { this.domPlayer.currentTime = p * this.time; }\n\n  getPosition(): number { return this.domPlayer.currentTime / this.time; }\n\n  get totalTime(): number { return this._delay + this._duration; }\n\n  beforeDestroy() {\n    const styles: {[key: string]: string | number} = {};\n    if (this.hasStarted()) {\n      Object.keys(this._finalKeyframe).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] =\n              this._finished ? this._finalKeyframe[prop] : computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, ÉµStyleData} from '@angular/animations';\n\nimport {allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, copyStyles} from '../../util';\nimport {AnimationDriver} from '../animation_driver';\nimport {CssKeyframesDriver} from '../css_keyframes/css_keyframes_driver';\nimport {containsElement, invokeQuery, isBrowser, matchesElement, validateStyleProperty} from '../shared';\nimport {packageNonAnimatableStyles} from '../special_cased_styles';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\n\nexport class WebAnimationsDriver implements AnimationDriver {\n  private _isNativeImpl = /\\{\\s*\\[native\\s+code\\]\\s*\\}/.test(getElementAnimateFn().toString());\n  private _cssKeyframesDriver = new CssKeyframesDriver();\n\n  validateStyleProperty(prop: string): boolean { return validateStyleProperty(prop); }\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return (window.getComputedStyle(element) as any)[prop] as string;\n  }\n\n  overrideWebAnimationsSupport(supported: boolean) { this._isNativeImpl = supported; }\n\n  animate(\n      element: any, keyframes: ÉµStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = [], scrubberAccessRequested?: boolean): AnimationPlayer {\n    const useKeyframes = !scrubberAccessRequested && !this._isNativeImpl;\n    if (useKeyframes) {\n      return this._cssKeyframesDriver.animate(\n          element, keyframes, duration, delay, easing, previousPlayers);\n    }\n\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const previousStyles: {[key: string]: any} = {};\n    const previousWebAnimationPlayers = <WebAnimationsPlayer[]>previousPlayers.filter(\n        player => player instanceof WebAnimationsPlayer);\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        let styles = player.currentSnapshot;\n        Object.keys(styles).forEach(prop => previousStyles[prop] = styles[prop]);\n      });\n    }\n\n    keyframes = keyframes.map(styles => copyStyles(styles, false));\n    keyframes = balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, keyframes);\n    return new WebAnimationsPlayer(element, keyframes, playerOptions, specialStyles);\n  }\n}\n\nexport function supportsWebAnimations() {\n  return typeof getElementAnimateFn() === 'function';\n}\n\nfunction getElementAnimateFn(): any {\n  return (isBrowser() && (<any>Element).prototype['animate']) || {};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {Animation as ÉµAnimation} from './dsl/animation';\nexport {AnimationStyleNormalizer as ÉµAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ÉµNoopAnimationStyleNormalizer} from './dsl/style_normalization/animation_style_normalizer';\nexport {WebAnimationsStyleNormalizer as ÉµWebAnimationsStyleNormalizer} from './dsl/style_normalization/web_animations_style_normalizer';\nexport {AnimationDriver as ÉµAnimationDriver, NoopAnimationDriver as ÉµNoopAnimationDriver} from './render/animation_driver';\nexport {AnimationEngine as ÉµAnimationEngine} from './render/animation_engine_next';\nexport {CssKeyframesDriver as ÉµCssKeyframesDriver} from './render/css_keyframes/css_keyframes_driver';\nexport {CssKeyframesPlayer as ÉµCssKeyframesPlayer} from './render/css_keyframes/css_keyframes_player';\nexport {containsElement as ÉµcontainsElement, invokeQuery as ÉµinvokeQuery, matchesElement as ÉµmatchesElement, validateStyleProperty as ÉµvalidateStyleProperty} from './render/shared';\nexport {WebAnimationsDriver as ÉµWebAnimationsDriver, supportsWebAnimations as ÉµsupportsWebAnimations} from './render/web_animations/web_animations_driver';\nexport {WebAnimationsPlayer as ÉµWebAnimationsPlayer} from './render/web_animations/web_animations_player';\nexport {allowPreviousPlayerStylesMerge as ÉµallowPreviousPlayerStylesMerge} from './util';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {AnimationDriver} from './render/animation_driver';\nexport * from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/browser';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verifcation. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n\nexport {SpecialCasedStyles as Éµangular_packages_animations_browser_browser_a} from './src/render/special_cased_styles';"], "names": ["NoopAnimationPlayer", "ɵAnimationGroupPlayer", "PRE_STYLE", "AUTO_STYLE", "Injectable", "sequence", "style", "tslib_1.__extends", "tslib_1.__assign", "tslib_1.__values", "AnimationGroupPlayer", "ONE_SECOND"], "mappings": ";;;;;;;;;;;;IAAA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;;AAEF,IAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;;AAED,IAAO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;IACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AACD,AAUA;AACA,IAAO,SAAS,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;IAC1D,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;IACjI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACnI,SAAS,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IACtJ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;AACD,AAiDA;AACA,IAAO,SAAS,QAAQ,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACtE,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,OAAO;IACX,QAAQ,IAAI,EAAE,YAAY;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;IAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACpD,SAAS;IACT,KAAK,CAAC;IACN,CAAC;;AAED,IAAO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACnF,KAAK;IACL,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3C,YAAY;IACZ,QAAQ,IAAI;IACZ,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,SAAS;IACT,gBAAgB,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;IACzC,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;;AAED,IAAO,SAAS,QAAQ,GAAG;IAC3B,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;IACtD,QAAQ,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI,OAAO,EAAE,CAAC;IACd,CAAC;;aCzHe,SAAS;QACvB,QAAQ,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,EAAE;IACnF,CAAC;AAED,aAAgB,MAAM;QACpB,QAAQ,OAAO,OAAO,KAAK,WAAW,EAAE;IAC1C,CAAC;AAED,aAAgB,mBAAmB,CAAC,OAA0B;QAC5D,QAAQ,OAAO,CAAC,MAAM;YACpB,KAAK,CAAC;gBACJ,OAAO,IAAIA,8BAAmB,EAAE,CAAC;YACnC,KAAK,CAAC;gBACJ,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;YACpB;gBACE,OAAO,IAAIC,gCAAqB,CAAC,OAAO,CAAC,CAAC;SAC7C;IACH,CAAC;AAED,aAAgB,kBAAkB,CAC9B,MAAuB,EAAE,UAAoC,EAAE,OAAY,EAC3E,SAAuB,EAAE,SAA0B,EACnD,UAA2B;QADF,0BAAA,EAAA,cAA0B;QACnD,2BAAA,EAAA,eAA2B;QAC7B,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,mBAAmB,GAAiB,EAAE,CAAC;QAC7C,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;QACxB,IAAI,gBAAgB,GAAoB,IAAI,CAAC;QAC7C,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE;YAClB,IAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAW,CAAC;YACtC,IAAM,YAAY,GAAG,MAAM,IAAI,cAAc,CAAC;YAC9C,IAAM,kBAAkB,GAAe,CAAC,YAAY,IAAI,gBAAgB,KAAK,EAAE,CAAC;YAChF,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC1B,IAAI,cAAc,GAAG,IAAI,CAAC;gBAC1B,IAAI,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;gBAC/B,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACrB,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;oBAC1E,QAAQ,eAAe;wBACrB,KAAKC,qBAAS;4BACZ,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;4BAClC,MAAM;wBAER,KAAKC,qBAAU;4BACb,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;4BACnC,MAAM;wBAER;4BACE,eAAe;gCACX,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;4BAClF,MAAM;qBACT;iBACF;gBACD,kBAAkB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;aACtD,CAAC,CAAC;YACH,IAAI,CAAC,YAAY,EAAE;gBACjB,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9C;YACD,gBAAgB,GAAG,kBAAkB,CAAC;YACtC,cAAc,GAAG,MAAM,CAAC;SACzB,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,IAAM,UAAU,GAAG,OAAO,CAAC;YAC3B,MAAM,IAAI,KAAK,CACX,mDAAiD,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAG,CAAC,CAAC;SAC9F;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;AAED,aAAgB,cAAc,CAC1B,MAAuB,EAAE,SAAiB,EAAE,KAAiC,EAC7E,QAA6B;QAC/B,QAAQ,SAAS;YACf,KAAK,OAAO;gBACV,MAAM,CAAC,OAAO,CAAC,cAAM,OAAA,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAA,CAAC,CAAC;gBACpF,MAAM;YACR,KAAK,MAAM;gBACT,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,GAAA,CAAC,CAAC;gBAClF,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,GAAA,CAAC,CAAC;gBACxF,MAAM;SACT;IACH,CAAC;AAED,aAAgB,kBAAkB,CAC9B,CAAiB,EAAE,SAAiB,EAAE,MAAuB;QAC/D,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;QACzD,IAAM,KAAK,GAAG,kBAAkB,CAC5B,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,SAAS,EAC1E,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAM,IAAI,GAAI,CAAS,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,IAAI,IAAI,IAAI,EAAE;YACf,KAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;SAChC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;AAED,aAAgB,kBAAkB,CAC9B,OAAY,EAAE,WAAmB,EAAE,SAAiB,EAAE,OAAe,EAAE,SAAsB,EAC7F,SAAqB,EAAE,QAAkB;QAD8B,0BAAA,EAAA,cAAsB;QAC7F,0BAAA,EAAA,aAAqB;QACvB,OAAO,EAAC,OAAO,SAAA,EAAE,WAAW,aAAA,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAE,SAAS,WAAA,EAAE,SAAS,WAAA,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAC,CAAC;IAChG,CAAC;AAED,aAAgB,eAAe,CAC3B,GAAwC,EAAE,GAAQ,EAAE,YAAiB;QACvE,IAAI,KAAU,CAAC;QACf,IAAI,GAAG,YAAY,GAAG,EAAE;YACtB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,KAAK,EAAE;gBACV,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;aACpC;SACF;aAAM;YACL,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACjB,IAAI,CAAC,KAAK,EAAE;gBACV,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;aACjC;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;AAED,aAAgB,oBAAoB,CAAC,OAAe;QAClD,IAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAM,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAC9C,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAChD,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,SAAS,GAAsC,UAAC,IAAS,EAAE,IAAS,IAAK,OAAA,KAAK,GAAA,CAAC;IACnF,IAAI,QAAQ,GAAgD,UAAC,OAAY,EAAE,QAAgB;QACvF,OAAA,KAAK;IAAL,CAAK,CAAC;IACV,IAAI,MAAM,GACN,UAAC,OAAY,EAAE,QAAgB,EAAE,KAAc;QAC7C,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAEN;IACA;IACA,IAAM,OAAO,GAAG,MAAM,EAAE,CAAC;IACzB,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;;QAE7C,SAAS,GAAG,UAAC,IAAS,EAAE,IAAS,IAAO,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAY,CAAC,EAAE,CAAC;QAEjF,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;YACxC,QAAQ,GAAG,UAAC,OAAY,EAAE,QAAgB,IAAK,OAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAA,CAAC;SAC1E;aAAM;YACL,IAAM,KAAK,GAAG,OAAO,CAAC,SAAgB,CAAC;YACvC,IAAM,IAAE,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;gBACnF,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;YAC1D,IAAI,IAAE,EAAE;gBACN,QAAQ,GAAG,UAAC,OAAY,EAAE,QAAgB,IAAK,OAAA,IAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAA,CAAC;aAC9E;SACF;QAED,MAAM,GAAG,UAAC,OAAY,EAAE,QAAgB,EAAE,KAAc;YACtD,IAAI,OAAO,GAAU,EAAE,CAAC;YACxB,IAAI,KAAK,EAAE;gBACT,OAAO,CAAC,IAAI,OAAZ,OAAO,WAAS,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAE;aACrD;iBAAM;gBACL,IAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC5C,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACnB;aACF;YACD,OAAO,OAAO,CAAC;SAChB,CAAC;KACH;IAED,SAAS,oBAAoB,CAAC,IAAY;;;QAGxC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;IACzC,CAAC;IAED,IAAI,YAAY,GAAsB,IAAI,CAAC;IAC3C,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,aAAgB,qBAAqB,CAAC,IAAY;QAChD,IAAI,CAAC,YAAY,EAAE;YACjB,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;YACnC,UAAU,GAAG,YAAc,CAAC,KAAK,IAAI,kBAAkB,IAAI,YAAc,CAAC,KAAK,IAAI,KAAK,CAAC;SAC1F;QAED,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,YAAc,CAAC,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;YACvD,MAAM,GAAG,IAAI,IAAI,YAAc,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;gBACzB,IAAM,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC3E,MAAM,GAAG,SAAS,IAAI,YAAc,CAAC,KAAK,CAAC;aAC5C;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;AAED,aAAgB,WAAW;QACzB,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;YAClC,OAAO,QAAQ,CAAC,IAAI,CAAC;SACtB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;AAED,QAAa,cAAc,GAAG,QAAQ,CAAC;AACvC,QAAa,eAAe,GAAG,SAAS,CAAC;AACzC,QAAa,WAAW,GAAG,MAAM,CAAC;AAElC,aAAgB,mBAAmB,CAAC,MAA4B;QAC9D,IAAM,MAAM,GAAyB,EAAE,CAAC;QACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC9B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SAChC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;;ICzND;;;AAIA;QAAA;SAuBC;QAtBC,mDAAqB,GAArB,UAAsB,IAAY,IAAa,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;QAEpF,4CAAc,GAAd,UAAe,OAAY,EAAE,QAAgB;YAC3C,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1C;QAED,6CAAe,GAAf,UAAgB,IAAS,EAAE,IAAS,IAAa,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;QAEtF,mCAAK,GAAL,UAAM,OAAY,EAAE,QAAgB,EAAE,KAAc;YAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC9C;QAED,0CAAY,GAAZ,UAAa,OAAY,EAAE,IAAY,EAAE,YAAqB;YAC5D,OAAO,YAAY,IAAI,EAAE,CAAC;SAC3B;QAED,qCAAO,GAAP,UACI,OAAY,EAAE,SAA6C,EAAE,QAAgB,EAAE,KAAa,EAC5F,MAAc,EAAE,eAA2B,EAC3C,uBAAiC;YADjB,gCAAA,EAAA,oBAA2B;YAE7C,OAAO,IAAIH,8BAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACjD;QAtBU,mBAAmB;YAD/BI,eAAU,EAAE;WACA,mBAAmB,CAuB/B;QAAD,0BAAC;KAvBD,IAuBC;IAED;;;AAGA;QAAA;SAgBC;QAfQ,oBAAI,GAAoB,IAAI,mBAAmB,EAAE,CAAC;QAe3D,sBAAC;KAhBD;;IC5CA;;;;;;;AAOA,IAKO,IAAM,UAAU,GAAG,IAAI,CAAC;AAE/B,IAAO,IAAM,uBAAuB,GAAG,IAAI,CAAC;AAC5C,IAAO,IAAM,qBAAqB,GAAG,IAAI,CAAC;AAC1C,IAAO,IAAM,eAAe,GAAG,UAAU,CAAC;AAC1C,IAAO,IAAM,eAAe,GAAG,UAAU,CAAC;AAC1C,IAEO,IAAM,oBAAoB,GAAG,YAAY,CAAC;AACjD,IAAO,IAAM,mBAAmB,GAAG,aAAa,CAAC;AACjD,IAAO,IAAM,sBAAsB,GAAG,cAAc,CAAC;AACrD,IAAO,IAAM,qBAAqB,GAAG,eAAe,CAAC;AAErD,aAAgB,kBAAkB,CAAC,KAAsB;QACvD,IAAI,OAAO,KAAK,IAAI,QAAQ;YAAE,OAAO,KAAK,CAAC;QAE3C,IAAM,OAAO,GAAI,KAAgB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAE7C,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,SAAS,qBAAqB,CAAC,KAAa,EAAE,IAAY;QACxD,QAAQ,IAAI;YACV,KAAK,GAAG;gBACN,OAAO,KAAK,GAAG,UAAU,CAAC;YAC5B;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;AAED,aAAgB,aAAa,CACzB,OAAyC,EAAE,MAAa,EAAE,mBAA6B;QACzF,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC;YACrB,OAAO;YACvB,mBAAmB,CAAgB,OAAO,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;IAC/E,CAAC;IAED,SAAS,mBAAmB,CACxB,GAAoB,EAAE,MAAgB,EAAE,mBAA6B;QACvE,IAAM,KAAK,GAAG,0EAA0E,CAAC;QACzF,IAAI,QAAgB,CAAC;QACrB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,MAAM,GAAW,EAAE,CAAC;QACxB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,OAAO,KAAK,IAAI,EAAE;gBACpB,MAAM,CAAC,IAAI,CAAC,iCAA8B,GAAG,mBAAe,CAAC,CAAC;gBAC9D,OAAO,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC;aAC5C;YAED,QAAQ,GAAG,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAErE,IAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,KAAK,GAAG,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACnE;YAED,IAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,SAAS,EAAE;gBACb,MAAM,GAAG,SAAS,CAAC;aACpB;SACF;aAAM;YACL,QAAQ,GAAW,GAAG,CAAC;SACxB;QAED,IAAI,CAAC,mBAAmB,EAAE;YACxB,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;YAC/B,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAChB,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;gBAChF,cAAc,GAAG,IAAI,CAAC;aACvB;YACD,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC7E,cAAc,GAAG,IAAI,CAAC;aACvB;YACD,IAAI,cAAc,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,iCAA8B,GAAG,mBAAe,CAAC,CAAC;aAChF;SACF;QAED,OAAO,EAAC,QAAQ,UAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAC,CAAC;IACnC,CAAC;AAED,aAAgB,OAAO,CACnB,GAAyB,EAAE,WAAsC;QAAtC,4BAAA,EAAA,gBAAsC;QACnE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAM,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,WAAW,CAAC;IACrB,CAAC;AAED,aAAgB,eAAe,CAAC,MAAiC;QAC/D,IAAM,gBAAgB,GAAe,EAAE,CAAC;QACxC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,CAAC,GAAA,CAAC,CAAC;SACnE;aAAM;YACL,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;SAC7C;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;AAED,aAAgB,UAAU,CACtB,MAAkB,EAAE,aAAsB,EAAE,WAA4B;QAA5B,4BAAA,EAAA,gBAA4B;QAC1E,IAAI,aAAa,EAAE;;;;YAIjB,KAAK,IAAI,IAAI,IAAI,MAAM,EAAE;gBACvB,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;aAClC;SACF;aAAM;YACL,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;SAC9B;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,SAAS,uBAAuB,CAAC,OAAY,EAAE,GAAW,EAAE,KAAa;;;QAGvE,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;SAChC;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,SAAS,mBAAmB,CAAC,OAAY;;;;;QAKvC,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,cAAc,IAAI,uBAAuB,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9F;QACD,KAAK,IAAM,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE;;YAE/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC7D,SAAS;aACV;YACD,IAAM,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACzC,cAAc,IAAI,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACjF;QACD,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAChD,CAAC;AAED,aAAgB,SAAS,CAAC,OAAY,EAAE,MAAkB,EAAE,YAAmC;QAC7F,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC9B,IAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACtD,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;iBAC/C;gBACD,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;aACzC,CAAC,CAAC;;YAEH,IAAI,MAAM,EAAE,EAAE;gBACZ,mBAAmB,CAAC,OAAO,CAAC,CAAC;aAC9B;SACF;IACH,CAAC;AAED,aAAgB,WAAW,CAAC,OAAY,EAAE,MAAkB;QAC1D,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC9B,IAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC5C,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;aAC/B,CAAC,CAAC;;YAEH,IAAI,MAAM,EAAE,EAAE;gBACZ,mBAAmB,CAAC,OAAO,CAAC,CAAC;aAC9B;SACF;IACH,CAAC;AAED,aAAgB,uBAAuB,CAAC,KAA8C;QAEpF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;gBAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;YACvC,OAAOC,mBAAQ,CAAC,KAAK,CAAC,CAAC;SACxB;QACD,OAAO,KAA0B,CAAC;IACpC,CAAC;AAED,aAAgB,mBAAmB,CAC/B,KAAsB,EAAE,OAAyB,EAAE,MAAa;QAClE,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;QACpC,IAAM,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,OAAO,CAAC,UAAA,OAAO;gBACrB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;oBACnC,MAAM,CAAC,IAAI,CACP,iDAA+C,OAAO,iCAA8B,CAAC,CAAC;iBAC3F;aACF,CAAC,CAAC;SACJ;IACH,CAAC;IAED,IAAM,WAAW,GACb,IAAI,MAAM,CAAI,uBAAuB,qBAAgB,qBAAuB,EAAE,GAAG,CAAC,CAAC;AACvF,aAAgB,kBAAkB,CAAC,KAAsB;QACvD,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAE7B,IAAI,KAAK,SAAK,CAAC;YACf,OAAO,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAW,CAAC,CAAC;aACjC;YACD,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;SAC3B;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;AAED,aAAgB,iBAAiB,CAC7B,KAAsB,EAAE,MAA6B,EAAE,MAAa;QACtE,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAClC,IAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,UAAC,CAAC,EAAE,OAAO;YACnD,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;;YAE/B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACnC,MAAM,CAAC,IAAI,CAAC,oDAAkD,OAAS,CAAC,CAAC;gBACzE,QAAQ,GAAG,EAAE,CAAC;aACf;YACD,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAC5B,CAAC,CAAC;;QAGH,OAAO,GAAG,IAAI,QAAQ,GAAG,KAAK,GAAG,GAAG,CAAC;IACvC,CAAC;AAED,aAAgB,eAAe,CAAC,QAAa;QAC3C,IAAM,GAAG,GAAU,EAAE,CAAC;QACtB,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;YACjB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;AAED,IAiBA,IAAM,gBAAgB,GAAG,eAAe,CAAC;AACzC,aAAgB,mBAAmB,CAAC,KAAa;QAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAAC,WAAW;iBAAX,UAAW,EAAX,qBAAW,EAAX,IAAW;gBAAX,sBAAW;;YAAK,OAAA,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;SAAA,CAAC,CAAC;IAC9E,CAAC;IAED,SAAS,mBAAmB,CAAC,KAAa;QACxC,OAAO,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IACjE,CAAC;AAED,aAAgB,8BAA8B,CAAC,QAAgB,EAAE,KAAa;QAC5E,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;IACvC,CAAC;AAED,aAAgB,kCAAkC,CAC9C,OAAY,EAAE,SAAiC,EAAE,cAAoC;QACvF,IAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,kBAAkB,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE;YACjD,IAAI,kBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,mBAAiB,GAAa,EAAE,CAAC;YACrC,kBAAkB,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC7B,IAAI,CAAC,kBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC1C,mBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC9B;gBACD,kBAAgB,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;aAC/C,CAAC,CAAC;YAEH,IAAI,mBAAiB,CAAC,MAAM,EAAE;;oBAG1B,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACtB,mBAAiB,CAAC,OAAO,CAAC,UAAS,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;;gBAFxF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;;iBAGxC;aACF;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;AAMD,aAAgB,YAAY,CAAC,OAAY,EAAE,IAAS,EAAE,OAAY;QAChE,QAAQ,IAAI,CAAC,IAAI;YACf;gBACE,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC7C;gBACE,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC3C;gBACE,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAChD;gBACE,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9C;gBACE,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC3C;gBACE,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC7C;gBACE,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C;gBACE,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC3C;gBACE,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C;gBACE,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAClD;gBACE,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAChD;gBACE,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC3C;gBACE,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC7C;gBACE,MAAM,IAAI,KAAK,CAAC,gDAA8C,IAAI,CAAC,IAAM,CAAC,CAAC;SAC9E;IACH,CAAC;AAED,aAAgB,YAAY,CAAC,OAAY,EAAE,IAAY;QACrD,OAAa,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;;IC3VD;;;;;;;AAOA,IAAO,IAAM,SAAS,GAAG,GAAG,CAAC;AAI7B,aAAgB,mBAAmB,CAC/B,eAA6C,EAAE,MAAgB;QACjE,IAAM,WAAW,GAA0B,EAAE,CAAC;QAC9C,IAAI,OAAO,eAAe,IAAI,QAAQ,EAAE;YAC7B,eAAgB;iBACpB,KAAK,CAAC,SAAS,CAAC;iBAChB,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,uBAAuB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,GAAA,CAAC,CAAC;SACxE;aAAM;YACL,WAAW,CAAC,IAAI,CAAsB,eAAe,CAAC,CAAC;SACxD;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,SAAS,uBAAuB,CAC5B,QAAgB,EAAE,WAAkC,EAAE,MAAgB;QACxE,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YACtB,IAAM,MAAM,GAAG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,OAAO,MAAM,IAAI,UAAU,EAAE;gBAC/B,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,OAAO;aACR;YACD,QAAQ,GAAG,MAAgB,CAAC;SAC7B;QAED,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QACxE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,MAAM,CAAC,IAAI,CAAC,0CAAuC,QAAQ,wBAAoB,CAAC,CAAC;YACjF,OAAO,WAAW,CAAC;SACpB;QAED,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QAE3D,IAAM,kBAAkB,GAAG,SAAS,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,CAAC;QAC1E,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;YAC9C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;SAC5D;IACH,CAAC;IAED,SAAS,mBAAmB,CAAC,KAAa,EAAE,MAAgB;QAC1D,QAAQ,KAAK;YACX,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC;YACrB,KAAK,YAAY;gBACf,OAAO,UAAC,SAAc,EAAE,OAAY,IAAc,OAAA,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,GAAA,CAAC;YAChG,KAAK,YAAY;gBACf,OAAO,UAAC,SAAc,EAAE,OAAY,IAAc,OAAA,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,GAAA,CAAC;YAChG;gBACE,MAAM,CAAC,IAAI,CAAC,kCAA+B,KAAK,wBAAoB,CAAC,CAAC;gBACtE,OAAO,QAAQ,CAAC;SACnB;IACH,CAAC;IAED;IACA;IACA;IACA;IACA,IAAM,mBAAmB,GAAG,IAAI,GAAG,CAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAM,oBAAoB,GAAG,IAAI,GAAG,CAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAE7D,SAAS,oBAAoB,CAAC,GAAW,EAAE,GAAW;QACpD,IAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxF,IAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAExF,OAAO,UAAC,SAAc,EAAE,OAAY;YAClC,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,CAAC;YACpD,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,CAAC;YAElD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;gBACpE,QAAQ,GAAG,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACrF;YACD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;gBAClE,QAAQ,GAAG,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACnF;YAED,OAAO,QAAQ,IAAI,QAAQ,CAAC;SAC7B,CAAC;IACJ,CAAC;;IC3ED,IAAM,UAAU,GAAG,OAAO,CAAC;IAC3B,IAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,OAAM,UAAU,SAAO,EAAE,GAAG,CAAC,CAAC;IAElE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,aAAgB,iBAAiB,CAC7B,MAAuB,EAAE,QAAiD,EAC1E,MAAa;QACf,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACxE,CAAC;IAED,IAAM,aAAa,GAAG,EAAE,CAAC;IAEzB;QACE,oCAAoB,OAAwB;YAAxB,YAAO,GAAP,OAAO,CAAiB;SAAI;QAEhD,0CAAK,GAAL,UAAM,QAA+C,EAAE,MAAa;YAElE,IAAM,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAC5C,OAAmC,YAAY,CAC3C,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;SACvD;QAEO,kEAA6B,GAArC,UAAsC,OAAmC;YACvE,OAAO,CAAC,oBAAoB,GAAG,aAAa,CAAC;YAC7C,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;YAC7B,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;YAC5C,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;SACzB;QAED,iDAAY,GAAZ,UAAa,QAAkC,EAAE,OAAmC;YAApF,iBAqCC;YAnCC,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;YACxC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpC,IAAM,MAAM,GAAe,EAAE,CAAC;YAC9B,IAAM,WAAW,GAAoB,EAAE,CAAC;YACxC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;gBAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CACf,wFAAwF,CAAC,CAAC;aAC/F;YAED,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC9B,KAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;gBAC5C,IAAI,GAAG,CAAC,IAAI,mBAAiC;oBAC3C,IAAM,UAAQ,GAAG,GAA6B,CAAC;oBAC/C,IAAM,MAAI,GAAG,UAAQ,CAAC,IAAI,CAAC;oBAC3B,MAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;wBACxC,UAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;wBAClB,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,UAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;qBACjD,CAAC,CAAC;oBACH,UAAQ,CAAC,IAAI,GAAG,MAAI,CAAC;iBACtB;qBAAM,IAAI,GAAG,CAAC,IAAI,wBAAsC;oBACvD,IAAM,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,GAAkC,EAAE,OAAO,CAAC,CAAC;oBACrF,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;oBACpC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;oBAChC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC9B;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC,IAAI,CACf,yEAAyE,CAAC,CAAC;iBAChF;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI;gBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,QAAA,EAAE,WAAW,aAAA,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA;gBAC9D,OAAO,EAAE,IAAI;aACd,CAAC;SACH;QAED,+CAAU,GAAV,UAAW,QAAgC,EAAE,OAAmC;YAC9E,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC3D,IAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;YACxE,IAAI,QAAQ,CAAC,qBAAqB,EAAE;gBAClC,IAAM,aAAW,GAAG,IAAI,GAAG,EAAU,CAAC;gBACtC,IAAM,QAAM,GAAG,SAAS,IAAI,EAAE,CAAC;gBAC/B,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;oBAC3B,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;wBACnB,IAAM,WAAS,GAAG,KAAY,CAAC;wBAC/B,MAAM,CAAC,IAAI,CAAC,WAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;4BACjC,kBAAkB,CAAC,WAAS,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gCAC7C,IAAI,CAAC,QAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oCAC/B,aAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iCACtB;6BACF,CAAC,CAAC;yBACJ,CAAC,CAAC;qBACJ;iBACF,CAAC,CAAC;gBACH,IAAI,aAAW,CAAC,IAAI,EAAE;oBACpB,IAAM,cAAc,GAAG,eAAe,CAAC,aAAW,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7D,OAAO,CAAC,MAAM,CAAC,IAAI,CACf,aAAU,QAAQ,CAAC,IAAI,uFAAiF,cAAc,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;iBAC1I;aACF;YAED,OAAO;gBACL,IAAI;gBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ;gBACf,OAAO,EAAE,SAAS,GAAG,EAAC,MAAM,EAAE,SAAS,EAAC,GAAG,IAAI;aAChD,CAAC;SACH;QAED,oDAAe,GAAf,UAAgB,QAAqC,EAAE,OAAmC;YAExF,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;YACvB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACrB,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;YAC3F,IAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAEpE,OAAO;gBACL,IAAI;gBACJ,QAAQ,UAAA;gBACR,SAAS,WAAA;gBACT,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,kDAAa,GAAb,UAAc,QAAmC,EAAE,OAAmC;YAAtF,iBAOC;YALC,OAAO;gBACL,IAAI;gBACJ,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,CAAC,KAAI,EAAE,CAAC,EAAE,OAAO,CAAC,GAAA,CAAC;gBAC9D,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,+CAAU,GAAV,UAAW,QAAgC,EAAE,OAAmC;YAAhF,iBAgBC;YAfC,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACxC,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI;gBACnC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;gBAClC,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACnD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;gBAC3D,OAAO,QAAQ,CAAC;aACjB,CAAC,CAAC;YAEH,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC;YACnC,OAAO;gBACL,IAAI;gBACJ,KAAK,OAAA;gBACL,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,iDAAY,GAAZ,UAAa,QAAkC,EAAE,OAAmC;YAElF,IAAM,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACvE,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAE1C,IAAI,QAA+B,CAAC;YACpC,IAAI,aAAa,GAAsB,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAGC,gBAAK,CAAC,EAAE,CAAC,CAAC;YACrF,IAAI,aAAa,CAAC,IAAI,uBAAqC;gBACzD,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,aAAmD,EAAE,OAAO,CAAC,CAAC;aAC9F;iBAAM;gBACL,IAAI,eAAa,GAAG,QAAQ,CAAC,MAAgC,CAAC;gBAC9D,IAAI,OAAO,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,eAAa,EAAE;oBAClB,OAAO,GAAG,IAAI,CAAC;oBACf,IAAM,YAAY,GAAsC,EAAE,CAAC;oBAC3D,IAAI,SAAS,CAAC,MAAM,EAAE;wBACpB,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;qBAC3C;oBACD,eAAa,GAAGA,gBAAK,CAAC,YAAY,CAAC,CAAC;iBACrC;gBACD,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;gBAC5D,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,eAAa,EAAE,OAAO,CAAC,CAAC;gBAC1D,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;gBAChC,QAAQ,GAAG,SAAS,CAAC;aACtB;YAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACrC,OAAO;gBACL,IAAI;gBACJ,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,QAAQ;gBACf,OAAO,EAAE,IAAI;aACd,CAAC;SACH;QAED,+CAAU,GAAV,UAAW,QAAgC,EAAE,OAAmC;YAC9E,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACrC,OAAO,GAAG,CAAC;SACZ;QAEO,kDAAa,GAArB,UAAsB,QAAgC,EAAE,OAAmC;YAEzF,IAAM,MAAM,GAA4B,EAAE,CAAC;YAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACjC,QAAQ,CAAC,MAAiC,CAAC,OAAO,CAAC,UAAA,UAAU;oBAC5D,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;wBACjC,IAAI,UAAU,IAAIH,qBAAU,EAAE;4BAC5B,MAAM,CAAC,IAAI,CAAC,UAAoB,CAAC,CAAC;yBACnC;6BAAM;4BACL,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAmC,UAAU,qBAAkB,CAAC,CAAC;yBACtF;qBACF;yBAAM;wBACL,MAAM,CAAC,IAAI,CAAC,UAAwB,CAAC,CAAC;qBACvC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aAC9B;YAED,IAAI,qBAAqB,GAAG,KAAK,CAAC;YAClC,IAAI,eAAe,GAAgB,IAAI,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAA,SAAS;gBACtB,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;oBACvB,IAAM,QAAQ,GAAG,SAAuB,CAAC;oBACzC,IAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAClC,IAAI,MAAM,EAAE;wBACV,eAAe,GAAG,MAAgB,CAAC;wBACnC,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBAC3B;oBACD,IAAI,CAAC,qBAAqB,EAAE;wBAC1B,KAAK,IAAI,IAAI,IAAI,QAAQ,EAAE;4BACzB,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;4BAC7B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;gCAC1D,qBAAqB,GAAG,IAAI,CAAC;gCAC7B,MAAM;6BACP;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI;gBACJ,MAAM,QAAA;gBACN,MAAM,EAAE,eAAe;gBACvB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,qBAAqB,uBAAA;gBAC9C,OAAO,EAAE,IAAI;aACd,CAAC;SACH;QAEO,sDAAiB,GAAzB,UAA0B,GAAa,EAAE,OAAmC;YAA5E,iBA4CC;YA3CC,IAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;YAC9C,IAAI,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC;YAClC,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;YACpC,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE;gBAC5B,SAAS,IAAI,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;aAC/C;YAED,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gBACtB,IAAI,OAAO,KAAK,IAAI,QAAQ;oBAAE,OAAO;gBAErC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBAC7B,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;wBAC7C,OAAO,CAAC,MAAM,CAAC,IAAI,CACf,uCAAoC,IAAI,sDAAkD,CAAC,CAAC;wBAChG,OAAO;qBACR;oBAED,IAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,oBAAsB,CAAC,CAAC;oBAChF,IAAM,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC7C,IAAI,oBAAoB,GAAG,IAAI,CAAC;oBAChC,IAAI,cAAc,EAAE;wBAClB,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,cAAc,CAAC,SAAS;4BAC7D,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE;4BACrC,OAAO,CAAC,MAAM,CAAC,IAAI,CACf,wBAAqB,IAAI,8CAAuC,cAAc,CAAC,SAAS,mBAAY,cAAc,CAAC,OAAO,mFAA4E,SAAS,mBAAY,OAAO,SAAK,CAAC,CAAC;4BAC7O,oBAAoB,GAAG,KAAK,CAAC;yBAC9B;;;;wBAKD,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;qBACtC;oBAED,IAAI,oBAAoB,EAAE;wBACxB,eAAe,CAAC,IAAI,CAAC,GAAG,EAAC,SAAS,WAAA,EAAE,OAAO,SAAA,EAAC,CAAC;qBAC9C;oBAED,IAAI,OAAO,CAAC,OAAO,EAAE;wBACnB,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnE;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;QAED,mDAAc,GAAd,UAAe,QAA4C,EAAE,OAAmC;YAAhG,iBAgEC;YA9DC,IAAM,GAAG,GAAiB,EAAC,IAAI,qBAAmC,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;YAC7F,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBAChF,OAAO,GAAG,CAAC;aACZ;YAED,IAAM,mBAAmB,GAAG,CAAC,CAAC;YAE9B,IAAI,yBAAyB,GAAG,CAAC,CAAC;YAClC,IAAM,OAAO,GAAa,EAAE,CAAC;YAC7B,IAAI,iBAAiB,GAAG,KAAK,CAAC;YAC9B,IAAI,mBAAmB,GAAG,KAAK,CAAC;YAChC,IAAI,cAAc,GAAW,CAAC,CAAC;YAE/B,IAAM,SAAS,GAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,MAAM;gBACrD,IAAM,KAAK,GAAG,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAClD,IAAI,SAAS,GACT,KAAK,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACtE,IAAI,MAAM,GAAW,CAAC,CAAC;gBACvB,IAAI,SAAS,IAAI,IAAI,EAAE;oBACrB,yBAAyB,EAAE,CAAC;oBAC5B,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;iBACnC;gBACD,mBAAmB,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;gBACtE,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,GAAG,cAAc,CAAC;gBACjE,cAAc,GAAG,MAAM,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,OAAO,KAAK,CAAC;aACd,CAAC,CAAC;YAEH,IAAI,mBAAmB,EAAE;gBACvB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;aACpF;YAED,IAAI,iBAAiB,EAAE;gBACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;aAC7E;YAED,IAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;YACrC,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,yBAAyB,GAAG,CAAC,IAAI,yBAAyB,GAAG,MAAM,EAAE;gBACvE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;aAC9F;iBAAM,IAAI,yBAAyB,IAAI,CAAC,EAAE;gBACzC,eAAe,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;aACtD;YAED,IAAM,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;YACzB,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACxC,IAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAuB,CAAC;YAC9D,IAAM,eAAe,GAAG,qBAAqB,CAAC,QAAQ,CAAC;YACvD,SAAS,CAAC,OAAO,CAAC,UAAC,EAAE,EAAE,CAAC;gBACtB,IAAM,MAAM,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC3F,IAAM,qBAAqB,GAAG,MAAM,GAAG,eAAe,CAAC;gBACvD,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,qBAAqB,CAAC,KAAK,GAAG,qBAAqB,CAAC;gBACxF,qBAAqB,CAAC,QAAQ,GAAG,qBAAqB,CAAC;gBACvD,KAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACpC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;gBAEnB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACrB,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC;SACZ;QAED,mDAAc,GAAd,UAAe,QAAoC,EAAE,OAAmC;YAEtF,OAAO;gBACL,IAAI;gBACJ,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;gBACnF,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,sDAAiB,GAAjB,UAAkB,QAAuC,EAAE,OAAmC;YAE5F,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;gBACL,IAAI;gBACJ,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,oDAAe,GAAf,UAAgB,QAAqC,EAAE,OAAmC;YAExF,OAAO;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;gBAC3D,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,+CAAU,GAAV,UAAW,QAAgC,EAAE,OAAmC;YAC9E,IAAM,cAAc,GAAG,OAAO,CAAC,oBAAsB,CAAC;YACtD,IAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,CAA0B,CAAC;YAElE,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;YAC1B,IAAA,oDAA8D,EAA7D,gBAAQ,EAAE,mBAAmD,CAAC;YACrE,OAAO,CAAC,oBAAoB;gBACxB,cAAc,CAAC,MAAM,IAAI,cAAc,GAAG,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC;YACzE,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;YAE3E,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;YAC3F,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAC5B,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC;YAE9C,OAAO;gBACL,IAAI;gBACJ,QAAQ,UAAA;gBACR,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;gBACzB,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,aAAA,EAAE,SAAS,WAAA;gBACpD,gBAAgB,EAAE,QAAQ,CAAC,QAAQ;gBACnC,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;aACrD,CAAC;SACH;QAED,iDAAY,GAAZ,UAAa,QAAkC,EAAE,OAAmC;YAElF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBACzB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;aACrE;YACD,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,KAAK,MAAM;gBACvC,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAC;gBACvC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE1D,OAAO;gBACL,IAAI;gBACJ,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,SAAA;gBAC5F,OAAO,EAAE,IAAI;aACd,CAAC;SACH;QACH,iCAAC;IAAD,CAAC,IAAA;IAED,SAAS,iBAAiB,CAAC,QAAgB;QACzC,IAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,IAAI,UAAU,GAAA,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;QACjG,IAAI,YAAY,EAAE;YAChB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;SACnD;;QAGD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,mBAAmB,CAAC;aACxC,OAAO,CAAC,OAAO,EAAE,UAAA,KAAK,IAAI,OAAA,mBAAmB,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAA,CAAC;aACtE,OAAO,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;QAE9D,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAClC,CAAC;IAGD,SAAS,eAAe,CAAC,GAA+B;QACtD,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACnC,CAAC;IAMD;QAUE,oCAAmB,MAAa;YAAb,WAAM,GAAN,MAAM,CAAO;YATzB,eAAU,GAAW,CAAC,CAAC;YACvB,aAAQ,GAAW,CAAC,CAAC;YACrB,sBAAiB,GAAqC,IAAI,CAAC;YAC3D,iBAAY,GAAgC,IAAI,CAAC;YACjD,yBAAoB,GAAgB,IAAI,CAAC;YACzC,0BAAqB,GAAmB,IAAI,CAAC;YAC7C,gBAAW,GAAW,CAAC,CAAC;YACxB,oBAAe,GAAmE,EAAE,CAAC;YACrF,YAAO,GAA0B,IAAI,CAAC;SACT;QACtC,iCAAC;IAAD,CAAC,IAAA;IAED,SAAS,aAAa,CAAC,MAAqD;QAC1E,IAAI,OAAO,MAAM,IAAI,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE3C,IAAI,MAAM,GAAgB,IAAI,CAAC;QAE/B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,CAAC,OAAO,CAAC,UAAA,UAAU;gBACvB,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;oBAC/D,IAAM,GAAG,GAAG,UAAwB,CAAC;oBACrC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC,CAAC;oBAC7C,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;iBACtB;aACF,CAAC,CAAC;SACJ;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC9D,IAAM,GAAG,GAAG,MAAoB,CAAC;YACjC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,QAAQ,CAAC,KAAU;QAC1B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,IAAI,QAAQ,CAAC;IAC3D,CAAC;IAED,SAAS,kBAAkB,CAAC,KAAuC,EAAE,MAAa;QAChF,IAAI,OAAO,GAAwB,IAAI,CAAC;QACxC,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;YACpC,OAAO,GAAG,KAAuB,CAAC;SACnC;aAAM,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;YACnC,IAAM,QAAQ,GAAG,aAAa,CAAC,KAAe,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC;YACjE,OAAO,aAAa,CAAC,QAAkB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;SACjD;QAED,IAAM,QAAQ,GAAG,KAAe,CAAC;QACjC,IAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,GAAA,CAAC,CAAC;QAC5F,IAAI,SAAS,EAAE;YACb,IAAM,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAQ,CAAC;YAC3C,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;YACnB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACxB,OAAO,GAAuB,CAAC;SAChC;QAED,OAAO,GAAG,OAAO,IAAI,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAED,SAAS,yBAAyB,CAAC,OAAgC;QACjE,IAAI,OAAO,EAAE;YACX,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACrB,OAAO,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAG,CAAC;aAC1D;SACF;aAAM;YACL,OAAO,GAAG,EAAE,CAAC;SACd;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,aAAa,CAAC,QAAgB,EAAE,KAAa,EAAE,MAAqB;QAC3E,OAAO,EAAC,QAAQ,UAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAC,CAAC;IACnC,CAAC;;aChiBe,yBAAyB,CACrC,OAAY,EAAE,SAAuB,EAAE,aAAuB,EAAE,cAAwB,EACxF,QAAgB,EAAE,KAAa,EAAE,MAA4B,EAC7D,WAA4B;QADK,uBAAA,EAAA,aAA4B;QAC7D,4BAAA,EAAA,mBAA4B;QAC9B,OAAO;YACL,IAAI;YACJ,OAAO,SAAA;YACP,SAAS,WAAA;YACT,aAAa,eAAA;YACb,cAAc,gBAAA;YACd,QAAQ,UAAA;YACR,KAAK,OAAA;YACL,SAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,MAAM,QAAA,EAAE,WAAW,aAAA;SACjD,CAAC;IACJ,CAAC;;IC5BD;QAAA;YACU,SAAI,GAAG,IAAI,GAAG,EAAuC,CAAC;SAuB/D;QArBC,uCAAO,GAAP,UAAQ,OAAY;YAClB,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1C,IAAI,YAAY,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC3B;iBAAM;gBACL,YAAY,GAAG,EAAE,CAAC;aACnB;YACD,OAAO,YAAY,CAAC;SACrB;QAED,sCAAM,GAAN,UAAO,OAAY,EAAE,YAA4C;YAC/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAClD,IAAI,CAAC,oBAAoB,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,oBAAoB,GAAG,EAAE,CAAC,CAAC;aACnD;YACD,oBAAoB,CAAC,IAAI,OAAzB,oBAAoB,WAAS,YAAY,GAAE;SAC5C;QAED,mCAAG,GAAH,UAAI,OAAY,IAAa,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE;QAE7D,qCAAK,GAAL,cAAU,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,4BAAC;IAAD,CAAC,IAAA;;ICjBD,IAAM,yBAAyB,GAAG,CAAC,CAAC;IACpC,IAAM,WAAW,GAAG,QAAQ,CAAC;IAC7B,IAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACvD,IAAM,WAAW,GAAG,QAAQ,CAAC;IAC7B,IAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,aAAgB,uBAAuB,CACnC,MAAuB,EAAE,WAAgB,EAAE,GAA+B,EAC1E,cAAsB,EAAE,cAAsB,EAAE,cAA+B,EAC/E,WAA4B,EAAE,OAAyB,EACvD,eAAuC,EAAE,MAAkB;QAFX,+BAAA,EAAA,mBAA+B;QAC/E,4BAAA,EAAA,gBAA4B;QACa,uBAAA,EAAA,WAAkB;QAC7D,OAAO,IAAI,+BAA+B,EAAE,CAAC,cAAc,CACvD,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EACrF,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IAED;QAAA;SA+TC;QA9TC,wDAAc,GAAd,UACI,MAAuB,EAAE,WAAgB,EAAE,GAA+B,EAC1E,cAAsB,EAAE,cAAsB,EAAE,cAA0B,EAC1E,WAAuB,EAAE,OAAyB,EAAE,eAAuC,EAC3F,MAAkB;YAAlB,uBAAA,EAAA,WAAkB;YACpB,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;YACjE,IAAM,OAAO,GAAG,IAAI,wBAAwB,CACxC,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACtF,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAC1B,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEnF,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;;YAGjC,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,iBAAiB,EAAE,GAAA,CAAC,CAAC;YACrF,IAAI,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;gBACvD,IAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3C,IAAI,CAAC,EAAE,CAAC,uBAAuB,EAAE,EAAE;oBACjC,EAAE,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBAC5D;aACF;YAED,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,cAAc,EAAE,GAAA,CAAC;gBACpD,CAAC,yBAAyB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;SACjG;QAED,sDAAY,GAAZ,UAAa,GAAe,EAAE,OAAiC;;SAE9D;QAED,oDAAU,GAAV,UAAW,GAAa,EAAE,OAAiC;;SAE1D;QAED,yDAAe,GAAf,UAAgB,GAAkB,EAAE,OAAiC;;SAEpE;QAED,2DAAiB,GAAjB,UAAkB,GAAoB,EAAE,OAAiC;YACvE,IAAM,mBAAmB,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC7E,IAAI,mBAAmB,EAAE;gBACvB,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC3D,IAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;gBACtD,IAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CACtC,mBAAmB,EAAE,YAAY,EAAE,YAAY,CAAC,OAA8B,CAAC,CAAC;gBACpF,IAAI,SAAS,IAAI,OAAO,EAAE;;;oBAGxB,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;iBAC3C;aACF;YACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,yDAAe,GAAf,UAAgB,GAAkB,EAAE,OAAiC;YACnE,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3D,YAAY,CAAC,wBAAwB,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACjD,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAC3E,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAEO,+DAAqB,GAA7B,UACI,YAA4C,EAAE,OAAiC,EAC/E,OAA4B;YAC9B,IAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;YACtD,IAAI,YAAY,GAAG,SAAS,CAAC;;;YAI7B,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;YACxF,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YAC/E,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAClB,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW;oBAC9B,IAAM,kBAAkB,GACpB,OAAO,CAAC,2BAA2B,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;oBACtE,YAAY;wBACR,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;iBACpF,CAAC,CAAC;aACJ;YAED,OAAO,YAAY,CAAC;SACrB;QAED,wDAAc,GAAd,UAAe,GAAiB,EAAE,OAAiC;YACjE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACzC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,uDAAa,GAAb,UAAc,GAAgB,EAAE,OAAiC;YAAjE,iBAmCC;YAlCC,IAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAChD,IAAI,GAAG,GAAG,OAAO,CAAC;YAClB,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;YAE5B,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;gBAChD,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACxC,GAAG,CAAC,wBAAwB,EAAE,CAAC;gBAE/B,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;oBACzB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,mBAAiC;wBACxD,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;wBAC5C,GAAG,CAAC,YAAY,GAAG,0BAA0B,CAAC;qBAC/C;oBAED,IAAM,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAChD,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACF;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;gBACpB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,CAAC,KAAI,EAAE,CAAC,EAAE,GAAG,CAAC,GAAA,CAAC,CAAC;;gBAGnD,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;;;;gBAK5C,IAAI,GAAG,CAAC,eAAe,GAAG,eAAe,EAAE;oBACzC,GAAG,CAAC,wBAAwB,EAAE,CAAC;iBAChC;aACF;YAED,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,oDAAU,GAAV,UAAW,GAAa,EAAE,OAAiC;YAA3D,iBAuBC;YAtBC,IAAM,cAAc,GAAsB,EAAE,CAAC;YAC7C,IAAI,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;YACvD,IAAM,KAAK,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE3F,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBACjB,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC3D,IAAI,KAAK,EAAE;oBACT,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;iBACnC;gBAED,YAAY,CAAC,KAAI,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;gBACpC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBAChF,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;aACnD,CAAC,CAAC;;;;YAKH,cAAc,CAAC,OAAO,CAClB,UAAA,QAAQ,IAAI,OAAA,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,QAAQ,CAAC,GAAA,CAAC,CAAC;YAChF,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAC/C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAEO,sDAAY,GAApB,UAAqB,GAAc,EAAE,OAAiC;YACpE,IAAK,GAAwB,CAAC,OAAO,EAAE;gBACrC,IAAM,QAAQ,GAAI,GAAwB,CAAC,QAAQ,CAAC;gBACpD,IAAM,WAAW,GACb,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;gBAC5F,OAAO,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;aACnD;iBAAM;gBACL,OAAO,EAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;aACvE;SACF;QAED,sDAAY,GAAZ,UAAa,GAAe,EAAE,OAAiC;YAC7D,IAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxF,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;YACzC,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACrC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;aAClC;YAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YACxB,IAAI,KAAK,CAAC,IAAI,uBAAqC;gBACjD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aACrC;iBAAM;gBACL,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,CAAC,UAAU,CAAC,KAAiB,EAAE,OAAO,CAAC,CAAC;gBAC5C,QAAQ,CAAC,qBAAqB,EAAE,CAAC;aAClC;YAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACrC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,oDAAU,GAAV,UAAW,GAAa,EAAE,OAAiC;YACzD,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;YACzC,IAAM,OAAO,GAAG,OAAO,CAAC,qBAAuB,CAAC;;;YAIhD,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE;gBAC3D,QAAQ,CAAC,YAAY,EAAE,CAAC;aACzB;YAED,IAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC;YACzD,IAAI,GAAG,CAAC,WAAW,EAAE;gBACnB,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;aACjC;iBAAM;gBACL,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;aACzE;YAED,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,wDAAc,GAAd,UAAe,GAAiB,EAAE,OAAiC;YACjE,IAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAuB,CAAC;YAC9D,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,eAAiB,EAAE,QAAQ,CAAC;YACvD,IAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;YAChD,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAChD,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC;YACnD,aAAa,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,CAAC;YAEpD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,IAAI;gBACrB,IAAM,MAAM,GAAW,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;gBACxC,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;gBAC7C,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBACnF,aAAa,CAAC,qBAAqB,EAAE,CAAC;aACvC,CAAC,CAAC;;;YAIH,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;;;YAIpE,OAAO,CAAC,wBAAwB,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;YACvD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,oDAAU,GAAV,UAAW,GAAa,EAAE,OAAiC;YAA3D,iBAqDC;;;YAlDC,IAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;YACtD,IAAM,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI,EAAE,CAA0B,CAAC;YAC7D,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAEpE,IAAI,KAAK,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI;iBACxB,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;gBAC7F,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;gBAChD,OAAO,CAAC,YAAY,GAAG,0BAA0B,CAAC;aACnD;YAED,IAAI,YAAY,GAAG,SAAS,CAAC;YAC7B,IAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAC5B,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,EAC9D,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAErD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;YACxC,IAAI,mBAAmB,GAAyB,IAAI,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,CAAC;gBAEtB,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAC9B,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACpE,IAAI,KAAK,EAAE;oBACT,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;iBACnC;gBAED,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;oBAC/B,mBAAmB,GAAG,YAAY,CAAC,eAAe,CAAC;iBACpD;gBAED,YAAY,CAAC,KAAI,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;;;;gBAKhD,YAAY,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;gBAErD,IAAM,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;gBACzD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;aAChD,CAAC,CAAC;YAEH,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC9B,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC9B,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAE/C,IAAI,mBAAmB,EAAE;gBACvB,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,CAAC;gBAC1E,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;aACjD;YAED,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;SAC5B;QAED,sDAAY,GAAZ,UAAa,GAAe,EAAE,OAAiC;YAC7D,IAAM,aAAa,GAAG,OAAO,CAAC,aAAe,CAAC;YAC9C,IAAM,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;YACnC,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;YAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAM,OAAO,GAAG,QAAQ,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAI,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;YAEjD,IAAI,kBAAkB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3E,QAAQ,kBAAkB;gBACxB,KAAK,SAAS;oBACZ,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC;oBACxB,MAAM;gBACR,KAAK,MAAM;oBACT,KAAK,GAAG,aAAa,CAAC,kBAAkB,CAAC;oBACzC,MAAM;aACT;YAED,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;YACzC,IAAI,KAAK,EAAE;gBACT,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC/B;YAED,IAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC;YAC1C,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;;;;;YAM3B,aAAa,CAAC,kBAAkB;gBAC5B,CAAC,EAAE,CAAC,WAAW,GAAG,YAAY,KAAK,EAAE,CAAC,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAChG;QACH,sCAAC;IAAD,CAAC,IAAA;IAMD,IAAM,0BAA0B,GAA+B,EAAE,CAAC;IAClE;QAWE,kCACY,OAAwB,EAAS,OAAY,EAC9C,eAAsC,EAAU,eAAuB,EACtE,eAAuB,EAAS,MAAa,EAAS,SAA4B,EAC1F,eAAiC;YAHzB,YAAO,GAAP,OAAO,CAAiB;YAAS,YAAO,GAAP,OAAO,CAAK;YAC9C,oBAAe,GAAf,eAAe,CAAuB;YAAU,oBAAe,GAAf,eAAe,CAAQ;YACtE,oBAAe,GAAf,eAAe,CAAQ;YAAS,WAAM,GAAN,MAAM,CAAO;YAAS,cAAS,GAAT,SAAS,CAAmB;YAbvF,kBAAa,GAAkC,IAAI,CAAC;YAEpD,0BAAqB,GAAwB,IAAI,CAAC;YAClD,iBAAY,GAA+B,0BAA0B,CAAC;YACtE,oBAAe,GAAG,CAAC,CAAC;YACpB,YAAO,GAAqB,EAAE,CAAC;YAC/B,sBAAiB,GAAW,CAAC,CAAC;YAC9B,sBAAiB,GAAW,CAAC,CAAC;YAC9B,uBAAkB,GAAW,CAAC,CAAC;YAOpC,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YACxF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAED,sBAAI,4CAAM;iBAAV,cAAe,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;;WAAA;QAE5C,gDAAa,GAAb,UAAc,OAA8B,EAAE,YAAsB;YAApE,iBA4BC;YA3BC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAErB,IAAM,UAAU,GAAG,OAAc,CAAC;YAClC,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;;YAGnC,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAC9B,eAAuB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;aAC7E;YAED,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;gBAC5B,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aAC9D;YAED,IAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;YACpC,IAAI,SAAS,EAAE;gBACb,IAAI,gBAAc,GAA0B,eAAe,CAAC,MAAQ,CAAC;gBACrE,IAAI,CAAC,gBAAc,EAAE;oBACnB,gBAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;iBAC3C;gBAED,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBACjC,IAAI,CAAC,YAAY,IAAI,CAAC,gBAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;wBACzD,gBAAc,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,gBAAc,EAAE,KAAI,CAAC,MAAM,CAAC,CAAC;qBACxF;iBACF,CAAC,CAAC;aACJ;SACF;QAEO,+CAAY,GAApB;YACE,IAAM,OAAO,GAAqB,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAM,WAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBACtC,IAAI,WAAS,EAAE;oBACb,IAAM,QAAM,GAA0B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;oBAC7D,MAAM,CAAC,IAAI,CAAC,WAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAM,QAAM,CAAC,IAAI,CAAC,GAAG,WAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC7E;aACF;YACD,OAAO,OAAO,CAAC;SAChB;QAED,mDAAgB,GAAhB,UAAiB,OAAqC,EAAE,OAAa,EAAE,OAAgB;YAAtE,wBAAA,EAAA,cAAqC;YAEpD,IAAM,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YACvC,IAAM,OAAO,GAAG,IAAI,wBAAwB,CACxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EACtF,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;YAClF,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YACzC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAE3D,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAE/B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACnD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACnD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC;SAChB;QAED,2DAAwB,GAAxB,UAAyB,OAAgB;YACvC,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;YAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;QAED,8DAA2B,GAA3B,UACI,WAAyC,EAAE,QAAqB,EAChE,KAAkB;YACpB,IAAM,cAAc,GAAmB;gBACrC,QAAQ,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ;gBAC5D,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK;gBACzF,MAAM,EAAE,EAAE;aACX,CAAC;YACF,IAAM,OAAO,GAAG,IAAI,kBAAkB,CAClC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,aAAa,EACnF,WAAW,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,CAAC,uBAAuB,CAAC,CAAC;YACrF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,OAAO,cAAc,CAAC;SACvB;QAED,gDAAa,GAAb,UAAc,IAAY;YACxB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;SACxE;QAED,gDAAa,GAAb,UAAc,KAAa;;YAEzB,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC3C;SACF;QAED,8CAAW,GAAX,UACI,QAAgB,EAAE,gBAAwB,EAAE,KAAa,EAAE,WAAoB,EAC/E,QAAiB,EAAE,MAAa;YAClC,IAAI,OAAO,GAAU,EAAE,CAAC;YACxB,IAAI,WAAW,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC5B;YACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC3E,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC3E,IAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;gBACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACjE,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;wBACxD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;iBACjD;gBACD,OAAO,CAAC,IAAI,OAAZ,OAAO,WAAS,QAAQ,GAAE;aAC3B;YAED,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;gBACpC,MAAM,CAAC,IAAI,CACP,cAAY,gBAAgB,mDAA8C,gBAAgB,yDAAsD,CAAC,CAAC;aACvJ;YACD,OAAO,OAAO,CAAC;SAChB;QACH,+BAAC;IAAD,CAAC,IAAA;IAGD;QAcE,yBACY,OAAwB,EAAS,OAAY,EAAS,SAAiB,EACvE,4BAAmD;YADnD,YAAO,GAAP,OAAO,CAAiB;YAAS,YAAO,GAAP,OAAO,CAAK;YAAS,cAAS,GAAT,SAAS,CAAQ;YACvE,iCAA4B,GAA5B,4BAA4B,CAAuB;YAfxD,aAAQ,GAAW,CAAC,CAAC;YAGpB,sBAAiB,GAAe,EAAE,CAAC;YACnC,qBAAgB,GAAe,EAAE,CAAC;YAClC,eAAU,GAAG,IAAI,GAAG,EAAsB,CAAC;YAC3C,kBAAa,GAAkC,EAAE,CAAC;YAGlD,mBAAc,GAAe,EAAE,CAAC;YAChC,cAAS,GAAe,EAAE,CAAC;YAC3B,8BAAyB,GAAoB,IAAI,CAAC;YAKxD,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACtC,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAmB,CAAC;aAChE;YAED,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;YAC9E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC;gBACvD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC3E;YACD,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QAED,2CAAiB,GAAjB;YACE,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;gBAC1B,KAAK,CAAC;oBACJ,OAAO,KAAK,CAAC;gBACf,KAAK,CAAC;oBACJ,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;gBACrD;oBACE,OAAO,IAAI,CAAC;aACf;SACF;QAED,mDAAyB,GAAzB,cAAwC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE;QAEpF,sBAAI,wCAAW;iBAAf,cAAoB,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;;;WAAA;QAE5D,uCAAa,GAAb,UAAc,KAAa;;;;;YAKzB,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;YAE7F,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,EAAE;gBACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;gBAC3C,IAAI,eAAe,EAAE;oBACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBAC9B;aACF;iBAAM;gBACL,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC;aACzB;SACF;QAED,8BAAI,GAAJ,UAAK,OAAY,EAAE,WAAoB;YACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO,IAAI,eAAe,CACtB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAChG;QAEO,uCAAa,GAArB;YACE,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;aAChD;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAG,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC3D;SACF;QAED,sCAAY,GAAZ;YACE,IAAI,CAAC,QAAQ,IAAI,yBAAyB,CAAC;YAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QAED,qCAAW,GAAX,UAAY,IAAY;YACtB,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QAEO,sCAAY,GAApB,UAAqB,IAAY,EAAE,KAAoB;YACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACxC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,OAAA,EAAC,CAAC;SAC5D;QAED,iDAAuB,GAAvB,cAA4B,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;QAE9F,wCAAc,GAAd,UAAe,MAAmB;YAAlC,iBAgBC;YAfC,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;aAC3C;;;;;;;YAQD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAClD,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAIA,qBAAU,CAAC;gBACtE,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAGA,qBAAU,CAAC;aAC1C,CAAC,CAAC;YACH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC;SACxD;QAED,mCAAS,GAAT,UACI,KAA4B,EAAE,MAAmB,EAAE,MAAa,EAChE,OAA0B;YAF9B,iBAmBC;YAhBC,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;aAC3C;YAED,IAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;YACjD,IAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC9B,IAAM,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC5D,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBAChC,IAAI,CAAC,KAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACnD,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC;wBAClE,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;wBAChCA,qBAAU,CAAC;iBAChB;gBACD,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aAC9B,CAAC,CAAC;SACJ;QAED,+CAAqB,GAArB;YAAA,iBAiBC;YAhBC,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;YACnC,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;gBAAE,OAAO;YAE9B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YAEzB,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBAChB,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBACzB,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACjD,IAAI,CAAC,KAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC/C,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;iBAC/D;aACF,CAAC,CAAC;SACJ;QAED,+CAAqB,GAArB;YAAA,iBAMC;YALC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACjD,IAAM,GAAG,GAAG,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC5C,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBAChC,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aAC9B,CAAC,CAAC;SACJ;QAED,0CAAgB,GAAhB,cAAqB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;QAEjE,sBAAI,uCAAU;iBAAd;gBACE,IAAM,UAAU,GAAa,EAAE,CAAC;gBAChC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACtC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACvB;gBACD,OAAO,UAAU,CAAC;aACnB;;;WAAA;QAED,sDAA4B,GAA5B,UAA6B,QAAyB;YAAtD,iBAQC;YAPC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC9C,IAAM,QAAQ,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC9C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;oBAC9C,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACzC;aACF,CAAC,CAAC;SACJ;QAED,wCAAc,GAAd;YAAA,iBAsCC;YArCC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;YACxC,IAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;YACzC,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;YAElE,IAAI,cAAc,GAAiB,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,IAAI;gBACrC,IAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBACrC,IAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;oBAClC,IAAI,KAAK,IAAID,qBAAS,EAAE;wBACtB,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;qBACzB;yBAAM,IAAI,KAAK,IAAIC,qBAAU,EAAE;wBAC9B,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;qBAC1B;iBACF,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,EAAE;oBACZ,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAI,CAAC,QAAQ,CAAC;iBAChD;gBACD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACpC,CAAC,CAAC;YAEH,IAAM,QAAQ,GAAa,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;YAC7F,IAAM,SAAS,GAAa,cAAc,CAAC,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;;YAGhG,IAAI,OAAO,EAAE;gBACX,IAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAClB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAClB,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC7B;YAED,OAAO,yBAAyB,CAC5B,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAChF,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACzB;QACH,sBAAC;IAAD,CAAC,IAAA;IAED;QAAiCI,sCAAe;QAG9C,4BACI,MAAuB,EAAS,OAAY,EAAS,SAAuB,EACrE,aAAuB,EAAS,cAAwB,EAAE,OAAuB,EAChF,wBAAyC;YAAzC,yCAAA,EAAA,gCAAyC;YAHrD,YAIE,kBAAM,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,SAEtC;YALmC,aAAO,GAAP,OAAO,CAAK;YAAS,eAAS,GAAT,SAAS,CAAc;YACrE,mBAAa,GAAb,aAAa,CAAU;YAAS,oBAAc,GAAd,cAAc,CAAU;YACvD,8BAAwB,GAAxB,wBAAwB,CAAiB;YAEnD,KAAI,CAAC,OAAO,GAAG,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC,CAAC;;SAC3F;QAED,8CAAiB,GAAjB,cAA+B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;QAElE,2CAAc,GAAd;YACE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAC3B,IAAA,iBAAwC,EAAvC,gBAAK,EAAE,sBAAQ,EAAE,kBAAsB,CAAC;YAC7C,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,EAAE;gBAC1C,IAAM,YAAY,GAAiB,EAAE,CAAC;gBACtC,IAAM,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;gBACnC,IAAM,WAAW,GAAG,KAAK,GAAG,SAAS,CAAC;;gBAGtC,IAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACzD,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC/B,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAEpC,IAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACzD,gBAAgB,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;gBACtD,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;;;;;;;;;;;;;;gBAkBpC,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;oBAC/B,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACzC,IAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAW,CAAC;oBACzC,IAAM,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAC;oBACpD,EAAE,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC;oBACvD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACvB;;gBAGD,QAAQ,GAAG,SAAS,CAAC;gBACrB,KAAK,GAAG,CAAC,CAAC;gBACV,MAAM,GAAG,EAAE,CAAC;gBAEZ,SAAS,GAAG,YAAY,CAAC;aAC1B;YAED,OAAO,yBAAyB,CAC5B,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EACzF,IAAI,CAAC,CAAC;SACX;QACH,yBAAC;IAAD,CAnEA,CAAiC,eAAe,GAmE/C;IAED,SAAS,WAAW,CAAC,MAAc,EAAE,aAAiB;QAAjB,8BAAA,EAAA,iBAAiB;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED,SAAS,aAAa,CAAC,KAA8B,EAAE,SAAqB;QAC1E,IAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,IAAI,aAAuB,CAAC;QAC5B,KAAK,CAAC,OAAO,CAAC,UAAA,KAAK;YACjB,IAAI,KAAK,KAAK,GAAG,EAAE;gBACjB,aAAa,GAAG,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACxD,aAAa,CAAC,OAAO,CAAC,UAAA,IAAI,IAAM,MAAM,CAAC,IAAI,CAAC,GAAGJ,qBAAU,CAAC,EAAE,CAAC,CAAC;aAC/D;iBAAM;gBACL,UAAU,CAAC,KAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAChD;SACF,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;;;QC72BC,mBAAoB,OAAwB,EAAE,KAA4C;YAAtE,YAAO,GAAP,OAAO,CAAiB;YAC1C,IAAM,MAAM,GAAU,EAAE,CAAC;YACzB,IAAM,GAAG,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACtD,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,IAAM,YAAY,GAAG,mCAAiC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC;gBAC1E,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;SAC1B;QAED,kCAAc,GAAd,UACI,OAAY,EAAE,cAAuC,EACrD,iBAA0C,EAAE,OAAyB,EACrE,eAAuC;YACzC,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC;gBACnB,cAAc,CAAC;YACzE,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC,iBAAiB,CAAC;gBACtB,iBAAiB,CAAC;YAC9E,IAAM,MAAM,GAAQ,EAAE,CAAC;YACvB,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;YACjE,IAAM,MAAM,GAAG,uBAAuB,CAClC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EACxF,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;YACtC,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,IAAM,YAAY,GAAG,iCAA+B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC;gBACxE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;aAC/B;YACD,OAAO,MAAM,CAAC;SACf;QACH,gBAAC;IAAD,CAAC;;ICjDD;;;;;;;IAQA;;;AAGA;QAAA;SAKC;QAAD,+BAAC;IAAD,CAAC,IAAA;IAED;;;AAGA;QAAA;SAQC;QAPC,4DAAqB,GAArB,UAAsB,YAAoB,EAAE,MAAgB,IAAY,OAAO,YAAY,CAAC,EAAE;QAE9F,0DAAmB,GAAnB,UACI,oBAA4B,EAAE,kBAA0B,EAAE,KAAoB,EAC9E,MAAgB;YAClB,OAAY,KAAK,CAAC;SACnB;QACH,mCAAC;IAAD,CAAC;;;QClBiDI,gDAAwB;QAA1E;;SAuBC;QAtBC,4DAAqB,GAArB,UAAsB,YAAoB,EAAE,MAAgB;YAC1D,OAAO,mBAAmB,CAAC,YAAY,CAAC,CAAC;SAC1C;QAED,0DAAmB,GAAnB,UACI,oBAA4B,EAAE,kBAA0B,EAAE,KAAoB,EAC9E,MAAgB;YAClB,IAAI,IAAI,GAAW,EAAE,CAAC;YACtB,IAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;YAEvC,IAAI,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;gBAC5E,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,IAAI,GAAG,IAAI,CAAC;iBACb;qBAAM;oBACL,IAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;oBAChE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;wBACzD,MAAM,CAAC,IAAI,CAAC,yCAAuC,oBAAoB,SAAI,KAAO,CAAC,CAAC;qBACrF;iBACF;aACF;YACD,OAAO,MAAM,GAAG,IAAI,CAAC;SACtB;QACH,mCAAC;IAAD,CAvBA,CAAkD,wBAAwB,GAuBzE;IAED,IAAM,oBAAoB,GAAG,cAAc,CACvC,gUAAgU;SAC3T,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAErB,SAAS,cAAc,CAAC,IAAc;QACpC,IAAM,GAAG,GAA6B,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAA,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;IACb,CAAC;;aCjBe,2BAA2B,CACvC,OAAY,EAAE,WAAmB,EAAE,SAAiB,EAAE,OAAe,EACrE,mBAA4B,EAAE,UAAsB,EAAE,QAAoB,EAC1E,SAAyC,EAAE,eAAsB,EACjE,aAAkD,EAClD,cAAmD,EAAE,SAAiB,EACtE,MAAc;QAChB,OAAO;YACL,IAAI;YACJ,OAAO,SAAA;YACP,WAAW,aAAA;YACX,mBAAmB,qBAAA;YACnB,SAAS,WAAA;YACT,UAAU,YAAA;YACV,OAAO,SAAA;YACP,QAAQ,UAAA;YACR,SAAS,WAAA;YACT,eAAe,iBAAA;YACf,aAAa,eAAA;YACb,cAAc,gBAAA;YACd,SAAS,WAAA;YACT,MAAM,QAAA;SACP,CAAC;IACJ,CAAC;;IC/BD,IAAM,YAAY,GAAG,EAAE,CAAC;IAExB;QACE,oCACY,YAAoB,EAAS,GAAkB,EAC/C,YAAyD;YADzD,iBAAY,GAAZ,YAAY,CAAQ;YAAS,QAAG,GAAH,GAAG,CAAe;YAC/C,iBAAY,GAAZ,YAAY,CAA6C;SAAI;QAEzE,0CAAK,GAAL,UAAM,YAAiB,EAAE,SAAc,EAAE,OAAY,EAAE,MAA4B;YACjF,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC/F;QAED,gDAAW,GAAX,UAAY,SAAiB,EAAE,MAA4B,EAAE,MAAa;YACxE,IAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACjD,IAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,IAAM,YAAY,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC;YAC5F,OAAO,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC;SAC7E;QAED,0CAAK,GAAL,UACI,MAAuB,EAAE,OAAY,EAAE,YAAiB,EAAE,SAAc,EACxE,cAAsB,EAAE,cAAsB,EAAE,cAAiC,EACjF,WAA8B,EAAE,eAAuC,EACvE,YAAsB;YACxB,IAAM,MAAM,GAAU,EAAE,CAAC;YAEzB,IAAM,yBAAyB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,YAAY,CAAC;YAC9F,IAAM,sBAAsB,GAAG,cAAc,IAAI,cAAc,CAAC,MAAM,IAAI,YAAY,CAAC;YACvF,IAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;YAC1F,IAAM,mBAAmB,GAAG,WAAW,IAAI,WAAW,CAAC,MAAM,IAAI,YAAY,CAAC;YAC9E,IAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAEjF,IAAM,eAAe,GAAG,IAAI,GAAG,EAAO,CAAC;YACvC,IAAM,WAAW,GAAG,IAAI,GAAG,EAAkC,CAAC;YAC9D,IAAM,YAAY,GAAG,IAAI,GAAG,EAAkC,CAAC;YAC/D,IAAM,SAAS,GAAG,SAAS,KAAK,MAAM,CAAC;YAEvC,IAAM,gBAAgB,GAAG,EAAC,MAAM,eAAM,yBAAyB,EAAK,mBAAmB,CAAC,EAAC,CAAC;YAE1F,IAAM,SAAS,GAAG,YAAY,GAAG,EAAE,GAAG,uBAAuB,CACnB,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,EACnD,cAAc,EAAE,kBAAkB,EAAE,eAAe,EACnD,gBAAgB,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;YAErF,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAEtF,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,OAAO,2BAA2B,CAC9B,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAClF,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;aAC5E;YAED,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE;gBAClB,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACvD,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAA,CAAC,CAAC;gBAExD,IAAM,SAAS,GAAG,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzD,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,GAAA,CAAC,CAAC;gBAE1D,IAAI,GAAG,KAAK,OAAO,EAAE;oBACnB,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAC1B;aACF,CAAC,CAAC;YAEH,IAAM,mBAAmB,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;YACtE,OAAO,2BAA2B,CAC9B,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAClF,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;SAC5F;QACH,iCAAC;IAAD,CAAC,IAAA;IAED,SAAS,yBAAyB,CAC9B,QAA+B,EAAE,YAAiB,EAAE,SAAc,EAAE,OAAY,EAChF,MAA4B;QAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,GAAA,CAAC,CAAC;IAC3E,CAAC;IAED;QACE,8BAAoB,MAAgB,EAAU,aAAmC;YAA7D,WAAM,GAAN,MAAM,CAAU;YAAU,kBAAa,GAAb,aAAa,CAAsB;SAAI;QAErF,0CAAW,GAAX,UAAY,MAA4B,EAAE,MAAgB;YACxD,IAAM,WAAW,GAAe,EAAE,CAAC;YACnC,IAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC7B,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;iBAC7B;aACF,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,IAAM,UAAQ,GAAG,KAAY,CAAC;oBAC9B,MAAM,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;wBAChC,IAAI,GAAG,GAAG,UAAQ,CAAC,IAAI,CAAC,CAAC;wBACzB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;4BAClB,GAAG,GAAG,iBAAiB,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;yBACtD;wBACD,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;qBACzB,CAAC,CAAC;iBACJ;aACF,CAAC,CAAC;YACH,OAAO,WAAW,CAAC;SACpB;QACH,2BAAC;IAAD,CAAC,IAAA;;IC3GD;;;AAGA,aAAgB,YAAY,CAAC,IAAY,EAAE,GAAe;QACxD,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;IAGA;QAKE,0BAAmB,IAAY,EAAS,GAAe;YAAvD,iBAcC;YAdkB,SAAI,GAAJ,IAAI,CAAQ;YAAS,QAAG,GAAH,GAAG,CAAY;YAJhD,wBAAmB,GAAiC,EAAE,CAAC;YAEvD,WAAM,GAAgD,EAAE,CAAC;YAG9D,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,GAAG;gBACpB,IAAM,aAAa,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;gBAChE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;aAC5E,CAAC,CAAC;YAEH,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAC5C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;YAE7C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,GAAG;gBACzB,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC;aACvF,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACvE;QAED,sBAAI,6CAAe;iBAAnB,cAAwB,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE;;;WAAA;QAEzD,0CAAe,GAAf,UAAgB,YAAiB,EAAE,SAAc,EAAE,OAAY,EAAE,MAA4B;YAE3F,IAAM,KAAK,GACP,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,GAAA,CAAC,CAAC;YAC1F,OAAO,KAAK,IAAI,IAAI,CAAC;SACtB;QAED,sCAAW,GAAX,UAAY,YAAiB,EAAE,MAA4B,EAAE,MAAa;YACxE,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;SAC1E;QACH,uBAAC;IAAD,CAAC,IAAA;IAED,SAAS,wBAAwB,CAC7B,WAAmB,EACnB,MAAmD;QACrD,IAAM,QAAQ,GAAG,CAAC,UAAC,SAAc,EAAE,OAAY,IAAK,OAAA,IAAI,GAAA,CAAC,CAAC;QAC1D,IAAM,SAAS,GAAgB,EAAC,IAAI,oBAAkC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;QAChG,IAAM,UAAU,GAAkB;YAChC,IAAI;YACJ,SAAS,WAAA;YACT,QAAQ,UAAA;YACR,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,CAAC;SACZ,CAAC;QACF,OAAO,IAAI,0BAA0B,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACzE,CAAC;IAED,SAAS,iBAAiB,CAAC,GAAyB,EAAE,IAAY,EAAE,IAAY;QAC9E,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC5B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC7B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;aACvB;SACF;aAAM,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACnC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;SACvB;IACH,CAAC;;ICrFD;;;;;;;AAOA,IAaA,IAAM,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;IAE1D;QAKE,iCACW,QAAa,EAAU,OAAwB,EAC9C,WAAqC;YADtC,aAAQ,GAAR,QAAQ,CAAK;YAAU,YAAO,GAAP,OAAO,CAAiB;YAC9C,gBAAW,GAAX,WAAW,CAA0B;YANzC,gBAAW,GAA+C,EAAE,CAAC;YAC7D,iBAAY,GAAoC,EAAE,CAAC;YACpD,YAAO,GAAsB,EAAE,CAAC;SAIc;QAErD,0CAAQ,GAAR,UAAS,EAAU,EAAE,QAA+C;YAClE,IAAM,MAAM,GAAU,EAAE,CAAC;YACzB,IAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC9D,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,MAAM,IAAI,KAAK,CACX,gEAA8D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;aACxF;iBAAM;gBACL,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;aAC5B;SACF;QAEO,8CAAY,GAApB,UACI,CAA+B,EAAE,SAAqB,EACtD,UAAuB;YACzB,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;YAC1B,IAAM,SAAS,GAAG,kBAAkB,CAChC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;SAC1F;QAED,wCAAM,GAAN,UAAO,EAAU,EAAE,OAAY,EAAE,OAA8B;YAA/D,iBAwCC;YAxCgC,wBAAA,EAAA,YAA8B;YAC7D,IAAM,MAAM,GAAU,EAAE,CAAC;YACzB,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACjC,IAAI,YAA4C,CAAC;YAEjD,IAAM,aAAa,GAAG,IAAI,GAAG,EAAmB,CAAC;YAEjD,IAAI,GAAG,EAAE;gBACP,YAAY,GAAG,uBAAuB,CAClC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAC7E,qBAAqB,EAAE,MAAM,CAAC,CAAC;gBACnC,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI;oBACvB,IAAM,MAAM,GAAG,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBAChE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,GAAA,CAAC,CAAC;iBAC1D,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBACpF,YAAY,GAAG,EAAE,CAAC;aACnB;YAED,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,MAAM,IAAI,KAAK,CACX,iEAA+D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;aACzF;YAED,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,OAAO;gBACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CACvB,UAAA,IAAI,IAAM,MAAM,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAEJ,qBAAU,CAAC,CAAC,EAAE,CAAC,CAAC;aACvF,CAAC,CAAC;YAEH,IAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,UAAA,CAAC;gBAChC,IAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC5C,OAAO,KAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;aACzC,CAAC,CAAC;YACH,IAAM,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAA,CAAC,CAAC;YAEzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,OAAO,MAAM,CAAC;SACf;QAED,yCAAO,GAAP,UAAQ,EAAU;YAChB,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC/B;SACF;QAEO,4CAAU,GAAlB,UAAmB,EAAU;YAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,sDAAoD,EAAI,CAAC,CAAC;aAC3E;YACD,OAAO,MAAM,CAAC;SACf;QAED,wCAAM,GAAN,UAAO,EAAU,EAAE,OAAe,EAAE,SAAiB,EAAE,QAA6B;;YAGlF,IAAM,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1D,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACpE,OAAO,eAAQ,CAAC;SACjB;QAED,yCAAO,GAAP,UAAQ,EAAU,EAAE,OAAY,EAAE,OAAe,EAAE,IAAW;YAC5D,IAAI,OAAO,IAAI,UAAU,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAA4C,CAAC,CAAC;gBACtE,OAAO;aACR;YAED,IAAI,OAAO,IAAI,QAAQ,EAAE;gBACvB,IAAM,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAqB,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAClC,OAAO;aACR;YAED,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACnC,QAAQ,OAAO;gBACb,KAAK,MAAM;oBACT,MAAM,CAAC,IAAI,EAAE,CAAC;oBACd,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChB,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,CAAC,IAAI,EAAE,CAAC;oBACd,MAAM;gBACR,KAAK,aAAa;oBAChB,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAW,CAAC,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBACjB,MAAM;aACT;SACF;QACH,8BAAC;IAAD,CAAC,IAAA;;IC3ID,IAAM,gBAAgB,GAAG,mBAAmB,CAAC;IAC7C,IAAM,eAAe,GAAG,oBAAoB,CAAC;IAC7C,IAAM,kBAAkB,GAAG,qBAAqB,CAAC;IACjD,IAAM,iBAAiB,GAAG,sBAAsB,CAAC;IACjD,IAAM,cAAc,GAAG,kBAAkB,CAAC;IAC1C,IAAM,aAAa,GAAG,mBAAmB,CAAC;IAE1C,IAAM,kBAAkB,GAAgC,EAAE,CAAC;IAC3D,IAAM,kBAAkB,GAA0B;QAChD,WAAW,EAAE,EAAE;QACf,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,KAAK;QACnB,oBAAoB,EAAE,KAAK;KAC5B,CAAC;IACF,IAAM,0BAA0B,GAA0B;QACxD,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,KAAK;QACjB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,KAAK;QACnB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;AAkBF,IAAO,IAAM,YAAY,GAAG,cAAc,CAAC;IAU3C;QAME,oBAAY,KAAU,EAAS,WAAwB;YAAxB,4BAAA,EAAA,gBAAwB;YAAxB,gBAAW,GAAX,WAAW,CAAa;YACrD,IAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACrD,IAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;YAC7C,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAI,KAAK,EAAE;gBACT,IAAM,OAAO,GAAG,OAAO,CAAC,KAAY,CAAC,CAAC;gBACtC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,IAAI,CAAC,OAAO,GAAG,OAA2B,CAAC;aAC5C;iBAAM;gBACL,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;aAC1B;SACF;QAhBD,sBAAI,8BAAM;iBAAV,cAAqC,OAAO,IAAI,CAAC,OAAO,CAAC,MAA6B,CAAC,EAAE;;;WAAA;QAkBzF,kCAAa,GAAb,UAAc,OAAyB;YACrC,IAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,IAAI,SAAS,EAAE;gBACb,IAAM,WAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAQ,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBACjC,IAAI,WAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;wBAC3B,WAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;qBACnC;iBACF,CAAC,CAAC;aACJ;SACF;QACH,iBAAC;IAAD,CAAC,IAAA;IAEM,IAAM,UAAU,GAAG,MAAM,CAAC;AACjC,IAAO,IAAM,mBAAmB,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IAE9D;QAUE,sCACW,EAAU,EAAS,WAAgB,EAAU,OAAkC;YAA/E,OAAE,GAAF,EAAE,CAAQ;YAAS,gBAAW,GAAX,WAAW,CAAK;YAAU,YAAO,GAAP,OAAO,CAA2B;YAVnF,YAAO,GAAgC,EAAE,CAAC;YAEzC,cAAS,GAA8C,EAAE,CAAC;YAC1D,WAAM,GAAuB,EAAE,CAAC;YAEhC,sBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;YAM5D,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SAC5C;QAED,6CAAM,GAAN,UAAO,OAAY,EAAE,IAAY,EAAE,KAAa,EAAE,QAAiC;YAAnF,iBA0CC;YAzCC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,uDACZ,KAAK,2CAAoC,IAAI,sBAAmB,CAAC,CAAC;aACvE;YAED,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gBACtC,MAAM,IAAI,KAAK,CAAC,iDACZ,IAAI,gDAA4C,CAAC,CAAC;aACvD;YAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,4CAAyC,KAAK,uCAC1D,IAAI,yBAAqB,CAAC,CAAC;aAChC;YAED,IAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACvE,IAAM,IAAI,GAAG,EAAC,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAC,CAAC;YACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErB,IAAM,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACtF,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC5C,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;gBACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;gBACrD,kBAAkB,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC;aAChD;YAED,OAAO;;;;gBAIL,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC;oBACtB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACtC,IAAI,KAAK,IAAI,CAAC,EAAE;wBACd,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;qBAC5B;oBAED,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;wBACzB,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBACjC;iBACF,CAAC,CAAC;aACJ,CAAC;SACH;QAED,+CAAQ,GAAR,UAAS,IAAY,EAAE,GAAqB;YAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;;gBAExB,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBAC3B,OAAO,IAAI,CAAC;aACb;SACF;QAEO,kDAAW,GAAnB,UAAoB,IAAY;YAC9B,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,sCAAmC,IAAI,gCAA4B,CAAC,CAAC;aACtF;YACD,OAAO,OAAO,CAAC;SAChB;QAED,8CAAO,GAAP,UAAQ,OAAY,EAAE,WAAmB,EAAE,KAAU,EAAE,iBAAiC;YAAxF,iBAsGC;YAtGsD,kCAAA,EAAA,wBAAiC;YAEtF,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAC9C,IAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAE5E,IAAI,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACnE,IAAI,CAAC,kBAAkB,EAAE;gBACvB,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;gBACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC;aACpE;YAED,IAAI,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAChD,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAE/C,IAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;gBACvB,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAC1C;YAED,kBAAkB,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC;YAE1C,IAAI,CAAC,SAAS,EAAE;gBACd,SAAS,GAAG,mBAAmB,CAAC;aACjC;YAED,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC;;;;;;;YAQ/C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;;;gBAGnD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;oBAChD,IAAM,MAAM,GAAU,EAAE,CAAC;oBACzB,IAAM,YAAU,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClF,IAAM,UAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAC5E,IAAI,MAAM,CAAC,MAAM,EAAE;wBACjB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;qBAClC;yBAAM;wBACL,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;4BACtB,WAAW,CAAC,OAAO,EAAE,YAAU,CAAC,CAAC;4BACjC,SAAS,CAAC,OAAO,EAAE,UAAQ,CAAC,CAAC;yBAC9B,CAAC,CAAC;qBACJ;iBACF;gBACD,OAAO;aACR;YAED,IAAM,gBAAgB,GAClB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YAChE,gBAAgB,CAAC,OAAO,CAAC,UAAA,MAAM;;;;;gBAK7B,IAAI,MAAM,CAAC,WAAW,IAAI,KAAI,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;oBACvF,MAAM,CAAC,OAAO,EAAE,CAAC;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,UAAU,GACV,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACrF,IAAI,oBAAoB,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,iBAAiB;oBAAE,OAAO;gBAC/B,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;gBACxC,oBAAoB,GAAG,IAAI,CAAC;aAC7B;YAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,EAAC,OAAO,SAAA,EAAE,WAAW,aAAA,EAAE,UAAU,YAAA,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAE,MAAM,QAAA,EAAE,oBAAoB,sBAAA,EAAC,CAAC,CAAC;YAE1F,IAAI,CAAC,oBAAoB,EAAE;gBACzB,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;gBACpC,MAAM,CAAC,OAAO,CAAC,cAAQ,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;aACnE;YAED,MAAM,CAAC,MAAM,CAAC;gBACZ,IAAI,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACzC,IAAI,KAAK,IAAI,CAAC,EAAE;oBACd,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC/B;gBAED,IAAM,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC3D,IAAI,OAAO,EAAE;oBACX,IAAI,OAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,OAAK,IAAI,CAAC,EAAE;wBACd,OAAO,CAAC,MAAM,CAAC,OAAK,EAAE,CAAC,CAAC,CAAC;qBAC1B;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE9B,OAAO,MAAM,CAAC;SACf;QAED,iDAAU,GAAV,UAAW,IAAY;YAAvB,iBASC;YARC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,OAAO,IAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAExF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,OAAO;gBAChD,KAAI,CAAC,iBAAiB,CAAC,GAAG,CACtB,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,UAAA,KAAK,IAAM,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;aACzE,CAAC,CAAC;SACJ;QAED,wDAAiB,GAAjB,UAAkB,OAAY;YAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,IAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAClE,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC;gBACnD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC/C;SACF;QAEO,qEAA8B,GAAtC,UAAuC,WAAgB,EAAE,OAAY,EAAE,OAAwB;YAA/F,iBAgBC;YAhBsE,wBAAA,EAAA,eAAwB;;;;YAI7F,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;;;gBAG3E,IAAI,GAAG,CAAC,YAAY,CAAC;oBAAE,OAAO;gBAE9B,IAAM,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;gBAC9D,IAAI,UAAU,CAAC,IAAI,EAAE;oBACnB,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAA,CAAC,CAAC;iBAC/E;qBAAM;oBACL,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;iBAC7B;aACF,CAAC,CAAC;SACJ;QAED,4DAAqB,GAArB,UACI,OAAY,EAAE,OAAY,EAAE,oBAA8B,EAC1D,iBAA2B;YAF/B,iBA0BC;YAvBC,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,aAAa,EAAE;gBACjB,IAAM,SAAO,GAAgC,EAAE,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,WAAW;;;oBAG5C,IAAI,KAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;wBAC/B,IAAM,MAAM,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;wBACjF,IAAI,MAAM,EAAE;4BACV,SAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;qBACF;iBACF,CAAC,CAAC;gBAEH,IAAI,SAAO,CAAC,MAAM,EAAE;oBAClB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;oBACnE,IAAI,oBAAoB,EAAE;wBACxB,mBAAmB,CAAC,SAAO,CAAC,CAAC,MAAM,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;qBACnF;oBACD,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;SACd;QAED,qEAA8B,GAA9B,UAA+B,OAAY;YAA3C,iBA4BC;YA3BC,IAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE;gBACb,IAAM,iBAAe,GAAG,IAAI,GAAG,EAAU,CAAC;gBAC1C,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACxB,IAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;oBAClC,IAAI,iBAAe,CAAC,GAAG,CAAC,WAAW,CAAC;wBAAE,OAAO;oBAC7C,iBAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBAEjC,IAAM,OAAO,GAAG,KAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBAC5C,IAAM,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;oBAC9C,IAAM,aAAa,GAAG,KAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;oBAClE,IAAM,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC,IAAI,mBAAmB,CAAC;oBACpE,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;oBAC3C,IAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,KAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;oBAE5E,KAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAClC,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC;wBACf,OAAO,SAAA;wBACP,WAAW,aAAA;wBACX,UAAU,YAAA;wBACV,SAAS,WAAA;wBACT,OAAO,SAAA;wBACP,MAAM,QAAA;wBACN,oBAAoB,EAAE,IAAI;qBAC3B,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;SACF;QAED,iDAAU,GAAV,UAAW,OAAY,EAAE,OAAY;YAArC,iBAoDC;YAnDC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAE5B,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC7B,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;aAC7D;;YAGD,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;gBAAE,OAAO;;;YAI/D,IAAI,iCAAiC,GAAG,KAAK,CAAC;YAC9C,IAAI,MAAM,CAAC,eAAe,EAAE;gBAC1B,IAAM,cAAc,GAChB,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;;;;;gBAM7E,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;oBAC3C,iCAAiC,GAAG,IAAI,CAAC;iBAC1C;qBAAM;oBACL,IAAI,QAAM,GAAG,OAAO,CAAC;oBACrB,OAAO,QAAM,GAAG,QAAM,CAAC,UAAU,EAAE;wBACjC,IAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,QAAM,CAAC,CAAC;wBACpD,IAAI,QAAQ,EAAE;4BACZ,iCAAiC,GAAG,IAAI,CAAC;4BACzC,MAAM;yBACP;qBACF;iBACF;aACF;;;;;YAMD,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;;;YAI7C,IAAI,iCAAiC,EAAE;gBACrC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAC/D;iBAAM;;;gBAGL,MAAM,CAAC,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;gBACzD,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACvC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC7C;SACF;QAED,iDAAU,GAAV,UAAW,OAAY,EAAE,MAAW,IAAU,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE;QAEvF,6DAAsB,GAAtB,UAAuB,WAAmB;YAA1C,iBA0CC;YAzCC,IAAM,YAAY,GAAuB,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC5B,IAAI,MAAM,CAAC,SAAS;oBAAE,OAAO;gBAE7B,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC9B,IAAM,SAAS,GAAG,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACtD,IAAI,SAAS,EAAE;oBACb,SAAS,CAAC,OAAO,CAAC,UAAC,QAAyB;wBAC1C,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;4BACtC,IAAM,SAAS,GAAG,kBAAkB,CAChC,OAAO,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;4BAC3E,SAAiB,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC;4BAC1C,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;yBAC5E;qBACF,CAAC,CAAC;iBACJ;gBAED,IAAI,MAAM,CAAC,gBAAgB,EAAE;oBAC3B,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC;;;wBAGtB,MAAM,CAAC,OAAO,EAAE,CAAC;qBAClB,CAAC,CAAC;iBACJ;qBAAM;oBACL,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YAEjB,OAAO,YAAY,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;;;gBAG5B,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACrC,IAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACrC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;oBACtB,OAAO,EAAE,GAAG,EAAE,CAAC;iBAChB;gBACD,OAAO,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E,CAAC,CAAC;SACJ;QAED,8CAAO,GAAP,UAAQ,OAAY;YAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC;YACvC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SAChE;QAED,0DAAmB,GAAnB,UAAoB,OAAY;YAC9B,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAE,YAAY,GAAG,IAAI,CAAC;YAC7D,YAAY;gBACR,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,OAAO,KAAK,OAAO,GAAA,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,YAAY,CAAC;YAC1F,OAAO,YAAY,CAAC;SACrB;QACH,mCAAC;IAAD,CAAC,IAAA;IAQD;QA0BE,mCACW,QAAa,EAAS,MAAuB,EAC5C,WAAqC;YADtC,aAAQ,GAAR,QAAQ,CAAK;YAAS,WAAM,GAAN,MAAM,CAAiB;YAC5C,gBAAW,GAAX,WAAW,CAA0B;YA3B1C,YAAO,GAAgC,EAAE,CAAC;YAC1C,oBAAe,GAAG,IAAI,GAAG,EAAqC,CAAC;YAC/D,qBAAgB,GAAG,IAAI,GAAG,EAAoC,CAAC;YAC/D,4BAAuB,GAAG,IAAI,GAAG,EAAoC,CAAC;YACtE,oBAAe,GAAG,IAAI,GAAG,EAA4C,CAAC;YACtE,kBAAa,GAAG,IAAI,GAAG,EAAO,CAAC;YAE/B,oBAAe,GAAG,CAAC,CAAC;YACpB,uBAAkB,GAAG,CAAC,CAAC;YAEtB,qBAAgB,GAAiD,EAAE,CAAC;YACpE,mBAAc,GAAmC,EAAE,CAAC;YACpD,cAAS,GAAkB,EAAE,CAAC;YAC9B,kBAAa,GAAkB,EAAE,CAAC;YAEnC,4BAAuB,GAAG,IAAI,GAAG,EAAqC,CAAC;YACvE,2BAAsB,GAAU,EAAE,CAAC;YACnC,2BAAsB,GAAU,EAAE,CAAC;;YAGnC,sBAAiB,GAAG,UAAC,OAAY,EAAE,OAAY,KAAO,CAAC;SAOT;;QAJrD,sDAAkB,GAAlB,UAAmB,OAAY,EAAE,OAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;QAM5F,sBAAI,oDAAa;iBAAjB;gBACE,IAAM,OAAO,GAAgC,EAAE,CAAC;gBAChD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,EAAE;oBAC5B,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;wBACvB,IAAI,MAAM,CAAC,MAAM,EAAE;4BACjB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;qBACF,CAAC,CAAC;iBACJ,CAAC,CAAC;gBACH,OAAO,OAAO,CAAC;aAChB;;;WAAA;QAED,mDAAe,GAAf,UAAgB,WAAmB,EAAE,WAAgB;YACnD,IAAM,EAAE,GAAG,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAC5E,IAAI,WAAW,CAAC,UAAU,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;aAC7C;iBAAM;;;;gBAIL,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;;;;;;gBAO1C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;aACvC;YACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;SAChD;QAEO,yDAAqB,GAA7B,UAA8B,EAAgC,EAAE,WAAgB;YAC9E,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7C,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,KAAK,GAAG,KAAK,CAAC;gBAClB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC/B,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;wBACvE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBACzC,KAAK,GAAG,IAAI,CAAC;wBACb,MAAM;qBACP;iBACF;gBACD,IAAI,CAAC,KAAK,EAAE;oBACV,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;iBACtC;aACF;iBAAM;gBACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC9B;YAED,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC;SACX;QAED,4CAAQ,GAAR,UAAS,WAAmB,EAAE,WAAgB;YAC5C,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,EAAE,EAAE;gBACP,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;aACrD;YACD,OAAO,EAAE,CAAC;SACX;QAED,mDAAe,GAAf,UAAgB,WAAmB,EAAE,IAAY,EAAE,OAAyB;YAC1E,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBACpC,IAAI,CAAC,eAAe,EAAE,CAAC;aACxB;SACF;QAED,2CAAO,GAAP,UAAQ,WAAmB,EAAE,OAAY;YAAzC,iBAeC;YAdC,IAAI,CAAC,WAAW;gBAAE,OAAO;YAEzB,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE7C,IAAI,CAAC,UAAU,CAAC;gBACd,KAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBACpD,OAAO,KAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAC1C,IAAM,KAAK,GAAG,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC9C,IAAI,KAAK,IAAI,CAAC,EAAE;oBACd,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACtC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,wBAAwB,CAAC,cAAM,OAAA,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;SAC1D;QAEO,mDAAe,GAAvB,UAAwB,EAAU,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAEzE,4DAAwB,GAAxB,UAAyB,OAAY;;;;;;YAMnC,IAAM,UAAU,GAAG,IAAI,GAAG,EAAgC,CAAC;YAC3D,IAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACxD,IAAI,aAAa,EAAE;gBACjB,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,IAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;oBAChD,IAAI,IAAI,EAAE;wBACR,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;wBACtC,IAAI,EAAE,EAAE;4BACN,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;yBACpB;qBACF;iBACF;aACF;YACD,OAAO,UAAU,CAAC;SACnB;QAED,2CAAO,GAAP,UAAQ,WAAmB,EAAE,OAAY,EAAE,IAAY,EAAE,KAAU;YACjE,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBAC7C,IAAI,EAAE,EAAE;oBACN,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;oBACjC,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;SACd;QAED,8CAAU,GAAV,UAAW,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB;YAC9E,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;gBAAE,OAAO;;;YAIpC,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;YAC/D,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;gBACpC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC9B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC3D,IAAI,KAAK,IAAI,CAAC,EAAE;oBACd,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9C;aACF;;;;YAKD,IAAI,WAAW,EAAE;gBACf,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;;;;;;;gBAO7C,IAAI,EAAE,EAAE;oBACN,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBAChC;aACF;;YAGD,IAAI,YAAY,EAAE;gBAChB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;aACnC;SACF;QAED,uDAAmB,GAAnB,UAAoB,OAAY,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;QAEhF,yDAAqB,GAArB,UAAsB,OAAY,EAAE,KAAc;YAChD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAChC,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;iBACvC;aACF;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC1C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aAC1C;SACF;QAED,8CAAU,GAAV,UAAW,WAAmB,EAAE,OAAY,EAAE,OAAY;YACxD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC1C,OAAO;aACR;YAED,IAAM,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;YAClE,IAAI,EAAE,EAAE;gBACN,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACjC;iBAAM;gBACL,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aACjE;SACF;QAED,wDAAoB,GAApB,UAAqB,WAAmB,EAAE,OAAY,EAAE,YAAsB,EAAE,OAAa;YAC3F,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,OAAO,CAAC,YAAY,CAAC,GAAG;gBACtB,WAAW,aAAA;gBACX,aAAa,EAAE,OAAO,EAAE,YAAY,cAAA;gBACpC,oBAAoB,EAAE,KAAK;aAC5B,CAAC;SACH;QAED,0CAAM,GAAN,UACI,WAAmB,EAAE,OAAY,EAAE,IAAY,EAAE,KAAa,EAC9D,QAAiC;YACnC,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;aACjF;YACD,OAAO,eAAQ,CAAC;SACjB;QAEO,qDAAiB,GAAzB,UACI,KAAuB,EAAE,YAAmC,EAAE,cAAsB,EACpF,cAAsB,EAAE,YAAsB;YAChD,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CACzB,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,EACtF,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;SACjG;QAED,0DAAsB,GAAtB,UAAuB,gBAAqB;YAA5C,iBAQC;YAPC,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAC9E,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;YAE7E,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC;gBAAE,OAAO;YAEnD,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;YAC5E,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;SAClF;QAED,qEAAiC,GAAjC,UAAkC,OAAY;YAC5C,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;;;;oBAIpB,IAAI,MAAM,CAAC,MAAM,EAAE;wBACjB,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAChC;yBAAM;wBACL,MAAM,CAAC,OAAO,EAAE,CAAC;qBAClB;iBACF,CAAC,CAAC;aACJ;SACF;QAED,yEAAqC,GAArC,UAAsC,OAAY;YAChD,IAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,MAAM,EAAE,GAAA,CAAC,CAAC;aAC5C;SACF;QAED,qDAAiB,GAAjB;YAAA,iBAQC;YAPC,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO;gBACxB,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,OAAO,mBAAmB,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,cAAM,OAAA,OAAO,EAAE,GAAA,CAAC,CAAC;iBAClE;qBAAM;oBACL,OAAO,EAAE,CAAC;iBACX;aACF,CAAC,CAAC;SACJ;QAED,oDAAgB,GAAhB,UAAiB,OAAY;YAA7B,iBAsBC;YArBC,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;YAC/D,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;;gBAEpC,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;gBAC3C,IAAI,OAAO,CAAC,WAAW,EAAE;oBACvB,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;oBACrC,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;oBACrD,IAAI,EAAE,EAAE;wBACN,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;qBAC/B;iBACF;gBACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;aACzD;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE;gBAC1D,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAC5C;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC9D,KAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAED,yCAAK,GAAL,UAAM,WAAwB;YAA9B,iBAkDC;YAlDK,4BAAA,EAAA,eAAuB,CAAC;YAC5B,IAAI,OAAO,GAAsB,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,EAAE,EAAE,OAAO,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC,GAAA,CAAC,CAAC;gBACvF,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;aAC9B;YAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3D,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAC3C,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;iBAC/B;aACF;YAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;iBACzB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;gBACnE,IAAM,UAAU,GAAe,EAAE,CAAC;gBAClC,IAAI;oBACF,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;iBAC1D;wBAAS;oBACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC1C,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;qBACjB;iBACF;aACF;iBAAM;gBACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3D,IAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;iBAChC;aACF;YAED,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YAEpB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;;gBAI7B,IAAM,UAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;gBACpC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;gBAExB,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,cAAQ,UAAQ,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC9E;qBAAM;oBACL,UAAQ,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;iBAC9B;aACF;SACF;QAED,+CAAW,GAAX,UAAY,MAAgB;YAC1B,MAAM,IAAI,KAAK,CACX,oFACI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;SAC9B;QAEO,oDAAgB,GAAxB,UAAyB,UAAsB,EAAE,WAAmB;YAApE,iBAqXC;YAnXC,IAAM,YAAY,GAAG,IAAI,qBAAqB,EAAE,CAAC;YACjD,IAAM,cAAc,GAAgC,EAAE,CAAC;YACvD,IAAM,iBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;YAC5D,IAAM,kBAAkB,GAAuB,EAAE,CAAC;YAClD,IAAM,eAAe,GAAG,IAAI,GAAG,EAAoC,CAAC;YACpE,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAoB,CAAC;YACxD,IAAM,oBAAoB,GAAG,IAAI,GAAG,EAAoB,CAAC;YAEzD,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC7B,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAM,oBAAoB,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;gBAC5E,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAC,EAAE,EAAE;oBACpD,mBAAmB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAC,CAAC,CAAC,CAAC;iBAClD;aACF,CAAC,CAAC;YAEH,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;YACnE,IAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;;;;YAKnF,IAAM,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;gBAC/B,IAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;gBACxC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACrC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,GAAA,CAAC,CAAC;aAClD,CAAC,CAAC;YAEH,IAAM,aAAa,GAAU,EAAE,CAAC;YAChC,IAAM,gBAAgB,GAAG,IAAI,GAAG,EAAO,CAAC;YACxC,IAAM,2BAA2B,GAAG,IAAI,GAAG,EAAO,CAAC;YACnD,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAC,EAAE,EAAE;gBAC3D,IAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAC,CAAC,CAAC;gBAC/C,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;gBAC/D,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;oBACpC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC5B,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAC9B,IAAI,OAAO,CAAC,YAAY,EAAE;wBACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAA,CAAC,CAAC;qBAC3F;yBAAM;wBACL,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBAC1C;iBACF;aACF;YAED,IAAM,eAAe,GAAG,IAAI,GAAG,EAAe,CAAC;YAC/C,IAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACpF,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;gBAC/B,IAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;gBACxC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACrC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,GAAA,CAAC,CAAC;aAClD,CAAC,CAAC;YAEH,UAAU,CAAC,IAAI,CAAC;gBACd,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;oBAC/B,IAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAG,CAAC;oBAC9C,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,GAAA,CAAC,CAAC;iBACrD,CAAC,CAAC;gBAEH,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;oBAC/B,IAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAG,CAAC;oBAC9C,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,GAAA,CAAC,CAAC;iBACrD,CAAC,CAAC;gBAEH,aAAa,CAAC,OAAO,CAAC,UAAA,OAAO,IAAM,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;aACvE,CAAC,CAAC;YAEH,IAAM,UAAU,GAAgC,EAAE,CAAC;YACnD,IAAM,oBAAoB,GAAqC,EAAE,CAAC;YAClE,KAAK,IAAI,GAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,GAAC,IAAI,CAAC,EAAE,GAAC,EAAE,EAAE;gBACxD,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,GAAC,CAAC,CAAC;gBAClC,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;oBAClD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;oBAC5B,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;oBAC9B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAExB,IAAI,KAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;wBACtC,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;;wBAE/D,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;4BACjC,MAAM,CAAC,OAAO,EAAE,CAAC;4BACjB,OAAO;yBACR;qBACF;oBAED,IAAM,cAAc,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBACpF,IAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;oBACtD,IAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;oBACtD,IAAM,WAAW,GAAG,KAAI,CAAC,iBAAiB,CACtC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAG,CAAC;oBAC3E,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;wBACnD,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACvC,OAAO;qBACR;;;;;oBAMD,IAAI,cAAc,EAAE;wBAClB,MAAM,CAAC,OAAO,CAAC,cAAM,OAAA,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,GAAA,CAAC,CAAC;wBACnE,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAA,CAAC,CAAC;wBACjE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,OAAO;qBACR;;;oBAID,IAAI,KAAK,CAAC,oBAAoB,EAAE;wBAC9B,MAAM,CAAC,OAAO,CAAC,cAAM,OAAA,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,GAAA,CAAC,CAAC;wBACnE,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAA,CAAC,CAAC;wBACjE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,OAAO;qBACR;;;;;;oBAOD,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,uBAAuB,GAAG,IAAI,GAAA,CAAC,CAAC;oBAEvE,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;oBAEpD,IAAM,KAAK,GAAG,EAAC,WAAW,aAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAC,CAAC;oBAE7C,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAE/B,WAAW,CAAC,eAAe,CAAC,OAAO,CAC/B,UAAA,OAAO,IAAI,OAAA,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAA,CAAC,CAAC;oBAE3E,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,OAAO;wBACnD,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACrC,IAAI,KAAK,CAAC,MAAM,EAAE;4BAChB,IAAI,QAAM,GAAgB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;4BAC7D,IAAI,CAAC,QAAM,EAAE;gCACX,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,QAAM,GAAG,IAAI,GAAG,EAAU,CAAC,CAAC;6BAC9D;4BACD,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,QAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;yBACzC;qBACF,CAAC,CAAC;oBAEH,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,OAAO;wBACpD,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACrC,IAAI,MAAM,GAAgB,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;wBAC9D,IAAI,CAAC,MAAM,EAAE;4BACX,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC,CAAC;yBAC/D;wBACD,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;qBACzC,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;YAED,IAAI,oBAAoB,CAAC,MAAM,EAAE;gBAC/B,IAAM,QAAM,GAAa,EAAE,CAAC;gBAC5B,oBAAoB,CAAC,OAAO,CAAC,UAAA,WAAW;oBACtC,QAAM,CAAC,IAAI,CAAC,MAAI,WAAW,CAAC,WAAW,0BAAuB,CAAC,CAAC;oBAChE,WAAW,CAAC,MAAQ,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,QAAM,CAAC,IAAI,CAAC,OAAK,KAAK,OAAI,CAAC,GAAA,CAAC,CAAC;iBACpE,CAAC,CAAC;gBAEH,UAAU,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC;gBAC/C,IAAI,CAAC,WAAW,CAAC,QAAM,CAAC,CAAC;aAC1B;YAED,IAAM,qBAAqB,GAAG,IAAI,GAAG,EAAoC,CAAC;;;;;YAK1E,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAY,CAAC;YAChD,kBAAkB,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC9B,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC9B,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAC7B,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAC1C,KAAI,CAAC,qBAAqB,CACtB,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;iBACzE;aACF,CAAC,CAAC;YAEH,cAAc,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC3B,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBAC/B,IAAM,eAAe,GACjB,KAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC3F,eAAe,CAAC,OAAO,CAAC,UAAA,UAAU;oBAChC,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACrE,UAAU,CAAC,OAAO,EAAE,CAAC;iBACtB,CAAC,CAAC;aACJ,CAAC,CAAC;;;;;;;;YASH,IAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI;gBAC5C,OAAO,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;aAChF,CAAC,CAAC;;YAGH,IAAM,aAAa,GAAG,IAAI,GAAG,EAAmB,CAAC;YACjD,IAAM,oBAAoB,GAAG,qBAAqB,CAC9C,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,2BAA2B,EAAE,oBAAoB,EAAEA,qBAAU,CAAC,CAAC;YAE/F,oBAAoB,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC/B,IAAI,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,EAAE;oBAC3E,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACzB;aACF,CAAC,CAAC;;YAGH,IAAM,YAAY,GAAG,IAAI,GAAG,EAAmB,CAAC;YAChD,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;gBAC/B,qBAAqB,CACjB,YAAY,EAAE,KAAI,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,mBAAmB,EAAED,qBAAS,CAAC,CAAC;aAChF,CAAC,CAAC;YAEH,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI;gBACvB,IAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACnC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAEM,aAAK,IAAI,EAAK,GAAG,CAAS,CAAC,CAAC;aACrD,CAAC,CAAC;YAEH,IAAM,WAAW,GAAgC,EAAE,CAAC;YACpD,IAAM,UAAU,GAAgC,EAAE,CAAC;YACnD,IAAM,oCAAoC,GAAG,EAAE,CAAC;YAChD,kBAAkB,CAAC,OAAO,CAAC,UAAA,KAAK;gBACvB,IAAA,uBAAO,EAAE,qBAAM,EAAE,+BAAW,CAAU;;;gBAG7C,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAC7B,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;wBACpC,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAA,CAAC,CAAC;wBACjE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACvB,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAChD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,OAAO;qBACR;;;;;;;oBAQD,IAAI,qBAAmB,GAAQ,oCAAoC,CAAC;oBACpE,IAAI,mBAAmB,CAAC,IAAI,GAAG,CAAC,EAAE;wBAChC,IAAI,GAAG,GAAG,OAAO,CAAC;wBAClB,IAAM,YAAY,GAAU,EAAE,CAAC;wBAC/B,OAAO,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE;4BAC3B,IAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BACpD,IAAI,cAAc,EAAE;gCAClB,qBAAmB,GAAG,cAAc,CAAC;gCACrC,MAAM;6BACP;4BACD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACxB;wBACD,YAAY,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAmB,CAAC,GAAA,CAAC,CAAC;qBACtF;oBAED,IAAM,WAAW,GAAG,KAAI,CAAC,eAAe,CACpC,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EACvF,aAAa,CAAC,CAAC;oBAEnB,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBAElC,IAAI,qBAAmB,KAAK,oCAAoC,EAAE;wBAChE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC1B;yBAAM;wBACL,IAAM,aAAa,GAAG,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,qBAAmB,CAAC,CAAC;wBACrE,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;4BACzC,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;yBAC1D;wBACD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC7B;iBACF;qBAAM;oBACL,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;oBAC7C,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAA,CAAC,CAAC;;;;oBAIjE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACxB,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;wBACpC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC7B;iBACF;aACF,CAAC,CAAC;;YAGH,UAAU,CAAC,OAAO,CAAC,UAAA,MAAM;;;gBAGvB,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;oBACjD,IAAM,WAAW,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;oBAC3D,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;iBACnC;aACF,CAAC,CAAC;;;;YAKH,cAAc,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC3B,IAAI,MAAM,CAAC,YAAY,EAAE;oBACvB,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;iBAC9C;qBAAM;oBACL,MAAM,CAAC,OAAO,EAAE,CAAC;iBAClB;aACF,CAAC,CAAC;;;;YAKH,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,aAAa,CAAC,MAAM,EAAE,GAAC,EAAE,EAAE;gBAC7C,IAAM,OAAO,GAAG,aAAa,CAAC,GAAC,CAAC,CAAC;gBACjC,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;gBAC/D,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;;;;gBAKtC,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY;oBAAE,SAAS;gBAE9C,IAAI,OAAO,GAAgC,EAAE,CAAC;;;;gBAK9C,IAAI,eAAe,CAAC,IAAI,EAAE;oBACxB,IAAI,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBACxD,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE;wBACvD,OAAO,CAAC,IAAI,OAAZ,OAAO,WAAS,oBAAoB,GAAE;qBACvC;oBAED,IAAI,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;oBACnF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACpD,IAAI,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClE,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;4BAC3C,OAAO,CAAC,IAAI,OAAZ,OAAO,WAAS,cAAc,GAAE;yBACjC;qBACF;iBACF;gBAED,IAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,SAAS,GAAA,CAAC,CAAC;gBACxD,IAAI,aAAa,CAAC,MAAM,EAAE;oBACxB,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;iBAC7D;qBAAM;oBACL,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;iBAChC;aACF;;YAGD,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;YAEzB,WAAW,CAAC,OAAO,CAAC,UAAA,MAAM;gBACxB,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1B,MAAM,CAAC,MAAM,CAAC;oBACZ,MAAM,CAAC,OAAO,EAAE,CAAC;oBAEjB,IAAM,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC/B,CAAC,CAAC;gBACH,MAAM,CAAC,IAAI,EAAE,CAAC;aACf,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;SACpB;QAED,uDAAmB,GAAnB,UAAoB,WAAmB,EAAE,OAAY;YACnD,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B,CAAC;YAC/D,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa;gBAAE,YAAY,GAAG,IAAI,CAAC;YAC1D,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAE,YAAY,GAAG,IAAI,CAAC;YAC5D,IAAI,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAE,YAAY,GAAG,IAAI,CAAC;YACnE,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAE,YAAY,GAAG,IAAI,CAAC;YAC3D,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC;SACvF;QAED,8CAAU,GAAV,UAAW,QAAmB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;QAElE,4DAAwB,GAAxB,UAAyB,QAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;QAE5E,uDAAmB,GAA3B,UACI,OAAe,EAAE,gBAAyB,EAAE,WAAoB,EAAE,WAAoB,EACtF,YAAkB;YACpB,IAAI,OAAO,GAAgC,EAAE,CAAC;YAC9C,IAAI,gBAAgB,EAAE;gBACpB,IAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACxE,IAAI,qBAAqB,EAAE;oBACzB,OAAO,GAAG,qBAAqB,CAAC;iBACjC;aACF;iBAAM;gBACL,IAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC1D,IAAI,cAAc,EAAE;oBAClB,IAAM,oBAAkB,GAAG,CAAC,YAAY,IAAI,YAAY,IAAI,UAAU,CAAC;oBACvE,cAAc,CAAC,OAAO,CAAC,UAAA,MAAM;wBAC3B,IAAI,MAAM,CAAC,MAAM;4BAAE,OAAO;wBAC1B,IAAI,CAAC,oBAAkB,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW;4BAAE,OAAO;wBACrE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACtB,CAAC,CAAC;iBACJ;aACF;YACD,IAAI,WAAW,IAAI,WAAW,EAAE;gBAC9B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAA,MAAM;oBAC7B,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;wBAAE,OAAO,KAAK,CAAC;oBACnE,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;wBAAE,OAAO,KAAK,CAAC;oBACnE,OAAO,IAAI,CAAC;iBACb,CAAC,CAAC;aACJ;YACD,OAAO,OAAO,CAAC;SAChB;QAEO,yDAAqB,GAA7B,UACI,WAAmB,EAAE,WAA2C,EAChE,qBAA4D;;YAC9D,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;YAC5C,IAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;YAIxC,IAAM,iBAAiB,GACnB,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;YAC9D,IAAM,iBAAiB,GACnB,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;oCAEnD,mBAAmB;gBAC5B,IAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;gBAC5C,IAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;gBACjD,IAAM,OAAO,GAAG,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;gBACpE,IAAM,eAAe,GAAG,OAAK,mBAAmB,CAC5C,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC1F,eAAe,CAAC,OAAO,CAAC,UAAA,MAAM;oBAC5B,IAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAS,CAAC;oBACjD,IAAI,UAAU,CAAC,aAAa,EAAE;wBAC5B,UAAU,CAAC,aAAa,EAAE,CAAC;qBAC5B;oBACD,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACtB,CAAC,CAAC;;;;gBAbL,KAAkC,IAAA,KAAAC,SAAA,WAAW,CAAC,SAAS,CAAA,gBAAA;oBAAlD,IAAM,mBAAmB,WAAA;4BAAnB,mBAAmB;iBAc7B;;;;;;;;;;;YAID,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;SAClD;QAEO,mDAAe,GAAvB,UACI,WAAmB,EAAE,WAA2C,EAChE,qBAA4D,EAC5D,iBAA8C,EAAE,YAAkC,EAClF,aAAmC;YAJvC,iBA2EC;YAtEC,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;YAC5C,IAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;YAIxC,IAAM,iBAAiB,GAAgC,EAAE,CAAC;YAC1D,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO,CAAC;YAC3C,IAAM,cAAc,GAAG,IAAI,GAAG,EAAO,CAAC;YACtC,IAAM,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,mBAAmB;gBACjE,IAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;gBAC5C,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;gBAGjC,IAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;gBACtC,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB;oBACzC,OAAO,IAAIT,8BAAmB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBAE1F,IAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;gBACjD,IAAM,eAAe,GACjB,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,kBAAkB;qBACpD,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,EAAE,GAAA,CAAC,CAAC;qBAChD,MAAM,CAAC,UAAA,CAAC;;;;;oBAKP,IAAM,EAAE,GAAG,CAAQ,CAAC;oBACpB,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,CAAC;iBACpD,CAAC,CAAC;gBAEX,IAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC5C,IAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC9C,IAAM,SAAS,GAAG,kBAAkB,CAChC,KAAI,CAAC,MAAM,EAAE,KAAI,CAAC,WAAW,EAAE,OAAO,EAAE,mBAAmB,CAAC,SAAS,EAAE,SAAS,EAChF,UAAU,CAAC,CAAC;gBAChB,IAAM,MAAM,GAAG,KAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;;;gBAIlF,IAAI,mBAAmB,CAAC,WAAW,IAAI,iBAAiB,EAAE;oBACxD,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;iBAC7B;gBAED,IAAI,gBAAgB,EAAE;oBACpB,IAAM,aAAa,GAAG,IAAI,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;oBACvF,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;oBACpC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;iBACvC;gBAED,OAAO,MAAM,CAAC;aACf,CAAC,CAAC;YAEH,iBAAiB,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC9B,eAAe,CAAC,KAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/E,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,kBAAkB,CAAC,KAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,GAAA,CAAC,CAAC;aAC/F,CAAC,CAAC;YAEH,mBAAmB,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,QAAQ,CAAC,OAAO,EAAE,sBAAsB,CAAC,GAAA,CAAC,CAAC;YAClF,IAAM,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC;gBACf,mBAAmB,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,OAAO,EAAE,sBAAsB,CAAC,GAAA,CAAC,CAAC;gBACrF,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC9C,CAAC,CAAC;;;YAIH,cAAc,CAAC,OAAO,CAClB,UAAA,OAAO,IAAM,eAAe,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAElF,OAAO,MAAM,CAAC;SACf;QAEO,gDAAY,GAApB,UACI,WAAyC,EAAE,SAAuB,EAClE,eAAkC;YACpC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CACtB,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,EACvE,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;aAC1C;;;YAID,OAAO,IAAIA,8BAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;SACzE;QACH,gCAAC;IAAD,CAAC,IAAA;IAED;QAeE,mCAAmB,WAAmB,EAAS,WAAmB,EAAS,OAAY;YAApE,gBAAW,GAAX,WAAW,CAAQ;YAAS,gBAAW,GAAX,WAAW,CAAQ;YAAS,YAAO,GAAP,OAAO,CAAK;YAd/E,YAAO,GAAoB,IAAIA,8BAAmB,EAAE,CAAC;YACrD,wBAAmB,GAAG,KAAK,CAAC;YAE5B,qBAAgB,GAAoC,EAAE,CAAC;YAC/C,cAAS,GAAG,KAAK,CAAC;YAI3B,qBAAgB,GAAY,KAAK,CAAC;YAClC,aAAQ,GAAG,KAAK,CAAC;YAEf,WAAM,GAAY,IAAI,CAAC;YAChB,cAAS,GAAW,CAAC,CAAC;SAEqD;QAE3F,iDAAa,GAAb,UAAc,MAAuB;YAArC,iBAYC;YAXC,IAAI,IAAI,CAAC,mBAAmB;gBAAE,OAAO;YAErC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC9C,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAChC,UAAA,QAAQ,IAAI,OAAA,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,GAAA,CAAC,CAAC;aACrE,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxC,IAAyB,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3C;QAED,iDAAa,GAAb,cAAkB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;QAExC,qDAAiB,GAAjB,UAAkB,SAAiB,IAAK,IAAY,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;QAE7E,oDAAgB,GAAhB,UAAiB,MAAuB;YAAxC,iBAOC;YANC,IAAM,CAAC,GAAG,IAAI,CAAC,OAAc,CAAC;YAC9B,IAAI,CAAC,CAAC,eAAe,EAAE;gBACrB,MAAM,CAAC,OAAO,CAAC,cAAM,OAAA,CAAC,CAAC,eAAiB,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;aACpD;YACD,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,KAAI,CAAC,MAAM,EAAE,GAAA,CAAC,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,EAAE,GAAA,CAAC,CAAC;SACxC;QAEO,+CAAW,GAAnB,UAAoB,IAAY,EAAE,QAA6B;YAC7D,eAAe,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjE;QAED,0CAAM,GAAN,UAAO,EAAc;YACnB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACzB;QAED,2CAAO,GAAP,UAAQ,EAAc;YACpB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC1B;QAED,6CAAS,GAAT,UAAU,EAAc;YACtB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;aACjC;YACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,wCAAI,GAAJ,cAAe,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE;QAErC,8CAAU,GAAV,cAAwB,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE;QAEjF,wCAAI,GAAJ,cAAe,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE;QAErD,yCAAK,GAAL,cAAgB,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE;QAEvD,2CAAO,GAAP,cAAkB,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE;QAE3D,0CAAM,GAAN,cAAiB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;QAEzC,2CAAO,GAAP;YACG,IAA4B,CAAC,SAAS,GAAG,IAAI,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SACxB;QAED,yCAAK,GAAL,cAAgB,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE;QAEvD,+CAAW,GAAX,UAAY,CAAM;YAChB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,+CAAW,GAAX,cAAwB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;;QAG9E,mDAAe,GAAf,UAAgB,SAAiB;YAC/B,IAAM,CAAC,GAAG,IAAI,CAAC,OAAc,CAAC;YAC9B,IAAI,CAAC,CAAC,eAAe,EAAE;gBACrB,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;aAC9B;SACF;QACH,gCAAC;IAAD,CAAC,IAAA;IAED,SAAS,kBAAkB,CAAC,GAA0C,EAAE,GAAQ,EAAE,KAAU;QAC1F,IAAI,aAAmC,CAAC;QACxC,IAAI,GAAG,YAAY,GAAG,EAAE;YACtB,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI,aAAa,EAAE;gBACjB,IAAI,aAAa,CAAC,MAAM,EAAE;oBACxB,IAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC3C,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAChC;gBACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;oBAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBACjB;aACF;SACF;aAAM;YACL,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACzB,IAAI,aAAa,EAAE;gBACjB,IAAI,aAAa,CAAC,MAAM,EAAE;oBACxB,IAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC3C,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAChC;gBACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;oBAC7B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;iBACjB;aACF;SACF;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,SAAS,qBAAqB,CAAC,KAAU;;;;QAIvC,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,SAAS,aAAa,CAAC,IAAS;QAC9B,OAAO,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,mBAAmB,CAAC,SAAiB;QAC5C,OAAO,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,MAAM,CAAC;IACrD,CAAC;IAED,SAAS,YAAY,CAAC,OAAY,EAAE,KAAc;QAChD,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;QACvC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;QACvD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,SAAS,qBAAqB,CAC1B,SAA+B,EAAE,MAAuB,EAAE,QAAkB,EAC5E,eAAsC,EAAE,YAAoB;QAC9D,IAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAA,CAAC,CAAC;QAEnE,IAAM,cAAc,GAAU,EAAE,CAAC;QAEjC,eAAe,CAAC,OAAO,CAAC,UAAC,KAAkB,EAAE,OAAY;YACvD,IAAM,MAAM,GAAe,EAAE,CAAC;YAC9B,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBAChB,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;;;gBAI9E,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;oBAC/B,OAAO,CAAC,YAAY,CAAC,GAAG,0BAA0B,CAAC;oBACnD,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC9B;aACF,CAAC,CAAC;YACH,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAChC,CAAC,CAAC;;;QAIH,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAA,CAAC,CAAC;QAEnE,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;;;;;IAUA,SAAS,YAAY,CAAC,KAAY,EAAE,KAAY;QAC9C,IAAM,OAAO,GAAG,IAAI,GAAG,EAAc,CAAC;QACtC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,GAAA,CAAC,CAAC;QAE7C,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAEtC,IAAM,SAAS,GAAG,CAAC,CAAC;QACpB,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAM,YAAY,GAAG,IAAI,GAAG,EAAY,CAAC;QAEzC,SAAS,OAAO,CAAC,IAAS;YACxB,IAAI,CAAC,IAAI;gBAAE,OAAO,SAAS,CAAC;YAE5B,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEtB,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,GAAG,MAAM,CAAC;aACf;iBAAM,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC9B,IAAI,GAAG,SAAS,CAAC;aAClB;iBAAM;gBACL,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;aACxB;YAED,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;SACb;QAED,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;YAChB,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3B,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAChC;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAM,iBAAiB,GAAG,WAAW,CAAC;AACtC,IASA,SAAS,QAAQ,CAAC,OAAY,EAAE,SAAiB;QAC/C,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAClC;aAAM;YACL,IAAI,OAAO,GAAmC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;aAC3C;YACD,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;SAC3B;IACH,CAAC;IAED,SAAS,WAAW,CAAC,OAAY,EAAE,SAAiB;QAClD,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACrC;aAAM;YACL,IAAI,OAAO,GAAmC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,OAAO,EAAE;gBACX,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;aAC3B;SACF;IACH,CAAC;IAED,SAAS,6BAA6B,CAClC,MAAiC,EAAE,OAAY,EAAE,OAA0B;QAC7E,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,cAAM,OAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;IAC9E,CAAC;IAED,SAAS,mBAAmB,CAAC,OAA0B;QACrD,IAAM,YAAY,GAAsB,EAAE,CAAC;QAC3C,yBAAyB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACjD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,SAAS,yBAAyB,CAAC,OAA0B,EAAE,YAA+B;QAC5F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,MAAM,YAAYU,gCAAoB,EAAE;gBAC1C,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;aACzD;iBAAM;gBACL,YAAY,CAAC,IAAI,CAAC,MAAyB,CAAC,CAAC;aAC9C;SACF;IACH,CAAC;IAED,SAAS,SAAS,CAAC,CAAuB,EAAE,CAAuB;QACjE,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;gBAAE,OAAO,KAAK,CAAC;SAClE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,sBAAsB,CAC3B,OAAY,EAAE,mBAA0C,EACxD,oBAA2C;QAC7C,IAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAE7B,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE;YACZ,SAAS,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,QAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;SACjD;aAAM;YACL,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;SAC7C;QAED,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;;;QC5qDC,yBACY,QAAa,EAAU,OAAwB,EACvD,UAAoC;YAFxC,iBAQC;YAPW,aAAQ,GAAR,QAAQ,CAAK;YAAU,YAAO,GAAP,OAAO,CAAiB;YANnD,kBAAa,GAAsC,EAAE,CAAC;;YAGvD,sBAAiB,GAAG,UAAC,OAAY,EAAE,OAAY,KAAO,CAAC;YAK5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YACtF,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAElF,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,UAAC,OAAY,EAAE,OAAY;gBAClE,OAAA,KAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC;aAAA,CAAC;SAC9C;QAED,yCAAe,GAAf,UACI,WAAmB,EAAE,WAAmB,EAAE,WAAgB,EAAE,IAAY,EACxE,QAAkC;YACpC,IAAM,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC;YAC1C,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,CAAC,OAAO,EAAE;gBACZ,IAAM,MAAM,GAAU,EAAE,CAAC;gBACzB,IAAM,GAAG,GACL,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAA6B,EAAE,MAAM,CAAe,CAAC;gBACzF,IAAI,MAAM,CAAC,MAAM,EAAE;oBACjB,MAAM,IAAI,KAAK,CACX,6BAA0B,IAAI,gEAA0D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAG,CAAC,CAAC;iBACrH;gBACD,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;aACxC;YACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACpE;QAED,kCAAQ,GAAR,UAAS,WAAmB,EAAE,WAAgB;YAC5C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;SAC3D;QAED,iCAAO,GAAP,UAAQ,WAAmB,EAAE,OAAY;YACvC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACtD;QAED,kCAAQ,GAAR,UAAS,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB;YAC5E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;SAC/E;QAED,kCAAQ,GAAR,UAAS,WAAmB,EAAE,OAAY,EAAE,OAAY;YACtD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAClE;QAED,2CAAiB,GAAjB,UAAkB,OAAY,EAAE,OAAgB;YAC9C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAChE;QAED,iCAAO,GAAP,UAAQ,WAAmB,EAAE,OAAY,EAAE,QAAgB,EAAE,KAAU;YACrE,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;gBACvB,IAAA,8CAA6C,EAA5C,UAAE,EAAE,cAAwC,CAAC;gBACpD,IAAM,IAAI,GAAG,KAAc,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aACzD;iBAAM;gBACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;aACvE;SACF;QAED,gCAAM,GAAN,UACI,WAAmB,EAAE,OAAY,EAAE,SAAiB,EAAE,UAAkB,EACxE,QAA6B;;YAE/B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;gBACxB,IAAA,+CAA8C,EAA7C,UAAE,EAAE,cAAyC,CAAC;gBACrD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;aACnE;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SAC7F;QAED,+BAAK,GAAL,UAAM,WAAwB;YAAxB,4BAAA,EAAA,eAAuB,CAAC;YAAU,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAAE;QAEpF,sBAAI,oCAAO;iBAAX;gBACE,OAAQ,IAAI,CAAC,iBAAiB,CAAC,OAA6B;qBACvD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAA4B,CAAC,CAAC;aAChE;;;WAAA;QAED,2CAAiB,GAAjB,cAAoC,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,EAAE;QAC1F,sBAAC;IAAD,CAAC;;ICzGD;;;;;;;AAOA,IAEA;;;;;;;;;;;AAWA,aAAgB,0BAA0B,CACtC,OAAY,EAAE,MAAqD;QACrE,IAAI,WAAW,GAA8B,IAAI,CAAC;QAClD,IAAI,SAAS,GAA8B,IAAI,CAAC;QAChD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;YAC1C,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,SAAS,GAAG,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;aAClE;SACF;aAAM,IAAI,MAAM,EAAE;YACjB,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC;SACjD;QAED,OAAO,CAAC,WAAW,IAAI,SAAS,IAAI,IAAI,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC;YACvD,IAAI,CAAC;IAC3C,CAAC;IAED;;;;;;;;AAQA;QAME,4BACY,QAAa,EAAU,YAAuC,EAC9D,UAAqC;YADrC,aAAQ,GAAR,QAAQ,CAAK;YAAU,iBAAY,GAAZ,YAAY,CAA2B;YAC9D,eAAU,GAAV,UAAU,CAA2B;YALzC,WAAM,mBAAmC;YAM/C,IAAI,aAAa,GAAG,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5E,IAAI,CAAC,aAAa,EAAE;gBAClB,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC;aAC7E;YACD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;SACrC;QAED,kCAAK,GAAL;YACE,IAAI,IAAI,CAAC,MAAM,oBAAoC;gBACjD,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;iBAClE;gBACD,IAAI,CAAC,MAAM,mBAAmC;aAC/C;SACF;QAED,mCAAM,GAAN;YACE,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,MAAM,qBAAqC;gBAClD,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9C,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;iBACxB;gBACD,IAAI,CAAC,MAAM,mBAAmC;aAC/C;SACF;QAED,oCAAO,GAAP;YACE,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,MAAM,sBAAsC;gBACnD,kBAAkB,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChE,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;iBACxB;gBACD,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;iBACxB;gBACD,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,qBAAqC;aACjD;SACF;QAnDM,yCAAsB,GAAG,IAAI,OAAO,EAA6B,CAAC;QAoD3E,yBAAC;KArDD,IAqDC;IAmBD,SAAS,yBAAyB,CAAC,MAA4B;QAC7D,IAAI,MAAM,GAA8B,IAAI,CAAC;QAC7C,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE;gBAC9B,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;aAC7B;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,oBAAoB,CAAC,IAAY;QACxC,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,CAAC;IACnD,CAAC;;ICpID;;;;;;;IAOA,IAAM,+BAA+B,GAAG,CAAC,CAAC;IAC1C,IAAM,cAAc,GAAG,WAAW,CAAC;IACnC,IAAM,kBAAkB,GAAG,cAAc,CAAC;IAC1C,IAAMC,YAAU,GAAG,IAAI,CAAC;IAExB;QAOE,sCACqB,QAAa,EAAmB,KAAa,EAC7C,SAAiB,EAAmB,MAAc,EAClD,OAAe,EAAmB,SAA+B,EACjE,SAAoB;YAJzC,iBAMC;YALoB,aAAQ,GAAR,QAAQ,CAAK;YAAmB,UAAK,GAAL,KAAK,CAAQ;YAC7C,cAAS,GAAT,SAAS,CAAQ;YAAmB,WAAM,GAAN,MAAM,CAAQ;YAClD,YAAO,GAAP,OAAO,CAAQ;YAAmB,cAAS,GAAT,SAAS,CAAsB;YACjE,cAAS,GAAT,SAAS,CAAW;YATjC,cAAS,GAAG,KAAK,CAAC;YAClB,eAAU,GAAG,KAAK,CAAC;YACnB,eAAU,GAAG,CAAC,CAAC;YACf,cAAS,GAAG,CAAC,CAAC;YAOpB,IAAI,CAAC,QAAQ,GAAG,UAAC,CAAC,IAAK,OAAA,KAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAA,CAAC;SAChD;QAED,4CAAK,GAAL;YACE,sBAAsB,CAClB,IAAI,CAAC,QAAQ,EACV,IAAI,CAAC,SAAS,WAAM,IAAI,CAAC,OAAO,SAAI,IAAI,CAAC,MAAM,oBAAe,IAAI,CAAC,SAAS,SAAI,IAAI,CAAC,KAAO,CAAC,CAAC;YACrG,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9B;QAED,4CAAK,GAAL,cAAU,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE;QAEpE,6CAAM,GAAN,cAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE;QAEtE,kDAAW,GAAX,UAAY,QAAgB;YAC1B,IAAM,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC3C,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAI,IAAI,CAAC,SAAS,OAAI,EAAE,KAAK,CAAC,CAAC;SAC1E;QAED,kDAAW,GAAX,cAAgB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAEhC,sDAAe,GAAvB,UAAwB,KAAU;YAChC,IAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7D,IAAM,WAAW,GACb,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC,GAAGA,YAAU,CAAC;YACxF,IAAI,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK;gBACjC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE;gBAC5F,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;SACF;QAED,6CAAM,GAAN;YACE,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAO;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC7D;QAED,8CAAO,GAAP;YACE,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SACpD;QACH,mCAAC;IAAD,CAAC,IAAA;IAED,SAAS,kBAAkB,CAAC,OAAY,EAAE,IAAY,EAAE,MAA4B;QAClF,IAAM,KAAK,GAAG,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnD,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,SAAS,sBAAsB,CAAC,OAAY,EAAE,KAAa;QACzD,IAAM,IAAI,GAAG,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACnD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,KAAK,GAAG,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YAClC,KAAK,GAAM,IAAI,UAAK,KAAO,CAAC;SAC7B;QACD,iBAAiB,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,uBAAuB,CAAC,OAAY,EAAE,IAAY;QACzD,IAAM,IAAI,GAAG,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAM,KAAK,GAAG,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnD,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACxB,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,iBAAiB,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;SAC1C;IACH,CAAC;IAED,SAAS,qBAAqB,CAAC,OAAY,EAAE,KAAa;QACxD,IAAM,IAAI,GAAG,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACzB,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,OAAO,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC9C;QACD,OAAO,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,sBAAsB,CAAC,MAAgB,EAAE,WAAmB;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;gBACvC,OAAO,CAAC,CAAC;aACV;SACF;QACD,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,SAAS,uBAAuB,CAAC,OAAY,EAAE,EAAmB,EAAE,QAAiB;QACnF,QAAQ,GAAG,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,EAAE,CAAC;YACnD,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED,SAAS,iBAAiB,CAAC,OAAY,EAAE,IAAY,EAAE,KAAa,EAAE,KAAc;QAClF,IAAM,IAAI,GAAG,cAAc,GAAG,IAAI,CAAC;QACnC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,IAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;gBACtB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC1B;SACF;QACD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED,SAAS,iBAAiB,CAAC,OAAY,EAAE,IAAY;QACnD,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,SAAS,UAAU,CAAC,KAAa,EAAE,IAAY;QAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,KAAK,IAAI;gBAAE,KAAK,EAAE,CAAC;SACzB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;;ICrID,IAAM,iBAAiB,GAAG,UAAU,CAAC;IACrC,IAAM,cAAc,GAAG,QAAQ,CAAC;AAIhC;QAiBE,4BACoB,OAAY,EAAkB,SAA6C,EAC3E,aAAqB,EAAmB,SAAiB,EACxD,MAAc,EAAE,MAAc,EAC9B,YAAkC,EAClC,cAAwC;YAJzC,YAAO,GAAP,OAAO,CAAK;YAAkB,cAAS,GAAT,SAAS,CAAoC;YAC3E,kBAAa,GAAb,aAAa,CAAQ;YAAmB,cAAS,GAAT,SAAS,CAAQ;YACxD,WAAM,GAAN,MAAM,CAAQ;YACd,iBAAY,GAAZ,YAAY,CAAsB;YAClC,mBAAc,GAAd,cAAc,CAA0B;YArBrD,eAAU,GAAe,EAAE,CAAC;YAC5B,gBAAW,GAAe,EAAE,CAAC;YAC7B,kBAAa,GAAe,EAAE,CAAC;YAE/B,aAAQ,GAAG,KAAK,CAAC;YAQlB,oBAAe,GAA4B,EAAE,CAAC;YAE7C,WAAM,GAAyB,CAAC,CAAC;YAQvC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,cAAc,CAAC;YACvC,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;QAED,oCAAO,GAAP,UAAQ,EAAc,IAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAE5D,mCAAM,GAAN,UAAO,EAAc,IAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAE1D,sCAAS,GAAT,UAAU,EAAc,IAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAEhE,oCAAO,GAAP;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,CAAC,MAAM;gBAAoC,OAAO;YAC1D,IAAI,CAAC,MAAM,qBAAkC;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;aAC/B;YACD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SACzB;QAEO,0CAAa,GAArB;YACE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACtB;QAEO,2CAAc,GAAtB;YACE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;SACvB;QAED,mCAAM,GAAN;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,CAAC,MAAM;gBAAmC,OAAO;YACzD,IAAI,CAAC,MAAM,oBAAiC;YAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;aAC9B;YACD,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QAED,wCAAW,GAAX,UAAY,KAAa,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAE/D,wCAAW,GAAX,cAAwB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;QAE5D,uCAAU,GAAV,cAAwB,OAAO,IAAI,CAAC,MAAM,oBAAiC,EAAE;QAC7E,iCAAI,GAAJ;YACE,IAAI,IAAI,CAAC,MAAM;gBAAsC,OAAO;YAC5D,IAAI,CAAC,MAAM,uBAAoC;YAC/C,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aACtB;SACF;QAED,iCAAI,GAAJ;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,mBAAgC;gBAC3C,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;iBAC7B;aACF;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;SACvB;QAED,kCAAK,GAAL;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACtB;QACD,oCAAO,GAAP;YACE,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QACD,kCAAK,GAAL;YACE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACtB;QAEO,yCAAY,GAApB;YAAA,iBAIC;YAHC,IAAI,CAAC,OAAO,GAAG,IAAI,4BAA4B,CAC3C,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAC1E,iBAAiB,EAAE,cAAM,OAAA,KAAI,CAAC,MAAM,EAAE,GAAA,CAAC,CAAC;SAC7C;;QAGD,4CAAe,GAAf,UAAgB,SAAiB;YAC/B,IAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC1E,OAAO,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SACpB;QAED,0CAAa,GAAb;YAAA,iBAYC;YAXC,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAM,MAAM,GAA4B,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,IAAM,UAAQ,GAAG,IAAI,CAAC,MAAM,qBAAkC;gBAC9D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBACzC,IAAI,IAAI,IAAI,QAAQ,EAAE;wBACpB,MAAM,CAAC,IAAI,CAAC,GAAG,UAAQ,GAAG,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACtF;iBACF,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;SAC/B;QACH,yBAAC;IAAD,CAAC;;ICjJD;QAAuCJ,qCAAmB;QAKxD,2BAAmB,OAAY,EAAE,MAA4B;YAA7D,YACE,iBAAO,SAER;YAHkB,aAAO,GAAP,OAAO,CAAK;YAJvB,qBAAe,GAA8B,EAAE,CAAC;YAChD,mBAAa,GAAG,KAAK,CAAC;YAK5B,KAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;;SAC5C;QAED,gCAAI,GAAJ;YAAA,iBAOC;YANC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe;gBAAE,OAAO;YACxD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACpC,KAAI,CAAC,eAAiB,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACzD,CAAC,CAAC;YACH,iBAAM,IAAI,WAAE,CAAC;SACd;QAED,gCAAI,GAAJ;YAAA,iBAMC;YALC,IAAI,CAAC,IAAI,CAAC,eAAe;gBAAE,OAAO;YAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;iBACpB,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAA,CAAC,CAAC;YAC/E,iBAAM,IAAI,WAAE,CAAC;SACd;QAED,mCAAO,GAAP;YAAA,iBAYC;YAXC,IAAI,CAAC,IAAI,CAAC,eAAe;gBAAE,OAAO;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC5C,IAAM,KAAK,GAAG,KAAI,CAAC,eAAiB,CAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,KAAK,EAAE;oBACT,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBAC7C;qBAAM;oBACL,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;iBACzC;aACF,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,iBAAM,OAAO,WAAE,CAAC;SACjB;QACH,wBAAC;IAAD,CAxCA,CAAuCP,8BAAmB,GAwCzD;;ICjCD,IAAM,qBAAqB,GAAG,aAAa,CAAC;IAC5C,IAAM,SAAS,GAAG,GAAG,CAAC;AAEtB;QAAA;YACU,WAAM,GAAG,CAAC,CAAC;YACF,UAAK,GAAQ,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACrD,mBAAc,GAAG,KAAK,CAAC;SAqGhC;QAnGC,kDAAqB,GAArB,UAAsB,IAAY,IAAa,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;QAEpF,2CAAc,GAAd,UAAe,OAAY,EAAE,QAAgB;YAC3C,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1C;QAED,4CAAe,GAAf,UAAgB,IAAS,EAAE,IAAS,IAAa,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;QAEtF,kCAAK,GAAL,UAAM,OAAY,EAAE,QAAgB,EAAE,KAAc;YAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC9C;QAED,yCAAY,GAAZ,UAAa,OAAY,EAAE,IAAY,EAAE,YAAqB;YAC5D,OAAQ,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAS,CAAC,IAAI,CAAW,CAAC;SAClE;QAED,iDAAoB,GAApB,UAAqB,OAAY,EAAE,IAAY,EAAE,SAAiC;YAChF,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,mBAAmB,CAAC,EAAE,CAAC,GAAA,CAAC,CAAC;YACzD,IAAI,WAAW,GAAG,gBAAc,IAAI,SAAM,CAAC;YAC3C,IAAI,GAAG,GAAG,EAAE,CAAC;YACb,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE;gBAClB,GAAG,GAAG,SAAS,CAAC;gBAChB,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACxC,WAAW,IAAI,KAAG,GAAG,GAAG,MAAM,GAAG,GAAG,UAAO,CAAC;gBAC5C,GAAG,IAAI,SAAS,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBAC1B,IAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;oBACvB,QAAQ,IAAI;wBACV,KAAK,QAAQ;4BACX,OAAO;wBACT,KAAK,QAAQ;4BACX,IAAI,KAAK,EAAE;gCACT,WAAW,IAAO,GAAG,mCAA8B,KAAK,QAAK,CAAC;6BAC/D;4BACD,OAAO;wBACT;4BACE,WAAW,IAAI,KAAG,GAAG,GAAG,IAAI,UAAK,KAAK,QAAK,CAAC;4BAC5C,OAAO;qBACV;iBACF,CAAC,CAAC;gBACH,WAAW,IAAO,GAAG,QAAK,CAAC;aAC5B,CAAC,CAAC;YACH,WAAW,IAAI,KAAK,CAAC;YAErB,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC;YAC9B,OAAO,KAAK,CAAC;SACd;QAED,oCAAO,GAAP,UACI,OAAY,EAAE,SAAuB,EAAE,QAAgB,EAAE,KAAa,EAAE,MAAc,EACtF,eAAuC,EAAE,uBAAiC;YAA1E,gCAAA,EAAA,oBAAuC;YACzC,IAAI,uBAAuB,EAAE;gBAC3B,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;YAED,IAAM,0BAA0B,GAAyB,eAAe,CAAC,MAAM,CAC3E,UAAA,MAAM,IAAI,OAAA,MAAM,YAAY,kBAAkB,GAAA,CAAC,CAAC;YAEpD,IAAM,cAAc,GAAyB,EAAE,CAAC;YAEhD,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;gBACnD,0BAA0B,CAAC,OAAO,CAAC,UAAA,MAAM;oBACvC,IAAI,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;oBACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;iBAC1E,CAAC,CAAC;aACJ;YAED,SAAS,GAAG,kCAAkC,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACnF,IAAM,WAAW,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC;;;;;YAM1D,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACjB,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;aACpD;YAED,IAAM,aAAa,GAAG,KAAG,qBAAqB,GAAG,IAAI,CAAC,MAAM,EAAI,CAAC;YACjE,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YAC3E,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEpD,IAAM,aAAa,GAAG,0BAA0B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACrE,IAAM,MAAM,GAAG,IAAI,kBAAkB,CACjC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;YAE5F,MAAM,CAAC,SAAS,CAAC,cAAM,OAAA,aAAa,CAAC,KAAK,CAAC,GAAA,CAAC,CAAC;YAC7C,OAAO,MAAM,CAAC;SACf;QAEO,kDAAqB,GAA7B;YACE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,OAAO,CAAC,IAAI,CACR,mGAAmG,EACnG,uFAAuF,CAAC,CAAC;gBAC7F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC5B;SACF;QACH,yBAAC;IAAD,CAAC,IAAA;IAED,SAAS,0BAA0B,CAC/B,SAA+D;QACjE,IAAI,aAAa,GAAyB,EAAE,CAAC;QAC7C,IAAI,SAAS,EAAE;YACb,IAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;YAC/D,GAAG,CAAC,OAAO,CAAC,UAAA,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBAC1B,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ;wBAAE,OAAO;oBACjD,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;iBAChC,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,SAAS,aAAa,CAAC,IAAS;QAC9B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;;;QC7GC,6BACW,OAAY,EAAS,SAA6C,EAClE,OAAyC,EACxC,cAAwC;YAFzC,YAAO,GAAP,OAAO,CAAK;YAAS,cAAS,GAAT,SAAS,CAAoC;YAClE,YAAO,GAAP,OAAO,CAAkC;YACxC,mBAAc,GAAd,cAAc,CAA0B;YAtB5C,eAAU,GAAe,EAAE,CAAC;YAC5B,gBAAW,GAAe,EAAE,CAAC;YAC7B,kBAAa,GAAe,EAAE,CAAC;YAG/B,iBAAY,GAAG,KAAK,CAAC;YACrB,cAAS,GAAG,KAAK,CAAC;YAClB,aAAQ,GAAG,KAAK,CAAC;YACjB,eAAU,GAAG,KAAK,CAAC;YAMpB,SAAI,GAAG,CAAC,CAAC;YAET,iBAAY,GAAyB,IAAI,CAAC;YAC1C,oBAAe,GAA2C,EAAE,CAAC;YAMlE,IAAI,CAAC,SAAS,GAAW,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,GAAW,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;SAC1C;QAEO,uCAAS,GAAjB;YACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;aACtB;SACF;QAED,kCAAI,GAAJ;YACE,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;QAEO,0CAAY,GAApB;YAAA,iBASC;YARC,IAAI,IAAI,CAAC,YAAY;gBAAE,OAAO;YAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAChC,IAAiC,CAAC,SAAS;gBACxC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9E,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAM,OAAA,KAAI,CAAC,SAAS,EAAE,GAAA,CAAC,CAAC;SACnE;QAEO,uDAAyB,GAAjC;;YAEE,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;aACxB;SACF;;QAGD,kDAAoB,GAApB,UAAqB,OAAY,EAAE,SAAgB,EAAE,OAAY;;;YAG/D,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAiB,CAAC;SAC/D;QAED,qCAAO,GAAP,UAAQ,EAAc,IAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAE5D,oCAAM,GAAN,UAAO,EAAc,IAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAE1D,uCAAS,GAAT,UAAU,EAAc,IAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAEhE,kCAAI,GAAJ;YACE,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;iBAC7B;aACF;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;SACvB;QAED,mCAAK,GAAL;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;QAED,oCAAM,GAAN;YACE,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;aAC9B;YACD,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;SACzB;QAED,mCAAK,GAAL;YACE,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;QAEO,kDAAoB,GAA5B;YACE,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACzB;SACF;QAED,qCAAO,GAAP;YACE,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QAED,wCAAU,GAAV,cAAwB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;QAE/C,qCAAO,GAAP;YACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;iBAC/B;gBACD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;gBACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;aACzB;SACF;QAED,yCAAW,GAAX,UAAY,CAAS,IAAU,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;QAE5E,yCAAW,GAAX,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;QAExE,sBAAI,0CAAS;iBAAb,cAA0B,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;;;WAAA;QAEhE,2CAAa,GAAb;YAAA,iBAWC;YAVC,IAAM,MAAM,GAAqC,EAAE,CAAC;YACpD,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;oBAC3C,IAAI,IAAI,IAAI,QAAQ,EAAE;wBACpB,MAAM,CAAC,IAAI,CAAC;4BACR,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACnF;iBACF,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;SAC/B;;QAGD,6CAAe,GAAf,UAAgB,SAAiB;YAC/B,IAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC1E,OAAO,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SACpB;QACH,0BAAC;IAAD,CAAC;;;QC7JD;YACU,kBAAa,GAAG,6BAA6B,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrF,wBAAmB,GAAG,IAAI,kBAAkB,EAAE,CAAC;SAqDxD;QAnDC,mDAAqB,GAArB,UAAsB,IAAY,IAAa,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;QAEpF,4CAAc,GAAd,UAAe,OAAY,EAAE,QAAgB;YAC3C,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1C;QAED,6CAAe,GAAf,UAAgB,IAAS,EAAE,IAAS,IAAa,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;QAEtF,mCAAK,GAAL,UAAM,OAAY,EAAE,QAAgB,EAAE,KAAc;YAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC9C;QAED,0CAAY,GAAZ,UAAa,OAAY,EAAE,IAAY,EAAE,YAAqB;YAC5D,OAAQ,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAS,CAAC,IAAI,CAAW,CAAC;SAClE;QAED,0DAA4B,GAA5B,UAA6B,SAAkB,IAAI,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE;QAEpF,qCAAO,GAAP,UACI,OAAY,EAAE,SAAuB,EAAE,QAAgB,EAAE,KAAa,EAAE,MAAc,EACtF,eAAuC,EAAE,uBAAiC;YAA1E,gCAAA,EAAA,oBAAuC;YACzC,IAAM,YAAY,GAAG,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;YACrE,IAAI,YAAY,EAAE;gBAChB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CACnC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;aACnE;YAED,IAAM,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU,CAAC;YAC9C,IAAM,aAAa,GAAqC,EAAC,QAAQ,UAAA,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAC,CAAC;;;YAGhF,IAAI,MAAM,EAAE;gBACV,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;aAClC;YAED,IAAM,cAAc,GAAyB,EAAE,CAAC;YAChD,IAAM,2BAA2B,GAA0B,eAAe,CAAC,MAAM,CAC7E,UAAA,MAAM,IAAI,OAAA,MAAM,YAAY,mBAAmB,GAAA,CAAC,CAAC;YAErD,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;gBACnD,2BAA2B,CAAC,OAAO,CAAC,UAAA,MAAM;oBACxC,IAAI,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;oBACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;iBAC1E,CAAC,CAAC;aACJ;YAED,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,MAAM,IAAI,OAAA,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,GAAA,CAAC,CAAC;YAC/D,SAAS,GAAG,kCAAkC,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACnF,IAAM,aAAa,GAAG,0BAA0B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACrE,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;SAClF;QACH,0BAAC;IAAD,CAAC,IAAA;aAEe,qBAAqB;QACnC,OAAO,OAAO,mBAAmB,EAAE,KAAK,UAAU,CAAC;IACrD,CAAC;IAED,SAAS,mBAAmB;QAC1B,OAAO,CAAC,SAAS,EAAE,IAAU,OAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACpE,CAAC;;IChFD;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;OAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}