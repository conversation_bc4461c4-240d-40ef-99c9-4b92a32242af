/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ApplicationRef, ComponentFactoryResolver, Injectable, Injector } from '@angular/core';
import { Subject } from 'rxjs';
import { TitleWindow } from '../controls/title-window.component';
import { WindowUtils } from '../utils/window-utils';
import { Alert, parentApplication } from "../../..";
import * as i0 from "@angular/core";
/**
 * @record
 */
export function IWindow() { }
if (false) {
    /** @type {?} */
    IWindow.prototype.component;
    /** @type {?} */
    IWindow.prototype.selector;
    /** @type {?} */
    IWindow.prototype.layoutOrder;
    /** @type {?} */
    IWindow.prototype.winid;
}
//@dynamic
var WindowManager = /** @class */ (function () {
    function WindowManager(factory, injector, applicationRef) {
        this.factory = factory;
        this.injector = injector;
        this.applicationRef = applicationRef;
        // counter to indicate window index
        this.counter = 0;
        // private variable to hold opened windows.
        this.windows = {};
        // create subject
        this.windowSubject = new Subject();
        this.exists = "";
        this.windowObservable = this.windowSubject.asObservable();
    }
    /**
     * @param {?} parent
     * @param {?} component
     * @param {?} data
     * @param {?=} modal
     * @return {?}
     */
    WindowManager.prototype.createWindow = /**
     * @param {?} parent
     * @param {?} component
     * @param {?} data
     * @param {?=} modal
     * @return {?}
     */
    function (parent, component, data, modal) {
        if (modal === void 0) { modal = false; }
        this.compRef = this.factory.resolveComponentFactory(TitleWindow).create(this.injector);
        // include content in title window.
        this.compRef.instance.includeContent(parent, this, "", component, data);
        return this.compRef.instance;
    };
    /**+
     * This method is used to load component from its lazy path.
     * @param url
     */
    /**
     * +
     * This method is used to load component from its lazy path.
     * @param {?} url
     * @return {?}
     */
    WindowManager.prototype.load = /**
     * +
     * This method is used to load component from its lazy path.
     * @param {?} url
     * @return {?}
     */
    function (url) {
        this.compRef = this.factory.resolveComponentFactory(TitleWindow).create(this.injector);
        // include content in title window.
        this.compRef.instance.url = url;
        parentApplication.loaderInfo.url = url;
        parentApplication.setParams(url);
        this.compRef.instance.includeContent(parent, this, url);
        return this.compRef.instance;
    };
    // save opened window instance in HashMap
    // save opened window instance in HashMap
    /**
     * @return {?}
     */
    WindowManager.prototype.show = 
    // save opened window instance in HashMap
    /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var id = 'win_' + this.counter++;
        this.compRef.instance.layoutOrder = this.counter;
        this.compRef.instance.tabid = parentApplication.selectedTab;
        if (this.windows[this.compRef.instance.id]) {
            console.error("WindowManager - [ show ] - Cannot instantiate two window with same id! window with the id ", this.compRef.instance.id, " exists.");
            return;
        }
        if (this.compRef.instance.id) {
            this.windows[this.compRef.instance.id] = {
                component: this.compRef,
                selector: this.compRef.hostView,
                id: id,
                layoutOrder: this.counter
            };
            this.exists = this.compRef.instance.id;
        }
        else {
            this.compRef.instance.id = id;
            // save window reference in hashMap.
            this.windows[id] = {
                component: this.compRef,
                selector: this.compRef.hostView,
                id: id,
                layoutOrder: this.counter
            };
            this.exists = id;
        }
        // this line is used to set id of alert component.
        if (!this.compRef.instance.alertId) {
            this.compRef.instance.alertId = id;
        }
        // attach component to current application.
        this.applicationRef.attachView(this.compRef.hostView);
        // get component root node (component selector).
        /** @type {?} */
        var selector = (/** @type {?} */ (((/** @type {?} */ (this.compRef.hostView))).rootNodes[0]));
        // attach component root node to DOM.
        document.body.appendChild(selector);
    };
    /**
     * This function is used to crate alert.
     */
    /**
     * This function is used to crate alert.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    WindowManager.prototype.createAlert = /**
     * This function is used to crate alert.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    function (text, title, flags /* Alert.OK */, parent, closeHandler, iconClass, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = ""; }
        if (defaultButtonFlag === void 0) { defaultButtonFlag = 4; }
        this.compRef = this.factory.resolveComponentFactory(Alert).create(this.injector);
        this.compRef.instance.windowManager = this;
        this.compRef.instance.title = title;
        this.show();
        return this.compRef.instance;
    };
    /**
     * @return {?}
     */
    WindowManager.prototype.removeAll = /**
     * @return {?}
     */
    function () {
        for (var window_1 in this.windows) {
            if (this.windows.hasOwnProperty(window_1)) {
                this.close(window_1);
                WindowUtils.winOrder = 100;
            }
        }
    };
    /**
     * @param {?} id
     * @return {?}
     */
    WindowManager.prototype.close = /**
     * @param {?} id
     * @return {?}
     */
    function (id) {
        if (this.windows[id]) {
            this.windows[id].component.destroy();
            this.applicationRef.detachView(this.windows[id].selector);
            WindowUtils.winid = 'win_0';
            delete this.windows[id];
        }
    };
    /**
     * @param {?} id
     * @return {?}
     */
    WindowManager.prototype.getWindow = /**
     * @param {?} id
     * @return {?}
     */
    function (id) {
        return this.windows[id].component.instance;
    };
    /**
     * @return {?}
     */
    WindowManager.prototype.getFirstWindowInstance = /**
     * @return {?}
     */
    function () {
        return this.windows['win_0'];
    };
    /**
     * @return {?}
     */
    WindowManager.prototype.getLastWindowInsatance = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var key = 'win_' + (this.counter - 1);
        return this.windows[key];
    };
    /**
     * @param {?} winid
     * @return {?}
     */
    WindowManager.prototype.getWindowInstance = /**
     * @param {?} winid
     * @return {?}
     */
    function (winid) {
        return this.windows[winid];
    };
    /**
     * @return {?}
     */
    WindowManager.prototype.getFirstLayoutOrder = /**
     * @return {?}
     */
    function () {
        return this.getFirstWindowInstance().layoutOrder;
    };
    /**
     * @return {?}
     */
    WindowManager.prototype.getLastLayoutOrder = /**
     * @return {?}
     */
    function () {
        return this.getLastWindowInsatance().layoutOrder;
    };
    /**
     * @param {?} winid
     * @return {?}
     */
    WindowManager.prototype.getLayoutOrder = /**
     * @param {?} winid
     * @return {?}
     */
    function (winid) {
        return this.getWindowInstance(winid).layoutOrder;
    };
    WindowManager.decorators = [
        { type: Injectable, args: [{
                    providedIn: 'root'
                },] }
    ];
    /** @nocollapse */
    WindowManager.ctorParameters = function () { return [
        { type: ComponentFactoryResolver },
        { type: Injector },
        { type: ApplicationRef }
    ]; };
    /** @nocollapse */ WindowManager.ngInjectableDef = i0.defineInjectable({ factory: function WindowManager_Factory() { return new WindowManager(i0.inject(i0.ComponentFactoryResolver), i0.inject(i0.INJECTOR), i0.inject(i0.ApplicationRef)); }, token: WindowManager, providedIn: "root" });
    return WindowManager;
}());
export { WindowManager };
if (false) {
    /** @type {?} */
    WindowManager.prototype.counter;
    /** @type {?} */
    WindowManager.prototype.windows;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.windowSubject;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.windowObservable;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.compRef;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.exists;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.factory;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.injector;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.applicationRef;
}
//# sourceMappingURL=data:application/json;base64,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