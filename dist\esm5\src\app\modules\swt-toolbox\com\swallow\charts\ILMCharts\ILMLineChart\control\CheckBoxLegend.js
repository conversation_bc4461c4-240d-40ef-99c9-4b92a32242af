/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { CheckBoxLegendItem } from './CheckBoxLegendItem';
import { Series } from './Series';
import { VBox } from '../../../../controls/swt-vbox.component';
var CheckBoxLegend = /** @class */ (function (_super) {
    tslib_1.__extends(CheckBoxLegend, _super);
    function CheckBoxLegend(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._seriesList = [];
        _this._dataProvider = [];
        return _this;
    }
    /**
     * @return {?}
     */
    CheckBoxLegend.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
        //Add 'implements OnInit' to the class.
    };
    Object.defineProperty(CheckBoxLegend.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dataProvider = value;
            this.refreshLegends(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CheckBoxLegend.prototype, "seriesList", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesList;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesList = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} seriesList
     * @return {?}
     */
    CheckBoxLegend.prototype.refreshLegends = /**
     * @param {?} seriesList
     * @return {?}
     */
    function (seriesList) {
        this.legendContainer.removeAllChildren();
        for (var index = 0; index < seriesList.length; index++) {
            /** @type {?} */
            var element = seriesList[index];
            /** @type {?} */
            var checkBoxLegendItem = (/** @type {?} */ (this.legendContainer.addChild(CheckBoxLegendItem)));
            checkBoxLegendItem.yField = element.yField;
            checkBoxLegendItem.seriesStyle = element.appliedStyle;
            checkBoxLegendItem.liveValue = element.legendDisplayName;
            checkBoxLegendItem.selected = element.visible;
            checkBoxLegendItem.toolTip = element.legendTooltip;
            checkBoxLegendItem.highlight = element.highlighted;
        }
    };
    /**
     * @param {?} series
     * @return {?}
     */
    CheckBoxLegend.prototype.getLegendItem = /**
     * @param {?} series
     * @return {?}
     */
    function (series) {
        /** @type {?} */
        var found = null;
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'CheckBoxLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                // if (item && (item.element == series)) {
                if (item && (item.yField === series.yField)) {
                    found = item;
                    break;
                }
            }
        }
        return found;
    };
    /**
     * @return {?}
     */
    CheckBoxLegend.prototype.getUncheckedLegends = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var uncheckedItems = [];
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'CheckBoxLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                if (!item.selected) {
                    uncheckedItems.push(item.yField);
                }
            }
        }
        return uncheckedItems;
    };
    /**
     * @param {?} series
     * @param {?} isSelected
     * @return {?}
     */
    CheckBoxLegend.prototype.setCheckBoxLegendSelected = /**
     * @param {?} series
     * @param {?} isSelected
     * @return {?}
     */
    function (series, isSelected) {
        /** @type {?} */
        var legendItem = this.getLegendItem(series);
        if (legendItem) {
            legendItem.selected = isSelected;
        }
    };
    /**
     * To highlight the correct legend item
     * */
    /**
     * To highlight the correct legend item
     *
     * @param {?} event
     * @return {?}
     */
    CheckBoxLegend.prototype.highlighTrueFalse = /**
     * To highlight the correct legend item
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        /** @type {?} */
        var series = new Series;
        series.yField = event.yField;
        /** @type {?} */
        var legendItem = this.getLegendItem(series);
        if (legendItem) {
            legendItem.highlight = event.highligh;
        }
    };
    CheckBoxLegend.decorators = [
        { type: Component, args: [{
                    selector: 'CheckBoxLegend',
                    template: "\n        <VBox  #legendContainer width=\"100%\"> \n        </VBox>\n  ",
                    styles: ["\n      "]
                }] }
    ];
    /** @nocollapse */
    CheckBoxLegend.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    CheckBoxLegend.propDecorators = {
        legendContainer: [{ type: ViewChild, args: ['legendContainer',] }]
    };
    return CheckBoxLegend;
}(Container));
export { CheckBoxLegend };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegend.prototype.legendContainer;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype._seriesList;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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