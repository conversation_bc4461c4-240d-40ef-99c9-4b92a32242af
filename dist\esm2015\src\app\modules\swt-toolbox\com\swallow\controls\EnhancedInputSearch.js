/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { OperatorType } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
/** @type {?} */
const o = DOMPurify_;
export class EnhancedInputSearch {
    constructor() {
        this._clearFilterTriggered = false;
        this._shouldTriggerQuery = true;
        this.searchTerms = [];
        this.operator = OperatorType.equal;
        this._inputType = 'text';
    }
    /**
     * Getter for the Column Filter
     * @return {?}
     */
    get columnFilter() {
        return this.columnDef && this.columnDef.filter || {};
    }
    /**
     * Getter for the Grid Options pulled through the Grid Object
     * @protected
     * @return {?}
     */
    get gridOptions() {
        return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
    }
    /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    init(args) {
        console.log('enter custominput');
        this.grid = args.grid;
        this.callback = args.callback;
        this.columnDef = args.columnDef;
        this.searchTerms = (args.hasOwnProperty('searchTerms') ? args.searchTerms : []) || [];
        // filter input can only have 1 search term, so we will use the 1st array index if it exist
        /** @type {?} */
        const searchTerm = (Array.isArray(this.searchTerms) && this.searchTerms.length >= 0) ? this.searchTerms[0] : '';
        // step 1, create HTML string template
        /** @type {?} */
        const filterTemplate = this.buildTemplateHtmlString();
        // step 2, create the DOM Element of the filter & initialize it if searchTerm is filled
        this.$filterElm = this.createDomElement(filterTemplate, searchTerm);
        $('#search-filter-' + this.grid.getUID() + this.columnDef.id).focus();
        // step 3, subscribe to the keyup event and run the callback when that happens
        this.$filterElm.keyup((/**
         * @param {?} e
         * @return {?}
         */
        (e) => {
            /** @type {?} */
            let value = e && e.target && e.target.value || '';
            /** @type {?} */
            const enableWhiteSpaceTrim = this.gridOptions.enableFilterTrimWhiteSpace || this.columnFilter.enableTrimWhiteSpace;
            if (typeof value === 'string' && enableWhiteSpaceTrim) {
                value = value.trim();
            }
            if (this._clearFilterTriggered) {
                this.callback(e, { columnDef: this.columnDef, clearFilterTriggered: this._clearFilterTriggered, shouldTriggerQuery: this._shouldTriggerQuery });
                this.$filterElm.removeClass('filled');
            }
            else {
                value === '' ? this.$filterElm.removeClass('filled') : this.$filterElm.addClass('filled');
                this.callback(e, { columnDef: this.columnDef, searchTerms: [value], shouldTriggerQuery: this._shouldTriggerQuery });
            }
            // reset both flags for next use
            this._clearFilterTriggered = false;
            this._shouldTriggerQuery = true;
            if (e.keyCode == 13) {
                /** @type {?} */
                let $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
                $($headerElm).parent().toggle();
            }
        }));
    }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    clear(shouldTriggerQuery = true) {
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            this.$filterElm.val('');
            this.$filterElm.trigger('keyup');
        }
    }
    /**
     * destroy the filter
     * @return {?}
     */
    destroy() {
        if (this.$filterElm) {
            this.$filterElm.off('keyup').remove();
        }
    }
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    setValues(values) {
        if (values) {
            this.$filterElm.val((/** @type {?} */ (values)));
        }
    }
    //
    // private functions
    // ------------------
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    buildTemplateHtmlString() {
        /** @type {?} */
        let placeholder = 'search ' + this.columnDef.id;
        /** @type {?} */
        let $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        /** @type {?} */
        let elem = $('#' + this.grid.getUID() + this.columnDef.id);
        console.log('elem', $($headerElm).parent());
        elem.append(`<div class="ms-parent ms-filter search-filter filter-${this.columnDef.id}" style="width: 32px;"><button id="filter-${this.grid.getUID() + this.columnDef.id}" type="button" class="ms-choice"><span class="placeholder" title="" ></span><div></div></button> </div> 
       `);
        setTimeout((/**
         * @return {?}
         */
        () => {
            $('#filter-' + this.grid.getUID() + this.columnDef.id).click((/**
             * @return {?}
             */
            () => {
                $($headerElm).parent().toggle();
            }));
        }), 0);
        //       return `<input id="search-filter-${this.grid.getUID()+this.columnDef.id}"  type="${this._inputType || 'text'}" role="presentation"  autocomplete="off" class="form-control compound-input" placeholder="${placeholder}" /><span></span>`;
        return `
      <input id="search-filter-${this.grid.getUID() + this.columnDef.id}" type="text" class="form-control search-filter" placeholder="${placeholder}">`;
    }
    /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    createDomElement(filterTemplate, searchTerm) {
        /** @type {?} */
        const $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        $($headerElm).empty();
        // create the DOM element & add an ID and filter class
        /** @type {?} */
        const $filterElm = $(filterTemplate);
        $filterElm.val(searchTerm);
        $filterElm.data('columnId', this.columnDef.id);
        // append the new DOM element to the header row
        if ($filterElm && typeof $filterElm.appendTo === 'function') {
            $filterElm.appendTo($headerElm);
        }
        return $filterElm;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype._clearFilterTriggered;
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype.$filterElm;
    /** @type {?} */
    EnhancedInputSearch.prototype.grid;
    /** @type {?} */
    EnhancedInputSearch.prototype.searchTerms;
    /** @type {?} */
    EnhancedInputSearch.prototype.columnDef;
    /** @type {?} */
    EnhancedInputSearch.prototype.callback;
    /** @type {?} */
    EnhancedInputSearch.prototype.operator;
    /**
     * @type {?}
     * @protected
     */
    EnhancedInputSearch.prototype._inputType;
}
//# sourceMappingURL=data:application/json;base64,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