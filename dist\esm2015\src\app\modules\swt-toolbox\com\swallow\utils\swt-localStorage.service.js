/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
export class SwtLocalStorage {
    /**
     * addRecord is used to set a new record to
     * localStorage.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    static addRecord(key, value) {
        if (localStorage.getItem(key) === null) {
            try {
                /** @type {?} */
                const record = JSON.parse(value);
                localStorage.setItem(key, JSON.stringify(record));
            }
            catch (e) {
                localStorage.setItem(key, value);
            }
        }
        else {
            console.error('[', this.name, '] -> addRecord(key, value)', 'The', key, 'key already exists.');
        }
    }
    /**
     * getRecordCount is used to get the number
     * of records saved in the localStorage.
     * @return {?}
     */
    static getRecordsCount() {
        return localStorage.length;
    }
    /**
     * clearAll is used to clear all
     * records.
     * @return {?}
     */
    static clearAll() {
        localStorage.clear();
    }
    /**
     * this method is used to remove record.
     * @param {?} key
     * @return {?}
     */
    static removeRecord(key) {
        localStorage.removeItem(key);
    }
    /**
     * this method is used to get record.
     * @param {?} key
     * @return {?}
     */
    static getRecord(key) {
        return localStorage.getItem(key);
    }
    /**
     * this method is used to get the key in the
     * index passed as parameter.
     * @param {?} index
     * @return {?}
     */
    static getRecordKey(index) {
        return localStorage.key(index);
    }
    /**
     * this method is used to get all records.
     * return object.
     * @return {?}
     */
    static getRecords() {
        /** @type {?} */
        const records = {};
        for (let index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            /** @type {?} */
            const key = SwtLocalStorage.getRecordKey(index);
            /** @type {?} */
            const value = SwtLocalStorage.getRecord(key);
            try {
                records[key] = JSON.parse(value);
            }
            catch (e) {
                records[key] = value;
            }
        }
        return records;
    }
    /**
     * this method is used to return the list of keys.
     * @return {?}
     */
    static getRecordKeys() {
        /** @type {?} */
        const keys = [];
        for (let index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            keys.push(SwtLocalStorage.getRecordKey(index));
        }
        return keys;
    }
    /**
     * this method is used to get the list of values
     * @return {?}
     */
    static getRecordValues() {
        /** @type {?} */
        const values = new Array();
        for (let index = 0; index < SwtLocalStorage.getRecordsCount(); index++) {
            try {
                /** @type {?} */
                const value = JSON.parse(SwtLocalStorage.getRecord(SwtLocalStorage.getRecordKey(index)));
                values.push(value);
            }
            catch (e) {
                values.push(SwtLocalStorage.getRecord(SwtLocalStorage.getRecordKey(index)));
            }
        }
        return values;
    }
}
//# sourceMappingURL=data:application/json;base64,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