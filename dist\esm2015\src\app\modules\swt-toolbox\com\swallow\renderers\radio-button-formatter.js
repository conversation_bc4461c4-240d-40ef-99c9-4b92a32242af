/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export const RadioButtonFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let negative = false;
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (value instanceof Object) {
        value = value['content'];
    }
    /** @type {?} */
    let text = "";
    if (showHideCells) {
        text = `<input ${(value === 'N' || value === 'false' || value === false || value == null || value == undefined || value == '') ? '' : 'checked'} ${(enabledFlag == false || !columnDef.params.grid.enabled) ? 'disabled' : ''} type='radio'  class='editor-radio'  />`;
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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