import { OnInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
export declare class AssetsLegendItem extends Container implements OnInit {
    private elem;
    private commonService;
    private cd;
    protected hboxContainer: ElementRef;
    protected square: ElementRef;
    protected assetLabel: ElementRef;
    protected assetslabelValue: ElementRef;
    private styleClassMap;
    liveValue: string;
    private _labelValue;
    private _yField;
    private _seriesStyle;
    private created;
    labelValue: string;
    seriesStyle: string;
    ngOnInit(): void;
    yField: string;
    constructor(elem: ElementRef, commonService: CommonService, cd: ChangeDetectorRef);
}
