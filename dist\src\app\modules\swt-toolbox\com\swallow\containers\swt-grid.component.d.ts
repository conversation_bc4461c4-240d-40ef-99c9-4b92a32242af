import { ElementRef } from '@angular/core';
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class Grid extends Container {
    private elem;
    private commonService;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
}
/**
 *
 * GridRow component.
 * **/
export declare class GridRow extends Container {
    private elem;
    private commonService;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
}
/**
 *
 * GridItem component.
 *
 * **/
export declare class GridItem extends Container {
    private elem;
    private commonService;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
}
