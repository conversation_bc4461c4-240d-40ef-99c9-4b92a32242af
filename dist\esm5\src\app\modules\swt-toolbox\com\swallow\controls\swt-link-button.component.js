/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { SwtButton } from "./swt-button.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input } from "@angular/core";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var LinkButton = /** @class */ (function (_super) {
    tslib_1.__extends(LinkButton, _super);
    function LinkButton(eleme, commonService_) {
        var _this = _super.call(this, eleme, commonService_) || this;
        _this.eleme = eleme;
        _this.commonService_ = commonService_;
        $($(_this.eleme.nativeElement)[0]).attr('selector', 'SwtHelpButton');
        return _this;
    }
    Object.defineProperty(LinkButton.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var element = $($(this.swtbutton)[0].nativeElement).find('.buttonLabel');
            if (element.length > 0) {
                $(element).text(value);
            }
            this._label = value;
        },
        enumerable: true,
        configurable: true
    });
    LinkButton.decorators = [
        { type: Component, args: [{
                    selector: 'LinkButton',
                    template: "\n       <div selector=\"LinkButton\"\n            #swtbutton  \n            class=\"minWidthBtn linkbutton\">\n            <span  class=\"truncate buttonLabel\" ></span>\n       </div>\n  ",
                    styles: ["\n            :host{\n                outline:none;\n            }\n           .disabledLinkButton{\n                color: #ABB5B5!important;\n                user-select: none;\n                pointer-events: none;\n                outline: none;\n           }\n  "]
                }] }
    ];
    /** @nocollapse */
    LinkButton.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    LinkButton.propDecorators = {
        label: [{ type: Input }]
    };
    return LinkButton;
}(SwtButton));
export { LinkButton };
if (false) {
    /**
     * @type {?}
     * @private
     */
    LinkButton.prototype.eleme;
    /**
     * @type {?}
     * @private
     */
    LinkButton.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,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