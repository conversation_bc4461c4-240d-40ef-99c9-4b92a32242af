import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewContainerRef, AfterViewInit } from "@angular/core";
import { CommonService } from "../utils/common.service";
import { UIComponent } from "../controls/UIComponent.service";
import { Observable } from "rxjs/internal/Observable";
/**
 * @auth : Rihab JABALLAH created on June-2018
 */
export declare class Container extends UIComponent implements OnInit, AfterViewInit, OnDestroy {
    private __element;
    private __common;
    ngAfterViewInit(): void;
    _container: ViewContainerRef;
    private _hostElement;
    private _domElement;
    private isCancelUsed;
    private X;
    private Y;
    private urlLoadingStart;
    private urlLoadingEnd;
    components: any[];
    originalValue: any;
    firstCall: boolean;
    private _IsIncludeInLayout;
    private _isVisible;
    private _isEnabled;
    private _name;
    private _toolTip;
    private _toolTipPreviousValue;
    private __paddingTop;
    private __paddingLeft;
    private __paddingRight;
    private __paddingBottom;
    private __marginBottom;
    private __marginLeft;
    private __marginRight;
    private __marginTop;
    private __horizontalAlign;
    private __verticalAlign;
    private __width;
    private _minWidth;
    private _maxWidth;
    private __height;
    private _minHeight;
    private _maxHeight;
    private _isloaded;
    private __styleName;
    private __horizontalGap;
    private __verticalGap;
    private __right;
    private __left;
    private __cornerRadius;
    private __borderColor;
    private __backGroundColor;
    private __borderStyle;
    private __borderThickness;
    private __dropShadowEnabled;
    dropShadowEnabled: any;
    cornerRadius: any;
    borderThickness: string;
    borderStyle: string;
    borderColor: string;
    backGroundColor: string;
    right: any;
    left: any;
    bottom: any;
    top: any;
    horizontalGap: string;
    verticalGap: string;
    textAlign: string;
    readonly hostElement: any;
    readonly domElement: any;
    addTabIndex(element: any, value: any): void;
    removeTabIndex(element: any): void;
    toolTip: string;
    toolTipPreviousValue: string;
    textDictionaryId: any;
    name: string;
    styleName: string;
    horizontalAlign: any;
    verticalAlign: any;
    width: any;
    private _showScrollBar;
    showScrollBar: any;
    height: any;
    minHeight: number;
    minWidth: number;
    maxHeight: number;
    maxWidth: number;
    includeInLayout: boolean;
    visible: any;
    enabled: any;
    paddingTop: any;
    paddingBottom: any;
    paddingLeft: any;
    paddingRight: any;
    marginTop: any;
    marginBottom: any;
    marginLeft: any;
    marginRight: any;
    maxChars: any;
    constructor(__element: ElementRef, __common: CommonService);
    ngOnInit(): void;
    getRealHeight(): any;
    /**
     * addChild : append a dynamically created component.
     * @param type : Component type
     */
    addChild(value: any): any;
    /**
     * addChildAt : append a dynamically created component at a specific index.
     * @param index : index
     */
    addChildAt(type: any, index: any): any;
    /**
     * removeChild : Delete a component child
     * @param componentClass : the component to remove
     */
    removeChild(componentClass: any): void;
    /**
     * removeAllChildren : Deletes all the statically
     * & dynamically child components.
     */
    removeAllChildren(): void;
    /**
     * getChildren : get children of a statically
     * & dynamically child components.
     */
    getChildren(): any[];
    /**
     * contains : return if the component exists in the container or not
     */
    contains(componentClass: any): boolean;
    /**
     * returns a component at a specific key index
     * @param key
     */
    getChildAt(key: any): any;
    /**
     *
     */
    getChildByName(name: any): any;
    /**
     *
     */
    removeChildAt(index: any): void;
    /**
     * returns  component'index if exists otherwise -1.
     * @param component
     */
    getChildIndex(component: any): number;
    /**
     * This method is used to load component instance from its lazy module path.
     * @param url
     */
    load(url: string): Observable<import("@angular/core").ComponentFactory<any>>;
    /**
     * This method is used to clear container
     */
    clean(): void;
    /**
     * This method is used to check if current container
     * already contains DOM.
     */
    isloaded(): boolean;
    unload(): void;
    /**
     *
     * @param prop
     * @param value
     */
    setStyle(prop: any, value: any, elem?: any): void;
    /**
     *
     * @param visibility
     */
    setVisible(visibility: boolean): void;
    _spyChanges(event: any): void;
    resetOriginalValue(value: any): void;
    clone(): void;
    validateRestrict(text: any): any;
    validateMaxChar(text: any): any;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
