/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { CommonService } from '../../utils/common.service';
import { SwtModule } from '../../controls/swt-module.component';
//import $ from 'jquery';
/** @type {?} */
var _ = parent;
var JSONViewer = /** @class */ (function (_super) {
    tslib_1.__extends(J<PERSON><PERSON>Viewer, _super);
    // @ViewChild("txtinput") txtinput: SwtTextInput;
    // private textinput: SwtTextInput;
    function JSONViewer(commonService, element) {
        var _this = _super.call(this, element, commonService) || this;
        _this.commonService = commonService;
        _this.element = element;
        return _this;
    }
    Object.defineProperty(JSONViewer.prototype, "code", {
        get: /**
         * @return {?}
         */
        function () {
            return JSON.stringify(this.data, null, 2);
        },
        set: /**
         * @param {?} v
         * @return {?}
         */
        function (v) {
            try {
                this.data = JSON.parse(v);
            }
            catch (e) {
                console.log('error occored while you were typing the JSON');
            }
            ;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    JSONViewer.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.data = this.jsonData;
    };
    /**
     * @return {?}
     */
    JSONViewer.prototype.close = /**
     * @return {?}
     */
    function () {
        this.close();
    };
    JSONViewer.decorators = [
        { type: Component, args: [{
                    selector: 'swt-jsonviewer',
                    template: "<ngx-json-viewer [json]=\"data\" #jsonViewer  id=\"jsonViewer\"  (close)=\"close()\"></ngx-json-viewer>\r\n",
                    providers: [CommonService],
                    styles: [""]
                }] }
    ];
    /** @nocollapse */
    JSONViewer.ctorParameters = function () { return [
        { type: CommonService },
        { type: ElementRef }
    ]; };
    return JSONViewer;
}(SwtModule));
export { JSONViewer };
if (false) {
    /** @type {?} */
    JSONViewer.prototype.data;
    /** @type {?} */
    JSONViewer.prototype.jsonData;
    /** @type {?} */
    JSONViewer.prototype.description;
    /** @type {?} */
    JSONViewer.prototype.message;
    /**
     * @type {?}
     * @private
     */
    JSONViewer.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    JSONViewer.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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