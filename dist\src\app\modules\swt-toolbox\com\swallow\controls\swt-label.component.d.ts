import { OnInit, ElementRef, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class SwtLabel extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    private labelDOM;
    private _text;
    private _color;
    private _truncate;
    private _htmlText;
    private _fontSize;
    private _fontWeight;
    private _width;
    private _buttonMode;
    width: any;
    truncate: any;
    tabIndex: any;
    styleName: string;
    textAlign: string;
    htmlText: string;
    text: string;
    fontSize: string;
    fontWeight: string;
    color: string;
    buttonMode: any;
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    constructor(elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    textDictionaryId: any;
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param event
     */
    defaultOnnClick(event: any): void;
    emtpyOnnClick(event: any): void;
    /**
     * "onClick" function should be executed to prevent the native "Click()" of Angular .
     * @param event
     */
    onClick(event: any): void;
    /**
     * ngOnDestroy
     */
    ngOnDestroy(): void;
}
