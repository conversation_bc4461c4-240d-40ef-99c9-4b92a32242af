/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { SwtLabel } from "./swt-label.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef } from "@angular/core";
;
/** @type {?} */
var $ = require('jquery');
var SwtText = /** @class */ (function (_super) {
    tslib_1.__extends(SwtText, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function SwtText(elem_, commonService_) {
        var _this = _super.call(this, elem_, commonService_) || this;
        _this.elem_ = elem_;
        _this.commonService_ = commonService_;
        return _this;
    }
    SwtText.decorators = [
        { type: Component, args: [{
                    selector: 'SwtText',
                    template: "\n        <span selector=\"SwtText\"\n               class=\"ellipsisEnabled\"\n               #labelDOM>\n        </span>\n  ",
                    styles: ["\n       :host {\n          display: block;\n           outline:none;\n       }\n       span {\n         display: inline-block;\n       }\n      .ellipsisEnabled{\n        vertical-align: bottom;\n        text-overflow: ellipsis; \n        overflow: hidden; \n        white-space: nowrap;\n     }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtText.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return SwtText;
}(SwtLabel));
export { SwtText };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtText.prototype.elem_;
    /**
     * @type {?}
     * @private
     */
    SwtText.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,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