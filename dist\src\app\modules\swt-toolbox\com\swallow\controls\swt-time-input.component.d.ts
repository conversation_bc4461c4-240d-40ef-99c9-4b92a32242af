import { AfterViewInit, ElementRef, EventEmitter, OnInit } from '@angular/core';
export declare class SwtTimeInput implements OnInit, AfterViewInit {
    private elem;
    originalValue: any;
    onSpyChange: EventEmitter<any>;
    onSpyNoChange: EventEmitter<any>;
    id: string;
    toolTip: any;
    KeyDown: EventEmitter<Function>;
    keyUp: EventEmitter<Function>;
    focus: EventEmitter<Function>;
    stepperChange: EventEmitter<Function>;
    creationComplete: EventEmitter<Function>;
    marginTop: string;
    marginRight: string;
    marginBottom: string;
    marginLeft: string;
    private _hours;
    private _minutes;
    private _seconds;
    private _selectedField;
    private _visibility;
    private hours;
    private minutes;
    private seconds;
    private uparrow;
    private downarrow;
    private separatorspan1;
    private separatorspan2;
    private timeInput;
    private firstCallHours;
    private firstCallMin;
    private firstCallSec;
    constructor(elem: ElementRef);
    private _secondEnable;
    secondEnable: boolean;
    private _separator;
    separator: string;
    private _editable;
    editable: boolean;
    private _enabled;
    enabled: boolean;
    visible: boolean;
    time: any;
    hour: number;
    minute: number;
    second: number;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    spyChanges(event: any): void;
    resetOriginalValue(): void;
    private changeStyle;
    /**
     * This method is used to disable timeInput seconds.
     */
    private disableSeconds;
    /**
     * This method is used to enable timeInput seconds.
     */
    private enableSeconds;
    private onHoursKeyDown;
    private onHoursKeyUp;
    private onMinutesKeyDown;
    private onMinutesKeyUp;
    private onSecondsKeyDown;
    private onSecondsKeyUp;
    private onArrowUpClick;
    private onArrowDownClick;
    private loadingZero;
    private increment;
    private decrement;
}
