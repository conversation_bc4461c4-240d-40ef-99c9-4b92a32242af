{"__symbolic": "module", "version": 4, "metadata": {"AnimationDriver": {"__symbolic": "class", "members": {"validateStyleProperty": [{"__symbolic": "method"}], "matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"NOOP": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵNoopAnimationDriver"}}}}, "ɵAnimation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵAnimationDriver"}, {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationMetadata", "line": 20, "character": 55}]}], "buildTimelines": [{"__symbolic": "method"}]}}, "ɵAnimationStyleNormalizer": {"__symbolic": "class", "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵNoopAnimationStyleNormalizer": {"__symbolic": "class", "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵWebAnimationsStyleNormalizer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵAnimationStyleNormalizer"}, "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵAnimationDriver": {"__symbolic": "reference", "name": "AnimationDriver"}, "ɵNoopAnimationDriver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 15, "character": 1}}], "members": {"validateStyleProperty": [{"__symbolic": "method"}], "matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "ɵAnimationEngine": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "ɵAnimationDriver"}, {"__symbolic": "reference", "name": "ɵAnimationStyleNormalizer"}]}], "registerTrigger": [{"__symbolic": "method"}], "register": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "onInsert": [{"__symbolic": "method"}], "onRemove": [{"__symbolic": "method"}], "disableAnimations": [{"__symbolic": "method"}], "process": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}], "flush": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}]}}, "ɵCssKeyframesDriver": {"__symbolic": "class", "members": {"validateStyleProperty": [{"__symbolic": "method"}], "matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "buildKeyframeElement": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}], "_notifyFaultyScrubber": [{"__symbolic": "method"}]}}, "ɵCssKeyframesPlayer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 36, "character": 63, "module": "./src/render/css_keyframes/css_keyframes_player"}]}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 39, "character": 37, "module": "./src/render/css_keyframes/css_keyframes_player"}, {"__symbolic": "reference", "name": "ɵangular_packages_animations_browser_browser_a"}]}], "onStart": [{"__symbolic": "method"}], "onDone": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "_flushDoneFns": [{"__symbolic": "method"}], "_flushStartFns": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "getPosition": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "pause": [{"__symbolic": "method"}], "restart": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "_buildStyler": [{"__symbolic": "method"}], "triggerCallback": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}]}}, "ɵcontainsElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 145, "character": 4, "context": {"name": "_contains"}, "module": "./src/render/shared"}, "ɵinvokeQuery": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 148, "character": 4, "context": {"name": "_query"}, "module": "./src/render/shared"}, "ɵmatchesElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 146, "character": 4, "context": {"name": "_matches"}, "module": "./src/render/shared"}, "ɵvalidateStyleProperty": {"__symbolic": "function"}, "ɵWebAnimationsDriver": {"__symbolic": "class", "members": {"validateStyleProperty": [{"__symbolic": "method"}], "matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "overrideWebAnimationsSupport": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "ɵsupportsWebAnimations": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 75, "character": 9, "module": "./src/render/web_animations/web_animations_driver"}, "right": "function"}}, "ɵWebAnimationsPlayer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 35, "character": 45, "module": "./src/render/web_animations/web_animations_player"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 36, "character": 22, "module": "./src/render/web_animations/web_animations_player"}, {"__symbolic": "reference", "name": "ɵangular_packages_animations_browser_browser_a"}]}], "_onFinish": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "_buildPlayer": [{"__symbolic": "method"}], "_preparePlayerBeforeStart": [{"__symbolic": "method"}], "_triggerWebAnimation": [{"__symbolic": "method"}], "onStart": [{"__symbolic": "method"}], "onDone": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "pause": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "_resetDomPlayerState": [{"__symbolic": "method"}], "restart": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "getPosition": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}], "triggerCallback": [{"__symbolic": "method"}]}}, "ɵallowPreviousPlayerStylesMerge": {"__symbolic": "function", "parameters": ["duration", "delay"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "duration"}, "right": 0}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "delay"}, "right": 0}}}, "ɵangular_packages_animations_browser_browser_a": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 52, "character": 51, "module": "./src/render/special_cased_styles"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 53, "character": 26, "module": "./src/render/special_cased_styles"}]}], "start": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}]}, "statics": {"initialStylesByElement": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "WeakMap"}}}}}, "origins": {"AnimationDriver": "./src/render/animation_driver", "ɵAnimation": "./src/dsl/animation", "ɵAnimationStyleNormalizer": "./src/dsl/style_normalization/animation_style_normalizer", "ɵNoopAnimationStyleNormalizer": "./src/dsl/style_normalization/animation_style_normalizer", "ɵWebAnimationsStyleNormalizer": "./src/dsl/style_normalization/web_animations_style_normalizer", "ɵAnimationDriver": "./src/render/animation_driver", "ɵNoopAnimationDriver": "./src/render/animation_driver", "ɵAnimationEngine": "./src/render/animation_engine_next", "ɵCssKeyframesDriver": "./src/render/css_keyframes/css_keyframes_driver", "ɵCssKeyframesPlayer": "./src/render/css_keyframes/css_keyframes_player", "ɵcontainsElement": "./src/render/shared", "ɵinvokeQuery": "./src/render/shared", "ɵmatchesElement": "./src/render/shared", "ɵvalidateStyleProperty": "./src/render/shared", "ɵWebAnimationsDriver": "./src/render/web_animations/web_animations_driver", "ɵsupportsWebAnimations": "./src/render/web_animations/web_animations_driver", "ɵWebAnimationsPlayer": "./src/render/web_animations/web_animations_player", "ɵallowPreviousPlayerStylesMerge": "./src/util", "ɵangular_packages_animations_browser_browser_a": "./src/render/special_cased_styles"}, "importAs": "@angular/animations/browser"}