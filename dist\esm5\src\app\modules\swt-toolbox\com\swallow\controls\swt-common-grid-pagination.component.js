/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
import { Logger } from '../logging/logger.service';
import { HttpClient } from '@angular/common/http';
/** @type {?} */
var $ = require('jquery');
/**
 * Custom pagination component: It allows editing the page number manually
 *  << < Page [1] of 5 > >>
 *
 * <AUTHOR> Chebka, saber.chebka\@gmail.com
 */
var SwtCommonGridPagination = /** @class */ (function () {
    function SwtCommonGridPagination(httpClient, elem) {
        this.httpClient = httpClient;
        this.elem = elem;
        this.logger = null;
        this.maximum = 0;
        this.minimum_ = 0;
        this.value = 0;
        this.horizontalAlign = "right";
        this.totalItems = 0;
        this.processing = false;
        this.styleObject = [];
        // Reference to the real pagination component
        this.realPagination = true;
        // -------------------------------------------------------------------------------------------------------------------- //
        this._enabled = true;
        this.logger = new Logger('Grid-pagination', httpClient, 0);
        this.logger.info('method [constructor] - START/END');
    }
    Object.defineProperty(SwtCommonGridPagination.prototype, "gridPaginationOptions", {
        get: /**
         * @return {?}
         */
        function () {
            return this._gridPaginationOptions;
        },
        set: /**
         * @param {?} gridPaginationOptions
         * @return {?}
         */
        function (gridPaginationOptions) {
            this.logger.info('method [set gridPaginationOptions] - START');
            this._gridPaginationOptions = gridPaginationOptions;
            // The backendServiceApi is itself the SwtCommonGridComponent (This is a hack)
            //        this.commonGrid = <SwtCommonGrid>this.gridPaginationOptions.backendServiceApi.service;
            this.commonGrid = (/** @type {?} */ (this.gridPaginationOptions['grid']));
            this.logger.info('method [set gridPaginationOptions] - END');
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtCommonGridPagination.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.logger.info('init: ');
    };
    /**
     * @return {?}
     */
    SwtCommonGridPagination.prototype.ngAfterContentChecked = /**
     * @return {?}
     */
    function () {
        this.styleObject = {
            'float': this.horizontalAlign,
            'margin-left': this.marginLeft,
            'margin-right': this.marginRight,
        };
    };
    Object.defineProperty(SwtCommonGridPagination.prototype, "minimum", {
        get: /**
         * @return {?}
         */
        function () {
            return this.minimum_;
        },
        set: /**
         * @param {?} $v
         * @return {?}
         */
        function ($v) {
            this.minimum_ = $v;
            if (!this.value)
                this.value = this.minimum_;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonGridPagination.prototype.changeToFirstPage = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info('method [changeToFirstPage] - START/END');
        this.value = 1;
        this.onPageChanged(event, this.value);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonGridPagination.prototype.changeToLastPage = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info('method [changeToLastPage] - START/END');
        this.value = this.maximum;
        this.onPageChanged(event, this.value);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonGridPagination.prototype.changeToNextPage = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info('method [changeToNextPage] - START/END');
        if (this.value < this.maximum) {
            this.value++;
            this.onPageChanged(event, this.value);
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonGridPagination.prototype.changeToPreviousPage = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info('method [changeToNextPage] - START/END');
        if (this.value > 1) {
            this.value--;
            this.onPageChanged(event, this.value);
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtCommonGridPagination.prototype.changeToCurrentPage = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info('method [changeToCurrentPage] - START -  value', event.currentTarget.value);
        if (Number(event.currentTarget.value) < 1) {
            event.currentTarget.value = 1;
        }
        else if (Number(event.currentTarget.value) > Number(this.maximum)) {
            event.currentTarget.value = this.maximum;
        }
        else {
            this.value = event.currentTarget.value;
        }
        this.value = event.currentTarget.value;
        this.onPageChanged(event, this.value);
        this.logger.info('method [changeToCurrentPage] -  END - current page :', this.value);
    };
    /**
     * @param {?=} event
     * @param {?=} pageNumber
     * @return {?}
     */
    SwtCommonGridPagination.prototype.onPageChanged = /**
     * @param {?=} event
     * @param {?=} pageNumber
     * @return {?}
     */
    function (event, pageNumber) {
        this.logger.info('method [onPageChanged] - START/END', this.commonGrid);
        this.commonGrid.processOnPaginationChanged(event, { newPage: pageNumber, pageSize: -1 });
        this.commonGrid.selectedIndex = -1;
    };
    Object.defineProperty(SwtCommonGridPagination.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (typeof (value) === 'string') {
                value === 'true' ? this._enabled = true : null;
                value === 'false' ? this._enabled = false : null;
                if (value == 'false') {
                    $($(this.elem.nativeElement)[0].children[0]).addClass("disabled-container");
                }
                else {
                    $($(this.elem.nativeElement)[0].children[0]).removeClass("disabled-container");
                }
            }
            else {
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    if (value == false) {
                        _this._enabled = false;
                        $($(_this.elem.nativeElement)[0].children[0]).addClass("disabled-container");
                    }
                    else {
                        _this._enabled = true;
                        $($(_this.elem.nativeElement)[0].children[0]).removeClass("disabled-container");
                    }
                }), 0);
            }
        },
        enumerable: true,
        configurable: true
    });
    SwtCommonGridPagination.decorators = [
        { type: Component, args: [{
                    selector: 'SwtCommonGridPagination',
                    template: "\n        <div class=\"slick-pagination\">\n            <div class=\"slick-pagination-nav\"  [ngStyle]=\"styleObject\" >\n                <nav aria-label=\"Page navigation\">\n                    <ul class=\"pagination\">\n                        <li class=\"page-item\" [ngClass]=\"value <= 1 ? 'disabled' : ''\">\n                            <a class=\"page-link icon-seek-first fa fa-angle-double-left\"\n                               aria-label=\"First\" (click)=\"changeToFirstPage($event)\"> </a>\n                        </li>\n                        <li class=\"page-item\" [ngClass]=\"value <= 1 ? 'disabled' : ''\">\n                            <a class=\"page-link icon-seek-prev fa fa-angle-left\"\n                               aria-label=\"Previous\" (click)=\"changeToPreviousPage($event)\"> </a>\n                        </li>\n                    </ul>\n                </nav>\n\n                <div class=\"slick-page-number\">\n                    <input  type=\"number\" value=\"{{value}}\" size=\"1\" (keyup.enter)=\"changeToCurrentPage($event)\" (change)=\"changeToCurrentPage($event)\" >\n                    <span style=\"padding-left: 2px\" [translate]=\"'OF'\"></span><span> {{maximum}}</span>\n                </div>\n\n                <nav aria-label=\"Page navigation\">\n                    <ul class=\"pagination\">\n                        <li class=\"page-item CGP-next-page\"\n                            [ngClass]=\"value === maximum ? 'disabled' : ''\"><a\n                                class=\"page-link icon-seek-next text-center fa fa-lg fa-angle-right\"\n                                aria-label=\"Next\" (click)=\"changeToNextPage($event)\"> </a></li>\n                        <li class=\"page-item CGP-end-page\"\n                            [ngClass]=\"value === maximum ? 'disabled' : ''\"><a\n                                class=\"page-link icon-seek-end fa fa-lg fa-angle-double-right\"\n                                aria-label=\"Last\" (click)=\"changeToLastPage($event)\"> </a></li>\n                    </ul>\n                </nav>\n            </div>\n        </div>\n    ",
                    styles: ["\n        .slick-pagination {\n            padding: 0px !important;\n            height: 24px;\n            width:auto;\n            padding: 2px;\n            border-radius: 5px;\n            margin: 0 5px 0 5px;\n        }\n        .slick-page-number {\n            border: 1px solid #ADCCE3;\n            height:23px;\n            line-height:23px;\n            color:black;\n        }\n        .slick-pagination .slick-pagination-nav {\n            padding: 0px !important;\n            height: 24px!important;\n        }\n        .page-spin {\n            border: none;\n            height: 23px;\n            width: 23px;\n            background-color: transparent;\n            cursor: default;\n            animation: fa-spin 1.2s infinite linear !important;\n        }\n        .page-spin:hover {\n            background-color: transparent;\n        }\n        .slick-pagination .slick-pagination-nav .pagination .page-link {\n            height: 23px;\n            padding-right: 6px; \n            height: 23px; \n            padding-top: 3px; \n            padding-bottom: 3px;\n        }\n        .pagination>li>a, .pagination>li>span {\n            padding: 3px 6px;\n            border: none;\n            border-radius: 0px;\n            background-image: -webkit-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -moz-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -ms-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -o-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: linear-gradient(to bottom, #E3F5FF, #ADCCE3);\n            cursor: default;\n            border: 1px solid #ADCCE3;\n            color: #173553;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number input {\n            background-color: white;\n            height: 21px;\n            width: 34px;\n            padding: 2px;\n            display: inline-block;\n            text-align: center;\n            border-top:1px solid  #4C5E6F;\n            margin-top: 2px;\n            margin-bottom: 2px;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number input:focus {\n            outline: none;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number {\n            vertical-align: top;\n            margin-top: 0px;\n            display: inline-block;\n            padding: 0 5px;\n        }\n        .slick-page-number{\n            line-height : 0px!important;\n            border: none;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    SwtCommonGridPagination.ctorParameters = function () { return [
        { type: HttpClient },
        { type: ElementRef }
    ]; };
    SwtCommonGridPagination.propDecorators = {
        maximum: [{ type: Input, args: ['maximum',] }],
        minimum_: [{ type: Input, args: ['minimum',] }],
        value: [{ type: Input, args: ['value',] }],
        horizontalAlign: [{ type: Input }],
        marginLeft: [{ type: Input }],
        marginRight: [{ type: Input }],
        gridPaginationOptions: [{ type: Input }],
        enabled: [{ type: Input }]
    };
    return SwtCommonGridPagination;
}());
export { SwtCommonGridPagination };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.logger;
    /** @type {?} */
    SwtCommonGridPagination.prototype.maximum;
    /** @type {?} */
    SwtCommonGridPagination.prototype.minimum_;
    /** @type {?} */
    SwtCommonGridPagination.prototype.value;
    /** @type {?} */
    SwtCommonGridPagination.prototype.horizontalAlign;
    /** @type {?} */
    SwtCommonGridPagination.prototype.marginLeft;
    /** @type {?} */
    SwtCommonGridPagination.prototype.marginRight;
    /** @type {?} */
    SwtCommonGridPagination.prototype.totalItems;
    /** @type {?} */
    SwtCommonGridPagination.prototype.processing;
    /** @type {?} */
    SwtCommonGridPagination.prototype.styleObject;
    /** @type {?} */
    SwtCommonGridPagination.prototype.realPagination;
    /** @type {?} */
    SwtCommonGridPagination.prototype._gridPaginationOptions;
    /** @type {?} */
    SwtCommonGridPagination.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.httpClient;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.elem;
}
//# sourceMappingURL=data:application/json;base64,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