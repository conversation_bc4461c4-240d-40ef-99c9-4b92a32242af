{"__symbolic": "module", "version": 4, "metadata": {"ɵa": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 16, "character": 1}, "arguments": [{"selector": "[<PERSON><PERSON><PERSON><PERSON>]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 20, "character": 41}]}]}}, "ModuleEvent": {"__symbolic": "class", "members": {}, "statics": {"READY": "ready", "PROGRESS": "progress", "ERROR": "error", "SETUP": "setup", "UNLOAD": "unload", "DISPOSE": "dispose"}}, "TitleWindow": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 24, "character": 1}, "arguments": [{"selector": "TitleWindow", "template": "\n        <div class=\"window-overlay\" [ngClass]=\"{'hidden': !visible }\">\n            <div (mousedown)=\"onTitleBarClick()\" class=\"window-container\"  [style.width.px]=\"width\"\n                 [style.height.px]=\"height\"\n                 [style.top.px]=\"this.position.y\" [style.left.px]=\"this.position.x\">\n                <div (dblclick)=\"minimizeWindow()\" class=\"window-heading\" [hidden]=\"!showHeader\">\n                    <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n                        <tr width=\"100%\">\n                            <td style=\"padding-left: 5px; border: none !important\">{{ title }}</td>\n                            <td style=\"border: none !important\" width=\"50\" align=\"right\" *ngIf=\"showControls\">\n                                <img [src]=\"minimizeIcon\" (click)=\"minimizeWindow()\">\n                                <img (click)=\"close()\" class=\"closebtn\" src=\"assets/images/closeButton.png\">\n                            </td>\n                        </tr>\n                    </table>\n                </div>\n                <div  [class.window-body]=\"showHeader\"\n                      [class.window-bodyFull]=\"!showHeader\"  >\n                    <ng-template winHandler></ng-template>\n                    <ng-template #mloaderOutlet></ng-template>\n                </div>\n            </div>\n        </div>\n    ", "styles": ["\n        .window-overlay {\n            width: 100%;\n            height: 100%;\n            background-color: rgba(255, 255, 255, .2);\n            position: fixed;\n            top: 0;\n            left: 0;\n        }\n\n        .window-container {\n            position: fixed;\n            left: 30%;\n            top: 20%;\n            width: auto;\n            height: auto;\n            border-top: none;\n            border-bottom: 8px solid #369;\n            border-right: 8px solid #369;\n            border-left: 8px solid #369;\n            border-radius: 5px;\n            background-color: #ccecff;\n            min-width: 55px;\n            min-height: 29px;\n            box-sizing: border-box;\n        }\n\n        .window-heading {\n            width: 100%;\n            height: 29px;\n            background-color: #369;\n            color: #FFF;\n            line-height: 30px;\n            padding: 0px;\n            box-sizing: border-box;\n            cursor: default;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            font-size: 11px;\n            font-family: verdana, halvatica, sans-serif;\n            font-weight: bolder;\n        }\n\n        .window-body {\n            width: 100%;\n            height: calc(100% - 30px);\n            overflow: auto;\n        }\n        .window-bodyFull {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n        }\n\n        img:hover {\n            cursor: pointer;\n        }\n\n        .closebtn {\n            margin: -2px 0px 0px 5px;\n        }\n\n    "]}]}], "members": {"contentHolder": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 137, "character": 5}, "arguments": [{"__symbolic": "reference", "name": "ɵa"}]}]}], "mloaderOutlet": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 138, "character": 5}, "arguments": ["mloaderOutlet", {"read": {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 138, "character": 39}}]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 147, "character": 33}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "addChild": [{"__symbolic": "method"}], "display": [{"__symbolic": "method"}], "onTitleBarClick": [{"__symbolic": "method"}], "validateNow": [{"__symbolic": "method"}], "getChild": [{"__symbolic": "method"}], "includeContent": [{"__symbolic": "method"}], "minimizeWindow": [{"__symbolic": "method"}], "setBounds": [{"__symbolic": "method"}], "setWindowXY": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "isMinimized": [{"__symbolic": "method"}], "close": [{"__symbolic": "method"}], "getUrlQuery": [{"__symbolic": "method"}], "loadComponent": [{"__symbolic": "method"}], "mapDataObject": [{"__symbolic": "method"}]}}, "SwtToolBoxModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 109, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule", "line": 111, "character": 8}, {"__symbolic": "reference", "module": "@angular/forms", "name": "ReactiveFormsModule", "line": 111, "character": 22}, {"__symbolic": "reference", "module": "ngx-bootstrap", "name": "ButtonsModule", "line": 112, "character": 8}, {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "ngx-bootstrap", "name": "TooltipModule", "line": 112, "character": 23}, "member": "forRoot"}}, {"__symbolic": "reference", "module": "ng-select", "name": "SelectModule", "line": 113, "character": 8}, {"__symbolic": "reference", "module": "@angular/forms", "name": "FormsModule", "line": 113, "character": 22}, {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AngularSlickgridModule", "line": 114, "character": 8}, "member": "forRoot"}}, {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateModule", "line": 115, "character": 8}, "member": "forRoot"}}, {"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClientModule", "line": 116, "character": 8}, {"__symbolic": "reference", "module": "@angular/http", "name": "HttpModule", "line": 117, "character": 8}, {"__symbolic": "reference", "module": "@ctrl/ngx-codemirror", "name": "CodemirrorModule", "line": 118, "character": 8}, {"__symbolic": "reference", "module": "ng2-file-upload", "name": "FileUploadModule", "line": 119, "character": 8}, {"__symbolic": "reference", "module": "@angular/flex-layout", "name": "FlexLayoutModule", "line": 120, "character": 8}, {"__symbolic": "reference", "module": "ngx-json-viewer-scrolling", "name": "NgxJsonViewerModule", "line": 121, "character": 8}, {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "ngx-popper", "name": "NgxPopperModule", "line": 122, "character": 8}, "member": "forRoot"}, "arguments": [{}]}, {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "ng-multiselect-dropdown-angular7", "name": "NgMultiSelectDropDownModule", "line": 123, "character": 8}, "member": "forRoot"}}], "declarations": [{"__symbolic": "reference", "name": "SwtCheckBox"}, {"__symbolic": "reference", "name": "SwtLabel"}, {"__symbolic": "reference", "name": "HBox"}, {"__symbolic": "reference", "name": "VBox"}, {"__symbolic": "reference", "name": "HDividedBox"}, {"__symbolic": "reference", "name": "SwtButton"}, {"__symbolic": "reference", "name": "SwtCanvas"}, {"__symbolic": "reference", "name": "SwtComboBox"}, {"__symbolic": "reference", "name": "ɵf"}, {"__symbolic": "reference", "name": "SwtDataExport"}, {"__symbolic": "reference", "name": "DataExportMultiPage"}, {"__symbolic": "reference", "name": "SwtDateField"}, {"__symbolic": "reference", "name": "SwtHelpButton"}, {"__symbolic": "reference", "name": "SwtNumericInput"}, {"__symbolic": "reference", "name": "SwtAdvSlider"}, {"__symbolic": "reference", "name": "SwtEditableComboBox"}, {"__symbolic": "reference", "name": "AssetsLegendItem"}, {"__symbolic": "reference", "name": "ConfigurableToolTip"}, {"__symbolic": "reference", "name": "AssetsLegend"}, {"__symbolic": "reference", "name": "SwtFieldSet"}, {"__symbolic": "reference", "name": "CheckBoxLegend"}, {"__symbolic": "reference", "name": "ILMSeriesLiveValue"}, {"__symbolic": "reference", "name": "ProcessStatusBox"}, {"__symbolic": "reference", "name": "CheckBoxLegendItem"}, {"__symbolic": "reference", "name": "SwtPanel"}, {"__symbolic": "reference", "name": "SwtRadioItem"}, {"__symbolic": "reference", "name": "SwtRadioButtonGroup"}, {"__symbolic": "reference", "name": "VDividedBox"}, {"__symbolic": "reference", "name": "SwtStepper"}, {"__symbolic": "reference", "name": "SwtTimeInput"}, {"__symbolic": "reference", "name": "SwtTextInput"}, {"__symbolic": "reference", "name": "SwtCommonGrid"}, {"__symbolic": "reference", "name": "SwtCommonGridPagination"}, {"__symbolic": "reference", "name": "SwtTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedCommonGrid"}, {"__symbolic": "reference", "name": "SwtTreeCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtScreen"}, {"__symbolic": "reference", "name": "SwtLoadingImage"}, {"__symbolic": "reference", "name": "<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "SwtTextArea"}, {"__symbolic": "reference", "name": "SwtPrettyPrintTextArea"}, {"__symbolic": "reference", "name": "SwtModule"}, {"__symbolic": "reference", "name": "SwtText"}, {"__symbolic": "reference", "name": "LinkButton"}, {"__symbolic": "reference", "name": "SwtList"}, {"__symbolic": "reference", "name": "SwtRichTextEditor"}, {"__symbolic": "reference", "name": "SwtTabNavigator"}, {"__symbolic": "reference", "name": "TabPushStategy"}, {"__symbolic": "reference", "name": "Tab"}, {"__symbolic": "reference", "name": "Spacer"}, {"__symbolic": "reference", "name": "Grid"}, {"__symbolic": "reference", "name": "GridRow"}, {"__symbolic": "reference", "name": "GridItem"}, {"__symbolic": "reference", "name": "Container"}, {"__symbolic": "reference", "name": "CustomTree"}, {"__symbolic": "reference", "name": "ILMTreeIndeterminate"}, {"__symbolic": "reference", "name": "SwtProgressBar"}, {"__symbolic": "reference", "name": "ContextMenu"}, {"__symbolic": "reference", "name": "ExportInProgress"}, {"__symbolic": "reference", "name": "SwtPagesToExport"}, {"__symbolic": "reference", "name": "SwtDOMManager"}, {"__symbolic": "reference", "name": "SwtImage"}, {"__symbolic": "reference", "name": "SwtCommonModule"}, {"__symbolic": "reference", "name": "SwtPasswordMeter"}, {"__symbolic": "reference", "name": "HRule"}, {"__symbolic": "reference", "name": "VRule"}, {"__symbolic": "reference", "name": "SwtAlert"}, {"__symbolic": "reference", "name": "FileUpload"}, {"__symbolic": "reference", "name": "TitleWindow"}, {"__symbolic": "reference", "name": "AdvancedDataGrid"}, {"__symbolic": "reference", "name": "LinkItemRander"}, {"__symbolic": "reference", "name": "ɵg"}, {"__symbolic": "reference", "name": "ɵh"}, {"__symbolic": "reference", "name": "ɵa"}, {"__symbolic": "reference", "name": "SwtSlider"}, {"__symbolic": "reference", "name": "JSONViewer"}, {"__symbolic": "reference", "name": "ɵi"}, {"__symbolic": "reference", "name": "SwtSummary"}, {"__symbolic": "reference", "name": "EnhancedAlertingTooltip"}, {"__symbolic": "reference", "name": "ILMLineChart"}, {"__symbolic": "reference", "name": "ɵj"}, {"__symbolic": "reference", "name": "SwtMultiselectCombobox"}], "exports": [{"__symbolic": "reference", "name": "SwtCheckBox"}, {"__symbolic": "reference", "name": "SwtLabel"}, {"__symbolic": "reference", "name": "HBox"}, {"__symbolic": "reference", "name": "VBox"}, {"__symbolic": "reference", "name": "HDividedBox"}, {"__symbolic": "reference", "name": "SwtButton"}, {"__symbolic": "reference", "name": "SwtCanvas"}, {"__symbolic": "reference", "name": "SwtComboBox"}, {"__symbolic": "reference", "name": "ɵf"}, {"__symbolic": "reference", "name": "SwtDataExport"}, {"__symbolic": "reference", "name": "DataExportMultiPage"}, {"__symbolic": "reference", "name": "SwtDateField"}, {"__symbolic": "reference", "name": "SwtHelpButton"}, {"__symbolic": "reference", "name": "SwtNumericInput"}, {"__symbolic": "reference", "name": "SwtAdvSlider"}, {"__symbolic": "reference", "name": "SwtEditableComboBox"}, {"__symbolic": "reference", "name": "ILMSeriesLiveValue"}, {"__symbolic": "reference", "name": "ProcessStatusBox"}, {"__symbolic": "reference", "name": "CheckBoxLegendItem"}, {"__symbolic": "reference", "name": "SwtSlider"}, {"__symbolic": "reference", "name": "AssetsLegendItem"}, {"__symbolic": "reference", "name": "ConfigurableToolTip"}, {"__symbolic": "reference", "name": "AssetsLegend"}, {"__symbolic": "reference", "name": "SwtFieldSet"}, {"__symbolic": "reference", "name": "CheckBoxLegend"}, {"__symbolic": "reference", "name": "SwtPanel"}, {"__symbolic": "reference", "name": "SwtRadioItem"}, {"__symbolic": "reference", "name": "SwtRadioButtonGroup"}, {"__symbolic": "reference", "name": "VDividedBox"}, {"__symbolic": "reference", "name": "SwtStepper"}, {"__symbolic": "reference", "name": "SwtTimeInput"}, {"__symbolic": "reference", "name": "SwtTextInput"}, {"__symbolic": "reference", "name": "SwtCommonGrid"}, {"__symbolic": "reference", "name": "SwtCommonGridPagination"}, {"__symbolic": "reference", "name": "SwtTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedCommonGrid"}, {"__symbolic": "reference", "name": "SwtTreeCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtScreen"}, {"__symbolic": "reference", "name": "SwtLoadingImage"}, {"__symbolic": "reference", "name": "<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "SwtTextArea"}, {"__symbolic": "reference", "name": "SwtModule"}, {"__symbolic": "reference", "name": "SwtPrettyPrintTextArea"}, {"__symbolic": "reference", "name": "SwtText"}, {"__symbolic": "reference", "name": "LinkButton"}, {"__symbolic": "reference", "name": "SwtList"}, {"__symbolic": "reference", "name": "SwtRichTextEditor"}, {"__symbolic": "reference", "name": "SwtTabNavigator"}, {"__symbolic": "reference", "name": "TabPushStategy"}, {"__symbolic": "reference", "name": "Tab"}, {"__symbolic": "reference", "name": "Spacer"}, {"__symbolic": "reference", "name": "Grid"}, {"__symbolic": "reference", "name": "GridRow"}, {"__symbolic": "reference", "name": "GridItem"}, {"__symbolic": "reference", "name": "Container"}, {"__symbolic": "reference", "name": "CustomTree"}, {"__symbolic": "reference", "name": "ILMTreeIndeterminate"}, {"__symbolic": "reference", "name": "SwtProgressBar"}, {"__symbolic": "reference", "name": "ContextMenu"}, {"__symbolic": "reference", "name": "ExportInProgress"}, {"__symbolic": "reference", "name": "SwtPagesToExport"}, {"__symbolic": "reference", "name": "SwtDOMManager"}, {"__symbolic": "reference", "name": "SwtImage"}, {"__symbolic": "reference", "name": "SwtCommonModule"}, {"__symbolic": "reference", "name": "SwtPasswordMeter"}, {"__symbolic": "reference", "name": "HRule"}, {"__symbolic": "reference", "name": "VRule"}, {"__symbolic": "reference", "name": "SwtAlert"}, {"__symbolic": "reference", "name": "FileUpload"}, {"__symbolic": "reference", "name": "TitleWindow"}, {"__symbolic": "reference", "name": "AdvancedDataGrid"}, {"__symbolic": "reference", "name": "LinkItemRander"}, {"__symbolic": "reference", "name": "ɵg"}, {"__symbolic": "reference", "name": "ɵh"}, {"__symbolic": "reference", "name": "ɵa"}, {"__symbolic": "reference", "name": "JSONViewer"}, {"__symbolic": "reference", "name": "ɵi"}, {"__symbolic": "reference", "name": "SwtSummary"}, {"__symbolic": "reference", "name": "EnhancedAlertingTooltip"}, {"__symbolic": "reference", "name": "ILMLineChart"}, {"__symbolic": "reference", "name": "ɵj"}, {"__symbolic": "reference", "name": "SwtMultiselectCombobox"}], "entryComponents": [{"__symbolic": "reference", "name": "SwtCheckBox"}, {"__symbolic": "reference", "name": "SwtLabel"}, {"__symbolic": "reference", "name": "HBox"}, {"__symbolic": "reference", "name": "VBox"}, {"__symbolic": "reference", "name": "HDividedBox"}, {"__symbolic": "reference", "name": "SwtButton"}, {"__symbolic": "reference", "name": "SwtCanvas"}, {"__symbolic": "reference", "name": "SwtComboBox"}, {"__symbolic": "reference", "name": "ɵf"}, {"__symbolic": "reference", "name": "SwtDataExport"}, {"__symbolic": "reference", "name": "DataExportMultiPage"}, {"__symbolic": "reference", "name": "SwtDateField"}, {"__symbolic": "reference", "name": "SwtHelpButton"}, {"__symbolic": "reference", "name": "SwtNumericInput"}, {"__symbolic": "reference", "name": "SwtAdvSlider"}, {"__symbolic": "reference", "name": "SwtEditableComboBox"}, {"__symbolic": "reference", "name": "ILMSeriesLiveValue"}, {"__symbolic": "reference", "name": "ProcessStatusBox"}, {"__symbolic": "reference", "name": "CheckBoxLegendItem"}, {"__symbolic": "reference", "name": "AssetsLegendItem"}, {"__symbolic": "reference", "name": "ConfigurableToolTip"}, {"__symbolic": "reference", "name": "AssetsLegend"}, {"__symbolic": "reference", "name": "SwtFieldSet"}, {"__symbolic": "reference", "name": "CheckBoxLegend"}, {"__symbolic": "reference", "name": "SwtSlider"}, {"__symbolic": "reference", "name": "SwtPanel"}, {"__symbolic": "reference", "name": "SwtRadioItem"}, {"__symbolic": "reference", "name": "SwtRadioButtonGroup"}, {"__symbolic": "reference", "name": "VDividedBox"}, {"__symbolic": "reference", "name": "SwtStepper"}, {"__symbolic": "reference", "name": "SwtTimeInput"}, {"__symbolic": "reference", "name": "SwtTextInput"}, {"__symbolic": "reference", "name": "SwtCommonGrid"}, {"__symbolic": "reference", "name": "SwtCommonGridPagination"}, {"__symbolic": "reference", "name": "SwtTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedCommonGrid"}, {"__symbolic": "reference", "name": "SwtTreeCommonGrid"}, {"__symbolic": "reference", "name": "SwtGroupedTotalCommonGrid"}, {"__symbolic": "reference", "name": "SwtScreen"}, {"__symbolic": "reference", "name": "SwtLoadingImage"}, {"__symbolic": "reference", "name": "<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "SwtTextArea"}, {"__symbolic": "reference", "name": "SwtPrettyPrintTextArea"}, {"__symbolic": "reference", "name": "SwtModule"}, {"__symbolic": "reference", "name": "SwtText"}, {"__symbolic": "reference", "name": "LinkButton"}, {"__symbolic": "reference", "name": "SwtList"}, {"__symbolic": "reference", "name": "SwtRichTextEditor"}, {"__symbolic": "reference", "name": "SwtTabNavigator"}, {"__symbolic": "reference", "name": "TabPushStategy"}, {"__symbolic": "reference", "name": "Tab"}, {"__symbolic": "reference", "name": "Spacer"}, {"__symbolic": "reference", "name": "Grid"}, {"__symbolic": "reference", "name": "GridRow"}, {"__symbolic": "reference", "name": "GridItem"}, {"__symbolic": "reference", "name": "Container"}, {"__symbolic": "reference", "name": "CustomTree"}, {"__symbolic": "reference", "name": "ILMTreeIndeterminate"}, {"__symbolic": "reference", "name": "SwtProgressBar"}, {"__symbolic": "reference", "name": "ContextMenu"}, {"__symbolic": "reference", "name": "ExportInProgress"}, {"__symbolic": "reference", "name": "SwtPagesToExport"}, {"__symbolic": "reference", "name": "SwtImage"}, {"__symbolic": "reference", "name": "SwtCommonModule"}, {"__symbolic": "reference", "name": "SwtPasswordMeter"}, {"__symbolic": "reference", "name": "HRule"}, {"__symbolic": "reference", "name": "VRule"}, {"__symbolic": "reference", "name": "SwtAlert"}, {"__symbolic": "reference", "name": "FileUpload"}, {"__symbolic": "reference", "name": "TitleWindow"}, {"__symbolic": "reference", "name": "AdvancedDataGrid"}, {"__symbolic": "reference", "name": "LinkItemRander"}, {"__symbolic": "reference", "name": "ɵg"}, {"__symbolic": "reference", "name": "ɵh"}, {"__symbolic": "reference", "name": "JSONViewer"}, {"__symbolic": "reference", "name": "ɵi"}, {"__symbolic": "reference", "name": "SwtSummary"}, {"__symbolic": "reference", "name": "EnhancedAlertingTooltip"}, {"__symbolic": "reference", "name": "ILMLineChart"}, {"__symbolic": "reference", "name": "ɵj"}, {"__symbolic": "reference", "name": "SwtMultiselectCombobox"}], "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/common/http", "name": "HTTP_INTERCEPTORS", "line": 377, "character": 20}, "useClass": {"__symbolic": "reference", "name": "SwtHttpInterceptor"}, "multi": true}], "schemas": [{"__symbolic": "reference", "module": "@angular/core", "name": "CUSTOM_ELEMENTS_SCHEMA", "line": 382, "character": 17}]}]}], "members": {}}, "Timer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}], "start": [{"__symbolic": "method"}], "stop": [{"__symbolic": "method"}], "delay": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}]}}, "SwtHttpInterceptor": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 4, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "intercept": [{"__symbolic": "method"}]}}, "Encryptor": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 14, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"hash": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "ts-md5/dist/md5", "name": "Md5", "line": 25, "character": 15}, "member": "hashStr"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}, "member": "toString"}}}}}, "VRule": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "VRule", "template": "\n    <div class=\"v-rule\"></div>\n  ", "styles": ["\n       .v-rule {\n           background-color: #000;\n           height: 100%;\n           width:1px;\n           box-shadow: -1px 0px 0px #fff;\n       }\n  "]}]}], "members": {"strokeColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 30, "character": 5}}]}], "shadowColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 38, "character": 5}}]}], "themeColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 46, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 59, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "HRule": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "HRule", "template": "\n    <div class=\"h-rule\"></div>\n  ", "styles": ["\n       .h-rule {\n           background-color: #000;\n           height: 1px;\n           width:100%;\n           box-shadow: 0px 1px 0px #fff;\n       }\n  "]}]}], "members": {"strokeColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 30, "character": 5}}]}], "shadowColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 38, "character": 5}}]}], "themeColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 46, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 59, "character": 29}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "SwtPasswordMeter": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtPasswordMeter", "template": "\n    <div #passMetterObject class=\"password-meter-progressBar\">\n      <div class=\"password-meter-progress\"></div>\n      <span id=\"label\"></span>\n    </div>\n  ", "styles": ["\n           .password-meter-progressBar {\n              width: 100%;\n              height: 9px;\n              margin-left:5px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#F5F8FA), to(#A9D2E4));\n              background-image: -webkit-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -moz-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -ms-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: -o-linear-gradient(top, #F5F8FA, #A9D2E4);\n              background-image: linear-gradient(to bottom, #F5F8FA, #A9D2E4);\n              border-top: 1px solid #6B6B6B;\n              border-left: 1px solid #818181;\n              border-right: 1px solid #7D7D7D;\n              border-bottom: 1px solid #949494;\n            }\n            span {\n                display: block;\n                position: relative;\n                top:-9px;\n                width:100%;\n                text-align: center;\n                color: #000;\n                font-size: 9px;\n                font-weight: bold;\n                font-family: verdana,helvetica;\n            }\n            .password-meter-progress {\n              width: 0%;\n              height: 100%;\n              border-radius: 0px;\n            }\n  "]}]}], "members": {"passMetterObject": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 90, "character": 5}, "arguments": ["passMetterObject"]}]}], "__ctor__": [{"__symbolic": "constructor"}], "ngOnInit": [{"__symbolic": "method"}], "calcuateStrength": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "setProgress": [{"__symbolic": "method"}], "clearProgress": [{"__symbolic": "method"}], "onValueChange": [{"__symbolic": "method"}], "getStrengthLabel": [{"__symbolic": "method"}], "target": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 332, "character": 3}}]}], "labelFunction": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 427, "character": 3}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 441, "character": 3}}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 457, "character": 3}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 472, "character": 3}}]}], "includeInLayout": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 515, "character": 3}}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 529, "character": 3}}]}]}}, "SwtCommonModule": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 32, "character": 1}, "arguments": [{"selector": "SwtCommonModule", "template": "\n        <SwtModule (creationComplete)=\"onCommonModuleInit($event)\" (preinitialize)=\"onCommonModuleBeforeInit($event)\"\n                   width=\"{{ _width }}\" height=\"{{ _height }}\">\n            <div #commonTemplate [style.width.px]=\"_width\" [style.height.px]=\"_height\">\n                <ng-content></ng-content>\n            </div>\n            <div #buttonsTemplate class=\"swtmodule\" [style.width.px]=\"_width\" [style.height.px]=\"_height\">\n                <SwtCanvas #canvasContainer width=\"100%\" height=\"90%\" paddingLeft=\"10\" paddingRight=\"10\" paddingTop=\"10\">\n\n                    <SwtCanvas #customGrid width=\"100%\" height=\"87%\">\n                        <ng-content select=\".commonModule\"></ng-content>\n                    </SwtCanvas>\n                    <SwtCanvas width=\"100%\" marginTop=\"10\">\n                        <HBox width=\"100%\" height=\"100%\">\n                            <HBox #hboxButtons width=\"100%\"></HBox>\n                            <HBox #hboxExport horizontalAlign=\"right\">\n                                <SwtLoadingImage #loadingImage id=\"loadingImage\"></SwtLoadingImage>\n                                <SwtButton #settingButton\n                                           buttonMode=\"true\"\n                                           styleName=\"reportIcon\"\n                                           tabIndex=\"11\"\n                                           id=\"settingButton\"\n                                           (click)=\"getSetting()\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                           toolTip=\"{{ getCommonMessages('button.report_setting') }}\"></SwtButton>\n                                <SwtButton #csv\n                                           buttonMode=\"true\"\n                                           tabIndex=\"12\"\n                                           id=\"csv\"\n                                           enabled=\"true\"\n                                           styleName=\"csvIcon\"\n                                           (click)=\"report('csv')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.csv') }}\"></SwtButton>\n                                <SwtButton #excel\n                                           buttonMode=\"true\"\n                                           tabIndex=\"13\"\n                                           id=\"excel\"\n                                           enabled=\"true\"\n                                           styleName=\"excelIcon\"\n                                           (click)=\"report('xls')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.excel') }}\"></SwtButton>\n                                <SwtButton #pdf\n                                           buttonMode=\"true\"\n                                           tabIndex=\"14\"\n                                           id=\"pdf\"\n                                           enabled=\"true\"\n                                           styleName=\"pdfIcon\"\n                                           (click)=\"report('pdf')\"\n                                           (keyDown)=\"keyDownHandlerEvent($event)\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.pdf') }}\"></SwtButton>\n                                <SwtButton #fatcaReport\n                                           buttonMode=\"true\"\n                                           tabIndex=\"15\"\n                                           id=\"fatcaReport\"\n                                           includeInLayout=\"false\"\n                                           visible=\"false\"\n                                           enabled=\"true\"\n                                           styleName=\"fatcaReportIcon\"\n                                               toolTip=\"{{ getCommonMessages('button.tooltip.pdf') }}\"></SwtButton>\n                                <SwtHelpButton #helpIcon\n                                               id=\"helpIcon\"\n                                               tabIndex=\"16\"\n                                               buttonMode=\"true\"\n                                               (click)=\"showhelp()\"\n                                               enabled=\"true\"\n                                               styleName=\"helpIcon\"></SwtHelpButton>\n                            </HBox>\n                        </HBox>\n                    </SwtCanvas>\n                </SwtCanvas>\n            </div>\n        </SwtModule>\n    ", "styles": []}]}], "members": {"_creationComplete": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 113, "character": 5}, "arguments": ["creationComplete"]}]}], "_preinitialize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 115, "character": 5}, "arguments": ["preinitialize"]}]}], "_width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 116, "character": 5}, "arguments": ["width"]}]}], "_height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 117, "character": 5}, "arguments": ["height"]}]}], "hboxButtons": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 118, "character": 5}, "arguments": ["hboxButtons"]}]}], "customGrid": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 119, "character": 5}, "arguments": ["customGrid"]}]}], "reportbuttons": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 120, "character": 5}, "arguments": ["reportbuttons"]}]}], "_loadingImage": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 121, "character": 5}, "arguments": ["loadingImage"]}]}], "_settingButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 122, "character": 5}, "arguments": ["settingButton"]}]}], "_csv": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 123, "character": 5}, "arguments": ["csv"]}]}], "_excel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 124, "character": 5}, "arguments": ["excel"]}]}], "_pdf": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 125, "character": 5}, "arguments": ["pdf"]}]}], "_fatcaReport": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 126, "character": 5}, "arguments": ["fatcaReport"]}]}], "_helpIcon": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 127, "character": 5}, "arguments": ["helpIcon"]}]}], "buttonsTemplate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 129, "character": 5}, "arguments": ["buttonsTemplate"]}]}], "commonTemplate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 130, "character": 5}, "arguments": ["commonTemplate"]}]}], "buttons": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 132, "character": 5}, "arguments": ["buttons"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 239, "character": 34}, {"__symbolic": "reference", "name": "CommonService"}]}], "onCommonModuleInit": [{"__symbolic": "method"}], "onCommonModuleBeforeInit": [{"__symbolic": "method"}], "report": [{"__symbolic": "method"}], "keyDownHandlerEvent": [{"__symbolic": "method"}], "getSetting": [{"__symbolic": "method"}], "showhelp": [{"__symbolic": "method"}], "kvReportParams": [{"__symbolic": "method"}], "getKVParams": [{"__symbolic": "method"}], "clickHandler": [{"__symbolic": "method"}], "loadModule": [{"__symbolic": "method"}], "moduleReadyEventHandler": [{"__symbolic": "method"}], "popupClosedEventHandler": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "closeScreenHandler": [{"__symbolic": "method"}], "moduleReadyEventHandlerReportSetting": [{"__symbolic": "method"}], "popupClosedEventHandlerReportSetting": [{"__symbolic": "method"}], "deleteAction": [{"__symbolic": "method"}], "duplicateAction": [{"__symbolic": "method"}], "setDynamicButtons": [{"__symbolic": "method"}], "getDataFromUrl": [{"__symbolic": "method"}]}}, "ExternalInterface": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 3, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}}, "ScreenVersion": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 13, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "loadScreenVersion": [{"__symbolic": "method"}], "pushJsonData": [{"__symbolic": "method"}], "viewScrenVersion": [{"__symbolic": "method"}], "getJsonData": [{"__symbolic": "method"}]}}, "SwtImage": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 10, "character": 1}, "arguments": [{"selector": "SwtImage", "template": "\n    <img [src]=\"source\" [id]=\"id\">\n  ", "styles": []}]}], "members": {"source": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 19, "character": 4}, "arguments": ["source"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 20, "character": 4}, "arguments": ["id"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 21, "character": 4}, "arguments": ["width"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 22, "character": 4}, "arguments": ["height"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 23, "character": 31}]}], "ngOnInit": [{"__symbolic": "method"}]}}, "CommonUtil": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 20, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"log": {"__symbolic": "error", "message": "Variable not initialized", "line": 23, "character": 17}, "changes": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HashMap"}}, "spyTimers": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HashMap"}}, "CDBegin": "<![CDATA[", "CDEnd": "]]>", "ignoredObjects": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Array"}}, "SPY_TIME_INTERVAL": 1000, "gridImages": [], "USER_UPLOAD_PATH": {"__symbolic": "binop", "operator": "+", "left": "/uploads/", "right": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonUtil"}, "member": "getCurrentUserId"}}}, "getCurrentUserId": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ExternalInterface"}, "member": "call"}, "arguments": ["eval", "userId"]}}, "dateFromString": {"__symbolic": "function", "parameters": ["dateString", "pattern"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Date"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "ngx-bootstrap/chronos/test/chain", "name": "moment", "line": 114, "character": 20}, "arguments": [{"__symbolic": "reference", "name": "dateString"}, {"__symbolic": "reference", "name": "pattern"}]}, "member": "toString"}}]}}, "parseDate": {"__symbolic": "function", "parameters": ["dateString", "pattern"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Date"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "ngx-bootstrap/chronos/test/chain", "name": "moment", "line": 117, "character": 20}, "arguments": [{"__symbolic": "reference", "name": "dateString"}, {"__symbolic": "reference", "name": "pattern"}, true]}, "member": "toDate"}}]}}, "function": {"__symbolic": "error", "message": "Variable not initialized", "line": 189, "character": 16}, "getCurrentModuleId": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "String"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ExternalInterface"}, "member": "call"}, "arguments": ["getCurrentModuleId"]}]}, "member": "toUpperCase"}}}, "getCurrentEntityId": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ExternalInterface"}, "member": "call"}, "arguments": ["eval", "currEntityId"]}}}}, "CommonLogic": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 14, "character": 1}}], "members": {"convertDate": [{"__symbolic": "method"}], "calculateDays": [{"__symbolic": "method"}], "convertUKtoUS": [{"__symbolic": "method"}], "validateDate": [{"__symbolic": "method"}], "showAlert": [{"__symbolic": "method"}], "validateDateHelper": [{"__symbolic": "method"}], "isLeapYear": [{"__symbolic": "method"}], "checkDateRange": [{"__symbolic": "method"}], "result": [{"__symbolic": "method"}], "fault": [{"__symbolic": "method"}]}}, "XML": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 16, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}], "fromXMLString": [{"__symbolic": "method"}], "toXMLString": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}], "appendChild": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "parent": [{"__symbolic": "method"}], "children": [{"__symbolic": "method"}]}}, "XMLListCollection": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 106, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "toString": [{"__symbolic": "method"}], "toXMLString": [{"__symbolic": "method"}]}}, "SwtDOMManager": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 23, "character": 1}, "arguments": [{"selector": "[SwtDOMManager]"}]}], "members": {"onClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 44, "character": 3}, "arguments": ["onClick"]}]}], "onKeyDown": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 46, "character": 3}, "arguments": ["onKeyDown"]}]}], "onFocusOut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 48, "character": 3}, "arguments": ["onFocusOut"]}]}], "onKeyUp": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 50, "character": 3}, "arguments": ["onKeyUp"]}]}], "mouseOver": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 52, "character": 3}, "arguments": ["mouseOver"]}]}], "mouseEnter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 54, "character": 3}, "arguments": ["mouseEnter"]}]}], "mouseLeave": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 56, "character": 3}, "arguments": ["mouseLeave"]}]}], "onFocus": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 58, "character": 3}, "arguments": ["onFocus"]}]}], "mouseDown": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 60, "character": 3}, "arguments": ["mouseDown"]}]}], "mouseUp": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 62, "character": 3}, "arguments": ["mouseUp"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 64, "character": 3}, "arguments": ["width"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 66, "character": 3}, "arguments": ["height"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 68, "character": 3}, "arguments": ["styleName"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 70, "character": 3}, "arguments": ["id"]}]}], "paddingTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 72, "character": 3}, "arguments": ["paddingTop"]}]}], "paddingRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 74, "character": 3}, "arguments": ["paddingRight"]}]}], "paddingBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 76, "character": 3}, "arguments": ["paddingBottom"]}]}], "paddingLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 78, "character": 3}, "arguments": ["paddingLeft"]}]}], "marginTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 80, "character": 3}, "arguments": ["marginTop"]}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 82, "character": 3}, "arguments": ["marginRight"]}]}], "marginBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 84, "character": 3}, "arguments": ["marginBottom"]}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 86, "character": 3}, "arguments": ["marginLeft"]}]}], "horizontalAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 88, "character": 3}, "arguments": ["horizontalAlign"]}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 90, "character": 3}}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 140, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 150, "character": 31}]}], "ngOnInit": [{"__symbolic": "method"}], "disableComponent": [{"__symbolic": "method"}], "removeListeners": [{"__symbolic": "method"}], "setPaddings": [{"__symbolic": "method"}], "setMargins": [{"__symbolic": "method"}], "alignContent": [{"__symbolic": "method"}]}}, "FileReference": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 3, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "save": [{"__symbolic": "method"}]}}, "ExportInProgress": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 13, "character": 1}, "arguments": [{"selector": "SwtReportProgress", "template": "\n    <SwtModule (creationComplete)=\"initData()\"  (close)=\"close()\" width=\"100%\" height=\"100%\" controlBarEnabled=\"false\">\n          <VBox  paddingLeft =\"10\" paddingTop =\"10\" horizontalAlign=\"center\" verticalAlign=\"middle\" class=\"popup\" width=\"264\" height=\"100%\">\n                  <div  style=\"width:100%\" class=\"progress\">\n                      <div class=\"progress-bar progress-bar-striped active\" role=\"progressbar\"\n                      aria-valuenow=\"40\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:100%\"></div>\n                  </div>\n              <HBox paddingTop =\"10\" horizontalAlign=\"center\">\n                  <SwtButton #cancelButton label=\"Cancel\" (click)=\"cancelReport()\" style=\"margin: auto\"></SwtButton>\n              </HBox>\n          </VBox>\n    </SwtModule>\n  ", "styles": ["\n       .progress {\n           height: 8px;\n           border-radius: 0px;\n           margin-top: 6px;\n       }\n  \n  \n  "]}]}], "members": {"cancelButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 56, "character": 5}, "arguments": ["cancelButton"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 57, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}]}], "initData": [{"__symbolic": "method"}], "defaultContentFunction": [{"__symbolic": "method"}], "show": [{"__symbolic": "method"}], "hide": [{"__symbolic": "method"}], "cancelReport": [{"__symbolic": "method"}], "cancelExport": [{"__symbolic": "method"}], "initTimer": [{"__symbolic": "method"}], "ExportCanceled": [{"__symbolic": "method"}], "getTimer": [{"__symbolic": "method"}]}}, "SwtPagesToExport": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 15, "character": 1}, "arguments": [{"selector": "SwtPagesToExport", "template": "\n        <SwtModule (close)='popupClosed()' (creationComplete)='initData()' width=\"100%\" height=\"100%\" >\n        <VBox  width='100%' height='100%' paddingTop=\"10\" paddingRight=\"10\" paddingLeft=\"10\" paddingBottom=\"10\">\n            <SwtCanvas width=\"100%\" height=\"50%\">\n            <SwtRadioButtonGroup #exportButtonGroup   id=\"exportButtonGroup\"\n                                align=\"vertical\">\n                <SwtRadioItem #radioC id=\"radioC\" value=\"current\"  width=\"120\" groupName=\"exportButtonGroup\" selected=\"true\"  ></SwtRadioItem>\n                <SwtRadioItem #radioA id=\"radioA\" value=\"all\"   width=\"120\" groupName=\"exportButtonGroup\"  ></SwtRadioItem>\n            </SwtRadioButtonGroup>\n            </SwtCanvas>\n            <SwtCanvas width=\"100%\">\n            <HBox>\n\n            <SwtButton buttonMode=\"true\"\n                        id=\"okButton\"\n                        #okButton\n                        width=\"70\"\n                        enabled=\"true\"\n                        (click)=\"exportType()\"> </SwtButton>\n            <SwtButton buttonMode=\"true\"\n                        id=\"cancelButton\"\n                        #cancelButton\n                        width=\"70\"\n                        enabled=\"true\"\n                        (click)=\"popupClosed()\"></SwtButton>\n            </HBox>\n            </SwtCanvas>\n        </VBox>\n        </SwtModule>\n  ", "styles": ["\n  "]}]}], "members": {"okButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 54, "character": 3}, "arguments": ["okButton"]}]}], "cancelButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 55, "character": 3}, "arguments": ["cancelButton"]}]}], "exportButtonGroup": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 56, "character": 3}, "arguments": ["exportButtonGroup"]}]}], "radioC": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 57, "character": 3}, "arguments": ["radioC"]}]}], "radioA": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 58, "character": 3}, "arguments": ["radioA"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 69, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "initData": [{"__symbolic": "method"}], "popupClosed": [{"__symbolic": "method"}], "defaultContentFunction": [{"__symbolic": "method"}], "exportType": [{"__symbolic": "method"}], "show": [{"__symbolic": "method"}], "hide": [{"__symbolic": "method"}], "ExportCanceled": [{"__symbolic": "method"}]}}, "CancelExportEvent": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}]}], "clone": [{"__symbolic": "method"}]}, "statics": {"CANCEL_BUTTON_CLICK": "cancelclick"}}, "ContextMenu": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 6, "character": 1}, "arguments": [{"selector": "ContextMenu", "template": "\n  \n  ", "styles": []}]}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "hideBuiltInItems": [{"__symbolic": "method"}]}}, "ContextMenuItem": {"__symbolic": "class", "members": {"itemDOM": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 32, "character": 5}, "arguments": ["item"]}]}], "MenuItemSelect": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 35, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}]}]}}, "PopupWindowCloseEvent": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}]}], "clone": [{"__symbolic": "method"}]}}, "SwtProgressBar": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "SwtProgressBar", "template": "\n    <div #progressBar class=\"progressBar\">\n      <div class=\"progress\"><span id=\"label\"></span></div>\n    </div>\n  ", "styles": ["\n           .progressBar {\n              width: 100%;\n              height: 15px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#E7E7E7), to(#FDFDFD));\n              background-image: -webkit-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -moz-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -ms-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: -o-linear-gradient(top, #E7E7E7, #FDFDFD);\n              background-image: linear-gradient(to bottom, #E7E7E7, #FDFDFD);\n              border: 1px solid #808283;\n            }\n            span {\n                display: block;\n                width:100%;\n                height: 15px;\n                text-align: center;\n                color: #000;\n                font-size: 11px;\n                font-weight: bold;\n                font-family: verdana,helvetica;\n            }\n            .progress {\n              width: 0%;\n              height: 100%;\n              border-radius: 0px;\n              background-image: -webkit-gradient(linear, left top, left bottom, from(#5EC1FF), to(#009DFF));\n              background-image: -webkit-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -moz-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -ms-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: -o-linear-gradient(top, #5EC1FF, #009DFF);\n              background-image: linear-gradient(to bottom, #5EC1FF, #009DFF);\n            }\n            .stripedbg {\n                width: 0%;\n                height: 100%;\n                border-radius: 0px;\n                background-image: repeating-linear-gradient(45deg, #70CAF8, #70CAF8 15px, #3F99E6 15px, #3F99E6 30px);\n                -webkit-animation-name: move;\n                -webkit-animation-duration: 2s;\n                -webkit-animation-timing-function: linear;\n                -webkit-animation-iteration-count: infinite;\n                animation-name: move;\n                animation-duration: 2s;\n                animation-timing-function: linear;\n                animation-iteration-count: infinite;\n            }\n  "]}]}], "members": {"progressBar": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 66, "character": 3}, "arguments": ["progressBar"]}]}], "striped": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 76, "character": 3}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 102, "character": 3}}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 109, "character": 3}}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 118, "character": 3}}]}], "labelColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 126, "character": 3}, "arguments": ["labelColor"]}]}], "labelSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 127, "character": 3}, "arguments": ["labelSize"]}]}], "progressColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 128, "character": 3}, "arguments": ["progressColor"]}]}], "background": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 129, "character": 3}, "arguments": ["background"]}]}], "marginTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 131, "character": 3}, "arguments": ["marginTop"]}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 132, "character": 3}, "arguments": ["marginLeft"]}]}], "marginBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 133, "character": 3}, "arguments": ["marginBottom"]}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 134, "character": 3}, "arguments": ["marginRight"]}]}], "__ctor__": [{"__symbolic": "constructor"}], "ngOnInit": [{"__symbolic": "method"}], "value": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 198, "character": 3}}]}]}}, "FileUpload": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 20, "character": 1}, "arguments": [{"selector": "FileUpload", "template": "\n        <SwtModule (creationComplete)=\"onLoad()\" width=\"100%\" height=\"100%\">\n            <VBox width=\"100%\" height=\"100%\" paddingTop=\"5\" paddingLeft=\"5\" paddingBottom=\"5\" paddingRight=\"5\">\n                <SwtPanel id=\"lblTitle\" title=\" \" width=\"100%\" height=\"132\">\n                    <HBox paddingLeft=\"8\">\n                        <SwtLabel id=\"lblnameFile\"\n                                  text=\"{{ getCommonMessages('fileUpload.lblnameFile.label') }}\"></SwtLabel>\n                        <SwtTextInput #toUploadFile id=\"toUploadFile\" editable=\"false\" width=\"330\" marginTop=\"5\"></SwtTextInput>\n                        <!--<SwtButton #btnAdd id=\"btnAdd\" width=\"50\" label=\"...\" (onClick)=\"addFiles()\"\n                         toolTip=\"{{ getCommonMessages('fileUpload.btnAdd.tooltip') }}\"></SwtButton>-->\n\n                        <div class=\"file_Upload\" >\n                            <span>...</span>\n                            <input type=\"file\" class=\"upload\"  ng2FileSelect [uploader]=\"_refUploadFile\"   multiple />\n                        </div>\n                    </HBox>\n                    <HBox>\n                        <SwtLabel #lblSize id=\"lblSize\" width=\"48\" textAlign=\"right\"\n                                  text=\"{{ getCommonMessages('fileUpload.lblSize.label') }}\"></SwtLabel>\n                        <SwtLabel #lblFomattedSize id=\"lblFomattedSize\" width=\"105\" text=\"\"></SwtLabel>\n                    </HBox>\n                    <HBox>\n                        <SwtProgressBar #progBar marginLeft=\"15\" marginBottom=\"5\"  width=\"445\"></SwtProgressBar>\n                    </HBox>\n                    <HBox>\n                        <spacer width=\"405\"></spacer>\n                        <SwtButton #buttonClose\n                                   id=\"buttonClose\"\n                                   label=\"{{ getCommonMessages('button.close') }}\"\n                                   (click)=\"close()\"\n                                   toolTip=\"{{ getCommonMessages('button.tooltip.close') }}\"></SwtButton>\n                    </HBox>\n                </SwtPanel>\n            </VBox>\n        </SwtModule>\n    ", "styles": ["\n        .file_Upload {\n            position: relative;\n            overflow: hidden;\n            margin: 10px;\n            border-bottom:1px solid #52869a!important ;\n            border-top:1px solid #90b6c4!important;\n            border-left:1px solid #52869a!important;\n            border-right:1px solid #52869a!important;\n            border-radius: 5px;\n            font-size:12px;\n            letter-spacing:0.2px;\n            height: 23px;\n            margin : 5px!important;\n            font-weight:bolder;\n            color: #173553;\n            width: auto;\n            padding: 0px 10px;\n            background-color: #C2E5FF;\n            background-image: -webkit-gradient(linear, left top, left bottom, from(#C2E5FF), to(#92ACBF));\n            background-image: -webkit-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -moz-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -ms-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: -o-linear-gradient(top, #C2E5FF, #92ACBF);\n            background-image: linear-gradient(to bottom, #C2E5FF, #92ACBF);\n            background-image: url(\"assets/images/button_bg.png\");\n        }\n\n        .file_Upload input.upload {\n            position: absolute;\n            top: 0;\n            right: 0;\n            margin: 0;\n            padding: 0;\n            font-size: 20px;\n            cursor: pointer;\n            opacity: 0;\n            filter: alpha(opacity=0);\n        }\n\n        #lblnameFile {\n            margin-top: 5px;\n        }\n    "]}]}], "members": {"progBar": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 106, "character": 5}, "arguments": ["progBar"]}]}], "lblFomattedSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 107, "character": 5}, "arguments": ["lblFomattedSize"]}]}], "toUploadFile": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 108, "character": 5}, "arguments": ["toUploadFile"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 141, "character": 33}, {"__symbolic": "reference", "name": "CommonService"}]}], "onLoad": [{"__symbolic": "method"}], "closeHandler": [{"__symbolic": "method"}], "startUpload": [{"__symbolic": "method"}], "fileOverBase": [{"__symbolic": "method"}], "fileOverAnother": [{"__symbolic": "method"}], "onSuccessItem": [{"__symbolic": "method"}], "onErrorItem": [{"__symbolic": "method"}], "clearUpload": [{"__symbolic": "method"}], "updateProgBar": [{"__symbolic": "method"}], "formatFileSize": [{"__symbolic": "method"}]}}, "StringUtils": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 10, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"decimalSeparatorTo": {"__symbolic": "error", "message": "Variable not initialized", "line": 20, "character": 17}, "thousandsSeparatorTo": {"__symbolic": "error", "message": "Variable not initialized", "line": 21, "character": 17}, "AMOUNT_PATTERN0": 0, "AMOUNT_PATTERN1": 1, "AMOUNT_PATTERN2": 2, "AMOUNT_PATTERN3": 3, "htmlTagsRegex": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "RegExp"}, "arguments": ["(<style[^>]*/>|<style[^>]*>|</style>|<font[^>]*>|</font>|<br>|<br/>|<a[^>]*>|</a>|<b>|</b>|<li>|</li>|<i>|</i>|<u>|</u>|<p[^>]*>|</p>|<span[^>]*>|</span>)", "ig"]}}}, "CustomTree": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 24, "character": 1}, "arguments": [{"selector": "CustomTree", "template": "\n        <!--<div id=\"{{id}}\" selector=\"SwtCustomTree\" #treeContainer class=\"treeContainer {{ styleName }}\"></div>-->\n        <div class=\"customTreeWarapper\">\n            <table #treeContainer class=\"treeContainer {{ styleName }}\">\n                <tr style=\"height: 23px\">\n                    <td></td>\n                </tr>\n            </table>\n        </div>\n        <span #treeTipHolder></span>\n    ", "styles": ["\n        :host {\n            display: block;\n            /*background-color: #FFF;*/\n            /*overflow: auto;*/\n            width: 300px;\n            height: 450px;\n        }\n\n        .customTreeWarapper {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            background-color: #FFF;\n            border: 1px solid #B7BABC;\n            line-height: initial;\n        }\n\n        .customTreeWarapper table td, table th{\n            padding : 2px;\n        }\n\n\n\n        .treeContainer {\n            width: 100%;\n            padding-left: 3px;\n        }\n\n        .fancytree-container {\n            outline: none;\n            border: none;\n        }\n\n        .fancytree-container:focus {\n            outline: none;\n            border: none;\n        }\n    "]}]}], "members": {"ITEM_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 94, "character": 5}, "arguments": ["itemClick"]}]}], "ITEM_ACTIVATE": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 95, "character": 5}, "arguments": ["itemActivate"]}]}], "ITEM_DBCLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 96, "character": 5}, "arguments": ["dbClick"]}]}], "MOUSE_OUT": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 97, "character": 5}, "arguments": ["mouseOut"]}]}], "itemOpen": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 98, "character": 5}, "arguments": ["itemOpen"]}]}], "itemClose": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 99, "character": 5}, "arguments": ["itemClose"]}]}], "MOUSE_OVER": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 100, "character": 5}, "arguments": ["mouseOver"]}]}], "FOCUS_IN": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 101, "character": 5}, "arguments": ["focusIn"]}]}], "FOCUS_OUT": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 102, "character": 5}, "arguments": ["focusOut"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 103, "character": 5}, "arguments": ["id"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 104, "character": 5}, "arguments": ["styleName "]}]}], "itemEditEnd": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 111, "character": 5}, "arguments": ["itemEditEnd"]}]}], "treeContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 145, "character": 5}, "arguments": ["treeContainer"]}]}], "treeTipHolder": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 146, "character": 5}, "arguments": ["treeTipHolder"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 154, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "defaultHideFunction": [{"__symbolic": "method"}], "level2Order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 211, "character": 5}}]}], "level3Order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 227, "character": 5}}]}], "level4Order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 245, "character": 5}}]}], "allowMultipleSelection": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 261, "character": 5}}]}], "dataProvider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 308, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 358, "character": 5}}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 372, "character": 5}}]}], "hideIcons": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 382, "character": 5}}]}], "addCheckbox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 401, "character": 5}}]}], "indeterminateCheckbox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 419, "character": 5}}]}], "saveTreeStateBasedOn": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 436, "character": 5}}]}], "selectedItem": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 507, "character": 5}}]}], "doubleClickEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 529, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 559, "character": 5}}]}], "level0Order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 585, "character": 5}}]}], "level1Order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 594, "character": 5}}]}], "dragMoveEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 665, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 687, "character": 5}}]}], "manageChangesArray": [{"__symbolic": "method"}], "findNode": [{"__symbolic": "method"}], "findAndExpandNode": [{"__symbolic": "method"}], "expandNode": [{"__symbolic": "method"}], "sort": [{"__symbolic": "method"}], "treeLabelFunction": [{"__symbolic": "method"}], "findNodeRecursively": [{"__symbolic": "method"}], "selectNodeByAttribute": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterContentInit": [{"__symbolic": "method"}], "isVisibleNode": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "findNodeinDataprovider": [{"__symbolic": "method"}], "recusiveSelectDataProvider": [{"__symbolic": "method"}], "recusiveSelectDataProviderChildren": [{"__symbolic": "method"}], "removeNode": [{"__symbolic": "method"}], "manageExpanderOrientation": [{"__symbolic": "method"}], "removeAll": [{"__symbolic": "method"}], "setEditableItem": [{"__symbolic": "method"}], "appendNode": [{"__symbolic": "method"}], "expandAll": [{"__symbolic": "method"}], "collapseAll": [{"__symbolic": "method"}], "setDataTip": [{"__symbolic": "method"}], "getDataTip": [{"__symbolic": "method"}], "selectedItems": [{"__symbolic": "method"}], "validateDisplayList": [{"__symbolic": "method"}], "reOpenSavedState": [{"__symbolic": "method"}], "saveTreeOpenState": [{"__symbolic": "method"}], "expandItem": [{"__symbolic": "method"}], "customStyleFunction": [{"__symbolic": "method"}], "getItemIndex": [{"__symbolic": "method"}], "clearChanges": [{"__symbolic": "method"}], "getSelectedLevel": [{"__symbolic": "method"}], "getInstance": [{"__symbolic": "method"}], "dataProviderCRUD": [{"__symbolic": "method"}], "diselectAll": [{"__symbolic": "method"}], "updateStateOnExpand": [{"__symbolic": "method"}], "itemEditEndHandler": [{"__symbolic": "method"}], "sortNodeBy": [{"__symbolic": "method"}], "scrollToBottom": [{"__symbolic": "method"}], "scrollToTop": [{"__symbolic": "method"}], "recursive": [{"__symbolic": "method"}], "getOpenedItems": [{"__symbolic": "method"}], "getClosedItems": [{"__symbolic": "method"}], "selectNode": [{"__symbolic": "method"}], "getScrollPosition": [{"__symbolic": "method"}]}, "statics": {"seqAttribute": "TREE_ITEM_LOCAL_SEQ", "TREE_STR": "Tree", "LEVEL_1_STR": "Level1", "LEVEL_2_STR": "Level2", "LEVEL_3_STR": "Level3", "CRUD_OPERATION": "crud_operation", "CRUD_DATA": "crud_data"}}, "ILMTreeIndeterminate": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CustomTree"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 20, "character": 1}, "arguments": [{"selector": "ILMTreeIndeterminate", "template": "\n        <!--<div id=\"{{id}}\" selector=\"SwtCustomTree\" #treeContainer class=\"treeContainer {{ styleName }}\"></div>-->\n        <div class=\"customTreeWarapper\">\n            <table #treeContainer class=\"treeContainer {{ styleName }}\">\n                <tr style=\"height: 23px\">\n                    <td></td>\n                </tr>\n            </table>\n        </div>\n        <span #treeTipHolder></span>\n    ", "styles": ["\n        :host {\n            display: block;\n            /*background-color: #FFF;*/\n            /*overflow: auto;*/\n            width: 300px;\n            height: 450px;\n        }\n\n        .customTreeWarapper {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            background-color: #FFF;\n            border: 1px solid #B7BABC;\n            line-height: initial;\n        }\n\n        .customTreeWarapper table td, table th{\n            padding : 0px;\n        }\n\n        .treeContainer {\n            width: 100%;\n            padding-left: 3px;\n        }\n\n        .fancytree-container {\n            outline: none;\n            border: none;\n        }\n\n        .fancytree-container:focus {\n            outline: none;\n            border: none;\n        }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 74, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "customHideFunction": [{"__symbolic": "method"}], "showActualDatasetsOnly": [{"__symbolic": "method"}], "showBalanceDatasetsOnly": [{"__symbolic": "method"}], "showHideGroupScenarioNodes": [{"__symbolic": "method"}], "getSelectedNodes": [{"__symbolic": "method"}], "checkedGroupScenarioCharts": [{"__symbolic": "method"}], "getCheckedScenarioNodes": [{"__symbolic": "method"}], "getCheckedScenarioNodesTree": [{"__symbolic": "method"}], "setCheckedScenarioNodesTree": [{"__symbolic": "method"}], "getDisplayedScenarios": [{"__symbolic": "method"}], "findYFields": [{"__symbolic": "method"}], "getVisibleYFields": [{"__symbolic": "method"}], "getDisplayedThresolds": [{"__symbolic": "method"}], "getDisplayedThresoldsArray": [{"__symbolic": "method"}], "getGroupScenarioCombinations": [{"__symbolic": "method"}], "dataProvider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 486, "character": 5}}]}], "reloadNodeById": [{"__symbolic": "method"}]}}, "HashMap": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "containsKey": [{"__symbolic": "method"}], "containsValue": [{"__symbolic": "method"}], "getKeys": [{"__symbolic": "method"}], "getValues": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}], "getValueAsObject": [{"__symbolic": "method"}], "getKey": [{"__symbolic": "method"}], "put": [{"__symbolic": "method"}], "putAll": [{"__symbolic": "method"}], "clear": [{"__symbolic": "method"}], "remove": [{"__symbolic": "method"}], "size": [{"__symbolic": "method"}], "isEmpty": [{"__symbolic": "method"}], "findKey": [{"__symbolic": "method"}], "findValue": [{"__symbolic": "method"}]}}, "Container": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 20, "character": 1}, "arguments": [{"selector": "Container", "template": "  \n    ", "styles": []}]}], "members": {"ngAfterViewInit": [{"__symbolic": "method"}], "_container": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 45, "character": 5}, "arguments": ["_container", {"read": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 45, "character": 36}}]}]}], "dropShadowEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 101, "character": 5}, "arguments": ["dropShadowEnabled"]}]}], "cornerRadius": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 113, "character": 5}, "arguments": ["cornerRadius"]}]}], "borderThickness": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 125, "character": 5}, "arguments": ["borderThickness"]}]}], "borderStyle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 137, "character": 5}, "arguments": ["borderStyle"]}]}], "borderColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 149, "character": 5}, "arguments": ["borderColor"]}]}], "backGroundColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 160, "character": 5}, "arguments": ["backGroundColor"]}]}], "right": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 172, "character": 5}, "arguments": ["right"]}]}], "left": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 188, "character": 5}, "arguments": ["left"]}]}], "bottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 198, "character": 5}}]}], "top": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 207, "character": 5}}]}], "horizontalGap": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 216, "character": 5}, "arguments": ["horizontalGap"]}]}], "verticalGap": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 226, "character": 5}, "arguments": ["verticalGap"]}]}], "textAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 235, "character": 5}, "arguments": ["textAlign"]}]}], "addTabIndex": [{"__symbolic": "method"}], "removeTabIndex": [{"__symbolic": "method"}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 260, "character": 5}, "arguments": ["toolTip"]}]}], "toolTipPreviousValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 279, "character": 8}, "arguments": ["toolTipPreviousValue"]}]}], "textDictionaryId": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 316, "character": 5}, "arguments": ["tooltipDictionaryId"]}]}], "name": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 323, "character": 5}, "arguments": ["name"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 335, "character": 5}, "arguments": ["styleName"]}]}], "horizontalAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 344, "character": 5}}]}], "verticalAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 360, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 376, "character": 5}, "arguments": ["width"]}]}], "showScrollBar": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 403, "character": 5}, "arguments": ["showScrollBar"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 422, "character": 5}, "arguments": ["height"]}]}], "minHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 451, "character": 5}, "arguments": ["minHeight"]}]}], "minWidth": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 462, "character": 5}, "arguments": ["min<PERSON><PERSON><PERSON>"]}]}], "maxHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 472, "character": 5}, "arguments": ["maxHeight"]}]}], "maxWidth": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 483, "character": 5}, "arguments": ["max<PERSON><PERSON><PERSON>"]}]}], "includeInLayout": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 494, "character": 5}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 520, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 543, "character": 5}}]}], "paddingTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 575, "character": 5}}]}], "paddingBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 598, "character": 5}}]}], "paddingLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 620, "character": 5}}]}], "paddingRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 642, "character": 5}}]}], "marginTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 665, "character": 5}}]}], "marginBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 678, "character": 5}}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 691, "character": 5}}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 704, "character": 5}}]}], "maxChars": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 718, "character": 5}, "arguments": ["maxChars"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 722, "character": 35}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "getRealHeight": [{"__symbolic": "method"}], "addChild": [{"__symbolic": "method"}], "addChildAt": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "removeAllChildren": [{"__symbolic": "method"}], "getChildren": [{"__symbolic": "method"}], "contains": [{"__symbolic": "method"}], "getChildAt": [{"__symbolic": "method"}], "getChildByName": [{"__symbolic": "method"}], "removeChildAt": [{"__symbolic": "method"}], "getChildIndex": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "clean": [{"__symbolic": "method"}], "isloaded": [{"__symbolic": "method"}], "unload": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}], "_spyChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "validateRestrict": [{"__symbolic": "method"}], "validateMaxChar": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "Grid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 4, "character": 1}, "arguments": [{"selector": "Grid", "template": "\n            <div  \n                 fxLayout=\"column\" class=\"noOutline\"\n                 fxLayoutGap=\"{{verticalGap}}\">\n                   <ng-content></ng-content>\n\n            </div>\n  ", "styles": ["\n  :host {\n      outline: none;\n    }\n    .noOutline{\n      outline: none;\n    }\n  "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 30, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}]}}, "GridRow": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 39, "character": 1}, "arguments": [{"selector": "GridRow", "template": "\n            <div  \n                 fxLayout=\"row\" class=\"noOutline\"\n                 fxLayoutGap=\"{{horizontalGap}}\" >\n                <ng-content></ng-content>\n            </div>\n    ", "styles": ["\n    :host {\n        outline: none;\n      }\n      .noOutline{\n        outline: none;\n      }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 30, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}]}}, "GridItem": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 74, "character": 1}, "arguments": [{"selector": "GridItem", "template": "\n            <div   fxLayout=\"row\" class=\"noOutline\" fxLayoutGap=\"{{horizontalGap}}\" >\n               <ng-content></ng-content>\n            </div>\n    ", "styles": ["\n    :host {\n        outline: none;\n      }\n      .noOutline{\n        outline: none;\n      }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 30, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}]}}, "SwtTabNavigator": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 355, "character": 1}, "arguments": [{"selector": "SwtTabNavigator", "template": "\n      <div >\n          <div class=\"nav-bar-container\">\n              <div class=\"btn leftArrow\" (mousedown)=\"scrollTabs('left')\" (mouseup)=\"dispose()\"><span><b>&#9666;</b></span></div>\n              <ul *ngIf=\"aboutActive\" (mouseup)=\"dispose()\"   id=\"swtTabNavigator\" class=\"nav nav-tabs \">\n              <ng-container   *ngFor=\"let tab of sortTabChildrenArray() let act = index\"  >\n                    <li *ngIf=\"tab.visible && tab.label\"  \n                        id='header_{{tab.id}}'\n                        [class.active]=\"tab.active\"\n                        [title]=\"tab.toolTip\" \n                        [class.disabled-container]=\"!tab.enabled\"\n                        class=\"tabNavigator-tabs\"\n                            (click)=\"onMousedown(tab, $event)\"\n                             >\n                            <span style=\"display:flex;\">\n                          {{tab.label}}\n                               <i *ngIf=\"tab.closable\"   class=\"fa fa-times closeFav\" aria-hidden=\"true\" (click)=\"removeContentTab(tab)\"></i>\n                        </span>\n\n                    </li>\n               </ng-container>\n          </ul>\n            \n              <div class=\"btn rightArrow\" [ngStyle]=\"{'visibility': ( tabChildrenArray.length == 0  || clientWidth <  scrollWidth  || scrollValue ==  maxScroll ) ? 'hidden':'visible'}\" (mousedown)=\"scrollTabs('right')\" (mouseup)=\"dispose()\"><span><b>&#9656;</b></span></div>\n                <div class=\"btn-group\"[ngStyle]=\"{'visibility': tabChildrenArray.length > 0 && showDropDown ? 'visible':'hidden'}\" >\n                <a id=\"test-dropdown-btn\" class=\"input-group-addon dropdownBtn dropdown-toggle\"  >\n                        <i _ngcontent-c8=\"\" class=\"glyphicon glyphicon-triangle-bottom\"></i>\n                    </a>\n                    <ul  class=\"dropdown-menu\" role=\"menu\"   >\n                        <li role=\"menuitem\" *ngFor=\"let tab of tabChildrenArray\">\n                            <a class=\"dropdown-item\" (click)=\"scrollToTabFromCombo(tab)\">\n                                <b>{{ tab.label }}</b>\n                            </a>\n                        </li>\n                  </ul> \n                  \n                </div>\n\n          </div>\n          <div  class=\"tabNavigator-content\" style=\"width:100%; height:calc(100% - 20px);\">\n              <ng-content></ng-content>\n              <div #_container ></div>\n          </div>\n    </div>\n  ", "styles": ["\n         :host {\n               display: block;\n               margin: 0px 0px 5px 0px;\n               width: 100%;\n               height: 100%;\n               outline: none;\n         }\n       .dropdownBtn{\n           height: 20px;\n           width: 20px;\n           border: 1px solid #9C9FA1;\n           padding: 0px;\n           cursor: default;\n           margin-top: 1px;\n           border-radius: 2px;\n           text-decoration: none;\n           color: black;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff );\n       }\n       .leftArrow, .rightArrow {\n           height: 20px;\n           width: 20px;\n           border: 1px solid #9C9FA1;\n           padding: 0px; \n           cursor: default;\n           margin-top: 1px;\n           border-radius: 2px;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #ccecff);\n           background-image: -o-linear-gradient(top,#FFFFFF, #ccecff);\n           background-image: linear-gradient(to bottom,#FFFFFF, #ccecff);\n       }\n       .leftArrow:hover, .rightArrow:hover {\n           border: 1px solid #009DFF;\n           background-image: -webkit-linear-gradient(top,#FFFFFF, #EEEEEE);\n           background-image: -moz-linear-gradient(top, #FFFFFF, #EEEEEE);\n           background-image: -ms-linear-gradient(top, #FFFFFF, #EEEEEE);\n           background-image: -o-linear-gradient(top,#FFFFFF, #EEEEEE);\n           background-image: linear-gradient(to bottom,#FFFFFF, #EEEEEE);\n       }\n       .leftArrow>span, .rightArrow>span {\n           font-size: 14px;\n       }\n       .nav-bar-container{\n           width:100%;\n           display: flex;\n           margin: 0px 0px 0px -2px !important;\n           display:-ms-flexbox;\n           -ms-flex-wrap: nowrap;\n       }\n       \n       .icon {\n           width: 15px;\n           margin-top: -3px;\n       }\n       .nav {\n           padding: 0px;\n           height: 20px;\n           overflow-x: auto;\n           overflow-y: hidden;\n           margin-top: 1px;\n           flex-grow:1;\n           width: max-content;\n       }\n       /* hide scroll bar and keep div scrollable */\n       .nav::-webkit-scrollbar {\n           width: 0px;\n       }\n       /* width */\n       .nav::-webkit-scrollbar:horizontal {\n           height: 0px;\n       }\n        \n       .nav-tabs{\n            padding: 0px;\n            height: 20px;\n            overflow-x: auto;\n            overflow-y: hidden;\n            margin-top: 1px;\n            -ms-flex-positive: 1;\n            flex-grow: 1;\n            display:flex;\n            width: max-content;\n       }\n\n       .closeFav{\n            color: #EB5946;\n            border-radius: 2px;\n            font-size: 12px;\n            padding: 1px;\n            padding-top: 0px;\n            position: relative;\n            left: 6px;\n            top: 1px;\n       } \n       .btn-group> ul {\n           margin-top: -1px;\n           margin-left: -139px;\n           width: -webkit-stretch;\n       }\n       .btn-group> ul> li {\n           font-size: 11px;\n           font-weight: bold;\n       }\n       .btn-group> ul> li> a:hover {\n           background-color: #b2e1ff !important;\n           cursor: default;\n       }\n\n\n\n  "]}]}], "members": {"containerNavigator": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 547, "character": 5}, "arguments": ["containerNavigator"]}]}], "swtTabNavigator": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 548, "character": 5}, "arguments": ["swtTabNavigator"]}]}], "onChange_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 551, "character": 5}, "arguments": ["onChange"]}]}], "sortTabChildrenArray": [{"__symbolic": "method"}], "calculateHeigt": [{"__symbolic": "method"}], "tabChildren": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren", "line": 566, "character": 5}, "arguments": [{"__symbolic": "reference", "name": "Tab"}]}]}], "borderBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 657, "character": 5}, "arguments": ["borderBottom"]}]}], "showDropDown": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 667, "character": 5}, "arguments": ["showDropDown"]}]}], "applyOrder": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 676, "character": 6}, "arguments": ["applyOrder"]}]}], "borderTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 685, "character": 5}, "arguments": ["borderTop"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 140, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 303, "character": 96}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "setSelectedTab": [{"__symbolic": "method"}], "getSelectedTab": [{"__symbolic": "method"}], "addChild": [{"__symbolic": "method"}], "addChildPushStategy": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "getChildAt": [{"__symbolic": "method"}], "getTabChildren": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "setUndockPolicyForTab": [{"__symbolic": "method"}], "onMousedown": [{"__symbolic": "method"}], "scrollTabs": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "removeContentTab": [{"__symbolic": "method"}], "scrollToTabFromCombo": [{"__symbolic": "method"}], "checkValue": [{"__symbolic": "method"}]}}, "Tab": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 19, "character": 1}, "arguments": [{"selector": "SwtTab", "template": "\n        <div  class=\"swttab\" >\n            <ng-content></ng-content>\n             <div  #_container ></div>\n        </div> \n    ", "styles": ["\n         :host {\n              margin:  0px;\n              width: 100%;\n              height: 100%;\n              display:block;\n              outline: none;\n         }\n        .swttab {\n              box-sizing: border-box;\n              display: flex;\n              display: -moz-flex;\n              display: -o-flex;\n              display: -webkit-flex;\n              flex-direction: column;\n              width: 100%;\n              height: 100%;\n              outline: none;\n        }\n    "]}]}], "members": {"label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 60, "character": 5}, "arguments": ["label"]}]}], "order": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 70, "character": 6}, "arguments": ["order"]}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 80, "character": 5}, "arguments": ["visible"]}]}], "active": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 94, "character": 5}, "arguments": ["active"]}]}], "closable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 127, "character": 5}, "arguments": ["closable"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 140, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}], "setTabHeaderStyle": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "setUndockPolicy": [{"__symbolic": "method"}]}}, "TabPushStategy": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Tab"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 241, "character": 1}, "arguments": [{"selector": "SwtTabPushStrategy", "template": "\n        <div  class=\"swttab\" >\n            <ng-content></ng-content>\n             <div  #_container ></div>\n        </div> \n    ", "styles": ["\n         :host {\n              margin:  0px;\n              width: 100%;\n              height: 100%;\n              display:block;\n              outline: none;\n         }\n        .swttab {\n              box-sizing: border-box;\n              display: flex;\n              display: -moz-flex;\n              display: -o-flex;\n              display: -webkit-flex;\n              flex-direction: column;\n              width: 100%;\n              height: 100%;\n              outline: none;\n        }\n    "], "providers": [{"provide": {"__symbolic": "reference", "name": "Tab"}, "useExisting": {"__symbolic": "reference", "name": "TabPushStategy"}}], "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 270, "character": 21}, "member": "OnPush"}}]}], "members": {"active": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 275, "character": 2}, "arguments": ["active"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 140, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 303, "character": 96}]}], "ngOnInit": [{"__symbolic": "method"}]}}, "Spacer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "spacer", "template": "\n    <div class=\"spacer\"></div>\n  ", "styles": []}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 20, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}]}}, "SwtRichTextEditor": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtTextArea"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 13, "character": 1}, "arguments": [{"selector": "SwtRichTextEditor", "template": "\n     <div class=\"tinymce-editor-container\">\n      <div class=\"tinymce-editor-header\">\n          <h6><b>{{ title }}</b></h6>\n      </div>\n      <div class=\"tinymce-editor-body\">\n         <textarea id=\"{{elementId}}\"></textarea>\n      </div>\n    </div>\n    \n        \n    ", "styles": ["\n  "]}]}], "members": {"title": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 32, "character": 5}, "arguments": ["title"]}]}]}}, "SwtList": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "SwtList", "template": "\n    <div #list id=\"ecran\" class=\"boxbox\" [ngClass]=\"{'disabl':enabled==false}\">\n    <ul #swtlistItems class=\"list-group\" (window:keydown)=\"testkey($event)\">\n        <div *ngIf=\"enabled==true&&selectable==true\">\n            <li (click)=\"clickHandler($event,i);onClick($event)\" (mouseenter)=\"setToolTip($event,item)\"\n                data-placement=\"right\" (dblclick)=\"doubleClicked($event)\" class=\"list-group-item\"\n                [ngClass]=\"{active:coloredIndex[i]==true,hover:coloredIndex!=i,multipleSelectNav:multipleSelectNav[i]==true}\"\n                *ngFor=\"let item of dataProvider; let i = index\">\n                <p  >{{ item.content }}</p>\n                <img *ngIf=\"item.img\" src=\"{{ item.img }}\"  style=\"height:20px;width:20px\"\n                    class=\"icon-item\">\n            </li>\n        </div>\n        <div *ngIf=\"!enabled || !selectable\">\n            <li *ngFor=\"let item of dataProvider; let i = index\" class=\"list-group-item-disabled\" (mouseenter)=\"setToolTip($event,item)\"\n                 data-placement=\"right\">\n                <p>{{ item.content }}</p> \n                <img *ngIf=\"item.img\" src=\"{{ item.img }}\" style=\"height:20px;width:20px\" class=\"item-img\">\n            </li>\n        </div>\n    </ul>\n</div>\n\n  ", "styles": ["\n    .icon-item{\n        float:right; \n        margin-right:10px\n    }\n    .item-img{\n        float:right; \n        margin-right:10px\n    }\n    .list-group-item-disabled{\n        height: 25px;\n        font-size: 14px;\n        font-family: verdana,helvetica;\n        padding: 0px 0px 0px 10px;\n        margin: 0px;\n        line-height: 25px;\n        border: none;\n        text-overflow: ellipsis; \n        white-space: nowrap;\n        overflow:hidden\n    }\n    .list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {\n        color: black;  \n    } \n    .list-group-item.active{\n        background-color: #7FCEFF;\n    }\n    .list-group-item.multipleSelectNav{\n        border-color: #7FCEFF;\n        border-width: thin;\n        border-style:solid;\n    }\n    .list-group-item:focus {\n         background-color: red; \n    }\n    .list-group{\n        margin-bottom:0px !important;\n    }\n    .list-group-item:hover {\n       background-color: #B2E1FF;\n       cursor: default;\n    }\n    .list-group-item {\n        height: 25px;\n        font-size: 14px;\n        font-family: verdana,helvetica;\n        padding: 0px 0px 0px 10px;\n        margin: 0px;\n        line-height: 25px;\n        border: none;\n        text-overflow: ellipsis; \n        white-space: nowrap;\n        overflow:hidden\n    }\n    \n    .boxbox{\n        height:100%;\n        width:100%;\n        border: 1px solid #ddd; \n        overflow: auto;\n        background-color: white;\n    }\n    .boxbox:hover{\n        cursor: default;\n    }\n    .disabl{\n        z-index: 1;\n        background-color: #ddd;\n        opacity: 0.3;\n    }\n"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 128, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 128, "character": 99}]}], "ngOnInit": [{"__symbolic": "method"}], "click_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 151, "character": 5}, "arguments": ["itemClick"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 153, "character": 5}, "arguments": ["toolTip"]}]}], "showDataTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 154, "character": 5}, "arguments": ["showDataTips"]}]}], "doubleClickEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 155, "character": 5}, "arguments": ["doubleClickEnabled"]}]}], "allowMultipleSelection": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 157, "character": 5}}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 173, "character": 5}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 174, "character": 5}, "arguments": ["id"]}]}], "setToolTip": [{"__symbolic": "method"}], "selectedIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 221, "character": 5}}]}], "selectedIndices": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 254, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 272, "character": 5}}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 288, "character": 5}}]}], "swtlistItems": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 302, "character": 5}, "arguments": ["swtlistItems"]}]}], "list": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 303, "character": 5}, "arguments": ["list"]}]}], "onChange": [{"__symbolic": "method"}], "testkey": [{"__symbolic": "method"}], "scrollToBottom": [{"__symbolic": "method"}], "scrollToTop": [{"__symbolic": "method"}], "scrollFocusToBottom": [{"__symbolic": "method"}], "scrollFocusToTop": [{"__symbolic": "method"}], "addItem": [{"__symbolic": "method"}], "removeItem": [{"__symbolic": "method"}], "removeItems": [{"__symbolic": "method"}], "removeAll": [{"__symbolic": "method"}], "clickHandler": [{"__symbolic": "method"}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 630, "character": 5}}]}], "selectable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 641, "character": 5}}]}], "spyChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "onClick": [{"__symbolic": "method"}], "doubleClicked": [{"__symbolic": "method"}]}}, "parentApplication": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 118, "character": 1}}], "members": {}, "statics": {"loaderInfo": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LoaderInfo"}}, "undock": null, "height": null, "screenName": null, "menuOrderGroup": null, "width": null, "moduleId": null, "programId": null, "menuAction": null, "labelGeneric": null, "menuAccess": null, "menuId": null, "navigator": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Navigator"}}, "screenClosed": null, "customParams": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HashMap"}}, "translateObject": null, "modules": [], "favorites": [], "singletons": null, "currentModule": null, "document": null, "selectedTab": "", "getCurrentModuleId": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 201, "character": 15, "module": "./src/app/modules/swt-toolbox/com/swallow/utils/parent-application.service"}}}}, "Navigator": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "removeChildAt": [{"__symbolic": "method"}], "addChildAt": [{"__symbolic": "method"}], "addChild": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "removeAllChild": [{"__symbolic": "method"}], "getTabs": [{"__symbolic": "method"}], "setToActive": [{"__symbolic": "method"}]}}, "LoaderInfo": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}]}}, "LinkButton": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtButton"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 10, "character": 1}, "arguments": [{"selector": "LinkButton", "template": "\n       <div selector=\"LinkButton\"\n            #swtbutton  \n            class=\"minWidthBtn linkbutton\">\n            <span  class=\"truncate buttonLabel\" ></span>\n       </div>\n  ", "styles": ["\n            :host{\n                outline:none;\n            }\n           .disabledLinkButton{\n                color: #ABB5B5!important;\n                user-select: none;\n                pointer-events: none;\n                outline: none;\n           }\n  "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 34, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 39, "character": 5}}]}]}}, "SwtText": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtLabel"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtText", "template": "\n        <span selector=\"SwtText\"\n               class=\"ellipsisEnabled\"\n               #labelDOM>\n        </span>\n  ", "styles": ["\n       :host {\n          display: block;\n           outline:none;\n       }\n       span {\n         display: inline-block;\n       }\n      .ellipsisEnabled{\n        vertical-align: bottom;\n        text-overflow: ellipsis; \n        overflow: hidden; \n        white-space: nowrap;\n     }\n  "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 46, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}]}]}}, "SwtModule": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 13, "character": 1}, "arguments": [{"selector": "SwtModule", "template": "\n        <div style=\"background-color: #D6E3FE;\"\n             (contextmenu)=\"onRightClick($event)\">\n            <ng-content></ng-content>\n            <ng-container #_container></ng-container>\n        </div>\n    "}]}], "members": {"creationComplete": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 33, "character": 5}, "arguments": ["creationComplete"]}]}], "preinitialize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 35, "character": 5}, "arguments": ["preinitialize"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 42, "character": 35}, {"__symbolic": "reference", "name": "CommonService"}]}], "contextMenu": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 54, "character": 5}}]}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "close": [{"__symbolic": "method"}], "getQualifiedClassName": [{"__symbolic": "method"}], "getSystemMessages": [{"__symbolic": "method"}], "getCommonMessages": [{"__symbolic": "method"}], "getLoginMessages": [{"__symbolic": "method"}], "getAMLMessages": [{"__symbolic": "method"}], "getDUPMessages": [{"__symbolic": "method"}], "getARCMessages": [{"__symbolic": "method"}], "getInputMessages": [{"__symbolic": "method"}], "getCashMessages": [{"__symbolic": "method"}], "getFatcaMessages": [{"__symbolic": "method"}], "resetSpy": [{"__symbolic": "method"}], "subscribeSpy": [{"__symbolic": "method"}], "onComponentChanged": [{"__symbolic": "method"}], "onComponentNotChanged": [{"__symbolic": "method"}], "onRightClick": [{"__symbolic": "method"}]}}, "Keyboard": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"BACKSPACE": 8, "CAPS_LOCK": 20, "CONTROL": 17, "DELETE": 46, "DOWN": 40, "END": 35, "ENTER": 13, "ESCAPE": 32, "F1": 112, "F2": 113, "F3": 114, "F4": 115, "F5": 116, "F6": 117, "F7": 118, "F8": 119, "F9": 120, "F10": 121, "F11": 122, "F12": 123, "F13": {"__symbolic": "error", "message": "Variable not initialized", "line": 24, "character": 17}, "F14": {"__symbolic": "error", "message": "Variable not initialized", "line": 25, "character": 17}, "F15": {"__symbolic": "error", "message": "Variable not initialized", "line": 26, "character": 17}, "HOME": 36, "INSERT": 45, "LEFT": 37, "NUMPAD_0": 96, "NUMPAD_1": 97, "NUMPAD_2": 98, "NUMPAD_3": 99, "NUMPAD_4": 100, "NUMPAD_5": 101, "NUMPAD_6": 102, "NUMPAD_7": 103, "NUMPAD_8": 104, "NUMPAD_9": 105, "NUMPAD_ADD": 107, "NUMPAD_DECIMAL": 110, "NUMPAD_MULTIPLY": 106, "NUMPAD_DEVIDE": 111, "NUMPAD_ENTER": 13, "NUMPAD_SUBTRACT": 109, "PAGE_DOWN": 34, "PAGE_UP": 33, "RIGHT": 39, "SHIFT": 8, "SPACE": 32, "TAB": 9, "UP": 38, "capsLock": 20, "numLock": 114}}, "focusManager": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 8, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 14, "character": 31}]}]}, "statics": {"focusTarget": null, "hoverButton": null}}, "SwtTextArea": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 24, "character": 1}, "arguments": [{"selector": "SwtTextArea", "template": "\n        <textarea    class=\"txtAreaClass\" id=\"{{elementId}}\"   ></textarea> \n      ", "styles": ["\n        .txtAreaClass{\n            height: 100%;\n            width: 100%;\n            display: none !important;        \n        }\n  "]}]}], "members": {"contextMenu": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 68, "character": 5}}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 107, "character": 5}, "arguments": ["tabIndex"]}]}], "selectionBeginIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 110, "character": 5}, "arguments": ["selectionBeginIndex"]}]}], "verticalScrollPosition": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 124, "character": 5}, "arguments": ["verticalScrollPosition"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 136, "character": 5}, "arguments": ["width"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 149, "character": 5}, "arguments": ["height"]}]}], "required": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 161, "character": 5}, "arguments": ["required"]}]}], "doubleClickEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 184, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 199, "character": 5}}]}], "htmlToText": [{"__symbolic": "method"}], "htmlText": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 255, "character": 5}}]}], "verticalScrollPolicy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 277, "character": 5}}]}], "horizontalScrollPolicy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 288, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 300, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 327, "character": 5}}]}], "fontWeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 373, "character": 5}}]}], "fontSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 384, "character": 5}}]}], "textColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 395, "character": 5}}]}], "color": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 406, "character": 5}}]}], "textAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 417, "character": 5}}]}], "backgroundColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 428, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 440, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 440, "character": 86}]}], "ngAfterViewInit": [{"__symbolic": "method"}], "isHTML": [{"__symbolic": "method"}], "onRightClickTextArea": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}]}}, "SwtTabNavigatorHandler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 3, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"emitChangeSource": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "rxjs", "name": "Subject", "line": 9, "character": 42}}, "Listener": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "SwtTabNavigatorHandler"}, "member": "emitChangeSource"}, "member": "asObservable"}}}}, "SwtLoadingImage": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 2, "character": 1}, "arguments": [{"selector": "SwtLoadingImage", "template": "\n      \n          <img class=\"swt-spinner\" src=\"assets/images/Rolling.gif\">\n     \n  ", "styles": ["\n      .swt-spinner {\n              height:18px;\n              margin: auto 0px;\n              vertical-align: baseline;\n      }\n  "]}]}], "members": {"visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 19, "character": 3}, "arguments": ["visible"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 41, "character": 31}]}], "ngOnInit": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}]}}, "SwtCheckBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 9, "character": 1}, "arguments": [{"selector": "SwtCheckBox", "template": "\n  \n     <label   class=\"SwtCheckBox-container\" tabindex=\"-1\" >\n          <input (change)=\"ChangeEventHandler($event)\" \n        \n                 (keydown.Enter)=\"KeyDownEventHandler($event)\"\n                 (click)=\"emitClickEvent($event)\" \n                 type    =\"checkbox\" \n                 class   =\"item\" \n                 tabindex=\"-1\"  >\n          <span    popper=\"{{this.toolTipPreviousValue}}\"\n          [popperTrigger]=\"'hover'\"\n          [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n          [popperPlacement]=\"'bottom'\"\n          [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\" class=\"checkmark\" (keydown.Enter)=\"KeyDownEventHandler($event)\"></span>\n      </label>   \n     \n     ", "styles": ["\n            :host {\n                /*overflow: hidden;*/\n                width: fit-content;\n                outline: none!important;\n                flex-direction: row;\n                box-sizing: border-box;\n                display: flex;\n                place-content: stretch flex-start;\n                align-items: stretch;\n                height:5px;\n                line-height: 22px;\n            }\n            .swtcheck-container{\n               /* margin: 0px 0px 5px 0px;*/\n                outline: none !important;\n             }\n            .cblabel{\n                position: relative;\n                top: -3px;\n             }\n            .disabled{\n                color :#AAB3B3;\n             }\n            /* The container */\n            .SwtCheckBox-container {\n                display: block;\n                position: relative;\n                padding-left: 20px;\n                cursor: pointer;\n                outline: none;\n                font-size: 11px;\n                -webkit-user-select: none;\n                -moz-user-select: none;\n                -ms-user-select: none;\n                user-select: none;\n            }\n            \n            /* Hide the browser's default checkbox */\n            .SwtCheckBox-container input {\n                position: absolute;\n                opacity: 0;\n                cursor: pointer;\n                height: 0;\n                width: 0;\n            }\n            \n            /* Create a custom checkbox */\n            .checkmark {\n                position: absolute;\n                top: 5px;\n                left: 0;\n                height: 13px;\n                width: 13px;\n                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);\n                border:1px solid #B7BABC;\n            }\n            \n            /* On mouse-over, add a grey background color */\n            .SwtCheckBox-container:hover input ~ .checkmark {\n               background-image: -webkit-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -moz-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -ms-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -o-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: linear-gradient(to bottom, #F6FBFF, #E2EEF4);\n                border: 1px solid #009DFF;\n                cursor: default;\n            }\n            \n            /* When the checkbox is checked, add a blue background */\n            .SwtCheckBox-container input:checked ~ .checkmark {\n                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);\n            }\n            .SwtCheckBox-container input:active ~ .checkmark {\n               background-image: -webkit-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -moz-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -ms-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -o-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: linear-gradient(to bottom, #E3F4FF, #9ED9FF);\n            }\n            \n            /* Create the checkmark/indicator (hidden when not checked) */\n            .checkmark:after {\n                content: \"\";\n                position: absolute;\n                display: none;\n            }\n            \n            /* Show the checkmark when checked */\n            .SwtCheckBox-container input:checked ~ .checkmark:after {\n                display: block;\n            }\n            \n            /* Style the checkmark/indicator */\n            .SwtCheckBox-container .checkmark:after {\n                 left: 3px;\n                 top: 0px;\n                 width: 5px;\n                 height: 10px;\n                 border: solid #525960;\n                 border-width: 0 3px 3px 0;\n                 -webkit-transform: rotate(45deg);\n                 -ms-transform: rotate(45deg);\n                 transform: rotate(45deg);\n            }\n           \n            label {\n                font-weight: 100;\n                line-height:12px;\n            }\n            label:hover {\n                cursor: default;\n            }\n            \n         "]}]}], "members": {"ngAfterViewInit": [{"__symbolic": "method"}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 171, "character": 5}, "arguments": ["label"]}]}], "value": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 182, "character": 5}, "arguments": ["value"]}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 200, "character": 5}, "arguments": ["tabIndex"]}]}], "selected": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 219, "character": 5}, "arguments": ["selected"]}]}], "fontWeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 242, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 263, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ChangeEventHandler": [{"__symbolic": "method"}], "emitClickEvent": [{"__symbolic": "method"}], "KeyDownEventHandler": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}]}}, "SwtLabel": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtLabel", "template": "\n        <label  \n               (click)=\"onClick($event)\"\n               class=\"ellipsisEnabled\"\n               #labelDOM>\n        </label>\n    ", "styles": ["\n          :host {\n              outline:none;\n              display: block;\n           }\n           \n           label{\n             font-size:11px;\n             color: black;\n             height: 23px;\n              line-height: 22px;\n             font-family: verdana,helvetica;\n             vertical-align: bottom;\n            /* margin: 0px 0px 5px 0px;*/\n             pointer-events: auto!important; \n           }\n          .ellipsisEnabled{\n             text-overflow: ellipsis;\n             overflow: hidden;\n             white-space: nowrap;\n          }\n          .ellipsisDisabled{\n             text-overflow: clip;\n          }\n   "]}]}], "members": {"labelDOM": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 45, "character": 5}, "arguments": ["labelDOM"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 58, "character": 5}, "arguments": ["width"]}]}], "truncate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 75, "character": 5}, "arguments": ["truncate"]}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 88, "character": 5}, "arguments": ["tabIndex"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 91, "character": 5}, "arguments": ["styleName"]}]}], "textAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 97, "character": 5}, "arguments": ["textAlign"]}]}], "htmlText": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 105, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 124, "character": 5}}]}], "fontSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 146, "character": 5}, "arguments": ["fontSize"]}]}], "fontWeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 156, "character": 5}}]}], "color": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 166, "character": 5}}]}], "buttonMode": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 176, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 208, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "textDictionaryId": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 224, "character": 5}, "arguments": ["textDictionaryId"]}]}], "defaultOnnClick": [{"__symbolic": "method"}], "emtpyOnnClick": [{"__symbolic": "method"}], "onClick": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "HBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 5, "character": 1}, "arguments": [{"selector": "HBox", "styles": ["\n             :host {\n               margin: 0px;\n               display: block;\n               outline: none;\n             }\n             .horizontalLayout {\n               width: 100%;\n               outline: none;\n             }\n             .wrapContent {\n              flex-wrap:wrap;\n             }\n           \n   "], "template": "\n     <div fxLayout=\"row\" #hboxDiv fxLayoutAlign=\"{{horizontalAlign}} {{verticalAlign}} \"  fxLayoutGap=\"{{horizontalGap}}\"  class=\"horizontalLayout\" tabindex=\"-1\">\n        <ng-content></ng-content>\n        <ng-container #_container></ng-container>\n     </div>\n  "}]}], "members": {"hboxDiv": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 31, "character": 2}, "arguments": ["hboxDiv"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 33, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "wrapContent": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 41, "character": 5}}]}]}}, "VBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 4, "character": 1}, "arguments": [{"selector": "VBox", "template": "\n    \n    <div fxLayout=\"column\" fxLayoutAlign=\"{{verticalAlign}} {{horizontalAlign}}\"   class=\"verticalLayout {{styleName}}\" scroll=\"scroll\" tabindex=\"-1\">\n        <ng-content ></ng-content>\n        <ng-container #_container></ng-container>\n    </div>\n  ", "styles": ["  \n    :host {\n      margin:  0px;\n      width: fit-content;\n      outline: none;\n    }\n    .verticalLayout {\n      width: 100%;\n      outline: none;\n    }\n  "]}]}], "members": {"styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 27, "character": 5}, "arguments": ["styleName"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 30, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 30, "character": 99}]}], "ngOnInit": [{"__symbolic": "method"}]}}, "HDividedBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 11, "character": 1}, "arguments": [{"selector": "HDividedBox", "template": "\n         <div #hdividedboxContainer  fxLayout=\"row\" class=\"panel-container\">\n            <div #panelLeft  fxLayout=\"row\" fxLayoutGap=\"{{horizontalGap}}\" class=\"panel-left\" >\n                <ng-content  select=\".left\"></ng-content>\n            </div>\n            <div  class=\"splitter\" #splitter (click)=\"onButtonClickHandler()\"></div>\n            <div #panelRight  fxLayout=\"row\" fxLayoutGap=\"{{horizontalGap}}\" class=\"panel-right\" >\n                <ng-content select=\".right\"></ng-content>\n            </div>\n        </div>\n         \n    ", "styles": ["\n\n        .panel-container {\n            overflow: hidden;\n            background-color: #D6E3FE;\n            xtouch-action: none;\n         }\n\n        .panel-left {\n            flex: 0 0 auto;\n            height:100%;\n            min-height: 20px;\n            min-width: 10px;\n            padding-bottom : 5px;\n        }\n\n        .splitter {\n            flex: 0 0 auto;\n            width: 10px !important;\n            background-image: url(\"assets/images/hresize-handler.png\");\n            background-repeat: no-repeat;\n            background-position: center;\n        }\n\n        .panel-right {\n            flex: 1 1 auto;\n            height:100%;\n            min-height: 20px;\n            min-width: 20px;\n            padding-bottom : 5px;\n        }\n\n\n    "]}]}], "members": {"DIVIDER_DRAG_COMPLETE": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 61, "character": 5}, "arguments": ["DIVIDER_DRAG_COMPLETE"]}]}], "DIVIDER_BUTTON_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 62, "character": 5}, "arguments": ["DIVIDER_BUTTON_CLICK"]}]}], "maxWidthRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 95, "character": 5}}]}], "minWidthRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 102, "character": 5}}]}], "maxWidthLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 109, "character": 5}}]}], "minWidthLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 116, "character": 5}}]}], "hdividedboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 122, "character": 5}, "arguments": ["hdividedboxContainer"]}]}], "panelLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 123, "character": 5}, "arguments": ["panelLeft"]}]}], "panelRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 124, "character": 5}, "arguments": ["panelRight"]}]}], "splitter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 125, "character": 5}, "arguments": ["splitter"]}]}], "resize_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 128, "character": 5}, "arguments": ["resize"]}]}], "resizeStart_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 129, "character": 5}, "arguments": ["resizeStart"]}]}], "resizeStop_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 130, "character": 5}, "arguments": ["resizeStop"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 171, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 180, "character": 5}}]}], "onButtonClickHandler": [{"__symbolic": "method"}], "widthRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 223, "character": 5}}]}], "setWidthRightWithoutEvent": [{"__symbolic": "method"}], "setWidthLeftWithoutEvent": [{"__symbolic": "method"}], "dividersAnimation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 264, "character": 5}}]}], "extendedDividedBox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 274, "character": 5}}]}], "liveDrag": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 285, "character": 5}}]}], "widthLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 298, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 337, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 337, "character": 98}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 337, "character": 142}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "VDividedBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 23, "character": 1}, "arguments": [{"selector": "VDividedBox", "template": "\n        <div #vdividedboxContainer fxLayout=\"column\" class=\"panel-container-vertical\">\n            \n            <div #panelTop fxLayout=\"column\" fxLayoutGap=\"{{verticalGap}}\" class=\"panel-top\">\n                <ng-content select=\".top\"></ng-content>\n            </div>\n            <div class=\"splitter-horizontal\" #splitter  (click)=\"onButtonClickHandler()\" ></div>\n            <div #panelBottom fxLayout=\"column\" fxLayoutGap=\"{{verticalGap}}\" class=\"panel-bottom\">\n                <ng-content select=\".bottom\"></ng-content>\n            </div>\n        </div>", "styles": ["\n        /* vertical panel */\n\n        .panel-container-vertical {\n            height: 100%;\n            overflow: hidden;\n            background-color: #D6E3FE;\n            margin-bottom: 5px;\n         }\n\n        .panel-top {\n            flex: 0 0 auto;\n            width: 100% !important;\n        }\n\n        .ui-resizable-se {\n            display: none;\n        }\n\n        .splitter-horizontal {\n            flex: 0 0 auto;\n            height: 10px !important;\n            background-image: url(\"assets/images/resizer-handler.png\");\n            background-repeat: no-repeat;\n            background-position: center;\n        }\n\n        .panel-bottom {\n            flex: 1 1 auto;\n            width: 100% !important;\n        }\n\n    "]}]}], "members": {"DIVIDER_DRAG_COMPLETE": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 71, "character": 5}, "arguments": ["DIVIDER_DRAG_COMPLETE"]}]}], "DIVIDER_BUTTON_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 72, "character": 5}, "arguments": ["DIVIDER_BUTTON_CLICK"]}]}], "maxHeightBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 103, "character": 5}}]}], "minHeightBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 110, "character": 5}}]}], "maxHeightTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 118, "character": 5}}]}], "minHeightTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 126, "character": 5}}]}], "vdividedboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 132, "character": 5}, "arguments": ["vdividedboxContainer"]}]}], "panelTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 133, "character": 5}, "arguments": ["panelTop"]}]}], "panelBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 134, "character": 5}, "arguments": ["panelBottom"]}]}], "resize_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 137, "character": 5}, "arguments": ["resize"]}]}], "resizeStart_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 138, "character": 5}, "arguments": ["resizeStart"]}]}], "resizeStop_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 139, "character": 5}, "arguments": ["resizeStop"]}]}], "splitter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 141, "character": 5}, "arguments": ["splitter"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 174, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 182, "character": 5}}]}], "onButtonClickHandler": [{"__symbolic": "method"}], "heightBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 232, "character": 5}}]}], "dividersAnimation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 263, "character": 5}}]}], "extendedDividedBox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 273, "character": 5}}]}], "liveDrag": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 284, "character": 5}}]}], "setHeightTopWithoutEvent": [{"__symbolic": "method"}], "setHeightBottomWithoutEvent": [{"__symbolic": "method"}], "heightTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 307, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 340, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "SwtButton": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "SwtButton", "template": "<div \n                    #swtbutton  \n                    class=\"swtbtn minWidthBtn \">\n                    <span class=\"truncate buttonLabel\" ></span>\n               </div>\n    ", "styles": ["\n         :host{\n             outline:none;\n         }\n        .truncate{\n            margin:0px 2px 1px 2px; \n            text-overflow: ellipsis; \n            overflow: hidden; \n            white-space: nowrap;\n            display:block;\n        }\n    "]}]}], "members": {"swtbutton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 37, "character": 5}, "arguments": ["swtbutton"]}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 48, "character": 5}, "arguments": ["tabIndex"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 64, "character": 5}}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 82, "character": 5}, "arguments": ["styleName"]}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 96, "character": 5}}]}], "textDictionaryId": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 108, "character": 5}, "arguments": ["textDictionaryId"]}]}], "buttonMode": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 115, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 124, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 174, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "removeEventsListeners": [{"__symbolic": "method"}], "setButtonState": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}], "adaptValue": [{"__symbolic": "method"}], "hasIcon": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "SwtCanvas": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtCanvas", "template": "\n     <div  fxLayout=\"row\" fxLayoutAlign=\"{{horizontalAlign}} {{verticalAlign}} \" fxLayoutGap=\"{{horizontalGap}}\" class=\"canvasLayout  {{styleName}}\" tabindex=\"-1\">\n        <ng-content></ng-content>\n        <ng-container #_container></ng-container>\n     </div>\n  ", "styles": ["\n             :host {\n               margin: 0px 0px 5px 0px;\n               display: block;\n               outline: none;\n             }\n             .canvasLayout {\n               box-sizing: border-box;\n               width: 100%;\n               outline:none;\n             }\n   "]}]}], "members": {"border": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 31, "character": 5}, "arguments": ["border"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 32, "character": 5}, "arguments": ["styleName"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 34, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngAfterViewInit": [{"__symbolic": "method"}]}}, "SwtComboBox": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "SwtComboBox", "template": "\n       <div class=\"SwtComboBox-container\" \n       popper=\"{{this.toolTipPreviousValue}}\"\n             [popperTrigger]=\"'hover'\"\n             [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n       [popperPlacement]=\"'bottom'\" >\n          <div #inputGroup class=\"input-group\">\n            <input #filter type=\"text\"\n            (click)=inputClickHandler($event)\n            [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n            (change)=\"inputChangeHandler($event)\"\n            placeholder=\"{{prompt}}\"\n            [style.background-image]=\"this.getbackGroundImange()\"\n            [class.requiredInput]= \"required==true && enabled==true && !this.filter.value\"\n            class=\"form-control swtcombobox-filter-input\" >\n            <div class=\"input-group-addon\"><i class=\"glyphicon glyphicon-triangle-bottom\"></i></div> \n          </div>\n          <div #dropDownContainer class=\"swtcomboBox-dropDown\" >\n              <ul #listitem class=\"swtcomboBox-dropDown-ul\">\n                  <li #dropDownli *ngFor=\"let item of dataProvider let count = index\" \n                      [ngClass]=\"{'selected':count == 0}\"\n                      class=\"swtcomboBox-dropDown-li\"\n                      (click)=\"selectItem(item, true)\">\n                        <a *ngIf=\"item.iconImage\">\n                            <img src=\"{{item.iconImage }}\" alt=\"Facebook Icon\"  />\n                        </a>\n                        <option class=\"combo-option\" value=\"{{ item.value }}\" style=\"display: inline-block;\"> \n                        {{ item.content }}\n                      </option>{{ showDescriptionInDropDown && item.value? '  '+item.value : '' }}\n                  </li>\n                  <!--<li *ngIf=\"_exist\">{{ notFound }}</li>-->\n              </ul>\n          </div>\n        </div>\n  ", "styles": ["\n      .SwtComboBox-container {\n         /*width: 260px;  this width must be updated with jquery*/\n            margin: 0 5px 5px 0;\n      }\n\n      .input-group {\n         width: 100%;\n      }\n\n      .form-control {\n          background-repeat: no-repeat;\n          background-position: center; \n          background-size: 100% 100%;\n          padding: 0px 0px 0px 10px;\n          background-color: #FFF;\n            z-index: 0;\n      }\n\n      .disable {\n          color: #919999;\n      }\n\n      .swtcomboBox-dropDown-ul {\n          padding: 0px;\n          width: auto;\n          background-color: white;\n          margin: 0px;\n          max-height: 150px;\n          overflow-y: auto;\n          overflow-x: hidden;\n          padding-bottom: 3px;\n          cursor: default;\n          word-wrap: break-word;\n      }\n\n      .swtcomboBox-dropDown-li {\n         list-style-type: none;\n         width: 100%;\n         height: auto;\n         padding-left: 10px;\n         font-size: 11px;\n         height: 21px;\n         line-height: 21px;\n      }\n\n      .swtcomboBox-dropDown-li:hover {\n         background-color: #0000A0;\n         color: #FFF;\n      }\n      \n      .combo-option:hover {\n        background-color: #0000A0;\n        color: #FFF;\n      }\n\n      li.selected {\n        background-color: #0000A0;\n        color: #FFF;\n      }\n\n      .swtcomboBox-selectedItem {\n          background-color: #0000A0;\n          color: #FFF;\n      }\n\n      .swtcomboBox-dropDown-li> a {\n          font-family: verdana,helvetica;\n          font-size: 11px;\n      }\n\n      .input-group-addon {\n          width: 22px;\n          height:22px;\n            border-top: 1px solid #7F9DB9;\n          border-right:1px solid #637A90;\n          border-bottom:1px solid #415160;\n            border-left: 1px solid #637A90;\n          padding: 0px;\n          margin: 0px;\n          font-size: 8px;\n            background-image: -webkit-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -moz-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -ms-linear-gradient(top, #DBF1FF, #A7C6DE);\n          background-image: -o-linear-gradient(top, #DBF1FF, #A7C6DE);\n            background-image: linear-gradient(to bottom, #DBF1FF, #A7C6DE);\n      }\n\n      .input-group-addon-disable {\n          width: 22px;\n          height:22px;\n          border-top:1px solid #A5C4DC;\n          border-right:1px solid #96B1C6;\n          border-bottom:1px solid #869EB0;\n          border-left:1px solid #96B1C6;\n          padding: 0px;\n          margin: 0px;\n          font-size: 8px;\n          background-image: -webkit-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -moz-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -ms-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: -o-linear-gradient(top, #CAEBFE, #B1D0E7);\n          background-image: linear-gradient(to bottom, #CAEBFE, #B1D0E7);\n      }\n\n      .glyphicon {\n         font-size: 9px !important;\n         position: relative;\n         top: 2px;\n         cursor: default;\n      }\n\n      .swtcombobox-filter-input {\n        height: 22px;\n        font-size: 12px;\n        border-radius: 0px;\n        border-top: 1px solid #4C5E6F;\n        border-right: 1px solid #9FB5CA;\n        border-bottom:1px solid #B2C4D5;\n        border-left:1px solid #9FB5CA;\n        font-family: verdana,helvetica;\n            font-size: 11px;\n      }\n\n      .swtcomboBox-dropDown {\n         position: fixed;\n         z-index: 999;\n         background-color: #FFF;\n         display: none;\n         border: 1px solid #D9D9D9;\n         box-shadow: 0px 4px 5px #999999;\n         width: 260px; /* this width must be updated with jquery*/\n      }\n    \n  "]}]}], "members": {"onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 188, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 189, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "mouseWeelEventHandler": [{"__symbolic": "method", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostListener", "line": 191, "character": 5}, "arguments": ["window:mousewheel", ["$event.target"]]}]}], "dataLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 254, "character": 5}, "arguments": ["dataLabel"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 270, "character": 5}}]}], "toolTipPreviousValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 290, "character": 5}}]}], "prompt": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 304, "character": 5}, "arguments": ["prompt"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 307, "character": 5}, "arguments": ["width"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 309, "character": 5}, "arguments": ["height"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 311, "character": 5}, "arguments": ["id"]}]}], "required": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 338, "character": 5}, "arguments": ["required"]}]}], "inputClickHandler": [{"__symbolic": "method"}], "inputChangeHandler": [{"__symbolic": "method"}], "dataProvider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 393, "character": 5}}]}], "open_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 430, "character": 5}, "arguments": ["open"]}]}], "inputClick_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 432, "character": 5}, "arguments": ["inputClick"]}]}], "onTextChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 434, "character": 5}, "arguments": ["onTextChange"]}]}], "close_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 436, "character": 5}, "arguments": ["close"]}]}], "focus_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 438, "character": 5}, "arguments": ["focus"]}]}], "focusout_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 440, "character": 5}, "arguments": ["focusout"]}]}], "change_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 442, "character": 5}, "arguments": ["change"]}]}], "filter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 444, "character": 5}, "arguments": ["filter"]}]}], "inputGroup": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 446, "character": 5}, "arguments": ["inputGroup"]}]}], "listitem": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 448, "character": 5}, "arguments": ["listitem"]}]}], "dropDownli": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 450, "character": 5}, "arguments": ["dropDownli"]}]}], "dropDownContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 452, "character": 5}, "arguments": ["dropDownContainer"]}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 455, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 493, "character": 5}}]}], "shiftUp": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 513, "character": 5}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 522, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 546, "character": 28}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "onArrowClick": [{"__symbolic": "method"}], "filterDropDown": [{"__symbolic": "method"}], "fillSelectedIndexWithFiltertext": [{"__symbolic": "method"}], "showAlert": [{"__symbolic": "method"}], "setFilterFocus": [{"__symbolic": "method"}], "showDropDown": [{"__symbolic": "method"}], "hideDropDown": [{"__symbolic": "method"}], "addItem": [{"__symbolic": "method"}], "addItemAt": [{"__symbolic": "method"}], "selectItem": [{"__symbolic": "method"}], "updateSelectedItem": [{"__symbolic": "method"}], "setPrompt": [{"__symbolic": "method"}], "setComboData": [{"__symbolic": "method"}], "setComboDataAndForceSelected": [{"__symbolic": "method"}], "highlightItem": [{"__symbolic": "method"}], "getIndexOf": [{"__symbolic": "method"}], "scrollToBottom": [{"__symbolic": "method"}], "reverse": [{"__symbolic": "method"}], "scrollToTop": [{"__symbolic": "method"}], "appendOption": [{"__symbolic": "method"}], "removeAll": [{"__symbolic": "method"}], "removeItemAt": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "getbackGroundImange": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}]}}, "SwtDataExport": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 17, "character": 1}, "arguments": [{"selector": "DataExport", "template": "\n  <div>\n\t\t\t<SwtComboBox id=\"exportDataComponent\" (inputClick)=\"onDataExportClick($event)\" \n\t\t\t(change)=\"onDataExportChange($event)\" editable=\"false\" #exportDataComponent width=\"43\"></SwtComboBox>\n  </div>\n  ", "styles": ["\n  "]}]}], "members": {"exportDataComponent": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 31, "character": 2}, "arguments": ["exportDataComponent"]}]}], "ngAfterViewInit": [{"__symbolic": "method"}], "onDataExportClick": [{"__symbolic": "method"}], "onDataExportChange": [{"__symbolic": "method"}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 82, "character": 2}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 103, "character": 68}]}], "ngOnInit": [{"__symbolic": "method"}], "typeFromIndex": [{"__symbolic": "method"}], "deepCopy": [{"__symbolic": "method"}], "convertArrayToXML": [{"__symbolic": "method"}], "convertArraysToXML": [{"__symbolic": "method"}], "convertData": [{"__symbolic": "method"}]}}, "SwtDateField": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 21, "character": 1}, "arguments": [{"selector": "SwtDateField", "template": "\n          <input popper=\"{{this.toolTipPreviousValue}}\"\n          [popperTrigger]=\"'hover'\"\n          [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n          [popperPlacement]=\"'bottom'\"\n          [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\" selector=\"SwtDatefield\" #datefield type=\"text\" class=\"mdatepicker\" tabindex=\"{{tabIndex}}\">\n  ", "styles": ["\n      .mdatepicker {\n           height: 22px;\n           border-left:1px solid #D3D5D6;\n           border-right:1px solid #D3D5D6;\n           border-bottom:1px solid #D3D5D6;\n           border-top: 1px solid #6D6F70;\n           padding: 0px 5px 0px 5px;\n           font-size: 11px;\n           font-family: sans-serif;\n           font-weight: normal;\n           margin: 0px 5px 5px 0px !important;\n           color: #000; /*this line is added to set the color of datefield as black when using SwtFieldSet*/\n      }\n\n      .hide {\n           display: none;\n      }\n  "]}]}], "members": {"onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 52, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 53, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "datefield": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 54, "character": 5}, "arguments": ["datefield"]}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 56, "character": 5}, "arguments": ["tabIndex"]}]}], "restrict": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 58, "character": 5}, "arguments": ["restrict"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 61, "character": 6}, "arguments": ["toolTip"]}]}], "textDictionaryId": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 75, "character": 5}, "arguments": ["tooltipDictionaryId"]}]}], "toolTipPreviousValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 81, "character": 5}}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 92, "character": 5}, "arguments": ["id"]}]}], "openEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 106, "character": 5}, "arguments": ["open"]}]}], "closeEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 107, "character": 5}, "arguments": ["close"]}]}], "keyDownEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 108, "character": 5}, "arguments": ["keyDown"]}]}], "changeEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 109, "character": 5}, "arguments": ["change"]}]}], "focusEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 110, "character": 5}, "arguments": ["focus"]}]}], "focusOutEventOutPut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 111, "character": 5}, "arguments": ["focusOut"]}]}], "keyFocusChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 112, "character": 5}, "arguments": ["keyFocus<PERSON>hange"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 113, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 113, "character": 99}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 122, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 143, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 163, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 178, "character": 5}}]}], "textAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 232, "character": 5}}]}], "selectableRange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 282, "character": 5}}]}], "showToday": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 366, "character": 5}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 382, "character": 5}, "arguments": ["visible"]}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}], "parseDate": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "formatDate": [{"__symbolic": "method"}], "openDropDown": [{"__symbolic": "method"}], "getAbbreviationFromDate": [{"__symbolic": "method"}], "getDate": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "updateDate": [{"__symbolic": "method"}], "loadingZero": [{"__symbolic": "method"}], "spliter": [{"__symbolic": "method"}], "isValidDate": [{"__symbolic": "method"}]}}, "SwtHelpButton": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtButton"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "SwtHelpButton", "template": " \n         <div   \n                #swtbutton  \n                (click)    =\"doHelp()\" \n                class=\"helpIcon inline-field\">\n                <span class=\"truncate buttonLabel\" ></span>\n         </div>\n    ", "styles": ["\n    \n             :host{\n                 outline:none;\n             }\n            .helpIcon    {\n                height: 16px; \n                width: 16px;                     \n                border: 0;\n                overflow: hidden;\n                color: transparent;\n                background:transparent url('assets/images/swtHelp.png') no-repeat;                  \n                border-radius: 15px;   \n                cursor: pointer;\n            }            \n             .helpIcon:hover{\n                 background:transparent url('assets/images/swtHelpOver.png') no-repeat;\n              }\n             .helpIcon:focus {\n                outline: 0px auto -webkit-focus-ring-color;\n                background:transparent url('assets/images/swtHelpDown.png') no-repeat;\n             }\n            .helpIconDisabled {\n                height: 16px; \n                width: 16px;                     \n                border: 0;\n                overflow: hidden;\n                color: transparent;\n                background:transparent url('assets/images/swtHelp.png') no-repeat;                  \n                border-radius: 15px;   \n                opacity: 0.6;\n                pointer-events: none;    \n                cursor: default;      \n             }      \n            /*========================================= Styling Tootltip  ======================================*/\n            \n             :host >>> .tooltip-inner {\n                 background-color: rgba(250, 250, 230, 1);\n                 font-size:9px;\n                 color: #0b333c;\n                 border: 1px solid #0b333c;\n                 box-shadow: 1px 2px 5px #888888;\n             }\n         "]}]}], "members": {"toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 67, "character": 5}, "arguments": ["toolTip"]}]}], "helpFile": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 68, "character": 5}, "arguments": ["helpFile"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 75, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "doHelp": [{"__symbolic": "method"}]}}, "SwtNumericInput": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtTextInput"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtNumericInput", "template": "\n     <input #textfield\n     popper=\"{{this.toolTipPreviousValue}}\"\n     [popperTrigger]=\"'hover'\"\n     [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n     [popperPlacement]=\"'bottom'\"\n     [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n            type=\"number\"\n            (paste)=\"onPaste($event)\"\n            class=\"textinput \"\n            [class.requiredInput]= \"this.required==true && !this.text  && enabled==true\"\n     />\n  ", "styles": ["\n        :host {\n            outline:none;\n        }\n           input {\n               height: 23px;\n               line-height:23px;\n               padding-left: 3px;\n               padding-right: 3px;\n               border: 1px solid #7f9db9; \n               width: 100%;\n               cursor: text;\n               color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/\n           /* margin: 0px 5px 5px 0px;*/\n          }\n\n           input:disabled {\n               color: #E5E5E5;\n           }\n\n          .ng-invalid {\n             border: 2px solid red; \n             color: red;\n          }\n\n          span {\n              color: red;\n              font-weight: bold;\n              font-size: 11px;\n          }\n      "]}]}], "members": {"maximum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 60, "character": 5}, "arguments": ["maximum"]}]}], "minimum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 76, "character": 5}, "arguments": ["minimum"]}]}], "ngOnInit": [{"__symbolic": "method"}], "onValueChange": [{"__symbolic": "method"}], "checkValidValue": [{"__symbolic": "method"}]}}, "SwtAdvSlider": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 16, "character": 1}, "arguments": [{"selector": "SwtAdvSlider", "template": "\n    <HBox  [id]=\"generateDynamicId()\" width=\"220\" height=\"100%\"> \n        <input class=\"sliderInput\" #hboxContainer  #sliderFromInput  id=\"sliderFromInput\"  [disabled]=\"false\">\n        <input #slider type=\"slider\"  name=\"area\" value=\"0;100\" />\n        <input class=\"sliderInput\" #sliderToInput id=\"sliderToInput\"  [disabled]=\"false\">\n        </HBox>\n  ", "styles": ["\n        .sliderInput{\n            width:40px;\n            height:20px;\n            font-size : 11px !important;\n        }\n      \n      "]}]}], "members": {"sliderFromInput": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 35, "character": 5}, "arguments": ["sliderFromInput"]}]}], "sliderToInput": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 36, "character": 5}, "arguments": ["sliderToInput"]}]}], "ZOOM_EVENT_ZOOMED": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 37, "character": 5}, "arguments": ["ZOOM_EVENT_ZOOMED"]}]}], "hboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 38, "character": 5}, "arguments": ["hboxContainer"]}]}], "slider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 39, "character": 5}, "arguments": ["slider"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 42, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "defaultFunction": [{"__symbolic": "method"}], "defaultReturnFunction": [{"__symbolic": "method"}], "sliderComp": [{"__symbolic": "method"}], "maximum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 245, "character": 5}, "arguments": ["maximum"]}]}], "initTextInputs": [{"__symbolic": "method"}], "minimum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 279, "character": 5}, "arguments": ["minimum"]}]}], "generateDynamicId": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "calculateTime": [{"__symbolic": "method"}], "refreshSliderValues": [{"__symbolic": "method"}], "validateTime": [{"__symbolic": "method"}]}}, "SwtEditableComboBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtEditableComboBox", "template": "\n       <div #SwtComboBoxcontainer class=\"SwtComboBox-container\">\n       <select id='editableSelect' class='editableCombo' #editableSelect>\n        </select>\n        </div>\n  ", "styles": ["\n  "]}]}], "members": {"editableSelect": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 19, "character": 5}, "arguments": ["editableSelect"]}]}], "swtComboBoxcontainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 20, "character": 5}, "arguments": ["SwtComboBoxcontainer"]}]}], "addItem": [{"__symbolic": "method"}], "removeItem": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 91, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}]}}, "ILMLineChart": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 26, "character": 1}, "arguments": [{"selector": "ILMLineChart", "template": "<!--<iframe title=\"charts\" class=\"iframeiframe\" #iframeContaier src=\"http://localhost:8080/swallowtech/charts.htm\" style=\"position: absolute;left: 20px;right: 20px; height: 150px;width: 150px;\" seamless></iframe>-->\r\n<swt-ilm-chart width=\"100%\" height=\"300\"  #chartElement ></swt-ilm-chart>", "styles": [""]}]}], "members": {"chartElement": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 33, "character": 3}, "arguments": ["chartElement"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 154, "character": 28}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 154, "character": 97}]}], "receiveMessage": [{"__symbolic": "method"}], "removeAllSeries": [{"__symbolic": "method"}], "resetILMLineChart": [{"__symbolic": "method"}], "addChart": [{"__symbolic": "method"}], "createThreshold": [{"__symbolic": "method"}], "addThreshold": [{"__symbolic": "method"}], "removeSeries": [{"__symbolic": "method"}], "createSeries": [{"__symbolic": "method"}], "removeFromDataSet": [{"__symbolic": "method"}], "updateLineLegend": [{"__symbolic": "method"}], "updateThresholdStyleColor": [{"__symbolic": "method"}], "updateAreaLegend": [{"__symbolic": "method"}], "updateLiquidityZonesLegend": [{"__symbolic": "method"}], "updateMetadatas": [{"__symbolic": "method"}], "updateDataprovider": [{"__symbolic": "method"}], "updateDataProviderFor": [{"__symbolic": "method"}], "showHideThreshold": [{"__symbolic": "method"}], "getThresholdsColorStyle": [{"__symbolic": "method"}], "switchDataProvider": [{"__symbolic": "method"}], "calculateLiquidityZonesLimites": [{"__symbolic": "method"}], "removeLiquidityRegion": [{"__symbolic": "method"}], "drawLiquidityZones": [{"__symbolic": "method"}], "drawLiquidityRegion": [{"__symbolic": "method"}], "callMethodInIframe": [{"__symbolic": "method"}], "getLiquidityRegionColor": [{"__symbolic": "method"}], "updateStyleMetadata": [{"__symbolic": "method"}], "getChartStyleSeriesStyleAsString": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}]}, "statics": {"ACCUM_ACTUAL_INFLOW_TAG": "aac", "ACCUM_ACTUAL_OUTFLOWS_TAG": "aad", "ACCUM_FORECAST_OUTFLOWS_TAG": "afd", "ACCUM_FORECAST_INFLOWS_TAG": "afc", "ACTUAL_BALANCE_TAG": "ab", "FORECAST_BALANCE_TAG": "fbb", "FORECAST_BAL_INC_ACTUALS_TAG": "fbia", "THRESHOLD_TYPE": ["min1", "min2", "max1", "max2"], "SERIES_LIST": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Array"}, "arguments": ["timeSlot", "timeSlotE", "afd", "afc", "aad", "aac", "ab", "fbb", "fbia", "sumExSOD", "sumForcastSOD"]}, "ARRAY_COLLECTION_FROM_SERIES": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ILMLineChart"}, "member": "SERIES_LIST"}, "LIQUIDITY_ZONES_LIST": ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE"], "SERIES_IGNORE_HIGHLIGH": ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "ILMSeriesLiveValue": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 10, "character": 1}, "arguments": [{"selector": "ILMSeriesLiveValue", "template": "\n         <HBox #hboxContainer  width=\"100%\"> \n            <div    #circle class='circle' ></div>\n            <div class='labelValue' #labelValue >{{seriesValue}}</div>\n        </HBox>\n        ", "styles": ["\n\n        .labelValue{\n            position:relative;\n            font-size:11px;\n            top:-2px;\n        }\n        .timeValue{\n            padding-right :10px !important;\n        }\n        .circle{\n            margin-right:2px !important;\n        }\n        \n        .bg-CONT_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_BOLD_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -74px;\n        }\n        \n        .bg-CONT_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -74px;\n        }\n        \n        .bg-CONT_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -74px;\n        }\n        \n        .bg-DASHED_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -10px;\n        }\n        \n        .bg-DASHED_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -42px;\n        }\n        \n        .bg-DASHED_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -74px;\n        }\n        \n        .bg-DASHED_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -10px;\n        }\n        \n        .bg-DOTTED_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -42px;\n        }\n        \n        .bg-DOTTED_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -74px;\n        }\n        \n        .bg-DOTTED_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -106px;\n        }\n        \n        .bg-DOTTED_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -138px;\n        }\n        "], "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 157, "character": 25}, "member": "OnPush"}}]}], "members": {"hboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 160, "character": 5}, "arguments": ["hboxContainer"]}]}], "circle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 161, "character": 5}, "arguments": ["circle"]}]}], "labelValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 162, "character": 5}, "arguments": ["labelValue"]}]}], "seriesStyle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 198, "character": 5}, "arguments": ["seriesStyle"]}]}], "seriesValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 228, "character": 5}, "arguments": ["seriesValue"]}]}], "ngOnInit": [{"__symbolic": "method"}], "isTimeLiveItem": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 248, "character": 5}}]}], "removeLiveValue": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 261, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 261, "character": 92}]}]}}, "ProcessStatusBox": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 9, "character": 1}, "arguments": [{"selector": "ProcessStatusBox", "template": "\n    <img class=\"processStatusBox\" #image src=\"assets/images/green.png\">\n  ", "styles": ["\n        .processStatusBox {\n                height:20px;\n                width : 20px;\n                margin: auto 0px;\n                box-shadow: 1px 1px 1px 1px #888888;\n                border: 1px solid white;\n        }\n      "]}]}], "members": {"image": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 25, "character": 5}, "arguments": ["image"]}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "createTooltip": [{"__symbolic": "method"}], "removeTooltip": [{"__symbolic": "method"}], "setCalculatingState": [{"__symbolic": "method"}], "setRed": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 92, "character": 71}]}], "setStyleFuction": [{"__symbolic": "method"}]}}, "CheckBoxLegendItem": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "CheckBoxLegendItem", "template": "\n         <HBox paddingLeft=\"5\" #hboxContainer  width=\"100%\" height=\"26\"> \n         <SwtCheckBox  selected='{{selected}}' (change)=\"checkboxChanged($event)\" class='checkBoxObject'  #checkBox></SwtCheckBox>\n            <div    #square class='square' (click)='legendItemClicked($event)'></div>\n            <div class='labelValue' #labelValue (click)='legendItemClicked($event)' > {{ getLiveValue() }}</div>\n        </HBox>\n        ", "styles": [" \n            .checkBoxObject{\n                margin-right : 0px !important;\n            }\n            .labelValue{\n                width :1px;\n                padding-top:2px;\n                padding-left:5px;\n                position:relative;\n                font-size:11px;\n                cursor: default;\n                white-space: nowrap;\n            }\n            .square{\n                margin-right:2px !important;\n                margin-top:3px;\n                cursor: default;\n            }\n            \n            .bg-DOTTED_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -10px;\n            }\n            .bg-CONT_AREA_ANTIQUE_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -10px;\n            }\n            .bg-CONT_AREA_APRICOT_PEACH {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -46px;\n            }\n            .bg-CONT_AREA_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -46px;\n            }\n            .bg-CONT_AREA_BLIZZARD_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -10px;\n            }\n            .bg-CONT_AREA_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -46px;\n            }\n            .bg-CONT_AREA_COTTON_CANDY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -82px;\n            }\n            .bg-CONT_AREA_DARK_SALMON {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -82px;\n            }\n            .bg-CONT_AREA_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -82px;\n            }\n            .bg-CONT_AREA_GREY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -118px;\n            }\n            .bg-CONT_AREA_INDIAN_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -118px;\n            }\n            .bg-CONT_AREA_LIGHT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -118px;\n            }\n            .bg-CONT_AREA_LIGHT_CYAN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -10px;\n            }\n            .bg-CONT_AREA_LIGHT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -46px;\n            }\n            .bg-CONT_AREA_LIGHT_GREY {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -82px;\n            }\n            .bg-CONT_AREA_LIME {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -118px;\n            }\n            .bg-CONT_AREA_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -154px;\n            }\n            .bg-CONT_AREA_NAVAJO_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -154px;\n            }\n            .bg-CONT_AREA_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -154px;\n            }\n            .bg-CONT_AREA_PEACH_PUFF {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -154px;\n            }\n            .bg-CONT_AREA_PINK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -10px;\n            }\n            .bg-CONT_AREA_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -46px;\n            }\n            .bg-CONT_AREA_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -82px;\n            }\n            .bg-CONT_AREA_ROSE_FOG {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -118px;\n            }\n            .bg-CONT_AREA_STEEL_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -154px;\n            }\n            .bg-CONT_AREA_VIOLET {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -190px;\n            }\n            .bg-CONT_AREA_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -190px;\n            }\n            .bg-CONT_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -190px;\n            }\n            .bg-CONT_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -190px;\n            }\n            .bg-CONT_SEGMENT_BOLD_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -190px;\n            }\n            .bg-CONT_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -10px;\n            }\n            .bg-CONT_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -46px;\n            }\n            .bg-CONT_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -82px;\n            }\n            .bg-CONT_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -118px;\n            }\n            .bg-CONT_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -154px;\n            }\n            .bg-CONT_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -190px;\n            }\n            .bg-DASHED_AQUA_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -226px;\n            }\n            .bg-DASHED_AREA_PERANO {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -226px;\n            }\n            .bg-DASHED_BLUE_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -226px;\n            }\n            .bg-DASHED_CORAL_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -226px;\n            }\n            .bg-DASHED_DEEP_PINK_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -226px;\n            }\n            .bg-DASHED_GOLDEN_ROD_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -226px;\n            }\n            .bg-DASHED_GREEN_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -262px;\n            }\n            .bg-DASHED_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -262px;\n            }\n            .bg-DASHED_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -262px;\n            }\n            .bg-DASHED_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -262px;\n            }\n            .bg-DASHED_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -262px;\n            }\n            .bg-DASHED_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -260px -262px;\n            }\n            .bg-DASHED_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -10px;\n            }\n            .bg-DASHED_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -46px;\n            }\n            .bg-DASHED_SEGMENT_YELLOW {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -82px;\n            }\n            .bg-DOTTED_GREEN_YELLOW_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -118px;\n            }\n            .bg-DOTTED_INDIAN_RED_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -154px;\n            }\n            .bg-DOTTED_MAGENTA_AREA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -190px;\n            }\n            .bg-DOTTED_SEGMENT_BLACK {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -226px;\n            }\n            .bg-DOTTED_SEGMENT_BLUE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -310px -262px;\n            }\n            .bg-DOTTED_SEGMENT_GREEN {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -298px;\n            }\n            .bg-DOTTED_SEGMENT_MAGENTA {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -298px;\n            }\n            .bg-DOTTED_SEGMENT_ORANGE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -110px -298px;\n            }\n            .bg-DOTTED_SEGMENT_PURPLE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -160px -298px;\n            }\n            .bg-DOTTED_SEGMENT_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -298px;\n            }\n        "], "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 285, "character": 25}, "member": "OnPush"}}]}], "members": {"hboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 288, "character": 5}, "arguments": ["hboxContainer"]}]}], "square": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 289, "character": 5}, "arguments": ["square"]}]}], "checkBox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 290, "character": 5}, "arguments": ["checkBox"]}]}], "labelValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 291, "character": 5}, "arguments": ["labelValue"]}]}], "getLiveValue": [{"__symbolic": "method"}], "liveValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 360, "character": 5}, "arguments": ["liveValue"]}]}], "highlight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 372, "character": 5}, "arguments": ["highlight"]}]}], "checkboxChanged": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "yField": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 427, "character": 5}, "arguments": ["highlight"]}]}], "selected": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 441, "character": 5}, "arguments": ["selected"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 450, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 450, "character": 92}]}], "legendItemClicked": [{"__symbolic": "method"}]}}, "AssetsLegendItem": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 10, "character": 1}, "arguments": [{"selector": "AssetsLegendItem", "template": "\n         <HBox #hboxContainer  width=\"210\" height=\"25\"> \n            <div    #square class='square' ></div>\n            <div class='assetLabel' #assetLabel ></div>\n            <div class='assetslabelValue' #assetslabelValue ></div>\n        </HBox>\n        ", "styles": ["\n            .assetLabel{\n                padding-top:2px;\n                padding-left:5px;\n                position:relative;\n                font-size:11px;\n                width: 100px;\n            }\n            .assetslabelValue{\n                padding-top:2px;\n                padding-left:5px;\n                position:relative;\n                font-size:11px;\n                width: 40%;\n                text-align: right;\n            }\n            .square{\n                margin-right:2px !important;\n                margin-top:3px;\n            }\n            \n            .bg-CONT_AREA_ANTIQUE_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -10px;\n            }\n            .bg-CONT_AREA_APRICOT_PEACH {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -10px -46px;\n            }\n\n            .bg-CONT_AREA_NAVAJO_WHITE {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -60px -154px;\n            }\n\n            .bg-CONT_AREA_ROSE_FOG {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -118px;\n            }\n\n\n            .bg-CONT_SEGMENT_BOLD_RED {\n                width: 30px; height: 16px;\n                background: url('assets/ILM/sprites/legendRectangle.png') -210px -190px;\n            }\n\n\n\n\n\n        "], "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 70, "character": 25}, "member": "OnPush"}}]}], "members": {"hboxContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 73, "character": 5}, "arguments": ["hboxContainer"]}]}], "square": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 74, "character": 5}, "arguments": ["square"]}]}], "assetLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 75, "character": 5}, "arguments": ["assetLabel"]}]}], "assetslabelValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 76, "character": 5}, "arguments": ["assetslabelValue"]}]}], "labelValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 97, "character": 5}, "arguments": ["labelValue"]}]}], "seriesStyle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 107, "character": 5}, "arguments": ["seriesStyle"]}]}], "ngOnInit": [{"__symbolic": "method"}], "yField": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 149, "character": 5}, "arguments": ["yField"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 157, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 157, "character": 92}]}]}}, "ConfigurableToolTip": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 13, "character": 1}, "arguments": [{"selector": "ConfigurableToolTip", "template": "\n        <VBox  paddingLeft=\"3\"  paddingRight=\"3\" paddingTop=\"3\" #customTooltip width=\"100%\" height=\"100%\" verticalGap=\"2\"> \n            <HBox> <SwtLabel  #dataForLabel text='Date For'    fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #dataForLabelValue    fontWeight=\"normal\">  </SwtLabel> </HBox>\n            <HBox> \n            <VBox  width=\"55%\" style=\"border-right: 1px solid black\">\n            <HBox ><SwtLabel  #labelnumberAccounts text='Number of accounts'    fontWeight=\"normal\" width=\"70%\">  </SwtLabel> <SwtLabel  #labelnumberAccountsValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox ><SwtLabel  #labelnewDataExistFor text='New data exist for'    fontWeight=\"normal\" width=\"70%\">  </SwtLabel> <SwtLabel #labelnewDataExistForValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            </VBox>\n            \n            <VBox  width=\"45%\">\n            <HBox> <SwtLabel  #labelincomplete text='Incomplete'  width=\"50%\"  fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #labelincompleteValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox> <SwtLabel  #labelinconsistent text='Inconsistent'  width=\"50%\"   fontWeight=\"normal\">  </SwtLabel> <SwtLabel #labelinconsistentValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            </VBox>\n            </HBox>\n            <HBox> <SwtLabel  #labelLastUpdate text='Last Update'    fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #labelLastUpdateValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox  horizontalAlign=\"center\">\n                <SwtLabel  id=\"requestRecalculation\" #requestRecalculation \n                color=\"black\" fontWeight=\"bold\" label=\"\">\n            </SwtLabel>\n             </HBox>\n        </VBox>\n  ", "styles": ["\n      "]}]}], "members": {"customTooltip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 41, "character": 5}, "arguments": ["customTooltip"]}]}], "dataForLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 42, "character": 5}, "arguments": ["dataForLabel"]}]}], "dataForLabelValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 43, "character": 5}, "arguments": ["dataForLabelValue"]}]}], "labelnumberAccounts": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 45, "character": 5}, "arguments": ["labelnumberAccounts"]}]}], "labelnumberAccountsValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 46, "character": 5}, "arguments": ["labelnumberAccountsValue"]}]}], "labelnewDataExistFor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 48, "character": 5}, "arguments": ["labelnewDataExistFor"]}]}], "labelnewDataExistForValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 49, "character": 5}, "arguments": ["labelnewDataExistForValue"]}]}], "labelincomplete": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 51, "character": 5}, "arguments": ["labelincomplete"]}]}], "labelincompleteValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 52, "character": 5}, "arguments": ["labelincompleteValue"]}]}], "labelinconsistent": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 54, "character": 5}, "arguments": ["labelinconsistent"]}]}], "labelinconsistentValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 55, "character": 5}, "arguments": ["labelinconsistentValue"]}]}], "labelLastUpdate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 57, "character": 5}, "arguments": ["labelLastUpdate"]}]}], "labelLastUpdateValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 58, "character": 5}, "arguments": ["labelLastUpdateValue"]}]}], "requestRecalculation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 59, "character": 5}, "arguments": ["requestRecalculation"]}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 85, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "boxRollOutEventListner": [{"__symbolic": "method"}], "createCustomTip": [{"__symbolic": "method"}], "recalculateDataAlert": [{"__symbolic": "method"}], "alertListener": [{"__symbolic": "method"}]}}, "SeriesStyle": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}]}}, "SeriesStyleProvider": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"_instance": null, "LINE": 0, "LINE_SOLID": 1, "LINE_DOTTED": 2, "LINE_DASHED": 3, "AREA": 10, "AREA_SOLID": 11, "AREA_DASHED": 12, "AREA_DASHED45": 13, "AREA_DASHED135": 14, "AREA_DOTTED": 15, "CONT_AREA_BLIZZARD_BLUE": "CONT_AREA_BLIZZARD_BLUE", "CONT_AREA_COTTON_CANDY": "CONT_AREA_COTTON_CANDY", "DASHED_AREA_PERANO": "DASHED_AREA_PERANO", "DASHED_AREA_SEA_PINK": "DASHED_AREA_SEA_PINK", "CONT_AREA_BLACK": "CONT_AREA_BLACK", "CONT_AREA_GREY": "CONT_AREA_GREY", "CONT_AREA_LIGHT_GREY": "CONT_AREA_LIGHT_GREY", "CONT_AREA_RED": "CONT_AREA_RED", "CONT_AREA_INDIAN_RED": "CONT_AREA_INDIAN_RED", "CONT_AREA_PINK": "CONT_AREA_PINK", "CONT_AREA_ORANGE": "CONT_AREA_ORANGE", "CONT_AREA_PEACH_PUFF": "CONT_AREA_PEACH_PUFF", "CONT_AREA_YELLOW": "CONT_AREA_YELLOW", "CONT_AREA_GREEN": "CONT_AREA_GREEN", "CONT_AREA_LIME": "CONT_AREA_LIME", "CONT_AREA_LIGHT_GREEN": "CONT_AREA_LIGHT_GREEN", "CONT_AREA_BLUE": "CONT_AREA_BLUE", "CONT_AREA_STEEL_BLUE": "CONT_AREA_STEEL_BLUE", "CONT_AREA_LIGHT_BLUE": "CONT_AREA_LIGHT_BLUE", "CONT_AREA_LIGHT_CYAN": "CONT_AREA_LIGHT_CYAN", "CONT_AREA_MAGENTA": "CONT_AREA_MAGENTA", "CONT_AREA_PURPLE": "CONT_AREA_PURPLE", "CONT_AREA_VIOLET": "CONT_AREA_VIOLET", "DASHED_CORAL_AREA": "DASHED_CORAL_AREA", "DASHED_BLUE_AREA": "DASHED_BLUE_AREA", "DASHED_AQUA_AREA": "DASHED_AQUA_AREA", "DASHED_DEEP_PINK_AREA": "DASHED_DEEP_PINK_AREA", "DASHED_GOLDEN_ROD_AREA": "DASHED_GOLDEN_ROD_AREA", "DASHED_GREEN_AREA": "DASHED_GREEN_AREA", "DOTTED_GREEN_YELLOW_AREA": "DOTTED_GREEN_YELLOW_AREA", "DOTTED_INDIAN_RED_AREA": "DOTTED_INDIAN_RED_AREA", "DOTTED_MAGENTA_AREA": "DOTTED_MAGENTA_AREA", "CONT_AREA_ANTIQUE_WHITE": "CONT_AREA_ANTIQUE_WHITE", "CONT_AREA_APRICOT_PEACH": "CONT_AREA_APRICOT_PEACH", "CONT_AREA_NAVAJO_WHITE": "CONT_AREA_NAVAJO_WHITE", "CONT_AREA_ROSE_FOG": "CONT_AREA_ROSE_FOG", "CONT_AREA_DARK_SALMON": "CONT_AREA_DARK_SALMON", "CONT_SEGMENT_LYNCH": "CONT_SEGMENT_LYNCH", "DASHED_SEGMENT_SAN_MARINO": "DASHED_SEGMENT_SAN_MARINO", "CONT_SEGMENT_BLACK": "CONT_SEGMENT_BLACK", "DASHED_SEGMENT_BLACK": "DASHED_SEGMENT_BLACK", "DOTTED_SEGMENT_BLACK": "DOTTED_SEGMENT_BLACK", "CONT_SEGMENT_GREY": "CONT_SEGMENT_GREY", "DASHED_SEGMENT_GREY": "DASHED_SEGMENT_GREY", "DOTTED_SEGMENT_GREY": "DOTTED_SEGMENT_GREY", "CONT_SEGMENT_RED": "CONT_SEGMENT_RED", "DASHED_SEGMENT_RED": "DASHED_SEGMENT_RED", "DOTTED_SEGMENT_RED": "DOTTED_SEGMENT_RED", "CONT_SEGMENT_BOLD_RED": "CONT_SEGMENT_BOLD_RED", "CONT_SEGMENT_YELLOW": "CONT_SEGMENT_YELLOW", "DASHED_SEGMENT_YELLOW": "DASHED_SEGMENT_YELLOW", "DOTTED_SEGMENT_YELLOW": "DOTTED_SEGMENT_YELLOW", "CONT_SEGMENT_GREEN": "CONT_SEGMENT_GREEN", "DASHED_SEGMENT_GREEN": "DASHED_SEGMENT_GREEN", "DOTTED_SEGMENT_GREEN": "DOTTED_SEGMENT_GREEN", "CONT_SEGMENT_BLUE": "CONT_SEGMENT_BLUE", "DASHED_SEGMENT_BLUE": "DASHED_SEGMENT_BLUE", "DOTTED_SEGMENT_BLUE": "DOTTED_SEGMENT_BLUE", "CONT_SEGMENT_ORANGE": "CONT_SEGMENT_ORANGE", "DASHED_SEGMENT_ORANGE": "DASHED_SEGMENT_ORANGE", "DOTTED_SEGMENT_ORANGE": "DOTTED_SEGMENT_ORANGE", "CONT_SEGMENT_MAGENTA": "CONT_SEGMENT_MAGENTA", "DASHED_SEGMENT_MAGENTA": "DASHED_SEGMENT_MAGENTA", "DOTTED_SEGMENT_MAGENTA": "DOTTED_SEGMENT_MAGENTA", "CONT_SEGMENT_PURPLE": "CONT_SEGMENT_PURPLE", "DASHED_SEGMENT_PURPLE": "DASHED_SEGMENT_PURPLE", "DOTTED_SEGMENT_PURPLE": "DOTTED_SEGMENT_PURPLE", "SIMILAR_COLORS": [["CONT_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"], ["DASHED_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"], ["DOTTED_SEGMENT_ORANGE", "CONT_AREA_NAVAJO_WHITE"], ["CONT_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"], ["DASHED_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"], ["DOTTED_SEGMENT_PURPLE", "CONT_AREA_DARK_SALMON"]], "DASHED_PATTERN_REAL_COLOR": {"DASHED_AREA_PERANO": "#748DF2", "DASHED_CORAL_AREA": "#E6755E", "DASHED_BLUE_AREA": "#0000FF", "DASHED_AQUA_AREA": "#00FFFF", "DASHED_DEEP_PINK_AREA": "#F51FCA", "DASHED_GOLDEN_ROD_AREA": "#F7BE1F", "DASHED_GREEN_AREA": "#008000", "DOTTED_GREEN_YELLOW_AREA": "#90DC12", "DOTTED_INDIAN_RED_AREA": "#E18470", "DOTTED_MAGENTA_AREA": "#FF00FF", "$quoted$": ["DASHED_AREA_PERANO", "DASHED_CORAL_AREA", "DASHED_BLUE_AREA", "DASHED_AQUA_AREA", "DASHED_DEEP_PINK_AREA", "DASHED_GOLDEN_ROD_AREA", "DASHED_GREEN_AREA", "DOTTED_GREEN_YELLOW_AREA", "DOTTED_INDIAN_RED_AREA", "DOTTED_MAGENTA_AREA"]}, "instance": {"__symbolic": "error", "message": "Variable not initialized", "line": 135, "character": 22}}}, "AssetsLegend": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "AssetsLegend", "template": "\n        <VBox  #legendContainer width=\"100%\"> \n        </VBox>\n  ", "styles": ["\n      "]}]}], "members": {"legendContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 17, "character": 5}, "arguments": ["<PERSON><PERSON><PERSON><PERSON>"]}]}], "ngOnInit": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 32, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "refreshLegends": [{"__symbolic": "method"}], "getLegendItem": [{"__symbolic": "method"}]}}, "CheckBoxLegend": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 6, "character": 1}, "arguments": [{"selector": "CheckBoxLegend", "template": "\n        <VBox  #legendContainer width=\"100%\"> \n        </VBox>\n  ", "styles": ["\n      "]}]}], "members": {"legendContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 16, "character": 5}, "arguments": ["<PERSON><PERSON><PERSON><PERSON>"]}]}], "ngOnInit": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 32, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "refreshLegends": [{"__symbolic": "method"}], "getLegendItem": [{"__symbolic": "method"}], "getUncheckedLegends": [{"__symbolic": "method"}], "setCheckBoxLegendSelected": [{"__symbolic": "method"}], "highlighTrueFalse": [{"__symbolic": "method"}]}}, "SwtFieldSet": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 5, "character": 1}, "arguments": [{"selector": "SwtFieldSet", "styles": ["\n             :host {\n               margin: 0px;\n               display: block;\n               margin-top :10px;\n               outline: none;\n             }\n\n             .fieldset {\n              border: 1px solid silver;\n              border-top: none;\n              padding: 0.5em;\n              width: 100%;\n              height : 100%;\n          }\n          .fieldset > h1 {\n              font: 1em normal;\n              margin: -1em -0.5em 0;\n          }   \n          .fieldset > h1 > span {\n              float: left;\n          }\n          .fieldset > h1:before {\n              border-top: 1px solid silver;\n              content: ' ';\n              float: left;\n              margin: 0.5em 2px 0 -1px;\n              width: 0.75em;\n          }\n          .fieldset > h1:after {\n              border-top: 1px solid silver;\n              content: ' ';\n              display: block;\n              left: 2px;\n              margin: 0 1px 0 0;\n              overflow: hidden;\n              position: relative;\n              top: 0.5em;\n          }\n           \n   "], "template": "\n    <div class=\"fieldset\"><h1><span #legendItem>Legend</span></h1> \n    <ng-content></ng-content>\n    <ng-container #_container></ng-container>\n    </div>\n\n  "}]}], "members": {"legendItem": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 58, "character": 3}, "arguments": ["legendItem"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 60, "character": 28}, {"__symbolic": "reference", "name": "CommonService"}]}], "legendText": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 68, "character": 3}}]}]}}, "SwtPanel": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "SwtPanel", "template": "\n    <div fxLayout=\"column\"  class=\"panelLayout panelInsideFormLayout\" tabindex=\"-1\" >\n         <div class=\"panelTitle hidden\" tabindex=\"-1\"> </div>\n         <div fxLayout=\"column\" fxLayoutAlign=\"{{verticalAlign}} {{horizontalAlign}}\"  fxLayoutGap=\"{{verticalGap}}\"   class=\"panelBody\" tabindex=\"-1\">\n             <ng-content ></ng-content>\n             <ng-container #container></ng-container> \n        </div>\n    </div>\n  ", "styles": ["\n             :host {\n               margin: 0 0px 5px 0;\n               display: block;\n             }\n             .panelLayout {\n               box-sizing: border-box;\n               width: 100%;\n               height: 100%;\n               outline: none;\n             }\n   "]}]}], "members": {"container": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 36, "character": 5}, "arguments": ["container", {"read": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 36, "character": 37}}]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 39, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 39, "character": 100}]}], "addChild": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 91, "character": 5}, "arguments": ["styleName"]}]}], "title": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 105, "character": 5}, "arguments": ["title"]}]}]}}, "SwtScreen": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 2, "character": 1}, "arguments": [{"selector": "SwtScreen", "template": "\n    <div class=\"screen\">\n          <ng-content></ng-content>\n    </div>\n  ", "styles": ["\n           .screen {\n               background-color: #ccecff;\n               width: 100%;\n               height: 100%;\n               padding: 10px;\n           }\n  "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "ngOnInit": [{"__symbolic": "method"}]}}, "SwtStepper": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "SwtStepper", "template": "\n              <input #stepperinput \n               selector=\"SwtStepper\"\n               type=\"number\"\n               value=\"{{ value }}\"\n               id=\"{{ id }}\"\n               min=\"{{ minimum}}\"  \n               max=\"{{ maximum }}\" \n               step=\"{{stepSize}}\"\n               (keydown)=\"onkeyDown($event)\"\n               (change)=\"onChange($event)\"\n               (focus)=\"onFocus($event)\"\n               (focusout)=\"onFocusOut($event)\"\n               (keyup)=\"onkeyup($event)\"\n               class=\"{{ styleName }}\"\n               Swtrestrict=\"[0-9]\"\n               tabindex=\"{{ tabindex }}\"\n               [attr.disabled]= \"enabled? null: true\" \n               [attr.readOnly]= \"editable? null: true\"\n                popper=\"{{this.toolTipPreviousValue}}\"\n     [popperTrigger]=\"'hover'\"\n     [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n     [popperPlacement]=\"'bottom'\"\n     [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n               [class.requiredInput]= \"this.required==true && (this.text == null)  && enabled==true\"  \n               placement=\"top\">\n           <!--<input Swtregex=\"[0-9]*\" name=\"value\">    -->\n  ", "styles": ["\n      input {\n          /* padding-left: 2px;\n           padding-right: 10px;\n           border: 1px solid #7f9db9; \n           width: 100%; \n           height: 20px;\n           padding-right: 0px;*/\n      }\n      input:disabled {\n          color: gray;\n      }\n\n\n  "]}]}], "members": {"onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 61, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 62, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "regex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 93, "character": 5}, "arguments": ["regex"]}]}], "pattern": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 95, "character": 5}, "arguments": ["pattern"]}]}], "restrict": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 97, "character": 5}, "arguments": ["restrict"]}]}], "minimum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 99, "character": 5}, "arguments": ["minimum"]}]}], "maximum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 101, "character": 5}, "arguments": ["maximum"]}]}], "keyup_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 103, "character": 5}, "arguments": ["keyUp"]}]}], "keyDown_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 105, "character": 5}, "arguments": ["keyDown"]}]}], "focus_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 107, "character": 5}, "arguments": ["focus"]}]}], "focusOut_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 109, "character": 5}, "arguments": ["focusOut"]}]}], "change_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 111, "character": 5}, "arguments": ["change"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 114, "character": 5}, "arguments": ["styleName"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 116, "character": 5}, "arguments": ["toolTip"]}]}], "maxChars": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 118, "character": 5}, "arguments": ["maxChars"]}]}], "tabindex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 120, "character": 5}, "arguments": ["tabIndex"]}]}], "stepSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 122, "character": 5}, "arguments": ["stepSize"]}]}], "toolTipPreviousValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 124, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 134, "character": 5}}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 147, "character": 5}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 150, "character": 5}, "arguments": ["height"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 152, "character": 5}, "arguments": ["id"]}]}], "value": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 155, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 171, "character": 5}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 192, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 212, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 236, "character": 5}}]}], "required": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 250, "character": 8}, "arguments": ["required"]}]}], "stepperDOM": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 267, "character": 5}, "arguments": ["stepperinput"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 268, "character": 30}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "updateText": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "onkeyup": [{"__symbolic": "method"}], "onkeyDown": [{"__symbolic": "method"}], "onFocus": [{"__symbolic": "method"}], "onFocusOut": [{"__symbolic": "method"}], "onChange": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}]}}, "SwtRadioItem": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "SwtRadioItem", "template": "      \n    <div  class=\"radio-item\"  tabindex=\"-1\"  >\n        <input [disabled]=\"parentGroup && !parentGroup.enabled\" type=\"radio\"/>\n        <label class=\"rblabel\" ></label>\n    </div>\n     \n  ", "styles": ["\n           :host {\n               outline: none;\n             }\n           .rblabel{\n               font-size: 11px;\n               color: #173553;\n               height: auto;\n               margin: 5px 4px 0;\n               position: relative;\n               top:1px;\n               font-weight: normal;\n               overflow: hidden;\n               text-overflow: ellipsis;\n             }\n           \n             div.radio-item > input:disabled{\n               background-color: transparent!important;\n             }\n           \n             @media all and (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {\n                 .selector:not(*:root), .rblabel{\n                     margin: 1px 4px 0!important;\n                 }\n             }\n\n            .label{\n                font-weight: bold!important;\n             }\n            .radio-item{\n                width : 100%;\n                display: flex;\n                outline: none;\n            }\n            \n         "]}]}], "members": {"tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 64, "character": 5}, "arguments": ["tabIndex"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 79, "character": 5}}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 84, "character": 5}, "arguments": ["toolTip"]}]}], "groupName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 87, "character": 5}}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 100, "character": 5}, "arguments": ["label"]}]}], "value": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 111, "character": 5}}]}], "selected": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 131, "character": 5}, "arguments": ["selected"]}]}], "fontSize": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 149, "character": 5}, "arguments": ["fontSize"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 160, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "SwtTextInput": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 9, "character": 1}, "arguments": [{"selector": "SwtTextInput", "template": "\n            <input #textfield\n            popper=\"{{this.toolTipPreviousValue}}\"\n            [popperTrigger]=\"'hover'\"\n            [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n            [popperPlacement]=\"'bottom'\"\n            [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n               (paste)=\"onPaste($event)\"\n               class=\"textinput\"\n               [class.requiredInput]= \"this.required==true && !this.text\"/>\n    ", "styles": ["\n        :host {\n             outline:none;\n        }\n        .textArea {\n            position: fixed;\n            z-index: 4;\n        }\n        input::placeholder {\n            color: transparent;\n       }\n        input {\n            border: 1px solid #7f9db9;\n            width: 100%;\n            font-size: 11px;\n            height: 23px;\n            line-height:23px;\n            cursor: text;\n            color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/\n        }\n\n        input:disabled {\n            color: gray;\n            height: 23px;\n        }\n\n        input:focus {\n            border: 1px solid #7F9DB9;\n            outline-style: solid;\n            outline-width: 1px;\n            outline-color: #49B9FF;\n        }\n\n        .hideInput {\n            visibility: hidden;\n        }\n\n        .textinput {\n            padding: 0 5px;\n        }\n    "]}]}], "members": {"textfieldDOM": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 67, "character": 5}, "arguments": ["textfield", {"read": {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 67, "character": 36}}]}]}], "required": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 78, "character": 5}, "arguments": ["required"]}]}], "textAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 95, "character": 5}, "arguments": ["textAlign"]}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 112, "character": 5}, "arguments": ["tabIndex"]}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 128, "character": 5}, "arguments": ["styleName"]}]}], "displayAsPassword": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 139, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 154, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 164, "character": 5}}]}], "dataProvider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 185, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 206, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "onPaste": [{"__symbolic": "method"}], "setFocusAndSelect": [{"__symbolic": "method"}]}}, "SwtTimeInput": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 10, "character": 1}, "arguments": [{"selector": "SwtTimeInput", "template": "\n    <span #timeInput class=\"timeInput-container\"  \n              [style.margin-left.px]=\"this.marginLeft\"\n              [style.margin-right.px]=\"this.marginRight\"\n              [style.margin-top.px]=\"this.marginTop\"\n              [style.margin-bottom.px]=\"this.marginBottom\">\n    <tr>\n       <td>\n        <div class=\"timeInput-time\">  \n          <input #hours type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-hours\">\n          <span #separator1 class=\"seconds-after\"></span>\n          <input #minutes type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-minutes\">\n          <span #separator2 class=\"seconds-before\" style=\"display: none\"></span>\n          <input #seconds type=\"text\" Swtregex=\"[0-9]{2}\" value=\"00\" class=\"timeInput-seconds\" style=\"display: none\">\n        </div>\n       </td>\n       <td>\n        <div class=\"timeInput-spiners\">\n             <tr>\n                 <td>\n                    <button #uparrow class=\"arrow-up\">&#9652;</button>\n                 </td>\n             </tr>\n             <tr>\n                 <td>   \n                    <button #downarrow class=\"arrow-down\">&#9662;</button>\n                 </td>   \n             </tr>\n          </div>\n         </td>\n    </tr>   \n    </span>\n    ", "styles": ["\n\n        td {\n            line-height: 8px;\n        }\n\n        .timeInput-container {\n            height: 21px;\n            width: auto;\n            display: inline-block;\n            border: none;\n            margin: 0px 5px 5px 0px;;\n            background-color: #FFF;\n        }\n\n        .timeInput-container:focus {\n            outline: none !important;\n        }\n\n        .timeInput-time {\n            border: 1px solid #8D8F91;\n            padding-left: 5px;\n            padding-right: 5px;\n            height: 22px;\n        }\n\n        .timeInput-hours, .timeInput-minutes, .timeInput-seconds {\n            width: 15px;\n            height: 19px;\n            text-align: center;\n            border: none;\n            color: #173553;\n        }\n\n        .timeInput-hours:focus, .timeInput-minutes:focus, .timeInput-seconds:focus {\n            outline: none;\n        }\n\n        .timeInput-spiners {\n            height: 19px;\n        }\n\n        .seconds-after, .seconds-after {\n            font-family: verdana, helvetica;\n            margin: -1px;\n        }\n\n        .arrow-down, .arrow-up {\n            width: 18px;\n            padding: 0px;\n            height: 11px;\n            line-height: 4px;\n            font-size: 12px;\n            font-weight: bolder;\n            cursor: default;\n        }\n\n        .arrow-down:focus, .arrow-up:focus {\n            outline: none;\n        }\n\n        .arrow-down:hover, .arrow-up:hover {\n            border: 1px solid #0086E8;\n            background-image: -webkit-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -moz-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -ms-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: -o-linear-gradient(top, #FFFFFF, #F1F1F1);\n            background-image: linear-gradient(to bottom, #FFFFFF, #F1F1F1);\n        }\n\n        .arrow-down {\n            border-top: 0.5px solid #8D8F91;\n            border-bottom: 1px solid #8D8F91;\n            border-left: 1px solid #8D8F91;\n            border-right: 1px solid #8D8F91;\n            border-bottom-right-radius: 4px;\n            background-image: -webkit-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -moz-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -ms-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: -o-linear-gradient(top, #D8E8F2, #CDE0EB);\n            background-image: linear-gradient(to bottom, #D8E8F2, #CDE0EB);\n        }\n\n        .arrow-up {\n            border-top: 1px solid #8D8F91;\n            border-bottom: 1px solid transparent;\n            border-left: 1px solid #8D8F91;\n            border-right: 1px solid #8D8F91;\n            border-top-right-radius: 4px;\n            margin-top: -1px;\n            background-image: -webkit-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -moz-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -ms-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: -o-linear-gradient(top, #F1F9FF, #DDECF5);\n            background-image: linear-gradient(to bottom, #F1F9FF, #DDECF5);\n        }\n    "]}]}], "members": {"onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 146, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 147, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 148, "character": 5}, "arguments": ["id"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 149, "character": 5}, "arguments": ["toolTip"]}]}], "KeyDown": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 151, "character": 5}, "arguments": ["KeyDown"]}]}], "keyUp": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 152, "character": 5}, "arguments": ["keyUp"]}]}], "focus": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 153, "character": 5}, "arguments": ["focus"]}]}], "stepperChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 154, "character": 5}, "arguments": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}]}], "creationComplete": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 155, "character": 5}, "arguments": ["creationComplete"]}]}], "marginTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 157, "character": 5}, "arguments": ["marginTop"]}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 158, "character": 5}, "arguments": ["marginRight"]}]}], "marginBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 159, "character": 5}, "arguments": ["marginBottom"]}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 160, "character": 5}, "arguments": ["marginLeft"]}]}], "hours": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 167, "character": 5}, "arguments": ["hours"]}]}], "minutes": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 169, "character": 5}, "arguments": ["minutes"]}]}], "seconds": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 171, "character": 5}, "arguments": ["seconds"]}]}], "uparrow": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 173, "character": 5}, "arguments": ["uparrow"]}]}], "downarrow": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 175, "character": 5}, "arguments": ["downarrow"]}]}], "separatorspan1": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 177, "character": 5}, "arguments": ["separator1"]}]}], "separatorspan2": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 179, "character": 5}, "arguments": ["separator2"]}]}], "timeInput": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 181, "character": 5}, "arguments": ["timeInput"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 186, "character": 30}]}], "secondEnable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 196, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 228, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 254, "character": 5}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 297, "character": 5}}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "changeStyle": [{"__symbolic": "method"}], "disableSeconds": [{"__symbolic": "method"}], "enableSeconds": [{"__symbolic": "method"}], "onHoursKeyDown": [{"__symbolic": "method"}], "onHoursKeyUp": [{"__symbolic": "method"}], "onMinutesKeyDown": [{"__symbolic": "method"}], "onMinutesKeyUp": [{"__symbolic": "method"}], "onSecondsKeyDown": [{"__symbolic": "method"}], "onSecondsKeyUp": [{"__symbolic": "method"}], "onArrowUpClick": [{"__symbolic": "method"}], "onArrowDownClick": [{"__symbolic": "method"}], "loadingZero": [{"__symbolic": "method"}], "increment": [{"__symbolic": "method"}], "decrement": [{"__symbolic": "method"}]}}, "HTTPComms": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 11, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "send": [{"__symbolic": "method"}], "cancel": [{"__symbolic": "method"}], "arraySend": [{"__symbolic": "method"}], "isBusy": [{"__symbolic": "method"}], "resetTransactionUId": [{"__symbolic": "method"}], "getTransactionUId": [{"__symbolic": "method"}], "setTransactionUId": [{"__symbolic": "method"}], "startRemoteTransaction": [{"__symbolic": "method"}], "sendTransaction": [{"__symbolic": "method"}], "result": [{"__symbolic": "method"}], "showLogon": [{"__symbolic": "method"}], "fault": [{"__symbolic": "method"}], "onStart": [{"__symbolic": "method"}], "getResultObject": [{"__symbolic": "method"}], "encodeData": [{"__symbolic": "method"}], "sendRequest": [{"__symbolic": "method"}], "onFinish": [{"__symbolic": "method"}], "arrayToGetParams": [{"__symbolic": "method"}], "getTimer": [{"__symbolic": "method"}]}}, "RemoteTransaction": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "HTTPComms"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}], "start": [{"__symbolic": "method"}], "commit": [{"__symbolic": "method"}], "rollback": [{"__symbolic": "method"}], "isActive": [{"__symbolic": "method"}], "wasCommitted": [{"__symbolic": "method"}], "wasRolledBack": [{"__symbolic": "method"}], "getTimer": [{"__symbolic": "method"}], "remoteTransaction": [{"__symbolic": "method"}]}, "statics": {"remoteTransactions": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Array"}}}}, "Alert": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 12, "character": 1}, "arguments": [{"selector": "swt-alert", "template": "\n        <div class=\"alert-overlay\">\n            <div class=\"alert-content\">\n                <div class=\"alert-heading\">\n                    <span [innerHTML]=\"title\"></span>\n                </div>\n                <div class=\"alert-body\">\n                    <div class=\"alert-message\">\n                        <div class=\"image\">\n                            <img src=\"{{ iconClass }}\" alt=\"{{ title }}\"/>\n                        </div>\n                        <div class=\"msg\" [innerHTML]=\"texthtml\"></div>\n                    </div>\n                    <div class=\"alert-btn\">\n                        <button class=\"alert-button\" *ngFor=\"let flag of buttonFlags let count = index\" (click)=\"destroy(flag, count)\">\n                            {{ flag }}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    ", "providers": [{"__symbolic": "reference", "name": "CommonService"}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "show": [{"__symbolic": "method"}], "getButtonLabel": [{"__symbolic": "method"}], "getbuttonNumber": [{"__symbolic": "method"}]}, "statics": {"YES": 1, "NO": 2, "OK": 4, "CANCEL": 8, "yesLabel": "Yes", "noLabel": "No", "okLabel": "Ok", "cancelLabel": "Cancel"}}, "SwtAlert": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "<PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 5, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "info": [{"__symbolic": "method"}], "question": [{"__symbolic": "method"}], "error": [{"__symbolic": "method"}], "warning": [{"__symbolic": "method"}], "confirm": [{"__symbolic": "method"}], "invalid": [{"__symbolic": "method"}]}}, "CommonService": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 17, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 28, "character": 9}, "arguments": ["routes"]}], null, null, null], "parameters": [{"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClient", "line": 23, "character": 27}, {"__symbolic": "reference", "module": "@angular/router", "name": "Router", "line": 24, "character": 23}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 25, "character": 41}, {"__symbolic": "reference", "name": "ɵb"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 25, "character": 41}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader", "line": 29, "character": 23}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 30, "character": 25}, {"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationRef", "line": 31, "character": 31}]}], "getQualifiedClassName": [{"__symbolic": "method"}]}, "statics": {"WindowManager": {"__symbolic": "error", "message": "Variable not initialized", "line": 19, "character": 11}, "instance": null, "jsonpath": {"__symbolic": "function", "parameters": ["obj", "path"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 9, "character": 4, "context": {"name": "jp"}, "module": "./src/app/modules/swt-toolbox/com/swallow/utils/common.service"}}}}, "SwtLocalStorage": {"__symbolic": "class", "members": {}, "statics": {"getRecordsCount": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "localStorage"}, "member": "length"}}, "getRecord": {"__symbolic": "function", "parameters": ["key"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "localStorage"}, "member": "getItem"}, "arguments": [{"__symbolic": "reference", "name": "key"}]}}, "getRecordKey": {"__symbolic": "function", "parameters": ["index"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "localStorage"}, "member": "key"}, "arguments": [{"__symbolic": "reference", "name": "index"}]}}}}, "SwtHelpWindow": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 2, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"_toolbar": "yes", "_scrollbars": "yes", "_resizable": "yes", "width": 500, "height": 400, "top": 50, "left": 50, "content": "_blank", "BLANK": "_blank"}}, "SwtUtil": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "SwtUtil": [{"__symbolic": "method"}]}, "statics": {"CommonServiceInstance": null, "COMMON_MODULE_ID": "COMMON", "SYSTEM_MODULE_ID": "SYSTEM", "AML_MODULE_ID": "AML", "DUP_MODULE_ID": "DUP", "ARC_MODULE_ID": "ARC", "INPUT_MODULE_ID": "INPUT", "CASH_MODULE_ID": "CASH", "FATCA_MODULE_ID": "FATCA", "PREDICT_MODULE_ID": "PREDICT", "PCM_MODULE_ID": "PCM", "LOGIN_ID": "LOGIN", "lang": "es", "translate": null, "screenWidth": {"__symbolic": "error", "message": "Variable not initialized", "line": 39, "character": 18}, "screenHeight": {"__symbolic": "error", "message": "Variable not initialized", "line": 40, "character": 18}, "actionMethod": "logError.do", "actionPath": "system/errorlog!", "inputData": {"__symbolic": "error", "message": "Variable not initialized", "line": 46, "character": 19}, "requestParams": {"__symbolic": "error", "message": "Variable not initialized", "line": 48, "character": 19}, "obtainURL": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "window"}, "member": "location"}}, "isVisible": {"__symbolic": "function", "parameters": ["e"], "value": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "e"}, "member": "offsetWidth"}, "right": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "e"}, "member": "offsetHeight"}}, "right": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "e"}, "member": "getClientRects"}}, "member": "length"}}}}}, "isEmpty": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "value"}, "right": {"__symbolic": "reference", "name": "undefined"}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "value"}, "right": null}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "value"}, "right": ""}}}, "arrToStr": {"__symbolic": "function", "parameters": ["arrV<PERSON>ue", "separator"], "value": ""}, "getUserLanguage": {"__symbolic": "function", "parameters": [], "value": ""}, "getCurrEntityId": {"__symbolic": "function", "parameters": [], "value": ""}, "getDefaultEntityId": {"__symbolic": "function", "parameters": [], "value": ""}}}, "Logger": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 13, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClient", "line": 29, "character": 54}, {"__symbolic": "reference", "name": "number"}]}], "trace": [{"__symbolic": "method"}], "debug": [{"__symbolic": "method"}], "info": [{"__symbolic": "method"}], "log": [{"__symbolic": "method"}], "warn": [{"__symbolic": "method"}], "error": [{"__symbolic": "method"}], "_timestamp": [{"__symbolic": "method"}], "_logOnServer": [{"__symbolic": "method"}], "_logIE": [{"__symbolic": "method"}], "_log": [{"__symbolic": "method"}], "_getColor": [{"__symbolic": "method"}]}}, "LoggerLevel": {"TRACE": 0, "DEBUG": 1, "INFO": 2, "LOG": 3, "WARN": 4, "ERROR": 5, "OFF": 6}, "JSONReader": {"__symbolic": "class", "members": {"JSONReader": [{"__symbolic": "method"}], "setInputJSON": [{"__symbolic": "method"}], "getInputJSON": [{"__symbolic": "method"}], "getScreenAttributes": [{"__symbolic": "method"}], "getRequestReplyStatus": [{"__symbolic": "method"}], "getRequestReplyMessage": [{"__symbolic": "method"}], "getRequestReplyLocation": [{"__symbolic": "method"}], "isDataBuilding": [{"__symbolic": "method"}], "getDateFormat": [{"__symbolic": "method"}], "getRefreshRate": [{"__symbolic": "method"}], "getTiming": [{"__symbolic": "method"}], "getSingletons": [{"__symbolic": "method"}], "getColumnData": [{"__symbolic": "method"}], "getGridData": [{"__symbolic": "method"}], "getBottomGridData": [{"__symbolic": "method"}], "getGridMetaData": [{"__symbolic": "method"}], "getRowSize": [{"__symbolic": "method"}], "getTotalsData": [{"__symbolic": "method"}], "getSelects": [{"__symbolic": "method"}], "getMaxPage": [{"__symbolic": "method"}], "getCurrentPage": [{"__symbolic": "method"}], "getTreeData": [{"__symbolic": "method"}], "getAllColumnData": [{"__symbolic": "method"}], "getRoot": [{"__symbolic": "method"}], "getprossesInfoStatus": [{"__symbolic": "method"}], "getprossesInfoRunning": [{"__symbolic": "method"}]}, "statics": {"jsonpath": {"__symbolic": "function", "parameters": ["obj", "path"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "jsonpath"}, "arguments": [{"__symbolic": "reference", "name": "obj"}, {"__symbolic": "reference", "name": "path"}]}}, "jsonpathes": {"__symbolic": "function", "parameters": ["obj", "paths"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "<PERSON><PERSON><PERSON><PERSON>"}, "arguments": [{"__symbolic": "reference", "name": "obj"}, {"__symbolic": "reference", "name": "paths"}]}}}}, "SwtRadioButtonGroup": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 9, "character": 1}, "arguments": [{"selector": "SwtRadioButtonGroup", "template": "\n    <div  \n         tabindex=\"-1\"  \n         popper=\"{{this.toolTipPreviousValue}}\"\n         [popperTrigger]=\"'hover'\"\n         [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n         [popperPlacement]=\"'bottom'\"\n         [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n         class=\"inline-field SwtRadioButtonGroup\"   \n         (change)=\"Change($event)\" >\n          <ng-content  ></ng-content>\n          <ng-container #_container></ng-container>\n    </div>    \n  ", "styles": [" \n            :host {\n               display: block;\n               outline: none;\n             }\n             .SwtRadioButtonGroup{\n                  outline: none;\n             }\n             .verticalHorizontalAlign{\n                 display : flex;\n                \n             }\n\n            .SwtRadioButtonGroup.disabled-container{\n                opacity: unset!important;\n            }\n     "]}]}], "members": {"toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 57, "character": 5}, "arguments": ["toolTip"]}]}], "radioItems": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren", "line": 60, "character": 5}, "arguments": [{"__symbolic": "reference", "name": "SwtRadioItem"}]}]}], "align": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 76, "character": 5}, "arguments": ["align"]}]}], "selectedValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 90, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClient", "line": 135, "character": 36}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 135, "character": 62}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "Change": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}], "spyNoChanges": [{"__symbolic": "method"}], "resetOriginalValue": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "SwtCommonGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 91, "character": 1}, "arguments": [{"selector": "SwtCommonGrid", "template": "\n        <angular-slickgrid\n                #angularSlickGrid\n                class=\"commonSlickGrid\"\n                gridId='grid-{{id}}'\n                (onDataviewCreated)=\"dataviewReady($event)\"\n                (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n                [columnDefinitions]=\"columnDefinitions\"\n                (onMouseEnter)=\"handleOnMouseEnter($event.detail.eventData)\"\n                (onMouseLeave)=\"handleOnMouseLeave()\"\n                [gridOptions]=\"gridOptions\"\n                gridHeightString=\"100%\"\n                gridWidthtString=\"100%\"\n                gridHeight=\"100%\"\n                gridWidth=\"100%\"\n                [dataset]=\"dataset\"\n        >\n        </angular-slickgrid>\n\n    ", "styles": ["\n        .gridContent{\n            min-width: 300px;\n            height: 100%;\n        }\n        :host ::ng-deep .gridPane {\n            min-width: 100%;\n            min-height: 100%;\n            height:100%;\n            width:100%;            overflow: auto;\n            display: block;\n        }\n        :host ::ng-deep .slickgrid-container {\n            min-height: 100%;\n        }\n\n\n    "], "providers": [{"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 132, "character": 8}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 133, "character": 8}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionService", "line": 134, "character": 8}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 135, "character": 8}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 136, "character": 8}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 137, "character": 8}]}]}], "members": {"gridContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 169, "character": 5}, "arguments": ["grid-container"]}]}], "_width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 170, "character": 5}, "arguments": ["width"]}]}], "_height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 171, "character": 5}, "arguments": ["height"]}]}], "_editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 172, "character": 5}, "arguments": ["editable"]}]}], "_styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 173, "character": 5}, "arguments": ["styleName"]}]}], "angularSlickGrid": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 174, "character": 5}, "arguments": ["angularSlickGrid"]}]}], "onFilterChanged_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 311, "character": 5}, "arguments": ["onFilterChanged"]}]}], "filterUpdate_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 312, "character": 5}, "arguments": ["filterUpdate"]}]}], "onPaginationChanged_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 313, "character": 5}, "arguments": ["onPaginationChanged"]}]}], "onSortChanged_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 314, "character": 5}, "arguments": ["onSortChanged"]}]}], "sortUpdate_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 315, "character": 5}, "arguments": ["sortUpdate"]}]}], "cellClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 317, "character": 5}, "arguments": ["cellClick"]}]}], "ITEM_CHANGED": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 318, "character": 5}, "arguments": ["ITEM_CHANGED"]}]}], "ITEM_FOCUS_OUT": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 319, "character": 5}, "arguments": ["ITEM_FOCUS_OUT"]}]}], "ITEM_FOCUS_IN": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 320, "character": 5}, "arguments": ["ITEM_FOCUS_IN"]}]}], "onDoubleClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 321, "character": 5}, "arguments": ["onDoubleClick"]}]}], "ITEM_DOUBLE_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 322, "character": 5}, "arguments": ["ITEM_DOUBLE_CLICK"]}]}], "ITEM_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 323, "character": 5}, "arguments": ["ITEM_CLICK"]}]}], "radioButtonChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 324, "character": 5}, "arguments": ["radioButtonChange"]}]}], "columnWidthChanged": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 325, "character": 5}, "arguments": ["columnWidthChanged"]}]}], "columnOrderChanged": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 326, "character": 5}, "arguments": ["columnOrderChanged"]}]}], "cellLinkClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 327, "character": 5}, "arguments": ["cellLinkClick"]}]}], "onFocusOut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 328, "character": 5}, "arguments": ["onFocusOut"]}]}], "keyup": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 329, "character": 5}, "arguments": ["keyup"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 498, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 498, "character": 117}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 498, "character": 167}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 498, "character": 211}, {"__symbolic": "reference", "name": "CollectionService", "module": "angular-slickgrid", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 499, "character": 75}]}], "handleOnMouseEnter": [{"__symbolic": "method"}], "handleOnMouseLeave": [{"__symbolic": "method"}], "toObject": [{"__symbolic": "method"}], "paginationComponent": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 865, "character": 5}, "arguments": ["pagination"]}]}], "setSelectedIndices": [{"__symbolic": "method"}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1100, "character": 5}}]}], "styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1124, "character": 5}}]}], "allowMultipleSelection": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1138, "character": 5}}]}], "selectable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1158, "character": 5}}]}], "enableRowSelection": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1180, "character": 5}}]}], "addPreHeaderBackGroundColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1193, "character": 5}}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1205, "character": 5}}]}], "showHeader": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1277, "character": 5}}]}], "showFilter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1330, "character": 5}}]}], "enableFilter": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1358, "character": 5}}]}], "enableSort": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1386, "character": 5}}]}], "doubleClickEnabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 1414, "character": 5}}]}], "drawLockedColumns": [{"__symbolic": "method"}], "refreshPaneFrozenRows": [{"__symbolic": "method"}], "CustomGrid": [{"__symbolic": "method"}], "sorterNumeric": [{"__symbolic": "method"}], "drawGridBackgroundColor": [{"__symbolic": "method"}], "drawGridVerticalLines": [{"__symbolic": "method"}], "clearAll": [{"__symbolic": "method"}], "translateHeader": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "shallowEqual": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}], "isIE": [{"__symbolic": "method"}], "dataviewReady": [{"__symbolic": "method"}], "onAngularGridCreated": [{"__symbolic": "method"}], "gridReady": [{"__symbolic": "method"}], "sortTreeGrid": [{"__symbolic": "method"}], "convertHierarchicalViewToParentChildArrayLocal": [{"__symbolic": "method"}], "convertHierarchicalViewToParentChildArrayByReferenceLocal": [{"__symbolic": "method"}], "convertParentChildArrayToHierarchicalViewLocal": [{"__symbolic": "method"}], "setRefreshColumnWidths": [{"__symbolic": "method"}], "setNewFiltercollection": [{"__symbolic": "method"}], "refreshFilters": [{"__symbolic": "method"}], "removeDuplicates": [{"__symbolic": "method"}], "sortCollection": [{"__symbolic": "method"}], "sortByFieldType": [{"__symbolic": "method"}], "CommonGrid": [{"__symbolic": "method"}], "gridComboDataProviders": [{"__symbolic": "method"}], "scrollToIndex": [{"__symbolic": "method"}], "gridStateChanged": [{"__symbolic": "method"}], "escapeRegExp": [{"__symbolic": "method"}], "buildQuery": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "resetPaginationOptions": [{"__symbolic": "method"}], "updateOptions": [{"__symbolic": "method"}], "processOnFilterChanged": [{"__symbolic": "method"}], "processOnSortChanged": [{"__symbolic": "method"}], "processOnPaginationChanged": [{"__symbolic": "method"}], "getFilteredGridColumns": [{"__symbolic": "method"}], "getCurrentFilter": [{"__symbolic": "method"}], "getSortedGridColumn": [{"__symbolic": "method"}], "getMsdSortedGridColumn": [{"__symbolic": "method"}], "getFilterColumns": [{"__symbolic": "method"}], "getSortColumns": [{"__symbolic": "method"}], "getFilteredItems": [{"__symbolic": "method"}], "getFilteredData": [{"__symbolic": "method"}], "onKeyDown": [{"__symbolic": "method"}], "spyChanges": [{"__symbolic": "method"}], "spyNoChanges": [{"__symbolic": "method"}], "kvFilterSortParams": [{"__symbolic": "method"}], "filterSortParamsKVStr": [{"__symbolic": "method"}], "kvFilterSortAsQuery": [{"__symbolic": "method"}], "implicitUpdate": [{"__symbolic": "method"}], "updateCrud": [{"__symbolic": "method"}], "itemToKvtype": [{"__symbolic": "method"}], "defaultEnableDisableCells": [{"__symbolic": "method"}], "defaultValidate": [{"__symbolic": "method"}], "defaultShowHideCells": [{"__symbolic": "method"}], "defaultRowColorFunction": [{"__symbolic": "method"}], "defaultRowClickableFunction": [{"__symbolic": "method"}], "defaultDisabledRow": [{"__symbolic": "method"}], "defaultContentFunction": [{"__symbolic": "method"}], "defaultextraHTMLContentFunction": [{"__symbolic": "method"}], "defaultTooltipFunction": [{"__symbolic": "method"}], "defaultColWidthURL": [{"__symbolic": "method"}], "saveResult": [{"__symbolic": "method"}], "saveFault": [{"__symbolic": "method"}], "getScrollbarWidth": [{"__symbolic": "method"}], "updateWidths": [{"__symbolic": "method"}], "updateColumnWidths": [{"__symbolic": "method"}], "colWidthURL": [{"__symbolic": "method"}], "colOrderURL": [{"__symbolic": "method"}], "updateColumnOrder": [{"__symbolic": "method"}], "deepCopy": [{"__symbolic": "method"}], "enableColumn": [{"__symbolic": "method"}], "refresh": [{"__symbolic": "method"}], "deepIsEqual": [{"__symbolic": "method"}], "clearChanges": [{"__symbolic": "method"}], "Filter": [{"__symbolic": "method"}], "removeSelected": [{"__symbolic": "method"}], "appendRow": [{"__symbolic": "method"}], "appendRows": [{"__symbolic": "method"}], "updateRow": [{"__symbolic": "method"}], "updateRowByCondition": [{"__symbolic": "method"}], "getRowByCondition": [{"__symbolic": "method"}], "onCellClicked": [{"__symbolic": "method"}], "onSelectedRowsChanged": [{"__symbolic": "method"}], "onCellChanged": [{"__symbolic": "method"}], "gridInitialized": [{"__symbolic": "method"}], "setColumnComboFiltredRows": [{"__symbolic": "method"}], "validateNow": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "navigatorUserAgentDetect": [{"__symbolic": "method"}], "hideColumn": [{"__symbolic": "method"}], "displayColumn": [{"__symbolic": "method"}], "clearSortFilter": [{"__symbolic": "method"}], "resetFilter": [{"__symbolic": "method"}], "refreshGridDataIds": [{"__symbolic": "method"}], "addCheckBoxHeaderColumn": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "sortGridColumnByColOrder": [{"__symbolic": "method"}], "displayHideScrollBar": [{"__symbolic": "method"}], "StrToBool": [{"__symbolic": "method"}], "updateCheckboxHeader": [{"__symbolic": "method"}], "thereIsVerticalScrollbar": [{"__symbolic": "method"}], "thereIsHorizontalScrollbar": [{"__symbolic": "method"}], "resizeGrid": [{"__symbolic": "method"}], "refrehHeaderFiters": [{"__symbolic": "method"}], "sortByProperty": [{"__symbolic": "method"}], "collapseAllTreeLevels": [{"__symbolic": "method"}], "expandAllTreeLevels": [{"__symbolic": "method"}], "expandTreeToLevel": [{"__symbolic": "method"}], "refreshLastOpenedTreeItems": [{"__symbolic": "method"}], "addMultipleFilterStyle": [{"__symbolic": "method"}]}, "statics": {"CRUD_OPERATION": "crud_operation", "CRUD_DATA": "crud_data", "CRUD_ORIGINAL_DATA": "crud_original_data"}}, "SwtCommonGridPagination": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 13, "character": 1}, "arguments": [{"selector": "SwtCommonGridPagination", "template": "\n        <div class=\"slick-pagination\">\n            <div class=\"slick-pagination-nav\"  [ngStyle]=\"styleObject\" >\n                <nav aria-label=\"Page navigation\">\n                    <ul class=\"pagination\">\n                        <li class=\"page-item\" [ngClass]=\"value <= 1 ? 'disabled' : ''\">\n                            <a class=\"page-link icon-seek-first fa fa-angle-double-left\"\n                               aria-label=\"First\" (click)=\"changeToFirstPage($event)\"> </a>\n                        </li>\n                        <li class=\"page-item\" [ngClass]=\"value <= 1 ? 'disabled' : ''\">\n                            <a class=\"page-link icon-seek-prev fa fa-angle-left\"\n                               aria-label=\"Previous\" (click)=\"changeToPreviousPage($event)\"> </a>\n                        </li>\n                    </ul>\n                </nav>\n\n                <div class=\"slick-page-number\">\n                    <input  type=\"number\" value=\"{{value}}\" size=\"1\" (keyup.enter)=\"changeToCurrentPage($event)\" (change)=\"changeToCurrentPage($event)\" >\n                    <span style=\"padding-left: 2px\" [translate]=\"'OF'\"></span><span> {{maximum}}</span>\n                </div>\n\n                <nav aria-label=\"Page navigation\">\n                    <ul class=\"pagination\">\n                        <li class=\"page-item CGP-next-page\"\n                            [ngClass]=\"value === maximum ? 'disabled' : ''\"><a\n                                class=\"page-link icon-seek-next text-center fa fa-lg fa-angle-right\"\n                                aria-label=\"Next\" (click)=\"changeToNextPage($event)\"> </a></li>\n                        <li class=\"page-item CGP-end-page\"\n                            [ngClass]=\"value === maximum ? 'disabled' : ''\"><a\n                                class=\"page-link icon-seek-end fa fa-lg fa-angle-double-right\"\n                                aria-label=\"Last\" (click)=\"changeToLastPage($event)\"> </a></li>\n                    </ul>\n                </nav>\n            </div>\n        </div>\n    ", "styles": ["\n        .slick-pagination {\n            padding: 0px !important;\n            height: 24px;\n            width:auto;\n            padding: 2px;\n            border-radius: 5px;\n            margin: 0 5px 0 5px;\n        }\n        .slick-page-number {\n            border: 1px solid #ADCCE3;\n            height:23px;\n            line-height:23px;\n            color:black;\n        }\n        .slick-pagination .slick-pagination-nav {\n            padding: 0px !important;\n            height: 24px!important;\n        }\n        .page-spin {\n            border: none;\n            height: 23px;\n            width: 23px;\n            background-color: transparent;\n            cursor: default;\n            animation: fa-spin 1.2s infinite linear !important;\n        }\n        .page-spin:hover {\n            background-color: transparent;\n        }\n        .slick-pagination .slick-pagination-nav .pagination .page-link {\n            height: 23px;\n            padding-right: 6px; \n            height: 23px; \n            padding-top: 3px; \n            padding-bottom: 3px;\n        }\n        .pagination>li>a, .pagination>li>span {\n            padding: 3px 6px;\n            border: none;\n            border-radius: 0px;\n            background-image: -webkit-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -moz-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -ms-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: -o-linear-gradient(left, #E3F5FF, #ADCCE3);\n            background-image: linear-gradient(to bottom, #E3F5FF, #ADCCE3);\n            cursor: default;\n            border: 1px solid #ADCCE3;\n            color: #173553;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number input {\n            background-color: white;\n            height: 21px;\n            width: 34px;\n            padding: 2px;\n            display: inline-block;\n            text-align: center;\n            border-top:1px solid  #4C5E6F;\n            margin-top: 2px;\n            margin-bottom: 2px;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number input:focus {\n            outline: none;\n        }\n        .slick-pagination .slick-pagination-nav .slick-page-number {\n            vertical-align: top;\n            margin-top: 0px;\n            display: inline-block;\n            padding: 0 5px;\n        }\n        .slick-page-number{\n            line-height : 0px!important;\n            border: none;\n        }\n    "]}]}], "members": {"maximum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 131, "character": 5}, "arguments": ["maximum"]}]}], "minimum_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 132, "character": 5}, "arguments": ["minimum"]}]}], "value": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 133, "character": 5}, "arguments": ["value"]}]}], "horizontalAlign": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 135, "character": 5}}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 136, "character": 5}}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 137, "character": 5}}]}], "gridPaginationOptions": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 147, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClient", "line": 163, "character": 36}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 163, "character": 61}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterContentChecked": [{"__symbolic": "method"}], "changeToFirstPage": [{"__symbolic": "method"}], "changeToLastPage": [{"__symbolic": "method"}], "changeToNextPage": [{"__symbolic": "method"}], "changeToPreviousPage": [{"__symbolic": "method"}], "changeToCurrentPage": [{"__symbolic": "method"}], "onPageChanged": [{"__symbolic": "method"}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 245, "character": 5}}]}]}}, "SwtTotalCommonGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtCommonGrid"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 60, "character": 1}, "arguments": [{"selector": "SwtTotalCommonGrid", "template": "\n    <angular-slickgrid\n            #angularSlickGrid\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n", "styles": ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"], "providers": [{"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 93, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionService", "line": 94, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 95, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 96, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 97, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 98, "character": 4}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 104, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 104, "character": 117}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 104, "character": 167}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 104, "character": 211}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 105, "character": 29}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 105, "character": 70}]}]}}, "SwtGroupedTotalCommonGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtCommonGrid"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 60, "character": 1}, "arguments": [{"selector": "SwtGroupedTotalCommonGrid", "template": "\n    <angular-slickgrid\n    class=\"commonSlickGrid\"\n            #angularSlickGrid\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n", "styles": ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"], "providers": [{"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 94, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionService", "line": 95, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 96, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 97, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 98, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 99, "character": 4}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 105, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 105, "character": 117}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 105, "character": 167}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 105, "character": 211}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 106, "character": 29}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 106, "character": 70}]}]}}, "SwtGroupedCommonGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtCommonGrid"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 60, "character": 1}, "arguments": [{"selector": "SwtGroupedCommonGrid", "template": "\n    <angular-slickgrid\n    class=\"commonSlickGrid\"\n            #angularSlickGrid\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n", "styles": ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"], "providers": [{"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 94, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionService", "line": 95, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 96, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 97, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 98, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 99, "character": 4}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 105, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 105, "character": 117}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 105, "character": 167}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 105, "character": 211}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 106, "character": 29}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 106, "character": 70}]}]}}, "SwtTreeCommonGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtCommonGrid"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 60, "character": 1}, "arguments": [{"selector": "SwtTreeCommonGrid", "template": "\n    <angular-slickgrid\n            #angularSlickGrid\n            class=\"commonSlickGrid\"\n            gridId='grid-{{id}}'\n            (onDataviewCreated)=\"dataviewReady($event)\"\n            (onAngularGridCreated)=\"onAngularGridCreated($event)\"\n            [columnDefinitions]=\"columnDefinitions\"\n            [gridOptions]=\"gridOptions\"\n            gridHeight=\"100%\"\n            gridWidth=\"100%\"\n            [dataset]=\"dataset\"\n    >\n    </angular-slickgrid>\n\n", "styles": ["\n    .gridContent{\n        min-width: 300px;\n        height: 100%;\n    }\n    :host ::ng-deep .gridPane {\n        overflow: auto;\n        display: block;\n    }\n    :host ::ng-deep .slickgrid-container {\n        min-height: 100%;\n    }\n\n\n"], "providers": [{"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 94, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionService", "line": 95, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 96, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 97, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 98, "character": 4}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 99, "character": 4}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 105, "character": 31}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "AutoTooltipExtension", "line": 105, "character": 117}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "ExtensionUtility", "line": 105, "character": 167}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "SharedService", "line": 105, "character": 211}, {"__symbolic": "reference", "module": "angular-slickgrid", "name": "CollectionService", "line": 106, "character": 29}, {"__symbolic": "reference", "module": "@ngx-translate/core", "name": "TranslateService", "line": 106, "character": 70}]}]}}, "SwtPopUpManager": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 13, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"modalId": -1, "position": {"left": 0, "top": 0}, "createPopUp": {"__symbolic": "function", "parameters": ["parent", "childComponent", "data", "modal"], "defaults": [null, null, null, false], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "WindowManager"}, "member": "createWindow"}, "arguments": [{"__symbolic": "reference", "name": "parent"}, {"__symbolic": "reference", "name": "childComponent"}, {"__symbolic": "reference", "name": "data"}, {"__symbolic": "reference", "name": "modal"}]}}, "load": {"__symbolic": "function", "parameters": ["url"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "WindowManager"}, "member": "load"}, "arguments": [{"__symbolic": "reference", "name": "url"}]}}, "getPopUpById": {"__symbolic": "function", "parameters": ["id"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "WindowManager"}, "member": "getWindow"}, "arguments": [{"__symbolic": "reference", "name": "id"}]}}, "close": {"__symbolic": "function", "parameters": ["window"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "CommonService"}, "member": "WindowManager"}, "member": "close"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "window"}, "member": "id"}]}}}}, "EmailValidator": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 3, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"DISALLOWED_CHARS": "()<>,;:\\\"[] `~!#$%^&*+={}|/?'"}}, "SwtPrettyPrintTextArea": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 11, "character": 1}, "arguments": [{"selector": "SwtPrettyPrintTextArea", "template": "\n      <div class=\"swtPrettyPrintTextArea\"\n      [ngStyle]=\"{\n        'width'         :width,\n        'height'        :height,\n        'padding-top'   :paddingTop,\n        'padding-right' :paddingRight,\n        'padding-bottom':paddingBottom,\n        'padding-left'  :paddingLeft,\n        'margin-top'    :marginTop,\n        'margin-right'  :marginRight,\n        'margin-bottom' :marginBottom,\n        'margin-left'   :marginLeft\n      }\"\n     >\n      <ngx-codemirror #ngxCodeMirror [options]= \"options\" [(ngModel)]=\"contentValue\"\n      (ngModelChange)=\"handleChange($event)\"\n      (focusChange)=\"focusChange($event)\">\n      </ngx-codemirror>\n      </div >\n      ", "styles": ["\n      textarea {\n          resize: none;\n                }\n    .swtPrettyPrintTextArea {\n                    -moz-appearance: textfield-multiline;\n                    -webkit-appearance: textarea;\n                    background-color: #FFF;\n                    overflow: auto;\n                    border: 1px solid #A9A9A9;\n                    padding: 5px;\n                    font-size: 11px;\n                    font-weight: bold;\n                    font-family: sans-serif;\n                }\n          "]}]}], "members": {"onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 54, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 55, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "backgroundColor": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 79, "character": 5}}]}], "tabIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 80, "character": 5}, "arguments": ["tabIndex"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 81, "character": 5}, "arguments": ["id"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 82, "character": 5}, "arguments": ["toolTip"]}]}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 83, "character": 5}, "arguments": ["enabled"]}]}], "change_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 85, "character": 5}, "arguments": ["change"]}]}], "focusOut_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 86, "character": 5}, "arguments": ["focusOut"]}]}], "focusIn_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 87, "character": 5}, "arguments": ["focusIn"]}]}], "swtPrettyPrintTextArea": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 88, "character": 5}, "arguments": ["swtPrettyPrintTextArea"]}]}], "ngxCodeMirror": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 89, "character": 5}, "arguments": ["ngxCodeMirror"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 103, "character": 30}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 103, "character": 54}]}], "ngDoCheck": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "handleChange": [{"__symbolic": "method"}], "focusChange": [{"__symbolic": "method"}], "defineMode": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngAfterViewChecked": [{"__symbolic": "method"}], "registerSyntax": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 236, "character": 5}}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 250, "character": 5}}]}], "paddingTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 263, "character": 5}}]}], "paddingRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 276, "character": 5}}]}], "marginLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 288, "character": 5}}]}], "marginBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 301, "character": 5}}]}], "marginRight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 314, "character": 5}}]}], "marginTop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 327, "character": 5}}]}], "paddingLeft": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 340, "character": 5}}]}], "paddingBottom": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 353, "character": 5}}]}], "text": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 396, "character": 5}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 413, "character": 5}}]}], "spyChanges": [{"__symbolic": "method"}]}}, "ModuleLoader": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 12, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}]}], "loadModule": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}], "unload": [{"__symbolic": "method"}], "dispatchEvent": [{"__symbolic": "method"}]}}, "SwtSlider": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 4, "character": 1}, "arguments": [{"selector": "SwtSlider", "template": "<div  class=\"slider-container range\">\n    <input matInput class=\"sliderInputs\" [(ngModel)]=\"start\" type=\"number\" [disabled]=\"false\">\n    <ng5-slider [(value)]=\"start\" [(highValue)]=\"end\"        [options]=\"options\" (userChange)=\"onUserChange($event)\"></ng5-slider>\n    <input matInput class=\"sliderInputs\" [(ngModel)]=\"end\"  type=\"number\" [disabled]=\"false\">\n     </div>\n    ", "styles": ["\n  .sliderInputs {\n    width : 25px;\n\n  }\n  .slider-container{\n    width : 275px;\n  }\n  ", ""]}]}], "members": {"change_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 26, "character": 3}, "arguments": ["change"]}]}], "createDateRange": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor"}], "ngOnInit": [{"__symbolic": "method"}], "enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 80, "character": 3}}]}], "minimum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 97, "character": 3}}]}], "maximum": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 109, "character": 3}}]}], "values": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 121, "character": 3}}]}], "dataTipFormatString": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 133, "character": 3}}]}], "updateData": [{"__symbolic": "method"}], "onUserChange": [{"__symbolic": "method"}]}}, "AdvancedDataGrid": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 28, "character": 1}, "arguments": [{"selector": "AdvancedDataGrid", "template": "\n        <div class = \"advancedTreeDivItem\" style=\"overflow-x: scroll; overflow-y: hidden; border: 1px solid #cccccc; width: calc(100% - 17px)\"\n             [style.height.%]=\"100\">\n\n            <table   class=\"head\" [style.width.px]=\"headerWidth\" [class.default]=\"visibleColumn.length === 0\">\n                <colgroup>\n                    <col [style.width.px]=\"treeWidth\"/>\n                    <col *ngFor=\"let column of visibleColumn\"  [style.width.px]=\"column.width\"/>\n                </colgroup>\n                <thead>\n                <tr class=\"advancedDataGridHeader\">\n                    <th class=\"header-column\" style=\"border-left: 1px solid #529FED\" [style.width.px]=\"treeWidth\">{{ treeName }}</th>\n                    <ng-container *ngFor=\"let column of visibleColumn let colindex = index\">\n                        <th class=\"header-column\" *ngIf=\"column.visible\" [style.width.px]=\"column.width\" [class.lastcolumn-border]=\"(colindex === visibleColumn.length - 1)\">{{ column.heading }}</th>\n                    </ng-container>\n                </tr>\n                </thead>\n            </table>\n            <div  class=\"scrollable-content\"  style=\"overflow-x: hidden; overflow-y: auto;\" [style.width.px]=\"headerWidth\">\n                <table  #treegrid class=\"advancedDatagrid-content\" [style.width.px]=\"headerWidth\">\n                    <tbody class=\"advancedtree-body\">\n                    <tr>\n                        <td style=\"padding-left: 3px\" [style.width.px]=\"treeWidth\"></td>\n                        <ng-container *ngFor=\"let column of visibleColumn let colIndex = index\">\n                            <td class=\"header-column-r\" [id]=\"colIndex\" [style.width.px]=\"column.width\" *ngIf=\"column.visible\"></td>\n                        </ng-container>\n                    </tr>\n                    </tbody>\n                </table>\n                <div class=\"divTable\"  style=\"width:100%\">\n                    <div class=\"divTableBody\">\n                        <div id=\"advancedTreeNoDataBody\" class=\"divTableRow\">\n\n                        </div>\n\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class=\"scroll-bar\" style=\"display: block; border: none\">\n            <div class=\"header-end\"\n                 style=\"height: 26px; background-color: #529FED; border-top: 1px solid #CCC; border-left: none; width: 100%\"></div>\n            <div class=\"scroller-container\"\n                 style=\"width: 100%; height: calc(100% - 23px); overflow-y: scroll; overflow-x: hidden\">\n                <div class=\"scroller\" style=\"width: 1px;\"></div>\n            </div>\n        </div>\n    ", "styles": ["\n        :host {\n            display: flex;\n            overflow: auto;\n            background-color: #FFF;\n        }\n        .scroll-bar {\n            width: 17px;\n            height: calc(100% - 17px);\n        }\n        @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n            .scroll-bar {\n                width: 19px !important;\n            }\n        }\n\n        .advancedTreeDivItem table td, table th{\n            padding : 2px;\n        }\n\n        .scrollable-content::-webkit-scrollbar {\n            width: 0 !important; /* Remove scrollbar space */\n            background: transparent !important; /* Optional: just make scrollbar invisible */\n            -ms-overflow-style: none !important; /* hide scroll bar for IE*/\n            overflow: -moz-scrollbars-none !important;\n            display: none !important;\n        }\n\n        .scrollable-content {\n            -ms-overflow-style: none;\n            scrollbar-width: none;  // Firefox\n        }\n\n        .advancedDatagrid-content {\n            width: 100%;\n            border: 1px solid #DFDFDF;\n            overflow: hidden;\n        }\n\n        .advancedDatagrid-content:focus {\n            outline: none;\n        }\n\n        /* class to set the last border darkblue*/\n        .lastcolumn-border {\n            border-right: 1px solid #529FED !important;\n        }\n\n        .default {\n            width: 100%;\n        }\n\n        thead tr {\n            height: 25px;\n            background-color: #529FED;\n            color: #FFFFFF;\n            font-size: 11px!important;\n            font-weight: bold!important;\n            font-family: Verdana, Helvatica !important;\n        }\n\n        /* provide some minimal visual accomodation for IE8 and below */\n        .advancedtree-body tr {\n            background: #FFFFFF;\n            border-top: 1px solid #DFDFDF;\n            border-bottom: 1px solid #DFDFDF;\n            text-overflow: clip;\n            white-space: nowrap;\n            overflow: hidden;\n        }\n\n        /*  Define the background color for all the ODD background rows  */\n        .advancedtree-body tr:nth-child(odd) {\n            background: #FFFFFF;\n        }\n\n        /*  Define the background color for all the EVEN background rows  */\n        .advancedtree-body tr:nth-child(even) {\n            background: #E0F0FF;\n        }\n\n        table.fancytree-ext-table tbody tr.fancytree-active {\n            background-color: #FFCC66 !important;\n        }\n        .fancytree-selected {\n            background-color: #FFCC66 !important;\n        }\n        .span.fancytree-title {\n            color:black !important;\n        }\n\n        .fancytree-active span.fancytree-title {\n            background-color: transparent !important;\n        }\n\n        .header-column {\n            border-left: 1px solid #FFFFFF;\n            border-right: 1px solid #FFFFFF;\n            padding-left: 10px;\n            text-align: center;\n        }\n\n        .head {\n            table-layout: fixed;\n        }\n        .header-column-r {\n            border: 1px solid #DFDFDF;\n            padding: 0 0 0 0;\n            margin: 0px;\n        }\n\n\n\n    "]}]}], "members": {"treegrid": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 194, "character": 5}, "arguments": ["treegrid"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 198, "character": 5}, "arguments": ["width"]}]}], "rowClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 246, "character": 5}, "arguments": ["rowClick"]}]}], "rowDbClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 249, "character": 5}, "arguments": ["rowDbClick"]}]}], "itemExpand": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 252, "character": 5}, "arguments": ["itemExpand"]}]}], "itemCollapse": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 255, "character": 5}, "arguments": ["itemCollapse"]}]}], "activate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 258, "character": 5}, "arguments": ["activate"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 280, "character": 32}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngAfterViewInit": [{"__symbolic": "method"}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 302, "character": 5}}]}], "calculateDivHeight": [{"__symbolic": "method"}], "dataProvider": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 328, "character": 5}}]}], "ngOnInit": [{"__symbolic": "method"}], "paintColumns": [{"__symbolic": "method"}], "getScrollbarWidth": [{"__symbolic": "method"}], "getTreeStates": [{"__symbolic": "method"}], "setTreeStates": [{"__symbolic": "method"}], "getTreeInstance": [{"__symbolic": "method"}], "setAdvancedGridMetaData": [{"__symbolic": "method"}], "saveOpenTreeState": [{"__symbolic": "method"}], "openSavedTreeState": [{"__symbolic": "method"}], "getAdvancedGridMetaData": [{"__symbolic": "method"}], "setConnectors": [{"__symbolic": "method"}], "advancedDataGridRowDbClick": [{"__symbolic": "method"}], "advancedDataGridRowClick": [{"__symbolic": "method"}], "onGroupItemRender": [{"__symbolic": "method"}], "getSelectedRow": [{"__symbolic": "method"}], "setSelectedRow": [{"__symbolic": "method"}], "getSelectedColumn": [{"__symbolic": "method"}], "getSelectedCell": [{"__symbolic": "method"}], "collapseAll": [{"__symbolic": "method"}], "expandAll": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "refresh": [{"__symbolic": "method"}], "expandChildrenOf": [{"__symbolic": "method"}], "expandItem": [{"__symbolic": "method"}], "getParentItem": [{"__symbolic": "method"}], "isItemOpen": [{"__symbolic": "method"}], "setItemIcon": [{"__symbolic": "method"}], "isFirstLoad": [{"__symbolic": "method"}], "deepCopy": [{"__symbolic": "method"}], "synchronizeHeaderLayout": [{"__symbolic": "method"}], "isIE": [{"__symbolic": "method"}], "componentToHex": [{"__symbolic": "method"}], "rgbToHex": [{"__symbolic": "method"}], "recursive": [{"__symbolic": "method"}], "initialize": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}]}}, "AdvancedDataGridCell": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 1297, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "CommonService"}]}], "getParentRow": [{"__symbolic": "method"}], "setParentRow": [{"__symbolic": "method"}], "renderCell": [{"__symbolic": "method"}], "getItemRander": [{"__symbolic": "method"}], "getColumnHeader": [{"__symbolic": "method"}]}}, "AdvancedDataGridRow": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 1026, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "CommonService"}]}], "isExpanded": [{"__symbolic": "method"}], "isSelected": [{"__symbolic": "method"}], "getDataAttribute": [{"__symbolic": "method"}], "createCells": [{"__symbolic": "method"}], "getParentItem": [{"__symbolic": "method"}], "getRowData": [{"__symbolic": "method"}], "getCells": [{"__symbolic": "method"}], "getCellAt": [{"__symbolic": "method"}], "expand": [{"__symbolic": "method"}], "collapse": [{"__symbolic": "method"}]}}, "LinkItemRander": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "LinkItemRander", "template": "\n        <span class=\"link\" [id]=\"id\" (click)=\"linkItemRenderClickHandler($event)\">\n            {{ text }}\n        </span>\n    ", "styles": ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .link {\n            display: block;\n            width: 100%;\n            text-decoration: underline;\n            color: #52AEFB;\n            text-align: right;\n            padding: 0 5px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n        .link:hover {\n            cursor: pointer;\n        }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 44, "character": 37}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "linkItemRenderClickHandler": [{"__symbolic": "method"}]}}, "DateUtils": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}]}, "statics": {"timepat1": {"__symbolic": "error", "message": "Expression form not supported", "line": 9, "character": 27}, "timepat2": {"__symbolic": "error", "message": "Expression form not supported", "line": 10, "character": 27}, "timepat3": {"__symbolic": "error", "message": "Expression form not supported", "line": 11, "character": 27}, "dateFromString": {"__symbolic": "function", "parameters": ["dateString", "pattern"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 107, "character": 9, "module": "./src/app/modules/swt-toolbox/com/swallow/utils/date-utils.service"}}}}, "SwtSummary": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 19, "character": 1}, "arguments": [{"selector": "SwtSummary", "template": "        \n        <VBox #mainHGroup id=\"mainHGroup\"  width=\"100%\" height=\"100%\">\n            <HDividedBox id=\"divBox\" #divBox  width=\"100%\" height=\"90%\">\n                <VBox id=\"treeContainer\" #treeContainer width=\"40%\" height=\"100%\" class=\"left\">\n                    <SwtLabel  id=\"treeTitle\" #treeTitle\n                               fontWeight=\"normal\"\n                               height=\"8%\"\n                               paddingBottom=\"10\"\n                               paddingLeft=\"5\">\n                    </SwtLabel>\n                    <CustomTree id=\"tree\" #tree\n                                width=\"100%\"\n                                height=\"92%\"\n                                doubleClickEnabled=\"true\"\n                                (keyDown)=\"treeKeyDownHandler($event)\"\n                                (itemDoubleClick)=\"treeItemDoubleClickHandler($event)\">\n                    </CustomTree>\n                </VBox>\n                <VBox height=\"100%\"  width=\"60%\" id=\"gridContainer\" #gridContainer  class=\"right\">\n                    <SwtLabel id=\"gridTitle\"\n                              #gridTitle\n                              height=\"8%\"\n                              fontWeight=\"normal\"\n                              paddingBottom=\"10\">\n                    </SwtLabel>\n                    <SwtCanvas id=\"customGrid\" #customGrid width=\"100%\" height=\"92%\" borderStyle=\"solid\" cornerRadius=\"4\" dropShadowEnabled=\"true\" borderColor=\"#f9f9f9\" >\n                        <SwtCommonGrid \n                                (onFilterChanged)=\"saveGridVisibleState($event)\"\n                                id=\"summaryGrid\" \n                                #summaryGrid \n                                width=\"100%\" \n                                height=\"100%\">\n                        </SwtCommonGrid>\n                    </SwtCanvas>\n                </VBox>\n            </HDividedBox>\n            <HBox width=\"100%\" height=\"10%\"\n                  horizontalAlign=\"right\"\n                  paddingTop=\"6\"\n                  paddingRight=\"10\">\n                <SwtLabel id=\"lastRanLbl\"  #lastRanLbl  fontWeight=\"normal\" visible=\"false\">\n                </SwtLabel>\n            </HBox>\n        </VBox>\n  "}]}], "members": {"styleName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 69, "character": 5}, "arguments": ["styleName"]}]}], "IDField": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 70, "character": 5}, "arguments": ["IDField"]}]}], "expandFirstLoad": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 71, "character": 5}, "arguments": ["expandFirstLoad"]}]}], "gridTitle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 72, "character": 5}, "arguments": ["gridTitle"]}]}], "treeTitle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 73, "character": 5}, "arguments": ["treeTitle"]}]}], "lastRanLbl": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 74, "character": 5}, "arguments": ["lastRanLbl"]}]}], "divBox": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 75, "character": 5}, "arguments": ["divBox"]}]}], "tree": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 76, "character": 5}, "arguments": ["tree"]}]}], "mainHGroup": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 77, "character": 5}, "arguments": ["mainHGroup"]}]}], "treeContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 78, "character": 5}, "arguments": ["treeContainer"]}]}], "customGrid": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 79, "character": 5}, "arguments": ["customGrid"]}]}], "summaryGrid": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 80, "character": 5}, "arguments": ["summaryGrid"]}]}], "gridContainer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 81, "character": 5}, "arguments": ["gridContainer"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 121, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2", "line": 121, "character": 99}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "setLabelField": [{"__symbolic": "method"}], "hideRoot": [{"__symbolic": "method"}], "getSortedGridColumn": [{"__symbolic": "method"}], "getIsScenarioAlertable": [{"__symbolic": "method"}], "getIsBranch": [{"__symbolic": "method"}], "getDividerPosition": [{"__symbolic": "method"}], "setActionPath": [{"__symbolic": "method"}], "setBaseURL": [{"__symbolic": "method"}], "setAccessRules": [{"__symbolic": "method"}], "getSelectedItemID": [{"__symbolic": "method"}], "setFirstSubNode": [{"__symbolic": "method"}], "enableTree": [{"__symbolic": "method"}], "disableTree": [{"__symbolic": "method"}], "showTreeDataTips": [{"__symbolic": "method"}], "dataTipFunction": [{"__symbolic": "method"}], "treeNodeEventHandler": [{"__symbolic": "method"}], "saveTreeOpenState": [{"__symbolic": "method"}], "openTreeItems": [{"__symbolic": "method"}], "treeScrollEventHandler": [{"__symbolic": "method"}], "treeItemDoubleClickHandler": [{"__symbolic": "method"}], "isItemOpen": [{"__symbolic": "method"}], "changeTreeData": [{"__symbolic": "method"}], "treeKeyDownHandler": [{"__symbolic": "method"}], "resetGridData": [{"__symbolic": "method"}], "dataProvider": [{"__symbolic": "method"}], "setScenarioLastRan": [{"__symbolic": "method"}], "setClickable": [{"__symbolic": "method"}], "showVisibleItems": [{"__symbolic": "method"}], "saveGridVisibleState": [{"__symbolic": "method"}], "saveGridOpenState": [{"__symbolic": "method"}], "deepCopy": [{"__symbolic": "method"}], "openGridItems": [{"__symbolic": "method"}], "setTreeTotals": [{"__symbolic": "method"}]}}, "EnhancedAlertingTooltip": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 16, "character": 1}, "arguments": [{"selector": "EnhancedAlertingTooltip", "template": "\n        <VBox  paddingLeft=\"3\"  paddingRight=\"3\" paddingTop=\"3\" #customTooltip width=\"100%\" height=\"100%\" verticalGap=\"2\"> \n            <VBox id=\"treeContainer\" #treeContainer width=\"100%\" height=\"100%\" class=\"left\">\n            <HBox width=\"100%\" height=\"4%\">\n                <SwtLabel #ccyLabel  paddingLeft=\"5\" textDictionaryId=\"label.alertSummaryTooltip.facility\" ></SwtLabel>\n                <SwtLabel  id=\"facilityName\" #facilityName\n                        fontWeight=\"normal\"\n                        paddingLeft=\"5\">\n                </SwtLabel>\n            </HBox>\n            \n\n            <HBox height=\"8%\" width=\"100%\">\n                <SwtLabel #ccyLabel paddingLeft=\"5\" textDictionaryId=\"label.alertSummaryTooltip.parameters\" ></SwtLabel>\n                <SwtLabel  id=\"paramsList\" #paramsList\n                fontWeight=\"normal\"\n                paddingLeft=\"5\"></SwtLabel>\n            </HBox>\n            <HBox height=\"5%\" width=\"100%\" paddingBottom=\"7\">\n            <SwtLabel  id=\"treeTitle\" #treeTitle\n                    fontWeight=\"normal\"\n                    paddingLeft=\"5\">\n            </SwtLabel>\n            </HBox>\n            <CustomTree id=\"tree\" #tree\n                        width=\"100%\"\n                        height=\"75%\"\n                        doubleClickEnabled=\"true\">\n            </CustomTree>\n\n\n            <HBox height=\"6%\" width=\"100%\" paddingTop=\"7\">\n            <HBox height=\"100%\" width=\"35%\">\n                <SwtButton  buttonMode=\"true\"\n                        id=\"closeButton\" #closeButton\n                            (click)=\"closeTooltip()\"> </SwtButton>\n            </HBox>\n            <HBox height=\"100%\" width=\"65%\" horizontalAlign=\"right\">\n            <SwtButton (click)=\"displayListEventhandler()\"\n            buttonMode=\"true\"\n                            id=\"displayListButton\" #displayListButton></SwtButton>\n            <SwtButton buttonMode=\"true\" \n             id=\"linkToSpecificButton\" #linkToSpecificButton\n                        (click)=\"linkToSpecifiEventHandler()\"> </SwtButton>\n            </HBox>\n            </HBox>\n            </VBox>\n        </VBox>\n  ", "styles": ["\n      "]}]}], "members": {"customTooltip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 71, "character": 5}, "arguments": ["customTooltip"]}]}], "tree": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 72, "character": 5}, "arguments": ["tree"]}]}], "treeTitle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 73, "character": 5}, "arguments": ["treeTitle"]}]}], "facilityName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 74, "character": 5}, "arguments": ["facilityName"]}]}], "paramsList": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 75, "character": 5}, "arguments": ["paramsList"]}]}], "closeButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 76, "character": 5}, "arguments": ["closeButton"]}]}], "linkToSpecificButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 77, "character": 5}, "arguments": ["linkToSpecificButton"]}]}], "displayListButton": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 78, "character": 5}, "arguments": ["displayListButton"]}]}], "ITEM_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 79, "character": 5}, "arguments": ["ITEM_CLICK"]}]}], "DISPLAY_LIST_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 80, "character": 5}, "arguments": ["DISPLAY_LIST_CLICK"]}]}], "LINK_TO_SPECIF_CLICK": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 81, "character": 5}, "arguments": ["LINK_TO_SPECIF_CLICK"]}]}], "ngOnInit": [{"__symbolic": "method"}], "closeTooltip": [{"__symbolic": "method"}], "displayListEventhandler": [{"__symbolic": "method"}], "getParamsList": [{"__symbolic": "method"}], "linkToSpecifiEventHandler": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "treeEventHandler": [{"__symbolic": "method"}], "extractParentFilterDataFromNode": [{"__symbolic": "method"}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 252, "character": 30}, {"__symbolic": "reference", "name": "CommonService"}]}], "boxRollOutEventListner": [{"__symbolic": "method"}], "createCustomTip": [{"__symbolic": "method"}], "inputDataResult": [{"__symbolic": "method"}], "htmlEntities": [{"__symbolic": "method"}], "startOfComms": [{"__symbolic": "method"}], "endOfComms": [{"__symbolic": "method"}], "inputDataFault": [{"__symbolic": "method"}], "recalculateDataAlert": [{"__symbolic": "method"}], "alertListener": [{"__symbolic": "method"}]}}, "Series": {"__symbolic": "class", "members": {}}, "DataExportMultiPage": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtDataExport"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 16, "character": 1}, "arguments": [{"selector": "DataExportMultiPage", "template": "\n  <div>\n\t\t\t<SwtComboBox id=\"exportDataComponent\" (inputClick)=\"onDataExportClick($event)\" \n\t\t\t(change)=\"onDataExportClick($event)\" editable=\"false\" #exportDataComponent width=\"43\"></SwtComboBox>\n  </div>\n  ", "styles": ["\n  "]}]}], "members": {"ngOnInit": [{"__symbolic": "method"}], "onDataExportClick": [{"__symbolic": "method"}], "createPopup": [{"__symbolic": "method"}], "closePopup": [{"__symbolic": "method"}], "showReportProgress": [{"__symbolic": "method"}], "onConfimClose": [{"__symbolic": "method"}], "generateReport": [{"__symbolic": "method"}], "closeCancelPopup": [{"__symbolic": "method"}], "progresswinPopup": [{"__symbolic": "method"}], "onProgressCancelClick": [{"__symbolic": "method"}]}}, "JSONViewer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "SwtModule"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 8, "character": 1}, "arguments": [{"selector": "swt-jsonviewer", "providers": [{"__symbolic": "reference", "name": "CommonService"}], "template": "<ngx-json-viewer [json]=\"data\" #jsonViewer  id=\"jsonViewer\"  (close)=\"close()\"></ngx-json-viewer>\r\n", "styles": [""]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CommonService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 24, "character": 71}]}], "ngOnInit": [{"__symbolic": "method"}], "close": [{"__symbolic": "method"}]}}, "TabSelectEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 2, "character": 34}}, "DividerResizeComplete": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 3, "character": 41}}, "TabCloseEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 4, "character": 33}}, "WindowDragEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 5, "character": 35}}, "WindowDragStartEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 6, "character": 40}}, "WindowDragEndEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 7, "character": 38}}, "WindowCloseEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 8, "character": 36}}, "WindowCreateEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 9, "character": 37}}, "WindowResizeEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 10, "character": 37}}, "WindowMinimizeEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 11, "character": 39}}, "WindowMaximizeEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 12, "character": 39}}, "HDividedEndResizeEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 13, "character": 42}}, "VDividedEndResizeEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 14, "character": 42}}, "TabChange": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 15, "character": 29}}, "TabClose": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 16, "character": 28}}, "SwtCommonGridItemRenderChanges": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 17, "character": 50}}, "ExportEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 18, "character": 31}}, "AdvancedExportEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 19, "character": 39}}, "HorizontalScrollPositionEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 20, "character": 49}}, "VerticalScrollPositionEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 21, "character": 47}}, "SeriesHighlightEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 22, "character": 40}}, "LegendItemChangedEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 23, "character": 42}}, "SwtCheckboxEvent": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "EventEmitter", "line": 24, "character": 36}}, "WindowEvent": {"__symbolic": "class", "members": {}, "statics": {"DRAG": "drag", "DRAGSTART": "dragstart", "DRAGEND": "dragend", "WINDOWCLOSE": "windowClose", "CREATE": "create", "RESIZE": "resize", "MINIMIZE": "minimize", "MAXIMIZE": "maximize"}}, "CustomTreeEvent": {"__symbolic": "class", "members": {}, "statics": {"ITEMRENDER": "itemrender", "ITEMDOUBLECLICK": "itemDoubleClick", "ITEMCLICK": "itemClick", "ICONCLICK": "iconclick", "EXPANDERCLICK": "expanderclick", "TITLECLICK": "titleclick", "ICONFOCUS": "iconfocus", "ICONFOCUSOUT": "iconfocusout", "ICONMOUSELEAVE": "iconmouseleave", "ICONMOUSEENTER": "icon<PERSON><PERSON>ter"}}, "genericEvent": {"__symbolic": "class", "members": {}, "statics": {"ACTIVATE": "activate", "MOUSE_OVER": "mouseover", "MOUSE_UP": "mouseup", "MOUSE_DOWN": "mousedown", "FOCUS": "focus", "FOCUSOUT": "focusout", "ADDED": "added", "ADDED_TO_STAGE": "addedToStage", "BROWSER_ZOOM_CHANGE": "browserZoomChange", "CANCEL": "cancel", "CHANGE": "change", "CHANNEL_MESSAGE": "channelMessage", "CHANNEL_STATE": "channelState", "CLEAR": "clear", "CLOSE": "close", "CLOSING": "closing", "COMPLETE": "complete", "CONNECT": "connect", "CONTEXT3D_CREATE": "context3DCreate", "COPY": "copy", "CUT": "cut", "DEACTIVATE": "deactivate", "DISPLAYING": "displaying", "ENTER_FRAME": "enterFrame", "EXIT_FRAME": "exitFrame", "EXITING": "exiting", "FRAME_CONSTRUCTED": "frameConstructed", "FRAME_LABEL": "frame<PERSON>abel", "FULLSCREEN": "fullScreen", "HTML_BOUNDS_CHANGE": "htmlBoundsChange", "HTML_DOM_INITIALIZE": "htmlDOMInitialize", "HTML_RENDER": "htmlRender", "ID3": "id3", "INIT": "init", "LOCATION_CHANGE": "locationChange", "MOUSE_LEAVE": "mouseLeave", "NETWORK_CHANGE": "networkChange", "OPEN": "open", "PASTE": "paste", "PREPARING": "preparing", "REMOVED": "removed", "REMOVED_FROM_STAGE": "removedFromStage", "RENDER": "render", "RESIZE": "resize", "SCROLL": "scroll", "SELECT": "select", "SELECT_ALL": "selectAll", "SOUND_COMPLETE": "soundComplete", "STANDARD_ERROR_CLOSE": "standardErrorClose", "STANDARD_INPUT_CLOSE": "standardInputClose", "STANDARD_OUTPUT_CLOSE": "standardOutputClose", "SUSPEND": "suspend", "TAB_CHILDREN_CHANGE": "tabChildrenChange", "TAB_ENABLED_CHANGE": "tabEnabledChange", "TAB_INDEX_CHANGE": "tabIndexChange", "TEXT_INTERACTION_MODE_CHANGE": "textInteractionModeChange", "TEXTURE_READY": "textureReady", "UNLOAD": "unload", "USER_IDLE": "userIdle", "USER_PRESENT": "userPresent", "VIDEO_FRAME": "videoFrame", "WORKER_STATE": "workerState", "ROW_CLICK": "rowClick", "CELL_CLICK": "cellclick", "ROW_DBCLICK": "rowDbClick", "CELL_DBCLICK": "celldbclick", "ITEM_EXPAND": "itemExpand", "ITEM_COLLAPSE": "itemCollapse", "CLICK": "click"}}, "SwtEventsModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 278, "character": 1}, "arguments": [{"declarations": [], "imports": []}]}], "members": {}}, "SwtMultiselectCombobox": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 4, "character": 1}, "arguments": [{"selector": "SwtMultiselectCombobox", "template": "<ng-multiselect-dropdown-angular7 class=\"multiselect\"\n    [placeholder]=\"placeholder\"\n    [disabled] =\"isDropdownDisabled\"\n    [(ngModel)]=\"defaultSelectedItems\"\n    [data]=\"dataProvider\"\n    [settings]=\"dropdownSettings\"\n    (onSelect)=\"onItemSelect($event)\"\n    (onSelectAll)=\"onSelectAll($event)\"\n    (onDeSelect) = \"onItemDeSelect($event)\"\n    (onDeSelectAll) = \"onDeSelectAll($event)\"\n  >\n  </ng-multiselect-dropdown-angular7>\n  ", "styles": ["\n\n       .dropdown-btn {\n         display: inline-block;\n         background: #ffffff;\n         padding-top: 2px !important;\n         padding-right: 0px !important;\n         padding-bottom: 1px !important;\n         padding-left: 4px !important;\n         border-top: 1px solid #7F9DB9 !important;\n         border-left: 1px solid #7F9DB9 !important;\n         border-right: 1px solid #7F9DB9 !important;\n         border-bottom: 1px solid #7F9DB9 !important;\n         color:#808080; /*to over write fieldset color*/\n         background-image: linear-gradient(to left, #ccddea 1px, #A7C6DE 20px,#ffffff 2px, #ffffff) !important;\n       }\n\n       .multiselect-dropdown .dropdown-btn .dropdown-down {\n            display: inline-block;\n            margin-top: 6px;\n            border-top: 5px solid #555 !important;\n            border-left: 5px solid transparent !important;\n            border-right: 5px solid transparent !important;\n          }\n\n        .multiselect-dropdown .dropdown-btn .dropdown-up {\n          display: inline-block;\n          margin-top: 5px;\n          border-bottom: 5px solid #555 !important;\n          border-left: 5px solid transparent !important;\n          border-right: 5px solid transparent !important;\n        }\n        .multiselect {\n         display: inline-block;\n         width: 200px;\n         height: 70px;\n         padding: 6px 12px;\n         margin-bottom: 0;\n         font-weight: 400;\n         line-height: 1.52857143;\n         text-align: left;\n         vertical-align: middle;\n         cursor: pointer;\n         background-image: none;\n         border-radius: 10px;\n        }\n        .dropdown-list{\n          position: absolute;\n          padding-top: 1px !important;\n          width: 100%;\n          z-index: 9999;\n          border: 1px solid #ccc;\n          border-radius: 3px;\n          background: #fff;\n          box-shadow: 0 1px 5px #959595; \n        }\n        .multiselect-item-checkbox{\n          padding-top: 2px !important;\n          padding-right: 3px !important;\n          padding-bottom: 2px !important;\n          padding-left: 3px !important;\n        }\n        .filter-textbox{\n          padding-top: 1px !important;\n          padding-right: 1px !important;\n          padding-bottom: 1px !important;\n          padding-left: 1px !important;\n        }\n        .ng-pristine.ng-valid.ng-touched{\n          padding-top: 0px !important;\n          padding-right: 0px !important;\n          padding-bottom: 0px !important;\n          padding-left: 30px !important;\n          \n        }\n\n        div.tooltip-inner{\n          background-color: #F1F1DE !important;\n          width: auto !important;\n          max-width:1200px !important;\n          min-width: 10px !important;\n          margin-top: -10px !important;\n          -moz-transition-delay: 0s !important;\n          transition-delay: 0s !important;\n          box-shadow: 0px 5px 8px #7A8D99  !important;\n          padding: 3px 5px 3px 5px !important;\n          border-radius: 5px;\n          transition: opacity  5s linear 0s !important;\n          transition-delay: 0s !important;\n          color: #000;\n          font-size: 10px;\n        }\n\n        div.tooltip-arrow{\n          border-left: 0 solid #000000 !important;\n          border-right: 0 solid #000000 !important;\n          border-bottom: 0 solid #000000 !important;\n          border-top: 0 solid #000000 !important;\n        }\n        .selected-item{\n          margin-right: 4px !important;\n          margin-bottom: 4px !important;\n        }\n        "], "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation", "line": 124, "character": 17}, "member": "None"}}]}], "members": {"ITEM_SELECT": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 127, "character": 3}, "arguments": ["ITEM_SELECT"]}]}], "ITEM_DESELECT": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 128, "character": 3}, "arguments": ["ITEM_DESELECT"]}]}], "SELECT_ALL": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 129, "character": 3}, "arguments": ["SELECT_ALL"]}]}], "DESELECT_ALL": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 130, "character": 3}, "arguments": ["DESELECT_ALL"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 131, "character": 28}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 185, "character": 3}, "arguments": ["width"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 187, "character": 3}, "arguments": ["height"]}]}], "dropHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 189, "character": 3}, "arguments": ["dropHeight"]}]}], "ngOnInit": [{"__symbolic": "method"}], "shiftUp": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 205, "character": 3}}]}], "showAbove": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 215, "character": 3}}]}], "visible": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 230, "character": 1}, "arguments": ["visible"]}]}], "toolTip": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 247, "character": 1}}]}], "onItemSelect": [{"__symbolic": "method"}], "onSelectAll": [{"__symbolic": "method"}], "onItemDeSelect": [{"__symbolic": "method"}], "onDeSelectAll": [{"__symbolic": "method"}]}}, "ɵb": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 14, "character": 1}, "arguments": [{"providedIn": "root"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 29, "character": 33}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 29, "character": 77}, {"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationRef", "line": 29, "character": 111}]}], "createWindow": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "show": [{"__symbolic": "method"}], "createAlert": [{"__symbolic": "method"}], "removeAll": [{"__symbolic": "method"}], "close": [{"__symbolic": "method"}], "getWindow": [{"__symbolic": "method"}], "getFirstWindowInstance": [{"__symbolic": "method"}], "getLastWindowInsatance": [{"__symbolic": "method"}], "getWindowInstance": [{"__symbolic": "method"}], "getFirstLayoutOrder": [{"__symbolic": "method"}], "getLastLayoutOrder": [{"__symbolic": "method"}], "getLayoutOrder": [{"__symbolic": "method"}]}, "statics": {"ngInjectableDef": {}}}, "ɵc": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵd"}, "members": {"maxChars": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 21, "character": 5}}]}], "restrict": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 33, "character": 5}, "arguments": ["restrict"]}]}], "id": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 51, "character": 5}, "arguments": ["id"]}]}], "onClick_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 63, "character": 5}, "arguments": ["click"]}]}], "dbClick_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 64, "character": 5}, "arguments": ["dbClick"]}]}], "doubleClick_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 65, "character": 5}, "arguments": ["doubleClick"]}]}], "itemDoubleClick_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 66, "character": 5}, "arguments": ["itemDoubleClick"]}]}], "onKeyDown_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 68, "character": 5}, "arguments": ["keyDown"]}]}], "onKeyUp_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 69, "character": 5}, "arguments": ["keyUp"]}]}], "mouseUp_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 71, "character": 5}, "arguments": ["mouseUp"]}]}], "mouseOver_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 72, "character": 5}, "arguments": ["mouseOver"]}]}], "mouseDown_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 73, "character": 5}, "arguments": ["mouseDown"]}]}], "mouseEnter_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 74, "character": 5}, "arguments": ["mouseEnter"]}]}], "mouseLeave_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 75, "character": 5}, "arguments": ["mouseLeave"]}]}], "mouseOut_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 76, "character": 5}, "arguments": ["mouseOut"]}]}], "mouseIn_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 77, "character": 5}, "arguments": ["mouseIn"]}]}], "mouseMove_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 78, "character": 5}, "arguments": ["mouseMove"]}]}], "focus_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 80, "character": 5}, "arguments": ["focus"]}]}], "focusIn_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 81, "character": 5}, "arguments": ["focusIn"]}]}], "onFocusOut_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 82, "character": 5}, "arguments": ["focusOut"]}]}], "keyFocusChange_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 83, "character": 5}, "arguments": ["keyFocus<PERSON>hange"]}]}], "change_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 85, "character": 5}, "arguments": ["change"]}]}], "onSpyChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 87, "character": 5}, "arguments": ["onSpyChange"]}]}], "onSpyNoChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 88, "character": 5}, "arguments": ["onSpyNoChange"]}]}], "scroll_": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 90, "character": 5}, "arguments": ["scroll"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "CommonService"}]}], "addStyleClient": [{"__symbolic": "method"}], "getCursorLocation": [{"__symbolic": "method"}], "callLater": [{"__symbolic": "method"}], "clearStyle": [{"__symbolic": "method"}], "contentToGlobal": [{"__symbolic": "method"}], "contentToLocal": [{"__symbolic": "method"}], "createAutomationIDPart": [{"__symbolic": "method"}], "createAutomationIDPartWithRequiredProperties": [{"__symbolic": "method"}], "createReferenceOnParentDocument": [{"__symbolic": "method"}], "deleteReferenceOnParentDocument": [{"__symbolic": "method"}], "determineTextFormatFromStyles": [{"__symbolic": "method"}], "drawFocus": [{"__symbolic": "method"}], "drawRoundRect": [{"__symbolic": "method"}], "effectFinished": [{"__symbolic": "method"}], "effectStarted": [{"__symbolic": "method"}], "endEffectsStarted": [{"__symbolic": "method"}], "executeBindings": [{"__symbolic": "method"}], "finishPrint": [{"__symbolic": "method"}], "getAutomationChildAt": [{"__symbolic": "method"}], "getAutomationChildren": [{"__symbolic": "method"}], "getBoundsXAtSize": [{"__symbolic": "method"}], "getBoundsYAtSize": [{"__symbolic": "method"}], "getClassStyleDeclarations": [{"__symbolic": "method"}], "getConstraintValue": [{"__symbolic": "method"}], "getExplicitOrMeasuredHeight": [{"__symbolic": "method"}], "getExplicitOrMeasuredWidth": [{"__symbolic": "method"}], "getFocus": [{"__symbolic": "method"}], "getLayoutBoundsHeight": [{"__symbolic": "method"}], "getLayoutBoundsWidth": [{"__symbolic": "method"}], "getLayoutBoundsX": [{"__symbolic": "method"}], "getLayoutBoundsY": [{"__symbolic": "method"}], "getLayoutMatrix": [{"__symbolic": "method"}], "getLayoutMatrix3D": [{"__symbolic": "method"}], "getMaxBoundsHeight": [{"__symbolic": "method"}], "getMaxBoundsWidth": [{"__symbolic": "method"}], "getMinBoundsHeight": [{"__symbolic": "method"}], "getMinBoundsWidth": [{"__symbolic": "method"}], "getPreferredBoundsHeight": [{"__symbolic": "method"}], "getPreferredBoundsWidth": [{"__symbolic": "method"}], "getRepeaterItem": [{"__symbolic": "method"}], "getStyle": [{"__symbolic": "method"}], "globalToContent": [{"__symbolic": "method"}], "hasCSSState": [{"__symbolic": "method"}], "hasState": [{"__symbolic": "method"}], "getBroserType": [{"__symbolic": "method"}], "horizontalGradientMatrix": [{"__symbolic": "method"}], "initialize": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "setFocus": [{"__symbolic": "method"}], "setVisible": [{"__symbolic": "method"}], "addChild": [{"__symbolic": "method"}], "addEventsListenersForTooltip": [{"__symbolic": "method"}], "addAllOutputsEventsListeners": [{"__symbolic": "method"}], "removeAllOuputsEventsListeners": [{"__symbolic": "method"}], "getComponentName": [{"__symbolic": "method"}], "adaptUnit": [{"__symbolic": "method"}], "adaptValueAsBoolean": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "ɵd": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵe"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "CommonService"}]}], "addEventListener": [{"__symbolic": "method"}], "dispatchEvent": [{"__symbolic": "method"}], "hasEventListener": [{"__symbolic": "method"}], "removeEventListener": [{"__symbolic": "method"}], "willTrigger": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "ɵe": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "bindAttribute": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "renameAttr": [{"__symbolic": "method"}], "getNumberFrom": [{"__symbolic": "method"}]}}, "ɵf": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 2, "character": 1}, "arguments": [{"selector": "swt-drop-down-list", "template": "\n        <div class=\"swt-dropdown-container\">\n            <div class=\"swt-dropdown-items\">\n                <!--<div class=\"swt-dropdown-item\" id=\"0\"></div>-->\n            </div>\n            <div class=\"swt-virtual-scroll\">\n                <div class=\"swt-dropdown-scrollhandler\"></div>\n            </div>\n        </div>\n    ", "styles": ["\n        .swt-dropdown-container {\n            display: flex;\n            outline: 1px solid #cccccc;\n            max-height: 147px;\n            width: 100%;\n            box-sizing: border-box;\n        }\n\n        .swt-dropdown-items, .swt-virtual-scroll {\n            height: 100%;\n        }\n\n        .swt-dropdown-items {\n            background-color: #FFF;\n            width: calc(100% - 13px);\n            display: block;\n        }\n\n        .swt-virtual-scroll {\n            /*border: 1px solid green;*/\n            width: 13px;\n            overflow-x: hidden;\n            overflow-y: auto;\n            display: none;\n        }\n\n        .swt-dropdown-scrollhandler {\n            /*border: 1px solid purple;*/\n            width: 2px;\n        }\n    "]}]}], "members": {"onItemClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 49, "character": 5}}]}], "onItemChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 51, "character": 5}}]}], "onClickOutSide": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 53, "character": 5}}]}], "onItemNavigation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 55, "character": 5}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 69, "character": 36}]}], "selectedIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 88, "character": 5}}]}], "selectedItem": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 104, "character": 5}}]}], "selectedLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 115, "character": 5}}]}], "selectedValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 126, "character": 5}}]}], "dataLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 137, "character": 5}}]}], "dataSource": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 151, "character": 5}}]}], "ngOnInit": [{"__symbolic": "method"}], "clickoutEventHandler": [{"__symbolic": "method", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostListener", "line": 186, "character": 5}, "arguments": ["document:click", ["$event"]]}]}], "ngOnDestroy": [{"__symbolic": "method"}], "onMouseWheelMotion": [{"__symbolic": "method"}], "scrollToIndex": [{"__symbolic": "method"}], "navigateDown": [{"__symbolic": "method"}], "navigateUp": [{"__symbolic": "method"}], "validateNow": [{"__symbolic": "method"}], "filter": [{"__symbolic": "method"}], "clearFilter": [{"__symbolic": "method"}], "applyDataSource": [{"__symbolic": "method"}]}, "statics": {"DEFAULT_ITEM_NUMBER": 7}}, "ɵg": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 6, "character": 1}, "arguments": [{"selector": "StringItemRender", "template": "\n        <span class=\"string-item-render\" [id]=\"id\">\n            {{ text }}\n        </span>\n    ", "styles": ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .string-item-render {\n            display: block;\n            width: 100%;\n            text-align: justify;\n            padding: 0 5px;\n            margin: 0px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 36, "character": 39}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}]}}, "ɵh": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵc"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "NumberItemRender", "template": "\n        <span class=\"number-item-render\">\n            {{ text }}\n        </span>\n    ", "styles": ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .number-item-render {\n            display: block;\n            width: 100%;\n            text-align: right;\n            padding: 0 5px;\n            margin: 0px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n    "]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 37, "character": 39}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}]}}, "ɵi": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 7, "character": 1}, "arguments": [{"selector": "AdvancedToolTip", "template": "\n        <div class=\"advanced-tooltip\">\n            <ng-container #container></ng-container>\n        </div>\n    ", "styles": ["\n            .advanced-tooltip {\n                width: auto;\n                height: auto;\n                position: fixed;\n                background-color: transparent;\n                display: none;\n            }\n    "], "animations": []}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 32, "character": 36}, {"__symbolic": "reference", "name": "CommonService"}]}], "ngOnInit": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "display": [{"__symbolic": "method"}], "getChild": [{"__symbolic": "method"}]}}, "ɵj": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Container"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 29, "character": 1}, "arguments": [{"selector": "swt-ilm-chart", "template": "<div class=\"chartContainer\" #containerHighChart ></div>", "styles": [""]}]}], "members": {"containerHighChart": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 35, "character": 3}, "arguments": ["containerHighChart"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 92, "character": 28}, {"__symbolic": "reference", "name": "CommonService"}]}], "callMethodByName": [{"__symbolic": "method"}], "setILMData": [{"__symbolic": "method"}], "forceDrawChartIfNotDrawn": [{"__symbolic": "method"}], "getFillPatternForImange": [{"__symbolic": "method"}], "firstLoad": [{"__symbolic": "method"}], "setILMDataZones": [{"__symbolic": "method"}], "destroyChart": [{"__symbolic": "method"}], "getData": [{"__symbolic": "method"}], "mergeNewCharts": [{"__symbolic": "method"}], "createILMChart": [{"__symbolic": "method"}], "updateClock": [{"__symbolic": "method"}], "showHideActualDataSet": [{"__symbolic": "method"}], "showHideThreshold": [{"__symbolic": "method"}], "alignScaleWithSOD": [{"__symbolic": "method"}], "showHideSourcesOfLiquidity": [{"__symbolic": "method"}], "hideThreshold": [{"__symbolic": "method"}], "removeThreshold": [{"__symbolic": "method"}], "showThreshold": [{"__symbolic": "method"}], "checkDataSet": [{"__symbolic": "method"}], "unCheckDataSet": [{"__symbolic": "method"}], "enableAutoredrawAndRedrawChart": [{"__symbolic": "method"}], "adjutMinMax": [{"__symbolic": "method"}], "enableAutoredrawOnly": [{"__symbolic": "method"}], "disableAutoRedraw": [{"__symbolic": "method"}], "calculateStatistics": [{"__symbolic": "method"}], "alignScale": [{"__symbolic": "method"}], "applySODandUnalignScale": [{"__symbolic": "method"}], "updateVisibleThresholds": [{"__symbolic": "method"}], "uncheckSODandalignScale": [{"__symbolic": "method"}], "toDate": [{"__symbolic": "method"}], "showLiquiditySource": [{"__symbolic": "method"}], "hideOrShowBandOrLine": [{"__symbolic": "method"}], "hideLiquiditySource": [{"__symbolic": "method"}], "getPlotBandById": [{"__symbolic": "method"}], "checkMultiplierCurrenyMultiplier": [{"__symbolic": "method"}], "uncheckMultiplierCurrenyMultiplier": [{"__symbolic": "method"}], "smallestLabelSigDigitDcPlaces": [{"__symbolic": "method"}], "getSignificantDigitCount": [{"__symbolic": "method"}], "rightVerticalAxisFormatter": [{"__symbolic": "method"}], "leftVerticalAxisFormatter": [{"__symbolic": "method"}], "commonAxisFormatter": [{"__symbolic": "method"}], "formatMoney": [{"__symbolic": "method"}], "getFirstSignificantDigit": [{"__symbolic": "method"}], "firstSignificant": [{"__symbolic": "method"}], "ccyMultiplierEventHandler": [{"__symbolic": "method"}], "highlightSerie": [{"__symbolic": "method"}], "highlightSerieFunction": [{"__symbolic": "method"}], "unHighlightSerieFunction": [{"__symbolic": "method"}], "showSerie": [{"__symbolic": "method"}], "showOrHideMultipleSeries": [{"__symbolic": "method"}], "removeMultipleCharts": [{"__symbolic": "method"}], "setEntityOrCurrencyTimeFrame": [{"__symbolic": "method"}], "timeConverter": [{"__symbolic": "method"}], "zoom": [{"__symbolic": "method"}], "resetZoom": [{"__symbolic": "method"}], "exportChart": [{"__symbolic": "method"}], "svg_to_png_data": [{"__symbolic": "method"}], "createElementFromHTML": [{"__symbolic": "method"}], "runReport": [{"__symbolic": "method"}], "formatDate": [{"__symbolic": "method"}], "convertToXML": [{"__symbolic": "method"}], "changeChartsStyle": [{"__symbolic": "method"}], "isEmpty": [{"__symbolic": "method"}], "addZero": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "redrawChart": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}]}}}, "origins": {"ɵa": "./src/app/modules/swt-toolbox/com/swallow/controls/title-window.component", "ModuleEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "TitleWindow": "./src/app/modules/swt-toolbox/com/swallow/controls/title-window.component", "SwtToolBoxModule": "./src/app/modules/swt-toolbox/swt-tool-box.module", "Timer": "./src/app/modules/swt-toolbox/com/swallow/utils/timer.service", "SwtHttpInterceptor": "./src/app/modules/swt-toolbox/com/swallow/communication/swt-http-interceptor", "Encryptor": "./src/app/modules/swt-toolbox/com/swallow/utils/encryptor.service", "VRule": "./src/app/modules/swt-toolbox/com/swallow/controls/vrule.component", "HRule": "./src/app/modules/swt-toolbox/com/swallow/controls/hrule.component", "SwtPasswordMeter": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-password-meter.component", "SwtCommonModule": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-module.component", "ExternalInterface": "./src/app/modules/swt-toolbox/com/swallow/utils/external-interface.service", "ScreenVersion": "./src/app/modules/swt-toolbox/com/swallow/utils/screen-version.service", "SwtImage": "./src/app/modules/swt-toolbox/com/swallow/controls/image.component", "CommonUtil": "./src/app/modules/swt-toolbox/com/swallow/utils/common-util.service", "CommonLogic": "./src/app/modules/swt-toolbox/com/swallow/logic/common-logic", "XML": "./src/app/modules/swt-toolbox/com/swallow/xmlhandler/swt-xml.service", "XMLListCollection": "./src/app/modules/swt-toolbox/com/swallow/xmlhandler/swt-xml.service", "SwtDOMManager": "./src/app/modules/swt-toolbox/com/swallow/managers/swt-dommanager.directive", "FileReference": "./src/app/modules/swt-toolbox/com/swallow/utils/file-reference.service", "ExportInProgress": "./src/app/modules/swt-toolbox/com/swallow/controls/ExportInProgress", "SwtPagesToExport": "./src/app/modules/swt-toolbox/com/swallow/controls/PagesToExport", "CancelExportEvent": "./src/app/modules/swt-toolbox/com/swallow/events/cancel-export-event.service", "ContextMenu": "./src/app/modules/swt-toolbox/com/swallow/controls/context-menu.component", "ContextMenuItem": "./src/app/modules/swt-toolbox/com/swallow/controls/context-menu.component", "PopupWindowCloseEvent": "./src/app/modules/swt-toolbox/com/swallow/events/popup-window-close-event.service", "SwtProgressBar": "./src/app/modules/swt-toolbox/com/swallow/controls/progress-bar.component", "FileUpload": "./src/app/modules/swt-toolbox/com/swallow/controls/file-upload.component", "StringUtils": "./src/app/modules/swt-toolbox/com/swallow/utils/string-utils.service", "CustomTree": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-custom-tree.component", "ILMTreeIndeterminate": "./src/app/modules/swt-toolbox/com/swallow/controls/ILMTreeIndeterminate", "HashMap": "./src/app/modules/swt-toolbox/com/swallow/utils/HashMap.service", "Container": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-container.component", "Grid": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-grid.component", "GridRow": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-grid.component", "GridItem": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-grid.component", "SwtTabNavigator": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-tab-navigator.component", "Tab": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-tab-navigator.component", "TabPushStategy": "./src/app/modules/swt-toolbox/com/swallow/containers/swt-tab-navigator.component", "Spacer": "./src/app/modules/swt-toolbox/com/swallow/controls/spacer.component", "SwtRichTextEditor": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-rich-text-editor.component", "SwtList": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-list.component", "parentApplication": "./src/app/modules/swt-toolbox/com/swallow/utils/parent-application.service", "Navigator": "./src/app/modules/swt-toolbox/com/swallow/utils/parent-application.service", "LoaderInfo": "./src/app/modules/swt-toolbox/com/swallow/utils/parent-application.service", "LinkButton": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-link-button.component", "SwtText": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-text.component", "SwtModule": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-module.component", "Keyboard": "./src/app/modules/swt-toolbox/com/swallow/utils/keyboard.service", "focusManager": "./src/app/modules/swt-toolbox/com/swallow/managers/focus-manager.service", "SwtTextArea": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-text-area.component", "SwtTabNavigatorHandler": "./src/app/modules/swt-toolbox/com/swallow/utils/swt-tabnavigator.service", "SwtLoadingImage": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-loading-image.component", "SwtCheckBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-checkbox.component", "SwtLabel": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-label.component", "HBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-hbox.component", "VBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-vbox.component", "HDividedBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-hdividedbox.component", "VDividedBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-vdividedbox.component", "SwtButton": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-button.component", "SwtCanvas": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-canvas.component", "SwtComboBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-combobox.component", "SwtDataExport": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-data-export.component", "SwtDateField": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-datefield.component", "SwtHelpButton": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-helpButton.component", "SwtNumericInput": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-numeric-input.component", "SwtAdvSlider": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-advanced-slider.component", "SwtEditableComboBox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-editable-combobox", "ILMLineChart": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/ILMLineChart", "ILMSeriesLiveValue": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/ILMSeriesLiveValue", "ProcessStatusBox": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/ProcessStatusBox", "CheckBoxLegendItem": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/CheckBoxLegendItem", "AssetsLegendItem": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/AssetsLegendItem", "ConfigurableToolTip": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/ConfigurableToolTip", "SeriesStyle": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/style/SeriesStyle", "SeriesStyleProvider": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/style/SeriesStyleProvider", "AssetsLegend": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/AssetsLegend", "CheckBoxLegend": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/CheckBoxLegend", "SwtFieldSet": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-fieldset.component", "SwtPanel": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-panel.component", "SwtScreen": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-screen.component", "SwtStepper": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-stepper.component", "SwtRadioItem": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-radioItem.component", "SwtTextInput": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-text-input.component", "SwtTimeInput": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-time-input.component", "HTTPComms": "./src/app/modules/swt-toolbox/com/swallow/communication/httpcomms.service", "RemoteTransaction": "./src/app/modules/swt-toolbox/com/swallow/communication/RemoteTransaction", "Alert": "./src/app/modules/swt-toolbox/com/swallow/utils/alert.component", "SwtAlert": "./src/app/modules/swt-toolbox/com/swallow/utils/swt-alert.service", "CommonService": "./src/app/modules/swt-toolbox/com/swallow/utils/common.service", "SwtLocalStorage": "./src/app/modules/swt-toolbox/com/swallow/utils/swt-localStorage.service", "SwtHelpWindow": "./src/app/modules/swt-toolbox/com/swallow/utils/swt-help-window.service", "SwtUtil": "./src/app/modules/swt-toolbox/com/swallow/utils/swt-util.service", "Logger": "./src/app/modules/swt-toolbox/com/swallow/logging/logger.service", "LoggerLevel": "./src/app/modules/swt-toolbox/com/swallow/logging/logger.service", "JSONReader": "./src/app/modules/swt-toolbox/com/swallow/jsonhandler/jsonreader.service", "SwtRadioButtonGroup": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-radioButtonGroup.component", "SwtCommonGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-grid.component", "SwtCommonGridPagination": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-grid-pagination.component", "SwtTotalCommonGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-total-grid.component", "SwtGroupedTotalCommonGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-grouped-total-grid.component", "SwtGroupedCommonGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-grouped-grid.component", "SwtTreeCommonGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-common-tree-grid.component", "SwtPopUpManager": "./src/app/modules/swt-toolbox/com/swallow/managers/swt-pop-up-manager.service", "EmailValidator": "./src/app/modules/swt-toolbox/com/swallow/utils/email-validator.service", "SwtPrettyPrintTextArea": "./src/app/modules/swt-toolbox/com/swallow/syntaxhighlight/PrettyPrintTextArea.component", "ModuleLoader": "./src/app/modules/swt-toolbox/com/swallow/utils/module-loader.service", "SwtSlider": "./src/app/modules/swt-toolbox/com/swallow/model/SwtSLider/swt-slider.component", "AdvancedDataGrid": "./src/app/modules/swt-toolbox/com/swallow/controls/advanced-data-grid.component", "AdvancedDataGridCell": "./src/app/modules/swt-toolbox/com/swallow/controls/advanced-data-grid.component", "AdvancedDataGridRow": "./src/app/modules/swt-toolbox/com/swallow/controls/advanced-data-grid.component", "LinkItemRander": "./src/app/modules/swt-toolbox/com/swallow/renderers/advancedDataGridRendres/link-item-render.component", "DateUtils": "./src/app/modules/swt-toolbox/com/swallow/utils/date-utils.service", "SwtSummary": "./src/app/modules/swt-toolbox/com/swallow/summary/swt-summary.component", "EnhancedAlertingTooltip": "./src/app/modules/swt-toolbox/com/swallow/summary/EnhancedAlertingTooltip", "Series": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/Series", "DataExportMultiPage": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-data-export.multipage.component", "JSONViewer": "./src/app/modules/swt-toolbox/com/swallow/screensUtils/jsonviewer/jsonviewer.component", "TabSelectEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "DividerResizeComplete": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "TabCloseEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowDragEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowDragStartEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowDragEndEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowCloseEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowCreateEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowResizeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowMinimizeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowMaximizeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "HDividedEndResizeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "VDividedEndResizeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "TabChange": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "TabClose": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "SwtCommonGridItemRenderChanges": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "ExportEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "AdvancedExportEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "HorizontalScrollPositionEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "VerticalScrollPositionEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "SeriesHighlightEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "LegendItemChangedEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "SwtCheckboxEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "WindowEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "CustomTreeEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "genericEvent": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "SwtEventsModule": "./src/app/modules/swt-toolbox/com/swallow/events/swt-events.module", "SwtMultiselectCombobox": "./src/app/modules/swt-toolbox/com/swallow/controls/swt-multiSelect-combobox.component", "ɵb": "./src/app/modules/swt-toolbox/com/swallow/managers/window-manager.service", "ɵc": "./src/app/modules/swt-toolbox/com/swallow/controls/UIComponent.service", "ɵd": "./src/app/modules/swt-toolbox/com/swallow/events/event-dispatcher.service", "ɵe": "./src/app/modules/swt-toolbox/com/swallow/model/base-object", "ɵf": "./src/app/modules/swt-toolbox/com/swallow/controls/drop-down-list.component", "ɵg": "./src/app/modules/swt-toolbox/com/swallow/renderers/advancedDataGridRendres/string-item-render.component", "ɵh": "./src/app/modules/swt-toolbox/com/swallow/renderers/advancedDataGridRendres/number-item-render.component", "ɵi": "./src/app/modules/swt-toolbox/com/swallow/controls/advanced-tool-tip.component", "ɵj": "./src/app/modules/swt-toolbox/com/swallow/charts/ILMCharts/ILMLineChart/control/Chart/SwtILMChart"}, "importAs": "swt-tool-box"}