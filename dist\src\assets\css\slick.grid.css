.slickgrid-container .slick-row .slick-cell.active {
    border-left: 0px !important;
    padding: 0px 0px 0px 0px;
}

.slick-gridmenu {
    z-index: 9999;
}

.slickgrid-container .slick-row .slick-cell {
    padding: 0px 0px 0px 0px;
}

.slick-row:last-child>.slick-cell {
    border-bottom: 1px solid #DFDFDF !important;
}

/* Slick.Editors.YesNoSelect */
select.editor-yesno {
    width: 100%;
    margin: 0;
    vertical-align: middle;
}

/* Slick.Editors.Checkbox */
input.editor-checkbox,
input.editor-radio {
    margin: 0;
    height: 100%;
    padding: 0;
    border: 0;
    text-align: center;
}


div.slick-cell < input[type=checkbox]:disabled,
div.slick-cell < input[type=radio]:disabled {
    pointer-events: none;
}


.editor-radio {
    display: inline;
    margin-left: calc(50% - 5px) !important;
    cursor: pointer;
}

.slickgrid-container input[type=checkbox] {
    vertical-align: text-top !important;
    height: 100% !important;
    margin-left: calc(50% - 5px);
    margin-top: -2px;
    cursor: pointer;
}

.slickgrid-container input[type=checkbox]:focus {
    outline: none;
}

.slickgrid-container {
    border-bottom: 0px !important;
    background-color: white;
}

.slickgrid-container input[type=checkbox]:disabled+label,
.slickgrid-container input[type=radio]:disabled+label {
    cursor: default;
    pointer-events: none;
}

.disabled-text-editor {
    pointer-events: none;
    border: none !important;
}

.slick-header {
    /*----For Predict --------------------
     background-color: #369!important;
     */
    background-color: #529fed !important;
    font-size: 11px !important;
    font-weight: bold !important;
}

.slick-header-column {
    padding: 0px 0px 0px 10px !important;
    color: white !important;
    border-right: 2px solid silver !important;
    z-index: 0 !important;
}

.slick-columnpicker-list input[type=checkbox]:disabled+label:before{
    color: #ddd;

}

.slick-header-column.holiday {
    color: #D8D8D8 !important;
}
.slick-header-column.alerting {
    background :  url(../images/alert.png) no-repeat 30% 20% !important;
}


.slick-header-column.skippedColumn {}

.slick-row {
    height: 0px !important;
}

.slick-gridmenu-button {
    padding: 5px 6px !important;
    background-color: #369 !important;
    margin-top: 0px !important;
    color: white !important;
}

.slick-cell {
    border-right: 1px solid silver !important;
    border-bottom: 1px solid #dfdfdf !important;
    border-top: 0px !important;
}

.slickgrid-container .grid-canvas .slick-row.odd .slick-cell {
    background: #E0F0FF !important;
    /*----For Predict --------------------
     background: rgba(250, 250, 230, 1)!important;
     */
}
.slickgrid-container .grid-canvas .slick-row.even .slick-cell {
    background: white !important;
    /*----For Predict --------------------
     background: rgba(250, 250, 230, 1)!important;
     */
}

.slick-headerrow-columns {
    border-bottom: 1px solid #dfdfdf !important;
    display: none ;
}

.slickgrid-container .slick-headerrow {
/*     display: none !important; */
}

.slickgrid-container .grid-canvas .slick-row .slick-cell.selected {
    background-color: rgba(255, 204, 102, 1) !important;
}

div.selected *:not(.CustomStrFormatterDiv) *:not(.renderAsInput):not(.strLinkRender):not(.select2-selection__rendered):not(.TextAreaItem):not(.LinkEditor):not(.form-control):not(.selectEditable):not(.slick-group-toggle){
    background-color: rgba(255, 204, 102, 1) !important;
}

.slickgrid-container .slick-header-columns .slick-header-column:hover {
    background-color: #4BB1FF !important;
}

.slick-headerrow-column {
    border-right: 1px solid silver !important;
}

.slickgrid-container {
    box-sizing: border-box;
    overflow-y: scroll;
}

.renderAsInput {
    width: 100%;

}

.text-style {
    height: 20px !important;
    font-size: 12px !important;
    font-family: Verdana, Helvatica !important;
}

.grid-canvas {
    min-height: 100%;
    z-index: 0;
}

.slickgrid-container .slick-header-columns .slick-header-column .slick-sort-indicator {
    color: white !important;
    top: -6px !important;
    right: 5px !important;
    left: 0px !important;
}

.slickgrid-container .slick-header-columns {
    background-color: transparent;
}

.slick-header {
    height: 0px;
}

.select-arrow-comboGrid {
    position: absolute;
    right: 0%;
    border-left: 1px solid #337ab7;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: rgba(207, 218, 229, 1);
    color: rgba(86, 105, 139, 0.71);
    font-size: 9px;
    height: 100% !important;
    width: 22.6px;
    padding-left: 5px;
}

.slickgrid-container .grid-canvas .slick-cell {
    font-size: 12px !important;
}

.dataGridSmall .slickgrid-container .grid-canvas .slick-cell * {
    font-size: 11px !important;
}

.ui-widget-content {
    color: #173553 !important;
    font-family: Verdana, helvetica !important;
    font-weight: normal !important;
}

/*======================================COMBOBOX EDITOR & FORMATER style (grid) ================================================ */
.ms-filter.search-filter.form-control {
    /* 	width:100%!important; */
    /*     margin: 0px!important; */
    /*     font-family: Verdana, Helvatica !important; */
    /*     font-size: 11px; */
    /*     color: #173553!important; */
    /*     height: 98%!important; */
    /*     background:  url(../images/combo_arrow_selected2.png) no-repeat right #ffffff!important;  */

    /*     height: 100%!important; */
    /*     padding-left: 0px!important; */
    /*     padding-top: 1px!important; */

    /*     width: 80%; */
    /*     text-overflow: ellipsis!important; */
    /*     overflow: hidden!important; */
    /*     white-space: nowrap!important; */
    /*     display: inline-block!important; */

    width: 100% !important;
    margin: 0;
    font-family: Verdana, Helvatica !important;
    font-size: 11px;
    color: #173553 !important;
    height: 100% !important;
    padding-top: 1px !important;
}

.select2-container--open .select2-dropdown {
    z-index: 9999 !important;
}

.select2-selection__arrow>b {
    display: none !important;
}

.select2-selection__arrow {
    top: 0px !important;
    right: 0px !important;
    width: 23px !important;
    background-color: rgb(255, 204, 102);
    height: 100% !important;
    background: url(../images/combo_arrow_selected.png) no-repeat right #ffffff !important;
}

.select2-results__group {
    display: none !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 20px !important;
    padding-left: 2px !important;
    height: 100%;
    background-color: white;
}

.select2-selection__rendered {
    word-wrap: break-word !important;
    text-overflow: inherit !important;
    white-space: normal !important;
}

.select2-container {
    outline: 2px solid #2295b978;
}

.select2-container--default .select2-selection--single {
    position: absolute;
    width: 100% !important;
    height: 100% !important;
    border: 0px !important;
}

.select2-results__option {
    padding: 4px;
    ine-height: initial;
    /*      max-width: 100%; */
    overflow: hidden;
    word-wrap: normal !important;
    white-space: normal;
    padding-right: 0px;
}

.select2-results>ul>li:nth-child(odd) {
    background: #F9F9E3;
}

.select2-results__option[aria-selected=true],
.select2-results__option.select2-results__option--highlighted[aria-selected=true] {
    background-color: #ffcc66 !important;
    color: #173553 !important;
}

.select2-results__option.select2-results__option--highlighted[aria-selected=true]:hover {
    background-color: #0000A0 !important;
    color: white !important;
}

.select2-results__option.select2-results__option--highlighted {
    background-color: #0000A0 !important;
    color: white !important;
}

.combo-container {
    border: 1px solid #61a4de;
    position: relative;
    height: 20px !important;
    font-size: 11px;
    background-color: white;
    border-radius: 3px;
}

.combo-value {
    padding-left: 5px;
}

.select2-search__field {
    height: 20px !important;
}


.select2 {
    width: 100% !important;
    border: 1px solid #7F9DB9 !important;
    font-size: 11px !important;
    border-radius: 3px;
    font-family: Verdana, Helvatica !important;
    position: relative;
    height: 97% !important;
    font-family: Verdana, Helvatica !important;
    cursor: default;
    margin: 0;
    max-height: 24px;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    overflow: hidden;
    padding-left: 2px;
    padding-top: 0px;

}

.selectDiv {
    background: url(../images/combo_arrow.png) no-repeat right #c55d5d;
    box-shadow: inset 0px 0px -1px 1px #f5f5f5;
    z-index: 100;
    background-color: white;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 97% !important;
    line-height: unset !important;
}



.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
    padding-right: 25px;
}

.render-combo-editor:hover {
    background-color: red !important;
}

.gridWithHeader {
    height: calc(100% - 11px) !important;
}

.gridWithoutHeader {
    height: 100% !important;
}

.gridWithoutHeaderLocked {
    height: calc(100% + 12px) !important;
}

.slick-viewport {
    /* 	background-color: #ffffff; */
    /*     overflow-x: hidden!important; */
    /*     overflow-y: auto!important; */
}

input::-webkit-calendar-picker-indicator {
    display: none;
}


/*************************************************INPUT TEXT EDITOR ENDER**************************************************** */
.input-text-editor {
    border: 1px solid #8cc6e2;
    width: 100%;
    height: 96% !important;
    position: relative;
    top: 0px;
    left: 1px;
    text-align: right;
    padding-right: 5px !important;
}

.input-text-editor-disabled {
    width: 100%;
    height: 96% !important;
    position: relative;
    top: 0px;
    pointer-events: none;
    text-align: right;
    padding-right: 5px !important;
    border: none !important;
    /*
     color: #A2A2A2!important;
     */
    padding-left: 5px;
}

/*************************************************STEPPER RENDER (formatter & editor)**************************************************** */

.render-stepper-arrow-up {
    width: 0;
    position: absolute;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid black;
    top: 25%;
    left: 4px;
}

.render-stepper-arrow-down {
    width: 0px;
    height: 0px;
    border-left: 3px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4.5px solid black;
    position: absolute;
    top: 25%;
    left: 4px;
}

.stepper-up-container {
    background-color: #ECECE2;
    position: absolute;
    height: 50%;
    width: 18px;
    border: 1px solid gray;
    border-top-right-radius: 5px;
    border-bottom-style: none;
}

.stepper-down-container {
    position: absolute;
    background-color: #ECECE2;
    height: 50%;
    width: 18px;
    top: 48%;
    border: 1px solid gray;
    border-bottom-right-radius: 6px;
}

.stepper-down-container:hover,
.stepper-up-container:hover {
    border-color: #0086E8 !important;
    background-color: #ECECE2 !important;
}

.main-input {
    border-right-style: none;
}

.highlightedRow {
    background-color: #FFCC66 !important;
}

.slick-column-name {
    font-size: 12px;
    padding-right: 17px;
    margin-bottom: 0px !important;
    width: 100%;
    text-align: center;
}

.selected>.strFormatterDiv {
    background-color: rgba(255, 204, 102, 1) !important;
}

.strFormatterDiv {
    white-space: pre;
    padding-left: 5px;
    height: 100%;
    margin: 0;
    /*color: black !important;*/
}

.strLinkRender {
    color: #333 !important;
}

.strLinkRender:hover {
    color: #08c !important;
    cursor: pointer;
}

.strFormatterDivDisabled {
    /*color: 	#696969 !important;*/
}

/*************************************************ELEMENT with blink=true (formatter & editor)**************************************************** */
.blink_me {
    -webkit-animation-name: blinker;
    -webkit-animation-duration: 1s;
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;

    -moz-animation-name: blinker;
    -moz-animation-duration: 1s;
    -moz-animation-timing-function: linear;
    -moz-animation-iteration-count: infinite;

    animation-name: blinker;
    animation-duration: 1s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    color: #FF0000;
}

@-moz-keyframes blinker {
    0% {
        opacity: 1.0;
    }

    50% {
        opacity: 0.0;
    }

    100% {
        opacity: 1.0;
    }
}

@-webkit-keyframes blinker {
    0% {
        opacity: 1.0;
    }

    50% {
        opacity: 0.0;
    }

    100% {
        opacity: 1.0;
    }
}

@keyframes blinker {
    0% {
        opacity: 1.0;
    }

    50% {
        opacity: 0.0;
    }

    100% {
        opacity: 1.0;
    }
}


.blink_border_alert {
    -webkit-animation-name: blinker_border;
    -webkit-animation-duration: 1s;
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;

    -moz-animation-name: blinker_border;
    -moz-animation-duration: 1s;
    -moz-animation-timing-function: linear;
    -moz-animation-iteration-count: infinite;

    animation-name: blinker_border;
    animation-duration: 1s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    border: 1px #ff0000 solid;
}

@-moz-keyframes blinker_border {
    50% { border-color:#fff ; }
}

@-webkit-keyframes blinker_border {
    50% { border-color:#fff ; }
}

@keyframes blinker_border {
    50% { border-color:#fff ; }
}

.negative {
    color: red;
}

/**************************************************SLICK-GRID-FILTER [START]**************************************************************/
.ms-filter.search-filter {
    width: 17px !important;
    right: 0px;
    margin-top: 0px;
    margin-left: 2px;
    margin-right: 0px;
    margin-bottom: 0px;
    position: absolute !important;
    /*-------- For Predict -----------------
     width:15px!important;
     right:0px;
     margin: 2px 1px;
     position: absolute!important;
     */
}

.ms-choice {
    padding: 0px !important;
    border: 0px !important;
    background-color: #B8DAFB !important;
    border-radius: 0px !important;
    height: 21px !important;
    /*-------- For Predict -----------------
     height: 17px!important;
     */
}

.ms-choice>div {
    width: 15px !important;
    top: 0px !important;
    margin-top: 3px;
    /*-------- For Predict -----------------
     */
    height: 12px !important;
    right: -1px !important;
    background: url(../images/filter-arrow-down.png) left top no-repeat !important;
}

.ms-choice>div.open {
    background: url(../images/filter-arrow-down.png) left top no-repeat !important;
}

.ms-drop input[type="checkbox"]+span:before,
.ms-drop input[type="radio"]+span:before {
    opacity: 0 !important;
    width: 0px !important;
}

.ms-drop>ul>li:nth-child(odd) {
    background: #F9F9E3;
}

.ms-drop ul {
    padding: 0px 0px !important;
}

.ms-drop {
    border-radius: 0px !important;
    min-width: 100px !important;
}

.ms-drop>ul>li>label {
    margin-bottom: 1px !important;
}

.ms-drop label:hover {
    background-color: #529FED !important;
}

.ms-drop label span {
    top: -2px !important;
}

.filled>.ms-choice {
    background-color: rgba(255, 204, 102, 1) !important;
}

/**************************************************SLICK-GRID-FILTER [END]**************************************************************/

.slick-row .slick-cell.frozen:last-child,
.slick-headerrow-column.frozen:last-child,
.slick-footerrow-column.frozen:last-child {
    margin-right: 1px;
    border-right: 0px !important;
}

.grid-canvas-left>div.slick-cell.frozen {
    border-right: 1px solid black !important;
}

.slick-pane-bottom {
    border-top: 1px solid black !important;
}

.lockedColumnPane {
    overflow-y: hidden !important;
    overflow-x: hidden !important;
    z-index: 1;
}

.slickgrid-container .grid-canvas .slick-row .slick-cell.selected>.selectDiv>option {
    top: 0px !important;
    right: 0px !important;
    height: 100% !important;
    background: url(../images/combo_arrow_selected.png) no-repeat right #ffffff !important;
}


.slickGridCanvasContainer {
    border: 1px solid #FFF;
    background-color: #ccecff;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 0px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.6), 0 0px 0px 0 rgba(0, 0, 0, 0.5);
}

.slickgrid-container .grid-canvas .slick-cell .slick-group-toggle.collapsed:before {
    content: "\f067" !important;
    font-size: 8px !important;
    color: #555 !important;
    font-weight: normal !important;
    border: 1px solid #7898B5;
    padding: 0px 0.5px 0px 0.5px;
    display: inline;

}

.slickgrid-container .grid-canvas .slick-cell .slick-group-toggle.expanded:before {
    content: "\f068" !important;
    font-size: 8px !important;
    color: #555 !important;
    font-weight: normal !important;
    border: 1px solid #7898B5;
    padding: 0px 0.5px 0px 0.5px;
    display: inline;
}

.slickgrid-container .grid-canvas .slick-cell .slick-group-toggle.center {
    text-align: center !important;
    width: 100% !important;
}

.slickgrid-container .grid-canvas .slick-cell .slick-group-toggle.right {
    text-align: right !important;
    width: 75% !important;
}

.slick-viewport-left,
.slick-viewport.slick-viewport-top.slick-viewport-right,
.slick-viewport.slick-viewport-bottom.slick-viewport-right {
    /*     height: calc(100% - 23px)!important; */
    height: 100% !important;
}

.slick-viewport.slick-viewport-top.slick-viewport-right {
    height: 100% !important;
}

select::-ms-expand {
    display: none;
}

/*------------------------------------------------------CheckBoxHeader in Column---------------------------------------------------------*/
.slick-column-name input[type=checkbox] {
    display: block !important;
    height: 15px !important;
    width: 15px !important;
    margin-left: calc(50% - 7px);
}

.checkBoxHeader {
    display: block;
    position: absolute;
    left: 2px;
    top: 5px;
    width: 100%;
}

/* div.active *:not(.renderAsInput) { */
/* 	background-color: rgba(255, 204, 102, 1)!important; */
/* } */

.paneWithNoHeader {
    height: 100% !important;
    top: 0px !important;
}

.LinkIsDisabled {
    color: gray;
    cursor: not-allowed;
    opacity: 0.5;
}

.imageIsDisabled {
    pointer-events: none;
    opacity: 0.5;
}

.slick-columnpicker .title {
    font-size: 12px !important;
    margin-bottom: 0px !important;
    width: 100% !important;
    padding-bottom: 5px;
}

.slick-columnpicker>.close {
    padding: 0px !important;
}

.slick-columnpicker {
    min-width: 180px !important;
}

.close {
    position: relative;
    top: -3px;
}

.commonGrid-scroll-tooltip {
    position: fixed;
    pointer-events: none;
    display: block;
    width: auto;
    height: auto;
    background: #FAFAE7;
    font-size: 11px;
    color: #0B333C;
    text-align: center;
    border-radius: 3px;
    padding: 3px 5px;
    box-shadow: 3px 3px 3px gray;
    z-index: 1;
}

.slick-viewport.slick-viewport-top.slick-viewport-left {
    border-right: 1px solid #5c5e5f69;
}

.slick-preheader-panel.ui-state-default.slick-header {
    border-bottom: 1px solid silver !important;
}
.slickgrid-container .slick-preheader-panel.ui-state-default .slick-header-columns .slick-header-column {
    display:block !important;
}
.zeroHeight {
    height: 0px !important;
}

.slick-preheader-panel {
    border-left: 0px !important;
    border-right: 0px !important;
    border-top: 0px !important;
    border-bottom: 0px !important;
    /* height: 25px !important; */


}

.slick-header-right {
    border-left: 0px !important;
    border-right: 0px !important;
    border-top: 0px !important;
    border-bottom: 0px !important;
}

.fullWidth {
    width: 100% !important;
}

.fullWidthWithScrollBar {
    width: calc(100% + 14px) !important;
}

.headerTop {
    top: 25px;
}

.headerBackgroundColor {
    background-color: #4040C0 !important;
}

.groupeCollapsedButton {
    float: right;
    color: transparent;
    cursor: pointer;
}

.nonCollapsedButton {
    background: url(../images/minus_collapsed_img.png) no-repeat;
    margin-top: -20px;
}

.collapsedButton {
    background: url(../images/plus_collapsed_img.png) no-repeat;
    margin-top: -19px;
}

.containerCheckBox {
    /* text-align: center!important; */
    vertical-align: middle !important;
    height: 100%;
    width: 100%;
}

/*.slick-viewport.slick-viewport-top.slick-viewport-right{
     overflow-x: hidden!important;
 } */
.overflowXVisible {
    overflow-x: scroll !important;
}

.overflowXAuto {
    overflow-x: auto !important;
}

.slick-header.ui-state-default.slick-header-left {
    border: 0px !important;
}


.slick-header-columns,
.slick-header-column,
.slick-header-right,
.slick-header,
.slick-header-columns-right {
    box-sizing: content-box !important;
}

.slick-group-header-column.ui-state-default {
    box-sizing: content-box !important;
}

.test {
    height: 100%;
    width: 100%;
}

.positionFixed {
    position: fixed !important;
}

.grid-canvas .slick-row:hover,
.grid-canvas .slick-row.odd:hover {
    background-color: transparent !important;
    border: 0px !important;
}

.slickgrid-container .grid-canvas .slick-row.even:hover {
    background-color: #fff !important;
    border: 0px !important;
}

/*  .slickgrid-container .slick-header-columns .slick-header-column .slick-resizable-handle{ */
/*  	width: 0px!important; */
/*    	right: -2px!important; */
/*  } */
/*.slick-header-columns-left>.slick-header-column:last-child {
     border-right:1px solid white!important;
 }*/

.slick-header-columns-right>.slick-header-column:first-child>.slick-column-name {
    border-left: 0px !important;
}

.ms-no-results {
    display: none !important;
}

.disable-reorder {
    -webkit-user-drag: none !important;
}

.search-filter.form-control>.renderAsInput:nth-child(odd) {
    background: #F9F9E3;
}

.overflowXHidden {
    overflow-x: hidden !important;
}

.overflowYHidden {
    overflow-y: hidden !important;
}

.overflowYVisible {
    overflow-y: scroll !important;
}

.disabled-combo {
    background-color: #EEEEEE;
    pointer-events: none;
}

input[type='text'] {
    margin: 0px !important;
}


/**********************ComboRender*************************************/


.input-group {
    width: 100%;
}

.form-control {
    padding: 0px 0px 0px 5px;
    background-color: #FFF;
    z-index: 0;
    color: black;
}

.comboBoxRender-container {
    z-index: 0;
    width: 100% !important;
}

.disable {
    color: #919999;
}

.comboBoxRender-dropDown-ul {
    position: absolute;
    z-index: 900;
    padding: 0px;
    width: auto;
    margin: 0px;
    max-height: 100px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 3px;
    cursor: default;
    word-wrap: break-words;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6), 0 0px 0px 0 rgba(0, 0, 0, 0.5);
}

.comboBoxrender-dropDown-li {
    list-style-type: none;
    width: 100%;
    padding-left: 2px;
    height: 18px;
    line-height: 21px;
    color: #173553;
    font-size: 11px !important;
    text-overflow: ellipsis !important;
    font-family: Verdana, Helvatica !important;
}

.comboBoxrender-dropDown-li:hover {
    background-color: #0000A0 !important;
    color: #FFF;
}

li {
    color: black;
}

.combo-option:hover {
    /* background-color: transparent; */
    background-color: #0000A0 !important;

    color: #FFF;
}

.comboBoxrender-dropDown-li.isSelected {
    background-color: rgba(255, 204, 102, 1) !important;
}

.comboBoxrender-dropDown-li.isSelected:hover {
    background-color: #0000A0 !important;
}

.comboBoxRender-selectedItem {
    background-color: rgba(255, 204, 102, 1) !important;
    color: #FFF;
}

.comboBoxrender-dropDown-li>a {
    font-family: verdana, helvetica;
    font-size: 11px;
}

.input-group {
    padding-top: 1px;
}

.input-group-addon {
    width: 19px;
    height: 19px;
    border-top: 1px solid #7F9DB9;
    border-right: 1px solid #637A90;
    border-bottom: 1px solid #415160;
    border-left: 1px solid #637A90;
    padding: 0px;
    margin: 0px;
    font-size: 8px;
    background-image: -webkit-linear-gradient(top, #DBF1FF, #A7C6DE);
    background-image: -moz-linear-gradient(top, #DBF1FF, #A7C6DE);
    background-image: -ms-linear-gradient(top, #DBF1FF, #A7C6DE);
    background-image: -o-linear-gradient(top, #DBF1FF, #A7C6DE);
    background-image: linear-gradient(to bottom, #DBF1FF, #A7C6DE);
}

.input-group-addon-disable {
    width: 22px;
    height: 22px;
    border-top: 1px solid #A5C4DC;
    border-right: 1px solid #96B1C6;
    border-bottom: 1px solid #869EB0;
    border-left: 1px solid #96B1C6;
    padding: 0px;
    margin: 0px;
    font-size: 8px;
    background-image: -webkit-linear-gradient(top, #CAEBFE, #B1D0E7);
    background-image: -moz-linear-gradient(top, #CAEBFE, #B1D0E7);
    background-image: -ms-linear-gradient(top, #CAEBFE, #B1D0E7);
    background-image: -o-linear-gradient(top, #CAEBFE, #B1D0E7);
    background-image: linear-gradient(to bottom, #CAEBFE, #B1D0E7);
}

.glyphicon {
    font-size: 9px !important;
    position: relative;
    top: 2px;
    cursor: default;
}

.comboBoxRender-filter-input {
    height: 20px !important;
    border-radius: 0px;
    border-top: 1px solid #4C5E6F;
    border-right: 1px solid #9FB5CA;
    border-bottom: 1px solid #B2C4D5;
    border-left: 1px solid #9FB5CA;
    font-family: verdana, helvetica;
    color: #173553;
    font-size: 11px !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    display: inline-block !important;
    padding-left: 4px !important;
    padding-right: 0px !important;
    font-family: Verdana, Helvatica !important;
}

.comboBoxRender-dropDown {
    position: fixed;
    z-index: 999;
    background-color: #FFF;
    display: none;
    border: 1px solid #D9D9D9;
    box-shadow: 0px 4px 5px #999999;
    width: 260px;
    /* this width must be updated with jquery*/
}

.comboBoxrender-dropDown-li.odd {
    background-color: #fcf8e3;
}

.oddRows {
    background-color: #E0F0FF;
}
.evenRows {
    background-color:white;
}

.comboBoxRender-container>.input-group>.input-group-addon {
    color: #555555db !important;
}

.disabled {
    pointer-events: none;
}


.slickgrid-container .slick-group-toggle {
    display: inline;
}
.ms-select-all { display:none; }