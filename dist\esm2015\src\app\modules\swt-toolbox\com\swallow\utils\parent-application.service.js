/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { HashMap } from "./HashMap.service";
import { CommonService } from "./common.service";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class LoaderInfo {
    constructor() {
        this.url = null;
    }
}
if (false) {
    /** @type {?} */
    LoaderInfo.prototype.url;
}
export class Navigator {
    constructor() {
        this.selectedIndex = -1;
        this.selectedTab = null;
        this.tabs = new Array();
    }
    /**
     * This method used to remove tab in the given index.
     * @param {?} index
     * @return {?}
     */
    removeChildAt(index) {
        if (index === 0) {
            if (this.tabs.length > 1) {
                this.setToActive(this.tabs[index + 1]);
                parentApplication.navigator.selectedIndex = index + 1;
                parentApplication.navigator.selectedTab = this.tabs[index + 1];
                CommonService.instance.Router.navigateByUrl(this.tabs[index].menuaction);
                this.tabs.splice(index, 1);
                parentApplication.document.opnedTabs.splice(index, 1);
                event.preventDefault();
            }
            else {
                CommonService.instance.Router.navigateByUrl("/");
                parentApplication.navigator.selectedIndex = -1;
                parentApplication.navigator.selectedTab = null;
                this.tabs.splice(0, 1);
                parentApplication.document.opnedTabs.splice(0, 1);
                event.preventDefault();
            }
        }
        else {
            this.setToActive(this.tabs[index - 1]);
            parentApplication.navigator.selectedIndex = index - 1;
            parentApplication.navigator.selectedTab = this.tabs[index - 1];
            this.tabs.splice(index, 1);
            parentApplication.document.opnedTabs.splice(index, 1);
            event.preventDefault();
            CommonService.instance.Router.navigateByUrl(this.tabs[index - 1].menuaction);
        }
    }
    /**
     * This method used to add tab in the given index.
     * @param {?} index
     * @param {?} child
     * @return {?}
     */
    addChildAt(index, child) {
        this.tabs.splice(index, 0, child);
        parentApplication.document.opnedTabs.splice(index, 0, child);
    }
    /**
     * This method used to add tab to the tabNavigator.
     * @param {?} child
     * @return {?}
     */
    addChild(child) {
        this.tabs.push(child);
        parentApplication.document.opnedTabs.push(child);
    }
    /**
     * This method used to remove a child from tabNvigator.
     * @param {?} child
     * @return {?}
     */
    removeChild(child) {
        /** @type {?} */
        const index = parentApplication.document.opnedTabs.indexOf(child);
        this.removeChildAt(index);
    }
    /**
     * This method used to remove all children from tabNavigator.
     * @return {?}
     */
    removeAllChild() {
        for (var index = 0; index < this.tabs.length; index++) {
            this.removeChildAt(index);
        }
        this.tabs = new Array();
        parentApplication.document.opnedTabs = new Array();
    }
    /**
     * @return {?}
     */
    getTabs() {
        return this.tabs;
    }
    /**
     * @private
     * @param {?} tab
     * @return {?}
     */
    setToActive(tab) {
        SwtPopUpManager.modalId = tab.tabIndex;
        for (var index = 0; index < this.tabs.length; index++) {
            this.tabs[index].active = false;
            if (index !== tab.tabIndex) {
                $(".modal_" + index).css("display", "none");
            }
            else {
                $(".modal_" + index).css("display", "table");
            }
        }
        this.tabs[this.tabs.indexOf(tab)].active = true;
    }
}
if (false) {
    /** @type {?} */
    Navigator.prototype.selectedIndex;
    /** @type {?} */
    Navigator.prototype.selectedTab;
    /** @type {?} */
    Navigator.prototype.tabs;
}
//@dynamic
export class parentApplication {
    /**
     * This method is used to get params from url.
     * @param {?} url
     * @return {?}
     */
    static setParams(url) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.loaderInfo.url = url;
            /** @type {?} */
            const qm = url.indexOf("?");
            if (qm !== -1) {
                errorLocation = 10;
                /** @type {?} */
                const query = url.substr(qm + 1);
                /** @type {?} */
                const params = query.split("&");
                for (var i = 0; i < params.length; i++) {
                    errorLocation = 20;
                    /** @type {?} */
                    const param = params[i];
                    /** @type {?} */
                    const nameValue = param.split("=");
                    if (nameValue.length === 2) {
                        errorLocation = 30;
                        /** @type {?} */
                        const key = nameValue[0];
                        /** @type {?} */
                        const val = nameValue[1];
                        errorLocation = 40;
                        if (key.toLowerCase() === "access") {
                            //Get access right
                            this.menuAccess = val;
                        }
                        else if (key === "programId") {
                            //Get programId
                            this.programId = val;
                        }
                        else if (key === "moduleId") {
                            this.moduleId = val;
                        }
                        else if (key === "undock") {
                            this.undock = val;
                        }
                        else if (key === "height") {
                            this.height = val;
                        }
                        else if (key === "screen") {
                            this.screenName = val;
                        }
                        else if (key === "menuordergroup") {
                            this.menuOrderGroup = val;
                        }
                        else if (key === "width") {
                            this.width = val;
                        }
                        else if (key === "menuaction") {
                            this.menuAction = val;
                        }
                        else if (key === "labelgeneric") {
                            this.labelGeneric = val;
                        }
                        else if (key === "menuId") {
                            this.menuId = val;
                        }
                        else {
                            //                          val == undefined || val == '' ? val = '' : null;
                            this.customParams.put(key, val);
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error("parentApplication [ getQueries ] method error :", error);
        }
    }
    /**
     * @return {?}
     */
    static getCurrentModuleId() {
        return this.moduleId;
    }
    /**
     * @return {?}
     */
    static clearParams() {
        this.customParams = new HashMap();
    }
}
parentApplication.loaderInfo = new LoaderInfo();
parentApplication.undock = null;
parentApplication.height = null;
parentApplication.screenName = null;
parentApplication.menuOrderGroup = null;
parentApplication.width = null;
parentApplication.moduleId = null;
parentApplication.programId = null;
parentApplication.menuAction = null;
parentApplication.labelGeneric = null;
parentApplication.menuAccess = null;
parentApplication.menuId = null;
parentApplication.navigator = new Navigator();
parentApplication.screenClosed = null;
parentApplication.customParams = new HashMap();
parentApplication.translateObject = null;
parentApplication.modules = [];
parentApplication.favorites = [];
parentApplication.singletons = null;
parentApplication.currentModule = null;
parentApplication.document = null;
parentApplication.selectedTab = "";
parentApplication.decorators = [
    { type: Injectable }
];
if (false) {
    /** @type {?} */
    parentApplication.loaderInfo;
    /** @type {?} */
    parentApplication.undock;
    /** @type {?} */
    parentApplication.height;
    /** @type {?} */
    parentApplication.screenName;
    /** @type {?} */
    parentApplication.menuOrderGroup;
    /** @type {?} */
    parentApplication.width;
    /** @type {?} */
    parentApplication.moduleId;
    /** @type {?} */
    parentApplication.programId;
    /** @type {?} */
    parentApplication.menuAction;
    /** @type {?} */
    parentApplication.labelGeneric;
    /** @type {?} */
    parentApplication.menuAccess;
    /** @type {?} */
    parentApplication.menuId;
    /** @type {?} */
    parentApplication.navigator;
    /** @type {?} */
    parentApplication.screenClosed;
    /** @type {?} */
    parentApplication.customParams;
    /** @type {?} */
    parentApplication.translateObject;
    /** @type {?} */
    parentApplication.modules;
    /** @type {?} */
    parentApplication.favorites;
    /** @type {?} */
    parentApplication.singletons;
    /** @type {?} */
    parentApplication.currentModule;
    /** @type {?} */
    parentApplication.document;
    /** @type {?} */
    parentApplication.selectedTab;
}
export class Tab {
    constructor() {
        this.icon = undefined;
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.width = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
        this.menuOrderGroup = undefined;
        this.height = undefined;
        this.type = undefined;
        this.menuitem = undefined;
        this.active = undefined;
        this.disabled = undefined;
        this.tabIndex = undefined;
    }
}
if (false) {
    /** @type {?} */
    Tab.prototype.icon;
    /** @type {?} */
    Tab.prototype.itemid;
    /** @type {?} */
    Tab.prototype.menuaccess;
    /** @type {?} */
    Tab.prototype.width;
    /** @type {?} */
    Tab.prototype.undock;
    /** @type {?} */
    Tab.prototype.label;
    /** @type {?} */
    Tab.prototype.menuaction;
    /** @type {?} */
    Tab.prototype.screenname;
    /** @type {?} */
    Tab.prototype.moduleid;
    /** @type {?} */
    Tab.prototype.programid;
    /** @type {?} */
    Tab.prototype.menuOrderGroup;
    /** @type {?} */
    Tab.prototype.height;
    /** @type {?} */
    Tab.prototype.type;
    /** @type {?} */
    Tab.prototype.menuitem;
    /** @type {?} */
    Tab.prototype.active;
    /** @type {?} */
    Tab.prototype.disabled;
    /** @type {?} */
    Tab.prototype.tabIndex;
}
//# sourceMappingURL=data:application/json;base64,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