/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
/** @type {?} */
const select2 = require('select2');
//@dynamic
export class ComboBoxItemRenderer {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        // private appendSource = this.commonGrid.appendSourceFunction( this.args.item, this.args.column.field );
        this.enabledFlag = true;
        this.columnDef = this.args.column;
        this.fieldId = this.columnDef && this.columnDef.id;
        this.cursor = 0;
        this.logger = new Logger('ComboBoxItemRenderer', null, 6);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    init() {
        this.logger.info('Method [init] -START- ', this.args.column.params.selectDataSource);
        try {
            if (this.columnDef.params.enableDisableCells) {
                this.enabledFlag = this.columnDef.params.enableDisableCells(this.args.item, this.args.column.field);
            }
            if (this.commonGrid.ITEM_CHANGED.observers && this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                /** @type {?} */
                var x = this.commonGrid.ITEM_CHANGED.observers[0];
                this.commonGrid.ITEM_CHANGED.observers = [];
                this.commonGrid.ITEM_CHANGED.observers[0] = x;
            }
            if (this.showHideCells) {
                /** @type {?} */
                const gridOptions = this.args.grid.getOptions();
                $('.comboBoxRender-dropDown-' + this.fieldId).remove();
                this.$input = $(`
                        <div class="renderAsInput comboBoxRender-container  container-${this.fieldId}">
                           <div class="input-group">
                               <input ${(!this.enabledFlag || !this.columnDef.params.grid.enabled) ? 'disabled' : ''} autocomplete="off" id="combobox-filter-input-${this.fieldId}" type="text"   class="form-control comboBoxRender-filter-input">
                               <div ${!this.enabledFlag || !this.columnDef.params.grid.enabled ? 'style="opacity:0.7;"' : ""} class="renderAsInput input-group-addon arrow-${this.fieldId}"><i class="renderAsInput glyphicon glyphicon-triangle-bottom"></i></div>
                           </div>`);
                this.$dropDown = $(`         ${this.enabledFlag && this.columnDef.params.grid.enabled ?
                    `<div class="renderAsInput comboBoxRender-dropDown-${this.fieldId}" style="position:absolute;z-index:2"> 
                               <ul id="list-${this.fieldId}" class="renderAsInput comboBoxRender-dropDown-ul">
                                   <!--<li *ngIf="_exist">{{ notFound }}</li>-->
                               </ul>
                           </div>` : ''}
                           
                       </div>
                   `);
                this.$input.appendTo(this.args.container);
                $('body').append(this.$dropDown);
                this.populateSelect(this.$input, this.args.column.params.selectDataSource, false);
            }
            else {
                this.$input = $(``);
            }
        }
        catch (e) {
            return;
        }
        this.logger.info('Method [init] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} select
     * @param {?} dataSource
     * @param {?} addBlank
     * @return {?}
     */
    populateSelect(select, dataSource, addBlank) {
        try {
            this.logger.info('Method [populateSelect] -START-', this.showHideCells);
            if (this.showHideCells) {
                // make sure the prop exists first
                /** @type {?} */
                var item = this.args.item;
                if (!(dataSource instanceof Array)) {
                    /** @type {?} */
                    var array = [];
                    array[0] = dataSource;
                    dataSource = array;
                }
                this.dataSource = dataSource;
                this.defaultValue = item[this.columnDef.field] && item[this.columnDef.field].toString();
                if (dataSource && dataSource.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                x => x.value == this.defaultValue))) {
                    this.defaultContent = dataSource.find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => x.value == this.defaultValue)).content;
                }
                if ((this.defaultContent == '' || this.defaultContent == undefined) && this.defaultValue != '') {
                    this.defaultContent = this.defaultValue;
                }
                $('#combobox-filter-input-' + this.fieldId).val(this.defaultContent);
                //console.log(' this.enabledFlag :',this.enabledFlag, ' this.columnDef.params.grid.enabled :',this.columnDef.params.grid.enabled)
                if (this.enabledFlag && this.columnDef.params.grid.enabled) {
                    //console.log('enter')
                    for (let index = 0; index < dataSource.length; index++) {
                        /** @type {?} */
                        var item = dataSource[index];
                        if (this.columnDef.dataId) {
                            if (dataSource[index].type && this.args.item[this.columnDef.dataId]) {
                                if (dataSource[index].type == this.args.item[this.columnDef.dataId]) {
                                    $('#list-' + this.fieldId).append(`<li  class='dropDownli-${this.fieldId} renderAsInput  comboBoxrender-dropDown-li ${String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : ""}'> 
                                  <option class="renderAsInput combo-option" value="${item.value}">
                                       ${item.content ? item.content : ''}   
                                  </option> 
                          </li>`);
                                }
                            }
                            else {
                                $('#list-' + this.fieldId).append(`<li  class='dropDownli-${this.fieldId} renderAsInput  comboBoxrender-dropDown-li ${String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : ""}'> 
                                    <option class="renderAsInput combo-option" value="${item.value}">
                                        ${item.content ? item.content : ''}   
                                    </option> 
                                </li>`);
                            }
                        }
                        else {
                            $('#list-' + this.fieldId).append(`<li  class='dropDownli-${this.fieldId} renderAsInput  comboBoxrender-dropDown-li ${String(this.defaultValue).trim() === String(item.value).trim() ? "isSelected" : ""}'> 
                                <option class="renderAsInput combo-option" value="${item.value}">
                                    ${item.content ? item.content : ''}   
                                </option> 
                            </li>`);
                        }
                    }
                    $('ul.comboBoxRender-dropDown-ul li:even').addClass('evenRows');
                    $('ul.comboBoxRender-dropDown-ul li:odd').addClass('oddRows');
                    $('.comboBoxRender-dropDown-' + this.fieldId).show();
                    $('#combobox-filter-input-' + this.fieldId).focus();
                    $('#combobox-filter-input-' + this.fieldId).select();
                    $(".arrow-" + this.fieldId).click((/**
                     * @return {?}
                     */
                    () => {
                        $('.comboBoxRender-dropDown-' + this.fieldId).toggle();
                    }));
                    $('.comboBoxRender-dropDown-ul').width($('.container-' + this.fieldId).width());
                    $("span[class^='fancytree-'] , .swtbtn ").click((/**
                     * @return {?}
                     */
                    () => {
                        $('#list-' + this.fieldId).remove();
                    }));
                    $(".arrow-" + this.fieldId).click((/**
                     * @return {?}
                     */
                    () => {
                        //-M5821 -issue 2.
                        $('#list-' + this.fieldId).toggle();
                    }));
                    $("#list-" + this.fieldId).width($('.container-' + this.fieldId).width());
                    $('.comboBoxRender-dropDown-' + this.fieldId).css({ 'z-index': '999' });
                    /** @type {?} */
                    const left = this.commonGrid.gridObj.getActiveCellPosition().left;
                    /** @type {?} */
                    const bottom = $(window).height() - this.commonGrid.gridObj.getActiveCellPosition().top - this.commonGrid.gridObj.getActiveCellPosition().height;
                    //-Fix M5267 / Issue 23 : Open the "Recon.type/Life.type" combobox in the upper grid ==> The list options is shifted
                    $('.comboBoxRender-dropDown-' + this.fieldId).css('bottom', bottom);
                    /** @type {?} */
                    var classes = $($($('.slickgrid-container.' + this.commonGrid.gridUID)[0].offsetParent)[0]).attr("class");
                    /** @type {?} */
                    var exist = false;
                    if (classes) {
                        exist = classes.includes('window-container');
                    }
                    $("#list-" + this.fieldId).css('left', left + (exist ? 9 : 0));
                    /** @type {?} */
                    var selectedItem = $('li.comboBoxrender-dropDown-li.isSelected:visible');
                    //console.log(' selectedItem =',selectedItem);
                    //-Fix M5305/Issue 1.
                    if (selectedItem && selectedItem.length > 0)
                        $(selectedItem)[0].scrollIntoView();
                    /** @type {?} */
                    var selected = false;
                    $('#combobox-filter-input-' + this.fieldId).on("input", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        $("#list-" + this.fieldId + " li").filter((/**
                         * @return {?}
                         */
                        function () {
                            //-Fix M5305 : Search By UpperCase does not work.
                            $(this).toggle($(this).text().toLowerCase().indexOf(String(event.target.value).toLowerCase()) > -1);
                        }));
                        //console.log('displayed li :', $('li[style*="display: list-item"]') );
                        /** @type {?} */
                        var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                        if (visibleLi.length > 0) {
                            $('.isSelected').removeClass('isSelected');
                            $(visibleLi[0]).addClass('isSelected');
                        }
                        this.cursor = 0;
                    }));
                    $('#combobox-filter-input-' + this.fieldId).keydown((/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        //console.log('dropDownli keydown event.keyCode',event.keyCode);
                        if (event.keyCode == 40 || event.keyCode == 38) {
                            event.preventDefault();
                            event.stopPropagation();
                            $("#list-" + this.fieldId).show();
                            $('.comboBoxRender-dropDown-' + this.fieldId).show();
                        }
                        else if (event.keyCode == 13) {
                            /** @type {?} */
                            var newValue = $('#combobox-filter-input-' + this.fieldId).val();
                            /** @type {?} */
                            var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                            if (visibleLi.length == 0) {
                                this.args.grid.getEditorLock().commitCurrentEdit();
                            }
                            else {
                                $('li.isSelected').mousedown();
                            }
                        }
                    }));
                    //-CLICK ON LIST - SELECT ITEM
                    $('.dropDownli-' + this.fieldId).mousedown((/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        /** @type {?} */
                        var target_value = '';
                        if ($(event.target)[0].nodeName == "LI") {
                            //console.log('$($( li.isSelected ).children()[0] ).text().trim() =',$($('li.isSelected').children()[0] ).text().trim())
                            /** @type {?} */
                            var target_value_index = this.args.column.params.selectDataSource.findIndex((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => x.content == $($('li.isSelected').children()[0]).text().trim()));
                            if (target_value_index != -1)
                                target_value = this.args.column.params.selectDataSource[target_value_index].value;
                        }
                        /** @type {?} */
                        var value = $(event.target)[0].nodeName == "LI" ? target_value : event.target.value;
                        /** @type {?} */
                        var label = $(event.target)[0].nodeName == "LI" ? $($('li.isSelected').children()[0]).text().trim() : event.target.label;
                        if (value !== this.defaultValue) {
                            $('#combobox-filter-input-' + this.fieldId).val(label);
                            this.args.item[this.args.column.field] = value;
                            this.commonGrid.selectedItem[this.args.column.field].content = value;
                            // -- check if changed
                            this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => ((x.crud_data.id == this.args.item.id))));
                            if (this.CRUD_CHANGES_DATA != undefined && this.CRUD_CHANGES_DATA != null) {
                                this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : null;
                            }
                            /** @type {?} */
                            var crudInsert = null;
                            /** @type {?} */
                            var thereIsInsert = false;
                            //console.log('originalDefaultValue :',this.originalDefaultValue)
                            //console.log('this.defaultValue :',this.defaultValue)
                            //console.log('this.args.item[this.args.column.field] :',this.args.item[this.args.column.field])
                            if ((this.originalDefaultValue == null && (this.defaultValue != this.args.item[this.args.column.field])) || (((this.originalDefaultValue != null) && (this.originalDefaultValue != this.args.item[this.args.column.field])))) {
                                if (this.commonGrid.changes.getValues().length > 0) {
                                    crudInsert = this.commonGrid.changes.getValues().find((/**
                                     * @param {?} x
                                     * @return {?}
                                     */
                                    x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                                    if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                                        thereIsInsert = true;
                                }
                                /** @type {?} */
                                var ComboboxSelectedItem = this.dataSource.find((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => x.value == value));
                                /** @type {?} */
                                var updatedObject = {
                                    rowIndex: this.args.item.id,
                                    columnIndex: this.args.column.columnorder,
                                    new_row: this.args.item,
                                    changedColumn: this.args.column.field,
                                    oldValue: this.defaultValue,
                                    newValue: this.args.item[this.args.column.field],
                                    ComboboxSelectedItem: ComboboxSelectedItem
                                };
                                if (!thereIsInsert) {
                                    /** @type {?} */
                                    var original_row = [];
                                    for (let key in this.args.item) {
                                        if (key != 'slickgrid_rowcontent') {
                                            original_row[key] = this.args.item[key];
                                        }
                                        else {
                                            break;
                                        }
                                    }
                                    original_row['slickgrid_rowcontent'] = {};
                                    for (let key in this.args.item['slickgrid_rowcontent']) {
                                        original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                                        original_row[key] = original_row['slickgrid_rowcontent'][key].content;
                                    }
                                    original_row[this.args.column.field] = this.defaultValue;
                                    original_row['slickgrid_rowcontent'][this.args.column.field].content = this.defaultValue;
                                    updatedObject['original_row'] = original_row;
                                    this.commonGrid.spyChanges({ field: this.args.column.field, value: this.args.item[this.args.column.field] });
                                    if (SwtCommonGridItemRenderChanges.observers.length > 1) {
                                        /** @type {?} */
                                        var x = SwtCommonGridItemRenderChanges.observers[0];
                                        SwtCommonGridItemRenderChanges.observers = [];
                                        SwtCommonGridItemRenderChanges.observers[0] = x;
                                    }
                                    SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                                    this.commonGrid.updateCrud(updatedObject);
                                }
                                else {
                                    crudInsert['crud_data'][this.args.column.field] = this.args.item[this.args.column.field];
                                    crudInsert['crud_data']['slickgrid_rowcontent'][this.args.column.field] = { content: this.args.item[this.args.column.field] };
                                }
                                /** @type {?} */
                                var listEvent = {
                                    rowIndex: this.args.item.id,
                                    target: "ComboBoxItemRenderer",
                                    dataField: this.args.column.field,
                                    listData: Object.assign({}, updatedObject),
                                    dataprovider: this.args.column.params.selectDataSource
                                };
                                /*  if ( this.commonGrid.ITEM_CHANGED.observers.length > 1 ) {
                                    var x: Observer<any> = this.commonGrid.ITEM_CHANGED.observers[0];
                                    this.commonGrid.ITEM_CHANGED.observers = []
                                    this.commonGrid.ITEM_CHANGED.observers[0] = x;
                                }*/
                                this.commonGrid.ITEM_CHANGED.emit(listEvent);
                            }
                            else if ((this.originalDefaultValue == this.args.item[this.args.column.field])) {
                                /** @type {?} */
                                var crudChange = this.commonGrid.changes.getValues().find((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => ((x.crud_data.id == this.args.item.id))));
                                /** @type {?} */
                                var ch = String("U(" + this.args.column.field + ")");
                                if (crudChange) {
                                    if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                                    }
                                    else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                                    }
                                    else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                        crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                                    }
                                    if (crudChange['crud_operation'] == "") {
                                        /** @type {?} */
                                        var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                         * @param {?} x
                                         * @return {?}
                                         */
                                        x => ((x.crud_data.id == this.args.item.id))));
                                        this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                                    }
                                }
                                //- Do not emit SpyNoChanges on the grid unless there is other changes.
                                if (this.commonGrid.changes.size() == 0)
                                    this.commonGrid.spyNoChanges({ field: this.args.column.field });
                                SwtCommonGridItemRenderChanges.emit({ field: this.args.column.field, value: this.args.item[this.args.column.field], id: this.args.item.id });
                            }
                        }
                        //-M5821 -issue 2.
                        $('#list-' + this.fieldId).hide();
                        this.args.grid.getEditorLock().commitCurrentEdit();
                        this.cursor = 0;
                    }));
                    //-FOCUS IN :
                    /** @type {?} */
                    var target = {
                        name: this.args.column.name,
                        field: this.args.column.field,
                        editor: "ComboBoxItemRenderer",
                        formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                        data: this.args.item
                    };
                    /** @type {?} */
                    var ListEvent = {
                        rowIndex: this.args.item.id,
                        cellIndex: this.args.column.columnorder,
                        columnIndex: this.args.column.columnorder,
                        target: target
                    };
                    if (this.commonGrid.ITEM_FOCUS_IN.observers.length > 1) {
                        /** @type {?} */
                        var x = this.commonGrid.ITEM_FOCUS_IN.observers[0];
                        this.commonGrid.ITEM_FOCUS_IN.observers = [];
                        this.commonGrid.ITEM_FOCUS_IN.observers[0] = x;
                    }
                    this.commonGrid.ITEM_FOCUS_IN.emit(ListEvent);
                    //FOCUS OUT :
                    $(this.$input).on('focusout', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        console.log('focusout');
                        /** @type {?} */
                        var newValue = $('#combobox-filter-input-' + this.fieldId).val();
                        if (this.commonGrid.onFocusOut.observers.length > 1) {
                            /** @type {?} */
                            var x = this.commonGrid.onFocusOut.observers[0];
                            this.commonGrid.onFocusOut.observers = [];
                            this.commonGrid.onFocusOut.observers[0] = x;
                        }
                        this.commonGrid.onFocusOut.emit(this);
                        /** @type {?} */
                        var target = {
                            name: this.args.column.name,
                            field: this.args.column.field,
                            editor: "ComboBoxItemRenderer",
                            formatter: "ComboFormatter",
                            data: this.args.item
                        };
                        /** @type {?} */
                        var ListEvent = {
                            rowIndex: this.args.item.id,
                            cellIndex: this.args.column.columnorder,
                            columnIndex: this.args.column.columnorder,
                            target: target,
                            dataprovider: this.args.column.params.selectDataSource
                        };
                        if (this.commonGrid.ITEM_FOCUS_OUT.observers.length > 1) {
                            /** @type {?} */
                            var x = this.commonGrid.ITEM_FOCUS_OUT.observers[0];
                            this.commonGrid.ITEM_FOCUS_OUT.observers = [];
                            this.commonGrid.ITEM_FOCUS_OUT.observers[0] = x;
                        }
                        this.commonGrid.ITEM_FOCUS_OUT.emit(ListEvent);
                        //-M5821 -issue 2.
                        console.log('this.fieldId  :', this.fieldId);
                        $('.comboBoxRender-dropDown-ul').hide();
                        $('#list-' + this.fieldId).hide();
                        if (!$($(event)[0].relatedTarget).is('div.grid-canvas')) {
                            this.commonGrid.gridObj.getEditorLock().commitCurrentEdit();
                        }
                        if (this.dataSource.findIndex((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => x.content === $('#combobox-filter-input-' + this.fieldId).val())) == -1) {
                            //-Append new item to combodataprovider:
                            this.args.item[this.args.column.field] = newValue;
                            this.commonGrid.selectedItem[this.args.column.field].content = newValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: newValue,
                                ComboboxSelectedItem: null
                            };
                            /** @type {?} */
                            var listEvent = {
                                rowIndex: this.args.item.id,
                                target: "ComboBoxItemRenderer",
                                dataField: this.args.column.field,
                                listData: Object.assign({}, updatedObject),
                                dataprovider: this.args.column.params.selectDataSource
                            };
                            this.commonGrid.ITEM_CHANGED.emit(listEvent);
                            this.args.grid.getEditorLock().commitCurrentEdit();
                            this.cursor = 0;
                        }
                    }));
                    //-FILTER ON INPUT:
                    /** @type {?} */
                    var i = 0;
                    $('#combobox-filter-input-' + this.fieldId).on('keyup', (/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        /** @type {?} */
                        var $hlight = $('li.isSelected:visible');
                        /** @type {?} */
                        var $div = $('li');
                        /** @type {?} */
                        var visibleLi = $('li.comboBoxrender-dropDown-li:visible');
                        //console.log('displayed visibleLi.length :', visibleLi.length , ' this.cursor : ',this.cursor);
                        if (event.keyCode == 40) {
                            if (visibleLi.length > 0 && this.cursor < visibleLi.length - 1) {
                                $('.isSelected').removeClass('isSelected');
                                $(visibleLi[this.cursor + 1]).addClass('isSelected');
                                $(visibleLi[this.cursor + 1])[0].scrollIntoView();
                                this.cursor++;
                            }
                        }
                        else if (event.keyCode === 38) {
                            if (visibleLi.length > 0 && this.cursor > 0) {
                                $('.isSelected').removeClass('isSelected');
                                $(visibleLi[this.cursor - 1]).addClass('isSelected');
                                $(visibleLi[this.cursor - 1])[0].scrollIntoView();
                                this.cursor--;
                            }
                        }
                    }));
                }
            }
            this.logger.info('Method [populateSelect] -END-');
        }
        catch (e) {
            return;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('Method [loadValue] -START-');
        this.logger.info('Method [loadValue] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    serializeValue() {
        this.logger.info('Method [serializeValue] -START/END-', this.$input.val());
        return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    destroy() {
        try {
            this.logger.info('Method [destroy] -START/END-');
        }
        catch (e) {
            return;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    focus() {
        this.logger.info('Method [focus] -START/END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('Method [applyValue] -START/end- item =', item, ' state =', state);
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    isValueChanged() {
        try {
            this.logger.info('Method [isValueChanged] -START/END-  =');
            return true;
        }
        catch (e) {
            return;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    validate() {
        try {
            this.logger.info('Method [validate] -START-');
            //console.log('Method [validate] -START-');
            if (this.showHideCells) {
                if (this.args.column.validator) {
                    /** @type {?} */
                    const validationResults = this.args.column.validator(this.$input.val());
                    if (!validationResults.valid) {
                        return validationResults;
                    }
                }
            }
            this.logger.info('Method [validate] -END-');
            //console.log('Method [validate] -END-');
            return {
                valid: true,
                msg: null
            };
        }
        catch (e) {
            return;
        }
    }
    /**
     * This method is used to refresh
     * comboBox view.
     * @return {?}
     */
    showAlert() {
        try {
            /** @type {?} */
            const index = this.dataSource.findIndex((/**
             * @param {?} item
             * @return {?}
             */
            (item) => (((((/** @type {?} */ (item.content))).toUpperCase()).trim()).startsWith((((/** @type {?} */ ($('#combobox-filter-input-' + this.fieldId).val()))).toUpperCase()).trim()))));
            // if ( index === -1 && !this.appendSource && this.dataSource.length > 0 ) {
            // SwtAlert.warning( SwtUtil.getCommonMessages( 'alert.combo_warning' ), null, 4, this, () => {
            //     $( '#combobox-filter-input-' + this.fieldId ).val( null );
            //     $( '#combobox-filter-input-' + this.fieldId ).focus();
            //     $( "#list-" + this.fieldId + " li" ).filter( function() {
            //         $( this ).toggle( true )
            //     } );
            // }, 4 );
            // }
        }
        catch (error) {
            return;
        }
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.defaultContent;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.dataSource;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.correspondantProperty;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.$dropDown;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_OPERATION;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    ComboBoxItemRenderer.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.enabledFlag;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.columnDef;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.fieldId;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.cursor;
    /**
     * @type {?}
     * @private
     */
    ComboBoxItemRenderer.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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