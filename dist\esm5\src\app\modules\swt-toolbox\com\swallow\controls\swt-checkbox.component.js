/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Input, ElementRef, Component } from "@angular/core";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService } from "../utils/common.service";
import { Container } from "../containers/swt-container.component";
/** @type {?} */
var $ = require('jquery');
var SwtCheckBox = /** @class */ (function (_super) {
    tslib_1.__extends(SwtCheckBox, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function SwtCheckBox(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        _this._selected = false;
        _this._fontWeight = "100";
        return _this;
    }
    /**
     * @return {?}
     */
    SwtCheckBox.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var popperContentEl = this.elem.nativeElement.querySelector('popper-content');
        if (popperContentEl)
            this.elem.nativeElement.appendChild(popperContentEl);
    };
    Object.defineProperty(SwtCheckBox.prototype, "label", {
        get: /**
         * @return {?}
         */
        function () {
            return this._label;
        },
        //---Label--------------------------------------------------------------------------------------------------------
        set: 
        //---Label--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._label = value;
            if ($(this.elem.nativeElement))
                $($(this.elem.nativeElement).children()[0]).append("<span class='checkboxLabel'>" + this._label + "</span>");
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtCheckBox.prototype, "value", {
        get: /**
         * @return {?}
         */
        function () {
            return this._value;
        },
        //---value--------------------------------------------------------------------------------------------------------
        set: 
        //---value--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._value = value;
                /** @type {?} */
                var item = $(this.elem.nativeElement) ? $(this.elem.nativeElement).find('.item') : null;
                if (item.length > 0) {
                    $(item).val(this._value);
                }
            }
            catch (error) {
                console.error('method [ set value] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtCheckBox.prototype, "tabIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._tabIndex;
        },
        //---tabIndex-----------------------------------------------------------------------------------------------------
        set: 
        //---tabIndex-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._tabIndex = String(value);
                /** @type {?} */
                var checkmark = $(this.elem.nativeElement) ? $(this.elem.nativeElement).find('.checkmark') : null;
                if (checkmark.length > 0) {
                    this.addTabIndex(checkmark, this._tabIndex);
                }
            }
            catch (error) {
                console.error('method [ set tabIndex] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtCheckBox.prototype, "selected", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selected;
        },
        //---selected-----------------------------------------------------------------------------------------------------
        set: 
        //---selected-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                value = this.adaptValueAsBoolean(value);
                /** @type {?} */
                var item = $(this.elem.nativeElement) ? $($($(this.elem.nativeElement).children()[0]).children()[0]) : null;
                if (item.length > 0) {
                    $(item).prop('checked', value);
                    this._selected = value;
                }
                if (this.firstCall) {
                    this.originalValue = value;
                    this.firstCall = false;
                }
                this._spyChanges(this._selected);
            }
            catch (error) {
                console.error('method [set selected] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtCheckBox.prototype, "fontWeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this._fontWeight;
        },
        //---FontWeight------------------------------------------------------------------------------------------------
        set: 
        //---FontWeight------------------------------------------------------------------------------------------------
        /**
         * @param {?} fontWeight
         * @return {?}
         */
        function (fontWeight) {
            try {
                this._fontWeight = fontWeight;
                this.setStyle('font-weight', fontWeight, this.elem.nativeElement.children[0]);
            }
            catch (error) {
                console.error('method [ set fontWeight ] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    SwtCheckBox.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtCheckBox.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtCheckBox');
        //-Set selected value to checkbox for Spy changes .
        if (this.firstCall) {
            this.selected = this._selected;
        }
    };
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the change event.
     * */
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the change event.
     *
     * @param {?} event
     * @return {?}
     */
    SwtCheckBox.prototype.ChangeEventHandler = /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the change event.
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        try {
            this._selected = event.target.checked;
            event.component = this;
            this.change_.emit(event);
            this.change(event);
            this._spyChanges(this.selected);
            event.stopImmediatePropagation(); // to prevent jQuery Click fires twice  
        }
        catch (error) {
            console.error('method [ChangeEventHandler] error :', error);
        }
    };
    /**
     * Click
     * @param event
     */
    /**
     * Click
     * @param {?} event
     * @return {?}
     */
    SwtCheckBox.prototype.emitClickEvent = /**
     * Click
     * @param {?} event
     * @return {?}
     */
    function (event) {
        try {
            this._selected = event.target.checked;
            event.stopImmediatePropagation(); // to prevent jQuery Click fires twice when clicking on checkbox
            // update focus Manager data (focused elem)
            focusManager.focusTarget = this.id;
        }
        catch (error) {
            console.error('method [emitClickEvent] error :', error);
        }
    };
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the keyDown event.
     * */
    /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the keyDown event.
     *
     * @param {?} event
     * @return {?}
     */
    SwtCheckBox.prototype.KeyDownEventHandler = /**
     * Warning : don't use this method
     * when instantiate the checkBox. it's an intern
     * method to handle the keyDown event.
     *
     * @param {?} event
     * @return {?}
     */
    function (event) {
        try {
            //prevent click enter on checkbox when it's disabled
            if (event.keyCode == 13 && !this.enabled) {
                event.preventDefault();
            }
            else if (this.enabled) {
                this.onKeyDown_.emit(event);
                this.keyDown(event);
                this.selected = !this.selected;
            }
        }
        catch (error) {
            console.error('method [KeyDownEventHandler] error :', error);
        }
    };
    /**
     * resetOriginalValue
     */
    /**
     * resetOriginalValue
     * @return {?}
     */
    SwtCheckBox.prototype.resetOriginalValue = /**
     * resetOriginalValue
     * @return {?}
     */
    function () {
        try {
            this.selected = this.originalValue;
            this._spyChanges(this.selected);
        }
        catch (error) {
            console.error('method [spyChanges] error :', error);
        }
    };
    SwtCheckBox.decorators = [
        { type: Component, args: [{
                    selector: 'SwtCheckBox',
                    template: "\n  \n     <label   class=\"SwtCheckBox-container\" tabindex=\"-1\" >\n          <input (change)=\"ChangeEventHandler($event)\" \n        \n                 (keydown.Enter)=\"KeyDownEventHandler($event)\"\n                 (click)=\"emitClickEvent($event)\" \n                 type    =\"checkbox\" \n                 class   =\"item\" \n                 tabindex=\"-1\"  >\n          <span    popper=\"{{this.toolTipPreviousValue}}\"\n          [popperTrigger]=\"'hover'\"\n          [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n          [popperPlacement]=\"'bottom'\"\n          [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\" class=\"checkmark\" (keydown.Enter)=\"KeyDownEventHandler($event)\"></span>\n      </label>   \n     \n     ",
                    styles: ["\n            :host {\n                /*overflow: hidden;*/\n                width: fit-content;\n                outline: none!important;\n                flex-direction: row;\n                box-sizing: border-box;\n                display: flex;\n                place-content: stretch flex-start;\n                align-items: stretch;\n                height:5px;\n                line-height: 22px;\n            }\n            .swtcheck-container{\n               /* margin: 0px 0px 5px 0px;*/\n                outline: none !important;\n             }\n            .cblabel{\n                position: relative;\n                top: -3px;\n             }\n            .disabled{\n                color :#AAB3B3;\n             }\n            /* The container */\n            .SwtCheckBox-container {\n                display: block;\n                position: relative;\n                padding-left: 20px;\n                cursor: pointer;\n                outline: none;\n                font-size: 11px;\n                -webkit-user-select: none;\n                -moz-user-select: none;\n                -ms-user-select: none;\n                user-select: none;\n            }\n            \n            /* Hide the browser's default checkbox */\n            .SwtCheckBox-container input {\n                position: absolute;\n                opacity: 0;\n                cursor: pointer;\n                height: 0;\n                width: 0;\n            }\n            \n            /* Create a custom checkbox */\n            .checkmark {\n                position: absolute;\n                top: 5px;\n                left: 0;\n                height: 13px;\n                width: 13px;\n                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);\n                border:1px solid #B7BABC;\n            }\n            \n            /* On mouse-over, add a grey background color */\n            .SwtCheckBox-container:hover input ~ .checkmark {\n               background-image: -webkit-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -moz-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -ms-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: -o-linear-gradient(top, #F6FBFF, #E2EEF4);\n                background-image: linear-gradient(to bottom, #F6FBFF, #E2EEF4);\n                border: 1px solid #009DFF;\n                cursor: default;\n            }\n            \n            /* When the checkbox is checked, add a blue background */\n            .SwtCheckBox-container input:checked ~ .checkmark {\n                background-image: -webkit-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: -o-linear-gradient(top, #F1F9FF, #CDE0EB);\n                background-image: linear-gradient(to bottom, #F1F9FF, #CDE0EB);\n            }\n            .SwtCheckBox-container input:active ~ .checkmark {\n               background-image: -webkit-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -moz-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -ms-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: -o-linear-gradient(top, #E3F4FF, #9ED9FF);\n                background-image: linear-gradient(to bottom, #E3F4FF, #9ED9FF);\n            }\n            \n            /* Create the checkmark/indicator (hidden when not checked) */\n            .checkmark:after {\n                content: \"\";\n                position: absolute;\n                display: none;\n            }\n            \n            /* Show the checkmark when checked */\n            .SwtCheckBox-container input:checked ~ .checkmark:after {\n                display: block;\n            }\n            \n            /* Style the checkmark/indicator */\n            .SwtCheckBox-container .checkmark:after {\n                 left: 3px;\n                 top: 0px;\n                 width: 5px;\n                 height: 10px;\n                 border: solid #525960;\n                 border-width: 0 3px 3px 0;\n                 -webkit-transform: rotate(45deg);\n                 -ms-transform: rotate(45deg);\n                 transform: rotate(45deg);\n            }\n           \n            label {\n                font-weight: 100;\n                line-height:12px;\n            }\n            label:hover {\n                cursor: default;\n            }\n            \n         "]
                }] }
    ];
    /** @nocollapse */
    SwtCheckBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtCheckBox.propDecorators = {
        label: [{ type: Input, args: ['label',] }],
        value: [{ type: Input, args: ['value',] }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        selected: [{ type: Input, args: ['selected',] }],
        fontWeight: [{ type: Input }]
    };
    return SwtCheckBox;
}(Container));
export { SwtCheckBox };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._value;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtCheckBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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