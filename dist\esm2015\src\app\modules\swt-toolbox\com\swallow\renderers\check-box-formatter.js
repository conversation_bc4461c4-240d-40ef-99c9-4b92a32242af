/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CellBackgroundColor, CustomCell } from "./cellItemRenderUtilities";
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
export const CheckBoxFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let negative = false;
    /** @type {?} */
    let color = '#173553';
    /** @type {?} */
    let text;
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let selectable = columnDef.params.grid.selectable;
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    /** @type {?} */
    let backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    let backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    let style = CustomCell(dataContext, field);
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        style += 'background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (showHideCells) {
        if (value === 'N' || value === 'false' || value === false || value == undefined) {
            // non checked
            text = `<div class="containerCheckBox" style='${style}' ><input ${(!selectable || !enabledFlag || !columnDef.params.grid.enabled) ? 'disabled' : ''}   type='checkbox' style='${style}'  class='formator-checkbox'  /></div>`;
        }
        else {
            // checked
            text = `<div class="containerCheckBox" style='${style}' > <input ${(!selectable || !enabledFlag || !columnDef.params.grid.enabled) ? 'disabled' : ''} checked type='checkbox' style='${style}'    class='formator-checkbox'    /> </div> `;
        }
    }
    else {
        text = ``;
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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