/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Logger } from "../../../com/swallow/logging/logger.service";
import { focusManager } from "../managers/focus-manager.service";
import { BaseObject } from "../model/base-object";
/**
 * <AUTHOR>
 * \@date 28/05/2019
 * Language Version:    TypeScript 3.2
 * The EventDispatcher service is the base class for all classes that dispatch events.
 * The EventDispatcher service is the base class for all other classes.
 * The EventDispatcher service allows any object on the subclasses to be an event target and as such, to use the methods of the IEventDispatcher interface.
 * Event targets are an important part of the SwtToolBox event model.
 * The event target serves as the focal point for how events flow through the subclasses hierarchy.
 * When an event such as a mouse click or a keypress occurs, SwtToolbox dispatches an event object into the event flow from the root of the subclasses.
 * The event object then makes its way through the subclasses until it reaches the event target, at which point it begins its return trip through the subclasses.
 * This round-trip journey to the event target is conceptually divided into three phases:
 * the capture phase comprises the journey from the root to the last node before the event target's node, the target phase comprises only the event target node,
 * and the bubbling phase comprises any subsequent nodes encountered on the return trip to the root of the subclasses.
 * In general, the easiest way for a user-defined class to gain event dispatching capabilities is to extend EventDispatcher.
 * If this is impossible (that is, if the class is already extending another class), you can instead implement the IEventDispatcher interface,
 * create an EventDispatcher member, and write simple hooks to route calls into the aggregated EventDispatcher.
 */
export class EventDispatcher extends BaseObject {
    /**
     * @param {?} target
     * @param {?} __targetService
     */
    constructor(target, __targetService) {
        super();
        this.target = target;
        this.__targetService = __targetService;
        this.eventlist = [];
        this.log = new Logger("EventDispatcher", this.__targetService.httpclient);
    }
    /**
     * Registers an event listener object with an EventDispatcher object so
     * that the listener receives notification of an event.
     * @param {?} type
     * @param {?} listener
     * @param {?=} useCapture
     * @param {?=} priority
     * @param {?=} useWeakReference
     * @param {?=} target
     * @return {?}
     */
    //
    addEventListener(type, listener, useCapture = false, priority = 0, useWeakReference = false, target) {
        try {
            this.eventlist[type] = listener;
            if (!target) {
                target = this.target.nativeElement;
            }
            if ($(target) && $(target)[0]) {
                $(target)[0].addEventListener(type, (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    if (type == "click" || type == "focus" || type == "focusin" || type == "keyup" || type == "keydown" || type == "change") {
                        focusManager.focusTarget = this;
                    }
                    this.eventlist[type]();
                }), useCapture);
            }
        }
        catch (error) {
            this.log.error("addEventListener - error: ", error);
        }
    }
    /**
     * Dispatches an event into the event flow.
     * @param {?} event
     * @return {?}
     */
    dispatchEvent(event) {
        this.log.info('dispatchEvent START.');
        /** @type {?} */
        var eventName = "";
        try {
            if (typeof (event) === 'object') {
                event.type ? eventName = event.type : '';
            }
            else {
                eventName = event;
            }
            if (this.eventlist[eventName]) {
                this.eventlist[eventName](event);
            }
        }
        catch (error) {
            this.log.error('dispatchEvent method' + error);
        }
        this.log.info('dispatchEvent END.');
    }
    /**
     * Checks whether the EventDispatcher object has any listeners registered for a specific type of event.
     * @param {?} type
     * @return {?}
     */
    hasEventListener(type) {
        try {
        }
        catch (error) {
            this.log.error("hasEventListener - error: ", error);
        }
    }
    /**
     * Removes a listener from the EventDispatcher object.
     * @param {?} type
     * @param {?=} listener
     * @param {?=} useCapture
     * @return {?}
     */
    removeEventListener(type, listener, useCapture) {
        try {
            this.log.info("removeEventListener ENTER. type:", type);
            delete this.eventlist[type];
        }
        catch (error) {
            this.log.error("removeEventListener - error: ", error);
        }
        this.log.info("removeEventListener END.");
    }
    /**
     * Checks whether an event listener is registered with this EventDispatcher
     * object or any of its ancestors for the specified event type.
     * @param {?} type
     * @return {?}
     */
    willTrigger(type) {
        try {
        }
        catch (error) {
            this.log.error("willTrigger - error: ", error);
        }
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            //  console.log('[EventDispatcher] ngOnDestroy');
            //            for (var index = 0; index <  this.eventlist.length; index++) {
            //                this.removeEventListener(type, null, null)
            //            }
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    }
}
if (false) {
    /**
     * @type {?}
     * @protected
     */
    EventDispatcher.prototype.log;
    /**
     * @type {?}
     * @protected
     */
    EventDispatcher.prototype.eventlist;
    /**
     * @type {?}
     * @private
     */
    EventDispatcher.prototype.target;
    /**
     * @type {?}
     * @private
     */
    EventDispatcher.prototype.__targetService;
}
/**
 * @record
 */
export function IEventDispatcher() { }
if (false) {
    /** @type {?} */
    IEventDispatcher.prototype.addEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.dispatchEvent;
    /** @type {?} */
    IEventDispatcher.prototype.hasEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.removeEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.willTrigger;
}
//# sourceMappingURL=data:application/json;base64,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