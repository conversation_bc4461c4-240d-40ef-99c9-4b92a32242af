/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { OperatorType } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
/** @type {?} */
var o = DOMPurify_;
var CustomInputFilter = /** @class */ (function () {
    function CustomInputFilter() {
        this._clearFilterTriggered = false;
        this._shouldTriggerQuery = true;
        this.searchTerms = [];
        this.operator = OperatorType.equal;
        this.clientSideFilter = true;
    }
    Object.defineProperty(CustomInputFilter.prototype, "columnFilter", {
        /** Getter for the Column Filter */
        get: /**
         * Getter for the Column Filter
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter || {};
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomInputFilter.prototype, "gridOptions", {
        /** Getter for the Grid Options pulled through the Grid Object */
        get: /**
         * Getter for the Grid Options pulled through the Grid Object
         * @protected
         * @return {?}
         */
        function () {
            return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Initialize the Filter
     */
    /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    CustomInputFilter.prototype.init = /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    function (args) {
        var _this = this;
        this.grid = args.grid;
        this.callback = args.callback;
        this.columnDef = args.columnDef;
        this.searchTerms = (args.hasOwnProperty('searchTerms') ? args.searchTerms : []) || [];
        this.clientSideFilter = this.columnDef.params.grid.clientSideFilter;
        // filter input can only have 1 search term, so we will use the 1st array index if it exist
        /** @type {?} */
        var searchTerm = (Array.isArray(this.searchTerms) && this.searchTerms.length >= 0) ? this.searchTerms[0] : '';
        // step 1, create HTML string template
        /** @type {?} */
        var filterTemplate = this.buildTemplateHtmlString();
        // step 2, create the DOM Element of the filter & initialize it if searchTerm is filled
        this.$filterElm = this.createDomElement(filterTemplate, searchTerm);
        $('#search-filter-' + this.grid.getUID() + this.columnDef.id).focus();
        /** @type {?} */
        var elem = $('.ms-filter-' + this.columnDef.id);
        if (searchTerm)
            $(elem).addClass('filled');
        // step 3, subscribe to the keyup event and run the callback when that happens
        this.$filterElm.keyup((/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            // Prevent the event from bubbling up
            /** @type {?} */
            var value = e && e.target && e.target.value || '';
            /** @type {?} */
            var enableWhiteSpaceTrim = _this.gridOptions.enableFilterTrimWhiteSpace || _this.columnFilter.enableTrimWhiteSpace;
            if (typeof value === 'string' && enableWhiteSpaceTrim) {
                value = value.trim();
            }
            /** @type {?} */
            var elem = $('.ms-filter-' + _this.columnDef.id);
            if (_this._clearFilterTriggered)
                $(elem).removeClass('filled');
            else {
                if (_this.clientSideFilter)
                    value === '' ? $(elem).removeClass('filled') : $(elem).addClass('filled');
            }
            if (_this._clearFilterTriggered && (_this.clientSideFilter || (!_this.clientSideFilter && e.keyCode == 13))) {
                _this.callback(e, { columnDef: _this.columnDef, clearFilterTriggered: _this._clearFilterTriggered, shouldTriggerQuery: _this._shouldTriggerQuery });
            }
            else if (!_this._clearFilterTriggered && (_this.clientSideFilter || (!_this.clientSideFilter && e.keyCode == 13))) {
                _this.callback(e, { columnDef: _this.columnDef, searchTerms: [value], shouldTriggerQuery: _this._shouldTriggerQuery });
            }
            // reset both flags for next use
            _this._clearFilterTriggered = false;
            _this._shouldTriggerQuery = true;
            if (e.keyCode == 13) {
                $.each(_this.grid.getHeaderRow(), (/**
                 * @param {?} index
                 * @param {?} header
                 * @return {?}
                 */
                function (index, header) {
                    if (header instanceof HTMLElement) { // Ensure it's a valid DOM element
                        $(header).toggle();
                    }
                }));
            }
        }));
    };
    /**
     * Clear the filter value
     */
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    CustomInputFilter.prototype.clear = /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    function (shouldTriggerQuery) {
        if (shouldTriggerQuery === void 0) { shouldTriggerQuery = true; }
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            this.$filterElm.val('');
            this.$filterElm.trigger('keyup');
        }
    };
    /**
     * destroy the filter
     */
    /**
     * destroy the filter
     * @return {?}
     */
    CustomInputFilter.prototype.destroy = /**
     * destroy the filter
     * @return {?}
     */
    function () {
        if (this.$filterElm) {
            this.$filterElm.off('keyup').remove();
        }
    };
    /** Set value(s) on the DOM element */
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    CustomInputFilter.prototype.setValues = /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    function (values) {
        if (values) {
            this.$filterElm.val((/** @type {?} */ (values)));
        }
    };
    //
    // private functions
    // ------------------
    /**
     * Create the HTML template as a string
     */
    /**
  * Create the HTML template as a string
  */
    /**
     * Create the HTML template as a string
     */
    //
    // private functions
    // ------------------
    /**
         * Create the HTML template as a string
         */
    /**
     * Create the HTML template as a string
     */
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    CustomInputFilter.prototype.buildTemplateHtmlString = 
    //
    // private functions
    // ------------------
    /**
         * Create the HTML template as a string
         */
    /**
     * Create the HTML template as a string
     */
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        console.log('buildTemplateHtmlString');
        /** @type {?} */
        var placeholder = 'search in ' + this.columnDef.name;
        /** @type {?} */
        var $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        /** @type {?} */
        var elem = $('#' + this.grid.getUID() + this.columnDef.id);
        // Add the filter button to the header
        elem.append("\n    <div class=\"ms-parent ms-filter search-filter ms-filter-" + this.columnDef.id + "\" style=\"width: 32px;\">\n      <button id=\"filter-" + (this.grid.getUID() + this.columnDef.id) + "\" type=\"button\" class=\"ms-choice\">\n        <span class=\"placeholder\" title=\"\"></span>\n        <div></div>\n      </button>\n    </div>\n  ");
        setTimeout((/**
         * @return {?}
         */
        function () {
            console.log("🚀 ~ CustomInputFilter ~ buildTemplateHtmlString ~ elem:", elem);
            console.log('setTimeout');
            $('#filter-' + _this.grid.getUID() + _this.columnDef.id).click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                event.stopPropagation();
                event.preventDefault();
                // Close all other open dropdowns
                $('div[name^=filter-]').each((/**
                 * @param {?} index
                 * @param {?} item
                 * @return {?}
                 */
                function (index, item) {
                    if ($(item).css('display') === "block") {
                        $(item).hide();
                    }
                }));
                // Only toggle headers within this grid instance
                /** @type {?} */
                var $gridContainer = $('.' + _this.grid.getUID());
                $gridContainer.find('.slick-headerrow-columns-left, .slick-headerrow-columns-right').each((/**
                 * @return {?}
                 */
                function () {
                    /** @type {?} */
                    var $header = $(this);
                    /** @type {?} */
                    var $parent = $header.parent();
                    /** @type {?} */
                    var isHeaderVisible = $header.is(':visible');
                    /** @type {?} */
                    var isParentVisible = $parent.is(':visible');
                    /** @type {?} */
                    var isVisible = isHeaderVisible && isParentVisible;
                    if (isVisible) {
                        $header.hide();
                        $parent.hide();
                        $header.data('visible', false);
                    }
                    else {
                        $header.show();
                        $parent.show();
                        $header.data('visible', true);
                    }
                }));
                // Focus the search input if visible
                /** @type {?} */
                var searchInput = $("#search-filter-" + (_this.grid.getUID() + _this.columnDef.id));
                if (searchInput.length && searchInput.is(':visible')) {
                    searchInput.focus();
                }
            }));
        }), 0);
        return "\n    <input id=\"search-filter-" + (this.grid.getUID() + this.columnDef.id) + "\" type=\"text\" class=\"form-control search-filter\" placeholder=\"" + placeholder + "\">\n  ";
    };
    /**
     * From the html template string, create a DOM element
     * @param filterTemplate
     */
    /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    CustomInputFilter.prototype.createDomElement = /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    function (filterTemplate, searchTerm) {
        /** @type {?} */
        var $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        $($headerElm).empty();
        // create the DOM element & add an ID and filter class
        /** @type {?} */
        var $filterElm = $(filterTemplate);
        $filterElm.val(searchTerm);
        $filterElm.data('columnId', this.columnDef.id);
        // append the new DOM element to the header row
        if ($filterElm && typeof $filterElm.appendTo === 'function') {
            $filterElm.appendTo($headerElm);
        }
        return $filterElm;
    };
    return CustomInputFilter;
}());
export { CustomInputFilter };
if (false) {
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype._clearFilterTriggered;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype.$filterElm;
    /** @type {?} */
    CustomInputFilter.prototype.grid;
    /** @type {?} */
    CustomInputFilter.prototype.searchTerms;
    /** @type {?} */
    CustomInputFilter.prototype.columnDef;
    /** @type {?} */
    CustomInputFilter.prototype.callback;
    /** @type {?} */
    CustomInputFilter.prototype.operator;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype.clientSideFilter;
}
//# sourceMappingURL=data:application/json;base64,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