/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Injectable, Input, Output, ViewChild } from '@angular/core';
import { UIComponent } from "./UIComponent.service";
import { CommonService } from "../utils/common.service";
import { Logger } from "../../../com/swallow/logging/logger.service";
import { genericEvent } from "../../../com/swallow/events/swt-events.module";
import { LinkItemRander } from "../renderers/advancedDataGridRendres/link-item-render.component";
import { StringItemRender } from "../renderers/advancedDataGridRendres/string-item-render.component";
import { NumberItemRender } from "../renderers/advancedDataGridRendres/number-item-render.component";
import { Types } from "../renderers/advancedDataGridRendres/types";
import ResizeObserver from 'resize-observer-polyfill';
/** @type {?} */
const $ = require('jquery');
require('jquery.fancytree/dist/modules/jquery.fancytree.table');
require('jquery.fancytree/dist/modules/jquery.fancytree.fixed');
require('jquery.fancytree/dist/modules/jquery.fancytree.gridnav');
export class AdvancedDataGrid extends UIComponent {
    /**
     * @param {?} elemnt
     * @param {?} commonS
     */
    constructor(elemnt, commonS) {
        super(elemnt.nativeElement, commonS);
        this.elemnt = elemnt;
        this.commonS = commonS;
        // input to set advancedDataGrid width.
        this.width = "";
        // array to store advancedDataGrid column definition.
        this.columnDefinition = [];
        // array to hold visible columns.
        this.visibleColumn = [];
        // variable to set header width.
        this.headerWidth = this.width;
        // variable to set tree width.
        this.treeWidth = 250;
        // variable to show/hide tree name in the dataGrid header
        this.showTreeHeader = false;
        // variable to store tree name;
        this.treeName = "";
        // public array to contain dataGrid Headers
        this.AdvancedDataGridHeaders = [];
        // event emitter to dispatch row click event.
        this.rowClick = new EventEmitter();
        // event emitter to dispatch row double click event.
        this.rowDbClick = new EventEmitter();
        // event emitter to dispatch item expand event.
        this.itemExpand = new EventEmitter();
        // event emitter to dispatch item collapse event.
        this.itemCollapse = new EventEmitter();
        // event emitter to dispatch activate event.
        this.activate = new EventEmitter();
        // variable to hold advanced data grid tree instance
        this._treeInstance = null;
        // private variable to be true if it's the first load of grid
        this._firstload = true;
        // private array to hold tree opened items.
        this.savedState = [];
        // private variable to define row height
        this._rowHeight = 22;
        // private variable to indicate that the grid have scroll bar or not.
        this.scrollable = true;
        this.isInternetExplorer = true;
        // private variable to handle scroll position.
        this._verticalScrollPosition = 0;
        // private variable to set advancedDataGrid height.
        this._height = "";
        // variable to store advancedDataGrid data.
        this._dataProvider = [];
        this.scrollableContentHeight = 0;
        this.advancedDatagridContentHeight = 0;
        this.rowHeight = -1;
        this.itemRenderList = [];
        this.log = new Logger("AdvancedDataGrid", this.commonS.httpclient);
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        /** @type {?} */
        const ro = new ResizeObserver((/**
         * @param {?} entries
         * @param {?} observer
         * @return {?}
         */
        (entries, observer) => {
            this.calculateDivHeight();
        }));
        ro.observe(this.treegrid.nativeElement);
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = value;
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @return {?}
     */
    calculateDivHeight() {
        /** @type {?} */
        let calculateDivHeight;
        // console.log($(".scrollable-content")[.height()] , $(".advancedDatagrid-content").height());
        this.scrollableContentHeight = $('.scrollable-content')[0].clientHeight;
        this.advancedDatagridContentHeight = $('.advancedDatagrid-content')[0].clientHeight;
        if (this.advancedDatagridContentHeight >= this.scrollableContentHeight) {
            calculateDivHeight = 0;
        }
        else {
            calculateDivHeight = this.scrollableContentHeight - this.advancedDatagridContentHeight - 5;
        }
        $('.divTable').height(calculateDivHeight);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        try {
            this.initialize();
            if (this.isFirstLoad()) {
                this.treeData = value["Tree"];
                /** @type {?} */
                const columns = value["grid_data"].metadata.columns.column;
                this.recursive(this.treeData);
                if (this.treeData.children) {
                    this._dataProvider = this.treeData;
                }
                else {
                    this._dataProvider = [];
                }
                // get tree width if exists.
                if (this.treeData.width) {
                    this.treeWidth = this.treeData.width;
                }
                this.columnDefinition = [...columns];
                this.AdvancedDataGridHeaders = columns;
                this.columnDefinition.forEach((/**
                 * @param {?} column
                 * @return {?}
                 */
                (column) => {
                    if (column.visible) {
                        this.visibleColumn.push(column);
                    }
                }));
                this.paintColumns();
                this.tempWidth = "";
                this.tempWidth = this.headerWidth;
                if (this.showTreeHeader) {
                    this.treeName = this.treeData.title;
                }
                else {
                    this.treeName = "";
                }
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    this.init();
                }), 0);
                this._firstload = false;
            }
            else {
                for (let index = 0; index < this.itemRenderList.length; index++) {
                    this.itemRenderList[index].destroy();
                }
                this.itemRenderList = null;
                this.itemRenderList = [];
                /** @type {?} */
                const clone = this.clone(value["Tree"]);
                if (clone && clone.children) {
                    this.treeData = value["Tree"];
                    this.recursive(clone);
                    this._dataProvider = clone;
                    this.refresh();
                    if (this.savedState) {
                        this.openSavedTreeState();
                    }
                }
                else {
                    this._dataProvider = [];
                    this.refresh();
                }
            }
        }
        catch (error) {
            this.log.error('[ dataProvider ] METHOD ERROR: ', error);
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.setStyle("height", this.height + (this.height.toString().indexOf("%") === -1 ? "px" : ""));
        this.setStyle("width", this.width + (this.width.toString().indexOf("%") === -1 ? "px" : ""));
        this.elemnt.nativeElement.style.overflow = "hidden";
        $(((/** @type {?} */ (window)))).resize((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.paintColumns();
            this.synchronizeHeaderLayout();
            this.calculateDivHeight();
        }));
        this.isInternetExplorer = this.isIE();
    }
    /**
     * This method is used to paint advanced data grid columns
     * @private
     * @return {?}
     */
    paintColumns() {
        /** @type {?} */
        var __width = 0;
        /** @type {?} */
        var diff = 0;
        /** @type {?} */
        var colId = 0;
        try {
            for (let i = 0; i < this.visibleColumn.length; i++) {
                __width += Number(this.visibleColumn[i].width);
            }
            this.headerWidth = String(__width + this.treeWidth);
            if (Number(this.headerWidth) > Number($(this.elemnt.nativeElement).width())) {
                this.scrollable = true;
            }
            else {
                this.scrollable = false;
            }
            /** @type {?} */
            const columnNumber = this.visibleColumn.length;
            /** @type {?} */
            const scrollBarContentWidht = this.isInternetExplorer ? 4 : 2;
            colId = columnNumber - 1;
            if (Number(this.headerWidth) < Number($(this.elemnt.nativeElement).width())) {
                this.scrollable = false;
                diff = Number($(this.elemnt.nativeElement).width()) - Number(this.headerWidth) - (scrollBarContentWidht + this.getScrollbarWidth());
                this.headerWidth = String(Number(this.headerWidth) + diff);
            }
            else if (Number(this.headerWidth) > Number($(this.elemnt.nativeElement).width())) {
                if (Number(this.headerWidth) > this.tempWidth) {
                    diff = Number(this.headerWidth) - Number($(this.elemnt.nativeElement).width()) - 2;
                    this.scrollable = true;
                    this.visibleColumn[colId].width = Number(this.visibleColumn[columnNumber - 1].width) - diff;
                    if (Number($(this.elemnt.nativeElement).width() > Number(this.tempWidth))) {
                        this.headerWidth = String(Number(this.headerWidth) - diff);
                    }
                    else {
                        this.headerWidth = this.tempWidth;
                    }
                    $(".scrollable-content").width(this.tempWidth);
                    $("#" + colId).width(this.visibleColumn[columnNumber - 1].width);
                }
            }
        }
        catch (error) {
            this.log.error("paintColumns error: ", error);
        }
    }
    /**
     * You can specify a minimum width for the columns
     * Also then updates and saves the column's width to the database
     *
     * @private
     * @return {?}
     */
    getScrollbarWidth() {
        /** @type {?} */
        var outer = document.createElement("div");
        outer.style.visibility = "hidden";
        outer.style.width = "100px";
        outer.style.msOverflowStyle = "scrollbar"; // needed for WinJS apps
        document.body.appendChild(outer);
        /** @type {?} */
        var widthNoScroll = outer.offsetWidth;
        // force scrollbars
        outer.style.overflow = "scroll";
        // add innerdiv
        /** @type {?} */
        var inner = document.createElement("div");
        inner.style.width = "100%";
        outer.appendChild(inner);
        /** @type {?} */
        var widthWithScroll = inner.offsetWidth;
        // remove divs
        outer.parentNode.removeChild(outer);
        return widthNoScroll - widthWithScroll;
    }
    /**
     * @return {?}
     */
    getTreeStates() {
        return this.savedState;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    setTreeStates(value) {
        this.savedState = value;
    }
    /**
     * This method is used to get tree instance.
     * @return {?}
     */
    getTreeInstance() {
        if (this._treeInstance) {
            return this._treeInstance.fancytree("getTree");
        }
        else {
            return null;
        }
    }
    /**
     * This method is used to set metaData to advanced dataGrid instance.
     * @param {?} metadata
     * @return {?}
     */
    setAdvancedGridMetaData(metadata) {
        try {
            this.visibleColumn = [];
            metadata.forEach((/**
             * @param {?} column
             * @return {?}
             */
            (column) => {
                if (column.visible) {
                    this.visibleColumn.push(column);
                }
            }));
            this.dataProvider = this.treeData;
            this.getTreeInstance().reload(this.treeData);
        }
        catch (error) {
            this.log.error("setGridMetaData - error: ", error);
        }
    }
    /**
     * This method is used to get capture of the
     * current tree state and save it in the
     * saved tree state array.
     * @return {?}
     */
    saveOpenTreeState() {
        try {
            if (this.getTreeInstance()) {
                this.savedState = this.getTreeInstance().findAll((/**
                 * @param {?} node
                 * @return {?}
                 */
                (node) => {
                    return node.expanded === true;
                }));
            }
            return this.savedState;
        }
        catch (error) {
            this.log.error("saveOpenTreeState - error: ", error);
        }
    }
    /**
     * This method is used to open saved tree state
     * or open a given state.
     * @param {?=} state
     * @return {?}
     */
    openSavedTreeState(state) {
        /** @type {?} */
        var _state = null;
        try {
            if (state) {
                _state = state;
            }
            else {
                _state = this.savedState;
            }
            /** @type {?} */
            const tree = this.getTreeInstance();
            if (_state && _state.length > 0) {
                _state.forEach((/**
                 * @param {?} node
                 * @return {?}
                 */
                (node) => {
                    /** @type {?} */
                    const savedNode = tree.getNodeByKey(node.key);
                    if (savedNode) {
                        savedNode.setExpanded(true);
                    }
                }));
            }
            this.savedState = _state;
        }
        catch (error) {
            this.log.error("openSavedTreeState - error: ", error);
        }
    }
    /**
     * get all the advancedDataGrid meta data if index is undefined
     * else return the metaData in the given index.
     * @param {?=} index
     * @return {?}
     */
    getAdvancedGridMetaData(index) {
        if (index) {
            return this.columnDefinition[index];
        }
        else {
            return this.columnDefinition;
        }
    }
    /**
     * @param {?} value
     * @return {?}
     */
    setConnectors(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            $(".fancytree-container").toggleClass("fancytree-connectors");
        }), 0);
    }
    /**
     * This method is used to handle double click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} data
     * @return {?}
     */
    advancedDataGridRowDbClick(event, data) {
        try {
            /** @type {?} */
            const node = data.node;
            /** @type {?} */
            const selectedRow = new AdvancedDataGridRow(node, this.commonS);
            selectedRow.node = node;
            this.rowDbClick.emit(selectedRow);
            if (this.eventlist[genericEvent.ROW_DBCLICK]) {
                this.eventlist[genericEvent.ROW_DBCLICK](selectedRow);
            }
        }
        catch (error) {
            this.log.error("advancedDataGridRowDbClick - error: ", error);
        }
    }
    /**
     * This method is used to handle click event
     * in advanced DataGrid.
     * @param {?} event
     * @param {?} row
     * @return {?}
     */
    advancedDataGridRowClick(event, row) {
        try {
            /** @type {?} */
            let searchIDs = this.getTreeInstance().getSelectedNodes();
            searchIDs.forEach((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                node.setSelected(false);
                node.selected = false;
                node.render();
            }));
            // Update selected row.
            this._selectedRow = row;
            this.rowClick.emit(row);
            if (this.eventlist[genericEvent.ROW_CLICK]) {
                this.eventlist[genericEvent.ROW_CLICK](row);
            }
            if (Number(event.target.id) > -1) {
                if (this.eventlist[genericEvent.CELL_CLICK]) {
                    /** @type {?} */
                    const cell = (/** @type {?} */ (row.getCellAt(Number(event.target.id))));
                    cell.setParentRow(row);
                    cell.columnHeader = this.visibleColumn[Number(event.target.id)];
                    this.eventlist[genericEvent.CELL_CLICK](cell);
                }
            }
            /** @type {?} */
            const node = this.getTreeInstance().getNodeByKey(row.key);
            node.setSelected(true);
        }
        catch (error) {
            this.log.error("advancedDataGridRowClick - error: ", error);
        }
    }
    /**
     * This method called on each group item render.
     * @param {?} row
     * @return {?}
     */
    onGroupItemRender(row) {
        try {
        }
        catch (error) {
            this.log.error("onGroupItemRender - error: ", error);
        }
    }
    /**
     * This method is used to get the selected row.
     * @return {?}
     */
    getSelectedRow() {
        return this._selectedRow;
    }
    /**
     * This method is used to set selected row.
     * @param {?} value
     * @return {?}
     */
    setSelectedRow(value) {
        try {
            this._selectedRow = value;
            if (value) {
                /** @type {?} */
                const node = this.getTreeInstance().getNodeByKey(value.key);
                if (node) {
                    try {
                        node.setSelected(true);
                    }
                    catch (error) {
                        console.log(error);
                    }
                }
            }
        }
        catch (error) {
            this.log.error("setSelectedRow - error: ", error);
        }
    }
    /**
     * This method is used to get the selected column.
     * @return {?}
     */
    getSelectedColumn() {
        return this._selectedColumn;
    }
    /**
     * This method is used to get the selected
     * cell.
     * @return {?}
     */
    getSelectedCell() {
        return this._selectedCell;
    }
    /**
     * Collapses all the nodes of the navigation tree.
     * @return {?}
     */
    collapseAll() {
        try {
            if (this.getTreeInstance()) {
                this.getTreeInstance().expandAll(false);
                this.synchronizeHeaderLayout();
            }
        }
        catch (error) {
            this.log.error("collapseAll - error: ", error);
        }
    }
    /**
     *  Expands all the nodes of the navigation tree in the control.
     * @return {?}
     */
    expandAll() {
        try {
            if (this.getTreeInstance()) {
                this.getTreeInstance().expandAll(true);
                this.synchronizeHeaderLayout();
            }
        }
        catch (error) {
            this.log.error("expandAll - error: ", error);
        }
    }
    /**
     * @param {?} source
     * @return {?}
     */
    clone(source) {
        if (Object.prototype.toString.call(source) === '[object Array]') {
            /** @type {?} */
            const clone = [];
            for (let i = 0; i < source.length; i++) {
                clone[i] = this.clone(source[i]);
            }
            return clone;
        }
        else if (typeof (source) === "object") {
            /** @type {?} */
            const clone = {};
            for (const prop in source) {
                if (source.hasOwnProperty(prop)) {
                    clone[prop] = this.clone(source[prop]);
                }
            }
            return clone;
        }
        else {
            return source;
        }
    }
    /**
     * @return {?}
     */
    refresh() {
        try {
            // reload the tree data.
            this.getTreeInstance().reload(this.dataProvider);
            // repaint the grid view.
            this.synchronizeHeaderLayout();
        }
        catch (error) {
            this.log.error("refresh - error: ", error);
        }
    }
    /**
     *  Opens or closes all the nodes of the navigation tree below the specified item.
     * @param {?} item
     * @param {?} open
     * @return {?}
     */
    expandChildrenOf(item, open) {
    }
    /**
     *  Opens or closes a branch node of the navigation tree.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    expandItem(item, open, animate = false, dispatchEvent = false, cause = null) {
    }
    /**
     *  Returns the parent of a child item.
     * @param {?} item
     * @return {?}
     */
    getParentItem(item) {
    }
    /**
     *  Returns true if the specified branch node is open.
     * @param {?} item
     * @return {?}
     */
    isItemOpen(item) {
    }
    /**
     *  Sets the associated icon in the navigation tree for the item.
     * @param {?} item
     * @param {?} iconID
     * @param {?} iconID2
     * @return {?}
     */
    setItemIcon(item, iconID, iconID2) {
    }
    /**
     * This method returns true if is the first load of
     * grid.
     * @return {?}
     */
    isFirstLoad() {
        return this._firstload;
    }
    /**
     * @private
     * @param {?} mainObj
     * @return {?}
     */
    deepCopy(mainObj) {
        /** @type {?} */
        const objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        let key;
        for (key in mainObj) {
            if (mainObj.hasOwnProperty(key)) {
                objCopy[key] = mainObj[key]; // copies each property to the objCopy object
            }
        }
        return objCopy;
    }
    /**
     * This method is used to synchronize the position
     * of headers with the corresponding column
     * @private
     * @return {?}
     */
    synchronizeHeaderLayout() {
        try {
            $("tr.unselectable_r").remove();
            /** @type {?} */
            const headerheigth = Number($(".head")[0].clientHeight);
            $(".scrollable-content").height(Number($(this.elemnt.nativeElement).height()) - headerheigth - 15 + "px");
            $('.scroller').height($(".advancedDatagrid-content").height());
            /** @type {?} */
            let localHeigt = $($('.fancytree-has-children')[0]).outerHeight();
            this.rowHeight = localHeigt;
            if (this.isInternetExplorer) {
                $($($(this.elemnt.nativeElement).find('.advancedTreeDivItem'))[0])[0].style.backgroundImage = '-ms-repeating-linear-gradient( -90deg, white, white ' + (localHeigt - 1) + 'px,#DFDFDF ' + (localHeigt - 1) + 'px, #DFDFDF ' + (localHeigt) + 'px, #E0F0FF ' + (localHeigt) + 'px, #E0F0FF ' + (localHeigt * 2 - 1) + 'px,#DFDFDF ' + (localHeigt * 2 - 1) + 'px, #DFDFDF ' + (localHeigt * 2) + 'px)';
            }
            else {
                $($($(this.elemnt.nativeElement).find('.advancedTreeDivItem'))[0])[0].style.backgroundImage = 'repeating-linear-gradient( 180deg, white, white ' + (this.rowHeight - 1) + 'px,#DFDFDF ' + (this.rowHeight - 1) + 'px, #DFDFDF ' + (this.rowHeight) + 'px, #E0F0FF ' + (this.rowHeight) + 'px, #E0F0FF ' + (this.rowHeight * 2 - 1) + 'px,#DFDFDF ' + (this.rowHeight * 2 - 1) + 'px, #DFDFDF ' + (this.rowHeight * 2) + 'px)';
            }
            $('#advancedTreeNoDataBody').empty();
            $('#advancedTreeNoDataBody').append("<div class='divTableCell'  style=' height:100%;width: " + this.treeWidth + "px; border: 1px solid #DFDFDF'></div>");
            for (let i = 0; i < this.visibleColumn.length; i++) {
                $('#advancedTreeNoDataBody').append("<div class='divTableCell' style=' height:100%;width: " + (i < this.visibleColumn.length - 1 ? (this.visibleColumn[i].width - 1) : (this.visibleColumn[i].width - 2)) + "px; border-right: 1px solid #DFDFDF'></div>");
            }
            this.paintColumns();
        }
        catch (e) {
            this.log.error("synchronizeHeaderLayout - error :", e);
        }
    }
    /**
     * @return {?}
     */
    get verticalScrollPosition() {
        return this._verticalScrollPosition;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set verticalScrollPosition(value) {
        $(".scrollable-content").animate({ scrollTop: value }, 0);
        this._verticalScrollPosition = value;
    }
    /**
     * @private
     * @return {?}
     */
    isIE() {
        /** @type {?} */
        let ua = navigator.userAgent;
        /* MSIE used to detect old browsers and Trident used to newer ones*/
        /** @type {?} */
        var is_ie = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;
        return is_ie;
    }
    /**
     * @private
     * @param {?} c
     * @return {?}
     */
    componentToHex(c) {
        /** @type {?} */
        const hex = c.toString(16);
        return hex.length === 1 ? "0" + hex : hex;
    }
    /**
     * @private
     * @param {?} r
     * @param {?} g
     * @param {?} b
     * @return {?}
     */
    rgbToHex(r, g, b) {
        return "#" + this.componentToHex(r) + this.componentToHex(g) + this.componentToHex(b);
    }
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @return {?}
     */
    recursive(value) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            // test if value is an object.
            if (value.length === undefined) {
                value = [...[value]];
            }
            errorLocation = 10;
            // loop to data
            for (let index = 0; index < value.length; index++) {
                /** @type {?} */
                const leaf = value[index];
                // loop leaf keys.
                for (const key in leaf) {
                    if (leaf.hasOwnProperty(key)) {
                        if (leaf[key] === "true") {
                            leaf[key] = true;
                        }
                        else if (leaf[key] === "false") {
                            leaf[key] = false;
                        }
                        if (typeof (leaf[key]) === 'object') {
                            if (!leaf[key].length) {
                                leaf[key] = [leaf[key]];
                            }
                            this.recursive(leaf[key]);
                        }
                    }
                }
            }
        }
        catch (error) {
            this.log.error('[ recursive ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
        return value;
    }
    /**
     * This method is used to initialize params to default values.
     * @return {?}
     */
    initialize() {
        this.setSelectedRow(null);
    }
    /**
     * @private
     * @return {?}
     */
    init() {
        try {
            this._treeInstance = ((/** @type {?} */ ($('.advancedDatagrid-content')))).fancytree({
                extensions: ['table'],
                checkbox: false,
                table: {
                    indentation: 20,
                    // indent 20px per node level
                    nodeColumnIdx: 0,
                    // render the node title into the 2nd column
                    checkboxColumnIdx: 0 // render the checkboxes into the 1st column
                },
                source: this.dataProvider,
                tooltip: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    return data.node.data.title;
                }),
                lazyLoad: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                }),
                renderColumns: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    /** @type {?} */
                    const tdList = $(node.tr).find('>td');
                    /** @type {?} */
                    const row = new AdvancedDataGridRow(node, this.commonS);
                    row.itemRenderListFromParent = this.itemRenderList;
                    row.node = node;
                    row.createCells(node, this.visibleColumn);
                    this.onGroupItemRender(row);
                    if (this.groupItemRenderer) {
                        this.groupItemRenderer(row);
                    }
                    $(node.tr).click((/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        if (!$(event.target).hasClass("fancytree-expander")) {
                            this.advancedDataGridRowClick(event, row);
                        }
                    }));
                    $(node.tr).dblclick((/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        this.advancedDataGridRowDbClick(event, data);
                    }));
                }),
                activate: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    /** @type {?} */
                    const node = data.node;
                    // const selectedRow = new AdvancedDataGridRow($(node.tr)[0], this.commonS);
                    /** @type {?} */
                    const selectedRow = new AdvancedDataGridRow(node, this.commonS);
                    selectedRow.node = node;
                    selectedRow.createCells(node, this.visibleColumn);
                    this.activate.emit(selectedRow);
                    if (this.eventlist[genericEvent.ACTIVATE]) {
                        this.eventlist[genericEvent.ACTIVATE](selectedRow);
                    }
                }),
                expand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    this.synchronizeHeaderLayout();
                }),
                collapse: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                    this.synchronizeHeaderLayout();
                }),
                beforeExpand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                (event, data) => {
                })
            });
            // synchronize the dataGrid view.
            this.synchronizeHeaderLayout();
            // a helper variable
            /** @type {?} */
            var timeout;
            $('.scroller-container, .scrollable-content').on("scroll", (/**
             * @return {?}
             */
            function callback() {
                // clear the 'timeout' every 'scroll' event call
                // to prevent re-assign 'scroll' event to other element
                // before finished scrolling
                clearTimeout(timeout);
                // get the used elements
                /** @type {?} */
                const source = $(this);
                /** @type {?} */
                const target = $(source.is(".scroller-container") ? '.scrollable-content' : '.scroller-container');
                /** @type {?} */
                const scrollPoisition = source.scrollTop();
                this._verticalScrollPosition = scrollPoisition;
                // remove the callback from the other 'div' and set the 'scrollTop'
                target.off("scroll").scrollTop(scrollPoisition);
                // create a new 'timeout' and reassign 'scroll' event
                // to other 'div' on 100ms after the last event call
                timeout = setTimeout((/**
                 * @return {?}
                 */
                function () {
                    target.on("scroll", callback);
                }), 100);
            }));
        }
        catch (error) {
            this.log.error('[ init ] METHOD ERROR: ', error);
        }
    }
}
AdvancedDataGrid.decorators = [
    { type: Component, args: [{
                selector: 'AdvancedDataGrid',
                template: `
        <div class = "advancedTreeDivItem" style="overflow-x: scroll; overflow-y: hidden; border: 1px solid #cccccc; width: calc(100% - 17px)"
             [style.height.%]="100">

            <table   class="head" [style.width.px]="headerWidth" [class.default]="visibleColumn.length === 0">
                <colgroup>
                    <col [style.width.px]="treeWidth"/>
                    <col *ngFor="let column of visibleColumn"  [style.width.px]="column.width"/>
                </colgroup>
                <thead>
                <tr class="advancedDataGridHeader">
                    <th class="header-column" style="border-left: 1px solid #529FED" [style.width.px]="treeWidth">{{ treeName }}</th>
                    <ng-container *ngFor="let column of visibleColumn let colindex = index">
                        <th class="header-column" *ngIf="column.visible" [style.width.px]="column.width" [class.lastcolumn-border]="(colindex === visibleColumn.length - 1)">{{ column.heading }}</th>
                    </ng-container>
                </tr>
                </thead>
            </table>
            <div  class="scrollable-content"  style="overflow-x: hidden; overflow-y: auto;" [style.width.px]="headerWidth">
                <table  #treegrid class="advancedDatagrid-content" [style.width.px]="headerWidth">
                    <tbody class="advancedtree-body">
                    <tr>
                        <td style="padding-left: 3px" [style.width.px]="treeWidth"></td>
                        <ng-container *ngFor="let column of visibleColumn let colIndex = index">
                            <td class="header-column-r" [id]="colIndex" [style.width.px]="column.width" *ngIf="column.visible"></td>
                        </ng-container>
                    </tr>
                    </tbody>
                </table>
                <div class="divTable"  style="width:100%">
                    <div class="divTableBody">
                        <div id="advancedTreeNoDataBody" class="divTableRow">

                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-bar" style="display: block; border: none">
            <div class="header-end"
                 style="height: 26px; background-color: #529FED; border-top: 1px solid #CCC; border-left: none; width: 100%"></div>
            <div class="scroller-container"
                 style="width: 100%; height: calc(100% - 23px); overflow-y: scroll; overflow-x: hidden">
                <div class="scroller" style="width: 1px;"></div>
            </div>
        </div>
    `,
                styles: [`
        :host {
            display: flex;
            overflow: auto;
            background-color: #FFF;
        }
        .scroll-bar {
            width: 17px;
            height: calc(100% - 17px);
        }
        @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
            .scroll-bar {
                width: 19px !important;
            }
        }

        .advancedTreeDivItem table td, table th{
            padding : 2px;
        }

        .scrollable-content::-webkit-scrollbar {
            width: 0 !important; /* Remove scrollbar space */
            background: transparent !important; /* Optional: just make scrollbar invisible */
            -ms-overflow-style: none !important; /* hide scroll bar for IE*/
            overflow: -moz-scrollbars-none !important;
            display: none !important;
        }

        .scrollable-content {
            -ms-overflow-style: none;
            scrollbar-width: none;  // Firefox
        }

        .advancedDatagrid-content {
            width: 100%;
            border: 1px solid #DFDFDF;
            overflow: hidden;
        }

        .advancedDatagrid-content:focus {
            outline: none;
        }

        /* class to set the last border darkblue*/
        .lastcolumn-border {
            border-right: 1px solid #529FED !important;
        }

        .default {
            width: 100%;
        }

        thead tr {
            height: 25px;
            background-color: #529FED;
            color: #FFFFFF;
            font-size: 11px!important;
            font-weight: bold!important;
            font-family: Verdana, Helvatica !important;
        }

        /* provide some minimal visual accomodation for IE8 and below */
        .advancedtree-body tr {
            background: #FFFFFF;
            border-top: 1px solid #DFDFDF;
            border-bottom: 1px solid #DFDFDF;
            text-overflow: clip;
            white-space: nowrap;
            overflow: hidden;
        }

        /*  Define the background color for all the ODD background rows  */
        .advancedtree-body tr:nth-child(odd) {
            background: #FFFFFF;
        }

        /*  Define the background color for all the EVEN background rows  */
        .advancedtree-body tr:nth-child(even) {
            background: #E0F0FF;
        }

        table.fancytree-ext-table tbody tr.fancytree-active {
            background-color: #FFCC66 !important;
        }
        .fancytree-selected {
            background-color: #FFCC66 !important;
        }
        .span.fancytree-title {
            color:black !important;
        }

        .fancytree-active span.fancytree-title {
            background-color: transparent !important;
        }

        .header-column {
            border-left: 1px solid #FFFFFF;
            border-right: 1px solid #FFFFFF;
            padding-left: 10px;
            text-align: center;
        }

        .head {
            table-layout: fixed;
        }
        .header-column-r {
            border: 1px solid #DFDFDF;
            padding: 0 0 0 0;
            margin: 0px;
        }



    `]
            }] }
];
/** @nocollapse */
AdvancedDataGrid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
AdvancedDataGrid.propDecorators = {
    treegrid: [{ type: ViewChild, args: ['treegrid',] }],
    width: [{ type: Input, args: ['width',] }],
    rowClick: [{ type: Output, args: ["rowClick",] }],
    rowDbClick: [{ type: Output, args: ["rowDbClick",] }],
    itemExpand: [{ type: Output, args: ["itemExpand",] }],
    itemCollapse: [{ type: Output, args: ["itemCollapse",] }],
    activate: [{ type: Output, args: ["activate",] }],
    height: [{ type: Input }],
    dataProvider: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    AdvancedDataGrid.prototype.treegrid;
    /** @type {?} */
    AdvancedDataGrid.prototype.width;
    /** @type {?} */
    AdvancedDataGrid.prototype.columnDefinition;
    /** @type {?} */
    AdvancedDataGrid.prototype.visibleColumn;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeData;
    /** @type {?} */
    AdvancedDataGrid.prototype.headerWidth;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeWidth;
    /** @type {?} */
    AdvancedDataGrid.prototype.showTreeHeader;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeName;
    /** @type {?} */
    AdvancedDataGrid.prototype.displayDisclosureIcon;
    /** @type {?} */
    AdvancedDataGrid.prototype.displayItemsExpanded;
    /** @type {?} */
    AdvancedDataGrid.prototype.firstVisibleItem;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupIconFunction;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupItemRenderer;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupLabelFunction;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupRowHeight;
    /** @type {?} */
    AdvancedDataGrid.prototype.groupedColumns;
    /** @type {?} */
    AdvancedDataGrid.prototype.hierarchicalCollectionView;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemIcons;
    /** @type {?} */
    AdvancedDataGrid.prototype.lockedColumnCount;
    /** @type {?} */
    AdvancedDataGrid.prototype.lockedRowCount;
    /** @type {?} */
    AdvancedDataGrid.prototype.rendererProviders;
    /** @type {?} */
    AdvancedDataGrid.prototype.selectedCells;
    /** @type {?} */
    AdvancedDataGrid.prototype.treeColumn;
    /** @type {?} */
    AdvancedDataGrid.prototype.AdvancedDataGridHeaders;
    /** @type {?} */
    AdvancedDataGrid.prototype.rowClick;
    /** @type {?} */
    AdvancedDataGrid.prototype.rowDbClick;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemExpand;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemCollapse;
    /** @type {?} */
    AdvancedDataGrid.prototype.activate;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedColumn;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedRow;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._selectedCell;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._treeInstance;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._firstload;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.savedState;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._rowHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.scrollable;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.y;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.tempWidth;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.isInternetExplorer;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._verticalScrollPosition;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._height;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.scrollableContentHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.advancedDatagridContentHeight;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.rowHeight;
    /** @type {?} */
    AdvancedDataGrid.prototype.itemRenderList;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.elemnt;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGrid.prototype.commonS;
}
export class AdvancedDataGridRow extends UIComponent {
    /**
     * @param {?} adelement
     * @param {?} commonServ
     */
    constructor(adelement, commonServ) {
        super($(adelement.tr)[0], commonServ);
        this.adelement = adelement;
        this.commonServ = commonServ;
        // private variable to store advancedDataGridTree node
        this.node = null;
        // array to contain the list of cell of this row.
        this._cellList = [];
        // variable to hold parent
        this._parent = null;
        // private variable set row state expanded / collapsed
        this._expanded = false;
        // private variable to know if row is selected or not.
        this._selected = false;
        // private variable to hold row toolTip.
        this._toolTip = "";
        this.itemRenderListFromParent = [];
        // private variable to hold row title
        this._title = "";
        // private variable to hold row icon
        this._icon = "";
        // private variable to hold row key
        this._key = "";
        // private variable to hold row icon type folder.
        this._folder = false;
        this.node = adelement;
        this._rowData = adelement.data;
        this._title = adelement.title;
        this._icon = adelement.icon;
        this._key = this.adelement.key;
        this._folder = adelement.folder;
        this._expanded = adelement.expanded;
        this._selected = adelement.selected;
        this._toolTip = adelement.tooltip;
        this._children = adelement.children;
    }
    /**
     * @return {?}
     */
    get title() {
        return this._title;
    }
    /**
     * set advancedDataGridRow title.
     * @param {?} value
     * @return {?}
     */
    set title(value) {
        this._title = value;
    }
    /**
     * @return {?}
     */
    get icon() {
        return this._icon;
    }
    /**
     * set advancedDataGridRow icon.
     * @param {?} value
     * @return {?}
     */
    set icon(value) {
        this._icon = value;
    }
    /**
     * @return {?}
     */
    get key() {
        return this._key;
    }
    /**
     * set advancedDataGridRow key.
     * @param {?} value
     * @return {?}
     */
    set key(value) {
        this._key = value;
    }
    /**
     * @return {?}
     */
    get folder() {
        return this._folder;
    }
    /**
     * set advancedDataGridRow icon type.
     * @param {?} value
     * @return {?}
     */
    set folder(value) {
        this._folder = value;
    }
    /**
     * read only property.
     * @return {?}
     */
    get width() {
        return this._width;
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        if (value && typeof value === "number") {
            this._height = value;
            this.setStyle("height", value + "px");
        }
    }
    /**
     * this method return is the current row is expanded or not.
     * @return {?}
     */
    isExpanded() {
        return this._expanded;
    }
    /**
     * This method return if the current row is selected or not.
     * @return {?}
     */
    isSelected() {
        return this._selected;
    }
    /**
     * This method is used to get a given attribute from
     * selected row data.
     * @param {?} attr
     * @return {?}
     */
    getDataAttribute(attr) {
        return this.getRowData()[attr];
    }
    /**
     * This method is used to create AdvancedDataGridCell
     * Do not use this method because it is used internally.
     * @param {?} node
     * @param {?} columns
     * @return {?}
     */
    createCells(node, columns) {
        try {
            /** @type {?} */
            var type;
            /** @type {?} */
            var itemRander;
            /** @type {?} */
            var startCellRenderIndex = 1;
            this.node = node;
            this._rowData = node.data;
            /** @type {?} */
            const tds = $(node.tr).find('>td');
            for (let i = 0; i < tds.length - 1; i++) {
                // create new cell object.
                /** @type {?} */
                const cell = new AdvancedDataGridCell(tds[startCellRenderIndex], this.commonServ);
                // set cell parent row.
                cell.setParentRow(this);
                // set cell column header.
                cell.columnHeader = columns[i];
                // get column type.
                if (columns[i].type) {
                    type = columns[i].type;
                }
                else {
                    this.log.warn("Column shoold contain field type to display renders.");
                }
                // render cells with the specific item render.
                if (type === Types.LINK_NUM) {
                    itemRander = cell.renderCell(LinkItemRander);
                    ((/** @type {?} */ (itemRander))).text = ('' + this.getRowData()[columns[i].dataelement]).replace(new RegExp('\\$', 'g'), '');
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.LINK_NUM;
                }
                else if (type === Types.STR) {
                    itemRander = cell.renderCell(StringItemRender);
                    ((/** @type {?} */ (itemRander))).text = this.getRowData()[columns[i].dataelement];
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.STR;
                }
                else if (type === Types.NUM) {
                    itemRander = cell.renderCell(NumberItemRender);
                    ((/** @type {?} */ (itemRander))).text = ('' + this.getRowData()[columns[i].dataelement]).replace(new RegExp('\\$', 'g'), '');
                    ;
                    ((/** @type {?} */ (itemRander))).id = i;
                    ((/** @type {?} */ (itemRander))).type = Types.NUM;
                }
                else if (type === Types.DATE) {
                }
                else if (type === Types.CHECKBOX) { // checkbox
                }
                else if (type === Types.RADIO) {
                }
                else if (type === Types.COMBO) {
                }
                else if (type === Types.TIME) {
                }
                else if (type === Types.INPUT) {
                }
                this._cellList.push(cell);
                startCellRenderIndex++;
            }
        }
        catch (error) {
            this.log.error("createCells - error: ", error);
        }
    }
    /**
     * This method will return the parent of selected
     * row.
     * @return {?}
     */
    getParentItem() {
        // limit to break the loop if no parent found.
        /** @type {?} */
        var loopLimit = 20;
        // variable to hold the root node.
        /** @type {?} */
        var rootNode = null;
        // if the node has a parent
        if (this.node.parent) {
            return new AdvancedDataGridRow(this.node.parent, this.commonServ);
        }
        else {
            // search for the root node and return it.
            rootNode = this.node;
            while (loopLimit > 0 && rootNode.title !== "root") {
                loopLimit--;
                rootNode = this.node.parent;
            }
            return new AdvancedDataGridRow(rootNode, this.commonServ);
        }
    }
    /**
     * @return {?}
     */
    getRowData() {
        return this._rowData;
    }
    /**
     * @return {?}
     */
    getCells() {
        try {
            return this._cellList;
        }
        catch (error) {
            this.log.error("getCells - error: ", error);
        }
    }
    /**
     * @param {?} index
     * @return {?}
     */
    getCellAt(index) {
        try {
            return this._cellList[index];
        }
        catch (error) {
            this.log.error("getCellAt - error: ", error);
        }
    }
    /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    expand() {
        try {
            this.node.setExpanded(true);
        }
        catch (error) {
            this.log.error("expand - error: ", error);
        }
    }
    /**
     * This method is used to collapse the
     * current item.
     * @return {?}
     */
    collapse() {
        try {
            this.node.setExpanded(false);
        }
        catch (error) {
            this.log.error("collapse - error: ", error);
        }
    }
}
AdvancedDataGridRow.decorators = [
    { type: Injectable }
];
/** @nocollapse */
AdvancedDataGridRow.ctorParameters = () => [
    { type: undefined },
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    AdvancedDataGridRow.prototype.node;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._cellList;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._rowData;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._parent;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._expanded;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._children;
    /** @type {?} */
    AdvancedDataGridRow.prototype.itemRenderListFromParent;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._title;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._icon;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._key;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._folder;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._width;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype._height;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype.adelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridRow.prototype.commonServ;
}
export class AdvancedDataGridCell extends UIComponent {
    /**
     * @param {?} cellelement
     * @param {?} comServ
     */
    constructor(cellelement, comServ) {
        super(cellelement, comServ);
        this.cellelement = cellelement;
        this.comServ = comServ;
        // private variable to store the item render instance.
        this._itemRander = null;
        // private variable to hold cell parent row.
        this._parentRow = null;
        // private variable to store cell column header.
        this._columnHeader = null;
    }
    /**
     * This method is used to set the cell column header.
     * @param {?} value
     * @return {?}
     */
    set columnHeader(value) {
        this._columnHeader = value;
    }
    /**
     * This method is used to return the parent row of the current cell.
     * @return {?}
     */
    getParentRow() {
        return this._parentRow;
    }
    /**
     * This method is used to set parent row for the current cell.
     * @param {?} row
     * @return {?}
     */
    setParentRow(row) {
        this._parentRow = row;
    }
    /**
     * This method is used to create rander in this
     * grid cell.
     * @param {?} itemRender
     * @return {?}
     */
    renderCell(itemRender) {
        $(this.cellelement).empty();
        // $(this.cellelement).html(itemRender as any);
        try {
            // Create a component reference from the component
            /** @type {?} */
            const componentRef = this.comServ.componentFactoryResolver
                .resolveComponentFactory((/** @type {?} */ (itemRender)))
                .create(this.comServ.injector);
            // Attach component to the appRef so that it's inside the ng component tree
            this.comServ.applicationRef.attachView(componentRef.hostView);
            // Get DOM element from component
            /** @type {?} */
            const domElem = (/** @type {?} */ (((/** @type {?} */ (componentRef.hostView)))
                .rootNodes[0]));
            // Append DOM element to the body
            // document.body.appendChild(domElem);
            $(this.cellelement).html(domElem);
            this._itemRander = componentRef.instance;
            this._parentRow.itemRenderListFromParent.push(componentRef);
            return componentRef.instance;
        }
        catch (error) {
            this.log.error("renderCell - error: ", error);
        }
    }
    /**
     * This method is used to get the cell item render.
     * @return {?}
     */
    getItemRander() {
        return this._itemRander;
    }
    /**
     * This method is used to get the cell column header.
     * @return {?}
     */
    getColumnHeader() {
        return this._columnHeader;
    }
}
AdvancedDataGridCell.decorators = [
    { type: Injectable }
];
/** @nocollapse */
AdvancedDataGridCell.ctorParameters = () => [
    { type: undefined },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._itemRander;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._parentRow;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype._columnHeader;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype.cellelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridCell.prototype.comServ;
}
export class AdvancedDataGridColumn extends UIComponent {
    /**
     * @param {?} adelement
     * @param {?} commonServ
     */
    constructor(adelement, commonServ) {
        super(adelement, commonServ);
        this.adelement = adelement;
        this.commonServ = commonServ;
        // private variable to store column index.
        this._index = null;
    }
    /**
     * This method is used to return the index of column
     * in the advancedDataGrid.
     * @return {?}
     */
    get index() {
        return this._index;
    }
    /**
     * This method is used to get the metaData of this column.
     * @return {?}
     */
    get metaData() {
        return this._metaData;
    }
    /**
     * This method is used to set new metaData to current column.
     * @param {?} value
     * @return {?}
     */
    set metaData(value) {
        this._metaData = value;
    }
    /**
     * This method is used to get the list of cell in the current
     * column.
     * @return {?}
     */
    getCells() {
        return this._listCell;
    }
    /**
     * This method is used to get the cell in the given index.
     * @param {?} index
     * @return {?}
     */
    getCellAt(index) {
        return this._listCell[index];
    }
}
AdvancedDataGridColumn.decorators = [
    { type: Injectable }
];
/** @nocollapse */
AdvancedDataGridColumn.ctorParameters = () => [
    { type: undefined },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._listCell;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._index;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype._metaData;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype.adelement;
    /**
     * @type {?}
     * @private
     */
    AdvancedDataGridColumn.prototype.commonServ;
}
/**
 * Interface to declare all attribute of advancedDataGrid.
 * @record
 */
export function IAdvancedDataGrid() { }
if (false) {
    /** @type {?} */
    IAdvancedDataGrid.prototype.displayDisclosureIcon;
    /** @type {?} */
    IAdvancedDataGrid.prototype.displayItemsExpanded;
    /** @type {?} */
    IAdvancedDataGrid.prototype.firstVisibleItem;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupedColumns;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupIconFunction;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupItemRenderer;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupLabelFunction;
    /** @type {?} */
    IAdvancedDataGrid.prototype.groupRowHeight;
    /** @type {?} */
    IAdvancedDataGrid.prototype.hierarchicalCollectionView;
    /** @type {?} */
    IAdvancedDataGrid.prototype.itemIcons;
    /** @type {?} */
    IAdvancedDataGrid.prototype.lockedColumnCount;
    /** @type {?} */
    IAdvancedDataGrid.prototype.lockedRowCount;
    /** @type {?} */
    IAdvancedDataGrid.prototype.rendererProviders;
    /** @type {?} */
    IAdvancedDataGrid.prototype.selectedCells;
    /** @type {?} */
    IAdvancedDataGrid.prototype.treeColumn;
}
/**
 * This interface define the aAdvancedDataGrid metaData.
 * @record
 */
export function IAdvancedDataGridMetaData() { }
if (false) {
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.dataelement;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.heading;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.type;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.columnorder;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.width;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.draggable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.filterable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.visible;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.visible_default;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.editable;
    /** @type {?} */
    IAdvancedDataGridMetaData.prototype.sort;
}
//# sourceMappingURL=data:application/json;base64,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