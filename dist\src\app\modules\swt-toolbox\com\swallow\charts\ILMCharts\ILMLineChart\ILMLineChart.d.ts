import { <PERSON>Ini<PERSON>, ElementRef, Renderer2 } from '@angular/core';
import { Container } from '../../../containers/swt-container.component';
import { CommonService } from '../../../utils/common.service';
import { SwtLabel } from '../../../controls/swt-label.component';
import { HashMap } from '../../../utils/HashMap.service';
import { CheckBoxLegend } from './control/CheckBoxLegend';
import { ILMSeriesLiveValue } from './control/ILMSeriesLiveValue';
import { HBox } from '../../../controls/swt-hbox.component';
import { AssetsLegend } from './control/AssetsLegend';
import { SwtILMChart } from './control/Chart/SwtILMChart';
export declare class ILMLineChart extends Container implements OnInit {
    private elem;
    private commonService;
    private _renderer;
    chartElement: SwtILMChart;
    static ACCUM_ACTUAL_INFLOW_TAG: string;
    static ACCUM_ACTUAL_OUTFLOWS_TAG: string;
    static ACCUM_FORECAST_OUTFLOWS_TAG: string;
    static ACCUM_FORECAST_INFLOWS_TAG: string;
    static ACTUAL_BALANCE_TAG: string;
    static FORECAST_BALANCE_TAG: string;
    static FORECAST_BAL_INC_ACTUALS_TAG: string;
    static THRESHOLD_TYPE: string[];
    static SERIES_LIST: string[];
    static ARRAY_COLLECTION_FROM_SERIES: string[];
    static LIQUIDITY_ZONES_LIST: string[];
    static SERIES_IGNORE_HIGHLIGH: string[];
    private liquidityZonesListValues;
    private liquidityZonesListLimits;
    private seriesStyleProperty;
    private highlightedLiquiditySeries;
    selectedCurrencyId: string;
    selectedEntityId: string;
    private _accumulatedDCLegend;
    private _balancesLegend;
    private _assetsLegend;
    private _accumLabel;
    stopUpdateLegend: boolean;
    private _chartValuesContainer;
    private _timeDynamicValue;
    parentDocument: any;
    private _visibleThresholds;
    private NoSOD_Dataprovider;
    private dataProvider;
    isEntityTimeframe: boolean;
    includeSOD: boolean;
    private timeFromAsInt;
    private timeToAsInt;
    private sumExternalSodMap;
    private _balanceLabel;
    private _ilmTree;
    private lowesetAxisValue;
    private lastSelectedGroupForZones;
    private lastSelectScenarioForZones;
    JsonLiquditiyZonesNOSOD: string;
    private _jsonData;
    datasets: any[];
    balanceLabel: SwtLabel;
    chartValuesContainer: HBox;
    timeDynamicValue: ILMSeriesLiveValue;
    jsonData: any[];
    accumLabel: SwtLabel;
    accumulatedDCLegend: CheckBoxLegend;
    balancesLegend: CheckBoxLegend;
    assetsLegend: AssetsLegend;
    constructor(elem: ElementRef, commonService: CommonService, _renderer: Renderer2);
    receiveMessage(): void;
    /**
           * Removes completely all the series in the chart
           *
           * */
    removeAllSeries(): void;
    resetILMLineChart(): void;
    /**
           * Adds a new Charts (LineSeries or AreaSeries) element
           * */
    addChart(yField: string, visible?: boolean): boolean;
    /**
           * Creates a threshold from cached DTO
           * */
    createThreshold(yField: string): boolean;
    /**
     * Add a threshold
     * */
    private addThreshold;
    /**
     * Remove the series with its legend
     * */
    removeSeries(yField: string): void;
    /**
     * Create and adds a line/area Series to line chart based on its yField
     * */
    createSeries(yField: string, visible?: boolean): boolean;
    /**
   *
   * Remove a group from list of data sets
   *
   **/
    removeFromDataSet(groupId: string): void;
    /**
     * Draws sorted legend items for line series
     * */
    updateLineLegend(recall?: boolean): void;
    /**
           * Updates the threshold color when series color changes
           * */
    updateThresholdStyleColor(groupId: string, styleName: string): void;
    /**
  * Draws sorted legend items for area series
  * */
    updateAreaLegend(recall?: boolean): void;
    /**
           * Draws sorted legend items for area series
           * */
    updateLiquidityZonesLegend(recall?: boolean): void;
    /**
     * Updates local datasets
     * value: datasets.dataset
     * */
    updateMetadatas(metadatas: any): void;
    JSONDataNOSOD: HashMap;
    JSONDataSOD: HashMap;
    seriesList: HashMap;
    JSONDataSODAsString: string;
    timeRangeArray: any[];
    timeRangeArrayEntity: any[];
    /**
           * Updates the dataprovider
           * */
    updateDataprovider(xmlDatasets: any, SODSelected: boolean, selectedItemsIntree?: any, uncheckedItemFromLegends?: any, uncheckedThresholdFromProfile?: any): void;
    /**
     * Updates the dataprovider for group/scenario
     * Returns false if an error on timeSlots, otherwise true
     * value: dataset.data
     * */
    private updateDataProviderFor;
    /**
   * Show and hide thresholds legend
   */
    showHideThreshold(groupId: string, selected: boolean): void;
    private getThresholdsColorStyle;
    switchDataProvider(): void;
    calculateLiquidityZonesLimites(groupId: string, scenarioId: string): boolean;
    removeLiquidityRegion(groupId: string, scenarioId: string): void;
    drawLiquidityZones(groupId: string, scenarioId: string, lastSelectedGroup?: string, lastSelectScenario?: string, withSOD?: boolean, visiblity?: boolean): void;
    drawLiquidityRegion(groupId: string, scenarioId: string, lastSelectedGroup?: string, lastSelectScenario?: string, visiblity?: boolean): void;
    callMethodInIframe(method: any, data: any): void;
    private getLiquidityRegionColor;
    /**
           * Updates the style metadata when style popup OK button is clicked.
           * */
    updateStyleMetadata(yField: string, styleId: string): void;
    getChartStyleSeriesStyleAsString(): string;
    ngOnInit(): void;
    resizeFinished: any;
    ngAfterViewInit(): void;
    private canCall;
    redrawChart: (delay: any) => void;
    debounce: (fn: any, delay: any) => (...args: any[]) => void;
    private previousRct;
    isVisible(e: any): boolean;
}
