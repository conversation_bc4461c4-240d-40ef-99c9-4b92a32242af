/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable, ElementRef } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
//@dynamic
export class focusManager {
    /**
     * @param {?} element
     */
    constructor(element) {
        this.element = element;
    }
    /**
     * @return {?}
     */
    static getFocus() {
        if (this.focusTarget) {
            return { name: this.focusTarget, id: this.focusTarget };
        }
        /** @type {?} */
        var activeElement = $(document.activeElement);
        if (activeElement.length) {
            if ($(activeElement)[0].id == "") {
                return { name: $(activeElement.parent())[0].id, id: $(activeElement.parent())[0].id };
            }
            return { name: $(activeElement)[0].id, id: $(activeElement)[0].id };
        }
    }
    /**
     * @return {?}
     */
    static getHoverButton() {
        if (this.hoverButton) {
            /** @type {?} */
            var activeElement = $(this.hoverButton);
            if (activeElement.length) {
                if ($(activeElement)[0].id == "") {
                    return { name: $(activeElement.parent())[0].id, id: $(activeElement.parent())[0].id };
                }
                return { name: $(activeElement)[0].id, id: $(activeElement)[0].id };
            }
        }
    }
}
focusManager.focusTarget = null;
focusManager.hoverButton = null;
focusManager.decorators = [
    { type: Injectable }
];
/** @nocollapse */
focusManager.ctorParameters = () => [
    { type: ElementRef }
];
if (false) {
    /** @type {?} */
    focusManager.focusTarget;
    /** @type {?} */
    focusManager.hoverButton;
    /**
     * @type {?}
     * @private
     */
    focusManager.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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