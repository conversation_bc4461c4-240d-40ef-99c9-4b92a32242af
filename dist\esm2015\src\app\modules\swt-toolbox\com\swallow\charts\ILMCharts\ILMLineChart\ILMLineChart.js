/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { Container } from '../../../containers/swt-container.component';
import { CommonService } from '../../../utils/common.service';
import { SeriesStyleProvider } from './style/SeriesStyleProvider';
import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { HashMap } from '../../../utils/HashMap.service';
import { Series } from './control/Series';
import { ExternalInterface } from '../../../utils/external-interface.service';
import { StringUtils } from '../../../utils/string-utils.service';
import { ILMSeriesLiveValue } from './control/ILMSeriesLiveValue';
import { SeriesHighlightEvent } from "../../../events/swt-events.module";
import { SwtUtil } from '../../../utils/swt-util.service';
import { SwtILMChart } from './control/Chart/SwtILMChart';
import { DividerResizeComplete } from "../../../events/swt-events.module";
export class ILMLineChart extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} _renderer
     */
    constructor(elem, commonService, _renderer) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._renderer = _renderer;
        this.liquidityZonesListValues = [];
        this.liquidityZonesListLimits = new HashMap;
        this.highlightedLiquiditySeries = [];
        this.stopUpdateLegend = false;
        this._visibleThresholds = [];
        this.NoSOD_Dataprovider = [];
        this.dataProvider = [];
        this.isEntityTimeframe = false;
        this.includeSOD = false;
        this.sumExternalSodMap = [];
        this.lowesetAxisValue = NaN;
        this.lastSelectedGroupForZones = [];
        this.lastSelectScenarioForZones = [];
        this.JsonLiquditiyZonesNOSOD = null;
        this._jsonData = [];
        this.datasets = [];
        this.JSONDataNOSOD = new HashMap;
        this.JSONDataSOD = new HashMap;
        this.seriesList = new HashMap;
        this.JSONDataSODAsString = '';
        this.timeRangeArray = [];
        this.timeRangeArrayEntity = [];
        this.canCall = true;
        this.redrawChart = (/**
         * @param {?} delay
         * @return {?}
         */
        (delay) => {
            if (!this.canCall)
                return;
            this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
            }), delay);
            this.canCall = false;
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.canCall = true;
            }), delay);
        });
        this.debounce = (/**
         * @param {?} fn
         * @param {?} delay
         * @return {?}
         */
        (fn, delay) => {
            /** @type {?} */
            let timer;
            return (/**
             * @param {...?} args
             * @return {?}
             */
            (...args) => {
                if (timer)
                    clearTimeout(timer);
                timer = setTimeout((/**
                 * @return {?}
                 */
                () => {
                    fn.apply(null, args);
                }), delay);
            });
        });
        this.previousRct = {
            left: 0,
            top: 0,
            width: 0,
            height: 0
        };
        // if (window.addEventListener) {
        //   window.addEventListener("message", this.receiveMessage.bind(this), false);
        // } else {
        //    (<any>window).attachEvent("onmessage", this.receiveMessage.bind(this));
        // }
    }
    /**
     * @return {?}
     */
    get balanceLabel() {
        return this._balanceLabel;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set balanceLabel(value) {
        this._balanceLabel = value;
    }
    /**
     * @return {?}
     */
    get chartValuesContainer() {
        return this._chartValuesContainer;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set chartValuesContainer(value) {
        this._chartValuesContainer = value;
    }
    /**
     * @return {?}
     */
    get timeDynamicValue() {
        return this._timeDynamicValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set timeDynamicValue(value) {
        this._timeDynamicValue = value;
    }
    /**
     * @return {?}
     */
    get jsonData() {
        return this._jsonData;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set jsonData(value) {
        this._jsonData = value;
    }
    /**
     * @return {?}
     */
    get accumLabel() {
        return this._accumLabel;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set accumLabel(value) {
        this._accumLabel = value;
    }
    /**
     * @return {?}
     */
    get accumulatedDCLegend() {
        return this._accumulatedDCLegend;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set accumulatedDCLegend(value) {
        this._accumulatedDCLegend = value;
    }
    /**
     * @return {?}
     */
    get balancesLegend() {
        return this._balancesLegend;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set balancesLegend(value) {
        this._balancesLegend = value;
    }
    /**
     * @return {?}
     */
    get assetsLegend() {
        return this._assetsLegend;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set assetsLegend(value) {
        this._assetsLegend = value;
    }
    /**
     * @return {?}
     */
    receiveMessage() {
    }
    /**
     * Removes completely all the series in the chart
     *
     *
     * @return {?}
     */
    removeAllSeries() {
        // Remove all the chartvalues
        // for each (var item:ILMSeriesLiveValue in chartValuesContainer.getChildren())
        // this.chartValuesContainer.removeChild(item);
        for (let i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            let serie = this.seriesList.getValues()[i];
            if (serie.seriesLabel) {
                serie.seriesLabel.removeLiveValue();
            }
        }
        // Init the series array
        this.seriesList = new HashMap;
        this.dataProvider = [];
        this.NoSOD_Dataprovider = [];
        // Remove datasets metadata as well
        this.datasets = [];
        // Update the area and line legends 
        if (this._accumulatedDCLegend)
            this.updateAreaLegend();
        if (this._balancesLegend)
            this.updateLineLegend();
        //refreshHorizontalAxis();
        //refreshVerticalAxis();
        this.callMethodInIframe("destroyChart", []);
    }
    /**
     * @return {?}
     */
    resetILMLineChart() {
        this.dataProvider = [];
        this.removeAllSeries();
        this.liquidityZonesListValues = [];
        this.liquidityZonesListLimits.clear();
        this.seriesList = new HashMap;
    }
    /**
     * Adds a new Charts (LineSeries or AreaSeries) element
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    addChart(yField, visible = true) {
        if (yField.indexOf("Thresholds") == -1)
            return this.createSeries(yField, visible);
        else {
            return this.createThreshold(yField);
        }
    }
    /**
     * Creates a threshold from cached DTO
     *
     * @param {?} yField
     * @return {?}
     */
    createThreshold(yField) {
        /** @type {?} */
        var dto = this.datasets[yField];
        if (!dto) {
            //throw Error("Programming error: method [updateDataSets] should be called first !");	
            return false;
        }
        /** @type {?} */
        var thresholds = dto["thresholds"];
        /** @type {?} */
        var styleColor = dto["thresholdsColor"];
        /** @type {?} */
        var threasholdValue;
        // for each (var chart:XML in thresholds.children()){
        for (var chart in thresholds) {
            threasholdValue = '' + thresholds[chart];
            // threasholdValue = chart.valueOf();
            if (threasholdValue.charAt(0) == '.') {
                threasholdValue = '0' + threasholdValue;
            }
            this.addThreshold(yField, chart, threasholdValue, styleColor);
        }
        return true;
    }
    /**
     * Add a threshold
     *
     * @private
     * @param {?} yField
     * @param {?} minmax
     * @param {?} value
     * @param {?} style
     * @return {?}
     */
    addThreshold(yField, minmax, value, style) {
        if (!this.dataProvider || this.dataProvider.length == 0 || String(value) == "" || isNaN(value)) {
            if (this.seriesList.getValue(yField + "." + minmax) != null) {
                this.seriesList.remove(yField + "." + minmax);
            }
            return;
        }
        else {
            value = Number(value);
            /** @type {?} */
            var lineSeries = new Series;
            lineSeries.seriesType = style;
            lineSeries.displayName = (yField + "." + minmax);
            // Setting the lineSeries xfield accordind to EntityTimeFrame value 
            if (this.isEntityTimeframe)
                lineSeries.xField = "timeSlotE";
            else
                lineSeries.xField = "timeSlot";
            lineSeries.yField = (yField + "." + minmax);
            //FIXME:CHECK IF NEEDED
            // Add dummy values for first and last dp objects
            // var first:Object=this.dataProvider[0];
            // first[lineSeries.yField]= includeSOD ? value : value - sumExternalSodMap.getValue(yField.split(".")[0]);
            // var last:Object=dataProvider.getItemAt(dataProvider.length - 1);
            // last[lineSeries.yField]=includeSOD ? value : value - sumExternalSodMap.getValue(yField.split(".")[0]);
            // var firstNoSOD:Object=NoSOD_Dataprovider.getItemAt(0);
            // firstNoSOD[lineSeries.yField]=includeSOD? value - sumExternalSodMap.getValue(yField.split(".")[0]) : value ;
            // var lastNoSOD:Object=NoSOD_Dataprovider.getItemAt(dataProvider.length - 1);
            // lastNoSOD[lineSeries.yField]=includeSOD ? value - sumExternalSodMap.getValue(yField.split(".")[0]): value ;
            // // _yFieldVisibleThresholds.push(lineSeries.yField);
            // ArrayCollection(dataProvider).setItemAt(first, 0);
            // ArrayCollection(dataProvider).setItemAt(last, dataProvider.length - 1);
            // ArrayCollection(NoSOD_Dataprovider).setItemAt(firstNoSOD, 0);
            // ArrayCollection(NoSOD_Dataprovider).setItemAt(lastNoSOD, dataProvider.length - 1);
            this.seriesList.put(lineSeries.yField, lineSeries);
            // thresholdsSanityCheck(lineSeries.yField);
        }
    }
    /**
     * Remove the series with its legend
     *
     * @param {?} yField
     * @return {?}
     */
    removeSeries(yField) {
        if (yField.indexOf("Thresholds") != -1) {
            for (let type in ILMLineChart.THRESHOLD_TYPE) {
                this.seriesList.remove(yField + "." + type);
            }
        }
        else {
            // super.removeSeries(yField);
            /** @type {?} */
            const series = this.seriesList.getValue(yField);
            if (series) {
                /** @type {?} */
                const seriesType = series.seriesType;
                if (series.seriesLabel) {
                    series.seriesLabel.removeLiveValue();
                }
                this.seriesList.remove(yField);
                // Remove the legend
                if (seriesType == 'line')
                    this.updateLineLegend();
                else
                    this.updateAreaLegend();
            }
        }
    }
    /**
     * Create and adds a line/area Series to line chart based on its yField
     *
     * @param {?} yField
     * @param {?=} visible
     * @return {?}
     */
    createSeries(yField, visible = true) {
        try {
            /** @type {?} */
            var arr = yField.split(".");
            /** @type {?} */
            var groupId = arr[0];
            /** @type {?} */
            var entityId = this.selectedEntityId;
            /** @type {?} */
            var currencyId = this.selectedCurrencyId;
            /** @type {?} */
            var groupId = arr[0];
            /** @type {?} */
            var scenarioId = arr[1];
            /** @type {?} */
            var dataElement = arr[2];
            /** @type {?} */
            var dto = this.datasets[groupId + '.' + scenarioId];
            if (!dto) {
                //trace("Chart not exist and to plot it need to send request to DB side");
                //throw Error("Programming error: method [updateDataSets] should be called first !");
                return false;
            }
            /** @type {?} */
            var metadata = dto['charts'];
            if (currencyId != metadata.currencyId || entityId != metadata.entityId) {
                return false;
            }
            /** @type {?} */
            var _yField = "";
            for (let i = 0; i < metadata.chart.length; i++) {
                // // Loop on all received datasets
                /** @type {?} */
                let chart = metadata.chart[i];
                if (chart.dataelement == dataElement) {
                    // Now create the timeseries (area or line)
                    /** @type {?} */
                    var chartType = chart.type;
                    _yField = (groupId + "." + scenarioId + ".") + chart.dataelement;
                    /* if the series already exist, change only their styles if have been changed
                              once the data changed */
                    // var series:Series = this.getSeriesByYField(yField);
                    /** @type {?} */
                    var series = this.seriesList.getValue(yField);
                    if (series) {
                        // if (series is CustomAreaSeries)
                        if (series.seriesType == 'area') {
                            // var area:CustomAreaSeries = series as CustomAreaSeries;
                            /** @type {?} */
                            var seriesStyle = series.appliedStyle;
                            if (chart.seriesStyle != seriesStyle) {
                                series.appliedStyle = chart.seriesStyle;
                            }
                        }
                        else {
                            /** @type {?} */
                            var seriesStyle = series.appliedStyle;
                            if (chart.seriesStyle != seriesStyle) {
                                series.appliedStyle = chart.seriesStyle;
                            }
                        }
                    }
                    else {
                        if (chartType == "area") {
                            /** @type {?} */
                            var areaSeries = new Series;
                            areaSeries.appliedStyle = chart.seriesStyle;
                            // areaSeries.displayName = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']');
                            areaSeries.displayName = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement);
                            if (this.isEntityTimeframe)
                                areaSeries.xField = "timeSlotE";
                            else
                                areaSeries.xField = "timeSlot";
                            //Set tooltip for legend in this stanadard :Tooltip: <Dataset description>.<scenario name> <Group name>		E.g.  "Forecast Outflow.Scenario 1.Global Euro"
                            // areaSeries.legendTooltip = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']') + "." + chart.legendTooltip;
                            areaSeries.legendTooltip = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "." + chart.legendTooltip;
                            areaSeries.yField = _yField;
                            areaSeries.legendDisplayName = chart.chartId;
                            areaSeries.visible = visible;
                            areaSeries.seriesType = 'area';
                            this.seriesList.put(_yField, areaSeries);
                            // setTimeout(() => {
                            this.updateAreaLegend(true);
                            // }, 0);
                        }
                        else {
                            // var lineSeries:ILMCustomLineSeries = new ILMCustomLineSeries(); 	
                            /** @type {?} */
                            var lineSeries = new Series;
                            // SeriesStyleProvider.applyLineSeriesStyle(lineSeries,chart.@seriesStyle);
                            lineSeries.appliedStyle = chart.seriesStyle;
                            // lineSeries.displayName = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']');
                            lineSeries.displayName = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement);
                            if (this.isEntityTimeframe)
                                lineSeries.xField = "timeSlotE";
                            else
                                lineSeries.xField = "timeSlot";
                            lineSeries.yField = _yField;
                            //FIXME:CHECK utility
                            // lineSeries.interpolateValues = true;
                            //Set tooltip for legend in this stanadard :Tooltip: <Dataset description>.<scenario name> <Group name>		E.g.  "Forecast Outflow.Scenario 1.Global Euro"
                            // lineSeries.legendTooltip = ExternalInterface.call('eval', 'label[\'text\'][\'' + chart.dataelement + '\']') + "." + chart.legendTooltip;
                            lineSeries.legendTooltip = ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "." + chart.legendTooltip;
                            // lineSeries.verticalAxis = vAxisLeft;						
                            lineSeries.legendDisplayName = chart.chartId;
                            /* 	Add the renderer in the chart values container which will contain the live value when moving
                                          the mouse in the chart */
                            /** @type {?} */
                            var seriesLabel = (/** @type {?} */ (this.chartValuesContainer.addChild(ILMSeriesLiveValue)));
                            // var seriesLabel:ILMSeriesLiveValue = new ILMSeriesLiveValue();
                            // the id of the serie 
                            seriesLabel.seriesId = lineSeries.yField;
                            // the value is set to empty on the first add
                            if (this.parentDocument.currencyFormat == 'currencyPat1') {
                                seriesLabel.seriesValue = "0.00";
                            }
                            else {
                                seriesLabel.seriesValue = "0,00";
                            }
                            seriesLabel.seriesStyle = chart.seriesStyle;
                            // the cercle fill for the renderer
                            // seriesLabel.cercleFill = cercleFill;
                            // We have always the cercle for the series label 
                            seriesLabel.isTimeLiveItem = false;
                            // Set the new defined renderer to the requested line series
                            lineSeries.seriesLabel = seriesLabel;
                            if (!visible) {
                                lineSeries.seriesLabel.visible = false;
                                lineSeries.seriesLabel.includeInLayout = false;
                            }
                            lineSeries.visible = visible;
                            lineSeries.seriesType = 'line';
                            // Add the renderer to the container if it is the first add of the line series
                            this.seriesList.put(_yField, lineSeries);
                            // }
                            if (!visible) {
                                lineSeries.seriesLabel.visible = false;
                            }
                            // updateLineLegend needs to be called twice to validate properties
                            this.updateLineLegend(true);
                        }
                    }
                    break;
                }
            }
            return true;
        }
        catch (e) {
        }
    }
    /**
     *
     * Remove a group from list of data sets
     *
     *
     * @param {?} groupId
     * @return {?}
     */
    removeFromDataSet(groupId) {
        for (const key in this.datasets) {
            if (key.indexOf(groupId) != -1) {
                delete this.datasets[key];
            }
        }
    }
    /**
     * Draws sorted legend items for line series
     *
     * @param {?=} recall
     * @return {?}
     */
    updateLineLegend(recall = false) {
        if (!this.stopUpdateLegend) {
            /** @type {?} */
            var isEmpty = true;
            /** @type {?} */
            var tmp = [];
            for (let i = 0; i < this.seriesList.getValues().length; i++) {
                /** @type {?} */
                let serie = this.seriesList.getValues()[i];
                if (serie && serie.seriesType === 'line' && serie.yField.indexOf("NO_MORE_LIQUIDITY_AVAILABLE") == -1) {
                    isEmpty = false;
                    tmp.push(serie);
                }
            }
            this._balancesLegend.dataProvider = tmp;
            if (isEmpty) {
                this.balanceLabel.visible = false;
                this.balanceLabel.includeInLayout = false;
            }
            else {
                this.balanceLabel.visible = true;
                this.balanceLabel.includeInLayout = true;
            }
            // updateLineLegend needs to be called twice to validate properties
            if (recall)
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    this.updateLineLegend(false);
                }), 0);
        }
    }
    /**
     * Updates the threshold color when series color changes
     *
     * @param {?} groupId
     * @param {?} styleName
     * @return {?}
     */
    //FIXME: need tp be dp,e
    updateThresholdStyleColor(groupId, styleName) {
        /** @type {?} */
        var dto = this.datasets[groupId.concat(".Thresholds")];
        if (dto) {
            dto["thresholdsColor"] = styleName;
            /** @type {?} */
            var color = SeriesStyleProvider.getStyleColor(styleName);
            for (let type in ILMLineChart.THRESHOLD_TYPE) {
                /** @type {?} */
                var yField = groupId.concat(".Thresholds.").concat(type);
                /** @type {?} */
                var thresholdSeries = this.seriesList.getValue(yField);
                if (thresholdSeries) {
                    // var theresholdShadow: uint = 0XFF8C00;
                    // //Glow effect attributes
                    // var alpha: Number = 0.3;
                    // var blurX: Number = 6;
                    // var blurY: Number = 2;
                    // var strength: Number = 100;
                    // var quality: Number = 1;
                    // // set color to red if the threshold is max2 or min 2
                    // if (type.indexOf("2") != -1) {
                    //   theresholdShadow = 0xFF0000;
                    // }
                    // var seriesStyle: SeriesStyle = SeriesStyleProvider.drawDashedLineSeriesStyle(0.5, color);
                    // SeriesStyleProvider.setLineSeriesStyle(thresholdSeries, seriesStyle);
                    // //Add glow effect to thresholds
                    // var glowFilter: GlowFilter = new GlowFilter(theresholdShadow, alpha, blurX, blurY, strength, quality);
                    // thresholdSeries.filters = [glowFilter];
                }
            }
        }
    }
    /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    updateAreaLegend(recall = false) {
        if (!this.stopUpdateLegend) {
            /** @type {?} */
            var isEmpty = true;
            /** @type {?} */
            var tmp = [];
            // for (var i:int = 0 ; i < this.series.length ; i++) 
            for (let i = 0; i < this.seriesList.getValues().length; i++) {
                /** @type {?} */
                let serie = this.seriesList.getValues()[i];
                if (serie.seriesType == 'area') {
                    // var areaSerie:CustomAreaSeries = 	(this.series[i] as CustomAreaSeries);
                    if (StringUtils.isEmpty(serie.minField)) {
                        isEmpty = false;
                        tmp.push(serie);
                    }
                }
            }
            this._accumulatedDCLegend.dataProvider = tmp;
            // updateAreaLegend needs to be called twice to validate properties
            if (recall)
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    this.updateAreaLegend(false);
                }), 0);
            if (isEmpty) {
                this.accumLabel.visible = false;
                this.accumLabel.includeInLayout = false;
            }
            else {
                this.accumLabel.visible = true;
                this.accumLabel.includeInLayout = true;
            }
        }
    }
    /**
     * Draws sorted legend items for area series
     *
     * @param {?=} recall
     * @return {?}
     */
    updateLiquidityZonesLegend(recall = false) {
        /** @type {?} */
        var tmp = [];
        if (this.parentDocument.showSourceLiquidity) {
            this.accumLabel.visible = false;
            this.accumLabel.includeInLayout = false;
        }
        else {
            this.accumLabel.visible = true;
            this.accumLabel.includeInLayout = true;
        }
        for (let i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            let serie = this.seriesList.getValues()[i];
            if (serie.seriesType == 'area') {
                if (!StringUtils.isEmpty(serie.minField)) {
                    tmp.push(serie);
                }
            }
        }
        // console.trace();
        this._assetsLegend.dataProvider = tmp;
        // updateAreaLegend needs to be called twice to validate properties
        if (recall)
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.updateLiquidityZonesLegend(false);
            }), 0);
        /*if (isEmpty){
        accumLabel.visible = false;
        accumLabel.includeInLayout = false;
        }
        else{
        accumLabel.visible = true;
        accumLabel.includeInLayout = true;
        }*/
    }
    /**
     * Updates local datasets
     * value: datasets.dataset
     *
     * @param {?} metadatas
     * @return {?}
     */
    updateMetadatas(metadatas) {
        /** @type {?} */
        let metaDataList = [];
        /** @type {?} */
        let chartsArray = [];
        // if(metadatas.metadata && !metadatas.metadata.length)
        //    metaDataList.push(metadatas.metadata);
        // else 
        //   metaDataList = metadatas.metadata;
        metaDataList = SwtUtil.convertObjectToArray(metadatas.metadata);
        for (let i = 0; i < metaDataList.length; i++) {
            // Loop on all received datasets
            /** @type {?} */
            var thresholdsColorStyle = "";
            /** @type {?} */
            let xmlData = metaDataList[i];
            /** @type {?} */
            var groupId = xmlData.groupId;
            if (xmlData.charts && !xmlData.charts.length)
                chartsArray.push(xmlData.charts);
            else
                chartsArray = xmlData.charts;
            for (let k = 0; k < chartsArray.length; k++) {
                /** @type {?} */
                let charts = chartsArray[k];
                /** @type {?} */
                var scenarioId = charts.scenarioId;
                /** @type {?} */
                var dto = new Object();
                if (charts.scenarioId == "Standard")
                    thresholdsColorStyle = this.getThresholdsColorStyle(charts);
                dto['charts'] = charts;
                this.datasets[groupId + '.' + scenarioId] = dto;
            }
            /** @type {?} */
            var thresholds = new Object();
            thresholds["thresholds"] = xmlData.thresholds;
            thresholds['thresholdsColor'] = thresholdsColorStyle;
            this.datasets[groupId + '.Thresholds'] = thresholds;
        }
    }
    /**
     * Updates the dataprovider
     *
     * @param {?} xmlDatasets
     * @param {?} SODSelected
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @param {?=} uncheckedThresholdFromProfile
     * @return {?}
     */
    updateDataprovider(xmlDatasets, SODSelected, selectedItemsIntree = null, uncheckedItemFromLegends = null, uncheckedThresholdFromProfile = null) {
        /** @type {?} */
        var newDp = [];
        /** @type {?} */
        var noSodDp = [];
        this.JSONDataNOSOD = new HashMap;
        this.JSONDataSOD = new HashMap;
        this.JSONDataSODAsString = '';
        /** @type {?} */
        var listOfGroups = [];
        if (this.dataProvider != null && this.dataProvider.length > 0) {
            if (SODSelected) {
                // newDp = (this.dataProvider as ArrayCollection).toArray();
                // noSodDp = (this.NoSOD_Dataprovider as ArrayCollection).toArray();
                newDp = this.dataProvider;
                noSodDp = this.NoSOD_Dataprovider;
            }
            else {
                newDp = this.NoSOD_Dataprovider;
                noSodDp = this.dataProvider;
            }
        }
        if (selectedItemsIntree == null)
            selectedItemsIntree = [];
        if (uncheckedItemFromLegends == null)
            uncheckedItemFromLegends = [];
        if (uncheckedThresholdFromProfile == null)
            uncheckedThresholdFromProfile = [];
        /** @type {?} */
        var dataToPass;
        /** @type {?} */
        var dataToPassSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartColor;
        /** @type {?} */
        var valueThresholdAsNumber;
        /** @type {?} */
        var valueThresholdAsNumberIncSOD;
        /** @type {?} */
        let xmlDatasetsAsArray = SwtUtil.convertObjectToArray(xmlDatasets.dataset);
        for (let k = 0; k < xmlDatasetsAsArray.length; k++) {
            /** @type {?} */
            let dataset = xmlDatasetsAsArray[k];
            /** @type {?} */
            var groupId = dataset.groupId;
            /** @type {?} */
            var scenarioId = dataset.scenarioId;
            /** @type {?} */
            let data = dataset.data;
            if (data) {
                data.parent = dataset;
                this.updateDataProviderFor(groupId, scenarioId, data, newDp, noSodDp, selectedItemsIntree, uncheckedItemFromLegends);
                if (listOfGroups.indexOf(groupId) == -1) {
                    listOfGroups.push(groupId);
                }
            }
        }
        // for each (var thresholdGroupId:string in listOfGroups)
        for (let k = 0; k < listOfGroups.length; k++) {
            /** @type {?} */
            let thresholdGroupId = listOfGroups[k];
            /** @type {?} */
            var dto = this.datasets[thresholdGroupId + '.Thresholds'];
            if (dto) {
                /** @type {?} */
                var thresholds = dto["thresholds"];
                /** @type {?} */
                var styleColor = dto["thresholdsColor"];
                /** @type {?} */
                var threasholdValue;
                /** @type {?} */
                var chartNameAsString;
                /** @type {?} */
                var visiblity;
                /** @type {?} */
                var visiblityAsString;
                // for each (var chart:XML in thresholds.children()){
                for (var chart in thresholds) {
                    threasholdValue = '' + thresholds[chart];
                    if (('' + threasholdValue).charAt(0) == '.') {
                        threasholdValue = '0' + threasholdValue;
                    }
                    if (threasholdValue != null && threasholdValue.length > 0) {
                        valueThresholdAsNumber = Number(threasholdValue);
                        valueThresholdAsNumberIncSOD = valueThresholdAsNumber - this.sumExternalSodMap[thresholdGroupId];
                        chartNameAsString = chart;
                        chartName = "\"name\":\"" + thresholdGroupId + '.' + chartNameAsString + "\"";
                        chartTypeToDisplay = "\"type\":\"Threshold\"";
                        chartStyleName = "\"chartStyleName\":\"" + styleColor + "\"";
                        /*visiblity = (uncheckedThresholdFromProfile.indexOf(thresholdGroupId) == -1);*/
                        if (selectedItemsIntree.indexOf(thresholdGroupId + '.Thresholds') == -1) {
                            visiblity = false;
                        }
                        else {
                            if (uncheckedThresholdFromProfile.indexOf(thresholdGroupId) == -1) {
                                visiblity = true;
                            }
                            else {
                                visiblity = false;
                            }
                        }
                        visiblityAsString = "\"visibility\":\"" + visiblity + "\"";
                        if (chartNameAsString.indexOf("2") != -1) {
                            chartColor = "\"color\":\"hsla(0, 100%, 50%, 0.3)\"";
                        }
                        else {
                            chartColor = "\"color\":\"hsla(33, 100%, 50%, 0.3)\"";
                        }
                        dataToPass = "\"data\":[" + valueThresholdAsNumber + "]";
                        dataToPassSOD = "\"dataSOD\":[" + valueThresholdAsNumberIncSOD + "]";
                        if (this.JSONDataSODAsString.length == 0)
                            this.JSONDataSODAsString += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + visiblityAsString + "," + chartColor + "," + dataToPass + "," + dataToPassSOD + "}";
                        else
                            this.JSONDataSODAsString += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + visiblityAsString + "," + chartColor + "," + dataToPass + "," + dataToPassSOD + "}";
                    }
                }
            }
        }
        this.dataProvider = newDp;
        this.NoSOD_Dataprovider = noSodDp;
    }
    /**
     * Updates the dataprovider for group/scenario
     * Returns false if an error on timeSlots, otherwise true
     * value: dataset.data
     *
     * @private
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?} value
     * @param {?} newDp
     * @param {?} noSodDp
     * @param {?=} selectedItemsIntree
     * @param {?=} uncheckedItemFromLegends
     * @return {?}
     */
    updateDataProviderFor(groupId, scenarioId, value, newDp, noSodDp, selectedItemsIntree = null, uncheckedItemFromLegends = null) {
        /** @type {?} */
        var valueOfHashMapElement = [];
        /** @type {?} */
        var valueOfHashMapElementNoSOD = [];
        this.JSONDataNOSOD = new HashMap;
        this.JSONDataSOD = new HashMap;
        this.timeRangeArray = [];
        this.timeRangeArrayEntity = [];
        /** @type {?} */
        var decRegex = new RegExp(',');
        /** @type {?} */
        var tempSOD = '' + value.parent.SUM_FORECAST_SOD;
        /** @type {?} */
        var sumCreditLineTotal = value.parent.SUM_CREDIT_LINE_TOTAL || 0;
        /** @type {?} */
        var grpScenKey = (groupId + "." + scenarioId + ".");
        this.liquidityZonesListValues[grpScenKey + "SUM_CREDIT_LINE_TOTAL"] = sumCreditLineTotal;
        /** @type {?} */
        var sumCollateral = value.parent.SUM_COLLATERAL || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_COLLATERAL"] = sumCollateral;
        /** @type {?} */
        var sumUnLiquidAssets = value.parent.SUM_UN_LIQUID_ASSETS || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_UN_LIQUID_ASSETS"] = sumUnLiquidAssets;
        /** @type {?} */
        var sumOtherTotal = value.parent.SUM_OTHER_TOTAL || 0;
        this.liquidityZonesListValues[grpScenKey + "SUM_OTHER_TOTAL"] = sumOtherTotal;
        tempSOD = tempSOD.replace(decRegex, '.');
        if (tempSOD.charAt(0) == '.') {
            tempSOD = '0' + tempSOD;
        }
        /** @type {?} */
        var sumForecastSod = Number(tempSOD);
        tempSOD = '' + value.parent.SUM_EXTERNAL_SOD;
        tempSOD = tempSOD.replace(decRegex, '.');
        if (tempSOD.charAt(0) == '.') {
            tempSOD = '0' + tempSOD;
        }
        /** @type {?} */
        var sumExternalSod = Number(tempSOD);
        this.sumExternalSodMap[groupId] = sumExternalSod;
        /** @type {?} */
        var idx = 0;
        /** @type {?} */
        var partOfDataProvider = [];
        /** @type {?} */
        var partOfDataProviderNOSOD = [];
        /** @type {?} */
        var firstTimeToShow;
        /** @type {?} */
        var firstTimeToShowWithEntityTimeFrame;
        // Complexity should be O(n)x8x(currentDp[i] time) => complexity=O(nLog(n))
        // for each(var resultXml: XML in value.result)
        for (let k = 0; k < value.result.length; k++) {
            /** @type {?} */
            let resultXml = value.result[k];
            /** @type {?} */
            var resultObj = partOfDataProvider.length == 0 ? new Object() : partOfDataProvider[idx];
            resultObj = !resultObj ? new Object() : resultObj;
            /** @type {?} */
            var resultObjNoSOD = partOfDataProviderNOSOD.length == 0 ? new Object() : partOfDataProviderNOSOD[idx];
            resultObjNoSOD = !resultObjNoSOD ? new Object() : resultObjNoSOD;
            /** @type {?} */
            var slotsAmountsArr = resultXml.toString().split(";");
            if (slotsAmountsArr.length == 11) {
                tempSOD = slotsAmountsArr[10];
                tempSOD = tempSOD.replace(decRegex, '.');
                if (tempSOD.charAt(0) == '.') {
                    tempSOD = '0' + tempSOD;
                }
                sumForecastSod = Number(tempSOD);
                tempSOD = '' + slotsAmountsArr[9];
                tempSOD = tempSOD.replace(decRegex, '.');
                if (tempSOD.charAt(0) == '.') {
                    tempSOD = '0' + tempSOD;
                }
                sumExternalSod = Number(tempSOD);
            }
            for (var i = 0; i < slotsAmountsArr.length; i++) {
                if ((slotsAmountsArr.length == 11) && (i == 9 || i == 10))
                    continue;
                // var dataField: string = ILMLineChart.ARRAY_COLLECTION_FROM_SERIES.getItemAt(i).toString();
                /** @type {?} */
                var dataField = ILMLineChart.ARRAY_COLLECTION_FROM_SERIES[i];
                /** @type {?} */
                var xmlValue = '' + slotsAmountsArr[i];
                xmlValue = xmlValue.replace(decRegex, '.');
                /** @type {?} */
                var sodType = dataField;
                // keep timeSlot data field not changed
                if (dataField != 'timeSlot' && dataField != 'timeSlotE') {
                    dataField = (groupId + "." + scenarioId + ".") + dataField;
                }
                // If not the first and second (timeSlot and timeSlotE)
                if (i != 0 && i != 1) {
                    if (xmlValue && xmlValue != '') {
                        if (xmlValue.charAt(0) == '.') {
                            xmlValue = '0' + xmlValue;
                        }
                        /** @type {?} */
                        var num = Number(xmlValue);
                        if (this.JSONDataSOD.getValue(dataField) == null) {
                            valueOfHashMapElement = new Array;
                        }
                        else {
                            valueOfHashMapElement = this.JSONDataSOD.getValue(dataField);
                        }
                        if (this.JSONDataNOSOD.getValue(dataField) == null) {
                            valueOfHashMapElementNoSOD = new Array;
                        }
                        else {
                            valueOfHashMapElementNoSOD = this.JSONDataNOSOD.getValue(dataField);
                        }
                        resultObj[dataField] = num;
                        valueOfHashMapElement.push("" + num);
                        this.JSONDataSOD.put(dataField, valueOfHashMapElement);
                        if ((sodType == ILMLineChart.FORECAST_BALANCE_TAG) || (sodType == ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG)) {
                            resultObjNoSOD[dataField] = num - sumForecastSod;
                            valueOfHashMapElementNoSOD.push("" + (num - sumForecastSod));
                        }
                        else if (sodType == ILMLineChart.ACTUAL_BALANCE_TAG) {
                            resultObjNoSOD[dataField] = num - sumExternalSod;
                            valueOfHashMapElementNoSOD.push("" + (num - sumExternalSod));
                        }
                        else {
                            resultObjNoSOD[dataField] = num;
                            valueOfHashMapElementNoSOD.push("" + num);
                        }
                        this.JSONDataNOSOD.put(dataField, valueOfHashMapElementNoSOD);
                    }
                }
                else {
                    if (isNaN(firstTimeToShow) && dataField == 'timeSlot')
                        firstTimeToShow = moment(xmlValue).toDate().getTime();
                    // firstTimeToShow = new Date(xmlValue).getTime();
                    // firstTimeToShow = DateFormatter.parseDateString(xmlValue).getTime();
                    if (isNaN(firstTimeToShowWithEntityTimeFrame) && dataField == 'timeSlotE')
                        firstTimeToShowWithEntityTimeFrame = moment(xmlValue).toDate().getTime();
                    //firstTimeToShowWithEntityTimeFrame = DateFormatter.parseDateString(xmlValue).getTime();
                    // Timeslots
                    resultObj[dataField] = xmlValue;
                    resultObjNoSOD[dataField] = xmlValue;
                    if (dataField == 'timeSlot') {
                        this.timeRangeArray.push(xmlValue);
                    }
                    else if (dataField == 'timeSlotE') {
                        this.timeRangeArrayEntity.push(xmlValue);
                    }
                }
            }
            partOfDataProvider[idx] = resultObj;
            partOfDataProviderNOSOD[idx] = resultObjNoSOD;
            idx++;
        }
        newDp[0] = partOfDataProvider[0];
        noSodDp[0] = partOfDataProviderNOSOD[0];
        newDp[1] = partOfDataProvider[idx - 1];
        noSodDp[1] = partOfDataProviderNOSOD[idx - 1];
        /** @type {?} */
        var pointStart = "\"pointStart\":" + firstTimeToShow;
        /** @type {?} */
        var pointStartEntity = "\"pointStartEntity\":" + firstTimeToShowWithEntityTimeFrame;
        /** @type {?} */
        var pointInterval = "\"pointInterval\":60000";
        /** @type {?} */
        var dataLength;
        /** @type {?} */
        var dataToPass;
        /** @type {?} */
        var dataToPassSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartTypeDetailsToDisplay;
        /** @type {?} */
        var chartlegendDisplayName;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartDisplayName;
        /** @type {?} */
        var chartDisplayLabel;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartStyleColor;
        /** @type {?} */
        var chartStyleBorderColor;
        /** @type {?} */
        var isVisibleChart;
        /** @type {?} */
        var dto = this.datasets[groupId + '.' + scenarioId];
        if (!dto) {
            //trace("Chart not exist and to plot it need to send request to DB side");
            //throw Error("Programming error: method [updateDataSets] should be called first !");
            return false;
        }
        /** @type {?} */
        var metadata = dto['charts'];
        /*if(currencyId != metadata.@currencyId || entityId != metadata.@entityId)
            {
            return false;
            }*/
        /** @type {?} */
        var _yField = "";
        /** @type {?} */
        var isVisibleFromLegend = true;
        //for each(var str:string in JSONDataSOD.getKeys()){
        // for (var str in this.JSONDataSOD) {
        for (let i = 0; i < this.JSONDataSOD.getKeys().length; i++) {
            //	if(selectedItemsIntree.indexOf(str) != -1 ) {
            /** @type {?} */
            let str = this.JSONDataSOD.getKeys()[i];
            dataLength = "\"dataLength\":" + this.JSONDataSOD.getValue(str).length;
            chartName = "\"name\":\"" + str + "\"";
            if (selectedItemsIntree.indexOf(str) == -1) {
                isVisibleFromLegend = false;
            }
            else {
                if (uncheckedItemFromLegends.indexOf(str) == -1) {
                    isVisibleFromLegend = true;
                }
                else {
                    isVisibleFromLegend = false;
                }
            }
            /** @type {?} */
            var chartIdParts = str.split(".");
            // for each (var chart:XML in metadata.children()){
            for (let k = 0; k < metadata.chart.length; k++) {
                /** @type {?} */
                let chart = metadata.chart[k];
                if (chart.dataelement == chartIdParts[2]) {
                    // Now create the timeseries (area or line)
                    /** @type {?} */
                    var chartType = chart.type;
                    chartTypeToDisplay = "\"type\":\"" + chart.type + "\"";
                    chartTypeDetailsToDisplay = "\"typeDetails\":\"" + SeriesStyleProvider.getStyleType(chart.seriesStyle) + "\"";
                    _yField = (groupId + "." + scenarioId + ".") + chart.dataelement;
                    chartStyleName = "\"chartStyleName\":\"" + SeriesStyleProvider.getStyleName(chart.seriesStyle) + "\"";
                    chartStyleColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(chart.seriesStyle) + "\"";
                    chartStyleBorderColor = "\"borderColor\":\"" + SeriesStyleProvider.getStyleBorderColorAsSring(chart.seriesStyle) + "\"";
                    chartDisplayName = "\"chartDisplayName\":\"" + chart.dataelement + "\"";
                    chartDisplayLabel = "\"chartDisplayLabel\":\"" + ExternalInterface.call('getBundle', 'text', chart.dataelement, chart.dataelement) + "\"";
                    // chartDisplayLabel = "\"chartDisplayLabel\":\"" + chart.dataelement + "\"";
                    chartlegendDisplayName = "\"chartlegendDisplayName\":\"" + chart.chartId + "\"";
                    isVisibleChart = "\"visibility\":\"" + isVisibleFromLegend + "\"";
                }
            }
            dataToPass = "\"data\":[" + this.JSONDataSOD.getValue(str) + "]";
            dataToPassSOD = "\"dataSOD\":[" + this.JSONDataNOSOD.getValue(str) + "]";
            if (this.JSONDataSODAsString.length == 0)
                this.JSONDataSODAsString += "{" + pointStart + "," + pointStartEntity + "," + pointInterval + "," + dataLength + "," + chartName + "," + isVisibleChart + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartDisplayName + "," + chartDisplayLabel + "," + chartlegendDisplayName + "," + dataToPass + "," + dataToPassSOD + "}";
            else
                this.JSONDataSODAsString += ",{" + pointStart + "," + pointStartEntity + "," + pointInterval + "," + dataLength + "," + chartName + "," + isVisibleChart + "," + chartTypeToDisplay + "," + chartTypeDetailsToDisplay + "," + chartStyleName + "," + chartStyleColor + "," + chartStyleBorderColor + "," + chartDisplayName + "," + chartDisplayLabel + "," + chartlegendDisplayName + "," + dataToPass + "," + dataToPassSOD + "}";
        }
        return true;
    }
    /**
     * Show and hide thresholds legend
     * @param {?} groupId
     * @param {?} selected
     * @return {?}
     */
    showHideThreshold(groupId, selected) {
        // for each (var series:Series in this.series){
        for (let i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            let series = this.seriesList.getValues()[i];
            if (series.seriesType == 'line') {
                if ((series.yField).indexOf("Thresholds") != -1 && (series.yField).indexOf(groupId) != -1) {
                    series.visible = selected;
                }
            }
        }
    }
    /**
     * @private
     * @param {?} charts
     * @return {?}
     */
    getThresholdsColorStyle(charts) {
        //default color
        /** @type {?} */
        var styleColor = SeriesStyleProvider.DASHED_SEGMENT_BLACK;
        /** @type {?} */
        let chartsList = SwtUtil.convertObjectToArray(charts.chart);
        // for each(var chart :XML in charts.children()){
        for (let k = 0; k < chartsList.length; k++) {
            /** @type {?} */
            let chart = chartsList[k];
            if (chart.dataelement == ILMLineChart.ACTUAL_BALANCE_TAG) {
                styleColor = chart.seriesStyle;
                break;
            }
        }
        return styleColor;
    }
    /**
     * @return {?}
     */
    switchDataProvider() {
        /** @type {?} */
        var tempDp = this.dataProvider;
        this.dataProvider = this.NoSOD_Dataprovider;
        this.NoSOD_Dataprovider = tempDp;
    }
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    calculateLiquidityZonesLimites(groupId, scenarioId) {
        /** @type {?} */
        var zoneStartValue = NaN;
        /** @type {?} */
        var zoneStartValueSOD = NaN;
        /** @type {?} */
        var zoneEndValue = NaN;
        /** @type {?} */
        var zoneEndValueSOD = NaN;
        /** @type {?} */
        var dataToPassNOSOD;
        /** @type {?} */
        var dataToPasSOD;
        /** @type {?} */
        var chartName;
        /** @type {?} */
        var chartTypeToDisplay;
        /** @type {?} */
        var chartyField;
        /** @type {?} */
        var chartStyleName;
        /** @type {?} */
        var chartColor;
        /** @type {?} */
        var limitValue = 0;
        /** @type {?} */
        var noMoreValues = 0;
        /** @type {?} */
        var grpScenKey = (groupId + "." + scenarioId + ".");
        /** @type {?} */
        var sumExternalSod = this.sumExternalSodMap[groupId] || 0;
        /** @type {?} */
        var valuesChanged = false;
        /** @type {?} */
        var prevuousJSONSent = this.JsonLiquditiyZonesNOSOD;
        this.JsonLiquditiyZonesNOSOD = "";
        // for each(var asset:String in LIQUIDITY_ZONES_LIST){
        for (let index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
            /** @type {?} */
            const asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
            /** @type {?} */
            const assetsValueAsNumber = this.liquidityZonesListValues[grpScenKey + asset] ? this.liquidityZonesListValues[grpScenKey + asset] : 0;
            if (asset != "NO_MORE_LIQUIDITY_AVAILABLE") {
                zoneEndValue = Number(limitValue + assetsValueAsNumber) * (-1);
                zoneEndValueSOD = -limitValue - Number(assetsValueAsNumber) - sumExternalSod;
                zoneStartValue = -limitValue;
                zoneStartValueSOD = -limitValue - sumExternalSod;
                chartName = "\"name\":\"" + groupId + "." + scenarioId + "." + asset + "\"";
                chartTypeToDisplay = "\"type\":\"area\"";
                chartStyleName = "\"chartStyleName\":\"" + this.getLiquidityRegionColor(asset) + "\"";
                chartColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(this.getLiquidityRegionColor(asset)) + "\"";
                dataToPassNOSOD = "\"dataNoSOD\":[" + zoneStartValue + "," + zoneEndValue + "]";
                dataToPasSOD = "\"dataSOD\":[" + zoneStartValueSOD + "," + zoneEndValueSOD + "]";
                if (this.JsonLiquditiyZonesNOSOD.length == 0)
                    this.JsonLiquditiyZonesNOSOD += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                else
                    this.JsonLiquditiyZonesNOSOD += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                if (asset == "SUM_OTHER_TOTAL") {
                    noMoreValues = Number(limitValue + assetsValueAsNumber) * (-1);
                }
                limitValue = Number(limitValue + assetsValueAsNumber);
            }
            else {
                chartName = "\"name\":\"" + groupId + "." + scenarioId + "." + asset + "\"";
                chartTypeToDisplay = "\"type\":\"line\"";
                chartStyleName = "\"chartStyleName\":\"" + this.getLiquidityRegionColor(asset) + "\"";
                chartColor = "\"color\":\"" + SeriesStyleProvider.getStyleColorAsSring(this.getLiquidityRegionColor(asset)) + "\"";
                dataToPasSOD = "\"dataSOD\":[" + (noMoreValues - sumExternalSod) + "," + (noMoreValues - sumExternalSod) + "]";
                dataToPassNOSOD = "\"dataNoSOD\":[" + (noMoreValues) + "," + (noMoreValues) + "]";
                if (this.JsonLiquditiyZonesNOSOD.length == 0)
                    this.JsonLiquditiyZonesNOSOD += "{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
                else
                    this.JsonLiquditiyZonesNOSOD += ",{" + chartName + "," + chartTypeToDisplay + "," + chartStyleName + "," + chartColor + "," + dataToPassNOSOD + "," + dataToPasSOD + "}";
            }
        }
        if (prevuousJSONSent != this.JsonLiquditiyZonesNOSOD) {
            return true;
        }
        else {
            return false;
        }
    }
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @return {?}
     */
    removeLiquidityRegion(groupId, scenarioId) {
        /** @type {?} */
        var yField = groupId + "." + scenarioId + ".";
        /** @type {?} */
        var asset = null;
        for (let index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
            /** @type {?} */
            const asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
            this.seriesList.remove(yField + asset);
        }
        //Remove highlighting caused by similar colors in the legend
        for (let index = 0; index < this.highlightedLiquiditySeries.length; index++) {
            /** @type {?} */
            const serie = this.highlightedLiquiditySeries[index];
            /** @type {?} */
            var dto = {};
            dto.highligh = false;
            dto.yField = serie.yField;
            if ($(this.elem.nativeElement).closest('.legendsDivider')) {
                dto.parentTab = $(this.elem.nativeElement).closest('.legendsDivider').attr('name');
            }
            SeriesHighlightEvent.emit(dto);
            // Dispatch the event so that legends will delete highlight
        }
        this.highlightedLiquiditySeries.pop();
    }
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} withSOD
     * @param {?=} visiblity
     * @return {?}
     */
    drawLiquidityZones(groupId, scenarioId, lastSelectedGroup = null, lastSelectScenario = null, withSOD = false, visiblity = true) {
        try {
            if (this.dataProvider.length && groupId && scenarioId) {
                /** @type {?} */
                var grpScenKey = (groupId + "." + scenarioId + ".");
                /** @type {?} */
                var sumExternalSod = this.sumExternalSodMap[groupId];
                // var firstItem_before:Object=dataProvider.getItemAt(0);
                // var lastItem_before:Object=dataProvider.getItemAt(dataProvider.length - 1);
                // var firstItemSod_before:Object = NoSOD_Dataprovider.getItemAt(0);
                // var lastItemSod_before:Object = NoSOD_Dataprovider.getItemAt(NoSOD_Dataprovider.length - 1);
                /** @type {?} */
                var firstItem_before = this.dataProvider[0];
                /** @type {?} */
                var lastItem_before = this.dataProvider[this.dataProvider.length - 1];
                /** @type {?} */
                var firstItemSod_before = this.NoSOD_Dataprovider[0];
                /** @type {?} */
                var lastItemSod_before = this.NoSOD_Dataprovider[this.dataProvider.length - 1];
                /** @type {?} */
                var limitValue = 0;
                /** @type {?} */
                var noMoreValues = 0;
                // (this.vAxisLeft as CustomLinearAxis).recalculateMaxMin();
                // min = vAxisLeft.minimum;
                /** @type {?} */
                var zoneStartValue = NaN;
                /** @type {?} */
                var zoneStartValueSOD = NaN;
                /** @type {?} */
                var zoneEndValue = NaN;
                /** @type {?} */
                var zoneEndValueSOD = NaN;
                for (let index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
                    /** @type {?} */
                    const asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
                    if (asset != "NO_MORE_LIQUIDITY_AVAILABLE") {
                        zoneEndValue = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]) * (-1);
                        zoneEndValueSOD = -limitValue - Number(this.liquidityZonesListValues[grpScenKey + asset]) - sumExternalSod;
                        zoneStartValue = -limitValue;
                        zoneStartValueSOD = -limitValue - sumExternalSod;
                        //FIXME:CHECK IF NEEDED
                        // if((zoneEndValue < min && zoneStartValue > min))
                        // 	zoneEndValue = min;
                        // if((zoneEndValueSOD < min && zoneStartValueSOD > min))
                        // 	zoneEndValueSOD = min;
                        if (this.includeSOD) {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                        }
                        else {
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = zoneEndValueSOD;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = zoneEndValue;
                        }
                        if (asset == "SUM_OTHER_TOTAL") {
                            noMoreValues = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]) * (-1);
                        }
                        if (this.includeSOD) {
                            firstItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                            lastItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                        }
                        else {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            firstItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValue;
                            lastItem_before[groupId + "." + scenarioId + "." + asset + "Limit"] = zoneStartValueSOD;
                        }
                        this.liquidityZonesListLimits.put(groupId + "." + scenarioId + "." + asset, limitValue);
                        limitValue = Number(limitValue + this.liquidityZonesListValues[grpScenKey + asset]);
                    }
                    else {
                        if (this.includeSOD) {
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                        }
                        else {
                            firstItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            firstItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                            lastItemSod_before[groupId + "." + scenarioId + "." + asset] = noMoreValues;
                            lastItem_before[groupId + "." + scenarioId + "." + asset] = noMoreValues - sumExternalSod;
                        }
                        this.liquidityZonesListValues[grpScenKey + "NO_MORE_LIQUIDITY_AVAILABLE"] = noMoreValues;
                    }
                }
                this.drawLiquidityRegion(groupId, scenarioId, lastSelectedGroup, lastSelectScenario, visiblity);
                //Run action on IFRAME
                /** @type {?} */
                var isUpdated = this.calculateLiquidityZonesLimites(groupId, scenarioId);
                this.parentDocument.refreshSourceOfLiquidityForIFrame(isUpdated);
            }
        }
        catch (err) {
        }
    }
    /**
     * @param {?} groupId
     * @param {?} scenarioId
     * @param {?=} lastSelectedGroup
     * @param {?=} lastSelectScenario
     * @param {?=} visiblity
     * @return {?}
     */
    drawLiquidityRegion(groupId, scenarioId, lastSelectedGroup = null, lastSelectScenario = null, visiblity = true) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (groupId != null && scenarioId != null) {
                /** @type {?} */
                var yField = groupId + "." + scenarioId + ".";
                /** @type {?} */
                var assetValue;
                for (let index = 0; index < ILMLineChart.LIQUIDITY_ZONES_LIST.length; index++) {
                    /** @type {?} */
                    const asset = ILMLineChart.LIQUIDITY_ZONES_LIST[index];
                    if (asset != 'NO_MORE_LIQUIDITY_AVAILABLE') {
                        // var areaSeries:CustomAreaSeries = new CustomAreaSeries(); 	
                        /** @type {?} */
                        let areaSeries = new Series;
                        areaSeries.visible = visiblity;
                        if (this.parentDocument.currencyFormat == 'currencyPat1') {
                            errorLocation = 27;
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            // assetValue = this.leftVerticalAxisFormatter(Number(amount));
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), ',');
                            errorLocation = 2;
                        }
                        else {
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            // assetValue = this.leftVerticalAxisFormatter(Number(amount));
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), '.');
                        }
                        areaSeries.legendDisplayName = ExternalInterface.call('getBundle', 'text', asset, asset) + "|" + assetValue;
                        /** @type {?} */
                        var areaColor = this.getLiquidityRegionColor(asset);
                        /** @type {?} */
                        var minField = yField + asset + "Limit";
                        if (this.isEntityTimeframe)
                            areaSeries.xField = "timeSlotE";
                        else
                            areaSeries.xField = "timeSlot";
                        areaSeries.appliedStyle = areaColor;
                        areaSeries.yField = yField + asset;
                        areaSeries.minField = minField;
                        areaSeries.seriesType = 'area';
                        this.seriesList.put(yField + asset, areaSeries);
                        errorLocation = 5;
                    }
                    else {
                        /** @type {?} */
                        let lineSeries = new Series;
                        lineSeries.visible = visiblity;
                        if (this.parentDocument.currencyFormat == 'currencyPat1') {
                            errorLocation = 27;
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), ',');
                            errorLocation = 2;
                        }
                        else {
                            /** @type {?} */
                            var amount = this.liquidityZonesListValues[yField + asset] || 0;
                            ;
                            assetValue = this.parentDocument.leftVerticalAxisFormatter(Number(amount));
                            assetValue = assetValue.replace(new RegExp('\\ ', 'g'), '.');
                        }
                        lineSeries.displayName = yField + asset;
                        lineSeries.legendDisplayName = ExternalInterface.call('getBundle', 'text', asset, asset) + "|" + assetValue;
                        /** @type {?} */
                        var lineColor = this.getLiquidityRegionColor(asset);
                        lineSeries.appliedStyle = lineColor;
                        lineSeries.seriesType = 'line';
                        if (this.isEntityTimeframe)
                            lineSeries.xField = "timeSlotE";
                        else
                            lineSeries.xField = "timeSlot";
                        lineSeries.yField = yField + asset;
                        this.seriesList.put(yField + asset, lineSeries);
                    }
                }
                if (!StringUtils.isEmpty(lastSelectedGroup) && !StringUtils.isEmpty(lastSelectScenario)) {
                    this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
                }
                this.updateLiquidityZonesLegend(true);
                //FIXME:
                // callLater(distinctSimilarColorSeries);
            }
            else {
                if (lastSelectedGroup != null && lastSelectScenario != null) {
                    this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
                }
                this.updateLiquidityZonesLegend(false);
            }
        }
        catch (error) {
            if (!StringUtils.isEmpty(lastSelectedGroup) && !StringUtils.isEmpty(lastSelectScenario)) {
                this.removeLiquidityRegion(lastSelectedGroup, lastSelectScenario);
            }
            this._assetsLegend.dataProvider = new Array();
            // Alert.show("No data are availables , need to select (at least)one figure for the correspond group ["+groupId+"] and scenario [ "+scenarioId+"]")
            ExternalInterface.call("console.log", "No data are availables , need to select (at least)one figure for the correspond group: " + groupId);
        }
    }
    /**
     * @param {?} method
     * @param {?} data
     * @return {?}
     */
    callMethodInIframe(method, data) {
        // var o = document.getElementsByTagName('iframe')[0];
        this.chartElement.callMethodByName(method, data);
        // if (this.iframeContaier && this.iframeContaier.contentWindow) {
        //   this.iframeContaier.contentWindow.postMessage([method, data], '*');
        // }
    }
    // getIframeWindow(iframe_object) {
    //   var doc;
    //   if (iframe_object.contentWindow) {
    //     return iframe_object.contentWindow;
    //   }
    //   if (iframe_object.window) {
    //     return iframe_object.window;
    //   }
    //   if (!doc && iframe_object.contentDocument) {
    //     doc = iframe_object.contentDocument;
    //   }
    //   if (!doc && iframe_object.document) {
    //     doc = iframe_object.document;
    //   }
    //   if (doc && doc.defaultView) {
    //     return doc.defaultView;
    //   }
    //   if (doc && doc.parentWindow) {
    //     return doc.parentWindow;
    //   }
    //   return undefined;
    // }
    /**
     * @private
     * @param {?} assetName
     * @return {?}
     */
    getLiquidityRegionColor(assetName) {
        /** @type {?} */
        var color = "";
        if (assetName == "SUM_CREDIT_LINE_TOTAL")
            color = SeriesStyleProvider.CONT_AREA_ANTIQUE_WHITE;
        else if (assetName == "SUM_COLLATERAL")
            color = SeriesStyleProvider.CONT_AREA_APRICOT_PEACH;
        else if (assetName == "SUM_UN_LIQUID_ASSETS")
            color = SeriesStyleProvider.CONT_AREA_NAVAJO_WHITE;
        else if (assetName == "SUM_OTHER_TOTAL")
            color = SeriesStyleProvider.CONT_AREA_ROSE_FOG;
        else
            color = SeriesStyleProvider.CONT_SEGMENT_BOLD_RED;
        return color;
    }
    /**
     * Updates the style metadata when style popup OK button is clicked.
     *
     * @param {?} yField
     * @param {?} styleId
     * @return {?}
     */
    updateStyleMetadata(yField, styleId) {
        /** @type {?} */
        var erroLocation = 0;
        /** @type {?} */
        var arr = yField.split(".");
        /** @type {?} */
        var groupId = arr[0];
        /** @type {?} */
        var scenarioId = arr[1];
        /** @type {?} */
        var dataElement = arr[2];
        /** @type {?} */
        var dto = this.datasets[groupId + '.' + scenarioId];
        // Do this test as 'updateStyleMetadata' can be called from SeriesStylePopup.mxml to serve both group and global tabs
        if (dto) {
            /** @type {?} */
            var charts = dto['charts'];
            /** @type {?} */
            let chartsList = SwtUtil.convertObjectToArray(charts.chart);
            // for each(var chart :XML in charts.children()){
            for (let k = 0; k < chartsList.length; k++) {
                /** @type {?} */
                let chart = chartsList[k];
                if (chart.dataelement == dataElement) {
                    // Update the seriesStyle attribute
                    chart.seriesStyle = styleId;
                }
            }
        }
    }
    /**
     * @return {?}
     */
    getChartStyleSeriesStyleAsString() {
        /** @type {?} */
        var chartsStyles = "";
        /** @type {?} */
        var seriesStyle = "";
        /** @type {?} */
        var yField = "";
        for (let i = 0; i < this.seriesList.getValues().length; i++) {
            /** @type {?} */
            let serie = this.seriesList.getValues()[i];
            if (serie.seriesType == 'area') {
                yField = serie.yField;
                if (yField.indexOf('SUM_UN_LIQUID_ASSETS') == -1 && yField.indexOf('SUM_OTHER_TOTAL') == -1
                    && yField.indexOf('SUM_COLLATERAL') == -1
                    && yField.indexOf('SUM_CREDIT_LINE_TOTAL') == -1) {
                    seriesStyle = serie.appliedStyle;
                    chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
                }
                //FIXME:CECK
            }
            else if (serie.seriesType == 'line' && serie.legendTooltip) {
                yField = serie.yField;
                if (yField.indexOf('NO_MORE_LIQUIDITY_AVAILABLE') == -1) {
                    seriesStyle = serie.appliedStyle;
                    chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
                }
            }
        }
        if (chartsStyles.length > 1)
            chartsStyles = chartsStyles.substr(0, chartsStyles.length - 1);
        return chartsStyles;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        // this.iframeContaier = document.createElement('iframe');
        // this.iframeContaier.style.cssText = "position: absolute;top:0px;left: 0px; height: 0px;width: 0px; border: 1px solid";
        // this.iframeContaier.setAttribute("class", "iframeClass");
        // this.iframeContaier.src = SwtUtil.getBaseURL()+"charts.htm";
        // document.body.appendChild(this.iframeContaier);
        $(window).resize((/**
         * @return {?}
         */
        () => {
            clearTimeout(this.resizeFinished);
            this.resizeFinished = setTimeout((/**
             * @return {?}
             */
            () => {
                if (this.isVisible(this.elem.nativeElement)) {
                    this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
                }
            }), 250);
        }));
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        //   let mutationObserver = new window.MutationObserver(this.checkDiff)
        //   mutationObserver.observe(this.elem.nativeElement, {
        //     attributes: true,
        //     attributeFilter: ['style', 'class','width', 'x','y']
        //   })
        //   this.elem.nativeElement.classList.add('testtesttest');
        //   const ro = new ResizeObserver((entries, observer) => {
        //     if (this.isVisible(this.elem.nativeElement)) {
        //       this.iframeContaier.style.display = 'none';
        //       // this.iframeContaier.style.visibility = 'visible';
        //       var popupRect = this.elem.nativeElement.getBoundingClientRect();
        //       if(popupRect.height < 15){
        //         this.iframeContaier.style.display = 'none';
        //         return;
        //       }
        //       if (this.previousRct.height !== popupRect.height) {
        //         this.iframeContaier.style.height = popupRect.height + "px";
        //       }
        //       if (this.previousRct.left !== popupRect.left) {
        //         this.iframeContaier.style.left = popupRect.left + "px";
        //       }
        //       if (this.previousRct.width !== popupRect.width) {
        //         this.iframeContaier.style.width = popupRect.width + "px";
        //       }
        //       if (this.previousRct.top !== popupRect.top) {
        //         this.iframeContaier.style.top = popupRect.top + "px";
        //       }
        //       //this.elem.nativeElement.getBoundingClientRect()
        //       this.previousRct = { left: this.iframeContaier.style.left, top: this.iframeContaier.top, height: this.iframeContaier.style.height, width: this.iframeContaier.style.width };
        //     } else {
        //       this.iframeContaier.style.display = 'none';
        //     }
        //  });
        // ro.observe(this.elem.nativeElement);
        // let parentHeight = this.elem.nativeElement.offsetParent.clientHeight;
        // const ro = new ResizeObserver((entries, observer) => {
        //   if (this.isVisible(this.elem.nativeElement)) {
        //     //Call redraw function in the chart but limit the call to once per 100 militsecond to win perfermance as 
        //     //ResizeObserver can be callaed too many times in 
        //     // this.redrawChart(100);
        //     // this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
        //     //     this.chart.reflow();
        //     //     this.chart.update({
        //     //       plotOptions: {
        //     //         series: {
        //     //           states: {
        //     //             hover: {
        //     //               enabled: false
        //     //             },
        //     //             inactive: {
        //     //               enabled: false
        //     //             }
        //     //           }
        //     //         }
        //     //       },
        //     //     })
        //   }
        // });
        // ro.observe(this.elem.nativeElement);
        DividerResizeComplete.subscribe((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            // const isSelectedTab =  this.tabName == "GlobalView" && (this.parentDocument.tabNavigator.selectedIndex == 0)
            if (event.id === "legendsDivider" || event.id === "gridDivider" || event.id === "treeDivider") {
                if (this.isVisible(this.elem.nativeElement)) {
                    this.callMethodInIframe("redrawChart", [$(this.elem.nativeElement)[0].clientHeight]);
                }
            }
        }));
    }
    /**
     * @param {?} e
     * @return {?}
     */
    isVisible(e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    }
}
// public iframeContaier;
ILMLineChart.ACCUM_ACTUAL_INFLOW_TAG = "aac";
ILMLineChart.ACCUM_ACTUAL_OUTFLOWS_TAG = "aad";
ILMLineChart.ACCUM_FORECAST_OUTFLOWS_TAG = "afd";
ILMLineChart.ACCUM_FORECAST_INFLOWS_TAG = "afc";
ILMLineChart.ACTUAL_BALANCE_TAG = "ab";
ILMLineChart.FORECAST_BALANCE_TAG = "fbb";
ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG = "fbia";
ILMLineChart.THRESHOLD_TYPE = ["min1", "min2", "max1", "max2"];
ILMLineChart.SERIES_LIST = new Array("timeSlot", "timeSlotE", "afd", "afc", "aad", "aac", "ab", "fbb", "fbia", "sumExSOD", "sumForcastSOD");
// static ARRAY_COLLECTION_FROM_SERIES:ArrayCollection = new ArrayCollection(SERIES_LIST);
ILMLineChart.ARRAY_COLLECTION_FROM_SERIES = ILMLineChart.SERIES_LIST;
ILMLineChart.LIQUIDITY_ZONES_LIST = ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE"];
ILMLineChart.SERIES_IGNORE_HIGHLIGH = ["SUM_CREDIT_LINE_TOTAL", "SUM_COLLATERAL", "SUM_UN_LIQUID_ASSETS", "SUM_OTHER_TOTAL", "NO_MORE_LIQUIDITY_AVAILABLE", "Thresholds"];
ILMLineChart.decorators = [
    { type: Component, args: [{
                selector: 'ILMLineChart',
                template: "<!--<iframe title=\"charts\" class=\"iframeiframe\" #iframeContaier src=\"http://localhost:8080/swallowtech/charts.htm\" style=\"position: absolute;left: 20px;right: 20px; height: 150px;width: 150px;\" seamless></iframe>-->\r\n<swt-ilm-chart width=\"100%\" height=\"300\"  #chartElement ></swt-ilm-chart>",
                styles: [""]
            }] }
];
/** @nocollapse */
ILMLineChart.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: Renderer2 }
];
ILMLineChart.propDecorators = {
    chartElement: [{ type: ViewChild, args: ['chartElement',] }]
};
if (false) {
    /** @type {?} */
    ILMLineChart.ACCUM_ACTUAL_INFLOW_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_ACTUAL_OUTFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_FORECAST_OUTFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACCUM_FORECAST_INFLOWS_TAG;
    /** @type {?} */
    ILMLineChart.ACTUAL_BALANCE_TAG;
    /** @type {?} */
    ILMLineChart.FORECAST_BALANCE_TAG;
    /** @type {?} */
    ILMLineChart.FORECAST_BAL_INC_ACTUALS_TAG;
    /** @type {?} */
    ILMLineChart.THRESHOLD_TYPE;
    /** @type {?} */
    ILMLineChart.SERIES_LIST;
    /** @type {?} */
    ILMLineChart.ARRAY_COLLECTION_FROM_SERIES;
    /** @type {?} */
    ILMLineChart.LIQUIDITY_ZONES_LIST;
    /** @type {?} */
    ILMLineChart.SERIES_IGNORE_HIGHLIGH;
    /** @type {?} */
    ILMLineChart.prototype.chartElement;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.liquidityZonesListValues;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.liquidityZonesListLimits;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.seriesStyleProperty;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.highlightedLiquiditySeries;
    /** @type {?} */
    ILMLineChart.prototype.selectedCurrencyId;
    /** @type {?} */
    ILMLineChart.prototype.selectedEntityId;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._accumulatedDCLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._balancesLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._assetsLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._accumLabel;
    /** @type {?} */
    ILMLineChart.prototype.stopUpdateLegend;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._chartValuesContainer;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._timeDynamicValue;
    /** @type {?} */
    ILMLineChart.prototype.parentDocument;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._visibleThresholds;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.NoSOD_Dataprovider;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.dataProvider;
    /** @type {?} */
    ILMLineChart.prototype.isEntityTimeframe;
    /** @type {?} */
    ILMLineChart.prototype.includeSOD;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.timeFromAsInt;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.timeToAsInt;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.sumExternalSodMap;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._balanceLabel;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._ilmTree;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lowesetAxisValue;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lastSelectedGroupForZones;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.lastSelectScenarioForZones;
    /** @type {?} */
    ILMLineChart.prototype.JsonLiquditiyZonesNOSOD;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._jsonData;
    /** @type {?} */
    ILMLineChart.prototype.datasets;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataNOSOD;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataSOD;
    /** @type {?} */
    ILMLineChart.prototype.seriesList;
    /** @type {?} */
    ILMLineChart.prototype.JSONDataSODAsString;
    /** @type {?} */
    ILMLineChart.prototype.timeRangeArray;
    /** @type {?} */
    ILMLineChart.prototype.timeRangeArrayEntity;
    /** @type {?} */
    ILMLineChart.prototype.resizeFinished;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.canCall;
    /** @type {?} */
    ILMLineChart.prototype.redrawChart;
    /** @type {?} */
    ILMLineChart.prototype.debounce;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.previousRct;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ILMLineChart.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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