import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { OnInit, OnDestroy, ElementRef } from "@angular/core";
export declare class SwtButton extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    protected swtbutton: ElementRef;
    private hostButton;
    private _enabled;
    private _styleName;
    private _buttonMode;
    private _width;
    protected _label: string;
    private _tabIndex;
    tabIndex: any;
    width: string;
    styleName: string;
    label: any;
    textDictionaryId: any;
    buttonMode: boolean;
    enabled: any;
    /**
     * Constructor.
     */
    constructor(elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit.
     */
    ngOnInit(): void;
    /**
     * Removes all handlers attached to the element.
     */
    removeEventsListeners(element: any): void;
    /**
     * This method is used to change button view.
     * @param state
     */
    private setButtonState;
    /**
     * This method is used to set focus to button.
     */
    setFocus(): void;
    setVisible(visibility: boolean): void;
    /**
      * returns DOM of the button element.
      */
    readonly button: any;
    /**
     * convert entered value as string to boolean.
     * @param value
     */
    private adaptValue;
    /**
     * Check if the button has an icon or not.
     */
    private hasIcon;
    /**
     * Destroy all event listeners
     */
    ngOnDestroy(): void;
}
