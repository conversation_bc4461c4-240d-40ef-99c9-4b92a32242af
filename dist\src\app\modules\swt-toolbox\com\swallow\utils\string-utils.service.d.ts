import { XML } from '../xmlhandler/swt-xml.service';
export declare class StringUtils {
    private static decimalSeparatorTo;
    private static thousandsSeparatorTo;
    constructor();
    static AMOUNT_PATTERN0: number;
    static AMOUNT_PATTERN1: number;
    static AMOUNT_PATTERN2: number;
    static AMOUNT_PATTERN3: number;
    private static htmlTagsRegex;
    /**
    *	unformats a formatted amount
    *
    *	@param amount The amount string to be formatted
    *   @amountPattern:    0 or others : e.g 12,345,698.50
    *                      1: e.g 12.345.698,50
    *                      2: e.g 12 345 698.50
    *                      3: e.g 12 345 698,50
    *	@returns string The unformatted amount
    *
    * 	@langversion Angular
    *
    */
    static unformatAmount(formattedAmount: string, amountPattern: number): string;
    /**
   * function used to format amout with precision
   * used to unformat format amount but keep the last '.'
   * for the decimal part
   * the block of "," decimal seperator is removed because the "," is replaced by "." in expandMBTAmount()
   *  please see formatAmount() function
   * */
    static unformatAmountWithPrecision(formattedAmount: string, amountPattern: number, precision: number): string;
    /**
  *	Formats an amount
  *
  *	@param amount The amount string to be formatted
  *   @param dicimals The number of dicimals for a given currency
  *   @formattingOption: 0 or others : 12345698.50 -> 12,345,698.50
  *                      1: 12345698.50 -> 12.345.698,50
  *                      2: 12345698.50 -> 12 345 698.50
  *                      3: 12345698.50 -> 12 345 698,50
  *	@returns string The formatted amount
  *
  * 	@langversion Angular
  *
  */
    static formatAmount(amount: string, decimals: number, amountPattern: number): string;
    private static format;
    /**
  *  Expands 0 to an amount regarding to MBT marker
  *	@returns string The expanded amount
  *
  * 	@langversion Angular
  *
  */
    private static expandMBTAmount;
    /**
          *	Determines whether the specified string is numeric.
          *
          *	@param p_string The string.
          *
          *	@returns Boolean
          *
          * 	@langversion Angular
          *
          *	@tiptext
          */
    static isNumeric(p_string: string): boolean;
    /**
  *	Determines whether the specified string contains any characters.
  *
  *	@param p_string The string to check
  *
  *	@returns Boolean
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    static isEmpty(p_string: string): boolean;
    static isTrue(value: any): boolean;
    /**
  * Pads p_string with specified character to a specified length from the left.
  *
  *	@param p_string string to pad
  *
  *	@param p_padChar Character for pad.
  *
  *	@param p_length Length to pad to.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    static padLeft(p_string: string, p_padChar: string, p_length: number): string;
    /**
  * Pads p_string with specified character to a specified length from the right.
  *
  *	@param p_string string to pad
  *
  *	@param p_padChar Character for pad.
  *
  *	@param p_length Length to pad to.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    static padRight(p_string: string, p_padChar: string, p_length: number): string;
    /**
  *	Removes extraneous whitespace (extra spaces, tabs, line breaks, etc) from the
  *	specified string.
  *
  *	@param p_string The string whose extraneous whitespace will be removed.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    static removeExtraWhitespace(p_string: string): string;
    /**
  *	Removes whitespace from the front and the end of the specified
  *	string.
  *
  *	@param p_string The string whose beginning and ending whitespace will
  *	will be removed.
  *
  *	@returns string
  *
  * 	@langversion Angular
  *
  *	@tiptext
  */
    static trim(p_string: string): string;
    /**
    *	Removes whitespace from the front (left-side) of the specified string.
    *
    *	@param p_string The string whose beginning whitespace will be removed.
    *
    *	@returns string
    *
    * 	@langversion Angular
    *
    *	@tiptext
    */
    static trimLeft(p_string: string): string;
    /**
    *	Removes whitespace from the end (right-side) of the specified string.
    *
    *	@param p_string The string whose ending whitespace will be removed.
    *
    *	@returns string	.
    *
    * 	@langversion ActionScript 3.0
    *	@playerversion Flash 9.0
    *	@tiptext
    */
    static trimRight(p_string: string, trimChar?: string): string;
    /**
     * This part of code is used to ignore reserved chars inside a regex
     * @param regex
     * @return
     */
    static fixRegex(regex: string): string;
    /**
   * decodeXmlChars
   *
   * @param text:string
   * <AUTHOR> SwallowTech Tunisia
   *
   * this method is used to clean a string from unwanted encoded characters
   **/
    static decodeXmlChars(text: string): string;
    /**
     * replaceAll
     *
     * @param source: string
     * @param map: Object
     * <AUTHOR> SwallowTech Tunisia
     *
     * Replace all occurences of the map values in a given string
     **/
    static replaceAll(source: string, map: any): string;
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     * */
    static encode64(text: string): string;
    /**
  * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
  * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
  * getMenuWindow().decode64(text);
  **/
    static decode64(text: string): string;
    /**
  * codeXmlChars
  *
  * this method is used to clean a string from unwanted encoded characters
  **/
    static codeXmlChars(text: string, keepHtml?: boolean): string;
    /**
  * Removes an XML node from its parent root
  * */
    /**
  * Basic convertion of XMLList into an Array
  * the resulting array will contain either value or child XML for each node
  * */
    /**
     * Converts an XML into an Objects array collection
     * here datafield is your xml tag
     * */
    static formatDate(date: Date, patern: string): string;
    /**
     * Converts all url parameters into string
     */
    /**
  * Converts an object to XML without using SimpleXMLEncoder class
  *
  * <AUTHOR> SwallowTech Tunisia
  * */
    /**
  * Converts all url parameters string into an array
  */
    /**
     * Get url params as indexed array
     * */
    static getUrlParams(url: string): any[];
    /**
           * Clones an object and returns the cloned instance
           * */
    static cloneObject(obj: any): any;
    /**
           * counts number of occurences of pattern inside a given string
           * */
    static countOccurences(pattern: string, target: string): number;
    /**
       *  Substitutes "{n}" tokens within the specified string
       *  with the respective arguments passed in.
       *
       *  @param stringParam The string to make substitutions in.
       *  This string can contain special tokens of the form
       *  <code>{n}</code>, where <code>n</code> is a zero based index,
       *  that will be replaced with the additional parameters
       *  found at that index if specified.
       *
       *  @param words Additional parameters that can be substituted
       *  in the <code>stringParam</code> parameter at each <code>{n}</code>
       *  location, where <code>n</code> is an integer (zero based)
       *  index value into the array of values specified.
       *  If the first parameter is an array this array will be used as
       *  a parameter list.
       *  This allows reuse of this routine in other methods that want to
       *  use the ... words signature.
       *  For example <pre>
       *     public function myTracer(stringParam:String, ... words):void
       *     {
       *         label.text += StringUtil.substitute(stringParam, words) + "\n";
       *     } </pre>
       *
       *  @return New string with all of the <code>{n}</code> tokens
       *  replaced with the respective arguments specified.
       *
       *  @example
       *
       *  var stringParam:String = "here is some info '{0}' and {1}";
       *  trace(StringUtil.substitute(stringParam, 15.4, true));
       *
       *  // this will output the following string:
       *  // "here is some info '15.4' and true"
       */
    static substitute(stringParam: string, ...words: any[]): string;
    /**
      *  Returns <code>true</code> if the specified string is
      *  a single space, tab, carriage return, newline, or formfeed character.
      *
      *  @param str The String that is is being queried.
      *
      *  @return <code>true</code> if the specified string is
      *  a single space, tab, carriage return, newline, or formfeed character.
      */
    static isWhitespace(character: string): boolean;
    /**
     * Converts an associative array of parameters into an XML string,
     * The goal is to use Jaxb marshalling on java side.
     *
     * <AUTHOR> JABALLAH, SwallowTech Tunisia
     * */
    static getKVTypeTabAsXML(params: any, tableName: string, operation: string, tableLevel: string): XML;
    /**
     * Removes an XML node from its parent root
     * @param parentXml : XML
     * @param xmlToDelete :XML
     */
    static deleteXMLNode(parentXml: XML, xmlToDelete: XML): boolean;
}
