import { ElementRef, OnInit } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class AdvancedToolTip extends Container implements OnInit {
    private tipelement;
    private tipcommon;
    private _top;
    private _left;
    private _destroyTimeOut;
    child: any;
    constructor(tipelement: ElementRef, tipcommon: CommonService);
    ngOnInit(): void;
    top: number;
    left: number;
    destroy(): void;
    display(): void;
    timeOut: number;
    getChild(): any;
}
