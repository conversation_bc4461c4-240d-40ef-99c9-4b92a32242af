/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, ElementRef, NgZone } from '@angular/core';
//import { SwtAbstract } from "./swt-abstract";
import { Container } from "../containers/swt-container.component";
import { ContextMenu } from "./context-menu.component";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService, unsubscribeAllObservables } from "../utils/common.service";
import { FormControl, Validators } from "@angular/forms";
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/autoresize';
import 'tinymce/themes/silver';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var SwtTextArea = /** @class */ (function (_super) {
    tslib_1.__extends(SwtTextArea, _super);
    //----------------------------------------------------------------------------------------------------------
    function SwtTextArea(elem, commonService, zone) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._text = '';
        _this._htmlText = '';
        _this._editable = true;
        _this._enabled = true;
        _this._fontSize = 11;
        _this._doubleClickEnabled = true;
        _this.__verticalScrollPolicy = "auto";
        _this.__horizontalScrollPolicy = "auto";
        _this.elementId = Math.random().toString(36).substring(2);
        _this._verticalScrollPosition = 0;
        _this._selectionBeginIndex = 0;
        //= new ContextMenu();
        _this.contextmenuItems = [];
        _this.subscriptions = [];
        _this._required = false;
        _this.zone = zone;
        return _this;
    }
    Object.defineProperty(SwtTextArea.prototype, "contextMenu", {
        get: /**
         * @return {?}
         */
        function () {
            return this._contextMenu;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._contextMenu = value;
            this.contextmenuItems = this._contextMenu.customItems;
            /* creating list of ContextMenuItem dynamically - [START] */
            this.id_contextMenu = "contextMenu-" + Math.random().toString(36).substr(2, 5);
            /** @type {?} */
            var custom_menu_ul = $(this.elem.nativeElement).find('.custom-menu');
            //-Fix M5041/ISS-290.
            if (custom_menu_ul.length == 0) {
                /** @type {?} */
                var list = $(this.elem.nativeElement).append("<ul id=" + this.id_contextMenu + " class='custom-menu' ></ul>").find('ul');
                for (var index = 0; index < this.contextmenuItems.length; index++) {
                    list.append('<li data=\'' + this.contextmenuItems[index].label + '\'>' + this.contextmenuItems[index].label + '</li>');
                }
            }
            /** @type {?} */
            var contextmenuItems = this.contextmenuItems;
            $("#" + this.id_contextMenu + " li").bind("click", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                /** @type {?} */
                var contextMenuItemData = event.currentTarget.attributes[0].nodeValue;
                // alert(contextMenuItemData);
                /** @type {?} */
                var item = contextmenuItems.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.label == contextMenuItemData; }));
                if (item) {
                    item.MenuItemSelect();
                }
                $(".custom-menu").hide(100);
                $('.custom-menu').removeClass('openedFontSetting');
            }));
            $(document).on('click.contextMenu' + this.id_contextMenu, (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                $(".custom-menu").hide(100);
                $('.custom-menu').removeClass('openedFontSetting');
            }));
            /* creating list of ContextMenuItem dynamically - [END] */
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "selectionBeginIndex", {
        get: /**
         * @return {?}
         */
        function () {
            //console.log("get selectionBeginIndex ===> position",   this.editor.selection.getRng().startOffset  );
            if (this.editor)
                this._selectionBeginIndex = this.editor.selection.getRng().startOffset;
            return this._selectionBeginIndex;
        },
        //---selectionBeginIndex-------------------------------------------------------------------------------------------------------
        set: 
        //---selectionBeginIndex-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            //console.log("set selectionBeginIndex ===>value :",value );
            this._selectionBeginIndex = Number(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "verticalScrollPosition", {
        get: /**
         * @return {?}
         */
        function () {
            //console.log("verticalScrollPosition ===>", $(this.editor.getContainer()).position().top);
            return tinymce.DOM.getViewPort(this.editor.getWin()).y;
        },
        //---verticalScrollPosition-------------------------------------------------------------------------------------------------------
        set: 
        //---verticalScrollPosition-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._verticalScrollPosition = Number(value);
            //console.log('this._verticalScrollPosition :',$(this.editor.getWin()) )
            $(this.editor.getDoc()).find('HTML').animate({ scrollTop: this._verticalScrollPosition }, 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        //---width-------------------------------------------------------------------------------------------------------
        set: 
        //---width-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._width = this.adaptUnit(value, "auto");
            if (this.editor) {
                this.setStyle("width", this._width, this.elem.nativeElement);
                this.setStyle("width", "100%", this.editor.getContainer());
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        //---height-------------------------------------------------------------------------------------------------------
        set: 
        //---height-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = this.adaptUnit(value, "auto");
            if (this.editor) {
                this.setStyle("height", this._height, this.elem.nativeElement);
                this.setStyle("height", "calc(100% + 20px)", this.editor.getContainer());
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "required", {
        get: /**
         * @return {?}
         */
        function () {
            return this._required;
        },
        /* input to hold component visibility */
        set: /* input to hold component visibility */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) == "string") {
                if (value === 'true') {
                    this._required = true;
                }
                else {
                    this._required = false;
                }
            }
            else {
                this._required = value;
            }
            if (this._required && !this.text && this.enabled == true)
                $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
            else
                $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "doubleClickEnabled", {
        get: /**
         * @return {?}
         */
        function () { return this._doubleClickEnabled; },
        //---doubleClickEnabled-------------------------------------------------------------------------------------------------------
        set: 
        //---doubleClickEnabled-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (this.adaptValueAsBoolean(value) !== this._doubleClickEnabled) {
                this._doubleClickEnabled = this.adaptValueAsBoolean(value);
                if (this.editor && this._doubleClickEnabled) {
                    this.editor.on('dblclick', (/**
                     * @return {?}
                     */
                    function () {
                        _this.doubleClick();
                        // this.doubleClick_.emit();
                    }));
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            if (this.editor) {
                /** @type {?} */
                var datatmp = this.editor.getContent().
                    replace(/<br\s*\/?>/ig, "\n")
                    .replace(/&nbsp;/ig, ' ')
                    .replace(/<[^>]*>/ig, '')
                    .replace(/<\/[^>]*>/ig, '');
                datatmp = $($.parseHTML(datatmp)).text();
                // console.log('get text datatmp:', datatmp) 
                return this.validateMaxChar(this.validateRestrict(datatmp));
            }
            return this._text;
        },
        //---text-------------------------------------------------------------------------------------------------------
        set: 
        //---text-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value != null && value != undefined) {
                if (this.isHTML(value)) {
                    value = value.replace(/&<;/ig, '&#60').replace(/>/g, '&#62');
                }
                if (value !== this._text) {
                    this._text = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\r\n|\r|\n)/g, '<br>');
                }
                if (this.firstCall) {
                    this.originalValue = this._text;
                    this.firstCall = false;
                }
                else {
                    this._spyChanges(this._text);
                }
                if (this.editor) {
                    this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._text)));
                }
            }
            else {
                if (this.editor) {
                    this.editor.setContent("");
                }
            }
            if (this.required && !this.text && this.enabled == true)
                $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
            else
                $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} text
     * @return {?}
     */
    SwtTextArea.prototype.htmlToText = /**
     * @param {?} text
     * @return {?}
     */
    function (text) {
        return text;
    };
    Object.defineProperty(SwtTextArea.prototype, "htmlText", {
        get: /**
         * @return {?}
         */
        function () { return this.editor.getContent(); },
        //---htmlText-------------------------------------------------------------------------------------------------------
        set: 
        //---htmlText-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (!value)
                value = "";
            if (value !== this._htmlText) {
                this._htmlText = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\n)/g, '<br>').replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
            }
            if (!this.editor) {
                /** @type {?} */
                var intervalId_1 = setInterval((/**
                 * @return {?}
                 */
                function () {
                    if (_this.editor) {
                        clearInterval(intervalId_1);
                        _this.editor.setContent(_this.validateMaxChar(_this.validateRestrict(_this._htmlText)));
                    }
                }), 1000);
            }
            else {
                this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._htmlText)));
            }
        },
        enumerable: true,
        configurable: true
    });
    ;
    Object.defineProperty(SwtTextArea.prototype, "verticalScrollPolicy", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__verticalScrollPolicy;
        },
        //---verticalScrollPolicy------------------------------------------------------------------------------------------------
        set: 
        //---verticalScrollPolicy------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__verticalScrollPolicy = (value == "on" ? "scroll" : (value == "off" ? "hidden" : "auto"));
            if (this.editor) {
                this.editor.getBody().style.overflowY = this.__verticalScrollPolicy;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "horizontalScrollPolicy", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__horizontalScrollPolicy;
        },
        //---horizontalScrollPolicy------------------------------------------------------------------------------------------------
        set: 
        //---horizontalScrollPolicy------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__horizontalScrollPolicy = (value == "on" ? "scroll" : (value == "off" ? "hidden" : "auto"));
            if (this.editor) {
                this.editor.getBody().style.overflowX = this.__horizontalScrollPolicy;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () { return this._editable; },
        //---Editable-------------------------------------------------------------------------------------------------------
        set: 
        //---Editable-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._editable = this.adaptValueAsBoolean(value);
            //-Editable
            if (this.editor) {
                if (!this.editor.getBody()) {
                    /** @type {?} */
                    var intervalId_2 = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.editor.getBody()) {
                            clearInterval(intervalId_2);
                            _this.editor.getBody().setAttribute('contenteditable', _this._editable);
                            if (_this._editable)
                                $(_this.editor.getBody()).removeClass('pointerEvents');
                            else
                                $(_this.editor.getBody()).addClass('pointerEvents');
                        }
                    }), 1000);
                }
                else {
                    this.editor.getBody().setAttribute('contenteditable', this._editable);
                    if (this._editable)
                        $(this.editor.getBody()).removeClass('pointerEvents');
                    else
                        $(this.editor.getBody()).addClass('pointerEvents');
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    ;
    Object.defineProperty(SwtTextArea.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        //---enabled------------------------------------------------------------------------------------------------------
        set: 
        //---enabled------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._enabled = this.adaptValueAsBoolean(value);
            //console.log('set disabled !!!',this.editor)
            if (this.editor) {
                if (!this.editor.getBody()) {
                    /** @type {?} */
                    var intervalId_3 = setInterval((/**
                     * @return {?}
                     */
                    function () {
                        if (_this.editor.getBody()) {
                            clearInterval(intervalId_3);
                            if (!_this.enabled) {
                                $(_this.elem.nativeElement).addClass('disabled-container');
                                _this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                                _this.editor.getBody().disabled = true;
                                //console.log("this.editor.getBody()",this.editor.getBody());
                            }
                            else {
                                $(_this.elem.nativeElement).removeClass('disabled-container');
                                _this.editor.getBody().style.backgroundColor = "white";
                                _this.editor.getBody().disabled = false;
                                //console.log("this.editor.getBody()",this.editor.getBody());
                            }
                        }
                    }), 1000);
                }
                else {
                    if (!this.enabled) {
                        $(this.elem.nativeElement).addClass('disabled-container');
                        this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                        this.editor.getBody().disabled = true;
                        //console.log("this.editor.getBody()",this.editor.getBody());
                    }
                    else {
                        $(this.elem.nativeElement).removeClass('disabled-container');
                        this.editor.getBody().style.backgroundColor = "white";
                        this.editor.getBody().disabled = false;
                        //console.log("this.editor.getBody()",this.editor.getBody());
                    }
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "fontWeight", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__fontWeight;
        },
        //---fontWeight------------------------------------------------------------------------------------------------------
        set: 
        //---fontWeight------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__fontWeight = value;
            if (this.editor) {
                this.editor.getBody().style.fontWeight = this.fontWeight;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "fontSize", {
        get: /**
         * @return {?}
         */
        function () {
            return this._fontSize;
        },
        //---fontSize------------------------------------------------------------------------------------------------------
        set: 
        //---fontSize------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._fontSize = Number(value);
            if (this.editor) {
                this.editor.getBody().style.fontSize = this.fontSize + "px";
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "textColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this._textColor;
        },
        //----textColor------------------------------------------------------------------------------------------------------
        set: 
        //----textColor------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._textColor = value;
            if (this.editor) {
                this.editor.getBody().style.color = this._textColor;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "color", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__color;
        },
        //----Color------------------------------------------------------------------------------------------------------
        set: 
        //----Color------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__color = value;
            if (this.editor) {
                this.editor.getBody().style.color = this.__color;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "textAlign", {
        get: /**
         * @return {?}
         */
        function () {
            return this._textAlign;
        },
        //----textAlign------------------------------------------------------------------------------------------------------
        set: 
        //----textAlign------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._textAlign = value;
            if (this.editor) {
                this.editor.getBody().style.textAlign = this._textAlign;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextArea.prototype, "backgroundColor", {
        get: /**
         * @return {?}
         */
        function () {
            return this.__backgroundColor;
        },
        //----textAlign------------------------------------------------------------------------------------------------------
        set: 
        //----textAlign------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.__backgroundColor = value;
            if (this.editor) {
                this.editor.getBody().style.backgroundColor = this.__backgroundColor;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtTextArea.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        // const popperContentEl = this.elem.nativeElement.querySelector('popper-content');
        // this.elem.nativeElement.appendChild(popperContentEl);
        //console.log("SwtTextArea --------------->", window.tinymce);
        window.tinyMCEPreInit = {
            base: '/assets/tinymce',
            suffix: '.min'
        };
        //-M5117 / ISS-Q : Enrichissement in Send Mail exist but is hidden.
        if (this.elem.nativeElement.nodeName.toLowerCase() != "swtrichtexteditor") {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtTextArea');
            tinymce.init({
                skin: false,
                content_css: './assets/css/tinymce/SwtTextArea.css',
                menubar: false,
                toolbar: false,
                branding: false,
                width: "0",
                height: "0",
                body_class: "swtTextArea",
                setup: (/**
                 * @param {?} ed
                 * @return {?}
                 */
                function (ed) {
                    var _this = this;
                    ed.on("keydown", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        //console.log('event.keyCode :',event.keyCode, ' this.editable :',this.editable)
                        if (_this.editable) {
                            if (event.keyCode != 13 && event.keyCode != 46 && event.keyCode != 8 && event.keyCode != 36 && event.keyCode != 37 && event.keyCode != 38 && event.keyCode != 39 && event.keyCode != 40) {
                                if (_this.restrict) {
                                    /** @type {?} */
                                    var controlRestrict = new FormControl(event.key, Validators.pattern('[' + _this.restrict + ']'));
                                    //console.log('controlRestrict.valid :',controlRestrict.valid)
                                    if (!controlRestrict.valid) {
                                        event.preventDefault();
                                        return false;
                                    }
                                }
                                if (_this.maxChars) {
                                    /** @type {?} */
                                    var controlMaxChar = new FormControl(_this.text, Validators.maxLength(_this.maxChars - 1));
                                    //console.log('controlMaxChar.valid :',controlMaxChar.valid)
                                    if (!controlMaxChar.valid && !_this.editor.selection.getContent()) {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        event.stopImmediatePropagation();
                                        return false;
                                    }
                                }
                                if (event.keyCode == 13 && !event.shiftKey) {
                                    tinymce.EditorManager.execCommand('InsertLineBreak');
                                    tinymce.dom.Event.cancel(event);
                                    return;
                                }
                                _this.keyDown();
                                _this.onKeyDown_.emit(_this.text);
                            }
                        }
                    })),
                        ed.on("keyup", (/**
                         * @param {?} event
                         * @return {?}
                         */
                        function (event) {
                            if (_this.maxChars) {
                                //console.log(' this.editor.getContent() ', this.editor.getContent() )
                                /** @type {?} */
                                var controlMaxChar = new FormControl(_this.editor ? _this.editor.getContent() : _this.text, Validators.maxLength(_this.maxChars));
                                if (!controlMaxChar.valid && !_this.editor.selection.getContent()) {
                                    //this.editor.undoManager.undo();
                                    //-M5204 By Rihab.JB @15/01/2021 : The copy/paste must be take in account the maxchars of component.
                                    _this.text = _this.validateMaxChar(_this.validateRestrict(_this.text));
                                }
                                /*else {
                                    this.editor.undoManager.reset();
                                }*/
                            }
                        }));
                }).bind(this)
            });
        }
        else {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRichTextEditor');
            tinymce.init({
                skin: false,
                content_css: './assets/css/tinymce/SwtTextArea.css',
                body_class: "swtTextArea",
                plugins: ['advlist', 'lists'],
                menubar: false,
                branding: false,
                width: "0",
                height: "0",
                toolbar: ["fontselect  |fontsizeselect | bold italic underline |forecolor | alignleft aligncenter alignright alignjustify | numlist"],
                font_formats: "mirza; aref; monospace; serif; times; timesRoman; arial; verdana; courier; courierNew; geneva; georgia ; helvetica",
                fontsize_formats: '8px 9px 10px 11px 12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 48px 72px ',
                setup: (/**
                 * @param {?} ed
                 * @return {?}
                 */
                function (ed) {
                    var _this = this;
                    ed.on("keydown", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        if (event.keyCode != 46 && event.keyCode != 8 && event.keyCode != 36 && event.keyCode != 37 && event.keyCode != 38 && event.keyCode != 39 && event.keyCode != 40) {
                            if (_this.restrict) {
                                /** @type {?} */
                                var controlRestrict = new FormControl(event.key, Validators.pattern('[' + _this.restrict + ']'));
                                //console.log('controlRestrict.valid :',controlRestrict.valid)
                                if (!controlRestrict.valid) {
                                    event.preventDefault();
                                    return false;
                                }
                            }
                            if (_this.maxChars) {
                                /** @type {?} */
                                var controlMaxChar = new FormControl(_this.text, Validators.maxLength(_this.maxChars - 1));
                                //console.log('controlMaxChar.valid :',controlMaxChar.valid)
                                if (!controlMaxChar.valid && !_this.editor.selection.getContent()) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    event.stopImmediatePropagation();
                                    return false;
                                }
                            }
                            /*
                                if (event.keyCode == 13 && !event.shiftKey) {
                                    tinymce.EditorManager.execCommand('InsertLineBreak')
                                    tinymce.dom.Event.cancel(event);
                                    return;
                                }*/
                            _this.keyDown();
                            _this.onKeyDown_.emit(_this.text);
                        }
                        /*setTimeout(() => {
                            this._spyChanges(this.text);
                        }, 0);*/
                    })),
                        ed.on("keyup", (/**
                         * @param {?} event
                         * @return {?}
                         */
                        function (event) {
                            if (_this.maxChars) {
                                /** @type {?} */
                                var controlMaxChar = new FormControl(_this.text, Validators.maxLength(_this.maxChars));
                                if (!controlMaxChar.valid && !_this.editor.selection.getContent()) {
                                    _this.editor.undoManager.undo();
                                }
                                else {
                                    _this.editor.undoManager.reset();
                                }
                            }
                        }));
                }).bind(this)
            });
        }
        if (this.elem.nativeElement.nodeName.toLowerCase() != "swtrichtexteditor") {
            $($(this.elem.nativeElement).find('.tox-editor-header')).hide();
        }
        tinymce.EditorManager.execCommand('mceAddEditor', true, this.elementId);
        this.editor = tinymce.get(this.elementId);
        if (this.editor) {
            //- Init 
            this.editor.on('init', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                _this.editor.on('contextmenu', (/**
                 * @param {?} e
                 * @return {?}
                 */
                function (e) {
                    if (e.which === 3) {
                        $(".custom-menu").hide(100);
                        $('.custom-menu').removeClass('openedFontSetting');
                        _this.onRightClickTextArea(e);
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        e.stopPropagation();
                    }
                }));
                //-ToolTip
                if (_this.editor.getBody())
                    _this.editor.getBody().setAttribute('title', _this.toolTip);
                _this.toolTipObject = $(_this.elem.nativeElement);
                _this.toolTipObject.tooltip({
                    position: { my: "left bottom-20", at: "left bottom+45" }
                });
                $(_this.editor.getBody()).addClass(_this.styleName);
                //To avoid getting originaleValue as undefined the spy wont work.
                if (_this.originalValue == undefined)
                    _this.originalValue = "";
                //-Fix to set the previous text within textarea after the reload of the page.
                if (_this.editor.getContent()) {
                    _this.editor.setContent(_this.editor.getContent());
                }
                else if (_this._text && _this._text != null && _this._text != undefined) {
                    _this.editor.setContent(_this.validateMaxChar(_this.validateRestrict(_this._text)));
                }
                _this.setStyle("width", _this._width, _this.elem.nativeElement);
                _this.setStyle("width", "100%", _this.editor.getContainer());
                _this.setStyle("height", _this._height, _this.elem.nativeElement);
                _this.setStyle("height", "100%", _this.editor.getContainer());
                _this.editor.getBody().style.fontSize = _this.fontSize + "px";
                _this.editor.getBody().style.fontFamily = 'verdana';
                if (_this.required && !_this.text && _this.enabled == true)
                    $(_this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(_this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
                if (_this.toolTipPreviousValue)
                    $(_this.elem.nativeElement).find('.tox-tinymce').addClass('border-orange-previous');
                else
                    $(_this.elem.nativeElement).find('.tox-tinymce').removeClass('border-orange-previous');
                // [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}
                if (_this.textColor)
                    _this.editor.getBody().style.color = _this.textColor;
                if (_this.color)
                    _this.editor.getBody().style.color = _this.color;
                if (_this.textAlign)
                    _this.editor.getBody().style.textAlign = _this.textAlign;
                if (_this.backgroundColor)
                    _this.editor.getBody().style.backgroundColor = _this.backgroundColor;
                if (_this.backgroundColor)
                    _this.editor.getBody().style.backgroundColor = _this.backgroundColor;
                if (_this.verticalScrollPolicy)
                    _this.editor.getBody().style.overflowY = _this.verticalScrollPolicy;
                if (_this.horizontalScrollPolicy)
                    _this.editor.getBody().style.overflowX = _this.horizontalScrollPolicy;
                if (_this.constructor.name.toLowerCase() != "swtrichtexteditor") {
                    if (_this.paddingTop)
                        _this.setStyle("padding-top", _this.paddingTop, $(_this.elem.nativeElement));
                    if (_this.paddingBottom)
                        _this.setStyle("padding-bottom", _this.paddingBottom, $(_this.elem.nativeElement));
                    if (_this.paddingLeft)
                        _this.setStyle("padding-left", _this.paddingLeft, $(_this.elem.nativeElement));
                    if (_this.paddingRight)
                        _this.setStyle("padding-right", _this.paddingRight, $(_this.elem.nativeElement));
                    if (_this.marginTop)
                        _this.setStyle("margin-top", _this.marginTop, $(_this.elem.nativeElement));
                    if (_this.marginBottom)
                        _this.setStyle("margin-bottom", _this.marginBottom, $(_this.elem.nativeElement));
                    if (_this.marginLeft)
                        _this.setStyle("margin-left", _this.marginLeft, $(_this.elem.nativeElement));
                    if (_this.marginRight)
                        _this.setStyle("margin-right", _this.marginRight, $(_this.elem.nativeElement));
                }
                //tinymce.execCommand('mceFocus', true,  this.editor );      
                // console.log('---------- .tox-edit-area__iframe" ', $($($(this.editor.getContainer()).find(".tox-edit-area__iframe"))))
                // $($(this.editor.getContainer()).find(".tox-edit-area__iframe")).width('100%');
                $('#' + e.target.id + '_ifr').removeAttr('title');
            }));
            this.editor.on('ResizeEditor', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                //this.setStyle("height", "100%", this.editor.getContainer());
                //event.preventDefault();
            }));
            //-paste  
            this.editor.on('paste', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                //event.preventDefault();
                /** @type {?} */
                var content = ((event.originalEvent || event).clipboardData || window.clipboardData).getData('Text').toString();
                //console.log('-----------on paste--------------',content);
                //-Fix M5041/ISS-270
                //this.text += content.toString();
                //event.preventDefault();
            }));
            this.editor.on('change', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                if (_this.required && !_this.text && _this.enabled == true)
                    $(_this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(_this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
            }));
            this.editor.on('input', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                if (_this.required && !_this.text && _this.enabled == true)
                    $(_this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(_this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
                _this.change();
                _this.change_.emit(_this.text);
            }));
            //-KeyUp  
            this.editor.on('keyup', (/**
             * @return {?}
             */
            function () {
                _this.keyUp();
                _this.onKeyUp_.emit(_this.text);
                //-Rihab.JB @05/01/2021 : this is added because the event "input" is not working on IE.
                if (_this.getBroserType().toLowerCase() == 'ie') {
                    _this.change();
                    _this.change_.emit(_this.text);
                }
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this._spyChanges(_this.text);
                }), 0);
            }));
            //-Click 
            this.editor.on('click', (/**
             * @return {?}
             */
            function () {
                focusManager.focusTarget = _this;
                _this.click();
                _this.onClick_.emit(_this.text);
            }));
            //-Double Click 
            if (this.doubleClickEnabled) {
                this.editor.on('dblclick', (/**
                 * @return {?}
                 */
                function () {
                    //console.log('------------dblclick-------------');
                    _this.doubleClick();
                    _this.doubleClick_.emit();
                }));
            }
            //-Scroll 
            $(this.editor.getWin()).bind('scroll', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                // console.log('Editor window scrolled!');
                _this.scroll_.emit(event);
                //this.scroll();
                e.preventDefault();
            }));
            //-mouseDown 
            this.editor.on('mousedown', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------mousedown-------------');
                $(".custom-menu").hide(100);
                $('.custom-menu').removeClass('openedFontSetting');
            }));
            //-mouseUp 
            this.editor.on('mouseup', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------mouseup-------------');
            }));
            //-mouseOver 
            this.editor.on('mouseover', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------mouseover-------------');
                if (_this.toolTipPreviousValue) {
                    // Get the element that the mouse is over
                    /** @type {?} */
                    var target = _this.elem.nativeElement;
                    // Create a new tooltip element
                    /** @type {?} */
                    var tooltip_1 = document.createElement('div');
                    tooltip_1.textContent = _this.toolTipPreviousValue;
                    // Position the tooltip next to the element
                    /** @type {?} */
                    var rect = target.getBoundingClientRect();
                    tooltip_1.style.position = 'absolute';
                    tooltip_1.style.left = rect.left + window.pageXOffset + 'px';
                    tooltip_1.style.top = rect.top + window.pageYOffset + rect.height + 'px';
                    tooltip_1.classList.add('basic-tooltip');
                    tooltip_1.classList.add('border-orange-previous');
                    // Add the tooltip to the document
                    document.body.appendChild(tooltip_1);
                    // Remove the tooltip when the mouse moves away
                    target.addEventListener('mouseout', (/**
                     * @return {?}
                     */
                    function () {
                        try {
                            document.body.removeChild(tooltip_1);
                        }
                        catch (e) {
                        }
                    }));
                }
                console.log("🚀 ~ file: swt-text-area.component.ts:791 ~ SwtTextArea ~ this.editor.on ~ e:", e);
            }));
            //-mouseMove 
            this.editor.on('mousemove', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------mousemove-------------');
            }));
            //-mouseOut 
            this.editor.on('mouseout', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------mouseout-------------');
            }));
            //-mouseWheel 
            this.editor.on('wheel', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //console.log('------------wheel-------------', );
            }));
            //-focus  
            this.editor.on('focus', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                if ($(_this.editor.getContainer())) {
                    //-Set  border color on focus
                    $(_this.editor.getContainer()).find('.tox-editor-container').toggleClass('focused');
                }
                focusManager.focusTarget = _this;
                _this.focusIn();
                _this.focusIn_.emit(_this.text);
            }));
            //-blur  
            this.editor.on('blur', (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                if ($(_this.editor.getContainer())) {
                    //-Set  border color on focus
                    $(_this.editor.getContainer()).find('.tox-editor-container').toggleClass('focused');
                }
                _this.focusOut();
                _this.onFocusOut_.emit(_this.text);
                _this.keyFocusChange(e);
                _this.keyFocusChange_.emit(_this.text);
            }));
            //-Editable
            if (this.editor.getBody()) {
                this.editor.getBody().setAttribute('contenteditable', this._editable);
                if (this._editable)
                    $(this.editor.getBody()).removeClass('pointerEvents');
                else
                    $(this.editor.getBody()).addClass('pointerEvents');
            }
            //-Enabled
            if (this.editor.getBody()) {
                if (!this.enabled) {
                    $(this.elem.nativeElement).addClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                }
                else {
                    $(this.elem.nativeElement).removeClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "white";
                }
            }
        }
    };
    /**
     * @private
     * @param {?} str
     * @return {?}
     */
    SwtTextArea.prototype.isHTML = /**
     * @private
     * @param {?} str
     * @return {?}
     */
    function (str) {
        /** @type {?} */
        var a = document.createElement('div');
        a.innerHTML = str;
        for (var c = a.childNodes, i = c.length; i--;) {
            if (c[i].nodeType == 1)
                return true;
        }
        return false;
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtTextArea.prototype.onRightClickTextArea = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        event.preventDefault();
        /** @type {?} */
        var screenVerion = $('.screenVerion');
        if (screenVerion.length > 0) {
            screenVerion.addClass('hidden');
            $('.custom-menu').addClass('openedFontSetting');
        }
        /** @type {?} */
        var __this = this;
        // Show contextmenu
        /** @type {?} */
        var contextmenu = $(this.elem.nativeElement).find('.custom-menu');
        /** @type {?} */
        var windowWidth = $(window).width();
        /** @type {?} */
        var containerLeft = $(this.editor.getContainer()).position().left;
        //-Fix M5041/ISS-159-B.
        /** @type {?} */
        var left = $(this.editor.getContainer()).position().left > event.pageX + $(contextmenu).width() ? event.pageX + $(this.editor.getContainer()).position().left : event.pageX + $(this.editor.getContainer()).position().left - $(contextmenu).width();
        if ((containerLeft > event.pageX + $(contextmenu).width()) && (containerLeft + event.pageX + $(contextmenu).width() < windowWidth)) {
            left = event.pageX + containerLeft;
        }
        else {
            left = event.pageX + containerLeft - $(contextmenu).width();
        }
        contextmenu.finish().toggle(100).css('position', 'absolute').css({
            top: event.clientY + $(this.editor.getContainer()).position().top,
            left: left
        });
        event.preventDefault();
    };
    /**
     * @private
     * @param {?} e
     * @return {?}
     */
    SwtTextArea.prototype.isVisible = /**
     * @private
     * @param {?} e
     * @return {?}
     */
    function (e) {
        if (e)
            return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
        else
            return false;
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    SwtTextArea.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            delete this._contextMenu;
            delete this.contextmenuItems;
            $("#" + this.id_contextMenu + " li").unbind("click");
            _super.prototype.ngOnDestroy.call(this);
            if (this.editor) {
                $(this.editor.getWin()).unbind('scroll');
            }
            $('.tox-tinymce-aux').remove();
            $('.ui-helper-hidden-accessible').remove();
            tinymce.execCommand('mceRemoveControl', true, this.elementId);
            tinymce.EditorManager.execCommand('mceRemoveEditor', true, this.elementId);
            //- unscbscribe all Observables. 
            this.subscriptions = unsubscribeAllObservables(this.subscriptions);
            $(document).off('click.contextMenu' + this.id_contextMenu);
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    /**
     * @return {?}
     */
    SwtTextArea.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    SwtTextArea.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTextArea',
                    template: "\n        <textarea    class=\"txtAreaClass\" id=\"{{elementId}}\"   ></textarea> \n      ",
                    styles: ["\n        .txtAreaClass{\n            height: 100%;\n            width: 100%;\n            display: none !important;        \n        }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtTextArea.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: NgZone }
    ]; };
    SwtTextArea.propDecorators = {
        contextMenu: [{ type: Input }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        selectionBeginIndex: [{ type: Input, args: ['selectionBeginIndex',] }],
        verticalScrollPosition: [{ type: Input, args: ['verticalScrollPosition',] }],
        width: [{ type: Input, args: ['width',] }],
        height: [{ type: Input, args: ['height',] }],
        required: [{ type: Input, args: ['required',] }],
        doubleClickEnabled: [{ type: Input }],
        text: [{ type: Input }],
        htmlText: [{ type: Input }],
        verticalScrollPolicy: [{ type: Input }],
        horizontalScrollPolicy: [{ type: Input }],
        editable: [{ type: Input }],
        enabled: [{ type: Input }],
        fontWeight: [{ type: Input }],
        fontSize: [{ type: Input }],
        textColor: [{ type: Input }],
        color: [{ type: Input }],
        textAlign: [{ type: Input }],
        backgroundColor: [{ type: Input }]
    };
    return SwtTextArea;
}(Container));
export { SwtTextArea };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._htmlText;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._fontSize;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._textColor;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__color;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._doubleClickEnabled;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__backgroundColor;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__verticalScrollPolicy;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__horizontalScrollPolicy;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.zone;
    /** @type {?} */
    SwtTextArea.prototype.editor;
    /** @type {?} */
    SwtTextArea.prototype.elementId;
    /** @type {?} */
    SwtTextArea.prototype._verticalScrollPosition;
    /** @type {?} */
    SwtTextArea.prototype._selectionBeginIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._contextMenu;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.contextmenuItems;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.id_contextMenu;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._required;
    /** @type {?} */
    SwtTextArea.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.commonService;
    /* Skipping unhandled member: [x: string]: any;*/
    /* Skipping unhandled member: ;*/
    /* Skipping unhandled member: ;*/
}
//# sourceMappingURL=data:application/json;base64,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