/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { VBox } from '../../../../controls/swt-vbox.component';
import { SwtLabel } from '../../../../controls/swt-label.component';
import { ExternalInterface } from '../../../../utils/external-interface.service';
import { SwtAlert } from '../../../../utils/swt-alert.service';
import { Alert } from '../../../../utils/alert.component';
var ConfigurableToolTip = /** @class */ (function (_super) {
    tslib_1.__extends(ConfigurableToolTip, _super);
    function ConfigurableToolTip(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.recalculateEnable = false;
        _this.clickable = false;
        _this.swtAlert = new SwtAlert(commonService);
        return _this;
    }
    /**
     * @return {?}
     */
    ConfigurableToolTip.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.dataForLabel.text = ExternalInterface.call('getBundle', 'text', 'label-dataFor', 'Data For');
        this.labelnumberAccounts.text = ExternalInterface.call('getBundle', 'text', 'label-numberAccounts', 'Number of accounts');
        this.labelnewDataExistFor.text = ExternalInterface.call('getBundle', 'text', 'label-newDataExistFor', 'New data exist for');
        this.labelincomplete.text = ExternalInterface.call('getBundle', 'text', 'label-incomplete', 'Incomplete');
        this.labelinconsistent.text = ExternalInterface.call('getBundle', 'text', 'label-inconsistent', 'Inconsistent');
        this.labelLastUpdate.text = ExternalInterface.call('getBundle', 'text', 'label-LastUpdate', 'Last Update');
        // this.customTooltip.focusOut = this.boxRollOutEventListner;
    };
    /**
     * @return {?}
     */
    ConfigurableToolTip.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.createCustomTip();
        jQuery(this.customTooltip.domElement).mouseleave((/**
         * @return {?}
         */
        function () {
            jQuery(_this.customTooltip.domElement).off('mouseleave');
            _this.processBox.removeTooltip();
        }));
    };
    /**
     * @private
     * @return {?}
     */
    ConfigurableToolTip.prototype.boxRollOutEventListner = /**
     * @private
     * @return {?}
     */
    function () {
        console.log("ConfigurableToolTip -> boxRollOutEventListner -> boxRollOutEventListner");
    };
    /**
     * @private
     * @return {?}
     */
    ConfigurableToolTip.prototype.createCustomTip = /**
     * @private
     * @return {?}
     */
    function () {
        try {
            this.clickable = false;
            if (this.dataArray['ENTITY']) {
                this.dataForLabelValue.text = this.dataArray['ENTITY'] + ' / ' + this.dataArray['CURRENCY'];
                this.labelnumberAccountsValue.text = this.dataArray['ACCOUNTS_NUMBER'];
                this.labelnewDataExistForValue.text = this.dataArray['ACCOUNTS_NEW_DATA'];
                if (parseInt(this.dataArray['ACCOUNTS_NEW_DATA'])) {
                    this.labelnewDataExistForValue.color = '#FF9900';
                }
                this.labelincompleteValue.text = this.dataArray['ACCOUNTS_INCOMPLETE'];
                if (parseInt(this.dataArray['ACCOUNTS_INCOMPLETE'])) {
                    this.labelincompleteValue.color = 'red';
                }
                this.labelinconsistentValue.text = this.dataArray['ACCOUNTS_INCONSISTENT'];
                if (parseInt(this.dataArray['ACCOUNTS_INCONSISTENT'])) {
                    this.labelinconsistentValue.color = 'red';
                }
                this.labelLastUpdateValue.text = this.dataArray['END_DATE'];
                if (this.recalculateEnable && this.dataArray['RECALCULATE'] === 'Y') {
                    if (this.dataArray['IS_ALREADY_RUNNING'] === 'Y') {
                        this.requestRecalculation.text = ExternalInterface.call('getBundle', 'text', 'label-recalculationInProgress', 'Calculation in progress');
                        // this.requestRecalculation.enabled = false;
                        this.requestRecalculation.color = "grey";
                    }
                    else {
                        this.clickable = true;
                        this.requestRecalculation.text = ExternalInterface.call('getBundle', 'text', 'label-requestRecalculation', 'A data recalculation is needed');
                        this.requestRecalculation.color = "red";
                        // this.requestRecalculation.enabled = false;
                    }
                }
            }
        }
        catch (error) {
        }
    };
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     */
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    ConfigurableToolTip.prototype.recalculateDataAlert = /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.clickable) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-recalculateConfirm', 'Are you sure? This may take some time?'), //text
            ExternalInterface.call('getBundle', 'text', 'label-recalculateConfirmAlertTitle', 'Are you sure? This may take some time?'), Alert.OK | Alert.CANCEL, this, //parent
            this.alertListener.bind(this), //close handler
            null, Alert.CANCEL); //icon and default button
        }
    };
    /**
     * This function is used to listen to the alert
     *
     * @param eventObj CloseEvent
     */
    /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    ConfigurableToolTip.prototype.alertListener = /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    function (eventObj) {
        // Checks for Alert OK
        if (eventObj.detail == Alert.OK) {
            // Recalculate data if "OK" is clicked
            this.parentDocument.recalculateDataFunction();
        }
    };
    ConfigurableToolTip.decorators = [
        { type: Component, args: [{
                    selector: 'ConfigurableToolTip',
                    template: "\n        <VBox  paddingLeft=\"3\"  paddingRight=\"3\" paddingTop=\"3\" #customTooltip width=\"100%\" height=\"100%\" verticalGap=\"2\"> \n            <HBox> <SwtLabel  #dataForLabel text='Date For'    fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #dataForLabelValue    fontWeight=\"normal\">  </SwtLabel> </HBox>\n            <HBox> \n            <VBox  width=\"55%\" style=\"border-right: 1px solid black\">\n            <HBox ><SwtLabel  #labelnumberAccounts text='Number of accounts'    fontWeight=\"normal\" width=\"70%\">  </SwtLabel> <SwtLabel  #labelnumberAccountsValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox ><SwtLabel  #labelnewDataExistFor text='New data exist for'    fontWeight=\"normal\" width=\"70%\">  </SwtLabel> <SwtLabel #labelnewDataExistForValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            </VBox>\n            \n            <VBox  width=\"45%\">\n            <HBox> <SwtLabel  #labelincomplete text='Incomplete'  width=\"50%\"  fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #labelincompleteValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox> <SwtLabel  #labelinconsistent text='Inconsistent'  width=\"50%\"   fontWeight=\"normal\">  </SwtLabel> <SwtLabel #labelinconsistentValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            </VBox>\n            </HBox>\n            <HBox> <SwtLabel  #labelLastUpdate text='Last Update'    fontWeight=\"normal\">  </SwtLabel> <SwtLabel  #labelLastUpdateValue    fontWeight=\"normal\">  </SwtLabel></HBox>\n            <HBox  horizontalAlign=\"center\">\n                <SwtLabel  id=\"requestRecalculation\" #requestRecalculation \n                color=\"black\" fontWeight=\"bold\" label=\"\">\n            </SwtLabel>\n             </HBox>\n        </VBox>\n  ",
                    styles: ["\n      "]
                }] }
    ];
    /** @nocollapse */
    ConfigurableToolTip.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    ConfigurableToolTip.propDecorators = {
        customTooltip: [{ type: ViewChild, args: ['customTooltip',] }],
        dataForLabel: [{ type: ViewChild, args: ['dataForLabel',] }],
        dataForLabelValue: [{ type: ViewChild, args: ['dataForLabelValue',] }],
        labelnumberAccounts: [{ type: ViewChild, args: ['labelnumberAccounts',] }],
        labelnumberAccountsValue: [{ type: ViewChild, args: ['labelnumberAccountsValue',] }],
        labelnewDataExistFor: [{ type: ViewChild, args: ['labelnewDataExistFor',] }],
        labelnewDataExistForValue: [{ type: ViewChild, args: ['labelnewDataExistForValue',] }],
        labelincomplete: [{ type: ViewChild, args: ['labelincomplete',] }],
        labelincompleteValue: [{ type: ViewChild, args: ['labelincompleteValue',] }],
        labelinconsistent: [{ type: ViewChild, args: ['labelinconsistent',] }],
        labelinconsistentValue: [{ type: ViewChild, args: ['labelinconsistentValue',] }],
        labelLastUpdate: [{ type: ViewChild, args: ['labelLastUpdate',] }],
        labelLastUpdateValue: [{ type: ViewChild, args: ['labelLastUpdateValue',] }],
        requestRecalculation: [{ type: ViewChild, args: ['requestRecalculation',] }]
    };
    return ConfigurableToolTip;
}(Container));
export { ConfigurableToolTip };
if (false) {
    /** @type {?} */
    ConfigurableToolTip.prototype.customTooltip;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataForLabel;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataForLabelValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnumberAccounts;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnumberAccountsValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnewDataExistFor;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelnewDataExistForValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelincomplete;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelincompleteValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelinconsistent;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelinconsistentValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelLastUpdate;
    /** @type {?} */
    ConfigurableToolTip.prototype.labelLastUpdateValue;
    /** @type {?} */
    ConfigurableToolTip.prototype.requestRecalculation;
    /** @type {?} */
    ConfigurableToolTip.prototype.dataArray;
    /** @type {?} */
    ConfigurableToolTip.prototype.parentDocument;
    /** @type {?} */
    ConfigurableToolTip.prototype.processBox;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.swtAlert;
    /** @type {?} */
    ConfigurableToolTip.prototype.recalculateEnable;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.clickable;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ConfigurableToolTip.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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