/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
//import * as $ from "jquery";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
var ListPickerItemEditor = /** @class */ (function () {
    function ListPickerItemEditor(args) {
        this.args = args;
        this.init();
    }
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.init = /**
     * @return {?}
     */
    function () {
        this.$input = $("<input type=\"text\" style=\"width: 70%; \"    hideFocus />\n                       <SwtButton    id       =\"SwtNoteIconButton\"\n                                    label     =\"...\"\n                                    styleName = \"button\"\n                                    toolTip   =\"note\"\n                                    [enabled]   =\"true\"\n                                    tabindex  =\"1\"\n                                    [onClick]   =\"\"\n                                    [onKeyDown] =\"\"\n                                    [onFocusOut]=\"\">\n     ");
        this.$input.appendTo(this.args.container);
        this.$input.focus();
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.destroy = /**
     * @return {?}
     */
    function () {
        this.$input.remove();
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.focus = /**
     * @return {?}
     */
    function () {
        this.$input.focus();
    };
    /**
     * @param {?} item
     * @return {?}
     */
    ListPickerItemEditor.prototype.loadValue = /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.defaultValue = !!item[this.args.column.field];
        if (this.defaultValue) {
            this.$input.prop('checked', true);
        }
        else {
            this.$input.prop('checked', false);
        }
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.preClick = /**
     * @return {?}
     */
    function () {
        this.$input.prop('checked', !this.$input.prop('checked'));
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.serializeValue = /**
     * @return {?}
     */
    function () {
        return this.$input.prop('checked');
    };
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    ListPickerItemEditor.prototype.applyValue = /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        item[this.args.column.field] = state;
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.isValueChanged = /**
     * @return {?}
     */
    function () {
        return (this.serializeValue() !== this.defaultValue);
    };
    /**
     * @return {?}
     */
    ListPickerItemEditor.prototype.validate = /**
     * @return {?}
     */
    function () {
        return {
            valid: true,
            msg: null
        };
    };
    return ListPickerItemEditor;
}());
export { ListPickerItemEditor };
if (false) {
    /** @type {?} */
    ListPickerItemEditor.prototype.$input;
    /** @type {?} */
    ListPickerItemEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    ListPickerItemEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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