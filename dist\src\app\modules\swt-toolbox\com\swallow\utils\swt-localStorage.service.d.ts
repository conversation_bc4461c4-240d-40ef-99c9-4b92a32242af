export declare class SwtLocalStorage {
    /**
     * addRecord is used to set a new record to
     * localStorage.
     * @param key
     * @param value
     */
    static addRecord(key: any, value: any): void;
    /**
     * getRecordCount is used to get the number
     * of records saved in the localStorage.
     */
    static getRecordsCount(): number;
    /**
     * clearAll is used to clear all
     * records.
     */
    static clearAll(): void;
    /**
     * this method is used to remove record.
     * @param key
     */
    static removeRecord(key: string): void;
    /**
     * this method is used to get record.
     * @param key
     */
    static getRecord(key: any): any;
    /**
     * this method is used to get the key in the
     * index passed as parameter.
     * @param index
     */
    static getRecordKey(index: number): string;
    /**
     * this method is used to get all records.
     * return object.
     */
    static getRecords(): any;
    /**
     * this method is used to return the list of keys.
     */
    static getRecordKeys(): string[];
    /**
     * this method is used to get the list of values
     */
    static getRecordValues(): Array<any>;
}
