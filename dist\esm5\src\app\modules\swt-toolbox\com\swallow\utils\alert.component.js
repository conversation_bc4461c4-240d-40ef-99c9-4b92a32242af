/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, EventEmitter } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "./common.service";
import { Logger } from "../logging/logger.service";
/** @type {?} */
var $ = require('jquery');
var Alert = /** @class */ (function () {
    function Alert(common) {
        this.common = common;
        this.flags = 4;
        this.onClose = new EventEmitter();
        this.warningMsg = "Warning Alert";
        this.errorMsg = "Error Alert";
        this.confirmMsg = "Confirm Alert";
        this.invalidMsg = "Invalid Alert";
        this.infoMsg = "Info Alert";
        this.defaultButtonFlag = 4;
        this.alertShown = false;
        this.hash = "";
        this.logger = new Logger("Alert", common.httpclient, 6);
    }
    /**
     * @return {?}
     */
    Alert.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.logger.info("[ ngOnInit ] method START");
        try {
            $(".alert-content").draggable({ handle: ".alert-heading" });
            setTimeout((/**
             * @return {?}
             */
            function () {
                $(".alert-button").focus();
            }), 0);
        }
        catch (error) {
            this.logger.error("[ ngOnInit ] method - error ", error);
        }
        this.logger.info("[ ngOnInit ] method END");
    };
    /**
     * This method is used to handle the alert close
     * @param flag
     */
    /**
     * This method is used to handle the alert close
     * @param {?=} flag
     * @param {?=} count
     * @return {?}
     */
    Alert.prototype.destroy = /**
     * This method is used to handle the alert close
     * @param {?=} flag
     * @param {?=} count
     * @return {?}
     */
    function (flag, count) {
        this.logger.info("[ destroy ] method START");
        try {
            /** @type {?} */
            var btn = 0;
            if (flag) {
                btn = this.getbuttonNumber(flag);
            }
            this.onClose.emit({ detail: btn });
            this.windowManager.close(this.alertId);
        }
        catch (error) {
            this.logger.error("[ destroy ] method - error ", error);
        }
        this.logger.info("[ destroy ] method END");
    };
    /**
     * this method is used to populate the alert dialogue.
     * @param text
     * @param title
     * @param flags
     * @param parent
     * @param closeHandler
     * @param iconClass
     * @param defaultButtonFlag
     */
    /**
     * this method is used to populate the alert dialogue.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    Alert.prototype.show = /**
     * this method is used to populate the alert dialogue.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    function (text, title, flags /* Alert.OK */, parent, closeHandler, iconClass, defaultButtonFlag /* Alert.OK */) {
        var _this = this;
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = ""; }
        if (defaultButtonFlag === void 0) { defaultButtonFlag = 4; }
        this.logger.info("[ show ] method START");
        try {
            /** @type {?} */
            var temphash = "" + text + flags + closeHandler;
            if (!this.alertShown || (this.alertShown && temphash != this.hash)) {
                /** @type {?} */
                var alert_1 = this.common.windowManager.createAlert();
                //Commented for Predict as they don't wont alert title
                // alert.title = title;
                alert_1.title = "";
                alert_1.iconClass = iconClass;
                alert_1.texthtml = text.replace(/(\\n|\n)/g, '<br>');
                alert_1.buttonFlags = flags ? this.getButtonLabel(flags) : this.getButtonLabel(4);
                alert_1.onClose.subscribe((/**
                 * @param {?} result
                 * @return {?}
                 */
                function (result) {
                    _this.alertShown = false;
                    _this.hash = null;
                    if (closeHandler) {
                        _this.alertShown = false;
                        _this.hash = null;
                        closeHandler(result);
                    }
                }));
                this.hash = "" + text + flags + closeHandler;
                this.alertShown = true;
                return alert_1;
            }
            else {
                return null;
            }
        }
        catch (error) {
            this.logger.error("[ show ] method - error ", error);
        }
        this.logger.info("[ show ] method END");
    };
    /**
     * this method is used to covert the given flag (type number)
     * to the appropriate button label.
     * @param flag
     */
    /**
     * this method is used to covert the given flag (type number)
     * to the appropriate button label.
     * @protected
     * @param {?} value
     * @return {?}
     */
    Alert.prototype.getButtonLabel = /**
     * this method is used to covert the given flag (type number)
     * to the appropriate button label.
     * @protected
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.logger.info("[ getButtonLabel ] method START");
        try {
            this.logger.info("[ getButtonLabel ] method END");
            /** @type {?} */
            var buttons = { Yes: 1, No: 2, Ok: 4, Canel: 8 };
            /** @type {?} */
            var labels = new Array();
            value & 1 ? labels.push(Alert.yesLabel) : null;
            value & 2 ? labels.push(Alert.noLabel) : null;
            value & 4 ? labels.push(Alert.okLabel) : null;
            value & 8 ? labels.push(Alert.cancelLabel) : null;
            return labels;
        }
        catch (error) {
            this.logger.error("[ getButtonLabel ] method - error ", error);
        }
    };
    /**
     * @private
     * @param {?} label
     * @return {?}
     */
    Alert.prototype.getbuttonNumber = /**
     * @private
     * @param {?} label
     * @return {?}
     */
    function (label) {
        this.logger.info("[ getbuttonNumber ] method START");
        try {
            switch (label.toUpperCase()) {
                case Alert.okLabel.toUpperCase():
                    return Alert.OK;
                case Alert.yesLabel.toUpperCase():
                    return Alert.YES;
                case Alert.noLabel.toUpperCase():
                    return Alert.NO;
                case Alert.cancelLabel.toUpperCase():
                    return Alert.CANCEL;
            }
        }
        catch (error) {
            this.logger.error("[ getbuttonNumber ] method - error ", error);
        }
        this.logger.info("[ getbuttonNumber ] method END");
    };
    Object.defineProperty(Alert.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            return this.texthtml;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.texthtml = value;
        },
        enumerable: true,
        configurable: true
    });
    Alert.YES = 1;
    Alert.NO = 2;
    Alert.OK = 4;
    Alert.CANCEL = 8;
    Alert.yesLabel = "Yes";
    Alert.noLabel = "No";
    Alert.okLabel = "Ok";
    Alert.cancelLabel = "Cancel";
    Alert.decorators = [
        { type: Component, args: [{
                    selector: 'swt-alert',
                    template: "\n        <div class=\"alert-overlay\">\n            <div class=\"alert-content\">\n                <div class=\"alert-heading\">\n                    <span [innerHTML]=\"title\"></span>\n                </div>\n                <div class=\"alert-body\">\n                    <div class=\"alert-message\">\n                        <div class=\"image\">\n                            <img src=\"{{ iconClass }}\" alt=\"{{ title }}\"/>\n                        </div>\n                        <div class=\"msg\" [innerHTML]=\"texthtml\"></div>\n                    </div>\n                    <div class=\"alert-btn\">\n                        <button class=\"alert-button\" *ngFor=\"let flag of buttonFlags let count = index\" (click)=\"destroy(flag, count)\">\n                            {{ flag }}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    ",
                    providers: [CommonService]
                }] }
    ];
    /** @nocollapse */
    Alert.ctorParameters = function () { return [
        { type: CommonService }
    ]; };
    return Alert;
}());
export { Alert };
if (false) {
    /** @type {?} */
    Alert.YES;
    /** @type {?} */
    Alert.NO;
    /** @type {?} */
    Alert.OK;
    /** @type {?} */
    Alert.CANCEL;
    /** @type {?} */
    Alert.yesLabel;
    /** @type {?} */
    Alert.noLabel;
    /** @type {?} */
    Alert.okLabel;
    /** @type {?} */
    Alert.cancelLabel;
    /** @type {?} */
    Alert.prototype.buttonFlags;
    /** @type {?} */
    Alert.prototype.texthtml;
    /** @type {?} */
    Alert.prototype.title;
    /** @type {?} */
    Alert.prototype.iconClass;
    /** @type {?} */
    Alert.prototype.flags;
    /** @type {?} */
    Alert.prototype.windowManager;
    /** @type {?} */
    Alert.prototype.alertId;
    /** @type {?} */
    Alert.prototype.onClose;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.warningMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.errorMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.confirmMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.invalidMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.infoMsg;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.defaultButtonFlag;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.alertShown;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.hash;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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