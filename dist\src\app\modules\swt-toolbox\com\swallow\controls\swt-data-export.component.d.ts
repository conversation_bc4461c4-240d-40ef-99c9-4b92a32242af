import { OnInit, AfterViewInit, ElementRef } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { SwtComboBox } from './swt-combobox.component';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SwtModule } from './swt-module.component';
import { CommonService } from '../utils/common.service';
import { SwtAlert } from '../utils/swt-alert.service';
export declare class SwtDataExport extends SwtModule implements OnInit, AfterViewInit {
    private commonService;
    private element;
    exportDataComponent: SwtComboBox;
    dp: {
        type: string;
        value: string;
        selected: number;
        content: string;
        iconImage: string;
    }[];
    dpDisabled: {
        type: string;
        value: string;
        selected: number;
        content: string;
        iconImage: string;
    }[];
    private accountStartPattern;
    private accountStartWithoutAttributesPattern;
    private nameStartPattern;
    private nameStartWithoutAttributesPattern;
    private accountEndPattern;
    private nameEndPattern;
    private interfaceStartPattern;
    private interfaceStartWithoutAttributesPattern;
    private interfaceEndPattern;
    private assumptionStartPattern;
    private assumptionEndPattern;
    protected elementReference: any;
    protected commonServiceRef: any;
    SwtAlert: SwtAlert;
    ngAfterViewInit(): void;
    onDataExportClick(event: any): void;
    onDataExportChange(event: any): void;
    private _enabled;
    enabled: any;
    constructor(commonService: CommonService, element: ElementRef);
    ngOnInit(): void;
    typeFromIndex(index: number): String;
    deepCopy(mainObj: any): any[];
    convertArrayToXML(josnData: any, objectName: any, parentName?: string, attributeList?: Array<string>): string;
    convertArraysToXML(josnData: any, objectName: any, parentName?: string, attributeList?: Array<string>): string;
    /**
               *  This method is used to convert the xmlData to String and handle the special character
               *
               * @param cMetaData :XMLList
               * @param cGrid :CustomGrid
               * @param tData :XMLList
               * @param selects :Array
               * @param type :String
               */
    convertData(cMetaData: any, cGrid: SwtCommonGrid, tData: any, selects: any, type?: string, isTotalGrid?: boolean, forceAllData?: boolean): void;
}
