import { HttpClient } from '@angular/common/http';
export declare class Logger {
    private _class;
    private http;
    private level;
    private _clientLogLevel;
    private _serverLogLevel;
    private _isIE;
    private forceErrorLog;
    private options;
    constructor(_class: string, http: HttpClient, level?: number);
    trace(message: any, ...additional: any[]): void;
    debug(message: any, ...additional: any[]): void;
    info(message: any, ...additional: any[]): void;
    log(message: any, ...additional: any[]): void;
    warn(message: any, ...additional: any[]): void;
    error(message: any, ...additional: any[]): void;
    private _timestamp;
    private _logOnServer;
    private _logIE;
    private _log;
    private _getColor;
}
export declare class LoggerConfig {
    level: LoggerLevel;
    serverLogLevel: LoggerLevel;
    serverLoggingUrl?: string;
}
export declare enum LoggerLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    LOG = 3,
    WARN = 4,
    ERROR = 5,
    OFF = 6
}
