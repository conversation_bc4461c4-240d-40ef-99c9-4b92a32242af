/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { SwtAlert } from '../utils/swt-alert.service';
import { CommonService } from '../utils/common.service';
var SwtEditableComboBox = /** @class */ (function (_super) {
    tslib_1.__extends(SwtEditableComboBox, _super);
    function SwtEditableComboBox(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._selectedIndex = -1;
        _this._selectedLabel = '';
        // initialize setter.
        _this.swtalert = new SwtAlert(commonService);
        return _this;
    }
    Object.defineProperty(SwtEditableComboBox.prototype, "selectedLabel", {
        get: /**
         * @return {?}
         */
        function () {
            /** @type {?} */
            var comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
            return jQuery(comboComponent).val();
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
            this._selectedLabel = value;
            jQuery(comboComponent).val(value);
            if (this.dataProvider) {
                this._selectedIndex = this.dataProvider.indexOf(this.selectedLabel);
            }
            else {
                this._selectedIndex = -1;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtEditableComboBox.prototype, "selectedIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedIndex;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (this.dataProvider && this.dataProvider.length >= (value + 1)) {
                this.selectedLabel = this.dataProvider[value];
            }
            else {
                this.selectedLabel = '';
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtEditableComboBox.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this.selectedIndex;
                /** @type {?} */
                var comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
                if (value && Array.isArray(value)) {
                    value.sort();
                    this._dataProvider = value;
                    jQuery(comboComponent).editableSelect('clear');
                    for (var index = 0; index < value.length; index++) {
                        /** @type {?} */
                        var element = value[index];
                        jQuery(comboComponent).editableSelect('add', element);
                    }
                }
            }
            catch (error) {
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} element
     * @return {?}
     */
    SwtEditableComboBox.prototype.addItem = /**
     * @param {?} element
     * @return {?}
     */
    function (element) {
        /** @type {?} */
        var comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        for (var i = 0; i < this.dataProvider.length && this.dataProvider[i].localeCompare(element) < 0; i++) { }
        this.dataProvider.splice(i, 0, element);
        jQuery(comboComponent).editableSelect('add', element, i);
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtEditableComboBox.prototype.removeItem = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        /** @type {?} */
        var index = this.dataProvider.indexOf(value);
        jQuery(comboComponent).editableSelect('remove', index);
    };
    /**
     * @return {?}
     */
    SwtEditableComboBox.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        jQuery(this.editableSelect.nativeElement).editableSelect().on('select.editable-select', (/**
         * @param {?} e
         * @param {?} li
         * @return {?}
         */
        function (e, li) {
            _this.selectedLabel = li.text();
            _this._selectedIndex = _this.dataProvider.indexOf(_this.selectedLabel);
        }));
    };
    /**
     * @return {?}
     */
    SwtEditableComboBox.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        // this.dataProvider = ['dorsaf','atef','ekram','rihab']
        // setTimeout(() => {
        //     this.addItem('bora')
        // }, 2000);
        // setTimeout(() => {
        //     this.removeItem('ekram')
        // }, 4000);
        // setTimeout(() => {
        //     // this.selectedLabel = 'atef';
        //     this.selectedIndex = 3;
        // }, 4000);
    };
    SwtEditableComboBox.decorators = [
        { type: Component, args: [{
                    selector: 'SwtEditableComboBox',
                    template: "\n       <div #SwtComboBoxcontainer class=\"SwtComboBox-container\">\n       <select id='editableSelect' class='editableCombo' #editableSelect>\n        </select>\n        </div>\n  ",
                    styles: ["\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtEditableComboBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtEditableComboBox.propDecorators = {
        editableSelect: [{ type: ViewChild, args: ['editableSelect',] }],
        swtComboBoxcontainer: [{ type: ViewChild, args: ['SwtComboBoxcontainer',] }]
    };
    return SwtEditableComboBox;
}(Container));
export { SwtEditableComboBox };
if (false) {
    /** @type {?} */
    SwtEditableComboBox.prototype.editableSelect;
    /** @type {?} */
    SwtEditableComboBox.prototype.swtComboBoxcontainer;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.swtalert;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._selectedLabel;
    /** @type {?} */
    SwtEditableComboBox.prototype.originalValue;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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