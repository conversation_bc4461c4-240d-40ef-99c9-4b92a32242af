/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export var stepperFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var negative = false;
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (showHideCells) {
        if (value == undefined || value == null) {
            return '';
        }
        else {
            return "<input " + (enabledFlag ? '' : 'disabled') + "     style=\"text-align: right; padding-right: 2px!important; width:50px;height: 98%; background-color: white!important ; " + (!enabledFlag ? 'opacity: 0.8;' : '') + " \" type=\"number\" min=\"0\" max=\"2000\" step=\"1\" value =\"" + value + "\"/>\n                    <span class=\"stepper-up-container  renderAsInput  " + (enabledFlag ? '' : 'disabled-container') + " \"><span class=\"render-stepper-arrow-up\"></span></span>\n                    <span class=\"stepper-down-container renderAsInput  " + (enabledFlag ? '' : 'disabled-container') + "  \"><span class=\"render-stepper-arrow-down\"></span></span>\n                   ";
        }
    }
    else {
        return '';
    }
});
//# sourceMappingURL=data:application/json;base64,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