import { ApplicationRef, ComponentFactoryResolver, Injector } from '@angular/core';
import { TitleWindow } from '../controls/title-window.component';
import { Alert } from "../../..";
export interface IWindow {
    component: any;
    selector: any;
    layoutOrder: any;
    winid: string;
}
export declare class WindowManager {
    private factory;
    private injector;
    private applicationRef;
    counter: number;
    windows: {};
    private windowSubject;
    private windowObservable;
    private compRef;
    private exists;
    private logger;
    constructor(factory: ComponentFactoryResolver, injector: Injector, applicationRef: ApplicationRef);
    createWindow(parent: any, component: any, data: any, modal?: boolean): TitleWindow;
    /**+
     * This method is used to load component from its lazy path.
     * @param url
     */
    load(url: any): any;
    show(): void;
    /**
     * This function is used to crate alert.
     */
    createAlert(text?: String, title?: String, flags?: number, parent?: any, closeHandler?: Function, iconClass?: any, defaultButtonFlag?: number): Alert;
    removeAll(): void;
    close(id: any): void;
    getWindow(id: string): any;
    getFirstWindowInstance(): IWindow;
    getLastWindowInsatance(): IWindow;
    getWindowInstance(winid: string): IWindow;
    getFirstLayoutOrder(): IWindow;
    getLastLayoutOrder(): IWindow;
    getLayoutOrder(winid: string): IWindow;
}
