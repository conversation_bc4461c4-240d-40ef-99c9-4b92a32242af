import { SwtTextInput } from '../controls/swt-text-input.component';
import { SwtDateField } from '../controls/swt-datefield.component';
export declare class CommonLogic {
    private _testDate;
    private _dateFormat;
    private _showBuildInProgress;
    private swtAlert;
    testDate: string;
    convertDate(dateAsString: string): string;
    dateFormat: string;
    calculateDays(start: Date, end: Date): number;
    convertUKtoUS(input: string): string;
    static removeLineBreaks(input: string): string;
    /**
     * This function validates the date field - Mantis 1262. <br>
     * The date field is an editable one where the user can type the desired date.<br>
     * The date is taken as an argument and it is validate against certain rules.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010<br>
     */
    validateDate(event: FocusEvent, startDay: SwtDateField, dateVal: Object): Boolean;
    showAlert(startDay: SwtDateField): void;
    /**
     * Helper method for validating the given date field - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     */
    validateDateHelper(day: number, month: number, year: number, startDay: SwtDateField): Boolean;
    /**
     * Helper function for checking whether the given year is a leap year or not - Mantis 1262.<br>
     *
     * Author: Marshal.<br>
     * Date: 19-10-2010.<br>
     */
    isLeapYear(year: number): Boolean;
    /**
     * This function converts the given string to boolean
     *
     * @param value:String - Boolean value in string format or an expression
     * @return Boolean
     */
    static booleanValue(value: string): Boolean;
    /**
     * Added for mantis by KaisBS 2016 + 1468
     * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
     * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
     * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
     *
     * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
     * 	Note that mantis concerns other screens that contain from and to date.
     **/
    checkDateRange(autoRefreshOrcheckLocalDateRange: string, fromDate: Date, toDate: Date, showDays: SwtTextInput, systemDate: string, dateFormat: string, updateDataFunction: Function, classObject: any): Boolean;
    readonly showBuildInProgress: Boolean;
    result(data: any): void;
    fault(datat: any): void;
}
