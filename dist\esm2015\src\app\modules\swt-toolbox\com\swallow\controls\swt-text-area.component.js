/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef, NgZone } from '@angular/core';
//import { SwtAbstract } from "./swt-abstract";
import { Container } from "../containers/swt-container.component";
import { ContextMenu } from "./context-menu.component";
import { focusManager } from "../managers/focus-manager.service";
import { CommonService, unsubscribeAllObservables } from "../utils/common.service";
import { FormControl, Validators } from "@angular/forms";
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/autoresize';
import 'tinymce/themes/silver';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtTextArea extends Container {
    //----------------------------------------------------------------------------------------------------------
    /**
     * @param {?} elem
     * @param {?} commonService
     * @param {?} zone
     */
    constructor(elem, commonService, zone) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._text = '';
        this._htmlText = '';
        this._editable = true;
        this._enabled = true;
        this._fontSize = 11;
        this._doubleClickEnabled = true;
        this.__verticalScrollPolicy = "auto";
        this.__horizontalScrollPolicy = "auto";
        this.elementId = Math.random().toString(36).substring(2);
        this._verticalScrollPosition = 0;
        this._selectionBeginIndex = 0;
        //= new ContextMenu();
        this.contextmenuItems = [];
        this.subscriptions = [];
        this._required = false;
        this.zone = zone;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set contextMenu(value) {
        this._contextMenu = value;
        this.contextmenuItems = this._contextMenu.customItems;
        /* creating list of ContextMenuItem dynamically - [START] */
        this.id_contextMenu = "contextMenu-" + Math.random().toString(36).substr(2, 5);
        /** @type {?} */
        var custom_menu_ul = $(this.elem.nativeElement).find('.custom-menu');
        //-Fix M5041/ISS-290.
        if (custom_menu_ul.length == 0) {
            /** @type {?} */
            var list = $(this.elem.nativeElement).append("<ul id=" + this.id_contextMenu + " class='custom-menu' ></ul>").find('ul');
            for (let index = 0; index < this.contextmenuItems.length; index++) {
                list.append('<li data=\'' + this.contextmenuItems[index].label + '\'>' + this.contextmenuItems[index].label + '</li>');
            }
        }
        /** @type {?} */
        const contextmenuItems = this.contextmenuItems;
        $("#" + this.id_contextMenu + " li").bind("click", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            /** @type {?} */
            let contextMenuItemData = event.currentTarget.attributes[0].nodeValue;
            // alert(contextMenuItemData);
            /** @type {?} */
            const item = contextmenuItems.find((/**
             * @param {?} x
             * @return {?}
             */
            x => x.label == contextMenuItemData));
            if (item) {
                item.MenuItemSelect();
            }
            $(".custom-menu").hide(100);
            $('.custom-menu').removeClass('openedFontSetting');
        }));
        $(document).on('click.contextMenu' + this.id_contextMenu, (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            $(".custom-menu").hide(100);
            $('.custom-menu').removeClass('openedFontSetting');
        }));
        /* creating list of ContextMenuItem dynamically - [END] */
    }
    /**
     * @return {?}
     */
    get contextMenu() {
        return this._contextMenu;
    }
    //---selectionBeginIndex-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set selectionBeginIndex(value) {
        //console.log("set selectionBeginIndex ===>value :",value );
        this._selectionBeginIndex = Number(value);
    }
    /**
     * @return {?}
     */
    get selectionBeginIndex() {
        //console.log("get selectionBeginIndex ===> position",   this.editor.selection.getRng().startOffset  );
        if (this.editor)
            this._selectionBeginIndex = this.editor.selection.getRng().startOffset;
        return this._selectionBeginIndex;
    }
    //---verticalScrollPosition-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set verticalScrollPosition(value) {
        this._verticalScrollPosition = Number(value);
        //console.log('this._verticalScrollPosition :',$(this.editor.getWin()) )
        $(this.editor.getDoc()).find('HTML').animate({ scrollTop: this._verticalScrollPosition }, 0);
    }
    /**
     * @return {?}
     */
    get verticalScrollPosition() {
        //console.log("verticalScrollPosition ===>", $(this.editor.getContainer()).position().top);
        return tinymce.DOM.getViewPort(this.editor.getWin()).y;
    }
    //---width-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        this._width = this.adaptUnit(value, "auto");
        if (this.editor) {
            this.setStyle("width", this._width, this.elem.nativeElement);
            this.setStyle("width", "100%", this.editor.getContainer());
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    //---height-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set height(value) {
        this._height = this.adaptUnit(value, "auto");
        if (this.editor) {
            this.setStyle("height", this._height, this.elem.nativeElement);
            this.setStyle("height", "calc(100% + 20px)", this.editor.getContainer());
        }
    }
    /**
     * @return {?}
     */
    get height() {
        return this._height;
    }
    /* input to hold component visibility */
    /**
     * @param {?} value
     * @return {?}
     */
    set required(value) {
        if (typeof (value) == "string") {
            if (value === 'true') {
                this._required = true;
            }
            else {
                this._required = false;
            }
        }
        else {
            this._required = value;
        }
        if (this._required && !this.text && this.enabled == true)
            $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
        else
            $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
    }
    /**
     * @return {?}
     */
    get required() {
        return this._required;
    }
    //---doubleClickEnabled-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set doubleClickEnabled(value) {
        if (this.adaptValueAsBoolean(value) !== this._doubleClickEnabled) {
            this._doubleClickEnabled = this.adaptValueAsBoolean(value);
            if (this.editor && this._doubleClickEnabled) {
                this.editor.on('dblclick', (/**
                 * @return {?}
                 */
                () => {
                    this.doubleClick();
                    // this.doubleClick_.emit();
                }));
            }
        }
    }
    /**
     * @return {?}
     */
    get doubleClickEnabled() { return this._doubleClickEnabled; }
    //---text-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        if (value != null && value != undefined) {
            if (this.isHTML(value)) {
                value = value.replace(/&<;/ig, '&#60').replace(/>/g, '&#62');
            }
            if (value !== this._text) {
                this._text = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\r\n|\r|\n)/g, '<br>');
            }
            if (this.firstCall) {
                this.originalValue = this._text;
                this.firstCall = false;
            }
            else {
                this._spyChanges(this._text);
            }
            if (this.editor) {
                this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._text)));
            }
        }
        else {
            if (this.editor) {
                this.editor.setContent("");
            }
        }
        if (this.required && !this.text && this.enabled == true)
            $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
        else
            $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
    }
    /**
     * @param {?} text
     * @return {?}
     */
    htmlToText(text) {
        return text;
    }
    /**
     * @return {?}
     */
    get text() {
        if (this.editor) {
            /** @type {?} */
            let datatmp = this.editor.getContent().
                replace(/<br\s*\/?>/ig, "\n")
                .replace(/&nbsp;/ig, ' ')
                .replace(/<[^>]*>/ig, '')
                .replace(/<\/[^>]*>/ig, '');
            datatmp = $($.parseHTML(datatmp)).text();
            // console.log('get text datatmp:', datatmp) 
            return this.validateMaxChar(this.validateRestrict(datatmp));
        }
        return this._text;
    }
    //---htmlText-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set htmlText(value) {
        if (!value)
            value = "";
        if (value !== this._htmlText) {
            this._htmlText = value.replace(/&nbsp;/ig, '&nbsp;').replace(/(?:\n)/g, '<br>').replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
        }
        if (!this.editor) {
            /** @type {?} */
            let intervalId = setInterval((/**
             * @return {?}
             */
            () => {
                if (this.editor) {
                    clearInterval(intervalId);
                    this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._htmlText)));
                }
            }), 1000);
        }
        else {
            this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._htmlText)));
        }
    }
    /**
     * @return {?}
     */
    get htmlText() { return this.editor.getContent(); }
    ;
    //---verticalScrollPolicy------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set verticalScrollPolicy(value) {
        this.__verticalScrollPolicy = (value == "on" ? "scroll" : (value == "off" ? "hidden" : "auto"));
        if (this.editor) {
            this.editor.getBody().style.overflowY = this.__verticalScrollPolicy;
        }
    }
    /**
     * @return {?}
     */
    get verticalScrollPolicy() {
        return this.__verticalScrollPolicy;
    }
    //---horizontalScrollPolicy------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set horizontalScrollPolicy(value) {
        this.__horizontalScrollPolicy = (value == "on" ? "scroll" : (value == "off" ? "hidden" : "auto"));
        if (this.editor) {
            this.editor.getBody().style.overflowX = this.__horizontalScrollPolicy;
        }
    }
    /**
     * @return {?}
     */
    get horizontalScrollPolicy() {
        return this.__horizontalScrollPolicy;
    }
    //---Editable-------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        this._editable = this.adaptValueAsBoolean(value);
        //-Editable
        if (this.editor) {
            if (!this.editor.getBody()) {
                /** @type {?} */
                let intervalId = setInterval((/**
                 * @return {?}
                 */
                () => {
                    if (this.editor.getBody()) {
                        clearInterval(intervalId);
                        this.editor.getBody().setAttribute('contenteditable', this._editable);
                        if (this._editable)
                            $(this.editor.getBody()).removeClass('pointerEvents');
                        else
                            $(this.editor.getBody()).addClass('pointerEvents');
                    }
                }), 1000);
            }
            else {
                this.editor.getBody().setAttribute('contenteditable', this._editable);
                if (this._editable)
                    $(this.editor.getBody()).removeClass('pointerEvents');
                else
                    $(this.editor.getBody()).addClass('pointerEvents');
            }
        }
    }
    /**
     * @return {?}
     */
    get editable() { return this._editable; }
    ;
    //---enabled------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        this._enabled = this.adaptValueAsBoolean(value);
        //console.log('set disabled !!!',this.editor)
        if (this.editor) {
            if (!this.editor.getBody()) {
                /** @type {?} */
                let intervalId = setInterval((/**
                 * @return {?}
                 */
                () => {
                    if (this.editor.getBody()) {
                        clearInterval(intervalId);
                        if (!this.enabled) {
                            $(this.elem.nativeElement).addClass('disabled-container');
                            this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                            this.editor.getBody().disabled = true;
                            //console.log("this.editor.getBody()",this.editor.getBody());
                        }
                        else {
                            $(this.elem.nativeElement).removeClass('disabled-container');
                            this.editor.getBody().style.backgroundColor = "white";
                            this.editor.getBody().disabled = false;
                            //console.log("this.editor.getBody()",this.editor.getBody());
                        }
                    }
                }), 1000);
            }
            else {
                if (!this.enabled) {
                    $(this.elem.nativeElement).addClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                    this.editor.getBody().disabled = true;
                    //console.log("this.editor.getBody()",this.editor.getBody());
                }
                else {
                    $(this.elem.nativeElement).removeClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "white";
                    this.editor.getBody().disabled = false;
                    //console.log("this.editor.getBody()",this.editor.getBody());
                }
            }
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    //---fontWeight------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set fontWeight(value) {
        this.__fontWeight = value;
        if (this.editor) {
            this.editor.getBody().style.fontWeight = this.fontWeight;
        }
    }
    /**
     * @return {?}
     */
    get fontWeight() {
        return this.__fontWeight;
    }
    //---fontSize------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set fontSize(value) {
        this._fontSize = Number(value);
        if (this.editor) {
            this.editor.getBody().style.fontSize = this.fontSize + "px";
        }
    }
    /**
     * @return {?}
     */
    get fontSize() {
        return this._fontSize;
    }
    //----textColor------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set textColor(value) {
        this._textColor = value;
        if (this.editor) {
            this.editor.getBody().style.color = this._textColor;
        }
    }
    /**
     * @return {?}
     */
    get textColor() {
        return this._textColor;
    }
    //----Color------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set color(value) {
        this.__color = value;
        if (this.editor) {
            this.editor.getBody().style.color = this.__color;
        }
    }
    /**
     * @return {?}
     */
    get color() {
        return this.__color;
    }
    //----textAlign------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set textAlign(value) {
        this._textAlign = value;
        if (this.editor) {
            this.editor.getBody().style.textAlign = this._textAlign;
        }
    }
    /**
     * @return {?}
     */
    get textAlign() {
        return this._textAlign;
    }
    //----textAlign------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set backgroundColor(value) {
        this.__backgroundColor = value;
        if (this.editor) {
            this.editor.getBody().style.backgroundColor = this.__backgroundColor;
        }
    }
    /**
     * @return {?}
     */
    get backgroundColor() {
        return this.__backgroundColor;
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        // const popperContentEl = this.elem.nativeElement.querySelector('popper-content');
        // this.elem.nativeElement.appendChild(popperContentEl);
        //console.log("SwtTextArea --------------->", window.tinymce);
        window.tinyMCEPreInit = {
            base: '/assets/tinymce',
            suffix: '.min'
        };
        //-M5117 / ISS-Q : Enrichissement in Send Mail exist but is hidden.
        if (this.elem.nativeElement.nodeName.toLowerCase() != "swtrichtexteditor") {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtTextArea');
            tinymce.init({
                skin: false,
                content_css: './assets/css/tinymce/SwtTextArea.css',
                menubar: false,
                toolbar: false,
                branding: false,
                width: "0",
                height: "0",
                body_class: "swtTextArea",
                setup: (/**
                 * @param {?} ed
                 * @return {?}
                 */
                function (ed) {
                    ed.on("keydown", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        //console.log('event.keyCode :',event.keyCode, ' this.editable :',this.editable)
                        if (this.editable) {
                            if (event.keyCode != 13 && event.keyCode != 46 && event.keyCode != 8 && event.keyCode != 36 && event.keyCode != 37 && event.keyCode != 38 && event.keyCode != 39 && event.keyCode != 40) {
                                if (this.restrict) {
                                    /** @type {?} */
                                    let controlRestrict = new FormControl(event.key, Validators.pattern('[' + this.restrict + ']'));
                                    //console.log('controlRestrict.valid :',controlRestrict.valid)
                                    if (!controlRestrict.valid) {
                                        event.preventDefault();
                                        return false;
                                    }
                                }
                                if (this.maxChars) {
                                    /** @type {?} */
                                    let controlMaxChar = new FormControl(this.text, Validators.maxLength(this.maxChars - 1));
                                    //console.log('controlMaxChar.valid :',controlMaxChar.valid)
                                    if (!controlMaxChar.valid && !this.editor.selection.getContent()) {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        event.stopImmediatePropagation();
                                        return false;
                                    }
                                }
                                if (event.keyCode == 13 && !event.shiftKey) {
                                    tinymce.EditorManager.execCommand('InsertLineBreak');
                                    tinymce.dom.Event.cancel(event);
                                    return;
                                }
                                this.keyDown();
                                this.onKeyDown_.emit(this.text);
                            }
                        }
                    })),
                        ed.on("keyup", (/**
                         * @param {?} event
                         * @return {?}
                         */
                        (event) => {
                            if (this.maxChars) {
                                //console.log(' this.editor.getContent() ', this.editor.getContent() )
                                /** @type {?} */
                                let controlMaxChar = new FormControl(this.editor ? this.editor.getContent() : this.text, Validators.maxLength(this.maxChars));
                                if (!controlMaxChar.valid && !this.editor.selection.getContent()) {
                                    //this.editor.undoManager.undo();
                                    //-M5204 By Rihab.JB @15/01/2021 : The copy/paste must be take in account the maxchars of component.
                                    this.text = this.validateMaxChar(this.validateRestrict(this.text));
                                }
                                /*else {
                                    this.editor.undoManager.reset();
                                }*/
                            }
                        }));
                }).bind(this)
            });
        }
        else {
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRichTextEditor');
            tinymce.init({
                skin: false,
                content_css: './assets/css/tinymce/SwtTextArea.css',
                body_class: "swtTextArea",
                plugins: ['advlist', 'lists'],
                menubar: false,
                branding: false,
                width: "0",
                height: "0",
                toolbar: ["fontselect  |fontsizeselect | bold italic underline |forecolor | alignleft aligncenter alignright alignjustify | numlist"],
                font_formats: "mirza; aref; monospace; serif; times; timesRoman; arial; verdana; courier; courierNew; geneva; georgia ; helvetica",
                fontsize_formats: '8px 9px 10px 11px 12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 48px 72px ',
                setup: (/**
                 * @param {?} ed
                 * @return {?}
                 */
                function (ed) {
                    ed.on("keydown", (/**
                     * @param {?} event
                     * @return {?}
                     */
                    (event) => {
                        if (event.keyCode != 46 && event.keyCode != 8 && event.keyCode != 36 && event.keyCode != 37 && event.keyCode != 38 && event.keyCode != 39 && event.keyCode != 40) {
                            if (this.restrict) {
                                /** @type {?} */
                                let controlRestrict = new FormControl(event.key, Validators.pattern('[' + this.restrict + ']'));
                                //console.log('controlRestrict.valid :',controlRestrict.valid)
                                if (!controlRestrict.valid) {
                                    event.preventDefault();
                                    return false;
                                }
                            }
                            if (this.maxChars) {
                                /** @type {?} */
                                let controlMaxChar = new FormControl(this.text, Validators.maxLength(this.maxChars - 1));
                                //console.log('controlMaxChar.valid :',controlMaxChar.valid)
                                if (!controlMaxChar.valid && !this.editor.selection.getContent()) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    event.stopImmediatePropagation();
                                    return false;
                                }
                            }
                            /*
                                if (event.keyCode == 13 && !event.shiftKey) {
                                    tinymce.EditorManager.execCommand('InsertLineBreak')
                                    tinymce.dom.Event.cancel(event);
                                    return;
                                }*/
                            this.keyDown();
                            this.onKeyDown_.emit(this.text);
                        }
                        /*setTimeout(() => {
                            this._spyChanges(this.text);
                        }, 0);*/
                    })),
                        ed.on("keyup", (/**
                         * @param {?} event
                         * @return {?}
                         */
                        (event) => {
                            if (this.maxChars) {
                                /** @type {?} */
                                let controlMaxChar = new FormControl(this.text, Validators.maxLength(this.maxChars));
                                if (!controlMaxChar.valid && !this.editor.selection.getContent()) {
                                    this.editor.undoManager.undo();
                                }
                                else {
                                    this.editor.undoManager.reset();
                                }
                            }
                        }));
                }).bind(this)
            });
        }
        if (this.elem.nativeElement.nodeName.toLowerCase() != "swtrichtexteditor") {
            $($(this.elem.nativeElement).find('.tox-editor-header')).hide();
        }
        tinymce.EditorManager.execCommand('mceAddEditor', true, this.elementId);
        this.editor = tinymce.get(this.elementId);
        if (this.editor) {
            //- Init 
            this.editor.on('init', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                this.editor.on('contextmenu', (/**
                 * @param {?} e
                 * @return {?}
                 */
                (e) => {
                    if (e.which === 3) {
                        $(".custom-menu").hide(100);
                        $('.custom-menu').removeClass('openedFontSetting');
                        this.onRightClickTextArea(e);
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        e.stopPropagation();
                    }
                }));
                //-ToolTip
                if (this.editor.getBody())
                    this.editor.getBody().setAttribute('title', this.toolTip);
                this.toolTipObject = $(this.elem.nativeElement);
                this.toolTipObject.tooltip({
                    position: { my: "left bottom-20", at: "left bottom+45" }
                });
                $(this.editor.getBody()).addClass(this.styleName);
                //To avoid getting originaleValue as undefined the spy wont work.
                if (this.originalValue == undefined)
                    this.originalValue = "";
                //-Fix to set the previous text within textarea after the reload of the page.
                if (this.editor.getContent()) {
                    this.editor.setContent(this.editor.getContent());
                }
                else if (this._text && this._text != null && this._text != undefined) {
                    this.editor.setContent(this.validateMaxChar(this.validateRestrict(this._text)));
                }
                this.setStyle("width", this._width, this.elem.nativeElement);
                this.setStyle("width", "100%", this.editor.getContainer());
                this.setStyle("height", this._height, this.elem.nativeElement);
                this.setStyle("height", "100%", this.editor.getContainer());
                this.editor.getBody().style.fontSize = this.fontSize + "px";
                this.editor.getBody().style.fontFamily = 'verdana';
                if (this.required && !this.text && this.enabled == true)
                    $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
                if (this.toolTipPreviousValue)
                    $(this.elem.nativeElement).find('.tox-tinymce').addClass('border-orange-previous');
                else
                    $(this.elem.nativeElement).find('.tox-tinymce').removeClass('border-orange-previous');
                // [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}
                if (this.textColor)
                    this.editor.getBody().style.color = this.textColor;
                if (this.color)
                    this.editor.getBody().style.color = this.color;
                if (this.textAlign)
                    this.editor.getBody().style.textAlign = this.textAlign;
                if (this.backgroundColor)
                    this.editor.getBody().style.backgroundColor = this.backgroundColor;
                if (this.backgroundColor)
                    this.editor.getBody().style.backgroundColor = this.backgroundColor;
                if (this.verticalScrollPolicy)
                    this.editor.getBody().style.overflowY = this.verticalScrollPolicy;
                if (this.horizontalScrollPolicy)
                    this.editor.getBody().style.overflowX = this.horizontalScrollPolicy;
                if (this.constructor.name.toLowerCase() != "swtrichtexteditor") {
                    if (this.paddingTop)
                        this.setStyle("padding-top", this.paddingTop, $(this.elem.nativeElement));
                    if (this.paddingBottom)
                        this.setStyle("padding-bottom", this.paddingBottom, $(this.elem.nativeElement));
                    if (this.paddingLeft)
                        this.setStyle("padding-left", this.paddingLeft, $(this.elem.nativeElement));
                    if (this.paddingRight)
                        this.setStyle("padding-right", this.paddingRight, $(this.elem.nativeElement));
                    if (this.marginTop)
                        this.setStyle("margin-top", this.marginTop, $(this.elem.nativeElement));
                    if (this.marginBottom)
                        this.setStyle("margin-bottom", this.marginBottom, $(this.elem.nativeElement));
                    if (this.marginLeft)
                        this.setStyle("margin-left", this.marginLeft, $(this.elem.nativeElement));
                    if (this.marginRight)
                        this.setStyle("margin-right", this.marginRight, $(this.elem.nativeElement));
                }
                //tinymce.execCommand('mceFocus', true,  this.editor );      
                // console.log('---------- .tox-edit-area__iframe" ', $($($(this.editor.getContainer()).find(".tox-edit-area__iframe"))))
                // $($(this.editor.getContainer()).find(".tox-edit-area__iframe")).width('100%');
                $('#' + e.target.id + '_ifr').removeAttr('title');
            }));
            this.editor.on('ResizeEditor', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                //this.setStyle("height", "100%", this.editor.getContainer());
                //event.preventDefault();
            }));
            //-paste  
            this.editor.on('paste', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                //event.preventDefault();
                /** @type {?} */
                var content = ((event.originalEvent || event).clipboardData || window.clipboardData).getData('Text').toString();
                //console.log('-----------on paste--------------',content);
                //-Fix M5041/ISS-270
                //this.text += content.toString();
                //event.preventDefault();
            }));
            this.editor.on('change', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                if (this.required && !this.text && this.enabled == true)
                    $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
            }));
            this.editor.on('input', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                if (this.required && !this.text && this.enabled == true)
                    $(this.elem.nativeElement).find('.tox-tinymce').addClass('requiredInput');
                else
                    $(this.elem.nativeElement).find('.tox-tinymce').removeClass('requiredInput');
                this.change();
                this.change_.emit(this.text);
            }));
            //-KeyUp  
            this.editor.on('keyup', (/**
             * @return {?}
             */
            () => {
                this.keyUp();
                this.onKeyUp_.emit(this.text);
                //-Rihab.JB @05/01/2021 : this is added because the event "input" is not working on IE.
                if (this.getBroserType().toLowerCase() == 'ie') {
                    this.change();
                    this.change_.emit(this.text);
                }
                setTimeout((/**
                 * @return {?}
                 */
                () => {
                    this._spyChanges(this.text);
                }), 0);
            }));
            //-Click 
            this.editor.on('click', (/**
             * @return {?}
             */
            () => {
                focusManager.focusTarget = this;
                this.click();
                this.onClick_.emit(this.text);
            }));
            //-Double Click 
            if (this.doubleClickEnabled) {
                this.editor.on('dblclick', (/**
                 * @return {?}
                 */
                () => {
                    //console.log('------------dblclick-------------');
                    this.doubleClick();
                    this.doubleClick_.emit();
                }));
            }
            //-Scroll 
            $(this.editor.getWin()).bind('scroll', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                // console.log('Editor window scrolled!');
                this.scroll_.emit(event);
                //this.scroll();
                e.preventDefault();
            }));
            //-mouseDown 
            this.editor.on('mousedown', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------mousedown-------------');
                $(".custom-menu").hide(100);
                $('.custom-menu').removeClass('openedFontSetting');
            }));
            //-mouseUp 
            this.editor.on('mouseup', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------mouseup-------------');
            }));
            //-mouseOver 
            this.editor.on('mouseover', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------mouseover-------------');
                if (this.toolTipPreviousValue) {
                    // Get the element that the mouse is over
                    /** @type {?} */
                    const target = this.elem.nativeElement;
                    // Create a new tooltip element
                    /** @type {?} */
                    const tooltip = document.createElement('div');
                    tooltip.textContent = this.toolTipPreviousValue;
                    // Position the tooltip next to the element
                    /** @type {?} */
                    const rect = target.getBoundingClientRect();
                    tooltip.style.position = 'absolute';
                    tooltip.style.left = rect.left + window.pageXOffset + 'px';
                    tooltip.style.top = rect.top + window.pageYOffset + rect.height + 'px';
                    tooltip.classList.add('basic-tooltip');
                    tooltip.classList.add('border-orange-previous');
                    // Add the tooltip to the document
                    document.body.appendChild(tooltip);
                    // Remove the tooltip when the mouse moves away
                    target.addEventListener('mouseout', (/**
                     * @return {?}
                     */
                    () => {
                        try {
                            document.body.removeChild(tooltip);
                        }
                        catch (e) {
                        }
                    }));
                }
                console.log("🚀 ~ file: swt-text-area.component.ts:791 ~ SwtTextArea ~ this.editor.on ~ e:", e);
            }));
            //-mouseMove 
            this.editor.on('mousemove', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------mousemove-------------');
            }));
            //-mouseOut 
            this.editor.on('mouseout', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------mouseout-------------');
            }));
            //-mouseWheel 
            this.editor.on('wheel', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                //console.log('------------wheel-------------', );
            }));
            //-focus  
            this.editor.on('focus', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                if ($(this.editor.getContainer())) {
                    //-Set  border color on focus
                    $(this.editor.getContainer()).find('.tox-editor-container').toggleClass('focused');
                }
                focusManager.focusTarget = this;
                this.focusIn();
                this.focusIn_.emit(this.text);
            }));
            //-blur  
            this.editor.on('blur', (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                if ($(this.editor.getContainer())) {
                    //-Set  border color on focus
                    $(this.editor.getContainer()).find('.tox-editor-container').toggleClass('focused');
                }
                this.focusOut();
                this.onFocusOut_.emit(this.text);
                this.keyFocusChange(e);
                this.keyFocusChange_.emit(this.text);
            }));
            //-Editable
            if (this.editor.getBody()) {
                this.editor.getBody().setAttribute('contenteditable', this._editable);
                if (this._editable)
                    $(this.editor.getBody()).removeClass('pointerEvents');
                else
                    $(this.editor.getBody()).addClass('pointerEvents');
            }
            //-Enabled
            if (this.editor.getBody()) {
                if (!this.enabled) {
                    $(this.elem.nativeElement).addClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "#aaaaaa85";
                }
                else {
                    $(this.elem.nativeElement).removeClass('disabled-container');
                    this.editor.getBody().style.backgroundColor = "white";
                }
            }
        }
    }
    /**
     * @private
     * @param {?} str
     * @return {?}
     */
    isHTML(str) {
        /** @type {?} */
        var a = document.createElement('div');
        a.innerHTML = str;
        for (var c = a.childNodes, i = c.length; i--;) {
            if (c[i].nodeType == 1)
                return true;
        }
        return false;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onRightClickTextArea(event) {
        event.preventDefault();
        /** @type {?} */
        var screenVerion = $('.screenVerion');
        if (screenVerion.length > 0) {
            screenVerion.addClass('hidden');
            $('.custom-menu').addClass('openedFontSetting');
        }
        /** @type {?} */
        let __this = this;
        // Show contextmenu
        /** @type {?} */
        var contextmenu = $(this.elem.nativeElement).find('.custom-menu');
        /** @type {?} */
        var windowWidth = $(window).width();
        /** @type {?} */
        var containerLeft = $(this.editor.getContainer()).position().left;
        //-Fix M5041/ISS-159-B.
        /** @type {?} */
        var left = $(this.editor.getContainer()).position().left > event.pageX + $(contextmenu).width() ? event.pageX + $(this.editor.getContainer()).position().left : event.pageX + $(this.editor.getContainer()).position().left - $(contextmenu).width();
        if ((containerLeft > event.pageX + $(contextmenu).width()) && (containerLeft + event.pageX + $(contextmenu).width() < windowWidth)) {
            left = event.pageX + containerLeft;
        }
        else {
            left = event.pageX + containerLeft - $(contextmenu).width();
        }
        contextmenu.finish().toggle(100).css('position', 'absolute').css({
            top: event.clientY + $(this.editor.getContainer()).position().top,
            left: left
        });
        event.preventDefault();
    }
    /**
     * @private
     * @param {?} e
     * @return {?}
     */
    isVisible(e) {
        if (e)
            return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
        else
            return false;
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            delete this._contextMenu;
            delete this.contextmenuItems;
            $("#" + this.id_contextMenu + " li").unbind("click");
            super.ngOnDestroy();
            if (this.editor) {
                $(this.editor.getWin()).unbind('scroll');
            }
            $('.tox-tinymce-aux').remove();
            $('.ui-helper-hidden-accessible').remove();
            tinymce.execCommand('mceRemoveControl', true, this.elementId);
            tinymce.EditorManager.execCommand('mceRemoveEditor', true, this.elementId);
            //- unscbscribe all Observables. 
            this.subscriptions = unsubscribeAllObservables(this.subscriptions);
            $(document).off('click.contextMenu' + this.id_contextMenu);
        }
        catch (error) {
            console.error('error :', error);
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
}
SwtTextArea.decorators = [
    { type: Component, args: [{
                selector: 'SwtTextArea',
                template: `
        <textarea    class="txtAreaClass" id="{{elementId}}"   ></textarea> 
      `,
                styles: [`
        .txtAreaClass{
            height: 100%;
            width: 100%;
            display: none !important;        
        }
  `]
            }] }
];
/** @nocollapse */
SwtTextArea.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: NgZone }
];
SwtTextArea.propDecorators = {
    contextMenu: [{ type: Input }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    selectionBeginIndex: [{ type: Input, args: ['selectionBeginIndex',] }],
    verticalScrollPosition: [{ type: Input, args: ['verticalScrollPosition',] }],
    width: [{ type: Input, args: ['width',] }],
    height: [{ type: Input, args: ['height',] }],
    required: [{ type: Input, args: ['required',] }],
    doubleClickEnabled: [{ type: Input }],
    text: [{ type: Input }],
    htmlText: [{ type: Input }],
    verticalScrollPolicy: [{ type: Input }],
    horizontalScrollPolicy: [{ type: Input }],
    editable: [{ type: Input }],
    enabled: [{ type: Input }],
    fontWeight: [{ type: Input }],
    fontSize: [{ type: Input }],
    textColor: [{ type: Input }],
    color: [{ type: Input }],
    textAlign: [{ type: Input }],
    backgroundColor: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._htmlText;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__fontWeight;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._fontSize;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._textColor;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__color;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._doubleClickEnabled;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__backgroundColor;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__verticalScrollPolicy;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.__horizontalScrollPolicy;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.zone;
    /** @type {?} */
    SwtTextArea.prototype.editor;
    /** @type {?} */
    SwtTextArea.prototype.elementId;
    /** @type {?} */
    SwtTextArea.prototype._verticalScrollPosition;
    /** @type {?} */
    SwtTextArea.prototype._selectionBeginIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._contextMenu;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.contextmenuItems;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.toolTipObject;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.id_contextMenu;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype._required;
    /** @type {?} */
    SwtTextArea.prototype.tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTextArea.prototype.commonService;
    /* Skipping unhandled member: [x: string]: any;*/
    /* Skipping unhandled member: ;*/
    /* Skipping unhandled member: ;*/
}
//# sourceMappingURL=data:application/json;base64,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