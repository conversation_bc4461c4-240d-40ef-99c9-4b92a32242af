/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
export class Grid extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
    }
}
Grid.decorators = [
    { type: Component, args: [{
                selector: 'Grid',
                template: `
            <div  
                 fxLayout="column" class="noOutline"
                 fxLayoutGap="{{verticalGap}}">
                   <ng-content></ng-content>

            </div>
  `,
                styles: [`
  :host {
      outline: none;
    }
    .noOutline{
      outline: none;
    }
  `]
            }] }
];
/** @nocollapse */
Grid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    Grid.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    Grid.prototype.commonService;
}
/**
 *
 * GridRow component.
 * *
 */
export class GridRow extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
    }
}
GridRow.decorators = [
    { type: Component, args: [{
                selector: 'GridRow',
                template: `
            <div  
                 fxLayout="row" class="noOutline"
                 fxLayoutGap="{{horizontalGap}}" >
                <ng-content></ng-content>
            </div>
    `,
                styles: [`
    :host {
        outline: none;
      }
      .noOutline{
        outline: none;
      }
    `]
            }] }
];
/** @nocollapse */
GridRow.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    GridRow.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    GridRow.prototype.commonService;
}
/**
 *
 * GridItem component.
 *
 * *
 */
export class GridItem extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
    }
}
GridItem.decorators = [
    { type: Component, args: [{
                selector: 'GridItem',
                template: `
            <div   fxLayout="row" class="noOutline" fxLayoutGap="{{horizontalGap}}" >
               <ng-content></ng-content>
            </div>
    `,
                styles: [`
    :host {
        outline: none;
      }
      .noOutline{
        outline: none;
      }
    `]
            }] }
];
/** @nocollapse */
GridItem.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
if (false) {
    /**
     * @type {?}
     * @private
     */
    GridItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    GridItem.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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