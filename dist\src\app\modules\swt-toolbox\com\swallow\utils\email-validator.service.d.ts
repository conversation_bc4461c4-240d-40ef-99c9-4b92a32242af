export declare class EmailValidator {
    /**
    *  Storage for the missingAtSignError property.
    */
    private _missingAtSignError;
    private static DISALLOWED_CHARS;
    constructor();
    /**
     *  Convenience method for calling a validator
     *  from within a custom validation function.
     *  Each of the standard Angular validators has a similar convenience method.
     *
     *  @param validator The EmailValidator instance.
     *
     *  @param value A field to validate.
     *
     *  @param baseField Text representation of the subfield
     *  specified in the value parameter.
     *  For example, if the <code>value</code> parameter specifies value.email,
     *  the <code>baseField</code> value is "email".
     *
     *  @return An Array of ValidationResult objects, with one
     *  ValidationResult object for each field examined by the validator.
     *
     *  @see mx.validators.ValidationResult
     */
    static validateEmail(validator: EmailValidator, value: Object, baseField: string): any[];
    /**
     * Validate a given IP address
     *
     * If IP domain, then must follow [x.x.x.x] format
     * or for IPv6, then follow [x:x:x:x:x:x:x:x] or [x::x:x:x] or some
     * IPv4 hybrid, like [::x.x.x.x] or [0:00::***********]
     */
    private static isValidIPAddress;
}
