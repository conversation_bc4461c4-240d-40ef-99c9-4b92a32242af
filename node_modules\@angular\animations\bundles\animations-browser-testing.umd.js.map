{"version": 3, "file": "animations-browser-testing.umd.js", "sources": ["../../../../../../../../../../external/npm/node_modules/tslib/tslib.es6.js", "../../../../../../../packages/animations/browser/testing/src/mock_animation_driver.ts", "../../../../../../../packages/animations/browser/testing/src/testing.ts", "../../../../../../../packages/animations/browser/testing/public_api.ts", "../../../../../../../packages/animations/browser/testing/index.ts", "../../../../../../../packages/animations/browser/testing/testing.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationPlayer, NoopAnimationPlayer, ÉµStyleData} from '@angular/animations';\nimport {ÉµAnimationDriver as AnimationDriver, ÉµallowPreviousPlayerStylesMerge as allowPreviousPlayerStylesMerge, ÉµcontainsElement as containsElement, ÉµinvokeQuery as invokeQuery, ÉµmatchesElement as matchesElement, ÉµvalidateStyleProperty as validateStyleProperty} from '@angular/animations/browser';\n\n\n/**\n * @publicApi\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  validateStyleProperty(prop: string): boolean { return validateStyleProperty(prop); }\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): MockAnimationPlayer {\n    const player =\n        new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @publicApi\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: {[key: string]: string | number} = {};\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ÉµStyleData = {};\n\n  constructor(\n      public element: any, public keyframes: {[key: string]: string | number}[],\n      public duration: number, public delay: number, public easing: string,\n      public previousPlayers: any[]) {\n    super(duration, delay);\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousPlayers.forEach(player => {\n        if (player instanceof MockAnimationPlayer) {\n          const styles = player.currentSnapshot;\n          Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n        }\n      });\n    }\n  }\n\n  /* @internal */\n  onInit(fn: () => any) { this._onInitFns.push(fn); }\n\n  /* @internal */\n  init() {\n    super.init();\n    this._onInitFns.forEach(fn => fn());\n    this._onInitFns = [];\n  }\n\n  finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /* @internal */\n  triggerMicrotask() {}\n\n  play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  hasStarted() { return this.__started; }\n\n  beforeDestroy() {\n    const captures: ÉµStyleData = {};\n\n    Object.keys(this.previousStyles).forEach(prop => {\n      captures[prop] = this.previousStyles[prop];\n    });\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this.keyframes.forEach(kf => {\n        Object.keys(kf).forEach(prop => {\n          if (prop != 'offset') {\n            captures[prop] = this.__finished ? kf[prop] : AUTO_STYLE;\n          }\n        });\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {MockAnimationDriver, MockAnimationPlayer} from './mock_animation_driver';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verifcation. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["validateStyleProperty", "matchesElement", "containsElement", "invoke<PERSON><PERSON>y", "tslib_1.__extends", "allowPreviousPlayerStylesMerge", "AUTO_STYLE", "NoopAnimationPlayer"], "mappings": ";;;;;;;;;;;;IAAA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnF,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;;AAEF,IAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;;IChBD;;;AAGA;QAAA;SA2BC;QAxBC,mDAAqB,GAArB,UAAsB,IAAY,IAAa,OAAOA,8BAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;QAEpF,4CAAc,GAAd,UAAe,OAAY,EAAE,QAAgB;YAC3C,OAAOC,uBAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1C;QAED,6CAAe,GAAf,UAAgB,IAAS,EAAE,IAAS,IAAa,OAAOC,wBAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;QAEtF,mCAAK,GAAL,UAAM,OAAY,EAAE,QAAgB,EAAE,KAAc;YAClD,OAAOC,oBAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC9C;QAED,0CAAY,GAAZ,UAAa,OAAY,EAAE,IAAY,EAAE,YAAqB;YAC5D,OAAO,YAAY,IAAI,EAAE,CAAC;SAC3B;QAED,qCAAO,GAAP,UACI,OAAY,EAAE,SAA6C,EAAE,QAAgB,EAAE,KAAa,EAC5F,MAAc,EAAE,eAA2B;YAA3B,gCAAA,EAAA,oBAA2B;YAC7C,IAAM,MAAM,GACR,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;YAC1F,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAkB,MAAM,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;SACf;QAzBM,uBAAG,GAAsB,EAAE,CAAC;QA0BrC,0BAAC;KA3BD,IA2BC;IAED;;;AAGA;QAAyCC,uCAAmB;QAO1D,6BACW,OAAY,EAAS,SAA6C,EAClE,QAAgB,EAAS,KAAa,EAAS,MAAc,EAC7D,eAAsB;YAHjC,YAIE,kBAAM,QAAQ,EAAE,KAAK,CAAC,SAUvB;YAbU,aAAO,GAAP,OAAO,CAAK;YAAS,eAAS,GAAT,SAAS,CAAoC;YAClE,cAAQ,GAAR,QAAQ,CAAQ;YAAS,WAAK,GAAL,KAAK,CAAQ;YAAS,YAAM,GAAN,MAAM,CAAQ;YAC7D,qBAAe,GAAf,eAAe,CAAO;YATzB,gBAAU,GAAG,KAAK,CAAC;YACnB,eAAS,GAAG,KAAK,CAAC;YACnB,oBAAc,GAAqC,EAAE,CAAC;YACrD,gBAAU,GAAkB,EAAE,CAAC;YAChC,qBAAe,GAAe,EAAE,CAAC;YAQtC,IAAIC,uCAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;gBACnD,eAAe,CAAC,OAAO,CAAC,UAAA,MAAM;oBAC5B,IAAI,MAAM,YAAY,mBAAmB,EAAE;wBACzC,IAAM,QAAM,GAAG,MAAM,CAAC,eAAe,CAAC;wBACtC,MAAM,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,QAAM,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;qBAC/E;iBACF,CAAC,CAAC;aACJ;;SACF;;QAGD,oCAAM,GAAN,UAAO,EAAa,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;QAGnD,kCAAI,GAAJ;YACE,iBAAM,IAAI,WAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,EAAE,GAAA,CAAC,CAAC;YACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACtB;QAED,oCAAM,GAAN;YACE,iBAAM,MAAM,WAAE,CAAC;YACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;QAED,qCAAO,GAAP;YACE,iBAAM,OAAO,WAAE,CAAC;YAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;;QAGD,8CAAgB,GAAhB,eAAqB;QAErB,kCAAI,GAAJ;YACE,iBAAM,IAAI,WAAE,CAAC;YACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;QAED,wCAAU,GAAV,cAAe,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAEvC,2CAAa,GAAb;YAAA,iBAqBC;YApBC,IAAM,QAAQ,GAAe,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC3C,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAC5C,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;gBAIrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,EAAE;oBACvB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;wBAC1B,IAAI,IAAI,IAAI,QAAQ,EAAE;4BACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,GAAGC,qBAAU,CAAC;yBAC1D;qBACF,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;SACjC;QACH,0BAAC;IAAD,CA3EA,CAAyCC,8BAAmB;;IC9C5D;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;;;;;OAMG;;ICNH;;OAEG;;;;;;;;;;;;;"}