/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { OperatorType } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
/** @type {?} */
var o = DOMPurify_;
var EnhancedInputSearch = /** @class */ (function () {
    function EnhancedInputSearch() {
        this._clearFilterTriggered = false;
        this._shouldTriggerQuery = true;
        this.searchTerms = [];
        this.operator = OperatorType.equal;
        this._inputType = 'text';
    }
    Object.defineProperty(EnhancedInputSearch.prototype, "columnFilter", {
        /** Getter for the Column Filter */
        get: /**
         * Getter for the Column Filter
         * @return {?}
         */
        function () {
            return this.columnDef && this.columnDef.filter || {};
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(EnhancedInputSearch.prototype, "gridOptions", {
        /** Getter for the Grid Options pulled through the Grid Object */
        get: /**
         * Getter for the Grid Options pulled through the Grid Object
         * @protected
         * @return {?}
         */
        function () {
            return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Initialize the Filter
     */
    /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    EnhancedInputSearch.prototype.init = /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    function (args) {
        var _this = this;
        console.log('enter custominput');
        this.grid = args.grid;
        this.callback = args.callback;
        this.columnDef = args.columnDef;
        this.searchTerms = (args.hasOwnProperty('searchTerms') ? args.searchTerms : []) || [];
        // filter input can only have 1 search term, so we will use the 1st array index if it exist
        /** @type {?} */
        var searchTerm = (Array.isArray(this.searchTerms) && this.searchTerms.length >= 0) ? this.searchTerms[0] : '';
        // step 1, create HTML string template
        /** @type {?} */
        var filterTemplate = this.buildTemplateHtmlString();
        // step 2, create the DOM Element of the filter & initialize it if searchTerm is filled
        this.$filterElm = this.createDomElement(filterTemplate, searchTerm);
        $('#search-filter-' + this.grid.getUID() + this.columnDef.id).focus();
        // step 3, subscribe to the keyup event and run the callback when that happens
        this.$filterElm.keyup((/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            /** @type {?} */
            var value = e && e.target && e.target.value || '';
            /** @type {?} */
            var enableWhiteSpaceTrim = _this.gridOptions.enableFilterTrimWhiteSpace || _this.columnFilter.enableTrimWhiteSpace;
            if (typeof value === 'string' && enableWhiteSpaceTrim) {
                value = value.trim();
            }
            if (_this._clearFilterTriggered) {
                _this.callback(e, { columnDef: _this.columnDef, clearFilterTriggered: _this._clearFilterTriggered, shouldTriggerQuery: _this._shouldTriggerQuery });
                _this.$filterElm.removeClass('filled');
            }
            else {
                value === '' ? _this.$filterElm.removeClass('filled') : _this.$filterElm.addClass('filled');
                _this.callback(e, { columnDef: _this.columnDef, searchTerms: [value], shouldTriggerQuery: _this._shouldTriggerQuery });
            }
            // reset both flags for next use
            _this._clearFilterTriggered = false;
            _this._shouldTriggerQuery = true;
            if (e.keyCode == 13) {
                /** @type {?} */
                var $headerElm = _this.grid.getHeaderRowColumn(_this.columnDef.id);
                $($headerElm).parent().toggle();
            }
        }));
    };
    /**
     * Clear the filter value
     */
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    EnhancedInputSearch.prototype.clear = /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    function (shouldTriggerQuery) {
        if (shouldTriggerQuery === void 0) { shouldTriggerQuery = true; }
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            this.$filterElm.val('');
            this.$filterElm.trigger('keyup');
        }
    };
    /**
     * destroy the filter
     */
    /**
     * destroy the filter
     * @return {?}
     */
    EnhancedInputSearch.prototype.destroy = /**
     * destroy the filter
     * @return {?}
     */
    function () {
        if (this.$filterElm) {
            this.$filterElm.off('keyup').remove();
        }
    };
    /** Set value(s) on the DOM element */
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    EnhancedInputSearch.prototype.setValues = /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    function (values) {
        if (values) {
            this.$filterElm.val((/** @type {?} */ (values)));
        }
    };
    //
    // private functions
    // ------------------
    /**
     * Create the HTML template as a string
     */
    //
    // private functions
    // ------------------
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    EnhancedInputSearch.prototype.buildTemplateHtmlString = 
    //
    // private functions
    // ------------------
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var placeholder = 'search ' + this.columnDef.id;
        /** @type {?} */
        var $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        /** @type {?} */
        var elem = $('#' + this.grid.getUID() + this.columnDef.id);
        console.log('elem', $($headerElm).parent());
        elem.append("<div class=\"ms-parent ms-filter search-filter filter-" + this.columnDef.id + "\" style=\"width: 32px;\"><button id=\"filter-" + (this.grid.getUID() + this.columnDef.id) + "\" type=\"button\" class=\"ms-choice\"><span class=\"placeholder\" title=\"\" ></span><div></div></button> </div> \n       ");
        setTimeout((/**
         * @return {?}
         */
        function () {
            $('#filter-' + _this.grid.getUID() + _this.columnDef.id).click((/**
             * @return {?}
             */
            function () {
                $($headerElm).parent().toggle();
            }));
        }), 0);
        //       return `<input id="search-filter-${this.grid.getUID()+this.columnDef.id}"  type="${this._inputType || 'text'}" role="presentation"  autocomplete="off" class="form-control compound-input" placeholder="${placeholder}" /><span></span>`;
        return "\n      <input id=\"search-filter-" + (this.grid.getUID() + this.columnDef.id) + "\" type=\"text\" class=\"form-control search-filter\" placeholder=\"" + placeholder + "\">";
    };
    /**
     * From the html template string, create a DOM element
     * @param filterTemplate
     */
    /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    EnhancedInputSearch.prototype.createDomElement = /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    function (filterTemplate, searchTerm) {
        /** @type {?} */
        var $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        $($headerElm).empty();
        // create the DOM element & add an ID and filter class
        /** @type {?} */
        var $filterElm = $(filterTemplate);
        $filterElm.val(searchTerm);
        $filterElm.data('columnId', this.columnDef.id);
        // append the new DOM element to the header row
        if ($filterElm && typeof $filterElm.appendTo === 'function') {
            $filterElm.appendTo($headerElm);
        }
        return $filterElm;
    };
    return EnhancedInputSearch;
}());
export { EnhancedInputSearch };
if (false) {
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype._clearFilterTriggered;
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    EnhancedInputSearch.prototype.$filterElm;
    /** @type {?} */
    EnhancedInputSearch.prototype.grid;
    /** @type {?} */
    EnhancedInputSearch.prototype.searchTerms;
    /** @type {?} */
    EnhancedInputSearch.prototype.columnDef;
    /** @type {?} */
    EnhancedInputSearch.prototype.callback;
    /** @type {?} */
    EnhancedInputSearch.prototype.operator;
    /**
     * @type {?}
     * @protected
     */
    EnhancedInputSearch.prototype._inputType;
}
//# sourceMappingURL=data:application/json;base64,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