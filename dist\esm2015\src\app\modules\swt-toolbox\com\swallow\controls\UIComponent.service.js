/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { EventEmitter, Output, Input } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { EventDispatcher } from "../events/event-dispatcher.service";
import { Browsers } from "../model/Browsers";
import { FormControl, Validators } from "@angular/forms";
/** @type {?} */
const $ = require('jquery');
export class UIComponent extends EventDispatcher {
    /**
     * @param {?} uielement
     * @param {?} __commonService
     */
    constructor(uielement, __commonService) {
        super(uielement, __commonService);
        this.uielement = uielement;
        this.__commonService = __commonService;
        this._id = "dynamic-" + Math.random().toString(36).substr(2, 5);
        //---Outputs EventEmitter------------------------------------------------------------------------------------------------
        this.onClick_ = new EventEmitter();
        this.dbClick_ = new EventEmitter();
        this.doubleClick_ = new EventEmitter();
        this.itemDoubleClick_ = new EventEmitter();
        this.onKeyDown_ = new EventEmitter();
        this.onKeyUp_ = new EventEmitter();
        this.mouseUp_ = new EventEmitter();
        this.mouseOver_ = new EventEmitter();
        this.mouseDown_ = new EventEmitter();
        this.mouseEnter_ = new EventEmitter();
        this.mouseLeave_ = new EventEmitter();
        this.mouseOut_ = new EventEmitter();
        this.mouseIn_ = new EventEmitter();
        this.mouseMove_ = new EventEmitter();
        this.focus_ = new EventEmitter();
        this.focusIn_ = new EventEmitter();
        this.onFocusOut_ = new EventEmitter();
        this.keyFocusChange_ = new EventEmitter();
        this.change_ = new EventEmitter();
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this.scroll_ = new EventEmitter();
        //---Functions------------------------------------------------------------------------------------------------
        this._click = new Function();
        this._dbClick = new Function();
        this._doubleClick = new Function();
        this.__itemDoubleClick = new Function();
        this._keyDown = new Function();
        this._keyUp = new Function();
        this._mouseUp = new Function();
        this._mouseOver = new Function();
        this._mouseDown = new Function();
        this._mouseEnter = new Function();
        this._mouseLeave = new Function();
        this._mouseOut = new Function();
        this._mouseIn = new Function();
        this._mouseMove = new Function();
        this._focus = new Function();
        this._focusIn = new Function();
        this._focusOut = new Function();
        this._keyFocusChange = new Function();
        this._change = new Function();
        this._scroll = new Function();
        this.eventlist = [];
        this.cursorLocation = { x: 0, y: 0 };
        this.log = new Logger("UIComponent", this.__commonService.httpclient);
    }
    //---maxChars-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set maxChars(value) {
        this._maxChars = Number(value);
        setTimeout((/**
         * @return {?}
         */
        () => {
            $($(this.uielement.nativeElement).children()[0]).attr('maxlength', this._maxChars);
        }), 0);
    }
    /**
     * @return {?}
     */
    get maxChars() {
        return this._maxChars;
    }
    //---restrict-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set restrict(value) {
        this._restrict = value;
        $(this.uielement.nativeElement).on("keypress", (/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            //Check if pressed key is not a : backspace / delete / tabulation
            try {
                /** @type {?} */
                const control = new FormControl(String.fromCharCode(e.keyCode), Validators.pattern('[' + value + ']{1}'));
                if (!control.valid) {
                    e.preventDefault();
                }
            }
            catch (e) {
            }
        }));
    }
    /**
     * @return {?}
     */
    get restrict() {
        return this._restrict;
    }
    //---Id---------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set id(value) {
        // set id to the component's DOM.
        this._id = value;
        if (this.uielement && $(this.uielement.nativeElement))
            $($(this.uielement.nativeElement)[0]).attr("id", this._id);
    }
    /**
     * @return {?}
     */
    get id() {
        return this._id;
    }
    //--- Event Setters------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set click(value) {
        this._click = value;
        this.addEventListener('click', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.click(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dbClick(value) {
        this._dbClick = value;
        this.addEventListener('dblclick', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.dbClick(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set doubleClick(value) {
        this._doubleClick = value;
        this.addEventListener('dblclick', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.doubleClick(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set itemDoubleClick(value) {
        this.__itemDoubleClick = value;
        this.addEventListener('dblclick', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.itemDoubleClick(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set keyDown(value) {
        this._keyDown = value;
        this.addEventListener('keydown', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.keyDown(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set keyUp(value) {
        this._keyUp = value;
        this.addEventListener('keyup', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.keyUp(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseUp(value) {
        this._mouseUp = value;
        this.addEventListener('mouseup', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseUp(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseOver(value) {
        this._mouseOver = value;
        this.addEventListener('mouseover', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseOver(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseDown(value) {
        this._mouseDown = value;
        this.addEventListener('mousedown', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseDown(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseEnter(value) {
        this._mouseEnter = value;
        this.addEventListener('mouseenter', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseEnter(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseIn(value) {
        this._mouseIn = value;
        this.addEventListener('mouseenter', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseIn(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseMove(value) {
        this._mouseMove = value;
        this.addEventListener('mousemove', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseMove(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseLeave(value) {
        this._mouseLeave = value;
        this.addEventListener('mouseleave', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseLeave(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set mouseOut(value) {
        this._mouseOut = value;
        this.addEventListener('mouseleave', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.mouseOut(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set focus(value) {
        this._focus = value;
        this.addEventListener('focus', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.focus(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set focusIn(value) {
        this._focusIn = value;
        this.addEventListener('focus', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.focusIn(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set focusOut(value) {
        this._focusOut = value;
        this.addEventListener('focusout', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.focusOut(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set keyFocusChange(value) {
        this._keyFocusChange = value;
        this.addEventListener('focusout', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.keyFocusChange(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set change(value) {
        this._change = value;
        this.addEventListener('change', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.change(); }));
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set scroll(value) {
        this._scroll = value;
        this.addEventListener('scroll', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => { this.scroll(); }));
    }
    //--- Event Getters------------------------------------------------------------------------------------------------
    /**
     * @return {?}
     */
    get click() { return this._click; }
    /**
     * @return {?}
     */
    get dbClick() { return this._dbClick; }
    /**
     * @return {?}
     */
    get doubleClick() { return this._doubleClick; }
    /**
     * @return {?}
     */
    get itemDoubleClick() { return this.__itemDoubleClick; }
    /**
     * @return {?}
     */
    get keyDown() { return this._keyDown; }
    /**
     * @return {?}
     */
    get keyUp() { return this._keyUp; }
    /**
     * @return {?}
     */
    get mouseUp() { return this._mouseUp; }
    /**
     * @return {?}
     */
    get mouseOver() { return this._mouseOver; }
    /**
     * @return {?}
     */
    get mouseDown() { return this._mouseDown; }
    /**
     * @return {?}
     */
    get mouseEnter() { return this._mouseEnter; }
    /**
     * @return {?}
     */
    get mouseMove() { return this._mouseMove; }
    /**
     * @return {?}
     */
    get mouseLeave() { return this._mouseLeave; }
    /**
     * @return {?}
     */
    get mouseOut() { return this._mouseOut; }
    /**
     * @return {?}
     */
    get mouseIn() { return this._mouseIn; }
    /**
     * @return {?}
     */
    get focus() { return this._focus; }
    /**
     * @return {?}
     */
    get focusIn() { return this._focusIn; }
    /**
     * @return {?}
     */
    get focusOut() { return this._focusOut; }
    /**
     * @return {?}
     */
    get keyFocusChange() { return this._keyFocusChange; }
    /**
     * @return {?}
     */
    get change() { return this._change; }
    /**
     * @return {?}
     */
    get scroll() { return this._scroll; }
    /**
     * Adds a non-visual style client to this component instance.
     * @param {?} styleClient
     * @return {?}
     */
    addStyleClient(styleClient) {
    }
    /**
     * This method is used to get the mouse
     * pointer location.
     * @return {?}
     */
    getCursorLocation() {
        return this.cursorLocation;
    }
    /**
     * Queues a function to be called later.
     * @param {?} method
     * @param {?=} args
     * @return {?}
     */
    callLater(method, args = null) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this[method].apply(this, args);
        }), 0);
    }
    /**
     * Deletes a style property from this component instance.
     * @param {?} styleProp
     * @return {?}
     */
    clearStyle(styleProp) {
        try {
            $(this.uielement).css(styleProp, "");
        }
        catch (error) {
            this.log.error("clearStyle - error", error);
        }
    }
    /**
     * Converts a Point object from content coordinates to global coordinates.
     * @param {?} point
     * @return {?}
     */
    contentToGlobal(point) {
    }
    /**
     * Converts a Point object from content to local coordinates.
     * @param {?} point
     * @return {?}
     */
    contentToLocal(point) {
    }
    /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @return {?}
     */
    createAutomationIDPart(child) {
    }
    /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @param {?} properties
     * @return {?}
     */
    createAutomationIDPartWithRequiredProperties(child, properties) {
    }
    /**
     * Creates an id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    createReferenceOnParentDocument(parentDocument) {
    }
    /**
     * Deletes the id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    deleteReferenceOnParentDocument(parentDocument) {
    }
    /**
     * Returns a UITextFormat object corresponding to the text styles for this UIComponent.
     * @return {?}
     */
    determineTextFormatFromStyles() {
    }
    /**
     * Shows or hides the focus indicator around this component.
     * @param {?} isFocused
     * @return {?}
     */
    drawFocus(isFocused) {
    }
    /**
     * Programmatically draws a rectangle into this skin's Graphics object.
     * @param {?} x
     * @param {?} y
     * @param {?} w
     * @param {?} h
     * @param {?=} r
     * @param {?=} c
     * @param {?=} alpha
     * @param {?=} rot
     * @param {?=} gradient
     * @param {?=} ratios
     * @param {?=} hole
     * @return {?}
     */
    drawRoundRect(x, y, w, h, r = null, c = null, alpha = null, rot = null, gradient = null, ratios = null, hole = null) {
    }
    /**
     * Called by the effect instance when it stops playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    effectFinished(effectInst) {
    }
    /**
     * Called by the effect instance when it starts playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    effectStarted(effectInst) {
    }
    /**
     * Ends all currently playing effects on the component.
     * @return {?}
     */
    endEffectsStarted() {
    }
    /**
     * Executes all the bindings for which the UIComponent object is the destination.
     * @param {?=} recurse
     * @return {?}
     */
    executeBindings(recurse = false) {
    }
    /**
     * Called after printing is complete.
     * @param {?} obj
     * @param {?} target
     * @return {?}
     */
    finishPrint(obj, target) {
    }
    /**
     * Provides the automation object at the specified index.
     * @param {?} index
     * @return {?}
     */
    getAutomationChildAt(index) {
    }
    /**
     * Provides the automation object list .
     * @return {?}
     */
    getAutomationChildren() {
    }
    /**
     * Returns the x coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getBoundsXAtSize(width, height, postLayoutTransform = true) {
    }
    /**
     * Returns the y coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getBoundsYAtSize(width, height, postLayoutTransform = true) {
    }
    /**
     * Finds the type selectors for this UIComponent instance.
     * @return {?}
     */
    getClassStyleDeclarations() {
    }
    /**
     * Returns a layout constraint value, which is the same as getting the constraint style for this component.
     * @param {?} constraintName
     * @return {?}
     */
    getConstraintValue(constraintName) {
    }
    /**
     * A convenience method for determining whether to use the explicit or measured height
     * @return {?}
     */
    getExplicitOrMeasuredHeight() {
    }
    /**
     * A convenience method for determining whether to use the explicit or measured width
     * @return {?}
     */
    getExplicitOrMeasuredWidth() {
    }
    /**
     * Gets the object that currently has focus.
     * @return {?}
     */
    getFocus() {
    }
    /**
     * Returns the element's layout height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getLayoutBoundsHeight(postLayoutTransform = true) {
    }
    /**
     * Returns the element's layout width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getLayoutBoundsWidth(postLayoutTransform = true) {
    }
    /**
     * Returns the x coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getLayoutBoundsX(postLayoutTransform = true) {
    }
    /**
     * Returns the y coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getLayoutBoundsY(postLayoutTransform = true) {
    }
    /**
     * Returns the transform matrix that is used to calculate the component's layout relative to its siblings.
     * @return {?}
     */
    getLayoutMatrix() {
    }
    /**
     * Returns the layout transform Matrix3D for this element.
     * @return {?}
     */
    getLayoutMatrix3D() {
    }
    /**
     * Returns the element's maximum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getMaxBoundsHeight(postLayoutTransform = true) {
    }
    /**
     * Returns the element's maximum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getMaxBoundsWidth(postLayoutTransform = true) {
    }
    /**
     * Returns the element's minimum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getMinBoundsHeight(postLayoutTransform = true) {
    }
    /**
     * Returns the element's minimum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getMinBoundsWidth(postLayoutTransform = true) {
    }
    /**
     * Returns the element's preferred height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getPreferredBoundsHeight(postLayoutTransform = true) {
    }
    /**
     * Returns the element's preferred width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    getPreferredBoundsWidth(postLayoutTransform = true) {
    }
    /**
     * Returns the item in the dataProvider that was used by the specified Repeater to produce this Repeater, or null if this Repeater isn't repeated.
     * @param {?=} whichRepeater
     * @return {?}
     */
    getRepeaterItem(whichRepeater = -1) {
    }
    /**
     * Gets a style property that has been set anywhere in this component's style lookup chain.
     * @param {?} styleProp
     * @return {?}
     */
    getStyle(styleProp) {
    }
    /**
     * Converts a Point object from global to content coordinates.
     * @param {?} point
     * @return {?}
     */
    globalToContent(point) {
    }
    /**
     * Returns true if currentCSSState is not null.
     * @return {?}
     */
    hasCSSState() {
    }
    /**
     * Determines whether the specified state has been defined on this UIComponent.
     * @param {?} stateName
     * @return {?}
     */
    hasState(stateName) {
    }
    /**
     * This method will return the browser type.
     * @return {?}
     */
    getBroserType() {
        /** @type {?} */
        var isOpera = false;
        /** @type {?} */
        var isFirefox = false;
        /** @type {?} */
        var isSafari = false;
        /** @type {?} */
        var isIE = false;
        /** @type {?} */
        var isChrome = false;
        /** @type {?} */
        var isFirefox = false;
        if ((navigator.userAgent.indexOf("Opera") || navigator.userAgent.indexOf('OPR')) !== -1) {
            isOpera = true;
        }
        else if (navigator.userAgent.indexOf("Chrome") !== -1) {
            isChrome = true;
        }
        else if (navigator.userAgent.indexOf("Safari") !== -1) {
            isSafari = true;
        }
        else if (navigator.userAgent.indexOf("Firefox") !== -1) {
            isFirefox = true;
        }
        else if ((navigator.userAgent.indexOf("MSIE") !== -1) || (!!((/** @type {?} */ (document))).documentMode === true)) { //IF IE > 10
            isIE = true;
        }
        if (isOpera) {
            return Browsers.OPERA;
        }
        else if (isFirefox) {
            return Browsers.FIREFOX;
        }
        else if (isSafari) {
            return Browsers.SAFARI;
        }
        else if (isIE) {
            return Browsers.IE;
        }
        else if (isChrome) {
            return Browsers.CHROME;
        }
        else {
            return Browsers.UNKNOWN_BROWSER;
        }
    }
    /**
     * Returns a box Matrix which can be passed to the drawRoundRect() method as the rot parameter when drawing a horizontal gradient.
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    horizontalGradientMatrix(x, y, width, height) {
    }
    /**
     * Initializes the internal structure of this component.
     * @return {?}
     */
    initialize() {
    }
    /**
     *  Sets a style property on this component instance.
     *
     *  <p>This can override a style that was set globally.</p>
     *
     *  <p>Calling the <code>setStyle()</code> method can result in decreased performance.
     *  Use it only when necessary.</p>
     *
     * @param {?} styleProp Name of the style property.
     *
     * @param {?} newValue New value for the style.
     * @return {?}
     */
    setStyle(styleProp, newValue) {
        try {
            $(this.uielement).css(styleProp, newValue);
        }
        catch (error) {
            this.log.error("setStyle - error: ", error);
        }
    }
    /**
     *  Sets the focus to this component.
     *  The component can in turn pass focus to a subcomponent.
     *  <p><b>Note:</b> Only the TextInput and TextArea controls show a highlight
     *  when this method sets the focus.
     *  All controls show a highlight when the user tabs to the control.</p>
     * @return {?}
     */
    setFocus() {
    }
    /**
     *  Called when the <code>visible</code> property changes.
     *  Set the <code>visible</code> property to show or hide
     *  a component instead of calling this method directly.
     *
     * @param {?} value The new value of the <code>visible</code> property.
     *  Specify <code>true</code> to show the component, and <code>false</code> to hide it.
     *
     * @param {?=} noEvent If <code>true</code>, do not dispatch an event.
     *  If <code>false</code>, dispatch a <code>show</code> event when
     *  the component becomes visible, and a <code>hide</code> event when
     *  the component becomes invisible.
     * @return {?}
     */
    setVisible(value, noEvent = false) {
    }
    /**
     * This method is used to add child to current
     * view.
     * @param {?} child
     * @return {?}
     */
    addChild(child) {
        try {
        }
        catch (error) {
            this.log.error("addChild - error: ", error);
        }
    }
    /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    addEventsListenersForTooltip(element) {
        // Subscribe to mouse enter event.
        if (this.mouseOver_.observers.length > 0 || this._mouseOver.name != 'anonymous') {
            this.addEventListener('mouseover', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseOver_.emit(event);
                this.mouseOver();
            }));
        }
        // Subscribe to mouse leave/out event.
        if (this.mouseLeave_.observers.length > 0 || this.mouseOut_.observers.length > 0 || this._mouseLeave.name != 'anonymous' || this._mouseOut.name != 'anonymous') {
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseLeave_.emit(event);
                this.mouseOut_.emit(event);
                this.mouseLeave();
                this.mouseOut();
            }));
        }
        // Subscribe to mouse down event.
        if (this.mouseDown_.observers.length > 0 || this._mouseDown.name != 'anonymous') {
            this.addEventListener('mousedown', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseDown_.emit(event);
                this.mouseDown();
            }));
        }
        // Subscribe to mouse up event.
        if (this.mouseUp_.observers.length > 0 || this._mouseUp.name != 'anonymous') {
            this.addEventListener('mouseup', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseUp_.emit(event);
                this.mouseUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseEnter_.observers.length > 0 || this.mouseIn_.observers.length > 0 || this._mouseEnter.name != 'anonymous' || this._mouseIn.name != 'anonymous') {
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseEnter_.emit(event);
                this.mouseIn_.emit(event);
                this.mouseIn();
                this.mouseEnter();
            }));
        }
        // Subscribe to mouse move event.
        if (this.mouseMove_.observers.length > 0 || this._mouseMove.name != 'anonymous') {
            this.addEventListener('mousemove', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseMove_.emit(event);
                this.mouseMove();
            }));
        }
    }
    /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    addAllOutputsEventsListeners(element) {
        // Subscribe to click event.
        if (this.onClick_.observers.length > 0 || this._click.name != 'anonymous') {
            // Attach Click event to the component.
            this.addEventListener('click', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.onClick_.emit(event);
                this.click();
            }));
        }
        // Subscribe to click event.
        if (this.dbClick_.observers.length > 0 || this.doubleClick_.observers.length > 0 || this.itemDoubleClick_.observers.length > 0 ||
            this._doubleClick.name != 'anonymous' || this._dbClick.name != 'anonymous' || this.__itemDoubleClick.name != 'anonymous') {
            // Attach double click event to the component.
            this.addEventListener('dblclick', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.dbClick_.emit(event);
                this.doubleClick_.emit(event);
                this.itemDoubleClick_.emit(event);
                this.dbClick();
                this.doubleClick();
                this.itemDoubleClick();
            }), true);
        }
        // Subscribe to focus event.
        if (this.focus_.observers.length > 0 || this.focusIn_.observers.length > 0 || this._focus.name != 'anonymous' || this._focusIn.name != 'anonymous') {
            this.addEventListener('focus', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.focus_.emit(event);
                this.focusIn_.emit(event);
                this.focus();
                this.focusIn();
            }));
        }
        // Subscribe to focusout event.
        if (this.onFocusOut_.observers.length > 0 || this.keyFocusChange_.observers.length > 0 || this._focusOut.name != 'anonymous' || this._keyFocusChange.name != 'anonymous') {
            // Attach focusout event to the component.
            this.addEventListener('focusout', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.onFocusOut_.emit(event);
                this.keyFocusChange_.emit(event);
                this.focusOut();
                this.keyFocusChange();
            }));
        }
        // Subscribe to key down event.
        if (this.getComponentName() != "SWTTEXTINPUT" && (this.onKeyDown_.observers.length > 0 || this._keyDown.name != 'anonymous')) {
            // Attach keyDown event to the component except SwtTextInput because it has its one event to handle restrict so we wont override it.
            this.addEventListener('keydown', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.onKeyDown_.emit(event);
                this._keyDown;
            }));
        }
        // Subscribe to key up event.
        if (this.onKeyUp_.observers.length > 0 || this._keyUp.name != 'anonymous') {
            this.addEventListener('keyup', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.onKeyUp_.emit(event);
                this.keyUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseOver_.observers.length > 0 || this._mouseOver.name != 'anonymous') {
            this.addEventListener('mouseover', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseOver_.emit(event);
                this.mouseOver();
            }));
        }
        // Subscribe to mouse leave/out event.
        if (this.mouseLeave_.observers.length > 0 || this.mouseOut_.observers.length > 0 || this._mouseLeave.name != 'anonymous' || this._mouseOut.name != 'anonymous') {
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseLeave_.emit(event);
                this.mouseOut_.emit(event);
                this.mouseLeave();
                this.mouseOut();
            }));
        }
        // Subscribe to mouse down event.
        if (this.mouseDown_.observers.length > 0 || this._mouseDown.name != 'anonymous') {
            this.addEventListener('mousedown', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseDown_.emit(event);
                this.mouseDown();
            }));
        }
        // Subscribe to mouse up event.
        if (this.mouseUp_.observers.length > 0 || this._mouseUp.name != 'anonymous') {
            this.addEventListener('mouseup', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseUp_.emit(event);
                this.mouseUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseEnter_.observers.length > 0 || this.mouseIn_.observers.length > 0 || this._mouseEnter.name != 'anonymous' || this._mouseIn.name != 'anonymous') {
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseEnter_.emit(event);
                this.mouseIn_.emit(event);
                this.mouseIn();
                this.mouseEnter();
            }));
        }
        // Subscribe to mouse move event.
        if (this.mouseMove_.observers.length > 0 || this._mouseMove.name != 'anonymous') {
            this.addEventListener('mousemove', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.mouseMove_.emit(event);
                this.mouseMove();
            }));
        }
        // Subscribe to change event.
        if (this.change_.observers.length > 0 || this._change.name != 'anonymous') {
            this.addEventListener('change', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.change_.emit(event);
                this.change();
            }));
        }
        // Subscribe to scroll event.
        if (this.scroll_.observers.length > 0 || this._scroll.name != 'anonymous') {
            this.addEventListener('scroll', (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.scroll_.emit(event);
                this.scroll();
            }));
        }
    }
    /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    removeAllOuputsEventsListeners(element) {
        if (element && element[0]) {
            // - Remove All native event Listeners of Zone.js.
            ((/** @type {?} */ (element)))[0].removeAllListeners('click');
            ((/** @type {?} */ (element)))[0].removeAllListeners('dblclick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('doubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('itemDoubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focus');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusIn');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusout');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusOut');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyFocusChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyup');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyUp');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseover');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseOver');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseleave');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseLeave');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mousedown');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseup');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseUp');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseenter');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseEnter');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mousemove');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseMove');
            ((/** @type {?} */ (element)))[0].removeAllListeners('change');
            ((/** @type {?} */ (element)))[0].removeAllListeners('onSpyChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('onSpyNoChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('click');
            ((/** @type {?} */ (element)))[0].removeAllListeners('dblclick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('doubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('itemDoubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focus');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusIn');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusout');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusOut');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyFocusChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('scroll');
            if (!this.restrict) {
                // we should not remove keydown event handler on component having restrict.
                ((/** @type {?} */ (element)))[0].removeAllListeners('keydown');
            }
        }
        //console.log('[container] removeEventsListeners - hostElement .eventListeners() :',(this.hostElement  as any).eventListeners());
        //console.log('[container] removeEventsListeners - domElement  .eventListeners() :',(this.domElement  as any).eventListeners());
    }
    /**
     * @return {?}
     */
    getComponentName() {
        return $(this.uielement.nativeElement) ? $(this.uielement.nativeElement)[0].tagName : undefined;
    }
    /**
     * This function used to adapt layout measure
     * it will append px if no % sign.
     * @param {?} value
     * @param {?=} defaultValue
     * @return {?}
     */
    adaptUnit(value, defaultValue) {
        /** @type {?} */
        let rtn = "";
        if (value) {
            if (String(value).indexOf("%") === -1) {
                rtn = String(value) + "px";
            }
            else {
                rtn = String(value);
            }
            return rtn;
        }
        else {
            return (defaultValue ? (defaultValue == "auto" ? defaultValue : defaultValue + "px") : "0px");
        }
    }
    /**
     * convert entered value as string to boolean.
     * @param {?} value
     * @return {?}
     */
    adaptValueAsBoolean(value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                value = false;
            }
            else {
                value = true;
            }
        }
        return value;
    }
    /**
     * ngOnDestroy
     * @return {?}
     */
    ngOnDestroy() {
        try {
            //console.log('[UIComponent] ngOnDestroy');
            delete this._click;
            delete this._dbClick;
            delete this._doubleClick;
            delete this.__itemDoubleClick;
            delete this._keyDown;
            delete this._keyUp;
            delete this._mouseUp;
            delete this._mouseOver;
            delete this._mouseDown;
            delete this._mouseEnter;
            delete this._mouseLeave;
            delete this._mouseMove;
            delete this._mouseIn;
            delete this._focus;
            delete this._focusIn;
            delete this._focusOut;
            delete this.eventlist;
            delete this.cursorLocation;
            delete this.uielement;
            delete this.__commonService;
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    }
}
UIComponent.propDecorators = {
    maxChars: [{ type: Input }],
    restrict: [{ type: Input, args: ['restrict',] }],
    id: [{ type: Input, args: ['id',] }],
    onClick_: [{ type: Output, args: ["click",] }],
    dbClick_: [{ type: Output, args: ["dbClick",] }],
    doubleClick_: [{ type: Output, args: ["doubleClick",] }],
    itemDoubleClick_: [{ type: Output, args: ["itemDoubleClick",] }],
    onKeyDown_: [{ type: Output, args: ["keyDown",] }],
    onKeyUp_: [{ type: Output, args: ["keyUp",] }],
    mouseUp_: [{ type: Output, args: ["mouseUp",] }],
    mouseOver_: [{ type: Output, args: ["mouseOver",] }],
    mouseDown_: [{ type: Output, args: ["mouseDown",] }],
    mouseEnter_: [{ type: Output, args: ["mouseEnter",] }],
    mouseLeave_: [{ type: Output, args: ["mouseLeave",] }],
    mouseOut_: [{ type: Output, args: ["mouseOut",] }],
    mouseIn_: [{ type: Output, args: ["mouseIn",] }],
    mouseMove_: [{ type: Output, args: ["mouseMove",] }],
    focus_: [{ type: Output, args: ["focus",] }],
    focusIn_: [{ type: Output, args: ["focusIn",] }],
    onFocusOut_: [{ type: Output, args: ["focusOut",] }],
    keyFocusChange_: [{ type: Output, args: ["keyFocusChange",] }],
    change_: [{ type: Output, args: ["change",] }],
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    scroll_: [{ type: Output, args: ['scroll',] }]
};
if (false) {
    /** @type {?} */
    UIComponent.prototype.name;
    /** @type {?} */
    UIComponent.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._id;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._restrict;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._maxChars;
    /** @type {?} */
    UIComponent.prototype.onClick_;
    /** @type {?} */
    UIComponent.prototype.dbClick_;
    /** @type {?} */
    UIComponent.prototype.doubleClick_;
    /** @type {?} */
    UIComponent.prototype.itemDoubleClick_;
    /** @type {?} */
    UIComponent.prototype.onKeyDown_;
    /** @type {?} */
    UIComponent.prototype.onKeyUp_;
    /** @type {?} */
    UIComponent.prototype.mouseUp_;
    /** @type {?} */
    UIComponent.prototype.mouseOver_;
    /** @type {?} */
    UIComponent.prototype.mouseDown_;
    /** @type {?} */
    UIComponent.prototype.mouseEnter_;
    /** @type {?} */
    UIComponent.prototype.mouseLeave_;
    /** @type {?} */
    UIComponent.prototype.mouseOut_;
    /** @type {?} */
    UIComponent.prototype.mouseIn_;
    /** @type {?} */
    UIComponent.prototype.mouseMove_;
    /** @type {?} */
    UIComponent.prototype.focus_;
    /** @type {?} */
    UIComponent.prototype.focusIn_;
    /** @type {?} */
    UIComponent.prototype.onFocusOut_;
    /** @type {?} */
    UIComponent.prototype.keyFocusChange_;
    /** @type {?} */
    UIComponent.prototype.change_;
    /** @type {?} */
    UIComponent.prototype.onSpyChange;
    /** @type {?} */
    UIComponent.prototype.onSpyNoChange;
    /** @type {?} */
    UIComponent.prototype.scroll_;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._click;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._dbClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._doubleClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.__itemDoubleClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyDown;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyUp;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseUp;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseOver;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseDown;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseEnter;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseLeave;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseOut;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseIn;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseMove;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focusIn;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyFocusChange;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._change;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._scroll;
    /**
     * @type {?}
     * @protected
     */
    UIComponent.prototype.eventlist;
    /**
     * @type {?}
     * @protected
     */
    UIComponent.prototype.cursorLocation;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.uielement;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.__commonService;
}
/**
 * @record
 */
export function IUIComponent() { }
if (false) {
    /** @type {?} */
    IUIComponent.prototype.name;
    /** @type {?} */
    IUIComponent.prototype.id;
    /** @type {?} */
    IUIComponent.prototype.styleName;
}
//# sourceMappingURL=data:application/json;base64,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