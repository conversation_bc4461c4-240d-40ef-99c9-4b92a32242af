/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { SwtCommonGrid } from './swt-common-grid.component';
import { SharedService, ExtensionUtility, ExtensionService, AutoTooltipExtension, CollectionService, } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../utils/common.service';
export class SwtTreeCommonGrid extends SwtCommonGrid {
    /**
     * @param {?} el
     * @param {?} commonService
     * @param {?} autoTooltipExtension
     * @param {?} extensionUtility
     * @param {?} sharedService
     * @param {?} collectionService
     * @param {?} translate
     */
    constructor(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate) {
        super(el, commonService, autoTooltipExtension, extensionUtility, sharedService, collectionService, translate);
        this.el = el;
        this.commonService = commonService;
        this.autoTooltipExtension = autoTooltipExtension;
        this.extensionUtility = extensionUtility;
        this.sharedService = sharedService;
        this.translate = translate;
        this.isTreeGrid = true;
    }
}
SwtTreeCommonGrid.decorators = [
    { type: Component, args: [{
                selector: 'SwtTreeCommonGrid',
                template: `
    <angular-slickgrid
            #angularSlickGrid
            class="commonSlickGrid"
            gridId='grid-{{id}}'
            (onDataviewCreated)="dataviewReady($event)"
            (onAngularGridCreated)="onAngularGridCreated($event)"
            [columnDefinitions]="columnDefinitions"
            [gridOptions]="gridOptions"
            gridHeight="100%"
            gridWidth="100%"
            [dataset]="dataset"
    >
    </angular-slickgrid>

`,
                providers: [
                    TranslateService,
                    ExtensionService,
                    AutoTooltipExtension,
                    ExtensionUtility,
                    SharedService,
                    CollectionService
                ],
                styles: [`
    .gridContent{
        min-width: 300px;
        height: 100%;
    }
    :host ::ng-deep .gridPane {
        overflow: auto;
        display: block;
    }
    :host ::ng-deep .slickgrid-container {
        min-height: 100%;
    }


`]
            }] }
];
/** @nocollapse */
SwtTreeCommonGrid.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService },
    { type: AutoTooltipExtension },
    { type: ExtensionUtility },
    { type: SharedService },
    { type: CollectionService },
    { type: TranslateService }
];
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.el;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.commonService;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.autoTooltipExtension;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.extensionUtility;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.sharedService;
    /**
     * @type {?}
     * @protected
     */
    SwtTreeCommonGrid.prototype.translate;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWNvbW1vbi10cmVlLWdyaWQuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1jb21tb24tdHJlZS1ncmlkLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUNtRCxTQUFTLEVBRTBELFVBQVUsRUFDdEksTUFBTSxlQUFlLENBQUM7QUFDdkIsT0FBTyxFQUFFLGFBQWEsRUFBRSxNQUFNLDZCQUE2QixDQUFDO0FBQzVELE9BQU8sRUE0QkgsYUFBYSxFQU1iLGdCQUFnQixFQUdoQixnQkFBZ0IsRUFDaEIsb0JBQW9CLEVBV3BCLGlCQUFpQixHQUNwQixNQUFNLG1CQUFtQixDQUFDO0FBQzNCLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLHFCQUFxQixDQUFDO0FBQ3ZELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSx5QkFBeUIsQ0FBQztBQTZDeEQsTUFBTSxPQUFPLGlCQUFrQixTQUFRLGFBQWE7Ozs7Ozs7Ozs7SUFFaEQsWUFBdUIsRUFBYyxFQUFZLGFBQTRCLEVBQWEsb0JBQTJDLEVBQVksZ0JBQWtDLEVBQVksYUFBNkIsRUFDdk4saUJBQXFDLEVBQWEsU0FBMkI7UUFDOUUsS0FBSyxDQUFDLEVBQUUsRUFBQyxhQUFhLEVBQUMsb0JBQW9CLEVBQUUsZ0JBQWdCLEVBQUMsYUFBYSxFQUFFLGlCQUFpQixFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBRnhGLE9BQUUsR0FBRixFQUFFLENBQVk7UUFBWSxrQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUFhLHlCQUFvQixHQUFwQixvQkFBb0IsQ0FBdUI7UUFBWSxxQkFBZ0IsR0FBaEIsZ0JBQWdCLENBQWtCO1FBQVksa0JBQWEsR0FBYixhQUFhLENBQWdCO1FBQ3JLLGNBQVMsR0FBVCxTQUFTLENBQWtCO1FBRTlFLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO0lBQzNCLENBQUM7OztZQWpESixTQUFTLFNBQUU7Z0JBQ1IsUUFBUSxFQUFFLG1CQUFtQjtnQkFDN0IsUUFBUSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Q0FlYjtnQkFnQkQsU0FBUyxFQUFFO29CQUNQLGdCQUFnQjtvQkFDaEIsZ0JBQWdCO29CQUNoQixvQkFBb0I7b0JBQ3BCLGdCQUFnQjtvQkFDaEIsYUFBYTtvQkFDYixpQkFBaUI7aUJBQ3BCO3lCQXRCUTs7Ozs7Ozs7Ozs7Ozs7Q0FjUjthQVNBOzs7O1lBbEc0SCxVQUFVO1lBdUQ5SCxhQUFhO1lBZGxCLG9CQUFvQjtZQUpwQixnQkFBZ0I7WUFOaEIsYUFBYTtZQXFCYixpQkFBaUI7WUFFWixnQkFBZ0I7Ozs7Ozs7SUFnRFIsK0JBQXdCOzs7OztJQUFFLDBDQUFzQzs7Ozs7SUFBRyxpREFBcUQ7Ozs7O0lBQUUsNkNBQTRDOzs7OztJQUFFLDBDQUF1Qzs7Ozs7SUFDL0ssc0NBQXFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICAgIEFmdGVyQ29udGVudENoZWNrZWQsIEFmdGVyVmlld0NoZWNrZWQsIEFmdGVyVmlld0luaXQsIENvbXBvbmVudCwgQ29tcG9uZW50RmFjdG9yeSxcclxuICAgIENvbXBvbmVudEZhY3RvcnlSZXNvbHZlciwgQ29tcG9uZW50UmVmLEV2ZW50RW1pdHRlcixcclxuICAgIEluamVjdGFibGUsIElucHV0LCBPbkNoYW5nZXMsIE9uSW5pdCwgT3V0cHV0LCBSZW5kZXJlciwgVmlld0NoaWxkLCBWaWV3Q29udGFpbmVyUmVmLE9uRGVzdHJveSwgSW5qZWN0b3IsIEFwcGxpY2F0aW9uUmVmLCBFbGVtZW50UmVmXHJcbn0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IFN3dENvbW1vbkdyaWQgfSBmcm9tICcuL3N3dC1jb21tb24tZ3JpZC5jb21wb25lbnQnO1xyXG5pbXBvcnQge1xyXG4gICAgQWdncmVnYXRvcnMsXHJcbiAgICBBbmd1bGFyR3JpZEluc3RhbmNlLFxyXG4gICAgQW5ndWxhclNsaWNrZ3JpZENvbXBvbmVudCxcclxuICAgIEJhY2tlbmRTZXJ2aWNlT3B0aW9uLFxyXG4gICAgQ2VsbEFyZ3MsXHJcbiAgICBDb2x1bW4sXHJcbiAgICBDb2x1bW5GaWx0ZXJzLFxyXG4gICAgRWRpdG9ycyxcclxuICAgIEV4cG9ydFNlcnZpY2UsXHJcbiAgICBGaWVsZFR5cGUsXHJcbiAgICBGaWx0ZXJDaGFuZ2VkQXJncyxcclxuICAgIEZpbHRlckZhY3RvcnksXHJcbiAgICBGaWx0ZXJzLFxyXG4gICAgRmlsdGVyU2VydmljZSxcclxuICAgIEZvcm1hdHRlcixcclxuICAgIEZvcm1hdHRlcnMsXHJcbiAgICBHcmlkTWVudUV4dGVuc2lvbixcclxuICAgIEdyaWRPcHRpb24sXHJcbiAgICBHcmlkU2VydmljZSxcclxuICAgIEdyaWRTdGF0ZUNoYW5nZSxcclxuICAgIEdyaWRTdGF0ZVNlcnZpY2UsXHJcbiAgICBHcm91cGluZyxcclxuICAgIEdyb3VwVG90YWxGb3JtYXR0ZXJzLFxyXG4gICAgT25FdmVudEFyZ3MsXHJcbiAgICBQYWdpbmF0aW9uLFxyXG4gICAgUGFnaW5hdGlvbkNoYW5nZWRBcmdzLFxyXG4gICAgUmVzaXplclNlcnZpY2UsXHJcbiAgICBTaGFyZWRTZXJ2aWNlLFxyXG4gICAgU2xpY2tncmlkQ29uZmlnLFxyXG4gICAgU29ydENoYW5nZWRBcmdzLFxyXG4gICAgU29ydERpcmVjdGlvbk51bWJlcixcclxuICAgIFNvcnRlcnMsXHJcbiAgICBTb3J0U2VydmljZSxcclxuICAgIEV4dGVuc2lvblV0aWxpdHksXHJcbiAgICBFeHRlbnNpb25OYW1lLFxyXG4gICAgSGVhZGVyTWVudSxcclxuICAgIEV4dGVuc2lvblNlcnZpY2UsXHJcbiAgICBBdXRvVG9vbHRpcEV4dGVuc2lvbixcclxuICAgIENlbGxFeHRlcm5hbENvcHlNYW5hZ2VyRXh0ZW5zaW9uLFxyXG4gICAgQ29sdW1uUGlja2VyRXh0ZW5zaW9uLFxyXG4gICAgRHJhZ2dhYmxlR3JvdXBpbmdFeHRlbnNpb24sXHJcbiAgICBIZWFkZXJNZW51RXh0ZW5zaW9uLFxyXG4gICAgSGVhZGVyQnV0dG9uRXh0ZW5zaW9uLFxyXG4gICAgR3JvdXBJdGVtTWV0YVByb3ZpZGVyRXh0ZW5zaW9uLFxyXG4gICAgQ2hlY2tib3hTZWxlY3RvckV4dGVuc2lvbixcclxuICAgIFJvd0RldGFpbFZpZXdFeHRlbnNpb24sXHJcbiAgICBSb3dNb3ZlTWFuYWdlckV4dGVuc2lvbixcclxuICAgIFJvd1NlbGVjdGlvbkV4dGVuc2lvbixcclxuICAgIENvbGxlY3Rpb25TZXJ2aWNlLFxyXG59IGZyb20gJ2FuZ3VsYXItc2xpY2tncmlkJztcclxuaW1wb3J0IHsgVHJhbnNsYXRlU2VydmljZSB9IGZyb20gJ0BuZ3gtdHJhbnNsYXRlL2NvcmUnO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSAnLi4vdXRpbHMvY29tbW9uLnNlcnZpY2UnO1xyXG5cclxuQENvbXBvbmVudCgge1xyXG4gICAgc2VsZWN0b3I6ICdTd3RUcmVlQ29tbW9uR3JpZCcsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgPGFuZ3VsYXItc2xpY2tncmlkXHJcbiAgICAgICAgICAgICNhbmd1bGFyU2xpY2tHcmlkXHJcbiAgICAgICAgICAgIGNsYXNzPVwiY29tbW9uU2xpY2tHcmlkXCJcclxuICAgICAgICAgICAgZ3JpZElkPSdncmlkLXt7aWR9fSdcclxuICAgICAgICAgICAgKG9uRGF0YXZpZXdDcmVhdGVkKT1cImRhdGF2aWV3UmVhZHkoJGV2ZW50KVwiXHJcbiAgICAgICAgICAgIChvbkFuZ3VsYXJHcmlkQ3JlYXRlZCk9XCJvbkFuZ3VsYXJHcmlkQ3JlYXRlZCgkZXZlbnQpXCJcclxuICAgICAgICAgICAgW2NvbHVtbkRlZmluaXRpb25zXT1cImNvbHVtbkRlZmluaXRpb25zXCJcclxuICAgICAgICAgICAgW2dyaWRPcHRpb25zXT1cImdyaWRPcHRpb25zXCJcclxuICAgICAgICAgICAgZ3JpZEhlaWdodD1cIjEwMCVcIlxyXG4gICAgICAgICAgICBncmlkV2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgW2RhdGFzZXRdPVwiZGF0YXNldFwiXHJcbiAgICA+XHJcbiAgICA8L2FuZ3VsYXItc2xpY2tncmlkPlxyXG5cclxuYCxcclxuc3R5bGVzOiBbYFxyXG4gICAgLmdyaWRDb250ZW50e1xyXG4gICAgICAgIG1pbi13aWR0aDogMzAwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgfVxyXG4gICAgOmhvc3QgOjpuZy1kZWVwIC5ncmlkUGFuZSB7XHJcbiAgICAgICAgb3ZlcmZsb3c6IGF1dG87XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB9XHJcbiAgICA6aG9zdCA6Om5nLWRlZXAgLnNsaWNrZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgIG1pbi1oZWlnaHQ6IDEwMCU7XHJcbiAgICB9XHJcblxyXG5cclxuYF0sXHJcbnByb3ZpZGVyczogW1xyXG4gICAgVHJhbnNsYXRlU2VydmljZSxcclxuICAgIEV4dGVuc2lvblNlcnZpY2UsXHJcbiAgICBBdXRvVG9vbHRpcEV4dGVuc2lvbixcclxuICAgIEV4dGVuc2lvblV0aWxpdHksXHJcbiAgICBTaGFyZWRTZXJ2aWNlLFxyXG4gICAgQ29sbGVjdGlvblNlcnZpY2VcclxuXSxcclxufSApXHJcblxyXG5leHBvcnQgY2xhc3MgU3d0VHJlZUNvbW1vbkdyaWQgZXh0ZW5kcyBTd3RDb21tb25HcmlkIGltcGxlbWVudHMgT25Jbml0LCBBZnRlclZpZXdJbml0ICwgT25EZXN0cm95IHtcclxuICAgXHJcbiAgICBjb25zdHJ1Y3RvciggcHJvdGVjdGVkIGVsOiBFbGVtZW50UmVmLCBwcm90ZWN0ZWQgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSAsIHByb3RlY3RlZCBhdXRvVG9vbHRpcEV4dGVuc2lvbiA6IEF1dG9Ub29sdGlwRXh0ZW5zaW9uLCBwcm90ZWN0ZWQgZXh0ZW5zaW9uVXRpbGl0eSA6RXh0ZW5zaW9uVXRpbGl0eSwgcHJvdGVjdGVkIHNoYXJlZFNlcnZpY2UgOiBTaGFyZWRTZXJ2aWNlXHJcbiAgICAgICAgLGNvbGxlY3Rpb25TZXJ2aWNlIDogQ29sbGVjdGlvblNlcnZpY2UgLCBwcm90ZWN0ZWQgdHJhbnNsYXRlOiBUcmFuc2xhdGVTZXJ2aWNlKSB7XHJcbiAgICAgICAgc3VwZXIoZWwsY29tbW9uU2VydmljZSxhdXRvVG9vbHRpcEV4dGVuc2lvbiwgZXh0ZW5zaW9uVXRpbGl0eSxzaGFyZWRTZXJ2aWNlLCBjb2xsZWN0aW9uU2VydmljZSwgdHJhbnNsYXRlKTtcclxuICAgICAgICB0aGlzLmlzVHJlZUdyaWQgPSB0cnVlO1xyXG4gICAgfVxyXG59XHJcbiJdfQ==