/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { CellBackgroundColor, CustomCell } from "./cellItemRenderUtilities";
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
export var NonEditableCheckboxRenderer = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var field = columnDef.field;
    // return `<input type="checkbox" value="false" class="editor-checkbox" hideFocus />`;
    /** @type {?} */
    var text;
    /** @type {?} */
    var backgroundColorCell = CellBackgroundColor(dataContext, field);
    /** @type {?} */
    var backgroundColor = columnDef.params.rowColorFunction(dataContext, row, 'transparent', columnDef.field);
    /** @type {?} */
    var enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    var style = CustomCell(dataContext, field);
    /** @type {?} */
    var negative = false;
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (style == "") {
        if (backgroundColor == undefined && !backgroundColorCell) {
            backgroundColor = 'transparent';
        }
        else if (backgroundColorCell) {
            backgroundColor = '#C9C9C9';
        }
        style += 'background-color:' + backgroundColor + (!enableRowSelection ? ' !important; ' : ';');
        if (negative) {
            style += 'color: #ff0000; ';
        }
        else {
            style += 'color: #173553; ';
        }
    }
    if (value === 'N' || value === 'false') {
        // non checked
        //text  = '&#x2716;';
        text = "<div class=\"containerCheckBox\" style='" + style + "' ><input disabled   type='checkbox' value='false' class='formator-checkbox' hideFocus /></div>";
    }
    else {
        // checked
        //text = '&#x2714;';
        text = "<div class=\"containerCheckBox\" style='" + style + "' ><input disabled checked  type='checkbox' value='false' class='formator-checkbox' hideFocus /></div>";
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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