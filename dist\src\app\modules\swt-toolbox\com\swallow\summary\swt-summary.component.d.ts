import { HDividedBox } from './../controls/swt-hdividedbox.component';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { SwtCanvas } from './../controls/swt-canvas.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, Renderer2, OnInit, OnDestroy } from "@angular/core";
import { CustomTreeItem } from "../controls/swt-custom-tree.component";
import { SwtCommonGrid } from '../controls/swt-common-grid.component';
import { SwtLabel } from '../controls/swt-label.component';
export declare class SwtSummary extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    private _renderer;
    styleName: string;
    IDField: string;
    expandFirstLoad: boolean;
    gridTitle: SwtLabel;
    treeTitle: SwtLabel;
    lastRanLbl: SwtLabel;
    divBox: HDividedBox;
    tree: CustomTree;
    mainHGroup: VBox;
    treeContainer: VBox;
    customGrid: SwtCanvas;
    summaryGrid: SwtCommonGrid;
    gridContainer: VBox;
    /**
     * JSON Objects
     **/
    private jsonReader;
    private lastRecievedJSON;
    private prevRecievedJSON;
    /**
     * Communication Objects
     **/
    baseURL: string;
    actionMethod: string;
    actionPath: string;
    private facilityAccess;
    private supportAllCurrency;
    private supportAllEntity;
    /**
     * Summary Tree Objects
     **/
    treeOpenedItems: any[];
    treeClosedItems: any[];
    private gridOpenedItems;
    private gridVisibleItems;
    private verticalScrollPosition;
    lastSelectedItem: any;
    currentDivPos: number;
    facilityId: string;
    facilityName: string;
    useGeneric: string;
    scenarioTitle: string;
    selectedscenario: string;
    private resetFilter;
    private swtAlert;
    private logger;
    private columnData;
    constructor(elem: ElementRef, commonService: CommonService, _renderer: Renderer2);
    ngOnDestroy(): void;
    ngOnInit(): void;
    setLabelField(label: string): void;
    hideRoot(hide: boolean): void;
    /**
     * getSortedGridColumn
     * This method is called to get the selected sort in the datagrid
     **/
    getSortedGridColumn(): string;
    /**
     * getIsScenarioAlertable
     * This method is used to found out that selected scenario is alertable or not
     **/
    getIsScenarioAlertable(): string;
    /**
     * getIsBranch
     *
     * This method is used to found out that seletced node is branch or not
     **/
    getIsBranch(): string;
    /**
     * getDividerPosition
     *
     * This method is called to get the divider current position
     **/
    getDividerPosition(): string;
    /**
     * setActionPath
     *
     * @param _actionPath:String
     *
     * This method is called to set actionPath to save controls user prefrerence
     **/
    setActionPath(actionPath: string): void;
    /**
     * setBaseURL
     *
     * This method is called to set baseURL
     **/
    setBaseURL(baseURL: string): void;
    setAccessRules(facilityAcc: boolean, entityAcc: boolean, ccyAcc: boolean): void;
    /**
     * getSelectedItemID
     *
     * This method is called to get the ID of the selected item on the tree
     **/
    getSelectedItemID(): string;
    /**
     * Return the first subNode of the tree
     **/
    setFirstSubNode(): void;
    /**
     * selectTreeItem
     * @param newJSON:
     * This method is called to auto-select the last selected item when updating the dataprovider
     **/
    /**
     * enableTree
     *
     * This method is used to enable selection on tree control
     **/
    enableTree(): void;
    /**
     * disableTree
     *
     * This method is used to disable selection on tree control
     **/
    disableTree(): void;
    /**
     * showTreeDataTips
     * @param:boolean
     * This method is used enable/disable tooltips on tree node
     **/
    showTreeDataTips(show: boolean): void;
    /**
     * dataTipFunction
     * @param: Object
     * This function is used to set tooltip of tree node
     */
    dataTipFunction(item: CustomTreeItem): string;
    /**
     * treeNodeEventHandler
     * @param event:Event
     * This method is called when opening or closing a tree node
     **/
    treeNodeEventHandler(event: any): void;
    /**
     * saveTreeOpenState
     *
     * used to save the tree node states
     */
    saveTreeOpenState(): void;
    /**
     * openTreeItems
     * used to maintain the tree node
     */
    openTreeItems(): void;
    /**
     * treeScrollEventHandler
     * @param event:ScrollEvent
     * This method is called when a scroll event occurs on the tree
     **/
    treeScrollEventHandler(event: any): void;
    /**
     * treeItemDoubleClickHandler
     *
     * @param event:ListEvent
     *
     * This method is used to expand tree when double clicking on a node
     **/
    treeItemDoubleClickHandler(event: any): void;
    isItemOpen(item: any): boolean;
    /**
     * changeTreeData
     *
     * @param: TreeEvent
     *
     * This method is called when the expand/collapse icon is clicked on summaryGrid
     */
    changeTreeData(event: any): void;
    treeKeyDownHandler(event: any): void;
    /**
     * resetGridData
     *
     * This method is used to empty the summary grid
     **/
    resetGridData(): void;
    /**
     * dataProvider
     *
     * @param data:XML
     *
     * This method used to set data for swtSummary controls
     **/
    dataProvider(data: any): void;
    private setScenarioLastRan;
    setClickable(dataContext: any, index: any, dataElement: any): boolean;
    /**
     * showVisibleItems
     * @param dataProvider:XMLList
     * This method is called to show visible rows in the datagrid after filter selection
     **/
    showVisibleItems(dataProvider: any): any;
    saveGridVisibleState(e: Event): void;
    /**
     * saveGridOpenState
     * @param newXML
     * used to save opened grid items
     */
    saveGridOpenState(newXML: any): void;
    deepCopy(mainObj: any): any[];
    /**
     * openGridItems
     *
     * @param dataProvider:XMLList
     *
     * used to maintain opened grid items when refreshing
     */
    private openGridItems;
    setTreeTotals(total: string): void;
}
