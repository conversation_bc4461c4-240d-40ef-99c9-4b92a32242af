/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { SwtUtil } from '../utils/swt-util.service';
import { SwtAlert } from '../utils/swt-alert.service';
import { CommonService } from '../utils/common.service';
import { moment } from 'ngx-bootstrap/chronos/test/chain';
import { DateUtils } from '../utils/date-utils.service';
var SwtAdvSlider = /** @class */ (function (_super) {
    tslib_1.__extends(SwtAdvSlider, _super);
    function SwtAdvSlider(elem, commonService) {
        var _this_1 = _super.call(this, elem, commonService) || this;
        _this_1.elem = elem;
        _this_1.commonService = commonService;
        _this_1.ZOOM_EVENT_ZOOMED = new EventEmitter();
        _this_1.localId = '';
        _this_1._timeData = [];
        _this_1.valueFrom = "";
        _this_1.valueTo = "";
        _this_1._maximum = 1020;
        _this_1._minimum = 480;
        _this_1._values = [];
        _this_1.RESTRICT_CHARS = "0-9:";
        _this_1._calculateFunction = _this_1.defaultReturnFunction.bind(_this_1);
        _this_1._callbackFunction = _this_1.defaultFunction.bind(_this_1);
        _this_1._onstatechangeFunction = _this_1.defaultFunction.bind(_this_1);
        _this_1.options = {
            from: 0, to: 100,
            skin: "round",
            step: 1,
            dimension: '',
            scale: [],
            limits: false,
            calculate: (/**
             * @param {?} value
             * @return {?}
             */
            function (value) {
                _this_1._calculateFunction(value);
            }),
            onstatechange: (/**
             * @param {?} value
             * @return {?}
             */
            function (value) {
                _this_1._onstatechangeFunction(value);
                _this_1.calculateTime(value);
                if (_this_1.tooltipOnLeftTrigger)
                    _this_1.toolTipSlider.text(_this_1.leftTriggerTooltipValue);
                else
                    _this_1.toolTipSlider.text(_this_1.rightTriggerTooltipValue);
            }),
            callback: (/**
             * @param {?} value
             * @param {?} e
             * @return {?}
             */
            function (value, e) {
                _this_1._calculateFunction(value);
                if (_this_1.startDate && _this_1.endDate) {
                    console.log("🚀 ~ SwtAdvSlider ~ this.endDate:", _this_1.endDate);
                    console.log("🚀 ~ SwtAdvSlider ~ this.startDate:", _this_1.startDate);
                    /** @type {?} */
                    var tmpTable = String(value).split(';');
                    for (var index = 0; index < tmpTable.length; index++) {
                        /** @type {?} */
                        var element = Number(tmpTable[index]);
                        /** @type {?} */
                        var timeNow = new Date(element * 60 * 1000);
                        /** @type {?} */
                        var hours = timeNow.getHours();
                        /** @type {?} */
                        var minutes = timeNow.getMinutes();
                        /** @type {?} */
                        var timeString = '' + (hours < 10 ? "0" : "") + hours;
                        timeString += ((minutes < 10) ? ":0" : ":") + minutes;
                        if (index == 0) {
                            _this_1.sliderFromInput.nativeElement.value = timeString;
                        }
                        else {
                            _this_1.sliderToInput.nativeElement.value = timeString;
                        }
                    }
                    /** @type {?} */
                    var fromIsFormat = DateUtils.dateToIso(new Date(Number(tmpTable[0]) * 1000 * 60), false);
                    /** @type {?} */
                    var toIsoFormat = DateUtils.dateToIso(new Date(Number(tmpTable[1]) * 1000 * 60), false);
                    if (_this_1.timeData)
                        _this_1._values = [_this_1.timeData.indexOf(fromIsFormat), _this_1.timeData.indexOf(toIsoFormat)];
                    if (e) {
                        /** @type {?} */
                        var event_1 = { from: fromIsFormat, to: toIsoFormat };
                        _this_1.ZOOM_EVENT_ZOOMED.emit(event_1);
                    }
                }
            }),
        };
        // public scale = ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];
        _this_1.scale = null;
        _this_1.step = 1;
        _this_1.tooltipOnLeftTrigger = true;
        _this_1.leftTriggerTooltipValue = '';
        _this_1.rightTriggerTooltipValue = '';
        // initialize setter.
        _this_1.swtalert = new SwtAlert(commonService);
        return _this_1;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    SwtAdvSlider.prototype.defaultFunction = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtAdvSlider.prototype.defaultReturnFunction = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return value;
    };
    Object.defineProperty(SwtAdvSlider.prototype, "timeData", {
        get: /**
         * @return {?}
         */
        function () {
            return this._timeData;
        },
        set: /**
         * @param {?} values
         * @return {?}
         */
        function (values) {
            if (values) {
                this._timeData = values;
                /** @type {?} */
                var from = this.timeData[0];
                /** @type {?} */
                var to = this.timeData[this.timeData.length - 1];
                /** @type {?} */
                var min_val = Date.parse(from) / (1000 * 60);
                /** @type {?} */
                var max_val = Date.parse(to) / (1000 * 60);
                this.options.from = min_val;
                this.options.to = max_val;
                jQuery(this.slider.nativeElement).slider().redrawAll(this.options);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtAdvSlider.prototype, "values", {
        get: /**
         * @return {?}
         */
        function () {
            return this._values;
        },
        set: /**
         * @param {?} values
         * @return {?}
         */
        function (values) {
            this._values = values;
            if (jQuery(this.slider.nativeElement).slider() && values && values.length == 2 && this.timeData) {
                /** @type {?} */
                var from = this.timeData[values[0]];
                /** @type {?} */
                var to = this.timeData[values[1]];
                /** @type {?} */
                var valueFrom = Date.parse(from) / (1000 * 60);
                /** @type {?} */
                var valueTo = Date.parse(to) / (1000 * 60);
                if (valueTo < valueFrom) {
                    valueTo = valueFrom;
                }
                else if (valueFrom > valueTo) {
                    valueFrom = valueTo;
                }
                if (from)
                    this.sliderFromInput.nativeElement.value = from.split(" ")[1];
                if (to)
                    this.sliderToInput.nativeElement.value = to.split(" ")[1];
                jQuery(this.slider.nativeElement).slider("value", valueFrom, valueTo);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
         * Get the start and end value for the first load
         **/
    /**
     * Get the start and end value for the first load
     *
     * @return {?}
     */
    SwtAdvSlider.prototype.sliderComp = /**
     * Get the start and end value for the first load
     *
     * @return {?}
     */
    function () {
        if (!this.startDate || !this.endDate)
            this.initTextInputs();
        if (this.timeData != null) {
            this.values = [0, this.timeData.length - 1];
        }
        // this.refreshSliderValues()
        // startValue=slider.values[0];
        // endValue=slider.values[1];
    };
    Object.defineProperty(SwtAdvSlider.prototype, "calculateFunction", {
        /**
         *
         * Change the cell content based on function .
         **/
        get: /**
         *
         * Change the cell content based on function .
         *
         * @return {?}
         */
        function () {
            return this._calculateFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._calculateFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtAdvSlider.prototype, "callbackFunction", {
        /**
         *
         * Change the cell content based on function .
         **/
        get: /**
         *
         * Change the cell content based on function .
         *
         * @return {?}
         */
        function () {
            return this._callbackFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._callbackFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtAdvSlider.prototype, "onstatechangeFunction", {
        /**
         *
         * Change the cell content based on function .
         **/
        get: /**
         *
         * Change the cell content based on function .
         *
         * @return {?}
         */
        function () {
            return this._onstatechangeFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._onstatechangeFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtAdvSlider.prototype, "maximum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maximum;
        },
        //---maximum-----------------------------------------------------------------------------------------------------
        set: 
        //---maximum-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._maximum = Number(value);
            }
            catch (error) {
                console.error('method [ set maximum] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
 * Init Slider labels after completing the creation of the slider
 **/
    /**
     * Init Slider labels after completing the creation of the slider
     *
     * @return {?}
     */
    SwtAdvSlider.prototype.initTextInputs = /**
     * Init Slider labels after completing the creation of the slider
     *
     * @return {?}
     */
    function () {
        if (this.timeData && this.timeData.length > 1) {
            this.valueDate = this.timeData[0].split(" ")[0];
            this.startDate = this.timeData[0].split(" ")[0];
            this.endDate = this.timeData[this.timeData.length - 1].split(" ")[0];
            this.sliderFromInput.nativeElement.value = this.timeData[0].split(" ")[1];
            this.sliderToInput.nativeElement.value = this.timeData[this.timeData.length - 1].split(" ")[1];
        }
        // this.fromValue.text=this.timeData[0].split(" ")[1];
        // this.toValue.text=this.timeData[this.timeData.length - 1].split(" ")[1];
    };
    Object.defineProperty(SwtAdvSlider.prototype, "minimum", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minimum;
        },
        //---minimum-----------------------------------------------------------------------------------------------------
        set: 
        //---minimum-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._minimum = Number(value);
            }
            catch (error) {
                console.error('method [ set minimum] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtAdvSlider.prototype.generateDynamicId = /**
     * @return {?}
     */
    function () {
        if (!this.localId) {
            this.localId = 'dyn' + Math.floor((1 + Math.random()) * 0x100000)
                .toString(16)
                .substring(1);
        }
        return this.localId;
    };
    /**
     * @return {?}
     */
    SwtAdvSlider.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var _this = this;
        $('#' + this.localId).on('mouseover', '.jslider-pointer', (/**
         * @return {?}
         */
        function () {
            if ($(this).find('.sliderTooltip').length == 0) {
                $(this).append(_this.toolTipSlider);
            }
            if ($(this).hasClass('jslider-pointer-to')) {
                _this.tooltipOnLeftTrigger = false;
                _this.toolTipSlider.text(_this.rightTriggerTooltipValue);
            }
            else {
                _this.tooltipOnLeftTrigger = true;
                _this.toolTipSlider.text(_this.leftTriggerTooltipValue);
            }
            _this.toolTipSlider.show();
        }));
        $('#' + this.localId).on('mouseout', '.jslider-pointer', (/**
         * @return {?}
         */
        function () {
            _this.toolTipSlider.hide();
        }));
    };
    /**
     * @return {?}
     */
    SwtAdvSlider.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this_1 = this;
        _super.prototype.ngOnInit.call(this);
        /** @type {?} */
        var dt_from = "1970/10/01 00:00";
        /** @type {?} */
        var dt_to = "2050/10/02 00:00";
        // const nowDate = new Date();
        /** @type {?} */
        var min_val = Date.parse(dt_from) / (1000 * 60);
        /** @type {?} */
        var max_val = Date.parse(dt_to) / (1000 * 60);
        this.options.from = min_val;
        this.options.to = max_val;
        this.toolTipSlider = $('<div class="ui-tooltip ui-tooltip-content sliderTooltip" />').css({
            position: 'absolute',
            top: 30,
            left: 10
        }).hide();
        jQuery(this.slider.nativeElement).slider(this.options);
        //  let newOption =  {
        //     from: 120, to: 1400,
        //     skin: "round",
        //     step: this.step,
        //     dimension: '',
        //     scale : ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'],
        //     limits : true,
        //     calculate:   value  => {
        //        this._calculateFunction(value);
        //      },
        //     onstatechange: value  => {
        //        this._onstatechangeFunction(value);
        //      },
        //     callback:  value  => {
        //        this._calculateFunction(value);
        //        this.ZOOM_EVENT_ZOOMED.emit( value );
        //        let tmpTable = String(value).split(';');
        //        for (let index = 0; index < tmpTable.length; index++) {
        //            const element = Number(tmpTable[index]);
        //            const hours = Math.floor(element / 60);
        //            const mins = (element - hours * 60);
        //            const time = (hours < 10 ? "0" + hours : hours) + ":" + (mins == 0 ? "00" : mins);
        //            if(index == 0){
        //                this.sliderFromInput.nativeElement.value = time;
        //            }else {
        //                this.sliderToInput.nativeElement.value = time;
        //            }
        //        }
        //      },
        // }; 
        //jQuery(this.slider.nativeElement).slider().redrawAll(newOption);
        //Allows numbers
        this.restrict = this.RESTRICT_CHARS;
        //Add listener
        //   this.addEventListener("input", this.onValueChange.bind(this), false, 0, true);
        //add focus out event listener to hours input.
        $(this.sliderFromInput.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this_1.validateTime(_this_1.sliderFromInput.nativeElement);
            _this_1.refreshSliderValues();
            //  $(".selector").slider("value", p1, p2)
        }));
        $(this.sliderToInput.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this_1.validateTime(_this_1.sliderToInput.nativeElement);
            _this_1.refreshSliderValues();
        }));
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtAdvSlider.prototype.calculateTime = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        /** @type {?} */
        var tmpTable = String(value).split(';');
        for (var index = 0; index < tmpTable.length; index++) {
            /** @type {?} */
            var element = Number(tmpTable[index]);
            /** @type {?} */
            var hours = Math.floor(element / 60);
            /** @type {?} */
            var mins = (element - hours * 60);
            /** @type {?} */
            var time = (hours < 10 ? "0" + hours : hours) + ":" + (mins == 0 ? "00" : mins);
            if (index == 0) {
                this.leftTriggerTooltipValue = time;
            }
            else {
                this.rightTriggerTooltipValue = time;
            }
        }
        for (var index = 0; index < tmpTable.length; index++) {
            if (index == 0) {
                this.leftTriggerTooltipValue = DateUtils.dateToIso(new Date(Number(tmpTable[index]) * 1000 * 60), false);
            }
            else {
                this.rightTriggerTooltipValue = DateUtils.dateToIso(new Date(Number(tmpTable[index]) * 1000 * 60), false);
            }
        }
    };
    /**
     * @return {?}
     */
    SwtAdvSlider.prototype.refreshSliderValues = /**
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var time = this.sliderFromInput.nativeElement.value;
            /** @type {?} */
            var timeTo = this.sliderToInput.nativeElement.value;
            //Not needed anymore protection was added to set enddate  = start date if the date does not exist in the timeData 
            //as it will be calculated need to check if the start day need protection or not.
            // this.startDate = this.timeData[this.values[0]].split(" ")[0];
            // this.endDate = this.timeData[this.values[1]].split(" ")[0];
            /** @type {?} */
            var dt_from = this.startDate + ' ' + time;
            /** @type {?} */
            var dt_to = this.endDate + ' ' + timeTo;
            if (this.timeData.indexOf(dt_to) == -1) {
                dt_to = this.startDate + ' ' + timeTo;
            }
            /** @type {?} */
            var valueTo = Date.parse(dt_to) / (1000 * 60);
            /** @type {?} */
            var valueFrom = Date.parse(dt_from) / (1000 * 60);
            /** @type {?} */
            var fromIsFormat = void 0;
            /** @type {?} */
            var toIsoFormat = void 0;
            if (valueTo < valueFrom) {
                valueTo = valueFrom;
            }
            else if (valueFrom > valueTo) {
                valueFrom = valueTo;
            }
            fromIsFormat = DateUtils.dateToIso(new Date(valueFrom * 1000 * 60), false);
            this.sliderFromInput.nativeElement.value = fromIsFormat.split(" ")[1];
            toIsoFormat = DateUtils.dateToIso(new Date(valueTo * 1000 * 60), false);
            this.sliderToInput.nativeElement.value = toIsoFormat.split(" ")[1];
            jQuery(this.slider.nativeElement).slider("value", valueFrom, valueTo);
            this._values = [this.timeData.indexOf(fromIsFormat), this.timeData.indexOf(toIsoFormat)];
            /** @type {?} */
            var event_2 = { from: fromIsFormat, to: toIsoFormat };
            this.ZOOM_EVENT_ZOOMED.emit(event_2);
        }
        catch (e) {
            console.log(e);
        }
    };
    /**
     * @param {?} textInput
     * @return {?}
     */
    SwtAdvSlider.prototype.validateTime = /**
     * @param {?} textInput
     * @return {?}
     */
    function (textInput) {
        /** @type {?} */
        var validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
        /** @type {?} */
        var hour;
        /** @type {?} */
        var mins;
        /** @type {?} */
        var tmpValue = textInput.value;
        /** @type {?} */
        var pos = tmpValue.indexOf(":");
        if (pos < 0) {
            if (tmpValue.length == 1) {
                tmpValue = '0' + tmpValue + ':00';
            }
            else if (tmpValue.length == 2) {
                tmpValue = tmpValue + ':00';
            }
            else if (tmpValue.length == 3) {
                tmpValue = tmpValue.substring(0, 2) + ':' + tmpValue[2] + '0';
            }
            else if (tmpValue.length == 4) {
                tmpValue = tmpValue.substring(0, 2) + ':' + tmpValue.substring(2, 4);
            }
            else {
                tmpValue = tmpValue.substring(0, 5);
                tmpValue = tmpValue.substring(0, 2) + ':' + tmpValue.substring(2, 4);
            }
            hour = parseFloat(tmpValue.substring(0, 2));
            mins = parseFloat(tmpValue.substring(3, 5));
            hour = (hour > 24 ? 23 : (hour < 10 ? (hour == 0 ? '00' : '0'
                + hour) : hour));
            mins = (mins >= 60 ? 59 : (mins < 10 ? (mins >= 6 ? 59 : mins
                + '0') : mins));
        }
        else {
            if (tmpValue.charAt(0) == '0') {
                hour = '0'
                    + (parseFloat(tmpValue.substring(1, 2)) ? parseFloat(tmpValue
                        .substring(1, 2)) : '0');
            }
            else {
                hour = (parseFloat(tmpValue.substring(0, 2)) ? parseFloat(tmpValue
                    .substring(0, 2)) : '00');
                hour = (parseFloat(hour) > 24 ? 23 : (parseFloat(hour) < 10 ? (hour == 0 ? '00' : '0'
                    + hour) : hour));
            }
            if (pos != -1) {
                if (tmpValue.charAt(pos + 1) == '0') {
                    mins = '0'
                        + (parseFloat(tmpValue.substring(pos + 2, pos + 3))
                            ? parseFloat(tmpValue.substring(pos + 2, pos + 3))
                            : '0');
                }
                else {
                    mins = (parseFloat(tmpValue.substring(pos + 1, 5))
                        ? parseFloat(tmpValue.substring(pos + 1, 5))
                        : '00');
                    if (mins != '00') {
                        mins = (parseFloat(mins) >= 60 ? 59 : (parseFloat(mins) < 10 ? (parseFloat(mins) >= 6 ? 59 : mins
                            + '0') : mins));
                    }
                }
            }
        }
        /** @type {?} */
        var result = hour + ":" + mins;
        /** @type {?} */
        var valid = moment(result, 'HH:mm', true).isValid();
        if (valid && !valid) {
            this.swtalert.warning(validTimeMessage, null);
            //textInput.text = "";
            return false;
        }
        else {
            textInput.value = result;
        }
    };
    SwtAdvSlider.decorators = [
        { type: Component, args: [{
                    selector: 'SwtAdvSlider',
                    template: "\n    <HBox  [id]=\"generateDynamicId()\" width=\"220\" height=\"100%\"> \n        <input class=\"sliderInput\" #hboxContainer  #sliderFromInput  id=\"sliderFromInput\"  [disabled]=\"false\">\n        <input #slider type=\"slider\"  name=\"area\" value=\"0;100\" />\n        <input class=\"sliderInput\" #sliderToInput id=\"sliderToInput\"  [disabled]=\"false\">\n        </HBox>\n  ",
                    styles: ["\n        .sliderInput{\n            width:40px;\n            height:20px;\n            font-size : 11px !important;\n        }\n      \n      "]
                }] }
    ];
    /** @nocollapse */
    SwtAdvSlider.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtAdvSlider.propDecorators = {
        sliderFromInput: [{ type: ViewChild, args: ['sliderFromInput',] }],
        sliderToInput: [{ type: ViewChild, args: ['sliderToInput',] }],
        ZOOM_EVENT_ZOOMED: [{ type: Output, args: ['ZOOM_EVENT_ZOOMED',] }],
        hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
        slider: [{ type: ViewChild, args: ['slider',] }],
        maximum: [{ type: Input, args: ['maximum',] }],
        minimum: [{ type: Input, args: ['minimum',] }]
    };
    return SwtAdvSlider;
}(Container));
export { SwtAdvSlider };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtAdvSlider.prototype.sliderFromInput;
    /**
     * @type {?}
     * @protected
     */
    SwtAdvSlider.prototype.sliderToInput;
    /** @type {?} */
    SwtAdvSlider.prototype.ZOOM_EVENT_ZOOMED;
    /**
     * @type {?}
     * @protected
     */
    SwtAdvSlider.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    SwtAdvSlider.prototype.slider;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.localId;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._timeData;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.swtalert;
    /** @type {?} */
    SwtAdvSlider.prototype.valueFrom;
    /** @type {?} */
    SwtAdvSlider.prototype.valueTo;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._maximum;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._minimum;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._values;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.RESTRICT_CHARS;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._calculateFunction;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._callbackFunction;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype._onstatechangeFunction;
    /** @type {?} */
    SwtAdvSlider.prototype.startDate;
    /** @type {?} */
    SwtAdvSlider.prototype.endDate;
    /** @type {?} */
    SwtAdvSlider.prototype.valueDate;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.options;
    /** @type {?} */
    SwtAdvSlider.prototype.scale;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.step;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.tooltipOnLeftTrigger;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.toolTipSlider;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.leftTriggerTooltipValue;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.rightTriggerTooltipValue;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtAdvSlider.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWFkdmFuY2VkLXNsaWRlci5jb21wb25lbnQuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvY29udHJvbHMvc3d0LWFkdmFuY2VkLXNsaWRlci5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxPQUFPLEVBQWlCLFNBQVMsRUFBRSxVQUFVLEVBQUUsWUFBWSxFQUFVLEtBQUssRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBR3JILE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx1Q0FBdUMsQ0FBQztBQUVsRSxPQUFPLEVBQUUsT0FBTyxFQUFFLE1BQU0sMkJBQTJCLENBQUM7QUFDcEQsT0FBTyxFQUFFLFFBQVEsRUFBRSxNQUFNLDRCQUE0QixDQUFDO0FBQ3RELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSx5QkFBeUIsQ0FBQztBQUN4RCxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sa0NBQWtDLENBQUM7QUFFeEQsT0FBTyxFQUFFLFNBQVMsRUFBRSxNQUFNLDZCQUE2QixDQUFDO0FBTXhEO0lBa0JrQyx3Q0FBUztJQVF2QyxzQkFBb0IsSUFBZ0IsRUFBVSxhQUE0QjtRQUExRSxjQUNJLGtCQUFNLElBQUksRUFBRSxhQUFhLENBQUMsU0FHN0I7UUFKbUIsWUFBSSxHQUFKLElBQUksQ0FBWTtRQUFVLHFCQUFhLEdBQWIsYUFBYSxDQUFlO1FBTDdDLHlCQUFpQixHQUFzQixJQUFJLFlBQVksRUFBTyxDQUFDO1FBVXBGLGVBQU8sR0FBRyxFQUFFLENBQUM7UUFDYixpQkFBUyxHQUFHLEVBQUUsQ0FBQztRQUdoQixpQkFBUyxHQUFHLEVBQUUsQ0FBQztRQUNmLGVBQU8sR0FBRyxFQUFFLENBQUM7UUFDWixnQkFBUSxHQUFHLElBQUksQ0FBQztRQUNoQixnQkFBUSxHQUFHLEdBQUcsQ0FBQztRQUNmLGVBQU8sR0FBRyxFQUFFLENBQUM7UUFFYixzQkFBYyxHQUFXLE1BQU0sQ0FBQztRQUNoQywwQkFBa0IsR0FBYSxPQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLE9BQUksQ0FBQyxDQUFDO1FBQ3JFLHlCQUFpQixHQUFhLE9BQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLE9BQUksQ0FBQyxDQUFDO1FBQzlELDhCQUFzQixHQUFhLE9BQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLE9BQUksQ0FBQyxDQUFDO1FBT25FLGVBQU8sR0FBRztZQUNkLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUc7WUFDaEIsSUFBSSxFQUFFLE9BQU87WUFDYixJQUFJLEVBQUUsQ0FBQztZQUNQLFNBQVMsRUFBRSxFQUFFO1lBQ2IsS0FBSyxFQUFFLEVBQUU7WUFDVCxNQUFNLEVBQUUsS0FBSztZQUNiLFNBQVM7Ozs7WUFBRSxVQUFBLEtBQUs7Z0JBQ1osT0FBSSxDQUFDLGtCQUFrQixDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ25DLENBQUMsQ0FBQTtZQUNELGFBQWE7Ozs7WUFBRSxVQUFBLEtBQUs7Z0JBQ2hCLE9BQUksQ0FBQyxzQkFBc0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDbkMsT0FBSSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDMUIsSUFBSSxPQUFJLENBQUMsb0JBQW9CO29CQUN6QixPQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxPQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQTs7b0JBRXJELE9BQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLE9BQUksQ0FBQyx3QkFBd0IsQ0FBQyxDQUFBO1lBRTlELENBQUMsQ0FBQTtZQUNELFFBQVE7Ozs7O1lBQUUsVUFBQyxLQUFLLEVBQUMsQ0FBQztnQkFDZCxPQUFJLENBQUMsa0JBQWtCLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQy9CLElBQUksT0FBSSxDQUFDLFNBQVMsSUFBSSxPQUFJLENBQUMsT0FBTyxFQUFFO29CQUNwQyxPQUFPLENBQUMsR0FBRyxDQUFDLG1DQUFtQyxFQUFFLE9BQUksQ0FBQyxPQUFPLENBQUMsQ0FBQTtvQkFDOUQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxxQ0FBcUMsRUFBRSxPQUFJLENBQUMsU0FBUyxDQUFDLENBQUE7O3dCQUcxRCxRQUFRLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7b0JBQ3ZDLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxRQUFRLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFOzs0QkFDNUMsT0FBTyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUM7OzRCQUNqQyxPQUFPLEdBQUcsSUFBSSxJQUFJLENBQUMsT0FBTyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUM7OzRCQUN6QyxLQUFLLEdBQUcsT0FBTyxDQUFDLFFBQVEsRUFBRTs7NEJBQzFCLE9BQU8sR0FBRyxPQUFPLENBQUMsVUFBVSxFQUFFOzs0QkFDOUIsVUFBVSxHQUFHLEVBQUUsR0FBRyxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEdBQUcsS0FBSzt3QkFDckQsVUFBVSxJQUFJLENBQUMsQ0FBQyxPQUFPLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsT0FBTyxDQUFDO3dCQUV0RCxJQUFJLEtBQUssSUFBSSxDQUFDLEVBQUU7NEJBQ1osT0FBSSxDQUFDLGVBQWUsQ0FBQyxhQUFhLENBQUMsS0FBSyxHQUFHLFVBQVUsQ0FBQzt5QkFDekQ7NkJBQU07NEJBQ0gsT0FBSSxDQUFDLGFBQWEsQ0FBQyxhQUFhLENBQUMsS0FBSyxHQUFHLFVBQVUsQ0FBQzt5QkFDdkQ7cUJBQ0o7O3dCQUdLLFlBQVksR0FBRyxTQUFTLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDLEVBQUUsS0FBSyxDQUFDOzt3QkFDcEYsV0FBVyxHQUFHLFNBQVMsQ0FBQyxTQUFTLENBQUMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksR0FBRyxFQUFFLENBQUMsRUFBRSxLQUFLLENBQUM7b0JBQ3pGLElBQUcsT0FBSSxDQUFDLFFBQVE7d0JBQ2hCLE9BQUksQ0FBQyxPQUFPLEdBQUcsQ0FBQyxPQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxZQUFZLENBQUMsRUFBQyxPQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBRSxDQUFBO29CQUN4RixJQUFHLENBQUMsRUFBQzs7NEJBQ0csT0FBSyxHQUFHLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxFQUFFLEVBQUUsV0FBVyxFQUFFO3dCQUNuRCxPQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE9BQUssQ0FBQyxDQUFDO3FCQUN0QztpQkFDSjtZQUNMLENBQUMsQ0FBQTtTQUNKLENBQUE7O1FBMEhNLGFBQUssR0FBRyxJQUFJLENBQUM7UUFDWixZQUFJLEdBQUcsQ0FBQyxDQUFDO1FBMERULDRCQUFvQixHQUFHLElBQUksQ0FBQztRQTBCNUIsK0JBQXVCLEdBQUcsRUFBRSxDQUFDO1FBQzdCLGdDQUF3QixHQUFHLEVBQUUsQ0FBQztRQTVSbEMscUJBQXFCO1FBQ3JCLE9BQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxRQUFRLENBQUMsYUFBYSxDQUFDLENBQUM7O0lBQ2hELENBQUM7Ozs7O0lBK0VNLHNDQUFlOzs7O0lBQXRCLFVBQXVCLEtBQVU7SUFFakMsQ0FBQzs7Ozs7SUFDTSw0Q0FBcUI7Ozs7SUFBNUIsVUFBNkIsS0FBVTtRQUNuQyxPQUFPLEtBQUssQ0FBQztJQUNqQixDQUFDO0lBRUQsc0JBQVcsa0NBQVE7Ozs7UUFBbkI7WUFDSSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7UUFDMUIsQ0FBQzs7Ozs7UUFFRCxVQUFvQixNQUFNO1lBQ3RCLElBQUksTUFBTSxFQUFFO2dCQUNSLElBQUksQ0FBQyxTQUFTLEdBQUcsTUFBTSxDQUFDOztvQkFHbEIsSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDOztvQkFDdkIsRUFBRSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDOztvQkFDOUMsT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDOztvQkFDeEMsT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDO2dCQUUxQyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksR0FBRyxPQUFPLENBQUM7Z0JBQzVCLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQztnQkFDMUIsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQzthQUN0RTtRQUNMLENBQUM7OztPQWhCQTtJQW1CRCxzQkFBVyxnQ0FBTTs7OztRQUFqQjtZQUNJLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQztRQUN4QixDQUFDOzs7OztRQUNELFVBQWtCLE1BQU07WUFFcEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxNQUFNLENBQUM7WUFDdEIsSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxNQUFNLElBQUksTUFBTSxDQUFDLE1BQU0sSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLFFBQVEsRUFBRTs7b0JBQ3ZGLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQzs7b0JBQy9CLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQzs7b0JBQy9CLFNBQVMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsQ0FBQzs7b0JBQzFDLE9BQU8sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsQ0FBQztnQkFFMUMsSUFBSSxPQUFPLEdBQUcsU0FBUyxFQUFFO29CQUNyQixPQUFPLEdBQUcsU0FBUyxDQUFDO2lCQUV2QjtxQkFBTSxJQUFJLFNBQVMsR0FBRyxPQUFPLEVBQUU7b0JBQzVCLFNBQVMsR0FBRyxPQUFPLENBQUM7aUJBQ3ZCO2dCQUNELElBQUcsSUFBSTtvQkFDSCxJQUFJLENBQUMsZUFBZSxDQUFDLGFBQWEsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDbEUsSUFBRyxFQUFFO29CQUNELElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUU5RCxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLFNBQVMsRUFBRSxPQUFPLENBQUMsQ0FBQTthQUN4RTtRQUVMLENBQUM7OztPQXhCQTtJQTJCRDs7WUFFUTs7Ozs7O0lBQ0QsaUNBQVU7Ozs7O0lBQWpCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTztZQUNoQyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7UUFFMUIsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksRUFDekI7WUFDSSxJQUFJLENBQUMsTUFBTSxHQUFDLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1NBQzdDO1FBRUQsNkJBQTZCO1FBQzdCLCtCQUErQjtRQUMvQiw2QkFBNkI7SUFDakMsQ0FBQztJQUtELHNCQUFXLDJDQUFpQjtRQUo1Qjs7O1lBR0k7Ozs7Ozs7UUFDSjtZQUNJLE9BQU8sSUFBSSxDQUFDLGtCQUFrQixDQUFDO1FBQ25DLENBQUM7UUFFRDs7V0FFRzs7Ozs7O1FBQ0gsVUFBNkIsS0FBZTtZQUN4QyxJQUFJLENBQUMsa0JBQWtCLEdBQUcsS0FBSyxDQUFDO1FBQ3BDLENBQUM7OztPQVBBO0lBWUQsc0JBQVcsMENBQWdCO1FBSjNCOzs7WUFHSTs7Ozs7OztRQUNKO1lBQ0ksT0FBTyxJQUFJLENBQUMsaUJBQWlCLENBQUM7UUFDbEMsQ0FBQztRQUVEOztXQUVHOzs7Ozs7UUFDSCxVQUE0QixLQUFlO1lBQ3ZDLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxLQUFLLENBQUM7UUFDbkMsQ0FBQzs7O09BUEE7SUFZRCxzQkFBVywrQ0FBcUI7UUFKaEM7OztZQUdJOzs7Ozs7O1FBQ0o7WUFDSSxPQUFPLElBQUksQ0FBQyxzQkFBc0IsQ0FBQztRQUN2QyxDQUFDO1FBRUQ7O1dBRUc7Ozs7OztRQUNILFVBQWlDLEtBQWU7WUFDNUMsSUFBSSxDQUFDLHNCQUFzQixHQUFHLEtBQUssQ0FBQztRQUN4QyxDQUFDOzs7T0FQQTtJQWFELHNCQUNXLGlDQUFPOzs7O1FBU2xCO1lBQ0ksT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDO1FBQ3pCLENBQUM7UUFiRCxpSEFBaUg7Ozs7Ozs7UUFDakgsVUFDbUIsS0FBVTtZQUN6QixJQUFJO2dCQUNBLElBQUksQ0FBQyxRQUFRLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBRWpDO1lBQUMsT0FBTyxLQUFLLEVBQUU7Z0JBQ1osT0FBTyxDQUFDLEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRSxLQUFLLENBQUMsQ0FBQzthQUMxRDtRQUNMLENBQUM7OztPQUFBO0lBT0Q7O0lBRUE7Ozs7OztJQUNPLHFDQUFjOzs7OztJQUFyQjtRQUNJLElBQUcsSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBQyxDQUFDLEVBQUM7WUFFdkMsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNoRCxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hELElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFckUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxhQUFhLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFFLElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztTQUNsRztRQUNELHNEQUFzRDtRQUN0RCwyRUFBMkU7SUFDL0UsQ0FBQztJQUlELHNCQUNXLGlDQUFPOzs7O1FBa0JsQjtZQUNJLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztRQUN6QixDQUFDO1FBdEJELGlIQUFpSDs7Ozs7OztRQUNqSCxVQUNtQixLQUFVO1lBQ3pCLElBQUk7Z0JBQ0EsSUFBSSxDQUFDLFFBQVEsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7YUFFakM7WUFBQyxPQUFPLEtBQUssRUFBRTtnQkFDWixPQUFPLENBQUMsS0FBSyxDQUFDLGdDQUFnQyxFQUFFLEtBQUssQ0FBQyxDQUFDO2FBQzFEO1FBQ0wsQ0FBQzs7O09BQUE7Ozs7SUFDRCx3Q0FBaUI7OztJQUFqQjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2YsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxRQUFRLENBQUM7aUJBQzVELFFBQVEsQ0FBQyxFQUFFLENBQUM7aUJBQ1osU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDO1NBRXJCO1FBRUQsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO0lBQ3hCLENBQUM7Ozs7SUFNRCxzQ0FBZTs7O0lBQWY7O1lBQ1EsS0FBSyxHQUFHLElBQUk7UUFDaEIsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsRUFBRSxDQUFDLFdBQVcsRUFBRSxrQkFBa0I7OztRQUFFO1lBQ3RELElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sSUFBSSxDQUFDLEVBQUU7Z0JBQzVDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxDQUFDO2FBQ3ZDO1lBRUQsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsUUFBUSxDQUFDLG9CQUFvQixDQUFDLEVBQUM7Z0JBQ3ZDLEtBQUssQ0FBQyxvQkFBb0IsR0FBRyxLQUFLLENBQUM7Z0JBQ25DLEtBQUssQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyx3QkFBd0IsQ0FBQyxDQUFBO2FBQzNEO2lCQUNHO2dCQUNBLEtBQUssQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUM7Z0JBQ2xDLEtBQUssQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQyxDQUFBO2FBQzFEO1lBQ0QsS0FBSyxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUUvQixDQUFDLEVBQUMsQ0FBQztRQUNILENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxVQUFVLEVBQUUsa0JBQWtCOzs7UUFBRTtZQUNyRCxLQUFLLENBQUMsYUFBYSxDQUFDLElBQUksRUFBRSxDQUFDO1FBRS9CLENBQUMsRUFBQyxDQUFDO0lBQ1AsQ0FBQzs7OztJQUlELCtCQUFROzs7SUFBUjtRQUFBLG1CQXFFQztRQXBFRyxpQkFBTSxRQUFRLFdBQUUsQ0FBQzs7WUFDYixPQUFPLEdBQUcsa0JBQWtCOztZQUM1QixLQUFLLEdBQUcsa0JBQWtCOzs7WUFFMUIsT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDOztZQUMzQyxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUM7UUFFN0MsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEdBQUcsT0FBTyxDQUFDO1FBQzVCLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxHQUFHLE9BQU8sQ0FBQztRQUUxQixJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQyw2REFBNkQsQ0FBQyxDQUFDLEdBQUcsQ0FBQztZQUN0RixRQUFRLEVBQUUsVUFBVTtZQUNwQixHQUFHLEVBQUUsRUFBRTtZQUNQLElBQUksRUFBRSxFQUFFO1NBQ1gsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO1FBRVYsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxDQUNwQyxJQUFJLENBQUMsT0FBTyxDQUNmLENBQUM7UUFFRixzQkFBc0I7UUFDdEIsMkJBQTJCO1FBQzNCLHFCQUFxQjtRQUNyQix1QkFBdUI7UUFDdkIscUJBQXFCO1FBQ3JCLHdHQUF3RztRQUN4RyxxQkFBcUI7UUFDckIsK0JBQStCO1FBQy9CLHlDQUF5QztRQUN6QyxVQUFVO1FBQ1YsaUNBQWlDO1FBQ2pDLDZDQUE2QztRQUM3QyxVQUFVO1FBQ1YsNkJBQTZCO1FBQzdCLHlDQUF5QztRQUN6QywrQ0FBK0M7UUFDL0Msa0RBQWtEO1FBQ2xELGlFQUFpRTtRQUNqRSxzREFBc0Q7UUFDdEQscURBQXFEO1FBQ3JELGtEQUFrRDtRQUNsRCxnR0FBZ0c7UUFDaEcsNkJBQTZCO1FBQzdCLGtFQUFrRTtRQUNsRSxxQkFBcUI7UUFDckIsZ0VBQWdFO1FBQ2hFLGVBQWU7UUFFZixXQUFXO1FBQ1gsVUFBVTtRQUNWLE1BQU07UUFDTixrRUFBa0U7UUFDbEUsZ0JBQWdCO1FBQ2hCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQztRQUNwQyxjQUFjO1FBQ2QsbUZBQW1GO1FBRW5GLDhDQUE4QztRQUM5QyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxFQUFFLENBQUMsVUFBVTs7OztRQUFFLFVBQUMsS0FBSztZQUN2RCxPQUFJLENBQUMsWUFBWSxDQUFDLE9BQUksQ0FBQyxlQUFlLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDdEQsT0FBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7WUFDM0IsMENBQTBDO1FBQzlDLENBQUMsRUFBQyxDQUFDO1FBQ0gsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLENBQUMsRUFBRSxDQUFDLFVBQVU7Ozs7UUFBRSxVQUFDLEtBQUs7WUFDckQsT0FBSSxDQUFDLFlBQVksQ0FBQyxPQUFJLENBQUMsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ3BELE9BQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO1FBQy9CLENBQUMsRUFBQyxDQUFDO0lBRVAsQ0FBQzs7Ozs7SUFFRCxvQ0FBYTs7OztJQUFiLFVBQWMsS0FBSzs7WUFDWCxRQUFRLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7UUFDdkMsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLFFBQVEsQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7O2dCQUM1QyxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQzs7Z0JBQ2pDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sR0FBRyxFQUFFLENBQUM7O2dCQUNoQyxJQUFJLEdBQUcsQ0FBQyxPQUFPLEdBQUcsS0FBSyxHQUFHLEVBQUUsQ0FBQzs7Z0JBQzdCLElBQUksR0FBRyxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLElBQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1lBQ2pGLElBQUksS0FBSyxJQUFJLENBQUMsRUFBRTtnQkFDWixJQUFJLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDO2FBQ3ZDO2lCQUFNO2dCQUNILElBQUksQ0FBQyx3QkFBd0IsR0FBRyxJQUFJLENBQUM7YUFDeEM7U0FDSjtRQUdELEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxRQUFRLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO1lBQ2xELElBQUksS0FBSyxJQUFJLENBQUMsRUFBRTtnQkFDWixJQUFJLENBQUMsdUJBQXVCLEdBQUcsU0FBUyxDQUFDLFNBQVMsQ0FBQyxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDO2FBQzVHO2lCQUFNO2dCQUNILElBQUksQ0FBQyx3QkFBd0IsR0FBRyxTQUFTLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUM7YUFDN0c7U0FDSjtJQUdMLENBQUM7Ozs7SUFLRCwwQ0FBbUI7OztJQUFuQjtRQUVJLElBQUk7O2dCQUNNLElBQUksR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLGFBQWEsQ0FBQyxLQUFLOztnQkFDL0MsTUFBTSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLEtBQUs7Ozs7OztnQkFPakQsT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLEdBQUcsR0FBRyxHQUFHLElBQUk7O2dCQUNyQyxLQUFLLEdBQUcsSUFBSSxDQUFDLE9BQU8sR0FBRyxHQUFHLEdBQUcsTUFBTTtZQUN2QyxJQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFDO2dCQUNsQyxLQUFLLEdBQUcsSUFBSSxDQUFDLFNBQVMsR0FBRyxHQUFHLEdBQUcsTUFBTSxDQUFDO2FBQ3pDOztnQkFDRyxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUM7O2dCQUN6QyxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUM7O2dCQUM3QyxZQUFZLFNBQUE7O2dCQUNaLFdBQVcsU0FBQTtZQUNmLElBQUksT0FBTyxHQUFHLFNBQVMsRUFBRTtnQkFDckIsT0FBTyxHQUFHLFNBQVMsQ0FBQzthQUV2QjtpQkFBTSxJQUFJLFNBQVMsR0FBRyxPQUFPLEVBQUU7Z0JBQzVCLFNBQVMsR0FBRyxPQUFPLENBQUM7YUFDdkI7WUFFRCxZQUFZLEdBQUcsU0FBUyxDQUFDLFNBQVMsQ0FBQyxJQUFJLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBQzNFLElBQUksQ0FBQyxlQUFlLENBQUMsYUFBYSxDQUFDLEtBQUssR0FBRyxZQUFZLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RFLFdBQVcsR0FBRyxTQUFTLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDeEUsSUFBSSxDQUFDLGFBQWEsQ0FBQyxhQUFhLENBQUMsS0FBSyxHQUFHLFdBQVcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbkUsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxTQUFTLEVBQUUsT0FBTyxDQUFDLENBQUE7WUFFckUsSUFBSSxDQUFDLE9BQU8sR0FBRyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxFQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFFLENBQUE7O2dCQUdwRixPQUFLLEdBQUcsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLEVBQUUsRUFBRSxXQUFXLEVBQUU7WUFDbkQsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxPQUFLLENBQUMsQ0FBQztTQUV0QztRQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQ1IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQztTQUVsQjtJQUNMLENBQUM7Ozs7O0lBRUQsbUNBQVk7Ozs7SUFBWixVQUFhLFNBQVM7O1lBQ2QsZ0JBQWdCLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixFQUFFLElBQUksQ0FBQzs7WUFDckUsSUFBUzs7WUFDVCxJQUFTOztZQUNULFFBQVEsR0FBRyxTQUFTLENBQUMsS0FBSzs7WUFFMUIsR0FBRyxHQUFHLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDO1FBQy9CLElBQUksR0FBRyxHQUFHLENBQUMsRUFBRTtZQUNULElBQUksUUFBUSxDQUFDLE1BQU0sSUFBSSxDQUFDLEVBQUU7Z0JBQ3RCLFFBQVEsR0FBRyxHQUFHLEdBQUcsUUFBUSxHQUFHLEtBQUssQ0FBQzthQUNyQztpQkFBTSxJQUFJLFFBQVEsQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUFFO2dCQUM3QixRQUFRLEdBQUcsUUFBUSxHQUFHLEtBQUssQ0FBQzthQUMvQjtpQkFBTSxJQUFJLFFBQVEsQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUFFO2dCQUM3QixRQUFRLEdBQUcsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLFFBQVEsQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7YUFDakU7aUJBQU0sSUFBSSxRQUFRLENBQUMsTUFBTSxJQUFJLENBQUMsRUFBRTtnQkFDN0IsUUFBUSxHQUFHLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQzthQUN4RTtpQkFBTTtnQkFDSCxRQUFRLEdBQUcsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3BDLFFBQVEsR0FBRyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDeEU7WUFFRCxJQUFJLEdBQUcsVUFBVSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDNUMsSUFBSSxHQUFHLFVBQVUsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRTVDLElBQUksR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRztrQkFDdkQsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7WUFFckIsSUFBSSxHQUFHLENBQUMsSUFBSSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJO2tCQUN2RCxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztTQUV2QjthQUFNO1lBRUgsSUFBSSxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsRUFBRTtnQkFDM0IsSUFBSSxHQUFHLEdBQUc7c0JBQ0osQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLFFBQVE7eUJBQ3hELFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDcEM7aUJBQU07Z0JBQ0gsSUFBSSxHQUFHLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxRQUFRO3FCQUM3RCxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUM5QixJQUFJLEdBQUcsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUc7c0JBQy9FLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO2FBQ3hCO1lBRUQsSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDLEVBQUU7Z0JBQ1gsSUFBSSxRQUFRLENBQUMsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsSUFBSSxHQUFHLEVBQUU7b0JBQ2pDLElBQUksR0FBRyxHQUFHOzBCQUNKLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsR0FBRyxHQUFHLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUM7NEJBQy9DLENBQUMsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQzs0QkFDbEQsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lCQUNsQjtxQkFBTTtvQkFDSCxJQUFJLEdBQUcsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO3dCQUM5QyxDQUFDLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsR0FBRyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQzt3QkFDNUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDO29CQUNaLElBQUksSUFBSSxJQUFJLElBQUksRUFBRTt3QkFDZCxJQUFJLEdBQUcsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUk7OEJBQzNGLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO3FCQUN2QjtpQkFDSjthQUNKO1NBR0o7O1lBQ0csTUFBTSxHQUFHLElBQUksR0FBRyxHQUFHLEdBQUcsSUFBSTs7WUFFeEIsS0FBSyxHQUFHLE1BQU0sQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDLE9BQU8sRUFBRTtRQUVyRCxJQUFJLEtBQUssSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNqQixJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsQ0FBQztZQUM5QyxzQkFBc0I7WUFDdEIsT0FBTyxLQUFLLENBQUM7U0FDaEI7YUFBTTtZQUNILFNBQVMsQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDO1NBQzVCO0lBQ0wsQ0FBQzs7Z0JBbmhCSixTQUFTLFNBQUM7b0JBQ1AsUUFBUSxFQUFFLGNBQWM7b0JBQ3hCLFFBQVEsRUFBRSxpWUFNWDs2QkFDVSxpSkFPTjtpQkFDTjs7OztnQkFqQ2tDLFVBQVU7Z0JBT3BDLGFBQWE7OztrQ0E0QmpCLFNBQVMsU0FBQyxpQkFBaUI7Z0NBQzNCLFNBQVMsU0FBQyxlQUFlO29DQUN6QixNQUFNLFNBQUMsbUJBQW1CO2dDQUMxQixTQUFTLFNBQUMsZUFBZTt5QkFDekIsU0FBUyxTQUFDLFFBQVE7MEJBOE1sQixLQUFLLFNBQUMsU0FBUzswQkFrQ2YsS0FBSyxTQUFDLFNBQVM7O0lBNlNwQixtQkFBQztDQUFBLEFBcGpCRCxDQWtCa0MsU0FBUyxHQWtpQjFDO1NBbGlCWSxZQUFZOzs7Ozs7SUFDckIsdUNBQW9FOzs7OztJQUNwRSxxQ0FBZ0U7O0lBQ2hFLHlDQUE0Rjs7Ozs7SUFDNUYscUNBQWdFOzs7OztJQUNoRSw4QkFBa0Q7Ozs7O0lBUWxELCtCQUFxQjs7Ozs7SUFDckIsaUNBQXVCOzs7OztJQUV2QixnQ0FBMkI7O0lBQzNCLGlDQUFzQjs7SUFDdEIsK0JBQW9COzs7OztJQUNwQixnQ0FBd0I7Ozs7O0lBQ3hCLGdDQUF1Qjs7Ozs7SUFDdkIsK0JBQXFCOzs7OztJQUVyQixzQ0FBd0M7Ozs7O0lBQ3hDLDBDQUE2RTs7Ozs7SUFDN0UseUNBQXNFOzs7OztJQUN0RSw4Q0FBMkU7O0lBRTNFLGlDQUF5Qjs7SUFDekIsK0JBQXVCOztJQUN2QixpQ0FBeUI7Ozs7O0lBR3pCLCtCQXFEQzs7SUEwSEQsNkJBQW9COzs7OztJQUNwQiw0QkFBaUI7Ozs7O0lBMERqQiw0Q0FBb0M7Ozs7O0lBeUJwQyxxQ0FBc0I7Ozs7O0lBQ3RCLCtDQUFxQzs7Ozs7SUFDckMsZ0RBQXNDOzs7OztJQTlSMUIsNEJBQXdCOzs7OztJQUFFLHFDQUFvQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFmdGVyVmlld0luaXQsIENvbXBvbmVudCwgRWxlbWVudFJlZiwgRXZlbnRFbWl0dGVyLCBPbkluaXQsIElucHV0LCBPdXRwdXQsIFZpZXdDaGlsZCB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG5pbXBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tIFwiLi4vbWFuYWdlcnMvZm9jdXMtbWFuYWdlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dFRleHRJbnB1dCB9IGZyb20gXCIuL3N3dC10ZXh0LWlucHV0LmNvbXBvbmVudFwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQgeyBGb3JtQ29udHJvbCwgVmFsaWRhdG9ycyB9IGZyb20gXCJAYW5ndWxhci9mb3Jtc1wiO1xyXG5pbXBvcnQgeyBTd3RVdGlsIH0gZnJvbSAnLi4vdXRpbHMvc3d0LXV0aWwuc2VydmljZSc7XHJcbmltcG9ydCB7IFN3dEFsZXJ0IH0gZnJvbSAnLi4vdXRpbHMvc3d0LWFsZXJ0LnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSAnLi4vdXRpbHMvY29tbW9uLnNlcnZpY2UnO1xyXG5pbXBvcnQge21vbWVudH0gZnJvbSAnbmd4LWJvb3RzdHJhcC9jaHJvbm9zL3Rlc3QvY2hhaW4nO1xyXG5pbXBvcnQgeyBIQm94IH0gZnJvbSAnLi9zd3QtaGJveC5jb21wb25lbnQnO1xyXG5pbXBvcnQgeyBEYXRlVXRpbHMgfSBmcm9tICcuLi91dGlscy9kYXRlLXV0aWxzLnNlcnZpY2UnO1xyXG4vLyBkZWNsYXJlIHZhciByZXF1aXJlOiBhbnk7XHJcbi8vIGNvbnN0ICQgPSByZXF1aXJlKCdqcXVlcnknKTtcclxuLy8gWW91IGFkZCBpdCBoZXJlXHJcbmRlY2xhcmUgdmFyIGpRdWVyeTogYW55O1xyXG5kZWNsYXJlIGZ1bmN0aW9uIHZhbGlkYXRlRm9ybWF0VGltZShzdHJGaWVsZCk6IGFueTtcclxuQENvbXBvbmVudCh7XHJcbiAgICBzZWxlY3RvcjogJ1N3dEFkdlNsaWRlcicsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgPEhCb3ggIFtpZF09XCJnZW5lcmF0ZUR5bmFtaWNJZCgpXCIgd2lkdGg9XCIyMjBcIiBoZWlnaHQ9XCIxMDAlXCI+IFxyXG4gICAgICAgIDxpbnB1dCBjbGFzcz1cInNsaWRlcklucHV0XCIgI2hib3hDb250YWluZXIgICNzbGlkZXJGcm9tSW5wdXQgIGlkPVwic2xpZGVyRnJvbUlucHV0XCIgIFtkaXNhYmxlZF09XCJmYWxzZVwiPlxyXG4gICAgICAgIDxpbnB1dCAjc2xpZGVyIHR5cGU9XCJzbGlkZXJcIiAgbmFtZT1cImFyZWFcIiB2YWx1ZT1cIjA7MTAwXCIgLz5cclxuICAgICAgICA8aW5wdXQgY2xhc3M9XCJzbGlkZXJJbnB1dFwiICNzbGlkZXJUb0lucHV0IGlkPVwic2xpZGVyVG9JbnB1dFwiICBbZGlzYWJsZWRdPVwiZmFsc2VcIj5cclxuICAgICAgICA8L0hCb3g+XHJcbiAgYCxcclxuICAgIHN0eWxlczogW2BcclxuICAgICAgICAuc2xpZGVySW5wdXR7XHJcbiAgICAgICAgICAgIHdpZHRoOjQwcHg7XHJcbiAgICAgICAgICAgIGhlaWdodDoyMHB4O1xyXG4gICAgICAgICAgICBmb250LXNpemUgOiAxMXB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgICBcclxuICAgICAgYF1cclxufSlcclxuZXhwb3J0IGNsYXNzIFN3dEFkdlNsaWRlciBleHRlbmRzIENvbnRhaW5lciBpbXBsZW1lbnRzIE9uSW5pdCwgQWZ0ZXJWaWV3SW5pdCB7XHJcbiAgICBAVmlld0NoaWxkKCdzbGlkZXJGcm9tSW5wdXQnKSBwcm90ZWN0ZWQgc2xpZGVyRnJvbUlucHV0OiBFbGVtZW50UmVmO1xyXG4gICAgQFZpZXdDaGlsZCgnc2xpZGVyVG9JbnB1dCcpIHByb3RlY3RlZCBzbGlkZXJUb0lucHV0OiBFbGVtZW50UmVmO1xyXG4gICAgQE91dHB1dCgnWk9PTV9FVkVOVF9aT09NRUQnKSBaT09NX0VWRU5UX1pPT01FRDogRXZlbnRFbWl0dGVyPGFueT4gPSBuZXcgRXZlbnRFbWl0dGVyPGFueT4oKTtcclxuICAgIEBWaWV3Q2hpbGQoJ2hib3hDb250YWluZXInKSBwcm90ZWN0ZWQgaGJveENvbnRhaW5lcjogRWxlbWVudFJlZjtcclxuICAgIEBWaWV3Q2hpbGQoJ3NsaWRlcicpIHByb3RlY3RlZCBzbGlkZXI6IEVsZW1lbnRSZWY7XHJcblxyXG5cclxuICAgIGNvbnN0cnVjdG9yKHByaXZhdGUgZWxlbTogRWxlbWVudFJlZiwgcHJpdmF0ZSBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlKSB7XHJcbiAgICAgICAgc3VwZXIoZWxlbSwgY29tbW9uU2VydmljZSlcclxuICAgICAgICAvLyBpbml0aWFsaXplIHNldHRlci5cclxuICAgICAgICB0aGlzLnN3dGFsZXJ0ID0gbmV3IFN3dEFsZXJ0KGNvbW1vblNlcnZpY2UpO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSBsb2NhbElkID0gJyc7XHJcbiAgICBwcml2YXRlIF90aW1lRGF0YSA9IFtdO1xyXG5cclxuICAgIHByaXZhdGUgc3d0YWxlcnQ6IFN3dEFsZXJ0O1xyXG4gICAgcHVibGljIHZhbHVlRnJvbSA9IFwiXCI7XHJcbiAgICBwdWJsaWMgdmFsdWVUbyA9IFwiXCI7XHJcbiAgICBwcml2YXRlIF9tYXhpbXVtID0gMTAyMDtcclxuICAgIHByaXZhdGUgX21pbmltdW0gPSA0ODA7XHJcbiAgICBwcml2YXRlIF92YWx1ZXMgPSBbXTtcclxuXHJcbiAgICBwcml2YXRlIFJFU1RSSUNUX0NIQVJTOiBzdHJpbmcgPSBcIjAtOTpcIjtcclxuICAgIHByaXZhdGUgX2NhbGN1bGF0ZUZ1bmN0aW9uOiBGdW5jdGlvbiA9IHRoaXMuZGVmYXVsdFJldHVybkZ1bmN0aW9uLmJpbmQodGhpcyk7XHJcbiAgICBwcml2YXRlIF9jYWxsYmFja0Z1bmN0aW9uOiBGdW5jdGlvbiA9IHRoaXMuZGVmYXVsdEZ1bmN0aW9uLmJpbmQodGhpcyk7XHJcbiAgICBwcml2YXRlIF9vbnN0YXRlY2hhbmdlRnVuY3Rpb246IEZ1bmN0aW9uID0gdGhpcy5kZWZhdWx0RnVuY3Rpb24uYmluZCh0aGlzKTtcclxuXHJcbiAgICBwdWJsaWMgc3RhcnREYXRlOiBzdHJpbmc7XHJcbiAgICBwdWJsaWMgZW5kRGF0ZTogc3RyaW5nO1xyXG4gICAgcHVibGljIHZhbHVlRGF0ZTogc3RyaW5nO1xyXG5cclxuXHJcbiAgICBwcml2YXRlIG9wdGlvbnMgPSB7XHJcbiAgICAgICAgZnJvbTogMCwgdG86IDEwMCxcclxuICAgICAgICBza2luOiBcInJvdW5kXCIsXHJcbiAgICAgICAgc3RlcDogMSxcclxuICAgICAgICBkaW1lbnNpb246ICcnLFxyXG4gICAgICAgIHNjYWxlOiBbXSxcclxuICAgICAgICBsaW1pdHM6IGZhbHNlLFxyXG4gICAgICAgIGNhbGN1bGF0ZTogdmFsdWUgPT4ge1xyXG4gICAgICAgICAgICB0aGlzLl9jYWxjdWxhdGVGdW5jdGlvbih2YWx1ZSk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbnN0YXRlY2hhbmdlOiB2YWx1ZSA9PiB7XHJcbiAgICAgICAgICAgIHRoaXMuX29uc3RhdGVjaGFuZ2VGdW5jdGlvbih2YWx1ZSk7XHJcbiAgICAgICAgICAgIHRoaXMuY2FsY3VsYXRlVGltZSh2YWx1ZSk7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLnRvb2x0aXBPbkxlZnRUcmlnZ2VyKVxyXG4gICAgICAgICAgICAgICAgdGhpcy50b29sVGlwU2xpZGVyLnRleHQodGhpcy5sZWZ0VHJpZ2dlclRvb2x0aXBWYWx1ZSlcclxuICAgICAgICAgICAgZWxzZVxyXG4gICAgICAgICAgICAgICAgdGhpcy50b29sVGlwU2xpZGVyLnRleHQodGhpcy5yaWdodFRyaWdnZXJUb29sdGlwVmFsdWUpXHJcblxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY2FsbGJhY2s6ICh2YWx1ZSxlKSA9PiB7XHJcbiAgICAgICAgICAgIHRoaXMuX2NhbGN1bGF0ZUZ1bmN0aW9uKHZhbHVlKTtcclxuICAgICAgICAgICAgaWYgKHRoaXMuc3RhcnREYXRlICYmIHRoaXMuZW5kRGF0ZSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCfmoAgfiBTd3RBZHZTbGlkZXIgfiB0aGlzLmVuZERhdGU6XCIsIHRoaXMuZW5kRGF0ZSlcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCLwn5qAIH4gU3d0QWR2U2xpZGVyIH4gdGhpcy5zdGFydERhdGU6XCIsIHRoaXMuc3RhcnREYXRlKVxyXG5cclxuXHJcbiAgICAgICAgICAgICAgICBsZXQgdG1wVGFibGUgPSBTdHJpbmcodmFsdWUpLnNwbGl0KCc7Jyk7XHJcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdG1wVGFibGUubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZWxlbWVudCA9IE51bWJlcih0bXBUYWJsZVtpbmRleF0pO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRpbWVOb3cgPSBuZXcgRGF0ZShlbGVtZW50ICogNjAgKiAxMDAwKTtcclxuICAgICAgICAgICAgICAgICAgICB2YXIgaG91cnMgPSB0aW1lTm93LmdldEhvdXJzKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyIG1pbnV0ZXMgPSB0aW1lTm93LmdldE1pbnV0ZXMoKTtcclxuICAgICAgICAgICAgICAgICAgICB2YXIgdGltZVN0cmluZyA9ICcnICsgKGhvdXJzIDwgMTAgPyBcIjBcIiA6IFwiXCIpICsgaG91cnM7XHJcbiAgICAgICAgICAgICAgICAgICAgdGltZVN0cmluZyArPSAoKG1pbnV0ZXMgPCAxMCkgPyBcIjowXCIgOiBcIjpcIikgKyBtaW51dGVzO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoaW5kZXggPT0gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNsaWRlckZyb21JbnB1dC5uYXRpdmVFbGVtZW50LnZhbHVlID0gdGltZVN0cmluZztcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNsaWRlclRvSW5wdXQubmF0aXZlRWxlbWVudC52YWx1ZSA9IHRpbWVTdHJpbmc7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBmcm9tSXNGb3JtYXQgPSBEYXRlVXRpbHMuZGF0ZVRvSXNvKG5ldyBEYXRlKE51bWJlcih0bXBUYWJsZVswXSkgKiAxMDAwICogNjApLCBmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0b0lzb0Zvcm1hdCA9IERhdGVVdGlscy5kYXRlVG9Jc28obmV3IERhdGUoTnVtYmVyKHRtcFRhYmxlWzFdKSAqIDEwMDAgKiA2MCksIGZhbHNlKTtcclxuICAgICAgICAgICAgICAgIGlmKHRoaXMudGltZURhdGEpXHJcbiAgICAgICAgICAgICAgICB0aGlzLl92YWx1ZXMgPSBbdGhpcy50aW1lRGF0YS5pbmRleE9mKGZyb21Jc0Zvcm1hdCksdGhpcy50aW1lRGF0YS5pbmRleE9mKHRvSXNvRm9ybWF0KSBdXHJcbiAgICAgICAgICAgICAgICBpZihlKXtcclxuICAgICAgICAgICAgICAgICAgICBsZXQgZXZlbnQgPSB7IGZyb206IGZyb21Jc0Zvcm1hdCwgdG86IHRvSXNvRm9ybWF0IH07XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5aT09NX0VWRU5UX1pPT01FRC5lbWl0KGV2ZW50KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICB9XHJcblxyXG5cclxuXHJcblxyXG4gICAgcHVibGljIGRlZmF1bHRGdW5jdGlvbih2YWx1ZTogYW55KSB7XHJcblxyXG4gICAgfVxyXG4gICAgcHVibGljIGRlZmF1bHRSZXR1cm5GdW5jdGlvbih2YWx1ZTogYW55KSB7XHJcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXQgdGltZURhdGEoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3RpbWVEYXRhO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzZXQgdGltZURhdGEodmFsdWVzKSB7XHJcbiAgICAgICAgaWYgKHZhbHVlcykge1xyXG4gICAgICAgICAgICB0aGlzLl90aW1lRGF0YSA9IHZhbHVlcztcclxuXHJcblxyXG4gICAgICAgICAgICBjb25zdCBmcm9tID0gdGhpcy50aW1lRGF0YVswXTtcclxuICAgICAgICAgICAgY29uc3QgdG8gPSB0aGlzLnRpbWVEYXRhW3RoaXMudGltZURhdGEubGVuZ3RoIC0gMV07XHJcbiAgICAgICAgICAgIHZhciBtaW5fdmFsID0gRGF0ZS5wYXJzZShmcm9tKSAvICgxMDAwICogNjApO1xyXG4gICAgICAgICAgICB2YXIgbWF4X3ZhbCA9IERhdGUucGFyc2UodG8pIC8gKDEwMDAgKiA2MCk7XHJcblxyXG4gICAgICAgICAgICB0aGlzLm9wdGlvbnMuZnJvbSA9IG1pbl92YWw7XHJcbiAgICAgICAgICAgIHRoaXMub3B0aW9ucy50byA9IG1heF92YWw7XHJcbiAgICAgICAgICAgIGpRdWVyeSh0aGlzLnNsaWRlci5uYXRpdmVFbGVtZW50KS5zbGlkZXIoKS5yZWRyYXdBbGwodGhpcy5vcHRpb25zKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG5cclxuICAgIHB1YmxpYyBnZXQgdmFsdWVzKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl92YWx1ZXM7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgc2V0IHZhbHVlcyh2YWx1ZXMpIHtcclxuXHJcbiAgICAgICAgdGhpcy5fdmFsdWVzID0gdmFsdWVzO1xyXG4gICAgICAgIGlmIChqUXVlcnkodGhpcy5zbGlkZXIubmF0aXZlRWxlbWVudCkuc2xpZGVyKCkgJiYgdmFsdWVzICYmIHZhbHVlcy5sZW5ndGggPT0gMiAmJiB0aGlzLnRpbWVEYXRhKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZyb20gPSB0aGlzLnRpbWVEYXRhW3ZhbHVlc1swXV07XHJcbiAgICAgICAgICAgIGNvbnN0IHRvID0gdGhpcy50aW1lRGF0YVt2YWx1ZXNbMV1dO1xyXG4gICAgICAgICAgICBsZXQgdmFsdWVGcm9tID0gRGF0ZS5wYXJzZShmcm9tKSAvICgxMDAwICogNjApO1xyXG4gICAgICAgICAgICBsZXQgdmFsdWVUbyA9IERhdGUucGFyc2UodG8pIC8gKDEwMDAgKiA2MCk7XHJcblxyXG4gICAgICAgICAgICBpZiAodmFsdWVUbyA8IHZhbHVlRnJvbSkge1xyXG4gICAgICAgICAgICAgICAgdmFsdWVUbyA9IHZhbHVlRnJvbTtcclxuXHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWVGcm9tID4gdmFsdWVUbykge1xyXG4gICAgICAgICAgICAgICAgdmFsdWVGcm9tID0gdmFsdWVUbztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZihmcm9tKVxyXG4gICAgICAgICAgICAgICAgdGhpcy5zbGlkZXJGcm9tSW5wdXQubmF0aXZlRWxlbWVudC52YWx1ZSA9IGZyb20uc3BsaXQoXCIgXCIpWzFdO1xyXG4gICAgICAgICAgICBpZih0bylcclxuICAgICAgICAgICAgICAgIHRoaXMuc2xpZGVyVG9JbnB1dC5uYXRpdmVFbGVtZW50LnZhbHVlID0gdG8uc3BsaXQoXCIgXCIpWzFdO1xyXG5cclxuICAgICAgICAgICAgalF1ZXJ5KHRoaXMuc2xpZGVyLm5hdGl2ZUVsZW1lbnQpLnNsaWRlcihcInZhbHVlXCIsIHZhbHVlRnJvbSwgdmFsdWVUbylcclxuICAgICAgICB9XHJcblxyXG4gICAgfVxyXG5cclxuXHJcbiAgICAvKipcclxuICAgICAgICAgKiBHZXQgdGhlIHN0YXJ0IGFuZCBlbmQgdmFsdWUgZm9yIHRoZSBmaXJzdCBsb2FkXHJcbiAgICAgICAgICoqL1xyXG4gICAgcHVibGljIHNsaWRlckNvbXAoKTogdm9pZCB7XHJcbiAgICAgICAgaWYgKCF0aGlzLnN0YXJ0RGF0ZSB8fCAhdGhpcy5lbmREYXRlKVxyXG4gICAgICAgICAgICB0aGlzLmluaXRUZXh0SW5wdXRzKCk7XHJcblxyXG4gICAgICAgIGlmICh0aGlzLnRpbWVEYXRhICE9IG51bGwpXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB0aGlzLnZhbHVlcz1bMCwgdGhpcy50aW1lRGF0YS5sZW5ndGggLSAxXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgIC8vIHRoaXMucmVmcmVzaFNsaWRlclZhbHVlcygpXHJcbiAgICAgICAgLy8gc3RhcnRWYWx1ZT1zbGlkZXIudmFsdWVzWzBdO1xyXG4gICAgICAgIC8vIGVuZFZhbHVlPXNsaWRlci52YWx1ZXNbMV07XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqXHJcbiAgICAgKiBDaGFuZ2UgdGhlIGNlbGwgY29udGVudCBiYXNlZCBvbiBmdW5jdGlvbiAuXHJcbiAgICAgKiovXHJcbiAgICBwdWJsaWMgZ2V0IGNhbGN1bGF0ZUZ1bmN0aW9uKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9jYWxjdWxhdGVGdW5jdGlvbjtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBzZXQgY2FsY3VsYXRlRnVuY3Rpb24odmFsdWU6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fY2FsY3VsYXRlRnVuY3Rpb24gPSB2YWx1ZTtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICpcclxuICAgICAqIENoYW5nZSB0aGUgY2VsbCBjb250ZW50IGJhc2VkIG9uIGZ1bmN0aW9uIC5cclxuICAgICAqKi9cclxuICAgIHB1YmxpYyBnZXQgY2FsbGJhY2tGdW5jdGlvbigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fY2FsbGJhY2tGdW5jdGlvbjtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqXHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBzZXQgY2FsbGJhY2tGdW5jdGlvbih2YWx1ZTogRnVuY3Rpb24pIHtcclxuICAgICAgICB0aGlzLl9jYWxsYmFja0Z1bmN0aW9uID0gdmFsdWU7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqXHJcbiAgICAgKiBDaGFuZ2UgdGhlIGNlbGwgY29udGVudCBiYXNlZCBvbiBmdW5jdGlvbiAuXHJcbiAgICAgKiovXHJcbiAgICBwdWJsaWMgZ2V0IG9uc3RhdGVjaGFuZ2VGdW5jdGlvbigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fb25zdGF0ZWNoYW5nZUZ1bmN0aW9uO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICpcclxuICAgICAqL1xyXG4gICAgcHVibGljIHNldCBvbnN0YXRlY2hhbmdlRnVuY3Rpb24odmFsdWU6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5fb25zdGF0ZWNoYW5nZUZ1bmN0aW9uID0gdmFsdWU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gcHVibGljIHNjYWxlID0gWyc4OjAwJywgJzk6MDAnLCAnMTA6MDAnLCAnMTE6MDAnLCAnMTI6MDAnLCAnMTM6MDAnLCAnMTQ6MDAnLCAnMTU6MDAnLCAnMTY6MDAnLCAnMTc6MDAnXTtcclxuICAgIHB1YmxpYyBzY2FsZSA9IG51bGw7XHJcbiAgICBwcml2YXRlIHN0ZXAgPSAxO1xyXG4gICAgLy8tLS1tYXhpbXVtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgnbWF4aW11bScpXHJcbiAgICBwdWJsaWMgc2V0IG1heGltdW0odmFsdWU6IGFueSkge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIHRoaXMuX21heGltdW0gPSBOdW1iZXIodmFsdWUpO1xyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtZXRob2QgWyBzZXQgbWF4aW11bV0gLSBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXQgbWF4aW11bSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fbWF4aW11bTtcclxuICAgIH1cclxuXHJcblxyXG4gICAgLyoqXHJcbiAqIEluaXQgU2xpZGVyIGxhYmVscyBhZnRlciBjb21wbGV0aW5nIHRoZSBjcmVhdGlvbiBvZiB0aGUgc2xpZGVyXHJcbiAqKi9cclxuICAgIHB1YmxpYyBpbml0VGV4dElucHV0cygpIHtcclxuICAgICAgICBpZih0aGlzLnRpbWVEYXRhICYmIHRoaXMudGltZURhdGEubGVuZ3RoPjEpe1xyXG5cclxuICAgICAgICAgICAgdGhpcy52YWx1ZURhdGUgPSB0aGlzLnRpbWVEYXRhWzBdLnNwbGl0KFwiIFwiKVswXTtcclxuICAgICAgICAgICAgdGhpcy5zdGFydERhdGUgPSB0aGlzLnRpbWVEYXRhWzBdLnNwbGl0KFwiIFwiKVswXTtcclxuICAgICAgICAgICAgdGhpcy5lbmREYXRlID0gdGhpcy50aW1lRGF0YVt0aGlzLnRpbWVEYXRhLmxlbmd0aCAtIDFdLnNwbGl0KFwiIFwiKVswXTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHRoaXMuc2xpZGVyRnJvbUlucHV0Lm5hdGl2ZUVsZW1lbnQudmFsdWUgPSB0aGlzLnRpbWVEYXRhWzBdLnNwbGl0KFwiIFwiKVsxXTtcclxuICAgICAgICAgICAgdGhpcy5zbGlkZXJUb0lucHV0Lm5hdGl2ZUVsZW1lbnQudmFsdWUgPSB0aGlzLnRpbWVEYXRhW3RoaXMudGltZURhdGEubGVuZ3RoIC0gMV0uc3BsaXQoXCIgXCIpWzFdO1xyXG4gICAgICAgIH1cclxuICAgICAgICAvLyB0aGlzLmZyb21WYWx1ZS50ZXh0PXRoaXMudGltZURhdGFbMF0uc3BsaXQoXCIgXCIpWzFdO1xyXG4gICAgICAgIC8vIHRoaXMudG9WYWx1ZS50ZXh0PXRoaXMudGltZURhdGFbdGhpcy50aW1lRGF0YS5sZW5ndGggLSAxXS5zcGxpdChcIiBcIilbMV07XHJcbiAgICB9XHJcblxyXG5cclxuICAgIC8vLS0tbWluaW11bS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoJ21pbmltdW0nKVxyXG4gICAgcHVibGljIHNldCBtaW5pbXVtKHZhbHVlOiBhbnkpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICB0aGlzLl9taW5pbXVtID0gTnVtYmVyKHZhbHVlKTtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbWV0aG9kIFsgc2V0IG1pbmltdW1dIC0gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdlbmVyYXRlRHluYW1pY0lkKCkge1xyXG4gICAgICAgIGlmICghdGhpcy5sb2NhbElkKSB7XHJcbiAgICAgICAgICAgIHRoaXMubG9jYWxJZCA9ICdkeW4nICsgTWF0aC5mbG9vcigoMSArIE1hdGgucmFuZG9tKCkpICogMHgxMDAwMDApXHJcbiAgICAgICAgICAgICAgICAudG9TdHJpbmcoMTYpXHJcbiAgICAgICAgICAgICAgICAuc3Vic3RyaW5nKDEpO1xyXG5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiB0aGlzLmxvY2FsSWQ7XHJcbiAgICB9XHJcbiAgICBwdWJsaWMgZ2V0IG1pbmltdW0oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX21pbmltdW07XHJcbiAgICB9XHJcbiAgICBwcml2YXRlIHRvb2x0aXBPbkxlZnRUcmlnZ2VyID0gdHJ1ZTtcclxuXHJcbiAgICBuZ0FmdGVyVmlld0luaXQoKTogdm9pZCB7XHJcbiAgICAgICAgbGV0IF90aGlzID0gdGhpcztcclxuICAgICAgICAkKCcjJyArIHRoaXMubG9jYWxJZCkub24oJ21vdXNlb3ZlcicsICcuanNsaWRlci1wb2ludGVyJywgZnVuY3Rpb24gKCkge1xyXG4gICAgICAgICAgICBpZiAoJCh0aGlzKS5maW5kKCcuc2xpZGVyVG9vbHRpcCcpLmxlbmd0aCA9PSAwKSB7XHJcbiAgICAgICAgICAgICAgICAkKHRoaXMpLmFwcGVuZChfdGhpcy50b29sVGlwU2xpZGVyKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgaWYgKCQodGhpcykuaGFzQ2xhc3MoJ2pzbGlkZXItcG9pbnRlci10bycpKXtcclxuICAgICAgICAgICAgICAgIF90aGlzLnRvb2x0aXBPbkxlZnRUcmlnZ2VyID0gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICBfdGhpcy50b29sVGlwU2xpZGVyLnRleHQoX3RoaXMucmlnaHRUcmlnZ2VyVG9vbHRpcFZhbHVlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVsc2V7XHJcbiAgICAgICAgICAgICAgICBfdGhpcy50b29sdGlwT25MZWZ0VHJpZ2dlciA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICBfdGhpcy50b29sVGlwU2xpZGVyLnRleHQoX3RoaXMubGVmdFRyaWdnZXJUb29sdGlwVmFsdWUpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgX3RoaXMudG9vbFRpcFNsaWRlci5zaG93KCk7XHJcblxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgICQoJyMnICsgdGhpcy5sb2NhbElkKS5vbignbW91c2VvdXQnLCAnLmpzbGlkZXItcG9pbnRlcicsIGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgX3RoaXMudG9vbFRpcFNsaWRlci5oaWRlKCk7XHJcblxyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgcHJpdmF0ZSB0b29sVGlwU2xpZGVyO1xyXG4gICAgcHJpdmF0ZSBsZWZ0VHJpZ2dlclRvb2x0aXBWYWx1ZSA9ICcnO1xyXG4gICAgcHJpdmF0ZSByaWdodFRyaWdnZXJUb29sdGlwVmFsdWUgPSAnJztcclxuICAgIG5nT25Jbml0KCkge1xyXG4gICAgICAgIHN1cGVyLm5nT25Jbml0KCk7XHJcbiAgICAgICAgdmFyIGR0X2Zyb20gPSBcIjE5NzAvMTAvMDEgMDA6MDBcIjtcclxuICAgICAgICB2YXIgZHRfdG8gPSBcIjIwNTAvMTAvMDIgMDA6MDBcIjtcclxuICAgICAgICAvLyBjb25zdCBub3dEYXRlID0gbmV3IERhdGUoKTtcclxuICAgICAgICB2YXIgbWluX3ZhbCA9IERhdGUucGFyc2UoZHRfZnJvbSkgLyAoMTAwMCAqIDYwKTtcclxuICAgICAgICB2YXIgbWF4X3ZhbCA9IERhdGUucGFyc2UoZHRfdG8pIC8gKDEwMDAgKiA2MCk7XHJcblxyXG4gICAgICAgIHRoaXMub3B0aW9ucy5mcm9tID0gbWluX3ZhbDtcclxuICAgICAgICB0aGlzLm9wdGlvbnMudG8gPSBtYXhfdmFsO1xyXG5cclxuICAgICAgICB0aGlzLnRvb2xUaXBTbGlkZXIgPSAkKCc8ZGl2IGNsYXNzPVwidWktdG9vbHRpcCB1aS10b29sdGlwLWNvbnRlbnQgc2xpZGVyVG9vbHRpcFwiIC8+JykuY3NzKHtcclxuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXHJcbiAgICAgICAgICAgIHRvcDogMzAsXHJcbiAgICAgICAgICAgIGxlZnQ6IDEwXHJcbiAgICAgICAgfSkuaGlkZSgpO1xyXG5cclxuICAgICAgICBqUXVlcnkodGhpcy5zbGlkZXIubmF0aXZlRWxlbWVudCkuc2xpZGVyKFxyXG4gICAgICAgICAgICB0aGlzLm9wdGlvbnNcclxuICAgICAgICApO1xyXG5cclxuICAgICAgICAvLyAgbGV0IG5ld09wdGlvbiA9ICB7XHJcbiAgICAgICAgLy8gICAgIGZyb206IDEyMCwgdG86IDE0MDAsXHJcbiAgICAgICAgLy8gICAgIHNraW46IFwicm91bmRcIixcclxuICAgICAgICAvLyAgICAgc3RlcDogdGhpcy5zdGVwLFxyXG4gICAgICAgIC8vICAgICBkaW1lbnNpb246ICcnLFxyXG4gICAgICAgIC8vICAgICBzY2FsZSA6IFsnODowMCcsICc5OjAwJywgJzEwOjAwJywgJzExOjAwJywgJzEyOjAwJywgJzEzOjAwJywgJzE0OjAwJywgJzE1OjAwJywgJzE2OjAwJywgJzE3OjAwJ10sXHJcbiAgICAgICAgLy8gICAgIGxpbWl0cyA6IHRydWUsXHJcbiAgICAgICAgLy8gICAgIGNhbGN1bGF0ZTogICB2YWx1ZSAgPT4ge1xyXG4gICAgICAgIC8vICAgICAgICB0aGlzLl9jYWxjdWxhdGVGdW5jdGlvbih2YWx1ZSk7XHJcbiAgICAgICAgLy8gICAgICB9LFxyXG4gICAgICAgIC8vICAgICBvbnN0YXRlY2hhbmdlOiB2YWx1ZSAgPT4ge1xyXG4gICAgICAgIC8vICAgICAgICB0aGlzLl9vbnN0YXRlY2hhbmdlRnVuY3Rpb24odmFsdWUpO1xyXG4gICAgICAgIC8vICAgICAgfSxcclxuICAgICAgICAvLyAgICAgY2FsbGJhY2s6ICB2YWx1ZSAgPT4ge1xyXG4gICAgICAgIC8vICAgICAgICB0aGlzLl9jYWxjdWxhdGVGdW5jdGlvbih2YWx1ZSk7XHJcbiAgICAgICAgLy8gICAgICAgIHRoaXMuWk9PTV9FVkVOVF9aT09NRUQuZW1pdCggdmFsdWUgKTtcclxuICAgICAgICAvLyAgICAgICAgbGV0IHRtcFRhYmxlID0gU3RyaW5nKHZhbHVlKS5zcGxpdCgnOycpO1xyXG4gICAgICAgIC8vICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdG1wVGFibGUubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgLy8gICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gTnVtYmVyKHRtcFRhYmxlW2luZGV4XSk7XHJcbiAgICAgICAgLy8gICAgICAgICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IoZWxlbWVudCAvIDYwKTtcclxuICAgICAgICAvLyAgICAgICAgICAgIGNvbnN0IG1pbnMgPSAoZWxlbWVudCAtIGhvdXJzICogNjApO1xyXG4gICAgICAgIC8vICAgICAgICAgICAgY29uc3QgdGltZSA9IChob3VycyA8IDEwID8gXCIwXCIgKyBob3VycyA6IGhvdXJzKSArIFwiOlwiICsgKG1pbnMgPT0gMCA/IFwiMDBcIiA6IG1pbnMpO1xyXG4gICAgICAgIC8vICAgICAgICAgICAgaWYoaW5kZXggPT0gMCl7XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgdGhpcy5zbGlkZXJGcm9tSW5wdXQubmF0aXZlRWxlbWVudC52YWx1ZSA9IHRpbWU7XHJcbiAgICAgICAgLy8gICAgICAgICAgICB9ZWxzZSB7XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgdGhpcy5zbGlkZXJUb0lucHV0Lm5hdGl2ZUVsZW1lbnQudmFsdWUgPSB0aW1lO1xyXG4gICAgICAgIC8vICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyAgICAgICAgfVxyXG4gICAgICAgIC8vICAgICAgfSxcclxuICAgICAgICAvLyB9OyBcclxuICAgICAgICAvL2pRdWVyeSh0aGlzLnNsaWRlci5uYXRpdmVFbGVtZW50KS5zbGlkZXIoKS5yZWRyYXdBbGwobmV3T3B0aW9uKTtcclxuICAgICAgICAvL0FsbG93cyBudW1iZXJzXHJcbiAgICAgICAgdGhpcy5yZXN0cmljdCA9IHRoaXMuUkVTVFJJQ1RfQ0hBUlM7XHJcbiAgICAgICAgLy9BZGQgbGlzdGVuZXJcclxuICAgICAgICAvLyAgIHRoaXMuYWRkRXZlbnRMaXN0ZW5lcihcImlucHV0XCIsIHRoaXMub25WYWx1ZUNoYW5nZS5iaW5kKHRoaXMpLCBmYWxzZSwgMCwgdHJ1ZSk7XHJcblxyXG4gICAgICAgIC8vYWRkIGZvY3VzIG91dCBldmVudCBsaXN0ZW5lciB0byBob3VycyBpbnB1dC5cclxuICAgICAgICAkKHRoaXMuc2xpZGVyRnJvbUlucHV0Lm5hdGl2ZUVsZW1lbnQpLm9uKFwiZm9jdXNvdXRcIiwgKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgIHRoaXMudmFsaWRhdGVUaW1lKHRoaXMuc2xpZGVyRnJvbUlucHV0Lm5hdGl2ZUVsZW1lbnQpO1xyXG4gICAgICAgICAgICB0aGlzLnJlZnJlc2hTbGlkZXJWYWx1ZXMoKTtcclxuICAgICAgICAgICAgLy8gICQoXCIuc2VsZWN0b3JcIikuc2xpZGVyKFwidmFsdWVcIiwgcDEsIHAyKVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgICQodGhpcy5zbGlkZXJUb0lucHV0Lm5hdGl2ZUVsZW1lbnQpLm9uKFwiZm9jdXNvdXRcIiwgKGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgIHRoaXMudmFsaWRhdGVUaW1lKHRoaXMuc2xpZGVyVG9JbnB1dC5uYXRpdmVFbGVtZW50KTtcclxuICAgICAgICAgICAgdGhpcy5yZWZyZXNoU2xpZGVyVmFsdWVzKCk7XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgfVxyXG5cclxuICAgIGNhbGN1bGF0ZVRpbWUodmFsdWUpIHtcclxuICAgICAgICBsZXQgdG1wVGFibGUgPSBTdHJpbmcodmFsdWUpLnNwbGl0KCc7Jyk7XHJcbiAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRtcFRhYmxlLmxlbmd0aDsgaW5kZXgrKykge1xyXG4gICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gTnVtYmVyKHRtcFRhYmxlW2luZGV4XSk7XHJcbiAgICAgICAgICAgIGNvbnN0IGhvdXJzID0gTWF0aC5mbG9vcihlbGVtZW50IC8gNjApO1xyXG4gICAgICAgICAgICBjb25zdCBtaW5zID0gKGVsZW1lbnQgLSBob3VycyAqIDYwKTtcclxuICAgICAgICAgICAgY29uc3QgdGltZSA9IChob3VycyA8IDEwID8gXCIwXCIgKyBob3VycyA6IGhvdXJzKSArIFwiOlwiICsgKG1pbnMgPT0gMCA/IFwiMDBcIiA6IG1pbnMpO1xyXG4gICAgICAgICAgICBpZiAoaW5kZXggPT0gMCkge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5sZWZ0VHJpZ2dlclRvb2x0aXBWYWx1ZSA9IHRpbWU7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnJpZ2h0VHJpZ2dlclRvb2x0aXBWYWx1ZSA9IHRpbWU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG5cclxuICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdG1wVGFibGUubGVuZ3RoOyBpbmRleCsrKSB7XHJcbiAgICAgICAgICAgIGlmIChpbmRleCA9PSAwKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmxlZnRUcmlnZ2VyVG9vbHRpcFZhbHVlID0gRGF0ZVV0aWxzLmRhdGVUb0lzbyhuZXcgRGF0ZShOdW1iZXIodG1wVGFibGVbaW5kZXhdKSAqIDEwMDAgKiA2MCksIGZhbHNlKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMucmlnaHRUcmlnZ2VyVG9vbHRpcFZhbHVlID0gRGF0ZVV0aWxzLmRhdGVUb0lzbyhuZXcgRGF0ZShOdW1iZXIodG1wVGFibGVbaW5kZXhdKSAqIDEwMDAgKiA2MCksIGZhbHNlKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcblxyXG4gICAgfVxyXG5cclxuXHJcblxyXG5cclxuICAgIHJlZnJlc2hTbGlkZXJWYWx1ZXMoKSB7XHJcblxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRpbWUgPSB0aGlzLnNsaWRlckZyb21JbnB1dC5uYXRpdmVFbGVtZW50LnZhbHVlO1xyXG4gICAgICAgICAgICBjb25zdCB0aW1lVG8gPSB0aGlzLnNsaWRlclRvSW5wdXQubmF0aXZlRWxlbWVudC52YWx1ZTtcclxuICAgICAgICAgICAgLy9Ob3QgbmVlZGVkIGFueW1vcmUgcHJvdGVjdGlvbiB3YXMgYWRkZWQgdG8gc2V0IGVuZGRhdGUgID0gc3RhcnQgZGF0ZSBpZiB0aGUgZGF0ZSBkb2VzIG5vdCBleGlzdCBpbiB0aGUgdGltZURhdGEgXHJcbiAgICAgICAgICAgIC8vYXMgaXQgd2lsbCBiZSBjYWxjdWxhdGVkIG5lZWQgdG8gY2hlY2sgaWYgdGhlIHN0YXJ0IGRheSBuZWVkIHByb3RlY3Rpb24gb3Igbm90LlxyXG4gICAgICAgICAgICAvLyB0aGlzLnN0YXJ0RGF0ZSA9IHRoaXMudGltZURhdGFbdGhpcy52YWx1ZXNbMF1dLnNwbGl0KFwiIFwiKVswXTtcclxuICAgICAgICAgICAgLy8gdGhpcy5lbmREYXRlID0gdGhpcy50aW1lRGF0YVt0aGlzLnZhbHVlc1sxXV0uc3BsaXQoXCIgXCIpWzBdO1xyXG5cclxuXHJcbiAgICAgICAgICAgIGxldCBkdF9mcm9tID0gdGhpcy5zdGFydERhdGUgKyAnICcgKyB0aW1lO1xyXG4gICAgICAgICAgICBsZXQgZHRfdG8gPSB0aGlzLmVuZERhdGUgKyAnICcgKyB0aW1lVG87XHJcbiAgICAgICAgICAgIGlmKHRoaXMudGltZURhdGEuaW5kZXhPZihkdF90bykgPT0gLTEpe1xyXG4gICAgICAgICAgICAgICAgZHRfdG8gPSB0aGlzLnN0YXJ0RGF0ZSArICcgJyArIHRpbWVUbztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBsZXQgdmFsdWVUbyA9IERhdGUucGFyc2UoZHRfdG8pIC8gKDEwMDAgKiA2MCk7XHJcbiAgICAgICAgICAgIGxldCB2YWx1ZUZyb20gPSBEYXRlLnBhcnNlKGR0X2Zyb20pIC8gKDEwMDAgKiA2MCk7XHJcbiAgICAgICAgICAgIGxldCBmcm9tSXNGb3JtYXQ7XHJcbiAgICAgICAgICAgIGxldCB0b0lzb0Zvcm1hdDtcclxuICAgICAgICAgICAgaWYgKHZhbHVlVG8gPCB2YWx1ZUZyb20pIHtcclxuICAgICAgICAgICAgICAgIHZhbHVlVG8gPSB2YWx1ZUZyb207XHJcblxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHZhbHVlRnJvbSA+IHZhbHVlVG8pIHtcclxuICAgICAgICAgICAgICAgIHZhbHVlRnJvbSA9IHZhbHVlVG87XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGZyb21Jc0Zvcm1hdCA9IERhdGVVdGlscy5kYXRlVG9Jc28obmV3IERhdGUodmFsdWVGcm9tICogMTAwMCAqIDYwKSwgZmFsc2UpO1xyXG4gICAgICAgICAgICB0aGlzLnNsaWRlckZyb21JbnB1dC5uYXRpdmVFbGVtZW50LnZhbHVlID0gZnJvbUlzRm9ybWF0LnNwbGl0KFwiIFwiKVsxXTtcclxuICAgICAgICAgICAgdG9Jc29Gb3JtYXQgPSBEYXRlVXRpbHMuZGF0ZVRvSXNvKG5ldyBEYXRlKHZhbHVlVG8gKiAxMDAwICogNjApLCBmYWxzZSk7XHJcbiAgICAgICAgICAgIHRoaXMuc2xpZGVyVG9JbnB1dC5uYXRpdmVFbGVtZW50LnZhbHVlID0gdG9Jc29Gb3JtYXQuc3BsaXQoXCIgXCIpWzFdO1xyXG4gICAgICAgICAgICBqUXVlcnkodGhpcy5zbGlkZXIubmF0aXZlRWxlbWVudCkuc2xpZGVyKFwidmFsdWVcIiwgdmFsdWVGcm9tLCB2YWx1ZVRvKVxyXG5cclxuICAgICAgICAgICAgdGhpcy5fdmFsdWVzID0gW3RoaXMudGltZURhdGEuaW5kZXhPZihmcm9tSXNGb3JtYXQpLHRoaXMudGltZURhdGEuaW5kZXhPZih0b0lzb0Zvcm1hdCkgXVxyXG4gICAgICAgICAgICBcclxuXHJcbiAgICAgICAgICAgIGxldCBldmVudCA9IHsgZnJvbTogZnJvbUlzRm9ybWF0LCB0bzogdG9Jc29Gb3JtYXQgfTtcclxuICAgICAgICAgICAgdGhpcy5aT09NX0VWRU5UX1pPT01FRC5lbWl0KGV2ZW50KTtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhlKTtcclxuXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHZhbGlkYXRlVGltZSh0ZXh0SW5wdXQpOiBhbnkge1xyXG4gICAgICAgIGxldCB2YWxpZFRpbWVNZXNzYWdlID0gU3d0VXRpbC5nZXRQcmVkaWN0TWVzc2FnZSgnYWxlcnQudmFsaWRUaW1lJywgbnVsbClcclxuICAgICAgICBsZXQgaG91cjogYW55O1xyXG4gICAgICAgIGxldCBtaW5zOiBhbnk7XHJcbiAgICAgICAgbGV0IHRtcFZhbHVlID0gdGV4dElucHV0LnZhbHVlO1xyXG5cclxuICAgICAgICB2YXIgcG9zID0gdG1wVmFsdWUuaW5kZXhPZihcIjpcIik7XHJcbiAgICAgICAgaWYgKHBvcyA8IDApIHtcclxuICAgICAgICAgICAgaWYgKHRtcFZhbHVlLmxlbmd0aCA9PSAxKSB7XHJcbiAgICAgICAgICAgICAgICB0bXBWYWx1ZSA9ICcwJyArIHRtcFZhbHVlICsgJzowMCc7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodG1wVmFsdWUubGVuZ3RoID09IDIpIHtcclxuICAgICAgICAgICAgICAgIHRtcFZhbHVlID0gdG1wVmFsdWUgKyAnOjAwJztcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh0bXBWYWx1ZS5sZW5ndGggPT0gMykge1xyXG4gICAgICAgICAgICAgICAgdG1wVmFsdWUgPSB0bXBWYWx1ZS5zdWJzdHJpbmcoMCwgMikgKyAnOicgKyB0bXBWYWx1ZVsyXSArICcwJztcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh0bXBWYWx1ZS5sZW5ndGggPT0gNCkge1xyXG4gICAgICAgICAgICAgICAgdG1wVmFsdWUgPSB0bXBWYWx1ZS5zdWJzdHJpbmcoMCwgMikgKyAnOicgKyB0bXBWYWx1ZS5zdWJzdHJpbmcoMiwgNCk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB0bXBWYWx1ZSA9IHRtcFZhbHVlLnN1YnN0cmluZygwLCA1KTtcclxuICAgICAgICAgICAgICAgIHRtcFZhbHVlID0gdG1wVmFsdWUuc3Vic3RyaW5nKDAsIDIpICsgJzonICsgdG1wVmFsdWUuc3Vic3RyaW5nKDIsIDQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBob3VyID0gcGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcoMCwgMikpO1xyXG4gICAgICAgICAgICBtaW5zID0gcGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcoMywgNSkpO1xyXG5cclxuICAgICAgICAgICAgaG91ciA9IChob3VyID4gMjQgPyAyMyA6IChob3VyIDwgMTAgPyAoaG91ciA9PSAwID8gJzAwJyA6ICcwJ1xyXG4gICAgICAgICAgICAgICAgKyBob3VyKSA6IGhvdXIpKTtcclxuXHJcbiAgICAgICAgICAgIG1pbnMgPSAobWlucyA+PSA2MCA/IDU5IDogKG1pbnMgPCAxMCA/IChtaW5zID49IDYgPyA1OSA6IG1pbnNcclxuICAgICAgICAgICAgICAgICsgJzAnKSA6IG1pbnMpKTtcclxuXHJcbiAgICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgICAgIGlmICh0bXBWYWx1ZS5jaGFyQXQoMCkgPT0gJzAnKSB7XHJcbiAgICAgICAgICAgICAgICBob3VyID0gJzAnXHJcbiAgICAgICAgICAgICAgICAgICAgKyAocGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcoMSwgMikpID8gcGFyc2VGbG9hdCh0bXBWYWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc3Vic3RyaW5nKDEsIDIpKSA6ICcwJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBob3VyID0gKHBhcnNlRmxvYXQodG1wVmFsdWUuc3Vic3RyaW5nKDAsIDIpKSA/IHBhcnNlRmxvYXQodG1wVmFsdWVcclxuICAgICAgICAgICAgICAgICAgICAuc3Vic3RyaW5nKDAsIDIpKSA6ICcwMCcpO1xyXG4gICAgICAgICAgICAgICAgaG91ciA9IChwYXJzZUZsb2F0KGhvdXIpID4gMjQgPyAyMyA6IChwYXJzZUZsb2F0KGhvdXIpIDwgMTAgPyAoaG91ciA9PSAwID8gJzAwJyA6ICcwJ1xyXG4gICAgICAgICAgICAgICAgICAgICsgaG91cikgOiBob3VyKSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmIChwb3MgIT0gLTEpIHtcclxuICAgICAgICAgICAgICAgIGlmICh0bXBWYWx1ZS5jaGFyQXQocG9zICsgMSkgPT0gJzAnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWlucyA9ICcwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICArIChwYXJzZUZsb2F0KHRtcFZhbHVlLnN1YnN0cmluZyhwb3MgKyAyLCBwb3MgKyAzKSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcocG9zICsgMiwgcG9zICsgMykpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcwJyk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIG1pbnMgPSAocGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcocG9zICsgMSwgNSkpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gcGFyc2VGbG9hdCh0bXBWYWx1ZS5zdWJzdHJpbmcocG9zICsgMSwgNSkpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJzAwJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG1pbnMgIT0gJzAwJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW5zID0gKHBhcnNlRmxvYXQobWlucykgPj0gNjAgPyA1OSA6IChwYXJzZUZsb2F0KG1pbnMpIDwgMTAgPyAocGFyc2VGbG9hdChtaW5zKSA+PSA2ID8gNTkgOiBtaW5zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICArICcwJykgOiBtaW5zKSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG5cclxuICAgICAgICB9XHJcbiAgICAgICAgbGV0IHJlc3VsdCA9IGhvdXIgKyBcIjpcIiArIG1pbnM7XHJcblxyXG4gICAgICAgIGNvbnN0IHZhbGlkID0gbW9tZW50KHJlc3VsdCwgJ0hIOm1tJywgdHJ1ZSkuaXNWYWxpZCgpO1xyXG5cclxuICAgICAgICBpZiAodmFsaWQgJiYgIXZhbGlkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuc3d0YWxlcnQud2FybmluZyh2YWxpZFRpbWVNZXNzYWdlLCBudWxsKTtcclxuICAgICAgICAgICAgLy90ZXh0SW5wdXQudGV4dCA9IFwiXCI7XHJcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0ZXh0SW5wdXQudmFsdWUgPSByZXN1bHQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIC8qKlxyXG4gICAgLy8gICAqIFZhbGlkYXRlIHRoZSB2YWx1ZSwgd2hlbmV2ZXIgaXRzIGNoYW5nZWRcclxuICAgIC8vICAgKiBcclxuICAgIC8vICAgKiBAcGFyYW0gZXZlbnQ6IEV2ZW50XHJcbiAgICAvLyAgKi9cclxuICAgIC8vICBwcml2YXRlICBvblZhbHVlQ2hhbmdlKGV2ZW50KTogdm9pZCB7XHJcbiAgICAvLyAgICAgIC8vIFN0b3AgdGhlIGV2ZW50LlxyXG4gICAgLy8gICAgIC8vIGV2ZW50LnN0b3BJbW1lZGlhdGVQcm9wYWdhdGlvbigpO1xyXG4gICAgLy8gICAgICB0aGlzLnRleHQgPSAodGhpcy50ZXh0ID09IFwiXCIgPyBcIlwiIDogU3RyaW5nKHRoaXMuY2hlY2tWYWxpZFZhbHVlKHRoaXMudGV4dCkpKTtcclxuICAgIC8vICAgICAgLy9fb25DaGFuZ2UoZXZlbnQpO1xyXG4gICAgLy8gfVxyXG5cclxuICAgIC8vICBwcml2YXRlICBjaGVja1ZhbGlkVmFsdWUodmFsdWU6IGFueSk6IGFueSB7XHJcblxyXG4gICAgLy8gICAgICBpZiAodGhpcy5tYXhpbXVtICE9IC0xICYmIHRoaXMubWluaW11bSAhPSAtMSkge1xyXG4gICAgLy8gICAgICAgICAgLy9JZiB0aGUgdmFsdWUgZXhjZWVkcyBtYXhpbXVtLCB0aGVuIHJldHVybnMgbWF4aW11bSB2YWx1ZVxyXG4gICAgLy8gICAgICAgICAgY29uc3QgY29udHJvbE1heCA9IG5ldyBGb3JtQ29udHJvbCh2YWx1ZSAsIFZhbGlkYXRvcnMubWF4KE51bWJlcih0aGlzLm1heGltdW0pKSk7XHJcbiAgICAvLyAgICAgICAgICBjb25zdCBjb250cm9sTWluID0gbmV3IEZvcm1Db250cm9sKHZhbHVlICwgVmFsaWRhdG9ycy5taW4oTnVtYmVyKHRoaXMubWluaW11bSkpKTtcclxuICAgIC8vICAgICAgICAgIGlmICghY29udHJvbE1heC52YWxpZCkge1xyXG4gICAgLy8gICAgICAgICAgICAgIHJldHVybiB0aGlzLm1heGltdW07XHJcbiAgICAvLyAgICAgICAgICB9IGVsc2UgaWYgKCFjb250cm9sTWluLnZhbGlkKSB7XHJcbiAgICAvLyAgICAgICAgICAgICAgLy9JZiB0aGUgdmFsdWUgbGVzcyB0aGFuIG1pbmltdW0sIHRoZW4gcmV0dXJucyBtaW5pbXVtIHZhbHVlXHJcbiAgICAvLyAgICAgICAgICAgICAgcmV0dXJuIHRoaXMubWluaW11bTtcclxuICAgIC8vICAgICB9IGVsc2Uge1xyXG4gICAgLy8gICAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIC8vICAgICB9XHJcbiAgICAvLyAgICAgfSBlbHNlIHtcclxuICAgIC8vICAgICAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIC8vIH1cclxuICAgIC8vIH1cclxuXHJcbn1cclxuIl19