import { OnInit, ElementRef } from '@angular/core';
import { CommonService } from '../../utils/common.service';
import { SwtModule } from '../../controls/swt-module.component';
export declare class JSONViewer extends SwtModule implements OnInit {
    private commonService;
    private element;
    data: any;
    jsonData: any;
    description: any;
    message: any;
    constructor(commonService: CommonService, element: ElementRef);
    code: string;
    ngOnInit(): void;
    close(): void;
}
