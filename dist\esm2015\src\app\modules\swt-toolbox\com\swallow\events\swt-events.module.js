/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { EventEmitter, NgModule } from '@angular/core';
/** @type {?} */
export const TabSelectEvent = new EventEmitter();
/** @type {?} */
export const DividerResizeComplete = new EventEmitter();
/** @type {?} */
export const TabCloseEvent = new EventEmitter();
/** @type {?} */
export const WindowDragEvent = new EventEmitter();
/** @type {?} */
export const WindowDragStartEvent = new EventEmitter();
/** @type {?} */
export const WindowDragEndEvent = new EventEmitter();
/** @type {?} */
export const WindowCloseEvent = new EventEmitter();
/** @type {?} */
export const WindowCreateEvent = new EventEmitter();
/** @type {?} */
export const WindowResizeEvent = new EventEmitter();
/** @type {?} */
export const WindowMinimizeEvent = new EventEmitter();
/** @type {?} */
export const WindowMaximizeEvent = new EventEmitter();
/** @type {?} */
export const HDividedEndResizeEvent = new EventEmitter();
/** @type {?} */
export const VDividedEndResizeEvent = new EventEmitter();
/** @type {?} */
export const TabChange = new EventEmitter();
/** @type {?} */
export const TabClose = new EventEmitter();
/** @type {?} */
export const SwtCommonGridItemRenderChanges = new EventEmitter();
/** @type {?} */
export const ExportEvent = new EventEmitter();
/** @type {?} */
export const AdvancedExportEvent = new EventEmitter();
/** @type {?} */
export const HorizontalScrollPositionEvent = new EventEmitter();
/** @type {?} */
export const VerticalScrollPositionEvent = new EventEmitter();
/** @type {?} */
export const SeriesHighlightEvent = new EventEmitter();
/** @type {?} */
export const LegendItemChangedEvent = new EventEmitter();
/** @type {?} */
export const SwtCheckboxEvent = new EventEmitter();
/**
 * This class contain all module events
 */
export class ModuleEvent {
}
// Dispatched once the module is sufficiently loaded
ModuleEvent.READY = "ready";
// Dispatched at regular intervals while the module is being loaded.
ModuleEvent.PROGRESS = "progress";
// Dispatched if there was an error during module loading.
ModuleEvent.ERROR = "error";
// Dispatched  once the module is sufficiently loaded
ModuleEvent.SETUP = "setup";
// Dispatched when the module data is unloaded.
ModuleEvent.UNLOAD = "unload";
ModuleEvent.DISPOSE = "dispose";
if (false) {
    /** @type {?} */
    ModuleEvent.READY;
    /** @type {?} */
    ModuleEvent.PROGRESS;
    /** @type {?} */
    ModuleEvent.ERROR;
    /** @type {?} */
    ModuleEvent.SETUP;
    /** @type {?} */
    ModuleEvent.UNLOAD;
    /** @type {?} */
    ModuleEvent.DISPOSE;
}
export class WindowEvent {
}
WindowEvent.DRAG = "drag";
WindowEvent.DRAGSTART = "dragstart";
WindowEvent.DRAGEND = "dragend";
WindowEvent.WINDOWCLOSE = "windowClose";
WindowEvent.CREATE = "create";
WindowEvent.RESIZE = "resize";
WindowEvent.MINIMIZE = "minimize";
WindowEvent.MAXIMIZE = "maximize";
if (false) {
    /** @type {?} */
    WindowEvent.DRAG;
    /** @type {?} */
    WindowEvent.DRAGSTART;
    /** @type {?} */
    WindowEvent.DRAGEND;
    /** @type {?} */
    WindowEvent.WINDOWCLOSE;
    /** @type {?} */
    WindowEvent.CREATE;
    /** @type {?} */
    WindowEvent.RESIZE;
    /** @type {?} */
    WindowEvent.MINIMIZE;
    /** @type {?} */
    WindowEvent.MAXIMIZE;
}
export class CustomTreeEvent {
}
// declare event itemrender to be fired on tree item rendering.
CustomTreeEvent.ITEMRENDER = "itemrender";
// declare event itemdoubleclick  to be fired on tree item double click.
CustomTreeEvent.ITEMDOUBLECLICK = "itemDoubleClick";
// declare event itemclick  to be fired on tree item click.
CustomTreeEvent.ITEMCLICK = "itemClick";
CustomTreeEvent.ICONCLICK = "iconclick";
CustomTreeEvent.EXPANDERCLICK = "expanderclick";
CustomTreeEvent.TITLECLICK = "titleclick";
CustomTreeEvent.ICONFOCUS = "iconfocus";
CustomTreeEvent.ICONFOCUSOUT = "iconfocusout";
CustomTreeEvent.ICONMOUSELEAVE = "iconmouseleave";
CustomTreeEvent.ICONMOUSEENTER = "iconmouseenter";
if (false) {
    /** @type {?} */
    CustomTreeEvent.ITEMRENDER;
    /** @type {?} */
    CustomTreeEvent.ITEMDOUBLECLICK;
    /** @type {?} */
    CustomTreeEvent.ITEMCLICK;
    /** @type {?} */
    CustomTreeEvent.ICONCLICK;
    /** @type {?} */
    CustomTreeEvent.EXPANDERCLICK;
    /** @type {?} */
    CustomTreeEvent.TITLECLICK;
    /** @type {?} */
    CustomTreeEvent.ICONFOCUS;
    /** @type {?} */
    CustomTreeEvent.ICONFOCUSOUT;
    /** @type {?} */
    CustomTreeEvent.ICONMOUSELEAVE;
    /** @type {?} */
    CustomTreeEvent.ICONMOUSEENTER;
}
export class genericEvent {
}
// [static] The ACTIVATE constant defines the value of the type property of an activate event object.
genericEvent.ACTIVATE = "activate";
// [static] The mouseover constant defines the value of the type property of an mouseover event object.
genericEvent.MOUSE_OVER = "mouseover";
// [static] The mouseup constant defines the value of the type property of an mouseup event object.
genericEvent.MOUSE_UP = "mouseup";
// [static] The mousedown constant defines the value of the type property of an mousedown event object.
genericEvent.MOUSE_DOWN = "mousedown";
// [static] The focus constant defines the value of the type property of an focus event object.
genericEvent.FOCUS = "focus";
// [static] The focusout constant defines the value of the type property of an focusout event object.
genericEvent.FOCUSOUT = "focusout";
// [static] The Event.ADDED constant defines the value of the type property of an added event object.
genericEvent.ADDED = "added";
// [static] The Event.ADDED_TO_STAGE constant defines the value of the type property of an addedToStage event object.
genericEvent.ADDED_TO_STAGE = "addedToStage";
// [static] The Event.BROWSER_ZOOM_CHANGE constant defines the value of the type property of an browserZoomChange event object.
genericEvent.BROWSER_ZOOM_CHANGE = "browserZoomChange";
// [static] The Event.CANCEL constant defines the value of the type property of a cancel event object.
genericEvent.CANCEL = "cancel";
// [static] The Event.CHANGE constant defines the value of the type property of a change event object.
genericEvent.CHANGE = "change";
// [static] The Event.CHANNEL_MESSAGE constant defines the value of the type property of a channelMessage event object.
genericEvent.CHANNEL_MESSAGE = "channelMessage";
// [static] The Event.CHANNEL_STATE constant defines the value of the type property of a channelState event object.
genericEvent.CHANNEL_STATE = "channelState";
// [static] The Event.CLEAR constant defines the value of the type property of a clear event object.
genericEvent.CLEAR = "clear";
// [static] The Event.CLOSE constant defines the value of the type property of a close event object.
genericEvent.CLOSE = "close";
// [static] The Event.CLOSING constant defines the value of the type property of a closing event object.
genericEvent.CLOSING = "closing";
// [static] The Event.COMPLETE constant defines the value of the type property of a complete event object.
genericEvent.COMPLETE = "complete";
// [static] The Event.CONNECT constant defines the value of the type property of a connect event object.
genericEvent.CONNECT = "connect";
// [static] The Event.CONTEXT3D_CREATE constant defines the value of the type property of a context3Dcreate event object.
genericEvent.CONTEXT3D_CREATE = "context3DCreate";
// [static] Defines the value of the type property of a copy event object.
genericEvent.COPY = "copy";
// [static] Defines the value of the type property of a cut event object.
genericEvent.CUT = "cut";
// [static] The Event.DEACTIVATE constant defines the value of the type property of a deactivate event object.
genericEvent.DEACTIVATE = "deactivate";
// [static] The Event.DISPLAYING constant defines the value of the type property of a displaying event object.
genericEvent.DISPLAYING = "displaying";
// [static] The Event.ENTER_FRAME constant defines the value of the type property of an enterFrame event object.
genericEvent.ENTER_FRAME = "enterFrame";
// [static] The Event.EXIT_FRAME constant defines the value of the type property of an exitFrame event object.
genericEvent.EXIT_FRAME = "exitFrame";
// [static] The Event.EXITING constant defines the value of the type property of an exiting event object.
genericEvent.EXITING = "exiting";
// [static] The Event.FRAME_CONSTRUCTED constant defines the value of the type property of an frameConstructed event object.
genericEvent.FRAME_CONSTRUCTED = "frameConstructed";
// [static] The Event.FRAME_LABEL constant defines the value of the type property of an frameLabel event object.
genericEvent.FRAME_LABEL = "frameLabel";
// [static] The Event.FULL_SCREEN constant defines the value of the type property of a fullScreen event object.
genericEvent.FULLSCREEN = "fullScreen";
// [static] The Event.HTML_BOUNDS_CHANGE constant defines the value of the type property of an htmlBoundsChange event object.
genericEvent.HTML_BOUNDS_CHANGE = "htmlBoundsChange";
// [static] The Event.HTML_DOM_INITIALIZE constant defines the value of the type property of an htmlDOMInitialize event object.
genericEvent.HTML_DOM_INITIALIZE = "htmlDOMInitialize";
// [static] The Event.HTML_RENDER constant defines the value of the type property of an htmlRender event object.
genericEvent.HTML_RENDER = "htmlRender";
// [static] The Event.ID3 constant defines the value of the type property of an id3 event object.
genericEvent.ID3 = "id3";
// [static] The Event.INIT constant defines the value of the type property of an init event object.
genericEvent.INIT = "init";
// [static] The Event.LOCATION_CHANGE constant defines the value of the type property of a locationChange event object.
genericEvent.LOCATION_CHANGE = "locationChange";
// [static] The Event.MOUSE_LEAVE constant defines the value of the type property of a mouseLeave event object.
genericEvent.MOUSE_LEAVE = "mouseLeave";
// [static] The Event.NETWORK_CHANGE constant defines the value of the type property of a networkChange event object.
genericEvent.NETWORK_CHANGE = "networkChange";
// [static] The Event.OPEN constant defines the value of the type property of an open event object.
genericEvent.OPEN = "open";
// [static] The Event.PASTE constant defines the value of the type property of a paste event object.
genericEvent.PASTE = "paste";
// [static] The Event.PREPARING constant defines the value of the type property of a preparing event object.
genericEvent.PREPARING = "preparing";
// [static] The Event.REMOVED constant defines the value of the type property of a removed event object.
genericEvent.REMOVED = "removed";
// [static] The Event.REMOVED_FROM_STAGE constant defines the value of the type property of a removedFromStage event object.
genericEvent.REMOVED_FROM_STAGE = "removedFromStage";
// [static] The Event.RENDER constant defines the value of the type property of a render event object.
genericEvent.RENDER = "render";
// [static] The Event.RESIZE constant defines the value of the type property of a resize event object.
genericEvent.RESIZE = "resize";
// [static] The Event.SCROLL constant defines the value of the type property of a scroll event object.
genericEvent.SCROLL = "scroll";
// [static] The Event.SELECT constant defines the value of the type property of a select event object.
genericEvent.SELECT = "select";
// [static] The Event.SELECT_ALL constant defines the value of the type property of a selectAll event object.
genericEvent.SELECT_ALL = "selectAll";
// [static] The Event.SOUND_COMPLETE constant defines the value of the type property of a soundComplete event object.
genericEvent.SOUND_COMPLETE = "soundComplete";
// [static] The Event.STANDARD_ERROR_CLOSE constant defines the value of the type property of a standardErrorClose event object.
genericEvent.STANDARD_ERROR_CLOSE = "standardErrorClose";
// [static] The Event.STANDARD_INPUT_CLOSE constant defines the value of the type property of a standardInputClose event object.
genericEvent.STANDARD_INPUT_CLOSE = "standardInputClose";
// [static] The Event.STANDARD_OUTPUT_CLOSE constant defines the value of the type property of a standardOutputClose event object.
genericEvent.STANDARD_OUTPUT_CLOSE = "standardOutputClose";
// [static] The Event.SUSPEND constant defines the value of the type property of an suspend event object.
genericEvent.SUSPEND = "suspend";
// [static] The Event.TAB_CHILDREN_CHANGE constant defines the value of the type property of a tabChildrenChange event object.
genericEvent.TAB_CHILDREN_CHANGE = "tabChildrenChange";
// [static] The Event.TAB_ENABLED_CHANGE constant defines the value of the type property of a tabEnabledChange event object.
genericEvent.TAB_ENABLED_CHANGE = "tabEnabledChange";
// [static] The Event.TAB_INDEX_CHANGE constant defines the value of the type property of a tabIndexChange event object.
genericEvent.TAB_INDEX_CHANGE = "tabIndexChange";
// [static] The Event.TEXT_INTERACTION_MODE_CHANGE constant defines the value of the type property of a interaction mode event object.
genericEvent.TEXT_INTERACTION_MODE_CHANGE = "textInteractionModeChange";
// [static] The Event.TEXTURE_READY constant defines the value of the type property of a textureReady event object.
genericEvent.TEXTURE_READY = "textureReady";
// [static] The Event.UNLOAD constant defines the value of the type property of an unload event object.
genericEvent.UNLOAD = "unload";
// [static] The Event.USER_IDLE constant defines the value of the type property of a userIdle event object.
genericEvent.USER_IDLE = "userIdle";
// [static] The Event.USER_PRESENT constant defines the value of the type property of a userPresent event object.
genericEvent.USER_PRESENT = "userPresent";
// [static] The Event.VIDEO_FRAME constant defines the value of the type property of a videoFrame event object.
genericEvent.VIDEO_FRAME = "videoFrame";
// [static] The Event.WORKER_STATE constant defines the value of the type property of a workerState event object.
genericEvent.WORKER_STATE = "workerState";
// [static] The Event.ROW_CLICK constant defines the value of the type property of a rowClick event object.
genericEvent.ROW_CLICK = "rowClick";
// [static] The Event.ROW_CLICK constant defines the value of the type property of a cell click event object.
genericEvent.CELL_CLICK = "cellclick";
// [static] The Event.ROW_DBCLICK constant defines the value of the type property of a rowDbClick event object.
genericEvent.ROW_DBCLICK = "rowDbClick";
// [static] The Event.CELL_DBCLICK constant defines the value of the type property of a cell double click event object.
genericEvent.CELL_DBCLICK = "celldbclick";
// [static] The Event.ITEM_EXPAND constant defines the value of the type property of a itemExpand event object.
genericEvent.ITEM_EXPAND = "itemExpand";
// [static] The Event.ITEM_COLLAPSE constant defines the value of the type property of a itemCollapse event object.
genericEvent.ITEM_COLLAPSE = "itemCollapse";
// [static] The Event.CLICK constant defines the value of the type property of a click event object.
genericEvent.CLICK = "click";
if (false) {
    /** @type {?} */
    genericEvent.ACTIVATE;
    /** @type {?} */
    genericEvent.MOUSE_OVER;
    /** @type {?} */
    genericEvent.MOUSE_UP;
    /** @type {?} */
    genericEvent.MOUSE_DOWN;
    /** @type {?} */
    genericEvent.FOCUS;
    /** @type {?} */
    genericEvent.FOCUSOUT;
    /** @type {?} */
    genericEvent.ADDED;
    /** @type {?} */
    genericEvent.ADDED_TO_STAGE;
    /** @type {?} */
    genericEvent.BROWSER_ZOOM_CHANGE;
    /** @type {?} */
    genericEvent.CANCEL;
    /** @type {?} */
    genericEvent.CHANGE;
    /** @type {?} */
    genericEvent.CHANNEL_MESSAGE;
    /** @type {?} */
    genericEvent.CHANNEL_STATE;
    /** @type {?} */
    genericEvent.CLEAR;
    /** @type {?} */
    genericEvent.CLOSE;
    /** @type {?} */
    genericEvent.CLOSING;
    /** @type {?} */
    genericEvent.COMPLETE;
    /** @type {?} */
    genericEvent.CONNECT;
    /** @type {?} */
    genericEvent.CONTEXT3D_CREATE;
    /** @type {?} */
    genericEvent.COPY;
    /** @type {?} */
    genericEvent.CUT;
    /** @type {?} */
    genericEvent.DEACTIVATE;
    /** @type {?} */
    genericEvent.DISPLAYING;
    /** @type {?} */
    genericEvent.ENTER_FRAME;
    /** @type {?} */
    genericEvent.EXIT_FRAME;
    /** @type {?} */
    genericEvent.EXITING;
    /** @type {?} */
    genericEvent.FRAME_CONSTRUCTED;
    /** @type {?} */
    genericEvent.FRAME_LABEL;
    /** @type {?} */
    genericEvent.FULLSCREEN;
    /** @type {?} */
    genericEvent.HTML_BOUNDS_CHANGE;
    /** @type {?} */
    genericEvent.HTML_DOM_INITIALIZE;
    /** @type {?} */
    genericEvent.HTML_RENDER;
    /** @type {?} */
    genericEvent.ID3;
    /** @type {?} */
    genericEvent.INIT;
    /** @type {?} */
    genericEvent.LOCATION_CHANGE;
    /** @type {?} */
    genericEvent.MOUSE_LEAVE;
    /** @type {?} */
    genericEvent.NETWORK_CHANGE;
    /** @type {?} */
    genericEvent.OPEN;
    /** @type {?} */
    genericEvent.PASTE;
    /** @type {?} */
    genericEvent.PREPARING;
    /** @type {?} */
    genericEvent.REMOVED;
    /** @type {?} */
    genericEvent.REMOVED_FROM_STAGE;
    /** @type {?} */
    genericEvent.RENDER;
    /** @type {?} */
    genericEvent.RESIZE;
    /** @type {?} */
    genericEvent.SCROLL;
    /** @type {?} */
    genericEvent.SELECT;
    /** @type {?} */
    genericEvent.SELECT_ALL;
    /** @type {?} */
    genericEvent.SOUND_COMPLETE;
    /** @type {?} */
    genericEvent.STANDARD_ERROR_CLOSE;
    /** @type {?} */
    genericEvent.STANDARD_INPUT_CLOSE;
    /** @type {?} */
    genericEvent.STANDARD_OUTPUT_CLOSE;
    /** @type {?} */
    genericEvent.SUSPEND;
    /** @type {?} */
    genericEvent.TAB_CHILDREN_CHANGE;
    /** @type {?} */
    genericEvent.TAB_ENABLED_CHANGE;
    /** @type {?} */
    genericEvent.TAB_INDEX_CHANGE;
    /** @type {?} */
    genericEvent.TEXT_INTERACTION_MODE_CHANGE;
    /** @type {?} */
    genericEvent.TEXTURE_READY;
    /** @type {?} */
    genericEvent.UNLOAD;
    /** @type {?} */
    genericEvent.USER_IDLE;
    /** @type {?} */
    genericEvent.USER_PRESENT;
    /** @type {?} */
    genericEvent.VIDEO_FRAME;
    /** @type {?} */
    genericEvent.WORKER_STATE;
    /** @type {?} */
    genericEvent.ROW_CLICK;
    /** @type {?} */
    genericEvent.CELL_CLICK;
    /** @type {?} */
    genericEvent.ROW_DBCLICK;
    /** @type {?} */
    genericEvent.CELL_DBCLICK;
    /** @type {?} */
    genericEvent.ITEM_EXPAND;
    /** @type {?} */
    genericEvent.ITEM_COLLAPSE;
    /** @type {?} */
    genericEvent.CLICK;
}
export class SwtEventsModule {
}
SwtEventsModule.decorators = [
    { type: NgModule, args: [{
                declarations: [],
                imports: []
            },] }
];
//# sourceMappingURL=data:application/json;base64,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