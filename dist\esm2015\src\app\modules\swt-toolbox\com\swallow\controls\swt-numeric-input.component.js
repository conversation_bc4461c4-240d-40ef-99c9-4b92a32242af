/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
import { FormControl, Validators } from "@angular/forms";
export class SwtNumericInput extends SwtTextInput {
    constructor() {
        super(...arguments);
        this._maximum = -1;
        this._minimum = -1;
        this.RESTRICT_CHARS = "0-9";
    }
    //---maximum-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set maximum(value) {
        try {
            this._maximum = Number(value);
        }
        catch (error) {
            console.error('method [ set maximum] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get maximum() {
        return this._maximum;
    }
    //---minimum-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set minimum(value) {
        try {
            this._minimum = Number(value);
        }
        catch (error) {
            console.error('method [ set minimum] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get minimum() {
        return this._minimum;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        //Allows numbers
        this.restrict = this.RESTRICT_CHARS;
        //Add listener
        this.addEventListener("input", this.onValueChange.bind(this), false, 0, true);
    }
    /**
     * Validate the value, whenever its changed
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    onValueChange(event) {
        // Stop the event.
        // event.stopImmediatePropagation();
        this.text = (this.text == "" ? "" : String(this.checkValidValue(this.text)));
        //_onChange(event);
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    checkValidValue(value) {
        if (this.maximum != -1 && this.minimum != -1) {
            //If the value exceeds maximum, then returns maximum value
            /** @type {?} */
            const controlMax = new FormControl(value, Validators.max(Number(this.maximum)));
            /** @type {?} */
            const controlMin = new FormControl(value, Validators.min(Number(this.minimum)));
            if (!controlMax.valid) {
                return this.maximum;
            }
            else if (!controlMin.valid) {
                //If the value less than minimum, then returns minimum value
                return this.minimum;
            }
            else {
                return value;
            }
        }
        else {
            return value;
        }
    }
}
SwtNumericInput.decorators = [
    { type: Component, args: [{
                selector: 'SwtNumericInput',
                template: `
     <input #textfield
     popper="{{this.toolTipPreviousValue}}"
     [popperTrigger]="'hover'"
     [popperDisabled]="toolTipPreviousValue === null ? true : false"
     [popperPlacement]="'bottom'"
     [ngClass]="{'border-orange-previous': toolTipPreviousValue != null}"
            type="number"
            (paste)="onPaste($event)"
            class="textinput "
            [class.requiredInput]= "this.required==true && !this.text  && enabled==true"
     />
  `,
                styles: [`
        :host {
            outline:none;
        }
           input {
               height: 23px;
               line-height:23px;
               padding-left: 3px;
               padding-right: 3px;
               border: 1px solid #7f9db9; 
               width: 100%;
               cursor: text;
               color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/
           /* margin: 0px 5px 5px 0px;*/
          }

           input:disabled {
               color: #E5E5E5;
           }

          .ng-invalid {
             border: 2px solid red; 
             color: red;
          }

          span {
              color: red;
              font-weight: bold;
              font-size: 11px;
          }
      `]
            }] }
];
SwtNumericInput.propDecorators = {
    maximum: [{ type: Input, args: ['maximum',] }],
    minimum: [{ type: Input, args: ['minimum',] }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype._maximum;
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype._minimum;
    /**
     * @type {?}
     * @private
     */
    SwtNumericInput.prototype.RESTRICT_CHARS;
}
//# sourceMappingURL=data:application/json;base64,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