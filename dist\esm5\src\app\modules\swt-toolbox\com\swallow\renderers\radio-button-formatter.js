/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export var RadioButtonFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var field = columnDef.field;
    /** @type {?} */
    var negative = false;
    /** @type {?} */
    var enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    var showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (value instanceof Object) {
        value = value['content'];
    }
    /** @type {?} */
    var text = "";
    if (showHideCells) {
        text = "<input " + ((value === 'N' || value === 'false' || value === false || value == null || value == undefined || value == '') ? '' : 'checked') + " " + ((enabledFlag == false || !columnDef.params.grid.enabled) ? 'disabled' : '') + " type='radio'  class='editor-radio'  />";
    }
    return (text);
});
//# sourceMappingURL=data:application/json;base64,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