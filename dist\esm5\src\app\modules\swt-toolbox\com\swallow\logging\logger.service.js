/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
/**
 * This is a "ngx-logger" branch (https://www.npmjs.com/package/ngx-logger)
 * Customized logging template + Simplified into our needs
 */
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
var Logger = /** @class */ (function () {
    function Logger(_class, http, level) {
        if (level === void 0) { level = LoggerLevel.TRACE; }
        this._class = _class;
        this.http = http;
        this.level = level;
        this._clientLogLevel = LoggerLevel.INFO;
        this._serverLogLevel = LoggerLevel.OFF;
        this._isIE = true;
        this.forceErrorLog = true;
        this.options = {
            level: this._clientLogLevel,
            serverLogLevel: this._serverLogLevel,
            serverLoggingUrl: '/remotelog.do'
        };
        this._isIE = !!(navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.match(/Trident\//) || navigator.userAgent.match(/Edge\//));
        ;
        if (this.forceErrorLog) {
            this.level = LoggerLevel.ERROR;
        }
    }
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.trace = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level <= LoggerLevel.TRACE ? this._log(LoggerLevel.TRACE, true, message, additional) : null;
    };
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.debug = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level < LoggerLevel.TRACE ? this._log(LoggerLevel.DEBUG, true, message, additional) : null;
    };
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.info = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level < LoggerLevel.DEBUG ? this._log(LoggerLevel.INFO, true, message, additional) : null;
    };
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.log = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level < LoggerLevel.INFO ? this._log(LoggerLevel.LOG, true, message, additional) : null;
    };
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.warn = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level < LoggerLevel.LOG ? this._log(LoggerLevel.WARN, true, message, additional) : null;
    };
    /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    Logger.prototype.error = /**
     * @param {?} message
     * @param {...?} additional
     * @return {?}
     */
    function (message) {
        var additional = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            additional[_i - 1] = arguments[_i];
        }
        this.level < LoggerLevel.WARN ? this._log(LoggerLevel.ERROR, true, message, additional) : null;
    };
    /**
     * @private
     * @return {?}
     */
    Logger.prototype._timestamp = /**
     * @private
     * @return {?}
     */
    function () {
        return new Date().toISOString();
    };
    /**
     * @private
     * @param {?} level
     * @param {?} message
     * @param {?} additional
     * @return {?}
     */
    Logger.prototype._logOnServer = /**
     * @private
     * @param {?} level
     * @param {?} message
     * @param {?} additional
     * @return {?}
     */
    function (level, message, additional) {
        var _this = this;
        if (!this.options.serverLoggingUrl) {
            return;
        }
        // if the user provides a serverLogLevel and the current level is than that do not log
        if (level < this._serverLogLevel) {
            return;
        }
        /** @type {?} */
        var headers = new HttpHeaders().set('Content-Type', 'application/json');
        this.http.post(this.options.serverLoggingUrl, {
            level: Levels[level],
            message: message,
            additional: additional,
            timestamp: this._timestamp()
        }, { headers: headers })
            .subscribe((/**
         * @param {?} res
         * @return {?}
         */
        function (res) { return null; }), (/**
         * @param {?} error
         * @return {?}
         */
        function (error) { return _this._log(LoggerLevel.ERROR, false, 'FAILED TO LOG ON SERVER'); }));
    };
    /**
     * @private
     * @param {?} level
     * @param {?} message
     * @param {?} additional
     * @return {?}
     */
    Logger.prototype._logIE = /**
     * @private
     * @param {?} level
     * @param {?} message
     * @param {?} additional
     * @return {?}
     */
    function (level, message, additional) {
        switch (level) {
            case LoggerLevel.WARN:
                console.warn.apply(console, tslib_1.__spread([this._timestamp() + " [" + Levels[level] + "]  -" + this._class + "-", message], additional));
                break;
            case LoggerLevel.ERROR:
                console.error.apply(console, tslib_1.__spread([this._timestamp() + " [" + Levels[level] + "]  -" + this._class + "-", message], additional));
                break;
            case LoggerLevel.INFO:
                console.info.apply(console, tslib_1.__spread([this._timestamp() + " [" + Levels[level] + "]  -" + this._class + "-", message], additional));
                break;
            default:
                console.log.apply(console, tslib_1.__spread([this._timestamp() + " [" + Levels[level] + "]  -" + this._class + "-", message], additional));
        }
    };
    /**
     * @private
     * @param {?} level
     * @param {?} logOnServer
     * @param {?} message
     * @param {?=} additional
     * @return {?}
     */
    Logger.prototype._log = /**
     * @private
     * @param {?} level
     * @param {?} logOnServer
     * @param {?} message
     * @param {?=} additional
     * @return {?}
     */
    function (level, logOnServer, message, additional) {
        if (additional === void 0) { additional = []; }
        if (!message) {
            return;
        }
        // Allow logging on server even if client log level is off
        if (logOnServer) {
            this._logOnServer(level, message, additional);
        }
        // if no message or the log level is less than the environ
        if (level < this._clientLogLevel) {
            return;
        }
        if (typeof message === 'object') {
            try {
                message = JSON.stringify(message, null, 2);
            }
            catch (e) {
                additional = tslib_1.__spread([message], additional);
                message = 'circular object in message. ';
            }
        }
        // Coloring doesn't work in IE
        if (this._isIE) {
            return this._logIE(level, message, additional);
        }
        /** @type {?} */
        var color = this._getColor(level);
        console.log.apply(console, tslib_1.__spread(["%c" + this._timestamp() + " [" + Levels[level] + "] - " + this._class + " -", "color:" + color, message], additional));
    };
    /**
     * @private
     * @param {?} level
     * @return {?}
     */
    Logger.prototype._getColor = /**
     * @private
     * @param {?} level
     * @return {?}
     */
    function (level) {
        switch (level) {
            case LoggerLevel.TRACE:
                return 'blue';
            case LoggerLevel.DEBUG:
                return 'teal';
            case LoggerLevel.INFO:
            case LoggerLevel.LOG:
                return 'gray';
            case LoggerLevel.WARN:
            case LoggerLevel.ERROR:
                return 'red';
            case LoggerLevel.OFF:
            default:
                return;
        }
    };
    Logger.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    Logger.ctorParameters = function () { return [
        { type: String },
        { type: HttpClient },
        { type: Number }
    ]; };
    return Logger;
}());
export { Logger };
if (false) {
    /**
     * @type {?}
     * @private
     */
    Logger.prototype._clientLogLevel;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype._serverLogLevel;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype._isIE;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype.forceErrorLog;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype.options;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype._class;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype.http;
    /**
     * @type {?}
     * @private
     */
    Logger.prototype.level;
}
var LoggerConfig = /** @class */ (function () {
    function LoggerConfig() {
    }
    return LoggerConfig;
}());
export { LoggerConfig };
if (false) {
    /** @type {?} */
    LoggerConfig.prototype.level;
    /** @type {?} */
    LoggerConfig.prototype.serverLogLevel;
    /** @type {?} */
    LoggerConfig.prototype.serverLoggingUrl;
}
/** @enum {number} */
var LoggerLevel = {
    TRACE: 0, DEBUG: 1, INFO: 2, LOG: 3, WARN: 4, ERROR: 5, OFF: 6,
};
export { LoggerLevel };
LoggerLevel[LoggerLevel.TRACE] = 'TRACE';
LoggerLevel[LoggerLevel.DEBUG] = 'DEBUG';
LoggerLevel[LoggerLevel.INFO] = 'INFO';
LoggerLevel[LoggerLevel.LOG] = 'LOG';
LoggerLevel[LoggerLevel.WARN] = 'WARN';
LoggerLevel[LoggerLevel.ERROR] = 'ERROR';
LoggerLevel[LoggerLevel.OFF] = 'OFF';
/** @type {?} */
var Levels = [
    'TRACE',
    'DEBUG',
    'INFO',
    'LOG',
    'WARN',
    'ERROR',
    'OFF'
];
//# sourceMappingURL=data:application/json;base64,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