/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable, ElementRef } from '@angular/core';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
//@dynamic
var focusManager = /** @class */ (function () {
    function focusManager(element) {
        this.element = element;
    }
    /**
     * @return {?}
     */
    focusManager.getFocus = /**
     * @return {?}
     */
    function () {
        if (this.focusTarget) {
            return { name: this.focusTarget, id: this.focusTarget };
        }
        /** @type {?} */
        var activeElement = $(document.activeElement);
        if (activeElement.length) {
            if ($(activeElement)[0].id == "") {
                return { name: $(activeElement.parent())[0].id, id: $(activeElement.parent())[0].id };
            }
            return { name: $(activeElement)[0].id, id: $(activeElement)[0].id };
        }
    };
    /**
     * @return {?}
     */
    focusManager.getHoverButton = /**
     * @return {?}
     */
    function () {
        if (this.hoverButton) {
            /** @type {?} */
            var activeElement = $(this.hoverButton);
            if (activeElement.length) {
                if ($(activeElement)[0].id == "") {
                    return { name: $(activeElement.parent())[0].id, id: $(activeElement.parent())[0].id };
                }
                return { name: $(activeElement)[0].id, id: $(activeElement)[0].id };
            }
        }
    };
    focusManager.focusTarget = null;
    focusManager.hoverButton = null;
    focusManager.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    focusManager.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    return focusManager;
}());
export { focusManager };
if (false) {
    /** @type {?} */
    focusManager.focusTarget;
    /** @type {?} */
    focusManager.hoverButton;
    /**
     * @type {?}
     * @private
     */
    focusManager.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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