/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class contain all module events
 */
var /**
 * This class contain all module events
 */
Series = /** @class */ (function () {
    function Series() {
        this._yField = "";
        this._name = "";
        this._seriesType = '';
        this._visible = true;
        this._appliedStyle = '';
        this._displayName = '';
        this._legendDisplayName = '';
        this._xField = '';
        this._legendTooltip = '';
        this._minField = '';
        this._highlighted = false;
    }
    Object.defineProperty(Series.prototype, "highlighted", {
        get: /**
         * @return {?}
         */
        function () {
            return this._highlighted;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._highlighted = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "seriesLabel", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesLabel;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesLabel = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "minField", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minField;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minField = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "legendTooltip", {
        get: /**
         * @return {?}
         */
        function () {
            return this._legendTooltip;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._legendTooltip = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "xField", {
        get: /**
         * @return {?}
         */
        function () {
            return this._xField;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._xField = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "displayName", {
        get: /**
         * @return {?}
         */
        function () {
            return this._displayName;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._displayName = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "legendDisplayName", {
        get: /**
         * @return {?}
         */
        function () {
            return this._legendDisplayName;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._legendDisplayName = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "appliedStyle", {
        get: /**
         * @return {?}
         */
        function () {
            return this._appliedStyle;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._appliedStyle = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visible;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._visible = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "seriesType", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesType;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesType = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "yField", {
        get: /**
         * @return {?}
         */
        function () {
            return this._yField;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._yField = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(Series.prototype, "name", {
        get: /**
         * @return {?}
         */
        function () {
            return this._name;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._name = value;
        },
        enumerable: true,
        configurable: true
    });
    return Series;
}());
/**
 * This class contain all module events
 */
export { Series };
if (false) {
    /**
     * @type {?}
     * @private
     */
    Series.prototype._yField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._name;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._seriesType;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._appliedStyle;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._displayName;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._legendDisplayName;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._xField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._legendTooltip;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._minField;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._seriesLabel;
    /**
     * @type {?}
     * @private
     */
    Series.prototype._highlighted;
}
//# sourceMappingURL=data:application/json;base64,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