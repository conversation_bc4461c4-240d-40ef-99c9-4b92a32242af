import { ElementRef, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
export declare class DropDownList implements OnInit, OnDestroy {
    private elementRef;
    static DEFAULT_ITEM_NUMBER: number;
    onItemClick: EventEmitter<any>;
    onItemChange: EventEmitter<any>;
    onClickOutSide: EventEmitter<any>;
    onItemNavigation: EventEmitter<any>;
    highlightIndex: number;
    private dropDownContainer;
    private dropDownItems;
    private virtualScroll;
    private scrollHandler;
    private highlightedItem;
    private pointer;
    private previousPointer;
    isFilterActive: boolean;
    private originalDataSource;
    private firstLoad;
    constructor(elementRef: ElementRef);
    private _activeItemIndex;
    readonly activeItemIndex: number;
    readonly activeItem: any;
    private _selectedIndex;
    selectedIndex: number;
    private _selectedItem;
    selectedItem: any;
    private _selectedLabel;
    selectedLabel: any;
    private _selectedValue;
    selectedValue: any;
    private _dataLabel;
    dataLabel: string;
    private _dataSource;
    dataSource: any[];
    ngOnInit(): void;
    clickoutEventHandler(event: any): void;
    ngOnDestroy(): void;
    onMouseWheelMotion(event: any): void;
    scrollToIndex(index: number): void;
    navigateDown(): void;
    navigateUp(): void;
    validateNow(): void;
    /**
     * This method is used to filter the current
     * list with a given word.
     * @param word
     */
    filter(word: string): void;
    /**
     * This method is used to clear the filter
     */
    clearFilter(): void;
    private applyDataSource;
}
