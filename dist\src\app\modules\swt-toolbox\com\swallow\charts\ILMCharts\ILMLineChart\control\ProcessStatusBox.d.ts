import { OnInit, ElementRef, AfterViewInit } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
export declare class ProcessStatusBox extends Container implements OnInit, AfterViewInit {
    private commonService;
    private element;
    protected image: ElementRef;
    private redIcon;
    private greenImage;
    private amberIcon;
    private inProgress;
    parentDocument: any;
    dataFromXMLParent: any;
    GREEN_STATE: string;
    AMBER_STATE: string;
    RED_STATE: string;
    private customTooltip;
    private recalculateEnable;
    private state;
    private swtAlert;
    dataArray: any[];
    ngOnInit(): void;
    ngAfterViewInit(): void;
    createTooltip(): void;
    removeTooltip(): void;
    setCalculatingState(): void;
    setRed(): void;
    constructor(commonService: CommonService, element: ElementRef);
    setStyleFuction(dataAsXML?: any): void;
}
