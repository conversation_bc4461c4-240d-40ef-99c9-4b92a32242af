/*!* Define default tree icons *!*/
/*.arrowClose {*/
/*width: 8px !important;*/
/*height: 8px !important;*/
/*background: url('../spirites/treeIcons.png') -10px -10px !important;*/
/*}*/

/*.file {*/
/*width: 16px !important;*/
/*height: 16px !important;*/
/*background: url('../spirites/treefile.png');*/
/*}*/

/*!* .folderClosed { *!*/
/*!*     width: 14px !important; height: 12px !important; *!*/
/*!*     background: url('../spirites/treeIcons.png') -157px -10px !important; *!*/
/*!* } *!*/

/*.plus {*/
/*width: 10px !important;*/
/*height: 11px !important;*/
/*background: url('../images/plus.gif') !important;*/
/*}*/

/*.arrowOpen {*/
/*width: 8px !important;*/
/*height: 8px !important;*/
/*background: url('../spirites/treeIcons.png') !important;*/
/*}*/

/*!* .folderOpened { *!*/
/*!*     width: 15px; height: 12px; *!*/
/*!*     background: url('../spirites/treeIcons.png') -191px -10px; *!*/
/*!* } *!*/
/*!* .user { *!*/
/*!*   background: url("~assets/images/userInTree.png") !important; *!*/
/*!* } *!*/
/*.minus {*/
/*width: 10px !important;*/
/*height: 11px !important;*/
/*background: url('../images/minus.gif') !important;*/
/*}*/

/*!* span.fancytree-expander { *!*/
/*!* 	cursor: default !important; *!*/
/*!* 	margin: 4px 0px 4px 6px !important; *!*/
/*!* } *!*/
/*!* .fancytree-plain span.fancytree-node span.fancytree-title { *!*/
/*!*  	background-color: transparent !important;  *!*/
/*!*  	cursor: default !important; *!*/
/*!* } *!*/
/*!* .fancytree-plain span.fancytree-node span.fancytree-title:hover { *!*/
/*!*  	background-color: transparent !important; *!*/
/*!*  	border: 1px solid transparent !important; *!*/
/*!* } *!*/
/*!* #fancytree-drop-marker, span.fancytree-checkbox, *!*/
/*!* span.fancytree-drag-helper-img, span.fancytree-empty, *!*/
/*!* span.fancytree-expander, span.fancytree-icon, span.fancytree-vline { *!*/
/*!* 	display: inline-block  !important; *!*/
/*!* 	vertical-align: middle !important;	 *!*/
/*!* } *!*/
/*!* span.fancytree-node { *!*/
/*!* 	display: inline-block !important; *!*/
/*!* 	height: 23px; *!*/
/*!* 	line-height: 17px; *!*/
/*!* 	border: none !important; *!*/
/*!* } *!*/
/*!* span.fancytree-node:hover { *!*/
/*!* 	background-color: #B2E1FF !important; *!*/
/*!* } *!*/
/*!* span.fancytree-node:hover > span.fancytree-title { *!*/
/*!* 	border: 1px solid transparent !important; *!*/
/*!* } *!*/
/*!* span.fancytree-focused > span.fancytree-title { *!*/
/*!* 	border: 1px solid transparent !important; *!*/
/*!* } *!*/

/*!***************************************************************************************************************/
/**												Tree wide style*/
/***/
/**!*/
/*.fancytree-plain span.fancytree-node span.fancytree-title {*/
/*line-height: 22px;*/
/*}*/

/*.fancytree-plain span.fancytree-node:hover span.fancytree-title {*/
/*background-color: #B2E1FF !important;*/
/*border: 1px solid #B2E1FF !important;*/
/*}*/

/*.fancytree-plain span.fancytree-active span.fancytree-title,*/
/*.fancytree-plain span.fancytree-selected span.fancytree-title {*/
/*background-color: #7FCEFF !important;*/
/*border: 1px solid #7FCEFF !important;*/
/*}*/

/*span.fancytree-node.fancytree-folder > span.fancytree-icon {*/
/*width: 16px !important;*/
/*height: 16px;*/
/*background: url('../spirites/treefldrclsd.png');*/
/*}*/

/*span.fancytree-node.fancytree-folder.fancytree-expanded > span.fancytree-icon {*/
/*width: 16px !important;*/
/*height: 16px;*/
/*background: url('../spirites/treefldropn.png');*/
/*}*/

/*!********************************************************!*/

/*span.fancytree-node.fancytree-folder > span.fancytree-expander {*/
/*width: 8px;*/
/*height: 8px;*/
/*background: url('../spirites/treeIcons.png') -10px -10px;*/
/*}*/

/*span.fancytree-node.fancytree-folder.fancytree-expanded > span.fancytree-expander {*/
/*width: 8px;*/
/*height: 8px;*/
/*background: url('../spirites/treeIcons.png') -38px -10px;*/
/*}*/

/*span.fancytree-expander {*/
/*margin-top: 4px !important;*/
/*margin-left: 4px !important;*/
/*position: relative;*/
/*top: 4px;*/
/*}*/

/*!********************************************************!*/
/*span.fancytree-title, span.fancytree-expander, span.fancytree-icon {*/
/*cursor: default !important;*/
/*}*/

/*ul.fancytree-container {*/
/*padding: 0px !important;*/
/*overflow-x: hidden;*/
/*}*/
