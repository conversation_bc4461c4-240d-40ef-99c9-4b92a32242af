/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
var Browsers = /** @class */ (function () {
    function Browsers() {
    }
    // Opera 8.0+
    Browsers.FIREFOX = "firefox";
    // Firefox 1.0+
    Browsers.CHROME = "chrome";
    // Safari 3.0+ "[object HTMLElementConstructor]"
    Browsers.SAFARI = "safari";
    // Internet Explorer 6-11
    Browsers.OPERA = "opera";
    // Edge 20+
    Browsers.IE = "ie";
    // Chrome 1 - 71
    Browsers.EDGE = "edge";
    // unknown browser type.
    Browsers.UNKNOWN_BROWSER = "unknown_browser";
    return Browsers;
}());
export { Browsers };
if (false) {
    /** @type {?} */
    Browsers.FIREFOX;
    /** @type {?} */
    Browsers.CHROME;
    /** @type {?} */
    Browsers.SAFARI;
    /** @type {?} */
    Browsers.OPERA;
    /** @type {?} */
    Browsers.IE;
    /** @type {?} */
    Browsers.EDGE;
    /** @type {?} */
    Browsers.UNKNOWN_BROWSER;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQnJvd3NlcnMuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvbW9kZWwvQnJvd3NlcnMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0lBQUE7SUFlQSxDQUFDOztJQWJpQixnQkFBTyxHQUFHLFNBQVMsQ0FBQzs7SUFFcEIsZUFBTSxHQUFHLFFBQVEsQ0FBQzs7SUFFbEIsZUFBTSxHQUFHLFFBQVEsQ0FBQzs7SUFFbEIsY0FBSyxHQUFHLE9BQU8sQ0FBQzs7SUFFaEIsV0FBRSxHQUFHLElBQUksQ0FBQzs7SUFFVixhQUFJLEdBQUcsTUFBTSxDQUFDOztJQUVkLHdCQUFlLEdBQUcsaUJBQWlCLENBQUM7SUFDdEQsZUFBQztDQUFBLEFBZkQsSUFlQztTQWZZLFFBQVE7OztJQUVqQixpQkFBa0M7O0lBRWxDLGdCQUFnQzs7SUFFaEMsZ0JBQWdDOztJQUVoQyxlQUE4Qjs7SUFFOUIsWUFBd0I7O0lBRXhCLGNBQTRCOztJQUU1Qix5QkFBa0QiLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgQnJvd3NlcnMge1xyXG4gICAgLy8gT3BlcmEgOC4wK1xyXG4gICAgcHVibGljIHN0YXRpYyBGSVJFRk9YID0gXCJmaXJlZm94XCI7XHJcbiAgICAvLyBGaXJlZm94IDEuMCtcclxuICAgIHB1YmxpYyBzdGF0aWMgQ0hST01FID0gXCJjaHJvbWVcIjtcclxuICAgIC8vIFNhZmFyaSAzLjArIFwiW29iamVjdCBIVE1MRWxlbWVudENvbnN0cnVjdG9yXVwiXHJcbiAgICBwdWJsaWMgc3RhdGljIFNBRkFSSSA9IFwic2FmYXJpXCI7XHJcbiAgICAvLyBJbnRlcm5ldCBFeHBsb3JlciA2LTExXHJcbiAgICBwdWJsaWMgc3RhdGljIE9QRVJBID0gXCJvcGVyYVwiO1xyXG4gICAgLy8gRWRnZSAyMCtcclxuICAgIHB1YmxpYyBzdGF0aWMgSUUgPSBcImllXCI7XHJcbiAgICAvLyBDaHJvbWUgMSAtIDcxXHJcbiAgICBwdWJsaWMgc3RhdGljIEVER0UgPSBcImVkZ2VcIjtcclxuICAgIC8vIHVua25vd24gYnJvd3NlciB0eXBlLlxyXG4gICAgcHVibGljIHN0YXRpYyBVTktOT1dOX0JST1dTRVIgPSBcInVua25vd25fYnJvd3NlclwiO1xyXG59XHJcbiJdfQ==