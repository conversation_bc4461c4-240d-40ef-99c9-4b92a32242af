/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, ViewChild, ElementRef, ViewContainerRef, Renderer2 } from "@angular/core";
import { focusManager } from "../managers/focus-manager.service";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
/** @type {?} */
var $ = require('jquery');
var SwtPanel = /** @class */ (function (_super) {
    tslib_1.__extends(SwtPanel, _super);
    function SwtPanel(elem, commonService, _renderer) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._renderer = _renderer;
        _this._title = "";
        return _this;
    }
    /**
     * addChild : append a dynamically created component.
     * @param type : Component type
     */
    /**
     * addChild : append a dynamically created component.
     * @param {?} type : Component type
     * @return {?}
     */
    SwtPanel.prototype.addChild = /**
     * addChild : append a dynamically created component.
     * @param {?} type : Component type
     * @return {?}
     */
    function (type) {
        /** @type {?} */
        var comp = this.commonService.componentFactoryResolver.resolveComponentFactory(type);
        /** @type {?} */
        var componentInstance = this.container.createComponent(comp);
        /* Push the component so that we can keep track of which components are created */
        this.components.push(componentInstance);
        $(componentInstance.instance).attr('id', "dynamic-" + Math.random().toString(36).substr(2, 5));
        return (componentInstance.instance);
    };
    /**
     * @return {?}
     */
    SwtPanel.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        $(this.elem.nativeElement.children[0]).off('click.' + this.id);
    };
    /**
     * @return {?}
     */
    SwtPanel.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        /*
         * when mouse hover to panel a default toolTip appear.
         * this line will remove this behavior.
         * */
        $(this.elem.nativeElement).attr("title", null);
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtPanel');
        // - Setting Title.
        if (this.title) {
            /** @type {?} */
            var panelTitle = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]).find('.panelTitle') : null;
            if (panelTitle.length > 0) {
                $(panelTitle).removeClass('hidden');
                $(panelTitle).text(String(this.title));
            }
        }
        // - Setting StyleName.
        if (this.styleName) {
            /** @type {?} */
            var panel = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]) : null;
            if (panel.length > 0) {
                $(panel).addClass(this.styleName);
            }
        }
        console.log('panel there');
        // update focus Manager data (focused element)
        $(this.elem.nativeElement.children[0]).on('click.' + this.id, (/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            focusManager.focusTarget = _this.id;
        }));
    };
    Object.defineProperty(SwtPanel.prototype, "styleName", {
        get: /**
         * @return {?}
         */
        function () {
            return this._styleName;
        },
        /*-----------------------------------*/
        set: /*-----------------------------------*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._styleName = String(value);
            /** @type {?} */
            var panel = $(this.elem.nativeElement) ? $($(this.elem.nativeElement).children()[0]) : null;
            if (panel.length > 0) {
                $(panel).addClass(this._styleName);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPanel.prototype, "title", {
        get: /**
         * @return {?}
         */
        function () {
            return this._title;
        },
        /*-----------------------------------*/
        set: /*-----------------------------------*/
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._title = String(value);
            /** @type {?} */
            var panelTitle = $($(this.elem.nativeElement).children()[0]).find('.panelTitle');
            if (panelTitle.length > 0 && this._title) {
                $(panelTitle).removeClass('hidden');
                $(panelTitle).text(String(value));
            }
        },
        enumerable: true,
        configurable: true
    });
    SwtPanel.decorators = [
        { type: Component, args: [{
                    selector: 'SwtPanel',
                    template: "\n    <div fxLayout=\"column\"  class=\"panelLayout panelInsideFormLayout\" tabindex=\"-1\" >\n         <div class=\"panelTitle hidden\" tabindex=\"-1\"> </div>\n         <div fxLayout=\"column\" fxLayoutAlign=\"{{verticalAlign}} {{horizontalAlign}}\"  fxLayoutGap=\"{{verticalGap}}\"   class=\"panelBody\" tabindex=\"-1\">\n             <ng-content ></ng-content>\n             <ng-container #container></ng-container> \n        </div>\n    </div>\n  ",
                    styles: ["\n             :host {\n               margin: 0 0px 5px 0;\n               display: block;\n             }\n             .panelLayout {\n               box-sizing: border-box;\n               width: 100%;\n               height: 100%;\n               outline: none;\n             }\n   "]
                }] }
    ];
    /** @nocollapse */
    SwtPanel.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    SwtPanel.propDecorators = {
        container: [{ type: ViewChild, args: ['container', { read: ViewContainerRef },] }],
        styleName: [{ type: Input, args: ['styleName',] }],
        title: [{ type: Input, args: ["title",] }]
    };
    return SwtPanel;
}(Container));
export { SwtPanel };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._title;
    /** @type {?} */
    SwtPanel.prototype.container;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtPanel.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXBhbmVsLmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9zd3QtcGFuZWwuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFVLFVBQVUsRUFBRSxnQkFBZ0IsRUFBRSxTQUFTLEVBQWEsTUFBTSxlQUFlLENBQUM7QUFDeEgsT0FBTyxFQUFFLFlBQVksRUFBRSxNQUFNLG1DQUFtQyxDQUFDO0FBQ2pFLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx1Q0FBdUMsQ0FBQztBQUNsRSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7O0lBRWxELENBQUMsR0FBRyxPQUFPLENBQUUsUUFBUSxDQUFFO0FBRTdCO0lBd0I4QixvQ0FBUztJQVFuQyxrQkFBcUIsSUFBZ0IsRUFBVSxhQUE0QixFQUFVLFNBQW9CO1FBQXpHLFlBQ0ksa0JBQU8sSUFBSSxFQUFFLGFBQWEsQ0FBRSxTQUMvQjtRQUZvQixVQUFJLEdBQUosSUFBSSxDQUFZO1FBQVUsbUJBQWEsR0FBYixhQUFhLENBQWU7UUFBVSxlQUFTLEdBQVQsU0FBUyxDQUFXO1FBTGpHLFlBQU0sR0FBVyxFQUFFLENBQUM7O0lBTzVCLENBQUM7SUFDRDs7O09BR0c7Ozs7OztJQUNILDJCQUFROzs7OztJQUFSLFVBQVUsSUFBSTs7WUFDTixJQUFJLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyx3QkFBd0IsQ0FBQyx1QkFBdUIsQ0FBRSxJQUFJLENBQUU7O1lBQ2xGLGlCQUFpQixHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsZUFBZSxDQUFFLElBQUksQ0FBRTtRQUM5RCxrRkFBa0Y7UUFDbEYsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUUsaUJBQWlCLENBQUUsQ0FBQztRQUMxQyxDQUFDLENBQUUsaUJBQWlCLENBQUMsUUFBUSxDQUFFLENBQUMsSUFBSSxDQUFFLElBQUksRUFBRSxVQUFVLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLFFBQVEsQ0FBRSxFQUFFLENBQUUsQ0FBQyxNQUFNLENBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBRSxDQUFFLENBQUM7UUFDdkcsT0FBTyxDQUFFLGlCQUFpQixDQUFDLFFBQVEsQ0FBRSxDQUFDO0lBQzFDLENBQUM7Ozs7SUFHRCw4QkFBVzs7O0lBQVg7UUFDSSxDQUFDLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFFLENBQUMsR0FBRyxDQUFDLFFBQVEsR0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDbkUsQ0FBQzs7OztJQUNELDJCQUFROzs7SUFBUjtRQUFBLGlCQTZCQztRQTVCRzs7O2FBR0s7UUFDSixDQUFDLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUUsQ0FBQyxJQUFJLENBQUUsT0FBTyxFQUFFLElBQUksQ0FBRSxDQUFDO1FBQ25ELENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsVUFBVSxDQUFDLENBQUM7UUFFOUQsbUJBQW1CO1FBQ25CLElBQUcsSUFBSSxDQUFDLEtBQUssRUFBQzs7Z0JBQ04sVUFBVSxHQUFJLENBQUMsQ0FBRSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUk7WUFDdkgsSUFBRyxVQUFVLENBQUMsTUFBTSxHQUFFLENBQUMsRUFBQztnQkFDcEIsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDcEMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7YUFDMUM7U0FDSjtRQUNELHVCQUF1QjtRQUN2QixJQUFHLElBQUksQ0FBQyxTQUFTLEVBQUM7O2dCQUNWLEtBQUssR0FBRyxDQUFDLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBRSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUk7WUFDOUYsSUFBRyxLQUFLLENBQUMsTUFBTSxHQUFFLENBQUMsRUFBQztnQkFDZixDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQzthQUNyQztTQUNKO1FBQ0QsT0FBTyxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUMsQ0FBQTtRQUMzQiw4Q0FBOEM7UUFDOUMsQ0FBQyxDQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBRSxDQUFDLEVBQUUsQ0FBQyxRQUFRLEdBQUMsSUFBSSxDQUFDLEVBQUU7Ozs7UUFBRSxVQUFDLEtBQUs7WUFDaEUsWUFBWSxDQUFDLFdBQVcsR0FBRyxLQUFJLENBQUMsRUFBRSxDQUFDO1FBQ3ZDLENBQUMsRUFBRSxDQUFDO0lBRVIsQ0FBQztJQUdELHNCQUNJLCtCQUFTOzs7O1FBT2I7WUFFSSxPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7UUFDM0IsQ0FBQztRQVpELHVDQUF1Qzs7Ozs7O1FBQ3ZDLFVBQ2UsS0FBYTtZQUN4QixJQUFJLENBQUMsVUFBVSxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQzs7Z0JBQzVCLEtBQUssR0FBRyxDQUFDLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBRSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUk7WUFDOUYsSUFBRyxLQUFLLENBQUMsTUFBTSxHQUFFLENBQUMsRUFBQztnQkFDZixDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQzthQUN0QztRQUNMLENBQUM7OztPQUFBO0lBT0Qsc0JBQ0ksMkJBQUs7Ozs7UUFTVDtZQUNJLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUN2QixDQUFDO1FBYkQsdUNBQXVDOzs7Ozs7UUFDdkMsVUFDVyxLQUFhO1lBQ3BCLElBQUksQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDOztnQkFDeEIsVUFBVSxHQUFJLENBQUMsQ0FBQyxDQUFDLENBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUM7WUFDbEYsSUFBRyxVQUFVLENBQUMsTUFBTSxHQUFFLENBQUMsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFO2dCQUNwQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO2dCQUNwQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO2FBQ3JDO1FBRUwsQ0FBQzs7O09BQUE7O2dCQTNHSixTQUFTLFNBQUU7b0JBQ1IsUUFBUSxFQUFFLFVBQVU7b0JBQ3BCLFFBQVEsRUFBRSxzY0FRWDs2QkFDVSxpU0FXVDtpQkFDSDs7OztnQkE5QjZDLFVBQVU7Z0JBRy9DLGFBQWE7Z0JBSHNELFNBQVM7Ozs0QkFvQ2hGLFNBQVMsU0FBRSxXQUFXLEVBQUUsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUU7NEJBdURsRCxLQUFLLFNBQUUsV0FBVzt3QkFjbEIsS0FBSyxTQUFFLE9BQU87O0lBY25CLGVBQUM7Q0FBQSxBQWhIRCxDQXdCOEIsU0FBUyxHQXdGdEM7U0F4RlksUUFBUTs7Ozs7O0lBRWpCLDhCQUEyQjs7Ozs7SUFDM0IsMEJBQTRCOztJQUU1Qiw2QkFBa0Y7Ozs7O0lBR3JFLHdCQUF3Qjs7Ozs7SUFBRSxpQ0FBb0M7Ozs7O0lBQUUsNkJBQTRCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29tcG9uZW50LCBJbnB1dCwgVmlld0NoaWxkLCBPbkluaXQsIEVsZW1lbnRSZWYsIFZpZXdDb250YWluZXJSZWYsIFJlbmRlcmVyMiwgT25EZXN0cm95IH0gZnJvbSBcIkBhbmd1bGFyL2NvcmVcIjtcclxuaW1wb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSBcIi4uL21hbmFnZXJzL2ZvY3VzLW1hbmFnZXIuc2VydmljZVwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudFwiO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XHJcbmRlY2xhcmUgdmFyIHJlcXVpcmU6IGFueTtcclxuY29uc3QgJCA9IHJlcXVpcmUoICdqcXVlcnknICk7XHJcblxyXG5AQ29tcG9uZW50KCB7XHJcbiAgICBzZWxlY3RvcjogJ1N3dFBhbmVsJyxcclxuICAgIHRlbXBsYXRlOiBgXHJcbiAgICA8ZGl2IGZ4TGF5b3V0PVwiY29sdW1uXCIgIGNsYXNzPVwicGFuZWxMYXlvdXQgcGFuZWxJbnNpZGVGb3JtTGF5b3V0XCIgdGFiaW5kZXg9XCItMVwiID5cclxuICAgICAgICAgPGRpdiBjbGFzcz1cInBhbmVsVGl0bGUgaGlkZGVuXCIgdGFiaW5kZXg9XCItMVwiPiA8L2Rpdj5cclxuICAgICAgICAgPGRpdiBmeExheW91dD1cImNvbHVtblwiIGZ4TGF5b3V0QWxpZ249XCJ7e3ZlcnRpY2FsQWxpZ259fSB7e2hvcml6b250YWxBbGlnbn19XCIgIGZ4TGF5b3V0R2FwPVwie3t2ZXJ0aWNhbEdhcH19XCIgICBjbGFzcz1cInBhbmVsQm9keVwiIHRhYmluZGV4PVwiLTFcIj5cclxuICAgICAgICAgICAgIDxuZy1jb250ZW50ID48L25nLWNvbnRlbnQ+XHJcbiAgICAgICAgICAgICA8bmctY29udGFpbmVyICNjb250YWluZXI+PC9uZy1jb250YWluZXI+IFxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgYCxcclxuICAgIHN0eWxlczogW2BcclxuICAgICAgICAgICAgIDpob3N0IHtcclxuICAgICAgICAgICAgICAgbWFyZ2luOiAwIDBweCA1cHggMDtcclxuICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAucGFuZWxMYXlvdXQge1xyXG4gICAgICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgICAgICAgfVxyXG4gICBgXSxcclxufSApXHJcbmV4cG9ydCBjbGFzcyBTd3RQYW5lbCBleHRlbmRzIENvbnRhaW5lciBpbXBsZW1lbnRzIE9uSW5pdCwgT25EZXN0cm95IHtcclxuXHJcbiAgICBwcml2YXRlIF9zdHlsZU5hbWU6IHN0cmluZztcclxuICAgIHByaXZhdGUgX3RpdGxlOiBzdHJpbmcgPSBcIlwiO1xyXG5cclxuICAgIEBWaWV3Q2hpbGQoICdjb250YWluZXInLCB7IHJlYWQ6IFZpZXdDb250YWluZXJSZWYgfSApIGNvbnRhaW5lcjogVmlld0NvbnRhaW5lclJlZjtcclxuXHJcblxyXG4gICAgY29uc3RydWN0b3IoIHByaXZhdGUgZWxlbTogRWxlbWVudFJlZiwgcHJpdmF0ZSBjb21tb25TZXJ2aWNlOiBDb21tb25TZXJ2aWNlLCBwcml2YXRlIF9yZW5kZXJlcjogUmVuZGVyZXIyICkge1xyXG4gICAgICAgIHN1cGVyKCBlbGVtLCBjb21tb25TZXJ2aWNlICk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIGFkZENoaWxkIDogYXBwZW5kIGEgZHluYW1pY2FsbHkgY3JlYXRlZCBjb21wb25lbnQuXHJcbiAgICAgKiBAcGFyYW0gdHlwZSA6IENvbXBvbmVudCB0eXBlIFxyXG4gICAgICovXHJcbiAgICBhZGRDaGlsZCggdHlwZSApIHtcclxuICAgICAgICB2YXIgY29tcCA9IHRoaXMuY29tbW9uU2VydmljZS5jb21wb25lbnRGYWN0b3J5UmVzb2x2ZXIucmVzb2x2ZUNvbXBvbmVudEZhY3RvcnkoIHR5cGUgKTtcclxuICAgICAgICB2YXIgY29tcG9uZW50SW5zdGFuY2UgPSB0aGlzLmNvbnRhaW5lci5jcmVhdGVDb21wb25lbnQoIGNvbXAgKTtcclxuICAgICAgICAvKiBQdXNoIHRoZSBjb21wb25lbnQgc28gdGhhdCB3ZSBjYW4ga2VlcCB0cmFjayBvZiB3aGljaCBjb21wb25lbnRzIGFyZSBjcmVhdGVkICovXHJcbiAgICAgICAgdGhpcy5jb21wb25lbnRzLnB1c2goIGNvbXBvbmVudEluc3RhbmNlICk7XHJcbiAgICAgICAgJCggY29tcG9uZW50SW5zdGFuY2UuaW5zdGFuY2UgKS5hdHRyKCAnaWQnLCBcImR5bmFtaWMtXCIgKyBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKCAzNiApLnN1YnN0ciggMiwgNSApICk7XHJcbiAgICAgICAgcmV0dXJuICggY29tcG9uZW50SW5zdGFuY2UuaW5zdGFuY2UgKTtcclxuICAgIH1cclxuXHJcblxyXG4gICAgbmdPbkRlc3Ryb3koKXtcclxuICAgICAgICAkKCB0aGlzLmVsZW0ubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXSApLm9mZignY2xpY2suJyt0aGlzLmlkKTtcclxuICAgIH1cclxuICAgIG5nT25Jbml0KCkge1xyXG4gICAgICAgIC8qXHJcbiAgICAgICAgICogd2hlbiBtb3VzZSBob3ZlciB0byBwYW5lbCBhIGRlZmF1bHQgdG9vbFRpcCBhcHBlYXIuXHJcbiAgICAgICAgICogdGhpcyBsaW5lIHdpbGwgcmVtb3ZlIHRoaXMgYmVoYXZpb3IuIFxyXG4gICAgICAgICAqICovXHJcbiAgICAgICAgICQoIHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50ICkuYXR0ciggXCJ0aXRsZVwiLCBudWxsICk7XHJcbiAgICAgICAgICQoJCh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudClbMF0pLmF0dHIoJ3NlbGVjdG9yJywgJ1N3dFBhbmVsJyk7XHJcblxyXG4gICAgICAgICAvLyAtIFNldHRpbmcgVGl0bGUuXHJcbiAgICAgICAgIGlmKHRoaXMudGl0bGUpe1xyXG4gICAgICAgICAgICAgdmFyIHBhbmVsVGl0bGUgPSAgJCggdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpID8gJCgkKCB0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCkuY2hpbGRyZW4oKVswXSkuZmluZCgnLnBhbmVsVGl0bGUnKSA6IG51bGw7XHJcbiAgICAgICAgICAgICBpZihwYW5lbFRpdGxlLmxlbmd0aD4gMCl7XHJcbiAgICAgICAgICAgICAgICAgJChwYW5lbFRpdGxlKS5yZW1vdmVDbGFzcygnaGlkZGVuJyk7XHJcbiAgICAgICAgICAgICAgICAgJChwYW5lbFRpdGxlKS50ZXh0KFN0cmluZyh0aGlzLnRpdGxlKSk7XHJcbiAgICAgICAgICAgICB9XHJcbiAgICAgICAgIH0gXHJcbiAgICAgICAgIC8vIC0gU2V0dGluZyBTdHlsZU5hbWUuXHJcbiAgICAgICAgIGlmKHRoaXMuc3R5bGVOYW1lKXtcclxuICAgICAgICAgICAgIHZhciBwYW5lbCA9ICQoIHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KSA/ICAkKCQoIHRoaXMuZWxlbS5uYXRpdmVFbGVtZW50KS5jaGlsZHJlbigpWzBdKSA6IG51bGw7XHJcbiAgICAgICAgICAgICBpZihwYW5lbC5sZW5ndGg+IDApe1xyXG4gICAgICAgICAgICAgICAgICQocGFuZWwpLmFkZENsYXNzKHRoaXMuc3R5bGVOYW1lKTtcclxuICAgICAgICAgICAgIH1cclxuICAgICAgICAgfSBcclxuICAgICAgICAgY29uc29sZS5sb2coJ3BhbmVsIHRoZXJlJylcclxuICAgICAgICAvLyB1cGRhdGUgZm9jdXMgTWFuYWdlciBkYXRhIChmb2N1c2VkIGVsZW1lbnQpXHJcbiAgICAgICAgJCggdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0gKS5vbignY2xpY2suJyt0aGlzLmlkLCAoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgZm9jdXNNYW5hZ2VyLmZvY3VzVGFyZ2V0ID0gdGhpcy5pZDtcclxuICAgICAgICB9ICk7XHJcblxyXG4gICAgfVxyXG5cclxuICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG4gICAgQElucHV0KCAnc3R5bGVOYW1lJyApXHJcbiAgICBzZXQgc3R5bGVOYW1lKCB2YWx1ZTogc3RyaW5nICkge1xyXG4gICAgICAgIHRoaXMuX3N0eWxlTmFtZSA9IFN0cmluZyh2YWx1ZSk7XHJcbiAgICAgICAgdmFyIHBhbmVsID0gJCggdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpID8gICQoJCggdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pIDogbnVsbDtcclxuICAgICAgICBpZihwYW5lbC5sZW5ndGg+IDApe1xyXG4gICAgICAgICAgICAkKHBhbmVsKS5hZGRDbGFzcyh0aGlzLl9zdHlsZU5hbWUpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGdldCBzdHlsZU5hbWUoKSB7XHJcbiAgICAgIFxyXG4gICAgICAgIHJldHVybiB0aGlzLl9zdHlsZU5hbWU7XHJcbiAgICB9XHJcbiAgIFxyXG4gICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXHJcbiAgICBASW5wdXQoIFwidGl0bGVcIiApXHJcbiAgICBzZXQgdGl0bGUoIHZhbHVlOiBzdHJpbmcgKSB7XHJcbiAgICAgICAgdGhpcy5fdGl0bGUgPSBTdHJpbmcodmFsdWUpO1xyXG4gICAgICAgIHZhciBwYW5lbFRpdGxlID0gICQoJCggdGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpLmNoaWxkcmVuKClbMF0pLmZpbmQoJy5wYW5lbFRpdGxlJykgO1xyXG4gICAgICAgIGlmKHBhbmVsVGl0bGUubGVuZ3RoPiAwICYmIHRoaXMuX3RpdGxlICl7XHJcbiAgICAgICAgICAgICQocGFuZWxUaXRsZSkucmVtb3ZlQ2xhc3MoJ2hpZGRlbicpO1xyXG4gICAgICAgICAgICAkKHBhbmVsVGl0bGUpLnRleHQoU3RyaW5nKHZhbHVlKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgfVxyXG4gICAgZ2V0IHRpdGxlKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl90aXRsZTtcclxuICAgIH1cclxuICAgIFxyXG59Il19