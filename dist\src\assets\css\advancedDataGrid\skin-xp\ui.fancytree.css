/*!
 * Fancytree "XP" skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */
/*******************************************************************************
 * Common Styles for Fancytree Skins.
 *
 * This section is automatically generated from the `skin-common.less` template.
 *
 * Copyright (c) 2008-2019, <PERSON> (http://wwWendt.de)
 * Released under the MIT license
 * https://github.com/mar10/fancytree/wiki/LicenseInfo
 *
 * @version 2.30.2
 * @date 2019-01-13T08:17:01Z
******************************************************************************/
/*------------------------------------------------------------------------------
 * Helpers
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem .fancytree-helper-hidden {
  display: none;
}
.advancedTreeDivItem .fancytree-helper-indeterminate-cb {
  color: #777;
}
.advancedTreeDivItem .fancytree-helper-disabled {
  color: #c0c0c0;
}
/* Helper to allow spinning loader icon with glyph-, ligature-, and SVG-icons. */
.advancedTreeDivItem .fancytree-helper-spin {
  -webkit-animation: spin 1000ms infinite linear;
  animation: spin 1000ms infinite linear;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/*------------------------------------------------------------------------------
 * Container and UL / LI
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem  ul.fancytree-container {
  font-family: tahoma, arial, helvetica;
  font-size: 10pt;
  white-space: nowrap;
  padding: 3px;
  margin: 0;
  background-color: white;
  border: 1px dotted gray;
  min-height: 0%;
  position: relative;
}
.advancedTreeDivItem ul.fancytree-container ul {
  padding: 0 0 0 16px;
  margin: 0;
}
.advancedTreeDivItem ul.fancytree-container ul > li:before {
  content: none;
}
.advancedTreeDivItem ul.fancytree-container li {
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  -moz-background-clip: border;
  -moz-background-inline-policy: continuous;
  -moz-background-origin: padding;
  background-attachment: scroll;
  background-color: transparent;
  background-position: 0px 0px;
  background-repeat: repeat-y;
  background-image: none;
  margin: 0;
}
.advancedTreeDivItem ul.fancytree-container li.fancytree-lastsib {
  background-image: none;
}
.advancedTreeDivItem .ui-fancytree-disabled ul.fancytree-container {
  opacity: 0.5;
  background-color: silver;
}
.advancedTreeDivItem ul.fancytree-connectors.fancytree-container li {
  background-image: url("../skin-xp/vline.gif");
  background-position: 0 0;
}
.advancedTreeDivItem ul.fancytree-container li.fancytree-lastsib,
.advancedTreeDivItem ul.fancytree-no-connector > li {
  background-image: none;
}
.advancedTreeDivItem li.fancytree-animating {
  position: relative;
}
/*------------------------------------------------------------------------------
 * Common icon definitions
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem span.fancytree-empty,
.advancedTreeDivItem span.fancytree-vline,
.advancedTreeDivItem span.fancytree-expander,
.advancedTreeDivItem span.fancytree-icon,
.advancedTreeDivItem span.fancytree-checkbox,
.advancedTreeDivItem span.fancytree-drag-helper-img,
.advancedTreeDivItem #fancytree-drop-marker {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: top;
  background-repeat: no-repeat;
  background-position: left;
  background-image: url("../skin-xp/micons.gif");
  background-position: 0px 0px;
}
.advancedTreeDivItem span.fancytree-icon,
.advancedTreeDivItem span.fancytree-checkbox,
.advancedTreeDivItem span.fancytree-expander,
.advancedTreeDivItem span.fancytree-custom-icon {
  margin-top: 0px;
}
/* Used by icon option: */
.advancedTreeDivItem span.fancytree-custom-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-left: 3px;
  background-position: 0px 0px;
}
/* Used by 'icon' node option: */
.advancedTreeDivItem img.fancytree-icon {
  width: 16px;
  height: 16px;
  margin-left: 3px;
  margin-top: 0px;
  vertical-align: top;
  border-style: none;
}
/*------------------------------------------------------------------------------
 * Expander icon
 *
 * Note: IE6 doesn't correctly evaluate multiples class names,
 *		 so we create combined class names that can be used in the CSS.
 *
 * Prefix: fancytree-exp-
 * 1st character: 'e': expanded, 'c': collapsed, 'n': no children
 * 2nd character (optional): 'd': lazy (Delayed)
 * 3rd character (optional): 'l': Last sibling
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem span.fancytree-expander {
  cursor: default;
}
.advancedTreeDivItem .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-nl span.fancytree-expander {
  background-image: none;
  cursor: default;
}
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-nl span.fancytree-expander {
  background-image: url("../skin-xp/micons.gif");
  margin-top: 0;
}
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-n span.fancytree-expander:hover {
  background-position: 0px -64px;
}
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-nl span.fancytree-expander,
.advancedTreeDivItem .fancytree-connectors .fancytree-exp-nl span.fancytree-expander:hover {
  background-position: -16px -64px;
}
.advancedTreeDivItem .fancytree-exp-c span.fancytree-expander {
  background-position: 0px -80px;
}
.advancedTreeDivItem .fancytree-exp-c span.fancytree-expander:hover {
  background-position: -16px -80px;
}
.advancedTreeDivItem .fancytree-exp-cl span.fancytree-expander {
  background-position: 0px -96px;
}
.advancedTreeDivItem .fancytree-exp-cl span.fancytree-expander:hover {
  background-position: -16px -96px;
}
.advancedTreeDivItem .fancytree-exp-cd span.fancytree-expander {
  background-position: -64px -80px;
}
.advancedTreeDivItem .fancytree-exp-cd span.fancytree-expander:hover {
  background-position: -80px -80px;
}
.advancedTreeDivItem .fancytree-exp-cdl span.fancytree-expander {
  background-position: -64px -96px;
}
.advancedTreeDivItem .fancytree-exp-cdl span.fancytree-expander:hover {
  background-position: -80px -96px;
}
.advancedTreeDivItem .fancytree-exp-e span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-ed span.fancytree-expander {
  background-position: -32px -80px;
}
.advancedTreeDivItem .fancytree-exp-e span.fancytree-expander:hover,
.advancedTreeDivItem .fancytree-exp-ed span.fancytree-expander:hover {
  background-position: -48px -80px;
}
.advancedTreeDivItem .fancytree-exp-el span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-edl span.fancytree-expander {
  background-position: -32px -96px;
}
.advancedTreeDivItem .fancytree-exp-el span.fancytree-expander:hover,
.advancedTreeDivItem .fancytree-exp-edl span.fancytree-expander:hover {
  background-position: -48px -96px;
}
/* Fade out expanders, when container is not hovered or active */
.advancedTreeDivItem .fancytree-fade-expander span.fancytree-expander {
  transition: opacity 1.5s;
  opacity: 0;
}
.advancedTreeDivItem .fancytree-fade-expander:hover span.fancytree-expander,
.advancedTreeDivItem .fancytree-fade-expander.fancytree-treefocus span.fancytree-expander,
.advancedTreeDivItem .fancytree-fade-expander .fancytree-treefocus span.fancytree-expander,
.advancedTreeDivItem .fancytree-fade-expander [class*='fancytree-statusnode-'] span.fancytree-expander {
  transition: opacity 0.6s;
  opacity: 1;
}
/*------------------------------------------------------------------------------
 * Checkbox icon
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem span.fancytree-checkbox {
  margin-left: 3px;
  background-position: 0px -32px;
}
.advancedTreeDivItem span.fancytree-checkbox:hover {
  background-position: -16px -32px;
}
.advancedTreeDivItem span.fancytree-checkbox.fancytree-radio {
  background-position: 0px -48px;
}
.advancedTreeDivItem span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -16px -48px;
}
.advancedTreeDivItem .fancytree-partsel span.fancytree-checkbox {
  background-position: -64px -32px;
}
.advancedTreeDivItem .fancytree-partsel span.fancytree-checkbox:hover {
  background-position: -80px -32px;
}
.advancedTreeDivItem .fancytree-partsel span.fancytree-checkbox.fancytree-radio {
  background-position: -64px -48px;
}
.advancedTreeDivItem .fancytree-partsel span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -80px -48px;
}
.advancedTreeDivItem .fancytree-selected span.fancytree-checkbox {
  background-position: -32px -32px;
}
.advancedTreeDivItem .fancytree-selected span.fancytree-checkbox:hover {
  background-position: -48px -32px;
}
.advancedTreeDivItem .fancytree-selected span.fancytree-checkbox.fancytree-radio {
  background-position: -32px -48px;
}
.advancedTreeDivItem .fancytree-selected span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -48px -48px;
}
.advancedTreeDivItem .fancytree-unselectable span.fancytree-checkbox {
  opacity: 0.4;
  filter: alpha(opacity=40);
}
.advancedTreeDivItem .fancytree-unselectable span.fancytree-checkbox:hover {
  background-position: 0px -32px;
}
.advancedTreeDivItem .fancytree-unselectable.fancytree-partsel span.fancytree-checkbox:hover {
  background-position: -64px -32px;
}
.advancedTreeDivItem .fancytree-unselectable.fancytree-selected span.fancytree-checkbox:hover {
  background-position: -32px -32px;
}
/*------------------------------------------------------------------------------
 * Node type icon
 * Note: IE6 doesn't correctly evaluate multiples class names,
 *		 so we create combined class names that can be used in the CSS.
 *
 * Prefix: fancytree-ico-
 * 1st character: 'e': expanded, 'c': collapsed
 * 2nd character (optional): 'f': folder
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem span.fancytree-icon {
  margin-left: 3px;
  background-position: 0px 0px;
}
/* Documents */
.advancedTreeDivItem .fancytree-ico-c span.fancytree-icon:hover {
  background-position: -16px 0px;
}
.advancedTreeDivItem .fancytree-has-children.fancytree-ico-c span.fancytree-icon {
  background-position: -32px 0px;
}
.advancedTreeDivItem .fancytree-has-children.fancytree-ico-c span.fancytree-icon:hover {
  background-position: -48px 0px;
}
.advancedTreeDivItem .fancytree-ico-e span.fancytree-icon {
  background-position: -64px 0px;
}
.advancedTreeDivItem .fancytree-ico-e span.fancytree-icon:hover {
  background-position: -80px 0px;
}
/* Folders */
.advancedTreeDivItem .fancytree-ico-cf span.fancytree-icon {
  background-position: 0px -16px;
}
.advancedTreeDivItem .fancytree-ico-cf span.fancytree-icon:hover {
  background-position: -16px -16px;
}
.advancedTreeDivItem .fancytree-has-children.fancytree-ico-cf span.fancytree-icon {
  background-position: -32px -16px;
}
.advancedTreeDivItem .fancytree-has-children.fancytree-ico-cf span.fancytree-icon:hover {
  background-position: -48px -16px;
}
.advancedTreeDivItem .fancytree-ico-ef span.fancytree-icon {
  background-position: -64px -16px;
}
.advancedTreeDivItem .fancytree-ico-ef span.fancytree-icon:hover {
  background-position: -80px -16px;
}
.advancedTreeDivItem .fancytree-loading span.fancytree-expander,
.advancedTreeDivItem .fancytree-loading span.fancytree-expander:hover,
.advancedTreeDivItem .fancytree-statusnode-loading span.fancytree-icon,
.advancedTreeDivItem .fancytree-statusnode-loading span.fancytree-icon:hover,
.advancedTreeDivItem span.fancytree-icon.fancytree-icon-loading {
  background-image: url("../skin-xp/loading.gif");
  background-position: 0px 0px;
}
/* Status node icons */
.advancedTreeDivItem .fancytree-statusnode-error span.fancytree-icon,
.advancedTreeDivItem .fancytree-statusnode-error span.fancytree-icon:hover {
  background-position: 0px -112px;
}
/*------------------------------------------------------------------------------
 * Node titles and highlighting
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem span.fancytree-node {
  /* See #117 */
  display: inherit;
  width: 100%;
  margin-top: 1px;
  min-height: 16px;
}
.advancedTreeDivItem span.fancytree-title {
  color: black;
  cursor: default;
  display: inline-block;
  vertical-align: top;
  min-height: 16px;
  padding: 0 3px 0 3px;
  margin: 0px 0 0 3px;
  border: 0 solid transparent;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
}
.advancedTreeDivItem span.fancytree-node.fancytree-error span.fancytree-title {
  color: red;
}
/*------------------------------------------------------------------------------
 * Drag'n'drop support
 *----------------------------------------------------------------------------*/
/* ext-dnd5: */
.advancedTreeDivItem span.fancytree-childcounter {
  color: #fff;
  background: #337ab7;
  border: 1px solid gray;
  border-radius: 10px;
  padding: 2px;
  text-align: center;
}
/* ext-dnd: */
.advancedTreeDivItem div.fancytree-drag-helper span.fancytree-childcounter,
.advancedTreeDivItem div.fancytree-drag-helper span.fancytree-dnd-modifier {
  display: inline-block;
  color: #fff;
  background: #337ab7;
  border: 1px solid gray;
  min-width: 10px;
  height: 10px;
  line-height: 1;
  vertical-align: baseline;
  border-radius: 10px;
  padding: 2px;
  text-align: center;
  font-size: 9px;
}
.advancedTreeDivItem div.fancytree-drag-helper span.fancytree-childcounter {
  position: absolute;
  top: -6px;
  right: -6px;
}
.advancedTreeDivItem div.fancytree-drag-helper span.fancytree-dnd-modifier {
  background: #5cb85c;
  border: none;
  font-weight: bolder;
}
.advancedTreeDivItem div.fancytree-drag-helper.fancytree-drop-accept span.fancytree-drag-helper-img {
  background-position: -32px -112px;
}
.advancedTreeDivItem div.fancytree-drag-helper.fancytree-drop-reject span.fancytree-drag-helper-img {
  background-position: -16px -112px;
}
/*** Drop marker icon *********************************************************/
.advancedTreeDivItem #fancytree-drop-marker {
  width: 32px;
  position: absolute;
  background-position: 0px -128px;
  margin: 0;
}
.advancedTreeDivItem #fancytree-drop-marker.fancytree-drop-after,
.advancedTreeDivItem #fancytree-drop-marker.fancytree-drop-before {
  width: 64px;
  background-position: 0px -144px;
}
.advancedTreeDivItem #fancytree-drop-marker.fancytree-drop-copy {
  background-position: -64px -128px;
}
.advancedTreeDivItem #fancytree-drop-marker.fancytree-drop-move {
  background-position: -32px -128px;
}
/*** Source node while dragging ***********************************************/
.advancedTreeDivItem span.fancytree-drag-source.fancytree-drag-remove {
  opacity: 0.15;
}
/*** Target node while dragging cursor is over it *****************************/
/*------------------------------------------------------------------------------
 * 'rtl' option
 *----------------------------------------------------------------------------*/
.fancytree-container.fancytree-rtl .fancytree-title {
  /*unicode-bidi: bidi-override;*/
  /* optional: reverse title letters */
}
.advancedTreeDivItem .fancytree-container.fancytree-rtl span.fancytree-connector,
.advancedTreeDivItem .fancytree-container.fancytree-rtl span.fancytree-expander,
.advancedTreeDivItem .fancytree-container.fancytree-rtl span.fancytree-icon,
.advancedTreeDivItem .fancytree-container.fancytree-rtl span.fancytree-drag-helper-img {
  background-image: url("../skin-xp/icons-rtl.gif");
}
.advancedTreeDivItem .fancytree-container.fancytree-rtl .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-container.fancytree-rtl .fancytree-exp-nl span.fancytree-expander {
  background-image: none;
}
.advancedTreeDivItem .fancytree-container.fancytree-rtl.fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-container.fancytree-rtl.fancytree-connectors .fancytree-exp-nl span.fancytree-expander {
  background-image: url("../skin-xp/icons-rtl.gif");
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl ul {
  padding: 0 16px 0 0;
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl.fancytree-connectors li {
  background-position: right 0;
  background-image: url("../skin-xp/vline-rtl.gif");
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl li.fancytree-lastsib,
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl.fancytree-no-connector > li {
  background-image: none;
}
.advancedTreeDivItem #fancytree-drop-marker.fancytree-rtl {
  background-image: url("../skin-xp/icons-rtl.gif");
}
/*------------------------------------------------------------------------------
 * 'table' extension
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem table.fancytree-ext-table {
  font-family: tahoma, arial, helvetica;
  font-size: 10pt;
  border-collapse: collapse;
  /* ext-ariagrid */
}
.advancedTreeDivItem table.fancytree-ext-table span.fancytree-node {
  display: inline-block;
  box-sizing: border-box;
}
.advancedTreeDivItem table.fancytree-ext-table td.fancytree-status-merged {
  text-align: center;
  font-style: italic;
  color: #c0c0c0;
}
.advancedTreeDivItem table.fancytree-ext-table tr.fancytree-statusnode-error td.fancytree-status-merged {
  color: red;
}
.advancedTreeDivItem table.fancytree-ext-table.fancytree-ext-ariagrid.fancytree-cell-mode > tbody > tr.fancytree-active > td {
  background-color: #eee;
}
.advancedTreeDivItem table.fancytree-ext-table.fancytree-ext-ariagrid.fancytree-cell-mode > tbody > tr > td.fancytree-active-cell {
  background-color: #cbe8f6;
}
.advancedTreeDivItem table.fancytree-ext-table.fancytree-ext-ariagrid.fancytree-cell-mode.fancytree-cell-nav-mode > tbody > tr > td.fancytree-active-cell {
  background-color: #3875d7;
}
/*------------------------------------------------------------------------------
 * 'columnview' extension
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem table.fancytree-ext-columnview tbody tr td {
  position: relative;
  border: 1px solid gray;
  vertical-align: top;
  overflow: auto;
}
.advancedTreeDivItem table.fancytree-ext-columnview tbody tr td > ul {
  padding: 0;
}
.advancedTreeDivItem table.fancytree-ext-columnview tbody tr td > ul li {
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  -moz-background-clip: border;
  -moz-background-inline-policy: continuous;
  -moz-background-origin: padding;
  background-attachment: scroll;
  background-color: transparent;
  background-position: 0px 0px;
  background-repeat: repeat-y;
  background-image: none;
  /* no v-lines */
  margin: 0;
}
.advancedTreeDivItem table.fancytree-ext-columnview span.fancytree-node {
  position: relative;
  /* allow positioning of embedded spans */
  display: inline-block;
}
.advancedTreeDivItem table.fancytree-ext-columnview span.fancytree-node.fancytree-expanded {
  background-color: #e0e0e0;
}
.advancedTreeDivItem table.fancytree-ext-columnview span.fancytree-node.fancytree-active {
  background-color: #CBE8F6;
}
.advancedTreeDivItem table.fancytree-ext-columnview .fancytree-has-children span.fancytree-cv-right {
  position: absolute;
  right: 3px;
  background-position: 0px -80px;
}
.advancedTreeDivItem table.fancytree-ext-columnview .fancytree-has-children span.fancytree-cv-right:hover {
  background-position: -16px -80px;
}
/*------------------------------------------------------------------------------
 * 'filter' extension
 *----------------------------------------------------------------------------*/
.fancytree-ext-filter-dimm span.fancytree-node span.fancytree-title {
  color: #c0c0c0;
  font-weight: lighter;
}
.advancedTreeDivItem .fancytree-ext-filter-dimm tr.fancytree-submatch span.fancytree-title,
.advancedTreeDivItem .fancytree-ext-filter-dimm span.fancytree-node.fancytree-submatch span.fancytree-title {
  color: black;
  font-weight: normal;
}
.advancedTreeDivItem .fancytree-ext-filter-dimm tr.fancytree-match span.fancytree-title,
.advancedTreeDivItem .fancytree-ext-filter-dimm span.fancytree-node.fancytree-match span.fancytree-title {
  color: black;
  font-weight: bold;
}
.advancedTreeDivItem .fancytree-ext-filter-hide tr.fancytree-hide,
.advancedTreeDivItem .fancytree-ext-filter-hide span.fancytree-node.fancytree-hide {
  display: none;
}
.advancedTreeDivItem .fancytree-ext-filter-hide tr.fancytree-submatch span.fancytree-title,
.advancedTreeDivItem .fancytree-ext-filter-hide span.fancytree-node.fancytree-submatch span.fancytree-title {
  color: #c0c0c0;
  font-weight: lighter;
}
.advancedTreeDivItem .fancytree-ext-filter-hide tr.fancytree-match span.fancytree-title,
.advancedTreeDivItem .fancytree-ext-filter-hide span.fancytree-node.fancytree-match span.fancytree-title {
  color: black;
  font-weight: normal;
}
/* Hide expanders if all child nodes are hidden by filter */
.advancedTreeDivItem .fancytree-ext-filter-hide-expanders tr.fancytree-match span.fancytree-expander,
.advancedTreeDivItem .fancytree-ext-filter-hide-expanders span.fancytree-node.fancytree-match span.fancytree-expander {
  visibility: hidden;
}
.advancedTreeDivItem .fancytree-ext-filter-hide-expanders tr.fancytree-submatch span.fancytree-expander,
.advancedTreeDivItem .fancytree-ext-filter-hide-expanders span.fancytree-node.fancytree-submatch span.fancytree-expander {
  visibility: visible;
}
.advancedTreeDivItem .fancytree-ext-childcounter span.fancytree-icon,
.advancedTreeDivItem .fancytree-ext-filter span.fancytree-icon,
.advancedTreeDivItem .fancytree-ext-childcounter span.fancytree-custom-icon,
.advancedTreeDivItem .fancytree-ext-filter span.fancytree-custom-icon {
  position: relative;
}
.advancedTreeDivItem .fancytree-ext-childcounter span.fancytree-childcounter,
.advancedTreeDivItem .fancytree-ext-filter span.fancytree-childcounter {
  color: #fff;
  background: #777;
  border: 1px solid gray;
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 10px;
  height: 10px;
  line-height: 1;
  vertical-align: baseline;
  border-radius: 10px;
  padding: 2px;
  text-align: center;
  font-size: 9px;
}
/*------------------------------------------------------------------------------
 * 'wide' extension
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem ul.fancytree-ext-wide {
  position: relative;
  min-width: 100%;
  z-index: 2;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.advancedTreeDivItem ul.fancytree-ext-wide span.fancytree-node > span {
  position: relative;
  z-index: 2;
}
.advancedTreeDivItem ul.fancytree-ext-wide span.fancytree-node span.fancytree-title {
  position: absolute;
  z-index: 1;
  left: 0px;
  min-width: 100%;
  margin-left: 0;
  margin-right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
/*------------------------------------------------------------------------------
 * 'fixed' extension
 *----------------------------------------------------------------------------*/
.advancedTreeDivItem .fancytree-ext-fixed-wrapper .fancytree-ext-fixed-hidden {
  display: none;
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-scroll-border-bottom {
  border-bottom: 3px solid rgba(0, 0, 0, 0.75);
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-scroll-border-right {
  border-right: 3px solid rgba(0, 0, 0, 0.75);
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-wrapper-tl {
  position: absolute;
  overflow: hidden;
  z-index: 3;
  top: 0px;
  left: 0px;
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-wrapper-tr {
  position: absolute;
  overflow: hidden;
  z-index: 2;
  top: 0px;
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-wrapper-bl {
  position: absolute;
  overflow: hidden;
  z-index: 2;
  left: 0px;
}
.advancedTreeDivItem .fancytree-ext-fixed-wrapper div.fancytree-ext-fixed-wrapper-br {
  position: absolute;
  overflow: scroll;
  z-index: 1;
}
/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/
/*******************************************************************************
 * Tree container
 */
.advancedTreeDivItem ul.fancytree-container li {
  background-image: url("../skin-xp/vline.gif");
  background-position: 0 0;
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl li {
  background-position: right 0;
  background-image: url("../skin-xp/vline-rtl.gif");
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl .fancytree-exp-n span.fancytree-expander {
  background-image: url("../skin-xp/icons-rtl.gif");
  background-position: 0px -64px;
}
.advancedTreeDivItem ul.fancytree-container.fancytree-rtl .fancytree-exp-nl span.fancytree-expander {
  background-image: url("../skin-xp/icons-rtl.gif");
  background-position: -16px -64px;
}
.advancedTreeDivItem ul.fancytree-container li.fancytree-lastsib {
  background-image: none;
}
.advancedTreeDivItem ul.fancytree-no-connector > li {
  background-image: none;
}
.advancedTreeDivItem .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-nl span.fancytree-expander {
  background-image: url("../skin-xp/micons.gif");
}
.advancedTreeDivItem .fancytree-exp-n span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-n span.fancytree-expander:hover {
  background-position: 0px -64px;
}
.advancedTreeDivItem .fancytree-exp-nl span.fancytree-expander,
.advancedTreeDivItem .fancytree-exp-nl span.fancytree-expander:hover {
  background-position: -16px -64px;
}
/*******************************************************************************
 * Node titles
 */
.advancedTreeDivItem span.fancytree-title {
  border: 0 solid transparent;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 80%;
}
.advancedTreeDivItem span.fancytree-title:hover {
  background-color: transparent;
  border-color: transparent;
}
.advancedTreeDivItem span.fancytree-focused span.fancytree-title {
  outline: none;
  background-color: #EFEBDE;
}
.advancedTreeDivItem .fancytree-folder span.fancytree-title {
  font-weight: bold;
}
.advancedTreeDivItem .fancytree-selected span.fancytree-title {
  color: green;
  font-style: italic;
}
.advancedTreeDivItem .fancytree-active span.fancytree-title {
  background-color: transparent;
}
/*******************************************************************************
 * 'table' extension
 */
.advancedTreeDivItem table.fancytree-ext-table {
  border-collapse: collapse;
  table-layout: fixed;
}
.advancedTreeDivItem table.fancytree-ext-table tbody tr.fancytree-focused {
  background-color: #99DEFD;
}
.advancedTreeDivItem table.fancytree-ext-table tbody tr.fancytree-active {
  background-color: royalblue;
}
.advancedTreeDivItem table.fancytree-ext-table tbody tr.fancytree-selected {
  background-color: #99FDDE;
}
