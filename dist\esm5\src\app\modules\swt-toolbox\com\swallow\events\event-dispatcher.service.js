/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Logger } from "../../../com/swallow/logging/logger.service";
import { focusManager } from "../managers/focus-manager.service";
import { BaseObject } from "../model/base-object";
/**
 * <AUTHOR>
 * \@date 28/05/2019
 * Language Version:    TypeScript 3.2
 * The EventDispatcher service is the base class for all classes that dispatch events.
 * The EventDispatcher service is the base class for all other classes.
 * The EventDispatcher service allows any object on the subclasses to be an event target and as such, to use the methods of the IEventDispatcher interface.
 * Event targets are an important part of the SwtToolBox event model.
 * The event target serves as the focal point for how events flow through the subclasses hierarchy.
 * When an event such as a mouse click or a keypress occurs, SwtToolbox dispatches an event object into the event flow from the root of the subclasses.
 * The event object then makes its way through the subclasses until it reaches the event target, at which point it begins its return trip through the subclasses.
 * This round-trip journey to the event target is conceptually divided into three phases:
 * the capture phase comprises the journey from the root to the last node before the event target's node, the target phase comprises only the event target node,
 * and the bubbling phase comprises any subsequent nodes encountered on the return trip to the root of the subclasses.
 * In general, the easiest way for a user-defined class to gain event dispatching capabilities is to extend EventDispatcher.
 * If this is impossible (that is, if the class is already extending another class), you can instead implement the IEventDispatcher interface,
 * create an EventDispatcher member, and write simple hooks to route calls into the aggregated EventDispatcher.
 */
var /**
 * <AUTHOR> belgouthi
 * \@date 28/05/2019
 * Language Version:    TypeScript 3.2
 * The EventDispatcher service is the base class for all classes that dispatch events.
 * The EventDispatcher service is the base class for all other classes.
 * The EventDispatcher service allows any object on the subclasses to be an event target and as such, to use the methods of the IEventDispatcher interface.
 * Event targets are an important part of the SwtToolBox event model.
 * The event target serves as the focal point for how events flow through the subclasses hierarchy.
 * When an event such as a mouse click or a keypress occurs, SwtToolbox dispatches an event object into the event flow from the root of the subclasses.
 * The event object then makes its way through the subclasses until it reaches the event target, at which point it begins its return trip through the subclasses.
 * This round-trip journey to the event target is conceptually divided into three phases:
 * the capture phase comprises the journey from the root to the last node before the event target's node, the target phase comprises only the event target node,
 * and the bubbling phase comprises any subsequent nodes encountered on the return trip to the root of the subclasses.
 * In general, the easiest way for a user-defined class to gain event dispatching capabilities is to extend EventDispatcher.
 * If this is impossible (that is, if the class is already extending another class), you can instead implement the IEventDispatcher interface,
 * create an EventDispatcher member, and write simple hooks to route calls into the aggregated EventDispatcher.
 */
EventDispatcher = /** @class */ (function (_super) {
    tslib_1.__extends(EventDispatcher, _super);
    function EventDispatcher(target, __targetService) {
        var _this = _super.call(this) || this;
        _this.target = target;
        _this.__targetService = __targetService;
        _this.eventlist = [];
        _this.log = new Logger("EventDispatcher", _this.__targetService.httpclient);
        return _this;
    }
    /**
     * Registers an event listener object with an EventDispatcher object so
     * that the listener receives notification of an event.
     * @param type
     * @param listener
     * @param useCapture
     * @param priority
     * @param useWeakReference
     */
    //
    /**
     * Registers an event listener object with an EventDispatcher object so
     * that the listener receives notification of an event.
     * @param {?} type
     * @param {?} listener
     * @param {?=} useCapture
     * @param {?=} priority
     * @param {?=} useWeakReference
     * @param {?=} target
     * @return {?}
     */
    //
    EventDispatcher.prototype.addEventListener = /**
     * Registers an event listener object with an EventDispatcher object so
     * that the listener receives notification of an event.
     * @param {?} type
     * @param {?} listener
     * @param {?=} useCapture
     * @param {?=} priority
     * @param {?=} useWeakReference
     * @param {?=} target
     * @return {?}
     */
    //
    function (type, listener, useCapture, priority, useWeakReference, target) {
        var _this = this;
        if (useCapture === void 0) { useCapture = false; }
        if (priority === void 0) { priority = 0; }
        if (useWeakReference === void 0) { useWeakReference = false; }
        try {
            this.eventlist[type] = listener;
            if (!target) {
                target = this.target.nativeElement;
            }
            if ($(target) && $(target)[0]) {
                $(target)[0].addEventListener(type, (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                    if (type == "click" || type == "focus" || type == "focusin" || type == "keyup" || type == "keydown" || type == "change") {
                        focusManager.focusTarget = _this;
                    }
                    _this.eventlist[type]();
                }), useCapture);
            }
        }
        catch (error) {
            this.log.error("addEventListener - error: ", error);
        }
    };
    /**
     * Dispatches an event into the event flow.
     * @param event
     */
    /**
     * Dispatches an event into the event flow.
     * @param {?} event
     * @return {?}
     */
    EventDispatcher.prototype.dispatchEvent = /**
     * Dispatches an event into the event flow.
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.log.info('dispatchEvent START.');
        /** @type {?} */
        var eventName = "";
        try {
            if (typeof (event) === 'object') {
                event.type ? eventName = event.type : '';
            }
            else {
                eventName = event;
            }
            if (this.eventlist[eventName]) {
                this.eventlist[eventName](event);
            }
        }
        catch (error) {
            this.log.error('dispatchEvent method' + error);
        }
        this.log.info('dispatchEvent END.');
    };
    /**
     * Checks whether the EventDispatcher object has any listeners registered for a specific type of event.
     * @param type
     */
    /**
     * Checks whether the EventDispatcher object has any listeners registered for a specific type of event.
     * @param {?} type
     * @return {?}
     */
    EventDispatcher.prototype.hasEventListener = /**
     * Checks whether the EventDispatcher object has any listeners registered for a specific type of event.
     * @param {?} type
     * @return {?}
     */
    function (type) {
        try {
        }
        catch (error) {
            this.log.error("hasEventListener - error: ", error);
        }
    };
    /**
     * Removes a listener from the EventDispatcher object.
     * @param type
     * @param listener
     * @param useCapture
     */
    /**
     * Removes a listener from the EventDispatcher object.
     * @param {?} type
     * @param {?=} listener
     * @param {?=} useCapture
     * @return {?}
     */
    EventDispatcher.prototype.removeEventListener = /**
     * Removes a listener from the EventDispatcher object.
     * @param {?} type
     * @param {?=} listener
     * @param {?=} useCapture
     * @return {?}
     */
    function (type, listener, useCapture) {
        try {
            this.log.info("removeEventListener ENTER. type:", type);
            delete this.eventlist[type];
        }
        catch (error) {
            this.log.error("removeEventListener - error: ", error);
        }
        this.log.info("removeEventListener END.");
    };
    /**
     * Checks whether an event listener is registered with this EventDispatcher
     * object or any of its ancestors for the specified event type.
     * @param type
     */
    /**
     * Checks whether an event listener is registered with this EventDispatcher
     * object or any of its ancestors for the specified event type.
     * @param {?} type
     * @return {?}
     */
    EventDispatcher.prototype.willTrigger = /**
     * Checks whether an event listener is registered with this EventDispatcher
     * object or any of its ancestors for the specified event type.
     * @param {?} type
     * @return {?}
     */
    function (type) {
        try {
        }
        catch (error) {
            this.log.error("willTrigger - error: ", error);
        }
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    EventDispatcher.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            //  console.log('[EventDispatcher] ngOnDestroy');
            //            for (var index = 0; index <  this.eventlist.length; index++) {
            //                this.removeEventListener(type, null, null)
            //            }
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    };
    return EventDispatcher;
}(BaseObject));
/**
 * <AUTHOR> belgouthi
 * \@date 28/05/2019
 * Language Version:    TypeScript 3.2
 * The EventDispatcher service is the base class for all classes that dispatch events.
 * The EventDispatcher service is the base class for all other classes.
 * The EventDispatcher service allows any object on the subclasses to be an event target and as such, to use the methods of the IEventDispatcher interface.
 * Event targets are an important part of the SwtToolBox event model.
 * The event target serves as the focal point for how events flow through the subclasses hierarchy.
 * When an event such as a mouse click or a keypress occurs, SwtToolbox dispatches an event object into the event flow from the root of the subclasses.
 * The event object then makes its way through the subclasses until it reaches the event target, at which point it begins its return trip through the subclasses.
 * This round-trip journey to the event target is conceptually divided into three phases:
 * the capture phase comprises the journey from the root to the last node before the event target's node, the target phase comprises only the event target node,
 * and the bubbling phase comprises any subsequent nodes encountered on the return trip to the root of the subclasses.
 * In general, the easiest way for a user-defined class to gain event dispatching capabilities is to extend EventDispatcher.
 * If this is impossible (that is, if the class is already extending another class), you can instead implement the IEventDispatcher interface,
 * create an EventDispatcher member, and write simple hooks to route calls into the aggregated EventDispatcher.
 */
export { EventDispatcher };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    EventDispatcher.prototype.log;
    /**
     * @type {?}
     * @protected
     */
    EventDispatcher.prototype.eventlist;
    /**
     * @type {?}
     * @private
     */
    EventDispatcher.prototype.target;
    /**
     * @type {?}
     * @private
     */
    EventDispatcher.prototype.__targetService;
}
/**
 * @record
 */
export function IEventDispatcher() { }
if (false) {
    /** @type {?} */
    IEventDispatcher.prototype.addEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.dispatchEvent;
    /** @type {?} */
    IEventDispatcher.prototype.hasEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.removeEventListener;
    /** @type {?} */
    IEventDispatcher.prototype.willTrigger;
}
//# sourceMappingURL=data:application/json;base64,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