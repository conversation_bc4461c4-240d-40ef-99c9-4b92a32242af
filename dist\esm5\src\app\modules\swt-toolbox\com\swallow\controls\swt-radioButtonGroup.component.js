/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ContentChildren, ElementRef, Input } from "@angular/core";
import { Logger } from "../logging/logger.service";
import { SwtRadioItem } from "./swt-radioItem.component";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { HttpClient } from "@angular/common/http";
/** @type {?} */
var $ = require('jquery');
var SwtRadioButtonGroup = /** @class */ (function (_super) {
    tslib_1.__extends(SwtRadioButtonGroup, _super);
    /**
     * Constructor
     * @param httpClient
     * @param elem
     * @param commonService
     * @param _renderer
     */
    function SwtRadioButtonGroup(httpClient, elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.httpClient = httpClient;
        _this.elem = elem;
        _this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        _this.logger = null;
        _this.originalRadioItem = null;
        _this.radioItemsArray = [];
        _this._selectedRadioId = null;
        _this.logger = new Logger('SwtRadioButtonGroup', httpClient, 6);
        _this.logger.info("[ SwtRadioButtonGroup ] construction - START/END");
        return _this;
    }
    Object.defineProperty(SwtRadioButtonGroup.prototype, "radioItems", {
        //---ContentChildren-----------------------------------------------------------------------------------------------
        set: 
        //---ContentChildren-----------------------------------------------------------------------------------------------
        /**
         * @param {?} items
         * @return {?}
         */
        function (items) {
            if (items._results.length > 0)
                this.radioItemsArray = items._results;
            /** @type {?} */
            var __this = this;
            this.radioItemsArray.forEach((/**
             * @param {?} item
             * @return {?}
             */
            function (item) {
                item.parentGroup = __this;
                if (!item.value) {
                    item.value = item.id;
                }
                if (item.selected) {
                    __this.selectedValue = item.value;
                }
            }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioButtonGroup.prototype, "align", {
        get: /**
         * @return {?}
         */
        function () {
            return this._align;
        },
        //---Align---------------------------------------------------------------------------------------------------------
        set: 
        //---Align---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._align = value;
            if (this._align == 'horizontal') {
                $($(this.elem.nativeElement).children()[0]).addClass('verticalHorizontalAlign');
            }
            else {
                $($(this.elem.nativeElement).children()[0]).removeClass('verticalHorizontalAlign');
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioButtonGroup.prototype, "selectedValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedValue;
        },
        //---selectedValue-------------------------------------------------------------------------------------------------
        set: 
        //---selectedValue-------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            try {
                /** @type {?} */
                var selectedItem = null;
                if (this.selectedRadioId != null && this._selectedValue == value) {
                    selectedItem = this.radioItemsArray.find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return x.id == _this.selectedRadioId; }));
                }
                else {
                    selectedItem = this.radioItemsArray.find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return (x.value == value); }));
                }
                if (selectedItem && !selectedItem.selected)
                    selectedItem.selected = true;
                this._selectedValue = value;
                if (this.firstCall) {
                    this.originalValue = selectedItem.value;
                    this.originalRadioItem = selectedItem.id;
                    this.firstCall = false;
                }
                if ((this.originalValue != this.selectedValue) || (this.originalRadioItem != selectedItem.id)) {
                    this._spyChanges(value);
                }
                else {
                    this.spyNoChanges(value);
                }
            }
            catch (error) {
                this.logger.error('set selectedValue method ', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtRadioButtonGroup.prototype, "selectedRadioId", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedRadioId;
        },
        //----selectedRadioId----------------------------------------------------------------------------------------------
        set: 
        //----selectedRadioId----------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selectedRadioId = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        //-START- Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtRadioButtonGroup.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtRadioButtonGroup');
    };
    /**
     * Change
     * @param event
     */
    /**
     * Change
     * @param {?} event
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.Change = /**
     * Change
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.logger.info("[ SwtRadioButtonGroup ] Change - START/END - ", event.target.id);
        this.selectedRadioId = event.target.id;
        this.selectedValue = ((/** @type {?} */ (event.target.value))) == "" ? null : event.target.value;
        if (this.change != undefined) {
            this.change(event);
        }
    };
    /**
     * spyChanges
     * @param event
     * @Added by Khalil.B
     * @Reviewed By Rihab.Jaballah @23/04/2019.
     */
    /**
     * spyChanges
     * \@Added by Khalil.B
     * \@Reviewed By Rihab.Jaballah \@23/04/2019.
     * @param {?} event
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.spyChanges = /**
     * spyChanges
     * \@Added by Khalil.B
     * \@Reviewed By Rihab.Jaballah \@23/04/2019.
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.onSpyChange.emit({ "target": this, "value": event });
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.spyNoChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.onSpyNoChange.emit({ "target": this, "value": event });
    };
    /**
     * resetOriginalValue
     */
    /**
     * resetOriginalValue
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.resetOriginalValue = /**
     * resetOriginalValue
     * @return {?}
     */
    function () {
        this.originalValue = this._selectedValue;
        this.originalRadioItem = this._selectedRadioId;
        this.spyNoChanges(this._selectedValue);
    };
    /**
     * Destroy all event listeners
     */
    /**
     * Destroy all event listeners
     * @return {?}
     */
    SwtRadioButtonGroup.prototype.ngOnDestroy = /**
     * Destroy all event listeners
     * @return {?}
     */
    function () {
        try {
            //console.log('[SwtRadioButtonGroup] ngOnDestroy ');
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this.logger;
            delete this.firstCall;
            delete this.originalRadioItem;
            delete this.radioItemsArray;
            delete this._align;
            delete this._selectedValue;
            delete this._selectedRadioId;
            delete this.change;
        }
        catch (error) {
            console.error('method [ngOnDestroy] - error :', error);
        }
    };
    SwtRadioButtonGroup.decorators = [
        { type: Component, args: [{
                    selector: 'SwtRadioButtonGroup',
                    template: "\n    <div  \n         tabindex=\"-1\"  \n         popper=\"{{this.toolTipPreviousValue}}\"\n         [popperTrigger]=\"'hover'\"\n         [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n         [popperPlacement]=\"'bottom'\"\n         [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n         class=\"inline-field SwtRadioButtonGroup\"   \n         (change)=\"Change($event)\" >\n          <ng-content  ></ng-content>\n          <ng-container #_container></ng-container>\n    </div>    \n  ",
                    styles: [" \n            :host {\n               display: block;\n               outline: none;\n             }\n             .SwtRadioButtonGroup{\n                  outline: none;\n             }\n             .verticalHorizontalAlign{\n                 display : flex;\n                \n             }\n\n            .SwtRadioButtonGroup.disabled-container{\n                opacity: unset!important;\n            }\n     "]
                }] }
    ];
    /** @nocollapse */
    SwtRadioButtonGroup.ctorParameters = function () { return [
        { type: HttpClient },
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtRadioButtonGroup.propDecorators = {
        toolTip: [{ type: Input, args: ['toolTip',] }],
        radioItems: [{ type: ContentChildren, args: [SwtRadioItem,] }],
        align: [{ type: Input, args: ['align',] }],
        selectedValue: [{ type: Input }]
    };
    return SwtRadioButtonGroup;
}(Container));
export { SwtRadioButtonGroup };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.originalRadioItem;
    /** @type {?} */
    SwtRadioButtonGroup.prototype.radioItemsArray;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._align;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype._selectedRadioId;
    /** @type {?} */
    SwtRadioButtonGroup.prototype.toolTip;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.httpClient;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtRadioButtonGroup.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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