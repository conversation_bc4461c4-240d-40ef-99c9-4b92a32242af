import { Column, Filter, FilterArguments, FilterCallback, SearchTerm, OperatorType, OperatorString, ColumnFilter, GridOption } from 'angular-slickgrid';
export declare class EnhancedInputSearch implements Filter {
    private _clearFilterTriggered;
    private _shouldTriggerQuery;
    private $filterElm;
    grid: any;
    searchTerms: SearchTerm[];
    columnDef: Column;
    callback: FilterCallback;
    operator: OperatorType | OperatorString;
    protected _inputType: string;
    constructor();
    /** Getter for the Column Filter */
    readonly columnFilter: ColumnFilter;
    /** Getter for the Grid Options pulled through the Grid Object */
    protected readonly gridOptions: GridOption;
    /**
     * Initialize the Filter
     */
    init(args: FilterArguments): void;
    /**
     * Clear the filter value
     */
    clear(shouldTriggerQuery?: boolean): void;
    /**
     * destroy the filter
     */
    destroy(): void;
    /** Set value(s) on the DOM element */
    setValues(values: SearchTerm | SearchTerm[]): void;
    /**
     * Create the HTML template as a string
     */
    private buildTemplateHtmlString;
    /**
     * From the html template string, create a DOM element
     * @param filterTemplate
     */
    private createDomElement;
}
