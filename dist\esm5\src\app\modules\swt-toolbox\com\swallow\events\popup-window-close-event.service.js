/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
var PopupWindowCloseEvent = /** @class */ (function () {
    function PopupWindowCloseEvent(type, bubbles, cancelable) {
        if (bubbles === void 0) { bubbles = false; }
        if (cancelable === void 0) { cancelable = false; }
        this.type = type;
        this.bubbles = bubbles;
        this.cancelable = cancelable;
    }
    Object.defineProperty(PopupWindowCloseEvent.prototype, "popupDataDTO", {
        get: /**
         * @return {?}
         */
        function () {
            return this._popupDataDTO;
        },
        set: /**
         * @param {?} popupDataDTO
         * @return {?}
         */
        function (popupDataDTO) {
            this._popupDataDTO = popupDataDTO;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    PopupWindowCloseEvent.prototype.clone = /**
     * @return {?}
     */
    function () {
        return new PopupWindowCloseEvent(this.type, this.bubbles, this.cancelable);
    };
    PopupWindowCloseEvent.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    PopupWindowCloseEvent.ctorParameters = function () { return [
        { type: String },
        { type: Boolean },
        { type: Boolean }
    ]; };
    return PopupWindowCloseEvent;
}());
export { PopupWindowCloseEvent };
if (false) {
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype._popupDataDTO;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.type;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.bubbles;
    /**
     * @type {?}
     * @private
     */
    PopupWindowCloseEvent.prototype.cancelable;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicG9wdXAtd2luZG93LWNsb3NlLWV2ZW50LnNlcnZpY2UuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvZXZlbnRzL3BvcHVwLXdpbmRvdy1jbG9zZS1ldmVudC5zZXJ2aWNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBRTNDO0lBRUksK0JBQW9CLElBQVksRUFBVyxPQUF3QixFQUFXLFVBQTJCO1FBQTlELHdCQUFBLEVBQUEsZUFBd0I7UUFBVywyQkFBQSxFQUFBLGtCQUEyQjtRQUFyRixTQUFJLEdBQUosSUFBSSxDQUFRO1FBQVcsWUFBTyxHQUFQLE9BQU8sQ0FBaUI7UUFBVyxlQUFVLEdBQVYsVUFBVSxDQUFpQjtJQUN6RyxDQUFDO0lBSUQsc0JBQUksK0NBQVk7Ozs7UUFBaEI7WUFDSSxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUM7UUFDaEMsQ0FBQzs7Ozs7UUFFQyxVQUFpQixZQUFpQjtZQUNoQyxJQUFJLENBQUMsYUFBYSxHQUFHLFlBQVksQ0FBQztRQUN0QyxDQUFDOzs7T0FKQTs7OztJQU1RLHFDQUFLOzs7SUFBWjtRQUNJLE9BQU8sSUFBSSxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQ2pGLENBQUM7O2dCQWpCRixVQUFVOzs7Ozs7OztJQWtCWCw0QkFBQztDQUFBLEFBbEJELElBa0JDO1NBakJZLHFCQUFxQjs7Ozs7O0lBSTlCLDhDQUEyQjs7Ozs7SUFIZixxQ0FBb0I7Ozs7O0lBQUUsd0NBQWlDOzs7OztJQUFFLDJDQUFvQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGFibGUgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcclxuXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIFBvcHVwV2luZG93Q2xvc2VFdmVudCB7XHJcbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIHR5cGU6IHN0cmluZywgcHJpdmF0ZSAgYnViYmxlczogYm9vbGVhbiA9IGZhbHNlLCBwcml2YXRlICBjYW5jZWxhYmxlOiBib29sZWFuID0gZmFsc2UpIHtcclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIF9wb3B1cERhdGFEVE86IGFueTtcclxuICBcclxuICAgIGdldCBwb3B1cERhdGFEVE8oKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3BvcHVwRGF0YURUTztcclxuICB9XHJcblxyXG4gICAgc2V0IHBvcHVwRGF0YURUTyhwb3B1cERhdGFEVE86IGFueSkge1xyXG4gICAgICB0aGlzLl9wb3B1cERhdGFEVE8gPSBwb3B1cERhdGFEVE87XHJcbiAgfVxyXG5cclxuICAgIHB1YmxpYyBjbG9uZSgpIHtcclxuICAgICAgICByZXR1cm4gbmV3IFBvcHVwV2luZG93Q2xvc2VFdmVudCh0aGlzLnR5cGUsIHRoaXMuYnViYmxlcywgdGhpcy5jYW5jZWxhYmxlKTtcclxuICB9XHJcbn1cclxuIl19