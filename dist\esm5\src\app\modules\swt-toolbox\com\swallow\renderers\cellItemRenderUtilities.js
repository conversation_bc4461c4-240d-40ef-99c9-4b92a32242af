/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * Return the formatter based on 'negative' value .
 * @param {?} row
 * @param {?} field
 * @return {?}
 */
export function isNegative(row, field) {
    /** @type {?} */
    var negative = false;
    if (row.slickgrid_rowcontent && row.slickgrid_rowcontent[field] != undefined) {
        negative = row.slickgrid_rowcontent[field].negative;
        if (typeof (negative) == 'string') {
            if (negative == 'true') {
                negative = true;
            }
            else {
                negative = false;
            }
        }
    }
    return negative;
}
/**
 * Return the formatter based on 'negative' value .
 * @param {?} row
 * @param {?} field
 * @return {?}
 */
export function CellBackgroundColor(row, field) {
    /** @type {?} */
    var holiday = false;
    if (row.slickgrid_rowcontent && row.slickgrid_rowcontent.hasOwnProperty(field)) {
        holiday = row.slickgrid_rowcontent[field].holiday;
        if (typeof (holiday) == 'string') {
            if (holiday == 'true') {
                holiday = true;
            }
            else {
                holiday = false;
            }
        }
    }
    return holiday;
}
/**
 * Return the formatter as a link .
 * @param {?} row
 * @param {?} field
 * @return {?}
 */
export function isClickable(row, field) {
    /** @type {?} */
    var isLink = false;
    if (row.slickgrid_rowcontent && row.slickgrid_rowcontent[field] != undefined) {
        isLink = row.slickgrid_rowcontent[field].clickable;
    }
    return isLink;
}
/**
 * Return the formatter based on 'bold' value .
 * @param {?} row
 * @param {?} field
 * @return {?}
 */
export function isBold(row, field) {
    /** @type {?} */
    var bold = false;
    if (row.slickgrid_rowcontent && row.slickgrid_rowcontent[field] != undefined && row.slickgrid_rowcontent[field].hasOwnProperty('bold')) {
        bold = row.slickgrid_rowcontent[field].bold;
        if (typeof (bold) == 'string') {
            if (bold == 'true') {
                bold = true;
            }
            else {
                bold = false;
            }
        }
        else {
            return bold;
        }
    }
    return bold;
}
/**
 * Return the formatter based on 'negative' value .
 * @param {?} value
 * @return {?}
 */
export function isBlink(value) {
    /** @type {?} */
    var blink_me = false;
    if ((String(value)).indexOf('blink') != -1) {
        /** @type {?} */
        var val1 = (value.split(">"))[2];
        /** @type {?} */
        var val2 = val1.split("<");
        value = val2[0];
        blink_me = true;
    }
    return blink_me;
}
/**
 * Added for set a new design for the custom column
 * @param {?} row
 * @param {?} field
 * @return {?}
 */
export function CustomCell(row, field) {
    /** @type {?} */
    var code = null;
    /** @type {?} */
    var color = '#173553';
    /** @type {?} */
    var cellStyle = "";
    if (row.slickgrid_rowcontent && row.slickgrid_rowcontent[field] != undefined) {
        code = row.slickgrid_rowcontent[field].code;
        if (field == "riskScore") {
            cellStyle = 'text-align: center!important; font-weight: bold!important; ';
            //- Cell's text style
            if (code == "10" || code == "9" || code == "8" || code == "7") {
                cellStyle += ' color: #FFFFFF;';
            }
            //- Cell's background color
            if (code == "4" || code == "3" || code == "2" || code == "1") {
                cellStyle += ' background-color: #CCFFCC!important;';
            }
            else if (code == "6" || code == "5") {
                cellStyle += ' background-color: #00FF00!important;';
            }
            else if (code == "8" || code == "7") {
                cellStyle += ' background-color: #FF9900!important;';
            }
            else if (code == "10" || code == "9") {
                cellStyle += ' background-color: #FF0000!important;';
            }
        }
        else if (field == "risk" || field == "riskWeight" || field == "levelCode") {
            cellStyle = 'text-align: center!important; font-weight: bold!important; ';
            //- Cell's text style
            if (code == "4" || code == "3") {
                cellStyle += ' color: #FFFFFF;';
            }
            //- Cell's background color
            if (code == "1") {
                cellStyle += ' background-color: #CCFFCC!important;';
            }
            else if (code == "2") {
                cellStyle += ' background-color: #00FF00!important;';
            }
            else if (code == "3") {
                cellStyle += ' background-color: #FF9900!important;';
            }
            else if (code == "4") {
                cellStyle += ' background-color: #FF0000!important;';
            }
        }
    }
    return cellStyle;
}
/**
 * Encodes special html characters
 * param string
 * return {*}
 * @param {?} string
 * @return {?}
 */
export function html_encode(string) {
    /** @type {?} */
    var ret_val = '';
    for (var i = 0; i < string.length; i++) {
        if (string.codePointAt(i) > 127) {
            ret_val += '&#' + string.codePointAt(i) + ';';
        }
        else {
            ret_val += string.charAt(i);
        }
    }
    return ret_val;
}
//# sourceMappingURL=data:application/json;base64,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