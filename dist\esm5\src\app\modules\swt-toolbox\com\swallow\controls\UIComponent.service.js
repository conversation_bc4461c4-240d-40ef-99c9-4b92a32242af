/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { EventEmitter, Output, Input } from '@angular/core';
import { Logger } from "../logging/logger.service";
import { EventDispatcher } from "../events/event-dispatcher.service";
import { Browsers } from "../model/Browsers";
import { FormControl, Validators } from "@angular/forms";
/** @type {?} */
var $ = require('jquery');
var UIComponent = /** @class */ (function (_super) {
    tslib_1.__extends(UIComponent, _super);
    function UIComponent(uielement, __commonService) {
        var _this = _super.call(this, uielement, __commonService) || this;
        _this.uielement = uielement;
        _this.__commonService = __commonService;
        _this._id = "dynamic-" + Math.random().toString(36).substr(2, 5);
        //---Outputs EventEmitter------------------------------------------------------------------------------------------------
        _this.onClick_ = new EventEmitter();
        _this.dbClick_ = new EventEmitter();
        _this.doubleClick_ = new EventEmitter();
        _this.itemDoubleClick_ = new EventEmitter();
        _this.onKeyDown_ = new EventEmitter();
        _this.onKeyUp_ = new EventEmitter();
        _this.mouseUp_ = new EventEmitter();
        _this.mouseOver_ = new EventEmitter();
        _this.mouseDown_ = new EventEmitter();
        _this.mouseEnter_ = new EventEmitter();
        _this.mouseLeave_ = new EventEmitter();
        _this.mouseOut_ = new EventEmitter();
        _this.mouseIn_ = new EventEmitter();
        _this.mouseMove_ = new EventEmitter();
        _this.focus_ = new EventEmitter();
        _this.focusIn_ = new EventEmitter();
        _this.onFocusOut_ = new EventEmitter();
        _this.keyFocusChange_ = new EventEmitter();
        _this.change_ = new EventEmitter();
        _this.onSpyChange = new EventEmitter();
        _this.onSpyNoChange = new EventEmitter();
        _this.scroll_ = new EventEmitter();
        //---Functions------------------------------------------------------------------------------------------------
        _this._click = new Function();
        _this._dbClick = new Function();
        _this._doubleClick = new Function();
        _this.__itemDoubleClick = new Function();
        _this._keyDown = new Function();
        _this._keyUp = new Function();
        _this._mouseUp = new Function();
        _this._mouseOver = new Function();
        _this._mouseDown = new Function();
        _this._mouseEnter = new Function();
        _this._mouseLeave = new Function();
        _this._mouseOut = new Function();
        _this._mouseIn = new Function();
        _this._mouseMove = new Function();
        _this._focus = new Function();
        _this._focusIn = new Function();
        _this._focusOut = new Function();
        _this._keyFocusChange = new Function();
        _this._change = new Function();
        _this._scroll = new Function();
        _this.eventlist = [];
        _this.cursorLocation = { x: 0, y: 0 };
        _this.log = new Logger("UIComponent", _this.__commonService.httpclient);
        return _this;
    }
    Object.defineProperty(UIComponent.prototype, "maxChars", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxChars;
        },
        //---maxChars-----------------------------------------------------------------------------------------------------
        set: 
        //---maxChars-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._maxChars = Number(value);
            setTimeout((/**
             * @return {?}
             */
            function () {
                $($(_this.uielement.nativeElement).children()[0]).attr('maxlength', _this._maxChars);
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "restrict", {
        get: /**
         * @return {?}
         */
        function () {
            return this._restrict;
        },
        //---restrict-----------------------------------------------------------------------------------------------------
        set: 
        //---restrict-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._restrict = value;
            $(this.uielement.nativeElement).on("keypress", (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                //Check if pressed key is not a : backspace / delete / tabulation
                try {
                    /** @type {?} */
                    var control = new FormControl(String.fromCharCode(e.keyCode), Validators.pattern('[' + value + ']{1}'));
                    if (!control.valid) {
                        e.preventDefault();
                    }
                }
                catch (e) {
                }
            }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "id", {
        get: /**
         * @return {?}
         */
        function () {
            return this._id;
        },
        //---Id---------------------------------------------------------------------------------------------------------
        set: 
        //---Id---------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            // set id to the component's DOM.
            this._id = value;
            if (this.uielement && $(this.uielement.nativeElement))
                $($(this.uielement.nativeElement)[0]).attr("id", this._id);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "click", {
        //--- Event Getters------------------------------------------------------------------------------------------------
        get: 
        //--- Event Getters------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () { return this._click; },
        //--- Event Setters------------------------------------------------------------------------------------------------
        set: 
        //--- Event Setters------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._click = value;
            this.addEventListener('click', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.click(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "dbClick", {
        get: /**
         * @return {?}
         */
        function () { return this._dbClick; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._dbClick = value;
            this.addEventListener('dblclick', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.dbClick(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "doubleClick", {
        get: /**
         * @return {?}
         */
        function () { return this._doubleClick; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._doubleClick = value;
            this.addEventListener('dblclick', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.doubleClick(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "itemDoubleClick", {
        get: /**
         * @return {?}
         */
        function () { return this.__itemDoubleClick; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this.__itemDoubleClick = value;
            this.addEventListener('dblclick', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.itemDoubleClick(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "keyDown", {
        get: /**
         * @return {?}
         */
        function () { return this._keyDown; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._keyDown = value;
            this.addEventListener('keydown', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.keyDown(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "keyUp", {
        get: /**
         * @return {?}
         */
        function () { return this._keyUp; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._keyUp = value;
            this.addEventListener('keyup', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.keyUp(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseUp", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseUp; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseUp = value;
            this.addEventListener('mouseup', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseUp(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseOver", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseOver; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseOver = value;
            this.addEventListener('mouseover', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseOver(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseDown", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseDown; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseDown = value;
            this.addEventListener('mousedown', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseDown(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseEnter", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseEnter; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseEnter = value;
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseEnter(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseIn", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseIn; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseIn = value;
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseIn(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseMove", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseMove; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseMove = value;
            this.addEventListener('mousemove', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseMove(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseLeave", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseLeave; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseLeave = value;
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseLeave(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "mouseOut", {
        get: /**
         * @return {?}
         */
        function () { return this._mouseOut; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._mouseOut = value;
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.mouseOut(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "focus", {
        get: /**
         * @return {?}
         */
        function () { return this._focus; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._focus = value;
            this.addEventListener('focus', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.focus(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "focusIn", {
        get: /**
         * @return {?}
         */
        function () { return this._focusIn; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._focusIn = value;
            this.addEventListener('focus', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.focusIn(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "focusOut", {
        get: /**
         * @return {?}
         */
        function () { return this._focusOut; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._focusOut = value;
            this.addEventListener('focusout', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.focusOut(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "keyFocusChange", {
        get: /**
         * @return {?}
         */
        function () { return this._keyFocusChange; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._keyFocusChange = value;
            this.addEventListener('focusout', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.keyFocusChange(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "change", {
        get: /**
         * @return {?}
         */
        function () { return this._change; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._change = value;
            this.addEventListener('change', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.change(); }));
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(UIComponent.prototype, "scroll", {
        get: /**
         * @return {?}
         */
        function () { return this._scroll; },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            this._scroll = value;
            this.addEventListener('scroll', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) { _this.scroll(); }));
        },
        enumerable: true,
        configurable: true
    });
    /**
     * Adds a non-visual style client to this component instance.
     * @param styleClient
     */
    /**
     * Adds a non-visual style client to this component instance.
     * @param {?} styleClient
     * @return {?}
     */
    UIComponent.prototype.addStyleClient = /**
     * Adds a non-visual style client to this component instance.
     * @param {?} styleClient
     * @return {?}
     */
    function (styleClient) {
    };
    /**
     * This method is used to get the mouse
     * pointer location.
     */
    /**
     * This method is used to get the mouse
     * pointer location.
     * @return {?}
     */
    UIComponent.prototype.getCursorLocation = /**
     * This method is used to get the mouse
     * pointer location.
     * @return {?}
     */
    function () {
        return this.cursorLocation;
    };
    /**
     * Queues a function to be called later.
     * @param method
     * @param args
     */
    /**
     * Queues a function to be called later.
     * @param {?} method
     * @param {?=} args
     * @return {?}
     */
    UIComponent.prototype.callLater = /**
     * Queues a function to be called later.
     * @param {?} method
     * @param {?=} args
     * @return {?}
     */
    function (method, args) {
        var _this = this;
        if (args === void 0) { args = null; }
        setTimeout((/**
         * @return {?}
         */
        function () {
            _this[method].apply(_this, args);
        }), 0);
    };
    /**
     * Deletes a style property from this component instance.
     * @param styleProp
     */
    /**
     * Deletes a style property from this component instance.
     * @param {?} styleProp
     * @return {?}
     */
    UIComponent.prototype.clearStyle = /**
     * Deletes a style property from this component instance.
     * @param {?} styleProp
     * @return {?}
     */
    function (styleProp) {
        try {
            $(this.uielement).css(styleProp, "");
        }
        catch (error) {
            this.log.error("clearStyle - error", error);
        }
    };
    /**
     * Converts a Point object from content coordinates to global coordinates.
     * @param point
     */
    /**
     * Converts a Point object from content coordinates to global coordinates.
     * @param {?} point
     * @return {?}
     */
    UIComponent.prototype.contentToGlobal = /**
     * Converts a Point object from content coordinates to global coordinates.
     * @param {?} point
     * @return {?}
     */
    function (point) {
    };
    /**
     * Converts a Point object from content to local coordinates.
     * @param point
     */
    /**
     * Converts a Point object from content to local coordinates.
     * @param {?} point
     * @return {?}
     */
    UIComponent.prototype.contentToLocal = /**
     * Converts a Point object from content to local coordinates.
     * @param {?} point
     * @return {?}
     */
    function (point) {
    };
    /**
     * Returns a set of properties that identify the child within this container.
     * @param child
     */
    /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @return {?}
     */
    UIComponent.prototype.createAutomationIDPart = /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @return {?}
     */
    function (child) {
    };
    /**
     * Returns a set of properties that identify the child within this container.
     * @param child
     * @param properties
     */
    /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @param {?} properties
     * @return {?}
     */
    UIComponent.prototype.createAutomationIDPartWithRequiredProperties = /**
     * Returns a set of properties that identify the child within this container.
     * @param {?} child
     * @param {?} properties
     * @return {?}
     */
    function (child, properties) {
    };
    /**
     * Creates an id reference to this IUIComponent object on its parent document object.
     * @param parentDocument
     */
    /**
     * Creates an id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    UIComponent.prototype.createReferenceOnParentDocument = /**
     * Creates an id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    function (parentDocument) {
    };
    /**
     * Deletes the id reference to this IUIComponent object on its parent document object.
     * @param parentDocument
     */
    /**
     * Deletes the id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    UIComponent.prototype.deleteReferenceOnParentDocument = /**
     * Deletes the id reference to this IUIComponent object on its parent document object.
     * @param {?} parentDocument
     * @return {?}
     */
    function (parentDocument) {
    };
    /**
     * Returns a UITextFormat object corresponding to the text styles for this UIComponent.
     */
    /**
     * Returns a UITextFormat object corresponding to the text styles for this UIComponent.
     * @return {?}
     */
    UIComponent.prototype.determineTextFormatFromStyles = /**
     * Returns a UITextFormat object corresponding to the text styles for this UIComponent.
     * @return {?}
     */
    function () {
    };
    /**
     * Shows or hides the focus indicator around this component.
     * @param isFocused
     */
    /**
     * Shows or hides the focus indicator around this component.
     * @param {?} isFocused
     * @return {?}
     */
    UIComponent.prototype.drawFocus = /**
     * Shows or hides the focus indicator around this component.
     * @param {?} isFocused
     * @return {?}
     */
    function (isFocused) {
    };
    /**
     * Programmatically draws a rectangle into this skin's Graphics object.
     * @param x
     * @param y
     * @param w
     * @param h
     * @param r
     * @param c
     * @param alpha
     * @param rot
     * @param gradient
     * @param ratios
     * @param hole
     */
    /**
     * Programmatically draws a rectangle into this skin's Graphics object.
     * @param {?} x
     * @param {?} y
     * @param {?} w
     * @param {?} h
     * @param {?=} r
     * @param {?=} c
     * @param {?=} alpha
     * @param {?=} rot
     * @param {?=} gradient
     * @param {?=} ratios
     * @param {?=} hole
     * @return {?}
     */
    UIComponent.prototype.drawRoundRect = /**
     * Programmatically draws a rectangle into this skin's Graphics object.
     * @param {?} x
     * @param {?} y
     * @param {?} w
     * @param {?} h
     * @param {?=} r
     * @param {?=} c
     * @param {?=} alpha
     * @param {?=} rot
     * @param {?=} gradient
     * @param {?=} ratios
     * @param {?=} hole
     * @return {?}
     */
    function (x, y, w, h, r, c, alpha, rot, gradient, ratios, hole) {
        if (r === void 0) { r = null; }
        if (c === void 0) { c = null; }
        if (alpha === void 0) { alpha = null; }
        if (rot === void 0) { rot = null; }
        if (gradient === void 0) { gradient = null; }
        if (ratios === void 0) { ratios = null; }
        if (hole === void 0) { hole = null; }
    };
    /**
     * Called by the effect instance when it stops playing on the component.
     * @param effectInst
     */
    /**
     * Called by the effect instance when it stops playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    UIComponent.prototype.effectFinished = /**
     * Called by the effect instance when it stops playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    function (effectInst) {
    };
    /**
     * Called by the effect instance when it starts playing on the component.
     * @param effectInst
     */
    /**
     * Called by the effect instance when it starts playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    UIComponent.prototype.effectStarted = /**
     * Called by the effect instance when it starts playing on the component.
     * @param {?} effectInst
     * @return {?}
     */
    function (effectInst) {
    };
    /**
     * Ends all currently playing effects on the component.
     */
    /**
     * Ends all currently playing effects on the component.
     * @return {?}
     */
    UIComponent.prototype.endEffectsStarted = /**
     * Ends all currently playing effects on the component.
     * @return {?}
     */
    function () {
    };
    /**
     * Executes all the bindings for which the UIComponent object is the destination.
     * @param recurse
     */
    /**
     * Executes all the bindings for which the UIComponent object is the destination.
     * @param {?=} recurse
     * @return {?}
     */
    UIComponent.prototype.executeBindings = /**
     * Executes all the bindings for which the UIComponent object is the destination.
     * @param {?=} recurse
     * @return {?}
     */
    function (recurse) {
        if (recurse === void 0) { recurse = false; }
    };
    /**
     * Called after printing is complete.
     * @param obj
     * @param target
     */
    /**
     * Called after printing is complete.
     * @param {?} obj
     * @param {?} target
     * @return {?}
     */
    UIComponent.prototype.finishPrint = /**
     * Called after printing is complete.
     * @param {?} obj
     * @param {?} target
     * @return {?}
     */
    function (obj, target) {
    };
    /**
     * Provides the automation object at the specified index.
     * @param index
     */
    /**
     * Provides the automation object at the specified index.
     * @param {?} index
     * @return {?}
     */
    UIComponent.prototype.getAutomationChildAt = /**
     * Provides the automation object at the specified index.
     * @param {?} index
     * @return {?}
     */
    function (index) {
    };
    /**
     * Provides the automation object list .
     */
    /**
     * Provides the automation object list .
     * @return {?}
     */
    UIComponent.prototype.getAutomationChildren = /**
     * Provides the automation object list .
     * @return {?}
     */
    function () {
    };
    /**
     * Returns the x coordinate of the element's bounds at the specified element size.
     * @param width
     * @param height
     * @param postLayoutTransform
     */
    /**
     * Returns the x coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getBoundsXAtSize = /**
     * Returns the x coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (width, height, postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the y coordinate of the element's bounds at the specified element size.
     * @param width
     * @param height
     * @param postLayoutTransform
     */
    /**
     * Returns the y coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getBoundsYAtSize = /**
     * Returns the y coordinate of the element's bounds at the specified element size.
     * @param {?} width
     * @param {?} height
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (width, height, postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Finds the type selectors for this UIComponent instance.
     */
    /**
     * Finds the type selectors for this UIComponent instance.
     * @return {?}
     */
    UIComponent.prototype.getClassStyleDeclarations = /**
     * Finds the type selectors for this UIComponent instance.
     * @return {?}
     */
    function () {
    };
    /**
     * Returns a layout constraint value, which is the same as getting the constraint style for this component.
     * @param constraintName
     */
    /**
     * Returns a layout constraint value, which is the same as getting the constraint style for this component.
     * @param {?} constraintName
     * @return {?}
     */
    UIComponent.prototype.getConstraintValue = /**
     * Returns a layout constraint value, which is the same as getting the constraint style for this component.
     * @param {?} constraintName
     * @return {?}
     */
    function (constraintName) {
    };
    /**
     * A convenience method for determining whether to use the explicit or measured height
     */
    /**
     * A convenience method for determining whether to use the explicit or measured height
     * @return {?}
     */
    UIComponent.prototype.getExplicitOrMeasuredHeight = /**
     * A convenience method for determining whether to use the explicit or measured height
     * @return {?}
     */
    function () {
    };
    /**
     * A convenience method for determining whether to use the explicit or measured width
     */
    /**
     * A convenience method for determining whether to use the explicit or measured width
     * @return {?}
     */
    UIComponent.prototype.getExplicitOrMeasuredWidth = /**
     * A convenience method for determining whether to use the explicit or measured width
     * @return {?}
     */
    function () {
    };
    /**
     * Gets the object that currently has focus.
     */
    /**
     * Gets the object that currently has focus.
     * @return {?}
     */
    UIComponent.prototype.getFocus = /**
     * Gets the object that currently has focus.
     * @return {?}
     */
    function () {
    };
    /**
     * Returns the element's layout height.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's layout height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getLayoutBoundsHeight = /**
     * Returns the element's layout height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's layout width.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's layout width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getLayoutBoundsWidth = /**
     * Returns the element's layout width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the x coordinate that the element uses to draw on screen.
     * @param postLayoutTransform
     */
    /**
     * Returns the x coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getLayoutBoundsX = /**
     * Returns the x coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the y coordinate that the element uses to draw on screen.
     * @param postLayoutTransform
     */
    /**
     * Returns the y coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getLayoutBoundsY = /**
     * Returns the y coordinate that the element uses to draw on screen.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the transform matrix that is used to calculate the component's layout relative to its siblings.
     */
    /**
     * Returns the transform matrix that is used to calculate the component's layout relative to its siblings.
     * @return {?}
     */
    UIComponent.prototype.getLayoutMatrix = /**
     * Returns the transform matrix that is used to calculate the component's layout relative to its siblings.
     * @return {?}
     */
    function () {
    };
    /**
     * Returns the layout transform Matrix3D for this element.
     */
    /**
     * Returns the layout transform Matrix3D for this element.
     * @return {?}
     */
    UIComponent.prototype.getLayoutMatrix3D = /**
     * Returns the layout transform Matrix3D for this element.
     * @return {?}
     */
    function () {
    };
    /**
     * Returns the element's maximum height.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's maximum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getMaxBoundsHeight = /**
     * Returns the element's maximum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's maximum width.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's maximum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getMaxBoundsWidth = /**
     * Returns the element's maximum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's minimum height.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's minimum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getMinBoundsHeight = /**
     * Returns the element's minimum height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's minimum width.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's minimum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getMinBoundsWidth = /**
     * Returns the element's minimum width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's preferred height.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's preferred height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getPreferredBoundsHeight = /**
     * Returns the element's preferred height.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the element's preferred width.
     * @param postLayoutTransform
     */
    /**
     * Returns the element's preferred width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    UIComponent.prototype.getPreferredBoundsWidth = /**
     * Returns the element's preferred width.
     * @param {?=} postLayoutTransform
     * @return {?}
     */
    function (postLayoutTransform) {
        if (postLayoutTransform === void 0) { postLayoutTransform = true; }
    };
    /**
     * Returns the item in the dataProvider that was used by the specified Repeater to produce this Repeater, or null if this Repeater isn't repeated.
     * @param whichRepeater
     */
    /**
     * Returns the item in the dataProvider that was used by the specified Repeater to produce this Repeater, or null if this Repeater isn't repeated.
     * @param {?=} whichRepeater
     * @return {?}
     */
    UIComponent.prototype.getRepeaterItem = /**
     * Returns the item in the dataProvider that was used by the specified Repeater to produce this Repeater, or null if this Repeater isn't repeated.
     * @param {?=} whichRepeater
     * @return {?}
     */
    function (whichRepeater) {
        if (whichRepeater === void 0) { whichRepeater = -1; }
    };
    /**
     * Gets a style property that has been set anywhere in this component's style lookup chain.
     * @param styleProp
     */
    /**
     * Gets a style property that has been set anywhere in this component's style lookup chain.
     * @param {?} styleProp
     * @return {?}
     */
    UIComponent.prototype.getStyle = /**
     * Gets a style property that has been set anywhere in this component's style lookup chain.
     * @param {?} styleProp
     * @return {?}
     */
    function (styleProp) {
    };
    /**
     * Converts a Point object from global to content coordinates.
     * @param point
     */
    /**
     * Converts a Point object from global to content coordinates.
     * @param {?} point
     * @return {?}
     */
    UIComponent.prototype.globalToContent = /**
     * Converts a Point object from global to content coordinates.
     * @param {?} point
     * @return {?}
     */
    function (point) {
    };
    /**
     * Returns true if currentCSSState is not null.
     */
    /**
     * Returns true if currentCSSState is not null.
     * @return {?}
     */
    UIComponent.prototype.hasCSSState = /**
     * Returns true if currentCSSState is not null.
     * @return {?}
     */
    function () {
    };
    /**
     * Determines whether the specified state has been defined on this UIComponent.
     * @param stateName
     */
    /**
     * Determines whether the specified state has been defined on this UIComponent.
     * @param {?} stateName
     * @return {?}
     */
    UIComponent.prototype.hasState = /**
     * Determines whether the specified state has been defined on this UIComponent.
     * @param {?} stateName
     * @return {?}
     */
    function (stateName) {
    };
    /**
     * This method will return the browser type.
     */
    /**
     * This method will return the browser type.
     * @return {?}
     */
    UIComponent.prototype.getBroserType = /**
     * This method will return the browser type.
     * @return {?}
     */
    function () {
        /** @type {?} */
        var isOpera = false;
        /** @type {?} */
        var isFirefox = false;
        /** @type {?} */
        var isSafari = false;
        /** @type {?} */
        var isIE = false;
        /** @type {?} */
        var isChrome = false;
        /** @type {?} */
        var isFirefox = false;
        if ((navigator.userAgent.indexOf("Opera") || navigator.userAgent.indexOf('OPR')) !== -1) {
            isOpera = true;
        }
        else if (navigator.userAgent.indexOf("Chrome") !== -1) {
            isChrome = true;
        }
        else if (navigator.userAgent.indexOf("Safari") !== -1) {
            isSafari = true;
        }
        else if (navigator.userAgent.indexOf("Firefox") !== -1) {
            isFirefox = true;
        }
        else if ((navigator.userAgent.indexOf("MSIE") !== -1) || (!!((/** @type {?} */ (document))).documentMode === true)) { //IF IE > 10
            isIE = true;
        }
        if (isOpera) {
            return Browsers.OPERA;
        }
        else if (isFirefox) {
            return Browsers.FIREFOX;
        }
        else if (isSafari) {
            return Browsers.SAFARI;
        }
        else if (isIE) {
            return Browsers.IE;
        }
        else if (isChrome) {
            return Browsers.CHROME;
        }
        else {
            return Browsers.UNKNOWN_BROWSER;
        }
    };
    /**
     * Returns a box Matrix which can be passed to the drawRoundRect() method as the rot parameter when drawing a horizontal gradient.
     * @param x
     * @param y
     * @param width
     * @param height
     */
    /**
     * Returns a box Matrix which can be passed to the drawRoundRect() method as the rot parameter when drawing a horizontal gradient.
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    UIComponent.prototype.horizontalGradientMatrix = /**
     * Returns a box Matrix which can be passed to the drawRoundRect() method as the rot parameter when drawing a horizontal gradient.
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    function (x, y, width, height) {
    };
    /**
     * Initializes the internal structure of this component.
     */
    /**
     * Initializes the internal structure of this component.
     * @return {?}
     */
    UIComponent.prototype.initialize = /**
     * Initializes the internal structure of this component.
     * @return {?}
     */
    function () {
    };
    /**
     *  Sets a style property on this component instance.
     *
     *  <p>This can override a style that was set globally.</p>
     *
     *  <p>Calling the <code>setStyle()</code> method can result in decreased performance.
     *  Use it only when necessary.</p>
     *
     *  @param styleProp Name of the style property.
     *
     *  @param newValue New value for the style.
     */
    /**
     *  Sets a style property on this component instance.
     *
     *  <p>This can override a style that was set globally.</p>
     *
     *  <p>Calling the <code>setStyle()</code> method can result in decreased performance.
     *  Use it only when necessary.</p>
     *
     * @param {?} styleProp Name of the style property.
     *
     * @param {?} newValue New value for the style.
     * @return {?}
     */
    UIComponent.prototype.setStyle = /**
     *  Sets a style property on this component instance.
     *
     *  <p>This can override a style that was set globally.</p>
     *
     *  <p>Calling the <code>setStyle()</code> method can result in decreased performance.
     *  Use it only when necessary.</p>
     *
     * @param {?} styleProp Name of the style property.
     *
     * @param {?} newValue New value for the style.
     * @return {?}
     */
    function (styleProp, newValue) {
        try {
            $(this.uielement).css(styleProp, newValue);
        }
        catch (error) {
            this.log.error("setStyle - error: ", error);
        }
    };
    /**
     *  Sets the focus to this component.
     *  The component can in turn pass focus to a subcomponent.
     *  <p><b>Note:</b> Only the TextInput and TextArea controls show a highlight
     *  when this method sets the focus.
     *  All controls show a highlight when the user tabs to the control.</p>
     */
    /**
     *  Sets the focus to this component.
     *  The component can in turn pass focus to a subcomponent.
     *  <p><b>Note:</b> Only the TextInput and TextArea controls show a highlight
     *  when this method sets the focus.
     *  All controls show a highlight when the user tabs to the control.</p>
     * @return {?}
     */
    UIComponent.prototype.setFocus = /**
     *  Sets the focus to this component.
     *  The component can in turn pass focus to a subcomponent.
     *  <p><b>Note:</b> Only the TextInput and TextArea controls show a highlight
     *  when this method sets the focus.
     *  All controls show a highlight when the user tabs to the control.</p>
     * @return {?}
     */
    function () {
    };
    /**
     *  Called when the <code>visible</code> property changes.
     *  Set the <code>visible</code> property to show or hide
     *  a component instead of calling this method directly.
     *
     *  @param value The new value of the <code>visible</code> property.
     *  Specify <code>true</code> to show the component, and <code>false</code> to hide it.
     *
     *  @param noEvent If <code>true</code>, do not dispatch an event.
     *  If <code>false</code>, dispatch a <code>show</code> event when
     *  the component becomes visible, and a <code>hide</code> event when
     *  the component becomes invisible.
     */
    /**
     *  Called when the <code>visible</code> property changes.
     *  Set the <code>visible</code> property to show or hide
     *  a component instead of calling this method directly.
     *
     * @param {?} value The new value of the <code>visible</code> property.
     *  Specify <code>true</code> to show the component, and <code>false</code> to hide it.
     *
     * @param {?=} noEvent If <code>true</code>, do not dispatch an event.
     *  If <code>false</code>, dispatch a <code>show</code> event when
     *  the component becomes visible, and a <code>hide</code> event when
     *  the component becomes invisible.
     * @return {?}
     */
    UIComponent.prototype.setVisible = /**
     *  Called when the <code>visible</code> property changes.
     *  Set the <code>visible</code> property to show or hide
     *  a component instead of calling this method directly.
     *
     * @param {?} value The new value of the <code>visible</code> property.
     *  Specify <code>true</code> to show the component, and <code>false</code> to hide it.
     *
     * @param {?=} noEvent If <code>true</code>, do not dispatch an event.
     *  If <code>false</code>, dispatch a <code>show</code> event when
     *  the component becomes visible, and a <code>hide</code> event when
     *  the component becomes invisible.
     * @return {?}
     */
    function (value, noEvent) {
        if (noEvent === void 0) { noEvent = false; }
    };
    /**
     * This method is used to add child to current
     * view.
     */
    /**
     * This method is used to add child to current
     * view.
     * @param {?} child
     * @return {?}
     */
    UIComponent.prototype.addChild = /**
     * This method is used to add child to current
     * view.
     * @param {?} child
     * @return {?}
     */
    function (child) {
        try {
        }
        catch (error) {
            this.log.error("addChild - error: ", error);
        }
    };
    /**
    * This method is used to add button events listeners.
    */
    /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    UIComponent.prototype.addEventsListenersForTooltip = /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    function (element) {
        var _this = this;
        // Subscribe to mouse enter event.
        if (this.mouseOver_.observers.length > 0 || this._mouseOver.name != 'anonymous') {
            this.addEventListener('mouseover', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseOver_.emit(event);
                _this.mouseOver();
            }));
        }
        // Subscribe to mouse leave/out event.
        if (this.mouseLeave_.observers.length > 0 || this.mouseOut_.observers.length > 0 || this._mouseLeave.name != 'anonymous' || this._mouseOut.name != 'anonymous') {
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseLeave_.emit(event);
                _this.mouseOut_.emit(event);
                _this.mouseLeave();
                _this.mouseOut();
            }));
        }
        // Subscribe to mouse down event.
        if (this.mouseDown_.observers.length > 0 || this._mouseDown.name != 'anonymous') {
            this.addEventListener('mousedown', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseDown_.emit(event);
                _this.mouseDown();
            }));
        }
        // Subscribe to mouse up event.
        if (this.mouseUp_.observers.length > 0 || this._mouseUp.name != 'anonymous') {
            this.addEventListener('mouseup', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseUp_.emit(event);
                _this.mouseUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseEnter_.observers.length > 0 || this.mouseIn_.observers.length > 0 || this._mouseEnter.name != 'anonymous' || this._mouseIn.name != 'anonymous') {
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseEnter_.emit(event);
                _this.mouseIn_.emit(event);
                _this.mouseIn();
                _this.mouseEnter();
            }));
        }
        // Subscribe to mouse move event.
        if (this.mouseMove_.observers.length > 0 || this._mouseMove.name != 'anonymous') {
            this.addEventListener('mousemove', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseMove_.emit(event);
                _this.mouseMove();
            }));
        }
    };
    /**
     * This method is used to add button events listeners.
     */
    /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    UIComponent.prototype.addAllOutputsEventsListeners = /**
     * This method is used to add button events listeners.
     * @param {?=} element
     * @return {?}
     */
    function (element) {
        var _this = this;
        // Subscribe to click event.
        if (this.onClick_.observers.length > 0 || this._click.name != 'anonymous') {
            // Attach Click event to the component.
            this.addEventListener('click', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.onClick_.emit(event);
                _this.click();
            }));
        }
        // Subscribe to click event.
        if (this.dbClick_.observers.length > 0 || this.doubleClick_.observers.length > 0 || this.itemDoubleClick_.observers.length > 0 ||
            this._doubleClick.name != 'anonymous' || this._dbClick.name != 'anonymous' || this.__itemDoubleClick.name != 'anonymous') {
            // Attach double click event to the component.
            this.addEventListener('dblclick', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.dbClick_.emit(event);
                _this.doubleClick_.emit(event);
                _this.itemDoubleClick_.emit(event);
                _this.dbClick();
                _this.doubleClick();
                _this.itemDoubleClick();
            }), true);
        }
        // Subscribe to focus event.
        if (this.focus_.observers.length > 0 || this.focusIn_.observers.length > 0 || this._focus.name != 'anonymous' || this._focusIn.name != 'anonymous') {
            this.addEventListener('focus', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.focus_.emit(event);
                _this.focusIn_.emit(event);
                _this.focus();
                _this.focusIn();
            }));
        }
        // Subscribe to focusout event.
        if (this.onFocusOut_.observers.length > 0 || this.keyFocusChange_.observers.length > 0 || this._focusOut.name != 'anonymous' || this._keyFocusChange.name != 'anonymous') {
            // Attach focusout event to the component.
            this.addEventListener('focusout', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.onFocusOut_.emit(event);
                _this.keyFocusChange_.emit(event);
                _this.focusOut();
                _this.keyFocusChange();
            }));
        }
        // Subscribe to key down event.
        if (this.getComponentName() != "SWTTEXTINPUT" && (this.onKeyDown_.observers.length > 0 || this._keyDown.name != 'anonymous')) {
            // Attach keyDown event to the component except SwtTextInput because it has its one event to handle restrict so we wont override it.
            this.addEventListener('keydown', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.onKeyDown_.emit(event);
                _this._keyDown;
            }));
        }
        // Subscribe to key up event.
        if (this.onKeyUp_.observers.length > 0 || this._keyUp.name != 'anonymous') {
            this.addEventListener('keyup', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.onKeyUp_.emit(event);
                _this.keyUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseOver_.observers.length > 0 || this._mouseOver.name != 'anonymous') {
            this.addEventListener('mouseover', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseOver_.emit(event);
                _this.mouseOver();
            }));
        }
        // Subscribe to mouse leave/out event.
        if (this.mouseLeave_.observers.length > 0 || this.mouseOut_.observers.length > 0 || this._mouseLeave.name != 'anonymous' || this._mouseOut.name != 'anonymous') {
            this.addEventListener('mouseleave', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseLeave_.emit(event);
                _this.mouseOut_.emit(event);
                _this.mouseLeave();
                _this.mouseOut();
            }));
        }
        // Subscribe to mouse down event.
        if (this.mouseDown_.observers.length > 0 || this._mouseDown.name != 'anonymous') {
            this.addEventListener('mousedown', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseDown_.emit(event);
                _this.mouseDown();
            }));
        }
        // Subscribe to mouse up event.
        if (this.mouseUp_.observers.length > 0 || this._mouseUp.name != 'anonymous') {
            this.addEventListener('mouseup', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseUp_.emit(event);
                _this.mouseUp();
            }));
        }
        // Subscribe to mouse enter event.
        if (this.mouseEnter_.observers.length > 0 || this.mouseIn_.observers.length > 0 || this._mouseEnter.name != 'anonymous' || this._mouseIn.name != 'anonymous') {
            this.addEventListener('mouseenter', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseEnter_.emit(event);
                _this.mouseIn_.emit(event);
                _this.mouseIn();
                _this.mouseEnter();
            }));
        }
        // Subscribe to mouse move event.
        if (this.mouseMove_.observers.length > 0 || this._mouseMove.name != 'anonymous') {
            this.addEventListener('mousemove', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.mouseMove_.emit(event);
                _this.mouseMove();
            }));
        }
        // Subscribe to change event.
        if (this.change_.observers.length > 0 || this._change.name != 'anonymous') {
            this.addEventListener('change', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.change_.emit(event);
                _this.change();
            }));
        }
        // Subscribe to scroll event.
        if (this.scroll_.observers.length > 0 || this._scroll.name != 'anonymous') {
            this.addEventListener('scroll', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                _this.scroll_.emit(event);
                _this.scroll();
            }));
        }
    };
    /**
     * Removes all handlers attached to the element.
     */
    /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    UIComponent.prototype.removeAllOuputsEventsListeners = /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    function (element) {
        if (element && element[0]) {
            // - Remove All native event Listeners of Zone.js.
            ((/** @type {?} */ (element)))[0].removeAllListeners('click');
            ((/** @type {?} */ (element)))[0].removeAllListeners('dblclick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('doubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('itemDoubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focus');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusIn');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusout');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusOut');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyFocusChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyup');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyUp');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseover');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseOver');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseleave');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseLeave');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mousedown');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseup');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseUp');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseenter');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseEnter');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mousemove');
            ((/** @type {?} */ (element)))[0].removeAllListeners('mouseMove');
            ((/** @type {?} */ (element)))[0].removeAllListeners('change');
            ((/** @type {?} */ (element)))[0].removeAllListeners('onSpyChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('onSpyNoChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('click');
            ((/** @type {?} */ (element)))[0].removeAllListeners('dblclick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('doubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('itemDoubleClick');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focus');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusIn');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusout');
            ((/** @type {?} */ (element)))[0].removeAllListeners('focusOut');
            ((/** @type {?} */ (element)))[0].removeAllListeners('keyFocusChange');
            ((/** @type {?} */ (element)))[0].removeAllListeners('scroll');
            if (!this.restrict) {
                // we should not remove keydown event handler on component having restrict.
                ((/** @type {?} */ (element)))[0].removeAllListeners('keydown');
            }
        }
        //console.log('[container] removeEventsListeners - hostElement .eventListeners() :',(this.hostElement  as any).eventListeners());
        //console.log('[container] removeEventsListeners - domElement  .eventListeners() :',(this.domElement  as any).eventListeners());
    };
    /**
     * @return {?}
     */
    UIComponent.prototype.getComponentName = /**
     * @return {?}
     */
    function () {
        return $(this.uielement.nativeElement) ? $(this.uielement.nativeElement)[0].tagName : undefined;
    };
    /**
     * This function used to adapt layout measure
     * it will append px if no % sign.
     * @param value
     * @param defaultValue
     */
    /**
     * This function used to adapt layout measure
     * it will append px if no % sign.
     * @param {?} value
     * @param {?=} defaultValue
     * @return {?}
     */
    UIComponent.prototype.adaptUnit = /**
     * This function used to adapt layout measure
     * it will append px if no % sign.
     * @param {?} value
     * @param {?=} defaultValue
     * @return {?}
     */
    function (value, defaultValue) {
        /** @type {?} */
        var rtn = "";
        if (value) {
            if (String(value).indexOf("%") === -1) {
                rtn = String(value) + "px";
            }
            else {
                rtn = String(value);
            }
            return rtn;
        }
        else {
            return (defaultValue ? (defaultValue == "auto" ? defaultValue : defaultValue + "px") : "0px");
        }
    };
    /**
     * convert entered value as string to boolean.
     * @param value
     */
    /**
     * convert entered value as string to boolean.
     * @param {?} value
     * @return {?}
     */
    UIComponent.prototype.adaptValueAsBoolean = /**
     * convert entered value as string to boolean.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                value = false;
            }
            else {
                value = true;
            }
        }
        return value;
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    UIComponent.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            //console.log('[UIComponent] ngOnDestroy');
            delete this._click;
            delete this._dbClick;
            delete this._doubleClick;
            delete this.__itemDoubleClick;
            delete this._keyDown;
            delete this._keyUp;
            delete this._mouseUp;
            delete this._mouseOver;
            delete this._mouseDown;
            delete this._mouseEnter;
            delete this._mouseLeave;
            delete this._mouseMove;
            delete this._mouseIn;
            delete this._focus;
            delete this._focusIn;
            delete this._focusOut;
            delete this.eventlist;
            delete this.cursorLocation;
            delete this.uielement;
            delete this.__commonService;
        }
        catch (error) {
            console.error('method [ngOnDestroy] error :', error);
        }
    };
    UIComponent.propDecorators = {
        maxChars: [{ type: Input }],
        restrict: [{ type: Input, args: ['restrict',] }],
        id: [{ type: Input, args: ['id',] }],
        onClick_: [{ type: Output, args: ["click",] }],
        dbClick_: [{ type: Output, args: ["dbClick",] }],
        doubleClick_: [{ type: Output, args: ["doubleClick",] }],
        itemDoubleClick_: [{ type: Output, args: ["itemDoubleClick",] }],
        onKeyDown_: [{ type: Output, args: ["keyDown",] }],
        onKeyUp_: [{ type: Output, args: ["keyUp",] }],
        mouseUp_: [{ type: Output, args: ["mouseUp",] }],
        mouseOver_: [{ type: Output, args: ["mouseOver",] }],
        mouseDown_: [{ type: Output, args: ["mouseDown",] }],
        mouseEnter_: [{ type: Output, args: ["mouseEnter",] }],
        mouseLeave_: [{ type: Output, args: ["mouseLeave",] }],
        mouseOut_: [{ type: Output, args: ["mouseOut",] }],
        mouseIn_: [{ type: Output, args: ["mouseIn",] }],
        mouseMove_: [{ type: Output, args: ["mouseMove",] }],
        focus_: [{ type: Output, args: ["focus",] }],
        focusIn_: [{ type: Output, args: ["focusIn",] }],
        onFocusOut_: [{ type: Output, args: ["focusOut",] }],
        keyFocusChange_: [{ type: Output, args: ["keyFocusChange",] }],
        change_: [{ type: Output, args: ["change",] }],
        onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
        onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
        scroll_: [{ type: Output, args: ['scroll',] }]
    };
    return UIComponent;
}(EventDispatcher));
export { UIComponent };
if (false) {
    /** @type {?} */
    UIComponent.prototype.name;
    /** @type {?} */
    UIComponent.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._id;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._restrict;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._maxChars;
    /** @type {?} */
    UIComponent.prototype.onClick_;
    /** @type {?} */
    UIComponent.prototype.dbClick_;
    /** @type {?} */
    UIComponent.prototype.doubleClick_;
    /** @type {?} */
    UIComponent.prototype.itemDoubleClick_;
    /** @type {?} */
    UIComponent.prototype.onKeyDown_;
    /** @type {?} */
    UIComponent.prototype.onKeyUp_;
    /** @type {?} */
    UIComponent.prototype.mouseUp_;
    /** @type {?} */
    UIComponent.prototype.mouseOver_;
    /** @type {?} */
    UIComponent.prototype.mouseDown_;
    /** @type {?} */
    UIComponent.prototype.mouseEnter_;
    /** @type {?} */
    UIComponent.prototype.mouseLeave_;
    /** @type {?} */
    UIComponent.prototype.mouseOut_;
    /** @type {?} */
    UIComponent.prototype.mouseIn_;
    /** @type {?} */
    UIComponent.prototype.mouseMove_;
    /** @type {?} */
    UIComponent.prototype.focus_;
    /** @type {?} */
    UIComponent.prototype.focusIn_;
    /** @type {?} */
    UIComponent.prototype.onFocusOut_;
    /** @type {?} */
    UIComponent.prototype.keyFocusChange_;
    /** @type {?} */
    UIComponent.prototype.change_;
    /** @type {?} */
    UIComponent.prototype.onSpyChange;
    /** @type {?} */
    UIComponent.prototype.onSpyNoChange;
    /** @type {?} */
    UIComponent.prototype.scroll_;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._click;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._dbClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._doubleClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.__itemDoubleClick;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyDown;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyUp;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseUp;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseOver;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseDown;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseEnter;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseLeave;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseOut;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseIn;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._mouseMove;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focus;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focusIn;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._focusOut;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._keyFocusChange;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._change;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype._scroll;
    /**
     * @type {?}
     * @protected
     */
    UIComponent.prototype.eventlist;
    /**
     * @type {?}
     * @protected
     */
    UIComponent.prototype.cursorLocation;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.uielement;
    /**
     * @type {?}
     * @private
     */
    UIComponent.prototype.__commonService;
}
/**
 * @record
 */
export function IUIComponent() { }
if (false) {
    /** @type {?} */
    IUIComponent.prototype.name;
    /** @type {?} */
    IUIComponent.prototype.id;
    /** @type {?} */
    IUIComponent.prototype.styleName;
}
//# sourceMappingURL=data:application/json;base64,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