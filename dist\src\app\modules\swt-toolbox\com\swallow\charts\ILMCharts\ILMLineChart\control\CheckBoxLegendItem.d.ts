import { OnInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { SwtCheckBox } from '../../../../controls/swt-checkbox.component';
import { HBox } from '../../../../controls/swt-hbox.component';
export declare class CheckBoxLegendItem extends Container implements OnInit {
    private elem;
    private commonService;
    private cd;
    protected hboxContainer: HBox;
    protected square: ElementRef;
    checkBox: SwtCheckBox;
    protected labelValue: ElementRef;
    getLiveValue(): string;
    private styleClassMap;
    liveValue: string;
    private _yField;
    private _selected;
    private _highlight;
    private _seriesStyle;
    private created;
    highlight: boolean;
    seriesStyle: string;
    checkboxChanged(event: any): void;
    ngOnInit(): void;
    yField: any;
    selected: boolean;
    constructor(elem: ElementRef, commonService: CommonService, cd: ChangeDetectorRef);
    /**
 * On click on the legend item label highlight the label and the line chart
 * */
    legendItemClicked(event: any): void;
}
