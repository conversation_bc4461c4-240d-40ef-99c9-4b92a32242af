/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { SwtUtil } from '../../../../utils/swt-util.service';
import { CommonService } from '../../../../utils/common.service';
import { SwtAlert } from '../../../../utils/swt-alert.service';
import { ExternalInterface } from '../../../../utils/external-interface.service';
import { SwtPopUpManager } from "../../../../managers/swt-pop-up-manager.service";
import { ConfigurableToolTip } from './ConfigurableToolTip';
var ProcessStatusBox = /** @class */ (function (_super) {
    tslib_1.__extends(ProcessStatusBox, _super);
    function ProcessStatusBox(commonService, element) {
        var _this = _super.call(this, element, commonService) || this;
        _this.commonService = commonService;
        _this.element = element;
        _this.redIcon = 'assets/images/red.png';
        _this.greenImage = 'assets/images/green.png';
        _this.amberIcon = 'assets/images/amber.png';
        _this.inProgress = 'assets/images/Gear.gif';
        _this.parentDocument = null;
        _this.GREEN_STATE = "green";
        _this.AMBER_STATE = "amber";
        _this.RED_STATE = "red";
        _this.customTooltip = null;
        _this.recalculateEnable = false;
        _this.state = _this.GREEN_STATE;
        _this.dataArray = [];
        _this.swtAlert = new SwtAlert(commonService);
        return _this;
    }
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        jQuery(this.image.nativeElement).mouseenter((/**
         * @return {?}
         */
        function () {
            _this.createTooltip();
        }));
    };
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.createTooltip = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var calclulatedXPosition = 0;
        /** @type {?} */
        var calclulatedYPosition = 0;
        /** @type {?} */
        var toolTipWidth = 400;
        this.customTooltip = SwtPopUpManager.createPopUp(parent, ConfigurableToolTip, {});
        this.customTooltip.enableResize = false;
        this.customTooltip.width = '' + toolTipWidth;
        this.customTooltip.height = "160";
        this.customTooltip.enableResize = false;
        this.customTooltip.showControls = false;
        // this.uploadWindow.initX = 10;
        // this.uploadWindow.initY = 10;
        /** @type {?} */
        var boxPosition = this.element.nativeElement.getBoundingClientRect();
        if (boxPosition.x) {
            calclulatedXPosition = boxPosition.x - (toolTipWidth - 30);
            calclulatedYPosition = boxPosition.y;
        }
        this.customTooltip.setWindowXY('' + calclulatedXPosition, '' + calclulatedYPosition);
        this.customTooltip.showHeader = false;
        this.customTooltip.dataArray = this.dataArray;
        this.customTooltip.parentDocument = this.parentDocument;
        this.customTooltip.processBox = this;
        this.customTooltip.recalculateEnable = this.recalculateEnable;
        this.customTooltip.display();
    };
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.removeTooltip = /**
     * @return {?}
     */
    function () {
        this.customTooltip.close();
    };
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.setCalculatingState = /**
     * @return {?}
     */
    function () {
        this.image.nativeElement.src = this.inProgress;
    };
    /**
     * @return {?}
     */
    ProcessStatusBox.prototype.setRed = /**
     * @return {?}
     */
    function () {
        this.image.nativeElement.src = this.redIcon;
    };
    /**
     * @param {?=} dataAsXML
     * @return {?}
     */
    ProcessStatusBox.prototype.setStyleFuction = /**
     * @param {?=} dataAsXML
     * @return {?}
     */
    function (dataAsXML) {
        if (dataAsXML === void 0) { dataAsXML = null; }
        this.dataArray = [];
        /** @type {?} */
        var recalculateState = false;
        /** @type {?} */
        var data = SwtUtil.convertObjectToArray(dataAsXML.element);
        for (var index = 0; index < data.length; index++) {
            /** @type {?} */
            var element = data[index];
            this.dataArray[element.name] = element.content;
        }
        if (this.dataArray["RECALCULATE"] == 'Y') {
            recalculateState = true;
        }
        if (this.parentDocument.isCalculationFinished == true) {
            if ((parseInt(this.dataArray["ACCOUNTS_INCOMPLETE"]) > 0) || (parseInt(this.dataArray["ACCOUNTS_INCONSISTENT"]) > 0)) {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.redIcon;
                if (this.state != this.RED_STATE) {
                    this.parentDocument.isRecalculateAlertShown = false;
                }
                //FIXME:CHECK IF WORKING
                this.parentDocument.recalculateData = recalculateState;
                this.parentDocument.showDataNotUpdatedAlert(recalculateState);
                this.state = this.RED_STATE;
                this.recalculateEnable = true;
            }
            else if (parseInt(this.dataArray["ACCOUNTS_NEW_DATA"]) > 0) {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.amberIcon;
                if (this.state != this.AMBER_STATE) {
                    this.parentDocument.isRecalculateAlertShown = false;
                }
                this.parentDocument.recalculateData = recalculateState;
                this.parentDocument.showDataNotUpdatedAlert(recalculateState);
                this.state = this.AMBER_STATE;
                this.recalculateEnable = true;
            }
            else {
                this.dataFromXMLParent = dataAsXML;
                this.image.nativeElement.src = this.greenImage;
                this.state = this.GREEN_STATE;
                this.recalculateEnable = false;
            }
            if (this.dataArray["RECALCULATE"] == "N") {
                if (!this.parentDocument.isRecalculateAlertShown) {
                    this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-NodataForSelection', 'The selected date is outside the retention period, No updated data will be displayed'));
                    this.parentDocument.isRecalculateAlertShown = true;
                }
            }
        }
    };
    ProcessStatusBox.decorators = [
        { type: Component, args: [{
                    selector: 'ProcessStatusBox',
                    template: "\n    <img class=\"processStatusBox\" #image src=\"assets/images/green.png\">\n  ",
                    styles: ["\n        .processStatusBox {\n                height:20px;\n                width : 20px;\n                margin: auto 0px;\n                box-shadow: 1px 1px 1px 1px #888888;\n                border: 1px solid white;\n        }\n      "]
                }] }
    ];
    /** @nocollapse */
    ProcessStatusBox.ctorParameters = function () { return [
        { type: CommonService },
        { type: ElementRef }
    ]; };
    ProcessStatusBox.propDecorators = {
        image: [{ type: ViewChild, args: ['image',] }]
    };
    return ProcessStatusBox;
}(Container));
export { ProcessStatusBox };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    ProcessStatusBox.prototype.image;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.redIcon;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.greenImage;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.amberIcon;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.inProgress;
    /** @type {?} */
    ProcessStatusBox.prototype.parentDocument;
    /** @type {?} */
    ProcessStatusBox.prototype.dataFromXMLParent;
    /** @type {?} */
    ProcessStatusBox.prototype.GREEN_STATE;
    /** @type {?} */
    ProcessStatusBox.prototype.AMBER_STATE;
    /** @type {?} */
    ProcessStatusBox.prototype.RED_STATE;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.customTooltip;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.recalculateEnable;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.state;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.swtAlert;
    /** @type {?} */
    ProcessStatusBox.prototype.dataArray;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ProcessStatusBox.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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