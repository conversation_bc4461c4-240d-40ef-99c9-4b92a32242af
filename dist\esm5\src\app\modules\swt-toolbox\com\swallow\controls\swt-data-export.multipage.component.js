/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component } from '@angular/core';
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
var select2 = require('select2');
import 'jquery-ui-dist/jquery-ui';
import { SwtDataExport } from './swt-data-export.component';
import { ExportInProgress } from './ExportInProgress';
import { SwtPagesToExport } from './PagesToExport';
import { ExportEvent } from '../events/swt-events.module';
import { Alert } from '../utils/alert.component';
import { StringUtils } from "../utils/string-utils.service";
import { ExternalInterface } from '../utils/external-interface.service';
var DataExportMultiPage = /** @class */ (function (_super) {
    tslib_1.__extends(DataExportMultiPage, _super);
    function DataExportMultiPage() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //Report type. CSV/Excel/PDF
        _this._reportType = null;
        _this._exportCancelFunction = new Function();
        //Export function (callback function)
        _this._exportFunction = new Function();
        _this._closePopupWindow = new Function();
        return _this;
    }
    /**
     * @return {?}
     */
    DataExportMultiPage.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.reportProgress = new ExportInProgress(this.elementReference, this.commonServiceRef);
        this.pagesToExport = new SwtPagesToExport(this.elementReference, this.commonServiceRef);
        this.pagesToExport.onexportFunction = this.showReportProgress.bind(this);
        this.pagesToExport.exportCancelFunction = this.closeCancelPopup.bind(this);
        this.reportProgress.exportCancelFunction = this._exportCancelFunction.bind(this);
    };
    Object.defineProperty(DataExportMultiPage.prototype, "exportCancelFunction", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportCancelFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._exportCancelFunction = value;
            this.reportProgress.exportCancelFunction = this._exportCancelFunction.bind(this);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DataExportMultiPage.prototype, "exportFunction", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._exportFunction = value;
            this.pagesToExport.onexportFunction = this.showReportProgress.bind(this);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DataExportMultiPage.prototype, "closePopupWindow", {
        get: /**
         * @return {?}
         */
        function () {
            return this._closePopupWindow;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._closePopupWindow = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    DataExportMultiPage.prototype.onDataExportClick = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this._reportType = this.typeFromIndex(this.exportDataComponent.selectedIndex);
        if (this._totalPages == 1)
            this.generateReport(this._currentPage, 1);
        else {
            // if (this.pagesToExport) {
            // 	//Popup is already created, set visibility to true
            // 	this.pagesToExport.visible = true;
            // } else {
            //Popup not yet created, so create the same
            this.createPopup();
            // }
            //Show popup
            // PopUpManager.centerPopUp(this._optionPopup);
            // PopUpManager.bringToFront(this._optionPopup);
            //Set default selection and focus
            // this._rbCurrentPage.selected = true;
            // this._rbCurrentPage.setFocus();
        }
        // this.createPopup();
    };
    /**
     * @return {?}
     */
    DataExportMultiPage.prototype.createPopup = /**
     * @return {?}
     */
    function () {
        this.pagesToExport.show(this);
    };
    /**
     * This function closes the option popup
     */
    /**
     * This function closes the option popup
     * @private
     * @return {?}
     */
    DataExportMultiPage.prototype.closePopup = /**
     * This function closes the option popup
     * @private
     * @return {?}
     */
    function () {
        this.pagesToExport.hide(this);
    };
    /**
     * @return {?}
     */
    DataExportMultiPage.prototype.showReportProgress = /**
     * @return {?}
     */
    function () {
        // this.reportProgress.show(this);
        // ExternalInterface.call('getBundle', 'text', 'label-exportMultiPages', 'First {0} pages will be exported.\nDo you want to continue?'), 
        if (this.pagesToExport.pagesToExport == "current") {
            this.generateReport(this._currentPage, 1);
        }
        else {
            if (this._totalPages > this._maxPages) {
                /** @type {?} */
                var confirmMsg = StringUtils.substitute(ExternalInterface.call('getBundle', 'text', 'label-exportMultiPages', 'First {0} pages will be exported.\nDo you want to continue?'), this._maxPages);
                // SwtAlert.getInstance().show(confirmMsg, "Confirm", 0x0003, null, onConfimClose, null, Alert.YES);	
                console.log("TCL: DataExportMultiPage -> showReportProgress -> confirmMsg", confirmMsg);
                this.SwtAlert.warning(confirmMsg, null, Alert.YES | Alert.NO, null, this.onConfimClose.bind(this), Alert.NO);
            }
            else {
                this.closePopup();
                this.generateReport(1, this._totalPages);
            }
        }
        // ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        // this._exportFunction();
    };
    /**
     * This function is used to generate report on click of
     * OK button on confim dialog
     *
     * @param event: CloseEvent
     */
    /**
     * This function is used to generate report on click of
     * OK button on confim dialog
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    DataExportMultiPage.prototype.onConfimClose = /**
     * This function is used to generate report on click of
     * OK button on confim dialog
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        //Close the popup window
        if (event.detail == Alert.YES) {
            this.closePopup();
            //pop up the progress bar window
            this.progresswinPopup();
            this.generateReport(1, this._maxPages);
        }
    };
    /**
     * @private
     * @param {?} startPage
     * @param {?} noOfPages
     * @return {?}
     */
    DataExportMultiPage.prototype.generateReport = /**
     * @private
     * @param {?} startPage
     * @param {?} noOfPages
     * @return {?}
     */
    function (startPage, noOfPages) {
        /** @type {?} */
        var exportEvent = new Object();
        exportEvent['type'] = this._reportType;
        exportEvent['startPage'] = startPage;
        exportEvent['noOfPages'] = noOfPages;
        ExportEvent.emit(exportEvent);
        if (this._exportFunction != null) {
            this._exportFunction(this._reportType, startPage, noOfPages);
        }
    };
    /**
     * @return {?}
     */
    DataExportMultiPage.prototype.closeCancelPopup = /**
     * @return {?}
     */
    function () {
        this.reportProgress.hide(this);
    };
    /**
         * This function is used to create option popup window
         */
    /**
     * This function is used to create option popup window
     * @return {?}
     */
    DataExportMultiPage.prototype.progresswinPopup = /**
     * This function is used to create option popup window
     * @return {?}
     */
    function () {
        this.reportProgress.show(this);
        /*var titleWindow:TitleWindow;
        titleWindow = PopUpManager.createPopUp(this, ExportInProgress, true) as TitleWindow;
        titleWindow.addEventListener("closeeee", onProgressCancelClick, false, 0, true);
        PopUpManager.centerPopUp(titleWindow);
*/
    };
    /**
     *This function is called whenever click on the cancel button in Progress bar window
     *@param event: MouseEvent
     */
    /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    DataExportMultiPage.prototype.onProgressCancelClick = /**
     * This function is called whenever click on the cancel button in Progress bar window
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.closeCancelPopup();
        this._exportCancelFunction(event);
    };
    Object.defineProperty(DataExportMultiPage.prototype, "currentPage", {
        /**
         * Getter function for currentPage
         */
        get: /**
         * Getter function for currentPage
         * @return {?}
         */
        function () {
            return this._currentPage;
        },
        ////////////////////////////////////////////////////////////////
        //	GETTERS & SETTERS
        ////////////////////////////////////////////////////////////////
        /**
         * Setter function for currentPage
         */
        set: 
        ////////////////////////////////////////////////////////////////
        //	GETTERS & SETTERS
        ////////////////////////////////////////////////////////////////
        /**
         * Setter function for currentPage
         * @param {?} currentPage
         * @return {?}
         */
        function (currentPage) {
            this._currentPage = currentPage;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DataExportMultiPage.prototype, "totalPages", {
        /**
         * Getter function for totalPages
         */
        get: /**
         * Getter function for totalPages
         * @return {?}
         */
        function () {
            return this._totalPages;
        },
        /**
         * Setter function for totalPages
         */
        set: /**
         * Setter function for totalPages
         * @param {?} totalPages
         * @return {?}
         */
        function (totalPages) {
            this._totalPages = totalPages;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DataExportMultiPage.prototype, "maxPages", {
        /**
         * Getter function for maxPages
         */
        get: /**
         * Getter function for maxPages
         * @return {?}
         */
        function () {
            return this._maxPages;
        },
        /**
         * Setter function for maxPages
         */
        set: /**
         * Setter function for maxPages
         * @param {?} maxPages
         * @return {?}
         */
        function (maxPages) {
            this._maxPages = maxPages;
        },
        enumerable: true,
        configurable: true
    });
    DataExportMultiPage.decorators = [
        { type: Component, args: [{
                    selector: 'DataExportMultiPage',
                    template: "\n  <div>\n\t\t\t<SwtComboBox id=\"exportDataComponent\" (inputClick)=\"onDataExportClick($event)\" \n\t\t\t(change)=\"onDataExportClick($event)\" editable=\"false\" #exportDataComponent width=\"43\"></SwtComboBox>\n  </div>\n  ",
                    styles: ["\n  "]
                }] }
    ];
    return DataExportMultiPage;
}(SwtDataExport));
export { DataExportMultiPage };
if (false) {
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._reportType;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._currentPage;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._totalPages;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._maxPages;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype.reportProgress;
    /** @type {?} */
    DataExportMultiPage.prototype.pagesToExport;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._exportCancelFunction;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._exportFunction;
    /**
     * @type {?}
     * @private
     */
    DataExportMultiPage.prototype._closePopupWindow;
}
//# sourceMappingURL=data:application/json;base64,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