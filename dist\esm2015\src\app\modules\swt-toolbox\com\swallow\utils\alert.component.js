/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, EventEmitter } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "./common.service";
import { Logger } from "../logging/logger.service";
/** @type {?} */
const $ = require('jquery');
export class Alert {
    /**
     * @param {?} common
     */
    constructor(common) {
        this.common = common;
        this.flags = 4;
        this.onClose = new EventEmitter();
        this.warningMsg = "Warning Alert";
        this.errorMsg = "Error Alert";
        this.confirmMsg = "Confirm Alert";
        this.invalidMsg = "Invalid Alert";
        this.infoMsg = "Info Alert";
        this.defaultButtonFlag = 4;
        this.alertShown = false;
        this.hash = "";
        this.logger = new Logger("Alert", common.httpclient, 6);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.logger.info("[ ngOnInit ] method START");
        try {
            $(".alert-content").draggable({ handle: ".alert-heading" });
            setTimeout((/**
             * @return {?}
             */
            () => {
                $(".alert-button").focus();
            }), 0);
        }
        catch (error) {
            this.logger.error("[ ngOnInit ] method - error ", error);
        }
        this.logger.info("[ ngOnInit ] method END");
    }
    /**
     * This method is used to handle the alert close
     * @param {?=} flag
     * @param {?=} count
     * @return {?}
     */
    destroy(flag, count) {
        this.logger.info("[ destroy ] method START");
        try {
            /** @type {?} */
            let btn = 0;
            if (flag) {
                btn = this.getbuttonNumber(flag);
            }
            this.onClose.emit({ detail: btn });
            this.windowManager.close(this.alertId);
        }
        catch (error) {
            this.logger.error("[ destroy ] method - error ", error);
        }
        this.logger.info("[ destroy ] method END");
    }
    /**
     * this method is used to populate the alert dialogue.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    show(text = "", title = "", flags /* Alert.OK */, parent, closeHandler, iconClass, defaultButtonFlag = 4 /* Alert.OK */) {
        this.logger.info("[ show ] method START");
        try {
            /** @type {?} */
            let temphash = "" + text + flags + closeHandler;
            if (!this.alertShown || (this.alertShown && temphash != this.hash)) {
                /** @type {?} */
                const alert = this.common.windowManager.createAlert();
                //Commented for Predict as they don't wont alert title
                // alert.title = title;
                alert.title = "";
                alert.iconClass = iconClass;
                alert.texthtml = text.replace(/(\\n|\n)/g, '<br>');
                alert.buttonFlags = flags ? this.getButtonLabel(flags) : this.getButtonLabel(4);
                alert.onClose.subscribe((/**
                 * @param {?} result
                 * @return {?}
                 */
                (result) => {
                    this.alertShown = false;
                    this.hash = null;
                    if (closeHandler) {
                        this.alertShown = false;
                        this.hash = null;
                        closeHandler(result);
                    }
                }));
                this.hash = "" + text + flags + closeHandler;
                this.alertShown = true;
                return alert;
            }
            else {
                return null;
            }
        }
        catch (error) {
            this.logger.error("[ show ] method - error ", error);
        }
        this.logger.info("[ show ] method END");
    }
    /**
     * this method is used to covert the given flag (type number)
     * to the appropriate button label.
     * @protected
     * @param {?} value
     * @return {?}
     */
    getButtonLabel(value) {
        this.logger.info("[ getButtonLabel ] method START");
        try {
            this.logger.info("[ getButtonLabel ] method END");
            /** @type {?} */
            const buttons = { Yes: 1, No: 2, Ok: 4, Canel: 8 };
            /** @type {?} */
            const labels = new Array();
            value & 1 ? labels.push(Alert.yesLabel) : null;
            value & 2 ? labels.push(Alert.noLabel) : null;
            value & 4 ? labels.push(Alert.okLabel) : null;
            value & 8 ? labels.push(Alert.cancelLabel) : null;
            return labels;
        }
        catch (error) {
            this.logger.error("[ getButtonLabel ] method - error ", error);
        }
    }
    /**
     * @private
     * @param {?} label
     * @return {?}
     */
    getbuttonNumber(label) {
        this.logger.info("[ getbuttonNumber ] method START");
        try {
            switch (label.toUpperCase()) {
                case Alert.okLabel.toUpperCase():
                    return Alert.OK;
                case Alert.yesLabel.toUpperCase():
                    return Alert.YES;
                case Alert.noLabel.toUpperCase():
                    return Alert.NO;
                case Alert.cancelLabel.toUpperCase():
                    return Alert.CANCEL;
            }
        }
        catch (error) {
            this.logger.error("[ getbuttonNumber ] method - error ", error);
        }
        this.logger.info("[ getbuttonNumber ] method END");
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set text(value) {
        this.texthtml = value;
    }
    /**
     * @return {?}
     */
    get text() {
        return this.texthtml;
    }
}
Alert.YES = 1;
Alert.NO = 2;
Alert.OK = 4;
Alert.CANCEL = 8;
Alert.yesLabel = "Yes";
Alert.noLabel = "No";
Alert.okLabel = "Ok";
Alert.cancelLabel = "Cancel";
Alert.decorators = [
    { type: Component, args: [{
                selector: 'swt-alert',
                template: `
        <div class="alert-overlay">
            <div class="alert-content">
                <div class="alert-heading">
                    <span [innerHTML]="title"></span>
                </div>
                <div class="alert-body">
                    <div class="alert-message">
                        <div class="image">
                            <img src="{{ iconClass }}" alt="{{ title }}"/>
                        </div>
                        <div class="msg" [innerHTML]="texthtml"></div>
                    </div>
                    <div class="alert-btn">
                        <button class="alert-button" *ngFor="let flag of buttonFlags let count = index" (click)="destroy(flag, count)">
                            {{ flag }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `,
                providers: [CommonService]
            }] }
];
/** @nocollapse */
Alert.ctorParameters = () => [
    { type: CommonService }
];
if (false) {
    /** @type {?} */
    Alert.YES;
    /** @type {?} */
    Alert.NO;
    /** @type {?} */
    Alert.OK;
    /** @type {?} */
    Alert.CANCEL;
    /** @type {?} */
    Alert.yesLabel;
    /** @type {?} */
    Alert.noLabel;
    /** @type {?} */
    Alert.okLabel;
    /** @type {?} */
    Alert.cancelLabel;
    /** @type {?} */
    Alert.prototype.buttonFlags;
    /** @type {?} */
    Alert.prototype.texthtml;
    /** @type {?} */
    Alert.prototype.title;
    /** @type {?} */
    Alert.prototype.iconClass;
    /** @type {?} */
    Alert.prototype.flags;
    /** @type {?} */
    Alert.prototype.windowManager;
    /** @type {?} */
    Alert.prototype.alertId;
    /** @type {?} */
    Alert.prototype.onClose;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.warningMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.errorMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.confirmMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.invalidMsg;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.infoMsg;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.defaultButtonFlag;
    /**
     * @type {?}
     * @protected
     */
    Alert.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.alertShown;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.hash;
    /**
     * @type {?}
     * @private
     */
    Alert.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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