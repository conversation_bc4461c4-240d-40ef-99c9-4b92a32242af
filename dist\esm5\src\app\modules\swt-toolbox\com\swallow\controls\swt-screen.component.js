/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component } from '@angular/core';
var SwtScreen = /** @class */ (function () {
    function SwtScreen() {
    }
    /**
     * @return {?}
     */
    SwtScreen.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    SwtScreen.decorators = [
        { type: Component, args: [{
                    selector: 'SwtScreen',
                    template: "\n    <div class=\"screen\">\n          <ng-content></ng-content>\n    </div>\n  ",
                    styles: ["\n           .screen {\n               background-color: #ccecff;\n               width: 100%;\n               height: 100%;\n               padding: 10px;\n           }\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtScreen.ctorParameters = function () { return []; };
    return SwtScreen;
}());
export { SwtScreen };
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LXNjcmVlbi5jb21wb25lbnQuanMiLCJzb3VyY2VSb290Ijoibmc6Ly9zd3QtdG9vbC1ib3gvIiwic291cmNlcyI6WyJzcmMvYXBwL21vZHVsZXMvc3d0LXRvb2xib3gvY29tL3N3YWxsb3cvY29udHJvbHMvc3d0LXNjcmVlbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE9BQU8sRUFBRSxTQUFTLEVBQVUsTUFBTSxlQUFlLENBQUM7QUFFbEQ7SUFrQkU7SUFBZ0IsQ0FBQzs7OztJQUVqQiw0QkFBUTs7O0lBQVI7SUFDQSxDQUFDOztnQkFyQkYsU0FBUyxTQUFDO29CQUNULFFBQVEsRUFBRSxXQUFXO29CQUNyQixRQUFRLEVBQUUsbUZBSVQ7NkJBQ1EsK0tBT1I7aUJBQ0Y7Ozs7SUFRRCxnQkFBQztDQUFBLEFBdkJELElBdUJDO1NBUFksU0FBUyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudCwgT25Jbml0IH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcblxyXG5AQ29tcG9uZW50KHtcclxuICBzZWxlY3RvcjogJ1N3dFNjcmVlbicsXHJcbiAgdGVtcGxhdGU6IGBcclxuICAgIDxkaXYgY2xhc3M9XCJzY3JlZW5cIj5cclxuICAgICAgICAgIDxuZy1jb250ZW50PjwvbmctY29udGVudD5cclxuICAgIDwvZGl2PlxyXG4gIGAsXHJcbiAgc3R5bGVzOiBbYFxyXG4gICAgICAgICAgIC5zY3JlZW4ge1xyXG4gICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjY2NlY2ZmO1xyXG4gICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgICAgICAgIH1cclxuICBgXVxyXG59KVxyXG5leHBvcnQgY2xhc3MgU3d0U2NyZWVuIGltcGxlbWVudHMgT25Jbml0IHtcclxuXHJcbiAgY29uc3RydWN0b3IoKSB7IH1cclxuXHJcbiAgbmdPbkluaXQoKSB7XHJcbiAgfVxyXG5cclxufVxyXG4iXX0=