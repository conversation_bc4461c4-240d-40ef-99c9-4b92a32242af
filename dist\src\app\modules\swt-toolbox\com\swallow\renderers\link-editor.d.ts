import { Editor } from 'angular-slickgrid';
import 'jquery-ui-dist/jquery-ui';
export declare class LinkEditor implements Editor {
    private args;
    private commonGrid;
    $input: any;
    defaultValue: any;
    private logger;
    constructor(args: any);
    init(): void;
    focus(): void;
    getValue(): any;
    setValue(val: string): void;
    loadValue(item: any): void;
    save(): void;
    serializeValue(): any;
    applyValue(item: any, state: any): void;
    isValueChanged(): boolean;
    validate(): {
        valid: boolean;
        msg: any;
    };
    destroy(): void;
}
