import { <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { IItemRender } from "./iitem-render";
import { CommonService } from "../../utils/common.service";
export declare class NumberItemRender extends UIComponent implements OnInit, IItemRender, OnDestroy {
    private numberelement;
    private common;
    text: string;
    color: string;
    type: string;
    id: number;
    constructor(numberelement: ElementRef, common: CommonService);
    ngOnDestroy(): void;
    ngOnInit(): void;
}
