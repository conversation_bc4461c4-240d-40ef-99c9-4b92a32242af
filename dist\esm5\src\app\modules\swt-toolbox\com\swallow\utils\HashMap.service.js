/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
var HashMap = /** @class */ (function () {
    //HashMap constructor.
    function HashMap() {
        // initialize keys
        this.keys = new Array();
        // initialize values.
        this.values = new Array();
    }
    /**
     * This method is used to check if HashMap contain given key.
     * @param key
     */
    /**
     * This method is used to check if HashMap contain given key.
     * @param {?} key
     * @return {?}
     */
    HashMap.prototype.containsKey = /**
     * This method is used to check if HashMap contain given key.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        return (this.findKey(key) > -1);
    };
    /**
     * This method is used to check if HashMap contain given value.
     * @param value
     */
    /**
     * This method is used to check if HashMap contain given value.
     * @param {?} value
     * @return {?}
     */
    HashMap.prototype.containsValue = /**
     * This method is used to check if HashMap contain given value.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return (this.values.indexOf(value) > -1);
    };
    /**
     * This method is used to return HashMap keys.
     */
    /**
     * This method is used to return HashMap keys.
     * @return {?}
     */
    HashMap.prototype.getKeys = /**
     * This method is used to return HashMap keys.
     * @return {?}
     */
    function () {
        return (this.keys.slice());
    };
    /**
     * This method is used to return HashMap values.
     */
    /**
     * This method is used to return HashMap values.
     * @return {?}
     */
    HashMap.prototype.getValues = /**
     * This method is used to return HashMap values.
     * @return {?}
     */
    function () {
        return (this.values.slice());
    };
    /**
     * This method is used to return value of given key.
     * @param key
     */
    /**
     * This method is used to return value of given key.
     * @param {?} key
     * @return {?}
     */
    HashMap.prototype.getValue = /**
     * This method is used to return value of given key.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        return (this.values[this.keys.indexOf(key)] ? this.values[this.keys.indexOf(key)] : null);
    };
    /**
     * This method is used to get value as object.
     * @param key
     */
    /**
     * This method is used to get value as object.
     * @param {?} key
     * @return {?}
     */
    HashMap.prototype.getValueAsObject = /**
     * This method is used to get value as object.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        return (this.values[this.keys.indexOf(key)]);
    };
    /**
     * This method is used to get key of given value.
     * @param value
     */
    /**
     * This method is used to get key of given value.
     * @param {?} value
     * @return {?}
     */
    HashMap.prototype.getKey = /**
     * This method is used to get key of given value.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return (this.keys[this.values.indexOf(value)]);
    };
    /**
     * This method is used to add new entry to HashMap.
     * @param key
     * @param value
     */
    /**
     * This method is used to add new entry to HashMap.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    HashMap.prototype.put = /**
     * This method is used to add new entry to HashMap.
     * @param {?} key
     * @param {?} value
     * @return {?}
     */
    function (key, value) {
        /** @type {?} */
        var oldKey;
        /** @type {?} */
        var theKey = this.keys.indexOf(key);
        if (theKey < 0) {
            this.keys.push(key);
            this.values.push(value !== undefined && value !== '' && value !== null ? value : '');
        }
        else {
            this.values[theKey] = value !== value !== undefined && value !== '' && value !== null ? value : '';
        }
    };
    /**
     * This method is used to add more then one entry to HashMap.
     * @param map
     */
    /**
     * This method is used to add more then one entry to HashMap.
     * @param {?} map
     * @return {?}
     */
    HashMap.prototype.putAll = /**
     * This method is used to add more then one entry to HashMap.
     * @param {?} map
     * @return {?}
     */
    function (map) {
        for (var key in map) {
            if (map.hasOwnProperty(key)) {
                this.keys.push(key);
                this.values.push(map[key]);
            }
        }
    };
    /**
    * This method is used to clear the HashMap.
    */
    /**
     * This method is used to clear the HashMap.
     * @return {?}
     */
    HashMap.prototype.clear = /**
     * This method is used to clear the HashMap.
     * @return {?}
     */
    function () {
        this.keys = new Array();
        this.values = new Array();
    };
    /**
     * This method is used to remove HashMap entry with given key.
     * @param key
     */
    /**
     * This method is used to remove HashMap entry with given key.
     * @param {?} key
     * @return {?}
     */
    HashMap.prototype.remove = /**
     * This method is used to remove HashMap entry with given key.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        /** @type {?} */
        var theKey = this.keys.indexOf(key);
        if (theKey > -1) {
            /** @type {?} */
            var theValue = this.values[theKey];
            this.values.splice(theKey, 1);
            this.keys.splice(theKey, 1);
        }
    };
    /**
     * This method is used to get HashMap size.
     */
    /**
     * This method is used to get HashMap size.
     * @return {?}
     */
    HashMap.prototype.size = /**
     * This method is used to get HashMap size.
     * @return {?}
     */
    function () {
        return (this.keys.length);
    };
    /**
     * This method is used to check if HashMap is empty or not.
     */
    /**
     * This method is used to check if HashMap is empty or not.
     * @return {?}
     */
    HashMap.prototype.isEmpty = /**
     * This method is used to check if HashMap is empty or not.
     * @return {?}
     */
    function () {
        return (this.size() < 1);
    };
    /**
     * This method is used to find given key.
     * @param key
     */
    /**
     * This method is used to find given key.
     * @param {?} key
     * @return {?}
     */
    HashMap.prototype.findKey = /**
     * This method is used to find given key.
     * @param {?} key
     * @return {?}
     */
    function (key) {
        return (this.keys.indexOf(key));
    };
    /**
     * This method is used to find given value.
     * @param value
     */
    /**
     * This method is used to find given value.
     * @param {?} value
     * @return {?}
     */
    HashMap.prototype.findValue = /**
     * This method is used to find given value.
     * @param {?} value
     * @return {?}
     */
    function (value) {
        return (this.values.indexOf(value));
    };
    HashMap.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    HashMap.ctorParameters = function () { return []; };
    return HashMap;
}());
export { HashMap };
if (false) {
    /**
     * @type {?}
     * @private
     */
    HashMap.prototype.keys;
    /**
     * @type {?}
     * @private
     */
    HashMap.prototype.values;
}
//# sourceMappingURL=data:application/json;base64,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