export declare class BaseObject {
    constructor();
    /**
     * This method is used to bind attribute to the current object.
     * @param name
     * @param value
     */
    bindAttribute(name: string, value: string): void;
    /**
     * This method is used to clone the current object.
     * @param source
     */
    clone(source: any): any;
    /**
     * This method is used to rename an attribute of the current object
     * @param Oldattr
     * @param newAttr
     */
    renameAttr(obj: object, oldAttr: string, newAttr: string): object;
    /**
     * This method extract numbers from given string.
     * @param str
     */
    getNumberFrom(str: string): number;
}
