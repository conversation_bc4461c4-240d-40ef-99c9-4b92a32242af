/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
import { Logger } from '../logging/logger.service';
import { HttpClient } from '@angular/common/http';
/** @type {?} */
const $ = require('jquery');
/**
 * Custom pagination component: It allows editing the page number manually
 *  << < Page [1] of 5 > >>
 *
 * <AUTHOR> Chebka, saber.chebka\@gmail.com
 */
export class SwtCommonGridPagination {
    /**
     * @param {?} httpClient
     * @param {?} elem
     */
    constructor(httpClient, elem) {
        this.httpClient = httpClient;
        this.elem = elem;
        this.logger = null;
        this.maximum = 0;
        this.minimum_ = 0;
        this.value = 0;
        this.horizontalAlign = "right";
        this.totalItems = 0;
        this.processing = false;
        this.styleObject = [];
        // Reference to the real pagination component
        this.realPagination = true;
        // -------------------------------------------------------------------------------------------------------------------- //
        this._enabled = true;
        this.logger = new Logger('Grid-pagination', httpClient, 0);
        this.logger.info('method [constructor] - START/END');
    }
    /**
     * @param {?} gridPaginationOptions
     * @return {?}
     */
    set gridPaginationOptions(gridPaginationOptions) {
        this.logger.info('method [set gridPaginationOptions] - START');
        this._gridPaginationOptions = gridPaginationOptions;
        // The backendServiceApi is itself the SwtCommonGridComponent (This is a hack)
        //        this.commonGrid = <SwtCommonGrid>this.gridPaginationOptions.backendServiceApi.service;
        this.commonGrid = (/** @type {?} */ (this.gridPaginationOptions['grid']));
        this.logger.info('method [set gridPaginationOptions] - END');
    }
    /**
     * @return {?}
     */
    get gridPaginationOptions() {
        return this._gridPaginationOptions;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.logger.info('init: ');
    }
    /**
     * @return {?}
     */
    ngAfterContentChecked() {
        this.styleObject = {
            'float': this.horizontalAlign,
            'margin-left': this.marginLeft,
            'margin-right': this.marginRight,
        };
    }
    /**
     * @param {?} $v
     * @return {?}
     */
    set minimum($v) {
        this.minimum_ = $v;
        if (!this.value)
            this.value = this.minimum_;
    }
    /**
     * @return {?}
     */
    get minimum() {
        return this.minimum_;
    }
    /**
     * @param {?} event
     * @return {?}
     */
    changeToFirstPage(event) {
        this.logger.info('method [changeToFirstPage] - START/END');
        this.value = 1;
        this.onPageChanged(event, this.value);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    changeToLastPage(event) {
        this.logger.info('method [changeToLastPage] - START/END');
        this.value = this.maximum;
        this.onPageChanged(event, this.value);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    changeToNextPage(event) {
        this.logger.info('method [changeToNextPage] - START/END');
        if (this.value < this.maximum) {
            this.value++;
            this.onPageChanged(event, this.value);
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    changeToPreviousPage(event) {
        this.logger.info('method [changeToNextPage] - START/END');
        if (this.value > 1) {
            this.value--;
            this.onPageChanged(event, this.value);
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    changeToCurrentPage(event) {
        this.logger.info('method [changeToCurrentPage] - START -  value', event.currentTarget.value);
        if (Number(event.currentTarget.value) < 1) {
            event.currentTarget.value = 1;
        }
        else if (Number(event.currentTarget.value) > Number(this.maximum)) {
            event.currentTarget.value = this.maximum;
        }
        else {
            this.value = event.currentTarget.value;
        }
        this.value = event.currentTarget.value;
        this.onPageChanged(event, this.value);
        this.logger.info('method [changeToCurrentPage] -  END - current page :', this.value);
    }
    /**
     * @param {?=} event
     * @param {?=} pageNumber
     * @return {?}
     */
    onPageChanged(event, pageNumber) {
        this.logger.info('method [onPageChanged] - START/END', this.commonGrid);
        this.commonGrid.processOnPaginationChanged(event, { newPage: pageNumber, pageSize: -1 });
        this.commonGrid.selectedIndex = -1;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === 'string') {
            value === 'true' ? this._enabled = true : null;
            value === 'false' ? this._enabled = false : null;
            if (value == 'false') {
                $($(this.elem.nativeElement)[0].children[0]).addClass("disabled-container");
            }
            else {
                $($(this.elem.nativeElement)[0].children[0]).removeClass("disabled-container");
            }
        }
        else {
            setTimeout((/**
             * @return {?}
             */
            () => {
                if (value == false) {
                    this._enabled = false;
                    $($(this.elem.nativeElement)[0].children[0]).addClass("disabled-container");
                }
                else {
                    this._enabled = true;
                    $($(this.elem.nativeElement)[0].children[0]).removeClass("disabled-container");
                }
            }), 0);
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
}
SwtCommonGridPagination.decorators = [
    { type: Component, args: [{
                selector: 'SwtCommonGridPagination',
                template: `
        <div class="slick-pagination">
            <div class="slick-pagination-nav"  [ngStyle]="styleObject" >
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <li class="page-item" [ngClass]="value <= 1 ? 'disabled' : ''">
                            <a class="page-link icon-seek-first fa fa-angle-double-left"
                               aria-label="First" (click)="changeToFirstPage($event)"> </a>
                        </li>
                        <li class="page-item" [ngClass]="value <= 1 ? 'disabled' : ''">
                            <a class="page-link icon-seek-prev fa fa-angle-left"
                               aria-label="Previous" (click)="changeToPreviousPage($event)"> </a>
                        </li>
                    </ul>
                </nav>

                <div class="slick-page-number">
                    <input  type="number" value="{{value}}" size="1" (keyup.enter)="changeToCurrentPage($event)" (change)="changeToCurrentPage($event)" >
                    <span style="padding-left: 2px" [translate]="'OF'"></span><span> {{maximum}}</span>
                </div>

                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <li class="page-item CGP-next-page"
                            [ngClass]="value === maximum ? 'disabled' : ''"><a
                                class="page-link icon-seek-next text-center fa fa-lg fa-angle-right"
                                aria-label="Next" (click)="changeToNextPage($event)"> </a></li>
                        <li class="page-item CGP-end-page"
                            [ngClass]="value === maximum ? 'disabled' : ''"><a
                                class="page-link icon-seek-end fa fa-lg fa-angle-double-right"
                                aria-label="Last" (click)="changeToLastPage($event)"> </a></li>
                    </ul>
                </nav>
            </div>
        </div>
    `,
                styles: [`
        .slick-pagination {
            padding: 0px !important;
            height: 24px;
            width:auto;
            padding: 2px;
            border-radius: 5px;
            margin: 0 5px 0 5px;
        }
        .slick-page-number {
            border: 1px solid #ADCCE3;
            height:23px;
            line-height:23px;
            color:black;
        }
        .slick-pagination .slick-pagination-nav {
            padding: 0px !important;
            height: 24px!important;
        }
        .page-spin {
            border: none;
            height: 23px;
            width: 23px;
            background-color: transparent;
            cursor: default;
            animation: fa-spin 1.2s infinite linear !important;
        }
        .page-spin:hover {
            background-color: transparent;
        }
        .slick-pagination .slick-pagination-nav .pagination .page-link {
            height: 23px;
            padding-right: 6px; 
            height: 23px; 
            padding-top: 3px; 
            padding-bottom: 3px;
        }
        .pagination>li>a, .pagination>li>span {
            padding: 3px 6px;
            border: none;
            border-radius: 0px;
            background-image: -webkit-linear-gradient(left, #E3F5FF, #ADCCE3);
            background-image: -moz-linear-gradient(left, #E3F5FF, #ADCCE3);
            background-image: -ms-linear-gradient(left, #E3F5FF, #ADCCE3);
            background-image: -o-linear-gradient(left, #E3F5FF, #ADCCE3);
            background-image: linear-gradient(to bottom, #E3F5FF, #ADCCE3);
            cursor: default;
            border: 1px solid #ADCCE3;
            color: #173553;
        }
        .slick-pagination .slick-pagination-nav .slick-page-number input {
            background-color: white;
            height: 21px;
            width: 34px;
            padding: 2px;
            display: inline-block;
            text-align: center;
            border-top:1px solid  #4C5E6F;
            margin-top: 2px;
            margin-bottom: 2px;
        }
        .slick-pagination .slick-pagination-nav .slick-page-number input:focus {
            outline: none;
        }
        .slick-pagination .slick-pagination-nav .slick-page-number {
            vertical-align: top;
            margin-top: 0px;
            display: inline-block;
            padding: 0 5px;
        }
        .slick-page-number{
            line-height : 0px!important;
            border: none;
        }
    `]
            }] }
];
/** @nocollapse */
SwtCommonGridPagination.ctorParameters = () => [
    { type: HttpClient },
    { type: ElementRef }
];
SwtCommonGridPagination.propDecorators = {
    maximum: [{ type: Input, args: ['maximum',] }],
    minimum_: [{ type: Input, args: ['minimum',] }],
    value: [{ type: Input, args: ['value',] }],
    horizontalAlign: [{ type: Input }],
    marginLeft: [{ type: Input }],
    marginRight: [{ type: Input }],
    gridPaginationOptions: [{ type: Input }],
    enabled: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.logger;
    /** @type {?} */
    SwtCommonGridPagination.prototype.maximum;
    /** @type {?} */
    SwtCommonGridPagination.prototype.minimum_;
    /** @type {?} */
    SwtCommonGridPagination.prototype.value;
    /** @type {?} */
    SwtCommonGridPagination.prototype.horizontalAlign;
    /** @type {?} */
    SwtCommonGridPagination.prototype.marginLeft;
    /** @type {?} */
    SwtCommonGridPagination.prototype.marginRight;
    /** @type {?} */
    SwtCommonGridPagination.prototype.totalItems;
    /** @type {?} */
    SwtCommonGridPagination.prototype.processing;
    /** @type {?} */
    SwtCommonGridPagination.prototype.styleObject;
    /** @type {?} */
    SwtCommonGridPagination.prototype.realPagination;
    /** @type {?} */
    SwtCommonGridPagination.prototype._gridPaginationOptions;
    /** @type {?} */
    SwtCommonGridPagination.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.httpClient;
    /**
     * @type {?}
     * @private
     */
    SwtCommonGridPagination.prototype.elem;
}
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************