export declare class Keyboard {
    static BACKSPACE: number;
    static CAPS_LOCK: number;
    static CONTROL: number;
    static DELETE: number;
    static DOWN: number;
    static END: number;
    static ENTER: number;
    static ESCAPE: number;
    static F1: number;
    static F2: number;
    static F3: number;
    static F4: number;
    static F5: number;
    static F6: number;
    static F7: number;
    static F8: number;
    static F9: number;
    static F10: number;
    static F11: number;
    static F12: number;
    static F13: any;
    static F14: any;
    static F15: any;
    static HOME: number;
    static INSERT: number;
    static LEFT: number;
    static NUMPAD_0: number;
    static NUMPAD_1: number;
    static NUMPAD_2: number;
    static NUMPAD_3: number;
    static NUMPAD_4: number;
    static NUMPAD_5: number;
    static NUMPAD_6: number;
    static NUMPAD_7: number;
    static NUMPAD_8: number;
    static NUMPAD_9: number;
    static NUMPAD_ADD: number;
    static NUMPAD_DECIMAL: number;
    static NUMPAD_MULTIPLY: number;
    static NUMPAD_DEVIDE: number;
    static NUMPAD_ENTER: number;
    static NUMPAD_SUBTRACT: number;
    static PAGE_DOWN: number;
    static PAGE_UP: number;
    static RIGHT: number;
    static SHIFT: number;
    static SPACE: number;
    static TAB: number;
    static UP: number;
    static capsLock: number;
    static numLock: number;
    constructor();
}
