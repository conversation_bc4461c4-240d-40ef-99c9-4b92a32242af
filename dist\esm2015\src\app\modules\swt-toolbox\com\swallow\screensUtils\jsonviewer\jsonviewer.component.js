/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef } from '@angular/core';
import { CommonService } from '../../utils/common.service';
import { SwtModule } from '../../controls/swt-module.component';
//import $ from 'jquery';
/** @type {?} */
const _ = parent;
export class JSONViewer extends SwtModule {
    // @ViewChild("txtinput") txtinput: SwtTextInput;
    // private textinput: SwtTextInput;
    /**
     * @param {?} commonService
     * @param {?} element
     */
    constructor(commonService, element) {
        super(element, commonService);
        this.commonService = commonService;
        this.element = element;
    }
    /**
     * @return {?}
     */
    get code() {
        return JSON.stringify(this.data, null, 2);
    }
    /**
     * @param {?} v
     * @return {?}
     */
    set code(v) {
        try {
            this.data = JSON.parse(v);
        }
        catch (e) {
            console.log('error occored while you were typing the JSON');
        }
        ;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.data = this.jsonData;
    }
    /**
     * @return {?}
     */
    close() {
        this.close();
    }
}
JSONViewer.decorators = [
    { type: Component, args: [{
                selector: 'swt-jsonviewer',
                template: "<ngx-json-viewer [json]=\"data\" #jsonViewer  id=\"jsonViewer\"  (close)=\"close()\"></ngx-json-viewer>\r\n",
                providers: [CommonService],
                styles: [""]
            }] }
];
/** @nocollapse */
JSONViewer.ctorParameters = () => [
    { type: CommonService },
    { type: ElementRef }
];
if (false) {
    /** @type {?} */
    JSONViewer.prototype.data;
    /** @type {?} */
    JSONViewer.prototype.jsonData;
    /** @type {?} */
    JSONViewer.prototype.description;
    /** @type {?} */
    JSONViewer.prototype.message;
    /**
     * @type {?}
     * @private
     */
    JSONViewer.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    JSONViewer.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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