/* You can add global styles to this file, and also import other style files */
@import '~bootstrap/dist/css/bootstrap.min.css';
@import "~ng2-dnd/bundles/style.css";
@import "assets/css/main.css";
@import "assets/css/highchartILM.css";
@import "assets/css/advancedDataGrid/skin-win8/ui.fancytree.css";
@import "assets/css/advancedDataGrid/skin-xp/ui.fancytree.css";
/*@import "assets/css/customTree.css";*/
@import "~codemirror/lib/codemirror.css";
@import "~codemirror/theme/neat.css";
.advancedDataGridHeader {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none;
}
label, span, p {
    font-family: verdana, helvetica;
    font-size: 11px;
}

body, html {
    overflow: hidden;
    height: 100%;
}

body.wait,
body.wait * {
    cursor: wait !important;
}

.spiner-container {
    background-color: #3896f5;
    height: 100%;
    margin: 0px;
    position: absolute;
    top: 0px;
    width: 100%;
}

.spinner {
    width: 70px;
    height: 70px;
    position: relative;
    margin: 350px auto;
}

.double-bounce1, .double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: white;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;

    -webkit-animation: sk-bounce 2.0s infinite ease-in-out;
    animation: sk-bounce 2.0s infinite ease-in-out;
}

.double-bounce2 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
}

/* @-webkit-keyframes sk-bounce { */
/*   0%, 100% { -webkit-transform: scale(0.0) } */
/*   50% { -webkit-transform: scale(1.0) } */
/* } */

/* @keyframes sk-bounce { */
/*   0%, 100% {  */
/*     transform: scale(0.0); */
/*     -webkit-transform: scale(0.0); */
/*   } 50% {  */
/*     transform: scale(1.0); */
/*     -webkit-transform: scale(1.0); */
/*   } */
/* } */

/*********************************************************************************************/
/* You can add global styles to this file, and also import other style files */
/* @import '~angular-tree-component/dist/angular-tree-component.css'; */
/* You can add global styles to this file, and also import other style files */
.header {
    /*background-color: #ccecff;*/
    background-color: #d4e1fc;
    padding: 20px;
    border-bottom: 1px solid gray;
}
.swt-pan {
    padding: 20px;
    border-left: 1px solid gray;
    overflow: auto;
    height: 800px;
}

/* html, body { */
/*   overflow: hidden; */
/* } */

/*==================================== STYLE ADDED BY CHIHEB TO STYLE ALERT BOX =======================================*/
.alert-overlay {
    position: fixed;
    top: 0px;
    left:0px;
    width:100%;
    height:100%;
    z-index: 9999;
    background-color: rgba(249, 250, 251, 0.4);
}
.alert-content {
    min-width: 240px;
    min-height: 110px;
    display: table;
    border-radius: 5px;
    border: 1px solid rgba(67, 115, 162, 1);
    background-color: #ccecff;
    margin: auto;
    position: relative;
    top: 45%;
}
.alert-heading {
    height: 20px;
    text-align: justify;
    width: auto;
    padding: 3px 0px 0px 14px;
    font-size: 12px;
    color: #FFF;
    font-weight: bold;
    background-color: rgba(67, 115, 162, 1);
    background-image: url("assets/images/alert-heading-img.png");
}
.alert-body {
    background-color: rgba(203, 235, 254, 1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}
.alert-message {
    display: flex;
    padding: 8px;
}
.image {
    padding: 8px;
}
.msg {
    height: auto;
    font-size: 12px;
    padding-top: 16px;
    text-align: left;
}
.alert-btn {
    padding: 0px 0px 10px 10px;
    text-align: center;
    min-width: 250px;
}
.alert-btn> button {
    border-bottom:1px solid #52869a!important ;
    border-top:1px solid #90b6c4!important;
    border-left:1px solid #52869a!important;
    border-right:1px solid #52869a!important;
    border-radius: 5px;
    font-size:12px;
    letter-spacing:0.2px;
    height: 23px;
    min-width: 60px;
    margin : 5px!important;
    font-weight:bolder;
    color: #173553;
    width: auto;
    padding: 0px 10px;
    background-color: #C2E5FF;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#C2E5FF), to(#92ACBF));
    background-image: -webkit-linear-gradient(top, #C2E5FF, #92ACBF);
    background-image: -moz-linear-gradient(top, #C2E5FF, #92ACBF);
    background-image: -ms-linear-gradient(top, #C2E5FF, #92ACBF);
    background-image: -o-linear-gradient(top, #C2E5FF, #92ACBF);
    background-image: linear-gradient(to bottom, #C2E5FF, #92ACBF);
    background-image: url("assets/images/button_bg.png");
    cursor: default;
    font-family: verdana,halvatica;
    font-size: 11px;
}
.alert-btn> button:focus {
    outline: none;
    border:2px solid #49B9FF;
    border-radius: 5px;
}
.alert-btn> button:hover {
    background-color: #C2E5FF;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#C2E5FF), to(#C2E5FF));
    background-image: -webkit-linear-gradient(top, #C2E5FF, #C2E5FF);
    background-image: -moz-linear-gradient(top, #C2E5FF, #C2E5FF);
    background-image: -ms-linear-gradient(top, #C2E5FF, #C2E5FF);
    background-image: -o-linear-gradient(top, #C2E5FF, #C2E5FF);
    background-image: linear-gradient(to bottom, #C2E5FF, #C2E5FF);
    background-image: url("assets/images/button_hv.png");
    border: 1px solid #49B9FF !important;
}
/*============================================ on dragging style ============================================*/
/* .ui-draggable-dragging { */
/* 	cursor: move; */
/* } */
/*========================================== toolTip Style ==================================================*/
.ui-tooltip {
    background-color: #F1F1DE !important;
    width: auto !important;
    max-width:1200px !important;
    min-width: 10px !important;
    margin-top: -10px !important;
    -moz-transition-delay: 0s !important;
    transition-delay: 0s !important;
    padding: 0px !important;
    box-shadow: 0px 5px 8px #7A8D99  !important;
}
.ui-tooltip-content {
    padding: 3px 5px 3px 5px !important;
    border-radius: 5px;
    transition: opacity  5s linear 0s !important;
    transition-delay: 0s !important;
    color: #000;
    font-size: 10px;
}

/*========================================== /END OF TOOLTIP STYLE==================================================*/

div.toggle {
    color: black !important;
    font-size: 12px !important;
    height: 22px !important;
    width: 23px !important;
    line-height: 23px !important;
    background-color: #a7c6de !important;
}
ng-select > div {
    border: 1px solid #a7c6de !important;
    height: 23px !important;

}
ng-select > div > div.single > div.value, ng-select > div > div.single > div.placeholder {
    line-height: 23px !important;
    font-size: 12px !important;
    padding: 0px 2px!important;
}
ng-select > div > div.single > div.clear {
    font-size: 12px !important;
    line-height: 23px !important;
    height: 23px !important;
    width: 23px !important;
    color: #369 !important;
}
ng-select > div > div.single {
    height: 23px !important;
}
select-dropdown > div .options ul li {
    min-height: 23px !important;
    padding: 2px 2px !important;

}
select-dropdown > div .options ul li> span {
    font-size: 12px !important;

}
select-dropdown > div .filter input {
    height: 23px !important;
}
ng-select > div > div.multiple {
    height: 23px !important;
}
select-dropdown > div .options {
    max-height:143px !important;
}
.message {
    font-size: 12px;
}
.filter> input {
    font-size: 12px;
}
select-dropdown > div{
    z-index: 2!important;
}

/*========================================== generic style classes ==================================================*/
.hide{
    visibility: hidden;
}
.visible{
    visibility: visible;
}
/*========================================== /END OF STYLE ADDED BY CHIHEB ==================================================*/
/*===================================Rihab's style==================================================*/
.content {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 1.42857143;
    color: #333;
    word-break: break-all;
    word-wrap: break-word;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.kword {
    color: #d43669;
}
/*==================== VBox STYLE =============================*/
.vboxOuter{
    border: 1px solid #97999a;
    border-radius: 5px;

}
.vbox  {
	overflow: auto;
/*     display: -webkit-box; */
/*     -webkit-box-orient: vertical; */
/*     display: -moz-box; */
/*     -moz-box-orient: vertical; */
/*     box-orient: vertical; */
}

/* @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) { */
/*  .vbox{	 */
/* 	 	border-radius:3px; */
/* 	 	padding:5px; */
/* 		margin-bottom: 2px; */
/* 	    display: -ms-flexbox !important; */
/* 	    display: -webkit-flex !important; */
/* 	    display: flex!important; */
/* 	    -ms-flex-direction: column !important; */
/* 	    -webkit-flex-direction: column !important; */
/* 	    flex-direction: column !important; */
/* 	} */
/* } */



.box {
    width: 300px;
    margin: 5px;
    text-align: center;
}
/*======================================VBOX and HBOX=============================================*/

/*======================================VBOX and HBOX=============================================*/
.hbox {
    display: -ms-inline-flexbox;
    overflow: auto;
}
/*Stack child items vertically*/
/* .vbox { */
/*     display: -webkit-flex; */
/*     display: -ms-flexbox; */
/*     display: flex; */

/* /*     Align children vetically */
/*     -webkit-flex-direction: column; */
/*     -ms-flex-direction: column; */
/*     flex-direction: column; */

/*     -webkit-align-content: flex-start; */

/*     -ms-flex-line-pack: start; */

/*     align-content: flex-start; */

/* } */



/* .vbox { */
/*     display: -webkit-flex; */
/*     display: -ms-flexbox; */
/*     display: flex; */
/*     Align children vetically */
/*     -webkit-flex-direction: column; */
/*     -ms-flex-direction: column; */
/*     flex-direction: column; */
/*     -webkit-align-content: flex-start; */
/*     -ms-flex-line-pack: start; */
/*     align-content: flex-start; */
/* } */

.backColor{
    background-color: red;
}
.inline-field input, .inline-field label {
    display: inline-block;
    margin-right: 2px;

}
/*============================== CANVAS STYLE ====================================*/
.canvasControlBar{
    border: 1px solid #F9F9F9;
    background-color: #d6e3fe;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.6), 0 0px 0px 0 rgba(0, 0, 0, 0.5);
    padding: 5px;
    margin-bottom: 5px;
}
.canvasControlBarWithoutBorder{
    height: 38px;
    overflow: hidden;
}
/*============================== Divided Box Separator STYLE ====================================*/

vertical-split-separator{
    /* 	background-color: transparent!important; */
    width: 4px  !important;
    background-color: #c0d7ea !important;
    margin-left: 15px;
    margin-right: 15px;
}
horizontal-split-separator{
    height: 4px  !important;
    background-color: #c0d7ea !important;
    margin-top: 3px;
    margin-bottom: 3px;

}
.handle {
    background-color: #286090 !important;
}
/*======================================Progressbar style=========================================================*/
.meter{
    height: 15px!important;
    margin-bottom: 5px;
    margin-top: 5px;
    border-radius: 3px;
}
/*=======================================Date field STYLE ==================================================*/
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
    margin: 0 0 0 5px;
    padding: 0;
    width: auto;
    font-size: 11px;
    font-family: Verdana, Helvetica, Arial, sans-serif;
    background-color: transparent;
}
select.ui-datepicker-year:focus {
    outline: none;
}
.ui-datepicker-trigger {
    /*margin-top: 4px !important;*/
    vertical-align: text-bottom !important;
    width: 13px;
    height: 15px;
}
.ui-datepicker table {
    font-size: 11px;
}
.ui-datepicker td {
    width: 22px;
    height: 20px;
    padding: 0px;
    font-size: 11px;
    line-height: initial;
}
.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: 0px;
    height: 28px;
    border-radius: 0px;
    background-color: #E8EBEF;
    border-bottom: 1px solid #B7BABC;
    font-size: 12px;
}
#ui-datepicker-div {
    padding: 0px !important;
    width: 179px;
    height: auto;
    padding: 0px 2px 0px 2px;
    box-shadow: 0px 5px 8px #7A8D99;
}

.ui-datepicker .ui-datepicker-title select {
    background-color: #E8EBEF !important;
}
.ui-datepicker .ui-datepicker-title select:focus {
    outline: none;
    background-color: #E8EBEF !important;
}
.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
    border: none;
    background: #ededed;
    font-weight: normal;
    color: #2b2b2b;
}
.ui-state-highlight,
.ui-widget-content
.ui-state-highlight,
.ui-widget-header
.ui-state-highlight{

    color: #fff !important;
    background-color: #818181  !important;
}
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 0px !important;
    width: 1.8em;
    height: 1.8em;
}
.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
    top: 4px !important;
    border: none;
}
.ui-datepicker .ui-datepicker-prev {
    left: 0px;
}
.ui-datepicker .ui-datepicker-next {
    right: 0px;
}
.ui-datepicker .ui-datepicker-prev-hover {
    left: 0px;
}
.ui-datepicker .ui-datepicker-next-hover {
    right: 0px;
}
.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
    display: block;
    /*position: absolute;*/
    left: 0;
    margin-left: 0;
    top: 0;
    margin-top: 0;
}
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    display: block;
    margin-top: 5px;
}
.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
    display: block;
    margin-top: 1px !important;
}
.ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center;
}

.ui-datepicker .ui-datepicker-title {
    margin-top: 4px;
}
.ui-datepicker th {
    padding: 2px;
    text-align: center;
    font-weight: bold;
    border: 0;
    font-size: 12px;
}
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid #ccecff !important;
    background: #ccecff !important;
    font-weight: normal;
    color: #454545 !important;
    line-height: initial;
}
input[type=text]::-ms-clear { display: none !important; }
.ui-state-default {
    border: none!important;
    background: transparent !important;
    font-weight: normal;
    color: #454545;
}

.ui-datepicker td:hover {
    background-color: #ccecff;
}
.ui-datepicker .ui-datepicker-title{
    margin: 0px!important;
}
.ui-datepicker-month{
    text-align-last: right;
    padding-right: .25em;
    position: relative;
    left: 8px;
}
/*======================================Link Button================================================*/
.linkbutton {
    background-color: transparent !important;
    background-image: none;
    border: none !important;
    font-weight: bold;
    color: blue;
}
.linkbutton:hover {
    background-color: #B2E1FF;
    background-image: none;
    border-radius: 3px;
    border: none !important;
    cursor: pointer;
    text-decoration: underline;
}
/*======================================Input File style================================================*/
.labelFont{
    font-weight: normal!important;
}
.optionDisabled{
    color : #AAB3B3 !important;
}
.labelMargin{
    margin-left: 30px!important;
}

/* =========================== Popup style =============================================== */
.ui-draggable-dragging {
    cursor: move !important;
}
.modal {
    z-index: 9999 !important;
    background-color: transparent !important;
}
.selectable {
    pointer-events: none !important;
    z-index: 99999;
}
.ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: -6px !important;
    bottom: -11px !important;
}
.modal.fade-anim {
    transition: none !important;
}
.modal.fade {
    transition: none !important;
    will-change: opacity;
    opacity: 0;
}
.fa-close {
    position: absolute;
    right: 0px;
    color: black !important;
}
.fa-close:hover {
    cursor: pointer;
}

/*===================================== Inputs properties ===========================*/
input::-moz-selection {
    background: #000000 !important;
    color: #FFFFFF !important;
}
input::selection {
    background: #D6E3FE !important;
    color: #000000 !important;
}
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance:textfield;
}
input:disabled {
    background-color: #eff4ff !important;
    cursor: default !important;
    color: #8e939e !important;
}
/*====================================== Stepper styling ===============================================*/
.ui-spinner-input {
    background: #FFF;
    color: inherit;
    padding: .222em 0;
    margin: 0;
    vertical-align: middle;
    margin-left: 0;
    margin-right: 0;
    outline: none;
    padding-left: 5px;
    font-size: 11px;
}
.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button {
    height: 22px;
    border: 1px solid #7F9DB9;
    padding-left: 5px;
}
.ui-corner-all, .ui-corner-top, .ui-corner-left, .ui-corner-tl {
    border: none !important;
}
.ui-spinner a.ui-spinner-button {
    /* 	border-left: 1px solid #7F9DB9 !important; */
    background-color: #DFEDF6;
    width: 19px;
}
.ui-button .ui-icon {
    margin-left: -7px;
}
.ui-spinner-down {
    border-left: 1px solid #7F9DB9 !important;
    border-top: 1px solid #7F9DB9 !important;
    border-bottom: 1px solid #7F9DB9 !important;
    border-right: 1px solid #7F9DB9 !important;
}
.ui-spinner-up {
    border-left: 1px solid #7F9DB9 !important;
    border-top: 1px solid #7F9DB9 !important;
    border-bottom: 0px solid #7F9DB9 !important;
    border-right: 1px solid #7F9DB9 !important;
}
/*--------------------------------------------------------------- styling HDVIDED BOX ----------------------------------------------------------*/
.vdividedbox-resizable-helper {
    border-bottom: 3px solid #999;
}
.hdividedbox-resizable-helper {
    border-right: 3px solid #999;
}

.ui-spinner{
    margin: 0px 5px 5px 0px;
}
/*--------------------------------------------------------------- styling SwtList ----------------------------------------------------------*/
.list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
    color: black;
}
.list-group-item.active{
    background-color: #7FCEFF;
}
.list-group-item:focus {
    background-color: red;
}
.list-group-item:hover {
    background-color: #B2E1FF;
    cursor: default;
}
.list-group-item {
    height: 22px;
    font-size: 12px;
    padding: 0px 0px 0px 10px;
    margin: 0px;
    line-height: 22px;
    border: none;
}

.boxbox{
    height:300px;
    width:200px;
    border: 1px solid #ddd;
    overflow: auto;
    background-color: white;
}
.boxbox:hover{
    cursor: default;
}
.dis{
    z-index: 1;
    background-color: #ddd;
    opacity: 0.3;
}

/*--------------------------------------------------------------- styling SwtTextArea  ----------------------------------------------------------*/

.focused {
	border: 1px solid #49B9FF!important;
}
.tinymce-editor-container {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    box-shadow: 0px 4px 4px #7A8D99;
    height: calc(100% - 15px) !important
}
 
.ql-editor {
    background-color: #FFF;
}

.tinymce-editor-header {
    color: #173553;
    font-size: 11px !important;
    font-family: Verdana,helvetica;
    height: 15px;
    line-height: 15px;
    padding-left :5px;
    background-color: #7f9db9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#7f9db9), to(#b7dbff));
    background-image: -webkit-linear-gradient(top,#7f9db9, #b7dbff);
    background-image: -moz-linear-gradient(top, #7f9db9, #b7dbff);
    background-image: -ms-linear-gradient(top, #7f9db9, #b7dbff);
    background-image: -o-linear-gradient(top,#7f9db9, #b7dbff);
    background-image: linear-gradient(to bottom,#7f9db9, #b7dbff);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: 1px solid #92AFCC;
}

.tinymce-editor-body {
    border: none !important;
    padding: 10px 10px 10px 10px;
    background-color: #ccecff !important;
    height: calc(100% - 12px) !important;
}

.mce-content-body{
	font-size: 11px;
}
.tox-edit-area__iframe{
/* 	width: 1420px!important; */
}
.tox-statusbar{
	display: none!important;
}
.tox-tinymce{
	border: 0px!important;
    height: 100% !important;
/* 	min-height: 100%!important; */
}
.tinymce-editor-body > .tox-tinymce > .tox-editor-container >.tox-sidebar-wrap{
	margin-top: 10px!important;
}
.tox-sidebar-wrap{
	border: 1px solid #7f9db9 !important;
}
.tinymce-editor-body > .tox-tinymce > .tox-editor-container >.tox-editor-header{
	border-top: 1px solid #7f9db9 !important;
}
.tox-editor-header{
    border-left: 1px solid #ccc!important;
    border-right: 1px solid #ccc!important;
}

.ql-toolbar.ql-snow {
    border: none !important;
    padding: 3px !important;
}

.ql-formats > button {
    border: 1px solid #B7BABC !important;
    border-radius: 5px;
    background-image: -webkit-linear-gradient(top,#F1F9FF, #CDE0EB) !important;
    background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB) !important;
    background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB) !important;
    background-image: -o-linear-gradient(top,#F1F9FF, #CDE0EB) !important;
    background-image: linear-gradient(to bottom,#F1F9FF, #CDE0EB) !important;
}
.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
    position: absolute;
    margin-top: -20px;
    right: -1px !important;
    top: 36% !important;
    width: 24px !important;
    height: 24px;
    border-top-right-radius: 5px;
    border: 1px solid #B7BABC !important;
    background-image: -webkit-linear-gradient(top,#F1F9FF, #CDE0EB) !important;
    background-image: -moz-linear-gradient(top, #F1F9FF, #CDE0EB) !important;
    background-image: -ms-linear-gradient(top, #F1F9FF, #CDE0EB) !important;
    background-image: -o-linear-gradient(top,#F1F9FF, #CDE0EB) !important;
    background-image: linear-gradient(to bottom,#F1F9FF, #CDE0EB) !important;
}

.ql-snow .ql-picker-label {
    cursor: pointer;
    display: inline-block;
    height: 100%;
    padding-left: 8px;
    padding-right: 2px;
    position: relative;
    width: 100%;
    border: 1px solid #B7BABC !important;
    padding-top: 0px;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    background-color: #FFF !important;
}
.ql-snow .ql-picker.ql-font {
    background-color: #FFF;
    color: #334D67  !important;
    font-weight: bold;
    width: 165px !important;
}
.ql-toolbar.ql-snow .ql-picker-label {
    background-color: #FFF;
    color: #334D67  !important;
    font-weight: bold;
    /*     width: 165px !important; */
}

.ql-picker-label {
    border: 1px solid red !important;
}

.ql-container .ql-snow {
    height: 100% !important;
}

/*--------------------------------------------------------------- styling SwtNavigator ----------------------------------------------------------*/
 .tabNavigator-tabs {
 	height: 20px;
 	background-image: url("assets/images/tab_bg.png");
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-top: 1px solid #bec0c2;
    border-left: 1px solid #bec0c2;
    border-right: 1px solid #bec0c2;
 }

.tabNavigator-tabs>span {
	height: 19px!important;
	width: 100%;
	margin: 0px !important;
 	padding: 1px 10px 3px 10px !important;
 	font-weight: bold;
 	font-size: 11px!important;
 	/*color: #173553 !important;*/
 	cursor: default!important;
    display: block;
    line-height: 1.42857143;
    font-family: verdana,helvetica;
}
.tabNavigator-tabs>span:hover{
	background-color: #eee;
 }

.tabNavigator-tabs.active>span {
    background-color: #d6e3fe !important;
 	color: #173553 !important;
 }

 .tabNavigator-content {
/*     padding: 5px; */
    border: 1px solid #ccc;
    background-color: #d6e3fe !important;
    /*
    overflow: auto;
    */
}
.nav-tabs {
    border: none  !important;
}
.tab {
    display:none;
}

.show {
    display: block;
}
.disabled-container {
    pointer-events: none;
    text-decoration:none;
   opacity: 0.6;
   /* background-color: #E5E5E5;*/
}

.disabled-container-tooltip {
    text-decoration:none;
    opacity: 0.6;
   /* background-color: #E5E5E5;*/
}

.disabled-container>span {
    color:#333!important;
    /*No need to set background color background-color: #cccccc; */
}
.swtTabNavigator-container{
    height: 100%;
    width: 100%;
}

/*================================ disable text selection =====================================================*/
span, p {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select:none;
    user-select:none;
    -o-user-select:none;
}

/*================================ Tree view Style=====================================================*/
ul.fancytree-container {
    border: none !important;
}
ul.fancytree-container:focus {
    border: none !important;
    outline: none !important;
}

.fancytree-selected span.fancytree-title {
    color:black !important;
    font-style:normal !important;
  }

 .divTable {
    display: table;
    margin: auto;
    width: 100%
}
.divTableBody {
    display: table-row-group;
}
.divTableRow {
    display: table-row;
}
.divTableCell {
    display: table-cell;
}


/********************************** styling scrollBar*********************************************/
/* width */
::-webkit-scrollbar {
    width: 13px;
}
/* width */
::-webkit-scrollbar:horizontal {
    height: 13px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #fff;
    border: 1px solid #ACBDC8;
    background-image: -webkit-linear-gradient(left,#A3A7A9, #FFF);
    background-image: -moz-linear-gradient(left, #A3A7A9, #FFF);
    background-image: -ms-linear-gradient(left, #A3A7A9, #FFF);
    background-image: -o-linear-gradient(left,#A3A7A9, #FFF);
    background-image: linear-gradient(to right,#A3A7A9, #FFF);
    border: 1px solid #5C5E5F;
}
/* Track */
::-webkit-scrollbar-track:horizontal {
    background: #fff;
    border: 1px solid #ACBDC8;
    background-image: -webkit-linear-gradient(top,#A3A7A9, #FFF);
    background-image: -moz-linear-gradient(top, #A3A7A9, #FFF);
    background-image: -ms-linear-gradient(top, #A3A7A9, #FFF);
    background-image: -o-linear-gradient(top,#A3A7A9, #FFF);
    background-image: linear-gradient(to bottom,#A3A7A9, #FFF);
    border: 1px solid #5C5E5F;
}

/* Handle */
::-webkit-scrollbar-thumb:vertical {
    background-image: url("assets/images/scrollIcon.png");
    background-color: #EFEFEF;
    background-repeat: no-repeat;
    border: 1px solid #5C5E5F;
    background-position: center;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
/* Handle */
::-webkit-scrollbar-thumb:horizontal {
    background-image: url("assets/images/hscrollBar.png");
    background-color: #EFEFEF;
    background-repeat: no-repeat;
    border: 1px solid #5C5E5F;
    background-position: center;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background-color: #FCFCFC;
    border-top-color: #009DFF;
    border-right-color: #009DFF;
    border-bottom-color: #009DFF;
}

/* scroll Bar buttons */
::-webkit-scrollbar-button {
    width: 13px;
    height: 13px;
    background: #F8F8F8;
    border: 1px solid #8B8D8F;
}

::-webkit-scrollbar-button:vertical:increment {
    background: url("assets/images/arrowScrollDown.png") no-repeat 50% 50%;
}
::-webkit-scrollbar-button:vertical:decrement {
    background: url("assets/images/arrowScrollUp.png") no-repeat 50% 50%;
}

::-webkit-scrollbar-button:horizontal:increment {
    background: url("assets/images/arrowScrollRight.png") no-repeat 50% 50%;
}
::-webkit-scrollbar-button:horizontal:decrement {
    background: url("assets/images/arrowScrollLeft.png") no-repeat 50% 50%;
}


/*******************Context Menu********************************/
/* The whole thing */
.custom-menu, .screenVerion {
    display: none;
    z-index: 1000;
    position: absolute;
    overflow: hidden;
    border: 1px solid #CCC;
    white-space: nowrap;
    font-family: sans-serif;
    background: #FFF;
    color: #333;
    border-radius: 3px;
    padding: 0;
    box-shadow: 2px 3px 5px gray;
}

/* Each of the items in the list */
.custom-menu li, .screenVerion li {
    cursor: default;
    user-select: none;
    list-style-type: none;
    transition: none !important;
    -webkit-user-select: none;
    -moz-user-select: none;
    height: 23px;
    line-height: 23px;
    font-family: verdana, helvetica;
    font-size: 11px;
    padding-left: 10px;
    padding-right: 10px;
    -ms-user-select: none;
}

.custom-menu li:hover, .screenVerion li:hover {
    background-color: #DEF;
}

.progress-bar-striped, .progress-striped .progress-bar {
    background-image: repeating-linear-gradient(-45deg, #70CAF8, #70CAF8 7px, #3F99E6 5px, #3F99E6 15px) !important;
}

.progress-bar.active, .progress.active .progress-bar {
    animation: progress-bar-stripes 0.4s linear infinite !important;
}

.add-fav {
    float: right;
    color: green;
    line-height: 25px !important;
    margin-right: 10px;
}

.add-fav:hover {
    font-size: 16px;
}

/**********************************************************popup****************************************************/
.ui-widget-content {
    background: #ccecff;
    padding: 0px !important;
}

.ui-autocomplete {
    position: absolute;
    z-index: 1000;
    padding: 0;
    margin-top: 2px;
    list-style: none;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    background-clip: padding-box;
    max-height: 25vh;
    min-height: 0px !important;
    min-width: unset;
    overflow-y: auto;
    overflow-x: hidden;
}

.ui-menu .ui-menu-item {
    height: 28px
}
/* ====================== toolTip Style ==================================== */
/*.tooltip-example {
    text-align: center;
    padding: 0 50px;
  }
  .tooltip-example [tooltip] {
    display: inline-block;
    margin: 50px 20px;
    width: 180px;
    height: 50px;
    border: 1px solid gray;
    border-radius: 5px;
    line-height: 50px;
    text-align: center;
  }
  .ng-tooltip {
    position: absolute;
    max-width: 300px;
    font-size: 9px;
    text-align: left;
    color: #0B333C;
    padding: 3px 8px;
    background: #F8F9E7;
    border-radius: 3px;
    z-index: 1000;
    box-shadow: 0px 3px 3px #7A8D99;
    opacity: 0;
    font-size: 8px;
    font-family: verdana,helvetica;
  }

  .ng-tooltip-top:after {
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-color: black transparent transparent transparent;
    word-break: break-word;
  }
  .ng-tooltip-bottom:after {
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-color: transparent transparent black transparent;
  }
  .ng-tooltip-left:after {
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-color: transparent transparent transparent black;
  }
  .ng-tooltip-right:after {
    top: 50%;
    right: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-color: transparent black transparent transparent;
  }
  .ng-tooltip-show {
    opacity: 1;
  }*/
/* ====================== END toolTip Style ==================================== */

/*
.ui-dialog {
    padding: 0px;
    width: auto !important;
    outline: 8px solid #369;
}
.ui-dialog .ui-dialog-titlebar {
    background: #369;
    border-radius: 0px !important;
    padding: 2px;
}

.ui-icon-gripsmall-diagonal-se {
    background: none !important;
}

.window-container > .ui-icon, .window-container >.ui-widget-content .ui-icon {
    position: absolute !important;
    right: -8px !important;
    bottom: -8px !important;
    background-image: url("assets/images/resizeHandler.png") !important;
    background-repeat: no-repeat !important;
    cursor: url("assets/images/resizeCursor.png"), auto !important;
}*/

.requiredInput{
    border: 1px solid red !important;
}
.SwtCheckBox-container > span.checkmark + span{
    position: relative!important;
    top: 5px!important;
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: grey !important;
    font-style: italic;
    opacity: 1; /* Firefox */
  }
  
  :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: grey !important;
    font-style: italic;
  }
  
  ::-ms-input-placeholder { /* Microsoft Edge */
    color: grey !important;
    font-style: italic;
  }
  .jslider-value {
      visibility: hidden !important;
  }
  .jslider {
      width: 120px !important;
  }
.tox-edit-area__iframe{
    visibility: visible !important;
}
.ngx-json-viewer {
    height: auto !important;
}
.noOutline{
    outline: none;
}
.sliderTooltip{
    width:110px !important;
}

.jslider-pointer{
    /* top: -2px !important; */
    /* z-index: 0 !important; */
}

/****************************************************************************New Tab Navigator */
li.swt-tablayout-item {
    display: flex;
    background: linear-gradient(#E9EFFB, #D5E4EB, #CCDCE9);
    border: 1px solid #C8CECA;
    border-bottom: transparent;
    width: fit-content;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

li.swt-tablayout-item:hover > span.swt-tablayout-item-controls {
    /*visibility: visible;*/
    color: red;
}

span.swt-tablayout-item-controls {
    display: block;
    width: 20px;
    height: 100%;
    color: #C8CECA;
    /*border: 1px solid red;*/
    text-align: center;
    line-height: 20px;
    /*visibility: hidden;*/
    background: transparent;
}

span.swt-tablayout-item-title-closable {
    /*border: 1px solid green; */
    width: fit-content;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 0 0 4px;
    line-height: 20px;
    font-size: 11px;
    font-family: Verdana, Helvetica, Arial, sans-serif;
    font-weight: bolder;
    background: transparent;
}

span.swt-tablayout-item-title {
    /*border: 1px solid green; */
    width: fit-content;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 4px;
    line-height: 20px;
    font-size: 11px;
    font-family: Verdana, Helvetica, Arial, sans-serif;
    font-weight: bolder;
    background: transparent;
}

li.swt-tablayout-item-active {
    background: #D5E3FE;
    height: 25px;
    border-bottom: transparent;
}
/**********************************************************************************************************************/
.swt-dropdown-item {
    width: 100%;
    height: 21px;
    line-height: 21px;
    padding: 0 10px;
    box-sizing: border-box;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.swt-dropdown-item:hover {
    background-color: #0000A0;
    color: #FFF;
}

.swt-selected-dropdown-item {
    background-color: #0000A0;
    color: #FFF;
}
.ngxp__container {
background-color:#ee82ee;
 z-index: 999;
 color: white;
}


.border-orange-previous{
    border: 1px solid #ee82ee !important;
}


/* basic styling */
.basic-tooltip {
    position: absolute;
    color: white;
    z-index: 999;
    background-color: #ee82ee;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    border:1px solid grey;
    box-shadow:0 0 2px rgba(0,0,0,.5);
  }