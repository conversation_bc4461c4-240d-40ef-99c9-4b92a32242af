import { Editor } from 'angular-slickgrid';
export declare class CheckBoxEditor implements Editor {
    private args;
    private isBool;
    private $input;
    private defaultValue;
    private logger;
    private enableFlag;
    private showHideCells;
    private row_index;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    CRUD_ORIGINAL_DATA: string;
    CRUD_CHANGES_DATA: any;
    originalDefaultValue: any;
    private commonGrid;
    constructor(args: any);
    /**
     * init: function to be loaded once the item render is activated by the first click on the cell.
     */
    init(): void;
    /**
     * loadValue : loading the value from the data provider into the input.
     * @param item : the selected item
     */
    loadValue(item: any): void;
    /**
     * serializeValue : return the state of the checkbox (checked or not)
     */
    serializeValue(): boolean;
    /**
     * focus : the input gets the focus in.
     */
    focus(): void;
    /**
     * applyValue : will update the value of the checkbox in the data provider (N/Y) or (true/false).
     * @param item : the selected current item
     * @param state : the state of the checkbox (true/false)
     */
    applyValue(item: any, state: any): void;
    /**
     * isValueChanged : dispatch the change event.
     */
    isValueChanged(): boolean;
    /**
     * validate :
     */
    validate(): {
        valid: boolean;
        msg: any;
    };
    /**
     * destroy : Called once the focus is out and the item render become hidden.
     */
    destroy(): void;
}
