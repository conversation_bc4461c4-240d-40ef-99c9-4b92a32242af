/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as moment_ from 'moment-mini';
import { FieldType, mapMomentDateFormatWithFieldType } from "angular-slickgrid";
import { isClickable } from './cellItemRenderUtilities';
/** @type {?} */
const moment = moment_;
// patch to fix rollup "moment has no default export" issue, document here https://github.com/rollup/rollup/issues/670
/** @type {?} */
const FORMAT = mapMomentDateFormatWithFieldType(FieldType.dateIso);
/** @type {?} */
export const dateFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let type = columnDef['columnType'];
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let negative = false;
    /** @type {?} */
    var defaultColor = 'transparent';
    /** @type {?} */
    var dataIndex = dataContext['id'];
    /** @type {?} */
    let color = columnDef.params.rowColorFunction(dataContext, dataIndex, defaultColor, columnDef.field);
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    /** @type {?} */
    let blink_me = false;
    /** @type {?} */
    let isLink = isClickable(dataContext, field);
    /** @type {?} */
    let enableRowSelection = columnDef.params.grid.enableRowSelection;
    /** @type {?} */
    let style = "";
    if (color == undefined) {
        color = defaultColor;
    }
    if (color && color.toString().indexOf('|') > -1) {
        // background: linear-gradient(to right, colorList[0] 0%,colorList[0] 50%,#000000 50%,colorList[1] 50%,colorList[1] 100%);
        /** @type {?} */
        const colorList = color.split('|');
        style += ' background:linear-gradient(to right, ' + colorList[0] + ' 0%,' + colorList[0] + ' 50%,#000000 50%,' + colorList[1] + ' 50%,' + colorList[1] + ' 100%) ' + (!enableRowSelection ? ' !important; ' : ';');
    }
    else {
        style += ' background-color:' + color + (!enableRowSelection ? ' !important; ' : ';');
    }
    //- Return the formatter based on 'negative' value and background row 'color' .
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
        if (typeof (negative) == 'string') {
            if (negative == 'true') {
                negative = true;
            }
            else {
                negative = false;
            }
        }
    }
    if ((String(value)).indexOf('blink') != -1) {
        /** @type {?} */
        var val1 = (value.split(">"))[2];
        /** @type {?} */
        var val2 = val1.split("<");
        value = val2[0];
        blink_me = true;
    }
    if (value instanceof Object) {
        value = value[field];
    }
    if (showHideCells) {
        if (value == undefined || value == null) {
            return `<div class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''} ${(( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''}" style='padding-left: 5px; height: 100%; ${style} text-align: center; ' ></div>`;
        }
        else {
            value = columnDef.params.customContentFunction(dataContext, columnDef.field, value, type);
            if (!negative) {
                return `<div class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''} ${(( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''} " style='padding-left: 5px; height: 100%; position: relative; ${style} text-align: center;'>${isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value}</div>`;
            }
            else {
                return `<div class="strFormatterDiv ${blink_me == true ? 'blink_me' : ''} ${(( /*enabledFlag == false ||*/!columnDef.params.grid.enabled)) ? 'strFormatterDivDisabled' : ''}" style="padding-left: 5px; height: 100%; position: relative; color: red; ${style} text-align: center;">${isLink && enabledFlag ? '<a class="strLinkRender"  >' + value + '</a>' : value}</div>`;
            }
        }
    }
    else {
        return ``;
    }
    //  return (value && isDateValid) ? moment(value).format(FORMAT) : value;
});
//# sourceMappingURL=data:application/json;base64,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