import { OnInit, ElementRef, Renderer2 } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class SwtList extends Container implements OnInit {
    private elem;
    private commonService;
    private _renderer;
    private _selectedIndices;
    private _selectedIndex;
    private _selectable;
    private _height;
    private _selectedItems;
    private _selectedItem;
    private SwtListObject;
    private _width;
    private _enabled;
    private _allowMultipleSelection;
    private _dataProvider;
    private dataProviderOld;
    coloredIndex: boolean[];
    multipleSelectNav: boolean[];
    private focusedIndex;
    private index;
    private firstShown;
    constructor(elem: ElementRef, commonService: CommonService, _renderer: Renderer2);
    ngOnInit(): void;
    private click_;
    toolTip: string;
    showDataTip: boolean;
    doubleClickEnabled: boolean;
    allowMultipleSelection: any;
    firstCall: boolean;
    id: string;
    dataProvider: any;
    setToolTip(event: any, item: any): void;
    selectedIndex: any;
    selectedIndices: any;
    width: string;
    height: string;
    swtlistItems: ElementRef;
    list: ElementRef;
    onChange(event: any): void;
    testkey(event: any): void;
    /**
 * This method is used to handle scrolling
 * to bottom.
 */
    private scrollToBottom;
    /**
  * This method is used to handle scrolling
  * to Top.
  */
    private scrollToTop;
    private scrollFocusToBottom;
    /**
  * This method is used to handle scrolling
  * to Top.
  */
    private scrollFocusToTop;
    addItem(value: any): void;
    removeItem(index: any): void;
    removeItems(indexes: any): void;
    removeAll(): void;
    clickHandler(event: any, i: any): void;
    selectedItems: any;
    selectedItem: any;
    enabled: any;
    selectable: any;
    spyChanges(event: any): void;
    resetOriginalValue(): void;
    onClick(event: any): void;
    doubleClicked(event: any): void;
}
