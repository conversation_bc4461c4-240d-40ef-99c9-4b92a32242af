import { ElementRef, OnInit, <PERSON><PERSON><PERSON>roy } from "@angular/core";
import 'jquery-ui-dist/jquery-ui';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
export declare class SwtRadioItem extends Container implements OnInit, OnDestroy {
    private elem;
    private commonService;
    private _value;
    private _label;
    private _groupName;
    private _tabIndex;
    parentGroup: any;
    tabIndex: any;
    styleName: string;
    toolTip: string;
    groupName: string;
    label: string;
    value: string;
    selected: boolean;
    fontSize: any;
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    constructor(elem: ElementRef, commonService: CommonService);
    ngOnInit(): void;
    /**
     * Destroy all event listeners
     */
    ngOnDestroy(): void;
}
