/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
//import * as $ from "jquery";
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
import { SwtCommonGridItemRenderChanges } from "../events/swt-events.module";
//@dynamic
var 
//@dynamic
RadioButtonEditor = /** @class */ (function () {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    function RadioButtonEditor(args) {
        this.args = args;
        this.isBool = false; //isBool : variable to test the radiobutton's value type (boolean or string)
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.commonGrid = this.args.column.params.grid;
        this.logger = new Logger('RadioButtonEditor', null);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.init = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [init] -START- enabledFlag :', this.enableFlag);
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        //- add Highlighting 
        /** @type {?} */
        var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
        if (activeRow && activeRow.children().length > 0) {
            for (var index = 0; index < activeRow.children().length; index++) {
                /** @type {?} */
                var item = activeRow.children()[index];
                $(item).addClass('selected');
            }
        }
        this.defaultValue = this.args.item[this.args.column.field];
        this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field];
        if (this.showHideCells) {
            this.$input = $("<input type='radio'  " + ((this.enableFlag == false) ? 'disabled' : '') + " value='" + this.defaultValue + "' class='editor-radio' hideFocus  />");
        }
        else {
            this.$input = $("");
        }
        this.$input.appendTo(this.args.container);
        this.logger.info('Method [init] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.destroy = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [destroy] -START-');
        this.$input.remove();
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.logger.info('Method [destroy] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    RadioButtonEditor.prototype.loadValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('Method [loadValue] -START/END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.focus = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [focus] -START/END-');
        this.$input.focus();
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.serializeValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.logger.info('Method [serializeValue] -START- this.enableFlag =', this.enableFlag);
        if (this.showHideCells) {
            //For the first click on the render , the CheckBox will change the value within the opposite precede state.
            if (this.enableFlag == true) {
                this.$input.prop('checked', true);
            }
            ;
            //RadioButton's Event change handler
            this.$input.change((/**
             * @return {?}
             */
            function () {
                if (_this.enableFlag == true) {
                    _this.applyValue(_this.args.item, _this.$input.prop('checked'));
                    _this.CRUD_CHANGES_DATA = _this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                    ;
                    if (_this.CRUD_CHANGES_DATA != undefined && _this.CRUD_CHANGES_DATA != null) {
                        _this.originalDefaultValue = _this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? _this.CRUD_CHANGES_DATA['crud_original_data'][_this.args.column.field] : null;
                    }
                    /*console.log('[serializeValue] this.originalDefaultValue =',this.originalDefaultValue)
                    console.log('[serializeValue] this.defaultValue =',this.defaultValue)
                    console.log('[serializeValue] this.args.item[this.args.column.field] =',this.args.item[this.args.column.field])*/
                    if ((_this.originalDefaultValue == null && _this.defaultValue != _this.args.item[_this.args.column.field]) || ((_this.originalDefaultValue != null) && (_this.originalDefaultValue != _this.args.item[_this.args.column.field]))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (_this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = _this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            function (x) { return ((x.crud_data.id == _this.args.item.id) && (x.crud_operation == "I")); }));
                            if (crudInsert != undefined && crudInsert[_this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            //console.log('is changed so execute item changed function' )
                            /** @type {?} */
                            var original_row = [];
                            for (var key in _this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = _this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (var key in _this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = tslib_1.__assign({}, _this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[_this.args.column.field] = _this.defaultValue;
                            original_row['slickgrid_rowcontent'][_this.args.column.field] = { content: _this.defaultValue };
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: _this.args.item.id,
                                columnIndex: _this.args.column.columnorder,
                                new_row: _this.args.item,
                                original_row: original_row,
                                changedColumn: _this.args.column.field,
                                oldValue: _this.defaultValue,
                                newValue: _this.args.item[_this.args.column.field]
                            };
                            _this.commonGrid.spyChanges({ field: _this.args.column.field });
                            _this.commonGrid.updateCrud(updatedObject);
                            SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                        }
                    }
                    else if ((_this.originalDefaultValue == _this.args.item[_this.args.column.field])) {
                        /** @type {?} */
                        var crudChange = _this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                        /** @type {?} */
                        var ch = String("U(" + _this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = _this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                function (x) { return ((x.crud_data.id == _this.args.item.id)); }));
                                _this.commonGrid.changes.remove(_this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        //- Do not emit SpyNoChanges on the grid unless there is other changes.
                        if (_this.commonGrid.changes.size() == 0)
                            _this.commonGrid.spyNoChanges({ field: _this.args.column.field });
                        SwtCommonGridItemRenderChanges.emit({ field: _this.args.column.field, value: _this.args.item[_this.args.column.field], id: _this.args.item.id });
                    }
                    /* for (var index = 0; index < this.commonGrid.dataProvider.length; index++) {
                         this.commonGrid.dataProvider[index][this.args.column.field]="false";
                     }
                     this.commonGrid.refresh();*/
                    //                    this.commonGrid.spyChanges({ field: this.args.column.field });
                    _this.commonGrid.RadioButtonChange();
                    console.log('[RadioButtonEditor] this.args.column =', _this.args.column);
                    console.log('[RadioButtonEditor] this.args.item =', _this.args.item);
                    /** @type {?} */
                    var ListEvent = {
                        radioButtonDTO: {
                            col: _this.args.column.columnorder,
                            id: _this.args.column.id,
                            field: _this.args.column.field,
                            name: _this.args.column.name,
                            type: _this.args.column.columnType
                        }
                    };
                    for (var key in _this.args.item) {
                        if (key != 'slickgrid_rowcontent')
                            ListEvent.radioButtonDTO[key] = _this.args.item[key];
                    }
                    _this.commonGrid.radioButtonChange.emit(ListEvent);
                }
            }));
            this.$input.change();
        }
        this.logger.info('Method [serializeValue] -END-');
        return this.$input.prop('checked');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    RadioButtonEditor.prototype.applyValue = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    function (item, state) {
        this.logger.info('Method [applyValue] -START-');
        if (this.showHideCells && this.enableFlag) {
            this.args.item[this.args.column.field] = '' + state;
            this.args.item.slickgrid_rowcontent[this.args.column.field] = { content: '' + state };
            this.$input.val(state);
        }
        this.logger.info('Method [applyValue] -END-');
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.isValueChanged = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [isValueChanged] -START/END-');
        if (this.showHideCells) {
            return (!(this.$input.val() === '' && this.defaultValue === null)) && (this.$input.val() !== this.defaultValue);
        }
        else {
            return false;
        }
    };
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    RadioButtonEditor.prototype.validate = /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    function () {
        this.logger.info('Method [validate] -START-');
        return {
            valid: true,
            msg: null
        };
    };
    return RadioButtonEditor;
}());
//@dynamic
export { RadioButtonEditor };
if (false) {
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.isBool;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.showHideCells;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    RadioButtonEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    RadioButtonEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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