/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class HRule extends Container {
    /**
     * Constructor
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions----------------------------------------------------------------------------------------
        this._strokeColor = "";
        this._shadowColor = "";
        this._themeColor = "";
        //-START- Added by Rihab.J   - needed to be used in dynamically added HRule.
        $($(this.elem.nativeElement)[0]).attr('selector', 'HRule');
    }
    //----strokeColor--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set strokeColor(value) {
    }
    /**
     * @return {?}
     */
    get strokeColor() {
        return this._strokeColor;
    }
    //----shadowColor--------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set shadowColor(value) {
    }
    /**
     * @return {?}
     */
    get shadowColor() {
        return this._shadowColor;
    }
    //----themeColor---------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set themeColor(value) {
    }
    /**
     * @return {?}
     */
    get themeColor() {
        return this._themeColor;
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        try {
            delete this._strokeColor;
            delete this._shadowColor;
            delete this._themeColor;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
}
HRule.decorators = [
    { type: Component, args: [{
                selector: 'HRule',
                template: `
    <div class="h-rule"></div>
  `,
                styles: [`
       .h-rule {
           background-color: #000;
           height: 1px;
           width:100%;
           box-shadow: 0px 1px 0px #fff;
       }
  `]
            }] }
];
/** @nocollapse */
HRule.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
HRule.propDecorators = {
    strokeColor: [{ type: Input }],
    shadowColor: [{ type: Input }],
    themeColor: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._strokeColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._shadowColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype._themeColor;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HRule.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaHJ1bGUuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL2hydWxlLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTyxFQUFFLFNBQVMsRUFBVSxLQUFLLEVBQUUsVUFBVSxFQUFhLE1BQU0sZUFBZSxDQUFDO0FBQ2hGLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx1Q0FBdUMsQ0FBQztBQUNsRSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7OztNQUlsRCxDQUFDLEdBQUcsT0FBTyxDQUFDLFFBQVEsQ0FBQztBQWdCM0IsTUFBTSxPQUFPLEtBQU0sU0FBUSxTQUFTOzs7Ozs7SUFxQ2pDLFlBQW9CLElBQWdCLEVBQVUsYUFBNEI7UUFDdEUsS0FBSyxDQUFDLElBQUksRUFBRSxhQUFhLENBQUMsQ0FBQztRQURYLFNBQUksR0FBSixJQUFJLENBQVk7UUFBVSxrQkFBYSxHQUFiLGFBQWEsQ0FBZTs7UUFsQ2pFLGlCQUFZLEdBQVcsRUFBRSxDQUFDO1FBQzFCLGlCQUFZLEdBQVcsRUFBRSxDQUFDO1FBQzFCLGdCQUFXLEdBQVcsRUFBRSxDQUFDO1FBa0MvQiw0RUFBNEU7UUFDM0UsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxPQUFPLENBQUMsQ0FBQTtJQUM5RCxDQUFDOzs7Ozs7SUFqQ0EsSUFDSSxXQUFXLENBQUMsS0FBYTtJQUM3QixDQUFDOzs7O0lBQ0QsSUFBSSxXQUFXO1FBQ1gsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO0lBQzdCLENBQUM7Ozs7OztJQUdELElBQ0ksV0FBVyxDQUFDLEtBQWE7SUFDN0IsQ0FBQzs7OztJQUNELElBQUksV0FBVztRQUNYLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztJQUM3QixDQUFDOzs7Ozs7SUFHRCxJQUNJLFVBQVUsQ0FBQyxLQUFhO0lBQzVCLENBQUM7Ozs7SUFDRCxJQUFJLFVBQVU7UUFDVixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7SUFDNUIsQ0FBQzs7OztJQWNGLFdBQVc7UUFDUCxJQUFHO1lBQ0MsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ3pCLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztZQUN6QixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7U0FDM0I7UUFBQSxPQUFNLEtBQUssRUFBRTtZQUNWLE9BQU8sQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ2pDO0lBQ0wsQ0FBQzs7O1lBakVILFNBQVMsU0FBQztnQkFDVCxRQUFRLEVBQUUsT0FBTztnQkFDakIsUUFBUSxFQUFFOztHQUVUO3lCQUNROzs7Ozs7O0dBT1I7YUFDRjs7OztZQXJCa0MsVUFBVTtZQUVwQyxhQUFhOzs7MEJBNEJqQixLQUFLOzBCQVFMLEtBQUs7eUJBUUwsS0FBSzs7Ozs7OztJQXJCTiw2QkFBa0M7Ozs7O0lBQ2xDLDZCQUFrQzs7Ozs7SUFDbEMsNEJBQWlDOzs7OztJQWdDdEIscUJBQXdCOzs7OztJQUFFLDhCQUFvQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudCwgT25Jbml0LCBJbnB1dCwgRWxlbWVudFJlZiwgT25EZXN0cm95IH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IENvbnRhaW5lciB9IGZyb20gXCIuLi9jb250YWluZXJzL3N3dC1jb250YWluZXIuY29tcG9uZW50XCI7XHJcbmltcG9ydCB7IENvbW1vblNlcnZpY2UgfSBmcm9tIFwiLi4vdXRpbHMvY29tbW9uLnNlcnZpY2VcIjtcclxuXHJcbmRlY2xhcmUgdmFyIHJlcXVpcmU6IGFueTtcclxuLy9pbXBvcnQgJCBmcm9tICdqcXVlcnknO1xyXG5jb25zdCAkID0gcmVxdWlyZSgnanF1ZXJ5Jyk7XHJcblxyXG5AQ29tcG9uZW50KHtcclxuICBzZWxlY3RvcjogJ0hSdWxlJyxcclxuICB0ZW1wbGF0ZTogYFxyXG4gICAgPGRpdiBjbGFzcz1cImgtcnVsZVwiPjwvZGl2PlxyXG4gIGAsXHJcbiAgc3R5bGVzOiBbYFxyXG4gICAgICAgLmgtcnVsZSB7XHJcbiAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwMDtcclxuICAgICAgICAgICBoZWlnaHQ6IDFweDtcclxuICAgICAgICAgICB3aWR0aDoxMDAlO1xyXG4gICAgICAgICAgIGJveC1zaGFkb3c6IDBweCAxcHggMHB4ICNmZmY7XHJcbiAgICAgICB9XHJcbiAgYF1cclxufSlcclxuZXhwb3J0IGNsYXNzIEhSdWxlIGV4dGVuZHMgQ29udGFpbmVyICBpbXBsZW1lbnRzIE9uRGVzdHJveSB7XHJcbiAgIFxyXG4gICAgLy8tLS1Qcm9wZXJ0aWVzIGRlZmluaXRpb25zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgcHJpdmF0ZSBfc3Ryb2tlQ29sb3I6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBwcml2YXRlIF9zaGFkb3dDb2xvcjogc3RyaW5nID0gXCJcIjtcclxuICAgIHByaXZhdGUgX3RoZW1lQ29sb3I6IHN0cmluZyA9IFwiXCI7XHJcblxyXG4gICAgLy8tLS0tc3Ryb2tlQ29sb3ItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgQElucHV0KClcclxuICAgIHNldCBzdHJva2VDb2xvcih2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICB9XHJcbiAgICBnZXQgc3Ryb2tlQ29sb3IoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3N0cm9rZUNvbG9yO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLy0tLS1zaGFkb3dDb2xvci0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICBASW5wdXQoKVxyXG4gICAgc2V0IHNoYWRvd0NvbG9yKHZhbHVlOiBzdHJpbmcpIHtcclxuICAgIH1cclxuICAgIGdldCBzaGFkb3dDb2xvcigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fc2hhZG93Q29sb3I7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vLS0tLXRoZW1lQ29sb3ItLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIEBJbnB1dCgpXHJcbiAgICBzZXQgdGhlbWVDb2xvcih2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICB9XHJcbiAgICBnZXQgdGhlbWVDb2xvcigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fdGhlbWVDb2xvcjtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLyoqXHJcbiAgICAgKiBDb25zdHJ1Y3RvclxyXG4gICAgICogQHBhcmFtIGVsZW1cclxuICAgICAqIEBwYXJhbSBjb21tb25TZXJ2aWNlXHJcbiAgICAgKiBAcGFyYW0gX3JlbmRlcmVyXHJcbiAgICAgKi9cclxuICAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtOiBFbGVtZW50UmVmLCBwcml2YXRlIGNvbW1vblNlcnZpY2U6IENvbW1vblNlcnZpY2UpIHtcclxuICAgICAgIHN1cGVyKGVsZW0sIGNvbW1vblNlcnZpY2UpO1xyXG4gICAgICAvLy1TVEFSVC0gQWRkZWQgYnkgUmloYWIuSiAgIC0gbmVlZGVkIHRvIGJlIHVzZWQgaW4gZHluYW1pY2FsbHkgYWRkZWQgSFJ1bGUuXHJcbiAgICAgICAkKCQodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQpWzBdKS5hdHRyKCdzZWxlY3RvcicsICdIUnVsZScpXHJcbiAgIH1cclxuIFxyXG4gICBuZ09uRGVzdHJveSgpOiB2b2lkIHtcclxuICAgICAgIHRyeXtcclxuICAgICAgICAgICBkZWxldGUgdGhpcy5fc3Ryb2tlQ29sb3I7XHJcbiAgICAgICAgICAgZGVsZXRlIHRoaXMuX3NoYWRvd0NvbG9yO1xyXG4gICAgICAgICAgIGRlbGV0ZSB0aGlzLl90aGVtZUNvbG9yO1xyXG4gICAgICAgfWNhdGNoKGVycm9yICl7XHJcbiAgICAgICAgICAgY29uc29sZS5lcnJvcignZXJyb3IgOicsZXJyb3IpXHJcbiAgICAgICB9XHJcbiAgIH1cclxufVxyXG4iXX0=