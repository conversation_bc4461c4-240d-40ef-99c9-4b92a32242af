/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * This class define the JSON response typing;
 */
export class JSONResponse {
    constructor() {
        this.request_reply = undefined;
        this.sysitem = undefined;
        this.singletons = undefined;
    }
}
if (false) {
    /** @type {?} */
    JSONResponse.prototype.request_reply;
    /** @type {?} */
    JSONResponse.prototype.selects;
    /** @type {?} */
    JSONResponse.prototype.sysitem;
    /** @type {?} */
    JSONResponse.prototype.singletons;
}
/**
 * This class define a root object typing.
 */
export class RootObject {
    constructor() {
        this.menus = undefined;
        this.name = undefined;
        this.value = undefined;
    }
}
if (false) {
    /** @type {?} */
    RootObject.prototype.menus;
    /** @type {?} */
    RootObject.prototype.name;
    /** @type {?} */
    RootObject.prototype.value;
}
/**
 * This class define the request relay typing.
 */
export class ProcessInfo {
    constructor() {
        this.process_status = undefined;
        this.running_seqnbr = undefined;
    }
}
if (false) {
    /** @type {?} */
    ProcessInfo.prototype.process_status;
    /** @type {?} */
    ProcessInfo.prototype.running_seqnbr;
}
/**
 * This class define the request relay typing.
 */
export class RequestReply {
    constructor() {
        this.message = undefined;
        this.status_ok = undefined;
    }
}
if (false) {
    /** @type {?} */
    RequestReply.prototype.message;
    /** @type {?} */
    RequestReply.prototype.status_ok;
}
/**
 * This class define selects typing.
 */
export class Selects {
    constructor() {
        this.select = undefined;
    }
}
if (false) {
    /** @type {?} */
    Selects.prototype.select;
}
/**
 * This class define the Select typing.
 */
export class Select {
    constructor() {
        this.id = undefined;
        this.option = undefined;
    }
}
if (false) {
    /** @type {?} */
    Select.prototype.id;
    /** @type {?} */
    Select.prototype.option;
}
/**
 * This class define combo box option
 */
export class Option {
    constructor() {
        this.content = undefined;
        this.selected = undefined;
        this.value = undefined;
        this.type = undefined;
    }
}
if (false) {
    /** @type {?} */
    Option.prototype.content;
    /** @type {?} */
    Option.prototype.selected;
    /** @type {?} */
    Option.prototype.value;
    /** @type {?} */
    Option.prototype.type;
}
export class Menus {
    constructor() {
        this.favourites = undefined;
        this.request_reply = undefined;
        this.alert = undefined;
        this.about = undefined;
        this.singletons = undefined;
        this.modules = undefined;
    }
}
if (false) {
    /** @type {?} */
    Menus.prototype.favourites;
    /** @type {?} */
    Menus.prototype.request_reply;
    /** @type {?} */
    Menus.prototype.alert;
    /** @type {?} */
    Menus.prototype.about;
    /** @type {?} */
    Menus.prototype.singletons;
    /** @type {?} */
    Menus.prototype.modules;
}
export class Favourites {
    constructor() {
        this.favourite = undefined;
    }
}
if (false) {
    /** @type {?} */
    Favourites.prototype.favourite;
}
export class Alert {
    constructor() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.programid = undefined;
        this.url = undefined;
    }
}
if (false) {
    /** @type {?} */
    Alert.prototype.itemid;
    /** @type {?} */
    Alert.prototype.menuaccess;
    /** @type {?} */
    Alert.prototype.undock;
    /** @type {?} */
    Alert.prototype.label;
    /** @type {?} */
    Alert.prototype.programid;
    /** @type {?} */
    Alert.prototype.url;
}
export class About {
    constructor() {
        this.itemid = undefined;
    }
}
if (false) {
    /** @type {?} */
    About.prototype.itemid;
}
export class Singletons {
    constructor() {
        this.rolename = undefined;
        this.alertCounter = undefined;
        this.hostid = undefined;
        this.userid = undefined;
    }
}
if (false) {
    /** @type {?} */
    Singletons.prototype.rolename;
    /** @type {?} */
    Singletons.prototype.alertCounter;
    /** @type {?} */
    Singletons.prototype.hostid;
    /** @type {?} */
    Singletons.prototype.userid;
}
export class Modules {
    constructor() {
        this.module = undefined;
    }
}
if (false) {
    /** @type {?} */
    Modules.prototype.module;
}
export class Favourite {
    constructor() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.icon = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
    }
}
if (false) {
    /** @type {?} */
    Favourite.prototype.itemid;
    /** @type {?} */
    Favourite.prototype.menuaccess;
    /** @type {?} */
    Favourite.prototype.icon;
    /** @type {?} */
    Favourite.prototype.undock;
    /** @type {?} */
    Favourite.prototype.label;
    /** @type {?} */
    Favourite.prototype.menuaction;
    /** @type {?} */
    Favourite.prototype.screenname;
    /** @type {?} */
    Favourite.prototype.moduleid;
    /** @type {?} */
    Favourite.prototype.programid;
}
export class Module {
    constructor() {
        this.icon = undefined;
        this.id = undefined;
        this.label = undefined;
        this.menu = undefined;
    }
}
if (false) {
    /** @type {?} */
    Module.prototype.icon;
    /** @type {?} */
    Module.prototype.id;
    /** @type {?} */
    Module.prototype.label;
    /** @type {?} */
    Module.prototype.menu;
}
export class Menu {
    constructor() {
        this.label = undefined;
        this.menuitem = undefined;
    }
}
if (false) {
    /** @type {?} */
    Menu.prototype.label;
    /** @type {?} */
    Menu.prototype.menuitem;
}
export class MenuItem {
    constructor() {
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.width = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
        this.menuOrderGroup = undefined;
        this.height = undefined;
        this.type = undefined;
        this.menuitem = undefined;
        this.tabIndex = undefined;
    }
}
if (false) {
    /** @type {?} */
    MenuItem.prototype.itemid;
    /** @type {?} */
    MenuItem.prototype.menuaccess;
    /** @type {?} */
    MenuItem.prototype.width;
    /** @type {?} */
    MenuItem.prototype.undock;
    /** @type {?} */
    MenuItem.prototype.label;
    /** @type {?} */
    MenuItem.prototype.menuaction;
    /** @type {?} */
    MenuItem.prototype.screenname;
    /** @type {?} */
    MenuItem.prototype.moduleid;
    /** @type {?} */
    MenuItem.prototype.programid;
    /** @type {?} */
    MenuItem.prototype.menuOrderGroup;
    /** @type {?} */
    MenuItem.prototype.height;
    /** @type {?} */
    MenuItem.prototype.type;
    /** @type {?} */
    MenuItem.prototype.menuitem;
    /** @type {?} */
    MenuItem.prototype.tabIndex;
}
export class TabItem {
    constructor() {
        this.icon = undefined;
        this.itemid = undefined;
        this.menuaccess = undefined;
        this.width = undefined;
        this.undock = undefined;
        this.label = undefined;
        this.menuaction = undefined;
        this.screenname = undefined;
        this.moduleid = undefined;
        this.programid = undefined;
        this.menuOrderGroup = undefined;
        this.height = undefined;
        this.type = undefined;
        this.menuitem = undefined;
        this.active = undefined;
        this.disabled = undefined;
        this.tabIndex = undefined;
    }
}
if (false) {
    /** @type {?} */
    TabItem.prototype.icon;
    /** @type {?} */
    TabItem.prototype.itemid;
    /** @type {?} */
    TabItem.prototype.menuaccess;
    /** @type {?} */
    TabItem.prototype.width;
    /** @type {?} */
    TabItem.prototype.undock;
    /** @type {?} */
    TabItem.prototype.label;
    /** @type {?} */
    TabItem.prototype.menuaction;
    /** @type {?} */
    TabItem.prototype.screenname;
    /** @type {?} */
    TabItem.prototype.moduleid;
    /** @type {?} */
    TabItem.prototype.programid;
    /** @type {?} */
    TabItem.prototype.menuOrderGroup;
    /** @type {?} */
    TabItem.prototype.height;
    /** @type {?} */
    TabItem.prototype.type;
    /** @type {?} */
    TabItem.prototype.menuitem;
    /** @type {?} */
    TabItem.prototype.active;
    /** @type {?} */
    TabItem.prototype.disabled;
    /** @type {?} */
    TabItem.prototype.tabIndex;
}
export class UserData {
    constructor() {
        this.currentEntity = undefined;
        this.baseUrl = undefined;
        this.localeDir = undefined;
        this.userid = undefined;
        this.languageid = undefined;
        this.systemLanguage = undefined;
        this.request_reply = undefined;
    }
}
if (false) {
    /** @type {?} */
    UserData.prototype.currentEntity;
    /** @type {?} */
    UserData.prototype.baseUrl;
    /** @type {?} */
    UserData.prototype.localeDir;
    /** @type {?} */
    UserData.prototype.userid;
    /** @type {?} */
    UserData.prototype.languageid;
    /** @type {?} */
    UserData.prototype.systemLanguage;
    /** @type {?} */
    UserData.prototype.request_reply;
}
//# sourceMappingURL=data:application/json;base64,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