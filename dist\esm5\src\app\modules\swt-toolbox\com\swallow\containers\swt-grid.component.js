/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { Container } from "./swt-container.component";
import { CommonService } from "../utils/common.service";
var Grid = /** @class */ (function (_super) {
    tslib_1.__extends(Grid, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function Grid(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        return _this;
    }
    Grid.decorators = [
        { type: Component, args: [{
                    selector: 'Grid',
                    template: "\n            <div  \n                 fxLayout=\"column\" class=\"noOutline\"\n                 fxLayoutGap=\"{{verticalGap}}\">\n                   <ng-content></ng-content>\n\n            </div>\n  ",
                    styles: ["\n  :host {\n      outline: none;\n    }\n    .noOutline{\n      outline: none;\n    }\n  "]
                }] }
    ];
    /** @nocollapse */
    Grid.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return Grid;
}(Container));
export { Grid };
if (false) {
    /**
     * @type {?}
     * @private
     */
    Grid.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    Grid.prototype.commonService;
}
/**
 *
 * GridRow component.
 * *
 */
var GridRow = /** @class */ (function (_super) {
    tslib_1.__extends(GridRow, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function GridRow(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        return _this;
    }
    GridRow.decorators = [
        { type: Component, args: [{
                    selector: 'GridRow',
                    template: "\n            <div  \n                 fxLayout=\"row\" class=\"noOutline\"\n                 fxLayoutGap=\"{{horizontalGap}}\" >\n                <ng-content></ng-content>\n            </div>\n    ",
                    styles: ["\n    :host {\n        outline: none;\n      }\n      .noOutline{\n        outline: none;\n      }\n    "]
                }] }
    ];
    /** @nocollapse */
    GridRow.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return GridRow;
}(Container));
export { GridRow };
if (false) {
    /**
     * @type {?}
     * @private
     */
    GridRow.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    GridRow.prototype.commonService;
}
/**
 *
 * GridItem component.
 *
 * *
 */
var GridItem = /** @class */ (function (_super) {
    tslib_1.__extends(GridItem, _super);
    /**
     * Constructor
     * @param elem
     * @param commonService
     */
    function GridItem(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        return _this;
    }
    GridItem.decorators = [
        { type: Component, args: [{
                    selector: 'GridItem',
                    template: "\n            <div   fxLayout=\"row\" class=\"noOutline\" fxLayoutGap=\"{{horizontalGap}}\" >\n               <ng-content></ng-content>\n            </div>\n    ",
                    styles: ["\n    :host {\n        outline: none;\n      }\n      .noOutline{\n        outline: none;\n      }\n    "]
                }] }
    ];
    /** @nocollapse */
    GridItem.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return GridItem;
}(Container));
export { GridItem };
if (false) {
    /**
     * @type {?}
     * @private
     */
    GridItem.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    GridItem.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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