{"version": 3, "sources": ["packages/animations/animations-browser-testing.umd.js"], "names": ["global", "factory", "exports", "module", "require", "define", "amd", "self", "ng", "animations", "browser", "testing", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "MockAnimationDriver", "prototype", "validateStyleProperty", "prop", "ɵvalidateStyleProperty", "matchesElement", "element", "selector", "ɵmatchesElement", "containsElement", "elm1", "elm2", "ɵcontainsElement", "query", "multi", "ɵinvokeQuery", "computeStyle", "defaultValue", "animate", "keyframes", "duration", "delay", "easing", "previousPlayers", "player", "MockAnimationPlayer", "log", "push", "_super", "_this", "call", "__finished", "__started", "previousStyles", "_onInitFns", "currentSnapshot", "ɵallowPreviousPlayerStylesMerge", "for<PERSON>ach", "styles_1", "keys", "__extends", "__", "constructor", "create", "onInit", "fn", "init", "finish", "destroy", "triggerMicrotask", "play", "hasStarted", "<PERSON><PERSON><PERSON><PERSON>", "captures", "kf", "AUTO_STYLE", "NoopAnimationPlayer", "defineProperty", "value"], "mappings": ";;;;;CAMC,SAAUA,EAAQC,GACI,iBAAZC,SAA0C,oBAAXC,OAAyBF,EAAQC,QAASE,QAAQ,uBAAwBA,QAAQ,gCACtG,mBAAXC,QAAyBA,OAAOC,IAAMD,OAAO,uCAAwC,UAAW,sBAAuB,+BAAgCJ,GACpIA,IAAzBD,EAASA,GAAUO,MAAsBC,GAAKR,EAAOQ,OAAUR,EAAOQ,GAAGC,WAAaT,EAAOQ,GAAGC,eAAkBT,EAAOQ,GAAGC,WAAWC,QAAUV,EAAOQ,GAAGC,WAAWC,YAAeV,EAAOQ,GAAGC,WAAWC,QAAQC,YAAeX,EAAOQ,GAAGC,WAAYT,EAAOQ,GAAGC,WAAWC,SAHjR,CAIEE,KAAM,SAAUV,EAASO,EAAYC,GAAW,aAkB9C,IAAIG,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,iBAChBC,wBAA2BC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,MACpDN,EAAGC,IAYxBO,EAAqC,WACrC,SAASA,KAoBT,OAlBAA,EAAoBC,UAAUC,sBAAwB,SAAUC,GAAQ,OAAOf,EAAQgB,uBAAuBD,IAC9GH,EAAoBC,UAAUI,eAAiB,SAAUC,EAASC,GAC9D,OAAOnB,EAAQoB,gBAAgBF,EAASC,IAE5CP,EAAoBC,UAAUQ,gBAAkB,SAAUC,EAAMC,GAAQ,OAAOvB,EAAQwB,iBAAiBF,EAAMC,IAC9GX,EAAoBC,UAAUY,MAAQ,SAAUP,EAASC,EAAUO,GAC/D,OAAO1B,EAAQ2B,aAAaT,EAASC,EAAUO,IAEnDd,EAAoBC,UAAUe,aAAe,SAAUV,EAASH,EAAMc,GAClE,OAAOA,GAAgB,IAE3BjB,EAAoBC,UAAUiB,QAAU,SAAUZ,EAASa,EAAWC,EAAUC,EAAOC,EAAQC,QACnE,IAApBA,IAA8BA,MAClC,IAAIC,EAAS,IAAIC,EAAoBnB,EAASa,EAAWC,EAAUC,EAAOC,EAAQC,GAElF,OADAvB,EAAoB0B,IAAIC,KAAKH,GACtBA,GAEXxB,EAAoB0B,OACb1B,EArB6B,GA0BpCyB,EAAqC,SAAUG,GAE/C,SAASH,EAAoBnB,EAASa,EAAWC,EAAUC,EAAOC,EAAQC,GACtE,IAAIM,EAAQD,EAAOE,KAAKxC,KAAM8B,EAAUC,IAAU/B,KAoBlD,OAnBAuC,EAAMvB,QAAUA,EAChBuB,EAAMV,UAAYA,EAClBU,EAAMT,SAAWA,EACjBS,EAAMR,MAAQA,EACdQ,EAAMP,OAASA,EACfO,EAAMN,gBAAkBA,EACxBM,EAAME,YAAa,EACnBF,EAAMG,WAAY,EAClBH,EAAMI,kBACNJ,EAAMK,cACNL,EAAMM,mBACF/C,EAAQgD,gCAAgChB,EAAUC,IAClDE,EAAgBc,QAAQ,SAAUb,GAC9B,GAAIA,aAAkBC,EAAqB,CACvC,IAAIa,EAAWd,EAAOW,gBACtBzC,OAAO6C,KAAKD,GAAUD,QAAQ,SAAUlC,GAAQ,OAAO0B,EAAMI,eAAe9B,GAAQmC,EAASnC,QAIlG0B,EA6CX,OAvGJ,SAASW,EAAUhD,EAAGC,GAElB,SAASgD,IAAOnD,KAAKoD,YAAclD,EADnCD,EAAcC,EAAGC,GAEjBD,EAAES,UAAkB,OAANR,EAAaC,OAAOiD,OAAOlD,IAAMgD,EAAGxC,UAAYR,EAAEQ,UAAW,IAAIwC,GAiC/ED,CAAUf,EAAqBG,GAyB/BH,EAAoBxB,UAAU2C,OAAS,SAAUC,GAAMvD,KAAK4C,WAAWP,KAAKkB,IAE5EpB,EAAoBxB,UAAU6C,KAAO,WACjClB,EAAO3B,UAAU6C,KAAKhB,KAAKxC,MAC3BA,KAAK4C,WAAWG,QAAQ,SAAUQ,GAAM,OAAOA,MAC/CvD,KAAK4C,eAETT,EAAoBxB,UAAU8C,OAAS,WACnCnB,EAAO3B,UAAU8C,OAAOjB,KAAKxC,MAC7BA,KAAKyC,YAAa,GAEtBN,EAAoBxB,UAAU+C,QAAU,WACpCpB,EAAO3B,UAAU+C,QAAQlB,KAAKxC,MAC9BA,KAAKyC,YAAa,GAGtBN,EAAoBxB,UAAUgD,iBAAmB,aACjDxB,EAAoBxB,UAAUiD,KAAO,WACjCtB,EAAO3B,UAAUiD,KAAKpB,KAAKxC,MAC3BA,KAAK0C,WAAY,GAErBP,EAAoBxB,UAAUkD,WAAa,WAAc,OAAO7D,KAAK0C,WACrEP,EAAoBxB,UAAUmD,cAAgB,WAC1C,IAAIvB,EAAQvC,KACR+D,KACJ3D,OAAO6C,KAAKjD,KAAK2C,gBAAgBI,QAAQ,SAAUlC,GAC/CkD,EAASlD,GAAQ0B,EAAMI,eAAe9B,KAEtCb,KAAK6D,cAIL7D,KAAK6B,UAAUkB,QAAQ,SAAUiB,GAC7B5D,OAAO6C,KAAKe,GAAIjB,QAAQ,SAAUlC,GAClB,UAARA,IACAkD,EAASlD,GAAQ0B,EAAME,WAAauB,EAAGnD,GAAQhB,EAAWoE,gBAK1EjE,KAAK6C,gBAAkBkB,GAEpB5B,EApE6B,CAqEtCtC,EAAWqE;;;;;;;;;;;;;;;;;;;;;;AA8Bb5E,EAAQoB,oBAAsBA,EAC9BpB,EAAQ6C,oBAAsBA,EAE9B/B,OAAO+D,eAAe7E,EAAS,cAAgB8E,OAAO", "sourcesContent": ["/**\n * @license Angular v7.2.16\n * (c) 2010-2019 Google LLC. https://angular.io/\n * License: MIT\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@angular/animations'), require('@angular/animations/browser')) :\n    typeof define === 'function' && define.amd ? define('@angular/animations/browser/testing', ['exports', '@angular/animations', '@angular/animations/browser'], factory) :\n    (global = global || self, factory((global.ng = global.ng || {}, global.ng.animations = global.ng.animations || {}, global.ng.animations.browser = global.ng.animations.browser || {}, global.ng.animations.browser.testing = {}), global.ng.animations, global.ng.animations.browser));\n}(this, function (exports, animations, browser) { 'use strict';\n\n    /*! *****************************************************************************\r\n    Copyright (c) Microsoft Corporation. All rights reserved.\r\n    Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\n    this file except in compliance with the License. You may obtain a copy of the\r\n    License at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\n    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\n    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\n    MERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\n    See the Apache Version 2.0 License for specific language governing permissions\r\n    and limitations under the License.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise */\r\n\r\n    var extendStatics = function(d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n\r\n    function __extends(d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    }\n\n    /**\n     * @publicApi\n     */\n    var MockAnimationDriver = /** @class */ (function () {\n        function MockAnimationDriver() {\n        }\n        MockAnimationDriver.prototype.validateStyleProperty = function (prop) { return browser.ɵvalidateStyleProperty(prop); };\n        MockAnimationDriver.prototype.matchesElement = function (element, selector) {\n            return browser.ɵmatchesElement(element, selector);\n        };\n        MockAnimationDriver.prototype.containsElement = function (elm1, elm2) { return browser.ɵcontainsElement(elm1, elm2); };\n        MockAnimationDriver.prototype.query = function (element, selector, multi) {\n            return browser.ɵinvokeQuery(element, selector, multi);\n        };\n        MockAnimationDriver.prototype.computeStyle = function (element, prop, defaultValue) {\n            return defaultValue || '';\n        };\n        MockAnimationDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers) {\n            if (previousPlayers === void 0) { previousPlayers = []; }\n            var player = new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n            MockAnimationDriver.log.push(player);\n            return player;\n        };\n        MockAnimationDriver.log = [];\n        return MockAnimationDriver;\n    }());\n    /**\n     * @publicApi\n     */\n    var MockAnimationPlayer = /** @class */ (function (_super) {\n        __extends(MockAnimationPlayer, _super);\n        function MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers) {\n            var _this = _super.call(this, duration, delay) || this;\n            _this.element = element;\n            _this.keyframes = keyframes;\n            _this.duration = duration;\n            _this.delay = delay;\n            _this.easing = easing;\n            _this.previousPlayers = previousPlayers;\n            _this.__finished = false;\n            _this.__started = false;\n            _this.previousStyles = {};\n            _this._onInitFns = [];\n            _this.currentSnapshot = {};\n            if (browser.ɵallowPreviousPlayerStylesMerge(duration, delay)) {\n                previousPlayers.forEach(function (player) {\n                    if (player instanceof MockAnimationPlayer) {\n                        var styles_1 = player.currentSnapshot;\n                        Object.keys(styles_1).forEach(function (prop) { return _this.previousStyles[prop] = styles_1[prop]; });\n                    }\n                });\n            }\n            return _this;\n        }\n        /* @internal */\n        MockAnimationPlayer.prototype.onInit = function (fn) { this._onInitFns.push(fn); };\n        /* @internal */\n        MockAnimationPlayer.prototype.init = function () {\n            _super.prototype.init.call(this);\n            this._onInitFns.forEach(function (fn) { return fn(); });\n            this._onInitFns = [];\n        };\n        MockAnimationPlayer.prototype.finish = function () {\n            _super.prototype.finish.call(this);\n            this.__finished = true;\n        };\n        MockAnimationPlayer.prototype.destroy = function () {\n            _super.prototype.destroy.call(this);\n            this.__finished = true;\n        };\n        /* @internal */\n        MockAnimationPlayer.prototype.triggerMicrotask = function () { };\n        MockAnimationPlayer.prototype.play = function () {\n            _super.prototype.play.call(this);\n            this.__started = true;\n        };\n        MockAnimationPlayer.prototype.hasStarted = function () { return this.__started; };\n        MockAnimationPlayer.prototype.beforeDestroy = function () {\n            var _this = this;\n            var captures = {};\n            Object.keys(this.previousStyles).forEach(function (prop) {\n                captures[prop] = _this.previousStyles[prop];\n            });\n            if (this.hasStarted()) {\n                // when assembling the captured styles, it's important that\n                // we build the keyframe styles in the following order:\n                // {other styles within keyframes, ... previousStyles }\n                this.keyframes.forEach(function (kf) {\n                    Object.keys(kf).forEach(function (prop) {\n                        if (prop != 'offset') {\n                            captures[prop] = _this.__finished ? kf[prop] : animations.AUTO_STYLE;\n                        }\n                    });\n                });\n            }\n            this.currentSnapshot = captures;\n        };\n        return MockAnimationPlayer;\n    }(animations.NoopAnimationPlayer));\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * Generated bundle index. Do not edit.\n     */\n\n    exports.MockAnimationDriver = MockAnimationDriver;\n    exports.MockAnimationPlayer = MockAnimationPlayer;\n\n    Object.defineProperty(exports, '__esModule', { value: true });\n\n}));\n//# sourceMappingURL=animations-browser-testing.umd.js.map\n"]}