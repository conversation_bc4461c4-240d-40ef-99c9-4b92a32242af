import { HTTPComms } from './httpcomms.service';
export declare class RemoteTransaction {
    private httpComms_;
    private programId_;
    private uniqueIdentifier_;
    static remoteTransactions: any[];
    remoteTransactionResult: any;
    protected httpComms: HTTPComms;
    protected programId: number;
    protected uniqueIdentifier: string;
    protected startTime: number;
    private status;
    /**
     * constructor
     *
     */
    constructor(httpComms_: HTTPComms, programId_: number, uniqueIdentifier_: string);
    start(): boolean;
    commit(): boolean;
    rollback(): boolean;
    isActive(): boolean;
    wasCommitted(): Boolean;
    wasRolledBack(): Boolean;
    private getTimer;
    /**
     * Remote transactions list of actions
     *
     */
    private remoteTransaction;
}
