/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver/FileSaver';
export class FileReference {
    constructor() { }
    /**
     * @param {?} bytearray
     * @param {?} fileName
     * @return {?}
     */
    save(bytearray, fileName) {
        try {
            /** @type {?} */
            var file = new File(bytearray, fileName, { type: "text/plain;charset=utf-8" });
            saveAs(file);
        }
        catch (error) {
            console.error("FileReference - [ save ] method error :", error);
        }
    }
}
FileReference.decorators = [
    { type: Injectable }
];
/** @nocollapse */
FileReference.ctorParameters = () => [];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmlsZS1yZWZlcmVuY2Uuc2VydmljZS5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy91dGlscy9maWxlLXJlZmVyZW5jZS5zZXJ2aWNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsVUFBVSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQzNDLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxzQkFBc0IsQ0FBQztBQUc5QyxNQUFNLE9BQU8sYUFBYTtJQUV4QixnQkFBZ0IsQ0FBQzs7Ozs7O0lBRVYsSUFBSSxDQUFDLFNBQWdCLEVBQUMsUUFBZ0I7UUFDekMsSUFBSTs7Z0JBQ0ksSUFBSSxHQUFHLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRSxRQUFRLEVBQUUsRUFBQyxJQUFJLEVBQUUsMEJBQTBCLEVBQUMsQ0FBQztZQUM1RSxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7U0FDaEI7UUFBQyxPQUFNLEtBQUssRUFBRTtZQUNYLE9BQU8sQ0FBQyxLQUFLLENBQUMseUNBQXlDLEVBQUUsS0FBSyxDQUFDLENBQUM7U0FDbkU7SUFDTCxDQUFDOzs7WUFaRixVQUFVIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW5qZWN0YWJsZSB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG5pbXBvcnQgeyBzYXZlQXMgfSBmcm9tICdmaWxlLXNhdmVyL0ZpbGVTYXZlcic7XHJcblxyXG5ASW5qZWN0YWJsZSgpXHJcbmV4cG9ydCBjbGFzcyBGaWxlUmVmZXJlbmNlIHtcclxuXHJcbiAgY29uc3RydWN0b3IoKSB7IH1cclxuXHJcbiAgcHVibGljIHNhdmUoYnl0ZWFycmF5OiBhbnlbXSxmaWxlTmFtZTogc3RyaW5nKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgICB2YXIgZmlsZSA9IG5ldyBGaWxlKGJ5dGVhcnJheSwgZmlsZU5hbWUsIHt0eXBlOiBcInRleHQvcGxhaW47Y2hhcnNldD11dGYtOFwifSk7XHJcbiAgICAgICAgICBzYXZlQXMoZmlsZSk7XHJcbiAgICAgIH0gY2F0Y2goZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGaWxlUmVmZXJlbmNlIC0gWyBzYXZlIF0gbWV0aG9kIGVycm9yIDpcIiwgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgfVxyXG4gIFxyXG59XHJcbiJdfQ==