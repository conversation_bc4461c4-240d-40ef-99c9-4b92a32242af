/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//dynamic
export class CancelExportEvent {
    /**
     * @param {?} type
     * @param {?=} bubbles
     * @param {?=} cancelable
     */
    constructor(type, bubbles = false, cancelable = false) {
        this.type = type;
        this.bubbles = bubbles;
        this.cancelable = cancelable;
        //      super(type)
    }
    /**
     * @return {?}
     */
    clone() {
        return new CancelExportEvent(this.type, this.bubbles, this.cancelable);
    }
    /**
     * @param {?} exportDTO
     * @return {?}
     */
    set exportDTO(exportDTO) {
        this._exportDTO = exportDTO;
    }
    /**
     * @return {?}
     */
    get exportDTO() {
        return this._exportDTO;
    }
}
CancelExportEvent.CANCEL_BUTTON_CLICK = "cancelclick";
CancelExportEvent.decorators = [
    { type: Injectable }
];
/** @nocollapse */
CancelExportEvent.ctorParameters = () => [
    { type: String },
    { type: Boolean },
    { type: Boolean }
];
if (false) {
    /** @type {?} */
    CancelExportEvent.CANCEL_BUTTON_CLICK;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype._exportDTO;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.type;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.bubbles;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.cancelable;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2FuY2VsLWV4cG9ydC1ldmVudC5zZXJ2aWNlLmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2V2ZW50cy9jYW5jZWwtZXhwb3J0LWV2ZW50LnNlcnZpY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE9BQU8sRUFBRSxVQUFVLEVBQUUsTUFBTSxlQUFlLENBQUM7O0FBRzNDLE1BQU0sT0FBTyxpQkFBaUI7Ozs7OztJQUk1QixZQUFvQixJQUFXLEVBQVMsVUFBZ0IsS0FBSyxFQUFTLGFBQW1CLEtBQUs7UUFBMUUsU0FBSSxHQUFKLElBQUksQ0FBTztRQUFTLFlBQU8sR0FBUCxPQUFPLENBQWM7UUFBUyxlQUFVLEdBQVYsVUFBVSxDQUFjO1FBQ2hHLG1CQUFtQjtJQUNqQixDQUFDOzs7O0lBRU0sS0FBSztRQUNSLE9BQU8sSUFBSSxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQzNFLENBQUM7Ozs7O0lBRUQsSUFBSSxTQUFTLENBQUUsU0FBaUI7UUFDNUIsSUFBSSxDQUFDLFVBQVUsR0FBRyxTQUFTLENBQUM7SUFDaEMsQ0FBQzs7OztJQUNELElBQUksU0FBUztRQUNULE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQztJQUMzQixDQUFDOztBQWZhLHFDQUFtQixHQUFVLGFBQWEsQ0FBQzs7WUFIMUQsVUFBVTs7Ozs7Ozs7OztJQUdULHNDQUF5RDs7Ozs7SUFEekQsdUNBQXVCOzs7OztJQUdYLGlDQUFtQjs7Ozs7SUFBQyxvQ0FBNkI7Ozs7O0lBQUMsdUNBQWdDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW5qZWN0YWJsZSB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG4vL2R5bmFtaWNcclxuQEluamVjdGFibGUoKVxyXG5leHBvcnQgY2xhc3MgQ2FuY2VsRXhwb3J0RXZlbnQge1xyXG4gIHByaXZhdGUgX2V4cG9ydERUTzphbnk7XHJcbiAgcHVibGljIHN0YXRpYyBDQU5DRUxfQlVUVE9OX0NMSUNLOnN0cmluZyA9IFwiY2FuY2VsY2xpY2tcIjtcclxuICBcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHR5cGU6c3RyaW5nLHByaXZhdGUgYnViYmxlczpib29sZWFuPWZhbHNlLHByaXZhdGUgY2FuY2VsYWJsZTpib29sZWFuPWZhbHNlKSB7IFxyXG4vLyAgICAgIHN1cGVyKHR5cGUpXHJcbiAgfVxyXG4gIFxyXG4gIHB1YmxpYyBjbG9uZSgpOmFueSB7XHJcbiAgICAgIHJldHVybiBuZXcgQ2FuY2VsRXhwb3J0RXZlbnQodGhpcy50eXBlLCB0aGlzLmJ1YmJsZXMsIHRoaXMuY2FuY2VsYWJsZSk7XHJcbiAgfVxyXG4gIFxyXG4gIHNldCBleHBvcnREVE8gKGV4cG9ydERUTyA6T2JqZWN0ICkgIHtcclxuICAgICAgdGhpcy5fZXhwb3J0RFRPID0gZXhwb3J0RFRPO1xyXG4gIH1cclxuICBnZXQgZXhwb3J0RFRPICgpICB7XHJcbiAgICAgIHJldHVybiB0aGlzLl9leHBvcnREVE87XHJcbiAgfVxyXG59XHJcbiJdfQ==