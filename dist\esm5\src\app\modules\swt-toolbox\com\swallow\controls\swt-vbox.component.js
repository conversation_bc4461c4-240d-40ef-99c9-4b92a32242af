/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, Component, Input, Renderer2 } from "@angular/core";
var VBox = /** @class */ (function (_super) {
    tslib_1.__extends(VBox, _super);
    //-------constructor-----------------------------------------------------------//
    function VBox(elem, commonService, _renderer) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._renderer = _renderer;
        _this.verticalGap = "6";
        return _this;
    }
    /**
     * @return {?}
     */
    VBox.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        _super.prototype.ngOnInit.call(this);
        $($(this.elem.nativeElement)[0]).attr('selector', 'VBox');
    };
    VBox.decorators = [
        { type: Component, args: [{
                    selector: 'VBox',
                    template: "\n    \n    <div fxLayout=\"column\" fxLayoutAlign=\"{{verticalAlign}} {{horizontalAlign}}\"   class=\"verticalLayout {{styleName}}\" scroll=\"scroll\" tabindex=\"-1\">\n        <ng-content ></ng-content>\n        <ng-container #_container></ng-container>\n    </div>\n  ",
                    styles: ["  \n    :host {\n      margin:  0px;\n      width: fit-content;\n      outline: none;\n    }\n    .verticalLayout {\n      width: 100%;\n      outline: none;\n    }\n  "]
                }] }
    ];
    /** @nocollapse */
    VBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    VBox.propDecorators = {
        styleName: [{ type: Input, args: ['styleName',] }]
    };
    return VBox;
}(Container));
export { VBox };
if (false) {
    /** @type {?} */
    VBox.prototype.styleName;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    VBox.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,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