import { After<PERSON><PERSON>wInit, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, EventEmitter } from '@angular/core';
import { CommonService } from "../utils/common.service";
export declare class SwtMultiselectCombobox implements OnInit, AfterViewInit, OnDestroy {
    private elem;
    private commonService;
    ITEM_SELECT: EventEmitter<any>;
    ITEM_DESELECT: EventEmitter<any>;
    SELECT_ALL: EventEmitter<any>;
    DESELECT_ALL: EventEmitter<any>;
    constructor(elem: ElementRef, commonService: CommonService);
    ngOnDestroy(): void;
    ngAfterViewInit(): void;
    placeholder: string;
    isDropdownDisabled: boolean;
    dataProvider: any[];
    defaultSelectedItems: any[];
    selectedItems: any[];
    dropdownSettings: {};
    itemLimit: any;
    private selectedItem;
    selects: any[];
    tooltipValue: any[];
    private _visibility;
    private logger;
    private SwtAlert;
    private toolTipObject;
    private _shiftUp;
    private _showAbove;
    width: string;
    height: string;
    dropHeight: string;
    ngOnInit(): void;
    shiftUp: any;
    showAbove: any;
    /* input to hold component visibility */
    visible: boolean;
    toolTip: any;
    onItemSelect(item: any): void;
    onSelectAll(items: any): void;
    onItemDeSelect(item: any): void;
    onDeSelectAll(items: any): void;
}
