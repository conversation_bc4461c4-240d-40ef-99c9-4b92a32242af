/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { focusManager } from "../managers/focus-manager.service";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ViewChild, ElementRef, Input, Component } from "@angular/core";
import { SwtUtil } from '../utils/swt-util.service';
/** @type {?} */
const $ = require('jquery');
// This list to store all styleNames that not contain Icon in the end.
/** @type {?} */
const styleNameList = ["arrowUp", "arrowDown", "upButtonEnable", "downButtonEnable"];
export class SwtButton extends Container {
    /**
     * Constructor.
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        //---Properties definitions---------------------------------------------------------------------------------------
        this._enabled = true;
        this._styleName = "";
        this._buttonMode = true;
        this._width = "auto";
        this._label = "";
        //-START- Added by Rihab.J   - needed to be used in dynamically added SwtButton.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtButton');
    }
    //---tabIndex-----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set tabIndex(value) {
        try {
            this._tabIndex = String(value);
            if ($(this.button))
                $(this.button).attr("tabindex", this._tabIndex);
        }
        catch (error) {
            console.error('method [ set tabIndex] - error:', error);
        }
    }
    /**
     * @return {?}
     */
    get tabIndex() {
        return this._tabIndex;
    }
    //---width--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set width(value) {
        if (Number(value) < 60) {
            $(this.swtbutton.nativeElement).removeClass("minWidthBtn");
        }
        this._width = this.adaptUnit(value, "auto");
        if (this._width.indexOf("px") !== -1) {
            this.setStyle("width", this._width, this.button);
        }
        else {
            this.setStyle("width", this._width);
            this.setStyle("width", "100%", this.button);
        }
    }
    /**
     * @return {?}
     */
    get width() {
        return this._width;
    }
    //---styleName----------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set styleName(value) {
        if (value && (value.indexOf("Icon") != -1 || styleNameList.indexOf(value) != -1)) {
            $(this.button).removeClass().addClass(value);
        }
        else {
            $(this.button).addClass(value);
        }
        this._styleName = value;
    }
    /**
     * @return {?}
     */
    get styleName() {
        return this._styleName;
    }
    //---label--------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set label(value) {
        /** @type {?} */
        var element = $($(this.swtbutton)[0].nativeElement).find('.buttonLabel');
        if (element.length > 0) {
            $(element).text(value);
        }
        this._label = value;
    }
    /**
     * @return {?}
     */
    get label() {
        return this._label;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set textDictionaryId(value) {
        if (value) {
            this.label = SwtUtil.getPredictMessage(value);
        }
    }
    //---ButtonMode---------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set buttonMode(value) {
        this._buttonMode = this.adaptValue(value);
    }
    /**
     * @return {?}
     */
    get buttonMode() {
        return this._buttonMode;
    }
    //---enabled------------------------------------------------------------------------------------------------------
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (String(this._enabled) != String(value)) {
            this._enabled = this.adaptValue(value);
            if (this._enabled) {
                this.addAllOutputsEventsListeners($(this.elem.nativeElement));
            }
            else {
                this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            }
            /** @type {?} */
            let minWidthBtn = $(this.button).hasClass("minWidthBtn");
            if (this.hasIcon()) {
                // - its a button with ICON.
                if (this._enabled) {
                    $(this.button).removeClass().addClass(this.styleName);
                }
                else {
                    $(this.button).removeClass().addClass(this.styleName + "Disabled");
                }
            }
            else {
                // - its a simple button.
                if (this._enabled) {
                    if (this.getComponentName() == "LINKBUTTON") {
                        $(this.button).removeClass("disabledLinkButton");
                    }
                    else {
                        $(this.button).removeClass().addClass("swtbtn");
                    }
                }
                else {
                    if (this.getComponentName() == "LINKBUTTON") {
                        $(this.button).addClass("disabledLinkButton");
                    }
                    else {
                        $(this.button).removeClass().addClass("swtbtn-disabled");
                    }
                }
                if (minWidthBtn) {
                    $(this.button).addClass("minWidthBtn");
                }
            }
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /**
     * ngOnInit.
     * @return {?}
     */
    ngOnInit() {
        super.ngOnInit();
        //Remove all events on hostElement
        this.removeEventsListeners(this.hostElement);
        $(this.button).hover((/**
         * @return {?}
         */
        function () {
            focusManager.hoverButton = this;
        }), (/**
         * @return {?}
         */
        function () {
            setTimeout((/**
             * @return {?}
             */
            () => {
                focusManager.hoverButton = null;
            }), 0);
        }));
        if (this._enabled) {
            this.addAllOutputsEventsListeners(this.hostElement);
        }
    }
    /**
     * Removes all handlers attached to the element.
     * @param {?} element
     * @return {?}
     */
    removeEventsListeners(element) {
        super.removeAllOuputsEventsListeners($(element));
        this.buttonMode = false;
    }
    /**
     * This method is used to change button view.
     * @private
     * @param {?=} state
     * @return {?}
     */
    setButtonState(state) {
        if (this.hasIcon()) {
            if ((this.styleName + state) != "printIconOver")
                $(this.button).removeClass().addClass(this.styleName + state);
            this.buttonMode = this._buttonMode;
        }
    }
    /**
     * This method is used to set focus to button.
     * @return {?}
     */
    setFocus() { }
    /**
     * @param {?} visibility
     * @return {?}
     */
    setVisible(visibility) {
        this.visible = visibility;
    }
    /**
     * returns DOM of the button element.
     * @return {?}
     */
    get button() {
        return this.swtbutton ? $(this.swtbutton.nativeElement)[0] : null;
    }
    /**
     * convert entered value as string to boolean.
     * @private
     * @param {?} value
     * @return {?}
     */
    adaptValue(value) {
        if (typeof (value) === "string") {
            if (value === "false") {
                value = false;
            }
            else {
                value = true;
            }
        }
        return value;
    }
    /**
     * Check if the button has an icon or not.
     * @private
     * @return {?}
     */
    hasIcon() {
        if (this.styleName != "" && (this.styleName.indexOf("Icon") != -1 || styleNameList.indexOf(this.styleName) != -1)) {
            return true;
        }
        return false;
    }
    /**
     * Destroy all event listeners
     * @return {?}
     */
    ngOnDestroy() {
        try {
            console.log('[SwtButton] ngOnDestroy ');
            this.removeEventsListeners(this.button);
            delete this.swtbutton;
            delete this._enabled;
            delete this._styleName;
            delete this._buttonMode;
            delete this._width;
            delete this._label;
        }
        catch (error) {
            console.error('error :', error);
        }
    }
}
SwtButton.decorators = [
    { type: Component, args: [{
                selector: 'SwtButton',
                template: `<div 
                    #swtbutton  
                    class="swtbtn minWidthBtn ">
                    <span class="truncate buttonLabel" ></span>
               </div>
    `,
                styles: [`
         :host{
             outline:none;
         }
        .truncate{
            margin:0px 2px 1px 2px; 
            text-overflow: ellipsis; 
            overflow: hidden; 
            white-space: nowrap;
            display:block;
        }
    `]
            }] }
];
/** @nocollapse */
SwtButton.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtButton.propDecorators = {
    swtbutton: [{ type: ViewChild, args: ['swtbutton',] }],
    tabIndex: [{ type: Input, args: ['tabIndex',] }],
    width: [{ type: Input }],
    styleName: [{ type: Input, args: ['styleName',] }],
    label: [{ type: Input }],
    textDictionaryId: [{ type: Input, args: ['textDictionaryId',] }],
    buttonMode: [{ type: Input }],
    enabled: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    SwtButton.prototype.swtbutton;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.hostButton;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._styleName;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._buttonMode;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._width;
    /**
     * @type {?}
     * @protected
     */
    SwtButton.prototype._label;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtButton.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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