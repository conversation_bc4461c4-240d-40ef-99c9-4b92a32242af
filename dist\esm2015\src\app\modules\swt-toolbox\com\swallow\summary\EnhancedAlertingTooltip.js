/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { StringUtils } from './../utils/string-utils.service';
import { SwtButton } from './../controls/swt-button.component';
import { SwtUtil } from './../utils/swt-util.service';
import { HTTPComms } from './../communication/httpcomms.service';
import { JSONReader } from './../jsonhandler/jsonreader.service';
import { Alert } from './../utils/alert.component';
import { ExternalInterface } from './../utils/external-interface.service';
import { CommonService } from './../utils/common.service';
import { SwtAlert } from './../utils/swt-alert.service';
import { CustomTree } from './../controls/swt-custom-tree.component';
import { VBox } from './../controls/swt-vbox.component';
import { Container } from './../containers/swt-container.component';
import { Component, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { CustomTreeEvent } from '../events/swt-events.module';
import { SwtLabel } from '../controls/swt-label.component';
export class EnhancedAlertingTooltip extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this.ITEM_CLICK = new EventEmitter();
        this.DISPLAY_LIST_CLICK = new EventEmitter();
        this.LINK_TO_SPECIF_CLICK = new EventEmitter();
        this.clickable = false;
        this.jsonReader = new JSONReader();
        /**
         * Communication Objects
         *
         */
        this.inputData = new HTTPComms(this.commonService);
        this.baseURL = SwtUtil.getBaseURL();
        this.actionMethod = "";
        this.actionPath = "";
        this.requestParams = [];
        this.hostId = "";
        this.swtAlert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        // this.dataForLabel.text = ExternalInterface.call('getBundle', 'text', 'label-dataFor', 'Data For');
        // this.labelnumberAccounts.text = ExternalInterface.call('getBundle', 'text', 'label-numberAccounts', 'Number of accounts');
        // this.labelnewDataExistFor.text = ExternalInterface.call('getBundle', 'text', 'label-newDataExistFor', 'New data exist for');
        this.closeButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.closeButton', null);
        this.displayListButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.displayListButton', null);
        this.linkToSpecificButton.label = SwtUtil.getPredictMessage('scenarioTooltipSummary.linkToSpecificButton', null);
        this.linkToSpecificButton.enabled = false;
        // this.customTooltip.focusOut = this.boxRollOutEventListner;
    }
    /**
     * @return {?}
     */
    closeTooltip() {
        console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 118 ~ EnhancedAlertingTooltip ~ closeTooltip ~ closeTooltip");
        /** @type {?} */
        const fromJSP = ExternalInterface.call('eval', 'fromJSP');
        console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 120 ~ EnhancedAlertingTooltip ~ closeTooltip ~ fromJSP", fromJSP);
        if (StringUtils.isTrue(fromJSP)) {
            window.close();
        }
        else {
            this.processBox.removeTooltip();
        }
    }
    /**
     * @return {?}
     */
    displayListEventhandler() {
        console.log(this.jsonReader.getSingletons().params);
        /** @type {?} */
        let selectedParamList = "";
        if (this.tree.selectedItem)
            selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        /** @type {?} */
        const allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        let dataArray = [];
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        const target = {
            itemParamList: selectedParamList,
            dataParams: dataArray,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            noode: this.tree.selectedItem,
            facilityId: this.parentDocument.tooltipFacilityId,
        };
        this.DISPLAY_LIST_CLICK.emit(target);
    }
    /**
     * @param {?} item
     * @param {?} paramsList
     * @return {?}
     */
    getParamsList(item, paramsList) {
        try {
            if (item) {
                if (paramsList) {
                    paramsList += " AND " + item.treeLevelName + "='" + item.treeLevelValue + "'";
                }
                else {
                    paramsList += item.treeLevelName + "='" + item.treeLevelValue + "'";
                }
                if (item.parentData) {
                    return this.getParamsList(item.parentData, paramsList);
                }
            }
            return paramsList;
        }
        catch (error) {
            console.log("🚀 ~ file: EnhancedAlertingTooltip.ts ~ line 130 ~ EnhancedAlertingTooltip ~ getParamsList ~ error", error);
        }
    }
    /**
     * @return {?}
     */
    linkToSpecifiEventHandler() {
        /** @type {?} */
        let selectedParamList = "";
        selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        /** @type {?} */
        const allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        let dataArray = [];
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        const target = {
            hostId: this.hostId,
            itemParamList: selectedParamList,
            dataParams: dataArray,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            noode: this.tree.selectedItem,
        };
        this.LINK_TO_SPECIF_CLICK.emit(target);
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        this.createCustomTip();
        /*jQuery(this.customTooltip.domElement).mouseleave(() => {
            jQuery(this.customTooltip.domElement).off('mouseleave');
            this.processBox.removeTooltip();
        });*/
        this.tree.ITEM_ACTIVATE.subscribe((/**
         * @param {?} item
         * @return {?}
         */
        (item) => {
            this.treeEventHandler(item);
        }));
        this.tree.addEventListener(CustomTreeEvent.ITEMRENDER, (/**
         * @param {?} item
         * @return {?}
         */
        (item) => {
            /** @type {?} */
            let name = item.data.name;
            /** @type {?} */
            let ALERTABLE = "Y";
            /** @type {?} */
            let alert = item.data.alert;
            /** @type {?} */
            let isBranch = item.data.isBranch;
            if (isBranch == true) {
                item.setStyle("fontWeight", "normal");
                item.setStyle("fontSize", 12);
                item.setStyle("color", "black");
            }
            else {
                if (alert == ALERTABLE) {
                    item.setStyle("fontSize", 12);
                    setTimeout((/**
                     * @return {?}
                     */
                    () => {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/alert.png')");
                    }), 0);
                }
                else {
                    item.setStyle("fontWeight", "normal");
                    item.setStyle("fontSize", 12);
                    setTimeout((/**
                     * @return {?}
                     */
                    () => {
                        $($(item.node.span).find(".fancytree-custom-icon")).css('background-image', "url('assets/images/predict_images/treefile.png')");
                    }), 0);
                }
            }
        }));
    }
    /**
     * @param {?} item
     * @return {?}
     */
    treeEventHandler(item) {
        console.log(this.jsonReader.getSingletons().params);
        /** @type {?} */
        let selectedParamList = "";
        /** @type {?} */
        let dataArray = [];
        selectedParamList = this.getParamsList(this.tree.selectedItem.data, selectedParamList);
        dataArray = this.extractParentFilterDataFromNode(this.tree.selectedItem, dataArray);
        /** @type {?} */
        const allParams = this.jsonReader.getSingletons().paramsSQL + (selectedParamList ? ' AND ' + selectedParamList : "");
        /** @type {?} */
        const target = {
            itemParamList: selectedParamList,
            paramsSQL: this.jsonReader.getSingletons().paramsSQL,
            allParams: allParams,
            dataParams: dataArray,
            noode: this.tree.selectedItem,
        };
        this.ITEM_CLICK.emit(target);
    }
    /**
     * @param {?} parentData
     * @param {?} dataArray
     * @return {?}
     */
    extractParentFilterDataFromNode(parentData, dataArray) {
        if (parentData != null && parentData != undefined && parentData.treeLevelName != null) {
            dataArray[parentData.treeLevelName] = parentData.treeLevelValue;
            dataArray = this.extractParentFilterDataFromNode(parentData.parentData, dataArray);
        }
        return dataArray;
    }
    /**
     * @private
     * @return {?}
     */
    boxRollOutEventListner() {
        console.log("ConfigurableToolTip -> boxRollOutEventListner -> boxRollOutEventListner");
    }
    /**
     * @private
     * @return {?}
     */
    createCustomTip() {
        try {
            this.requestParams = [];
            this.inputData.cbStart = this.startOfComms.bind(this);
            this.inputData.cbStop = this.endOfComms.bind(this);
            this.inputData.cbResult = (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                this.inputDataResult(event);
            });
            this.inputData.cbFault = this.inputDataFault.bind(this);
            this.inputData.encodeURL = false;
            this.actionPath = 'scenarioSummary.do?';
            this.actionMethod = 'method=getAlertingTooltipDetails';
            this.requestParams['entityId'] = this.parentDocument.tooltipEntityId;
            this.requestParams['currencyCode'] = this.parentDocument.tooltipCurrencyCode;
            this.requestParams['facilityId'] = this.parentDocument.tooltipFacilityId;
            this.requestParams['selectedDate'] = this.parentDocument.tooltipSelectedDate;
            this.requestParams['selectedAccountId'] = this.parentDocument.tooltipSelectedAccount;
            this.requestParams['selectedMvtId'] = this.parentDocument.tooltipMvtId;
            this.requestParams['selectedMatchId'] = this.parentDocument.tooltipMatchId;
            if (this.parentDocument.tooltipOtherParams != null) {
                for (var key in this.parentDocument.tooltipOtherParams) {
                    if (this.parentDocument.tooltipOtherParams.hasOwnProperty(key))
                        this.requestParams[key] = this.parentDocument.tooltipOtherParams[key];
                }
            }
            this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
            this.inputData.send(this.requestParams);
        }
        catch (error) {
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    inputDataResult(event) {
        try {
            // Checks the inputData and stops the communication
            if (this.inputData.isBusy()) {
                this.inputData.cbStop();
            }
            else {
                this.lastRecievedJSON = event;
                this.jsonReader.setInputJSON(this.lastRecievedJSON);
                if (this.jsonReader.getRequestReplyStatus()) {
                    if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
                        if (!this.jsonReader.isDataBuilding()) {
                            this.treeTitle.text = SwtUtil.getPredictMessage('scenarioSummary.scenTotals', null) + " (" + this.jsonReader.getSingletons().total + ") ";
                            this.facilityName.text = this.jsonReader.getSingletons().facilityName;
                            this.hostId = this.jsonReader.getSingletons().hostId;
                            /** @type {?} */
                            var parameters = (this.jsonReader.getSingletons().params).split("$#$,").join("\n");
                            /** @type {?} */
                            var parametersHtml = this.htmlEntities(parameters);
                            this.paramsList.htmlText = parametersHtml;
                            /** @type {?} */
                            let treeData = this.lastRecievedJSON.scenarioDetails.tree.root.node;
                            if (this.tree.dataProvider != treeData) {
                                this.tree.expandAll(CustomTree.LEVEL_1_STR);
                                this.tree.dataProvider = treeData;
                            }
                        }
                    }
                }
                else {
                    if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
                        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
                    }
                }
            }
        }
        catch (error) {
            console.log("EnhancedAlertingTooltip -> inputDataResult -> error", error);
        }
    }
    /**
     * @param {?} str
     * @return {?}
     */
    htmlEntities(str) {
        //Variable for errorLocation
        /** @type {?} */
        let errorLocation = 0;
        try {
            return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
                replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
        }
        catch (error) {
            console.log('error', error, str);
            SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MessageDetails.ts', "htmlEntities", errorLocation);
        }
    }
    /**
     * @return {?}
     */
    startOfComms() {
    }
    /**
     * Part of a callback function to all for control of the loading swf from the HTTPComms Object
     * @return {?}
     */
    endOfComms() {
    }
    /**
     * If a fault occurs with the connection with the server then display the lost connection label
     * @private
     * @param {?} event
     * @return {?}
     */
    inputDataFault(event) {
        this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
        this.swtAlert.show("fault " + this._invalidComms);
    }
    /**
     * This function is used to confirm the calculation process after clicking on the link button
     * @param {?} event
     * @return {?}
     */
    recalculateDataAlert(event) {
        if (this.clickable) {
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-recalculateConfirm', 'Are you sure? This may take some time?'), //text
            ExternalInterface.call('getBundle', 'text', 'label-recalculateConfirmAlertTitle', 'Are you sure? This may take some time?'), Alert.OK | Alert.CANCEL, this, //parent
            this.alertListener.bind(this), //close handler
            null, Alert.CANCEL); //icon and default button
        }
    }
    /**
     * This function is used to listen to the alert
     *
     * @private
     * @param {?} eventObj CloseEvent
     * @return {?}
     */
    alertListener(eventObj) {
        // Checks for Alert OK
        if (eventObj.detail == Alert.OK) {
            // Recalculate data if "OK" is clicked
            this.parentDocument.recalculateDataFunction();
        }
    }
}
EnhancedAlertingTooltip.decorators = [
    { type: Component, args: [{
                selector: 'EnhancedAlertingTooltip',
                template: `
        <VBox  paddingLeft="3"  paddingRight="3" paddingTop="3" #customTooltip width="100%" height="100%" verticalGap="2"> 
            <VBox id="treeContainer" #treeContainer width="100%" height="100%" class="left">
            <HBox width="100%" height="4%">
                <SwtLabel #ccyLabel  paddingLeft="5" textDictionaryId="label.alertSummaryTooltip.facility" ></SwtLabel>
                <SwtLabel  id="facilityName" #facilityName
                        fontWeight="normal"
                        paddingLeft="5">
                </SwtLabel>
            </HBox>
            

            <HBox height="8%" width="100%">
                <SwtLabel #ccyLabel paddingLeft="5" textDictionaryId="label.alertSummaryTooltip.parameters" ></SwtLabel>
                <SwtLabel  id="paramsList" #paramsList
                fontWeight="normal"
                paddingLeft="5"></SwtLabel>
            </HBox>
            <HBox height="5%" width="100%" paddingBottom="7">
            <SwtLabel  id="treeTitle" #treeTitle
                    fontWeight="normal"
                    paddingLeft="5">
            </SwtLabel>
            </HBox>
            <CustomTree id="tree" #tree
                        width="100%"
                        height="75%"
                        doubleClickEnabled="true">
            </CustomTree>


            <HBox height="6%" width="100%" paddingTop="7">
            <HBox height="100%" width="35%">
                <SwtButton  buttonMode="true"
                        id="closeButton" #closeButton
                            (click)="closeTooltip()"> </SwtButton>
            </HBox>
            <HBox height="100%" width="65%" horizontalAlign="right">
            <SwtButton (click)="displayListEventhandler()"
            buttonMode="true"
                            id="displayListButton" #displayListButton></SwtButton>
            <SwtButton buttonMode="true" 
             id="linkToSpecificButton" #linkToSpecificButton
                        (click)="linkToSpecifiEventHandler()"> </SwtButton>
            </HBox>
            </HBox>
            </VBox>
        </VBox>
  `,
                styles: [`
      `]
            }] }
];
/** @nocollapse */
EnhancedAlertingTooltip.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
EnhancedAlertingTooltip.propDecorators = {
    customTooltip: [{ type: ViewChild, args: ['customTooltip',] }],
    tree: [{ type: ViewChild, args: ['tree',] }],
    treeTitle: [{ type: ViewChild, args: ['treeTitle',] }],
    facilityName: [{ type: ViewChild, args: ['facilityName',] }],
    paramsList: [{ type: ViewChild, args: ['paramsList',] }],
    closeButton: [{ type: ViewChild, args: ['closeButton',] }],
    linkToSpecificButton: [{ type: ViewChild, args: ['linkToSpecificButton',] }],
    displayListButton: [{ type: ViewChild, args: ['displayListButton',] }],
    ITEM_CLICK: [{ type: Output, args: ['ITEM_CLICK',] }],
    DISPLAY_LIST_CLICK: [{ type: Output, args: ['DISPLAY_LIST_CLICK',] }],
    LINK_TO_SPECIF_CLICK: [{ type: Output, args: ['LINK_TO_SPECIF_CLICK',] }]
};
if (false) {
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.customTooltip;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.tree;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.treeTitle;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.facilityName;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.paramsList;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.closeButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.linkToSpecificButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.displayListButton;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.ITEM_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.DISPLAY_LIST_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.LINK_TO_SPECIF_CLICK;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.dataArray;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.parentDocument;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.clickable;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.processBox;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.jsonReader;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.lastRecievedJSON;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.prevRecievedJSON;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.initReceivedJSON;
    /**
     * Communication Objects
     *
     * @type {?}
     */
    EnhancedAlertingTooltip.prototype.inputData;
    /** @type {?} */
    EnhancedAlertingTooltip.prototype.baseURL;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.actionMethod;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.actionPath;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.requestParams;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype._invalidComms;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.hostId;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    EnhancedAlertingTooltip.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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