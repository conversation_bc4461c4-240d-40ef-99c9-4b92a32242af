/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//dynamic
var CancelExportEvent = /** @class */ (function () {
    function CancelExportEvent(type, bubbles, cancelable) {
        if (bubbles === void 0) { bubbles = false; }
        if (cancelable === void 0) { cancelable = false; }
        this.type = type;
        this.bubbles = bubbles;
        this.cancelable = cancelable;
        //      super(type)
    }
    /**
     * @return {?}
     */
    CancelExportEvent.prototype.clone = /**
     * @return {?}
     */
    function () {
        return new CancelExportEvent(this.type, this.bubbles, this.cancelable);
    };
    Object.defineProperty(CancelExportEvent.prototype, "exportDTO", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportDTO;
        },
        set: /**
         * @param {?} exportDTO
         * @return {?}
         */
        function (exportDTO) {
            this._exportDTO = exportDTO;
        },
        enumerable: true,
        configurable: true
    });
    CancelExportEvent.CANCEL_BUTTON_CLICK = "cancelclick";
    CancelExportEvent.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    CancelExportEvent.ctorParameters = function () { return [
        { type: String },
        { type: Boolean },
        { type: Boolean }
    ]; };
    return CancelExportEvent;
}());
export { CancelExportEvent };
if (false) {
    /** @type {?} */
    CancelExportEvent.CANCEL_BUTTON_CLICK;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype._exportDTO;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.type;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.bubbles;
    /**
     * @type {?}
     * @private
     */
    CancelExportEvent.prototype.cancelable;
}
//# sourceMappingURL=data:application/json;base64,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