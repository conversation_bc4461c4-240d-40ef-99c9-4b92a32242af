/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { Md5 } from 'ts-md5/dist/md5';
import { ExternalInterface } from './external-interface.service';
/** @type {?} */
const aesjs = require('aes-js');
/** @type {?} */
const CryptoJS = require("crypto-js");
/** @type {?} */
const AES = require("crypto-js/aes");
/** @type {?} */
const SHA256 = require("crypto-js/sha256");
/**
 * This service is used to encrypt data for
 * security purpose
 * <AUTHOR>
 */
export class Encryptor {
    constructor() { }
    /**
     * Calculates a hash in MD5 and returns result as Hex string
     * Project: http://code.google.com/p/as3crypto/
     *
     * See also flex/Java encryption on: http://groups.adobe.com/index.cfm?event=post.display&postid=29193
     *
     * @param {?} value
     * @return {?}
     */
    static hash(value) {
        return Md5.hashStr(value).toString();
    }
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} clear
     * @return {?}
     */
    static encrypt(clear) {
        /** @type {?} */
        const key = CryptoJS.enc.Utf8.parse('swallowtechltdtn');
        /** @type {?} */
        const iv = CryptoJS.enc.Utf8.parse('swallowtechltdtn');
        /** @type {?} */
        const encrypted = CryptoJS.AES.encrypt(clear, key, {
            keySize: 16,
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        return encrypted;
    }
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     *
     * @param {?} jsessionid
     * @param {?} userId
     * @param {?} clear
     * @return {?}
     */
    static encryptPredict(jsessionid, userId, clear) {
        /** @type {?} */
        var encrypted;
        try {
            encrypted = ExternalInterface.call('encryptPass', userId, clear);
        }
        catch (error) {
        }
        if (!encrypted) {
            /** @type {?} */
            let key = jsessionid.substring(0, jsessionid.length > 6 ? 6 : jsessionid.length);
            key += userId.substring(0, userId.length > 4 ? 4 : userId.length);
            // key = this.hash(key);
            key = CryptoJS.enc.Utf8.parse(key);
            /** @type {?} */
            const iv = CryptoJS.enc.Utf8.parse(key);
            encrypted = CryptoJS.AES.encrypt(clear, key, {
                keySize: 16,
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
        }
        return encrypted;
    }
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    static encode64(text) {
        /** @type {?} */
        var result = btoa(text);
        result = this.replaceAll(result, {
            '=': '(',
            '+': ')'
        });
        return result;
    }
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     *
     * @param {?} text
     * @return {?}
     */
    static decode64(text) {
        /** @type {?} */
        var result = this.replaceAll(text, {
            '(': '=',
            ')': '+'
        });
        result = atob(result);
        return result;
    }
    /**
     * Replace all occurences of a string
     *
     * @param {?} source
     * @param {?} map
     * @return {?}
     */
    static replaceAll(source, map) {
        for (var replaceToken in map) {
            source = source.split(replaceToken).join(map[replaceToken]);
        }
        return source;
    }
}
Encryptor.decorators = [
    { type: Injectable }
];
/** @nocollapse */
Encryptor.ctorParameters = () => [];
//# sourceMappingURL=data:application/json;base64,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