/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { VDividedEndResizeEvent, DividerResizeComplete } from "../events/swt-events.module";
import { StringUtils } from '../utils/string-utils.service';
import ResizeObserver from 'resize-observer-polyfill';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
var VDividedBox = /** @class */ (function (_super) {
    tslib_1.__extends(VDividedBox, _super);
    /**
     * constructor
     * @param elem
     * @param commonService
     * @param _rendred
     */
    function VDividedBox(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.DIVIDER_DRAG_COMPLETE = new EventEmitter();
        _this.DIVIDER_BUTTON_CLICK = new EventEmitter();
        _this._resize = new Function();
        _this._resizeStart = new Function();
        _this._resizeStop = new Function();
        _this._heightTop = null;
        _this._heightBottom = null;
        _this.TopContent = null;
        _this.BottomContent = null;
        _this.prevHeightTop = null;
        _this.prevHeightBottom = null;
        _this.heightTopPixel = 0;
        _this.heightBottomPixel = 0;
        _this._dividersAnimation = 'S';
        _this._extendedDividedBox = false;
        _this._liveDrag = false;
        _this.doResize = false;
        _this.startDrag = false;
        _this._maxHeightBottom = null;
        _this._minHeightBottom = null;
        _this._maxHeightTop = null;
        _this._minHeightTop = null;
        // Make panelBottom DOM reference.
        //---Output---------------------------------------------------------------------------------------
        _this.resize_ = new EventEmitter();
        _this.resizeStart_ = new EventEmitter();
        _this.resizeStop_ = new EventEmitter();
        // Make panelRight DOM reference.
        _this.defaultIcon = 'assets/images/resizer-handler.png';
        _this.vdividerClosed = 'assets/images/vdividerClosed.png';
        _this.vdividerOpened = 'assets/images/vdividerOpened.png';
        _this.forceNoEvent = false;
        return _this;
    }
    Object.defineProperty(VDividedBox.prototype, "maxHeightBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxHeightBottom;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxHeightBottom = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "minHeightBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minHeightBottom;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minHeightBottom = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "maxHeightTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._maxHeightTop;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._maxHeightTop = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "minHeightTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._minHeightTop;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._minHeightTop = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "resize", {
        //---resize getter and setter--------------------------------------------------------------------------------------------------------
        get: 
        //---resize getter and setter--------------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._resize;
        },
        set: /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resize = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "resizeStart", {
        //---resize Start getter and setter--------------------------------------------------------------------------------------------------------
        get: 
        //---resize Start getter and setter--------------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._resizeStart;
        },
        set: /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resizeStart = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "resizeStop", {
        //---resize Stop getter and setter--------------------------------------------------------------------------------------------------------
        get: 
        //---resize Stop getter and setter--------------------------------------------------------------------------------------------------------
        /**
         * @return {?}
         */
        function () {
            return this._resizeStop;
        },
        set: /**
         * @param {?} handler
         * @return {?}
         */
        function (handler) {
            this._resizeStop = handler;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        //---height-------------------------------------------------------------------------------------------------------
        set: 
        //---height-------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = this.adaptUnit(value, 'auto');
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        //---width--------------------------------------------------------------------------------------------------------
        set: 
        //---width--------------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._width = this.adaptUnit(value, 'auto');
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    VDividedBox.prototype.onButtonClickHandler = /**
     * @return {?}
     */
    function () {
        if (!this.startDrag) {
            if (this.extendedDividedBox) {
                /** @type {?} */
                var isClosed = false;
                if (this._dividersAnimation != 'S') {
                    if (this.heightTop == '0') {
                        $(this.panelTop.nativeElement).css('display', 'flex');
                        this.heightTop = this.prevHeightTop;
                        isClosed = false;
                    }
                    else {
                        this.heightTop = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.vdividerClosed + ')';
                    }
                }
                else {
                    if (this.heightBottom == '0') {
                        $(this.panelBottom.nativeElement).css('display', 'flex');
                        this.heightBottom = this.prevHeightBottom;
                        isClosed = false;
                    }
                    else {
                        this.heightBottom = '0';
                        isClosed = true;
                        this.splitter.nativeElement.style.backgroundImage = 'url(' + this.vdividerOpened + ')';
                    }
                }
                this.DIVIDER_BUTTON_CLICK.emit({ id: this.id, isClosed: isClosed, type: 'DIVIDER_BUTTON_CLICK' });
            }
            DividerResizeComplete.emit({ id: this.id });
        }
    };
    Object.defineProperty(VDividedBox.prototype, "heightBottom", {
        get: /**
         * @return {?}
         */
        function () {
            return this._heightBottom;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if ('0' !== this.heightBottom && '0%' !== this.heightBottom)
                this.prevHeightBottom = this.heightBottom;
            this._heightBottom = value;
            if (('' + this._heightBottom).indexOf("%") > -1) {
                /** @type {?} */
                var tmp = Number(('' + this._heightBottom).replace('%', ''));
                /** @type {?} */
                var newTopWidth = $($(this.panelTop.nativeElement).parent()).height() * ((100 - tmp) / 100);
                $(this.panelTop.nativeElement).resizable("resizeTo", { height: newTopWidth - 17 });
            }
            else {
                /** @type {?} */
                var newTopWidth = ($($(this.panelTop.nativeElement).parent()).height() - this._heightBottom);
                $(this.panelTop.nativeElement).resizable("resizeTo", { height: Number(newTopWidth - 17) });
            }
            if (value === '0' || value === '0%') {
                this.heightBottomPixel = 0;
                // this.forceZeroWidth = true;
                //Commented cause issue if collopsed and no place for splitter
                // $($(this.panelBottom.nativeElement).children()[0]).css('display', 'none');
                // $(this.panelTop.nativeElement).css("height", '100%');
                // $(this.panelTop.nativeElement).css("margin-top", '-10px');
                // $(this.panelTop.nativeElement).css("padding-top", '10px');
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "dividersAnimation", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dividersAnimation;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                this._dividersAnimation = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "extendedDividedBox", {
        get: /**
         * @return {?}
         */
        function () {
            return this._extendedDividedBox;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = StringUtils.isTrue(value);
            if (value) {
                this._extendedDividedBox = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(VDividedBox.prototype, "liveDrag", {
        get: /**
         * @return {?}
         */
        function () {
            return this._liveDrag;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = StringUtils.isTrue(value);
            if (value) {
                this._liveDrag = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} value
     * @return {?}
     */
    VDividedBox.prototype.setHeightTopWithoutEvent = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.forceNoEvent = true;
        this.heightTop = value;
    };
    /**
     * @param {?} value
     * @return {?}
     */
    VDividedBox.prototype.setHeightBottomWithoutEvent = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.forceNoEvent = true;
        this.heightBottom = value;
    };
    Object.defineProperty(VDividedBox.prototype, "heightTop", {
        get: /**
         * @return {?}
         */
        function () {
            return this._heightTop;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if ('0' !== this.heightTop && '0%' !== this.heightTop)
                this.prevHeightTop = this.heightTop;
            this._heightTop = value;
            if (value === '0') {
                $(this.panelTop.nativeElement).css('display', 'none');
                this.heightTopPixel = 0;
            }
            else {
                if (('' + this._heightTop).indexOf("%") > -1) {
                    /** @type {?} */
                    var tmp = Number(('' + this._heightTop).replace('%', ''));
                    /** @type {?} */
                    var newLeftHeight = $($(this.panelTop.nativeElement).parent()).height() * (tmp / 100);
                    $(this.panelTop.nativeElement).resizable("resizeTo", { height: newLeftHeight });
                }
                else {
                    // this.setStyle("width", this._heightTop , $(this.panelLeft.nativeElement))
                    $(this.panelTop.nativeElement).resizable("resizeTo", { height: Number(value) });
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * ngOnInit
     */
    /**
     * ngOnInit
     * @return {?}
     */
    VDividedBox.prototype.ngOnInit = /**
     * ngOnInit
     * @return {?}
     */
    function () {
        try {
            $($(this.elem.nativeElement)[0]).attr('selector', 'VDividedBox');
            // set vdividedbox height and width
            this.setStyle("height", this.height, $(this.vdividedboxContainer.nativeElement.parentElement));
            this.setStyle("width", this.width, $(this.vdividedboxContainer.nativeElement.parentElement));
            this.setStyle("width", "100%", $(this.vdividedboxContainer.nativeElement));
            this._heightTop = $($(this.panelTop.nativeElement).children()[0]).attr("height");
            this._heightBottom = $($(this.panelBottom.nativeElement).children()[0]).attr("height");
            if (this._heightTop)
                this.setStyle("height", this._heightTop, this.panelTop.nativeElement);
            if (this._heightBottom)
                this.setStyle("height", this._heightBottom, this.panelBottom.nativeElement);
            if (this.extendedDividedBox) {
                if (this._dividersAnimation != 'S') {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.vdividerOpened + ')';
                }
                else {
                    this.splitter.nativeElement.style.backgroundImage = 'url(' + this.vdividerClosed + ')';
                }
            }
        }
        catch (error) {
            console.error('methode [] error :', error);
        }
    };
    /**
     * ngAfterViewInit
     */
    /**
     * ngAfterViewInit
     * @return {?}
     */
    VDividedBox.prototype.ngAfterViewInit = /**
     * ngAfterViewInit
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            this.TopContent = $($(this.panelTop.nativeElement).children()[0]);
            this.BottomContent = $($(this.panelBottom.nativeElement).children()[0]);
            $(this.TopContent).css("height", "100%");
            $(this.BottomContent).css("height", "100%");
            setTimeout((/**
             * @return {?}
             */
            function () {
                //              set the resizing properties to the vdividedbox
                //              set the resizing properties to the vdividedbox
                /** @type {?} */
                var parentHeight = $($(_this.panelTop.nativeElement).parent()).height();
                $(_this.panelTop.nativeElement).resizable({
                    handleSelector: ".splitter-horizontal",
                    resizeWidth: false,
                    helper: _this._liveDrag ? false : "vdividedbox-resizable-helper",
                    maxHeight: parentHeight,
                    minHeight: 0,
                    handles: 's',
                    resize: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        _this._resize(event);
                        _this.resize_.emit(event);
                        event.preventDefault();
                        if (_this.extendedDividedBox) {
                            if (_this._dividersAnimation != 'S') {
                                if ($(_this.panelTop.nativeElement).css('display') == 'none') {
                                    $(_this.panelTop.nativeElement).css('display', 'flex');
                                    // TabSelectEvent.emit(null);
                                }
                            }
                            else {
                                if ($(_this.panelBottom.nativeElement).css('display') == 'none') {
                                    $(_this.panelBottom.nativeElement).css('display', 'flex');
                                    // TabSelectEvent.emit(null);
                                }
                            }
                        }
                    }),
                    create: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        if (_this.extendedDividedBox) {
                            $($(_this.panelTop.nativeElement).children('.ui-resizable-handle')[0]).click((/**
                             * @return {?}
                             */
                            function () {
                                _this.onButtonClickHandler();
                            }));
                        }
                    }),
                    start: (/**
                     * @param {?} event
                     * @return {?}
                     */
                    function (event) {
                        _this.startDrag = true;
                        $('iframe').css('pointer-events', 'none');
                        _this.prevHeightTop = _this._heightTop;
                        /** @type {?} */
                        var maxHeightTop = !_this.minHeightBottom ? $($(_this.panelTop.nativeElement).parent()).height() - 10 : $($(_this.panelTop.nativeElement).parent()).height() - _this.minHeightBottom - 10;
                        /** @type {?} */
                        var minHeightTop = !_this.maxHeightBottom ? 1 : $($(_this.panelTop.nativeElement).parent()).height() - _this.maxHeightBottom;
                        $(_this.panelTop.nativeElement).resizable("option", "maxHeight", maxHeightTop);
                        $(_this.panelTop.nativeElement).resizable("option", "minHeight", minHeightTop);
                        _this._resizeStart(event);
                        _this.resizeStart_.emit(event);
                        event.preventDefault();
                    }),
                    stop: (/**
                     * @param {?} event
                     * @param {?} ui
                     * @return {?}
                     */
                    function (event, ui) {
                        setTimeout((/**
                         * @return {?}
                         */
                        function () {
                            $('iframe').css('pointer-events', 'auto');
                            _this.startDrag = false;
                            /** @type {?} */
                            var cellPercentHeight = 100 * ui.originalElement.outerHeight() / $($(_this.panelTop.nativeElement).parent()).innerHeight();
                            if (!isNaN(cellPercentHeight))
                                ui.originalElement.css('height', Math.ceil(cellPercentHeight) + '%');
                            /** @type {?} */
                            var nextCell = ui.originalElement.next().next();
                            /** @type {?} */
                            var nextPercentHeight = 100 * nextCell.outerHeight() / $($(_this.panelTop.nativeElement).parent()).innerHeight();
                            if (!isNaN(nextPercentHeight))
                                nextCell.css('height', Math.floor(nextPercentHeight) + '%');
                            /** @type {?} */
                            var panelBottomHeight = parentHeight - event.target.clientHeight;
                            $(_this.panelBottom.nativeElement).css("height", panelBottomHeight - 13);
                            if (_this.extendedDividedBox) {
                                if (ui.size.height == 1) {
                                    _this.prevHeightTop = ui.originalSize.height;
                                    _this.heightTop = '0';
                                    _this._heightBottom = '100%';
                                }
                                else {
                                    if (cellPercentHeight > 97) {
                                        if (!_this.doResize) {
                                            _this.doResize = true;
                                            /** @type {?} */
                                            var percentOrginalHeight = 100 * ui.originalSize.height / $($(_this.panelTop.nativeElement).parent()).innerHeight();
                                            if (percentOrginalHeight < 97)
                                                _this.prevHeightBottom = Math.round((100 - percentOrginalHeight)) + '%';
                                            // if(this.heightBottom != '0')
                                            _this.heightBottom = '0';
                                            _this._heightTop = '100%';
                                        }
                                        else {
                                            _this.doResize = false;
                                        }
                                    }
                                    else {
                                        _this.prevHeightTop = _this._heightTop = Math.round(cellPercentHeight) + '%';
                                        _this.prevHeightBottom = _this._heightBottom = nextPercentHeight + '%';
                                    }
                                    if (_this._dividersAnimation != 'S') {
                                        if ($(_this.panelTop.nativeElement).css('display') == 'none') {
                                            $(_this.panelTop.nativeElement).css('display', 'flex');
                                            // TabSelectEvent.emit(null);
                                        }
                                    }
                                    else {
                                        if ($(_this.panelBottom.nativeElement).css('display') == 'none') {
                                            $(_this.panelBottom.nativeElement).css('display', 'flex');
                                            // TabSelectEvent.emit(null);
                                        }
                                    }
                                }
                            }
                            if (_this.heightTop == '0') {
                                _this.heightTopPixel = 0;
                            }
                            else {
                                _this.heightTopPixel = Math.round(ui.originalElement.outerHeight()) || 0;
                            }
                            if (_this.heightBottom == '0') {
                                _this.heightBottomPixel = 0;
                            }
                            else {
                                _this.heightBottomPixel = Math.round(nextCell.outerHeight()) || 0;
                            }
                            if (!_this.forceNoEvent) {
                                _this._resizeStop();
                                _this.resizeStop_.emit(event);
                                VDividedEndResizeEvent.emit(event);
                                _this.DIVIDER_DRAG_COMPLETE.emit({ id: _this.id, type: 'DIVIDER_DRAG_COMPLETE' });
                            }
                            else {
                                _this.forceNoEvent = false;
                            }
                            if (_this.extendedDividedBox) {
                                if (_this._dividersAnimation != 'S') {
                                    if (_this.heightTopPixel == 0) {
                                        _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.vdividerClosed + ')';
                                    }
                                    else {
                                        _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.vdividerOpened + ')';
                                    }
                                }
                                else {
                                    if (_this.heightBottomPixel == 0) {
                                        _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.vdividerOpened + ')';
                                    }
                                    else {
                                        _this.splitter.nativeElement.style.backgroundImage = 'url(' + _this.vdividerClosed + ')';
                                    }
                                }
                            }
                            DividerResizeComplete.emit({ id: _this.id });
                        }), 0);
                    })
                });
                if (_this.minHeightBottom || _this.maxHeightBottom) {
                    /** @type {?} */
                    var ro = new ResizeObserver((/**
                     * @param {?} entries
                     * @param {?} observer
                     * @return {?}
                     */
                    function (entries, observer) {
                        /** @type {?} */
                        var maxHeightTop = !_this.minHeightBottom ? $($(_this.panelTop.nativeElement).parent()).height() - 10 : $($(_this.panelTop.nativeElement).parent()).height() - _this.minHeightBottom - 10;
                        /** @type {?} */
                        var minHeightTop = !_this.maxHeightBottom ? 1 : $($(_this.panelTop.nativeElement).parent()).height() - _this.maxHeightBottom;
                        $(_this.panelTop.nativeElement).resizable("option", "maxHeight", maxHeightTop);
                        $(_this.panelTop.nativeElement).resizable("option", "minHeight", minHeightTop);
                        if ($(_this.panelTop.nativeElement).height() < minHeightTop) {
                            _this.setHeightTopWithoutEvent('' + minHeightTop);
                        }
                        else {
                            // this.setHeightTopWithoutEvent(''+this.heightTop);
                        }
                    }));
                    ro.observe(_this.vdividedboxContainer.nativeElement);
                }
            }), 0);
        }
        catch (error) {
            console.error('methode [] error :', error);
        }
    };
    /**
     * ngOnDestroy
     */
    /**
     * ngOnDestroy
     * @return {?}
     */
    VDividedBox.prototype.ngOnDestroy = /**
     * ngOnDestroy
     * @return {?}
     */
    function () {
        try {
            this.removeAllOuputsEventsListeners($(this.elem.nativeElement));
            delete this._height;
            delete this._width;
            delete this._resize;
            delete this._resizeStart;
            delete this._resizeStop;
            delete this._heightTop;
            delete this._heightBottom;
            delete this.TopContent;
            delete this.BottomContent;
            delete this.vdividedboxContainer;
            delete this.panelTop;
            delete this.panelBottom;
        }
        catch (error) {
            console.error('error :', error);
        }
    };
    VDividedBox.decorators = [
        { type: Component, args: [{
                    selector: 'VDividedBox',
                    template: "\n        <div #vdividedboxContainer fxLayout=\"column\" class=\"panel-container-vertical\">\n            \n            <div #panelTop fxLayout=\"column\" fxLayoutGap=\"{{verticalGap}}\" class=\"panel-top\">\n                <ng-content select=\".top\"></ng-content>\n            </div>\n            <div class=\"splitter-horizontal\" #splitter  (click)=\"onButtonClickHandler()\" ></div>\n            <div #panelBottom fxLayout=\"column\" fxLayoutGap=\"{{verticalGap}}\" class=\"panel-bottom\">\n                <ng-content select=\".bottom\"></ng-content>\n            </div>\n        </div>",
                    styles: ["\n        /* vertical panel */\n\n        .panel-container-vertical {\n            height: 100%;\n            overflow: hidden;\n            background-color: #D6E3FE;\n            margin-bottom: 5px;\n         }\n\n        .panel-top {\n            flex: 0 0 auto;\n            width: 100% !important;\n        }\n\n        .ui-resizable-se {\n            display: none;\n        }\n\n        .splitter-horizontal {\n            flex: 0 0 auto;\n            height: 10px !important;\n            background-image: url(\"assets/images/resizer-handler.png\");\n            background-repeat: no-repeat;\n            background-position: center;\n        }\n\n        .panel-bottom {\n            flex: 1 1 auto;\n            width: 100% !important;\n        }\n\n    "]
                }] }
    ];
    /** @nocollapse */
    VDividedBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    VDividedBox.propDecorators = {
        DIVIDER_DRAG_COMPLETE: [{ type: Output, args: ['DIVIDER_DRAG_COMPLETE',] }],
        DIVIDER_BUTTON_CLICK: [{ type: Output, args: ['DIVIDER_BUTTON_CLICK',] }],
        maxHeightBottom: [{ type: Input }],
        minHeightBottom: [{ type: Input }],
        maxHeightTop: [{ type: Input }],
        minHeightTop: [{ type: Input }],
        vdividedboxContainer: [{ type: ViewChild, args: ["vdividedboxContainer",] }],
        panelTop: [{ type: ViewChild, args: ["panelTop",] }],
        panelBottom: [{ type: ViewChild, args: ["panelBottom",] }],
        resize_: [{ type: Output, args: ['resize',] }],
        resizeStart_: [{ type: Output, args: ['resizeStart',] }],
        resizeStop_: [{ type: Output, args: ['resizeStop',] }],
        splitter: [{ type: ViewChild, args: ["splitter",] }],
        height: [{ type: Input }],
        width: [{ type: Input }],
        heightBottom: [{ type: Input }],
        dividersAnimation: [{ type: Input }],
        extendedDividedBox: [{ type: Input }],
        liveDrag: [{ type: Input }],
        heightTop: [{ type: Input }]
    };
    return VDividedBox;
}(Container));
export { VDividedBox };
if (false) {
    /** @type {?} */
    VDividedBox.prototype.DIVIDER_DRAG_COMPLETE;
    /** @type {?} */
    VDividedBox.prototype.DIVIDER_BUTTON_CLICK;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._height;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._width;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._resize;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._resizeStart;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._resizeStop;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._heightTop;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._heightBottom;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.TopContent;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.BottomContent;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.prevHeightTop;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.prevHeightBottom;
    /** @type {?} */
    VDividedBox.prototype.heightTopPixel;
    /** @type {?} */
    VDividedBox.prototype.heightBottomPixel;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._dividersAnimation;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._extendedDividedBox;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._liveDrag;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.doResize;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.startDrag;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._maxHeightBottom;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._minHeightBottom;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._maxHeightTop;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype._minHeightTop;
    /** @type {?} */
    VDividedBox.prototype.vdividedboxContainer;
    /** @type {?} */
    VDividedBox.prototype.panelTop;
    /** @type {?} */
    VDividedBox.prototype.panelBottom;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.resize_;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.resizeStart_;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.resizeStop_;
    /** @type {?} */
    VDividedBox.prototype.splitter;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.defaultIcon;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.vdividerClosed;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.vdividerOpened;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.forceNoEvent;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    VDividedBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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