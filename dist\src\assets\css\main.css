/*
 This style sheet contain icons created with sprites images...
 to add new Icon buttons follow this steps.
 	1 - create the sprite image then put it in spirites folder.
 	2 - copy css from sprites and change the class name to styleName+[Over,Up, Down];
*/
:root {
    /*constant to save default paddingTop*/ 
    --paddingTop: 2px;
    /*constant to save default paddingRight*/
    --paddingRight: 2px;
    /*constant to save default paddingBottom*/
    --paddingBottom: 2px;
    /*constant to save default paddingTop*/
    --paddingLeft: 2px;
    /*constant to save default paddingLeft*/
    --marginTop: 2px;
    /*constant to save default marginRight*/
    --marginRight: 2px;
    /*constant to save default marginBottom*/
    --marginBottom: 2px;
    /*constant to save default marginLeft*/
    --marginLeft: 2px; 
    /*constant to save default textColor
    --textColor: string = "#0B333C";  
    /*constant to save default fontSize*/
    --fontSize: 11px;
    /*constant to save default borderRadius*/
    --borderRadius: 3px;
    /*constant to save default primaryColor*/
    --primaryColor: #369;
    /*constant to save default secondaryColor*/
    --secondaryColor: #ccecff;
    /*constant to save default textBold*/
    --textBold: bold;
    /*constant to save default fontFamily*/
    --fontFamily: Verdana,helvetica;  
    /*constant to save default margins*/
    --margins: 3px;
}
/* define csvDisabled icon */
.csvIconDisabled {
    width: 17px; height: 16px;
    background: url('../spirites/report.png') -10px -10px;
    margin: auto 3px;
}
/* define csvDown icon */
.csvIconDown {
    width: 17px; height: 16px;
    background: url('../spirites/report.png') -47px -46px;
    margin: auto 3px;
}
/* define csvOver icon */
.csvIconOver {
    width: 17px; height: 16px;
    background: url('../spirites/report.png') -47px -10px;
    margin: auto 3px;
}
/* define csvUp icon */
.csvIcon {
    width: 17px; height: 16px;
    background: url('../spirites/report.png') -10px -46px;
    margin: auto 3px;
}
/* define excelDisabled icon */
.excelIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -84px -10px;
    margin: auto 3px;
}
/* define excelDown icon */
.excelIconDown {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -84px -46px;
    margin: auto 3px;
}
/* define excelOver icon */
.excelIconOver {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -10px -82px;
    margin: auto 3px;
}
/* define excelUp icon */
.excelIcon {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -46px -82px;
    margin: auto 3px;
}
/* define help icon */
.help {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -10px -118px;
    margin: auto 3px;
}
.graphIcon{
    width: 16px; height: 16px;
    background: url('../images/SwtPieChartUp.png');
    margin: auto 3px;
}
/* define graphDisabled icon */
.graphIconDisabled {
    width: 16px; height: 16px;
    background: url('../images/SwtPieChartDisabled.png');
    margin: auto 3px;
}
/* define graphDown icon */
.graphIconDown {
    width: 16px; height: 16px;
    background: url('../images/SwtPieChartDown.png');
    margin: auto 3px;
}
/* define graphOver icon */
.graphIconOver {
    width: 16px; height: 16px;
    background: url('../images/SwtPieChartOver.png');
    margin: auto 3px;
}
/* define pdfDisabled icon */
.pdfIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -46px -118px;
    margin: auto 3px;
}
/* define pdfDown icon */
.pdfIconDown {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -82px -118px;
    margin: auto 3px;
}
/* define pdfOver icon */
.pdfIconOver {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -118px -118px;
    margin: auto 3px;
}
/* define pdfUp icon */
.pdfIcon {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -156px -10px;
    margin: auto 3px;
    cursor:pointer !important;
}
/* define reportDisabled icon */
.reportIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -156px -46px;
    margin: auto 3px;
}
/* define reportDown icon */
.reportIconDown {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -156px -82px;
    margin: auto 3px;
}
/* define reportOver icon */
.reportIconOver {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -156px -118px;
    margin: auto 3px;
}
/* define reportUp icon */
.reportIcon {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -120px -46px;
    margin: auto 3px;
}
/* define fatca_report_disabled icon */
.fatca_report_disabled {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -82px -82px;
    margin: auto 3px;
}
/* define fatca_report_down icon */
.fatca_report_down {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -120px -10px;
    margin: auto 3px;
}
/* define fatca_report_over icon */
.fatca_report_over {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -10px -154px;
    margin: auto 3px;
}
/* define fatca_report_up icon */
.fatca_report_up {
    width: 16px; height: 16px;
    background: url('../spirites/report.png') -120px -82px;
    margin: auto 3px;
}
/* define acceptIcon icon */
.acceptIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -10px;
    margin: auto 3px;
}
/* define acceptIconDisabled icon */
.acceptIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -114px;
    margin: auto 3px;
}
/* define acceptIconDown icon */
.acceptIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -62px;
    margin: auto 3px;
}
/* define acceptIconOver icon */
.acceptIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -62px;
    margin: auto 3px;
}
/* define enforceIcon icon */
.enforceIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -10px;
    margin: auto 3px;
}
/* define enforceIconDisabled icon */
.enforceIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -62px;
    margin: auto 3px;
}
/* define enforceIconDown icon */
.enforceIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -114px;
    margin: auto 3px;
}
/* define enforceIconOver icon */
.enforceIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -114px;
    margin: auto 3px;
}
/* define manualIcon icon */
.manualIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -114px;
    margin: auto 3px;
}
/* define manualIconDisabled icon */
.manualIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -10px;
    margin: auto 3px;
}
/* define manualIconDown icon */
.manualIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -62px;
    margin: auto 3px;
}
/* define manualIconOver icon */
.manualIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -114px;
    margin: auto 3px;
}
/* define noteIcon icon */
.noteIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -166px;
    margin: auto 3px;
}
/* define noteIconDisabled icon */
.noteIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -166px;
    margin: auto 3px;
}
/* define noteIconDown icon */
.noteIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -166px;
    margin: auto 3px;
}
/* define noteIconOver icon */
.noteIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -166px;
    margin: auto 3px;
}
/* define rejectIcon icon */
.rejectIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -10px;
    margin: auto 3px;
}
/* define rejectIconDisabled icon */
.rejectIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -62px;
    margin: auto 3px;
}
/* define rejectIconDown icon */
.rejectIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -10px;
    margin: auto 3px;
}
/* define rejectIconOver icon */
.rejectIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -166px;
    margin: auto 3px;
}
/* define restoreIcon icon */
.restoreIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -218px;
    margin: auto 3px;
}
/* define restoreIconDisabled icon */
.restoreIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -218px;
    margin: auto 3px;
}
/* define restoreIconDown icon */
.restoreIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -218px;
    margin: auto 3px;
}
/* define restoreIconOver icon */
.restoreIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -218px;
    margin: auto 3px;
}
/* define sdnIcon icon */
.sdnIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -218px;
    margin: auto 3px;
}
/* define sdnIconDisabled icon */
.sdnIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -10px;
    margin: auto 3px;
}
/* define sdnIconDown icon */
.sdnIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -62px;
    margin: auto 3px;
}
/* define sdnIconOver icon */
.sdnIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -114px;
    margin: auto 3px;
}
/* define waitingIcon icon */
.waitingIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -166px;
    margin: auto 3px;
}
/* define waitingIconDisabled icon */
.waitingIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -218px;
    margin: auto 3px;
}
/* define waitingIconDown icon */
.waitingIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -10px -270px;
    margin: auto 3px;
}
/* define waitingIconOver icon */
.waitingIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -62px -270px;
    margin: auto 3px;
}
/* define weightIcon icon */
.weightIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -114px -270px;
    margin: auto 3px;
}
/* define weightIconDisabled icon */
.weightIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -166px -270px;
    margin: auto 3px;
}
/* define weightIconDown icon */
.weightIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -218px -270px;
    margin: auto 3px;
}
/* define weightIconOver icon */
.weightIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons1.png') -270px -270px;
    margin: auto 3px;
}
/* define clearIcon icon */
.clearIcon {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -166px -54px;
}
/* define clearIconDisabled icon */
.clearIconDisabled {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -210px -98px;
}
/* define clearIconDown icon */
.clearIconDown {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -166px -98px;
}
/* define clearIconOver icon */
.clearIconOver {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -10px -166px;
}
/* define downButtonEnableDisabled icon */
.arrowDownIconDisabled, .downButtonEnableDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -154px;
}
/* define downButtonEnable icon */
.arrowDownIcon, .downButtonEnable {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -118px;
}
/* define arrowDownIconOver icon */
.arrowDownIconOver, .downButtonEnableOver {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -118px;
}
/* define arrowDownIconDown icon */
.arrowDownIconDown, .downButtonEnableDown {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -118px;
}
/* define help icon */
.help {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -82px;
}
/* define helpDisabled icon */
.helpDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -46px;
}
/* define helpDown icon */
.helpDown {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -190px;
}
/* define helpOver icon */
.helpOver {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -254px -10px;
}
/* define historyIcon icon */
.historyIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -10px -10px;
}
/* define historyIconDisabled icon */
.historyIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -62px -10px;
}
/* define historyIconDown icon */
.historyIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -10px -62px;
}
/* define historyIconOver icon */
.historyIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -62px -62px;
}
/* define openFolderIcon icon */
.openFolderIcon {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -114px -10px;
}
/* define openFolderIconDisabled icon */
.openFolderIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -114px -62px;
}
/* define openFolderIconDown icon */
.openFolderIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -10px -114px;
}
/* define openFolderIconOver icon */
.openFolderIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/buttons2.png') -62px -114px;
}
/* define openIcon icon */
.openIcon {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -114px -114px;
}
/* define openIconDisabled icon */
.openIconDisabled {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -210px -142px;
}
/* define openIconDown icon */
.openIconDown {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -10px -210px;
}
/* define openIconOver icon */
.openIconOver {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -54px -210px;
}
/* define removeIcon icon */
.removeIcon {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -98px -210px;
}
/* define removeIconDisabled icon */
.removeIconDisabled {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -142px -210px;
}
/* define weightIconOver icon */
.removeIconDown {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -166px -10px;
}
/* define weightIconOver icon */
.removeIconOver {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -210px -54px;
}
/* define saveIcon icon */
.saveIcon {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -210px -10px;
}
/* define saveIconDisabled icon */
.saveIconDisabled {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -142px -166px;
}
/* define saveIconDown icon */
.saveIconDown {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -98px -166px;
}
/* define saveIconOver icon */
.saveIconOver {
    width: 24px; height: 24px;
    background: url('../spirites/buttons2.png') -54px -166px;
}
/* define upButtonEnable icon */
.arrowUpIcon, .upButtonEnable {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -186px -210px;
}
/* define arrowUpIconOver icon */
.arrowUpIconOver, .upButtonEnableOver {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -186px -210px;
}
/* define arrowUpIconDown icon */
.arrowUpIconDown, .upButtonEnableDown {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -186px -210px;
}
/* define upButtonEnableDisabled icon */
.arrowUpIconDisabled, .upButtonEnableDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -10px -254px;
}
/* define xmlIcon icon */
.xmlIcon {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -46px -254px;
}
/* define xmlIconDisabled icon */
.xmlIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -82px -254px;
}
/* define xmlIconDown icon */
.xmlIconDown {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -118px -254px;
}
/* define xmlIconOver icon */
.xmlIconOver {
    width: 16px; height: 16px;
    background: url('../spirites/buttons2.png') -154px -254px;
}

.arrowDown {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -10px -190px;
}

.arrowDownDisabled {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -49px -190px;
}

.arrowDownDown {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -88px -190px;
}

.arrowDownOver {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -127px -190px;
}

.arrowUp {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -166px -190px;
}

.arrowUpDisabled {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -205px -190px;
}

.arrowUpDown {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -250px -10px;
}

.arrowUpOver {
    width: 19px; height: 19px;
    background: url('../spirites/search.png') -250px -49px;
}

.ascIconDisabled {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -10px -10px;
}

.descIconDown {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -90px -10px;
}

.descIconOver {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -10px -55px;
}

.descIcon {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -90px -55px;
}

.ascIconDown {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -10px -100px;
}

.ascIconOver {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -90px -100px;
}

.ascIcon {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -170px -10px;
}

.descIconDisabled {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -170px -55px;
}

.navLeftIconDown {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -170px -100px;
}

.navLeftIcon {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -10px -145px;
}

.navLeftIconDisabled {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -90px -145px;
}

.navLeftIconOver {
    width: 60px; height: 25px;
    background: url('../spirites/search.png') -170px -145px;
}
.copyIcon {
    width: 48px; height: 48px;
    background: url('../spirites/buttons3.png') -10px -10px;
}

.copyIconDisabled {
    width: 48px; height: 48px;
    background: url('../spirites/buttons3.png') -78px -10px;
}

.copyIconDown {
    width: 48px; height: 48px;
    background: url('../spirites/buttons3.png') -10px -78px;
}

.copyIconOver {
    width: 48px; height: 48px;
    background: url('../spirites/buttons3.png') -78px -78px;
}

.groupUserIcon {
    width: 28px; height: 28px;
    background: url('../spirites/buttons3.png') -146px -10px;
}

.groupUserIconDisable {
    width: 28px; height: 28px;
    background: url('../spirites/buttons3.png') -146px -58px;
}

.groupUserIconDown {
    width: 28px; height: 28px;
    background: url('../spirites/buttons3.png') -10px -146px;
}

.groupUserIconOver {
    width: 28px; height: 28px;
    background: url('../spirites/buttons3.png') -58px -146px;
}

.printerIcon {
    width: 22px; height: 22px;
    background: url('../spirites/buttons3.png') -106px -146px;
}

.printerIconDisabled {
    width: 22px; height: 22px;
    background: url('../spirites/buttons3.png') -148px -146px;
}

.printerIconDown {
    width: 22px; height: 22px;
    background: url('../spirites/buttons3.png') -194px -10px;
}

.printerIconOver {
    width: 22px; height: 22px;
    background: url('../spirites/buttons3.png') -194px -52px;
}

.printIcon {
    width: 18px; height: 18px;
    background: url('../spirites/buttons3.png') -194px -94px;
}

.printIconDisabled {
    width: 18px; height: 18px;
    background: url('../spirites/buttons3.png') -194px -132px;
}

.printIconDown {
    width: 18px; height: 18px;
    background: url('../spirites/buttons3.png') -146px -106px;
}

.printIconOver {
    width: 18px; height: 18px;
    background: url('../spirites/buttons3.png') -10px -194px;
}
.swtbtn-disabled {
	outline: none!important;
    border-bottom:1px solid #52869a!important ;
    border-top:1px solid #90b6c4!important;
    border-left:1px solid #52869a!important;
    border-right:1px solid #52869a!important;  
     border-radius: 5px;
     font-size: 11px; 
     letter-spacing:0.2px;
    /*height: 23px;*/
     line-height: 21px;
    font-weight:bolder;
     color: #0B333C;
     width: fit-content;
     text-align: center;
     background-color: #E5F5FF;
    background-image: url('../images/predict_images/skyButtonUp.png') !important;
    background-repeat: repeat;
    /*background-image: -webkit-gradient(linear, left top, left bottom, from(#E5F5FF), to(#A7C6DE));
    background-image: -webkit-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -moz-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -ms-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -o-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: linear-gradient(to bottom, #E5F5FF, #A7C6DE);*/
     font-family: Verdana,helvetica !important;
     /* disable text selection */
     -moz-user-select: none;
	 -webkit-user-select: none;
	 -ms-user-select:none;
	  user-select:none;
	 -o-user-select:none;
    cursor: default!important;
    opacity: 0.3;
}
/* define button style */
 @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	 div.swtbtn:not(.swtbtn-disabled) { 
	 	display: table; 
}
	 
}

.swtbtn {
     border-bottom:1px solid #52869a!important ;
    border-top:1px solid #90b6c4!important;
    border-left:1px solid #52869a!important;
    border-right:1px solid #52869a!important;  
    border-radius: 5px;
    font-size: 11px; 
    letter-spacing:0.2px;
     height: 23px;
    line-height: 21px;
    font-weight:bolder; 
/*      margin : 0 0 5px 5px; */
    color: #0B333C;
    width: fit-content;
    text-align: center;
    background-color: #E5F5FF;
    background-image: url('../images/predict_images/skyButtonUp.png')!important;
    background-repeat: repeat;
    /*background-image: -webkit-gradient(linear, left top, left bottom, from(#E5F5FF), to(#A7C6DE));
    background-image: -webkit-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -moz-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -ms-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: -o-linear-gradient(top, #E5F5FF, #A7C6DE);
    background-image: linear-gradient(to bottom, #E5F5FF, #A7C6DE);*/
    font-family: Verdana,helvetica !important;
    /* disable text selection */
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select:none;
     user-select:none;
    -o-user-select:none;
    cursor: pointer!important;
     outline:none;
}
.minWidthBtn{
	min-width:70px;
}
/* define button hover style */
.swtbtn:hover {
	border: 1px solid #49B9FF !important;
    background-color: #E5F5FF; 
    /* height: 23px; */
    line-height: 21px;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#E5F5FF), to(#B0D8FF));
    background-image: -webkit-linear-gradient(top, #E5F5FF, #B0D8FF);
    background-image: -moz-linear-gradient(top, #E5F5FF, #B0D8FF);
    background-image: -ms-linear-gradient(top, #E5F5FF, #B0D8FF);
    background-image: -o-linear-gradient(top, #E5F5FF, #B0D8FF);
    background-image: linear-gradient(to bottom, #E5F5FF, #B0D8FF);
    outline: none; 
    cursor: default; 
}
/* define button focus style */
.swtbtn:focus {
	outline-color: #49B9FF;
}
/* define button disabled style */
.swtbtnDisabled {
	border-bottom:1px solid #52869a!important ;
     border-top:1px solid #90b6c4!important;
     border-left:1px solid #52869a!important;
     border-right:1px solid #52869a!important;  
     border-radius: 5px;
     font-size: 11px; 
     letter-spacing:0.2px;
     height: 23px;
     line-height: 21px;
     margin : auto 3px; 
     font-weight:bolder; 
     color: #0B333C;
     width: auto;
     padding: 0px 10px;
     background-color: #E5F5FF;
     background-image: -webkit-gradient(linear, left top, left bottom, from(#E5F5FF), to(#A7C6DE));
     background-image: -webkit-linear-gradient(top, #E5F5FF, #A7C6DE);
     background-image: -moz-linear-gradient(top, #E5F5FF, #A7C6DE);
     background-image: -ms-linear-gradient(top, #E5F5FF, #A7C6DE);
     background-image: -o-linear-gradient(top, #E5F5FF, #A7C6DE);
     background-image: linear-gradient(to bottom, #E5F5FF, #A7C6DE);
     font-family: Verdana,helvetica !important;
     /* disable text selection */
     -moz-user-select: none;
	 -webkit-user-select: none;
	 -ms-user-select:none;
	  user-select:none;
	 -o-user-select:none;
	 opacity: 0.3;
     cursor: default !important; 
}
/* basic style of panel component */
.panelTitle {
    color: #173553;
    font-size: 11px !important;
    font-family: Verdana,helvetica;
    height: 15px;
    line-height: 12px;
    padding-left :5px;
    background-image: url(../spirites/pane.png);
    background-color: #7f9db9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#7f9db9), to(#b7dbff)) !important;
    background-image: -webkit-linear-gradient(top,#7f9db9, #b7dbff) !important;
    background-image: -moz-linear-gradient(top, #7f9db9, #b7dbff) !important;
    background-image: -ms-linear-gradient(top, #7f9db9, #b7dbff) !important;
    background-image: -o-linear-gradient(top,#7f9db9, #b7dbff) !important;
    background-image: linear-gradient(to bottom,#7f9db9, #b7dbff) !important;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: 1px solid lightGray;
    outline: none;
 }
  
.panelBody {
   padding : 0 !important;
   width: 100%;
   height: 100%;
   border: 1px solid #FFF;
   border-bottom: none;
   border-top: none;
   border-bottom-left-radius: 5px;
   border-bottom-right-radius: 5px;
   outline: none;
}  
.panelInsideFormLayout{    
    height:100%;
    background-color:#d6e3fe;
    box-shadow:  0 2px 5px 0 rgba(0, 0, 0, 0.45), 0 0px 0px 0 rgba(0, 0, 0, 1);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
 } 
 
 /* edit_disabled.png */
.editIconDisabled
{
	background:url('../spirites/editableButtons.png') 0px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* edit_down.png */
.editIconDown
{
	background:url('../spirites/editableButtons.png') -32px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* edit_over.png */
.editIconOver
{
background:url('../spirites/editableButtons.png') -64px -0px;
display:inline-block;
width:32px;
height:32px;
}

/* edit_up.png */
.editIcon
{
	background:url('../spirites/editableButtons.png') -96px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtAddDisabled.png */
.addIconDisabled
{
	background:url('../spirites/editableButtons.png') -128px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtAddDown.png */
.addIconDown
{
	background:url('../spirites/editableButtons.png') -160px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtAddOver.png */
.addIconOver
{
	background:url('../spirites/editableButtons.png') -192px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtAddUp.png */
.addIcon
{
	background:url('../spirites/editableButtons.png') -224px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtEditDisabled.png */
.changeIconDisabled
{
	background:url('../spirites/editableButtons.png') -256px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtEditDown.png */
.changeIconDown
{
	background:url('../spirites/editableButtons.png') -288px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtEditOver.png */
.changeIconOver
{
	background:url('../spirites/editableButtons.png') -320px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtEditUp.png */
.changeIcon
{
	background:url('../spirites/editableButtons.png') -352px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtRemoveDisabled.png */
.deleteIconDisabled
{
	background:url('../spirites/editableButtons.png') -384px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtRemoveDown.png */
.deleteIconDown
{
	background:url('../spirites/editableButtons.png') -416px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtRemoveOver.png */
.deleteIconOver
{
	background:url('../spirites/editableButtons.png') -448px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}

/* SwtRemoveUp.png */
.deleteIcon
{
	background:url('../spirites/editableButtons.png') -480px -0px;
	display:inline-block;
	width:32px;
	height:32px;
}
/* define searchSdnIconDisabled icon */
.searchSdnIconDisabled {
    width: 32px; height: 32px;
    background: url('../spirites/searchsdn.png') -10px -10px;
}

/* define searchSdnIconDown icon */
.searchSdnIconDown {
    width: 32px; height: 32px;
    background: url('../spirites/searchsdn.png') -62px -10px;
}

/* define searchSdnIconOver icon */
.searchSdnIconOver {
    width: 32px; height: 32px;
    background: url('../spirites/searchsdn.png') -10px -62px;
}

/* define searchSdnIconUp icon */
.searchSdnIcon {
    width: 32px; height: 32px;
    background: url('../spirites/searchsdn.png') -62px -62px;
}
.disablePointerEvents{
	pointer-events: none!important;
        color: #7C7C7C !important;
}

.fileDeleteIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -46px -10px;
}
.fileDeleteIcon {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -10px -10px;
}

.fileDeleteIcon:hover
{
    -moz-box-shadow: 0 0 2px black;
    -webkit-box-shadow: 0 0 2px black;
    box-shadow: 0 0 2px black;
}

.fileSaveIcon {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -82px -10px;
}

.fileSaveIcon:hover
{
    -moz-box-shadow: 0 0 2px black;
    -webkit-box-shadow: 0 0 2px black;
    box-shadow: 0 0 2px black;
}

.fileSaveIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -82px -46px;
}
.fileRevertIcon {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -10px -46px;
}

.fileRevertIcon:hover
{
    -moz-box-shadow: 0 0 2px black;
    -webkit-box-shadow: 0 0 2px black;
    box-shadow: 0 0 2px black;
}


.fileRevertIconDisabled {
    width: 16px; height: 16px;
    background: url('../spirites/css_sprites.png') -46px -46px;
}
.minusIcon {
    width: 9px; height: 12px;
    background: url('../spirites/minus_plus.png') -10px -10px;
}

.plusIcon {
    width: 9px; height: 12px;
    background: url('../spirites/minus_plus.png') -39px -10px;
}
/**********ExportData Component*****************/
#exportDataComponent .SwtComboBox-container .swtcomboBox-dropDown
{
    margin-top: -70px;
    width: 100px !important;
}

#exportDataComponent .SwtComboBox-container .swtcomboBox-dropDown ul li
{
    padding-left: 0px !important;
}

#exportDataComponent .SwtComboBox-container .input-group input
{
    cursor: pointer !important;
    color: transparent !important;
    height: 19px !important;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: -moz-none;
    -o-user-select: none;
    user-select: none;
}

#exportDataComponent .SwtComboBox-container .input-group .input-group-addon{
    height: 19px !important;

}
