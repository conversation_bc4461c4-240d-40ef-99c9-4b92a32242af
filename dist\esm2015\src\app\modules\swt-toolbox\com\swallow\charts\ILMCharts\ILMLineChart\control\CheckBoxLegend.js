/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { CheckBoxLegendItem } from './CheckBoxLegendItem';
import { Series } from './Series';
import { VBox } from '../../../../controls/swt-vbox.component';
export class CheckBoxLegend extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._seriesList = [];
        this._dataProvider = [];
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
        //Add 'implements OnInit' to the class.
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        this._dataProvider = value;
        this.refreshLegends(value);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set seriesList(value) {
        this._seriesList = value;
    }
    /**
     * @return {?}
     */
    get seriesList() {
        return this._seriesList;
    }
    /**
     * @param {?} seriesList
     * @return {?}
     */
    refreshLegends(seriesList) {
        this.legendContainer.removeAllChildren();
        for (let index = 0; index < seriesList.length; index++) {
            /** @type {?} */
            const element = seriesList[index];
            /** @type {?} */
            var checkBoxLegendItem = (/** @type {?} */ (this.legendContainer.addChild(CheckBoxLegendItem)));
            checkBoxLegendItem.yField = element.yField;
            checkBoxLegendItem.seriesStyle = element.appliedStyle;
            checkBoxLegendItem.liveValue = element.legendDisplayName;
            checkBoxLegendItem.selected = element.visible;
            checkBoxLegendItem.toolTip = element.legendTooltip;
            checkBoxLegendItem.highlight = element.highlighted;
        }
    }
    /**
     * @param {?} series
     * @return {?}
     */
    getLegendItem(series) {
        /** @type {?} */
        var found = null;
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'CheckBoxLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                // if (item && (item.element == series)) {
                if (item && (item.yField === series.yField)) {
                    found = item;
                    break;
                }
            }
        }
        return found;
    }
    /**
     * @return {?}
     */
    getUncheckedLegends() {
        /** @type {?} */
        var uncheckedItems = [];
        for (var i = 0; i < this.legendContainer.getChildren().length; i++) {
            if (this.legendContainer.getChildAt(i).childType == 'CheckBoxLegendItem') {
                /** @type {?} */
                var item = ((/** @type {?} */ (this.legendContainer.getChildAt(i).component)));
                if (!item.selected) {
                    uncheckedItems.push(item.yField);
                }
            }
        }
        return uncheckedItems;
    }
    /**
     * @param {?} series
     * @param {?} isSelected
     * @return {?}
     */
    setCheckBoxLegendSelected(series, isSelected) {
        /** @type {?} */
        var legendItem = this.getLegendItem(series);
        if (legendItem) {
            legendItem.selected = isSelected;
        }
    }
    /**
     * To highlight the correct legend item
     *
     * @param {?} event
     * @return {?}
     */
    highlighTrueFalse(event) {
        /** @type {?} */
        let series = new Series;
        series.yField = event.yField;
        /** @type {?} */
        const legendItem = this.getLegendItem(series);
        if (legendItem) {
            legendItem.highlight = event.highligh;
        }
    }
}
CheckBoxLegend.decorators = [
    { type: Component, args: [{
                selector: 'CheckBoxLegend',
                template: `
        <VBox  #legendContainer width="100%"> 
        </VBox>
  `,
                styles: [`
      `]
            }] }
];
/** @nocollapse */
CheckBoxLegend.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
CheckBoxLegend.propDecorators = {
    legendContainer: [{ type: ViewChild, args: ['legendContainer',] }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    CheckBoxLegend.prototype.legendContainer;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype._seriesList;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    CheckBoxLegend.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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