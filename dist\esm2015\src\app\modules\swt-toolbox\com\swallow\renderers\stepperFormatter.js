/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export const stepperFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
(row, cell, value, columnDef, dataContext) => {
    /** @type {?} */
    let field = columnDef.field;
    /** @type {?} */
    let negative = false;
    /** @type {?} */
    let enabledFlag = columnDef.params.enableDisableCells(dataContext, columnDef.field);
    /** @type {?} */
    let showHideCells = columnDef.params.showHideCells(dataContext, columnDef.field);
    if (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent[field] != undefined) {
        negative = dataContext.slickgrid_rowcontent[field].negative;
    }
    if (showHideCells) {
        if (value == undefined || value == null) {
            return '';
        }
        else {
            return `<input ${enabledFlag ? '' : 'disabled'}     style="text-align: right; padding-right: 2px!important; width:50px;height: 98%; background-color: white!important ; ${!enabledFlag ? 'opacity: 0.8;' : ''} " type="number" min="0" max="2000" step="1" value ="${value}"/>
                    <span class="stepper-up-container  renderAsInput  ${enabledFlag ? '' : 'disabled-container'} "><span class="render-stepper-arrow-up"></span></span>
                    <span class="stepper-down-container renderAsInput  ${enabledFlag ? '' : 'disabled-container'}  "><span class="render-stepper-arrow-down"></span></span>
                   `;
        }
    }
    else {
        return '';
    }
});
//# sourceMappingURL=data:application/json;base64,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