import { HashMap } from "./HashMap.service";
import { Favourite, Module, Singletons } from "./swt-interfaces";
export declare class LoaderInfo {
    url: string;
    constructor();
}
export declare class Navigator {
    selectedIndex: number;
    selectedTab: Tab;
    tabs: any[];
    constructor();
    /**
     * This method used to remove tab in the given index.
     * @param index
     */
    removeChildAt(index: number): void;
    /**
     * This method used to add tab in the given index.
     * @param index
     * @param child
     */
    addChildAt(index: number, child: any): void;
    /**
     * This method used to add tab to the tabNavigator.
     * @param child
     */
    addChild(child: any): void;
    /**
     * This method used to remove a child from tabNvigator.
     * @param child
     */
    removeChild(child: any): void;
    /**
     * This method used to remove all children from tabNavigator.
     * @param index
     */
    removeAllChild(): void;
    getTabs(): Tab[];
    private setToActive;
}
export declare class parentApplication {
    static loaderInfo: LoaderInfo;
    static undock: any;
    static height: any;
    static screenName: any;
    static menuOrderGroup: any;
    static width: any;
    static moduleId: any;
    static programId: any;
    static menuAction: any;
    static labelGeneric: any;
    static menuAccess: any;
    static menuId: any;
    static navigator: Navigator;
    static screenClosed: string;
    static customParams: HashMap;
    static translateObject: any;
    static modules: Module[];
    static favorites: Favourite[];
    static singletons: Singletons;
    static currentModule: Module;
    static document: any;
    static selectedTab: string;
    /**
     * This method is used to get params from url.
     * @param url
     */
    static setParams(url: any): void;
    static getCurrentModuleId(): any;
    static clearParams(): void;
}
export declare class Tab {
    icon: string;
    itemid: number;
    menuaccess: number;
    width: number;
    undock: string;
    label: string;
    menuaction: string;
    screenname: string;
    moduleid: string;
    programid: number;
    menuOrderGroup: number;
    height: number;
    type: string;
    menuitem: any[];
    active: boolean;
    disabled: boolean;
    tabIndex: number;
    constructor();
}
