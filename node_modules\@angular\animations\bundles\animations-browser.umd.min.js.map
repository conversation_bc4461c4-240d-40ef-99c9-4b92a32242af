{"version": 3, "sources": ["packages/animations/animations-browser.umd.js"], "names": ["global", "factory", "exports", "module", "require", "define", "amd", "self", "ng", "animations", "browser", "core", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "constructor", "prototype", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "call", "apply", "__read", "o", "m", "Symbol", "iterator", "r", "e", "ar", "next", "done", "push", "value", "error", "__spread", "concat", "isNode", "process", "optimizeGroupPlayer", "players", "NoopAnimationPlayer", "ɵAnimationGroupPlayer", "normalizeKeyframes", "driver", "normalizer", "element", "keyframes", "preStyles", "postStyles", "errors", "normalizedKeyframes", "previousOffset", "previousKeyframe", "for<PERSON>ach", "kf", "offset", "isSameOffset", "normalizedKeyframe", "keys", "prop", "normalizedProp", "normalizedValue", "normalizePropertyName", "ɵPRE_STYLE", "AUTO_STYLE", "normalizeStyleValue", "Error", "join", "listenOnPlayer", "player", "eventName", "event", "callback", "onStart", "copyAnimationEvent", "onDone", "onDestroy", "phaseName", "totalTime", "makeAnimationEvent", "triggerName", "fromState", "toState", "undefined", "disabled", "data", "getOrSetAsInMap", "map", "key", "defaultValue", "Map", "get", "set", "parseTimelineCommand", "command", "separatorPos", "indexOf", "substring", "substr", "_contains", "elm1", "elm2", "_matches", "selector", "_query", "multi", "_isNode", "Element", "contains", "matches", "proto", "fn_1", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "results", "querySelectorAll", "elm", "querySelector", "_CACHED_BODY", "_IS_WEBKIT", "validateStyleProperty", "getBodyNode", "document", "body", "style", "result", "containsVendorPrefix", "char<PERSON>t", "toUpperCase", "matchesElement", "containsElement", "invoke<PERSON><PERSON>y", "hypenatePropsObject", "object", "newObj", "newProp", "replace", "NoopAnimationDriver", "query", "computeStyle", "animate", "duration", "delay", "easing", "previousPlayers", "scrubberAccessRequested", "__decorate", "decorators", "target", "desc", "c", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "Injectable", "AnimationDriver", "NOOP", "ONE_SECOND", "resolveTimingValue", "match", "_convertTimeValueToMS", "parseFloat", "unit", "resolveTiming", "timings", "allowNegativeValues", "parseTimeExpression", "exp", "delayMatch", "easingVal", "containsErrors", "startIndex", "splice", "copyObj", "obj", "destination", "normalizeStyles", "styles", "normalizedStyles", "isArray", "copyStyles", "readPrototype", "getStyleAttributeString", "writeStyleAttribute", "styleAttrValue", "item", "getPropertyValue", "startsWith", "toLowerCase", "setAttribute", "setStyles", "formerStyles", "camelProp", "dashCaseToCamelCase", "eraseStyles", "normalizeAnimationEntry", "steps", "sequence", "PARAM_REGEX", "RegExp", "SUBSTITUTION_EXPR_START", "extractStyleParams", "params", "val", "toString", "exec", "lastIndex", "interpolateParams", "original", "str", "_", "varName", "localVal", "iteratorToArray", "arr", "DASH_CASE_REGEXP", "input", "_i", "allowPreviousPlayerStylesMerge", "balancePreviousStylesIntoKeyframes", "previousStyles", "previousStyleProps", "startingKeyframe_1", "missingStyleProps_1", "_loop_1", "visitDslNode", "visitor", "node", "context", "type", "visitTrigger", "visitState", "visitTransition", "visitSequence", "visitGroup", "visitAnimate", "visitKeyframes", "visitStyle", "visitReference", "visitAnimateChild", "visitAnimateRef", "visit<PERSON><PERSON><PERSON>", "visitStagger", "window", "getComputedStyle", "ANY_STATE", "TRUE_BOOLEAN_VALUES", "Set", "FALSE_BOOLEAN_VALUES", "makeLambdaFromStates", "lhs", "rhs", "LHS_MATCH_BOOLEAN", "has", "RHS_MATCH_BOOLEAN", "lhsMatch", "rhsMatch", "SELF_TOKEN_REGEX", "buildAnimationAst", "metadata", "AnimationAstBuilderVisitor", "build", "_driver", "AnimationAstBuilderContext", "_resetContextStyleTimingState", "currentQuerySelector", "collectedStyles", "currentTime", "_this", "queryCount", "depCount", "states", "transitions", "name", "definitions", "def", "stateDef_1", "name_1", "split", "transition", "options", "styleAst", "astParams", "containsDynamicStyles", "missingSubs_1", "params_1", "isObject", "stylesObj_1", "sub", "add", "size", "missingSubsArr", "values", "animation", "matchers", "parseTransitionExpr", "transitionValue", "expressions", "parseInnerTransitionStr", "eventStr", "parseAnimationAlias", "alias", "separator", "expr", "normalizeAnimationOptions", "furthestTime", "step", "innerAst", "Math", "max", "timingAst", "constructTimingAst", "makeTimingAst", "strValue", "some", "v", "ast", "dynamic", "currentAnimateTimings", "styleMetadata", "styleMetadata_1", "isEmpty", "newStyleData", "_styleAst", "isEmptyStep", "_makeStyleAst", "_validateStyleAst", "styleTuple", "collectedEasing", "styleData", "styleMap", "endTime", "startTime", "tuple", "collectedEntry", "updateCollectedStyle", "validateStyleParams", "totalKeyframesWithOffsets", "offsets", "offsetsOutOfOrder", "keyframesOutOfRange", "offsetVal", "consumeOffset", "generatedOffset", "limit", "animateDuration", "durationUpToThisFrame", "parentSelector", "<PERSON><PERSON><PERSON><PERSON>", "_a", "normalizeSelector", "hasAmpersand", "find", "token", "NG_TRIGGER_SELECTOR", "includeSelf", "optional", "originalSelector", "currentTransition", "normalizeParams", "createTimelineInstruction", "preStyleProps", "postStyleProps", "subTimeline", "ElementInstructionMap", "_map", "consume", "instructions", "delete", "append", "existingInstructions", "clear", "ENTER_TOKEN_REGEX", "LEAVE_TOKEN_REGEX", "buildAnimationTimelines", "rootElement", "enterClassName", "leaveClassName", "startingStyles", "finalStyles", "subInstructions", "AnimationTimelineBuilderVisitor", "buildKeyframes", "AnimationTimelineContext", "currentTimeline", "timelines", "filter", "timeline", "containsAnimation", "tl", "allowOnlyTimelineStyles", "elementInstructions", "innerContext", "createSubContext", "_visitSubInstructions", "transformIntoNewTimeline", "previousNode", "instruction", "instructionTimings", "appendInstructionToTimeline", "updateOptions", "subContextCount", "ctx", "snapshotCurrentStyles", "DEFAULT_NOOP_PREVIOUS_NODE", "delayNextStep", "applyStylesToKeyframe", "innerTimelines", "mergeTimelineCollectedStyles", "_visitTiming", "incrementTime", "getCurrentStyleProperties", "<PERSON><PERSON><PERSON><PERSON>", "applyEmptyStep", "innerTimeline", "forwardTime", "elms", "currentQueryTotal", "sameElementTimeline", "currentQueryIndex", "parentContext", "abs", "maxTime", "currentStaggerTime", "startingTime", "_enterClassName", "_leaveClassName", "initialTimeline", "TimelineBuilder", "enumerable", "configurable", "skipIfExists", "newOptions", "optionsToUpdate", "newParams", "paramsToUpdate_1", "_copyOptions", "oldParams_1", "newTime", "fork", "updatedTimings", "builder", "SubTimelineBuilder", "stretchStartingKeyframe", "time", "elements", "slice", "_elementTimelineStylesLookup", "_previousKeyframe", "_currentKeyframe", "_keyframes", "_styleSummary", "_pendingStyles", "_backFill", "_currentEmptyStepKeyframe", "_localTimelineStyles", "_globalTimelineStyles", "_loadKeyframe", "hasPreStyleStep", "_updateStyle", "flattenStyles", "allStyles", "allProperties", "props", "getFinalKeyframe", "properties", "details0", "details1", "finalKeyframes", "keyframe", "finalKeyframe", "preProps", "postProps", "kf0", "kf1", "_super", "_stretchStartingKeyframe", "newKeyframes", "startingGap", "newFirstKeyframe", "oldFirstKeyframe", "roundOffset", "decimalPoints", "mult", "pow", "round", "Animation", "errorMessage", "_animationAst", "buildTimelines", "destinationStyles", "start", "dest", "AnimationStyleNormalizer", "NoopAnimationStyleNormalizer", "propertyName", "userProvidedProperty", "normalizedProperty", "WebAnimationsStyleNormalizer", "strVal", "trim", "DIMENSIONAL_PROP_MAP", "valAndSuffixMatch", "makeBooleanMap", "createTransitionInstruction", "isRemovalTransition", "fromStyles", "to<PERSON><PERSON>les", "queriedElements", "EMPTY_OBJECT", "AnimationTransitionFactory", "_triggerName", "_stateStyles", "currentState", "nextState", "oneOrMoreTransitionsMatch", "matchFns", "fn", "buildStyles", "stateName", "backupStateStyler", "stateStyler", "backup<PERSON><PERSON><PERSON>", "currentOptions", "nextOptions", "skipAstBuild", "transitionAnimationParams", "currentStateStyles", "nextAnimationParams", "nextStateStyles", "preStyleMap", "postStyleMap", "isRemoval", "animationOptions", "queriedElementsList", "AnimationStateStyles", "defaultParams", "combinedParams", "styleObj_1", "AnimationTrigger", "transitionFactories", "balanceProperties", "fallbackTransition", "createFallbackTransition", "matchTransition", "f", "matchStyles", "key1", "key2", "EMPTY_INSTRUCTION_MAP", "TimelineAnimationEngine", "bodyNode", "_normalizer", "_animations", "_playersById", "register", "id", "_buildPlayer", "autoStylesMap", "inst", "destroy", "_getPlayer", "index", "listen", "baseEvent", "args", "play", "pause", "reset", "restart", "finish", "init", "setPosition", "EMPTY_PLAYER_ARRAY", "NULL_REMOVAL_STATE", "namespaceId", "setForRemoval", "setForMove", "hasAnimation", "removed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NULL_REMOVED_QUERIED_STATE", "REMOVAL_FLAG", "StateValue", "isObj", "normalizeTriggerValue", "absorbOptions", "DEFAULT_STATE_VALUE", "AnimationTransitionNamespace", "hostElement", "_engine", "_triggers", "_queue", "_elementListeners", "_hostClassName", "addClass", "phase", "isTriggerEventValid", "listeners", "triggersWithStates", "statesByElement", "NG_TRIGGER_CLASSNAME", "afterFlush", "_getTrigger", "trigger", "defaultToFallback", "TransitionAnimationPlayer", "playersOnElement", "players<PERSON>y<PERSON><PERSON>", "queued", "isFallbackTransition", "totalQueuedPlayers", "removeClass", "index_1", "obj<PERSON><PERSON><PERSON>", "a", "k1", "k2", "fromStyles_1", "toStyles_1", "reportError", "deregister", "stateMap", "entry", "clearElementCache", "elementPlayers", "_signalRemovalForInnerTriggers", "namespaces", "fetchNamespacesByElement", "ns", "triggerLeaveAnimation", "destroyAfterComplete", "triggerStates", "players_1", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>oved", "processLeaveNode", "prepareLeaveAnimationListeners", "visitedTriggers_1", "listener", "removeNode", "engine", "childElementCount", "containsPotentialParentTransition", "totalAnimations", "currentPlayers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent_1", "parentNode", "destroyInnerAnimations", "_onRemovalComplete", "insertNode", "parent", "drainQueuedTransitions", "microtaskId", "destroyed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "d0", "d1", "elementContainsData", "containsData", "TransitionAnimationEngine", "newHostElements", "disabledNodes", "_namespaceLookup", "_namespaceList", "_flushFns", "_whenQuietFns", "namespacesByHostElement", "collectedEnterElements", "collectedLeaveElements", "onRemovalComplete", "createNamespace", "_balanceNamespaceList", "collectEnterElement", "found", "registerTrigger", "_fetchNamespace", "afterFlushAnimationsDone", "elementStates", "nsId", "isElementNode", "insertBefore", "details", "markElementAsDisabled", "_buildInstruction", "subTimelines", "skipBuildAst", "containerElement", "destroyActiveAnimationsForElement", "finishActiveQueriedAnimationOnElement", "whenRenderingDone", "Promise", "resolve", "flush", "cleanupFns", "_flushAnimations", "quietFns_1", "skippedPlayers", "skippedPlayersMap", "queuedInstructions", "allPreStyleElements", "allPostStyleElements", "disabledElementsSet", "nodesThatAreDisabled", "i_1", "allTriggerElements", "from", "enterNodeMap", "buildRootMap", "enterNodeMapIds", "nodes", "root", "className", "allLeaveNodes", "mergedLeaveNodes", "leaveNodesWithoutAnimations", "i_2", "leaveNodeMapIds", "leaveNodeMap", "allPlayers", "erroneousTransitions", "i_3", "nodeIsOrphaned", "stringMap", "setVal_1", "setVal", "errors_1", "allPreviousPlayersMap", "animationElementMap", "_beforeAnimationBuild", "_getPreviousPlayers", "prevPlayer", "replaceNodes", "replacePostStylesAsPre", "postStylesMap", "cloakAndComputeStyles", "preStylesMap", "post", "pre", "rootPlayers", "subPlayers", "NO_PARENT_ANIMATION_ELEMENT_DETECTED", "overrideTotalTime", "parentWithAnimation_1", "parentsToAdd", "detectedParent", "innerPlayer", "_buildAnimation", "setRealPlayer", "parentPlayers", "parentPlayer", "playersFor<PERSON>lement", "syncPlayerEvents", "i_4", "queriedPlayerResults", "queriedInnerElements", "j", "queriedPlayers", "activePlayers", "removeNodesAfterAnimationDone", "isQueriedElement", "toStateValue", "queriedElementPlayers", "isRemovalAnimation_1", "e_1", "targetNameSpaceId", "targetTriggerName", "timelineInstruction", "this_1", "realPlayer", "getRealPlayer", "<PERSON><PERSON><PERSON><PERSON>", "_b", "__values", "_c", "e_1_1", "return", "allQueriedPlayers", "allConsumedElements", "allSubElements", "allNewPlayers", "flattenGroupPlayers", "finalPlayers", "_flattenGroupPlayersRecur", "wrappedPlayer", "deleteOrUnsetInMap", "currentV<PERSON>ues", "_player", "_containsRealPlayer", "_queuedCallbacks", "triggerCallback", "_queueEvent", "hasStarted", "getPosition", "cloakElement", "oldValue", "display", "valuesMap", "elementPropsMap", "defaultStyle", "cloakVals", "failedElements", "roots", "rootMap", "nodeSet", "localRootMap", "getRoot", "CLASSES_CACHE_KEY", "classList", "classes", "remove", "postEntry", "preEntry", "AnimationEngine", "_triggerCache", "_transitionEngine", "_timelineEngine", "componentId", "cache<PERSON>ey", "buildTrigger", "onInsert", "onRemove", "disableAnimations", "disable", "property", "eventPhase", "packageNonAnimatableStyles", "startStyles", "endStyles", "filterNonAnimatableStyles", "SpecialCasedStyles", "_element", "_startStyles", "_endStyles", "_state", "initialStyles", "initialStylesByElement", "_initialStyles", "WeakMap", "isNonAnimatableStyle", "ANIMATION_PROP", "ANIMATIONEND_EVENT", "ElementAnimationStyleHandler", "_name", "_duration", "_delay", "_easing", "_fillMode", "_onDoneFn", "_finished", "_destroyed", "_startTime", "_position", "_eventFn", "_handleCallback", "applyKeyframeAnimation", "anim", "getAnimationStyle", "count<PERSON><PERSON><PERSON>", "char", "count", "setAnimationStyle", "addRemoveAnimationEvent", "Date", "now", "playPauseAnimation", "resume", "position", "findIndexForAnimation", "timestamp", "_ngTestManualTimestamp", "elapsedTime", "toFixed", "animationName", "removeKeyframeAnimation", "tokens", "findMatchingTokenIndex", "status", "searchToken", "doRemove", "removeEventListener", "addEventListener", "DEFAULT_EASING", "CssKeyframesPlayer", "_finalStyles", "_specialStyles", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_started", "currentSnapshot", "_buildStyler", "_styler", "_flushStartFns", "_flushDoneFns", "methods", "finished_1", "DirectStylePlayer", "_startingStyles", "__initialized", "_styles", "setProperty", "removeProperty", "CssKeyframesDriver", "_count", "_head", "_warningIssued", "buildKeyframeElement", "keyframeStr", "tab", "kfElm", "createElement", "innerHTML", "_notifyFaultyScrubber", "previousCssKeyframePlayers", "flattenKeyframesIntoStyles", "flatKeyframes", "append<PERSON><PERSON><PERSON>", "specialStyles", "removeElement", "<PERSON><PERSON><PERSON><PERSON>", "console", "warn", "WebAnimationsPlayer", "_initialized", "_onFinish", "_preparePlayerBeforeStart", "domPlayer", "_triggerWebAnimation", "_finalKeyframe", "_resetDomPlayerState", "cancel", "WebAnimationsDriver", "_isNativeImpl", "test", "getElementAnimateFn", "_cssKeyframesDriver", "overrideWebAnimationsSupport", "supported", "playerOptions", "fill", "previousWebAnimationPlayers", "<PERSON><PERSON><PERSON><PERSON>", "ɵangular_packages_animations_browser_browser_a", "ɵAnimationDriver", "ɵAnimation", "ɵAnimationStyleNormalizer", "ɵNoopAnimationStyleNormalizer", "ɵWebAnimationsStyleNormalizer", "ɵNoopAnimationDriver", "ɵAnimationEngine", "ɵCssKeyframesDriver", "ɵCssKeyframesPlayer", "ɵcontainsElement", "ɵinvokeQuery", "ɵmatchesElement", "ɵvalidateStyleProperty", "ɵWebAnimationsDriver", "ɵsupportsWebAnimations", "supportsWebAnimations", "ɵWebAnimationsPlayer", "ɵallowPreviousPlayerStylesMerge"], "mappings": ";;;;;CAMC,SAAUA,EAAQC,GACI,iBAAZC,SAA0C,oBAAXC,OAAyBF,EAAQC,QAASE,QAAQ,uBAAwBA,QAAQ,kBACtG,mBAAXC,QAAyBA,OAAOC,IAAMD,OAAO,+BAAgC,UAAW,sBAAuB,iBAAkBJ,GAC9GA,IAAzBD,EAASA,GAAUO,MAAsBC,GAAKR,EAAOQ,OAAUR,EAAOQ,GAAGC,WAAaT,EAAOQ,GAAGC,eAAkBT,EAAOQ,GAAGC,WAAWC,YAAeV,EAAOQ,GAAGC,WAAYT,EAAOQ,GAAGG,MAH3L,CAIEC,KAAM,SAAUV,EAASO,EAAYE,GAAQ,aAkB3C,IAAIE,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,iBAChBC,wBAA2BC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOA,EAAEM,eAAeD,KAAIN,EAAEM,GAAKL,EAAEK,MACpDN,EAAGC,IAG5B,SAASO,EAAUR,EAAGC,GAElB,SAASQ,IAAOX,KAAKY,YAAcV,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEW,UAAkB,OAANV,EAAaC,OAAOU,OAAOX,IAAMQ,EAAGE,UAAYV,EAAEU,UAAW,IAAIF,GAGnF,IAAII,EAAW,WAQX,OAPAA,EAAWX,OAAOY,QAAU,SAASD,EAASE,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAIX,KADTU,EAAIG,UAAUF,GACOf,OAAOS,UAAUJ,eAAec,KAAKL,EAAGV,KAAIS,EAAET,GAAKU,EAAEV,IAE9E,OAAOS,IAEKO,MAAMxB,KAAMqB,YAqBhC,SAASI,EAAOC,EAAGN,GACf,IAAIO,EAAsB,mBAAXC,QAAyBF,EAAEE,OAAOC,UACjD,IAAKF,EAAG,OAAOD,EACf,IAAmBI,EAAYC,EAA3BZ,EAAIQ,EAAEJ,KAAKG,GAAOM,KACtB,IACI,WAAc,IAANZ,GAAgBA,KAAM,MAAQU,EAAIX,EAAEc,QAAQC,MAAMF,EAAGG,KAAKL,EAAEM,OAExE,MAAOC,GAASN,GAAMM,MAAOA,GAC7B,QACI,IACQP,IAAMA,EAAEI,OAASP,EAAIR,EAAU,SAAIQ,EAAEJ,KAAKJ,GAElD,QAAU,GAAIY,EAAG,MAAMA,EAAEM,OAE7B,OAAOL,EAGX,SAASM,IACL,IAAK,IAAIN,KAASb,EAAI,EAAGA,EAAIE,UAAUC,OAAQH,IAC3Ca,EAAKA,EAAGO,OAAOd,EAAOJ,UAAUF,KACpC,OAAOa,EAMX,SAASQ,IACL,MAA2B,oBAAZC,QAEnB,SAASC,EAAoBC,GACzB,OAAQA,EAAQrB,QACZ,KAAK,EACD,OAAO,IAAIzB,EAAW+C,oBAC1B,KAAK,EACD,OAAOD,EAAQ,GACnB,QACI,OAAO,IAAI9C,EAAWgD,sBAAsBF,IAGxD,SAASG,EAAmBC,EAAQC,EAAYC,EAASC,EAAWC,EAAWC,QACzD,IAAdD,IAAwBA,WACT,IAAfC,IAAyBA,MAC7B,IAAIC,KACAC,KACAC,GAAkB,EAClBC,EAAmB,KA+BvB,GA9BAN,EAAUO,QAAQ,SAAUC,GACxB,IAAIC,EAASD,EAAW,OACpBE,EAAeD,GAAUJ,EACzBM,EAAsBD,GAAgBJ,MAC1CpD,OAAO0D,KAAKJ,GAAID,QAAQ,SAAUM,GAC9B,IAAIC,EAAiBD,EACjBE,EAAkBP,EAAGK,GACzB,GAAa,WAATA,EAEA,OADAC,EAAiBhB,EAAWkB,sBAAsBF,EAAgBX,GAC1DY,GACJ,KAAKpE,EAAWsE,WACZF,EAAkBd,EAAUY,GAC5B,MACJ,KAAKlE,EAAWuE,WACZH,EAAkBb,EAAWW,GAC7B,MACJ,QACIE,EACIjB,EAAWqB,oBAAoBN,EAAMC,EAAgBC,EAAiBZ,GAItFQ,EAAmBG,GAAkBC,IAEpCL,GACDN,EAAoBnB,KAAK0B,GAE7BL,EAAmBK,EACnBN,EAAiBI,IAEjBN,EAAO/B,OAEP,MAAM,IAAIgD,MAAM,sDAAgEjB,EAAOkB,KADtE,UAGrB,OAAOjB,EAEX,SAASkB,EAAeC,EAAQC,EAAWC,EAAOC,GAC9C,OAAQF,GACJ,IAAK,QACDD,EAAOI,QAAQ,WAAc,OAAOD,EAASD,GAASG,EAAmBH,EAAO,QAASF,MACzF,MACJ,IAAK,OACDA,EAAOM,OAAO,WAAc,OAAOH,EAASD,GAASG,EAAmBH,EAAO,OAAQF,MACvF,MACJ,IAAK,UACDA,EAAOO,UAAU,WAAc,OAAOJ,EAASD,GAASG,EAAmBH,EAAO,UAAWF,OAIzG,SAASK,EAAmB/C,EAAGkD,EAAWR,GACtC,IAAIS,EAAYT,EAAOS,UAEnBP,EAAQQ,EAAmBpD,EAAEkB,QAASlB,EAAEqD,YAAarD,EAAEsD,UAAWtD,EAAEuD,QAASL,GAAalD,EAAEkD,eAAwBM,GAAbL,EAAyBnD,EAAEmD,UAAYA,IADnIT,EAAOe,UAElBC,EAAO1D,EAAS,MAIpB,OAHY,MAAR0D,IACAd,EAAa,MAAIc,GAEdd,EAEX,SAASQ,EAAmBlC,EAASmC,EAAaC,EAAWC,EAASL,EAAWC,EAAWM,GAGxF,YAFkB,IAAdP,IAAwBA,EAAY,SACtB,IAAdC,IAAwBA,EAAY,IAC/BjC,QAASA,EAASmC,YAAaA,EAAaC,UAAWA,EAAWC,QAASA,EAASL,UAAWA,EAAWC,UAAWA,EAAWM,WAAYA,GAEzJ,SAASE,EAAgBC,EAAKC,EAAKC,GAC/B,IAAIzD,EAaJ,OAZIuD,aAAeG,KACf1D,EAAQuD,EAAII,IAAIH,KAEZD,EAAIK,IAAIJ,EAAKxD,EAAQyD,IAIzBzD,EAAQuD,EAAIC,MAERxD,EAAQuD,EAAIC,GAAOC,GAGpBzD,EAEX,SAAS6D,EAAqBC,GAC1B,IAAIC,EAAeD,EAAQE,QAAQ,KAGnC,OAFSF,EAAQG,UAAU,EAAGF,GACjBD,EAAQI,OAAOH,EAAe,IAG/C,IAAII,EAAY,SAAUC,EAAMC,GAAQ,OAAO,GAC3CC,EAAW,SAAUzD,EAAS0D,GAC9B,OAAO,GAEPC,EAAS,SAAU3D,EAAS0D,EAAUE,GACtC,UAIAC,EAAUtE,IACd,GAAIsE,GAA8B,oBAAZC,QAAyB,CAG3C,GADAR,EAAY,SAAUC,EAAMC,GAAQ,OAAOD,EAAKQ,SAASP,IACrDK,GAAWC,QAAQlG,UAAUoG,QAC7BP,EAAW,SAAUzD,EAAS0D,GAAY,OAAO1D,EAAQgE,QAAQN,QAEhE,CACD,IAAIO,EAAQH,QAAQlG,UAChBsG,EAAOD,EAAME,iBAAmBF,EAAMG,oBAAsBH,EAAMI,mBAClEJ,EAAMK,kBAAoBL,EAAMM,sBAChCL,IACAT,EAAW,SAAUzD,EAAS0D,GAAY,OAAOQ,EAAK3F,MAAMyB,GAAU0D,MAG9EC,EAAS,SAAU3D,EAAS0D,EAAUE,GAClC,IAAIY,KACJ,GAAIZ,EACAY,EAAQtF,KAAKX,MAAMiG,EAASnF,EAASW,EAAQyE,iBAAiBf,SAE7D,CACD,IAAIgB,EAAM1E,EAAQ2E,cAAcjB,GAC5BgB,GACAF,EAAQtF,KAAKwF,GAGrB,OAAOF,GAQf,IAAII,EAAe,KACfC,GAAa,EACjB,SAASC,EAAsBhE,GACtB8D,IACDA,EAaR,SAASG,IACL,MAAuB,oBAAZC,SACAA,SAASC,KAEb,KAjBYF,OACfF,IAAaD,EAAaM,OAAS,qBAAsBN,EAAaM,OAE1E,IAAIC,GAAS,EAQb,OAPIP,EAAaM,QAbrB,SAASE,EAAqBtE,GAG1B,MAA+B,SAAxBA,EAAKsC,UAAU,EAAG,GAUEgC,CAAqBtE,MAC5CqE,EAASrE,KAAQ8D,EAAaM,QACfL,IAEXM,EADgB,SAAWrE,EAAKuE,OAAO,GAAGC,cAAgBxE,EAAKuC,OAAO,KAChDuB,EAAaM,OAGpCC,EAQX,IAAII,EAAiB9B,EACjB+B,EAAkBlC,EAClBmC,EAAc9B,EAClB,SAAS+B,EAAoBC,GACzB,IAAIC,KAKJ,OAJAzI,OAAO0D,KAAK8E,GAAQnF,QAAQ,SAAUM,GAClC,IAAI+E,EAAU/E,EAAKgF,QAAQ,kBAAmB,SAC9CF,EAAOC,GAAWF,EAAO7E,KAEtB8E,EAMX,IAAIG,EAAqC,WACrC,SAASA,KAoBT,OAlBAA,EAAoBnI,UAAUkH,sBAAwB,SAAUhE,GAAQ,OAAOgE,EAAsBhE,IACrGiF,EAAoBnI,UAAU2H,eAAiB,SAAUvF,EAAS0D,GAC9D,OAAO6B,EAAevF,EAAS0D,IAEnCqC,EAAoBnI,UAAU4H,gBAAkB,SAAUjC,EAAMC,GAAQ,OAAOgC,EAAgBjC,EAAMC,IACrGuC,EAAoBnI,UAAUoI,MAAQ,SAAUhG,EAAS0D,EAAUE,GAC/D,OAAO6B,EAAYzF,EAAS0D,EAAUE,IAE1CmC,EAAoBnI,UAAUqI,aAAe,SAAUjG,EAASc,EAAM8B,GAClE,OAAOA,GAAgB,IAE3BmD,EAAoBnI,UAAUsI,QAAU,SAAUlG,EAASC,EAAWkG,EAAUC,EAAOC,EAAQC,EAAiBC,GAE5G,YADwB,IAApBD,IAA8BA,MAC3B,IAAI1J,EAAW+C,oBAAoBwG,EAAUC,IAvP5D,SAASI,EAAWC,EAAYC,EAAQ/D,EAAKgE,GACzC,IAA2H1J,EAAvH2J,EAAIxI,UAAUC,OAAQQ,EAAI+H,EAAI,EAAIF,EAAkB,OAATC,EAAgBA,EAAOxJ,OAAO0J,yBAAyBH,EAAQ/D,GAAOgE,EACrH,GAAuB,iBAAZG,SAAoD,mBAArBA,QAAQC,SAAyBlI,EAAIiI,QAAQC,SAASN,EAAYC,EAAQ/D,EAAKgE,QACpH,IAAK,IAAIzI,EAAIuI,EAAWpI,OAAS,EAAGH,GAAK,EAAGA,KAASjB,EAAIwJ,EAAWvI,MAAIW,GAAK+H,EAAI,EAAI3J,EAAE4B,GAAK+H,EAAI,EAAI3J,EAAEyJ,EAAQ/D,EAAK9D,GAAK5B,EAAEyJ,EAAQ/D,KAAS9D,GAChJ,OAAO+H,EAAI,GAAK/H,GAAK1B,OAAO6J,eAAeN,EAAQ/D,EAAK9D,GAAIA,EAqPtC2H,EAClB1J,EAAKmK,cACNlB,GApBiC,GA0BpCmB,EAAiC,WACjC,SAASA,KAGT,OADAA,EAAgBC,KAAO,IAAIpB,EACpBmB,EAJyB,GAchCE,EAAa,IASjB,SAASC,EAAmBlI,GACxB,GAAoB,iBAATA,EACP,OAAOA,EACX,IAAI6E,EAAU7E,EAAMmI,MAAM,qBAC1B,OAAKtD,GAAWA,EAAQ3F,OAAS,EACtB,EACJkJ,EAAsBC,WAAWxD,EAAQ,IAAKA,EAAQ,IAEjE,SAASuD,EAAsBpI,EAAOsI,GAClC,OAAQA,GACJ,IAAK,IACD,OAAOtI,EAAQiI,EACnB,QACI,OAAOjI,GAGnB,SAASuI,EAAcC,EAASvH,EAAQwH,GACpC,OAAOD,EAAQnK,eAAe,YAC1BmK,EAGR,SAASE,EAAoBC,EAAK1H,EAAQwH,GACtC,IACIzB,EACAC,EAAQ,EACRC,EAAS,GACb,GAAmB,iBAARyB,EAAkB,CACzB,IAAI9D,EAAU8D,EAAIR,MALV,4EAMR,GAAgB,OAAZtD,EAEA,OADA5D,EAAOlB,KAAK,8BAAiC4I,EAAM,kBAC1C3B,SAAU,EAAGC,MAAO,EAAGC,OAAQ,IAE5CF,EAAWoB,EAAsBC,WAAWxD,EAAQ,IAAKA,EAAQ,IACjE,IAAI+D,EAAa/D,EAAQ,GACP,MAAd+D,IACA3B,EAAQmB,EAAsBC,WAAWO,GAAa/D,EAAQ,KAElE,IAAIgE,EAAYhE,EAAQ,GACpBgE,IACA3B,EAAS2B,QAIb7B,EAAW2B,EAEf,IAAKF,EAAqB,CACtB,IAAIK,GAAiB,EACjBC,EAAa9H,EAAO/B,OACpB8H,EAAW,IACX/F,EAAOlB,KAAK,oEACZ+I,GAAiB,GAEjB7B,EAAQ,IACRhG,EAAOlB,KAAK,iEACZ+I,GAAiB,GAEjBA,GACA7H,EAAO+H,OAAOD,EAAY,EAAG,8BAAiCJ,EAAM,iBAG5E,OAAS3B,SAAUA,EAAUC,MAAOA,EAAOC,OAAQA,GAzC/CwB,CAAoBF,EAASvH,EAAQwH,GA2C7C,SAASQ,EAAQC,EAAKC,GAGlB,YAFoB,IAAhBA,IAA0BA,MAC9BnL,OAAO0D,KAAKwH,GAAK7H,QAAQ,SAAUM,GAAQwH,EAAYxH,GAAQuH,EAAIvH,KAC5DwH,EAEX,SAASC,EAAgBC,GACrB,IAAIC,KAOJ,OANInL,MAAMoL,QAAQF,GACdA,EAAOhI,QAAQ,SAAUgC,GAAQ,OAAOmG,EAAWnG,GAAM,EAAOiG,KAGhEE,EAAWH,GAAQ,EAAOC,GAEvBA,EAEX,SAASE,EAAWH,EAAQI,EAAeN,GAEvC,QADoB,IAAhBA,IAA0BA,MAC1BM,EAIA,IAAK,IAAI9H,KAAQ0H,EACbF,EAAYxH,GAAQ0H,EAAO1H,QAI/BsH,EAAQI,EAAQF,GAEpB,OAAOA,EAEX,SAASO,EAAwB7I,EAAS2C,EAAKxD,GAG3C,OAAIA,EACOwD,EAAM,IAAMxD,EAAQ,IAGpB,GAGf,SAAS2J,EAAoB9I,GAMzB,IADA,IAAI+I,EAAiB,GACZ7K,EAAI,EAAGA,EAAI8B,EAAQkF,MAAM7G,OAAQH,IAEtC6K,GAAkBF,EAAwB7I,EADtC2C,EAAM3C,EAAQkF,MAAM8D,KAAK9K,GAC2B8B,EAAQkF,MAAM+D,iBAAiBtG,IAE3F,IAAK,IAAIA,KAAO3C,EAAQkF,MAEflF,EAAQkF,MAAM1H,eAAemF,KAAQA,EAAIuG,WAAW,OAIzDH,GAAkBF,EAAwB7I,EADR2C,EAkGzBmD,QAAQ,kBAAmB,SAASqD,cAjGenJ,EAAQkF,MAAMvC,KAE9E3C,EAAQoJ,aAAa,QAASL,GAElC,SAASM,EAAUrJ,EAASwI,EAAQc,GAC5BtJ,EAAe,QACf7C,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAClC,IAAIyI,EAAYC,EAAoB1I,GAChCwI,IAAiBA,EAAa9L,eAAesD,KAC7CwI,EAAaxI,GAAQd,EAAQkF,MAAMqE,IAEvCvJ,EAAQkF,MAAMqE,GAAaf,EAAO1H,KAGlCvB,KACAuJ,EAAoB9I,IAIhC,SAASyJ,EAAYzJ,EAASwI,GACtBxI,EAAe,QACf7C,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAClC,IAAIyI,EAAYC,EAAoB1I,GACpCd,EAAQkF,MAAMqE,GAAa,KAG3BhK,KACAuJ,EAAoB9I,IAIhC,SAAS0J,EAAwBC,GAC7B,OAAIrM,MAAMoL,QAAQiB,GACM,GAAhBA,EAAMtL,OACCsL,EAAM,GACV/M,EAAWgN,SAASD,GAExBA,EAaX,IAAIE,EAAc,IAAIC,OAAOC,oBAAmE,KAChG,SAASC,EAAmB7K,GACxB,IAAI8K,KACJ,GAAqB,iBAAV9K,EAAoB,CAG3B,IAFA,IAAI+K,EAAM/K,EAAMgL,WACZ7C,OAAQ,EACLA,EAAQuC,EAAYO,KAAKF,IAC5BD,EAAO/K,KAAKoI,EAAM,IAEtBuC,EAAYQ,UAAY,EAE5B,OAAOJ,EAEX,SAASK,EAAkBnL,EAAO8K,EAAQ7J,GACtC,IAAImK,EAAWpL,EAAMgL,WACjBK,EAAMD,EAASzE,QAAQ+D,EAAa,SAAUY,EAAGC,GACjD,IAAIC,EAAWV,EAAOS,GAMtB,OAJKT,EAAOzM,eAAekN,KACvBtK,EAAOlB,KAAK,kDAAoDwL,GAChEC,EAAW,IAERA,EAASR,aAGpB,OAAOK,GAAOD,EAAWpL,EAAQqL,EAErC,SAASI,EAAgBhM,GAGrB,IAFA,IAAIiM,KACA7B,EAAOpK,EAASI,QACZgK,EAAK/J,MACT4L,EAAI3L,KAAK8J,EAAK7J,OACd6J,EAAOpK,EAASI,OAEpB,OAAO6L,EAEX,IAAIC,EAAmB,gBACvB,SAAStB,EAAoBuB,GACzB,OAAOA,EAAMjF,QAAQgF,EAAkB,WAEnC,IADA,IAAIpM,KACKsM,EAAK,EAAGA,EAAK5M,UAAUC,OAAQ2M,IACpCtM,EAAEsM,GAAM5M,UAAU4M,GAEtB,OAAOtM,EAAE,GAAG4G,gBAMpB,SAAS2F,EAA+B9E,EAAUC,GAC9C,OAAoB,IAAbD,GAA4B,IAAVC,EAE7B,SAAS8E,EAAmClL,EAASC,EAAWkL,GAC5D,IAAIC,EAAqBjO,OAAO0D,KAAKsK,GACrC,GAAIC,EAAmB/M,QAAU4B,EAAU5B,OAAQ,CAC/C,IAAIgN,EAAqBpL,EAAU,GAC/BqL,KAOJ,GANAF,EAAmB5K,QAAQ,SAAUM,GAC5BuK,EAAmB7N,eAAesD,IACnCwK,EAAoBpM,KAAK4B,GAE7BuK,EAAmBvK,GAAQqK,EAAerK,KAE1CwK,EAAoBjN,OAMpB,IALA,IAAIkN,EAAU,WACV,IAAI9K,EAAKR,EAAU/B,GACnBoN,EAAoB9K,QAAQ,SAAUM,GAAQL,EAAGK,GAAQmF,EAAajG,EAASc,MAG1E5C,EAAI,EAAGA,EAAI+B,EAAU5B,OAAQH,IAClCqN,IAIZ,OAAOtL,EAEX,SAASuL,EAAaC,EAASC,EAAMC,GACjC,OAAQD,EAAKE,MACT,KAAK,EACD,OAAOH,EAAQI,aAAaH,EAAMC,GACtC,KAAK,EACD,OAAOF,EAAQK,WAAWJ,EAAMC,GACpC,KAAK,EACD,OAAOF,EAAQM,gBAAgBL,EAAMC,GACzC,KAAK,EACD,OAAOF,EAAQO,cAAcN,EAAMC,GACvC,KAAK,EACD,OAAOF,EAAQQ,WAAWP,EAAMC,GACpC,KAAK,EACD,OAAOF,EAAQS,aAAaR,EAAMC,GACtC,KAAK,EACD,OAAOF,EAAQU,eAAeT,EAAMC,GACxC,KAAK,EACD,OAAOF,EAAQW,WAAWV,EAAMC,GACpC,KAAK,EACD,OAAOF,EAAQY,eAAeX,EAAMC,GACxC,KAAK,EACD,OAAOF,EAAQa,kBAAkBZ,EAAMC,GAC3C,KAAK,GACD,OAAOF,EAAQc,gBAAgBb,EAAMC,GACzC,KAAK,GACD,OAAOF,EAAQe,WAAWd,EAAMC,GACpC,KAAK,GACD,OAAOF,EAAQgB,aAAaf,EAAMC,GACtC,QACI,MAAM,IAAItK,MAAM,8CAAgDqK,EAAKE,OAGjF,SAAS3F,EAAajG,EAASc,GAC3B,OAAO4L,OAAOC,iBAAiB3M,GAASc;;;;;;;OAU5C,IAAI8L,EAAY,IAuDZC,GAAsB,IAAIC,KAAK,OAAQ,MACvCC,GAAuB,IAAID,KAAK,QAAS,MAC7C,SAASE,GAAqBC,EAAKC,GAC/B,IAAIC,EAAoBN,GAAoBO,IAAIH,IAAQF,GAAqBK,IAAIH,GAC7EI,EAAoBR,GAAoBO,IAAIF,IAAQH,GAAqBK,IAAIF,GACjF,OAAO,SAAU9K,EAAWC,GACxB,IAAIiL,EAAWL,GAAOL,GAAaK,GAAO7K,EACtCmL,EAAWL,GAAON,GAAaM,GAAO7K,EAO1C,OANKiL,GAAYH,GAA0C,kBAAd/K,IACzCkL,EAAWlL,EAAYyK,GAAoBO,IAAIH,GAAOF,GAAqBK,IAAIH,KAE9EM,GAAYF,GAAwC,kBAAZhL,IACzCkL,EAAWlL,EAAUwK,GAAoBO,IAAIF,GAAOH,GAAqBK,IAAIF,IAE1EI,GAAYC,GAI3B,IACIC,GAAmB,IAAI1D,OAAO,cAA4B,KAqC9D,SAAS2D,GAAkB3N,EAAQ4N,EAAUtN,GACzC,OAAO,IAAIuN,GAA2B7N,GAAQ8N,MAAMF,EAAUtN,GAElE,IACIuN,GAA4C,WAC5C,SAASA,EAA2BE,GAChC9Q,KAAK8Q,QAAUA,EAkWnB,OAhWAF,EAA2B/P,UAAUgQ,MAAQ,SAAUF,EAAUtN,GAC7D,IAAIuL,EAAU,IAAImC,GAA2B1N,GAE7C,OADArD,KAAKgR,8BAA8BpC,GAC5BH,EAAazO,KAAM2M,EAAwBgE,GAAW/B,IAEjEgC,EAA2B/P,UAAUmQ,8BAAgC,SAAUpC,GAC3EA,EAAQqC,qBAXI,GAYZrC,EAAQsC,mBACRtC,EAAQsC,gBAbI,OAcZtC,EAAQuC,YAAc,GAE1BP,EAA2B/P,UAAUiO,aAAe,SAAU6B,EAAU/B,GACpE,IAAIwC,EAAQpR,KACRqR,EAAazC,EAAQyC,WAAa,EAClCC,EAAW1C,EAAQ0C,SAAW,EAC9BC,KACAC,KAyBJ,MAxB+B,KAA3Bb,EAASc,KAAKnJ,OAAO,IACrBsG,EAAQvL,OAAOlB,KAAK,wFAExBwO,EAASe,YAAYjO,QAAQ,SAAUkO,GAEnC,GADAP,EAAMJ,8BAA8BpC,GACpB,GAAZ+C,EAAI9C,KAAuB,CAC3B,IAAI+C,EAAaD,EACbE,EAASD,EAAWH,KACxBI,EAAOzE,WAAW0E,MAAM,WAAWrO,QAAQ,SAAUrC,GACjDwQ,EAAWH,KAAOrQ,EAClBmQ,EAAOpP,KAAKiP,EAAMrC,WAAW6C,EAAYhD,MAE7CgD,EAAWH,KAAOI,OAEjB,GAAgB,GAAZF,EAAI9C,KAA4B,CACrC,IAAIkD,EAAaX,EAAMpC,gBAAgB2C,EAAK/C,GAC5CyC,GAAcU,EAAWV,WACzBC,GAAYS,EAAWT,SACvBE,EAAYrP,KAAK4P,QAGjBnD,EAAQvL,OAAOlB,KAAK,8EAIxB0M,KAAM,EACN4C,KAAMd,EAASc,KAAMF,OAAQA,EAAQC,YAAaA,EAAaH,WAAYA,EAAYC,SAAUA,EACjGU,QAAS,OAGjBpB,EAA2B/P,UAAUkO,WAAa,SAAU4B,EAAU/B,GAClE,IAAIqD,EAAWjS,KAAKqP,WAAWsB,EAASlF,OAAQmD,GAC5CsD,EAAavB,EAASqB,SAAWrB,EAASqB,QAAQ9E,QAAW,KACjE,GAAI+E,EAASE,sBAAuB,CAChC,IAAIC,EAAgB,IAAIrC,IACpBsC,EAAWH,MAaf,GAZAD,EAASxG,OAAOhI,QAAQ,SAAUrB,GAC9B,GAAIkQ,GAASlQ,GAAQ,CACjB,IAAImQ,EAAcnQ,EAClBhC,OAAO0D,KAAKyO,GAAa9O,QAAQ,SAAUM,GACvCkJ,EAAmBsF,EAAYxO,IAAON,QAAQ,SAAU+O,GAC/CH,EAAS5R,eAAe+R,IACzBJ,EAAcK,IAAID,UAMlCJ,EAAcM,KAAM,CACpB,IAAIC,EAAiB9E,EAAgBuE,EAAcQ,UACnDhE,EAAQvL,OAAOlB,KAAK,UAAawO,EAASc,KAAO,iFAAoFkB,EAAepO,KAAK,QAGjK,OACIsK,KAAM,EACN4C,KAAMd,EAASc,KACftJ,MAAO8J,EACPD,QAASE,GAAchF,OAAQgF,GAAc,OAGrDtB,EAA2B/P,UAAUmO,gBAAkB,SAAU2B,EAAU/B,GACvEA,EAAQyC,WAAa,EACrBzC,EAAQ0C,SAAW,EACnB,IAAIuB,EAAYpE,EAAazO,KAAM2M,EAAwBgE,EAASkC,WAAYjE,GAEhF,OACIC,KAAM,EACNiE,SA1MZ,SAASC,EAAoBC,EAAiB3P,GAC1C,IAAI4P,KASJ,MAR8B,iBAAnBD,EACPA,EACKlB,MAAM,WACNrO,QAAQ,SAAUgK,GAAO,OAOtC,SAASyF,EAAwBC,EAAUF,EAAa5P,GACpD,GAAmB,KAAf8P,EAAS,GAAW,CACpB,IAAI/K,EAqBZ,SAASgL,EAAoBC,EAAOhQ,GAChC,OAAQgQ,GACJ,IAAK,SACD,MAAO,YACX,IAAK,SACD,MAAO,YACX,IAAK,aACD,OAAO,SAAUhO,EAAWC,GAAW,OAAOmF,WAAWnF,GAAWmF,WAAWpF,IACnF,IAAK,aACD,OAAO,SAAUA,EAAWC,GAAW,OAAOmF,WAAWnF,GAAWmF,WAAWpF,IACnF,QAEI,OADAhC,EAAOlB,KAAK,+BAAkCkR,EAAQ,sBAC/C,UAjCED,CAAoBD,EAAU9P,GAC3C,GAAqB,mBAAV+E,EAEP,YADA6K,EAAY9Q,KAAKiG,GAGrB+K,EAAW/K,EAEf,IAAImC,EAAQ4I,EAAS5I,MAAM,2CAC3B,GAAa,MAATA,GAAiBA,EAAMjJ,OAAS,EAEhC,OADA+B,EAAOlB,KAAK,uCAA0CgR,EAAW,sBAC1DF,EAEX,IAAI5N,EAAYkF,EAAM,GAClB+I,EAAY/I,EAAM,GAClBjF,EAAUiF,EAAM,GACpB0I,EAAY9Q,KAAK8N,GAAqB5K,EAAWC,IAE7B,KAAhBgO,EAAU,IADWjO,GAAawK,GAAavK,GAAWuK,GAE1DoD,EAAY9Q,KAAK8N,GAAqB3K,EAASD,IA3BV6N,CAAwBzF,EAAKwF,EAAa5P,KAG/E4P,EAAY9Q,KAAK6Q,GAEdC,EA6LYF,CAAoBpC,EAAS4C,KAAM3E,EAAQvL,QAItDwP,UAAWA,EACXxB,WAAYzC,EAAQyC,WACpBC,SAAU1C,EAAQ0C,SAClBU,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAUoO,cAAgB,SAAU0B,EAAU/B,GACrE,IAAIwC,EAAQpR,KACZ,OACI6O,KAAM,EACNjC,MAAO+D,EAAS/D,MAAMjH,IAAI,SAAUzE,GAAK,OAAOuN,EAAa2C,EAAOlQ,EAAG0N,KACvEoD,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAUqO,WAAa,SAAUyB,EAAU/B,GAClE,IAAIwC,EAAQpR,KACRmR,EAAcvC,EAAQuC,YACtBsC,EAAe,EACf7G,EAAQ+D,EAAS/D,MAAMjH,IAAI,SAAU+N,GACrC9E,EAAQuC,YAAcA,EACtB,IAAIwC,EAAWlF,EAAa2C,EAAOsC,EAAM9E,GAEzC,OADA6E,EAAeG,KAAKC,IAAIJ,EAAc7E,EAAQuC,aACvCwC,IAGX,OADA/E,EAAQuC,YAAcsC,GAElB5E,KAAM,EACNjC,MAAOA,EACPoF,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAUsO,aAAe,SAAUwB,EAAU/B,GACpE,IAEIqD,EAFA6B,EAiSZ,SAASC,EAAmB3R,EAAOiB,GAC/B,IAAIuH,EAAU,KACd,GAAIxI,EAAM3B,eAAe,YACrBmK,EAAUxI,OAET,GAAoB,iBAATA,EAEZ,OAAO4R,GADQrJ,EAAcvI,EAAOiB,GAAQ+F,SACb,EAAG,IAEtC,IAAI6K,EAAW7R,EAEf,GADgB6R,EAASnC,MAAM,OAAOoC,KAAK,SAAUC,GAAK,MAAsB,KAAfA,EAAE7L,OAAO,IAA4B,KAAf6L,EAAE7L,OAAO,KACjF,CACX,IAAI8L,EAAMJ,GAAc,EAAG,EAAG,IAG9B,OAFAI,EAAIC,SAAU,EACdD,EAAIH,SAAWA,EACRG,EAGX,OAAOJ,IADPpJ,EAAUA,GAAWD,EAAcsJ,EAAU5Q,IAChB+F,SAAUwB,EAAQvB,MAAOuB,EAAQtB,QAnT1CyK,CAAmBpD,EAAS/F,QAASgE,EAAQvL,QAC7DuL,EAAQ0F,sBAAwBR,EAEhC,IAAIS,EAAgB5D,EAASlF,OAASkF,EAASlF,OAAS5L,EAAWsI,UACnE,GAA0B,GAAtBoM,EAAc1F,KACdoD,EAAWjS,KAAKoP,eAAemF,EAAe3F,OAE7C,CACD,IAAI4F,EAAkB7D,EAASlF,OAC3BgJ,GAAU,EACd,IAAKD,EAAiB,CAClBC,GAAU,EACV,IAAIC,KACAZ,EAAUxK,SACVoL,EAAqB,OAAIZ,EAAUxK,QAEvCkL,EAAkB3U,EAAWsI,MAAMuM,GAEvC9F,EAAQuC,aAAe2C,EAAU1K,SAAW0K,EAAUzK,MACtD,IAAIsL,EAAY3U,KAAKqP,WAAWmF,EAAiB5F,GACjD+F,EAAUC,YAAcH,EACxBxC,EAAW0C,EAGf,OADA/F,EAAQ0F,sBAAwB,MAE5BzF,KAAM,EACNjE,QAASkJ,EACT3L,MAAO8J,EACPD,QAAS,OAGjBpB,EAA2B/P,UAAUwO,WAAa,SAAUsB,EAAU/B,GAClE,IAAIwF,EAAMpU,KAAK6U,cAAclE,EAAU/B,GAEvC,OADA5O,KAAK8U,kBAAkBV,EAAKxF,GACrBwF,GAEXxD,EAA2B/P,UAAUgU,cAAgB,SAAUlE,EAAU/B,GACrE,IAAInD,KACAlL,MAAMoL,QAAQgF,EAASlF,QACvBkF,EAASlF,OAAOhI,QAAQ,SAAUsR,GACL,iBAAdA,EACHA,GAAclV,EAAWuE,WACzBqH,EAAOtJ,KAAK4S,GAGZnG,EAAQvL,OAAOlB,KAAK,mCAAqC4S,EAAa,oBAI1EtJ,EAAOtJ,KAAK4S,KAKpBtJ,EAAOtJ,KAAKwO,EAASlF,QAEzB,IAAI0G,GAAwB,EACxB6C,EAAkB,KAoBtB,OAnBAvJ,EAAOhI,QAAQ,SAAUwR,GACrB,GAAI3C,GAAS2C,GAAY,CACrB,IAAIC,EAAWD,EACX3L,EAAS4L,EAAiB,OAK9B,GAJI5L,IACA0L,EAAkB1L,SACX4L,EAAiB,SAEvB/C,EACD,IAAK,IAAIpO,KAAQmR,EAEb,GADYA,EAASnR,GACXqJ,WAAWhH,QAxlBf,OAwlBmD,EAAG,CACxD+L,GAAwB,EACxB,WAOhBtD,KAAM,EACNpD,OAAQA,EACRnC,OAAQ0L,EACRrR,OAAQgN,EAAShN,OAAQwO,sBAAuBA,EAChDH,QAAS,OAGjBpB,EAA2B/P,UAAUiU,kBAAoB,SAAUV,EAAKxF,GACpE,IAAIwC,EAAQpR,KACR4K,EAAUgE,EAAQ0F,sBAClBa,EAAUvG,EAAQuC,YAClBiE,EAAYxG,EAAQuC,YACpBvG,GAAWwK,EAAY,IACvBA,GAAaxK,EAAQxB,SAAWwB,EAAQvB,OAE5C+K,EAAI3I,OAAOhI,QAAQ,SAAU4R,GACL,iBAATA,GAEXjV,OAAO0D,KAAKuR,GAAO5R,QAAQ,SAAUM,GACjC,GAAKqN,EAAMN,QAAQ/I,sBAAsBhE,GAAzC,CAIA,IAAImN,EAAkBtC,EAAQsC,gBAAgBtC,EAAQqC,sBAClDqE,EAAiBpE,EAAgBnN,GACjCwR,GAAuB,EACvBD,IACIF,GAAaD,GAAWC,GAAaE,EAAeF,WACpDD,GAAWG,EAAeH,UAC1BvG,EAAQvL,OAAOlB,KAAK,qBAAwB4B,EAAO,uCAA2CuR,EAAeF,UAAY,YAAgBE,EAAeH,QAAU,4EAAgFC,EAAY,YAAgBD,EAAU,OACxRI,GAAuB,GAK3BH,EAAYE,EAAeF,WAE3BG,IACArE,EAAgBnN,IAAUqR,UAAWA,EAAWD,QAASA,IAEzDvG,EAAQoD,SApe5B,SAASwD,EAAoBpT,EAAO4P,EAAS3O,GACzC,IAAI6J,EAAS8E,EAAQ9E,WACjBjG,EAAUgG,EAAmB7K,GAC7B6E,EAAQ3F,QACR2F,EAAQxD,QAAQ,SAAUkK,GACjBT,EAAOzM,eAAekN,IACvBtK,EAAOlB,KAAK,+CAAiDwL,EAAU,kCA+dnE6H,CAAoBH,EAAMtR,GAAO6K,EAAQoD,QAASpD,EAAQvL,aArB1DuL,EAAQvL,OAAOlB,KAAK,oCAAuC4B,EAAO,yDA0BlF6M,EAA2B/P,UAAUuO,eAAiB,SAAUuB,EAAU/B,GACtE,IAAIwC,EAAQpR,KACRoU,GAAQvF,KAAM,EAAmBpD,UAAYuG,QAAS,MAC1D,IAAKpD,EAAQ0F,sBAET,OADA1F,EAAQvL,OAAOlB,KAAK,4DACbiS,EAEX,IACIqB,EAA4B,EAC5BC,KACAC,GAAoB,EACpBC,GAAsB,EACtBrS,EAAiB,EACjBL,EAAYyN,EAAS/D,MAAMjH,IAAI,SAAU8F,GACzC,IAAItD,EAAQiJ,EAAMyD,cAAcpJ,EAAQmD,GACpCiH,EAA4B,MAAhB1N,EAAMxE,OAAiBwE,EAAMxE,OA+HzD,SAASmS,EAAcrK,GACnB,GAAqB,iBAAVA,EACP,OAAO,KACX,IAAI9H,EAAS,KACb,GAAIpD,MAAMoL,QAAQF,GACdA,EAAOhI,QAAQ,SAAUsR,GACrB,GAAIzC,GAASyC,IAAeA,EAAWtU,eAAe,UAAW,CAC7D,IAAI6K,EAAMyJ,EACVpR,EAAS8G,WAAWa,EAAY,eACzBA,EAAY,eAI1B,GAAIgH,GAAS7G,IAAWA,EAAOhL,eAAe,UAAW,CAC1D,IAAI6K,EAAMG,EACV9H,EAAS8G,WAAWa,EAAY,eACzBA,EAAY,OAEvB,OAAO3H,EAjJuDmS,CAAc3N,EAAMsD,QACtE9H,EAAS,EASb,OARiB,MAAbkS,IACAJ,IACA9R,EAASwE,EAAMxE,OAASkS,GAE5BD,EAAsBA,GAAuBjS,EAAS,GAAKA,EAAS,EACpEgS,EAAoBA,GAAqBhS,EAASJ,EAClDA,EAAiBI,EACjB+R,EAAQvT,KAAKwB,GACNwE,IAEPyN,GACAhH,EAAQvL,OAAOlB,KAAK,+DAEpBwT,GACA/G,EAAQvL,OAAOlB,KAAK,wDAExB,IAAIb,EAASqP,EAAS/D,MAAMtL,OACxByU,EAAkB,EAClBN,EAA4B,GAAKA,EAA4BnU,EAC7DsN,EAAQvL,OAAOlB,KAAK,yEAEc,GAA7BsT,IACLM,EAhCsB,GAgCmBzU,EAAS,IAEtD,IAAI0U,EAAQ1U,EAAS,EACjB6P,EAAcvC,EAAQuC,YACtBmD,EAAwB1F,EAAQ0F,sBAChC2B,EAAkB3B,EAAsBlL,SAU5C,OATAlG,EAAUO,QAAQ,SAAUC,EAAIvC,GAC5B,IAAIwC,EAASoS,EAAkB,EAAK5U,GAAK6U,EAAQ,EAAKD,EAAkB5U,EAAMuU,EAAQvU,GAClF+U,EAAwBvS,EAASsS,EACrCrH,EAAQuC,YAAcA,EAAcmD,EAAsBjL,MAAQ6M,EAClE5B,EAAsBlL,SAAW8M,EACjC9E,EAAM0D,kBAAkBpR,EAAIkL,GAC5BlL,EAAGC,OAASA,EACZyQ,EAAI3I,OAAOtJ,KAAKuB,KAEb0Q,GAEXxD,EAA2B/P,UAAUyO,eAAiB,SAAUqB,EAAU/B,GACtE,OACIC,KAAM,EACNgE,UAAWpE,EAAazO,KAAM2M,EAAwBgE,EAASkC,WAAYjE,GAC3EoD,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAU0O,kBAAoB,SAAUoB,EAAU/B,GAEzE,OADAA,EAAQ0C,YAEJzC,KAAM,EACNmD,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAU2O,gBAAkB,SAAUmB,EAAU/B,GACvE,OACIC,KAAM,GACNgE,UAAW7S,KAAKsP,eAAeqB,EAASkC,UAAWjE,GACnDoD,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAU4O,WAAa,SAAUkB,EAAU/B,GAClE,IAAIuH,EAAiBvH,EAAQqC,qBACzBe,EAAWrB,EAASqB,YACxBpD,EAAQyC,aACRzC,EAAQwH,aAAezF,EACvB,IAAI0F,EAAK5U,EA+BjB,SAAS6U,EAAkB3P,GACvB,IAAI4P,IAAe5P,EAASmL,MAAM,WAAW0E,KAAK,SAAUC,GAAS,MAjZxD,SAiZ+DA,IAQ5E,OAPIF,IACA5P,EAAWA,EAASoC,QAAQ0H,GAAkB,MAGlD9J,EAAWA,EAASoC,QAAQ,OAjwBN,eAkwBjBA,QAAQ,QAAS,SAAUwB,GAAS,MAAOmM,eAA4BnM,EAAMjE,OAAO,KACpFyC,QAAQ,cAjwBW,iBAkwBNwN,GAxCED,CAAkB3F,EAAShK,UAAW,GAAIA,EAAW0P,EAAG,GAAIM,EAAcN,EAAG,GAC7FzH,EAAQqC,qBACJkF,EAAe7U,OAAU6U,EAAiB,IAAMxP,EAAYA,EAChEjB,EAAgBkJ,EAAQsC,gBAAiBtC,EAAQqC,yBACjD,IAAI4B,EAAYpE,EAAazO,KAAM2M,EAAwBgE,EAASkC,WAAYjE,GAGhF,OAFAA,EAAQwH,aAAe,KACvBxH,EAAQqC,qBAAuBkF,GAE3BtH,KAAM,GACNlI,SAAUA,EACVqP,MAAOhE,EAAQgE,OAAS,EACxBY,WAAY5E,EAAQ4E,SAAUD,YAAaA,EAAa9D,UAAWA,EACnEgE,iBAAkBlG,EAAShK,SAC3BqL,QAASwB,GAA0B7C,EAASqB,WAGpDpB,EAA2B/P,UAAU6O,aAAe,SAAUiB,EAAU/B,GAC/DA,EAAQwH,cACTxH,EAAQvL,OAAOlB,KAAK,gDAExB,IAAIyI,EAA+B,SAArB+F,EAAS/F,SACjBxB,SAAU,EAAGC,MAAO,EAAGC,OAAQ,QACjCqB,EAAcgG,EAAS/F,QAASgE,EAAQvL,QAAQ,GACpD,OACIwL,KAAM,GACNgE,UAAWpE,EAAazO,KAAM2M,EAAwBgE,EAASkC,WAAYjE,GAAUhE,QAASA,EAC9FoH,QAAS,OAGVpB,EApWoC,GAoX3CG,GACA,SAASA,GAA2B1N,GAChCrD,KAAKqD,OAASA,EACdrD,KAAKqR,WAAa,EAClBrR,KAAKsR,SAAW,EAChBtR,KAAK8W,kBAAoB,KACzB9W,KAAKoW,aAAe,KACpBpW,KAAKiR,qBAAuB,KAC5BjR,KAAKsU,sBAAwB,KAC7BtU,KAAKmR,YAAc,EACnBnR,KAAKkR,mBACLlR,KAAKgS,QAAU,MAwBvB,SAASM,GAASlQ,GACd,OAAQ7B,MAAMoL,QAAQvJ,IAA0B,iBAATA,EAsB3C,SAASoR,GAA0BxB,GAU/B,OATIA,GACAA,EAAU3G,EAAQ2G,IACE,SAChBA,EAAgB,OAjE5B,SAAS+E,EAAgBzL,GACrB,OAAOA,EAAMD,EAAQC,GAAO,KAgEAyL,CAAgB/E,EAAgB,SAIxDA,KAEGA,EAEX,SAASgC,GAAc5K,EAAUC,EAAOC,GACpC,OAASF,SAAUA,EAAUC,MAAOA,EAAOC,OAAQA,GAGvD,SAAS0N,GAA0B/T,EAASC,EAAW+T,EAAeC,EAAgB9N,EAAUC,EAAOC,EAAQ6N,GAG3G,YAFe,IAAX7N,IAAqBA,EAAS,WACd,IAAhB6N,IAA0BA,GAAc,IAExCtI,KAAM,EACN5L,QAASA,EACTC,UAAWA,EACX+T,cAAeA,EACfC,eAAgBA,EAChB9N,SAAUA,EACVC,MAAOA,EACPnE,UAAWkE,EAAWC,EAAOC,OAAQA,EAAQ6N,YAAaA,GAIlE,IAAIC,GAAuC,WACvC,SAASA,IACLpX,KAAKqX,KAAO,IAAIvR,IAqBpB,OAnBAsR,EAAsBvW,UAAUyW,QAAU,SAAUrU,GAChD,IAAIsU,EAAevX,KAAKqX,KAAKtR,IAAI9C,GAOjC,OANIsU,EACAvX,KAAKqX,KAAKG,OAAOvU,GAGjBsU,KAEGA,GAEXH,EAAsBvW,UAAU4W,OAAS,SAAUxU,EAASsU,GACxD,IAAIG,EAAuB1X,KAAKqX,KAAKtR,IAAI9C,GACpCyU,GACD1X,KAAKqX,KAAKrR,IAAI/C,EAASyU,MAE3BA,EAAqBvV,KAAKX,MAAMkW,EAAsBpV,EAASiV,KAEnEH,EAAsBvW,UAAUwP,IAAM,SAAUpN,GAAW,OAAOjD,KAAKqX,KAAKhH,IAAIpN,IAChFmU,EAAsBvW,UAAU8W,MAAQ,WAAc3X,KAAKqX,KAAKM,SACzDP,EAvB+B,GA4BtCQ,GAAoB,IAAI7K,OADV,SAC8B,KAE5C8K,GAAoB,IAAI9K,OADV,SAC8B,KAqFhD,SAAS+K,GAAwB/U,EAAQgV,EAAa3D,EAAK4D,EAAgBC,EAAgBC,EAAgBC,EAAanG,EAASoG,EAAiB/U,GAI9I,YAHuB,IAAnB6U,IAA6BA,WACb,IAAhBC,IAA0BA,WACf,IAAX9U,IAAqBA,OAClB,IAAIgV,IAAkCC,eAAevV,EAAQgV,EAAa3D,EAAK4D,EAAgBC,EAAgBC,EAAgBC,EAAanG,EAASoG,EAAiB/U,GAEjL,IAAIgV,GAAiD,WACjD,SAASA,KAqQT,OAnQAA,EAAgCxX,UAAUyX,eAAiB,SAAUvV,EAAQgV,EAAa3D,EAAK4D,EAAgBC,EAAgBC,EAAgBC,EAAanG,EAASoG,EAAiB/U,QACnK,IAAXA,IAAqBA,MACzB+U,EAAkBA,GAAmB,IAAIhB,GACzC,IAAIxI,EAAU,IAAI2J,GAAyBxV,EAAQgV,EAAaK,EAAiBJ,EAAgBC,EAAgB5U,MACjHuL,EAAQoD,QAAUA,EAClBpD,EAAQ4J,gBAAgBlM,WAAW4L,GAAiB,KAAMtJ,EAAQvL,OAAQ2O,GAC1EvD,EAAazO,KAAMoU,EAAKxF,GAExB,IAAI6J,EAAY7J,EAAQ6J,UAAUC,OAAO,SAAUC,GAAY,OAAOA,EAASC,sBAC/E,GAAIH,EAAUnX,QAAUlB,OAAO0D,KAAKqU,GAAa7W,OAAQ,CACrD,IAAIuX,EAAKJ,EAAUA,EAAUnX,OAAS,GACjCuX,EAAGC,2BACJD,EAAGvM,WAAW6L,GAAc,KAAMvJ,EAAQvL,OAAQ2O,GAG1D,OAAOyG,EAAUnX,OAASmX,EAAU9S,IAAI,SAAUgT,GAAY,OAAOA,EAASL,oBACzEtB,GAA0Be,WAAyB,EAAG,EAAG,IAAI,KAEtEM,EAAgCxX,UAAUiO,aAAe,SAAUsF,EAAKxF,KAGxEyJ,EAAgCxX,UAAUkO,WAAa,SAAUqF,EAAKxF,KAGtEyJ,EAAgCxX,UAAUmO,gBAAkB,SAAUoF,EAAKxF,KAG3EyJ,EAAgCxX,UAAU0O,kBAAoB,SAAU6E,EAAKxF,GACzE,IAAImK,EAAsBnK,EAAQwJ,gBAAgBd,QAAQ1I,EAAQ3L,SAClE,GAAI8V,EAAqB,CACrB,IAAIC,EAAepK,EAAQqK,iBAAiB7E,EAAIpC,SAC5CoD,EAAYxG,EAAQ4J,gBAAgBrH,YACpCgE,EAAUnV,KAAKkZ,sBAAsBH,EAAqBC,EAAcA,EAAahH,SACrFoD,GAAaD,GAGbvG,EAAQuK,yBAAyBhE,GAGzCvG,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAU2O,gBAAkB,SAAU4E,EAAKxF,GACvE,IAAIoK,EAAepK,EAAQqK,iBAAiB7E,EAAIpC,SAChDgH,EAAaG,2BACbnZ,KAAKsP,eAAe8E,EAAIvB,UAAWmG,GACnCpK,EAAQuK,yBAAyBH,EAAaR,gBAAgBrH,aAC9DvC,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUqY,sBAAwB,SAAU3B,EAAc3I,EAASoD,GAC/F,IACIyB,EADY7E,EAAQ4J,gBAAgBrH,YAIpC/H,EAA+B,MAApB4I,EAAQ5I,SAAmBkB,EAAmB0H,EAAQ5I,UAAY,KAC7EC,EAAyB,MAAjB2I,EAAQ3I,MAAgBiB,EAAmB0H,EAAQ3I,OAAS,KAQxE,OAPiB,IAAbD,GACAmO,EAAa9T,QAAQ,SAAU4V,GAC3B,IAAIC,EAAqB1K,EAAQ2K,4BAA4BF,EAAajQ,EAAUC,GACpFoK,EACIG,KAAKC,IAAIJ,EAAc6F,EAAmBlQ,SAAWkQ,EAAmBjQ,SAG7EoK,GAEX4E,EAAgCxX,UAAUyO,eAAiB,SAAU8E,EAAKxF,GACtEA,EAAQ4K,cAAcpF,EAAIpC,SAAS,GACnCvD,EAAazO,KAAMoU,EAAIvB,UAAWjE,GAClCA,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUoO,cAAgB,SAAUmF,EAAKxF,GACrE,IAAIwC,EAAQpR,KACRyZ,EAAkB7K,EAAQ6K,gBAC1BC,EAAM9K,EACNoD,EAAUoC,EAAIpC,QAClB,GAAIA,IAAYA,EAAQ9E,QAAU8E,EAAQ3I,UACtCqQ,EAAM9K,EAAQqK,iBAAiBjH,IAC3BmH,2BACiB,MAAjBnH,EAAQ3I,OAAe,CACM,GAAzBqQ,EAAIN,aAAavK,OACjB6K,EAAIlB,gBAAgBmB,wBACpBD,EAAIN,aAAeQ,IAEvB,IAAIvQ,EAAQiB,EAAmB0H,EAAQ3I,OACvCqQ,EAAIG,cAAcxQ,GAGtB+K,EAAIxH,MAAMtL,SACV8S,EAAIxH,MAAMnJ,QAAQ,SAAUvC,GAAK,OAAOuN,EAAa2C,EAAOlQ,EAAGwY,KAE/DA,EAAIlB,gBAAgBsB,wBAIhBJ,EAAID,gBAAkBA,GACtBC,EAAIP,4BAGZvK,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUqO,WAAa,SAAUkF,EAAKxF,GAClE,IAAIwC,EAAQpR,KACR+Z,KACAtG,EAAe7E,EAAQ4J,gBAAgBrH,YACvC9H,EAAQ+K,EAAIpC,SAAWoC,EAAIpC,QAAQ3I,MAAQiB,EAAmB8J,EAAIpC,QAAQ3I,OAAS,EACvF+K,EAAIxH,MAAMnJ,QAAQ,SAAUvC,GACxB,IAAI8X,EAAepK,EAAQqK,iBAAiB7E,EAAIpC,SAC5C3I,GACA2P,EAAaa,cAAcxQ,GAE/BoF,EAAa2C,EAAOlQ,EAAG8X,GACvBvF,EAAeG,KAAKC,IAAIJ,EAAcuF,EAAaR,gBAAgBrH,aACnE4I,EAAe5X,KAAK6W,EAAaR,mBAKrCuB,EAAetW,QAAQ,SAAUkV,GAAY,OAAO/J,EAAQ4J,gBAAgBwB,6BAA6BrB,KACzG/J,EAAQuK,yBAAyB1F,GACjC7E,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUoZ,aAAe,SAAU7F,EAAKxF,GACpE,GAAIwF,EAAIC,QAAS,CACb,IAAIJ,EAAWG,EAAIH,SAEnB,OAAOtJ,EADWiE,EAAQ1B,OAASK,EAAkB0G,EAAUrF,EAAQ1B,OAAQ0B,EAAQvL,QAAU4Q,EAC/DrF,EAAQvL,QAG1C,OAAS+F,SAAUgL,EAAIhL,SAAUC,MAAO+K,EAAI/K,MAAOC,OAAQ8K,EAAI9K,SAGvE+O,EAAgCxX,UAAUsO,aAAe,SAAUiF,EAAKxF,GACpE,IAAIhE,EAAUgE,EAAQ0F,sBAAwBtU,KAAKia,aAAa7F,EAAIxJ,QAASgE,GACzE+J,EAAW/J,EAAQ4J,gBACnB5N,EAAQvB,QACRuF,EAAQsL,cAActP,EAAQvB,OAC9BsP,EAASgB,yBAEb,IAAIxR,EAAQiM,EAAIjM,MACE,GAAdA,EAAM0G,KACN7O,KAAKoP,eAAejH,EAAOyG,IAG3BA,EAAQsL,cAActP,EAAQxB,UAC9BpJ,KAAKqP,WAAWlH,EAAOyG,GACvB+J,EAASmB,yBAEblL,EAAQ0F,sBAAwB,KAChC1F,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUwO,WAAa,SAAU+E,EAAKxF,GAClE,IAAI+J,EAAW/J,EAAQ4J,gBACnB5N,EAAUgE,EAAQ0F,uBAGjB1J,GAAW+N,EAASwB,4BAA4B7Y,QACjDqX,EAASyB,eAEb,IAAI9Q,EAAUsB,GAAWA,EAAQtB,QAAW8K,EAAI9K,OAC5C8K,EAAIQ,YACJ+D,EAAS0B,eAAe/Q,GAGxBqP,EAASrM,UAAU8H,EAAI3I,OAAQnC,EAAQsF,EAAQvL,OAAQuL,EAAQoD,SAEnEpD,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAUuO,eAAiB,SAAUgF,EAAKxF,GACtE,IAAI0F,EAAwB1F,EAAQ0F,sBAChCc,EAAaxG,EAAuB,gBAAExF,SACtCA,EAAWkL,EAAsBlL,SAEjCkR,EADe1L,EAAQqK,mBACMT,gBACjC8B,EAAchR,OAASgL,EAAsBhL,OAC7C8K,EAAI3I,OAAOhI,QAAQ,SAAUiQ,GAEzB4G,EAAcC,aADD7G,EAAK/P,QAAU,GACOyF,GACnCkR,EAAchO,UAAUoH,EAAKjI,OAAQiI,EAAKpK,OAAQsF,EAAQvL,OAAQuL,EAAQoD,SAC1EsI,EAAcR,0BAIlBlL,EAAQ4J,gBAAgBwB,6BAA6BM,GAGrD1L,EAAQuK,yBAAyB/D,EAAYhM,GAC7CwF,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAU4O,WAAa,SAAU2E,EAAKxF,GAClE,IAAIwC,EAAQpR,KAGRoV,EAAYxG,EAAQ4J,gBAAgBrH,YACpCa,EAAWoC,EAAIpC,YACf3I,EAAQ2I,EAAQ3I,MAAQiB,EAAmB0H,EAAQ3I,OAAS,EAC5DA,IAAwC,IAA9BuF,EAAQwK,aAAavK,MACjB,GAAbuG,GAAkBxG,EAAQ4J,gBAAgB2B,4BAA4B7Y,UACvEsN,EAAQ4J,gBAAgBmB,wBACxB/K,EAAQwK,aAAeQ,IAE3B,IAAInG,EAAe2B,EACfoF,EAAO5L,EAAQlG,YAAY0L,EAAIzN,SAAUyN,EAAIyC,iBAAkBzC,EAAI4B,MAAO5B,EAAIuC,cAAa3E,EAAQ4E,SAAyBhI,EAAQvL,QACxIuL,EAAQ6L,kBAAoBD,EAAKlZ,OACjC,IAAIoZ,EAAsB,KAC1BF,EAAK/W,QAAQ,SAAUR,EAAS9B,GAC5ByN,EAAQ+L,kBAAoBxZ,EAC5B,IAAI6X,EAAepK,EAAQqK,iBAAiB7E,EAAIpC,QAAS/O,GACrDoG,GACA2P,EAAaa,cAAcxQ,GAE3BpG,IAAY2L,EAAQ3L,UACpByX,EAAsB1B,EAAaR,iBAEvC/J,EAAa2C,EAAOgD,EAAIvB,UAAWmG,GAInCA,EAAaR,gBAAgBsB,wBAE7BrG,EAAeG,KAAKC,IAAIJ,EADVuF,EAAaR,gBAAgBrH,eAG/CvC,EAAQ+L,kBAAoB,EAC5B/L,EAAQ6L,kBAAoB,EAC5B7L,EAAQuK,yBAAyB1F,GAC7BiH,IACA9L,EAAQ4J,gBAAgBwB,6BAA6BU,GACrD9L,EAAQ4J,gBAAgBmB,yBAE5B/K,EAAQwK,aAAehF,GAE3BiE,EAAgCxX,UAAU6O,aAAe,SAAU0E,EAAKxF,GACpE,IAAIgM,EAAgBhM,EAAQgM,cACxB/B,EAAKjK,EAAQ4J,gBACb5N,EAAUwJ,EAAIxJ,QACdxB,EAAWwK,KAAKiH,IAAIjQ,EAAQxB,UAC5B0R,EAAU1R,GAAYwF,EAAQ6L,kBAAoB,GAClDpR,EAAQD,EAAWwF,EAAQ+L,kBAE/B,OADyB/P,EAAQxB,SAAW,EAAI,UAAYwB,EAAQtB,QAEhE,IAAK,UACDD,EAAQyR,EAAUzR,EAClB,MACJ,IAAK,OACDA,EAAQuR,EAAcG,mBAG9B,IAAIpC,EAAW/J,EAAQ4J,gBACnBnP,GACAsP,EAASkB,cAAcxQ,GAE3B,IAAI2R,EAAerC,EAASxH,YAC5B1C,EAAazO,KAAMoU,EAAIvB,UAAWjE,GAClCA,EAAQwK,aAAehF,EAKvBwG,EAAcG,mBACTlC,EAAG1H,YAAc6J,GAAiBnC,EAAGzD,UAAYwF,EAAcpC,gBAAgBpD,YAEjFiD,EAtQyC,GAwQhDuB,MACArB,GAA0C,WAC1C,SAASA,EAAyBzH,EAAS7N,EAASmV,EAAiB6C,EAAiBC,EAAiB7X,EAAQoV,EAAW0C,GACtHnb,KAAK8Q,QAAUA,EACf9Q,KAAKiD,QAAUA,EACfjD,KAAKoY,gBAAkBA,EACvBpY,KAAKib,gBAAkBA,EACvBjb,KAAKkb,gBAAkBA,EACvBlb,KAAKqD,OAASA,EACdrD,KAAKyY,UAAYA,EACjBzY,KAAK4a,cAAgB,KACrB5a,KAAKsU,sBAAwB,KAC7BtU,KAAKoZ,aAAeQ,GACpB5Z,KAAKyZ,gBAAkB,EACvBzZ,KAAKgS,WACLhS,KAAK2a,kBAAoB,EACzB3a,KAAKya,kBAAoB,EACzBza,KAAK+a,mBAAqB,EAC1B/a,KAAKwY,gBAAkB2C,GAAmB,IAAIC,GAAgBpb,KAAK8Q,QAAS7N,EAAS,GACrFwV,EAAUtW,KAAKnC,KAAKwY,iBAwGxB,OAtGApY,OAAO6J,eAAesO,EAAyB1X,UAAW,UACtDkF,IAAK,WAAc,OAAO/F,KAAKgS,QAAQ9E,QACvCmO,YAAY,EACZC,cAAc,IAElB/C,EAAyB1X,UAAU2Y,cAAgB,SAAUxH,EAASuJ,GAClE,IAAInK,EAAQpR,KACZ,GAAKgS,EAAL,CAEA,IAAIwJ,EAAaxJ,EACbyJ,EAAkBzb,KAAKgS,QAEA,MAAvBwJ,EAAWpS,WACXqS,EAAgBrS,SAAWkB,EAAmBkR,EAAWpS,WAErC,MAApBoS,EAAWnS,QACXoS,EAAgBpS,MAAQiB,EAAmBkR,EAAWnS,QAE1D,IAAIqS,EAAYF,EAAWtO,OAC3B,GAAIwO,EAAW,CACX,IAAIC,EAAmBF,EAAgBvO,OAClCyO,IACDA,EAAmB3b,KAAKgS,QAAQ9E,WAEpC9M,OAAO0D,KAAK4X,GAAWjY,QAAQ,SAAUgO,GAChC8J,GAAiBI,EAAiBlb,eAAegR,KAClDkK,EAAiBlK,GAAQlE,EAAkBmO,EAAUjK,GAAOkK,EAAkBvK,EAAM/N,cAKpGkV,EAAyB1X,UAAU+a,aAAe,WAC9C,IAAI5J,KACJ,GAAIhS,KAAKgS,QAAS,CACd,IAAI6J,EAAc7b,KAAKgS,QAAQ9E,OAC/B,GAAI2O,EAAa,CACb,IAAIxJ,EAAWL,EAAgB,UAC/B5R,OAAO0D,KAAK+X,GAAapY,QAAQ,SAAUgO,GAAQY,EAASZ,GAAQoK,EAAYpK,MAGxF,OAAOO,GAEXuG,EAAyB1X,UAAUoY,iBAAmB,SAAUjH,EAAS/O,EAAS6Y,QAC9D,IAAZ9J,IAAsBA,EAAU,MACpC,IAAIrI,EAAS1G,GAAWjD,KAAKiD,QACzB2L,EAAU,IAAI2J,EAAyBvY,KAAK8Q,QAASnH,EAAQ3J,KAAKoY,gBAAiBpY,KAAKib,gBAAiBjb,KAAKkb,gBAAiBlb,KAAKqD,OAAQrD,KAAKyY,UAAWzY,KAAKwY,gBAAgBuD,KAAKpS,EAAQmS,GAAW,IAS7M,OARAlN,EAAQwK,aAAepZ,KAAKoZ,aAC5BxK,EAAQ0F,sBAAwBtU,KAAKsU,sBACrC1F,EAAQoD,QAAUhS,KAAK4b,eACvBhN,EAAQ4K,cAAcxH,GACtBpD,EAAQ+L,kBAAoB3a,KAAK2a,kBACjC/L,EAAQ6L,kBAAoBza,KAAKya,kBACjC7L,EAAQgM,cAAgB5a,KACxBA,KAAKyZ,kBACE7K,GAEX2J,EAAyB1X,UAAUsY,yBAA2B,SAAU2C,GAIpE,OAHA9b,KAAKoZ,aAAeQ,GACpB5Z,KAAKwY,gBAAkBxY,KAAKwY,gBAAgBuD,KAAK/b,KAAKiD,QAAS6Y,GAC/D9b,KAAKyY,UAAUtW,KAAKnC,KAAKwY,iBAClBxY,KAAKwY,iBAEhBD,EAAyB1X,UAAU0Y,4BAA8B,SAAUF,EAAajQ,EAAUC,GAC9F,IAAI2S,GACA5S,SAAsB,MAAZA,EAAmBA,EAAWiQ,EAAYjQ,SACpDC,MAAOrJ,KAAKwY,gBAAgBrH,aAAwB,MAAT9H,EAAgBA,EAAQ,GAAKgQ,EAAYhQ,MACpFC,OAAQ,IAER2S,EAAU,IAAIC,GAAmBlc,KAAK8Q,QAASuI,EAAYpW,QAASoW,EAAYnW,UAAWmW,EAAYpC,cAAeoC,EAAYnC,eAAgB8E,EAAgB3C,EAAY8C,yBAElL,OADAnc,KAAKyY,UAAUtW,KAAK8Z,GACbD,GAEXzD,EAAyB1X,UAAUqZ,cAAgB,SAAUkC,GACzDpc,KAAKwY,gBAAgB+B,YAAYva,KAAKwY,gBAAgBpP,SAAWgT,IAErE7D,EAAyB1X,UAAUgZ,cAAgB,SAAUxQ,GAErDA,EAAQ,GACRrJ,KAAKwY,gBAAgBqB,cAAcxQ,IAG3CkP,EAAyB1X,UAAU6H,YAAc,SAAU/B,EAAUkQ,EAAkBb,EAAOW,EAAaC,EAAUvT,GACjH,IAAIoE,KAIJ,GAHIkP,GACAlP,EAAQtF,KAAKnC,KAAKiD,SAElB0D,EAASrF,OAAS,EAAG,CAErBqF,GADAA,EAAWA,EAASoC,QAAQ6O,GAAmB,IAAM5X,KAAKib,kBACtClS,QAAQ8O,GAAmB,IAAM7X,KAAKkb,iBAC1D,IACImB,EAAWrc,KAAK8Q,QAAQ7H,MAAMjJ,KAAKiD,QAAS0D,EAD3B,GAATqP,GAEE,IAAVA,IACAqG,EAAWrG,EAAQ,EAAIqG,EAASC,MAAMD,EAAS/a,OAAS0U,EAAOqG,EAAS/a,QACpE+a,EAASC,MAAM,EAAGtG,IAE1BvO,EAAQtF,KAAKX,MAAMiG,EAASnF,EAAS+Z,IAKzC,OAHKzF,GAA8B,GAAlBnP,EAAQnG,QACrB+B,EAAOlB,KAAK,WAAc0U,EAAmB,4CAAgDA,EAAmB,uDAE7GpP,GAEJ8Q,EA1HkC,GA4HzC6C,GAAiC,WACjC,SAASA,EAAgBtK,EAAS7N,EAASmS,EAAWmH,GAClDvc,KAAK8Q,QAAUA,EACf9Q,KAAKiD,QAAUA,EACfjD,KAAKoV,UAAYA,EACjBpV,KAAKuc,6BAA+BA,EACpCvc,KAAKoJ,SAAW,EAChBpJ,KAAKwc,qBACLxc,KAAKyc,oBACLzc,KAAK0c,WAAa,IAAI5W,IACtB9F,KAAK2c,iBACL3c,KAAK4c,kBACL5c,KAAK6c,aACL7c,KAAK8c,0BAA4B,KAC5B9c,KAAKuc,+BACNvc,KAAKuc,6BAA+B,IAAIzW,KAE5C9F,KAAK+c,qBAAuB3c,OAAOU,OAAOd,KAAK6c,cAC/C7c,KAAKgd,sBAAwBhd,KAAKuc,6BAA6BxW,IAAI9C,GAC9DjD,KAAKgd,wBACNhd,KAAKgd,sBAAwBhd,KAAK+c,qBAClC/c,KAAKuc,6BAA6BvW,IAAI/C,EAASjD,KAAK+c,uBAExD/c,KAAKid,gBAoLT,OAlLA7B,EAAgBva,UAAU+X,kBAAoB,WAC1C,OAAQ5Y,KAAK0c,WAAWhK,MACpB,KAAK,EACD,OAAO,EACX,KAAK,EACD,OAAO1S,KAAKma,4BAA4B7Y,OAAS,EACrD,QACI,OAAO,IAGnB8Z,EAAgBva,UAAUsZ,0BAA4B,WAAc,OAAO/Z,OAAO0D,KAAK9D,KAAKyc,mBAC5Frc,OAAO6J,eAAemR,EAAgBva,UAAW,eAC7CkF,IAAK,WAAc,OAAO/F,KAAKoV,UAAYpV,KAAKoJ,UAChDiS,YAAY,EACZC,cAAc,IAElBF,EAAgBva,UAAUgZ,cAAgB,SAAUxQ,GAKhD,IAAI6T,EAA0C,GAAxBld,KAAK0c,WAAWhK,MAAatS,OAAO0D,KAAK9D,KAAK4c,gBAAgBtb,OAChFtB,KAAKoJ,UAAY8T,GACjBld,KAAKua,YAAYva,KAAKmR,YAAc9H,GAChC6T,GACAld,KAAK2Z,yBAIT3Z,KAAKoV,WAAa/L,GAG1B+R,EAAgBva,UAAUkb,KAAO,SAAU9Y,EAASkO,GAEhD,OADAnR,KAAK8Z,wBACE,IAAIsB,EAAgBpb,KAAK8Q,QAAS7N,EAASkO,GAAenR,KAAKmR,YAAanR,KAAKuc,+BAE5FnB,EAAgBva,UAAUoc,cAAgB,WAClCjd,KAAKyc,mBACLzc,KAAKwc,kBAAoBxc,KAAKyc,kBAElCzc,KAAKyc,iBAAmBzc,KAAK0c,WAAW3W,IAAI/F,KAAKoJ,UAC5CpJ,KAAKyc,mBACNzc,KAAKyc,iBAAmBrc,OAAOU,OAAOd,KAAK6c,cAC3C7c,KAAK0c,WAAW1W,IAAIhG,KAAKoJ,SAAUpJ,KAAKyc,oBAGhDrB,EAAgBva,UAAUuZ,aAAe,WACrCpa,KAAKoJ,UA5iBmB,EA6iBxBpJ,KAAKid,iBAET7B,EAAgBva,UAAU0Z,YAAc,SAAU6B,GAC9Cpc,KAAK8Z,wBACL9Z,KAAKoJ,SAAWgT,EAChBpc,KAAKid,iBAET7B,EAAgBva,UAAUsc,aAAe,SAAUpZ,EAAM3B,GACrDpC,KAAK+c,qBAAqBhZ,GAAQ3B,EAClCpC,KAAKgd,sBAAsBjZ,GAAQ3B,EACnCpC,KAAK2c,cAAc5Y,IAAUqY,KAAMpc,KAAKmR,YAAa/O,MAAOA,IAEhEgZ,EAAgBva,UAAUiY,wBAA0B,WAAc,OAAO9Y,KAAK8c,4BAA8B9c,KAAKyc,kBACjHrB,EAAgBva,UAAUwZ,eAAiB,SAAU/Q,GACjD,IAAI8H,EAAQpR,KACRsJ,IACAtJ,KAAKwc,kBAA0B,OAAIlT,GAQvClJ,OAAO0D,KAAK9D,KAAKgd,uBAAuBvZ,QAAQ,SAAUM,GACtDqN,EAAMyL,UAAU9Y,GAAQqN,EAAM4L,sBAAsBjZ,IAASlE,EAAWuE,WACxEgN,EAAMqL,iBAAiB1Y,GAAQlE,EAAWuE,aAE9CpE,KAAK8c,0BAA4B9c,KAAKyc,kBAE1CrB,EAAgBva,UAAUyL,UAAY,SAAU0B,EAAO1E,EAAQjG,EAAQ2O,GACnE,IAAIZ,EAAQpR,KACRsJ,IACAtJ,KAAKwc,kBAA0B,OAAIlT,GAEvC,IAAI4D,EAAU8E,GAAWA,EAAQ9E,WAC7BzB,EAkKZ,SAAS2R,EAAcpP,EAAOqP,GAC1B,IACIC,EADA7R,KAWJ,OATAuC,EAAMvK,QAAQ,SAAUgT,GACN,MAAVA,GACA6G,EAAgBA,GAAiBld,OAAO0D,KAAKuZ,IAC/B5Z,QAAQ,SAAUM,GAAQ0H,EAAO1H,GAAQlE,EAAWuE,aAGlEwH,EAAW6K,GAAO,EAAOhL,KAG1BA,EA9KU2R,CAAcpP,EAAOhO,KAAKgd,uBACvC5c,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAClC,IAAIoJ,EAAMI,EAAkB9B,EAAO1H,GAAOmJ,EAAQ7J,GAClD+N,EAAMwL,eAAe7Y,GAAQoJ,EACxBiE,EAAM2L,qBAAqBtc,eAAesD,KAC3CqN,EAAMyL,UAAU9Y,GAAQqN,EAAM4L,sBAAsBvc,eAAesD,GAC/DqN,EAAM4L,sBAAsBjZ,GAC5BlE,EAAWuE,YAEnBgN,EAAM+L,aAAapZ,EAAMoJ,MAGjCiO,EAAgBva,UAAUiZ,sBAAwB,WAC9C,IAAI1I,EAAQpR,KACRyL,EAASzL,KAAK4c,eACdW,EAAQnd,OAAO0D,KAAK2H,GACJ,GAAhB8R,EAAMjc,SAEVtB,KAAK4c,kBACLW,EAAM9Z,QAAQ,SAAUM,GAEpBqN,EAAMqL,iBAAiB1Y,GADb0H,EAAO1H,KAGrB3D,OAAO0D,KAAK9D,KAAK+c,sBAAsBtZ,QAAQ,SAAUM,GAChDqN,EAAMqL,iBAAiBhc,eAAesD,KACvCqN,EAAMqL,iBAAiB1Y,GAAQqN,EAAM2L,qBAAqBhZ,QAItEqX,EAAgBva,UAAU8Y,sBAAwB,WAC9C,IAAIvI,EAAQpR,KACZI,OAAO0D,KAAK9D,KAAK+c,sBAAsBtZ,QAAQ,SAAUM,GACrD,IAAIoJ,EAAMiE,EAAM2L,qBAAqBhZ,GACrCqN,EAAMwL,eAAe7Y,GAAQoJ,EAC7BiE,EAAM+L,aAAapZ,EAAMoJ,MAGjCiO,EAAgBva,UAAU2c,iBAAmB,WAAc,OAAOxd,KAAK0c,WAAW3W,IAAI/F,KAAKoJ,WAC3FhJ,OAAO6J,eAAemR,EAAgBva,UAAW,cAC7CkF,IAAK,WACD,IAAI0X,KACJ,IAAK,IAAI1Z,KAAQ/D,KAAKyc,iBAClBgB,EAAWtb,KAAK4B,GAEpB,OAAO0Z,GAEXpC,YAAY,EACZC,cAAc,IAElBF,EAAgBva,UAAUmZ,6BAA+B,SAAUrB,GAC/D,IAAIvH,EAAQpR,KACZI,OAAO0D,KAAK6U,EAASgE,eAAelZ,QAAQ,SAAUM,GAClD,IAAI2Z,EAAWtM,EAAMuL,cAAc5Y,GAC/B4Z,EAAWhF,EAASgE,cAAc5Y,KACjC2Z,GAAYC,EAASvB,KAAOsB,EAAStB,OACtChL,EAAM+L,aAAapZ,EAAM4Z,EAASvb,UAI9CgZ,EAAgBva,UAAUyX,eAAiB,WACvC,IAAIlH,EAAQpR,KACZA,KAAK8Z,wBACL,IAAI7C,EAAgB,IAAIlH,IACpBmH,EAAiB,IAAInH,IACrB0E,EAAmC,IAAzBzU,KAAK0c,WAAWhK,MAAgC,IAAlB1S,KAAKoJ,SAC7CwU,KACJ5d,KAAK0c,WAAWjZ,QAAQ,SAAUoa,EAAUzB,GACxC,IAAI0B,EAAgBlS,EAAWiS,GAAU,GACzCzd,OAAO0D,KAAKga,GAAera,QAAQ,SAAUM,GACzC,IAAI3B,EAAQ0b,EAAc/Z,GACtB3B,GAASvC,EAAWsE,WACpB8S,EAAcxE,IAAI1O,GAEb3B,GAASvC,EAAWuE,YACzB8S,EAAezE,IAAI1O,KAGtB0Q,IACDqJ,EAAsB,OAAI1B,EAAOhL,EAAMhI,UAE3CwU,EAAezb,KAAK2b,KAExB,IAAIC,EAAW9G,EAAcvE,KAAO7E,EAAgBoJ,EAAcrE,aAC9DoL,EAAY9G,EAAexE,KAAO7E,EAAgBqJ,EAAetE,aAErE,GAAI6B,EAAS,CACT,IAAIwJ,EAAML,EAAe,GACrBM,EAAM7S,EAAQ4S,GAClBA,EAAY,OAAI,EAChBC,EAAY,OAAI,EAChBN,GAAkBK,EAAKC,GAE3B,OAAOlH,GAA0BhX,KAAKiD,QAAS2a,EAAgBG,EAAUC,EAAWhe,KAAKoJ,SAAUpJ,KAAKoV,UAAWpV,KAAKsJ,QAAQ,IAE7H8R,EA3MyB,GA6MhCc,GAAoC,SAAUiC,GAE9C,SAASjC,EAAmBnZ,EAAQE,EAASC,EAAW+T,EAAeC,EAAgBtM,EAASwT,QAC3D,IAA7BA,IAAuCA,GAA2B,GACtE,IAAIhN,EAAQ+M,EAAO5c,KAAKvB,KAAM+C,EAAQE,EAAS2H,EAAQvB,QAAUrJ,KAOjE,OANAoR,EAAMnO,QAAUA,EAChBmO,EAAMlO,UAAYA,EAClBkO,EAAM6F,cAAgBA,EACtB7F,EAAM8F,eAAiBA,EACvB9F,EAAMgN,yBAA2BA,EACjChN,EAAMxG,SAAYxB,SAAUwB,EAAQxB,SAAUC,MAAOuB,EAAQvB,MAAOC,OAAQsB,EAAQtB,QAC7E8H,EAgDX,OA1DA1Q,EAAUwb,EAAoBiC,GAY9BjC,EAAmBrb,UAAU+X,kBAAoB,WAAc,OAAO5Y,KAAKkD,UAAU5B,OAAS,GAC9F4a,EAAmBrb,UAAUyX,eAAiB,WAC1C,IAAIpV,EAAYlD,KAAKkD,UACjBmT,EAAKrW,KAAK4K,QAASvB,EAAQgN,EAAGhN,MAAOD,EAAWiN,EAAGjN,SAAUE,EAAS+M,EAAG/M,OAC7E,GAAItJ,KAAKoe,0BAA4B/U,EAAO,CACxC,IAAIgV,KACAnZ,EAAYkE,EAAWC,EACvBiV,EAAcjV,EAAQnE,EAEtBqZ,EAAmB3S,EAAW1I,EAAU,IAAI,GAChDqb,EAAyB,OAAI,EAC7BF,EAAalc,KAAKoc,GAClB,IAAIC,EAAmB5S,EAAW1I,EAAU,IAAI,GAChDsb,EAAyB,OAAIC,GAAYH,GACzCD,EAAalc,KAAKqc,GAiBlB,IADA,IAAIxI,EAAQ9S,EAAU5B,OAAS,EACtBH,EAAI,EAAGA,GAAK6U,EAAO7U,IAAK,CAC7B,IAAIuC,EAAKkI,EAAW1I,EAAU/B,IAAI,GAGlCuC,EAAW,OAAI+a,IADMpV,EADL3F,EAAW,OACc0F,GACGlE,GAC5CmZ,EAAalc,KAAKuB,GAGtB0F,EAAWlE,EACXmE,EAAQ,EACRC,EAAS,GACTpG,EAAYmb,EAEhB,OAAOrH,GAA0BhX,KAAKiD,QAASC,EAAWlD,KAAKiX,cAAejX,KAAKkX,eAAgB9N,EAAUC,EAAOC,GAAQ,IAEzH4S,EA3D4B,CA4DrCd,IACF,SAASqD,GAAY9a,EAAQ+a,QACH,IAAlBA,IAA4BA,EAAgB,GAChD,IAAIC,EAAO/K,KAAKgL,IAAI,GAAIF,EAAgB,GACxC,OAAO9K,KAAKiL,MAAMlb,EAASgb,GAAQA,EAiBvC,IAAIG,GAA2B,WAC3B,SAASA,EAAUhO,EAAS9C,GACxBhO,KAAK8Q,QAAUA,EACf,IAAIzN,KACA+Q,EAAM1D,GAAkBI,EAAS9C,EAAO3K,GAC5C,GAAIA,EAAO/B,OAAQ,CACf,IAAIyd,EAAe,iCAAmC1b,EAAOkB,KAAK,MAClE,MAAM,IAAID,MAAMya,GAEpB/e,KAAKgf,cAAgB5K,EAgBzB,OAdA0K,EAAUje,UAAUoe,eAAiB,SAAUhc,EAASiV,EAAgBgH,EAAmBlN,EAASoG,GAChG,IAAI+G,EAAQ5e,MAAMoL,QAAQuM,GAAkB1M,EAAgB0M,GACxDA,EACAkH,EAAO7e,MAAMoL,QAAQuT,GAAqB1T,EAAgB0T,GAC1DA,EACA7b,KACJ+U,EAAkBA,GAAmB,IAAIhB,GACzC,IAAIhP,EAAS0P,GAAwB9X,KAAK8Q,QAAS7N,EAASjD,KAAKgf,cAnpDnD,WACA,WAkpDoGG,EAAOC,EAAMpN,EAASoG,EAAiB/U,GACzJ,GAAIA,EAAO/B,OAAQ,CACf,IAAIyd,EAAe,+BAAiC1b,EAAOkB,KAAK,MAChE,MAAM,IAAID,MAAMya,GAEpB,OAAO3W,GAEJ0W,EAzBmB,GAsC1BO,GACA,SAASA,OAOTC,GAA8C,WAC9C,SAASA,KAMT,OAJAA,EAA6Bze,UAAUqD,sBAAwB,SAAUqb,EAAclc,GAAU,OAAOkc,GACxGD,EAA6Bze,UAAUwD,oBAAsB,SAAUmb,EAAsBC,EAAoBrd,EAAOiB,GACpH,OAAOjB,GAEJkd,EAPsC,GAU7CI,GAA8C,SAAUvB,GAExD,SAASuB,IACL,OAAkB,OAAXvB,GAAmBA,EAAO3c,MAAMxB,KAAMqB,YAAcrB,KAqB/D,OAvBAU,EAAUgf,EAA8BvB,GAIxCuB,EAA6B7e,UAAUqD,sBAAwB,SAAUqb,EAAclc,GACnF,OAAOoJ,EAAoB8S,IAE/BG,EAA6B7e,UAAUwD,oBAAsB,SAAUmb,EAAsBC,EAAoBrd,EAAOiB,GACpH,IAAIqH,EAAO,GACPiV,EAASvd,EAAMgL,WAAWwS,OAC9B,GAAIC,GAAqBJ,IAAiC,IAAVrd,GAAyB,MAAVA,EAC3D,GAAqB,iBAAVA,EACPsI,EAAO,SAEN,CACD,IAAIoV,EAAoB1d,EAAMmI,MAAM,0BAChCuV,GAAoD,GAA/BA,EAAkB,GAAGxe,QAC1C+B,EAAOlB,KAAK,uCAAyCqd,EAAuB,IAAMpd,GAI9F,OAAOud,EAASjV,GAEbgV,EAxBsC,CAyB/CL,IACEQ,GAEJ,SAASE,GAAejc,GACpB,IAAI6B,KAEJ,OADA7B,EAAKL,QAAQ,SAAUmC,GAAO,OAAOD,EAAIC,IAAO,IACzCD,EALgBoa,CAAe,iUACrCjO,MAAM;;;;;;;OAOX,SAASkO,GAA4B/c,EAASmC,EAAaC,EAAWC,EAAS2a,EAAqBC,EAAYC,EAAU1H,EAAW2H,EAAiBnJ,EAAeC,EAAgBhS,EAAW7B,GAC5L,OACIwL,KAAM,EACN5L,QAASA,EACTmC,YAAaA,EACb6a,oBAAqBA,EACrB5a,UAAWA,EACX6a,WAAYA,EACZ5a,QAASA,EACT6a,SAAUA,EACV1H,UAAWA,EACX2H,gBAAiBA,EACjBnJ,cAAeA,EACfC,eAAgBA,EAChBhS,UAAWA,EACX7B,OAAQA,GAIhB,IAAIgd,MACAC,GAA4C,WAC5C,SAASA,EAA2BC,EAAcnM,EAAKoM,GACnDxgB,KAAKugB,aAAeA,EACpBvgB,KAAKoU,IAAMA,EACXpU,KAAKwgB,aAAeA,EA0CxB,OAxCAF,EAA2Bzf,UAAU0J,MAAQ,SAAUkW,EAAcC,EAAWzd,EAASiK,GACrF,OAyCR,SAASyT,EAA0BC,EAAUH,EAAcC,EAAWzd,EAASiK,GAC3E,OAAO0T,EAAS1M,KAAK,SAAU2M,GAAM,OAAOA,EAAGJ,EAAcC,EAAWzd,EAASiK,KA1CtEyT,CAA0B3gB,KAAKoU,IAAItB,SAAU2N,EAAcC,EAAWzd,EAASiK,IAE1FoT,EAA2Bzf,UAAUigB,YAAc,SAAUC,EAAW7T,EAAQ7J,GAC5E,IAAI2d,EAAoBhhB,KAAKwgB,aAAa,KACtCS,EAAcjhB,KAAKwgB,aAAaO,GAChCG,EAAeF,EAAoBA,EAAkBF,YAAY5T,EAAQ7J,MAC7E,OAAO4d,EAAcA,EAAYH,YAAY5T,EAAQ7J,GAAU6d,GAEnEZ,EAA2Bzf,UAAUgQ,MAAQ,SAAU9N,EAAQE,EAASwd,EAAcC,EAAW1I,EAAgBC,EAAgBkJ,EAAgBC,EAAahJ,EAAiBiJ,GAC3K,IAAIhe,KACAie,EAA4BthB,KAAKoU,IAAIpC,SAAWhS,KAAKoU,IAAIpC,QAAQ9E,QAAUmT,GAE3EkB,EAAqBvhB,KAAK8gB,YAAYL,EADbU,GAAkBA,EAAejU,QAAUmT,GACQhd,GAC5Eme,EAAsBJ,GAAeA,EAAYlU,QAAUmT,GAC3DoB,EAAkBzhB,KAAK8gB,YAAYJ,EAAWc,EAAqBne,GACnE+c,EAAkB,IAAIrQ,IACtB2R,EAAc,IAAI5b,IAClB6b,EAAe,IAAI7b,IACnB8b,EAA0B,SAAdlB,EACZmB,GAAqB3U,OAAQnM,KAAaugB,EAA2BE,IACrE/I,EAAY4I,KAAoBvJ,GAAwB/U,EAAQE,EAASjD,KAAKoU,IAAIvB,UAAWmF,EAAgBC,EAAgBsJ,EAAoBE,EAAiBI,EAAkBzJ,EAAiB/U,GACrM6B,EAAY,EAEhB,GADAuT,EAAUhV,QAAQ,SAAUoV,GAAM3T,EAAY0O,KAAKC,IAAIgF,EAAGzP,SAAWyP,EAAGxP,MAAOnE,KAC3E7B,EAAO/B,OACP,OAAO0e,GAA4B/c,EAASjD,KAAKugB,aAAcE,EAAcC,EAAWkB,EAAWL,EAAoBE,QAAyBC,EAAaC,EAAczc,EAAW7B,GAE1LoV,EAAUhV,QAAQ,SAAUoV,GACxB,IAAIlR,EAAMkR,EAAG5V,QACT8a,EAAWrY,EAAgBgc,EAAa/Z,MAC5CkR,EAAG5B,cAAcxT,QAAQ,SAAUM,GAAQ,OAAOga,EAASha,IAAQ,IACnE,IAAIia,EAAYtY,EAAgBic,EAAcha,MAC9CkR,EAAG3B,eAAezT,QAAQ,SAAUM,GAAQ,OAAOia,EAAUja,IAAQ,IACjE4D,IAAQ1E,GACRmd,EAAgB3N,IAAI9K,KAG5B,IAAIma,EAAsBjU,EAAgBuS,EAAgBxN,UAC1D,OAAOoN,GAA4B/c,EAASjD,KAAKugB,aAAcE,EAAcC,EAAWkB,EAAWL,EAAoBE,EAAiBhJ,EAAWqJ,EAAqBJ,EAAaC,EAAczc,IAEhMob,EA9CoC,GAmD3CyB,GAAsC,WACtC,SAASA,EAAqBtW,EAAQuW,GAClChiB,KAAKyL,OAASA,EACdzL,KAAKgiB,cAAgBA,EAyBzB,OAvBAD,EAAqBlhB,UAAUigB,YAAc,SAAU5T,EAAQ7J,GAC3D,IAAI8U,KACA8J,EAAiB5W,EAAQrL,KAAKgiB,eAmBlC,OAlBA5hB,OAAO0D,KAAKoJ,GAAQzJ,QAAQ,SAAUmC,GAClC,IAAIxD,EAAQ8K,EAAOtH,GACN,MAATxD,IACA6f,EAAerc,GAAOxD,KAG9BpC,KAAKyL,OAAOA,OAAOhI,QAAQ,SAAUrB,GACjC,GAAqB,iBAAVA,EAAoB,CAC3B,IAAI8f,EAAa9f,EACjBhC,OAAO0D,KAAKoe,GAAYze,QAAQ,SAAUM,GACtC,IAAIoJ,EAAM+U,EAAWne,GACjBoJ,EAAI7L,OAAS,IACb6L,EAAMI,EAAkBJ,EAAK8U,EAAgB5e,IAEjD8U,EAAYpU,GAAQoJ,OAIzBgL,GAEJ4J,EA5B8B,GAwCrCI,GAAkC,WAClC,SAASA,EAAiB1Q,EAAM2C,GAC5B,IAAIhD,EAAQpR,KACZA,KAAKyR,KAAOA,EACZzR,KAAKoU,IAAMA,EACXpU,KAAKoiB,uBACLpiB,KAAKuR,UACL6C,EAAI7C,OAAO9N,QAAQ,SAAU2Q,GAEzBhD,EAAMG,OAAO6C,EAAI3C,MAAQ,IAAIsQ,GAAqB3N,EAAIjM,MADjCiM,EAAIpC,SAAWoC,EAAIpC,QAAQ9E,cAGpDmV,GAAkBriB,KAAKuR,OAAQ,OAAQ,KACvC8Q,GAAkBriB,KAAKuR,OAAQ,QAAS,KACxC6C,EAAI5C,YAAY/N,QAAQ,SAAU2Q,GAC9BhD,EAAMgR,oBAAoBjgB,KAAK,IAAIme,GAA2B7O,EAAM2C,EAAKhD,EAAMG,WAEnFvR,KAAKsiB,mBAgBb,SAASC,EAAyBnd,EAAamM,GAW3C,OAAO,IAAI+O,GAA2Blb,GAPlCyJ,KAAM,EACNgE,WAHchE,KAAM,EAAkBjC,SAAWoF,QAAS,MAI1Dc,UALY,SAAUzN,EAAWC,GAAW,OAAO,IAMnD0M,QAAS,KACTX,WAAY,EACZC,SAAU,GAEiDC,GA3BjCgR,CAAyB9Q,EAAMzR,KAAKuR,QAclE,OAZAnR,OAAO6J,eAAekY,EAAiBthB,UAAW,mBAC9CkF,IAAK,WAAc,OAAO/F,KAAKoU,IAAI/C,WAAa,GAChDgK,YAAY,EACZC,cAAc,IAElB6G,EAAiBthB,UAAU2hB,gBAAkB,SAAU/B,EAAcC,EAAWzd,EAASiK,GAErF,OADYlN,KAAKoiB,oBAAoB5L,KAAK,SAAUiM,GAAK,OAAOA,EAAElY,MAAMkW,EAAcC,EAAWzd,EAASiK,MAC1F,MAEpBiV,EAAiBthB,UAAU6hB,YAAc,SAAUjC,EAAcvT,EAAQ7J,GACrE,OAAOrD,KAAKsiB,mBAAmBxB,YAAYL,EAAcvT,EAAQ7J,IAE9D8e,EA9B0B,GA6CrC,SAASE,GAAkB/W,EAAKqX,EAAMC,GAC9BtX,EAAI7K,eAAekiB,GACdrX,EAAI7K,eAAemiB,KACpBtX,EAAIsX,GAAQtX,EAAIqX,IAGfrX,EAAI7K,eAAemiB,KACxBtX,EAAIqX,GAAQrX,EAAIsX;;;;;;;OAWxB,IAAIC,GAAwB,IAAIzL,GAC5B0L,GAAyC,WACzC,SAASA,EAAwBC,EAAUjS,EAASkS,GAChDhjB,KAAK+iB,SAAWA,EAChB/iB,KAAK8Q,QAAUA,EACf9Q,KAAKgjB,YAAcA,EACnBhjB,KAAKijB,eACLjjB,KAAKkjB,gBACLljB,KAAK2C,WA+GT,OA7GAmgB,EAAwBjiB,UAAUsiB,SAAW,SAAUC,EAAIzS,GACvD,IAAItN,KACA+Q,EAAM1D,GAAkB1Q,KAAK8Q,QAASH,EAAUtN,GACpD,GAAIA,EAAO/B,OACP,MAAM,IAAIgD,MAAM,8DAAgEjB,EAAOkB,KAAK,OAG5FvE,KAAKijB,YAAYG,GAAMhP,GAG/B0O,EAAwBjiB,UAAUwiB,aAAe,SAAUliB,EAAGgC,EAAWC,GACrE,IAAIH,EAAU9B,EAAE8B,QACZC,EAAYJ,EAAmB9C,EAAcA,KAAKgjB,YAAa/f,EAAS9B,EAAE+B,UAAWC,EAAWC,GACpG,OAAOpD,KAAK8Q,QAAQ3H,QAAQlG,EAASC,EAAW/B,EAAEiI,SAAUjI,EAAEkI,MAAOlI,EAAEmI,WAAY,IAEvFwZ,EAAwBjiB,UAAUC,OAAS,SAAUsiB,EAAIngB,EAAS+O,GAC9D,IAAIZ,EAAQpR,UACI,IAAZgS,IAAsBA,MAC1B,IAEIuF,EAFAlU,KACA+Q,EAAMpU,KAAKijB,YAAYG,GAEvBE,EAAgB,IAAIxd,IAYxB,GAXIsO,GACAmD,EAAeO,GAAwB9X,KAAK8Q,QAAS7N,EAASmR,EA16DpD,WACA,iBAy6DmGpC,EAAS6Q,GAAuBxf,IAChII,QAAQ,SAAU8f,GAC3B,IAAI9X,EAAS/F,EAAgB4d,EAAeC,EAAKtgB,YACjDsgB,EAAKrM,eAAezT,QAAQ,SAAUM,GAAQ,OAAO0H,EAAO1H,GAAQ,UAIxEV,EAAOlB,KAAK,uEACZoV,MAEAlU,EAAO/B,OACP,MAAM,IAAIgD,MAAM,+DAAiEjB,EAAOkB,KAAK,OAEjG+e,EAAc7f,QAAQ,SAAUgI,EAAQxI,GACpC7C,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAAQ0H,EAAO1H,GAAQqN,EAAMN,QAAQ5H,aAAajG,EAASc,EAAMlE,EAAWuE,gBAEtH,IAIIK,EAAS/B,EAJC6U,EAAa5R,IAAI,SAAUxE,GACrC,IAAIsK,EAAS6X,EAAcvd,IAAI5E,EAAE8B,SACjC,OAAOmO,EAAMiS,aAAaliB,KAAOsK,MAMrC,OAHAzL,KAAKkjB,aAAaE,GAAM3e,EACxBA,EAAOO,UAAU,WAAc,OAAOoM,EAAMoS,QAAQJ,KACpDpjB,KAAK2C,QAAQR,KAAKsC,GACXA,GAEXqe,EAAwBjiB,UAAU2iB,QAAU,SAAUJ,GAClD,IAAI3e,EAASzE,KAAKyjB,WAAWL,GAC7B3e,EAAO+e,iBACAxjB,KAAKkjB,aAAaE,GACzB,IAAIM,EAAQ1jB,KAAK2C,QAAQyD,QAAQ3B,GAC7Bif,GAAS,GACT1jB,KAAK2C,QAAQyI,OAAOsY,EAAO,IAGnCZ,EAAwBjiB,UAAU4iB,WAAa,SAAUL,GACrD,IAAI3e,EAASzE,KAAKkjB,aAAaE,GAC/B,IAAK3e,EACD,MAAM,IAAIH,MAAM,oDAAsD8e,GAE1E,OAAO3e,GAEXqe,EAAwBjiB,UAAU8iB,OAAS,SAAUP,EAAIngB,EAASyB,EAAWE,GAEzE,IAAIgf,EAAYze,EAAmBlC,EAAS,GAAI,GAAI,IAEpD,OADAuB,EAAexE,KAAKyjB,WAAWL,GAAK1e,EAAWkf,EAAWhf,GACnD,cAEXke,EAAwBjiB,UAAUqF,QAAU,SAAUkd,EAAIngB,EAASiD,EAAS2d,GACxE,GAAe,YAAX3d,EAIJ,GAAe,UAAXA,EAAJ,CAKA,IAAIzB,EAASzE,KAAKyjB,WAAWL,GAC7B,OAAQld,GACJ,IAAK,OACDzB,EAAOqf,OACP,MACJ,IAAK,QACDrf,EAAOsf,QACP,MACJ,IAAK,QACDtf,EAAOuf,QACP,MACJ,IAAK,UACDvf,EAAOwf,UACP,MACJ,IAAK,SACDxf,EAAOyf,SACP,MACJ,IAAK,OACDzf,EAAO0f,OACP,MACJ,IAAK,cACD1f,EAAO2f,YAAY3Z,WAAWoZ,EAAK,KACnC,MACJ,IAAK,UACD7jB,KAAKwjB,QAAQJ,SA3BjBpjB,KAAKc,OAAOsiB,EAAIngB,EADD4gB,EAAK,aAJpB7jB,KAAKmjB,SAASC,EAAIS,EAAK,KAoCxBf,EAtHiC,GA+HxCuB,MACAC,IACAC,YAAa,GACbC,eAAe,EACfC,YAAY,EACZC,cAAc,EACdC,sBAAsB,GAEtBC,IACAL,YAAa,GACbE,YAAY,EACZD,eAAe,EACfE,cAAc,EACdC,sBAAsB,GAEtBE,GAAe,eACfC,GAA4B,WAC5B,SAASA,EAAW9W,EAAOuW,QACH,IAAhBA,IAA0BA,EAAc,IAC5CvkB,KAAKukB,YAAcA,EACnB,IAAIQ,EAAQ/W,GAASA,EAAMvN,eAAe,SAG1C,GADAT,KAAKoC,MAiyCb,SAAS4iB,EAAsB5iB,GAI3B,OAAgB,MAATA,EAAgBA,EAAQ,KAryCd4iB,CADDD,EAAQ/W,EAAa,MAAIA,GAEjC+W,EAAO,CACP,IAAI/S,EAAU3G,EAAQ2C,UACfgE,EAAe,MACtBhS,KAAKgS,QAAUA,OAGfhS,KAAKgS,WAEJhS,KAAKgS,QAAQ9E,SACdlN,KAAKgS,QAAQ9E,WAmBrB,OAhBA9M,OAAO6J,eAAe6a,EAAWjkB,UAAW,UACxCkF,IAAK,WAAc,OAAO/F,KAAKgS,QAAQ9E,QACvCmO,YAAY,EACZC,cAAc,IAElBwJ,EAAWjkB,UAAUokB,cAAgB,SAAUjT,GAC3C,IAAI0J,EAAY1J,EAAQ9E,OACxB,GAAIwO,EAAW,CACX,IAAIG,EAAc7b,KAAKgS,QAAQ9E,OAC/B9M,OAAO0D,KAAK4X,GAAWjY,QAAQ,SAAUM,GACZ,MAArB8X,EAAY9X,KACZ8X,EAAY9X,GAAQ2X,EAAU3X,QAKvC+gB,EAnCoB,GAsC3BI,GAAsB,IAAIJ,GADb,QAEbK,GAA8C,WAC9C,SAASA,EAA6B/B,EAAIgC,EAAaC,GACnDrlB,KAAKojB,GAAKA,EACVpjB,KAAKolB,YAAcA,EACnBplB,KAAKqlB,QAAUA,EACfrlB,KAAK2C,WACL3C,KAAKslB,aACLtlB,KAAKulB,UACLvlB,KAAKwlB,kBAAoB,IAAI1f,IAC7B9F,KAAKylB,eAAiB,UAAYrC,EAClCsC,GAASN,EAAaplB,KAAKylB,gBAiV/B,OA/UAN,EAA6BtkB,UAAU8iB,OAAS,SAAU1gB,EAASwO,EAAMkU,EAAO/gB,GAC5E,IAAIwM,EAAQpR,KACZ,IAAKA,KAAKslB,UAAU7kB,eAAegR,GAC/B,MAAM,IAAInN,MAAM,oDAAuDqhB,EAAQ,oCAAwClU,EAAO,qBAElI,GAAa,MAATkU,GAAiC,GAAhBA,EAAMrkB,OACvB,MAAM,IAAIgD,MAAM,8CAAiDmN,EAAO,8CAE5E,IAqvCR,SAASmU,EAAoBlhB,GACzB,MAAoB,SAAbA,GAAqC,QAAbA,EAtvCtBkhB,CAAoBD,GACrB,MAAM,IAAIrhB,MAAM,yCAA4CqhB,EAAQ,gCAAoClU,EAAO,uBAEnH,IAAIoU,EAAYngB,EAAgB1F,KAAKwlB,kBAAmBviB,MACpDwC,GAASgM,KAAMA,EAAMkU,MAAOA,EAAO/gB,SAAUA,GACjDihB,EAAU1jB,KAAKsD,GACf,IAAIqgB,EAAqBpgB,EAAgB1F,KAAKqlB,QAAQU,gBAAiB9iB,MAMvE,OALK6iB,EAAmBrlB,eAAegR,KACnCiU,GAASziB,EA1lEM,cA2lEfyiB,GAASziB,EAAS+iB,cAA6BvU,GAC/CqU,EAAmBrU,GAAQyT,IAExB,WAIH9T,EAAMiU,QAAQY,WAAW,WACrB,IAAIvC,EAAQmC,EAAUzf,QAAQX,GAC1Bie,GAAS,GACTmC,EAAUza,OAAOsY,EAAO,GAEvBtS,EAAMkU,UAAU7T,WACVqU,EAAmBrU,OAK1C0T,EAA6BtkB,UAAUsiB,SAAW,SAAU1R,EAAM2C,GAC9D,OAAIpU,KAAKslB,UAAU7T,KAKfzR,KAAKslB,UAAU7T,GAAQ2C,GAChB,IAGf+Q,EAA6BtkB,UAAUqlB,YAAc,SAAUzU,GAC3D,IAAI0U,EAAUnmB,KAAKslB,UAAU7T,GAC7B,IAAK0U,EACD,MAAM,IAAI7hB,MAAM,mCAAsCmN,EAAO,8BAEjE,OAAO0U,GAEXhB,EAA6BtkB,UAAUslB,QAAU,SAAUljB,EAASmC,EAAahD,EAAOgkB,GACpF,IAAIhV,EAAQpR,UACc,IAAtBomB,IAAgCA,GAAoB,GACxD,IAAID,EAAUnmB,KAAKkmB,YAAY9gB,GAC3BX,EAAS,IAAI4hB,GAA0BrmB,KAAKojB,GAAIhe,EAAanC,GAC7D6iB,EAAqB9lB,KAAKqlB,QAAQU,gBAAgBhgB,IAAI9C,GACrD6iB,IACDJ,GAASziB,EAroEM,cAsoEfyiB,GAASziB,EAAS+iB,cAA6B5gB,GAC/CpF,KAAKqlB,QAAQU,gBAAgB/f,IAAI/C,EAAS6iB,OAE9C,IAAIzgB,EAAYygB,EAAmB1gB,GAC/BE,EAAU,IAAIwf,GAAW1iB,EAAOpC,KAAKojB,IAgBzC,KAfYhhB,GAASA,EAAM3B,eAAe,WAC5B4E,GACVC,EAAQ2f,cAAc5f,EAAU2M,SAEpC8T,EAAmB1gB,GAAeE,EAC7BD,IACDA,EAAY6f,IArFP,SAuFO5f,EAAQlD,OAONiD,EAAUjD,QAAUkD,EAAQlD,MAA9C,CAmBA,IAAIkkB,EAAmB5gB,EAAgB1F,KAAKqlB,QAAQkB,iBAAkBtjB,MACtEqjB,EAAiB7iB,QAAQ,SAAUgB,GAK3BA,EAAO8f,aAAenT,EAAMgS,IAAM3e,EAAOW,aAAeA,GAAeX,EAAO+hB,QAC9E/hB,EAAO+e,YAGf,IAAIzR,EAAaoU,EAAQ3D,gBAAgBnd,EAAUjD,MAAOkD,EAAQlD,MAAOa,EAASqC,EAAQ4H,QACtFuZ,GAAuB,EAC3B,IAAK1U,EAAY,CACb,IAAKqU,EACD,OACJrU,EAAaoU,EAAQ7D,mBACrBmE,GAAuB,EAuB3B,OArBAzmB,KAAKqlB,QAAQqB,qBACb1mB,KAAKulB,OAAOpjB,MAAOc,QAASA,EAASmC,YAAaA,EAAa2M,WAAYA,EAAY1M,UAAWA,EAAWC,QAASA,EAASb,OAAQA,EAAQgiB,qBAAsBA,IAChKA,IACDf,GAASziB,EAjME,qBAkMXwB,EAAOI,QAAQ,WAAc8hB,GAAY1jB,EAlM9B,wBAoMfwB,EAAOM,OAAO,WACV,IAAI2e,EAAQtS,EAAMzO,QAAQyD,QAAQ3B,GAC9Bif,GAAS,GACTtS,EAAMzO,QAAQyI,OAAOsY,EAAO,GAEhC,IAAI/gB,EAAUyO,EAAMiU,QAAQkB,iBAAiBxgB,IAAI9C,GACjD,GAAIN,EAAS,CACT,IAAIikB,EAAUjkB,EAAQyD,QAAQ3B,GAC1BmiB,GAAW,GACXjkB,EAAQyI,OAAOwb,EAAS,MAIpC5mB,KAAK2C,QAAQR,KAAKsC,GAClB6hB,EAAiBnkB,KAAKsC,GACfA,EAvDH,IAiyCZ,SAASoiB,EAAUC,EAAG3mB,GAClB,IAAI4mB,EAAK3mB,OAAO0D,KAAKgjB,GACjBE,EAAK5mB,OAAO0D,KAAK3D,GACrB,GAAI4mB,EAAGzlB,QAAU0lB,EAAG1lB,OAChB,OAAO,EACX,IAAK,IAAIH,EAAI,EAAGA,EAAI4lB,EAAGzlB,OAAQH,IAAK,CAChC,IAAI4C,EAAOgjB,EAAG5lB,GACd,IAAKhB,EAAEM,eAAesD,IAAS+iB,EAAE/iB,KAAU5D,EAAE4D,GACzC,OAAO,EAEf,OAAO,EA3yCM8iB,CAAUxhB,EAAU6H,OAAQ5H,EAAQ4H,QAAS,CAC9C,IAAI7J,KACA4jB,EAAed,EAAQzD,YAAYrd,EAAUjD,MAAOiD,EAAU6H,OAAQ7J,GACtE6jB,EAAaf,EAAQzD,YAAYpd,EAAQlD,MAAOkD,EAAQ4H,OAAQ7J,GAChEA,EAAO/B,OACPtB,KAAKqlB,QAAQ8B,YAAY9jB,GAGzBrD,KAAKqlB,QAAQY,WAAW,WACpBvZ,EAAYzJ,EAASgkB,GACrB3a,EAAUrJ,EAASikB,OA+CvC/B,EAA6BtkB,UAAUumB,WAAa,SAAU3V,GAC1D,IAAIL,EAAQpR,YACLA,KAAKslB,UAAU7T,GACtBzR,KAAKqlB,QAAQU,gBAAgBtiB,QAAQ,SAAU4jB,EAAUpkB,UAAkBokB,EAAS5V,KACpFzR,KAAKwlB,kBAAkB/hB,QAAQ,SAAUoiB,EAAW5iB,GAChDmO,EAAMoU,kBAAkBxf,IAAI/C,EAAS4iB,EAAUnN,OAAO,SAAU4O,GAAS,OAAOA,EAAM7V,MAAQA,QAGtG0T,EAA6BtkB,UAAU0mB,kBAAoB,SAAUtkB,GACjEjD,KAAKqlB,QAAQU,gBAAgBvO,OAAOvU,GACpCjD,KAAKwlB,kBAAkBhO,OAAOvU,GAC9B,IAAIukB,EAAiBxnB,KAAKqlB,QAAQkB,iBAAiBxgB,IAAI9C,GACnDukB,IACAA,EAAe/jB,QAAQ,SAAUgB,GAAU,OAAOA,EAAO+e,YACzDxjB,KAAKqlB,QAAQkB,iBAAiB/O,OAAOvU,KAG7CkiB,EAA6BtkB,UAAU4mB,+BAAiC,SAAU1P,EAAanJ,EAASzF,GACpG,IAAIiI,EAAQpR,UACI,IAAZmJ,IAAsBA,GAAU,GAIpCnJ,KAAKqlB,QAAQtiB,OAAOkG,MAAM8O,EA5uER,eA4uE0C,GAAMtU,QAAQ,SAAUkE,GAGhF,IAAIA,EAAIkd,IAAR,CAEA,IAAI6C,EAAatW,EAAMiU,QAAQsC,yBAAyBhgB,GACpD+f,EAAWhV,KACXgV,EAAWjkB,QAAQ,SAAUmkB,GAAM,OAAOA,EAAGC,sBAAsBlgB,EAAKiH,GAAS,GAAO,KAGxFwC,EAAMmW,kBAAkB5f,OAIpCwd,EAA6BtkB,UAAUgnB,sBAAwB,SAAU5kB,EAAS2L,EAASkZ,EAAsB1B,GAC7G,IAAIhV,EAAQpR,KACR+nB,EAAgB/nB,KAAKqlB,QAAQU,gBAAgBhgB,IAAI9C,GACrD,GAAI8kB,EAAe,CACf,IAAIC,KAWJ,GAVA5nB,OAAO0D,KAAKikB,GAAetkB,QAAQ,SAAU2B,GAGzC,GAAIgM,EAAMkU,UAAUlgB,GAAc,CAC9B,IAAIX,EAAS2M,EAAM+U,QAAQljB,EAASmC,EAxMvC,OAwMgEghB,GACzD3hB,GACAujB,EAAU7lB,KAAKsC,MAIvBujB,EAAU1mB,OAKV,OAJAtB,KAAKqlB,QAAQ4C,qBAAqBjoB,KAAKojB,GAAIngB,GAAS,EAAM2L,GACtDkZ,GACAplB,EAAoBslB,GAAWjjB,OAAO,WAAc,OAAOqM,EAAMiU,QAAQ6C,iBAAiBjlB,MAEvF,EAGf,OAAO,GAEXkiB,EAA6BtkB,UAAUsnB,+BAAiC,SAAUllB,GAC9E,IAAImO,EAAQpR,KACR6lB,EAAY7lB,KAAKwlB,kBAAkBzf,IAAI9C,GAC3C,GAAI4iB,EAAW,CACX,IAAIuC,EAAoB,IAAIrY,IAC5B8V,EAAUpiB,QAAQ,SAAU4kB,GACxB,IAAIjjB,EAAcijB,EAAS5W,KAC3B,IAAI2W,EAAkB/X,IAAIjL,GAA1B,CAEAgjB,EAAkB3V,IAAIrN,GACtB,IACI2M,EADUX,EAAMkU,UAAUlgB,GACLkd,mBAErBjd,EADgB+L,EAAMiU,QAAQU,gBAAgBhgB,IAAI9C,GACxBmC,IAAgB8f,GAC1C5f,EAAU,IAAIwf,GAtOjB,QAuOGrgB,EAAS,IAAI4hB,GAA0BjV,EAAMgS,GAAIhe,EAAanC,GAClEmO,EAAMiU,QAAQqB,qBACdtV,EAAMmU,OAAOpjB,MACTc,QAASA,EACTmC,YAAaA,EACb2M,WAAYA,EACZ1M,UAAWA,EACXC,QAASA,EACTb,OAAQA,EACRgiB,sBAAsB,SAKtCtB,EAA6BtkB,UAAUynB,WAAa,SAAUrlB,EAAS2L,GACnE,IAAIwC,EAAQpR,KACRuoB,EAASvoB,KAAKqlB,QAKlB,GAJIpiB,EAAQulB,mBACRxoB,KAAKynB,+BAA+BxkB,EAAS2L,GAAS,IAGtD5O,KAAK6nB,sBAAsB5kB,EAAS2L,GAAS,GAAjD,CAIA,IAAI6Z,GAAoC,EACxC,GAAIF,EAAOG,gBAAiB,CACxB,IAAIC,EAAiBJ,EAAO5lB,QAAQrB,OAASinB,EAAOK,wBAAwB7iB,IAAI9C,MAKhF,GAAI0lB,GAAkBA,EAAernB,OACjCmnB,GAAoC,OAIpC,IADA,IAAII,EAAW5lB,EACR4lB,EAAWA,EAASC,YAEvB,GADeP,EAAOxC,gBAAgBhgB,IAAI8iB,GAC5B,CACVJ,GAAoC,EACpC,OAShBzoB,KAAKmoB,+BAA+BllB,GAGhCwlB,EACAF,EAAON,qBAAqBjoB,KAAKojB,GAAIngB,GAAS,EAAO2L,IAKrD2Z,EAAOtC,WAAW,WAAc,OAAO7U,EAAMmW,kBAAkBtkB,KAC/DslB,EAAOQ,uBAAuB9lB,GAC9BslB,EAAOS,mBAAmB/lB,EAAS2L,MAG3CuW,EAA6BtkB,UAAUooB,WAAa,SAAUhmB,EAASimB,GAAUxD,GAASziB,EAASjD,KAAKylB,iBACxGN,EAA6BtkB,UAAUsoB,uBAAyB,SAAUC,GACtE,IAAIhY,EAAQpR,KACRuX,KA4BJ,OA3BAvX,KAAKulB,OAAO9hB,QAAQ,SAAU6jB,GAC1B,IAAI7iB,EAAS6iB,EAAM7iB,OACnB,IAAIA,EAAO4kB,UAAX,CAEA,IAAIpmB,EAAUqkB,EAAMrkB,QAChB4iB,EAAYzU,EAAMoU,kBAAkBzf,IAAI9C,GACxC4iB,GACAA,EAAUpiB,QAAQ,SAAU4kB,GACxB,GAAIA,EAAS5W,MAAQ6V,EAAMliB,YAAa,CACpC,IAAIwe,EAAYze,EAAmBlC,EAASqkB,EAAMliB,YAAakiB,EAAMjiB,UAAUjD,MAAOklB,EAAMhiB,QAAQlD,OACpGwhB,EAAiB,MAAIwF,EACrB5kB,EAAe8iB,EAAM7iB,OAAQ4jB,EAAS1C,MAAO/B,EAAWyE,EAASzjB,aAIzEH,EAAO6kB,iBACPlY,EAAMiU,QAAQY,WAAW,WAGrBxhB,EAAO+e,YAIXjM,EAAapV,KAAKmlB,MAG1BtnB,KAAKulB,UACEhO,EAAagS,KAAK,SAAUzC,EAAG3mB,GAGlC,IAAIqpB,EAAK1C,EAAE/U,WAAWqC,IAAI9C,SACtBmY,EAAKtpB,EAAE4R,WAAWqC,IAAI9C,SAC1B,OAAU,GAANkY,GAAiB,GAANC,EACJD,EAAKC,EAETrY,EAAMiU,QAAQtiB,OAAO0F,gBAAgBqe,EAAE7jB,QAAS9C,EAAE8C,SAAW,GAAK,KAGjFkiB,EAA6BtkB,UAAU2iB,QAAU,SAAU5U,GACvD5O,KAAK2C,QAAQc,QAAQ,SAAUjD,GAAK,OAAOA,EAAEgjB,YAC7CxjB,KAAKynB,+BAA+BznB,KAAKolB,YAAaxW,IAE1DuW,EAA6BtkB,UAAU6oB,oBAAsB,SAAUzmB,GACnE,IAAI0mB,GAAe,EAKnB,OAJI3pB,KAAKwlB,kBAAkBnV,IAAIpN,KAC3B0mB,GAAe,KAEd3pB,KAAKulB,OAAO/O,KAAK,SAAU8Q,GAAS,OAAOA,EAAMrkB,UAAYA,KAA+B0mB,GAG9FxE,EA3VsC,GA6V7CyE,GAA2C,WAC3C,SAASA,EAA0B7G,EAAUhgB,EAAQigB,GACjDhjB,KAAK+iB,SAAWA,EAChB/iB,KAAK+C,OAASA,EACd/C,KAAKgjB,YAAcA,EACnBhjB,KAAK2C,WACL3C,KAAK6pB,gBAAkB,IAAI/jB,IAC3B9F,KAAKumB,iBAAmB,IAAIzgB,IAC5B9F,KAAK4oB,wBAA0B,IAAI9iB,IACnC9F,KAAK+lB,gBAAkB,IAAIjgB,IAC3B9F,KAAK8pB,cAAgB,IAAI/Z,IACzB/P,KAAK0oB,gBAAkB,EACvB1oB,KAAK0mB,mBAAqB,EAC1B1mB,KAAK+pB,oBACL/pB,KAAKgqB,kBACLhqB,KAAKiqB,aACLjqB,KAAKkqB,iBACLlqB,KAAKmqB,wBAA0B,IAAIrkB,IACnC9F,KAAKoqB,0BACLpqB,KAAKqqB,0BAELrqB,KAAKsqB,kBAAoB,SAAUrnB,EAAS2L,KA2xBhD,OAxxBAgb,EAA0B/oB,UAAUmoB,mBAAqB,SAAU/lB,EAAS2L,GAAW5O,KAAKsqB,kBAAkBrnB,EAAS2L,IACvHxO,OAAO6J,eAAe2f,EAA0B/oB,UAAW,iBACvDkF,IAAK,WACD,IAAIpD,KAQJ,OAPA3C,KAAKgqB,eAAevmB,QAAQ,SAAUmkB,GAClCA,EAAGjlB,QAAQc,QAAQ,SAAUgB,GACrBA,EAAO+hB,QACP7jB,EAAQR,KAAKsC,OAIlB9B,GAEX0Y,YAAY,EACZC,cAAc,IAElBsO,EAA0B/oB,UAAU0pB,gBAAkB,SAAUhG,EAAaa,GACzE,IAAIwC,EAAK,IAAIzC,GAA6BZ,EAAaa,EAAaplB,MAgBpE,OAfIolB,EAAY0D,WACZ9oB,KAAKwqB,sBAAsB5C,EAAIxC,IAM/BplB,KAAK6pB,gBAAgB7jB,IAAIof,EAAawC,GAMtC5nB,KAAKyqB,oBAAoBrF,IAEtBplB,KAAK+pB,iBAAiBxF,GAAeqD,GAEhDgC,EAA0B/oB,UAAU2pB,sBAAwB,SAAU5C,EAAIxC,GACtE,IAAIpP,EAAQhW,KAAKgqB,eAAe1oB,OAAS,EACzC,GAAI0U,GAAS,EAAG,CAEZ,IADA,IAAI0U,GAAQ,EACHvpB,EAAI6U,EAAO7U,GAAK,EAAGA,IAExB,GAAInB,KAAK+C,OAAO0F,gBADIzI,KAAKgqB,eAAe7oB,GACMikB,YAAaA,GAAc,CACrEplB,KAAKgqB,eAAe5e,OAAOjK,EAAI,EAAG,EAAGymB,GACrC8C,GAAQ,EACR,MAGHA,GACD1qB,KAAKgqB,eAAe5e,OAAO,EAAG,EAAGwc,QAIrC5nB,KAAKgqB,eAAe7nB,KAAKylB,GAG7B,OADA5nB,KAAKmqB,wBAAwBnkB,IAAIof,EAAawC,GACvCA,GAEXgC,EAA0B/oB,UAAUsiB,SAAW,SAAUoB,EAAaa,GAClE,IAAIwC,EAAK5nB,KAAK+pB,iBAAiBxF,GAI/B,OAHKqD,IACDA,EAAK5nB,KAAKuqB,gBAAgBhG,EAAaa,IAEpCwC,GAEXgC,EAA0B/oB,UAAU8pB,gBAAkB,SAAUpG,EAAa9S,EAAM0U,GAC/E,IAAIyB,EAAK5nB,KAAK+pB,iBAAiBxF,GAC3BqD,GAAMA,EAAGzE,SAAS1R,EAAM0U,IACxBnmB,KAAK0oB,mBAGbkB,EAA0B/oB,UAAU2iB,QAAU,SAAUe,EAAa3V,GACjE,IAAIwC,EAAQpR,KACZ,GAAKukB,EAAL,CAEA,IAAIqD,EAAK5nB,KAAK4qB,gBAAgBrG,GAC9BvkB,KAAKimB,WAAW,WACZ7U,EAAM+Y,wBAAwB3S,OAAOoQ,EAAGxC,oBACjChU,EAAM2Y,iBAAiBxF,GAC9B,IAAIb,EAAQtS,EAAM4Y,eAAe5jB,QAAQwhB,GACrClE,GAAS,GACTtS,EAAM4Y,eAAe5e,OAAOsY,EAAO,KAG3C1jB,KAAK6qB,yBAAyB,WAAc,OAAOjD,EAAGpE,QAAQ5U,OAElEgb,EAA0B/oB,UAAU+pB,gBAAkB,SAAUxH,GAAM,OAAOpjB,KAAK+pB,iBAAiB3G,IACnGwG,EAA0B/oB,UAAU8mB,yBAA2B,SAAU1kB,GAMrE,IAAIykB,EAAa,IAAI3X,IACjB+a,EAAgB9qB,KAAK+lB,gBAAgBhgB,IAAI9C,GAC7C,GAAI6nB,EAEA,IADA,IAAIhnB,EAAO1D,OAAO0D,KAAKgnB,GACd3pB,EAAI,EAAGA,EAAI2C,EAAKxC,OAAQH,IAAK,CAClC,IAAI4pB,EAAOD,EAAchnB,EAAK3C,IAAIojB,YAClC,GAAIwG,EAAM,CACN,IAAInD,EAAK5nB,KAAK4qB,gBAAgBG,GAC1BnD,GACAF,EAAWjV,IAAImV,IAK/B,OAAOF,GAEXkC,EAA0B/oB,UAAUslB,QAAU,SAAU5B,EAAathB,EAASwO,EAAMrP,GAChF,GAAI4oB,GAAc/nB,GAAU,CACxB,IAAI2kB,EAAK5nB,KAAK4qB,gBAAgBrG,GAC9B,GAAIqD,EAEA,OADAA,EAAGzB,QAAQljB,EAASwO,EAAMrP,IACnB,EAGf,OAAO,GAEXwnB,EAA0B/oB,UAAUooB,WAAa,SAAU1E,EAAathB,EAASimB,EAAQ+B,GACrF,GAAKD,GAAc/nB,GAAnB,CAIA,IAAIioB,EAAUjoB,EAAQ4hB,IACtB,GAAIqG,GAAWA,EAAQ1G,cAAe,CAClC0G,EAAQ1G,eAAgB,EACxB0G,EAAQzG,YAAa,EACrB,IAAIf,EAAQ1jB,KAAKqqB,uBAAuBjkB,QAAQnD,GAC5CygB,GAAS,GACT1jB,KAAKqqB,uBAAuBjf,OAAOsY,EAAO,GAMlD,GAAIa,EAAa,CACb,IAAIqD,EAAK5nB,KAAK4qB,gBAAgBrG,GAO1BqD,GACAA,EAAGqB,WAAWhmB,EAASimB,GAI3B+B,GACAjrB,KAAKyqB,oBAAoBxnB,KAGjC2mB,EAA0B/oB,UAAU4pB,oBAAsB,SAAUxnB,GAAWjD,KAAKoqB,uBAAuBjoB,KAAKc,IAChH2mB,EAA0B/oB,UAAUsqB,sBAAwB,SAAUloB,EAASb,GACvEA,EACKpC,KAAK8pB,cAAczZ,IAAIpN,KACxBjD,KAAK8pB,cAAcrX,IAAIxP,GACvByiB,GAASziB,EA7kBA,wBAglBRjD,KAAK8pB,cAAczZ,IAAIpN,KAC5BjD,KAAK8pB,cAActS,OAAOvU,GAC1B0jB,GAAY1jB,EAllBC,yBAqlBrB2mB,EAA0B/oB,UAAUynB,WAAa,SAAU/D,EAAathB,EAAS2L,GAC7E,GAAKoc,GAAc/nB,GAAnB,CAIA,IAAI2kB,EAAKrD,EAAcvkB,KAAK4qB,gBAAgBrG,GAAe,KACvDqD,EACAA,EAAGU,WAAWrlB,EAAS2L,GAGvB5O,KAAKioB,qBAAqB1D,EAAathB,GAAS,EAAO2L,QARvD5O,KAAKgpB,mBAAmB/lB,EAAS2L,IAWzCgb,EAA0B/oB,UAAUonB,qBAAuB,SAAU1D,EAAathB,EAASyhB,EAAc9V,GACrG5O,KAAKqqB,uBAAuBloB,KAAKc,GACjCA,EAAQ4hB,KACJN,YAAaA,EACbC,cAAe5V,EAAS8V,aAAcA,EACtCC,sBAAsB,IAG9BiF,EAA0B/oB,UAAU8iB,OAAS,SAAUY,EAAathB,EAASwO,EAAMkU,EAAO/gB,GACtF,OAAIomB,GAAc/nB,GACPjD,KAAK4qB,gBAAgBrG,GAAaZ,OAAO1gB,EAASwO,EAAMkU,EAAO/gB,GAEnE,cAEXglB,EAA0B/oB,UAAUuqB,kBAAoB,SAAU9D,EAAO+D,EAAcrT,EAAgBC,EAAgBqT,GACnH,OAAOhE,EAAMvV,WAAWlB,MAAM7Q,KAAK+C,OAAQukB,EAAMrkB,QAASqkB,EAAMjiB,UAAUjD,MAAOklB,EAAMhiB,QAAQlD,MAAO4V,EAAgBC,EAAgBqP,EAAMjiB,UAAU2M,QAASsV,EAAMhiB,QAAQ0M,QAASqZ,EAAcC,IAExM1B,EAA0B/oB,UAAUkoB,uBAAyB,SAAUwC,GACnE,IAAIna,EAAQpR,KACRqc,EAAWrc,KAAK+C,OAAOkG,MAAMsiB,EAvnFf,eAunFsD,GACxElP,EAAS5Y,QAAQ,SAAUR,GAAW,OAAOmO,EAAMoa,kCAAkCvoB,KAC5C,GAArCjD,KAAK4oB,wBAAwBlW,OAEjC2J,EAAWrc,KAAK+C,OAAOkG,MAAMsiB,EAznFT,iBAynFkD,IAC7D9nB,QAAQ,SAAUR,GAAW,OAAOmO,EAAMqa,sCAAsCxoB,MAE7F2mB,EAA0B/oB,UAAU2qB,kCAAoC,SAAUvoB,GAC9E,IAAIN,EAAU3C,KAAKumB,iBAAiBxgB,IAAI9C,GACpCN,GACAA,EAAQc,QAAQ,SAAUgB,GAIlBA,EAAO+hB,OACP/hB,EAAO6kB,kBAAmB,EAG1B7kB,EAAO+e,aAKvBoG,EAA0B/oB,UAAU4qB,sCAAwC,SAAUxoB,GAClF,IAAIN,EAAU3C,KAAK4oB,wBAAwB7iB,IAAI9C,GAC3CN,GACAA,EAAQc,QAAQ,SAAUgB,GAAU,OAAOA,EAAOyf,YAG1D0F,EAA0B/oB,UAAU6qB,kBAAoB,WACpD,IAAIta,EAAQpR,KACZ,OAAO,IAAI2rB,QAAQ,SAAUC,GACzB,GAAIxa,EAAMzO,QAAQrB,OACd,OAAOoB,EAAoB0O,EAAMzO,SAASoC,OAAO,WAAc,OAAO6mB,MAGtEA,OAIZhC,EAA0B/oB,UAAUqnB,iBAAmB,SAAUjlB,GAC7D,IAAImO,EAAQpR,KACRkrB,EAAUjoB,EAAQ4hB,IACtB,GAAIqG,GAAWA,EAAQ1G,cAAe,CAGlC,GADAvhB,EAAQ4hB,IAAgBP,GACpB4G,EAAQ3G,YAAa,CACrBvkB,KAAK+oB,uBAAuB9lB,GAC5B,IAAI2kB,EAAK5nB,KAAK4qB,gBAAgBM,EAAQ3G,aAClCqD,GACAA,EAAGL,kBAAkBtkB,GAG7BjD,KAAKgpB,mBAAmB/lB,EAASioB,EAAQ1G,eAEzCxkB,KAAK+C,OAAOyF,eAAevF,EA3qBf,yBA4qBZjD,KAAKmrB,sBAAsBloB,GAAS,GAExCjD,KAAK+C,OAAOkG,MAAMhG,EA9qBF,wBA8qB8B,GAAMQ,QAAQ,SAAUkL,GAClEyC,EAAM+Z,sBAAsBxc,GAAM,MAG1Cib,EAA0B/oB,UAAUgrB,MAAQ,SAAUzC,GAClD,IAAIhY,EAAQpR,UACQ,IAAhBopB,IAA0BA,GAAe,GAC7C,IAAIzmB,KAKJ,GAJI3C,KAAK6pB,gBAAgBnX,OACrB1S,KAAK6pB,gBAAgBpmB,QAAQ,SAAUmkB,EAAI3kB,GAAW,OAAOmO,EAAMoZ,sBAAsB5C,EAAI3kB,KAC7FjD,KAAK6pB,gBAAgBlS,SAErB3X,KAAK0oB,iBAAmB1oB,KAAKoqB,uBAAuB9oB,OACpD,IAAK,IAAIH,EAAI,EAAGA,EAAInB,KAAKoqB,uBAAuB9oB,OAAQH,IAEpDukB,GADU1lB,KAAKoqB,uBAAuBjpB,GA3rBjC,oBA+rBb,GAAInB,KAAKgqB,eAAe1oB,SACnBtB,KAAK0mB,oBAAsB1mB,KAAKqqB,uBAAuB/oB,QAAS,CACjE,IAAIwqB,KACJ,IACInpB,EAAU3C,KAAK+rB,iBAAiBD,EAAY1C,GAEhD,QACI,IAASjoB,EAAI,EAAGA,EAAI2qB,EAAWxqB,OAAQH,IACnC2qB,EAAW3qB,WAKnB,IAASA,EAAI,EAAGA,EAAInB,KAAKqqB,uBAAuB/oB,OAAQH,IAEpDnB,KAAKkoB,iBADSloB,KAAKqqB,uBAAuBlpB,IASlD,GALAnB,KAAK0mB,mBAAqB,EAC1B1mB,KAAKoqB,uBAAuB9oB,OAAS,EACrCtB,KAAKqqB,uBAAuB/oB,OAAS,EACrCtB,KAAKiqB,UAAUxmB,QAAQ,SAAUod,GAAM,OAAOA,MAC9C7gB,KAAKiqB,aACDjqB,KAAKkqB,cAAc5oB,OAAQ,CAI3B,IAAI0qB,EAAahsB,KAAKkqB,cACtBlqB,KAAKkqB,iBACDvnB,EAAQrB,OACRoB,EAAoBC,GAASoC,OAAO,WAAcinB,EAAWvoB,QAAQ,SAAUod,GAAM,OAAOA,QAG5FmL,EAAWvoB,QAAQ,SAAUod,GAAM,OAAOA,QAItD+I,EAA0B/oB,UAAUsmB,YAAc,SAAU9jB,GACxD,MAAM,IAAIiB,MAAM,kFAAoFjB,EAAOkB,KAAK,QAEpHqlB,EAA0B/oB,UAAUkrB,iBAAmB,SAAUD,EAAY1C,GACzE,IAAIhY,EAAQpR,KACRqrB,EAAe,IAAIjU,GACnB6U,KACAC,EAAoB,IAAIpmB,IACxBqmB,KACA/L,EAAkB,IAAIta,IACtBsmB,EAAsB,IAAItmB,IAC1BumB,EAAuB,IAAIvmB,IAC3BwmB,EAAsB,IAAIvc,IAC9B/P,KAAK8pB,cAAcrmB,QAAQ,SAAUkL,GACjC2d,EAAoB7Z,IAAI9D,GAExB,IADA,IAAI4d,EAAuBnb,EAAMrO,OAAOkG,MAAM0F,EAtvBpC,sBAsvB2D,GAC5D6d,EAAM,EAAGA,EAAMD,EAAqBjrB,OAAQkrB,IACjDF,EAAoB7Z,IAAI8Z,EAAqBC,MAGrD,IAAIzJ,EAAW/iB,KAAK+iB,SAChB0J,EAAqBlsB,MAAMmsB,KAAK1sB,KAAK+lB,gBAAgBjiB,QACrD6oB,EAAeC,GAAaH,EAAoBzsB,KAAKoqB,wBAIrDyC,EAAkB,IAAI/mB,IACtB3E,EAAI,EACRwrB,EAAalpB,QAAQ,SAAUqpB,EAAOC,GAClC,IAAIC,EAxwFM,WAwwFwB7rB,IAClC0rB,EAAgB7mB,IAAI+mB,EAAMC,GAC1BF,EAAMrpB,QAAQ,SAAUkL,GAAQ,OAAO+W,GAAS/W,EAAMqe,OAK1D,IAHA,IAAIC,KACAC,EAAmB,IAAInd,IACvBod,EAA8B,IAAIpd,IAC7Bqd,EAAM,EAAGA,EAAMptB,KAAKqqB,uBAAuB/oB,OAAQ8rB,KAEpDlC,GADAjoB,EAAUjD,KAAKqqB,uBAAuB+C,IACpBvI,MACPqG,EAAQ1G,gBACnByI,EAAc9qB,KAAKc,GACnBiqB,EAAiBza,IAAIxP,GACjBioB,EAAQxG,aACR1kB,KAAK+C,OAAOkG,MAAMhG,EA9wBlB,qBA8wB0C,GAAMQ,QAAQ,SAAUkE,GAAO,OAAOulB,EAAiBza,IAAI9K,KAGrGwlB,EAA4B1a,IAAIxP,IAI5C,IAAIoqB,EAAkB,IAAIvnB,IACtBwnB,EAAeV,GAAaH,EAAoBlsB,MAAMmsB,KAAKQ,IAC/DI,EAAa7pB,QAAQ,SAAUqpB,EAAOC,GAClC,IAAIC,EA/xFM,WA+xFwB7rB,IAClCksB,EAAgBrnB,IAAI+mB,EAAMC,GAC1BF,EAAMrpB,QAAQ,SAAUkL,GAAQ,OAAO+W,GAAS/W,EAAMqe,OAE1DlB,EAAW3pB,KAAK,WACZwqB,EAAalpB,QAAQ,SAAUqpB,EAAOC,GAClC,IAAIC,EAAYH,EAAgB9mB,IAAIgnB,GACpCD,EAAMrpB,QAAQ,SAAUkL,GAAQ,OAAOgY,GAAYhY,EAAMqe,OAE7DM,EAAa7pB,QAAQ,SAAUqpB,EAAOC,GAClC,IAAIC,EAAYK,EAAgBtnB,IAAIgnB,GACpCD,EAAMrpB,QAAQ,SAAUkL,GAAQ,OAAOgY,GAAYhY,EAAMqe,OAE7DC,EAAcxpB,QAAQ,SAAUR,GAAWmO,EAAM8W,iBAAiBjlB,OAItE,IAFA,IAAIsqB,KACAC,KACKC,EAAMztB,KAAKgqB,eAAe1oB,OAAS,EAAGmsB,GAAO,EAAGA,IAC5CztB,KAAKgqB,eAAeyD,GAC1BtE,uBAAuBC,GAAa3lB,QAAQ,SAAU6jB,GACrD,IAAI7iB,EAAS6iB,EAAM7iB,OACfxB,EAAUqkB,EAAMrkB,QAEpB,GADAsqB,EAAWprB,KAAKsC,GACZ2M,EAAMgZ,uBAAuB9oB,OAAQ,CACrC,IAAI4pB,EAAUjoB,EAAQ4hB,IAEtB,GAAIqG,GAAWA,EAAQzG,WAEnB,YADAhgB,EAAO+e,UAIf,IAAIkK,GAAkB3K,IAAa3R,EAAMrO,OAAO0F,gBAAgBsa,EAAU9f,GACtEgV,EAAiBoV,EAAgBtnB,IAAI9C,GACrC+U,EAAiB6U,EAAgB9mB,IAAI9C,GACrCoW,EAAcjI,EAAMga,kBAAkB9D,EAAO+D,EAAcrT,EAAgBC,EAAgByV,GAC/F,GAAIrU,EAAYhW,QAAUgW,EAAYhW,OAAO/B,OACzCksB,EAAqBrrB,KAAKkX,OAD9B,CAQA,GAAIqU,EAIA,OAHAjpB,EAAOI,QAAQ,WAAc,OAAO6H,EAAYzJ,EAASoW,EAAY6G,cACrEzb,EAAOO,UAAU,WAAc,OAAOsH,EAAUrJ,EAASoW,EAAY8G,iBACrE8L,EAAe9pB,KAAKsC,GAKxB,GAAI6iB,EAAMb,qBAIN,OAHAhiB,EAAOI,QAAQ,WAAc,OAAO6H,EAAYzJ,EAASoW,EAAY6G,cACrEzb,EAAOO,UAAU,WAAc,OAAOsH,EAAUrJ,EAASoW,EAAY8G,iBACrE8L,EAAe9pB,KAAKsC,GAQxB4U,EAAYZ,UAAUhV,QAAQ,SAAUoV,GAAM,OAAOA,EAAGsD,yBAA0B,IAClFkP,EAAa5T,OAAOxU,EAASoW,EAAYZ,WAEzC0T,EAAmBhqB,MADLkX,YAAaA,EAAa5U,OAAQA,EAAQxB,QAASA,IAEjEoW,EAAY+G,gBAAgB3c,QAAQ,SAAUR,GAAW,OAAOyC,EAAgB0a,EAAiBnd,MAAad,KAAKsC,KACnH4U,EAAYpC,cAAcxT,QAAQ,SAAUkqB,EAAW1qB,GACnD,IAAIsa,EAAQnd,OAAO0D,KAAK6pB,GACxB,GAAIpQ,EAAMjc,OAAQ,CACd,IAAIssB,EAAWxB,EAAoBrmB,IAAI9C,GAClC2qB,GACDxB,EAAoBpmB,IAAI/C,EAAS2qB,EAAW,IAAI7d,KAEpDwN,EAAM9Z,QAAQ,SAAUM,GAAQ,OAAO6pB,EAASnb,IAAI1O,QAG5DsV,EAAYnC,eAAezT,QAAQ,SAAUkqB,EAAW1qB,GACpD,IAAIsa,EAAQnd,OAAO0D,KAAK6pB,GACpBE,EAASxB,EAAqBtmB,IAAI9C,GACjC4qB,GACDxB,EAAqBrmB,IAAI/C,EAAS4qB,EAAS,IAAI9d,KAEnDwN,EAAM9Z,QAAQ,SAAUM,GAAQ,OAAO8pB,EAAOpb,IAAI1O,UAI9D,GAAIypB,EAAqBlsB,OAAQ,CAC7B,IAAIwsB,KACJN,EAAqB/pB,QAAQ,SAAU4V,GACnCyU,EAAS3rB,KAAK,IAAMkX,EAAYjU,YAAc,yBAC9CiU,EAAYhW,OAAOI,QAAQ,SAAUpB,GAAS,OAAOyrB,EAAS3rB,KAAK,KAAOE,EAAQ,UAEtFkrB,EAAW9pB,QAAQ,SAAUgB,GAAU,OAAOA,EAAO+e,YACrDxjB,KAAKmnB,YAAY2G,GAErB,IAAIC,EAAwB,IAAIjoB,IAK5BkoB,EAAsB,IAAIloB,IAC9BqmB,EAAmB1oB,QAAQ,SAAU6jB,GACjC,IAAIrkB,EAAUqkB,EAAMrkB,QAChBooB,EAAahb,IAAIpN,KACjB+qB,EAAoBhoB,IAAI/C,EAASA,GACjCmO,EAAM6c,sBAAsB3G,EAAM7iB,OAAO8f,YAAa+C,EAAMjO,YAAa0U,MAGjF9B,EAAexoB,QAAQ,SAAUgB,GAC7B,IAAIxB,EAAUwB,EAAOxB,QACCmO,EAAM8c,oBAAoBjrB,GAAS,EAAOwB,EAAO8f,YAAa9f,EAAOW,YAAa,MACxF3B,QAAQ,SAAU0qB,GAC9BzoB,EAAgBqoB,EAAuB9qB,MAAad,KAAKgsB,GACzDA,EAAW3K,cAUnB,IAAI4K,EAAenB,EAAcvU,OAAO,SAAU/J,GAC9C,OAAO0f,GAAuB1f,EAAMyd,EAAqBC,KAGzDiC,EAAgB,IAAIxoB,IACGyoB,GAAsBD,EAAetuB,KAAK+C,OAAQoqB,EAA6Bd,EAAsBxsB,EAAWuE,YACtHX,QAAQ,SAAUkL,GAC/B0f,GAAuB1f,EAAMyd,EAAqBC,IAClD+B,EAAajsB,KAAKwM,KAI1B,IAAI6f,EAAe,IAAI1oB,IACvB6mB,EAAalpB,QAAQ,SAAUqpB,EAAOC,GAClCwB,GAAsBC,EAAcpd,EAAMrO,OAAQ,IAAIgN,IAAI+c,GAAQV,EAAqBvsB,EAAWsE,cAEtGiqB,EAAa3qB,QAAQ,SAAUkL,GAC3B,IAAI8f,EAAOH,EAAcvoB,IAAI4I,GACzB+f,EAAMF,EAAazoB,IAAI4I,GAC3B2f,EAActoB,IAAI2I,EAAM5N,KAAa0tB,EAAMC,MAE/C,IAAIC,KACAC,KACAC,KACJ1C,EAAmB1oB,QAAQ,SAAU6jB,GACjC,IAAIrkB,EAAUqkB,EAAMrkB,QAASwB,EAAS6iB,EAAM7iB,OAAQ4U,EAAciO,EAAMjO,YAGxE,GAAIgS,EAAahb,IAAIpN,GAAU,CAC3B,GAAIqpB,EAAoBjc,IAAIpN,GAKxB,OAJAwB,EAAOO,UAAU,WAAc,OAAOsH,EAAUrJ,EAASoW,EAAY8G,YACrE1b,EAAOe,UAAW,EAClBf,EAAOqqB,kBAAkBzV,EAAYnU,gBACrC+mB,EAAe9pB,KAAKsC,GASxB,IAAIsqB,EAAwBF,EAC5B,GAAIb,EAAoBtb,KAAO,EAAG,CAG9B,IAFA,IAAI/K,EAAM1E,EACN+rB,KACGrnB,EAAMA,EAAImhB,YAAY,CACzB,IAAImG,EAAiBjB,EAAoBjoB,IAAI4B,GAC7C,GAAIsnB,EAAgB,CAChBF,EAAwBE,EACxB,MAEJD,EAAa7sB,KAAKwF,GAEtBqnB,EAAavrB,QAAQ,SAAUylB,GAAU,OAAO8E,EAAoBhoB,IAAIkjB,EAAQ6F,KAEpF,IAAIG,EAAc9d,EAAM+d,gBAAgB1qB,EAAO8f,YAAalL,EAAa0U,EAAuB7B,EAAmBsC,EAAcF,GAEjI,GADA7pB,EAAO2qB,cAAcF,GACjBH,IAA0BF,EAC1BF,EAAYxsB,KAAKsC,OAEhB,CACD,IAAI4qB,EAAgBje,EAAMmV,iBAAiBxgB,IAAIgpB,GAC3CM,GAAiBA,EAAc/tB,SAC/BmD,EAAO6qB,aAAe5sB,EAAoB2sB,IAE9CpD,EAAe9pB,KAAKsC,SAIxBiI,EAAYzJ,EAASoW,EAAY6G,YACjCzb,EAAOO,UAAU,WAAc,OAAOsH,EAAUrJ,EAASoW,EAAY8G,YAIrEyO,EAAWzsB,KAAKsC,GACZ6nB,EAAoBjc,IAAIpN,IACxBgpB,EAAe9pB,KAAKsC,KAKhCmqB,EAAWnrB,QAAQ,SAAUgB,GAGzB,IAAI8qB,EAAoBrD,EAAkBnmB,IAAItB,EAAOxB,SACrD,GAAIssB,GAAqBA,EAAkBjuB,OAAQ,CAC/C,IAAI4tB,EAAcxsB,EAAoB6sB,GACtC9qB,EAAO2qB,cAAcF,MAM7BjD,EAAexoB,QAAQ,SAAUgB,GACzBA,EAAO6qB,aACP7qB,EAAO+qB,iBAAiB/qB,EAAO6qB,cAG/B7qB,EAAO+e,YAMf,IAAK,IAAIiM,EAAM,EAAGA,EAAMxC,EAAc3rB,OAAQmuB,IAAO,CACjD,IAAIxsB,EACAioB,GADAjoB,EAAUgqB,EAAcwC,IACN5K,IAKtB,GAJA8B,GAAY1jB,EAvgGF,aA2gGNioB,IAAWA,EAAQxG,aAAvB,CAEA,IAAI/hB,KAIJ,GAAIyd,EAAgB1N,KAAM,CACtB,IAAIgd,EAAuBtP,EAAgBra,IAAI9C,GAC3CysB,GAAwBA,EAAqBpuB,QAC7CqB,EAAQR,KAAKX,MAAMmB,EAASL,EAASotB,IAGzC,IADA,IAAIC,EAAuB3vB,KAAK+C,OAAOkG,MAAMhG,EAlhGjC,iBAkhGiE,GACpE2sB,EAAI,EAAGA,EAAID,EAAqBruB,OAAQsuB,IAAK,CAClD,IAAIC,EAAiBzP,EAAgBra,IAAI4pB,EAAqBC,IAC1DC,GAAkBA,EAAevuB,QACjCqB,EAAQR,KAAKX,MAAMmB,EAASL,EAASutB,KAIjD,IAAIC,EAAgBntB,EAAQ+V,OAAO,SAAUlY,GAAK,OAAQA,EAAE6oB,YACxDyG,EAAcxuB,OACdyuB,GAA8B/vB,KAAMiD,EAAS6sB,GAG7C9vB,KAAKkoB,iBAAiBjlB,IAc9B,OAVAgqB,EAAc3rB,OAAS,EACvBqtB,EAAYlrB,QAAQ,SAAUgB,GAC1B2M,EAAMzO,QAAQR,KAAKsC,GACnBA,EAAOM,OAAO,WACVN,EAAO+e,UACP,IAAIE,EAAQtS,EAAMzO,QAAQyD,QAAQ3B,GAClC2M,EAAMzO,QAAQyI,OAAOsY,EAAO,KAEhCjf,EAAOqf,SAEJ6K,GAEX/E,EAA0B/oB,UAAU6oB,oBAAsB,SAAUnF,EAAathB,GAC7E,IAAI0mB,GAAe,EACfuB,EAAUjoB,EAAQ4hB,IAStB,OARIqG,GAAWA,EAAQ1G,gBACnBmF,GAAe,GACf3pB,KAAKumB,iBAAiBlW,IAAIpN,KAC1B0mB,GAAe,GACf3pB,KAAK4oB,wBAAwBvY,IAAIpN,KACjC0mB,GAAe,GACf3pB,KAAK+lB,gBAAgB1V,IAAIpN,KACzB0mB,GAAe,GACZ3pB,KAAK4qB,gBAAgBrG,GAAamF,oBAAoBzmB,IAAY0mB,GAE7EC,EAA0B/oB,UAAUolB,WAAa,SAAUrhB,GAAY5E,KAAKiqB,UAAU9nB,KAAKyC,IAC3FglB,EAA0B/oB,UAAUgqB,yBAA2B,SAAUjmB,GAAY5E,KAAKkqB,cAAc/nB,KAAKyC,IAC7GglB,EAA0B/oB,UAAUqtB,oBAAsB,SAAUjrB,EAAS+sB,EAAkBzL,EAAanf,EAAa6qB,GACrH,IAAIttB,KACJ,GAAIqtB,EAAkB,CAClB,IAAIE,EAAwBlwB,KAAK4oB,wBAAwB7iB,IAAI9C,GACzDitB,IACAvtB,EAAUutB,OAGb,CACD,IAAI1I,EAAiBxnB,KAAKumB,iBAAiBxgB,IAAI9C,GAC/C,GAAIukB,EAAgB,CAChB,IAAI2I,GAAwBF,GAhhC3B,QAghC2CA,EAC5CzI,EAAe/jB,QAAQ,SAAUgB,GACzBA,EAAO+hB,SAEN2J,GAAwB1rB,EAAOW,aAAeA,IAEnDzC,EAAQR,KAAKsC,MAazB,OATI8f,GAAenf,KACfzC,EAAUA,EAAQ+V,OAAO,SAAUjU,GAC/B,QAAI8f,GAAeA,GAAe9f,EAAO8f,aAErCnf,GAAeA,GAAeX,EAAOW,gBAK1CzC,GAEXinB,EAA0B/oB,UAAUotB,sBAAwB,SAAU1J,EAAalL,EAAa0U,GAC5F,IAAIqC,EAAK/Z,EAEL0B,EAAcsB,EAAYpW,QAG1BotB,EAAoBhX,EAAY4G,yBAAsB1a,EAAYgf,EAClE+L,EAAoBjX,EAAY4G,yBAAsB1a,EALxC8T,EAAYjU,YAM1BoJ,EAAU,SAAU+hB,GACpB,IAAIttB,EAAUstB,EAAoBttB,QAC9B+sB,EAAmB/sB,IAAY8U,EAC/BpV,EAAU+C,EAAgBqoB,EAAuB9qB,MAC/ButB,EAAOtC,oBAAoBjrB,EAAS+sB,EAAkBK,EAAmBC,EAAmBjX,EAAY/T,SAC9G7B,QAAQ,SAAUgB,GAC9B,IAAIgsB,EAAahsB,EAAOisB,gBACpBD,EAAWE,eACXF,EAAWE,gBAEflsB,EAAO+e,UACP7gB,EAAQR,KAAKsC,MAGjB+rB,EAASxwB,KACb,IACI,IAAK,IAAI4wB,EAt4GrB,SAASC,EAASnvB,GACd,IAAIC,EAAsB,mBAAXC,QAAyBF,EAAEE,OAAOC,UAAWV,EAAI,EAChE,OAAIQ,EAAUA,EAAEJ,KAAKG,IAEjBO,KAAM,WAEF,OADIP,GAAKP,GAAKO,EAAEJ,SAAQI,OAAI,IACnBU,MAAOV,GAAKA,EAAEP,KAAMe,MAAOR,KAg4GtBmvB,CAASxX,EAAYZ,WAAYqY,EAAKF,EAAG3uB,QAAS6uB,EAAG5uB,KAAM4uB,EAAKF,EAAG3uB,OAE7EuM,EAD0BsiB,EAAG1uB,OAIrC,MAAO2uB,GAASX,GAAQ/tB,MAAO0uB,GAC/B,QACI,IACQD,IAAOA,EAAG5uB,OAASmU,EAAKua,EAAGI,SAAS3a,EAAG9U,KAAKqvB,GAEpD,QAAU,GAAIR,EAAK,MAAMA,EAAI/tB,OAIjCqK,EAAYqL,EAAasB,EAAY6G,aAEzC0J,EAA0B/oB,UAAUsuB,gBAAkB,SAAU5K,EAAalL,EAAa0U,EAAuB7B,EAAmBsC,EAAcF,GAC9I,IAAIld,EAAQpR,KACRoF,EAAciU,EAAYjU,YAC1B2S,EAAcsB,EAAYpW,QAG1BguB,KACAC,EAAsB,IAAInhB,IAC1BohB,EAAiB,IAAIphB,IACrBqhB,EAAgB/X,EAAYZ,UAAU9S,IAAI,SAAU4qB,GACpD,IAAIttB,EAAUstB,EAAoBttB,QAClCiuB,EAAoBze,IAAIxP,GAExB,IAAIioB,EAAUjoB,EAAQ4hB,IACtB,GAAIqG,GAAWA,EAAQvG,qBACnB,OAAO,IAAI9kB,EAAW+C,oBAAoB2tB,EAAoBnnB,SAAUmnB,EAAoBlnB,OAChG,IAAI2mB,EAAmB/sB,IAAY8U,EAC/BxO,EAoRhB,SAAS8nB,EAAoB1uB,GACzB,IAAI2uB,KAEJ,OAEJ,SAASC,EAA0B5uB,EAAS2uB,GACxC,IAAK,IAAInwB,EAAI,EAAGA,EAAIwB,EAAQrB,OAAQH,IAAK,CACrC,IAAIsD,EAAS9B,EAAQxB,GACjBsD,aAAkB5E,EAAWgD,sBAC7B0uB,EAA0B9sB,EAAO9B,QAAS2uB,GAG1CA,EAAanvB,KAAKsC,IAV1B8sB,CAA0B5uB,EAAS2uB,GAC5BA,EAvRuBD,EAAqBtD,EAAsBhoB,IAAI9C,IAAYohB,IAC5E1e,IAAI,SAAUnF,GAAK,OAAOA,EAAEkwB,mBAC5BhY,OAAO,SAAUlY,GAMlB,QADSA,EACCyC,SADDzC,EACcyC,UAAYA,IAEnCE,EAAYqrB,EAAazoB,IAAI9C,GAC7BG,EAAakrB,EAAcvoB,IAAI9C,GAC/BC,EAAYJ,EAAmBsO,EAAcA,EAAM4R,YAAa/f,EAASstB,EAAoBrtB,UAAWC,EAAWC,GACnHqB,EAAS2M,EAAMiS,aAAakN,EAAqBrtB,EAAWqG,GAMhE,GAHIgnB,EAAoBpZ,aAAe+U,GACnCiF,EAAe1e,IAAIxP,GAEnB+sB,EAAkB,CAClB,IAAIwB,EAAgB,IAAInL,GAA0B9B,EAAanf,EAAanC,GAC5EuuB,EAAcpC,cAAc3qB,GAC5BwsB,EAAkB9uB,KAAKqvB,GAE3B,OAAO/sB,IAEXwsB,EAAkBxtB,QAAQ,SAAUgB,GAChCiB,EAAgB0L,EAAMwX,wBAAyBnkB,EAAOxB,YAAad,KAAKsC,GACxEA,EAAOM,OAAO,WAAc,OA4GxC,SAAS0sB,EAAmB9rB,EAAKC,EAAKxD,GAClC,IAAIsvB,EACJ,GAAI/rB,aAAeG,KAEf,GADA4rB,EAAgB/rB,EAAII,IAAIH,GACL,CACf,GAAI8rB,EAAcpwB,OAAQ,CACtB,IAAIoiB,EAAQgO,EAActrB,QAAQhE,GAClCsvB,EAActmB,OAAOsY,EAAO,GAEJ,GAAxBgO,EAAcpwB,QACdqE,EAAI6R,OAAO5R,SAKnB8rB,EAAgB/rB,EAAIC,MAEZ8rB,EAAcpwB,SACVoiB,EAAQgO,EAActrB,QAAQhE,GAClCsvB,EAActmB,OAAOsY,EAAO,IAEJ,GAAxBgO,EAAcpwB,eACPqE,EAAIC,IAIvB,OAAO8rB,EAtIoCD,CAAmBrgB,EAAMwX,wBAAyBnkB,EAAOxB,QAASwB,OAEzGysB,EAAoBztB,QAAQ,SAAUR,GAAW,OAAOyiB,GAASziB,EAtrG5C,kBAurGrB,IAAIwB,EAAS/B,EAAoB0uB,GAQjC,OAPA3sB,EAAOO,UAAU,WACbksB,EAAoBztB,QAAQ,SAAUR,GAAW,OAAO0jB,GAAY1jB,EAzrGnD,kBA0rGjBqJ,EAAUyL,EAAasB,EAAY8G,YAIvCgR,EAAe1tB,QAAQ,SAAUR,GAAWyC,EAAgBwmB,EAAmBjpB,MAAad,KAAKsC,KAC1FA,GAEXmlB,EAA0B/oB,UAAUwiB,aAAe,SAAUhK,EAAanW,EAAWqG,GACjF,OAAIrG,EAAU5B,OAAS,EACZtB,KAAK+C,OAAOoG,QAAQkQ,EAAYpW,QAASC,EAAWmW,EAAYjQ,SAAUiQ,EAAYhQ,MAAOgQ,EAAY/P,OAAQC,GAIrH,IAAI1J,EAAW+C,oBAAoByW,EAAYjQ,SAAUiQ,EAAYhQ,QAEzEugB,EAhzBmC,GAkzB1CvD,GAA2C,WAC3C,SAASA,EAA0B9B,EAAanf,EAAanC,GACzDjD,KAAKukB,YAAcA,EACnBvkB,KAAKoF,YAAcA,EACnBpF,KAAKiD,QAAUA,EACfjD,KAAK2xB,QAAU,IAAI9xB,EAAW+C,oBAC9B5C,KAAK4xB,qBAAsB,EAC3B5xB,KAAK6xB,oBACL7xB,KAAKqpB,WAAY,EACjBrpB,KAAKspB,kBAAmB,EACxBtpB,KAAKwF,UAAW,EAChBxF,KAAKwmB,QAAS,EACdxmB,KAAKkF,UAAY,EAuErB,OArEAmhB,EAA0BxlB,UAAUuuB,cAAgB,SAAU3qB,GAC1D,IAAI2M,EAAQpR,KACRA,KAAK4xB,sBAET5xB,KAAK2xB,QAAUltB,EACfrE,OAAO0D,KAAK9D,KAAK6xB,kBAAkBpuB,QAAQ,SAAUkiB,GACjDvU,EAAMygB,iBAAiBlM,GAAOliB,QAAQ,SAAUmB,GAAY,OAAOJ,EAAeC,EAAQkhB,OAAOpgB,EAAWX,OAEhH5E,KAAK6xB,oBACL7xB,KAAK4xB,qBAAsB,EAC3B5xB,KAAK8uB,kBAAkBrqB,EAAOS,WAC9BlF,KAAKwmB,QAAS,IAElBH,EAA0BxlB,UAAU6vB,cAAgB,WAAc,OAAO1wB,KAAK2xB,SAC9EtL,EAA0BxlB,UAAUiuB,kBAAoB,SAAU5pB,GAAalF,KAAKkF,UAAYA,GAChGmhB,EAA0BxlB,UAAU2uB,iBAAmB,SAAU/qB,GAC7D,IAAI2M,EAAQpR,KACRQ,EAAIR,KAAK2xB,QACTnxB,EAAEsxB,iBACFrtB,EAAOI,QAAQ,WAAc,OAAOrE,EAAEsxB,gBAAgB,WAE1DrtB,EAAOM,OAAO,WAAc,OAAOqM,EAAM8S,WACzCzf,EAAOO,UAAU,WAAc,OAAOoM,EAAMoS,aAEhD6C,EAA0BxlB,UAAUkxB,YAAc,SAAUtgB,EAAM7M,GAC9Dc,EAAgB1F,KAAK6xB,iBAAkBpgB,MAAUtP,KAAKyC,IAE1DyhB,EAA0BxlB,UAAUkE,OAAS,SAAU8b,GAC/C7gB,KAAKwmB,QACLxmB,KAAK+xB,YAAY,OAAQlR,GAE7B7gB,KAAK2xB,QAAQ5sB,OAAO8b,IAExBwF,EAA0BxlB,UAAUgE,QAAU,SAAUgc,GAChD7gB,KAAKwmB,QACLxmB,KAAK+xB,YAAY,QAASlR,GAE9B7gB,KAAK2xB,QAAQ9sB,QAAQgc,IAEzBwF,EAA0BxlB,UAAUmE,UAAY,SAAU6b,GAClD7gB,KAAKwmB,QACLxmB,KAAK+xB,YAAY,UAAWlR,GAEhC7gB,KAAK2xB,QAAQ3sB,UAAU6b,IAE3BwF,EAA0BxlB,UAAUsjB,KAAO,WAAcnkB,KAAK2xB,QAAQxN,QACtEkC,EAA0BxlB,UAAUmxB,WAAa,WAAc,OAAOhyB,KAAKwmB,QAAiBxmB,KAAK2xB,QAAQK,cACzG3L,EAA0BxlB,UAAUijB,KAAO,YAAe9jB,KAAKwmB,QAAUxmB,KAAK2xB,QAAQ7N,QACtFuC,EAA0BxlB,UAAUkjB,MAAQ,YAAe/jB,KAAKwmB,QAAUxmB,KAAK2xB,QAAQ5N,SACvFsC,EAA0BxlB,UAAUojB,QAAU,YAAejkB,KAAKwmB,QAAUxmB,KAAK2xB,QAAQ1N,WACzFoC,EAA0BxlB,UAAUqjB,OAAS,WAAclkB,KAAK2xB,QAAQzN,UACxEmC,EAA0BxlB,UAAU2iB,QAAU,WAC1CxjB,KAAKqpB,WAAY,EACjBrpB,KAAK2xB,QAAQnO,WAEjB6C,EAA0BxlB,UAAUmjB,MAAQ,YAAehkB,KAAKwmB,QAAUxmB,KAAK2xB,QAAQ3N,SACvFqC,EAA0BxlB,UAAUujB,YAAc,SAAU5jB,GACnDR,KAAKwmB,QACNxmB,KAAK2xB,QAAQvN,YAAY5jB,IAGjC6lB,EAA0BxlB,UAAUoxB,YAAc,WAAc,OAAOjyB,KAAKwmB,OAAS,EAAIxmB,KAAK2xB,QAAQM,eAEtG5L,EAA0BxlB,UAAUixB,gBAAkB,SAAU7sB,GAC5D,IAAIzE,EAAIR,KAAK2xB,QACTnxB,EAAEsxB,iBACFtxB,EAAEsxB,gBAAgB7sB,IAGnBohB,EAnFmC,GAuH9C,SAAS2E,GAAcrc,GACnB,OAAOA,GAA6B,IAArBA,EAAe,SAKlC,SAASujB,GAAajvB,EAASb,GAC3B,IAAI+vB,EAAWlvB,EAAQkF,MAAMiqB,QAE7B,OADAnvB,EAAQkF,MAAMiqB,QAAmB,MAAThwB,EAAgBA,EAAQ,OACzC+vB,EAEX,SAAS5D,GAAsB8D,EAAWtvB,EAAQsZ,EAAUiW,EAAiBC,GACzE,IAAIC,KACJnW,EAAS5Y,QAAQ,SAAUR,GAAW,OAAOuvB,EAAUrwB,KAAK+vB,GAAajvB,MACzE,IAAIwvB,KACJH,EAAgB7uB,QAAQ,SAAU8Z,EAAOta,GACrC,IAAIwI,KACJ8R,EAAM9Z,QAAQ,SAAUM,GACpB,IAAI3B,EAAQqJ,EAAO1H,GAAQhB,EAAOmG,aAAajG,EAASc,EAAMwuB,GAGzDnwB,GAAyB,GAAhBA,EAAMd,SAChB2B,EAAQ4hB,IAAgBD,GACxB6N,EAAetwB,KAAKc,MAG5BovB,EAAUrsB,IAAI/C,EAASwI,KAI3B,IAAItK,EAAI,EAER,OADAkb,EAAS5Y,QAAQ,SAAUR,GAAW,OAAOivB,GAAajvB,EAASuvB,EAAUrxB,QACtEsxB,EAYX,SAAS7F,GAAa8F,EAAO5F,GACzB,IAAI6F,EAAU,IAAI7sB,IAElB,GADA4sB,EAAMjvB,QAAQ,SAAUspB,GAAQ,OAAO4F,EAAQ3sB,IAAI+mB,QAC/B,GAAhBD,EAAMxrB,OACN,OAAOqxB,EACX,IACIC,EAAU,IAAI7iB,IAAI+c,GAClB+F,EAAe,IAAI/sB,IA0BvB,OANAgnB,EAAMrpB,QAAQ,SAAUkL,GACpB,IAAIoe,EApBR,SAAS+F,EAAQnkB,GACb,IAAKA,EACD,OALQ,EAMZ,IAAIoe,EAAO8F,EAAa9sB,IAAI4I,GAC5B,GAAIoe,EACA,OAAOA,EACX,IAAI7D,EAASva,EAAKma,WAWlB,OATIiE,EADA4F,EAAQtiB,IAAI6Y,GACLA,EAEF0J,EAAQviB,IAAI6Y,GAbT,EAiBD4J,EAAQ5J,GAEnB2J,EAAa7sB,IAAI2I,EAAMoe,GAChBA,EAGI+F,CAAQnkB,GAvBP,IAwBRoe,GACA4F,EAAQ5sB,IAAIgnB,GAAM5qB,KAAKwM,KAGxBgkB,EAEX,IAAII,GAAoB,YACxB,SAASrN,GAASziB,EAAS+pB,GACvB,GAAI/pB,EAAQ+vB,UACR/vB,EAAQ+vB,UAAUvgB,IAAIua,OAErB,CACD,IAAIiG,EAAUhwB,EAAQ8vB,IACjBE,IACDA,EAAUhwB,EAAQ8vB,QAEtBE,EAAQjG,IAAa,GAG7B,SAASrG,GAAY1jB,EAAS+pB,GAC1B,GAAI/pB,EAAQ+vB,UACR/vB,EAAQ+vB,UAAUE,OAAOlG,OAExB,CACD,IAAIiG,EAAUhwB,EAAQ8vB,IAClBE,UACOA,EAAQjG,IAI3B,SAAS+C,GAA8BxH,EAAQtlB,EAASN,GACpDD,EAAoBC,GAASoC,OAAO,WAAc,OAAOwjB,EAAOL,iBAAiBjlB,KA8BrF,SAASorB,GAAuBprB,EAASmpB,EAAqBC,GAC1D,IAAI8G,EAAY9G,EAAqBtmB,IAAI9C,GACzC,IAAKkwB,EACD,OAAO,EACX,IAAIC,EAAWhH,EAAoBrmB,IAAI9C,GAQvC,OAPImwB,EACAD,EAAU1vB,QAAQ,SAAUgC,GAAQ,OAAO2tB,EAAS3gB,IAAIhN,KAGxD2mB,EAAoBpmB,IAAI/C,EAASkwB,GAErC9G,EAAqB7U,OAAOvU,IACrB,EAGX,IAAIowB,GAAiC,WACjC,SAASA,EAAgBtQ,EAAUjS,EAAS9N,GACxC,IAAIoO,EAAQpR,KACZA,KAAK+iB,SAAWA,EAChB/iB,KAAK8Q,QAAUA,EACf9Q,KAAKszB,iBAELtzB,KAAKsqB,kBAAoB,SAAUrnB,EAAS2L,KAC5C5O,KAAKuzB,kBAAoB,IAAI3J,GAA0B7G,EAAUjS,EAAS9N,GAC1EhD,KAAKwzB,gBAAkB,IAAI1Q,GAAwBC,EAAUjS,EAAS9N,GACtEhD,KAAKuzB,kBAAkBjJ,kBAAoB,SAAUrnB,EAAS2L,GAC1D,OAAOwC,EAAMkZ,kBAAkBrnB,EAAS2L,IA+DhD,OA5DAykB,EAAgBxyB,UAAU8pB,gBAAkB,SAAU8I,EAAalP,EAAaa,EAAa3T,EAAMd,GAC/F,IAAI+iB,EAAWD,EAAc,IAAMhiB,EAC/B0U,EAAUnmB,KAAKszB,cAAcI,GACjC,IAAKvN,EAAS,CACV,IAAI9iB,KACA+Q,EAAM1D,GAAkB1Q,KAAK8Q,QAASH,EAAUtN,GACpD,GAAIA,EAAO/B,OACP,MAAM,IAAIgD,MAAM,0BAA6BmN,EAAO,0DAA6DpO,EAAOkB,KAAK,UAEjI4hB,EA9qDZ,SAASwN,EAAaliB,EAAM2C,GACxB,OAAO,IAAI+N,GAAiB1Q,EAAM2C,GA6qDhBuf,CAAaliB,EAAM2C,GAC7BpU,KAAKszB,cAAcI,GAAYvN,EAEnCnmB,KAAKuzB,kBAAkB5I,gBAAgBpG,EAAa9S,EAAM0U,IAE9DkN,EAAgBxyB,UAAUsiB,SAAW,SAAUoB,EAAaa,GACxDplB,KAAKuzB,kBAAkBpQ,SAASoB,EAAaa,IAEjDiO,EAAgBxyB,UAAU2iB,QAAU,SAAUe,EAAa3V,GACvD5O,KAAKuzB,kBAAkB/P,QAAQe,EAAa3V,IAEhDykB,EAAgBxyB,UAAU+yB,SAAW,SAAUrP,EAAathB,EAASimB,EAAQ+B,GACzEjrB,KAAKuzB,kBAAkBtK,WAAW1E,EAAathB,EAASimB,EAAQ+B,IAEpEoI,EAAgBxyB,UAAUgzB,SAAW,SAAUtP,EAAathB,EAAS2L,GACjE5O,KAAKuzB,kBAAkBjL,WAAW/D,EAAathB,EAAS2L,IAE5DykB,EAAgBxyB,UAAUizB,kBAAoB,SAAU7wB,EAAS8wB,GAC7D/zB,KAAKuzB,kBAAkBpI,sBAAsBloB,EAAS8wB,IAE1DV,EAAgBxyB,UAAU4B,QAAU,SAAU8hB,EAAathB,EAAS+wB,EAAU5xB,GAC1E,GAA0B,KAAtB4xB,EAAS1rB,OAAO,GAAW,CAC3B,IAAI+N,EAAK5U,EAAOwE,EAAqB+tB,GAAW,GAEhDh0B,KAAKwzB,gBAAgBttB,QAFoCmQ,EAAG,GAE3BpT,EAFwCoT,EAAG,GACjEjU,QAIXpC,KAAKuzB,kBAAkBpN,QAAQ5B,EAAathB,EAAS+wB,EAAU5xB,IAGvEixB,EAAgBxyB,UAAU8iB,OAAS,SAAUY,EAAathB,EAASyB,EAAWuvB,EAAYrvB,GAEtF,GAA2B,KAAvBF,EAAU4D,OAAO,GAAW,CAC5B,IAAI+N,EAAK5U,EAAOwE,EAAqBvB,GAAY,GACjD,OAAO1E,KAAKwzB,gBAAgB7P,OAD8BtN,EAAG,GACtBpT,EADmCoT,EAAG,GACrBzR,GAE5D,OAAO5E,KAAKuzB,kBAAkB5P,OAAOY,EAAathB,EAASyB,EAAWuvB,EAAYrvB,IAEtFyuB,EAAgBxyB,UAAUgrB,MAAQ,SAAUzC,QACpB,IAAhBA,IAA0BA,GAAe,GAC7CppB,KAAKuzB,kBAAkB1H,MAAMzC,IAEjChpB,OAAO6J,eAAeopB,EAAgBxyB,UAAW,WAC7CkF,IAAK,WACD,OAAO/F,KAAKuzB,kBAAkB5wB,QACzBJ,OAAOvC,KAAKwzB,gBAAgB7wB,UAErC0Y,YAAY,EACZC,cAAc,IAElB+X,EAAgBxyB,UAAU6qB,kBAAoB,WAAc,OAAO1rB,KAAKuzB,kBAAkB7H,qBACnF2H,EA1EyB;;;;;;;OA+FpC,SAASa,GAA2BjxB,EAASwI,GACzC,IAAI0oB,EAAc,KACdC,EAAY,KAUhB,OATI7zB,MAAMoL,QAAQF,IAAWA,EAAOnK,QAChC6yB,EAAcE,GAA0B5oB,EAAO,IAC3CA,EAAOnK,OAAS,IAChB8yB,EAAYC,GAA0B5oB,EAAOA,EAAOnK,OAAS,MAG5DmK,IACL0oB,EAAcE,GAA0B5oB,IAEpC0oB,GAAeC,EAAa,IAAIE,GAAmBrxB,EAASkxB,EAAaC,GAC7E,KAUR,IAAIE,GAAoC,WACpC,SAASA,EAAmBC,EAAUC,EAAcC,GAChDz0B,KAAKu0B,SAAWA,EAChBv0B,KAAKw0B,aAAeA,EACpBx0B,KAAKy0B,WAAaA,EAClBz0B,KAAK00B,OAAS,EACd,IAAIC,EAAgBL,EAAmBM,uBAAuB7uB,IAAIwuB,GAC7DI,GACDL,EAAmBM,uBAAuB5uB,IAAIuuB,EAAUI,MAE5D30B,KAAK60B,eAAiBF,EAsC1B,OApCAL,EAAmBzzB,UAAUse,MAAQ,WAC7Bnf,KAAK00B,OAAS,IACV10B,KAAKw0B,cACLloB,EAAUtM,KAAKu0B,SAAUv0B,KAAKw0B,aAAcx0B,KAAK60B,gBAErD70B,KAAK00B,OAAS,IAGtBJ,EAAmBzzB,UAAUqjB,OAAS,WAClClkB,KAAKmf,QACDnf,KAAK00B,OAAS,IACdpoB,EAAUtM,KAAKu0B,SAAUv0B,KAAK60B,gBAC1B70B,KAAKy0B,aACLnoB,EAAUtM,KAAKu0B,SAAUv0B,KAAKy0B,YAC9Bz0B,KAAKy0B,WAAa,MAEtBz0B,KAAK00B,OAAS,IAGtBJ,EAAmBzzB,UAAU2iB,QAAU,WACnCxjB,KAAKkkB,SACDlkB,KAAK00B,OAAS,IACdJ,EAAmBM,uBAAuBpd,OAAOxX,KAAKu0B,UAClDv0B,KAAKw0B,eACL9nB,EAAY1M,KAAKu0B,SAAUv0B,KAAKw0B,cAChCx0B,KAAKy0B,WAAa,MAElBz0B,KAAKy0B,aACL/nB,EAAY1M,KAAKu0B,SAAUv0B,KAAKy0B,YAChCz0B,KAAKy0B,WAAa,MAEtBnoB,EAAUtM,KAAKu0B,SAAUv0B,KAAK60B,gBAC9B70B,KAAK00B,OAAS,IAGtBJ,EAAmBM,uBAAyB,IAAIE,QACzCR,EAhD4B,GAkDvC,SAASD,GAA0B5oB,GAG/B,IAFA,IAAIrD,EAAS,KACTmV,EAAQnd,OAAO0D,KAAK2H,GACftK,EAAI,EAAGA,EAAIoc,EAAMjc,OAAQH,IAAK,CACnC,IAAI4C,EAAOwZ,EAAMpc,GACb4zB,GAAqBhxB,MACrBqE,EAASA,OACFrE,GAAQ0H,EAAO1H,IAG9B,OAAOqE,EAEX,SAAS2sB,GAAqBhxB,GAC1B,MAAgB,YAATA,GAA+B,aAATA;;;;;;;OAUjC,IACIixB,GAAiB,YACjBC,GAAqB,eAErBC,GAA8C,WAC9C,SAASA,EAA6BX,EAAUY,EAAOC,EAAWC,EAAQC,EAASC,EAAWC,GAC1F,IAAIpkB,EAAQpR,KACZA,KAAKu0B,SAAWA,EAChBv0B,KAAKm1B,MAAQA,EACbn1B,KAAKo1B,UAAYA,EACjBp1B,KAAKq1B,OAASA,EACdr1B,KAAKs1B,QAAUA,EACft1B,KAAKu1B,UAAYA,EACjBv1B,KAAKw1B,UAAYA,EACjBx1B,KAAKy1B,WAAY,EACjBz1B,KAAK01B,YAAa,EAClB11B,KAAK21B,WAAa,EAClB31B,KAAK41B,UAAY,EACjB51B,KAAK61B,SAAW,SAAU9zB,GAAK,OAAOqP,EAAM0kB,gBAAgB/zB,IAqChE,OAnCAmzB,EAA6Br0B,UAAUW,MAAQ,YAyCnD,SAASu0B,EAAuB9yB,EAASb,GACrC,IAAI4zB,EAAOC,GAAkBhzB,EAAS,IAAI2c,OAEtCoW,EAAK10B,SAoDb,SAAS40B,EAAW9zB,EAAO+zB,GAEvB,IADA,IAAIC,EAAQ,EACHj1B,EAAI,EAAGA,EAAIiB,EAAMd,OAAQH,IACtBiB,EAAMkG,OAAOnH,KACXg1B,GACNC,IAER,OAAOA,EA1DKF,CAAWF,EAAM,KAAO,EAChC5zB,EAAQ4zB,EAAO,KAAO5zB,GAE1Bi0B,GAAkBpzB,EAAS,GAAIb,GA/C3B2zB,CAAuB/1B,KAAKu0B,SAAUv0B,KAAKo1B,UAAY,MAAQp1B,KAAKs1B,QAAU,IAAMt1B,KAAKq1B,OAAS,eAAiBr1B,KAAKu1B,UAAY,IAAMv1B,KAAKm1B,OAC/ImB,GAAwBt2B,KAAKu0B,SAAUv0B,KAAK61B,UAAU,GACtD71B,KAAK21B,WAAaY,KAAKC,OAE3BtB,EAA6Br0B,UAAUkjB,MAAQ,WAAc0S,GAAmBz2B,KAAKu0B,SAAUv0B,KAAKm1B,MAAO,WAC3GD,EAA6Br0B,UAAU61B,OAAS,WAAcD,GAAmBz2B,KAAKu0B,SAAUv0B,KAAKm1B,MAAO,YAC5GD,EAA6Br0B,UAAUujB,YAAc,SAAUuS,GAC3D,IAAIjT,EAAQkT,GAAsB52B,KAAKu0B,SAAUv0B,KAAKm1B,OACtDn1B,KAAK41B,UAAYe,EAAW32B,KAAKo1B,UACjCiB,GAAkBr2B,KAAKu0B,SAAU,QAAS,IAAMv0B,KAAK41B,UAAY,KAAMlS,IAE3EwR,EAA6Br0B,UAAUoxB,YAAc,WAAc,OAAOjyB,KAAK41B,WAC/EV,EAA6Br0B,UAAUi1B,gBAAkB,SAAUnxB,GAC/D,IAAIkyB,EAAYlyB,EAAMmyB,wBAA0BP,KAAKC,MACjDO,EAhCO,IAgCOtsB,WAAW9F,EAAMoyB,YAAYC,QAnCjB,IAoC1BryB,EAAMsyB,eAAiBj3B,KAAKm1B,OAC5BvhB,KAAKC,IAAIgjB,EAAY72B,KAAK21B,WAAY,IAAM31B,KAAKq1B,QAAU0B,GAAe/2B,KAAKo1B,WAC/Ep1B,KAAKkkB,UAGbgR,EAA6Br0B,UAAUqjB,OAAS,WACxClkB,KAAKy1B,YAETz1B,KAAKy1B,WAAY,EACjBz1B,KAAKw1B,YACLc,GAAwBt2B,KAAKu0B,SAAUv0B,KAAK61B,UAAU,KAE1DX,EAA6Br0B,UAAU2iB,QAAU,WACzCxjB,KAAK01B,aAET11B,KAAK01B,YAAa,EAClB11B,KAAKkkB,SAmBb,SAASgT,EAAwBj0B,EAASwO,GACtC,IACI0lB,EADOlB,GAAkBhzB,EAAS,IACpB6O,MAAM,KACpB4R,EAAQ0T,GAAuBD,EAAQ1lB,GACvCiS,GAAS,IACTyT,EAAO/rB,OAAOsY,EAAO,GAErB2S,GAAkBpzB,EAAS,GADZk0B,EAAO5yB,KAAK,OAxB3B2yB,CAAwBl3B,KAAKu0B,SAAUv0B,KAAKm1B,SAEzCD,EAnDsC,GAqDjD,SAASuB,GAAmBxzB,EAASwO,EAAM4lB,GAEvChB,GAAkBpzB,EAAS,YAAao0B,EAD5BT,GAAsB3zB,EAASwO,IAuB/C,SAASmlB,GAAsB3zB,EAASb,GACpC,IAAI4zB,EAAOC,GAAkBhzB,EAAS,IACtC,OAAI+yB,EAAK5vB,QAAQ,KAAO,EAEbgxB,GADMpB,EAAKlkB,MAAM,KACc1P,GAEnCg1B,IAAwBpB,GAAO5zB,GAE1C,SAASg1B,GAAuBD,EAAQG,GACpC,IAAK,IAAIn2B,EAAI,EAAGA,EAAIg2B,EAAO71B,OAAQH,IAC/B,GAAIg2B,EAAOh2B,GAAGiF,QAAQkxB,IAAgB,EAClC,OAAOn2B,EAGf,OAAQ,EAEZ,SAASm1B,GAAwBrzB,EAAS4d,EAAI0W,GAC1CA,EAAWt0B,EAAQu0B,oBAAoBvC,GAAoBpU,GACvD5d,EAAQw0B,iBAAiBxC,GAAoBpU,GAErD,SAASwV,GAAkBpzB,EAASwO,EAAMrP,EAAOshB,GAC7C,IAAI3f,EAAOixB,GAAiBvjB,EAC5B,GAAa,MAATiS,EAAe,CACf,IAAIyO,EAAWlvB,EAAQkF,MAAMpE,GAC7B,GAAIouB,EAAS7wB,OAAQ,CACjB,IAAI61B,EAAShF,EAASrgB,MAAM,KAC5BqlB,EAAOzT,GAASthB,EAChBA,EAAQ+0B,EAAO5yB,KAAK,MAG5BtB,EAAQkF,MAAMpE,GAAQ3B,EAE1B,SAAS6zB,GAAkBhzB,EAASwO,GAChC,OAAOxO,EAAQkF,MAAM6sB,GAAiBvjB,GAY1C,IACIimB,GAAiB,SACjBC,GAAoC,WACpC,SAASA,EAAmB10B,EAASC,EAAW+zB,EAAe7B,EAAWC,EAAQ/rB,EAAQsuB,EAAcC,GACpG73B,KAAKiD,QAAUA,EACfjD,KAAKkD,UAAYA,EACjBlD,KAAKi3B,cAAgBA,EACrBj3B,KAAKo1B,UAAYA,EACjBp1B,KAAKq1B,OAASA,EACdr1B,KAAK43B,aAAeA,EACpB53B,KAAK63B,eAAiBA,EACtB73B,KAAK83B,cACL93B,KAAK+3B,eACL/3B,KAAKg4B,iBACLh4B,KAAKi4B,UAAW,EAChBj4B,KAAKk4B,mBACLl4B,KAAK00B,OAAS,EACd10B,KAAKsJ,OAASA,GAAUouB,GACxB13B,KAAKkF,UAAYkwB,EAAYC,EAC7Br1B,KAAKm4B,eAoGT,OAlGAR,EAAmB92B,UAAUgE,QAAU,SAAUgc,GAAM7gB,KAAK+3B,YAAY51B,KAAK0e,IAC7E8W,EAAmB92B,UAAUkE,OAAS,SAAU8b,GAAM7gB,KAAK83B,WAAW31B,KAAK0e,IAC3E8W,EAAmB92B,UAAUmE,UAAY,SAAU6b,GAAM7gB,KAAKg4B,cAAc71B,KAAK0e,IACjF8W,EAAmB92B,UAAU2iB,QAAU,WACnCxjB,KAAKmkB,OACDnkB,KAAK00B,QAAU,IAEnB10B,KAAK00B,OAAS,EACd10B,KAAKo4B,QAAQ5U,UACbxjB,KAAKq4B,iBACLr4B,KAAKs4B,gBACDt4B,KAAK63B,gBACL73B,KAAK63B,eAAerU,UAExBxjB,KAAKg4B,cAAcv0B,QAAQ,SAAUod,GAAM,OAAOA,MAClD7gB,KAAKg4B,mBAETL,EAAmB92B,UAAUy3B,cAAgB,WACzCt4B,KAAK83B,WAAWr0B,QAAQ,SAAUod,GAAM,OAAOA,MAC/C7gB,KAAK83B,eAETH,EAAmB92B,UAAUw3B,eAAiB,WAC1Cr4B,KAAK+3B,YAAYt0B,QAAQ,SAAUod,GAAM,OAAOA,MAChD7gB,KAAK+3B,gBAETJ,EAAmB92B,UAAUqjB,OAAS,WAClClkB,KAAKmkB,OACDnkB,KAAK00B,QAAU,IAEnB10B,KAAK00B,OAAS,EACd10B,KAAKo4B,QAAQlU,SACblkB,KAAKq4B,iBACDr4B,KAAK63B,gBACL73B,KAAK63B,eAAe3T,SAExBlkB,KAAKs4B,kBAETX,EAAmB92B,UAAUujB,YAAc,SAAUhiB,GAASpC,KAAKo4B,QAAQhU,YAAYhiB,IACvFu1B,EAAmB92B,UAAUoxB,YAAc,WAAc,OAAOjyB,KAAKo4B,QAAQnG,eAC7E0F,EAAmB92B,UAAUmxB,WAAa,WAAc,OAAOhyB,KAAK00B,QAAU,GAC9EiD,EAAmB92B,UAAUsjB,KAAO,WAC5BnkB,KAAK00B,QAAU,IAEnB10B,KAAK00B,OAAS,EAEd10B,KAAKo4B,QAAQ52B,QACTxB,KAAKq1B,QACLr1B,KAAKo4B,QAAQrU,UAGrB4T,EAAmB92B,UAAUijB,KAAO,WAChC9jB,KAAKmkB,OACAnkB,KAAKgyB,eACNhyB,KAAKq4B,iBACLr4B,KAAK00B,OAAS,EACV10B,KAAK63B,gBACL73B,KAAK63B,eAAe1Y,SAG5Bnf,KAAKo4B,QAAQ1B,UAEjBiB,EAAmB92B,UAAUkjB,MAAQ,WACjC/jB,KAAKmkB,OACLnkB,KAAKo4B,QAAQrU,SAEjB4T,EAAmB92B,UAAUojB,QAAU,WACnCjkB,KAAKgkB,QACLhkB,KAAK8jB,QAET6T,EAAmB92B,UAAUmjB,MAAQ,WACjChkB,KAAKo4B,QAAQ5U,UACbxjB,KAAKm4B,eACLn4B,KAAKo4B,QAAQ52B,SAEjBm2B,EAAmB92B,UAAUs3B,aAAe,WACxC,IAAI/mB,EAAQpR,KACZA,KAAKo4B,QAAU,IAAIlD,GAA6Bl1B,KAAKiD,QAASjD,KAAKi3B,cAAej3B,KAAKo1B,UAAWp1B,KAAKq1B,OAAQr1B,KAAKsJ,OAjGpG,WAiG+H,WAAc,OAAO8H,EAAM8S,YAG9KyT,EAAmB92B,UAAUixB,gBAAkB,SAAU7sB,GACrD,IAAIszB,EAAuB,SAAbtzB,EAAuBjF,KAAK+3B,YAAc/3B,KAAK83B,WAC7DS,EAAQ90B,QAAQ,SAAUod,GAAM,OAAOA,MACvC0X,EAAQj3B,OAAS,GAErBq2B,EAAmB92B,UAAU8vB,cAAgB,WACzC,IAAIvf,EAAQpR,KACZA,KAAKmkB,OACL,IAAI1Y,KACJ,GAAIzL,KAAKgyB,aAAc,CACnB,IAAIwG,EAAax4B,KAAK00B,QAAU,EAChCt0B,OAAO0D,KAAK9D,KAAK43B,cAAcn0B,QAAQ,SAAUM,GACjC,UAARA,IACA0H,EAAO1H,GAAQy0B,EAAapnB,EAAMwmB,aAAa7zB,GAAQmF,EAAakI,EAAMnO,QAASc,MAI/F/D,KAAKk4B,gBAAkBzsB,GAEpBksB,EArH4B,GAwHnCc,GAAmC,SAAUta,GAE7C,SAASsa,EAAkBx1B,EAASwI,GAChC,IAAI2F,EAAQ+M,EAAO5c,KAAKvB,OAASA,KAKjC,OAJAoR,EAAMnO,QAAUA,EAChBmO,EAAMsnB,mBACNtnB,EAAMunB,eAAgB,EACtBvnB,EAAMwnB,QAAUjwB,EAAoB8C,GAC7B2F,EAqCX,OA5CA1Q,EAAU+3B,EAAmBta,GAS7Bsa,EAAkB53B,UAAUsjB,KAAO,WAC/B,IAAI/S,EAAQpR,MACRA,KAAK24B,eAAkB34B,KAAK04B,kBAEhC14B,KAAK24B,eAAgB,EACrBv4B,OAAO0D,KAAK9D,KAAK44B,SAASn1B,QAAQ,SAAUM,GACxCqN,EAAMsnB,gBAAgB30B,GAAQqN,EAAMnO,QAAQkF,MAAMpE,KAEtDoa,EAAOtd,UAAUsjB,KAAK5iB,KAAKvB,QAE/By4B,EAAkB53B,UAAUijB,KAAO,WAC/B,IAAI1S,EAAQpR,KACPA,KAAK04B,kBAEV14B,KAAKmkB,OACL/jB,OAAO0D,KAAK9D,KAAK44B,SACZn1B,QAAQ,SAAUM,GAAQ,OAAOqN,EAAMnO,QAAQkF,MAAM0wB,YAAY90B,EAAMqN,EAAMwnB,QAAQ70B,MAC1Foa,EAAOtd,UAAUijB,KAAKviB,KAAKvB,QAE/By4B,EAAkB53B,UAAU2iB,QAAU,WAClC,IAAIpS,EAAQpR,KACPA,KAAK04B,kBAEVt4B,OAAO0D,KAAK9D,KAAK04B,iBAAiBj1B,QAAQ,SAAUM,GAChD,IAAI3B,EAAQgP,EAAMsnB,gBAAgB30B,GAC9B3B,EACAgP,EAAMnO,QAAQkF,MAAM0wB,YAAY90B,EAAM3B,GAGtCgP,EAAMnO,QAAQkF,MAAM2wB,eAAe/0B,KAG3C/D,KAAK04B,gBAAkB,KACvBva,EAAOtd,UAAU2iB,QAAQjiB,KAAKvB,QAE3By4B,EA7C2B,CA8CpC54B,EAAW+C,qBAITm2B,GAAoC,WACpC,SAASA,IACL/4B,KAAKg5B,OAAS,EACdh5B,KAAKi5B,MAAQhxB,SAASL,cAAc,QACpC5H,KAAKk5B,gBAAiB,EAgF1B,OA9EAH,EAAmBl4B,UAAUkH,sBAAwB,SAAUhE,GAAQ,OAAOgE,EAAsBhE,IACpGg1B,EAAmBl4B,UAAU2H,eAAiB,SAAUvF,EAAS0D,GAC7D,OAAO6B,EAAevF,EAAS0D,IAEnCoyB,EAAmBl4B,UAAU4H,gBAAkB,SAAUjC,EAAMC,GAAQ,OAAOgC,EAAgBjC,EAAMC,IACpGsyB,EAAmBl4B,UAAUoI,MAAQ,SAAUhG,EAAS0D,EAAUE,GAC9D,OAAO6B,EAAYzF,EAAS0D,EAAUE,IAE1CkyB,EAAmBl4B,UAAUqI,aAAe,SAAUjG,EAASc,EAAM8B,GACjE,OAAO8J,OAAOC,iBAAiB3M,GAASc,IAE5Cg1B,EAAmBl4B,UAAUs4B,qBAAuB,SAAUl2B,EAASwO,EAAMvO,GAEzE,IAAIk2B,EAAc,cAAgB3nB,EAAO,OACrC4nB,EAAM,IAFVn2B,EAAYA,EAAUyC,IAAI,SAAUjC,GAAM,OAAOiF,EAAoBjF,MAG3DD,QAAQ,SAAUC,GACxB21B,EAvBI,IAwBJ,IAAI11B,EAAS8G,WAAW/G,EAAW,QACnC01B,GAAe,GAAKC,EAAe,IAAT11B,EAAe,QACzC01B,GA1BI,IA2BJj5B,OAAO0D,KAAKJ,GAAID,QAAQ,SAAUM,GAC9B,IAAI3B,EAAQsB,EAAGK,GACf,OAAQA,GACJ,IAAK,SACD,OACJ,IAAK,SAID,YAHI3B,IACAg3B,GAAeC,EAAM,8BAAgCj3B,EAAQ,QAGrE,QAEI,YADAg3B,GAAe,GAAKC,EAAMt1B,EAAO,KAAO3B,EAAQ,UAI5Dg3B,GAAeC,EAAM,QAEzBD,GAAe,MACf,IAAIE,EAAQrxB,SAASsxB,cAAc,SAEnC,OADAD,EAAME,UAAYJ,EACXE,GAEXP,EAAmBl4B,UAAUsI,QAAU,SAAUlG,EAASC,EAAWkG,EAAUC,EAAOC,EAAQC,EAAiBC,QACnF,IAApBD,IAA8BA,MAC9BC,GACAxJ,KAAKy5B,wBAET,IAAIC,EAA6BnwB,EAAgBmP,OAAO,SAAUjU,GAAU,OAAOA,aAAkBkzB,KACjGvpB,KACAF,EAA+B9E,EAAUC,IACzCqwB,EAA2Bj2B,QAAQ,SAAUgB,GACzC,IAAIgH,EAAShH,EAAOyzB,gBACpB93B,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAAQ,OAAOqK,EAAerK,GAAQ0H,EAAO1H,OAI3F,IAAIoU,EAwBZ,SAASwhB,EAA2Bz2B,GAChC,IAAI02B,KAWJ,OAVI12B,IACU3C,MAAMoL,QAAQzI,GAAaA,GAAaA,IAC9CO,QAAQ,SAAUC,GAClBtD,OAAO0D,KAAKJ,GAAID,QAAQ,SAAUM,GAClB,UAARA,GAA4B,UAARA,IAExB61B,EAAc71B,GAAQL,EAAGK,QAI9B61B,EApCeD,CADlBz2B,EAAYiL,EAAmClL,EAASC,EAAWkL,IAMnE,GAAgB,GAAZhF,EACA,OAAO,IAAIqvB,GAAkBx1B,EAASkV,GAE1C,IAAI8e,EAxEgB,cAwE6Bj3B,KAAKg5B,SAClDM,EAAQt5B,KAAKm5B,qBAAqBl2B,EAASg0B,EAAe/zB,GAC9D+E,SAASL,cAAc,QAAQiyB,YAAYP,GAC3C,IAAIQ,EAAgB5F,GAA2BjxB,EAASC,GACpDuB,EAAS,IAAIkzB,GAAmB10B,EAASC,EAAW+zB,EAAe7tB,EAAUC,EAAOC,EAAQ6O,EAAa2hB,GAE7G,OADAr1B,EAAOO,UAAU,WAAc,OAyBvC,SAAS+0B,EAAcprB,GACnBA,EAAKma,WAAWkR,YAAYrrB,GA1BcorB,CAAcT,KAC7C70B,GAEXs0B,EAAmBl4B,UAAU44B,sBAAwB,WAC5Cz5B,KAAKk5B,iBACNe,QAAQC,KAAK,oGAAqG,yFAClHl6B,KAAKk5B,gBAAiB,IAGvBH,EApF4B,GAwGnCoB,GAAqC,WACrC,SAASA,EAAoBl3B,EAASC,EAAW8O,EAAS6lB,GACtD73B,KAAKiD,QAAUA,EACfjD,KAAKkD,UAAYA,EACjBlD,KAAKgS,QAAUA,EACfhS,KAAK63B,eAAiBA,EACtB73B,KAAK83B,cACL93B,KAAK+3B,eACL/3B,KAAKg4B,iBACLh4B,KAAKo6B,cAAe,EACpBp6B,KAAKy1B,WAAY,EACjBz1B,KAAKi4B,UAAW,EAChBj4B,KAAK01B,YAAa,EAClB11B,KAAKoc,KAAO,EACZpc,KAAKsvB,aAAe,KACpBtvB,KAAKk4B,mBACLl4B,KAAKo1B,UAAYpjB,EAAkB,SACnChS,KAAKq1B,OAASrjB,EAAe,OAAK,EAClChS,KAAKoc,KAAOpc,KAAKo1B,UAAYp1B,KAAKq1B,OAwHtC,OAtHA8E,EAAoBt5B,UAAUw5B,UAAY,WACjCr6B,KAAKy1B,YACNz1B,KAAKy1B,WAAY,EACjBz1B,KAAK83B,WAAWr0B,QAAQ,SAAUod,GAAM,OAAOA,MAC/C7gB,KAAK83B,gBAGbqC,EAAoBt5B,UAAUsjB,KAAO,WACjCnkB,KAAKqjB,eACLrjB,KAAKs6B,6BAETH,EAAoBt5B,UAAUwiB,aAAe,WACzC,IAAIjS,EAAQpR,KACZ,IAAIA,KAAKo6B,aAAT,CAEAp6B,KAAKo6B,cAAe,EACpB,IAAIl3B,EAAYlD,KAAKkD,UACrBlD,KAAKu6B,UACDv6B,KAAKw6B,qBAAqBx6B,KAAKiD,QAASC,EAAWlD,KAAKgS,SAC5DhS,KAAKy6B,eAAiBv3B,EAAU5B,OAAS4B,EAAUA,EAAU5B,OAAS,MACtEtB,KAAKu6B,UAAU9C,iBAAiB,SAAU,WAAc,OAAOrmB,EAAMipB,gBAEzEF,EAAoBt5B,UAAUy5B,0BAA4B,WAElDt6B,KAAKq1B,OACLr1B,KAAK06B,uBAGL16B,KAAKu6B,UAAUxW,SAIvBoW,EAAoBt5B,UAAU25B,qBAAuB,SAAUv3B,EAASC,EAAW8O,GAG/E,OAAO/O,EAAiB,QAAEC,EAAW8O,IAEzCmoB,EAAoBt5B,UAAUgE,QAAU,SAAUgc,GAAM7gB,KAAK+3B,YAAY51B,KAAK0e,IAC9EsZ,EAAoBt5B,UAAUkE,OAAS,SAAU8b,GAAM7gB,KAAK83B,WAAW31B,KAAK0e,IAC5EsZ,EAAoBt5B,UAAUmE,UAAY,SAAU6b,GAAM7gB,KAAKg4B,cAAc71B,KAAK0e,IAClFsZ,EAAoBt5B,UAAUijB,KAAO,WACjC9jB,KAAKqjB,eACArjB,KAAKgyB,eACNhyB,KAAK+3B,YAAYt0B,QAAQ,SAAUod,GAAM,OAAOA,MAChD7gB,KAAK+3B,eACL/3B,KAAKi4B,UAAW,EACZj4B,KAAK63B,gBACL73B,KAAK63B,eAAe1Y,SAG5Bnf,KAAKu6B,UAAUzW,QAEnBqW,EAAoBt5B,UAAUkjB,MAAQ,WAClC/jB,KAAKmkB,OACLnkB,KAAKu6B,UAAUxW,SAEnBoW,EAAoBt5B,UAAUqjB,OAAS,WACnClkB,KAAKmkB,OACDnkB,KAAK63B,gBACL73B,KAAK63B,eAAe3T,SAExBlkB,KAAKq6B,YACLr6B,KAAKu6B,UAAUrW,UAEnBiW,EAAoBt5B,UAAUmjB,MAAQ,WAClChkB,KAAK06B,uBACL16B,KAAK01B,YAAa,EAClB11B,KAAKy1B,WAAY,EACjBz1B,KAAKi4B,UAAW,GAEpBkC,EAAoBt5B,UAAU65B,qBAAuB,WAC7C16B,KAAKu6B,WACLv6B,KAAKu6B,UAAUI,UAGvBR,EAAoBt5B,UAAUojB,QAAU,WACpCjkB,KAAKgkB,QACLhkB,KAAK8jB,QAETqW,EAAoBt5B,UAAUmxB,WAAa,WAAc,OAAOhyB,KAAKi4B,UACrEkC,EAAoBt5B,UAAU2iB,QAAU,WAC/BxjB,KAAK01B,aACN11B,KAAK01B,YAAa,EAClB11B,KAAK06B,uBACL16B,KAAKq6B,YACDr6B,KAAK63B,gBACL73B,KAAK63B,eAAerU,UAExBxjB,KAAKg4B,cAAcv0B,QAAQ,SAAUod,GAAM,OAAOA,MAClD7gB,KAAKg4B,mBAGbmC,EAAoBt5B,UAAUujB,YAAc,SAAU5jB,GAAKR,KAAKu6B,UAAUppB,YAAc3Q,EAAIR,KAAKoc,MACjG+d,EAAoBt5B,UAAUoxB,YAAc,WAAc,OAAOjyB,KAAKu6B,UAAUppB,YAAcnR,KAAKoc,MACnGhc,OAAO6J,eAAekwB,EAAoBt5B,UAAW,aACjDkF,IAAK,WAAc,OAAO/F,KAAKq1B,OAASr1B,KAAKo1B,WAC7C/Z,YAAY,EACZC,cAAc,IAElB6e,EAAoBt5B,UAAU8vB,cAAgB,WAC1C,IAAIvf,EAAQpR,KACRyL,KACAzL,KAAKgyB,cACL5xB,OAAO0D,KAAK9D,KAAKy6B,gBAAgBh3B,QAAQ,SAAUM,GACnC,UAARA,IACA0H,EAAO1H,GACHqN,EAAMqkB,UAAYrkB,EAAMqpB,eAAe12B,GAAQmF,EAAakI,EAAMnO,QAASc,MAI3F/D,KAAKk4B,gBAAkBzsB,GAG3B0uB,EAAoBt5B,UAAUixB,gBAAkB,SAAU7sB,GACtD,IAAIszB,EAAuB,SAAbtzB,EAAuBjF,KAAK+3B,YAAc/3B,KAAK83B,WAC7DS,EAAQ90B,QAAQ,SAAUod,GAAM,OAAOA,MACvC0X,EAAQj3B,OAAS,GAEd64B,EA1I6B,GA6IpCS,GAAqC,WACrC,SAASA,IACL56B,KAAK66B,cAAgB,8BAA8BC,KAAKC,KAAsB3tB,YAC9EpN,KAAKg7B,oBAAsB,IAAIjC,GAwCnC,OAtCA6B,EAAoB/5B,UAAUkH,sBAAwB,SAAUhE,GAAQ,OAAOgE,EAAsBhE,IACrG62B,EAAoB/5B,UAAU2H,eAAiB,SAAUvF,EAAS0D,GAC9D,OAAO6B,EAAevF,EAAS0D,IAEnCi0B,EAAoB/5B,UAAU4H,gBAAkB,SAAUjC,EAAMC,GAAQ,OAAOgC,EAAgBjC,EAAMC,IACrGm0B,EAAoB/5B,UAAUoI,MAAQ,SAAUhG,EAAS0D,EAAUE,GAC/D,OAAO6B,EAAYzF,EAAS0D,EAAUE,IAE1C+zB,EAAoB/5B,UAAUqI,aAAe,SAAUjG,EAASc,EAAM8B,GAClE,OAAO8J,OAAOC,iBAAiB3M,GAASc,IAE5C62B,EAAoB/5B,UAAUo6B,6BAA+B,SAAUC,GAAal7B,KAAK66B,cAAgBK,GACzGN,EAAoB/5B,UAAUsI,QAAU,SAAUlG,EAASC,EAAWkG,EAAUC,EAAOC,EAAQC,EAAiBC,GAG5G,QAFwB,IAApBD,IAA8BA,OACdC,IAA4BxJ,KAAK66B,cAEjD,OAAO76B,KAAKg7B,oBAAoB7xB,QAAQlG,EAASC,EAAWkG,EAAUC,EAAOC,EAAQC,GAEzF,IACI4xB,GAAkB/xB,SAAUA,EAAUC,MAAOA,EAAO+xB,KADpC,GAAT/xB,EAAa,OAAS,YAI7BC,IACA6xB,EAAsB,OAAI7xB,GAE9B,IAAI8E,KACAitB,EAA8B9xB,EAAgBmP,OAAO,SAAUjU,GAAU,OAAOA,aAAkB01B,KAClGjsB,EAA+B9E,EAAUC,IACzCgyB,EAA4B53B,QAAQ,SAAUgB,GAC1C,IAAIgH,EAAShH,EAAOyzB,gBACpB93B,OAAO0D,KAAK2H,GAAQhI,QAAQ,SAAUM,GAAQ,OAAOqK,EAAerK,GAAQ0H,EAAO1H,OAK3F,IAAI+1B,EAAgB5F,GAA2BjxB,EAD/CC,EAAYiL,EAAmClL,EAD/CC,EAAYA,EAAUyC,IAAI,SAAU8F,GAAU,OAAOG,EAAWH,GAAQ,KACL2C,IAEnE,OAAO,IAAI+rB,GAAoBl3B,EAASC,EAAWi4B,EAAerB,IAE/Dc,EA3C6B,GAgDxC,SAASG,KACL,OAn9IJ,SAASO,IACL,MAA0B,oBAAX3rB,aAAqD,IAApBA,OAAO1H,SAk9I/CqzB,IAAev0B,QAAQlG,UAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCtDvB,EAAQi8B,+CAAiDjH,GACzDh1B,EAAQ6K,gBAAkBA,EAC1B7K,EAAQk8B,iBAAmBrxB,EAC3B7K,EAAQm8B,WAAa3c,GACrBxf,EAAQo8B,0BAA4Brc,GACpC/f,EAAQq8B,8BAAgCrc,GACxChgB,EAAQs8B,8BAAgClc,GACxCpgB,EAAQu8B,qBAAuB7yB,EAC/B1J,EAAQw8B,iBAAmBzI,GAC3B/zB,EAAQy8B,oBAAsBhD,GAC9Bz5B,EAAQ08B,oBAAsBrE,GAC9Br4B,EAAQ28B,iBAAmBxzB,EAC3BnJ,EAAQ48B,aAAexzB,EACvBpJ,EAAQ68B,gBAAkB3zB,EAC1BlJ,EAAQ88B,uBAAyBr0B,EACjCzI,EAAQ+8B,qBAAuBzB,GAC/Bt7B,EAAQg9B,uBA3DR,SAASC,KACL,MAAwC,mBAA1BxB,MA2DlBz7B,EAAQk9B,qBAAuBrC,GAC/B76B,EAAQm9B,gCAAkCvuB,EAE1C9N,OAAO6J,eAAe3K,EAAS,cAAgB8C,OAAO", "sourcesContent": ["/**\n * @license Angular v7.2.16\n * (c) 2010-2019 Google LLC. https://angular.io/\n * License: MIT\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@angular/animations'), require('@angular/core')) :\n    typeof define === 'function' && define.amd ? define('@angular/animations/browser', ['exports', '@angular/animations', '@angular/core'], factory) :\n    (global = global || self, factory((global.ng = global.ng || {}, global.ng.animations = global.ng.animations || {}, global.ng.animations.browser = {}), global.ng.animations, global.ng.core));\n}(this, function (exports, animations, core) { 'use strict';\n\n    /*! *****************************************************************************\r\n    Copyright (c) Microsoft Corporation. All rights reserved.\r\n    Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\n    this file except in compliance with the License. You may obtain a copy of the\r\n    License at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\n    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\n    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\n    MERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\n    See the Apache Version 2.0 License for specific language governing permissions\r\n    and limitations under the License.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise */\r\n\r\n    var extendStatics = function(d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n\r\n    function __extends(d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    }\r\n\r\n    var __assign = function() {\r\n        __assign = Object.assign || function __assign(t) {\r\n            for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n                s = arguments[i];\r\n                for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n            }\r\n            return t;\r\n        };\r\n        return __assign.apply(this, arguments);\r\n    };\r\n\r\n    function __decorate(decorators, target, key, desc) {\r\n        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n        if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n        return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n    }\r\n\r\n    function __values(o) {\r\n        var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n        if (m) return m.call(o);\r\n        return {\r\n            next: function () {\r\n                if (o && i >= o.length) o = void 0;\r\n                return { value: o && o[i++], done: !o };\r\n            }\r\n        };\r\n    }\r\n\r\n    function __read(o, n) {\r\n        var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n        if (!m) return o;\r\n        var i = m.call(o), r, ar = [], e;\r\n        try {\r\n            while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n        }\r\n        catch (error) { e = { error: error }; }\r\n        finally {\r\n            try {\r\n                if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n            }\r\n            finally { if (e) throw e.error; }\r\n        }\r\n        return ar;\r\n    }\r\n\r\n    function __spread() {\r\n        for (var ar = [], i = 0; i < arguments.length; i++)\r\n            ar = ar.concat(__read(arguments[i]));\r\n        return ar;\r\n    }\n\n    function isBrowser() {\n        return (typeof window !== 'undefined' && typeof window.document !== 'undefined');\n    }\n    function isNode() {\n        return (typeof process !== 'undefined');\n    }\n    function optimizeGroupPlayer(players) {\n        switch (players.length) {\n            case 0:\n                return new animations.NoopAnimationPlayer();\n            case 1:\n                return players[0];\n            default:\n                return new animations.ɵAnimationGroupPlayer(players);\n        }\n    }\n    function normalizeKeyframes(driver, normalizer, element, keyframes, preStyles, postStyles) {\n        if (preStyles === void 0) { preStyles = {}; }\n        if (postStyles === void 0) { postStyles = {}; }\n        var errors = [];\n        var normalizedKeyframes = [];\n        var previousOffset = -1;\n        var previousKeyframe = null;\n        keyframes.forEach(function (kf) {\n            var offset = kf['offset'];\n            var isSameOffset = offset == previousOffset;\n            var normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n            Object.keys(kf).forEach(function (prop) {\n                var normalizedProp = prop;\n                var normalizedValue = kf[prop];\n                if (prop !== 'offset') {\n                    normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                    switch (normalizedValue) {\n                        case animations.ɵPRE_STYLE:\n                            normalizedValue = preStyles[prop];\n                            break;\n                        case animations.AUTO_STYLE:\n                            normalizedValue = postStyles[prop];\n                            break;\n                        default:\n                            normalizedValue =\n                                normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                            break;\n                    }\n                }\n                normalizedKeyframe[normalizedProp] = normalizedValue;\n            });\n            if (!isSameOffset) {\n                normalizedKeyframes.push(normalizedKeyframe);\n            }\n            previousKeyframe = normalizedKeyframe;\n            previousOffset = offset;\n        });\n        if (errors.length) {\n            var LINE_START = '\\n - ';\n            throw new Error(\"Unable to animate due to the following errors:\" + LINE_START + errors.join(LINE_START));\n        }\n        return normalizedKeyframes;\n    }\n    function listenOnPlayer(player, eventName, event, callback) {\n        switch (eventName) {\n            case 'start':\n                player.onStart(function () { return callback(event && copyAnimationEvent(event, 'start', player)); });\n                break;\n            case 'done':\n                player.onDone(function () { return callback(event && copyAnimationEvent(event, 'done', player)); });\n                break;\n            case 'destroy':\n                player.onDestroy(function () { return callback(event && copyAnimationEvent(event, 'destroy', player)); });\n                break;\n        }\n    }\n    function copyAnimationEvent(e, phaseName, player) {\n        var totalTime = player.totalTime;\n        var disabled = player.disabled ? true : false;\n        var event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n        var data = e['_data'];\n        if (data != null) {\n            event['_data'] = data;\n        }\n        return event;\n    }\n    function makeAnimationEvent(element, triggerName, fromState, toState, phaseName, totalTime, disabled) {\n        if (phaseName === void 0) { phaseName = ''; }\n        if (totalTime === void 0) { totalTime = 0; }\n        return { element: element, triggerName: triggerName, fromState: fromState, toState: toState, phaseName: phaseName, totalTime: totalTime, disabled: !!disabled };\n    }\n    function getOrSetAsInMap(map, key, defaultValue) {\n        var value;\n        if (map instanceof Map) {\n            value = map.get(key);\n            if (!value) {\n                map.set(key, value = defaultValue);\n            }\n        }\n        else {\n            value = map[key];\n            if (!value) {\n                value = map[key] = defaultValue;\n            }\n        }\n        return value;\n    }\n    function parseTimelineCommand(command) {\n        var separatorPos = command.indexOf(':');\n        var id = command.substring(1, separatorPos);\n        var action = command.substr(separatorPos + 1);\n        return [id, action];\n    }\n    var _contains = function (elm1, elm2) { return false; };\n    var _matches = function (element, selector) {\n        return false;\n    };\n    var _query = function (element, selector, multi) {\n        return [];\n    };\n    // Define utility methods for browsers and platform-server(domino) where Element\n    // and utility methods exist.\n    var _isNode = isNode();\n    if (_isNode || typeof Element !== 'undefined') {\n        // this is well supported in all browsers\n        _contains = function (elm1, elm2) { return elm1.contains(elm2); };\n        if (_isNode || Element.prototype.matches) {\n            _matches = function (element, selector) { return element.matches(selector); };\n        }\n        else {\n            var proto = Element.prototype;\n            var fn_1 = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n                proto.oMatchesSelector || proto.webkitMatchesSelector;\n            if (fn_1) {\n                _matches = function (element, selector) { return fn_1.apply(element, [selector]); };\n            }\n        }\n        _query = function (element, selector, multi) {\n            var results = [];\n            if (multi) {\n                results.push.apply(results, __spread(element.querySelectorAll(selector)));\n            }\n            else {\n                var elm = element.querySelector(selector);\n                if (elm) {\n                    results.push(elm);\n                }\n            }\n            return results;\n        };\n    }\n    function containsVendorPrefix(prop) {\n        // Webkit is the only real popular vendor prefix nowadays\n        // cc: http://shouldiprefix.com/\n        return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n    }\n    var _CACHED_BODY = null;\n    var _IS_WEBKIT = false;\n    function validateStyleProperty(prop) {\n        if (!_CACHED_BODY) {\n            _CACHED_BODY = getBodyNode() || {};\n            _IS_WEBKIT = _CACHED_BODY.style ? ('WebkitAppearance' in _CACHED_BODY.style) : false;\n        }\n        var result = true;\n        if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n            result = prop in _CACHED_BODY.style;\n            if (!result && _IS_WEBKIT) {\n                var camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n                result = camelProp in _CACHED_BODY.style;\n            }\n        }\n        return result;\n    }\n    function getBodyNode() {\n        if (typeof document != 'undefined') {\n            return document.body;\n        }\n        return null;\n    }\n    var matchesElement = _matches;\n    var containsElement = _contains;\n    var invokeQuery = _query;\n    function hypenatePropsObject(object) {\n        var newObj = {};\n        Object.keys(object).forEach(function (prop) {\n            var newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n            newObj[newProp] = object[prop];\n        });\n        return newObj;\n    }\n\n    /**\n     * @publicApi\n     */\n    var NoopAnimationDriver = /** @class */ (function () {\n        function NoopAnimationDriver() {\n        }\n        NoopAnimationDriver.prototype.validateStyleProperty = function (prop) { return validateStyleProperty(prop); };\n        NoopAnimationDriver.prototype.matchesElement = function (element, selector) {\n            return matchesElement(element, selector);\n        };\n        NoopAnimationDriver.prototype.containsElement = function (elm1, elm2) { return containsElement(elm1, elm2); };\n        NoopAnimationDriver.prototype.query = function (element, selector, multi) {\n            return invokeQuery(element, selector, multi);\n        };\n        NoopAnimationDriver.prototype.computeStyle = function (element, prop, defaultValue) {\n            return defaultValue || '';\n        };\n        NoopAnimationDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers, scrubberAccessRequested) {\n            if (previousPlayers === void 0) { previousPlayers = []; }\n            return new animations.NoopAnimationPlayer(duration, delay);\n        };\n        NoopAnimationDriver = __decorate([\n            core.Injectable()\n        ], NoopAnimationDriver);\n        return NoopAnimationDriver;\n    }());\n    /**\n     * @publicApi\n     */\n    var AnimationDriver = /** @class */ (function () {\n        function AnimationDriver() {\n        }\n        AnimationDriver.NOOP = new NoopAnimationDriver();\n        return AnimationDriver;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var ONE_SECOND = 1000;\n    var SUBSTITUTION_EXPR_START = '{{';\n    var SUBSTITUTION_EXPR_END = '}}';\n    var ENTER_CLASSNAME = 'ng-enter';\n    var LEAVE_CLASSNAME = 'ng-leave';\n    var NG_TRIGGER_CLASSNAME = 'ng-trigger';\n    var NG_TRIGGER_SELECTOR = '.ng-trigger';\n    var NG_ANIMATING_CLASSNAME = 'ng-animating';\n    var NG_ANIMATING_SELECTOR = '.ng-animating';\n    function resolveTimingValue(value) {\n        if (typeof value == 'number')\n            return value;\n        var matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n        if (!matches || matches.length < 2)\n            return 0;\n        return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n    }\n    function _convertTimeValueToMS(value, unit) {\n        switch (unit) {\n            case 's':\n                return value * ONE_SECOND;\n            default: // ms or something else\n                return value;\n        }\n    }\n    function resolveTiming(timings, errors, allowNegativeValues) {\n        return timings.hasOwnProperty('duration') ?\n            timings :\n            parseTimeExpression(timings, errors, allowNegativeValues);\n    }\n    function parseTimeExpression(exp, errors, allowNegativeValues) {\n        var regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n        var duration;\n        var delay = 0;\n        var easing = '';\n        if (typeof exp === 'string') {\n            var matches = exp.match(regex);\n            if (matches === null) {\n                errors.push(\"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n                return { duration: 0, delay: 0, easing: '' };\n            }\n            duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n            var delayMatch = matches[3];\n            if (delayMatch != null) {\n                delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n            }\n            var easingVal = matches[5];\n            if (easingVal) {\n                easing = easingVal;\n            }\n        }\n        else {\n            duration = exp;\n        }\n        if (!allowNegativeValues) {\n            var containsErrors = false;\n            var startIndex = errors.length;\n            if (duration < 0) {\n                errors.push(\"Duration values below 0 are not allowed for this animation step.\");\n                containsErrors = true;\n            }\n            if (delay < 0) {\n                errors.push(\"Delay values below 0 are not allowed for this animation step.\");\n                containsErrors = true;\n            }\n            if (containsErrors) {\n                errors.splice(startIndex, 0, \"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n            }\n        }\n        return { duration: duration, delay: delay, easing: easing };\n    }\n    function copyObj(obj, destination) {\n        if (destination === void 0) { destination = {}; }\n        Object.keys(obj).forEach(function (prop) { destination[prop] = obj[prop]; });\n        return destination;\n    }\n    function normalizeStyles(styles) {\n        var normalizedStyles = {};\n        if (Array.isArray(styles)) {\n            styles.forEach(function (data) { return copyStyles(data, false, normalizedStyles); });\n        }\n        else {\n            copyStyles(styles, false, normalizedStyles);\n        }\n        return normalizedStyles;\n    }\n    function copyStyles(styles, readPrototype, destination) {\n        if (destination === void 0) { destination = {}; }\n        if (readPrototype) {\n            // we make use of a for-in loop so that the\n            // prototypically inherited properties are\n            // revealed from the backFill map\n            for (var prop in styles) {\n                destination[prop] = styles[prop];\n            }\n        }\n        else {\n            copyObj(styles, destination);\n        }\n        return destination;\n    }\n    function getStyleAttributeString(element, key, value) {\n        // Return the key-value pair string to be added to the style attribute for the\n        // given CSS style key.\n        if (value) {\n            return key + ':' + value + ';';\n        }\n        else {\n            return '';\n        }\n    }\n    function writeStyleAttribute(element) {\n        // Read the style property of the element and manually reflect it to the\n        // style attribute. This is needed because Domino on platform-server doesn't\n        // understand the full set of allowed CSS properties and doesn't reflect some\n        // of them automatically.\n        var styleAttrValue = '';\n        for (var i = 0; i < element.style.length; i++) {\n            var key = element.style.item(i);\n            styleAttrValue += getStyleAttributeString(element, key, element.style.getPropertyValue(key));\n        }\n        for (var key in element.style) {\n            // Skip internal Domino properties that don't need to be reflected.\n            if (!element.style.hasOwnProperty(key) || key.startsWith('_')) {\n                continue;\n            }\n            var dashKey = camelCaseToDashCase(key);\n            styleAttrValue += getStyleAttributeString(element, dashKey, element.style[key]);\n        }\n        element.setAttribute('style', styleAttrValue);\n    }\n    function setStyles(element, styles, formerStyles) {\n        if (element['style']) {\n            Object.keys(styles).forEach(function (prop) {\n                var camelProp = dashCaseToCamelCase(prop);\n                if (formerStyles && !formerStyles.hasOwnProperty(prop)) {\n                    formerStyles[prop] = element.style[camelProp];\n                }\n                element.style[camelProp] = styles[prop];\n            });\n            // On the server set the 'style' attribute since it's not automatically reflected.\n            if (isNode()) {\n                writeStyleAttribute(element);\n            }\n        }\n    }\n    function eraseStyles(element, styles) {\n        if (element['style']) {\n            Object.keys(styles).forEach(function (prop) {\n                var camelProp = dashCaseToCamelCase(prop);\n                element.style[camelProp] = '';\n            });\n            // On the server set the 'style' attribute since it's not automatically reflected.\n            if (isNode()) {\n                writeStyleAttribute(element);\n            }\n        }\n    }\n    function normalizeAnimationEntry(steps) {\n        if (Array.isArray(steps)) {\n            if (steps.length == 1)\n                return steps[0];\n            return animations.sequence(steps);\n        }\n        return steps;\n    }\n    function validateStyleParams(value, options, errors) {\n        var params = options.params || {};\n        var matches = extractStyleParams(value);\n        if (matches.length) {\n            matches.forEach(function (varName) {\n                if (!params.hasOwnProperty(varName)) {\n                    errors.push(\"Unable to resolve the local animation param \" + varName + \" in the given list of values\");\n                }\n            });\n        }\n    }\n    var PARAM_REGEX = new RegExp(SUBSTITUTION_EXPR_START + \"\\\\s*(.+?)\\\\s*\" + SUBSTITUTION_EXPR_END, 'g');\n    function extractStyleParams(value) {\n        var params = [];\n        if (typeof value === 'string') {\n            var val = value.toString();\n            var match = void 0;\n            while (match = PARAM_REGEX.exec(val)) {\n                params.push(match[1]);\n            }\n            PARAM_REGEX.lastIndex = 0;\n        }\n        return params;\n    }\n    function interpolateParams(value, params, errors) {\n        var original = value.toString();\n        var str = original.replace(PARAM_REGEX, function (_, varName) {\n            var localVal = params[varName];\n            // this means that the value was never overridden by the data passed in by the user\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(\"Please provide a value for the animation param \" + varName);\n                localVal = '';\n            }\n            return localVal.toString();\n        });\n        // we do this to assert that numeric values stay as they are\n        return str == original ? value : str;\n    }\n    function iteratorToArray(iterator) {\n        var arr = [];\n        var item = iterator.next();\n        while (!item.done) {\n            arr.push(item.value);\n            item = iterator.next();\n        }\n        return arr;\n    }\n    var DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n    function dashCaseToCamelCase(input) {\n        return input.replace(DASH_CASE_REGEXP, function () {\n            var m = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                m[_i] = arguments[_i];\n            }\n            return m[1].toUpperCase();\n        });\n    }\n    function camelCaseToDashCase(input) {\n        return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n    }\n    function allowPreviousPlayerStylesMerge(duration, delay) {\n        return duration === 0 || delay === 0;\n    }\n    function balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n        var previousStyleProps = Object.keys(previousStyles);\n        if (previousStyleProps.length && keyframes.length) {\n            var startingKeyframe_1 = keyframes[0];\n            var missingStyleProps_1 = [];\n            previousStyleProps.forEach(function (prop) {\n                if (!startingKeyframe_1.hasOwnProperty(prop)) {\n                    missingStyleProps_1.push(prop);\n                }\n                startingKeyframe_1[prop] = previousStyles[prop];\n            });\n            if (missingStyleProps_1.length) {\n                var _loop_1 = function () {\n                    var kf = keyframes[i];\n                    missingStyleProps_1.forEach(function (prop) { kf[prop] = computeStyle(element, prop); });\n                };\n                // tslint:disable-next-line\n                for (var i = 1; i < keyframes.length; i++) {\n                    _loop_1();\n                }\n            }\n        }\n        return keyframes;\n    }\n    function visitDslNode(visitor, node, context) {\n        switch (node.type) {\n            case 7 /* Trigger */:\n                return visitor.visitTrigger(node, context);\n            case 0 /* State */:\n                return visitor.visitState(node, context);\n            case 1 /* Transition */:\n                return visitor.visitTransition(node, context);\n            case 2 /* Sequence */:\n                return visitor.visitSequence(node, context);\n            case 3 /* Group */:\n                return visitor.visitGroup(node, context);\n            case 4 /* Animate */:\n                return visitor.visitAnimate(node, context);\n            case 5 /* Keyframes */:\n                return visitor.visitKeyframes(node, context);\n            case 6 /* Style */:\n                return visitor.visitStyle(node, context);\n            case 8 /* Reference */:\n                return visitor.visitReference(node, context);\n            case 9 /* AnimateChild */:\n                return visitor.visitAnimateChild(node, context);\n            case 10 /* AnimateRef */:\n                return visitor.visitAnimateRef(node, context);\n            case 11 /* Query */:\n                return visitor.visitQuery(node, context);\n            case 12 /* Stagger */:\n                return visitor.visitStagger(node, context);\n            default:\n                throw new Error(\"Unable to resolve animation metadata node #\" + node.type);\n        }\n    }\n    function computeStyle(element, prop) {\n        return window.getComputedStyle(element)[prop];\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var ANY_STATE = '*';\n    function parseTransitionExpr(transitionValue, errors) {\n        var expressions = [];\n        if (typeof transitionValue == 'string') {\n            transitionValue\n                .split(/\\s*,\\s*/)\n                .forEach(function (str) { return parseInnerTransitionStr(str, expressions, errors); });\n        }\n        else {\n            expressions.push(transitionValue);\n        }\n        return expressions;\n    }\n    function parseInnerTransitionStr(eventStr, expressions, errors) {\n        if (eventStr[0] == ':') {\n            var result = parseAnimationAlias(eventStr, errors);\n            if (typeof result == 'function') {\n                expressions.push(result);\n                return;\n            }\n            eventStr = result;\n        }\n        var match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n        if (match == null || match.length < 4) {\n            errors.push(\"The provided transition expression \\\"\" + eventStr + \"\\\" is not supported\");\n            return expressions;\n        }\n        var fromState = match[1];\n        var separator = match[2];\n        var toState = match[3];\n        expressions.push(makeLambdaFromStates(fromState, toState));\n        var isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n        if (separator[0] == '<' && !isFullAnyStateExpr) {\n            expressions.push(makeLambdaFromStates(toState, fromState));\n        }\n    }\n    function parseAnimationAlias(alias, errors) {\n        switch (alias) {\n            case ':enter':\n                return 'void => *';\n            case ':leave':\n                return '* => void';\n            case ':increment':\n                return function (fromState, toState) { return parseFloat(toState) > parseFloat(fromState); };\n            case ':decrement':\n                return function (fromState, toState) { return parseFloat(toState) < parseFloat(fromState); };\n            default:\n                errors.push(\"The transition alias value \\\"\" + alias + \"\\\" is not supported\");\n                return '* => *';\n        }\n    }\n    // DO NOT REFACTOR ... keep the follow set instantiations\n    // with the values intact (closure compiler for some reason\n    // removes follow-up lines that add the values outside of\n    // the constructor...\n    var TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\n    var FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\n    function makeLambdaFromStates(lhs, rhs) {\n        var LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n        var RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n        return function (fromState, toState) {\n            var lhsMatch = lhs == ANY_STATE || lhs == fromState;\n            var rhsMatch = rhs == ANY_STATE || rhs == toState;\n            if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n                lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n            }\n            if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n                rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n            }\n            return lhsMatch && rhsMatch;\n        };\n    }\n\n    var SELF_TOKEN = ':self';\n    var SELF_TOKEN_REGEX = new RegExp(\"s*\" + SELF_TOKEN + \"s*,?\", 'g');\n    /*\n     * [Validation]\n     * The visitor code below will traverse the animation AST generated by the animation verb functions\n     * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n     * following corner-cases will be validated:\n     *\n     * 1. Overlap of animations\n     * Given that a CSS property cannot be animated in more than one place at the same time, it's\n     * important that this behavior is detected and validated. The way in which this occurs is that\n     * each time a style property is examined, a string-map containing the property will be updated with\n     * the start and end times for when the property is used within an animation step.\n     *\n     * If there are two or more parallel animations that are currently running (these are invoked by the\n     * group()) on the same element then the validator will throw an error. Since the start/end timing\n     * values are collected for each property then if the current animation step is animating the same\n     * property and its timing values fall anywhere into the window of time that the property is\n     * currently being animated within then this is what causes an error.\n     *\n     * 2. Timing values\n     * The validator will validate to see if a timing value of `duration delay easing` or\n     * `durationNumber` is valid or not.\n     *\n     * (note that upon validation the code below will replace the timing data with an object containing\n     * {duration,delay,easing}.\n     *\n     * 3. Offset Validation\n     * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n     * Offsets within keyframes() are considered valid when:\n     *\n     *   - No offsets are used at all\n     *   - Each style() entry contains an offset value\n     *   - Each offset is between 0 and 1\n     *   - Each offset is greater to or equal than the previous one\n     *\n     * Otherwise an error will be thrown.\n     */\n    function buildAnimationAst(driver, metadata, errors) {\n        return new AnimationAstBuilderVisitor(driver).build(metadata, errors);\n    }\n    var ROOT_SELECTOR = '';\n    var AnimationAstBuilderVisitor = /** @class */ (function () {\n        function AnimationAstBuilderVisitor(_driver) {\n            this._driver = _driver;\n        }\n        AnimationAstBuilderVisitor.prototype.build = function (metadata, errors) {\n            var context = new AnimationAstBuilderContext(errors);\n            this._resetContextStyleTimingState(context);\n            return visitDslNode(this, normalizeAnimationEntry(metadata), context);\n        };\n        AnimationAstBuilderVisitor.prototype._resetContextStyleTimingState = function (context) {\n            context.currentQuerySelector = ROOT_SELECTOR;\n            context.collectedStyles = {};\n            context.collectedStyles[ROOT_SELECTOR] = {};\n            context.currentTime = 0;\n        };\n        AnimationAstBuilderVisitor.prototype.visitTrigger = function (metadata, context) {\n            var _this = this;\n            var queryCount = context.queryCount = 0;\n            var depCount = context.depCount = 0;\n            var states = [];\n            var transitions = [];\n            if (metadata.name.charAt(0) == '@') {\n                context.errors.push('animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n            }\n            metadata.definitions.forEach(function (def) {\n                _this._resetContextStyleTimingState(context);\n                if (def.type == 0 /* State */) {\n                    var stateDef_1 = def;\n                    var name_1 = stateDef_1.name;\n                    name_1.toString().split(/\\s*,\\s*/).forEach(function (n) {\n                        stateDef_1.name = n;\n                        states.push(_this.visitState(stateDef_1, context));\n                    });\n                    stateDef_1.name = name_1;\n                }\n                else if (def.type == 1 /* Transition */) {\n                    var transition = _this.visitTransition(def, context);\n                    queryCount += transition.queryCount;\n                    depCount += transition.depCount;\n                    transitions.push(transition);\n                }\n                else {\n                    context.errors.push('only state() and transition() definitions can sit inside of a trigger()');\n                }\n            });\n            return {\n                type: 7 /* Trigger */,\n                name: metadata.name, states: states, transitions: transitions, queryCount: queryCount, depCount: depCount,\n                options: null\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitState = function (metadata, context) {\n            var styleAst = this.visitStyle(metadata.styles, context);\n            var astParams = (metadata.options && metadata.options.params) || null;\n            if (styleAst.containsDynamicStyles) {\n                var missingSubs_1 = new Set();\n                var params_1 = astParams || {};\n                styleAst.styles.forEach(function (value) {\n                    if (isObject(value)) {\n                        var stylesObj_1 = value;\n                        Object.keys(stylesObj_1).forEach(function (prop) {\n                            extractStyleParams(stylesObj_1[prop]).forEach(function (sub) {\n                                if (!params_1.hasOwnProperty(sub)) {\n                                    missingSubs_1.add(sub);\n                                }\n                            });\n                        });\n                    }\n                });\n                if (missingSubs_1.size) {\n                    var missingSubsArr = iteratorToArray(missingSubs_1.values());\n                    context.errors.push(\"state(\\\"\" + metadata.name + \"\\\", ...) must define default values for all the following style substitutions: \" + missingSubsArr.join(', '));\n                }\n            }\n            return {\n                type: 0 /* State */,\n                name: metadata.name,\n                style: styleAst,\n                options: astParams ? { params: astParams } : null\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitTransition = function (metadata, context) {\n            context.queryCount = 0;\n            context.depCount = 0;\n            var animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n            var matchers = parseTransitionExpr(metadata.expr, context.errors);\n            return {\n                type: 1 /* Transition */,\n                matchers: matchers,\n                animation: animation,\n                queryCount: context.queryCount,\n                depCount: context.depCount,\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitSequence = function (metadata, context) {\n            var _this = this;\n            return {\n                type: 2 /* Sequence */,\n                steps: metadata.steps.map(function (s) { return visitDslNode(_this, s, context); }),\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitGroup = function (metadata, context) {\n            var _this = this;\n            var currentTime = context.currentTime;\n            var furthestTime = 0;\n            var steps = metadata.steps.map(function (step) {\n                context.currentTime = currentTime;\n                var innerAst = visitDslNode(_this, step, context);\n                furthestTime = Math.max(furthestTime, context.currentTime);\n                return innerAst;\n            });\n            context.currentTime = furthestTime;\n            return {\n                type: 3 /* Group */,\n                steps: steps,\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitAnimate = function (metadata, context) {\n            var timingAst = constructTimingAst(metadata.timings, context.errors);\n            context.currentAnimateTimings = timingAst;\n            var styleAst;\n            var styleMetadata = metadata.styles ? metadata.styles : animations.style({});\n            if (styleMetadata.type == 5 /* Keyframes */) {\n                styleAst = this.visitKeyframes(styleMetadata, context);\n            }\n            else {\n                var styleMetadata_1 = metadata.styles;\n                var isEmpty = false;\n                if (!styleMetadata_1) {\n                    isEmpty = true;\n                    var newStyleData = {};\n                    if (timingAst.easing) {\n                        newStyleData['easing'] = timingAst.easing;\n                    }\n                    styleMetadata_1 = animations.style(newStyleData);\n                }\n                context.currentTime += timingAst.duration + timingAst.delay;\n                var _styleAst = this.visitStyle(styleMetadata_1, context);\n                _styleAst.isEmptyStep = isEmpty;\n                styleAst = _styleAst;\n            }\n            context.currentAnimateTimings = null;\n            return {\n                type: 4 /* Animate */,\n                timings: timingAst,\n                style: styleAst,\n                options: null\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitStyle = function (metadata, context) {\n            var ast = this._makeStyleAst(metadata, context);\n            this._validateStyleAst(ast, context);\n            return ast;\n        };\n        AnimationAstBuilderVisitor.prototype._makeStyleAst = function (metadata, context) {\n            var styles = [];\n            if (Array.isArray(metadata.styles)) {\n                metadata.styles.forEach(function (styleTuple) {\n                    if (typeof styleTuple == 'string') {\n                        if (styleTuple == animations.AUTO_STYLE) {\n                            styles.push(styleTuple);\n                        }\n                        else {\n                            context.errors.push(\"The provided style string value \" + styleTuple + \" is not allowed.\");\n                        }\n                    }\n                    else {\n                        styles.push(styleTuple);\n                    }\n                });\n            }\n            else {\n                styles.push(metadata.styles);\n            }\n            var containsDynamicStyles = false;\n            var collectedEasing = null;\n            styles.forEach(function (styleData) {\n                if (isObject(styleData)) {\n                    var styleMap = styleData;\n                    var easing = styleMap['easing'];\n                    if (easing) {\n                        collectedEasing = easing;\n                        delete styleMap['easing'];\n                    }\n                    if (!containsDynamicStyles) {\n                        for (var prop in styleMap) {\n                            var value = styleMap[prop];\n                            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                                containsDynamicStyles = true;\n                                break;\n                            }\n                        }\n                    }\n                }\n            });\n            return {\n                type: 6 /* Style */,\n                styles: styles,\n                easing: collectedEasing,\n                offset: metadata.offset, containsDynamicStyles: containsDynamicStyles,\n                options: null\n            };\n        };\n        AnimationAstBuilderVisitor.prototype._validateStyleAst = function (ast, context) {\n            var _this = this;\n            var timings = context.currentAnimateTimings;\n            var endTime = context.currentTime;\n            var startTime = context.currentTime;\n            if (timings && startTime > 0) {\n                startTime -= timings.duration + timings.delay;\n            }\n            ast.styles.forEach(function (tuple) {\n                if (typeof tuple == 'string')\n                    return;\n                Object.keys(tuple).forEach(function (prop) {\n                    if (!_this._driver.validateStyleProperty(prop)) {\n                        context.errors.push(\"The provided animation property \\\"\" + prop + \"\\\" is not a supported CSS property for animations\");\n                        return;\n                    }\n                    var collectedStyles = context.collectedStyles[context.currentQuerySelector];\n                    var collectedEntry = collectedStyles[prop];\n                    var updateCollectedStyle = true;\n                    if (collectedEntry) {\n                        if (startTime != endTime && startTime >= collectedEntry.startTime &&\n                            endTime <= collectedEntry.endTime) {\n                            context.errors.push(\"The CSS property \\\"\" + prop + \"\\\" that exists between the times of \\\"\" + collectedEntry.startTime + \"ms\\\" and \\\"\" + collectedEntry.endTime + \"ms\\\" is also being animated in a parallel animation between the times of \\\"\" + startTime + \"ms\\\" and \\\"\" + endTime + \"ms\\\"\");\n                            updateCollectedStyle = false;\n                        }\n                        // we always choose the smaller start time value since we\n                        // want to have a record of the entire animation window where\n                        // the style property is being animated in between\n                        startTime = collectedEntry.startTime;\n                    }\n                    if (updateCollectedStyle) {\n                        collectedStyles[prop] = { startTime: startTime, endTime: endTime };\n                    }\n                    if (context.options) {\n                        validateStyleParams(tuple[prop], context.options, context.errors);\n                    }\n                });\n            });\n        };\n        AnimationAstBuilderVisitor.prototype.visitKeyframes = function (metadata, context) {\n            var _this = this;\n            var ast = { type: 5 /* Keyframes */, styles: [], options: null };\n            if (!context.currentAnimateTimings) {\n                context.errors.push(\"keyframes() must be placed inside of a call to animate()\");\n                return ast;\n            }\n            var MAX_KEYFRAME_OFFSET = 1;\n            var totalKeyframesWithOffsets = 0;\n            var offsets = [];\n            var offsetsOutOfOrder = false;\n            var keyframesOutOfRange = false;\n            var previousOffset = 0;\n            var keyframes = metadata.steps.map(function (styles) {\n                var style = _this._makeStyleAst(styles, context);\n                var offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n                var offset = 0;\n                if (offsetVal != null) {\n                    totalKeyframesWithOffsets++;\n                    offset = style.offset = offsetVal;\n                }\n                keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n                offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n                previousOffset = offset;\n                offsets.push(offset);\n                return style;\n            });\n            if (keyframesOutOfRange) {\n                context.errors.push(\"Please ensure that all keyframe offsets are between 0 and 1\");\n            }\n            if (offsetsOutOfOrder) {\n                context.errors.push(\"Please ensure that all keyframe offsets are in order\");\n            }\n            var length = metadata.steps.length;\n            var generatedOffset = 0;\n            if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n                context.errors.push(\"Not all style() steps within the declared keyframes() contain offsets\");\n            }\n            else if (totalKeyframesWithOffsets == 0) {\n                generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n            }\n            var limit = length - 1;\n            var currentTime = context.currentTime;\n            var currentAnimateTimings = context.currentAnimateTimings;\n            var animateDuration = currentAnimateTimings.duration;\n            keyframes.forEach(function (kf, i) {\n                var offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n                var durationUpToThisFrame = offset * animateDuration;\n                context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n                currentAnimateTimings.duration = durationUpToThisFrame;\n                _this._validateStyleAst(kf, context);\n                kf.offset = offset;\n                ast.styles.push(kf);\n            });\n            return ast;\n        };\n        AnimationAstBuilderVisitor.prototype.visitReference = function (metadata, context) {\n            return {\n                type: 8 /* Reference */,\n                animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitAnimateChild = function (metadata, context) {\n            context.depCount++;\n            return {\n                type: 9 /* AnimateChild */,\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitAnimateRef = function (metadata, context) {\n            return {\n                type: 10 /* AnimateRef */,\n                animation: this.visitReference(metadata.animation, context),\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitQuery = function (metadata, context) {\n            var parentSelector = context.currentQuerySelector;\n            var options = (metadata.options || {});\n            context.queryCount++;\n            context.currentQuery = metadata;\n            var _a = __read(normalizeSelector(metadata.selector), 2), selector = _a[0], includeSelf = _a[1];\n            context.currentQuerySelector =\n                parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n            getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n            var animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n            context.currentQuery = null;\n            context.currentQuerySelector = parentSelector;\n            return {\n                type: 11 /* Query */,\n                selector: selector,\n                limit: options.limit || 0,\n                optional: !!options.optional, includeSelf: includeSelf, animation: animation,\n                originalSelector: metadata.selector,\n                options: normalizeAnimationOptions(metadata.options)\n            };\n        };\n        AnimationAstBuilderVisitor.prototype.visitStagger = function (metadata, context) {\n            if (!context.currentQuery) {\n                context.errors.push(\"stagger() can only be used inside of query()\");\n            }\n            var timings = metadata.timings === 'full' ?\n                { duration: 0, delay: 0, easing: 'full' } :\n                resolveTiming(metadata.timings, context.errors, true);\n            return {\n                type: 12 /* Stagger */,\n                animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context), timings: timings,\n                options: null\n            };\n        };\n        return AnimationAstBuilderVisitor;\n    }());\n    function normalizeSelector(selector) {\n        var hasAmpersand = selector.split(/\\s*,\\s*/).find(function (token) { return token == SELF_TOKEN; }) ? true : false;\n        if (hasAmpersand) {\n            selector = selector.replace(SELF_TOKEN_REGEX, '');\n        }\n        // the :enter and :leave selectors are filled in at runtime during timeline building\n        selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n            .replace(/@\\w+/g, function (match) { return NG_TRIGGER_SELECTOR + '-' + match.substr(1); })\n            .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n        return [selector, hasAmpersand];\n    }\n    function normalizeParams(obj) {\n        return obj ? copyObj(obj) : null;\n    }\n    var AnimationAstBuilderContext = /** @class */ (function () {\n        function AnimationAstBuilderContext(errors) {\n            this.errors = errors;\n            this.queryCount = 0;\n            this.depCount = 0;\n            this.currentTransition = null;\n            this.currentQuery = null;\n            this.currentQuerySelector = null;\n            this.currentAnimateTimings = null;\n            this.currentTime = 0;\n            this.collectedStyles = {};\n            this.options = null;\n        }\n        return AnimationAstBuilderContext;\n    }());\n    function consumeOffset(styles) {\n        if (typeof styles == 'string')\n            return null;\n        var offset = null;\n        if (Array.isArray(styles)) {\n            styles.forEach(function (styleTuple) {\n                if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n                    var obj = styleTuple;\n                    offset = parseFloat(obj['offset']);\n                    delete obj['offset'];\n                }\n            });\n        }\n        else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n            var obj = styles;\n            offset = parseFloat(obj['offset']);\n            delete obj['offset'];\n        }\n        return offset;\n    }\n    function isObject(value) {\n        return !Array.isArray(value) && typeof value == 'object';\n    }\n    function constructTimingAst(value, errors) {\n        var timings = null;\n        if (value.hasOwnProperty('duration')) {\n            timings = value;\n        }\n        else if (typeof value == 'number') {\n            var duration = resolveTiming(value, errors).duration;\n            return makeTimingAst(duration, 0, '');\n        }\n        var strValue = value;\n        var isDynamic = strValue.split(/\\s+/).some(function (v) { return v.charAt(0) == '{' && v.charAt(1) == '{'; });\n        if (isDynamic) {\n            var ast = makeTimingAst(0, 0, '');\n            ast.dynamic = true;\n            ast.strValue = strValue;\n            return ast;\n        }\n        timings = timings || resolveTiming(strValue, errors);\n        return makeTimingAst(timings.duration, timings.delay, timings.easing);\n    }\n    function normalizeAnimationOptions(options) {\n        if (options) {\n            options = copyObj(options);\n            if (options['params']) {\n                options['params'] = normalizeParams(options['params']);\n            }\n        }\n        else {\n            options = {};\n        }\n        return options;\n    }\n    function makeTimingAst(duration, delay, easing) {\n        return { duration: duration, delay: delay, easing: easing };\n    }\n\n    function createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing, subTimeline) {\n        if (easing === void 0) { easing = null; }\n        if (subTimeline === void 0) { subTimeline = false; }\n        return {\n            type: 1 /* TimelineAnimation */,\n            element: element,\n            keyframes: keyframes,\n            preStyleProps: preStyleProps,\n            postStyleProps: postStyleProps,\n            duration: duration,\n            delay: delay,\n            totalTime: duration + delay, easing: easing, subTimeline: subTimeline\n        };\n    }\n\n    var ElementInstructionMap = /** @class */ (function () {\n        function ElementInstructionMap() {\n            this._map = new Map();\n        }\n        ElementInstructionMap.prototype.consume = function (element) {\n            var instructions = this._map.get(element);\n            if (instructions) {\n                this._map.delete(element);\n            }\n            else {\n                instructions = [];\n            }\n            return instructions;\n        };\n        ElementInstructionMap.prototype.append = function (element, instructions) {\n            var existingInstructions = this._map.get(element);\n            if (!existingInstructions) {\n                this._map.set(element, existingInstructions = []);\n            }\n            existingInstructions.push.apply(existingInstructions, __spread(instructions));\n        };\n        ElementInstructionMap.prototype.has = function (element) { return this._map.has(element); };\n        ElementInstructionMap.prototype.clear = function () { this._map.clear(); };\n        return ElementInstructionMap;\n    }());\n\n    var ONE_FRAME_IN_MILLISECONDS = 1;\n    var ENTER_TOKEN = ':enter';\n    var ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\n    var LEAVE_TOKEN = ':leave';\n    var LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n    /*\n     * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n     * animation DSL code.\n     *\n     * The code below will be converted from:\n     *\n     * ```\n     * sequence([\n     *   style({ opacity: 0 }),\n     *   animate(1000, style({ opacity: 0 }))\n     * ])\n     * ```\n     *\n     * To:\n     * ```\n     * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n     * duration = 1000\n     * delay = 0\n     * easing = ''\n     * ```\n     *\n     * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n     * combination of prototypical inheritance, AST traversal and merge-sort-like algorithms are used.\n     *\n     * [AST Traversal]\n     * Each of the animation verbs, when executed, will return an string-map object representing what\n     * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n     * that when functional composition mix of these functions is evaluated (like in the example above)\n     * then it will end up producing a tree of objects representing the animation itself.\n     *\n     * When this animation object tree is processed by the visitor code below it will visit each of the\n     * verb statements within the visitor. And during each visit it will build the context of the\n     * animation keyframes by interacting with the `TimelineBuilder`.\n     *\n     * [TimelineBuilder]\n     * This class is responsible for tracking the styles and building a series of keyframe objects for a\n     * timeline between a start and end time. The builder starts off with an initial timeline and each\n     * time the AST comes across a `group()`, `keyframes()` or a combination of the two wihtin a\n     * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n     * they are complete.\n     *\n     * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n     * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n     * merge the styles used within the sub timelines into itself (only with group() this will happen).\n     * This happens with a merge operation (much like how the merge works in mergesort) and it will only\n     * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n     * that if the styles are used later on in another phase of the animation then they will be the most\n     * up-to-date values.\n     *\n     * [How Missing Styles Are Updated]\n     * Each timeline has a `backFill` property which is responsible for filling in new styles into\n     * already processed keyframes if a new style shows up later within the animation sequence.\n     *\n     * ```\n     * sequence([\n     *   style({ width: 0 }),\n     *   animate(1000, style({ width: 100 })),\n     *   animate(1000, style({ width: 200 })),\n     *   animate(1000, style({ width: 300 }))\n     *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n     * else\n     * ])\n     * ```\n     *\n     * What is happening here is that the `height` value is added later in the sequence, but is missing\n     * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n     * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n     * to properly fill in the style it will place the previous value (the value from the parent\n     * timeline) or a default value of `*` into the backFill object. Given that each of the keyframe\n     * styles are objects that prototypically inhert from the backFill object, this means that if a\n     * value is added into the backFill then it will automatically propagate any missing values to all\n     * keyframes. Therefore the missing `height` value will be properly filled into the already\n     * processed keyframes.\n     *\n     * When a sub-timeline is created it will have its own backFill property. This is done so that\n     * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n     * keyframes\n     *\n     * (For prototypically-inherited contents to be detected a `for(i in obj)` loop must be used.)\n     *\n     * [Validation]\n     * The code in this file is not responsible for validation. That functionality happens with within\n     * the `AnimationValidatorVisitor` code.\n     */\n    function buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n        if (startingStyles === void 0) { startingStyles = {}; }\n        if (finalStyles === void 0) { finalStyles = {}; }\n        if (errors === void 0) { errors = []; }\n        return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n    }\n    var AnimationTimelineBuilderVisitor = /** @class */ (function () {\n        function AnimationTimelineBuilderVisitor() {\n        }\n        AnimationTimelineBuilderVisitor.prototype.buildKeyframes = function (driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n            if (errors === void 0) { errors = []; }\n            subInstructions = subInstructions || new ElementInstructionMap();\n            var context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n            context.options = options;\n            context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n            visitDslNode(this, ast, context);\n            // this checks to see if an actual animation happened\n            var timelines = context.timelines.filter(function (timeline) { return timeline.containsAnimation(); });\n            if (timelines.length && Object.keys(finalStyles).length) {\n                var tl = timelines[timelines.length - 1];\n                if (!tl.allowOnlyTimelineStyles()) {\n                    tl.setStyles([finalStyles], null, context.errors, options);\n                }\n            }\n            return timelines.length ? timelines.map(function (timeline) { return timeline.buildKeyframes(); }) :\n                [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitTrigger = function (ast, context) {\n            // these values are not visited in this AST\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitState = function (ast, context) {\n            // these values are not visited in this AST\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitTransition = function (ast, context) {\n            // these values are not visited in this AST\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitAnimateChild = function (ast, context) {\n            var elementInstructions = context.subInstructions.consume(context.element);\n            if (elementInstructions) {\n                var innerContext = context.createSubContext(ast.options);\n                var startTime = context.currentTimeline.currentTime;\n                var endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n                if (startTime != endTime) {\n                    // we do this on the upper context because we created a sub context for\n                    // the sub child animations\n                    context.transformIntoNewTimeline(endTime);\n                }\n            }\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitAnimateRef = function (ast, context) {\n            var innerContext = context.createSubContext(ast.options);\n            innerContext.transformIntoNewTimeline();\n            this.visitReference(ast.animation, innerContext);\n            context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype._visitSubInstructions = function (instructions, context, options) {\n            var startTime = context.currentTimeline.currentTime;\n            var furthestTime = startTime;\n            // this is a special-case for when a user wants to skip a sub\n            // animation from being fired entirely.\n            var duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n            var delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n            if (duration !== 0) {\n                instructions.forEach(function (instruction) {\n                    var instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                    furthestTime =\n                        Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n                });\n            }\n            return furthestTime;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitReference = function (ast, context) {\n            context.updateOptions(ast.options, true);\n            visitDslNode(this, ast.animation, context);\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitSequence = function (ast, context) {\n            var _this = this;\n            var subContextCount = context.subContextCount;\n            var ctx = context;\n            var options = ast.options;\n            if (options && (options.params || options.delay)) {\n                ctx = context.createSubContext(options);\n                ctx.transformIntoNewTimeline();\n                if (options.delay != null) {\n                    if (ctx.previousNode.type == 6 /* Style */) {\n                        ctx.currentTimeline.snapshotCurrentStyles();\n                        ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                    }\n                    var delay = resolveTimingValue(options.delay);\n                    ctx.delayNextStep(delay);\n                }\n            }\n            if (ast.steps.length) {\n                ast.steps.forEach(function (s) { return visitDslNode(_this, s, ctx); });\n                // this is here just incase the inner steps only contain or end with a style() call\n                ctx.currentTimeline.applyStylesToKeyframe();\n                // this means that some animation function within the sequence\n                // ended up creating a sub timeline (which means the current\n                // timeline cannot overlap with the contents of the sequence)\n                if (ctx.subContextCount > subContextCount) {\n                    ctx.transformIntoNewTimeline();\n                }\n            }\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitGroup = function (ast, context) {\n            var _this = this;\n            var innerTimelines = [];\n            var furthestTime = context.currentTimeline.currentTime;\n            var delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n            ast.steps.forEach(function (s) {\n                var innerContext = context.createSubContext(ast.options);\n                if (delay) {\n                    innerContext.delayNextStep(delay);\n                }\n                visitDslNode(_this, s, innerContext);\n                furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n                innerTimelines.push(innerContext.currentTimeline);\n            });\n            // this operation is run after the AST loop because otherwise\n            // if the parent timeline's collected styles were updated then\n            // it would pass in invalid data into the new-to-be forked items\n            innerTimelines.forEach(function (timeline) { return context.currentTimeline.mergeTimelineCollectedStyles(timeline); });\n            context.transformIntoNewTimeline(furthestTime);\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype._visitTiming = function (ast, context) {\n            if (ast.dynamic) {\n                var strValue = ast.strValue;\n                var timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n                return resolveTiming(timingValue, context.errors);\n            }\n            else {\n                return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n            }\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitAnimate = function (ast, context) {\n            var timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n            var timeline = context.currentTimeline;\n            if (timings.delay) {\n                context.incrementTime(timings.delay);\n                timeline.snapshotCurrentStyles();\n            }\n            var style = ast.style;\n            if (style.type == 5 /* Keyframes */) {\n                this.visitKeyframes(style, context);\n            }\n            else {\n                context.incrementTime(timings.duration);\n                this.visitStyle(style, context);\n                timeline.applyStylesToKeyframe();\n            }\n            context.currentAnimateTimings = null;\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitStyle = function (ast, context) {\n            var timeline = context.currentTimeline;\n            var timings = context.currentAnimateTimings;\n            // this is a special case for when a style() call\n            // directly follows  an animate() call (but not inside of an animate() call)\n            if (!timings && timeline.getCurrentStyleProperties().length) {\n                timeline.forwardFrame();\n            }\n            var easing = (timings && timings.easing) || ast.easing;\n            if (ast.isEmptyStep) {\n                timeline.applyEmptyStep(easing);\n            }\n            else {\n                timeline.setStyles(ast.styles, easing, context.errors, context.options);\n            }\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitKeyframes = function (ast, context) {\n            var currentAnimateTimings = context.currentAnimateTimings;\n            var startTime = (context.currentTimeline).duration;\n            var duration = currentAnimateTimings.duration;\n            var innerContext = context.createSubContext();\n            var innerTimeline = innerContext.currentTimeline;\n            innerTimeline.easing = currentAnimateTimings.easing;\n            ast.styles.forEach(function (step) {\n                var offset = step.offset || 0;\n                innerTimeline.forwardTime(offset * duration);\n                innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n                innerTimeline.applyStylesToKeyframe();\n            });\n            // this will ensure that the parent timeline gets all the styles from\n            // the child even if the new timeline below is not used\n            context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n            // we do this because the window between this timeline and the sub timeline\n            // should ensure that the styles within are exactly the same as they were before\n            context.transformIntoNewTimeline(startTime + duration);\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitQuery = function (ast, context) {\n            var _this = this;\n            // in the event that the first step before this is a style step we need\n            // to ensure the styles are applied before the children are animated\n            var startTime = context.currentTimeline.currentTime;\n            var options = (ast.options || {});\n            var delay = options.delay ? resolveTimingValue(options.delay) : 0;\n            if (delay && (context.previousNode.type === 6 /* Style */ ||\n                (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n                context.currentTimeline.snapshotCurrentStyles();\n                context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n            }\n            var furthestTime = startTime;\n            var elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n            context.currentQueryTotal = elms.length;\n            var sameElementTimeline = null;\n            elms.forEach(function (element, i) {\n                context.currentQueryIndex = i;\n                var innerContext = context.createSubContext(ast.options, element);\n                if (delay) {\n                    innerContext.delayNextStep(delay);\n                }\n                if (element === context.element) {\n                    sameElementTimeline = innerContext.currentTimeline;\n                }\n                visitDslNode(_this, ast.animation, innerContext);\n                // this is here just incase the inner steps only contain or end\n                // with a style() call (which is here to signal that this is a preparatory\n                // call to style an element before it is animated again)\n                innerContext.currentTimeline.applyStylesToKeyframe();\n                var endTime = innerContext.currentTimeline.currentTime;\n                furthestTime = Math.max(furthestTime, endTime);\n            });\n            context.currentQueryIndex = 0;\n            context.currentQueryTotal = 0;\n            context.transformIntoNewTimeline(furthestTime);\n            if (sameElementTimeline) {\n                context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n                context.currentTimeline.snapshotCurrentStyles();\n            }\n            context.previousNode = ast;\n        };\n        AnimationTimelineBuilderVisitor.prototype.visitStagger = function (ast, context) {\n            var parentContext = context.parentContext;\n            var tl = context.currentTimeline;\n            var timings = ast.timings;\n            var duration = Math.abs(timings.duration);\n            var maxTime = duration * (context.currentQueryTotal - 1);\n            var delay = duration * context.currentQueryIndex;\n            var staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n            switch (staggerTransformer) {\n                case 'reverse':\n                    delay = maxTime - delay;\n                    break;\n                case 'full':\n                    delay = parentContext.currentStaggerTime;\n                    break;\n            }\n            var timeline = context.currentTimeline;\n            if (delay) {\n                timeline.delayNextStep(delay);\n            }\n            var startingTime = timeline.currentTime;\n            visitDslNode(this, ast.animation, context);\n            context.previousNode = ast;\n            // time = duration + delay\n            // the reason why this computation is so complex is because\n            // the inner timeline may either have a delay value or a stretched\n            // keyframe depending on if a subtimeline is not used or is used.\n            parentContext.currentStaggerTime =\n                (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n        };\n        return AnimationTimelineBuilderVisitor;\n    }());\n    var DEFAULT_NOOP_PREVIOUS_NODE = {};\n    var AnimationTimelineContext = /** @class */ (function () {\n        function AnimationTimelineContext(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n            this._driver = _driver;\n            this.element = element;\n            this.subInstructions = subInstructions;\n            this._enterClassName = _enterClassName;\n            this._leaveClassName = _leaveClassName;\n            this.errors = errors;\n            this.timelines = timelines;\n            this.parentContext = null;\n            this.currentAnimateTimings = null;\n            this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n            this.subContextCount = 0;\n            this.options = {};\n            this.currentQueryIndex = 0;\n            this.currentQueryTotal = 0;\n            this.currentStaggerTime = 0;\n            this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n            timelines.push(this.currentTimeline);\n        }\n        Object.defineProperty(AnimationTimelineContext.prototype, \"params\", {\n            get: function () { return this.options.params; },\n            enumerable: true,\n            configurable: true\n        });\n        AnimationTimelineContext.prototype.updateOptions = function (options, skipIfExists) {\n            var _this = this;\n            if (!options)\n                return;\n            var newOptions = options;\n            var optionsToUpdate = this.options;\n            // NOTE: this will get patched up when other animation methods support duration overrides\n            if (newOptions.duration != null) {\n                optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n            }\n            if (newOptions.delay != null) {\n                optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n            }\n            var newParams = newOptions.params;\n            if (newParams) {\n                var paramsToUpdate_1 = optionsToUpdate.params;\n                if (!paramsToUpdate_1) {\n                    paramsToUpdate_1 = this.options.params = {};\n                }\n                Object.keys(newParams).forEach(function (name) {\n                    if (!skipIfExists || !paramsToUpdate_1.hasOwnProperty(name)) {\n                        paramsToUpdate_1[name] = interpolateParams(newParams[name], paramsToUpdate_1, _this.errors);\n                    }\n                });\n            }\n        };\n        AnimationTimelineContext.prototype._copyOptions = function () {\n            var options = {};\n            if (this.options) {\n                var oldParams_1 = this.options.params;\n                if (oldParams_1) {\n                    var params_1 = options['params'] = {};\n                    Object.keys(oldParams_1).forEach(function (name) { params_1[name] = oldParams_1[name]; });\n                }\n            }\n            return options;\n        };\n        AnimationTimelineContext.prototype.createSubContext = function (options, element, newTime) {\n            if (options === void 0) { options = null; }\n            var target = element || this.element;\n            var context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n            context.previousNode = this.previousNode;\n            context.currentAnimateTimings = this.currentAnimateTimings;\n            context.options = this._copyOptions();\n            context.updateOptions(options);\n            context.currentQueryIndex = this.currentQueryIndex;\n            context.currentQueryTotal = this.currentQueryTotal;\n            context.parentContext = this;\n            this.subContextCount++;\n            return context;\n        };\n        AnimationTimelineContext.prototype.transformIntoNewTimeline = function (newTime) {\n            this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n            this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n            this.timelines.push(this.currentTimeline);\n            return this.currentTimeline;\n        };\n        AnimationTimelineContext.prototype.appendInstructionToTimeline = function (instruction, duration, delay) {\n            var updatedTimings = {\n                duration: duration != null ? duration : instruction.duration,\n                delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n                easing: ''\n            };\n            var builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n            this.timelines.push(builder);\n            return updatedTimings;\n        };\n        AnimationTimelineContext.prototype.incrementTime = function (time) {\n            this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n        };\n        AnimationTimelineContext.prototype.delayNextStep = function (delay) {\n            // negative delays are not yet supported\n            if (delay > 0) {\n                this.currentTimeline.delayNextStep(delay);\n            }\n        };\n        AnimationTimelineContext.prototype.invokeQuery = function (selector, originalSelector, limit, includeSelf, optional, errors) {\n            var results = [];\n            if (includeSelf) {\n                results.push(this.element);\n            }\n            if (selector.length > 0) { // if :self is only used then the selector is empty\n                selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n                selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n                var multi = limit != 1;\n                var elements = this._driver.query(this.element, selector, multi);\n                if (limit !== 0) {\n                    elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) :\n                        elements.slice(0, limit);\n                }\n                results.push.apply(results, __spread(elements));\n            }\n            if (!optional && results.length == 0) {\n                errors.push(\"`query(\\\"\" + originalSelector + \"\\\")` returned zero elements. (Use `query(\\\"\" + originalSelector + \"\\\", { optional: true })` if you wish to allow this.)\");\n            }\n            return results;\n        };\n        return AnimationTimelineContext;\n    }());\n    var TimelineBuilder = /** @class */ (function () {\n        function TimelineBuilder(_driver, element, startTime, _elementTimelineStylesLookup) {\n            this._driver = _driver;\n            this.element = element;\n            this.startTime = startTime;\n            this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n            this.duration = 0;\n            this._previousKeyframe = {};\n            this._currentKeyframe = {};\n            this._keyframes = new Map();\n            this._styleSummary = {};\n            this._pendingStyles = {};\n            this._backFill = {};\n            this._currentEmptyStepKeyframe = null;\n            if (!this._elementTimelineStylesLookup) {\n                this._elementTimelineStylesLookup = new Map();\n            }\n            this._localTimelineStyles = Object.create(this._backFill, {});\n            this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n            if (!this._globalTimelineStyles) {\n                this._globalTimelineStyles = this._localTimelineStyles;\n                this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n            }\n            this._loadKeyframe();\n        }\n        TimelineBuilder.prototype.containsAnimation = function () {\n            switch (this._keyframes.size) {\n                case 0:\n                    return false;\n                case 1:\n                    return this.getCurrentStyleProperties().length > 0;\n                default:\n                    return true;\n            }\n        };\n        TimelineBuilder.prototype.getCurrentStyleProperties = function () { return Object.keys(this._currentKeyframe); };\n        Object.defineProperty(TimelineBuilder.prototype, \"currentTime\", {\n            get: function () { return this.startTime + this.duration; },\n            enumerable: true,\n            configurable: true\n        });\n        TimelineBuilder.prototype.delayNextStep = function (delay) {\n            // in the event that a style() step is placed right before a stagger()\n            // and that style() step is the very first style() value in the animation\n            // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n            // properly applies the style() values to work with the stagger...\n            var hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n            if (this.duration || hasPreStyleStep) {\n                this.forwardTime(this.currentTime + delay);\n                if (hasPreStyleStep) {\n                    this.snapshotCurrentStyles();\n                }\n            }\n            else {\n                this.startTime += delay;\n            }\n        };\n        TimelineBuilder.prototype.fork = function (element, currentTime) {\n            this.applyStylesToKeyframe();\n            return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n        };\n        TimelineBuilder.prototype._loadKeyframe = function () {\n            if (this._currentKeyframe) {\n                this._previousKeyframe = this._currentKeyframe;\n            }\n            this._currentKeyframe = this._keyframes.get(this.duration);\n            if (!this._currentKeyframe) {\n                this._currentKeyframe = Object.create(this._backFill, {});\n                this._keyframes.set(this.duration, this._currentKeyframe);\n            }\n        };\n        TimelineBuilder.prototype.forwardFrame = function () {\n            this.duration += ONE_FRAME_IN_MILLISECONDS;\n            this._loadKeyframe();\n        };\n        TimelineBuilder.prototype.forwardTime = function (time) {\n            this.applyStylesToKeyframe();\n            this.duration = time;\n            this._loadKeyframe();\n        };\n        TimelineBuilder.prototype._updateStyle = function (prop, value) {\n            this._localTimelineStyles[prop] = value;\n            this._globalTimelineStyles[prop] = value;\n            this._styleSummary[prop] = { time: this.currentTime, value: value };\n        };\n        TimelineBuilder.prototype.allowOnlyTimelineStyles = function () { return this._currentEmptyStepKeyframe !== this._currentKeyframe; };\n        TimelineBuilder.prototype.applyEmptyStep = function (easing) {\n            var _this = this;\n            if (easing) {\n                this._previousKeyframe['easing'] = easing;\n            }\n            // special case for animate(duration):\n            // all missing styles are filled with a `*` value then\n            // if any destination styles are filled in later on the same\n            // keyframe then they will override the overridden styles\n            // We use `_globalTimelineStyles` here because there may be\n            // styles in previous keyframes that are not present in this timeline\n            Object.keys(this._globalTimelineStyles).forEach(function (prop) {\n                _this._backFill[prop] = _this._globalTimelineStyles[prop] || animations.AUTO_STYLE;\n                _this._currentKeyframe[prop] = animations.AUTO_STYLE;\n            });\n            this._currentEmptyStepKeyframe = this._currentKeyframe;\n        };\n        TimelineBuilder.prototype.setStyles = function (input, easing, errors, options) {\n            var _this = this;\n            if (easing) {\n                this._previousKeyframe['easing'] = easing;\n            }\n            var params = (options && options.params) || {};\n            var styles = flattenStyles(input, this._globalTimelineStyles);\n            Object.keys(styles).forEach(function (prop) {\n                var val = interpolateParams(styles[prop], params, errors);\n                _this._pendingStyles[prop] = val;\n                if (!_this._localTimelineStyles.hasOwnProperty(prop)) {\n                    _this._backFill[prop] = _this._globalTimelineStyles.hasOwnProperty(prop) ?\n                        _this._globalTimelineStyles[prop] :\n                        animations.AUTO_STYLE;\n                }\n                _this._updateStyle(prop, val);\n            });\n        };\n        TimelineBuilder.prototype.applyStylesToKeyframe = function () {\n            var _this = this;\n            var styles = this._pendingStyles;\n            var props = Object.keys(styles);\n            if (props.length == 0)\n                return;\n            this._pendingStyles = {};\n            props.forEach(function (prop) {\n                var val = styles[prop];\n                _this._currentKeyframe[prop] = val;\n            });\n            Object.keys(this._localTimelineStyles).forEach(function (prop) {\n                if (!_this._currentKeyframe.hasOwnProperty(prop)) {\n                    _this._currentKeyframe[prop] = _this._localTimelineStyles[prop];\n                }\n            });\n        };\n        TimelineBuilder.prototype.snapshotCurrentStyles = function () {\n            var _this = this;\n            Object.keys(this._localTimelineStyles).forEach(function (prop) {\n                var val = _this._localTimelineStyles[prop];\n                _this._pendingStyles[prop] = val;\n                _this._updateStyle(prop, val);\n            });\n        };\n        TimelineBuilder.prototype.getFinalKeyframe = function () { return this._keyframes.get(this.duration); };\n        Object.defineProperty(TimelineBuilder.prototype, \"properties\", {\n            get: function () {\n                var properties = [];\n                for (var prop in this._currentKeyframe) {\n                    properties.push(prop);\n                }\n                return properties;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        TimelineBuilder.prototype.mergeTimelineCollectedStyles = function (timeline) {\n            var _this = this;\n            Object.keys(timeline._styleSummary).forEach(function (prop) {\n                var details0 = _this._styleSummary[prop];\n                var details1 = timeline._styleSummary[prop];\n                if (!details0 || details1.time > details0.time) {\n                    _this._updateStyle(prop, details1.value);\n                }\n            });\n        };\n        TimelineBuilder.prototype.buildKeyframes = function () {\n            var _this = this;\n            this.applyStylesToKeyframe();\n            var preStyleProps = new Set();\n            var postStyleProps = new Set();\n            var isEmpty = this._keyframes.size === 1 && this.duration === 0;\n            var finalKeyframes = [];\n            this._keyframes.forEach(function (keyframe, time) {\n                var finalKeyframe = copyStyles(keyframe, true);\n                Object.keys(finalKeyframe).forEach(function (prop) {\n                    var value = finalKeyframe[prop];\n                    if (value == animations.ɵPRE_STYLE) {\n                        preStyleProps.add(prop);\n                    }\n                    else if (value == animations.AUTO_STYLE) {\n                        postStyleProps.add(prop);\n                    }\n                });\n                if (!isEmpty) {\n                    finalKeyframe['offset'] = time / _this.duration;\n                }\n                finalKeyframes.push(finalKeyframe);\n            });\n            var preProps = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n            var postProps = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n            // special case for a 0-second animation (which is designed just to place styles onscreen)\n            if (isEmpty) {\n                var kf0 = finalKeyframes[0];\n                var kf1 = copyObj(kf0);\n                kf0['offset'] = 0;\n                kf1['offset'] = 1;\n                finalKeyframes = [kf0, kf1];\n            }\n            return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n        };\n        return TimelineBuilder;\n    }());\n    var SubTimelineBuilder = /** @class */ (function (_super) {\n        __extends(SubTimelineBuilder, _super);\n        function SubTimelineBuilder(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe) {\n            if (_stretchStartingKeyframe === void 0) { _stretchStartingKeyframe = false; }\n            var _this = _super.call(this, driver, element, timings.delay) || this;\n            _this.element = element;\n            _this.keyframes = keyframes;\n            _this.preStyleProps = preStyleProps;\n            _this.postStyleProps = postStyleProps;\n            _this._stretchStartingKeyframe = _stretchStartingKeyframe;\n            _this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n            return _this;\n        }\n        SubTimelineBuilder.prototype.containsAnimation = function () { return this.keyframes.length > 1; };\n        SubTimelineBuilder.prototype.buildKeyframes = function () {\n            var keyframes = this.keyframes;\n            var _a = this.timings, delay = _a.delay, duration = _a.duration, easing = _a.easing;\n            if (this._stretchStartingKeyframe && delay) {\n                var newKeyframes = [];\n                var totalTime = duration + delay;\n                var startingGap = delay / totalTime;\n                // the original starting keyframe now starts once the delay is done\n                var newFirstKeyframe = copyStyles(keyframes[0], false);\n                newFirstKeyframe['offset'] = 0;\n                newKeyframes.push(newFirstKeyframe);\n                var oldFirstKeyframe = copyStyles(keyframes[0], false);\n                oldFirstKeyframe['offset'] = roundOffset(startingGap);\n                newKeyframes.push(oldFirstKeyframe);\n                /*\n                  When the keyframe is stretched then it means that the delay before the animation\n                  starts is gone. Instead the first keyframe is placed at the start of the animation\n                  and it is then copied to where it starts when the original delay is over. This basically\n                  means nothing animates during that delay, but the styles are still renderered. For this\n                  to work the original offset values that exist in the original keyframes must be \"warped\"\n                  so that they can take the new keyframe + delay into account.\n          \n                  delay=1000, duration=1000, keyframes = 0 .5 1\n          \n                  turns into\n          \n                  delay=0, duration=2000, keyframes = 0 .33 .66 1\n                 */\n                // offsets between 1 ... n -1 are all warped by the keyframe stretch\n                var limit = keyframes.length - 1;\n                for (var i = 1; i <= limit; i++) {\n                    var kf = copyStyles(keyframes[i], false);\n                    var oldOffset = kf['offset'];\n                    var timeAtKeyframe = delay + oldOffset * duration;\n                    kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n                    newKeyframes.push(kf);\n                }\n                // the new starting keyframe should be added at the start\n                duration = totalTime;\n                delay = 0;\n                easing = '';\n                keyframes = newKeyframes;\n            }\n            return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n        };\n        return SubTimelineBuilder;\n    }(TimelineBuilder));\n    function roundOffset(offset, decimalPoints) {\n        if (decimalPoints === void 0) { decimalPoints = 3; }\n        var mult = Math.pow(10, decimalPoints - 1);\n        return Math.round(offset * mult) / mult;\n    }\n    function flattenStyles(input, allStyles) {\n        var styles = {};\n        var allProperties;\n        input.forEach(function (token) {\n            if (token === '*') {\n                allProperties = allProperties || Object.keys(allStyles);\n                allProperties.forEach(function (prop) { styles[prop] = animations.AUTO_STYLE; });\n            }\n            else {\n                copyStyles(token, false, styles);\n            }\n        });\n        return styles;\n    }\n\n    var Animation = /** @class */ (function () {\n        function Animation(_driver, input) {\n            this._driver = _driver;\n            var errors = [];\n            var ast = buildAnimationAst(_driver, input, errors);\n            if (errors.length) {\n                var errorMessage = \"animation validation failed:\\n\" + errors.join(\"\\n\");\n                throw new Error(errorMessage);\n            }\n            this._animationAst = ast;\n        }\n        Animation.prototype.buildTimelines = function (element, startingStyles, destinationStyles, options, subInstructions) {\n            var start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) :\n                startingStyles;\n            var dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) :\n                destinationStyles;\n            var errors = [];\n            subInstructions = subInstructions || new ElementInstructionMap();\n            var result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n            if (errors.length) {\n                var errorMessage = \"animation building failed:\\n\" + errors.join(\"\\n\");\n                throw new Error(errorMessage);\n            }\n            return result;\n        };\n        return Animation;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * @publicApi\n     */\n    var AnimationStyleNormalizer = /** @class */ (function () {\n        function AnimationStyleNormalizer() {\n        }\n        return AnimationStyleNormalizer;\n    }());\n    /**\n     * @publicApi\n     */\n    var NoopAnimationStyleNormalizer = /** @class */ (function () {\n        function NoopAnimationStyleNormalizer() {\n        }\n        NoopAnimationStyleNormalizer.prototype.normalizePropertyName = function (propertyName, errors) { return propertyName; };\n        NoopAnimationStyleNormalizer.prototype.normalizeStyleValue = function (userProvidedProperty, normalizedProperty, value, errors) {\n            return value;\n        };\n        return NoopAnimationStyleNormalizer;\n    }());\n\n    var WebAnimationsStyleNormalizer = /** @class */ (function (_super) {\n        __extends(WebAnimationsStyleNormalizer, _super);\n        function WebAnimationsStyleNormalizer() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        WebAnimationsStyleNormalizer.prototype.normalizePropertyName = function (propertyName, errors) {\n            return dashCaseToCamelCase(propertyName);\n        };\n        WebAnimationsStyleNormalizer.prototype.normalizeStyleValue = function (userProvidedProperty, normalizedProperty, value, errors) {\n            var unit = '';\n            var strVal = value.toString().trim();\n            if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n                if (typeof value === 'number') {\n                    unit = 'px';\n                }\n                else {\n                    var valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                    if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                        errors.push(\"Please provide a CSS unit value for \" + userProvidedProperty + \":\" + value);\n                    }\n                }\n            }\n            return strVal + unit;\n        };\n        return WebAnimationsStyleNormalizer;\n    }(AnimationStyleNormalizer));\n    var DIMENSIONAL_PROP_MAP = makeBooleanMap('width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n    function makeBooleanMap(keys) {\n        var map = {};\n        keys.forEach(function (key) { return map[key] = true; });\n        return map;\n    }\n\n    function createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n        return {\n            type: 0 /* TransitionAnimation */,\n            element: element,\n            triggerName: triggerName,\n            isRemovalTransition: isRemovalTransition,\n            fromState: fromState,\n            fromStyles: fromStyles,\n            toState: toState,\n            toStyles: toStyles,\n            timelines: timelines,\n            queriedElements: queriedElements,\n            preStyleProps: preStyleProps,\n            postStyleProps: postStyleProps,\n            totalTime: totalTime,\n            errors: errors\n        };\n    }\n\n    var EMPTY_OBJECT = {};\n    var AnimationTransitionFactory = /** @class */ (function () {\n        function AnimationTransitionFactory(_triggerName, ast, _stateStyles) {\n            this._triggerName = _triggerName;\n            this.ast = ast;\n            this._stateStyles = _stateStyles;\n        }\n        AnimationTransitionFactory.prototype.match = function (currentState, nextState, element, params) {\n            return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n        };\n        AnimationTransitionFactory.prototype.buildStyles = function (stateName, params, errors) {\n            var backupStateStyler = this._stateStyles['*'];\n            var stateStyler = this._stateStyles[stateName];\n            var backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};\n            return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;\n        };\n        AnimationTransitionFactory.prototype.build = function (driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n            var errors = [];\n            var transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n            var currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n            var currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n            var nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n            var nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n            var queriedElements = new Set();\n            var preStyleMap = new Map();\n            var postStyleMap = new Map();\n            var isRemoval = nextState === 'void';\n            var animationOptions = { params: __assign({}, transitionAnimationParams, nextAnimationParams) };\n            var timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n            var totalTime = 0;\n            timelines.forEach(function (tl) { totalTime = Math.max(tl.duration + tl.delay, totalTime); });\n            if (errors.length) {\n                return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n            }\n            timelines.forEach(function (tl) {\n                var elm = tl.element;\n                var preProps = getOrSetAsInMap(preStyleMap, elm, {});\n                tl.preStyleProps.forEach(function (prop) { return preProps[prop] = true; });\n                var postProps = getOrSetAsInMap(postStyleMap, elm, {});\n                tl.postStyleProps.forEach(function (prop) { return postProps[prop] = true; });\n                if (elm !== element) {\n                    queriedElements.add(elm);\n                }\n            });\n            var queriedElementsList = iteratorToArray(queriedElements.values());\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap, totalTime);\n        };\n        return AnimationTransitionFactory;\n    }());\n    function oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n        return matchFns.some(function (fn) { return fn(currentState, nextState, element, params); });\n    }\n    var AnimationStateStyles = /** @class */ (function () {\n        function AnimationStateStyles(styles, defaultParams) {\n            this.styles = styles;\n            this.defaultParams = defaultParams;\n        }\n        AnimationStateStyles.prototype.buildStyles = function (params, errors) {\n            var finalStyles = {};\n            var combinedParams = copyObj(this.defaultParams);\n            Object.keys(params).forEach(function (key) {\n                var value = params[key];\n                if (value != null) {\n                    combinedParams[key] = value;\n                }\n            });\n            this.styles.styles.forEach(function (value) {\n                if (typeof value !== 'string') {\n                    var styleObj_1 = value;\n                    Object.keys(styleObj_1).forEach(function (prop) {\n                        var val = styleObj_1[prop];\n                        if (val.length > 1) {\n                            val = interpolateParams(val, combinedParams, errors);\n                        }\n                        finalStyles[prop] = val;\n                    });\n                }\n            });\n            return finalStyles;\n        };\n        return AnimationStateStyles;\n    }());\n\n    /**\n     * @publicApi\n     */\n    function buildTrigger(name, ast) {\n        return new AnimationTrigger(name, ast);\n    }\n    /**\n    * @publicApi\n    */\n    var AnimationTrigger = /** @class */ (function () {\n        function AnimationTrigger(name, ast) {\n            var _this = this;\n            this.name = name;\n            this.ast = ast;\n            this.transitionFactories = [];\n            this.states = {};\n            ast.states.forEach(function (ast) {\n                var defaultParams = (ast.options && ast.options.params) || {};\n                _this.states[ast.name] = new AnimationStateStyles(ast.style, defaultParams);\n            });\n            balanceProperties(this.states, 'true', '1');\n            balanceProperties(this.states, 'false', '0');\n            ast.transitions.forEach(function (ast) {\n                _this.transitionFactories.push(new AnimationTransitionFactory(name, ast, _this.states));\n            });\n            this.fallbackTransition = createFallbackTransition(name, this.states);\n        }\n        Object.defineProperty(AnimationTrigger.prototype, \"containsQueries\", {\n            get: function () { return this.ast.queryCount > 0; },\n            enumerable: true,\n            configurable: true\n        });\n        AnimationTrigger.prototype.matchTransition = function (currentState, nextState, element, params) {\n            var entry = this.transitionFactories.find(function (f) { return f.match(currentState, nextState, element, params); });\n            return entry || null;\n        };\n        AnimationTrigger.prototype.matchStyles = function (currentState, params, errors) {\n            return this.fallbackTransition.buildStyles(currentState, params, errors);\n        };\n        return AnimationTrigger;\n    }());\n    function createFallbackTransition(triggerName, states) {\n        var matchers = [function (fromState, toState) { return true; }];\n        var animation = { type: 2 /* Sequence */, steps: [], options: null };\n        var transition = {\n            type: 1 /* Transition */,\n            animation: animation,\n            matchers: matchers,\n            options: null,\n            queryCount: 0,\n            depCount: 0\n        };\n        return new AnimationTransitionFactory(triggerName, transition, states);\n    }\n    function balanceProperties(obj, key1, key2) {\n        if (obj.hasOwnProperty(key1)) {\n            if (!obj.hasOwnProperty(key2)) {\n                obj[key2] = obj[key1];\n            }\n        }\n        else if (obj.hasOwnProperty(key2)) {\n            obj[key1] = obj[key2];\n        }\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\n    var TimelineAnimationEngine = /** @class */ (function () {\n        function TimelineAnimationEngine(bodyNode, _driver, _normalizer) {\n            this.bodyNode = bodyNode;\n            this._driver = _driver;\n            this._normalizer = _normalizer;\n            this._animations = {};\n            this._playersById = {};\n            this.players = [];\n        }\n        TimelineAnimationEngine.prototype.register = function (id, metadata) {\n            var errors = [];\n            var ast = buildAnimationAst(this._driver, metadata, errors);\n            if (errors.length) {\n                throw new Error(\"Unable to build the animation due to the following errors: \" + errors.join(\"\\n\"));\n            }\n            else {\n                this._animations[id] = ast;\n            }\n        };\n        TimelineAnimationEngine.prototype._buildPlayer = function (i, preStyles, postStyles) {\n            var element = i.element;\n            var keyframes = normalizeKeyframes(this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n            return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n        };\n        TimelineAnimationEngine.prototype.create = function (id, element, options) {\n            var _this = this;\n            if (options === void 0) { options = {}; }\n            var errors = [];\n            var ast = this._animations[id];\n            var instructions;\n            var autoStylesMap = new Map();\n            if (ast) {\n                instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n                instructions.forEach(function (inst) {\n                    var styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n                    inst.postStyleProps.forEach(function (prop) { return styles[prop] = null; });\n                });\n            }\n            else {\n                errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n                instructions = [];\n            }\n            if (errors.length) {\n                throw new Error(\"Unable to create the animation due to the following errors: \" + errors.join(\"\\n\"));\n            }\n            autoStylesMap.forEach(function (styles, element) {\n                Object.keys(styles).forEach(function (prop) { styles[prop] = _this._driver.computeStyle(element, prop, animations.AUTO_STYLE); });\n            });\n            var players = instructions.map(function (i) {\n                var styles = autoStylesMap.get(i.element);\n                return _this._buildPlayer(i, {}, styles);\n            });\n            var player = optimizeGroupPlayer(players);\n            this._playersById[id] = player;\n            player.onDestroy(function () { return _this.destroy(id); });\n            this.players.push(player);\n            return player;\n        };\n        TimelineAnimationEngine.prototype.destroy = function (id) {\n            var player = this._getPlayer(id);\n            player.destroy();\n            delete this._playersById[id];\n            var index = this.players.indexOf(player);\n            if (index >= 0) {\n                this.players.splice(index, 1);\n            }\n        };\n        TimelineAnimationEngine.prototype._getPlayer = function (id) {\n            var player = this._playersById[id];\n            if (!player) {\n                throw new Error(\"Unable to find the timeline player referenced by \" + id);\n            }\n            return player;\n        };\n        TimelineAnimationEngine.prototype.listen = function (id, element, eventName, callback) {\n            // triggerName, fromState, toState are all ignored for timeline animations\n            var baseEvent = makeAnimationEvent(element, '', '', '');\n            listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n            return function () { };\n        };\n        TimelineAnimationEngine.prototype.command = function (id, element, command, args) {\n            if (command == 'register') {\n                this.register(id, args[0]);\n                return;\n            }\n            if (command == 'create') {\n                var options = (args[0] || {});\n                this.create(id, element, options);\n                return;\n            }\n            var player = this._getPlayer(id);\n            switch (command) {\n                case 'play':\n                    player.play();\n                    break;\n                case 'pause':\n                    player.pause();\n                    break;\n                case 'reset':\n                    player.reset();\n                    break;\n                case 'restart':\n                    player.restart();\n                    break;\n                case 'finish':\n                    player.finish();\n                    break;\n                case 'init':\n                    player.init();\n                    break;\n                case 'setPosition':\n                    player.setPosition(parseFloat(args[0]));\n                    break;\n                case 'destroy':\n                    this.destroy(id);\n                    break;\n            }\n        };\n        return TimelineAnimationEngine;\n    }());\n\n    var QUEUED_CLASSNAME = 'ng-animate-queued';\n    var QUEUED_SELECTOR = '.ng-animate-queued';\n    var DISABLED_CLASSNAME = 'ng-animate-disabled';\n    var DISABLED_SELECTOR = '.ng-animate-disabled';\n    var STAR_CLASSNAME = 'ng-star-inserted';\n    var STAR_SELECTOR = '.ng-star-inserted';\n    var EMPTY_PLAYER_ARRAY = [];\n    var NULL_REMOVAL_STATE = {\n        namespaceId: '',\n        setForRemoval: false,\n        setForMove: false,\n        hasAnimation: false,\n        removedBeforeQueried: false\n    };\n    var NULL_REMOVED_QUERIED_STATE = {\n        namespaceId: '',\n        setForMove: false,\n        setForRemoval: false,\n        hasAnimation: false,\n        removedBeforeQueried: true\n    };\n    var REMOVAL_FLAG = '__ng_removed';\n    var StateValue = /** @class */ (function () {\n        function StateValue(input, namespaceId) {\n            if (namespaceId === void 0) { namespaceId = ''; }\n            this.namespaceId = namespaceId;\n            var isObj = input && input.hasOwnProperty('value');\n            var value = isObj ? input['value'] : input;\n            this.value = normalizeTriggerValue(value);\n            if (isObj) {\n                var options = copyObj(input);\n                delete options['value'];\n                this.options = options;\n            }\n            else {\n                this.options = {};\n            }\n            if (!this.options.params) {\n                this.options.params = {};\n            }\n        }\n        Object.defineProperty(StateValue.prototype, \"params\", {\n            get: function () { return this.options.params; },\n            enumerable: true,\n            configurable: true\n        });\n        StateValue.prototype.absorbOptions = function (options) {\n            var newParams = options.params;\n            if (newParams) {\n                var oldParams_1 = this.options.params;\n                Object.keys(newParams).forEach(function (prop) {\n                    if (oldParams_1[prop] == null) {\n                        oldParams_1[prop] = newParams[prop];\n                    }\n                });\n            }\n        };\n        return StateValue;\n    }());\n    var VOID_VALUE = 'void';\n    var DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\n    var AnimationTransitionNamespace = /** @class */ (function () {\n        function AnimationTransitionNamespace(id, hostElement, _engine) {\n            this.id = id;\n            this.hostElement = hostElement;\n            this._engine = _engine;\n            this.players = [];\n            this._triggers = {};\n            this._queue = [];\n            this._elementListeners = new Map();\n            this._hostClassName = 'ng-tns-' + id;\n            addClass(hostElement, this._hostClassName);\n        }\n        AnimationTransitionNamespace.prototype.listen = function (element, name, phase, callback) {\n            var _this = this;\n            if (!this._triggers.hasOwnProperty(name)) {\n                throw new Error(\"Unable to listen on the animation trigger event \\\"\" + phase + \"\\\" because the animation trigger \\\"\" + name + \"\\\" doesn't exist!\");\n            }\n            if (phase == null || phase.length == 0) {\n                throw new Error(\"Unable to listen on the animation trigger \\\"\" + name + \"\\\" because the provided event is undefined!\");\n            }\n            if (!isTriggerEventValid(phase)) {\n                throw new Error(\"The provided animation trigger event \\\"\" + phase + \"\\\" for the animation trigger \\\"\" + name + \"\\\" is not supported!\");\n            }\n            var listeners = getOrSetAsInMap(this._elementListeners, element, []);\n            var data = { name: name, phase: phase, callback: callback };\n            listeners.push(data);\n            var triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n            if (!triggersWithStates.hasOwnProperty(name)) {\n                addClass(element, NG_TRIGGER_CLASSNAME);\n                addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n                triggersWithStates[name] = DEFAULT_STATE_VALUE;\n            }\n            return function () {\n                // the event listener is removed AFTER the flush has occurred such\n                // that leave animations callbacks can fire (otherwise if the node\n                // is removed in between then the listeners would be deregistered)\n                _this._engine.afterFlush(function () {\n                    var index = listeners.indexOf(data);\n                    if (index >= 0) {\n                        listeners.splice(index, 1);\n                    }\n                    if (!_this._triggers[name]) {\n                        delete triggersWithStates[name];\n                    }\n                });\n            };\n        };\n        AnimationTransitionNamespace.prototype.register = function (name, ast) {\n            if (this._triggers[name]) {\n                // throw\n                return false;\n            }\n            else {\n                this._triggers[name] = ast;\n                return true;\n            }\n        };\n        AnimationTransitionNamespace.prototype._getTrigger = function (name) {\n            var trigger = this._triggers[name];\n            if (!trigger) {\n                throw new Error(\"The provided animation trigger \\\"\" + name + \"\\\" has not been registered!\");\n            }\n            return trigger;\n        };\n        AnimationTransitionNamespace.prototype.trigger = function (element, triggerName, value, defaultToFallback) {\n            var _this = this;\n            if (defaultToFallback === void 0) { defaultToFallback = true; }\n            var trigger = this._getTrigger(triggerName);\n            var player = new TransitionAnimationPlayer(this.id, triggerName, element);\n            var triggersWithStates = this._engine.statesByElement.get(element);\n            if (!triggersWithStates) {\n                addClass(element, NG_TRIGGER_CLASSNAME);\n                addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n                this._engine.statesByElement.set(element, triggersWithStates = {});\n            }\n            var fromState = triggersWithStates[triggerName];\n            var toState = new StateValue(value, this.id);\n            var isObj = value && value.hasOwnProperty('value');\n            if (!isObj && fromState) {\n                toState.absorbOptions(fromState.options);\n            }\n            triggersWithStates[triggerName] = toState;\n            if (!fromState) {\n                fromState = DEFAULT_STATE_VALUE;\n            }\n            var isRemoval = toState.value === VOID_VALUE;\n            // normally this isn't reached by here, however, if an object expression\n            // is passed in then it may be a new object each time. Comparing the value\n            // is important since that will stay the same despite there being a new object.\n            // The removal arc here is special cased because the same element is triggered\n            // twice in the event that it contains animations on the outer/inner portions\n            // of the host container\n            if (!isRemoval && fromState.value === toState.value) {\n                // this means that despite the value not changing, some inner params\n                // have changed which means that the animation final styles need to be applied\n                if (!objEquals(fromState.params, toState.params)) {\n                    var errors = [];\n                    var fromStyles_1 = trigger.matchStyles(fromState.value, fromState.params, errors);\n                    var toStyles_1 = trigger.matchStyles(toState.value, toState.params, errors);\n                    if (errors.length) {\n                        this._engine.reportError(errors);\n                    }\n                    else {\n                        this._engine.afterFlush(function () {\n                            eraseStyles(element, fromStyles_1);\n                            setStyles(element, toStyles_1);\n                        });\n                    }\n                }\n                return;\n            }\n            var playersOnElement = getOrSetAsInMap(this._engine.playersByElement, element, []);\n            playersOnElement.forEach(function (player) {\n                // only remove the player if it is queued on the EXACT same trigger/namespace\n                // we only also deal with queued players here because if the animation has\n                // started then we want to keep the player alive until the flush happens\n                // (which is where the previousPlayers are passed into the new palyer)\n                if (player.namespaceId == _this.id && player.triggerName == triggerName && player.queued) {\n                    player.destroy();\n                }\n            });\n            var transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n            var isFallbackTransition = false;\n            if (!transition) {\n                if (!defaultToFallback)\n                    return;\n                transition = trigger.fallbackTransition;\n                isFallbackTransition = true;\n            }\n            this._engine.totalQueuedPlayers++;\n            this._queue.push({ element: element, triggerName: triggerName, transition: transition, fromState: fromState, toState: toState, player: player, isFallbackTransition: isFallbackTransition });\n            if (!isFallbackTransition) {\n                addClass(element, QUEUED_CLASSNAME);\n                player.onStart(function () { removeClass(element, QUEUED_CLASSNAME); });\n            }\n            player.onDone(function () {\n                var index = _this.players.indexOf(player);\n                if (index >= 0) {\n                    _this.players.splice(index, 1);\n                }\n                var players = _this._engine.playersByElement.get(element);\n                if (players) {\n                    var index_1 = players.indexOf(player);\n                    if (index_1 >= 0) {\n                        players.splice(index_1, 1);\n                    }\n                }\n            });\n            this.players.push(player);\n            playersOnElement.push(player);\n            return player;\n        };\n        AnimationTransitionNamespace.prototype.deregister = function (name) {\n            var _this = this;\n            delete this._triggers[name];\n            this._engine.statesByElement.forEach(function (stateMap, element) { delete stateMap[name]; });\n            this._elementListeners.forEach(function (listeners, element) {\n                _this._elementListeners.set(element, listeners.filter(function (entry) { return entry.name != name; }));\n            });\n        };\n        AnimationTransitionNamespace.prototype.clearElementCache = function (element) {\n            this._engine.statesByElement.delete(element);\n            this._elementListeners.delete(element);\n            var elementPlayers = this._engine.playersByElement.get(element);\n            if (elementPlayers) {\n                elementPlayers.forEach(function (player) { return player.destroy(); });\n                this._engine.playersByElement.delete(element);\n            }\n        };\n        AnimationTransitionNamespace.prototype._signalRemovalForInnerTriggers = function (rootElement, context, animate) {\n            var _this = this;\n            if (animate === void 0) { animate = false; }\n            // emulate a leave animation for all inner nodes within this node.\n            // If there are no animations found for any of the nodes then clear the cache\n            // for the element.\n            this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(function (elm) {\n                // this means that an inner remove() operation has already kicked off\n                // the animation on this element...\n                if (elm[REMOVAL_FLAG])\n                    return;\n                var namespaces = _this._engine.fetchNamespacesByElement(elm);\n                if (namespaces.size) {\n                    namespaces.forEach(function (ns) { return ns.triggerLeaveAnimation(elm, context, false, true); });\n                }\n                else {\n                    _this.clearElementCache(elm);\n                }\n            });\n        };\n        AnimationTransitionNamespace.prototype.triggerLeaveAnimation = function (element, context, destroyAfterComplete, defaultToFallback) {\n            var _this = this;\n            var triggerStates = this._engine.statesByElement.get(element);\n            if (triggerStates) {\n                var players_1 = [];\n                Object.keys(triggerStates).forEach(function (triggerName) {\n                    // this check is here in the event that an element is removed\n                    // twice (both on the host level and the component level)\n                    if (_this._triggers[triggerName]) {\n                        var player = _this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                        if (player) {\n                            players_1.push(player);\n                        }\n                    }\n                });\n                if (players_1.length) {\n                    this._engine.markElementAsRemoved(this.id, element, true, context);\n                    if (destroyAfterComplete) {\n                        optimizeGroupPlayer(players_1).onDone(function () { return _this._engine.processLeaveNode(element); });\n                    }\n                    return true;\n                }\n            }\n            return false;\n        };\n        AnimationTransitionNamespace.prototype.prepareLeaveAnimationListeners = function (element) {\n            var _this = this;\n            var listeners = this._elementListeners.get(element);\n            if (listeners) {\n                var visitedTriggers_1 = new Set();\n                listeners.forEach(function (listener) {\n                    var triggerName = listener.name;\n                    if (visitedTriggers_1.has(triggerName))\n                        return;\n                    visitedTriggers_1.add(triggerName);\n                    var trigger = _this._triggers[triggerName];\n                    var transition = trigger.fallbackTransition;\n                    var elementStates = _this._engine.statesByElement.get(element);\n                    var fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n                    var toState = new StateValue(VOID_VALUE);\n                    var player = new TransitionAnimationPlayer(_this.id, triggerName, element);\n                    _this._engine.totalQueuedPlayers++;\n                    _this._queue.push({\n                        element: element,\n                        triggerName: triggerName,\n                        transition: transition,\n                        fromState: fromState,\n                        toState: toState,\n                        player: player,\n                        isFallbackTransition: true\n                    });\n                });\n            }\n        };\n        AnimationTransitionNamespace.prototype.removeNode = function (element, context) {\n            var _this = this;\n            var engine = this._engine;\n            if (element.childElementCount) {\n                this._signalRemovalForInnerTriggers(element, context, true);\n            }\n            // this means that a * => VOID animation was detected and kicked off\n            if (this.triggerLeaveAnimation(element, context, true))\n                return;\n            // find the player that is animating and make sure that the\n            // removal is delayed until that player has completed\n            var containsPotentialParentTransition = false;\n            if (engine.totalAnimations) {\n                var currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n                // when this `if statement` does not continue forward it means that\n                // a previous animation query has selected the current element and\n                // is animating it. In this situation want to continue forwards and\n                // allow the element to be queued up for animation later.\n                if (currentPlayers && currentPlayers.length) {\n                    containsPotentialParentTransition = true;\n                }\n                else {\n                    var parent_1 = element;\n                    while (parent_1 = parent_1.parentNode) {\n                        var triggers = engine.statesByElement.get(parent_1);\n                        if (triggers) {\n                            containsPotentialParentTransition = true;\n                            break;\n                        }\n                    }\n                }\n            }\n            // at this stage we know that the element will either get removed\n            // during flush or will be picked up by a parent query. Either way\n            // we need to fire the listeners for this element when it DOES get\n            // removed (once the query parent animation is done or after flush)\n            this.prepareLeaveAnimationListeners(element);\n            // whether or not a parent has an animation we need to delay the deferral of the leave\n            // operation until we have more information (which we do after flush() has been called)\n            if (containsPotentialParentTransition) {\n                engine.markElementAsRemoved(this.id, element, false, context);\n            }\n            else {\n                // we do this after the flush has occurred such\n                // that the callbacks can be fired\n                engine.afterFlush(function () { return _this.clearElementCache(element); });\n                engine.destroyInnerAnimations(element);\n                engine._onRemovalComplete(element, context);\n            }\n        };\n        AnimationTransitionNamespace.prototype.insertNode = function (element, parent) { addClass(element, this._hostClassName); };\n        AnimationTransitionNamespace.prototype.drainQueuedTransitions = function (microtaskId) {\n            var _this = this;\n            var instructions = [];\n            this._queue.forEach(function (entry) {\n                var player = entry.player;\n                if (player.destroyed)\n                    return;\n                var element = entry.element;\n                var listeners = _this._elementListeners.get(element);\n                if (listeners) {\n                    listeners.forEach(function (listener) {\n                        if (listener.name == entry.triggerName) {\n                            var baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                            baseEvent['_data'] = microtaskId;\n                            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                        }\n                    });\n                }\n                if (player.markedForDestroy) {\n                    _this._engine.afterFlush(function () {\n                        // now we can destroy the element properly since the event listeners have\n                        // been bound to the player\n                        player.destroy();\n                    });\n                }\n                else {\n                    instructions.push(entry);\n                }\n            });\n            this._queue = [];\n            return instructions.sort(function (a, b) {\n                // if depCount == 0 them move to front\n                // otherwise if a contains b then move back\n                var d0 = a.transition.ast.depCount;\n                var d1 = b.transition.ast.depCount;\n                if (d0 == 0 || d1 == 0) {\n                    return d0 - d1;\n                }\n                return _this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n            });\n        };\n        AnimationTransitionNamespace.prototype.destroy = function (context) {\n            this.players.forEach(function (p) { return p.destroy(); });\n            this._signalRemovalForInnerTriggers(this.hostElement, context);\n        };\n        AnimationTransitionNamespace.prototype.elementContainsData = function (element) {\n            var containsData = false;\n            if (this._elementListeners.has(element))\n                containsData = true;\n            containsData =\n                (this._queue.find(function (entry) { return entry.element === element; }) ? true : false) || containsData;\n            return containsData;\n        };\n        return AnimationTransitionNamespace;\n    }());\n    var TransitionAnimationEngine = /** @class */ (function () {\n        function TransitionAnimationEngine(bodyNode, driver, _normalizer) {\n            this.bodyNode = bodyNode;\n            this.driver = driver;\n            this._normalizer = _normalizer;\n            this.players = [];\n            this.newHostElements = new Map();\n            this.playersByElement = new Map();\n            this.playersByQueriedElement = new Map();\n            this.statesByElement = new Map();\n            this.disabledNodes = new Set();\n            this.totalAnimations = 0;\n            this.totalQueuedPlayers = 0;\n            this._namespaceLookup = {};\n            this._namespaceList = [];\n            this._flushFns = [];\n            this._whenQuietFns = [];\n            this.namespacesByHostElement = new Map();\n            this.collectedEnterElements = [];\n            this.collectedLeaveElements = [];\n            // this method is designed to be overridden by the code that uses this engine\n            this.onRemovalComplete = function (element, context) { };\n        }\n        /** @internal */\n        TransitionAnimationEngine.prototype._onRemovalComplete = function (element, context) { this.onRemovalComplete(element, context); };\n        Object.defineProperty(TransitionAnimationEngine.prototype, \"queuedPlayers\", {\n            get: function () {\n                var players = [];\n                this._namespaceList.forEach(function (ns) {\n                    ns.players.forEach(function (player) {\n                        if (player.queued) {\n                            players.push(player);\n                        }\n                    });\n                });\n                return players;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        TransitionAnimationEngine.prototype.createNamespace = function (namespaceId, hostElement) {\n            var ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n            if (hostElement.parentNode) {\n                this._balanceNamespaceList(ns, hostElement);\n            }\n            else {\n                // defer this later until flush during when the host element has\n                // been inserted so that we know exactly where to place it in\n                // the namespace list\n                this.newHostElements.set(hostElement, ns);\n                // given that this host element is apart of the animation code, it\n                // may or may not be inserted by a parent node that is an of an\n                // animation renderer type. If this happens then we can still have\n                // access to this item when we query for :enter nodes. If the parent\n                // is a renderer then the set data-structure will normalize the entry\n                this.collectEnterElement(hostElement);\n            }\n            return this._namespaceLookup[namespaceId] = ns;\n        };\n        TransitionAnimationEngine.prototype._balanceNamespaceList = function (ns, hostElement) {\n            var limit = this._namespaceList.length - 1;\n            if (limit >= 0) {\n                var found = false;\n                for (var i = limit; i >= 0; i--) {\n                    var nextNamespace = this._namespaceList[i];\n                    if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n                        this._namespaceList.splice(i + 1, 0, ns);\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    this._namespaceList.splice(0, 0, ns);\n                }\n            }\n            else {\n                this._namespaceList.push(ns);\n            }\n            this.namespacesByHostElement.set(hostElement, ns);\n            return ns;\n        };\n        TransitionAnimationEngine.prototype.register = function (namespaceId, hostElement) {\n            var ns = this._namespaceLookup[namespaceId];\n            if (!ns) {\n                ns = this.createNamespace(namespaceId, hostElement);\n            }\n            return ns;\n        };\n        TransitionAnimationEngine.prototype.registerTrigger = function (namespaceId, name, trigger) {\n            var ns = this._namespaceLookup[namespaceId];\n            if (ns && ns.register(name, trigger)) {\n                this.totalAnimations++;\n            }\n        };\n        TransitionAnimationEngine.prototype.destroy = function (namespaceId, context) {\n            var _this = this;\n            if (!namespaceId)\n                return;\n            var ns = this._fetchNamespace(namespaceId);\n            this.afterFlush(function () {\n                _this.namespacesByHostElement.delete(ns.hostElement);\n                delete _this._namespaceLookup[namespaceId];\n                var index = _this._namespaceList.indexOf(ns);\n                if (index >= 0) {\n                    _this._namespaceList.splice(index, 1);\n                }\n            });\n            this.afterFlushAnimationsDone(function () { return ns.destroy(context); });\n        };\n        TransitionAnimationEngine.prototype._fetchNamespace = function (id) { return this._namespaceLookup[id]; };\n        TransitionAnimationEngine.prototype.fetchNamespacesByElement = function (element) {\n            // normally there should only be one namespace per element, however\n            // if @triggers are placed on both the component element and then\n            // its host element (within the component code) then there will be\n            // two namespaces returned. We use a set here to simply the dedupe\n            // of namespaces incase there are multiple triggers both the elm and host\n            var namespaces = new Set();\n            var elementStates = this.statesByElement.get(element);\n            if (elementStates) {\n                var keys = Object.keys(elementStates);\n                for (var i = 0; i < keys.length; i++) {\n                    var nsId = elementStates[keys[i]].namespaceId;\n                    if (nsId) {\n                        var ns = this._fetchNamespace(nsId);\n                        if (ns) {\n                            namespaces.add(ns);\n                        }\n                    }\n                }\n            }\n            return namespaces;\n        };\n        TransitionAnimationEngine.prototype.trigger = function (namespaceId, element, name, value) {\n            if (isElementNode(element)) {\n                var ns = this._fetchNamespace(namespaceId);\n                if (ns) {\n                    ns.trigger(element, name, value);\n                    return true;\n                }\n            }\n            return false;\n        };\n        TransitionAnimationEngine.prototype.insertNode = function (namespaceId, element, parent, insertBefore) {\n            if (!isElementNode(element))\n                return;\n            // special case for when an element is removed and reinserted (move operation)\n            // when this occurs we do not want to use the element for deletion later\n            var details = element[REMOVAL_FLAG];\n            if (details && details.setForRemoval) {\n                details.setForRemoval = false;\n                details.setForMove = true;\n                var index = this.collectedLeaveElements.indexOf(element);\n                if (index >= 0) {\n                    this.collectedLeaveElements.splice(index, 1);\n                }\n            }\n            // in the event that the namespaceId is blank then the caller\n            // code does not contain any animation code in it, but it is\n            // just being called so that the node is marked as being inserted\n            if (namespaceId) {\n                var ns = this._fetchNamespace(namespaceId);\n                // This if-statement is a workaround for router issue #21947.\n                // The router sometimes hits a race condition where while a route\n                // is being instantiated a new navigation arrives, triggering leave\n                // animation of DOM that has not been fully initialized, until this\n                // is resolved, we need to handle the scenario when DOM is not in a\n                // consistent state during the animation.\n                if (ns) {\n                    ns.insertNode(element, parent);\n                }\n            }\n            // only *directives and host elements are inserted before\n            if (insertBefore) {\n                this.collectEnterElement(element);\n            }\n        };\n        TransitionAnimationEngine.prototype.collectEnterElement = function (element) { this.collectedEnterElements.push(element); };\n        TransitionAnimationEngine.prototype.markElementAsDisabled = function (element, value) {\n            if (value) {\n                if (!this.disabledNodes.has(element)) {\n                    this.disabledNodes.add(element);\n                    addClass(element, DISABLED_CLASSNAME);\n                }\n            }\n            else if (this.disabledNodes.has(element)) {\n                this.disabledNodes.delete(element);\n                removeClass(element, DISABLED_CLASSNAME);\n            }\n        };\n        TransitionAnimationEngine.prototype.removeNode = function (namespaceId, element, context) {\n            if (!isElementNode(element)) {\n                this._onRemovalComplete(element, context);\n                return;\n            }\n            var ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n            if (ns) {\n                ns.removeNode(element, context);\n            }\n            else {\n                this.markElementAsRemoved(namespaceId, element, false, context);\n            }\n        };\n        TransitionAnimationEngine.prototype.markElementAsRemoved = function (namespaceId, element, hasAnimation, context) {\n            this.collectedLeaveElements.push(element);\n            element[REMOVAL_FLAG] = {\n                namespaceId: namespaceId,\n                setForRemoval: context, hasAnimation: hasAnimation,\n                removedBeforeQueried: false\n            };\n        };\n        TransitionAnimationEngine.prototype.listen = function (namespaceId, element, name, phase, callback) {\n            if (isElementNode(element)) {\n                return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n            }\n            return function () { };\n        };\n        TransitionAnimationEngine.prototype._buildInstruction = function (entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n            return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n        };\n        TransitionAnimationEngine.prototype.destroyInnerAnimations = function (containerElement) {\n            var _this = this;\n            var elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n            elements.forEach(function (element) { return _this.destroyActiveAnimationsForElement(element); });\n            if (this.playersByQueriedElement.size == 0)\n                return;\n            elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n            elements.forEach(function (element) { return _this.finishActiveQueriedAnimationOnElement(element); });\n        };\n        TransitionAnimationEngine.prototype.destroyActiveAnimationsForElement = function (element) {\n            var players = this.playersByElement.get(element);\n            if (players) {\n                players.forEach(function (player) {\n                    // special case for when an element is set for destruction, but hasn't started.\n                    // in this situation we want to delay the destruction until the flush occurs\n                    // so that any event listeners attached to the player are triggered.\n                    if (player.queued) {\n                        player.markedForDestroy = true;\n                    }\n                    else {\n                        player.destroy();\n                    }\n                });\n            }\n        };\n        TransitionAnimationEngine.prototype.finishActiveQueriedAnimationOnElement = function (element) {\n            var players = this.playersByQueriedElement.get(element);\n            if (players) {\n                players.forEach(function (player) { return player.finish(); });\n            }\n        };\n        TransitionAnimationEngine.prototype.whenRenderingDone = function () {\n            var _this = this;\n            return new Promise(function (resolve) {\n                if (_this.players.length) {\n                    return optimizeGroupPlayer(_this.players).onDone(function () { return resolve(); });\n                }\n                else {\n                    resolve();\n                }\n            });\n        };\n        TransitionAnimationEngine.prototype.processLeaveNode = function (element) {\n            var _this = this;\n            var details = element[REMOVAL_FLAG];\n            if (details && details.setForRemoval) {\n                // this will prevent it from removing it twice\n                element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n                if (details.namespaceId) {\n                    this.destroyInnerAnimations(element);\n                    var ns = this._fetchNamespace(details.namespaceId);\n                    if (ns) {\n                        ns.clearElementCache(element);\n                    }\n                }\n                this._onRemovalComplete(element, details.setForRemoval);\n            }\n            if (this.driver.matchesElement(element, DISABLED_SELECTOR)) {\n                this.markElementAsDisabled(element, false);\n            }\n            this.driver.query(element, DISABLED_SELECTOR, true).forEach(function (node) {\n                _this.markElementAsDisabled(node, false);\n            });\n        };\n        TransitionAnimationEngine.prototype.flush = function (microtaskId) {\n            var _this = this;\n            if (microtaskId === void 0) { microtaskId = -1; }\n            var players = [];\n            if (this.newHostElements.size) {\n                this.newHostElements.forEach(function (ns, element) { return _this._balanceNamespaceList(ns, element); });\n                this.newHostElements.clear();\n            }\n            if (this.totalAnimations && this.collectedEnterElements.length) {\n                for (var i = 0; i < this.collectedEnterElements.length; i++) {\n                    var elm = this.collectedEnterElements[i];\n                    addClass(elm, STAR_CLASSNAME);\n                }\n            }\n            if (this._namespaceList.length &&\n                (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n                var cleanupFns = [];\n                try {\n                    players = this._flushAnimations(cleanupFns, microtaskId);\n                }\n                finally {\n                    for (var i = 0; i < cleanupFns.length; i++) {\n                        cleanupFns[i]();\n                    }\n                }\n            }\n            else {\n                for (var i = 0; i < this.collectedLeaveElements.length; i++) {\n                    var element = this.collectedLeaveElements[i];\n                    this.processLeaveNode(element);\n                }\n            }\n            this.totalQueuedPlayers = 0;\n            this.collectedEnterElements.length = 0;\n            this.collectedLeaveElements.length = 0;\n            this._flushFns.forEach(function (fn) { return fn(); });\n            this._flushFns = [];\n            if (this._whenQuietFns.length) {\n                // we move these over to a variable so that\n                // if any new callbacks are registered in another\n                // flush they do not populate the existing set\n                var quietFns_1 = this._whenQuietFns;\n                this._whenQuietFns = [];\n                if (players.length) {\n                    optimizeGroupPlayer(players).onDone(function () { quietFns_1.forEach(function (fn) { return fn(); }); });\n                }\n                else {\n                    quietFns_1.forEach(function (fn) { return fn(); });\n                }\n            }\n        };\n        TransitionAnimationEngine.prototype.reportError = function (errors) {\n            throw new Error(\"Unable to process animations due to the following failed trigger transitions\\n \" + errors.join('\\n'));\n        };\n        TransitionAnimationEngine.prototype._flushAnimations = function (cleanupFns, microtaskId) {\n            var _this = this;\n            var subTimelines = new ElementInstructionMap();\n            var skippedPlayers = [];\n            var skippedPlayersMap = new Map();\n            var queuedInstructions = [];\n            var queriedElements = new Map();\n            var allPreStyleElements = new Map();\n            var allPostStyleElements = new Map();\n            var disabledElementsSet = new Set();\n            this.disabledNodes.forEach(function (node) {\n                disabledElementsSet.add(node);\n                var nodesThatAreDisabled = _this.driver.query(node, QUEUED_SELECTOR, true);\n                for (var i_1 = 0; i_1 < nodesThatAreDisabled.length; i_1++) {\n                    disabledElementsSet.add(nodesThatAreDisabled[i_1]);\n                }\n            });\n            var bodyNode = this.bodyNode;\n            var allTriggerElements = Array.from(this.statesByElement.keys());\n            var enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n            // this must occur before the instructions are built below such that\n            // the :enter queries match the elements (since the timeline queries\n            // are fired during instruction building).\n            var enterNodeMapIds = new Map();\n            var i = 0;\n            enterNodeMap.forEach(function (nodes, root) {\n                var className = ENTER_CLASSNAME + i++;\n                enterNodeMapIds.set(root, className);\n                nodes.forEach(function (node) { return addClass(node, className); });\n            });\n            var allLeaveNodes = [];\n            var mergedLeaveNodes = new Set();\n            var leaveNodesWithoutAnimations = new Set();\n            for (var i_2 = 0; i_2 < this.collectedLeaveElements.length; i_2++) {\n                var element = this.collectedLeaveElements[i_2];\n                var details = element[REMOVAL_FLAG];\n                if (details && details.setForRemoval) {\n                    allLeaveNodes.push(element);\n                    mergedLeaveNodes.add(element);\n                    if (details.hasAnimation) {\n                        this.driver.query(element, STAR_SELECTOR, true).forEach(function (elm) { return mergedLeaveNodes.add(elm); });\n                    }\n                    else {\n                        leaveNodesWithoutAnimations.add(element);\n                    }\n                }\n            }\n            var leaveNodeMapIds = new Map();\n            var leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n            leaveNodeMap.forEach(function (nodes, root) {\n                var className = LEAVE_CLASSNAME + i++;\n                leaveNodeMapIds.set(root, className);\n                nodes.forEach(function (node) { return addClass(node, className); });\n            });\n            cleanupFns.push(function () {\n                enterNodeMap.forEach(function (nodes, root) {\n                    var className = enterNodeMapIds.get(root);\n                    nodes.forEach(function (node) { return removeClass(node, className); });\n                });\n                leaveNodeMap.forEach(function (nodes, root) {\n                    var className = leaveNodeMapIds.get(root);\n                    nodes.forEach(function (node) { return removeClass(node, className); });\n                });\n                allLeaveNodes.forEach(function (element) { _this.processLeaveNode(element); });\n            });\n            var allPlayers = [];\n            var erroneousTransitions = [];\n            for (var i_3 = this._namespaceList.length - 1; i_3 >= 0; i_3--) {\n                var ns = this._namespaceList[i_3];\n                ns.drainQueuedTransitions(microtaskId).forEach(function (entry) {\n                    var player = entry.player;\n                    var element = entry.element;\n                    allPlayers.push(player);\n                    if (_this.collectedEnterElements.length) {\n                        var details = element[REMOVAL_FLAG];\n                        // move animations are currently not supported...\n                        if (details && details.setForMove) {\n                            player.destroy();\n                            return;\n                        }\n                    }\n                    var nodeIsOrphaned = !bodyNode || !_this.driver.containsElement(bodyNode, element);\n                    var leaveClassName = leaveNodeMapIds.get(element);\n                    var enterClassName = enterNodeMapIds.get(element);\n                    var instruction = _this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n                    if (instruction.errors && instruction.errors.length) {\n                        erroneousTransitions.push(instruction);\n                        return;\n                    }\n                    // even though the element may not be apart of the DOM, it may\n                    // still be added at a later point (due to the mechanics of content\n                    // projection and/or dynamic component insertion) therefore it's\n                    // important we still style the element.\n                    if (nodeIsOrphaned) {\n                        player.onStart(function () { return eraseStyles(element, instruction.fromStyles); });\n                        player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                        skippedPlayers.push(player);\n                        return;\n                    }\n                    // if a unmatched transition is queued to go then it SHOULD NOT render\n                    // an animation and cancel the previously running animations.\n                    if (entry.isFallbackTransition) {\n                        player.onStart(function () { return eraseStyles(element, instruction.fromStyles); });\n                        player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                        skippedPlayers.push(player);\n                        return;\n                    }\n                    // this means that if a parent animation uses this animation as a sub trigger\n                    // then it will instruct the timeline builder to not add a player delay, but\n                    // instead stretch the first keyframe gap up until the animation starts. The\n                    // reason this is important is to prevent extra initialization styles from being\n                    // required by the user in the animation.\n                    instruction.timelines.forEach(function (tl) { return tl.stretchStartingKeyframe = true; });\n                    subTimelines.append(element, instruction.timelines);\n                    var tuple = { instruction: instruction, player: player, element: element };\n                    queuedInstructions.push(tuple);\n                    instruction.queriedElements.forEach(function (element) { return getOrSetAsInMap(queriedElements, element, []).push(player); });\n                    instruction.preStyleProps.forEach(function (stringMap, element) {\n                        var props = Object.keys(stringMap);\n                        if (props.length) {\n                            var setVal_1 = allPreStyleElements.get(element);\n                            if (!setVal_1) {\n                                allPreStyleElements.set(element, setVal_1 = new Set());\n                            }\n                            props.forEach(function (prop) { return setVal_1.add(prop); });\n                        }\n                    });\n                    instruction.postStyleProps.forEach(function (stringMap, element) {\n                        var props = Object.keys(stringMap);\n                        var setVal = allPostStyleElements.get(element);\n                        if (!setVal) {\n                            allPostStyleElements.set(element, setVal = new Set());\n                        }\n                        props.forEach(function (prop) { return setVal.add(prop); });\n                    });\n                });\n            }\n            if (erroneousTransitions.length) {\n                var errors_1 = [];\n                erroneousTransitions.forEach(function (instruction) {\n                    errors_1.push(\"@\" + instruction.triggerName + \" has failed due to:\\n\");\n                    instruction.errors.forEach(function (error) { return errors_1.push(\"- \" + error + \"\\n\"); });\n                });\n                allPlayers.forEach(function (player) { return player.destroy(); });\n                this.reportError(errors_1);\n            }\n            var allPreviousPlayersMap = new Map();\n            // this map works to tell which element in the DOM tree is contained by\n            // which animation. Further down below this map will get populated once\n            // the players are built and in doing so it can efficiently figure out\n            // if a sub player is skipped due to a parent player having priority.\n            var animationElementMap = new Map();\n            queuedInstructions.forEach(function (entry) {\n                var element = entry.element;\n                if (subTimelines.has(element)) {\n                    animationElementMap.set(element, element);\n                    _this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n                }\n            });\n            skippedPlayers.forEach(function (player) {\n                var element = player.element;\n                var previousPlayers = _this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n                previousPlayers.forEach(function (prevPlayer) {\n                    getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer);\n                    prevPlayer.destroy();\n                });\n            });\n            // this is a special case for nodes that will be removed (either by)\n            // having their own leave animations or by being queried in a container\n            // that will be removed once a parent animation is complete. The idea\n            // here is that * styles must be identical to ! styles because of\n            // backwards compatibility (* is also filled in by default in many places).\n            // Otherwise * styles will return an empty value or auto since the element\n            // that is being getComputedStyle'd will not be visible (since * = destination)\n            var replaceNodes = allLeaveNodes.filter(function (node) {\n                return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n            });\n            // POST STAGE: fill the * styles\n            var postStylesMap = new Map();\n            var allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, animations.AUTO_STYLE);\n            allLeaveQueriedNodes.forEach(function (node) {\n                if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                    replaceNodes.push(node);\n                }\n            });\n            // PRE STAGE: fill the ! styles\n            var preStylesMap = new Map();\n            enterNodeMap.forEach(function (nodes, root) {\n                cloakAndComputeStyles(preStylesMap, _this.driver, new Set(nodes), allPreStyleElements, animations.ɵPRE_STYLE);\n            });\n            replaceNodes.forEach(function (node) {\n                var post = postStylesMap.get(node);\n                var pre = preStylesMap.get(node);\n                postStylesMap.set(node, __assign({}, post, pre));\n            });\n            var rootPlayers = [];\n            var subPlayers = [];\n            var NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n            queuedInstructions.forEach(function (entry) {\n                var element = entry.element, player = entry.player, instruction = entry.instruction;\n                // this means that it was never consumed by a parent animation which\n                // means that it is independent and therefore should be set for animation\n                if (subTimelines.has(element)) {\n                    if (disabledElementsSet.has(element)) {\n                        player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                        player.disabled = true;\n                        player.overrideTotalTime(instruction.totalTime);\n                        skippedPlayers.push(player);\n                        return;\n                    }\n                    // this will flow up the DOM and query the map to figure out\n                    // if a parent animation has priority over it. In the situation\n                    // that a parent is detected then it will cancel the loop. If\n                    // nothing is detected, or it takes a few hops to find a parent,\n                    // then it will fill in the missing nodes and signal them as having\n                    // a detected parent (or a NO_PARENT value via a special constant).\n                    var parentWithAnimation_1 = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                    if (animationElementMap.size > 1) {\n                        var elm = element;\n                        var parentsToAdd = [];\n                        while (elm = elm.parentNode) {\n                            var detectedParent = animationElementMap.get(elm);\n                            if (detectedParent) {\n                                parentWithAnimation_1 = detectedParent;\n                                break;\n                            }\n                            parentsToAdd.push(elm);\n                        }\n                        parentsToAdd.forEach(function (parent) { return animationElementMap.set(parent, parentWithAnimation_1); });\n                    }\n                    var innerPlayer = _this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                    player.setRealPlayer(innerPlayer);\n                    if (parentWithAnimation_1 === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                        rootPlayers.push(player);\n                    }\n                    else {\n                        var parentPlayers = _this.playersByElement.get(parentWithAnimation_1);\n                        if (parentPlayers && parentPlayers.length) {\n                            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                        }\n                        skippedPlayers.push(player);\n                    }\n                }\n                else {\n                    eraseStyles(element, instruction.fromStyles);\n                    player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                    // there still might be a ancestor player animating this\n                    // element therefore we will still add it as a sub player\n                    // even if its animation may be disabled\n                    subPlayers.push(player);\n                    if (disabledElementsSet.has(element)) {\n                        skippedPlayers.push(player);\n                    }\n                }\n            });\n            // find all of the sub players' corresponding inner animation player\n            subPlayers.forEach(function (player) {\n                // even if any players are not found for a sub animation then it\n                // will still complete itself after the next tick since it's Noop\n                var playersForElement = skippedPlayersMap.get(player.element);\n                if (playersForElement && playersForElement.length) {\n                    var innerPlayer = optimizeGroupPlayer(playersForElement);\n                    player.setRealPlayer(innerPlayer);\n                }\n            });\n            // the reason why we don't actually play the animation is\n            // because all that a skipped player is designed to do is to\n            // fire the start/done transition callback events\n            skippedPlayers.forEach(function (player) {\n                if (player.parentPlayer) {\n                    player.syncPlayerEvents(player.parentPlayer);\n                }\n                else {\n                    player.destroy();\n                }\n            });\n            // run through all of the queued removals and see if they\n            // were picked up by a query. If not then perform the removal\n            // operation right away unless a parent animation is ongoing.\n            for (var i_4 = 0; i_4 < allLeaveNodes.length; i_4++) {\n                var element = allLeaveNodes[i_4];\n                var details = element[REMOVAL_FLAG];\n                removeClass(element, LEAVE_CLASSNAME);\n                // this means the element has a removal animation that is being\n                // taken care of and therefore the inner elements will hang around\n                // until that animation is over (or the parent queried animation)\n                if (details && details.hasAnimation)\n                    continue;\n                var players = [];\n                // if this element is queried or if it contains queried children\n                // then we want for the element not to be removed from the page\n                // until the queried animations have finished\n                if (queriedElements.size) {\n                    var queriedPlayerResults = queriedElements.get(element);\n                    if (queriedPlayerResults && queriedPlayerResults.length) {\n                        players.push.apply(players, __spread(queriedPlayerResults));\n                    }\n                    var queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                    for (var j = 0; j < queriedInnerElements.length; j++) {\n                        var queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                        if (queriedPlayers && queriedPlayers.length) {\n                            players.push.apply(players, __spread(queriedPlayers));\n                        }\n                    }\n                }\n                var activePlayers = players.filter(function (p) { return !p.destroyed; });\n                if (activePlayers.length) {\n                    removeNodesAfterAnimationDone(this, element, activePlayers);\n                }\n                else {\n                    this.processLeaveNode(element);\n                }\n            }\n            // this is required so the cleanup method doesn't remove them\n            allLeaveNodes.length = 0;\n            rootPlayers.forEach(function (player) {\n                _this.players.push(player);\n                player.onDone(function () {\n                    player.destroy();\n                    var index = _this.players.indexOf(player);\n                    _this.players.splice(index, 1);\n                });\n                player.play();\n            });\n            return rootPlayers;\n        };\n        TransitionAnimationEngine.prototype.elementContainsData = function (namespaceId, element) {\n            var containsData = false;\n            var details = element[REMOVAL_FLAG];\n            if (details && details.setForRemoval)\n                containsData = true;\n            if (this.playersByElement.has(element))\n                containsData = true;\n            if (this.playersByQueriedElement.has(element))\n                containsData = true;\n            if (this.statesByElement.has(element))\n                containsData = true;\n            return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n        };\n        TransitionAnimationEngine.prototype.afterFlush = function (callback) { this._flushFns.push(callback); };\n        TransitionAnimationEngine.prototype.afterFlushAnimationsDone = function (callback) { this._whenQuietFns.push(callback); };\n        TransitionAnimationEngine.prototype._getPreviousPlayers = function (element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n            var players = [];\n            if (isQueriedElement) {\n                var queriedElementPlayers = this.playersByQueriedElement.get(element);\n                if (queriedElementPlayers) {\n                    players = queriedElementPlayers;\n                }\n            }\n            else {\n                var elementPlayers = this.playersByElement.get(element);\n                if (elementPlayers) {\n                    var isRemovalAnimation_1 = !toStateValue || toStateValue == VOID_VALUE;\n                    elementPlayers.forEach(function (player) {\n                        if (player.queued)\n                            return;\n                        if (!isRemovalAnimation_1 && player.triggerName != triggerName)\n                            return;\n                        players.push(player);\n                    });\n                }\n            }\n            if (namespaceId || triggerName) {\n                players = players.filter(function (player) {\n                    if (namespaceId && namespaceId != player.namespaceId)\n                        return false;\n                    if (triggerName && triggerName != player.triggerName)\n                        return false;\n                    return true;\n                });\n            }\n            return players;\n        };\n        TransitionAnimationEngine.prototype._beforeAnimationBuild = function (namespaceId, instruction, allPreviousPlayersMap) {\n            var e_1, _a;\n            var triggerName = instruction.triggerName;\n            var rootElement = instruction.element;\n            // when a removal animation occurs, ALL previous players are collected\n            // and destroyed (even if they are outside of the current namespace)\n            var targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n            var targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n            var _loop_1 = function (timelineInstruction) {\n                var element = timelineInstruction.element;\n                var isQueriedElement = element !== rootElement;\n                var players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n                var previousPlayers = this_1._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n                previousPlayers.forEach(function (player) {\n                    var realPlayer = player.getRealPlayer();\n                    if (realPlayer.beforeDestroy) {\n                        realPlayer.beforeDestroy();\n                    }\n                    player.destroy();\n                    players.push(player);\n                });\n            };\n            var this_1 = this;\n            try {\n                for (var _b = __values(instruction.timelines), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var timelineInstruction = _c.value;\n                    _loop_1(timelineInstruction);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // this needs to be done so that the PRE/POST styles can be\n            // computed properly without interfering with the previous animation\n            eraseStyles(rootElement, instruction.fromStyles);\n        };\n        TransitionAnimationEngine.prototype._buildAnimation = function (namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n            var _this = this;\n            var triggerName = instruction.triggerName;\n            var rootElement = instruction.element;\n            // we first run this so that the previous animation player\n            // data can be passed into the successive animation players\n            var allQueriedPlayers = [];\n            var allConsumedElements = new Set();\n            var allSubElements = new Set();\n            var allNewPlayers = instruction.timelines.map(function (timelineInstruction) {\n                var element = timelineInstruction.element;\n                allConsumedElements.add(element);\n                // FIXME (matsko): make sure to-be-removed animations are removed properly\n                var details = element[REMOVAL_FLAG];\n                if (details && details.removedBeforeQueried)\n                    return new animations.NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n                var isQueriedElement = element !== rootElement;\n                var previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY)\n                    .map(function (p) { return p.getRealPlayer(); }))\n                    .filter(function (p) {\n                    // the `element` is not apart of the AnimationPlayer definition, but\n                    // Mock/WebAnimations\n                    // use the element within their implementation. This will be added in Angular5 to\n                    // AnimationPlayer\n                    var pp = p;\n                    return pp.element ? pp.element === element : false;\n                });\n                var preStyles = preStylesMap.get(element);\n                var postStyles = postStylesMap.get(element);\n                var keyframes = normalizeKeyframes(_this.driver, _this._normalizer, element, timelineInstruction.keyframes, preStyles, postStyles);\n                var player = _this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n                // this means that this particular player belongs to a sub trigger. It is\n                // important that we match this player up with the corresponding (@trigger.listener)\n                if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                    allSubElements.add(element);\n                }\n                if (isQueriedElement) {\n                    var wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                    wrappedPlayer.setRealPlayer(player);\n                    allQueriedPlayers.push(wrappedPlayer);\n                }\n                return player;\n            });\n            allQueriedPlayers.forEach(function (player) {\n                getOrSetAsInMap(_this.playersByQueriedElement, player.element, []).push(player);\n                player.onDone(function () { return deleteOrUnsetInMap(_this.playersByQueriedElement, player.element, player); });\n            });\n            allConsumedElements.forEach(function (element) { return addClass(element, NG_ANIMATING_CLASSNAME); });\n            var player = optimizeGroupPlayer(allNewPlayers);\n            player.onDestroy(function () {\n                allConsumedElements.forEach(function (element) { return removeClass(element, NG_ANIMATING_CLASSNAME); });\n                setStyles(rootElement, instruction.toStyles);\n            });\n            // this basically makes all of the callbacks for sub element animations\n            // be dependent on the upper players for when they finish\n            allSubElements.forEach(function (element) { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n            return player;\n        };\n        TransitionAnimationEngine.prototype._buildPlayer = function (instruction, keyframes, previousPlayers) {\n            if (keyframes.length > 0) {\n                return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n            }\n            // special case for when an empty transition|definition is provided\n            // ... there is no point in rendering an empty animation\n            return new animations.NoopAnimationPlayer(instruction.duration, instruction.delay);\n        };\n        return TransitionAnimationEngine;\n    }());\n    var TransitionAnimationPlayer = /** @class */ (function () {\n        function TransitionAnimationPlayer(namespaceId, triggerName, element) {\n            this.namespaceId = namespaceId;\n            this.triggerName = triggerName;\n            this.element = element;\n            this._player = new animations.NoopAnimationPlayer();\n            this._containsRealPlayer = false;\n            this._queuedCallbacks = {};\n            this.destroyed = false;\n            this.markedForDestroy = false;\n            this.disabled = false;\n            this.queued = true;\n            this.totalTime = 0;\n        }\n        TransitionAnimationPlayer.prototype.setRealPlayer = function (player) {\n            var _this = this;\n            if (this._containsRealPlayer)\n                return;\n            this._player = player;\n            Object.keys(this._queuedCallbacks).forEach(function (phase) {\n                _this._queuedCallbacks[phase].forEach(function (callback) { return listenOnPlayer(player, phase, undefined, callback); });\n            });\n            this._queuedCallbacks = {};\n            this._containsRealPlayer = true;\n            this.overrideTotalTime(player.totalTime);\n            this.queued = false;\n        };\n        TransitionAnimationPlayer.prototype.getRealPlayer = function () { return this._player; };\n        TransitionAnimationPlayer.prototype.overrideTotalTime = function (totalTime) { this.totalTime = totalTime; };\n        TransitionAnimationPlayer.prototype.syncPlayerEvents = function (player) {\n            var _this = this;\n            var p = this._player;\n            if (p.triggerCallback) {\n                player.onStart(function () { return p.triggerCallback('start'); });\n            }\n            player.onDone(function () { return _this.finish(); });\n            player.onDestroy(function () { return _this.destroy(); });\n        };\n        TransitionAnimationPlayer.prototype._queueEvent = function (name, callback) {\n            getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n        };\n        TransitionAnimationPlayer.prototype.onDone = function (fn) {\n            if (this.queued) {\n                this._queueEvent('done', fn);\n            }\n            this._player.onDone(fn);\n        };\n        TransitionAnimationPlayer.prototype.onStart = function (fn) {\n            if (this.queued) {\n                this._queueEvent('start', fn);\n            }\n            this._player.onStart(fn);\n        };\n        TransitionAnimationPlayer.prototype.onDestroy = function (fn) {\n            if (this.queued) {\n                this._queueEvent('destroy', fn);\n            }\n            this._player.onDestroy(fn);\n        };\n        TransitionAnimationPlayer.prototype.init = function () { this._player.init(); };\n        TransitionAnimationPlayer.prototype.hasStarted = function () { return this.queued ? false : this._player.hasStarted(); };\n        TransitionAnimationPlayer.prototype.play = function () { !this.queued && this._player.play(); };\n        TransitionAnimationPlayer.prototype.pause = function () { !this.queued && this._player.pause(); };\n        TransitionAnimationPlayer.prototype.restart = function () { !this.queued && this._player.restart(); };\n        TransitionAnimationPlayer.prototype.finish = function () { this._player.finish(); };\n        TransitionAnimationPlayer.prototype.destroy = function () {\n            this.destroyed = true;\n            this._player.destroy();\n        };\n        TransitionAnimationPlayer.prototype.reset = function () { !this.queued && this._player.reset(); };\n        TransitionAnimationPlayer.prototype.setPosition = function (p) {\n            if (!this.queued) {\n                this._player.setPosition(p);\n            }\n        };\n        TransitionAnimationPlayer.prototype.getPosition = function () { return this.queued ? 0 : this._player.getPosition(); };\n        /** @internal */\n        TransitionAnimationPlayer.prototype.triggerCallback = function (phaseName) {\n            var p = this._player;\n            if (p.triggerCallback) {\n                p.triggerCallback(phaseName);\n            }\n        };\n        return TransitionAnimationPlayer;\n    }());\n    function deleteOrUnsetInMap(map, key, value) {\n        var currentValues;\n        if (map instanceof Map) {\n            currentValues = map.get(key);\n            if (currentValues) {\n                if (currentValues.length) {\n                    var index = currentValues.indexOf(value);\n                    currentValues.splice(index, 1);\n                }\n                if (currentValues.length == 0) {\n                    map.delete(key);\n                }\n            }\n        }\n        else {\n            currentValues = map[key];\n            if (currentValues) {\n                if (currentValues.length) {\n                    var index = currentValues.indexOf(value);\n                    currentValues.splice(index, 1);\n                }\n                if (currentValues.length == 0) {\n                    delete map[key];\n                }\n            }\n        }\n        return currentValues;\n    }\n    function normalizeTriggerValue(value) {\n        // we use `!= null` here because it's the most simple\n        // way to test against a \"falsy\" value without mixing\n        // in empty strings or a zero value. DO NOT OPTIMIZE.\n        return value != null ? value : null;\n    }\n    function isElementNode(node) {\n        return node && node['nodeType'] === 1;\n    }\n    function isTriggerEventValid(eventName) {\n        return eventName == 'start' || eventName == 'done';\n    }\n    function cloakElement(element, value) {\n        var oldValue = element.style.display;\n        element.style.display = value != null ? value : 'none';\n        return oldValue;\n    }\n    function cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n        var cloakVals = [];\n        elements.forEach(function (element) { return cloakVals.push(cloakElement(element)); });\n        var failedElements = [];\n        elementPropsMap.forEach(function (props, element) {\n            var styles = {};\n            props.forEach(function (prop) {\n                var value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n                // there is no easy way to detect this because a sub element could be removed\n                // by a parent animation element being detached.\n                if (!value || value.length == 0) {\n                    element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                    failedElements.push(element);\n                }\n            });\n            valuesMap.set(element, styles);\n        });\n        // we use a index variable here since Set.forEach(a, i) does not return\n        // an index value for the closure (but instead just the value)\n        var i = 0;\n        elements.forEach(function (element) { return cloakElement(element, cloakVals[i++]); });\n        return failedElements;\n    }\n    /*\n    Since the Angular renderer code will return a collection of inserted\n    nodes in all areas of a DOM tree, it's up to this algorithm to figure\n    out which nodes are roots for each animation @trigger.\n\n    By placing each inserted node into a Set and traversing upwards, it\n    is possible to find the @trigger elements and well any direct *star\n    insertion nodes, if a @trigger root is found then the enter element\n    is placed into the Map[@trigger] spot.\n     */\n    function buildRootMap(roots, nodes) {\n        var rootMap = new Map();\n        roots.forEach(function (root) { return rootMap.set(root, []); });\n        if (nodes.length == 0)\n            return rootMap;\n        var NULL_NODE = 1;\n        var nodeSet = new Set(nodes);\n        var localRootMap = new Map();\n        function getRoot(node) {\n            if (!node)\n                return NULL_NODE;\n            var root = localRootMap.get(node);\n            if (root)\n                return root;\n            var parent = node.parentNode;\n            if (rootMap.has(parent)) { // ngIf inside @trigger\n                root = parent;\n            }\n            else if (nodeSet.has(parent)) { // ngIf inside ngIf\n                root = NULL_NODE;\n            }\n            else { // recurse upwards\n                root = getRoot(parent);\n            }\n            localRootMap.set(node, root);\n            return root;\n        }\n        nodes.forEach(function (node) {\n            var root = getRoot(node);\n            if (root !== NULL_NODE) {\n                rootMap.get(root).push(node);\n            }\n        });\n        return rootMap;\n    }\n    var CLASSES_CACHE_KEY = '$$classes';\n    function addClass(element, className) {\n        if (element.classList) {\n            element.classList.add(className);\n        }\n        else {\n            var classes = element[CLASSES_CACHE_KEY];\n            if (!classes) {\n                classes = element[CLASSES_CACHE_KEY] = {};\n            }\n            classes[className] = true;\n        }\n    }\n    function removeClass(element, className) {\n        if (element.classList) {\n            element.classList.remove(className);\n        }\n        else {\n            var classes = element[CLASSES_CACHE_KEY];\n            if (classes) {\n                delete classes[className];\n            }\n        }\n    }\n    function removeNodesAfterAnimationDone(engine, element, players) {\n        optimizeGroupPlayer(players).onDone(function () { return engine.processLeaveNode(element); });\n    }\n    function flattenGroupPlayers(players) {\n        var finalPlayers = [];\n        _flattenGroupPlayersRecur(players, finalPlayers);\n        return finalPlayers;\n    }\n    function _flattenGroupPlayersRecur(players, finalPlayers) {\n        for (var i = 0; i < players.length; i++) {\n            var player = players[i];\n            if (player instanceof animations.ɵAnimationGroupPlayer) {\n                _flattenGroupPlayersRecur(player.players, finalPlayers);\n            }\n            else {\n                finalPlayers.push(player);\n            }\n        }\n    }\n    function objEquals(a, b) {\n        var k1 = Object.keys(a);\n        var k2 = Object.keys(b);\n        if (k1.length != k2.length)\n            return false;\n        for (var i = 0; i < k1.length; i++) {\n            var prop = k1[i];\n            if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n                return false;\n        }\n        return true;\n    }\n    function replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n        var postEntry = allPostStyleElements.get(element);\n        if (!postEntry)\n            return false;\n        var preEntry = allPreStyleElements.get(element);\n        if (preEntry) {\n            postEntry.forEach(function (data) { return preEntry.add(data); });\n        }\n        else {\n            allPreStyleElements.set(element, postEntry);\n        }\n        allPostStyleElements.delete(element);\n        return true;\n    }\n\n    var AnimationEngine = /** @class */ (function () {\n        function AnimationEngine(bodyNode, _driver, normalizer) {\n            var _this = this;\n            this.bodyNode = bodyNode;\n            this._driver = _driver;\n            this._triggerCache = {};\n            // this method is designed to be overridden by the code that uses this engine\n            this.onRemovalComplete = function (element, context) { };\n            this._transitionEngine = new TransitionAnimationEngine(bodyNode, _driver, normalizer);\n            this._timelineEngine = new TimelineAnimationEngine(bodyNode, _driver, normalizer);\n            this._transitionEngine.onRemovalComplete = function (element, context) {\n                return _this.onRemovalComplete(element, context);\n            };\n        }\n        AnimationEngine.prototype.registerTrigger = function (componentId, namespaceId, hostElement, name, metadata) {\n            var cacheKey = componentId + '-' + name;\n            var trigger = this._triggerCache[cacheKey];\n            if (!trigger) {\n                var errors = [];\n                var ast = buildAnimationAst(this._driver, metadata, errors);\n                if (errors.length) {\n                    throw new Error(\"The animation trigger \\\"\" + name + \"\\\" has failed to build due to the following errors:\\n - \" + errors.join(\"\\n - \"));\n                }\n                trigger = buildTrigger(name, ast);\n                this._triggerCache[cacheKey] = trigger;\n            }\n            this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n        };\n        AnimationEngine.prototype.register = function (namespaceId, hostElement) {\n            this._transitionEngine.register(namespaceId, hostElement);\n        };\n        AnimationEngine.prototype.destroy = function (namespaceId, context) {\n            this._transitionEngine.destroy(namespaceId, context);\n        };\n        AnimationEngine.prototype.onInsert = function (namespaceId, element, parent, insertBefore) {\n            this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n        };\n        AnimationEngine.prototype.onRemove = function (namespaceId, element, context) {\n            this._transitionEngine.removeNode(namespaceId, element, context);\n        };\n        AnimationEngine.prototype.disableAnimations = function (element, disable) {\n            this._transitionEngine.markElementAsDisabled(element, disable);\n        };\n        AnimationEngine.prototype.process = function (namespaceId, element, property, value) {\n            if (property.charAt(0) == '@') {\n                var _a = __read(parseTimelineCommand(property), 2), id = _a[0], action = _a[1];\n                var args = value;\n                this._timelineEngine.command(id, element, action, args);\n            }\n            else {\n                this._transitionEngine.trigger(namespaceId, element, property, value);\n            }\n        };\n        AnimationEngine.prototype.listen = function (namespaceId, element, eventName, eventPhase, callback) {\n            // @@listen\n            if (eventName.charAt(0) == '@') {\n                var _a = __read(parseTimelineCommand(eventName), 2), id = _a[0], action = _a[1];\n                return this._timelineEngine.listen(id, element, action, callback);\n            }\n            return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n        };\n        AnimationEngine.prototype.flush = function (microtaskId) {\n            if (microtaskId === void 0) { microtaskId = -1; }\n            this._transitionEngine.flush(microtaskId);\n        };\n        Object.defineProperty(AnimationEngine.prototype, \"players\", {\n            get: function () {\n                return this._transitionEngine.players\n                    .concat(this._timelineEngine.players);\n            },\n            enumerable: true,\n            configurable: true\n        });\n        AnimationEngine.prototype.whenRenderingDone = function () { return this._transitionEngine.whenRenderingDone(); };\n        return AnimationEngine;\n    }());\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    /**\n     * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n     * detected.\n     *\n     * In CSS there exist properties that cannot be animated within a keyframe animation\n     * (whether it be via CSS keyframes or web-animations) and the animation implementation\n     * will ignore them. This function is designed to detect those special cased styles and\n     * return a container that will be executed at the start and end of the animation.\n     *\n     * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n     */\n    function packageNonAnimatableStyles(element, styles) {\n        var startStyles = null;\n        var endStyles = null;\n        if (Array.isArray(styles) && styles.length) {\n            startStyles = filterNonAnimatableStyles(styles[0]);\n            if (styles.length > 1) {\n                endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n            }\n        }\n        else if (styles) {\n            startStyles = filterNonAnimatableStyles(styles);\n        }\n        return (startStyles || endStyles) ? new SpecialCasedStyles(element, startStyles, endStyles) :\n            null;\n    }\n    /**\n     * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n     *\n     * When started (when the `start()` method is run) then the provided `startStyles`\n     * will be applied. When finished (when the `finish()` method is called) the\n     * `endStyles` will be applied as well any any starting styles. Finally when\n     * `destroy()` is called then all styles will be removed.\n     */\n    var SpecialCasedStyles = /** @class */ (function () {\n        function SpecialCasedStyles(_element, _startStyles, _endStyles) {\n            this._element = _element;\n            this._startStyles = _startStyles;\n            this._endStyles = _endStyles;\n            this._state = 0 /* Pending */;\n            var initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n            if (!initialStyles) {\n                SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = {});\n            }\n            this._initialStyles = initialStyles;\n        }\n        SpecialCasedStyles.prototype.start = function () {\n            if (this._state < 1 /* Started */) {\n                if (this._startStyles) {\n                    setStyles(this._element, this._startStyles, this._initialStyles);\n                }\n                this._state = 1 /* Started */;\n            }\n        };\n        SpecialCasedStyles.prototype.finish = function () {\n            this.start();\n            if (this._state < 2 /* Finished */) {\n                setStyles(this._element, this._initialStyles);\n                if (this._endStyles) {\n                    setStyles(this._element, this._endStyles);\n                    this._endStyles = null;\n                }\n                this._state = 1 /* Started */;\n            }\n        };\n        SpecialCasedStyles.prototype.destroy = function () {\n            this.finish();\n            if (this._state < 3 /* Destroyed */) {\n                SpecialCasedStyles.initialStylesByElement.delete(this._element);\n                if (this._startStyles) {\n                    eraseStyles(this._element, this._startStyles);\n                    this._endStyles = null;\n                }\n                if (this._endStyles) {\n                    eraseStyles(this._element, this._endStyles);\n                    this._endStyles = null;\n                }\n                setStyles(this._element, this._initialStyles);\n                this._state = 3 /* Destroyed */;\n            }\n        };\n        SpecialCasedStyles.initialStylesByElement = new WeakMap();\n        return SpecialCasedStyles;\n    }());\n    function filterNonAnimatableStyles(styles) {\n        var result = null;\n        var props = Object.keys(styles);\n        for (var i = 0; i < props.length; i++) {\n            var prop = props[i];\n            if (isNonAnimatableStyle(prop)) {\n                result = result || {};\n                result[prop] = styles[prop];\n            }\n        }\n        return result;\n    }\n    function isNonAnimatableStyle(prop) {\n        return prop === 'display' || prop === 'position';\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n    var ELAPSED_TIME_MAX_DECIMAL_PLACES = 3;\n    var ANIMATION_PROP = 'animation';\n    var ANIMATIONEND_EVENT = 'animationend';\n    var ONE_SECOND$1 = 1000;\n    var ElementAnimationStyleHandler = /** @class */ (function () {\n        function ElementAnimationStyleHandler(_element, _name, _duration, _delay, _easing, _fillMode, _onDoneFn) {\n            var _this = this;\n            this._element = _element;\n            this._name = _name;\n            this._duration = _duration;\n            this._delay = _delay;\n            this._easing = _easing;\n            this._fillMode = _fillMode;\n            this._onDoneFn = _onDoneFn;\n            this._finished = false;\n            this._destroyed = false;\n            this._startTime = 0;\n            this._position = 0;\n            this._eventFn = function (e) { return _this._handleCallback(e); };\n        }\n        ElementAnimationStyleHandler.prototype.apply = function () {\n            applyKeyframeAnimation(this._element, this._duration + \"ms \" + this._easing + \" \" + this._delay + \"ms 1 normal \" + this._fillMode + \" \" + this._name);\n            addRemoveAnimationEvent(this._element, this._eventFn, false);\n            this._startTime = Date.now();\n        };\n        ElementAnimationStyleHandler.prototype.pause = function () { playPauseAnimation(this._element, this._name, 'paused'); };\n        ElementAnimationStyleHandler.prototype.resume = function () { playPauseAnimation(this._element, this._name, 'running'); };\n        ElementAnimationStyleHandler.prototype.setPosition = function (position) {\n            var index = findIndexForAnimation(this._element, this._name);\n            this._position = position * this._duration;\n            setAnimationStyle(this._element, 'Delay', \"-\" + this._position + \"ms\", index);\n        };\n        ElementAnimationStyleHandler.prototype.getPosition = function () { return this._position; };\n        ElementAnimationStyleHandler.prototype._handleCallback = function (event) {\n            var timestamp = event._ngTestManualTimestamp || Date.now();\n            var elapsedTime = parseFloat(event.elapsedTime.toFixed(ELAPSED_TIME_MAX_DECIMAL_PLACES)) * ONE_SECOND$1;\n            if (event.animationName == this._name &&\n                Math.max(timestamp - this._startTime, 0) >= this._delay && elapsedTime >= this._duration) {\n                this.finish();\n            }\n        };\n        ElementAnimationStyleHandler.prototype.finish = function () {\n            if (this._finished)\n                return;\n            this._finished = true;\n            this._onDoneFn();\n            addRemoveAnimationEvent(this._element, this._eventFn, true);\n        };\n        ElementAnimationStyleHandler.prototype.destroy = function () {\n            if (this._destroyed)\n                return;\n            this._destroyed = true;\n            this.finish();\n            removeKeyframeAnimation(this._element, this._name);\n        };\n        return ElementAnimationStyleHandler;\n    }());\n    function playPauseAnimation(element, name, status) {\n        var index = findIndexForAnimation(element, name);\n        setAnimationStyle(element, 'PlayState', status, index);\n    }\n    function applyKeyframeAnimation(element, value) {\n        var anim = getAnimationStyle(element, '').trim();\n        var index = 0;\n        if (anim.length) {\n            index = countChars(anim, ',') + 1;\n            value = anim + \", \" + value;\n        }\n        setAnimationStyle(element, '', value);\n        return index;\n    }\n    function removeKeyframeAnimation(element, name) {\n        var anim = getAnimationStyle(element, '');\n        var tokens = anim.split(',');\n        var index = findMatchingTokenIndex(tokens, name);\n        if (index >= 0) {\n            tokens.splice(index, 1);\n            var newValue = tokens.join(',');\n            setAnimationStyle(element, '', newValue);\n        }\n    }\n    function findIndexForAnimation(element, value) {\n        var anim = getAnimationStyle(element, '');\n        if (anim.indexOf(',') > 0) {\n            var tokens = anim.split(',');\n            return findMatchingTokenIndex(tokens, value);\n        }\n        return findMatchingTokenIndex([anim], value);\n    }\n    function findMatchingTokenIndex(tokens, searchToken) {\n        for (var i = 0; i < tokens.length; i++) {\n            if (tokens[i].indexOf(searchToken) >= 0) {\n                return i;\n            }\n        }\n        return -1;\n    }\n    function addRemoveAnimationEvent(element, fn, doRemove) {\n        doRemove ? element.removeEventListener(ANIMATIONEND_EVENT, fn) :\n            element.addEventListener(ANIMATIONEND_EVENT, fn);\n    }\n    function setAnimationStyle(element, name, value, index) {\n        var prop = ANIMATION_PROP + name;\n        if (index != null) {\n            var oldValue = element.style[prop];\n            if (oldValue.length) {\n                var tokens = oldValue.split(',');\n                tokens[index] = value;\n                value = tokens.join(',');\n            }\n        }\n        element.style[prop] = value;\n    }\n    function getAnimationStyle(element, name) {\n        return element.style[ANIMATION_PROP + name];\n    }\n    function countChars(value, char) {\n        var count = 0;\n        for (var i = 0; i < value.length; i++) {\n            var c = value.charAt(i);\n            if (c === char)\n                count++;\n        }\n        return count;\n    }\n\n    var DEFAULT_FILL_MODE = 'forwards';\n    var DEFAULT_EASING = 'linear';\n    var CssKeyframesPlayer = /** @class */ (function () {\n        function CssKeyframesPlayer(element, keyframes, animationName, _duration, _delay, easing, _finalStyles, _specialStyles) {\n            this.element = element;\n            this.keyframes = keyframes;\n            this.animationName = animationName;\n            this._duration = _duration;\n            this._delay = _delay;\n            this._finalStyles = _finalStyles;\n            this._specialStyles = _specialStyles;\n            this._onDoneFns = [];\n            this._onStartFns = [];\n            this._onDestroyFns = [];\n            this._started = false;\n            this.currentSnapshot = {};\n            this._state = 0;\n            this.easing = easing || DEFAULT_EASING;\n            this.totalTime = _duration + _delay;\n            this._buildStyler();\n        }\n        CssKeyframesPlayer.prototype.onStart = function (fn) { this._onStartFns.push(fn); };\n        CssKeyframesPlayer.prototype.onDone = function (fn) { this._onDoneFns.push(fn); };\n        CssKeyframesPlayer.prototype.onDestroy = function (fn) { this._onDestroyFns.push(fn); };\n        CssKeyframesPlayer.prototype.destroy = function () {\n            this.init();\n            if (this._state >= 4 /* DESTROYED */)\n                return;\n            this._state = 4 /* DESTROYED */;\n            this._styler.destroy();\n            this._flushStartFns();\n            this._flushDoneFns();\n            if (this._specialStyles) {\n                this._specialStyles.destroy();\n            }\n            this._onDestroyFns.forEach(function (fn) { return fn(); });\n            this._onDestroyFns = [];\n        };\n        CssKeyframesPlayer.prototype._flushDoneFns = function () {\n            this._onDoneFns.forEach(function (fn) { return fn(); });\n            this._onDoneFns = [];\n        };\n        CssKeyframesPlayer.prototype._flushStartFns = function () {\n            this._onStartFns.forEach(function (fn) { return fn(); });\n            this._onStartFns = [];\n        };\n        CssKeyframesPlayer.prototype.finish = function () {\n            this.init();\n            if (this._state >= 3 /* FINISHED */)\n                return;\n            this._state = 3 /* FINISHED */;\n            this._styler.finish();\n            this._flushStartFns();\n            if (this._specialStyles) {\n                this._specialStyles.finish();\n            }\n            this._flushDoneFns();\n        };\n        CssKeyframesPlayer.prototype.setPosition = function (value) { this._styler.setPosition(value); };\n        CssKeyframesPlayer.prototype.getPosition = function () { return this._styler.getPosition(); };\n        CssKeyframesPlayer.prototype.hasStarted = function () { return this._state >= 2 /* STARTED */; };\n        CssKeyframesPlayer.prototype.init = function () {\n            if (this._state >= 1 /* INITIALIZED */)\n                return;\n            this._state = 1 /* INITIALIZED */;\n            var elm = this.element;\n            this._styler.apply();\n            if (this._delay) {\n                this._styler.pause();\n            }\n        };\n        CssKeyframesPlayer.prototype.play = function () {\n            this.init();\n            if (!this.hasStarted()) {\n                this._flushStartFns();\n                this._state = 2 /* STARTED */;\n                if (this._specialStyles) {\n                    this._specialStyles.start();\n                }\n            }\n            this._styler.resume();\n        };\n        CssKeyframesPlayer.prototype.pause = function () {\n            this.init();\n            this._styler.pause();\n        };\n        CssKeyframesPlayer.prototype.restart = function () {\n            this.reset();\n            this.play();\n        };\n        CssKeyframesPlayer.prototype.reset = function () {\n            this._styler.destroy();\n            this._buildStyler();\n            this._styler.apply();\n        };\n        CssKeyframesPlayer.prototype._buildStyler = function () {\n            var _this = this;\n            this._styler = new ElementAnimationStyleHandler(this.element, this.animationName, this._duration, this._delay, this.easing, DEFAULT_FILL_MODE, function () { return _this.finish(); });\n        };\n        /** @internal */\n        CssKeyframesPlayer.prototype.triggerCallback = function (phaseName) {\n            var methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n            methods.forEach(function (fn) { return fn(); });\n            methods.length = 0;\n        };\n        CssKeyframesPlayer.prototype.beforeDestroy = function () {\n            var _this = this;\n            this.init();\n            var styles = {};\n            if (this.hasStarted()) {\n                var finished_1 = this._state >= 3 /* FINISHED */;\n                Object.keys(this._finalStyles).forEach(function (prop) {\n                    if (prop != 'offset') {\n                        styles[prop] = finished_1 ? _this._finalStyles[prop] : computeStyle(_this.element, prop);\n                    }\n                });\n            }\n            this.currentSnapshot = styles;\n        };\n        return CssKeyframesPlayer;\n    }());\n\n    var DirectStylePlayer = /** @class */ (function (_super) {\n        __extends(DirectStylePlayer, _super);\n        function DirectStylePlayer(element, styles) {\n            var _this = _super.call(this) || this;\n            _this.element = element;\n            _this._startingStyles = {};\n            _this.__initialized = false;\n            _this._styles = hypenatePropsObject(styles);\n            return _this;\n        }\n        DirectStylePlayer.prototype.init = function () {\n            var _this = this;\n            if (this.__initialized || !this._startingStyles)\n                return;\n            this.__initialized = true;\n            Object.keys(this._styles).forEach(function (prop) {\n                _this._startingStyles[prop] = _this.element.style[prop];\n            });\n            _super.prototype.init.call(this);\n        };\n        DirectStylePlayer.prototype.play = function () {\n            var _this = this;\n            if (!this._startingStyles)\n                return;\n            this.init();\n            Object.keys(this._styles)\n                .forEach(function (prop) { return _this.element.style.setProperty(prop, _this._styles[prop]); });\n            _super.prototype.play.call(this);\n        };\n        DirectStylePlayer.prototype.destroy = function () {\n            var _this = this;\n            if (!this._startingStyles)\n                return;\n            Object.keys(this._startingStyles).forEach(function (prop) {\n                var value = _this._startingStyles[prop];\n                if (value) {\n                    _this.element.style.setProperty(prop, value);\n                }\n                else {\n                    _this.element.style.removeProperty(prop);\n                }\n            });\n            this._startingStyles = null;\n            _super.prototype.destroy.call(this);\n        };\n        return DirectStylePlayer;\n    }(animations.NoopAnimationPlayer));\n\n    var KEYFRAMES_NAME_PREFIX = 'gen_css_kf_';\n    var TAB_SPACE = ' ';\n    var CssKeyframesDriver = /** @class */ (function () {\n        function CssKeyframesDriver() {\n            this._count = 0;\n            this._head = document.querySelector('head');\n            this._warningIssued = false;\n        }\n        CssKeyframesDriver.prototype.validateStyleProperty = function (prop) { return validateStyleProperty(prop); };\n        CssKeyframesDriver.prototype.matchesElement = function (element, selector) {\n            return matchesElement(element, selector);\n        };\n        CssKeyframesDriver.prototype.containsElement = function (elm1, elm2) { return containsElement(elm1, elm2); };\n        CssKeyframesDriver.prototype.query = function (element, selector, multi) {\n            return invokeQuery(element, selector, multi);\n        };\n        CssKeyframesDriver.prototype.computeStyle = function (element, prop, defaultValue) {\n            return window.getComputedStyle(element)[prop];\n        };\n        CssKeyframesDriver.prototype.buildKeyframeElement = function (element, name, keyframes) {\n            keyframes = keyframes.map(function (kf) { return hypenatePropsObject(kf); });\n            var keyframeStr = \"@keyframes \" + name + \" {\\n\";\n            var tab = '';\n            keyframes.forEach(function (kf) {\n                tab = TAB_SPACE;\n                var offset = parseFloat(kf['offset']);\n                keyframeStr += \"\" + tab + offset * 100 + \"% {\\n\";\n                tab += TAB_SPACE;\n                Object.keys(kf).forEach(function (prop) {\n                    var value = kf[prop];\n                    switch (prop) {\n                        case 'offset':\n                            return;\n                        case 'easing':\n                            if (value) {\n                                keyframeStr += tab + \"animation-timing-function: \" + value + \";\\n\";\n                            }\n                            return;\n                        default:\n                            keyframeStr += \"\" + tab + prop + \": \" + value + \";\\n\";\n                            return;\n                    }\n                });\n                keyframeStr += tab + \"}\\n\";\n            });\n            keyframeStr += \"}\\n\";\n            var kfElm = document.createElement('style');\n            kfElm.innerHTML = keyframeStr;\n            return kfElm;\n        };\n        CssKeyframesDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers, scrubberAccessRequested) {\n            if (previousPlayers === void 0) { previousPlayers = []; }\n            if (scrubberAccessRequested) {\n                this._notifyFaultyScrubber();\n            }\n            var previousCssKeyframePlayers = previousPlayers.filter(function (player) { return player instanceof CssKeyframesPlayer; });\n            var previousStyles = {};\n            if (allowPreviousPlayerStylesMerge(duration, delay)) {\n                previousCssKeyframePlayers.forEach(function (player) {\n                    var styles = player.currentSnapshot;\n                    Object.keys(styles).forEach(function (prop) { return previousStyles[prop] = styles[prop]; });\n                });\n            }\n            keyframes = balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles);\n            var finalStyles = flattenKeyframesIntoStyles(keyframes);\n            // if there is no animation then there is no point in applying\n            // styles and waiting for an event to get fired. This causes lag.\n            // It's better to just directly apply the styles to the element\n            // via the direct styling animation player.\n            if (duration == 0) {\n                return new DirectStylePlayer(element, finalStyles);\n            }\n            var animationName = \"\" + KEYFRAMES_NAME_PREFIX + this._count++;\n            var kfElm = this.buildKeyframeElement(element, animationName, keyframes);\n            document.querySelector('head').appendChild(kfElm);\n            var specialStyles = packageNonAnimatableStyles(element, keyframes);\n            var player = new CssKeyframesPlayer(element, keyframes, animationName, duration, delay, easing, finalStyles, specialStyles);\n            player.onDestroy(function () { return removeElement(kfElm); });\n            return player;\n        };\n        CssKeyframesDriver.prototype._notifyFaultyScrubber = function () {\n            if (!this._warningIssued) {\n                console.warn('@angular/animations: please load the web-animations.js polyfill to allow programmatic access...\\n', '  visit http://bit.ly/IWukam to learn more about using the web-animation-js polyfill.');\n                this._warningIssued = true;\n            }\n        };\n        return CssKeyframesDriver;\n    }());\n    function flattenKeyframesIntoStyles(keyframes) {\n        var flatKeyframes = {};\n        if (keyframes) {\n            var kfs = Array.isArray(keyframes) ? keyframes : [keyframes];\n            kfs.forEach(function (kf) {\n                Object.keys(kf).forEach(function (prop) {\n                    if (prop == 'offset' || prop == 'easing')\n                        return;\n                    flatKeyframes[prop] = kf[prop];\n                });\n            });\n        }\n        return flatKeyframes;\n    }\n    function removeElement(node) {\n        node.parentNode.removeChild(node);\n    }\n\n    var WebAnimationsPlayer = /** @class */ (function () {\n        function WebAnimationsPlayer(element, keyframes, options, _specialStyles) {\n            this.element = element;\n            this.keyframes = keyframes;\n            this.options = options;\n            this._specialStyles = _specialStyles;\n            this._onDoneFns = [];\n            this._onStartFns = [];\n            this._onDestroyFns = [];\n            this._initialized = false;\n            this._finished = false;\n            this._started = false;\n            this._destroyed = false;\n            this.time = 0;\n            this.parentPlayer = null;\n            this.currentSnapshot = {};\n            this._duration = options['duration'];\n            this._delay = options['delay'] || 0;\n            this.time = this._duration + this._delay;\n        }\n        WebAnimationsPlayer.prototype._onFinish = function () {\n            if (!this._finished) {\n                this._finished = true;\n                this._onDoneFns.forEach(function (fn) { return fn(); });\n                this._onDoneFns = [];\n            }\n        };\n        WebAnimationsPlayer.prototype.init = function () {\n            this._buildPlayer();\n            this._preparePlayerBeforeStart();\n        };\n        WebAnimationsPlayer.prototype._buildPlayer = function () {\n            var _this = this;\n            if (this._initialized)\n                return;\n            this._initialized = true;\n            var keyframes = this.keyframes;\n            this.domPlayer =\n                this._triggerWebAnimation(this.element, keyframes, this.options);\n            this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n            this.domPlayer.addEventListener('finish', function () { return _this._onFinish(); });\n        };\n        WebAnimationsPlayer.prototype._preparePlayerBeforeStart = function () {\n            // this is required so that the player doesn't start to animate right away\n            if (this._delay) {\n                this._resetDomPlayerState();\n            }\n            else {\n                this.domPlayer.pause();\n            }\n        };\n        /** @internal */\n        WebAnimationsPlayer.prototype._triggerWebAnimation = function (element, keyframes, options) {\n            // jscompiler doesn't seem to know animate is a native property because it's not fully\n            // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n            return element['animate'](keyframes, options);\n        };\n        WebAnimationsPlayer.prototype.onStart = function (fn) { this._onStartFns.push(fn); };\n        WebAnimationsPlayer.prototype.onDone = function (fn) { this._onDoneFns.push(fn); };\n        WebAnimationsPlayer.prototype.onDestroy = function (fn) { this._onDestroyFns.push(fn); };\n        WebAnimationsPlayer.prototype.play = function () {\n            this._buildPlayer();\n            if (!this.hasStarted()) {\n                this._onStartFns.forEach(function (fn) { return fn(); });\n                this._onStartFns = [];\n                this._started = true;\n                if (this._specialStyles) {\n                    this._specialStyles.start();\n                }\n            }\n            this.domPlayer.play();\n        };\n        WebAnimationsPlayer.prototype.pause = function () {\n            this.init();\n            this.domPlayer.pause();\n        };\n        WebAnimationsPlayer.prototype.finish = function () {\n            this.init();\n            if (this._specialStyles) {\n                this._specialStyles.finish();\n            }\n            this._onFinish();\n            this.domPlayer.finish();\n        };\n        WebAnimationsPlayer.prototype.reset = function () {\n            this._resetDomPlayerState();\n            this._destroyed = false;\n            this._finished = false;\n            this._started = false;\n        };\n        WebAnimationsPlayer.prototype._resetDomPlayerState = function () {\n            if (this.domPlayer) {\n                this.domPlayer.cancel();\n            }\n        };\n        WebAnimationsPlayer.prototype.restart = function () {\n            this.reset();\n            this.play();\n        };\n        WebAnimationsPlayer.prototype.hasStarted = function () { return this._started; };\n        WebAnimationsPlayer.prototype.destroy = function () {\n            if (!this._destroyed) {\n                this._destroyed = true;\n                this._resetDomPlayerState();\n                this._onFinish();\n                if (this._specialStyles) {\n                    this._specialStyles.destroy();\n                }\n                this._onDestroyFns.forEach(function (fn) { return fn(); });\n                this._onDestroyFns = [];\n            }\n        };\n        WebAnimationsPlayer.prototype.setPosition = function (p) { this.domPlayer.currentTime = p * this.time; };\n        WebAnimationsPlayer.prototype.getPosition = function () { return this.domPlayer.currentTime / this.time; };\n        Object.defineProperty(WebAnimationsPlayer.prototype, \"totalTime\", {\n            get: function () { return this._delay + this._duration; },\n            enumerable: true,\n            configurable: true\n        });\n        WebAnimationsPlayer.prototype.beforeDestroy = function () {\n            var _this = this;\n            var styles = {};\n            if (this.hasStarted()) {\n                Object.keys(this._finalKeyframe).forEach(function (prop) {\n                    if (prop != 'offset') {\n                        styles[prop] =\n                            _this._finished ? _this._finalKeyframe[prop] : computeStyle(_this.element, prop);\n                    }\n                });\n            }\n            this.currentSnapshot = styles;\n        };\n        /** @internal */\n        WebAnimationsPlayer.prototype.triggerCallback = function (phaseName) {\n            var methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n            methods.forEach(function (fn) { return fn(); });\n            methods.length = 0;\n        };\n        return WebAnimationsPlayer;\n    }());\n\n    var WebAnimationsDriver = /** @class */ (function () {\n        function WebAnimationsDriver() {\n            this._isNativeImpl = /\\{\\s*\\[native\\s+code\\]\\s*\\}/.test(getElementAnimateFn().toString());\n            this._cssKeyframesDriver = new CssKeyframesDriver();\n        }\n        WebAnimationsDriver.prototype.validateStyleProperty = function (prop) { return validateStyleProperty(prop); };\n        WebAnimationsDriver.prototype.matchesElement = function (element, selector) {\n            return matchesElement(element, selector);\n        };\n        WebAnimationsDriver.prototype.containsElement = function (elm1, elm2) { return containsElement(elm1, elm2); };\n        WebAnimationsDriver.prototype.query = function (element, selector, multi) {\n            return invokeQuery(element, selector, multi);\n        };\n        WebAnimationsDriver.prototype.computeStyle = function (element, prop, defaultValue) {\n            return window.getComputedStyle(element)[prop];\n        };\n        WebAnimationsDriver.prototype.overrideWebAnimationsSupport = function (supported) { this._isNativeImpl = supported; };\n        WebAnimationsDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers, scrubberAccessRequested) {\n            if (previousPlayers === void 0) { previousPlayers = []; }\n            var useKeyframes = !scrubberAccessRequested && !this._isNativeImpl;\n            if (useKeyframes) {\n                return this._cssKeyframesDriver.animate(element, keyframes, duration, delay, easing, previousPlayers);\n            }\n            var fill = delay == 0 ? 'both' : 'forwards';\n            var playerOptions = { duration: duration, delay: delay, fill: fill };\n            // we check for this to avoid having a null|undefined value be present\n            // for the easing (which results in an error for certain browsers #9752)\n            if (easing) {\n                playerOptions['easing'] = easing;\n            }\n            var previousStyles = {};\n            var previousWebAnimationPlayers = previousPlayers.filter(function (player) { return player instanceof WebAnimationsPlayer; });\n            if (allowPreviousPlayerStylesMerge(duration, delay)) {\n                previousWebAnimationPlayers.forEach(function (player) {\n                    var styles = player.currentSnapshot;\n                    Object.keys(styles).forEach(function (prop) { return previousStyles[prop] = styles[prop]; });\n                });\n            }\n            keyframes = keyframes.map(function (styles) { return copyStyles(styles, false); });\n            keyframes = balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles);\n            var specialStyles = packageNonAnimatableStyles(element, keyframes);\n            return new WebAnimationsPlayer(element, keyframes, playerOptions, specialStyles);\n        };\n        return WebAnimationsDriver;\n    }());\n    function supportsWebAnimations() {\n        return typeof getElementAnimateFn() === 'function';\n    }\n    function getElementAnimateFn() {\n        return (isBrowser() && Element.prototype['animate']) || {};\n    }\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * @license\n     * Copyright Google Inc. All Rights Reserved.\n     *\n     * Use of this source code is governed by an MIT-style license that can be\n     * found in the LICENSE file at https://angular.io/license\n     */\n\n    /**\n     * Generated bundle index. Do not edit.\n     */\n\n    exports.ɵangular_packages_animations_browser_browser_a = SpecialCasedStyles;\n    exports.AnimationDriver = AnimationDriver;\n    exports.ɵAnimationDriver = AnimationDriver;\n    exports.ɵAnimation = Animation;\n    exports.ɵAnimationStyleNormalizer = AnimationStyleNormalizer;\n    exports.ɵNoopAnimationStyleNormalizer = NoopAnimationStyleNormalizer;\n    exports.ɵWebAnimationsStyleNormalizer = WebAnimationsStyleNormalizer;\n    exports.ɵNoopAnimationDriver = NoopAnimationDriver;\n    exports.ɵAnimationEngine = AnimationEngine;\n    exports.ɵCssKeyframesDriver = CssKeyframesDriver;\n    exports.ɵCssKeyframesPlayer = CssKeyframesPlayer;\n    exports.ɵcontainsElement = containsElement;\n    exports.ɵinvokeQuery = invokeQuery;\n    exports.ɵmatchesElement = matchesElement;\n    exports.ɵvalidateStyleProperty = validateStyleProperty;\n    exports.ɵWebAnimationsDriver = WebAnimationsDriver;\n    exports.ɵsupportsWebAnimations = supportsWebAnimations;\n    exports.ɵWebAnimationsPlayer = WebAnimationsPlayer;\n    exports.ɵallowPreviousPlayerStylesMerge = allowPreviousPlayerStylesMerge;\n\n    Object.defineProperty(exports, '__esModule', { value: true });\n\n}));\n//# sourceMappingURL=animations-browser.umd.js.map\n"]}