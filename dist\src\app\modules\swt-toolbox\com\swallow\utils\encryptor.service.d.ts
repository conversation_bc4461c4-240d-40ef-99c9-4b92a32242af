/**
 * This service is used to encrypt data for
 * security purpose
 * <AUTHOR>
 */
export declare class Encryptor {
    constructor();
    /**
     * Calculates a hash in MD5 and returns result as Hex string
     * Project: http://code.google.com/p/as3crypto/
     *
     * See also flex/Java encryption on: http://groups.adobe.com/index.cfm?event=post.display&postid=29193
     * */
    static hash(value: string): any;
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     * */
    static encrypt(clear: string): any;
    /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     * */
    static encryptPredict(jsessionid: String, userId: String, clear: String): any;
    /**
     * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().encode64(text);
     * */
    static encode64(text: string): string;
    /**
     * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
     * The caller for this callback should be better implemented on main.jsp, and on each other child jsp screen make a call as follow:
     * getMenuWindow().decode64(text);
     **/
    static decode64(text: string): string;
    /**
     * Replace all occurences of a string
     **/
    static replaceAll(source: string, map: Object): string;
}
