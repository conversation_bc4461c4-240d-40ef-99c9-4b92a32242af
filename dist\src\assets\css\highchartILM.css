/* Start -- Highchart  ILM styles */
.chartContainer .circle:before {
    content: ' \25CF';
    font-size: 12px;
}

.chartContainer .circle-dotted:before {
    content: ' \25CC';
    font-size: 12px;
}

.chartContainer .circle-dashed:before {
    content: ' \25CD';
    font-size: 12px;
}

.chartContainer .button-link {
    background: none !important;
    border: none;

    /*optional*/
    font-family: arial, sans-serif; /*input has OS specific font-family*/
    color: #069;
    cursor: pointer;
}

.chartContainer .thetooltip {
    border: 0.5px solid black;
    background-color: #fff;
    opacity: 0.8500000000000001;
    padding: 4px 12px;
    border-radius: 3px;
    position: absolute;
    top: 100px;
    left: 50px;
    box-shadow: 1px 1px 3px #666;
}

.chartContainer  .highcharts-container {
    position: inherit !important;
}

.chartContainer  .highcharts-tooltip > span {
    background-color: white;
    border-radius: 5px !important;
    opacity: 1;
    z-index: 9999 !important;
    box-shadow: 1px 1px 2px black !important;
    /*border: 1px solid black !important;*/

}

.chartContainer  .tooltip {
    padding: 5px !important;
    margin-top: -20px !important;
}

.chartContainer .label {
    z-index: 1 !important;
}

.chartContainer .highcharts-axis-title {
    top: 620px !important;
}

.chartContainer {
    height: 100%;
    width: 100%;
    overflow: hidden;
}
.chartContainer .circle:before {
    content: ' \25CF';
    font-size: 12px;
}

.chartContainer .circle-dotted:before {
    content: ' \25CC';
    font-size: 12px;
}

.chartContainer .circle-dashed:before {
    content: ' \25CD';
    font-size: 12px;
}

.chartContainer .button-link {
    background: none !important;
    border: none;

    /*optional*/
    font-family: arial, sans-serif; /*input has OS specific font-family*/
    color: #069;
    cursor: pointer;
}

.chartContainer .thetooltip {
    border: 0.5px solid black;
    background-color: #fff;
    opacity: 0.8500000000000001;
    padding: 4px 12px;
    border-radius: 3px;
    position: absolute;
    top: 100px;
    left: 50px;
    box-shadow: 1px 1px 3px #666;
}

.chartContainer .highcharts-container {
    position: inherit !important;
}

.chartContainer .highcharts-tooltip > span {
    background-color: white;
    border-radius: 5px !important;
    opacity: 1;
    z-index: 9999 !important;
    box-shadow: 1px 1px 2px black !important;
    /*border: 1px solid black !important;*/

}

.chartContainer .tooltip {
    padding: 5px !important;
    margin-top: -20px !important;
}

.chartContainer .label {
    z-index: 1 !important;
}

.chartContainer .highcharts-axis-title {
    top: 620px !important;
}

.chartContainer {
    height: 100%;
    width: 100%;
    overflow: hidden;
}
/* end -- Highchart  ILM styles */