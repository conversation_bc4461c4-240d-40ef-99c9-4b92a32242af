/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 *
 * <AUTHOR>
 */
//@dynamic
export class DateUtils {
    constructor() { }
    /**
     * Compare two dates and return:
     * <ul>
     * 	<li>0, if date1 > date2</li>
     * 	<li>1, if date1 < date2</li>
     * 	<li>-1, if date1 = date2</li>
     * 	</ul>
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?=} includeHours
     * @return {?}
     */
    static compareDates(date1, date2, includeHours = true) {
        if (includeHours) {
            /** @type {?} */
            var date1Timestamp = date1.getTime();
            /** @type {?} */
            var date2Timestamp = date2.getTime();
            if (date1Timestamp > date2Timestamp)
                return 0;
            else if (date1Timestamp < date2Timestamp)
                return 1;
            else if (date1Timestamp == date2Timestamp)
                return -1;
        }
        else {
            // TODO: compare two dates without hours .. 
        }
        return 0;
    }
    /**
     * Compare two dates with string format following the dateFormat and return:
     * - 0, if date1>date2
     * - 1, if date1=date2
     * - -1, if date1<date2
     *
     * @param {?} date1
     * @param {?} date2
     * @param {?} dateFormat
     * @return {?}
     */
    static compareDatesAsString(date1, date2, dateFormat) {
        // TODO: complete the work for al dateFormat 
        switch (dateFormat) {
            case "MM/DD/YYYY":
            case "DD/MM/YYYY":
            case "DD-MM-YYYY":
            case "MM-DD-YYYY":
            case "YYYY/MM/DD":
            case "YYYY/DD/MM":
        }
        return 0;
    }
    /**
     * Return the date as string with giving the date object and its format
     *
     * @param {?} date
     * @return {?}
     */
    static getDateAsString(date) {
        /** @type {?} */
        var dateAsString = "";
        /** @type {?} */
        var selectedDate = date.selectedDate;
        if (date.formatString == "DD/MM/YYYY")
            dateAsString = selectedDate ? (selectedDate.getDate() < 10 ? "0" + selectedDate.getDate() : selectedDate.getDate()) + "/" + (selectedDate.getMonth() + 1 < 10 ? "0" + (selectedDate.getMonth() + 1) : selectedDate.getMonth() + 1) + "/" + selectedDate.getFullYear() : null;
        else
            dateAsString = selectedDate ? ((selectedDate.getMonth() + 1) < 10 ? "0" + (selectedDate.getMonth() + 1) : (selectedDate.getMonth() + 1)) + "/" + (selectedDate.getDate() < 10 ? "0" + (selectedDate.getDate()) : selectedDate.getDate()) + "/" + selectedDate.getFullYear() : null;
        return dateAsString;
    }
    /**
     * Used to valide time
     *
     * @param {?} timeStr
     * @return {?}
     */
    static validateTime(timeStr) {
        /** @type {?} */
        var exc;
        /** @type {?} */
        var resultStr = "ERROR";
        /** @type {?} */
        var res = false;
        if (exc = this.timepat1.exec(timeStr)) {
            /** @type {?} */
            var time = (exc[1].length == 2 ? "" : "0") + exc[1] + ":" + exc[2];
            resultStr = time;
        }
        else if (exc = this.timepat3.exec(timeStr)) {
            /** @type {?} */
            var time = (exc[1].length == 2 ? "" : "0") + exc[1] + ":00";
            resultStr = time;
        }
        return resultStr;
    }
    /**
     * Converts a string to a Date object
     *
     * @param {?} dateString
     * @param {?} pattern
     * @return {?}
     */
    static dateFromString(dateString, pattern) {
        return this.stringToDate(dateString, pattern);
    }
    /**
     * Converts an Oracle ISO format into a date
     *
     * @param {?} value
     * @return {?}
     */
    static isoToDate(value) {
        /** @type {?} */
        var dateStr = value;
        dateStr = dateStr.replace(/\-/g, "/");
        dateStr = dateStr.replace("T", " ");
        dateStr = dateStr.replace("Z", " GMT-0000");
        return new Date(Date.parse(dateStr));
    }
    /**
     * Converts a date into Oracle ISO format
     *
     * @param {?} value
     * @param {?=} displaySeconds
     * @return {?}
     */
    static dateToIso(value, displaySeconds = true) {
        /** @type {?} */
        var hours = '' + value.getHours();
        if (hours.length < 2)
            hours = "0" + hours;
        /** @type {?} */
        var minutes = '' + value.getMinutes();
        if (minutes.length < 2)
            minutes = "0" + minutes;
        /** @type {?} */
        var seconds = '' + value.getSeconds();
        if (seconds.length < 2)
            seconds = "0" + seconds;
        return this.dateToString(value, 'YYYY-MM-DD') + " " + hours + ":" + minutes + (displaySeconds ? (":" + seconds) : "");
    }
    /**
     *  Parses a String object that contains a date, and returns a Date
     *  object corresponding to the String.
     *  The <code>inputFormat</code> argument contains the pattern
     *  in which the <code>valueString</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     *  <p>The function does not check for the validity of the Date object.
     *  If the value of the date, month, or year is NaN, this method returns null.</p>
     *
     *  <p>For example:
     *  <pre>var dob:Date = DateField.stringToDate("06/30/2005", "MM/DD/YYYY");</pre>
     *  </p>
     *
     * @param {?} valueString Date value to format.
     *
     * @param {?} inputFormat String defining the date format.
     *
     * @return {?} The formatted date as a Date object.
     *
     */
    static stringToDate(valueString, inputFormat) {
        /** @type {?} */
        var mask;
        /** @type {?} */
        var temp;
        /** @type {?} */
        var dateString = "";
        /** @type {?} */
        var monthString = "";
        /** @type {?} */
        var yearString = "";
        /** @type {?} */
        var j = 0;
        /** @type {?} */
        var n = inputFormat.length;
        for (var i = 0; i < n; i++, j++) {
            temp = "" + valueString.charAt(j);
            mask = "" + inputFormat.charAt(i);
            if (mask == "M") {
                if (isNaN(Number(temp)) || temp == " ")
                    j--;
                else
                    monthString += temp;
            }
            else if (mask == "D") {
                if (isNaN(Number(temp)) || temp == " ")
                    j--;
                else
                    dateString += temp;
            }
            else if (mask == "Y") {
                yearString += temp;
            }
            else if (!isNaN(Number(temp)) && temp != " ") {
                return null;
            }
        }
        temp = "" + valueString.charAt(inputFormat.length - i + j);
        if (!(temp == "") && (temp != " "))
            return null;
        /** @type {?} */
        var monthNum = Number(monthString);
        /** @type {?} */
        var dayNum = Number(dateString);
        /** @type {?} */
        var yearNum = Number(yearString);
        if (isNaN(yearNum) || isNaN(monthNum) || isNaN(dayNum))
            return null;
        if (yearString.length == 2 && yearNum < 70)
            yearNum += 2000;
        /** @type {?} */
        var newDate = new Date(yearNum, monthNum - 1, dayNum);
        if (dayNum != newDate.getDate() || (monthNum - 1) != newDate.getMonth())
            return null;
        return newDate;
    }
    /**
     *  Formats a Date into a String according to the <code>outputFormat</code> argument.
     *  The <code>outputFormat</code> argument contains a pattern in which
     *  the <code>value</code> String is formatted.
     *  It can contain <code>"M"</code>,<code>"D"</code>,<code>"Y"</code>,
     *  and delimiter and punctuation characters.
     *
     * \@example <pre>var todaysDate:String = DateField.dateToString(new Date(), "MM/DD/YYYY");</pre>
     * @param {?} value Date value to format.
     *
     * @param {?} outputFormat String defining the date format.
     *
     * @return {?} The formatted date as a String.
     *
     */
    static dateToString(value, outputFormat) {
        if (!value)
            return "";
        /** @type {?} */
        var date = String(value.getDate());
        if (date.length < 2)
            date = "0" + date;
        /** @type {?} */
        var month = String(value.getMonth() + 1);
        if (month.length < 2)
            month = "0" + month;
        /** @type {?} */
        var year = String(value.getFullYear());
        /** @type {?} */
        var output = "";
        /** @type {?} */
        var mask;
        // outputFormat will be null if there are no resources.
        /** @type {?} */
        var n = outputFormat != null ? outputFormat.length : 0;
        for (var i = 0; i < n; i++) {
            mask = outputFormat.charAt(i);
            if (mask == "M") {
                if (outputFormat.charAt(i + 1) == "/" && value.getMonth() < 9) {
                    output += month.substring(1) + "/";
                }
                else {
                    output += month;
                }
                i++;
            }
            else if (mask == "D") {
                if (outputFormat.charAt(i + 1) == "/" && value.getDate() < 10) {
                    output += date.substring(1) + "/";
                }
                else {
                    output += date;
                }
                i++;
            }
            else if (mask == "Y") {
                if (outputFormat.charAt(i + 2) == "Y") {
                    output += year;
                    i += 3;
                }
                else {
                    output += year.substring(2, 4);
                    i++;
                }
            }
            else {
                output += mask;
            }
        }
        return output;
    }
}
DateUtils.timepat1 = /^\s*([01]?\d|2[0-3]):?([0-5]\d)\s*$/;
DateUtils.timepat2 = /^\s*([01]?\d|2[0-3]):?([0-5]\d):?([0-5]\d)\s*$/;
DateUtils.timepat3 = /^\s*([01]?\d|2[0-3])\s*$/;
if (false) {
    /** @type {?} */
    DateUtils.timepat1;
    /** @type {?} */
    DateUtils.timepat2;
    /** @type {?} */
    DateUtils.timepat3;
}
//# sourceMappingURL=data:application/json;base64,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