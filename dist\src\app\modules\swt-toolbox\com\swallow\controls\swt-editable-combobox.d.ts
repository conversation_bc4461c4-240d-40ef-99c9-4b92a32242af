import { OnInit, ElementRef } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { CommonService } from '../utils/common.service';
export declare class SwtEditableComboBox extends Container implements OnInit {
    private elem;
    private commonService;
    editableSelect: ElementRef;
    swtComboBoxcontainer: ElementRef;
    private swtalert;
    private _dataProvider;
    private _selectedIndex;
    private _selectedLabel;
    selectedLabel: string;
    selectedIndex: number;
    dataProvider: any;
    addItem(element: any): void;
    removeItem(value: any): void;
    constructor(elem: ElementRef, commonService: CommonService);
    ngOnInit(): void;
    ngAfterViewInit(): void;
    originalValue: any;
}
