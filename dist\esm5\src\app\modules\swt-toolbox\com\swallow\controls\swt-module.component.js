/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, Input, Output, } from '@angular/core';
import { SwtUtil } from "../utils/swt-util.service";
import { HashMap } from "../utils/HashMap.service";
import { ContextMenu } from "../controls/context-menu.component";
import { CommonService } from "../utils/common.service";
import { Container } from '../containers/swt-container.component';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
var _ = parent;
var SwtModule = /** @class */ (function (_super) {
    tslib_1.__extends(SwtModule, _super);
    function SwtModule(elementRef, comService) {
        var _this = _super.call(this, elementRef, comService) || this;
        _this.elementRef = elementRef;
        _this.comService = comService;
        _this.changes = new HashMap();
        // set creation Complete event
        _this.creationComplete = new EventEmitter();
        // set preinitialize event
        _this.preinitialize = new EventEmitter();
        _this.title = "";
        _this.contextmenuItems = [];
        $($(_this.elementRef.nativeElement)[0]).attr('selector', 'SwtModule');
        return _this;
    }
    Object.defineProperty(SwtModule.prototype, "contextMenu", {
        get: /**
         * @return {?}
         */
        function () {
            return this._contextMenu;
        },
        /*- Parameters to handle context menu of screenVersion screen -START- Added by Rihab JABALLAH on 17/10/2018  */
        set: /*- Parameters to handle context menu of screenVersion screen -START- Added by Rihab JABALLAH on 17/10/2018  */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $('.screenVerion').remove();
            this._contextMenu = value;
            this.contextmenuItems = this._contextMenu.customItems;
            /* creating list of ContextMenuItem dynamically - [START] */
            $(this.elementRef.nativeElement).append("<ul class='screenVerion' ></ul>");
            /** @type {?} */
            var list = $(".screenVerion");
            for (var index = 0; index < this.contextmenuItems.length; index++) {
                list.append('<li data=\'' + this.contextmenuItems[index].label + '\'>' + this.contextmenuItems[index].label + '</li>');
            }
            /** @type {?} */
            var __this = this;
            $(".screenVerion li").click((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                /** @type {?} */
                var contextMenuItemData = event.currentTarget.attributes[0].nodeValue;
                /** @type {?} */
                var item = __this.contextmenuItems.find((/**
                 * @param {?} x
                 * @return {?}
                 */
                function (x) { return x.label === contextMenuItemData; }));
                if (item) {
                    item.MenuItemSelect();
                }
                $(".screenVerion").hide(100);
            }));
            $(document).on('click', (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                $(".screenVerion").hide(100);
            }));
            /* creating list of ContextMenuItem dynamically - [END] */
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtModule.prototype, "result", {
        // get title() {
        //    // return this.titleWindow.title;
        // }
        //
        // set title(value: string) {
        //    // this.titleWindow.title = value;
        // }
        get: 
        // get title() {
        //    // return this.titleWindow.title;
        // }
        //
        // set title(value: string) {
        //    // this.titleWindow.title = value;
        // }
        /**
         * @return {?}
         */
        function () {
            return this.titleWindow.result;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.titleWindow.result = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtModule.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    SwtModule.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        $("body").off("mousemove.module" + this.id);
        $("body").off("mousedown.module" + this.id);
        $("body").off("mouseup.module" + this.id);
    };
    /**
     * @return {?}
     */
    SwtModule.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        try {
            this.preinitialize.emit(this);
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this.creationComplete.emit(_this);
            }), 0);
            $(this.elementRef.nativeElement).attr('selector', 'SwtModule');
            /** @type {?} */
            var dragging_1 = false;
            $("body").on("mousedown.module" + this.id, (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                /** @type {?} */
                var x = e.screenX;
                /** @type {?} */
                var y = e.screenY;
                dragging_1 = false;
                /** @type {?} */
                var parentDisable = $(e.target).parent().hasClass('notDragable');
                $("body").on("mousemove.module" + _this.id, (/**
                 * @param {?} e
                 * @return {?}
                 */
                function (e) {
                    if (Math.abs(x - e.screenX) > 5 || Math.abs(y - e.screenY) > 5) {
                        dragging_1 = true;
                        if (parentDisable) {
                            $(document).trigger("mouseup");
                            event.preventDefault ? event.preventDefault() : (event.returnValue = false);
                            return false;
                        }
                    }
                }));
            }));
            $("body").on("mouseup.module" + this.id, (/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                $("body").off("mousemove.module" + _this.id);
            }));
            $.widget("ui.resizable", $.ui.resizable, {
                resizeTo: (/**
                 * @param {?} newSize
                 * @return {?}
                 */
                function (newSize) {
                    /** @type {?} */
                    var start = new $.Event("mousedown", { pageX: 0, pageY: 0 });
                    this._mouseStart(start);
                    this.axis = 'se';
                    /** @type {?} */
                    var end = new $.Event("mouseup", {
                        pageX: newSize.width - this.originalSize.width || 0,
                        pageY: newSize.height - this.originalSize.height || 0
                    });
                    this._mouseDrag(end);
                    this._mouseStop(end);
                })
            });
        }
        catch (e) {
            console.error("SwtModule [ ngOnInit ] - error :", e);
        }
    };
    /**
     * This method is used to close title window.
     */
    /**
     * This method is used to close title window.
     * @return {?}
     */
    SwtModule.prototype.close = /**
     * This method is used to close title window.
     * @return {?}
     */
    function () {
        this.titleWindow.close();
    };
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param classObject
     */
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    SwtModule.prototype.getQualifiedClassName = /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    function (classObject) {
        return classObject.constructor.name + ".ts";
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getSystemMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getSystemMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getCommonMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getCommonMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getLoginMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getLoginMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getAMLMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getAMLMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getDUPMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getDUPMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getARCMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getARCMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getInputMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getInputMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getCashMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getCashMessages(key, object);
    };
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    SwtModule.prototype.getFatcaMessages = /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    function (key, object) {
        return SwtUtil.getFatcaMessages(key, object);
    };
    /**
     * @return {?}
     */
    SwtModule.prototype.resetSpy = /**
     * @return {?}
     */
    function () {
        this._components.forEach((/**
         * @param {?} element
         * @return {?}
         */
        function (element) {
            element.resetOriginalValue();
        }));
    };
    /**
     * @param {?} components
     * @return {?}
     */
    SwtModule.prototype.subscribeSpy = /**
     * @param {?} components
     * @return {?}
     */
    function (components) {
        var _this = this;
        this._components = components;
        components.forEach((/**
         * @param {?} element
         * @return {?}
         */
        function (element) {
            element.onSpyChange.subscribe((/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                _this.onComponentChanged(e);
            }));
            element.onSpyNoChange.subscribe((/**
             * @param {?} e
             * @return {?}
             */
            function (e) {
                _this.onComponentNotChanged(e);
            }));
        }));
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtModule.prototype.onComponentChanged = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // Vérifier si le hashmap a été vide, puis rempli par le premier élement,
        // Si Oui, alors le SwtModule dispatche (emit) son evement "change"
        // Sinon, ne rien faire
        //if (this.changes.size() == 0){
        // this.onSpyChange.emit(this.changes);
        //}
        this.changes.put(event.target, event.value);
        this.onSpyChange.emit(this.changes);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtModule.prototype.onComponentNotChanged = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // Vérifier si le hashmap est devenu vide
        // Si Oui, alors le SwtModule dispatche son evement "no_change"
        // Sinon, ne rien faire
        if (this.changes.containsKey(event.target)) {
            this.changes.remove(event.target);
        }
        if (this.changes.size() === 0) {
            this.onSpyNoChange.emit(this.changes);
        }
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtModule.prototype.onRightClick = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        event.preventDefault();
        /** @type {?} */
        var __this = this;
        if ($(".openedFontSetting").length === 0) {
            $(".screenVerion").removeClass('hidden');
        }
        /** @type {?} */
        var contextmenu = $(this.elementRef.nativeElement.parentElement).find('.screenVerion');
        contextmenu.finish().toggle(100).css('position', 'fixed').css({
            top: event.pageY + "px",
            left: event.pageX + "px"
        });
    };
    SwtModule.decorators = [
        { type: Component, args: [{
                    selector: 'SwtModule',
                    template: "\n        <div style=\"background-color: #D6E3FE;\"\n             (contextmenu)=\"onRightClick($event)\">\n            <ng-content></ng-content>\n            <ng-container #_container></ng-container>\n        </div>\n    "
                }] }
    ];
    /** @nocollapse */
    SwtModule.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtModule.propDecorators = {
        creationComplete: [{ type: Output, args: ['creationComplete',] }],
        preinitialize: [{ type: Output, args: ['preinitialize',] }],
        contextMenu: [{ type: Input }]
    };
    return SwtModule;
}(Container));
export { SwtModule };
if (false) {
    /** @type {?} */
    SwtModule.prototype.parentDocument;
    /** @type {?} */
    SwtModule.prototype.titleWindow;
    /** @type {?} */
    SwtModule.prototype.changes;
    /** @type {?} */
    SwtModule.prototype.creationComplete;
    /** @type {?} */
    SwtModule.prototype.preinitialize;
    /** @type {?} */
    SwtModule.prototype.title;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype.contextmenuItems;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype._components;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype._contextMenu;
    /** @type {?} */
    SwtModule.prototype.elementRef;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype.comService;
}
//# sourceMappingURL=data:application/json;base64,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