/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
//@dynamic
var XML = /** @class */ (function () {
    function XML(xmlString) {
        this.xmlString = xmlString;
        this.xpath = require('xpath');
        this.dom = require('xmldom').DOMParser;
        this.fromXMLString();
    }
    /**
     * This method is used to convert the entered String (xmlString) to XML .
     * returns XML
     */
    /**
     * This method is used to convert the entered String (xmlString) to XML .
     * returns XML
     * @return {?}
     */
    XML.prototype.fromXMLString = /**
     * This method is used to convert the entered String (xmlString) to XML .
     * returns XML
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var doc = new this.dom().parseFromString(this.xmlString, 'text/xml');
            this.xml = this.xpath.select("/" + doc.childNodes[0].nodeName, doc);
            return this.xml;
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     */
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    XML.prototype.toXMLString = /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    function () {
        try {
            return this.xml.toString();
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     */
    /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    XML.prototype.toString = /**
     * This method is used to convert the existing XML to String .
     * returns string
     * @return {?}
     */
    function () {
        try {
            return this.xml.toString();
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     * appends xml or string to the existing xml.
     * @param value : it can be string or xml(SwtXml)
     */
    /**
     * appends xml or string to the existing xml.
     * @param {?} value : it can be string or xml(SwtXml)
     * @return {?}
     */
    XML.prototype.appendChild = /**
     * appends xml or string to the existing xml.
     * @param {?} value : it can be string or xml(SwtXml)
     * @return {?}
     */
    function (value) {
        try {
            /** @type {?} */
            var child = new this.dom().parseFromString(value.toString(), 'text/xml');
            this.xml[0].appendChild(child);
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     *
     * @param xml
     */
    /**
     *
     * @param {?} xmlToDelete
     * @return {?}
     */
    XML.prototype.removeChild = /**
     *
     * @param {?} xmlToDelete
     * @return {?}
     */
    function (xmlToDelete) {
        for (var index = 0; index < this.children().length; index++) {
            if (this.children()[index].toString() == xmlToDelete.toString()) {
                this.xml[0].removeChild(this.children()[index]);
                return true;
            }
        }
        return false;
    };
    /**
     *
     */
    /**
     *
     * @return {?}
     */
    XML.prototype.parent = /**
     *
     * @return {?}
     */
    function () {
        return (this.xml[0].parentNode);
    };
    /**
     *
     */
    /**
     *
     * @return {?}
     */
    XML.prototype.children = /**
     *
     * @return {?}
     */
    function () {
        return (this.xml[0].childNodes);
    };
    XML.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    XML.ctorParameters = function () { return [
        { type: String }
    ]; };
    return XML;
}());
export { XML };
if (false) {
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xpath;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.dom;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xml;
    /**
     * @type {?}
     * @private
     */
    XML.prototype.xmlString;
}
var XMLListCollection = /** @class */ (function () {
    function XMLListCollection() {
        this.list = new XML("</>");
    }
    /**
     * returns string
     */
    /**
     * returns string
     * @return {?}
     */
    XMLListCollection.prototype.toString = /**
     * returns string
     * @return {?}
     */
    function () {
        try {
            return this.list.toString();
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     * @return {?}
     */
    XMLListCollection.prototype.toXMLString = /**
     * @return {?}
     */
    function () {
        try {
            return this.list.toString();
        }
        catch (error) {
            console.error(error);
        }
    };
    XMLListCollection.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    XMLListCollection.ctorParameters = function () { return []; };
    return XMLListCollection;
}());
export { XMLListCollection };
if (false) {
    /**
     * @type {?}
     * @private
     */
    XMLListCollection.prototype.list;
}
//# sourceMappingURL=data:application/json;base64,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