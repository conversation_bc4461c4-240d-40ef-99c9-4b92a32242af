/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { StringUtils } from '../../utils/string-utils.service';
export class SwtSlider {
    constructor() {
        this.change_ = new EventEmitter();
        this.dateRange = this.createDateRange();
        this.start = 1547512616; // start time in milli Seconds
        // start time in milli Seconds
        this.end = 15475126720; // end time in milli Seconds
        // end time in milli Seconds
        this.options = {
            floor: this.start,
            ceil: this.end,
            step: 1000,
            translate: (/**
             * @param {?} value
             * @param {?} label
             * @return {?}
             */
            (value, label) => {
                return new Date(value).toTimeString(); // this will translate label to time stamp.
            })
        };
        this.miValue = this.start;
        this.maValue = this.end;
        this.value = this.minimum;
        this.firstValue = this.minimum;
        this.lastValue = this.maximum;
        this.numberOfDays = this.maximum - this.minimum;
        this._dataTipFormatString = "DD/MM/YYYY";
    }
    // options: Options = {
    //   stepsArray: this.dateRange.map((date: Date) => {
    //     return { value: date.getTime() };
    //   }),
    //   translate: (value: number, label: LabelType): string => {
    //     return StringUtils.formatDate(new Date(value), this.dataTipFormatString);
    //   },
    //   disabled: false,
    // };
    /**
     * @private
     * @return {?}
     */
    createDateRange() {
        try {
            /** @type {?} */
            const dates = [];
            /** @type {?} */
            const diffTime = Math.abs(this.maximum - this.minimum);
            /** @type {?} */
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24) + 1);
            /** @type {?} */
            let minAsDate = new Date(this.minimum);
            for (let i = minAsDate.getDate(); i <= (minAsDate.getDate() + diffDays - 1); i++) {
                dates.push(new Date(minAsDate.getFullYear(), minAsDate.getMonth(), i));
            }
            return dates;
        }
        catch (error) {
            console.error("SwtSlider [createDateRange Method] ERROR: ", error);
        }
    }
    /**
     * @return {?}
     */
    ngOnInit() {
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === "string") {
            if (value == "true") {
                this.options.disabled = false;
            }
            if (value == "false") {
                this.options.disabled = true;
            }
        }
        else {
            this.options.disabled = !value;
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return !this.options.disabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set minimum(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.firstValue = value;
            if (this.maximum) {
                this.updateData();
            }
        }), 0);
    }
    /**
     * @return {?}
     */
    get minimum() {
        return this.firstValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set maximum(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.lastValue = value;
            if (this.minimum) {
                this.updateData();
            }
        }), 0);
    }
    /**
     * @return {?}
     */
    get maximum() {
        return this.lastValue;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set values(value) {
        setTimeout((/**
         * @return {?}
         */
        () => {
            this.minValue = value[0];
            this.maxValue = value[1];
            this.updateData();
        }), 5);
    }
    /**
     * @return {?}
     */
    get values() {
        return [this.minValue, this.maxValue];
    }
    //Date format string 
    /**
     * @param {?} value
     * @return {?}
     */
    set dataTipFormatString(value) {
        this._dataTipFormatString = value;
    }
    /**
     * @return {?}
     */
    get dataTipFormatString() {
        return this._dataTipFormatString;
    }
    /**
     * @private
     * @return {?}
     */
    updateData() {
        try {
            this.dateRange = this.createDateRange();
            /** @type {?} */
            const newOptions = Object.assign({}, {
                stepsArray: this.dateRange.map((/**
                 * @param {?} date
                 * @return {?}
                 */
                (date) => {
                    return { value: date.getTime() };
                })),
                translate: (/**
                 * @param {?} value
                 * @param {?} label
                 * @return {?}
                 */
                (value, label) => {
                    return StringUtils.formatDate(new Date(value), this.dataTipFormatString);
                }),
                disabled: !this.enabled,
            });
            // newOptions.ceil = newCeil;
            this.options = Object.assign({}, newOptions);
        }
        catch (error) {
            console.error("SwtSlider [updateData METHOD] ERROR: ", error);
        }
    }
    /**
     * @param {?} e
     * @return {?}
     */
    onUserChange(e) {
        this.change_.emit(e);
    }
}
SwtSlider.decorators = [
    { type: Component, args: [{
                selector: 'SwtSlider',
                template: `<div  class="slider-container range">
    <input matInput class="sliderInputs" [(ngModel)]="start" type="number" [disabled]="false">
    <ng5-slider [(value)]="start" [(highValue)]="end"        [options]="options" (userChange)="onUserChange($event)"></ng5-slider>
    <input matInput class="sliderInputs" [(ngModel)]="end"  type="number" [disabled]="false">
     </div>
    `,
                styles: ["", `
  .sliderInputs {
    width : 25px;

  }
  .slider-container{
    width : 275px;
  }
  `]
            }] }
];
/** @nocollapse */
SwtSlider.ctorParameters = () => [];
SwtSlider.propDecorators = {
    change_: [{ type: Output, args: ['change',] }],
    enabled: [{ type: Input }],
    minimum: [{ type: Input }],
    maximum: [{ type: Input }],
    values: [{ type: Input }],
    dataTipFormatString: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.change_;
    /** @type {?} */
    SwtSlider.prototype.minValue;
    /** @type {?} */
    SwtSlider.prototype.maxValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.dateRange;
    /** @type {?} */
    SwtSlider.prototype.start;
    /** @type {?} */
    SwtSlider.prototype.end;
    /** @type {?} */
    SwtSlider.prototype.options;
    /** @type {?} */
    SwtSlider.prototype.miValue;
    /** @type {?} */
    SwtSlider.prototype.maValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.value;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.firstValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.lastValue;
    /**
     * @type {?}
     * @private
     */
    SwtSlider.prototype.numberOfDays;
    /** @type {?} */
    SwtSlider.prototype._dataTipFormatString;
}
//# sourceMappingURL=data:application/json;base64,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