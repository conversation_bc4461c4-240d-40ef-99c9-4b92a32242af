/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
//import { SwtAbstract } from './swt-abstract';
import { focusManager } from "../managers/focus-manager.service";
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
export class SwtTimeInput {
    /**
     * @param {?} elem
     */
    constructor(elem) {
        this.elem = elem;
        this.onSpyChange = new EventEmitter();
        this.onSpyNoChange = new EventEmitter();
        this.id = "";
        //Outputs to handle timeInput events.
        this.KeyDown = new EventEmitter();
        this.keyUp = new EventEmitter();
        this.focus = new EventEmitter();
        this.stepperChange = new EventEmitter();
        this.creationComplete = new EventEmitter();
        this._hours = 0;
        this._minutes = 0;
        this._seconds = 0;
        this._selectedField = 0;
        this._visibility = true;
        this.firstCallHours = true;
        this.firstCallMin = true;
        this.firstCallSec = true;
        //private variable to enable or disable seconds
        this._secondEnable = false;
        this._separator = ":";
        this._editable = true;
        this._enabled = true;
    }
    /**
     * @return {?}
     */
    get secondEnable() {
        return this._secondEnable;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set secondEnable(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._secondEnable = true;
                this.enableSeconds();
            }
            else {
                this._secondEnable = false;
                this.disableSeconds();
            }
        }
        else {
            this._secondEnable = value;
            !value ? this.disableSeconds() : this.enableSeconds();
        }
    }
    /**
     * @return {?}
     */
    get separator() {
        return this._separator;
    }
    /**
     * @param {?} separator
     * @return {?}
     */
    set separator(separator) {
        this._separator = separator;
    }
    /**
     * @return {?}
     */
    get editable() {
        return this._enabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set editable(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._editable = true;
                this.hours.nativeElement.readOnly = false;
                this.minutes.nativeElement.readOnly = false;
                this.seconds.nativeElement.readOnly = false;
            }
            else {
                this._editable = false;
                this.hours.nativeElement.readOnly = true;
                this.minutes.nativeElement.readOnly = true;
                this.seconds.nativeElement.readOnly = true;
            }
        }
        else {
            this._editable = value;
        }
    }
    /**
     * @return {?}
     */
    get enabled() {
        return this._enabled;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set enabled(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._enabled = true;
                this.hours.nativeElement.disabled = false;
                this.minutes.nativeElement.disabled = false;
                this.seconds.nativeElement.disabled = false;
                //add click event listener to arrow Up.
                $(this.uparrow.nativeElement).on("click", (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.onArrowUpClick(event);
                    this.stepperChange.emit(event);
                    this.spyChanges({ hour: Number(this.hour), minute: Number(this.minute), second: Number(this.second) });
                }));
                //add click event listener to arrow Down.
                $(this.downarrow.nativeElement).on("click", (/**
                 * @param {?} event
                 * @return {?}
                 */
                (event) => {
                    this.onArrowDownClick(event);
                    this.stepperChange.emit(event);
                    this.spyChanges({ hour: Number(this.hour), minute: Number(this.minute), second: Number(this.second) });
                }));
            }
            else {
                this._enabled = false;
                this.hours.nativeElement.disabled = true;
                this.minutes.nativeElement.disabled = true;
                this.seconds.nativeElement.disabled = true;
                $(this.timeInput.nativeElement).css("background-color", "#F0EFEF");
                $(this.timeInput.nativeElement.children[0]).css("opacity", "0.5");
            }
        }
        else {
            this._enabled = value;
            this.hours.nativeElement.disabled = !value;
            this.minutes.nativeElement.disabled = !value;
            this.seconds.nativeElement.disabled = !value;
            $(this.timeInput.nativeElement).css("background-color", value ? "#FFF" : "#F0EFEF");
            $(this.timeInput.nativeElement.children[0]).css("opacity", value ? "1" : "0.5");
        }
    }
    /**
     * @return {?}
     */
    get visible() {
        return this._visibility;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set visible(value) {
        if (typeof (value) === "string") {
            if (value === "true") {
                this._visibility = true;
                $(this.timeInput.nativeElement).show();
            }
            else {
                this._visibility = false;
                $(this.timeInput.nativeElement).hide();
            }
        }
        else {
            this._visibility = value;
            value ? $(this.timeInput.nativeElement).show() :
                $(this.timeInput.nativeElement).hide();
        }
    }
    /**
     * @return {?}
     */
    get time() {
        return { "hour": this.loadingZero(this.hour + ""), "minute": this.loadingZero(this.minute + ""), "second": this.loadingZero(this._seconds + "") };
    }
    /**
     * @param {?} time
     * @return {?}
     */
    set time(time) {
        if (time.hour !== undefined && time.hour >= 0 && time.hour <= 23) {
            this.hour = time.hour;
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else {
            console.log("[ time ] method error : time.hour = ", time.hour);
        }
        if (time.minute !== undefined && time.minute >= 0 && time.minute <= 59) {
            this.minute = time.minute;
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else {
            console.log("[ time ] method error : time.minute = ", time.minute);
        }
        if (time.second !== undefined && time.second >= 0 && time.second <= 59) {
            this.second = time.second;
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
        else {
            console.log("[ time ] method error : time.second = ", time.second);
        }
    }
    /**
     * @return {?}
     */
    get hour() {
        return this._hours;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set hour(value) {
        this._hours = value;
        if (this.firstCallHours) {
            this.originalValue["hours"] = Number(value);
            this.firstCallHours = false;
        }
        this.spyChanges(this.time);
    }
    /**
     * @return {?}
     */
    get minute() {
        return this._minutes;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set minute(value) {
        this._minutes = value;
        if (this.firstCallMin) {
            this.originalValue["minutes"] = Number(value);
            this.firstCallMin = false;
        }
        this.spyChanges(this.time);
    }
    /**
     * @return {?}
     */
    get second() {
        return this._seconds;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set second(value) {
        this._seconds = value;
        if (this.firstCallSec) {
            this.originalValue["seconds"] = Number(value);
            this.firstCallSec = false;
        }
        this.spyChanges(this.time);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.originalValue = {};
        //**************************set timeInput separator.**************************
        $(this.separatorspan1.nativeElement).text(this._separator);
        $(this.separatorspan2.nativeElement).text(this._separator);
        //**************************hours input field events.**************************
        //add Key Up event listener to hours input.
        $(this.hours.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onHoursKeyDown(event);
            this.KeyDown.emit(event);
        }));
        //add Key Up event listener to hours input.
        $(this.hours.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onHoursKeyUp(event);
            this.keyUp.emit(event);
            this.focus.emit(event);
        }));
        //add focus event listener to hours input.
        $(this.hours.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this._selectedField = 0;
            focusManager.focusTarget = this.id;
        }));
        //add focus out event listener to hours input.
        $(this.hours.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            $(this.hours.nativeElement).val(this.loadingZero(this.hour + ""));
        }));
        // **************************minutes input field events.**************************
        //add Key Up event listener to minutes input.
        $(this.minutes.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onMinutesKeyDown(event);
            this.KeyDown.emit(event);
        }));
        //add Key Up event listener to minutes input.
        $(this.minutes.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onMinutesKeyUp(event);
            this.keyUp.emit(event);
        }));
        //add focus event listener to minutes input.
        $(this.minutes.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this._selectedField = 1;
            this.focus.emit(event);
        }));
        //add focus out event listener to minutes input.
        $(this.minutes.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            $(this.minutes.nativeElement).val(this.loadingZero(this.minute + ""));
        }));
        // **************************seconds input field events.**************************
        //add Key Up event listener to seconds input.
        $(this.seconds.nativeElement).on("keydown", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onSecondsKeyDown(event);
            this.KeyDown.emit(event);
        }));
        //add Key Up event listener to seconds input.
        $(this.seconds.nativeElement).on("keyup", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this.onSecondsKeyUp(event);
            this.keyUp.emit(event);
        }));
        //add focus event listener to seconds input.
        $(this.seconds.nativeElement).on("focus", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            this._selectedField = 2;
            this.focus.emit(event);
        }));
        //add focus out event listener to seconds input.
        $(this.seconds.nativeElement).on("focusout", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            $(this.seconds.nativeElement).val(this.loadingZero(this.second + ""));
        }));
        // **************************seconds Up arrow events.**************************
        //add click event listener to arrow Up.
        $(this.uparrow.nativeElement).on("click", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (this.enabled) {
                this.onArrowUpClick(event);
                this.stepperChange.emit(event);
                this.spyChanges({ hour: Number(this.hour), minute: Number(this.minute), second: Number(this.second) });
            }
        }));
        // **************************seconds Down arrow events.**************************
        //add click event listener to arrow Down.
        $(this.downarrow.nativeElement).on("click", (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (this.enabled) {
                this.onArrowDownClick(event);
                this.stepperChange.emit(event);
                this.spyChanges({ hour: Number(this.hour), minute: Number(this.minute), second: Number(this.second) });
            }
        }));
        // Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtTimeInput.
        $($(this.elem.nativeElement)[0]).attr('selector', 'SwtTimeInput');
        // set id to button DOM.
        if (this.id) {
            $($(this.elem.nativeElement)[0]).attr("id", this.id);
        }
        //-END-
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        this.creationComplete.emit(new Function());
    }
    /**
     * @param {?} event
     * @return {?}
     */
    spyChanges(event) {
        if (this.originalValue.hours == event.hour && this.originalValue.minutes == event.minute && this.originalValue.seconds == event.second) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else if (!this.firstCallHours && !this.firstCallMin && !this.firstCallSec) {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    }
    /**
     * @return {?}
     */
    resetOriginalValue() {
        this.originalValue.hours = this.time.hour;
        this.originalValue.minutes = this.time.minute;
        this.originalValue.seconds = this.time.second;
        this.spyChanges(this.time);
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    changeStyle(value) {
        if (value) {
        }
        else {
        }
    }
    /**
     * This method is used to disable timeInput seconds.
     * @private
     * @return {?}
     */
    disableSeconds() {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            $(this.seconds.nativeElement).hide();
            errorLocation = 20;
            $(this.separatorspan2.nativeElement).hide();
        }
        catch (error) {
            console.error("[ disableSeconds ] method error :", error, "errorLocation :", errorLocation);
        }
    }
    /**
     * This method is used to enable timeInput seconds.
     * @private
     * @return {?}
     */
    enableSeconds() {
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            $(this.seconds.nativeElement).show();
            errorLocation = 20;
            $(this.separatorspan2.nativeElement).show();
        }
        catch (error) {
            console.error("[ enableSeconds ] method error :", error, "errorLocation :", errorLocation);
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onHoursKeyDown(event) {
        if (event.keyCode == 38) {
            this.hour = this.increment(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (event.keyCode == 40) {
            this.hour = this.decrement(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onHoursKeyUp(event) {
        this.hour = event.target.value;
        if (this.hour > 23) {
            $(this.hours.nativeElement).val("23");
            this.hour = 23;
        }
        else if (this.hour < 0) {
            $(this.hours.nativeElement).val("00");
            this.hour = 0;
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onMinutesKeyDown(event) {
        if (event.keyCode == 38) {
            this.minute = this.increment(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (event.keyCode == 40) {
            this.minute = this.decrement(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onMinutesKeyUp(event) {
        this.minute = event.target.value;
        if (this.minute > 59) {
            $(this.minutes.nativeElement).val("59");
            this.minute = 59;
        }
        else if (this.minute < 0) {
            $(this.minutes.nativeElement).val("00");
            this.minute = 0;
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onSecondsKeyDown(event) {
        if (event.keyCode == 38) {
            this.second = this.increment(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
        else if (event.keyCode == 40) {
            this.second = this.decrement(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onSecondsKeyUp(event) {
        this.second = event.target.value;
        if (this.second > 59) {
            $(this.seconds.nativeElement).val("59");
            this.second = 59;
        }
        else if (this.second < 0) {
            $(this.seconds.nativeElement).val("00");
            this.second = 0;
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onArrowUpClick(event) {
        if (this._selectedField === 0) {
            this.hour = this.increment(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (this._selectedField === 1) {
            this.minute = this.increment(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (this._selectedField === 2) {
            this.second = this.increment(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    }
    /**
     * @private
     * @param {?} event
     * @return {?}
     */
    onArrowDownClick(event) {
        if (this._selectedField === 0) {
            this.hour = this.decrement(this.hour, 0, 23);
            $(this.hours.nativeElement).val(this.loadingZero(String(this.hour)));
        }
        else if (this._selectedField === 1) {
            this.minute = this.decrement(this.minute, 0, 59);
            $(this.minutes.nativeElement).val(this.loadingZero(String(this.minute)));
        }
        else if (this._selectedField === 2) {
            this.second = this.decrement(this.second, 0, 59);
            $(this.seconds.nativeElement).val(this.loadingZero(String(this.second)));
        }
    }
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    loadingZero(value) {
        /** @type {?} */
        var str = "";
        value.length === 1 ? str = "0" + value : str = value;
        return str;
    }
    /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    increment(value, min, max) {
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (value < max) {
                value++;
            }
            else if (value >= max) {
                value = min;
            }
            return value;
        }
        catch (error) {
            console.error("[ increment ] method error :", error, "errorLocation :", errorLocation);
        }
    }
    /**
     * @private
     * @param {?} value
     * @param {?} min
     * @param {?} max
     * @return {?}
     */
    decrement(value, min, max) {
        /** @type {?} */
        const errorLocation = 0;
        try {
            if (value > min) {
                value--;
            }
            else if (value <= min) {
                value = max;
            }
            return value;
        }
        catch (error) {
            console.error("[ decrement ] method error :", error, "errorLocation :", errorLocation);
        }
    }
}
SwtTimeInput.decorators = [
    { type: Component, args: [{
                selector: 'SwtTimeInput',
                template: `
    <span #timeInput class="timeInput-container"  
              [style.margin-left.px]="this.marginLeft"
              [style.margin-right.px]="this.marginRight"
              [style.margin-top.px]="this.marginTop"
              [style.margin-bottom.px]="this.marginBottom">
    <tr>
       <td>
        <div class="timeInput-time">  
          <input #hours type="text" Swtregex="[0-9]{2}" value="00" class="timeInput-hours">
          <span #separator1 class="seconds-after"></span>
          <input #minutes type="text" Swtregex="[0-9]{2}" value="00" class="timeInput-minutes">
          <span #separator2 class="seconds-before" style="display: none"></span>
          <input #seconds type="text" Swtregex="[0-9]{2}" value="00" class="timeInput-seconds" style="display: none">
        </div>
       </td>
       <td>
        <div class="timeInput-spiners">
             <tr>
                 <td>
                    <button #uparrow class="arrow-up">&#9652;</button>
                 </td>
             </tr>
             <tr>
                 <td>   
                    <button #downarrow class="arrow-down">&#9662;</button>
                 </td>   
             </tr>
          </div>
         </td>
    </tr>   
    </span>
    `,
                styles: [`

        td {
            line-height: 8px;
        }

        .timeInput-container {
            height: 21px;
            width: auto;
            display: inline-block;
            border: none;
            margin: 0px 5px 5px 0px;;
            background-color: #FFF;
        }

        .timeInput-container:focus {
            outline: none !important;
        }

        .timeInput-time {
            border: 1px solid #8D8F91;
            padding-left: 5px;
            padding-right: 5px;
            height: 22px;
        }

        .timeInput-hours, .timeInput-minutes, .timeInput-seconds {
            width: 15px;
            height: 19px;
            text-align: center;
            border: none;
            color: #173553;
        }

        .timeInput-hours:focus, .timeInput-minutes:focus, .timeInput-seconds:focus {
            outline: none;
        }

        .timeInput-spiners {
            height: 19px;
        }

        .seconds-after, .seconds-after {
            font-family: verdana, helvetica;
            margin: -1px;
        }

        .arrow-down, .arrow-up {
            width: 18px;
            padding: 0px;
            height: 11px;
            line-height: 4px;
            font-size: 12px;
            font-weight: bolder;
            cursor: default;
        }

        .arrow-down:focus, .arrow-up:focus {
            outline: none;
        }

        .arrow-down:hover, .arrow-up:hover {
            border: 1px solid #0086E8;
            background-image: -webkit-linear-gradient(top, #FFFFFF, #F1F1F1);
            background-image: -moz-linear-gradient(top, #FFFFFF, #F1F1F1);
            background-image: -ms-linear-gradient(top, #FFFFFF, #F1F1F1);
            background-image: -o-linear-gradient(top, #FFFFFF, #F1F1F1);
            background-image: linear-gradient(to bottom, #FFFFFF, #F1F1F1);
        }

        .arrow-down {
            border-top: 0.5px solid #8D8F91;
            border-bottom: 1px solid #8D8F91;
            border-left: 1px solid #8D8F91;
            border-right: 1px solid #8D8F91;
            border-bottom-right-radius: 4px;
            background-image: -webkit-linear-gradient(top, #D8E8F2, #CDE0EB);
            background-image: -moz-linear-gradient(top, #D8E8F2, #CDE0EB);
            background-image: -ms-linear-gradient(top, #D8E8F2, #CDE0EB);
            background-image: -o-linear-gradient(top, #D8E8F2, #CDE0EB);
            background-image: linear-gradient(to bottom, #D8E8F2, #CDE0EB);
        }

        .arrow-up {
            border-top: 1px solid #8D8F91;
            border-bottom: 1px solid transparent;
            border-left: 1px solid #8D8F91;
            border-right: 1px solid #8D8F91;
            border-top-right-radius: 4px;
            margin-top: -1px;
            background-image: -webkit-linear-gradient(top, #F1F9FF, #DDECF5);
            background-image: -moz-linear-gradient(top, #F1F9FF, #DDECF5);
            background-image: -ms-linear-gradient(top, #F1F9FF, #DDECF5);
            background-image: -o-linear-gradient(top, #F1F9FF, #DDECF5);
            background-image: linear-gradient(to bottom, #F1F9FF, #DDECF5);
        }
    `]
            }] }
];
/** @nocollapse */
SwtTimeInput.ctorParameters = () => [
    { type: ElementRef }
];
SwtTimeInput.propDecorators = {
    onSpyChange: [{ type: Output, args: ['onSpyChange',] }],
    onSpyNoChange: [{ type: Output, args: ['onSpyNoChange',] }],
    id: [{ type: Input, args: ["id",] }],
    toolTip: [{ type: Input, args: ["toolTip",] }],
    KeyDown: [{ type: Output, args: ["KeyDown",] }],
    keyUp: [{ type: Output, args: ["keyUp",] }],
    focus: [{ type: Output, args: ["focus",] }],
    stepperChange: [{ type: Output, args: ["stepperChange",] }],
    creationComplete: [{ type: Output, args: ["creationComplete",] }],
    marginTop: [{ type: Input, args: ["marginTop",] }],
    marginRight: [{ type: Input, args: ["marginRight",] }],
    marginBottom: [{ type: Input, args: ["marginBottom",] }],
    marginLeft: [{ type: Input, args: ["marginLeft",] }],
    hours: [{ type: ViewChild, args: ["hours",] }],
    minutes: [{ type: ViewChild, args: ["minutes",] }],
    seconds: [{ type: ViewChild, args: ["seconds",] }],
    uparrow: [{ type: ViewChild, args: ["uparrow",] }],
    downarrow: [{ type: ViewChild, args: ["downarrow",] }],
    separatorspan1: [{ type: ViewChild, args: ["separator1",] }],
    separatorspan2: [{ type: ViewChild, args: ["separator2",] }],
    timeInput: [{ type: ViewChild, args: ["timeInput",] }],
    secondEnable: [{ type: Input }],
    editable: [{ type: Input }],
    enabled: [{ type: Input }],
    visible: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtTimeInput.prototype.originalValue;
    /** @type {?} */
    SwtTimeInput.prototype.onSpyChange;
    /** @type {?} */
    SwtTimeInput.prototype.onSpyNoChange;
    /** @type {?} */
    SwtTimeInput.prototype.id;
    /** @type {?} */
    SwtTimeInput.prototype.toolTip;
    /** @type {?} */
    SwtTimeInput.prototype.KeyDown;
    /** @type {?} */
    SwtTimeInput.prototype.keyUp;
    /** @type {?} */
    SwtTimeInput.prototype.focus;
    /** @type {?} */
    SwtTimeInput.prototype.stepperChange;
    /** @type {?} */
    SwtTimeInput.prototype.creationComplete;
    /** @type {?} */
    SwtTimeInput.prototype.marginTop;
    /** @type {?} */
    SwtTimeInput.prototype.marginRight;
    /** @type {?} */
    SwtTimeInput.prototype.marginBottom;
    /** @type {?} */
    SwtTimeInput.prototype.marginLeft;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._hours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._minutes;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._seconds;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._selectedField;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._visibility;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.hours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.minutes;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.seconds;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.uparrow;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.downarrow;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.separatorspan1;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.separatorspan2;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.timeInput;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallHours;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallMin;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.firstCallSec;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._secondEnable;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._separator;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtTimeInput.prototype.elem;
}
//# sourceMappingURL=data:application/json;base64,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