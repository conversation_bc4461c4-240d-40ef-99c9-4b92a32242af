import { <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ElementRef } from "@angular/core";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { HttpClient } from "@angular/common/http";
export declare class SwtRadioButtonGroup extends Container implements OnInit, OnDestroy {
    private httpClient;
    private elem;
    private commonService;
    private logger;
    private originalRadioItem;
    radioItemsArray: any[];
    private _align;
    private _tabIndex;
    private _selectedValue;
    private _selectedRadioId;
    toolTip: any;
    radioItems: any;
    align: string;
    selectedValue: any;
    selectedRadioId: any;
    /**
     * Constructor
     * @param httpClient
     * @param elem
     * @param commonService
     * @param _renderer
     */
    constructor(httpClient: HttpClient, elem: ElementRef, commonService: CommonService);
    /**
     * ngOnInit
     */
    ngOnInit(): void;
    /**
     * Change
     * @param event
     */
    Change(event: any): void;
    /**
     * spyChanges
     * @param event
     * @Added by <PERSON><PERSON><PERSON>.B
     * @Reviewed By Rihab.<PERSON><PERSON>llah @23/04/2019.
     */
    spyChanges(event: any): void;
    spyNoChanges(event: any): void;
    /**
     * resetOriginalValue
     */
    resetOriginalValue(): void;
    /**
     * Destroy all event listeners
     */
    ngOnDestroy(): void;
}
