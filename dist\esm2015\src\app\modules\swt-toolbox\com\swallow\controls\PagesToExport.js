/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtButton } from "./swt-button.component";
import { SwtRadioItem } from './swt-radioItem.component';
import { SwtRadioButtonGroup } from './swt-radioButtonGroup.component';
export class SwtPagesToExport extends SwtModule {
    /**
     * @param {?} element
     * @param {?} common
     */
    constructor(element, common) {
        super(element, common);
        this.element = element;
        this.common = common;
        this._exportCancelFunction = new Function();
        //Export function (callback function)
        this._exportFunction = null;
        this.pagesToExport = "";
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.radioC.label = SwtUtil.getPredictMessage('exportPages.label.current', null);
        this.radioA.label = SwtUtil.getPredictMessage('exportPages.label.allPages', null);
        this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
        this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    }
    /**
     * @return {?}
     */
    initData() {
    }
    /**
     * @return {?}
     */
    popupClosed() {
        this.close();
        this._exportCancelFunction();
    }
    /**
     * @return {?}
     */
    defaultContentFunction() {
        console.log("***defaultContentFunction");
    }
    /**
     * @return {?}
     */
    exportType() {
        if (this.radioC.selected) {
            this.result = "current";
            this.pagesToExport = "current";
        }
        else {
            this.result = "all";
            this.pagesToExport = "all";
        }
        this.close();
        this._exportFunction();
    }
    /**
     * @return {?}
     */
    get onexportFunction() {
        return this._exportFunction;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set onexportFunction(value) {
        this._exportFunction = value;
    }
    /**
     * @return {?}
     */
    get exportCancelFunction() {
        return this._exportCancelFunction;
    }
    /**
     *
     * @param {?} value
     * @return {?}
     */
    set exportCancelFunction(value) {
        this._exportCancelFunction = value;
    }
    /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    show(parent) {
        this.win = SwtPopUpManager.createPopUp(this, SwtPagesToExport, {});
        this.win.enableResize = false;
        this.win.title = "Pages To Export";
        this.win.height = '160';
        this.win.width = '220';
        this.win.showControls = false;
        this.win.isModal = true;
        this.win.onexportFunction = this._exportFunction.bind(this);
        this.win.onClose.subscribe((/**
         * @param {?} res
         * @return {?}
         */
        (res) => {
            if (this.win.getChild().result) {
                if (this.win.getChild().result == "current") {
                    this.pagesToExport = "current";
                }
                else {
                    this.pagesToExport = "all";
                }
            }
        }));
        this.win.display();
    }
    /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    hide(parent) {
        SwtPopUpManager.hide();
    }
    /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    ExportCanceled(event) {
        // ExternalInterface.call('finishExport');
    }
}
SwtPagesToExport.decorators = [
    { type: Component, args: [{
                selector: 'SwtPagesToExport',
                template: `
        <SwtModule (close)='popupClosed()' (creationComplete)='initData()' width="100%" height="100%" >
        <VBox  width='100%' height='100%' paddingTop="10" paddingRight="10" paddingLeft="10" paddingBottom="10">
            <SwtCanvas width="100%" height="50%">
            <SwtRadioButtonGroup #exportButtonGroup   id="exportButtonGroup"
                                align="vertical">
                <SwtRadioItem #radioC id="radioC" value="current"  width="120" groupName="exportButtonGroup" selected="true"  ></SwtRadioItem>
                <SwtRadioItem #radioA id="radioA" value="all"   width="120" groupName="exportButtonGroup"  ></SwtRadioItem>
            </SwtRadioButtonGroup>
            </SwtCanvas>
            <SwtCanvas width="100%">
            <HBox>

            <SwtButton buttonMode="true"
                        id="okButton"
                        #okButton
                        width="70"
                        enabled="true"
                        (click)="exportType()"> </SwtButton>
            <SwtButton buttonMode="true"
                        id="cancelButton"
                        #cancelButton
                        width="70"
                        enabled="true"
                        (click)="popupClosed()"></SwtButton>
            </HBox>
            </SwtCanvas>
        </VBox>
        </SwtModule>
  `,
                styles: [`
  `]
            }] }
];
/** @nocollapse */
SwtPagesToExport.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtPagesToExport.propDecorators = {
    okButton: [{ type: ViewChild, args: ['okButton',] }],
    cancelButton: [{ type: ViewChild, args: ['cancelButton',] }],
    exportButtonGroup: [{ type: ViewChild, args: ['exportButtonGroup',] }],
    radioC: [{ type: ViewChild, args: ['radioC',] }],
    radioA: [{ type: ViewChild, args: ['radioA',] }]
};
if (false) {
    /**
     * Buttons*******
     * @type {?}
     */
    SwtPagesToExport.prototype.okButton;
    /** @type {?} */
    SwtPagesToExport.prototype.cancelButton;
    /** @type {?} */
    SwtPagesToExport.prototype.exportButtonGroup;
    /** @type {?} */
    SwtPagesToExport.prototype.radioC;
    /** @type {?} */
    SwtPagesToExport.prototype.radioA;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype._exportCancelFunction;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype._exportFunction;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.win;
    /** @type {?} */
    SwtPagesToExport.prototype.pagesToExport;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.element;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUGFnZXNUb0V4cG9ydC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy9QYWdlc1RvRXhwb3J0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUUsU0FBUyxFQUFVLFVBQVUsRUFBRSxTQUFTLEVBQVMsTUFBTSxlQUFlLENBQUM7QUFDaEYsT0FBTyxFQUFFLFNBQVMsRUFBRSxNQUFNLHdCQUF3QixDQUFDO0FBQ25ELE9BQU8sRUFBRSxlQUFlLEVBQUUsTUFBTSx3Q0FBd0MsQ0FBQztBQUN6RSxPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0seUJBQXlCLENBQUM7QUFJeEQsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLDJCQUEyQixDQUFDO0FBRXBELE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx3QkFBd0IsQ0FBQztBQUduRCxPQUFPLEVBQUUsWUFBWSxFQUFFLE1BQU0sMkJBQTJCLENBQUM7QUFDekQsT0FBTyxFQUFFLG1CQUFtQixFQUFFLE1BQU0sa0NBQWtDLENBQUM7QUFxQ3ZFLE1BQU0sT0FBTyxnQkFBaUIsU0FBUSxTQUFTOzs7OztJQW1CN0MsWUFBb0IsT0FBbUIsRUFBVSxNQUFxQjtRQUNsRSxLQUFLLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRFAsWUFBTyxHQUFQLE9BQU8sQ0FBWTtRQUFVLFdBQU0sR0FBTixNQUFNLENBQWU7UUFQOUQsMEJBQXFCLEdBQWEsSUFBSSxRQUFRLEVBQUUsQ0FBQzs7UUFFaEQsb0JBQWUsR0FBYSxJQUFJLENBQUM7UUFJbkMsa0JBQWEsR0FBUyxFQUFFLENBQUM7SUFHaEMsQ0FBQzs7OztJQUVELFFBQVE7UUFDTixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssR0FBRyxPQUFPLENBQUMsaUJBQWlCLENBQUMsMkJBQTJCLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDakYsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLDRCQUE0QixFQUFFLElBQUksQ0FBQyxDQUFDO1FBQ2xGLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxHQUFHLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDbkUsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUM3RSxDQUFDOzs7O0lBRUQsUUFBUTtJQUNSLENBQUM7Ozs7SUFFTSxXQUFXO1FBQ2hCLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUNiLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO0lBRS9CLENBQUM7Ozs7SUFFRCxzQkFBc0I7UUFDbEIsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkIsQ0FBQyxDQUFDO0lBQzdDLENBQUM7Ozs7SUFHTSxVQUFVO1FBQ2YsSUFBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRTtZQUNyQixJQUFJLENBQUMsTUFBTSxHQUFHLFNBQVMsQ0FBQztZQUN4QixJQUFJLENBQUMsYUFBYSxHQUFJLFNBQVMsQ0FBQztTQUNqQzthQUFNO1lBQ0wsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7WUFDcEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUM7U0FDN0I7UUFDRCxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDYixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7SUFDeEIsQ0FBQzs7OztJQUVELElBQVcsZ0JBQWdCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQztJQUNoQyxDQUFDOzs7Ozs7SUFLRCxJQUFXLGdCQUFnQixDQUFFLEtBQWU7UUFDeEMsSUFBSSxDQUFDLGVBQWUsR0FBRyxLQUFLLENBQUM7SUFDakMsQ0FBQzs7OztJQUdELElBQVcsb0JBQW9CO1FBQzNCLE9BQU8sSUFBSSxDQUFDLHFCQUFxQixDQUFDO0lBQ3RDLENBQUM7Ozs7OztJQUtELElBQVcsb0JBQW9CLENBQUUsS0FBZTtRQUM1QyxJQUFJLENBQUMscUJBQXFCLEdBQUcsS0FBSyxDQUFDO0lBQ3ZDLENBQUM7Ozs7OztJQUlJLElBQUksQ0FBQyxNQUFNO1FBQ2QsSUFBSSxDQUFDLEdBQUcsR0FBSSxlQUFlLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxnQkFBZ0IsRUFBRSxFQUMvRCxDQUFDLENBQUM7UUFFTCxJQUFJLENBQUMsR0FBRyxDQUFDLFlBQVksR0FBRyxLQUFLLENBQUM7UUFDNUIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLEdBQUcsaUJBQWlCLENBQUM7UUFDckMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1FBQ3hCLElBQUksQ0FBQyxHQUFHLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQztRQUN2QixJQUFJLENBQUMsR0FBRyxDQUFDLFlBQVksR0FBRyxLQUFLLENBQUM7UUFDOUIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO1FBQ3hCLElBQUksQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDNUQsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsU0FBUzs7OztRQUFDLENBQUMsR0FBRyxFQUFFLEVBQUU7WUFFbkMsSUFBRyxJQUFJLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDLE1BQU0sRUFBRTtnQkFDM0IsSUFBSSxJQUFJLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDLE1BQU0sSUFBSSxTQUFTLEVBQUU7b0JBQ3pDLElBQUksQ0FBQyxhQUFhLEdBQUcsU0FBUyxDQUFDO2lCQUNsQztxQkFBTztvQkFDSixJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQztpQkFFOUI7YUFFQTtRQUVMLENBQUMsRUFBQyxDQUFDO1FBQ0gsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQztJQUVyQixDQUFDOzs7Ozs7SUFJTSxJQUFJLENBQUMsTUFBVztRQUNsQixlQUFlLENBQUMsSUFBSSxFQUFFLENBQUE7SUFDM0IsQ0FBQzs7Ozs7O0lBS00sY0FBYyxDQUFDLEtBQUs7UUFDdkIsMENBQTBDO0lBQzlDLENBQUM7OztZQTNKRixTQUFTLFNBQUM7Z0JBQ1QsUUFBUSxFQUFFLGtCQUFrQjtnQkFDNUIsUUFBUSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQTZCVDt5QkFDUTtHQUNSO2FBQ0Y7Ozs7WUFqRDJCLFVBQVU7WUFHN0IsYUFBYTs7O3VCQW1EbkIsU0FBUyxTQUFDLFVBQVU7MkJBQ3BCLFNBQVMsU0FBQyxjQUFjO2dDQUN4QixTQUFTLFNBQUMsbUJBQW1CO3FCQUM3QixTQUFTLFNBQUMsUUFBUTtxQkFDbEIsU0FBUyxTQUFDLFFBQVE7Ozs7Ozs7SUFKbkIsb0NBQTJDOztJQUMzQyx3Q0FBbUQ7O0lBQ25ELDZDQUF1RTs7SUFDdkUsa0NBQTBDOztJQUMxQyxrQ0FBMEM7Ozs7O0lBSTFDLGlEQUF5RDs7Ozs7SUFFeEQsMkNBQXlDOzs7OztJQUUxQywrQkFBa0I7O0lBRWxCLHlDQUFnQzs7Ozs7SUFDcEIsbUNBQTJCOzs7OztJQUFFLGtDQUE2QiIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudCwgT25Jbml0LCBFbGVtZW50UmVmLCBWaWV3Q2hpbGQsIElucHV0IH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XHJcbmltcG9ydCB7IFN3dE1vZHVsZSB9IGZyb20gXCIuL3N3dC1tb2R1bGUuY29tcG9uZW50XCI7XHJcbmltcG9ydCB7IFN3dFBvcFVwTWFuYWdlciB9IGZyb20gXCIuLi9tYW5hZ2Vycy9zd3QtcG9wLXVwLW1hbmFnZXIuc2VydmljZVwiO1xyXG5pbXBvcnQgeyBDb21tb25TZXJ2aWNlIH0gZnJvbSBcIi4uL3V0aWxzL2NvbW1vbi5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IEhUVFBDb21tcyB9IGZyb20gXCIuLi9jb21tdW5pY2F0aW9uL2h0dHBjb21tcy5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IENhbmNlbEV4cG9ydEV2ZW50IH0gZnJvbSBcIi4uL2V2ZW50cy9jYW5jZWwtZXhwb3J0LWV2ZW50LnNlcnZpY2VcIjtcclxuaW1wb3J0IHsgcGFyZW50QXBwbGljYXRpb24gfSBmcm9tIFwiLi4vdXRpbHMvcGFyZW50LWFwcGxpY2F0aW9uLnNlcnZpY2VcIjtcclxuaW1wb3J0IHsgU3d0VXRpbCB9IGZyb20gXCIuLi91dGlscy9zd3QtdXRpbC5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dEFsZXJ0IH0gZnJvbSBcIi4uL3V0aWxzL3N3dC1hbGVydC5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dEJ1dHRvbiB9IGZyb20gXCIuL3N3dC1idXR0b24uY29tcG9uZW50XCI7XHJcbmltcG9ydCB7IEV4dGVybmFsSW50ZXJmYWNlIH0gZnJvbSBcIi4uL3V0aWxzL2V4dGVybmFsLWludGVyZmFjZS5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IExvZ2dlciB9IGZyb20gXCIuLi9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFN3dFJhZGlvSXRlbSB9IGZyb20gJy4vc3d0LXJhZGlvSXRlbS5jb21wb25lbnQnO1xyXG5pbXBvcnQgeyBTd3RSYWRpb0J1dHRvbkdyb3VwIH0gZnJvbSAnLi9zd3QtcmFkaW9CdXR0b25Hcm91cC5jb21wb25lbnQnO1xyXG5cclxuQENvbXBvbmVudCh7XHJcbiAgc2VsZWN0b3I6ICdTd3RQYWdlc1RvRXhwb3J0JyxcclxuICB0ZW1wbGF0ZTogYFxyXG4gICAgICAgIDxTd3RNb2R1bGUgKGNsb3NlKT0ncG9wdXBDbG9zZWQoKScgKGNyZWF0aW9uQ29tcGxldGUpPSdpbml0RGF0YSgpJyB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCIxMDAlXCIgPlxyXG4gICAgICAgIDxWQm94ICB3aWR0aD0nMTAwJScgaGVpZ2h0PScxMDAlJyBwYWRkaW5nVG9wPVwiMTBcIiBwYWRkaW5nUmlnaHQ9XCIxMFwiIHBhZGRpbmdMZWZ0PVwiMTBcIiBwYWRkaW5nQm90dG9tPVwiMTBcIj5cclxuICAgICAgICAgICAgPFN3dENhbnZhcyB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCI1MCVcIj5cclxuICAgICAgICAgICAgPFN3dFJhZGlvQnV0dG9uR3JvdXAgI2V4cG9ydEJ1dHRvbkdyb3VwICAgaWQ9XCJleHBvcnRCdXR0b25Hcm91cFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ249XCJ2ZXJ0aWNhbFwiPlxyXG4gICAgICAgICAgICAgICAgPFN3dFJhZGlvSXRlbSAjcmFkaW9DIGlkPVwicmFkaW9DXCIgdmFsdWU9XCJjdXJyZW50XCIgIHdpZHRoPVwiMTIwXCIgZ3JvdXBOYW1lPVwiZXhwb3J0QnV0dG9uR3JvdXBcIiBzZWxlY3RlZD1cInRydWVcIiAgPjwvU3d0UmFkaW9JdGVtPlxyXG4gICAgICAgICAgICAgICAgPFN3dFJhZGlvSXRlbSAjcmFkaW9BIGlkPVwicmFkaW9BXCIgdmFsdWU9XCJhbGxcIiAgIHdpZHRoPVwiMTIwXCIgZ3JvdXBOYW1lPVwiZXhwb3J0QnV0dG9uR3JvdXBcIiAgPjwvU3d0UmFkaW9JdGVtPlxyXG4gICAgICAgICAgICA8L1N3dFJhZGlvQnV0dG9uR3JvdXA+XHJcbiAgICAgICAgICAgIDwvU3d0Q2FudmFzPlxyXG4gICAgICAgICAgICA8U3d0Q2FudmFzIHdpZHRoPVwiMTAwJVwiPlxyXG4gICAgICAgICAgICA8SEJveD5cclxuXHJcbiAgICAgICAgICAgIDxTd3RCdXR0b24gYnV0dG9uTW9kZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cIm9rQnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgI29rQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiNzBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlbmFibGVkPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChjbGljayk9XCJleHBvcnRUeXBlKClcIj4gPC9Td3RCdXR0b24+XHJcbiAgICAgICAgICAgIDxTd3RCdXR0b24gYnV0dG9uTW9kZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNhbmNlbEJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICNjYW5jZWxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCI3MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVuYWJsZWQ9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGNsaWNrKT1cInBvcHVwQ2xvc2VkKClcIj48L1N3dEJ1dHRvbj5cclxuICAgICAgICAgICAgPC9IQm94PlxyXG4gICAgICAgICAgICA8L1N3dENhbnZhcz5cclxuICAgICAgICA8L1ZCb3g+XHJcbiAgICAgICAgPC9Td3RNb2R1bGU+XHJcbiAgYCxcclxuICBzdHlsZXM6IFtgXHJcbiAgYF0sXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBTd3RQYWdlc1RvRXhwb3J0IGV4dGVuZHMgU3d0TW9kdWxlIGltcGxlbWVudHMgT25Jbml0IHtcclxuXHJcbiAgICAgLyoqKkJ1dHRvbnMqKioqKioqKi9cclxuXHJcbiAgQFZpZXdDaGlsZCgnb2tCdXR0b24nKSBva0J1dHRvbjogU3d0QnV0dG9uO1xyXG4gIEBWaWV3Q2hpbGQoJ2NhbmNlbEJ1dHRvbicpIGNhbmNlbEJ1dHRvbjogU3d0QnV0dG9uO1xyXG4gIEBWaWV3Q2hpbGQoJ2V4cG9ydEJ1dHRvbkdyb3VwJykgZXhwb3J0QnV0dG9uR3JvdXA6IFN3dFJhZGlvQnV0dG9uR3JvdXA7XHJcbiAgQFZpZXdDaGlsZCgncmFkaW9DJykgcmFkaW9DOiBTd3RSYWRpb0l0ZW07XHJcbiAgQFZpZXdDaGlsZCgncmFkaW9BJykgcmFkaW9BOiBTd3RSYWRpb0l0ZW07XHJcblxyXG5cclxuICBcclxuICBwcml2YXRlIF9leHBvcnRDYW5jZWxGdW5jdGlvbjogRnVuY3Rpb24gPSBuZXcgRnVuY3Rpb24oKTtcclxuXHQvL0V4cG9ydCBmdW5jdGlvbiAoY2FsbGJhY2sgZnVuY3Rpb24pXHJcbiAgIHByaXZhdGUgX2V4cG9ydEZ1bmN0aW9uOiBGdW5jdGlvbiA9IG51bGw7XHJcbiAgXHJcbiAgcHJpdmF0ZSB3aW4gOiBhbnk7IFxyXG5cclxuICBwdWJsaWMgcGFnZXNUb0V4cG9ydDpzdHJpbmcgPVwiXCI7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBlbGVtZW50OiBFbGVtZW50UmVmLCBwcml2YXRlIGNvbW1vbjogQ29tbW9uU2VydmljZSkge1xyXG4gICAgICBzdXBlcihlbGVtZW50LCBjb21tb24pO1xyXG4gIH1cclxuXHJcbiAgbmdPbkluaXQoKTogdm9pZCB7XHJcbiAgICB0aGlzLnJhZGlvQy5sYWJlbCA9IFN3dFV0aWwuZ2V0UHJlZGljdE1lc3NhZ2UoJ2V4cG9ydFBhZ2VzLmxhYmVsLmN1cnJlbnQnLCBudWxsKTtcclxuICAgIHRoaXMucmFkaW9BLmxhYmVsID0gU3d0VXRpbC5nZXRQcmVkaWN0TWVzc2FnZSgnZXhwb3J0UGFnZXMubGFiZWwuYWxsUGFnZXMnLCBudWxsKTtcclxuICAgIHRoaXMub2tCdXR0b24ubGFiZWwgPSBTd3RVdGlsLmdldFByZWRpY3RNZXNzYWdlKCdidXR0b24ub2snLCBudWxsKTtcclxuICAgIHRoaXMuY2FuY2VsQnV0dG9uLmxhYmVsID0gU3d0VXRpbC5nZXRQcmVkaWN0TWVzc2FnZSgnYnV0dG9uLmNhbmNlbCcsIG51bGwpO1xyXG4gIH1cclxuXHJcbiAgaW5pdERhdGEoKSB7XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgcG9wdXBDbG9zZWQoKXtcclxuICAgIHRoaXMuY2xvc2UoKTtcclxuICAgIHRoaXMuX2V4cG9ydENhbmNlbEZ1bmN0aW9uKCk7XHJcblxyXG4gIH1cclxuXHJcbiAgZGVmYXVsdENvbnRlbnRGdW5jdGlvbigpe1xyXG4gICAgICBjb25zb2xlLmxvZyhcIioqKmRlZmF1bHRDb250ZW50RnVuY3Rpb25cIik7XHJcbiAgfVxyXG5cclxuXHJcbiAgcHVibGljIGV4cG9ydFR5cGUoKXtcclxuICAgIGlmKHRoaXMucmFkaW9DLnNlbGVjdGVkKSB7XHJcbiAgICAgICAgdGhpcy5yZXN1bHQgPSBcImN1cnJlbnRcIjtcclxuICAgICAgICB0aGlzLnBhZ2VzVG9FeHBvcnQgID0gXCJjdXJyZW50XCI7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhpcy5yZXN1bHQgPSBcImFsbFwiO1xyXG4gICAgICAgIHRoaXMucGFnZXNUb0V4cG9ydCA9IFwiYWxsXCI7XHJcbiAgICAgfVxyXG4gICAgIHRoaXMuY2xvc2UoKTtcclxuICAgICB0aGlzLl9leHBvcnRGdW5jdGlvbigpO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXQgb25leHBvcnRGdW5jdGlvbigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZXhwb3J0RnVuY3Rpb247XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgc2V0IG9uZXhwb3J0RnVuY3Rpb24oIHZhbHVlOiBGdW5jdGlvbiApIHtcclxuICAgICAgICB0aGlzLl9leHBvcnRGdW5jdGlvbiA9IHZhbHVlO1xyXG4gICAgfVxyXG4gIFxyXG4gIFxyXG4gICAgcHVibGljIGdldCBleHBvcnRDYW5jZWxGdW5jdGlvbigpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5fZXhwb3J0Q2FuY2VsRnVuY3Rpb247XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgc2V0IGV4cG9ydENhbmNlbEZ1bmN0aW9uKCB2YWx1ZTogRnVuY3Rpb24gKSB7XHJcbiAgICAgICAgdGhpcy5fZXhwb3J0Q2FuY2VsRnVuY3Rpb24gPSB2YWx1ZTtcclxuICAgIH1cclxuICAvKipcclxuICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIHNob3cgcmVwb3J0IFByb2dyZXNzLlxyXG4gICAqL1xyXG4gIHB1YmxpYyBzaG93KHBhcmVudCkge1xyXG4gICAgICB0aGlzLndpbiA9ICBTd3RQb3BVcE1hbmFnZXIuY3JlYXRlUG9wVXAodGhpcywgU3d0UGFnZXNUb0V4cG9ydCwge1xyXG4gICAgICB9KTtcclxuXHJcbiAgICB0aGlzLndpbi5lbmFibGVSZXNpemUgPSBmYWxzZTtcclxuICAgICAgdGhpcy53aW4udGl0bGUgPSBcIlBhZ2VzIFRvIEV4cG9ydFwiO1xyXG4gICAgdGhpcy53aW4uaGVpZ2h0ID0gJzE2MCc7XHJcbiAgICB0aGlzLndpbi53aWR0aCA9ICcyMjAnO1xyXG4gICAgdGhpcy53aW4uc2hvd0NvbnRyb2xzID0gZmFsc2U7XHJcbiAgICB0aGlzLndpbi5pc01vZGFsID0gdHJ1ZTtcclxuICAgIHRoaXMud2luLm9uZXhwb3J0RnVuY3Rpb24gPSB0aGlzLl9leHBvcnRGdW5jdGlvbi5iaW5kKHRoaXMpO1xyXG4gICAgdGhpcy53aW4ub25DbG9zZS5zdWJzY3JpYmUoKHJlcykgPT4ge1xyXG4gICAgICAgIFxyXG4gICAgaWYodGhpcy53aW4uZ2V0Q2hpbGQoKS5yZXN1bHQpIHtcclxuICAgICAgICBpZiAodGhpcy53aW4uZ2V0Q2hpbGQoKS5yZXN1bHQgPT0gXCJjdXJyZW50XCIpIHtcclxuICAgICAgICAgICAgdGhpcy5wYWdlc1RvRXhwb3J0ID0gXCJjdXJyZW50XCI7XHJcbiAgICAgICAgfSBlbHNlICB7XHJcbiAgICAgICAgICAgIHRoaXMucGFnZXNUb0V4cG9ydCA9IFwiYWxsXCI7XHJcblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgfVxyXG5cclxuICAgIH0pO1xyXG4gICAgdGhpcy53aW4uZGlzcGxheSgpO1xyXG4gICAgICBcclxuICB9XHJcbiAgLyoqXHJcbiAgICogIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gaGlkZSByZXBvcnQgUHJvZ3Jlc3MuXHJcbiAgICovXHJcbiAgcHVibGljIGhpZGUocGFyZW50OiBhbnkpIHtcclxuICAgICAgIFN3dFBvcFVwTWFuYWdlci5oaWRlKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4cG9ydCBpcyBjYW5jZWxlZFxyXG4gICAqL1xyXG4gIHB1YmxpYyBFeHBvcnRDYW5jZWxlZChldmVudCk6dm9pZCB7XHJcbiAgICAgIC8vIEV4dGVybmFsSW50ZXJmYWNlLmNhbGwoJ2ZpbmlzaEV4cG9ydCcpO1xyXG4gIH1cclxufVxyXG4iXX0=