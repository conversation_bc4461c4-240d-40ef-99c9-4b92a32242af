/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { OperatorType } from 'angular-slickgrid';
import * as DOMPurify_ from 'dompurify';
/** @type {?} */
const o = DOMPurify_;
export class CustomInputFilter {
    constructor() {
        this._clearFilterTriggered = false;
        this._shouldTriggerQuery = true;
        this.searchTerms = [];
        this.operator = OperatorType.equal;
        this.clientSideFilter = true;
    }
    /**
     * Getter for the Column Filter
     * @return {?}
     */
    get columnFilter() {
        return this.columnDef && this.columnDef.filter || {};
    }
    /**
     * Getter for the Grid Options pulled through the Grid Object
     * @protected
     * @return {?}
     */
    get gridOptions() {
        return (this.grid && this.grid.getOptions) ? this.grid.getOptions() : {};
    }
    /**
     * Initialize the Filter
     * @param {?} args
     * @return {?}
     */
    init(args) {
        this.grid = args.grid;
        this.callback = args.callback;
        this.columnDef = args.columnDef;
        this.searchTerms = (args.hasOwnProperty('searchTerms') ? args.searchTerms : []) || [];
        this.clientSideFilter = this.columnDef.params.grid.clientSideFilter;
        // filter input can only have 1 search term, so we will use the 1st array index if it exist
        /** @type {?} */
        const searchTerm = (Array.isArray(this.searchTerms) && this.searchTerms.length >= 0) ? this.searchTerms[0] : '';
        // step 1, create HTML string template
        /** @type {?} */
        const filterTemplate = this.buildTemplateHtmlString();
        // step 2, create the DOM Element of the filter & initialize it if searchTerm is filled
        this.$filterElm = this.createDomElement(filterTemplate, searchTerm);
        $('#search-filter-' + this.grid.getUID() + this.columnDef.id).focus();
        /** @type {?} */
        let elem = $('.ms-filter-' + this.columnDef.id);
        if (searchTerm)
            $(elem).addClass('filled');
        // step 3, subscribe to the keyup event and run the callback when that happens
        this.$filterElm.keyup((/**
         * @param {?} e
         * @return {?}
         */
        (e) => {
            // Prevent the event from bubbling up
            /** @type {?} */
            let value = e && e.target && e.target.value || '';
            /** @type {?} */
            const enableWhiteSpaceTrim = this.gridOptions.enableFilterTrimWhiteSpace || this.columnFilter.enableTrimWhiteSpace;
            if (typeof value === 'string' && enableWhiteSpaceTrim) {
                value = value.trim();
            }
            /** @type {?} */
            let elem = $('.ms-filter-' + this.columnDef.id);
            if (this._clearFilterTriggered)
                $(elem).removeClass('filled');
            else {
                if (this.clientSideFilter)
                    value === '' ? $(elem).removeClass('filled') : $(elem).addClass('filled');
            }
            if (this._clearFilterTriggered && (this.clientSideFilter || (!this.clientSideFilter && e.keyCode == 13))) {
                this.callback(e, { columnDef: this.columnDef, clearFilterTriggered: this._clearFilterTriggered, shouldTriggerQuery: this._shouldTriggerQuery });
            }
            else if (!this._clearFilterTriggered && (this.clientSideFilter || (!this.clientSideFilter && e.keyCode == 13))) {
                this.callback(e, { columnDef: this.columnDef, searchTerms: [value], shouldTriggerQuery: this._shouldTriggerQuery });
            }
            // reset both flags for next use
            this._clearFilterTriggered = false;
            this._shouldTriggerQuery = true;
            if (e.keyCode == 13) {
                $.each(this.grid.getHeaderRow(), (/**
                 * @param {?} index
                 * @param {?} header
                 * @return {?}
                 */
                (index, header) => {
                    if (header instanceof HTMLElement) { // Ensure it's a valid DOM element
                        $(header).toggle();
                    }
                }));
            }
        }));
    }
    /**
     * Clear the filter value
     * @param {?=} shouldTriggerQuery
     * @return {?}
     */
    clear(shouldTriggerQuery = true) {
        if (this.$filterElm) {
            this._clearFilterTriggered = true;
            this._shouldTriggerQuery = shouldTriggerQuery;
            this.$filterElm.val('');
            this.$filterElm.trigger('keyup');
        }
    }
    /**
     * destroy the filter
     * @return {?}
     */
    destroy() {
        if (this.$filterElm) {
            this.$filterElm.off('keyup').remove();
        }
    }
    /**
     * Set value(s) on the DOM element
     * @param {?} values
     * @return {?}
     */
    setValues(values) {
        if (values) {
            this.$filterElm.val((/** @type {?} */ (values)));
        }
    }
    //
    // private functions
    // ------------------
    /**
         * Create the HTML template as a string
         */
    /**
     * Create the HTML template as a string
     */
    /**
     * Create the HTML template as a string
     * @private
     * @return {?}
     */
    buildTemplateHtmlString() {
        console.log('buildTemplateHtmlString');
        /** @type {?} */
        let placeholder = 'search in ' + this.columnDef.name;
        /** @type {?} */
        let $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        /** @type {?} */
        let elem = $('#' + this.grid.getUID() + this.columnDef.id);
        // Add the filter button to the header
        elem.append(`
    <div class="ms-parent ms-filter search-filter ms-filter-${this.columnDef.id}" style="width: 32px;">
      <button id="filter-${this.grid.getUID() + this.columnDef.id}" type="button" class="ms-choice">
        <span class="placeholder" title=""></span>
        <div></div>
      </button>
    </div>
  `);
        setTimeout((/**
         * @return {?}
         */
        () => {
            console.log("🚀 ~ CustomInputFilter ~ buildTemplateHtmlString ~ elem:", elem);
            console.log('setTimeout');
            $('#filter-' + this.grid.getUID() + this.columnDef.id).click((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                event.stopPropagation();
                event.preventDefault();
                // Close all other open dropdowns
                $('div[name^=filter-]').each((/**
                 * @param {?} index
                 * @param {?} item
                 * @return {?}
                 */
                (index, item) => {
                    if ($(item).css('display') === "block") {
                        $(item).hide();
                    }
                }));
                // Only toggle headers within this grid instance
                /** @type {?} */
                const $gridContainer = $('.' + this.grid.getUID());
                $gridContainer.find('.slick-headerrow-columns-left, .slick-headerrow-columns-right').each((/**
                 * @return {?}
                 */
                function () {
                    /** @type {?} */
                    const $header = $(this);
                    /** @type {?} */
                    const $parent = $header.parent();
                    /** @type {?} */
                    const isHeaderVisible = $header.is(':visible');
                    /** @type {?} */
                    const isParentVisible = $parent.is(':visible');
                    /** @type {?} */
                    const isVisible = isHeaderVisible && isParentVisible;
                    if (isVisible) {
                        $header.hide();
                        $parent.hide();
                        $header.data('visible', false);
                    }
                    else {
                        $header.show();
                        $parent.show();
                        $header.data('visible', true);
                    }
                }));
                // Focus the search input if visible
                /** @type {?} */
                const searchInput = $(`#search-filter-${this.grid.getUID() + this.columnDef.id}`);
                if (searchInput.length && searchInput.is(':visible')) {
                    searchInput.focus();
                }
            }));
        }), 0);
        return `
    <input id="search-filter-${this.grid.getUID() + this.columnDef.id}" type="text" class="form-control search-filter" placeholder="${placeholder}">
  `;
    }
    /**
     * From the html template string, create a DOM element
     * @private
     * @param {?} filterTemplate
     * @param {?=} searchTerm
     * @return {?}
     */
    createDomElement(filterTemplate, searchTerm) {
        /** @type {?} */
        const $headerElm = this.grid.getHeaderRowColumn(this.columnDef.id);
        $($headerElm).empty();
        // create the DOM element & add an ID and filter class
        /** @type {?} */
        const $filterElm = $(filterTemplate);
        $filterElm.val(searchTerm);
        $filterElm.data('columnId', this.columnDef.id);
        // append the new DOM element to the header row
        if ($filterElm && typeof $filterElm.appendTo === 'function') {
            $filterElm.appendTo($headerElm);
        }
        return $filterElm;
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype._clearFilterTriggered;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype._shouldTriggerQuery;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype.$filterElm;
    /** @type {?} */
    CustomInputFilter.prototype.grid;
    /** @type {?} */
    CustomInputFilter.prototype.searchTerms;
    /** @type {?} */
    CustomInputFilter.prototype.columnDef;
    /** @type {?} */
    CustomInputFilter.prototype.callback;
    /** @type {?} */
    CustomInputFilter.prototype.operator;
    /**
     * @type {?}
     * @private
     */
    CustomInputFilter.prototype.clientSideFilter;
}
//# sourceMappingURL=data:application/json;base64,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