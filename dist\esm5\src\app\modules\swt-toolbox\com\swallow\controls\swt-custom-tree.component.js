/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, Injectable, Input, Output, ViewChild } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { SwtCheckboxEvent, CustomTreeEvent, genericEvent } from "../events/swt-events.module";
//Import LESS or CSS:
// import 'assets/css/TreeThemes/skin-lion/ui.fancytree.less'
import { JSONReader } from '../jsonhandler/jsonreader.service';
import { Logger } from '../logging/logger.service';
import { CommonService } from '../utils/common.service';
import { HashMap } from '../utils/HashMap.service';
import { AdvancedToolTip } from "./advanced-tool-tip.component";
import { UIComponent } from "./UIComponent.service";
import { StringUtils } from '../utils/string-utils.service';
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
var fancytree = require('jquery.fancytree');
require('jquery.fancytree/dist/modules/jquery.fancytree.edit');
require('jquery.fancytree/dist/modules/jquery.fancytree.filter');
require('jquery.fancytree/dist/modules/jquery.fancytree.dnd');
require('jquery.fancytree/dist/modules/jquery.fancytree.multi');
require('jquery.fancytree/dist/modules/jquery.fancytree.table');
require('jquery.fancytree/dist/modules/jquery.fancytree.wide');
var CustomTree = /** @class */ (function (_super) {
    tslib_1.__extends(CustomTree, _super);
    // tree constructor.
    function CustomTree(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        // handle to check if tree loaded for first time.
        _this.firstLoad = true;
        // A local sequence
        _this.local_sequence = 1;
        // variables to handle CRUD operations.
        _this.CRUD_OPERATION = 'crud_operation';
        _this.CRUD_DATA = 'crud_data';
        _this.iconFunction = new Function();
        _this.ITEM_CLICK = new EventEmitter();
        _this.ITEM_ACTIVATE = new EventEmitter();
        _this.ITEM_DBCLICK = new EventEmitter();
        _this.MOUSE_OUT = new EventEmitter();
        _this.itemOpen = new EventEmitter();
        _this.itemClose = new EventEmitter();
        _this.MOUSE_OVER = new EventEmitter();
        _this.FOCUS_IN = new EventEmitter();
        _this.FOCUS_OUT = new EventEmitter();
        _this.styleName = '';
        //    @Output('mouseOut') mouseOut = new EventEmitter<Function>();
        _this.buttonMode = false;
        // variable used in the algorithm that calculate selectedIndex.
        _this._tree_state = new Array();
        _this.itemEditEnd = new EventEmitter();
        /**
         * private array to hold tree options.
         * private.
         */
        _this.options = ['dnd', 'edit', 'table' /*, 'filter' 'wide'*/];
        _this.isItemSelected = false;
        //    private _click: Function = null;
        _this._dbclick = null;
        //    private _mouseOver: Function = null;
        _this._tempIndex = -1;
        //    private _focusIn: Function = null;
        //    private _focusOut: Function = null;
        //    private _mouseIn: Function = null;
        // private variable to handle selectedLevel.
        _this._selectedLevel = null;
        _this._keyDownFlag = false;
        _this._level0OrderAttrName = 'DEFAULT_ORDER_ATTR';
        _this._level1OrderAttrName = 'DEFAULT_ORDER_ATTR';
        _this._level2OrderAttrName = 'DEFAULT_ORDER_ATTR';
        _this._level3OrderAttrName = 'DEFAULT_ORDER_ATTR';
        _this._level4OrderAttrName = 'DEFAULT_ORDER_ATTR';
        _this._globalFindStop = false;
        //Parameter List for Edited ITem
        _this.editableAttribute = null;
        _this.editableAdditionalAttribute = null;
        _this.editableValidationFunction = null;
        _this.leaf_key = 0;
        _this._firstLoad = true;
        _this.__OriginalDataProvider = null;
        _this._hideIcons = false;
        _this._addCheckbox = false;
        _this._indeterminateCheckbox = false;
        _this._saveTreeStateBasedOn = '';
        // private variable to handle changes.
        _this._changes = new HashMap();
        //    @Output('mouseOver') mouseOver = new EventEmitter<Function>();
        _this._hideFunction = (/**
         * @param {?} item
         * @return {?}
         */
        function (item) { return _this.defaultHideFunction(item); });
        _this._level2Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        _this._level3Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        _this._level4Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        /**
         * private.
         */
        _this._allowMultipleSelection = false;
        _this._selectedIndices = new Array();
        // private variable to handle selected index.
        _this._selectedIndex = -1;
        // private variable to activate doubleClick
        _this._doubleClickEnabled = true;
        // private variable to enable/disable selectable property
        _this._selectable = true;
        // private variable to enable / disable tree.
        _this._enabled = true;
        _this._level1Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        _this._level0Order = 'DEFAULT_ORDER_ATTR=ASC:N';
        _this._iconWidth = 16; // Adjust this if @fancy-icon-width != "16px"
        _this._iconSpacing = 3; // Adjust this if @fancy-icon-spacing != "3px"
        _this._labelSpacing = 3; // Adjust this if padding between icon and label !=  "3px"
        _this._levelOfs = 16; // Adjust this if ul padding != "16px"
        _this._dragMoveEnabled = false;
        _this._dragEnabled = false;
        _this._editable = false;
        _this._openItems = new Array();
        _this._closeItems = new Array();
        _this._verticalScrollPosition = 0;
        _this._showRoot = true;
        _this.logger = new Logger('CustomTree', _this.commonService.httpclient, 6);
        return _this;
    }
    /**
     * @protected
     * @param {?} item
     * @return {?}
     */
    CustomTree.prototype.defaultHideFunction = /**
     * @protected
     * @param {?} item
     * @return {?}
     */
    function (item) {
        if (item.data.visible == false) {
            return true;
        }
        else {
            if (!this.isVisibleNode(item)) {
                return true;
            }
        }
    };
    Object.defineProperty(CustomTree.prototype, "changes", {
        /**
         * _changes getter
         * */
        get: /**
         * _changes getter
         *
         * @return {?}
         */
        function () {
            return this._changes;
        },
        /**
         * _changes setter
         * */
        set: /**
         * _changes setter
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._changes = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "hideFunction", {
        /**
         * This method is used to set a callback function to the tree
         * component in order to show or hide specific items.
         * @param callback
         */
        set: /**
         * This method is used to set a callback function to the tree
         * component in order to show or hide specific items.
         * @param {?} callback
         * @return {?}
         */
        function (callback) {
            this._hideFunction = callback;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "level2Order", {
        /**
         * Setter for _level2Order
         */
        get: /**
         * Setter for _level2Order
         * @return {?}
         */
        function () {
            return this._level2Order;
        },
        /**
         * Setter for _level2Order
         */
        set: /**
         * Setter for _level2Order
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level2Order = value;
            this._level2OrderAttrName = this._level2Order.substr(0, this._level2Order.indexOf('=') !== -1 ? this._level2Order.indexOf('=') : this._level2Order.length);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "level3Order", {
        /**
         * Setter for _level3Order
         * */
        get: /**
         * Setter for _level3Order
         *
         * @return {?}
         */
        function () {
            return this._level3Order;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level3Order = value;
            this._level3OrderAttrName = this._level3Order.substr(0, this._level3Order.indexOf('=') != -1 ? this._level3Order.indexOf('=') : this._level3Order.length);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "level4Order", {
        /**
         * Setter for _level4Order
         * */
        get: /**
         * Setter for _level4Order
         *
         * @return {?}
         */
        function () {
            return this._level4Order;
        },
        /**
         * Setter for _level4Order
         * */
        set: /**
         * Setter for _level4Order
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level4Order = value;
            this._level4OrderAttrName = this._level4Order.substr(0, this._level4Order.indexOf('=') != -1 ? this._level4Order.indexOf('=') : this._level4Order.length);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "allowMultipleSelection", {
        get: /**
         * @return {?}
         */
        function () {
            return this._allowMultipleSelection;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (this._allowMultipleSelection == value) {
                    return;
                }
                value.toString() == "true" ? this._allowMultipleSelection = true : this._allowMultipleSelection = false;
                if (this._allowMultipleSelection) {
                    this.options.push("multi");
                }
                else {
                    this.options.indexOf("multi") !== -1 ? this.options.splice(this.options.indexOf("multi")) : null;
                }
            }
            catch (e) {
                this.logger.error("allowMultipleSelection error: " + e);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "selectedIndices", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedIndices;
        },
        /**
         * This method is used to get selected Indices.
         */
        set: /**
         * This method is used to get selected Indices.
         * @param {?} indices
         * @return {?}
         */
        function (indices) {
            this.logger.info('[ selectedIndices ] START.');
            /** @type {?} */
            var errorLocation = 0;
            try {
            }
            catch (error) {
                this.logger.error('[ selectedIndices ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
            }
            this.logger.info('[ selectedIndices ] END.');
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "dataProvider", {
        // dataProvider getter.
        get: 
        // dataProvider getter.
        /**
         * @return {?}
         */
        function () {
            return this.__OriginalDataProvider;
        },
        // Input to hold tree dataProvider.
        set: 
        // Input to hold tree dataProvider.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var tempData = [];
            /** @type {?} */
            var errorLocation = 0;
            try {
                tempData = this._dataProvider;
                /** Khalil needs */
                if (value.length) {
                    this.__OriginalDataProvider = tslib_1.__spread(value);
                }
                else {
                    this.__OriginalDataProvider = JSON.parse(JSON.stringify(value));
                }
                // Khalil needs end.
                this.leaf_key = 0;
                errorLocation = 10;
                this._dataProvider = this.recursive(value, null);
                if (this._dataProvider !== tempData) {
                    if (this.eventlist[genericEvent.CHANGE]) {
                        this.eventlist[genericEvent.CHANGE](this);
                    }
                }
                errorLocation = 20;
                if (this.firstLoad) {
                    this.init();
                    // set first load to false.
                    this.firstLoad = false;
                    // initialize tree instance.
                    this._instance = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree');
                }
                else {
                    this.getInstance().reload(this._dataProvider);
                }
                errorLocation = 30;
            }
            catch (error) {
                this.logger.error('[ dataProvider ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "width", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        // width setter method
        set: 
        // width setter method
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._width = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "height", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "hideIcons", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._hideIcons;
        },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._hideIcons = true;
                }
                else {
                    this._hideIcons = false;
                }
            }
            else {
                this._hideIcons = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "addCheckbox", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._addCheckbox;
        },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._addCheckbox = true;
                }
                else {
                    this._addCheckbox = false;
                }
            }
            else {
                this._addCheckbox = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "indeterminateCheckbox", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._indeterminateCheckbox;
        },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._indeterminateCheckbox = true;
                }
                else {
                    this._indeterminateCheckbox = false;
                }
            }
            else {
                this._indeterminateCheckbox = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "saveTreeStateBasedOn", {
        // width getter method.
        get: 
        // width getter method.
        /**
         * @return {?}
         */
        function () {
            return this._saveTreeStateBasedOn;
        },
        // height setter method.
        set: 
        // height setter method.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._saveTreeStateBasedOn = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "selectedIndex", {
        // get tree selectedIndex.
        get: 
        // get tree selectedIndex.
        /**
         * @return {?}
         */
        function () {
            return this._selectedIndex;
        },
        // get tree selectedIndex.
        set: 
        // get tree selectedIndex.
        /**
         * @param {?} index
         * @return {?}
         */
        function (index) {
            this.logger.info('[ selectedIndex ] START.');
            /** @type {?} */
            var errorLocation = 0;
            try {
                if (index > -1) {
                    this.diselectAll();
                    /** @type {?} */
                    var node = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getNodeByKey(this._tree_state[index]);
                    if (this._hideFunction) {
                        /** @type {?} */
                        var item = new CustomTreeItem(node, this.commonService);
                        /** @type {?} */
                        var hideNode = this._hideFunction(item);
                        if (!hideNode) {
                            // scroll into selected node.
                            node.makeVisible({ scrollIntoView: true });
                            node.setActive(true);
                            this.selectNode(node);
                            if (node.isSelected()) {
                                $(node.span).closest('tr').addClass("fancytree-selected");
                            }
                            if (node.isActive()) {
                                $(node.span).closest('tr').addClass("fancytree-active");
                            }
                        }
                    }
                    /** @type {?} */
                    var _height = $(this.treeContainer.nativeElement).height();
                    /** @type {?} */
                    var scrollTop = this.getScrollPosition();
                    if (scrollTop > _height) {
                        $($(this.treeContainer.nativeElement)[0]).scrollTop(scrollTop);
                    }
                }
                else {
                    if (!this.addCheckbox) {
                        ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
                         * @param {?} node
                         * @return {?}
                         */
                        function (node) {
                            if (node.isSelected()) {
                                node.setSelected(false);
                            }
                        }));
                    }
                    this._selectedItem = null;
                    this._selectedLevel = null;
                }
                this._selectedIndex = index;
                this._verticalScrollPosition = index * 20;
            }
            catch (error) {
                this.logger.error('[ selectedIndex ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
            }
            this.logger.info('[ selectedIndex ] END.');
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "selectedItem", {
        // get Tree selected Item.
        get: 
        // get Tree selected Item.
        /**
         * @return {?}
         */
        function () {
            return this._selectedItem;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value) {
                if (value === this._selectedItem) {
                    return;
                }
                this.diselectAll();
                value.selected = true;
                this._selectedItem = value;
                this._selectedIndex = this._tree_state.indexOf(this._selectedItem.key);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "doubleClickEnabled", {
        // doubleClickEnabled getter.
        get: 
        // doubleClickEnabled getter.
        /**
         * @return {?}
         */
        function () {
            return this._doubleClickEnabled;
        },
        // doubleClickEnabled setter
        set: 
        // doubleClickEnabled setter
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value.toString() == "false") {
                this._doubleClickEnabled = false;
            }
            else if (value.toString() == "true") {
                this._doubleClickEnabled = true;
            }
            else {
                this._doubleClickEnabled = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "selectable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectable;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            //TODO
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "enabled", {
        // enabled getter.
        get: 
        // enabled getter.
        /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        // enabled setter.
        set: 
        // enabled setter.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._enabled = value;
            ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').enable(this._enabled);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "level1Order", {
        /**
         * Setter for _level1Order
         */
        get: /**
         * Setter for _level1Order
         * @return {?}
         */
        function () {
            return this._level1Order;
        },
        /**
         * Setter for _level1Order
         */
        set: /**
         * Setter for _level1Order
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level1Order = value;
            this._level1OrderAttrName = this._level1Order.substr(0, this._level1Order.indexOf('=') !== -1 ? this._level1Order.indexOf('=') : this._level1Order.length);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "level0Order", {
        /**
             * Setter for _level1Order
             */
        get: /**
         * Setter for _level1Order
         * @return {?}
         */
        function () {
            return this._level0Order;
        },
        /**
         * Setter for _level1Order
         */
        set: /**
         * Setter for _level1Order
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level0Order = value;
            this._level0OrderAttrName = this._level0Order.substr(0, this._level0Order.indexOf('=') !== -1 ? this._level0Order.indexOf('=') : this._level0Order.length);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "iconWidth", {
        get: 
        // Adjust this if @fancy-icon-width != "16px"
        /**
         * @return {?}
         */
        function () {
            return this._iconWidth;
        },
        /**
         *  set tree icon width
         * @param value
         */
        set: /**
         *  set tree icon width
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._iconWidth = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "iconSpacing", {
        get: 
        // Adjust this if @fancy-icon-spacing != "3px"
        /**
         * @return {?}
         */
        function () {
            return this._iconSpacing;
        },
        /**
         * set tree icon spacing.
         * @param value
         */
        set: /**
         * set tree icon spacing.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._iconSpacing = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "labelSpacing", {
        get: 
        // Adjust this if padding between icon and label !=  "3px"
        /**
         * @return {?}
         */
        function () {
            return this._labelSpacing;
        },
        /**
         * set tree label spacing
         * @param value
         */
        set: /**
         * set tree label spacing
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._labelSpacing = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "levelOfs", {
        get: 
        // Adjust this if ul padding != "16px"
        /**
         * @return {?}
         */
        function () {
            return this._levelOfs;
        },
        /**
         * set tree level offsets.
         * @param value
         */
        set: /**
         * set tree level offsets.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._levelOfs = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "dragMoveEnabled", {
        // enabled getter.
        get: 
        // enabled getter.
        /**
         * @return {?}
         */
        function () {
            return this._dragMoveEnabled;
        },
        // enabled setter.
        set: 
        // enabled setter.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dragMoveEnabled = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "dragEnabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dragEnabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._dragEnabled = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "editable", {
        // enabled getter.
        get: 
        // enabled getter.
        /**
         * @return {?}
         */
        function () {
            return this._editable;
        },
        // enabled setter.
        set: 
        // enabled setter.
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof value === "string") {
                if (value === "true") {
                    this._editable = true;
                }
                else if (value === "false") {
                    this._editable = false;
                }
            }
            else {
                this._editable = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "openItems", {
        get: /**
         * @return {?}
         */
        function () {
            return this._openItems;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._openItems = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "closeItems", {
        get: /**
         * @return {?}
         */
        function () {
            return this._closeItems;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._closeItems = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "verticalScrollPosition", {
        get: /**
         * @return {?}
         */
        function () {
            return this._verticalScrollPosition;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            $(this.elem.nativeElement.children[0]).animate({ scrollTop: value }, 0);
            this._verticalScrollPosition = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "showRoot", {
        /**
         *  Sets the visibility of the root item.
         *
         *  If the dataProvider data has a root node, and this is set to
         *  <code>false</code>, the Tree control does not display the root item.
         *  Only the decendants of the root item are displayed.
         *
         *  This flag has no effect on non-rooted dataProviders, such as List and Array.
         *
         *  @default true
         *  @see #hasRoot
         */
        get: /**
         *  Sets the visibility of the root item.
         *
         *  If the dataProvider data has a root node, and this is set to
         *  <code>false</code>, the Tree control does not display the root item.
         *  Only the decendants of the root item are displayed.
         *
         *  This flag has no effect on non-rooted dataProviders, such as List and Array.
         *
         * \@default true
         * @see #hasRoot
         * @return {?}
         */
        function () {
            return this._showRoot;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (this._showRoot !== value) {
                this._showRoot = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "editedItemPosition", {
        /**
         *  The column and row index of the item renderer for the
         *  data provider item being edited, if any.
         *
         *  <p>This Object has two fields, <code>columnIndex</code> and
         *  <code>rowIndex</code>,
         *  the zero-based column and item indexes of the item.
         *  For a List control, the <code>columnIndex</code> property is always 0;
         *  for example: <code>{columnIndex:0, rowIndex:3}</code>.</p>
         *
         *  <p>Setting this property scrolls the item into view and
         *  dispatches the <code>itemEditBegin</code> event to
         *  open an item editor on the specified item,
         *  </p>
         */
        get: /**
         *  The column and row index of the item renderer for the
         *  data provider item being edited, if any.
         *
         *  <p>This Object has two fields, <code>columnIndex</code> and
         *  <code>rowIndex</code>,
         *  the zero-based column and item indexes of the item.
         *  For a List control, the <code>columnIndex</code> property is always 0;
         *  for example: <code>{columnIndex:0, rowIndex:3}</code>.</p>
         *
         *  <p>Setting this property scrolls the item into view and
         *  dispatches the <code>itemEditBegin</code> event to
         *  open an item editor on the specified item,
         *  </p>
         * @return {?}
         */
        function () {
            if (this._editedItemPosition) {
                return {
                    rowIndex: this._editedItemPosition.rowIndex,
                    columnIndex: 0
                };
            }
            else {
                return this._editedItemPosition;
            }
        },
        /**
         *  private
         */
        set: /**
         *  private
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                /** @type {?} */
                var key = this._tree_state[value.rowIndex];
                /** @type {?} */
                var node = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getNodeByKey(key);
                this.editable = true;
                // this.diselectAll();
                // node.setSelected();
                this.selectedItem = new CustomTreeItem(node, this.commonService);
                node.editStart();
                this._editedItemPosition = value;
            }
            catch (e) {
                this.logger.error("editedItemPosition " + e);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTree.prototype, "isFirstLoad", {
        get: /**
         * @return {?}
         */
        function () {
            return this._firstLoad;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} node
     * @return {?}
     */
    CustomTree.getExpander = /**
     * @param {?} node
     * @return {?}
     */
    function (node) {
        /** @type {?} */
        var $span = $(node.span);
        return $span.find('> span.fancytree-expander');
    };
    /**
     * @param {?} node
     * @param {?} operation
     * @param {?=} updatedAttribute
     * @param {?=} map
     * @return {?}
     */
    CustomTree.prototype.manageChangesArray = /**
     * @param {?} node
     * @param {?} operation
     * @param {?=} updatedAttribute
     * @param {?=} map
     * @return {?}
     */
    function (node, operation, updatedAttribute, map) {
        if (updatedAttribute === void 0) { updatedAttribute = null; }
        if (map === void 0) { map = null; }
        /** @type {?} */
        var changeObject = new Object;
        /** @type {?} */
        var tempObject = new Object;
        /** @type {?} */
        var changesChildObject = new Object;
        /** @type {?} */
        var putChanges = true;
        /** @type {?} */
        var existAndEqual = true;
        /** @type {?} */
        var sequence = -1;
        /** @type {?} */
        var index = 0;
        /** @type {?} */
        var seq;
        // Set itemSeq value
        sequence = node.key;
        // check if the operation is insert
        if (operation == "I") {
            index = this.changes.size();
            // if the changes contains items and map is not null
            if (this.changes.size() > 0 && map != null) {
                //                 existAndEqual=true;
                // itterrate the changes map
                for (seq in this.changes.getKeys()) {
                    changesChildObject = this.changes.getValue(seq);
                    //
                    existAndEqual = false;
                    if (changesChildObject[CustomTree.CRUD_OPERATION] == "D") {
                        existAndEqual = true;
                        for (var key in map.getKeys()) {
                            if (changesChildObject[key] == null || changesChildObject[key] != map.getValue(key)) {
                                existAndEqual = false;
                                break;
                            }
                        }
                    }
                    if (existAndEqual) {
                        break;
                    }
                    index--;
                }
                if (existAndEqual) {
                    this._changes.remove(seq);
                    putChanges = false;
                }
            }
            changeObject[CustomTree.CRUD_OPERATION] = "I";
            changeObject[CustomTree.CRUD_DATA] = node;
        }
        else if (operation == "U") { // if operation is an update
            // if operation is an update
            // set the attribute map
            /** @type {?} */
            var findMap = new HashMap();
            findMap.put('TREE_ITEM_LOCAL_SEQ', sequence.toString());
            // set _globalFindStop to false
            this._globalFindStop = false;
            // set the original xml node
            /** @type {?} */
            var xml = this.getInstance().getNodeByKey(sequence.toString());
            // check if the chnages map contains the updated element
            if (this._changes.containsKey(sequence)) {
                changeObject = this._changes.getValue(sequence);
                if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf("U") != -1) {
                    // check if the updated attributes is the same as the original attribute value
                    if (xml[updatedAttribute] == node[updatedAttribute]) {
                        /* Remove the updated attribute from changes */
                        changeObject[CustomTree.CRUD_OPERATION] = String(changeObject[CustomTree.CRUD_OPERATION]).replace('U(' + updatedAttribute + ')', "");
                        changeObject[CustomTree.CRUD_OPERATION] = String(changeObject[CustomTree.CRUD_OPERATION]).replace('>>', '>');
                        if (String(changeObject[CustomTree.CRUD_OPERATION]).indexOf('U') < 0)
                            this._changes.remove(sequence);
                        // operation will not be logged in the changes map
                        putChanges = false;
                    }
                    if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf(updatedAttribute) < 0) {
                        changeObject[CustomTree.CRUD_OPERATION] = changeObject[CustomTree.CRUD_OPERATION] + '>' + 'U(' + updatedAttribute + ')';
                    }
                }
                // set the changeObject
                changeObject[CustomTree.CRUD_DATA] = node;
            }
            else {
                // check if xml is not null
                if (xml != null) {
                    // check if the updated attributes is the same as the original attribute value
                    if (xml[updatedAttribute] == node[updatedAttribute]) {
                        // operation will not be logged in the changes map
                        putChanges = false;
                    }
                }
                changeObject[CustomTree.CRUD_OPERATION] = 'U(' + updatedAttribute + ')';
                changeObject[CustomTree.CRUD_DATA] = node;
            }
        }
        else if (operation == "D") {
            if (this._changes.containsKey(sequence)) {
                changeObject = this._changes.getValue(sequence);
                if (((/** @type {?} */ (changeObject[CustomTree.CRUD_OPERATION]))).indexOf("I") != -1) {
                    this._changes.remove(sequence);
                    putChanges = false;
                }
                else {
                    changeObject[CustomTree.CRUD_OPERATION] = "D";
                    changeObject[CustomTree.CRUD_DATA] = node;
                }
            }
            else {
                changeObject[CustomTree.CRUD_OPERATION] = "D";
                changeObject[CustomTree.CRUD_DATA] = node;
            }
        }
        else {
            changeObject[CustomTree.CRUD_OPERATION] = operation;
            changeObject[CustomTree.CRUD_DATA] = node;
            //     sequence = local_sequence++;
        }
        if (map != null) {
            /** @type {?} */
            var mapkeys = map.getKeys();
            for (var index_1 in mapkeys) {
                tempObject = map.getValue(mapkeys[index_1]);
                changeObject[mapkeys[index_1]] = tempObject;
            }
        }
        if (putChanges) {
            this._changes.put(sequence, changeObject);
        }
        putChanges = true;
    };
    /**
     * Finds a node based on a map of attribute values
     * @param attributes
     * @param parentNode
     */
    /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @param {?=} parentNode
     * @return {?}
     */
    CustomTree.prototype.findNode = /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @param {?=} parentNode
     * @return {?}
     */
    function (attributes, parentNode) {
        var _this = this;
        if (parentNode === void 0) { parentNode = null; }
        /** @type {?} */
        var matchedNode = null;
        try {
            if (parentNode) {
                /** @type {?} */
                var subChildren = parentNode.getChildren();
                subChildren.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                function (item) {
                    if (_this.findNodeRecursively(item.getNode(), attributes)) {
                        matchedNode = item;
                        return;
                    }
                }));
            }
            else {
                ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
                 * @param {?} node
                 * @return {?}
                 */
                function (node) {
                    if (_this.findNodeRecursively(node, attributes)) {
                        matchedNode = new CustomTreeItem(node, _this.commonService);
                        return;
                    }
                }));
            }
        }
        catch (e) {
            this.logger.error("findNode " + e);
        }
        return matchedNode;
    };
    /**
     * Finds a node based on a map of attribute values
     * @param attributes
     */
    /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @return {?}
     */
    CustomTree.prototype.findAndExpandNode = /**
     * Finds a node based on a map of attribute values
     * @param {?} attributes
     * @return {?}
     */
    function (attributes) {
        try {
            this.findNode(attributes, null).expand();
        }
        catch (e) {
            this.logger.error("findAndExpandNode " + e);
        }
    };
    /**
     * Recursively expand a node
     *
     * Enhancement to expand the Tree to specified Level
     */
    /**
     * Recursively expand a node
     *
     * Enhancement to expand the Tree to specified Level
     * @param {?} nodeXmlList
     * @param {?} expandToLvl
     * @return {?}
     */
    CustomTree.prototype.expandNode = /**
     * Recursively expand a node
     *
     * Enhancement to expand the Tree to specified Level
     * @param {?} nodeXmlList
     * @param {?} expandToLvl
     * @return {?}
     */
    function (nodeXmlList, expandToLvl) {
        try {
            nodeXmlList.expand();
        }
        catch (e) {
            this.logger.error("expandNode " + e);
        }
    };
    /**
     * Sorts a tree based on configs given for level1Order, level2Order, level3Order and level4Order
     * TODO: Think on a recursive function for levels 1, 2 3 and .. n
     */
    /**
     * Sorts a tree based on configs given for level1Order, level2Order, level3Order and level4Order
     * TODO: Think on a recursive function for levels 1, 2 3 and .. n
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.sort = /**
     * Sorts a tree based on configs given for level1Order, level2Order, level3Order and level4Order
     * TODO: Think on a recursive function for levels 1, 2 3 and .. n
     * @param {?} node
     * @return {?}
     */
    function (node) {
        try {
            /** @type {?} */
            var nodeLevel = node.getLevel();
            if (nodeLevel === 1) {
                this.sortNodeBy(node, this.level1Order);
            }
            else if (nodeLevel === 2) {
                this.sortNodeBy(node, this.level2Order);
            }
            else if (nodeLevel === 3) {
                this.sortNodeBy(node, this.level3Order);
            }
            else if (nodeLevel === 4) {
                this.sortNodeBy(node, this.level4Order);
            }
        }
        catch (e) {
            this.log.error("sort error: ", e);
        }
    };
    /**
     * Function responsible of setting the label
     */
    /**
     * Function responsible of setting the label
     * @param {?} item
     * @return {?}
     */
    CustomTree.prototype.treeLabelFunction = /**
     * Function responsible of setting the label
     * @param {?} item
     * @return {?}
     */
    function (item) {
        return item ? item.NAME : "";
    };
    /**
     * Recursively finds a node and expands it if argument expand is passed "true"
     * @param nodeXmlList
     * @param attributes
     * @param expand
     */
    /**
     * Recursively finds a node and expands it if argument expand is passed "true"
     * @param {?} node
     * @param {?} attributes
     * @param {?=} expand
     * @return {?}
     */
    CustomTree.prototype.findNodeRecursively = /**
     * Recursively finds a node and expands it if argument expand is passed "true"
     * @param {?} node
     * @param {?} attributes
     * @param {?=} expand
     * @return {?}
     */
    function (node, attributes, expand) {
        if (expand === void 0) { expand = false; }
        /** @type {?} */
        var nodeData = node.data;
        /** @type {?} */
        var keys = attributes.getKeys();
        /** @type {?} */
        var values = attributes.getValues();
        try {
            for (var index = 0; index < keys.length; index++) {
                if (!nodeData[keys[index]]) {
                    if (!node[keys[index]]) {
                        return false;
                    }
                }
                else {
                    if (nodeData[keys[index]] != attributes.getValue(keys[index])) {
                        return false;
                    }
                }
            }
            if (expand) {
                node.setExpanded();
            }
            return true;
        }
        catch (e) {
            console.error(e);
        }
    };
    /**
     * This method is used to select node item by attribute id and value
     * @param selectNodeByAttribute
     */
    /**
     * This method is used to select node item by attribute id and value
     * @param {?} attributeId
     * @param {?} attributeValue
     * @param {?=} uniqueNode
     * @return {?}
     */
    CustomTree.prototype.selectNodeByAttribute = /**
     * This method is used to select node item by attribute id and value
     * @param {?} attributeId
     * @param {?} attributeValue
     * @param {?=} uniqueNode
     * @return {?}
     */
    function (attributeId, attributeValue, uniqueNode) {
        var _this = this;
        if (uniqueNode === void 0) { uniqueNode = false; }
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var nodeToFind = null;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                if (node.data[attributeId] == attributeValue) {
                    try {
                        if (nodeToFind == null && uniqueNode) {
                            nodeToFind = node;
                            node.setActive(true);
                            _this.selectNode(node);
                        }
                        else {
                            if (!uniqueNode) {
                                node.setActive(true);
                                _this.selectNode(node);
                            }
                        }
                    }
                    catch (e) {
                    }
                }
            }));
        }
        catch (error) {
            this.logger.error('[ selectNodeByAttribute ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectNodeByAttribute ] END.');
    };
    /**
     * @return {?}
     */
    CustomTree.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (!this.id) {
                throw new Error('Programming error you must specify CustomTree id');
            }
            this.logger.info('[ ngOnInit ] METHOD ENTER.');
            // Added by Rihab.J @10/12/2018 - needed to be used in dynamically added SwtCustomTree.
            $($(this.elem.nativeElement)[0]).attr('selector', 'SwtCustomTree');
            // set id to SwtCustomTree DOM.
            if (this.id) {
                $($(this.elem.nativeElement)[0]).attr('id', this.id);
            }
            //-END-
            $(this.elem.nativeElement).height(this.height);
            $(this.elem.nativeElement).width(this.width);
            this.logger.info('[ ngOnInit ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ ngOnInit ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    };
    /**
     * @return {?}
     */
    CustomTree.prototype.ngAfterContentInit = /**
     * @return {?}
     */
    function () {
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.logger.info('[ ngAfterContentInit ] METHOD ENTER.');
            this.logger.info('[ ngAfterContentInit ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ ngAfterContentInit ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    };
    /**
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.isVisibleNode = /**
     * @param {?} node
     * @return {?}
     */
    function (node) {
        /** @type {?} */
        var visible = true;
        if (node.data && node.data.visible == false) {
            return false;
        }
        else if (node.parent != null && node.parent.data != null) {
            return (this.isVisibleNode(node.parent));
        }
        else {
            return true;
        }
    };
    /**
     * This method initialize the tree component View
     */
    /**
     * This method initialize the tree component View
     * @return {?}
     */
    CustomTree.prototype.init = /**
     * This method initialize the tree component View
     * @return {?}
     */
    function () {
        var _this = this;
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.logger.info('[ init ] METHOD ENTER.');
            this._instance = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree({
                extensions: this.options,
                source: this._dataProvider,
                checkbox: this.addCheckbox,
                lazy: true,
                // lazyLoad: function(event, data){
                //     data.result = data.node.data;
                // },
                selectMode: this.addCheckbox && this.indeterminateCheckbox ? 3 : 2,
                autoScroll: false,
                quicksearch: (/**
                 * @return {?}
                 */
                function () {
                    return true;
                }),
                table: {
                    indentation: 20,
                    // indent 20px per node level
                    nodeColumnIdx: 0,
                    // render the node title into the 2nd column
                    checkboxColumnIdx: this.addCheckbox ? 1 : 0 // render the checkboxes into the 1st column
                },
                tooltip: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    // Create dynamic tooltips
                    /** @type {?} */
                    var dataTip = data.node.title;
                    if (_this.dataTipFunction) {
                        dataTip = _this.dataTipFunction(new CustomTreeItem(data.node, _this.commonService));
                    }
                    return dataTip;
                }),
                create: (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                }),
                init: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    // data.tree.getRootNode().sortChildren((n1, n2) => {
                    //     const title1 = String(n1.title);
                    //     const title2 = String(n2.title);
                    //     return title2 === title1 ? 0 : title1 > title2 ? 1 : -1;
                    // }, true);
                    _this.sortNodeBy(data.tree.getRootNode(), _this.level0Order);
                    data.tree.visit((/**
                     * @param {?} node
                     * @return {?}
                     */
                    function (node) {
                        _this.sort(node);
                    }));
                }),
                click: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    if (data.targetType == 'expander')
                        return;
                    // get tree node to be rendered.
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var isItemSelected = node.isSelected();
                    _this._selectedIndex = _this._tree_state.indexOf(node.key);
                    // create new Tree Item object.
                    /** @type {?} */
                    var item = new CustomTreeItem(node, _this.commonService);
                    _this._selectedLevel = item.level;
                    _this._selectedItem = item;
                    if (event.ctrlKey) {
                        // if (node) {
                        //     if (node.isSelected()) {
                        //         // node.selected = false;
                        //         // this.diselectAll();
                        //     } else {
                        //         node.selected = true;
                        //     }
                        //     node.render();
                        // }
                    }
                    _this._selectedIndices = [];
                    // calculate selectedIndices on each item click.
                    if (_this._allowMultipleSelection) {
                        setTimeout((/**
                         * @return {?}
                         */
                        function () {
                            ((/** @type {?} */ ($(_this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
                             * @param {?} treeNode
                             * @return {?}
                             */
                            function (treeNode) {
                                if ($(treeNode.span).closest('tr').hasClass("fancytree-selected")) {
                                    // verify if the selected indice already exist
                                    if (_this._selectedIndices.indexOf(_this._tree_state.indexOf(treeNode.key)) === -1) {
                                        _this._selectedIndices.push(_this._tree_state.indexOf(treeNode.key));
                                    }
                                }
                            }));
                            // dispache the item click event;
                            if (_this.eventlist[CustomTreeEvent.ITEMCLICK]) {
                                _this.eventlist[CustomTreeEvent.ITEMCLICK](item);
                            }
                            // emit item click event.
                            _this.ITEM_CLICK.emit(item);
                        }), 0);
                    }
                    else {
                        _this._selectedIndices.push(_this._tree_state.indexOf(node.key));
                        // dispache the item click event;
                        if (_this.eventlist[CustomTreeEvent.ITEMCLICK]) {
                            _this.eventlist[CustomTreeEvent.ITEMCLICK](item);
                        }
                        // emit item click event.
                        _this.ITEM_CLICK.emit(item);
                    }
                    _this.cursorLocation = { x: event.pageX, y: event.pageY };
                    if (item.getCustomToolTip()) {
                        item.getCustomToolTip().top = event.pageY;
                        item.getCustomToolTip().left = event.pageX;
                    }
                }),
                icon: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    if (_this.hideIcons) {
                        return false;
                    }
                    // get tree node to be rendered.
                    /** @type {?} */
                    var node = data.node;
                    // create new Tree Item object.
                    /** @type {?} */
                    var item = new CustomTreeItem(node, _this.commonService);
                    try {
                        // call custom StyleFunction
                        return _this.customStyleFunction(item);
                    }
                    catch (e) {
                        console.error(e);
                    }
                }),
                dblclick: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    if (!_this.doubleClickEnabled) {
                        return false;
                    }
                    // get tree node to be rendered.
                    /** @type {?} */
                    var tempSelectedIndex = _this._selectedIndex;
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var item = new CustomTreeItem(node, _this.commonService);
                    _this._selectedIndex = _this._tree_state.indexOf(node.key);
                    _this._selectedItem = item;
                    if (_this._selectedIndex !== tempSelectedIndex) {
                        if (_this.eventlist[genericEvent.CHANGE]) {
                            _this.eventlist[genericEvent.CHANGE](_this);
                        }
                    }
                    // this.itemDoubleClick_.emit(item);
                    // if exist a double click listener so emit the event.
                    if (_this.eventlist[CustomTreeEvent.ITEMDOUBLECLICK]) {
                        _this.eventlist[CustomTreeEvent.ITEMDOUBLECLICK](item);
                    }
                }),
                keydown: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    _this._keyDownFlag = true;
                    /** @type {?} */
                    var node = data.node;
                    // handle  key navigation
                    if (event.keyCode === 40) {
                        _this.scrollToBottom(event, node);
                    }
                    else if (event.keyCode === 38) {
                        _this.scrollToTop(event, node);
                    }
                }),
                focus: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                }),
                focusTree: (/**
                 * @param {?} event
                 * @return {?}
                 */
                function (event) {
                }),
                select: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    // ($(this.treeContainer.nativeElement) as any).fancytree('getTree').visit((node) => {
                    //     let item = new CustomTreeItem(node, this.commonService);
                    //     if (this._hideFunction) {
                    //     }
                    // });
                    if (data.targetType === 'checkbox') {
                        // node.data.selected = node.selected;
                        /** @type {?} */
                        var dataProviderNode = _this.findNodeinDataprovider(node.key, _this.dataProvider);
                        _this.recusiveSelectDataProvider(node, dataProviderNode);
                        _this.recusiveSelectDataProviderChildren(node, dataProviderNode);
                        /** @type {?} */
                        var dto = {};
                        dto.node = node;
                        dto.data = data;
                        // dto.selected = this.selected;
                        // dto.yField = this.yField;
                        // // Dispatch the event so that legends will be highlightened as well
                        if ($(_this.elem.nativeElement).closest('swttabpushstrategy')) {
                            dto.parentTab = $(_this.elem.nativeElement).closest('swttabpushstrategy').attr('id');
                        }
                        SwtCheckboxEvent.emit(dto);
                    }
                    // if (node.getChildren() && node.getChildren().length > 0) {
                    //     for (var index = 0; index < node.getChildren().length; index++) {
                    //         if (this._hideFunction) {
                    //             const item = new CustomTreeItem(node.getChildren()[index], this.commonService);
                    //             let hideNode = this._hideFunction(item);
                    //             if (hideNode + "" === "true") {
                    //                 $(node.getChildren()[index].span).closest('tr').addClass('fancytree-helper-hidden');
                    //             }
                    //         }
                    //     }
                    // }
                    node.visit((/**
                     * @param {?} childNode
                     * @return {?}
                     */
                    function (childNode) {
                        if (_this._hideFunction) {
                            /** @type {?} */
                            var item = new CustomTreeItem(childNode, _this.commonService);
                            /** @type {?} */
                            var hideNode = _this._hideFunction(item);
                            if (hideNode + "" === "true") {
                                $(childNode.span).closest('tr').addClass('fancytree-helper-hidden');
                            }
                        }
                    }));
                }),
                beforeSelect: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    //this.diselectAll();
                }),
                activate: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var tempSelectedIndex = _this._selectedIndex;
                    /** @type {?} */
                    var isItemSelected = node.isSelected();
                    // this.diselectAll();
                    /*                this.selectedIndex = */
                    _this._selectedIndex = _this._tree_state.indexOf(node.key);
                    if (_this._selectedIndex !== tempSelectedIndex) {
                        if (_this.eventlist[genericEvent.CHANGE]) {
                            _this.eventlist[genericEvent.CHANGE](_this);
                        }
                    }
                    // update selected item.
                    _this._selectedItem = new CustomTreeItem(node, _this.commonService);
                    _this._verticalScrollPosition = $(_this.elem.nativeElement.children[0]).scrollTop();
                    _this._selectedLevel = 'Level' + data.node.getLevel();
                    _this.selectNode(node);
                    // if (!this._keyDownFlag) { TODO item clik event moved to clik verify if an error appear.
                    //     this.ITEM_CLICK.emit(data.node);
                    // }
                    _this.ITEM_ACTIVATE.emit(data.node);
                    // this._keyDownFlag = false;
                }),
                // Image folder used for data.icon attribute.
                imagePath: 'assets/spirites/',
                // icon: false,
                renderNode: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    // get tree node to be rendered.
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var $span = $(node.span);
                    /** @type {?} */
                    var _icon = null;
                    if ($span.find('> span.fancytree-icon').length > 0) {
                        _icon = $span.find('> span.fancytree-icon');
                    }
                    else {
                        _icon = $span.find('> span.fancytree-custom-icon');
                    }
                    /** @type {?} */
                    var _expander = $span.find('> span.fancytree-expander');
                    /** @type {?} */
                    var _title = $span.find('> span.fancytree-title');
                    try {
                        if (_this.labelFunction) {
                            /** @type {?} */
                            var label = new TreeLabel(_title, _this.commonService);
                            if (node.data) {
                                for (var attribute in node.data) {
                                    if (node.data.hasOwnProperty(attribute)) {
                                        if (isNaN(Number(attribute))) {
                                            label.bindAttribute(attribute, node.data[attribute]);
                                        }
                                    }
                                }
                            }
                            label.item = new CustomTreeItem(node, _this.commonService);
                            _this.labelFunction(label);
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                    try {
                        /** @type {?} */
                        var _data = node.data;
                        if (_this._hideFunction) {
                            /** @type {?} */
                            var item = new CustomTreeItem(node, _this.commonService);
                            /** @type {?} */
                            var hideNode = _this._hideFunction(item);
                            if (hideNode + "" === "true") {
                                $(node.span).closest('tr').addClass('fancytree-helper-hidden');
                            }
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                    // console.log(node.data, node.selected , node.partsel);
                    try {
                        /** @type {?} */
                        var dataProviderNode = _this.findNodeinDataprovider(node.key, _this.dataProvider);
                        if (dataProviderNode) {
                            dataProviderNode.selected = node.data.selected = node.selected;
                            dataProviderNode.indeterminate = node.data.indeterminate = node.partsel;
                        }
                    }
                    catch (e) {
                        console.log(e);
                    }
                    // if(this._saveTreeStateBasedOn){
                    //     let itemFound =  this.openItems.find(x => (x['data'][this._saveTreeStateBasedOn] == node.data[this._saveTreeStateBasedOn])) != null;
                    //     if(itemFound){
                    //     }
                    // }
                    if (_this.eventlist[CustomTreeEvent.ICONCLICK]) {
                        $(_icon).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        function (evt) {
                            /** @type {?} */
                            var item = new CustomTreeItem(node, _this.commonService);
                            evt.target = item;
                            _this.eventlist[CustomTreeEvent.ICONCLICK](evt);
                            if (item.getCustomToolTip()) {
                                // item.getCustomToolTip().top = this.getCursorLocation().y;
                                item.getCustomToolTip().left = _this.getCursorLocation().x;
                            }
                            _this._selectedItem = item;
                        }));
                        $(_icon).css("cursor", "pointer");
                    }
                    if (_this.eventlist[CustomTreeEvent.ICONMOUSEENTER]) {
                        $(_icon).mouseenter((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        function (evt) {
                        }));
                        _this.buttonMode = true;
                    }
                    if (_this.eventlist[CustomTreeEvent.ICONMOUSELEAVE]) {
                        $(_icon).mouseleave((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        function (evt) {
                            /** @type {?} */
                            var item = new CustomTreeItem(node, _this.commonService);
                            _this.eventlist[CustomTreeEvent.ICONMOUSELEAVE](item);
                        }));
                        _this.buttonMode = false;
                    }
                    if (_this.eventlist[CustomTreeEvent.TITLECLICK]) {
                        $($span.find('> span.fancytree-title')).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        function (evt) {
                        }));
                    }
                    if (_this.eventlist[CustomTreeEvent.EXPANDERCLICK]) {
                        $($span.find('> span.fancytree-expander')).click((/**
                         * @param {?} evt
                         * @return {?}
                         */
                        function (evt) {
                        }));
                    }
                    if (_this.eventlist[CustomTreeEvent.ICONFOCUS]) {
                        // TODO -------
                    }
                    if (_this.eventlist[CustomTreeEvent.ICONFOCUSOUT]) {
                        // TODO -------
                    }
                }),
                expand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    var _a;
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var $span = $(node.span);
                    /** @type {?} */
                    var _title = $span.find('> span.fancytree-title');
                    // this._openItems = this.getOpenedItems();
                    // start of code used to update tree state capture
                    /** @type {?} */
                    var _children = new Array();
                    if (node.getChildren()) {
                        for (var index = 0; index < node.getChildren().length; index++) {
                            _children.push(node.getChildren()[index].key);
                        }
                    }
                    /** @type {?} */
                    var _exist = _this._tree_state.join().indexOf(_children.join()) !== -1 ? true : false;
                    if (_this._tree_state.indexOf(_children[0]) === -1) {
                        (_a = _this._tree_state).splice.apply(_a, tslib_1.__spread([_this._tree_state.indexOf(node.key) + 1, 0], _children));
                    }
                    // end of code used to update tree state capture
                    _this.itemOpen.emit({ item: new CustomTreeItem(node, _this.commonService), target: _this });
                }),
                collapse: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    // this._openItems = this.getOpenedItems();
                    // const _index = this._openItems.indexOf(node.data.id);
                    // if (_index !== -1) {
                    //     this._openItems.splice(_index, 1);
                    // }
                    // start of code used to update tree state capture
                    _this._tree_state.splice(_this._tree_state.indexOf(node.key) + 1, node.getChildren().length);
                    // end of code used to update tree state capture
                }),
                beforeExpand: (/**
                 * @param {?} event
                 * @param {?} data
                 * @return {?}
                 */
                function (event, data) {
                    /** @type {?} */
                    var node = data.node;
                    /** @type {?} */
                    var $span = $(node.span);
                }),
                //            multi: {
                //               mode: "sameParent"  // Restrict range selection behavior
                //            },
                dnd: {
                    autoExpandMS: 400,
                    focusOnClick: true,
                    preventVoidMoves: true,
                    preventRecursiveMoves: true,
                    dragStart: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    function (node, data) {
                        return _this.dragMoveEnabled;
                    }),
                    dragEnter: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    function (node, data) {
                        return _this.dragMoveEnabled;
                    }),
                    dragDrop: (/**
                     * @param {?} node
                     * @param {?} data
                     * @return {?}
                     */
                    function (node, data) {
                        data.otherNode.moveTo(node, data.hitMode);
                    })
                },
                edit: {
                    triggerStart: ['clickActive', 'dblclick', 'f2', 'mac+enter', 'shift+click'],
                    beforeEdit: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    function (event, data) {
                        /** @type {?} */
                        var node = data.node;
                        /** @type {?} */
                        var $span = $(node.span);
                        if (_this.editable) {
                            /** @type {?} */
                            var _title = $span.find('> span.fancytree-title');
                            _title.css("background-color", "#FFF");
                            _title.css("outline", "2px solid #8ACFFF");
                        }
                        // Return false to prevent edit mode
                        return _this.editable;
                    }),
                    edit: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    function (event, data) {
                        // Editor was opened (available as data.input)
                        // return this.editable;
                    }),
                    beforeClose: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    function (event, data) {
                        /** @type {?} */
                        var node = data.node;
                        /** @type {?} */
                        var $span = $(node.span);
                        // call edit item function to validate the entered string.
                        // this.itemEditEndHandler(new CustomTreeItem(node, this.commonService));
                        // Return false to prevent cancel/save (data.input is available)
                        // return true;
                    }),
                    save: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    function (event, data) {
                        // return false;
                    }),
                    close: (/**
                     * @param {?} event
                     * @param {?} data
                     * @return {?}
                     */
                    function (event, data) {
                        // Editor was removed
                        if (data.save) {
                            // Since we started an async request, mark the node as preliminary
                            $(data.node.span).addClass('pending');
                        }
                        /** @type {?} */
                        var editedItem = new CustomTreeItem(data.node, _this.commonService);
                        // editedItem.title = data.input.val();
                        _this.itemEditEndHandler(editedItem);
                        // return this.editable;
                    })
                }
            });
            $.ui.fancytree.debugLevel = 2;
            /** @type {?} */
            var i = 0;
            //        $(".fancytree-container").toggleClass("fancytree-connectors");
            if (this.isFirstLoad) {
                /** @type {?} */
                var root = ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').getRootNode();
                for (var index = 0; index < root.getChildren().length; index++) {
                    this._tree_state.push(root.getChildren()[index].key);
                }
            }
            // update tree instance.
            this.logger.info('[ init ] METHOD END.');
        }
        catch (error) {
            this.logger.error('[ init ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
    };
    /**
     * @param {?} id
     * @param {?} currentNode
     * @return {?}
     */
    CustomTree.prototype.findNodeinDataprovider = /**
     * @param {?} id
     * @param {?} currentNode
     * @return {?}
     */
    function (id, currentNode) {
        /** @type {?} */
        var i;
        /** @type {?} */
        var currentChild;
        /** @type {?} */
        var result;
        if (id == currentNode.key) {
            return currentNode;
        }
        else {
            // Use a for loop instead of forEach to avoid nested functions
            // Otherwise "return" will not work properly
            if (currentNode.length !== undefined) {
                for (i = 0; i < currentNode.length; i += 1) {
                    currentChild = currentNode[i];
                    // Search in the current child
                    result = this.findNodeinDataprovider(id, currentChild);
                    // Return the result if the node has been found
                    if (result !== false) {
                        return result;
                    }
                }
            }
            for (i = 0; currentNode.children !== undefined && i < currentNode.children.length; i += 1) {
                currentChild = currentNode.children[i];
                // Search in the current child
                result = this.findNodeinDataprovider(id, currentChild);
                // Return the result if the node has been found
                if (result !== false) {
                    return result;
                }
            }
            // The node has not been found and we have no more options
            return false;
        }
    };
    /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    CustomTree.prototype.recusiveSelectDataProvider = /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    function (node, dataProvidernode) {
        dataProvidernode.selected = node.data.selected = node.selected;
        dataProvidernode.indeterminate = node.data.indeterminate = node.partsel;
        if (node.parent.title != 'root') {
            /** @type {?} */
            var parentProviderNode = dataProvidernode.parentData;
            this.recusiveSelectDataProvider(node.parent, parentProviderNode);
        }
        // if()
    };
    /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    CustomTree.prototype.recusiveSelectDataProviderChildren = /**
     * @param {?} node
     * @param {?} dataProvidernode
     * @return {?}
     */
    function (node, dataProvidernode) {
        if (node.children && node.children.length > 0) {
            for (var index = 0; index < node.children.length; index++) {
                dataProvidernode.children[index].selected = node.children[index].data.selected = node.children[index].selected;
                dataProvidernode.children[index].indeterminate = node.children[index].data.indeterminate = node.children[index].partsel;
                if (node.children[index].children && node.children[index].children.length > 0)
                    this.recusiveSelectDataProviderChildren(node.children[index], dataProvidernode.children[index]);
            }
        }
    };
    /**
     * This method is used to remove tree node.
     * @param itemToRemove
     * @param map
     * @param addToChanges
     */
    /**
     * This method is used to remove tree node.
     * @param {?=} itemToRemove
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    CustomTree.prototype.removeNode = /**
     * This method is used to remove tree node.
     * @param {?=} itemToRemove
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    function (itemToRemove, map, addToChanges) {
        if (addToChanges === void 0) { addToChanges = true; }
        this.logger.info('[ removeNode ] START.');
        /** @type {?} */
        var item = null;
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.editable = false;
            if (itemToRemove == null) {
                item = this.selectedItem;
            }
            else {
                item = itemToRemove;
            }
            if (item) {
                this._tree_state.splice(this._tree_state.indexOf(item.key), 1);
                item.remove();
            }
            if (addToChanges) {
                this.manageChangesArray(item, "D", null, map);
            }
        }
        catch (error) {
            this.logger.error('[ removeNode ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ removeNode ] END.');
    };
    /**
     * This method is used to determine the expander orientation according
     * to it's state (expanded/collapsed)
     * @param node
     */
    /**
     * This method is used to determine the expander orientation according
     * to it's state (expanded/collapsed)
     * @private
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.manageExpanderOrientation = /**
     * This method is used to determine the expander orientation according
     * to it's state (expanded/collapsed)
     * @private
     * @param {?} node
     * @return {?}
     */
    function (node) {
        try {
            /** @type {?} */
            var $span = $(node.span);
            /** @type {?} */
            var isCustomIcons = false;
            /** @type {?} */
            var icon = null;
            if ($span.find('> span.fancytree-icon').length > 0) {
                icon = $span.find('> span.fancytree-icon');
                isCustomIcons = false;
            }
            else {
                icon = $span.find('> span.fancytree-custom-icon');
                isCustomIcons = true;
            }
            if (!isCustomIcons) {
                if (node.isExpanded()) {
                    icon.removeClass("swt-icon-folder-closed");
                    icon.addClass("swt-icon-folder-open");
                }
                else {
                    icon.removeClass("swt-icon-folder-open");
                    icon.addClass("swt-icon-folder-closed");
                }
            }
            /** @type {?} */
            var expander = $span.find('> span.fancytree-expander');
            expander.css("background-image", "url('icons.gif')");
            if (!node.hasChildren() && node.getLevel() !== 1) {
                expander.css("visibility", "hidden");
                icon.addClass("file");
            }
            else {
                if (node.isExpanded()) {
                    expander.css("background-position", "-48px -80px");
                }
                else {
                    expander.css("background-position", "0px -80px");
                }
            }
        }
        catch (e) {
            this.logger.error("manageExpanderOrientatio", e);
        }
    };
    /**
     * This method is used to remove all tree children.
     * @param addToChanges
     */
    /**
     * This method is used to remove all tree children.
     * @param {?=} addToChanges
     * @return {?}
     */
    CustomTree.prototype.removeAll = /**
     * This method is used to remove all tree children.
     * @param {?=} addToChanges
     * @return {?}
     */
    function (addToChanges) {
        if (addToChanges === void 0) { addToChanges = true; }
        this.logger.info('[ removeAll ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('destroy');
        }
        catch (error) {
            this.logger.error('[ removeAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ removeAll ] END.');
    };
    /**
     * This method is used to set tree item to editable.
     * @param attributeName
     * @param attributesToFind
     * @param parentNode
     * @param additionalInformation
     * @param validateFunction
     */
    /**
     * This method is used to set tree item to editable.
     * @param {?} attributeName
     * @param {?=} attributesToFind
     * @param {?=} parentNode
     * @param {?=} additionalInformation
     * @param {?=} validateFunction
     * @return {?}
     */
    CustomTree.prototype.setEditableItem = /**
     * This method is used to set tree item to editable.
     * @param {?} attributeName
     * @param {?=} attributesToFind
     * @param {?=} parentNode
     * @param {?=} additionalInformation
     * @param {?=} validateFunction
     * @return {?}
     */
    function (attributeName, attributesToFind, parentNode, additionalInformation, validateFunction) {
        if (attributesToFind === void 0) { attributesToFind = null; }
        if (parentNode === void 0) { parentNode = null; }
        if (additionalInformation === void 0) { additionalInformation = null; }
        if (validateFunction === void 0) { validateFunction = null; }
        this.logger.info('[ setEditableItem ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.editableAttribute = attributeName.toString();
            this.editableAdditionalAttribute = additionalInformation;
            this.editableValidationFunction = validateFunction;
            /** @type {?} */
            var itemToEdit = null;
            //Find XML if attribute hashMap is passed
            if (attributesToFind != null && attributesToFind.size() > 0) {
                itemToEdit = this.findNode(attributesToFind, parentNode);
                this.selectedItem = itemToEdit;
            }
            // set the item as editable
            this.editable = true;
            this.editedItemPosition = { rowIndex: this.selectedIndex };
        }
        catch (error) {
            this.logger.error('[ setEditableItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ setEditableItem ] END.');
    };
    /**
     * Appends a node to tree, if afterNode is passed as parameter usually tree.selectedItem
     * then add node after the passed node else add it at the end
     * @param aNode
     * @param afterNode
     * @param map
     * @param addToChanges
     */
    /**
     * Appends a node to tree, if afterNode is passed as parameter usually tree.selectedItem
     * then add node after the passed node else add it at the end
     * @param {?} aNode
     * @param {?=} afterNode
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    CustomTree.prototype.appendNode = /**
     * Appends a node to tree, if afterNode is passed as parameter usually tree.selectedItem
     * then add node after the passed node else add it at the end
     * @param {?} aNode
     * @param {?=} afterNode
     * @param {?=} map
     * @param {?=} addToChanges
     * @return {?}
     */
    function (aNode, afterNode, map, addToChanges) {
        if (afterNode === void 0) { afterNode = null; }
        if (map === void 0) { map = null; }
        if (addToChanges === void 0) { addToChanges = true; }
        this.logger.info('[ appendNode ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (aNode) {
                if (!aNode.key) {
                    aNode.key = "apnd_" + new Date().getTime();
                }
                // convert name or label to title.
                if (aNode.NAME !== undefined) {
                    aNode["title"] = aNode["NAME"];
                }
                else if (aNode.label !== undefined) {
                    aNode["title"] = aNode["label"];
                }
                if (afterNode) {
                    if (afterNode instanceof CustomTreeItem) {
                        if (aNode.level) {
                            if (this.getNumberFrom(aNode.level) == afterNode.getNode().getLevel() + 1) {
                                aNode.level = "Level" + this.getNumberFrom(aNode.level);
                                ((/** @type {?} */ (afterNode))).getNode().addNode(aNode, "firstChild");
                                /** @type {?} */
                                var afterKey = afterNode.key;
                                this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                                !afterNode.isExpanded() ? afterNode.expand() : null;
                            }
                            else {
                                aNode.level = "Level" + afterNode.getNode().getLevel();
                                ((/** @type {?} */ (afterNode))).getNode().appendSibling(aNode);
                                /** @type {?} */
                                var afterKey = afterNode.key;
                                this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                            }
                        }
                        else {
                            aNode.level = "Level" + afterNode.getNode().getLevel();
                            ((/** @type {?} */ (afterNode))).getNode().addNode(aNode, "after");
                            /** @type {?} */
                            var afterKey = afterNode.key;
                            this._tree_state.splice(this._tree_state.indexOf(afterKey), 0, aNode.key);
                        }
                    }
                }
                else {
                    this.getInstance().getRootNode().addChildren(aNode);
                    this._tree_state.push(aNode.key);
                }
            }
        }
        catch (error) {
            this.logger.error('[ appendNode ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ appendNode ] END.');
    };
    /**
     * This method is used to expand all tree nodes.
     * @param expandToLvl
     */
    /**
     * This method is used to expand all tree nodes.
     * @param {?=} expandToLvl
     * @return {?}
     */
    CustomTree.prototype.expandAll = /**
     * This method is used to expand all tree nodes.
     * @param {?=} expandToLvl
     * @return {?}
     */
    function (expandToLvl) {
        var _this = this;
        this.logger.info('[ expandAll ] START.');
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var hide;
        /** @type {?} */
        var targetLevel = expandToLvl ? Number(expandToLvl.match(/(\d+)/)[0]) : -1;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                /** @type {?} */
                var item = new CustomTreeItem(node, _this.commonService);
                if (_this._hideFunction) {
                    hide = _this._hideFunction(item);
                    if (hide + "" !== "true") {
                        if (expandToLvl && targetLevel !== -1) {
                            if (node.getLevel() <= targetLevel) {
                                // node.setExpanded();
                                item.expand();
                            }
                        }
                        else {
                            // node.setExpanded();
                            item.expand();
                        }
                    }
                    else {
                        $(node.span).closest('tr').addClass('fancytree-helper-hidden');
                    }
                }
                else {
                    if (expandToLvl && targetLevel !== -1) {
                        if (node.getLevel() <= targetLevel) {
                            // node.setExpanded();
                            item.expand();
                        }
                    }
                    else {
                        // node.setExpanded();
                        item.expand();
                    }
                }
            }));
        }
        catch (error) {
            this.logger.error('[ expandAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandAll ] END.');
    };
    /**
     * Close all nodes
     */
    /**
     * Close all nodes
     * @return {?}
     */
    CustomTree.prototype.collapseAll = /**
     * Close all nodes
     * @return {?}
     */
    function () {
        this.logger.info('[ collapseAll ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                node.setExpanded(false);
            }));
        }
        catch (error) {
            this.logger.error('[ expandAll ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ collapseAll ] END.');
    };
    /**
     * @param {?} Comp
     * @return {?}
     */
    CustomTree.prototype.setDataTip = /**
     * @param {?} Comp
     * @return {?}
     */
    function (Comp) {
        try {
            /** @type {?} */
            var compRef = this.commonService.resolver.resolveComponentFactory(AdvancedToolTip).create(this.commonService.injector);
            /** @type {?} */
            var child = compRef.instance.addChild(Comp);
            // attach component to current application.
            this.commonService.applicationRef.attachView(compRef.hostView);
            // get component root node (component selector).
            /** @type {?} */
            var selector = (/** @type {?} */ (((/** @type {?} */ (compRef.hostView))).rootNodes[0]));
            // attach component root node to DOM.
            $(this.treeTipHolder.nativeElement).append(selector);
            this._dataTip = compRef.instance;
            return compRef.instance;
        }
        catch (error) {
            this.logger.error("setDataTip error: ", error);
        }
    };
    /**
     * @return {?}
     */
    CustomTree.prototype.getDataTip = /**
     * @return {?}
     */
    function () {
        return this._dataTip;
    };
    /**
     * This method is used to get selected Items.
     */
    /**
     * This method is used to get selected Items.
     * @return {?}
     */
    CustomTree.prototype.selectedItems = /**
     * This method is used to get selected Items.
     * @return {?}
     */
    function () {
        this.logger.info('[ selectedItems ] START.');
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var items = [];
        try {
            if (this.selectedIndices.length > 0) {
                for (var i = 0; i < this.selectedIndices.length; i++) {
                    /** @type {?} */
                    var key = this._tree_state[this.selectedIndices[i]];
                    /** @type {?} */
                    var node = ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(key);
                    items.push(new CustomTreeItem(node, this.commonService));
                }
            }
            return items;
        }
        catch (error) {
            this.logger.error('[ selectedItems ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ selectedItems ] END.');
    };
    /**
     * This method is used to validate Display List.
     */
    /**
     * This method is used to validate Display List.
     * @return {?}
     */
    CustomTree.prototype.validateDisplayList = /**
     * This method is used to validate Display List.
     * @return {?}
     */
    function () {
        this.logger.info('[ validateDisplayList ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            //TODO
        }
        catch (error) {
            this.logger.error('[ validateDisplayList ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ validateDisplayList ] END.');
    };
    /**
     * This method is used reopens the already opened items
     */
    /**
     * This method is used reopens the already opened items
     * @return {?}
     */
    CustomTree.prototype.reOpenSavedState = /**
     * This method is used reopens the already opened items
     * @return {?}
     */
    function () {
        var _this = this;
        this.logger.info('[ reOpenSavedState ] START.');
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var i = 0;
        try {
            /*($(this.elem.nativeElement.children[0].children[0]) as any).fancytree('getTree').visit((node) => {
                if (this.openItems.indexOf(node.data.id) !== -1) {
                    this.expandItem({key: node.key}, true);
                    i++;
                }
            });*/
            if (this.saveTreeStateBasedOn) {
                this.openItems.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                function (item) {
                    ((/** @type {?} */ ($(_this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
                     * @param {?} node
                     * @return {?}
                     */
                    function (node) {
                        if (node.data[_this._saveTreeStateBasedOn] == item.data[_this._saveTreeStateBasedOn]) {
                            node.setExpanded();
                        }
                    }));
                }));
            }
            else {
                this.openItems.forEach((/**
                 * @param {?} item
                 * @return {?}
                 */
                function (item) {
                    // item.setExpanded(true);
                    ((/** @type {?} */ ($(_this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(item.key + '').setExpanded();
                }));
            }
            // selected saved index
            this.selectedIndex = this._tempIndex;
        }
        catch (error) {
            this.logger.error('[ reOpenSavedState ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ reOpenSavedState ] END.');
    };
    /**
     * Save the tree open state
     */
    /**
     * Save the tree open state
     * @return {?}
     */
    CustomTree.prototype.saveTreeOpenState = /**
     * Save the tree open state
     * @return {?}
     */
    function () {
        this.logger.info('[ saveTreeOpenState ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            // ($(this.treeContainer.nativeElement) as any).fancytree('getTree').visit((node) => {
            //     if (node.isExpanded()) {
            //         if (this._openItems.indexOf(node.data.id) === -1) {
            //             this._openItems.push(node.data.id);
            //         }
            //     }
            // });
            this._openItems = this.getOpenedItems();
            this._closeItems = this.getClosedItems();
            // save selected index.
            this._tempIndex = this.selectedIndex;
        }
        catch (error) {
            this.logger.error('[ saveTreeOpenState ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ saveTreeOpenState ] END.');
    };
    /**
     * Opens or closes a branch item.
     * When a branch item opens, it restores the open and closed states
     * of its child branches if they were already opened.
     * @param item
     * @param open
     * @param animate
     * @param dispatchEvent
     * @param cause
     */
    /**
     * Opens or closes a branch item.
     * When a branch item opens, it restores the open and closed states
     * of its child branches if they were already opened.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    CustomTree.prototype.expandItem = /**
     * Opens or closes a branch item.
     * When a branch item opens, it restores the open and closed states
     * of its child branches if they were already opened.
     * @param {?} item
     * @param {?} open
     * @param {?=} animate
     * @param {?=} dispatchEvent
     * @param {?=} cause
     * @return {?}
     */
    function (item, open, animate, dispatchEvent, cause) {
        this.logger.info('[ expandItem ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            /** @type {?} */
            var _nodePath = (((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(item.key + '').getKeyPath(false)).split('/');
            errorLocation = 10;
            _nodePath = _nodePath.slice(1);
            errorLocation = 20;
            for (var key = 0; key < _nodePath.length; key++) {
                errorLocation = 30;
                ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').getNodeByKey(_nodePath[key] + '').setExpanded();
                errorLocation = 40;
            }
        }
        catch (error) {
            this.logger.error('[ expandItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandItem ] END.');
    };
    /**
     * This method is used to set custom style function.
     */
    /**
     * This method is used to set custom style function.
     * @param {?} item
     * @return {?}
     */
    CustomTree.prototype.customStyleFunction = /**
     * This method is used to set custom style function.
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('[ customStyleFunction ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            if (this.eventlist[CustomTreeEvent.ITEMRENDER]) {
                this.eventlist[CustomTreeEvent.ITEMRENDER](item);
            }
            // call icon function if exist.
            return this.iconFunction(item);
        }
        catch (error) {
            this.logger.error('[ customStyleFunction ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ customStyleFunction ] END.');
    };
    /**
     * This method is used to get Item Index.
     * @param item
     */
    /**
     * This method is used to get Item Index.
     * @param {?} item
     * @return {?}
     */
    CustomTree.prototype.getItemIndex = /**
     * This method is used to get Item Index.
     * @param {?} item
     * @return {?}
     */
    function (item) {
        this.logger.info('[ expandItem ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            return item.key ? this._tree_state.indexOf(item.key) : -1;
        }
        catch (error) {
            this.logger.error('[ expandItem ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ expandItem ] END.');
    };
    /**
     * This method is used to cleans CRUD changes.
     */
    /**
     * This method is used to cleans CRUD changes.
     * @return {?}
     */
    CustomTree.prototype.clearChanges = /**
     * This method is used to cleans CRUD changes.
     * @return {?}
     */
    function () {
        this.logger.info('[ clearChanges ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.changes.clear();
        }
        catch (error) {
            this.logger.error('[ clearChanges ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ clearChanges ] END.');
    };
    /**
     * Returns the level of the selected node, result is Level1, Level2..Leveln
     */
    /**
     * Returns the level of the selected node, result is Level1, Level2..Leveln
     * @return {?}
     */
    CustomTree.prototype.getSelectedLevel = /**
     * Returns the level of the selected node, result is Level1, Level2..Leveln
     * @return {?}
     */
    function () {
        return this._selectedLevel;
    };
    /**
     * public method used to return tree Instance.
     */
    /**
     * public method used to return tree Instance.
     * @return {?}
     */
    CustomTree.prototype.getInstance = /**
     * public method used to return tree Instance.
     * @return {?}
     */
    function () {
        return this._instance;
    };
    /**
     * manageChangesArray
     *
     * <AUTHOR> M.Bouraoui
     *
     * This method is used to log the crud opertaions in the changes HashMap
     */
    /**
     * manageChangesArray
     *
     * <AUTHOR> M.Bouraoui
     *
     * This method is used to log the crud opertaions in the changes HashMap
     * @param {?} dataprovider
     * @param {?} item
     * @param {?=} operation
     * @return {?}
     */
    CustomTree.prototype.dataProviderCRUD = /**
     * manageChangesArray
     *
     * <AUTHOR> M.Bouraoui
     *
     * This method is used to log the crud opertaions in the changes HashMap
     * @param {?} dataprovider
     * @param {?} item
     * @param {?=} operation
     * @return {?}
     */
    function (dataprovider, item, operation) {
        if (operation === void 0) { operation = 0; }
        this.logger.info('[ dataProviderCRUD ] START.');
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var obj = null;
        try {
            if (item) {
                for (var index = 0; index < dataprovider.length; index++) {
                    obj = dataprovider[index];
                    for (var key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof (obj[key]) === 'object') {
                                this.dataProviderCRUD(obj[key], item, operation);
                            }
                            if (JSONReader.compareJSON(obj, item)) {
                            }
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ dataProviderCRUD ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ dataProviderCRUD ] END.');
    };
    /**
     * This method is used to diselect All selected items of tree.
     */
    /**
     * This method is used to diselect All selected items of tree.
     * @return {?}
     */
    CustomTree.prototype.diselectAll = /**
     * This method is used to diselect All selected items of tree.
     * @return {?}
     */
    function () {
        if (!this.addCheckbox) {
            ((/** @type {?} */ ($(this.elem.nativeElement.children[0].children[0])))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                if (node.isSelected()) {
                    $(node.span).closest('tr').removeClass("fancytree-selected");
                }
                if (node.isActive()) {
                    $(node.span).closest('tr').removeClass("fancytree-active");
                }
            }));
            this._selectedIndex = this._selectedLevel = this._selectedItem = null;
            this._selectedIndices = new Array();
        }
    };
    /**
     * This method will be used to update the tree state
     * when the given node is expanded.
     * @param node
     */
    /**
     * This method will be used to update the tree state
     * when the given node is expanded.
     * @private
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.updateStateOnExpand = /**
     * This method will be used to update the tree state
     * when the given node is expanded.
     * @private
     * @param {?} node
     * @return {?}
     */
    function (node) {
        var _a;
        try { // start of code used to update tree state capture
            // start of code used to update tree state capture
            /** @type {?} */
            var _children = new Array();
            if (node.getChildren()) {
                for (var index = 0; index < node.getChildren().length; index++) {
                    _children.push(node.getChildren()[index].key);
                }
            }
            /** @type {?} */
            var _exist = this._tree_state.join().indexOf(_children.join()) !== -1 ? true : false;
            if (this._tree_state.indexOf(_children[0]) === -1) {
                (_a = this._tree_state).splice.apply(_a, tslib_1.__spread([this._tree_state.indexOf(node.key) + 1, 0], _children));
            }
        }
        catch (e) {
            this.logger.error("updateStateOnExpand ", e);
        }
    };
    /**
     * @private
     * @param {?} targetItem
     * @return {?}
     */
    CustomTree.prototype.itemEditEndHandler = /**
     * @private
     * @param {?} targetItem
     * @return {?}
     */
    function (targetItem) {
        // Get the new value from the editor.
        /** @type {?} */
        var newVal = targetItem.title;
        /** @type {?} */
        var valid = true;
        this.log.info("Function [itemEditEndHandler] is called newVal: " + newVal);
        //Validate new entred value
        if (this.editableValidationFunction != null) {
            valid = this.editableValidationFunction(newVal);
        }
        // set new value if it's valid
        if (valid) {
            ((/** @type {?} */ (this.selectedItem)))[this.editableAttribute] = newVal;
            //save changes
            this.manageChangesArray(this.selectedItem, "U", this.editableAttribute, this.editableAdditionalAttribute);
        }
        this.editable = false;
        this.itemEditEnd.emit(targetItem);
    };
    /**
     * @private
     * @param {?} node
     * @param {?} levelIOrder
     * @return {?}
     */
    CustomTree.prototype.sortNodeBy = /**
     * @private
     * @param {?} node
     * @param {?} levelIOrder
     * @return {?}
     */
    function (node, levelIOrder) {
        try {
            /** @type {?} */
            var levelIOrderAttr = StringUtils.trim(levelIOrder);
            /** @type {?} */
            var indexOf_Eq = levelIOrderAttr.indexOf('=');
            /** @type {?} */
            var indexOf_Colon = levelIOrderAttr.indexOf(":");
            /** @type {?} */
            var sortDirection = levelIOrder.indexOf("DESC") != -1 ? "DESC" : "ASC";
            /** @type {?} */
            var attrName_1 = levelIOrder.indexOf("=") != -1 ? levelIOrder.split("=")[0] : levelIOrder;
            if (node) {
                if (sortDirection === "ASC") {
                    node.sortChildren((/**
                     * @param {?} n1
                     * @param {?} n2
                     * @return {?}
                     */
                    function (n1, n2) {
                        return n1.data[attrName_1] === n2.data[attrName_1] ? 0 : n1.data[attrName_1] > n2.data[attrName_1] ? 1 : -1;
                    }), false);
                }
                else {
                    node.sortChildren((/**
                     * @param {?} n1
                     * @param {?} n2
                     * @return {?}
                     */
                    function (n1, n2) {
                        return n1.data[attrName_1] === n2.data[attrName_1] ? 0 : n1.data[attrName_1] < n2.data[attrName_1] ? 1 : -1;
                    }), false);
                }
            }
        }
        catch (e) {
            this.log.error("sortNodeBy: ", e);
        }
    };
    /**
     * This method is used to navigate to bottom.
     * @param event
     * @param node
     */
    /**
     * This method is used to navigate to bottom.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.scrollToBottom = /**
     * This method is used to navigate to bottom.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    function (event, node) {
        this.logger.info('[ scrollToBottom ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            // make reference to list items.
            /** @type {?} */
            var tree = this.elem.nativeElement.children[0];
            // get list items height.
            /** @type {?} */
            var treeHeight = Number($(this.elem.nativeElement).height());
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = tree.children[itemIndex];
                // check existence of item.
                if (node) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = 27 /*Number($($(this.selectedItem.li.children[0])[0]).height())*/;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    var viewBottom = this.verticalScrollPosition + treeHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        this.verticalScrollPosition = itemBottom - treeHeight;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ scrollToBottom ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ scrollToBottom ] END.');
    };
    /**
     * This method is used to navigate to top.
     * @param event
     * @param node
     */
    /**
     * This method is used to navigate to top.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.scrollToTop = /**
     * This method is used to navigate to top.
     * @private
     * @param {?} event
     * @param {?} node
     * @return {?}
     */
    function (event, node) {
        this.logger.info('[ scrollToTop ] START.');
        /** @type {?} */
        var errorLocation = 0;
        try {
            // make reference to list items.
            /** @type {?} */
            var tree = this.elem.nativeElement.children[0];
            // get list items height.
            /** @type {?} */
            var treeHeight = Number($(this.elem.nativeElement).height());
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = tree.children[itemIndex];
                // check existence of item.
                if (node) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = 27 /*Number($($(this.selectedItem.li.children[0])[0]).height())*/;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    var viewBottom = this.verticalScrollPosition + treeHeight;
                    // update scroll bar position.
                    if (itemTop < this.verticalScrollPosition) {
                        this.verticalScrollPosition = itemTop;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('[ scrollToTop ] METHOD ERROR:', error, ' errorLocation = ', errorLocation);
        }
        this.logger.info('[ scrollToTop ] END.');
    };
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @param value
     */
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @param {?} parent
     * @return {?}
     */
    CustomTree.prototype.recursive = /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @private
     * @param {?} value
     * @param {?} parent
     * @return {?}
     */
    function (value, parent) {
        /** @type {?} */
        var errorLocation = 0;
        try {
            // test if value is an object.
            if (value.length === undefined) {
                value = [value];
            }
            errorLocation = 10;
            // loop to data
            for (var index = 0; index < value.length; index++) {
                /** @type {?} */
                var leaf = value[index];
                if (leaf.NAME) {
                    leaf['title'] = leaf.NAME;
                }
                else if (leaf.label || leaf.label == "") {
                    leaf['title'] = leaf.label;
                }
                /*
                 * if the tree leaf isBranch, that means this leaf has children
                * so put folder icon by default and this icon can be changed by
                * custom icon in the tree use (with icon function).
                * */
                if (leaf.isBranch) {
                    if (Boolean(leaf.isBranch)) {
                        leaf['folder'] = true;
                    }
                    else {
                        leaf['icon'] = "file";
                    }
                }
                else { // if isBranch attribute is not exist put file icon.
                    leaf['icon'] = "file";
                }
                // generate dynamic item key.
                leaf['key'] = 'lf_' + this.leaf_key;
                this.leaf_key++;
                // loop leaf keys.
                for (var key in leaf) {
                    if (typeof (leaf[key]) === 'object' && key != "parentData" && key != "children") {
                        if (leaf[key].length) { // if leaf has children.
                            leaf['children'] = leaf[key];
                            // leaf['folder'] = true;
                        }
                        else { // if leaf has one children.
                            leaf['children'] = [leaf[key]];
                            // leaf['folder'] = true;
                        }
                        // check if leaf is Branch.
                        // if (leaf.isBranch) {
                        //     leaf['folder'] = true;
                        // }
                        if (leaf.checked) {
                            // leaf["checkbox"] = leaf.checked;
                        }
                        this.recursive(leaf[key], leaf);
                    }
                    else {
                        // if (leaf.isBranch) {
                        //     leaf['folder'] = true;
                        // }
                    }
                    // if (leaf.isBranch) {
                    //     if (Boolean(leaf.isBranch)) {
                    //         leaf['folder'] = true;
                    //         if (leaf['children'] === undefined) {
                    //             leaf['children'] = [];
                    //         }
                    //     } else {
                    //
                    //     }
                    // } else {
                    //     if (leaf[key].children) {
                    //         leaf['folder'] = true;
                    //     }
                    // }
                    leaf['folder'] = true;
                    leaf['parentData'] = parent;
                }
            }
        }
        catch (error) {
            this.logger.error('[ recursive ] METHOD ERROR: ', error, ' errorLocation = ', errorLocation);
        }
        return value;
    };
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     */
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    CustomTree.prototype.getOpenedItems = /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var rtn = new Array();
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                if (node.isExpanded()) {
                    rtn.push(node);
                }
            }));
            return rtn;
        }
        catch (error) {
            console.error("getOpenedItems error: ", error);
        }
    };
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     */
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    CustomTree.prototype.getClosedItems = /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     * @private
     * @return {?}
     */
    function () {
        /** @type {?} */
        var rtn = new Array();
        try {
            ((/** @type {?} */ ($(this.treeContainer.nativeElement)))).fancytree('getTree').visit((/**
             * @param {?} node
             * @return {?}
             */
            function (node) {
                if ((node.expanded != undefined) && !node.isExpanded()) {
                    rtn.push(node);
                }
            }));
            return rtn;
        }
        catch (error) {
            console.error("getClosedItems error: ", error);
        }
    };
    /**
     * @private
     * @param {?} node
     * @return {?}
     */
    CustomTree.prototype.selectNode = /**
     * @private
     * @param {?} node
     * @return {?}
     */
    function (node) {
        if (node) {
            // node.setSelected(true);
            this._selectedItem = new CustomTreeItem(node, this.commonService);
            this._selectedLevel = 'Level' + node.getLevel();
        }
    };
    /**
     * @private
     * @return {?}
     */
    CustomTree.prototype.getScrollPosition = /**
     * @private
     * @return {?}
     */
    function () {
        return this.selectedIndex * 20;
    };
    CustomTree.seqAttribute = 'TREE_ITEM_LOCAL_SEQ';
    CustomTree.TREE_STR = 'Tree';
    CustomTree.LEVEL_1_STR = 'Level1';
    CustomTree.LEVEL_2_STR = 'Level2';
    CustomTree.LEVEL_3_STR = 'Level3';
    CustomTree.CRUD_OPERATION = "crud_operation";
    CustomTree.CRUD_DATA = "crud_data";
    CustomTree.decorators = [
        { type: Component, args: [{
                    selector: 'CustomTree',
                    template: "\n        <!--<div id=\"{{id}}\" selector=\"SwtCustomTree\" #treeContainer class=\"treeContainer {{ styleName }}\"></div>-->\n        <div class=\"customTreeWarapper\">\n            <table #treeContainer class=\"treeContainer {{ styleName }}\">\n                <tr style=\"height: 23px\">\n                    <td></td>\n                </tr>\n            </table>\n        </div>\n        <span #treeTipHolder></span>\n    ",
                    styles: ["\n        :host {\n            display: block;\n            /*background-color: #FFF;*/\n            /*overflow: auto;*/\n            width: 300px;\n            height: 450px;\n        }\n\n        .customTreeWarapper {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            background-color: #FFF;\n            border: 1px solid #B7BABC;\n            line-height: initial;\n        }\n\n        .customTreeWarapper table td, table th{\n            padding : 2px;\n        }\n\n\n\n        .treeContainer {\n            width: 100%;\n            padding-left: 3px;\n        }\n\n        .fancytree-container {\n            outline: none;\n            border: none;\n        }\n\n        .fancytree-container:focus {\n            outline: none;\n            border: none;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    CustomTree.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    CustomTree.propDecorators = {
        ITEM_CLICK: [{ type: Output, args: ['itemClick',] }],
        ITEM_ACTIVATE: [{ type: Output, args: ['itemActivate',] }],
        ITEM_DBCLICK: [{ type: Output, args: ['dbClick',] }],
        MOUSE_OUT: [{ type: Output, args: ['mouseOut',] }],
        itemOpen: [{ type: Output, args: ['itemOpen',] }],
        itemClose: [{ type: Output, args: ['itemClose',] }],
        MOUSE_OVER: [{ type: Output, args: ['mouseOver',] }],
        FOCUS_IN: [{ type: Output, args: ['focusIn',] }],
        FOCUS_OUT: [{ type: Output, args: ['focusOut',] }],
        id: [{ type: Input, args: ['id',] }],
        styleName: [{ type: Input, args: ['styleName ',] }],
        itemEditEnd: [{ type: Output, args: ["itemEditEnd",] }],
        treeContainer: [{ type: ViewChild, args: ['treeContainer',] }],
        treeTipHolder: [{ type: ViewChild, args: ['treeTipHolder',] }],
        level2Order: [{ type: Input }],
        level3Order: [{ type: Input }],
        level4Order: [{ type: Input }],
        allowMultipleSelection: [{ type: Input }],
        dataProvider: [{ type: Input }],
        width: [{ type: Input }],
        height: [{ type: Input }],
        hideIcons: [{ type: Input }],
        addCheckbox: [{ type: Input }],
        indeterminateCheckbox: [{ type: Input }],
        saveTreeStateBasedOn: [{ type: Input }],
        selectedItem: [{ type: Input }],
        doubleClickEnabled: [{ type: Input }],
        enabled: [{ type: Input }],
        level0Order: [{ type: Input }],
        level1Order: [{ type: Input }],
        dragMoveEnabled: [{ type: Input }],
        editable: [{ type: Input }]
    };
    return CustomTree;
}(Container));
export { CustomTree };
if (false) {
    /** @type {?} */
    CustomTree.seqAttribute;
    /** @type {?} */
    CustomTree.TREE_STR;
    /** @type {?} */
    CustomTree.LEVEL_1_STR;
    /** @type {?} */
    CustomTree.LEVEL_2_STR;
    /** @type {?} */
    CustomTree.LEVEL_3_STR;
    /** @type {?} */
    CustomTree.CRUD_OPERATION;
    /** @type {?} */
    CustomTree.CRUD_DATA;
    /** @type {?} */
    CustomTree.prototype.firstLoad;
    /** @type {?} */
    CustomTree.prototype.local_sequence;
    /** @type {?} */
    CustomTree.prototype.CRUD_OPERATION;
    /** @type {?} */
    CustomTree.prototype.CRUD_DATA;
    /** @type {?} */
    CustomTree.prototype.iconFunction;
    /** @type {?} */
    CustomTree.prototype.ITEM_CLICK;
    /** @type {?} */
    CustomTree.prototype.ITEM_ACTIVATE;
    /** @type {?} */
    CustomTree.prototype.ITEM_DBCLICK;
    /** @type {?} */
    CustomTree.prototype.MOUSE_OUT;
    /** @type {?} */
    CustomTree.prototype.itemOpen;
    /** @type {?} */
    CustomTree.prototype.itemClose;
    /** @type {?} */
    CustomTree.prototype.MOUSE_OVER;
    /** @type {?} */
    CustomTree.prototype.FOCUS_IN;
    /** @type {?} */
    CustomTree.prototype.FOCUS_OUT;
    /** @type {?} */
    CustomTree.prototype.id;
    /** @type {?} */
    CustomTree.prototype.styleName;
    /** @type {?} */
    CustomTree.prototype.buttonMode;
    /** @type {?} */
    CustomTree.prototype.labelFunction;
    /** @type {?} */
    CustomTree.prototype.dataTipFunction;
    /** @type {?} */
    CustomTree.prototype._tree_state;
    /** @type {?} */
    CustomTree.prototype.itemEditEnd;
    /**
     * private array to hold tree options.
     * private.
     * @type {?}
     * @protected
     */
    CustomTree.prototype.options;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.isItemSelected;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dbclick;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._tempIndex;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._selectedLevel;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._keyDownFlag;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level0OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level1OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level2OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level3OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level4OrderAttrName;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._globalFindStop;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableAttribute;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableAdditionalAttribute;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.editableValidationFunction;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.logger;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.leaf_key;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._instance;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._firstLoad;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.treeContainer;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.treeTipHolder;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.__OriginalDataProvider;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dataTip;
    /** @type {?} */
    CustomTree.prototype._hideIcons;
    /** @type {?} */
    CustomTree.prototype._addCheckbox;
    /** @type {?} */
    CustomTree.prototype._indeterminateCheckbox;
    /** @type {?} */
    CustomTree.prototype._saveTreeStateBasedOn;
    /** @type {?} */
    CustomTree.prototype._changes;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._hideFunction;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level2Order;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level3Order;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._level4Order;
    /**
     * private.
     * @type {?}
     * @protected
     */
    CustomTree.prototype._allowMultipleSelection;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._selectedIndices;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._width;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._height;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._doubleClickEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._selectable;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._level1Order;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._level0Order;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._iconWidth;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._iconSpacing;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._labelSpacing;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._levelOfs;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._dragMoveEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._dragEnabled;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._openItems;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._closeItems;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._verticalScrollPosition;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._showRoot;
    /**
     * @type {?}
     * @private
     */
    CustomTree.prototype._editedItemPosition;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.elem;
    /**
     * @type {?}
     * @protected
     */
    CustomTree.prototype.commonService;
}
var CustomTreeItem = /** @class */ (function (_super) {
    tslib_1.__extends(CustomTreeItem, _super);
    /**
     * CustomTreeItem constructor.
     * @param itemElement
     * @param commonServ
     */
    function CustomTreeItem(itemElement, commonServ) {
        var _this = _super.call(this, itemElement.span, commonServ) || this;
        _this.itemElement = itemElement;
        _this.commonServ = commonServ;
        // private variable to hold item parent.
        _this.parent = null;
        // private variable will be true if the item is expanded.
        _this._expanded = false;
        // private variable to contains all item children.
        _this._children = [];
        // private variable will be true if this item is editable.
        _this._editable = false;
        // private variable to hold customToolTip.
        _this._customToolTip = null;
        // This varible will be true when we editing the current item.
        _this._editMode = false;
        // private variable will be true if the item is selected.
        _this._selected = false;
        // private variable to specify if this item has folder icon or not.
        _this._folder = true;
        _this._visible = true;
        // private variable to hold tree item icon.
        _this._icon = "";
        // private variable to hold tree item title.
        _this._title = "";
        // private variable to hold tree item expander.
        _this._expander = "";
        // private variable to hold tree item key.
        _this._key = "";
        // private variable to set item tooltip.
        _this._toolTip = "";
        // private variable to set item height.
        _this._height = "";
        // private variable to set tree item level.
        _this._level = "";
        try {
            _this.node = itemElement;
            _this.data = itemElement.data;
            _this._title = itemElement.title;
            _this._icon = itemElement.icon;
            _this._key = itemElement.key;
            _this._folder = itemElement.folder;
            _this._expanded = itemElement.expanded;
            _this._selected = itemElement.selected;
            _this._toolTip = itemElement.tooltip;
            if (itemElement.parent)
                _this.parent = itemElement.parent;
            _this._level = "Level" + _this.node.getLevel();
            if (itemElement.children) {
                for (var index = 0; index < itemElement.children.length; index++) {
                    _this._children.push(new CustomTreeItem(itemElement.children[index], commonServ));
                }
            }
            /**
             * Bind data in tree item.
             */
            if (_this.data) {
                for (var attribute in _this.data) {
                    if (_this.data.hasOwnProperty(attribute)) {
                        // this[attribute] = this._data[attribute];
                        if (isNaN(Number(attribute))) {
                            // CustomTreeItem[attribute] = this._data[attribute];
                            _this.bindAttribute(attribute, _this.data[attribute]);
                        }
                    }
                }
            }
        }
        catch (e) {
            _this.log.error('CustomTreeItem constructor: ', e);
        }
        return _this;
    }
    Object.defineProperty(CustomTreeItem.prototype, "editMode", {
        get: /**
         * @return {?}
         */
        function () {
            return this._editMode;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (this._editMode === value) {
                return;
            }
            this._editMode = value;
            this._editMode ? this.triggerEdit() : this.stopEdit();
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "selected", {
        /**
         * This setter to make item selected.
         * @param value
         */
        set: /**
         * This setter to make item selected.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selected = value;
            // this.itemElement.setSelected(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "labelObject", {
        get: /**
         * @return {?}
         */
        function () {
            return this._labelObject;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._labelObject = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "folder", {
        get: /**
         * @return {?}
         */
        function () {
            return this._folder;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._folder = value;
            this.node.folder = this._folder;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "visible", {
        get: /**
         * @return {?}
         */
        function () {
            return this._visible;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._visible = value;
            this.itemVisibilityState(this._visible);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "icon", {
        /**
         * icon getter
         */
        get: /**
         * icon getter
         * @return {?}
         */
        function () {
            return this._icon;
        },
        /**
         * icon setter.
         * @param value
         */
        set: /**
         * icon setter.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof value === "string") {
                /** @type {?} */
                var $span = $(this.node.span);
                /** @type {?} */
                var icon = $span.find('> span.' + this._icon);
                icon.removeClass(this._icon).addClass(value);
                this._icon = value;
            }
            this.node.icon = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "title", {
        /**
         * title getter.
         */
        get: /**
         * title getter.
         * @return {?}
         */
        function () {
            return this._title;
        },
        /**
         * title setter.
         * @param value
         */
        set: /**
         * title setter.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._title = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "expander", {
        /**
         * expander getter.
         */
        get: /**
         * expander getter.
         * @return {?}
         */
        function () {
            return this._expander;
        },
        /**
         * expander setter.
         * @param value
         */
        set: /**
         * expander setter.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._expander = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "key", {
        /**
         * This method is used to get the key of
         * current item.
         */
        get: /**
         * This method is used to get the key of
         * current item.
         * @return {?}
         */
        function () {
            return this._key;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "toolTip", {
        /**
         * toolTip getter.
         */
        get: /**
         * toolTip getter.
         * @return {?}
         */
        function () {
            return this._toolTip;
        },
        /**
         * toolTip setter.
         * @param value
         */
        set: /**
         * toolTip setter.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._toolTip = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "height", {
        /**
         * height getter.
         */
        get: /**
         * height getter.
         * @return {?}
         */
        function () {
            return this._height;
        },
        /**
         * height setter.
         * @param value
         */
        set: /**
         * height setter.
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._height = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(CustomTreeItem.prototype, "level", {
        /**
         * level getter.
         */
        get: /**
         * level getter.
         * @return {?}
         */
        function () {
            return this._level;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._level = value;
            this.itemElement.level = this.getNumberFrom(value);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    CustomTreeItem.prototype.getNode = /**
     * @return {?}
     */
    function () {
        return this.itemElement;
    };
    /**
     * return index of the current item
     * relative to its parent
     */
    /**
     * return index of the current item
     * relative to its parent
     * @return {?}
     */
    CustomTreeItem.prototype.childIndex = /**
     * return index of the current item
     * relative to its parent
     * @return {?}
     */
    function () {
        /** @type {?} */
        var index = 0;
        try {
            /** @type {?} */
            var children = this.getParent().getChildren();
            for (var i = 0; i < children.length; i++) {
                if (children[i].key === this.key) {
                    index = i;
                    break;
                }
            }
        }
        catch (e) {
            this.log.error(e);
        }
        return index;
    };
    /**
     * This method is used to get parent item
     * of the current tree item.
     */
    /**
     * This method is used to get parent item
     * of the current tree item.
     * @return {?}
     */
    CustomTreeItem.prototype.getParent = /**
     * This method is used to get parent item
     * of the current tree item.
     * @return {?}
     */
    function () {
        this.parent = new CustomTreeItem(this.itemElement.parent, this.commonServ);
        return this.parent;
    };
    /**
     * This method is used to get children of current tree item.
     */
    /**
     * This method is used to get children of current tree item.
     * @return {?}
     */
    CustomTreeItem.prototype.getChildren = /**
     * This method is used to get children of current tree item.
     * @return {?}
     */
    function () {
        return this._children;
    };
    /**
     * return true if the item is expanded.
     */
    /**
     * return true if the item is expanded.
     * @return {?}
     */
    CustomTreeItem.prototype.isExpanded = /**
     * return true if the item is expanded.
     * @return {?}
     */
    function () {
        return this._expanded;
    };
    /**
     * return true if the item is editable.
     */
    /**
     * return true if the item is editable.
     * @return {?}
     */
    CustomTreeItem.prototype.isEditable = /**
     * return true if the item is editable.
     * @return {?}
     */
    function () {
        return this._editable;
    };
    /**
     * return true if the item is selected.
     */
    /**
     * return true if the item is selected.
     * @return {?}
     */
    CustomTreeItem.prototype.isSelected = /**
     * return true if the item is selected.
     * @return {?}
     */
    function () {
        return this._selected;
    };
    /**
     * This method is used to set data to the current
     * item.
     * @param prop
     * @param value
     */
    /**
     * This method is used to set data to the current
     * item.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    CustomTreeItem.prototype.setData = /**
     * This method is used to set data to the current
     * item.
     * @param {?} prop
     * @param {?} value
     * @return {?}
     */
    function (prop, value) {
        this.data[prop] = value;
    };
    /**
     * This method with optional property is used to get the data
     * if prop is empty else return the given property.
     * @param prop?
     */
    /**
     * This method with optional property is used to get the data
     * if prop is empty else return the given property.
     * @param {?=} prop
     * @return {?}
     */
    CustomTreeItem.prototype.getData = /**
     * This method with optional property is used to get the data
     * if prop is empty else return the given property.
     * @param {?=} prop
     * @return {?}
     */
    function (prop) {
        if (prop === void 0) { prop = ""; }
        if (prop && prop !== "") {
            return this.data[prop];
        }
        return this.data;
    };
    /**
     * This method is used to expand the current
     * item.
     */
    /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    CustomTreeItem.prototype.expand = /**
     * This method is used to expand the current
     * item.
     * @return {?}
     */
    function () {
        try {
            this.itemElement.setExpanded(true);
        }
        catch (error) {
            this.log.error("expand - error : ", error);
        }
    };
    /**
     * This method is used to collapse the current item.
     */
    /**
     * This method is used to collapse the current item.
     * @return {?}
     */
    CustomTreeItem.prototype.collapse = /**
     * This method is used to collapse the current item.
     * @return {?}
     */
    function () {
        try {
            this.itemElement.setExpanded(false);
        }
        catch (error) {
            this.log.error("collapse - error : ", error);
        }
    };
    /**
     * This method is used to add a sub item to the current item.
     * @param item
     */
    /**
     * This method is used to add a sub item to the current item.
     * @param {?} item
     * @return {?}
     */
    CustomTreeItem.prototype.appendItem = /**
     * This method is used to add a sub item to the current item.
     * @param {?} item
     * @return {?}
     */
    function (item) {
        try {
            this.folder = true;
            this.node.addChildren(item);
            this.node.render(true);
        }
        catch (error) {
            this.log.error("appendItem - error : ", error);
        }
    };
    /**
     * This method is used to remove the current item.
     */
    /**
     * This method is used to remove the current item.
     * @return {?}
     */
    CustomTreeItem.prototype.remove = /**
     * This method is used to remove the current item.
     * @return {?}
     */
    function () {
        try {
            if (!this._editMode) {
                this.itemElement.remove();
            }
            else {
                this.stopEdit();
                this.itemElement.remove();
            }
        }
        catch (error) {
            this.log.error("remove - error : ", error);
        }
    };
    /**
     * This method trigger the edit mode of the
     * current CustomTreeItem.
     */
    /**
     * This method trigger the edit mode of the
     * current CustomTreeItem.
     * @return {?}
     */
    CustomTreeItem.prototype.triggerEdit = /**
     * This method trigger the edit mode of the
     * current CustomTreeItem.
     * @return {?}
     */
    function () {
        this.itemElement.editStart();
        this._editMode = true;
    };
    /**
     * This method stop the editing mode
     * of the current item.
     */
    /**
     * This method stop the editing mode
     * of the current item.
     * @param {?=} applyChanges
     * @return {?}
     */
    CustomTreeItem.prototype.stopEdit = /**
     * This method stop the editing mode
     * of the current item.
     * @param {?=} applyChanges
     * @return {?}
     */
    function (applyChanges) {
        if (applyChanges === void 0) { applyChanges = false; }
        this.itemElement.editEnd(applyChanges);
        this._editMode = false;
    };
    /**
     * This method is used to get the root of the current item.
     */
    /**
     * This method is used to get the root of the current item.
     * @return {?}
     */
    CustomTreeItem.prototype.getRoot = /**
     * This method is used to get the root of the current item.
     * @return {?}
     */
    function () {
        try {
            return new CustomTreeItem(this.node.getRootNode(), this.commonServ);
        }
        catch (error) {
            this.log.error("getRoot - error : ", error);
        }
    };
    /**
     * This method is used to detect if the current item
     * has a children or not
     * it will return true | false | undefined.
     */
    /**
     * This method is used to detect if the current item
     * has a children or not
     * it will return true | false | undefined.
     * @return {?}
     */
    CustomTreeItem.prototype.hasChildren = /**
     * This method is used to detect if the current item
     * has a children or not
     * it will return true | false | undefined.
     * @return {?}
     */
    function () {
        try {
            return this.node.hasChildren();
        }
        catch (e) {
            this.log.error("hasChildren - error: ", e);
        }
    };
    /**
     * @param {?} tooltip
     * @return {?}
     */
    CustomTreeItem.prototype.setCustomTooltip = /**
     * @param {?} tooltip
     * @return {?}
     */
    function (tooltip) {
        try {
            // $(this.node.span).append("<AdvancedToolTip></AdvancedToolTip>")
            // this.addChild(AdvancedToolTip);
            /** @type {?} */
            var compRef = this.commonServ.resolver.resolveComponentFactory(AdvancedToolTip).create(this.commonServ.injector);
            /** @type {?} */
            var child = compRef.instance.addChild(tooltip);
            // attach component to current application.
            this.commonServ.applicationRef.attachView(compRef.hostView);
            // get component root node (component selector).
            /** @type {?} */
            var selector = (/** @type {?} */ (((/** @type {?} */ (compRef.hostView))).rootNodes[0]));
            // attach component root node to DOM.
            $(this.node.span).append(selector);
            this._customToolTip = compRef.instance;
            this._customToolTip.child = child;
            return compRef.instance;
        }
        catch (error) {
            this.log.error("setCustomTooltip - error: ", error);
        }
    };
    /**
     * @return {?}
     */
    CustomTreeItem.prototype.localName = /**
     * @return {?}
     */
    function () {
        return this._level;
    };
    /**
     * @return {?}
     */
    CustomTreeItem.prototype.getCustomToolTip = /**
     * @return {?}
     */
    function () {
        return this._customToolTip;
    };
    /**
     * @param {?} visibility
     * @return {?}
     */
    CustomTreeItem.prototype.setVisible = /**
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        this._visible = visibility;
        this.itemVisibilityState(this._visible);
    };
    /**
     * @private
     * @param {?} visibility
     * @return {?}
     */
    CustomTreeItem.prototype.itemVisibilityState = /**
     * @private
     * @param {?} visibility
     * @return {?}
     */
    function (visibility) {
        // setTimeout(() => {
        //     if (!visibility) {
        //         $(this.itemElement.span).closest('tr').addClass('fancytree-helper-hidden');
        //     } else {
        //         $(this.itemElement.span).closest('tr').removeClass('fancytree-helper-hidden');
        //     }
        // }, 0);
    };
    CustomTreeItem.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    CustomTreeItem.ctorParameters = function () { return [
        { type: undefined },
        { type: CommonService }
    ]; };
    return CustomTreeItem;
}(UIComponent));
export { CustomTreeItem };
if (false) {
    /** @type {?} */
    CustomTreeItem.prototype.data;
    /** @type {?} */
    CustomTreeItem.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.node;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._expanded;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._children;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._customToolTip;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._toolTipchild;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._editMode;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._selected;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._labelObject;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._folder;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._visible;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._icon;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._title;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._expander;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._key;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._toolTip;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._height;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype._level;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.itemElement;
    /**
     * @type {?}
     * @private
     */
    CustomTreeItem.prototype.commonServ;
}
var TreeLabel = /** @class */ (function (_super) {
    tslib_1.__extends(TreeLabel, _super);
    function TreeLabel(lblelement, commonService) {
        var _this = _super.call(this, lblelement, commonService) || this;
        _this.lblelement = lblelement;
        _this.commonService = commonService;
        return _this;
    }
    Object.defineProperty(TreeLabel.prototype, "item", {
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._item = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    TreeLabel.prototype.getParentItem = /**
     * @return {?}
     */
    function () {
        return this._item;
    };
    return TreeLabel;
}(UIComponent));
export { TreeLabel };
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype._item;
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype.lblelement;
    /**
     * @type {?}
     * @private
     */
    TreeLabel.prototype.commonService;
}
var TreeIcon = /** @class */ (function (_super) {
    tslib_1.__extends(TreeIcon, _super);
    function TreeIcon(iconElement, commonService) {
        var _this = _super.call(this, iconElement, commonService) || this;
        _this.iconElement = iconElement;
        _this.commonService = commonService;
        return _this;
    }
    return TreeIcon;
}(UIComponent));
export { TreeIcon };
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeIcon.prototype.iconElement;
    /**
     * @type {?}
     * @private
     */
    TreeIcon.prototype.commonService;
}
var TreeExpander = /** @class */ (function (_super) {
    tslib_1.__extends(TreeExpander, _super);
    function TreeExpander(expanderElement, commonService) {
        var _this = _super.call(this, expanderElement, commonService) || this;
        _this.expanderElement = expanderElement;
        _this.commonService = commonService;
        return _this;
    }
    return TreeExpander;
}(UIComponent));
export { TreeExpander };
if (false) {
    /**
     * @type {?}
     * @private
     */
    TreeExpander.prototype.expanderElement;
    /**
     * @type {?}
     * @private
     */
    TreeExpander.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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