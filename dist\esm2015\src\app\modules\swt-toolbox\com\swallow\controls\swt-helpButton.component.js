/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Input, ElementRef } from '@angular/core';
import { SwtUtil } from '../utils/swt-util.service';
import { ExternalInterface } from '../utils/external-interface.service';
import { CommonUtil } from '../utils/common-util.service';
import { SwtButton } from "./swt-button.component";
import { CommonService } from "../utils/common.service";
export class SwtHelpButton extends SwtButton {
    /**
     * constructor
     * @param {?} eleme
     * @param {?} commonService_
     */
    constructor(eleme, commonService_) {
        super(eleme, commonService_);
        this.eleme = eleme;
        this.commonService_ = commonService_;
        $($(this.eleme.nativeElement)[0]).attr('selector', 'SwtHelpButton');
    }
    /**
     * ngOnInit
     * @return {?}
     */
    ngOnInit() {
        if (!this.toolTip) {
            this.toolTip = SwtUtil.getPredictMessage('button.help');
        }
    }
    /**
     * @return {?}
     */
    doHelp() {
        // Variable to hold error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            errorLocation = 10;
            //Get the user language locale path
            /** @type {?} */
            var urlLanguage = SwtUtil.getBaseURL() + "logon!getUserLanguage.do";
            errorLocation = 20;
            // get user language
            /** @type {?} */
            var userLang = ExternalInterface.call('getUserLanguageId', urlLanguage);
            errorLocation = 30;
            // If Module Id is null
            if (this.moduleId == null) {
                // Get module ID
                //moduleId = String(parentApplication.getCurrentModuleId()).toLowerCase();
                errorLocation = 40;
                this.moduleId = CommonUtil.getCurrentModuleId().toLowerCase();
            }
            //set http url
            errorLocation = 50;
            ExternalInterface.call("loadHelpPage", "about!helpPage.do?help_id=" + this.helpFile + "&module_id=" + this.moduleId + "&language_id=" + userLang.split("/")[1]);
        }
        catch (error) {
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, "SwtHelpButton", "doHelp", errorLocation);
        }
    }
}
SwtHelpButton.decorators = [
    { type: Component, args: [{
                selector: 'SwtHelpButton',
                template: ` 
         <div   
                #swtbutton  
                (click)    ="doHelp()" 
                class="helpIcon inline-field">
                <span class="truncate buttonLabel" ></span>
         </div>
    `,
                styles: [`
    
             :host{
                 outline:none;
             }
            .helpIcon    {
                height: 16px; 
                width: 16px;                     
                border: 0;
                overflow: hidden;
                color: transparent;
                background:transparent url('assets/images/swtHelp.png') no-repeat;                  
                border-radius: 15px;   
                cursor: pointer;
            }            
             .helpIcon:hover{
                 background:transparent url('assets/images/swtHelpOver.png') no-repeat;
              }
             .helpIcon:focus {
                outline: 0px auto -webkit-focus-ring-color;
                background:transparent url('assets/images/swtHelpDown.png') no-repeat;
             }
            .helpIconDisabled {
                height: 16px; 
                width: 16px;                     
                border: 0;
                overflow: hidden;
                color: transparent;
                background:transparent url('assets/images/swtHelp.png') no-repeat;                  
                border-radius: 15px;   
                opacity: 0.6;
                pointer-events: none;    
                cursor: default;      
             }      
            /*========================================= Styling Tootltip  ======================================*/
            
             :host >>> .tooltip-inner {
                 background-color: rgba(250, 250, 230, 1);
                 font-size:9px;
                 color: #0b333c;
                 border: 1px solid #0b333c;
                 box-shadow: 1px 2px 5px #888888;
             }
         `]
            }] }
];
/** @nocollapse */
SwtHelpButton.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtHelpButton.propDecorators = {
    toolTip: [{ type: Input, args: ['toolTip',] }],
    helpFile: [{ type: Input, args: ['helpFile',] }]
};
if (false) {
    /** @type {?} */
    SwtHelpButton.prototype.moduleId;
    /** @type {?} */
    SwtHelpButton.prototype.toolTip;
    /** @type {?} */
    SwtHelpButton.prototype.helpFile;
    /**
     * @type {?}
     * @private
     */
    SwtHelpButton.prototype.eleme;
    /**
     * @type {?}
     * @private
     */
    SwtHelpButton.prototype.commonService_;
}
//# sourceMappingURL=data:application/json;base64,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