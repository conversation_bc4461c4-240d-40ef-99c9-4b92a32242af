import { Alert } from "./alert.component";
import { CommonService } from "./common.service";
export declare class SwtAlert extends Alert {
    private commonService;
    private alert;
    private alertErrorImage;
    private alertInfoImage;
    private alertQuestionImage;
    private alertWarningImage;
    constructor(commonService: CommonService);
    /**
     * info
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    info(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
    /**
     * info
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    question(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
    /**
     * error
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the error Alert.
     */
    error(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
    /**
     * warning
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    warning(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
    /**
     * confirm
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    confirm(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
    /**
     * invalid
     *
     * @return Alert - AlertInstance
     *
     *  This function is used to return the invalid Alert.
     */
    invalid(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, defaultButtonFlag?: number): Alert;
}
