/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
var SwtHelpWindow = /** @class */ (function () {
    function SwtHelpWindow() {
    }
    /**
     * @param {?} url
     * @return {?}
     */
    SwtHelpWindow.open = /**
     * @param {?} url
     * @return {?}
     */
    function (url) {
        window.open(url, this.content, "toolbar=" + this._toolbar + ",scrollbars=" + this._scrollbars + "," +
            "resizable=" + this._resizable + ",top=" + this.top + "," +
            "left=" + this.left + ",width=" + this.width + "," +
            "height=" + this.height);
    };
    /*
    public static close() {
     //   this.helpwindow.close();
    }
    */
    /*
      public static close() {
       //   this.helpwindow.close();
      }
      */
    /**
     * @param {?} value
     * @return {?}
     */
    SwtHelpWindow.toolbar = /*
      public static close() {
       //   this.helpwindow.close();
      }
      */
    /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (value) {
            this._toolbar = "yes";
        }
        else {
            this._toolbar = "no";
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtHelpWindow.scrollbars = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (value) {
            this._scrollbars = "yes";
        }
        else {
            this._scrollbars = "no";
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtHelpWindow.resizable = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        if (value) {
            this._resizable = "yes";
        }
        else {
            this._resizable = "no";
        }
    };
    SwtHelpWindow._toolbar = "yes";
    SwtHelpWindow._scrollbars = "yes";
    SwtHelpWindow._resizable = "yes";
    SwtHelpWindow.width = 500;
    SwtHelpWindow.height = 400;
    SwtHelpWindow.top = 50;
    SwtHelpWindow.left = 50;
    SwtHelpWindow.content = "_blank";
    SwtHelpWindow.BLANK = "_blank";
    SwtHelpWindow.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    SwtHelpWindow.ctorParameters = function () { return []; };
    return SwtHelpWindow;
}());
export { SwtHelpWindow };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._toolbar;
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._scrollbars;
    /**
     * @type {?}
     * @private
     */
    SwtHelpWindow._resizable;
    /** @type {?} */
    SwtHelpWindow.width;
    /** @type {?} */
    SwtHelpWindow.height;
    /** @type {?} */
    SwtHelpWindow.top;
    /** @type {?} */
    SwtHelpWindow.left;
    /** @type {?} */
    SwtHelpWindow.content;
    /** @type {?} */
    SwtHelpWindow.BLANK;
}
//# sourceMappingURL=data:application/json;base64,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