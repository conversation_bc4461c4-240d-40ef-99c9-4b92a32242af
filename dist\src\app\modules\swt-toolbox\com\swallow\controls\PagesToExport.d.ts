import { OnInit, ElementRef } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { CommonService } from "../utils/common.service";
import { SwtButton } from "./swt-button.component";
import { SwtRadioItem } from './swt-radioItem.component';
import { SwtRadioButtonGroup } from './swt-radioButtonGroup.component';
export declare class SwtPagesToExport extends SwtModule implements OnInit {
    private element;
    private common;
    /***Buttons********/
    okButton: SwtButton;
    cancelButton: SwtButton;
    exportButtonGroup: SwtRadioButtonGroup;
    radioC: SwtRadioItem;
    radioA: SwtRadioItem;
    private _exportCancelFunction;
    private _exportFunction;
    private win;
    pagesToExport: string;
    constructor(element: ElementRef, common: CommonService);
    ngOnInit(): void;
    initData(): void;
    popupClosed(): void;
    defaultContentFunction(): void;
    exportType(): void;
    /**
    *
    */
    onexportFunction: Function;
    /**
    *
    */
    exportCancelFunction: Function;
    /**
     * This method is used to show report Progress.
     */
    show(parent: any): void;
    /**
     *  This method is used to hide report Progress.
     */
    hide(parent: any): void;
    /**
     * Export is canceled
     */
    ExportCanceled(event: any): void;
}
