/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef } from '@angular/core';
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
import { Logger } from "../../logging/logger.service";
import { genericEvent } from "../../events/swt-events.module";
import { Types } from "./types";
var LinkItemRander = /** @class */ (function (_super) {
    tslib_1.__extends(LinkItemRander, _super);
    function LinkItemRander(linkelement, common) {
        var _this = _super.call(this, linkelement.nativeElement, common) || this;
        _this.linkelement = linkelement;
        _this.common = common;
        _this.text = "";
        _this.color = "";
        _this.type = Types.LINK;
        _this.id = 0;
        _this.log = new Logger("LinkItemRander", common.httpclient);
        return _this;
    }
    /**
     * @return {?}
     */
    LinkItemRander.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    LinkItemRander.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
    };
    /**
     * This method will be fired on link item render
     * click.
     * @param event
     */
    /**
     * This method will be fired on link item render
     * click.
     * @param {?} event
     * @return {?}
     */
    LinkItemRander.prototype.linkItemRenderClickHandler = /**
     * This method will be fired on link item render
     * click.
     * @param {?} event
     * @return {?}
     */
    function (event) {
        var _this = this;
        try {
            setTimeout((/**
             * @return {?}
             */
            function () {
                // console.log("linkItemRenderClickHandler = ", this.eventlist);
                if (_this.eventlist[genericEvent.CLICK]) {
                    _this.eventlist[genericEvent.CLICK].call({ target: _this, event: event });
                }
            }));
        }
        catch (error) {
            this.log.error("linkItemRenderClickHandler - error: ", error);
        }
    };
    LinkItemRander.decorators = [
        { type: Component, args: [{
                    selector: 'LinkItemRander',
                    template: "\n        <span class=\"link\" [id]=\"id\" (click)=\"linkItemRenderClickHandler($event)\">\n            {{ text }}\n        </span>\n    ",
                    styles: ["\n        :host {\n            display: block;\n            width: 100%;\n        }\n\n        .link {\n            display: block;\n            width: 100%;\n            text-decoration: underline;\n            color: #52AEFB;\n            text-align: right;\n            padding: 0 5px;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            white-space: nowrap;\n        }\n        .link:hover {\n            cursor: pointer;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    LinkItemRander.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    return LinkItemRander;
}(UIComponent));
export { LinkItemRander };
if (false) {
    /** @type {?} */
    LinkItemRander.prototype.text;
    /** @type {?} */
    LinkItemRander.prototype.color;
    /** @type {?} */
    LinkItemRander.prototype.type;
    /** @type {?} */
    LinkItemRander.prototype.id;
    /**
     * @type {?}
     * @private
     */
    LinkItemRander.prototype.linkelement;
    /**
     * @type {?}
     * @private
     */
    LinkItemRander.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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