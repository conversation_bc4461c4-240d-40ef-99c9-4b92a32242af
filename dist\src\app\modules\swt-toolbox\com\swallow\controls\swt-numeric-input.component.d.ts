import { OnInit } from '@angular/core';
import { SwtTextInput } from "./swt-text-input.component";
export declare class SwtNumericInput extends SwtTextInput implements OnInit {
    private _maximum;
    private _minimum;
    private RESTRICT_CHARS;
    maximum: any;
    minimum: any;
    ngOnInit(): void;
    /**
      * Validate the value, whenever its changed
      *
      * @param event: Event
     */
    private onValueChange;
    private checkValidValue;
}
