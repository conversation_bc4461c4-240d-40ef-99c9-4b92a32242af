import { ElementRef } from '@angular/core';
import { SwtModule } from "../controls/swt-module.component";
import { CommonService } from "../utils/common.service";
import { HTTPComms } from "../communication/httpcomms.service";
import { CancelExportEvent } from "../events/cancel-export-event.service";
import { SwtButton } from "./swt-button.component";
export declare class ExportInProgress extends SwtModule {
    private element;
    private common;
    labelValue: string;
    exportToken: string;
    exportTimer: any;
    inputData: HTTPComms;
    private actionPath;
    private requestParams;
    private SwtAlert;
    private logger;
    win: any;
    private _exportCancelFunction;
    cancelButton: SwtButton;
    constructor(element: ElementRef, common: CommonService);
    initData(): void;
    defaultContentFunction(): void;
    /**
    *
    */
    exportCancelFunction: Function;
    /**
     * This method is used to show report Progress.
     */
    show(parent: any): void;
    /**
     *  This method is used to hide report Progress.
     */
    hide(parent: any): void;
    cancelReport(): void;
    /**
     *This function is called whenever click on the cancel button in Progress bar window
     *@param event: MouseEvent
     */
    cancelExport(event: CancelExportEvent): void;
    initTimer(): void;
    /**
     * Export is canceled
     */
    ExportCanceled(event: any): void;
    private getTimer;
}
