/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';
var DropDownList = /** @class */ (function () {
    function DropDownList(elementRef) {
        this.elementRef = elementRef;
        this.onItemClick = new EventEmitter();
        this.onItemChange = new EventEmitter();
        this.onClickOutSide = new EventEmitter();
        this.onItemNavigation = new EventEmitter();
        this.highlightIndex = -1;
        this.dropDownContainer = null;
        this.dropDownItems = null;
        this.virtualScroll = null;
        this.scrollHandler = null;
        this.highlightedItem = null;
        this.pointer = 0;
        this.previousPointer = 0;
        this.isFilterActive = false;
        this.originalDataSource = [];
        this.firstLoad = true;
        this._activeItemIndex = -1;
        this._selectedIndex = -1;
        this._selectedItem = null;
        this._selectedLabel = null;
        this._selectedValue = null;
        this._dataLabel = 'content';
        this._dataSource = [];
    }
    Object.defineProperty(DropDownList.prototype, "activeItemIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._activeItemIndex;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "activeItem", {
        get: /**
         * @return {?}
         */
        function () {
            return this.dataSource[this._activeItemIndex];
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "selectedIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedIndex;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (value > -1 && value < this.dataSource.length) {
                this._activeItemIndex = this._selectedIndex = value;
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this.scrollToIndex(value);
                }), 0);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "selectedItem", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedItem;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selectedItem = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "selectedLabel", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedLabel;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selectedLabel = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "selectedValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedValue;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._selectedValue = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "dataLabel", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataLabel;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (!value) {
                return;
            }
            this._dataLabel = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(DropDownList.prototype, "dataSource", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataSource;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            if (!value && !value.length) {
                return;
            }
            this._dataSource = value;
            this.pointer = 0;
            if ($(this.dropDownItems)[0]) {
                this.applyDataSource(value);
            }
            else {
                setTimeout((/**
                 * @return {?}
                 */
                function () {
                    _this.applyDataSource(value);
                }), 0);
            }
            if (!this.isFilterActive && this.firstLoad) {
                // store copy of data source.
                this.originalDataSource = tslib_1.__spread(value);
                this.firstLoad = false;
            }
            else {
                this.highlightIndex = -1;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    DropDownList.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.dropDownContainer = $(this.elementRef.nativeElement).find('div.swt-dropdown-container');
        this.dropDownItems = $(this.elementRef.nativeElement).find('div.swt-dropdown-items');
        this.virtualScroll = $(this.elementRef.nativeElement).find('div.swt-virtual-scroll');
        this.scrollHandler = $(this.elementRef.nativeElement).find('div.swt-dropdown-scrollhandler');
        $(this.virtualScroll).scroll((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            _this.pointer = Math.ceil($(_this.virtualScroll).scrollTop() / 22);
            _this.validateNow();
        }));
        // bind mouse wheel event listener to native element.
        this.elementRef.nativeElement.addEventListener('mousewheel', this.onMouseWheelMotion.bind(this));
    };
    /**
     * @param {?} event
     * @return {?}
     */
    DropDownList.prototype.clickoutEventHandler = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // if user clicked outside the close dropdown.
        if (!this.elementRef.nativeElement.contains(event.target)) {
            this.onClickOutSide.emit(event);
        }
        else {
            if ($(event.target).is('div.swt-dropdown-item')) {
                this._selectedIndex = !isNaN(Number($(event.target).attr('index'))) ? Number($(event.target).attr('index')) : -1;
                this._selectedItem = this._selectedIndex !== -1 ? this.originalDataSource[this._selectedIndex] : null;
                this._selectedValue = this.selectedItem !== null ? this.selectedItem.value : null;
                this._selectedLabel = this.selectedItem !== null ? this.selectedItem[this.dataLabel] : null;
                $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
                $(this.dropDownItems).find("div#" + event.target.id).addClass('swt-selected-dropdown-item');
                this.highlightIndex = Number(event.target.id);
                event.selectedItem = this.selectedItem;
                this.onItemClick.emit(event);
            }
        }
    };
    /**
     * @return {?}
     */
    DropDownList.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        try {
            $((/** @type {?} */ (window))).off();
            $(this.dropDownContainer).off();
            $(this.dropDownItems).off();
            $(this.virtualScroll).off();
            $(this.scrollHandler).off();
            $(this.dropDownContainer).remove();
            $(this.dropDownItems).remove();
            $(this.virtualScroll).remove();
            $(this.scrollHandler).remove();
            this.pointer = 0;
            this.onClickOutSide.unsubscribe();
            this.onItemChange.unsubscribe();
            this.onItemClick.unsubscribe();
            this.elementRef.nativeElement.removeEventListener('mousewheel', this.onMouseWheelMotion.bind(this));
        }
        catch (e) {
            console.error('method [ngOnDestroy] - error: ', e);
        }
    };
    // @HostListener('mousewheel', ['$event'])
    // @HostListener('mousewheel', ['$event'])
    /**
     * @param {?} event
     * @return {?}
     */
    DropDownList.prototype.onMouseWheelMotion = 
    // @HostListener('mousewheel', ['$event'])
    /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event.wheelDelta > 0 && this.pointer > 0) {
            this.pointer--;
        }
        if (event.wheelDelta < 0 && this.pointer < this.dataSource.length - 7) {
            this.pointer++;
        }
        if (this.pointer !== this.previousPointer) {
            if (this.pointer > this.selectedIndex) {
                this.highlightedItem = "";
            }
            else {
                this.highlightedItem = $(this.elementRef.nativeElement).find('#' + 0);
            }
            this.validateNow();
            $(this.virtualScroll).scrollTop(this.pointer * 21);
        }
        this.previousPointer = this.pointer;
    };
    /**
     * @param {?} index
     * @return {?}
     */
    DropDownList.prototype.scrollToIndex = /**
     * @param {?} index
     * @return {?}
     */
    function (index) {
        if (index > -1 && index < this.dataSource.length) {
            if (this.dataSource.length < DropDownList.DEFAULT_ITEM_NUMBER) {
                this.highlightIndex = this.pointer = 0;
                this.highlightedItem = $(this.elementRef.nativeElement).find('#' + index);
                this.validateNow();
            }
            else {
                if (index > this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER) {
                    this.pointer = this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER;
                    this.highlightIndex = DropDownList.DEFAULT_ITEM_NUMBER - (this.dataSource.length - index);
                    this.highlightedItem = $(this.elementRef.nativeElement).find('#' + this.highlightIndex);
                }
                else {
                    this.pointer = index;
                    this.highlightIndex = 0;
                    this.highlightedItem = $(this.elementRef.nativeElement).find('div#0');
                }
                $(this.virtualScroll).scrollTop(this.pointer * 21);
                this.validateNow();
            }
        }
    };
    /**
     * @return {?}
     */
    DropDownList.prototype.navigateDown = /**
     * @return {?}
     */
    function () {
        if (this.highlightIndex < $(this.dropDownItems).children().length - 1) {
            this.highlightIndex++;
            $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
            this.highlightedItem = $(this.dropDownItems).find("div#" + this.highlightIndex);
            this.highlightedItem.addClass('swt-selected-dropdown-item');
        }
        else {
            if (this.pointer < this.dataSource.length - DropDownList.DEFAULT_ITEM_NUMBER) {
                this.pointer++;
                this.validateNow();
            }
        }
        // increment active item index while is less than datasource length.
        this._activeItemIndex = Number(this.highlightedItem.attr('index'));
        $(this.virtualScroll).scrollTop(this.pointer * 21);
        this.onItemNavigation.emit({ direction: 'down', highlightIndex: this.dataSource[this.highlightIndex] });
    };
    /**
     * @return {?}
     */
    DropDownList.prototype.navigateUp = /**
     * @return {?}
     */
    function () {
        if (this.highlightIndex > 0) {
            this.highlightIndex--;
            $(this.dropDownItems).find("div.swt-selected-dropdown-item").removeClass("swt-selected-dropdown-item");
            this.highlightedItem = $(this.dropDownItems).find("div#" + this.highlightIndex);
            this.highlightedItem.addClass('swt-selected-dropdown-item');
        }
        else {
            if (this.pointer > 0) {
                this.pointer--;
                this.validateNow();
            }
        }
        // decrement active item index while is greater than 0.
        this._activeItemIndex = Number(this.highlightedItem.attr('index'));
        $(this.virtualScroll).scrollTop(this.pointer * 21);
        this.onItemNavigation.emit({ direction: 'up', highlightIndex: this.dataSource[this.highlightIndex] });
    };
    /**
     * @return {?}
     */
    DropDownList.prototype.validateNow = /**
     * @return {?}
     */
    function () {
        try {
            /** @type {?} */
            var item = void 0;
            /** @type {?} */
            var object = null;
            /** @type {?} */
            var loopEnd = this.dataSource.length > DropDownList.DEFAULT_ITEM_NUMBER ? 7 : this.dataSource.length;
            for (var i = 0; i < loopEnd; i++) {
                item = $(this.elementRef.nativeElement).find('#' + i);
                object = this.dataSource[this.pointer + i];
                $(item).text(object[this.dataLabel]);
                $(item).attr('index', this.originalDataSource.indexOf(object));
                $(item).attr('title', object[this.dataLabel]);
            }
            if (this.highlightedItem) {
                /** @type {?} */
                var previous = $(this.elementRef.nativeElement).find('div.swt-selected-dropdown-item');
                $(previous).removeClass('swt-selected-dropdown-item');
                $(this.highlightedItem).addClass('swt-selected-dropdown-item');
            }
            else {
                $(this.dropDownItems).find('div.swt-selected-dropdown-item').removeClass('swt-selected-dropdown-item');
            }
        }
        catch (e) {
            console.error('method [validateNow] - error: ', e);
        }
    };
    /**
     * This method is used to filter the current
     * list with a given word.
     * @param word
     */
    /**
     * This method is used to filter the current
     * list with a given word.
     * @param {?} word
     * @return {?}
     */
    DropDownList.prototype.filter = /**
     * This method is used to filter the current
     * list with a given word.
     * @param {?} word
     * @return {?}
     */
    function (word) {
        var _this = this;
        this.clearFilter();
        /** @type {?} */
        var result = this.dataSource.filter((/**
         * @param {?} item
         * @return {?}
         */
        function (item) {
            return String(item[_this.dataLabel]).toUpperCase().startsWith(word.toUpperCase());
        }));
        this.isFilterActive = true;
        this.highlightIndex = this.pointer = 0;
        this.dataSource = result;
    };
    /**
     * This method is used to clear the filter
     */
    /**
     * This method is used to clear the filter
     * @return {?}
     */
    DropDownList.prototype.clearFilter = /**
     * This method is used to clear the filter
     * @return {?}
     */
    function () {
        if (this.isFilterActive) {
            this.dataSource = tslib_1.__spread(this.originalDataSource);
            this.isFilterActive = false;
        }
    };
    /**
     * @private
     * @param {?} value
     * @return {?}
     */
    DropDownList.prototype.applyDataSource = /**
     * @private
     * @param {?} value
     * @return {?}
     */
    function (value) {
        try {
            $(this.dropDownItems).empty();
            if (value.length <= DropDownList.DEFAULT_ITEM_NUMBER) {
                for (var i = 0; i < value.length; i++) {
                    $(this.dropDownItems).append("<div class=\"swt-dropdown-item\" id=\"" + i + "\"></div>");
                }
                $(this.virtualScroll).hide();
                $(this.virtualScroll).height(value.length * 21 + 'px');
                $(this.dropDownItems).css('width', '100%');
                this.validateNow();
            }
            else {
                for (var i = 0; i < DropDownList.DEFAULT_ITEM_NUMBER; i++) {
                    $(this.dropDownItems).append("<div class=\"swt-dropdown-item\" id=\"" + i + "\"></div>");
                }
                $(this.virtualScroll).show();
                $(this.virtualScroll).height('147px');
                this.validateNow();
                $(this.scrollHandler).height(value.length * 21);
            }
        }
        catch (e) {
            console.error('method [applyDataSource ] - error: ', e);
        }
    };
    DropDownList.DEFAULT_ITEM_NUMBER = 7;
    DropDownList.decorators = [
        { type: Component, args: [{
                    selector: 'swt-drop-down-list',
                    template: "\n        <div class=\"swt-dropdown-container\">\n            <div class=\"swt-dropdown-items\">\n                <!--<div class=\"swt-dropdown-item\" id=\"0\"></div>-->\n            </div>\n            <div class=\"swt-virtual-scroll\">\n                <div class=\"swt-dropdown-scrollhandler\"></div>\n            </div>\n        </div>\n    ",
                    styles: ["\n        .swt-dropdown-container {\n            display: flex;\n            outline: 1px solid #cccccc;\n            max-height: 147px;\n            width: 100%;\n            box-sizing: border-box;\n        }\n\n        .swt-dropdown-items, .swt-virtual-scroll {\n            height: 100%;\n        }\n\n        .swt-dropdown-items {\n            background-color: #FFF;\n            width: calc(100% - 13px);\n            display: block;\n        }\n\n        .swt-virtual-scroll {\n            /*border: 1px solid green;*/\n            width: 13px;\n            overflow-x: hidden;\n            overflow-y: auto;\n            display: none;\n        }\n\n        .swt-dropdown-scrollhandler {\n            /*border: 1px solid purple;*/\n            width: 2px;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    DropDownList.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    DropDownList.propDecorators = {
        onItemClick: [{ type: Output }],
        onItemChange: [{ type: Output }],
        onClickOutSide: [{ type: Output }],
        onItemNavigation: [{ type: Output }],
        selectedIndex: [{ type: Input }],
        selectedItem: [{ type: Input }],
        selectedLabel: [{ type: Input }],
        selectedValue: [{ type: Input }],
        dataLabel: [{ type: Input }],
        dataSource: [{ type: Input }],
        clickoutEventHandler: [{ type: HostListener, args: ['document:click', ['$event'],] }]
    };
    return DropDownList;
}());
export { DropDownList };
if (false) {
    /** @type {?} */
    DropDownList.DEFAULT_ITEM_NUMBER;
    /** @type {?} */
    DropDownList.prototype.onItemClick;
    /** @type {?} */
    DropDownList.prototype.onItemChange;
    /** @type {?} */
    DropDownList.prototype.onClickOutSide;
    /** @type {?} */
    DropDownList.prototype.onItemNavigation;
    /** @type {?} */
    DropDownList.prototype.highlightIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.dropDownContainer;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.dropDownItems;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.virtualScroll;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.scrollHandler;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.highlightedItem;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.pointer;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.previousPointer;
    /** @type {?} */
    DropDownList.prototype.isFilterActive;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.originalDataSource;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.firstLoad;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._activeItemIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedLabel;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._selectedValue;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._dataLabel;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype._dataSource;
    /**
     * @type {?}
     * @private
     */
    DropDownList.prototype.elementRef;
}
//# sourceMappingURL=data:application/json;base64,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