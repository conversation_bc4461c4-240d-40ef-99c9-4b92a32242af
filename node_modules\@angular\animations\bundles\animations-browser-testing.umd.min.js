/**
 * @license Angular v7.2.16
 * (c) 2010-2019 Google LLC. https://angular.io/
 * License: MIT
 */
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("@angular/animations"),require("@angular/animations/browser")):"function"==typeof define&&define.amd?define("@angular/animations/browser/testing",["exports","@angular/animations","@angular/animations/browser"],n):n(((t=t||self).ng=t.ng||{},t.ng.animations=t.ng.animations||{},t.ng.animations.browser=t.ng.animations.browser||{},t.ng.animations.browser.testing={}),t.ng.animations,t.ng.animations.browser)}(this,function(t,n,o){"use strict";var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var o in n)n.hasOwnProperty(o)&&(t[o]=n[o])})(t,n)},r=function(){function t(){}return t.prototype.validateStyleProperty=function(t){return o.ɵvalidateStyleProperty(t)},t.prototype.matchesElement=function(t,n){return o.ɵmatchesElement(t,n)},t.prototype.containsElement=function(t,n){return o.ɵcontainsElement(t,n)},t.prototype.query=function(t,n,e){return o.ɵinvokeQuery(t,n,e)},t.prototype.computeStyle=function(t,n,o){return o||""},t.prototype.animate=function(n,o,e,r,s,a){void 0===a&&(a=[]);var u=new i(n,o,e,r,s,a);return t.log.push(u),u},t.log=[],t}(),i=function(t){function r(n,e,i,s,a,u){var p=t.call(this,i,s)||this;return p.element=n,p.keyframes=e,p.duration=i,p.delay=s,p.easing=a,p.previousPlayers=u,p.__finished=!1,p.__started=!1,p.previousStyles={},p._onInitFns=[],p.currentSnapshot={},o.ɵallowPreviousPlayerStylesMerge(i,s)&&u.forEach(function(t){if(t instanceof r){var n=t.currentSnapshot;Object.keys(n).forEach(function(t){return p.previousStyles[t]=n[t]})}}),p}return function i(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}(r,t),r.prototype.onInit=function(t){this._onInitFns.push(t)},r.prototype.init=function(){t.prototype.init.call(this),this._onInitFns.forEach(function(t){return t()}),this._onInitFns=[]},r.prototype.finish=function(){t.prototype.finish.call(this),this.__finished=!0},r.prototype.destroy=function(){t.prototype.destroy.call(this),this.__finished=!0},r.prototype.triggerMicrotask=function(){},r.prototype.play=function(){t.prototype.play.call(this),this.__started=!0},r.prototype.hasStarted=function(){return this.__started},r.prototype.beforeDestroy=function(){var t=this,o={};Object.keys(this.previousStyles).forEach(function(n){o[n]=t.previousStyles[n]}),this.hasStarted()&&this.keyframes.forEach(function(e){Object.keys(e).forEach(function(r){"offset"!=r&&(o[r]=t.__finished?e[r]:n.AUTO_STYLE)})}),this.currentSnapshot=o},r}(n.NoopAnimationPlayer);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
t.MockAnimationDriver=r,t.MockAnimationPlayer=i,Object.defineProperty(t,"__esModule",{value:!0})});