/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
//@dynamic
export class NumberAmountEditor {
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.validationResult = true;
        this.logger = null;
        //public static commonGrid :any;
        this.CRUD_OPERATION = "crud_operation";
        this.CRUD_DATA = "crud_data";
        this.CRUD_ORIGINAL_DATA = "crud_original_data";
        this.CRUD_CHANGES_DATA = null;
        this.originalDefaultValue = null;
        this.enableFlag = this.args.column.params.enableDisableCells(this.args.item, this.args.column.field);
        this.showHideCells = this.args.column.params.showHideCells(this.args.item, this.args.column.field);
        this.commonGrid = this.args.column.params.grid;
        this.columnDef = this.args.column;
        this.maxChars = this.columnDef.maxChars;
        this.logger = new Logger('NumberAmountEditor', null, 6);
        this.init();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    init() {
        if (this.columnDef.params.enableDisableCells) {
            this.enableFlag = this.columnDef.params.enableDisableCells(this.args.item, this.args.column.field);
        }
        else {
            this.enableFlag = true;
        }
        if (this.columnDef['properties'] && this.columnDef['properties']['enabled']) {
            this.enableFlag = this.columnDef['properties']['enabled'];
        }
        this.logger.info('method [init] -START- enableFlag :', this.enableFlag);
        this.loadValue(this.args.item);
        if (this.showHideCells) {
            this.$input = $(`<input id="input" class="text-style renderAsInput ${this.enableFlag ? 'input-text-editor' : 'input-text-editor-disabled'}" style="${(this.enableFlag == true) ? 'background-color: white !important;' : 'background-color: rgba(255, 204, 102, 1) !important'}"  pattern="[0-9]+([\.,][0-9]+)?"   value="${this.defaultValue}" ></input>`);
        }
        else {
            this.$input = $(``);
        }
        this.$input.appendTo(this.args.container);
        if (this.enableFlag) {
            this.$input.focus();
            this.$input.select();
        }
        this.$input.keypress((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (!document.getSelection().toString() && document.getElementById("input")['selectionStart'] == document.getElementById("input")['selectionEnd'] && event.target.value.length >= this.maxChars)
                return false;
        }));
        /*this.$input.keydown(function (event) {
            if( event.ctrlKey==true && (event.which == '118' || event.which == '86'))
                event.preventDefault()
        });*/
        this.$input.keypress((/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            /** @type {?} */
            var regex = new RegExp("^[0-9\-.,tbmTBM]$");
            /** @type {?} */
            var str = String.fromCharCode(!e.charCode ? e.which : e.charCode);
            if (regex.test(str)) {
                return true;
            }
            e.preventDefault();
            return false;
        }));
        this.$input.focusout((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            if (event.handleObj.type == "focusout") {
                this.destroy();
            }
        }));
        /** @type {?} */
        var target = {
            name: this.args.column.name,
            field: this.args.column.field,
            editor: (this.args.column.editor != null && this.args.column.editor != undefined) ? this.args.column.editor.name : null,
            formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
            data: this.args.item
        };
        /** @type {?} */
        var ListEvent = {
            rowIndex: this.args.item.id,
            cellIndex: this.args.column.columnorder,
            columnIndex: this.args.column.columnorder,
            target: target
        };
        this.commonGrid.ITEM_FOCUS_IN.emit(ListEvent);
        // - remove Highlighting .
        /** @type {?} */
        var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
        if (selectedCells.length > 0 && !this.commonGrid.enableRowSelection) {
            for (var index = 0; index < selectedCells.length; index++) {
                /** @type {?} */
                var item = selectedCells[index];
                $(item).removeClass('selected');
            }
        }
        this.logger.info('method [init] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    destroy() {
        this.logger.info('method [destroy] -START-');
        this.applyValue(this.args.item, this.getValue());
        this.isValueChanged();
        if (this.showHideCells) {
            setTimeout((/**
             * @return {?}
             */
            () => {
                // focusout event .
                /** @type {?} */
                var target = {
                    name: this.args.column.name,
                    field: this.args.column.field,
                    editor: (this.args.column.editor != null && this.args.column.editor != undefined) ? this.args.column.editor.name : null,
                    formatter: (this.args.column.formatter != null && this.args.column.formatter != undefined) ? this.args.column.formatter.name : null,
                    data: this.args.item
                };
                /** @type {?} */
                var ListEvent = {
                    rowIndex: this.args.item.id,
                    cellIndex: this.args.column.columnorder,
                    columnIndex: this.args.column.columnorder,
                    target: target
                };
                this.commonGrid.ITEM_FOCUS_OUT.emit(ListEvent);
                //if(this.$input)this.$input.remove();
            }), 10);
        }
        this.logger.info('method [destroy] -END-');
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    focus() {
        this.logger.info('method [focus] -START/END-');
        this.$input.focus();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    getValue() {
        this.logger.info('method [getValue] -START/END-');
        return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} val
     * @return {?}
     */
    setValue(val) {
        this.logger.info('method [setValue] -START/END-');
        this.$input.val(val);
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('method [loadValue] -START-');
        if (this.showHideCells) {
            this.defaultValue = item[this.args.column.field] != undefined ? item[this.args.column.field] : '';
            this.originalDefaultValue = this.commonGrid.originalDataprovider[this.args.item['id']] != undefined ? this.commonGrid.originalDataprovider[this.args.item['id']][this.args.column.field] : "";
        }
        this.logger.info('method [loadValue] -END-' + this.originalDefaultValue + '***' + this.defaultValue);
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    save() {
        this.logger.info('method [save] - START/END');
        if (this.showHideCells) {
            this.args.commitChanges();
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    serializeValue() {
        this.logger.info('method [serializeValue] - START/END');
        if (this.$input.val() == "") {
            return this.originalDefaultValue;
        }
        else
            return this.$input.val();
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        this.logger.info('method [applyValue] - START/END');
        if (this.showHideCells && this.enableFlag == true) {
            item[this.args.column.field] = state;
            item.slickgrid_rowcontent[this.args.column.field] = { content: state };
            /** @type {?} */
            var crudChange = this.commonGrid.changes.getValues().find((/**
             * @param {?} x
             * @return {?}
             */
            x => ((x.crud_data.id == this.args.item.id))));
            if (crudChange)
                crudChange['crud_data'][this.args.column.field] = state;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    isValueChanged() {
        if (this.showHideCells) {
            setTimeout((/**
             * @return {?}
             */
            () => {
                /** @type {?} */
                let isChanged = (this.$input.val() !== this.defaultValue);
                this.logger.info('method [isValueChanged] , returned value: "' + this.$input.val() + '" - START/END');
                if (this.enableFlag == true) {
                    this.CRUD_CHANGES_DATA = this.commonGrid.changes.getValues().find((/**
                     * @param {?} x
                     * @return {?}
                     */
                    x => ((x.crud_data.id == this.args.item.id))));
                    ;
                    if (this.CRUD_CHANGES_DATA != undefined && this.CRUD_CHANGES_DATA != null) {
                        this.originalDefaultValue = this.CRUD_CHANGES_DATA['crud_original_data'] != undefined ? this.CRUD_CHANGES_DATA['crud_original_data'][this.args.column.field] : undefined;
                        if (this.originalDefaultValue == undefined)
                            this.originalDefaultValue = "";
                    }
                    if ((isChanged) || (((this.originalDefaultValue != null)) && (this.originalDefaultValue != this.$input.val()))) {
                        /** @type {?} */
                        var thereIsInsert = false;
                        if (this.commonGrid.changes.getValues().length > 0) {
                            /** @type {?} */
                            var crudInsert = this.commonGrid.changes.getValues().find((/**
                             * @param {?} x
                             * @return {?}
                             */
                            x => ((x.crud_data.id == this.args.item.id) && (x.crud_operation == "I"))));
                            if (crudInsert != undefined && crudInsert[this.CRUD_OPERATION].indexOf('I') == 0)
                                thereIsInsert = true;
                        }
                        if (!thereIsInsert) {
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field] = { content: this.defaultValue };
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            this.validationResult = this.commonGrid.validate(this.args.item, original_row, false);
                            if (this.validationResult) {
                                this.commonGrid.updateCrud(updatedObject);
                                this.commonGrid.spyChanges({ field: this.args.column.field });
                                //ITEM_CHANGED
                                /** @type {?} */
                                var event = {
                                    rowIndex: this.args.item.id,
                                    target: this.args.column.editor.name,
                                    dataField: this.args.column.field,
                                    listData: Object.assign({}, updatedObject)
                                };
                                this.commonGrid.ITEM_CHANGED.emit(event);
                            }
                            else {
                                isChanged = false;
                                if (this.originalDefaultValue == this.$input.val()) {
                                    /** @type {?} */
                                    var crudChange = this.commonGrid.changes.getValues().find((/**
                                     * @param {?} x
                                     * @return {?}
                                     */
                                    x => ((x.crud_data.id == this.args.item.id))));
                                    /** @type {?} */
                                    var ch = String("U(" + this.args.column.field + ")");
                                    if (crudChange) {
                                        if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                                        }
                                        else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                            crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                                        }
                                        if (crudChange['crud_operation'] == "") {
                                            /** @type {?} */
                                            var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                             * @param {?} x
                                             * @return {?}
                                             */
                                            x => ((x.crud_data.id == this.args.item.id))));
                                            this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                                        }
                                    }
                                    //- Do not emit SpyNoChanges on the grid unless there is other changes.
                                    if (this.commonGrid.changes.size() == 0)
                                        this.commonGrid.spyNoChanges({ field: this.args.column.field });
                                }
                            }
                        }
                    }
                    else if (this.originalDefaultValue == this.$input.val()) {
                        if (this.commonGrid.changes.size() == 0)
                            this.commonGrid.spyNoChanges({ field: this.args.column.field });
                        /** @type {?} */
                        var crudChange = this.commonGrid.changes.getValues().find((/**
                         * @param {?} x
                         * @return {?}
                         */
                        x => ((x.crud_data.id == this.args.item.id))));
                        /** @type {?} */
                        var ch = String("U(" + this.args.column.field + ")");
                        if (crudChange) {
                            if (String(crudChange['crud_operation']).indexOf(ch + ">") != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch + ">", "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(">" + ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(">" + ch, "");
                            }
                            else if (String(crudChange['crud_operation']).indexOf(ch) != -1) {
                                crudChange['crud_operation'] = String(crudChange['crud_operation']).replace(ch, "");
                            }
                            if (crudChange['crud_operation'] == "") {
                                /** @type {?} */
                                var crudChangeIndex = this.commonGrid.changes.getValues().findIndex((/**
                                 * @param {?} x
                                 * @return {?}
                                 */
                                x => ((x.crud_data.id == this.args.item.id))));
                                this.commonGrid.changes.remove(this.commonGrid.changes.getKeys()[crudChangeIndex]);
                            }
                        }
                        if (isChanged) {
                            /** @type {?} */
                            var original_row = [];
                            for (let key in this.args.item) {
                                if (key != 'slickgrid_rowcontent') {
                                    original_row[key] = this.args.item[key];
                                }
                                else {
                                    break;
                                }
                            }
                            original_row['slickgrid_rowcontent'] = {};
                            for (let key in this.args.item['slickgrid_rowcontent']) {
                                original_row['slickgrid_rowcontent'][key] = Object.assign({}, this.args.item['slickgrid_rowcontent'][key]);
                            }
                            original_row[this.args.column.field] = this.defaultValue;
                            original_row['slickgrid_rowcontent'][this.args.column.field].content = this.defaultValue;
                            /** @type {?} */
                            var updatedObject = {
                                rowIndex: this.args.item.id,
                                columnIndex: this.args.column.columnorder,
                                new_row: this.args.item,
                                original_row: original_row,
                                changedColumn: this.args.column.field,
                                oldValue: this.defaultValue,
                                newValue: this.args.item[this.args.column.field]
                            };
                            //ITEM_CHANGED
                            /** @type {?} */
                            var event = {
                                rowIndex: this.args.item.id,
                                target: this.args.column.editor,
                                dataField: this.args.column.field,
                                listData: Object.assign({}, updatedObject)
                            };
                            if (this.commonGrid.ITEM_CHANGED.observers.length > 1) {
                                /** @type {?} */
                                var x = this.commonGrid.ITEM_CHANGED.observers[0];
                                this.commonGrid.ITEM_CHANGED.observers = [];
                                this.commonGrid.ITEM_CHANGED.observers[0] = x;
                            }
                            this.commonGrid.ITEM_CHANGED.emit(event);
                        }
                    }
                }
                return isChanged;
            }), 0);
        }
        else {
            return false;
        }
    }
    /*-----------------------------------------------------------------------------------------------------------------------------*/
    /**
     * @return {?}
     */
    validate() {
        this.logger.info('method [validate] - START/END');
        return {
            valid: true,
            msg: null
        };
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.validationResult;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.$input;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.logger;
    /** @type {?} */
    NumberAmountEditor.prototype.CRUD_OPERATION;
    /** @type {?} */
    NumberAmountEditor.prototype.CRUD_DATA;
    /** @type {?} */
    NumberAmountEditor.prototype.CRUD_ORIGINAL_DATA;
    /** @type {?} */
    NumberAmountEditor.prototype.CRUD_CHANGES_DATA;
    /** @type {?} */
    NumberAmountEditor.prototype.originalDefaultValue;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.enableFlag;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.showHideCells;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.commonGrid;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.columnDef;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.maxChars;
    /**
     * @type {?}
     * @private
     */
    NumberAmountEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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