/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Injectable } from '@angular/core';
import { Alert } from "./alert.component";
import { CommonService } from "./common.service";
import { Logger } from '../logging/logger.service';
var SwtAlert = /** @class */ (function (_super) {
    tslib_1.__extends(SwtAlert, _super);
    function SwtAlert(commonService) {
        var _this = _super.call(this, commonService) || this;
        _this.commonService = commonService;
        _this.alertErrorImage = "assets/images//error.png";
        _this.alertInfoImage = "assets/images/info.png";
        _this.alertQuestionImage = "assets/images/question-mark.png";
        _this.alertWarningImage = "assets/images/warning.png";
        _this.alert = new Alert(commonService);
        _this.logger = new Logger("SwtAlert", commonService.httpclient);
        return _this;
    }
    /**
     * info
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    SwtAlert.prototype.info = /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ info ] method START");
        try {
            if (title == null) {
                title = this.infoMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertInfoImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ info ] method - error ", error);
        }
        this.logger.info("[ info ] method END");
    };
    /**
     * info
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    SwtAlert.prototype.question = /**
     * info
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the info Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ info ] method START");
        try {
            if (title == null) {
                title = this.infoMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertQuestionImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ info ] method - error ", error);
        }
        this.logger.info("[ info ] method END");
    };
    /**
     * error
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the error Alert.
     */
    /**
     * error
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the error Alert.
     */
    SwtAlert.prototype.error = /**
     * error
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the error Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ error ] method START");
        try {
            if (title == null) {
                title = this.errorMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertErrorImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ error ] method - error ", error);
        }
        this.logger.info("[ error ] method END");
    };
    /**
     * warning
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    /**
     * warning
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    SwtAlert.prototype.warning = /**
     * warning
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ warning ] method START");
        try {
            if (title == null) {
                title = this.warningMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertWarningImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ warning ] method - error ", error);
        }
        this.logger.info("[ warning ] method END");
    };
    /**
     * confirm
     *
     * @return Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    /**
     * confirm
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    SwtAlert.prototype.confirm = /**
     * confirm
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     * This function is used to return the warning Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ confirm ] method START");
        try {
            if (title == null) {
                title = this.confirmMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertWarningImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ confirm ] method - error ", error);
        }
        this.logger.info("[ confirm ] method END");
    };
    /**
     * invalid
     *
     * @return Alert - AlertInstance
     *
     *  This function is used to return the invalid Alert.
     */
    /**
     * invalid
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     *  This function is used to return the invalid Alert.
     */
    SwtAlert.prototype.invalid = /**
     * invalid
     *
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} defaultButtonFlag
     * @return {?} Alert - AlertInstance
     *
     *  This function is used to return the invalid Alert.
     */
    function (text, title, flags, parent, closeHandler, defaultButtonFlag /* Alert.OK */) {
        if (text === void 0) { text = ""; }
        if (title === void 0) { title = null; }
        this.logger.info("[ invalid ] method START");
        try {
            if (title == null) {
                title = this.invalidMsg;
            }
            return this.alert.show(text, title, flags, parent, closeHandler, this.alertErrorImage, defaultButtonFlag);
        }
        catch (error) {
            this.logger.error("[ invalid ] method - error ", error);
        }
        this.logger.info("[ invalid ] method END");
    };
    SwtAlert.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    SwtAlert.ctorParameters = function () { return [
        { type: CommonService }
    ]; };
    return SwtAlert;
}(Alert));
export { SwtAlert };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alert;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertErrorImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertInfoImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertQuestionImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.alertWarningImage;
    /**
     * @type {?}
     * @private
     */
    SwtAlert.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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