import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { ElementRef, AfterViewInit } from "@angular/core";
export declare class SwtCanvas extends Container implements AfterViewInit {
    private elem;
    private commonService;
    border: any;
    styleName: string;
    constructor(elem: ElementRef, commonService: CommonService);
    ngAfterViewInit(): void;
}
