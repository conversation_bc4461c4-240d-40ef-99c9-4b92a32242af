import { element } from '@angular/core/src/render3';
import { Column, Filter, FilterArguments, FilterCallback, SearchTerm, OperatorType, OperatorString, MultipleSelectOption, CollectionService, ColumnFilter, CollectionOption, CollectionCustomStructure, GridOption, unsubscribeAllObservables, castToPromise, getDescendantProperty, SelectOption, htmlEncode } from 'angular-slickgrid';
import { TranslateService } from '@ngx-translate/core';
import * as DOMPurify_ from 'dompurify';
import { Observable, Subject, Subscription } from 'rxjs';
const o = DOMPurify_; // patch to fix rollup to work 
import { SwtUtil } from '../utils/swt-util.service';

// using external non-typed js libraries
declare var $: any;

export class SwtColumnFilter implements Filter {
    /** DOM Element Name, useful for auto-detecting positioning (dropup / dropdown) */
    elementName: string;

    /** Filter Multiple-Select options */
    filterElmOptions: MultipleSelectOption;

    /** The JQuery DOM element */
    $filterElm: any;

    grid: any;
    searchTerms: SearchTerm[];
    columnDef: Column;
    callback: FilterCallback;
    defaultOptions: MultipleSelectOption;
    isFilled = false;
    lastSelectedValue = undefined;
    labelName: string;
    labelPrefixName: string;
    labelSuffixName: string;
    optionLabel: string;
    valueName: string;
    enableTranslateLabel = false;
    subscriptions: Subscription[] = [];
    private scroll = false;
    private _clearFilterTriggered = false;
    public isMultipleSelect;
    private FilterInputSearch=false;
  private _shouldTriggerQuery = true;
    private isOpened =false;
    checkboxContainer = null;
    /**
     * Initialize the Filter
     */
    constructor( protected translate: TranslateService, protected collectionService: CollectionService<any> ) {
                    }
    public refreshFilterValues() {
        if ( this.columnFilter ) {
            const newCollection = this.columnFilter.collection || [];
            this.renderDomElement( newCollection );

            // Ensure clear button exists after refresh
            if (this.isMultipleSelect) {
                this.ensureClearButtonExists();
            }
        }


    }

    
    /** Getter for the Column Filter itself */
    protected get columnFilter(): ColumnFilter {
        return this.columnDef && this.columnDef.filter;
    }

    /** Getter for the Collection Options */
    protected get collectionOptions(): CollectionOption {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.collectionOptions;
    }

    /** Getter for the Custom Structure if exist */
    protected get customStructure(): CollectionCustomStructure {
        return this.columnDef && this.columnDef.filter && this.columnDef.filter.customStructure;
    }

    /** Getter for the Grid Options pulled through the Grid Object */
    protected get gridOptions(): GridOption {
        return ( this.grid && this.grid.getOptions ) ? this.grid.getOptions() : {};
    }

    /** Getter for the filter operator */
    get operator(): OperatorType | OperatorString {
        if ( this.columnDef && this.columnDef.filter && this.columnDef.filter.operator ) {
            return this.columnDef && this.columnDef.filter && this.columnDef.filter.operator;
        }
        return this.isMultipleSelect ? OperatorType.in : OperatorType.equal;
    }

    /**
     * Initialize the filter template
     */
    init( args: FilterArguments ) {
        try{
        this.grid = args.grid;
        this.callback = args.callback;
        this.columnDef = args.columnDef;
        this.searchTerms = args.searchTerms || [];
        this.isMultipleSelect = this.columnDef['FilterType'] == "MultipleSelect" ? true : false;
        this.FilterInputSearch = this.columnDef['FilterInputSearch'];
            this.setFilterOptions();
        if ( !this.grid || !this.columnDef || !this.columnFilter || ( !this.columnFilter.collection && !this.columnFilter.collectionAsync ) ) {
            throw new Error( `[Angular-SlickGrid] You need to pass a "collection" (or "collectionAsync") for the MultipleSelect/SingleSelect Filter to work correctly. Also each option should include a value/label pair (or value/labelKey when using Locale). For example:: { filter: model: Filters.multipleSelect, collection: [{ value: true, label: 'True' }, { value: false, label: 'False'}] }` );
        }

        this.enableTranslateLabel = this.columnFilter.enableTranslateLabel;
        this.labelName = this.customStructure && this.customStructure.label || 'label';
        this.labelPrefixName = this.customStructure && this.customStructure.labelPrefix || 'labelPrefix';
        this.labelSuffixName = this.customStructure && this.customStructure.labelSuffix || 'labelSuffix';
        this.optionLabel = this.customStructure && this.customStructure.optionLabel || 'value';
        this.valueName = this.customStructure && this.customStructure.value || 'value';

        if ( this.enableTranslateLabel && ( !this.translate || typeof this.translate.instant !== 'function' ) ) {
            throw new Error( `[select-editor] The ngx-translate TranslateService is required for the Select Filter to work correctly` );
        }

        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        const newCollection = this.columnFilter.collection || [];
        this.renderDomElement( newCollection );

        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if ( collectionAsync ) {
            this.renderOptionsAsync( collectionAsync ); // create Subject after resolve (createCollectionAsyncSubject)
        }

          // step 3, subscribe to the keyup event and run the callback when that happens
            this.$filterElm.keyup(( e: any ) => {
        let value = e && e.target && e.target.value || '';
        const enableWhiteSpaceTrim = this.gridOptions.enableFilterTrimWhiteSpace || this.columnFilter.enableTrimWhiteSpace;
                if ( typeof value === 'string' && enableWhiteSpaceTrim ) {
          value = value.trim();
        }
  
                if ( this._clearFilterTriggered ) {
                    this.callback( e, { columnDef: this.columnDef, clearFilterTriggered: this._clearFilterTriggered, shouldTriggerQuery: this._shouldTriggerQuery } );
                    this.$filterElm.removeClass( 'filled' );
        } else {
                    value === '' ? this.$filterElm.removeClass( 'filled' ) : this.$filterElm.addClass( 'filled' );
                    this.callback( e, { columnDef: this.columnDef, searchTerms: [value], shouldTriggerQuery: this._shouldTriggerQuery } );
        }
                    
        // reset both flags for next use
        this._clearFilterTriggered = false;
        this._shouldTriggerQuery = true;
            } );
        
        }catch(error){
            console.error('method [ init] error :',error);
        }
    }

    refreshHeaderOnly() {
        // always render the Select (dropdown) DOM element, even if user passed a "collectionAsync",
        // if that is the case, the Select will simply be without any options but we still have to render it (else SlickGrid would throw an error)
        const newCollection = this.columnFilter.collection || [];
        this.renderDomElement( newCollection );

        // on every Filter which have a "collection" or a "collectionAsync"
        // we will add (or replace) a Subject to the "collectionAsync" property so that user has possibility to change the collection
        // if "collectionAsync" is already set by the user, it will resolve it first then after it will replace it with a Subject
        const collectionAsync = this.columnFilter && this.columnFilter.collectionAsync;
        if ( collectionAsync ) {
            this.renderOptionsAsync( collectionAsync ); // create Subject after resolve (createCollectionAsyncSubject)
        }

        // Ensure clear button exists after header refresh
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }

    }

    // /**
    //  * Clear the filter values
    //  */
    // clear() {
    //     console.log("console .log clear !!!!!");
    //     this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: []} );
    //     if ( this.$filterElm && this.$filterElm.multipleSelect ) {
    //         // reload the filter element by it's id, to make sure it's still a valid element (because of some issue in the GraphQL example)
    //         this.$filterElm.multipleSelect( 'setSelects', [] );
    //         this.$filterElm.removeClass( 'filled' );
    //         this.searchTerms = [];
    //         // this.columnDef.params.grid.refreshFilters();
    //     }
    // }
/**
   * Clear the filter value
   */
clear(shouldTriggerQuery = true) {
    console.log("run clear function !");
  
    if (this.$filterElm) {
        this._clearFilterTriggered = true;
        this._shouldTriggerQuery = shouldTriggerQuery;
        
        // For multiselect, we need to clear selections using the multipleSelect API
        if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
            this.$filterElm.multipleSelect('setSelects', []);
        } else {
            this.$filterElm.val('');
        }
        
        this.searchTerms = [];
        
        // For multiselect, trigger the onClose event which will call the callback
        if (this.isMultipleSelect && this.$filterElm.multipleSelect) {
            // Directly call the callback to clear the filter
            this.callback(undefined, { 
                columnDef: this.columnDef, 
                operator: this.operator, 
                searchTerms: [],
                shouldTriggerQuery: true
            });
            
            // Remove filled class if present
            this.$filterElm.removeClass('filled');
        } else {
            // For regular input, trigger keyup which will call the callback
            this.$filterElm.trigger('keyup');
        }
    }
}

    /**
     * destroy the filter
     */
    destroy() {
        if ( this.$filterElm ) {
            // remove event watcher
            this.$filterElm.off().remove();
        }
        this.$filterElm.multipleSelect('destroy');

        // also dispose of all Subscriptions
        this.subscriptions = unsubscribeAllObservables( this.subscriptions );
    }

    /**
     * Set value(s) on the DOM element
     */
    setValues( values: SearchTerm | SearchTerm[] ) {
        if ( values ) {
            values = Array.isArray( values ) ? values : [values];
            this.$filterElm.multipleSelect( 'setSelects', values );
        }
    }

    //
    // protected functions
    // ------------------

    /**
     * user might want to filter certain items of the collection
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    protected filterCollection( inputCollection ) {
        let outputCollection = inputCollection;

        // user might want to filter certain items of the collection
        if ( this.columnDef && this.columnFilter && this.columnFilter.collectionFilterBy ) {
            const filterBy = this.columnFilter.collectionFilterBy;
            const filterCollectionBy = this.columnFilter.collectionOptions && this.columnFilter.collectionOptions.filterResultAfterEachPass || null;
            outputCollection = this.collectionService.filterCollection( outputCollection, filterBy, filterCollectionBy );
        }

        return outputCollection;
    }

    /**
     * user might want to sort the collection in a certain way
     * @param inputCollection
     * @return outputCollection filtered and/or sorted collection
     */
    protected sortCollection( inputCollection ) {
        let outputCollection = inputCollection;

        // user might want to sort the collection
        if ( this.columnDef && this.columnFilter && this.columnFilter.collectionSortBy ) {
            const sortBy = this.columnFilter.collectionSortBy;
            outputCollection = this.collectionService.sortCollection( this.columnDef, outputCollection, sortBy, this.enableTranslateLabel );
        }

        return outputCollection;
    }

    protected async renderOptionsAsync( collectionAsync: Promise<any> | Observable<any> | Subject<any> ) {

        let awaitedCollection: any = [];

        if ( collectionAsync ) {
            awaitedCollection = await castToPromise( collectionAsync );
            this.renderDomElementFromCollectionAsync( awaitedCollection );

            // because we accept Promises & HttpClient Observable only execute once
            // we will re-create an RxJs Subject which will replace the "collectionAsync" which got executed once anyway
            // doing this provide the user a way to call a "collectionAsync.next()"
            this.createCollectionAsyncSubject();
        }
    }

    /** Create or recreate an Observable Subject and reassign it to the "collectionAsync" object so user can call a "collectionAsync.next()" on it */
    protected createCollectionAsyncSubject() {
        const newCollectionAsync = new Subject<any>();
        this.columnFilter.collectionAsync = newCollectionAsync;
        this.subscriptions.push(
            newCollectionAsync.subscribe( collection => this.renderDomElementFromCollectionAsync( collection ) )
        );
    }

    /**
     * When user use a CollectionAsync we will use the returned collection to render the filter DOM element
     * and reinitialize filter collection with this new collection
     */
    protected renderDomElementFromCollectionAsync( collection ) {

        if ( this.collectionOptions && this.collectionOptions.collectionInObjectProperty ) {
            collection = getDescendantProperty( collection, this.collectionOptions.collectionInObjectProperty );
        }
        if ( !Array.isArray( collection ) ) {
            throw new Error( 'Something went wrong while trying to pull the collection from the "collectionAsync" call in the Select Filter, the collection is not a valid array.' );
        }

        // copy over the array received from the async call to the "collection" as the new collection to use
        // this has to be BEFORE the `collectionObserver().subscribe` to avoid going into an infinite loop
        this.columnFilter.collection = collection;

        // recreate Multiple Select after getting async collection
        this.renderDomElement( collection );

        // Ensure clear button exists after async collection update
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
        }
    }

    protected renderDomElement( collection ) {

        if ( !Array.isArray( collection ) && this.collectionOptions && this.collectionOptions.collectionInObjectProperty ) {
            collection = getDescendantProperty( collection, this.collectionOptions.collectionInObjectProperty );
        }
        if ( !Array.isArray( collection ) ) {
            throw new Error( 'The "collection" passed to the Select Filter is not a valid array' );
        }

        // user can optionally add a blank entry at the beginning of the collection
        if ( this.collectionOptions && this.collectionOptions.addBlankEntry ) {
            collection.unshift( this.createBlankEntry() );
        }

        let newCollection = collection;

        // user might want to filter and/or sort certain items of the collection
        newCollection = this.filterCollection( newCollection );
        newCollection = this.sortCollection( newCollection );

        // step 1, create HTML string template
        const filterTemplate = this.buildTemplateHtmlString( newCollection, this.searchTerms );

        // step 2, create the DOM Element of the filter & pre-load search terms
        // also subscribe to the onClose event
        this.createDomElement( filterTemplate );

    }

    /**
     * Create the HTML template as a string
     */
    protected buildTemplateHtmlString( optionCollection: any[], searchTerms: SearchTerm[] ) {

        let options = '';
        const fieldId = this.columnDef && this.columnDef.id;
        const separatorBetweenLabels = this.collectionOptions && this.collectionOptions.separatorBetweenTextLabels || '';
        const isRenderHtmlEnabled = this.columnFilter && this.columnFilter.enableRenderHtml || false;
        const sanitizedOptions = this.gridOptions && this.gridOptions.sanitizeHtmlOptions || {};

        // collection could be an Array of Strings OR Objects
        if ( optionCollection.every( x => typeof x === 'string' ) ) {
            optionCollection.forEach(( option: string ) => {
                const selected = ( searchTerms.findIndex(( term ) => term === option ) >= 0 ) ? 'selected' : '';
                options += `<option value="${option}" label="${option}" ${selected}>${option}</option>`;

                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if ( selected ) {
                    this.isFilled = true;
                }
            } );
        } else {
            // array of objects will require a label/value pair unless a customStructure is passed
            optionCollection.forEach(( option: SelectOption ) => {
                if ( !option || ( option[this.labelName] === undefined && option.labelKey === undefined ) ) {
                    throw new Error( `[select-filter] A collection with value/label (or value/labelKey when using Locale) is required to populate the Select list, for example:: { filter: model: Filters.multipleSelect, collection: [ { value: '1', label: 'One' } ]')` );
                }
                const labelKey = ( option.labelKey || option[this.labelName] ) as string;

                const selected = (searchTerms.length > 0 ) ? 'selected' : '';
                const labelText = ( ( option.labelKey || this.enableTranslateLabel ) && labelKey ) ? this.translate.instant( labelKey || ' ' ) : labelKey;
                let prefixText = option[this.labelPrefixName] || '';
                let suffixText = option[this.labelSuffixName] || '';
                let optionLabel = option[this.optionLabel] || '';
                optionLabel = optionLabel.toString().replace( /\"/g, '\'' ); // replace double quotes by single quotes to avoid interfering with regular html

                // also translate prefix/suffix if enableTranslateLabel is true and text is a string
                prefixText = ( this.enableTranslateLabel && prefixText && typeof prefixText === 'string' ) ? this.translate.instant( prefixText || ' ' ) : prefixText;
                suffixText = ( this.enableTranslateLabel && suffixText && typeof suffixText === 'string' ) ? this.translate.instant( suffixText || ' ' ) : suffixText;
                optionLabel = ( this.enableTranslateLabel && optionLabel && typeof optionLabel === 'string' ) ? this.translate.instant( optionLabel || ' ' ) : optionLabel;

                // add to a temp array for joining purpose and filter out empty text
                const tmpOptionArray = [prefixText, labelText, suffixText].filter(( text ) => text );
                let optionText = tmpOptionArray.join( separatorBetweenLabels );

                // if user specifically wants to render html text, he needs to opt-in else it will stripped out by default
                // also, the 3rd party lib will saninitze any html code unless it's encoded, so we'll do that
                if ( isRenderHtmlEnabled ) {
                    // sanitize any unauthorized html tags like script and others
                    // for the remaining allowed tags we'll permit all attributes
                    const sanitizedText = DOMPurify_.sanitize( optionText, sanitizedOptions );
                    optionText = htmlEncode( sanitizedText );
                }

                // html text of each select option
                options += `<option value="${option[this.valueName]}" label="${optionLabel}" ${selected}>${optionText}</option>`;

                // if there's at least 1 search term found, we will add the "filled" class for styling purposes
                if ( selected ) {
                    this.isFilled = true;
                }
            } );
        }

        return `<select class="ms-filter search-filter filter-${fieldId}" ${this.isMultipleSelect ? 'multiple="multiple"' : ''}>${options}</select>`;
    }

    /** Create a blank entry that can be added to the collection. It will also reuse the same customStructure if need be */
    protected createBlankEntry() {

        const blankEntry = {
            [this.labelName]: '',
            [this.valueName]: ''
        };
        if ( this.labelPrefixName ) {
            blankEntry[this.labelPrefixName] = '';
        }
        if ( this.labelSuffixName ) {
            blankEntry[this.labelSuffixName] = '';
        }
        return blankEntry;
    }

    /**
     * From the html template string, create a DOM element
     * Subscribe to the onClose event and run the callback when that happens
     * @param filterTemplate
     */
    protected createDomElement( filterTemplate: string ) {
        const fieldId = this.columnDef && this.columnDef.id;

        // provide the name attribute to the DOM element which will be needed to auto-adjust drop position (dropup / dropdown)
        this.elementName = `filter-${fieldId}`;
        this.defaultOptions.name = this.elementName;

        const $headerElm = this.grid.getHeaderColumn( fieldId );
        // create the DOM element & add an ID and filter class
        this.$filterElm = $( filterTemplate );

        if ( typeof this.$filterElm.multipleSelect !== 'function' ) {
            throw new Error( `multiple-select.js was not found, make sure to modify your "angular-cli.json" file and include "../node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js" and it's css or SASS file` );
        }
        this.$filterElm.attr( 'id', this.elementName );
        this.$filterElm.data( 'columnId', fieldId );

        // if there's a search term, we will add the "filled" class for styling purposes
        if ( this.isFilled ) {
            this.$filterElm.addClass( 'filled' );
        }

        // append the new DOM element to the header row
        if ( this.$filterElm && typeof this.$filterElm.appendTo === 'function' ) {
            this.$filterElm.appendTo( $headerElm );
            $( '.slick-header-column > .ms-parent' ).click( function( event ) {
                event.stopPropagation();
            } );
        }

        // merge options & attach multiSelect
        const elementOptions: MultipleSelectOption = { ...this.defaultOptions, ...this.columnFilter.filterOptions };
        this.filterElmOptions = { ...this.defaultOptions, ...elementOptions };
        this.$filterElm = this.$filterElm.multipleSelect( this.filterElmOptions );

        // Ensure clear button is added for multiple select filters after DOM is ready
        if (this.isMultipleSelect) {
            this.ensureClearButtonExists();
            // Also start monitoring to catch cases where the filter is recreated
            this.monitorAndEnsureClearButton();
        }
    }

    /**
     * Ensures the clear button exists in the multiple select dropdown
     * This method checks for ms-select-all element as a trigger and adds clear button if missing
     * Called whenever the filter is recreated or refreshed
     */
    private ensureClearButtonExists() {
        if (!this.isMultipleSelect) {
            return;
        }

        // Use a more robust approach to find the checkbox container
        // Try multiple times with increasing delays to handle async DOM updates
        const attempts = [0, 50, 100, 200, 500, 1000]; // milliseconds

        const tryAddClearButton = (attemptIndex: number) => {
            if (attemptIndex >= attempts.length) {
                console.warn('Failed to add clear button after all attempts for column:', this.columnDef.id);
                return;
            }

            setTimeout(() => {
                // Find the container using multiple selectors to be more robust
                let container = $(`div[name=filter-${this.columnDef.id}]`);

                // If not found, try alternative selectors
                if (!container.length) {
                    container = $(`.ms-drop[data-name=filter-${this.columnDef.id}]`);
                }

                if (!container.length) {
                    container = $(`.ms-drop:has(.ms-choice[data-name=filter-${this.columnDef.id}])`);
                }

                // Check if ms-select-all exists as a trigger - this indicates the dropdown is properly initialized
                const hasSelectAll = container.find('.ms-select-all').length > 0;
                console.log("🚀 ~ setTimeout ~ hasSelectAll:", hasSelectAll)

                // If container exists, has ms-select-all, and clear button doesn't exist, add it
                    // Create clear filter button with an inline SVG icon on the right
                    const clearBtn = $(
                        `<button class="ms-ok-button clear-filter-btn">
                            <span style="display: inline-flex; align-items: center;">
                                Clear Filter
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-left: 5px;">
                                    <path d="M3 4h18l-7 8v8h-4v-8l-7-8z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                                    <path d="M5 5L19 19" stroke="red" stroke-width="2"/>
                                </svg>
                            </span>
                        </button>`
                    );

                    // Insert at the very beginning of the dropdown container
                    container.prepend(clearBtn);

                    // Add click handler to clear button
                    clearBtn.on('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        // Call the clear method
                        this.clear(true);

                        // Close the dropdown menu
                        if (this.$filterElm && this.$filterElm.multipleSelect) {
                            this.$filterElm.multipleSelect('close');
                        }
                    });

                    console.log('Clear button successfully added for column:', this.columnDef.id);
                // If container exists, has select-all, but clear button already exists, we're done
            }, attempts[attemptIndex]);
        };

        // Start the first attempt
        tryAddClearButton(0);
    }

    /**
     * Monitors for the existence of ms-select-all and ensures clear button is added
     * This is a more aggressive approach for cases where the filter is frequently recreated
     */
    private monitorAndEnsureClearButton() {
        if (!this.isMultipleSelect) {
            return;
        }

        const checkInterval = setInterval(() => {
            const container = $(`div[name=filter-${this.columnDef.id}]`);
            const hasSelectAll = container.find('.ms-select-all').length > 0;
            const hasClearButton = container.find('.clear-filter-btn').length > 0;

            // If we have select-all but no clear button, add it
            if (container.length && hasSelectAll && !hasClearButton) {
                // Create clear filter button
                const clearBtn = $(
                    `<button class="ms-ok-button clear-filter-btn">
                        <span style="display: inline-flex; align-items: center;">
                            Clear Filter
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-left: 5px;">
                                <path d="M3 4h18l-7 8v8h-4v-8l-7-8z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                                <path d="M5 5L19 19" stroke="red" stroke-width="2"/>
                            </svg>
                        </span>
                    </button>`
                );

                // Insert at the very beginning of the dropdown container
                container.prepend(clearBtn);

                // Add click handler to clear button
                clearBtn.on('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    // Call the clear method
                    this.clear(true);

                    // Close the dropdown menu
                    if (this.$filterElm && this.$filterElm.multipleSelect) {
                        this.$filterElm.multipleSelect('close');
                    }
                });

                console.log('Clear button added via monitoring for column:', this.columnDef.id);
            }

            // If container no longer exists, stop monitoring
            if (!container.length) {
                clearInterval(checkInterval);
            }
        }, 100); // Check every 100ms

        // Stop monitoring after 10 seconds to prevent memory leaks
        setTimeout(() => {
            clearInterval(checkInterval);
        }, 10000);
    }

    private setFilterOptions(){
        
        try{

            
            const clickHandler = (event) => {
                const clickedCheckbox = event.target;
                const name = clickedCheckbox.dataset ? clickedCheckbox.dataset.name : "";
                if (this.checkboxContainer && clickedCheckbox.value === "(NOT EMPTY)") {
                  this.checkboxContainer.find("input[type=checkbox][value='(EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && clickedCheckbox.value === "(EMPTY)") {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
                if (this.checkboxContainer && name.includes("selectAllfilter")) {
                    this.checkboxContainer.find("input[type=checkbox][value='(NOT EMPTY)']").prop('checked', false);
                }
              
                // Add your desired code here to handle the checkbox click event
              };

            const options: MultipleSelectOption = {
                    autoAdjustDropHeight: true,
                    autoAdjustDropPosition: true,
                    autoAdjustDropWidthByTextSize: true,
                    container: 'body',
                    filter: this.FilterInputSearch, 
                    maxHeight: 275,
                    minWidth: this.columnDef.width,//-Fix M6549:try to enhance the design of the filter in case of short values (case of sign).
                    filterAcceptOnEnter: true,
                    single: !this.isMultipleSelect,
                    //animate: 'slide',
                    textTemplate: ( $elm ) => {
                        // render HTML code or not, by default it is sanitized and won't be rendered
                        const isRenderHtmlEnabled = this.columnDef && this.columnDef.filter && this.columnDef.filter.enableRenderHtml || false;
                        return isRenderHtmlEnabled ? $elm.text() : $elm.html();
                    },
                    onClose: () => {
                        try{
                            //console.log('-----onClose----------', this.elementName);
                        // we will subscribe to the onClose event for triggering our callback
                        // also add/remove "filled" class for styling purposes
                                if((!this.isMultipleSelect && this.lastSelectedValue != undefined) || this.isMultipleSelect ){
                            let selectedItems = this.$filterElm.multipleSelect( 'getSelects' );
                    if ( Array.isArray( selectedItems ) && selectedItems.length > 0 &&    this.lastSelectedValue != this.columnDef.params.grid.all ) {
                                this.isFilled = true;
                                this.$filterElm.addClass( 'filled' ).siblings( 'div .search-filter' ).addClass( 'filled' );
                            } else {
                                        selectedItems = [];
                                this.isFilled = false;
                                this.$filterElm.removeClass( 'filled' ).siblings( 'div .search-filter' ).removeClass( 'filled' );
                            }

                                    //-Fix M6549:Filter with an empty string doesn't exist.
                                    if(selectedItems.length == 1 && selectedItems[0] == ''  ) selectedItems.push('')
                                    this.callback( undefined, { columnDef: this.columnDef, operator: this.operator, searchTerms: selectedItems, shouldTriggerQuery: true } );
                                    $( document ).off( 'click' );
                                }
                                if(event) event.stopPropagation();
                                if(this.checkboxContainer)
                                    this.checkboxContainer.find("input[type=checkbox]").off("click", clickHandler);
                            
                        }catch(error){
                            console.error(' error', error)
                        }
                    },
                    onOpen: () => {
                        console.log('-----onOpen----------', this.columnDef.width);
                        if ( !this.isMultipleSelect ) {
                            console.log('02020')
                            this.lastSelectedValue = undefined;
                            //console.log('-----onOpen----------');
                            $( "div[name^=filter-]" ).each(( index, item ) => {
                                let name = $( item ).attr( 'name' );
                                if ( name != this.elementName && $( item ).css( 'display' ) == "block" ) {
                                    //console.log('-----onOpen---------- slideUp ')
                                    $( item ).slideUp();
                                }
                            } )
                            event.preventDefault();
                            event.stopImmediatePropagation();
                            var left = $( "div[name=filter-" + this.columnDef.id + "]" ).position().left;
                            var width = $( "div[name=filter-" + this.columnDef.id + "]" ).width();
                            if ( left >= width ) {
                                var newposLeft = ( left - width ) + 14;
                                $( "div[name=filter-" + this.columnDef.id + "]" ).css( { left: newposLeft } );
                            }

                            var ul = $( $( $( "div[name=filter-" + this.columnDef.id + "]" ).children()[0] ) );
                            $( document ).on( 'click', ( event ) => {
                                var target = $( event.target );
                                if ( !target.is( ul[0] ) ) {
                                    $( "div[name=filter-" + this.columnDef.id + "]" ).slideUp(() => {
                                        $( document ).off( 'click' );
                                    } );
                                }
                            } );

                            this.columnDef.params.grid.gridObj.onScroll.subscribe(( e ) => {
                                $( "div[name=filter-" + this.columnDef.id + "]" ).slideUp(() => {
                                    $( document ).off( 'click' );
                                } );
                            } );
                            event.stopPropagation();


                        }else{
                            this.checkboxContainer = $( "div[name=filter-" + this.columnDef.id + "]" );

                            // Ensure clear button exists using the centralized method
                            this.ensureClearButtonExists();

                            // Also start monitoring to catch any recreation of the filter
                            this.monitorAndEnsureClearButton();

                            // Add a small delay to ensure the DOM is ready for checkbox handlers
                            setTimeout(() => {
                                // Attach the click event handler to checkboxes
                                if (this.checkboxContainer) {
                                    this.checkboxContainer.find("input[type=checkbox]").click(clickHandler);
                                }
                            }, 50);

                            event.preventDefault();
                            event.stopImmediatePropagation();
                            
                            var left = $( "div[name=filter-" + this.columnDef.id + "]" ).position().left;
                            var width = $( "div[name=filter-" + this.columnDef.id + "]" ).width();
                            if ( left >= width ) {
                                var newposLeft = ( left - width ) + 14;
                                // console.log("🚀 ~ file: swt-column-filter.ts:591 ~ setFilterOptions ~ newposLeft:", newposLeft)
                                $( "div[name=filter-" + this.columnDef.id + "]" ).css( { left: newposLeft } );
                            }

                            //-Fix M6549: Select a filter then scroll left, the filter is still open.
                            this.columnDef.params.grid.gridObj.onScroll.subscribe(( e ) => {
                                
                                $( "div[name=filter-" + this.columnDef.id + "]" ).slideUp(() => {
                                    $( document ).off( 'click' );
                                } );

                                
                            } );
                            
                            
                            $('div[name^="filter-"]').each((index, item )=> {
                                const name = $( item ).attr( 'name' );
                                if(name != "filter-" + this.columnDef.id && $( item ).css( 'display' ) == "block" ) {
                                    $( item ).slideUp(() => {
                                        $( document ).off( 'click' );
                                    } );
                                }
                              });
                              event.stopPropagation();

                           

                        }
                        
                           
                    },
                    onClick: ( event ) => {
                        this.lastSelectedValue = event.label;
                        //Commented is not needed check if it's working fine 
                        /*if ( event.label == this.columnDef.params.grid.all ) {
                            if ( !this.columnDef.params.grid.paginationComponent.realPagination && this.columnDef.params.grid.GroupId == null){
                                this.clear(true);
                            }
                        }*/
                    } 
                    
                };


                if ( this.isMultipleSelect ) {
                    options.single = false;
                    options.okButton = true;
                    options.addTitle = true; // show tooltip of all selected items while hovering the filter
                    options.countSelected = this.translate.instant( 'X_OF_Y_SELECTED' );
                    options.allSelected = this.translate.instant( 'ALL_SELECTED' );
                    options.selectAllText = this.translate.instant( 'ALL' );
                    options.selectAllDelimiter = ['', '']; // remove default square brackets of default text "[Select All]" => "Select All"
                }

                this.defaultOptions = options;
        }catch(error){
            console.error('method [setFilterOptions] error :',error);
        }
    }
}