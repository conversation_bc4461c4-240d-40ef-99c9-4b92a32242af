/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Injectable } from '@angular/core';
import { ContextMenu, ContextMenuItem } from "../controls/context-menu.component";
import { SwtUtil } from "./swt-util.service";
import { SwtAlert } from "./swt-alert.service";
import { CommonService } from "./common.service";
import { SwtPopUpManager } from '../managers/swt-pop-up-manager.service';
import { JSONViewer } from '../screensUtils/jsonviewer/jsonviewer.component';
/* Display informations about Screen Version
 * <AUTHOR> JABALLAH on 17/10/2018
 *
 * */
//@dynamic
var ScreenVersion = /** @class */ (function () {
    function ScreenVersion(common) {
        this.common = common;
        // Declare ContextMenu object
        this.svContextMenu = null;
        this._moduleName = null;
        this._versionNumber = null;
        this._releaseDate = null;
        this.swtAlert = new SwtAlert(common);
    }
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} moduleName
     * @param {?} versionNumber
     * @param {?} releaseDate
     * @return {?}
     */
    ScreenVersion.prototype.loadScreenVersion = /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} moduleName
     * @param {?} versionNumber
     * @param {?} releaseDate
     * @return {?}
     */
    function (parent, moduleName, versionNumber, releaseDate) {
        // Variable to hold the error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            // Set the screen name
            this._moduleName = moduleName;
            errorLocation = 10;
            // Set the screen version
            this._versionNumber = versionNumber;
            errorLocation = 20;
            // Set the release date of current version
            this._releaseDate = releaseDate;
            // create instance for ContextMenu
            this.svContextMenu = new ContextMenu();
            this.svContextMenu.customItems = new Array();
            /** @type {?} */
            var addMenuItem = null;
            errorLocation = 80;
            addMenuItem = new ContextMenuItem("Screen Version");
            // add the listener to addMenuItem
            addMenuItem.MenuItemSelect = this.viewScrenVersion.bind(this);
            errorLocation = 100;
            this.svContextMenu.customItems.push(addMenuItem);
            errorLocation = 110;
            parent.contextMenu = this.svContextMenu;
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, null, "loadScreenVersion", errorLocation);
        }
    };
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} jsonData
     * @return {?}
     */
    ScreenVersion.prototype.pushJsonData = /**
     * loadScreenVersion
     * This method is used to add on context Menu the Item Screen Version.
     * <AUTHOR> JABALLAH on 17/10/2018
     * @param {?} parent
     * @param {?} jsonData
     * @return {?}
     */
    function (parent, jsonData) {
        // Variable to hold the error location
        /** @type {?} */
        var errorLocation = 0;
        try {
            this.jsonData = jsonData;
            /** @type {?} */
            var addMenuItem = null;
            errorLocation = 80;
            addMenuItem = new ContextMenuItem("Show JSON Data");
            // add the listener to addMenuItem
            addMenuItem.MenuItemSelect = this.getJsonData.bind(this);
            errorLocation = 100;
            this.svContextMenu.customItems.push(addMenuItem);
            errorLocation = 110;
            parent.contextMenu = this.svContextMenu;
            this.callerWindowObject = parent;
            console.log("pushJsonData  parent", parent);
        }
        catch (error) {
            // log the error in ERROR LOG
            SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, null, "loadScreenVersion", errorLocation);
        }
    };
    /**
     * viewScrenVersion
     * @param event:ContextMenuEvent
     * Method used to display informations about Screen Version
     * <AUTHOR> JABALLAH on 17/10/2018
     */
    /**
     * viewScrenVersion
     * <AUTHOR> JABALLAH on 17/10/2018
     * @private
     * @param {?} event
     * @return {?}
     */
    ScreenVersion.prototype.viewScrenVersion = /**
     * viewScrenVersion
     * <AUTHOR> JABALLAH on 17/10/2018
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.swtAlert = new SwtAlert(this.common);
        this.swtAlert.info("Screen Name: " + this._moduleName + "<br>" + ((this._versionNumber) ? "Screen Version: " + this._versionNumber : "") + "<br>" + ((this._releaseDate) ? "Release Date: " + this._releaseDate : ""), "Version");
    };
    /**
     * viewScrenVersion
     * @param event:ContextMenuEvent
     * Method used to display informations about Screen Version
     * <AUTHOR> Sridi on 31/10/2019
     */
    /**
     * viewScrenVersion
     * <AUTHOR> Sridi on 31/10/2019
     * @private
     * @param {?} event
     * @return {?}
     */
    ScreenVersion.prototype.getJsonData = /**
     * viewScrenVersion
     * <AUTHOR> Sridi on 31/10/2019
     * @private
     * @param {?} event
     * @return {?}
     */
    function (event) {
        try {
            this.win = SwtPopUpManager.createPopUp(this, JSONViewer, {
                jsonData: this.jsonData ? this.jsonData : this.callerWindowObject.lastRecievedJSON,
            });
            this.win.width = "700";
            this.win.title = "Last Received JSON";
            this.win.height = "600";
            this.win.enableResize = false;
            this.win.showControls = true;
            this.win.display();
        }
        catch (e) {
            // log the error in ERROR LOG
            // SwtUtil.logError(e, this.moduleId, 'ClassName', 'doViewMessage', this.errorLocation);
        }
    };
    ScreenVersion.decorators = [
        { type: Injectable }
    ];
    /** @nocollapse */
    ScreenVersion.ctorParameters = function () { return [
        { type: CommonService }
    ]; };
    return ScreenVersion;
}());
export { ScreenVersion };
if (false) {
    /** @type {?} */
    ScreenVersion.prototype.svContextMenu;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._moduleName;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._versionNumber;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype._releaseDate;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.swtAlert;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.win;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.jsonData;
    /** @type {?} */
    ScreenVersion.prototype.callerWindowObject;
    /**
     * @type {?}
     * @private
     */
    ScreenVersion.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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