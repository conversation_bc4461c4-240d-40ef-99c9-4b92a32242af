import { EventEmitter, OnInit } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { CommonService } from "./common.service";
import { WindowManager } from "../managers/window-manager.service";
import { Logger } from "../logging/logger.service";
export declare class Alert implements OnInit {
    private common;
    static YES: number;
    static NO: number;
    static OK: number;
    static CANCEL: number;
    static yesLabel: string;
    static noLabel: string;
    static okLabel: string;
    static cancelLabel: string;
    buttonFlags: any;
    texthtml: string;
    title: string;
    iconClass: string;
    flags: number;
    windowManager: WindowManager;
    alertId: string;
    onClose: EventEmitter<any>;
    protected warningMsg: string;
    protected errorMsg: string;
    protected confirmMsg: string;
    protected invalidMsg: string;
    protected infoMsg: string;
    private defaultButtonFlag;
    protected logger: Logger;
    private alertShown;
    private hash;
    constructor(common: CommonService);
    ngOnInit(): void;
    /**
     * This method is used to handle the alert close
     * @param flag
     */
    destroy(flag?: any, count?: any): void;
    /**
     * this method is used to populate the alert dialogue.
     * @param text
     * @param title
     * @param flags
     * @param parent
     * @param closeHandler
     * @param iconClass
     * @param defaultButtonFlag
     */
    show(text?: string, title?: string, flags?: number, parent?: any, closeHandler?: Function, iconClass?: any, defaultButtonFlag?: number): Alert;
    /**
     * this method is used to covert the given flag (type number)
     * to the appropriate button label.
     * @param flag
     */
    protected getButtonLabel(value: number): string[];
    private getbuttonNumber;
    text: string;
}
