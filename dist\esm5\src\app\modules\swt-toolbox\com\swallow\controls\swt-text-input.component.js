/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { CommonService } from '../utils/common.service';
import { Container } from "../containers/swt-container.component";
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Component, ViewChild, ElementRef, Input } from "@angular/core";
var SwtTextInput = /** @class */ (function (_super) {
    tslib_1.__extends(SwtTextInput, _super);
    /**
     * constructor
     * @param elem
     * @param commonService
     */
    function SwtTextInput(elem, commonService) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._required = false;
        if (_this.constructor.name.toUpperCase() == "SWTTEXTINPUT") {
            $($(_this.elem.nativeElement)[0]).attr('selector', 'SwtTextInput');
        }
        else {
            $($(_this.elem.nativeElement)[0]).attr('selector', 'SwtNumericInput');
        }
        return _this;
    }
    Object.defineProperty(SwtTextInput.prototype, "required", {
        get: /**
         * @return {?}
         */
        function () {
            return this._required;
        },
        /* input to hold component visibility */
        set: /* input to hold component visibility */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) == "string") {
                if (value === 'true') {
                    this._required = true;
                }
                else {
                    this._required = false;
                }
            }
            else {
                this._required = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "textAlign", {
        get: /**
         * @return {?}
         */
        function () {
            return this._textAlign;
        },
        //---tabIndex-----------------------------------------------------------------------------------------------------
        set: 
        //---tabIndex-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._textAlign = String(value);
                if (value) {
                    this.textfieldDOM.nativeElement.style.textAlign = value;
                }
            }
            catch (error) {
                console.error('method [ set tabIndex] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "tabIndex", {
        get: /**
         * @return {?}
         */
        function () {
            return this._tabIndex;
        },
        //---tabIndex-----------------------------------------------------------------------------------------------------
        set: 
        //---tabIndex-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._tabIndex = String(value);
                if (this.textfieldDOM)
                    $($(this.textfieldDOM)[0].nativeElement).attr("tabindex", this._tabIndex);
            }
            catch (error) {
                console.error('method [ set tabIndex] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "styleName", {
        //---styleName-----------------------------------------------------------------------------------------------------
        set: 
        //---styleName-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                if (this.textfieldDOM)
                    $($(this.textfieldDOM)[0].nativeElement).addClass(value);
            }
            catch (error) {
                console.error('method [ set styleName] - error:', error);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "displayAsPassword", {
        get: /**
         * @return {?}
         */
        function () {
            return this._displayAsPassword;
        },
        //---displayAsPassword-----------------------------------------------------------------------------------------------------
        set: 
        //---displayAsPassword-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._displayAsPassword = this.adaptValueAsBoolean(value);
            if (this._displayAsPassword) {
                if (this.textfieldDOM)
                    $($(this.textfieldDOM)[0].nativeElement).attr('type', 'password');
            }
            else {
                if (this.textfieldDOM)
                    $($(this.textfieldDOM)[0].nativeElement).attr('type', 'text');
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "editable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._editable;
        },
        //---editable-----------------------------------------------------------------------------------------------------
        set: 
        //---editable-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._editable = this.adaptValueAsBoolean(value);
            if (this.textfieldDOM)
                $($(this.textfieldDOM)[0].nativeElement).prop('readonly', !this._editable);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "text", {
        get: /**
         * @return {?}
         */
        function () {
            return $($(this.textfieldDOM)[0].nativeElement).val();
        },
        //---text-----------------------------------------------------------------------------------------------------
        set: 
        //---text-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            value = value.toString();
            this._text = !value ? "" : value;
            if (this.firstCall) {
                this.originalValue = value;
                this.firstCall = false;
            }
            else {
                this._spyChanges(this._text);
            }
            if (this._text != undefined && this._text != null && $(this.textfieldDOM)) {
                $($(this.textfieldDOM)[0].nativeElement).val(this.validateMaxChar(this.validateRestrict(this._text)));
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtTextInput.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        //---dataProvider-----------------------------------------------------------------------------------------------------
        set: 
        //---dataProvider-----------------------------------------------------------------------------------------------------
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            var _this = this;
            setTimeout((/**
             * @return {?}
             */
            function () {
                _this._dataProvider = value;
                if (value.length > 0) {
                    $($(_this.textfieldDOM)[0].nativeElement).autocomplete({
                        source: value
                    });
                }
            }), 0);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtTextInput.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        _super.prototype.ngOnInit.call(this);
        if (!this._text) {
            //-Set text to empty if there is no entered value, to detect spyChanges.
            this.text = "";
        }
        this.addEventListener('input', (/**
         * @param {?} e
         * @return {?}
         */
        function (e) {
            _this.change(event);
            _this.change_.emit(event);
        }));
    };
    /**
     * onPaste
     * @param event
     */
    /**
     * onPaste
     * @param {?} event
     * @return {?}
     */
    SwtTextInput.prototype.onPaste = /**
     * onPaste
     * @param {?} event
     * @return {?}
     */
    function (event) {
        this.change_.emit(event);
    };
    /**
     * focus
     * @param event
     */
    /**
     * focus
     * @return {?}
     */
    SwtTextInput.prototype.setFocusAndSelect = /**
     * focus
     * @return {?}
     */
    function () {
        var _this = this;
        setTimeout((/**
         * @return {?}
         */
        function () {
            $($(_this.elem.nativeElement.children['0'])[0]).select();
            // $( $( this.elem.nativeElement.children['0'] )[0] ).focus();
        }), 0);
    };
    SwtTextInput.decorators = [
        { type: Component, args: [{
                    selector: 'SwtTextInput',
                    template: "\n            <input #textfield\n            popper=\"{{this.toolTipPreviousValue}}\"\n            [popperTrigger]=\"'hover'\"\n            [popperDisabled]=\"toolTipPreviousValue === null ? true : false\"\n            [popperPlacement]=\"'bottom'\"\n            [ngClass]=\"{'border-orange-previous': toolTipPreviousValue != null}\"\n               (paste)=\"onPaste($event)\"\n               class=\"textinput\"\n               [class.requiredInput]= \"this.required==true && !this.text\"/>\n    ",
                    styles: ["\n        :host {\n             outline:none;\n        }\n        .textArea {\n            position: fixed;\n            z-index: 4;\n        }\n        input::placeholder {\n            color: transparent;\n       }\n        input {\n            border: 1px solid #7f9db9;\n            width: 100%;\n            font-size: 11px;\n            height: 23px;\n            line-height:23px;\n            cursor: text;\n            color: #000; /*this line is added because of swtfieldDset set its color affects the color of text input*/\n        }\n\n        input:disabled {\n            color: gray;\n            height: 23px;\n        }\n\n        input:focus {\n            border: 1px solid #7F9DB9;\n            outline-style: solid;\n            outline-width: 1px;\n            outline-color: #49B9FF;\n        }\n\n        .hideInput {\n            visibility: hidden;\n        }\n\n        .textinput {\n            padding: 0 5px;\n        }\n    "]
                }] }
    ];
    /** @nocollapse */
    SwtTextInput.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtTextInput.propDecorators = {
        textfieldDOM: [{ type: ViewChild, args: ['textfield', { read: ElementRef },] }],
        required: [{ type: Input, args: ['required',] }],
        textAlign: [{ type: Input, args: ['textAlign',] }],
        tabIndex: [{ type: Input, args: ['tabIndex',] }],
        styleName: [{ type: Input, args: ['styleName',] }],
        displayAsPassword: [{ type: Input }],
        editable: [{ type: Input }],
        text: [{ type: Input }],
        dataProvider: [{ type: Input }]
    };
    return SwtTextInput;
}(Container));
export { SwtTextInput };
if (false) {
    /** @type {?} */
    SwtTextInput.prototype.textfieldDOM;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._textAlign;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._tabIndex;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._displayAsPassword;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._editable;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._text;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype._required;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtTextInput.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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