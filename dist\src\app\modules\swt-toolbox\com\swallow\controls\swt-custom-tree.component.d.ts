import { AfterContentInit, ElementRef, EventEmitter, OnInit } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { Logger } from '../logging/logger.service';
import { CommonService } from '../utils/common.service';
import { HashMap } from '../utils/HashMap.service';
import { AdvancedToolTip } from "./advanced-tool-tip.component";
import { UIComponent } from "./UIComponent.service";
export declare class CustomTree extends Container implements OnInit, AfterContentInit {
    protected elem: ElementRef;
    protected commonService: CommonService;
    static seqAttribute: string;
    static TREE_STR: string;
    static LEVEL_1_STR: string;
    static LEVEL_2_STR: string;
    static LEVEL_3_STR: string;
    static CRUD_OPERATION: string;
    static CRUD_DATA: string;
    firstLoad: boolean;
    local_sequence: number;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    iconFunction: Function;
    ITEM_CLICK: EventEmitter<any>;
    ITEM_ACTIVATE: EventEmitter<any>;
    ITEM_DBCLICK: EventEmitter<any>;
    MOUSE_OUT: EventEmitter<any>;
    itemOpen: EventEmitter<any>;
    itemClose: EventEmitter<any>;
    MOUSE_OVER: EventEmitter<any>;
    FOCUS_IN: EventEmitter<any>;
    FOCUS_OUT: EventEmitter<any>;
    id: any;
    styleName: string;
    buttonMode: boolean;
    labelFunction: Function;
    dataTipFunction: Function;
    _tree_state: any[];
    itemEditEnd: EventEmitter<any>;
    /**
     * private array to hold tree options.
     * private.
     */
    protected options: string[];
    protected isItemSelected: boolean;
    protected _dbclick: Function;
    protected _tempIndex: number;
    protected _selectedLevel: string;
    protected _keyDownFlag: boolean;
    protected _level0OrderAttrName: string;
    protected _level1OrderAttrName: string;
    protected _level2OrderAttrName: string;
    protected _level3OrderAttrName: string;
    protected _level4OrderAttrName: string;
    protected _globalFindStop: boolean;
    protected editableAttribute: string;
    protected editableAdditionalAttribute: HashMap;
    protected editableValidationFunction: Function;
    protected logger: Logger;
    protected leaf_key: number;
    protected _instance: any;
    protected _firstLoad: boolean;
    protected treeContainer: ElementRef;
    protected treeTipHolder: ElementRef;
    protected __OriginalDataProvider: any;
    protected _dataTip: AdvancedToolTip;
    _hideIcons: boolean;
    _addCheckbox: boolean;
    _indeterminateCheckbox: boolean;
    _saveTreeStateBasedOn: string;
    constructor(elem: ElementRef, commonService: CommonService);
    protected defaultHideFunction(item: any): boolean;
    _changes: HashMap;
    /**
     * _changes getter
     * */
    /**
    * _changes setter
    * */
    changes: HashMap;
    protected _hideFunction: Function;
    /**
     * This method is used to set a callback function to the tree
     * component in order to show or hide specific items.
     * @param callback
     */
    hideFunction: Function;
    protected _level2Order: string;
    /**
     * Setter for _level2Order
     */
    /**
    * Setter for _level2Order
    */
    level2Order: string;
    protected _level3Order: string;
    /**
     * Setter for _level3Order
     * */
    level3Order: string;
    protected _level4Order: string;
    /**
     * Setter for _level4Order
     * */
    /**
    * Setter for _level4Order
    * */
    level4Order: string;
    /**
     * private.
     */
    protected _allowMultipleSelection: boolean;
    allowMultipleSelection: boolean;
    protected _selectedIndices: number[];
    /**
    * This method is used to get selected Indices.
    */
    selectedIndices: number[];
    protected _dataProvider: any[];
    dataProvider: any;
    private _width;
    width: string;
    private _height;
    height: string;
    hideIcons: boolean;
    addCheckbox: boolean;
    indeterminateCheckbox: boolean;
    saveTreeStateBasedOn: string;
    private _selectedIndex;
    selectedIndex: number;
    private _selectedItem;
    selectedItem: CustomTreeItem;
    private _doubleClickEnabled;
    doubleClickEnabled: boolean;
    private _selectable;
    selectable: boolean;
    private _enabled;
    enabled: boolean;
    private _level1Order;
    /**
     * Setter for _level1Order
     */
    /**
    * Setter for _level1Order
    */
    level1Order: string;
    private _level0Order;
    /**
         * Setter for _level1Order
         */
    /**
    * Setter for _level1Order
    */
    level0Order: string;
    private _iconWidth;
    /**
    *  set tree icon width
    * @param value
    */
    iconWidth: number;
    private _iconSpacing;
    /**
    * set tree icon spacing.
    * @param value
    */
    iconSpacing: number;
    private _labelSpacing;
    /**
    * set tree label spacing
    * @param value
    */
    labelSpacing: number;
    private _levelOfs;
    /**
    * set tree level offsets.
    * @param value
    */
    levelOfs: number;
    private _dragMoveEnabled;
    dragMoveEnabled: boolean;
    private _dragEnabled;
    dragEnabled: boolean;
    private _editable;
    editable: boolean;
    private _openItems;
    openItems: string[];
    private _closeItems;
    closeItems: string[];
    private _verticalScrollPosition;
    verticalScrollPosition: number;
    private _showRoot;
    /**
     *  Sets the visibility of the root item.
     *
     *  If the dataProvider data has a root node, and this is set to
     *  <code>false</code>, the Tree control does not display the root item.
     *  Only the decendants of the root item are displayed.
     *
     *  This flag has no effect on non-rooted dataProviders, such as List and Array.
     *
     *  @default true
     *  @see #hasRoot
     */
    showRoot: boolean;
    private _editedItemPosition;
    /**
     *  The column and row index of the item renderer for the
     *  data provider item being edited, if any.
     *
     *  <p>This Object has two fields, <code>columnIndex</code> and
     *  <code>rowIndex</code>,
     *  the zero-based column and item indexes of the item.
     *  For a List control, the <code>columnIndex</code> property is always 0;
     *  for example: <code>{columnIndex:0, rowIndex:3}</code>.</p>
     *
     *  <p>Setting this property scrolls the item into view and
     *  dispatches the <code>itemEditBegin</code> event to
     *  open an item editor on the specified item,
     *  </p>
     */
    /**
    *  private
    */
    editedItemPosition: {
        rowIndex: number;
        columnIndex?: number;
    };
    readonly isFirstLoad: boolean;
    static getExpander(node: any): any;
    manageChangesArray(node: any, operation: string, updatedAttribute?: string, map?: HashMap): void;
    /**
     * Finds a node based on a map of attribute values
     * @param attributes
     * @param parentNode
     */
    findNode(attributes: HashMap, parentNode?: CustomTreeItem): CustomTreeItem;
    /**
     * Finds a node based on a map of attribute values
     * @param attributes
     */
    findAndExpandNode(attributes: HashMap): void;
    /**
     * Recursively expand a node
     *
     * Enhancement to expand the Tree to specified Level
     */
    expandNode(nodeXmlList: CustomTreeItem, expandToLvl: any[]): void;
    /**
     * Sorts a tree based on configs given for level1Order, level2Order, level3Order and level4Order
     * TODO: Think on a recursive function for levels 1, 2 3 and .. n
     */
    sort(node: any): void;
    /**
     * Function responsible of setting the label
     */
    treeLabelFunction(item: any): string;
    /**
     * Recursively finds a node and expands it if argument expand is passed "true"
     * @param nodeXmlList
     * @param attributes
     * @param expand
     */
    findNodeRecursively(node: any, attributes: HashMap, expand?: boolean): boolean;
    /**
     * This method is used to select node item by attribute id and value
     * @param selectNodeByAttribute
     */
    selectNodeByAttribute(attributeId: string, attributeValue: string, uniqueNode?: boolean): void;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    isVisibleNode(node: any): any;
    /**
     * This method initialize the tree component View
     */
    init(): void;
    findNodeinDataprovider(id: any, currentNode: any): any;
    recusiveSelectDataProvider(node: any, dataProvidernode: any): void;
    recusiveSelectDataProviderChildren(node: any, dataProvidernode: any): void;
    /**
     * This method is used to remove tree node.
     * @param itemToRemove
     * @param map
     * @param addToChanges
     */
    removeNode(itemToRemove?: any, map?: HashMap, addToChanges?: boolean): void;
    /**
     * This method is used to determine the expander orientation according
     * to it's state (expanded/collapsed)
     * @param node
     */
    private manageExpanderOrientation;
    /**
     * This method is used to remove all tree children.
     * @param addToChanges
     */
    removeAll(addToChanges?: Boolean): void;
    /**
     * This method is used to set tree item to editable.
     * @param attributeName
     * @param attributesToFind
     * @param parentNode
     * @param additionalInformation
     * @param validateFunction
     */
    setEditableItem(attributeName: string, attributesToFind?: HashMap, parentNode?: any, additionalInformation?: HashMap, validateFunction?: Function): void;
    /**
     * Appends a node to tree, if afterNode is passed as parameter usually tree.selectedItem
     * then add node after the passed node else add it at the end
     * @param aNode
     * @param afterNode
     * @param map
     * @param addToChanges
     */
    appendNode(aNode: any, afterNode?: CustomTreeItem, map?: any, addToChanges?: boolean): void;
    /**
     * This method is used to expand all tree nodes.
     * @param expandToLvl
     */
    expandAll(expandToLvl?: string): void;
    /**
     * Close all nodes
     */
    collapseAll(): void;
    setDataTip(Comp: any): AdvancedToolTip;
    getDataTip(): AdvancedToolTip;
    /**
     * This method is used to get selected Items.
     */
    selectedItems(): CustomTreeItem[];
    /**
     * This method is used to validate Display List.
     */
    validateDisplayList(): void;
    /**
     * This method is used reopens the already opened items
     */
    reOpenSavedState(): void;
    /**
     * Save the tree open state
     */
    saveTreeOpenState(): void;
    /**
     * Opens or closes a branch item.
     * When a branch item opens, it restores the open and closed states
     * of its child branches if they were already opened.
     * @param item
     * @param open
     * @param animate
     * @param dispatchEvent
     * @param cause
     */
    expandItem(item: any, open: boolean, animate?: boolean, dispatchEvent?: boolean, cause?: any): void;
    /**
     * This method is used to set custom style function.
     */
    customStyleFunction(item: CustomTreeItem): string;
    /**
     * This method is used to get Item Index.
     * @param item
     */
    getItemIndex(item: any): number;
    /**
     * This method is used to cleans CRUD changes.
     */
    clearChanges(): void;
    /**
     * Returns the level of the selected node, result is Level1, Level2..Leveln
     */
    getSelectedLevel(): string;
    /**
     * public method used to return tree Instance.
     */
    getInstance(): any;
    /**
     * manageChangesArray
     *
     * <AUTHOR> M.Bouraoui
     *
     * This method is used to log the crud opertaions in the changes HashMap
     */
    dataProviderCRUD(dataprovider: any, item: any, operation?: number): void;
    /**
     * This method is used to diselect All selected items of tree.
     */
    diselectAll(): void;
    /**
     * This method will be used to update the tree state
     * when the given node is expanded.
     * @param node
     */
    private updateStateOnExpand;
    private itemEditEndHandler;
    private sortNodeBy;
    /**
     * This method is used to navigate to bottom.
     * @param event
     * @param node
     */
    private scrollToBottom;
    /**
     * This method is used to navigate to top.
     * @param event
     * @param node
     */
    private scrollToTop;
    /**
     * This method is used to loop tree data received as a JSON format
     * and adapt this last one to be compatible with fancyTree library
     * @param value
     */
    private recursive;
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     */
    private getOpenedItems;
    /**
     * This method will be called on each collapse
     * or expand of tree item in order to
     * know the tree state.
     */
    private getClosedItems;
    private selectNode;
    private getScrollPosition;
}
export declare class CustomTreeItem extends UIComponent {
    private itemElement;
    private commonServ;
    data: any;
    parent: CustomTreeItem;
    private node;
    private _expanded;
    private _children;
    private _editable;
    private _customToolTip;
    private _toolTipchild;
    /**
     * CustomTreeItem constructor.
     * @param itemElement
     * @param commonServ
     */
    constructor(itemElement: any, commonServ: CommonService);
    private _editMode;
    editMode: boolean;
    private _selected;
    /**
     * This setter to make item selected.
     * @param value
     */
    selected: boolean;
    private _labelObject;
    labelObject: TreeLabel;
    private _folder;
    folder: boolean;
    private _visible;
    visible: boolean;
    private _icon;
    /**
     * icon getter
     */
    /**
    * icon setter.
    * @param value
    */
    icon: any;
    private _title;
    /**
     * title getter.
     */
    /**
    * title setter.
    * @param value
    */
    title: string;
    private _expander;
    /**
     * expander getter.
     */
    /**
    * expander setter.
    * @param value
    */
    expander: string;
    private _key;
    /**
     * This method is used to get the key of
     * current item.
     */
    readonly key: string;
    private _toolTip;
    /**
     * toolTip getter.
     */
    /**
    * toolTip setter.
    * @param value
    */
    toolTip: string;
    private _height;
    /**
     * height getter.
     */
    /**
    * height setter.
    * @param value
    */
    height: string;
    private _level;
    /**
     * level getter.
     */
    level: string;
    getNode(): any;
    /**
     * return index of the current item
     * relative to its parent
     */
    childIndex(): number;
    /**
     * This method is used to get parent item
     * of the current tree item.
     */
    getParent(): CustomTreeItem;
    /**
     * This method is used to get children of current tree item.
     */
    getChildren(): CustomTreeItem[];
    /**
     * return true if the item is expanded.
     */
    isExpanded(): boolean;
    /**
     * return true if the item is editable.
     */
    isEditable(): boolean;
    /**
     * return true if the item is selected.
     */
    isSelected(): boolean;
    /**
     * This method is used to set data to the current
     * item.
     * @param prop
     * @param value
     */
    setData(prop: string, value: string): void;
    /**
     * This method with optional property is used to get the data
     * if prop is empty else return the given property.
     * @param prop?
     */
    getData(prop?: string): any;
    /**
     * This method is used to expand the current
     * item.
     */
    expand(): void;
    /**
     * This method is used to collapse the current item.
     */
    collapse(): void;
    /**
     * This method is used to add a sub item to the current item.
     * @param item
     */
    appendItem(item: any): void;
    /**
     * This method is used to remove the current item.
     */
    remove(): void;
    /**
     * This method trigger the edit mode of the
     * current CustomTreeItem.
     */
    triggerEdit(): void;
    /**
     * This method stop the editing mode
     * of the current item.
     */
    stopEdit(applyChanges?: boolean): void;
    /**
     * This method is used to get the root of the current item.
     */
    getRoot(): CustomTreeItem;
    /**
     * This method is used to detect if the current item
     * has a children or not
     * it will return true | false | undefined.
     */
    hasChildren(): any;
    setCustomTooltip(tooltip: any): AdvancedToolTip;
    localName(): string;
    getCustomToolTip(): AdvancedToolTip;
    setVisible(visibility: boolean): void;
    private itemVisibilityState;
}
export declare class TreeLabel extends UIComponent {
    private lblelement;
    private commonService;
    constructor(lblelement: any, commonService: CommonService);
    private _item;
    item: any;
    getParentItem(): CustomTreeItem;
}
export declare class TreeIcon extends UIComponent {
    private iconElement;
    private commonService;
    constructor(iconElement: any, commonService: CommonService);
}
export declare class TreeExpander extends UIComponent {
    private expanderElement;
    private commonService;
    constructor(expanderElement: any, commonService: CommonService);
}
