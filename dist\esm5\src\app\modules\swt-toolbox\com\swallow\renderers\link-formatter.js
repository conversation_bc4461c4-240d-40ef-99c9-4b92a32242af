/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
export var LinkFormatter = (/**
 * @param {?} row
 * @param {?} cell
 * @param {?} value
 * @param {?} columnDef
 * @param {?} dataContext
 * @return {?}
 */
function (row, cell, value, columnDef, dataContext) {
    /** @type {?} */
    var blink_me = false;
    if ((String(value)).indexOf('blink') != -1) {
        /** @type {?} */
        var val1 = (value.split(">"))[2];
        /** @type {?} */
        var val2 = val1.split("<");
        value = val2[0];
        blink_me = true;
    }
    if (value == undefined || value == null) {
        return "";
    }
    else {
        if (columnDef && columnDef['columnType'] && String(columnDef['columnType']) === 'link:str') {
            return "<a class=\"" + (blink_me == true ? 'blink_me' : '') + "\" style=\"color:#03A9F4!important; text-decoration: underline; cursor: pointer;\" ><p style=\"padding-left: 5px;\">" + value + "<p></a>";
        }
        else if (columnDef && columnDef['columnType'] && String(columnDef['columnType']) === 'link:num') {
            return "<a class=\"" + (blink_me == true ? 'blink_me' : '') + "\" style=\"color:#03A9F4!important; text-decoration: underline; cursor: pointer; text-align: right;\" ><p style=\"padding-right: 5px;\">" + value + "<p></a>";
        }
        else if (columnDef && columnDef['columnType'] && String(columnDef['columnType']) === 'link:date') {
            return "<a class=\"" + (blink_me == true ? 'blink_me' : '') + "\" style=\"color:#03A9F4!important; text-decoration: underline; cursor: pointer; text-align: center;\" ><p>" + value + "<p></a>";
        }
        else {
            return "<a class=\"" + (blink_me == true ? 'blink_me' : '') + "\" style=\"color:#03A9F4!important; text-decoration: underline; cursor: pointer;\" ><p style=\"padding-left: 5px;\">" + value + "<p></a>";
        }
    }
});
//# sourceMappingURL=data:application/json;base64,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