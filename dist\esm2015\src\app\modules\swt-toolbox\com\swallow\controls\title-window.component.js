/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Directive, ElementRef, EventEmitter, ViewChild, ViewContainerRef, } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { Position, WindowUtils } from '../utils/window-utils';
import { fromPromise } from "rxjs-compat/observable/fromPromise";
import { TabChange, TabClose, WindowResizeEvent } from "../events/swt-events.module";
import { CommonService } from "../../../com/swallow/utils/common.service";
import { unsubscribeAllObservables } from '../renderers/utilities';
import { Logger } from '../logging/logger.service';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
// define directive to inject element in title window.
export class Handler {
    /**
     * @param {?} viewContainerRef
     */
    constructor(viewContainerRef) {
        this.viewContainerRef = viewContainerRef;
    }
}
Handler.decorators = [
    { type: Directive, args: [{
                selector: '[winHandler]'
            },] }
];
/** @nocollapse */
Handler.ctorParameters = () => [
    { type: ViewContainerRef }
];
if (false) {
    /** @type {?} */
    Handler.prototype.viewContainerRef;
}
export class TitleWindow {
    /**
     * @param {?} element
     * @param {?} commonServ
     */
    constructor(element, commonServ) {
        this.element = element;
        this.commonServ = commonServ;
        this.tabid = "";
        this.visible = true;
        this.position = { x: 0, y: 0 };
        this.maxWidth = '';
        this.maxHeight = '';
        this.minWidth = '';
        this.minHeight = '';
        this.enableResize = true;
        this.showControls = true;
        this.showHeader = true;
        this.title = 'Default title';
        this.layoutOrder = 1;
        this.minimizeIcon = 'assets/images/minimize.png';
        this.onClose = new EventEmitter();
        this.isModal = false;
        this.windowStatte = false;
        this.enforceUserPosition = false;
        this.subscriptions = [];
        this.logger = new Logger("", commonServ.httpclient);
    }
    /**
     * @return {?}
     */
    get initX() {
        return this.position.x;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set initX(value) {
        this.position.x = value;
    }
    /**
     * @return {?}
     */
    get initY() {
        return this.position.y;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set initY(value) {
        this.position.y = value;
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        this.subscriptions.push(TabChange.subscribe((/**
         * @param {?} id
         * @return {?}
         */
        (id) => {
            if (this.tabid === id) {
                this.visible = true;
            }
            else {
                this.visible = false;
            }
        })));
        this.subscriptions.push(TabClose.subscribe((/**
         * @param {?} id
         * @return {?}
         */
        (id) => {
            if (this.tabid === id) {
                this.close();
            }
        })));
        // set window z-index.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('z-index', WindowUtils.winOrder);
        WindowUtils.winOrder++;
        WindowUtils.winid = this.id;
        // set window max width.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('max-width', this.maxWidth + 'px');
        // set window max height.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('max-height', this.maxHeight + 'px');
        // set window min width.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('min-width', this.minWidth + 'px');
        // set window min height.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('min-height', this.minHeight + 'px');
        // set width and height if undefined.
        if (!this.width && !this.height) {
            // this.width = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0]).width()) + 16).toString();
            // this.height = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0]).height()) + 38).toString();
            // set window max width.
            // TODO $($(this.element.nativeElement)[0].children[0].children[0]).css('max-width', (Number(this.maxWidth) + 16) + 'px');
            // set window max height.
            //  TODO $($(this.element.nativeElement)[0].children[0].children[0]).css('max-height', (Number(this.maxHeight) + 38) + 'px');
        }
        // make window draggable.
        $('div.window-container').draggable({
            handle: 'div.window-heading',
            containment: 'body',
            scroll: false,
            cursor: 'move',
        });
        if (this.enableResize) {
            $(this.element.nativeElement.children[0].children[0]).resizable();
            $(this.element.nativeElement.children[0].children[0]).on("resize", (/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                WindowResizeEvent.emit(event);
            }));
            $(this.element.nativeElement.children[0].children[0]).resize((/**
             * @param {?} event
             * @return {?}
             */
            (event) => {
                if ($(this.element.nativeElement.children[0].children[0]).height() !== Number(this.height) || $(this.element.nativeElement.children[0]).width() !== Number(this.width)) {
                    this.windowStatte = true;
                    this.minimizeIcon = 'assets/images/maximize.png';
                }
                else {
                    if (this.isMinimized()) {
                        this.minimizeIcon = 'assets/images/minimize.png';
                    }
                }
            }));
        }
        if (this.url && typeof (this.url) === "string") {
            this.subscriptions.push(this.loadComponent(this.url, this.commonServ.injector)
                .subscribe((/**
             * @param {?} component
             * @return {?}
             */
            (component) => {
                this.mloaderOutlet.createComponent(component);
            }), (/**
             * @param {?} error
             * @return {?}
             */
            (error) => {
                this.logger.error(error);
            })));
        }
        else {
            if (this.content) {
                // include content in title window body.
                /** @type {?} */
                const compRef = this.commonServ.resolver.resolveComponentFactory(this.content);
                this.childcomponent = this.contentHolder.viewContainerRef.createComponent(compRef);
                this.mapDataObject(this.childcomponent.instance, this.data);
            }
        }
        if (this.enforceUserPosition) {
            this.setPosition(this.position);
        }
        else {
            this.setPosition(Position.CENTER);
        }
        $(window).on("resize.title" + this.id, ((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            /** @type {?} */
            const popup = this.element.nativeElement.children[0].children[0];
            if (event.target !== popup) {
                this.setPosition(Position.CENTER);
            }
        })));
        // show popup as modal.
        if (!this.isModal) {
            $(this.element.nativeElement.children[0]).css({
                width: 'auto',
                height: 'auto'
            });
        }
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        // this.id = undefined; TODO
        // parentApplication.loaderInfo.url = "";
        // parentApplication.clearParams();
        $(window).off("resize.title" + this.id);
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    }
    /**
     * @param {?} child
     * @return {?}
     */
    addChild(child) {
        if (child.component) {
            this.childcomponent = ((/** @type {?} */ (child.component)));
            this.mapDataObject(this.childcomponent.instance, this.getUrlQuery(child.url));
            this.commonServ.applicationRef.attachView(child.component.hostView);
            /** @type {?} */
            const selector = (/** @type {?} */ (((/** @type {?} */ (child.component.hostView))).rootNodes[0]));
            // this.mloaderOutlet.element.appendChild(selector);
            $(this.element.nativeElement.children[0].children[0].children[1]).append(selector);
            child.component.instance["titleWindow"] = this;
        }
    }
    /**
     * @return {?}
     */
    display() {
        this.windowService.show();
    }
    /**
     * This method will be called when window title clicked.
     * @return {?}
     */
    onTitleBarClick() {
        if (WindowUtils.winid !== this.id) {
            WindowUtils.winOrder++;
            WindowUtils.winid = this.id;
            // set window z-index.
            $($(this.element.nativeElement)[0].children[0]).css('z-index', WindowUtils.winOrder);
        }
    }
    /**
     * This method is used to synchronize the title window
     * layout.
     * @return {?}
     */
    validateNow() {
        // set width and height if undefined.
        if (!this.width && !this.height) {
        }
        // this.width = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0])[0].scrollWidth) + 16).toString();
        // this.height = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0])[0].scrollHeight) + 38).toString();
    }
    /**
     * @return {?}
     */
    getChild() {
        return this.childcomponent.instance;
    }
    /**
     * this method is used to include content in the title window.
     * @param {?} parent
     * @param {?} service
     * @param {?=} url
     * @param {?=} component
     * @param {?=} data
     * @return {?}
     */
    includeContent(parent, service, url, component, data) {
        this.url = url;
        this.data = data;
        this.parent = parent;
        this.windowService = service;
        if (component) {
            this.content = component;
        }
    }
    /**
     * This method is used to minimize the current window.
     * @return {?}
     */
    minimizeWindow() {
        /** @type {?} */
        const winBody = $($(this.element.nativeElement)[0].children[0])[0].children[0].children[1];
        /** @type {?} */
        const winContainer = $($(this.element.nativeElement)[0].children[0])[0].children[0];
        if (this.isMinimized()) {
            this.minimizeIcon = 'assets/images/minimize.png';
            $(winContainer).css('height', this.height);
            $(winContainer).css('width', this.width);
            this.windowStatte = false;
        }
        else {
            this.minimizeIcon = 'assets/images/maximize.png';
            $(winContainer).css('height', '29px');
            this.windowStatte = true;
        }
    }
    /**
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    setBounds(x, y, width, height) {
        this.width = width;
        this.height = height;
        this.setWindowXY(x, y);
    }
    /**
     * @param {?} x
     * @param {?} y
     * @return {?}
     */
    setWindowXY(x, y) {
        this.enforceUserPosition = true;
        this.position = { x: x, y: y };
    }
    /**
     * @param {?} location
     * @return {?}
     */
    setPosition(location) {
        this.enforceUserPosition = true;
        if (this.height && this.width) {
            if (typeof location === 'string') {
                switch (location) {
                    case Position.TOPLEFT: {
                        this.setWindowXY('0', '0');
                        this.position = { x: '0', y: '0' };
                        break;
                    }
                    case Position.TOPCENTER: {
                        /** @type {?} */
                        const valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        const valy = '0';
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.TOPRIGHT: {
                        /** @type {?} */
                        const valx = (screen.width - Number(this.width)).toString();
                        /** @type {?} */
                        const valy = '0';
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMLEFT: {
                        /** @type {?} */
                        const valx = '0';
                        /** @type {?} */
                        const valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMCENTER: {
                        /** @type {?} */
                        const valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        const valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMRIGHT: {
                        /** @type {?} */
                        const valx = (document.body.clientWidth - Number(this.width)).toString();
                        /** @type {?} */
                        const valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.LEFTCENTER: {
                        /** @type {?} */
                        const valx = '0';
                        /** @type {?} */
                        const valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.CENTER: {
                        /** @type {?} */
                        const valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        const valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.RIGHTCENTER: {
                        /** @type {?} */
                        const valx = (document.body.clientWidth - Number(this.width)).toString();
                        /** @type {?} */
                        const valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    default: {
                        /** @type {?} */
                        const valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        const valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                }
            }
            else {
                this.setWindowXY(location.x, location.y);
                this.position = location;
            }
        }
        else {
            /** @type {?} */
            const width = Number($(this.element.nativeElement.children[0].children[0]).width()) + 16;
            /** @type {?} */
            const height = Number($(this.element.nativeElement.children[0].children[0]).height()) + 8;
            /** @type {?} */
            const valx = ((document.body.clientWidth - width) / 2).toString();
            /** @type {?} */
            const valy = ((document.body.clientHeight - height) / 2).toString();
            this.setWindowXY(valx, valy);
            this.position = { x: valx, y: valy };
        }
    }
    /**
     * This method return the window state:
     *  true : window minimized;
     *  false: window maximized;
     * @return {?}
     */
    isMinimized() {
        return this.windowStatte;
    }
    /**
     * This method is used to close current window.
     * @return {?}
     */
    close() {
        this.windowService.close(this.id);
        this.onClose.emit(this.result);
    }
    /**
     * This method is used to get query from url.
     * @private
     * @param {?} url
     * @return {?}
     */
    getUrlQuery(url) {
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        const data = {};
        try {
            /** @type {?} */
            const qm = url.indexOf("?");
            if (qm !== -1) {
                errorLocation = 10;
                /** @type {?} */
                const query = url.substr(qm + 1);
                /** @type {?} */
                const params = query.split("&");
                for (var i = 0; i < params.length; i++) {
                    errorLocation = 20;
                    /** @type {?} */
                    const param = params[i];
                    /** @type {?} */
                    const nameValue = param.split("=");
                    if (nameValue.length === 2) {
                        errorLocation = 30;
                        /** @type {?} */
                        const key = nameValue[0];
                        /** @type {?} */
                        const val = nameValue[1];
                        errorLocation = 40;
                        data[key] = val;
                    }
                }
            }
        }
        catch (error) {
            console.error("TitleWindow [ getUrlQuery ] method error :", error);
        }
        return data;
    }
    /**
     * @private
     * @template T
     * @param {?} url
     * @param {?=} injector
     * @return {?}
     */
    loadComponent(url, injector) {
        /** @type {?} */
        var _comp_url = "";
        if (typeof (url) === "string") {
            _comp_url = url.indexOf("?") ? url.split("?")[0] : url;
        }
        /** @type {?} */
        const manifest = this.commonServ.manifests
            .find((/**
         * @param {?} m
         * @return {?}
         */
        (m) => m.path === _comp_url));
        /** @type {?} */
        const p = this.commonServ.loader.load(manifest.loadChildren)
            .then((/**
         * @param {?} ngModuleFactory
         * @return {?}
         */
        (ngModuleFactory) => {
            /** @type {?} */
            const moduleRef = ngModuleFactory.create(injector || this.commonServ.injector);
            // Read from the moduleRef injector and locate the dynamic component type
            /** @type {?} */
            const dynamicComponentType = moduleRef.injector.get(_comp_url);
            this.mapDataObject(dynamicComponentType, this.getUrlQuery(this.url));
            // Resolve this component factory
            return moduleRef.componentFactoryResolver.resolveComponentFactory(dynamicComponentType);
        }));
        return fromPromise(p);
    }
    /**
     * Maps your object passed in the creation to fields in your own window classes
     * @private
     * @param {?} child
     * @param {?} data
     * @return {?}
     */
    mapDataObject(child, data) {
        /** @type {?} */
        const attributes = Object.getOwnPropertyNames(this);
        if (data) {
            /** @type {?} */
            const keys = Object.keys(data);
            for (let i = 0, length = keys.length; i < length; i++) {
                /** @type {?} */
                const key = keys[i];
                if (this.hasOwnProperty(key)) {
                    this[key] = data[key];
                }
                else {
                    child[key] = data[key];
                }
            }
        }
        child["titleWindow"] = this;
        child["parentDocument"] = this.parent;
        attributes.forEach((/**
         * @param {?} attr
         * @return {?}
         */
        (attr) => {
            child[attr] = this[attr];
        }));
    }
}
TitleWindow.decorators = [
    { type: Component, args: [{
                selector: 'TitleWindow',
                template: `
        <div class="window-overlay" [ngClass]="{'hidden': !visible }">
            <div (mousedown)="onTitleBarClick()" class="window-container"  [style.width.px]="width"
                 [style.height.px]="height"
                 [style.top.px]="this.position.y" [style.left.px]="this.position.x">
                <div (dblclick)="minimizeWindow()" class="window-heading" [hidden]="!showHeader">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr width="100%">
                            <td style="padding-left: 5px; border: none !important">{{ title }}</td>
                            <td style="border: none !important" width="50" align="right" *ngIf="showControls">
                                <img [src]="minimizeIcon" (click)="minimizeWindow()">
                                <img (click)="close()" class="closebtn" src="assets/images/closeButton.png">
                            </td>
                        </tr>
                    </table>
                </div>
                <div  [class.window-body]="showHeader"
                      [class.window-bodyFull]="!showHeader"  >
                    <ng-template winHandler></ng-template>
                    <ng-template #mloaderOutlet></ng-template>
                </div>
            </div>
        </div>
    `,
                styles: [`
        .window-overlay {
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, .2);
            position: fixed;
            top: 0;
            left: 0;
        }

        .window-container {
            position: fixed;
            left: 30%;
            top: 20%;
            width: auto;
            height: auto;
            border-top: none;
            border-bottom: 8px solid #369;
            border-right: 8px solid #369;
            border-left: 8px solid #369;
            border-radius: 5px;
            background-color: #ccecff;
            min-width: 55px;
            min-height: 29px;
            box-sizing: border-box;
        }

        .window-heading {
            width: 100%;
            height: 29px;
            background-color: #369;
            color: #FFF;
            line-height: 30px;
            padding: 0px;
            box-sizing: border-box;
            cursor: default;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-size: 11px;
            font-family: verdana, halvatica, sans-serif;
            font-weight: bolder;
        }

        .window-body {
            width: 100%;
            height: calc(100% - 30px);
            overflow: auto;
        }
        .window-bodyFull {
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        img:hover {
            cursor: pointer;
        }

        .closebtn {
            margin: -2px 0px 0px 5px;
        }

    `]
            }] }
];
/** @nocollapse */
TitleWindow.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
TitleWindow.propDecorators = {
    contentHolder: [{ type: ViewChild, args: [Handler,] }],
    mloaderOutlet: [{ type: ViewChild, args: ["mloaderOutlet", { read: ElementRef },] }]
};
if (false) {
    /** @type {?} */
    TitleWindow.prototype.tabid;
    /** @type {?} */
    TitleWindow.prototype.visible;
    /** @type {?} */
    TitleWindow.prototype.width;
    /** @type {?} */
    TitleWindow.prototype.height;
    /** @type {?} */
    TitleWindow.prototype.position;
    /** @type {?} */
    TitleWindow.prototype.maxWidth;
    /** @type {?} */
    TitleWindow.prototype.maxHeight;
    /** @type {?} */
    TitleWindow.prototype.minWidth;
    /** @type {?} */
    TitleWindow.prototype.minHeight;
    /** @type {?} */
    TitleWindow.prototype.enableResize;
    /** @type {?} */
    TitleWindow.prototype.showControls;
    /** @type {?} */
    TitleWindow.prototype.showHeader;
    /** @type {?} */
    TitleWindow.prototype.title;
    /** @type {?} */
    TitleWindow.prototype.data;
    /** @type {?} */
    TitleWindow.prototype.id;
    /** @type {?} */
    TitleWindow.prototype.layoutOrder;
    /** @type {?} */
    TitleWindow.prototype.minimizeIcon;
    /** @type {?} */
    TitleWindow.prototype.onClose;
    /** @type {?} */
    TitleWindow.prototype.result;
    /** @type {?} */
    TitleWindow.prototype.url;
    /** @type {?} */
    TitleWindow.prototype.isModal;
    /** @type {?} */
    TitleWindow.prototype.contentHolder;
    /** @type {?} */
    TitleWindow.prototype.mloaderOutlet;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.windowStatte;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.content;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.windowService;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.childcomponent;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.enforceUserPosition;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.element;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.commonServ;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGl0bGUtd2luZG93LmNvbXBvbmVudC5qcyIsInNvdXJjZVJvb3QiOiJuZzovL3N3dC10b29sLWJveC8iLCJzb3VyY2VzIjpbInNyYy9hcHAvbW9kdWxlcy9zd3QtdG9vbGJveC9jb20vc3dhbGxvdy9jb250cm9scy90aXRsZS13aW5kb3cuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLEVBQUMsU0FBUyxFQUFrQyxTQUFTLEVBQUUsVUFBVSxFQUFtQixZQUFZLEVBQStCLFNBQVMsRUFBRSxnQkFBZ0IsR0FBRSxNQUFNLGVBQWUsQ0FBQztBQUN6TCxPQUFPLDBCQUEwQixDQUFDO0FBQ2xDLE9BQU8sRUFBQyxRQUFRLEVBQUUsV0FBVyxFQUFDLE1BQU0sdUJBQXVCLENBQUM7QUFFNUQsT0FBTyxFQUFDLFdBQVcsRUFBQyxNQUFNLG9DQUFvQyxDQUFDO0FBRS9ELE9BQU8sRUFBQyxTQUFTLEVBQUUsUUFBUSxFQUFFLGlCQUFpQixFQUFDLE1BQU0sNkJBQTZCLENBQUM7QUFDbkYsT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLDJDQUEyQyxDQUFDO0FBQ3hFLE9BQU8sRUFBRSx5QkFBeUIsRUFBRSxNQUFNLHdCQUF3QixDQUFDO0FBQ25FLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSwyQkFBMkIsQ0FBQzs7O01BSTdDLENBQUMsR0FBUSxPQUFPLENBQUMsUUFBUSxDQUFDOztBQU1oQyxNQUFNLE9BQU8sT0FBTzs7OztJQUNoQixZQUFtQixnQkFBa0M7UUFBbEMscUJBQWdCLEdBQWhCLGdCQUFnQixDQUFrQjtJQUNyRCxDQUFDOzs7WUFMSixTQUFTLFNBQUM7Z0JBQ1AsUUFBUSxFQUFFLGNBQWM7YUFDM0I7Ozs7WUFsQmdKLGdCQUFnQjs7OztJQW9CakosbUNBQXlDOztBQStGekQsTUFBTSxPQUFPLFdBQVc7Ozs7O0lBZ0NwQixZQUFvQixPQUFtQixFQUFVLFVBQXlCO1FBQXRELFlBQU8sR0FBUCxPQUFPLENBQVk7UUFBVSxlQUFVLEdBQVYsVUFBVSxDQUFlO1FBL0JuRSxVQUFLLEdBQVcsRUFBRSxDQUFDO1FBQ25CLFlBQU8sR0FBWSxJQUFJLENBQUM7UUFHeEIsYUFBUSxHQUFRLEVBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFDLENBQUM7UUFDN0IsYUFBUSxHQUFHLEVBQUUsQ0FBQztRQUNkLGNBQVMsR0FBRyxFQUFFLENBQUM7UUFDZixhQUFRLEdBQUcsRUFBRSxDQUFDO1FBQ2QsY0FBUyxHQUFHLEVBQUUsQ0FBQztRQUNmLGlCQUFZLEdBQUcsSUFBSSxDQUFDO1FBQ3BCLGlCQUFZLEdBQUcsSUFBSSxDQUFDO1FBQ3BCLGVBQVUsR0FBRyxJQUFJLENBQUM7UUFDbEIsVUFBSyxHQUFXLGVBQWUsQ0FBQztRQUdoQyxnQkFBVyxHQUFHLENBQUMsQ0FBQztRQUNoQixpQkFBWSxHQUFHLDRCQUE0QixDQUFDO1FBQzVDLFlBQU8sR0FBRyxJQUFJLFlBQVksRUFBTyxDQUFDO1FBR2xDLFlBQU8sR0FBWSxLQUFLLENBQUM7UUFHeEIsaUJBQVksR0FBWSxLQUFLLENBQUM7UUFJOUIsd0JBQW1CLEdBQUcsS0FBSyxDQUFDO1FBRzVCLGtCQUFhLEdBQW1CLEVBQUUsQ0FBQztRQUV2QyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLEVBQUUsRUFBRSxVQUFVLENBQUMsVUFBVSxDQUFDLENBQUM7SUFDeEQsQ0FBQzs7OztJQUVELElBQUksS0FBSztRQUNMLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFDM0IsQ0FBQzs7Ozs7SUFFRCxJQUFJLEtBQUssQ0FBQyxLQUFhO1FBQ25CLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQztJQUM1QixDQUFDOzs7O0lBRUQsSUFBSSxLQUFLO1FBQ0wsT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUMzQixDQUFDOzs7OztJQUVELElBQUksS0FBSyxDQUFDLEtBQWE7UUFDbkIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDO0lBQzVCLENBQUM7Ozs7SUFDRCxRQUFRO1FBQ0osSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQ25CLFNBQVMsQ0FBQyxTQUFTOzs7O1FBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRTtZQUN2QixJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssRUFBRSxFQUFFO2dCQUNuQixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQzthQUN2QjtpQkFBTTtnQkFDSCxJQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQzthQUN4QjtRQUNMLENBQUMsRUFBQyxDQUNMLENBQUM7UUFDRixJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FDckIsUUFBUSxDQUFDLFNBQVM7Ozs7UUFBQyxDQUFDLEVBQUUsRUFBRSxFQUFFO1lBQ3hCLElBQUksSUFBSSxDQUFDLEtBQUssS0FBSyxFQUFFLEVBQUU7Z0JBQ25CLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQzthQUNoQjtRQUNELENBQUMsRUFBQyxDQUNMLENBQUM7UUFDRixzQkFBc0I7UUFDdEIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUNqRyxXQUFXLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDdkIsV0FBVyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDO1FBQzVCLHdCQUF3QjtRQUN4QixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsQ0FBQztRQUNuRyx5QkFBeUI7UUFDekIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLENBQUM7UUFDckcsd0JBQXdCO1FBQ3hCLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxDQUFDO1FBQ25HLHlCQUF5QjtRQUN6QixDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsQ0FBQztRQUNyRyxxQ0FBcUM7UUFDckMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQzdCLDBIQUEwSDtZQUMxSCw0SEFBNEg7WUFDNUgsd0JBQXdCO1lBQ3hCLDBIQUEwSDtZQUMxSCx5QkFBeUI7WUFDekIsNkhBQTZIO1NBQ2hJO1FBQ0QseUJBQXlCO1FBQ3pCLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLFNBQVMsQ0FBQztZQUNoQyxNQUFNLEVBQUUsb0JBQW9CO1lBQzVCLFdBQVcsRUFBRSxNQUFNO1lBQ25CLE1BQU0sRUFBRSxLQUFLO1lBQ2IsTUFBTSxFQUFFLE1BQU07U0FDakIsQ0FBQyxDQUFDO1FBQ0gsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxFQUFFLENBQUM7WUFDbEUsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsUUFBUTs7OztZQUFFLENBQUMsS0FBSyxFQUFFLEVBQUU7Z0JBQ3pFLGlCQUFpQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNsQyxDQUFDLEVBQUMsQ0FBQztZQUNILENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTTs7OztZQUFDLENBQUMsS0FBSyxFQUFFLEVBQUU7Z0JBRW5FLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsS0FBSyxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUUsS0FBSyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO29CQUNwSyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQztvQkFDekIsSUFBSSxDQUFDLFlBQVksR0FBRyw0QkFBNEIsQ0FBQztpQkFDcEQ7cUJBQU07b0JBQ0gsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUU7d0JBQ3BCLElBQUksQ0FBQyxZQUFZLEdBQUcsNEJBQTRCLENBQUM7cUJBQ3BEO2lCQUNKO1lBQ0wsQ0FBQyxFQUFDLENBQUM7U0FDTjtRQUNELElBQUksSUFBSSxDQUFDLEdBQUcsSUFBSSxPQUFNLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLFFBQVEsRUFBRTtZQUMzQyxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FDbkIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDO2lCQUNqRCxTQUFTOzs7O1lBQUMsQ0FBQyxTQUFjLEVBQUUsRUFBRTtnQkFDMUIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDbEQsQ0FBQzs7OztZQUFFLENBQUMsS0FBSyxFQUFFLEVBQUU7Z0JBQ1QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDN0IsQ0FBQyxFQUFDLENBQ1QsQ0FBQztTQUNMO2FBQU07WUFDSCxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7OztzQkFFUixPQUFPLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQztnQkFDOUUsSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLGdCQUFnQixDQUFDLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFDbkYsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDL0Q7U0FDSjtRQUVELElBQUksSUFBSSxDQUFDLG1CQUFtQixFQUFFO1lBQzFCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1NBQ25DO2FBQU07WUFDSCxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQztTQUNyQztRQUVELENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsY0FBYyxHQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7Ozs7UUFBQyxDQUFDLEtBQUssRUFBRSxFQUFFOztrQkFDckMsS0FBSyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2hFLElBQUksS0FBSyxDQUFDLE1BQU0sS0FBSyxLQUFLLEVBQUU7Z0JBQ3hCLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDO2FBQ3JDO1FBQ0wsQ0FBQyxFQUFDLENBQUMsQ0FBQztRQUNKLHVCQUF1QjtRQUN2QixJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNmLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7Z0JBQzFDLEtBQUssRUFBRSxNQUFNO2dCQUNiLE1BQU0sRUFBRSxNQUFNO2FBQ2pCLENBQUMsQ0FBQztTQUNOO0lBQ0wsQ0FBQzs7OztJQUVELFdBQVc7UUFDUCw0QkFBNEI7UUFDNUIseUNBQXlDO1FBQ3pDLG1DQUFtQztRQUNuQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLGNBQWMsR0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDdEMsSUFBSSxDQUFDLGFBQWEsR0FBRyx5QkFBeUIsQ0FBRSxJQUFJLENBQUMsYUFBYSxDQUFFLENBQUM7SUFDekUsQ0FBQzs7Ozs7SUFFTSxRQUFRLENBQUMsS0FBVTtRQUN0QixJQUFJLEtBQUssQ0FBQyxTQUFTLEVBQUU7WUFDakIsSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLG1CQUFBLEtBQUssQ0FBQyxTQUFTLEVBQXFCLENBQUMsQ0FBQztZQUM3RCxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7WUFDOUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxjQUFjLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUM7O2tCQUM5RCxRQUFRLEdBQUcsbUJBQUEsQ0FBQyxtQkFBQSxLQUFLLENBQUMsU0FBUyxDQUFDLFFBQVEsRUFBd0IsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBZTtZQUMvRixvREFBb0Q7WUFDcEQsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ25GLEtBQUssQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxHQUFHLElBQUksQ0FBQztTQUNsRDtJQUNMLENBQUM7Ozs7SUFFTSxPQUFPO1FBQ1YsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUM5QixDQUFDOzs7OztJQU1NLGVBQWU7UUFDbEIsSUFBSSxXQUFXLENBQUMsS0FBSyxLQUFLLElBQUksQ0FBQyxFQUFFLEVBQUU7WUFDL0IsV0FBVyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ3ZCLFdBQVcsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQztZQUM1QixzQkFBc0I7WUFDdEIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1NBQ3hGO0lBQ0wsQ0FBQzs7Ozs7O0lBS00sV0FBVztRQUNkLHFDQUFxQztRQUNyQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7U0FDaEM7UUFDRCxpSUFBaUk7UUFDakksbUlBQW1JO0lBRXZJLENBQUM7Ozs7SUFFTSxRQUFRO1FBQ1gsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQztJQUN4QyxDQUFDOzs7Ozs7Ozs7O0lBTU0sY0FBYyxDQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsR0FBWSxFQUFFLFNBQWUsRUFBRSxJQUFVO1FBQzVFLElBQUksQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDO1FBQ2YsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUM7UUFDakIsSUFBSSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUM7UUFDckIsSUFBSSxDQUFDLGFBQWEsR0FBRyxPQUFPLENBQUM7UUFDN0IsSUFBSSxTQUFTLEVBQUU7WUFDWCxJQUFJLENBQUMsT0FBTyxHQUFHLFNBQVMsQ0FBQztTQUM1QjtJQUNMLENBQUM7Ozs7O0lBS00sY0FBYzs7Y0FDWCxPQUFPLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDOztjQUNwRixZQUFZLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7UUFDbkYsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUU7WUFDcEIsSUFBSSxDQUFDLFlBQVksR0FBRyw0QkFBNEIsQ0FBQztZQUNqRCxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDM0MsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3pDLElBQUksQ0FBQyxZQUFZLEdBQUcsS0FBSyxDQUFDO1NBQzdCO2FBQU07WUFDSCxJQUFJLENBQUMsWUFBWSxHQUFHLDRCQUE0QixDQUFDO1lBQ2pELENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ3RDLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDO1NBQzVCO0lBQ0wsQ0FBQzs7Ozs7Ozs7SUFFTSxTQUFTLENBQUMsQ0FBUyxFQUFFLENBQVMsRUFBRSxLQUFhLEVBQUUsTUFBYztRQUNoRSxJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQztRQUNuQixJQUFJLENBQUMsTUFBTSxHQUFHLE1BQU0sQ0FBQztRQUNyQixJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUMzQixDQUFDOzs7Ozs7SUFFTSxXQUFXLENBQUMsQ0FBUyxFQUFFLENBQVM7UUFDbkMsSUFBSSxDQUFDLG1CQUFtQixHQUFHLElBQUksQ0FBQztRQUNoQyxJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFDLENBQUM7SUFDakMsQ0FBQzs7Ozs7SUFFTSxXQUFXLENBQUMsUUFBYTtRQUM1QixJQUFJLENBQUMsbUJBQW1CLEdBQUcsSUFBSSxDQUFDO1FBQ2hDLElBQUksSUFBSSxDQUFDLE1BQU0sSUFBSSxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQzNCLElBQUksT0FBTyxRQUFRLEtBQUssUUFBUSxFQUFFO2dCQUM5QixRQUFRLFFBQVEsRUFBRTtvQkFDZCxLQUFLLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQzt3QkFDbkIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUM7d0JBQzNCLElBQUksQ0FBQyxRQUFRLEdBQUcsRUFBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUMsQ0FBQzt3QkFDakMsTUFBTTtxQkFDVDtvQkFDRCxLQUFLLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQzs7OEJBQ2YsSUFBSSxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFOzs4QkFDeEUsSUFBSSxHQUFHLEdBQUc7d0JBQ2hCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7d0JBQ25DLE1BQU07cUJBQ1Q7b0JBQ0QsS0FBSyxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUM7OzhCQUNkLElBQUksR0FBRyxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRTs7OEJBQ3JELElBQUksR0FBRyxHQUFHO3dCQUNoQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQzt3QkFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBQyxDQUFDO3dCQUNuQyxNQUFNO3FCQUNUO29CQUNELEtBQUssUUFBUSxDQUFDLFVBQVUsQ0FBQyxDQUFDOzs4QkFDaEIsSUFBSSxHQUFHLEdBQUc7OzhCQUNWLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7d0JBQzFFLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7d0JBQ25DLE1BQU07cUJBQ1Q7b0JBQ0QsS0FBSyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUM7OzhCQUNsQixJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7OzhCQUN4RSxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFlBQVksR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFO3dCQUMxRSxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQzt3QkFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBQyxDQUFDO3dCQUNuQyxNQUFNO3FCQUNUO29CQUNELEtBQUssUUFBUSxDQUFDLFdBQVcsQ0FBQyxDQUFDOzs4QkFDakIsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRTs7OEJBQ2xFLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7d0JBQzFFLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7d0JBQ25DLE1BQU07cUJBQ1Q7b0JBQ0QsS0FBSyxRQUFRLENBQUMsVUFBVSxDQUFDLENBQUM7OzhCQUNoQixJQUFJLEdBQUcsR0FBRzs7OEJBQ1YsSUFBSSxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFlBQVksR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFO3dCQUNoRixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQzt3QkFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBQyxDQUFDO3dCQUNuQyxNQUFNO3FCQUNUO29CQUNELEtBQUssUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDOzs4QkFDWixJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7OzhCQUN4RSxJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7d0JBQ2hGLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7d0JBQ25DLE1BQU07cUJBQ1Q7b0JBQ0QsS0FBSyxRQUFRLENBQUMsV0FBVyxDQUFDLENBQUM7OzhCQUNqQixJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFOzs4QkFDbEUsSUFBSSxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFlBQVksR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFO3dCQUNoRixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQzt3QkFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBQyxDQUFDO3dCQUNuQyxNQUFNO3FCQUNUO29CQUNELE9BQU8sQ0FBQyxDQUFDOzs4QkFDQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsV0FBVyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7OzhCQUN4RSxJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7d0JBQ2hGLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO3dCQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7d0JBQ25DLE1BQU07cUJBQ1Q7aUJBQ0o7YUFDSjtpQkFBTTtnQkFDSCxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUN6QyxJQUFJLENBQUMsUUFBUSxHQUFHLFFBQVEsQ0FBQzthQUM1QjtTQUNKO2FBQU07O2tCQUNHLEtBQUssR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxHQUFHLEVBQUU7O2tCQUNsRixNQUFNLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxDQUFDOztrQkFDbkYsSUFBSSxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUU7O2tCQUMzRCxJQUFJLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRTtZQUNuRSxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztZQUM3QixJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFDLENBQUM7U0FDdEM7SUFDTCxDQUFDOzs7Ozs7O0lBT00sV0FBVztRQUNkLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztJQUM3QixDQUFDOzs7OztJQUtNLEtBQUs7UUFDUixJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDbEMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQ25DLENBQUM7Ozs7Ozs7SUFNTyxXQUFXLENBQUMsR0FBRzs7WUFDZixhQUFhLEdBQVcsQ0FBQzs7Y0FDdkIsSUFBSSxHQUFRLEVBQUU7UUFFcEIsSUFBSTs7a0JBQ00sRUFBRSxHQUFXLEdBQUcsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDO1lBQ25DLElBQUksRUFBRSxLQUFLLENBQUMsQ0FBQyxFQUFFO2dCQUNYLGFBQWEsR0FBRyxFQUFFLENBQUM7O3NCQUNiLEtBQUssR0FBVyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7O3NCQUNsQyxNQUFNLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUM7Z0JBQy9CLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO29CQUNwQyxhQUFhLEdBQUcsRUFBRSxDQUFDOzswQkFDYixLQUFLLEdBQVcsTUFBTSxDQUFDLENBQUMsQ0FBQzs7MEJBQ3pCLFNBQVMsR0FBRyxLQUFLLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQztvQkFDbEMsSUFBSSxTQUFTLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTt3QkFDeEIsYUFBYSxHQUFHLEVBQUUsQ0FBQzs7OEJBQ2IsR0FBRyxHQUFHLFNBQVMsQ0FBQyxDQUFDLENBQUM7OzhCQUNsQixHQUFHLEdBQUcsU0FBUyxDQUFDLENBQUMsQ0FBQzt3QkFDeEIsYUFBYSxHQUFHLEVBQUUsQ0FBQzt3QkFDbkIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQztxQkFDbkI7aUJBQ0o7YUFDSjtTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixPQUFPLENBQUMsS0FBSyxDQUFDLDRDQUE0QyxFQUFFLEtBQUssQ0FBQyxDQUFDO1NBQ3RFO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQzs7Ozs7Ozs7SUFFTyxhQUFhLENBQUksR0FBVyxFQUFFLFFBQW1COztZQUNqRCxTQUFTLEdBQUcsRUFBRTtRQUNsQixJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxRQUFRLEVBQUU7WUFDM0IsU0FBUyxHQUFHLEdBQUcsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQztTQUMxRDs7Y0FDSyxRQUFRLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxTQUFTO2FBQ3JDLElBQUk7Ozs7UUFBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxTQUFTLEVBQUM7O2NBQ2hDLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQzthQUN2RCxJQUFJOzs7O1FBQUMsQ0FBQyxlQUFlLEVBQUUsRUFBRTs7a0JBQ2hCLFNBQVMsR0FBRyxlQUFlLENBQUMsTUFBTSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQzs7O2tCQUV4RSxvQkFBb0IsR0FBRyxTQUFTLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxTQUFTLENBQUM7WUFDOUQsSUFBSSxDQUFDLGFBQWEsQ0FBQyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO1lBQ3JFLGlDQUFpQztZQUNqQyxPQUFPLFNBQVMsQ0FBQyx3QkFBd0IsQ0FBQyx1QkFBdUIsQ0FBSSxvQkFBb0IsQ0FBQyxDQUFDO1FBQy9GLENBQUMsRUFBQztRQUNOLE9BQU8sV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQzFCLENBQUM7Ozs7Ozs7O0lBUU8sYUFBYSxDQUFDLEtBQVUsRUFBRSxJQUFTOztjQUNqQyxVQUFVLEdBQVUsTUFBTSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQztRQUMxRCxJQUFJLElBQUksRUFBRTs7a0JBQ0EsSUFBSSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDO1lBQzlCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsR0FBRyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7O3NCQUM3QyxHQUFHLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQztnQkFDbkIsSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLEdBQUcsQ0FBQyxFQUFFO29CQUMxQixJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lCQUN6QjtxQkFBTTtvQkFDSCxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lCQUMxQjthQUNKO1NBQ0o7UUFDRCxLQUFLLENBQUMsYUFBYSxDQUFDLEdBQUcsSUFBSSxDQUFDO1FBQzVCLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDdEMsVUFBVSxDQUFDLE9BQU87Ozs7UUFBQyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ3hCLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDN0IsQ0FBQyxFQUFDLENBQUM7SUFDUCxDQUFDOzs7WUF4Z0JKLFNBQVMsU0FBQztnQkFDUCxRQUFRLEVBQUUsYUFBYTtnQkFDdkIsUUFBUSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQXVCVDt5QkFDUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0tBK0RSO2FBQ0o7Ozs7WUFsSDZELFVBQVU7WUFPaEUsYUFBYTs7OzRCQWtJaEIsU0FBUyxTQUFDLE9BQU87NEJBQ2pCLFNBQVMsU0FBQyxlQUFlLEVBQUUsRUFBQyxJQUFJLEVBQUUsVUFBVSxFQUFDOzs7O0lBdEI5Qyw0QkFBMEI7O0lBQzFCLDhCQUErQjs7SUFDL0IsNEJBQXFCOztJQUNyQiw2QkFBc0I7O0lBQ3RCLCtCQUFvQzs7SUFDcEMsK0JBQXFCOztJQUNyQixnQ0FBc0I7O0lBQ3RCLCtCQUFxQjs7SUFDckIsZ0NBQXNCOztJQUN0QixtQ0FBMkI7O0lBQzNCLG1DQUEyQjs7SUFDM0IsaUNBQXlCOztJQUN6Qiw0QkFBdUM7O0lBQ3ZDLDJCQUFpQjs7SUFDakIseUJBQWtCOztJQUNsQixrQ0FBdUI7O0lBQ3ZCLG1DQUFtRDs7SUFDbkQsOEJBQXlDOztJQUN6Qyw2QkFBbUI7O0lBQ25CLDBCQUFtQjs7SUFDbkIsOEJBQWdDOztJQUNoQyxvQ0FBMkM7O0lBQzNDLG9DQUFnRjs7Ozs7SUFDaEYsbUNBQXNDOzs7OztJQUN0Qyw4QkFBcUI7Ozs7O0lBQ3JCLG9DQUFxQzs7Ozs7SUFDckMscUNBQTRCOzs7OztJQUM1QiwwQ0FBb0M7Ozs7O0lBQ3BDLDZCQUF1Qjs7Ozs7SUFDdkIsNkJBQW9COzs7OztJQUNwQixvQ0FBMkM7Ozs7O0lBQy9CLDhCQUEyQjs7Ozs7SUFBRSxpQ0FBaUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0NvbXBvbmVudCwgQ29tcG9uZW50RmFjdG9yeSwgQ29tcG9uZW50UmVmLCBEaXJlY3RpdmUsIEVsZW1lbnRSZWYsIEVtYmVkZGVkVmlld1JlZiwgRXZlbnRFbWl0dGVyLCBJbmplY3RvciwgT25EZXN0cm95LCBPbkluaXQsIFZpZXdDaGlsZCwgVmlld0NvbnRhaW5lclJlZix9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xyXG5pbXBvcnQgJ2pxdWVyeS11aS1kaXN0L2pxdWVyeS11aSc7XHJcbmltcG9ydCB7UG9zaXRpb24sIFdpbmRvd1V0aWxzfSBmcm9tICcuLi91dGlscy93aW5kb3ctdXRpbHMnO1xyXG5pbXBvcnQge1dpbmRvd01hbmFnZXJ9IGZyb20gJy4uL21hbmFnZXJzL3dpbmRvdy1tYW5hZ2VyLnNlcnZpY2UnO1xyXG5pbXBvcnQge2Zyb21Qcm9taXNlfSBmcm9tIFwicnhqcy1jb21wYXQvb2JzZXJ2YWJsZS9mcm9tUHJvbWlzZVwiO1xyXG5pbXBvcnQge09ic2VydmFibGUsIFN1YnNjcmlwdGlvbn0gZnJvbSBcInJ4anNcIjtcclxuaW1wb3J0IHtUYWJDaGFuZ2UsIFRhYkNsb3NlLCBXaW5kb3dSZXNpemVFdmVudH0gZnJvbSBcIi4uL2V2ZW50cy9zd3QtZXZlbnRzLm1vZHVsZVwiO1xyXG5pbXBvcnQge0NvbW1vblNlcnZpY2V9IGZyb20gXCIuLi8uLi8uLi9jb20vc3dhbGxvdy91dGlscy9jb21tb24uc2VydmljZVwiO1xyXG5pbXBvcnQgeyB1bnN1YnNjcmliZUFsbE9ic2VydmFibGVzIH0gZnJvbSAnLi4vcmVuZGVyZXJzL3V0aWxpdGllcyc7XHJcbmltcG9ydCB7IExvZ2dlciB9IGZyb20gJy4uL2xvZ2dpbmcvbG9nZ2VyLnNlcnZpY2UnO1xyXG4vKiBJbXBvcnQgSlF1ZXJ5IGFuZCBKUXVlcnkgdWkgKi9cclxuZGVjbGFyZSB2YXIgcmVxdWlyZTogYW55O1xyXG4vL2ltcG9ydCAkIGZyb20gJ2pxdWVyeSc7XHJcbmNvbnN0ICQ6IGFueSA9IHJlcXVpcmUoJ2pxdWVyeScpO1xyXG5cclxuLy8gZGVmaW5lIGRpcmVjdGl2ZSB0byBpbmplY3QgZWxlbWVudCBpbiB0aXRsZSB3aW5kb3cuXHJcbkBEaXJlY3RpdmUoe1xyXG4gICAgc2VsZWN0b3I6ICdbd2luSGFuZGxlcl0nXHJcbn0pXHJcbmV4cG9ydCBjbGFzcyBIYW5kbGVyIHtcclxuICAgIGNvbnN0cnVjdG9yKHB1YmxpYyB2aWV3Q29udGFpbmVyUmVmOiBWaWV3Q29udGFpbmVyUmVmKSB7XHJcbiAgICB9XHJcbn1cclxuXHJcbkBDb21wb25lbnQoe1xyXG4gICAgc2VsZWN0b3I6ICdUaXRsZVdpbmRvdycsXHJcbiAgICB0ZW1wbGF0ZTogYFxyXG4gICAgICAgIDxkaXYgY2xhc3M9XCJ3aW5kb3ctb3ZlcmxheVwiIFtuZ0NsYXNzXT1cInsnaGlkZGVuJzogIXZpc2libGUgfVwiPlxyXG4gICAgICAgICAgICA8ZGl2IChtb3VzZWRvd24pPVwib25UaXRsZUJhckNsaWNrKClcIiBjbGFzcz1cIndpbmRvdy1jb250YWluZXJcIiAgW3N0eWxlLndpZHRoLnB4XT1cIndpZHRoXCJcclxuICAgICAgICAgICAgICAgICBbc3R5bGUuaGVpZ2h0LnB4XT1cImhlaWdodFwiXHJcbiAgICAgICAgICAgICAgICAgW3N0eWxlLnRvcC5weF09XCJ0aGlzLnBvc2l0aW9uLnlcIiBbc3R5bGUubGVmdC5weF09XCJ0aGlzLnBvc2l0aW9uLnhcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgKGRibGNsaWNrKT1cIm1pbmltaXplV2luZG93KClcIiBjbGFzcz1cIndpbmRvdy1oZWFkaW5nXCIgW2hpZGRlbl09XCIhc2hvd0hlYWRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDx0YWJsZSB3aWR0aD1cIjEwMCVcIiBib3JkZXI9XCIwXCIgY2VsbHBhZGRpbmc9XCIwXCIgY2VsbHNwYWNpbmc9XCIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ciB3aWR0aD1cIjEwMCVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT1cInBhZGRpbmctbGVmdDogNXB4OyBib3JkZXI6IG5vbmUgIWltcG9ydGFudFwiPnt7IHRpdGxlIH19PC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT1cImJvcmRlcjogbm9uZSAhaW1wb3J0YW50XCIgd2lkdGg9XCI1MFwiIGFsaWduPVwicmlnaHRcIiAqbmdJZj1cInNob3dDb250cm9sc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgW3NyY109XCJtaW5pbWl6ZUljb25cIiAoY2xpY2spPVwibWluaW1pemVXaW5kb3coKVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgKGNsaWNrKT1cImNsb3NlKClcIiBjbGFzcz1cImNsb3NlYnRuXCIgc3JjPVwiYXNzZXRzL2ltYWdlcy9jbG9zZUJ1dHRvbi5wbmdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICAgICAgPC90YWJsZT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiAgW2NsYXNzLndpbmRvdy1ib2R5XT1cInNob3dIZWFkZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgW2NsYXNzLndpbmRvdy1ib2R5RnVsbF09XCIhc2hvd0hlYWRlclwiICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPG5nLXRlbXBsYXRlIHdpbkhhbmRsZXI+PC9uZy10ZW1wbGF0ZT5cclxuICAgICAgICAgICAgICAgICAgICA8bmctdGVtcGxhdGUgI21sb2FkZXJPdXRsZXQ+PC9uZy10ZW1wbGF0ZT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgIGAsXHJcbiAgICBzdHlsZXM6IFtgXHJcbiAgICAgICAgLndpbmRvdy1vdmVybGF5IHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAuMik7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgICAgICAgICAgdG9wOiAwO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLndpbmRvdy1jb250YWluZXIge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICAgICAgICAgIGxlZnQ6IDMwJTtcclxuICAgICAgICAgICAgdG9wOiAyMCU7XHJcbiAgICAgICAgICAgIHdpZHRoOiBhdXRvO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICAgICAgICAgIGJvcmRlci10b3A6IG5vbmU7XHJcbiAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDhweCBzb2xpZCAjMzY5O1xyXG4gICAgICAgICAgICBib3JkZXItcmlnaHQ6IDhweCBzb2xpZCAjMzY5O1xyXG4gICAgICAgICAgICBib3JkZXItbGVmdDogOHB4IHNvbGlkICMzNjk7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2NjZWNmZjtcclxuICAgICAgICAgICAgbWluLXdpZHRoOiA1NXB4O1xyXG4gICAgICAgICAgICBtaW4taGVpZ2h0OiAyOXB4O1xyXG4gICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLndpbmRvdy1oZWFkaW5nIHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIGhlaWdodDogMjlweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM2OTtcclxuICAgICAgICAgICAgY29sb3I6ICNGRkY7XHJcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwcHg7XHJcbiAgICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICAgICAgICAgIGN1cnNvcjogZGVmYXVsdDtcclxuICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTFweDtcclxuICAgICAgICAgICAgZm9udC1mYW1pbHk6IHZlcmRhbmEsIGhhbHZhdGljYSwgc2Fucy1zZXJpZjtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGRlcjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC53aW5kb3ctYm9keSB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDMwcHgpO1xyXG4gICAgICAgICAgICBvdmVyZmxvdzogYXV0bztcclxuICAgICAgICB9XHJcbiAgICAgICAgLndpbmRvdy1ib2R5RnVsbCB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBhdXRvO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaW1nOmhvdmVyIHtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmNsb3NlYnRuIHtcclxuICAgICAgICAgICAgbWFyZ2luOiAtMnB4IDBweCAwcHggNXB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICBgXVxyXG59KVxyXG5leHBvcnQgY2xhc3MgVGl0bGVXaW5kb3cgaW1wbGVtZW50cyBPbkluaXQsIE9uRGVzdHJveSB7XHJcbiAgICBwdWJsaWMgdGFiaWQ6IHN0cmluZyA9IFwiXCI7XHJcbiAgICBwdWJsaWMgdmlzaWJsZTogYm9vbGVhbiA9IHRydWU7XHJcbiAgICBwdWJsaWMgd2lkdGg6IHN0cmluZztcclxuICAgIHB1YmxpYyBoZWlnaHQ6IHN0cmluZztcclxuICAgIHB1YmxpYyBwb3NpdGlvbjogYW55ID0ge3g6IDAsIHk6IDB9O1xyXG4gICAgcHVibGljIG1heFdpZHRoID0gJyc7XHJcbiAgICBwdWJsaWMgbWF4SGVpZ2h0ID0gJyc7XHJcbiAgICBwdWJsaWMgbWluV2lkdGggPSAnJztcclxuICAgIHB1YmxpYyBtaW5IZWlnaHQgPSAnJztcclxuICAgIHB1YmxpYyBlbmFibGVSZXNpemUgPSB0cnVlO1xyXG4gICAgcHVibGljIHNob3dDb250cm9scyA9IHRydWU7XHJcbiAgICBwdWJsaWMgc2hvd0hlYWRlciA9IHRydWU7XHJcbiAgICBwdWJsaWMgdGl0bGU6IHN0cmluZyA9ICdEZWZhdWx0IHRpdGxlJztcclxuICAgIHB1YmxpYyBkYXRhOiBhbnk7XHJcbiAgICBwdWJsaWMgaWQ6IHN0cmluZztcclxuICAgIHB1YmxpYyBsYXlvdXRPcmRlciA9IDE7XHJcbiAgICBwdWJsaWMgbWluaW1pemVJY29uID0gJ2Fzc2V0cy9pbWFnZXMvbWluaW1pemUucG5nJztcclxuICAgIHB1YmxpYyBvbkNsb3NlID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XHJcbiAgICBwdWJsaWMgcmVzdWx0OiBhbnk7XHJcbiAgICBwdWJsaWMgdXJsOiBzdHJpbmc7XHJcbiAgICBwdWJsaWMgaXNNb2RhbDogYm9vbGVhbiA9IGZhbHNlO1xyXG4gICAgQFZpZXdDaGlsZChIYW5kbGVyKSBjb250ZW50SG9sZGVyOiBIYW5kbGVyO1xyXG4gICAgQFZpZXdDaGlsZChcIm1sb2FkZXJPdXRsZXRcIiwge3JlYWQ6IEVsZW1lbnRSZWZ9KSBtbG9hZGVyT3V0bGV0OiBWaWV3Q29udGFpbmVyUmVmO1xyXG4gICAgcHJpdmF0ZSB3aW5kb3dTdGF0dGU6IGJvb2xlYW4gPSBmYWxzZTtcclxuICAgIHByaXZhdGUgY29udGVudDogYW55O1xyXG4gICAgcHJpdmF0ZSB3aW5kb3dTZXJ2aWNlOiBXaW5kb3dNYW5hZ2VyO1xyXG4gICAgcHJpdmF0ZSBjaGlsZGNvbXBvbmVudDogYW55O1xyXG4gICAgcHJpdmF0ZSBlbmZvcmNlVXNlclBvc2l0aW9uID0gZmFsc2U7XHJcbiAgICBwcml2YXRlIGxvZ2dlcjogTG9nZ2VyO1xyXG4gICAgcHJpdmF0ZSBwYXJlbnQ6IGFueTtcclxuICAgIHByaXZhdGUgc3Vic2NyaXB0aW9uczogU3Vic2NyaXB0aW9uW10gPSBbXTtcclxuICAgIGNvbnN0cnVjdG9yKHByaXZhdGUgZWxlbWVudDogRWxlbWVudFJlZiwgcHJpdmF0ZSBjb21tb25TZXJ2OiBDb21tb25TZXJ2aWNlKSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIgPSBuZXcgTG9nZ2VyKFwiXCIsIGNvbW1vblNlcnYuaHR0cGNsaWVudCk7XHJcbiAgICB9XHJcblxyXG4gICAgZ2V0IGluaXRYKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLnBvc2l0aW9uLng7XHJcbiAgICB9XHJcblxyXG4gICAgc2V0IGluaXRYKHZhbHVlOiBudW1iZXIpIHtcclxuICAgICAgICB0aGlzLnBvc2l0aW9uLnggPSB2YWx1ZTtcclxuICAgIH1cclxuXHJcbiAgICBnZXQgaW5pdFkoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMucG9zaXRpb24ueTtcclxuICAgIH1cclxuXHJcbiAgICBzZXQgaW5pdFkodmFsdWU6IG51bWJlcikge1xyXG4gICAgICAgIHRoaXMucG9zaXRpb24ueSA9IHZhbHVlO1xyXG4gICAgfVxyXG4gICAgbmdPbkluaXQoKSB7XHJcbiAgICAgICAgdGhpcy5zdWJzY3JpcHRpb25zLnB1c2goXHJcbiAgICAgICAgICAgIFRhYkNoYW5nZS5zdWJzY3JpYmUoKGlkKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAodGhpcy50YWJpZCA9PT0gaWQpIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSB0cnVlO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgKTtcclxuICAgICAgICB0aGlzLnN1YnNjcmlwdGlvbnMucHVzaChcclxuICAgICAgICAgIFRhYkNsb3NlLnN1YnNjcmliZSgoaWQpID0+IHtcclxuICAgICAgICAgICAgaWYgKHRoaXMudGFiaWQgPT09IGlkKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmNsb3NlKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgKTtcclxuICAgICAgICAvLyBzZXQgd2luZG93IHotaW5kZXguXHJcbiAgICAgICAgJCgkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5jaGlsZHJlblswXS5jaGlsZHJlblswXSkuY3NzKCd6LWluZGV4JywgV2luZG93VXRpbHMud2luT3JkZXIpO1xyXG4gICAgICAgIFdpbmRvd1V0aWxzLndpbk9yZGVyKys7XHJcbiAgICAgICAgV2luZG93VXRpbHMud2luaWQgPSB0aGlzLmlkO1xyXG4gICAgICAgIC8vIHNldCB3aW5kb3cgbWF4IHdpZHRoLlxyXG4gICAgICAgICQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0pLmNzcygnbWF4LXdpZHRoJywgdGhpcy5tYXhXaWR0aCArICdweCcpO1xyXG4gICAgICAgIC8vIHNldCB3aW5kb3cgbWF4IGhlaWdodC5cclxuICAgICAgICAkKCQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdKS5jc3MoJ21heC1oZWlnaHQnLCB0aGlzLm1heEhlaWdodCArICdweCcpO1xyXG4gICAgICAgIC8vIHNldCB3aW5kb3cgbWluIHdpZHRoLlxyXG4gICAgICAgICQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0pLmNzcygnbWluLXdpZHRoJywgdGhpcy5taW5XaWR0aCArICdweCcpO1xyXG4gICAgICAgIC8vIHNldCB3aW5kb3cgbWluIGhlaWdodC5cclxuICAgICAgICAkKCQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdKS5jc3MoJ21pbi1oZWlnaHQnLCB0aGlzLm1pbkhlaWdodCArICdweCcpO1xyXG4gICAgICAgIC8vIHNldCB3aWR0aCBhbmQgaGVpZ2h0IGlmIHVuZGVmaW5lZC5cclxuICAgICAgICBpZiAoIXRoaXMud2lkdGggJiYgIXRoaXMuaGVpZ2h0KSB7XHJcbiAgICAgICAgICAgIC8vIHRoaXMud2lkdGggPSAoTnVtYmVyKCQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXS5jaGlsZHJlblswXS5jaGlsZHJlblsxXSlbMF0pLndpZHRoKCkpICsgMTYpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIC8vIHRoaXMuaGVpZ2h0ID0gKE51bWJlcigkKCQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMV0pWzBdKS5oZWlnaHQoKSkgKyAzOCkudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgLy8gc2V0IHdpbmRvdyBtYXggd2lkdGguXHJcbiAgICAgICAgICAgIC8vIFRPRE8gJCgkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5jaGlsZHJlblswXS5jaGlsZHJlblswXSkuY3NzKCdtYXgtd2lkdGgnLCAoTnVtYmVyKHRoaXMubWF4V2lkdGgpICsgMTYpICsgJ3B4Jyk7XHJcbiAgICAgICAgICAgIC8vIHNldCB3aW5kb3cgbWF4IGhlaWdodC5cclxuICAgICAgICAgICAgLy8gIFRPRE8gJCgkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50KVswXS5jaGlsZHJlblswXS5jaGlsZHJlblswXSkuY3NzKCdtYXgtaGVpZ2h0JywgKE51bWJlcih0aGlzLm1heEhlaWdodCkgKyAzOCkgKyAncHgnKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gbWFrZSB3aW5kb3cgZHJhZ2dhYmxlLlxyXG4gICAgICAgICQoJ2Rpdi53aW5kb3ctY29udGFpbmVyJykuZHJhZ2dhYmxlKHtcclxuICAgICAgICAgICAgaGFuZGxlOiAnZGl2LndpbmRvdy1oZWFkaW5nJyxcclxuICAgICAgICAgICAgY29udGFpbm1lbnQ6ICdib2R5JyxcclxuICAgICAgICAgICAgc2Nyb2xsOiBmYWxzZSxcclxuICAgICAgICAgICAgY3Vyc29yOiAnbW92ZScsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgaWYgKHRoaXMuZW5hYmxlUmVzaXplKSB7XHJcbiAgICAgICAgICAgICQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0pLnJlc2l6YWJsZSgpO1xyXG4gICAgICAgICAgICAkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdKS5vbihcInJlc2l6ZVwiLCAoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgIFdpbmRvd1Jlc2l6ZUV2ZW50LmVtaXQoZXZlbnQpO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXS5jaGlsZHJlblswXSkucmVzaXplKChldmVudCkgPT4ge1xyXG5cclxuICAgICAgICAgICAgICAgIGlmICgkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdKS5oZWlnaHQoKSAhPT0gTnVtYmVyKHRoaXMuaGVpZ2h0KSB8fCAkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS53aWR0aCgpICE9PSBOdW1iZXIodGhpcy53aWR0aCkpIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzLndpbmRvd1N0YXR0ZSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5taW5pbWl6ZUljb24gPSAnYXNzZXRzL2ltYWdlcy9tYXhpbWl6ZS5wbmcnO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5pc01pbmltaXplZCgpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubWluaW1pemVJY29uID0gJ2Fzc2V0cy9pbWFnZXMvbWluaW1pemUucG5nJztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodGhpcy51cmwgJiYgdHlwZW9mKHRoaXMudXJsKSA9PT0gXCJzdHJpbmdcIikge1xyXG4gICAgICAgICAgICB0aGlzLnN1YnNjcmlwdGlvbnMucHVzaChcclxuICAgICAgICAgICAgICAgIHRoaXMubG9hZENvbXBvbmVudCh0aGlzLnVybCwgdGhpcy5jb21tb25TZXJ2LmluamVjdG9yKVxyXG4gICAgICAgICAgICAgICAgICAgIC5zdWJzY3JpYmUoKGNvbXBvbmVudDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubWxvYWRlck91dGxldC5jcmVhdGVDb21wb25lbnQoY29tcG9uZW50KTtcclxuICAgICAgICAgICAgICAgICAgICB9LCAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoZXJyb3IpO1xyXG4gICAgICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLmNvbnRlbnQpIHtcclxuICAgICAgICAgICAgICAgIC8vIGluY2x1ZGUgY29udGVudCBpbiB0aXRsZSB3aW5kb3cgYm9keS5cclxuICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBSZWYgPSB0aGlzLmNvbW1vblNlcnYucmVzb2x2ZXIucmVzb2x2ZUNvbXBvbmVudEZhY3RvcnkodGhpcy5jb250ZW50KTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2hpbGRjb21wb25lbnQgPSB0aGlzLmNvbnRlbnRIb2xkZXIudmlld0NvbnRhaW5lclJlZi5jcmVhdGVDb21wb25lbnQoY29tcFJlZik7XHJcbiAgICAgICAgICAgICAgICB0aGlzLm1hcERhdGFPYmplY3QodGhpcy5jaGlsZGNvbXBvbmVudC5pbnN0YW5jZSwgdGhpcy5kYXRhKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHRoaXMuZW5mb3JjZVVzZXJQb3NpdGlvbikge1xyXG4gICAgICAgICAgICB0aGlzLnNldFBvc2l0aW9uKHRoaXMucG9zaXRpb24pO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuc2V0UG9zaXRpb24oUG9zaXRpb24uQ0VOVEVSKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICQod2luZG93KS5vbihcInJlc2l6ZS50aXRsZVwiK3RoaXMuaWQsKChldmVudCkgPT57XHJcbiAgICAgICAgICAgIGNvbnN0IHBvcHVwID0gdGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF07XHJcbiAgICAgICAgICAgIGlmIChldmVudC50YXJnZXQgIT09IHBvcHVwKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnNldFBvc2l0aW9uKFBvc2l0aW9uLkNFTlRFUik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KSk7XHJcbiAgICAgICAgLy8gc2hvdyBwb3B1cCBhcyBtb2RhbC5cclxuICAgICAgICBpZiAoIXRoaXMuaXNNb2RhbCkge1xyXG4gICAgICAgICAgICAkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdKS5jc3Moe1xyXG4gICAgICAgICAgICAgICAgd2lkdGg6ICdhdXRvJyxcclxuICAgICAgICAgICAgICAgIGhlaWdodDogJ2F1dG8nXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBuZ09uRGVzdHJveSgpOiB2b2lkIHtcclxuICAgICAgICAvLyB0aGlzLmlkID0gdW5kZWZpbmVkOyBUT0RPXHJcbiAgICAgICAgLy8gcGFyZW50QXBwbGljYXRpb24ubG9hZGVySW5mby51cmwgPSBcIlwiO1xyXG4gICAgICAgIC8vIHBhcmVudEFwcGxpY2F0aW9uLmNsZWFyUGFyYW1zKCk7XHJcbiAgICAgICAgJCh3aW5kb3cpLm9mZihcInJlc2l6ZS50aXRsZVwiK3RoaXMuaWQpO1xyXG4gICAgICAgIHRoaXMuc3Vic2NyaXB0aW9ucyA9IHVuc3Vic2NyaWJlQWxsT2JzZXJ2YWJsZXMoIHRoaXMuc3Vic2NyaXB0aW9ucyApO1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhZGRDaGlsZChjaGlsZDogYW55KSB7XHJcbiAgICAgICAgaWYgKGNoaWxkLmNvbXBvbmVudCkge1xyXG4gICAgICAgICAgICB0aGlzLmNoaWxkY29tcG9uZW50ID0gKGNoaWxkLmNvbXBvbmVudCBhcyBDb21wb25lbnRSZWY8YW55Pik7XHJcbiAgICAgICAgICAgIHRoaXMubWFwRGF0YU9iamVjdCh0aGlzLmNoaWxkY29tcG9uZW50Lmluc3RhbmNlLCB0aGlzLmdldFVybFF1ZXJ5KGNoaWxkLnVybCkpO1xyXG4gICAgICAgICAgICB0aGlzLmNvbW1vblNlcnYuYXBwbGljYXRpb25SZWYuYXR0YWNoVmlldyhjaGlsZC5jb21wb25lbnQuaG9zdFZpZXcpO1xyXG4gICAgICAgICAgICBjb25zdCBzZWxlY3RvciA9IChjaGlsZC5jb21wb25lbnQuaG9zdFZpZXcgYXMgRW1iZWRkZWRWaWV3UmVmPGFueT4pLnJvb3ROb2Rlc1swXSBhcyBIVE1MRWxlbWVudDtcclxuICAgICAgICAgICAgLy8gdGhpcy5tbG9hZGVyT3V0bGV0LmVsZW1lbnQuYXBwZW5kQ2hpbGQoc2VsZWN0b3IpO1xyXG4gICAgICAgICAgICAkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuWzFdKS5hcHBlbmQoc2VsZWN0b3IpO1xyXG4gICAgICAgICAgICBjaGlsZC5jb21wb25lbnQuaW5zdGFuY2VbXCJ0aXRsZVdpbmRvd1wiXSA9IHRoaXM7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBkaXNwbGF5KCkge1xyXG4gICAgICAgIHRoaXMud2luZG93U2VydmljZS5zaG93KCk7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCB3aWxsIGJlIGNhbGxlZCB3aGVuIHdpbmRvdyB0aXRsZSBjbGlja2VkLlxyXG4gICAgICogQHBhcmFtIGV2ZW50XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBvblRpdGxlQmFyQ2xpY2soKSB7XHJcbiAgICAgICAgaWYgKFdpbmRvd1V0aWxzLndpbmlkICE9PSB0aGlzLmlkKSB7XHJcbiAgICAgICAgICAgIFdpbmRvd1V0aWxzLndpbk9yZGVyKys7XHJcbiAgICAgICAgICAgIFdpbmRvd1V0aWxzLndpbmlkID0gdGhpcy5pZDtcclxuICAgICAgICAgICAgLy8gc2V0IHdpbmRvdyB6LWluZGV4LlxyXG4gICAgICAgICAgICAkKCQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQpWzBdLmNoaWxkcmVuWzBdKS5jc3MoJ3otaW5kZXgnLCBXaW5kb3dVdGlscy53aW5PcmRlcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIHN5bmNocm9uaXplIHRoZSB0aXRsZSB3aW5kb3dcclxuICAgICAqIGxheW91dC5cclxuICAgICAqL1xyXG4gICAgcHVibGljIHZhbGlkYXRlTm93KCkge1xyXG4gICAgICAgIC8vIHNldCB3aWR0aCBhbmQgaGVpZ2h0IGlmIHVuZGVmaW5lZC5cclxuICAgICAgICBpZiAoIXRoaXMud2lkdGggJiYgIXRoaXMuaGVpZ2h0KSB7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIHRoaXMud2lkdGggPSAoTnVtYmVyKCQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXS5jaGlsZHJlblswXS5jaGlsZHJlblsxXSlbMF0pWzBdLnNjcm9sbFdpZHRoKSArIDE2KS50b1N0cmluZygpO1xyXG4gICAgICAgIC8vIHRoaXMuaGVpZ2h0ID0gKE51bWJlcigkKCQodGhpcy5lbGVtZW50Lm5hdGl2ZUVsZW1lbnQuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5bMV0pWzBdKVswXS5zY3JvbGxIZWlnaHQpICsgMzgpLnRvU3RyaW5nKCk7XHJcblxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRDaGlsZCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5jaGlsZGNvbXBvbmVudC5pbnN0YW5jZTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIHRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gaW5jbHVkZSBjb250ZW50IGluIHRoZSB0aXRsZSB3aW5kb3cuXHJcbiAgICAgKiBAcGFyYW0gY29tcG9uZW50XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBpbmNsdWRlQ29udGVudChwYXJlbnQsIHNlcnZpY2UsIHVybD86IHN0cmluZywgY29tcG9uZW50PzogYW55LCBkYXRhPzogYW55KSB7XHJcbiAgICAgICAgdGhpcy51cmwgPSB1cmw7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcclxuICAgICAgICB0aGlzLnBhcmVudCA9IHBhcmVudDtcclxuICAgICAgICB0aGlzLndpbmRvd1NlcnZpY2UgPSBzZXJ2aWNlO1xyXG4gICAgICAgIGlmIChjb21wb25lbnQpIHtcclxuICAgICAgICAgICAgdGhpcy5jb250ZW50ID0gY29tcG9uZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gbWluaW1pemUgdGhlIGN1cnJlbnQgd2luZG93LlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgbWluaW1pemVXaW5kb3coKSB7XHJcbiAgICAgICAgY29uc3Qgd2luQm9keSA9ICQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uY2hpbGRyZW5bMF0pWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuWzFdO1xyXG4gICAgICAgIGNvbnN0IHdpbkNvbnRhaW5lciA9ICQoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudClbMF0uY2hpbGRyZW5bMF0pWzBdLmNoaWxkcmVuWzBdO1xyXG4gICAgICAgIGlmICh0aGlzLmlzTWluaW1pemVkKCkpIHtcclxuICAgICAgICAgICAgdGhpcy5taW5pbWl6ZUljb24gPSAnYXNzZXRzL2ltYWdlcy9taW5pbWl6ZS5wbmcnO1xyXG4gICAgICAgICAgICAkKHdpbkNvbnRhaW5lcikuY3NzKCdoZWlnaHQnLCB0aGlzLmhlaWdodCk7XHJcbiAgICAgICAgICAgICQod2luQ29udGFpbmVyKS5jc3MoJ3dpZHRoJywgdGhpcy53aWR0aCk7XHJcbiAgICAgICAgICAgIHRoaXMud2luZG93U3RhdHRlID0gZmFsc2U7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhpcy5taW5pbWl6ZUljb24gPSAnYXNzZXRzL2ltYWdlcy9tYXhpbWl6ZS5wbmcnO1xyXG4gICAgICAgICAgICAkKHdpbkNvbnRhaW5lcikuY3NzKCdoZWlnaHQnLCAnMjlweCcpO1xyXG4gICAgICAgICAgICB0aGlzLndpbmRvd1N0YXR0ZSA9IHRydWU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzZXRCb3VuZHMoeDogc3RyaW5nLCB5OiBzdHJpbmcsIHdpZHRoOiBzdHJpbmcsIGhlaWdodDogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy53aWR0aCA9IHdpZHRoO1xyXG4gICAgICAgIHRoaXMuaGVpZ2h0ID0gaGVpZ2h0O1xyXG4gICAgICAgIHRoaXMuc2V0V2luZG93WFkoeCwgeSk7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHNldFdpbmRvd1hZKHg6IHN0cmluZywgeTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5lbmZvcmNlVXNlclBvc2l0aW9uID0gdHJ1ZTtcclxuICAgICAgICB0aGlzLnBvc2l0aW9uID0ge3g6IHgsIHk6IHl9O1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzZXRQb3NpdGlvbihsb2NhdGlvbjogYW55KSB7XHJcbiAgICAgICAgdGhpcy5lbmZvcmNlVXNlclBvc2l0aW9uID0gdHJ1ZTtcclxuICAgICAgICBpZiAodGhpcy5oZWlnaHQgJiYgdGhpcy53aWR0aCkge1xyXG4gICAgICAgICAgICBpZiAodHlwZW9mIGxvY2F0aW9uID09PSAnc3RyaW5nJykge1xyXG4gICAgICAgICAgICAgICAgc3dpdGNoIChsb2NhdGlvbikge1xyXG4gICAgICAgICAgICAgICAgICAgIGNhc2UgUG9zaXRpb24uVE9QTEVGVDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFdpbmRvd1hZKCcwJywgJzAnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvbiA9IHt4OiAnMCcsIHk6ICcwJ307XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBjYXNlIFBvc2l0aW9uLlRPUENFTlRFUjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx4ID0gKChkb2N1bWVudC5ib2R5LmNsaWVudFdpZHRoIC0gTnVtYmVyKHRoaXMud2lkdGgpKSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHkgPSAnMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkodmFseCwgdmFseSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucG9zaXRpb24gPSB7eDogdmFseCwgeTogdmFseX07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBjYXNlIFBvc2l0aW9uLlRPUFJJR0hUOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHggPSAoc2NyZWVuLndpZHRoIC0gTnVtYmVyKHRoaXMud2lkdGgpKS50b1N0cmluZygpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx5ID0gJzAnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFdpbmRvd1hZKHZhbHgsIHZhbHkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uID0ge3g6IHZhbHgsIHk6IHZhbHl9O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBQb3NpdGlvbi5CT1RUT01MRUZUOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHggPSAnMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHkgPSAoZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQgLSBOdW1iZXIodGhpcy5oZWlnaHQpKS50b1N0cmluZygpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFdpbmRvd1hZKHZhbHgsIHZhbHkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uID0ge3g6IHZhbHgsIHk6IHZhbHl9O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBQb3NpdGlvbi5CT1RUT01DRU5URVI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFseCA9ICgoZG9jdW1lbnQuYm9keS5jbGllbnRXaWR0aCAtIE51bWJlcih0aGlzLndpZHRoKSkgLyAyKS50b1N0cmluZygpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx5ID0gKGRvY3VtZW50LmJvZHkuY2xpZW50SGVpZ2h0IC0gTnVtYmVyKHRoaXMuaGVpZ2h0KSkudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRXaW5kb3dYWSh2YWx4LCB2YWx5KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvbiA9IHt4OiB2YWx4LCB5OiB2YWx5fTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNhc2UgUG9zaXRpb24uQk9UVE9NUklHSFQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFseCA9IChkb2N1bWVudC5ib2R5LmNsaWVudFdpZHRoIC0gTnVtYmVyKHRoaXMud2lkdGgpKS50b1N0cmluZygpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx5ID0gKGRvY3VtZW50LmJvZHkuY2xpZW50SGVpZ2h0IC0gTnVtYmVyKHRoaXMuaGVpZ2h0KSkudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRXaW5kb3dYWSh2YWx4LCB2YWx5KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvbiA9IHt4OiB2YWx4LCB5OiB2YWx5fTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNhc2UgUG9zaXRpb24uTEVGVENFTlRFUjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx4ID0gJzAnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx5ID0gKChkb2N1bWVudC5ib2R5LmNsaWVudEhlaWdodCAtIE51bWJlcih0aGlzLmhlaWdodCkpIC8gMikudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRXaW5kb3dYWSh2YWx4LCB2YWx5KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvbiA9IHt4OiB2YWx4LCB5OiB2YWx5fTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNhc2UgUG9zaXRpb24uQ0VOVEVSOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHggPSAoKGRvY3VtZW50LmJvZHkuY2xpZW50V2lkdGggLSBOdW1iZXIodGhpcy53aWR0aCkpIC8gMikudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFseSA9ICgoZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQgLSBOdW1iZXIodGhpcy5oZWlnaHQpKSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkodmFseCwgdmFseSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucG9zaXRpb24gPSB7eDogdmFseCwgeTogdmFseX07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBjYXNlIFBvc2l0aW9uLlJJR0hUQ0VOVEVSOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHggPSAoZG9jdW1lbnQuYm9keS5jbGllbnRXaWR0aCAtIE51bWJlcih0aGlzLndpZHRoKSkudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFseSA9ICgoZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQgLSBOdW1iZXIodGhpcy5oZWlnaHQpKSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkodmFseCwgdmFseSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucG9zaXRpb24gPSB7eDogdmFseCwgeTogdmFseX07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHggPSAoKGRvY3VtZW50LmJvZHkuY2xpZW50V2lkdGggLSBOdW1iZXIodGhpcy53aWR0aCkpIC8gMikudG9TdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFseSA9ICgoZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQgLSBOdW1iZXIodGhpcy5oZWlnaHQpKSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkodmFseCwgdmFseSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucG9zaXRpb24gPSB7eDogdmFseCwgeTogdmFseX07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkobG9jYXRpb24ueCwgbG9jYXRpb24ueSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uID0gbG9jYXRpb247XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zdCB3aWR0aCA9IE51bWJlcigkKHRoaXMuZWxlbWVudC5uYXRpdmVFbGVtZW50LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdKS53aWR0aCgpKSArIDE2O1xyXG4gICAgICAgICAgICBjb25zdCBoZWlnaHQgPSBOdW1iZXIoJCh0aGlzLmVsZW1lbnQubmF0aXZlRWxlbWVudC5jaGlsZHJlblswXS5jaGlsZHJlblswXSkuaGVpZ2h0KCkpICsgODtcclxuICAgICAgICAgICAgY29uc3QgdmFseCA9ICgoZG9jdW1lbnQuYm9keS5jbGllbnRXaWR0aCAtIHdpZHRoKSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbHkgPSAoKGRvY3VtZW50LmJvZHkuY2xpZW50SGVpZ2h0IC0gaGVpZ2h0KSAvIDIpLnRvU3RyaW5nKCk7XHJcbiAgICAgICAgICAgIHRoaXMuc2V0V2luZG93WFkodmFseCwgdmFseSk7XHJcbiAgICAgICAgICAgIHRoaXMucG9zaXRpb24gPSB7eDogdmFseCwgeTogdmFseX07XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogVGhpcyBtZXRob2QgcmV0dXJuIHRoZSB3aW5kb3cgc3RhdGU6XHJcbiAgICAgKiAgdHJ1ZSA6IHdpbmRvdyBtaW5pbWl6ZWQ7XHJcbiAgICAgKiAgZmFsc2U6IHdpbmRvdyBtYXhpbWl6ZWQ7XHJcbiAgICAgKi9cclxuICAgIHB1YmxpYyBpc01pbmltaXplZCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy53aW5kb3dTdGF0dGU7XHJcbiAgICB9XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGNsb3NlIGN1cnJlbnQgd2luZG93LlxyXG4gICAgICovXHJcbiAgICBwdWJsaWMgY2xvc2UoKSB7XHJcbiAgICAgICAgdGhpcy53aW5kb3dTZXJ2aWNlLmNsb3NlKHRoaXMuaWQpO1xyXG4gICAgICAgIHRoaXMub25DbG9zZS5lbWl0KHRoaXMucmVzdWx0KTtcclxuICAgIH1cclxuXHJcbiAgICAvKipcclxuICAgICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gZ2V0IHF1ZXJ5IGZyb20gdXJsLlxyXG4gICAgICogQHBhcmFtIHVybFxyXG4gICAgICovXHJcbiAgICBwcml2YXRlIGdldFVybFF1ZXJ5KHVybCkge1xyXG4gICAgICAgIHZhciBlcnJvckxvY2F0aW9uOiBudW1iZXIgPSAwO1xyXG4gICAgICAgIGNvbnN0IGRhdGE6IGFueSA9IHt9O1xyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCBxbTogbnVtYmVyID0gdXJsLmluZGV4T2YoXCI/XCIpO1xyXG4gICAgICAgICAgICBpZiAocW0gIT09IC0xKSB7XHJcbiAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMTA7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBxdWVyeTogc3RyaW5nID0gdXJsLnN1YnN0cihxbSArIDEpO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcGFyYW1zID0gcXVlcnkuc3BsaXQoXCImXCIpO1xyXG4gICAgICAgICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBwYXJhbXMubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvckxvY2F0aW9uID0gMjA7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGFyYW06IHN0cmluZyA9IHBhcmFtc1tpXTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lVmFsdWUgPSBwYXJhbS5zcGxpdChcIj1cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5hbWVWYWx1ZS5sZW5ndGggPT09IDIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDMwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBrZXkgPSBuYW1lVmFsdWVbMF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbCA9IG5hbWVWYWx1ZVsxXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JMb2NhdGlvbiA9IDQwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhW2tleV0gPSB2YWw7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIlRpdGxlV2luZG93IFsgZ2V0VXJsUXVlcnkgXSBtZXRob2QgZXJyb3IgOlwiLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgfVxyXG5cclxuICAgIHByaXZhdGUgbG9hZENvbXBvbmVudDxUPih1cmw6IHN0cmluZywgaW5qZWN0b3I/OiBJbmplY3Rvcik6IE9ic2VydmFibGU8Q29tcG9uZW50RmFjdG9yeTxUPj4ge1xyXG4gICAgICAgIHZhciBfY29tcF91cmwgPSBcIlwiO1xyXG4gICAgICAgIGlmICh0eXBlb2YgKHVybCkgPT09IFwic3RyaW5nXCIpIHtcclxuICAgICAgICAgICAgX2NvbXBfdXJsID0gdXJsLmluZGV4T2YoXCI/XCIpID8gdXJsLnNwbGl0KFwiP1wiKVswXSA6IHVybDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgbWFuaWZlc3QgPSB0aGlzLmNvbW1vblNlcnYubWFuaWZlc3RzXHJcbiAgICAgICAgICAgIC5maW5kKChtKSA9PiBtLnBhdGggPT09IF9jb21wX3VybCk7XHJcbiAgICAgICAgY29uc3QgcCA9IHRoaXMuY29tbW9uU2Vydi5sb2FkZXIubG9hZChtYW5pZmVzdC5sb2FkQ2hpbGRyZW4pXHJcbiAgICAgICAgICAgIC50aGVuKChuZ01vZHVsZUZhY3RvcnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1vZHVsZVJlZiA9IG5nTW9kdWxlRmFjdG9yeS5jcmVhdGUoaW5qZWN0b3IgfHwgdGhpcy5jb21tb25TZXJ2LmluamVjdG9yKTtcclxuICAgICAgICAgICAgICAgIC8vIFJlYWQgZnJvbSB0aGUgbW9kdWxlUmVmIGluamVjdG9yIGFuZCBsb2NhdGUgdGhlIGR5bmFtaWMgY29tcG9uZW50IHR5cGVcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR5bmFtaWNDb21wb25lbnRUeXBlID0gbW9kdWxlUmVmLmluamVjdG9yLmdldChfY29tcF91cmwpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5tYXBEYXRhT2JqZWN0KGR5bmFtaWNDb21wb25lbnRUeXBlLCB0aGlzLmdldFVybFF1ZXJ5KHRoaXMudXJsKSk7XHJcbiAgICAgICAgICAgICAgICAvLyBSZXNvbHZlIHRoaXMgY29tcG9uZW50IGZhY3RvcnlcclxuICAgICAgICAgICAgICAgIHJldHVybiBtb2R1bGVSZWYuY29tcG9uZW50RmFjdG9yeVJlc29sdmVyLnJlc29sdmVDb21wb25lbnRGYWN0b3J5PFQ+KGR5bmFtaWNDb21wb25lbnRUeXBlKTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuIGZyb21Qcm9taXNlKHApO1xyXG4gICAgfVxyXG5cclxuICAgIC8qKlxyXG4gICAgICogTWFwcyB5b3VyIG9iamVjdCBwYXNzZWQgaW4gdGhlIGNyZWF0aW9uIHRvIGZpZWxkcyBpbiB5b3VyIG93biB3aW5kb3cgY2xhc3Nlc1xyXG4gICAgICogQHBhcmFtIGNvbXBvbmVudFxyXG4gICAgICogQHBhcmFtIHdpbmRvd1xyXG4gICAgICogQHBhcmFtIGRhdGFcclxuICAgICAqL1xyXG4gICAgcHJpdmF0ZSBtYXBEYXRhT2JqZWN0KGNoaWxkOiBhbnksIGRhdGE6IGFueSk6IHZvaWQge1xyXG4gICAgICAgIGNvbnN0IGF0dHJpYnV0ZXM6IGFueVtdID0gT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXModGhpcyk7XHJcbiAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKGRhdGEpO1xyXG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMCwgbGVuZ3RoID0ga2V5cy5sZW5ndGg7IGkgPCBsZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qga2V5ID0ga2V5c1tpXTtcclxuICAgICAgICAgICAgICAgIGlmICh0aGlzLmhhc093blByb3BlcnR5KGtleSkpIHtcclxuICAgICAgICAgICAgICAgICAgICB0aGlzW2tleV0gPSBkYXRhW2tleV07XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNoaWxkW2tleV0gPSBkYXRhW2tleV07XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgY2hpbGRbXCJ0aXRsZVdpbmRvd1wiXSA9IHRoaXM7XHJcbiAgICAgICAgY2hpbGRbXCJwYXJlbnREb2N1bWVudFwiXSA9IHRoaXMucGFyZW50O1xyXG4gICAgICAgIGF0dHJpYnV0ZXMuZm9yRWFjaCgoYXR0cikgPT4ge1xyXG4gICAgICAgICAgICBjaGlsZFthdHRyXSA9IHRoaXNbYXR0cl07XHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcbn1cclxuIl19