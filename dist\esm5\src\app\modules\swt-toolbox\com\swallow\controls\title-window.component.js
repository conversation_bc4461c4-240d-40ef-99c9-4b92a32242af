/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, Directive, ElementRef, EventEmitter, ViewChild, ViewContainerRef, } from '@angular/core';
import 'jquery-ui-dist/jquery-ui';
import { Position, WindowUtils } from '../utils/window-utils';
import { fromPromise } from "rxjs-compat/observable/fromPromise";
import { TabChange, TabClose, WindowResizeEvent } from "../events/swt-events.module";
import { CommonService } from "../../../com/swallow/utils/common.service";
import { unsubscribeAllObservables } from '../renderers/utilities';
import { Logger } from '../logging/logger.service';
//import $ from 'jquery';
/** @type {?} */
var $ = require('jquery');
// define directive to inject element in title window.
var Handler = /** @class */ (function () {
    function Handler(viewContainerRef) {
        this.viewContainerRef = viewContainerRef;
    }
    Handler.decorators = [
        { type: Directive, args: [{
                    selector: '[winHandler]'
                },] }
    ];
    /** @nocollapse */
    Handler.ctorParameters = function () { return [
        { type: ViewContainerRef }
    ]; };
    return Handler;
}());
export { Handler };
if (false) {
    /** @type {?} */
    Handler.prototype.viewContainerRef;
}
var TitleWindow = /** @class */ (function () {
    function TitleWindow(element, commonServ) {
        this.element = element;
        this.commonServ = commonServ;
        this.tabid = "";
        this.visible = true;
        this.position = { x: 0, y: 0 };
        this.maxWidth = '';
        this.maxHeight = '';
        this.minWidth = '';
        this.minHeight = '';
        this.enableResize = true;
        this.showControls = true;
        this.showHeader = true;
        this.title = 'Default title';
        this.layoutOrder = 1;
        this.minimizeIcon = 'assets/images/minimize.png';
        this.onClose = new EventEmitter();
        this.isModal = false;
        this.windowStatte = false;
        this.enforceUserPosition = false;
        this.subscriptions = [];
        this.logger = new Logger("", commonServ.httpclient);
    }
    Object.defineProperty(TitleWindow.prototype, "initX", {
        get: /**
         * @return {?}
         */
        function () {
            return this.position.x;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.position.x = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(TitleWindow.prototype, "initY", {
        get: /**
         * @return {?}
         */
        function () {
            return this.position.y;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.position.y = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    TitleWindow.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        this.subscriptions.push(TabChange.subscribe((/**
         * @param {?} id
         * @return {?}
         */
        function (id) {
            if (_this.tabid === id) {
                _this.visible = true;
            }
            else {
                _this.visible = false;
            }
        })));
        this.subscriptions.push(TabClose.subscribe((/**
         * @param {?} id
         * @return {?}
         */
        function (id) {
            if (_this.tabid === id) {
                _this.close();
            }
        })));
        // set window z-index.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('z-index', WindowUtils.winOrder);
        WindowUtils.winOrder++;
        WindowUtils.winid = this.id;
        // set window max width.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('max-width', this.maxWidth + 'px');
        // set window max height.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('max-height', this.maxHeight + 'px');
        // set window min width.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('min-width', this.minWidth + 'px');
        // set window min height.
        $($(this.element.nativeElement)[0].children[0].children[0]).css('min-height', this.minHeight + 'px');
        // set width and height if undefined.
        if (!this.width && !this.height) {
            // this.width = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0]).width()) + 16).toString();
            // this.height = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0]).height()) + 38).toString();
            // set window max width.
            // TODO $($(this.element.nativeElement)[0].children[0].children[0]).css('max-width', (Number(this.maxWidth) + 16) + 'px');
            // set window max height.
            //  TODO $($(this.element.nativeElement)[0].children[0].children[0]).css('max-height', (Number(this.maxHeight) + 38) + 'px');
        }
        // make window draggable.
        $('div.window-container').draggable({
            handle: 'div.window-heading',
            containment: 'body',
            scroll: false,
            cursor: 'move',
        });
        if (this.enableResize) {
            $(this.element.nativeElement.children[0].children[0]).resizable();
            $(this.element.nativeElement.children[0].children[0]).on("resize", (/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                WindowResizeEvent.emit(event);
            }));
            $(this.element.nativeElement.children[0].children[0]).resize((/**
             * @param {?} event
             * @return {?}
             */
            function (event) {
                if ($(_this.element.nativeElement.children[0].children[0]).height() !== Number(_this.height) || $(_this.element.nativeElement.children[0]).width() !== Number(_this.width)) {
                    _this.windowStatte = true;
                    _this.minimizeIcon = 'assets/images/maximize.png';
                }
                else {
                    if (_this.isMinimized()) {
                        _this.minimizeIcon = 'assets/images/minimize.png';
                    }
                }
            }));
        }
        if (this.url && typeof (this.url) === "string") {
            this.subscriptions.push(this.loadComponent(this.url, this.commonServ.injector)
                .subscribe((/**
             * @param {?} component
             * @return {?}
             */
            function (component) {
                _this.mloaderOutlet.createComponent(component);
            }), (/**
             * @param {?} error
             * @return {?}
             */
            function (error) {
                _this.logger.error(error);
            })));
        }
        else {
            if (this.content) {
                // include content in title window body.
                /** @type {?} */
                var compRef = this.commonServ.resolver.resolveComponentFactory(this.content);
                this.childcomponent = this.contentHolder.viewContainerRef.createComponent(compRef);
                this.mapDataObject(this.childcomponent.instance, this.data);
            }
        }
        if (this.enforceUserPosition) {
            this.setPosition(this.position);
        }
        else {
            this.setPosition(Position.CENTER);
        }
        $(window).on("resize.title" + this.id, ((/**
         * @param {?} event
         * @return {?}
         */
        function (event) {
            /** @type {?} */
            var popup = _this.element.nativeElement.children[0].children[0];
            if (event.target !== popup) {
                _this.setPosition(Position.CENTER);
            }
        })));
        // show popup as modal.
        if (!this.isModal) {
            $(this.element.nativeElement.children[0]).css({
                width: 'auto',
                height: 'auto'
            });
        }
    };
    /**
     * @return {?}
     */
    TitleWindow.prototype.ngOnDestroy = /**
     * @return {?}
     */
    function () {
        // this.id = undefined; TODO
        // parentApplication.loaderInfo.url = "";
        // parentApplication.clearParams();
        $(window).off("resize.title" + this.id);
        this.subscriptions = unsubscribeAllObservables(this.subscriptions);
    };
    /**
     * @param {?} child
     * @return {?}
     */
    TitleWindow.prototype.addChild = /**
     * @param {?} child
     * @return {?}
     */
    function (child) {
        if (child.component) {
            this.childcomponent = ((/** @type {?} */ (child.component)));
            this.mapDataObject(this.childcomponent.instance, this.getUrlQuery(child.url));
            this.commonServ.applicationRef.attachView(child.component.hostView);
            /** @type {?} */
            var selector = (/** @type {?} */ (((/** @type {?} */ (child.component.hostView))).rootNodes[0]));
            // this.mloaderOutlet.element.appendChild(selector);
            $(this.element.nativeElement.children[0].children[0].children[1]).append(selector);
            child.component.instance["titleWindow"] = this;
        }
    };
    /**
     * @return {?}
     */
    TitleWindow.prototype.display = /**
     * @return {?}
     */
    function () {
        this.windowService.show();
    };
    /**
     * This method will be called when window title clicked.
     * @param event
     */
    /**
     * This method will be called when window title clicked.
     * @return {?}
     */
    TitleWindow.prototype.onTitleBarClick = /**
     * This method will be called when window title clicked.
     * @return {?}
     */
    function () {
        if (WindowUtils.winid !== this.id) {
            WindowUtils.winOrder++;
            WindowUtils.winid = this.id;
            // set window z-index.
            $($(this.element.nativeElement)[0].children[0]).css('z-index', WindowUtils.winOrder);
        }
    };
    /**
     * This method is used to synchronize the title window
     * layout.
     */
    /**
     * This method is used to synchronize the title window
     * layout.
     * @return {?}
     */
    TitleWindow.prototype.validateNow = /**
     * This method is used to synchronize the title window
     * layout.
     * @return {?}
     */
    function () {
        // set width and height if undefined.
        if (!this.width && !this.height) {
        }
        // this.width = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0])[0].scrollWidth) + 16).toString();
        // this.height = (Number($($(this.element.nativeElement.children[0].children[0].children[1])[0])[0].scrollHeight) + 38).toString();
    };
    /**
     * @return {?}
     */
    TitleWindow.prototype.getChild = /**
     * @return {?}
     */
    function () {
        return this.childcomponent.instance;
    };
    /**
     * this method is used to include content in the title window.
     * @param component
     */
    /**
     * this method is used to include content in the title window.
     * @param {?} parent
     * @param {?} service
     * @param {?=} url
     * @param {?=} component
     * @param {?=} data
     * @return {?}
     */
    TitleWindow.prototype.includeContent = /**
     * this method is used to include content in the title window.
     * @param {?} parent
     * @param {?} service
     * @param {?=} url
     * @param {?=} component
     * @param {?=} data
     * @return {?}
     */
    function (parent, service, url, component, data) {
        this.url = url;
        this.data = data;
        this.parent = parent;
        this.windowService = service;
        if (component) {
            this.content = component;
        }
    };
    /**
     * This method is used to minimize the current window.
     */
    /**
     * This method is used to minimize the current window.
     * @return {?}
     */
    TitleWindow.prototype.minimizeWindow = /**
     * This method is used to minimize the current window.
     * @return {?}
     */
    function () {
        /** @type {?} */
        var winBody = $($(this.element.nativeElement)[0].children[0])[0].children[0].children[1];
        /** @type {?} */
        var winContainer = $($(this.element.nativeElement)[0].children[0])[0].children[0];
        if (this.isMinimized()) {
            this.minimizeIcon = 'assets/images/minimize.png';
            $(winContainer).css('height', this.height);
            $(winContainer).css('width', this.width);
            this.windowStatte = false;
        }
        else {
            this.minimizeIcon = 'assets/images/maximize.png';
            $(winContainer).css('height', '29px');
            this.windowStatte = true;
        }
    };
    /**
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    TitleWindow.prototype.setBounds = /**
     * @param {?} x
     * @param {?} y
     * @param {?} width
     * @param {?} height
     * @return {?}
     */
    function (x, y, width, height) {
        this.width = width;
        this.height = height;
        this.setWindowXY(x, y);
    };
    /**
     * @param {?} x
     * @param {?} y
     * @return {?}
     */
    TitleWindow.prototype.setWindowXY = /**
     * @param {?} x
     * @param {?} y
     * @return {?}
     */
    function (x, y) {
        this.enforceUserPosition = true;
        this.position = { x: x, y: y };
    };
    /**
     * @param {?} location
     * @return {?}
     */
    TitleWindow.prototype.setPosition = /**
     * @param {?} location
     * @return {?}
     */
    function (location) {
        this.enforceUserPosition = true;
        if (this.height && this.width) {
            if (typeof location === 'string') {
                switch (location) {
                    case Position.TOPLEFT: {
                        this.setWindowXY('0', '0');
                        this.position = { x: '0', y: '0' };
                        break;
                    }
                    case Position.TOPCENTER: {
                        /** @type {?} */
                        var valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        var valy = '0';
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.TOPRIGHT: {
                        /** @type {?} */
                        var valx = (screen.width - Number(this.width)).toString();
                        /** @type {?} */
                        var valy = '0';
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMLEFT: {
                        /** @type {?} */
                        var valx = '0';
                        /** @type {?} */
                        var valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMCENTER: {
                        /** @type {?} */
                        var valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        var valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.BOTTOMRIGHT: {
                        /** @type {?} */
                        var valx = (document.body.clientWidth - Number(this.width)).toString();
                        /** @type {?} */
                        var valy = (document.body.clientHeight - Number(this.height)).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.LEFTCENTER: {
                        /** @type {?} */
                        var valx = '0';
                        /** @type {?} */
                        var valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.CENTER: {
                        /** @type {?} */
                        var valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        var valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    case Position.RIGHTCENTER: {
                        /** @type {?} */
                        var valx = (document.body.clientWidth - Number(this.width)).toString();
                        /** @type {?} */
                        var valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                    default: {
                        /** @type {?} */
                        var valx = ((document.body.clientWidth - Number(this.width)) / 2).toString();
                        /** @type {?} */
                        var valy = ((document.body.clientHeight - Number(this.height)) / 2).toString();
                        this.setWindowXY(valx, valy);
                        this.position = { x: valx, y: valy };
                        break;
                    }
                }
            }
            else {
                this.setWindowXY(location.x, location.y);
                this.position = location;
            }
        }
        else {
            /** @type {?} */
            var width = Number($(this.element.nativeElement.children[0].children[0]).width()) + 16;
            /** @type {?} */
            var height = Number($(this.element.nativeElement.children[0].children[0]).height()) + 8;
            /** @type {?} */
            var valx = ((document.body.clientWidth - width) / 2).toString();
            /** @type {?} */
            var valy = ((document.body.clientHeight - height) / 2).toString();
            this.setWindowXY(valx, valy);
            this.position = { x: valx, y: valy };
        }
    };
    /**
     * This method return the window state:
     *  true : window minimized;
     *  false: window maximized;
     */
    /**
     * This method return the window state:
     *  true : window minimized;
     *  false: window maximized;
     * @return {?}
     */
    TitleWindow.prototype.isMinimized = /**
     * This method return the window state:
     *  true : window minimized;
     *  false: window maximized;
     * @return {?}
     */
    function () {
        return this.windowStatte;
    };
    /**
     * This method is used to close current window.
     */
    /**
     * This method is used to close current window.
     * @return {?}
     */
    TitleWindow.prototype.close = /**
     * This method is used to close current window.
     * @return {?}
     */
    function () {
        this.windowService.close(this.id);
        this.onClose.emit(this.result);
    };
    /**
     * This method is used to get query from url.
     * @param url
     */
    /**
     * This method is used to get query from url.
     * @private
     * @param {?} url
     * @return {?}
     */
    TitleWindow.prototype.getUrlQuery = /**
     * This method is used to get query from url.
     * @private
     * @param {?} url
     * @return {?}
     */
    function (url) {
        /** @type {?} */
        var errorLocation = 0;
        /** @type {?} */
        var data = {};
        try {
            /** @type {?} */
            var qm = url.indexOf("?");
            if (qm !== -1) {
                errorLocation = 10;
                /** @type {?} */
                var query = url.substr(qm + 1);
                /** @type {?} */
                var params = query.split("&");
                for (var i = 0; i < params.length; i++) {
                    errorLocation = 20;
                    /** @type {?} */
                    var param = params[i];
                    /** @type {?} */
                    var nameValue = param.split("=");
                    if (nameValue.length === 2) {
                        errorLocation = 30;
                        /** @type {?} */
                        var key = nameValue[0];
                        /** @type {?} */
                        var val = nameValue[1];
                        errorLocation = 40;
                        data[key] = val;
                    }
                }
            }
        }
        catch (error) {
            console.error("TitleWindow [ getUrlQuery ] method error :", error);
        }
        return data;
    };
    /**
     * @private
     * @template T
     * @param {?} url
     * @param {?=} injector
     * @return {?}
     */
    TitleWindow.prototype.loadComponent = /**
     * @private
     * @template T
     * @param {?} url
     * @param {?=} injector
     * @return {?}
     */
    function (url, injector) {
        var _this = this;
        /** @type {?} */
        var _comp_url = "";
        if (typeof (url) === "string") {
            _comp_url = url.indexOf("?") ? url.split("?")[0] : url;
        }
        /** @type {?} */
        var manifest = this.commonServ.manifests
            .find((/**
         * @param {?} m
         * @return {?}
         */
        function (m) { return m.path === _comp_url; }));
        /** @type {?} */
        var p = this.commonServ.loader.load(manifest.loadChildren)
            .then((/**
         * @param {?} ngModuleFactory
         * @return {?}
         */
        function (ngModuleFactory) {
            /** @type {?} */
            var moduleRef = ngModuleFactory.create(injector || _this.commonServ.injector);
            // Read from the moduleRef injector and locate the dynamic component type
            /** @type {?} */
            var dynamicComponentType = moduleRef.injector.get(_comp_url);
            _this.mapDataObject(dynamicComponentType, _this.getUrlQuery(_this.url));
            // Resolve this component factory
            return moduleRef.componentFactoryResolver.resolveComponentFactory(dynamicComponentType);
        }));
        return fromPromise(p);
    };
    /**
     * Maps your object passed in the creation to fields in your own window classes
     * @param component
     * @param window
     * @param data
     */
    /**
     * Maps your object passed in the creation to fields in your own window classes
     * @private
     * @param {?} child
     * @param {?} data
     * @return {?}
     */
    TitleWindow.prototype.mapDataObject = /**
     * Maps your object passed in the creation to fields in your own window classes
     * @private
     * @param {?} child
     * @param {?} data
     * @return {?}
     */
    function (child, data) {
        var _this = this;
        /** @type {?} */
        var attributes = Object.getOwnPropertyNames(this);
        if (data) {
            /** @type {?} */
            var keys = Object.keys(data);
            for (var i = 0, length_1 = keys.length; i < length_1; i++) {
                /** @type {?} */
                var key = keys[i];
                if (this.hasOwnProperty(key)) {
                    this[key] = data[key];
                }
                else {
                    child[key] = data[key];
                }
            }
        }
        child["titleWindow"] = this;
        child["parentDocument"] = this.parent;
        attributes.forEach((/**
         * @param {?} attr
         * @return {?}
         */
        function (attr) {
            child[attr] = _this[attr];
        }));
    };
    TitleWindow.decorators = [
        { type: Component, args: [{
                    selector: 'TitleWindow',
                    template: "\n        <div class=\"window-overlay\" [ngClass]=\"{'hidden': !visible }\">\n            <div (mousedown)=\"onTitleBarClick()\" class=\"window-container\"  [style.width.px]=\"width\"\n                 [style.height.px]=\"height\"\n                 [style.top.px]=\"this.position.y\" [style.left.px]=\"this.position.x\">\n                <div (dblclick)=\"minimizeWindow()\" class=\"window-heading\" [hidden]=\"!showHeader\">\n                    <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n                        <tr width=\"100%\">\n                            <td style=\"padding-left: 5px; border: none !important\">{{ title }}</td>\n                            <td style=\"border: none !important\" width=\"50\" align=\"right\" *ngIf=\"showControls\">\n                                <img [src]=\"minimizeIcon\" (click)=\"minimizeWindow()\">\n                                <img (click)=\"close()\" class=\"closebtn\" src=\"assets/images/closeButton.png\">\n                            </td>\n                        </tr>\n                    </table>\n                </div>\n                <div  [class.window-body]=\"showHeader\"\n                      [class.window-bodyFull]=\"!showHeader\"  >\n                    <ng-template winHandler></ng-template>\n                    <ng-template #mloaderOutlet></ng-template>\n                </div>\n            </div>\n        </div>\n    ",
                    styles: ["\n        .window-overlay {\n            width: 100%;\n            height: 100%;\n            background-color: rgba(255, 255, 255, .2);\n            position: fixed;\n            top: 0;\n            left: 0;\n        }\n\n        .window-container {\n            position: fixed;\n            left: 30%;\n            top: 20%;\n            width: auto;\n            height: auto;\n            border-top: none;\n            border-bottom: 8px solid #369;\n            border-right: 8px solid #369;\n            border-left: 8px solid #369;\n            border-radius: 5px;\n            background-color: #ccecff;\n            min-width: 55px;\n            min-height: 29px;\n            box-sizing: border-box;\n        }\n\n        .window-heading {\n            width: 100%;\n            height: 29px;\n            background-color: #369;\n            color: #FFF;\n            line-height: 30px;\n            padding: 0px;\n            box-sizing: border-box;\n            cursor: default;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            font-size: 11px;\n            font-family: verdana, halvatica, sans-serif;\n            font-weight: bolder;\n        }\n\n        .window-body {\n            width: 100%;\n            height: calc(100% - 30px);\n            overflow: auto;\n        }\n        .window-bodyFull {\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n        }\n\n        img:hover {\n            cursor: pointer;\n        }\n\n        .closebtn {\n            margin: -2px 0px 0px 5px;\n        }\n\n    "]
                }] }
    ];
    /** @nocollapse */
    TitleWindow.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    TitleWindow.propDecorators = {
        contentHolder: [{ type: ViewChild, args: [Handler,] }],
        mloaderOutlet: [{ type: ViewChild, args: ["mloaderOutlet", { read: ElementRef },] }]
    };
    return TitleWindow;
}());
export { TitleWindow };
if (false) {
    /** @type {?} */
    TitleWindow.prototype.tabid;
    /** @type {?} */
    TitleWindow.prototype.visible;
    /** @type {?} */
    TitleWindow.prototype.width;
    /** @type {?} */
    TitleWindow.prototype.height;
    /** @type {?} */
    TitleWindow.prototype.position;
    /** @type {?} */
    TitleWindow.prototype.maxWidth;
    /** @type {?} */
    TitleWindow.prototype.maxHeight;
    /** @type {?} */
    TitleWindow.prototype.minWidth;
    /** @type {?} */
    TitleWindow.prototype.minHeight;
    /** @type {?} */
    TitleWindow.prototype.enableResize;
    /** @type {?} */
    TitleWindow.prototype.showControls;
    /** @type {?} */
    TitleWindow.prototype.showHeader;
    /** @type {?} */
    TitleWindow.prototype.title;
    /** @type {?} */
    TitleWindow.prototype.data;
    /** @type {?} */
    TitleWindow.prototype.id;
    /** @type {?} */
    TitleWindow.prototype.layoutOrder;
    /** @type {?} */
    TitleWindow.prototype.minimizeIcon;
    /** @type {?} */
    TitleWindow.prototype.onClose;
    /** @type {?} */
    TitleWindow.prototype.result;
    /** @type {?} */
    TitleWindow.prototype.url;
    /** @type {?} */
    TitleWindow.prototype.isModal;
    /** @type {?} */
    TitleWindow.prototype.contentHolder;
    /** @type {?} */
    TitleWindow.prototype.mloaderOutlet;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.windowStatte;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.content;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.windowService;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.childcomponent;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.enforceUserPosition;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.parent;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.subscriptions;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.element;
    /**
     * @type {?}
     * @private
     */
    TitleWindow.prototype.commonServ;
}
//# sourceMappingURL=data:application/json;base64,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