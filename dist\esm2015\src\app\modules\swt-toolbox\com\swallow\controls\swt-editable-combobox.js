/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ViewChild, ElementRef } from '@angular/core';
import { Container } from '../containers/swt-container.component';
import { SwtAlert } from '../utils/swt-alert.service';
import { CommonService } from '../utils/common.service';
export class SwtEditableComboBox extends Container {
    /**
     * @param {?} elem
     * @param {?} commonService
     */
    constructor(elem, commonService) {
        super(elem, commonService);
        this.elem = elem;
        this.commonService = commonService;
        this._selectedIndex = -1;
        this._selectedLabel = '';
        // initialize setter.
        this.swtalert = new SwtAlert(commonService);
    }
    /**
     * @return {?}
     */
    get selectedLabel() {
        /** @type {?} */
        let comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        return jQuery(comboComponent).val();
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedLabel(value) {
        /** @type {?} */
        let comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        this._selectedLabel = value;
        jQuery(comboComponent).val(value);
        if (this.dataProvider) {
            this._selectedIndex = this.dataProvider.indexOf(this.selectedLabel);
        }
        else {
            this._selectedIndex = -1;
        }
    }
    /**
     * @return {?}
     */
    get selectedIndex() {
        return this._selectedIndex;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set selectedIndex(value) {
        if (this.dataProvider && this.dataProvider.length >= (value + 1)) {
            this.selectedLabel = this.dataProvider[value];
        }
        else {
            this.selectedLabel = '';
        }
    }
    /**
     * @return {?}
     */
    get dataProvider() {
        return this._dataProvider;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set dataProvider(value) {
        try {
            this.selectedIndex;
            /** @type {?} */
            let comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
            if (value && Array.isArray(value)) {
                value.sort();
                this._dataProvider = value;
                jQuery(comboComponent).editableSelect('clear');
                for (let index = 0; index < value.length; index++) {
                    /** @type {?} */
                    const element = value[index];
                    jQuery(comboComponent).editableSelect('add', element);
                }
            }
        }
        catch (error) {
        }
    }
    /**
     * @param {?} element
     * @return {?}
     */
    addItem(element) {
        /** @type {?} */
        let comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        for (var i = 0; i < this.dataProvider.length && this.dataProvider[i].localeCompare(element) < 0; i++) { }
        this.dataProvider.splice(i, 0, element);
        jQuery(comboComponent).editableSelect('add', element, i);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    removeItem(value) {
        /** @type {?} */
        let comboComponent = jQuery(this.swtComboBoxcontainer.nativeElement).find('.editableCombo')[0];
        /** @type {?} */
        const index = this.dataProvider.indexOf(value);
        jQuery(comboComponent).editableSelect('remove', index);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        jQuery(this.editableSelect.nativeElement).editableSelect().on('select.editable-select', (/**
         * @param {?} e
         * @param {?} li
         * @return {?}
         */
        (e, li) => {
            this.selectedLabel = li.text();
            this._selectedIndex = this.dataProvider.indexOf(this.selectedLabel);
        }));
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
        // this.dataProvider = ['dorsaf','atef','ekram','rihab']
        // setTimeout(() => {
        //     this.addItem('bora')
        // }, 2000);
        // setTimeout(() => {
        //     this.removeItem('ekram')
        // }, 4000);
        // setTimeout(() => {
        //     // this.selectedLabel = 'atef';
        //     this.selectedIndex = 3;
        // }, 4000);
    }
}
SwtEditableComboBox.decorators = [
    { type: Component, args: [{
                selector: 'SwtEditableComboBox',
                template: `
       <div #SwtComboBoxcontainer class="SwtComboBox-container">
       <select id='editableSelect' class='editableCombo' #editableSelect>
        </select>
        </div>
  `,
                styles: [`
  `]
            }] }
];
/** @nocollapse */
SwtEditableComboBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtEditableComboBox.propDecorators = {
    editableSelect: [{ type: ViewChild, args: ['editableSelect',] }],
    swtComboBoxcontainer: [{ type: ViewChild, args: ['SwtComboBoxcontainer',] }]
};
if (false) {
    /** @type {?} */
    SwtEditableComboBox.prototype.editableSelect;
    /** @type {?} */
    SwtEditableComboBox.prototype.swtComboBoxcontainer;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.swtalert;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype._selectedLabel;
    /** @type {?} */
    SwtEditableComboBox.prototype.originalValue;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtEditableComboBox.prototype.commonService;
}
//# sourceMappingURL=data:application/json;base64,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