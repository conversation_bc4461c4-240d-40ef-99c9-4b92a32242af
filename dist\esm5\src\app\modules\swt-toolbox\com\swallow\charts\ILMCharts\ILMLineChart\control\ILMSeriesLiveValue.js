/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ViewChild, ElementRef, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Container } from '../../../../containers/swt-container.component';
import { CommonService } from '../../../../utils/common.service';
import { StringUtils } from '../../../../utils/string-utils.service';
var ILMSeriesLiveValue = /** @class */ (function (_super) {
    tslib_1.__extends(ILMSeriesLiveValue, _super);
    function ILMSeriesLiveValue(elem, commonService, cd) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this.cd = cd;
        _this.styleClassMap = {
            "CONT_SEGMENT_BLACK": 'bg-CONT_SEGMENT_BLACK',
            "CONT_SEGMENT_BLUE": 'bg-CONT_SEGMENT_BLUE',
            "CONT_SEGMENT_BOLD_RED": 'bg-CONT_SEGMENT_BOLD_RED',
            "CONT_SEGMENT_GREEN": 'bg-CONT_SEGMENT_GREEN',
            "CONT_SEGMENT_MAGENTA": 'bg-CONT_SEGMENT_MAGENTA',
            "CONT_SEGMENT_ORANGE": 'bg-CONT_SEGMENT_ORANGE',
            "CONT_SEGMENT_PURPLE": 'bg-CONT_SEGMENT_PURPLE',
            "CONT_SEGMENT_RED": 'bg-CONT_SEGMENT_RED',
            "CONT_SEGMENT_YELLOW": 'bg-CONT_SEGMENT_YELLOW',
            "DASHED_SEGMENT_BLACK": 'bg-DASHED_SEGMENT_BLACK',
            "DASHED_SEGMENT_BLUE": 'bg-DASHED_SEGMENT_BLUE',
            "DASHED_SEGMENT_GREEN": 'bg-DASHED_SEGMENT_GREEN',
            "DASHED_SEGMENT_MAGENTA": 'bg-DASHED_SEGMENT_MAGENTA',
            "DASHED_SEGMENT_ORANGE": 'bg-DASHED_SEGMENT_ORANGE',
            "DASHED_SEGMENT_PURPLE": 'bg-DASHED_SEGMENT_PURPLE',
            "DASHED_SEGMENT_RED": 'bg-DASHED_SEGMENT_RED',
            "DASHED_SEGMENT_YELLOW": 'bg-DASHED_SEGMENT_YELLOW',
            "DOTTED_SEGMENT_BLACK": 'bg-DOTTED_SEGMENT_BLACK',
            "DOTTED_SEGMENT_BLUE": 'bg-DOTTED_SEGMENT_BLUE',
            "DOTTED_SEGMENT_GREEN": 'bg-DOTTED_SEGMENT_GREEN',
            "DOTTED_SEGMENT_MAGENTA": 'bg-DOTTED_SEGMENT_MAGENTA',
            "DOTTED_SEGMENT_ORANGE": 'bg-DOTTED_SEGMENT_ORANGE',
            "DOTTED_SEGMENT_PURPLE": 'bg-DOTTED_SEGMENT_PURPLE',
            "DOTTED_SEGMENT_RED": 'bg-DOTTED_SEGMENT_RED',
            "DOTTED_SEGMENT_YELLOW": 'bg-DOTTED_SEGMENT_YELLOW',
        };
        _this._seriesValue = '';
        _this._isTimeLiveItem = false;
        return _this;
    }
    Object.defineProperty(ILMSeriesLiveValue.prototype, "seriesStyle", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesStyle;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var newStyle = this.styleClassMap[value];
            if (newStyle) {
                if (this._seriesStyle) {
                    /** @type {?} */
                    var prevStyle = this.styleClassMap[this._seriesStyle];
                    this.circle.nativeElement.classList.remove(prevStyle);
                }
                this.circle.nativeElement.classList.add(newStyle);
                this._seriesStyle = value;
            }
            if (!((/** @type {?} */ (this.cd))).destroyed) {
                this.cd.markForCheck();
            }
            // styleClassMap
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMSeriesLiveValue.prototype, "seriesId", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesId;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesId = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(ILMSeriesLiveValue.prototype, "seriesValue", {
        get: /**
         * @return {?}
         */
        function () {
            return this._seriesValue;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._seriesValue = value;
            if (!((/** @type {?} */ (this.cd))).destroyed) {
                this.cd.markForCheck();
            }
        },
        enumerable: true,
        configurable: true
    });
    // <img  id="circle" #circle src="assets/images/Rolling.gif">
    // <img  id="circle" #circle src="assets/images/Rolling.gif">
    /**
     * @return {?}
     */
    ILMSeriesLiveValue.prototype.ngOnInit = 
    // <img  id="circle" #circle src="assets/images/Rolling.gif">
    /**
     * @return {?}
     */
    function () {
        if (this._isTimeLiveItem) {
            // this.circle.nativeElement.classList.add("timeValue");
            this.circle.nativeElement.remove();
            this.seriesValue = '';
            this.labelValue.nativeElement.classList.add("timeValue");
        }
        else {
            // this.seriesValue = '-32.798.284.008';
            // this.circle.nativeElement.classList.add("bg-DOTTED_SEGMENT_RED");
        }
    };
    Object.defineProperty(ILMSeriesLiveValue.prototype, "isTimeLiveItem", {
        get: /**
         * @return {?}
         */
        function () {
            return this._isTimeLiveItem;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._isTimeLiveItem = StringUtils.isTrue(value);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    ILMSeriesLiveValue.prototype.removeLiveValue = /**
     * @return {?}
     */
    function () {
        this.elem.nativeElement.remove();
    };
    ILMSeriesLiveValue.decorators = [
        { type: Component, args: [{
                    selector: 'ILMSeriesLiveValue',
                    template: "\n         <HBox #hboxContainer  width=\"100%\"> \n            <div    #circle class='circle' ></div>\n            <div class='labelValue' #labelValue >{{seriesValue}}</div>\n        </HBox>\n        ",
                    changeDetection: ChangeDetectionStrategy.OnPush,
                    styles: ["\n\n        .labelValue{\n            position:relative;\n            font-size:11px;\n            top:-2px;\n        }\n        .timeValue{\n            padding-right :10px !important;\n        }\n        .circle{\n            margin-right:2px !important;\n        }\n        \n        .bg-CONT_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_BOLD_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -10px;\n        }\n        \n        .bg-CONT_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -42px;\n        }\n        \n        .bg-CONT_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -74px;\n        }\n        \n        .bg-CONT_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -74px;\n        }\n        \n        .bg-CONT_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -74px;\n        }\n        \n        .bg-DASHED_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -10px;\n        }\n        \n        .bg-DASHED_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -42px;\n        }\n        \n        .bg-DASHED_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -74px;\n        }\n        \n        .bg-DASHED_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -106px;\n        }\n        \n        .bg-DASHED_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -10px;\n        }\n        \n        .bg-DOTTED_SEGMENT_BLACK {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -42px;\n        }\n        \n        .bg-DOTTED_SEGMENT_BLUE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -74px;\n        }\n        \n        .bg-DOTTED_SEGMENT_GREEN {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -106px;\n        }\n        \n        .bg-DOTTED_SEGMENT_MAGENTA {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -10px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_ORANGE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -42px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_PURPLE {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -74px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_RED {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -106px -138px;\n        }\n        \n        .bg-DOTTED_SEGMENT_YELLOW {\n            width: 12px; height: 12px;\n            background: url('assets/ILM/sprites/liveValues.png') -138px -138px;\n        }\n        "]
                }] }
    ];
    /** @nocollapse */
    ILMSeriesLiveValue.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: ChangeDetectorRef }
    ]; };
    ILMSeriesLiveValue.propDecorators = {
        hboxContainer: [{ type: ViewChild, args: ['hboxContainer',] }],
        circle: [{ type: ViewChild, args: ['circle',] }],
        labelValue: [{ type: ViewChild, args: ['labelValue',] }],
        seriesStyle: [{ type: Input, args: ['seriesStyle',] }],
        seriesValue: [{ type: Input, args: ['seriesValue',] }],
        isTimeLiveItem: [{ type: Input }]
    };
    return ILMSeriesLiveValue;
}(Container));
export { ILMSeriesLiveValue };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.hboxContainer;
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.circle;
    /**
     * @type {?}
     * @protected
     */
    ILMSeriesLiveValue.prototype.labelValue;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.styleClassMap;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesValue;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesId;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._seriesStyle;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype._isTimeLiveItem;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    ILMSeriesLiveValue.prototype.cd;
}
//# sourceMappingURL=data:application/json;base64,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