{"__symbolic": "module", "version": 4, "metadata": {"MockAnimationDriver": {"__symbolic": "class", "members": {"validateStyleProperty": [{"__symbolic": "method"}], "matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"log": []}}, "MockAnimationPlayer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations", "name": "NoopAnimationPlayer", "line": 46, "character": 41}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 45, "module": "./src/mock_animation_driver"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "onInit": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "triggerMicrotask": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}]}}}, "origins": {"MockAnimationDriver": "./src/mock_animation_driver", "MockAnimationPlayer": "./src/mock_animation_driver"}, "importAs": "@angular/animations/browser/testing"}