/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { HTTPComms } from '../communication/httpcomms.service';
import { ExternalInterface } from './external-interface.service';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
//@dynamic
export class SwtUtil {
    /**
     * @param {?} common
     */
    constructor(common) {
        this.common = common;
        // httpcomms variable to send requests
        this.httpcomms = new HTTPComms(this.common);
    }
    /**
     * obtainURL
     * @return {?} String - base url
     * This function is used to return the base url of the application.
     */
    static obtainURL() {
        return window.location;
    }
    /**
     * @return {?}
     */
    static getBaseURL() {
        /** @type {?} */
        var baseUrl = "";
        try {
            baseUrl = ExternalInterface.call('getUrl');
        }
        catch (error) {
        }
        if (!baseUrl) {
            baseUrl = "";
            /** @type {?} */
            var origin = window.location.origin;
            /** @type {?} */
            var pathname = window.location.pathname;
            /** @type {?} */
            var deployName = pathname.split("\/");
            baseUrl = origin + "/" + deployName[1] + "/";
            return "http://localhost:8080/swallowtech/";
        }
        else {
            baseUrl = baseUrl + "/";
        }
        return baseUrl;
    }
    /**
     * @param {?} e
     * @return {?}
     */
    static isVisible(e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
    }
    /**
     * @param {?} value
     * @return {?}
     */
    static isEmpty(value) {
        return value === undefined || value === null || value === "";
    }
    /**
     * getSystemMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the systemMessages resource Bundle.
     */
    static getSystemMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.SYSTEM_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * @param {?} object
     * @return {?}
     */
    static convertObjectToArray(object) {
        if (object) {
            if (object.length) {
                return object;
            }
            else {
                return [object];
            }
        }
        else {
            return [];
        }
    }
    /**
     * getCommonMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the commonMessages resource Bundle.
     */
    static getCommonMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.COMMON_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getLoginMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the LoginMessages resource Bundle.
     */
    static getLoginMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.LOGIN_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getAMLMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    static getAMLMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.AML_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getDUPMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the DUPMessages resource Bundle.
     */
    static getDUPMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.DUP_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getARCMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the ARCMessages resource Bundle.
     */
    static getARCMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.ARC_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getInputMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    static getInputMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.INPUT_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getCashMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the InputMessages resource Bundle.
     */
    static getCashMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.CASH_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getFatcaMessages
     *
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the FatcaMessages resource Bundle.
     */
    static getFatcaMessages(key, object) {
        if (SwtUtil.translate) {
            return SwtUtil.translate.instant(SwtUtil.FATCA_MODULE_ID + "." + key, object);
        }
        else {
            return key;
        }
    }
    /**
     * getPredictMessage
     *
     * @param {?} key
     * @param {?=} lang
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the PredictMessage resource Bundle.
     */
    static getPredictMessage(key, lang) {
        /** @type {?} */
        let message;
        try {
            message = ExternalInterface.call('getMessage', key, lang);
        }
        catch (error) {
        }
        if (!message)
            return key;
        return message;
    }
    /**
     * This method converts the given array to string separated by given
     * separator.
     *
     * @param {?} arrValue
     * @param {?} separator
     * @return {?} String
     */
    static arrToStr(arrValue, separator) {
        //           //String - result value
        //           var value: String = null;
        //           try {
        //               // Set default value
        //               value = "";
        //               if (arrValue && arrValue.length > 0) {
        //                   //Append the string
        //                   for (var i: int = 0; i < arrValue.length; i++) {
        //                       if (i != 0) {
        //                           value += separator;
        //                       }
        //                       value += arrValue[i];
        //                   }
        //               }
        //           } catch (error: Error) {
        //               SwtAlert.error(error.message);
        //           }
        //           return value;
        /* return will be removed when edit this method */
        return '';
    }
    /**
     * logError
     *
     * @param {?} error
     * @param {?} moduleId
     * @param {?} className
     * @param {?} methodName
     * @param {?} errorLocation
     * @return {?} String - value
     *
     * This function is used to log the flex error in ERROR LOG.
     *
     * when  running the application in debugger version of flash player, this function will log the
     * error trace(location of error in src file) and when using non debugger version of flash player
     * this function will log the source file name and method name as the error Tace will be null.
     *
     */
    static logError(error, moduleId, className, methodName, errorLocation) {
        //         //To Get the errorTracesFirst Part
        //         var errorTracesBegin:String = null;
        //         //To Get the errorTracesEnd Part
        //         var errorTracesEnd:String = null;
        //         //Error Trace
        //         var errorTrace:String = null;
        //         //Error Description
        //         var errorDescription:String = null;
        //         // Error Id
        //         var errorId:String = null; 
        //         
        //         // get the error Id
        //         errorId = error.errorID.toString();
        //         //Assign error Id to request object
        //         requestParams["errorId"] = errorId;
        //         // If error message is not null then
        //          if(error.message != null) {
        //           // Get the error description
        //           errorDescription = error.message;
        //            //Assign error Description  to request object
        //           requestParams["errorDescription"] = errorDescription;
        //         }
        //         // If error trace  is not null then
        //         if(error.getStackTrace() != null) {
        //           // Get the error Trace
        //           errorTrace = error.getStackTrace().toString();
        //           // Get the Error Traces first part
        //           errorTracesBegin = errorTrace.substr(errorTrace.indexOf("[")+1,errorTrace.length-1);
        //           // Get the error location of source file
        //           errorTracesEnd = errorTracesBegin.substr(0,errorTracesBegin.indexOf("]")-1);
        //           //Get the src file name from the error Trace
        //           errorTrace = errorTracesEnd.substr(errorTracesEnd.indexOf("com"),errorTracesEnd.length-1);
        //           // Get the class and method name of source file
        //           errorTrace = "ClassName:"+className+":MethodName:"+methodName+":ErrorLocation:"+errorLocation + "\n" + errorTrace; 
        //           //Assign error Trace  to request object
        //           requestParams["errorTrace"] = errorTrace;
        //         } else {
        //           // Get the class and method name of source file
        //           errorTrace = "ClassName:"+className+":MethodName:"+methodName+":ErrorLocation:"+errorLocation; 
        //            //Assign errorLocation  to request object
        //           requestParams["errorTrace"] = errorTrace;
        //         }
        //          //Assign moduleId  to request object
        //         requestParams["moduleId"] = moduleId;
        //         // Prepare the URL of the request
        //         inputData.url = obtainURL() + actionPath + actionMethod;
        //         inputData.cbResult = inputResult;
        //         //Send the request to the server  
        //         inputData.send(requestParams);
        //         
        //         // Log error locally
        //         log.error("Error #"+requestParams["errorId"]+":"+requestParams["errorDescription"]
        //         +", ErrorLocation:"+errorLocation+"\n"+requestParams["errorTrace"]);
    }
    /**
     * this function is used to get the language of the user from jsp side
     *
     * @return {?}
     */
    static getUserLanguage() {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'userLanguage');
        /* return will be removed when edit this method */
        return '';
    }
    /**
     * this function is used to get the current entityId
     *
     * @return {?}
     */
    static getCurrEntityId() {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'currEntityId');
        /* return will be removed when edit this method */
        return '';
    }
    /**
     * this function is used to get the default entityId
     *
     * @return {?}
     */
    static getDefaultEntityId() {
        //           // get the current language from JSP
        //           return ExternalInterface.call('eval', 'defaultEntityId');
        /* return will be removed when edit this method */
        return '';
    }
    /**
     * getAMLMessages
     *
     * @param {?} moduleId
     * @param {?} key
     * @param {?=} object
     * @return {?} String - value
     *
     * This function is used to return the value for the key passed
     * to it from the AMLMessages resource Bundle.
     */
    static getMessages(moduleId, key, object) {
        if (SwtUtil.translate) {
            /** @type {?} */
            var trans = SwtUtil.translate.instant(moduleId + "." + key, object);
            // test if  the translated string really exists
            if (trans.indexOf(moduleId + ".") == -1) {
                //success of translation
                return trans;
            }
            else {
                return key;
            }
        }
        else {
            return key;
        }
    }
    /**
     * result
     *
     * @private
     * @param {?} event
     * @return {?}
     */
    static inputResult(event) {
        //           //To parse the response (result) xml
        //            var xmlReader:XMLReader = new XMLReader();
        //           //If the service is busy then remove the busy cursor
        //               if (inputData.isBusy()){
        //                   inputData.cbStop();
        //               }
        //             //Parse result xml
        //             xmlReader.setInputXML(event.result as XML);
        //             //If the result status is true, then load the grid
        //              if (!xmlReader.getRequestReplyStatus()){
        //                   //Error occurs, display the error message
        //                   SwtAlert.error(getCommonMessages('alert.generic_exception'));
        //               }
        //               xmlReader = null;
    }
    /**
     * Default constructor
     *
     * @return {?}
     */
    SwtUtil() {
        //          SwtUtil.inputData  = new HTTPComms()
    }
}
SwtUtil.CommonServiceInstance = null; // added by Rihab JABALLAH on 17/10/2018
// added by Rihab JABALLAH on 17/10/2018
// Common Module Id
SwtUtil.COMMON_MODULE_ID = 'COMMON';
// System Module Id
SwtUtil.SYSTEM_MODULE_ID = 'SYSTEM';
// AML module Id
SwtUtil.AML_MODULE_ID = 'AML';
// DUP module Id
SwtUtil.DUP_MODULE_ID = 'DUP';
// ARC module Id
SwtUtil.ARC_MODULE_ID = 'ARC';
// INPUT module Id
SwtUtil.INPUT_MODULE_ID = 'INPUT';
// GENREC module Id
SwtUtil.CASH_MODULE_ID = 'CASH';
// FATCA module Id
SwtUtil.FATCA_MODULE_ID = 'FATCA';
// Predict module Id
SwtUtil.PREDICT_MODULE_ID = 'PREDICT';
// PCM module Id
SwtUtil.PCM_MODULE_ID = 'PCM';
// String login
SwtUtil.LOGIN_ID = 'LOGIN';
// set the language
SwtUtil.lang = 'es';
SwtUtil.translate = null;
/* String variable to hold action method */
SwtUtil.actionMethod = 'logError.do';
/* String variable to hold action path */
SwtUtil.actionPath = 'system/errorlog!';
if (false) {
    /** @type {?} */
    SwtUtil.CommonServiceInstance;
    /** @type {?} */
    SwtUtil.COMMON_MODULE_ID;
    /** @type {?} */
    SwtUtil.SYSTEM_MODULE_ID;
    /** @type {?} */
    SwtUtil.AML_MODULE_ID;
    /** @type {?} */
    SwtUtil.DUP_MODULE_ID;
    /** @type {?} */
    SwtUtil.ARC_MODULE_ID;
    /** @type {?} */
    SwtUtil.INPUT_MODULE_ID;
    /** @type {?} */
    SwtUtil.CASH_MODULE_ID;
    /** @type {?} */
    SwtUtil.FATCA_MODULE_ID;
    /** @type {?} */
    SwtUtil.PREDICT_MODULE_ID;
    /** @type {?} */
    SwtUtil.PCM_MODULE_ID;
    /** @type {?} */
    SwtUtil.LOGIN_ID;
    /** @type {?} */
    SwtUtil.lang;
    /** @type {?} */
    SwtUtil.translate;
    /** @type {?} */
    SwtUtil.screenWidth;
    /** @type {?} */
    SwtUtil.screenHeight;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.actionMethod;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.actionPath;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.inputData;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.requestParams;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.prototype.httpcomms;
    /**
     * @type {?}
     * @private
     */
    SwtUtil.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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