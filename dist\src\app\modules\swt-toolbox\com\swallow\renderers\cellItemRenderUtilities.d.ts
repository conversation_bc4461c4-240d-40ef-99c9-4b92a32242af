/**
  * Return the formatter based on 'negative' value .
  */
export declare function isNegative(row: any, field: any): boolean;
/**
 * Return the formatter based on 'negative' value .
 */
export declare function CellBackgroundColor(row: any, field: any): boolean;
/**
  * Return the formatter as a link .
  */
export declare function isClickable(row: any, field: any): boolean;
/**
 * Return the formatter based on 'bold' value .
 */
export declare function isBold(row: any, field: any): boolean;
/**
  * Return the formatter based on 'negative' value .
  */
export declare function isBlink(value: any): boolean;
/**
  * Added for set a new design for the custom column
  */
export declare function CustomCell(row: any, field: any): string;
/**
 * Encodes special html characters
 * param string
 * return {*}
 */
export declare function html_encode(string: any): string;
