/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { ApplicationRef, ComponentFactoryResolver, Injectable, Injector } from '@angular/core';
import { Subject } from 'rxjs';
import { TitleWindow } from '../controls/title-window.component';
import { WindowUtils } from '../utils/window-utils';
import { Alert, parentApplication } from "../../..";
import * as i0 from "@angular/core";
/**
 * @record
 */
export function IWindow() { }
if (false) {
    /** @type {?} */
    IWindow.prototype.component;
    /** @type {?} */
    IWindow.prototype.selector;
    /** @type {?} */
    IWindow.prototype.layoutOrder;
    /** @type {?} */
    IWindow.prototype.winid;
}
//@dynamic
export class WindowManager {
    // TODO surround each method with try catch block and replace console with logger.
    /**
     * @param {?} factory
     * @param {?} injector
     * @param {?} applicationRef
     */
    constructor(factory, injector, applicationRef) {
        this.factory = factory;
        this.injector = injector;
        this.applicationRef = applicationRef;
        // counter to indicate window index
        this.counter = 0;
        // private variable to hold opened windows.
        this.windows = {};
        // create subject
        this.windowSubject = new Subject();
        this.exists = "";
        this.windowObservable = this.windowSubject.asObservable();
    }
    /**
     * @param {?} parent
     * @param {?} component
     * @param {?} data
     * @param {?=} modal
     * @return {?}
     */
    createWindow(parent, component, data, modal = false) {
        this.compRef = this.factory.resolveComponentFactory(TitleWindow).create(this.injector);
        // include content in title window.
        this.compRef.instance.includeContent(parent, this, "", component, data);
        return this.compRef.instance;
    }
    /**
     * +
     * This method is used to load component from its lazy path.
     * @param {?} url
     * @return {?}
     */
    load(url) {
        this.compRef = this.factory.resolveComponentFactory(TitleWindow).create(this.injector);
        // include content in title window.
        this.compRef.instance.url = url;
        parentApplication.loaderInfo.url = url;
        parentApplication.setParams(url);
        this.compRef.instance.includeContent(parent, this, url);
        return this.compRef.instance;
    }
    // save opened window instance in HashMap
    /**
     * @return {?}
     */
    show() {
        /** @type {?} */
        const id = 'win_' + this.counter++;
        this.compRef.instance.layoutOrder = this.counter;
        this.compRef.instance.tabid = parentApplication.selectedTab;
        if (this.windows[this.compRef.instance.id]) {
            console.error("WindowManager - [ show ] - Cannot instantiate two window with same id! window with the id ", this.compRef.instance.id, " exists.");
            return;
        }
        if (this.compRef.instance.id) {
            this.windows[this.compRef.instance.id] = {
                component: this.compRef,
                selector: this.compRef.hostView,
                id: id,
                layoutOrder: this.counter
            };
            this.exists = this.compRef.instance.id;
        }
        else {
            this.compRef.instance.id = id;
            // save window reference in hashMap.
            this.windows[id] = {
                component: this.compRef,
                selector: this.compRef.hostView,
                id: id,
                layoutOrder: this.counter
            };
            this.exists = id;
        }
        // this line is used to set id of alert component.
        if (!this.compRef.instance.alertId) {
            this.compRef.instance.alertId = id;
        }
        // attach component to current application.
        this.applicationRef.attachView(this.compRef.hostView);
        // get component root node (component selector).
        /** @type {?} */
        const selector = (/** @type {?} */ (((/** @type {?} */ (this.compRef.hostView))).rootNodes[0]));
        // attach component root node to DOM.
        document.body.appendChild(selector);
    }
    /**
     * This function is used to crate alert.
     * @param {?=} text
     * @param {?=} title
     * @param {?=} flags
     * @param {?=} parent
     * @param {?=} closeHandler
     * @param {?=} iconClass
     * @param {?=} defaultButtonFlag
     * @return {?}
     */
    createAlert(text = "", title = "", flags /* Alert.OK */, parent, closeHandler, iconClass, defaultButtonFlag = 4 /* Alert.OK */) {
        this.compRef = this.factory.resolveComponentFactory(Alert).create(this.injector);
        this.compRef.instance.windowManager = this;
        this.compRef.instance.title = title;
        this.show();
        return this.compRef.instance;
    }
    /**
     * @return {?}
     */
    removeAll() {
        for (const window in this.windows) {
            if (this.windows.hasOwnProperty(window)) {
                this.close(window);
                WindowUtils.winOrder = 100;
            }
        }
    }
    /**
     * @param {?} id
     * @return {?}
     */
    close(id) {
        if (this.windows[id]) {
            this.windows[id].component.destroy();
            this.applicationRef.detachView(this.windows[id].selector);
            WindowUtils.winid = 'win_0';
            delete this.windows[id];
        }
    }
    /**
     * @param {?} id
     * @return {?}
     */
    getWindow(id) {
        return this.windows[id].component.instance;
    }
    /**
     * @return {?}
     */
    getFirstWindowInstance() {
        return this.windows['win_0'];
    }
    /**
     * @return {?}
     */
    getLastWindowInsatance() {
        /** @type {?} */
        const key = 'win_' + (this.counter - 1);
        return this.windows[key];
    }
    /**
     * @param {?} winid
     * @return {?}
     */
    getWindowInstance(winid) {
        return this.windows[winid];
    }
    /**
     * @return {?}
     */
    getFirstLayoutOrder() {
        return this.getFirstWindowInstance().layoutOrder;
    }
    /**
     * @return {?}
     */
    getLastLayoutOrder() {
        return this.getLastWindowInsatance().layoutOrder;
    }
    /**
     * @param {?} winid
     * @return {?}
     */
    getLayoutOrder(winid) {
        return this.getWindowInstance(winid).layoutOrder;
    }
}
WindowManager.decorators = [
    { type: Injectable, args: [{
                providedIn: 'root'
            },] }
];
/** @nocollapse */
WindowManager.ctorParameters = () => [
    { type: ComponentFactoryResolver },
    { type: Injector },
    { type: ApplicationRef }
];
/** @nocollapse */ WindowManager.ngInjectableDef = i0.defineInjectable({ factory: function WindowManager_Factory() { return new WindowManager(i0.inject(i0.ComponentFactoryResolver), i0.inject(i0.INJECTOR), i0.inject(i0.ApplicationRef)); }, token: WindowManager, providedIn: "root" });
if (false) {
    /** @type {?} */
    WindowManager.prototype.counter;
    /** @type {?} */
    WindowManager.prototype.windows;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.windowSubject;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.windowObservable;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.compRef;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.exists;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.factory;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.injector;
    /**
     * @type {?}
     * @private
     */
    WindowManager.prototype.applicationRef;
}
//# sourceMappingURL=data:application/json;base64,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