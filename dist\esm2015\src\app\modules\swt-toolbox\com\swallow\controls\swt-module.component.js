/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Component, ElementRef, EventEmitter, Input, Output, } from '@angular/core';
import { SwtUtil } from "../utils/swt-util.service";
import { HashMap } from "../utils/HashMap.service";
import { ContextMenu } from "../controls/context-menu.component";
import { CommonService } from "../utils/common.service";
import { Container } from '../containers/swt-container.component';
//import $ from 'jquery';
/** @type {?} */
const $ = require('jquery');
/** @type {?} */
const _ = parent;
export class SwtModule extends Container {
    /**
     * @param {?} elementRef
     * @param {?} comService
     */
    constructor(elementRef, comService) {
        super(elementRef, comService);
        this.elementRef = elementRef;
        this.comService = comService;
        this.changes = new HashMap();
        // set creation Complete event
        this.creationComplete = new EventEmitter();
        // set preinitialize event
        this.preinitialize = new EventEmitter();
        this.title = "";
        this.contextmenuItems = [];
        $($(this.elementRef.nativeElement)[0]).attr('selector', 'SwtModule');
    }
    /**
     * @return {?}
     */
    get contextMenu() {
        return this._contextMenu;
    }
    /*- Parameters to handle context menu of screenVersion screen -START- Added by Rihab JABALLAH on 17/10/2018  */
    /**
     * @param {?} value
     * @return {?}
     */
    set contextMenu(value) {
        $('.screenVerion').remove();
        this._contextMenu = value;
        this.contextmenuItems = this._contextMenu.customItems;
        /* creating list of ContextMenuItem dynamically - [START] */
        $(this.elementRef.nativeElement).append("<ul class='screenVerion' ></ul>");
        /** @type {?} */
        const list = $(".screenVerion");
        for (let index = 0; index < this.contextmenuItems.length; index++) {
            list.append('<li data=\'' + this.contextmenuItems[index].label + '\'>' + this.contextmenuItems[index].label + '</li>');
        }
        /** @type {?} */
        const __this = this;
        $(".screenVerion li").click((/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            /** @type {?} */
            const contextMenuItemData = event.currentTarget.attributes[0].nodeValue;
            /** @type {?} */
            const item = __this.contextmenuItems.find((/**
             * @param {?} x
             * @return {?}
             */
            (x) => x.label === contextMenuItemData));
            if (item) {
                item.MenuItemSelect();
            }
            $(".screenVerion").hide(100);
        }));
        $(document).on('click', (/**
         * @param {?} event
         * @return {?}
         */
        (event) => {
            $(".screenVerion").hide(100);
        }));
        /* creating list of ContextMenuItem dynamically - [END] */
    }
    // get title() {
    //    // return this.titleWindow.title;
    // }
    //
    // set title(value: string) {
    //    // this.titleWindow.title = value;
    // }
    /**
     * @return {?}
     */
    get result() {
        return this.titleWindow.result;
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set result(value) {
        this.titleWindow.result = value;
    }
    /**
     * @return {?}
     */
    ngAfterViewInit() {
    }
    /**
     * @return {?}
     */
    ngOnDestroy() {
        $("body").off("mousemove.module" + this.id);
        $("body").off("mousedown.module" + this.id);
        $("body").off("mouseup.module" + this.id);
    }
    /**
     * @return {?}
     */
    ngOnInit() {
        try {
            this.preinitialize.emit(this);
            setTimeout((/**
             * @return {?}
             */
            () => {
                this.creationComplete.emit(this);
            }), 0);
            $(this.elementRef.nativeElement).attr('selector', 'SwtModule');
            /** @type {?} */
            let dragging = false;
            $("body").on("mousedown.module" + this.id, (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                /** @type {?} */
                var x = e.screenX;
                /** @type {?} */
                var y = e.screenY;
                dragging = false;
                /** @type {?} */
                let parentDisable = $(e.target).parent().hasClass('notDragable');
                $("body").on("mousemove.module" + this.id, (/**
                 * @param {?} e
                 * @return {?}
                 */
                (e) => {
                    if (Math.abs(x - e.screenX) > 5 || Math.abs(y - e.screenY) > 5) {
                        dragging = true;
                        if (parentDisable) {
                            $(document).trigger("mouseup");
                            event.preventDefault ? event.preventDefault() : (event.returnValue = false);
                            return false;
                        }
                    }
                }));
            }));
            $("body").on("mouseup.module" + this.id, (/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                $("body").off("mousemove.module" + this.id);
            }));
            $.widget("ui.resizable", $.ui.resizable, {
                resizeTo: (/**
                 * @param {?} newSize
                 * @return {?}
                 */
                function (newSize) {
                    /** @type {?} */
                    var start = new $.Event("mousedown", { pageX: 0, pageY: 0 });
                    this._mouseStart(start);
                    this.axis = 'se';
                    /** @type {?} */
                    var end = new $.Event("mouseup", {
                        pageX: newSize.width - this.originalSize.width || 0,
                        pageY: newSize.height - this.originalSize.height || 0
                    });
                    this._mouseDrag(end);
                    this._mouseStop(end);
                })
            });
        }
        catch (e) {
            console.error("SwtModule [ ngOnInit ] - error :", e);
        }
    }
    /**
     * This method is used to close title window.
     * @return {?}
     */
    close() {
        this.titleWindow.close();
    }
    /**
     * This method is used to return class name
     * <AUTHOR>
     * @param {?} classObject
     * @return {?}
     */
    getQualifiedClassName(classObject) {
        return classObject.constructor.name + ".ts";
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getSystemMessages(key, object) {
        return SwtUtil.getSystemMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getCommonMessages(key, object) {
        return SwtUtil.getCommonMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getLoginMessages(key, object) {
        return SwtUtil.getLoginMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getAMLMessages(key, object) {
        return SwtUtil.getAMLMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getDUPMessages(key, object) {
        return SwtUtil.getDUPMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getARCMessages(key, object) {
        return SwtUtil.getARCMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getInputMessages(key, object) {
        return SwtUtil.getInputMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getCashMessages(key, object) {
        return SwtUtil.getCashMessages(key, object);
    }
    /**
     * @param {?} key
     * @param {?=} object
     * @return {?}
     */
    getFatcaMessages(key, object) {
        return SwtUtil.getFatcaMessages(key, object);
    }
    /**
     * @return {?}
     */
    resetSpy() {
        this._components.forEach((/**
         * @param {?} element
         * @return {?}
         */
        (element) => {
            element.resetOriginalValue();
        }));
    }
    /**
     * @param {?} components
     * @return {?}
     */
    subscribeSpy(components) {
        this._components = components;
        components.forEach((/**
         * @param {?} element
         * @return {?}
         */
        (element) => {
            element.onSpyChange.subscribe((/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                this.onComponentChanged(e);
            }));
            element.onSpyNoChange.subscribe((/**
             * @param {?} e
             * @return {?}
             */
            (e) => {
                this.onComponentNotChanged(e);
            }));
        }));
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onComponentChanged(event) {
        // Vérifier si le hashmap a été vide, puis rempli par le premier élement,
        // Si Oui, alors le SwtModule dispatche (emit) son evement "change"
        // Sinon, ne rien faire
        //if (this.changes.size() == 0){
        // this.onSpyChange.emit(this.changes);
        //}
        this.changes.put(event.target, event.value);
        this.onSpyChange.emit(this.changes);
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onComponentNotChanged(event) {
        // Vérifier si le hashmap est devenu vide
        // Si Oui, alors le SwtModule dispatche son evement "no_change"
        // Sinon, ne rien faire
        if (this.changes.containsKey(event.target)) {
            this.changes.remove(event.target);
        }
        if (this.changes.size() === 0) {
            this.onSpyNoChange.emit(this.changes);
        }
    }
    /**
     * @param {?} event
     * @return {?}
     */
    onRightClick(event) {
        event.preventDefault();
        /** @type {?} */
        const __this = this;
        if ($(".openedFontSetting").length === 0) {
            $(".screenVerion").removeClass('hidden');
        }
        /** @type {?} */
        const contextmenu = $(this.elementRef.nativeElement.parentElement).find('.screenVerion');
        contextmenu.finish().toggle(100).css('position', 'fixed').css({
            top: event.pageY + "px",
            left: event.pageX + "px"
        });
    }
}
SwtModule.decorators = [
    { type: Component, args: [{
                selector: 'SwtModule',
                template: `
        <div style="background-color: #D6E3FE;"
             (contextmenu)="onRightClick($event)">
            <ng-content></ng-content>
            <ng-container #_container></ng-container>
        </div>
    `
            }] }
];
/** @nocollapse */
SwtModule.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
SwtModule.propDecorators = {
    creationComplete: [{ type: Output, args: ['creationComplete',] }],
    preinitialize: [{ type: Output, args: ['preinitialize',] }],
    contextMenu: [{ type: Input }]
};
if (false) {
    /** @type {?} */
    SwtModule.prototype.parentDocument;
    /** @type {?} */
    SwtModule.prototype.titleWindow;
    /** @type {?} */
    SwtModule.prototype.changes;
    /** @type {?} */
    SwtModule.prototype.creationComplete;
    /** @type {?} */
    SwtModule.prototype.preinitialize;
    /** @type {?} */
    SwtModule.prototype.title;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype.contextmenuItems;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype._components;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype._contextMenu;
    /** @type {?} */
    SwtModule.prototype.elementRef;
    /**
     * @type {?}
     * @private
     */
    SwtModule.prototype.comService;
}
//# sourceMappingURL=data:application/json;base64,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