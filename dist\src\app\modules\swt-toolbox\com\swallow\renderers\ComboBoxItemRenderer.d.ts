import { Editor } from 'angular-slickgrid';
import 'jquery-ui-dist/jquery-ui';
export declare class ComboBoxItemRenderer implements Editor {
    private args;
    private logger;
    private $input;
    private defaultValue;
    private defaultContent;
    private dataSource;
    correspondantProperty: any;
    private $dropDown;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    CRUD_ORIGINAL_DATA: string;
    CRUD_CHANGES_DATA: any;
    originalDefaultValue: any;
    private commonGrid;
    private showHideCells;
    private enabledFlag;
    private columnDef;
    private fieldId;
    private cursor;
    constructor(args: any);
    init(): void;
    populateSelect(select: any, dataSource: any, addBlank: any): void;
    loadValue(item: any): void;
    serializeValue(): any;
    destroy(): void;
    focus(): void;
    applyValue(item: any, state: any): void;
    isValueChanged(): boolean;
    validate(): any;
    /**
     * This method is used to refresh
     * comboBox view.
     */
    showAlert(): void;
}
