import { EventEmitter } from '@angular/core';
export declare const TabSelectEvent: EventEmitter<any>;
export declare const DividerResizeComplete: EventEmitter<any>;
export declare const TabCloseEvent: EventEmitter<any>;
export declare const WindowDragEvent: EventEmitter<any>;
export declare const WindowDragStartEvent: EventEmitter<any>;
export declare const WindowDragEndEvent: EventEmitter<any>;
export declare const WindowCloseEvent: EventEmitter<any>;
export declare const WindowCreateEvent: EventEmitter<any>;
export declare const WindowResizeEvent: EventEmitter<any>;
export declare const WindowMinimizeEvent: EventEmitter<any>;
export declare const WindowMaximizeEvent: EventEmitter<any>;
export declare const HDividedEndResizeEvent: EventEmitter<any>;
export declare const VDividedEndResizeEvent: EventEmitter<any>;
export declare const TabChange: EventEmitter<any>;
export declare const TabClose: EventEmitter<any>;
export declare const SwtCommonGridItemRenderChanges: EventEmitter<any>;
export declare const ExportEvent: EventEmitter<any>;
export declare const AdvancedExportEvent: EventEmitter<any>;
export declare const HorizontalScrollPositionEvent: EventEmitter<any>;
export declare const VerticalScrollPositionEvent: EventEmitter<any>;
export declare const SeriesHighlightEvent: EventEmitter<any>;
export declare const LegendItemChangedEvent: EventEmitter<any>;
export declare const SwtCheckboxEvent: EventEmitter<any>;
/**
 * This class contain all module events
 */
export declare class ModuleEvent {
    static READY: string;
    static PROGRESS: string;
    static ERROR: string;
    static SETUP: string;
    static UNLOAD: string;
    static DISPOSE: string;
}
export declare class WindowEvent {
    static DRAG: string;
    static DRAGSTART: string;
    static DRAGEND: string;
    static WINDOWCLOSE: string;
    static CREATE: string;
    static RESIZE: string;
    static MINIMIZE: string;
    static MAXIMIZE: string;
}
export declare class CustomTreeEvent {
    static ITEMRENDER: string;
    static ITEMDOUBLECLICK: string;
    static ITEMCLICK: string;
    static ICONCLICK: string;
    static EXPANDERCLICK: string;
    static TITLECLICK: string;
    static ICONFOCUS: string;
    static ICONFOCUSOUT: string;
    static ICONMOUSELEAVE: string;
    static ICONMOUSEENTER: string;
}
export declare class genericEvent {
    static ACTIVATE: string;
    static MOUSE_OVER: string;
    static MOUSE_UP: string;
    static MOUSE_DOWN: string;
    static FOCUS: string;
    static FOCUSOUT: string;
    static ADDED: string;
    static ADDED_TO_STAGE: string;
    static BROWSER_ZOOM_CHANGE: string;
    static CANCEL: string;
    static CHANGE: string;
    static CHANNEL_MESSAGE: string;
    static CHANNEL_STATE: string;
    static CLEAR: string;
    static CLOSE: string;
    static CLOSING: string;
    static COMPLETE: string;
    static CONNECT: string;
    static CONTEXT3D_CREATE: string;
    static COPY: string;
    static CUT: string;
    static DEACTIVATE: string;
    static DISPLAYING: string;
    static ENTER_FRAME: string;
    static EXIT_FRAME: string;
    static EXITING: string;
    static FRAME_CONSTRUCTED: string;
    static FRAME_LABEL: string;
    static FULLSCREEN: string;
    static HTML_BOUNDS_CHANGE: string;
    static HTML_DOM_INITIALIZE: string;
    static HTML_RENDER: string;
    static ID3: string;
    static INIT: string;
    static LOCATION_CHANGE: string;
    static MOUSE_LEAVE: string;
    static NETWORK_CHANGE: string;
    static OPEN: string;
    static PASTE: string;
    static PREPARING: string;
    static REMOVED: string;
    static REMOVED_FROM_STAGE: string;
    static RENDER: string;
    static RESIZE: string;
    static SCROLL: string;
    static SELECT: string;
    static SELECT_ALL: string;
    static SOUND_COMPLETE: string;
    static STANDARD_ERROR_CLOSE: string;
    static STANDARD_INPUT_CLOSE: string;
    static STANDARD_OUTPUT_CLOSE: string;
    static SUSPEND: string;
    static TAB_CHILDREN_CHANGE: string;
    static TAB_ENABLED_CHANGE: string;
    static TAB_INDEX_CHANGE: string;
    static TEXT_INTERACTION_MODE_CHANGE: string;
    static TEXTURE_READY: string;
    static UNLOAD: string;
    static USER_IDLE: string;
    static USER_PRESENT: string;
    static VIDEO_FRAME: string;
    static WORKER_STATE: string;
    static ROW_CLICK: string;
    static CELL_CLICK: string;
    static ROW_DBCLICK: string;
    static CELL_DBCLICK: string;
    static ITEM_EXPAND: string;
    static ITEM_COLLAPSE: string;
    static CLICK: string;
}
export declare class SwtEventsModule {
}
