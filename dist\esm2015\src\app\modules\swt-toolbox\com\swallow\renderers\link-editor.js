/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/** @type {?} */
const $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Logger } from "../logging/logger.service";
/** @type {?} */
const select2 = require('select2');
//@dynamic
export class LinkEditor {
    /**
     * @param {?} args
     */
    constructor(args) {
        this.args = args;
        this.commonGrid = this.args.column.params.grid;
        this.logger = null;
        this.logger = new Logger('LinkEditor', null);
        this.init();
    }
    /**
     * @return {?}
     */
    init() {
        try {
            this.logger.debug('method [init] - START/END');
            this.loadValue(this.args.item);
            this.$input = $(`<a style="color:#03A9F4!important; text-decoration: underline; cursor: pointer;" >
                            <p style="padding: 0 5px 0 5px;"> ${this.defaultValue}</p>
                     </a> 
      `);
            this.$input.appendTo(this.args.container);
            this.commonGrid.CellLinkClick();
            // - remove Highlighting .
            /** @type {?} */
            var selectedCells = $(this.commonGrid.el.nativeElement).find('.selected');
            if (selectedCells.length > 0) {
                for (var index = 0; index < selectedCells.length; index++) {
                    /** @type {?} */
                    var item = selectedCells[index];
                    $(item).removeClass('selected');
                }
            }
            //- add Highlighting 
            /** @type {?} */
            var activeRow = $(this.commonGrid.el.nativeElement).find('.slick-row.active');
            if (activeRow && activeRow.children().length > 0) {
                for (var index = 0; index < activeRow.children().length; index++) {
                    /** @type {?} */
                    var item = activeRow.children()[index];
                    $(item).addClass('selected');
                }
            }
        }
        catch (error) {
            console.log('method [init] - Error:', error);
        }
    }
    /**
     * @return {?}
     */
    focus() {
        this.logger.debug('method [focus] - START/END');
        this.$input.focus();
    }
    /**
     * @return {?}
     */
    getValue() {
        return this.$input.val();
    }
    /**
     * @param {?} val
     * @return {?}
     */
    setValue(val) {
        this.logger.debug('method [setValue] - START/END');
        //this.$input.val(val);
    }
    /**
     * @param {?} item
     * @return {?}
     */
    loadValue(item) {
        this.logger.info('method [loadValue] - START/END', this.args.column.field);
        this.defaultValue = item[this.args.column.field] || '';
    }
    /**
     * @return {?}
     */
    save() {
        this.logger.debug('method [save] - START/END');
        this.args.commitChanges();
    }
    /**
     * @return {?}
     */
    serializeValue() {
        return this.$input.val();
    }
    /**
     * @param {?} item
     * @param {?} state
     * @return {?}
     */
    applyValue(item, state) {
        // item[this.args.column.field] = state;
    }
    /**
     * @return {?}
     */
    isValueChanged() {
        /** @type {?} */
        let isChanged = (!(this.$input.val() === '' && this.defaultValue == null)) && (this.$input.val() !== this.defaultValue);
        this.logger.debug('method [isValueChanged] , returned value: "' + isChanged + '" - START/END');
        if ((isChanged && this.args && this.args.column.params && this.args.column.params.owner)) {
            this.args.column.params.owner.spyChanges({ field: this.args.column.field });
        }
        return isChanged;
    }
    /**
     * @return {?}
     */
    validate() {
        this.logger.debug('method [validate] - START/END');
        return {
            valid: true,
            msg: null
        };
    }
    /**
     * @return {?}
     */
    destroy() {
        // - remove Highlighting .
        /** @type {?} */
        var parent = $(this.args.container.parentElement);
        /** @type {?} */
        var children = $(parent).children();
        for (var index = 0; index < children.length; index++) {
            $(children[index]).removeClass('selected');
        }
        this.$input.remove();
    }
}
if (false) {
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.commonGrid;
    /** @type {?} */
    LinkEditor.prototype.$input;
    /** @type {?} */
    LinkEditor.prototype.defaultValue;
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.logger;
    /**
     * @type {?}
     * @private
     */
    LinkEditor.prototype.args;
}
//# sourceMappingURL=data:application/json;base64,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