import 'jquery-ui-dist/jquery-ui';
import { Editor } from 'angular-slickgrid';
export declare class stepperEditor implements Editor {
    private args;
    private $input;
    private defaultValue;
    private mouseDownFlag;
    private mouseUpFlag;
    private logger;
    CRUD_OPERATION: string;
    CRUD_DATA: string;
    CRUD_ORIGINAL_DATA: string;
    CRUD_CHANGES_DATA: any;
    originalDefaultValue: any;
    private commonGrid;
    private enableFlag;
    private showHideCells;
    constructor(args: any);
    init(): void;
    destroy(): void;
    focus(): void;
    getValue(): number;
    setValue(val: string): void;
    loadValue(item: any): void;
    save(): void;
    serializeValue(): any;
    applyValue(item: any, state: any): void;
    isValueChanged(): boolean;
    validate(): {
        valid: boolean;
        msg: any;
    };
}
