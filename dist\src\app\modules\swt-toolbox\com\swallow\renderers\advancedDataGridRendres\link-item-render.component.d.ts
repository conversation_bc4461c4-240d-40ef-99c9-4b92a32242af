import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IItemRender } from "./iitem-render";
import { UIComponent } from "../../controls/UIComponent.service";
import { CommonService } from "../../utils/common.service";
export declare class LinkItemRander extends UIComponent implements OnInit, IItemRender, OnDestroy {
    private linkelement;
    private common;
    text: string;
    color: string;
    type: string;
    id: number;
    constructor(linkelement: ElementRef, common: CommonService);
    ngOnInit(): void;
    ngOnDestroy(): void;
    /**
     * This method will be fired on link item render
     * click.
     * @param event
     */
    linkItemRenderClickHandler(event: any): void;
}
