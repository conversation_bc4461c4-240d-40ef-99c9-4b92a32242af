/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, Input } from '@angular/core';
/** @type {?} */
var $ = require('jquery');
import 'jquery-ui-dist/jquery-ui';
import { Container } from '../containers/swt-container.component';
import { genericEvent } from '../events/swt-events.module';
import { CommonService } from '../utils/common.service';
var SwtImage = /** @class */ (function (_super) {
    tslib_1.__extends(SwtImage, _super);
    function SwtImage(element) {
        var _this = _super.call(this, element, CommonService.instance) || this;
        _this.element = element;
        _this.source = "";
        _this.id = "";
        _this.width = 15;
        return _this;
    }
    /**
     * @return {?}
     */
    SwtImage.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        var _this = this;
        $(this.element.nativeElement.children[0])
            .width(this.width)
            .height(this.height);
        $(this.element.nativeElement.children[0]).on("mouseover", (/**
         * @param {?} evt
         * @return {?}
         */
        function (evt) {
            if (_this.eventlist[genericEvent.MOUSE_OVER]) {
                _this.eventlist[genericEvent.MOUSE_OVER]({ event: evt, target: _this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mouseup", (/**
         * @param {?} evt
         * @return {?}
         */
        function (evt) {
            if (_this.eventlist[genericEvent.MOUSE_UP]) {
                _this.eventlist[genericEvent.MOUSE_UP]({ event: evt, target: _this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mousedown", (/**
         * @param {?} evt
         * @return {?}
         */
        function (evt) {
            if (_this.eventlist[genericEvent.MOUSE_DOWN]) {
                _this.eventlist[genericEvent.MOUSE_DOWN]({ event: evt, target: _this });
            }
        }));
        $(this.element.nativeElement.children[0]).on("mouseleave", (/**
         * @param {?} evt
         * @return {?}
         */
        function (evt) {
            if (_this.eventlist[genericEvent.MOUSE_LEAVE]) {
                _this.eventlist[genericEvent.MOUSE_LEAVE]({ event: evt, target: _this });
            }
        }));
    };
    SwtImage.decorators = [
        { type: Component, args: [{
                    selector: 'SwtImage',
                    template: "\n    <img [src]=\"source\" [id]=\"id\">\n  "
                }] }
    ];
    /** @nocollapse */
    SwtImage.ctorParameters = function () { return [
        { type: ElementRef }
    ]; };
    SwtImage.propDecorators = {
        source: [{ type: Input, args: ["source",] }],
        id: [{ type: Input, args: ["id",] }],
        width: [{ type: Input, args: ["width",] }],
        height: [{ type: Input, args: ["height",] }]
    };
    return SwtImage;
}(Container));
export { SwtImage };
if (false) {
    /** @type {?} */
    SwtImage.prototype.source;
    /** @type {?} */
    SwtImage.prototype.id;
    /** @type {?} */
    SwtImage.prototype.width;
    /** @type {?} */
    SwtImage.prototype.height;
    /**
     * @type {?}
     * @private
     */
    SwtImage.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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