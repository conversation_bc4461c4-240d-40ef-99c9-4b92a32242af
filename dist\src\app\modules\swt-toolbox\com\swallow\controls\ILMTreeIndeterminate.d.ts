import { AfterContentInit, ElementRef, OnInit } from '@angular/core';
import { CustomTree } from './swt-custom-tree.component';
import { CommonService } from '../utils/common.service';
export declare class ILMTreeIndeterminate extends CustomTree implements OnInit, AfterContentInit {
    protected elem: ElementRef;
    protected commonService: CommonService;
    constructor(elem: ElementRef, commonService: CommonService);
    /**
   * accessTreeHide
   *
   * @param xmlNode:XML
   *
   * This method is called to hide some nodes in access Tree view
   **/
    private customHideFunction;
    /**
     *  Remove all the children nodes without branch that are not included in actual datasets<br>
     *	Actuals datasets are thresholds, actual balance and actual inflow/outflow
        * @param xmlList
        *
        */
    showActualDatasetsOnly(xmlList: any, inActualData: any): void;
    showBalanceDatasetsOnly(xmlList: any, inBalanceData: any, parentNode?: any): void;
    /**
         * Shows or hides a group or a scenario based on what is checked on group/scenario bottom datagrid
         **/
    showHideGroupScenarioNodes(data: any): void;
    /**
         * get list of selected nodes
         * */
    getSelectedNodes(selectedNodes: any, listNode: any): void;
    /**
     * Based on the event data transfert object, this functions returns an Array of datasets that will be shown or hedden
     * */
    checkedGroupScenarioCharts(data: Object): any[];
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     * */
    getCheckedScenarioNodes(checkedNodes: any, listNodes: any): void;
    /**
     * Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
     * */
    getCheckedScenarioNodesTree(checkedNodes: any, listNodes: any): void;
    /**
* Returns the list ilm datasets (in a passed array as parameter) that are checked and visible when a scenario or a group is chosen
* */
    setCheckedScenarioNodesTree(checkedNodes: any, listNodes: any): void;
    /**
 * Returns the list of displayed scenarios in the tree,
 * this will be useful to update the dataprovider of the whole ILM line chart
 * */
    getDisplayedScenarios(): string;
    private findYFields;
    getVisibleYFields(): any[];
    /**
     * Returns the list of displayed thresholds by seraching in the tree structure
     * */
    getDisplayedThresolds(): any[];
    getDisplayedThresoldsArray(): any[];
    getGroupScenarioCombinations(data: any): any[];
    /**
     * Override dataProvider to save tree opened elements
     **/
    dataProvider: any;
    /**
     * This method is used to expand all tree nodes.
     * @param expandToLvl
     */
    reloadNodeById(groupId?: string): void;
}
