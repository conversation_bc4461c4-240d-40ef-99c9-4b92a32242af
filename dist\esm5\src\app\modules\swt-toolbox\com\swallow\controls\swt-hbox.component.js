/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input, ViewChild } from "@angular/core";
import { StringUtils } from '../utils/string-utils.service';
var HBox = /** @class */ (function (_super) {
    tslib_1.__extends(HBox, _super);
    //-------constructor-----------------------------------------------------------//
    function HBox(elem, _commonService) {
        var _this = _super.call(this, elem, _commonService) || this;
        _this.elem = elem;
        _this._commonService = _commonService;
        _this._wrapContent = false;
        $($(_this.elem.nativeElement)[0]).attr('selector', 'HBox');
        _this.horizontalGap = "8";
        return _this;
    }
    Object.defineProperty(HBox.prototype, "wrapContent", {
        get: /**
         * @return {?}
         */
        function () {
            return this._wrapContent;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._wrapContent = StringUtils.isTrue(value);
            if (this._wrapContent) {
                this.hboxDiv.nativeElement.classList.add("wrapContent");
            }
        },
        enumerable: true,
        configurable: true
    });
    HBox.decorators = [
        { type: Component, args: [{
                    selector: 'HBox',
                    template: "\n     <div fxLayout=\"row\" #hboxDiv fxLayoutAlign=\"{{horizontalAlign}} {{verticalAlign}} \"  fxLayoutGap=\"{{horizontalGap}}\"  class=\"horizontalLayout\" tabindex=\"-1\">\n        <ng-content></ng-content>\n        <ng-container #_container></ng-container>\n     </div>\n  ",
                    styles: ["\n             :host {\n               margin: 0px;\n               display: block;\n               outline: none;\n             }\n             .horizontalLayout {\n               width: 100%;\n               outline: none;\n             }\n             .wrapContent {\n              flex-wrap:wrap;\n             }\n           \n   "]
                }] }
    ];
    /** @nocollapse */
    HBox.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    HBox.propDecorators = {
        hboxDiv: [{ type: ViewChild, args: ['hboxDiv',] }],
        wrapContent: [{ type: Input }]
    };
    return HBox;
}(Container));
export { HBox };
if (false) {
    /**
     * @type {?}
     * @protected
     */
    HBox.prototype.hboxDiv;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype._wrapContent;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,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