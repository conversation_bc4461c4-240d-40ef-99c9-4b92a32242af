/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SwtModule } from "./swt-module.component";
import { SwtPopUpManager } from "../managers/swt-pop-up-manager.service";
import { CommonService } from "../utils/common.service";
import { SwtUtil } from "../utils/swt-util.service";
import { SwtButton } from "./swt-button.component";
import { SwtRadioItem } from './swt-radioItem.component';
import { SwtRadioButtonGroup } from './swt-radioButtonGroup.component';
var SwtPagesToExport = /** @class */ (function (_super) {
    tslib_1.__extends(SwtPagesToExport, _super);
    function SwtPagesToExport(element, common) {
        var _this = _super.call(this, element, common) || this;
        _this.element = element;
        _this.common = common;
        _this._exportCancelFunction = new Function();
        //Export function (callback function)
        _this._exportFunction = null;
        _this.pagesToExport = "";
        return _this;
    }
    /**
     * @return {?}
     */
    SwtPagesToExport.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.radioC.label = SwtUtil.getPredictMessage('exportPages.label.current', null);
        this.radioA.label = SwtUtil.getPredictMessage('exportPages.label.allPages', null);
        this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
        this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    };
    /**
     * @return {?}
     */
    SwtPagesToExport.prototype.initData = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @return {?}
     */
    SwtPagesToExport.prototype.popupClosed = /**
     * @return {?}
     */
    function () {
        this.close();
        this._exportCancelFunction();
    };
    /**
     * @return {?}
     */
    SwtPagesToExport.prototype.defaultContentFunction = /**
     * @return {?}
     */
    function () {
        console.log("***defaultContentFunction");
    };
    /**
     * @return {?}
     */
    SwtPagesToExport.prototype.exportType = /**
     * @return {?}
     */
    function () {
        if (this.radioC.selected) {
            this.result = "current";
            this.pagesToExport = "current";
        }
        else {
            this.result = "all";
            this.pagesToExport = "all";
        }
        this.close();
        this._exportFunction();
    };
    Object.defineProperty(SwtPagesToExport.prototype, "onexportFunction", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._exportFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtPagesToExport.prototype, "exportCancelFunction", {
        get: /**
         * @return {?}
         */
        function () {
            return this._exportCancelFunction;
        },
        /**
         *
         */
        set: /**
         *
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this._exportCancelFunction = value;
        },
        enumerable: true,
        configurable: true
    });
    /**
     * This method is used to show report Progress.
     */
    /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    SwtPagesToExport.prototype.show = /**
     * This method is used to show report Progress.
     * @param {?} parent
     * @return {?}
     */
    function (parent) {
        var _this = this;
        this.win = SwtPopUpManager.createPopUp(this, SwtPagesToExport, {});
        this.win.enableResize = false;
        this.win.title = "Pages To Export";
        this.win.height = '160';
        this.win.width = '220';
        this.win.showControls = false;
        this.win.isModal = true;
        this.win.onexportFunction = this._exportFunction.bind(this);
        this.win.onClose.subscribe((/**
         * @param {?} res
         * @return {?}
         */
        function (res) {
            if (_this.win.getChild().result) {
                if (_this.win.getChild().result == "current") {
                    _this.pagesToExport = "current";
                }
                else {
                    _this.pagesToExport = "all";
                }
            }
        }));
        this.win.display();
    };
    /**
     *  This method is used to hide report Progress.
     */
    /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    SwtPagesToExport.prototype.hide = /**
     *  This method is used to hide report Progress.
     * @param {?} parent
     * @return {?}
     */
    function (parent) {
        SwtPopUpManager.hide();
    };
    /**
     * Export is canceled
     */
    /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    SwtPagesToExport.prototype.ExportCanceled = /**
     * Export is canceled
     * @param {?} event
     * @return {?}
     */
    function (event) {
        // ExternalInterface.call('finishExport');
    };
    SwtPagesToExport.decorators = [
        { type: Component, args: [{
                    selector: 'SwtPagesToExport',
                    template: "\n        <SwtModule (close)='popupClosed()' (creationComplete)='initData()' width=\"100%\" height=\"100%\" >\n        <VBox  width='100%' height='100%' paddingTop=\"10\" paddingRight=\"10\" paddingLeft=\"10\" paddingBottom=\"10\">\n            <SwtCanvas width=\"100%\" height=\"50%\">\n            <SwtRadioButtonGroup #exportButtonGroup   id=\"exportButtonGroup\"\n                                align=\"vertical\">\n                <SwtRadioItem #radioC id=\"radioC\" value=\"current\"  width=\"120\" groupName=\"exportButtonGroup\" selected=\"true\"  ></SwtRadioItem>\n                <SwtRadioItem #radioA id=\"radioA\" value=\"all\"   width=\"120\" groupName=\"exportButtonGroup\"  ></SwtRadioItem>\n            </SwtRadioButtonGroup>\n            </SwtCanvas>\n            <SwtCanvas width=\"100%\">\n            <HBox>\n\n            <SwtButton buttonMode=\"true\"\n                        id=\"okButton\"\n                        #okButton\n                        width=\"70\"\n                        enabled=\"true\"\n                        (click)=\"exportType()\"> </SwtButton>\n            <SwtButton buttonMode=\"true\"\n                        id=\"cancelButton\"\n                        #cancelButton\n                        width=\"70\"\n                        enabled=\"true\"\n                        (click)=\"popupClosed()\"></SwtButton>\n            </HBox>\n            </SwtCanvas>\n        </VBox>\n        </SwtModule>\n  ",
                    styles: ["\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtPagesToExport.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService }
    ]; };
    SwtPagesToExport.propDecorators = {
        okButton: [{ type: ViewChild, args: ['okButton',] }],
        cancelButton: [{ type: ViewChild, args: ['cancelButton',] }],
        exportButtonGroup: [{ type: ViewChild, args: ['exportButtonGroup',] }],
        radioC: [{ type: ViewChild, args: ['radioC',] }],
        radioA: [{ type: ViewChild, args: ['radioA',] }]
    };
    return SwtPagesToExport;
}(SwtModule));
export { SwtPagesToExport };
if (false) {
    /**
     * Buttons*******
     * @type {?}
     */
    SwtPagesToExport.prototype.okButton;
    /** @type {?} */
    SwtPagesToExport.prototype.cancelButton;
    /** @type {?} */
    SwtPagesToExport.prototype.exportButtonGroup;
    /** @type {?} */
    SwtPagesToExport.prototype.radioC;
    /** @type {?} */
    SwtPagesToExport.prototype.radioA;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype._exportCancelFunction;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype._exportFunction;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.win;
    /** @type {?} */
    SwtPagesToExport.prototype.pagesToExport;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.element;
    /**
     * @type {?}
     * @private
     */
    SwtPagesToExport.prototype.common;
}
//# sourceMappingURL=data:application/json;base64,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