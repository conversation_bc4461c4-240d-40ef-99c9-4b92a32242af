/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, Renderer2 } from '@angular/core';
//import { SwtAbstract } from "./swt-abstract";
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { focusManager } from '../managers/focus-manager.service';
// event.target.offsetWidth
// event.target.scrollWidth
/*
    SwtList
    <AUTHOR> Swallow TN 😎
*/
var SwtList = /** @class */ (function (_super) {
    tslib_1.__extends(SwtList, _super);
    function SwtList(elem, commonService, _renderer) {
        var _this = _super.call(this, elem, commonService) || this;
        _this.elem = elem;
        _this.commonService = commonService;
        _this._renderer = _renderer;
        _this._selectedIndices = [];
        _this._selectable = true;
        _this._selectedItems = [];
        _this._enabled = true;
        _this._allowMultipleSelection = false;
        _this._dataProvider = [];
        _this.dataProviderOld = new Array();
        _this.coloredIndex = [false];
        _this.multipleSelectNav = [false];
        _this.focusedIndex = 0;
        _this.index = 0;
        /* set change handler */
        _this.click_ = new EventEmitter();
        //    @Output('doubleClick') private doubleClick_ = new EventEmitter<any>();
        _this.toolTip = "";
        _this.showDataTip = false;
        _this.doubleClickEnabled = true;
        _this.firstCall = true;
        _this.id = "";
        return _this;
    }
    /**
     * @return {?}
     */
    SwtList.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        this.dataProviderOld = [];
        this.originalValue = [];
        this.firstShown = 0;
        if (this.enabled == undefined) {
            this.enabled = true;
        }
        if (this.selectable == undefined) {
            this.selectable = true;
        }
        if (!this.dataProvider) {
            this.dataProvider = [];
        }
        if (this.enabled === true) {
            for (var index = 1; index < this.dataProvider.length; index++) {
                this.coloredIndex[index] = false;
            }
        }
    };
    Object.defineProperty(SwtList.prototype, "allowMultipleSelection", {
        get: /**
         * @return {?}
         */
        function () {
            return this._allowMultipleSelection;
        },
        /* @Inputs */
        set: /* @Inputs */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._allowMultipleSelection = true;
                }
                else {
                    this._allowMultipleSelection = false;
                }
            }
            else {
                this._allowMultipleSelection = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "dataProvider", {
        get: /**
         * @return {?}
         */
        function () {
            return this._dataProvider;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            try {
                this._dataProvider = [];
                if (value.length == undefined) {
                    value = [value];
                }
                if ((!value) || (!value[0])) {
                    this._dataProvider = [];
                    if (this.firstCall) {
                        this.originalValue = [];
                        this.firstCall = false;
                    }
                }
                else if (value[0].length) {
                    /** @type {?} */
                    var temp = { "type": "", "value": "", "selected": 0, "content": "" };
                    for (var index = 0; index < value.length; index++) {
                        temp.value = value[index];
                        temp.content = value[index];
                        this._dataProvider[index] = tslib_1.__assign({}, temp);
                        if (this.firstCall) {
                            this.originalValue[index] = tslib_1.__assign({}, temp);
                            if (index == value.length - 1) {
                                this.firstCall = false;
                            }
                        }
                    }
                }
                else {
                    this._dataProvider = tslib_1.__spread(value);
                    if (this.firstCall) {
                        this.originalValue = tslib_1.__spread(value);
                        this.firstCall = false;
                    }
                }
                this.onChange(event);
            }
            catch (error) {
                console.error(error);
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @param {?} item
     * @return {?}
     */
    SwtList.prototype.setToolTip = /**
     * @param {?} event
     * @param {?} item
     * @return {?}
     */
    function (event, item) {
        this.toolTip = this.showDataTip && event.target.scrollWidth > event.target.offsetWidth ? item.content : '';
    };
    Object.defineProperty(SwtList.prototype, "selectedIndex", {
        get: /**
         * @return {?}
         */
        function () {
            /** @type {?} */
            var found = false;
            /** @type {?} */
            var i = 0;
            if (this.selectedIndices.length <= 1) {
                while (i < this.coloredIndex.length && !found) {
                    if (this.coloredIndex[i] == true) {
                        this._selectedIndex = i;
                        found = true;
                    }
                    i++;
                }
                if (!found) {
                    this._selectedIndex = -1;
                }
            }
            return this._selectedIndex;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.coloredIndex.fill(false);
            this.multipleSelectNav.fill(false);
            if (value != undefined && value != null && value != -1) {
                this.coloredIndex[value] = true;
                this.multipleSelectNav[value] = true;
                this.focusedIndex = value;
                this._selectedIndex = value;
                this._selectedItem = this.dataProvider[value].value;
            }
            else {
                this.selectedItem = null;
                this._selectedIndex = -1;
                this.selectedIndices = [];
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "selectedIndices", {
        get: /**
         * @return {?}
         */
        function () {
            this._selectedIndices = [];
            for (var index = 0; index < this.coloredIndex.length; index++) {
                if (this.coloredIndex[index] == true) {
                    this._selectedIndices.push(index);
                }
            }
            return this._selectedIndices;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            this.coloredIndex.fill(false);
            for (var index = 0; index < value.length; index++) {
                this.coloredIndex[value[index]] = true;
            }
            this._selectedIndices = tslib_1.__spread(value);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "width", {
        get: /**
         * @return {?}
         */
        function () {
            return this._width;
        },
        /* width getter and setter */
        set: /* width getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined) {
                if (value.indexOf('%') === -1) {
                    this._width = value + "px";
                }
                else {
                    this._width = value;
                }
            }
            this._renderer.setStyle(this.elem.nativeElement, 'width', this._width);
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "height", {
        get: /**
         * @return {?}
         */
        function () {
            return this._height;
        },
        /* @Inputs */
        /* height getter and setter */
        set: /* @Inputs */
        /* height getter and setter */
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value !== undefined) {
                if (value.indexOf('%') === -1) {
                    this._height = value + "px";
                }
                else {
                    this._height = value;
                }
            }
            this._renderer.setStyle(this.elem.nativeElement, 'height', this._height);
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    SwtList.prototype.onChange = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.dataProvider.length == this.dataProviderOld.length) {
            /** @type {?} */
            var change = !(JSON.stringify(this.dataProvider) === JSON.stringify(this.dataProviderOld));
            if (change) {
                for (var index = 0; index < this.dataProvider.length; index++) {
                    this.dataProviderOld[index] = this.dataProvider[index];
                }
                this.change_.emit(this.dataProvider);
            }
        }
        else {
            this.change_.emit(this.dataProvider);
            if (this.dataProvider.length == 0) {
                this.dataProviderOld = [];
            }
            else {
                for (var index = 0; index < this.dataProvider.length; index++) {
                    this.dataProviderOld[index] = this.dataProvider[index];
                }
            }
        }
        this.spyChanges(this.dataProvider);
    };
    // function used to handle keybords keys events
    // function used to handle keybords keys events
    /**
     * @param {?} event
     * @return {?}
     */
    SwtList.prototype.testkey = 
    // function used to handle keybords keys events
    /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (event.keyCode === 38) {
            if (!event.ctrlKey) {
                if (this.focusedIndex != this.selectedIndex) {
                    this.selectedIndex = this.focusedIndex;
                }
                event.preventDefault();
                this.scrollToTop();
                if (this.selectedIndex != 0) {
                    this.selectedIndex--;
                }
            }
            else if (event.keyCode === 38 && event.ctrlKey && this.allowMultipleSelection) {
                event.preventDefault();
                this.scrollFocusToTop();
                if (this.focusedIndex != 0) {
                    for (var index = 0; index < this.coloredIndex.length; index++) {
                        this.multipleSelectNav[index] = false;
                    }
                    this.multipleSelectNav[this.focusedIndex - 1] = true;
                    this.focusedIndex = this.focusedIndex - 1;
                }
            }
        }
        else if (event.keyCode === 40) {
            if (!event.ctrlKey) {
                if (this.focusedIndex != this.selectedIndex) {
                    this.selectedIndex = this.focusedIndex;
                }
                event.preventDefault();
                this.scrollToBottom();
                if (this.selectedIndex < this.coloredIndex.length - 1) {
                    this.selectedIndex++;
                }
            }
            else if (event.keyCode === 40 && event.ctrlKey && this.allowMultipleSelection) {
                event.preventDefault();
                this.scrollFocusToBottom();
                if (this.focusedIndex < this.coloredIndex.length - 1) {
                    for (var index = 0; index < this.coloredIndex.length; index++) {
                        this.multipleSelectNav[index] = false;
                    }
                    this.multipleSelectNav[this.focusedIndex + 1] = true;
                    this.focusedIndex = this.focusedIndex + 1;
                }
            }
        }
        if ((event.ctrlKey && event.keyCode == 32) && this.allowMultipleSelection === true) {
            this.coloredIndex[this.focusedIndex] = !this.coloredIndex[this.focusedIndex];
            if (this.coloredIndex[this.focusedIndex]) {
                this.selectedIndices.push(this.focusedIndex);
            }
        }
    };
    /**
 * This method is used to handle scrolling
 * to bottom.
 */
    /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    SwtList.prototype.scrollToBottom = /**
     * This method is used to handle scrolling
     * to bottom.
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    var viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
  * This method is used to handle scrolling
  * to Top.
  */
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    SwtList.prototype.scrollToTop = /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.selectedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    };
    /* This method is used to handle scrolling
 * to bottom.
 */
    /* This method is used to handle scrolling
     * to bottom.
     */
    /**
     * @private
     * @return {?}
     */
    SwtList.prototype.scrollFocusToBottom = /* This method is used to handle scrolling
     * to bottom.
     */
    /**
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.focusedIndex + 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // calculate the scroll value.
                    /** @type {?} */
                    var viewBottom = list.scrollTop + listHeight;
                    // update scroll bar position.
                    if (itemBottom > viewBottom) {
                        list.scrollTop = itemBottom - listHeight;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
  * This method is used to handle scrolling
  * to Top.
  */
    /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    SwtList.prototype.scrollFocusToTop = /**
     * This method is used to handle scrolling
     * to Top.
     * @private
     * @return {?}
     */
    function () {
        try {
            // make reference to list items.
            /** @type {?} */
            var list = this.list.nativeElement;
            // get list items height.
            /** @type {?} */
            var listHeight = list.offsetHeight;
            // get item index.
            /** @type {?} */
            var itemIndex = this.focusedIndex - 1;
            // if item index > -1 do.
            if (itemIndex > -1) {
                // get current item.
                /** @type {?} */
                var item = list.children[0].children[0].children[itemIndex]
                // check existence of item.
                ;
                // check existence of item.
                if (item !== undefined) {
                    // get item height.
                    /** @type {?} */
                    var itemHeight = item.offsetHeight;
                    // calculate item top position.
                    /** @type {?} */
                    var itemTop = itemIndex * itemHeight;
                    // calculate item bottom position.
                    /** @type {?} */
                    var itemBottom = itemTop + itemHeight;
                    // update scroll bar position.
                    if (itemTop < list.scrollTop) {
                        list.scrollTop = itemTop;
                    }
                }
            }
        }
        catch (error) {
            console.error(error);
        }
    };
    /**
     * @param {?} value
     * @return {?}
     */
    SwtList.prototype.addItem = /**
     * @param {?} value
     * @return {?}
     */
    function (value) {
        this.dataProvider.push(value);
        this.onChange(event);
    };
    // remove single item using it's own index
    // remove single item using it's own index
    /**
     * @param {?} index
     * @return {?}
     */
    SwtList.prototype.removeItem = 
    // remove single item using it's own index
    /**
     * @param {?} index
     * @return {?}
     */
    function (index) {
        this.dataProvider.splice(index, 1);
        if (this.dataProvider.length > 0 && index < this.dataProvider.length) {
            this.selectedIndex = index;
        }
        else if (index > 0) {
            this.selectedIndex = index - 1;
        }
        else if (index == 0) {
            this.selectedIndex = -1;
        }
        this.onChange(event);
    };
    //this method is used to remove multiple items at once 
    // @param: indexes: array of indexes
    //this method is used to remove multiple items at once 
    // @param: indexes: array of indexes
    /**
     * @param {?} indexes
     * @return {?}
     */
    SwtList.prototype.removeItems = 
    //this method is used to remove multiple items at once 
    // @param: indexes: array of indexes
    /**
     * @param {?} indexes
     * @return {?}
     */
    function (indexes) {
        /** @type {?} */
        var x = this.dataProvider;
        for (var index = 0; index < indexes.length; index++) {
            delete x[indexes[index]];
        }
        /** @type {?} */
        var filtered = x.filter((/**
         * @param {?} value
         * @param {?} index
         * @param {?} arr
         * @return {?}
         */
        function (value, index, arr) {
            return value != undefined;
        }));
        this.dataProvider = filtered;
        this.selectedIndex = indexes[0] - 1;
    };
    /**
     * @return {?}
     */
    SwtList.prototype.removeAll = /**
     * @return {?}
     */
    function () {
        this.dataProvider = [];
        this.onChange(event);
    };
    // function used to do multiple selection with ctrl & click
    // function used to do multiple selection with ctrl & click
    /**
     * @param {?} event
     * @param {?} i
     * @return {?}
     */
    SwtList.prototype.clickHandler = 
    // function used to do multiple selection with ctrl & click
    /**
     * @param {?} event
     * @param {?} i
     * @return {?}
     */
    function (event, i) {
        if (this.enabled === true) {
            if (event.ctrlKey && this.allowMultipleSelection == true) {
                this._selectedItems = new Array();
                this.coloredIndex[i] = !this.coloredIndex[i];
            }
            else if (event.shiftKey && this.allowMultipleSelection == true && this.selectedIndex != -1) {
                if (this.selectedIndex < i) {
                    /** @type {?} */
                    var temp = [];
                    /** @type {?} */
                    var lastSelected = this.selectedIndex;
                    for (var index = lastSelected; index <= i; index++) {
                        temp.push(index);
                    }
                    this.coloredIndex.fill(false);
                    this.selectedIndices = temp;
                }
                else {
                    /** @type {?} */
                    var temp = [];
                    /** @type {?} */
                    var lastSelected = this.selectedIndex;
                    for (var index = i; index <= lastSelected; index++) {
                        temp.push(index);
                    }
                    this.coloredIndex.fill(false);
                    this.selectedIndices = temp;
                }
            }
            else {
                // for (let index = 0; index < this.dataProvider.length; index++) {
                //     this.coloredIndex[index] = false;
                // }
                this._selectedItems = new Array();
                this.coloredIndex.fill(false, 0, this.coloredIndex.length - 1);
                this.selectedIndex = i;
                this.index = i;
                this.coloredIndex[i] = true;
            }
        }
    };
    Object.defineProperty(SwtList.prototype, "selectedItems", {
        get: /**
         * @return {?}
         */
        function () {
            this._selectedItems = [];
            if (this.enabled === true) {
                for (var index = 0; index < this.coloredIndex.length; index++) {
                    if (this.coloredIndex[index] === true) {
                        this._selectedItems.push(this.dataProvider[index]);
                    }
                }
                return this._selectedItems;
            }
            else {
                return null;
            }
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "selectedItem", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectedItem;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            /** @type {?} */
            var i = 0;
            /** @type {?} */
            var found = false;
            while (i < this.dataProvider.length && !found) {
                if (this.dataProvider[i].value == value) {
                    this.selectedIndex = i;
                    this._selectedItem = this.dataProvider[i].value;
                    found = true;
                }
                i++;
            }
            if (i == this.dataProvider.length - 1 && !found) {
                this.selectedIndex = -1;
                this._selectedItem = null;
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (value.toString() == "true") {
                this._enabled = true;
            }
            else if (value.toString() == "false")
                this._enabled = false;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(SwtList.prototype, "selectable", {
        get: /**
         * @return {?}
         */
        function () {
            return this._selectable;
        },
        set: /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "true") {
                    this._selectable = true;
                }
                else {
                    this._selectable = false;
                }
            }
            else {
                this._selectable = value;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @param {?} event
     * @return {?}
     */
    SwtList.prototype.spyChanges = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (JSON.stringify(this.originalValue) == JSON.stringify(event)) {
            this.onSpyNoChange.emit({ "target": this, "value": event });
        }
        else {
            this.onSpyChange.emit({ "target": this, "value": event });
        }
        ;
    };
    /**
     * @return {?}
     */
    SwtList.prototype.resetOriginalValue = /**
     * @return {?}
     */
    function () {
        this.originalValue = tslib_1.__spread(this.dataProvider);
        this.spyChanges(this.dataProvider);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtList.prototype.onClick = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        focusManager.focusTarget = this.id;
        this.click_.emit(event);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtList.prototype.doubleClicked = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        if (this.doubleClickEnabled) {
            this.doubleClick_.emit(event);
        }
    };
    SwtList.decorators = [
        { type: Component, args: [{
                    selector: 'SwtList',
                    template: "\n    <div #list id=\"ecran\" class=\"boxbox\" [ngClass]=\"{'disabl':enabled==false}\">\n    <ul #swtlistItems class=\"list-group\" (window:keydown)=\"testkey($event)\">\n        <div *ngIf=\"enabled==true&&selectable==true\">\n            <li (click)=\"clickHandler($event,i);onClick($event)\" (mouseenter)=\"setToolTip($event,item)\"\n                data-placement=\"right\" (dblclick)=\"doubleClicked($event)\" class=\"list-group-item\"\n                [ngClass]=\"{active:coloredIndex[i]==true,hover:coloredIndex!=i,multipleSelectNav:multipleSelectNav[i]==true}\"\n                *ngFor=\"let item of dataProvider; let i = index\">\n                <p  >{{ item.content }}</p>\n                <img *ngIf=\"item.img\" src=\"{{ item.img }}\"  style=\"height:20px;width:20px\"\n                    class=\"icon-item\">\n            </li>\n        </div>\n        <div *ngIf=\"!enabled || !selectable\">\n            <li *ngFor=\"let item of dataProvider; let i = index\" class=\"list-group-item-disabled\" (mouseenter)=\"setToolTip($event,item)\"\n                 data-placement=\"right\">\n                <p>{{ item.content }}</p> \n                <img *ngIf=\"item.img\" src=\"{{ item.img }}\" style=\"height:20px;width:20px\" class=\"item-img\">\n            </li>\n        </div>\n    </ul>\n</div>\n\n  ",
                    styles: ["\n    .icon-item{\n        float:right; \n        margin-right:10px\n    }\n    .item-img{\n        float:right; \n        margin-right:10px\n    }\n    .list-group-item-disabled{\n        height: 25px;\n        font-size: 14px;\n        font-family: verdana,helvetica;\n        padding: 0px 0px 0px 10px;\n        margin: 0px;\n        line-height: 25px;\n        border: none;\n        text-overflow: ellipsis; \n        white-space: nowrap;\n        overflow:hidden\n    }\n    .list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {\n        color: black;  \n    } \n    .list-group-item.active{\n        background-color: #7FCEFF;\n    }\n    .list-group-item.multipleSelectNav{\n        border-color: #7FCEFF;\n        border-width: thin;\n        border-style:solid;\n    }\n    .list-group-item:focus {\n         background-color: red; \n    }\n    .list-group{\n        margin-bottom:0px !important;\n    }\n    .list-group-item:hover {\n       background-color: #B2E1FF;\n       cursor: default;\n    }\n    .list-group-item {\n        height: 25px;\n        font-size: 14px;\n        font-family: verdana,helvetica;\n        padding: 0px 0px 0px 10px;\n        margin: 0px;\n        line-height: 25px;\n        border: none;\n        text-overflow: ellipsis; \n        white-space: nowrap;\n        overflow:hidden\n    }\n    \n    .boxbox{\n        height:100%;\n        width:100%;\n        border: 1px solid #ddd; \n        overflow: auto;\n        background-color: white;\n    }\n    .boxbox:hover{\n        cursor: default;\n    }\n    .disabl{\n        z-index: 1;\n        background-color: #ddd;\n        opacity: 0.3;\n    }\n"]
                }] }
    ];
    /** @nocollapse */
    SwtList.ctorParameters = function () { return [
        { type: ElementRef },
        { type: CommonService },
        { type: Renderer2 }
    ]; };
    SwtList.propDecorators = {
        click_: [{ type: Output, args: ['itemClick',] }],
        toolTip: [{ type: Input, args: ['toolTip',] }],
        showDataTip: [{ type: Input, args: ['showDataTips',] }],
        doubleClickEnabled: [{ type: Input, args: ['doubleClickEnabled',] }],
        allowMultipleSelection: [{ type: Input }],
        id: [{ type: Input }, { type: Input, args: ["id",] }],
        selectedIndex: [{ type: Input }],
        selectedIndices: [{ type: Input }],
        width: [{ type: Input }],
        height: [{ type: Input }],
        swtlistItems: [{ type: ViewChild, args: ["swtlistItems",] }],
        list: [{ type: ViewChild, args: ["list",] }],
        enabled: [{ type: Input }],
        selectable: [{ type: Input }]
    };
    return SwtList;
}(Container));
export { SwtList };
if (false) {
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedIndices;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectable;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._height;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedItems;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._selectedItem;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.SwtListObject;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._width;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._allowMultipleSelection;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._dataProvider;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.dataProviderOld;
    /** @type {?} */
    SwtList.prototype.coloredIndex;
    /** @type {?} */
    SwtList.prototype.multipleSelectNav;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.focusedIndex;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.index;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.firstShown;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.click_;
    /** @type {?} */
    SwtList.prototype.toolTip;
    /** @type {?} */
    SwtList.prototype.showDataTip;
    /** @type {?} */
    SwtList.prototype.doubleClickEnabled;
    /** @type {?} */
    SwtList.prototype.firstCall;
    /** @type {?} */
    SwtList.prototype.id;
    /** @type {?} */
    SwtList.prototype.swtlistItems;
    /** @type {?} */
    SwtList.prototype.list;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtList.prototype._renderer;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3d0LWxpc3QuY29tcG9uZW50LmpzIiwic291cmNlUm9vdCI6Im5nOi8vc3d0LXRvb2wtYm94LyIsInNvdXJjZXMiOlsic3JjL2FwcC9tb2R1bGVzL3N3dC10b29sYm94L2NvbS9zd2FsbG93L2NvbnRyb2xzL3N3dC1saXN0LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7OztBQUFBLE9BQU8sRUFBRSxTQUFTLEVBQVUsS0FBSyxFQUFhLE1BQU0sRUFBRSxZQUFZLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBNEIsU0FBUyxFQUFFLE1BQU0sZUFBZSxDQUFDOztBQUd0SixPQUFPLEVBQUUsU0FBUyxFQUFFLE1BQU0sdUNBQXVDLENBQUM7QUFDbEUsT0FBTyxFQUFFLGFBQWEsRUFBRSxNQUFNLHlCQUF5QixDQUFDO0FBQ3hELE9BQU8sRUFBRSxZQUFZLEVBQUUsTUFBTSxtQ0FBbUMsQ0FBQzs7Ozs7OztBQU9qRTtJQWtHNkIsbUNBQVM7SUFrQmxDLGlCQUFvQixJQUFnQixFQUFVLGFBQTRCLEVBQVUsU0FBb0I7UUFBeEcsWUFDSSxrQkFBTSxJQUFJLEVBQUUsYUFBYSxDQUFDLFNBQzdCO1FBRm1CLFVBQUksR0FBSixJQUFJLENBQVk7UUFBVSxtQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUFVLGVBQVMsR0FBVCxTQUFTLENBQVc7UUFqQmhHLHNCQUFnQixHQUFRLEVBQUUsQ0FBQztRQUUzQixpQkFBVyxHQUFRLElBQUksQ0FBQztRQUV4QixvQkFBYyxHQUFHLEVBQUUsQ0FBQztRQUlwQixjQUFRLEdBQVksSUFBSSxDQUFBO1FBQ3hCLDZCQUF1QixHQUFHLEtBQUssQ0FBQztRQUNoQyxtQkFBYSxHQUFRLEVBQUUsQ0FBQTtRQUN2QixxQkFBZSxHQUFHLElBQUksS0FBSyxFQUFFLENBQUM7UUFDL0Isa0JBQVksR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQ3RCLHVCQUFpQixHQUFHLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDMUIsa0JBQVksR0FBRyxDQUFDLENBQUE7UUFDaEIsV0FBSyxHQUFHLENBQUMsQ0FBQTs7UUF5QlksWUFBTSxHQUFHLElBQUksWUFBWSxFQUFPLENBQUM7O1FBRTVDLGFBQU8sR0FBVyxFQUFFLENBQUM7UUFDaEIsaUJBQVcsR0FBWSxLQUFLLENBQUM7UUFDdkIsd0JBQWtCLEdBQVksSUFBSSxDQUFDO1FBaUJoRSxlQUFTLEdBQVksSUFBSSxDQUFDO1FBRWIsUUFBRSxHQUFXLEVBQUUsQ0FBQzs7SUE1QzdCLENBQUM7Ozs7SUFDRCwwQkFBUTs7O0lBQVI7UUFDSSxJQUFJLENBQUMsZUFBZSxHQUFHLEVBQUUsQ0FBQTtRQUN6QixJQUFJLENBQUMsYUFBYSxHQUFHLEVBQUUsQ0FBQTtRQUN2QixJQUFJLENBQUMsVUFBVSxHQUFHLENBQUMsQ0FBQTtRQUNuQixJQUFJLElBQUksQ0FBQyxPQUFPLElBQUksU0FBUyxFQUFFO1lBQzNCLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFBO1NBQ3RCO1FBQ0QsSUFBSSxJQUFJLENBQUMsVUFBVSxJQUFJLFNBQVMsRUFBRTtZQUM5QixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQTtTQUN6QjtRQUNELElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ3BCLElBQUksQ0FBQyxZQUFZLEdBQUcsRUFBRSxDQUFBO1NBQ3pCO1FBQ0QsSUFBSSxJQUFJLENBQUMsT0FBTyxLQUFLLElBQUksRUFBRTtZQUN2QixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7Z0JBQzNELElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLEdBQUcsS0FBSyxDQUFBO2FBQ25DO1NBQ0o7SUFDTCxDQUFDO0lBUUQsc0JBQ0ksMkNBQXNCOzs7O1FBVzFCO1lBQ0ksT0FBTyxJQUFJLENBQUMsdUJBQXVCLENBQUM7UUFDeEMsQ0FBQztRQWZELGFBQWE7Ozs7OztRQUNiLFVBQzJCLEtBQUs7WUFDNUIsSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssUUFBUSxFQUFFO2dCQUM3QixJQUFJLEtBQUssS0FBSyxNQUFNLEVBQUU7b0JBQ2xCLElBQUksQ0FBQyx1QkFBdUIsR0FBRyxJQUFJLENBQUM7aUJBQ3ZDO3FCQUFNO29CQUNILElBQUksQ0FBQyx1QkFBdUIsR0FBRyxLQUFLLENBQUM7aUJBQ3hDO2FBQ0o7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLHVCQUF1QixHQUFHLEtBQUssQ0FBQzthQUN4QztRQUNMLENBQUM7OztPQUFBO0lBT0Qsc0JBQUksaUNBQVk7Ozs7UUF3Q2hCO1lBQ0ksT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO1FBQzlCLENBQUM7Ozs7O1FBMUNELFVBQWlCLEtBQUs7WUFDbEIsSUFBSTtnQkFDQSxJQUFJLENBQUMsYUFBYSxHQUFHLEVBQUUsQ0FBQTtnQkFDdkIsSUFBSSxLQUFLLENBQUMsTUFBTSxJQUFJLFNBQVMsRUFBRTtvQkFDM0IsS0FBSyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUE7aUJBQ2xCO2dCQUNELElBQUksQ0FBQyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRTtvQkFDekIsSUFBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUE7b0JBQ3ZCLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTt3QkFDaEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxFQUFFLENBQUM7d0JBQ3hCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO3FCQUMxQjtpQkFDSjtxQkFDSSxJQUFJLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUU7O3dCQUNsQixJQUFJLEdBQUcsRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFLE9BQU8sRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLENBQUMsRUFBRSxTQUFTLEVBQUUsRUFBRSxFQUFFO29CQUNwRSxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTt3QkFDL0MsSUFBSSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7d0JBQ3pCLElBQUksQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO3dCQUMzQixJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyx3QkFBUSxJQUFJLENBQUUsQ0FBQTt3QkFDdkMsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFOzRCQUNoQixJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyx3QkFBUSxJQUFJLENBQUUsQ0FBQTs0QkFDdkMsSUFBSSxLQUFLLElBQUksS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7Z0NBQzNCLElBQUksQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDOzZCQUMxQjt5QkFDSjtxQkFDSjtpQkFDSjtxQkFDSTtvQkFDRCxJQUFJLENBQUMsYUFBYSxvQkFBTyxLQUFLLENBQUMsQ0FBQztvQkFDaEMsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO3dCQUNoQixJQUFJLENBQUMsYUFBYSxvQkFBTyxLQUFLLENBQUMsQ0FBQzt3QkFDaEMsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7cUJBQzFCO2lCQUNKO2dCQUNELElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7YUFDdkI7WUFDRCxPQUFPLEtBQUssRUFBRTtnQkFDVixPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQ3hCO1FBQ0wsQ0FBQzs7O09BQUE7Ozs7OztJQUlELDRCQUFVOzs7OztJQUFWLFVBQVcsS0FBSyxFQUFFLElBQUk7UUFDbEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsV0FBVyxJQUFJLEtBQUssQ0FBQyxNQUFNLENBQUMsV0FBVyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUE7SUFDOUcsQ0FBQztJQUNELHNCQUNJLGtDQUFhOzs7O1FBZ0JqQjs7Z0JBQ1EsS0FBSyxHQUFHLEtBQUs7O2dCQUFFLENBQUMsR0FBRyxDQUFDO1lBQ3hCLElBQUksSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUFFO2dCQUNsQyxPQUFPLENBQUMsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sSUFBSSxDQUFDLEtBQUssRUFBRTtvQkFDM0MsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksRUFBRTt3QkFDOUIsSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLENBQUE7d0JBQ3ZCLEtBQUssR0FBRyxJQUFJLENBQUE7cUJBQ2Y7b0JBQ0QsQ0FBQyxFQUFFLENBQUE7aUJBQ047Z0JBQ0QsSUFBSSxDQUFDLEtBQUssRUFBRTtvQkFDUixJQUFJLENBQUMsY0FBYyxHQUFHLENBQUMsQ0FBQyxDQUFBO2lCQUMzQjthQUNKO1lBQ0QsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO1FBQy9CLENBQUM7Ozs7O1FBaENELFVBQ2tCLEtBQUs7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDN0IsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUNsQyxJQUFJLEtBQUssSUFBSSxTQUFTLElBQUksS0FBSyxJQUFJLElBQUksSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEVBQUU7Z0JBQ3BELElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFBO2dCQUMvQixJQUFJLENBQUMsaUJBQWlCLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFBO2dCQUNwQyxJQUFJLENBQUMsWUFBWSxHQUFHLEtBQUssQ0FBQTtnQkFDekIsSUFBSSxDQUFDLGNBQWMsR0FBRyxLQUFLLENBQUE7Z0JBQzNCLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLENBQUE7YUFDdEQ7aUJBQ0k7Z0JBQ0QsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUE7Z0JBQ3hCLElBQUksQ0FBQyxjQUFjLEdBQUcsQ0FBQyxDQUFDLENBQUE7Z0JBQ3hCLElBQUksQ0FBQyxlQUFlLEdBQUcsRUFBRSxDQUFBO2FBQzVCO1FBQ0wsQ0FBQzs7O09BQUE7SUFpQkQsc0JBQ0ksb0NBQWU7Ozs7UUFPbkI7WUFDSSxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsRUFBRSxDQUFBO1lBQzFCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtnQkFDM0QsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxJQUFJLElBQUksRUFBRTtvQkFDbEMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtpQkFDcEM7YUFDSjtZQUNELE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO1FBQ2pDLENBQUM7Ozs7O1FBaEJELFVBQ29CLEtBQUs7WUFDckIsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDN0IsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLEtBQUssQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7Z0JBQy9DLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFBO2FBQ3pDO1lBQ0QsSUFBSSxDQUFDLGdCQUFnQixvQkFBTyxLQUFLLENBQUMsQ0FBQTtRQUN0QyxDQUFDOzs7T0FBQTtJQVdELHNCQUNJLDBCQUFLOzs7O1FBVVQ7WUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDdkIsQ0FBQztRQWRELDZCQUE2Qjs7Ozs7O1FBQzdCLFVBQ1UsS0FBYTtZQUNuQixJQUFJLEtBQUssS0FBSyxTQUFTLEVBQUU7Z0JBQ3JCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtvQkFDM0IsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2lCQUM5QjtxQkFBTTtvQkFDSCxJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztpQkFDdkI7YUFDSjtZQUNELElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDMUUsQ0FBQzs7O09BQUE7SUFNRCxzQkFDSSwyQkFBTTs7OztRQVVWO1lBQ0ksT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO1FBQ3hCLENBQUM7UUFmRCxhQUFhO1FBQ2IsOEJBQThCOzs7Ozs7O1FBQzlCLFVBQ1csS0FBYTtZQUNwQixJQUFJLEtBQUssS0FBSyxTQUFTLEVBQUU7Z0JBQ3JCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtvQkFDM0IsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO2lCQUMvQjtxQkFBTTtvQkFDSCxJQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztpQkFDeEI7YUFDSjtZQUNELElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLFFBQVEsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDNUUsQ0FBQzs7O09BQUE7Ozs7O0lBTUQsMEJBQVE7Ozs7SUFBUixVQUFTLEtBQUs7UUFDVixJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxJQUFJLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxFQUFFOztnQkFDckQsTUFBTSxHQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUNuRyxJQUFJLE1BQU0sRUFBRTtnQkFDUixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7b0JBQzNELElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQTtpQkFDekQ7Z0JBQ0QsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO2FBQ3ZDO1NBQ0o7YUFDSTtZQUNELElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQTtZQUNwQyxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxJQUFJLENBQUMsRUFBRTtnQkFDL0IsSUFBSSxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUE7YUFDNUI7aUJBQ0k7Z0JBQ0QsS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO29CQUMzRCxJQUFJLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUE7aUJBQ3pEO2FBQ0o7U0FDSjtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO0lBQ3ZDLENBQUM7SUFDRCwrQ0FBK0M7Ozs7OztJQUMvQyx5QkFBTzs7Ozs7O0lBQVAsVUFBUSxLQUFLO1FBQ1QsSUFBSSxLQUFLLENBQUMsT0FBTyxLQUFLLEVBQUUsRUFBRTtZQUN0QixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRTtnQkFDaEIsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUU7b0JBQ3pDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQTtpQkFDekM7Z0JBQ0QsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUN2QixJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7Z0JBQ25CLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxDQUFDLEVBQUU7b0JBQ3pCLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztpQkFDeEI7YUFDSjtpQkFDSSxJQUFJLEtBQUssQ0FBQyxPQUFPLEtBQUssRUFBRSxJQUFJLEtBQUssQ0FBQyxPQUFPLElBQUksSUFBSSxDQUFDLHNCQUFzQixFQUFFO2dCQUMzRSxLQUFLLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQ3ZCLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO2dCQUN4QixJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksQ0FBQyxFQUFFO29CQUN4QixLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7d0JBQzNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsR0FBRyxLQUFLLENBQUE7cUJBQ3hDO29CQUNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQTtvQkFDcEQsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQTtpQkFDNUM7YUFDSjtTQUNKO2FBQ0ksSUFBSSxLQUFLLENBQUMsT0FBTyxLQUFLLEVBQUUsRUFBRTtZQUMzQixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRTtnQkFDaEIsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUU7b0JBQ3pDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQTtpQkFDekM7Z0JBQ0QsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUN2QixJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQ3RCLElBQUksSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7b0JBQ25ELElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztpQkFDeEI7YUFDSjtpQkFDSSxJQUFJLEtBQUssQ0FBQyxPQUFPLEtBQUssRUFBRSxJQUFJLEtBQUssQ0FBQyxPQUFPLElBQUksSUFBSSxDQUFDLHNCQUFzQixFQUFFO2dCQUMzRSxLQUFLLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQ3ZCLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO2dCQUMzQixJQUFJLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO29CQUNsRCxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUU7d0JBQzNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsR0FBRyxLQUFLLENBQUE7cUJBQ3hDO29CQUNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQTtvQkFDcEQsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQTtpQkFDNUM7YUFDSjtTQUNKO1FBQ0QsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLElBQUksS0FBSyxDQUFDLE9BQU8sSUFBSSxFQUFFLENBQUMsSUFBSSxJQUFJLENBQUMsc0JBQXNCLEtBQUssSUFBSSxFQUFFO1lBQ2hGLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7WUFDNUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRTtnQkFDdEMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO2FBQy9DO1NBQ0o7SUFDTCxDQUFDO0lBQ0Q7OztHQUdEOzs7Ozs7O0lBQ1MsZ0NBQWM7Ozs7OztJQUF0QjtRQUNJLElBQUk7OztnQkFFSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhOzs7Z0JBRTlCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O2dCQUU5QixTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDO1lBQ3RDLHlCQUF5QjtZQUN6QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTs7O29CQUVaLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDO2dCQUMzRCwyQkFBMkI7O2dCQUEzQiwyQkFBMkI7Z0JBQzNCLElBQUksSUFBSSxLQUFLLFNBQVMsRUFBRTs7O3dCQUVoQixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7Ozt3QkFFOUIsT0FBTyxHQUFHLFNBQVMsR0FBRyxVQUFVOzs7d0JBRWhDLFVBQVUsR0FBRyxPQUFPLEdBQUcsVUFBVTs7O3dCQUVqQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVO29CQUM1Qyw4QkFBOEI7b0JBQzlCLElBQUksVUFBVSxHQUFHLFVBQVUsRUFBRTt3QkFDekIsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVLEdBQUcsVUFBVSxDQUFDO3FCQUM1QztpQkFDSjthQUNKO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDdkI7SUFDTCxDQUFDO0lBQ0Q7OztJQUdBOzs7Ozs7O0lBQ1EsNkJBQVc7Ozs7OztJQUFuQjtRQUNJLElBQUk7OztnQkFFSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhOzs7Z0JBRTlCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O2dCQUU5QixTQUFTLEdBQUcsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDO1lBQ3RDLHlCQUF5QjtZQUN6QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTs7O29CQUVaLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDO2dCQUMzRCwyQkFBMkI7O2dCQUEzQiwyQkFBMkI7Z0JBQzNCLElBQUksSUFBSSxLQUFLLFNBQVMsRUFBRTs7O3dCQUVoQixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7Ozt3QkFFOUIsT0FBTyxHQUFHLFNBQVMsR0FBRyxVQUFVOzs7d0JBRWhDLFVBQVUsR0FBRyxPQUFPLEdBQUcsVUFBVTtvQkFDckMsOEJBQThCO29CQUM5QixJQUFJLE9BQU8sR0FBRyxJQUFJLENBQUMsU0FBUyxFQUFFO3dCQUMxQixJQUFJLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQztxQkFDNUI7aUJBQ0o7YUFDSjtTQUNKO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDWixPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3ZCO0lBQ0wsQ0FBQztJQUNEOztHQUVEOzs7Ozs7OztJQUNTLHFDQUFtQjs7Ozs7OztJQUEzQjtRQUNJLElBQUk7OztnQkFFSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhOzs7Z0JBRTlCLFVBQVUsR0FBRyxJQUFJLENBQUMsWUFBWTs7O2dCQUU5QixTQUFTLEdBQUcsSUFBSSxDQUFDLFlBQVksR0FBRyxDQUFDO1lBQ3JDLHlCQUF5QjtZQUN6QixJQUFJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRTs7O29CQUVaLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDO2dCQUMzRCwyQkFBMkI7O2dCQUEzQiwyQkFBMkI7Z0JBQzNCLElBQUksSUFBSSxLQUFLLFNBQVMsRUFBRTs7O3dCQUVoQixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7Ozt3QkFFOUIsT0FBTyxHQUFHLFNBQVMsR0FBRyxVQUFVOzs7d0JBRWhDLFVBQVUsR0FBRyxPQUFPLEdBQUcsVUFBVTs7O3dCQUVqQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVO29CQUM1Qyw4QkFBOEI7b0JBQzlCLElBQUksVUFBVSxHQUFHLFVBQVUsRUFBRTt3QkFDekIsSUFBSSxDQUFDLFNBQVMsR0FBRyxVQUFVLEdBQUcsVUFBVSxDQUFDO3FCQUM1QztpQkFDSjthQUNKO1NBQ0o7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDdkI7SUFDTCxDQUFDO0lBQ0Q7OztJQUdBOzs7Ozs7O0lBQ1Esa0NBQWdCOzs7Ozs7SUFBeEI7UUFDSSxJQUFJOzs7Z0JBRUksSUFBSSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYTs7O2dCQUU5QixVQUFVLEdBQUcsSUFBSSxDQUFDLFlBQVk7OztnQkFFOUIsU0FBUyxHQUFHLElBQUksQ0FBQyxZQUFZLEdBQUcsQ0FBQztZQUNyQyx5QkFBeUI7WUFDekIsSUFBSSxTQUFTLEdBQUcsQ0FBQyxDQUFDLEVBQUU7OztvQkFFWixJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQztnQkFDM0QsMkJBQTJCOztnQkFBM0IsMkJBQTJCO2dCQUMzQixJQUFJLElBQUksS0FBSyxTQUFTLEVBQUU7Ozt3QkFFaEIsVUFBVSxHQUFHLElBQUksQ0FBQyxZQUFZOzs7d0JBRTlCLE9BQU8sR0FBRyxTQUFTLEdBQUcsVUFBVTs7O3dCQUVoQyxVQUFVLEdBQUcsT0FBTyxHQUFHLFVBQVU7b0JBQ3JDLDhCQUE4QjtvQkFDOUIsSUFBSSxPQUFPLEdBQUcsSUFBSSxDQUFDLFNBQVMsRUFBRTt3QkFDMUIsSUFBSSxDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUM7cUJBQzVCO2lCQUNKO2FBQ0o7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUN2QjtJQUNMLENBQUM7Ozs7O0lBQ0QseUJBQU87Ozs7SUFBUCxVQUFRLEtBQUs7UUFDVCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUM3QixJQUFJLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFBO0lBQ3hCLENBQUM7SUFDRCwwQ0FBMEM7Ozs7OztJQUMxQyw0QkFBVTs7Ozs7O0lBQVYsVUFBVyxLQUFLO1FBQ1osSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFBO1FBQ2xDLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRTtZQUNsRSxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQTtTQUM3QjthQUNJLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtZQUNoQixJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssR0FBRyxDQUFDLENBQUE7U0FDakM7YUFDSSxJQUFJLEtBQUssSUFBSSxDQUFDLEVBQUU7WUFDakIsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDLENBQUMsQ0FBQTtTQUMxQjtRQUNELElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDeEIsQ0FBQztJQUNELHVEQUF1RDtJQUN2RCxvQ0FBb0M7Ozs7Ozs7SUFDcEMsNkJBQVc7Ozs7Ozs7SUFBWCxVQUFZLE9BQU87O1lBQ1gsQ0FBQyxHQUFHLElBQUksQ0FBQyxZQUFZO1FBQ3pCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxPQUFPLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxFQUFFO1lBQ2pELE9BQU8sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBO1NBQzNCOztZQUNHLFFBQVEsR0FBRyxDQUFDLENBQUMsTUFBTTs7Ozs7O1FBQUMsVUFBVSxLQUFLLEVBQUUsS0FBSyxFQUFFLEdBQUc7WUFDL0MsT0FBTyxLQUFLLElBQUksU0FBUyxDQUFDO1FBQzlCLENBQUMsRUFBQztRQUNGLElBQUksQ0FBQyxZQUFZLEdBQUcsUUFBUSxDQUFBO1FBQzVCLElBQUksQ0FBQyxhQUFhLEdBQUksT0FBTyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQTtJQUN4QyxDQUFDOzs7O0lBQ0QsMkJBQVM7OztJQUFUO1FBQ0ksSUFBSSxDQUFDLFlBQVksR0FBRyxFQUFFLENBQUE7UUFDdEIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUN4QixDQUFDO0lBQ0QsMkRBQTJEOzs7Ozs7O0lBQzNELDhCQUFZOzs7Ozs7O0lBQVosVUFBYSxLQUFLLEVBQUUsQ0FBQztRQUNqQixJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssSUFBSSxFQUFFO1lBQ3ZCLElBQUksS0FBSyxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsc0JBQXNCLElBQUksSUFBSSxFQUFFO2dCQUN0RCxJQUFJLENBQUMsY0FBYyxHQUFHLElBQUksS0FBSyxFQUFFLENBQUM7Z0JBQ2xDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQy9DO2lCQUNJLElBQUksS0FBSyxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsc0JBQXNCLElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxhQUFhLElBQUksQ0FBQyxDQUFDLEVBQUU7Z0JBQ3hGLElBQUksSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDLEVBQUU7O3dCQUNwQixJQUFJLEdBQUcsRUFBRTs7d0JBQ1QsWUFBWSxHQUFHLElBQUksQ0FBQyxhQUFhO29CQUNyQyxLQUFLLElBQUksS0FBSyxHQUFHLFlBQVksRUFBRSxLQUFLLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxFQUFFO3dCQUNoRCxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO3FCQUNuQjtvQkFDRCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtvQkFDN0IsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUE7aUJBQzlCO3FCQUFNOzt3QkFDQyxJQUFJLEdBQUcsRUFBRTs7d0JBQ1QsWUFBWSxHQUFHLElBQUksQ0FBQyxhQUFhO29CQUNyQyxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLElBQUksWUFBWSxFQUFFLEtBQUssRUFBRSxFQUFFO3dCQUNoRCxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO3FCQUNuQjtvQkFDRCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtvQkFDN0IsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUE7aUJBQzlCO2FBQ0o7aUJBQ0k7Z0JBQ0QsbUVBQW1FO2dCQUNuRSx3Q0FBd0M7Z0JBQ3hDLElBQUk7Z0JBQ0osSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLEtBQUssRUFBRSxDQUFDO2dCQUNsQyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFBO2dCQUM5RCxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQztnQkFDdkIsSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUE7Z0JBQ2QsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUE7YUFDOUI7U0FFSjtJQUNMLENBQUM7SUFDRCxzQkFBSSxrQ0FBYTs7OztRQUVqQjtZQUNJLElBQUksQ0FBQyxjQUFjLEdBQUcsRUFBRSxDQUFBO1lBQ3hCLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQUU7Z0JBQ3ZCLEtBQUssSUFBSSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsRUFBRTtvQkFDM0QsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxLQUFLLElBQUksRUFBRTt3QkFDbkMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBO3FCQUNyRDtpQkFDSjtnQkFDRCxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7YUFDOUI7aUJBQ0k7Z0JBQ0QsT0FBTyxJQUFJLENBQUE7YUFDZDtRQUNMLENBQUM7Ozs7O1FBZkQsVUFBa0IsS0FBSztRQUN2QixDQUFDOzs7T0FBQTtJQWVELHNCQUFJLGlDQUFZOzs7O1FBZ0JoQjtZQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztRQUM5QixDQUFDOzs7OztRQWxCRCxVQUFpQixLQUFLOztnQkFDZCxDQUFDLEdBQUcsQ0FBQzs7Z0JBQ0wsS0FBSyxHQUFHLEtBQUs7WUFDakIsT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLElBQUksQ0FBQyxLQUFLLEVBQUU7Z0JBQzNDLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLElBQUksS0FBSyxFQUFFO29CQUNyQyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQTtvQkFDdEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQTtvQkFDL0MsS0FBSyxHQUFHLElBQUksQ0FBQTtpQkFDZjtnQkFDRCxDQUFDLEVBQUUsQ0FBQTthQUNOO1lBQ0QsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFO2dCQUM3QyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQyxDQUFBO2dCQUN2QixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQTthQUM1QjtRQUNMLENBQUM7OztPQUFBO0lBSUQsc0JBQ0ksNEJBQU87Ozs7UUFPWDtZQUNJLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztRQUN6QixDQUFDOzs7OztRQVZELFVBQ1ksS0FBSztZQUNiLElBQUksS0FBSyxDQUFDLFFBQVEsRUFBRSxJQUFJLE1BQU0sRUFBRTtnQkFDNUIsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUE7YUFDdkI7aUJBQ0csSUFBSSxLQUFLLENBQUMsUUFBUSxFQUFFLElBQUksT0FBTztnQkFDM0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUE7UUFDakMsQ0FBQzs7O09BQUE7SUFJRCxzQkFDSSwrQkFBVTs7OztRQVdkO1lBQ0ksT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBQzVCLENBQUM7Ozs7O1FBZEQsVUFDZSxLQUFLO1lBQ2hCLElBQUksT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLFFBQVEsRUFBRTtnQkFDN0IsSUFBSSxLQUFLLEtBQUssTUFBTSxFQUFFO29CQUNsQixJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQztpQkFDM0I7cUJBQU07b0JBQ0gsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUM7aUJBQzVCO2FBQ0o7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUM7YUFDNUI7UUFDTCxDQUFDOzs7T0FBQTs7Ozs7SUFJRCw0QkFBVTs7OztJQUFWLFVBQVcsS0FBVTtRQUNqQixJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDN0QsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1NBQy9EO2FBQ0k7WUFDRCxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7U0FDN0Q7UUFBQSxDQUFDO0lBQ04sQ0FBQzs7OztJQUNELG9DQUFrQjs7O0lBQWxCO1FBQ0ksSUFBSSxDQUFDLGFBQWEsb0JBQU8sSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO1FBQzNDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQ3RDLENBQUM7Ozs7O0lBQ0QseUJBQU87Ozs7SUFBUCxVQUFRLEtBQUs7UUFDVCxZQUFZLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUM7UUFDbkMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDM0IsQ0FBQzs7Ozs7SUFDRCwrQkFBYTs7OztJQUFiLFVBQWMsS0FBSztRQUNmLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFO1lBQ3pCLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ2hDO0lBQ0wsQ0FBQzs7Z0JBeHBCSixTQUFTLFNBQUM7b0JBQ1AsUUFBUSxFQUFFLFNBQVM7b0JBQ25CLFFBQVEsRUFBRSxzeUNBdUJYOzZCQUNVLGtwREFzRVo7aUJBQ0E7Ozs7Z0JBN0c4RSxVQUFVO2dCQUloRixhQUFhO2dCQUorRixTQUFTOzs7eUJBdUp6SCxNQUFNLFNBQUMsV0FBVzswQkFFbEIsS0FBSyxTQUFDLFNBQVM7OEJBQ2YsS0FBSyxTQUFDLGNBQWM7cUNBQ3BCLEtBQUssU0FBQyxvQkFBb0I7eUNBRTFCLEtBQUs7cUJBZ0JMLEtBQUssWUFDTCxLQUFLLFNBQUMsSUFBSTtnQ0ErQ1YsS0FBSztrQ0FpQ0wsS0FBSzt3QkFrQkwsS0FBSzt5QkFnQkwsS0FBSzsrQkFjTCxTQUFTLFNBQUMsY0FBYzt1QkFDeEIsU0FBUyxTQUFDLE1BQU07MEJBdVVoQixLQUFLOzZCQVdMLEtBQUs7O0lBb0NWLGNBQUM7Q0FBQSxBQXpwQkQsQ0FrRzZCLFNBQVMsR0F1akJyQztTQXZqQlksT0FBTzs7Ozs7O0lBQ2hCLG1DQUFtQzs7Ozs7SUFDbkMsaUNBQTRCOzs7OztJQUM1Qiw4QkFBZ0M7Ozs7O0lBQ2hDLDBCQUF3Qjs7Ozs7SUFDeEIsaUNBQTRCOzs7OztJQUM1QixnQ0FBc0I7Ozs7O0lBQ3RCLGdDQUEyQjs7Ozs7SUFDM0IseUJBQXVCOzs7OztJQUN2QiwyQkFBZ0M7Ozs7O0lBQ2hDLDBDQUF3Qzs7Ozs7SUFDeEMsZ0NBQStCOzs7OztJQUMvQixrQ0FBc0M7O0lBQ3RDLCtCQUE2Qjs7SUFDN0Isb0NBQWtDOzs7OztJQUNsQywrQkFBd0I7Ozs7O0lBQ3hCLHdCQUFpQjs7Ozs7SUFDakIsNkJBQWtCOzs7OztJQXdCbEIseUJBQThEOztJQUU5RCwwQkFBdUM7O0lBQ3ZDLDhCQUFvRDs7SUFDcEQscUNBQWdFOztJQWlCaEUsNEJBQTBCOztJQUMxQixxQkFDNkI7O0lBZ0k3QiwrQkFBb0Q7O0lBQ3BELHVCQUFvQzs7Ozs7SUEvS3hCLHVCQUF3Qjs7Ozs7SUFBRSxnQ0FBb0M7Ozs7O0lBQUUsNEJBQTRCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29tcG9uZW50LCBPbkluaXQsIElucHV0LCBPbkNoYW5nZXMsIE91dHB1dCwgRXZlbnRFbWl0dGVyLCBWaWV3Q2hpbGQsIEVsZW1lbnRSZWYsIENvbXBvbmVudEZhY3RvcnlSZXNvbHZlciwgUmVuZGVyZXIyIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5pbXBvcnQgeyBFbGVtZW50RGVmIH0gZnJvbSAnQGFuZ3VsYXIvY29yZS9zcmMvdmlldyc7XG4vL2ltcG9ydCB7IFN3dEFic3RyYWN0IH0gZnJvbSBcIi4vc3d0LWFic3RyYWN0XCI7XG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiLi4vY29udGFpbmVycy9zd3QtY29udGFpbmVyLmNvbXBvbmVudFwiO1xuaW1wb3J0IHsgQ29tbW9uU2VydmljZSB9IGZyb20gXCIuLi91dGlscy9jb21tb24uc2VydmljZVwiO1xuaW1wb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSAnLi4vbWFuYWdlcnMvZm9jdXMtbWFuYWdlci5zZXJ2aWNlJztcbi8vIGV2ZW50LnRhcmdldC5vZmZzZXRXaWR0aFxuLy8gZXZlbnQudGFyZ2V0LnNjcm9sbFdpZHRoXG4vKiBcbiAgICBTd3RMaXN0IFxuICAgIEBBdXRob3IgS2hhbGlsLkIgU3dhbGxvdyBUTiDwn5iOXG4qL1xuQENvbXBvbmVudCh7XG4gICAgc2VsZWN0b3I6ICdTd3RMaXN0JyxcbiAgICB0ZW1wbGF0ZTogYFxuICAgIDxkaXYgI2xpc3QgaWQ9XCJlY3JhblwiIGNsYXNzPVwiYm94Ym94XCIgW25nQ2xhc3NdPVwieydkaXNhYmwnOmVuYWJsZWQ9PWZhbHNlfVwiPlxuICAgIDx1bCAjc3d0bGlzdEl0ZW1zIGNsYXNzPVwibGlzdC1ncm91cFwiICh3aW5kb3c6a2V5ZG93bik9XCJ0ZXN0a2V5KCRldmVudClcIj5cbiAgICAgICAgPGRpdiAqbmdJZj1cImVuYWJsZWQ9PXRydWUmJnNlbGVjdGFibGU9PXRydWVcIj5cbiAgICAgICAgICAgIDxsaSAoY2xpY2spPVwiY2xpY2tIYW5kbGVyKCRldmVudCxpKTtvbkNsaWNrKCRldmVudClcIiAobW91c2VlbnRlcik9XCJzZXRUb29sVGlwKCRldmVudCxpdGVtKVwiXG4gICAgICAgICAgICAgICAgZGF0YS1wbGFjZW1lbnQ9XCJyaWdodFwiIChkYmxjbGljayk9XCJkb3VibGVDbGlja2VkKCRldmVudClcIiBjbGFzcz1cImxpc3QtZ3JvdXAtaXRlbVwiXG4gICAgICAgICAgICAgICAgW25nQ2xhc3NdPVwie2FjdGl2ZTpjb2xvcmVkSW5kZXhbaV09PXRydWUsaG92ZXI6Y29sb3JlZEluZGV4IT1pLG11bHRpcGxlU2VsZWN0TmF2Om11bHRpcGxlU2VsZWN0TmF2W2ldPT10cnVlfVwiXG4gICAgICAgICAgICAgICAgKm5nRm9yPVwibGV0IGl0ZW0gb2YgZGF0YVByb3ZpZGVyOyBsZXQgaSA9IGluZGV4XCI+XG4gICAgICAgICAgICAgICAgPHAgID57eyBpdGVtLmNvbnRlbnQgfX08L3A+XG4gICAgICAgICAgICAgICAgPGltZyAqbmdJZj1cIml0ZW0uaW1nXCIgc3JjPVwie3sgaXRlbS5pbWcgfX1cIiAgc3R5bGU9XCJoZWlnaHQ6MjBweDt3aWR0aDoyMHB4XCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3M9XCJpY29uLWl0ZW1cIj5cbiAgICAgICAgICAgIDwvbGk+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2ICpuZ0lmPVwiIWVuYWJsZWQgfHwgIXNlbGVjdGFibGVcIj5cbiAgICAgICAgICAgIDxsaSAqbmdGb3I9XCJsZXQgaXRlbSBvZiBkYXRhUHJvdmlkZXI7IGxldCBpID0gaW5kZXhcIiBjbGFzcz1cImxpc3QtZ3JvdXAtaXRlbS1kaXNhYmxlZFwiIChtb3VzZWVudGVyKT1cInNldFRvb2xUaXAoJGV2ZW50LGl0ZW0pXCJcbiAgICAgICAgICAgICAgICAgZGF0YS1wbGFjZW1lbnQ9XCJyaWdodFwiPlxuICAgICAgICAgICAgICAgIDxwPnt7IGl0ZW0uY29udGVudCB9fTwvcD4gXG4gICAgICAgICAgICAgICAgPGltZyAqbmdJZj1cIml0ZW0uaW1nXCIgc3JjPVwie3sgaXRlbS5pbWcgfX1cIiBzdHlsZT1cImhlaWdodDoyMHB4O3dpZHRoOjIwcHhcIiBjbGFzcz1cIml0ZW0taW1nXCI+XG4gICAgICAgICAgICA8L2xpPlxuICAgICAgICA8L2Rpdj5cbiAgICA8L3VsPlxuPC9kaXY+XG5cbiAgYCxcbiAgICBzdHlsZXM6IFtgXG4gICAgLmljb24taXRlbXtcbiAgICAgICAgZmxvYXQ6cmlnaHQ7IFxuICAgICAgICBtYXJnaW4tcmlnaHQ6MTBweFxuICAgIH1cbiAgICAuaXRlbS1pbWd7XG4gICAgICAgIGZsb2F0OnJpZ2h0OyBcbiAgICAgICAgbWFyZ2luLXJpZ2h0OjEwcHhcbiAgICB9XG4gICAgLmxpc3QtZ3JvdXAtaXRlbS1kaXNhYmxlZHtcbiAgICAgICAgaGVpZ2h0OiAyNXB4O1xuICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgIGZvbnQtZmFtaWx5OiB2ZXJkYW5hLGhlbHZldGljYTtcbiAgICAgICAgcGFkZGluZzogMHB4IDBweCAwcHggMTBweDtcbiAgICAgICAgbWFyZ2luOiAwcHg7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4O1xuICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyBcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgb3ZlcmZsb3c6aGlkZGVuXG4gICAgfVxuICAgIC5saXN0LWdyb3VwLWl0ZW0uYWN0aXZlLCAubGlzdC1ncm91cC1pdGVtLmFjdGl2ZTpmb2N1cywgLmxpc3QtZ3JvdXAtaXRlbS5hY3RpdmU6aG92ZXIge1xuICAgICAgICBjb2xvcjogYmxhY2s7ICBcbiAgICB9IFxuICAgIC5saXN0LWdyb3VwLWl0ZW0uYWN0aXZle1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjN0ZDRUZGO1xuICAgIH1cbiAgICAubGlzdC1ncm91cC1pdGVtLm11bHRpcGxlU2VsZWN0TmF2e1xuICAgICAgICBib3JkZXItY29sb3I6ICM3RkNFRkY7XG4gICAgICAgIGJvcmRlci13aWR0aDogdGhpbjtcbiAgICAgICAgYm9yZGVyLXN0eWxlOnNvbGlkO1xuICAgIH1cbiAgICAubGlzdC1ncm91cC1pdGVtOmZvY3VzIHtcbiAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJlZDsgXG4gICAgfVxuICAgIC5saXN0LWdyb3Vwe1xuICAgICAgICBtYXJnaW4tYm90dG9tOjBweCAhaW1wb3J0YW50O1xuICAgIH1cbiAgICAubGlzdC1ncm91cC1pdGVtOmhvdmVyIHtcbiAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjQjJFMUZGO1xuICAgICAgIGN1cnNvcjogZGVmYXVsdDtcbiAgICB9XG4gICAgLmxpc3QtZ3JvdXAtaXRlbSB7XG4gICAgICAgIGhlaWdodDogMjVweDtcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICBmb250LWZhbWlseTogdmVyZGFuYSxoZWx2ZXRpY2E7XG4gICAgICAgIHBhZGRpbmc6IDBweCAwcHggMHB4IDEwcHg7XG4gICAgICAgIG1hcmdpbjogMHB4O1xuICAgICAgICBsaW5lLWhlaWdodDogMjVweDtcbiAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsgXG4gICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgIG92ZXJmbG93OmhpZGRlblxuICAgIH1cbiAgICBcbiAgICAuYm94Ym94e1xuICAgICAgICBoZWlnaHQ6MTAwJTtcbiAgICAgICAgd2lkdGg6MTAwJTtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RkZDsgXG4gICAgICAgIG92ZXJmbG93OiBhdXRvO1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcbiAgICB9XG4gICAgLmJveGJveDpob3ZlcntcbiAgICAgICAgY3Vyc29yOiBkZWZhdWx0O1xuICAgIH1cbiAgICAuZGlzYWJse1xuICAgICAgICB6LWluZGV4OiAxO1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGRkO1xuICAgICAgICBvcGFjaXR5OiAwLjM7XG4gICAgfVxuYF1cbn0pXG5leHBvcnQgY2xhc3MgU3d0TGlzdCBleHRlbmRzIENvbnRhaW5lciBpbXBsZW1lbnRzIE9uSW5pdCB7XG4gICAgcHJpdmF0ZSBfc2VsZWN0ZWRJbmRpY2VzOiBhbnkgPSBbXTtcbiAgICBwcml2YXRlIF9zZWxlY3RlZEluZGV4OiBhbnk7XG4gICAgcHJpdmF0ZSBfc2VsZWN0YWJsZTogYW55ID0gdHJ1ZTtcbiAgICBwcml2YXRlIF9oZWlnaHQ6IHN0cmluZztcbiAgICBwcml2YXRlIF9zZWxlY3RlZEl0ZW1zID0gW107XG4gICAgcHJpdmF0ZSBfc2VsZWN0ZWRJdGVtO1xuICAgIHByaXZhdGUgU3d0TGlzdE9iamVjdDogYW55O1xuICAgIHByaXZhdGUgX3dpZHRoOiBzdHJpbmc7XG4gICAgcHJpdmF0ZSBfZW5hYmxlZDogYm9vbGVhbiA9IHRydWVcbiAgICBwcml2YXRlIF9hbGxvd011bHRpcGxlU2VsZWN0aW9uID0gZmFsc2U7XG4gICAgcHJpdmF0ZSBfZGF0YVByb3ZpZGVyOiBhbnkgPSBbXVxuICAgIHByaXZhdGUgZGF0YVByb3ZpZGVyT2xkID0gbmV3IEFycmF5KCk7XG4gICAgcHVibGljIGNvbG9yZWRJbmRleCA9IFtmYWxzZV1cbiAgICBwdWJsaWMgbXVsdGlwbGVTZWxlY3ROYXYgPSBbZmFsc2VdXG4gICAgcHJpdmF0ZSBmb2N1c2VkSW5kZXggPSAwXG4gICAgcHJpdmF0ZSBpbmRleCA9IDBcbiAgICBwcml2YXRlIGZpcnN0U2hvd25cbiAgICBjb25zdHJ1Y3Rvcihwcml2YXRlIGVsZW06IEVsZW1lbnRSZWYsIHByaXZhdGUgY29tbW9uU2VydmljZTogQ29tbW9uU2VydmljZSwgcHJpdmF0ZSBfcmVuZGVyZXI6IFJlbmRlcmVyMikge1xuICAgICAgICBzdXBlcihlbGVtLCBjb21tb25TZXJ2aWNlKTtcbiAgICB9XG4gICAgbmdPbkluaXQoKSB7XG4gICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyT2xkID0gW11cbiAgICAgICAgdGhpcy5vcmlnaW5hbFZhbHVlID0gW11cbiAgICAgICAgdGhpcy5maXJzdFNob3duID0gMFxuICAgICAgICBpZiAodGhpcy5lbmFibGVkID09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5lbmFibGVkID0gdHJ1ZVxuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnNlbGVjdGFibGUgPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdGFibGUgPSB0cnVlXG4gICAgICAgIH1cbiAgICAgICAgaWYgKCF0aGlzLmRhdGFQcm92aWRlcikge1xuICAgICAgICAgICAgdGhpcy5kYXRhUHJvdmlkZXIgPSBbXVxuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmVuYWJsZWQgPT09IHRydWUpIHtcbiAgICAgICAgICAgIGZvciAobGV0IGluZGV4ID0gMTsgaW5kZXggPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleFtpbmRleF0gPSBmYWxzZVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIC8qIHNldCBjaGFuZ2UgaGFuZGxlciAqL1xuICAgIEBPdXRwdXQoJ2l0ZW1DbGljaycpIHByaXZhdGUgY2xpY2tfID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XG4vLyAgICBAT3V0cHV0KCdkb3VibGVDbGljaycpIHByaXZhdGUgZG91YmxlQ2xpY2tfID0gbmV3IEV2ZW50RW1pdHRlcjxhbnk+KCk7XG4gICAgQElucHV0KCd0b29sVGlwJykgdG9vbFRpcDogc3RyaW5nID0gXCJcIjtcbiAgICBASW5wdXQoJ3Nob3dEYXRhVGlwcycpIHNob3dEYXRhVGlwOiBib29sZWFuID0gZmFsc2U7XG4gICAgQElucHV0KCdkb3VibGVDbGlja0VuYWJsZWQnKSBkb3VibGVDbGlja0VuYWJsZWQ6IGJvb2xlYW4gPSB0cnVlO1xuICAgIC8qIEBJbnB1dHMgKi9cbiAgICBASW5wdXQoKVxuICAgIHNldCBhbGxvd011bHRpcGxlU2VsZWN0aW9uKHZhbHVlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgaWYgKHZhbHVlID09PSBcInRydWVcIikge1xuICAgICAgICAgICAgICAgIHRoaXMuX2FsbG93TXVsdGlwbGVTZWxlY3Rpb24gPSB0cnVlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9hbGxvd011bHRpcGxlU2VsZWN0aW9uID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl9hbGxvd011bHRpcGxlU2VsZWN0aW9uID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0IGFsbG93TXVsdGlwbGVTZWxlY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9hbGxvd011bHRpcGxlU2VsZWN0aW9uO1xuICAgIH1cbiAgICBmaXJzdENhbGw6IGJvb2xlYW4gPSB0cnVlO1xuICAgIEBJbnB1dCgpXG4gICAgQElucHV0KFwiaWRcIikgaWQ6IHN0cmluZyA9IFwiXCI7XG4gICAgc2V0IGRhdGFQcm92aWRlcih2YWx1ZSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgdGhpcy5fZGF0YVByb3ZpZGVyID0gW11cbiAgICAgICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgdmFsdWUgPSBbdmFsdWVdXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKCF2YWx1ZSkgfHwgKCF2YWx1ZVswXSkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9kYXRhUHJvdmlkZXIgPSBbXVxuICAgICAgICAgICAgICAgIGlmICh0aGlzLmZpcnN0Q2FsbCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWUgPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5maXJzdENhbGwgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh2YWx1ZVswXS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICB2YXIgdGVtcCA9IHsgXCJ0eXBlXCI6IFwiXCIsIFwidmFsdWVcIjogXCJcIiwgXCJzZWxlY3RlZFwiOiAwLCBcImNvbnRlbnRcIjogXCJcIiB9XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHZhbHVlLmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICB0ZW1wLnZhbHVlID0gdmFsdWVbaW5kZXhdXG4gICAgICAgICAgICAgICAgICAgIHRlbXAuY29udGVudCA9IHZhbHVlW2luZGV4XVxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9kYXRhUHJvdmlkZXJbaW5kZXhdID0geyAuLi50ZW1wIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZmlyc3RDYWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9yaWdpbmFsVmFsdWVbaW5kZXhdID0geyAuLi50ZW1wIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA9PSB2YWx1ZS5sZW5ndGggLSAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5maXJzdENhbGwgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuX2RhdGFQcm92aWRlciA9IFsuLi52YWx1ZV07XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZmlyc3RDYWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMub3JpZ2luYWxWYWx1ZSA9IFsuLi52YWx1ZV07XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZmlyc3RDYWxsID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5vbkNoYW5nZShldmVudClcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGdldCBkYXRhUHJvdmlkZXIoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9kYXRhUHJvdmlkZXI7XG4gICAgfVxuICAgIHNldFRvb2xUaXAoZXZlbnQsIGl0ZW0pIHtcbiAgICAgICAgdGhpcy50b29sVGlwID0gdGhpcy5zaG93RGF0YVRpcCAmJiBldmVudC50YXJnZXQuc2Nyb2xsV2lkdGggPiBldmVudC50YXJnZXQub2Zmc2V0V2lkdGggPyBpdGVtLmNvbnRlbnQgOiAnJ1xuICAgIH1cbiAgICBASW5wdXQoKVxuICAgIHNldCBzZWxlY3RlZEluZGV4KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuY29sb3JlZEluZGV4LmZpbGwoZmFsc2UpXG4gICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3ROYXYuZmlsbChmYWxzZSlcbiAgICAgICAgaWYgKHZhbHVlICE9IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPSBudWxsICYmIHZhbHVlICE9IC0xKSB7XG4gICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleFt2YWx1ZV0gPSB0cnVlXG4gICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0TmF2W3ZhbHVlXSA9IHRydWVcbiAgICAgICAgICAgIHRoaXMuZm9jdXNlZEluZGV4ID0gdmFsdWVcbiAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kZXggPSB2YWx1ZVxuICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtID0gdGhpcy5kYXRhUHJvdmlkZXJbdmFsdWVdLnZhbHVlXG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdGVkSXRlbSA9IG51bGxcbiAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kZXggPSAtMVxuICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGljZXMgPSBbXVxuICAgICAgICB9XG4gICAgfVxuICAgIGdldCBzZWxlY3RlZEluZGV4KCkge1xuICAgICAgICBsZXQgZm91bmQgPSBmYWxzZSwgaSA9IDBcbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbmRpY2VzLmxlbmd0aCA8PSAxKSB7XG4gICAgICAgICAgICB3aGlsZSAoaSA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aCAmJiAhZm91bmQpIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5jb2xvcmVkSW5kZXhbaV0gPT0gdHJ1ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEluZGV4ID0gaVxuICAgICAgICAgICAgICAgICAgICBmb3VuZCA9IHRydWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaSsrXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWZvdW5kKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRleCA9IC0xXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuX3NlbGVjdGVkSW5kZXg7XG4gICAgfVxuICAgIEBJbnB1dCgpXG4gICAgc2V0IHNlbGVjdGVkSW5kaWNlcyh2YWx1ZSkge1xuICAgICAgICB0aGlzLmNvbG9yZWRJbmRleC5maWxsKGZhbHNlKVxuICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdmFsdWUubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleFt2YWx1ZVtpbmRleF1dID0gdHJ1ZVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuX3NlbGVjdGVkSW5kaWNlcyA9IFsuLi52YWx1ZV1cbiAgICB9XG4gICAgZ2V0IHNlbGVjdGVkSW5kaWNlcygpIHtcbiAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRpY2VzID0gW11cbiAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgaWYgKHRoaXMuY29sb3JlZEluZGV4W2luZGV4XSA9PSB0cnVlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJbmRpY2VzLnB1c2goaW5kZXgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuX3NlbGVjdGVkSW5kaWNlcztcbiAgICB9XG4gICAgLyogd2lkdGggZ2V0dGVyIGFuZCBzZXR0ZXIgKi9cbiAgICBASW5wdXQoKVxuICAgIHNldCB3aWR0aCh2YWx1ZTogc3RyaW5nKSB7XG4gICAgICAgIGlmICh2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBpZiAodmFsdWUuaW5kZXhPZignJScpID09PSAtMSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX3dpZHRoID0gdmFsdWUgKyBcInB4XCI7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuX3dpZHRoID0gdmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fcmVuZGVyZXIuc2V0U3R5bGUodGhpcy5lbGVtLm5hdGl2ZUVsZW1lbnQsICd3aWR0aCcsIHRoaXMuX3dpZHRoKVxuICAgIH1cbiAgICBnZXQgd2lkdGgoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl93aWR0aDtcbiAgICB9XG4gICAgLyogQElucHV0cyAqL1xuICAgIC8qIGhlaWdodCBnZXR0ZXIgYW5kIHNldHRlciAqL1xuICAgIEBJbnB1dCgpXG4gICAgc2V0IGhlaWdodCh2YWx1ZTogc3RyaW5nKSB7XG4gICAgICAgIGlmICh2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBpZiAodmFsdWUuaW5kZXhPZignJScpID09PSAtMSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX2hlaWdodCA9IHZhbHVlICsgXCJweFwiO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9oZWlnaHQgPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9yZW5kZXJlci5zZXRTdHlsZSh0aGlzLmVsZW0ubmF0aXZlRWxlbWVudCwgJ2hlaWdodCcsIHRoaXMuX2hlaWdodClcbiAgICB9XG4gICAgZ2V0IGhlaWdodCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2hlaWdodDtcbiAgICB9XG4gICAgQFZpZXdDaGlsZChcInN3dGxpc3RJdGVtc1wiKSBzd3RsaXN0SXRlbXM6IEVsZW1lbnRSZWY7XG4gICAgQFZpZXdDaGlsZChcImxpc3RcIikgbGlzdDogRWxlbWVudFJlZjtcbiAgICBvbkNoYW5nZShldmVudCkge1xuICAgICAgICBpZiAodGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoID09IHRoaXMuZGF0YVByb3ZpZGVyT2xkLmxlbmd0aCkge1xuICAgICAgICAgICAgbGV0IGNoYW5nZTogYm9vbGVhbiA9ICEoSlNPTi5zdHJpbmdpZnkodGhpcy5kYXRhUHJvdmlkZXIpID09PSBKU09OLnN0cmluZ2lmeSh0aGlzLmRhdGFQcm92aWRlck9sZCkpO1xuICAgICAgICAgICAgaWYgKGNoYW5nZSkge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kYXRhUHJvdmlkZXJPbGRbaW5kZXhdID0gdGhpcy5kYXRhUHJvdmlkZXJbaW5kZXhdXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlXy5lbWl0KHRoaXMuZGF0YVByb3ZpZGVyKVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5jaGFuZ2VfLmVtaXQodGhpcy5kYXRhUHJvdmlkZXIpXG4gICAgICAgICAgICBpZiAodGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoID09IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRhdGFQcm92aWRlck9sZCA9IFtdXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyT2xkW2luZGV4XSA9IHRoaXMuZGF0YVByb3ZpZGVyW2luZGV4XVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnNweUNoYW5nZXModGhpcy5kYXRhUHJvdmlkZXIpO1xuICAgIH1cbiAgICAvLyBmdW5jdGlvbiB1c2VkIHRvIGhhbmRsZSBrZXlib3JkcyBrZXlzIGV2ZW50c1xuICAgIHRlc3RrZXkoZXZlbnQpIHtcbiAgICAgICAgaWYgKGV2ZW50LmtleUNvZGUgPT09IDM4KSB7XG4gICAgICAgICAgICBpZiAoIWV2ZW50LmN0cmxLZXkpIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5mb2N1c2VkSW5kZXggIT0gdGhpcy5zZWxlY3RlZEluZGV4KSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IHRoaXMuZm9jdXNlZEluZGV4XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxUb1RvcCgpO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkSW5kZXggIT0gMCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXgtLTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChldmVudC5rZXlDb2RlID09PSAzOCAmJiBldmVudC5jdHJsS2V5ICYmIHRoaXMuYWxsb3dNdWx0aXBsZVNlbGVjdGlvbikge1xuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxGb2N1c1RvVG9wKCk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9jdXNlZEluZGV4ICE9IDApIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdE5hdltpbmRleF0gPSBmYWxzZVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3ROYXZbdGhpcy5mb2N1c2VkSW5kZXggLSAxXSA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb2N1c2VkSW5kZXggPSB0aGlzLmZvY3VzZWRJbmRleCAtIDFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoZXZlbnQua2V5Q29kZSA9PT0gNDApIHtcbiAgICAgICAgICAgIGlmICghZXZlbnQuY3RybEtleSkge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvY3VzZWRJbmRleCAhPSB0aGlzLnNlbGVjdGVkSW5kZXgpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gdGhpcy5mb2N1c2VkSW5kZXhcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnNjcm9sbFRvQm90dG9tKCk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4Kys7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnQua2V5Q29kZSA9PT0gNDAgJiYgZXZlbnQuY3RybEtleSAmJiB0aGlzLmFsbG93TXVsdGlwbGVTZWxlY3Rpb24pIHtcbiAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIHRoaXMuc2Nyb2xsRm9jdXNUb0JvdHRvbSgpO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvY3VzZWRJbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuY29sb3JlZEluZGV4Lmxlbmd0aDsgaW5kZXgrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdE5hdltpbmRleF0gPSBmYWxzZVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3ROYXZbdGhpcy5mb2N1c2VkSW5kZXggKyAxXSA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb2N1c2VkSW5kZXggPSB0aGlzLmZvY3VzZWRJbmRleCArIDFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKChldmVudC5jdHJsS2V5ICYmIGV2ZW50LmtleUNvZGUgPT0gMzIpICYmIHRoaXMuYWxsb3dNdWx0aXBsZVNlbGVjdGlvbiA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgdGhpcy5jb2xvcmVkSW5kZXhbdGhpcy5mb2N1c2VkSW5kZXhdID0gIXRoaXMuY29sb3JlZEluZGV4W3RoaXMuZm9jdXNlZEluZGV4XVxuICAgICAgICAgICAgaWYgKHRoaXMuY29sb3JlZEluZGV4W3RoaXMuZm9jdXNlZEluZGV4XSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRpY2VzLnB1c2godGhpcy5mb2N1c2VkSW5kZXgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gKiBUaGlzIG1ldGhvZCBpcyB1c2VkIHRvIGhhbmRsZSBzY3JvbGxpbmdcbiAqIHRvIGJvdHRvbS5cbiAqL1xuICAgIHByaXZhdGUgc2Nyb2xsVG9Cb3R0b20oKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBtYWtlIHJlZmVyZW5jZSB0byBsaXN0IGl0ZW1zLlxuICAgICAgICAgICAgbGV0IGxpc3QgPSB0aGlzLmxpc3QubmF0aXZlRWxlbWVudDtcbiAgICAgICAgICAgIC8vIGdldCBsaXN0IGl0ZW1zIGhlaWdodC5cbiAgICAgICAgICAgIGxldCBsaXN0SGVpZ2h0ID0gbGlzdC5vZmZzZXRIZWlnaHQ7XG4gICAgICAgICAgICAvLyBnZXQgaXRlbSBpbmRleC5cbiAgICAgICAgICAgIGxldCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdGVkSW5kZXggKyAxO1xuICAgICAgICAgICAgLy8gaWYgaXRlbSBpbmRleCA+IC0xIGRvLlxuICAgICAgICAgICAgaWYgKGl0ZW1JbmRleCA+IC0xKSB7XG4gICAgICAgICAgICAgICAgLy8gZ2V0IGN1cnJlbnQgaXRlbS5cbiAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IGxpc3QuY2hpbGRyZW5bMF0uY2hpbGRyZW5bMF0uY2hpbGRyZW5baXRlbUluZGV4XVxuICAgICAgICAgICAgICAgIC8vIGNoZWNrIGV4aXN0ZW5jZSBvZiBpdGVtLlxuICAgICAgICAgICAgICAgIGlmIChpdGVtICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gZ2V0IGl0ZW0gaGVpZ2h0LlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUhlaWdodCA9IGl0ZW0ub2Zmc2V0SGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgaXRlbSB0b3AgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtVG9wID0gaXRlbUluZGV4ICogaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gYm90dG9tIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUJvdHRvbSA9IGl0ZW1Ub3AgKyBpdGVtSGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgdGhlIHNjcm9sbCB2YWx1ZS5cbiAgICAgICAgICAgICAgICAgICAgbGV0IHZpZXdCb3R0b20gPSBsaXN0LnNjcm9sbFRvcCArIGxpc3RIZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIHVwZGF0ZSBzY3JvbGwgYmFyIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbUJvdHRvbSA+IHZpZXdCb3R0b20pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpc3Quc2Nyb2xsVG9wID0gaXRlbUJvdHRvbSAtIGxpc3RIZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKVxuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAqIFRoaXMgbWV0aG9kIGlzIHVzZWQgdG8gaGFuZGxlIHNjcm9sbGluZ1xuICAqIHRvIFRvcC5cbiAgKi9cbiAgICBwcml2YXRlIHNjcm9sbFRvVG9wKCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gbWFrZSByZWZlcmVuY2UgdG8gbGlzdCBpdGVtcy5cbiAgICAgICAgICAgIGxldCBsaXN0ID0gdGhpcy5saXN0Lm5hdGl2ZUVsZW1lbnQ7XG4gICAgICAgICAgICAvLyBnZXQgbGlzdCBpdGVtcyBoZWlnaHQuXG4gICAgICAgICAgICBsZXQgbGlzdEhlaWdodCA9IGxpc3Qub2Zmc2V0SGVpZ2h0O1xuICAgICAgICAgICAgLy8gZ2V0IGl0ZW0gaW5kZXguXG4gICAgICAgICAgICBsZXQgaXRlbUluZGV4ID0gdGhpcy5zZWxlY3RlZEluZGV4IC0gMTtcbiAgICAgICAgICAgIC8vIGlmIGl0ZW0gaW5kZXggPiAtMSBkby5cbiAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkge1xuICAgICAgICAgICAgICAgIC8vIGdldCBjdXJyZW50IGl0ZW0uXG4gICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSBsaXN0LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuW2l0ZW1JbmRleF1cbiAgICAgICAgICAgICAgICAvLyBjaGVjayBleGlzdGVuY2Ugb2YgaXRlbS5cbiAgICAgICAgICAgICAgICBpZiAoaXRlbSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGdldCBpdGVtIGhlaWdodC5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1IZWlnaHQgPSBpdGVtLm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gdG9wIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbVRvcCA9IGl0ZW1JbmRleCAqIGl0ZW1IZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSBpdGVtIGJvdHRvbSBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1Cb3R0b20gPSBpdGVtVG9wICsgaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gdXBkYXRlIHNjcm9sbCBiYXIgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtVG9wIDwgbGlzdC5zY3JvbGxUb3ApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpc3Quc2Nyb2xsVG9wID0gaXRlbVRvcDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLyogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBoYW5kbGUgc2Nyb2xsaW5nXG4gKiB0byBib3R0b20uXG4gKi9cbiAgICBwcml2YXRlIHNjcm9sbEZvY3VzVG9Cb3R0b20oKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBtYWtlIHJlZmVyZW5jZSB0byBsaXN0IGl0ZW1zLlxuICAgICAgICAgICAgbGV0IGxpc3QgPSB0aGlzLmxpc3QubmF0aXZlRWxlbWVudDtcbiAgICAgICAgICAgIC8vIGdldCBsaXN0IGl0ZW1zIGhlaWdodC5cbiAgICAgICAgICAgIGxldCBsaXN0SGVpZ2h0ID0gbGlzdC5vZmZzZXRIZWlnaHQ7XG4gICAgICAgICAgICAvLyBnZXQgaXRlbSBpbmRleC5cbiAgICAgICAgICAgIGxldCBpdGVtSW5kZXggPSB0aGlzLmZvY3VzZWRJbmRleCArIDE7XG4gICAgICAgICAgICAvLyBpZiBpdGVtIGluZGV4ID4gLTEgZG8uXG4gICAgICAgICAgICBpZiAoaXRlbUluZGV4ID4gLTEpIHtcbiAgICAgICAgICAgICAgICAvLyBnZXQgY3VycmVudCBpdGVtLlxuICAgICAgICAgICAgICAgIGxldCBpdGVtID0gbGlzdC5jaGlsZHJlblswXS5jaGlsZHJlblswXS5jaGlsZHJlbltpdGVtSW5kZXhdXG4gICAgICAgICAgICAgICAgLy8gY2hlY2sgZXhpc3RlbmNlIG9mIGl0ZW0uXG4gICAgICAgICAgICAgICAgaWYgKGl0ZW0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBnZXQgaXRlbSBoZWlnaHQuXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtSGVpZ2h0ID0gaXRlbS5vZmZzZXRIZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSBpdGVtIHRvcCBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1Ub3AgPSBpdGVtSW5kZXggKiBpdGVtSGVpZ2h0O1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxjdWxhdGUgaXRlbSBib3R0b20gcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtQm90dG9tID0gaXRlbVRvcCArIGl0ZW1IZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSB0aGUgc2Nyb2xsIHZhbHVlLlxuICAgICAgICAgICAgICAgICAgICBsZXQgdmlld0JvdHRvbSA9IGxpc3Quc2Nyb2xsVG9wICsgbGlzdEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gdXBkYXRlIHNjcm9sbCBiYXIgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtQm90dG9tID4gdmlld0JvdHRvbSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGlzdC5zY3JvbGxUb3AgPSBpdGVtQm90dG9tIC0gbGlzdEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICogVGhpcyBtZXRob2QgaXMgdXNlZCB0byBoYW5kbGUgc2Nyb2xsaW5nXG4gICogdG8gVG9wLlxuICAqL1xuICAgIHByaXZhdGUgc2Nyb2xsRm9jdXNUb1RvcCgpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIG1ha2UgcmVmZXJlbmNlIHRvIGxpc3QgaXRlbXMuXG4gICAgICAgICAgICBsZXQgbGlzdCA9IHRoaXMubGlzdC5uYXRpdmVFbGVtZW50O1xuICAgICAgICAgICAgLy8gZ2V0IGxpc3QgaXRlbXMgaGVpZ2h0LlxuICAgICAgICAgICAgbGV0IGxpc3RIZWlnaHQgPSBsaXN0Lm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgIC8vIGdldCBpdGVtIGluZGV4LlxuICAgICAgICAgICAgbGV0IGl0ZW1JbmRleCA9IHRoaXMuZm9jdXNlZEluZGV4IC0gMTtcbiAgICAgICAgICAgIC8vIGlmIGl0ZW0gaW5kZXggPiAtMSBkby5cbiAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkge1xuICAgICAgICAgICAgICAgIC8vIGdldCBjdXJyZW50IGl0ZW0uXG4gICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSBsaXN0LmNoaWxkcmVuWzBdLmNoaWxkcmVuWzBdLmNoaWxkcmVuW2l0ZW1JbmRleF1cbiAgICAgICAgICAgICAgICAvLyBjaGVjayBleGlzdGVuY2Ugb2YgaXRlbS5cbiAgICAgICAgICAgICAgICBpZiAoaXRlbSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGdldCBpdGVtIGhlaWdodC5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1IZWlnaHQgPSBpdGVtLm9mZnNldEhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FsY3VsYXRlIGl0ZW0gdG9wIHBvc2l0aW9uLlxuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbVRvcCA9IGl0ZW1JbmRleCAqIGl0ZW1IZWlnaHQ7XG4gICAgICAgICAgICAgICAgICAgIC8vIGNhbGN1bGF0ZSBpdGVtIGJvdHRvbSBwb3NpdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1Cb3R0b20gPSBpdGVtVG9wICsgaXRlbUhlaWdodDtcbiAgICAgICAgICAgICAgICAgICAgLy8gdXBkYXRlIHNjcm9sbCBiYXIgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtVG9wIDwgbGlzdC5zY3JvbGxUb3ApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpc3Quc2Nyb2xsVG9wID0gaXRlbVRvcDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpXG4gICAgICAgIH1cbiAgICB9XG4gICAgYWRkSXRlbSh2YWx1ZSkge1xuICAgICAgICB0aGlzLmRhdGFQcm92aWRlci5wdXNoKHZhbHVlKVxuICAgICAgICB0aGlzLm9uQ2hhbmdlKGV2ZW50KVxuICAgIH1cbiAgICAvLyByZW1vdmUgc2luZ2xlIGl0ZW0gdXNpbmcgaXQncyBvd24gaW5kZXhcbiAgICByZW1vdmVJdGVtKGluZGV4KSB7XG4gICAgICAgIHRoaXMuZGF0YVByb3ZpZGVyLnNwbGljZShpbmRleCwgMSlcbiAgICAgICAgaWYgKHRoaXMuZGF0YVByb3ZpZGVyLmxlbmd0aCA+IDAgJiYgaW5kZXggPCB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGgpIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9IGluZGV4XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaW5kZXggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSBpbmRleCAtIDFcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpbmRleCA9PSAwKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSAtMVxuICAgICAgICB9XG4gICAgICAgIHRoaXMub25DaGFuZ2UoZXZlbnQpXG4gICAgfVxuICAgIC8vdGhpcyBtZXRob2QgaXMgdXNlZCB0byByZW1vdmUgbXVsdGlwbGUgaXRlbXMgYXQgb25jZSBcbiAgICAvLyBAcGFyYW06IGluZGV4ZXM6IGFycmF5IG9mIGluZGV4ZXNcbiAgICByZW1vdmVJdGVtcyhpbmRleGVzKSB7XG4gICAgICAgIGxldCB4ID0gdGhpcy5kYXRhUHJvdmlkZXJcbiAgICAgICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IGluZGV4ZXMubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICBkZWxldGUgeFtpbmRleGVzW2luZGV4XV1cbiAgICAgICAgfVxuICAgICAgICB2YXIgZmlsdGVyZWQgPSB4LmZpbHRlcihmdW5jdGlvbiAodmFsdWUsIGluZGV4LCBhcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZSAhPSB1bmRlZmluZWQ7XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmRhdGFQcm92aWRlciA9IGZpbHRlcmVkXG4gICAgICAgIHRoaXMuc2VsZWN0ZWRJbmRleCA9ICBpbmRleGVzWzBdIC0gMSBcbiAgICB9XG4gICAgcmVtb3ZlQWxsKCkge1xuICAgICAgICB0aGlzLmRhdGFQcm92aWRlciA9IFtdXG4gICAgICAgIHRoaXMub25DaGFuZ2UoZXZlbnQpXG4gICAgfVxuICAgIC8vIGZ1bmN0aW9uIHVzZWQgdG8gZG8gbXVsdGlwbGUgc2VsZWN0aW9uIHdpdGggY3RybCAmIGNsaWNrXG4gICAgY2xpY2tIYW5kbGVyKGV2ZW50LCBpKSB7XG4gICAgICAgIGlmICh0aGlzLmVuYWJsZWQgPT09IHRydWUpIHtcbiAgICAgICAgICAgIGlmIChldmVudC5jdHJsS2V5ICYmIHRoaXMuYWxsb3dNdWx0aXBsZVNlbGVjdGlvbiA9PSB0cnVlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtcyA9IG5ldyBBcnJheSgpO1xuICAgICAgICAgICAgICAgIHRoaXMuY29sb3JlZEluZGV4W2ldID0gIXRoaXMuY29sb3JlZEluZGV4W2ldXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChldmVudC5zaGlmdEtleSAmJiB0aGlzLmFsbG93TXVsdGlwbGVTZWxlY3Rpb24gPT0gdHJ1ZSAmJiB0aGlzLnNlbGVjdGVkSW5kZXggIT0gLTEpIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEluZGV4IDwgaSkge1xuICAgICAgICAgICAgICAgICAgICBsZXQgdGVtcCA9IFtdXG4gICAgICAgICAgICAgICAgICAgIGxldCBsYXN0U2VsZWN0ZWQgPSB0aGlzLnNlbGVjdGVkSW5kZXhcbiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSBsYXN0U2VsZWN0ZWQ7IGluZGV4IDw9IGk7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRlbXAucHVzaChpbmRleClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleC5maWxsKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kaWNlcyA9IHRlbXBcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBsZXQgdGVtcCA9IFtdXG4gICAgICAgICAgICAgICAgICAgIGxldCBsYXN0U2VsZWN0ZWQgPSB0aGlzLnNlbGVjdGVkSW5kZXhcbiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaW5kZXggPSBpOyBpbmRleCA8PSBsYXN0U2VsZWN0ZWQ7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRlbXAucHVzaChpbmRleClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbG9yZWRJbmRleC5maWxsKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kaWNlcyA9IHRlbXBcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoOyBpbmRleCsrKSB7XG4gICAgICAgICAgICAgICAgLy8gICAgIHRoaXMuY29sb3JlZEluZGV4W2luZGV4XSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIC8vIH1cbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RlZEl0ZW1zID0gbmV3IEFycmF5KCk7XG4gICAgICAgICAgICAgICAgdGhpcy5jb2xvcmVkSW5kZXguZmlsbChmYWxzZSwgMCwgdGhpcy5jb2xvcmVkSW5kZXgubGVuZ3RoIC0gMSlcbiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSBpO1xuICAgICAgICAgICAgICAgIHRoaXMuaW5kZXggPSBpXG4gICAgICAgICAgICAgICAgdGhpcy5jb2xvcmVkSW5kZXhbaV0gPSB0cnVlXG4gICAgICAgICAgICB9XG5cbiAgICAgICAgfVxuICAgIH1cbiAgICBzZXQgc2VsZWN0ZWRJdGVtcyh2YWx1ZSkge1xuICAgIH1cbiAgICBnZXQgc2VsZWN0ZWRJdGVtcygpIHtcbiAgICAgICAgdGhpcy5fc2VsZWN0ZWRJdGVtcyA9IFtdXG4gICAgICAgIGlmICh0aGlzLmVuYWJsZWQgPT09IHRydWUpIHtcbiAgICAgICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLmNvbG9yZWRJbmRleC5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5jb2xvcmVkSW5kZXhbaW5kZXhdID09PSB0cnVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSXRlbXMucHVzaCh0aGlzLmRhdGFQcm92aWRlcltpbmRleF0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3NlbGVjdGVkSXRlbXM7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG4gICAgfVxuICAgIHNldCBzZWxlY3RlZEl0ZW0odmFsdWUpIHtcbiAgICAgICAgbGV0IGkgPSAwXG4gICAgICAgIGxldCBmb3VuZCA9IGZhbHNlXG4gICAgICAgIHdoaWxlIChpIDwgdGhpcy5kYXRhUHJvdmlkZXIubGVuZ3RoICYmICFmb3VuZCkge1xuICAgICAgICAgICAgaWYgKHRoaXMuZGF0YVByb3ZpZGVyW2ldLnZhbHVlID09IHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gaVxuICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSXRlbSA9IHRoaXMuZGF0YVByb3ZpZGVyW2ldLnZhbHVlXG4gICAgICAgICAgICAgICAgZm91bmQgPSB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpKytcbiAgICAgICAgfVxuICAgICAgICBpZiAoaSA9PSB0aGlzLmRhdGFQcm92aWRlci5sZW5ndGggLSAxICYmICFmb3VuZCkge1xuICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gLTFcbiAgICAgICAgICAgIHRoaXMuX3NlbGVjdGVkSXRlbSA9IG51bGxcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgc2VsZWN0ZWRJdGVtKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fc2VsZWN0ZWRJdGVtO1xuICAgIH1cbiAgICBASW5wdXQoKVxuICAgIHNldCBlbmFibGVkKHZhbHVlKSB7XG4gICAgICAgIGlmICh2YWx1ZS50b1N0cmluZygpID09IFwidHJ1ZVwiKSB7XG4gICAgICAgICAgICB0aGlzLl9lbmFibGVkID0gdHJ1ZVxuICAgICAgICB9IGVsc2VcbiAgICAgICAgICAgIGlmICh2YWx1ZS50b1N0cmluZygpID09IFwiZmFsc2VcIilcbiAgICAgICAgICAgICAgICB0aGlzLl9lbmFibGVkID0gZmFsc2VcbiAgICB9XG4gICAgZ2V0IGVuYWJsZWQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9lbmFibGVkO1xuICAgIH1cbiAgICBASW5wdXQoKVxuICAgIHNldCBzZWxlY3RhYmxlKHZhbHVlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgKHZhbHVlKSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgaWYgKHZhbHVlID09PSBcInRydWVcIikge1xuICAgICAgICAgICAgICAgIHRoaXMuX3NlbGVjdGFibGUgPSB0cnVlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zZWxlY3RhYmxlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl9zZWxlY3RhYmxlID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0IHNlbGVjdGFibGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zZWxlY3RhYmxlO1xuICAgIH1cbiAgICBzcHlDaGFuZ2VzKGV2ZW50OiBhbnkpOiB2b2lkIHtcbiAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KHRoaXMub3JpZ2luYWxWYWx1ZSkgPT0gSlNPTi5zdHJpbmdpZnkoZXZlbnQpKSB7XG4gICAgICAgICAgICB0aGlzLm9uU3B5Tm9DaGFuZ2UuZW1pdCh7IFwidGFyZ2V0XCI6IHRoaXMsIFwidmFsdWVcIjogZXZlbnQgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLm9uU3B5Q2hhbmdlLmVtaXQoeyBcInRhcmdldFwiOiB0aGlzLCBcInZhbHVlXCI6IGV2ZW50IH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICByZXNldE9yaWdpbmFsVmFsdWUoKSB7XG4gICAgICAgIHRoaXMub3JpZ2luYWxWYWx1ZSA9IFsuLi50aGlzLmRhdGFQcm92aWRlcl1cbiAgICAgICAgdGhpcy5zcHlDaGFuZ2VzKHRoaXMuZGF0YVByb3ZpZGVyKVxuICAgIH1cbiAgICBvbkNsaWNrKGV2ZW50KSB7XG4gICAgICAgIGZvY3VzTWFuYWdlci5mb2N1c1RhcmdldCA9IHRoaXMuaWQ7XG4gICAgICAgIHRoaXMuY2xpY2tfLmVtaXQoZXZlbnQpXG4gICAgfVxuICAgIGRvdWJsZUNsaWNrZWQoZXZlbnQpIHtcbiAgICAgICAgaWYgKHRoaXMuZG91YmxlQ2xpY2tFbmFibGVkKSB7XG4gICAgICAgICAgICB0aGlzLmRvdWJsZUNsaWNrXy5lbWl0KGV2ZW50KVxuICAgICAgICB9XG4gICAgfVxufVxuIl19