/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import { Container } from "../containers/swt-container.component";
import { CommonService } from "../utils/common.service";
import { Component, ElementRef, Input, ViewChild } from "@angular/core";
import { StringUtils } from '../utils/string-utils.service';
export class HBox extends Container {
    //-------constructor-----------------------------------------------------------//
    /**
     * @param {?} elem
     * @param {?} _commonService
     */
    constructor(elem, _commonService) {
        super(elem, _commonService);
        this.elem = elem;
        this._commonService = _commonService;
        this._wrapContent = false;
        $($(this.elem.nativeElement)[0]).attr('selector', 'HBox');
        this.horizontalGap = "8";
    }
    /**
     * @param {?} value
     * @return {?}
     */
    set wrapContent(value) {
        this._wrapContent = StringUtils.isTrue(value);
        if (this._wrapContent) {
            this.hboxDiv.nativeElement.classList.add("wrapContent");
        }
    }
    /**
     * @return {?}
     */
    get wrapContent() {
        return this._wrapContent;
    }
}
HBox.decorators = [
    { type: Component, args: [{
                selector: 'HBox',
                template: `
     <div fxLayout="row" #hboxDiv fxLayoutAlign="{{horizontalAlign}} {{verticalAlign}} "  fxLayoutGap="{{horizontalGap}}"  class="horizontalLayout" tabindex="-1">
        <ng-content></ng-content>
        <ng-container #_container></ng-container>
     </div>
  `,
                styles: [`
             :host {
               margin: 0px;
               display: block;
               outline: none;
             }
             .horizontalLayout {
               width: 100%;
               outline: none;
             }
             .wrapContent {
              flex-wrap:wrap;
             }
           
   `]
            }] }
];
/** @nocollapse */
HBox.ctorParameters = () => [
    { type: ElementRef },
    { type: CommonService }
];
HBox.propDecorators = {
    hboxDiv: [{ type: ViewChild, args: ['hboxDiv',] }],
    wrapContent: [{ type: Input }]
};
if (false) {
    /**
     * @type {?}
     * @protected
     */
    HBox.prototype.hboxDiv;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype._wrapContent;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype.elem;
    /**
     * @type {?}
     * @private
     */
    HBox.prototype._commonService;
}
//# sourceMappingURL=data:application/json;base64,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