/**
 * @fileoverview added by tsickle
 * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
import * as tslib_1 from "tslib";
import { AdvancedExportEvent } from './../events/swt-events.module';
import { Component, Input, ViewChild, ElementRef } from '@angular/core';
/** @type {?} */
var $ = require('jquery');
/** @type {?} */
var select2 = require('select2');
import 'jquery-ui-dist/jquery-ui';
import { SwtComboBox } from './swt-combobox.component';
import { ExportEvent } from '../events/swt-events.module';
import { CommonLogic } from '../logic/common-logic';
import { ExternalInterface } from '../utils/external-interface.service';
import { SwtModule } from './swt-module.component';
import { CommonService } from '../utils/common.service';
import { SwtAlert } from '../utils/swt-alert.service';
/** @type {?} */
var convert = require('xml-js');
var SwtDataExport = /** @class */ (function (_super) {
    tslib_1.__extends(SwtDataExport, _super);
    function SwtDataExport(commonService, element) {
        var _this = _super.call(this, element, commonService) || this;
        _this.commonService = commonService;
        _this.element = element;
        _this.dp = [
            { type: "", value: 'PDF', selected: 0, content: "PDF", iconImage: "assets/images/pdfUp.jpg" },
            { type: "", value: 'Excel', selected: 1, content: "Excel", iconImage: "assets/images/excelUp.jpg" },
            { type: "", value: 'CSV', selected: 0, content: "CSV", iconImage: "assets/images/csvUp.jpg" }
        ];
        _this.dpDisabled = [
            { type: "", value: 'PDF', selected: 0, content: "", iconImage: "assets/images/pdfDisabled.jpg" },
            { type: "", value: 'Excel', selected: 1, content: "", iconImage: "assets/images/excelDisabled.jpg" },
            { type: "", value: 'CSV', selected: 0, content: "", iconImage: "assets/images/csvDisabled.jpg" }
        ];
        _this.accountStartPattern = /<account clickable="false">/g;
        _this.accountStartWithoutAttributesPattern = /<account>/g;
        _this.nameStartPattern = /<name clickable="false">/g;
        _this.nameStartWithoutAttributesPattern = /<name>/g;
        _this.accountEndPattern = /<\/account>/g;
        _this.nameEndPattern = /<\/name>/g;
        _this.interfaceStartPattern = /<interface clickable="false">/g;
        _this.interfaceStartWithoutAttributesPattern = /<interface>/g;
        _this.interfaceEndPattern = /<\/interface>/g;
        // variable to hold the assumption start pattern
        _this.assumptionStartPattern = /<assumption>/g;
        // variable to hold the assumption end pattern  
        _this.assumptionEndPattern = /<\/assumption>/g;
        // private variable to handle enabled status.
        _this._enabled = true;
        _this.elementReference = element;
        _this.commonServiceRef = commonService;
        _this.SwtAlert = new SwtAlert(_this.commonService);
        return _this;
    }
    /**
     * @return {?}
     */
    SwtDataExport.prototype.ngAfterViewInit = /**
     * @return {?}
     */
    function () {
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtDataExport.prototype.onDataExportClick = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        /** @type {?} */
        var eventToSend = { type: this.typeFromIndex(this.exportDataComponent.selectedIndex), id: this.id };
        AdvancedExportEvent.emit(eventToSend);
    };
    /**
     * @param {?} event
     * @return {?}
     */
    SwtDataExport.prototype.onDataExportChange = /**
     * @param {?} event
     * @return {?}
     */
    function (event) {
        ExportEvent.emit(this.typeFromIndex(this.exportDataComponent.selectedIndex));
        /** @type {?} */
        var eventToSend = { type: this.typeFromIndex(this.exportDataComponent.selectedIndex), id: this.id };
        AdvancedExportEvent.emit(eventToSend);
    };
    Object.defineProperty(SwtDataExport.prototype, "enabled", {
        get: /**
         * @return {?}
         */
        function () {
            return this._enabled;
        },
        // enabled getter and setter 
        set: 
        // enabled getter and setter 
        /**
         * @param {?} value
         * @return {?}
         */
        function (value) {
            if (typeof (value) === "string") {
                if (value === "false") {
                    value = false;
                }
                else {
                    value = true;
                }
            }
            this.exportDataComponent.enabled = value;
            if (value == false) {
                this.exportDataComponent.dataProvider = this.dpDisabled;
            }
            else {
                this.exportDataComponent.dataProvider = this.dp;
            }
        },
        enumerable: true,
        configurable: true
    });
    /**
     * @return {?}
     */
    SwtDataExport.prototype.ngOnInit = /**
     * @return {?}
     */
    function () {
        if (!(this.exportDataComponent.dataProvider.length == 3))
            this.exportDataComponent.dataProvider = this.dp;
    };
    /**
     * @param {?} index
     * @return {?}
     */
    SwtDataExport.prototype.typeFromIndex = /**
     * @param {?} index
     * @return {?}
     */
    function (index) {
        /** @type {?} */
        var type = "";
        if (index == 0)
            type = "pdf";
        else if (index == 1)
            type = "excel";
        else if (index == 2)
            type = "csv";
        return type;
    };
    /**
     * @param {?} mainObj
     * @return {?}
     */
    SwtDataExport.prototype.deepCopy = /**
     * @param {?} mainObj
     * @return {?}
     */
    function (mainObj) {
        /** @type {?} */
        var objCopy = [];
        // objCopy will store a copy of the mainObj
        /** @type {?} */
        var key;
        for (key in mainObj) {
            objCopy[key] = mainObj[key]; // copies each property to the objCopy object
        }
        return objCopy;
    };
    /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    SwtDataExport.prototype.convertArrayToXML = /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    function (josnData, objectName, parentName, attributeList) {
        var _a;
        /** @type {?} */
        var xmlData = parentName ? "<" + parentName + ">" : "";
        try {
            /** @type {?} */
            var options = { compact: true, ignoreComment: true, spaces: 4 };
            for (var i = 0; i < josnData.length; i++) {
                delete josnData[i].id;
                delete josnData[i].content;
                delete josnData[i].contains;
                delete josnData[i].remove;
                if (attributeList) {
                    josnData[i]._attributes = {};
                    for (var index = 0; index < attributeList.length; index++) {
                        /** @type {?} */
                        var element = attributeList[index];
                        josnData[i]._attributes[element] = josnData[i][element];
                        delete josnData[i][element];
                    }
                    xmlData += convert.js2xml((_a = {}, _a[objectName] = (/** @type {?} */ (josnData[i])), _a), options);
                }
                else {
                    if (josnData[i].slickgrid_rowcontent) {
                        for (var tagName in josnData[i]) {
                            if (josnData[i].slickgrid_rowcontent[tagName]) {
                                /** @type {?} */
                                var slickgridRowContent = josnData[i].slickgrid_rowcontent[tagName];
                                delete slickgridRowContent.contains;
                                delete slickgridRowContent.remove;
                                delete slickgridRowContent.content;
                                /** @type {?} */
                                var valueText = josnData[i][tagName];
                                delete josnData[i][tagName];
                                josnData[i][tagName] = new Object;
                                josnData[i][tagName]['_text'] = valueText;
                                josnData[i][tagName]._attributes = {};
                                for (var name_1 in josnData[i]) {
                                    if (name_1.includes("asNum")) {
                                        delete josnData[i][name_1];
                                    }
                                }
                                for (var attributeName in slickgridRowContent) {
                                    if (attributeName != 'content' && attributeName != 'contains' && attributeName != 'remove') {
                                        josnData[i][tagName]._attributes[attributeName] = slickgridRowContent[attributeName];
                                    }
                                }
                            }
                        }
                    }
                    delete josnData[i].id;
                    delete josnData[i].content;
                    delete josnData[i].remove;
                    delete josnData[i].contains;
                    delete josnData[i].slickgrid_rowcontent;
                    xmlData += "<" + objectName + ">" + convert.js2xml(josnData[i], options) + "</" + objectName + ">";
                }
            }
            xmlData += parentName ? "</" + parentName + ">" : "";
        }
        catch (e) {
            console.log("e", e);
        }
        return xmlData;
    };
    /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    SwtDataExport.prototype.convertArraysToXML = /**
     * @param {?} josnData
     * @param {?} objectName
     * @param {?=} parentName
     * @param {?=} attributeList
     * @return {?}
     */
    function (josnData, objectName, parentName, attributeList) {
        var _a;
        /** @type {?} */
        var xmlData = parentName ? "<" + parentName + ">" : "";
        try {
            /** @type {?} */
            var options = { compact: true, ignoreComment: true, spaces: 4 };
            delete josnData[0][1];
            josnData[0].length = 1;
            for (var j = 0; j < josnData.length; j++) {
                for (var i = 0; i < josnData[j].length; i++) {
                    delete josnData[j][i].id;
                    delete josnData[j][i].content;
                    delete josnData[j][i].contains;
                    delete josnData[j][i].remove;
                    if (attributeList) {
                        if ((josnData[j][i]._attributes == undefined))
                            josnData[j][i]._attributes = {};
                        if (objectName.split(",")[j] == "group") {
                            if (josnData[j][i].column.length) {
                                for (var k = 0; k < josnData[j][i].column.length; k++)
                                    josnData[j][i].column[k]._attributes = {}; //josnData[j][i].column ;
                            }
                            else
                                josnData[j][i].column._attributes = {};
                        }
                        for (var index = 0; index < attributeList.length; index++) {
                            /** @type {?} */
                            var element = attributeList[index];
                            //if(objectName.split(",")[j] =="group" )
                            if (josnData[j][i][element] || objectName.split(",")[j] == "group") {
                                josnData[j][i]._attributes[element] = josnData[j][i][element];
                                if (objectName.split(",")[j] == "group") {
                                    if (josnData[j][i].column.length) {
                                        for (var k = 0; k < josnData[j][i].column.length; k++) {
                                            if (element == "heading") {
                                                josnData[j][i].column[k]._attributes[element] = josnData[j][i]["heading"] + '-' + josnData[j][i].column[k][element]; //josnData[j][i].column ;
                                            }
                                            else
                                                josnData[j][i].column[k]._attributes[element] = josnData[j][i].column[k][element]; //josnData[j][i].column ;
                                            delete josnData[j][i].column[k][element];
                                        }
                                    }
                                    else {
                                        if (element == "heading") {
                                            josnData[j][i].column._attributes[element] = josnData[j][i]["heading"] + '-' + josnData[j][i].column[element];
                                        }
                                        else
                                            josnData[j][i].column._attributes[element] = josnData[j][i].column[element];
                                        delete josnData[j][i].column[element];
                                    }
                                }
                                if (josnData[j][i][element])
                                    delete josnData[j][i][element];
                                //if(josnData[j][i].column)
                            }
                        }
                        xmlData += convert.js2xml((_a = {}, _a[objectName.split(",")[j]] = (/** @type {?} */ (josnData[j][i])), _a), options);
                    }
                    else {
                        if (josnData[j][i].slickgrid_rowcontent) {
                            for (var tagName in josnData[i]) {
                                if (josnData[j][i].slickgrid_rowcontent[tagName]) {
                                    /** @type {?} */
                                    var slickgridRowContent = josnData[j][i].slickgrid_rowcontent[tagName];
                                    delete slickgridRowContent.contains;
                                    delete slickgridRowContent.remove;
                                    delete slickgridRowContent.content;
                                    /** @type {?} */
                                    var valueText = josnData[j][i][tagName];
                                    delete josnData[j][i][tagName];
                                    josnData[j][i][tagName] = new Object;
                                    josnData[j][i][tagName]['_text'] = valueText;
                                    josnData[j][i][tagName]._attributes = {};
                                    for (var attributeName in slickgridRowContent) {
                                        if (attributeName != 'content' && attributeName != 'contains' && attributeName != 'remove') {
                                            josnData[j][i][tagName]._attributes[attributeName] = slickgridRowContent[attributeName];
                                        }
                                    }
                                }
                            }
                        }
                        delete josnData[j][i].id;
                        delete josnData[j][i].content;
                        delete josnData[j][i].remove;
                        delete josnData[j][i].contains;
                        delete josnData[j][i].slickgrid_rowcontent;
                        xmlData += "<" + objectName.split(",")[j] + ">" + convert.js2xml(josnData[j][i], options) + "</" + objectName.split(",")[j] + ">";
                    }
                }
            }
            xmlData += parentName ? "</" + parentName + ">" : "";
        }
        catch (e) {
            console.log("e", e);
        }
        return xmlData;
    };
    /**
               *  This method is used to convert the xmlData to String and handle the special character
               *
               * @param cMetaData :XMLList
               * @param cGrid :CustomGrid
               * @param tData :XMLList
               * @param selects :Array
               * @param type :String
               */
    /**
     *  This method is used to convert the xmlData to String and handle the special character
     *
     * @param {?} cMetaData :XMLList
     * @param {?} cGrid :CustomGrid
     * @param {?} tData :XMLList
     * @param {?} selects :Array
     * @param {?=} type :String
     * @param {?=} isTotalGrid
     * @param {?=} forceAllData
     * @return {?}
     */
    SwtDataExport.prototype.convertData = /**
     *  This method is used to convert the xmlData to String and handle the special character
     *
     * @param {?} cMetaData :XMLList
     * @param {?} cGrid :CustomGrid
     * @param {?} tData :XMLList
     * @param {?} selects :Array
     * @param {?=} type :String
     * @param {?=} isTotalGrid
     * @param {?=} forceAllData
     * @return {?}
     */
    function (cMetaData, cGrid, tData, selects, type, isTotalGrid, forceAllData) {
        if (type === void 0) { type = "pdf"; }
        if (isTotalGrid === void 0) { isTotalGrid = false; }
        if (forceAllData === void 0) { forceAllData = false; }
        /** @type {?} */
        var str = "<data>";
        /** @type {?} */
        var totals = "";
        /** @type {?} */
        var columnMetaData = JSON.parse(JSON.stringify(cMetaData.column));
        /** @type {?} */
        var columnMetaDataGroup = "";
        /** @type {?} */
        var columnMetaDataGroupAsString = "";
        if (JSON.stringify(cMetaData.group)) {
            columnMetaDataGroup = JSON.parse(JSON.stringify(cMetaData.group));
            /** @type {?} */
            var columnMetaDataGroupAsArray = Array.of(columnMetaDataGroup);
            if (columnMetaDataGroupAsArray[0].length > 1) {
                columnMetaDataGroupAsArray = columnMetaDataGroupAsArray[0];
            }
            columnMetaDataGroupAsString = this.convertArraysToXML([columnMetaData, columnMetaDataGroupAsArray], "column,group", "columns", ['collapsable', 'headerTooltip', 'heading', 'filterable', 'dataelement', 'draggable', 'width', 'type', 'holiday', 'resizable', 'headerColor', 'clickable']);
        }
        /** @type {?} */
        var tempData = null;
        if (forceAllData) {
            tempData = $.extend(true, [], cGrid.dataset);
        }
        else {
            tempData = $.extend(true, [], cGrid.getFilteredItems());
        }
        /** @type {?} */
        var rowData = this.convertArrayToXML(tempData, "row", "rows");
        /** @type {?} */
        var columnMetaDataAsString = this.convertArrayToXML(columnMetaData, "column", "columns", ['filterable', 'dataelement', 'draggable', 'heading', 'width', 'type', 'holiday', 'visible', 'clickable', 'sort', 'resizable', 'headerTooltip', 'editable', 'columntype']);
        if (columnMetaDataGroup != "") {
            str += CommonLogic.removeLineBreaks(columnMetaDataGroupAsString);
        }
        else {
            str += CommonLogic.removeLineBreaks(columnMetaDataAsString);
        }
        str += CommonLogic.removeLineBreaks(rowData);
        if (str.search(this.accountStartPattern) > -1 || str.search(this.accountStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.accountStartPattern, '<account clickable="false"><![CDATA[');
            str = str.replace(this.accountStartWithoutAttributesPattern, '<account><![CDATA[');
            str = str.replace(this.accountEndPattern, "\]\]></account>");
        }
        if (str.search(this.nameStartPattern) > -1 || str.search(this.nameStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.nameStartPattern, '<name clickable="false"><![CDATA[');
            str = str.replace(this.nameStartWithoutAttributesPattern, '<name><![CDATA[');
            str = str.replace(this.nameEndPattern, "\]\]></name>");
        }
        if (str.search(this.interfaceStartPattern) > -1 || str.search(this.interfaceStartWithoutAttributesPattern) > -1) {
            str = str.replace(this.interfaceStartPattern, '<interface clickable="false"><![CDATA[');
            str = str.replace(this.interfaceStartWithoutAttributesPattern, '<interface><![CDATA[');
            str = str.replace(this.interfaceEndPattern, "\]\]></interface>");
        }
        // Start: Code modified by Bala on 09-Sep-2011 for Issue found in 1053 Beta testing - Assumption report not opened when special character present in Assumption  
        if (str.search(this.assumptionStartPattern) > -1) {
            str = str.replace(this.assumptionStartPattern, '<assumption><![CDATA[');
            str = str.replace(this.assumptionEndPattern, "\]\]></assumption>");
        }
        // End: Code modified by Bala on 09-Sep-2011 for Issue found in 1053 Beta testing - Assumption report not opened when special character present in Assumption  
        /*str = str.split("\\").join("BACKSLASH_REPLACE");
        str = str.split("'").join("\\\\'");
        str = str.split("&amp;").join("\\&");
        str = str.split("%").join("PERCENTAGE_REPLACE");
        str = str.split("&lt;").join("\\<");
        str = str.split("&gt;").join("\\>");
        str = str.split("+").join("PLUSSYMBOL_REPLACE");*/
        if (tData != null && isTotalGrid) {
            /** @type {?} */
            var totalData = this.convertArrayToXML($.extend(true, [], tData), "total");
            totals = CommonLogic.removeLineBreaks(totalData);
        }
        //totals = "<total>" + totals + "</total>";
        str += totals;
        /** @type {?} */
        var filters = "<filters>";
        if (selects != null) {
            for (var k = 0; k < selects.length; k++) {
                filters += "<filter id=\"" + selects[k].split("=")[0] + "\">" + selects[k].split("=")[1] + "</filter>";
            }
        }
        if (cGrid.isFiltered) {
            // console.log("cGrid.getFilteredItems()=",cGrid.getFilteredItems());
            // for (var i: number = 0; i < cGrid.getCurrentFilter().length; i++) {
            /** @type {?} */
            var filterVal = "";
            /*for (var j in 10) {
                if(j != 'content' && j != 'contains' && j != 'remove'){
                    filters += "<filter id=\"Column Filter " + j + "\"><![CDATA[" + filterVal[j] + "\]\]></filter>";

                }
                // Special Characters included in selected filter.
                // filters = filters.split("\\").join("BACKSLASH_REPLACE");
                // filters = filters.split("'").join("\\\\'");
                // filters = filters.split("&amp;").join("\\&");
                // filters = filters.split("%").join("PERCENTAGE_REPLACE");
                // filters = filters.split("&lt;").join("\\<");
                // filters = filters.split("&gt;").join("\\>");
                // filters = filters.split("+").join("PLUSSYMBOL_REPLACE");
            }*/
            // }
        }
        filters += "</filters>";
        str += filters;
        str += "</data>";
        // ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').action=\'flexdataexport.do?method=' + type + '\';');
        // ExternalInterface.call('eval', 'document.getElementById(\'exportData\').value=\'' + str + '\';');
        // ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').submit();');
        ExternalInterface.call('exportData', type, str);
    };
    SwtDataExport.decorators = [
        { type: Component, args: [{
                    selector: 'DataExport',
                    template: "\n  <div>\n\t\t\t<SwtComboBox id=\"exportDataComponent\" (inputClick)=\"onDataExportClick($event)\" \n\t\t\t(change)=\"onDataExportChange($event)\" editable=\"false\" #exportDataComponent width=\"43\"></SwtComboBox>\n  </div>\n  ",
                    styles: ["\n  "]
                }] }
    ];
    /** @nocollapse */
    SwtDataExport.ctorParameters = function () { return [
        { type: CommonService },
        { type: ElementRef }
    ]; };
    SwtDataExport.propDecorators = {
        exportDataComponent: [{ type: ViewChild, args: ['exportDataComponent',] }],
        enabled: [{ type: Input }]
    };
    return SwtDataExport;
}(SwtModule));
export { SwtDataExport };
if (false) {
    /** @type {?} */
    SwtDataExport.prototype.exportDataComponent;
    /** @type {?} */
    SwtDataExport.prototype.dp;
    /** @type {?} */
    SwtDataExport.prototype.dpDisabled;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.accountEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.nameEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceStartWithoutAttributesPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.interfaceEndPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.assumptionStartPattern;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.assumptionEndPattern;
    /**
     * @type {?}
     * @protected
     */
    SwtDataExport.prototype.elementReference;
    /**
     * @type {?}
     * @protected
     */
    SwtDataExport.prototype.commonServiceRef;
    /** @type {?} */
    SwtDataExport.prototype.SwtAlert;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype._enabled;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.commonService;
    /**
     * @type {?}
     * @private
     */
    SwtDataExport.prototype.element;
}
//# sourceMappingURL=data:application/json;base64,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